<template>
<el-dialog :title="title" :visible.sync="show" width="786px">
    <el-form label-width="120px" style="padding-right: 40px">
        <el-form-item label="回款人:">
            <tMemberSelect v-model="params.admin_id" placeholder="选择添加人"/>
        </el-form-item> 
        <el-form-item label="回款金额:">
            <el-input v-model="params.amount" placeholder="请输入回款金额">
                <template #suffix>元</template>
            </el-input>
        </el-form-item>
        <el-form-item label="回款状态:">
            <el-radio-group v-model="params.status">
                <el-radio :label="1">已回款</el-radio>
                <el-radio :label="2">未完成</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="类别:">
            <el-radio-group v-model="params.cate">
                <el-radio :label="1">默认</el-radio>
                <el-radio :label="2">开票</el-radio>
                <el-radio :label="3">其他</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="开票时间:" v-if="params.cate==2">
            <el-date-picker v-model="params.invoice_time" type="datetime" placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="开票金额:" v-if="params.cate==2">
            <el-input v-model="params.invoice_money" placeholder="请输入开票金额">
                <template #suffix>元</template>
            </el-input>
        </el-form-item>
        <el-form-item label="其他金额:" v-if="params.cate==3">
            <el-input v-model="params.other_money" placeholder="请输入其他金额">
                <template #suffix>元</template>
            </el-input>
        </el-form-item>
        <el-form-item label="回款时间:">
            <el-date-picker v-model="params.payment_time" type="datetime" placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="备注:">
            <el-input v-model="params.remark" placeholder="请输入备注信息" type="textarea"></el-input>
        </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>

<script>
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect.vue'
export default {
    components: {
        tMemberSelect
    },
    data(){
        return {
            show: false,        //dialog是否显示
            successFn: null, 
            isAdd: true,
            submiting: false,
            params: {}
        }
    },
    computed: {
        title(){
            return this.isAdd? '新增回款记录' : '编辑回款记录';
        }
    },
    methods: {
        open(params){
            this.params = {
                id: 0,
                report_id: 0,
                admin_id: '',
                status: 1,
                cate: 1,
                invoice_time: '',
                invoice_money: '',
                other_money: '',
                amount: '',
                payment_time: '',
                remark: ''
            };
            for(const key in params){
                if(this.params[key] !== undefined){
                    this.params[key] = params[key];
                }
            }
            this.isAdd = params.id ? false : true;
            
            this.show = true;
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            const params = {...this.params};
            if(this.isAdd){
                delete params.id;
            }
            this.submiting = true;
            const res = await this.$http[this.isAdd?'addCrmDealReportPaymentLogs':'editCrmDealReportPaymentLogs'](params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }  
}
</script>
<style lang="scss" scoped>
::v-deep .el-input{
    width: 220px;
} 
</style>