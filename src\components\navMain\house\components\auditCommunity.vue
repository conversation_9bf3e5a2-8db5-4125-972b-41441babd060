<template>
  <div class="audit_comm">
    <div class="oper">
      <el-button @click="onCreateNewRegion" icon="el-icon-plus" type="primary">
        添加
      </el-button>
    </div>
    <el-table
      :data="list"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
    >
      <!-- <el-table-column prop="id" label="ID"> </el-table-column> -->
      <el-table-column prop="title" label="名称"> </el-table-column>
      <el-table-column prop="addr" label="地址"> </el-table-column>
      <el-table-column prop="" label="商圈">
        <template slot-scope="scope">
          {{ scope.row.region ? scope.row.region.region_name : "" }}
        </template>
      </el-table-column>
      <el-table-column prop="ctime" label="创建时间"></el-table-column>
      <el-table-column label="操作" fixed="right" width="300">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="onEditData(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            @click="deleteData(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="total,prev, pager, next"
      :total="data_count"
      :page-size="params.rows"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog
      append-to-body
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
    >
      <addCommunity
        :form="form_info"
        :dialogTitle="dialogTitle"
        @success="handleSuccess"
      ></addCommunity>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import addCommunity from "./addCommunity.vue"
export default {
  name: "city_setup",
  components: { addCommunity },
  data() {
    return {
      list: [],
      data_count: 0,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      params: {
        page: 1,
        rows: 10
      },
      dialogTitle: "",
      dialogCreate: false,
      form_info: {
        pinyin: "",
        name: "",
        sort: 0,
        pid: 0,
      }
    };
  },
  computed: {
    ...mapState(["website_info"]),
  },
  mounted() {
    this.getList();
  },
  methods: {
    onCreateNewRegion() {
      this.dialogCreate = true;
      this.dialogTitle = "addData";
      this.form_info = {
        title: "",
        addr: "",
        lat: "",
        lng: "",
        region_id: '',
        region_name: ''
      };
    },
    onEditData(row) {
      this.dialogCreate = true;
      this.dialogTitle = "updateData";
      this.form_info = {
        title: row.title,
        addr: row.addr,
        lat: row.lat,
        lng: row.lng,
        region_id: row.region_id,
        region_name: row.region_name,
        id: row.id

      };
    },

    getList() {
      this.$ajax.house.getAuditCommunityList(this.params).then((res) => {
        console.log(res);
        if (res.status === 200) {
          let arr = res.data.data;
          this.list = arr
          this.data_count = res.data.total
        }
      });
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },

    deleteData(row) {
      this.$confirm("此操作将删除该数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$ajax.house.deleteAuditCommunity(row.id).then((res) => {
            if (res.status === 200) {
              this.$message.success("删除成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          console.log("取消");
        });
    },
    handleSuccess() {
      this.getList();
      this.dialogCreate = false;
    }
  },
};
</script>

<style>
.el-table .success-row {
  background: #f0f9eb;
}
.audit_comm {
  padding: 0 12px 0 80px;
}
.oper {
  margin-bottom: 20px;
}
</style>
