<template>
  <el-form ref="form" :model="form" label-width="120px" label-position="left">
    <div class="form_block">
      <div class="title">
        <span> 业主信息 </span>

        <el-tooltip placement="top-start" width="200" trigger="hover" effect="light" v-if="isOpenShowingSingle">
          <div slot="content" style="line-height: 1.5">
            单边代理已开启，仅同店和平台巡检可查看
          </div>
          <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
          <span icon="el-icon-info" type="danger"><i class="el-icon-info" style="color: #f56c6c"></i></span>
        </el-tooltip>
        <!-- <span class="add_owner">添加</span> -->
      </div>

      <template v-for="(item, index) in ownerList">
        <el-form-item label="业主姓名" :key="index">
          <template #label>
            <span style="color: #f56c6c">*</span>
            业主姓名
          </template>
          <el-input style="width: 180px" type="text" v-model="form.tel[index].owner" placeholder="请输入">
          </el-input>
          <el-select placeholder="请选择" style="width: 100px" v-model="form.tel[index].sex">
            <!--如果是select或者checkbox 、Radio就还需要选项信息-->
            <el-option label="先生" :value="1"></el-option>
            <el-option label="女士" :value="2"></el-option>
          </el-select>
          <template v-if="index == 0 && ownerList.length < 3">
            <el-button type="primary" style="margin-left: 10px" @click="addOwner">添加</el-button>
          </template>
          <template v-if="index !== 0">
            <el-button type="warning" style="margin-left: 10px" @click="delOwner(index)">删除</el-button>
          </template>
        </el-form-item>
        <el-form-item label="业主手机号" :key="index + '_2'">
          <template #label>
            <span style="color: #f56c6c">*</span>
            业主手机号
          </template>
          <el-input style="width: 180px" type="number" v-model="form.tel[index].owner_tel" placeholder="请输入"></el-input>
          <el-select v-model="form.tel[index].type" style="width: 100px" placeholder="请选择">
            <!--如果是select或者checkbox 、Radio就还需要选项信息-->
            <el-option v-for="(item, index) in typeList" :key="index" :label="item.name" :value="item.values"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="隐私保护" prop="privacy_type" v-if="privacy_status && isOpenShowingSingle">
        <el-radio v-model="form.privacy_type" :label="1" border>同店可查看</el-radio>
        <el-radio v-model="form.privacy_type" :label="2" style="margin-right: 0" border>联系维护人</el-radio>
        <el-tooltip placement="top-start" width="200" trigger="hover" effect="light">
          <div slot="content" style="line-height: 1.5">
            <!-- <div> -->
            1,房源业主电话将隐藏不显示<br />
            <!-- </div> -->
            <!-- <div> -->
            2,只能联系房源维护人<br />
            <!-- </div> -->
            <!-- <div> -->
            3,当前成员设置上限为{{ privaceNum }}条
            <!-- </div> -->
          </div>
          <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
          <span icon="el-icon-info" type="danger"><i class="el-icon-info" style="color: #f56c6c"></i></span>
        </el-tooltip>
      </el-form-item>
    </div>

    <el-form-item label="房源备注" prop="memo">
      <el-input v-model="form.memo" type="textarea" style="width: 290px" placeholder="请输入跟进内容 企业内公开"
        :autosize="{ minRows: 4 }" maxlength="200" show-word-limit></el-input>
    </el-form-item>
    <div class="form_block" v-if="isShowHousePromotion">
      <div class="title">
        <span> 外网信息 </span>
      </div>
      <el-form-item label="推广标题" prop="title_promotion">
        <el-input v-model="form.title_promotion" style="width: 290px" placeholder="请输入推广标题"></el-input>
      </el-form-item>
      <el-form-item label="特色卖点" prop="description">
        <el-input v-model="form.description" type="textarea" style="width: 290px" placeholder="请输入特色卖点"
          :autosize="{ minRows: 4 }" maxlength="300" show-word-limit></el-input>
      </el-form-item>
    </div>

  </el-form>
</template>

<script>
// import dayjs from 'dayjs'
export default {
  name: 'LmAddForm2',
  components: {

  },
  data() {
    return {
      isEntrust: false,
      from_focus: 0,
      fangxing: [3, 2, 2],
      form: {
        title: '',
        tel: [
          {
            owner: '',
            owner_tel: '',
            type: 1,
            sex: 1
          }
        ],
        title_promotion: '', //推广标题
        description: '',
        privacy_type: 1,
        memo: ""

        // sell_open_user: '',
        // rent_open_user: '',
        // survey_user: '',
      },
      lookDay: '',
      startTime: '',
      endTime: '',
      timeRange: '',
      typeList: [],
      ownerList: [
        {
          owner: '',
          owner_tel: '',
          type: 1,
          sex: 1
        }
      ]
    }
  },
  props: {
    entrustData: {
      type: Object,
      default: () => { },
    },
    form_options: {
      type: Object,
      default: () => { },
    },
    house_type: {
      type: Number,
      default: 1,
    },
    privaceNum: {
      type: [Number, String],
      default: 0,
    },
    privacy_status: {
      type: [Number, String],
      default: 0,
    },
    form_data: {
      type: Object,
      default: () => { },
    },
    isOpenShowingSingle: {
      type: [String, Boolean, Number],
      default: false,
    }
  },
  watch: {
    form_data(new_val) {
      console.log(new_val, new_val.house_info, "################")
      this.setForm(new_val)
    },
    lookDay() {
      this.setShowTime()
    },
    startTime() {
      this.setShowTime()
    },
    endTime() {
      this.setShowTime()
    },
  },
  computed: {
    //是否显示外网信息
    isShowHousePromotion() {
      return this.form_options?.house_promotion?.values == 1;
    }
  },
  created() {
    this.from_focus = this.$route.query.is_focus || 0
    // this.setForm(this.form_data)
    this.getTelTypeList()
    if (JSON.stringify(this.entrustData) != '{}') {
      this.isEntrust = true
      this.form.tel[0].owner = this.entrustData.owner
      this.form.tel[0].owner_tel = this.entrustData.owner_tel
      this.form.memo = this.entrustData.description
    }
  },
  methods: {
    setForm(params) {
      this.ownerList = params.tel
      for (let key in params) {
        if (
          this.form[key] !== undefined ||
          (this.form[key] && this.form[key].length === 0)
        ) {
          this.form[key] = params[key]
        }
      }
      // // 设置集中看房时间
      // if (params.showing_stime && params.showing_etime) {
      //   this.lookDay = dayjs(new Date(params.showing_stime * 1000)).format(
      //     'YYYY-MM-DD'
      //   )
      //   this.startTime = dayjs(new Date(params.showing_stime * 1000)).format(
      //     'HH:mm'
      //   )
      //   this.endTime = dayjs(new Date(params.showing_etime * 1000)).format(
      //     'HH:mm'
      //   )
      // }
    },
    onHuxingChange(e) {
      // console.log(e)
      this.form.shi = e[0]
      this.form.ting = e[1]
      this.form.wei = e[2]
    },
    setShowTime() {
      // console.log(this.lookDay)
      if (this.lookDay && this.startTime && this.endTime) {
        this.form.showing_stime = `${this.lookDay} ${this.startTime}`
        this.form.showing_etime = `${this.lookDay} ${this.endTime}`
      }
    },
    addOwner() {
      if (this.ownerList.length < 3) {
        this.ownerList.push({
          owner: '',
          owner_tel: '',
          type: 1,
          sex: 1
        })
        this.form.tel.push({
          owner: '',
          owner_tel: '',
          type: 1,
          sex: 1
        })
      }

    },
    delOwner(index) {
      this.ownerList.splice(index, 1)
      this.form.tel.splice(index, 1)
    },
    getTelTypeList() {
      this.$ajax.house.getTelTypeList().then(res => {
        // console.log(res);
        if (res.status == 200) {
          this.typeList = res.data
        }
      })
    },
    // 清除已经填入的信息
    clearChildData() {
      this.$refs.form.resetFields();
      // 清除业主姓名
      this.ownerList = [
        {
          owner: '',
          owner_tel: '',
          type: 1,
          sex: 1
        }
      ];
      // 清除业主手机号
      this.form.tel = [
        {
          owner: '',
          owner_tel: '',
          type: 1,
          sex: 1
        }
      ];
    },
  },
}
</script>

<style scoped lang="scss">
.form_block {
  margin-bottom: 48px;

  >.title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;

    .add_owner {
      margin-left: 180px;
      padding: 5px 10px;
      background: #409eff;
      color: #fff;
      border-radius: 3px;
      font-size: 14px;
      font-weight: normal;
    }
  }

  .remarks {
    font-size: 14px;
    font-weight: initial;
  }

  .el-form-item {
    color: #8a929f;

    .tip {
      color: #fe6c17;
    }

    .unit {
      margin-left: 12px;
    }

    ::v-deep .el-form-item__label {
      font-weight: bold;
      color: #2e3c4e;
    }

    ::v-deep .el-input {
      width: 240px;
    }

    ::v-deep .el-textarea textarea {
      resize: none;
    }

    .el-date-editor {
      width: 130px;

      &:first-child {
        margin-right: 6px;
        width: 150px;
      }
    }
  }
}

.form_block ::v-deep .el-select {
  margin-left: 10px;

  .el-input {
    width: 100%;
  }
}
</style>
