<template>
  <el-container>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "batch_pay_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        batch_id: "",
      },
      table_header: [
        { prop: "id", label: "ID", width: "80" },
        { prop: "batch_sn", label: "订单日期" },
        { prop: "paid_amount", label: "付款金额" },
        { prop: "paid_date", label: "付款日期" },
        { prop: "project_company_name", label: "项目公司" },
        { prop: "sale_company_name", label: "结佣公司" },
        { prop: "operation_user_name", label: "操作用户" },
        {
          label: "操作",
          width: "120",
          fixed: "right",
          render: (h, data) => {
            return (
              <el-button
                type="danger"
                disabled={data.row.return === 1}
                onClick={() => {
                  this.cancelPayment(data.row);
                }}
                size="mini"
              >
                撤销批次
              </el-button>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.params.batch_id = this.$route.query.batch_id;
    if (this.params.batch_id) {
      this.getDataList();
    }
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      this.$http.getBatchPayList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    cancelPayment(row) {
      let form = {
        bill_batch_id: row.batch_id,
        record_batch_id: row.id,
      };
      this.$http.cancelBatchData(form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "已取消",
            type: "success",
          });
          this.getDataList();
        }
      });
    },
  },
};
</script>

<style></style>
