<template>
<el-dialog :visible.sync="show" width="500px" title="初始化密码">
    <el-form label-width="120px" class="form">
        <el-form-item label="新密码">
            <el-input v-model="params.password" type="password"></el-input>
        </el-form-item>
        <el-form-item label="确认密码">
            <el-input v-model="params.password_confirmation" type="password"></el-input>
        </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" :loading="submiting" @click="confirm">提 交</el-button>
    </span>
</el-dialog>
</template>
<script>
export default {
    data() {
        return {
            show: false,
            submiting: false,
            successFn: null,
            params: {
                ids: '',
                password: '',
                password_confirmation: '',
            }
        }
    },
    methods: {
        open(ids){
            this.params.ids = ids || '';
            this.params.password = '';
            this.params.password_confirmation = '';
            this.show = true;
            return this;
        },
        onSuccess(fn){
            this.successFn = fn;
        },
        cancle(){
            this.show = false;
        },
        async confirm() {
            if(!this.Validate({
                password: {require: '请填写新密码'},
                password_confirmation: {require: '请填写确认密码'},
            }).check(this.params)){
                return;
            }
            if(this.params.password != this.params.password_confirmation){
                this.$message.error('两次密码不一致')
                return;
            }
            this.submiting = true
            const res = await this.$http.resetAdminPassword(this.params).catch(()=>{});
            this.submiting = false;
            if(res.status ==  200){
                this.$message.success(res.data?.msg || '设置成功')
                this.show = false;
                this.successFn && this.successFn();
            }
        }
    }
}

</script>

<style lang="scss" scoped>
.form{
    padding: 0 32px 0 0;
}
</style>