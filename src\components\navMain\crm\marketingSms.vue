<template>
  <div>
    <div class="content-box-crm">
      <!-- <div class="check-box div row" id="pages_content">
        <div
          v-for="item in tabs_list"
          :class="{ isactive: item.id === is_tabs_page }"
          @click="onClickTabs(item)"
          :key="item.id"
          class="check-item"
        >
          {{ item.name }}
        </div>
      </div> -->
      <template v-if="is_tabs_page == 1">
        <div class="table-top-box div row">
          <div class="t-t-b-left div row"></div>
          <div class="t-t-b-right div row">
            <!-- <el-button type="success" @click="signs"> 签名 </el-button> -->
            <el-button type="success" @click="downloadXLS(1)">
              下载txt示例
            </el-button>
            <el-button type="success" @click="downloadXLS(2)">
              下载xlsx示例
            </el-button>
            <el-button type="primary" @click="add"> 创建任务</el-button>
          </div>
        </div>
        <div>
          <el-table v-loading="is_table_loading" :data="taskData" border class="table"
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
            <el-table-column prop="id" label="ID"></el-table-column>
            <el-table-column prop="name" label="任务名称"></el-table-column>
            <el-table-column prop="msg" label="短信内容" width="200" v-slot="{ row }">
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  {{ row.msg }}
                </div>
                <div class="msg_con">
                  {{ row.msg }}
                </div>
              </el-tooltip>
            </el-table-column>

            <el-table-column prop="send_count" label="发送总条数"></el-table-column>
            <el-table-column prop="no_sms_send" label="未发送条数"></el-table-column>
            <el-table-column prop="has_been_count" label="已发送条数"></el-table-column>
            <el-table-column prop="err_sms_send" label="发送失败条数"></el-table-column>
            <!-- <el-table-column prop="msg" label="短信内容"></el-table-column> -->
            <el-table-column label="操作" v-slot="{ row }">
              <el-link @click="sendLog(row)">发送记录</el-link>
            </el-table-column>
          </el-table>

          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
            :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
          </el-pagination>
        </div>
      </template>
      <!-- <template v-else>
        <marketOrder v-if="is_tabs_page == 2"></marketOrder>
      </template> -->
    </div>
    <!-- <el-dialog width="500px" :visible.sync="is_show_sign_Dia" title="签名">
      <div v-if="is_show_sign_Dia">
        <el-form label-width="60px">
          <el-form-item label="签名">
            <el-input v-model="sign_form.value"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_show_sign_Dia = false">取 消</el-button>
        <el-button type="primary" v-loading="add_loading" @click="confirmSign"
          >确 定</el-button
        >
      </span>
    </el-dialog> -->

    <el-dialog width="500px" append-to-body :visible.sync="is_showDia" title="创建任务">
      <div v-if="is_showDia">
        <el-form label-width="120px">
          <el-form-item label="任务名称">
            <el-input v-model="task_form.name"></el-input>
          </el-form-item>
          <el-form-item label="发送内容">
            <el-input type="textarea" maxlength="65" v-model="task_form.msg"></el-input>
          </el-form-item>
          <el-form-item label="任务模板">
            <el-upload :limit="1" class="upload-demo" :on-change="change" :on-success="handleSuccessAvatarTemporary"
              :on-remove="handleRemoveAvatarTemporary" :accept="fileaccept" :action="user_avatar" :auto-upload="false"
              ref="uploadfile">
              <!-- :headers="myHeader"
              :action="user_avatar" -->
              <el-button class="el-icon-download" size="small">本地上传</el-button>
              <div slot="tip" class="el-upload__tip">
                <span>
                  可以是一行一个的纯手机号的.txt文本文件
                  或者是以.xlsx结尾的电子表格
                </span>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button v-loading="add_loading1" type="primary" @click="confirmAdd">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="800px" append-to-body :visible.sync="show_log" title="发送记录">
      <div v-if="show_log">
        <el-table v-loading="is_task_loading" :data="logList" border :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row :row-style="$TableRowStyle">
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column prop="phone" label="手机号"></el-table-column>
          <el-table-column prop="status" label="状态" v-slot="{ row }">
            <el-tag v-if="row.status == 0" type="success"> 发送成功 </el-tag>
            <el-tag v-if="row.status == -1" type="warning"> 未发送 </el-tag>
            <el-tag v-if="row.status == 2" type="danger"> 发送失败 </el-tag>
          </el-table-column>
          <el-table-column prop="report_time" label="发送时间"></el-table-column>

          <!-- <el-table-column prop="desc" label="描述"></el-table-column> -->
        </el-table>

        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="logTotal"
          :page-size="log_params.per_page" :current-page="log_params.page" @current-change="onPageChange">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config";
// import marketOrder from "./components/marketOrder/marketOrder"
import axios from "axios"
export default {
  components: {
    // marketOrder
  },
  data() {
    return {
      taskData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
      },
      log_params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      logTotal: 0,
      is_showDia: false,
      task_form: {
        name: "",
        file: "",
        msg: ""
      },
      add_loading: false,
      add_loading1: false,
      show_log: false,
      logList: [],
      is_task_loading: false,
      tabs_list: [
        { id: 1, name: "任务列表", },
        { id: 2, name: "订单套餐", }
      ],
      is_tabs_page: 1,
      is_show_sign_Dia: false,
      sign_form: {
        value: ''
      }


    }
  },
  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    fileaccept() {
      var val = ".txt,.xlsx";
      return val;
    },
  },
  created() {
    this.website_id = this.$route.query.website_id
    this.getList()
    this.user_avatar = `/api/admin/personnelMatters/sendSms`;
  },
  methods: {
    getList() {
      this.$http.getMaketingTaskList(this.params).then(res => {
        if (res.status == 200) {
          this.taskData = res.data.data
        }
      })
    },
    signs() {
      this.getSign()
      this.is_show_sign_Dia = true
    },
    getSign() {
      this.$http.getSign().then(res => {
        if (res.status == 200) {
          this.sign_form = res.data
        }
      })
    },
    add() {
      this.$refs.uploadfile && this.$refs.uploadfile.clearFiles();
      this.file = ''
      this.task_form.name = ''
      this.task_form.msg = ''
      this.is_showDia = true
    },
    confirmSign() {
      this.add_loading = true
      this.$http.saveSign(this.sign_form)
        .then(res => {
          if (res.status == 200) {
            this.$message.success(res.$message || '保存成功')
            this.is_show_sign_Dia = false
          }
          this.add_loading = false

        })
        .catch(() => {
          this.add_loading = false
        })
    },
    change(e) {
      this.file = e.raw
    },
    confirmAdd() {
      if (!this.task_form.name) {
        this.$message.warning("请输入任务名称")
        return
      }
      if (!this.task_form.msg) {
        this.$message.warning("请输入发送内容")
        return
      }
      this.add_loading1 = true
      let file = new FormData()
      file.append("file", this.file)
      file.append("name", this.task_form.name)
      file.append("msg", this.task_form.msg)
      file.append("website_id", this.website_id)
      axios.post('/api/admin/personnelMatters/sendSms', file, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") } }).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "提交成功")
          this.getList()
          this.is_showDia = false
        } else {
          this.$message.warning(res.message || "提交失败")
        }
        this.add_loading1 = false
      })
        .catch((err) => {
          this.add_loading1 = false
          this.$message.error(err.response?.data?.message || "提交失败")
        })

    },
    showToast() {
      this.$message.warning("请输入任务名称或任务描述")
    },
    handleSuccessAvatarTemporary(response) {
      this.task_form.file = response.url;
    },
    handleRemoveAvatarTemporary(response) {
      this.task_form.file = response.url;
    },
    onClickTabs(item) {
      this.is_tabs_page = item.id
    },
    downloadXLS(type) {
      if (type == 1) {
        window.open("https://img.tfcs.cn/backup/static/admin/xls/phone_sample_template.rar")
      }
      if (type == 2) {
        window.open("https://img.tfcs.cn/backup/static/admin/xls/phone_sample_template_xlsx.xlsx")
      }
    },
    search() {
      this.params.page = 1
      this.getOrderList()
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getOrderList()
    },
    sendLog(row) {
      this.show_log = true
      this.log_params.page = 1
      this.getTaskLog(row.id)
    },
    getTaskLog(id) {
      this.log_params.sms_id = id
      this.$http.getMarketSendLog(this.log_params).then(res => {
        if (res.status == 200) {
          this.logList = res.data.data
        }
      })
    }


  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}

.padd10 {
  padding: 10px 0 40px;
}

.ml50 {
  margin-left: 50px;
}

.title {
  padding-top: 20px;
  padding-left: 75px;
}

.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;

  // margin-left: 20px;
  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;

    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}

.msg_con {
  width: 180px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>