<template>
  <!-- 侧边栏导航 -->
  <el-row class="tac">
    <el-col :span="24">
      <el-menu
        style="border: none"
        @select="openMenu"
        @open="openClick"
        :default-active="indexPath"
        class="el-menu-vertical-demo"
        background-color="#0174ff"
        active-text-color="#fff"
        text-color="#fff"
        unique-opened
        router
      >
        <el-submenu
          v-for="item of slideNavs.filter((item) => {
            return item.level === 0;
          })"
          :index="item.name"
          :key="item.id"
          :disabled="is_qywx_expire"
        >
          <template slot="title" v-if="item.level === 0">
            <i :class="item.extend.icon"></i>
            <span>{{ item.extend.name }}</span>
          </template>
          <el-menu-item-group class="over-hide">
            <el-menu-item
              class="munu-item"
              v-for="sub of item.children"
              :index="
                sub.extend.type
                  ? sub.extend.componentName +
                    `?website_id=${website_info.website_id}&type=${sub.extend.type}`
                  : sub.extend.componentName +
                    `?website_id=${website_info.website_id}`
              "
              :key="sub.id"
              @click="clickMenu(sub.extend)"
            >
              <i v-if="sub.level === 1" :class="sub.extend.icon"></i>
              <!-- 判断权限id是否是城市/区域管理的id 再判断城市管理模式是否是多城市 -->
              <span v-if="sub.level === 1">{{
                sub.id == 37 && website_info.city_type == 2
                  ? "城市管理"
                  : sub.title
              }}</span>
            </el-menu-item>
          </el-menu-item-group>
        </el-submenu>
      </el-menu>
    </el-col>
  </el-row>
</template>

<script>
import { mapActions, mapState } from "vuex";
export default {
  name: "slideNavs",
  data() {
    return {
      menu: [],
      openedTab: [],
      indexPath: "",
    };
  },
  created() {
    this.getSlideNavs();
    this.getRoles();
  },
  computed: {
    ...mapState(["slideNavs", "website_info", "is_qywx_expire"]),
  },
  methods: {
    ...mapActions(["getSlideNavs", "getRoles"]),
    // 解决点击两次选中问题
    openMenu(indexPath) {
      this.indexPath = indexPath;
    },
    clickMenu(e) {
      this.openedTab = this.$store.state.openedTab;
      // tabNum 为当前点击的列表项在openedTab中的index，若不存在则为-1
      let tabNum = this.openedTab.indexOf(e.componentName);
      if (tabNum === -1) {
        // 该标签当前没有打开
        // 将componentName加入到已打开标签页state.openedTab数组中
        this.$store.commit("addTab", {
          componentName: e.componentName,
        });
      } else {
        // 该标签是已经打开过的，需要激活此标签页
        this.$store.commit("changeTab", e.componentName);
      }
    },
    openClick(e) {
      if (e === "CRM") {
        this.$goPath("/crm_index");
      }
    },
  },
};
</script>
<style lang="scss">
.el-submenu__title i {
  color: #fff; // 菜单文字颜色
}
</style>
<style lang="scss" scoped>
.el-menu-item.is-active {
  background-color: #409eff !important;
}
.el-menu-item i {
  // 菜单图标颜色
  color: #fff;
}
.munu-item {
  display: flex;
  align-items: center;
}
</style>
