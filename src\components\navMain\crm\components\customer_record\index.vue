<template>
  <div class="customer-record">
    <div class="customer-title-box">
      <div class="customer-title-item">获客量 ({{ customerStatisticsData.customer_count }})</div>
      <div class="customer-title-item">浏览量 ({{ customerStatisticsData.browse_count }})</div>
      <div class="customer-title-item">分享量 ({{ customerStatisticsData.share_count }})</div>
    </div>
    <div class="customer-body">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="拓展客户" name="1"></el-tab-pane>
        <el-tab-pane label="页面分享" name="2"></el-tab-pane>
        <el-tab-pane label="楼盘分享" name="3"></el-tab-pane>
      </el-tabs>
      <el-table :data="customerList" style="width: 100%">
        <template v-if="activeName == '1'">
          <el-table-column label="成员名称" width="200">
            <template slot-scope="scope">
              <span class="table-l-avatar" :style="'background:' + customerList[scope.$index].color">{{
                customerList[scope.$index].user_name[0] }}</span>
              <span>{{ customerList[scope.$index].user_name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="部门" width="200">
          </el-table-column>
          <el-table-column prop="customer_num" label="获客量" width="120">
          </el-table-column>
          <el-table-column prop="browse_num" label="浏览量" width="120">
          </el-table-column>
          <el-table-column prop="share_num" label="分享量" width="120">
          </el-table-column>
        </template>
        <template v-if="activeName == '2'">
          <el-table-column prop="page_name" label="页面名称" width="200">
          </el-table-column>
          <el-table-column prop="share_num" label="分享次数" width="120">
          </el-table-column>
        </template>
        <template v-if="activeName == '3'">
          <el-table-column prop="build_name" label="楼盘名称" width="200">
          </el-table-column>
          <el-table-column prop="share_num" label="分享次数" width="120">
          </el-table-column>
        </template>
      </el-table>
    </div>
    <div class="customer-bottom">
      <div class="house-bom-btn" style="margin-left: 20px;">
        <el-popover placement="top-start" title="请选择要导出的数据" width="300" trigger="hover">
          <el-checkbox style="margin-bottom: 10px;" :indeterminate="isIndeterminate" v-model="checkAll"
            @change="handleCheckAllChange">全选</el-checkbox>
          <!-- <el-button slot="reference">hover 激活</el-button> -->
          <el-checkbox-group v-model="checkList" @change="handleCheckedCitiesChange">
            <el-checkbox label="拓展客户"></el-checkbox>
            <el-checkbox label="页面分享"></el-checkbox>
            <el-checkbox label="楼盘分享"></el-checkbox>
          </el-checkbox-group>
          <el-button style="margin-top: 20px;" type="primary" size="mini" @click="exportData">确定</el-button>
          <span v-if="CheckListNull" style="color: red;font-size: 14px;margin-left: 20px;">请选择要导出的数据</span>
          <el-button slot="reference" type="primary" size="mini" icon="el-icon-download">导出数据</el-button>
        </el-popover>
        <el-button style="margin-left: 20px;" type="primary" size="mini" icon="el-icon-refresh-right"
          @click="refresh">刷新页面</el-button>
        <!-- <el-button type="primary" size="mini" icon="el-icon-delete" @click="clearFilter">清空筛选项</el-button> -->
      </div>
      <el-pagination style="margin-right: 20px;" :hide-on-single-page="true" background
        layout="total, sizes, prev, pager, next, jumper" :total="data_count" :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange"
        @size-change="onSizeChange">
      </el-pagination>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: '1',
      CheckListNull: false,
      checkAll: false,
      isIndeterminate: false,
      checkList: ['拓展客户'],
      data_count: 0,
      params: {
        admin_id: '',
        date_type: 1,
        page: 1,
        per_page: 10
      },
      customerList: [],
      customerStatisticsData: {}
    }
  },
  mounted() {
    this.getTableList()
    this.customerStatistics()
  },
  methods: {
    handleClick(e) {
      this.activeName = e._props.name
      this.getTableList()
    },
    handleCheckedCitiesChange(e) {
      !e.length ? this.CheckListNull = true : this.CheckListNull = false
      e.length == 3 ? this.checkAll = true : this.checkAll = false
    },
    handleCheckAllChange(e) {
      if (!e) {
        this.CheckListNull = true
        this.checkList = []
      } else {
        this.CheckListNull = false
        this.checkList = ['拓展客户', '页面分享', '楼盘分享']
      }
    },
    async customerStatistics() {
      const res = await this.$http.customerStatisticsAPI()
      if (res.status == 200) {
        this.customerStatisticsData = res.data
      }
    },
    async getTableList() {
      // this.customerList = []
      let params = this.params
      let res = {}
      if (this.activeName == '1') {
        res = await this.$http.getCustomerListAPI({ params })
      } else if (this.activeName == '2') {
        res = await this.$http.pageShareAPI({ params })
      } else if (this.activeName == '3') {
        res = await this.$http.houseShareAPI({ params })
      }
      if (res.status == 200) {
        this.customerList = res.data.data
        this.data_count = res.data.total || 0;
        if (this.params.page == 1 && this.activeName == '1') {
          res.data.data.forEach((item, index) => {
            switch (index) {
              case 0:
                this.customerList[index]['color'] = '#f52000'
                break;
              case 1:
                this.customerList[index]['color'] = '#f65600'
                break;
              case 2:
                this.customerList[index]['color'] = '#f7bd00'
                break;
              default:
                break;
            }
          })
        }
      }
    },
    async exportData() {
      if (this.checkList.length == 0) {
        return this.CheckListNull = true
      }
      this.checkList.forEach(async (item) => {
        if (item == '拓展客户') {
          const res = await this.$http.exportCustomerAPI()
          if (res.status == 200) {
            window.open(res.data)
          }
        }
        if (item == '页面分享') {
          const res = await this.$http.exportPageAPI()
          if (res.status == 200) {
            window.open(res.data)
          }
        }
        if (item == '楼盘分享') {
          const res = await this.$http.exportHouseAPI()
          if (res.status == 200) {
            window.open(res.data)
          }
        }
      })
    },
    refresh() {
      this.params.page = 1;
      this.getCustomerList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getCustomerList();
    },
    onSizeChange(e) {
      this.params.per_page = e
      this.getCustomerList();
    },
  }
}
</script>
<style lang="scss">
.table-l-avatar {
  display: inline-block;
  width: 30px;
  height: 30px;
  font-size: 16px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  color: #fff;
  background-color: #488AF6;
}

.customer-record {
  width: 100%;

  .customer-title-box {
    display: flex;
    flex-direction: row;

    .customer-title-item {
      width: 100px;
      margin-right: 40px;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
      border-radius: 5px;
      background-color: #f7f7f7;
    }
  }

  .customer-bottom {
    position: fixed;
    left: 230px;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    width: calc(100vw - 230px);
    padding: 20px 0 10px 0;
    box-shadow: 0px -3px 6px rgba(0, 0, 0, .1);
    border-radius: 3px 3px 0 0;
    background-color: #fff;
  }
}
</style>