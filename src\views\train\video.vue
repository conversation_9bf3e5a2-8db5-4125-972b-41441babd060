<template>
    <div class="video-container" v-loading="loading" element-loading-text="视频正在加载...">
        <template v-if="isLinkExpired">
            <div class="expired-block">
                链接已失效，请联系客服重新获取
            </div>
        </template>
        <template v-else-if="!loading">
            <div class="title">{{detail.title}}</div>
            <div class="video-wrapper">
                <video controls controlsList="nodownload" disablePictureInPicture ref="video" v-show="videoUrl"
                    :poster="videoPoster"
                    :src="videoUrl"
                ></video>
                <div class="content">
                    <div class="tip">
                        <p class="row">如有其它问题，欢迎在客户支持群内留言，我们将及时回复。</p>
                        <p class="row">官方客服工作时间：上午8：30-12：00；下午13：30-17：30 </p>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
export default {
    data(){
        return {
            id: 0,
            detail: {},
            videoUrl: '',
            videoPoster: '',
            isLinkExpired: false,
            loading: false
        }
    },
    created(){
        this.id = this.$route.query.id || 0;
        this.loading = true;
        try{
            //在移动端微信环境中，验证临时链接是否过期
            (async ()=>{
                let env = this.$envjudge();
                if((env == "com-wx-mobile" || env == "wx-mobile")){
                    let t = this.$route.query.t;
                    if(t){
                        this.isLinkExpired = await this.checkLinkExpired(t);
                        //this.isLinkExpired = true;
                    }
                }
            
                if(!this.isLinkExpired){
                    await this.getVideoDetail();
                }
                this.loading = false;
            })();
        }catch(e){
            this.loading = false;
        }
    },
    mounted(){
        const video = this.$refs.video;
        if(video){
            video.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
            video.disablePictureInPicture = true;
        }
    },
    methods: {
        checkLinkExpired(token){
            return this.$http.verifyVideoTutorialToken({token, id: this.id}).then(res => {
                if(res.status == 200){
                    return res.data?.is_expire === 0 ? false : true;
                }
                return true;
            }).catch(e=>{
                return true;
            });
        },
        async getVideoDetail(){
            const res = await this.$http.videoTutorialDetail(this.id);
            if(res.status ==  200){
                this.detail = res.data.tutorial;
                this.videoPoster = this.detail.cover_pic ? this.detail.cover_pic : this.detail.video_url + '?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast'
                this.videoUrl = this.detail.video_url;
            }
            console.log(res);
        }
    }
}
</script>
<style lang="scss" scoped>
.expired-block{
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 36px 16px 16px;
    font-size: 18px;
    color: #a3a4a5;
}
.video-container{
    display: flex;
    flex-direction: column;
    height: 100vh;
    .title{
        font-size: 18px;
        text-align: center;
        padding: 16px;
    }
    
    .video-wrapper{
        flex: 1;
        overflow: hidden;
        max-width: 640px;
        margin: 0 auto;
        video{
            width: 100%;
            outline: none;
            max-height: calc(100% - 150px);
        }
        .content{
            padding: 12px;
            line-height: 1.5;
            color: #606266;
            font-size: 15px;
            .tip{
                padding: 12px;
                border: 1px solid #e2e3e5;
                .row + .row{
                    margin-top: 6px;
                }
            }
        }
    }
    
}
</style>