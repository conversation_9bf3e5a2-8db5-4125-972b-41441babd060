<template>
    <div v-fixed-scroll="62">
      <div class="pagecontent">
        <div class="SearchCondition">
          <div class="block">
              <el-select style="width: 100px;margin-right:10px;" size="small" 
              v-model="paramsdata.date_type" key="dayoptions" placeholder="请选择" @change="datetypechange">
                <el-option
                  v-for="item in dayoptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
              <el-date-picker style="width: 300px" v-model="timeValue" type="datetimerange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                @change="onChangeTime">
              </el-date-picker>
                  <!-- 部门 -->
              <el-cascader style="margin-left:10px;" size="small" 
              class="crm-selected-label" v-model="paramsdata.department_id" placeholder="请选择部门"
               :style="{minWidth: '20px', width: '130px',}" :options="AllDepartment" 
               @change="Refresh" :clearable="true" :show-all-levels="false" 
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'subs',
                  checkStrictly: true,
                  emitPath: false,
                }">
              </el-cascader>
                <el-select style="width: 110px;margin-left:10px;" size="small" 
                  v-model="paramsdata.sort" key="zhubooptions" placeholder="排序(降序)" @change="Refresh">
                  <el-option
                    v-for="item in paixuptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
            </div>
            <div class="head-list">
              <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;margin: 7px 10px 0px 0px;">
                <div slot="content" style="max-width: 300px">
                  数据统计来源于:财务-成交报告  &nbsp;&nbsp;  <el-link type="primary" @click="tochengjiao">去补全></el-link>
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
              </el-tooltip>
              <el-button v-show="show==1" size="small" type="primary" @click="exporttakelook">导出</el-button>
            </div>
        </div>
            <div class="taketable">
                <div class="card-container">
                    <p class="data-card"><strong>成员数量:</strong> {{ ribaoalldata.admin_count }}</p>
                    <p class="data-card"><strong>总接通数量:</strong> {{ ribaoalldata.dail_put_count }}</p>
                    <p class="data-card"><strong>总带看量:</strong> {{ ribaoalldata.take_num }}</p>
                    <p class="data-card"><strong>总成交量:</strong> {{ ribaoalldata.deal_count }}</p>
                    <p class="data-card"><strong>总业绩值:</strong> {{ ribaoalldata.total_commission }}</p>
                </div>
            <el-table
              v-loading="is_table_loading"
              :data="ribaodata"
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }"
              border>
              <el-table-column
                prop="user_name"
                align="center"
                label="成员名称"
                fixed="left">
              </el-table-column>
              <el-table-column
                prop="department"
                align="center"
                label="部门名称">
              </el-table-column>
              <el-table-column label="分客量"
                align="center"
                prop="assign">
              </el-table-column>
              <el-table-column
                prop="dial"
                align="center"
                label="电话拨打量">
              </el-table-column>
              <el-table-column
                prop="dial_put"
                align="center"
                label="电话接通量">
              </el-table-column>
              <el-table-column
                prop="dial_duration"
                align="center"
                label="电话通话时长">
              </el-table-column>
              <el-table-column
                prop="dk"
                align="center"
                label="带看组数">
              </el-table-column>
              <el-table-column
                prop="cj"
                align="center"
                label="成交单数">
              </el-table-column>
              <el-table-column
                prop="total_commission"
                align="center"
                label="认购业绩(应收佣金)">
              </el-table-column>
            </el-table>
              <div class="page_footer flex-row items-center">
                <div class="page_footer_l flex-row flex-1 items-center">
                  <div class="head-list">
                    <el-button type="primary" size="small" @click="empty">清空</el-button>
                  </div> 
                  <div class="head-list">
                    <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                  </div>
                </div> 
                <div style="margin-right:10px;">
                  <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="paramsdata.total"
                    :page-sizes="[10, 20, 30, 50,100]" :page-size="paramsdata.per_page" :current-page="paramsdata.page"
                    @current-change="onPageChange" @size-change="handleSizeChange">
                  </el-pagination>
                </div>
              </div>
            </div>  
        </div>
      </div>
</template>
<script>
export default {
    props: {
        AllDepartment: {
            type: Array,
            default:() => []
        },
        show:{
          type: Number,
          default: false
        }
    },
    data() {
        return {
          timeValue:"",//时间检索
          ribaodata:[],//日报数据
          dayoptions:[
              {id:2,name:"今天"},
              {id:3,name:"昨天"},
              {id:4,name:"本周"},
              {id:5,name:"上周"},
              {id:6,name:"本月"},
              {id:7,name:"上月"},
            ],
            paixuptions:[
              { id: "dk", name: "带看"},
              { id: "cj", name: "成交"},
              { id: "total_commission", name: "业绩"},
              { id: "assign", name: "分客量"},
              { id: "dial", name: "拨打量"},
              { id: "dial_duration", name: "通话时长"},
              { id: "dial_put", name: "电话接通量"},
            ],//排序字段
            paramsdata:{
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                date_type:3,
                department_id:"",// 部门id
                sort:"",//排序字段
            },
            ribaoalldata:[],//日报汇总数据
            is_table_loading:false
            
        }
    },
    mounted() {
      let pagenum = localStorage.getItem( 'pagenum')
      this.paramsdata.per_page = Number(pagenum)||10
      this.getDailySummary()//获取日报汇总
      this.getBrokerDailyReport()//获取经纪人日报列表
    },

    methods:{
      //获取日报汇总
      getDailySummary(){
          //拷贝参数
          let params = JSON.parse(JSON.stringify(this.paramsdata))
          delete params.sort
            this.$http.getdailysummary(params).then((res)=>{
                if(res.status==200){
                    this.ribaoalldata = res.data
                    // console.log(res.data);
                }
            })
        },
        //获取经纪人日报列表
        getBrokerDailyReport(){
          this.is_table_loading = true
            this.$http.getbrokerdailyreport(this.paramsdata).then((res)=>{
                if(res.status==200){
                  this.is_table_loading = false
                    this.ribaodata = res.data.data
                    this.paramsdata.total = res.data.total
                    // console.log(this.ribaodata,"12121212121212");
                }
            })
        },
      //时间切换
      datetypechange(){
          this.timeValue = ""
          this.paramsdata.start_date = ""
          this.paramsdata.end_date = ""
          this.Refresh()
      },
      // 自定义筛选时间发生改变时触发
      onChangeTime(e) {
          this.paramsdata.date_type = ""
          if (e && e.length >= 2) {
            if (e[1].endsWith("00:00:00")) {
              e[1] = e[1].slice(0, -8) + "23:59:59";
            }
          }
          if(e){
            this.paramsdata.start_date = e[0]
            this.paramsdata.end_date = e[1]
          }else{
            this.paramsdata.start_date = ""
            this.paramsdata.end_date = ""
          }
          this.Refresh()
      },
      //刷新
      Refresh(){
        this.getDailySummary()//获取日报汇总
        this.getBrokerDailyReport()//获取经纪人日报列表
      },
      //清空
      empty(){
          this.paramsdata={
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                sort:"",//排序字段
                date_type:3,
                department_id:"",// 部门id
            }
         this.Refresh()
      },
      //分页
      onPageChange(current_page) {
          this.paramsdata.page = current_page
          this.Refresh()
      },
      //每页几条
      handleSizeChange(e){
          this.paramsdata.per_page = e
          this.Refresh()
      },
      //导出
      exporttakelook(){
          this.$confirm('确定要导出数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.exportdailyreport(this.paramsdata).then(res=>{
            if(res.status==200){
              window.open(res.data);
            }
          })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出'
            });          
          });
      },
      //跳转到成交报告
      tochengjiao(){
        this.$goPath('/crm_deal_report_index')
      },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .el-table .el-table__fixed{
          padding-bottom: 0px !important;
}
.pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .SearchCondition{
            margin: 20px;
            display: flex;
            justify-content: space-between;
            .block{
              display: flex;
            }
        }
        .head-list{
          display: flex;
        }
        .taketable{
            width: 97%;
            margin: 0 auto;
            margin-bottom: 20px;
            .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
            ::v-deep .el-table thead.is-group th{
              text-align: center;
            }
            .card-container {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              gap: 15px;
              padding: 0px 0px 20px 0px;
            }

            .data-card {
              background-color: #e6f7ff; // 浅蓝色背景
              border-left: 5px solid #409EFF; // 左侧边栏颜色
              padding: 15px;
              border-radius: 5px;
              width: calc(16% - 5px); // 每行显示5个卡片，
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
              // transition: transform 0.2s;
            
              // &:hover {
              //   transform: scale(1.02);
              // }
            
              p {
                margin: 5px 0;
              }
            }
        }
}
</style>