<template>
  <div>
    <el-tree
      :data="memberList"
      :show-checkbox="showCheckbox"
      :default-expand-all="defaultExpandAll"
      node-key="id"
      ref="tree"
      :check-strictly="checkStrictly"
      highlight-current
      :props="defaultProp"
      @node-click="clickItem"
      @check="checkChange"
      @current-change="currentChange"
      @check-change="currentCheckChange"
      :default-checked-keys="defaultValue"
    >
    </el-tree>
  </div>
</template>

<script>
export default {

  props: {
    list: {
      type: Array,
      default: () => []
    },
    from: {
      type: [String],
      default: "service",
    },
    value: {
      type: [String],
      default: "id",
    },

    checkStrictly: {
      type: [Boolean],
      default: true
    },
    showCheckbox: {
      type: [Boolean],
      default: true,

    },
    defaultExpandAll: {
      type: [Boolean],
      default: true,

    },
    keyData: {
      type: Array,
      default: () => []
    },
    source: {
      type: [String],
      default: "",
    },
    defaultValue: {
      type: Array,
      default: () => []
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'subs',
          label: 'name',
          value: "id",
          disabled: (data) => {
            return !data.user_name
          }
        }
      },
    }
  },
  name: 'memberList',
  data() {
    return {
      // checkedNode: [],
      defaultIds: [],
      appendIds: [],
      memberList: []
    }
  },
  created() {
    this.defaultIds = JSON.parse(JSON.stringify(this.defaultValue))
    this.defaultProp = this.defaultProps
    this.list.map(item => {
      this.memberList.push(JSON.parse(JSON.stringify(item)))
    })
  },
  components: {
  },
  mounted() {
    this.$nextTick(() => {
      this.changeSelected()
    })
  },
  methods: {
    checkChange(e, node) {
      if (node.checkedKeys) {
        for (let i = 0; i < this.defaultIds.length; i++) {
          if (!node.checkedKeys.includes(this.defaultIds[i])) {
            node.checkedKeys.push(this.defaultIds[i])
          }
        }
      }
      setTimeout(() => {
        this.$emit("onClickItem", node)
      }, 100);

    },
    changeSelected(ids = this.defaultValue, type = true) {
      // if (this.$refs.tree.getCheckedKeys().includes(...ids))
      console.log(ids, type);
      this.$refs.tree.setCheckedKeys(ids, type)
      setTimeout(() => {
        let nodes = this.$refs.tree.getCheckedNodes()
        let keys = this.$refs.tree.getCheckedKeys()
        let halfCheckedNodes = this.$refs.tree.getHalfCheckedNodes()
        let halfCheckedKeys = this.$refs.tree.getHalfCheckedKeys()
        let node = {
          checkedKeys: keys,
          checkedNodes: nodes,
          halfCheckedNodes,
          halfCheckedKeys,
        }
        // this.checkedNode = this.$refs.tree.getCheckedNodes()
        if (keys.length && nodes.length) {
          this.$emit("onClickItem", node)

        }

      }, 200);

    },
    async clickItem(data) {
      if (this.from != "service") return
      if (data.user_name) return
      let params = {
        department_id: data.id,
        is_wx: this.source == "renshi" ? 0 : 1,
        page: 1,
        per_page: 10000
      }
      let res = await this.$http.getCrmMemberList({ params }).catch(err => console.log(err))
      if (res.status == 200) {
        if (res.data.data.length) {
          let newIds = []
          res.data.data.map(item => {
            item.name = item.user_name
            item.id = item[this.value]
            newIds.push(item[this.value])
            return item
          })
          this.append(data, res.data.data, newIds)
        }
      }
    },
    append(data, newData, newIds) {
      if (data.subs) {
        // if (this.list && data.id == this.list[0].id) {
        if (!data.isAppend) {
          newData.map(item => {
            let obj = {
              id: item.id,
              pid: 0,
              user_name: item.user_name,
              name: item.user_name
            }
            obj[this.value] = item[this.value]
            data.subs.unshift(obj)
            data.isAppend = true
          })
          console.log(newIds, data.subs);
          // let filter = data.subs.filter(item => !newIds.includes(item[this.value]))
          // filter.map(item => {
          //   console.log(item, "11114555s", data);
          //   // if (this.appendIds.includes(item[this.value]) || this.appendIds.includes(item[this.value] + "")) {
          //   //   delete item.user_name
          //   // } else {
          //   //   this.appendIds.push(item[this.value])
          //   // }

          //   data.subs.push(item);
          //   data.isAppend = true
          // })
          // }
        }



      } else {
        console.log(12333);
        this.$set(data, 'subs', newData);
        data.isAppend = true
      }
      setTimeout(() => {
        this.$emit("setchecked")
      }, 100);





    },
    currentChange() {

    },
    currentCheckChange(data, type) {
      console.log(data, type);
      // this.checkedNode = this.$refs.tree.getCheckedNodes()
      if (type === false) {
        let index = this.defaultIds.findIndex(item => item == data.id)
        this.defaultIds.splice(index, 1)
      }
      this.$emit('currentCheckChange', { data, type })
    },



    // clickMenu (e, value) {
    //   console.log(e, 12323);
    //   this.$emit('onClickMenu', { detail: e.detail, item: value })
    // },
  }
}
</script>
<style scoped lang="scss">
.row_item {
  .img {
    width: 20px;
    height: 20px;
    object-fit: cover;
  }
}
.el-submenu .el-menu-item {
  padding: 0 20px 0 45px;
}
</style>