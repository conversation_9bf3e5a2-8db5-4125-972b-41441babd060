<template>
    <div class="member-box">
      <el-tree
        :data="list"
        :show-checkbox="showCheckbox"
        :default-expand-all="defaultExpandAll"
        node-key="id"
        ref="tree"
        :check-strictly="checkStrictly"
        highlight-current
        :props="defaultProp"
        :expand-on-click-node="false"
        @node-click="clickItem"
        @check="checkChange"
        @current-change="currentChange"
        @check-change="currentCheckChange"
        :default-checked-keys="defaultValue"
        :default-expanded-keys="defaultValue"
      >
      </el-tree>
    </div>
  </template>
  
  <script>
  export default {
  
    props: {
      list: {
        type: Array,
        default: () => []
      },
      from: {
        type: [String],
        default: "service",
      },
      value: {
        type: [String],
        default: "id",
      },
  
      checkStrictly: {
        type: [Boolean],
        default: true
      },
      showCheckbox: {
        type: [Boolean],
        default: true,
  
      },
      defaultExpandAll: {
        type: [<PERSON><PERSON><PERSON>],
        default: true,
  
      },
      keyData: {
        type: Array,
        default: () => []
      },
      defaultValue: {
        type: Array,
        default: () => []
      },
      defaultProps: {
        type: Object,
        default: () => {
          return {
            children: 'subs',
            label: 'name',
            value: "id",
            disabled: (data) => {
              return !data.user_name
            }
          }
        },
      }
    },
    name: 'memberList',
    data() {
      return {
        // checkedNode: [],
        defaultIds: [],
        appendIds: [],
        memberList: []
      }
    },
    created() {

      this.defaultIds = JSON.parse(JSON.stringify(this.defaultValue))
      this.defaultProp = this.defaultProps
      this.list.map(item => {
        this.memberList.push(JSON.parse(JSON.stringify(item)))
      })
    },
    components: {
    },
    mounted() {
      console.log(this.memberList,"this.memberList");
      this.$nextTick(() => {
        this.changeSelected()
      })
    },
    // watch:{
    //   memberList:{
    //     handler(newval,lodval) {
    //       console.log(newval,lodval,"newVal,oldVal");
    //     // this.shownum = newVal.some(item => item.number);
    //     // console.log(newVal.some(item => item.number));
    //   },
    //   }
    // },
    methods: {
      // 当复选框被点击的时候触发
      checkChange(e, node) {
        // console.log(e, node,"选中触发");
        const newCheckedKeys = node.checkedKeys.map(key => {
        if (typeof key === 'string' && key.includes('_')) {
          const [numStr] = key.split('_'); // 使用解构赋值获取第一个元素
          return parseInt(numStr); // 将字符串转换为数值
        } else {
          return key;
        }
      });
        console.log(newCheckedKeys);
        node.checkedKeys = newCheckedKeys
        this.$emit("onClickItem", node)
        // if (node.checkedKeys) {
        //   for (let i = 0; i < this.defaultIds.length; i++) {
        //     if (!node.checkedKeys.includes(this.defaultIds[i])) {
        //       node.checkedKeys.push(this.defaultIds[i])
        //     }
        //   }
        // }
        // setTimeout(() => {
        //   this.$emit("onClickItem", node)
        // }, 100);
      },
      changeSelected(ids = this.defaultValue, type = true) {
        // if (this.$refs.tree.getCheckedKeys().includes(...ids))
        console.log(ids, type);
        // this.$refs.tree.setCheckedKeys(ids, type)
        // setTimeout(() => {
        //   let nodes = this.$refs.tree.getCheckedNodes()
        //   let keys = this.$refs.tree.getCheckedKeys()
        //   let halfCheckedNodes = this.$refs.tree.getHalfCheckedNodes()
        //   let halfCheckedKeys = this.$refs.tree.getHalfCheckedKeys()
        //   let node = {
        //     checkedKeys: keys,
        //     checkedNodes: nodes,
        //     halfCheckedNodes,
        //     halfCheckedKeys,
        //   }
        //   // this.checkedNode = this.$refs.tree.getCheckedNodes()
        //   if (keys.length && nodes.length) {
        //     this.$emit("onClickItem", node)
        //   }
        // }, 200);
      },
      // 节点被点击时的回调
      async clickItem(data) {
        console.log(data);
        // if (this.from != "service") return
        // if (data.user_name) return
        // let params = {
        //   department_id: data.id,
        //   is_wx: 1
        // }
        // let res = await this.$http.getCrmMemberList({ params }).catch(err => console.log(err))
        // if (res.status == 200) {
        //   if (res.data.data.length) {
        //     let newIds = []
        //     res.data.data.map(item => {
        //       item.name = item.user_name
        //       item.id = item[this.value]
        //       newIds.push(item[this.value])
        //       return item
        //     })
        //     this.append(data, res.data.data, newIds)
        //   }
        // }
      },
    //   append(data, newData, newIds) {
    //     if (data.subs) {
    //       // if (this.list && data.id == this.list[0].id) {
    //       if (!data.isAppend) {
    //         newData.map(item => {
    //           let obj = {
    //             id: item.id,
    //             pid: 0,
    //             user_name: item.user_name,
    //             name: item.user_name
    //           }
    //           obj[this.value] = item[this.value]
    //           data.subs.unshift(obj)
    //           data.isAppend = true
    //         })
    //         console.log(newIds, data.subs);
    //       }
    //     } else {
    //       console.log(12333);
    //       this.$set(data, 'subs', newData);
    //       data.isAppend = true
    //     }
    //     setTimeout(() => {
    //       this.$emit("setchecked")
    //     }, 100);
    //   },
      // 当前选中节点变化时触发的事件  
      currentChange() {
  
      },
      // 节点选中状态发生变化时的回调
      currentCheckChange(data, type) {
        console.log(data, type);
        // this.checkedNode = this.$refs.tree.getCheckedNodes()
        // if (type === false) {
        //   let index = this.defaultIds.findIndex(item => item == data.id)
        //   this.defaultIds.splice(index, 1)
        // }
        // this.$emit('currentCheckChange', { data, type })
      },
    }
  }
  </script>
  <style scoped lang="scss">
  .member-box {
    height: 50vh;
    overflow-y: auto;
  }
  .row_item {
    .img {
      width: 20px;
      height: 20px;
      object-fit: cover;
    }
  }
  .el-submenu .el-menu-item {
    padding: 0 20px 0 45px;
  }
  </style>