<template>
    <div class="all_task_list">

        <div class="header-title div row">
            <!-- 导入任务列表 -->
            <div class="ht-title" v-for="item in titlelist" :key="item.id"
            :class="{isitem:titleid == item.id}"
            @click="tabititle(item.id)">
            {{ item.title }} </div>
            <div style="margin-right: 24px">
                <!-- <el-button
         
          type="primary"
          size="mini"
 
          >全部任务列表</el-button
        > -->

            </div>
        </div>
        <div style="margin-bottom: 15px; display: flex; align-items: center; justify-content: space-between;">
        <div style="display: flex; align-items: center;">
            <el-select v-model="typeid" slot="prepend" placeholder="可根据导入类型搜索">
              <el-option label="全部" value="0"></el-option>
              <el-option label="自定义导入" value="1"></el-option>
              <el-option label="抖音" value="2"></el-option>
              <el-option label="快手" value="3"></el-option>
              <el-option label="视频号" value="4"></el-option>
              <el-option label="巨好房" value="7"></el-option>
              <el-option label="腾讯广告" value="8"></el-option>
            </el-select>
            <el-button slot="append" icon="el-icon-search" @click="Search_Status"></el-button>
        </div>
        <span class="tip-text">超过24小时的上传文件,系统将自动清除</span>
    </div>
        <!-- 任务详情 -->    
        <!-- <el-row :gutter="20" v-if="show">
            <el-col :span="24">

                <div class="content-box-crm">
                    <div class="table-top-box div row">

                    </div>
                    <el-table :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
                        :row-style="$TableRowStyle"
                        v-loading="loading"> -->

                        <!-- <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-form label-position="left" inline class="demo-table-expand">
                                    <el-form-item label="管理员：">
                                        <span>{{ props.row.admin.user_name }}</span>
                                    </el-form-item>
                                    <el-form-item label="管理员电话：">
                                        <span>{{ props.row.admin.phone }}</span>
                                    </el-form-item>
                               
                                </el-form>
                            </template>
                        </el-table-column> -->
                        <!-- <el-table-column label="任务id" width="80" prop="id" align="left">

                        </el-table-column>
                        <el-table-column label="任务名称" prop="task_name" align="left">

                        </el-table-column> -->
                        <!-- <el-table-column label="操作人员" prop="admin.user_name" width="80" align="left">
                                
                        </el-table-column> -->
                        <!-- <el-table-column label="管理员电话" prop="admin.phone" align="left">
                                
                            </el-table-column> -->
                        <!-- <el-table-column label="OSS文件地址" prop="file_url" align="left">

                        </el-table-column>
                        <el-table-column label="新增客户数量" prop="success_num" width="120" align="left">
                        </el-table-column>
                        <el-table-column label="系统重复客户数量" prop="crm_repeat_num" width="140" align="left">

                        </el-table-column>
                        <el-table-column label="表格重复客户数量" prop="excel_repeat_num" width="140" align="left">

                        </el-table-column>
                        <el-table-column label="失败客户数量" prop="error_num" width="120" align="left">

                        </el-table-column>
                        <el-table-column label="创建时间" prop="created_at" width="100" align="left">

                        </el-table-column>
                        <el-table-column label="操作" width="100" align="center" scope="scope">

                            <template slot-scope="scope">
                                <el-link type="primary" @click="look_item(scope.row.id)">详情</el-link>
                                <el-link type="primary" @click="down_item(scope.row.file_url)" style="margin:0  10px;">下载</el-link>
                            </template>
                        </el-table-column>

                    </el-table> -->
                    <!-- 分页 -->
                    <!-- <div class="block">
                        <el-pagination
                        style="text-align: end; margin-top: 24px"
                          @size-change="handleSizeChange"
                          @current-change="handleCurrentChange"
                          :current-page="telinfo_params.page"
                          :page-sizes="[10, 20, 30, 40,50,100]"
                          :page-size="telinfo_params.per_page"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="telinfoTotal">
                        </el-pagination>
                    </div>
                </div>
            </el-col> -->
            <!-- <el-col v-if="!auth_transaction &&!showAdd &loadEnd" :span="24">
          <myEmpty desc="当前用户不可查看"></myEmpty>
        </el-col> -->
        <!-- </el-row> -->
        <!-- 任务详情(新) -->
        <el-row :gutter="20" v-if="show1">
            <el-col :span="24">

                <div class="content-box-crm">
                    <div class="table-top-box div row">

                    </div>
                    <el-table :data="tableDataA" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
                        :row-style="$TableRowStyle"
                        v-loading="loading">

                        <!-- <el-table-column type="expand">
                            <template slot-scope="props">
                                <el-form label-position="left" inline class="demo-table-expand">
                                    <el-form-item label="管理员：">
                                        <span>{{ props.row.admin.user_name }}</span>
                                    </el-form-item>
                                    <el-form-item label="管理员电话：">
                                        <span>{{ props.row.admin.phone }}</span>
                                    </el-form-item>
                               
                                </el-form>
                            </template>
                        </el-table-column> -->
                        <!-- <el-table-column label="任务id" width="80" prop="id" align="left">

                        </el-table-column>
                        <el-table-column label="任务名称" prop="task_name" align="left">

                        </el-table-column> -->
                        <el-table-column label="操作人员" prop="admin.user_name" align="left">
                                
                        </el-table-column>
                        <!-- <el-table-column label="管理员电话" prop="admin.phone" align="left">
                                
                            </el-table-column> -->
                        <el-table-column label="导入类型" prop="type" align="left">
                            <template slot-scope="scope">
                                        <div v-if="scope.row.type == 1">自定义导入</div>
                                        <div v-if="scope.row.type == 2">抖音</div>
                                        <div v-if="scope.row.type== 3">快手</div>
                                        <div v-if="scope.row.type== 4">海豚知道</div>
                                        <div v-if="scope.row.type== 5">视频号</div>
                                        <div v-if="scope.row.type== 7">巨好房</div>
                                        <div v-if="scope.row.type== 8">腾讯广告</div>
                                    </template>
                        </el-table-column>
                         <el-table-column label="状态" prop="status" width="140" align="left">
                            <template slot-scope="scope">
                                        <el-tag type="warning" size="default" v-if="scope.row.status == 0">导入中</el-tag>
                                        <el-tag type="success" size="default" v-if="scope.row.status == 1">导入完成</el-tag>
                                    </template>
                        </el-table-column>
                        <el-table-column label="总条数" prop="total_num" width="140" align="left">
                        </el-table-column>
                        <el-table-column label="新增客户数量" prop="success_num" align="left">
                        </el-table-column>
                        <!-- <el-table-column label="系统重复客户数量" prop="repeat_num" width="140" align="left">

                        </el-table-column> -->
                        <el-table-column label="重复客户数量" prop="repeat_num" align="left">

                        </el-table-column>
                        <el-table-column label="失败客户数量" prop="error_num" align="left">

                        </el-table-column>
                        <el-table-column label="创建时间" prop="created_at" align="left">

                        </el-table-column>
                        <el-table-column label="操作" align="center" scope="scope">

                            <template slot-scope="scope">
                                <template v-if="isExpired(scope.row.created_at)">
                                    <span>----</span>
                                </template>
                                <template v-else>
                                    <el-link type="primary" @click="look_item(scope.row.id)">详情</el-link>
                                    <el-link v-if="scope.row.status == 1" type="primary" @click="down_item(scope.row.file_url)" style="margin:0  10px;">下载</el-link>
                                </template>
                            </template>
                        </el-table-column>

                    </el-table>
                    <!-- 分页 -->
                    <div class="block">
                        <div> 
                            <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                        </div>
                        <el-pagination
                          @size-change="handleSizeChange"
                          @current-change="handleCurrentChange"
                          :current-page="telinfo_params.page"
                          :page-sizes="[10, 20, 30, 40,50,100]"
                          :page-size="telinfo_params.per_page"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="telinfoTotal">
                        </el-pagination>
                    </div>
                </div>
            </el-col>
            <!-- <el-col v-if="!auth_transaction &&!showAdd &loadEnd" :span="24">
          <myEmpty desc="当前用户不可查看"></myEmpty>
        </el-col> -->
        </el-row>

    </div>
</template>
  
<script>

export default {
    name: "crm_customer_all_task_list",
    components: {
        // myLabel,

    },
    data() {
        return {
            telinfo_params: {
                page: 1,

                per_page: 10,
            },
            tableData: [],
            tableDataA:[],
            task_id: 0,
            website_id: 0,
            telinfoTotal: 0,
            loading:false,
            show:false,
            show1:false,
            typeid:"0",
            titlelist:[
                {id:1,title:'任务列表'},
                {id:2,title:"流转客任务列表"}
            ],
            titleid:1,
        };
    },
    mounted() {
        let pagenum = localStorage.getItem( 'pagenum')
        this.telinfo_params.per_page = Number(pagenum)||10
        if (this.$route.query.website_id) {
            this.website_id = this.$route.query.website_id
        }
        // if(this.website_id==176||this.website_id==109){
        //    this.titlelist.push({id:2,title:"流转客任务列表"})
        // }
        if(this.$route.query.information&&this.$route.query.information==1){
            this.titleid = 2
            this.information()
        }else{
           this.getData(); 
        }
    },
    created() {
        // this.getData();
    },
    computed: {

    },
    methods: {
        // 拨打记录-直拨记录(crm记录)
        getData() {
            let item = {
                page: this.telinfo_params.page,
                per_page: this.telinfo_params.per_page,
                // task_id: this.task_id
            }
                if(this.typeid){
                    item.type = this.telinfo_params.type
                }
                if(this.typeid==0){
                    delete item.type
                }
                this.loading = true

                this.$ajax.house.newgetAllTaskList(item).then((res) => {
                    if (res.status == 200) {
                        this.tableDataA = res.data.data;
                        this.show1 = true
                        this.telinfoTotal = res.data.total
                        this.loading = false
                    }
                });
        },
        //流转客的导入列表记录
        information(){
            let item = {
                page: this.telinfo_params.page,
                per_page: this.telinfo_params.per_page,
                // task_id: this.task_id
            }
                if(this.typeid){
                    item.type = this.telinfo_params.type
                }
                if(this.typeid==0){
                    delete item.type
                }
                this.loading = true

                this.$http.newinformationimportlist(item).then((res) => {
                    if (res.status == 200) {
                        this.tableDataA = res.data.data;
                        this.show1 = true
                        this.telinfoTotal = res.data.total
                        this.loading = false
                    }
                });
        },
        // 分页器-当前页
        handleCurrentChange(val) {
            this.telinfo_params.page = val
            if(this.titleid==1){
               this.getData();
            }else{
                   this.information()
            }
            console.log(`当前页: ${val}`);
        },
        //查看详情
        look_item(id) {
            console.log(id);
            let url = `/crm_customer_task_list?task_id=` + id;
            if(this.titleid==2){
                url = `/crm_customer_task_list?task_id= + ${id}&information=1`;

            }
            this.$goPath(url); // 跳转客户详情
        },
        //刷新按钮，刷新表格
        Refresh(){
            if(this.titleid==1){
              this.getData()  
            }else{
                this.information()
            }
            
        },
        //下载
        down_item(url){
            window.open(url)
        },
        handleSizeChange(val) {
            this.telinfo_params.per_page = val 
            if(this.titleid==1){
              this.getData()  
            }else{
                this.information()
            }
        },
        //导入类型
        Search_Status(){

            if(this.typeid==""||this.typeid==undefined){
                this.$message.warning("请选择状态")
                return
            }else{
                this.telinfo_params.type = this.typeid
                if(this.titleid==1){
                  this.getData()  
                }else{
                    this.information()
                }
            }
       },
    //    头部列表切换
       tabititle(id){
        this.titleid = id
        if(this.titleid==1){
            this.getData();
        }else{
            this.information()
        }
       },
       // 判断是否超过24小时
       isExpired(createdAt) {
           if (!createdAt) return false;
           const now = new Date();
           const createTime = new Date(createdAt);
           const diffInHours = (now - createTime) / (1000 * 60 * 60);
           return diffInHours > 24;
       }
    },
};
</script>
  
<style scoped lang="scss">
.all_task_list {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 24px;

    .bottom-border {
        align-items: center;
        padding-bottom: 24px;
        justify-content: flex-start;
        border-bottom: 1px dashed #e2e2e2;

        .text {
            font-size: 14px;
            color: #8a929f;

            .label {
                width: 70px;
                display: inline-block;
                text-align: right;
            }
        }
    }

    .header-title {
        height: 50px;
        line-height: 50px;
        background: #fff;
        margin: -24px -24px 24px;
        // justify-content: space-between;
        align-items: center;
        cursor: pointer;

        .ht-title {
            margin-left: 43px;
            font-size: 18px;
            color: #2e3c4e;
            &.isitem{
              color: #165DFF;
              border: 2px solid white;
              border-bottom-color: #2d84fb;
            }
        }
    }
    .block{
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
    }

    .tip-text {
        font-size: 14px;
        color: #f56c6c;
        line-height: 32px;
        background: #fef0f0;
        padding: 4px 12px;
        border-radius: 4px;
        border: 1px solid #fbc4c4;
    }
}
</style>
  