<template>
        <div class="tab-content-container">
		<div class="tab-content-body white topped">
			<div class="body-inner main-scroll" ref="scrollElem">
				<el-tabs class="header-tabs" v-model="componentId">
					<el-tab-pane label="用户" name="userlist"></el-tab-pane>
					<el-tab-pane label="获客" name="huokelist"></el-tab-pane>
				</el-tabs>

				<keep-alive>
					<component :is="componentId" @room-change="handleRoomChange" :roomId="roomId"
					:platform_cat="platform_cat"
					ref="list"></component>
				</keep-alive>

			</div>
		</div>
	</div>
</template>

<script>
import userlist from "./user_list.vue"
import huokelist from './huoke_list.vue'

export default {
	name:"configure_user_list",
	components:{
		userlist,
		huokelist
	},

    data() {
        return {
            componentId: 'userlist',
			roomId: '',
			platform_cat:"",
        }
    },
	created(){
		this.platform_cat = this.$route.query.platform
		console.log(this.platform_cat);
	},
	methods:{
		handleRoomChange(room_id){
			this.roomId = room_id;
		},
	},
}
</script>

<style lang="scss" scoped>
    .tab-content-body{
	padding: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	padding-bottom: 48px;
}
.body-inner{
	flex: 1;
	overflow: auto;
	padding: 0 28px 28px;
}

	
.header-tabs{
	::v-deep.el-tabs__item{
		font-size: 16px !important;
	}
	padding: 16px 0 0;
}
::v-deep{
	.tab-content-footer{
		position: absolute;
		z-index: 99;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding-left: 28px;
		padding-right: 28px;
		white-space: nowrap;
		overflow: hidden;
	}
}
</style>