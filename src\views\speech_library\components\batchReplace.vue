<template>
    <el-dialog :visible.sync="show" title="批量替换" width="500px">   
        <el-form label-width="100px" class="replace-form" v-if="show">
            <el-form-item label="查找内容">
                <el-input v-model="params.replace_content" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="替换为">
                <el-input v-model="params.content" maxlength="10" show-word-limit></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="submiting">确 认</el-button>
        </span>
    </el-dialog>
</template>



<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        package_id: '',
    },
    data(){
        return {
            show: false,
            submiting: false,
            params: {
                replace_content: '',
                content: '',
            }
        }
    },
    watch: {
        visible(val){
            if(val){
                this.params.replace_content = '';
                this.params.content = '';
            }
            this.show = val;
        },
        show(val){
            val != this.visible && this.$emit("update:visible", val)
        }
    },
    methods: {
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(this.params.replace_content === ''){
                this.$message.warning('请输入查找内容')
                return;
            }
            if(this.params.content === ''){
                this.$message.warning('请输入替换内容')
                return;
            }
            if(this.params.content === this.params.replace_content){
                this.$message.warning('替换和查找内容相同')
                return;
            }
     
            this.submiting = true;
            const params = {...this.params, package_id: this.package_id}
            const res = await this.$http.speechReplaceKeywords(params).catch(e=>{});    
            this.submiting = false;
            if(res.status == 200){
                this.$message.success(res?.data?.msg || '替换成功');
                this.show = false;
                this.$emit('success');
            }
            
        }
    }
}
</script>
<style lang="scss" scoped>

</style>