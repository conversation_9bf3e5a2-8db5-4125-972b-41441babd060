<template>
  <div>
    <div class="tips">
      <div>
        1.在二维码处设置的欢迎语会被优先推送，如果成员在二维码处设置了欢迎语，在此设置的欢迎语
        不会生效
      </div>

      <div>
        2.欢迎语将在客户加为好友后20秒内发下，因网络延可能造成发送不成功
      </div>
    </div>
    <el-form label-width="100px">
      <el-form-item label="欢迎语">
        <welcome-mes
          style="padding-right: 20px"
          ref="member_welcome"
          :value="welcome_mes"
        ></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>
import config from "@/utils/config";
import welcomeMes from "@/components/navMain/crm/components/welcome_mes"
export default {
  components: {
    welcomeMes
  },
  props: ["navList"],
  data() {
    return {
      form_params: {
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      isSubmiting: false,

      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
          pic_url: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
    };
  },
  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      let _welcome_mes = this.$refs.member_welcome.welcome_mes
      // let _welcome_mes = Object.assign({}, this.welcome_mes);

      if (_welcome_mes.text && (!_welcome_mes.text.desc)) {
        this.$message.warning("欢迎语不能为空");
        return;
      }
      if (_welcome_mes.image) {
        if (!_welcome_mes.image.media_id) {
          delete _welcome_mes.image;
        }
      }
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key] && Object.keys(linkArr).includes(key)) {
            emptyLink.push(key);
          }
        }
        if (emptyLink.length == Object.keys(linkArr).length) {
          console.log(123);
          emptyLink.length = 0;
          delete _welcome_mes.link;
        } else if (emptyLink.length) {
          console.log(12323);
          this.$message.warning(linkArr[emptyLink[0]]);
          return;
        }
      }
      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (!_welcome_mes.miniprogram[key]) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          emptyMini.length = 0;
          delete _welcome_mes.miniprogram;
        } else if (emptyMini.length) {
          this.$message.warning(miniArr[emptyMini[0]]);
          return;
        }
      }

      if (_welcome_mes.video && (!_welcome_mes.video.media_id)) {
        delete _welcome_mes.video;
      }
      if (_welcome_mes.file && (!_welcome_mes.file.media_id)) {
        delete _welcome_mes.file;
      }
      params.welcome_msg = JSON.stringify(_welcome_mes);

      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .addCrmMyWelcomeWord(params)
        .then((res) => {
          console.log(res);
          if (res.status == 200) {
            this.$message.success(res.data.msg || "添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error(res.data.msg || "添加失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },

  }
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 30px;
  &.type_tips {
    margin-bottom: 0;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 115px;
    height: 115px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;
    &.active {
      border: 5px solid #409eff;
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    .file_img {
      width: 160px;
      height: 160px;
      text-align: center;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 5px;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;
  &.active {
    border: 1px solid #a6e22e;
  }
}
.upload-demo {
  margin-right: 5px;
}
</style>
