<template>
    <div class="page">
      <div>
        <div class="Header">带看数据</div>
      </div>
        <div class="pagecontent">
          <div class="SearchCondition">
            <div class="block">
              <el-select v-model="paramsdata.date_type" placeholder="时间类型"
                size="small" style="width: 135px;margin-right:10px;;" @change="Sortevents">
                  <el-option
                    v-for="item in lookoptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
              <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                @change="onChangeTime">
              </el-date-picker>
              <el-cascader style="margin:0px 10px;" size="small" v-model="member_value"
                :options="member_listNEW" clearable filterable placeholder="成员" :style="{
                    minWidth: '20px',
                     width: '110px',
                    }" :props="{
                        label: 'user_name',
                        value: 'id',
                        children: 'subs',
                        checkStrictly: true,
                    }" @change="loadFirstLevelChildren">
                </el-cascader>
                <el-select v-model="paramsdata.sort" placeholder="排序"
                size="small" style="width: 145px;" @change="Sortevents"
                clearable>
                  <el-option
                    v-for="item in sortoptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.value">
                  </el-option>
                </el-select>
            </div>
            <div class="head-list">
              <el-button v-show="this.show>0" size="small" type="primary" @click="exporttakelook">导出</el-button>
                <!-- <el-button class="listbnt" size="mini" @click="doulist">
                  <div class="flex-row items-center">
                    <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinlist.png" alt="">
                    <span>数据列表</span>
                  </div>
                </el-button> -->
            </div>
          </div>
          <div class="sevensCondition" v-if="sevendata.length > 2">
            <div class="sevenstyle" :class="{ isactive: sevenid == item.date }" v-for="(item, index) in sevendata" :key="index"
             @click="changeseven(item)">
              <div class="today" v-if="item.is_today == 1"></div>
              <div class="date-wrapper">
                <div class="date" >{{ item.date+" "+item.week_time }}</div>
                <div class="custom-num" :class="{numcolor:item.is_today == 1}">{{ item.custom_num }}</div>
              </div>
            </div>
          </div>
          <div class="taketable" v-fixed-scroll="62">
            <el-table
            v-loading="is_table_loading"
                :data="classifydata"
                style="width: 100%;margin-bottom: 20px;"
                :header-cell-style="{ background: '#EBF0F7' }"
                row-key="id"
                border
                show-summary
                :summary-method="handleSummary">
                <el-table-column
                  prop="user_name"
                  align="center"
                  label="姓名"
                  >
                </el-table-column>
                <el-table-column
                  prop="custom_num"
                  align="center"
                  label="带看客户量"
                  v-slot="{ row }">
                  <div style="font-weight: bold; color:red;">
                     {{row.custom_num}}
                  </div>
                
                </el-table-column>
                <el-table-column
                  prop="first_take_num"
                  align="center"
                  label="首看次数"
                  >
                </el-table-column>
                <el-table-column
                  prop="again_take_num"
                  align="center"
                  label="复看次数"
                  >
                </el-table-column>
                <el-table-column
                  prop="total_take_num"
                  align="center"
                  label="带看总次数"
                  v-slot="{ row }">
                  <div style="font-weight: bold;">
                    {{row.total_take_num}}
                  </div>
                </el-table-column>
                <el-table-column
                  prop="accompany_take_num"
                  align="center"
                  label="陪看次数"
                  >
                </el-table-column>
                <el-table-column
                  prop="take_date"
                  align="center"
                  label="最近带看日期"
                  v-slot="{ row }">
                  {{row.take_date}} {{row.take_time&&row.take_time==1 ?"上午":row.take_time==2?"下午": row.take_time==3?"晚上":""}}
                </el-table-column>
            </el-table>
          <div class="page_footer flex-row items-center">
            <div class="page_footer_l flex-row flex-1 items-center">
              <div class="head-list">
                <el-button type="primary" size="small" @click="empty">清空</el-button>
              </div>
              <div class="head-list">
                <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
              </div>
            </div>
            <div style="margin-right:10px;">
              <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
                :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                @current-change="onPageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
          </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            timeValue:"",//时间检索
            pickerOptions: {
                shortcuts: [{
                  text: '今天',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                    text: '昨天',
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date(end);
                      start.setDate(start.getDate() - 1);
                      end.setDate(start.getDate());
                      picker.$emit('pick', [start, end]);
                    }
                  },{
                  text: '本周',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上周',
                  onClick(picker) {
                    const end = new Date(); // 获取当前日期
                      end.setDate(end.getDate() - end.getDay() - 7); // 获取上周的最后一天
                      end.setHours(23, 59, 59, 0);
                      const start = new Date(end);
                      start.setDate(start.getDate() - 6); // 获取上一周的第一天
                      start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '本月',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上月',
                  onClick(picker) {
                    const end = new Date();
                    end.setDate(0); // 获取上个月的最后一天
                    end.setHours(23, 59, 59, 0);
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                },]
            },
            member_value:"",
            //成员
            member_listNEW: [],
            classifydata:[],//表格数据
            params:{
                total:0,
                per_page:10,
                page:1
            },
            paramsdata:{
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                admin_id:"",
                sort:"",
                date_type:1
            },
            lookoptions:[
              {id:1,name:"带看日期"},
              {id:2,name:"带看创建时间"},
            ],
            sortoptions:[
              {id:1,value:"custom_num",name:"带看客户量降序"},
              {id:2,value:"first_take_num",name:"首看降序"},
              {id:3,value:"again_take_num",name:"复看降序"},
              {id:4,value:"total_take_num",name:"带看总量降序"},
              {id:5,value:"accompany_take_num",name:"陪看降序"},
            ],
            show:0,
            is_table_loading:false,
            sevendata:[
              {date:"全部",week_time:""},
            ],
            sevenid:"全部",
            website_id:"",
        }
    },
    created(){
        // 赋值website_id
        if (this.$route.query.website_id) {
          this.website_id = this.$route.query.website_id;
        }
        let pagenum = localStorage.getItem( 'pagenum')
        this.params.per_page = Number(pagenum)||10
        this.paramsdata.per_page = Number(pagenum)||10
    },
    mounted(){
      if(this.$store.state.ismanager){
        this.show =  this.$store.state.ismanager
      }else{
        this.btnexport()
      }
        
        this.getlookdata()
        this.MembersNEW()
        this.btnexport()
        this.getsevenlookdata()
    },
    methods:{
      handleSummary({ columns, data }) {
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          if (index === 6) {
            sums[index] = '--';
            return;
          }
          if (column.property === 'custom_num' || column.property === 'first_take_num' || column.property === 'again_take_num'
          || column.property === 'total_take_num' || column.property === 'accompany_take_num') {
            const values = data.map(item => parseFloat(item[column.property]));
            if (!isNaN(values[0])) {
              sums[index] = values.reduce((prev, curr) => {
                const value = parseFloat(curr);
                return isNaN(value) ? prev : prev + value;
              }, 0);
              sums[index] = Math.round(sums[index] * 100) / 100;
            } else {
              sums[index] = '';
            }
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
      //获取近7天的带看数据
      getsevenlookdata(){
        this.$http.getsevenlookdata().then(res=>{
          if(res.status==200){
            this.sevendata.push(...res.data.data)
          }
        })
      },
      //七天数据切换
      changeseven(row){
        let currentDate = new Date();
        let currentYear = currentDate.getFullYear();
        this.timeValue =  ""
        this.sevenid = row.date
        if(row.date=="全部"){
          this.paramsdata.start_date = ""
          this.paramsdata.end_date = ""
        }else{
          this.paramsdata.start_date = currentYear+"-"+row.date
          this.paramsdata.end_date = currentYear+"-"+row.date 
        }
        this.paramsdata.page = 1; // 显示第一页
        this.params.page = 1; // 显示第一页
        this.getlookdata(); // 获取最新数据
      
      },
        //获取是否是可以显示导出按钮
        btnexport(){
          this.$http.determinecustomeradmin().then((res)=>{
            if(res.status==200){
              console.log(res.data," //判断是不是客户管理员");
              this.show = res.data.is_manager
            }
          })
        },
        //获取表格数据
        getlookdata(){
          this.is_table_loading = true
            this.$http.gettakelooklist(this.paramsdata).then((res)=>{
                if(res.status==200){
                  this.is_table_loading = false
                    this.classifydata = res.data.data
                    this.params.total = res.data.total
                }
            })
        },
        // 自定义筛选时间发生改变时触发
        onChangeTime(e) {
          this.paramsdata.start_date = e ? e[0] : ""; // 赋值开始时间
          this.paramsdata.end_date = e? e[1] : ""; // 赋值结束时间
          this.sevenid="全部"
          this.paramsdata.page = 1; // 显示第一页
          this.params.page = 1; // 显示第一页
          this.getlookdata(); // 获取最新数据
        },
        // 获取成员的接口（新）
        MembersNEW(){
          this.$http.getDepartmentMemberListNew().then((res)=>{
            if(res.status==200){
                console.log(res.data);
              this.member_listNEW = res.data
            }
          })
        },
        //成员
        loadFirstLevelChildren(value) {
          this.paramsdata.admin_id = value[0]
          this.getlookdata(); // 获取最新数据
        },
        Sortevents(){
          this.getlookdata(); // 获取最新数据
        },
        //跳转到数据列表页
        doulist() {
          this.$goPath(`/crm_Follow_up_list`);
        },
        exporttakelook(){
          this.$confirm('确定要导出数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
           this.$http.exportandviewdata(this.paramsdata).then((res)=>{
            if(res.status == 200){
              window.open(res.data);
            }
           })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出'
            });          
          });
        },
        //分页
        onPageChange(current_page) {
          this.params.page = current_page;
          this.paramsdata.page = current_page;
          this.getlookdata();
        },
        //每页几条
        handleSizeChange(e){
          this.params.per_page = e
          this.paramsdata.per_page = e;
          this.getlookdata();
        },
        //清空
        empty(){
          this.timeValue =  ""
          this.member_value = ""
          this.paramsdata={
            per_page:10,
            page:1,
            start_date:"",
            end_date:"",
            admin_id:"",
            sort:"",
            date_type:1
          }
          this.sevenid="全部"
          this.getlookdata();
        },
        //刷新
        Refresh(){
          this.getlookdata();
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 20px 24px 80px;
    .Header{
      width: 110px;
      height: 40px;
      background-color: #ffffff;
      text-align: center;
      line-height: 42px;
      color: #8a929f;
    }
    .pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .SearchCondition{
            margin:20px 20px 5px 20px;
            display: flex;
            justify-content: space-between;
        }
        .sevensCondition{
          margin:6px 20px;
          display: flex;
          justify-content: space-between;
        }
        .sevenstyle {
          position: relative; /* 添加相对定位，使得子元素可以使用绝对定位 */
          width: 200px;
          height: 80px;
          background-color: #f5f7fa;
          border-radius: 4px;
          color: #909399;
          margin: 10px;
          text-align: center;
          cursor: pointer;
          border: 1px solid #f5f7fa;
          display: flex;
          align-items: center;
        }

        .sevenstyle .today {
          position: absolute;
          top: 3px;
          right: 5px;
          width: 8px;
          height: 8px;
          background-color: red;
          border-radius: 50%;
        }

        .date-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          .date{
            font-size: 14px;
          }
          .custom-num{
            color:#606266;
            font-size: 20px;
          }
          .numcolor{
            color:red;
          }
        }

        .isactive {
          color: #409eff;
          background: #ecf5ff;
          border: 1px solid #b3d8ff;
        }

        .sevenstyle:first-child {
          margin-left: 0;
        }

        .sevenstyle:last-child {
          margin-right: 0;
        }
        .head-list{
          display: flex;
        }
        .taketable{
            width: 97%;
            margin: 0 auto;
            .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
        }
    }
}
</style>