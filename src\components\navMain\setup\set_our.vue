<template>
  <el-main>
    <div v-if="false" class="top-box div row">
      <div class="top-item">
        <div class="top">
          <span>{{ sms_amount.sms_balance_total || 0 }}</span
          >条
        </div>
        <div class="bottom">短信余额</div>
      </div>
      <div class="top-item">
        <div class="top">
          <span>{{ sms_amount.sms_have_sent || 0 }}</span
          >条
        </div>
        <div class="bottom">短信发送</div>
      </div>
      <div class="top-item" @click="$goPath('/sms_list')">
        <div class="top el-icon-chat-dot-round font"></div>
        <div class="bottom">充值短信</div>
      </div>
      <div class="top-item" @click="$goPath('/add_system_msg')">
        <div class="top el-icon-s-comment font"></div>
        <div class="bottom">消息通知</div>
      </div>
    </div>
    <myTable :table-list="tableData" :header="table_header"></myTable>
    <el-dialog title="免责声明" :visible.sync="dialogShow">
      <el-form :model="disclaimer_form" label-width="100px">
        <el-form-item label="选择类型：">
          <el-select
            @change="onChange"
            v-model="disclaimer_form.category"
            placeholder="请选择"
          >
            <el-option
              v-for="item in category_list"
              :key="item.value"
              :label="item.description"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="免责声明：">
          <el-input
            style="width: 300px"
            type="textarea"
            rows="10"
            placeholder="请输入免责声明内容"
            v-model="disclaimer_form.content"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item
          ><el-button
            type="primary"
            @click="onCreate"
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            >确定</el-button
          ></el-form-item
        > -->
      </el-form>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      width="50%"
      title="短信配置"
      :visible.sync="dialogShowSms"
    >
      <el-form label-width="auto" :model="config_obj" style="800px">
        <!-- <el-form-item label="短信配置：">
          <el-radio-group
            v-model="config_obj.config_category"
            @change="changeConfigCategory"
          >
            <el-radio-button
              v-for="item in config_category_list"
              :key="item.value"
              :label="item.value"
              :disabled="config_obj.config_category == 1"
              >{{ item.desc }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="是否开启：">
          <el-radio-group v-model="config_obj.enable">
            <el-radio
              v-for="item in enable_list"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="短信签名： "
          v-if="config_obj.config_category == 0"
        >
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.sign_name"
          ></el-input>
        </el-form-item>
        <el-form-item label="access_key_id：">
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.access_key_id"
          ></el-input>
        </el-form-item>
        <el-form-item label="access_secret：">
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.access_secret"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="验证码短信模板编号："
          v-if="config_obj.config_category == 0"
        >
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.captcha_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button
            type="primary"
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            @click="onSubmit"
            >提交修改</el-button
          >
        </el-form-item>
      </el-form>
      <!-- <myForm :form_create="form_create_obj" @onClick="onSubmit"></myForm> -->
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      width="50%"
      title="微信消息配置"
      :visible.sync="dialogShowWX"
    >
      <el-form label-width="auto" :model="config_obj_wx" ref="config_obj_wx">
        <el-form-item label="是否开启：" prop="enable">
          <el-radio-group v-model="config_obj_wx.enable">
            <el-radio
              v-for="item in enable_list"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="客户报备成功提醒："
          prop="new_reported_status_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.new_reported_status_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="客户状态更新通知："
          prop="change_reported_status_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.change_reported_status_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="客户跟进提醒："
          prop="customer_follow_up_remind_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.customer_follow_up_remind_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户成交通知：" prop="deal_status_template_code">
          <el-input
            type="textarea"
            v-model="config_obj_wx.deal_status_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="佣金发放通知："
          prop="settle_brokerage_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.settle_brokerage_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="账户变更通知："
          prop="brokerage_balance_change_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.brokerage_balance_change_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button
            type="primary"
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            @click="onSubmitWx"
            >提交修改</el-button
          >
        </el-form-item>
      </el-form>
      <!-- <myForm :form_create="form_create_obj_wx" @onClick="onSubmitWx"></myForm> -->
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      width="50%"
      title="企微配置"
      :visible.sync="weiXin"
    >
      <el-form label-width="auto" :model="config_obj_wx" ref="config_obj_wx">
        <el-form-item
          label="通讯录secret："
          prop="new_reported_status_template_code"
        >
          <el-input type="textarea" v-model="contact_secret"></el-input>
        </el-form-item>
        <el-form-item
          label="客户联系secret："
          prop="change_reported_status_template_code"
        >
          <el-input type="textarea" v-model="external_secret"></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button
            type="primary"
            v-loading="is_button_loading"
            @click="onSubmitSecret"
            :disabled="is_button_loading"
            >提交修改</el-button
          >
        </el-form-item>
      </el-form>
      <!-- <myForm :form_create="form_create_obj_wx" @onClick="onSubmitWx"></myForm> -->
    </el-dialog>
    <el-dialog title="消息开关" :visible.sync="is_message_dialog">
      <el-table :data="message_form">
        <el-table-column label="id" prop="id"></el-table-column>
        <el-table-column label="名称">
          <template slot-scope="scope">
            {{ scope.row.name === "ALI_SMS" ? "短信" : "微信通知" }}
          </template>
        </el-table-column>
        <el-table-column label="开关">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.value"
              active-value="1"
              inactive-value="0"
              @change="onChangeSwitch(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </el-main>
</template>

<script>
import myTable from "@/components/components/my_table";
import config from "@/utils/config.js";
export default {
  name: "set_our",
  components: {
    myTable,
  },
  data() {
    return {
      contact_secret: "",
      external_secret: "",
      tableData: [],
      multipleSelection: [],
      dialogShow: false,
      disclaimer_form: {
        category: "1",
      },
      category_list: [],
      table_header: [
        { prop: "name", label: "类型", align: "start" },
        {
          label: "操作",
          width: "200",
          render: (h, data) => {
            return (
              <el-button
                icon="el-icon-view"
                type="success"
                size="mini"
                onClick={() => {
                  this.checkData(data.row);
                }}
              >
                查看
              </el-button>
            );
          },
        },
      ],
      dialogShowSms: false,
      config_obj: {
        enable: "1",
        config_category: "1",
        access_key_id: "",
        access_secret: "",
        captcha_template_code: "",
        sign_name: "",
      },
      config_obj_wx: {
        enable: "1",
        new_reported_status_template_code: "",
        change_reported_status_template_code: "",
        deal_status_template_code: "",
        settle_brokerage_template_code: "",
        brokerage_balance_change_template_code: "",
        customer_follow_up_remind_template_code: "",
      },
      config_list: [],
      config_category_list: [
        { value: "0", desc: "自定义配置" },
        { value: "1", desc: "系统配置" },
      ],
      enable_list: [
        { id: "1", name: "开启" },
        { id: "0", name: "关闭" },
      ],
      dialogShowWX: false,
      weiXin: false,
      is_message_dialog: false, // 消息开关
      message_form: [],
      sms_amount: {},
      is_button_loading: false,
      roleName: "", // 存储当前客户信息
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  mounted() {
    let arr = [
      // { id: 3, name: "系统配置", isShow: this.$hasShow("系统配置") },
      { id: 5, name: "系统配置", isShow: this.$hasShow("系统配置") },
      // { id: 7, name: "短信配置", isShow: this.$hasShow("短信配置") },
      { id: 8, name: "微信消息配置", isShow: this.$hasShow("微信消息配置") },
      { id: 13, name: "企微配置", isShow: false },
      { id: 14, name: "短信配置", isShow: true },
      { id: 9, name: "oss订单", isShow: this.$hasShow("oss订单") },
      // { id: 10, name: "短信订单", isShow: this.$hasShow("短信订单") },
      { id: 11, name: "消息开关", isShow: false },
      { id: 12, name: "消息记录", isShow: true },
      { id: 1, name: "关于我们", isShow: this.$hasShow("关于我们") },
      { id: 2, name: "联系我们", isShow: this.$hasShow("联系我们") },
      { id: 4, name: "问题反馈", isShow: this.$hasShow("问题反馈") },
      { id: 6, name: "免责声明", isShow: this.$hasShow("免责声明") },
      // { id: 4, name: "系统查找" },
    ];
    this.tableData = arr.filter((item) => {
      return item.isShow === true;
    });
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "DISCLAIMER_CATEGORY":
            this.category_list = item.childs;
            break;
        }
      });
    });
    // this.getSiteSmsAmountData();
    this.getDisclaimerData();
    // this.getMsgSwitchData();
    this.getSecretContent();
    this.getadminUser();
  },
  methods: {
    // 获取系统短信数量
    getSiteSmsAmountData() {
      this.$http.getSiteSmsAmountData().then((res) => {
        if (res.status === 200) {
          this.sms_amount = res.data;
        }
      });
    },
    getMsgSwitchData() {
      this.$http.getMshSwitchData().then((res) => {
        if (res.status === 200) {
          this.message_form = res.data;
        }
      });
    },
    checkData(row) {
      switch (row.id) {
        case 1:
        case 2:
          this.$goPath(`/spa_detail?type=${row.id}`);
          break;
        case 4:
          this.$goPath("/feedback");
          break;
        case 5:
          this.$goPath("/website_update");
          break;
        case 6:
          this.dialogShow = true;
          break;
        case 7:
          this.dialogShowSms = true;
          this.getConfigList();
          break;
        case 8:
          this.dialogShowWX = true;
          this.getWxConfig();
          break;
        case 9:
          this.$goPath("/oss_list");
          break;
        case 10:
          this.$goPath("/sms_list");
          break;
        case 11:
          this.is_message_dialog = true;
          break;
        case 12:
          this.$goPath("/system_msg");
          break;
        case 13:
          this.weiXin = true;
          this.getSecretContent();
          break;
        case 14:
          if(this.roleName.roles[0].name == '站长') {
            this.$goPath("/crm_customer_business_setting?type=crm&title=sms");
          } else {
            this.$http.getAuthCrmShow('config_auth_uid').then(res => {
              if(res.status == 200) {
                if((res.data+'').indexOf(localStorage.getItem('admin_id')) == -1) {
                  this.$message({
                    message: '仅创始人以及指定用户可进入页面',
                    type: 'warning'
                  });
                } else {
                  this.$goPath("/crm_customer_business_setting?type=crm&title=sms");
                }
              }
            })
          }
          break;
        default:
          break;
      }
    },
    getConfigList() {
      this.$http.getSystemConfig().then((res) => {
        if (res.status === 200) {
          this.config_list.push(res.data);
          this.config_obj = res.data;
        }
      });
    },
    getSecretContent() {
      this.$http.getSecret().then((res) => {
        if (res.status === 200) {
          this.contact_secret = res.data.contact_secret;
          this.external_secret = res.data.external_secret;
        }
      });
    },
    getDisclaimerData() {
      this.$http
        .getDisclaimerData(this.disclaimer_form.category)
        .then((res) => {
          if (res.status === 200) {
            this.disclaimer_form = res.data;
            this.disclaimer_form.category = res.data.category + "";
          }
        });
    },
    onChange(e) {
      this.disclaimer_form.category = e;
      this.getDisclaimerData();
    },
    changeConfigCategory(e) {
      this.config_obj = {
        sign_name: e == 1 ? "腾房云" : "",
        captcha_template_code: e == 1 ? "SMS_163052744" : "",
        access_key_id: e == 1 ? "请勿修改" : "",
        access_secret: e == 1 ? "请勿修改" : "",
        config_category: e,
      };
    },
    getWxConfig() {
      this.$http.getWxconfig().then((res) => {
        if (res.status === 200) {
          this.config_obj_wx = res.data;
        }
      });
    },
    onSubmit() {
      this.is_button_loading = true;
      if (this.config_obj.config_category == 1) {
        this.config_obj.access_secret = "请勿修改";
        this.config_obj.access_key_id = "请勿修改";
      }
      this.config_obj.enable = this.config_obj.enable.toString();
      this.$http.editConfig(this.config_obj).then((res) => {
        this.is_button_loading = false;
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.config_obj.enable = parseInt(this.config_obj.enable);
          this.dialogShow = false;
        }
      });
    },
    onSubmitWx() {
      let formRules = {
        enable: "请选择是否开启",
        new_reported_status_template_code: "请填写内容",
        change_reported_status_template_code: "请填写内容",
        deal_status_template_code: "请填写内容",
        settle_brokerage_template_code: "请填写内容",
        brokerage_balance_change_template_code: "请填写内容",
        customer_follow_up_remind_template_code: "请填写内容",
      };
      for (var key in this.form_create_obj_wx) {
        if (this.form_create_obj_wx === "") {
          this.$message({
            message: formRules[key],
            type: "error",
          });
          return;
        }
      }
      this.config_obj_wx.enable = this.config_obj_wx.enable.toString();
      this.is_button_loading = true;
      this.$http.createWxMsg(this.config_obj_wx).then((res) => {
        this.is_button_loading = false;
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.config_obj_wx.enable = parseInt(this.config_obj_wx.enable);
          this.dialogShowWX = false;
        }
      });
    },
    onSubmitSecret() {
      this.is_button_loading = true;
      this.$http
        .changeSecret(this.contact_secret, this.external_secret)
        .then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.weiXin = false;
          }
        });
    },
    onCreate() {
      if (!this.disclaimer_form.content) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
   
      // this.$http.createDisclaimerData(this.disclaimer_form).then((res) => {
      //   this.is_button_loading = false;
      //   if (res.status === 200) {
      //     this.$message({
      //       message: "创建成功",
      //       type: "success",
      //     });
      //     this.is_button_loading = true;
      //   }
      // });
    },
    // 微信/短信通知开关
    onChangeSwitch(e) {
      let form = {
        id: e.id,
        value: e.value,
      };
      this.$http.setMsgSwitchData(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
        }
      });
    },
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if(res.status == 200) {
          this.roleName = res.data;
        }
      })
    }
    // 缩略图
  },
};
</script>

<style scoped lang="scss">
.el-dialog {
  p {
    margin: 10px 0;
  }
}
.top-box {
  margin-bottom: 20px;
  width: fit-content;
  .top-item {
    background: #f8faff;
    cursor: pointer;
    padding: 20px;
    margin-right: 20px;
    text-align: center;
    .top {
      color: #0174ff;
      line-height: 40px;
      span {
        font-size: 24px;
      }
    }
    .font {
      font-size: 30px;
    }
    .bottom {
      color: #768196;
    }
  }
}
</style>
