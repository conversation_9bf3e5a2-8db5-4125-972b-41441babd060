<template>
  <div class="pages">
    <div class="header-title div row">
      <div class="ht-title">我的话术库</div>
      <div style="margin-right: 24px">
        <el-button
          style="margin-left: 10px"
          type="primary"
          size="mini"
          class="el-icon-plus"
          @click="onCreateTalk"
        >
          新增我的话术
        </el-button>
      </div>
    </div>
    <el-row :gutter="20">
      <!-- <el-col :span="6">
        <div class="content-box-crm">
          <div class="title">
            分组
          </div>
        </div>
      </el-col> -->
      <el-col :span="24">
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
              <!-- <el-select
                size="small"
                style="width: 120px; margin-left: 14px"
                v-model="params.type"
                placeholder="请选择"
                @change="handleClick"
              >
                <el-option
                  v-for="item in type_list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select> -->
            </div>
            <div class="t-t-b-right div row">
              <el-input
                size="small"
                placeholder="请输入标题"
                style="width: 256px; margin-left: 12px"
                v-model="params.keywords"
                @change="onChangeKeywords"
              >
                <span slot="append" class="el-icon-search"></span>
              </el-input>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column width="100" prop="id" label="ID"></el-table-column>
            <el-table-column prop="title" label="标题"></el-table-column>
            <el-table-column prop="content" label="内容"></el-table-column>
            <el-table-column
              prop="created_at"
              label="添加时间"
            ></el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }">
              <el-link type="primary" @click="onChangeEdit(row)">编辑</el-link>
              <el-link
                type="primary"
                style="margin-left: 20px"
                @click="onDelete(row)"
                >删除</el-link
              >
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      width="660px"
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
      :before-close="cancel"
    >
      <el-form
        :model="form_info"
        label-position="right"
        label-width="100px"
        ref="languageRef"
        :rules="rules"
      >
        <el-form-item label="标题：" prop="title">
          <el-input
            style="width: 300px"
            v-model="form_info.title"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="文本：" prop="content">
          <el-input
            type="textarea"
            style="width: 300px"
            v-model="form_info.content"
            placeholder="请输入"
            :rows="6"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          @click="onCreateData"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "language",
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        type: 1,
        keywords: "",
      },
      type_list: [
        { id: 1, name: "企业" },
        { id: 2, name: "个人" },
      ],
      dialogCreate: false,
      titleMap: {
        addData: "添加我的话术",
        updateData: "修改我的话术",
      },
      dialogTitle: "",
      form_info: {
        title: "",
        content: "",
      },
      multipleSelection: [],
      is_button_loading: false,
      //表单验证
      rules: {
        title: [{ required: true, message: "请输入标题名称", trigger: "blur" }],
        content: [{ required: true, message: "请输入文本内容", trigger: "blur" }]
      }
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    //关闭新增回调
    cancel() {
      this.dialogCreate = false;
      this.form_info = {
        title: "",
        content: "",
      };
      this.$refs.languageRef.resetFields()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleClick() {
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmMyTalkData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onCreateTalk() {
      this.form_info = {};
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    onCreateData() {
      if (!this.form_info.content || !this.form_info.title) {
        this.$message.error("请检查内容后提交");
        return;
      }
      this.is_button_loading = true;
      if (this.dialogTitle === "addData") {
        this.$http.setCrmMyTalkData(this.form_info).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.editCrmMyTalkData(this.form_info).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    onChangeEdit(row) {
      this.form_info = row;
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    onDelete(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCrmMyTalkData(row.id).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => { });
    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
  },
};
</script>
<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    margin: -24px -24px 24px;
    justify-content: space-between;
    align-items: center;
    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }
}
</style>
