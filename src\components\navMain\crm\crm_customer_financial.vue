<template>
  <!-- 财务报告 -->
  <div>
    <div v-if="auth_finance" class="pages">
      <div class="data-box div row">
        <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/dqq.png?x-oss-process=style/w_1200');
        ">
          <div class="number">{{ statistical_list.income || 0 }}</div>
          <div class="number2">{{ income }}收入</div>
        </div>
        <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrjq.png?x-oss-process=style/w_1200');
        ">
          <div class="number">{{ statistical_list.expend || 0 }}</div>
          <div class="number2">{{ income }}支出</div>
        </div>
        <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrtq.png?x-oss-process=style/w_1200');
        ">
          <div class="number">{{ statistical_list.profit || 0 }}</div>
          <div class="number2">{{ income }}利润</div>
        </div>
        <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrtq.png?x-oss-process=style/w_1200');
        ">
          <div class="number">{{ statistical_list.total_income || 0 }}</div>
          <div class="number2">年度营收总和</div>
        </div>
      </div>
      <el-row>
        <el-col :span="24">
          <div class="content-box-crm" style="margin-bottom: 24px">
            <div class="bottom-border div row" style="padding: 0; margin: 0; border: none">
              <span class="text">成交时间：</span>
              <myLabel :arr="time_list" @onClick="onClickTime"></myLabel><span class="text">自定义：</span>
              <el-date-picker style="width: 250px" size="small" v-model="time_value" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" @change="onChangeVal" value-format="yyyy-MM-dd">
              </el-date-picker>
            </div>
          </div>
          <div class="content-box-crm">
            <div class="table-top-box div row">
              <div class="t-t-b-left div row">
                <myCheck :type="type_list" :is_tabs="params.type" label="name" value="id" @onClick="onClickTabs">
                </myCheck>
              </div>
              <div class="t-t-b-right div row">
                <el-input v-model="params.remarks" placeholder="事由检索" style="width:220px;marginRight:5px"
                  @keyup.enter.native="onchangeDepartment">
                  <el-button slot="append" icon="el-icon-search" @click="onchangeDepartment"></el-button></el-input>

                <div class="div row align-center">
                  <el-popover placement="bottom" width="200px" v-model="show_department_search">
                    <div>
                      <el-cascader placeholder="请选择部门" style="width: 210px; margin-right: 16px"
                        v-model="params.department_id" clearable :show-all-levels="false" :options="department_list"
                        :props="{
                                                  value: 'id',
                                                  label: 'name',
                                                  children: 'subs',
                                                  emitPath: false,
                                                }" @change="onchangeDepartment"></el-cascader>
                    </div>
                    <div style="margin-top: 10px">
                      <!--  v-model="params.admin_id" -->
                      <el-input style="width: 210px" v-model="search_username" placeholder="请选择成员" @focus="showMember">
                        <i slot="append" style="cursor: pointer;" @click="clearMember">X</i>
                        <!-- <el-button
                        slot="append"
                        icon="el-icon-search"
                        @click="onchangeDepartment"
                      ></el-button> -->
                      </el-input>
                    </div>

                    <div class="search_loudong flex-row align-center" slot="reference"
                      style="padding: 0 18px; margin-left: 0; margin-right: 5px">
                      <div class="seach_value flex-row align-center">
                        {{ department_name }}
                        <div class="sanjiao" :class="{ transt: show_department_search }"></div>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <el-button class="el-icon-plus" size="mini" type="primary" @click="onCreateData">添加</el-button>
              </div>
            </div>
            <el-table v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
              highlight-current-row :row-style="$TableRowStyle" @sort-change="sortListChange">
              <!-- <el-table-column
                prop="id"
                width="80"
                label="ID"
              ></el-table-column> -->
              <el-table-column :label="params.type == 1 ? '收入费用' : '支出费用'" prop="money">
              </el-table-column>
              <el-table-column label="事由" prop="remarks"> </el-table-column>
              <el-table-column label="发起人" v-slot="{ row }">
                <span v-if="row.admin_user">
                  {{ row.admin_user.user_name || "--" }}
                </span>
              </el-table-column>
              <el-table-column label="操作人" v-slot="{ row }">
                <span v-if="row.operation_user">
                  {{ row.operation_user.user_name || "--" }}
                </span>
              </el-table-column>
              <el-table-column label="操作时间" prop="add_date" sortable="custom"></el-table-column>
              <!-- <el-table-column label="分成比例" fixed="right" v-slot="{ row }">
                <el-link type="primary" @click="onClickDialog(row)"
                  >详情</el-link
                >
              </el-table-column>   -->
              <el-table-column label="发起/操作" fixed="right" v-slot="{ row }">
                <!-- <el-link type="primary" @click="onClickDialog(row)"
                  >详情</el-link
                > -->
                <el-link style="margin:0 10px" type="primary" @click="onClickEdit(row)">编辑</el-link>
                <el-link type="danger" @click="onDeleterData(row)">删除</el-link>
              </el-table-column>
            </el-table>
            <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
              :total="params.total" :page-size="params.per_page" :current-page="params.page"
              @current-change="onPageChange">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
      <el-dialog :visible.sync="is_dialog_detail" title="详细分成比例">
        <div class="title-lable">
          <span class="l">总收入：</span>
          <span class="r" v-if="detail_list.length > 0">￥{{ detail_list[0].money }}元</span>
        </div>
        <el-table :data="detail_list" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
          :row-style="$TableRowStyle">
          <el-table-column label="审批类型" v-slot="{ row }">
            <span v-if="row.approver">
              {{ row.approver.type == 1 ? "客源审批" : "房源审批" }}
            </span>
          </el-table-column>
          <el-table-column prop="admin.user_name" label="成员名称"></el-table-column>
          <el-table-column v-slot="{ row }" label="分成比例">
            {{ row.radio }}%
          </el-table-column>
        </el-table>
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="detail_params.total" :page-size="detail_params.per_page" :current-page="detail_params.page"
          @current-change="onPageChangeDetail">
        </el-pagination>
      </el-dialog>
      <el-dialog :title="titleMap[dialogTitle]" :visible.sync="is_dialog_content">
        <el-form :model="form_info" label-width="100px">
          <el-form-item label="类型：">
            <el-radio-group v-model="form_info.type">
              <el-radio v-for="item in type_list" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="成员：">
            <el-input placeholder="请选择使用成员" v-model="to_username" @focus="show_member_list = true" style="width: 300px">
              <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close"></i></el-input>
          </el-form-item>
          <el-form-item label="金额：">
            <el-input style="width:300px" v-model="form_info.money" placeholder="请输入金额" type="number" min="0">
            </el-input>
          </el-form-item>
          <el-form-item label="操作时间：">
            <el-date-picker v-model="form_info.add_date" style="width:300px" type="datetime" placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="事由：">
            <el-input style="width:300px" v-model="form_info.remarks" placeholder="请输入" type="textarea">
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="is_dialog_content = false">取 消</el-button>
          <el-button type="primary" @click="onCreateDataForm">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <div v-if="!auth_finance && loadEnd">
      <myEmpty desc="当前用户不可查看"></myEmpty>
    </div>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <memberListSingle v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" ref="memberList">
      </memberListSingle>
    </el-dialog>

    <el-dialog :visible.sync="show_search_member_list" width="660px" title="选择成员">
      <memberListSingle v-if="show_search_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedSearchMember" ref="memberList">
      </memberListSingle>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "./components/my_label";
import myCheck from "./components/my_check";
import myEmpty from "@/components/components/my_empty.vue";
import memberListSingle from "../../navMain/site/components/memberList_single.vue";

export default {
  name: "crm_customer_financial",
  components: {
    myLabel,
    myCheck,
    myEmpty,
    memberListSingle,
  },
  data() {
    return {
      radio: "",
      params: {
        page: 1,
        total: 0,
        per_page: 10,
        type: 1,
        date_type: 0,
      },
      detail_params: {
        page: 1,
        total: 0,
        per_page: 10,
      },
      detail_list: [],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今日" },
        { id: 2, name: "昨日" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      time_value: "",
      tableData: [],
      type_list: [
        { id: 1, name: "收入" },
        { id: 2, name: "支出" },
      ],
      is_table_loading: true,
      is_dialog_detail: false,
      is_dialog_content: false,
      statistical_list: {},
      auth_finance: 0, // 财务是否显示
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_info: {
        type: 1,
        admin_id: "",
        money: "",
        remarks: "",
      },
      admin_params: {
        page: 1,
        per_page: 5,
        type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
      },
      to_username: "",
      show_member_list: false,
      selectedIds: [],
      income: "全部",
      top_params: {
        date_type: 0,
        start_date: "",
        end_date: "",
      },
      department_name: "部门/成员",
      show_department_search: false,
      department_list: [],
      search_username: "",
      show_search_member_list: false,
      loadEnd: false,
      datalist:[],
    };
  },
  mounted() {
    this.getDepartment();
    this.getAdmin();
    this.getCrmDepartmentList();
  },
  methods: {
    getCurrentTime() {
      //获取当前时间并打印
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? "0" + new Date().getMinutes()
          : new Date().getMinutes();
      let ss =
        new Date().getSeconds() < 10
          ? "0" + new Date().getSeconds()
          : new Date().getSeconds();
      return (
        yy +
        "-" +
        (mm + "").padStart(2, 0) +
        "-" +
        (dd + "").padStart(2, 0) +
        " " +
        hh +
        ":" +
        mf +
        ":" +
        ss
      );
    },
    getAdmin() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          if (res.data.roles[0].name === "站长") {
            this.auth_finance = 1;
            this.getCrmCustomerFinancial();
            this.getDataList();
          } else {
            this.getCheckShow(res.data.id);
          }
        }
      });
    },
    onPageChangeadmin(e) {
      this.admin_params.page = e;
      this.getAdminList();
    },
    onchangeDepartment() {
      this.params.page = 1;
      this.getDataList();
      this.show_department_search = false;
    },
    delName() {
      this.form_info.admin_id = "";
      this.to_username = "";
    }, // 获取部门列表
    // async getDepartment() {
    //   let res = await this.$http.getCrmDepartmentList().catch((err) => {
    //     console.log(err);
    //   });
    //   if (res.status == 200) {
    //     this.memberList = res.data;
    //   }
    // },
    // 获取部门成员列表
    async getDepartment() {
      await this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = JSON.parse(JSON.stringify(res.data))
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user
          })
          this.recursionData(this.memberList);
          // 当键值key重复就更新key
          // for (let i = 0; i < this.datalist.length; i++) {
          //   for (let j = i + 1; j < this.datalist.length; j++) {
          //     if (this.datalist[i].id == this.datalist[j].id) {
          //       console.log(1212121212121,this.datalist[i],this.datalist[j]);
          //       this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
          //       console.log( this.datalist[i].id,"44444444444444444444");
          //     }
          //   }
          // }
        }
      })
    },
    // 递归数据处理
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    // 获取部门列表
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    selecetedMember(e) {
      console.log(e);
      if (e.checkedNodes && e.checkedNodes.length) {
        this.to_username = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.form_info.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        // this.form_info.department_id =
        //   e.checkedNodes[e.checkedNodes.length - 1].department[0].id;
          // console.log(this.form_info.department_id);
          console.log( this.form_info.admin_id);
      } else {
        this.to_username = "";
        this.form_info.admin_id = "";
        // this.form_info.department_id = "";
      }
      this.show_member_list = false;
    },
    clearMember() {
      this.search_username = "";
      this.params.admin_id = 0;
      this.params.page = 1;
      this.getDataList();
      this.show_department_search = false;
    },
    selecetedSearchMember(e) {
      console.log(e);
      if (e.checkedNodes && e.checkedNodes.length) {
        this.search_username = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.params.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        this.params.page == 1;
        this.getDataList();
      } else {
        this.params.admin_id = 0;
        this.params.page == 1;
        this.getDataList();
      }
      this.show_search_member_list = false;
    },
    // 获取财务显示
    getCheckShow(id) {
      // getCommonSettingRolesConf
      // this.$http.getAuthShow("finance_auth").then((res) => {
      this.$http.getCommonSettingRolesConf().then((res) => {
        
        if (res.status === 200) {
          let curr = res.data.find(item=>item.key=="finance_auth")
          if (curr&&(curr.value+'').indexOf(id) != -1){
          // if ((res.data+'').indexOf(id) != -1) {
            this.auth_finance = 1;
            this.getCrmCustomerFinancial();
            this.getDataList();
          } else {
            this.is_table_loading = false;
            this.loadEnd = true;
          }
        }
      });
    },
    onChangeVal(e) {
      this.params.start_date = e ? e[0] : "";
      this.params.end_date = e ? e[1] : "";
      this.top_params.start_date = e ? e[0] : "";
      this.top_params.end_date = e ? e[1] : "";
      this.params.page = 1;
      this.is_customer = true;
      this.getDataList();
      this.getCrmCustomerFinancial();
    },
    getCrmCustomerFinancial() {
      if (this.is_customer) {
        this.top_params.date_type = 0;
      } else {
        this.top_params.start_date = null;
        this.top_params.end_date = null;
      }
      this.$http
        .getCrmCustomerFinancial({ params: this.top_params })
        .then((res) => {
          if (res.status === 200) {
            this.loadEnd = true;
            this.statistical_list = res.data;
          }
        });
    },
    getDataList() {
      if (this.is_customer) {
        this.params.date_type = 0;
      } else {
        this.params.start_date = null;
        this.params.end_date = null;
      }
      this.$http
        .getCrmCustomerFinancialList({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        });
    },
    onClickTime(e) {
      this.is_customer = false;
      this.params.date_type = e.id;
      this.top_params.date_type = e.id;
      this.income = e.name;
      this.getDataList();
      this.getCrmCustomerFinancial();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onPageChangeDetail(e) {
      this.detail_params = e;
    },
    onClickTabs(e) {
      this.params.type = e.id;
      this.getDataList();
    },
    onCreateData() {
      this.is_dialog_content = true;
      this.dialogTitle = "addData";
      this.form_info = {
        type: this.params.type,
        money: "",
        admin_id: "",
        id: "",
        remarks: "",
        department_id: "",
        add_date: this.getCurrentTime(),
      };
    },
    onClickEdit(row) {
      this.dialogTitle = "updateData";
      this.to_username = row.admin_user.user_name;
      this.form_info = {
        type: row.type,
        money: row.money,
        admin_id: row.admin_id,
        id: row.id,
        remarks: row.remarks,
        department_id: row.department_id,
        add_date: row.add_date,
      };
      this.is_dialog_content = true;
    },
    getAuditDetail(id) {
      this.$http.getAuditDetail(id).then((res) => {
        if (res.status === 200) {
          this.detail_list.push(res.data);
        }
      });
    },
    onCreateDataForm() {
      if (!this.form_info.money || !this.form_info.admin_id) {
        this.$message.error("请检查内容后提交");
        return;
      }
      // if (!this.form_info.department_id) {
      //   this.$message.error("请检查是否选择成员");
      //   return;
      // }
      if (this.dialogTitle === "addData") {
        this.$http.createFinancialData(this.form_info).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作完成");
            this.params.page = 1;
            this.getDataList();
            this.to_username = ""
            this.is_dialog_content = false;
          }
        });
      } else {
        this.$http.editFinancialData(this.form_info).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作完成");
            this.params.page = 1;
            this.getDataList();
            this.is_dialog_content = false;
          }
        });
      }
    },
    onClickDialog(row) {
      console.log(row);
      // this.is_dialog_detail = true;
      // this.detail_list = [];
      // this.getAuditDetail(row.id);
    },
    showMember() {
      this.show_search_member_list = true;
    },
    onDeleterData(row) {
      this.$confirm("此操作将删除该内容, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleterFinancialData(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.params.page = 1;
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击表格排序触发
    sortListChange(column) {
      console.log(column,"column")
      // 升序
      if(column.order == "ascending") {
        this.params.order_type = 2;
      } else if(column.order == "descending") { // 降序
        this.params.order_type = 1;
      } else if(column.order_type == null) {
        delete this.params.order_type;
      }
      this.getDataList(); // 刷新数据
    }
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}

.bottom-border {
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 14px;
  margin-top: 14px;
  border-bottom: 1px dashed #e2e2e2;

  .text {
    font-size: 14px;
    color: #8a929f;

    .label {
      width: 70px;
      display: inline-block;
      text-align: right;
    }
  }

  .el-radio::v-deep {
    color: #8a929f;
  }
}

.data-box {
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  // margin-right: 20px;
  .data-item {
    display: flex;
    flex-direction: column;
    width: 23%;
    justify-content: center;
    padding-left: 3%;
    box-sizing: border-box;
    height: 140px;
    border-radius: 10px;
    background-size: 100% 100%;
    color: #fff;
    background-repeat: no-repeat;

    .number {
      font-size: 36px;
    }

    .number2 {
      font-size: 18px;
      margin-top: 6px;
    }
  }
}

.title-lable {
  font-size: 18px;
  margin-bottom: 14px;

  .r {
    color: red;
  }
}

.search_loudong {
  background: #f8f8f8;
  height: 41px;
  padding: 0 11px;
  margin-left: 5px;
  font-size: 13px;
  color: #999;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #999;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #999;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #999;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
</style>
