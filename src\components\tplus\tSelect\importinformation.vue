<template>
  <div>
    <div>
      <!-- 头部标题 -->
      <div class="titlepage">
        <div class="classtitle">
          <div class="titleitem" v-for="item in titledata" :key="item.id" :class="{isitem:titleid==item.id}"
            @click="tabititle(item.id)">
            {{item.name}}
          </div>
        </div>
      </div>
      <!-- 上传数据类型 -->
      <div class="importdata">
        <div class="text">请选择数据模板</div>
        <div>
          <div class="template_list">
            <div class="list_item" v-for="item,index in templatedata " :key="index"
              :class="{islist_item:templateid==item.type}" @click="templateswitch(item)">
              <span v-show="templateid==item.type">
                <img src="https://img.tfcs.cn/backup/icons/radioimport.png" alt="">
              </span>
              <div class="outside">
                <img :src="item.icon" alt="">
              </div>
              <div class="outsidetext">
                <div>
                  {{item.name}}
                </div>
                <div class="textwithin">
                  {{item.keywords}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 下载模板&上传文件 -->
      <div class="importset_up" v-if="is_show">
        <el-button size="small" @click="choose_file">选择表格文件</el-button>
        <el-link v-if="templateid==1" type="primary" style="margin-left: 12px" @click="onUploadTem">下载数据模板
          <i class="el-icon-download"></i></el-link>
      </div>
      <!-- 上传的表格 -->
      <div class="importset_up" v-if="tableData.length">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="{ background: '#EBF0F7' }" border>
          <el-table-column prop="name" label="文件名">
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <el-tag type="success" size="default" v-if="scope.row.status == 'success' ">上传成功，等待导入</el-tag>
              <el-tag type="warning" size="default" v-if="scope.row.status == 'warning'">失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80px">
            <template slot-scope="scope">
              <el-link :underline="false" @click="delurl(scope.row)">删除</el-link>
            </template>

          </el-table-column>
        </el-table>
      </div>
      <!-- 注意事项 -->
      <div class="importset_up" v-if="is_show">
        注意事项
      </div>
      <div class="importset_up note" v-if="is_show">
        <div class="labelrow">
          <span class="text">{{ tips }}
            <div v-if="titleid==4&&templateid==1" style="color:rgb(230, 162, 60);">流转客表格录入人姓名和维护人姓名不必填</div>
          </span>
        </div>
      </div>
      <!-- 导入设置 -->
      <div class="importset_up" v-if="is_show">
        可选设置
      </div>
      <div class="importset_up" v-if="is_show">
        <div class="container">
          <!-- 是否覆盖 -->
          <el-select v-if="templateid==1" style="width: 200px; margin-bottom: 10px" v-model="upload_form.is_cover"
            placeholder="请选择是否覆盖数据">
            <el-option label="不覆盖" :value="0"></el-option>
            <el-option label="覆盖" :value="1"></el-option>
          </el-select>
          <!-- 选择分类 -->
          <el-select class="aaaaaaaaa" :class="{isshow:templateid!=1}"
            style="width: 200px; margin-bottom: 10px; margin-left: 15px" v-model="upload_form.type_id" placeholder="请选择分类"
            clearable>
            <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
            </el-option>
          </el-select>
          <!-- 选择客户来源 -->
          <el-cascader :style="{
                        width: '200px',
                      }" style=" margin-bottom: 10px; margin-left: 15px" v-model="source_idvalue" placeholder="请选择客户来源"
            :options="sourceLabel" @change="sourceLabelimport" :props="{
                            label: 'title',
                            value: 'id',
                            children: 'children',
                            checkStrictly: true 
                          }" clearable>
          </el-cascader>
          <!-- </div>
        <div class="flex-row"> -->
          <el-select v-if="titleid==2" :class="{Ashow:titleid==2&&templateid!=1||titleid==1&&templateid!=1}"
            style="width: 200px;" v-model="allocationway" placeholder="分配方式">
            <el-option v-for="list in way_list" :key="list.id" :label="list.user_name" :value="list.id">
            </el-option>
          </el-select>
          <!-- 选择维护人、维护组 -->
          <el-input v-if="allocationway=='1_1'&&titleid==2" :class="{Ashow:titleid==2&&templateid==1}" ref="focusMember"
            placeholder="请选择维护人" v-model="uploadAdmin_id" style="width: 200px; display: block" @focus="focusSelete">
            <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
              class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
          </el-input>
          <el-select v-if="allocationway=='2_1'" :class="{isshow:allocationway=='2_1'&&titleid==2&&templateid!=1}"
            style="margin-left: 15px;width: 106px;" placeholder="请选择分组" v-model="follow_group_id" @change="douyinwei">
            <el-option v-for="list in respectuser_list" :key="list.id" :label="list.user_name" :value="list.id">
            </el-option></el-select>
          <!-- 录入人 -->
          <el-input v-if="titleid!=4" :class="{isshow:titleid==1&&templateid==1||titleid==3&&templateid==1}"
            ref="focusMember1" placeholder="请选择录入人" v-model="uploadAdmin_id1" style="width: 200px; display: block;
        margin-left: 15px" @focus="focusSelete1">
            <i v-show="uploadAdmin_id1 != '' && uploadAdmin_id1 != undefined" @click="delName" slot="suffix"
              class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
          </el-input>
          <!-- 标签 -->
          <el-cascader
            :class="{isshow:titleid==2&&templateid==1||titleid==3&&templateid!=1||titleid==1&&templateid!=1||titleid==4&&templateid==1}"
            style="width:200px; margin-bottom: 10px; margin-left: 15px" v-model="upload_form.label" clearable
            placeholder="请选择标签" :options="label_list" :props="{
                        value: 'id',
                        label: 'name',
                        children: 'label',
                        emitPath: false,
                        multiple: true,
                      }">
          </el-cascader>
        </div>
        <!-- 客户备注线索 -->
        <div class="clueRemark" v-if="templateid==1">
          <el-input type="textarea" :rows="6" placeholder="请输入客户备注" :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="upload_form.remark" style="width:100%;">
          </el-input>
        </div>
      </div>

      <div v-if="(titleid == 1 || titleid == 2) && (website_id==109 || website_id==176)">
          <div class="importset_up">
            <el-tooltip  placement="top" effect="light">
              <div slot="content" style="line-height:1.5">
                <p>去重： 公海/私客/潜客  号码不能重复导入</p>
                <p>不去重：重复号码将导入至流转客中跟进维护</p>
              </div>
              <span>去重规则 <i class="el-icon-warning-outline notice-icon"></i></span>
            </el-tooltip>
          </div>
          <div class="importset_up">
            <el-radio-group v-model="upload_form.is_add_lz">
              <el-radio :label="1">去重</el-radio>
              <el-radio :label="0">不去重</el-radio>
            </el-radio-group>
          </div>
      </div>


      <div class="importset_up" v-if="false">
        <div class="container">
          <!-- 是否覆盖 -->
          <el-select v-if="templateid==1" style="width: 200px; margin-bottom: 10px" v-model="upload_form.is_cover"
            placeholder="请选择是否覆盖数据">
            <el-option label="不覆盖" :value="0"></el-option>
            <el-option label="覆盖" :value="1"></el-option>
          </el-select>
          <!-- 选择分类 -->
          <el-select class="aaaaaaaaa" :class="{isshow:templateid!=1}"
            style="width: 200px; margin-bottom: 10px; margin-left: 15px" v-model="upload_form.type_id" placeholder="请选择分类"
            clearable>
            <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
            </el-option>
          </el-select>
          <!-- 选择客户来源 -->
          <el-cascader :style="{
                        width: '200px',
                      }" style=" margin-bottom: 10px; margin-left: 15px" v-model="source_idvalue" placeholder="请选择客户来源"
            :options="sourceLabel" @change="sourceLabelimport" :props="{
                            label: 'title',
                            value: 'id',
                            children: 'children',
                            checkStrictly: true 
                          }" clearable>
          </el-cascader>
          <!-- </div>
        <div class="flex-row"> -->
          <!-- 选择成员 -->
          <el-input v-if="titleid==2" :class="{Ashow:titleid==2&&templateid!=1}" ref="focusMember" placeholder="请选择维护人"
            v-model="uploadAdmin_id" style="width: 200px; display: block" @focus="focusSelete">
            <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
              class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
          </el-input>
          <!-- 录入人 -->
          <el-input :class="{isshow:titleid==2&&templateid!=1||titleid==1&&templateid==1||titleid==3&&templateid==1}"
            ref="focusMember1" placeholder="请选择录入人" v-model="uploadAdmin_id1" style="width: 200px; display: block;
        margin-left: 15px" @focus="focusSelete1">
            <i v-show="uploadAdmin_id1 != '' && uploadAdmin_id1 != undefined" @click="delName" slot="suffix"
              class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
          </el-input>
          <!-- 标签 -->
          <el-cascader :class="{isshow:titleid==1&&templateid!=1||titleid==3&&templateid!=1}"
            style="width:200px; margin-bottom: 10px; margin-left: 15px" v-model="upload_form.label" clearable
            placeholder="请选择标签" :options="label_list" :props="{
                        value: 'id',
                        label: 'name',
                        children: 'label',
                        emitPath: false,
                        multiple: true,
                      }">
          </el-cascader>
        </div>
        <!-- 客户备注线索 -->
        <div class="clueRemark" v-if="templateid==1">
          <el-input type="textarea" :rows="6" placeholder="请输入客户备注" :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="upload_form.remark" style="width:100%;">
          </el-input>
        </div>
      </div>
    </div>
    <el-upload :limit="1" class="upload-demo" :headers="myHeader" :action="user_avatar"
      :on-success="handleSuccessAvatarTemporary" ref="upload" style="display: none" v-if="is_dialog_upload">
      <el-button class="el-icon-download" size="small">本地上传</el-button>
    </el-upload>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员" :modal="false">
      <multipleTree ref="memberLists" v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list1" width="660px" title="选择成员" :modal="false">
      <multipleTree ref="memberLists" v-if="show_member_list1" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember1" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
  </div>
</template>
<script>
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import config from "@/utils/config";
export default {
  components:{
      multipleTree,
  },
  props:{
      type_list:{
          type: Array,
          default: () => [],
      },
      sourceLabel:{
          type: Array,
          default: () => [],
      },
      label_list:{
          type:Array,
          default:() => []
      },
      memberList:{
          type:Array,
          default:() => []
      },
      typesof:{
        type:Number,
        default:()=>'',
      }
  },
  data() {
      return {
          titledata:[
              {id:1,name:"公海"},
              {id:2,name:"私客"},
              {id:3,name:"潜客"},
              // {id:4,name:"流转客"},
          ],//头部标题
          titleid:"1",
          templatedata:[
              // {ids: 1 ,title: "系统内置" , url:"https://img.tfcs.cn/backup/icons/tfyxitimport.png"},
              // {ids: 2 ,title: "抖音" , url:"https://img.tfcs.cn/backup/icons/douyinimport.png"},
              // {ids: 5 ,title: "视频号" , url:"https://img.tfcs.cn/backup/icons/shiphimport.png"},
              // {ids: 6 ,title: "小红书" , url:"https://img.tfcs.cn/backup/icons/xhsimport.png"},
              // {ids: 3 ,title: "快手" , url:"https://img.tfcs.cn/backup/icons/kuaishouimport.png"},
              // {ids: 4 ,title: "海豚知道" , url:"https://img.tfcs.cn/backup/icons/htzdimport.png"},

          ],//模板列表数据
          templateid:"1",//模板id
          upload_form: {
            type: 1, // 导入类型(1:普通导入(自定义导入),2:抖音导入,3:快手导入,4:海豚导入,5:企微视频号导入)
            follow_id: "", // 维护人id
            create_id: "",//录入人id
            url: "", // 导入的文件地址
            type_id: 0, // 客户类型id
            source_id: "", // 客户来源
            source2_id:'',//客户来源2级
            label: "", // 标签:字符串格式，多个用逗号隔开
            remark: "", // 客户备注线索
            is_cover:0,//是否覆盖(0:否,1:是)
            is_potential:"0",//是否是潜在客(0:否,1:是)
            follow_group_id:0,//分组id
            is_add_lz: 0 //是否加入流转
          },//
          source_idvalue:"",//录入客户来源
          uploadAdmin_id: "",
          uploadAdmin_id1: "",
          //选择成员弹框控制
          show_member_list: false,
          show_member_list1: false,
          selectedIds: [],
          user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_FILE_2}`,
          is_dialog_upload: false,
          tableData:[],//上传的文件的表格,(目前单个上传，不支持批量)
          tips:"",
          is_show:true,
          allocationway:"1_1",//指定维护人或者维护组
          way_list: [
              {id:"1_1",user_name:"指定维护人"},
              {id:"2_1",user_name:"按分组分配"}
          ], 
          follow_group_id:"",//分组id
          respectuser_list:[],//维护分组列表
          website_id:"",//站点id
      }
  },
  created(){
    this.getgettemplatedata()
    this.getassociationsatff()
  },
  computed:{
      myHeader() {
    return {
      // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      Authorization: config.TOKEN,
    };
  },
  },
  mounted(){
    if (this.$route.query.website_id) {
          this.website_id = this.$route.query.website_id
      }
      this.is_dialog_upload = true
      if(this.typesof==4){
        this.titledata.push({id:4,name:"流转客"},)
      }
      if(this.typesof){
        this.titleid = this.typesof
      }
  },
  methods:{
      //获取模板信息与注意事项
      getgettemplatedata(){
        this.$http.gettemplatedata().then(res=>{
          if(res.status==200){
            console.log(res.data);
            this.templatedata = res.data
            this.tips = this.templatedata[0].desc
          }
        })
      },
      //获取关联分组
      getassociationsatff(){
          this.$http.getcluegrouping().then((res)=>{
              if (res.status==200){
                  // 拷贝res.data，将 id 和 title 属性复制到this.user_list中
                  // 将 title 属性重命名为 username
                  this.serverData = JSON.parse(JSON.stringify(res.data))
                  this.respectuser_list = this.serverData.map(obj => {
                    return { id: obj.id, user_name: obj.title }
                  })
                  // this.respectuser_list.push({
                  //     id: "1_1",
                  //     user_name:"创建分组"
                  // })
                  // console.log(this.user_list)
              }
          })
      },
      //头部标题事件
      tabititle(id){
          this.titleid = id
      },
      //模板事件 
      templateswitch(item){
          this.templateid = item.type
          this.upload_form.type = item.type
          this.tips =item.desc
          // if(this.titleid==4&&this.templateid !=1){
          //   this.is_show = false
          //   this.$message({
          //     type:"warning",
          //     message:"流转客暂未开通！"
          //   })
          //   return
          // }
          if(item.is_show==1){
            this.is_show = true
          }else{
            this.is_show = false
            this.$message({
              type:"warning",
              message:"请联系客服开通！"
            })
          }
      },
       //导入客户来源
      sourceLabelimport(e){
        if (e.length) {
          if(e.length>1){
            this.upload_form.source2_id = e[1]
            this.upload_form.source_id = e[0]
          }else{
            this.upload_form.source_id = e[0]
            this.upload_form.source2_id = 0
          }
        }else{
          this.upload_form.source_id =''
          this.upload_form.source2_id = 0
        }
      },
      // 导入成员当获取焦点时触发
      focusSelete() {
        this.$refs.focusMember.blur(); // 失去焦点
        this.show_member_list = true;
      },
      focusSelete1() {
        this.$refs.focusMember1.blur(); // 失去焦点
        this.show_member_list1 = true;
      },
      // 清除当前选择成员
      delName() {
      this.uploadAdmin_id = "";
      this.upload_form.follow_id = 0;
      },
      //选中部门人员
      selecetedMember(e) {
        console.log(e.checkedKeys, "成员列表");
        // this.upload_form.follow_id = e.checkedKeys;
        // console.log(this.upload_form.follow_id );
        // this.upload_form.follow_group_id = ""
        if (e.checkedNodes && e.checkedNodes.length) {
          this.uploadAdmin_id = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.upload_form.follow_id =
          e.checkedKeys; // 将id赋值
            this.upload_form.follow_group_id = ""
        } else {
          this.upload_form.follow_id = "";
        }
        this.show_member_list = false;
      },
      selecetedMember1(e) {
        // console.log(e.checkedNodes, "成员列表");
        // this.upload_form.create_id = e.checkedKeys;
        if (e.checkedNodes && e.checkedNodes.length) {
          this.uploadAdmin_id1 = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.upload_form.create_id =
          e.checkedKeys; // 将id赋值
        } else {
          this.upload_form.create_id = "";
        }
        this.show_member_list1 = false;
      },
      //维护分组
      douyinwei(e){ 
        this.upload_form.follow_group_id = e
        this.upload_form.follow_id = ""
      },
      // 下载最新模板
      onUploadTem() {
        window.open(
          "https://img.tfcs.cn/backup/static/admin/xls/client_template_new.xls?f=" +
          +new Date()
        );
      },
      //选择需要上传导入的表格
      choose_file(){
          if(this.tableData.length){
              this.$message({
              type: 'warning',
              message: '一次只能上传一个表格！'
            });
            return                                                  
          }
          this.is_dialog_upload = true;
          this.$nextTick(() => {
            // 在下一个 DOM 更新周期后访问 $refs
            this.$refs["upload"].$refs["upload-inner"].handleClick()
          });

      },
      handleSuccessAvatarTemporary(response, file) {
          console.log(response);
          console.log(file);
          if(file){
              this.tableData = [
              {name:file.name,status:file.status}
          ]
          }
          // console.log(response.url);
          // // this.handleFileUpload(response.url)
          this.upload_form.url = response.url
      },
      // 获取文件
      handleFileUpload(response) {
        // 阻止发生默认行为
        event.preventDefault();
        let url = response;
        let formData = new FormData();
        formData.append("url", url);
        formData.append("follow_id", this.upload_form.follow_id || 0);
        if(this.titleid==3){
          this.upload_form.is_potential = 1
          formData.append("is_potential", this.upload_form.is_potential);
          formData.append("type", this.upload_form.type);
        }else{
          formData.append("type", this.upload_form.type);
        }
        formData.append("type_id", this.upload_form.type_id||0);
        formData.append("source_id", this.upload_form.source_id||0);
        formData.append("source2_id", this.upload_form.source2_id||0);
        formData.append("create_id", this.upload_form.create_id || 0);
        // if(this.website_id==109){
          formData.append("follow_group_id", this.upload_form.follow_group_id || 0);
        // }
        if(this.upload_form.type==1){
          formData.append("is_cover", this.upload_form.is_cover);
          formData.append("remark", this.upload_form.remark);
        }
        if (Array.isArray(this.upload_form.label)) {
          formData.append("label", this.upload_form.label.join(","));
        } else {
          formData.append("label", this.upload_form.label);
        }

        if(this.titleid == 1 || this.titleid == 2){
          formData.append("is_add_lz", this.upload_form.is_add_lz);
        }
        // this.formData.get("file");
        console.log(formData);
        this.onUpload(formData);
      },
      //上传文件
      onUpload(formData) {
        this.loading = this.$loading({
          lock: true,
          text: "导入中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        console.log(formData);
        // this.$message.success("正在上传...");
        //  修改为传url
        let urlapi = 'uploadCrmCustomer'
        if(this.titleid==4){
          urlapi = 'informationimport'
        }
        this.$http[urlapi](formData).then((res) => {
          if (res.status === 200) {
            this.loading.close();
            this.$confirm("任务正在进行中，是否前往操作记录查看详情", "提示", {
              confirmButtonText: "查看详情",
              cancelButtonText: "取消",
              type: "success",
            })
              .then(() => {
              this.$emit('child-event', '');
              this.tableData = []
                this.is_dialog_upload = false;
                this.is_loading = false;
                let  url = `/crm_customer_all_task_list`;
                if(this.titleid==4){
                   url = `/crm_customer_all_task_list?information=1`;
                }
                this.$goPath(url); // 跳转客户详情
                this.qingkong()
              })
              .catch(() => {
                this.qingkong()
                this.tableData = []
                this.is_dialog_upload = false;
                this.is_loading = false;
              });
          } else {
            this.qingkong()
            this.tableData = []
            this.is_dialog_upload = false;
            this.is_loading = false;
            this.loading.close();
          }
        }).catch(() => {
          console.log(113123123323);
                this.qingkong()
                this.tableData = []
                this.is_dialog_upload = false;
                this.is_loading = false;
                this.loading.close();
              });
      },
      //删除表格
      delurl(){
          this.$confirm('此操作将删除该表格, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
              this.tableData = []
              this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.$refs.upload.clearFiles();
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      // 调用清空
      qingkong(){
        console.log(111111);
        // this.upload_form.type= 1, // 导入类型(1:普通导入(自定义导入),2:抖音导入,3:快手导入,4:海豚导入,5:企微视频号导入)
        this.upload_form.follow_id= "", // 维护人id
        this.upload_form.create_id= "",//录入人id
        this.upload_form.url= "", // 导入的文件地址
        this.upload_form.type_id= "", // 客户类型id
        this.upload_form.source_id= "", // 客户来源
        this.upload_form.source2_id='',//客户来源2级
        this.upload_form.label= "", // 标签:字符串格式，多个用逗号隔开
        this.upload_form.remark= "", // 客户备注线索
        this.upload_form.is_cover=0,//是否覆盖(0:否,1:是)
        this.upload_form.is_potential="0",//是否是潜在客(0:否,1:是)
        this.upload_form.follow_group_id=0,//分组id
        this.upload_form.is_add_lz=0 //是否加入流转
        this.allocationway="1_1"//指定维护人或者维护组
        this.follow_group_id = ""
        this.uploadAdmin_id = ''
        this.uploadAdmin_id1 = ''
        this.source_idvalue = ''
      },
      // 开始导入
      Starting_import(){
        let upload_data = JSON.parse(JSON.stringify(this.upload_form))
        if (Array.isArray(this.upload_form.label)) {
          upload_data.label = this.upload_form.label.join(",")
        } else {
          upload_data.label = this.upload_form.label
        }
        // if(this.website_id==109){
          if(this.titleid==2&&!upload_data.follow_id&&!upload_data.follow_group_id){
          this.$message({
            type:"warning",
            message:"私客请填写维护人/维护组！"
          })
          return
        }

       
        if(!this.upload_form.url){
          this.$message({
            type:"warning",
            message:"请上传表格！"
          })
          return
        }
        this.handleFileUpload(this.upload_form.url)
      },
  }
}
</script>
<style lang="scss" scoped>
.titlepage {
  width: 100%;
  height: 71px;
  // background-color: aqua;
  overflow: hidden;
  border-bottom: 1px solid #F2F3F5;

  .classtitle {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 33px;

    .titleitem {
      margin-right: 40px;
      color: #4E5969;
      position: relative;

      &.isitem {
        color: #165DFF;

        &::after {
          position: absolute;
          left: 38%;
          transform: translateX(-38%);
          content: "";
          height: 4px;
          background: #2d84fb;
          width: 40px;
          display: block;
          margin-top: 14px;
        }
      }
    }

  }
}

.prompt_Line {
  width: 100%;
  height: 36px;
  background-color: #FFF3E8;

  div {
    color: #F77234;
    font-size: 12px;
    overflow: hidden;
    padding-top: 8px;
    padding-left: 10px;
  }
}

.importdata {
  width: 95%;
  // background-color: #F77234;
  margin: 13px auto;
  cursor: pointer;

  .text {
    color: #1D2129;
    margin-left: 11px;
  }

  .template_list {
    width: 100%;
    display: flex;
    margin-top: 15px;
    flex-wrap: wrap;

    .list_item {
      flex: 1;
      min-width: 200px;
      height: 57px;
      border-radius: 11px;
      border: 1px solid #E5E6EB;
      margin: 0px 1px 10px 11px;
      display: flex;

      // justify-content: center;
      // align-items: center;
      .outside {
        margin: 7px 0px 0px 15px;
      }

      .outsidetext {
        margin: 7px 0px 0px 12px;

        .textwithin {
          font-size: 12px;
          color: #86909C;
        }
      }
    }

    .islist_item {
      border: 1px solid #165DFF;
    }

    .islist {
      margin-right: 20px;
    }
  }
}

.importset_up {
  width: 92%;
  // background-color: #F77234;
  margin: 13px auto;
  .notice-icon{
    color: #606266;
  }

  .container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .container>* {
    flex: 1 0 calc(33.33% - 10px);
    margin-bottom: 10px;
    box-sizing: border-box;
    ;
  }

  .container .flex-block{
    flex: 1 0 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 6px 0;
    .label{
      font-size: 14px;
      color: #606266;
      padding-right: 16px;
    }
  }
}

.isshow {
  margin-left: 0px !important;
}

.Ashow {
  margin-left: 15px !important;
}

.note {
  // height: 124px;
  background-color: #F7F8FA;
  border-radius: 8px;
  padding: 9px;

  .labelrow {
    font-size: 14px;
    color: #4E5969;
    white-space: pre-wrap;
    line-height: 25px;
  }

  .iscolor {
    color: #F77234;
  }
}
</style>