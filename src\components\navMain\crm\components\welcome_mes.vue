<template>
  <div class="wel_mes">
    <div class="content_top">
      <div class="top_box flex-row align-center">
        <div class="smile flex-row align-center">
          <img src="@/assets/smile.png" @click="show_emji = !show_emji" alt="" />
          <div class="emji" v-if="show_emji" @mouseleave="show_emji = false">
            <ul class="hd-emoji-list flex-row f-wrap">
              <li v-for="(item, index) in emoj" :key="index" @click="insert(item)">
                <a href="javascript:;" class="font-color">{{ item }}</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="content_con">
      <el-input type="textarea" cols="30" rows="10" ref="textarea" id="textarea" @focus="foucsEle('textarea')"
        v-model="welcome_mes.text.desc">
      </el-input>
      <!-- <textarea  cols="30" rows="10"></textarea> -->
    </div>
    <div class="content_bottom">
      <div class="con_b">
        <div class="img_box flex-row align-center" v-if="welcome_mes.image.media_id">
          <div class="del" @click="remove('image')">
            <i class="el-icon-close"></i>
          </div>
          <span>[图片]</span>
          <span>{{ imgName }}</span>
        </div>
        <div class="link_box flex-row align-center" v-if="welcome_mes.link.url">
          <div class="del" @click="remove('link')">
            <i class="el-icon-close"></i>
          </div>
          <span>[链接]</span>
          <span>{{ linkName }}</span>
        </div>
        <div class="video_box flex-row align-center" v-if="welcome_mes.miniprogram.appid">
          <div class="del" @click="remove('miniprogram')">
            <i class="el-icon-close"></i>
          </div>
          <span>[小程序]</span>
          <span>{{ miniprogramName }}</span>
        </div>
        <div class="file_box flex-row align-center" v-if="welcome_mes.video.media_id">
          <div class="del" @click="remove('video')">
            <i class="el-icon-close"></i>
          </div>
          <span>[视频]</span>
          <span>{{ videoName || "未命名视频" }}</span>
        </div>
        <div class="mini_box flex-row align-center" v-if="welcome_mes.file.media_id">
          <div class="del" @click="remove('file')">
            <i class="el-icon-close"></i>
          </div>
          <span>[文件]</span>
          <span>{{ fileName || "未命名文件" }}</span>
        </div>
      </div>

      <el-popover placement="top" width="150" trigger="click" v-model="show_add">
        <div class="f-list">
          <div class="f-item" v-for="item in list" :key="item.id" @click="onClickStatus(item)">
            {{ item.name }}
          </div>
        </div>
        <el-button class="right lab" slot="reference">添加附件</el-button>
      </el-popover>
    </div>
    <el-dialog :visible.sync="show_image_dia" width="600px" title="选择图片" append-to-body>
      <div class="con_box">
        <div class="left flex-row j-end">
          <!--本地上传 -->
          <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" :on-progress="beforeUpload"
            list-type="picture-card" :show-file-list="false" accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf">
            <el-button plain type="primary">本地上传</el-button>
          </el-upload>
          <!-- <el-button >素材库上传</el-button> -->
        </div>
        <div class="imgLists">
          <div class="img_list flex-row" @scroll="handleScroll">
            <template v-if="
                            current_type == 'image' ||
                            current_type == 'miniprogram' ||
                            current_type == 'link'
                          ">
              <div class="img" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <img :src="item.url" alt="" />
              </div>
            </template>
            <template v-if="current_type == 'video'">
              <div class="files" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <div class="file_img">
                  <video :src="item.url"></video>
                </div>
                <div class="file_name">
                  {{ item.user_name }}
                </div>
              </div>
            </template>
            <template v-if="current_type == 'file'">
              <div class="files" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <div class="file_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
                </div>
                <div class="file_name">
                  {{ item.user_name }}
                </div>
              </div>
            </template>
          </div>
          <div class="footer row align-center">
            <el-button type="text" @click="show_image_dia = false">取消</el-button>
            <el-button type="primary" @click="selectImgOk">确定</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_file_dia" width="600px" title="选择文件" append-to-body>
      <div class="con_box">
        <div class="left flex-row j-end">
          <!--本地上传 -->
          <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" list-type="picture-card"
            :show-file-list="false">
            <el-button plain type="primary">本地上传</el-button>
          </el-upload>
          <!-- <el-button >素材库上传</el-button> -->
        </div>
        <div class="imgLists">
          <div class="img_list flex-row" @scroll="handleScroll">
            <template v-if="
                            current_type == 'image' ||
                            current_type == 'miniprogram' ||
                            current_type == 'link'
                          ">
              <div class="img" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <img :src="item.url" alt="" />
              </div>
            </template>
            <template v-if="current_type == 'video'">
              <div class="files" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <div class="file_img">
                  <video :src="item.url"></video>
                </div>
                <div class="file_name">
                  {{ item.user_name }}
                </div>
              </div>
            </template>
            <template v-if="current_type == 'file'">
              <div class="files" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <div class="file_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
                </div>
                <div class="file_name">
                  {{ item.user_name }}
                </div>
              </div>
            </template>
          </div>
          <div class="footer row align-center">
            <el-button type="text" @click="show_file_dia = false">取消</el-button>
            <el-button type="primary" @click="selectImgOk">确定</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_video_dia" width="600px" title="选择视频" append-to-body>
      <div class="con_box">
        <div class="left flex-row j-end">
          <!--本地上传 -->
          <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" :on-progress="beforeUpload"
            list-type="picture-card" :show-file-list="false" accept=".mp4">
            <el-button plain type="primary">本地上传</el-button>
          </el-upload>
          <!-- <el-button >素材库上传</el-button> -->
        </div>
        <div class="imgLists">
          <div class="img_list flex-row" @scroll="handleScroll">
            <template v-if="
                            current_type == 'image' ||
                            current_type == 'miniprogram' ||
                            current_type == 'link'
                          ">
              <div class="img" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <img :src="item.url" alt="" />
              </div>
            </template>
            <template v-if="current_type == 'video'">
              <div class="files" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <div class="file_img">
                  <video :src="item.url"></video>
                </div>
                <div class="file_name">
                  {{ item.user_name }}
                </div>
              </div>
            </template>
            <template v-if="current_type == 'file'">
              <div class="files" v-for="(item, index) in imgList" :key="index"
                :class="{ active: currentImg.url == item.url }" @click="selectAvatar(item)">
                <div class="file_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
                </div>
                <div class="file_name">
                  {{ item.user_name }}
                </div>
              </div>
            </template>
          </div>
          <div class="footer row align-center">
            <el-button type="text" @click="show_video_dia = false">取消</el-button>
            <el-button type="primary" @click="selectImgOk">确定</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_link_dia" :width="linkWidth" title="链接" append-to-body>
      <div class="con_box">
        <div class="link_type flex-row">
          <div class="flex-1"></div>
          <div class="">
            <el-button type="primary" @click="changLinkType">{{
                          link_type == "customer" ? "文件库选择" : "自定义填写"
                          }}</el-button>
          </div>

          <!-- <el-radio
            v-model="link_type"
            v-for="item in link_type_list"
            :key="item.id"
            :label="item.id"
            border
            @change="changLinkType"
            >{{ item.name }}</el-radio
          > -->
        </div>
        <div class="imgLists" v-if="link_type == 'default'">
          <div class="img_list flex-row" @scroll="handleScroll">
            <div class="files files_other flex-row" v-for="(item, index) in sucaiList" :key="index"
              :class="{ active: currentLink.id == item.id }" @click="selectSucai(item)">
              <div class="files_left">
                <div class="files_title">{{ item.title }}</div>
                <div class="files_desc">{{ item.desc }}</div>
              </div>
              <div class="files_right">
                <div class="file_img">
                  <img :src="item.pic_url" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="imgLists" v-if="link_type == 'customer'">
          <el-form label-width="100px">
            <el-form-item label="跳转路径">
              <div class="form-item-block">
                <el-input v-model="welcome_mes.link.url" style="width: 240px; margin-right: 12px"
                  placeholder="请输入跳转路径"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="链接标题">
              <div class="form-item-block">
                <el-input v-model="welcome_mes.link.title" style="width: 240px; margin-right: 12px" placeholder="请输入链接标题"
                  maxlength="35" type="textarea" rows="2" show-word-limit></el-input>
              </div>
            </el-form-item>
            <el-form-item label="链接描述">
              <div class="form-item-block">
                <el-input v-model="welcome_mes.link.desc" style="width: 240px; margin-right: 12px" placeholder="请输入链接地址"
                  type="textarea" rows="3"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="封面">
              <div class="form-item-block cover_img flex-row align-center j-center"
                :class="{ border: !welcome_mes.link.pic_url }" @click="show_image_dia = true">
                <img v-if="welcome_mes.link.pic_url" :src="welcome_mes.link.pic_url" class="avatar" />
                <i v-else class="el-icon-plus"></i>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_link_dia = false">取消</el-button>
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_miniprogram_dia" :width="linkWidth" title="小程序" append-to-body>
      <div class="con_box">
        <div class="link_type flex-row">
          <div class="flex-1"></div>
          <div class="">
            <el-button type="primary" @click="changMiniType">{{
                          mini_type == "customer" ? "文件库选择" : "自定义填写"
                          }}</el-button>
          </div>
          <!-- <el-radio
            v-model="mini_type"
            v-for="item in link_type_list"
            :key="item.id"
            :label="item.id"
            border
            @change="changMiniType"
            >{{ item.name }}</el-radio
          > -->
        </div>
        <div class="imgLists" v-if="mini_type == 'default'">
          <div class="img_list flex-row" @scroll="handleScroll">
            <div class="files files_other flex-row" v-for="(item, index) in miniList" :key="index"
              :class="{ active: currentMini.id == item.id }" @click="selectSucai(item)">
              <div class="files_left">
                <div class="files_title">{{ item.title }}</div>
                <div class="files_desc">{{ item.url }}</div>
              </div>
              <div class="files_right">
                <div class="file_img">
                  <img :src="item.pic_url" alt="" />
                </div>
              </div>
              <!-- <div class="file_img">
                <img
                  src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png"
                  alt=""
                />
              </div>
              <div class="file_name" v-if="item.title">
                {{ item.title }}
              </div> -->
            </div>
          </div>
        </div>
        <div class="imgLists" v-if="mini_type == 'customer'">
          <el-form label-width="100px">
            <el-form-item label="小程序标题">
              <div class="form-item-block">
                <el-input v-model="welcome_mes.miniprogram.title" style="width: 240px; margin-right: 12px"
                  placeholder="请输入小程序标题" maxlength="20" show-word-limit type="textarea" rows="2"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="小程序appid">
              <div class="form-item-block">
                <el-input v-model="welcome_mes.miniprogram.appid" style="width: 240px; margin-right: 12px"
                  placeholder="请输入小程序id"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="跳转路径">
              <div class="form-item-block">
                <el-input v-model="welcome_mes.miniprogram.url" style="width: 240px; margin-right: 12px"
                  placeholder="请输入跳转路径"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="封面">
              <div class="form-item-block cover_img flex-row align-center j-center"
                :class="{ border: !welcome_mes.miniprogram.media_id }" @click="show_image_dia = true">
                <img v-if="welcome_mes.miniprogram.media_id" :src="miniName" class="avatar" />
                <i v-else class="el-icon-plus"></i>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_miniprogram_dia = false">取消</el-button>
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config";
import { emoj } from "@/assets/js/emoj.js";
import { Loading } from "element-ui";
export default {
  props: ["value"],
  data() {
    return {
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      show_add: false,
      current_type: "",
      list: [
        {
          id: "image",
          name: "图片",
        },
        {
          id: "link",
          name: "链接",
        },
        {
          id: "video",
          name: "视频",
        },
        {
          id: "file",
          name: "文件",
        },
        {
          id: "miniprogram",
          name: "小程序",
        },
      ],
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      show_image_dia: false,
      show_file_dia: false,
      show_video_dia: false,
      show_link_dia: false,
      show_miniprogram_dia: false,
      imgList: [],
      params: {
        page: 1,
        per_page: 14,
        type: 1,
      },
      currentImg: "",
      imgName: "",
      linkName: "",
      videoName: "",
      miniprogramName: "",
      fileName: "",
      currentUrl: "",
      emoj: emoj,
      show_emji: false,
      link_type: 'default',
      sucaiList: [],
      miniList: [],
      link_params: {
        page: 1,
        per_page: 10
      },
      currentLink: {}, //当前选中的素材
      link_type_list: [
        {
          id: 'default',
          name: '素材库选择'
        },
        {
          id: 'customer',
          name: '手动添加'
        }
      ],
      mini_type: "default",
      loadMore: true,
      currentMini: {},
      linkWidth: "600px",
      miniName: ""

    };
  },

  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
  },
  created() {
    for (const key in this.value) {

      this.welcome_mes[key] = Object.assign(
        this.welcome_mes[key],
        JSON.parse(JSON.stringify(this.value[key]))
      );
    }
  },
  methods: {
    onClickStatus(item) {
      this.current_type = item.id;
      console.log(item, 111);
      if (item.id == "video") {
        this.website_img = `/api/common/file/upload/admin?category=${config.CATEGORY_IM_VIDEO}`;
      } else if (item.id == "file") {
        this.website_img = `/api/common/file/upload/admin?category=${config.CATEGORY_IM_FILE_2}`;
      } else {
        this.website_img = `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`;
      }
      this.loadMore = true
      if (item.id == "link" || item.id == "miniprogram") {
        this.linkWidth = "600px"
        this.link_params.page = 1
        this.getLongLink(item.id)
        console.log(this.welcome_mes.miniprogram);
        if (item.id == "miniprogram") {
          // if (this.welcome_mes.miniprogram && this.welcome_mes.miniprogram.extra_id) {
          //   this.mini_type = "default"
          //   this.currentMini = this.welcome_mes.miniprogram
          //   this.miniName = this.welcome_mes.miniprogram.url
          // } else {
          this.mini_type = "customer"
          this.params.page = 1
          this.getImgList()
          this.currentUrl = this.welcome_mes.miniprogram.pic_url
          // }
        }
        if (item.id == "link") {
          // if (this.welcome_mes.link && this.welcome_mes.link.extra_id) {
          //   this.link_type = "default"
          //   this.currentLink = this.welcome_mes.link

          // } else {
          this.params.page = 1
          this.getImgList()
          this.link_type = "customer"

          // }
        }
      } else {
        this.linkWidth = "600px"
        this.params.page = 1;
        this.getImgList();
      }
      this["show_" + item.id + "_dia"] = true;
      this.show_add = false;
    },
    // 获取头像列表
    getImgList() {
      if (this.params.page == 1) {
        this.imgList = [];
      }
      console.log(this.current_type);
      this.loadMore = false;
      switch (this.current_type) {
        case "image":
        case "link":
        case "miniprogram":
          this.params.type = 1;
          break;
        case "video":
          this.params.type = 3;
          break;
        case "file":
          this.params.type = 4;
          break;

        default:
          break;
      }
      this.$http
        .getCrmServiceAvatarList(this.params)
        .then((res) => {
          if (res.status == 200) {
            console.log(2134231231);
            if (this.params.type == 4 || this.params.type == 3) {
              res.data.data.map((item) => {
                item.user_name = item.url.substring(
                  item.url.lastIndexOf("/") + 1
                );
                return item;
              });
            }
            this.imgList = this.imgList.concat(res.data.data);
            console.log(res.data.data.length == this.params.per_page, "====");
            if (res.data.data.length == this.params.per_page) {
              this.loadMore = true;
            } else {
              this.loadMore = false;
            }
          }
        })
        .catch(() => {
          this.loadMore = false;
        });
    },
    beforeUpload() {
      Loading.service({
        text: "正在上传 请稍候。。。",
      });
    },
    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.current_type) {
        case "image":
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          this.imgName = url;
          this.welcome_mes.image.media_id = res.data.media_id;

          break;
        case "link":
          // 链接
          this.welcome_mes.link.pic_url = url;

          // this.welcome_mes.link.media_id = res.data.media_id;
          break;
        case "miniprogram":
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          // 小程序 miniprogram   media_id
          // this.miniCover = url;
          this.miniName = url;
          this.welcome_mes.miniprogram.media_id = res.data.media_id;
          break;
        case "video":
          // 视频
          params.type = 3;
          res = await this.$http.getAvatarId(params).catch(() => {
            this.$message.error("获取素材id失败 请检查素材大小");
            return;
          });

          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case "file":
          // 文件
          params.type = 4;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败");
          });
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
      Loading.service().close();
      if (this.current_type !== "image " && this.show_image_dia) {
        this.show_image_dia = false;
      } else {
        this["show_" + this.current_type + "_dia"] = false;
      }
    },
    selectImgOk() {
      let current = this.imgList.find(
        (item) => item.url == this.currentImg.url
      );
      if (current) {
        switch (this.current_type) {
          case "image":
            // 图片
            this.welcome_mes.image.media_id = current.media_id;
            this.welcome_mes.image.pic_url = current.url;
            this.imgName =
              current.url &&
              current.url.substring(current.url.lastIndexOf("/") + 1);

            break;
          case "link":
            // 链接
            this.welcome_mes.link.pic_url = current.url;
            this.linkName = "@" + this.welcome_mes.link.url;
            // this.welcome_mes.link.pic_url = url;
            // this.welcome_mes.link.media_id = current.media_id;
            this.$set(this.welcome_mes.link, "media_id", current.media_id)
            break;
          case "miniprogram":
            // 小程序 miniprogram   media_id
            this.miniName = current.url;
            this.miniprogramName = "@" + this.welcome_mes.miniprogram.url;
            // this.welcome_mes.miniprogram.pic_url = url;
            this.$set(this.welcome_mes.miniprogram, "media_id", current.media_id)
            // this.welcome_mes.miniprogram.media_id = current.media_id;
            this.currentUrl = current.url;
            break;
          case "video":
            // 视频
            this.videoName =
              current.url &&
              current.url.substring(current.url.lastIndexOf("/") + 1);
            this.$set(this.welcome_mes.video, "media_id", current.media_id)
            // this.welcome_mes.video.media_id = current.media_id;
            break;
          case "file":
            // 文件
            this.fileName =
              current.url &&
              current.url.substring(current.url.lastIndexOf("/") + 1);
            this.$set(this.welcome_mes.file, "media_id", current.media_id)
            // this.welcome_mes.file.media_id = current.media_id;
            break;

          default:
            break;
        }
      }

      if (this.current_type !== "image" && this.show_image_dia) {
        this.show_image_dia = false;
      } else {
        this["show_" + this.current_type + "_dia"] = false;
      }
    },
    handleScroll(e) {
      // console.log(e, this.loadMore);
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        if ((this.current_type == 'link' && this.link_type == "default") || (this.current_type == "miniprogram" && this.mini_type == "default")) {
          this.link_params.page++
          this.getLongLink()
        } else {
          this.params.page++;
          this.getImgList();
        }

      }
    },

    selectAvatar(e) {
      console.log(e);
      this.currentImg = e;
    },
    remove(type) {
      switch (type) {
        case "image":
          this.welcome_mes[type].desc = "";
          this[type + "Name"] = "";
          this.welcome_mes[type].media_id = "";
          break;
        case "link":
          this.welcome_mes[type].media_id = "";
          this[type + "Name"] = "";
          this.welcome_mes[type].pic_url = "";
          this.welcome_mes[type].title = "";
          this.welcome_mes[type].url = "";
          this.welcome_mes[type].desc = "";
          this.welcome_mes[type].extra_id = "";
          // title: "",
          // pic_url: "",
          // desc: "",
          // url: "",
          break;
        case "video":
          this.welcome_mes[type].media_id = "";
          this[type + "Name"] = "";
          break;
        case "file":
          this.welcome_mes[type].media_id = "";
          this[type + "Name"] = "";
          break;
        case "miniprogram":
          this.welcome_mes[type].media_id = "";
          this[type + "Name"] = "";
          this.welcome_mes[type].appid = "";
          this.welcome_mes[type].title = "";
          this.welcome_mes[type].url = "";
          break;

        default:
          break;
      }
    },
    insert(str) {
      this.elem = document.querySelector("#textarea");
      var obj = this.elem;
      console.log(obj);
      if (
        typeof obj.selectionStart === "number" &&
        typeof obj.selectionEnd === "number"
      ) {
        var startPos = obj.selectionStart;
        var endPos = obj.selectionEnd;
        var cursorPos = startPos;
        var tmpStr = obj.value;
        obj.value =
          tmpStr.substring(0, startPos) +
          str +
          tmpStr.substring(endPos, tmpStr.length);
        cursorPos += str.length;
        obj.selectionStart = obj.selectionEnd = cursorPos;
      } else {
        obj.value += str;
      }
      this.welcome_mes.text.desc = obj.value;
      this.show_emji = false;
      // this.params[obj.id] = obj.value
    },
    selectSucai(item) {
      console.log(item);
      // this.currentLink = item
      if (this.current_type == 'link') {
        this.currentLink = item
        // this.currentMini = item
        // this.sucaiList.map(i => {
        //   if (i.id == item) {
        //     this.currentLink = i
        //   }
        //   return i
        // })
        this.welcome_mes.link = {
          type: 3,
          title: this.currentLink.title,
          pic_url: this.currentLink.pic_url,
          desc: this.currentLink.desc,
          url: this.currentLink.url,
          extra_id: this.currentLink.id
        }
      }
      if (this.current_type == 'miniprogram') {
        this.currentMini = item
        // this.miniList.map(i => {
        //   if (i.id == item) {
        //     this.currentMini = i
        //   }
        //   return i
        // })
        this.welcome_mes.miniprogram = {
          type: 4,
          title: this.currentMini.title,
          media_id: this.currentMini.media_id,
          appid: this.currentMini.appid,
          url: this.currentMini.url,
          extra_id: this.currentMini.id
        }
      }
    },
    getLongLink() {

      if (this.current_type == 'link') {
        if (this.link_params.page == 1) {
          this.sucaiList = []
        }
        this.link_params.type = 3
      } else {
        if (this.link_params.page == 1) {
          this.miniList = []
        }
        this.link_params.type = 4
      }
      this.$http.foreverMedias(this.link_params).then(res => {
        if (res.status == 200) {
          if (this.current_type == 'link') {
            this.sucaiList = this.sucaiList.concat(res.data.data)
            if (res.data.data.length < this.link_params.per_page) {
              this.loadMore = false
            } else {
              this.loadMore = true
            }
          } else if (this.current_type == 'miniprogram') {
            this.miniList = this.miniList.concat(res.data.data)
            if (res.data.data.length < this.link_params.per_page) {
              this.loadMore = false
            } else {
              this.loadMore = true
            }
          }

        }
      })
    },
    changLinkType() {
      if (this.link_type == "customer") {
        this.link_type = "default"
      } else {
        this.link_type = "customer"
      }
      if (this.link_type == "customer") {
        this.params.page = 1
        this.getImgList()
        this.welcome_mes.link = {
          type: 3,
          title: '',
          pic_url: '',
          desc: '',
          url: '',
        }
      } else {
        this.link_params.page = 1
        this.getLongLink()
      }
      this.$forceUpdate()
    },
    changMiniType() {
      if (this.mini_type == "customer") {
        this.mini_type = "default"
      } else {
        this.mini_type = "customer"
      }
      if (this.mini_type == "customer") {
        this.params.page = 1
        this.getImgList()
        this.welcome_mes.miniprogram = {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        }
      } else {
        this.link_params.page = 1
        this.getLongLink()
      }
      this.$forceUpdate()
    },
    foucsEle() {
      this.elem = document.querySelector("#textarea");
      // this.elem = this.$refs[elem]
    },
  },
};
</script>

<style lang="scss" scoped>
.wel_mes {
  background: #f9f9f9;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  overflow: hidden;

  .content_top {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border: 1px solid #dcdfe6;
    border-bottom: 0;
    padding: 20px 15px;

    .smile {
      position: relative;

      .emji {
        position: absolute;
        background: #fff;
        border: 1px solid #f9f9f9;

        top: 30px;
        left: 0;
        width: 400px;
        height: 400px;
        overflow-y: auto;
        z-index: 100;

        .hd-emoji-list {
          li {
            line-height: 24px;
            padding: 5px;
          }
        }
      }

      img {
        width: 16px;
        height: 16px;
        object-fit: cover;
      }
    }
  }

  .content_con {
    border: 1px solid #dcdfe6;
    min-height: 200px;
    background: #fff;
    // padding: 22px 15px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;

    ::v-deep textarea {
      border: none;
    }
  }

  .content_bottom {
    min-height: 100px;

    .con_b {
      min-height: 60px;

      .del {
        background: #9a9a9a;
        width: 16px;
        height: 16px;
        color: #fff;
        align-items: center;
        display: flex;
        border-radius: 100%;
        justify-content: center;
        cursor: pointer;
        margin-right: 10px;
      }
    }
  }
}

.f-list {
  .f-item {
    padding: 10px 5px;
    cursor: pointer;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.imgLists {
  min-height: 300px;
}

.link_type {
  margin-bottom: 10px;
}

.img_list {
  flex-wrap: wrap;
  min-height: 300px;
  max-height: 375px;
  margin-bottom: 15px;
  overflow-y: auto;
  scrollbar-width: 0;

  &::-webkit-scrollbar {
    width: 0;
  }

  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;

    &.active {
      border: 5px solid #409eff;

      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    &:nth-child(4n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 115px;
    border: 5px solid #fff;

    &.files_other {
      flex-direction: row;
      padding: 0 15px;
      border-radius: 5px;
      overflow: hidden;
      background: #f8f8f8;

      &.active {
        .files_left {
          .files_title {
            color: #2d84fb;
          }

          .files_desc {
            color: #2d84fb;
          }
        }
      }

      .files_left {
        // background: #fff;
        margin-right: 10px;

        .files_title {
          font-size: 14px;
          color: #333;
          width: 139px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .files_desc {
          font-size: 13px;
          margin-top: 10px;
          color: #999;
          width: 139px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .files_right {
        background: #fff;

        .file_img {
          width: 80px;
          height: 80px;
          overflow: hidden;
          border-radius: 5px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    &.active {
      border: 5px solid #409eff;
    }

    &:nth-child(3n) {
      margin-right: 0;
    }

    .file_img {
      width: 115px;
      height: 115px;
      text-align: center;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 5px;
    }
  }
}

.con_box {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
    margin-bottom: 10px;
  }
}

.cover_img {
  width: 148px;
  height: 148px;
  overflow: hidden;

  &.border {
    border: 1px solid #e8f8f8;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
