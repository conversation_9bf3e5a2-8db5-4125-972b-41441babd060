<template>
<div>
<el-dialog title="复制链接" :visible.sync="show" width="420px">
    <div class="container" v-loading="loading">
        <div class="header">
            <div class="search-bar">
                <el-input  size="medium"
                    placeholder="搜索分享人"
                    prefix-icon="el-icon-search" clearable
                    v-model="keyword">
                </el-input>
            </div>
            <el-row class="row">
                <el-col :span="16" class="th">分享人</el-col>
                <el-col :span="8" class="th op">操作</el-col>
            </el-row>
        </div>
        <div class="body">
            <el-row class="row" v-for="admin in curAdminList" :key="admin.values">
                <el-col :span="16" class="td">{{ admin.name }}</el-col>
                <el-col :span="8" class="td op">
                    <el-popover  placement="right" width="220" trigger="click" @show="getQrcode(admin)">
                        <div v-loading="admin.__getQrcoding" style="width: 200px;height:200px;margin: 10px auto">
                            <img :src="admin.__qrcode" width="200" v-if="admin.__qrcode">
                        </div>
                        <el-link slot="reference" type="primary">二维码</el-link>
                    </el-popover>
                    <el-link type="primary" @click="handleCopy(admin)" :disabled="admin.__copying">复制</el-link>
                </el-col>
            </el-row>
        </div>
    </div>
</el-dialog>
</div>
</template>

<script>
export default {
    data(){
        return {
            show: false,        //dialog是否显示
            loading: false,
            successFn: null, 
            copying: false,
            keyword: '',
            mapPluginId: 0,
            adminList: [],
        }
    },
    computed: {
        curAdminList(){
            const keyword = this.keyword.trim();
            if(keyword === ''){
                return this.adminList;    
            }
            return this.adminList.filter(e=>e.name.includes(keyword));
        }
    },
    methods: {
        open(mapPluginId){
            if(this.mapPluginId != mapPluginId){
                this.mapPluginId = mapPluginId;
                this.getAdminList();
            }
            this.show = true;
            return this
        },
        //获取成员
        async getAdminList(){
            let list = this.adminList;
            if(!list.length){
                this.loading = true;
                const res = await this.$http.getColleagueDetailsList().catch(()=>{});
                this.loading = false;
                if(res.status == 200){
                    list = res.data || [];
                }
            }
            this.adminList = list.map(e => {
                e.__copying = false;
                e.__share_id = '';
                e.__getQrcoding = false;
                e.__qrcode = '';
                return e;
            })
        },
        //复制链接
        async handleCopy(admin){
            let share_id = admin.__share_id;
            if(!share_id){
                const admin_id = admin.values;
                admin.__copying = true;
                const res = await this.$http.getShareUserId(admin_id).catch(()=>{});
                admin.__copying = false;
                if(res && res.status == 200){
                    share_id = admin.__share_id = res.data.share_id || 0;
                }
            }
            share_id && this.$onCopyValue('/collarMap/index?scene='+encodeURIComponent('id='+this.mapPluginId+'_'+share_id));
        },
        //小程序码
        async getQrcode(admin){
            if(admin.__getQrcoding){
                return;
            }
            if(!admin.__qrcode){
                const admin_id = admin.values;
                admin.__getQrcoding = true;
                const res = await this.$http.getMapPluginQRcode(this.mapPluginId, { env_version: 'release', admin_id}).catch(() => {})
                admin.__getQrcoding = false;
                if(res.status == 200){
                    admin.__qrcode = res.data;
                }
            }
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        }
    }  
}
</script>
<style lang="scss" scoped>
.container{
    .search-bar{
        margin: 0 0 12px;
        padding-right: 100px;
    }
    .row{
        .td{
            padding: 8px 10px;
            
        }
        .th{
            background: #f6f6f6;
            font-weight: 600;
            padding: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
    }
    .body{
        height: 300px;
        overflow: auto;
        .row{
            border-bottom: 1px dashed #e9e9e9;
        }
    }
    .el-link{
        margin-right: 10px;
    }
}
</style>