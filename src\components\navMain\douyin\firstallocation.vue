<template>
    <div>
        <el-dialog
          :title="user_name"
          :visible.sync="firstdialogVisible"
          width="600px"
          :before-close="handleClose">
          <div class="sevenstyle">
            首次分配数据统计
          </div>
            <el-table
              :data="tableData"
              style="width: 100%"
              border
              v-loading="tableloading"
              :header-cell-style="{ background: '#EBF0F7' }">
              <el-table-column
                label="成员"
                v-slot="{row}">
                <span>{{row.admin_user}}</span> <span style="color:red;" v-if="row.is_del" >({{row.is_del==1?"已离职":''}})</span>
              </el-table-column>
                <el-table-column
                  label="分配量"
                  v-slot="{row}">
                  {{row.crm_assign_num}}
                </el-table-column>
            </el-table>
            <div class="block">
              <el-pagination
                layout="prev, pager, next"
                @current-change="handleCurrentChange"
                :current-page="datadetails.page"
                :total="datadetails.total">
              </el-pagination>
            </div>

          <span slot="footer" class="dialog-footer">
            <el-button @click="firstdialogVisible = false">取 消</el-button>
            <!-- <el-button type="primary" @click="dialogVisible = false">关闭</el-button> -->
          </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            firstdialogVisible:false,
            tableloading:false,
            tableData:[],
            datadetails:{},
            user_name:""
        }
    },
    methods:{
        open(params,name){
            this.datadetails = params 
            this.user_name = name
            this.gettabledata()
        },
        gettabledata(){
            this.tableloading = true
            this.$http.getstatisticaldetails(this.datadetails).then(res=>{
              if(res.status==200){
                // console.log(res.data);
                this.datadetails.total = res.data.total
                this.datadetails.page = res.data.current_page
                this.tableData = res.data.data
                this.firstdialogVisible = true
                this.tableloading = false
              }
            })
        },
        handleClose(){
            this.firstdialogVisible = false
        },
        handleCurrentChange(val){
          this.datadetails.page = val
          this.gettabledata()
        },
    },
}
</script>
<style lang="scss" scoped>
.sevenstyle{
  text-align: center;
  line-height: 24px;
  font-size: 18px;
  color: #303133;
  font-weight: bold;
  margin-bottom: 15px;
}
.block{
  text-align:right;
}
</style>