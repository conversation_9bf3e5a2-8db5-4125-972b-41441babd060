<template>
  <el-container>
    <el-header>
      <el-button
        @click="onCreateNewRegion"
        v-if="$hasShow('添加区域')"
        icon="el-icon-plus"
        type="primary"
      >
        添加{{ website_info.city_type == 1 ? "区域" : "城市" }}
      </el-button>
    </el-header>
    <el-table
      :data="region_list"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :row-class-name="tableRowClassName"
    >
      <el-table-column
        prop="id"
        :label="website_info.city_type == 1 ? '区域ID' : '城市ID'"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        :label="website_info.city_type == 1 ? '区域名称' : '城市名称'"
      >
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间"> </el-table-column>
      <el-table-column label="操作" fixed="right" width="300">
        <template slot-scope="scope">
          <el-button
            v-if="$hasShow('添加下行区域') && scope.row.pid == 0"
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="checkData(scope.row)"
            >添加下行区域</el-button
          >
          <el-button type="primary" size="mini" @click="onEditData(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            @click="deleteData(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_info" label-position="right" label-width="100px">
        <el-form-item
          :label="website_info.city_type == 1 ? '区域名称：' : '城市名称：'"
        >
          <el-input
            :placeholder="
              `请输入${website_info.city_type == 1 ? '区域' : '城市'}名称`
            "
            style="width:300px"
            v-model="form_info.name"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="website_info.city_type == 1 ? '区域拼音：' : '城市拼音：'"
        >
          <el-input
            :placeholder="
              `请输入${website_info.city_type == 1 ? '区域' : '城市'}拼音全拼`
            "
            style="width:300px"
            v-model="form_info.pinyin"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="website_info.city_type == 1 ? '区域排序：' : '城市排序：'"
        >
          <el-input
            v-model="form_info.sort"
            :placeholder="
              `请输入${website_info.city_type == 1 ? '区域' : '城市'}排序`
            "
            type="number"
            min="0"
            step="1"
            style="width:300px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            @click="onCreateData"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "city_setup",
  data() {
    return {
      region_list: [],
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      dialogCreate: false,
      form_info: {
        name: "",
        sort: 0,
        pid: 0,
        pinyin: "",
      },
      is_button_loading: false,
    };
  },
  computed: {
    ...mapState(["website_info"]),
  },
  mounted() {
    this.getRegionData();
  },
  methods: {
    tableRowClassName({ row }) {
      if (!row.pid) {
        return "success-row";
      }
    },
    onCreateNewRegion() {
      this.dialogCreate = true;
      this.dialogTitle = "addData";
      this.form_info = {
        pinyin: "",
        name: "",
        sort: 0,
        pid: 0,
      };
    },
    onEditData(row) {
      this.dialogCreate = true;
      this.dialogTitle = "updateData";
      this.form_info = {
        name: row.name,
        pid: row.pid,
        sort: row.sort,
        id: row.id,
        pinyin: row.pinyin,
      };
    },
    // 添加下级区域
    checkData(row) {
      this.dialogCreate = true;
      this.dialogTitle = "addData";
      this.form_info = {
        pinyin: "",
        name: "",
        sort: 0,
        pid: row.id,
      };
    },
    getRegionData() {
      this.$http.getRegionList().then((res) => {
        if (res.status === 200) {
          let arr = res.data;
          this.region_list = this.getTree(arr, 0);
        }
      });
    },
    // 转成树状结构
    getTree(data, pid) {
      let result = [];
      let temp;
      for (let i = 0; i < data.length; i++) {
        if (data[i].pid == pid) {
          temp = this.getTree(data, data[i].id);
          if (temp.length > 0) {
            data[i].children = temp;
          }
          result.push(data[i]);
        }
      }
      return result;
    },
    deleteData(row) {
      if (row.children) {
        this.$message.error("请检查是否存在下级区域");
        return;
      }
      this.$confirm("此操作将删除该区域，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteRegion(row.id).then((res) => {
            if (res.status === 200) {
              this.$message.success("删除成功");
              this.getRegionData();
            }
          });
        })
        .catch(() => {
          console.log("取消");
        });
    },
    onCreateData() {
      this.is_button_loading = true;
      if (this.dialogTitle === "addData") {
        this.$http.createRegion(this.form_info).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getRegionData();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.updateRegion(this.form_info).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getRegionData();
            this.dialogCreate = false;
          }
        });
      }
    },
  },
};
</script>

<style>
.el-table .success-row {
  background: #f0f9eb;
}
</style>
