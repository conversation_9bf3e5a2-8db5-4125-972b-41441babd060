<template>
  <el-container>
    <el-main>
      <myTable
        v-loading="is_table_loading"
        :table-list="tableData"
        :header="table_header"
      ></myTable>
    </el-main>
  </el-container>
</template>

<script>
import myTable from "@/components/components/my_table";
export default {
  name: "advertising_list",
  components: {
    myTable,
  },
  data() {
    return {
      tableData: [],
      table_header: [
        { prop: "id", label: "调用ID", width: "100" },
        { prop: "name", label: "名称" },
        {
          label: "是否多个",
          render: (h, data) => {
            return <p>{data.row.multi === 1 ? "是" : "否"}</p>;
          },
        },
        {
          label: "操作",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("管理广告") ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.checkData(data.row);
                    }}
                  >
                    管理广告
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getBannerList();
  },
  methods: {
    getBannerList() {
      this.$http.getAdvTypeList().then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data;
        }
      });
    },
    checkData(row) {
      this.$goPath(`/adv_management?id=${row.id}`);
    },
  },
};
</script>

<style></style>
