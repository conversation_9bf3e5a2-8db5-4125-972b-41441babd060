<template>
  <div>
    <!-- <el-dialog :visible.sync="show" width="30%" title="添加项目动态"> -->
        <div>
          <div class="follow-input" >
            <div id="MyContent" class="paperview-input-text" contenteditable="true" placeholder="请输入项目动态（企业内公开）"
              ref="gain"></div>
              <el-button @click="confirm" style="margin-left: auto" :loading="is_loading" type="primary"
                  size="mini">提交</el-button>
          </div>
          <!-- <div class="whethertop">
              <div class="whethertoptext">是否置顶:</div>
              <div>
                <el-radio v-model="trendsdata.is_top" label="0">否</el-radio>
                <el-radio v-model="trendsdata.is_top" label="1">是</el-radio>
              </div>
          </div> -->
          
             <!-- 同事按钮 -->
             <div class="flex-row" style="margin-bottom: 15px;">
              <tMemberDropdown trigger="click" filterable @command="MemberClick">
                <div class="FriendsPiece">@同事</div>
              </tMemberDropdown>
              <div>
              <div class="addPicture addPicture-btn" style="margin-left: 10px;">
                <el-upload class="uploader-create" :disabled="disabled_picture" :headers="myHeader"
                  :action="picture_upurl" :on-success="(e) => UploadParamsSuccess(e)" accept=".jpg,.png"
                  :show-file-list="false" :multiple="true">
                  <span @click="addPictures($event)"><i class="el-icon-plus"></i>图片</span>
                </el-upload>
              </div>
              </div>
            </div>
            <div class="flex-row">
                 <div class="picture_list_box" v-for="(item, index) in imgList" :key="index">
                <img v-if="item.url" :src="item.url" class="photo-item-img" />
                <div class="delete-picture" @click="deletePicture(index)">
                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
                <span class="uploader-actions" v-if="item.url">
                  <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                    <i class="el-icon-view"></i>
                  </span>
                </span>
              </div>
              </div>
      </div>
    <!-- 查看已上传图片模态框 -->
    <div class="mask1" v-if="show_dialog_pictures" @click="show_dialog_pictures = false">
      <div class="preview_img" @click.prevent.stop="() => { }">
        <img id="preImg" :src="dialog_pictures_src" alt="" />
      </div>
    </div>
        <!-- <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span> -->
    <!-- </el-dialog> -->
  </div>
</template>

<script>
import config from "@/utils/config.js";
import tMemberDropdown from "@/components/tplus/tDropdown/tMemberDropdown.vue";
export default {
    name: 'Addprojectupdates',
    components: {
        tMemberDropdown,
    },
    props: {
        // 控制显示隐藏模态框
        params: {
            type: Object,
            default: ()=> {}
        },
        form_appoint:{
          type: Object,
            default: ()=> {}
        }
      },
    data(){
        return {
            show: false,        //dialog是否显示
            cancleFn: null,     //取消回调
            successFn: null,    //成功回调
            // project_id: "",         //表单参数
            trendsdata:{
                is_top:"0",
                notice_id:"",
            },//添加项目动态数据
            disabled_picture: false, // 是否禁用上传图片
            upload_headers: {
              Authorization: config.TOKEN,
            },
            picture_upurl: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
            imgList: [],// 上传图片列表
            colleagueID: [], // 存储同事id
            show_dialog_pictures:false,//查看上传的照片
            is_loading: false, // loading加载动画
        }
    },
    computed: {
        // 获取请求头
        myHeader() {
          return {
            // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
            Authorization: config.TOKEN,
          };
        },
    },
    methods: {
        //打开弹层
        // open(project_id){
        //     this.project_id = project_id;
        //     this.show = true;
        //     return this;
        // },
        MemberClick(item) {
          // console.log(item);
          if (this.colleagueID.includes(item.values)) {
            this.$message({
              message: '请勿重复@同事',
              type: 'warning'
            });
            return
          }
          this.colleagueID.push(item.values);
          // 获取输入框的dom
          let sel = this.$refs.gain;
          // 获取点击同事名
          let tpl = `<a 
                        href="javascript:void(0)"
                        class="myTS"
                        name='${item.values}'
                        style='color: #0088ff; text-decoration: none;'
                        contenteditable= 'false'
                      >
                        @${item.name}
                      </a> &nbsp;`
          sel.innerHTML += tpl;
          let range = window.getSelection()
          range.selectAllChildren(sel);
          range.collapseToEnd();
        },
        // 点击上传图片
        addPictures() {
          // console.log(this.imgList.length );
          // 判断图片列表的个数
          if (this.imgList.length == 9) {
            this.disabled_picture = true; // 禁用上传
            this.$message({
              message: '最多只能上传9张图片',
              type: 'warning'
            });
          } else {
            this.disabled_picture = false; // 启用上传
          }
        },
        // 上传图片成功
        UploadParamsSuccess(e) {
          console.log(e, "上传成功");
          this.imgList.unshift(e);
        },
        // 查看已上传的图片
        handlePictureCardPreview(item) {
          console.log(item,'item');
          this.show_dialog_pictures = true;
          if (item.url) {
            this.dialog_pictures_src = item.url;
          } else {
            this.dialog_pictures_src = item;
          }
        },
        // 删除已上传的图片
        deletePicture(index) {
          this.imgList.splice(index, 1);
        },
        //取消
        cancle(){
            this.show = false;
            this.cancleFn && this.cancleFn();
        },
        //确定
        confirm(){
            let params = {}
            params.project_id = this.params.id
            params.content = this.$refs.gain.innerText // 赋值跟进内容
            params.is_top = this.trendsdata.is_top
            if(this.form_appoint.project_id){
              params.project_id = this.form_appoint.project_id
            }
            if(this.colleagueID.length){
              params.notice_id = this.colleagueID.join(",")  
            }
            let image = []
            if(this.imgList.length){
              this.imgList.map(item=>{
                image.push(item.url)
              })
              params.image = image.join(",")
            }
            if(!params.content){
                this.$message.error("最少输入不能小于五个文字");
                return;
            }
            console.log(params);
            this.$http.saveprojectupdates(params).then((res)=>{
                if(res.status==200){
                    this.$message.success("添加成功！")
                }
                this.colleagueID = []
                this.imgList = []
                this.$refs.gain.innerText = ""
                this.trendsdata.is_top = "0"
                this.$emit('parentMethod',params.project_id);
                this.show = false;
            })
            
            // this.successFn && this.successFn();
        }
    }
}
</script>
<style lang="scss" scoped>
   .follow-input {
  width: 90%;
  padding: 12px 12px;
  border: 1px solid rgba(221, 225, 233, 1);
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
} 
.whethertop{
  margin-top:20px;
  display: flex;
  margin-bottom: 20px;
  .whethertoptext{
    color:#606266;
    margin-right:20px;
    font-size: 14px;
  }
}
.paperview-input-text {
  width: 100%;
  height: 100%;
  outline: none;
  cursor: text;
  max-width: 390px;
}
.paperview-input-text:empty::before {
  color: lightgrey;
  content: attr(placeholder);
}
.FriendsPiece,
    .addPicture {
      max-width: 45px;
      background: #f4f4f5;
      font-size: 14px;
      border: 1px solid #eaeaec;
      border-radius: 5px;
      color: #7a7c7f;
      padding: 3px 8px;
      margin-bottom: 32px;
      cursor: pointer;
    }

    .FriendsPiece,
    .addPicture.addPicture-btn {
      margin-bottom: 0;
    }
    .addPicture {
      .uploader-create {
        .el-upload {
          position: relative;
        
          .uploader-actions {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            cursor: default;
            text-align: center;
            color: #fff;
            opacity: 0;
            font-size: 20px;
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s;
        
            .uploader-actions-item {
              margin: 0 10px;
              font-size: 14px;
              cursor: pointer;
            
              & i {
                color: #fff;
              }
            }
          }
          .uploader-actions:hover {
            opacity: 1;
          }
        }
      }
}
.picture_list_box {
  width: 50px;
  height: 50px;
  border: 1px solid #eaeaec;
  border-radius: 4px;
  background-color: #e2e2e2;
  margin-left: 12px;
  position: relative;

  .photo-item-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .delete-picture {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    z-index: 2;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .uploader-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 4px;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;

    .uploader-actions-item {
      font-size: 20px;
      cursor: pointer;

      & i {
        color: #fff;
      }
    }
  }

  .uploader-actions:hover {
    opacity: 1;
  }
}
.mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 0px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
    //   object-fit: cover;
    }

    .img {
      max-width: 800px;
      height: 600px;
      overflow-y: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
} 
</style>