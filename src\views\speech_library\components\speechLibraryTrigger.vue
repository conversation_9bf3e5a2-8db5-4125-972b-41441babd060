<template>
    <div>
        <el-link type="primary" :underline="false" @click="openSpeechLibrary" v-if="auths.speechLibrary">话术库</el-link>
        <speechLibrary ref="speechLibrary" v-if="dialogs.speechLibrary" class="speech-library-drawer-wrapper" append-to-body
            custom-class="speech-library-drawer" :wrapperClosable="false" show-close @closed="handleSpeechLibraryClosed"></speechLibrary>
    </div>
</template>

<script>
import speechLibrary from "@/views/crm/share_follower/detailedinformation";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        enable: {
            type: Boolean,
            default: false
        },
    },
    components:{
        speechLibrary
    },
    data(){
        return {
            isInited: false,
            isAutoOpenSpeechLibrary: false,     //是否自动打开话术库
            dialogs: {
                speechLibrary: false
            },
            auths: {
                speechLibrary: false
            }
        }
    },
    watch: {
        visible(val){
            if(val){
                if(!this.isInited){
                    this.getSpeechLibraryConfig();
                }else{
                    this.isAutoOpenSpeechLibrary && this.openSpeechLibrary();
                }
            }else{
                this.$refs.speechLibrary && this.$refs.speechLibrary.cancle()
            }
        },
        'auths.speechLibrary'(val){
            this.$emit('update:enable', val)
        }
    },
    created(){
        
    },
    methods:{
        //获取话术库配置
        async getSpeechLibraryConfig(){
            const res = await this.$http.getSpeechLibraryConfig().catch(e=>{});
            if(res && res.status == 200){
                this.isInited = true;
                const conf = res.data || {};
                //是否显示话术库
                this.auths.speechLibrary = conf.show_words_package_by_phone == 1;
                //自动打开话术库
                this.isAutoOpenSpeechLibrary = this.auths.speechLibrary && conf.show_words_package_type == 2
                this.isAutoOpenSpeechLibrary && this.openSpeechLibrary();
            }
        },
        //打开话术库
        async openSpeechLibrary(){
            if(this.dialogs.speechLibrary){
                return;
            }
            this.dialogs.speechLibrary = true
            await this.$nextTick()
            this.$refs.speechLibrary.open()
        },
        handleSpeechLibraryClosed(){
            this.dialogs.speechLibrary = false;
        }
    },
};
</script>
<style lang="scss">
.el-drawer__wrapper.speech-library-drawer-wrapper{
    left: 70%;
    overflow: visible;
    .el-drawer.speech-library-drawer{
        width: 100%!important;
    }
}
</style>