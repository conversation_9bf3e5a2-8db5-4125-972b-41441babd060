<template>
  <el-container>
    <el-header class="div row" style="justify-content: space-between;">
      <div class="div row">
        <div class="title">结算列表</div>
        <div class="title_number">
          <div>
            当前页面共(
            <i>{{ tableData.length }}</i>
            )条数据
          </div>
        </div>
      </div>
      <div class="div row ">
        <!-- <el-dropdown @command="handleCommand" style="width:200px">
          <span class="el-dropdown-link">
            选择搜索方式<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dropdown_search"
              :key="item.value"
              :command="item.value"
              >{{ item.name }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown> -->
        <el-select
          placeholder="请选择结佣状态"
          v-model="params.disburse_brokerage_status"
          @change="onClickStatus"
          clearable
        >
          <el-option
            v-for="item in sale_list"
            :key="item.value"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-select
          placeholder="请选择会员服务费收款状态"
          v-model="params.earning_msc_status"
          clearable
          @change="onClickStatus"
        >
          <el-option
            v-for="item in sale_list"
            :key="item.value"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-select
          placeholder="请选择佣金收款状态"
          v-model="params.earning_brokerage_status"
          clearable
          @change="onClickStatus"
        >
          <el-option
            v-for="item in sale_list"
            :key="item.value"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-input
          @change="onChange"
          v-model="input"
          placeholder="请输入手机号"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <!-- <div class="div row">
				<el-button
					v-for="item in sale_list"
					:key="item.value"
					type="primary"
					style="border-radius: 4px; margin:5px"
					@click="onClickStatus(item)"
				>
					{{ item.desc }}
				</el-button>
			</div> -->
    <div class="browse-table">
      <div class="browse div row">
        <div class="browse-box div row">
          <div
            class="browse-item"
            v-for="(item, index) in time_array"
            :key="index"
            :class="{ browse_active: item.value === list_params.date_str }"
            @click="onClickBrowse(index, item.id)"
          >
            {{ item.desc }}
          </div>
        </div>
      </div>
    </div>
    <div class="block-time div row" v-if="isCustomize">
      <el-date-picker
        v-model="list_params.start"
        type="date"
        placeholder="请选择开始日期"
        value-format="yyyy-MM-dd"
      >
        >
      </el-date-picker>
      <el-date-picker
        v-model="list_params.end"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="请选择结束日期"
      >
        >
      </el-date-picker>
      <el-button type="primary" @click="clickTime">查询</el-button>
    </div>
    <el-row :gutter="20" class="el-row-tab">
      <el-col :span="8">
        <div class="grid-content bg-purple div row">
          <div class="left">
            <p>已结算金额</p>
            <p class="desc">已结算的总金额（元）</p>
          </div>
          <div class="right">
            {{ withdrawal_info.disburse_brokerage_amount }}
          </div>
        </div>
      </el-col>
    </el-row>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <!-- 分页 -->
    <div class="pagination-box">
      <myPagination
        :total="params.total"
        :currentPage="params.page"
        :pagesize="params.per_page"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </div>

    <el-dialog
      title="报备详情"
      :visible.sync="dialogVisibleReport"
      width="60%"
      custom-class="dia-detail"
      @close="reportClose"
    >
      <div class="nav div row">
        <div
          class="nav-item"
          v-for="(nav, index) in click_navs"
          :key="nav.type"
          :class="{ active: nav.type == data_type }"
          @click="onClickNav(index)"
        >
          {{ nav.name }}
        </div>
        <!-- <reportTxt :type="1"></reportTxt> -->
      </div>
      <div class="detail" v-if="data_type == 1">
        <div class="box div row">
          <div class="label">报备编号：</div>
          <div class="label-ctn">
            {{ detail_customer.sale_order_sn || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">报备备注：</div>
          <div class="label-ctn">{{ detail_customer.remark }}</div>
        </div>
        <div class="box div row">
          <div class="label">成交时间：</div>
          <div class="label-ctn">
            {{ detail_customer.deal_at || "无" }}
          </div>
        </div>
      </div>
      <div class="detail_step" v-if="data_type == 2">
        <div class="list div " v-loading="is_step_loading">
          <div
            class="reason"
            v-if="report_step.length > 0 && report_step[0].cancel_reason"
          >
            【无效原因】{{ report_step[0].cancel_reason }}
          </div>
          <div class="box" v-for="item in report_step" :key="item.id">
            <div class="left"></div>
            <div class="right div ">
              <div class="time">{{ item.created_at }}</div>
              <div class="content">{{ item.description }}</div>
              <div
                class="img-box div row"
                v-if="JSON.parse(item.data).length > 0"
              >
                <el-image
                  v-for="(img, index) in JSON.parse(item.data)"
                  :key="index"
                  :src="img"
                  @click="clickImg(img)"
                  style="width: 100px; height: 100px;margin:5px"
                ></el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="img-big-box" v-if="big_img" style="cursor: pointer;">
        <div class="close" @click="big_img = ''">关闭</div>
        <el-image class="img" :src="big_img" fit="fill"></el-image>
      </div>
      <div v-if="data_type == 3">
        <div class="img-detail">
          <el-image
            v-for="item in customer_img"
            :key="item.id"
            style="width: 100px; height: 100px;margin:5px"
            @click="clickImg(item.file)"
            :src="item.file"
            fit="fill"
          ></el-image>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="佣金结算" :visible.sync="dialogVisibleSale" width="60%">
      <el-form label-width="200px" :model="form_customer_deal">
        <el-form-item label="成交单号">
          <el-input v-model="form_customer_deal.contract_no"></el-input>
        </el-form-item>
        <el-form-item label="产权地址">
          <el-input
            v-model="form_customer_deal.property_right_address"
          ></el-input>
        </el-form-item>
        <el-form-item label="成交金额">
          <el-input v-model="form_customer_deal.deal_amount">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="成交时间">
          <el-input v-model="form_customer_deal.deal_at"></el-input>
        </el-form-item>
        <el-form-item label="分佣金额">
          <el-input v-model="form_customer_deal.brokerage_amount">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="会员服务费">
          <el-input v-model="form_customer_deal.member_service_charge">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="已收会员服务费">
          <el-input v-model="form_customer_deal.earning_member_service_charge">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="会员服务费账期天数">
          <el-input v-model="form_customer_deal.customer_payment_days">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="开发商佣金账期天数">
          <el-input v-model="form_customer_deal.project_payment_days">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="扫码案场" prop="scan_code_user_id">
          <el-select
            filterable
            remote
            reserve-keyword
            v-model="form_customer_deal.scan_code_user_id"
            placeholder="请输入手机号"
            :loading="scan_code_user_loading"
            :remote-method="getScanData"
          >
            <el-option
              v-for="(item, index) in client_list"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="跟进案场" prop="follow_up_user_id">
          <el-select
            filterable
            remote
            reserve-keyword
            v-model="form_customer_deal.follow_up_user_id"
            placeholder="请输入手机号"
            :loading="follow_up_user_loading"
            :remote-method="getScanData"
          >
            <el-option
              v-for="(item, index) in client_list"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            v-model="form_customer_deal.remark"
          ></el-input>
        </el-form-item>
        <div class="div row" style="margin-left:100px">
          <el-button type="primary" @click="onSubmit">提交修改</el-button>
        </div>
      </el-form>
    </el-dialog>
    <el-dialog title="结算详情" :visible.sync="jiesuanDetail" width="60%">
      <div class="jiesuan">
        <div class="div row jiesuan-list">
          <div class="left">成交单号号：</div>
          <div class="right">{{ form_customer_deal.contract_no }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">客户联系方式：</div>
          <div class="right">{{ form_customer_deal.customer_phone }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">成交金额：</div>
          <div class="right">{{ form_customer_deal.deal_amount }}元</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">佣金分成：</div>
          <div class="right">{{ form_customer_deal.brokerage_amount }}元</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">单号日期：</div>
          <div class="right">{{ form_customer_deal.created_at }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">产权地址：</div>
          <div class="right">
            {{ form_customer_deal.property_right_address }}
          </div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">报备备注：</div>
          <div class="right">{{ form_customer_deal.remark }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">经纪人姓名：</div>
          <div class="right">{{ form_customer_deal.fu_name }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">经纪人联系方式：</div>
          <div class="right">{{ form_customer_deal.fu_phone }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">扫码案场：</div>
          <div class="right">{{ form_customer_deal.sc_name }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">案场联系方式：</div>
          <div class="right">{{ form_customer_deal.sc_phone }}</div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">会员服务费：</div>
          <div class="right">
            {{ form_customer_deal.member_service_charge }}元
          </div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">已收会员服务费：</div>
          <div class="right">
            {{ form_customer_deal.earning_member_service_charge }}元
          </div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">会员服务费账期天数：</div>
          <div class="right">
            {{ form_customer_deal.customer_payment_days }}天
          </div>
        </div>
        <div class="div row jiesuan-list">
          <div class="left">开发商佣金账期天数：</div>
          <div class="right">
            {{ form_customer_deal.project_payment_days }}天
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="业绩确认" :visible.sync="orderStatus" width="60%">
      <el-form label-width="100px" :model="update_confirm" inline>
        <el-form-item label="订单状态">
          <el-select v-model="update_confirm.confirm_status">
            <el-option
              v-for="(item, index) in sale_order_confirm_status_default_list"
              :key="index"
              :value="item.value"
              :label="item.description"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          ><el-button
            style="margin:0"
            type="primary"
            size="mine"
            @click="onConfirmStatus"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "sale_settlement",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      dropdown_search: [{ value: "customer_phone", name: "联系方式" }],
      input: "",
      params: {
        page: 1,
        customer_phone: "",
        per_page: 10,
        total: 0,
      },
      list_params: {
        date_str: "day",
        start: "",
        end: "",
      },
      sale_list: [],
      click_navs: [
        { type: 1, name: "报备详情" },
        { type: 2, name: "跟进列表" },
        { type: 3, name: "资料照片" },
      ],
      report_list: [],
      reception_list: [],
      audit_list: [],
      visit_category_list: [],
      form_audit: {
        customer_reported_id: "",
        status: "",
      },
      report_status: "",
      reception_status: "",
      dialogVisibleReport: false,
      data_type: 1,
      customer_img: [],
      report_step: [],
      detail_customer: {},
      form_customer_deal: {
        customer_reported_id: "",
        contract_no: "",
        property_right_address: "",
        deal_amount: "",
        deal_at: "",
        brokerage_amount: "",
        remark: "",
        earning_member_service_charge: "",
        scan_code_user_id: "",
        follow_up_user_id: "",
      },
      dialogVisibleSale: false,
      big_img: "",
      jiesuanDetail: false,
      sale_order_confirm_status_default_list: [],
      // 项目状态
      update_confirm: {
        customer_reported_id: "",
        confirm_status: "",
      },
      orderStatus: false,
      scan_code_user_loading: false,
      client_list: [],
      follow_up_user_loading: false,
      table_header: [
        {
          expand: "expand",
          render: (h, data) => {
            return (
              <el-form
                label-width="160px"
                label-position="right"
                class="demo-table-expand"
              >
                <el-form-item label="佣金应收金额：">
                  <span>{data.row.brokerage_amount}</span>
                </el-form-item>
                <el-form-item label="佣金已收金额：">
                  <span>{data.row.earning_brokerage_amount}</span>
                </el-form-item>
                <el-form-item label="佣金收款状态：">
                  <span>
                    {this.changeDesc(data.row.earning_brokerage_status)}
                  </span>
                </el-form-item>
                <el-form-item label="已结佣金额：">
                  <span>{data.row.disburse_brokerage_amount}</span>
                </el-form-item>
                <el-form-item label="结佣状态：">
                  <span>
                    {this.changeDesc(data.row.disburse_brokerage_status)}
                  </span>
                </el-form-item>
                <el-form-item label="会员服务费应收金额：">
                  <span>{data.row.member_service_charge}</span>
                </el-form-item>
                <el-form-item label="会员服务费已收金额：">
                  <span>{data.row.earning_member_service_charge}</span>
                </el-form-item>
                <el-form-item label="会员服务费收款状态：">
                  <span>{this.changeDesc(data.row.earning_msc_status)}</span>
                </el-form-item>
                <el-form-item label="项目公司：">
                  <span>{data.row.project_company_name}</span>
                </el-form-item>
                <el-form-item label="销售公司：">
                  <span>{data.row.sale_company_name}</span>
                </el-form-item>
              </el-form>
            );
          },
        },
        { prop: "sale_order_sn", label: "成交单号" },
        {
          label: "客户",
          render: (h, data) => {
            return (
              <div>
                {data.row.customer_name}
                <el-tag type="success" size="mini">
                  {data.row.customer_phone}
                </el-tag>
              </div>
            );
          },
        },
        { prop: "build_name", label: "意向楼盘" },
        {
          label: "经纪人",
          render: (h, data) => {
            return (
              <div>
                {data.row.su_name ||
                  data.row.su_nickname ||
                  data.row.su_user_name}
                <el-tag type="success" size="mini">
                  {data.row.su_phone}
                </el-tag>
              </div>
            );
          },
        },
        { prop: "deal_amount", label: "成交金额/元" },
        { prop: "deal_at", label: "成交日期" },
        { prop: "created_at", label: "报备时间" },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {data.row.confirm_status == 0 && this.$hasShow("结算佣金") ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.editSale(data.row);
                    }}
                  >
                    修改订单
                  </el-button>
                ) : (
                  ""
                )}
                {data.row.confirm_status == 1 && this.$hasShow("报备详情") ? (
                  <el-button
                    type="primary"
                    size="mini"
                    onClick={() => {
                      this.datadetail(data.row);
                    }}
                  >
                    报备详情
                  </el-button>
                ) : (
                  ""
                )}
                {data.row.confirm_status == 1 && this.$hasShow("结算详情") ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.settlementDetail(data.row);
                    }}
                  >
                    结算详情
                  </el-button>
                ) : (
                  ""
                )}
                {data.row.confirm_status == 0 ? (
                  <el-button
                    type="primary"
                    size="mini"
                    onClick={() => {
                      this.orderStatus = true;
                      this.update_confirm.customer_reported_id =
                        data.row.customer_reported_id;
                    }}
                  >
                    业绩确认
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
      time_array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
        { desc: "自定义", value: "customize", id: 8 },
      ],
      is_step_loading: true,
      isCustomize: false,
      withdrawal_info: "",
    };
  },
  mounted() {
    this.getWithdrawalInfo();
    this.getList();
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "REPORTED_STATUS":
            this.report_list = item.childs.reverse();
            break;
          case "CUSTOMER_RECEPTION_STATUS":
            this.reception_list = item.childs;
            break;
          case "REPORTED_AUDIT_STATUS_TYPE":
            this.audit_list = item.childs;
            break;
          case "REPORTED_VISIT_CATEGORY":
            this.visit_category_list = item.childs;
            break;
          case "SALE_ORDER_CONFIRM_STATUS_CATEGORY":
            this.sale_order_confirm_status_default_list = item.childs;
            break;
          case "SALE_ORDER_BILL_PAYMENT_STATUS_CATEGORY":
            this.sale_list = item.childs;
            break;
        }
      });
    });
  },
  methods: {
    // 搜索下拉
    handleCommand(command) {
      for (let key in this.params) {
        if (key == "customer_phone") {
          this.params[command] = this.params[key]; //在对象中添加了myId键名,并把当前key值赋值给了myId
          delete this.params[key]; //删除当前键名为id的属性
        }
      }
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getList();
    },
    // 全选
    // 获取扫码案场列表
    getScanData(query) {
      this.scan_code_user_loading = true;
      let params = {
        phone: query,
      };
      this.$http.getUserBroker({ params: params }).then((res) => {
        if (res.status === 200) {
          this.scan_code_user_loading = false;
          this.client_list = res.data.data.map((item) => {
            return {
              id: item.id,
              name: item.name || item.nickname || item.user_name,
            };
          });
        }
      });
    },
    // 点击选择日期数据
    onClickBrowse(index, id) {
      this.list_params.date_str = this.time_array[index].value;
      if (id === 8) {
        this.isCustomize = true;
      } else {
        this.isCustomize = false;
        this.list_params.start = "";
        this.list_params.end = "";
        this.getWithdrawalInfo();
      }
    },
    getWithdrawalInfo() {
      this.$http.getWithdrawalData(this.list_params).then((res) => {
        if (res.status === 200) {
          this.withdrawal_info = res.data;
        }
      });
    },
    clickTime() {
      if (!this.list_params.start) {
        this.$message({
          message: "请选择开始时间",
          type: "error",
        });
      } else if (!this.list_params.end) {
        this.$message({
          message: "请选择结束时间",
          type: "error",
        });
      } else {
        this.getWithdrawalInfo();
      }
    },
    getList() {
      if (!this.params.disburse_brokerage_status) {
        delete this.params.disburse_brokerage_status;
      }
      if (!this.params.earning_msc_status) {
        delete this.params.earning_msc_status;
      }
      if (!this.params.earning_brokerage_status) {
        delete this.params.earning_brokerage_status;
      }
      this.$http.saleSettlementList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onChange() {
      this.search();
    },
    search() {
      this.params.customer_phone = this.input;
      this.params.page = 1;
      this.getList();
    },
    onClickStatus() {
      this.params.page = 1;
      this.getList();
      //   this.params.sale_status = e;
      //   this.params.page = 1;
      //   this.getList();
      //   if (item.value == 1) {
      //     this.params.page = 1;
      //     this.params.sale_status = 1;
      //     this.getList();
      //   } else {
      //     this.params.page = 1;
      //     this.params.sale_status = 0;
      //     this.getList();
      //   }
    },
    editSale(row) {
      this.dialogVisibleSale = true;
      this.$http.QueryOrder(row.customer_reported_id).then((res) => {
        if (res.status === 200) {
          this.form_customer_deal = res.data;
          this.client_list = [
            {
              id: this.form_customer_deal.follow_up_user_id,
              name:
                this.form_customer_deal.fu_name ||
                this.form_customer_deal.fu_nickname ||
                this.form_customer_deal.fu_user_name,
            },
            {
              id: this.form_customer_deal.scan_code_user_id,
              name:
                this.form_customer_deal.su_name ||
                this.form_customer_deal.su_nickname ||
                this.form_customer_deal.su_user_name,
            },
          ];
        }
      });
    },
    // 报备详情弹出框关闭清除
    reportClose() {
      this.customer_img = "";
      this.data_type = 1;
    },
    settlementDetail(row) {
      this.$http.QueryOrder(row.customer_reported_id).then((res) => {
        if (res.status === 200) {
          this.form_customer_deal = res.data;
          this.jiesuanDetail = true;
        }
      });
    },
    // 获取资料图片
    getCustomerImg() {
      this.$http
        .getCustomerImg(
          this.detail_customer.project_id,
          this.detail_customer.customer_reported_id
        )
        .then((res) => {
          if (res.status === 200) {
            if (res.data) {
              this.customer_img = res.data;
            }
          }
        });
    },
    // 获取报备进度
    getReportStep() {
      this.is_step_loading = true;
      this.report_step = [];
      this.$http
        .getReportStep(
          this.detail_customer.project_id,
          this.detail_customer.customer_reported_id
        )
        .then((res) => {
          this.is_step_loading = false;
          if (res.status === 200) {
            if (res.data) {
              this.report_step = res.data.data;
            }
          }
        });
    },
    onClickNav(index) {
      this.data_type = this.click_navs[index].type;
      if (this.data_type == 2) {
        this.getReportStep();
      }
      if (this.data_type == 3) {
        this.getCustomerImg();
      }
    },
    datadetail(row) {
      this.detail_customer = row;
      this.visit_category_list.map((item) => {
        if ((this.detail_customer.visit_category = parseInt(item.value))) {
          this.detail_customer.visit_category = item.description;
        }
      });
      this.form_audit.customer_reported_id = row.customer_reported_id;
      this.dialogVisibleReport = true;
      for (var i in this.report_list) {
        if (parseInt(this.report_list[i].value) === row.reported_status) {
          this.report_status = this.report_list[i].description;
        }
      }
      for (var j in this.reception_list) {
        if (parseInt(this.reception_list[j].value) === row.reception_status) {
          this.reception_status = this.reception_list[j].description;
        }
      }
      console.log(this.detail_customer);
    },
    onSubmit() {
      this.$http
        .editOrder({
          customer_reported_id: this.form_customer_deal.customer_reported_id,
          contract_no: this.form_customer_deal.contract_no,
          property_right_address: this.form_customer_deal
            .property_right_address,
          deal_amount: this.form_customer_deal.deal_amount,
          deal_at: this.form_customer_deal.deal_at,
          brokerage_amount: this.form_customer_deal.brokerage_amount,
          remark: this.form_customer_deal.remark,
          earning_member_service_charge: this.form_customer_deal
            .earning_member_service_charge,
          member_service_charge: this.form_customer_deal.member_service_charge,
          customer_payment_days: this.form_customer_deal.customer_payment_days,
          project_payment_days: this.form_customer_deal.project_payment_days,
          scan_code_user_id: this.form_customer_deal.scan_code_user_id,
          follow_up_user_id: this.form_customer_deal.follow_up_user_id,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.dialogVisibleSale = false;
            this.getList();
          }
        });
    },
    clickImg(img) {
      this.big_img = img;
    },
    onConfirmStatus() {
      this.$http.updateOrderStatus(this.update_confirm).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.getList();
          this.update_confirm.confirm_status = "";
          this.orderStatus = false;
        }
      });
    },
    changeDesc(e) {
      var data = this.sale_list.find((item) => {
        if (item.value == e) {
          return item;
        }
      });
      return data.description;
    },
  },
};
</script>

<style lang="scss" scoped>
.reason {
  margin-left: 50px;
}
// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;
  .browse {
    justify-content: space-between;
    .browse-box {
      width: 480px;
      align-items: center;
      .browse-item {
        margin: 0 5px;
        font-size: 14px;
        padding: 2px 10px;
        border-radius: 50px;
        color: #333;
        &.browse_active {
          color: #fff;
          background: #0068e6;
        }
      }
    }
  }

  .block-time {
    justify-content: flex-start;
    margin: 10px;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
  }
  .bg-purple {
    border: 1px dashed #d3dce6;
  }
  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;
    align-items: center;
    .left {
      color: #999;
      font-size: 14px;
      text-align: start;
      p {
        color: #333;
      }
      .desc {
        color: #999;
      }
    }
    .right {
      color: #26bf8c;
    }
  }
}

.el-row-tab {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
.bg-purple {
  border: 1px dashed #d3dce6;
}
.grid-content {
  padding: 10px;
  margin: 10px;
  justify-content: space-between;
  border-radius: 4px;
  min-height: 36px;
  align-items: center;
  .left {
    color: #999;
    font-size: 14px;
    text-align: start;
    p {
      color: #333;
    }
    .desc {
      color: #999;
    }
  }
  .right {
    color: #26bf8c;
  }
}
.demo-table-expand {
  font-size: 0;
  flex-wrap: wrap;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.jiesuan {
  font-size: 16px;
  .jiesuan-list {
    margin: 8px 0;
    background: #ecf5ff;
    .left {
      color: #409eff;
      width: 180px;
      text-align: right;
    }
    .right {
      margin-left: 4px;
    }
  }
}
.img-big-box {
  box-shadow: 2px 2px 3px #aaaaaa;
  width: 350px;
  height: 350px;
  background: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 10;
  .close {
    box-shadow: 2px 2px 3px #aaaaaa;
    z-index: 11;
    right: -20px;
    top: -20px;
    width: 40px;
    height: 40px;
    background: #eee;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    position: absolute;
  }
  .close:hover {
    color: #fff;
    box-shadow: 0 1px 3px rgba(209, 40, 42, 0.5);
    background: #d1282a;
  }
  .img {
    width: 350px;
    height: 350px;
  }
}
.select-box {
  margin: 30px;
  margin-top: 0;
  .el-button {
    z-index: 1;
    width: 100px;
    margin: 0 15px;
  }
  .block {
    height: 22px;
  }
}
.el-input {
  width: 150px;
}
.table-list {
  height: 580px;
  .el-table {
    height: 100%;
  }
  .top-select {
    .el-tabs--border-card {
      border: none;
      .el-tabs__nav-scroll {
        border: 1px solid #d1dbe5;
        border-bottom: none;
      }
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}

.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
.dropbutton {
  width: 54px;
  height: 26px;
  font-size: 12px;
  margin: 5px;
  padding: 5px 15px;
  background: #409eff;
  color: #fff;
}
/deep/ .dia-detail {
  height: auto;
  overflow: hidden;
}
.img-detail {
  height: 500px;
  overflow: hidden;
  overflow-y: scroll;
}
.nav {
  .nav-item {
    cursor: pointer;
    font-size: 14px;
    align-items: center;
    margin-right: 80px;
    padding-bottom: 10px;
    border-bottom: 4px solid #fff;
    &.active {
      color: #0068e6;
      border-bottom: 4px solid #0068e6;
    }
  }
}
.detail {
  margin-top: 20px;
  .box {
    margin-top: 20px;
    .label-ctn {
      margin-left: 10px;
      margin-right: 10px;
    }
  }
}
.list {
  margin-top: 40px;
  flex-direction: column;
  line-height: 50px;
  margin-bottom: 100px;
  overflow: hidden;
  height: 500px;
  overflow-y: scroll;
  .box {
    margin-left: 50px;
    position: relative;
    padding-left: 40px;
    border-left: 2px dashed #999;
  }
  .left {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 8px solid #999;
    left: -20px;
    background: #fff;
  }
  .right {
    margin-bottom: 40px;
    flex-direction: column;
    .time {
      font-size: 16px;
    }
    .content {
      color: #999;
      font-size: 13px;
    }
    .img-box {
      justify-content: flex-start;
      flex-wrap: wrap;

      &::after {
        content: "";
        width: auto;
      }
    }
  }
}
</style>
