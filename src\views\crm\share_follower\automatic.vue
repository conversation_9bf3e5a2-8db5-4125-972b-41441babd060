<template>
    <div>
        <div>
          <!-- 员工 -->
          <div v-if="staff==2">
            <el-input
            style="width: 89%;"
            v-model="searchMember"
            prefix-icon="el-icon-search"
            placeholder="请输入搜索词"
            class="choose_employee"
            @input="ChangeInput"
            ></el-input>
            <!-- 选择栏 -->
            <div class="flex-row">
            <!-- 员工列 -->
            <div class="choose_box_left flex-box">
              <div class="choose_box_header">
                <span>员工</span>
              </div>
              <div class="choose_box_content">
                <el-checkbox-group v-model="selected_Member">
                  <el-checkbox
                    v-for="(item, index) in userList"
                    v-model="item.selected"
                    :key="index"
                    class="choose_Multiple_selection"
                    :label="item.id"
                    @change="selectedChange"
                  >
                    {{ item.user_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <!-- 已选择员工列 -->
            <div class="choose_box_right flex-box">
              <div class="choose_box_header">
                <span>已选{{ this.selected_Member.length }}项</span>
                <span style="margin-left: auto" @click="clearSelected">
                  清空
                </span>
              </div>
              <div class="selected_box_content">
                <div
                  class="selected_box_text"
                  v-for="(item, index) in member_detailed"
                  :key="index"
                >
                  <span>{{ item.user_name }}</span>
                  <i
                    class="el-icon-close cancel_selected"
                    @click="deleteEmployee(item, index)"
                  ></i>
                </div>
              </div>
            </div>
            </div>
          </div>
          <!-- 分组 -->
          <div v-if="staff==3">
            <!-- 分组表格 -->
            <div>
              <el-table
              :data="GoupData"
              style="width: 100%"
              @row-click="singleElection"
              highlight-current-row>
              <el-table-column
                label="选择">
                <template slot-scope="scope">
                  <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
                  <el-radio class="radio" v-model="templateSelection" :label="scope.row.id"
                    >&nbsp;</el-radio
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="title"
                label="分组名"
                >
              </el-table-column>
              <el-table-column label="成员" v-slot="{ row }"
              width="300">
                  <template v-if="row.items && row.items.length">

                      <div>
                          <span v-for="(item, index) in row.items.slice(0, 3)" :key="item.admin.user_name">
                              {{ item.admin.user_name }}<span v-if="index < 2">, </span>
                          </span>
                          <span v-if="row.items.length > 3"> 等 (共{{row.items.length}}人)</span>
                      </div>
                  </template>
                  <template v-else>
                      暂无成员
                  </template>
              </el-table-column>
            </el-table>
            </div>
            <div class="gouppaging">
              <el-pagination
                layout="prev, pager, next"
                :total="allgoupdata.total"
                :page-size="allgoupdata.per_page"
                :current-page="allgoupdata.current_page"
                @current-change="onPageChange">
              </el-pagination>
            </div>
          </div>
          </div>
    </div>
</template>
<script>
export default {
    props:{
        search_user_list: {
          type: Array,
          required: true
        },
        user_list: {
            type: Array,
            default: () => []
        },
        GoupData: {
            type: Array,
            default: () => []
        },
        allgoupdata:{
          type: Object,
          default: () => {}
        },
        totalConfig_params:{
          type:Object,
          default:() => {}
        }
    },
    data() {
        return {
            push_way: 1,//成员or分组
            staff:2,//员工
            searchMember: "", // 搜索成员关键词
            selected_Member: [], // 已选中的成员
            member_detailed: [], // 已选中的成员的详细信息
            userList:[],
            // allgoupdata:[],//加分页的分组数据
            templateSelection: "",//分组当前选择的行的id
            // 线索分配配置
            totalConfig_paramscopy: {
            },
        }
    },
    watch:{
      'totalConfig_paramscopy.mode':{
          handler(newVal, oldVal) {
            if(newVal==2){
              this.staff = 2
            }else if(newVal==3){
              this.staff = 3
            }
        }
      },
      GoupData: {
        handler(newVal) {
            if (newVal) {
                this.GoupData = newVal;
                // 强制更新表格
                this.$forceUpdate();
            }
            // console.log(this.GoupData);
        },
        // immediate: true, // 如果需要在组件加载时立即执行
    },
    },
    created() {
      // 将prop的值赋给数据属性
      this.userList = this.search_user_list;
      this.totalConfig_paramscopy = this.totalConfig_params
      this.templateSelection = Number(this.totalConfig_paramscopy.private_group_id)
      if(this.totalConfig_paramscopy.mode){
        this.staff = this.totalConfig_paramscopy.mode
      }
      // this.push_way = this.totalConfig_paramscopy.private_push_way
      // this.groupchange(this.push_way)
      if (this.totalConfig_params.private_push_uid != "") {
              let num = this.totalConfig_params.private_push_uid.split(',')
              for (let i = 0; i < num.length; i++) {
                num[i] = parseInt(num[i])
              }
              // 如果已经选中的员工不在员工列表中就删除
              let is_exist = [];
              this.search_user_list.map((item) => { //遍历员工列表
                // 遍历已选中的员工列表
                num.map((list) => {
                  // 如果员工存在
                  if (item.id == list) {
                    is_exist.push(item.id);
                  }
                })
              })
              this.selected_Member = is_exist; // 赋值选中并且存在的员工
              this.getSelected_Member();
            }
    },
    methods:{
      // 搜索员工
      ChangeInput(val) {
        this.userList = this.user_list
        let num = [];
        this.userList.map(item => {
          if (item.user_name.indexOf(val) != -1) {
            num.push(item)
          }
        })
        this.userList = num;
      },
      selectedChange() {
        this.member_detailed = [];
        this.getSelected_Member();
      },
      //分组表格单选
      singleElection(row) {
        this.templateSelection = row.id 
        // console.log(this.templateSelection );
        this.totalConfig_params.private_group_id = String(this.templateSelection)
        this.$emit("receivegoupdata",{private_group_id:this.totalConfig_params.private_group_id, private_push_way: this.push_way})
      },
      //分组表格分页
      onPageChange(e){
        this.$emit("onPageChange",e)
        // this.cluegrouppage.page = e
        // console.log(this.cluegrouppage);
        // this.cluegrouping()
      },
      // 获取已选中成员的详细信息
      getSelected_Member() {
       let num = [];
      //  console.log(this.selected_Member);
       this.$emit("receivedata",{ private_push_uid: this.selected_Member, private_push_way: this.push_way })
       for (let i = 0; i < this.selected_Member.length; i++) {
         let Member = "";
         this.user_list.map((item) => {
           if (item.id == this.selected_Member[i]) {
             Member = item;
           }
         })
        //  console.log(Member,"==============");
         if (Member) {
           num.push(Member);
         }
       }
       num.map(item => {
         this.member_detailed.push(item)
       })
      //  console.log(this.member_detailed);
      },
      // 清除已选员工
      clearSelected() {
        this.member_detailed = [];
        this.selected_Member = [];
        this.$emit("receivedata",{ private_push_uid: this.selected_Member, private_push_way: this.push_way })
      },
      // 删除，指定已选择的员工
      deleteEmployee(item) {
        let subIndex = this.selected_Member.indexOf(item.id);
        // 删除已选中的成员 左部分选择
        this.selected_Member.splice(subIndex, 1);
        // 删除已选中的成员的详细信息 右部分选择
        this.member_detailed.splice(subIndex, 1);
        this.$emit("receivedata",{ private_push_uid: this.selected_Member, private_push_way: this.push_way })
      },
      //清空所选成员
      emptyuser(){
        this.member_detailed = []
        this.selected_Member = []
      },
      //清空所选组
      emptygroup(){
        this.templateSelection = ""
      },
    },
}
</script>
<style scoped lang="scss" >
    .choose_title {
      font-size: 14px;
      color: #2e3c4e;
      margin: 15px 0;
      cursor: pointer;
    }
    .chooseA{
      color: #409EFF;
    }
    .operatebtn{
      margin-left: 10px;
      margin-right: 10px;
    }
    .choose_employee {
      margin-bottom: 15px;
      .el-input__inner {
        width: 400px;
      }
    }
    .choose_box_left {
      .choose_box_content {
        width: 100%;
        height: 310px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .el-checkbox-group {
          display: flex;
          flex-direction: column;
        }
        .choose_Multiple_selection {
          background-color: #f4f4f5;
          padding: 5px 8px;
          border-bottom: 1px solid #e3e3e5;
          margin-right: 0px;
          .el-checkbox__label {
            font-size: 12px;
            color: #2e3c4e;
          }
        }
        .choose_Multiple_selection:last-child {
          border-bottom: none;
        }
      }
    }
    .choose_box_right,
    .choose_box_left {
      width: 240px;
      height: 346px;
      border: 1px solid #e3e3e5;
      border-radius: 3px;
      .choose_box_header {
        display: flex;
        background-color: #f8f8f7;
        padding: 10px 0;
        border-bottom: 1px solid #e3e3e5;
        & span {
          color: #2e3c4e;
          font-size: 12px;
          font-weight: 600;
          margin-left: 10px;
        }
      }
    }
    .choose_box_right {
      margin-left: 15px;
      .choose_box_header {
        & span:nth-child(2) {
          font-weight: 500;
          color: #3961e4;
          margin-right: 10px;
          cursor: pointer;
        }
        & span:last-child {
          font-weight: 500;
          color: #2e3c4e;
          margin-right: 10px;
        }
      }
      .selected_box_content {
        width: 100%;
        height: 310px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .selected_box_text:first-child {
          margin-top: 10px;
        }
        .selected_box_text {
          display: flex;
          align-items: center;
          background: #f4f4f5;
          padding: 2px 5px;
          margin: 0 10px 10px 10px;
          border-radius: 3px;
          font-size: 12px;
          color: #2e3c4e;
          .cancel_selected {
            width: 12px;
            height: 10px;
            margin-left: auto;
            cursor: pointer;
          }
        }
      }
    }
</style>