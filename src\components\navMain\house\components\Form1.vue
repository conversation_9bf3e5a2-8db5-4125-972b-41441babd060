
<template>
  <el-form ref="form" :model="form" label-width="80px" label-position="left">
    <div class="form_block">
      <!-- <div class="title">基础信息</div> -->
      <div class="title">
        {{
          form.trade_type === 1
          ? "基础信息"
          : form.trade_type === 2
            ? "出租信息"
            : "出售信息"
        }}
      </div>
      <el-form-item label="类型" prop="trade_type">
        <el-radio-group v-model="form.trade_type" size="mini">
          <el-radio v-for="item in form_options.tradeType" :key="item.values" :label="item.values" border>{{ item.name
          }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="小区" prop="community_id">
        <template #label>
          <span style="color: #F56C6C;">*</span>
          小区
        </template>
        <el-select v-model="form.community_id" filterable remote reserve-keyword :remote-method="searchCommunity"
          :loading="community_loading" style="width: 240px; margin-right: 12px" placeholder="请搜索并选择"
          @change="onCommunityChange">
          <el-option v-for="item in community_list" :key="item.id" :value="item.id" :label="item.title">
            <div class="option-wrapper">
              {{ item.detail }}
            </div>
          </el-option>
          <el-option v-if="community_list.length === 0 && unsearch" :value="null">
            <div class="add_community" @click="showApplyCommunity">
              <span>没找到？申请添加</span>
              <i class="el-icon-circle-plus"></i>
            </div>
          </el-option>
        </el-select>
        <el-select v-model="form.usage_type_id" style="width: 140px; margin-right: 12px" placeholder="请选择用途">
          <el-option v-for="item in form_options.usageType" :key="item.values" :value="item.values"
            :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <!-- label="房号" <template v-if="loudong_custom_level === 1">
          <div class="form-item-block">
            <el-input
              v-model="form.loudong"
              style="width: 140px; margin-right: 12px"
              placeholder="请输入楼栋号"
            ></el-input>
          </div>
        </template> -->
        <div class="flex-row items-center" slot="label">
          <span>
            <span style="color: #F56C6C; margin-right: 4px;">*</span>房号
          </span>
          <el-tooltip placement="top-start" width="200" trigger="hover" effect="light" v-if="isOpenShowingSingle">
            <div slot="content" style="line-height: 1.5">
              单边代理已开启，仅同店和平台巡检可查看
            </div>
            <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
            <span icon="el-icon-info" type="danger"><i class="el-icon-info" style="color: #f56c6c"></i></span>
          </el-tooltip>
        </div>
        <div class="form-item-block">
          <el-select :disabled="!form.community_id" v-model="form.building_loudong_id"
            style="width: 140px; margin-right: 12px" placeholder="请选择楼号" filterable remote :remote-method="customerSearch"
            @change="onSelectLoudong">
            <el-option v-for="item in loudong_list" :key="item.loudong" :value="item.loudong"
              :label="item.name"></el-option>
            <!-- <el-option value="">
              <span @click="loudong_custom_level = 1">没有合适的?自定义</span>
            </el-option> -->
          </el-select>
        </div>
        <!-- <template v-if="danyuan_custom_level === 1">
          <div class="form-item-block">
            <el-input
              v-model="form.danyuan"
              style="width: 140px; margin-right: 12px"
              placeholder="请输入单元号"
            ></el-input>
          </div>
        </template> -->

        <div class="form-item-block" v-if="show_unit">
          <el-select :disabled="!form.community_id" v-model="form.building_danyuan_id"
            style="width: 140px; margin-right: 12px" placeholder="请选择单元" filterable remote :remote-method="(e) => {
              return customerSearch(e, 'danyuan');
            }
              " @change="onSelectDanyuan">
            <el-option v-for="item in danyuan_list" :key="item.danyuan" :value="item.danyuan"
              :label="item.name"></el-option>
            <!-- <el-option value="">
              <span @click="danyuan_custom_level = 1">没有合适的?自定义</span>
            </el-option> -->
          </el-select>
        </div>
        <!-- <template v-if="fanghao_custom_level === 1">
          <div class="form-item-block">
            <el-input
              v-model="form.fanghao"
              style="width: 140px; margin-right: 12px"
              placeholder="请输入房号"
            ></el-input>
          </div>
        </template> -->
        <div class="form-item-block">
          <el-select :disabled="!form.community_id" v-model="form.building_unit_id"
            style="width: 140px; margin-right: 12px" placeholder="请选择户号" filterable remote :remote-method="(e) => {
              return customerSearch(e, 'fanghao');
            }
              " @change="checkRepeat">
            <el-option v-for="item in huhao_list" :key="item.value" :value="item.value" :label="item.name"></el-option>
            <!-- <el-option value="">
              <span @click="fanghao_custom_level = 1">没有合适的?自定义</span>
            </el-option> -->
          </el-select>
        </div>
        <div class="form-item-block" v-if="form.community_id">
          <el-link type="primary" @click="showSelectTable">选择</el-link>
        </div>

        <!-- <span style="margin: 0 6px">(号/室)</span> -->
      </el-form-item>
      <el-form-item style="display: inline-block" label="楼层" prop="total_floor" v-if="form.building_danyuan_id.split('_').length > 1 ||
        form.building_unit_id.split('_').length > 1 ||
        !show_unit
        ">
        <template #label>
          <span style="color: #F56C6C;">*</span>
          楼层
        </template>
        <el-input v-if="form.building_danyuan_id.split('_').length != 1 || !show_unit" v-model="form.total_floor"
          style="width: 130px; margin-right: 12px" placeholder="请输入总楼层"></el-input>
      </el-form-item>
      <el-form-item style="display: inline-block; position: absolute;" label-width="0px" prop="sz_floor" v-if="form.building_danyuan_id.split('_').length > 1 ||
        form.building_unit_id.split('_').length > 1 ||
        !show_unit
        ">
        <el-input v-if="form.building_unit_id.split('_').length != 1" v-model="form.sz_floor"
          style="width: 130px; margin-right: 12px;" placeholder="请输入所在楼层"></el-input>
      </el-form-item>
      <div></div>
      <el-form-item label="户型" prop="shi" style="display: inline-block">
        <el-input v-number_rang="[0]" v-model="form.shi" style="width: 127px; margin-right: 12px"
          placeholder="请输入室"><template slot="append">室</template></el-input>
      </el-form-item>
      <el-form-item label-width="0px" prop="ting" style="display: inline-block">
        <el-input v-number_rang="[0]" v-model="form.ting" style="width: 127px; margin: 0 12px"
          placeholder="请输入厅"><template slot="append">厅</template></el-input>
      </el-form-item>
      <el-form-item label-width="0px" prop="wei" style="display: inline-block">
        <el-input v-number_rang="[0]" v-model="form.wei" style="width: 126px; margin: 0 12px" placeholder="请输入卫">
          <template slot="append">卫</template></el-input>
      </el-form-item>
      <el-form-item label-width="0px" prop="kitchen" style="display: inline-block">
        <el-input v-number_rang="[0]" v-model="form.kitchen" style="width: 126px; margin: 0 12px" placeholder="厨房数量">
          <template slot="append">厨</template></el-input>
      </el-form-item>
      <el-form-item label-width="0px" prop="balcony" style="display: inline-block">
        <el-input v-number_rang="[0]" v-model="form.balcony" style="width: 126px; margin: 0 12px" placeholder="阳台数量">
          <template slot="append">阳台</template>
        </el-input>
      </el-form-item>
      <el-form-item label="装修" prop="zhuangxiu">
        <el-select v-model="form.zhuangxiu" style="width: 240px; margin-right: 12px" placeholder="请选择装修">
          <el-option v-for="item in form_options.decoration" :key="item.values" :value="item.values"
            :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="朝向" prop="chaoxiang">
        <el-select v-model="form.chaoxiang" style="width: 240px; margin-right: 12px" placeholder="请选择朝向">
          <el-option v-for="item in form_options.direction" :key="item.values" :value="item.values"
            :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="面积" prop="mianji">
        <template #label>
          <span style="color: #F56C6C;">*</span>
          面积
        </template>
        <el-input v-model="form.mianji" v-number_rang:[2]="[0]" style="width: 240px; margin-right: 12px"
          placeholder="请输入面积"><template slot="append">m²</template></el-input>
        <!-- <span>m²</span> -->
      </el-form-item>
      <el-row v-if="house_type === 1 || house_type === 3">
        <el-col :span="10">
          <el-form-item prop="sale_price" label="出售价格" v-if="house_type === 1 || house_type === 3">
            <template #label>
              <span style="color: #F56C6C;">*</span>
              出售价格
            </template>
            <el-input v-number_rang:[2]="[0]" v-model="form.sale_price" maxlength="13" style="width: 240px"><template
                slot="append">万元</template></el-input>
            <!-- <span class="unit">万元</span> -->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="house_type === 2 || house_type === 3">
        <el-col :span="10">
          <el-form-item label="出租价格" prop="rent_price">
            <template #label>
              <span style="color: #F56C6C;">*</span>
              出租价格
            </template>
            <el-input v-number_rang:[2]="[0]" v-model="form.rent_price" maxlength="13" style="width: 240px"><template
                slot="append">元/月</template></el-input>
            <!-- <span class="unit">元/月</span> -->
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="14">
          <el-form-item label="设置等级">
            <el-radio-group v-model="form.level" size="mini">
              <el-radio label="A" border="">A级</el-radio>
              <el-radio label="B" border="">B级</el-radio>
              <el-radio label="C" border="">C级</el-radio>
            </el-radio-group>
            <div class="tips level_tips">
              <div>
                A级房源业主配合度高、无交易纠纷、价格低、短期有可能成交的优质房源。
              </div>
              <div>B级房源价格适中、业主配合看房、可正常交易的一般房源。</div>
              <div>
                C级房源多为价格明显超出、业主配合度低不配合看房、不易成交的房源。
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row> -->
    </div>
    <div class="copy">
      <el-link type="primary">快速智能识别</el-link>
      <div>
        <el-input type="textarea" placeholder="请输入要识别的内容" :rows="6" style="width: 400px" v-model="copy_content"
          @input="getCopyData"></el-input>
      </div>
    </div>
    <el-dialog width="1000px" :visible.sync="show_select_table" title="选择楼栋">
      <div class="sel_container">
        <div class="top flex-row">
          <div class="top_left flex-1"></div>
          <template v-if="!isOpenShowingSingle">
            <div class="top_right sale_b flex-row align-center">出售</div>
            <div class="top_right rent_b flex-row align-center">出租</div>
            <div class="top_right sale_rent_b flex-row align-center">租售</div>
          </template>
          <div class="top_right">
            <el-link type="primary" @click="current_status_type = !current_status_type">{{ current_status_type ? "横排" :
              "竖排" }}</el-link>
          </div>
        </div>
        <div class="flex-row select_box">
          <div class="se_ld">
            <div class="se_container se_th">
              <div class="se_box">
                <div class="se_item se_th">
                  <div class="shangsanjiao"></div>
                  <div class="xiasanjiao"></div>
                  座栋名称
                </div>
              </div>
            </div>
            <div class="se_container se_tr">
              <div class="se_box">
                <div class="shangsanjiao"></div>
                <div class="xiasanjiao"></div>
                <div class="se_item" :class="{ active: form.building_loudong_id == item.loudong }"
                  v-for="item in loudong_list" :key="item.loudong" @click="clickLoudong(item)">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="se_ld" v-if="show_unit">
            <div class="se_container se_th">
              <div class="se_box">
                <div class="se_item se_th">
                  <div class="shangsanjiao"></div>
                  <div class="xiasanjiao"></div>
                  单元名称
                </div>
              </div>
            </div>
            <div class="se_container se_tr">
              <div class="se_box">
                <div class="shangsanjiao"></div>
                <div class="xiasanjiao"></div>
                <div class="se_item" :class="{ active: form.building_danyuan_id == item.danyuan }"
                  v-for="item in danyuan_list" :key="item.danyuan" @click="clickUnit(item)">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="se_ld" v-if="show_louceng">
            <div class="se_container se_th">
              <div class="se_box">
                <div class="se_item se_th">
                  <div class="shangsanjiao"></div>
                  <div class="xiasanjiao"></div>
                  楼层名称
                </div>
              </div>
            </div>
            <div class="se_container se_tr">
              <div class="se_box">
                <div class="shangsanjiao"></div>
                <div class="xiasanjiao"></div>
                <div class="se_item" :class="{ active: se_louceng == item.value }" v-for="item in louceng_list"
                  :key="item.value" @click="clickLouceng(item)">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="se_ld flex-1">
            <div class="se_item se_th">房间号</div>
            <div class="se_tr fanghao">
              <div class="se_box" :class="{
                'flex-row': !current_status_type,
                'align-center': !current_status_type,
                row: !current_status_type,
              }">
                <div class="se_item flex-row align-center j-center" :class="{
                  active: form.building_unit_id == item.value,
                  sale: item.trade_type == 1 && !isOpenShowingSingle,
                  rent: item.trade_type == 2 && !isOpenShowingSingle,
                  sale_rent: item.trade_type == 3 && !isOpenShowingSingle,
                }" v-for="item in huhao_list" :key="item.value" @click="selectFloor(item)">
                  <span>{{ item.name }}</span>
                  <!-- <el-link type="primary"> 选择 </el-link> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog width="1000px" :visible.sync="show_apply_community" title="申请小区">
      <addCommunity v-if="show_apply_community" dialogTitle="addData" :form="c_form" @success="handleSuccess">
      </addCommunity>
    </el-dialog>
  </el-form>
</template>

<script>
// import discernHouse from '@/utils/discern_house'
import config from "@/utils/config.js";
import addCommunity from "@/components/navMain/house/components/addCommunity.vue"
export default {
  name: "LmAddForm1",
  components: { addCommunity },
  data() {
    return {
      upload_api: process.env.VUE_APP_API + "/v1/erp/uploadHousePhoto",
      upload_api2: process.env.VUE_APP_API + "/v1/erp/uploadFile",
      upload_headers: {
        Authorization: config.TOKEN,
      },
      c_form: {
        title: "",
        addr: "",
        lat: "",
        lng: "",
        region_id: '',
        region_name: ''
      },
      loudong_units: [],
      danyuan_units: [],
      loudong_list: [],
      danyuan_list: [],
      huhao_list: [],
      fanghao_custom_level: 0, //户号自定义级别1：楼栋 2：单元 3：户号
      up_loading1: false,
      up_loading2: false,
      up_loading3: false,
      up_loading4: false,
      up_loading5: false,
      indoor_imgs: [],
      plans_imgs: [],
      outdoor_imgs: [],
      other_imgs: [],
      form: {
        trade_type: 1,
        community_id: "",
        shi: "",
        ting: "",
        wei: "",
        kitchen: "",
        balcony: "",
        mianji: "",
        taoneimianji: "",
        zhuangxiu: 1,
        chaoxiang: 9,
        loudong: "",
        loudongdanwei: "",
        danyuan: "",
        danyuandanwei: "",
        fanghao: "",
        building_unit_id: "",
        building_danyuan_id: "",
        building_loudong_id: "",
        exclusive: "",
        is_repeat: "", //房源是否查重
        status: "",
        trade_status: 9,
        // hand_over_date: '',
        // fangben: '',
        // chanquanleixing: '',
        // chanquannianxian: '',
        rent_expire_time: "",
        // open_time: '',
        // trade_status: '',
        // management: '',
        // management_fee: '',
        // beian_no: '',
        has_key: "",
        // exclusive: '',
        only_house: "",
        occupancy: "",
        // delegate_type: '',
        // sell_delegate_user: '',
        // rent_delegate_user: '',
        // sell_delegate_time: '',
        // rent_delegate_time: '',
        buy_payment_type: "",
        exclusive_voucher: "",
        payment_terms: "",
        sale_price: "",
        rent_price: "",
        sale_base_price: "",
        rent_base_price: "",
        pic: [],
        usage_type_id: 1,
      },
      unsearch: false,
      community_list: [],
      community_loading: false,
      show_apply_community: false,
      show_map: false,
      search_key: "",
      point_marker: {},
      map_address: "",
      map_search_list: [],
      mapkey: "3MQBZ-VZLW3-QTV3J-YWCM6-CT7ZJ-RPFJE",
      copy_content: "",
      has_set_cover: false,
      loudong_custom_level: 0,
      danyuan_custom_level: 0,
      loudong_list_back: [],
      danyuan_list_back: [],
      huhao_list_back: [],
      show_select_table: false,
      se_loudong: "",
      se_danyuan: "",
      se_louceng: "",
      se_floor: "",
      louceng_list: [],
      fanghao_list: [],
      current_status_type: true,
      current_loudong_name: "",
      current_danyuan_name: "",
      show_unit: true, //是否显示单元
      show_louceng: false, //是否显示楼层
    };
  },
  props: {
    form_options: {
      type: Object,
      default: () => { },
    },
    house_type: {
      type: Number,
      default: 1,
    },
    form_data: {
      type: Object,
      default: () => { },
    },
    isOpenShowingSingle: {
      type: [String, Boolean, Number],
      default: false,
    }
  },
  watch: {
    form_data(new_val) {
      this.community_list = [
        { title: new_val.title, id: new_val.community_id },
      ];
      this.huhao_list = [
        {
          name: new_val.fanghao,
          value: new_val.building_unit_id,
        },
      ];
      // this.params.loudong =
      for (let key in new_val) {
        if (
          this.form[key] !== undefined ||
          (this.form[key] && this.form[key].length === 0)
        ) {
          this.form[key] = new_val[key];
        }
      }
      this.form.pic.forEach((item) => {
        switch (item.category_id) {
          case 1:
            this.indoor_imgs.push(item);
            break;
          case 2:
            this.plans_imgs.push(item);
            break;
          case 3:
            this.outdoor_imgs.push(item);
            break;
          case 4:
            this.other_imgs.push(item);
            break;
        }
      });
      this.getOtherOptions(new_val);
      this.getRange(this.form.area_id);
    },
    form: {
      handler(n_val) {
        this.$emit("change", n_val);
      },
      deep: true,
    },
    "form.community_id"(val, old_value) {
      if (old_value) {
        // 小区有改变需要清空楼栋单元的值
        this.form.loudong = "";
        this.form.danyuan = "";
        this.form.fanghao = "";
        this.form.building_loudong_id = "";
        this.form.building_danyuan_id = "";
        this.form.building_unit_id = "";
        this.fanghao_custom_level = 0;
        this.loudong_custom_level = 0;
        this.danyuan_custom_level = 0;
      }
      // 获取小区的楼栋
      if (!this.unsearch && !this.noGetLong) {
        this.getLoudong(val);
      }

    },
    // fanghao_custom_level(val) {
    //   if (val === 1) {
    //     this.form.loudong = ''
    //     this.form.loudongdanwei = ''
    //     this.form.danyuan = ''
    //     this.form.danyuandanwei = ''
    //     this.form.fanghao = ''
    //     this.form.building_unit_id = ''
    //   }
    //   if (val === 2) {
    //     this.form.danyuan = ''
    //     this.form.danyuandanwei = ''
    //     this.form.fanghao = ''
    //     this.form.building_unit_id = ''
    //   }
    //   if (val === 3) {
    //     this.form.fanghao = ''
    //     this.form.building_unit_id = ''
    //   }
    // },
  },
  created() {
    // console.log(this.form, 111);
    this.form.trade_type = this.house_type;
    // this.getLoudong('')
    // this.getUnits()
  },
  methods: {
    getOtherOptions(params) {
      if (params.manual != 1) {
        this.getLoudong(params.community_id);
      }

      // this.getUnit(params.loudong)
      this.getHouseNum(params.danyuan);
    },
    getUnits() {
      this.$ajax.house.getHouseUnit().then((res) => {
        if (res.data.status === 200) {
          this.loudong_units = res.data.data.loudongdanwei;
          this.danyuan_units = res.data.data.danyuandanwei;
        }
      });
    },
    onAreaChange(e) {
      this.getRange(e);
    },
    getRange(area_id) {
      if (!area_id) {
        console.log("没有区域id");
        return;
      }
      this.form_options.area_region = [];
      this.$service.lm.getRange(area_id).then((res) => {
        if (res.data.status === 200) {
          this.form_options.area_region = res.data.data.map((item) => {
            return { values: item.id, name: item.region_name };
          });
          console.log(this.form_options.area_region);
        }
      });
    },

    onRegionChange() {
      this.searchCommunity("");
    },
    searchCommunity(e) {
      if (!e) return;
      this.community_loading = true;
      this.unsearch = false;
      this.$ajax.house
        .searchCommunity(e)
        .then((res) => {
          console.log(res);
          this.community_loading = false;
          if (res.status === 200) {
            this.community_list = res.data;
            if (this.community_list.length === 0) {
              this.unsearch = true;
            }
          } else {
            this.unsearch = true;
            this.community_list = [];
          }
        })
        .catch(() => {
          this.community_loading = false;
        });
    },
    onCommunityChange(e) {
      console.log(e);
      let curr = this.community_list.find(item => item.id == e)
      if (curr) {
        if (curr.manual == 1) {
          this.noGetLong = true
          this.form.status = 2;
        } else {
          this.noGetLong = false
          this.form.status = '';
        }

      }
      // this.form.status = "";
      // this.getLoudong(e)
    },
    getLoudong(community_id) {
      this.$ajax.house.getLoudong({ community_id }).then((res) => {
        console.log(res, 11);
        if (res.status === 200) {
          this.loudong_list = res.data;
          this.loudong_list_back = res.data;
        }
      });
    },
    customerSearch(e, type = "loudong") {
      setTimeout(() => {
        let list = [];
        switch (type) {
          case "loudong":
            this.loudong_list_back.map((item) => {
              list.push(JSON.parse(JSON.stringify(item)));
            });
            list = list.filter((item) => item.name.includes(e));
            if (!list.length) {
              if (e) {
                let obj = {
                  name: e,
                  loudong: "customer_" + e,
                  value: "customer_" + e,
                };
                list.push(obj);
              }

              this.loudong_list = list;
            }
            this.loudong_list = list;

            break;
          case "danyuan":
            if (this.danyuan_list_back.length) {
              this.danyuan_list_back.map((item) => {
                list.push(JSON.parse(JSON.stringify(item)));
              });
            }
            console.log(list);
            list = list.filter((item) => item.name.includes(e));
            if (!list.length) {
              if (e) {
                let obj = {
                  name: e,
                  danyuan: "customer_" + e,
                  value: "customer_" + e,
                };
                list.push(obj);
              }
            }
            console.log(list, "ttt");
            this.danyuan_list = list;

            break;
          case "fanghao":
            if (this.huhao_list_back.length) {
              this.huhao_list_back.map((item) => {
                list.push(JSON.parse(JSON.stringify(item)));
              });
            }
            list = list.filter((item) => item.name.includes(e));
            if (!list.length) {
              //   list.map(item => {
              //     console.log(item.name.includes(e), 'qqq');
              //     if (!item.name.includes(e) && item.name != 'e') {
              //       let obj = {
              //         name: e,
              //         value: "customer_" + e
              //       }
              //       list.push(obj)
              //     }

              //   })
              // } else {
              if (e) {
                let obj = {
                  name: e,
                  value: "customer_" + e,
                };
                list.push(obj);
              }


            }
            this.huhao_list = list;

            break;

          default:
            break;
        }
      }, 200);

      // if (this.loudong_list.includes(e))
    },
    onSelectLoudong(loudong_value) {
      this.form.building_danyuan_id = "";
      this.form.building_unit_id = "";
      if ((loudong_value + "").split("_").length > 1) {
        this.danyuan_list = [];
        this.danyuan_list_back = [];
        this.huhao_list = [];
        this.show_unit = true;
        this.checkRepeat();
        return;
      }
      var _current = this.loudong_list.find(
        (item) => item.loudong === loudong_value
      );
      if (_current) {
        this.form.loudongdanwei = _current.loudongdanwei;
        this.current_loudong_id = _current.loudong;
        this.current_loudong_value = _current.value;
        this.current_loudong_name = _current.value;
        this.checkRepeat();
        if (_current.is_unit) {
          this.getUnit(_current.loudong);
          this.show_unit = true;
        } else {
          this.show_unit = false;
          this.getHouseNum();
        }
      }
    },
    getUnit(loudong_id) {
      this.$ajax.house
        .getUnit({ community_id: this.form.community_id, loudong: loudong_id })
        .then((res) => {
          if (res.status === 200) {
            this.danyuan_list = res.data;
            this.danyuan_list_back = res.data;
          }
        });
    },
    onSelectDanyuan(danyuan_value) {
      this.form.fanghao = "";
      this.form.building_unit_id = "";
      if ((danyuan_value + "").split("_").length > 1) {
        this.huhao_list_back = [];
        this.huhao_list = [];
        this.checkRepeat();
        return;
      }
      var _current = this.danyuan_list.find(
        (item) => item.danyuan === danyuan_value
      );
      if (_current) {
        this.current_danyuan_id = _current.danyuan;
        this.current_danyuan_value = _current.value;
        this.form.danyuandanwei = _current.danyuandanwei;
        this.form.total_floor = _current.floor;
        this.current_danyuan_name = _current.value;
        this.checkRepeat();
        this.getHouseNum(_current.danyuan);
      }
    },
    getHouseNum(danyuan_id = "") {
      let params = {
        community_id: this.form.community_id,
        loudong: this.current_loudong_id,
      };
      if (danyuan_id) {
        params.danyuan = danyuan_id;
      }
      this.$ajax.house.getFanghao(params).then((res) => {
        if (res.status === 200) {
          this.huhao_list = res.data;
          this.huhao_list_back = res.data;
        }
      });
    },
    checkRepeat() {
      if (
        !this.form.building_unit_id ||
        !this.form.building_loudong_id ||
        (this.show_unit && !this.form.building_danyuan_id)
      )
        return;
      let params = { community_id: this.form.community_id, manual: this.noGetLong ? 1 : 0 };
      if (this.form.building_unit_id.split("_").length == 1) {
        //  全是选择的
        params.building_unit_id = this.form.building_unit_id;
      } else {
        if (this.form.building_loudong_id.split("_").length > 1) {
          params.loudong = this.form.building_loudong_id.split("_")[1];
        } else {
          params.loudong = this.current_loudong_value;
        }
        if (this.form.building_danyuan_id.split("_").length > 1) {
          params.danyuan = this.form.building_danyuan_id.split("_")[1];
        } else {
          params.danyuan = this.current_danyuan_value;
        }
        if (this.form.building_unit_id.split("_").length > 1) {
          params.fanghao = this.form.building_unit_id.split("_")[1];
        } else {
          var _current = this.huhao_list.find(
            (item) => item.value === this.this.form.building_unit_id
          );
          if (_current) {
            params.fanghao = _current.value;
          }
        }
      }
      // if (this.fanghao_custom_level) {
      //   params = {
      //     community_id: this.form.community_id,
      //     loudong: this.form.loudong,
      //     loudongdanwei: this.form.loudongdanwei,
      //     danyuan: this.form.danyuan,
      //     danyuandanwei: this.form.danyuandanwei,
      //     menpaihao: this.form.fanghao,
      //   }
      // } else if (this.form.building_unit_id) {
      //   params = {
      //     community_id: this.form.community_id,
      //     building_unit_id: this.form.building_unit_id,
      //   }
      // }
      this.$ajax.house.checkRepeat(params).then((res) => {
        console.log(res);
        if (res.status == 200 && res.data.status == 1) {
          this.$confirm("该房源已存在 ", "提示", {
            confirmButtonText: "立即查看",
            cancelButtonText: "重新录入",
            type: "warning",
          })
            .then(() => {
              this.$goPath("house_detail?id=" + res.data.id);
            })
            .catch(() => {
              this.form.building_unit_id = "";
            });
          // this.$message.error('该房源已存在 请重新选择')
          // this.form.building_unit_id = ''
        }

        // if (res.data.status === 200) {
        //   this.$message.success(res.data.message)
        // }
        // if (res.data.status === 209) {
        //   this.$confirm('该房源已经存在，您可以去跟进联系', '提示', {
        //     confirmButtonText: '去跟进',
        //     cancelButtonText: this.form.exclusive
        //       ? '取消独家'
        //       : this.form.is_repeat
        //         ? '取消查重'
        //         : '取消',
        //     type: 'warning',
        //   })
        //     .then(() => {
        //       this.form.exclusive = 0
        //       this.form.is_repeat = ''
        //       // console.log(this.$parent)
        //       this.$parent.current_info_id = res.data.data.id
        //       this.$parent.show_detail = true
        //       // this.$router.push({
        //       //   name: 'LmList',
        //       //   query: {
        //       //     id: res.data.data.id,
        //       //   },
        //       // })
        //       console.log('去详情页')
        //     })
        //     .catch(() => {
        //       // 取消则恢复成非独家且不查重查重
        //       this.form.exclusive = 0
        //       this.form.is_repeat = ''
        //       console.log('已取消')
        //     })
        // } else if (res.data.status !== 200) {
        //   this.form.exclusive = ''
        //   this.form.is_repeat = ''
        // } else if (res.data.status === 200) {
        //   this.form.is_repeat = 1
        // }
      });
    },
    showMap() {
      this.show_map = true;
      if (this.qqmap) {
        return;
      }
      // eventBus.$on('qqMapOnLoad', () => {
      //   this.initMap()
      // })
      // this.$nextTick(() => {
      //   loadJs(
      //     `https://map.qq.com/api/js?v=2.exp&&key=${this.mapkey}&callback=qqMapOnLoad&libraries=place`,
      //     'qq_map'
      //   )
      // })
    },
    // onMapOk() {
    //   this.show_map = false
    //   this.destroyMap()
    //   this.form.lat = this.point_marker.lat
    //   this.form.lng = this.point_marker.lng
    //   this.form.address = this.point_marker.address
    //   this.map_address = this.point_marker.address
    // },
    // onMapCancel() {
    //   this.show_map = false
    //   this.destroyMap()
    // },
    // destroyMap() {
    //   this.qqmap = null
    // },
    // // 初始化地图
    // initMap() {
    //   if (this.qqmap) {
    //     return
    //   }
    //   let center
    //   if (this.form.lat && this.form.lng) {
    //     center = new qq.maps.LatLng(
    //       parseFloat(this.form.lat),
    //       parseFloat(this.form.lng)
    //     )
    //   } else {
    //     // let latLng = this.config.centermappoint.split(',')
    //     let center_lat = 35.086867,
    //       center_lng = 117.161747
    //     // if (latLng.length > 1) {
    //     //   center_lat = latLng[0]
    //     //   center_lng = latLng[1]
    //     // }
    //     center = new qq.maps.LatLng(center_lat, center_lng)
    //   }
    //   this.qqmap = new qq.maps.Map(this.$refs.qqmap, {
    //     zoom: 14, // 设置地图缩放级别
    //     center: center, // 设置地图中心点坐标
    //   })

    //   if (this.form.lat && this.form.lng) {
    //     this.createMarker(parseFloat(this.form.lat), parseFloat(this.form.lng))
    //   }
    //   qq.maps.event.addListener(this.qqmap, 'click', async (evt) => {
    //     this.removeMarker()
    //     this.point_marker.lat = evt.latLng.getLat().toFixed(6)
    //     this.point_marker.lng = evt.latLng.getLng().toFixed(6)
    //     this.createMarker(this.point_marker.lat, this.point_marker.lng)
    //     var res = await this.getLatLngAddress(
    //       this.point_marker.lat,
    //       this.point_marker.lng
    //     )
    //     this.point_marker.address = res.address
    //   })
    // },
    async getLatLngAddress(lat, lng) {
      var res = await this.$jsonp("https://apis.map.qq.com/ws/geocoder/v1/", {
        location: `${lat},${lng}`,
        key: this.mapkey,
        get_poi: 1,
        output: "jsonp",
      });
      if (res.status === 0 && res.result) {
        return res.result;
      }
    },
    // 创建地图标记点
    // createMarker(lat, lng) {
    //   var anchor = new qq.maps.Point(12, 32),
    //     size = new qq.maps.Size(24, 32),
    //     origin = new qq.maps.Point(0, 0)
    //   this.marker = new qq.maps.Marker({
    //     position: new qq.maps.LatLng(lat, lng),
    //     map: this.qqmap,
    //     icon: new qq.maps.MarkerImage(
    //       require('@/assets/icon/marker.png'),
    //       size,
    //       origin,
    //       anchor,
    //       new qq.maps.Size(24, 32)
    //     ),
    //   })
    // },
    // 删除地图标记点
    removeMarker() {
      if (this.marker) {
        this.marker.setMap(null);
      }
    },
    // onSelectMapRes(item) {
    //   this.map_search_list = []
    //   this.qqmap.panTo(new qq.maps.LatLng(item.location.lat, item.location.lng))
    //   this.removeMarker()
    //   this.createMarker(item.location.lat, item.location.lng)
    //   this.point_marker.lat = item.location.lat
    //   this.point_marker.lng = item.location.lng
    //   this.point_marker.address = item.address
    // },
    // onMapSearchChange() {
    //   this.$jsonp('https://apis.map.qq.com/ws/place/v1/suggestion', {
    //     keyword: this.search_key,
    //     region: '',
    //     page_size: 20,
    //     key: this.mapkey,
    //     output: 'jsonp',
    //   }).then((res) => {
    //     console.log(res)
    //     this.map_search_list = res.data
    //   })
    // },
    onUploadLetterSuccess(response) {
      this.up_loading5 = false;
      this.form.exclusive_voucher = response.url;
    },
    onUploadLetterBefore(e) {
      this.up_loading5 = true;
      console.log(e);
    },
    beforeUpload(e, type) {
      this["up_loading" + type] = true;
    },
    onUploadSuccess(options) {
      let { response, category, descp } = options;
      this.up_loading1 = false;
      this.up_loading2 = false;
      this.up_loading3 = false;
      this.up_loading4 = false;
      let img = {
        category_id: category,
        descp: descp || "",
        is_cover: 0,
        url: response.url,
      };
      this.form.pic.push(img);
      switch (category) {
        case 1:
          this.indoor_imgs.push(img);
          break;
        case 2:
          this.plans_imgs.push(img);
          break;
        case 3:
          this.outdoor_imgs.push(img);
          break;
        default:
          this.other_imgs.push(img);
          break;
      }
      console.log(this.form.pic);
    },
    setCover(e) {
      this.has_set_cover = true;
      this.form.pic.map((item) => {
        if (item.url === e.url) {
          item.is_cover = 1;
        } else {
          item.is_cover = 0;
        }
      });
      this.indoor_imgs.forEach((item) => {
        if (item.url === e.url) {
          item.is_cover = 1;
        } else {
          item.is_cover = 0;
        }
      });

      this.plans_imgs.forEach((item) => {
        if (item.url === e.url) {
          item.is_cover = 1;
        } else {
          item.is_cover = 0;
        }
      });

      this.outdoor_imgs.forEach((item) => {
        if (item.url === e.url) {
          item.is_cover = 1;
        } else {
          item.is_cover = 0;
        }
      });
      this.other_imgs.forEach((item) => {
        if (item.url === e.url) {
          item.is_cover = 1;
        } else {
          item.is_cover = 0;
        }
      });
    },
    onRemoveImage(e, index, category) {
      var url = "";
      if (e.response) {
        url = e.response.data;
      } else if (e.url) {
        url = e.url;
      }
      this.form.pic = this.form.pic
        .map((item) => {
          if (item._id && item.url === url) {
            item.is_deleted = 1;
          }
          return item;
        })
        .filter((item) => item._id || item.url !== url);
      switch (category) {
        case 1:
          this.indoor_imgs = this.indoor_imgs.filter(
            (item) => item.url !== url
          );
          break;
        case 2:
          this.plans_imgs = this.plans_imgs.filter((item) => item.url !== url);
          break;
        case 3:
          this.outdoor_imgs = this.outdoor_imgs.filter(
            (item) => item.url !== url
          );
          break;
        default:
          this.other_imgs = this.other_imgs.filter((item) => item.url !== url);
          break;
      }
      // console.log(this.form.pic)
    },
    uploadError() {
      this.up_loading1 = false;
      this.up_loading2 = false;
      this.up_loading3 = false;
      this.up_loading4 = false;
      this.$message.error("上传失败");
    },
    showApplyCommunity() {
      this.show_apply_community = true;
    },
    onApplyCommunitySuccess(e) {
      this.show_apply_community = false;
      this.community_list.unshift({
        id: e.id,
        title: e.title,
        is_apply: true,
      });
      this.form.community_id = e.id;
      this.form.status = 2;
    },
    getCopyData(str) {
      this.$ajax.house.houseAddMatching(str).then((res) => {
        console.log(res);
        let data = {};
        if (res.status == 200) {
          data = res.data;
          for (const key in data) {
            if (key == "community_name") {
              this.form.community_id = data.community_id;
              this.community_list = [
                {
                  id: data.community_id,
                  title: data.community_name,
                },
              ];
            } else if (key != "community_is_verify") {
              this.form[key] = data[key];
            }
          }
          if (this.form.sale_price) {
            this.form.sale_price = +this.form.sale_price / 10000;
          }
          // this.community_name = data.name
          // this.form.community_id =data.community_id
        }
      });
      // var community_name = str.slice(0, 4)
      // if (community_name) {
      //   this.searchAreaName(community_name, () => {
      //     this.form.buildid = this.houseList[0].id
      //     this.community_name = this.houseList[0].name
      //     this.form.lat = this.houseList[0].lat
      //     this.form.lng = this.houseList[0].lng
      //     this.form.address = this.houseList[0].address
      //     this.form.areaid = this.houseList[0].areaid
      //   })
      // }
      // var _this = this
      // console.log(discernHouse)
      // var reg_temp_list = [
      //   discernHouse.mianji_reg,
      //   discernHouse.sale_price_reg,
      //   discernHouse.rent_price_reg,
      //   discernHouse.shi_reg,
      //   discernHouse.ting_reg,
      //   discernHouse.kitchen_reg,
      //   discernHouse.wei_reg,
      //   discernHouse.floor_reg,
      //   discernHouse.total_floor_reg,
      //   discernHouse.tel_reg,
      // ]
      // let reg_list = []
      // if (this.form.trade_type === 1) {
      //   reg_list = reg_temp_list.filter((item) => item.key !== 'rent_price')
      // } else if (this.form.trade_type === 2) {
      //   reg_list = reg_temp_list.filter((item) => item.key !== 'sale_price')
      // } else {
      //   reg_list = Array.from(reg_temp_list)
      // }
      // reg_list.forEach((item) => {
      //   str.replace(item.reg, function () {
      //     var value
      //     switch (item.key) {
      //       case 'sale_price':
      //         value =
      //           Array.prototype.slice.call(arguments)[item.value_index] * 10000
      //         break
      //       default:
      //         value = Array.prototype.slice.call(arguments)[item.value_index]
      //         break
      //     }
      //     if (value === undefined) {
      //       return
      //     }
      //     if (!isNaN(value - 0)) {
      //       _this.form[item.key] = value - 0
      //     } else {
      //       _this.form[item.key] = discernHouse.zh_num.find(
      //         (item) => item.name === value
      //       ).value
      //     }
      //   })
      // })
      // this.form.content = str
      //   .replace(discernHouse.tel_reg.reg, '')
      //   .replace(/(联系电话|手机号|电话)：?/, '')
      // if (this.form.shi) {
      //   this.huxing_value = `${this.form.shi}-${this.form.ting}-${this.form.wei}`
      // }
    },
    showSelectTable() {
      this.show_select_table = true;
    },
    clickLoudong(item) {
      this.form.building_loudong_id = item.loudong;
      this.current_loudong_name = item.value;
      // this.se_loudong_name = item.name
      this.danyuan_list = [];
      this.louceng_list = [];
      this.huhao_list = [];
      if (item.is_unit) {
        this.show_unit = true;
        this.getUnit(item.loudong);
      } else {
        this.show_unit = false;
        this.show_louceng = false;
        this.getFanghaoList();
      }
    },
    getLouceng(id) {
      this.$ajax.house
        .getLouceng({
          community_id: this.form.community_id,
          loudong: this.form.building_loudong_id,
          danyuan: id,
        })
        .then((res) => {
          if (res.status == 200) {
            this.louceng_list = res.data;
          }
        });
    },
    clickUnit(item) {
      this.form.building_danyuan_id = item.danyuan;
      this.current_danyuan_name = item.value;
      this.form.total_floor = item.floor;
      // this.se_danyuan_name = item.name
      this.louceng_list = [];
      this.huhao_list = [];
      if (item.floor > 0) {
        this.getLouceng(item.danyuan);
        this.show_louceng = true;
      } else {
        this.show_louceng = false;
        this.getFanghaoList();
      }
    },
    clickLouceng(item) {
      this.se_louceng = item.value;
      // this.se_louceng_name = item.name
      // this.louceng_list = []
      this.huhao_list = [];
      this.getFanghaoList(item.value);
    },
    getFanghaoList(id) {
      this.$ajax.house
        .getFanghaoList({
          community_id: this.form.community_id,
          loudong: this.form.building_loudong_id,
          danyuan: this.form.building_danyuan_id,
          floor: id,
        })
        .then((res) => {
          if (res.status == 200) {
            this.huhao_list = res.data;
          }
        });
    },
    selectFloor(item) {
      if (item.trade_type > 0) {
        this.$confirm("该房源已存在 ", "提示", {
          confirmButtonText: "立即查看",
          cancelButtonText: "重新录入",
          type: "warning",
        })
          .then(() => {
            this.show_select_table = false;
            this.$goPath("house_detail?id=" + item.house_id);
          })
          .catch(() => {
            this.se_floor = "";
            // this.form.building_unit_id = ''
          });
        return;
      }
      // this.building_unit_id = item.value
      this.form.building_unit_id = item.value;
      // this.form.building_danyuan_id = this.se_danyuan
      // this.form.building_loudong_id = this.se_loudong
      this.show_select_table = false;
    },
    handleSuccess(e) {
      this.community_list = [e]
      this.form.community_id = e.id
      this.form.status = 2;
      this.show_apply_community = false
    },
    // 通过父组件调用：清除标签数据
    clearChildData() {
      console.log("我是子组件");
      // 清除已选择单元
      this.form.building_danyuan_id = "";
      // 清除已选择户号
      this.form.building_unit_id = "";
      // 是否显示单元
      this.show_unit = true;
      this.$refs.form.resetFields();
    },
  }
  // destroyed() {
  //   eventBus.$off('qqMapOnLoad')
  // },
};
</script>

<style scoped lang="scss">
.el-form {
  position: relative;
}

.form_block {
  >.title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;
  }

  .el-form-item {
    color: #8a929f;

    .tip {
      color: #fe6c17;
    }

    .upload-box {
      overflow: hidden;
      margin: 0 8px 8px 0;
      display: inline-block;
      vertical-align: middle;
    }

    ::v-deep>.el-input {
      width: 240px;
    }

    ::v-deep.el-form-item__label {
      font-weight: bold;
      color: #2e3c4e;
    }

    .img_item {
      margin: 0 8px 8px 0;
      display: inline-block;
      vertical-align: top;
      line-height: 1;
    }

    ::v-deep .el-upload-list__item {
      width: 120px;
      height: 120px;
      position: relative;
      margin: 0;

      .remark_img {
        padding: 0 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        height: 28px;
        width: 80%;
        border-radius: 6px;
        color: #fff;
        background-color: rgba($color: #000000, $alpha: 0.5);
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;

        .el-input {
          .el-input__inner {
            padding: 0 3px;
            background: none;
            border: none;
            color: #fff;
          }
        }
      }

      &:hover {
        .remark_img {
          opacity: 1;
        }
      }
    }

    .el-upload-list--picture-card {
      display: block;

      .el-upload-list__item-thumbnail {
        object-fit: cover;
      }
    }

    ::v-deep.el-upload {
      width: 120px;
      height: 120px;
      line-height: 1.5;

      .upload_placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        color: #2d84fb;

        i {
          color: #2d84fb;
        }
      }

      .upload_image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    ::v-deep.el-textarea textarea {
      resize: none;
    }
  }
}

.img_options {
  display: flex;
  justify-content: space-between;

  >span {
    padding: 10px 0;
    cursor: pointer;
  }

  .set_cover {
    color: #2d84fb;
  }
}

.add_community {
  color: #2d84fb;
}

.map-box {
  position: relative;

  .map-container {
    height: 500px;
  }

  .res_list {
    position: absolute;
    width: 100%;
    top: 40px;
    max-height: 400px;
    overflow-x: hidden;
    background-color: #fff;
    z-index: 10;
    box-shadow: 0 5px 5px #dedede;
    padding: 5px 0;

    .res_item {
      cursor: pointer;
      padding: 8px 5px;
      border-bottom: 1px solid #dedede;

      .title {
        font-size: 16px;
        color: #333;
      }

      .address {
        color: #666;
      }
    }
  }
}

.form-item-block {
  display: inline-block;
  vertical-align: top;

  .tip {
    padding-left: 10px;
    margin-top: 5px;
    line-height: 1;
  }
}

.el-form-item {
  color: #8a929f;

  .tip {
    color: #fe6c17;
  }

  .unit {
    margin-left: 12px;
  }

  ::v-deep .el-form-item__label {
    font-weight: bold;
    color: #2e3c4e;
  }

  // ::v-deep .el-input {
  //   width: 240px;
  // }
  ::v-deep .el-textarea textarea {
    resize: none;
  }

  .el-date-editor {
    width: 240px;
    // &:first-child {
    //   margin-right: 6px;
    //   width: 130px;
    // }
  }
}

.copy {
  display: inline-block;
  vertical-align: top;
  position: absolute;
  top: 48px;
  left: 700px;
  margin-left: 50px;
}

.tips {
  padding: 10px;
  background: #e7f3fd;
  border-radius: 10px;
  line-height: 25px;
}

.sel_container {
  .top {
    padding: 10px 0;
  }

  .top_right {
    padding: 8px;
  }

  .sale_b {
    background: #fda148;
    border-radius: 4px;
    color: #fff;
    padding: 5px 15px;
    margin-right: 10px;
  }

  .rent_b {
    background: #f74c4c;
    border-radius: 4px;
    color: #fff;
    padding: 5px 15px;
    margin-right: 10px;
  }

  .sale_rent_b {
    background: #1cd300;
    border-radius: 4px;
    color: #fff;
    padding: 5px 15px;
    margin-right: 10px;
  }
}

.select_box {
  .se_ld {
    text-align: center;

    border: 1px solid #e6e7ea;

    .se_container {
      height: 500px;
      min-width: 117px;
      background: #ffffff;
      overflow-y: auto;
      overflow-x: hidden;

      &.se_th {
        height: 39px;
      }

      .se_box {
        position: relative;
        background: #fff;
        height: 100%;
        width: 100px;
        box-sizing: border-box;

        .shangsanjiao {
          height: 0;
          width: 0;
          z-index: 1;
          position: absolute;
          top: 3px;
          right: -14px;
          border: 5px solid transparent;
          border-bottom: 5px solid #a1a1a1;
        }

        .xiasanjiao {
          height: 0;
          width: 0;
          z-index: 1;
          position: absolute;
          bottom: 3px;
          right: -14px;
          border: 5px solid transparent;
          border-top: 5px solid #a1a1a1;
        }
      }
    }

    .se_tr {
      &.fanghao {
        .se_box {
          width: 100%;
          flex-wrap: wrap;

          .se_item {
            margin: 6px 12px;
            padding: 10px 20px;

            &.sale {
              background: #fda1481a;
              color: #fda148;
            }

            &.rent {
              background: #f74c4c1a;
              color: #f74c4c;
            }

            &.sale_rent {
              background: #1cd3001a;
              color: #1cd300;
            }
          }

          // justify-content: space-between;
        }
      }

      .se_box {
        padding: 6px 12px;

        .se_item {
          background: rgba(27, 93, 255, 0.1);
          border-radius: 4px;
          border-radius: 4px;
          color: #1b5dff;
          margin: 6px 0;

          &.active {
            background: #1b5dff;
            box-shadow: 2px 0px 6px 0px rgba(35, 90, 223, 0.4);
            color: #fff;
          }
        }
      }
    }

    +.se_ld {
      border-left: 0;
    }

    .row {
      // 横排
      padding: 10px;
      flex-wrap: wrap;

      .se_item {

        // &:nth-child(2n) {
        //   background: #fff;
        // }
        &.active {
          // background: #fff !important;
          border: 1px solid #bdcfe6;
          border-radius: 4px;
        }
      }
    }

    .se_item {
      padding: 10px;
      z-index: 2;
      cursor: pointer;

      &.se_th {
        background: #f8f8f9;
        box-sizing: border-box;

        .se_container {
          height: 39px;
        }
      }

      &.active {
        // background: #c8e9ff !important;
      }

      &:nth-child(2n) {
        background: #f8f8f9;
      }
    }
  }
}

::v-deep .el-link.is-underline:hover:after {
  content: none;
}
</style>