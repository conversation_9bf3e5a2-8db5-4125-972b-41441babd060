<template>
    <div class="page" v-fixed-scroll="62">
        <div>
          <div class="Header">报备</div>
        </div>
        <div class="pagecontent">
          <div class="tablestyle">
            <div class="Statusretrieval">
              <!-- 状态检索 -->
                <el-select size="small" style="width: 130px;" v-model="params.status" placeholder="请选择状态"
                @change="statuschange" clearable>
                  <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id">
                  </el-option>
                </el-select>
                <template>
                   <!-- 时间检索(创建时间) -->
                <el-date-picker class="margin" style="width: 350px" v-model="timeValue" type="datetimerange" size="small" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                  value-format="yyyy-MM-dd HH:mm:ss" @change="onChangeTime">
                </el-date-picker>
              <!-- 手机号筛选 -->
                <el-input placeholder="请输入手机号" size="small" style="width: 180px;" v-model="params.mobile" class="input-with-select">
                  <el-button slot="append" icon="el-icon-search" @click="statuschange"></el-button>
                </el-input>
              <!-- 带看状态 -->
                <el-select size="small" class="margin" style="width: 130px;" v-model="params.task_status" placeholder="带看状态"
                  @change="statuschange" >
                    <el-option
                      v-for="item in lookoptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id">
                    </el-option>
                </el-select>
              <!-- 客户名称筛选 -->
                <el-input placeholder="请输入客户名称" size="small" style="width: 200px;" v-model="params.cname" class="input-with-select">
                  <el-button slot="append" icon="el-icon-search" @click="statuschange"></el-button>
                </el-input>
                </template>
             
            </div>
            <el-table
                v-loading="is_table_loading"
              :data="tableData"
              border
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }">
              <el-table-column
                prop="visit_time"
                label="报备日期"
                width="130"
                fixed>
              </el-table-column>
              <el-table-column
                prop="cname"
                label="客户名称"
                width="180"
                v-slot="{ row }">
                <span style="white-space: nowrap">{{row.cname?row.cname:"--"}}</span>
              </el-table-column>
              <el-table-column
                prop="mobile"
                label="客户手机号"
                width="180"
                v-slot="{ row }">
                  <span
                    style="cursor: pointer"
                    @click="togglePhoneNumberDisplay(row)">
                    {{ row.displayedMobile }}
                  </span>
              </el-table-column>
              <el-table-column
                prop="report_project"
                label="项目名称"
                width="220"
                v-slot="{ row }">
                <span class="textcolor">{{row.report_project}}</span>
              </el-table-column>
              <el-table-column
                prop="visit_type"
                label="带访时间"
                v-slot="{ row }">
                {{row.visit_type==1?"上午":row.visit_type==2?"下午":row.visit_type==3?"晚上":"--"}}
              </el-table-column>
              <el-table-column
                prop="admin"
                label="经纪人"
                v-slot="{ row }">
                {{row.admin.user_name?row.admin.user_name:"--"}}
              </el-table-column>
              <el-table-column
                prop="status"
                label="状态"
                v-slot="{ row }">
                {{row.status==0?"已抄送":row.status==1?"已报备":row.status==2?"已驳回":row.status==3?"已到访":row.status==4?"未到访":"--"}}
              </el-table-column>
              <el-table-column
                prop="receipt_time"
                label="回执"
                v-slot="{ row }">
                {{row.receipt_time?"已回执":"未回执"}}
              </el-table-column>
              <el-table-column
                prop="take_status"
                label="带看记录"
                v-slot="{ row }">
                {{row.take_status==1?"未带看":row.take_status==2?"已带看":row.take_status==3?"有复看":"--"}}
              </el-table-column>
              <el-table-column
                prop="remarks"
                label="备注信息"
                v-slot="{ row }">
                {{row.remarks?row.remarks:"--"}}
              </el-table-column>
              <el-table-column
                prop="created_at"
                label="创建时间"
                width="200">
              </el-table-column>
              <el-table-column label="渠道专员"
              width="120">
                <template slot-scope="scope">
                  <span v-for="(item, index) in scope.row.channel" :key="index">
                    {{ item.user_name }}
                    <span v-if="index !== scope.row.channel.length - 1">, </span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                label="客户"
                fixed="right"
                width="120"
                v-slot="{ row }"> 
                <el-link type="warning" style="margin-right:10px;" v-if="row.status==0" @click="addreminder(row)">提醒</el-link>
                <el-link type="primary" :underline="false" @click="onClickDetail(row)">详情</el-link>
              </el-table-column>
            </el-table> 
            <div class="page_footer flex-row items-center">
              <div class="page_footer_l flex-row flex-1 items-center">
                <div class="head-list">
                  <el-button type="primary" size="small" @click="empty">清空</el-button>
                </div>
                <div class="head-list">
                  <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                </div>
              </div>
              <div>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="paramsdata.per_page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="paramsdata.total">
                  </el-pagination>
              </div>
            </div>
          </div>
            
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            tableData:[],//报备列表数据
            params:{
                per_page:10,
                page:1,
                status:"",
            },//请求字段
            paramsdata:{
                per_page: 10,
            },//分页
            is_table_loading:false,
            options:[
                {id:0,label:"已抄送"},
                {id:1,label:"已报备"},
                {id:2,label:"已驳回"},
                {id:3,label:"已到访"},
                {id:4,label:"未到访"},
            ],//状态检索   状态(0:已抄送,1:已报备,2:已驳回,3:已到访,4:未到访)
            lookoptions:[
              {id:0,label:"全部"},
              {id:1,label:"未带看"},
              {id:2,label:"已带看"},
              {id:3,label:"有复看"},
            ],//带看状态 (0:全部,1:未带看,2:已带看,3:有复看)
            timeValue:"",
            pickerOptions: {
              shortcuts: [{
                text: '今天',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '昨天',
                onClick(picker) {
                  const end = new Date();
                  end.setHours(0, 0, 0, 0);
                  const start = new Date(end);
                  start.setDate(start.getDate() - 1);
                  end.setDate(end.getDate() - 1);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本周',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上周',
                onClick(picker) {
                    const today = new Date(); // 获取当前日期
                    const end = new Date(today); // 结束日期为当前日期
                    const start = new Date(today); // 开始日期为当前日期
                    const day = today.getDay(); // 获取当前是星期几
                    const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
                    const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
                    end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
                    end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
                    start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
                    start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
                    // 将计算得到的时间范围传递给日期选择器
                    picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上月',
                onClick(picker) {
                  const end = new Date();
                  end.setDate(0); // 获取上个月的最后一天
                  end.setHours(23, 59, 59, 0);
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              },]
            },
            website_id:""
        }
    },
    created(){
       // 赋值website_id
      if (this.$route.query.website_id) {
        this.website_id = this.$route.query.website_id;
      }
    },
    mounted(){
        this.getdatalist()
    },
    methods:{
        togglePhoneNumberDisplay(row) {
          row.displayedMobile = row.mobile; // 更新displayedMobile属性
          this.tableData = [...this.tableData];//对整个数据进行重新赋值
          // this.$forceUpdate(); // 强制更新视图
        },
        getdatalist(){
            if(this.params.status!==0){
                if(this.params.status==""){
                    delete this.params.status
                }
            }
            // if(!this.params.task_status){
            //   this.params.task_status = 0
            // }
            this.is_table_loading = true
            this.$http.getreportingrecordslist(this.params).then((res)=>{
                if(res.status==200){
                    this.tableData = res.data.data
                    this.tableData.forEach(item => {
                      item.displayedMobile = item.mobile.substr(0, 3) + '*****' + item.mobile.substr(-3);
                    });
                    this.is_table_loading = false
                    this.paramsdata.page = res.data.per_page;
                    this.paramsdata.total = res.data.total;
                }
            })
        },
        //按时间筛选
        onChangeTime(e){
          if (e && e.length >= 2) {
            if (e[1].endsWith("00:00:00")) {
              e[1] = e[1].slice(0, -8) + "23:59:59";
            }
            this.params.start_date = e ? e[0] : ""; // 赋值开始时间
            this.params.end_date = e ? e[1] : ""; // 赋值结束时间
          } else {
            this.params.start_date = ""; // 赋值开始时间
            this.params.end_date = ""; // 赋值结束时间
          }
          this.params.page = 1; // 显示第一页
          this.getdatalist(); // 获取最新数据
        },
        //每页 条
        handleSizeChange(val){
            this.params.per_page = val
            this.getdatalist()
        },
        //当前页
        handleCurrentChange(val) {
            this.params.page = val
            this.getdatalist()
        },
        //清空
        empty(){
            this.params={
                per_page:10,
                page:1,
                status:"",
            }
            this.timeValue=""
            this.getdatalist()
        },
        //刷新
        Refresh(){
            this.getdatalist()
        },
        //条件筛选
        statuschange(){
          this.params.page = 1; // 显示第一页
            this.getdatalist()
        },
        //跳转详情
        onClickDetail(row) {
          console.log(row);
          let url = `/crm_customer_detail?id=${row.client_id}&type=seas`;
          this.$goPath(url);
        },
        //添加提醒
        addreminder(row){
          let params = {
            ids:row.id.toString() 
          }
          console.log(typeof(params.ids));
          this.$confirm('此操作将会发送报备提醒, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.sendreportreminder(params).then(res=>{
              if(res.status==200){
                this.$message({
                  type: 'success',
                  message: '发送成功!'
                });
              }
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作'
            });          
          });
        },
    },
}
</script>
<style lang="scss" scoped>
.page{
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 20px 24px 80px;
    .Header{
      width: 110px;
      height: 40px;
      background-color: #ffffff;
      text-align: center;
      line-height: 42px;
      color: #8a929f;
    }
    .pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .tablestyle{
            width: 97%;
            margin: 0 auto;
            margin-top: 20px;
            margin-bottom: 30px;
            .block{
                margin-top: 20px;
                margin-bottom: 20px;
                display: flex;
                justify-content: flex-end;
            }
            .textcolor{
              white-space: nowrap;
              color:#409EFF;
            }
        }
        .Statusretrieval{
            margin-bottom: 20px;
        }
    }
  .margin{
    margin: 0px 10px 0px 10px ;
  }
}
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
</style>