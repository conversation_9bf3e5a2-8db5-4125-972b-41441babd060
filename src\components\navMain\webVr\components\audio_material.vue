<template>
    <div>
        <el-table
            :data="forData_list"
            border
            style="width: 100%">
            <el-table-column
                prop="pk_media_res"
                label="素材id">
            </el-table-column>
            <el-table-column
                prop="media_name"
                label="素材名称">
            </el-table-column>
            <el-table-column
                prop="create_time"
                label="添加时间">
            </el-table-column>
            <el-table-column
                prop="media_size"
                label="素材大小"
                v-slot="{ row }">
                <template>
                    {{ (row.media_size / 1024).toFixed(1) }}MB
                </template>
            </el-table-column>
            <el-table-column
                prop="media_suffix"
                label="素材后缀">
            </el-table-column>
            <el-table-column
            label="操作" v-slot="{ row }">
            <el-link
                type="info"
                @click="onCopyVideo(row)">
                复制
            </el-link>
            <el-link
                type="success"
                style="margin-left: 12px"
                @click.prevent.stop="onPreview(row)">
                预览
            </el-link>
            <el-link
                type="primary"
                style="margin-left: 12px"
                @click="materialRename(row)">
                重命名
            </el-link>
            <el-link
                type="danger"
                style="margin-left: 12px"
                @click="deleteMaterial(row)">
                删除
            </el-link>
            </el-table-column>
        </el-table>
        <el-dialog
            title="素材重命名"
            :visible.sync="dialogMaterialRename"
            width="680px"
        >
            <el-form :model="params_rename" label-width="80px">
                <el-form-item label="素材名称">
                    <el-input v-model="params_rename.media_name"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer Rename-footer">
                <el-button @click="dialogMaterialRename = false">取 消</el-button>
                <el-button type="primary" @click="confirmRename">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 音频播放 -->
        <div class="audio_bg" v-show="audios_bg">
            <audio
                controls
                :src="dialogAudio">
            </audio>
            <div class="black_bg"></div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        forData_list: {
            type: Array,
            default: () => {}
        }
    },
    data() {
        return {
            // 素材重命名参数
            params_rename: {
                media_name: "", // 素材名称
                id: "", // 素材id
            },
            dialogAudio: "",
            dialogMaterialRename: false, // 素材重命名模态框
            audios_bg: false,
        }
    },
    watch: {
        audios_bg: {
            handler(newval) {
                if(newval) {
                    document.body.addEventListener('click', this.eventPreview);
                } else {
                    document.body.removeEventListener('click', this.eventPreview);
                }
            }
        }
    },
    methods: {
        // 素材预览
        onPreview(row) {
            this.dialogAudio = row.absolutelocation;
            this.audios_bg = true;
        },
        // 素材复制
        onCopyVideo(row) {
            this.$onCopyValue(row.absolutelocation)
        },
        // 监听素材预览点击事件
        eventPreview() {
            this.audios_bg = false;
        },
        // 素材重命名
        materialRename(row) {
            this.dialogMaterialRename = true;
            // 赋值当前重命名的素材id
            this.params_rename.id = row.pk_media_res;
            this.params_rename.media_name = row.media_name;
        },
        // 确定将素材重命名
        confirmRename() {
            this.$http.editMaterialRename(this.params_rename).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    });
                    this.dialogMaterialRename = false;
                    // this.getDataList();
                    this.$emit("materialList", "");
                }
            })
        },
        // 删除素材
        deleteMaterial(row) {
            this.$confirm('此操作将永久删除该素材, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.deleteMaterial(row.pk_media_res).then((res) => {
                    if(res.status == 200) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.$emit("materialList", "");
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.audio_bg {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 999;
    .black_bg {
        box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
    }
}
</style>