<template>
  <div class="list">
    <el-container>
      <el-header class="div row">
        <myTopTips title="公司列表" :number="tableData.length">
          <div class="add-build" v-if="$hasShow('添加项目公司')">
            <el-button type="primary" @click="addData" icon="el-icon-plus"
              >添加公司</el-button
            >
          </div></myTopTips
        >

        <div class="div row">
          <el-input
            v-model="params.name"
            placeholder="请输入公司名称"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>
      <myTable
        v-loading="is_table_loading"
        :table-list="tableData"
        :header="table_header"
      ></myTable>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :pagesize="params.per_page"
            :currentPage="params.page"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-container>
    <el-dialog title="修改密码" :visible.sync="show_edit_password">
      <el-form ref="form_password" :model="form_password">
        <el-form-item
          label="请输入新密码"
          label-width="200px"
          prop="new_password"
        >
          <el-input
            v-model="form_password.password"
            type="password "
            placeholder="请输入新密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="show_edit_password = false">取 消</el-button>
        <el-button type="primary" @click="confirmEditPasword">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "project_company_list",
  components: { myPagination, myTable },
  data() {
    return {
      // 搜索框数据
      input: "",
      tableData: [],
      // 存放列表图片
      imgbox: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
        category: 1,
      },
      form_password: {
        id: '',
        password: ''
      },
      table_header: [
        { prop: "id", label: "ID", width: "80" },
        { prop: "name", label: "公司名称" },
        {
          label: "标识",
          render: (h, data) => {
            return (
              <el-popover width="500px" trigger="hover" placement="right">
                {data.row.logo ? (
                  <el-image
                    style="width:300px;height:300px"
                    fit="contain"
                    src={this.$imageFilter(
                      data.row.logo || "https://img.tfcs.cn/static/img/que.jpg",
                      "w_240"
                    )}
                  ></el-image>
                ) : (
                  ""
                )}
                <img
                  slot="reference"
                  src={this.$imageFilter(
                    data.row.logo || "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                  style="max-height:50px;max-width:100px"
                />
              </el-popover>
            );
          },
        },
        { prop: "full_address", label: "详细地址" },
        { prop: "updated_at", label: "添加时间" },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改项目公司") ? (
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="success"
                    style="border-radius:5px"
                    onClick={() => {
                      this.updataCompany(0, data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.is_open_company_platform ? (
                  <el-button

                    size="mini"
                    type="warning"
                    style="border-radius:5px"
                    onClick={() => {
                      this.editPassword(data.row);
                    }}
                  >
                    修改密码
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除项目公司") ? (
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    style="border-radius:5px"
                    onClick={() => {
                      this.handleDelete(0, data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
      show_edit_password: false
    };
  },
  created() { },
  mounted() {
    this.getDataList();
  },
  computed: {
    is_open_company_platform() {
      return this.$store.state.website_info.is_open_company_platform
    }

  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.$http.showCompanyLists({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    // 搜索楼盘
    search() {
      this.params.page = 1;
      this.getDataList();
      // this.$http.searchCompany(this.input).then((res) => {
      // 	if (res.status === 200) {
      // 		this.tableData = res.data.data;
      // 	}
      // });
    },
    // 搜索下拉
    handleCommand(command) {
      this.$message(command);
    },
    // 点击进入添加楼盘界面
    addData() {
      this.$goPath("/upload_company?company_category=1");
    },
    // 操作
    editPassword(row) {
      this.form_password.id = row.id
      this.form_password.password = ''
      this.show_edit_password = true
    },
    confirmEditPasword() {
      if (!this.form_password.password) {
        this.$message.warning('请输入密码')
        return
      }
      this.$http.editCompanyPassword(this.form_password).then(res => {
        if (res.status == 200) {
          this.$message.success("修改成功")
          this.show_edit_password = false
        }
      })
    },

    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该公司, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCompany(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 修改操作
    updataCompany(index, row) {
      this.$goPath(`/updata_company?id=${row.id}`);
    },
  },
};
</script>

<style scoped lang="scss">
.el-select {
  margin-left: 10px;
}

.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;

  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  .el-image {
    height: 40px;
    width: 100%;
  }
}
.title-ctn {
  margin-left: 10px;
  margin-bottom: 5px;
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
    justify-content: flex-start;
  }
  i {
    margin-right: 5px;
    display: block;
    text-align: center;
    border-radius: 50%;
    line-height: 20px;
    width: 20px;
    height: 20px;
    background: #26bf8c;
    color: #fff;
  }
}
</style>
