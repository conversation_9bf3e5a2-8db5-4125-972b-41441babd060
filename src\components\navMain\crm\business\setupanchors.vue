<template>
    <el-dialog :visible.sync="show" title="请设置主播" width="430px">
        <div style="color: #e6a23c;"> <i class="el-icon-search"></i>可直接搜索成员或手机号</div>
            <el-input placeholder="请输入成员名字或手机号" style="width: 250px;" v-model="input2">
              <el-button slot="append" icon="el-icon-search" @click="userSearch"></el-button>
            </el-input>
            <div style="margin-top: 20px;">
              <multipleTree v-if="show_add_memberA" :list="serverData" :defaultValue="selectedIds"
                @onClickItem="selecetedMember" :defaultExpandAll="Allfalse" ref="memberList">
              </multipleTree>
            </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import multipleTree from "../../crm/my-detailTree.vue"
export default {
    name: 'setupanchors',
    components: { 
    multipleTree
  },
    data(){
        return {
            show: false,        //dialog是否显示
            serverData:[],
            input2:"",//检索成员
            selectedIds: [], //默认勾选和展开的节点的 key 的数组
            Allfalse:false,
            userparams:{
              user_name:""
            },
            datalist: [], // 全部部门人员
            show_add_memberA:false,
            adminid:[]
        }
    },
    methods: {
        //打开弹层
        open(params){
            this.params = params;
            this.selectedIds = []
            this.showAccompanyA()
            this.show = true;
            return this;
        },
        userSearch(){
            this.userparams.user_name = this.input2
            this.Allfalse = true
            this.showAccompanyA()
        },
        showAccompanyA(){
              // this.appointAccompany = []
              this.$http.getDepartmentMemberList(this.userparams).then((res) => {
                if (res.status == 200) {
                  this.serverData = JSON.parse(JSON.stringify(res.data))
                  console.log(this.serverData);
                  this.show_add_memberA = true
                  this.serverData.push({
                    id: 999,
                    name: "未分配部门成员",
                    order: *********,
                    pid: 0,
                    subs: this.serverData[0].user
                  })
                  this.recursionData(this.serverData);
                  // 当键值key重复就更新key+=父级
                  for (let i = 0; i < this.datalist.length; i++) {
                    for (let j = i + 1; j < this.datalist.length; j++) {
                      if (this.datalist[i].id == this.datalist[j].id) {
                        this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
                      }
                    }
                  }
                }
              })
            },
            // 递归数据处理
            recursionData(data) {
              for (let key in data) {
                if (typeof data[key].subs == "object") {
                  data[key].subs.map((item) => {
                    if (item.user) {
                      item.subs = item.user;
                      item.subs.map((list) => {
                        list.Parent = item.id;
                      })
                    }
                    if (item.user_name) {
                      item.name = item.user_name;
                      this.datalist.push(item)
                    }
                  })
                  this.recursionData(data[key].subs);
                }
              }
        },
        // 选中变化时触发
        selecetedMember(e) {
            this.selectedIds = e.checkedKeys;
        },
        //取消
        cancle(){
            this.show = false;
        },
        //确定
        confirm(){
            let params = {}
            if(!this.selectedIds.length){
                return this.$message.warning("请设置主播")
            }
            params.admin_ids = this.selectedIds.join(",")
            this.$http.setananchorpresent(params).then((res)=>{
                if(res.status==200){
                    this.$message.success("设置成功")
                    this.$emit("getlookdata",)
                    this.show = false;
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
    
</style>