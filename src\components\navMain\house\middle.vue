<template>
  <div v-if="show">123123</div>
</template>

<script>
export default {
  data() {
    return {
      show: false
    }
  },
  created() {
    let id = this.$route.query?.id
    let type = this.$route.query?.redirect_type || 1
    let website_id = this.$route.query?.website_id
    let appid = this.$route.query.appid || 'ww1fa7d290d0833791'
    let url = '', pcUrl = '', remotePcUrl = ''
    if (type == 1) {
      pcUrl = encodeURIComponent(`https://yun.tfcs.cn/admin/#/wx_work_auth_t`)
      remotePcUrl = encodeURIComponent(`house_detail?id=${id}&website_id=${website_id}`)
      url = encodeURIComponent(`https://yun.tfcs.cn/fenxiao/house/detail?id=${id}&type=3&website_id=${website_id}`)
    } else if (type == 2) {
      pcUrl = encodeURIComponent(`https://yun.tfcs.cn/admin/#/wx_work_auth_t`)
      remotePcUrl = encodeURIComponent(`house_audit?id=${id}&website_id=${website_id}`)
      url = encodeURIComponent(`https://yun.tfcs.cn/fenxiao/house/approvalDetail?id=${id}&type=3&website_id=${website_id}`)
    } else if (type == 3) {
      pcUrl = encodeURIComponent(`https://yun.tfcs.cn/admin/#/wx_work_auth_t`)
      remotePcUrl = encodeURIComponent(`crm_customer_audit?id=${id}&website_id=${website_id}`)
      url = encodeURIComponent(`https://yun.tfcs.cn/fenxiao/house/approvalDetail?id=${id}&type=3&website_id=${website_id}`)
    } else if (type == 4) {
      pcUrl = encodeURIComponent(`https://yun.tfcs.cn/admin/#/wx_work_auth_t`)
      remotePcUrl = encodeURIComponent(`crm_customer_information?information=1&website_id=${website_id}`)
      url = encodeURIComponent(`https://yun.tfcs.cn/fenxiao/customer/trans_list?website_id=${website_id}&type=3`)
    } else if (type == 5) {
      pcUrl = encodeURIComponent(`https://yun.tfcs.cn/admin/#/wx_work_auth_t`)
      remotePcUrl = encodeURIComponent(`crm_information_detail?id=${id}&type=my&information=1&website_id=${website_id}`)
      url = encodeURIComponent(`https://yun.tfcs.cn/fenxiao/customer/trans_detail?id=${id}&website_id=${website_id}&type=3`)
    }
    var sUserAgent = navigator.userAgent.toLowerCase();
    var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
    var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
    var bIsMidp = sUserAgent.match(/midp/i) == "midp";
    var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
    var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
    var bIsAndroid = sUserAgent.match(/android/i) == "android";
    var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
    var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";
    if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
      window.location.replace(`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&response_type=code&scope=snsapi_userinfo&agentid=&state=STATE#wechat_redirect&redirect_uri=${url}`) 
    } else {
      window.location.replace(`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&response_type=code&scope=snsapi_userinfo&agentid=&state=${remotePcUrl}#wechat_redirect&redirect_uri=${pcUrl}`)
    }
  },
}
</script>

<style>
</style>