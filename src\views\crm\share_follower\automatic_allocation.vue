<template>
    <div>
        <el-dialog
          :title="numberCustomer"
          :visible.sync="dialogVisible"
          width="600px"
          :before-close="handleClose">
          <!-- <div class="rule_title">当前已选择  条客资</div> -->
          <div class="allocation_rule" style="margin-bottom: 15px">
            <el-radio v-model="totalConfig_params.mode" :label="1">
              按自动分配规则中成员/分组分配
            </el-radio>
          </div>
          <div class="allocation_rule" style="margin-bottom: 15px">
            <el-radio v-model="totalConfig_params.mode" :label="2">
              按选择成员平均分配
            </el-radio>
          </div>
          <div class="allocation_rule" style="margin-bottom: 15px">
            <el-radio v-model="totalConfig_params.mode" :label="3">
              按选择分组平均分配
            </el-radio>
          </div>
          <div v-if="totalConfig_params.mode == 3||totalConfig_params.mode == 2">
            <automaticallocation ref="automaticallocation" :search_user_list="search_user_list" :user_list="user_list"
            :GoupData="teammembers" :allgoupdata="allgoupdata"  @onPageChange="onPageChange"
            @receivedata="receivedata" @receivegoupdata="receivegoupdata" 
            :totalConfig_params="totalConfig_params"
            ></automaticallocation>
          </div>
          <div style="color: red;margin-top:10px;font-size: 16px;">
            注意操作后维护人将变更，不可撤销及恢复</div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitdata">确认执行</el-button>
          </span>
        </el-dialog>
    </div>
</template>
<script>
import automaticallocation from "./automatic.vue"
export default {
    components:{
        automaticallocation,
    },
    data() {
        return {
            numberCustomer:"",
            dialogVisible:false,
            totalConfig_params:{
                mode:1,//方式
                private_push_uid:""
            },
            search_user_list: [], // 搜索成员列表
            user_list: [], // 关联成员列表
            employee_params: {
              page: 1,
              per_page: 100,
              type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
            },
            GoupData:[],//分组数据
            teammembers:[],//带有成员的分组
            cluegrouppage:{
              page:1,
              per_page:10
            },
            allgoupdata:{},//加分页的分组数据
            multipleSelection:[],//选择的客户
            params:{
              user_id:"",//成员id
              group_id:"",//组id
            },
        }
    },
    async mounted() {
      this.website_id = this.$route.query.website_id;
      let res = await this.$http.getManagerAuthList({ params: this.employee_params }).catch(() => { });
      if (res.status == 200) {
        this.user_list = res.data.data;
        this.search_user_list = res.data.data;
      }
      this.cluegrouping()
    },
    methods:{
        open(params){
          this.multipleSelection = params
          this.numberCustomer = `当前已选择${params.length}条客资 (批量自动分配)`
          this.dialogVisible = true
        },
        handleClose(){
            this.dialogVisible = false
        },
        //获取组成员
        getgroupall(){
          this.$http.getgroupall().then(res=>{
            if(res.status==200){
              let matchingItems =[]
              res.data.forEach(specificItem => {
                const groupId = specificItem.group?.id; // 获取 group ID
                if (groupId !== undefined) {
                    const matchingGroup = this.GoupData.find(groupItem => groupItem.id === groupId);

                    if (matchingGroup) {
                        // 重新命名项目
                        const newName = specificItem.name + ' (Renamed)';
                        // 如果 items 数组未定义，则初始化为一个空数组
                        if (!matchingGroup.items) {
                            matchingGroup.items = [];
                        }
                        // 将重命名后的项目添加到 matchingGroup 的 items 中
                        matchingGroup.items.push({ ...specificItem, name: newName });
                    }
                }
            });
            this.teammembers = this.GoupData
            }
          })
      },
        //获取组
        cluegrouping(){
          this.$http.cluegrouping(this.cluegrouppage.page,this.cluegrouppage.per_page).then((res)=>{
            if(res.status==200){
              // console.log(res.data);
              this.allgoupdata = res.data
              this.allgoupdata.per_page = Number(res.per_page)
              this.GoupData = res.data.data
              this.getgroupall()
            }
          })
        },
        //分组表格分页
        onPageChange(e){
          this.cluegrouppage.page = e
          this.cluegrouping()
        },
        //接收选中成员
        receivedata(e){
            // console.log(e);
            if(e.private_push_uid.length){
              this.params.user_id = e.private_push_uid.join(",")
            }else{
              this.totalConfig_params.private_push_uid = ""
            }
            delete this.params.group_id
            this.$refs.automaticallocation.emptygroup()
        },
        //接收选中分组
        receivegoupdata(e){
            // console.log(e);
            this.params.group_id = e.private_group_id
            delete this.params.user_id
            this.$refs.automaticallocation.emptyuser()
        },
        //确定批量分配
        submitdata(){
          if(this.totalConfig_params.mode==2&&!this.params.user_id){
            return this.$message.warning("请选择分配成员！")
          } 
          if(this.totalConfig_params.mode==3&&!this.params.group_id){
            return this.$message.warning("请选择分配组！")
          }
          if(this.totalConfig_params.mode==1){
            this.params.mode = this.totalConfig_params.mode
          }else{
            this.params.mode = 2
          }
          this.params.ids = this.multipleSelection.join(",")
          // console.log(this.params);
          const loading = this.$loading({
                  lock: true,
                  text: `正在分配....`,
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.7)'
                });
          this.$http.plbatchautomatic(this.params).then(res=>{
            if(res.status==200){
              const message = res.data.join('<br>');
              this.dialogVisible = false
              this.$message({
                type:"success",
                dangerouslyUseHTMLString: true,
                message: `<span style="line-height: 24px;">操作成功：<br> ${message}</span>`
              });

              loading.close();
              this.$emit("getDataList");
              }
          })
        },

    },
}
</script>
<style lang="scss" scoped>
    .rule_title {
      color: #2e3c4e;
      font-size: 14px;
    }
    ::v-deep .el-radio {
        .el-radio__label {
          font-size: 15px !important;
          color: #303133 !important;
        }
      }
</style>