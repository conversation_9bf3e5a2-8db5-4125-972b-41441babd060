<template>
  <div class="setting_daoru">
    <div class="tabs">
      <el-tabs v-model="tabName" type="card" @tab-click="handleClick">
        <el-tab-pane label="模板导入" name="daoru">
          <div class="daoru_con">
            <div class="daoru_title">模板导入</div>
            <div class="daoru_tip flex-row">
              <div class="daoru_tip_left flex-1">
                <p class="daoru_tip_con">导入指导：</p>
                <p class="daoru_tip_con">
                  1、请先设置导入模板，下载模板，按模板格式填写数据再导入。
                </p>
                <p class="daoru_tip_con">
                  2、导入表格文件大小不超过1000行，文件名建议15字以内，文件名不能与已上传文件名重复。
                </p>
                <p class="daoru_tip_con">
                  3、电话号码不规范，修改或删除模板字段，生日不是日期类型等情况会出现导入失败。
                </p>
              </div>
              <div class="daoru_right">
                <el-button
                  icon="el-icon-download"
                  type="primary"
                  plain
                  @click="download"
                  >下载模板</el-button
                >
              </div>
            </div>
            <div class="daoru_upload">
              <el-upload
                class="upload-demo"
                drag
                action="123"
                :limit="1"
                :auto-upload="false"
                :on-change="changeFile"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  只能上传xlsx文件，且不超过1000行
                </div>
              </el-upload>
            </div>
            <div class="daoru_form flex-row align-center f-wrap">
              <div class="daoru_form_item flex-row align-center">
                <div class="daoru_label">线索获量人：</div>

                <el-select
                  class="w300"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请选择线索获量人"
                  :remote-method="getUserList"
                  v-model="form_params.from_user"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.id"
                    :label="item.user_name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
              <div class="daoru_form_item flex-row align-center">
                <div class="daoru_label">包名：</div>
                <el-input
                  class="w300"
                  placeholder="请输入包名"
                  v-model="form_params.name"
                >
                </el-input>
              </div>
              <div class="daoru_form_item flex-row align-center">
                <div class="daoru_label">线索渠道：</div>
                <el-select class="w300" v-model="form_params.channel_id">
                  <el-option
                    v-for="item in channelList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="submit">
              <el-button
                type="primary"
                :loading="add_loading"
                @click="daoruTem"
              >
                导入
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import axios from "axios"
export default {
  data() {
    return {
      tabName: "daoru",
      form_params: {
        from_user: "",
        name: "",
        channel_id: "",
      },
      channel_params: {
        page: 1,
        per_page: 1000,
      },
      userList: [],
      channelList: [],
      add_loading: false
    }
  },
  created() {
    this.getUserList()
    this.getChannelList()
  },
  methods: {
    handleClick() {

    },
    getUserList(e) {
      this.$http.getUserList(
        1,
        10,
        e ? e : ""
      )
        .then((res) => {
          if (res.status === 200) {
            this.userList = res.data.data;
          }
        });
    },
    getChannelList() {
      this.$http.getChannel(
        this.channel_params
      )
        .then((res) => {
          if (res.status === 200) {
            this.channelList = res.data.data;
          }
        });
    },
    changeFile(e) {
      this.file = e.raw
    },
    download() {
      window.open(`${this.$imageDomain}/static/admin/waihu/clue_template.xlsx?f=` + (+new Date()))
    },
    daoruTem() {
      this.add_loading = true
      let file = new FormData()
      file.append("file", this.file)
      file.append("from_user", this.form_params.from_user)
      file.append("name", this.form_params.name)
      file.append("channel_id", this.form_params.channel_id)
      axios.post('/api/admin/call_clue/addCallClue', file, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") } }).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "提交成功")
          this.$emit("daoruSuccess")
        } else {
          this.$message.warning(res.message || "提交失败")
        }
        this.add_loading = false
      })
        .catch((err) => {
          this.add_loading = false
          this.$message.error(err.response?.data?.message || "提交失败")
        })

    }

  }
}
</script>

<style lang="scss" scoped>
.setting_daoru {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
.w300 {
  width: 300px;
}
.tabs {
  width: 900px;
  margin: 10px auto;
  .daoru_con {
    padding: 20px;
    background: #fff;
    .daoru_title {
      text-align: center;
      padding: 30px 0;
      color: #2e3c4e;
      font-size: 20px;
      font-weight: 500;
    }
    .daoru_tip_con {
      color: #8a929f;
      font-size: 14px;
      line-height: 1.8;
    }
    .daoru_upload {
      margin: 30px 0;
    }
    ::v-deep .upload-demo {
      .el-upload {
        width: 100%;
      }
      .el-upload-dragger {
        width: 100%;
      }
    }
  }
  .daoru_form {
    .daoru_form_item {
      margin-right: 70px;
      margin-bottom: 20px;
      &:nth-child(2n) {
        margin-right: 0;
      }
      .daoru_label {
        color: #2e3c4e;
        font-size: 14px;
        margin-right: 10px;
      }
    }
  }
  .submit {
    text-align: center;
  }
}
</style>