<template>
<el-dialog :visible.sync="show" title="导入话术库" width="600px">   

    <el-form ref="form" :model="params" label-width="120px">
      
        <el-form-item label="导入文件">
            <el-upload class="upload-demo" accept=".xls,.xlsx" action :auto-upload="false" :limit="1" :class="{'has-file':params.file}"
                :on-remove="clearUploadFile" :on-change="onUploadChange" ref="upload">
                <el-button v-if="!params.file">选择Excel文件</el-button>
            </el-upload>
            <div>
                <a class="link-download" :download="`${this.$imageDomain}/excel/words_excel_demo.xlsx`" target="_blank" :href="`${this.$imageDomain}/excel/words_excel_demo.xlsx`">
                    <i class="el-icon-download"></i>下载导入模板
                </a>
            </div>
        </el-form-item>
    </el-form>

    

    <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" @click="confirm" :loading="submiting">确 定</el-button>
    </span>
</el-dialog>
</template>

<script>
export default {
    data(){
        return {
            show: false,
            params: {           //表单参数
                package_id: '',
                file: null
            },
            submiting: false
        }
    },
    methods: {
        open(params){
            this.params.package_id = params.id;
            this.show = true;
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(!this.params.file){
                this.$message.error('请选择 excel 文件');
                return;
            }

            const formData = new FormData();
            formData.append('package_id', this.params.package_id);
            formData.append('file', this.params.file.raw);
            formData.append('website_id', localStorage.getItem("website_id"));
            this.submiting = true;
            try{
                const res = await this.$http.importSpeechLibrary(formData);    
                if(res.status == 200){
                    this.$message.success(res?.data?.msg || '导入成功');
                    this.show = false;
                    this.clearUploadFile();
                }
            }catch(e){
                console.error(e);
            }
            this.submiting = false;
        },
        onUploadChange(file){
            this.params.file = file;
        },
        clearUploadFile(){
            this.$refs.upload.clearFiles();
            this.params.file = null;
        }
    }
}
</script>
<style lang="scss" scoped>
.upload-demo{
   display: flex;
   align-items: center;
   height: 40px;
}
.link-download{
    display: inline-block;
    color: #409EFF;
    text-decoration: none;
    margin-top: 16px;
}
::v-deep .el-form-item__content{
    line-height: 1;    
    .el-upload-list__item:first-child{
        margin-top: 0;
    }
}
.has-file::v-deep .el-upload{
    display: none;
}
::v-deep .el-upload-list__item{
    transition: none;
}
</style>