<template>
		<div id="div2" class="chat" :class="{newWith:addRemove,newWith2:newF}"  v-if="showHide">
			<div class="button_button">
				<i class="iconfont icon-suoxiao" @click="suoxiao()"></i>
				<i class="iconfont icon-guanbi" @click="close()"></i>
					</div>
		<div  class="yyj_sidebar" v-if="directFlag">
			<div class="head_img" v-if="member">
				<div class="memberImg">
					<img  width="40" height="40" :src="member.prelogo" >
				</div>
				<div class="memberMessage">
					<p>
						<span :title="member.nickname" class="nickname">{{member.nickname}}</span>
						<span :title="member.build_names" class="tname">{{member.build_names}}</span>
						<span class="level_name">{{member.level_name}}</span>
					</p>
					<p class="font8 color8">
						<span>咨询量：{{member.traffic_volume}}</span>
						<span>活跃度：{{member.active}}</span>
						<span>浏览量：{{member.browse}}</span>
					</p>
					<p class="font8 color9">
						<span><i ref="tel" class="iconfont icon-dianhua11" @click="phone()"></i><span>发布楼盘动态</span></span>
						<span><i  @click="click_weixin()" class="iconfont icon-weixin31"></i><span>我的名片</span></span>
						<span><i class="iconfont icon-gengduo1"></i><span>更多</span></span>
					</p>
				</div>  
			</div>
			<div class="list">
				<div class="friendList" @click="clickList('friendsListFlag')">
						<i v-if="sidebar.friendsListFlag" class="iconfont icon-jiantou1"></i>
						<i v-else class="iconfont icon-jiantou3"></i>
						好友列表</div>
					<ul class="ul1" v-if="sidebar.friendsListFlag">
						
				<li v-for="(item,index) in seesionList" :class="{active:seesion.platform_id === item.platform_id}"  @click="selected(index)" :key="index" >	
								<img class="avatar"  width="25" height="25" :src="item.headimage">
								<span :title="item.nickname" class="span f14">{{item.nickname}}</span>
								<i v-if = "item.uncount > 0" class="redPoint">{{item.uncount}}</i>
									<span class="sign">{{item.chat.time}}</span>
									<p :title="item.chat.content" class="new">{{item.chat.content}}</p> 
								</li>
					</ul>
					<div class="backList"  @click="clickList('backListFlag')">
						<i v-if="sidebar.backListFlag" class="iconfont icon-jiantou1"></i>
						<i v-else class="iconfont icon-jiantou3"></i>黑名单</div>
					<ul class="ul1" v-if="sidebar.backListFlag">
					
					<li v-for="(item,index) in seesionListBack" :class="{active:seesionBack.platform_id === item.platform_id}"  @click="selected_back(index)" :key="index">
											
											<img class="avatar"  width="25" height="25" :src="item.headimage">
											<span class="span">{{item.nickname}}</span>
											
											<i v-if = "item.uncount > 0" class="redPoint">{{item.uncount}}</i>
												<span class="sign">{{item.chat.time}}</span>
												<p class="new">{{item.chat.content}}</p> 
											</li>
											<span v-if="reFlag" class="remove" @click="removeBack()">移除黑名单</span>
								</ul>
					<div class="visitorList" @click="clickList('visitorListFlag')">
						<i v-if="sidebar.visitorListFlag" class="iconfont icon-jiantou1"></i>
						<i v-else class="iconfont icon-jiantou3"></i>
						访客记录
					</div>
					<ul class="ul1" v-if="sidebar.visitorListFlag">
					<li v-for="(item,index) in visitorList" :key="index" @click="visitorDetail(item.user_id)">				
						<img class="avatar"  width="25" height="25" :src="item.prelogo">
						<span class="span">{{item.cname}}</span>
						<span class="sign">{{item.time}}</span>
						</li>
					</ul>
			</div>
		
			</div> 
			<div class="kehu" v-if="gengduoFlag">
				
				<div class="customerCard">
					<h4>客户名片</h4>
					<p style="margin-top:20px;"><span>姓名：</span><span>李女士</span></p>
					<p><span>标签：</span><span>意向客户</span></p>
					<p><span>I&nbsp;&nbsp;&nbsp;&nbsp;P：</span>127.140.0.2</p>
					<p class="last-child"><span>+</span><span>修改</span></p>
					<p class="last-child" @click="pullBack()">加入黑名单</p>
					<h4>客访轨迹</h4>
				</div>
				<div class="visitor">
					
					<ul>
						<li v-for="(item,index) in visitordetail" :key="index">
							<p :title="item.content">{{item.content}}</p>
							<span>{{item.time}}</span>
						</li>
					</ul>
				</div>
			
			</div>
			<div :class="{yyj_middle:isNo}">
			<div class="c_main" v-if="showMain">
				<div class="m_view">
					<div class="head_img2" v-if="to_member">
						<div class="memberImg">
							<img v-if="to_member.prelogo"  width="40" height="40" :src="to_member.prelogo" >
							<img  v-else  width="40" height="40" :src="to_member.headimage" >
						</div>
						<div style="float:right;width:172px;margin-top:50px;">
							<p class="color9">
								<i ref="tel" class="iconfont icon-dianhua11 f30" @click="phone()"></i>
							<i @click="click_weixin()" class="iconfont icon-weixin31 f30"></i>
					<i  @click="gengduo()" class="iconfont icon-gengduo1 f30"></i>
							</p>
						</div>
						<div class="memberMessage wd">
							<p>
								<span :title="to_member.nickname" class="nickname">{{to_member.nickname}}</span>
								<span :title="to_member.build_names" class="tname">{{to_member.build_names}}</span>
								<span class="level_name">{{to_member.level_name}}</span>
							</p>
							<p class="font8 color8">
								<span>咨询量：{{to_member.traffic_volume}}</span>
								<span>活跃度：{{to_member.active}}</span>
								<span>浏览量：{{to_member.browse}}</span>
							</p>
								
						</div>	
					</div>						
			<div id="message" class="message">
				<ul id="mescroll">
					<template  v-for="(item_item,index) in chatlogList">
						<li  v-if="item_item.isMy==0" :key="index">
							<p class="center"><span>{{item_item.time}}</span></p>
							<div class="dialog">
								<img class="yyj_left" width="32" height="32" :src="to_headimg">
								<div class="text2 yyj_left chatimageBox m_top" v-if="item_item.type=='image'">
									<img   class="ml chatimage" :src="item_item.content.img">
								</div>
								<div class="text2 yyj_left chatimageBox m_top" v-else-if="item_item.type=='map'">
									<span>{{item_item.content.address+item_item.content.name}}</span>
								</div>
								<div class="text2 yyj_left leftbox"  v-else-if="item_item.type=='text'">
									<div class="t_left" v-for="(item3,idx) in item_item.showArray" :key="idx">
										<img  v-if="item3.type==1" width="25" height="25" :src="item3.icon">
										<span v-else-if="item3.type==0" v-html="item3.text"></span>
										 <div class="trangleLeft"></div>
									</div>
								</div>
								<div  class="text2 yyj_left leftbox weixin"  v-else-if="item_item.type=='wechat'">
									<div class="wei_left"><img class="wxpng" src="../../assets/img/weixin.png" width="45" height="45"></div>
										<div class="wei_right"><span>{{to_member.nickname}}</span>
										</div>
											<p><i ref="wechat" @click="viewCode(to_member.wechat_img)">查看二维码</i></p>
									   </div>
								<div  class="text2 t_r yyj_left leftbox"  v-else-if="item_item.type=='build'">
										<img  :src="item_item.content.image" width="160" height="100" >
										<p class="ptitle">{{item_item.content.title}}</p>
									  </div>
								</div>
							
						</li>
						<li  v-else-if="item_item.isMy==1" class="my" :key="index">
							<p class="center"><span>{{item_item.time}}</span></p>
							<div class="dialog">
								 <img class="yyj_right" width="32" height="32" :src="headimg">
								 <div class="text2 t_r yyj_right chatimageBox"  v-if="item_item.type=='image'">
									<img  class="mr chatimage" :src="item_item.content.img">
								 </div>
								 <div class="text2 t_r yyj_right chatimageBox"  v-else-if="item_item.type=='map'">
									<span>{{item_item.content.address+item_item.content.name}}</span>
									
								 </div>
								 <div class="text2 t_r yyj_right rightbox"  v-else-if="item_item.type=='text'">
									<div class="t_left" v-for="(item3,idx) in item_item.showArray" :key="idx">
										  <img  v-if="item3.type==1" class="mr" width="25" height="25" :src="item3.icon">
										  <span v-else-if="item3.type==0" v-html="item3.text"></span>
										 
										  <div class="trangleRight"></div>
										 
									</div>
								   </div>
							<div   class="text2 t_r yyj_right rightbox weixin"  v-else-if="item_item.type=='wechat'">
								<div class="wei_left"><img class="wxpng" src="../../assets/img/weixin.png" width="45" height="45"></div>
								<div class="wei_right"><span>{{to_member.nickname}}</span>
								</div>
									<p><i ref="wechat" @click="viewCode(to_member.wechat_img)">查看二维码</i></p>
								   </div>
								   <div  class="text2 t_r yyj_right rightbox"  v-else-if="item_item.type=='build'">
										<img  :src="item_item.content.image" width="160" height="100">
										<p class="ptitle">{{item_item.content.title}}</p>
									   </div>
							</div>
						</li>
					</template>
				</ul> 
			</div>
			<div class="setMsg" v-if="msgFlag">
						<i class="iconfont icon-guanbi dw" @click="closeMsg()"></i>
						<div class="s_head">
							<p>设置快捷回复（50字以内）
							<select  @change="changeItem($event)">
			  <option v-for="(item,index) in common" :value ="index" :key="index">{{index}}</option>
			
			</select></p>
						</div>
						<div class="s_main">
							<div class="mainLeft"><textarea v-model="msgContent"></textarea></div>
							<div class="mainRigh">
							 <button @click="saveCommon()">保存</button>
							 
							</div>
						</div>
						<p v-if="resultFlag" style="position: absolute;top: 20px;left: 142px;width: 100px;height: 30px;background: #c2c3c2;
						text-align: center;
						line-height: 30px;
						 border-radius: 2px;
						 color: red;">{{succsessCommon}}</p>
					</div> 
					
		</div> 
		
		<div class="m_input">
			<template v-if="realShow">
			<div v-if="member" class="real">
				<img src="https://images.tengfun.com/attachment/build/20190827/b210a0c93dc61f7d98a388af87c055873d642985.jpeg" width="106" height="80" style="border-radius:4px;margin-left: 14px;">
				<span>保利海德家园</span>
				<button @click="sendReal()">发送楼盘</button>
			</div>
			</template>
			<div ref="qq" class="toolbar">
				<div class="t_left">
					<i class="iconfont icon-biaoqing" ref="expression"></i>
					<span class="iconfont icon-jiahao1" style="position:relative">
						<input id="file" class="file" type="file" @change="changFile()">
					</span>
				</div>
				<div class="t_right" ref="hide">
					<span><i class="iconfont icon-shandian1"></i>快捷回复</span>
					<div  class="common" v-if="commonFlag">
						<template v-for="(item,index) in common">
							<p  @click="clickSend(index)" :key="index">{{item.content}}</p>
						</template>
						
						<p @click="setMsg()">快捷回复设置</p>
						
					</div>
				</div>
				<div class="expression" v-if="expressionShow">
					<template v-for="(item,index) in list_img">
		<img :class="{aaa:list_img2[index_index]===item}"  width="30" height="30" :src="item" @click="click_img(index)" :key="index">
					</template>
				</div>
			</div>
			<div class="bottom">
				<!-- placeholder="在此输入消息..." -->
				<pre id="local-message-container" placeholder="想说点什么？"  class="placeholder" contenteditable="true" spellcheck="false"  @input="setPlaceHolder" @keyup.enter="send()"></pre>
				
			</div>
				<button  class="send" @click="send()" >发送</button>
		</div> 
			</div> 
			</div>
			<!--缩小窗口 start-->
			 	 <div class="suoxiao" v-if="showX" @click="view()">
					<span><img class="s_img" width="40" height="40" :src="headimg"></span>
					<span>{{nName}}</span>
				</div> 
				<!--缩小窗口 end-->
				<!--二维码 start-->
				 	 <div ref="erVode" class="erVode" v-if="vodeShow">
						<i class="iconfont icon-guanbi dw2" @click="closeVode()"></i>
						<img  :src="vodeImg" width="120" height="120">
					</div> 
					<!--二维码 end-->
			<!--电话 start-->
			 <div class="telbox2" v-if="telShow">
				<div class="tel2" >
					<span>{{tel}}</span>
				</div>
			</div> 
			<!--电话 end-->
			</div>
	
	 
	
</template>

<script>
	import MeScroll from 'mescroll.js'
	import 'mescroll.js/mescroll.min.css'
	var agent_id="";
	export default {
		
				  data() {
					  return {
						  data:"",
						  timer:"",
						    dragDiv:null,
						    commonFlag:false, //控制常用语显示隐藏
						    directFlag:true, //控制左侧导航栏显示隐藏
						    addRemove:false, //控制左侧导航栏显示时样式宽度
						    msgFlag:false, //快捷回复页面显示隐藏
						    messageDom:"", //在此输入行内容
						  //  wechat_img:"",//微信二维码图片
						    vodeShow:"",//微信二维码显示标志
						    telShow:false,//电话号码显示
						    showX:false,//控制缩小框显示
						    expressionShow:false,//控制表情显示
						    realShow:false,//楼盘信息显示隐藏
							dataCommon:"",//常用语参数
						    tel:"",
						    vodeFlag:false,
						    vodeImg:"",
						    seesionList:[],
							seesionListBack:[],
						    seesionIndex:0,
						    sourceData:"",
						    headimg:"",
						    to_headimg:"", //对方头像
						    to_nName:"",
						    nName:"",
						    webSocket: null,
						    list:['微笑','大笑','笑哭','开心','呲牙','坏笑','欣慰','鄙视','白眼','飞吻','鬼脸','酷','爱财','调皮','惊讶','无表情','思考','亲亲','喜欢','低沉','怒','生气','超爱','大哭','小声','惊恐','爱心','心碎','偷看','OK','耶','大拇指','握拳','强壮'],
						    list_img:[],
						    list_img2:[],
						    index_index:0,
						    flag:false,
						    inputMessage:"",//底部输入框内容,
						    showHide:true,//显示聊天窗口
						    msgText:"",
						    isConnet:false, //
						    member:"",//直聊获取的经纪人/职业顾问的信息数据
						    common: "", //常用语
						    resultFlag:false,
						    succsessCommon:"",
						    indexCommon:0,
						    msgContent:"",
						    user:{
								nickname:"",
								headimage:"",
								id:""
							},
							/*新添加 */
							to_member:"",
							chatlogList:[] ,//聊天记录
							seesionIndexBack:0,
							isDestroy:false,
							gengduoFlag:false,
							newF:true,
							reFlag:false, //黑名单显示隐藏
							visitorList:[],//访客列表
							visitordetail:"",//访客详情
							/**封装**/
							sidebar:{
								friendsListFlag:false,
								backListFlag:false,
								visitorListFlag:false
								
							},
							page:1,
							chat_id:"",
							mescroll:null,
							showMain:false,//显示聊天界面
							isNo:true,
							upFlag:false
							
					  }
					
				  },
				
				 created() {
					
					this.dragDiv = document.getElementById("div2");
					 for(let i=0;i<this.list.length;i++){
						this.list_img.push("https://images.tengfangyun.com/images/face/"+i+".png");
						this.list_img2.push("https://images.tengfangyun.com/images/face/"+i+".png");
					 }
					 var that=this;
						 document.addEventListener('click',(e)=>{ //点击快捷回复
						 if(that.$refs.hide){
							 if(that.$refs.hide.contains(e.target)){
							             that.commonFlag=true;
										that.ajaxCommon();
							 
							         }else{
							 				that.commonFlag=false;
							 }
						 }
									    
						 })  
						 
						 document.addEventListener('click',(e)=>{ //点击表情
						         //console.log("表情"+that.$refs.expression.contains(e.target));
						 if(that.$refs.expression){
							 if(that.$refs.expression.contains(e.target)){
							            that.expressionShow=true;
							         }else{
							 			that.expressionShow=false;	 
							 }
						 }
						      
						 })
						document.addEventListener('click',(e)=>{ //点击电话  
						if(that.$refs.tel){
							if(that.$refs.tel.contains(e.target)){
						           that.telShow=true;
						        }else{
									  that.telShow=false; 
							}
						}
						        
						})
				 }, 
				 beforeDestory(){
					 this.isDestroy=true;
				   this.webSocket.close();
				   clearInterval(this.timer);
				 },
				 destroyed(){
					 this.isDestroy=true;
					 this.webSocket.close();
					 clearInterval(this.timer);
				 },
				 
				mounted(){
					if(agent_id){
						this.directFlag=false;
						this.addRemove=true;
						this.newF=false;
						this.$userApi.chatInfo(agent_id).then(res=>{ //直聊
						//console.log("直聊："+JSON.stringify(res.data));
							      if(res.data.code == 1){
							         console.log("直聊："+JSON.stringify(res.data));
									 this.seesionList.push(res.data.toUser);
									 this.user.nickname=res.data.toUser.nickname
									 this.user.headimage=res.data.toUser.headimage;
									 this.user.id=res.data.toUser.platform_id;
									// console.log("seesionList:"+ JSON.stringify(this.seesionList));
									 if(res.data.toUser){
										 this.getContactDetails(res.data.toUser.chat_id,1,1);
									 } 
								
							      }
							  }).catch(err=>{
						    console.log("yyj"+err)
						   
						})
					}else{ //非直聊
						
						//var page=1;var is_black=0;
						var params={
							page:1,
							is_black:0
						}
						var params2={
							page:1,
							is_black:1
						}
					this.getChatFriends(params,1); //获取好友列表
					this.getChatFriends(params2,0);//获取黑名单
					this.getVisitorList();//获取访客记录

						var chat_id=""
					this.getContactDetails(chat_id); 
					
					
					
					
				/* 	var that=this;
					setTimeout(function () {
						that.makeDrag();
					},300); */
					
					}
					
				
					
				},
				  methods:{
					  upLoading(){ //上拉加载
					  
					  var that=this;
						  //创建MeScroll对象
						 that.mescroll = new MeScroll("mescroll", {
						  	down: {
						  		auto: false, //是否在初始化完毕之后自动执行下拉回调callback; 默认true
						  		callback: downCallback //下拉刷新的回调
						  	},
						  	
						  });
						  function downCallback(){		
						  	//联网加载数据
						  		that.getChatLog(that.chat_id,that.page,function(list,len){
						  			that.mescroll.endSuccess(len);
									that.chatlogList=that.pinjie(that.chatlogList,list);
						  				that.page++
						  		},function(){
						  			 that.mescroll.enderr();
						  			 that.page++
						  		});			
						  }
					  },
					    soketTitle(){
							var that=this;
						  const h = that.$createElement;
						  that.$msgbox({
							title: '消息',
							message: h('p', null, [
							  h('span', null, 'soket断掉是否重连？ '),
							  h('i', { style: 'color: teal' })
							]),
							showCancelButton: true,
							confirmButtonText: '是',
							cancelButtonText: '否',
							beforeClose: (action, instance, done) => {
							  if (action === 'confirm') {
								instance.confirmButtonLoading = true;
								instance.confirmButtonText = '连接中...';
								if(!that.isDestroy){
									that.soketInit();
								}
								setTimeout(() => {
								  done();
								  setTimeout(() => {
									instance.confirmButtonLoading = false;
								  }, 300);
								}, 3000);
							  } else {
								done();
							  }
							}
						  }).then(action => {
							that.$message({
							  type: 'info',
							  message: "连接成功"
							});
						  }).catch({
							  
						  });
					                
					         },
					  gengduo(){ //点击更多 
					  if(!this.gengduoFlag){
						   this.gengduoFlag=true;
						   this.newF=false;
					  }else{
						   this.gengduoFlag=false;
						   this.newF=true;
					  }
						 
					  },
					  visitorDetail(user_id){
						   
						  this.$userApi.visitorDetail(user_id).then(res=>{ //访客详情
						 // console.log("访客详情："+JSON.stringify(res.data));
						  if(res.data.list && res.data.list.length>0){
							  this.visitordetail=res.data.list;
						  }
							
						  
						  	    
						  	  }).catch(err=>{
						      console.log("yyj"+err)
						     
						  }) 
					  },
					 
					  clickList(e){
						  //alert(this.sidebar[e]);
						 if(!this.sidebar[e]){
						 	this.sidebar[e]=true;
						 							  
						 }else{
						 	this.sidebar[e]=false
						 }
						  
					  },
					pullBack(){ //加入黑名单
						if(this.seesionList.length==0){
							return;
						}
						//console.log("hahah:"+JSON.stringify(this.seesionList)+"===="+this.seesionIndex);
						
						this.reFlag=true;
					
						var chat_id=this.seesionList[this.seesionIndex].chat_id;
						this.$userApi.pullBack(chat_id).then(res=>{ //加入黑名单
						 console.log("pullback："+JSON.stringify(res.data));  
						 if(res.data.code==1){
							// alert(this.seesionList[this.seesionIndex].platform_id+"==="+this.user.id)
							var pull_back={
								flag:'pullBlack',
								to_id:this.seesionList[this.seesionIndex].platform_id,
								from_id:this.user.id
								}
								 var index=this.seesionIndex;
								if(this.seesionIndex==(this.seesionList.length-1)){
									this.seesionIndex=this.seesionList.length-2;
									if(this.seesionIndex==-1){
										this.seesionIndex=0;
									}
									
								} 
								console.log("推送参数："+JSON.stringify(pull_back));
								 this.webSocket.send(JSON.stringify(pull_back));
								 this.seesionListBack.push(this.seesionList[index]);
								 this.seesionList.splice(index,1);	
							/* this.chatlogList=[];
							this.to_member=""; */
							this.showMain=false;
							this.isNo=true;
							
								
						 }
							  }).catch(err=>{
						    console.log("yyj"+err)
						   
						})
						
					},
					removeBack(){ //移出黑名单
					//alert(this.seesionIndexBack);
					console.log("this.seesionListBack:"+JSON.stringify(this.seesionListBack[this.seesionIndexBack]));
						var chat_id=this.seesionListBack[this.seesionIndexBack].chat_id
						this.$userApi.removeBack(chat_id).then(res=>{ //移出黑名单
						 console.log("removeBack："+JSON.stringify(res.data));  
						 if(res.data.code==1){
							 var params={
								 flag:'removeBlack',
								 to_id:this.seesionListBack[this.seesionIndexBack].platform_id,
								 from_id:this.user.id
							 }
							  var index=this.seesionIndexBack;
							 if(this.seesionIndexBack==(this.seesionListBack.length-1)){
								   this.seesionIndexBack=this.seesionListBack.length-2;
								   if(this.seesionIndexBack==-1){
									   this.seesionIndexBack=0;
								   }
								  
							 }
							 console.log("params:"+JSON.stringify(params));
							 this.webSocket.send(JSON.stringify(params));
							 this.seesionList.push(this.seesionListBack[index]);
							  this.seesionListBack.splice(index,1);
						 }
							  }).catch(err=>{
						    console.log("yyj"+err)
						   
						})
						if(this.seesionListBack.length==1){
							this.reFlag=false;

						}
					},
					getVisitorList(){ 
						this.$userApi.visitorList().then(res=>{ //访客列表
						//console.log("访客轨迹："+JSON.stringify(res.data));
						if(res.data && res.data.list.length>0){
							this.visitorList=res.data.list;
						}
						
							      
							  }).catch(err=>{
						    console.log("yyj"+err)
						   
						})
					},
				
					getChatFriends(params,flag){ 
						this.$userApi.getChatFriends(params.page,params.is_black).then(res=>{ //获取好友列表
							      if(res.data.code == 1){
							        console.log("好友列表ddddd："+JSON.stringify(res.data));
									 if(res.data.friends && res.data.friends.length>0){
										 if(flag){
											this.seesionList=this.handleData(res.data.friends);
											// var chat_id=this.seesionList[0].chat_id; //默认取第一个好友chat_id
											 this.to_headimg=this.seesionList[0].headimage;
											 this.headimg=res.data.user.headimage;
											 this.user.nickname=res.data.user.nickname
											 this.user.headimage=res.data.user.headimage;
											 this.user.id=res.data.user.platform_id; 
											//this.getChatLog(chat_id,this.page);
											 //this.getContactDetails(chat_id,1);
										 }else{
											 this.seesionListBack=res.data.friends;
											 this.reFlag=true;
										 }
										
										 
									 }
									if(flag){
										 this.soketInit();
									}
									
							      }
							  }).catch(err=>{
						    console.log("yyj"+err)
						   
						})
						
						
					},
					
					  selected(index){
						  
					  	/*新更改*/
						//console.log("新新新："+JSON.stringify(this.seesionList[index]));
						
						if(this.seesionList[index].platform_id==this.seesionList[index].owner_id){
							//alert("对方已把你拉黑");
							this.$message({
							  message: '对方已把你拉黑，无法发送消息',
							  type: 'warning'
							});
							return;
						}
						this.to_headimg=this.seesionList[index].headimage;
						this.seesionIndex=index;
					   	var chat_id=this.seesionList[index].chat_id;
						this.chat_id=chat_id;
						//console.log(chat_id);
						this.getContactDetails(chat_id,1);
						this.getChatLog(chat_id,this.page);
						this.$userApi.clearUnread(chat_id).then(res=>{ //清空未读消息
							      if(res.data.code == 1){
							         console.log("未读消息："+JSON.stringify(res.data)); 
							      }
							  }).catch(err=>{
						    console.log("yyj"+err)
						   
						})	
								this.showMain=true;
								this.isNo=false;
								this.page=2;
									
							if(!this.upFlag){
								this.upFlag=true;
								var that=this;
								setTimeout(function(){
									that.upLoading();
								},3000)
								
							}
					  	},
						selected_back(index){
							this.seesionIndexBack=index;
							
						},
						getContactDetails(chat_id,flag,flag2){ //
							this.$userApi.getContactDetails(chat_id).then(res=>{ //获取置业顾问经纪人信息
								      if(res.data.code == 1){
										 if(res.data.member){
											 if(flag){
												this.to_member=res.data.member; 
												//this.headimg=res.data.member.prelogo;
												console.log("to_member:"+JSON.stringify(this.to_member));
												
											 }else{
												 this.member=res.data.member;
												// this.headimg=res.data.member.prelogo;
												//console.log("member："+JSON.stringify(this.member));
											 }
											  
										 }
										 if(flag2){
											 this.soketInit();
										 }
										
								      }
								  }).catch(err=>{
							    console.log("yyj"+err)
							   
							})
						},
						pinjie:function (list1,list2) {
								  var  array = [];
								  for (var i = 0; i < list1.length ; i++) {
									  var obj = list1[i];
									  array.push(obj);
								  }
								  for (var i = 0; i < list2.length ; i++) {
									  var obj = list2[i];
									  array.push(obj);
								  }
								  return array;
						 },
						getChatLog(chat_id,page,succeed,fail){ //获取聊天记录
							this.$userApi.getChatLog(chat_id,page).then(res=>{ //获取聊天记录
								//console.log(chat_id+"===="+page);
								      if(res.data.code == 1){
								         console.log("聊天记录hhh："+JSON.stringify(res.data)); 
										 
										 if(res.data.list && res.data.list.length>0){
											 
											// this.chatlogList=this.pinjie(this.chatlogList,this.handleChatLog(res.data.list))										
											this.chatlogList=this.handleChatLog(res.data.list);
											//console.log("this.chatlogList："+JSON.stringify(this.chatlogList));
										 }
										 if(succeed){
											 succeed(res.data.list,res.data.list.length)
											 
										 }
										 
								      }else{
										  if(succeed){
										  	succeed(0)
										  }
									  }
								  }).catch(err=>{
									 
							    console.log("yyj"+err)
							   
							})
						},
						handleChatLog2(datas){ //处理聊天记录
								var data=datas;
								var isMy=0;
								//alert(data.from_id+"===="+this.user.id);
								if(data.from_id==this.user.id){
									 isMy=1;
								}else{
									isMy=0;
								}
								
							var message={};
							var chat="";
							var showArray=""
							if(data.type=="image"){
								chat={
									"content": data.content,
									"type": data.type,
									"time": data.time
								}
							}
							else if(data.type=="text"){
							
								showArray=this.analysisMessage(data.content) 
								chat={
									"content": data.content,
									"type": data.type,
									"time": data.time
								}
							}else if(data.type=="map"){
								chat={
									"content": "[位置]",
									"type": data.type,
									"time": data.time
								}
							}else if(data.type=="wechat"){
							chat={
								"content": "[二维码]",
								"type": data.type,
								"time": data.time
							}
							}
							else if(data.type=="build"){
								chat={
									"content": "[来源]",
									"type": data.type,
									"time": data.time
								}
							}
							data.isMy=isMy;
							data.showArray=showArray;
							data.chat=chat;
							
							//console.log("处理后的聊天记录："+JSON.stringify(datas));
							
							return datas;
						},
						handleChatLog(datas){ //处理聊天记录
						
							for(var i=0;i<datas.length;i++){
								var data=datas[i];
								var isMy=0;
								//alert(data.from_id+"===="+this.user.id);
								if(data.from_id==this.user.id){
									 isMy=1;
								}else{
									isMy=0;
								}
								
							var message={};
							var showArray="";
							var chat="";
							if(data.type=="image"){
								
								chat={
									"content": "[图片]",
									"type": data.type,
									"time": data.time
								}
							}
							else if(data.type=="text"){
								
								showArray=this.analysisMessage(data.content) 
								chat={
									"content": data.content,
									"type": data.type,
									"time": data.time
								}
							}else if(data.type=="map"){
								
								data.content=JSON.parse(data.content);
								chat={
									"content": "[位置]",
									"type": data.type,
									"time": data.time
								}
							}else if(data.type=="wechat"){
								
								chat={
									"content": "[二维码]",
									"type": data.type,
									"time": data.time
								}
							}
							else if(data.type=="build"){
							
								chat={
									"content": "[来源]",
									"type": data.type,
									"time": data.time
								}
							}
							data.isMy=isMy;
							data.showArray=showArray;
							data.chat=chat;
							}
							
							
							//console.log("处理后的聊天记录："+JSON.stringify(datas));
							
							return datas;
						},
					 soketInit:function(){
						var vm=this;
						vm.webSocket = new WebSocket("wss://im.tengfangyun.com:9002");
						vm.webSocket.onopen = function(event) {
							    console.log("Connection open ...");
								  vm.chatInit();
								  
							vm.timer=setInterval(function () { //定时器
								switch (vm.webSocket.readyState) {
								case WebSocket.CONNECTING:
									var object = { flag: 'Heartbeat' };
									vm.webSocket.send(JSON.stringify(object));					               
								break;
								case WebSocket.OPEN:
									var object = { flag: 'Heartbeat' };
									vm.webSocket.send(JSON.stringify(object));					               
								break;
								case WebSocket.CLOSING:
								// do something
								break;
								case WebSocket.CLOSED:
								/* alert("soket断掉") 
								   if(!isDestroy){
									   vm.soketInit();
								   } */
								break;
								default:
								// this never happens
								break;
								}
							
								  
								},30000);
						 }
						vm.webSocket.onmessage = function(event) {
						    var data = event.data;
						    console.log( "Received Message: " + event.data);
							var response=JSON.parse(event.data);
							
							if(response.flag=="sendMessageStatus"){
								var params={
									chat_id:response.chat_id,
									is_online:response.is_online,
									content:response.content,
									type:response.type
								}
								if(response.type!="text"){
									params=JSON.stringify(params);
								}
								
								//console.log("保存聊天记录参数："+JSON.stringify(params));
							 vm.$userApi.saveChat(params).then(res=>{ //保存聊天记录
								//console.log(JSON.stringify("保存聊天记录："+JSON.stringify(res.data)));
									      if(res.data.code == 1){
									         
									      }
									  }).catch(err=>{
								    console.log("yyj"+err)
								   
								})
							}else if (response.flag == "sendMessage"){
							
				 	        vm.onReciveMessageResponse(response);
				 	    }
						else if (response.flag == "pullBlack"){
							
						    vm.addBlackUser(response.black_user_id);
						}
						else if (response.flag == "removeBlack"){
							
						    vm.removeBlackUser(response.black_user_id);
						}
							
						   
						};
						vm.webSocket.onclose = function(event) {
							
						       var code = event.code;
						       var reason = event.reason;
						       var wasClean = event.wasClean;
						                switch (vm.webSocket.readyState) {
						                case WebSocket.CONNECTING:
						               
						                break;
						                case WebSocket.OPEN:
						               
						                break;
						                case WebSocket.CLOSING:
						                // do something
						                break;
						                case WebSocket.CLOSED:
										//alert("soket断掉");
										vm.soketTitle();  
										 /*  if(!vm.isDestroy){
											   vm.soketInit();
										   } */
						                break;
						                default:
						                // this never happens
						                break;
						                }
						      
						     };
						 },
					
					  chatInit:function(){
						  /*新更改*/
						var messageBody ={
							flag:"init",
							from_id:this.user.id
						}
						//console.log("messageBody:"+JSON.stringify(messageBody));
						   this.webSocket.send(JSON.stringify(messageBody));
					  },
					  addBlackUser(userId){ //加黑
					  if(this.seesionListBack.length>0){
						  this.reFlag=true;
					  }
						  //console.log("黑名单："+JSON.stringify(this.seesionList));
						  var lists=this.seesionList;
						  for(var i=0;i<lists.length;i++){
							  var list=lists[i];
							  if(list.platform_id==userId){
								  list.owner_id=userId;
							  }
						  }
					  },
					  removeBlackUser(userId){ //移黑
						  var lists=this.seesionList;
						  for(var i=0;i<lists.length;i++){
							  var list=lists[i];
							  if(list.platform_id==userId){
								  list.owner_id=0;
							  }
						  }
					  },
				
					  changFile(){
						  this.flag=false;
							var that = this;
							var fileTag = document.getElementById('file');
							var file = fileTag.files[0];
							var size=fileTag.files[0].size;
							var fileReader = new FileReader();
							var width=""
							var height=""
							//console.log("11111:"+this.$userApi);
							fileReader.onloadend = function () {
								var image = new Image();
									image.onload = ()=>{
										width = image.width;
										height = image.height;
									}
									image.src=fileReader.result;
							    if (fileReader.readyState == fileReader.DONE) {
									var params={
										base64:fileReader.result
									}
									//console.log("params:"+JSON.stringify(params));
									//console.log("222222:"+that.$userApi);
									that.$userApi.uploadFile(params).then(res=>{ //获取置业顾问经纪人信息
										      if(res.data.code == 1){
										       //  console.log("图片"+JSON.stringify(res.data)); 
												that.sendMessage({img:res.data.url,width:width,height:height},"image")
										      }
										  }).catch(err=>{
									    console.log("yyj"+err)
									   
									})
							    }
							};
							fileReader.readAsDataURL(file);
						 	setTimeout(function () { //滚动条滚到最下
							    var  message = document.getElementById("message");
							    message.scrollTop = message.scrollHeight;
							},100); 
						
					  
					       },
						   click_img(index){
							  this.index_index=index;
							  var img_biaoqing="["+this.list[index]+"]";
							  var eUrl=this.list_img[index]+"?emx="+img_biaoqing;
							  var img='<img width="30" height="30" src='+eUrl+'>'
							  var input=document.getElementById("local-message-container");
							  input.innerHTML+=img;
							 this.setPlaceHolder(); 
						   },
					  setPlaceHolder(){
						 var input=document.getElementById("local-message-container");
						  var className="placeHolder";
						  if(input.classList.contains(className) && input.innerHTML){
							  input.setAttribute("class","")
						  }else{
							  input.setAttribute("class",className);
						  }
					  },
					  dianji(){
						  if(!this.flag){
							   this.flag=true;
						  }else{
							  this.flag=false;
						  }
						 
					  }, 
					  send(){
						 this.inputMessage=this.currentMessage();
						 var  input = document.getElementById("local-message-container");
						 input.innerHTML = "";
						 this.flag=false;
						 this.sendMessage(this.inputMessage,"text")
						 
						 setTimeout(function () {
						     var  message = document.getElementById("message");
						     message.scrollTop = message.scrollHeight;
						 },100);
		
					  },
					  sendMessage(message,type){
						//alert(JSON.stringify(this.member));
						  if(message){
							  this.realShow=false;//隐藏楼盘信息
							  this.messageDom=message;
							  var messageBody={};
							  var local_messageBody={};
							   var timestamp = new Date().getTime().toString();
							   var chat="";
							 if(type=="text"){
								  messageBody = {
									  flag:'sendMessage',
									  from_nickname:this.user.nickname,
									  from_headimage:this.user.headimage,
									  from_id:this.user.id,
									  to_id:this.seesionList[this.seesionIndex].platform_id,
									  chat_id:this.seesionList[this.seesionIndex].chat_id,
									  type:type,
									  content:message
							 	}
								local_messageBody={
										"id":"",
										"from_id": this.user.id,
										 "to_id":this.seesionList[this.seesionIndex].platform_id,
										"content": message,
										"ctime": "",
										"type": type,
										"chat_id": this.seesionList[this.seesionIndex].chat_id,
										"status": "",
										"time": this.timeformat(timestamp),
										"isMy": 1,
										"showArray":this.analysisMessage(message)
								}
								chat={
									"content": message,
									"type": type,
									"time": this.timeformat(timestamp)
								}
							 					
							 }else if(type=="image"){
								 messageBody = {
								 	 flag:'sendMessage',
								 	from_nickname:this.user.nickname,
								 	from_headimage:this.user.headimage,
								 	from_id:this.user.id,
									to_id:this.seesionList[this.seesionIndex].platform_id,
								 	 chat_id:this.seesionList[this.seesionIndex].chat_id,
								 	 type:type,
								 	 content:message
								 }
								 local_messageBody={
								 		"id":"",
								 		"from_id": this.user.id,
								 		 "to_id":this.seesionList[this.seesionIndex].platform_id,
								 		"content": message,
								 		"ctime": "",
								 		"type": type,
								 		"chat_id": this.seesionList[this.seesionIndex].chat_id,
								 		"status": "",
								 		"time": this.timeformat(timestamp),
								 		"isMy": 1
								 		
								 }
								 chat={
								 	"content": "[图片]",
								 	"type": type,
								 	"time": this.timeformat(timestamp)
								 }
								 
							 }
							 else if(type=="map"){
							 messageBody = {
							 	flag:'sendMessage',
							 	from_nickname:this.user.nickname,
							 	from_headimage:this.user.headimage,
							 	from_id:this.user.id,
							 	  to_id:this.seesionList[this.seesionIndex].platform_id,
							 	chat_id:this.seesionList[this.seesionIndex].chat_id,
							 	type:type,
							 	content:message
							 }
							 local_messageBody={
							 		"id": "",
							 		"from_id": this.user.id,
							 	  "to_id":this.seesionList[this.seesionIndex].platform_id,
							 		"content": message,
							 		"ctime": "",
							 		"type": type,
							 		"chat_id": this.seesionList[this.seesionIndex].chat_id,
							 		"status": "",
							 		"time": this.timeformat(timestamp),
							 		"isMy": 1
							 		
							 }
							chat={
								"content": "[位置]",
								"type": type,
								"time": this.timeformat(timestamp)
							}
							 						
							 }else if(type=="wechat"){
								messageBody = {
									 flag:'sendMessage',
									from_nickname:this.user.nickname,
									from_headimage:this.user.headimage,
									from_id:this.user.id,
									  to_id:this.seesionList[this.seesionIndex].platform_id,
									 chat_id:this.seesionList[this.seesionIndex].chat_id,
									 type:type,
									 content:message
								 }
								
								local_messageBody={
										"id": "",
										"from_id": this.user.id,
										  "to_id":this.seesionList[this.seesionIndex].platform_id,
										"content": message,
										"ctime": "",
										"type": type,
										"chat_id": this.seesionList[this.seesionIndex].chat_id,
										"status": "",
										"time": this.timeformat(timestamp),
										"isMy": 1
										
								}	  
								chat={
									"content": "[二维码]",
									"type": type,
									"time": this.timeformat(timestamp)
								}
								 					 		
							 }else if(type=="build"){
								messageBody = {
									flag:'sendMessage',
									from_nickname:this.user.nickname,
									from_headimage:this.user.headimage,
									from_id:this.user.id,
									to_id:this.seesionList[this.seesionIndex].platform_id,
									 chat_id:this.seesionList[this.seesionIndex].chat_id,
									 type:type,
									 content:message
								}
								
									local_messageBody={
											"id": "",
											"from_id": this.user.id,
											  "to_id":this.seesionList[this.seesionIndex].platform_id,
											"content": message,
											"ctime": "",
											"type": type,
											"chat_id": this.seesionList[this.seesionIndex].chat_id,
											"status": "",
											"time": this.timeformat(timestamp),
											"isMy": 1
											
									}
									chat={
										"content": "[楼盘]",
										"type": type,
										"time": this.timeformat(timestamp)
									}
								 					 		
							 }
							 // console.log("messageBody yyj:"+JSON.stringify(messageBody));
								this.seesionList[this.seesionIndex].chat=chat;
								this.chatlogList.push(local_messageBody);
								//console.log("chatlogList:"+JSON.stringify(this.chatlogList));
								if(type !="wechat"){
									 this.webSocket.send(JSON.stringify(messageBody));
								}
							  
						  }else{
							//alert("发送内容不能为空");
							this.$message({
							  message: '发送内容不能为空',
							  type: 'warning'
							});
							  
						  }
						 
						 
						  
					  },
					  changeItemToArrayFirst:function (array, index) {
					  	if (array.length > index) {
							var arr = [];
					  		var  item = array[index];
							arr.push(item);
							for (var  i = 0 ; i < array.length; i ++) {
								var  itm = array[i];
								if (i != index) {
									arr.push(itm);
								}
							}
							array = arr;
						}
						 
						return array;
					  },
					 
					 onReciveMessageResponse(response){
					 				  this.to_headimg=response.from_headimage;
					 				  this.to_nName=response.from_nickname;
					 				 if(response){
					 					 var msg = response;
										 var log=this.handleChatLog2(msg);
					 					this.chatlogList.push(log); 
					 					 var  sessionIndex = -1;
					 					 var  count = this.seesionList.length;
					 					 for (var i = 0; i < count; i++ ) {
					 						  var seesion = this.seesionList[i];
					 						  if (seesion.platformid == msg.from_id) {
					 							sessionIndex = i;
					 							  break;
					 						  }
					 					 }
					 					if ((sessionIndex > 0) && (sessionIndex < count)) {
					 						 this.seesionList = this.changeItemToArrayFirst(this.seesionList,sessionIndex);
					 						 if (this.seesionIndex < sessionIndex) {
					 							 this.seesionIndex ++;
					 						 }
					 					}
					 				 }
					 				 
					 		setTimeout(function () { //滚动条滚到最下
					 		    var  message = document.getElementById("message");
					 		    message.scrollTop = message.scrollHeight;
					 		},100); 		 
					 },
					  
					 
					  timeformat:function (sj) {
						sj=sj.toString();
					    sj = sj.substring(0,sj.length-3);
					            var now = new Date(sj * 1000);
					            var   year=now.getFullYear();
					            var   month=now.getMonth()+1;
					            var   date=now.getDate();
					            var   hour=now.getHours();
					            var   minute=now.getMinutes();
					            var   second=now.getSeconds();
					            return   year+"-"+month+"-"+date+" "+hour+":"+minute+":"+second;
					      
					  },
					  currentMessage(){
						  var input=document.getElementById("local-message-container");
						  var nodes=input.childNodes;
						  var string="";
						
						  for(var i=0;i<nodes.length;i++){
							  var node=nodes[i];
								
							 if(nodes[i].nodeName=="img" ||nodes[i].nodeName=="IMG" ){
								 var url=node.src;
								 if(url){
									 url=decodeURI(url);
									  var  emx = this.getQueryVariable(url,"emx");
									if(emx != undefined && emx){
										string+=decodeURI(emx);
									} 
									
								 }
							 }else{
								
								if (node.nodeName == "#text" || node.nodeName == "#TEXT") {
									
									if(input.innerText.trim()){
										 string+=node.data;
									}
								   
								} 
							/* 	else if (node.nodeName == "div" || node.nodeName == "DIV"){
								    string += "<br>"
								    string +=  node.innerHTML;
								} */
								
							 }
							
							 
						  }
						 
						  return string;
						  
					  },
		              
		              getQueryVariable:  function (url,paraName)  {
		                  var  params = this.getUrlkey(url);
		                  return params[paraName];
		              },
		              getUrlkey: function (url) {
		                  var params = {};
		                  var urls = url.split("?");
		                  if (urls.length > 1) {
		                      var arr = urls[1].split("&");
		                      for (var i = 0, l = arr.length; i < l; i++) {
		                          var a = arr[i].split("=");
		                          params[a[0]] = a[1];
		                      }
		                  }
		                  return params;
		              },
		            
					  handleData(friendDatas){
						 for(var i=0;i<friendDatas.length;i++){
							 var friendData=friendDatas[i];
							 if(friendData.type=="map"){
								 friendData.chat.content="[位置]";
							 }else if(friendData.type=="image"){
								 friendData.chat.content="[图片]";
							 }else if(friendData.type=="wechat"){
								 friendData.chat.content="[二维码]";
							 }else if(friendData.type=="build"){
								 friendData.chat.content="[来源]";
							 }
						 }
						  return friendDatas;
					  },
					  analysisMessage(message){
					  				  var itemExpression=message;
					    
					  				  var expressionItems=itemExpression.split(/\[|\]/);
					      
					  				  var messageItems=[];
					  				  for(var i=0;i<expressionItems.length;i++){
					  					  var expressionItem=expressionItems[i]
					  					  var messageItem={}
					  					  if((i%2)==0){
					  						  messageItem.text=expressionItem;
					  						  messageItem.type=0;//文本消息
					  					  }else{
					  					  	
					              var eIndex=this.list.indexOf(expressionItem);
					  						 if (eIndex >= 0) {
					                 var imgUrl=this.list_img[eIndex];
					                 messageItem.type=1;//图片消息
					                 messageItem.icon=imgUrl;
					                 messageItem.text = "["+expressionItem+"]";
					  						 } else  {
					                 messageItem.text = "["+expressionItem+"]";
					                 messageItem.type=0;//文本消息
					  						 }
					            
					  
					  					  }
					  					
					  					  messageItems.push(messageItem);
					  				  }
					  				  return messageItems;
					  				  
					  },
					  close(){
						  this.showHide=false;
					  },
					  suoxiao(){
						  this.showX=true;
						  this.showHide=false;
					  },
					  view(){
						  this.showX=false;
						  this.showHide=true;
					  },
					  setMsg(){ //快捷回复设置
						  this.msgFlag=true;
						  this.commonFlag=false;
						 this.msgContent= this.common[0].content;
						 this.indexCommon=this.common[0].id;
					  },
					  saveCommon(){ //保存常用语
					
					  this.dataCommon={
						  id:this.indexCommon,
						  content:this.msgContent
					  }
					 // console.log("常用语参数："+JSON.stringify(this.dataCommon));
					  this.$userApi.editExpressLanguage(this.dataCommon).then(res=>{ 
					  	      if(res.data.code == 1){
					  	         //console.log("yyj："+JSON.stringify(res.data));
					  	      }
					  	  }).catch(err=>{
					      console.log("yyj"+err)
					     
					  })
						
					  },
					  closeMsg(){
						  this.msgFlag=false;
					  },
					  changeItem(e){ //选择常用语
						var index=e.target.value;
						this.msgContent=this.common[index].content;
						this.indexCommon=this.common[index].id
						  
					  },
					  clickSend(index){
						  var msgText=this.common[index].content;
						   var input=document.getElementById("local-message-container");
						   var className="placeHolder";
						   if(input.classList.contains(className) && input.innerHTML){
						   					  input.setAttribute("class","")
						   }else{
						   					  input.setAttribute("class",className);
						   }
						   input.append(msgText);
						   this.commonFlag=false;
						
					  },
					  click_weixin(){
						  //alert(this.to_member.wechat_img);
						  if(this.to_member.wechat_img){
							   this.sendMessage(this.to_member.wechat_img,"wechat");
						  }else{
							  this.$message({
							    message: '对方还未上传二维码',
							    type: 'warning'
							  });
						  }
						 
						  setTimeout(function () {
						      var  message = document.getElementById("message");
						      message.scrollTop = message.scrollHeight;
						  },100);
					  },
					 phone(){
						  if(this.member.tel){
							   this.tel=this.member.tel;
							   this.telShow=true;
						  }
					  }, 
					  viewCode(data){ //查看二维码
						  this.vodeShow=true;
						  this.vodeImg=data;
						  
						  
					  },
					  closeVode(){ //关闭二维码
						    this.vodeShow=false;
					  },
					
					  ajaxCommon(){ 
						 this.$userApi.getExpressLanguage().then(res=>{
						      if(res.data.code == 1){
						         console.log("yyj："+JSON.stringify(res.data));
								 this.common=res.data.express_language;
								
						      }
						  }).catch(err=>{
                        console.log("yyj"+err)
                       
                    })
					  },
 
				
					 
					 makeDrag:function () {
					     this.dragDiv = document.getElementById("div2");
					     /*拖动 start*/
					     var div2=this.dragDiv;
					 
					     var flag = false;//鼠标|手指是否按下的标记
					     var cur = {//记录鼠标|手指按下时的坐标
					         x:0,
					         y:0
					     }
					     var nx,ny,dx,dy,x,y ;
					     //按下时的函数
					     function down(){
					         flag = true;//确认鼠标|手指按下
					         var touch ;
					         if(event.touches){
					             touch = event.touches[0];//多个鼠标|手指事件取第一个
					         }else {
					             touch = event;
					         }
					         cur.x = touch.clientX; //记录鼠标|手指当前的x坐标
					         cur.y = touch.clientY;//记录鼠标|手指当前的y坐标
					         dx = div2.offsetLeft;//记录div当时的左偏移量
					         dy = div2.offsetTop;//记录div的上偏移量
					     }
					     function move(){
					         if(flag){//如果是鼠标|手指按下则继续执行
					             var touch ;
					             if(event.touches){
					                 touch = event.touches[0];
					             }else {
					                 touch = event;
					             }
					             nx = touch.clientX - cur.x;//记录在鼠标|手指x轴移动的数据
					             ny = touch.clientY - cur.y;//记录在鼠标|手指y轴移动的数据
					             x = dx+nx; //div在x轴的偏移量加上鼠标|手指在x轴移动的距离
					             y = dy+ny; //div在y轴的偏移量加上鼠标|手指在y轴移动的距离
					             div2.style.left = x+"px";
					             div2.style.top = y +"px";
					             //阻止页面的滑动默认事件
					             document.addEventListener("touchmove",function(){
					                 event.preventDefault();
					             },false);
					         }
					     }
					     //鼠标|手指释放时候的函数
					     function end(){
					         flag = false; //鼠标|手指释放
					     }
					     //alert(div2);
					     /* var div2=document.getElementById("div2"); */
					     div2.addEventListener("mousedown",function(){
					         down();
					     },false);
					     div2.addEventListener("touchstart",function(){
					         down();
					     },false)
					     div2.addEventListener("mousemove",function(){
					         move();
					     },false);
					     div2.addEventListener("touchmove",function(){
					         move();
					     },false)
					     document.body.addEventListener("mouseup",function(){
					         end();
					     },false);
					     div2.addEventListener("touchend",function(){
					         end();
					     },false);
					     /*拖动 end*/
					 }, 
					  
				  },
				
				
				  computed:{
					  seesion:function(){
						  var obj=this.seesionList[this.seesionIndex];
						   return obj;
					  },
					  seesionBack:function(){
						  var obj=this.seesionListBack[this.seesionIndexBack]
						  return obj
					  }
				  } 
				
}				
				
</script>

<style>
	@import url("css/chat.less");
</style>
