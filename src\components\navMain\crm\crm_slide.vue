<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm div row">
          <div class="slide-title">
            管理员可将客户详情、专属名片等配置到企微的聊天侧边栏，方便员工与客户或客户群沟通时使用
          </div>
        </div>
        <div class="content-box-crm div row" style="margin-top:24px">
          <div class="slide-content">
            <span>侧边栏配置引导</span>
            <div class="i-box">
              <div class="slide-input div row">
                <span class="l">客户详情：</span>
                <div class="input">{{ detail }}</div>
                <div class="copy" @click="$onCopyValue(detail)">复制</div>
              </div>
              <div class="slide-input div row">
                <span class="l">文件库：</span>
                <div class="input">{{ file }}</div>
                <div class="copy" @click="$onCopyValue(file)">复制</div>
              </div>
              <div class="slide-input div row">
                <span class="l">话术库：</span>
                <div class="input">{{ talk }}</div>
                <div class="copy" @click="$onCopyValue(talk)">复制</div>
              </div>
              <!-- <div class="slide-input div row">
                <span class="l">营销素材：</span>
                <div class="input">{{ marketing }}</div>
                <div class="copy" @click="$onCopyValue(marketing)">复制</div>
              </div> -->
              <!-- <div class="slide-input div row">
                <span class="l">CRM：</span>
                <div class="input">{{ marketing }}</div>
                <div class="copy" @click="$onCopyValue(marketing)">复制</div>
              </div> -->
            </div>
          </div>
          <div class="slide-content" style="margin-left:48px;">
            <span>示例图</span>
            <img class="shili" src="https://img.tfcs.cn/backup/static/admin/crm/static/c-4.png" alt="" />
          </div>
        </div>
        <div class="content-box-crm img- div row" style="margin-top:10px;width:100%">
          <div class="l-title row div">
            <span>1.在企业微信管理后台【应用管理】第三方应用-找到腾房SCRM/企微助手</span>
            <span>2.点击第三方应用【腾房SCRM】，进入到第三方应用后台页面，找到【功能】模块的【配置到聊天工具栏】，点击【配置】</span>
          </div>
          <div class="slide-img row div">
            <img src="https://img.tfcs.cn/backup/static/admin/crm/static/c-1.png" alt="" /><img
              src="https://img.tfcs.cn/backup/static/admin/crm/static/c-2.png" alt="" /><img
              src="https://img.tfcs.cn/backup/static/admin/crm/static/c-3.png" alt="" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "crm_slide",
  data() {
    return {
      detail: "",
      file: "",
      talk: "",
      marketing: "",
    };
  },
  mounted() {
    var website_id =
      this.$store.state.website_info.website_id || this.$route.query.website_id;
    this.detail = `https://yun.tfcs.cn/fenxiao/customer/detail?website_id=${website_id}`;
    this.file = `https://yun.tfcs.cn/fenxiao/customer/file?website_id=${website_id}&type=1`;
    this.talk = `https://yun.tfcs.cn/fenxiao/customer/talk?website_id=${website_id}`;
    this.marketing = `https://yun.tfcs.cn/fenxiao/customer/index?website_id=${website_id}`;
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .top {
    align-items: center;
  }

  .slide-title {
    line-height: 60px;
    color: #8a929f;
    background: #e7f3fd;
    padding: 0 24px;
    width: 100%;
  }

  .slide-content {
    .i-box {
      margin-left: 42px;
      margin-top: 30px;

      .slide-input {
        margin-bottom: 20px;
        align-items: center;

        .l {
          color: #8a929f;
          width: 100px;
        }

        .input {
          width: 410px;
          color: #8a929f;
          border-radius: 4px;
          font-size: 14px;
          border: 0.5px solid #dde1e9;
          padding: 0 12px;
          line-height: 40px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
        }

        .copy {
          font-size: 16px;
          color: #2d84fb;
          cursor: pointer;
          margin-left: 20px;
        }
      }
    }

    .shili {
      margin-top: 30px;
      display: block;
      width: 600px;
    }
  }
}

.img- {
  flex-direction: column;
}

.l-title {
  color: #2e3c4e;
  justify-content: space-between;
  font-size: 14px;
  width: 100%;

  span {
    display: inline-block;
  }
}

.slide-img {
  margin-top: 26px;
  justify-content: space-between;
  flex: 1;
  flex-wrap: wrap;

  img {
    display: inline-block;
    margin-bottom: 48px;
  }
}
</style>
