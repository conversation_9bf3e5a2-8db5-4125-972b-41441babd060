<template>
  <el-form ref="form" :model="form" label-width="120px" label-position="left">
    <div class="form_block">
      <div class="title">
        <span> 业主信息 </span>
        <el-tooltip placement="top-start" width="200" trigger="hover" effect="light" v-if="isOpenShowingSingle">
          <div slot="content" style="line-height: 1.5">
            单边代理已开启，仅同店和平台巡检可查看
          </div>
          <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
          <span icon="el-icon-info" type="danger"><i class="el-icon-info" style="color: #f56c6c"></i></span>
        </el-tooltip>
        <!-- <span class="add_owner">添加</span> -->
      </div>
      <template v-if="form_data.tel && form_data.tel.length">
        <template v-for="(item, index) in ownerList">
          <el-form-item label="业主姓名" :key="index">
            <!-- :disabled="
                form_data.tel[index] && form_data.tel[index].owner
                  ? true
                  : false
              " -->
            <el-input style="width: 180px" type="text" v-if="form.tel[index]" v-model="form.tel[index].owner"
              placeholder="请输入">
            </el-input>
            <el-select placeholder="请选择" style="width: 100px" v-if="form.tel[index]" v-model="form.tel[index].sex">
              <!--如果是select或者checkbox 、Radio就还需要选项信息-->
              <el-option label="先生" :value="1"></el-option>
              <el-option label="女士" :value="2"></el-option>
            </el-select>
            <template v-if="index == 0 && ownerList.length < 3">
              <el-button type="primary" style="margin-left: 10px" @click="addOwner">添加</el-button>
            </template>
            <template v-if="index !== 0">
              <!-- :disabled="
                  form_data.tel[index] && form_data.tel[index].owner
                    ? true
                    : false
                " -->
              <el-button type="warning" style="margin-left: 10px" @click="delOwner(index)">删除</el-button>
            </template>
          </el-form-item>
          <el-form-item label="业主手机号" :key="index + '_2'">
            <!-- 
              :disabled="
                form_data.tel[index] && form_data.tel[index].owner_tel
                  ? true
                  : false
              " -->
            <el-input style="width: 180px" v-if="form.tel[index]" v-model="form.tel[index].owner_tel" placeholder="请输入"
              maxlength="11"></el-input>
            <el-select v-if="form.tel[index]" v-model="form.tel[index].type" style="width: 100px" placeholder="请选择">
              <!--如果是select或者checkbox 、Radio就还需要选项信息-->
              <el-option v-for="(item, index) in typeList" :key="index" :label="item.name"
                :value="item.values"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </template>
      <el-form-item label="隐私保护" v-if="privacy_status">
        <el-radio v-model="form.privacy_type" :label="1" border>同店可查看</el-radio>
        <el-radio v-model="form.privacy_type" style="margin-right: 0" border :label="2">联系维护人</el-radio>
        <el-tooltip placement="top-start" width="200" trigger="hover" effect="light">
          <div slot="content">
            <!-- <div> -->
            1,房源业主电话将隐藏不显示<br />
            <!-- </div> -->
            <!-- <div> -->
            2,只能联系房源维护人<br />
            <!-- </div> -->
            <!-- <div> -->
            3,当前成员设置上限为{{ privaceNum }}条
            <!-- </div> -->
          </div>
          <!-- slot="reference" -->
          <span icon="el-icon-info" type="danger"><i class="el-icon-info" style="color: #f56c6c"></i></span>
        </el-tooltip>
      </el-form-item>

      <!-- <el-form-item label="业主姓名">
        <el-input
          style="width: 180px"
          type="text"
          v-model="form.owner"
          placeholder="请输入"
        ></el-input>
        <el-button type="warning" size="mini">删除</el-button>
      </el-form-item>
      <el-form-item label="业主手机号">
        <el-input
          style="width: 180px"
          type="number"
          v-model="form.owner_tel"
          placeholder="请输入"
        ></el-input>
      </el-form-item> -->
    </div>
    <el-form-item label="房源备注">
      <el-input v-model="form.memo" :disabled="form.memo_edit == 1 ? false : true" type="textarea" style="width: 290px"
        placeholder="请输入跟进内容 企业内公开" :autosize="{ minRows: 4 }" maxlength="200" show-word-limit></el-input>
      <!-- <span>m²</span> -->
    </el-form-item>

    <div class="form_block" v-if="isShowHousePromotion">
      <div class="title">
        <span> 外网信息 </span>
      </div>
      <el-form-item label="推广标题" prop="title_promotion">
        <el-input v-model="form.title_promotion" style="width: 290px" placeholder="请输入推广标题"></el-input>
      </el-form-item>
      <el-form-item label="特色卖点" prop="description">
        <el-input v-model="form.description" type="textarea" style="width: 290px" placeholder="请输入特色卖点"
          :autosize="{ minRows: 4 }" maxlength="300" show-word-limit></el-input>
      </el-form-item>
    </div>

  </el-form>
</template>

<script>
// import dayjs from 'dayjs'
export default {
  name: "LmAddForm2",
  components: {},
  data() {
    return {
      from_focus: 0,
      fangxing: [3, 2, 2],
      form: {
        title: "",
        tel: [
          {
            id: '',
            owner: "",
            owner_tel: "",
            type: 1,
            sex: 1,
            is_del: 0
          },
        ],
        memo_edit: 1,
        privacy_type: 1,
        memo: '',
        title_promotion: '',
        description: '',
        // sell_open_user: '',
        // rent_open_user: '',
        // survey_user: '',
      },
      lookDay: "",
      startTime: "",
      endTime: "",
      timeRange: "",
      typeList: [],
      privaceNum: 0,
      ownerList: [
        {
          id: '',
          owner: "",
          owner_tel: "",
          type: 1,
          sex: 1,

        },
      ],
    };
  },
  props: {
    form_options: {
      type: Object,
      default: () => { },
    },
    house_type: {
      type: Number,
      default: 1,
    },
    form_data: {
      type: Object,
      default: () => { },
    },
    isOpenShowingSingle: {
      type: [String, Boolean, Number],
      default: false,
    }
  },
  computed: {
    //是否显示外网信息
    isShowHousePromotion() {
      return this.form_options?.house_promotion?.values == 1;
    }
  },
  watch: {},
  created() {
    this.getTelTypeList();

    if (this.form_data.tel && this.form_data.tel.length > 0) {
      this.ownerList = [];
      this.form_data.tel.map((item) => {
        item.is_del = 0
        if (item.owner && item.owner_tel) {
          item.change = true;
        } else {
          item.change = false;
        }
        this.ownerList.push(JSON.parse(JSON.stringify(item)));
      });
      console.log(this.ownerList, 'this.ownerList')
    } else {
      this.form_data.tel = [
        {
          id: '',
          owner: "",
          owner_tel: "",
          type: 1,
          sex: 1,
          is_del: 0
        },
      ];
    }
    // this.ownerList = params.tel
    for (let key in this.form_data) {
      if (
        this.form[key] !== undefined ||
        (this.form[key] && this.form[key].length === 0)
      ) {
        this.form[key] = this.form_data[key];
      }
    }
    this.privaceNum = this.form_data.num_privacy
    this.privacy_status = this.form_data.privacy_status && this.isOpenShowingSingle > 0
  },
  methods: {
    setForm(params) {
      this.ownerList = [];
      console.log(params);
      // this.ownerList = params.tel
      params.tel.map((item) => {
        this.ownerList.push(JSON.parse(JSON.stringify(item)));
      });
      for (let key in params) {
        if (
          this.form[key] !== undefined ||
          (this.form[key] && this.form[key].length === 0)
        ) {
          this.form[key] = params[key];
        }
      }
      // // 设置集中看房时间
      // if (params.showing_stime && params.showing_etime) {
      //   this.lookDay = dayjs(new Date(params.showing_stime * 1000)).format(
      //     'YYYY-MM-DD'
      //   )
      //   this.startTime = dayjs(new Date(params.showing_stime * 1000)).format(
      //     'HH:mm'
      //   )
      //   this.endTime = dayjs(new Date(params.showing_etime * 1000)).format(
      //     'HH:mm'
      //   )
      // }
    },
    onHuxingChange(e) {
      // console.log(e)
      this.form.shi = e[0];
      this.form.ting = e[1];
      this.form.wei = e[2];
    },
    setShowTime() {
      // console.log(this.lookDay)
      if (this.lookDay && this.startTime && this.endTime) {
        this.form.showing_stime = `${this.lookDay} ${this.startTime}`;
        this.form.showing_etime = `${this.lookDay} ${this.endTime}`;
      }
    },
    addOwner() {
      if (this.ownerList.length < 3) {
        this.ownerList.push({
          owner: "",
          owner_tel: "",
          type: 1,
          sex: 1,
        });

        this.form.tel.push({
          owner: "",
          owner_tel: "",
          type: 1,
          sex: 1,
        });
        console.log(this.form.tel, 'this.form.tel')
      }
    },
    delOwner(index) {
      this.ownerList.splice(index, 1);
      // this.form.tel.splice(index, 1);
      this.form.tel[index].is_del = 1;
    },
    getTelTypeList() {
      this.$ajax.house.getTelTypeList().then((res) => {
        if (res.status == 200) {
          this.typeList = res.data;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.form_block {
  margin-bottom: 48px;

  >.title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;

    .add_owner {
      margin-left: 180px;
      padding: 5px 10px;
      background: #409eff;
      color: #fff;
      border-radius: 3px;
      font-size: 14px;
      font-weight: normal;
    }
  }

  .remarks {
    font-size: 14px;
    font-weight: initial;
  }

  .el-form-item {
    color: #8a929f;

    .tip {
      color: #fe6c17;
    }

    .unit {
      margin-left: 12px;
    }

    ::v-deep .el-form-item__label {
      font-weight: bold;
      color: #2e3c4e;
    }

    ::v-deep .el-input {
      width: 240px;
    }

    ::v-deep .el-textarea textarea {
      resize: none;
    }

    .el-date-editor {
      width: 130px;

      &:first-child {
        margin-right: 6px;
        width: 150px;
      }
    }
  }
}

.form_block ::v-deep .el-select {
  margin-left: 10px;

  .el-input {
    width: 100%;
  }
}
</style>
