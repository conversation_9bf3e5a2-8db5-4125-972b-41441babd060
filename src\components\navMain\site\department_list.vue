<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm div row" style="margin-bottom: 24px">
          <div>
            <el-button
              type="primary"
              @click="addDepartment"
              size="mini"
              class="el-icon-plus"
              >新建部门</el-button
            >
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ tableData.length }}</span>
              <span class="text">条</span>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="TableRowStyle"
          >
            <el-table-column
              prop="departmentid"
              label="部门ID"
              width="100"
            ></el-table-column>
            <el-table-column prop="name" label="部门名称"></el-table-column>
            <el-table-column prop="parentid" label="上级部门"></el-table-column>
            <el-table-column prop="order" label="排序"></el-table-column>
            <el-table-column
              prop="created_date"
              label="创建日期"
            ></el-table-column>
            <el-table-column
              prop="updated_date"
              label="更新日期"
            ></el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog title="新建部门" :visible.sync="dialogCreate">
      <el-form :model="departmentCreate" label-width="120px">
        <el-form-item label="部门名称：">
          <el-input
            maxlength="20"
            v-model="departmentCreate.name"
            placeholder="请填写部门名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="上级部门ID：">
          <el-input
            maxlength="20"
            v-model="departmentCreate.parentid"
            placeholder="请填写上级部门ID"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序：">
          <el-input
            maxlength="20"
            v-model="departmentCreate.order"
            placeholder="请填写序号"
          ></el-input>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="onCreate"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "department_list",
  components: {},
  data() {
    return {
      tableData: [],
      is_table_loading: true,
      inputVal: "",
      departmentCreate: {
        name: "",
        parentid: "",
        order: "",
      },
      dialogCreate: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
    };
  },
  computed: {},
  watch: {},
  methods: {
    getDepartmentList() {
      this.is_table_loading = true;
      this.$http.getDepartmentList().then((res) => {
        console.log(res, "部门列表");
        if (res.status === 200) {
          this.tableData = res.data;
          this.is_table_loading = false;
        }
      });
    },
    addDepartment() {
      this.dialogCreate = true;
    },
    onCreate() {
      if (
        !this.departmentCreate.name ||
        !this.departmentCreate.parentid ||
        !this.departmentCreate.order
      ) {
        this.$message.error("请补全信息");
        return;
      }
      let msg = this.$message.success("请稍后...");
      this.$http.departmentCreate(this.departmentCreate).then((res) => {
        if (res.status === 200) {
          this.$message.success("创建成功");
          this.getDepartmentList();
          this.dialogCreate = false;
          this.departmentCreate.name = "";
          this.departmentCreate.parentid = "";
          this.departmentCreate.order = "";
          msg.close();
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    TableRowStyle({ rowIndex }) {
      let rowBackground = {};
      if ((rowIndex + 1) % 2 === 0) {
        rowBackground.background = "#EFEFEF";
        return rowBackground;
      }
    },
  },
  created() {},
  mounted() {
    this.getDepartmentList();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.addBtn {
  margin-bottom: 30px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
.el-button {
  border-radius: 3px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 400px;
}
</style>
