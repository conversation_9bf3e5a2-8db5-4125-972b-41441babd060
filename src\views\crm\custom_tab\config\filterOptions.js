import Http from '@/utils/http.js';
const Options = {
    //时间类型
    dateStyleList: [
        {value: 1, label: '创建时间'},
        {value: 2, label: '跟进时间'},
        {value: 3, label: '线索时间'},
        {value: 4, label: '更新时间'},
        {value: 5, label: '掉公时间'},
        {value: 6, label: '转公时间'}
    ],
    //时间周期类型
    dataTypes: [
        { value: 2, label: '前' },
        { value: 1, label: '内' },
    ],
    //跟进类型
    followTypes: [
        { value: 2, label: "已跟进" },
        { value: 3, label: "未跟进" },
    ],
    //邀约类型
    inviteList: [
        { id: 1, name: "查看电话" },
        { id: 2, name: "已接通（外呼）" },
        { id: 3, name: "未接通（外呼）" },
        { id: 4, name: "已带看" },
        { id: 5, name: "未带看" },
        { id: 6, name: "有复看" },
        { id: 7, name: "修改资料" },
        { id: 8, name: "修改标签" },
        { id: 9, name: "修改等级" },
    ],
    //通话状态
    callStatusList: [
        //{ id: 0, title: "全部" },
        { id: 1, title: "未联系" },
        { id: 2, title: "未接通" },
        { id: 3, title: "已接通" },
    ],
    //客户来源
    customerSourceList: [],
    //客户状态
    customerTrackingList: [],
    //客户类型
    customerTypeList: [],
    //客户等级
    customerLevelList: []
}
export default {
    getOptions(tabType){
        const followTypes = Options.followTypes.slice();
        switch(tabType){
            //公海客户
            case 2:
                followTypes.push({value: 9, label: '待分配'});
                followTypes.push({value: 10, label: '已转公'});
                followTypes.push({value: 11, label: '已掉公'});
                break;
            //潜在客户
            case 3:
                followTypes.push({value: 10, label: '已转公'});
                followTypes.push({value: 11, label: '已掉公'});
                break;
        }
        return {...Options, followTypes};
    },
    //获取客户来源
    async getCustomerSourceList(cb){
        const res = await Http.listcustomersourcenew();
        if(res.status == 200){
            const data = res.data.map(e => {
                if(e.children && e.children.length == 0){
                    delete e.children;
                }
                return e;
            });
            cb && cb(data);
            return data;
        }
    },
    //客户状态
    async getCustomerTrackingList(cb){
        const res = await Http.getCrmCustomerFollowInfo({params:{type:4}});
        if(res.status == 200){
            const data =  (res.data || []).map(e => {
                return {...e, value: e.id, label: e.title};
            });
            cb && cb(data);
            return data;
        }
    },
    //客户类型
    async getCustomerTypeList(cb){
        const res = await Http.getCrmCustomerTypeDataNopage();
        if(res.status == 200){
            const data = (res.data || []).map(e => {
                return {...e, value: e.id, label: e.title};
            });
            cb && cb(data);
            return data;
        }
    },
    //客户等级
    async getCustomerLevelList(cb){
        const res = await Http.getCrmCustomerLevelNopage();
        if(res.status == 200){
            const data = (res.data || []).map(e => {
                return {...e, value: e.id, label: e.title + ' '+e.desc};
            });
            cb && cb(data);
            return data;
        }
    },
}