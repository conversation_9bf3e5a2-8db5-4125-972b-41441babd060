<template>
<div class="tab-content-container">
<div class="tab-content-body main-scroll">
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="5">
        <div class="content-box-crm content-crm" style="padding: 0">
          <div class="bmtitle add_department div row">
            <!-- <div class="add_depart" @click="addDepartment">+</div> -->
          </div>
          <div class="my_slide">
            <!-- <el-menu class="menu-bar"> -->
            <!--   -->

            <menuTree :menuData="navList" :keyData="keyData" :menu_list="menu_list" @clickItem="onClickNavItem"
              @onClickMenu="onClickMenu"></menuTree>
            <!-- </el-menu> -->
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="content-box-crm" style="width: 100%">
          <div class="table-top-box div row align-center">
            <div class="t-t-b-left div row">
              <span class="text">已选</span>
              <div class="blue">{{ multipleSelection.length }}</div>
              <span class="text">个</span>
            </div>
            <div class="content-box-crm padd0">
              <el-input :placeholder="'请输入'+(params.searchType=='user_name'?'用户名' : '手机号')"  v-model="params.searchKeyword" size="small">
                <el-select v-model="params.searchType" slot="prepend" placeholder="请选择" style="width:90px">
                  <el-option label="用户名" value="user_name"></el-option>
                  <el-option label="手机号" value="phone"></el-option>
                </el-select>
              </el-input>
            </div>
            <div class="content-box-crm padd0">
              <el-button type="primary" @click="search" size="mini" class="el-icon-search">搜索</el-button>
            </div>
            <template v-if="!$store.state.disableClick">
              <div class="content-box-crm padd0" v-if="website_info.self_auth_create_all && hasRole">
                <el-button type="primary" v-if="false" @click="importConcat" size="mini">同步企微通讯录</el-button>
              </div>

              <template v-if="hasRole">
                <div class="content-box-crm padd0">
                  <el-button type="primary" @click="toAdd" size="mini" class="el-icon-plus">添加成员</el-button>
                </div>
                <div class="content-box-crm padd0">
                  <el-button type="primary" @click="is_uploadclick" size="mini">导入</el-button>
                  <input type="file" ref="file_p" accept=".xls,.doc,.txt,.xlsx" style="display: none"
                    v-on:change="handleFileUpload($event)" />
                </div>
                <div class="content-box-crm padd0">
                  <el-button type="primary" @click="setInher(1)" size="mini">离职继承</el-button>
                </div>
                <div class="content-box-crm padd0">
                  <el-button type="primary" @click="setInher(2)" size="mini">在职继承</el-button>
                </div>
                <div class="content-box-crm padd0">
                  <el-button type="primary" @click="onClickBind" size="mini">绑定角色</el-button>
                </div>
                <div class="content-box-crm padd0">
                  <el-button type="primary" @click="onClickRoles" size="mini">角色管理</el-button>
                </div>
                
              </template>
            </template>
          </div>
          <el-table v-loading="is_table_loading" :data="tableData" border class="table"
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange" ref="table">
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column
              label="ID"
              min-width="30px"
              align="center"
              prop="id"
            >
            </el-table-column> -->
            <el-table-column label="成员信息" min-width="120px" align="center" v-slot="{ row }">
              <div class="row-info flex-row align-center">
                <div class="row-img">
                  <img v-if="row.wx_work_avatar" :src="row.wx_work_avatar" alt="" />
                  <div class="row_no_img flex-row align-center j-center" v-else-if="row.user_name">
                    <span>
                      {{ row.user_name[0] }}
                    </span>
                  </div>
                  <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
                </div>
                <div class="info">
                  <div class="row-name flex-row align-center">
                    <div class="name">
                      {{ row.user_name }}
                    </div>
                    <el-tooltip class="item" effect="light" content="已授权企微" placement="top">
                      <div class="wechat" v-if="row.wx_work_user_id">
                        <img src="https://img.tfcs.cn/xinfang/qiwei.png" alt="" />
                      </div>
                    </el-tooltip>
                    <el-tooltip class="item" effect="light" content="已授权公众号" placement="top">
                      <div class="wechat" v-if="row.is_wx_auth">
                        <img src="https://img.tfcs.cn/xinfang/gongzhonghao.png" alt="" />
                      </div>
                    </el-tooltip>
                   
                      <div v-if="row.id == user_info.id" class="flex-row">
                        <el-tooltip class="item" effect="light" content="未授权抖音,可点击授权" placement="top">
                        <el-popconfirm v-if="!row.byte_open_id" title="确定绑定抖音吗？" @onConfirm="douyin(row)">
                          <span slot="reference">
                            <div class="wechat cur_point">
                              <img v-if="!row.byte_open_id" src="@/assets/icon/douyinhui.png" />
                            </div>
                          </span>
                        </el-popconfirm>
                      </el-tooltip>
                        <el-tooltip class="item" effect="light" content="已授权抖音" placement="top">
                        <div v-if="row.byte_open_id" class="wechat cur_point">
                          <img v-if="row.byte_open_id" src="@/assets/icon/douyin.png" />
                        </div>
                      </el-tooltip>
                      </div>
                  </div>
                  <div class="row-info flex-row">
                    <div class="tel">
                      {{ row.phone }}
                    </div>
                  </div>
                </div>
              </div>
            </el-table-column>
            <el-table-column label="所属部门" min-width="60px" align="center" v-slot="{ row }">
              <template v-if="row.department && row.department.length">
                <span class="name" v-for="(item, index) in row.department" :key="index + '_'">{{ item.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职位" min-width="60px" align="center" v-slot="{ row }">
              <el-tag v-if="row.post" size="mini" style="margin: 5px" type="success">{{ row.post }}</el-tag>
              <!-- <span class="name">{{ row.post }}</span> -->
              <el-tag size="mini" style="margin: 5px" v-for="item in row.roles" :key="item.id">{{ item.name === "站长" ?
                              "创始人" : item.name }}</el-tag>
            </el-table-column>
            <el-table-column label="状态" min-width="60px" align="center" v-slot="{ row }">
              <el-tag v-if="row.status == 1" type="success"> 启用 </el-tag>
              <el-tag v-else type="danger"> 禁用 </el-tag>

              <!-- <el-tag v-if="row.share_no_follow == 1" type="danger"> 限制分享进入私客 </el-tag>
              <el-tag v-if="row.share_no_follow == 0" type="success"> 不限制 </el-tag> -->
            </el-table-column>
            <el-table-column label="操作" min-width="60px" align="center" v-slot="{ row }" v-if="hasRole">
              <template v-if="!(
                              row.roles &&
                              row.roles.length &&
                              row.roles[0].name == '站长'
                            )
                              ">
                <el-link v-if="row.status == 1" type="danger" @click="fastConfig(row)"
                  style="margin: 0 10px">禁用成员</el-link>
                <el-link v-if="row.status == 0" type="success" @click="fastConfig(row)"
                  style="margin: 0 10px">启用成员</el-link>
              </template>
              <template v-if="!$store.state.disableClick">
                <el-link v-if="hasRole" type="primary" style="margin: 0 10px" icon="el-icon-edit"
                  @click="toEdit(row)">编辑</el-link>
              </template>
              <!-- <template>
                <el-link v-if="row.share_no_follow == 1" type="success" style="margin: 0 10px" 
                  @click="notshare(row)">启用分享</el-link>
                <el-link v-if="row.share_no_follow == 0" type="danger" style="margin: 0 10px" 
                  @click="notshare(row)">禁止分享</el-link>
              </template> -->
              <!-- <el-popconfirm
                v-if="hasRole && isShowRow(row.roles)"
                title="确定删除吗？"
                style="margin: 0 10px"
               "
              > -->
              <template v-if="hasRole && isShowRow(row.roles)">
                <div>
                  <el-link type="danger" icon="el-icon-delete" @click="deleteMember(row)">删除</el-link>
                </div>
              </template>

              <!-- </el-popconfirm> -->
              <template v-if="!$store.state.disableClick">
                <el-link v-if="isShowRow(row.roles)" style="margin: 0 10px" @click="bindRoles(row)"
                  type="primary">绑定角色</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="show_edit_member_dia" width="660px" title="编辑成员（请完善您的资料）">
      <editMember v-if="show_edit_member_dia" :form="form_params" :navList="navList" @success="editOk"
        @cancel="cancelEdit" :website_info="website_info"></editMember>
    </el-dialog>

    <el-dialog :visible.sync="show_add_member_dia" width="660px" title="添加成员（请完善您的资料）">
      <addMember v-if="show_add_member_dia" :navList="navList" :website_info="website_info" @success="addOk"
        @cancel="cancelAdd"></addMember>
    </el-dialog>

    <el-dialog :visible.sync="show_add_department" width="660px" title="添加子部门">
      <addDepartment v-if="show_add_department" :department_pid="department_pid" @success="addDepartmentOk"
        @cancel="cancelAddDepartment" :website_info="website_info"></addDepartment>
    </el-dialog>
    <el-dialog :visible.sync="show_edit_department" width="660px" title="编辑部门" :before-close="handleClose">
      <!-- :options="navList" -->
      <editDepartment v-if="show_edit_department" :form="currentDepartment" @success="editDepartmentOk"
        @cancel="cancelEditDepartment" :website_info="website_info" :destroy-on-close="true"></editDepartment>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_upload_dialog" title="导入">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">
        <span style="margin-left: 1rem">1）是否关联企业微信：</span>
        <el-switch v-model="is_associated_wxwork" :active-value="1" :inactive-value="0">
        </el-switch>
      </div>
      <div class="labelrow" style="color: red">
        企微管理后台->通讯录导入示例：
      </div>
      <img width="100%" src="https://img.tfcs.cn/backup/static/admin/crm/static/qwrenshi.png" alt="" />
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <div v-if="warningdata.is_show==1&&warningdata.msg">
        <span class="text" style="color: #f85d02;">8、{{ warningdata.msg }}</span>
      </div>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
        >添加文件</el-button
      > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_upload_dialog = false">取 消</el-button>
        <el-button type="primary" :disabled="disabledA" @click="$refs.file_p.click()">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog title="绑定角色" width="400px" :visible.sync="dialogCreateRoles">
      <el-form :model="create_form" label-width="100px" inline>
        <el-form-item label="角色名称:" prop="role_name">
          <el-select v-model="create_form.role_names" multiple placeholder="请选择">
            <el-option v-for="item in roles_list" :key="item.id" :label="item.name === '站长' ? '创始人' : item.name"
              :value="item.name" :disabled="item.name === '站长'"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="margin: 0" @click="submitData">提交</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="is_position == 1 ? '离职继承' : '在职继承'" width="660px" :visible.sync="is_inheritance_dialog">
      <el-form :model="inheritance_form" label-width="100px">
        <el-form-item :label="is_position == 1 ? '离职人员' : '在职人员'">
          <el-select v-model="inheritance_form.from_id" placeholder="请选择成员" filterable style="width: 300px">
            <el-option v-for="item in is_position == 1 ? user_list : tableDatapage" :label="item.user_name" :key="item.id"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="继承成员">
            <el-select v-model="inheritance_form.to_id" placeholder="请选择继承成员" filterable style="width: 300px">
              <el-option v-for="item in tableDatapage" :label="item.user_name" :key="item.id"
                :value="item.id"></el-option>
            </el-select>
            
            <div>
              <span class="danger-alert">操作后维护人将变更，不可恢复（无记录），请谨慎操作</span>
            </div>
        </el-form-item>

        <div style="line-height: 2;" v-if="inheritProgress.show">
          <p>正在处理，请稍后...</p>
          <el-progress :percentage="inheritProgress.pct"></el-progress>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="margin: 0" @click="submitDatainheritance" :loading="dialogs.inheritance_form">提交</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <div style="max-height: 70vh; overflow-y: auto">
        <memberListSingle v-if="show_member_list" :list="memberList" source="renshi" :defaultValue="selectedIds"
          @onClickItem="selecetedMember" ref="memberList">
        </memberListSingle>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_dialog_list" width="300px" title="绑定到抖音">
      <iframe style="border: none" width="300px" height="300px" v-bind:src="inframe"></iframe>
    </el-dialog>
    <el-dialog :visible.sync="show_qrcode" width="600px" title="发布到抖音">
      <div class="flex-row align-center j-center" style="width: 100%; height: 500px">
        <div id="qrcode" class="u_qrcode" ref="qrCodeUrl"></div>
      </div>

      <div></div>
    </el-dialog>
    
    <resetPassword v-if="dialogs.resetPassword" ref="resetPassword"/>
    <bindRoles v-if="dialogs.bindRoles" ref="bindRoles"/>
  </div>
</div>

<div class="tab-content-footer">
    <div>
        <el-dropdown @command="handleBatchCommand" trigger="click" style="margin-right: 12px">
          <el-button type="primary" size="small">批量设置 <i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="resetPassword">初始化密码</el-dropdown-item>
            <el-dropdown-item command="disabledMember">禁用成员</el-dropdown-item>
            <el-dropdown-item command="enabledMember">启用成员</el-dropdown-item>
            <el-dropdown-item command="bindRoles">绑定角色</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" size="small" @click="getMemberList" :loading="is_table_loading">刷新</el-button>
    </div>
    <el-pagination background :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
        :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
    </el-pagination>
</div>
</div>
</template>

<script>
import menuTree from "./components/menuTree.vue";
import editMember from "./components/editMember.vue";
import addMember from "./components/addMember.vue";
import addDepartment from "./components/addDepartment.vue";
import editDepartment from "./components/editDepartment.vue";
import memberListSingle from "../../navMain/site/components/memberList_single.vue";
import { Loading } from "element-ui";
import { mapState } from "vuex";
import QRCode from "qrcodejs2";
import resetPassword from './components/crmCustomerPersonnel/resetPassword.vue';
import bindRoles from './components/crmCustomerPersonnel/bindRoles.vue';
// import mySlide from './components/new_slide';
export default {
  name: "crm_customer_personnel",
  components: {
    // mySlide,
    menuTree,
    editMember,
    addMember,
    addDepartment,
    editDepartment,
    memberListSingle,
    resetPassword,
    bindRoles
  },
  data() {
    return {
      navList: [],
      params: {
        page: 1,
        per_page: 10,
        user_name: "",
        phone: "",
        department_id: "",
        searchType: 'user_name',
        searchKeyword: ''
      },
      follow: {
        share_no_follow: "",
        id: "",
      },
      form_params: {},
      total: 0,
      is_table_loading: false,
      multipleSelection: [],
      tableData: [],
      tableDatapage:[],
      show_edit_member_dia: false,
      show_add_member_dia: false,
      menu_list: [],
      show_add_department: false,
      show_edit_department: false,
      department_pid: "",
      currentDepartment: {},
      defaultMenu: "",
      keyData: [],
      is_upload_dialog: false,
      tips: [
        "2、不能在该Excel表中对成员信息类别进行增加、删除或修改；",
        "3、Excel中红色字段为必填字段,黑色字段为选填字段（手机和个人邮箱需选其一填写），字段值里请不要填入“，”字符；",
        "4、帐号：成员的唯一标识，由1-64个字母、数字、点(.)、减号(-)或下划线(_)组成，帐号相同会进行覆盖导入【帐号初始设定后则不支持修改，企业微信系统自动生成的帐号，支持修改一次。修改方法：在帐号字段用英文冒号隔开新旧帐号，示例：old_userid:new_userid】；",
        "5、支持国内、国际手机号（国内手机号直接输入手机号即可；国际手机号必须包含加号以及国家地区码，格式示例：“+85259****24”）；",
        "6、网页类型字段：网页名称与网页地址之间用‘；’隔开，并且网页地址以http://或https://开头，格式示例：企业微信；http://work.weixin.qq.com；",
        "7、视频号：填写视频号名字，对应的视频号必须已绑定到企业微信；",
      ],
      create_form: {
        id: "",
        role_names: [],
      },
      dialogCreateRoles: false,
      roles_list: [],
      is_inheritance_dialog: false,
      inheritance_form: {
        from_id: "",
        to_id: "",
      },
      show_member_list: false,
      selectedIds: [],
      to_username: "",
      user_list: [],
      user_info: {},
      is_associated_wxwork: 1, // 是否关联企微微信
      hasRole: false,
      is_position: 1, //1：离职 2：在职
      website_id: '',
      inframe: '',
      show_dialog_list: false,
      show_qrcode: false,
      share_no_follow:'',
      warningdata:{},
      isOrange:false,
      disabledA:false,
      inheritProgress: {
        show: false,
        pct: 0,
      },
      dialogs: {
        inheritance_form: false,
        resetPassword: false,
        bindRoles: false
      }
    };
  },
  computed: {
    ...mapState(["website_info"]),
  },
  created() {
    // 获取权限
    // this.getRoles()
    //  获取部门
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
    this.getAdminUserInfo();
    this.getDepartmentList();
    this.getWebsiteRoles();
    this.getDepartment();
    this.getResignedUsersData();
    this.website_id = this.$route.query.website_id
    // this.shareDouyin()

    // let href = window.location.href;
    // if (href.includes("?code=")) {  //url包括 com/?code 证明为从微信跳转回来的
    //   var url = href.substring(0, href.length - 2); //vue自动在末尾加了 #/ 符号，截取去掉
    //   var jingPosit = url.indexOf("com/") + 4; //获取域名结束的位置
    //   var urlLeft = url.substring(0, jingPosit);//url左侧部分
    //   var urlRight = url.substring(jingPosit, url.length); //url右侧部分
    //   window.location = urlLeft + "#/" + urlRight;//拼接跳转
    // }
    if (this.$route.query.code) {
      let c = this.$route.query.code
      // let params = this.$queryUrlParams(location.href.split("#")[0]);
      // if (params.code) {
      this.bindDouyin(c)
      // }
    }
    this.handle('https://lf3-static.bytednsdoc.com/obj/eden-cn/fljpeh7hozbf/douyin_open/cdn/dy_open_util_v0.0.6.umd.js', 'douyin_share_script')

  },
  methods: {
    handle(link, type = '') {
      document.getElementById(type) && document.getElementById(type).remove();
      var hm = document.createElement("script");
      hm.src = link;
      hm.id = type || (+new Date() + '')
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    },
    bindDouyin(code) {
      this.$http.bindDouyin(code).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.shareDouyin()
        }
      })
    },
    shareDouyin() {
      this.$http.shareDouyin().then(res => {
        const schema = window.dy_open_util.serialize(res.data);
        this.show_qrcode = true

        this.$nextTick(() => {
          this.$refs.qrCodeUrl.innerHTML = "";
          new QRCode(this.$refs.qrCodeUrl, {
            text: schema, // 需要转换为二维码的内容
            width: 200,
            height: 200,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H,
          });
        });
      })
    },
    douyin(row) {
      //获取授权
      if (row.byte_open_id) {
        // this.shareDouyin()
        return
      }
      let href = location.href
      href += '@@@@@@from@@@@@@crm_customer_personnel'
      href = href.replace("/#/", "/@@@@/")
      this.$http.getDouyinShouquan(href).then(res => {
        if (res.status == 200) {
          const url = res.data
          // let url = decodeURI(res.data).replace("@@@@", "#")
          // url = url.replace("****from****", "&from=")
          // const params = this.$queryUrlParams(res.data);
          // console.log(params);
          // params.redirect_uri = encodeURIComponent(location.href)
          // let url = 'https://open.douyin.com/qrconnect?'
          // for (const key in params) {
          //   url += `${key}=${params[key]}&`
          // }
          // url = url.substring(0, url.length - 1)
          // console.log(url);
          this.show_dialog_list = true
          this.inframe = url
        }
      })

    },
    getAdminUserInfo() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.user_info = res.data;
          // console.log(this.user_info, 11);


          if (res.data.roles && res.data.roles[0].name === "站长") {
            this.hasRole = true;
            this.menu_list = [
              { id: 1, name: "添加子部门" },
              { id: 2, name: "编辑" },
              { id: 3, name: "删除" },
            ];
          }
          else {
            this.getcommonRoles()
            // this.menu_list = [{ id: 2, name: "编辑" }];
          }
        }
      });
    },
    getcommonRoles() {

      this.$http.getAuthCrmShow('personnel_auth').then((res) => {
        if (res.status === 200) {
          let roles = res.data + "";
          if (roles && roles.split(",").includes(this.user_info.id + "")) {
            this.hasRole = true;
            this.menu_list = [
              { id: 1, name: "添加子部门" },
              { id: 2, name: "编辑" },
              { id: 3, name: "删除" },
            ];
          }
        }
      });
    },
    getResignedUsersData() {
      this.$http.getResignedUsersData().then((res) => {
        if (res.status === 200) {
          this.user_list = res.data;
        }
      });
    },
    isShow(role) {
      if (role && role.length && role[0].name === "站长") {
        return true;
      } else {
        return false;
      }
    },
    isShowRow(role) {
      if (role && role.length && role[0].name === "站长") {
        return false;
      } else {
        return true;
      }
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    delName() {
      this.inheritance_form.to_id = "";
      this.to_username = "";
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.to_username = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.inheritance_form.to_id =
          e.checkedNodes[e.checkedNodes.length - 1].id;
      } else {
        this.to_username = "";
        this.inheritance_form.to_id = "";
      }
      this.show_member_list = false;
    },
    setInher(type) {
      if(this.is_position != type){
        this.inheritance_form.from_id = '';
        this.inheritance_form.to_id = '';
      }
      this.is_position = type;
      this.is_inheritance_dialog = true;
    },
    getWebsiteRoles() {
      this.$http.getRolelist().then((res) => {
        if (res.status === 200) {
          this.roles_list = res.data;
        }
      });
    },
    getDepartmentList() {
      this.is_table_loading = true;
      this.$http
        .getCrmDepartmentList()
        .then((res) => {
          if (res.status == 200) {
            this.navList = this.reFormData(res.data);
            if (res.data.length) {
              this.keyData.push(res.data[0].id);
              this.defaultMenu = res.data[0].id;
              if (res.data[0].subs && res.data[0].subs.length) {
                this.keyData.push(res.data[0].subs[0].id);
              }
            }

            this.params.page = 1;
            delete this.params.department_id;
            this.getMemberList();
            this. getcrmcustomer()
          } else {
            this.is_table_loading = false;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    reFormData(data, line = "") {
      data.map((item) => {
        item.pArr = `${line ? line + "," : ""}${item.pid}`;
        if (item.subs && item.subs instanceof Array && item.subs.length) {
          let nameLine = `${line ? line + "," : ""}${item.pid}`;
          this.reFormData(item.subs, nameLine);
        }
      });
      return data;
      // for (let index = 0; index < data.length; index++) {
      //   this.pidArr.push()

      // }
    },
    // 获取成员列表  per_page=10&department_id=44&phone=13791416566&user_name=刘
    getMemberList() {
      let params = Object.assign({}, this.params);
      params[params.searchType] = params.searchKeyword;
      delete params.searchType;
      delete params.searchKeyword;
      // if (params.department_id == this.defaultMenu || !params.department_id) {
      //   delete params.department_id;
      // }
      this.is_table_loading = true;
      this.$http
        .getCrmMemberList({ params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status == 200) {
            this.tableData = res.data.data;
            this.total = res.data.total;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    // 获取全部成员
    getcrmcustomer(){
      this.$http.getMemberListpage().then(res=>{
          if(res.status==200){
            console.log(res.data);
            this.tableDatapage = res.data.map(item => {
              return {
                user_name: item.name,
                id: item.values
              };
            });
          }
      })
    },
    search() {
      this.params.page = 1;
      this.is_table_loading = true;
      this.getMemberList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onClickBind() {
      if (!this.multipleSelection.length) {
        this.$message.error("请选择成员");
        return;
      }
      var ids = this.multipleSelection.map((item) => {
        return item.id;
      });
      this.create_form.id = ids.join(",");
      this.dialogCreateRoles = true;
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getMemberList();
    },
    handleSizeChange(e){
      this.params.per_page = e
      this.getMemberList();
    },
    onClickNavItem(e) {
      if (this.user_info.all_department_id.includes(e.id + '') || this.hasRole) {
        this.params.page = 1;
        this.params.department_id = e.id;
        this.openedArr = [e.pid + ""];
        this.getMemberList();
      }

    },
    onClickMenu(e) {
      if (this.user_info.all_department_id.includes(e.id + '') || this.hasRole) {
        if (e.detail.id == 1) {
          // 添加子部门
          this.addDepartment(e.item);
        } else if (e.detail.id == 3) {
          this.delDepartment(e.item);
        } else if (e.detail.id == 2) {
          this.editDepartment(e.item);
        } else if (e.detail.id == 4) {
          // 成员列表
          this.params.department_id = e.item.id;
          this.getMemberList();
          // this.editDepartment(e.item)
        }
      }
    },
    addDepartment(item) {
      // if (!item.id) {
      //   this.department_pid =
      // }
      this.department_pid = item.id;
      this.show_add_department = true;
    },
    editDepartment(item) {
      this.currentDepartment = item;
      // this.department_id = item.id
      this.show_edit_department = true;
    },
    editDepartmentOk() {
      this.getDepartmentList();
      this.show_edit_department = false;
    },
    cancelEditDepartment() {
      this.show_edit_department = false;
    },
    delDepartment(item) {
      this.$confirm("此操作将删除该部门, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteDepartment(item.id);
        })
        .catch(() => { });
    },
    deleteMember(row) {
      this.$confirm("此操作将删除该成员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.delCrmMember(row.id).then((res) => {
            if (res.status == 200) {
              this.$message.success("删除成功");
              this.getMemberList();
            } else {
              this.$message.warning(res.message || "删除失败");
            }
          });
        })
        .catch(() => { });
    },
    deleteDepartment(id) {
      this.$http.delCrmDepartment(id).then((res) => {
        if (res.status == 200) {
          this.$message.success("删除成功");
          this.getDepartmentList();
        } else {
          this.$message.warning(res.message || "删除失败");
        }
      });
    },

    // 编辑
    toEdit(row) {
      let arr = [];
      if (row.department && row.department.length) {
        row.department.map((item) => {
          arr.push(item.id);
        });
      }

      this.form_params = row;
      this.form_params.wx_work_department_id = arr;
      this.title = "编辑成员";
      this.show_edit_member_dia = true;
    },
    cancelEdit() {
      this.show_edit_member_dia = false;
    },
    editOk() {
      this.getMemberList();
      this.show_edit_member_dia = false;
    },
    toAdd() {
      this.show_add_member_dia = true;
    },
    //导入
    is_uploadclick(){
      this.is_upload_dialog = true
      this.warningtext()
    },
    //获取is_upload_dialog模态框提示文字
    warningtext(){
      this.$http.warning_text().then((res)=>{
        if(res.status==200){
          this.warningdata = res.data
          if(this.warningdata.is_import==0){
            this.disabledA = true
          }
        }
      })
    },
    cancelAdd() {
      this.show_add_member_dia = false;
    },
    addOk() {
      this.getMemberList();
      this.show_add_member_dia = false;
    },
    cancelAddDepartment() {
      this.show_add_department = false;
    },
    addDepartmentOk() {
      this.show_add_department = false;
      this.getDepartmentList();
    },

    importConcat() {
      this.$confirm("此操作将同步企微通讯录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.loading = Loading.service({
          target: ".el-table",
          lock: true,
          text: "正在同步中 请稍后...", //可以自定义文字
          spinner: "el-icon-loading", //自定义加载图标类名
        });
        this.$http
          .importWxDepartment()
          .then((res) => {
            if (res.status == 200) {
              this.loading.close();
              this.loading = null;
              this.$message.success(res.message || "同步成功");
              this.params.page = 1;
              this.getMemberList();
            }
          })
          .catch(() => {
            this.loading.close();
            this.loading = null;
          });
      });
    },
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/personal_template.xlsx"
      );
    },
    handleFileUpload(event) {
      // 阻止默认行为
      event.preventDefault();
      let file = this.$refs.file_p.files[0];
      let formData = new FormData();
      formData.append("excel", file);
      formData.append("type", this.is_associated_wxwork);
      this.onUpload(formData);
    },
    onUpload(formData) {
      this.$message.success("正在上传...");
      //  self_auth_create_all区分是否是企业微信自建应用的导入程序
      this.$http.uploadCrmCustomerPersonnelData(formData).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.params.page = 1;
          this.is_upload_dialog = false;
          this.getMemberList();
        }
      });
    },
    // 绑定角色
    bindRoles(row) {
      this.create_form.role_names = [];
      this.dialogCreateRoles = true;
      this.create_form.id = row.id + "";
      this.queryAdminRoles();
    },
    queryAdminRoles() {
      this.$http.queryAdminRoles(this.create_form.id).then((res) => {
        if (res.status === 200) {
          res.data.roles.map((item) => {
            this.create_form.role_names.push(item.name);
          });
        }
      });
    },
    submitData() {
      this.$http.resetUserRole(this.create_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.dialogCreateRoles = false;
          this.multipleSelection = [];
          this.getMemberList();
        }
      });
    },
    async submitDatainheritance() {
      let isDone = false,
          isFail = false;
      this.dialogs.inheritance_form = true;
      setTimeout(async ()=>{
        this.inheritProgress.pct = 0;
        this.inheritProgress.show = true;
        while(this.inheritProgress.show && this.inheritProgress.pct < 100){
          await this.$Utils.sleep(100);
          let pct = this.inheritProgress.pct + Math.ceil(Math.random()*6);
          pct > 99 && (pct = 99);
          this.inheritProgress.pct = pct;
          if(isDone){
            if(pct > 95){
              this.inheritProgress.pct = 100;
              await this.$Utils.sleep(600);
              this.$message.success("操作成功");
              this.dialogs.inheritance_form = false
              this.is_inheritance_dialog = false;
              this.inheritProgress.show = false;
              this.params.page = 1;
              this.getMemberList();
            }
          }else if(isFail){
            this.dialogs.inheritance_form = false
            this.inheritProgress.show = false;
          }
        }
      })
      if (this.is_position == 1) {
        await this.$http
          .setPersonnelMattersData(this.inheritance_form)
          .then((res) => {
            if (res.status === 200) {
              isDone = true;
              if(this.$store.state.website_info.website_mode_show.includes(3)){
                this.$alert(`<div>继承客户的数量: ${res.data.crm_count}</div><br>
                  <div>继承房源的数量:  ${res.data.house_count}</div><br>
                  <div>继承流转客的数量:  ${res.data.private_client_count}</div>
                    `, '离职继承提示', {
                    dangerouslyUseHTMLString: true
                  });
              }else{
                this.$alert(`<div>继承客户的数量: ${res.data.crm_count}</div><br>
                  <div>继承流转客的数量:  ${res.data.private_client_count}</div>
                    `, '离职继承提示', {
                    dangerouslyUseHTMLString: true
                  });
              }
              
            }else{
              isFail = true;
            }
          }).catch(()=>{
            isFail = true;
          });
      } else {
        await this.$http
          .setPersonnelMattersDataZaizhi(this.inheritance_form)
          .then((res) => {
            if (res.status === 200) {
              isDone = true;
              let alertContent = `<div>继承客户的数量: ${res.data.crm_count}</div><br>
                  <div>继承房源的数量:  ${res.data.house_count}</div><br>
                  <div>继承流转客的数量:  ${res.data.private_client_count}</div>
                    `
              if(this.$store.state.website_info.website_mode_show.includes(3)){
                  // if(res.data.house_count==0){
                  //   alertContent = `<div>继承客户的数量: ${res.data.crm_count}</div><br>
                  //   `
                  // }
                this.$alert(alertContent, '在职继承提示', {
                    dangerouslyUseHTMLString: true
                  });
              }else{
                this.$alert(`<div>继承客户的数量: ${res.data.crm_count}</div><br>
                  <div>继承流转客的数量:  ${res.data.private_client_count}</div>
                    `, '在职继承提示', {
                    dangerouslyUseHTMLString: true
                  });
              }
            }else{
              isFail = true;
            }
          }).catch(()=>{
            isFail = true;
          });
      }
    },
    onClickRoles() {
      this.$router.push("permission_list");
    },
    handleClose() {
      this.show_edit_department = false;
    },
    //分享
    // notshare(row) {
    //   let id = row.id
    //   // let share_no_follow = row.share_no_follow
    //   if(row.share_no_follow == 0) {
    //     this.share_no_follow = 1
    //   } else if (row.share_no_follow == 1) {
    //     this.share_no_follow = 0
    //   }
    //   console.log(this.share_no_follow,"状态")
    //   this.$http.shareDepartment(id, this.share_no_follow).then((res) => {
    //     if (res.status == 200) {
    //       this.$message({
    //         message: row.status == 1 ? '禁用成功' : '启用成功',
    //         type: 'success'
    //       });
    //       this.getMemberList(); // 获取最新数据
    //     }
    //   })
    // },
    // 快速启用/禁用成员
    fastConfig(row) {
      this.$http.setFastmemberStatus(row.id).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: row.status == 1 ? '禁用成功' : '启用成功',
            type: 'success'
          });
          this.getMemberList(); // 获取最新数据
        }
      })
    },
    //批量设置
    async handleBatchCommand(val){
      if (!this.multipleSelection.length) {
        this.$message.error("请至少选择一个成员");
        return;
      }
      const ids = this.multipleSelection.map(e => e.id).join(',');
      switch(val){
        case 'resetPassword':
          this.dialogs.resetPassword = true;
          this.$nextTick(()=>{
            this.$refs.resetPassword.open(ids).onSuccess(()=>{
              this.$refs.table.clearSelection();
            });
          })
          break;
        case 'disabledMember':
        case 'enabledMember':
          const type = val == 'disabledMember' ? 2 : 1;
          const res = await this.$http.setAdminStatus({ids, type}).catch(()=>{});
          if(res.status == 200){
            this.$message.success(res.data?.msg || '操作成功')
            this.multipleSelection = [];
            this.getMemberList();
          }
          break;
        case 'bindRoles':
        this.dialogs.bindRoles = true;
          this.$nextTick(()=>{
            this.$refs.bindRoles.open(ids).onSuccess(()=>{
              this.multipleSelection = [];
              this.getMemberList();
            });
          })
          break;
      }
    }
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .danger-alert{
    display: inline-block;
    margin-top: 20px;
    line-height: 1;
    padding: 10px 12px;
    color: #f40;
    border: 1px solid #f40;
    border-radius: 2px;
  }
  .top {
    align-items: center;
  }

  .table-top-box {
    justify-content: flex-start;
    flex-wrap: wrap;

    .text {
      margin-right: 10px;
    }

    .blue {
      margin-right: 10px;
    }
  }

  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }

  .bmtitle {
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 24px;
    border-bottom: 1px solid #eee;

    &.add_department {
      justify-content: flex-end;

      .add_depart {
        font-size: 30px;
        cursor: pointer;
        justify-self: flex-end;
      }
    }
  }

  .el-tag.el-tag--danger {
    margin-left: 5px;
  }

  .el-tag.el-tag--success {
    margin-left: 5px
  }

  .title-bm {
    font-size: 18px;
  }
}

.labelrow {
  margin-bottom: 20px;
}

.content-crm {
  min-height: 600px;
}

.table {
  .name {
    ~.name {
      margin-left: 8px;
    }
  }

  .row-info {
    .row-img {
      width: 30px;
      height: 30px;
      overflow: hidden;
      border-radius: 50%;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .row_no_img {
        width: 100%;
        height: 100%;
        line-height: 1;
        font-weight: 600;
        font-size: 16px;
        color: #fff;
        background: #2d84fb;
      }
    }

    .info {
      .row-name {
        .wechat {
          width: 15px;
          height: 15px;
          margin-left: 8px;
          // overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            // object-fit: cover;
          }
        }
      }
    }
  }
}

.padd0 {
  padding: 10px;
}

.cur_point {
  cursor: pointer;
}
</style>
