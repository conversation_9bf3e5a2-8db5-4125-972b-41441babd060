<template>
    <div>
        <div class="header" v-if="!preview">
            <el-button type="primary" style="margin: 20px 0px" @click="add()">添加</el-button>
        </div>
        <el-table v-loading="loading" :data="list" class="house_table" border
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
            <el-table-column label="回款时间" v-slot="{ row }">
                {{row.payment_time}}
            </el-table-column>
            <el-table-column label="回款人" v-slot="{ row }">
                {{ row.admin_user.user_name }}
            </el-table-column>
            <el-table-column label="回款金额" v-slot="{ row }">
                {{ row.amount }}
            </el-table-column>
            <el-table-column label="备注" v-slot="{ row }">
                {{ row.remark }}
            </el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }" v-if="!preview">
                <el-link style="margin-right: 20px;" type="primary" @click="edit(row)">编辑</el-link>
                <el-link type="danger" @click="del(row)">删除</el-link>
            </el-table-column>
        </el-table>
        <div class="footer">
            <div></div>
            <div class="commission-text" v-if="paymentTotal">
                回款金额合计：<span class="commission-text-red">{{paymentTotal}}</span> 元
            </div>
        </div>
    
        <addPaymentLogs v-if="dialogs.addPaymentLogs" ref="addPaymentLogs"/>
    </div>
</template>

<script>
import addPaymentLogs from './add_payment_logs.vue'
export default {
    name: 'crmDealReportPaymentLogs',
    components: {
        addPaymentLogs
    },
    props: {
        reportData: {type: Object, default: ()=>{return {}}},
        preview: {type: Boolean, default: false}
    },
    data(){
        return {
            loading: false,
            list: [],
            dialogs: {
                addPaymentLogs: false
            }
        }
    },
    computed: {
        //已回款合计金额
        paymentTotal(){
            return this.reportData?.payment?.commission || 0;
        }
    },
    created(){
        this.getList();
    },
    methods: {
        async getList(){
            this.loading = true;
            const res = await this.$http.crmDealReportPaymentLogs({ report_id: this.reportData.report_id});
            this.loading = false;
            if(res.status == 200){
                this.list = res.data;
            }
        },
        async add(data = {}){
            this.dialogs.addPaymentLogs = true; 
            await this.$nextTick();

            data.report_id = this.reportData.report_id;
            data.status = this.reportData.deal.payment_status || 1;  //报告的回款状态：1已回款，2未回款
            this.$refs.addPaymentLogs.open(data).onSuccess(()=>{
                this.getList();
                this.$emit('commissionChange');
            });
        },
        edit(data){
            this.add(data);
        },
        async del(data){
            const res = await this.$http.delCrmDealReportPaymentLogs({ id: data.id});
            if(res.status == 200){
                this.$message.success( res.data?.msg || "删除成功");
                this.getList();
                this.$emit('commissionChange');
            }
        }
    }
        
}
</script>
<style lang="scss" scoped>
.footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    .commission-text{
        font-size: 16px;
        color: #3c3c3c;
    }
    .commission-text-red{
        color: #f40;
        font-weight: 600;
    }   
}
</style>