<template>
        <el-drawer
            :visible.sync="show"
            :direction="direction"
            :with-header="false"
            :modal="false" v-bind="$attrs" v-on="$listeners">
           <!-- :modal="false" -->
            <div class="QuickEditA" v-infinite-scroll="getscriptlist" :infinite-scroll-disabled="loading || loaded" infinite-scroll-immediate="false">
                <div class="titlestyle" v-show="showTopCate">
            <!-- 头部标题 -->
                  <div class="titlepage">
                    <div class="classtitle">
                      <div class="titleitem" v-for="item in titledata" :key="item.id" :class="{ isitem: c_type2 == item.id }"
                        @click="titlechange(item)">
                        {{item.name}}
                      </div>
                    </div>
                  </div>
                  <span class="btn-close"><i class="el-icon-close" @click="show = false"></i></span>
                </div>
                <div v-if="c_type2!=3" class="ScriptLibrary" :class="{'no-cate': !showTopCate}">
                    <div class="Libraryselect">
                      <el-select v-model="libraryvalue" placeholder="请选择" @change="getlibrarylist"
                          size="small">
                          <el-option
                            v-for="item in tableData"
                            :key="item.id"
                            :label="item.package_name"
                            :value="item.id">{{item.package_name}}
                          </el-option>
                        </el-select>
                        <el-input class="search-input" placeholder="请输入关键词" size="small" v-model="huashulist.keywords" prefix-icon="el-icon-search" @input="onInputSearch"></el-input>
                        <div class="collapse-all" @click="collapseAll = !collapseAll">
                          <template v-if="collapseAll">展开 <img class="icon-arrow-up" :src="$imageDomain+'/icons/arrow-down-double-line.png'"></template>
                          <template v-else>收起 <img class="icon-arrow-up" :src="$imageDomain+'/icons/arrow-up-double-line.png'"></template>
                        </div>
                        

                    </div>
                    <div>
                       <el-tabs v-model="sortout" @tab-click="sortchange">
                          <el-tab-pane v-for="(item,index) in sortoutData" :key="index"  :label="item.cate_name" :name="item.id.toString()"></el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
                <div>
                    <div v-for="item in speechcraft" :key="item.id">
                        <el-collapse v-model="activeNames">
                          <div class="speech-item">
                            <el-collapse-item :title="item.title" :name="item.id">
                              <template #title>
                                <div class="speech-item-title">{{item.title}}</div>
                              </template>
                              <div class="speech-item-content" :class="{'with-bottom': showCopy}">
                                <div v-html="item.contentHtml"></div>
                                <div class="copy" v-if="showCopy">
                                  <el-link :underline="false" icon="el-icon-copy-document" @click="scriptcopy(item)">
                                    复制
                                  </el-link>
                                </div>
                              </div>
                            </el-collapse-item>
                          </div>
                        </el-collapse>
                    </div>

                    <div class="load-status">
                      <div v-if="loaded">
                        <span class="loaded-text">已加载完整内容</span>
                        <el-link type="primary" :underline="false" @click="loadNextOrPrev">
                          <template v-if="hasNext">&nbsp;&nbsp;下一个<i class="el-icon-arrow-right"></i></template>
                          <template v-else-if="hasPrev">&nbsp;&nbsp;上一个<i class="el-icon-arrow-right"></i></template>
                        </el-link>
                      </div>
                      <div class="loading" v-else-if="loading">加载中... </div>
                      <div v-else>
                        <el-link type="primary" :underline="false" @click="getscriptlist">加载更多</el-link>
                      </div>
                    </div>
                </div>
               <!--  <myEmpty v-else></myEmpty> -->
            </div>
          </el-drawer>
</template>

<script>
import myEmpty from "@/components/components/my_empty.vue";
import Utils from "@/utils/utils";
export default {
    name: 'detailedinformation',
    props: {
      showTopCate: { type: Boolean, default: true },
      showCopy: { type: Boolean, default: false },
      isPreview: { type: Boolean, default: false },
    },
    components: {
    myEmpty,
  },
    data(){
        return {
            show: false,        //dialog是否显示
            loading: false,
            loaded: false,
            direction:'rtl',
            params: {},          //表单参数
            titledata:[
                {id:1, name:"团队"},
                {id:2, name:"自建"},
                {id:3, name:"AI助理"}
            ],
            c_type2: '',
            input2:"",//检索字段
            paramsdata: {
              // page: 1,
              // per_page: 10,
              // total: 0,
              scope: "",
              module: "",
              keywords:"",
            },//获取话术库的字段
            libraryvalue:"",//
            tableData:[],//话术库数据
            sortout:"",//分类
            sortoutData:[],//分类数据
            speechcraft:[],//话术列表
            huashulist:{
              page: 1,
            cate_id:"",//分类id
            keywords:"",//关键词
            package_id:"",//话术库id
           },//获取话术
           openParams: {
            id: ''
           },
           collapseAll: false,
        }
    },
    computed: {
      activeNames: {
        set(){},
        get(){
          return this.collapseAll ? '' : this.speechcraft.map(item => item.id);
        }
      },
      curCateIndex(){
        return this.sortoutData.findIndex(item => item.id == this.sortout);
      },
      hasNext(){
        return this.curCateIndex !== -1 && this.curCateIndex < this.sortoutData.length - 1;
      },
      hasPrev(){
        return this.curCateIndex > 0;
      },
    },
    watch:{
      c_type2(newVal, oldVal){
        if(newVal == 3){
          this.c_type2 = oldVal;
        }
      },
    },
    methods: {
        //打开弹层
        async open(data = {}){
          let scope = data.scope || 1;
          let libraryId = data.id || '';

          this.c_type2 = scope;
      
          this.getDataList(libraryId);
          
          this.collapseAll = false;
          this.loaded = false;
          this.loading = true;
          this.sortoutData = [];
          this.speechcraft = [];
          this.huashulist.keywords = '';
        
          this.show = true;
          return this;
        },
        //获取话术库
        getDataList(libraryId = '') {
            let api = '';
            if(this.isPreview){
              api = 'allSpeechLibraryPreviewList'
            }else{
              api = 'allSpeechLibraryManagerList'
              this.params.scope = this.c_type2
            }

          this.$http[api](this.params).then((res) => {
            if (res.status === 200) {
              this.tableData = res.data.list;
              if(this.tableData.length){
                this.$nextTick(()=>{
                  this.libraryvalue =  libraryId || this.tableData[0].id;
                  this.getlibrarylist();
                })
              }else{
                this.speechcraft = [];
                this.loaded = true;
              }
            }
          });
        },
        //头部切换并赋值
        titlechange(item){
            this.c_type2 = item.id
            if(item.id == 3){
              this.$message.warning("暂未开放!")
              return;
            }
            this.loading = true;
            this.loaded = false;
            this.libraryvalue = '';
            this.sortoutData = []
            this.speechcraft = [];
            this.getDataList();
        },
        //获取话术分类
        getlibrarylist(){
            this.$http.getscriptsortV2(this.libraryvalue).then((res)=>{
                if(res.status==200){
                    console.log(res.data,"内容");
                    this.sortoutData = res.data.list || [];
                    if(this.sortoutData.length){
                      this.sortout = this.sortoutData[0].id.toString()
                      this.sortchange();
                    }else{
                      this.loaded = true
                    }
                }
            })
        },
        //获取话术
        getscriptlist(){
            this.loading = true;
            this.huashulist.package_id = this.libraryvalue
            this.huashulist.cate_id = this.sortout
            const params = {...this.huashulist};
            this.huashulist.page++;
            this.$http.getscriptV2(params).then((res)=>{
              this.loading = false;
              if(res.status==200){
                let list = res.data.data || [],
                    pageSize = res.data.per_page;

                list = list.map(item => {
                  item.contentHtml =item.content.replace(/\n/g, "<br/>");
                  return item;
                })
                this.speechcraft = this.speechcraft.concat(list);
                if(!list.length || pageSize && list.length < pageSize){
                  this.loaded = true;
                }
              }
            }).catch(e=>{
              this.loading = false;
            })
        },
        search(){
          this.huashulist.page = 1;
          this.speechcraft = [];
          this.loaded = false;
          this.getscriptlist()
        },
        onInputSearch: Utils.debounce(function(){
          this.search()
        },500),
        loadNextOrPrev(){
          if(this.hasNext){
            this.sortout = String(this.sortoutData[this.curCateIndex+1].id);
            this.sortchange();
          }else if(this.hasPrev){
            this.sortout = String(this.sortoutData[this.curCateIndex-1].id);
            this.sortchange();
          }
        },
        //分类切换赋值
        sortchange(){
          this.huashulist.cate_id = this.sortout
          this.search()
        },
        //复制话术
        scriptcopy(row){
          // console.log(row,"fuzhi");
          this.copyTxt(row.content);
        },
        copyTxt(txt){
          var oinput = document.createElement('textarea');
          oinput.value = txt;
          document.body.appendChild(oinput);
          oinput.select();				
          document.execCommand('copy');
          document.body.removeChild(oinput);  
          this.$message({               
              type:"success",
              message:"复制成功！"
          })
        },
        //取消
        cancle(){
            this.show = false;
        },
        //确定
        confirm(){
            this.show = false;
        }
    }
}
</script>
<style lang="scss" scoped>
  ::v-deep .el-drawer {
    width: 30% !important;
    }
.QuickEditA {
    width: 100%;
    height: calc(97vh - 0px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 18px;
    .titlestyle{
        display: flex;
        color: #4E5969;
        margin: 0 -18px;
        .titlepage {
          width: 100%;
          height: 71px;
          // background-color: aqua;
          overflow: hidden;
          border-bottom: 1px solid #F2F3F5;
          white-space: nowrap;
          .classtitle {
            display: flex;
            flex-direction: row;
            justify-content: center;
            margin-top: 33px;
          
            .titleitem {
              cursor: pointer;
              margin-right: 40px;
              color: #4E5969;
              position: relative;
            
              &.isitem {
                color: #165DFF;
              
                &::after {
                  position: absolute;
                  left: 38%;
                  transform: translateX(-38%);
                  content: "";
                  height: 4px;
                  background: #2d84fb;
                  width: 40px;
                  display: block;
                  margin-top: 14px;
                }
              }
            }
          
          }
        }
        .btn-close{
          cursor: pointer;
          display: inline-block;
          width: 42px;
          height: 36px;
          position: absolute;
          right: 5px;
          top: 5px;
          font-size: 20px;
          text-align: center;
          line-height: 36px;
        }
    }
    .ScriptLibrary{
    border: 1px solid #ffff;
    margin-top: 20px;
    overflow: hidden;
    position: sticky;
    top: 0;
    z-index: 9;
    background: #fff;
    &.no-cate{
      margin-top: 0;
    }
    .Libraryselect{
        width: 98%;
        margin: 15px 0;
        display: flex;
        flex-direction: row;
        .el-select{
          flex: 1
        }
        .search-input{
          margin-left: 12px;
          width: 35%;
        }
        .collapse-all{
          display: flex;
          align-items: center;
          cursor: pointer;
          color: #165DFF;
          font-size: 14px;
          white-space: nowrap;
          margin-left: 12px;
          .icon-arrow-up{
           width: 11px;
           margin: 2px 0 0 3px;
          }
        }
    }
  }
  ::v-deep .el-collapse{
      border-top: 0px solid #EBEEF5;
      .el-collapse-item__header.is-active {
        color: #131315;
        font-weight: 600;
        font-size: 17px;
      }
      .el-collapse-item__content{
        padding-bottom: 6px;
      }
    }
    .copy{
      position: absolute;
      right: 10px;
      bottom: 0;
      z-index: 1;
      display: none;
      justify-content: flex-end;
        .el-link{
          color: #999;
          &:hover{
            color: #409EFF;
          }
        }
    }
  }

// .demo-drawer__footer{
//     display: flex;
//     justify-content: flex-end;
//     margin: 10px;
//   }
//   .article{
//     width: 90%;
//     margin: 0 auto;
//     margin-top: 30px;
//     text-indent: 2em; /* 设置首行缩进为2个字符宽度 */
//   }

.speech-item{
  &:hover{
    .copy{
      display: flex;
    }
  }
  .speech-item-title{
    white-space: nowrap;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    user-select: none;
  }
  .speech-item-content{
    font-size: 15px;
    word-break: break-all;
    position: relative;
    padding: 0 6px 10px 0;
    user-select: none;
    &.with-bottom{
      padding-bottom: 26px;
    }
  }
}
.load-status{
  position: relative;
  padding: 15px 0;
  font-size: 14px;
  color: #aaa;
  text-align: center;
  .loaded-text{
    vertical-align: middle;
  }
}
::v-deep{
  .el-tabs__header{
    margin-bottom: 0;
  }
}
</style>