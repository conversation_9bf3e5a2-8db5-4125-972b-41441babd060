<template>
<el-drawer :title="title" :visible.sync="show" append-to-body destroy-on-close v-on="$listeners" :show-close="closable" :wrapperClosable="closable" :close-on-press-escape="closable">
    <div class="task-container"> 
        <el-form label-width="105px" class="body"  v-loading="loading">
            <el-form-item label="流转计划名称:">
                <el-input style="width: 260px" v-model="params.work_name" placeholder="请输入流转计划名称" v-if="isAdd"></el-input>
                <span v-else>{{params.work_name}}</span>
            </el-form-item>
            <el-form-item label="流转客资数量:" v-if="tabledata==1">
                <div style="color: #3c3c3c;">共{{customerLength}}个客户</div>
            </el-form-item>
            <el-form-item label="流转客资表格:" v-if="tabledata==2">
                <div class="importset_up">
                    <el-button size="small" style="width: 100px;height: 33px;" type="primary">选择表格文件
                        <input ref="excel-upload-input" class="excel-upload-input tablefile" type="file" accept=".xlsx, .xls" @change="getFileData">
                    </el-button>
                  <el-link type="primary" style="margin: 0px 10px 0px 10px" @click="onUploadTem">下载数据模板
                    <i class="el-icon-download"></i></el-link>
                    <span v-if="files.name">
                        已选表格：{{files.name}}
                    </span>
                </div>
                <div style="color:red;margin-top: -13px;">
                    单个表格支持最多100条数据流转
                </div>
            </el-form-item>
            <el-form-item label="流转计划:">
                <template #label>
                    <div style="position: relative;z-index: 2;">
                        流转计划:
                    </div>
                </template>                      
                <div class="timeline-container">
                    <el-table :data="nodeList" empty-text=" ">
                        <el-table-column width="58" align="center">
                        <template slot-scope="scope">
                            <div class="timeline-item">
                                {{scope.$index+1}}
                            </div>
                        </template>
                        </el-table-column>
                        <el-table-column width="1">
                            <div style="height: 32px;"></div>
                        </el-table-column>
                    </el-table>
                </div>

                <el-table :header-cell-style="{ background: '#EBF0F7' }" :data="nodeList">
                    <el-table-column label="状态" width="80"  align="center" v-if="!isCreated">
                        <template #default="{ row }">
                            <el-tag type="success" size="mini" v-if="row.is_do == 1">已执行</el-tag>
                            <el-tag type="danger" size="mini" v-else>未执行</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="流转节点名称" width="180" prop="point_name" align="center">
                        <template #default="{ row }">
                            <el-input v-model="row.point_name" placeholder="请输入流转节点名称" size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="分配设置" minWidth="225" align="center">
                        <template #default=" { row, $index }">
                        <el-popover placement="left" width="660" trigger="click" @show="curNodeIndex=$index">
                            <span slot="reference" class="filter-item">
                                <i class="el-icon-plus"></i> 
                                <span v-if="(row.member_range==2 && row.assignAdminIds.length) || (row.member_range==1 && row.assignGroupIds.length)">
                                {{row.firstAssignName}}({{row.member_range==2?row.assignAdminIds.length:row.assignGroupIds.length}}) · {{row.assign_type==1?'手动分配':'平均分配'}}
                                </span>
                                <span v-else>分配设置</span>
                            </span>
                            <el-form label-width="220px">
                                <el-form-item label="分配范围：">
                                <el-radio v-model="row.member_range" :label="2">成员</el-radio>
                                <el-radio v-model="row.member_range" :label="1">分组</el-radio>
                                </el-form-item>
                                <el-form-item label="分配方式：">
                                <el-radio v-model="row.assign_type" :label="1">手动分配</el-radio>
                                <el-radio v-model="row.assign_type" :label="2">平均分配</el-radio>
                                </el-form-item>
                                <el-form-item label="选择成员：" v-show="row.member_range===2">
                                    <admSelect @change="handleAdminSelectChange" v-model="row.assignAdminIds" multiple filterable clearable :remote-filter="false"  ref="tMemberSelect"
                                    :data="adminList" props="{label:'name',value:'values',keyword:'name'}" placeholder="请选择成员"/>
                                </el-form-item>
                                <el-form-item label="选择分组：" v-show="row.member_range===1">
                                    <admSelect @change="handleGroupSelectChange" v-model="row.assignGroupIds" multiple filterable clearable :remote-filter="false"  ref="tGroupSelect"
                                    :data="adminGroupList" props="{label:'title',value:'id',keyword:'title'}" placeholder="请选择分组"/>
                                </el-form-item>
                                <el-form-item label="" v-show="row.assign_type===1" class="assign-list">
                                <p class="form-item-tip"></p>
                                <draggable v-model="assignList" animation="300" style="width: 100%;">
                                    <transition-group>
                                    <el-row v-for="item in assignList" :key="item.assign_id" class="assign-item">
                                        <el-col :span="2"><span class="hand"><i class="el-icon-rank"></i></span></el-col>
                                        <el-col :span="12"><span class="name">{{item.assign_name}}</span></el-col>
                                        <el-col :span="10"><el-input size="small" v-model="item.assign_num" type="number">
                                        <template slot="prepend">分配数量</template>
                                        </el-input></el-col>
                                    </el-row>
                                    </transition-group>
                                </draggable>
                                </el-form-item>
                            </el-form>
                        </el-popover>
                        </template>
                    </el-table-column>

                    <el-table-column label="执行方式" width="325" align="left">
                        <template slot-scope="scope">
                        <el-select size="small"  style="width: 110px;" v-model="scope.row.execute_type" :disabled="scope.row.isImmediateExecute">
                            <el-option label="立即执行" :value="1" :disabled="hasImmediateExecuteNode && scope.row.execute_type==2"></el-option>
                            <el-option label="选择时间" :value="2"></el-option>
                        </el-select>
                        <el-date-picker size="small" style="width: 190px!important;margin-left:5px" v-if="scope.row.execute_type==2"
                            v-model="scope.row.execute_time"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择日期时间">
                        </el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column label="配置" align="center" minWidth="130">
                        <template #default=" { row }">
                        <el-popover placement="left" width="660" trigger="click">
                            <span slot="reference" class="filter-item"><i class="el-icon-plus"></i> 配置项</span>
                            <el-form label-width="220px">
                                <el-form-item label="是否同步标签：">
                                <el-radio v-model="row.sync_label" :label="1">同步</el-radio>
                                <el-radio v-model="row.sync_label" :label="0">不同步</el-radio>
                                </el-form-item>
                                <el-form-item label="是否同步城市：">
                                <el-radio v-model="row.sync_city" :label="1">同步</el-radio>
                                <el-radio v-model="row.sync_city" :label="0">不同步</el-radio>
                                </el-form-item>
                                <el-form-item label="是否同步客户意向：">
                                <el-radio v-model="row.sync_intention" :label="1">同步</el-radio>
                                <el-radio v-model="row.sync_intention" :label="0">不同步</el-radio>
                                </el-form-item>
                                <el-form-item label="是否同步备注：">
                                <el-radio v-model="row.sync_remark" :label="1">同步</el-radio>
                                <el-radio v-model="row.sync_remark" :label="0">不同步</el-radio>
                                </el-form-item>
                                <el-form-item label="是否同步附属手机号：">
                                <el-radio v-model="row.sync_sub_mobile" :label="1">同步</el-radio>
                                <el-radio v-model="row.sync_sub_mobile" :label="0">不同步</el-radio>
                                </el-form-item>
                                <el-form-item label="是否可以查看历史跟进记录：">
                                <el-radio v-model="row.allow_view_follow" :label="1">是</el-radio>
                                <el-radio v-model="row.allow_view_follow" :label="0">否</el-radio>
                                </el-form-item>
                            </el-form>
                        </el-popover>
                        </template>
                        
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" width="130" v-if="isCreated">
                        <template #default="{ row, $index }">
                        <template v-if="!row.id">
                            <el-link type="primary" @click="saveTransNode(row, $index)" :disabled="row.submiting">确认</el-link>
                        </template>
                        <template v-else>
                            <el-link class="op-btn" type="primary" @click="saveTransNode(row, $index)" :disabled="row.submiting">确认</el-link>
                            <!-- <el-popconfirm class="op-btn" :title="`确定要${row.point_status==1?'禁用':'启用'}该节点吗？`" @onConfirm="setTransNodeStatus(row, $index)">
                            <el-link type="warning" slot="reference" :disabled="row.submiting">{{row.point_status==1?'禁用':'启用'}}</el-link>
                            </el-popconfirm> -->
                            <el-popconfirm class="op-btn" title="确定要移除该节点吗？" @onConfirm="delTransNode(row, $index)">
                            <el-link type="danger" slot="reference" :disabled="row.submiting">移除</el-link>
                            </el-popconfirm>
                        </template>
                        </template> 
                    </el-table-column>
                </el-table>
            </el-form-item>
        </el-form>
        <div class="footer">
            <div></div>
            <div>
                <el-button @click="handleCancleTask" :loading="cancling" :disabled="submiting">取消执行</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submiting" :disabled="cancling" v-if="isCreated">确认开启</el-button>
            </div>
        </div>
    </div>
</el-drawer>
</template>

<script>
import draggable from 'vuedraggable';
export default {
    components: {
        draggable
    },
    props: {
        //1:潜在客户，2：公海客户，3：我的客户，4：流转客
        source: { type: Number,default: 0 },
        customerIds: { type: Array, default: ()=>[] },
        tabledata: { type: Number,default: 0 },
    },
    data() {
        return {
            show: false,
            loading: false,
            submiting: false,
            cancling: false,
            isCreated: true,
            nodeList: [],         //节点列表
            curNodeIndex: 0,      //当前选中的节点索引
            adminList: [],        //成员列表
            adminGroupList: [],   //成员分组列表
            customerCount: 0,     //客户数量
            params:{
                id: 0,                  //计划id
                work_name:"",           //计划名称
                customer_ids: [],       //客户ids
                source: 0
            },
            nodeRowParams: {
                id: 0,                  //节点id
                point_name:"",          //节点名称
                assign_type: 2,         //分配方式：1手动，2平均         
                member_range: 1,        //分配范围：1分组，2成员
                assignAdminIds: [],     //已选成员ids
                assignGroupIds: [],     //已选分组ids
                assignAdmins:[],        //已选成员列表
                assignGroups:[],        //已选分组列表
                execute_type: 1,        //1: 立即执行 2: 定时执行
                execute_time:"",        //定时执行时间
                sync_label: 0,          //同步标签，1同步，0不同步
                sync_city: 0,           //同步城市，1同步，0不同步
                sync_intention: 0,      //同步客户意向，1同步，0不同步
                sync_remark: 0,         //同步备注，1同步，0不同步
                sync_sub_mobile: 0,     //同步附属手机号: 1同步，0不同步
                allow_view_follow: 0,   //是否可以查看历史跟进记录
                point_status: 1,        //状态，1启用，2禁用
                is_do: 0,
                submiting: false,   
                firstAssignName: '',
                isImmediateExecute: false,
            },
            isDataChanged: false,        //数据是否编辑过
            tablelen:[],//上传的文件的表格,(目前单个上传，不支持批量)
            files:{},
        }
    },
    computed: {
        //客户数量
        customerLength(){
            return this.customerCount || this.params.customer_ids.length
        },
        //分配列表
        assignList: {
            get(){
                const data = this.nodeList[this.curNodeIndex];
                if(!data){
                    return [];
                }
                return data.member_range == 1 ? data.assignGroups : data.assignAdmins;
            },
            set(val){
                const member_range = this.nodeList[this.curNodeIndex].member_range;
                this.$set(this.nodeList[this.curNodeIndex], member_range == 1 ? 'assignGroups' : 'assignAdmins', val);
            }
        },
        isAdd(){
            return !this.params.id;
        },
        title(){
            return this.isCreated ? '创建流转计划' : '编辑流转计划';
        },
        //只能添加一个立即执行
        hasImmediateExecuteNode(){
            return this.nodeList.some(e => e.execute_type == 1);
        },
        closable(){
            return !this.isCreated || this.isAdd
        }
        
    },
    watch: {
        source: {
            handler(val){
                this.params.source = val;
            },
            immediate: true
        },
        customerIds: {
            handler(val){
                this.params.customer_ids = val;
            },
            immediate: true
        },
        assignList(list){
            if(list.length){
                const data = this.nodeList[this.curNodeIndex];
                let field = data.member_range == 1 ? 'assignGroupIds' : 'assignAdminIds';
                this.$set(this.nodeList[this.curNodeIndex], field, list.map(e => e.assign_id));
                this.$set(this.nodeList[this.curNodeIndex], 'firstAssignName', list[0].assign_name+(list.length==1?'':'等'));
            }
        }
    },
    created(){
        this.getAdminList();
        this.getAdminGroupList();
    },
    methods:{
        open(params){
            if(params && params.id){
                this.isCreated = false;
                this.params.id = params.id;
                this.params.work_name = params.work_name;
                this.params.source = params.source;
                this.customerCount = params.customer_count;
                //获取节点列表
                this.nodeList = [];
                this.getTransNodelist(()=>{
                    //编辑不可再新增节点
                    //this.addNodeRow()
                });
            }else{
                this.isCreated = true;
                this.addNodeRow();
                //默认数据
                this.$store.dispatch('queryMyInfo', (myInfo)=>{
                    this.params.work_name = myInfo.user_name + '发起的流转计划'+this.$Utils.formatDate(null, 'HH:ii');
                });
            }
            this.show = true;
        },
        //获取节点列表
        async getTransNodelist(cb){
            try{
                this.loading = true;
                const res = await this.$http.getnodelist(this.params.id);
                this.loading = false;
                if(res.status == 200){
                    this.nodeList = res.data.data.map(e => {
                        e = {...e, ...e.sync};
                        const rowData = {...this.nodeRowParams};
                        for(const key in rowData){
                            if(e[key] !== undefined){
                                rowData[key] = e[key];
                            }
                        }

                        if(e.assign_arr.length){
                            let member_range = e.assign_arr[0].member_range;
                            rowData[member_range==1? 'assignGroupIds' : 'assignAdminIds'] =  e.assign_arr.map(e => e.assign_id);
                            rowData[member_range==1? 'assignGroups' : 'assignAdmins'] =  e.assign_arr.map(e => {
                                return {
                                    assign_id: e.assign_id,
                                    assign_name: e.assign_name,
                                    assign_num: e.assign_num,
                                    sort: e.sort || 0
                                }
                            });
                            rowData.firstAssignName = e.assign_arr[0].assign_name+(e.assign_arr.length==1?'':'等');
                        }
                        return rowData;
                    });
                }
                cb && cb();
            }catch(e){
                console.log(e);
                cb && cb();
            }
        },
        /**
         * 分配成员/分组选择
         * @param { Object } props {id: 'id', name: 'name', field: ''}
         */
        handleAssignSelected(ids, list, props){
            const assigns = this.nodeList[this.curNodeIndex][props.field];
            const assignList = ids.length === 0 ? [] : list.filter(e=>ids.includes(e[props.id])).map(e => {
                return assigns.find(assign => assign.assign_id === e[props.id]) || {
                    assign_id: e[props.id],
                    assign_name: e[props.name],
                    assign_num: '',
                    sort: 0
                }
            })
            if(assignList.length == 1 && assignList[0].assign_num === ''){
                assignList[0].assign_num = this.customerLength;
            }
            this.$set(this.nodeList[this.curNodeIndex], props.field, assignList)
        },
        //处理选择成员
        handleAdminSelectChange(ids){
            this.handleAssignSelected(ids, this.adminList, {id:'values', name: 'name', field: 'assignAdmins'});
        },
        //处理选择分组
        handleGroupSelectChange(ids){
            this.handleAssignSelected(ids, this.adminGroupList, {id:'id', name: 'title', field: 'assignGroups'});
        },
        //获取成员列表
        async getAdminList(){
            const res = await this.$http.getAutoWorkUsers();
            if(res.status == 200){
                this.adminList = res.data || [];
            }
        },
        //获取分组列表
        async getAdminGroupList(){
            const res = await this.$http.getcluegrouping();
            if(res.status == 200){
                // res.data || [];
                console.log(res.data);
                res.data.map((item)=>{
                    if(item.user_count>0){
                        this.adminGroupList.push(item)  
                    }
                })
                console.log(this.adminGroupList);
            }
        },
            //添加流转节点
        addNodeRow(){
            const row = {...this.nodeRowParams, execute_type: this.hasImmediateExecuteNode ? 2: 1};
            this.nodeList.push(row)
        },
        /**
         * 新增计划+第一个节点
         */
        async saveTransWork(){
            //检查计划参数
            let params = {...this.params};
            if(!this.Validate({
                work_name: {require: '请填写流转计划名称'}
            }).check(params)){
                return;
            }
            //检查节点参数
            const forms = this.nodeList[0];
            if(!this.checkTransNode(forms)){
                return;
            }

            params.customer_ids = params.customer_ids.join(',');
            delete params.id;
            Object.assign(params,this.getTransNodeForms(forms));
            const execute_type = params.execute_type;  //是否立即执行
            this.nodeList[0].submiting = true;
            let urlapi = 'batchcirculation'
            if(this.params.source==5){
                const formData = new FormData()
                formData.append('file',this.files)
                formData.append('source',5)
                formData.append('customer_ids',"")
                formData.append('allow_view_follow',params.allow_view_follow)
                formData.append('assign_arr',params.assign_arr)
                formData.append('assign_type',params.assign_type)
                formData.append('execute_time',params.execute_time)
                formData.append('execute_type',params.execute_type)
                formData.append('member_range',params.member_range)
                formData.append('point_name',params.point_name)
                formData.append('sync_city',params.sync_city)
                formData.append('sync_intention',params.sync_intention)
                formData.append('sync_label',params.sync_label)
                formData.append('sync_remark',params.sync_remark)
                formData.append('sync_sub_mobile',params.sync_sub_mobile)
                formData.append('work_name',params.work_name)
                urlapi = 'tablebatchcirculation'
                params = formData
            }
            const res = await this.$http[urlapi](params);
            console.log(res);
            this.nodeList[0].submiting = false;
            if(res.status == 200){
                //当前计划id
                this.params.id = res.data.auto_work_id;
                if(res.data.customer_ids){
                    this.params.customer_ids = res.data.customer_ids
                }
                //当前节点id
                this.nodeList[0].id = res.data.auto_work_point_id;
                this.nodeList[0].execute_type = execute_type;
                this.nodeList[0].isImmediateExecute = execute_type == 1;
                //增加下一个节点
                this.addNodeRow();
                return true;
                //this.$message.success(res.data?.msg || '保存成功');  
            }
        },
        /**
         * 保存节点数据
         * @param { Object } row 
         * @param { Number } index 
         */
        async saveTransNode(row, index){
            //新增计划+第一个节点
            if(!this.params.id){
                const res = await this.saveTransWork();
                if(res){
                    this.$message.success(res.data?.msg || '添加节点成功，可继续添加节点');     
                }
                return;    
            }

            if(!this.checkTransNode(row)){
                return;
            }
            const params = this.getTransNodeForms(row);
            const isAdd = !row.id;
            const execute_type = row.execute_type;  //是否立即执行
            if(this.params.source==5){
                params.customer_ids = this.params.customer_ids.join(',')
            }
            row.submiting = true;
            const res = await this.$http[isAdd ? 'addwanderaboutnode': 'editwanderaboutnodes'](params);
            row.submiting = false;
            if(res.status == 200){
                row.execute_type = execute_type;
                row.isImmediateExecute = execute_type == 1;
                if(isAdd){
                    row.id = res.data.auto_work_point_id;
                    this.addNodeRow();
                }
                this.isDataChanged = true;
                this.$message.success(res.data?.msg || '添加节点成功，可继续添加节点'); 
            }
        },
        /**
         * 设置节点状态
         */
        async setTransNodeStatus(row, index){
            if(row.submiting){
                return;
            }
            row.submiting = true;
            const point_status = row.point_status == 1? 2 : 1;
            const res = await this.$http.setnodestatus({
                work_point_id: row.id,
                point_status
            });
            row.submiting = false;
            if(res.status == 200){
                row.point_status = point_status;
                this.$message.success(point_status == 2 ? '禁用成功': '启用成功');
            }
        },
        /**
         * 删除节点
         * @param { Object } row 
         * @param { Number } index 
         */
        async delTransNode(row, index){
            if(row.submiting){
                return;
            }
            row.submiting = true;
            const res = await this.$http.deletasknode(row.id);
            row.submiting = false;
            if(res.status == 200){
                this.nodeList.splice(index, 1);
                this.isDataChanged = true;
                this.$message.success('删除成功');
            }
        },
        /**
         * 检查节点数据
         * @param { Object } params 
         */
        checkTransNode(params){
            if(!this.Validate({
                point_name: {require: '请填写流转节点名称'}
            }).check(params)){
                return false;
            }
            if(params[params.member_range == 1 ? 'assignGroups' : 'assignAdmins'].length == 0){
                this.$message.error('请设置要分配的'+(params.member_range == 1 ? '分组':'成员'));
                return false;
            }
            return true;
        },
        /**
         * 获取节点表单参数
         * @param { Object } params 
         */
        getTransNodeForms(params){
            const forms = {...params};
            //分配成员/分组数据
            forms.assign_arr = forms[forms.member_range == 1 ? 'assignGroups' : 'assignAdmins'].map(e => {
                let assign_num = forms.assign_type == 2 ? 0 : parseInt(e.assign_num) || 0;
                assign_num < 0 && (assign_num = 0);
                return {
                    assign_id: e.assign_id,
                    assign_num,
                    sort: 0
                }
            });
            forms.assign_arr = JSON.stringify(forms.assign_arr);

            //计划id
            if(this.params.id){
                forms.auto_work_id = this.params.id
            }
            delete forms.assignGroups;
            delete forms.assignAdmins;
            delete forms.assignGroupIds;
            delete forms.assignAdminIds;
            delete forms.point_status;
            delete forms.submiting;
            delete forms.firstAssignName;
            delete forms.isImmediateExecute
            delete forms.is_do
            if(forms.id == 0){
                delete forms.id;
            }
            return forms;
        },
        /**
         * 取消计划
         */
        async handleCancleTask(){
            if(this.isAdd){
                this.show = false;
                return;
            }
            let confirm = await this.$confirm('确认取消计划吗？').catch(()=>{});
            if(!confirm){
                return;
            }
            this.cancling = true;
            const res = await this.$http.cancelAutoWork(this.params.id);
            this.cancling = false;
            if(res.status == 200){
                this.$message.success(res.data?.msg || '取消计划成功');
                this.isDataChanged = true;
                this.show = false;
            }
        },
        /**
         * 确认创建
         */
        async handleSubmit(){
            if(this.isAdd){
                if(!await this.saveTransWork()){
                    return;
                }
            }
            this.submiting = true;
            const res = await this.$http.allowAutoWork(this.params.id);
            this.submiting = false;
            if(res.status == 200){
                this.$message.success(res.data?.msg || '计划提交成功');
                this.show = false;
            }
        },
        //选择需要上传导入的表格
        getFileData(e){
            this.files = e.target.files[0];
            this.params.source = 5
        },
        //下载最新的模板
        onUploadTem() {
            window.open(
              "https://img.tfcs.cn/backup/static/admin/xls/client_template_new.xls?f=" +
              +new Date()
            );
          },
        }
}
</script>

<style lang="scss" scoped>
.task-container{
    display: flex;
    flex-direction: column;
    height: calc(100vh - 78px);
    .body{
        flex: 1;
        padding: 0 20px;
        overflow: auto;
        .timeline-container{
            position: absolute;
            left:-62px;
            .timeline-item{
                display: inline-block;
                background-color:#fff;
                border: 2px solid #409EFF;;
                width: 27px;
                height: 27px;
                border-radius: 50%;
                text-align: center;
                color: #409EFF;
                font-weight: 600;
                &::before,&::after{
                    content: '';
                    position: absolute;
                    z-index: 1;
                    left: calc(50% - 1px);
                    width: 2px;
                    background-color: #409EFF;
                }
                &::before{
                    top: 0;
                    bottom: calc(50% + 13px);
                }
                &::after{
                    top: calc(50% + 13px);
                    bottom: 0;
                }
            }
            ::v-deep .el-table{
                &::before,
                tr:hover td{
                    background-color: #fff
                }
                tr td:first-child, tr td:first-child .cell{
                    padding: 0;
                    border: none;
                }
                tr:first-child .timeline-item::before,
                tr:last-child .timeline-item::after{
                    display: none;
                }
                td, th.is-leaf{
                    border-color: #fff;
                }
            }
        }

        .op-btn+.op-btn{
            margin-left: 10px;
        }
        .filter-item{
            display: inline-flex;
            justify-content: center;
            align-items: center;
            height: 30px;
            min-width: 60px;
            line-height: 1;
            padding: 0 12px;
            border: 1px solid #a8c7e8;
            color: #409eff;
            cursor: pointer;
        }
        .tablefile{
            float: left;
            opacity: 0;
            width: 100px;
            margin-top: -14px
        }
    }
    .footer{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 10px 20px;
        border-top: 1px solid #e6e6e6
    }
    


    ::v-deep .el-table{
        tr th{
            padding: 5px 0;
        }
    }
}
.assign-list{
    max-height: 300px;
    overflow: auto;  
    .assign-item{
        padding-right: 5px;
        border-bottom: 1px dashed #e9e9e9;
        .el-col{
            display: flex;
            align-items: center;
            height: 50px;
            .hand{
                cursor: move;
                padding: 0 6px;
                .el-icon-rank{
                    font-size: 16px;
                }
            }
            .name{
                display: inline-block;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}

::v-deep {
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    .el-popconfirm__main{
        line-height: 2;
    }
  .el-drawer{
    width: 58%!important;
    .el-drawer__header {
      color: #2E3C4E;
    }
  }
}
</style>