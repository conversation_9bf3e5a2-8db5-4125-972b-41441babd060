// import wx from "weixin-js-sdk"; // 引入微信jssdk
/* eslint-disable no-undef */
export default {
  methods: {
    loadRemoteJs(src, id) {
      return new Promise((resolve) => {
        id &&
          document.getElementById(id) &&
          document.getElementById(id).remove();
        var script = document.createElement("script");
        script.src = src;
        if (id) {
          script.id = id;
          script.type = "text/javascript";
        }
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(script, s);
        if (script.readyState) {
          script.onreadystatechange(() => {
            if (
              script.readyState === "complete" ||
              script.readyState === "loaded"
            ) {
              resolve();
            }
          });
        } else {
          script.onload = function() {
            resolve();
          };
        }
      });
    },
    getWxQyWxConfig(jsApiList, callback, siteID) {
      let url = location.href.split("#")[0];
      // let url = window.location.href;
      let params = { url };
      if (siteID) {
        params.website_id = siteID;
      }
      this.loadRemoteJs(
        "https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js",
        1
      ).then(() => {
        this.loadRemoteJs(
          "https://res.wx.qq.com/open/js/jweixin-1.2.0.js",
          2
        ).then(() => {
          // type = 1 第三方，type = 2自建
          this.$http.getWxWrokConfig({ params: params }).then((res) => {
            if (res.status === 200) {
              wx.config({
                beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: res.data.corpid, // 必填，企业微信的corpID
                timestamp: res.data.timestamp, // 必填，生成签名的时间戳
                nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
                signature: res.data.signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
                jsApiList: jsApiList || [
                  "agentConfig",
                  "onMenuShareWechat",
                  "scanQRCode", //  企业微信扫码
                  "onMenuShareAppMessage", // 企业微信自定义‘网页内容分享’
                ], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
              });
              wx.ready(() => {
                console.log("初始化wxready");
                this.getWxAgentConfig(res, jsApiList, callback);
              });
              wx.error(function(res) {
                console.log("wx.error:" + JSON.stringify(res));
                // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
              });
            }
          });
        });
      });
    },
    getWxAgentConfig(res, jsApiList, callback) {
      wx.checkJsApi({
        jsApiList: jsApiList || ["agentConfig"],
        success: () => {
          console.log("初始化wxcheckJsApi");
          wx.agentConfig({
            agentid: res.data.agent_id,
            corpid: res.data.corpid,
            debug: false,
            jsApiList: jsApiList,
            nonceStr: res.data.nonceStr,
            signature: res.data.agent_signature,
            timestamp: res.data.timestamp,
            success: function() {
              console.log("调用agentConfig：", res.data);
              if (callback) {
                callback(wx, res.data.auth_way);
              }
              // wx.invoke(
              //   "selectExternalContact",
              //   {
              //     filterType: 0, //0表示展示全部外部联系人列表，1表示仅展示未曾选择过的外部联系人。默认值为0；除了0与1，其他值非法。在企业微信2.4.22及以后版本支持该参数
              //   },
              //   function(res) {
              //     if (res.err_msg == "selectExternalContact:ok") {
              //       let userIds = res.userIds; //返回此次选择的外部联系人userId列表，数组类型
              //       console.log(userIds);
              //     } else {
              //       //错误处理
              //     }
              //   }
              // );
            },
            fail: function(res) {
              if (res.errMsg.indexOf("function not exist") > -1) {
                alert("版本过低请升级");
              } else {
                alert("error:" + JSON.stringify(res));
              }
            },
          });
        },
        fail: (err) => {
          console.log(err);
        },
      });
    },
  },
};
