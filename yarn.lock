# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/code-frame/download/@babel/code-frame-7.10.4.tgz#168da1a36e90da68ae8d49c0f1b48c7c6249213a"
  integrity sha1-Fo2ho26Q2miujUnA8bSMfGJJITo=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/compat-data/download/@babel/compat-data-7.10.5.tgz#d38425e67ea96b1480a3f50404d1bf85676301a6"
  integrity sha1-04Ql5n6paxSAo/UEBNG/hWdjAaY=
  dependencies:
    browserslist "^4.12.0"
    invariant "^2.2.4"
    semver "^5.5.0"

"@babel/core@^7.9.6":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/core/download/@babel/core-7.10.5.tgz#1f15e2cca8ad9a1d78a38ddba612f5e7cdbbd330"
  integrity sha1-HxXizKitmh14o43bphL158270zA=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.10.5"
    "@babel/helper-module-transforms" "^7.10.5"
    "@babel/helpers" "^7.10.4"
    "@babel/parser" "^7.10.5"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.5"
    "@babel/types" "^7.10.5"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.1"
    json5 "^2.1.2"
    lodash "^4.17.19"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.10.5":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/generator/download/@babel/generator-7.10.5.tgz#1b903554bc8c583ee8d25f1e8969732e6b829a69"
  integrity sha1-G5A1VLyMWD7o0l8eiWlzLmuCmmk=
  dependencies:
    "@babel/types" "^7.10.5"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.10.4.tgz#5bf0d495a3f757ac3bda48b5bf3b3ba309c72ba3"
  integrity sha1-W/DUlaP3V6w72ki1vzs7ownHK6M=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.10.4.tgz#bb0b75f31bf98cbf9ff143c1ae578b87274ae1a3"
  integrity sha1-uwt18xv5jL+f8UPBrleLhydK4aM=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-compilation-targets@^7.10.4", "@babel/helper-compilation-targets@^7.9.6":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.10.4.tgz#804ae8e3f04376607cc791b9d47d540276332bd2"
  integrity sha1-gEro4/BDdmB8x5G51H1UAnYzK9I=
  dependencies:
    "@babel/compat-data" "^7.10.4"
    browserslist "^4.12.0"
    invariant "^2.2.4"
    levenary "^1.1.1"
    semver "^5.5.0"

"@babel/helper-create-class-features-plugin@^7.10.4", "@babel/helper-create-class-features-plugin@^7.10.5":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.10.5.tgz#9f61446ba80e8240b0a5c85c6fdac8459d6f259d"
  integrity sha1-n2FEa6gOgkCwpchcb9rIRZ1vJZ0=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-member-expression-to-functions" "^7.10.5"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"

"@babel/helper-create-regexp-features-plugin@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.10.4.tgz#fdd60d88524659a0b6959c0579925e425714f3b8"
  integrity sha1-/dYNiFJGWaC2lZwFeZJeQlcU87g=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"
    regexpu-core "^4.7.0"

"@babel/helper-define-map@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/helper-define-map/download/@babel/helper-define-map-7.10.5.tgz#b53c10db78a640800152692b13393147acb9bb30"
  integrity sha1-tTwQ23imQIABUmkrEzkxR6y5uzA=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/types" "^7.10.5"
    lodash "^4.17.19"

"@babel/helper-explode-assignable-expression@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.10.4.tgz#40a1cd917bff1288f699a94a75b37a1a2dbd8c7c"
  integrity sha1-QKHNkXv/Eoj2malKdbN6Gi29jHw=
  dependencies:
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-function-name@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-function-name/download/@babel/helper-function-name-7.10.4.tgz#d2d3b20c59ad8c47112fa7d2a94bc09d5ef82f1a"
  integrity sha1-0tOyDFmtjEcRL6fSqUvAnV74Lxo=
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-get-function-arity@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.10.4.tgz#98c1cbea0e2332f33f9a4661b8ce1505b2c19ba2"
  integrity sha1-mMHL6g4jMvM/mkZhuM4VBbLBm6I=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-hoist-variables@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.10.4.tgz#d49b001d1d5a68ca5e6604dda01a6297f7c9381e"
  integrity sha1-1JsAHR1aaMpeZgTdoBpil/fJOB4=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-member-expression-to-functions@^7.10.4", "@babel/helper-member-expression-to-functions@^7.10.5":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.10.5.tgz#172f56e7a63e78112f3a04055f24365af702e7ee"
  integrity sha1-Fy9W56Y+eBEvOgQFXyQ2WvcC5+4=
  dependencies:
    "@babel/types" "^7.10.5"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.10.4", "@babel/helper-module-imports@^7.8.3":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-module-imports/download/@babel/helper-module-imports-7.10.4.tgz#4c5c54be04bd31670a7382797d75b9fa2e5b5620"
  integrity sha1-TFxUvgS9MWcKc4J5fXW5+i5bViA=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-module-transforms@^7.10.4", "@babel/helper-module-transforms@^7.10.5":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.10.5.tgz#120c271c0b3353673fcdfd8c053db3c544a260d6"
  integrity sha1-EgwnHAszU2c/zf2MBT2zxUSiYNY=
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-simple-access" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.5"
    lodash "^4.17.19"

"@babel/helper-optimise-call-expression@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.10.4.tgz#50dc96413d594f995a77905905b05893cd779673"
  integrity sha1-UNyWQT1ZT5lad5BZBbBYk813lnM=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.8.0":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.10.4.tgz#2f75a831269d4f677de49986dff59927533cf375"
  integrity sha1-L3WoMSadT2d95JmG3/WZJ1M883U=

"@babel/helper-regex@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/helper-regex/download/@babel/helper-regex-7.10.5.tgz#32dfbb79899073c415557053a19bd055aae50ae0"
  integrity sha1-Mt+7eYmQc8QVVXBToZvQVarlCuA=
  dependencies:
    lodash "^4.17.19"

"@babel/helper-remap-async-to-generator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.10.4.tgz#fce8bea4e9690bbe923056ded21e54b4e8b68ed5"
  integrity sha1-/Oi+pOlpC76SMFbe0h5UtOi2jtU=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-wrap-function" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-replace-supers@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.10.4.tgz#d585cd9388ea06e6031e4cd44b6713cbead9e6cf"
  integrity sha1-1YXNk4jqBuYDHkzUS2cTy+rZ5s8=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-simple-access@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-simple-access/download/@babel/helper-simple-access-7.10.4.tgz#0f5ccda2945277a2a7a2d3a821e15395edcf3461"
  integrity sha1-D1zNopRSd6KnotOoIeFTle3PNGE=
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-split-export-declaration@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.10.4.tgz#2c70576eaa3b5609b24cb99db2888cc3fc4251d1"
  integrity sha1-LHBXbqo7VgmyTLmdsoiMw/xCUdE=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-validator-identifier@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.10.4.tgz#a78c7a7251e01f616512d31b10adcf52ada5e0d2"
  integrity sha1-p4x6clHgH2FlEtMbEK3PUq2l4NI=

"@babel/helper-wrap-function@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.10.4.tgz#8a6f701eab0ff39f765b5a1cfef409990e624b87"
  integrity sha1-im9wHqsP8592W1oc/vQJmQ5iS4c=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helpers@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/helpers/download/@babel/helpers-7.10.4.tgz#2abeb0d721aff7c0a97376b9e1f6f65d7a475044"
  integrity sha1-Kr6w1yGv98Cpc3a54fb2XXpHUEQ=
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/highlight/download/@babel/highlight-7.10.4.tgz#7d1bdfd65753538fabe6c38596cdb76d9ac60143"
  integrity sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.10.4", "@babel/parser@^7.10.5", "@babel/parser@^7.7.0":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/parser/download/@babel/parser-7.10.5.tgz#e7c6bf5a7deff957cec9f04b551e2762909d826b"
  integrity sha1-58a/Wn3v+VfOyfBLVR4nYpCdgms=

"@babel/plugin-proposal-async-generator-functions@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.10.5.tgz#3491cabf2f7c179ab820606cec27fed15e0e8558"
  integrity sha1-NJHKvy98F5q4IGBs7Cf+0V4OhVg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.10.4"
    "@babel/plugin-syntax-async-generators" "^7.8.0"

"@babel/plugin-proposal-class-properties@^7.10.4", "@babel/plugin-proposal-class-properties@^7.8.3":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.10.4.tgz#a33bf632da390a59c7a8c570045d1115cd778807"
  integrity sha1-ozv2Mto5ClnHqMVwBF0RFc13iAc=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-decorators@^7.8.3":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.10.5.tgz#42898bba478bc4b1ae242a703a953a7ad350ffb4"
  integrity sha1-QomLukeLxLGuJCpwOpU6etNQ/7Q=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-decorators" "^7.10.4"

"@babel/plugin-proposal-dynamic-import@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.10.4.tgz#ba57a26cb98b37741e9d5bca1b8b0ddf8291f17e"
  integrity sha1-uleibLmLN3QenVvKG4sN34KR8X4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"

"@babel/plugin-proposal-json-strings@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.10.4.tgz#593e59c63528160233bd321b1aebe0820c2341db"
  integrity sha1-WT5ZxjUoFgIzvTIbGuvgggwjQds=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.0"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.10.4.tgz#02a7e961fc32e6d5b2db0649e01bf80ddee7e04a"
  integrity sha1-AqfpYfwy5tWy2wZJ4Bv4Dd7n4Eo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"

"@babel/plugin-proposal-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.10.4.tgz#ce1590ff0a65ad12970a609d78855e9a4c1aef06"
  integrity sha1-zhWQ/wplrRKXCmCdeIVemkwa7wY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.10.4.tgz#50129ac216b9a6a55b3853fdd923e74bf553a4c0"
  integrity sha1-UBKawha5pqVbOFP92SPnS/VTpMA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-transform-parameters" "^7.10.4"

"@babel/plugin-proposal-optional-catch-binding@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.10.4.tgz#31c938309d24a78a49d68fdabffaa863758554dd"
  integrity sha1-Mck4MJ0kp4pJ1o/av/qoY3WFVN0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"

"@babel/plugin-proposal-optional-chaining@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.10.4.tgz#750f1255e930a1f82d8cdde45031f81a0d0adff7"
  integrity sha1-dQ8SVekwofgtjN3kUDH4Gg0K3/c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"

"@babel/plugin-proposal-private-methods@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.10.4.tgz#b160d972b8fdba5c7d111a145fc8c421fc2a6909"
  integrity sha1-sWDZcrj9ulx9ERoUX8jEIfwqaQk=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-unicode-property-regex@^7.10.4", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.10.4.tgz#4483cda53041ce3413b7fe2f00022665ddfaa75d"
  integrity sha1-RIPNpTBBzjQTt/4vAAImZd36p10=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-async-generators@^7.8.0":
  version "7.8.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.10.4.tgz#6644e6a0baa55a61f9e3231f6c9eeb6ee46c124c"
  integrity sha1-ZkTmoLqlWmH54yMfbJ7rbuRsEkw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-decorators@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.10.4.tgz#6853085b2c429f9d322d02f5a635018cdeb2360c"
  integrity sha1-aFMIWyxCn50yLQL1pjUBjN6yNgw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-dynamic-import@^7.8.0", "@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-json-strings@^7.8.0":
  version "7.8.3"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.10.4.tgz#39abaae3cbf710c4373d8429484e6ba21340166c"
  integrity sha1-Oauq48v3EMQ3PYQpSE5rohNAFmw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.0":
  version "7.8.3"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.0":
  version "7.8.3"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.0":
  version "7.8.3"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.0":
  version "7.8.3"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.10.4.tgz#4bbeb8917b54fcf768364e0a81f560e33a3ef57d"
  integrity sha1-S764kXtU/PdoNk4KgfVg4zo+9X0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-arrow-functions@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.10.4.tgz#e22960d77e697c74f41c501d44d73dbf8a6a64cd"
  integrity sha1-4ilg135pfHT0HFAdRNc9v4pqZM0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-async-to-generator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.10.4.tgz#41a5017e49eb6f3cda9392a51eef29405b245a37"
  integrity sha1-QaUBfknrbzzak5KlHu8pQFskWjc=
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.10.4"

"@babel/plugin-transform-block-scoped-functions@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.10.4.tgz#1afa595744f75e43a91af73b0d998ecfe4ebc2e8"
  integrity sha1-GvpZV0T3XkOpGvc7DZmOz+Trwug=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-block-scoping@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.10.5.tgz#b81b8aafefbfe68f0f65f7ef397b9ece68a6037d"
  integrity sha1-uBuKr++/5o8PZffvOXuezmimA30=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-classes@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.10.4.tgz#405136af2b3e218bc4a1926228bc917ab1a0adc7"
  integrity sha1-QFE2rys+IYvEoZJiKLyRerGgrcc=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-define-map" "^7.10.4"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.10.4.tgz#9ded83a816e82ded28d52d4b4ecbdd810cdfc0eb"
  integrity sha1-ne2DqBboLe0o1S1LTsvdgQzfwOs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-destructuring@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.10.4.tgz#70ddd2b3d1bea83d01509e9bb25ddb3a74fc85e5"
  integrity sha1-cN3Ss9G+qD0BUJ6bsl3bOnT8heU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-dotall-regex@^7.10.4", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.10.4.tgz#469c2062105c1eb6a040eaf4fac4b488078395ee"
  integrity sha1-RpwgYhBcHragQOr0+sS0iAeDle4=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-duplicate-keys@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.10.4.tgz#697e50c9fee14380fe843d1f306b295617431e47"
  integrity sha1-aX5Qyf7hQ4D+hD0fMGspVhdDHkc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-exponentiation-operator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.10.4.tgz#5ae338c57f8cf4001bdb35607ae66b92d665af2e"
  integrity sha1-WuM4xX+M9AAb2zVgeuZrktZlry4=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-for-of@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.10.4.tgz#c08892e8819d3a5db29031b115af511dbbfebae9"
  integrity sha1-wIiS6IGdOl2ykDGxFa9RHbv+uuk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-function-name@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.10.4.tgz#6a467880e0fc9638514ba369111811ddbe2644b7"
  integrity sha1-akZ4gOD8ljhRS6NpERgR3b4mRLc=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-literals@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.10.4.tgz#9f42ba0841100a135f22712d0e391c462f571f3c"
  integrity sha1-n0K6CEEQChNfInEtDjkcRi9XHzw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.10.4.tgz#b1ec44fcf195afcb8db2c62cd8e551c881baf8b7"
  integrity sha1-sexE/PGVr8uNssYs2OVRyIG6+Lc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-modules-amd@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.10.5.tgz#1b9cddaf05d9e88b3aad339cb3e445c4f020a9b1"
  integrity sha1-G5zdrwXZ6Is6rTOcs+RFxPAgqbE=
  dependencies:
    "@babel/helper-module-transforms" "^7.10.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.10.4.tgz#66667c3eeda1ebf7896d41f1f16b17105a2fbca0"
  integrity sha1-ZmZ8Pu2h6/eJbUHx8WsXEFovvKA=
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-simple-access" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.10.5.tgz#6270099c854066681bae9e05f87e1b9cadbe8c85"
  integrity sha1-YnAJnIVAZmgbrp4F+H4bnK2+jIU=
  dependencies:
    "@babel/helper-hoist-variables" "^7.10.4"
    "@babel/helper-module-transforms" "^7.10.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.10.4.tgz#9a8481fe81b824654b3a0b65da3df89f3d21839e"
  integrity sha1-moSB/oG4JGVLOgtl2j34nz0hg54=
  dependencies:
    "@babel/helper-module-transforms" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-named-capturing-groups-regex@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.10.4.tgz#78b4d978810b6f3bcf03f9e318f2fc0ed41aecb6"
  integrity sha1-eLTZeIELbzvPA/njGPL8DtQa7LY=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"

"@babel/plugin-transform-new-target@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.10.4.tgz#9097d753cb7b024cb7381a3b2e52e9513a9c6888"
  integrity sha1-kJfXU8t7Aky3OBo7LlLpUTqcaIg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-object-super@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.10.4.tgz#d7146c4d139433e7a6526f888c667e314a093894"
  integrity sha1-1xRsTROUM+emUm+IjGZ+MUoJOJQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.10.4"

"@babel/plugin-transform-parameters@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.10.5.tgz#59d339d58d0b1950435f4043e74e2510005e2c4a"
  integrity sha1-WdM51Y0LGVBDX0BD504lEABeLEo=
  dependencies:
    "@babel/helper-get-function-arity" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-property-literals@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.10.4.tgz#f6fe54b6590352298785b83edd815d214c42e3c0"
  integrity sha1-9v5UtlkDUimHhbg+3YFdIUxC48A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-regenerator@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.10.4.tgz#2015e59d839074e76838de2159db421966fd8b63"
  integrity sha1-IBXlnYOQdOdoON4hWdtCGWb9i2M=
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.10.4.tgz#8f2682bcdcef9ed327e1b0861585d7013f8a54dd"
  integrity sha1-jyaCvNzvntMn4bCGFYXXAT+KVN0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-runtime@^7.9.6":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.10.5.tgz#3b39b7b24830e0c2d8ff7a4489fe5cf99fbace86"
  integrity sha1-Ozm3skgw4MLY/3pEif5c+Z+6zoY=
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    resolve "^1.8.1"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.10.4.tgz#9fd25ec5cdd555bb7f473e5e6ee1c971eede4dd6"
  integrity sha1-n9Jexc3VVbt/Rz5ebuHJce7eTdY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-spread@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.10.4.tgz#4e2c85ea0d6abaee1b24dcfbbae426fe8d674cff"
  integrity sha1-TiyF6g1quu4bJNz7uuQm/o1nTP8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-sticky-regex@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.10.4.tgz#8f3889ee8657581130a29d9cc91d7c73b7c4a28d"
  integrity sha1-jziJ7oZXWBEwop2cyR18c7fEoo0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-regex" "^7.10.4"

"@babel/plugin-transform-template-literals@^7.10.4":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.10.5.tgz#78bc5d626a6642db3312d9d0f001f5e7639fde8c"
  integrity sha1-eLxdYmpmQtszEtnQ8AH152Of3ow=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-typeof-symbol@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.10.4.tgz#9509f1a7eec31c4edbffe137c16cc33ff0bc5bfc"
  integrity sha1-lQnxp+7DHE7b/+E3wWzDP/C8W/w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-unicode-escapes@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.10.4.tgz#feae523391c7651ddac115dae0a9d06857892007"
  integrity sha1-/q5SM5HHZR3awRXa4KnQaFeJIAc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-unicode-regex@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.10.4.tgz#e56d71f9282fac6db09c82742055576d5e6d80a8"
  integrity sha1-5W1x+SgvrG2wnIJ0IFVXbV5tgKg=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/preset-env@^7.9.6":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/preset-env/download/@babel/preset-env-7.10.4.tgz#fbf57f9a803afd97f4f32e4f798bb62e4b2bef5f"
  integrity sha1-+/V/moA6/Zf08y5PeYu2Lksr718=
  dependencies:
    "@babel/compat-data" "^7.10.4"
    "@babel/helper-compilation-targets" "^7.10.4"
    "@babel/helper-module-imports" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-proposal-async-generator-functions" "^7.10.4"
    "@babel/plugin-proposal-class-properties" "^7.10.4"
    "@babel/plugin-proposal-dynamic-import" "^7.10.4"
    "@babel/plugin-proposal-json-strings" "^7.10.4"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.10.4"
    "@babel/plugin-proposal-numeric-separator" "^7.10.4"
    "@babel/plugin-proposal-object-rest-spread" "^7.10.4"
    "@babel/plugin-proposal-optional-catch-binding" "^7.10.4"
    "@babel/plugin-proposal-optional-chaining" "^7.10.4"
    "@babel/plugin-proposal-private-methods" "^7.10.4"
    "@babel/plugin-proposal-unicode-property-regex" "^7.10.4"
    "@babel/plugin-syntax-async-generators" "^7.8.0"
    "@babel/plugin-syntax-class-properties" "^7.10.4"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"
    "@babel/plugin-syntax-json-strings" "^7.8.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"
    "@babel/plugin-syntax-top-level-await" "^7.10.4"
    "@babel/plugin-transform-arrow-functions" "^7.10.4"
    "@babel/plugin-transform-async-to-generator" "^7.10.4"
    "@babel/plugin-transform-block-scoped-functions" "^7.10.4"
    "@babel/plugin-transform-block-scoping" "^7.10.4"
    "@babel/plugin-transform-classes" "^7.10.4"
    "@babel/plugin-transform-computed-properties" "^7.10.4"
    "@babel/plugin-transform-destructuring" "^7.10.4"
    "@babel/plugin-transform-dotall-regex" "^7.10.4"
    "@babel/plugin-transform-duplicate-keys" "^7.10.4"
    "@babel/plugin-transform-exponentiation-operator" "^7.10.4"
    "@babel/plugin-transform-for-of" "^7.10.4"
    "@babel/plugin-transform-function-name" "^7.10.4"
    "@babel/plugin-transform-literals" "^7.10.4"
    "@babel/plugin-transform-member-expression-literals" "^7.10.4"
    "@babel/plugin-transform-modules-amd" "^7.10.4"
    "@babel/plugin-transform-modules-commonjs" "^7.10.4"
    "@babel/plugin-transform-modules-systemjs" "^7.10.4"
    "@babel/plugin-transform-modules-umd" "^7.10.4"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.10.4"
    "@babel/plugin-transform-new-target" "^7.10.4"
    "@babel/plugin-transform-object-super" "^7.10.4"
    "@babel/plugin-transform-parameters" "^7.10.4"
    "@babel/plugin-transform-property-literals" "^7.10.4"
    "@babel/plugin-transform-regenerator" "^7.10.4"
    "@babel/plugin-transform-reserved-words" "^7.10.4"
    "@babel/plugin-transform-shorthand-properties" "^7.10.4"
    "@babel/plugin-transform-spread" "^7.10.4"
    "@babel/plugin-transform-sticky-regex" "^7.10.4"
    "@babel/plugin-transform-template-literals" "^7.10.4"
    "@babel/plugin-transform-typeof-symbol" "^7.10.4"
    "@babel/plugin-transform-unicode-escapes" "^7.10.4"
    "@babel/plugin-transform-unicode-regex" "^7.10.4"
    "@babel/preset-modules" "^0.1.3"
    "@babel/types" "^7.10.4"
    browserslist "^4.12.0"
    core-js-compat "^3.6.2"
    invariant "^2.2.2"
    levenary "^1.1.1"
    semver "^5.5.0"

"@babel/preset-modules@^0.1.3":
  version "0.1.3"
  resolved "https://r.cnpmjs.org/@babel/preset-modules/download/@babel/preset-modules-0.1.3.tgz#13242b53b5ef8c883c3cf7dddd55b36ce80fbc72"
  integrity sha1-EyQrU7XvjIg8PPfd3VWzbOgPvHI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@^7.8.4", "@babel/runtime@^7.9.6":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/runtime/download/@babel/runtime-7.10.5.tgz#303d8bd440ecd5a491eae6117fd3367698674c5c"
  integrity sha1-MD2L1EDs1aSR6uYRf9M2dphnTFw=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.10.4":
  version "7.10.4"
  resolved "https://r.cnpmjs.org/@babel/template/download/@babel/template-7.10.4.tgz#3251996c4200ebc71d1a8fc405fba940f36ba278"
  integrity sha1-MlGZbEIA68cdGo/EBfupQPNrong=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/parser" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/traverse@^7.10.4", "@babel/traverse@^7.10.5", "@babel/traverse@^7.7.0":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/traverse/download/@babel/traverse-7.10.5.tgz#77ce464f5b258be265af618d8fddf0536f20b564"
  integrity sha1-d85GT1sli+Jlr2GNj93wU28gtWQ=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.10.5"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-split-export-declaration" "^7.10.4"
    "@babel/parser" "^7.10.5"
    "@babel/types" "^7.10.5"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.19"

"@babel/types@^7.10.4", "@babel/types@^7.10.5", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  version "7.10.5"
  resolved "https://r.cnpmjs.org/@babel/types/download/@babel/types-7.10.5.tgz#d88ae7e2fde86bfbfe851d4d81afa70a997b5d15"
  integrity sha1-2Irn4v3oa/v+hR1Nga+nCpl7XRU=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    lodash "^4.17.19"
    to-fast-properties "^2.0.0"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "https://r.cnpmjs.org/@hapi/address/download/@hapi/address-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "https://r.cnpmjs.org/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "https://r.cnpmjs.org/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz#fde96064ca446dec8c55a8c2f130957b070c6e06"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.1":
  version "15.1.1"
  resolved "https://r.cnpmjs.org/@hapi/joi/download/@hapi/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "https://r.cnpmjs.org/@hapi/topo/download/@hapi/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  version "1.0.6"
  resolved "https://r.cnpmjs.org/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz#be7c7846128b88f6a9b1d1261a0ad06eb5c0fdf8"
  integrity sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=
  dependencies:
    cssnano "^4.0.0"
    cssnano-preset-default "^4.0.0"
    postcss "^7.0.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://r.cnpmjs.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://r.cnpmjs.org/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  version "1.7.1"
  resolved "https://r.cnpmjs.org/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.7.1.tgz#706f64bcb4a8b9642b48ae3ace444c70334d615d"
  integrity sha1-cG9kvLSouWQrSK46zkRMcDNNYV0=
  dependencies:
    chalk "^1.1.3"
    error-stack-parser "^2.0.0"
    string-width "^2.0.0"

"@soda/get-current-script@^1.0.0":
  version "1.0.2"
  resolved "https://r.cnpmjs.org/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz#a53515db25d8038374381b73af20bb4f2e508d87"
  integrity sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "https://r.cnpmjs.org/@types/color-name/download/@types/color-name-1.1.1.tgz#1c1261bbeaa10a8055bbc5d8ab84b7b2afc846a0"
  integrity sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA=

"@types/glob@^7.1.1":
  version "7.1.3"
  resolved "https://r.cnpmjs.org/@types/glob/download/@types/glob-7.1.3.tgz#e6ba80f36b7daad2c685acd9266382e68985c183"
  integrity sha1-5rqA82t9qtLGhazZJmOC5omFwYM=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/json-schema@^7.0.4":
  version "7.0.5"
  resolved "https://r.cnpmjs.org/@types/json-schema/download/@types/json-schema-7.0.5.tgz#dcce4430e64b443ba8945f0290fb564ad5bac6dd"
  integrity sha1-3M5EMOZLRDuolF8CkPtWStW6xt0=

"@types/minimatch@*":
  version "3.0.3"
  resolved "https://r.cnpmjs.org/@types/minimatch/download/@types/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
  integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=

"@types/node@*":
  version "14.0.23"
  resolved "https://r.cnpmjs.org/@types/node/download/@types/node-14.0.23.tgz#676fa0883450ed9da0bb24156213636290892806"
  integrity sha1-Z2+giDRQ7Z2guyQVYhNjYpCJKAY=

"@types/normalize-package-data@^2.4.0":
  version "2.4.0"
  resolved "https://r.cnpmjs.org/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz#e486d0d97396d79beedd0a6e33f4534ff6b4973e"
  integrity sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=

"@types/q@^1.5.1":
  version "1.5.4"
  resolved "https://r.cnpmjs.org/@types/q/download/@types/q-1.5.4.tgz#15925414e0ad2cd765bfef58842f7e26a7accb24"
  integrity sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ=

"@vue/babel-helper-vue-jsx-merge-props@^1.0.0":
  version "1.0.0"
  resolved "https://r.cnpmjs.org/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.0.0.tgz#048fe579958da408fb7a8b2a3ec050b50a661040"
  integrity sha1-BI/leZWNpAj7eosqPsBQtQpmEEA=

"@vue/babel-plugin-transform-vue-jsx@^1.1.2":
  version "1.1.2"
  resolved "https://r.cnpmjs.org/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.1.2.tgz#c0a3e6efc022e75e4247b448a8fc6b86f03e91c0"
  integrity sha1-wKPm78Ai515CR7RIqPxrhvA+kcA=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^4.4.6":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/babel-preset-app/download/@vue/babel-preset-app-4.4.6.tgz#92aef916e83f1abe0a2ed1e9e2cef5b332f958c4"
  integrity sha1-kq75Fug/Gr4KLtHp4s71szL5WMQ=
  dependencies:
    "@babel/core" "^7.9.6"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.9.6"
    "@babel/preset-env" "^7.9.6"
    "@babel/runtime" "^7.9.6"
    "@vue/babel-preset-jsx" "^1.1.2"
    babel-plugin-dynamic-import-node "^2.3.3"
    core-js "^3.6.5"
    core-js-compat "^3.6.5"
    semver "^6.1.0"

"@vue/babel-preset-jsx@^1.1.2":
  version "1.1.2"
  resolved "https://r.cnpmjs.org/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.1.2.tgz#2e169eb4c204ea37ca66c2ea85a880bfc99d4f20"
  integrity sha1-LhaetMIE6jfKZsLqhaiAv8mdTyA=
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    "@vue/babel-sugar-functional-vue" "^1.1.2"
    "@vue/babel-sugar-inject-h" "^1.1.2"
    "@vue/babel-sugar-v-model" "^1.1.2"
    "@vue/babel-sugar-v-on" "^1.1.2"

"@vue/babel-sugar-functional-vue@^1.1.2":
  version "1.1.2"
  resolved "https://r.cnpmjs.org/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.1.2.tgz#f7e24fba09e6f1ee70104560a8808057555f1a9a"
  integrity sha1-9+JPugnm8e5wEEVgqICAV1VfGpo=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.1.2":
  version "1.1.2"
  resolved "https://r.cnpmjs.org/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.1.2.tgz#8a5276b6d8e2ed16ffc8078aad94236274e6edf0"
  integrity sha1-ilJ2ttji7Rb/yAeKrZQjYnTm7fA=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.1.2":
  version "1.1.2"
  resolved "https://r.cnpmjs.org/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.1.2.tgz#1ff6fd1b800223fc9cb1e84dceb5e52d737a8192"
  integrity sha1-H/b9G4ACI/ycsehNzrXlLXN6gZI=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.0.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.1.2":
  version "1.1.2"
  resolved "https://r.cnpmjs.org/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.1.2.tgz#b2ef99b8f2fab09fbead25aad70ef42e1cf5b13b"
  integrity sha1-su+ZuPL6sJ++rSWq1w70Lhz1sTs=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.1.2"
    camelcase "^5.0.0"

"@vue/cli-overlay@^4.4.6":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-overlay/download/@vue/cli-overlay-4.4.6.tgz#58f996066d8c0a0a45ad4b5c6f3f213f9945a9ba"
  integrity sha1-WPmWBm2MCgpFrUtcbz8hP5lFqbo=

"@vue/cli-plugin-babel@~4.4.0":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-4.4.6.tgz#d5d750d4f74900c00836e19c164193650df26f3b"
  integrity sha1-1ddQ1PdJAMAINuGcFkGTZQ3ybzs=
  dependencies:
    "@babel/core" "^7.9.6"
    "@vue/babel-preset-app" "^4.4.6"
    "@vue/cli-shared-utils" "^4.4.6"
    babel-loader "^8.1.0"
    cache-loader "^4.1.0"
    thread-loader "^2.1.3"
    webpack "^4.0.0"

"@vue/cli-plugin-eslint@~4.4.0":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-4.4.6.tgz#442d62a70dd93e4a549ff9164d2d10f4e97a58f1"
  integrity sha1-RC1ipw3ZPkpUn/kWTS0Q9Ol6WPE=
  dependencies:
    "@vue/cli-shared-utils" "^4.4.6"
    eslint-loader "^2.2.1"
    globby "^9.2.0"
    inquirer "^7.1.0"
    webpack "^4.0.0"
    yorkie "^2.0.0"

"@vue/cli-plugin-router@^4.4.6":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-plugin-router/download/@vue/cli-plugin-router-4.4.6.tgz#db5bb1c92afd2046376e4faa90270d6363d4b9b8"
  integrity sha1-21uxySr9IEY3bk+qkCcNY2PUubg=
  dependencies:
    "@vue/cli-shared-utils" "^4.4.6"

"@vue/cli-plugin-vuex@^4.4.6":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-4.4.6.tgz#44b596f0d5236e0a2ce1aa4757bda5207c230be0"
  integrity sha1-RLWW8NUjbgos4apHV72lIHwjC+A=

"@vue/cli-service@~4.4.0":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-service/download/@vue/cli-service-4.4.6.tgz#83ca058d081eb145bbc1ae2fe8069d2d9ddf7e18"
  integrity sha1-g8oFjQgesUW7wa4v6AadLZ3ffhg=
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@vue/cli-overlay" "^4.4.6"
    "@vue/cli-plugin-router" "^4.4.6"
    "@vue/cli-plugin-vuex" "^4.4.6"
    "@vue/cli-shared-utils" "^4.4.6"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    acorn "^7.2.0"
    acorn-walk "^7.1.1"
    address "^1.1.2"
    autoprefixer "^9.8.0"
    browserslist "^4.12.0"
    cache-loader "^4.1.0"
    case-sensitive-paths-webpack-plugin "^2.3.0"
    cli-highlight "^2.1.4"
    clipboardy "^2.3.0"
    cliui "^6.0.0"
    copy-webpack-plugin "^5.1.1"
    css-loader "^3.5.3"
    cssnano "^4.1.10"
    debug "^4.1.1"
    default-gateway "^5.0.5"
    dotenv "^8.2.0"
    dotenv-expand "^5.1.0"
    file-loader "^4.2.0"
    fs-extra "^7.0.1"
    globby "^9.2.0"
    hash-sum "^2.0.0"
    html-webpack-plugin "^3.2.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    lodash.transform "^4.6.0"
    mini-css-extract-plugin "^0.9.0"
    minimist "^1.2.5"
    pnp-webpack-plugin "^1.6.4"
    portfinder "^1.0.26"
    postcss-loader "^3.0.0"
    ssri "^7.1.0"
    terser-webpack-plugin "^2.3.6"
    thread-loader "^2.1.3"
    url-loader "^2.2.0"
    vue-loader "^15.9.2"
    vue-style-loader "^4.1.2"
    webpack "^4.0.0"
    webpack-bundle-analyzer "^3.8.0"
    webpack-chain "^6.4.0"
    webpack-dev-server "^3.11.0"
    webpack-merge "^4.2.2"

"@vue/cli-shared-utils@^4.4.6":
  version "4.4.6"
  resolved "https://r.cnpmjs.org/@vue/cli-shared-utils/download/@vue/cli-shared-utils-4.4.6.tgz#0ec59880920736c6dd79079ac0b5ceac29fa55e1"
  integrity sha1-DsWYgJIHNsbdeQeawLXOrCn6VeE=
  dependencies:
    "@hapi/joi" "^15.0.1"
    chalk "^2.4.2"
    execa "^1.0.0"
    launch-editor "^2.2.1"
    lru-cache "^5.1.1"
    node-ipc "^9.1.1"
    open "^6.3.0"
    ora "^3.4.0"
    read-pkg "^5.1.1"
    request "^2.88.2"
    semver "^6.1.0"
    strip-ansi "^6.0.0"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  version "3.1.2"
  resolved "https://r.cnpmjs.org/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.1.2.tgz#8213a5ff3202f9f2137fe55370f9e8b9656081c3"
  integrity sha1-ghOl/zIC+fITf+VTcPnouWVggcM=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.14"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2"

"@vue/preload-webpack-plugin@^1.1.0":
  version "1.1.1"
  resolved "https://r.cnpmjs.org/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.1.tgz#18723530d304f443021da2292d6ec9502826104a"
  integrity sha1-GHI1MNME9EMCHaIpLW7JUCgmEEo=

"@vue/web-component-wrapper@^1.2.0":
  version "1.2.0"
  resolved "https://r.cnpmjs.org/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz#bb0e46f1585a7e289b4ee6067dcc5a6ae62f1dd1"
  integrity sha1-uw5G8VhafiibTuYGfcxaauYvHdE=

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz#bd850604b4042459a5a41cd7d338cbed695ed964"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz#3c3d3b271bddfc84deb00f71344438311d52ffb4"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz#203f676e333b96c9da2eeab3ccef33c45928b6a2"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz#a1442d269c5feb23fcbc9ef759dac3547f29de00"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz#647f8892cd2043a82ac0c8c5e75c36f1d9159f27"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz#c05256b71244214671f4b08ec108ad63b70eddb8"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz#25d8884b76839871a08a6c6f806c3979ef712f07"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz#4fed8beac9b8c14f8c58b70d124d549dd1fe5790"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz#5a4138d5a6292ba18b04c5ae49717e4167965346"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz#15c7a0fbaae83fb26143bbacf6d6df1702ad39e4"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz#f19ca0b76a6dc55623a09cffa769e838fa1e1c95"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz#04d33b636f78e6a6813227e82402f7637b6229ab"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz#3fe6d79d3f0f922183aa86002c42dd256cfee9cf"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz#50bc70ec68ded8e2763b01a1418bf43491a7a49c"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz#2211181e5b31326443cc8112eb9f0b9028721a61"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz#9d48e44826df4a6598294aa6c87469d642fff65e"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz#3031115d79ac5bd261556cecc3fa90a3ef451914"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "https://r.cnpmjs.org/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz#4935d54c85fef637b00ce9f52377451d00d47899"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://r.cnpmjs.org/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://r.cnpmjs.org/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abbrev@1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://r.cnpmjs.org/accepts/download/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-jsx@^5.2.0:
  version "5.2.0"
  resolved "https://r.cnpmjs.org/acorn-jsx/download/acorn-jsx-5.2.0.tgz#4c66069173d6fdd68ed85239fc256226182b2ebe"
  integrity sha1-TGYGkXPW/daO2FI5/CViJhgrLr4=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://r.cnpmjs.org/acorn-walk/download/acorn-walk-7.2.0.tgz#0de889a601203909b0fbe07b8938dc21d2e967bc"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^6.4.1:
  version "6.4.1"
  resolved "https://r.cnpmjs.org/acorn/download/acorn-6.4.1.tgz#531e58ba3f51b9dacb9a6646ca4debf5b14ca474"
  integrity sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ=

acorn@^7.1.1, acorn@^7.2.0:
  version "7.3.1"
  resolved "https://r.cnpmjs.org/acorn/download/acorn-7.3.1.tgz#85010754db53c3fbaf3b9ea3e083aa5c5d147ffd"
  integrity sha1-hQEHVNtTw/uvO56j4IOqXF0Uf/0=

address@^1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/address/download/address-1.1.2.tgz#bf1116c9c758c51b7a933d296b72c221ed9428b6"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

aggregate-error@^3.0.0:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/aggregate-error/download/aggregate-error-3.0.1.tgz#db2fe7246e536f40d9b5442a39e117d7dd6a24e0"
  integrity sha1-2y/nJG5Tb0DZtUQqOeEX191qJOA=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/ajv-errors/download/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1:
  version "3.5.1"
  resolved "https://r.cnpmjs.org/ajv-keywords/download/ajv-keywords-3.5.1.tgz#b83ca89c5d42d69031f424cad49aada0236c6957"
  integrity sha1-uDyonF1C1pAx9CTK1JqtoCNsaVc=

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.2, ajv@^6.5.5:
  version "6.12.3"
  resolved "https://r.cnpmjs.org/ajv/download/ajv-6.12.3.tgz#18c5af38a111ddeb4f2697bd78d68abc1cabd706"
  integrity sha1-GMWvOKER3etPJpe9eNaKvByr1wY=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

alphanum-sort@^1.0.0:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz#97a1119649b211ad33691d9f9f486a8ec9fbe0a3"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/amdefine/download/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://r.cnpmjs.org/ansi-colors/download/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-escapes@^4.2.1:
  version "4.3.1"
  resolved "https://r.cnpmjs.org/ansi-escapes/download/ansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-html@0.0.7:
  version "0.0.7"
  resolved "https://r.cnpmjs.org/ansi-html/download/ansi-html-0.0.7.tgz#813584021962a9e9e6fd039f940d12f56ca7859e"
  integrity sha1-gTWEAhliqenm/QOflA0S9WynhZ4=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/ansi-regex/download/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://r.cnpmjs.org/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://r.cnpmjs.org/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://r.cnpmjs.org/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "https://r.cnpmjs.org/ansi-styles/download/ansi-styles-4.2.1.tgz#90ae75c424d008d2624c5bf29ead3177ebfcf359"
  integrity sha1-kK51xCTQCNJiTFvynq0xd+v881k=
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.1:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/anymatch/download/anymatch-3.1.1.tgz#c55ecf02185e2469259399310c173ce31233b142"
  integrity sha1-xV7PAhheJGklk5kxDBc84xIzsUI=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

arch@^2.1.1:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/arch/download/arch-2.1.2.tgz#0c52bbe7344bb4fa260c443d2cbad9c00ff2f0bf"
  integrity sha1-DFK75zRLtPomDEQ9LLrZwA/y8L8=

are-we-there-yet@~1.1.2:
  version "1.1.5"
  resolved "https://r.cnpmjs.org/are-we-there-yet/download/are-we-there-yet-1.1.5.tgz#4b35c2944f062a8bfcda66410760350fe9ddfc21"
  integrity sha1-SzXClE8GKov82mZBB2A1D+nd/CE=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://r.cnpmjs.org/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/array-flatten/download/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://r.cnpmjs.org/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

asn1.js@^4.0.0:
  version "4.10.1"
  resolved "https://r.cnpmjs.org/asn1.js/download/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  integrity sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.0, asn1@~0.2.3:
  version "0.2.4"
  resolved "https://r.cnpmjs.org/asn1/download/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "https://r.cnpmjs.org/assert/download/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.1:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/async-each/download/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "https://r.cnpmjs.org/async-foreach/download/async-foreach-0.1.3.tgz#36121f845c0578172de419a97dbeb1d16ec34542"
  integrity sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@~1.8.1:
  version "1.8.5"
  resolved "https://r.cnpmjs.org/async-validator/download/async-validator-1.8.5.tgz#dc3e08ec1fd0dddb67e60842f02c0cd1cec6d7f0"
  integrity sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=
  dependencies:
    babel-runtime "6.x"

async@^2.6.2:
  version "2.6.3"
  resolved "https://r.cnpmjs.org/async/download/async-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

async@~0.9.0:
  version "0.9.2"
  resolved "https://r.cnpmjs.org/async/download/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://r.cnpmjs.org/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^9.8.0:
  version "9.8.5"
  resolved "https://r.cnpmjs.org/autoprefixer/download/autoprefixer-9.8.5.tgz#2c225de229ddafe1d1424c02791d0c3e10ccccaa"
  integrity sha1-LCJd4indr+HRQkwCeR0MPhDMzKo=
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001097"
    colorette "^1.2.0"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://r.cnpmjs.org/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.10.0"
  resolved "https://r.cnpmjs.org/aws4/download/aws4-1.10.0.tgz#a17b3a8ea811060e74d47d306122400ad4497ae2"
  integrity sha1-oXs6jqgRBg501H0wYSJACtRJeuI=

axios@^0.19.2:
  version "0.19.2"
  resolved "https://r.cnpmjs.org/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "https://r.cnpmjs.org/babel-eslint/download/babel-eslint-10.1.0.tgz#6968e568a910b78fb3779cdd8b6ac2f479943232"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-helper-vue-jsx-merge-props@^2.0.0:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz#22aebd3b33902328e513293a8e4992b384f9f1b6"
  integrity sha1-Iq69OzOQIyjlEyk6jkmSs4T58bY=

babel-loader@^8.1.0:
  version "8.1.0"
  resolved "https://r.cnpmjs.org/babel-loader/download/babel-loader-8.1.0.tgz#c611d5112bd5209abe8b9fa84c3e4da25275f1c3"
  integrity sha1-xhHVESvVIJq+i5+oTD5NolJ18cM=
  dependencies:
    find-cache-dir "^2.1.0"
    loader-utils "^1.4.0"
    mkdirp "^0.5.3"
    pify "^4.0.1"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://r.cnpmjs.org/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz#84fda19c976ec5c6defef57f9427b3def66e17a3"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-runtime@6.x:
  version "6.26.0"
  resolved "https://r.cnpmjs.org/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.0.2:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/base64-js/download/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"
  integrity sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "https://r.cnpmjs.org/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://r.cnpmjs.org/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0, bcrypt-pbkdf@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "https://r.cnpmjs.org/bfj/download/bfj-6.1.2.tgz#325c861a822bcb358a41c78a33b8e6e2086dde7f"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://r.cnpmjs.org/big.js/download/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://r.cnpmjs.org/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://r.cnpmjs.org/binary-extensions/download/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/binary-extensions/download/binary-extensions-2.1.0.tgz#30fa40c9e7fe07dbc895678cd287024dea241dd9"
  integrity sha1-MPpAyef+B9vIlWeM0ocCTeokHdk=

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://r.cnpmjs.org/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bl@^1.0.0:
  version "1.2.3"
  resolved "https://r.cnpmjs.org/bl/download/bl-1.2.3.tgz#1e8dd80142eac80d7158c9dccc047fb620e035e7"
  integrity sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^4.0.3:
  version "4.0.4"
  resolved "https://r.cnpmjs.org/bl/download/bl-4.0.4.tgz#f4fda39f81a811d0df6368c1ed91dae499d1c900"
  integrity sha1-9P2jn4GoEdDfY2jB7ZHa5JnRyQA=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

block-stream@*:
  version "0.0.9"
  resolved "https://r.cnpmjs.org/block-stream/download/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  integrity sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=
  dependencies:
    inherits "~2.0.0"

bluebird@^3.1.1, bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://r.cnpmjs.org/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.4.0:
  version "4.11.9"
  resolved "https://r.cnpmjs.org/bn.js/download/bn.js-4.11.9.tgz#26d556829458f9d1e81fc48952493d0ba3507828"
  integrity sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=

bn.js@^5.1.1:
  version "5.1.2"
  resolved "https://r.cnpmjs.org/bn.js/download/bn.js-5.1.2.tgz#c9686902d3c9a27729f43ab10f9d79c2004da7b0"
  integrity sha1-yWhpAtPJoncp9DqxD515wgBNp7A=

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://r.cnpmjs.org/body-parser/download/body-parser-1.19.0.tgz#96b2709e57c9c4e09a6fd66a8fd979844f69f08a"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://r.cnpmjs.org/bonjour/download/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://r.cnpmjs.org/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://r.cnpmjs.org/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@~3.0.2:
  version "3.0.2"
  resolved "https://r.cnpmjs.org/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/browserify-rsa/download/browserify-rsa-4.0.1.tgz#21e0abfaf6f2029cf2fafb133567a701d4135524"
  integrity sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.0"
  resolved "https://r.cnpmjs.org/browserify-sign/download/browserify-sign-4.2.0.tgz#545d0b1b07e6b2c99211082bf1b12cce7a0b0e11"
  integrity sha1-VF0LGwfmssmSEQgr8bEsznoLDhE=
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.2"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://r.cnpmjs.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.0.0, browserslist@^4.12.0, browserslist@^4.8.5:
  version "4.13.0"
  resolved "https://r.cnpmjs.org/browserslist/download/browserslist-4.13.0.tgz#42556cba011e1b0a2775b611cba6a8eca18e940d"
  integrity sha1-QlVsugEeGwondbYRy6ao7KGOlA0=
  dependencies:
    caniuse-lite "^1.0.30001093"
    electron-to-chromium "^1.3.488"
    escalade "^3.0.1"
    node-releases "^1.1.58"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"
  integrity sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/buffer-alloc/download/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  integrity sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://r.cnpmjs.org/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/buffer-fill/download/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"
  integrity sha1-+PeLdniYiO858gXNY39o5wISKyw=

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-json@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/buffer-json/download/buffer-json-2.0.0.tgz#f73e13b1e42f196fe2fd67d001c7d7107edd7c23"
  integrity sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://r.cnpmjs.org/buffer/download/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://r.cnpmjs.org/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/bytes/download/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cacache@^12.0.2, cacache@^12.0.3:
  version "12.0.4"
  resolved "https://r.cnpmjs.org/cacache/download/cacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cacache@^13.0.1:
  version "13.0.1"
  resolved "https://r.cnpmjs.org/cacache/download/cacache-13.0.1.tgz#a8000c21697089082f85287a1aec6e382024a71c"
  integrity sha1-qAAMIWlwiQgvhSh6GuxuOCAkpxw=
  dependencies:
    chownr "^1.1.2"
    figgy-pudding "^3.5.1"
    fs-minipass "^2.0.0"
    glob "^7.1.4"
    graceful-fs "^4.2.2"
    infer-owner "^1.0.4"
    lru-cache "^5.1.1"
    minipass "^3.0.0"
    minipass-collect "^1.0.2"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.2"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    p-map "^3.0.0"
    promise-inflight "^1.0.1"
    rimraf "^2.7.1"
    ssri "^7.0.0"
    unique-filename "^1.1.1"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-loader@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/cache-loader/download/cache-loader-4.1.0.tgz#9948cae353aec0a1fcb1eafda2300816ec85387e"
  integrity sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=
  dependencies:
    buffer-json "^2.0.0"
    find-cache-dir "^3.0.0"
    loader-utils "^1.2.3"
    mkdirp "^0.5.1"
    neo-async "^2.6.1"
    schema-utils "^2.0.0"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz#26d208ea89e37b5cbde60250a15f031c16a4d66b"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/camel-case/download/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/camelcase-keys/download/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://r.cnpmjs.org/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/caniuse-api/download/caniuse-api-3.0.0.tgz#5e4d90e2274961d46291997df599e3ed008ee4c0"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001093, caniuse-lite@^1.0.30001097:
  version "1.0.30001100"
  resolved "https://r.cnpmjs.org/caniuse-lite/download/caniuse-lite-1.0.30001100.tgz#2a58615e0c01cf716ab349b20ca4d86ef944aa4e"
  integrity sha1-KlhhXgwBz3Fqs0myDKTYbvlEqk4=

case-sensitive-paths-webpack-plugin@^2.3.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.3.0.tgz#23ac613cc9a856e4f88ff8bb73bbb5e989825cf7"
  integrity sha1-I6xhPMmoVuT4j/i7c7u16YmCXPc=

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://r.cnpmjs.org/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://r.cnpmjs.org/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/chalk/download/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/chalk/download/chalk-4.1.0.tgz#4e14870a618d9e2edd97dd8345fd9d9dc315646a"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://r.cnpmjs.org/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-types@^8.0.3:
  version "8.0.3"
  resolved "https://r.cnpmjs.org/check-types/download/check-types-8.0.3.tgz#3356cca19c889544f2d7a95ed49ce508a0ecf552"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://r.cnpmjs.org/chokidar/download/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.0:
  version "3.4.0"
  resolved "https://r.cnpmjs.org/chokidar/download/chokidar-3.4.0.tgz#b30611423ce376357c765b9b8f904b9fba3c0be8"
  integrity sha1-swYRQjzjdjV8dlubj5BLn7o8C+g=
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.4.0"
  optionalDependencies:
    fsevents "~2.1.2"

chownr@^1.1.1, chownr@^1.1.2:
  version "1.1.4"
  resolved "https://r.cnpmjs.org/chownr/download/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz#234090ee97c7d4ad1a2c4beae27505deffc608a4"
  integrity sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=
  dependencies:
    tslib "^1.9.0"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://r.cnpmjs.org/ci-info/download/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://r.cnpmjs.org/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@4.2.x:
  version "4.2.3"
  resolved "https://r.cnpmjs.org/clean-css/download/clean-css-4.2.3.tgz#507b5de7d97b48ee53d84adb0160ff6216380f78"
  integrity sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g=
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.4:
  version "2.1.4"
  resolved "https://r.cnpmjs.org/cli-highlight/download/cli-highlight-2.1.4.tgz#098cb642cf17f42adc1c1145e07f960ec4d7522b"
  integrity sha1-CYy2Qs8X9CrcHBFF4H+WDsTXUis=
  dependencies:
    chalk "^3.0.0"
    highlight.js "^9.6.0"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^5.1.1"
    yargs "^15.0.0"

cli-spinners@^2.0.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/cli-spinners/download/cli-spinners-2.3.0.tgz#0632239a4b5aa4c958610142c34bb7a651fc8df5"
  integrity sha1-BjIjmktapMlYYQFCw0u3plH8jfU=

cli-spinners@^2.5.0:
  version "2.5.0"
  resolved "https://r.cnpmjs.org/cli-spinners/download/cli-spinners-2.5.0.tgz#12763e47251bf951cb75c201dfa58ff1bcb2d047"
  integrity sha1-EnY+RyUb+VHLdcIB36WP8byy0Ec=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

clipboardy@^2.3.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/clipboardy/download/clipboardy-2.3.0.tgz#3c2903650c68e46a91b388985bc2774287dba290"
  integrity sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://r.cnpmjs.org/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://r.cnpmjs.org/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/coa/download/coa-2.0.2.tgz#43f6c21151b4ef2bf57187db0d73de229e3e7ec3"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.1:
  version "1.9.3"
  resolved "https://r.cnpmjs.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://r.cnpmjs.org/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.5.2:
  version "1.5.3"
  resolved "https://r.cnpmjs.org/color-string/download/color-string-1.5.3.tgz#c9bbc5f01b58b5492f3d6857459cb6590ce204cc"
  integrity sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.0.0:
  version "3.1.2"
  resolved "https://r.cnpmjs.org/color/download/color-3.1.2.tgz#68148e7f85d41ad7649c5fa8c8106f098d229e10"
  integrity sha1-aBSOf4XUGtdknF+oyBBvCY0inhA=
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.2"

colorette@^1.2.0:
  version "1.2.1"
  resolved "https://r.cnpmjs.org/colorette/download/colorette-1.2.1.tgz#4d0b921325c14faf92633086a536db6e89564b1b"
  integrity sha1-TQuSEyXBT6+SYzCGpTbbbolWSxs=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://r.cnpmjs.org/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@2.17.x:
  version "2.17.1"
  resolved "https://r.cnpmjs.org/commander/download/commander-2.17.1.tgz#bd77ab7de6de94205ceacc72f1716d29f20a77bf"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

commander@^2.18.0, commander@^2.20.0:
  version "2.20.3"
  resolved "https://r.cnpmjs.org/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@~2.19.0:
  version "2.19.0"
  resolved "https://r.cnpmjs.org/commander/download/commander-2.19.0.tgz#f6198aa84e5b83c46054b94ddedbfed5ee9ff12a"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://r.cnpmjs.org/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compressing@^1.5.1:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/compressing/download/compressing-1.5.1.tgz#d031a3311b8c2ed6561a8431671d5a844540482d"
  integrity sha1-0DGjMRuMLtZWGoQxZx1ahEVASC0=
  dependencies:
    flushwritable "^1.0.0"
    get-ready "^1.0.0"
    iconv-lite "^0.5.0"
    mkdirp "^0.5.1"
    pump "^3.0.0"
    streamifier "^0.1.1"
    tar-stream "^1.5.2"
    yauzl "^2.7.0"
    yazl "^2.4.2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://r.cnpmjs.org/compression/download/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://r.cnpmjs.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "https://r.cnpmjs.org/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://r.cnpmjs.org/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/console-browserify/download/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://r.cnpmjs.org/consolidate/download/consolidate-0.15.1.tgz#21ab043235c71a07d45d9aad98593b0dba56bab7"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://r.cnpmjs.org/content-disposition/download/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "https://r.cnpmjs.org/convert-source-map/download/convert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://r.cnpmjs.org/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://r.cnpmjs.org/cookie/download/cookie-0.4.0.tgz#beb437e7022b3b6d49019d088665303ebe9c14ba"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://r.cnpmjs.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^5.1.1:
  version "5.1.1"
  resolved "https://r.cnpmjs.org/copy-webpack-plugin/download/copy-webpack-plugin-5.1.1.tgz#5481a03dea1123d88a988c6ff8b78247214f0b88"
  integrity sha1-VIGgPeoRI9iKmIxv+LeCRyFPC4g=
  dependencies:
    cacache "^12.0.3"
    find-cache-dir "^2.1.0"
    glob-parent "^3.1.0"
    globby "^7.1.1"
    is-glob "^4.0.1"
    loader-utils "^1.2.3"
    minimatch "^3.0.4"
    normalize-path "^3.0.0"
    p-limit "^2.2.1"
    schema-utils "^1.0.0"
    serialize-javascript "^2.1.2"
    webpack-log "^2.0.0"

core-js-compat@^3.6.2, core-js-compat@^3.6.5:
  version "3.6.5"
  resolved "https://r.cnpmjs.org/core-js-compat/download/core-js-compat-3.6.5.tgz#2a51d9a4e25dfd6e690251aa81f99e3c05481f1c"
  integrity sha1-KlHZpOJd/W5pAlGqgfmePAVIHxw=
  dependencies:
    browserslist "^4.8.5"
    semver "7.0.0"

core-js@^2.4.0:
  version "2.6.11"
  resolved "https://r.cnpmjs.org/core-js/download/core-js-2.6.11.tgz#38831469f9922bded8ee21c9dc46985e0399308c"
  integrity sha1-OIMUafmSK97Y7iHJ3EaYXgOZMIw=

core-js@^3.6.5:
  version "3.6.5"
  resolved "https://r.cnpmjs.org/core-js/download/core-js-3.6.5.tgz#7395dc273af37fb2e50e9bd3d9fe841285231d1a"
  integrity sha1-c5XcJzrzf7LlDpvT2f6EEoUjHRo=

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://r.cnpmjs.org/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

create-ecdh@^4.0.0:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/create-ecdh/download/create-ecdh-4.0.3.tgz#c9111b6f33045c4697f144787f9254cdc77c45ff"
  integrity sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://r.cnpmjs.org/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/cross-spawn/download/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  integrity sha1-ElYDfsufDF9549bvE14wdwGEuYI=
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/cross-spawn/download/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://r.cnpmjs.org/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0:
  version "7.0.3"
  resolved "https://r.cnpmjs.org/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://r.cnpmjs.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-color-names@0.0.4, css-color-names@^0.0.4:
  version "0.0.4"
  resolved "https://r.cnpmjs.org/css-color-names/download/css-color-names-0.0.4.tgz#808adc2e79cf84738069b646cb20ec27beb629e0"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz#c198940f63a76d7e36c1e71018b001721054cb22"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-loader@^3.5.3:
  version "3.6.0"
  resolved "https://r.cnpmjs.org/css-loader/download/css-loader-3.6.0.tgz#2e4b2c7e6e2d27f8c8f28f61bffcd2e6c91ef645"
  integrity sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU=
  dependencies:
    camelcase "^5.3.1"
    cssesc "^3.0.0"
    icss-utils "^4.1.1"
    loader-utils "^1.2.3"
    normalize-path "^3.0.0"
    postcss "^7.0.32"
    postcss-modules-extract-imports "^2.0.0"
    postcss-modules-local-by-default "^3.0.2"
    postcss-modules-scope "^2.2.0"
    postcss-modules-values "^3.0.0"
    postcss-value-parser "^4.1.0"
    schema-utils "^2.7.0"
    semver "^6.3.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz#3b2ff4972cc362ab88561507a95408a1432135d7"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^1.1.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/css-select/download/css-select-1.2.0.tgz#2b3a110539c5355f1cd8d314623e870b121ec858"
  integrity sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=
  dependencies:
    boolbase "~1.0.0"
    css-what "2.1"
    domutils "1.5.1"
    nth-check "~1.0.1"

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/css-select/download/css-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://r.cnpmjs.org/css-tree/download/css-tree-1.0.0-alpha.37.tgz#98bebd62c4c1d9f960ec340cf9f7522e30709a22"
  integrity sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@1.0.0-alpha.39:
  version "1.0.0-alpha.39"
  resolved "https://r.cnpmjs.org/css-tree/download/css-tree-1.0.0-alpha.39.tgz#2bff3ffe1bb3f776cf7eefd91ee5cba77a149eeb"
  integrity sha1-K/8//huz93bPfu/ZHuXLp3oUnus=
  dependencies:
    mdn-data "2.0.6"
    source-map "^0.6.1"

css-what@2.1:
  version "2.1.3"
  resolved "https://r.cnpmjs.org/css-what/download/css-what-2.1.3.tgz#a6d7604573365fe74686c3f311c56513d88285f2"
  integrity sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI=

css-what@^3.2.1:
  version "3.3.0"
  resolved "https://r.cnpmjs.org/css-what/download/css-what-3.3.0.tgz#10fec696a9ece2e591ac772d759aacabac38cd39"
  integrity sha1-EP7Glqns4uWRrHctdZqsq6w4zTk=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^4.0.0, cssnano-preset-default@^4.0.7:
  version "4.0.7"
  resolved "https://r.cnpmjs.org/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz#51ec662ccfca0f88b396dcd9679cdb931be17f76"
  integrity sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.2"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz#ed3a08299f21d75741b20f3b81f194ed49cc150f"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz#c0e4ca07f5386bb17ec5e52250b4f5961365156d"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz#b26d5fd5f72a11dfe7a7846fb4c67260f96bf282"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz#574082fb2859d2db433855835d9a8456ea18bbf3"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^4.0.0, cssnano@^4.1.10:
  version "4.1.10"
  resolved "https://r.cnpmjs.org/cssnano/download/cssnano-4.1.10.tgz#0ac41f0b13d13d465487e111b778d42da631b8b2"
  integrity sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.7"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^4.0.2:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/csso/download/csso-4.0.3.tgz#0d9985dc852c7cc2b2cacfbbe1079014d1a8e903"
  integrity sha1-DZmF3IUsfMKyys+74QeQFNGo6QM=
  dependencies:
    css-tree "1.0.0-alpha.39"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://r.cnpmjs.org/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/cyclist/download/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://r.cnpmjs.org/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/de-indent/download/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "https://r.cnpmjs.org/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@=3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^3.1.1, debug@^3.2.5:
  version "3.2.6"
  resolved "https://r.cnpmjs.org/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
  version "4.1.1"
  resolved "https://r.cnpmjs.org/debug/download/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://r.cnpmjs.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/deep-equal/download/deep-equal-1.1.1.tgz#b5c98c942ceffaf7cb051e24e1434a25a2e6076a"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://r.cnpmjs.org/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@^1.2.0, deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://r.cnpmjs.org/deepmerge/download/deepmerge-1.5.2.tgz#10499d868844cdad4fee0842df8c7f6f0c95a753"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://r.cnpmjs.org/default-gateway/download/default-gateway-4.2.0.tgz#167104c7500c2115f6dd69b0a536bb8ed720552b"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-gateway@^5.0.5:
  version "5.0.5"
  resolved "https://r.cnpmjs.org/default-gateway/download/default-gateway-5.0.5.tgz#4fd6bd5d2855d39b34cc5a59505486e9aafc9b10"
  integrity sha1-T9a9XShV05s0zFpZUFSG6ar8mxA=
  dependencies:
    execa "^3.3.0"

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://r.cnpmjs.org/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^4.1.1:
  version "4.1.1"
  resolved "https://r.cnpmjs.org/del/download/del-4.1.1.tgz#9e8f117222ea44a31ff3a156c049b99052a9f0b4"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/des.js/download/des.js-1.0.1.tgz#5382142e1bdc53f85d86d53e5f4aa7deb91e0843"
  integrity sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-node@^2.0.4:
  version "2.0.4"
  resolved "https://r.cnpmjs.org/detect-node/download/detect-node-2.0.4.tgz#014ee8f8f669c5c58023da64b8179c083a28c46c"
  integrity sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://r.cnpmjs.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://r.cnpmjs.org/dir-glob/download/dir-glob-2.2.2.tgz#fa09f0694153c8918b18ba0deafae94769fc50c4"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/dns-packet/download/dns-packet-1.3.1.tgz#12aa426981075be500b910eedcd0b47dd7deda5a"
  integrity sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/dns-txt/download/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2:
  version "0.2.0"
  resolved "https://r.cnpmjs.org/dom-converter/download/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@0:
  version "0.2.2"
  resolved "https://r.cnpmjs.org/dom-serializer/download/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/domain-browser/download/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/domelementtype/download/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/domelementtype/download/domelementtype-2.0.1.tgz#1f8bdfe91f5a78063274e803b4bdcedf6e94f94d"
  integrity sha1-H4vf6R9aeAYydOgDtL3O326U+U0=

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://r.cnpmjs.org/domhandler/download/domhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domutils@1.5.1:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/domutils/download/domutils-1.5.1.tgz#dcd8488a26f563d61079e48c9f7b7e32373682cf"
  integrity sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^1.5.1, domutils@^1.7.0:
  version "1.7.0"
  resolved "https://r.cnpmjs.org/domutils/download/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-prop@^5.2.0:
  version "5.2.0"
  resolved "https://r.cnpmjs.org/dot-prop/download/dot-prop-5.2.0.tgz#c34ecc29556dc45f1f4c22697b6f4904e0cc4fcb"
  integrity sha1-w07MKVVtxF8fTCJpe29JBODMT8s=
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/dotenv-expand/download/dotenv-expand-5.1.0.tgz#3fbaf020bfd794884072ea26b1e9791d45a629f0"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^8.2.0:
  version "8.2.0"
  resolved "https://r.cnpmjs.org/dotenv/download/dotenv-8.2.0.tgz#97e619259ada750eea3e4ea3e26bceea5424b16a"
  integrity sha1-l+YZJZradQ7qPk6j4mvO6lQksWo=

duplexer@^0.1.1:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/duplexer/download/duplexer-0.1.1.tgz#ace6ff808c1ce66b57d1ebf97977acb02334cfc1"
  integrity sha1-rOb/gIwc5mtX0ev5eXessCM0z8E=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://r.cnpmjs.org/duplexify/download/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

easy-stack@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/easy-stack/download/easy-stack-1.0.0.tgz#12c91b3085a37f0baa336e9486eac4bf94e3e788"
  integrity sha1-EskbMIWjfwuqM26UhurEv5Tj54g=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts@^5.2.2:
  version "5.2.2"
  resolved "https://rg.cnpmjs.org/echarts/download/echarts-5.2.2.tgz#ec3c8b2a151cbba71ba3c2c7cf9b2f2047ce4370"
  integrity sha1-7DyLKhUcu6cbo8LHz5svIEfOQ3A=
  dependencies:
    tslib "2.3.0"
    zrender "5.2.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "https://r.cnpmjs.org/ejs/download/ejs-2.7.4.tgz#48661287573dcc53e366c7a1ae52c3a120eec9ba"
  integrity sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=

electron-to-chromium@^1.3.488:
  version "1.3.497"
  resolved "https://r.cnpmjs.org/electron-to-chromium/download/electron-to-chromium-1.3.497.tgz#de00f2f2f44c258c4577fbfbd5124b94c18bfa44"
  integrity sha1-3gDy8vRMJYxFd/v71RJLlMGL+kQ=

element-ui@^2.13.2:
  version "2.13.2"
  resolved "https://r.cnpmjs.org/element-ui/download/element-ui-2.13.2.tgz#582bf47aaaaaafe23ea1958fae217a687ad06447"
  integrity sha1-WCv0eqqqr+I+oZWPriF6aHrQZEc=
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"

elliptic@^6.0.0, elliptic@^6.5.2:
  version "6.5.3"
  resolved "https://r.cnpmjs.org/elliptic/download/elliptic-6.5.3.tgz#cb59eb2efdaf73a0bd78ccd7015a62ad6e0f93d6"
  integrity sha1-y1nrLv2vc6C9eMzXAVpirW4Pk9Y=
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://r.cnpmjs.org/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://r.cnpmjs.org/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/emojis-list/download/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://r.cnpmjs.org/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^4.1.0:
  version "4.2.0"
  resolved "https://r.cnpmjs.org/enhanced-resolve/download/enhanced-resolve-4.2.0.tgz#5d43bda4a0fd447cb0ebbe71bef8deff8805ad0d"
  integrity sha1-XUO9pKD9RHyw675xvvje/4gFrQ0=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

entities@^1.1.1:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/entities/download/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/entities/download/entities-2.0.3.tgz#5c487e5742ab93c15abb5da22759b8590ec03b7f"
  integrity sha1-XEh+V0Krk8Fau12iJ1m4WQ7AO38=

errno@^0.1.3, errno@~0.1.7:
  version "0.1.7"
  resolved "https://r.cnpmjs.org/errno/download/errno-0.1.7.tgz#4684d71779ad39af177e3f007996f7c67c852618"
  integrity sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://r.cnpmjs.org/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.0:
  version "2.0.6"
  resolved "https://r.cnpmjs.org/error-stack-parser/download/error-stack-parser-2.0.6.tgz#5a99a707bd7a4c58a797902d48d82803ede6aad8"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.17.0-next.1, es-abstract@^1.17.2, es-abstract@^1.17.5:
  version "1.17.6"
  resolved "https://r.cnpmjs.org/es-abstract/download/es-abstract-1.17.6.tgz#9142071707857b2cacc7b89ecb670316c3e2d52a"
  integrity sha1-kUIHFweFeyysx7iey2cDFsPi1So=
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.0"
    is-regex "^1.1.0"
    object-inspect "^1.7.0"
    object-keys "^1.1.1"
    object.assign "^4.1.0"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://r.cnpmjs.org/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.0.1:
  version "3.0.2"
  resolved "https://r.cnpmjs.org/escalade/download/escalade-3.0.2.tgz#6a580d70edb87880f22b4c91d0d56078df6962c4"
  integrity sha1-algNcO24eIDyK0yR0NVgeN9pYsQ=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://r.cnpmjs.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-loader@^2.2.1:
  version "2.2.1"
  resolved "https://r.cnpmjs.org/eslint-loader/download/eslint-loader-2.2.1.tgz#28b9c12da54057af0845e2a6112701a2f6bf8337"
  integrity sha1-KLnBLaVAV68IReKmEScBova/gzc=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-plugin-vue@^6.2.2:
  version "6.2.2"
  resolved "https://r.cnpmjs.org/eslint-plugin-vue/download/eslint-plugin-vue-6.2.2.tgz#27fecd9a3a24789b0f111ecdd540a9e56198e0fe"
  integrity sha1-J/7NmjokeJsPER7N1UCp5WGY4P4=
  dependencies:
    natural-compare "^1.4.0"
    semver "^5.6.0"
    vue-eslint-parser "^7.0.0"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/eslint-scope/download/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/eslint-scope/download/eslint-scope-5.1.0.tgz#d0f971dfe59c69e0cada684b23d49dbf82600ce5"
  integrity sha1-0Plx3+WcaeDK2mhLI9Sdv4JgDOU=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "https://r.cnpmjs.org/eslint-utils/download/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint@^6.7.2:
  version "6.8.0"
  resolved "https://r.cnpmjs.org/eslint/download/eslint-6.8.0.tgz#62262d6729739f9275723824302fb227c8c93ffb"
  integrity sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2, espree@^6.2.1:
  version "6.2.1"
  resolved "https://r.cnpmjs.org/espree/download/espree-6.2.1.tgz#77fc72e1fd744a2052c20f38a5b575832e82734a"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/esquery/download/esquery-1.3.1.tgz#b78b5828aa8e214e29fb74c4d5b752e1c033da57"
  integrity sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "https://r.cnpmjs.org/esrecurse/download/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  integrity sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.1.0, estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://r.cnpmjs.org/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/estraverse/download/estraverse-5.1.0.tgz#374309d39fd935ae500e7b92e8a6b4c720e59642"
  integrity sha1-N0MJ05/ZNa5QDnuS6Ka0xyDllkI=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://r.cnpmjs.org/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "https://r.cnpmjs.org/event-pubsub/download/event-pubsub-4.3.0.tgz#f68d816bc29f1ec02c539dc58c8dd40ce72cb36e"
  integrity sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=

eventemitter3@^4.0.0:
  version "4.0.4"
  resolved "https://r.cnpmjs.org/eventemitter3/download/eventemitter3-4.0.4.tgz#b5463ace635a083d018bdc7c917b4c5f10a85384"
  integrity sha1-tUY6zmNaCD0Bi9x8kXtMXxCoU4Q=

events@^3.0.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/events/download/events-3.1.0.tgz#84279af1b34cb75aa88bf5ff291f6d0bd9b31a59"
  integrity sha1-hCea8bNMt1qoi/X/KR9tC9mzGlk=

eventsource@^1.0.7:
  version "1.0.7"
  resolved "https://r.cnpmjs.org/eventsource/download/eventsource-1.0.7.tgz#8fbc72c93fcd34088090bc0a4e64f4b5cee6d8d0"
  integrity sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=
  dependencies:
    original "^1.0.0"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.8.0:
  version "0.8.0"
  resolved "https://r.cnpmjs.org/execa/download/execa-0.8.0.tgz#d8d76bbc1b55217ed190fd6dd49d3c774ecfc8da"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^3.3.0:
  version "3.4.0"
  resolved "https://r.cnpmjs.org/execa/download/execa-3.4.0.tgz#c08ed4550ef65d858fac269ffc8572446f37eb89"
  integrity sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://r.cnpmjs.org/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express@^4.16.3, express@^4.17.1:
  version "4.17.1"
  resolved "https://r.cnpmjs.org/express/download/express-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://r.cnpmjs.org/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://r.cnpmjs.org/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://r.cnpmjs.org/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/extsprintf/download/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://r.cnpmjs.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://r.cnpmjs.org/fast-glob/download/fast-glob-2.2.7.tgz#6953857c3afa475fff92ee6015d52da70a4cd39d"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://r.cnpmjs.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "https://r.cnpmjs.org/faye-websocket/download/faye-websocket-0.10.0.tgz#4e492f8d04dfb6f89003507f6edbf2d501e7c6f4"
  integrity sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.1:
  version "0.11.3"
  resolved "https://r.cnpmjs.org/faye-websocket/download/faye-websocket-0.11.3.tgz#5c0e9a8968e8912c286639fde977a8b209f2508e"
  integrity sha1-XA6aiWjokSwoZjn96XeosgnyUI4=
  dependencies:
    websocket-driver ">=0.5.1"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/fd-slicer/download/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  integrity sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=
  dependencies:
    pend "~1.2.0"

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "https://r.cnpmjs.org/figgy-pudding/download/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^3.0.0:
  version "3.2.0"
  resolved "https://r.cnpmjs.org/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://r.cnpmjs.org/file-entry-cache/download/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-loader@^4.2.0:
  version "4.3.0"
  resolved "https://r.cnpmjs.org/file-loader/download/file-loader-4.3.0.tgz#780f040f729b3d18019f20605f723e844b8a58af"
  integrity sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8=
  dependencies:
    loader-utils "^1.2.3"
    schema-utils "^2.5.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filesize@^3.6.1:
  version "3.6.1"
  resolved "https://r.cnpmjs.org/filesize/download/filesize-3.6.1.tgz#090bb3ee01b6f801a8a8be99d31710b3422bb317"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://r.cnpmjs.org/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/finalhandler/download/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/find-cache-dir/download/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0, find-cache-dir@^3.3.1:
  version "3.3.1"
  resolved "https://r.cnpmjs.org/find-cache-dir/download/find-cache-dir-3.3.1.tgz#89b33fad4a4670daa94f855f7fbe31d6d84fe880"
  integrity sha1-ibM/rUpGcNqpT4Vff74x1thP6IA=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/flat-cache/download/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/flatted/download/flatted-2.0.2.tgz#4575b21e2bcee7434aa9be662f4b7b5f9c2b5138"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

flushwritable@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/flushwritable/download/flushwritable-1.0.0.tgz#3e328d8fde412ad47e738e3be750b4d290043498"
  integrity sha1-PjKNj95BKtR+c44751C00pAENJg=

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://r.cnpmjs.org/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.0.0:
  version "1.12.1"
  resolved "https://r.cnpmjs.org/follow-redirects/download/follow-redirects-1.12.1.tgz#de54a6205311b93d60398ebc01cf7015682312b6"
  integrity sha1-3lSmIFMRuT1gOY68Ac9wFWgjErY=

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://r.cnpmjs.org/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://r.cnpmjs.org/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/forwarded/download/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://r.cnpmjs.org/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://r.cnpmjs.org/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/fs-constants/download/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"
  integrity sha1-a+Dem+mYzhavivwkSXue6bfM2a0=

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://r.cnpmjs.org/fs-extra/download/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/fs-minipass/download/fs-minipass-2.1.0.tgz#7f5036fdbf12c63c169190cbe4199c852271f9fb"
  integrity sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=
  dependencies:
    minipass "^3.0.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://r.cnpmjs.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://r.cnpmjs.org/fsevents/download/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.1.2:
  version "2.1.3"
  resolved "https://r.cnpmjs.org/fsevents/download/fsevents-2.1.3.tgz#fb738703ae8d2f9fe900c33836ddebee8b97f23e"
  integrity sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4=

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "https://r.cnpmjs.org/fstream/download/fstream-1.0.12.tgz#4e8ba8ee2d48be4f7d0de505455548eae5932045"
  integrity sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://r.cnpmjs.org/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/gaze/download/gaze-1.1.3.tgz#c441733e13b927ac8c0ff0b4c3b033f28812924a"
  integrity sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=
  dependencies:
    globule "^1.0.0"

gensync@^1.0.0-beta.1:
  version "1.0.0-beta.1"
  resolved "https://r.cnpmjs.org/gensync/download/gensync-1.0.0-beta.1.tgz#58f4361ff987e5ff6e1e7a210827aa371eaac269"
  integrity sha1-WPQ2H/mH5f9uHnohCCeqNx6qwmk=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://r.cnpmjs.org/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-ready@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/get-ready/download/get-ready-1.0.0.tgz#f91817f1e9adecfea13a562adfc8de883ab34782"
  integrity sha1-+RgX8emt7P6hOlYq38jeiDqzR4I=

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/get-stdin/download/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/get-stream/download/get-stream-5.1.0.tgz#01203cdc92597f9b909067c3e656cc1f4d3c4dc9"
  integrity sha1-ASA83JJZf5uQkGfD5lbMH008Tck=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://r.cnpmjs.org/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://r.cnpmjs.org/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0, glob-parent@~5.1.0:
  version "5.1.1"
  resolved "https://r.cnpmjs.org/glob-parent/download/glob-parent-5.1.1.tgz#b6c1ef417c4e5663ea498f1c45afac6916bbc229"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://r.cnpmjs.org/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^7.0.0, glob@^7.0.3, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@~7.1.1:
  version "7.1.6"
  resolved "https://r.cnpmjs.org/glob/download/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~7.0.3:
  version "7.0.6"
  resolved "https://r.cnpmjs.org/glob/download/glob-7.0.6.tgz#211bafaf49e525b8cd93260d14ab136152b3f57a"
  integrity sha1-IRuvr0nlJbjNkyYNFKsTYVKz9Xo=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://r.cnpmjs.org/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "https://r.cnpmjs.org/globals/download/globals-12.4.0.tgz#a18813576a41b00a24a97e7f815918c2e19925f8"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globby@^6.1.0:
  version "6.1.0"
  resolved "https://r.cnpmjs.org/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://r.cnpmjs.org/globby/download/globby-7.1.1.tgz#fb2ccff9401f8600945dfada97440cca972b8680"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://r.cnpmjs.org/globby/download/globby-9.2.0.tgz#fd029a706c703d29bdd170f4b6db3a3f7a7cb63d"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

globule@^1.0.0:
  version "1.3.2"
  resolved "https://r.cnpmjs.org/globule/download/globule-1.3.2.tgz#d8bdd9e9e4eef8f96e245999a5dee7eb5d8529c4"
  integrity sha1-2L3Z6eTu+PluJFmZpd7n612FKcQ=
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.10"
    minimatch "~3.0.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.2:
  version "4.2.4"
  resolved "https://r.cnpmjs.org/graceful-fs/download/graceful-fs-4.2.4.tgz#2256bde14d3632958c465ebc96dc467ca07a29fb"
  integrity sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "https://r.cnpmjs.org/gzip-size/download/gzip-size-5.1.1.tgz#cb9bee692f87c0612b232840a873904e4c135274"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/handle-thing/download/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.3"
  resolved "https://r.cnpmjs.org/har-validator/download/har-validator-5.1.3.tgz#1ef89ebd3e4996557675eed9893110dc350fa080"
  integrity sha1-HvievT5JllV2de7ZiTEQ3DUPoIA=
  dependencies:
    ajv "^6.5.5"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.0, has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/has-symbols/download/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8"
  integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://r.cnpmjs.org/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://r.cnpmjs.org/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/hash-base/download/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/hash-sum/download/hash-sum-1.0.2.tgz#33b40777754c6432573c120cc3808bbd10d47f04"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/hash-sum/download/hash-sum-2.0.0.tgz#81d01bb5de8ea4a214ad5d6ead1b523460b0b45a"
  integrity sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://r.cnpmjs.org/hash.js/download/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.2.x, he@^1.1.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/hex-color-regex/download/hex-color-regex-1.1.0.tgz#4c06fccb4602fe2602b3c93df82d7e7dbf1a8a8e"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

highlight.js@^9.6.0:
  version "9.18.1"
  resolved "https://r.cnpmjs.org/highlight.js/download/highlight.js-9.18.1.tgz#ed21aa001fe6252bb10a3d76d47573c6539fe13c"
  integrity sha1-7SGqAB/mJSuxCj121HVzxlOf4Tw=

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://r.cnpmjs.org/hoopy/download/hoopy-0.1.4.tgz#609207d661100033a9a9402ad3dea677381c1b1d"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "https://r.cnpmjs.org/hosted-git-info/download/hosted-git-info-2.8.8.tgz#7539bd4bc1e0e0a895815a2e0262420b12858488"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://r.cnpmjs.org/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/hsl-regex/download/hsl-regex-1.0.0.tgz#d49330c789ed819e276a4c0d272dffa30b18fe6e"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/hsla-regex/download/hsla-regex-1.0.0.tgz#c1ce7a3168c8c6614033a4b5f7877f3b225f9c38"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/html-comment-regex/download/html-comment-regex-1.1.2.tgz#97d4688aeb5c81886a364faa0cad1dda14d433a7"
  integrity sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=

html-entities@^1.3.1:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/html-entities/download/html-entities-1.3.1.tgz#fb9a1a4b5b14c5daba82d3e34c6ae4fe701a0e44"
  integrity sha1-+5oaS1sUxdq6gtPjTGrk/nAaDkQ=

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://r.cnpmjs.org/html-minifier/download/html-minifier-3.5.21.tgz#d0040e054730e354db008463593194015212d20c"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/html-tags/download/html-tags-2.0.0.tgz#10b30a386085f43cede353cc8fa7cb0deeea668b"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "https://r.cnpmjs.org/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz#b01abbd723acaaa7b37b6af4492ebda03d9dd37b"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

htmlparser2@^3.3.0:
  version "3.10.1"
  resolved "https://r.cnpmjs.org/htmlparser2/download/htmlparser2-3.10.1.tgz#bd679dc3f59897b6a34bb10749c855bb53a9392f"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://r.cnpmjs.org/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.7.2:
  version "1.7.2"
  resolved "https://r.cnpmjs.org/http-errors/download/http-errors-1.7.2.tgz#4f5029cf13239f31036e5b2e55292bcfbcc85c8f"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://r.cnpmjs.org/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://r.cnpmjs.org/http-errors/download/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-parser-js@>=0.5.1:
  version "0.5.2"
  resolved "https://r.cnpmjs.org/http-parser-js/download/http-parser-js-0.5.2.tgz#da2e31d237b393aae72ace43882dd7e270a8ff77"
  integrity sha1-2i4x0jezk6rnKs5DiC3X4nCo/3c=

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "https://r.cnpmjs.org/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz#183c7dc4aa1479150306498c210cdaf96080a43a"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0:
  version "1.18.1"
  resolved "https://r.cnpmjs.org/http-proxy/download/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://r.cnpmjs.org/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.5.0:
  version "0.5.2"
  resolved "https://r.cnpmjs.org/iconv-lite/download/iconv-lite-0.5.2.tgz#af6d628dccfb463b7364d97f715e4b74b8c8c2b8"
  integrity sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^4.0.0, icss-utils@^4.1.1:
  version "4.1.1"
  resolved "https://r.cnpmjs.org/icss-utils/download/icss-utils-4.1.1.tgz#21170b53789ee27447c2f47dd683081403f9a467"
  integrity sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=
  dependencies:
    postcss "^7.0.14"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://r.cnpmjs.org/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://r.cnpmjs.org/ieee754/download/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://r.cnpmjs.org/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://r.cnpmjs.org/ignore/download/ignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://r.cnpmjs.org/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/import-cwd/download/import-cwd-2.1.0.tgz#aa6cf36e722761285cb371ec6519f53e2435b0a9"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.2.1"
  resolved "https://r.cnpmjs.org/import-fresh/download/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
  integrity sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/import-from/download/import-from-2.1.0.tgz#335db7f2a7affd53aaa471d4b8021dee36b7f3b1"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/import-local/download/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://r.cnpmjs.org/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

in-publish@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/in-publish/download/in-publish-2.0.1.tgz#948b1a535c8030561cea522f73f78f4be357e00c"
  integrity sha1-lIsaU1yAMFYc6lIvc/ePS+NX4Aw=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/indent-string/download/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/indexes-of/download/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3, infer-owner@^1.0.4:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/infer-owner/download/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://r.cnpmjs.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://r.cnpmjs.org/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/inherits/download/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^7.0.0, inquirer@^7.1.0:
  version "7.3.2"
  resolved "https://r.cnpmjs.org/inquirer/download/inquirer-7.3.2.tgz#25245d2e32dc9f33dbe26eeaada231daa66e9c7c"
  integrity sha1-JSRdLjLcnzPb4m7qraIx2qZunHw=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.16"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

inquirer@^7.3.3:
  version "7.3.3"
  resolved "https://r.cnpmjs.org/inquirer/download/inquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://r.cnpmjs.org/internal-ip/download/internal-ip-4.3.0.tgz#845452baad9d2ca3b69c635a137acb9a0dad0907"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

invariant@^2.2.2, invariant@^2.2.4:
  version "2.2.4"
  resolved "https://r.cnpmjs.org/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/ip-regex/download/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "https://r.cnpmjs.org/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.1, ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "https://r.cnpmjs.org/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz#50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "https://r.cnpmjs.org/is-absolute-url/download/is-absolute-url-3.0.3.tgz#96c6a22b6a23929b11ea0afb1836c36ad4a5d698"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://r.cnpmjs.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/is-arguments/download/is-arguments-1.0.4.tgz#3faf966c7cba0ff437fb31f6250082fcf0448cf3"
  integrity sha1-P6+WbHy6D/Q3+zH2JQCC/PBEjPM=

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://r.cnpmjs.org/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://r.cnpmjs.org/is-arrayish/download/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://r.cnpmjs.org/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/is-callable/download/is-callable-1.2.0.tgz#83336560b54a38e35e3a2df7afd0454d691468bb"
  integrity sha1-gzNlYLVKOONeOi33r9BFTWkUaLs=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://r.cnpmjs.org/is-ci/download/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-color-stop/download/is-color-stop-1.1.0.tgz#cfff471aee4dd5c9e158598fbe12967b5cdad345"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.1.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/is-core-module/download/is-core-module-2.2.0.tgz#97037ef3d52224d85163f5597b2b63d9afed981a"
  integrity sha1-lwN+89UiJNhRY/VZeytj2a/tmBo=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://r.cnpmjs.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/is-date-object/download/is-date-object-1.0.2.tgz#bda736f2cd8fd06d32844e7743bfa7494c3bfd7e"
  integrity sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://r.cnpmjs.org/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://r.cnpmjs.org/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/is-docker/download/is-docker-2.0.0.tgz#2cb0df0e75e2d064fe1864c37cdeacb7b2dcf25b"
  integrity sha1-LLDfDnXi0GT+GGTDfN6st7Lc8ls=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-finite/download/is-finite-1.1.0.tgz#904135c77fb42c0641d6aa1bcdbc4daa8da082f3"
  integrity sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/is-glob/download/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://r.cnpmjs.org/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/is-obj/download/is-obj-2.0.0.tgz#473fb05d973705e3fd9620545018ca8e22ef4982"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/is-path-cwd/download/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz#bfe2dca26c69f397265a4009963602935a053acb"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/is-path-inside/download/is-path-inside-2.1.0.tgz#7c9810587d659a40d27bcdb4d5616eab059494b2"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://r.cnpmjs.org/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-regex@^1.0.4, is-regex@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-regex/download/is-regex-1.1.0.tgz#ece38e389e490df0dc21caea2bd596f987f767ff"
  integrity sha1-7OOOOJ5JDfDcIcrqK9WW+Yf3Z/8=
  dependencies:
    has-symbols "^1.0.1"

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-resolvable/download/is-resolvable-1.1.0.tgz#fb18f87ce1feb925169c9a407c19318a3206ed88"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/is-stream/download/is-stream-2.0.0.tgz#bde9c32680d6fae04129d6ac9d921ce7815f78e3"
  integrity sha1-venDJoDW+uBBKdasnZIc54FfeOM=

is-svg@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/is-svg/download/is-svg-3.0.0.tgz#9321dbd29c212e5ca99c4fa9794c714bcafa2f75"
  integrity sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/is-symbol/download/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937"
  integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
  dependencies:
    has-symbols "^1.0.1"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://r.cnpmjs.org/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://r.cnpmjs.org/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

javascript-stringify@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/javascript-stringify/download/javascript-stringify-2.0.1.tgz#6ef358035310e35d667c675ed63d3eb7c1aa19e5"
  integrity sha1-bvNYA1MQ411mfGde1j0+t8GqGeU=

jest-worker@^25.4.0:
  version "25.5.0"
  resolved "https://r.cnpmjs.org/jest-worker/download/jest-worker-25.5.0.tgz#2611d071b79cea0f43ee57a3d118593ac1547db1"
  integrity sha1-JhHQcbec6g9D7lej0RhZOsFUfbE=
  dependencies:
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

js-base64@^2.1.8:
  version "2.6.3"
  resolved "https://r.cnpmjs.org/js-base64/download/js-base64-2.6.3.tgz#7afdb9b57aa7717e15d370b66e8f36a9cb835dc3"
  integrity sha1-ev25tXqncX4V03C2bo82qcuDXcM=

js-message@1.0.5:
  version "1.0.5"
  resolved "https://r.cnpmjs.org/js-message/download/js-message-1.0.5.tgz#2300d24b1af08e89dd095bc1a4c9c9cfcb892d15"
  integrity sha1-IwDSSxrwjondCVvBpMnJz8uJLRU=

js-queue@2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/js-queue/download/js-queue-2.0.0.tgz#362213cf860f468f0125fc6c96abc1742531f948"
  integrity sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug=
  dependencies:
    easy-stack "^1.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.0"
  resolved "https://r.cnpmjs.org/js-yaml/download/js-yaml-3.14.0.tgz#a7a34170f26a21bb162424d8adacb4113a69e482"
  integrity sha1-p6NBcPJqIbsWJCTYray0ETpp5II=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://r.cnpmjs.org/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://r.cnpmjs.org/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://r.cnpmjs.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://r.cnpmjs.org/json-schema/download/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://r.cnpmjs.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json3@^3.3.2:
  version "3.3.3"
  resolved "https://r.cnpmjs.org/json3/download/json3-3.3.3.tgz#7fc10e375fc5ae42c4705a5cc0aa6f62be305b81"
  integrity sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=

json5@^0.5.0:
  version "0.5.1"
  resolved "https://r.cnpmjs.org/json5/download/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/json5/download/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.1.3"
  resolved "https://r.cnpmjs.org/json5/download/json5-2.1.3.tgz#c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43"
  integrity sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=
  dependencies:
    minimist "^1.2.5"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://r.cnpmjs.org/jsprim/download/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

killable@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/killable/download/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://r.cnpmjs.org/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://r.cnpmjs.org/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klona@^1.1.1:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/klona/download/klona-1.1.2.tgz#a79e292518a5a5412ec8d097964bff1571a64db0"
  integrity sha1-p54pJRilpUEuyNCXlkv/FXGmTbA=

launch-editor-middleware@^2.2.1:
  version "2.2.1"
  resolved "https://r.cnpmjs.org/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz#e14b07e6c7154b0a4b86a0fd345784e45804c157"
  integrity sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc=
  dependencies:
    launch-editor "^2.2.1"

launch-editor@^2.2.1:
  version "2.2.1"
  resolved "https://r.cnpmjs.org/launch-editor/download/launch-editor-2.2.1.tgz#871b5a3ee39d6680fcc26d37930b6eeda89db0ca"
  integrity sha1-hxtaPuOdZoD8wm03kwtu7aidsMo=
  dependencies:
    chalk "^2.3.0"
    shell-quote "^1.6.1"

leven@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/leven/download/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levenary@^1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/levenary/download/levenary-1.1.1.tgz#842a9ee98d2075aa7faeedbe32679e9205f46f77"
  integrity sha1-hCqe6Y0gdap/ru2+MmeekgX0b3c=
  dependencies:
    leven "^3.1.0"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://r.cnpmjs.org/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "https://r.cnpmjs.org/lines-and-columns/download/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/load-json-file/download/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz#f08657646d607078be2f0a032f8bd69dd6f277d9"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.1, loader-runner@^2.4.0:
  version "2.4.0"
  resolved "https://r.cnpmjs.org/loader-runner/download/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://r.cnpmjs.org/loader-utils/download/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3, loader-utils@^1.4.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/loader-utils/download/loader-utils-1.4.0.tgz#c579b5e34cb34b1a74edc6c1fb36bfa371d5a613"
  integrity sha1-xXm140yzSxp07cbB+za/o3HVphM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/loader-utils/download/loader-utils-2.0.0.tgz#e4cace5b816d425a166b5f097e10cd12b36064b0"
  integrity sha1-5MrOW4FtQloWa18JfhDNErNgZLA=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://r.cnpmjs.org/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "https://r.cnpmjs.org/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz#512e9bd721d272d94e3d3a63653fa17516741ca6"
  integrity sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://r.cnpmjs.org/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz#8489b1cb0d29ff88195cceca448ff6d6cc295c36"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "https://r.cnpmjs.org/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz#1bafa5005de9dd6f4f26668c30ca37230cc9689c"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://r.cnpmjs.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.transform@^4.6.0:
  version "4.6.0"
  resolved "https://r.cnpmjs.org/lodash.transform/download/lodash.transform-4.6.0.tgz#12306422f63324aed8483d3f38332b5f670547a0"
  integrity sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://r.cnpmjs.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.0.0, lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.16, lodash@^4.17.19, lodash@^4.17.3, lodash@~4.17.10:
  version "4.17.19"
  resolved "https://r.cnpmjs.org/lodash/download/lodash-4.17.19.tgz#e48ddedbe30b3321783c5b4301fbd353bc1e4a4b"
  integrity sha1-5I3e2+MLMyF4PFtDAfvTU7weSks=

lodash@~4.11.1:
  version "4.11.2"
  resolved "https://r.cnpmjs.org/lodash/download/lodash-4.11.2.tgz#d6b4338b110a58e21dae5cebcfdbbfd2bc4cdb3b"
  integrity sha1-1rQzixEKWOIdrlzrz9u/0rxM2zs=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/log-symbols/download/log-symbols-4.0.0.tgz#69b3cc46d20f448eccdb75ea1fa733d9e821c920"
  integrity sha1-abPMRtIPRI7M23XqH6cz2eghySA=
  dependencies:
    chalk "^4.0.0"

loglevel@^1.6.8:
  version "1.6.8"
  resolved "https://r.cnpmjs.org/loglevel/download/loglevel-1.6.8.tgz#8a25fb75d092230ecd4457270d80b54e28011171"
  integrity sha1-iiX7ddCSIw7NRFcnDYC1TigBEXE=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://r.cnpmjs.org/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://r.cnpmjs.org/lower-case/download/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://r.cnpmjs.org/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://r.cnpmjs.org/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

make-dir@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://r.cnpmjs.org/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://r.cnpmjs.org/md5.js/download/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://r.cnpmjs.org/mdn-data/download/mdn-data-2.0.4.tgz#699b3c38ac6f1d728091a64650b65d388502fd5b"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

mdn-data@2.0.6:
  version "2.0.6"
  resolved "https://r.cnpmjs.org/mdn-data/download/mdn-data-2.0.6.tgz#852dc60fcaa5daa2e8cf6c9189c440ed3e042978"
  integrity sha1-hS3GD8ql2qLoz2yRicRA7T4EKXg=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://r.cnpmjs.org/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-fs@^0.4.1:
  version "0.4.1"
  resolved "https://r.cnpmjs.org/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://r.cnpmjs.org/memory-fs/download/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.7.0:
  version "3.7.0"
  resolved "https://r.cnpmjs.org/meow/download/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/merge-source-map/download/merge-source-map-1.1.0.tgz#2fdde7e6020939f70906a68f2d7ae685e4c8c646"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3:
  version "1.4.1"
  resolved "https://r.cnpmjs.org/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://r.cnpmjs.org/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.44.0, "mime-db@>= 1.43.0 < 2":
  version "1.44.0"
  resolved "https://r.cnpmjs.org/mime-db/download/mime-db-1.44.0.tgz#fa11c5eb0aca1334b4233cb4d52f10c5a6272f92"
  integrity sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.27"
  resolved "https://r.cnpmjs.org/mime-types/download/mime-types-2.1.27.tgz#47949f98e279ea53119f5722e0f34e529bec009f"
  integrity sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=
  dependencies:
    mime-db "1.44.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://r.cnpmjs.org/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.4:
  version "2.4.6"
  resolved "https://r.cnpmjs.org/mime/download/mime-2.4.6.tgz#e5b407c90db442f2beb5b162373d07b69affa4d1"
  integrity sha1-5bQHyQ20QvK+tbFiNz0Htpr/pNE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-css-extract-plugin@^0.9.0:
  version "0.9.0"
  resolved "https://r.cnpmjs.org/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz#47f2cf07aa165ab35733b1fc97d4c46c0564339e"
  integrity sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54=
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "1.9.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.2, minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://r.cnpmjs.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://r.cnpmjs.org/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minipass-collect@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/minipass-collect/download/minipass-collect-1.0.2.tgz#22b813bf745dc6edba2576b940022ad6edc8c617"
  integrity sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=
  dependencies:
    minipass "^3.0.0"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "https://r.cnpmjs.org/minipass-flush/download/minipass-flush-1.0.5.tgz#82e7135d7e89a50ffe64610a787953c4c4cbb373"
  integrity sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=
  dependencies:
    minipass "^3.0.0"

minipass-pipeline@^1.2.2:
  version "1.2.3"
  resolved "https://r.cnpmjs.org/minipass-pipeline/download/minipass-pipeline-1.2.3.tgz#55f7839307d74859d6e8ada9c3ebe72cec216a34"
  integrity sha1-VfeDkwfXSFnW6K2pw+vnLOwhajQ=
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0, minipass@^3.1.1:
  version "3.1.3"
  resolved "https://r.cnpmjs.org/minipass/download/minipass-3.1.3.tgz#7d42ff1f39635482e15f9cdb53184deebd5815fd"
  integrity sha1-fUL/HzljVILhX5zbUxhN7r1YFf0=
  dependencies:
    yallist "^4.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/mississippi/download/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://r.cnpmjs.org/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

"mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://r.cnpmjs.org/mkdirp/download/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

moment@^2.27.0:
  version "2.27.0"
  resolved "https://r.cnpmjs.org/moment/download/moment-2.27.0.tgz#8bff4e3e26a236220dfe3e36de756b6ebaa0105d"
  integrity sha1-i/9OPiaiNiIN/j423nVrbrqgEF0=

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@^2.1.1:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://r.cnpmjs.org/multicast-dns/download/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://r.cnpmjs.org/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.4.0:
  version "2.7.0"
  resolved "https://r.cnpmjs.org/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1, nan@^2.13.2:
  version "2.14.1"
  resolved "https://r.cnpmjs.org/nan/download/nan-2.14.1.tgz#d7be34dfa3105b91494c3147089315eff8874b01"
  integrity sha1-174036MQW5FJTDFHCJMV7/iHSwE=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://r.cnpmjs.org/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://r.cnpmjs.org/negotiator/download/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0, neo-async@^2.6.0, neo-async@^2.6.1:
  version "2.6.2"
  resolved "https://r.cnpmjs.org/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://r.cnpmjs.org/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://r.cnpmjs.org/no-case/download/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-forge@0.9.0:
  version "0.9.0"
  resolved "https://r.cnpmjs.org/node-forge/download/node-forge-0.9.0.tgz#d624050edbb44874adca12bb9a52ec63cb782579"
  integrity sha1-1iQFDtu0SHStyhK7mlLsY8t4JXk=

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "https://r.cnpmjs.org/node-gyp/download/node-gyp-3.8.0.tgz#540304261c330e80d0d5edce253a68cb3964218c"
  integrity sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw=
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-ipc@^9.1.1:
  version "9.1.1"
  resolved "https://r.cnpmjs.org/node-ipc/download/node-ipc-9.1.1.tgz#4e245ed6938e65100e595ebc5dc34b16e8dd5d69"
  integrity sha1-TiRe1pOOZRAOWV68XcNLFujdXWk=
  dependencies:
    event-pubsub "4.3.0"
    js-message "1.0.5"
    js-queue "2.0.0"

node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "https://r.cnpmjs.org/node-libs-browser/download/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^1.1.58:
  version "1.1.59"
  resolved "https://r.cnpmjs.org/node-releases/download/node-releases-1.1.59.tgz#4d648330641cec704bff10f8e4fe28e453ab8e8e"
  integrity sha1-TWSDMGQc7HBL/xD45P4o5FOrjo4=

node-sass@^4.14.1:
  version "4.14.1"
  resolved "https://r.cnpmjs.org/node-sass/download/node-sass-4.14.1.tgz#99c87ec2efb7047ed638fb4c9db7f3a42e2217b5"
  integrity sha1-mch+wu+3BH7WOPtMnbfzpC4iF7U=
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash "^4.17.15"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.13.2"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "^2.88.0"
    sass-graph "2.2.5"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

node-ssh@^11.1.1:
  version "11.1.1"
  resolved "https://r.cnpmjs.org/node-ssh/download/node-ssh-11.1.1.tgz#b7ed0a82963d34c6e8c007723f83f626ef5b19e0"
  integrity sha1-t+0KgpY9NMbowAdyP4P2Ju9bGeA=
  dependencies:
    make-dir "^3.1.0"
    sb-promise-queue "^2.1.0"
    sb-scandir "^3.1.0"
    shell-escape "^0.2.0"
    ssh2 "^0.8.9"

"nopt@2 || 3":
  version "3.0.6"
  resolved "https://r.cnpmjs.org/nopt/download/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  integrity sha1-xkZdvwirzU2zWTF/eaxopkayj/k=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://r.cnpmjs.org/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/normalize-path/download/normalize-path-1.0.0.tgz#32d0e472f91ff345701c15a8311018d3b0a90379"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@1.9.1:
  version "1.9.1"
  resolved "https://r.cnpmjs.org/normalize-url/download/normalize-url-1.9.1.tgz#2cc0d66b31ea23036458436e3620d85954c66c3c"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "https://r.cnpmjs.org/normalize-url/download/normalize-url-3.3.0.tgz#b2e1c4dc4f7c6d57743df733a4f5978d18650559"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

normalize-wheel@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/normalize-wheel/download/normalize-wheel-1.0.1.tgz#aec886affdb045070d856447df62ecf86146ec45"
  integrity sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.0:
  version "4.1.2"
  resolved "https://r.cnpmjs.org/npmlog/download/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nth-check@^1.0.2, nth-check@~1.0.1:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/nth-check/download/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://r.cnpmjs.org/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://r.cnpmjs.org/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://r.cnpmjs.org/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://r.cnpmjs.org/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/object-hash/download/object-hash-1.3.1.tgz#fde452098a951cb145f039bb7d455449ddc126df"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.7.0:
  version "1.8.0"
  resolved "https://r.cnpmjs.org/object-inspect/download/object-inspect-1.8.0.tgz#df807e5ecf53a609cc6bfe93eac3cc7be5b3a9d0"
  integrity sha1-34B+Xs9TpgnMa/6T6sPMe+WzqdA=

object-is@^1.0.1:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/object-is/download/object-is-1.1.2.tgz#c5d2e87ff9e119f78b7a088441519e2eec1573b6"
  integrity sha1-xdLof/nhGfeLegiEQVGeLuwVc7Y=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/object.assign/download/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
  integrity sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.getownpropertydescriptors@^2.0.3, object.getownpropertydescriptors@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.0.tgz#369bf1f9592d8ab89d712dced5cb81c7c5352649"
  integrity sha1-Npvx+VktiridcS3O1cuBx8U1Jkk=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/object.values/download/object.values-1.1.1.tgz#68a99ecde356b7e9295a3c5e0ce31dc8c953de5e"
  integrity sha1-aKmezeNWt+kpWjxeDOMdyMlT3l4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"
    function-bind "^1.1.1"
    has "^1.0.3"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/onetime/download/onetime-5.1.0.tgz#fff0f3c91617fe62bb50189636e99ac8a6df7be5"
  integrity sha1-//DzyRYX/mK7UBiWNumayKbfe+U=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.3.0:
  version "6.4.0"
  resolved "https://r.cnpmjs.org/open/download/open-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opener@^1.5.1:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/opener/download/opener-1.5.1.tgz#6d2f0e77f1a0af0032aca716c2c1fbb8e7e8abed"
  integrity sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0=

opn@^5.5.0:
  version "5.5.0"
  resolved "https://r.cnpmjs.org/opn/download/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.3:
  version "0.8.3"
  resolved "https://r.cnpmjs.org/optionator/download/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^3.4.0:
  version "3.4.0"
  resolved "https://r.cnpmjs.org/ora/download/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

ora@^5.3.0:
  version "5.3.0"
  resolved "https://r.cnpmjs.org/ora/download/ora-5.3.0.tgz#fb832899d3a1372fe71c8b2c534bbfe74961bb6f"
  integrity sha1-+4MomdOhNy/nHIssU0u/50lhu28=
  dependencies:
    bl "^4.0.3"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    log-symbols "^4.0.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

original@^1.0.0:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/original/download/original-1.0.2.tgz#e442a61cffe1c5fd20a65f3261c26663b303f25f"
  integrity sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://r.cnpmjs.org/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@0:
  version "0.1.5"
  resolved "https://r.cnpmjs.org/osenv/download/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/p-finally/download/p-finally-2.0.1.tgz#bd6fcaa9c559a096b680806f4d657b3f0f240561"
  integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=

p-limit@^2.0.0, p-limit@^2.2.0, p-limit@^2.2.1, p-limit@^2.3.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-map@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/p-map/download/p-map-3.0.0.tgz#d704d9af8a2ba684e2600d9a215983d4141a979d"
  integrity sha1-1wTZr4orpoTiYA2aIVmD1BQal50=
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/p-retry/download/p-retry-3.0.1.tgz#316b4c8893e2c8dc1cfa891f406c4b422bebf328"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.11"
  resolved "https://r.cnpmjs.org/pako/download/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/parallel-transform/download/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/param-case/download/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.5"
  resolved "https://r.cnpmjs.org/parse-asn1/download/parse-asn1-5.1.5.tgz#003271343da58dc94cace494faef3d2147ecea0e"
  integrity sha1-ADJxND2ljclMrOSU+u89IUfs6g4=
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.0.0"
  resolved "https://r.cnpmjs.org/parse-json/download/parse-json-5.0.0.tgz#73e5114c986d143efa3712d4ea24db9a4266f60f"
  integrity sha1-c+URTJhtFD76NxLU6iTbmkJm9g8=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^5.1.1:
  version "5.1.1"
  resolved "https://r.cnpmjs.org/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-5.1.1.tgz#e8c743d4e92194d5293ecde2b08be31e67461cbc"
  integrity sha1-6MdD1OkhlNUpPs3isIvjHmdGHLw=
  dependencies:
    parse5 "^5.1.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://r.cnpmjs.org/parse5/download/parse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://r.cnpmjs.org/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://r.cnpmjs.org/path-browserify/download/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://r.cnpmjs.org/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://r.cnpmjs.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/path-type/download/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

pbkdf2@^3.0.3:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/pbkdf2/download/pbkdf2-3.1.1.tgz#cb8724b0fada984596856d1a6ebafd3584654b94"
  integrity sha1-y4cksPramEWWhW0abrr9NYRlS5Q=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pend@~1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"
  integrity sha1-elfrVQpng/kRUzH89GY9XI4AelA=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.2.2"
  resolved "https://r.cnpmjs.org/picomatch/download/picomatch-2.2.2.tgz#21f333e9b6b8eaff02468f5146ea406d345f4dad"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://r.cnpmjs.org/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/pkg-dir/download/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://r.cnpmjs.org/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pnp-webpack-plugin@^1.6.4:
  version "1.6.4"
  resolved "https://r.cnpmjs.org/pnp-webpack-plugin/download/pnp-webpack-plugin-1.6.4.tgz#c9711ac4dc48a685dabafc86f8b6dd9f8df84149"
  integrity sha1-yXEaxNxIpoXauvyG+Lbdn434QUk=
  dependencies:
    ts-pnp "^1.1.6"

portfinder@^1.0.26:
  version "1.0.26"
  resolved "https://r.cnpmjs.org/portfinder/download/portfinder-1.0.26.tgz#475658d56ca30bed72ac7f1378ed350bd1b64e70"
  integrity sha1-R1ZY1WyjC+1yrH8TeO01C9G2TnA=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.1"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^7.0.1:
  version "7.0.2"
  resolved "https://r.cnpmjs.org/postcss-calc/download/postcss-calc-7.0.2.tgz#504efcd008ca0273120568b0792b16cdcde8aac1"
  integrity sha1-UE780AjKAnMSBWiweSsWzc3oqsE=
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/postcss-colormin/download/postcss-colormin-4.0.3.tgz#ae060bce93ed794ac71264f08132d550956bd381"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz#ca3813ed4da0f812f9d43703584e449ebe189a7f"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz#1fbabd2c246bff6aaad7997b2b0918f4d7af4033"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz#3fe133cd3c82282e550fc9b239176a9207b784eb"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz#c8c951e9f73ed9428019458444a02ad90bb9f765"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz#652aef8a96726f029f5e3e00146ee7a4e755ff57"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-load-config@^2.0.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/postcss-load-config/download/postcss-load-config-2.1.0.tgz#c84d692b7bb7b41ddced94ee62e8ab31b417b003"
  integrity sha1-yE1pK3u3tB3c7ZTuYuirMbQXsAM=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/postcss-loader/download/postcss-loader-3.0.0.tgz#6b97943e47c72d845fa9e03f273773d4e8dd6c2d"
  integrity sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "https://r.cnpmjs.org/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz#62f49a13e4a0ee04e7b98f42bb16062ca2549e24"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz#362bea4ff5a1f98e4075a713c6cb25aefef9a650"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz#cd4c344cce474343fac5d82206ab2cbcb8afd5a6"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz#93b29c2ff5099c535eecda56c4aa6e665a663471"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz#6b9cef030c11e35261f95f618c90036d680db874"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz#e2e5eb40bfee500d0cd9243500f5f8ea4262fbd8"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz#818719a1ae1da325f9832446b01136eeb493cd7e"
  integrity sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=
  dependencies:
    postcss "^7.0.5"

postcss-modules-local-by-default@^3.0.2:
  version "3.0.2"
  resolved "https://r.cnpmjs.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.2.tgz#e8a6561be914aaf3c052876377524ca90dbb7915"
  integrity sha1-6KZWG+kUqvPAUodjd1JMqQ27eRU=
  dependencies:
    icss-utils "^4.1.1"
    postcss "^7.0.16"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.0"

postcss-modules-scope@^2.2.0:
  version "2.2.0"
  resolved "https://r.cnpmjs.org/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz#385cae013cc7743f5a7d7602d1073a89eaae62ee"
  integrity sha1-OFyuATzHdD9afXYC0Qc6iequYu4=
  dependencies:
    postcss "^7.0.6"
    postcss-selector-parser "^6.0.0"

postcss-modules-values@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz#5b5000d6ebae29b4255301b4a3a54574423e7f10"
  integrity sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=
  dependencies:
    icss-utils "^4.0.0"
    postcss "^7.0.6"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz#8b35add3aee83a136b0471e0d59be58a50285dd4"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz#0dbe04a4ce9063d4667ed2be476bb830c825935a"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz#05f757f84f260437378368a91f8932d4b102917f"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz#c4ebbc289f3991a028d44751cbdd11918b17910c"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz#cd44c40ab07a0c7a36dc5e99aace1eca4ec2690c"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz#8e009ca2a3949cdaf8ad23e6b6ab99cb5e7d28d9"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz#841bd48fdcf3019ad4baa7493a3d363b52ae1cfb"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz#10e437f86bc7c7e58f7b9652ed878daaa95faae1"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz#bf1d4070fe4fcea87d1348e825d8cc0c5faa7d82"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "https://r.cnpmjs.org/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz#0cf75c820ec7d5c4d280189559e0b571ebac0eee"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz#7fd42ebea5e9c814609639e2c2e84ae270ba48df"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz#17efa405eacc6e07be3414a5ca2d1074681d4e29"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "https://r.cnpmjs.org/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz#b310f5c4c0fdaf76f94902bbaa30db6aa84f5270"
  integrity sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.0, postcss-selector-parser@^6.0.2:
  version "6.0.2"
  resolved "https://r.cnpmjs.org/postcss-selector-parser/download/postcss-selector-parser-6.0.2.tgz#934cf799d016c83411859e09dcecade01286ec5c"
  integrity sha1-k0z3mdAWyDQRhZ4J3Oyt4BKG7Fw=
  dependencies:
    cssesc "^3.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-svgo@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/postcss-svgo/download/postcss-svgo-4.0.2.tgz#17b997bc711b333bab143aaed3b8d3d6e3d38258"
  integrity sha1-F7mXvHEbMzurFDqu07jT1uPTglg=
  dependencies:
    is-svg "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://r.cnpmjs.org/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz#9446911f3289bfd64c6d680f073c03b1f9ee4bac"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.0:
  version "3.3.1"
  resolved "https://r.cnpmjs.org/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz#9ff822547e2893213cf1c30efa51ac5fd1ba8281"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^4.0.0, postcss-value-parser@^4.0.2, postcss-value-parser@^4.1.0:
  version "4.1.0"
  resolved "https://r.cnpmjs.org/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz#443f6a20ced6481a2bda4fa8532a6e55d789a2cb"
  integrity sha1-RD9qIM7WSBor2k+oUypuVdeJoss=

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.14, postcss@^7.0.16, postcss@^7.0.27, postcss@^7.0.32, postcss@^7.0.5, postcss@^7.0.6:
  version "7.0.32"
  resolved "https://r.cnpmjs.org/postcss/download/postcss-7.0.32.tgz#4310d6ee347053da3433db2be492883d62cec59d"
  integrity sha1-QxDW7jRwU9o0M9sr5JKIPWLOxZ0=
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier@^1.18.2:
  version "1.19.1"
  resolved "https://r.cnpmjs.org/prettier/download/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

pretty-error@^2.0.2:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/pretty-error/download/pretty-error-2.1.1.tgz#5f4f87c8f91e5ae3f3ba87ab4cf5e03b1a17f1a3"
  integrity sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=
  dependencies:
    renderkid "^2.0.1"
    utila "~0.4"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://r.cnpmjs.org/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

proxy-addr@~2.0.5:
  version "2.0.6"
  resolved "https://r.cnpmjs.org/proxy-addr/download/proxy-addr-2.0.6.tgz#fdc2336505447d3f2f2c638ed272caf614bbb2bf"
  integrity sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://r.cnpmjs.org/psl/download/psl-1.8.0.tgz#9326f8bcfb013adcc005fdff056acce020e51c24"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/public-encrypt/download/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://r.cnpmjs.org/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://r.cnpmjs.org/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.1.2:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qqmap@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/qqmap/download/qqmap-1.0.1.tgz#46279e8f9cd68002e2e0cc5a5fc155f701f401d3"
  integrity sha1-Rieej5zWgALi4MxaX8FV9wH0AdM=

qrcodejs2@^0.0.2:
  version "0.0.2"
  resolved "https://rg.cnpmjs.org/qrcodejs2/download/qrcodejs2-0.0.2.tgz#465afe5e39f19facecb932c11f7a186109146ae1"
  integrity sha1-Rlr+Xjnxn6zsuTLBH3oYYQkUauE=

qs@6.7.0:
  version "6.7.0"
  resolved "https://r.cnpmjs.org/qs/download/qs-6.7.0.tgz#41dc1a015e3d581f1621776be31afb2876a9b1bc"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

qs@~6.5.2:
  version "6.5.2"
  resolved "https://r.cnpmjs.org/qs/download/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

query-string@^4.1.0:
  version "4.3.4"
  resolved "https://r.cnpmjs.org/query-string/download/query-string-4.3.4.tgz#bbb693b9ca915c232515b228b1a02b609043dbeb"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://r.cnpmjs.org/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://r.cnpmjs.org/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/querystringify/download/querystringify-2.1.1.tgz#60e5a5fd64a7f8bfa4d2ab2ed6fdf4c85bad154e"
  integrity sha1-YOWl/WSn+L+k0qsu1v30yFutFU4=

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://r.cnpmjs.org/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://r.cnpmjs.org/raw-body/download/raw-body-2.4.0.tgz#a1ce6fb9c9bc356ca52e89256ab59059e13d0332"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/read-pkg-up/download/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/read-pkg/download/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^5.1.1:
  version "5.2.0"
  resolved "https://r.cnpmjs.org/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.0, readable-stream@^2.3.3, readable-stream@^2.3.5, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://r.cnpmjs.org/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://r.cnpmjs.org/readable-stream/download/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~1.0.0:
  version "1.0.34"
  resolved "https://r.cnpmjs.org/readable-stream/download/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://r.cnpmjs.org/readdirp/download/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.4.0:
  version "3.4.0"
  resolved "https://r.cnpmjs.org/readdirp/download/readdirp-3.4.0.tgz#9fdccdf9e9155805449221ac645e8303ab5b9ada"
  integrity sha1-n9zN+ekVWAVEkiGsZF6DA6tbmto=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://r.cnpmjs.org/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/redent/download/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

regenerate-unicode-properties@^8.2.0:
  version "8.2.0"
  resolved "https://r.cnpmjs.org/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz#e5de7111d655e7ba60c057dbe9ff37c87e65cdec"
  integrity sha1-5d5xEdZV57pgwFfb6f83yH5lzew=
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.4.0:
  version "1.4.1"
  resolved "https://r.cnpmjs.org/regenerate/download/regenerate-1.4.1.tgz#cad92ad8e6b591773485fbe05a485caf4f457e6f"
  integrity sha1-ytkq2Oa1kXc0hfvgWkhcr09Ffm8=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://r.cnpmjs.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.5"
  resolved "https://r.cnpmjs.org/regenerator-runtime/download/regenerator-runtime-0.13.5.tgz#d878a1d094b4306d10b9096484b33ebd55e26697"
  integrity sha1-2Hih0JS0MG0QuQlkhLM+vVXiZpc=

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://r.cnpmjs.org/regenerator-transform/download/regenerator-transform-0.14.5.tgz#c98da154683671c9c4dcb16ece736517e1b7feb4"
  integrity sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/regexp.prototype.flags/download/regexp.prototype.flags-1.3.0.tgz#7aba89b3c13a64509dabcf3ca8d9fbb9bdf5cb75"
  integrity sha1-erqJs8E6ZFCdq888qNn7ub31y3U=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.0-next.1"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpu-core@^4.7.0:
  version "4.7.0"
  resolved "https://r.cnpmjs.org/regexpu-core/download/regexpu-core-4.7.0.tgz#fcbf458c50431b0bb7b45d6967b8192d91f3d938"
  integrity sha1-/L9FjFBDGwu3tF1pZ7gZLZHz2Tg=
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.2.0"
    regjsgen "^0.5.1"
    regjsparser "^0.6.4"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.2.0"

regjsgen@^0.5.1:
  version "0.5.2"
  resolved "https://r.cnpmjs.org/regjsgen/download/regjsgen-0.5.2.tgz#92ff295fb1deecbf6ecdab2543d207e91aa33733"
  integrity sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=

regjsparser@^0.6.4:
  version "0.6.4"
  resolved "https://r.cnpmjs.org/regjsparser/download/regjsparser-0.6.4.tgz#a769f8684308401a66e9b529d2436ff4d0666272"
  integrity sha1-p2n4aEMIQBpm6bUp0kNv9NBmYnI=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "https://r.cnpmjs.org/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.1:
  version "2.0.3"
  resolved "https://r.cnpmjs.org/renderkid/download/renderkid-2.0.3.tgz#380179c2ff5ae1365c522bf2fcfcff01c5b74149"
  integrity sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk=
  dependencies:
    css-select "^1.1.0"
    dom-converter "^0.2"
    htmlparser2 "^3.3.0"
    strip-ansi "^3.0.0"
    utila "^0.4.0"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://r.cnpmjs.org/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/repeating/download/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request@^2.87.0, request@^2.88.0, request@^2.88.2:
  version "2.88.2"
  resolved "https://r.cnpmjs.org/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.0:
  version "1.5.1"
  resolved "https://r.cnpmjs.org/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://r.cnpmjs.org/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6:
  version "1.19.0"
  resolved "https://r.cnpmjs.org/resolve/download/resolve-1.19.0.tgz#1af5bf630409734a067cae29318aac7fa29a267c"
  integrity sha1-GvW/YwQJc0oGfK4pMYqsf6KaJnw=
  dependencies:
    is-core-module "^2.1.0"
    path-parse "^1.0.6"

resolve@^1.10.0, resolve@^1.12.0, resolve@^1.3.2, resolve@^1.8.1:
  version "1.17.0"
  resolved "https://r.cnpmjs.org/resolve/download/resolve-1.17.0.tgz#b25941b54968231cc2d1bb76a79cb7f2c0bf8444"
  integrity sha1-sllBtUloIxzC0bt2p5y38sC/hEQ=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://r.cnpmjs.org/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "https://r.cnpmjs.org/retry/download/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/rgb-regex/download/rgb-regex-1.0.1.tgz#c0e0d6882df0e23be254a475e8edd41915feaeb1"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/rgba-regex/download/rgba-regex-1.0.0.tgz#43374e2e2ca0968b0ef1523460b7d730ff22eeb3"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

rimraf@2, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.3, rimraf@^2.7.1:
  version "2.7.1"
  resolved "https://r.cnpmjs.org/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://r.cnpmjs.org/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://r.cnpmjs.org/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.6.0:
  version "6.6.0"
  resolved "https://r.cnpmjs.org/rxjs/download/rxjs-6.6.0.tgz#af2901eedf02e3a83ffa7f886240ff9018bbec84"
  integrity sha1-rykB7t8C46g/+n+IYkD/kBi77IQ=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://r.cnpmjs.org/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://r.cnpmjs.org/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-graph@2.2.5:
  version "2.2.5"
  resolved "https://r.cnpmjs.org/sass-graph/download/sass-graph-2.2.5.tgz#a981c87446b8319d96dce0671e487879bd24c2e8"
  integrity sha1-qYHIdEa4MZ2W3OBnHkh4eb0kwug=
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^13.3.2"

sass-loader@^9.0.2:
  version "9.0.2"
  resolved "https://r.cnpmjs.org/sass-loader/download/sass-loader-9.0.2.tgz#847c9b4c95328ddc8c7d35cf28c9d6e54e59a90b"
  integrity sha1-hHybTJUyjdyMfTXPKMnW5U5ZqQs=
  dependencies:
    klona "^1.1.1"
    loader-utils "^2.0.0"
    neo-async "^2.6.1"
    schema-utils "^2.7.0"
    semver "^7.3.2"

sax@~1.2.4:
  version "1.2.4"
  resolved "https://r.cnpmjs.org/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

sb-promise-queue@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/sb-promise-queue/download/sb-promise-queue-2.1.0.tgz#7e44bebef643f75d809a3db7f605b815d877a04d"
  integrity sha1-fkS+vvZD912Amj239gW4Fdh3oE0=

sb-scandir@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/sb-scandir/download/sb-scandir-3.1.0.tgz#31c346abb5184b73c5a25b286858f4299aa8756c"
  integrity sha1-McNGq7UYS3PFolsoaFj0KZqodWw=
  dependencies:
    sb-promise-queue "^2.1.0"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/schema-utils/download/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.0.0, schema-utils@^2.5.0, schema-utils@^2.6.5, schema-utils@^2.6.6, schema-utils@^2.7.0:
  version "2.7.0"
  resolved "https://r.cnpmjs.org/schema-utils/download/schema-utils-2.7.0.tgz#17151f76d8eae67fbbf77960c33c676ad9f4efc7"
  integrity sha1-FxUfdtjq5n+793lgwzxnatn078c=
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

scp2@^0.5.0:
  version "0.5.0"
  resolved "https://r.cnpmjs.org/scp2/download/scp2-0.5.0.tgz#64ee74bc3685f3a4c6290f2da8c1e3b4eef92e8d"
  integrity sha1-ZO50vDaF86TGKQ8tqMHjtO75Lo0=
  dependencies:
    async "~0.9.0"
    glob "~7.0.3"
    lodash "~4.11.1"
    ssh2 "~0.4.10"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "https://r.cnpmjs.org/scss-tokenizer/download/scss-tokenizer-0.2.3.tgz#8eb06db9a9723333824d3f5530641149847ce5d1"
  integrity sha1-jrBtualyMzOCTT9VMGQRSYR85dE=
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.7:
  version "1.10.7"
  resolved "https://r.cnpmjs.org/selfsigned/download/selfsigned-1.10.7.tgz#da5819fd049d5574f28e88a9bcc6dbc6e6f3906b"
  integrity sha1-2lgZ/QSdVXTyjoipvMbbxubzkGs=
  dependencies:
    node-forge "0.9.0"

"semver@2 || 3 || 4 || 5", semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "https://r.cnpmjs.org/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@7.0.0:
  version "7.0.0"
  resolved "https://r.cnpmjs.org/semver/download/semver-7.0.0.tgz#5f3ca35761e47e05b206c6daff2cf814f0316b8e"
  integrity sha1-XzyjV2HkfgWyBsba/yz4FPAxa44=

semver@^6.0.0, semver@^6.1.0, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://r.cnpmjs.org/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.3.2:
  version "7.3.2"
  resolved "https://r.cnpmjs.org/semver/download/semver-7.3.2.tgz#604962b052b81ed0786aae84389ffba70ffd3938"
  integrity sha1-YElisFK4HtB4aq6EOJ/7pw/9OTg=

semver@~5.3.0:
  version "5.3.0"
  resolved "https://r.cnpmjs.org/semver/download/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"
  integrity sha1-myzl094C0XxgEq0yaqa00M9U+U8=

send@0.17.1:
  version "0.17.1"
  resolved "https://r.cnpmjs.org/send/download/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^2.1.2:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/serialize-javascript/download/serialize-javascript-2.1.2.tgz#ecec53b0e0317bdc95ef76ab7074b7384785fa61"
  integrity sha1-7OxTsOAxe9yV73arcHS3OEeF+mE=

serialize-javascript@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/serialize-javascript/download/serialize-javascript-3.1.0.tgz#8bf3a9170712664ef2561b44b691eafe399214ea"
  integrity sha1-i/OpFwcSZk7yVhtEtpHq/jmSFOo=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://r.cnpmjs.org/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://r.cnpmjs.org/serve-static/download/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://r.cnpmjs.org/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/setprototypeof/download/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://r.cnpmjs.org/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-escape@^0.2.0:
  version "0.2.0"
  resolved "https://r.cnpmjs.org/shell-escape/download/shell-escape-0.2.0.tgz#68fd025eb0490b4f567a027f0bf22480b5f84133"
  integrity sha1-aP0CXrBJC09WegJ/C/IkgLX4QTM=

shell-quote@^1.6.1:
  version "1.7.2"
  resolved "https://r.cnpmjs.org/shell-quote/download/shell-quote-1.7.2.tgz#67a7d02c76c9da24f99d20808fcaded0e0e04be2"
  integrity sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I=

shelljs@^0.8.4:
  version "0.8.4"
  resolved "https://r.cnpmjs.org/shelljs/download/shelljs-0.8.4.tgz#de7684feeb767f8716b326078a8a00875890e3c2"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://r.cnpmjs.org/signal-exit/download/signal-exit-3.0.3.tgz#a1410c2edd8f077b08b4e253c8eacfcaf057461c"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://r.cnpmjs.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/slash/download/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://r.cnpmjs.org/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://r.cnpmjs.org/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@1.4.0:
  version "1.4.0"
  resolved "https://r.cnpmjs.org/sockjs-client/download/sockjs-client-1.4.0.tgz#c9f2568e19c8fd8173b4997ea3420e0bb306c7d5"
  integrity sha1-yfJWjhnI/YFztJl+o0IOC7MGx9U=
  dependencies:
    debug "^3.2.5"
    eventsource "^1.0.7"
    faye-websocket "~0.11.1"
    inherits "^2.0.3"
    json3 "^3.3.2"
    url-parse "^1.4.3"

sockjs@0.3.20:
  version "0.3.20"
  resolved "https://r.cnpmjs.org/sockjs/download/sockjs-0.3.20.tgz#b26a283ec562ef8b2687b44033a4eeceac75d855"
  integrity sha1-smooPsVi74smh7RAM6Tuzqx12FU=
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.4.0"
    websocket-driver "0.6.5"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/sort-keys/download/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://r.cnpmjs.org/source-list-map/download/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://r.cnpmjs.org/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@~0.5.12:
  version "0.5.19"
  resolved "https://r.cnpmjs.org/source-map-support/download/source-map-support-0.5.19.tgz#a98b62f86dcaf4f67399648c085291ab9e8fed61"
  integrity sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://r.cnpmjs.org/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.4.2:
  version "0.4.4"
  resolved "https://r.cnpmjs.org/source-map/download/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  integrity sha1-66T12pwNyZneaAMti092FzZSA2s=
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"
  resolved "https://r.cnpmjs.org/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://r.cnpmjs.org/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/spdx-correct/download/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "https://r.cnpmjs.org/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
  integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://r.cnpmjs.org/spdy-transport/download/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/spdy/download/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

ssh2-streams@~0.0.22:
  version "0.0.23"
  resolved "https://r.cnpmjs.org/ssh2-streams/download/ssh2-streams-0.0.23.tgz#aeef30831bb5fc4af6aa3f6d0a261a413531612b"
  integrity sha1-ru8wgxu1/Er2qj9tCiYaQTUxYSs=
  dependencies:
    asn1 "~0.2.0"
    readable-stream "~1.0.0"
    streamsearch "~0.1.2"

ssh2-streams@~0.4.10:
  version "0.4.10"
  resolved "https://r.cnpmjs.org/ssh2-streams/download/ssh2-streams-0.4.10.tgz#48ef7e8a0e39d8f2921c30521d56dacb31d23a34"
  integrity sha1-SO9+ig452PKSHDBSHVbayzHSOjQ=
  dependencies:
    asn1 "~0.2.0"
    bcrypt-pbkdf "^1.0.2"
    streamsearch "~0.1.2"

ssh2@^0.8.9:
  version "0.8.9"
  resolved "https://r.cnpmjs.org/ssh2/download/ssh2-0.8.9.tgz#54da3a6c4ba3daf0d8477a538a481326091815f3"
  integrity sha1-VNo6bEuj2vDYR3pTikgTJgkYFfM=
  dependencies:
    ssh2-streams "~0.4.10"

ssh2@~0.4.10:
  version "0.4.15"
  resolved "https://r.cnpmjs.org/ssh2/download/ssh2-0.4.15.tgz#07c6f4106d9f7b6ea6e4df636c6c53f1f9817ff8"
  integrity sha1-B8b0EG2fe26m5N9jbGxT8fmBf/g=
  dependencies:
    readable-stream "~1.0.0"
    ssh2-streams "~0.0.22"

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://r.cnpmjs.org/sshpk/download/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.1:
  version "6.0.1"
  resolved "https://r.cnpmjs.org/ssri/download/ssri-6.0.1.tgz#2a3c41b28dd45b62b63676ecb74001265ae9edd8"
  integrity sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=
  dependencies:
    figgy-pudding "^3.5.1"

ssri@^7.0.0, ssri@^7.1.0:
  version "7.1.0"
  resolved "https://r.cnpmjs.org/ssri/download/ssri-7.1.0.tgz#92c241bf6de82365b5c7fb4bd76e975522e1294d"
  integrity sha1-ksJBv23oI2W1x/tL126XVSLhKU0=
  dependencies:
    figgy-pudding "^3.5.1"
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://r.cnpmjs.org/stable/download/stable-0.1.8.tgz#836eb3c8382fe2936feaf544631017ce7d47a3cf"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/stackframe/download/stackframe-1.2.0.tgz#52429492d63c62eb989804c11552e3d22e779303"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://r.cnpmjs.org/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "https://r.cnpmjs.org/stdout-stream/download/stdout-stream-1.4.1.tgz#5ac174cdd5cd726104aa0c0b2bd83815d8d535de"
  integrity sha1-WsF0zdXNcmEEqgwLK9g4FdjVNd4=
  dependencies:
    readable-stream "^2.0.1"

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/stream-browserify/download/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://r.cnpmjs.org/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://r.cnpmjs.org/stream-http/download/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/stream-shift/download/stream-shift-1.0.1.tgz#d7088281559ab2778424279b0877da3c392d5a3d"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

streamifier@^0.1.1:
  version "0.1.1"
  resolved "https://r.cnpmjs.org/streamifier/download/streamifier-0.1.1.tgz#97e98d8fa4d105d62a2691d1dc07e820db8dfc4f"
  integrity sha1-l+mNj6TRBdYqJpHR3AfoINuN/E8=

streamsearch@~0.1.2:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/streamsearch/download/streamsearch-0.1.2.tgz#808b9d0e56fc273d809ba57338e929919a1a9f1a"
  integrity sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.0.0:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://r.cnpmjs.org/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "https://r.cnpmjs.org/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.trimend@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/string.prototype.trimend/download/string.prototype.trimend-1.0.1.tgz#85812a6b847ac002270f5808146064c995fb6913"
  integrity sha1-hYEqa4R6wAInD1gIFGBkyZX7aRM=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trimstart@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/string.prototype.trimstart/download/string.prototype.trimstart-1.0.1.tgz#14af6d9f34b053f7cfc89b72f8f2ee14b9039a54"
  integrity sha1-FK9tnzSwU/fPyJty+PLuFLkDmlQ=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://r.cnpmjs.org/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://r.cnpmjs.org/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://r.cnpmjs.org/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://r.cnpmjs.org/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://r.cnpmjs.org/strip-ansi/download/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/strip-indent/download/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/strip-indent/download/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-json-comments@^3.0.1:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "https://r.cnpmjs.org/stylehacks/download/stylehacks-4.0.3.tgz#6718fcaf4d1e07d8a1318690881e8d96726a71d5"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://r.cnpmjs.org/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://r.cnpmjs.org/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.1.0"
  resolved "https://r.cnpmjs.org/supports-color/download/supports-color-7.1.0.tgz#68e32591df73e25ad1c4b49108a2ec507962bfd1"
  integrity sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E=
  dependencies:
    has-flag "^4.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^1.0.0:
  version "1.3.2"
  resolved "https://r.cnpmjs.org/svgo/download/svgo-1.3.2.tgz#b6dc511c063346c9e415b81e43401145b96d4167"
  integrity sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

table@^5.2.3:
  version "5.4.6"
  resolved "https://r.cnpmjs.org/table/download/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/tapable/download/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tar-stream@^1.5.2:
  version "1.6.2"
  resolved "https://r.cnpmjs.org/tar-stream/download/tar-stream-1.6.2.tgz#8ea55dab37972253d9a9af90fdcd559ae435c555"
  integrity sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

tar@^2.0.0:
  version "2.2.2"
  resolved "https://r.cnpmjs.org/tar/download/tar-2.2.2.tgz#0ca8848562c7299b8b446ff6a4d60cdbb23edc40"
  integrity sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA=
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

terser-webpack-plugin@^1.4.3:
  version "1.4.4"
  resolved "https://r.cnpmjs.org/terser-webpack-plugin/download/terser-webpack-plugin-1.4.4.tgz#2c63544347324baafa9a56baaddf1634c8abfc2f"
  integrity sha1-LGNUQ0cyS6r6mla6rd8WNMir/C8=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^3.1.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser-webpack-plugin@^2.3.6:
  version "2.3.7"
  resolved "https://r.cnpmjs.org/terser-webpack-plugin/download/terser-webpack-plugin-2.3.7.tgz#4910ff5d1a872168cc7fa6cd3749e2b0d60a8a0b"
  integrity sha1-SRD/XRqHIWjMf6bNN0nisNYKigs=
  dependencies:
    cacache "^13.0.1"
    find-cache-dir "^3.3.1"
    jest-worker "^25.4.0"
    p-limit "^2.3.0"
    schema-utils "^2.6.6"
    serialize-javascript "^3.1.0"
    source-map "^0.6.1"
    terser "^4.6.12"
    webpack-sources "^1.4.3"

terser@^4.1.2, terser@^4.6.12:
  version "4.8.0"
  resolved "https://r.cnpmjs.org/terser/download/terser-4.8.0.tgz#63056343d7c70bb29f3af665865a46fe03a0df17"
  integrity sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://r.cnpmjs.org/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://r.cnpmjs.org/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://r.cnpmjs.org/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thread-loader@^2.1.3:
  version "2.1.3"
  resolved "https://r.cnpmjs.org/thread-loader/download/thread-loader-2.1.3.tgz#cbd2c139fc2b2de6e9d28f62286ab770c1acbdda"
  integrity sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=
  dependencies:
    loader-runner "^2.3.1"
    loader-utils "^1.1.0"
    neo-async "^2.6.0"

throttle-debounce@^1.0.1:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/throttle-debounce/download/throttle-debounce-1.1.0.tgz#51853da37be68a155cb6e827b3514a3c422e89cd"
  integrity sha1-UYU9o3vmihVctugns1FKPEIuic0=

through2@^2.0.0:
  version "2.0.5"
  resolved "https://r.cnpmjs.org/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://r.cnpmjs.org/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/thunky/download/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.11"
  resolved "https://r.cnpmjs.org/timers-browserify/download/timers-browserify-2.0.11.tgz#800b1f3eee272e5bc53ee465a04d0e804c31211f"
  integrity sha1-gAsfPu4nLlvFPuRloE0OgEwxIR8=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://r.cnpmjs.org/timsort/download/timsort-0.3.0.tgz#405411a8e7e6339fe64db9a234de11dc31e02bd4"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://r.cnpmjs.org/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/to-buffer/download/to-buffer-1.1.1.tgz#493bd48f62d7c43fcded313a03dcadb2e1213a80"
  integrity sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://r.cnpmjs.org/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://r.cnpmjs.org/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://r.cnpmjs.org/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/toidentifier/download/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://r.cnpmjs.org/toposort/download/toposort-1.0.7.tgz#2e68442d9f64ec720b8cc89e6443ac6caa950029"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://r.cnpmjs.org/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/trim-newlines/download/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "https://r.cnpmjs.org/true-case-path/download/true-case-path-1.0.3.tgz#f813b5a8c86b40da59606722b144e3225799f47d"
  integrity sha1-+BO1qMhrQNpZYGcisUTjIleZ9H0=
  dependencies:
    glob "^7.1.2"

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/tryer/download/tryer-1.0.1.tgz#f2c85406800b9b0f74c9f7465b81eaad241252f8"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-pnp@^1.1.6:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/ts-pnp/download/ts-pnp-1.2.0.tgz#a500ad084b0798f1c3071af391e65912c86bca92"
  integrity sha1-pQCtCEsHmPHDBxrzkeZZEshrypI=

tslib@2.3.0:
  version "2.3.0"
  resolved "https://rg.cnpmjs.org/tslib/download/tslib-2.3.0.tgz#803b8cdab3e12ba581a4ca41c8839bbb0dacb09e"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tslib@^1.9.0:
  version "1.13.0"
  resolved "https://r.cnpmjs.org/tslib/download/tslib-1.13.0.tgz#c881e13cc7015894ed914862d276436fa9a47043"
  integrity sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM=

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://r.cnpmjs.org/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://r.cnpmjs.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://r.cnpmjs.org/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://r.cnpmjs.org/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.11.0:
  version "0.11.0"
  resolved "https://r.cnpmjs.org/type-fest/download/type-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://r.cnpmjs.org/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://r.cnpmjs.org/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://r.cnpmjs.org/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://r.cnpmjs.org/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://r.cnpmjs.org/uglify-js/download/uglify-js-3.4.10.tgz#9ad9563d8eb3acdfb8d38597d2af1d815f6a755f"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz#2619800c4c825800efdd8343af7dd9933cbe2818"
  integrity sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz#8ed2a32569961bce9227d09cd3ffbb8fed5f020c"
  integrity sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.2.0:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz#0d91f600eeeb3096aa962b1d6fc88876e64ea531"
  integrity sha1-DZH2AO7rMJaqlisdb8iIduZOpTE=

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.1.0"
  resolved "https://r.cnpmjs.org/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz#dd57a99f6207bedff4628abefb94c50db941c8f4"
  integrity sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ=

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/uniq/download/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/uniqs/download/uniqs-2.0.0.tgz#ffede4b36b25290696e6e165d4a59edb998e6b02"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/unique-slug/download/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://r.cnpmjs.org/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://r.cnpmjs.org/unquote/download/unquote-1.1.1.tgz#8fded7324ec6e88a0ff8b905e7c098cdc086d544"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://r.cnpmjs.org/upath/download/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/upper-case/download/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.2.2"
  resolved "https://r.cnpmjs.org/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  integrity sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://r.cnpmjs.org/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^2.2.0:
  version "2.3.0"
  resolved "https://r.cnpmjs.org/url-loader/download/url-loader-2.3.0.tgz#e0e2ef658f003efb8ca41b0f3ffbf76bab88658b"
  integrity sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs=
  dependencies:
    loader-utils "^1.2.3"
    mime "^2.4.4"
    schema-utils "^2.5.0"

url-parse@^1.4.3:
  version "1.4.7"
  resolved "https://r.cnpmjs.org/url-parse/download/url-parse-1.4.7.tgz#a8a83535e8c00a316e403a5db4ac1b9b853ae278"
  integrity sha1-qKg1NejACjFuQDpdtKwbm4U64ng=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.0"
  resolved "https://r.cnpmjs.org/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@1.0.0:
  version "1.0.0"
  resolved "https://r.cnpmjs.org/util.promisify/download/util.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/util.promisify/download/util.promisify-1.0.1.tgz#6baf7774b80eeb0f7520d8b81d07982a59abbaee"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

util@0.10.3:
  version "0.10.3"
  resolved "https://r.cnpmjs.org/util/download/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "https://r.cnpmjs.org/util/download/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@^0.4.0, utila@~0.4:
  version "0.4.0"
  resolved "https://r.cnpmjs.org/utila/download/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2, uuid@^3.4.0:
  version "3.4.0"
  resolved "https://r.cnpmjs.org/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v-distpicker@^1.2.7:
  version "1.2.7"
  resolved "https://r.cnpmjs.org/v-distpicker/download/v-distpicker-1.2.7.tgz#d4599056bda534cc231a0d33932b68deb9323ac8"
  integrity sha1-1FmQVr2lNMwjGg0zkyto3rkyOsg=
  dependencies:
    vue "^2.6.10"

v8-compile-cache@^2.0.3:
  version "2.1.1"
  resolved "https://r.cnpmjs.org/v8-compile-cache/download/v8-compile-cache-2.1.1.tgz#54bc3cdd43317bca91e35dcaf305b1a7237de745"
  integrity sha1-VLw83UMxe8qR413K8wWxpyN950U=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://r.cnpmjs.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://r.cnpmjs.org/vendors/download/vendors-1.0.4.tgz#e2b800a53e7a29b93506c3cf41100d16c4c4ad8e"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "https://r.cnpmjs.org/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://r.cnpmjs.org/vm-browserify/download/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-eslint-parser@^7.0.0:
  version "7.1.0"
  resolved "https://r.cnpmjs.org/vue-eslint-parser/download/vue-eslint-parser-7.1.0.tgz#9cdbcc823e656b087507a1911732b867ac101e83"
  integrity sha1-nNvMgj5lawh1B6GRFzK4Z6wQHoM=
  dependencies:
    debug "^4.1.1"
    eslint-scope "^5.0.0"
    eslint-visitor-keys "^1.1.0"
    espree "^6.2.1"
    esquery "^1.0.1"
    lodash "^4.17.15"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://r.cnpmjs.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz#532955cc1eb208a3d990b3a9f9a70574657e08f2"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

vue-loader@^15.9.2:
  version "15.9.3"
  resolved "https://r.cnpmjs.org/vue-loader/download/vue-loader-15.9.3.tgz#0de35d9e555d3ed53969516cac5ce25531299dda"
  integrity sha1-DeNdnlVdPtU5aVFsrFziVTEpndo=
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-router@^3.3.4:
  version "3.3.4"
  resolved "https://r.cnpmjs.org/vue-router/download/vue-router-3.3.4.tgz#4e38abc34a11c41b6c3d8244449a2e363ba6250b"
  integrity sha1-Tjirw0oRxBtsPYJERJouNjumJQs=

vue-style-loader@^4.1.0, vue-style-loader@^4.1.2:
  version "4.1.2"
  resolved "https://r.cnpmjs.org/vue-style-loader/download/vue-style-loader-4.1.2.tgz#dedf349806f25ceb4e64f3ad7c0a44fba735fcf8"
  integrity sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.6.11:
  version "2.6.11"
  resolved "https://r.cnpmjs.org/vue-template-compiler/download/vue-template-compiler-2.6.11.tgz#c04704ef8f498b153130018993e56309d4698080"
  integrity sha1-wEcE749JixUxMAGJk+VjCdRpgIA=
  dependencies:
    de-indent "^1.0.2"
    he "^1.1.0"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://r.cnpmjs.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz#1ee3bc9a16ecbf5118be334bb15f9c46f82f5825"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue@^2.6.10, vue@^2.6.11:
  version "2.6.11"
  resolved "https://r.cnpmjs.org/vue/download/vue-2.6.11.tgz#76594d877d4b12234406e84e35275c6d514125c5"
  integrity sha1-dllNh31LEiNEBuhONSdcbVFBJcU=

vuex@^3.5.1:
  version "3.5.1"
  resolved "https://r.cnpmjs.org/vuex/download/vuex-3.5.1.tgz#f1b8dcea649bc25254cf4f4358081dbf5da18b3d"
  integrity sha1-8bjc6mSbwlJUz09DWAgdv12hiz0=

watchpack-chokidar2@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/watchpack-chokidar2/download/watchpack-chokidar2-2.0.0.tgz#9948a1866cbbd6cb824dea13a7ed691f6c8ddff0"
  integrity sha1-mUihhmy71suCTeoTp+1pH2yN3/A=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.6.1:
  version "1.7.2"
  resolved "https://r.cnpmjs.org/watchpack/download/watchpack-1.7.2.tgz#c02e4d4d49913c3e7e122c3325365af9d331e9aa"
  integrity sha1-wC5NTUmRPD5+EiwzJTZa+dMx6ao=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.0"
    watchpack-chokidar2 "^2.0.0"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://r.cnpmjs.org/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://r.cnpmjs.org/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webpack-bundle-analyzer@^3.8.0:
  version "3.8.0"
  resolved "https://r.cnpmjs.org/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.8.0.tgz#ce6b3f908daf069fd1f7266f692cbb3bded9ba16"
  integrity sha1-zms/kI2vBp/R9yZvaSy7O97ZuhY=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-chain@^6.4.0:
  version "6.5.0"
  resolved "https://r.cnpmjs.org/webpack-chain/download/webpack-chain-6.5.0.tgz#0b4af2094a5058a9ccd34b8f7ab194de4c83365f"
  integrity sha1-C0ryCUpQWKnM00uPerGU3kyDNl8=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^3.7.2:
  version "3.7.2"
  resolved "https://r.cnpmjs.org/webpack-dev-middleware/download/webpack-dev-middleware-3.7.2.tgz#0019c3db716e3fa5cecbf64f2ab88a74bab331f3"
  integrity sha1-ABnD23FuP6XOy/ZPKriKdLqzMfM=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.11.0:
  version "3.11.0"
  resolved "https://r.cnpmjs.org/webpack-dev-server/download/webpack-dev-server-3.11.0.tgz#8f154a3bce1bcfd1cc618ef4e703278855e7ff8c"
  integrity sha1-jxVKO84bz9HMYY705wMniFXn/4w=
  dependencies:
    ansi-html "0.0.7"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.7"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "0.3.20"
    sockjs-client "1.4.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/webpack-log/download/webpack-log-2.0.0.tgz#5b7928e0637593f119d32f6227c1e0ac31e1b47f"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^4.2.2:
  version "4.2.2"
  resolved "https://r.cnpmjs.org/webpack-merge/download/webpack-merge-4.2.2.tgz#a27c52ea783d1398afd2087f547d7b9d2f43634d"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1, webpack-sources@^1.4.3:
  version "1.4.3"
  resolved "https://r.cnpmjs.org/webpack-sources/download/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^4.0.0:
  version "4.43.0"
  resolved "https://r.cnpmjs.org/webpack/download/webpack-4.43.0.tgz#c48547b11d563224c561dad1172c8aa0b8a678e6"
  integrity sha1-xIVHsR1WMiTFYdrRFyyKoLimeOY=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.6.1"
    webpack-sources "^1.4.1"

websocket-driver@0.6.5:
  version "0.6.5"
  resolved "https://r.cnpmjs.org/websocket-driver/download/websocket-driver-0.6.5.tgz#5cb2556ceb85f4373c6d8238aa691c8454e13a36"
  integrity sha1-XLJVbOuF9Dc8bYI4qmkchFThOjY=
  dependencies:
    websocket-extensions ">=0.1.1"

websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://r.cnpmjs.org/websocket-driver/download/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://r.cnpmjs.org/websocket-extensions/download/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

weixin-js-sdk@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/weixin-js-sdk/-/weixin-js-sdk-1.6.0.tgz#ff50484d8118ce1208f11248cf4a1c0831577514"
  integrity sha512-3IYQH7aalJGFJrwdT3epvTdR1MboMiH7vIZ5BRL2eYOJ12BNah7csoMkmSZzkq1+l92sSq29XdTCVjCJoK2sBQ==

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@1, which@^1.2.9:
  version "1.3.1"
  resolved "https://r.cnpmjs.org/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://r.cnpmjs.org/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.3"
  resolved "https://r.cnpmjs.org/wide-align/download/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
  integrity sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=
  dependencies:
    string-width "^1.0.2 || 2"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://r.cnpmjs.org/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://r.cnpmjs.org/worker-farm/download/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://r.cnpmjs.org/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://r.cnpmjs.org/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://r.cnpmjs.org/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write@1.0.3:
  version "1.0.3"
  resolved "https://r.cnpmjs.org/write/download/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^6.0.0, ws@^6.2.1:
  version "6.2.1"
  resolved "https://r.cnpmjs.org/ws/download/ws-6.2.1.tgz#442fdf0a47ed64f59b6a5d8ff130f4748ed524fb"
  integrity sha1-RC/fCkftZPWbal2P8TD0dI7VJPs=
  dependencies:
    async-limiter "~1.0.0"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://r.cnpmjs.org/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/y18n/download/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
  integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://r.cnpmjs.org/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://r.cnpmjs.org/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://r.cnpmjs.org/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://r.cnpmjs.org/yargs-parser/download/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://r.cnpmjs.org/yargs-parser/download/yargs-parser-18.1.3.tgz#be68c4975c6b2abf469236b0c870362fab09a7b0"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://r.cnpmjs.org/yargs/download/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.0.0:
  version "15.4.1"
  resolved "https://r.cnpmjs.org/yargs/download/yargs-15.4.1.tgz#0d87a16de01aee9d8bec2bfbf74f67851730f4f8"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yauzl@^2.7.0:
  version "2.10.0"
  resolved "https://r.cnpmjs.org/yauzl/download/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  integrity sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yazl@^2.4.2:
  version "2.5.1"
  resolved "https://r.cnpmjs.org/yazl/download/yazl-2.5.1.tgz#a3d65d3dd659a5b0937850e8609f22fffa2b5c35"
  integrity sha1-o9ZdPdZZpbCTeFDoYJ8i//orXDU=
  dependencies:
    buffer-crc32 "~0.2.3"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://r.cnpmjs.org/yorkie/download/yorkie-2.0.0.tgz#92411912d435214e12c51c2ae1093e54b6bb83d9"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

zrender@5.2.1:
  version "5.2.1"
  resolved "https://rg.cnpmjs.org/zrender/download/zrender-5.2.1.tgz#5f4bbda915ba6d412b0b19dc2431beaad05417bb"
  integrity sha1-X0u9qRW6bUErCxncJDG+qtBUF7s=
  dependencies:
    tslib "2.3.0"
