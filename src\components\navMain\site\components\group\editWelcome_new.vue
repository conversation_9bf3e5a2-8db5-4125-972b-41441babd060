<template>
  <div>
    <!-- <div class="tips">
      <div>
        1.在二维码处设置的欢迎语会被优先推送，如果成员在二维码处设置了欢迎语，在此设置的欢迎语
        不会生效
      </div>

      <div>
        2.欢迎语将在客户加为好友后20秒内发下，因网络延可能造成发送不成功
      </div>
    </div> -->
    <el-form label-width="100px">
      <el-form-item label="是否通知">
        <div class="form-item-block align-center flex-wrap">
          <el-switch v-model="notify" active-color="#2D84FB"> </el-switch>
        </div>
      </el-form-item>
      <el-form-item label="群欢迎语">
        <welcome-mes
          style="padding-right: 20px"
          ref="group_welcome"
          :value="welcome_mes"
        ></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>
import config from "@/utils/config";
import welcomeMes from "../../../crm/components/welcome_mes"
export default {
  components: {
    welcomeMes
  },
  props: ["form"],
  data() {
    return {
      form_params: {
        userid: "",
        title: "",
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      cover: "",
      imgList: [],
      show_select_dia: false,
      currentImg: "",
      params_type: "",
      img_params: {
        page: 1,
        per_page: 20,
        type: 1,
      },
      notify: '',
      welcome_mes: {
        text: {
          type: 1,
          content: "",
        },
        image: {
          type: 2,
          media_id: "",
          pic_url: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: ''
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      params_type_arr: [],
      id: "",
      filename: "",
      videoname: "",
      imgname: "",
      miniCover: "",
    };
  },
  created() {
    for (const key in this.form) {
      this.form_params[key] = this.form[key];
    }
    this.notify = this.form_params.notify == 0 ? false : true
    for (const key in this.form_params.group_welcome) {
      this.$set(
        this.welcome_mes,
        key,
        JSON.parse(JSON.stringify(this.form_params.group_welcome[key]))
      );
      // this.welcome_mes[key] = JSON.parse(JSON.stringify(this.form_params.welcome))
    }

    if (this.form_params.group_welcome.image) {
      delete this.welcome_mes.image.name;
    }
    if (this.form_params.group_welcome.file) {
      delete this.welcome_mes.file.name;
    }
    if (this.form_params.group_welcome.video) {
      delete this.welcome_mes.video.name;
      delete this.welcome_mes.video.url;
    }
    if (this.form_params.group_welcome.miniprogram) {
      delete this.welcome_mes.miniprogram.name;
      delete this.welcome_mes.miniprogram.org_url;
    }
    if (this.form_params.group_welcome.link) {
      delete this.welcome_mes.link.name;
    }
    delete this.welcome_mes.text.name;
    console.log(this.welcome_mes, 11);
    // this.$set(this.welcome_mes.text, "desc", this.form_params.welcome.text.content)
    this.$forceUpdate();
  },
  computed: {
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  methods: {
    subform() {
      let params = {}
      let oldTypeArr = Object.keys(this.form_params.group_welcome)
      let _welcome_mes = Object.assign({}, this.$refs.group_welcome.welcome_mes);

      if (!_welcome_mes.text.desc) {
        this.$message.warning("欢迎语不能为空");
        return;
      }

      if (
        _welcome_mes.image &&
        !_welcome_mes.image.media_id
      ) {
        if (oldTypeArr.includes("image")) {
          _welcome_mes.image = this.form_params.welcome.image
          _welcome_mes.image.is_del = 1
          delete _welcome_mes.image.name
        } else {
          delete _welcome_mes.image;
        }

      }
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key]) {

            emptyLink.push(key);


          }
        }
        if (emptyLink.length == Object.keys(linkArr).length) {
          if (oldTypeArr.includes("link")) {
            _welcome_mes.link = this.form_params.welcome.link
            _welcome_mes.link.is_del = 1
          } else {
            emptyLink.length = 0;
            delete _welcome_mes.link;
          }

          // emptyLink.length = 0;
          // delete _welcome_mes.link;
        } else if (emptyLink.length) {
          if (!oldTypeArr.includes("link")) {
            this.$message.warning(linkArr[emptyLink[0]]);
            return;
          }

        }
      }

      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (
            !_welcome_mes.miniprogram[key]
          ) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          if (oldTypeArr.includes("miniprogram")) {
            _welcome_mes.miniprogram = this.form_params.welcome.miniprogram
            _welcome_mes.miniprogram.is_del = 1
          } else {
            emptyMini.length = 0;
            delete _welcome_mes.miniprogram;
          }

        } else if (emptyMini.length) {
          if (!oldTypeArr.includes("miniprogram")) {
            this.$message.warning(miniArr[emptyMini[0]]);
            return;
          }
        }
      }

      if (
        _welcome_mes.video &&
        !_welcome_mes.video.media_id
      ) {
        if (oldTypeArr.includes("video")) {
          _welcome_mes.video = this.form_params.welcome.video
          _welcome_mes.video.is_del = 1
        } else {
          delete _welcome_mes.video;
        }

      }
      if (
        _welcome_mes.file &&
        !_welcome_mes.file.media_id
      ) {
        if (oldTypeArr.includes("file")) {
          _welcome_mes.file = this.form_params.welcome.file
          _welcome_mes.file.is_del = 1
        } else {
          delete _welcome_mes.file;
        }
      }
      _welcome_mes.template_id = this.form_params.template_id;
      params.welcome_msg = JSON.stringify(_welcome_mes);
      params.id = this.form_params.id
      params.notify = this.notify ? 1 : 0
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .editCrmGroupWelcomeWord(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },





  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 30px;
  &.type_tips {
    margin-bottom: 0;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .files {
    margin-bottom: 10px;
    margin-right: 6px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;
    &.active {
      border: 5px solid #409eff;
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    .file_img {
      width: 120px;
      height: 120px;
      text-align: center;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.el-form-item ::v-deep .el-radio {
  margin-right: 0;
  &.active {
    border: 1px solid #a6e22e;
  }
}
.upload-demo {
  margin-right: 5px;
}
</style>
