<template>
  <!-- 侧边栏导航 -->
  <el-menu
    style="border: none"
    @select="openMenu"
    @open="openClick"
    :default-active="indexPath"
    class="el-menu-vertical-demo"
    background-color="#3A3F53"
    active-text-color="#fff"
    text-color="#fff"
    unique-opened
    router
  >
    <template v-for="item in menu">
      <template v-if="item.children.length > 0">
        <el-submenu
          :index="item.id + ''"
          :key="item.id"
          :disabled="
            is_qywx_expire || ($store.state.disableClick ? true : false)
          "
        >
          <template slot="title">
            <i style="color: #fff" :class="item.extend_list.icon"></i>
            <span slot="title">{{ item.title | filterTitle }}</span>
          </template>
          <template v-for="(subItem, index) in item.children">
            <el-submenu
              v-if="subItem.children.length > 0"
              :index="subItem.extend_list.componentName"
              :key="subItem.id"
            >
              <template slot="title"
                ><i
                  v-if="subItem.extend_list.icon"
                  :class="subItem.extend_list.icon"
                ></i
                >{{ subItem.title | filterTitle }}</template
              >
              <el-menu-item
                v-for="(threeItem, i) in subItem.children"
                :key="i"
                :index="threeItem.extend_list.componentName | filterName"
                ><i
                  v-if="threeItem.extend_list.icon"
                  :class="threeItem.extend_list.icon"
                ></i
                >{{ threeItem.title | filterTitle }}</el-menu-item
              >
            </el-submenu>
            <el-menu-item
              v-else
              :key="index"
              :index="subItem.extend_list.componentName | filterName"
              @click="clickMenu(subItem.extend_list)"
            >
              <i :class="subItem.extend_list.icon"></i
              ><span>{{
                subItem.id == 37 && website_info.city_type == 2
                  ? "城市管理"
                  : subItem.title
              }}</span></el-menu-item
            >
          </template>
        </el-submenu>
      </template>
      <template v-else-if="item.pid === 0 && item.children.length == 0">
        <el-menu-item
          :index="item.extend_list.componentName | filterName"
          :is_auth="item.is_auth"
          :key="item.id"
          :disabled="
            is_qywx_expire || ($store.state.disableClick ? true : false)
          "
        >
          <i style="color: #fff" :class="item.extend_list.icon"></i
          ><span slot="title">{{ item.title | filterTitle }}</span>
        </el-menu-item>
      </template>
    </template>
  </el-menu>
</template>

<script>
import { mapActions, mapState } from "vuex";
let that = ''
export default {
  name: "slideNavs",
  data() {
    return {
      openedTab: [],
      indexPath: "crm_index",
    };
  },
  props: {
    menu: Array,
  },
  beforeCreate() {
    that = this;
  },
  created() {
    this.$store.commit("setSlideNavs", this.menu); // 存储menu
    this.getRoles();
  },
  computed: {
    ...mapState(["website_info", "is_qywx_expire"]),
  },
  filters: {
    filterName(val) {
      // console.log(val,"val")
      let name = val
      if (that) {

        if (val.indexOf("?") >= 0) {
          name = val + `&website_id=${that.website_info.website_id}`
        } else {
          name = val + `?website_id=${that.website_info.website_id}`
        }

      }
      return name

    },
    filterTitle(val) {
      let name = val

      name = name.replace(/\s/g, '')
      return name
    }
  },
  methods: {
    ...mapActions(["getRoles"]),
    // 解决点击两次选中问题
    openMenu(indexPath) {
      this.indexPath = indexPath;
    },
    clickMenu(e) {
      this.openedTab = this.$store.state.openedTab;
      // tabNum 为当前点击的列表项在openedTab中的index，若不存在则为-1
      let tabNum = this.openedTab.indexOf(e.componentName);
      if (tabNum === -1) {
        // 该标签当前没有打开
        // 将componentName加入到已打开标签页state.openedTab数组中
        this.$store.commit("addTab", {
          componentName: e.componentName,
        });
      } else {
        // 该标签是已经打开过的，需要激活此标签页
        this.$store.commit("changeTab", e.componentName);
      }
    },
    openClick(e) {
      console.log(e);
      // 170 企微工作台
      if (e == "170") {
        this.$goPath("/crm_customer_index");
      }
      // 公海展开
      if (e == "163") {
        // 默认跳转到导航里的第一个
        this.menu.map((item) => {
          if(item.id == e && item.children.length) {
            this.$goPath(item.children[0].extend_list.componentName);
          }
        })
        // this.$goPath("crm_customer_clue_list?type=0");
      }
      // 我的客户展开
      if (e == "178") {
        this.$goPath("crm_customer_my_list?type=2");
      }
    },
  },
};
</script>
<style lang="scss">
.el-submenu__title i {
  color: #fff; // 菜单文字颜色
}
</style>
<style lang="scss" scoped>
.el-menu-item.is-active {
  background-color: #409eff !important;
}
.el-menu-item i {
  // 菜单图标颜色
  color: #fff;
}
.munu-item {
  display: flex;
  align-items: center;
}
</style>
