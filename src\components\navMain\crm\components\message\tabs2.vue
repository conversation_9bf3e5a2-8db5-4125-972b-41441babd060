<template>
  <div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <el-table-column label="操作" fixed="right" v-slot="{ row }">
        <el-link type="primary" @click="onChangeEdit(row)">编辑</el-link>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      is_table_loading: false,
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        type: 1,
      },
    };
  },
  methods: {
    getDataList() {},
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onChangeEdit(row) {
      console.log(row);
    },
  },
};
</script>

<style></style>
