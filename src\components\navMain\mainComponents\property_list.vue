<template>
  <div class="list p-20">
    <el-container>
      <el-header class="div row">
        <myTopTips title="楼盘列表" :number="tableData.length">
          <el-tooltip placement="top" effect="light">
            <div class="add-build" slot="content">
              <el-button type="primary" plain @click="homeRecommend"
                >首页推荐</el-button
              >
              <el-button type="success" plain @click="hotList"
                >热门推荐</el-button
              >
              <el-button type="warning" plain @click="listRecommend"
                >列表推荐</el-button
              >
            </div>
            <el-button type="success" plain>推荐操作</el-button>
          </el-tooltip>
          <div class="add-build">
            <el-button
              v-if="website_info.website_mode_category == 2"
              icon="el-icon-plus"
              type="success"
              @click="$router.push('/custom_nav_link')"
              >导航</el-button
            >
            <el-button
              v-if="website_info.website_mode_category == 2"
              icon="el-icon-plus"
              type="success"
              @click="$router.push('/price_range_list')"
              >价格管理</el-button
            >
            <el-button
              v-if="$hasShow('添加楼盘')"
              icon="el-icon-plus"
              type="primary"
              @click="addbuild(website_info.website_mode_category)"
              >添加楼盘</el-button
            >
          </div></myTopTips
        >

        <div class="div row">
          <el-dropdown @command="handleCommand" style="margin-right: 10px">
            <span class="el-dropdown-link">
              {{ dropdown_title
              }}<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in dropdown_list"
                :key="index"
                :command="item.value"
                >{{ item.description }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
          <el-input
            v-model="params.name"
            placeholder="搜索相关楼盘名称"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>

      <div style="margin-bottom: 10px">
        <el-select
          clearable
          @change="regionOne"
          v-model="params.region_0"
          placeholder="请选择城市"
        >
          <el-option
            v-for="item in region_list_one"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-select
          @change="regionTwo"
          clearable
          v-model="params.region_1"
          v-if="region_list_two.length > 0"
          placeholder="请选择区域"
        >
          <el-option
            v-for="item in region_list_two"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <myTable
        v-loading="is_table_loading"
        :select="true"
        :table-list="tableData"
        :header="table_header"
        @selection-change="handleSelectionChange"
      ></myTable>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :currentPage="params.page"
            :pagesize="params.per_page"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import { mapState } from "vuex";
export default {
  name: "buildlist",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      // 搜索框数据
      input: "",
      tableData: [],
      multipleSelection: [],
      // 存放列表图片
      imgbox: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        region_0: "",
        region_1: "",
      },
      // 操作设置
      setup_box: [
        { name: "编辑楼盘", path: "setup_build", index: "1" },
        { name: "编辑户型", path: "setup_type", index: "2" },
        { name: "编辑画册", path: "setup_photo", index: "3" },
      ],
      dropdown_list: [
        { value: "all", description: "全部" },
        { value: "home_recommend", description: "首页推荐" },
        { value: "list_recommend", description: "列表推荐" },
        { value: "hot_search", description: "热搜推荐" },
      ],
      dropdown_title: "搜索条件",
      region_list: [],
      region_list_one: [],
      region_list_two: [],
      table_header: [
        {
          label: "ID",
          prop: "id",
        },
        {
          expand: "expand",
          label: "楼盘说明",
          width: "80",
          render: (h, data) => {
            return (
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="楼盘特色：">
                  <span domPropsInnerHTML={data.row.feature}></span>
                </el-form-item>
                <el-form-item label="楼盘地址：">
                  <span domPropsInnerHTML={data.row.full_build_address}></span>
                </el-form-item>
                <el-form-item label="售楼地址：">
                  <span
                    domPropsInnerHTML={data.row.full_sales_office_address}
                  ></span>
                </el-form-item>
                <el-form-item label="楼盘卖点：">
                  <span
                    domPropsInnerHTML={data.row.build_selling_points}
                  ></span>
                </el-form-item>
                <el-form-item label="价格说明：">
                  <span domPropsInnerHTML={data.row.price_description}></span>
                </el-form-item>
              </el-form>
            );
          },
        },
        {
          label: "名称",
          width: "340",
          render: (h, data) => {
            return (
              <div class="build-name">
                <img
                  style="height:50px;width:65px"
                  src={this.$imageFilter(
                    data.row.img || "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                />
                <div style="margin-left:10px" class="build-pp">
                  <p>{data.row.name}</p>
                  <p>
                    {data.row.home_recommend == 1 ? (
                      <el-tag class="tag-build" size="mini">
                        首页推荐
                      </el-tag>
                    ) : (
                      ""
                    )}
                    {data.row.hot_search == 1 ? (
                      <el-tag class="tag-build" size="mini" type="success">
                        热门推荐
                      </el-tag>
                    ) : (
                      ""
                    )}
                    {data.row.list_recommend == 1 ? (
                      <el-tag type="warning" class="tag-build" size="mini">
                        列表推荐
                      </el-tag>
                    ) : (
                      ""
                    )}
                  </p>
                </div>
              </div>
            );
          },
        },
        {
          label: "排序/关联项目",
          render: (h, data) => {
            return (
              <el-tag
                domPropsInnerHTML={
                  data.row.sort +
                  " / " +
                  (data.row.project_id > 0 ? data.row.project_name : "未关联")
                }
              ></el-tag>
            );
          },
        },
        {
          label: "列表排序",
          prop: "sort",
          render: (h, data) => {
            return (
              <div>
                <el-input
                  style="width:80px"
                  v-model={data.row.sort}
                  onChange={() => {
                    this.onChangeSort(data.row);
                  }}
                ></el-input>
              </div>
            );
          },
        },
        {
          prop: "build_status_name",
          label: "标识",
        },
        { prop: "region_0_name", label: "市" },
        {
          prop: "build_avg_price",
          label: "均价（元/平）",
          render: (h, data) => {
            return (
              <div>
                <el-input
                  style="width:80px"
                  v-model={data.row.build_avg_price}
                  onChange={() => {
                    this.onChangeSort(data.row);
                  }}
                ></el-input>
              </div>
            );
          },
        },
        {
          prop: "sales_office_phone",
          width: "140",
          label: "售楼热线",
          render: (h, data) => {
            return (
              <div>
                <el-input
                  style="width:120px"
                  v-model={data.row.sales_office_phone}
                  onChange={() => {
                    this.onChangeSort(data.row);
                  }}
                ></el-input>
              </div>
            );
          },
        },
        { prop: "newest_opening_time", label: "开盘时间" },
        {
          label: "操作",
          width: "280",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {
                  <el-dropdown class="oper">
                    {this.$hasShow("编辑楼盘") ? (
                      <el-button
                        type="success"
                        size="mini"
                        icon="el-icon-s-tools"
                        style="margin:5px;width:97px;border-radius:5px"
                      >
                        编辑
                      </el-button>
                    ) : (
                      ""
                    )}
                    {this.$hasShow("编辑楼盘") ? (
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          icon="el-icon-edit"
                          nativeOnClick={() => {
                            this.setupBuild(data.row);
                          }}
                        >
                          编辑楼盘
                        </el-dropdown-item>
                        <el-dropdown-item
                          icon="el-icon-edit"
                          nativeOnClick={() => {
                            this.setupType(data.row);
                          }}
                        >
                          编辑户型
                        </el-dropdown-item>
                        <el-dropdown-item
                          icon="el-icon-upload2"
                          nativeOnClick={() => {
                            this.uploadFile(data.row);
                          }}
                        >
                          上传附件
                        </el-dropdown-item>
                        <el-dropdown-item
                          icon="el-icon-edit"
                          nativeOnClick={() => {
                            this.setupPhoto(data.row);
                          }}
                        >
                          编辑画册
                        </el-dropdown-item>
                        <el-dropdown-item
                          icon="el-icon-edit"
                          nativeOnClick={() => {
                            this.setupDynamic(0, data.row);
                          }}
                        >
                          {this.website_info.website_mode_category === 1
                            ? "管理资讯"
                            : "管理动态"}
                        </el-dropdown-item>
                        {data.row.project_id == 0 ? (
                          <el-dropdown-item
                            icon="el-icon-plus"
                            nativeOnClick={() => {
                              this.toLink(data.row);
                            }}
                          >
                            关联项目
                          </el-dropdown-item>
                        ) : (
                          ""
                        )}
                      </el-dropdown-menu>
                    ) : (
                      ""
                    )}
                  </el-dropdown>
                }
                {this.$hasShow("取消推荐") ? (
                  <el-dropdown>
                    <el-button
                      icon="el-icon-s-tools"
                      size="mini"
                      type="primary"
                      style="margin:5px;width:97px;border-radius:5px"
                    >
                      取消推荐
                    </el-button>
                    {this.$hasShow("取消推荐") ? (
                      <el-dropdown-menu slot="dropdown">
                        {data.row.home_recommend == 1 ? (
                          <el-dropdown-item
                            icon="el-icon-close"
                            nativeOnClick={() => {
                              this.cancelHomeRecommend(data.row);
                            }}
                          >
                            取消首页推荐
                          </el-dropdown-item>
                        ) : (
                          ""
                        )}
                        {data.row.list_recommend == 1 ? (
                          <el-dropdown-item
                            icon="el-icon-close"
                            nativeOnClick={() => {
                              this.cancelListRecommend(data.row);
                            }}
                          >
                            取消列表推荐
                          </el-dropdown-item>
                        ) : (
                          ""
                        )}
                        {data.row.hot_search == 1 ? (
                          <el-dropdown-item
                            icon="el-icon-close"
                            nativeOnClick={() => {
                              this.cancelHotRecommend(data.row);
                            }}
                          >
                            取消热搜推荐
                          </el-dropdown-item>
                        ) : (
                          ""
                        )}
                      </el-dropdown-menu>
                    ) : (
                      ""
                    )}
                  </el-dropdown>
                ) : (
                  ""
                )}
                {this.website_info.website_mode_category !== 0 &&
                this.$hasShow("管理楼号") ? (
                  <el-button
                    size="mini"
                    type="primary"
                    style="margin:5px;width:97px;border-radius:5px"
                    onClick={() => {
                      this.handleFloor(0, data.row);
                    }}
                  >
                    管理楼号
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除楼盘") ? (
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    style="margin:5px;width:97px;border-radius:5px"
                    onClick={() => {
                      this.handleDelete(0, data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("报名列表") ||
                this.website_info.website_mode_category !== 0 ? (
                  <el-button
                    size="mini"
                    type="success"
                    style="margin:5px;width:97px;border-radius:5px"
                    onClick={() => {
                      this.handleApply(data.row);
                    }}
                  >
                    报名列表
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("报备列表") && data.row.project_id ? (
                  <el-button
                    size="mini"
                    type="primary"
                    style="margin:5px;width:97px;border-radius:5px;background:#409eff"
                    onClick={() => {
                      this.handleReport(data.row);
                    }}
                  >
                    报备列表
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("预售管理") &&
                this.website_info.website_mode_category == 2 ? (
                  <el-button
                    size="mini"
                    type="success"
                    onClick={() => {
                      this.handlePreSale(data.row);
                    }}
                  >
                    预售管理
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
    this.getRegion();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      if (!this.params.home_recommend) {
        delete this.params.home_recommend;
      }
      if (!this.params.list_recommend) {
        delete this.params.list_recommend;
      }
      if (!this.params.hot_search) {
        delete this.params.hot_search;
      }
      if (!this.params.region_1) {
        delete this.params.region_1;
      }
      if (!this.params.region_0) {
        delete this.params.region_0;
      }
      this.is_table_loading = true;
      this.$http.queryBuild({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        this.tableData = res.data.data;
        this.imgbox = res.data.data.img;
        this.params.page = res.data.current_page;
        this.params.total = res.data.total;
        this.params.row = res.data.per_page;
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    // 搜索楼盘
    search() {
      this.params.page = 1;
      this.getDataList();
    },
    // 搜索下拉
    handleCommand(command) {
      switch (command) {
        case "home_recommend":
          this.params.home_recommend = 1;
          this.params.list_recommend = "";
          this.params.hot_search = "";
          this.dropdown_title = "首页推荐";
          break;
        case "list_recommend":
          this.params.home_recommend = "";
          this.params.list_recommend = 1;
          this.params.hot_search = "";
          this.dropdown_title = "列表推荐";

          break;
        case "hot_search":
          this.params.hot_search = 1;
          this.params.list_recommend = "";
          this.params.home_recommend = "";
          this.dropdown_title = "热搜推荐";

          break;
        case "all":
          this.params.hot_search = "";
          this.params.list_recommend = "";
          this.params.home_recommend = "";
          this.dropdown_title = "全部";
          break;
      }
      this.params.page = 1;
      this.getDataList();
    },
    // 点击进入添加楼盘界面
    addbuild(mode) {
      // mode = 1 单楼盘
      if (mode === 1 && this.tableData.length >= 1) {
        this.$message({
          message: "您已添加楼盘",
          type: "error",
        });
      } else {
        this.$goPath("/addbuild");
      }
    },
    // 操作
    setupBuild(row) {
      this.$goPath(`/addbuild?build_id=${row.id}`);
    },
    setupType(row) {
      this.$goPath(`/setup_type?id=${row.id}`);
    },
    setupPhoto(row) {
      this.$goPath(`/setup_photo?id=${row.id}`);
    },
    toLink(row) {
      this.$goPath(`/updata_project?name=${row.name}`);
    },
    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该楼盘, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteBuild(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    homeRecommend() {
      if (this.multipleSelection.length > 0) {
        let arrHome = this.multipleSelection.map((item) => {
          return item.id;
        });
        this.$http
          .setHomeRecommend({
            ids: arrHome,
            value: 1,
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "推荐成功",
                type: "success",
              });
              this.getDataList();
            }
          });
      } else {
        this.$message({
          message: "选择项目推荐",
          type: "error",
        });
        return;
      }
    },
    setupDynamic(index, row) {
      if (this.website_info.website_mode_category === 1) {
        this.$goPath("management_page");
      } else {
        this.$goPath(`/dynamic_list?id=${row.id}`);
      }
    },
    listRecommend() {
      if (this.multipleSelection.length > 0) {
        let arrList = this.multipleSelection.map((item) => {
          return item.id;
        });
        this.$http
          .setListRecommend({
            ids: arrList,
            value: 1,
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "推荐成功",
                type: "success",
              });
              this.getDataList();
            }
          });
      } else {
        this.$message({
          message: "选择项目推荐",
          type: "error",
        });
        return;
      }
    },
    hotList() {
      if (this.multipleSelection.length > 0) {
        let arrHot = this.multipleSelection.map((item) => {
          return item.id;
        });
        this.$http
          .setHotRecommend({
            ids: arrHot,
            value: 1,
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "推荐成功",
                type: "success",
              });
              this.getDataList();
            }
          });
      } else {
        this.$message({
          message: "选择项目推荐",
          type: "error",
        });
        return;
      }
    },
    cancelHomeRecommend(row) {
      let arrHome = [];
      arrHome.push(row.id);
      this.$http
        .setHomeRecommend({
          ids: arrHome,
          value: 0,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "取消成功",
              type: "success",
            });
            this.getDataList();
          }
        });
    },
    cancelListRecommend(row) {
      let arrList = [];
      arrList.push(row.id);
      this.$http
        .setListRecommend({
          ids: arrList,
          value: 0,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "取消成功",
              type: "success",
            });
            this.getDataList();
          }
        });
    },
    cancelHotRecommend(row) {
      let arrHot = [];
      arrHot.push(row.id);
      this.$http
        .setHotRecommend({
          ids: arrHot,
          value: 0,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "取消成功",
              type: "success",
            });
            this.getDataList();
          }
        });
    },
    handleApply(row) {
      this.$goPath(`/apply_list?build_id=${row.id}`);
    },
    handleReport(row) {
      this.$goPath(`/report_customer?project_id=${row.project_id}`);
    },
    handleFloor(index, row) {
      this.$goPath(`/floor_list?build_id=${row.id}`);
    },
    handlePreSale(row) {
      this.$goPath(`/presell_list?build_id=${row.id}`);
    },
    // 上传附件
    uploadFile(row) {
      this.$goPath(`/file_list?build_id=${row.id}`);
    },
    // 获取城市
    getRegion() {
      this.$http.getRegionList().then((res) => {
        if (res.status === 200) {
          this.region_list = this.$sortPro(res.data, ["pid"]);
          this.region_list.map((item) => {
            if (item.pid === 0) {
              this.region_list_one = item.children;
            }
          });
        }
      });
    },
    regionOne(e) {
      if (!e) {
        // 无参数清空数据
        this.params.region_0 = "";
        this.params.region_1 = "";
        this.region_list_two = [];
        this.getDataList();
        return;
      }
      this.params.region_0 = e;
      this.getDataList();
      this.region_list_two = [];
      this.region_list.map((item) => {
        if (item.pid === 0) {
          this.region_list_one = item.children;
        } else {
          if (item.pid === e) {
            this.region_list_two = item.children;
            this.params.region_1 = item.children[0].id;
          }
        }
      });
    },
    regionTwo(e) {
      this.params.region_1 = e;
      this.getDataList();
    },
    onChangeSort(row) {
      let form = {
        id: row.id,
        sort: row.sort,
        build_avg_price: row.build_avg_price,
        sales_office_phone: row.sales_office_phone,
      };
      this.$http.setBuildSimpleData(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
        }
      });
    },
  },
  computed: {
    ...mapState(["website_info"]),
  },
};
</script>

<style lang="scss" scoped>
.build-name {
  display: flex;
}
.build-pp {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
}
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.oper {
  color: #409eff;
}
.el-tag.tag-build {
  margin-right: 5px;
}
.el-button {
  border-radius: 10px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
</style>
