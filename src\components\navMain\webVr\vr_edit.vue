<template>
    <div class="vr-edit">
        <div class="setup flex-row">
            <div class="Specific">
                <div class="Basic_Settings flex-row">
                    <div>
                        <span>基本设置</span>
                        <div class=" front_cover">
                            <img :src="Propertydata.thumb_path" alt="" style="width: 100%;height: 100%;">
                        </div>
                    </div>
                    <div style="margin-left: 20px;width: 40%;">
                        <el-input type="text" placeholder="填写标题" v-model="Propertydata.name" maxlength="50" show-word-limit
                            style="margin-top: 40px;"></el-input>
                        <div class="introduce">
                            <el-input v-model="Propertydata.profile" placeholder="填写介绍" type="textarea"
                                style="margin-top: 20px;"></el-input>
                        </div>
                        <div style="margin-top: 20px;">
                            <el-form :model="Propertydata" label-width="80px">
                                <el-form-item class="PhotoID">
                                    <el-select v-model="panoramadata.tag_id" multiple placeholder="请选择标签"
                                        style="width: 115%;">
                                        <el-option v-for="item in VrWorksLabel_list" :key="item.id" :label="item.name"
                                            :value="item.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                </div>
                <div class="flex-row">
                    <div>
                        <div>
                            <span>全景图片</span>
                        </div>
                        <div style="margin-top: 10px;">
                            <el-button type="primary" size="small" style="width: 88px;" @click="vrpanorama">全景图片</el-button>
                        </div>

                    </div>
                    <div class="housing">
                        <div>
                            <span>绑定房源</span>
                        </div>
                        <div style="margin-top: 10px;">
                            <el-button type="primary" size="small" style="width: 88px;"
                                @click="Bind_listings">绑定</el-button>
                            <el-tag type="warning" style="margin: 0px 20px;" v-show="showhousing">{{ BindLabel }}</el-tag>
                        </div>
                    </div>
                </div>
                <div class="GlobalSettings">
                    <div>
                        <span>全局设置</span>
                    </div>
                    <div style="min-width: 700px;">
                        <div style="margin: 10px 0px;">
                            <div class="create-works">
                                <el-button type="warning" size="small" @click="visualediting">
                                    可视化编辑跳转
                                </el-button>
                                <el-button type="primary" size="small" @click="createVrWorks">
                                    背景音乐设置
                                </el-button>
                                <el-button type="primary" size="small" @click="Explanation">
                                    音频解说设置
                                </el-button>
                                <el-button type="primary" size="small" @click="Rightmenu">
                                    设置右键菜单
                                </el-button>
                                <el-button type="primary" size="small" @click="accesspassword">
                                    设置访问密码
                                </el-button>
                                <el-button type="primary" size="small" @click="vrtopslogan">
                                    设置顶部广告语
                                </el-button>
                                <el-button type="primary" size="small" @click="OpenReminder">
                                    开场提示设置
                                </el-button>
                                <el-button type="primary" size="small" @click="Businesscard">
                                    设置企业名片
                                </el-button>
                                <el-button type="primary" size="small" @click="OpeningPicture">
                                    设置开场过渡图
                                </el-button>
                            </div>
                        </div>
                        <!-- <div> -->
                        <div class="create-works">
                            <el-button type="primary" size="small" @click="navigationphone">
                                链接电话与导航设置
                            </el-button>
                            <el-button type="primary" size="small" @click="customuser">
                                设置自定义logo、作者名
                            </el-button>
                            <el-button type="primary" size="small" @click="SkyGroundMask">
                                遮罩设置
                            </el-button>
                        </div>
                        <!-- 背景音乐 -->
                        <div>
                            <el-dialog title="背景音乐" :visible.sync="dialogAddLabel" width="580px"
                                :close-on-click-modal="false">
                                <div style="display:flex;justify-content: space-around;">
                                    <div>
                                        <el-radio v-model="worksLabelpipedmusic.isWhole" label="1" border
                                            size="medium">全局</el-radio>
                                        <el-radio v-model="worksLabelpipedmusic.isWhole" label="0" border
                                            size="medium">单场</el-radio>
                                    </div>
                                    <div>
                                        <el-checkbox v-model="sceneSettings.useSpeech" true-label="1"
                                            false-label="0">是否上传音乐</el-checkbox>
                                    </div>
                                </div>
                                <div v-show="sceneSettings.useSpeech == 1">
                                    <el-form :model="form_AddLabel" label-width="130px">
                                        <el-form-item label="音频标题" style="margin-top:20px">
                                            <el-input v-model="sceneSettings.mediaTitle" style="width: 240px;"
                                                placeholder="请输入音频标题"></el-input>
                                        </el-form-item>
                                        <el-form-item label="背景音乐" class="PhotoID">
                                            <el-select v-model="sceneSettings.mediaUrl" placeholder="请选择"
                                                style="width: 240px;">
                                                <el-option v-for="item in pipedmusic_list" :key="item.id"
                                                    :label="item.media_name" :value="item.absolutelocation">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-form>
                                </div>
                                <span slot="footer" class="dialog-footer EditPhoto-footer">
                                    <el-button @click="dialogAddLabel = false">取 消</el-button>
                                    <el-button type="primary" @click="Musicupload">确定上传</el-button>
                                </span>
                            </el-dialog>
                        </div>
                        <!-- 解说音频 -->
                        <div>
                            <el-dialog title="解说音频" :visible.sync="audio" width="580px" :close-on-click-modal="false">
                                <div style="display:flex;justify-content: space-around;">
                                    <div>
                                        <el-radio v-model="worksLabelmusic.isWhole" label="1" border
                                            size="medium">全局</el-radio>
                                        <el-radio v-model="worksLabelmusic.isWhole" label="0" border
                                            size="medium">单场</el-radio>
                                    </div>
                                    <div>
                                        <el-checkbox v-model="sceneSettingsmusic.useSpeech" true-label="1"
                                            false-label="0">是否上传解说音频</el-checkbox>
                                    </div>
                                </div>
                                <div v-show="sceneSettingsmusic.useSpeech == 1">
                                    <el-form :model="form_AddLabel" label-width="130px">
                                        <el-form-item label="音频标题" style="margin-top:20px">
                                            <el-input v-model="sceneSettingsmusic.mediaTitle" style="width: 240px;"
                                                placeholder="请输入音频标题"></el-input>
                                        </el-form-item>
                                        <el-form-item label="解说音频" class="PhotoID">
                                            <el-select v-model="sceneSettingsmusic.mediaUrl" placeholder="请选择"
                                                style="width: 240px;">
                                                <el-option v-for="item in pipedmusic_list" :key="item.id"
                                                    :label="item.media_name" :value="item.absolutelocation">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-form>
                                </div>
                                <span slot="footer" class="dialog-footer EditPhoto-footer">
                                    <el-button @click="audio = false">取 消</el-button>
                                    <el-button type="primary" @click="confirmAddLabel">确定上传</el-button>
                                </span>
                            </el-dialog>
                        </div>
                        <!-- 右键设置 -->
                        <div>
                            <el-dialog title="右键设置" :visible.sync="Right_settings" width="680px"
                                :close-on-click-modal="false">
                                <div style="margin-bottom:10px;margin-right:30px; text-align:right">
                                    <el-button @click="Add_Right">可自定义三个</el-button>
                                </div>
                                <div v-for="(item, index) in linkSettings" :key="index">
                                    <div style="margin-left:50px;margin-bottom:20px">
                                        名称:<el-input v-model="item.title" placeholder="请输入名称"
                                            style="width: 450px;margin-left:20px"></el-input>
                                    </div>
                                    <div style="margin-left:50px;margin-bottom:20px">
                                        链接:<el-input v-model="item.content" placeholder="请输入链接地址与电话"
                                            style="width: 450px;margin-left:20px;margin-right:10px"></el-input>
                                        <el-button @click="del_Right(item)">删除</el-button>
                                    </div>
                                </div>
                                <span slot="footer" class="dialog-footer EditPhoto-footer">
                                    <el-button @click="Right_settings = false">取 消</el-button>
                                    <el-button type="primary" @click="Right_clickupload">确定上传</el-button>
                                </span>
                            </el-dialog>
                        </div>
                        <!-- 访问密码 -->
                        <div>
                            <el-dialog title="设置访问密码" :visible.sync="accpassword" width="500px"
                                :close-on-click-modal="false">
                                <div>
                                    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="80px"
                                        class="demo-ruleForm">
                                        <el-form-item label="密码" prop="pass">
                                            <el-input type="password" show-password v-model="ruleForm.privacy_password"
                                                autocomplete="off" style="width: 350px;"></el-input>
                                        </el-form-item>
                                    </el-form>
                                </div>
                                <span slot="footer" class="dialog-footer EditPhoto-footer">
                                    <el-button @click="accpassword = false">取 消</el-button>
                                    <el-button type="primary" @click="upload_password">确定上传</el-button>
                                </span>
                            </el-dialog>
                        </div>
                        <!-- 顶部广告语 -->
                        <div class="Top-slogan" v-show="Topslogan">
                            <el-dialog title="设置顶部广告语" :visible.sync="Topslogan" width="500px"
                                :close-on-click-modal="false">
                                <div>
                                    <div style="margin-left:35px;color:#E6A23C">广告语不能超过255个字符,若不想使用该功能，请保持内容为空</div>
                                    <div style="margin-top:20px">
                                        <el-form ref="form" :model="formTopslogan" label-width="70px">
                                            <el-form-item label="内容">
                                                <el-input v-model="formTopslogan.adcontent" placeholder="请输入内容"
                                                    style="width: 350px;"></el-input>
                                            </el-form-item></el-form>
                                    </div>
                                    <div style="margin-left:85px;">
                                        <el-checkbox v-model="formTopslogan.allow_sys" true-label="1"
                                            false-label="0">允许显示系统广告</el-checkbox>
                                        <el-checkbox v-model="formTopslogan.show" true-label="1"
                                            false-label="0">是否有广告内容</el-checkbox>
                                    </div>
                                </div>
                                <span slot="footer" class="dialog-footer EditPhoto-footer">
                                    <el-button @click="Topslogan = false">取 消</el-button>
                                    <el-button type="primary" @click="access_advertisement">确定上传</el-button>
                                </span>
                            </el-dialog>
                        </div>
                        <!-- 绑定房源 -->
                        <div>
                            <el-dialog title="绑定房源" :visible.sync="Bindlistings" width="500px"
                                :close-on-click-modal="false">
                                <el-dialog width="50%" :title="webVrSeach.type == 1 ? 'T+房源' : '腾房云'"
                                    :visible.sync="innerVisible" append-to-body>
                                    <div class="">
                                        <el-table :data="tableData" height="600" border style="width: 100%"
                                            @selection-change="Selectedhome">
                                            <el-table-column type="selection">
                                            </el-table-column>
                                            <el-table-column prop="title" label="房源" align="center">
                                            </el-table-column>
                                            <el-table-column prop="pic" label="图片" align="center">
                                                <template slot-scope="scope">
                                                    <div style="width: 78px;height: 78px;margin: 0 auto;">
                                                        <img :src="scope.row.pic" alt="" style="width: 100%;height: 100%;">
                                                    </div>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="trade_type" label="类型" align="center">
                                                <template slot-scope="scope">
                                                    <el-tag :type="scope.row.trade_type === 1 ? 'danger' : ''">{{
                                                        scope.row.trade_type === 1 ? "出售" : "出租" }}</el-tag>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                    <div class="block">
                                        <el-pagination layout="prev, pager, next" prev-click next-click
                                            :page-size="webVrSeach.per_page" @current-change="page">
                                        </el-pagination>
                                    </div>

                                </el-dialog>
                                <div class="housing-resources flex-row">
                                    <div>
                                        <span style="cursor: pointer;" @click="listingsT">T+房源</span>
                                        <div id="Selected" :class="selected"></div>
                                    </div>
                                    <div>
                                        <span style="cursor: pointer;" @click="tengfangyun">腾房云</span>
                                        <div id="Selected1" :class="selected1"></div>
                                    </div>
                                </div>
                                <div class="ferret-about" v-show="show">
                                    搜索房源:<el-input placeholder="请输入内容" v-model="inputhouse" clearable
                                        style="width: 350px;margin-left: 10px;" @focus="housingresouces">
                                    </el-input>
                                </div>
                                <div class="ferret-about" v-show="show1">
                                    搜索房源:<el-input placeholder="请输入内容" v-model="inputhouse" clearable
                                        style="width: 350px;margin-left: 10px;" @focus="housingresouces">
                                    </el-input>
                                </div>
                                <span slot="footer" class="dialog-footer EditPhoto-footer">
                                    <el-button @click="Bindlistings = false">取 消</el-button>
                                    <el-button type="primary" @click="bindlistings">绑定</el-button>
                                </span>
                            </el-dialog>
                        </div>
                        <!-- </div> -->
                    </div>
                </div>
                <div style="margin-top: 20px ">
                    <span>全景设置</span>
                </div>
                <div class="Gglobal-main flex-row">
                    <div>
                        <span class="Gglobal-name" style="padding-left: 0px;">发布全景</span>
                        <el-switch v-model="panoramadata.flag_publish" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">场景选择</span>
                        <el-switch v-model="panoramadata.scenechoose" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">自动旋转</span>
                        <el-switch v-model="panoramadata.autorotate" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">小行星开场</span>
                        <el-switch v-model="panoramadata.littleplanet" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">允许打赏</span>
                        <el-switch v-model="panoramadata.reward" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">开启陀螺仪</span>
                        <el-switch v-model="panoramadata.gyro" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <!-- <div style="margin-top: 20px;"> -->
                        <span class="Gglobal-name">显示说一说</span>
                        <el-switch v-model="panoramadata.comment" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">足迹</span>
                        <el-switch v-model="panoramadata.footmark" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">隐藏作者名</span>
                        <el-switch v-model="panoramadata.hideuser_flag" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <span class="Gglobal-name">隐藏LOGO</span>
                        <el-switch v-model="panoramadata.hidelogo_flag" active-value="1" inactive-value="0" :width="32">
                        </el-switch>
                        <div style="margin-top: 20px;">
                            <span class="Gglobal-name" style="padding-left: 0px;">隐藏人气</span>
                            <el-switch v-model="panoramadata.hideviewnum_flag" active-value="1" inactive-value="0"
                                :width="32">
                            </el-switch>
                            <span class="Gglobal-name">隐藏VR眼睛</span>
                            <el-switch v-model="panoramadata.hidevrglasses_flag" active-value="1" inactive-value="0"
                                :width="32">
                            </el-switch>
                            <!-- </div> -->
                            <span class="Gglobal-name">隐藏简介</span>
                            <el-switch v-model="panoramadata.hideprofile_flag" active-value="1" inactive-value="0"
                                :width="32">
                            </el-switch>
                            <span class="Gglobal-name">隐藏点赞</span>
                            <el-switch v-model="panoramadata.hidepraise_flag" active-value="1" inactive-value="0"
                                :width="32">
                            </el-switch>
                            <span class="Gglobal-name">隐藏分享</span>
                            <el-switch v-model="panoramadata.hideshare_flag" active-value="1" inactive-value="0"
                                :width="32">
                            </el-switch>
                            <span class="Gglobal-name">允许推荐</span>
                            <el-switch v-model="panoramadata.flag_allowed_recomm" active-value="1" inactive-value="0"
                                :width="32">
                            </el-switch>
                        </div>
                    </div>
                </div>
                <div class="Final-upload">
                    <el-button type="warning" size="medium" @click="setpanorama">确认上传</el-button>
                </div>
            </div>
            <el-drawer :visible.sync="drawer" :direction="direction" :before-close="handleClose">
                <!-- <span>我来啦!</span> -->
                <div class="Specificset">
                    <div class="Specificset-one">
                        <!-- 全景图（引入外部子组件） -->
                        <div style="margin-top: 10px;" v-show="vr_photo && Propertydata.name != undefined">
                            <div style="margin-bottom: 3px;">全景图片</div>
                            <div class="material_list">
                                <panorama_Material :forData_list="forData_list" @Vr_photoid="handleMessage">
                                </panorama_Material>
                            </div>
                            <div class="vrphoto">
                                <el-button type="warning" size="small" style="width: 100px;"
                                    @click="handle">确定上传</el-button>
                            </div>
                        </div>


                        <!-- 开场提示 -->
                        <div class="Opening-Reminder" v-show="vrReminder">
                            <div>开场提示</div>
                            <div class="prompt" v-show="startradio.isDefault == 1">
                                <img src="./vrimage/vr.png" alt="">
                            </div>
                            <div class="OpenRadio">
                                <el-radio v-model="startradio.isDefault" label="1">使用默认开场提示</el-radio>
                                <el-radio v-model="startradio.isDefault" label="0">不使用开场提示</el-radio>
                            </div>
                            <div style="text-align:center;margin: 10px;" v-show="startradio.useAlert == 1">
                                <div style="margin-right:260px;font-size: 15px;
                        color: #E6A23C;">自定义图片:</div>
                                <el-upload class="avatar-uploader" action="/api/common/file/upload/admin?category=401"
                                    :headers="upload_headers" :show-file-list="false" accept=".jpg,.png,.jepg"
                                    :on-success="startradioSuccess">
                                    <img v-if="startradio.imgPath" :src="startradio.imgPath" class="avatar">
                                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </div>
                            <div style="margin-left:150px;">
                                <el-checkbox v-model="startradio.useAlert" true-label="1"
                                    false-label="0">是否使用自定义图片</el-checkbox>
                            </div>
                            <div style="text-align:right;">
                                <el-button type="warning" style="width: 100px;" @click="VReminder">确定上传</el-button>
                            </div>
                        </div>
                        <!-- 自定义logo/作者名 -->
                        <div v-show="customlogo">
                            <div style="margin-top:10px;">自定义Logo/作者名</div>
                            <div class="custom-logo">
                                <div class="Upload-size" style="margin: 30px;">图片大小建议: 120 x 120</div>
                                <div>
                                    <el-upload class="avatar-uploader" action="/api/common/file/upload/admin?category=401"
                                        :headers="upload_headers" :show-file-list="false" accept=".jpg,.png,.jepg"
                                        :on-success="logoauthorSuccess">
                                        <img v-if="formlogo.logoImgPath" :src="formlogo.logoImgPath" class="avatar">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                </div>
                                <div style="margin-top:30px ;">
                                    <div class="Upload-size">选填:</div>
                                    <el-form ref="form" :model="formlogo" label-width="100px">
                                        <el-form-item label="图片链接地址:">
                                            <el-input v-model="formlogo.logoLink" placeholder="请输入链接地址"
                                                style="width: 300px;"></el-input>
                                        </el-form-item>
                                        <div class="Upload-size" style="text-align:center;margin-bottom: 10px;">
                                            作者名称不能超过20个字符
                                        </div>
                                        <el-form-item label="作者姓名:">
                                            <el-input v-model="formlogo.uers" placeholder="请输入作者姓名"
                                                style="width: 300px;"></el-input>
                                        </el-form-item>
                                    </el-form>
                                    <div>
                                        <el-checkbox v-model="formlogo.useCustomLogo" true-label="1"
                                            false-label="0">是否选择自定义图片</el-checkbox>
                                    </div>
                                </div>
                                <div class="finish">
                                    <el-button type="warning" style="width: 150px;" @click="Authorupload">确定上传</el-button>
                                </div>
                            </div>
                        </div>
                        <!-- 企业名片 -->
                        <div v-show="Business">
                            <div style="margin-top:10px;">企业名片</div>
                            <div class="Business-card">
                                <div>
                                    <div style="margin-top:30px ;">
                                        <el-form ref="form" :model="formcard" label-width="80px">
                                            <el-form-item label="公司logo: ">
                                                <el-input v-model="formcard.logo" placeholder="请输入公司logo"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                            <el-form-item label="公司名称: ">
                                                <el-input v-model="formcard.company" placeholder="请输入公司名称"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                            <el-form-item label="E-mail: ">
                                                <el-input v-model="formcard.email" placeholder="请输入邮箱地址"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                            <el-form-item label="微信号: ">
                                                <el-input v-model="formcard.wx" placeholder="请输入微信公众号"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                            <el-form-item label="手机/电话: ">
                                                <el-input v-model="formcard.phone" placeholder="请输入手机/电话号码"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                    <div>
                                        <el-switch v-model="formcard.flag_vcard" inactive-text="按年付费">
                                        </el-switch>
                                    </div>
                                </div>
                                <div>

                                    <div style="display:flex;margin-top:29px;">
                                        <div>公众号二维码:</div>
                                        <div style="margin-left:10px;">
                                            <el-upload class="avatar-uploader"
                                                action="/api/common/file/upload/admin?category=401"
                                                :headers="upload_headers" :show-file-list="false" accept=".jpg,.png,.jepg"
                                                :on-success="BusinessSuccess">
                                                <img v-if="formcard.wx_logo" :src="formcard.wx_logo" class="avatar">
                                                <i v-else class="el-icon-plus avatar-uploader-icon"
                                                    style="width: 150px;height: 150px;"></i>
                                            </el-upload>
                                        </div>
                                    </div>
                                    <div style="margin-top:30px ;">
                                        <el-form ref="form" :model="formcard" label-width="80px">

                                            <el-form-item label="公司地址: ">
                                                <el-input v-model="formcard.addr" placeholder="请输入公司地址"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                            <el-form-item label="QQ: ">
                                                <el-input v-model="formcard.qq" placeholder="请输入QQ号码"
                                                    style="width: 180px;"></el-input>
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                    <div style="margin-left:120px;margin-top:110px;">
                                        <el-button type="warning" style="width: 150px;"
                                            @click="EnterprisesCard">确定上传</el-button>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!-- 开场过渡图 -->
                        <div v-show="startbegin">
                            <div style="margin-top:10px;">开场过渡图</div>
                            <div class="start-begin">
                                <div style="margin: 30px;font-size: 15px;color: #E6A23C;">图片大小建议:400 x 400</div>
                                <div>
                                    <el-upload class="avatar-uploader" action="/api/common/file/upload/admin?category=401"
                                        :headers="upload_headers" :show-file-list="false" accept=".jpg,.png,.jepg"
                                        :on-success="startSuccess">
                                        <img v-if="formstart.imgPath" :src="formstart.imgPath" class="avatar">
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                </div>
                                <div style="margin-top:25px; ;">
                                    <el-form ref="form" :model="formstart" label-width="80px">
                                        <el-form-item label="背景字体:">
                                            <el-input v-model="formstart.bgcolor" placeholder="例：#0093ff"
                                                style="width: 300px;"></el-input>
                                        </el-form-item>
                                    </el-form>
                                </div>
                                <div style="margin-top:20px;">
                                    <span style="margin-right:60px;">是否开启：</span>
                                    <el-radio v-model="formstart.isopen" label="1" style="margin-right:90px;">开启</el-radio>
                                    <el-radio v-model="formstart.isopen" label="0">关闭</el-radio>
                                </div>
                                <div class="finish">
                                    <el-button type="warning" style="width: 150px;"
                                        @click="Confirmuploadstart">确定上传</el-button>
                                </div>
                            </div>
                        </div>
                        <!-- 遮罩设置 -->
                        <div v-show="vrMask" class="vrMaskradio">
                            <div style="margin:10px 10px 20px;">天空地面遮罩</div>
                            <div style="margin-left:120px;" class="radio">
                                <el-radio v-model="formvrMaskr.isWhole" label="true">全局设置</el-radio>
                                <el-radio v-model="formvrMaskr.isWhole" label="false"
                                    style="margin-left:150px;">单场设置</el-radio>
                            </div>
                            <div v-show="formvrMaskr.isWhole != 2">
                                <div style="margin:40px 90px 20px;">
                                    <el-radio v-model="formvrMaskr.useShade" label="false">不使用遮罩</el-radio>
                                    <el-radio v-model="formvrMaskr.useShade" label="true"
                                        style="margin-left:150px;">使用全景云遮罩</el-radio>
                                </div>
                                <div style="margin:40px 50px 20px; display:flex;" v-show="formvrMaskr.useShade == 'true'">
                                    <div>
                                        <div>
                                            <el-form ref="form" :model="formvrMaskr" label-width="74px">
                                                <el-form-item label="公司名称: ">
                                                    <el-input v-model="formvrMaskr.shadeSetting.imgName"
                                                        placeholder="请输入公司名称" style="width: 180px;"></el-input>
                                                </el-form-item>
                                            </el-form>
                                        </div>
                                        <div style="margin:10px;">选择照片</div>
                                        <el-upload class="avatar-uploader"
                                            action="/api/common/file/upload/admin?category=401" :headers="upload_headers"
                                            :show-file-list="false" :on-success="vrradioSuccess">
                                            <img v-if="formvrMaskr.shadeSetting.imgPath"
                                                :src="formvrMaskr.shadeSetting.imgPath" class="avatar">
                                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                        </el-upload>
                                    </div>
                                    <div style="margin-top:100px;">
                                        <div style="margin:20px">全景云补地</div>
                                        <el-radio v-model="formvrMaskr.shadeSetting.location" label="0">天空</el-radio>
                                        <el-radio v-model="formvrMaskr.shadeSetting.location" label="1">地面</el-radio>
                                    </div>
                                </div>
                            </div>
                            <div class="finish">
                                <el-button type="warning" style="width: 150px;" @click="UploadMask">确定上传</el-button>
                            </div>
                        </div>
                        <!-- 链接，电话与导航 -->
                        <div v-show="vrnavigation" class="Link-navigation ">
                            <div>
                                <vrnavigation :Propertydata="Propertydata"></vrnavigation>
                            </div>
                        </div>
                    </div>
                </div>
            </el-drawer>
        </div>
    </div>
</template> 
<script>
import panorama_Material from "@/components/navMain/webVr/components/vr_imagelist.vue"
import vrnavigation from "@/components/navMain/webVr/components/vrnavigation.vue"
import config from "@/utils/config.js";
export default {
    components: {
        panorama_Material,
        vrnavigation,
    },

    data() {
        var validatePass = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入密码'));
            }
        };
        return {
            drawer: false,
            direction: 'rtl',
            Propertydata: {}, //vr的具体信息
            imgs_works_id: [],//全景图id
            panoramadata: {
                tag_id: "",//标签id
                flag_publish: "0",// 发布全景
                scenechoose: "0",// 场景选择
                autorotate: "0",//自动旋转
                littleplanet: "0",//小行星开场
                reward: "0",//允许打赏
                gyro: "0",//开启陀螺仪
                comment: "0",//显示说一说
                footmark: "0",//足迹
                hideuser_flag: "0",//隐藏作者名
                hidelogo_flag: "0",//隐藏logo
                hideviewnum_flag: "0",//隐藏人气
                hidevrglasses_flag: "0",//隐藏vr眼睛
                hideprofile_flag: "0",//隐藏简介
                hidepraise_flag: "0",//隐藏点赞
                hideshare_flag: "0",//隐藏分享
                flag_allowed_recomm: "0",//允许推荐
            },//全景编辑字段
            VrWorksLabel_list: [],//标签列表
            worksLabel: {},//全景图
            sceneSettingsmusic: {
                useSpeech: 0
            },
            worksLabelmusic: {
                isWhole: "1",
                sceneSettings: []
            },//解说音频 
            music_list: "",//背景音乐链接
            radiomusi2: "1",
            radiomusi1: "1",
            sceneSettings: {
                useSpeech: "0",//是否上传音乐
                mediaUrl: "",
                mediaTitle: "",
            },
            worksLabelpipedmusic: {
                isWhole: "1",//全局or单场
                sceneSettings: [],
            },//背景音乐
            BindLabel: [],//绑定房源
            tableData: [],
            //vr房源列表
            webVrSeach: {
                type: 1,
                page: 1,
                per_page: 10,
                keywords: ''
            },
            Prodata: {},
            inputhouse: "",
            // setshow: false,
            panorama: {}, //全景图
            showhousing: false, //控制绑定房源标签显示
            form_AddLabel: {}, //绑定房源，背景，解说音乐，音频
            // values: false,//控制开关
            dialogAddLabel: false,//控制设置背景音乐是否显示
            audio: false,//控制设置解说音频是否显示
            Right_settings: false,//控制右键设置是否显示
            linkSettings: [
                { title: "", content: "" }
            ],
            Rightclickmenu: {
                linkSettings: []
            },//右键菜单
            input: "", //右键中的 
            Bindlistings: false,//控制绑定房源是否显示
            innerVisible: false,
            Bindlistinghome: false,
            selected: "Selected",
            selected1: "", //控制绑定房源下的小蓝条 
            show: true,
            show1: false, //控制绑定房源下的小蓝条显示    
            material: {//获取全景图素材
                name: "", // 素材名称
                media_type: 0, // 素材类型
                time_s: "", // 筛选开始时间
                time_e: "", // 筛选结束时间
            },
            forData_list: [],//存放请求的全景图素材表
            vr_photo: true,//选择全景图是否显示
            vrReminder: false,//开场提示是否显示
            startradio: {
                isDefault: '0',//选择是否使用开场提示
                useAlert: "0",
                imgPath: ""
            },
            accpassword: false,//控制是否显示访问密码
            ruleForm: {
                privacy_password: '',
            },
            rules: {
                pass: [
                    { validator: validatePass, trigger: 'blur' }
                ],
            },
            Topslogan: false,//广告语是否显示
            formTopslogan: {
                adcontent: "",//广告内容
                allow_sys: "0",//允许显示系统广告
                show: "0",//是否有广告内容
            },
            customlogo: false,//自定义logo/作者名
            formlogo: {
                logoImgPath: "",
                useCustomLogo: "0"
            },
            Business: false,//企业名片
            upload_headers: {
                Authorization: config.TOKEN,
            },
            startbegin: false,//开场过渡
            formstart: {
                bgcolor: "",
                isopen: "",//控制是否开启开场过渡
                imgPath: "",
            },
            formcard: {
                flag_vcard: false,
                logo: '',
                wx_logo: "",
                company: "",
                email: "",
                wx: "",
                phone: "",
                addr: "",
                qq: ""

            },
            vrMask: false,//天空地面遮罩
            formvrMaskr: {
                isWhole: "",
                useShade: "",
                shadeSetting: {
                    imgName: "",
                    imgPath: "",
                    isDefault: false,
                    location: "",
                },
            },
            vrnavigation: false,//链接，导航与电话
            pipedmusic: {
                media_type: 1, // 音频素材
            },
            pipedmusic_list: {},
            uniqueArr: [],
            arr: {},

        }
    },
    methods: {
        handleClose() {
            this.drawer = false
        },
        //可视化编辑
        visualediting() {
            console.log(this.Propertydata.pk_works_main);
            this.$http.visualediting(this.Propertydata.pk_works_main).then((res) => {
                console.log(res.data);
                if (res.status == 200) {
                    console.log(res.data);
                    window.open(res.data, "_blank")
                }
            })
        },
        //背景音乐设置
        createVrWorks() {
            this.dialogAddLabel = true;
            this.$http.getMaterialList(this.pipedmusic).then((res) => {
                // console.log(res);
                if (res.status == 200) {
                    this.pipedmusic_list = res.data.data;
                }
            })
        },
        //背景音乐上传
        Musicupload() {
            this.worksLabelpipedmusic.pk_works_main = this.Propertydata.pk_works_main
            this.worksLabelpipedmusic.sceneSettings.push(this.sceneSettings)
            this.$http.Musicuploading(this.worksLabelpipedmusic).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "上传成功"
                    })
                    this.dialogAddLabel = false;
                }
            })
        },
        //音频解说设置
        Explanation() {
            this.audio = true
            this.worksLabelmusic.pk_works_main = this.Propertydata.pk_works_main
            this.$http.getMaterialList(this.pipedmusic).then((res) => {
                if (res.status == 200) {
                    this.pipedmusic_list = res.data.data;
                }
            })
        },
        //确认上传解说音频
        confirmAddLabel() {
            this.worksLabelmusic.sceneSettings.push(this.sceneSettingsmusic)
            this.$http.confirmAddLabel(this.worksLabelmusic).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "上传成功"
                    })
                }
            })
        },
        //绑定房源
        Bind_listings() {
            this.Bindlistings = true
            this.achievetfy()
        },
        //T+房源
        listingsT() {
            this.selected = "Selected"
            this.selected1 = ""
            this.show = true
            this.show1 = false
            this.webVrSeach.type = 1
            this.achievetfy()
        },
        //腾房云房源
        tengfangyun() {
            this.selected1 = "Selected"
            this.selected = ""
            this.show1 = true
            this.show = false
            this.webVrSeach.type = 2
            this.achievetfy()
        },
        //提交绑定房源
        bindlistings() {
            if (this.BindLabel == '') {
                this.showhousing = false
                this.Bindlistings = false
            } else {
                this.showhousing = true
                this.Bindlistings = false
            }
            this.$http.bindlistings(this.Prodata).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "绑定成功"
                    })
                }
            })
        },
        //input框改变获取房源列表
        housingresouces() {
            this.innerVisible = true
        },
        page(value) {
            this.webVrSeach.page = value
            this.achievetfy()
        },
        //获取T+,腾房云房源
        achievetfy() {
            this.$http.TPropertyInformation(this.webVrSeach).then((res) => {
                if (res.status == 200) {
                    this.tableData = res.data
                    // console.log(res.data);
                }
            })
        },
        //获取选中房源id
        Selectedhome(rows) {
            if (!Array.isArray(rows) || rows.length === 0) {
                return;
            }
            const selectedNames = rows.map(row => row.id);
            this.inputhouse = rows.map(row => row.title)

            this.inputhouse = this.inputhouse.join(', ');
            this.Prodata.house_type = this.webVrSeach.type
            this.Prodata.house_id = selectedNames.join(',')
            this.Prodata.pk_works_main = this.Propertydata.pk_works_main
        },
        // 获取vr标签列表
        getVrWorksLabel() {
            this.$http.getVrWorksLabel({ type: "" }).then((res) => {
                // console.log(res.data);
                if (res.status == 200) {
                    this.VrWorksLabel_list = res.data
                }
            })
        },
        // 获取图片素材列表
        getDataList() {
            this.$http.getPanoramaList(this.material).then((res) => {
                if (res.status == 200) {
                    this.forData_list = res.data.data;
                }
            })
        },
        // 处理子组件传递的全景图素材id
        handleMessage(Vr_photoid) {
            console.log(`${Vr_photoid}`);
            this.imgs_works_id.push(`${Vr_photoid}`)
            this.uniqueArr = [...new Set(this.imgs_works_id)];
            console.log(this.uniqueArr);
            this.uniqueArr.map((item) => {
                console.log();
                this.arr = item.toString()

            })
        },
        //确定按钮//
        handle() {
            if (this.arr != "") {
                this.panoramadata.imgs_works_id = this.arr.toString()
                this.$message({
                    type: "success",
                    message: "上传成功"
                })
                return
            }
        },
        //全景上传
        setpanorama() {
            this.panoramadata.pk_works_main = this.Propertydata.pk_works_main
            this.panoramadata.name = this.Propertydata.name
            this.panoramadata.thumb_path = this.Propertydata.thumb_path
            this.panoramadata.profile = this.Propertydata.profile
            this.panoramadata.tag_id = this.panoramadata.tag_id.join(',')
            console.log(this.panoramadata);
            this.$http.Panoramicediting(this.panoramadata).then((res) => {
                console.log(res);
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "修改成功"
                    })
                }
            })

        },
        //选择全景图是否显示
        vrpanorama() {
            this.drawer = true
            this.vrReminder = false
            this.customlogo = false
            this.startbegin = false
            this.Business = false
            this.vrMask = false
            this.vrnavigation = false
            this.vr_photo = true
            // console.log(this.vrReminder);
        },
        //开场提示是否显示
        OpenReminder() {
            this.startradio.pk_works_main = this.Propertydata.pk_works_main
            this.drawer = true
            this.vr_photo = false
            this.customlogo = false
            this.startbegin = false
            this.Business = false
            this.vrMask = false
            this.vrnavigation = false
            this.vrReminder = true
        },
        //开场提示上传自定义图片
        startradioSuccess(res, file) {
            this.startradio.imgPath = URL.createObjectURL(file.raw)
        },
        //确定上传开场提示
        VReminder() {
            // console.log(this.startradio);
            this.$http.VReminder(this.startradio).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "上传成功"
                    })
                }
            })
        },
        //右键菜单
        Rightmenu() {
            this.Rightclickmenu.pk_works_main = this.Propertydata.pk_works_main
            // console.log(this.Rightclickmenu);
            this.Right_settings = true

        },
        Add_Right() {
            if (this.linkSettings.length >= 3) {
                this.$message({
                    type: "warning",
                    message: "可添加数量已达上限"
                })
                return
            }
            this.linkSettings.push({
                title: "",
                content: ""
            })

        },
        del_Right(item) {
            this.linkSettings.splice(item, 1)
        },
        //上传右键菜单
        Right_clickupload() {
            // console.log(this.linkSettings.title );
            // console.log(this.linkSettings.content);
            if (this.linkSettings.title == undefined && this.linkSettings.content == undefined) {
                this.$message({
                    type: "warning",
                    message: "请填写必要信息"
                })
            } else {
                console.log(this.linkSettings);
                this.linkSettings.map((item) => {
                    // console.log(item);
                    this.Rightclickmenu.linkSettings.push(item)
                })
                // console.log(this.Rightclickmenu);
                this.$http.Right_clickupload(this.Rightclickmenu).then((res) => {
                    // console.log(res);
                    if (res.status == 200) {
                        this.$message({
                            type: "success",
                            message: "上传成功"
                        })
                    }
                })
            }

        },
        //设置访问密码
        accesspassword() {
            this.accpassword = true
        },
        //确定上传密码
        upload_password() {
            if (this.ruleForm.privacy_password == "") {
                this.$message({
                    type: "warning",
                    message: "密码不能为空"
                })
            } else {
                this.ruleForm.pk_works_main = this.Propertydata.pk_works_main
                // console.log(this.ruleForm);
                this.$http.upload_password(this.ruleForm).then((res) => {
                    if (res.status == 200) {
                        this.$message({
                            type: "success",
                            message: "上传成功"
                        })
                    }
                    this.accpassword = false
                })
            }

        },
        //确定上传广告
        access_advertisement() {
            if (this.formTopslogan.adcontent == "") {
                this.$message({
                    type: "warning",
                    message: "广告语不能为空"
                })
            } else {
                this.formTopslogan.pk_works_main = this.Propertydata.pk_works_main
                console.log(this.formTopslogan);
                this.$http.accessadvertisement(this.formTopslogan).then((res) => {
                    // console.log(res);
                    if (res.status == 200) {
                        this.$message({
                            type: "success",
                            message: "上传成功"
                        })
                        this.Topslogan = false
                    }
                })
            }

        },
        //顶部广告语
        vrtopslogan() {
            this.Topslogan = true
        },
        //自定义logo/作者名
        customuser() {
            this.formlogo.pk_works_main = this.Propertydata.pk_works_main
            this.drawer = true
            this.vr_photo = false
            this.vrReminder = false
            this.startbegin = false
            this.Business = false
            this.vrMask = false
            this.vrnavigation = false
            this.customlogo = true
        },
        //自定义logo，作者上传图
        logoauthorSuccess(res, file) {
            // console.log(res.url);
            this.formlogo.logoImgPath = URL.createObjectURL(file.raw);
        },
        //确定上传自定义logo
        Authorupload() {
            // console.log(this.formlogo);
            this.$http.Authorupload(this.formlogo).then((res) => {
                // console.log(res);
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "上传成功"
                    })
                }
            })
        },
        //企业名片
        Businesscard() {
            this.formcard.pk_works_main = this.Propertydata.pk_works_main
            this.drawer = true
            this.vr_photo = false
            this.vrReminder = false
            this.customlogo = false
            this.startbegin = false
            this.vrMask = false
            this.vrnavigation = false
            this.Business = true
        },
        BusinessSuccess(res, file) {
            this.formcard.wx_logo = URL.createObjectURL(file.raw);
        },
        //上传企业名片
        EnterprisesCard() {
            console.log(this.formcard);
            if (this.formcard.addr == "" || this.formcard.company == "" || this.formcard.email == "" || this.formcard.logo == "" || this.formcard.phone == "" || this.formcard.qq == "" || this.formcard.wx == "" || this.formcard.wx_logo == "") {
                this.$message({
                    type: "warning",
                    message: "上传内容不能为空"
                })

            } else {
                this.$http.EnterprisesCard(this.formcard).then((res) => {
                    if (res.status == 200) {
                        this.$message({
                            type: "success",
                            message: "上传成功"
                        })
                    }
                })
            }

        },
        //开场过渡图
        OpeningPicture() {
            this.formstart.pk_works_main = this.Propertydata.pk_works_main
            this.drawer = true
            this.vr_photo = false
            this.vrReminder = false
            this.customlogo = false
            this.Business = false
            this.vrMask = false
            this.vrnavigation = false
            this.startbegin = true
        },
        //上传开场图成功后
        startSuccess(res, file) {
            this.formstart.imgPath = URL.createObjectURL(file.raw);
        },
        //确定上传开场过渡图
        Confirmuploadstart() {
            this.$http.Confirmuploadstart(this.formstart).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "上传成功"
                    })
                }

            })
        },
        //遮罩
        vrradioSuccess(res, file) {
            this.formvrMaskr.shadeSetting.imgPath = URL.createObjectURL(file.raw);
        },
        //天空地面遮罩
        SkyGroundMask() {
            this.formvrMaskr.pk_works_main = this.Propertydata.pk_works_main
            this.drawer = true
            this.vr_photo = false
            this.vrReminder = false
            this.customlogo = false
            this.Business = false
            this.startbegin = false
            this.vrnavigation = false
            this.vrMask = true
        },
        //上传遮罩
        UploadMask() {
            let formvrMaskr = JSON.parse(JSON.stringify(this.formvrMaskr))
            if (formvrMaskr.isWhole === "true") {
                formvrMaskr.isWhole = true
            } else {
                formvrMaskr.isWhole = false
            }
            if (formvrMaskr.useShade === "true") {
                formvrMaskr.useShade = true
            } else {
                formvrMaskr.useShade = false
            }
            // console.log(formvrMaskr);
            this.$http.UploadMask(formvrMaskr).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        type: "success",
                        message: "上传成功"
                    })
                }
            })
        },
        //链接，导航与电话
        navigationphone() {
            this.drawer = true
            this.vr_photo = false
            this.vrReminder = false
            this.customlogo = false
            this.Business = false
            this.startbegin = false
            this.vrMask = false
            this.vrnavigation = true
        },

    },
    mounted() {
        
        var data = localStorage.getItem('data');
        this.Propertydata = JSON.parse(data)
        // console.log(this.$route.query.item);
        // console.log(this.Propertydata);
        this.getVrWorksLabel()
        this.getDataList()
    },
}
</script>
<stlye lang="scss" scoped>
.vr-edit {
    padding: 20px;
    margin: -15px;
    background: #F1F4FA;

    .setup {
        width: 98%;
        height: 770px;
        margin: 0 auto;
        justify-content: space-between;

        .Specific {
            width: 95%;
            background-color: white;
            padding-left: 35px;
            margin: 0 auto;

            .Basic_Settings {
                width: 100%;
                height: 260px;
                margin-top: 20px;

                // background: #c5a7c6;
                .front_cover {
                    width: 200px;
                    height: 200px;
                    margin-top: 20px;
                    // background-color: #d1b1d2;
                }

                .introduce {
                    .el-textarea__inner {
                        min-height: 42px;
                        height: 86px;
                    }
                }

                .PhotoID {
                    .el-form-item__content {
                        .el-select {
                            width: 100%;
                            // width: 500px;
                            // width: 60%;
                            margin-left: -80px;
                        }
                    }
                }

                .el-upload-dragger {
                    width: 530px;
                }

            }

            .housing {
                width: 700px;
                margin-left: 50px;
            }

            .GlobalSettings {
                margin-top: 20px;

                .housing-resources {
                    justify-content: space-around;

                    span {
                        font-size: 18px;
                        font-weight: 600;
                        color: #2E3C4E;
                    }

                    #Selected {
                        width: 48px;
                        height: 6px;
                        border-radius: 3px;
                        margin: 0 auto;
                    }

                    #Selected1 {
                        width: 48px;
                        height: 6px;
                        border-radius: 3px;
                        margin: 0 auto;
                    }

                    .Selected {
                        // width: 48px;
                        // height: 6px;
                        background: #2d84fb;
                        // border-radius: 3px;
                        // margin: 0 auto;
                    }
                }

                .ferret-about {
                    margin-top: 20px;
                    margin-left: 20px;
                }
            }

            .Gglobal-main {
                width: 77%;
                min-width: 820px;
                height: 150px;
                // background-color: palegoldenrod;
                margin-top: 10px;

                .Gglobal-name {
                    font-size: 14px;
                    padding-right: 8px;
                    padding-left: 20px;
                }
            }

            .Final-upload {
                width: 85%;
                text-align: right;
            }
        }

        .Specificset {
            // width: 40%;
            background-color: white;
            z-index: 2;

            .Specificset-one {
                width: 95%;
                height: 90%;
                margin: 0 auto;

                // background-color:palevioletred;
                .material_list {
                    box-sizing: border-box;
                    background-color: #F1F4FA;
                    max-height: calc(98vh - 259px);
                    overflow-y: auto;

                    .myPagination {
                        text-align: end;
                        padding: 24px;
                        // background-color: #F1F4FA;
                    }
                }

                .vrphoto {
                    margin-top: 25px;
                    text-align: right;
                }

                .Opening-Reminder {
                    margin: 10px;

                    .prompt {
                        width: 200px;
                        height: 200px;
                        background: #1a1b1d8f;
                        margin: 0 auto;
                        margin-top: 60px;

                        img {
                            width: 90%;
                            height: 90%;
                            margin: 10px;
                        }
                    }

                    .OpenRadio {
                        width: 300px;
                        height: 100px;
                        margin: 0 auto;
                        margin-top: 50px;
                    }

                    .avatar-uploader .el-upload {
                        border: 1px dashed #d9d9d9;
                        border-radius: 6px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }

                    .avatar-uploader .el-upload:hover {
                        border-color: #409EFF;
                    }

                    .avatar-uploader-icon {
                        font-size: 28px;
                        color: #8c939d;
                        width: 178px;
                        height: 178px;
                        line-height: 178px;
                        text-align: center;
                    }

                    .avatar {
                        width: 178px;
                        height: 178px;
                        display: block;
                    }
                }

                .custom-logo {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .Upload-size {
                        font-size: 15px;
                        color: #E6A23C;
                    }

                    .avatar-uploader .el-upload {
                        border: 1px dashed #d9d9d9;
                        border-radius: 6px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }

                    .avatar-uploader .el-upload:hover {
                        border-color: #409EFF;
                    }

                    .avatar-uploader-icon {
                        font-size: 28px;
                        color: #8c939d;
                        width: 178px;
                        height: 178px;
                        line-height: 178px;
                        text-align: center;
                    }

                    .avatar {
                        width: 178px;
                        height: 178px;
                        display: block;
                    }
                }

                .finish {
                    margin-top: 60px;
                    margin-left: 300px;
                }

                .start-begin {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .avatar-uploader .el-upload {
                        border: 1px dashed #d9d9d9;
                        border-radius: 6px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }

                    .avatar-uploader .el-upload:hover {
                        border-color: #409EFF;
                    }

                    .avatar-uploader-icon {
                        font-size: 28px;
                        color: #8c939d;
                        width: 178px;
                        height: 178px;
                        line-height: 178px;
                        text-align: center;
                    }

                    .avatar {
                        width: 178px;
                        height: 178px;
                        display: block;
                    }
                }

                .Business-card {
                    display: flex;
                    justify-content: space-around;
                    margin-top: 20px;

                    div {
                        .avatar-uploader .el-upload {
                            border: 1px dashed #d9d9d9;
                            border-radius: 6px;
                            cursor: pointer;
                            position: relative;
                            overflow: hidden;
                        }

                        .avatar-uploader .el-upload:hover {
                            border-color: #409EFF;
                        }

                        .avatar-uploader-icon {
                            font-size: 28px;
                            color: #8c939d;
                            width: 178px;
                            height: 178px;
                            line-height: 143px;
                            text-align: center;
                        }

                        .avatar {
                            width: 178px;
                            height: 178px;
                            display: block;
                        }
                    }
                }

                .vrMaskradio {
                    .radio {
                        &.el-radio__label {
                            font-size: 17px;
                        }
                    }

                    .avatar-uploader .el-upload {
                        border: 1px dashed #d9d9d9;
                        border-radius: 6px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }

                    .avatar-uploader .el-upload:hover {
                        border-color: #409EFF;
                    }

                    .avatar-uploader-icon {
                        font-size: 28px;
                        color: #8c939d;
                        width: 178px;
                        height: 178px;
                        line-height: 178px;
                        text-align: center;
                    }

                    .avatar {
                        width: 178px;
                        height: 178px;
                        display: block;
                    }

                    .finish {
                        margin-left: 400px;
                    }
                }

            }
        }
    }


}
</stlye>