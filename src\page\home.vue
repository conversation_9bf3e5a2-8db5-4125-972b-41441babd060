<template>
  <div>
    <!-- <login @click="isTrue" v-if="!this.isLogin"></login> -->
    <!-- v-if="this.isLogin === true" -->
    <div>
      <el-header class="header">
        <headerMenu :is_type_check="is_type_check" :type_list="top_menu_list" @onClickType="onClickHeader"></headerMenu>
      </el-header>
      <el-container>
        <el-aside width="230px">
          <div v-if="!is_company_login" class="web-name" @click="showSettingDia">
            {{ website_info.name }}
          </div>
          <template v-if="website_id == 176">
            <slideNavs v-if="
                            menu_list &&
                            (menu_list.id == 2 ||
                              menu_list.id == 3 ||
                              menu_list.id == 6 ||
                              menu_list.id == 7 ||
                              (menu_list.id == 4 && menu_list.new_set_id != 'new_qw'))
                          " style="margin-top: 20px" :menu="menu_list.children"></slideNavs>

            <slideNewNavs v-if="
                            menu_list &&
                            (menu_list.id == 1 ||
                              menu_list.id == 5 ||
                              menu_list.new_set_id == 'new_qw')
                          " @onClick="onClickSlide" :menu="menu_list.children"></slideNewNavs>
          </template>
          <template v-else>
            <slideNavs v-if="
                            menu_list &&
                            (menu_list.id == 2 ||
                              menu_list.id == 3 ||
                              menu_list.id == 6 ||
                              (menu_list.id == 4 && menu_list.new_set_id != 'new_qw'))
                          " style="margin-top: 20px" :menu="menu_list.children"></slideNavs>
            <slideNewNavs v-if="
                            menu_list &&
                            (menu_list.id == 1 ||
                              menu_list.id == 5 ||
                              menu_list.new_set_id == 'new_qw')
                          " @onClick="onClickSlide" :menu="menu_list.children"></slideNewNavs>
          </template>
        </el-aside>
        <el-main style="padding: 0; background: #f1f4fa 100%" class="main-content">
          <!--  开通房源 -->
          <div class="hometabs" v-if="is_type_check == 3 && !website_info.open_house">
            <router-view></router-view>
          </div>
          <!-- website_info.open_house // 是否开通房源 -->
          <div v-else>
            <el-tabs v-model="activeIndex" type="border-card" @tab-click="tabClick()" v-if="options.length"
              @tab-remove="tabRemove" class="hometabs">
              <el-tab-pane :key="item.route + index" v-for="(item, index) in options" :label="item.name"
                :name="item.route" :closable="index !== 0">
                <!-- <keep-alive :include="includePageNames">
                <router-view />
              </keep-alive> -->
              </el-tab-pane>

              <!-- <router-view
              :key="$route.fullPath"
              v-if="$route.meta.keepAlive"
            ></router-view> -->
              <keep-alive :exclude="excludeKeepAliveNames">
                <router-view :key="$route.fullPath" v-if="$route.meta.keepAlive"></router-view>
              </keep-alive>
              <router-view :key="$route.fullPath" v-if="!$route.meta.keepAlive"></router-view>
            </el-tabs>
          </div>
        </el-main>
      </el-container>
    </div>
    <a href=""></a>
    <!-- <el-dialog width="350px" :visible.sync="isDialog" title="提示">
      <div class="shouquan">
        <div
          class="s-box div row"
          style="margin-bottom: 12px"
          v-if="is_wx_show"
        >
          <div class="name-desc div row">
            <img
              src="https://img.tfcs.cn/backup/static/admin/crm/static/menu/wx.png"
              alt=""
            />微信公众号授权
          </div>
          <div class="label" v-if="wx_auth">已授权</div>
          <div class="label red" v-else @click="AuthorizationGX">未授权</div>
        </div>
        <div class="s-box div row" style="margin-bottom: 12px">
          <div class="name-desc div row">
            <img
              src="https://img.tfcs.cn/backup/static/admin/crm/static/menu/qw.png"
              alt=""
            />企业微信授权
          </div>
          <div class="label" v-if="website_info.crm_auth">已授权</div>
          <div class="label red" @click="getQyWxAppid" v-else>未授权</div>
        </div>
        <div
          class="s-box div row"
          style="margin-bottom: 12px"
          v-if="is_wx_show"
        >
          <div class="name-desc div row">
            <img
              src="https://img.tfcs.cn/backup/static/admin/crm/static/menu/wx.png"
              alt=""
            />微信小程序授权
          </div>
          <div class="label" v-if="!is_mini">已授权</div>
          <div class="label red" @click="setWxmini" v-else>未授权</div>
        </div>
        <div class="s-box div row" v-if="show_pingtai_setting">
          <div class="name-desc div row">
            <div class="tj">T</div>
            平台配置
          </div>
          <div class="label" @click="onClickSetting">进入</div>
        </div>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
/* eslint-disable */
import slideNavs from "@/components/slideNavs";
import headerMenu from "@/components/headerMenu";
import slideNewNavs from "@/components/slideNewNavs.vue";
import { mapActions, mapState } from "vuex";
export default {
  data() {
    return {
      isLogin: false,
      isShow: true,
      user: "",
      // 右键弹出
      contextMenuVisible: false,
      left: "",
      top: "",
      isDialog: false,
      wx_auth: false, // 微信公众号授权
      menu_list: {},
      top_menu_list: [],
      is_type_check: "",
      is_mini: false,
      // includePageNames: [],
      is_company_login: false, // 是否是企业站点后台登录
      webData: {},
      is_wx_show: false,
      show_pingtai_setting: 0,
      admin_list: {},
      website_id: '',
      router_name: "",
      excludeKeepAliveNames: []
    };
  },
  components: {
    slideNavs,
    headerMenu,
    slideNewNavs,
  },
  created() {
    this.website_id = localStorage.getItem("website_id")
  },
  mounted() {
    if (localStorage.getItem("company_token")) {
      this.is_company_login = true;
      this.is_type_check = 1;
      this.top_menu_list = [
        {
          id: 1,
          title: "新房",
          name: "新房",
          home_url: "/company_property_list",
          children: [
            {
              id: 29,
              pid: 0,
              name: "项目",
              title: "项目",
              type: "1",
              extend_list: {
                name: "项目",
                id: 4,
                icon: "el-icon-odometer",
                img:
                  "https://img.tfcs.cn/backup/static/admin/crm/static/menu/xiangmu.png",
              },
              children: [
                {
                  id: 44,
                  pid: 29,
                  name: "项目列表",
                  title: "项目列表",
                  type: "1",
                  extend_list: {
                    name: "company_project_list",
                    componentName: "company_project_list",
                    title: "项目列表",
                    icon: "el-icon-newfontxiangmuliebiao",
                  },
                  children: [],
                },
                {
                  id: 43,
                  pid: 29,
                  name: "发布楼盘",
                  title: "发布楼盘",
                  type: "1",
                  extend_list: {
                    name: "company_addbuild",
                    componentName: "company_addbuild",
                    title: "发布楼盘",
                    icon: "el-icon-plus",
                  },
                  children: [],
                },
                {
                  id: 42,
                  pid: 29,
                  name: "楼盘列表",
                  title: "楼盘列表",
                  type: "1",
                  extend_list: {
                    name: "company_property_list",
                    componentName: "company_property_list",
                    title: "楼盘列表",
                    icon: "el-icon-newfontloupanshuju",
                  },
                  children: [],
                },
              ],
            },
            // {
            //   id: 30,
            //   name: "项目",
            //   title: "项目",
            //   extend_list: {
            //     name: "客户",
            //     id: 5,
            //     icon: "el-icon-s-custom",
            //     img:
            //       "https://img.tfcs.cn/backup/static/admin/crm/static/menu/kehu.png",
            //   },
            //   children: [
            //     {
            //       id: 47,
            //       pid: 30,
            //       name: "报备客户",
            //       title: "报备客户",
            //       extend:
            //         '{"name":"report_customer","componentName":"report_customer","title":"报备客户", "icon":"el-icon-newfontjingjiren_baobeiguanli"}',
            //       type: "1",
            //       extend_list: {
            //         name: "company_report_customer",
            //         componentName: "company_report_customer",
            //         title: "报备客户",
            //         icon: "el-icon-newfontjingjiren_baobeiguanli",
            //       },
            //       children: [],
            //     },
            //   ],
            // },
          ],
        },
      ];
      this.menu_list = this.top_menu_list[0];
      this.$store.state.options_tabs = [
        {
          // 如果本地有顶部id，则id-1就是下标，否则就是0
          route: this.top_menu_list[0].home_url,
          name: this.top_menu_list[0].title,
        },
      ];
      return;
    }
    if (localStorage.getItem("TOKEN")) {
      this.getConfigInfo()
    } else {
      this.getUserInfo()
    }
    eventBus.$on("setNewSideBar", menuList => {
      this.menu_list = menuList
    })

    this.getWebInfo(this.setTopHeader)
    this.getRoles();
    this.queryWx();
    this.queryXiaoApp();
    this.getBrowserInfo();


    eventBus.$on("closeTab", (name) => {
      if (this.$store.state.closeTab) {
        this.$store.state.closeTab = false;
        this.tabRemove(name);
      }
    });

  },
  computed: {
    options() {
      return this.$store.state.options_tabs;
    },
    activeIndex: {
      get() {
        return this.$store.state.activeIndex;
      },
      set(val) {
        this.$store.commit("set_active_index", [val]);
      },
    },
    ...mapState(["website_info", "is_qywx_expire", "top_slide_menu",]),
    // top_slide_menu: {
    //   get() {
    //     return this.$store.state.top_slide_menu;
    //   },
    //   set(val) {
    //     console.log(val,123123);
    //    return val
    //   },
    // },
  },
  watch: {
    $route(to, from) {
      this.excludeKeepAliveNames = [];
      /* eslint-disable */
      // 路由缓存如果tabs存在路由则keepalive为true否则falsee
      // var activeIndex = store.state.activeIndex; // 当前激活的路由
      let flag = false;
      for (let option of this.options) {
        if (to.fullPath.indexOf(option.route) != -1) {
          flag = true;
          this.$store.commit("set_active_index", [to.fullPath, to.params.replaceRoute]);
          // to.meta.keepAlive = true;
          break;
        } else {
          // from.meta.keepAlive = false;
        }
      }
      if (!flag) {
        let menu = [], name = ''
        if (sessionStorage.getItem('top_menu_info')) {
          menu = JSON.parse(sessionStorage.getItem('top_menu_info'))
        }

        const found = this.setName(menu, to.fullPath)
        // console.log(name);
      
        this.$store.commit("add_tabs", {
          route: to.fullPath,
          name: found ? this.router_name || to.meta.title : to.meta.title || this.router_name,
          meta: to.meta
        });
        this.$store.commit("set_active_index", [to.fullPath, to.params.replaceRoute]);
        if(to.name){
          this.$nextTick(() => {
            this.$emitPageOpen(to.name, to.fullPath);
          })
        }
      }
    },
  },
  methods: {
    ...mapActions(["getRoles", "getTopSlideTopMenu"]),
    setName(obj, name) {
      /* 增加是否找到匹配的页签标题
      注：原代码为找到最后一个匹配的为准，调整后不更改此逻辑 */
      let found = false;

      // if (!Array.isArray(arr)) {
      //   return ''
      // }
      let res = '', end = false
      if (obj.children && obj.children.length) {
        // for (let index = 0; index < obj.children.length; index++) {
        //   const element = obj.children[index];
        //   if (element.children && element.children.length) {
        //     this.setName(element, name)
        //   } else {
        //     if (element.extend_list && element.extend_list.name == name) {
        //       res = element.extend_list.name
        //       return res
        //       break
        //     }
        //   }

        // }
        for(const item of obj.children){
          if (item.children && item.children.length) {
            const flag = this.setName(item, name)
            if(flag) found = true;
          } else {
            if (item.extend_list && name.indexOf(item.extend_list.componentName) >= 0) {
              this.router_name = item.extend_list.title
              found = true;
            }
          }
        }
      }
      return found;


    },
    //  获取当前日期用于对比七天试用期是否到期
    getCurrentTime() {
      const nowDate = new Date();
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      };
      const newmonth = date.month > 10 ? date.month : "0" + date.month;
      const newday = date.date > 10 ? date.date : "0" + date.date;
      const newminutes = date.minutes > 10 ? date.minutes : "0" + date.minutes;
      const newseconds = date.seconds > 10 ? date.seconds : "0" + date.seconds;
      const newhours = date.hours > 10 ? date.hours : "0" + date.hours;
      //   const newminutes = date.minutes < 10 ? "0" + date.minutes : date.minutes;
      //   const newseconds = date.seconds < 10 ? "0" + date.seconds : date.seconds;

      let dateTime =
        date.year +
        "-" +
        newmonth +
        "-" +
        newday +
        " " +
        newhours +
        ":" +
        newminutes +
        ":" +
        newseconds;
      return dateTime;
    },
    setTopHeader() {

      this.getTopSlideTopMenu((e) => {
        // console.log(this.top_menu_list, this.top_slide_menu[0].id);
        this.top_menu_list = this.top_slide_menu;
        // 判断本地存储是否存在这个id
        var top_slide_id = "";
        let ids = []
        this.top_menu_list.length && this.top_slide_menu.map(item => {
          ids.push(+item.id)
        })
        if (ids.includes("4") || ids.includes(4)) {
          this.$store.state.has_qw_menu = 1
        } else {
          this.$store.state.has_qw_menu = 0
        }
        if (ids.includes(+sessionStorage.getItem('top_select_id'))) {
          top_slide_id = sessionStorage.getItem('top_select_id') ? sessionStorage.getItem('top_select_id') : (this.top_menu_list.length > 0 ? this.top_menu_list[0].id : '');
        } else {
          sessionStorage.setItem('top_select_id', this.top_menu_list.length > 0 ? this.top_menu_list[0].id : '');
          top_slide_id = this.top_menu_list.length > 0 ? this.top_menu_list[0].id : '';
        }
        top_slide_id = sessionStorage.getItem('top_select_id') ? sessionStorage.getItem('top_select_id') : (this.top_menu_list.length > 0 ? this.top_menu_list[0].id : '');
        this.is_type_check = top_slide_id
        if (top_slide_id == 3 && !this.webData.open_house) {
          this.$goPath("/crm_house_open");
          return;
        }

        this.top_menu_list.map((item) => {
          // 本地储存id不一致取数据第一位id

          // if (item.id != sessionStorage.getItem("top_select_id")) {
          //   top_slide_id = this.top_menu_list[0].id;
          //   sessionStorage.setItem("top_select_id", top_slide_id);
          // } else {
          //   // 如果一致则取本地存储id
          //   top_slide_id = sessionStorage.getItem("top_select_id");
          // }
        });
        if (this.top_menu_list && this.top_menu_list.length > 0) {
          // 存储本地菜单顶级id
          this.is_type_check = top_slide_id || this.top_menu_list[0].id;
          this.menu_list = this.top_menu_list.filter((item) => {
            if (item.id == this.is_type_check) {
              return item;
            }
          })[0];
          if (sessionStorage.getItem("new_qw") == 1) {
            // sessionStorage.setItem('top_menu_info', sessionStorage.getItem('top_menu_info_new'))
            this.menu_list = JSON.parse(sessionStorage.getItem('top_menu_info_new'))
          }
          this.$store.state.options_tabs = [
            {
              // 如果本地有顶部id，则id-1就是下标，否则就是0
              route: this.top_menu_list[0].home_url,
              name: this.top_menu_list[0].title,
            },
          ];
          if (this.$route.path === "/index") {
            // 中控跳转后台或者路径直接输入/index跳转菜单第一项
            this.is_type_check = this.top_menu_list[0].id;

            sessionStorage.setItem('top_menu_info', JSON.stringify(this.top_slide_menu[0]))
            if (this.is_type_check == 3 && !this.webData.open_house) {
              this.$goPath("/crm_house_open");
              return;
            }
            if (this.is_type_check == 4) {
              this.$goPath("/crm_customer_crmlist");
              return;
            }
            this.$goPath(this.top_slide_menu[0].home_url);
          } else {
            let top_slide_id = "", top_menu = {};
            top_slide_id = sessionStorage.getItem('top_select_id') ? sessionStorage.getItem('top_select_id') : (this.top_slide_menu.length > 0 ? this.top_slide_menu[0].id : '');
            top_menu = this.top_slide_menu.find(item => item.id == top_slide_id)
            if (top_menu) {
              sessionStorage.setItem('top_menu_info', JSON.stringify(top_menu))
            } else {
              sessionStorage.setItem('top_menu_info', JSON.stringify(this.top_slide_menu[0]))
            }
            // sessionStorage.setItem('top_menu_info', JSON.stringify(this.top_slide_menu[0]))
            sessionStorage.setItem('top_select_id', top_slide_id)
            // sessionStorage.setItem('top_menu_info', this.top_slide_menu[0])
            // let top_menu_info = sessionStorage.getItem(top_menu_info)
            // if (top_menu_info) {
            //   // this.$goPath(top_menu_info.)
            // }
            // this.setIndexPath(this.is_type_check);
          }
        }
      })
    },
    getWebInfo(callback) {
      this.$http.getWebsite(localStorage.getItem("website_id")).then((res) => {
        if (res.status === 200) {
          this.$store.commit("setWebsiteInfo", res.data);
          this.$store.state.website_info = res.data;
          this.webData = res.data
          if (this.webData.website_mode_show.split(",").indexOf("1") != -1) {
            this.is_wx_show = true
          }
          sessionStorage.setItem('gsname', this.webData.name);
          // 储存系统是否开启crm
          localStorage.setItem("website_crm", res.data.is_open_crm);
          if (this.$envjudge() === 'com-wx-pc') {
            if (res.data.wx_work_expired_time) {
              if (
                // 时间对比到期菜单无法点击
                this.compare(this.getCurrentTime(), res.data.wx_work_expired_time)
              ) {
                // this.$store.state.is_qywx_expire = true
                this.$store.commit("setExprie", true)
                // this.setExprie(true);
                this.$message.warning("当前系统已过试用期，请联系官方进行操作");
              }
            }
          }
          callback && callback()
          // 获取字典数据
        }
      });
    },
    async getConfigInfo() {
      let admin_roles = await this.$http.getAdmin().catch(() => {
        console.log();
      })
      this.admin_list = admin_roles.data;
      if (admin_roles.status == 200 && admin_roles.data.roles && admin_roles.data.roles.length && admin_roles.data.roles[0].name === "站长") {
        this.show_pingtai_setting = 1;
        this.$http.getInfoConfig().then(res => {
          if (res.status == 200) {
            this.$store.state.disableClick = ''
            return
          }
          this.$store.state.disableClick = res.data.message
          // this.$store.state.disableClick = "已满"
          this.$router.replace("/crm_customer_personnel?website_id=" + localStorage.getItem("website_id"))
        }).catch((err) => {
          console.log(err);
        })
      } else {
        this.getCheckShow(admin_roles.data.id)
        this.$http.getInfoConfig().then(res => {
          if (res.status == 200) {
            this.$store.state.disableClick = ''
            return
          }
          this.$store.state.disableClick = res.data.message
          this.$router.replace("/web_overview?website_id=" + localStorage.getItem("website_id"))
        }).catch((err) => {
          console.log(err);

        })
      }

    },

    //日期比较的方法
    compare(date1, date2) {
      let dates1 = new Date(date1);
      let dates2 = new Date(date2);
      if (dates1 > dates2) {
        return true;
      } else {
        return false;
      }
    },
    // isTrue(e) {
    //   setTimeout(() => {
    //     this.isLogin = e;
    //     this.isShow = !e;
    //   }, 300);
    // }

    queryXiaoApp() {
      this.$http.queryXiaoApp().then((res) => {
        if (res.status === 200) {
          if (!res.data.id && !res.data.updated_at) {
            this.is_mini = true;
          }
        }
      });
    },
    onClickHeader(e) {
      if (this.$store.state.disableClick) return
      this.menu_list = "";
      this.is_type_check = e.id;
      // 如果选中房源且未开通房源跳转至开通房源页面
      if (e.id == 3 && !this.website_info.open_house) {
        this.$goPath("/crm_house_open");
        return;
      }
      sessionStorage.removeItem("new_qw")
      sessionStorage.setItem("top_menu_info", JSON.stringify(e));
      sessionStorage.setItem("top_select_id", e.id);
      this.setIndexPath(e.id);
      this.menu_list = e;
    },
    setIndexPath(id) {
      if (sessionStorage.getItem("is_other")) return
      switch (+id) {
        case 1:

          this.$goPath("web_overview");
          break;
        case 2:
          this.$goPath("crm_index");
          break;
        case 3:
          this.$goPath("house_list?trade_type=0");
          break;
        case 4:
          this.$goPath("crm_customer_crmlist");
          break;
        case 6:
          this.$goPath("douyin_index");
          break;

        default:
          break;
      }
    },
    getBrowserInfo() {
      var ua = navigator.userAgent.toLocaleLowerCase();
      var browserType = null;
      if (ua.match(/msie/) != null || ua.match(/trident/) != null) {
        browserType = "IE";
      } else if (ua.match(/firefox/) != null) {
        browserType = "火狐";
      } else if (ua.match(/ubrowser/) != null) {
        browserType = "UC";
      } else if (ua.match(/opera/) != null) {
        browserType = "欧朋";
      } else if (ua.match(/bidubrowser/) != null) {
        browserType = "百度";
      } else if (ua.match(/metasr/) != null) {
        browserType = "搜狗";
      } else if (
        ua.match(/tencenttraveler/) != null ||
        ua.match(/qqbrowse/) != null
      ) {
        browserType = "QQ";
      } else if (ua.match(/maxthon/) != null) {
        browserType = "遨游";
      } else if (ua.match(/chrome/) != null) {
        var is360 = _mime("type", "application/vnd.chromium.remoting-viewer");
        function _mime(option, value) {
          var mimeTypes = navigator.mimeTypes;
          for (var mt in mimeTypes) {
            if (mimeTypes[mt][option] == value) {
              return true;
            }
          }
          return false;
        }
        if (is360) {
          browserType = "360";
        } else {
          browserType = "谷歌";
        }
      } else if (ua.match(/safari/) != null) {
        browserType = "Safari";
      }
      if (browserType == "IE") {
        this.$message({
          message: "建议使用360极速模式",
          type: "warning",
        });
      }
    },
    AuthorizationGX() {
      let website_id = localStorage.getItem("website_id");
      let url = encodeURIComponent(
        `https://yun.tfcs.cn/admin/?website_id=${website_id}#/loading?is_loading=1`
      );
      this.$http.openWX(url).then((res) => {
        if (res.status === 200) {
          // window.location.href = res.data.url;
          var url = res.data.url;
          this.setNewLink(url);
        }
      });
    },
    queryWx() {
      // 调用这个接口查询授权
      this.$http.queryGongZhong().then((res) => {
        if (res.status === 200) {
          if (res.data.id && res.data.updated_at) {
            this.wx_auth = true;
          }
        }
      });
    },
    getQyWxAppid() {
      this.$http.getWxWorkAppId().then((res) => {
        if (res.status === 200) {
          let website_id = localStorage.getItem("website_id");
          let url = encodeURIComponent(
            `https://yun.tfcs.cn/admin/?website_id=${website_id}#/loading?is_loading=1`
          );
          let qywxurl = `https://open.work.weixin.qq.com/3rdapp/install?suite_id=${res.data.app_id}&pre_auth_code=${res.data.pre_auth_code}&redirect_uri=${url}&state=qywxnew`;
          this.setNewLink(qywxurl);
        }
      });
    },
    setNewLink(url) {
      let referLink = document.createElement("a");
      referLink.href = url;
      document.body.appendChild(referLink);
      referLink.click();
      parent.removeChild(referLink);
    },
    tabRemove(targetName) {
      try{
        const removeTag = this.$store.state.options_tabs.find(e => e.route === targetName);
        if(removeTag && removeTag?.meta?.keepAlive && removeTag?.meta?.keepAliveName){
          this.excludeKeepAliveNames.push(removeTag.meta.keepAliveName);
        }
      }catch(e){}
      
      this.$store.commit("delete_tabs", targetName);
      if (targetName.indexOf('/crm_customer_business_setting') >= 0 && sessionStorage.getItem("new_qw")) {
        let menuList = sessionStorage.getItem("top_menu_info") ? JSON.parse(sessionStorage.getItem("top_menu_info")) : {}
        sessionStorage.removeItem("new_qw")
        this.menu_list = menuList
      }

      setTimeout(() => {
        if (this.activeIndex === targetName) {
          // 设置当前激活的路由
          if (this.options && this.options.length >= 1) {
            this.$store.commit(
              "set_active_index",
              [this.options[this.options.length - 1].route]
            );
            this.$goPath(this.activeIndex);
            // this.$router.push(this.activeIndex);
          } else {
            this.setIndexPath(this.is_type_check);
          }
        } else if (
          this.activeIndex == "/crm_customer_business_setting?type=setting" ||
          this.activeIndex == "/crm_customer_business_setting?type=crm"
        ) {
          this.$router.go(-2);
        }
      }, 200);
    },
    onClickSlide(url) {
      this.$goPath(url);
    },
    // tab切换时，动态的切换路由
    tabClick(e) {
      let path = this.activeIndex;
      this.$goPath(path);
      // this.$goPath(path);
    },
    setWxmini() {
      this.isDialog = false;
      this.$goPath("website_update");
    },
    onClickSetting() {
      this.isDialog = false;
      this.$goPath("crm_customer_business_setting?type=crm");
    },
    showSettingDia() {
      // this.isDialog = true
      if (this.$store.state.disableClick) return
      if (this.admin_list.roles && this.admin_list.roles.length && this.admin_list.roles[0].name == '站长') {
        this.$goPath("crm_customer_business_setting?type=crm");
      } else {
        this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            if ((res.data + '').indexOf(this.admin_list.id) == -1) {
              this.$message({
                message: '仅创始人以及指定用户可进入页面',
                type: 'warning'
              });
            } else {
              this.$goPath("crm_customer_business_setting?type=crm");
            }
          }
        })
      }
    },
    getUserInfo() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.admin_list = res.data;
          if (res.data.roles && res.data.roles.length && res.data.roles[0].name === "站长") {
            this.show_pingtai_setting = 1;
          } else {
            this.getCheckShow(res.data.id)
          }
        }
      });
    },
    // 获取成交显示
    getCheckShow(id) {
      // this.$http.getAuthShow("deal_auth").then((res) => {
      this.$http.getCommonSettingRolesConf(1).then((res) => {
        if (res.status === 200) {
          let curr = res.data.find(item => item.key == "config_auth_uid")
          if (curr && (curr.value + '').indexOf(id) != -1) {
            this.show_pingtai_setting = 1;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-aside {
  // background-color: #0174ff;
  background: #3a3f53;
  height: calc(100vh - 60px);
}

* {
  scrollbar-width: none;
}

.el-aside::-webkit-scrollbar {
  display: none;
  /*放在overflow：auto；同级*/
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE10+ */
  &::-webkit-scrollbar {
    display: none;
    /* ChromeSafari */
  }
}

.el-main {
  min-height: 100%;
  height: calc(100vh - 60px);
}

.main-content {}

.header {
  z-index: 100;
}

.contextmenu {
  width: 100px;
  margin: 0;
  border: 1px solid #ccc;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.2);
}

.contextmenu li {
  margin: 0;
  padding: 0px 22px;
}

.contextmenu li:hover {
  background: #f2f2f2;
  cursor: pointer;
}

.contextmenu li button {
  color: #2c3e50;
}

.shouquan {
  .s-box {
    border-radius: 10px;
    border: 0.5px solid #dedede;
    padding: 10px;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    .name-desc {
      align-items: center;
    }

    img {
      margin-right: 8px;
      width: 40px;
      height: 40px;
    }

    .tj {
      margin-right: 8px;
      border-radius: 4px;
      color: #fff;
      text-align: center;
      line-height: 40px;
      width: 40px;
      height: 40px;
      font-size: 20px;
      background: #2d84fb;
    }

    .label {
      font-size: 12px;
      cursor: pointer;
      color: #fff;
      background: #00c800;
      border-radius: 2px;
      padding: 4px 7px;
      min-width: 50px;
      text-align: center;

      &.red {
        background: #ff6d6d;
      }
    }
  }
}

.web-name {
  background: #44495d;
  color: #fff;
  font-size: 13px;
  margin: 20px 14px 0;
  padding: 7px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff2d2d;
    box-shadow: 0px 0px 4px 0px #0000003f;
  }
}
</style>
