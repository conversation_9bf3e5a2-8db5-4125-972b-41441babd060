<template>
  <div class="add">
    <!-- <div class="tips">
      <div>
        1.什么是群活码：通过多个群聊配置一个二维码，客户通过扫描二维码加入群聊，当前面的群人数
        达到上限后，自动发送后面的群二维码，从而突破群聊人数限制，实现一码多群功能。
      </div>
      <div>
        2.怎么用：首先给群活码配置接待员工，客户通过群活码添加员工为好友，添加通过后自动向客户
        发送入群引导语和群聊二维码，再通过扫描群聊二维码入群，当二维码到期后可手动更新二维码。
      </div>
    </div> -->
    <el-form label-width="100px">
      <div class="title">基本信息</div>

      <el-form-item label="群sop名称">
        <div class="form-item-block">
          <el-input placeholder="请输入群sop名称" v-model="form_params.task_name" style="width: 240px; margin-right: 12px">
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="发送者">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-button size="big" class="member_button" v-for="item in form_send_selectedMember" :key="item.id">{{ item.name
                      || item.user_name }}</el-button>
          <el-button size="big" class="el-icon-plus" @click="showAddSendMember">添加员工</el-button>
        </div>
      </el-form-item>
      <el-form-item label="送达客户" v-if="form_send_selectedMember.length && customerList.length">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-button size="big" class="member_button" v-for="item in form_selectedMember" :key="item.id">{{ item.name ||
                      item.user_name }}</el-button>
          <el-button size="big" class="el-icon-plus" @click="showAddMember">添加客户</el-button>
        </div>
      </el-form-item>
      <el-form-item label="立即群发">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-radio-group v-model="form_params.opt_cate" size="medium ">
            <el-radio :label="1" border>立即发送</el-radio>
            <el-radio :label="2" border>定时发送</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item label="执行时间" v-if="form_params.opt_cate == 2">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-radio-group v-model="form_params.time_type" size="mini">
            <el-radio :label="1" border>自定义</el-radio>
            <el-radio :label="2" border>每天</el-radio>
            <el-radio :label="3" border>每周</el-radio>
            <el-radio :label="4" border>每月</el-radio>
          </el-radio-group>
        </div>
        <div v-if="form_params.time_type == 1">
          <el-date-picker style="width: 250px" size="small" v-model="form_params.send_time" type="datetime"
            placeholder="请选择日期" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm">
          </el-date-picker>
        </div>
      </el-form-item>

      <div class="title">发送内容设置</div>
      <div class="tips warning">
        温馨提示 ： 因企业微信限制客户群每天只能接收一条群发消息
      </div>
      <el-form-item label="文案">
        <welcome-mes style="padding-right: 20px" ref="member_sop" :value="welcome_mes"></welcome-mes>
      </el-form-item>
      <!-- <el-form-item label="文案">
        <div class="form-item-block">
          <el-input
            v-model="welcome_mes.text.desc"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入文案"
            type="textarea"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="附件">
        <el-checkbox-group v-model="params_type_arr" size="mini">
          <el-checkbox
            :label="2"
            border
            :class="{ active: params_type == 2 }"
            @change="checkChange($event, 2)"
            >图片</el-checkbox
          >
          <el-checkbox
            :label="5"
            border
            :class="{ active: params_type == 5 }"
            @change="checkChange($event, 5)"
            >视频</el-checkbox
          >
          <el-checkbox
            :label="6"
            border
            :class="{ active: params_type == 6 }"
            @change="checkChange($event, 6)"
            >文件</el-checkbox
          >
          <el-checkbox
            :label="3"
            border
            :class="{ active: params_type == 3 }"
            @change="checkChange($event, 3)"
            >链接</el-checkbox
          >
          <el-checkbox
            :label="4"
            :class="{ active: params_type == 4 }"
            border
            @change="checkChange($event, 4)"
            >小程序</el-checkbox
          >
        </el-checkbox-group>
        <div class="tips">
          <template
            v-if="params_type == 2 || params_type == 3 || params_type == 4"
          >
            图片仅支持png,jpg格式 最大10M</template
          >
          <template v-if="params_type == 5">
            视频仅支持mp4格式 最大10M</template
          >
          <template v-if="params_type == 6"> 文件最大20M</template>
        </div>
      </el-form-item>
      <template v-if="params_type == 2">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="图片" v-if="imgname">
          <div class="form-item-block">
            <img :src="imgname" class="avatar" />
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 3">
        <el-form-item label="跳转路径">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.link.url"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入跳转路径"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="链接标题">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.link.title"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入链接标题"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="链接描述">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.link.desc"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入链接地址"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="封面">
          <div class="form-item-block flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
            >
              <img
                v-if="welcome_mes.link.pic_url"
                :src="welcome_mes.link.pic_url"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 4">
        <el-form-item label="小程序标题">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.miniprogram.title"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入小程序标题"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="小程序appid">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.miniprogram.appid"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入小程序id"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="跳转路径">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.miniprogram.url"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入跳转路径"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="封面">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="封面" v-if="miniCover">
          <div class="form-item-block">
            <img :src="miniCover" class="avatar" />
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 5">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".mp4,.AVI,.MPEG,.MOV,.WMV"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="视频" v-if="videoname">
          <div class="form-item-block">
            <video :src="videoname" class="avatar"></video>
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 6">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              class="upload-demo"
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              :show-file-list="false"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>
            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
          <p class="tip">{{ filename }}</p>
        </el-form-item>
      </template> -->
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
    <el-dialog :visible.sync="show_add_send_member" width="600px" title="选择员工" append-to-body>
      <div class="member">
        <div class="member_box flex-row">
          <div class="left member_con flex-1">
            <memberList v-if="show_add_send_member" :list="memberList" :defaultValue="selectedSendIds"
              @onClickItem="selecetedSendMember" ref="sendMemberList">
            </memberList>
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择员工</div>
            <div class="selected_list">
              <div class="selected_item flex-row align-center" v-for="item in selectedSendList" :key="item.id">
                <div class="name flex-1">{{ item.name || item.user_name }}</div>
                <div class="delete" @click="deleteSendSelected(item)">×</div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="show_add_send_member = false">取消</el-button>
          <el-button type="primary" @click="selectSendMemberOk" :loading="isSubmiting">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_add_member" width="600px" title="选择送达的客户" append-to-body>
      <div class="member">
        <div class="member_box flex-row">
          <div class="right left member_con flex-1">
            <div class="select_title">选择客户</div>
            <div class="selected_list">
              <memberList v-if="show_add_member" :list="customerList" :defaultValue="selectedCustomerIds"
                @onClickItem="selecetedCustomer" :checkStrictly="false" ref="customerList" :defaultProps="{
                                  children: 'subs',
                                  label: 'user_name',
                                  value: 'id',
                                }">
                <!-- disabled: (data) => {
                    return !data.pid;
                  }, -->
              </memberList>
            </div>
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择客户</div>
            <div class="selected_list">
              <div class="selected_item flex-row align-center" v-for="item in selectedList" :key="item.id">
                <div class="name flex-1">{{ item.name || item.user_name }}</div>
                <div class="delete" @click="deleteSelected(item)">×</div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false">取消</el-button>
          <el-button type="primary" @click="selectMemberOk" :loading="isSubmiting">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_select_dia" width="600px" title="选择附件" append-to-body>
      <div class="imgLists">
        <div class="img_list flex-row" @scroll="handleScroll">
          <template v-if="params_type == 2 || params_type == 3 || params_type == 4">
            <div class="img" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <img :src="item.url" alt="" />
            </div>
          </template>
          <template v-if="params_type == 5">
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <video :src="item.url"></video>
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
          <template v-if="params_type == 6">
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_select_dia = false">取消</el-button>
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import memberList from "../memberList_new"
import config from "@/utils/config";
import welcomeMes from "../welcome_mes"
export default {
  components: {
    memberList, welcomeMes
  },
  data() {
    return {
      form_params: {
        sender: '',
        time_type: '',
        send_time: new Date(),
        is_open: true,
      },
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      show_select_dia: false,
      group_list: [],
      value1: "",
      params_type: 2,
      params_type_arr: [],
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      miniCover: "",
      filename: "",
      videoname: "",
      imgname: "",
      img_params: {
        page: 1,
        per_page: 10,
        type: 1,
      },
      imgList: [],
      currentImg: '',
      selectedCustomerIds: [],
      customerList: [],
      form_send_selectedMember: [],
      show_add_send_member: false,
      selectedSendList: [],
      selectedSendIds: [],
      memberList: []


    }
  },
  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  created() {
    this.getDepartment()
    this.form_params.send_time = this.formatDateTime(new Date())
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      if (params.is_open) {
        params.is_open = 1;
      } else {
        params.is_open = 0;
      }
      if (params.time_type != 1) {
        delete params.send_time
      }
      let _welcome_mes = Object.assign({}, this.$refs.member_sop.welcome_mes);

      if (!_welcome_mes.text.desc) {
        this.$message.warning("欢迎语不能为空");
        return;
      }
      if (!_welcome_mes.image.media_id) {
        delete _welcome_mes.image;
      }
      let linkArr = {
        title: "链接标题不能为空",
        pic_url: "链接封面不能为空",
        desc: "链接描述不能为空",
        url: "跳转链接不能为空",
      };
      let emptyLink = [];
      for (const key in _welcome_mes.link) {
        if (!_welcome_mes.link[key]) {
          emptyLink.push(key);
        }
      }
      if (emptyLink.length == Object.keys(linkArr).length) {
        emptyLink.length = 0;
        delete _welcome_mes.link;

      } else if (emptyLink.length) {
        this.$message.warning(linkArr[emptyLink[0]]);
        return;
      }
      let miniArr = {
        title: "小程序标题不能为空",
        media_id: "小程序封面不能为空",
        appid: "小程序appid不能为空",
        url: "小程序链接不能为空",
      };
      let emptyMini = [];
      for (const key in _welcome_mes.miniprogram) {
        if (!_welcome_mes.miniprogram[key]) {
          emptyMini.push(key);
        }
      }
      if (emptyMini.length == Object.keys(miniArr).length) {
        emptyMini.length = 0;
        delete _welcome_mes.miniprogram;
      } else if (emptyMini.length) {
        this.$message.warning(miniArr[emptyMini[0]]);
        return;
      }
      if (!_welcome_mes.video.media_id) {
        delete _welcome_mes.video;
      }
      if (!_welcome_mes.file.media_id) {
        delete _welcome_mes.file;
      }
      if (params.opt_cate == 1) {
        delete params.send_time
        delete params.time_type
      }
      params.sender = params.sender.join(",")
      params.external_userid = params.external_userid.join(',')
      params.media = JSON.stringify(_welcome_mes);
      if (this.isSubmiting) return
      this.isSubmiting = true
      console.log(params);

      // this.$http.addCrmMemberSop(params).then(res => {
      //   if (res.status == 200) {
      //     this.$message.success("添加成功");
      //     setTimeout(() => {
      //       this.isSubmiting = false
      //     }, 200);
      //     this.$emit("success")
      //   } else {
      //     this.isSubmiting = false
      //     this.$message.error("添加失败");
      //   }
      // }).catch(() => {
      //   this.isSubmiting = false
      // })
    },
    formatDateTime(date) {
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      let hour = date.getHours()
      let minute = date.getMinutes()
      if (month < 10) {
        month = `0${month}`
      }
      if (day < 10) {
        day = `0${day}`
      }
      hour = hour.toString().padStart(2, '0')
      minute = minute.toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    },

    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList()
        .catch(err => {
          console.log(err);
        })
      if (res.status == 200) {
        this.memberList = res.data
      }
    },
    cancel() {
      this.$emit('cancel')
    },
    // 移除选中的人员
    deleteSendSelected(e) {
      let idx = this.selectedSendList.findIndex(item => item.id == e.id)
      this.selectedSendList.splice(idx, 1)
      this.selectedSendIds.splice(idx, 1)

      setTimeout(() => {
        this.$refs.sendMemberList.changeSelected(this.selectedSendIds, true)
      }, 100);


    },
    // 显示选择会员弹框
    showAddSendMember() {
      this.show_add_send_member = true
    },
    // 选中会员
    selecetedSendMember(e) {
      this.selectedSendIds = e.checkedKeys
      this.selectedSendList = e.checkedNodes
    },
    selectSendMemberOk() {
      let sender = []
      this.selectedSendIds.map(item => {
        sender.push(item.split('_')[1])
      })
      this.form_params.sender = sender
      this.selectedSendList.map(item => {
        if (timer) clearTimeout(timer)
        let timer = setTimeout(() => {
          this.getCustomerList(item)
        }, 200);

      })
      this.form_send_selectedMember = this.selectedSendList
      this.show_add_send_member = false

    },

    // 移除选中的人员
    deleteSelected(e) {
      let curr = this.selectedList.find(item => item.id == e.id)
      if (curr && (!curr.pid)) {
        let subIds = []
        curr.subs.map(item => {
          subIds.push(item.id)
        })
        let selIds = this.selectedIds
        let selList = this.selectedList
        this.selectedIds = subIds.reduce((total, current) => {
          (!selIds.includes(current)) && total.push(current)
          return total
        }, [])
        this.selectedList = selList.reduce((total, current) => {
          (!selIds.includes(current.id)) && total.push(current)
          return total
        }, [])
        setTimeout(() => {
          this.$refs.customerList.changeSelected(this.selectedIds, false)
        }, 100);

      } else {
        let idx = this.selectedList.findIndex(item => item.id == e.id)
        this.selectedList.splice(idx, 1)
        this.selectedIds.splice(idx, 1)
        this.$refs.customerList.changeSelected(this.selectedIds, false)
      }
    },
    // 显示选择会员弹框
    showAddMember() {
      this.show_add_member = true
    },
    selecetedCustomer(e) {
      this.selectedIds = e.checkedKeys
      this.selectedList = e.checkedNodes
      this.halfSelectedIds = e.halfCheckedKeys
      this.allSelected = this.selectedIds.filter(item => (item + '').split("_").length
        > 1)
    },

    getCustomerList(data) {
      console.log(data);
      this.$http.getCrmMemberCustomer(data.oldId).then(res => {
        console.log(res);
        if (res.status == 200) {
          console.log(this.customerList);
          let cIndex = this.customerList.findIndex(item => item.id == data.id)
          if (cIndex < 0 && res.data.data.length) {
            res.data.data.map(item => {
              item.user_name = item.remark
              item.pid = data.id
              item.old_id = item.id
              item.id = data.id + '_' + item.id
              return item
            })
            this.customerList.push({
              id: data.id,
              user_name: data.name,
              subs: res.data.data
            })
          }
        }

      })
    },
    // 选中会员
    selecetedMember() {
      // this.selectedIds = e.checkedKeys
      // this.selectedList = e.checkedNodes
    },
    selectMemberOk() {
      let sender = []
      this.halfSelectedIds.map(id => {
        let name = "userid_" + id
        this.selectedIds.filter(item => item && (item + '').split("_").length == 2).map(item => {
          if (id == item.split("_")[0]) {
            name += "/" + item.split("_")[1]
          }
          return item
        })
        sender.push(name)
        return id
      })
      let selce = this.selectedIds.filter(item => (item + '').split("_").length == 1).map(item => 'userid_' + item)
      this.form_params.external_userid = sender.concat(selce)
      this.form_selectedMember = this.selectedList.filter(item => (item.id + '').split("_").length > 1)
      this.show_add_member = false

    },
    checkChange(e, type) {
      console.log(e, type);
      if (e) {
        this.params_type = type;
        var category = config.CATEGORY_IM_IMAGE;
        switch (type) {
          case 2:
          case 3:
          case 4:
            category = config.CATEGORY_IM_IMAGE;
            break;
          case 5:
            category = config.CATEGORY_IM_VIDEO;
            break;
          case 6:
            category = config.CATEGORY_IM_FILE;
            break;
          default:
            break;
        }
        this.website_img = `/api/common/file/upload/admin?category=${category}`;
      }
    },
    showSelectDia() {
      this.img_params.page = 1;
      this.getImgList();
      this.show_select_dia = true;
    },

    // 选中图片
    selectAvatar(e) {
      this.currentImg = e.url;
      // this.imgList.map(item => item.checked = false)
      // e.checked = true
    },
    // 选中图片确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      switch (this.params_type) {
        case 2:
          // 图片
          this.welcome_mes.image.media_id = current.media_id;
          this.imgname = current.url;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = current.url;
          // this.welcome_mes.link.pic_url = url;
          this.welcome_mes.link.media_id = current.media_id;
          break;
        case 4:
          // 小程序 miniprogram   media_id
          this.miniCover = current.url;
          // this.welcome_mes.miniprogram.pic_url = url;
          this.welcome_mes.miniprogram.media_id = current.media_id;
          break;
        case 5:
          // 视频
          this.videoname = current.url;
          this.welcome_mes.video.media_id = current.media_id;
          break;
        case 6:
          // 文件
          this.filename = current.url;
          this.welcome_mes.file.media_id = current.media_id;
          break;

        default:
          break;
      }
      this.show_select_dia = false;
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      this.loadMore = false
      switch (this.params_type) {
        case 2:
        case 3:
        case 4:
          this.img_params.type = 1;
          break;
        case 5:
          this.img_params.type = 3;
          break;
        case 6:
          this.img_params.type = 4;
          break;

        default:
          break;
      }
      this.$http.getCrmServiceAvatarList(this.img_params).then((res) => {
        if (res.status == 200) {
          if (this.img_params.type == 4 || this.img_params.type == 3) {
            res.data.data.map((item) => {
              item.user_name = item.url.substring(
                item.url.lastIndexOf("/") + 1
              );
              return item;
            });
          }
          this.imgList = this.imgList.concat(res.data.data);
          if (res.data.data.length == this.img_params.per_page) {
            this.loadMore = true;
          } else {
            this.loadMore = false;
          }
        }
      }).catch(() => {
        this.loadMore = false
      });
    },
    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.params_type) {
        case 2:
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          this.imgname = url;
          this.welcome_mes.image.media_id = res.data.media_id;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = url;
          // this.welcome_mes.link.media_id = res.data.media_id;
          break;
        case 4:
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          // 小程序 miniprogram   media_id
          this.miniCover = url;
          this.welcome_mes.miniprogram.media_id = res.data.media_id;
          break;
        case 5:
          // 视频
          params.type = 3;
          console.log(url);
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败 请检查素材大小");
            return;
          });

          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case 6:
          // 文件
          params.type = 4;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败");
          });
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
    },




  }
}
</script>

<style lang ="scss" scoped>
.footer {
  padding: 20px 40px;

  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.imgurl {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  .el-button {
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .el-button+.el-button {
    margin-left: 0;
  }

  &.border {
    padding: 20px;
    border: 1px solid #f8f8f8;
    border-radius: 10px;

    .tip {
      max-width: 200px;
      white-space: normal;
      margin-top: 5px;
      line-height: 22px;
    }
  }

  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;

  &.warning {
    background: #fffbe7;
    color: #8a929f;
    margin-bottom: 5px;
  }
}

.tip {
  color: #8a929f;
  font-family: PingFang SC;
  font-weight: regular;
  font-size: 12px;
}

.add {
  max-height: 70vh;
  overflow-y: auto;

  .title {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 16px;
    margin: 20px 0;
    font-weight: 600;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}

.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}

.selected_list {
  padding: 10px 0;

  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;

    .delete {
      font-size: 22px;
      cursor: pointer;
    }

    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}

.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;

  &::-webkit-scrollbar {
    width: 0;
  }

  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;

    &.active {
      border: 5px solid #409eff;

      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    &:nth-child(4n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;

    &.active {
      border: 5px solid #409eff;
    }

    &:nth-child(3n) {
      margin-right: 0;
    }

    .file_img {
      width: 115px;
      height: 115px;
      text-align: center;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}

.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }

  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;

  &.active {
    border: 1px solid #a6e22e;
  }
}

.upload-demo {
  margin-right: 5px;
}
</style>