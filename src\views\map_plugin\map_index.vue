<template>
    <div v-loading="loading" element-loading-text="资料加载中...">
        <div v-if="isLinkExpired">
            <div class="share-user-card">
                <div class="info">
                    <span class="avatar">{{shareUserInfo._firstNameWord}}</span>
                    <span class="name">{{shareUserInfo.name}}</span>
                </div>
                <div class="contact">
                    <span class="contact-btn" @click="contactMe">联系我</span>
                </div>
            </div>
            <div class="link-expired-cont">
                <p>内部资料</p>
                <p>邀请链接已失效，请联系选房师</p>
            </div>
        </div>

        <div class="login-container" id="box_mask" v-if="mapDetail">
            <div class="login-top">
                <img class="imgWeb" :src="mapDetail.top_pic" alt="" mode="widthFix" />
            </div>
            <div class="nav">
                <div class="nav-as" v-for="item in mapDetail.maps" :key="item.id">
                    <img class="imgWeb" :src="item.url" alt="" mode="widthFix" />
                </div>
            </div>
        </div>
    </div>

</template>
<script>
import http1 from "@/utils/http1";
export default {
    name: "",
    data() {
        return {
            loading: true,

            params: {
                plugin_id: 0,
                share_id: '',
                expire_code: ''      //地图链接过期code
            },
            isLinkExpired: false,
            mapDetail: null,
            shareUserInfo: null
        }
    },
    created() {
   

        //获取url参数
        const query = this.$route.query;
        this.params.plugin_id = query.plugin_id || 0;
        this.params.share_id = query._share_id || 0;
        this.params.expire_code = query._expire_code || '';
                  
        this.website_id = query.website_id || "";
        this.token = query.token || "";
        if (query.token) { 
            localStorage.setItem("map_token", this.token);
            localStorage.setItem("web_id", this.website_id);
        }
        //判断如果code存在则不再调起授权
        if(query.code){
            console.log(12121);
        }else{
            // 判断ID是否为1
            if (!this.website_id||this.website_id ==109) {
                   this.mapDetail = null;  // 设置为空，页面将不显示地图内容
                   this.loading = true;   // 停止loading
                   this.getWxConfig()
                   return;  // 不执行后续操作，直接返回
               }
        }
        if(!this.isWeChatEnvironment()){
            location.href = "https://www.tfcs.cn";
            return;
        }
        // // 在created生命周期钩子中获取URL中的code
        // const code = this.getCodeFromUrl();
        // if (code) {
        //   this.redirectH5authorization(code)

        // } else {
        //   console.error('未获取到code');
        // }
        //浏览时间统计
        try{
            let browseStartTime = parseInt(new Date().getTime() / 1000),
                browseEndTime = 0;
            document.addEventListener('visibilitychange', () => {
                switch(document.visibilityState){
                    case 'visible':
                        browseStartTime = parseInt(new Date().getTime() / 1000);
                        break;
                    case 'hidden':
                        browseEndTime = parseInt(new Date().getTime() / 1000);
                        let browse_time = browseEndTime - browseStartTime;
                        if(browse_time > 0){
                            const formData = new FormData();
                            formData.append('plugin_id', this.params.plugin_id);
                            formData.append('access_time', browseStartTime);
                            formData.append('login_out_time', browseEndTime);
                            formData.append('browse_time', browse_time);
                            formData.append('token', 'Bearer ' + this.token);
                            navigator.sendBeacon('/api/common/map_plugin/update_times_v2', formData);
                        }
                        break;
                }
            });
        }catch(e){
            console.error(e);
        }

        this.init();
    },
    methods: {
        // 判断是否在微信浏览器环境下
        isWeChatEnvironment() {
            return window.__wxjs_environment === "miniprogram";
        },
        //获取微信jssdk配置
        async getWxConfig() {
            let currentUrl = window.location.href;
            try {
                let res = await http1.getWxConfig(this.website_id);
                console.log(res,"appid........");
                const redirectUrl = encodeURIComponent(currentUrl);
                // 正确构造授权 URL
                const wxAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.app_id}&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_userinfo&state=%7b%22w_id%22%3a1%2c%22xx%22%3a11%2c%22b%22%3a%22c%22%7d&component_appid=${res.data.component_app_id}#wechat_redirect`;
                // 跳转到微信授权页面
                console.log(3333333,wxAuthUrl);
                window.location.href=wxAuthUrl; 
            } catch (error) {
                console.log(error);;
            }
        },
        async init(){
            //检查链接是否失效
            let isLinkExpired = false; 
            if(this.params.expire_code){
                isLinkExpired = await this.checkMapUrlexpire();
            }

            //链接已失效，获取分享人信息
            if(isLinkExpired){
                await this.getShareUserInfo();
                if(this.shareUserInfo){
                    if(!this.shareUserInfo.name){
                        this.shareUserInfo.name = '申请权限';
                    }
                    this.shareUserInfo._firstNameWord = this.shareUserInfo.name.substring(0, 1);
                    this.isLinkExpired = true;
                }
            }
            
            //链接未失效，获取地图信息
            if(!this.isLinkExpired){
                await this.getMapDetail();
            }
            this.loading = false;
        },
        //检查地图链接是否失效
        checkMapUrlexpire(){
            return new Promise(async resolve => {
                try{
                    const res = await http1.checkMapUrlexpire(this.params.expire_code, {});
                    if (res.status == 200) {
                        if(res?.data?.code == 1){
                            resolve(false);
                        }
                    } else if (res.statusCode == 401) {
                        console.log(res);
                    }
                }catch(e){
                    console.error(e);
                }
                resolve(true);
            })
        },
        //获取地图详情
        getMapDetail(){
            return new Promise(async resolve => {
                try{
                    const res = await http1.getMapPluginDetail(this.params.plugin_id);
                    if (res.status == 200) {
                        this.mapDetail = res.data;
                        document.title = this.mapDetail.title || this.mapDetail.share_title ||  '领取资料';
                        resolve(true);
                    } else if (res.statusCode == 401) {
                        console.log(res);
                    }
                }catch(e){
                    console.error(e);
                }
                resolve(false);
            })
        },
        //获取分享者信息
        getShareUserInfo(){
            return new Promise(async resolve => {
                try{
                    const res = await http1.getShareUserInfo(this.params.share_id);
                    if (res.status == 200) {
                        this.shareUserInfo = res.data.user_info;
                        resolve(true);
                    } else if (res.statusCode == 401) {
                        console.log(res);
                    }
                }catch(e){
                    console.error(e);
                }
                resolve(false);
            })
        },
        // 获取URL中查询参数code的方法
        getCodeFromUrl() {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get('code');
        },
        redirectH5authorization(code){
            let params ={
                code:code
            }
            this.$http.redirectH5authorization(params).then(res=>{
                  if(res.status==200){
                  }
            })
        },
       
        //联系我
        contactMe(){
            window.location.href="tel:"+this.shareUserInfo.phone
        }
    }
};
</script>
<style scoped lang="scss">
/deep/ .el-loading-mask{
    top:38px;
    bottom: auto;
}
/deep/ .el-loading-spinner .circular{
    width: 20px;
    height: 20px
}
/deep/ .el-loading-spinner .path{
    stroke: #999;
}
/deep/ .el-loading-spinner .el-loading-text{
    color: #999;
    letter-spacing: 1px;
}
.share-user-card{
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    padding: 0 20px;
    border-bottom: 1px solid #e6e6e9;
    .info{
        display: flex;
        align-items: center;
        justify-content: center;
        .avatar{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            font-weight: 600;
            font-size: 16px;
            color: #fff;
            background-color: #2d84fb;
        }
        .name{padding-left: 10px;}
    }
}
.link-expired-cont{
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    height: 35vh;
    p{
        text-align: center;
        line-height: 2;
        color: #f60;
    }
}


.login-container {
  user-select: none;
}
@media screen and (max-width: 768px) {
  // 移动端适配rem
  .login-container {
    width: 100%; /* 适应屏幕宽度 */
    height: 100%;
    // padding: .625rem;
    box-sizing: border-box;
    overflow-y: scroll;
    .login-top {
      width: 100%;
      img {
        width: 100%;
        object-fit: cover;
      }
    }
    .nav {
      width: 100%;
      padding-bottom: 6.25rem;
      box-sizing: border-box;
      .nav-as {
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 12px;
      }
      // margin-top: .625rem;
      img {
        padding: 0.1%;
        box-sizing: border-box;
      }
    }
  }
  .imgWeb {
    width: 100%;
    display: block;
    pointer-events: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -moz-user-select: none;
    user-select: none;
  }
 
}
</style>