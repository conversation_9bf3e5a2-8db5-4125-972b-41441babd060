<template>
  <div
    class="txt"
    :class="{
      report: type == 0,
      visite: type == 1,
      subscribe: type == 2,
      isBuy: type == 3,
      isRengou: type == 4,
      isDeal: type == 5,
      failure: type == 10,
    }"
  >
    {{ changeStr(txt) }}
  </div>
</template>

<script>
export default {
  props: {
    type: [String, Number],
    load_text: {
      type: Object,
      default: () => {
        return {
          0: "待审核",
          1: "已报备",
          2: "已到访",
          3: "已认筹",
          4: "已认购",
          5: "已成交",
          10: "已无效",
        };
      },
    },
  },
  data() {
    return {
      txt: "",
    };
  },
  created() {},
  computed: {},
  methods: {
    changeStr() {
      if (this.type) {
        return this.load_text[this.type];
      } else if (this.type == 0) {
        return this.load_text[this.type];
      }
    },
  },
};
</script>

<style scoped lang="scss">
.txt {
  margin-left: 10px;
  font-size: 12rpx;
  padding: 5px;
  width: 100px;
  &.report {
    color: #ff0000;
    background-color: #fef0f0;
    border-color: #fde2e2;
    border-radius: 4px;
  }
  &.visite {
    color: #409eff;
    border: 1px solid #d9ecff;
    background-color: #ecf5ff;
    border-radius: 4px;
  }
  &.subscribe {
    color: #67c23a;
    background-color: #f0f9eb;
    border-color: #e1f3d8;
    border-radius: 4px;
  }
  &.isBuy {
    color: #fff;
    background-color: #9c9391;
    border-color: #e1f3d8;
    border-radius: 4px;
  }
  &.isRengou {
    color: #67c23a;
    background-color: #f0f9eb;
    border-color: #e1f3d8;
    border-radius: 4px;
  }
  &.isDeal {
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #e6a23c;
    border-radius: 4px;
  }
  &.failure {
    background-color: #fef0f0;
    border-color: #fde2e2;
    color: #f56c6c;
    border-radius: 4px;
  }
}
</style>
