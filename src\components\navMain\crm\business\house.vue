<template>
  <div>
    <div v-if="is_house_show">
      <div class="table-top-box div row">
        <div class="t-t-b-left div row flex-1">
          <el-cascader placeholder="请选择部门" style="width: 210px; margin-right: 16px" v-model="params.department_id"
            clearable :options="department_list" :props="{
      value: 'id',
      label: 'name',
      children: 'subs',
      emitPath: false,
    }" @change="onPageChange(1)"></el-cascader>
          <el-input style="width: 200px" placeholder="请输入姓名" v-model="params.user_name"></el-input>
          <el-button style="margin: 0 16px" type="primary" class="el-icon-search"
            @click="onPageChange(1)">搜索</el-button>
          <el-date-picker v-if="website_id != 176" style="width: 210px; margin-right: 16px" v-model="p_time"
            type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss" @change="changeTimeRange">
          </el-date-picker>
          <el-button type="primary" v-if="false"> 数据导出</el-button>
        </div>
        <div class="t-t-b-right div row f-c">
          <span class="f f1"></span>第一 <span class="f f2"></span>第二
          <span class="f f3"></span>第三
        </div>
      </div>
      <!-- 
        自定义排序
       el-table  @sort-change="onSortChange"
       el-table-column sortable='custom'
       -->
      <el-table v-loading="is_table_loading" @sort-change="onSortChange" :data="tableData" border
        :header-cell-style="{ background: '#EBF0F7' }" :cell-style="cellStyle" :row-style="$TableRowStyle">
        <el-table-column label="成员" v-slot="{ row }" width="200px" fixed="left">
          <div class="name-box-cy div row">
            <div class="left">{{ row.user_name[0] }}</div>
            <div class="right">
              <div class="c">{{ row.user_name }}</div>
              <div class="b">{{ row.department }}</div>
            </div>
          </div>
        </el-table-column>
        <el-table-column align="center" sortable="custom" label="录入" prop="lr.num">
        </el-table-column>
        <el-table-column align="center" sortable="custom" label="维护" prop="wh.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="钥匙" prop="ys.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="实勘" prop="sk.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="委托" prop="wt.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="VIP" prop="vip.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="我司成交" prop="cj.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="无效" prop="wx.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="A级" prop="level_a.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="B级" prop="level_b.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="C级" prop="level_c.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="维护资料" prop="whzl.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="跟进" prop="gj.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="查看门牌" prop="ckmp.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="查看电话" prop="ckdh.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="跟进异常" prop="dhwgj.num"></el-table-column>
        <el-table-column align="center" sortable="custom" label="更新标签" prop="gxbq.num"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="params.total" :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
      </el-pagination>
    </div>
    <myEmpty v-else desc="当前用户不可查看"></myEmpty>
  </div>
</template>

<script>
import myEmpty from "@/components/components/my_empty.vue";
export default {
  components: {
    myEmpty,
  },
  props: {
    is_house_show: {
      type: [Boolean, Number],
      default: 1,
    },
    house_params: {
      type: [Object],
      default: () => { return { page: 1 } },
    }
  },
  watch: {
    "house_params.page": {
      handler(val) {
        console.log(val, "111");
        if (val) {
          this.params.page = val

        } else {
          this.params.page = 1
        }
        this.$forceUpdate()


      },
      immediate: true
    }
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
        department_id: "",
        b_date: "",
        e_date: "",
        order: "",
        date_type: 3
      },
      p_time: "",
      tableData: [],
      is_table_loading: false,
      department_list: [],
      website_id: ''
    };
  },
  created() {
    this.website_id = this.$route.query.website_id
  },
  mounted() {
    if (this.is_house_show) {
      this.getCrmIntelligentRateList();
      this.getCrmDepartmentList();
    }
  },
  methods: {
    /* eslint-disable */
    // 获取部门列表
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    /**
     *  row: 行数据
     *  column :列数据
     *  rowIndex: 行下标
     *  columnIndex: 列下标
     * */

    cellStyle({ row, column, rowIndex, columnIndex }) {
      // return `background-color:#BEDCFF;`;
      // return `background-color:#FFEBA5;`;
      // return `background-color:#FFB88D;`;
      if (column.label === "录入") {
        return this.setStyle(row.lr.rank);
      }
      if (column.label === "维护") {
        return this.setStyle(row.wh.rank);
      }
      if (column.label === "钥匙") {
        return this.setStyle(row.ys.rank);
      }
      if (column.label === "实勘") {
        return this.setStyle(row.sk.rank);
      }
      if (column.label === "委托") {
        return this.setStyle(row.wt.rank);
      }
      if (column.label === "VIP") {
        return this.setStyle(row.vip.rank);
      }
      if (column.label === "我司成交") {
        return this.setStyle(row.cj.rank);
      }
      if (column.label === "无效") {
        return this.setStyle(row.wx.rank);
      }
      if (column.label === "A级") {
        return this.setStyle(row.level_a.rank);
      }
      if (column.label === "B级") {
        return this.setStyle(row.level_b.rank);
      }
      if (column.label === "C级") {
        return this.setStyle(row.level_c.rank);
      }
      if (column.label === "维护资料") {
        return this.setStyle(row.whzl.rank);
      }
      if (column.label === "跟进") {
        return this.setStyle(row.gj.rank);
      }
      if (column.label === "查看门牌") {
        return this.setStyle(row.ckmp.rank);
      }
      if (column.label === "查看电话") {
        return this.setStyle(row.ckdh.rank);
      }
      if (column.label === "跟进异常") {
        return this.setStyle(row.dhwgj.rank);
      }
      if (column.label === "更新标签") {
        return this.setStyle(row.gxbq.rank);
      }
    },
    setStyle(rank) {
      if (rank === 1) {
        return `background-color:#ffb88d;`;
      }
      if (rank === 2) {
        return `background-color:#bedcff;`;
      }
      if (rank === 3) {
        return `background-color:#ffeba5;`;
      }
    },
    changeTimeRange(e) {
      this.params.b_date = e ? e[0] : "";
      this.params.e_date = e ? e[1] : "";
      this.params.page = 1;
      this.getCrmIntelligentRateList();
    },

    getCrmIntelligentRateList() {
      let params = Object.assign({}, this.params, this.house_params)
      this.is_table_loading = true;
      this.$http
        .getCrmIntelligentRateList_new({ params })
        .then((res) => {
          console.log(res);
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
            if (params.page == 1) {
              this.$emit("getDataOk", res.data)
            }

          }
        });
    },
    onPageChange(e) {
      this.params.page = this.house_params.page;
      this.$emit("getData", e)
      // this.params.page = e;
      // this.getCrmIntelligentRateList();
    },
    // 自定义排序
    onSortChange({ column, prop, order }) {
      var prop_1 = prop.split(".")[0];
      if (order === "descending") {
        // 大->小
        this.params.order = prop_1 + "_down";
      } else if (order === "ascending") {
        this.params.order = prop_1 + "_up";
      } else {
        this.params.order = "";
      }
      this.$emit("sortData")
      // this.params.page = 1;
      // this.getCrmIntelligentRateList();
    },
  },
};
</script>

<style scoped lang="scss">
.f-c {
  color: #2e3c4e;
  font-size: 14px;
  display: flex;
  align-items: center;

  .f {
    width: 20px;
    height: 8px;
    margin: 0 10px;

    &.f1 {
      background: #ffb88d;
    }

    &.f2 {
      background: #bedcff;
    }

    &.f3 {
      background: #ffeba5;
    }
  }
}

.name-box-cy {
  align-items: center;

  .left {
    margin-right: 7px;
    background: #2d84fb;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: #fff;
    line-height: 24px;
    text-align: center;
    font-size: 14px;
    flex-shrink: 0;
  }

  .right {
    font-size: 12px;

    .c {
      color: #2e3c4e;
    }

    .b {
      color: #8a929f;
    }
  }
}
</style>
