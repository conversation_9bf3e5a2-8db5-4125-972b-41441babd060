<template>
    <div v-fixed-scroll="48">
		<div class="filter-wrapper">
			<el-form inline size="medium">
				<el-form-item label="抖音名称">
					<el-input placeholder="请输入" v-model="params.name"></el-input>
				</el-form-item>

				<el-form-item label="抖音号">
					<el-input placeholder="请输入" v-model="params.ies_uniq_id"></el-input>
				</el-form-item>

				<el-form-item label="主播姓名">
					<el-input placeholder="请输入" v-model="params.user_name"></el-input>
				</el-form-item>

				<el-form-item label="">
					<el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
				</el-form-item>
			</el-form>
		</div>
        <myTable :table-list="list" :header="columns" tooltipEffect="light" v-loading="loading"
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
            ref="myTable"></myTable>

		<div class="tab-content-footer">
			<div></div>
			<el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="count"
			:page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
			@current-change="onPageChange" @size-change="handleSizeChange">
			</el-pagination>
		</div>

		
		<updateLiveAccount v-if="dialogs.updateLiveAccount" ref="updateLiveAccount"/>

	</div>
</template>
<script>
import myTable from "@/components/components/my_table";
import updateLiveAccount from "./updateLiveAccount.vue";
export default {
	components: {
		myTable, updateLiveAccount
	},
	data() {
		return {
			multipleSelection: [],
			loading: false,
			roomAccountList: [],
			params: {
				page: 1,
				per_page: 10,
				ies_uniq_id: '',
				name: '',
				user_name: '',
			},
			count: 0,
			list: [],
			dialogs: {
				updateLiveAccount: false
			}
		}
	},
	computed: {
		columns(){
			return [
				{ prop: "name", label: "抖音名称", minWidth: 200, },
				{ prop: "ies_uniq_id", label: "抖音号", minWidth: 200, },
				{ prop: "user_name", label: "主播姓名", minWidth: 200, },
				{ prop: "id", label: "操作", minWidth: 240, render: (h, {row})=>{
					return (
						<div>
							{ row.admin_id? 
								<el-popconfirm title="确定取消关联吗？" onOnConfirm={()=>this.cancleLiveAccount(row)}>
									<el-link type="primary"  slot="reference">取消关联人事账号</el-link>
								</el-popconfirm>
								: <el-link type="primary" onClick={()=>this.openUpdateLiveAccountPage(row)}>关联人事账号</el-link>
							}
						</div>
					)
				} },
			];
		}
	},
	mounted(){
		let pagenum = localStorage.getItem( 'pagenum')
		this.params.per_page = Number(pagenum)||10
		this.getList();
	},
	methods: {
		async openUpdateLiveAccountPage(row){
			this.dialogs.updateLiveAccount = true;
			this.$nextTick(()=>{
				this.$refs.updateLiveAccount.open(row).onSuccess(()=>{
					this.getList();
				});
			})
		},
		async cancleLiveAccount({id}){
			const res = await this.$http.cancelDouyinLiveAccount(id).catch(()=>{})
			if(res.status == 200){
				this.$message.success(res.data?.msg || '取消关联成功');
				this.getList();
			}
		},
		async getList(){
			const params = {...this.params};
			this.loading = true;
			const res = await this.$http.douyinLiveAccountList(params).catch(()=>{});
			this.loading = false;
			if(res.status == 200){
				this.count = res.data?.total || 0;
				this.list = res.data?.data || [];
			}
		},
		search(){
			this.params.page = 1;
			this.getList();
		},
		goRoomUserList({room_id}){
			this.$emit('room-change', room_id);
		},
		onPageChange(e){
			this.params.page = e;
			this.getList();
		},
		handleSizeChange(e){
			this.params.per_page = e;
			this.search();
		}
	}
}
</script>
<style  scoped lang="scss">
.filter-wrapper{
	padding: 16px 16px 12px;
}
</style>