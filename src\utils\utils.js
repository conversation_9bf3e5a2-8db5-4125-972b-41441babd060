const Utils = {
	/**
	 * 组装url参数
	 */
	buildHttpQuery(obj) { 
		const queryArr =[];
		for(let key in obj){ 
			let value = obj[key]; 
			key = encodeURIComponent(key);

			if(Array.isArray(value)){
				for (const val of value) { 
					queryArr.push(key + '=' + (val == null ? '' : encodeURIComponent(String(val)))); 
			   	} 
			}else{
				queryArr.push(key + '=' + (value == null ? '' : encodeURIComponent(String(value)))); 
			}
	   } 
	   return queryArr.join('&');
	},

	/**
	 * 解析 url 参数
	 */
	parseUrlQuery(query){
		const params = {};
		const queryArr = query.split("&");
		for(const item of queryArr){
			const [key, val] = item.split("=");
			params[decodeURIComponent(key)] = decodeURIComponent(val);
		}
		return params;
	},
	/**
	 * 是否数值或字符串数字
	 */
	isNumeric(num){
		return typeof num === 'number' || num  && num !== true && !isNaN(num);
	},
	/**
	 * 判断是否非空数组
	 */
	isNotEmptyArray(arr){
		return Array.isArray(arr) && arr.length ? true : false;
	},
	/** 
	 * 非阻塞暂停
	 */
	sleep(delay) { 
		return new Promise((resolve) => setTimeout(resolve, delay)); 
	},
	/**
	 * 防抖
	 */
	debounce(fn, delay = 300){
		let timer = null
		return function (...args) {
			if(timer != null){
				clearTimeout(timer)
				timer = null
			}
			timer = setTimeout(()=>{
				fn.call(this, ...args)
			}, delay);
		}
	},
	/**
	 * 节流
	 */
	throttle (fn, delay = 300) {
		let timer = null
		return function (...args) {
			if(timer == null){
				timer = setTimeout(() => {
					fn.call(this, ...args)
					clearTimeout(timer)
					timer = null
				}, delay);
			}
		}
	},
	formatNumber(num) {
		return num && num.toString().indexOf('.') != -1 ? num.toString().replace(/(\d)(?=(\d{3})+\.)/g, function($0, $1) {
			return $1 + ",";
		}) : String(num).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
	},
	/**
     * 格式化日期时间
     */
    formatDate(value, format){
        let date = value != null ? new Date(value) : new Date(),
			year, month, day, hours, minutes, seconds;
		
		if(!date.getTime()){
			return '';
		}
		if(!format){
			format = 'YYYY-MM-DD HH:ii';
		} 
		
		year = date.getFullYear();
		month = date.getMonth() + 1;
		day = date.getDate();
		hours = date.getHours();
		minutes = date.getMinutes();
		seconds = date.getSeconds();
		
		format = format.replace('YYYY', year);
		format = format.replace('MM', month > 9 ? month : '0'+month);
		format = format.replace('M', month);
		format = format.replace('DD', day > 9 ? day : '0'+day);
		format = format.replace('D', day);
		format = format.replace('HH', hours > 9 ? hours : '0'+hours);
		format = format.replace('H', hours);
		format = format.replace('ii', minutes > 9 ? minutes : '0'+minutes);
		format = format.replace('i', minutes);
		format = format.replace('ss', seconds > 9 ? seconds : '0'+seconds);
		format = format.replace('s', seconds);
		return format;
    },

	scrollLeft: function(elem,x,duration){
		if(duration && duration > 0){
			let left = elem.scrollLeft,
				step = Math.floor(((x-left) / duration)*16.7);
			if(step != 0){
				requestAnimationFrame(function stop(){
					if(left > x){
						left += step;
						if(left < x) left = x;
					}else{
						left += step;
						if(left > x) left = x;
					}

					elem.scrollLeft = left;
					if(left != x){
						requestAnimationFrame(stop);
					}
				});
			}
		}else{
			elem.scrollLeft = x;
		}
	}
};

//更多工具方法
//Utils.xxxx = function(){}


export default Utils;