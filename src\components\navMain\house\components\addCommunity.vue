<template>
  <div>
    <el-form :model="form_info" label-position="right" label-width="100px">
      <el-form-item label="小区名称">
        <el-input
          :placeholder="`请输入小区名称`"
          style="width: 300px"
          v-model="form_info.title"
        ></el-input>
      </el-form-item>

      <el-form-item label="商圈">
        <template>
          <el-select
            v-model="form_info.region_id"
            v-if="!showWrite"
            style="width: 200px"
          >
            <el-option
              v-for="item in regionList"
              :key="item.id"
              :label="item.region_name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px" @click="write">{{
            showWrite ? "选择商圈" : "手动输入"
          }}</el-button>
        </template>
      </el-form-item>
      <el-form-item label="输入商圈" v-if="showWrite">
        <el-input
          :placeholder="`请输入商圈名`"
          style="width: 300px"
          v-model="form_info.region_name"
        ></el-input>
      </el-form-item>
      <el-form-item size="base" label="小区位置" prop="address">
        <el-button
          @click="markMap"
          icon="el-icon-map-location"
          type="primary"
          plain
          style="margin-right: 5px"
          >点击标注</el-button
        >
        <el-input
          :value="
            form_info.lat &&
            form_info.lng &&
            form_info.lat != 0 &&
            form_info.lng != 0
              ? Number(form_info.lat).toFixed(6) +
                ',' +
                Number(form_info.lng).toFixed(6)
              : ''
          "
          disabled
          placeholder="请点击标注"
          style="width: 177px"
        ></el-input>
      </el-form-item>
      <el-form-item label="小区地址">
        <el-input
          :placeholder="`请输入小区地址`"
          style="width: 300px"
          v-model="form_info.addr"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          @click="onCreateData"
          >确认</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 地图容器 -->
    <el-dialog
      title="选择位置"
      :visible.sync="dialogVisibleMap"
      class="dialog-map"
      append-to-body
    >
      <template v-if="dialogVisibleMap">
        <div>
          <el-input
            v-model="temp.addr"
            style="width: 300px"
            placeholder="请输入市区/市县后在输入详细楼盘地址"
          ></el-input>
          <el-button type="primary" @click="search">搜索</el-button>
        </div>
        <div
          id="container"
          style="width: auto; height: 350px; margin: 20px 0"
        ></div>
        <div
          class="list"
          style="width: auto; height: 150px; overflow-y: auto; margin: 20px 0"
        >
          <div
            class="list_item flex-row items-center"
            :class="{ active: item.id == currentSelectId }"
            v-for="item in searchList"
            :key="item.id"
            @click="selectCity(item)"
          >
            <div class="item-left">
              <div class="title">{{ item.name }}</div>
              <div class="addr">{{ item.address }}</div>
            </div>
          </div>
        </div>
        <div slot="footer">
          <span class="dialog-footer">
            <el-button @click="dialogVisibleMap = false">取 消</el-button>
            <el-button type="primary" @click="confirmSelect">确 定</el-button>
          </span>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";

/* eslint-disable */
var citylocation;
export default {
  props: {
    dialogTitle: {
      type: String,
      default: ''
    },
    form: {
      type: Object,
      default: {
        title: "",
        addr: "",
        lat: "",
        lng: "",
        region_id: '',
        region_name: ''
      }
    }

  },
  computed: {
    ...mapState(["website_info"]),
  },
  watch: {
    form: {
      handler(val) {
        this.showWrite = false
        this.form_info = JSON.parse(JSON.stringify(val))
        this.form_info.region_id = this.form_info.region_id ? +this.form_info.region_id : ''
      },
      immediate: true
    }
  },
  data() {
    return {
      form_info: {},
      is_button_loading: false,
      dialogVisibleMap: false,
      search_addr: "",
      lat: "",
      lng: "",
      temp: {
        lat: "",
        lng: "",
        addr: ""
      },
      markersArray: [],
      currentSelectId: "",
      searchList: [],
      regionList: [],
      showWrite: false
    }
  },
  created() {
    this.getAreaRegion()
  },
  methods: {
    onCreateData() {
      this.is_button_loading = true;
      if (this.dialogTitle === "addData") {
        this.$ajax.house.addAuditCommunity(this.form_info).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.$emit("success", res.data)
          }
        });
      } else {
        this.$ajax.house.editAuditCommunity(this.form_info).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.$emit("success", {})
          }
        });
      }
    },
    getAreaRegion() {
      this.$ajax.house.getAreaRegion().then(res => {
        console.log(res, "regionList");
        if (res.status == 200) {
          this.regionList = res.data
        }
      })
    },
    write() {
      if (!this.showWrite) {
        this.form_info.region_id = ''
      } else {
        this.form_info.region_name = ''
      }
      this.showWrite = !this.showWrite
    },
    markMap() {
      // if (!this.website_info.map_lat && !this.website_info.map_long) {
      //   this.getTXLocation();
      // }
      // setTimeout(() => {
      this.map = null
      this.temp.lat = this.form_info.lat || ''
      this.temp.lng = this.form_info.lng || ''
      this.addr = this.form_info.addr || ''
      this.dialogVisibleMap = true;
      this.$nextTick(() => {
        this.init();
      })
      // }, 400);
    },
    init() {
      var that = this;
      if (that.website_info.map_lat && that.website_info.map_long) {
        var myLatlng = new qq.maps.LatLng(
          that.temp.lat || that.website_info.map_lat,
          that.temp.lng || that.website_info.map_long
        );
      } else {
        myLatlng = new qq.maps.LatLng(26.326248944008693, 116.35539315640926);
      }
      var myOptions = {
        zoom: 16,
        center: myLatlng,
      };
      that.mapLoading = false;
      that.map = new qq.maps.Map(
        document.getElementById("container"),
        myOptions
      );
      citylocation = new qq.maps.CityService({
        complete: function (results) {
          // console.log(results);
          that.positions = results.detail.name
          // console.log(_this.positions);

        }
      });
      citylocation.searchLocalCity();
      var marker = new qq.maps.Marker({
        map: this.map,
        position: new qq.maps.LatLng(
          this.temp.lat,
          this.temp.lng
        ),
      });
      this.markersArray.push(marker);
      if (this.markersArray.length > 1) {
        for (let i = 0; i < this.markersArray.length - 1; i++) {
          this.markersArray[i].setMap(null); // 清除标记
        }
      }

      //获取点击后的地址
      qq.maps.event.addListener(that.map, "click", function (event) {
        //通过坐标来显示地图地址
        console.log(event);
        let geocoder = new qq.maps.Geocoder({
          complete: function (result) {
            console.log(result);
            that.addr = result.detail.address
            console.log(that.temp.addr);
          }
        });
        var coord = new qq.maps.LatLng(event.latLng.lat, event.latLng.lng);
        geocoder.getAddress(coord);

        // that.getAddCode = new qq.maps.Geocoder({
        //   complete: function (result) {
        //     that.temp.addr =
        //       result.detail.addressComponents.city +
        //       result.detail.addressComponents.district +
        //       result.detail.addressComponents.town +
        //       result.detail.addressComponents.street;
        //   },
        // });
        // 获取点击后的地图坐标
        that.temp.lat = event.latLng.lat;
        that.temp.lng = event.latLng.lng;
        var marker = new qq.maps.Marker({
          map: that.map,
          position: new qq.maps.LatLng(
            that.temp.lat,
            that.temp.lng
          ),
        });
        that.markersArray.push(marker);
        if (that.markersArray.length > 1) {
          for (let i = 0; i < that.markersArray.length - 1; i++) {
            that.markersArray[i].setMap(null); // 清除标记
          }
        }

        // that.getAddressCode();
      });

      //调用地址显示地图位置并设置地址
      that.getAddress = new qq.maps.Geocoder({
        complete: function (result) {
          that.map.setCenter(result.detail.location);
          that.temp.lng =
            result.detail.location.lng;
          that.temp.lat = result.detail.location.lat;
          var marker = new qq.maps.Marker({
            map: that.map,
            position: result.detail.location,
          });
        },
      });
    },
    //通过地址获得位置  搜索功能
    getAddressKeyword() {
      //通过getLocation();方法获取位置信息
      this.getAddress.getLocation(this.temp.addr);
      // 调用自带的接口;
    },
    search() {
      let _this = this
      var latlngBounds = new qq.maps.LatLngBounds();
      var searchService = new qq.maps.SearchService({
        location: _this.positions,
        //设置搜索页码为1
        // pageIndex: 0,
        //设置每页的结果数为5
        // pageCapacity: 10,
        //设置展现查询结构到infoDIV上
        // panel: document.getElementById('infoDiv'),
        //设置动扩大检索区域。默认值true，会自动检索指定城市以外区域。
        autoExtend: true,
        complete: function (results) {
          if (results.detail.pois) {
            // if (markers) {
            //   for (i in markers) {
            //     markers[i].setMap(null);
            //   }
            // }
            // markers.length = 0;

            var pois = results.detail.pois;
            // for (var i = 0, l = pois.length; i < l; i++) {
            //   var poi = pois[i];
            // latlngBounds.extend(poi.latLng);
            //   var marker = new qq.maps.Marker({ map: _this.Tenmap, position: poi.latLng }); marker.setTitle(poi.name);
            //   markers.push(marker);
            // }

            // _this.Tenmap.fitBounds(latlngBounds);
            _this.searchList = pois
          } else {
            console.log("没找到您要搜索的信息");
          }

        },
        error: function (err) {
          console.log(err);

        }
      });
      searchService.search(_this.temp.addr);
    },
    // 通过坐标获得地址
    getAddressCode() {
      var lat = parseFloat(this.temp.lat);
      var lng = parseFloat(this.temp.lng);
      var latLng = new qq.maps.LatLng(lat, lng);
      //调用获取位置方法
      this.getAddCode.getLocation(this.getAddress);
      console.log(this.getAddCode.getLocation(this.getAddress), 11111);
    },
    // 获取当前定位，/市
    getTXLocation() {
      var _this = this;
      var geolocation = new qq.maps.Geolocation(
        "GVZBZ-47RCP-D6LDP-LCA5V-PUQFQ-QMB2V",
        "yunbaobei"
      );
      var options = { timeout: 8000 };
      var latitude, longitude, address;
      if (_this.website_info.map_lat && _this.website_info.map_long) {
        _this.temp.lat = _this.website_info.map_lat;
        _this.temp.lng = _this.website_info.map_long;
        _this.mapLoading = false;
        this.init();
      } else {
        geolocation.getLocation(showPosition, showErr, options);
        function showPosition(position) {
          if (position) {
            _this.mapLoading = false;
          }
          latitude = position.lat;
          longitude = position.lng;
          address = position.nation + position.province + position.city;
          _this.getShopmsg(latitude, longitude, address); //获取到经纬度后的操作
        }
        function showErr(position) { }
      }
    },
    // 获取经纬度后的操作
    getShopmsg(lat, lng, address) {
      this.temp.lat = lat;
      this.temp.lng = lng;
      this.addr = address;
      this.init();
    },
    selectCity(item) {
      let that = this
      this.currentSelectId = item.id
      that.temp.lat = item.latLng.lat;
      that.temp.lng = item.latLng.lng;
      this.addr = item.address

      let marker = new qq.maps.Marker({
        map: that.map,
        position: new qq.maps.LatLng(
          that.temp.lat,
          that.temp.lng
        ),
      });
      that.markersArray.push(marker);
      if (that.markersArray.length > 1) {
        for (let i = 0; i < that.markersArray.length - 1; i++) {
          that.markersArray[i].setMap(null); // 清除标记
        }
      }
      console.log(that.markersArray);
      // this.getShopmsg(that.temp.lat, that.temp.lng, this.addr)


    },
    confirmSelect() {
      this.form_info.lat = this.temp.lat
      this.form_info.lng = this.temp.lng
      this.form_info.addr = this.addr
      this.dialogVisibleMap = false
    }
  }
}
</script>

<style scoped lang="scss">
.list_item {
  padding: 15px 10px;
  border-bottom: 1px solid #eee;
  .addr {
    font-size: 12px;
  }
  .title {
    font-size: 14px;
  }
  &.active {
    color: #409eff;
  }
}
</style>