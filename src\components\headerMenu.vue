<template>
  <div class="header div row">
    <div class="right div row">
      <div class="left">
        <img src="https://img.tfcs.cn/backup/static/admin/crm/static/menu/crm-icon.png" alt />
        <!-- {{ website_info.name || "工作台" }} -->
      </div>
      <div class="back">
        <div class="b-tabs div row" v-if="type_list.length">
          <div class="b-t-item" :class="{ isactive: item.id == is_type_check }" v-for="item in type_list" :key="item.id"
            @click="onClickType(item)">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="right_r div row">
        <div class="in-station-info">
          <el-popover v-model="message_visible" popper-class="message_popover" placement="bottom" trigger="hover"
            width="374">
            <router-link slot="reference" to="">
              <i class="el-icon-bell"></i>
            </router-link>
            <message-list :chat_list="chat_list" :chat_total="chat_total" @onclickMessage="message_visible = false">
            </message-list>
          </el-popover>
        </div>
        <div class="admin div row">
          <!-- 头像 -->
          <el-popover v-if="false" placement="bottom" width="260" trigger="click">
            <div style="text-align: center; padding: 10px">
              <h3>
                {{
                                website_info.wx_work_corp_id ? "腾房云报备" : "腾房云轮席客服"
                                }}
              </h3>
              <p style="padding-top: 10px; color: rgb(45, 132, 251)">
                {{
                                website_info.wx_work_corp_id
                                ? "专属客户经理"
                                : "技术问题请优先小群反馈"
                                }}
              </p>
              <div style="width: 140px; height: 140px; margin: 18px auto 12px">
                <img :src="`https://img.tfcs.cn/backup/static/admin/header/${
                                    website_info.wx_work_corp_id
                                      ? 'customer_service_qrcode_qy'
                                      : 'customer_service_qrcode'
                                  }.jpg`" alt="" width="100%" height="100%" />
              </div>
              <p>
                {{
                                website_info.wx_work_corp_id
                                ? "联系电话：13581811263"
                                : " 微信扫码联系 接待时间 8：30-17：30"
                                }}
              </p>
            </div>
            <el-button slot="reference" size="mini" style="padding: 0 10px" round type="primary"><span
                class="el-icon-headset" style="margin-right: 5px"></span>在线客服</el-button>
          </el-popover>

          <el-avatar :size="40" src="@/assets/icon-tx.png" @error="errorHandler">
            <img src="@/assets/icon-tx.png" />
          </el-avatar>
          <el-dropdown @command="handleCommand" v-if="!is_company_login">
            <span class="el-dropdown-link" style="color: #fff">
              {{ admin_list.user_name }}
              <i class="el-icon-caret-bottom"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">修改密码</el-dropdown-item>
              <el-dropdown-item v-if="userName == '站长' || configUid" command="config">平台配置</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <!-- 已经授权企业微信隐藏授权企业微信按钮 -->

        <!-- v-if="!website_info.wx_work_corp_id" -->
        <span v-if="is_wx_work" style="margin-left: 20px" class="logout" @click="loginOut"><img
            src="https://img.tfcs.cn/backup/static/admin/crm/static/menu/logout.png" alt="" />
          退出</span>
      </div>
      <!-- <el-button>通知中心</el-button>
      <el-select v-model="value" filterable placeholder="请选择">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>-->
    </div>
    <el-dialog title="修改密码" append-to-body :visible.sync="dialogPassword">
      <el-form :rules="rules3" ref="form_password" :model="form_password">
        <!-- <el-form-item
          label="请输入原密码"
          label-width="200px"
          prop="old_password"
        >
          <el-input v-model="form_password.old_password"></el-input>
        </el-form-item> -->
        <el-form-item label="请输入新密码" label-width="200px" prop="new_password">
          <el-input v-model="form_password.new_password"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" label-width="200px" prop="new_password_confirmation">
          <el-input v-model="form_password.new_password_confirmation"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogPassword = false">取 消</el-button>
        <el-button type="primary" @click="isModify">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog append-to-body :modal="true" :close-on-click-modal="false" :close-on-press-escape="false"
      :show-close="false" :visible.sync="is_dialog_ziliao" title="完善资料">
      <el-form label-width="100px" :model="form_info">
        <el-form-item label="姓名：">
          <el-input style="width: 300px" v-model="form_info.user_name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号：">
          <el-input style="width: 300px" maxlength="11" v-model="form_info.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="codestatus">
          <el-input
            style="width: 175px"
            maxlength="11"
            v-model="form_info.captcha"
            placeholder="请输入短信验证码"
          ></el-input>
          <el-button type="primary" size="medium"
          style="margin-left: 10px;"
          @click="phonecode">发送验证码</el-button>
        </el-form-item> -->
        <el-form-item label="选择部门：">
          <el-cascader style="width: 300px" :options="memberList" :props="{
                        value: 'id',
                        label: 'name',
                        children: 'subs',
                        emitPath: false,
                        multiple: true,
                      }" clearable v-model="form_info.department_id"></el-cascader>
        </el-form-item>
        <!--<el-form-item label="绑定角色：">
          <el-select
            style="width: 300px"
            v-model="form_info.role_names"
            multiple
            placeholder="请选择"
            :disabled="istrue"
          >
            <el-option
              v-for="item in roles_list"
              :key="item.id"
              :label="item.name === '站长' ? '创始人' : item.name"
              :value="item.name"
              :disabled="item.name === '站长'"
            ></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="margin: 0" @click="submitDataBindZiliao">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import MessageList from "@/components/components/MessageList.vue"
export default {
  components: {
    MessageList
  },
  data() {
    var validatePass5 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.form_password.new_password_confirmation !== "") {
          this.$refs.form_password.validateField("new_password_confirmation");
        }
        callback();
      }
    };
    var validatePass6 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.form_password.new_password) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      value: "",
      options: [],
      levelList: [],
      admin_list: {},
      dialogPassword: false,
      form_password: {
        // old_password: "",
        new_password: "",
        new_password_confirmation: "",
      },

      password_loading: false,
      rules3: {
        // old_password: [
        //   { required: true, message: "请输入原密码", trigger: "blur" },
        // ],
        new_password: [{ validator: validatePass5, trigger: "blur" }],
        new_password_confirmation: [
          { validator: validatePass6, trigger: "blur" },
        ],
      },
      auth_code: "",
      expires_in: "",
      is_wx_work: true, // 是否是企业微信环境
      is_company_login: false, // 是否是公司企业后台站点登录
      is_dialog_ziliao: false,
      form_info: {
        user_name: "",
        phone: "",
        department_id: "",
        role_names: "",
      },
      memberList: [], //部门列表
      roles_list: [],
      userName: "",
      configUid: false, // 控制用户访问平台配置
      message_visible: false, // 控制站内消息icon图标
      chat_list: [], // 站内聊天消息列表
      chat_total: 0, // 站内聊天消息个数(length)
      website_ids: 176, // 站点限制176
      codestatus:false,//发送验证码按钮
    };
  },
  props: {
    type_list: Array,
    is_type_check: [String, Number],
  },
  watch: {
    $route() {
      // this.getBreadcrumb();
    },
  },
  created() {
    this.website_ids = this.$route.query.website_id; // 站点限制176
    if (!localStorage.getItem("company_token")) {
      this.getadmin();
    } else {
      this.is_company_login = true;
    }
  },
  computed: {
    ...mapState(["website_info"]),
    istrue() {
      if (this.form_info.role_names.indexOf("站长") != -1) {
        return true;
      } else {
        return false;
      }
    },
  },
  mounted() {
    // if (!localStorage.getItem("website_id")) {
    //   window.localStorage.removeItem("TOKEN");
    //   localStorage.removeItem("user_name");
    //   localStorage.removeItem("website_id");
    //   this.$router.push("/login");
    // }
    if (this.$envjudge() === "com-wx-pc") {
      this.is_wx_work = false;
    }
  },
  methods: {
    ...mapMutations(["login"]),
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
        console.log(res.data);
      }
    },
    onClickType(item) {
      if (this.is_type_check == item.id) {
        return;
      }
      this.$emit("onClickType", item);
    },
    getBreadcrumb() {
      let matched = this.$route.matched.filter((item) => item.name);
      const first = matched[0];
      if (
        first &&
        first.name.trim().toLocaleLowerCase() !== "index".toLocaleLowerCase()
      ) {
        matched = [
          {
            path: "/web_overview",
            meta: {
              title:
                this.website_info.website_mode_category === 0
                  ? "云报备"
                  : this.website_info.website_mode_category === 1
                    ? "单楼盘"
                    : this.website_info.website_mode_category === 2
                      ? "微房产"
                      : "腾房云",
            },
          },
        ].concat(matched);
      }
      this.levelList = matched;
    },
    loginOut() {
      // this.$store.commit("login", ""); //更新userInfo
      // localStorage.removeItem("TOKEN");
      // localStorage.removeItem("user_name");
      // this.$router.push("/login");

      // 注销后 清除session信息 ，并返回登录页
      this.$confirm("是否退出登录", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认退出登录
          if (localStorage.getItem("company_token")) {
            this.$router.push(
              `companyLogin?website_id=${localStorage.getItem("website_id")}`
            );
          } else {
            this.$router.push(
              `login`
            );
          }
          localStorage.removeItem("TOKEN");
          localStorage.removeItem("company_token");
          localStorage.removeItem(
            "admin_token_" + localStorage.getItem("website_id")
          );
          localStorage.removeItem("user_name");
          localStorage.removeItem("website_id");
          localStorage.removeItem("website_crm");
          sessionStorage.removeItem("top_select_id");
          sessionStorage.removeItem("top_menu_info");
          // 判断是否存在接跳转连接
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    // 头像展示
    errorHandler() {
      return true;
    },
    // 获取信息展示
    getadmin() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.admin_list = res.data;
          if (this.admin_list.roles.length) {
            this.userName = this.admin_list.roles[0].name
          } else {
            this.userName = ''
          }
          this.showSettingDia(res.data.id);
          if (!this.$store.state.website_info.website_id) return

          // 没有联系方式，部门，角色弹出完善资料
          if (
            !res.data.user_name ||
            !res.data.phone ||
            !res.data.wx_work_department_id ||
            !res.data.roles
          ) {

            this.$http.setDefaultDepart().then(() => { })
            this.form_info.department_id = res.data.wx_work_department_id
              .split(",")
              .map((item) => Number(item));
            this.is_dialog_ziliao = true;
            this.getDepartment();
            this.getWebsiteRoles();
          }
          this.form_info.role_names = res.data.roles.map((item) => {
            return item.name;
          });
          localStorage.setItem("admin_id", res.data.id);
        }
      });
    },
    showSettingDia(id) {
      this.$http.getAuthCrmShow('config_auth_uid').then(res => {
        if (res.status == 200) {
          if ((res.data + '').indexOf(id) == -1) {
            this.configUid = false;
          } else {
            this.configUid = true;
          }
        }
      })
    },
    getWebsiteRoles() {
      this.$http.getWebsiteRoles({ per_page: 100 }).then((res) => {
        if (res.status === 200) {
          this.roles_list = res.data.data;
        }
      });
    },
    goBack() {
      this.$router.back();
    },
    handleCommand(val) {
      if (val == "a") {
        this.dialogPassword = true;
      } else if (val == "config") {
        if (this.$store.state.disableClick) return
        this.$goPath("crm_customer_business_setting?type=crm");
      }
    },
    isModify() {
      this.$http.updataAdmin(this.form_password).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.dialogPassword = false;
        }
      });
    },
    // phonecode(){
    //   console.log(this.form_info.phone);
    //   let form = {}
    //   form.phone = this.form_info.phone
    //   // console.log(form);
    //   this.$http.getphonecode(form).then((res)=>{
    //     if(res.status==200){
    //       console.log(res.data);
    //     }
    //   })
    // },
    submitDataBindZiliao() {
      let form = Object.assign({}, this.form_info);
      form.department_id = form.department_id.join(",");
      if (
        !form.user_name ||
        !form.phone
      ) {
        this.$message.error("请检查表单内容后提交");
        return;
      }
      this.$http.setUserInfo(form).then((res) => {
        if (res.status === 200) {
          // if(res.data.code==1000){
          //   console.log(res.data);
          //   this.codestatus = true
          // }
          this.$message.success("操作完成");
          this.getAdmin();
          this.is_dialog_ziliao = false;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.header {
  position: relative;

  .back {
    position: absolute;
    left: 20%;

    .b-tabs {
      cursor: pointer;
      align-items: baseline;

      .b-t-item {
        margin-right: 50px;
        color: #fff;
        position: relative;

        &.isactive {
          color: #00a3ff;

          &::after {
            position: absolute;
            content: "";
            left: 50%;
            bottom: -1rem;
            transform: translateX(-50%);
            height: 3px;
            background: #2d84fb;
            width: 42px;
            display: block;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .el-breadcrumb {
    color: #fff;
    flex: 1;
  }

  align-items: center;
  width: 100%;
  background: #fff;
  justify-content: space-between;

  .left {
    width: 230px;
    display: flex;
    // background: #0083ff;
    background: #44495d;
    align-items: center;
    justify-content: center;
    // line-height: 60px;
    height: 60px;
  }

  .right {
    align-items: center;
    justify-content: space-between;
    background: #44495d;

    width: 100%;

    p {
      color: #9c9fa6;
      margin: auto 10px;
    }

    .el-button {
      padding: 5px;
      height: 34px;
      margin: auto 5px;
    }

    .el-select {
      width: 250px;
      border: none;
      border-radius: 0;

      .el-select {
        height: 30px;
      }
    }
  }

  .admin {
    align-items: center;

    .el-avatar {
      margin: 0 10px;
    }
  }
}

.el-header {
  padding: 0;
}

.right_r {
  margin-right: 100px;
  align-items: center;
}

.el-icon-bell {
  color: #fff;
  font-size: 22px;
  margin: 0 10px;
}

.logout {
  color: #00a3ff;
  display: flex;
  align-items: center;
  font-size: 15px;
}
</style>
