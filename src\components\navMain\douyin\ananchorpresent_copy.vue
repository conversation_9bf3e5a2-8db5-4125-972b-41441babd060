<template>
    <div class="page">
      <div class="flex-row">
        <div class="Header" v-for="item in titledata" :key="item.id" :class="{isheader:titleid==item.id}"
        @click="titleswitch(item)"> {{item.name}}</div>
      </div>
        <div class="pagecontent">
          <div class="SearchCondition">
            <div class="block">
              <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                @change="onChangeTime">
              </el-date-picker>
              <div v-if="titleid==1">
                <el-select v-model="paramsdata.refer_dy_id" placeholder="主播账号"
                size="small" style="width: 145px;margin:0px 10px;" @change="Sortevents"
                clearable>
                  <el-option
                    v-for="item in sortoptions"
                    :key="item.refer_dy_id"
                    :label="item.refer_dy_name"
                    :value="item.refer_dy_id">
                  </el-option>
                </el-select>
                <el-select v-model="paramsdata.platform" placeholder="平台类型"
                size="small" style="width: 145px;margin:0px 10px;" @change="platformevents"
                clearable>
                  <el-option
                    v-for="item in platformdata"
                    :key="item.platform"
                    :label="item.platform_name"
                    :value="item.platform">
                  </el-option>
                </el-select>
                <el-cascader style="margin:0px 10px;" size="small" v-model="member_value"
                :options="member_listNEW" clearable filterable placeholder="成员" :style="{
                    minWidth: '20px',
                     width: '110px',
                    }" :props="{ 
                        label: 'user_name',
                        value: 'id',
                        children: 'subs',
                        checkStrictly: true,
                    }" @change="loadFirstLevelChildren">
                </el-cascader>
              </div>
              <el-cascader v-else style="margin:0px 10px;" size="small" v-model="memberA_value"
                :options="member_listNEW" clearable filterable placeholder="成员" :style="{
                    minWidth: '20px',
                     width: '110px',
                    }" :props="{ 
                        label: 'user_name',
                        value: 'id',
                        children: 'subs',
                        checkStrictly: true,
                    }" @change="loadFirstLevelChildren">
                </el-cascader>
                <!-- <el-input size="small" style="width: 200px;margin:0px 10px;" v-else placeholder="请输入姓名" v-model="paramsmember.user_name" class="input-with-select">
                  <el-button slot="append" icon="el-icon-search" @click="searchmembers"></el-button>
                </el-input> -->
            </div>
            <div class="head-list">
              <!-- <el-button size="small" type="primary" @click="setanchor">设置主播</el-button> -->
              <el-button v-show="show" size="small" type="primary" @click="exporttakelook">导出</el-button>
                <!-- <el-button class="listbnt" size="mini" @click="doulist">
                  <div class="flex-row items-center">
                    <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinlist.png" alt="">
                    <span>数据列表</span>
                  </div>
                </el-button> -->
            </div>
          </div>
          <div class="taketable">
            <div v-if="titleid==1">
              <el-table
                  v-loading="is_table_loading"
                  :data="classifydata"
                  :header-cell-style="{ background: '#EBF0F7' }"
                  border
                  style="width: 100%"
                  show-summary
                  :summary-method="handleSummary">
                  <el-table-column
                    prop="refer_dy_name"
                    align="center"
                    label="直播帐号名称">
                  </el-table-column>
                  <el-table-column
                    prop="user_name"
                    align="center"
                    label="主播" 
                    v-slot="{ row }">
                      {{row.user_name?row.user_name:"--"}}
                  </el-table-column>
                  <el-table-column
                    align="center"
                    label="直播平台来源"
                    prop="platform"
                    v-slot="{ row }">
                      {{row.platform?row.platform:"--"}} 
                  </el-table-column>
                  <el-table-column
                    prop="total_custom_num"
                    align="center"
                    label="线索总量">
                  </el-table-column>
                  <el-table-column
                    prop="custom_num"
                    align="center"
                    label="线索量">
                  </el-table-column>
                  <el-table-column
                    prop="repeat_num"
                    align="center"
                    label="重复量">
                  </el-table-column>
                  <el-table-column
                    prop="proportion"
                    align="center"
                    label="线索占比">
                    <template slot="header">
                      <el-tooltip class="item" effect="dark" content="线索占比=推送量/CRM客户总量" placement="top">
                        <span>线索占比</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
            </div>
           <div v-else>
            <anchormemberlist :memberdata="memberdata" :is_table_loading="is_table_loading"></anchormemberlist>
          </div>
          <div class="page_footer flex-row items-center">
            <div class="page_footer_l flex-row flex-1 items-center">
              <div class="head-list">
                <el-button type="primary" size="small" @click="empty">清空</el-button>
              </div>
              <div class="head-list">
                <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
              </div>
            </div>
            <div style="margin-right:10px;">
              <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
                :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                @current-change="onPageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
          </div>
        </div>
        <setupanchors ref="anchors" @getlookdata="getlookdata"></setupanchors>
    </div>
</template>
<script>
import setupanchors from "@/components/navMain/crm/business/setupanchors.vue"
import anchormemberlist from "./anchormemberlist.vue"
export default {
  components: {
    setupanchors,
    anchormemberlist
  },
    data() {
        return {
            timeValue:"",//时间检索
            titledata:[
              {id:1,name:"主播账号"},
              {id:2,name:"客资分布"}
            ],//标题
            titleid:"1",//标题id
            pickerOptions: {
                shortcuts: [{
                  text: '今天',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                    text: '昨天',
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date(end);
                      start.setDate(start.getDate() - 1);
                      end.setDate(start.getDate());
                      picker.$emit('pick', [start, end]);
                    }
                  },{
                  text: '本周',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上周',
                  onClick(picker) {
                    const end = new Date(); // 获取当前日期
                      end.setDate(end.getDate() - end.getDay() - 7); // 获取上周的最后一天
                      end.setHours(23, 59, 59, 0);
                      const start = new Date(end);
                      start.setDate(start.getDate() - 6); // 获取上一周的第一天
                      start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '本月',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上月',
                  onClick(picker) {
                    const end = new Date();
                    end.setDate(0); // 获取上个月的最后一天
                    end.setHours(23, 59, 59, 0);
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                },]
            },
            member_value:"",
            //成员
            memberA_value:"",
            member_listNEW: [],
            classifydata:[],//主播账号表格数据
            memberdata:[],//主播成员表格数据
            params:{
                total:0,
                per_page:10,
                page:1
            },
            paramsdata:{
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                refer_dy_id:"",
                // sort:"",
            },
            paramsmember:{
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                admin_id:"",
            },
            sortoptions:[],//主播账号
            show:false,
            is_table_loading:false,
            Anchorplatform:{
              platform:"",
            },
            platformdata:[],//直播平台数据
        }
    },
    mounted(){
      console.log(************);
      if(this.$store.state.ismanager){
        this.show =  this.$store.state.ismanager
      }else{
        this.btnexport()
      }
        this.getanchor()
        this.getlookdata()
        this.MembersNEW()
        this.btnexport()
        this.getzhuboaccount()
        this.zhiboplatform()
    },
    methods:{
        handleSummary({ columns, data }) {
          const sums = [];
          columns.forEach((column, index) => {
            if (index === 0) {
              sums[index] = '合计';
              return;
            }
            if (index === 1) {
              sums[index] = '--';
              return;
            }
            if (index === 2) {
              sums[index] = '--';
              return;
            }
            if (index === 6) {
              sums[index] = '--';
              return;
            }
            if (column.property === 'total_custom_num' || column.property === 'custom_num' || column.property === 'repeat_num') {
              const values = data.map(item => parseFloat(item[column.property]));
              if (!isNaN(values[0])) {
                sums[index] = values.reduce((prev, curr) => {
                  const value = parseFloat(curr);
                  return isNaN(value) ? prev : prev + value;
                }, 0);
                sums[index] = Math.round(sums[index] * 100) / 100;
              } else {
                sums[index] = '';
              }
            } else {
              sums[index] = '';
            }
          });
          return sums;
        },
        //获取是否是可以显示导出按钮
        btnexport(){
          this.$http.determinecustomeradmin().then((res)=>{
            if(res.status==200){
              // console.log(res.data," //判断是不是客户管理员");
              this.show = res.data.is_manager
            }
          })
        },
        //获取是否有主播
        getanchor(){
          this.$http.getananchorpresent().then((res)=>{
            if(res.status==200){
              if(!res.data.anchors.length){
                this.$refs.anchors.open()
              }
            }
          })
        },
        //获取直播平台
        zhiboplatform(){
          this.$http.getzhiboplatform().then((res)=>{
            if(res.status==200){
              this.platformdata = res.data
            }
          })
        },
        //获取主播账号表格数据
        getlookdata(){
          this.is_table_loading = true
            this.$http.getanchoraccountdata(this.paramsdata).then((res)=>{
                if(res.status==200){
                  this.is_table_loading = false
                    this.classifydata = res.data.data
                    this.params.total = res.data.total
                }
            })
        },
        //获取主播成员统计
        getmembersdata(){
          this.is_table_loading = true
            this.$http.getanchormembersdata(this.paramsmember).then((res)=>{
                if(res.status==200){
                  // this.is_table_loading = false
                  console.log(res);
                    this.memberdata = res.data.data
                    console.log(this.memberdata);
                    this.params.total = res.data.total
                }
            })
        },
        //获取主播账号
        getzhuboaccount(){
          this.$http.getobtainanchoraccount(this.Anchorplatform).then((res)=>{
            if(res.status==200){
              console.log(res.data);
              this.sortoptions = res.data.list
            }
          })
        },
        //标题切换
        titleswitch(item){
          this.titleid = item.id
          if(item.id==1){
            this.getlookdata()
          }else{
            this.getmembersdata()
          }
        },
        // 自定义筛选时间发生改变时触发
        onChangeTime(e) {
          this.paramsdata.start_date = e ? e[0] : ""; // 赋值开始时间
          this.paramsdata.end_date = e? e[1] : ""; // 赋值结束时间
          this.paramsdata.page = 1; // 显示第一页
          this.params.page = 1; // 显示第一页
          this.getlookdata(); // 获取最新数据
        },
        // 获取成员的接口（新）
        MembersNEW(){
          this.$http.getDepartmentMemberListNew().then((res)=>{
            if(res.status==200){
                // console.log(res.data);
              this.member_listNEW = res.data
            }
          })
        },
        //成员
        loadFirstLevelChildren(value) {
          if(this.titleid==1){
            this.paramsdata.admin_id = value[0]
            this.getlookdata();
          }else{
            this.paramsmember.admin_id = value[0]
            this.getmembersdata()
          }
        },
        Sortevents(){
          this.getlookdata(); // 获取最新数据
        },
        platformevents(){
          this.getlookdata(); // 获取最新数据
        },
        //搜索主播成员数据
        searchmembers(){
          this.getmembersdata()
        },
        //跳转到数据列表页
        doulist() {
          this.$goPath(`/crm_Follow_up_list`);
        },
        //设置主播
        setanchor(){
          this.$refs.anchors.open()
        },
        exporttakelook(){
          this.$confirm('确定要导出数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if(this.titleid==1){
              this.$http.exportanchoraccountdata(this.paramsdata).then((res)=>{
               if(res.status == 200){
                 window.open(res.data);
               }
              })
            }else{
              this.$http.exportanchormembersdata(this.paramsmember).then((res)=>{
                if(res.status==200){
                  window.open(res.data);
                }
              })
            }
           
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出'
            });          
          });
        },
        //分页
        onPageChange(current_page) {
          this.params.page = current_page;
          if(this.titleid==1){
            this.paramsdata.page = current_page;
            this.getlookdata();
          }else{
            this.paramsmember.page = current_page;
            this.getmembersdata()
          }
      
        },
        //每页几条
        handleSizeChange(e){
          this.params.per_page = e
          if(this.titleid==1){
            this.paramsdata.per_page = e;
            this.getlookdata();
          }else{
            this.paramsmember.per_page = e;
            this.getmembersdata()
          }
          
        },
        //清空
        empty(){
          this.timeValue =  ""
          if(this.titleid==1){
            this.member_value = ""
            this.paramsdata={
              per_page:10,
              page:1,
              start_date:"",
              end_date:"",
              admin_id:"",
              sort:"",
            }
            this.getlookdata();
          }else{
            this.memberA_value = ""
            this.paramsmember={
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                admin_id:"",
            }
            this.getmembersdata()
          }
        },
        //刷新
        Refresh(){
          if(this.titleid==1){
            this.getlookdata();
          }else{
            this.getmembersdata()
          }
          
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 20px 24px 80px;
    .Header{
      width: 110px;
      height: 40px;
      text-align: center;
      line-height: 42px;
      color: #8a929f;
      cursor: pointer;
    }
    .isheader{
      background-color: #ffffff;
    }
    .pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .SearchCondition{
            margin: 20px;
            display: flex;
            justify-content: space-between;
            .block{
              display: flex;
            }
        }
        .head-list{
          display: flex;
        }
        .taketable{
            width: 97%;
            margin: 0 auto;
            margin-bottom: 20px;
            .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
            ::v-deep .el-table thead.is-group th{
              text-align: center;
            }
        }
    }
}
</style>