<template>
  <div>
    <div class="div row jus">
      <el-link class="create" type="primary" @click="onClickData">添加</el-link>
    </div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      highlight-current-row
    >
      <!-- <el-table-column prop="id" label="ID"></el-table-column> -->
      <el-table-column prop="title" label="名称"></el-table-column>
      <el-table-column label="状态" v-slot="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">{{
          row.status === 1 ? "启用" : "禁用"
        }}</el-tag>
      </el-table-column>
      <!-- <el-table-column prop="created_at" label="添加时间"></el-table-column> -->
      <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <el-link type="primary" @click="onChangeEdit(scope.row)"
            >编辑</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog
      width="500px"
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
    >
      <el-form :model="form_labels" label-position="left" label-width="100px">
        <el-form-item label="标签名称：">
          <el-input
            style="width:300px"
            v-model="form_labels.title"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="启用状态：">
          <el-radio v-model="form_labels.status" :label="1">启用</el-radio>
          <el-radio v-model="form_labels.status" :label="0">禁用</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCreate = false">取 消</el-button>
        <el-button
          type="primary"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          @click="submitData"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_labels: {},
      is_button_loading: false,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    onClickData() {
      this.form_labels = {
        title: "",
        status: 1,
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmCustomerTypeData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onChangeEdit(row) {
      this.form_labels = {
        id: row.id,
        title: row.title,
        status: row.status,
      };
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    submitData() {
      this.is_button_loading = true;
      if (this.dialogTitle === "updateData") {
        this.$http.updateCrmCustomerTypeData(this.form_labels).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.createCrmCustomerTypeData(this.form_labels).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.jus {
  margin-bottom: 10px;
  justify-content: flex-end;
}
</style>
