<template>
    <div class="tab-content-container">
		<div class="tab-content-body white topped">
			<div class="body-inner main-scroll" ref="scrollElem">
                <div class="switch">
                    <div class="header_left" :style="componentId=='ruleslist'?'width:90%':'width:100%'">
		        		<el-tabs class="header-tabs" v-model="componentId">
                            <el-tab-pane label="流程管理" name="ruleslist"></el-tab-pane>
		        			<el-tab-pane label="新增流程" name="addrules"></el-tab-pane>
                            <el-tab-pane label="流程日志" name="processlog" v-if="processlogshow"></el-tab-pane>
		        		</el-tabs>
                    </div>
                    <div class="header_right"  v-if="componentId=='ruleslist'">
                        <el-radio-group v-model="form_info.is_auto_recycle" size="small"
                        @change="changestate" >
                          <el-radio-button label="0">已停用</el-radio-button>
                          <el-radio-button label="2">启用中</el-radio-button>
                        </el-radio-group>
                        <el-tooltip class="item" effect="light" placement="bottom" 
                        style="display: inline-block; margin-left: 10px">
                          <div slot="content" style="max-width: 300px; line-height: 20px;">
                            启用中：全局启用，优先级大于单条流程规则<br>
                            已停用：全局停用，优先级大于单条流程规则<br>
                            新增流程：规则创建后将于次日生效运行<br>
                            掉公提醒：系统每间隔1小时提醒成员，当日即将掉公的客资数量，成员可通过我的客户——即将掉公查看并维护客户<br>
                            流程管理：可根据时间类型、行为、同时满足条件、适用范围部门成员、执行周期组合搭配SOP规则<br>
                            编辑流程：可对流程调整修改，次日生效运行<br>
                            删除流程：删除后不可恢复<br>
                            注意事项：所有流程自动执行后，不可恢复。下一执行周期前可删除或禁用<br>
                          </div>
                          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                        </el-tooltip>
                    </div>
                </div>
           
                <!-- <div class="add">  
                    <el-button type="primary" size="medium" @click="addrule">添加流程</el-button>
                </div> -->
				<keep-alive>
					<component :is="componentId" @room-change="handleRoomChange"
					ref="list" :conditiondata="conditiondata" @acceptlength="acceptlength" :processlogdata="processlogdata"
                    :member_listNEW="member_listNEW"></component>
				</keep-alive>
			</div>
		</div>
	</div>
</template>
<script>
import addrules from "./addpublic_rules.vue"
import ruleslist from "./ruleslist.vue"
import processlog from "./process_log.vue"
export default {
    name:"newdrop_the_public_rule",
	components:{
		addrules,
		ruleslist,
        processlog
	},
    data() {
        return {
            componentId: 'ruleslist',
            conditiondata:{},//条件
            form_info:{
                is_auto_recycle:"",
            },
            processlogshow:false,//控制流程日志显示
            processlogdata:{},//选中的流程
            member_listNEW:[],//成员
        }
    },
    watch:{
      componentId:{
        handler(newVal){
          if(newVal=="ruleslist"||newVal=="addrules"){
            this.processlogshow = false
          }
        }
      }
    },
    created(){
        this.getcondition()
        this.diaogongopen()
    },
    methods:{
        handleRoomChange(room_id){

            if(room_id&&room_id.text=="processlog"){
                this.processlogshow = true
                this.processlogdata = room_id.row
                this.member_listNEW = room_id.uiddata
            }
            if(room_id&&room_id.text){
                this.componentId = room_id.text;
            }else{
                this.componentId = room_id;
            }
            if(room_id=="ruleslist"){
            setTimeout(() => {
              // 这里需要根据具体的组件名调用方法
              if(this.$refs.list.gettabdata()){
                this.$refs.list.gettabdata();
              }
            }, 300); // 两秒后调用
            }
            
		},
        //获取条件
        getcondition(){
            this.$http.newdiaogongcondition().then(res=>{
                if(res.status==200){
                    // console.log(res.data);
                    this.conditiondata = res.data 
                }
            })
        },
        //获取掉公是否开启
        diaogongopen(){
            this.$http.getAuthShow("is_auto_recycle").then((res) => {
            //   console.log(res.data);
              if (res.status == 200) {
                this.form_info.is_auto_recycle = res.data

              }
            })
        },
        //获取表格长度
        acceptlength(tabledata){
            if(!tabledata.length){
                this.$alert('因掉公规则功能模块升级，请导入后修改调整规则。导入后原有规则将停止运行。', '导入规则提醒', {
                    confirmButtonText: '确定导入',
                    showClose:false,
                    callback: action => {
                        this.$http.droppublicimport().then(res=>{
                            if(res.status==200){
                                this.$refs.list.gettabdata()
                                this.$message({
                                  type: 'success',
                                  message: `导入成功`,   
                                });
                            }
                        })
                      }
                });
            }
        },
        //更改掉公状态
        changestate(){
            this.$http.setSiteCrmSetting(this.form_info).then((res) => {
                if(res.status==200){
                    this.$message.success("修改成功");
                }
            })
        },
        // //添加流程
        // addrule(){
        //     this.handleRoomChange("addrules")
        // }
    },
}
</script>
<style lang="scss" scoped>
.tab-content-body{
	padding: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	// padding-bottom: 48px;
.body-inner{
	flex: 1;
	overflow: auto;
	padding: 0 28px 28px;
}
    // margin-bottom: 90px;
    .switch{
        display: flex;
        margin-top: 20px;
        .header_left{
            width: 90%;
        }
        .header_right{
            min-width: 172px;
            margin-top: 7px;
        }
        // text-align: right;
    }
    .add{
        margin-top: 15px;
    }
}
::v-deep.el-tabs--border-card>.el-tabs__content{
    margin-bottom: 90px !important;
}
::v-deep .el-tabs__header{
	margin: 0 0 0 !important;
    .el-tabs__nav-scroll {  
    // display: flex !important;  
    // justify-content: center !important; /* 水平居中 */  
    .el-tabs__item {
    font-size: 17px !important;
    line-height: 40px !important;
}
}  
}
// ::v-deep{
// 	.tab-content-footer{
// 		position: absolute;
// 		z-index: 99;
// 		bottom: 0;
// 		left: 0;
// 		right: 0;
// 		background: #fff;
// 		padding-left: 28px;
// 		padding-right: 28px;
// 		white-space: nowrap;
//     	overflow: hidden;
// 	}
// }


</style>