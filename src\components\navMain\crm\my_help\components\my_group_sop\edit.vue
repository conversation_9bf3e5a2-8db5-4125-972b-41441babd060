<template>
  <div class="add">
    <!-- <div class="tips">
      <div>
        1.什么是群活码：通过多个群聊配置一个二维码，客户通过扫描二维码加入群聊，当前面的群人数
        达到上限后，自动发送后面的群二维码，从而突破群聊人数限制，实现一码多群功能。
      </div>
      <div>
        2.怎么用：首先给群活码配置接待员工，客户通过群活码添加员工为好友，添加通过后自动向客户
        发送入群引导语和群聊二维码，再通过扫描群聊二维码入群，当二维码到期后可手动更新二维码。
      </div>
    </div> -->
    <el-form label-width="100px">
      <div class="title">基本信息</div>

      <el-form-item label="群sop名称">
        <div class="form-item-block">
          <el-input
            placeholder="请输入群sop名称"
            v-model="form_params.task_name"
            style="width: 240px; margin-right: 12px"
          >
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="执行时间">
        <div class="form-item-block flex-row align-center flex-wrap">
          <el-radio-group v-model="form_params.time_type" size="mini">
            <el-radio :label="1" border>自定义</el-radio>
            <el-radio :label="2" border>每天</el-radio>
            <el-radio :label="3" border>每周</el-radio>
            <el-radio :label="4" border>每月</el-radio>
          </el-radio-group>
        </div>
        <div v-if="form_params.time_type == 1">
          <el-date-picker
            style="width: 250px"
            size="small"
            v-model="form_params.send_time"
            type="datetime"
            placeholder="请选择日期"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
          >
          </el-date-picker>
        </div>
      </el-form-item>

      <div class="title">发送内容设置</div>
      <div class="tips warning">
        温馨提示 ： 因企业微信限制客户群每天只能接收一条群发消息
      </div>
      <el-form-item label="文案">
        <welcome-mes
          style="padding-right: 20px"
          ref="group_sop_edit"
          :value="welcome_mes"
        ></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>
import welcomeMes from "@/components/navMain/crm/components/welcome_mes"
export default {
  components: { welcomeMes },
  props: ["form"],
  data() {
    return {
      form_params: {
        time_type: "",
        send_time: "",
        is_open: true,
      },
      isSubmiting: false,
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      type_arr: ["text", "image", "link", "miniprogram", "video", "file"],
    };
  },
  computed: {
  },
  created() {
    this.form_params = Object.assign({}, this.form);
    if (this.form_params.is_open == 1) {
      this.form_params.is_open = true;
    } else {
      this.form_params.is_open = false;
    }
    if (this.form_params.time_type != 1) {
      this.form_params.send_time = new Date();
    }

    for (const key in this.form_params.media) {
      this.$set(
        this.welcome_mes,
        key,
        JSON.parse(JSON.stringify(this.form_params.media[key]))
      );
      // this.welcome_mes[key] = JSON.parse(JSON.stringify(this.form_params.media))
    }
    if (this.form_params.media.image) {
      delete this.welcome_mes.image.org_url;
    }
    if (this.form_params.media.file) {
      delete this.welcome_mes.file.org_url;
    }
    if (this.form_params.media.video) {
      delete this.welcome_mes.video.org_url;
    }
    if (this.form_params.media.miniprogram) {

      this.welcome_mes.miniprogram.pic_url = this.form_params.media.miniprogram.org_url;
      delete this.welcome_mes.miniprogram.org_url;
    }
    if (this.form_params.media.link) {
      delete this.welcome_mes.link.org_url;
    }
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      if (params.is_open) {
        params.is_open = 1;
      } else {
        params.is_open = 0;
      }
      if (params.time_type != 1) {
        delete params.send_time;
      }
      let _welcome_mes = Object.assign(
        {},
        this.$refs.group_sop_edit.welcome_mes
      );
      let oldTypeArr = Object.keys(this.form_params.media)
      for (const key in _welcome_mes) {
        if (_welcome_mes[key].org_url !== undefined) {
          delete _welcome_mes[key].org_url
        }
      }
      if (!_welcome_mes.text.desc) {
        this.$message.warning("欢迎语不能为空");
        return;
      }
      if (_welcome_mes.image && !_welcome_mes.image.media_id) {
        if (oldTypeArr.includes("image")) {
          _welcome_mes.image = this.form_params.media.image
          _welcome_mes.image.is_del = 1
          delete _welcome_mes.image.name
        } else {
          delete _welcome_mes.image;
        }
        // delete _welcome_mes.image;
      }
      let linkArr = {
        title: "链接标题不能为空",
        pic_url: "链接封面不能为空",
        desc: "链接描述不能为空",
        url: "跳转链接不能为空",
      };
      let emptyLink = [];
      for (const key in _welcome_mes.link) {
        if (!_welcome_mes.link[key] && Object.keys(linkArr).includes(key)) {
          emptyLink.push(key);
        }
      }
      if (emptyLink.length == Object.keys(linkArr).length) {
        if (oldTypeArr.includes("link")) {
          _welcome_mes.link = this.form_params.media.link
          delete _welcome_mes.link.appid
          _welcome_mes.link.is_del = 1

        } else {
          emptyLink.length = 0;
          delete _welcome_mes.link;
        }
      } else if (emptyLink.length) {
        if (!oldTypeArr.includes("link")) {
          this.$message.warning(linkArr[emptyLink[0]]);
          return;
        }

      }
      let miniArr = {
        title: "小程序标题不能为空",
        media_id: "小程序封面不能为空",
        appid: "小程序appid不能为空",
        url: "小程序链接不能为空",
      };
      let emptyMini = [];
      for (const key in _welcome_mes.miniprogram) {
        if (!_welcome_mes.miniprogram[key] && Object.keys(miniArr).includes(key)) {
          emptyMini.push(key);
        }
      }
      if (emptyMini.length == Object.keys(miniArr).length) {
        if (oldTypeArr.includes("miniprogram")) {
          _welcome_mes.miniprogram = this.form_params.media.miniprogram
          _welcome_mes.miniprogram.is_del = 1
        } else {
          emptyMini.length = 0;
          delete _welcome_mes.miniprogram;
        }

      } else if (emptyMini.length) {
        if (!oldTypeArr.includes("miniprogram")) {
          this.$message.warning(miniArr[emptyMini[0]]);
          return;
        }
      }
      if (
        _welcome_mes.video &&
        !_welcome_mes.video.media_id
      ) {
        if (oldTypeArr.includes("video")) {
          _welcome_mes.video = this.form_params.media.video
          _welcome_mes.video.is_del = 1
        } else {
          delete _welcome_mes.video;
        }

      }
      if (
        _welcome_mes.file &&
        !_welcome_mes.file.media_id
      ) {
        if (oldTypeArr.includes("file")) {
          _welcome_mes.file = this.form_params.media.file
          _welcome_mes.file.is_del = 1
        } else {
          delete _welcome_mes.file;
        }
      }
      delete params.sender
      params.media = JSON.stringify(_welcome_mes);
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .editCrmMyGroupSop(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.imgurl {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
}
.tip {
  color: #8a929f;
  font-family: PingFang SC;
  font-weight: regular;
  font-size: 12px;
}
.add {
  max-height: 70vh;
  overflow-y: auto;
  .title {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 16px;
    margin: 20px 0;
    font-weight: 600;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;
    &.active {
      border: 5px solid #409eff;
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    .file_img {
      width: 115px;
      height: 115px;
      text-align: center;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;
  &.active {
    border: 1px solid #a6e22e;
  }
}
.upload-demo {
  margin-right: 5px;
}
</style>
