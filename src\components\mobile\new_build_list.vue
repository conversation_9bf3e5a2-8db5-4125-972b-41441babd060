<template>
  <!-- 
      @date：2022-04-20
      @description：首页组件更换
   -->
  <div class="newbuild" v-infinite-scroll="load" :infinite-scroll-disabled="is_disabled" v-if="list.length > 0">
    <div class="list-item bottom-line" v-for="(item, index) in list" :key="index" @click="onClick(item)">
      <div class="top">
        <div v-if="type === 1" class=" small row-l">
          <div class="s-img">
            <img :src="
                            item.build_image
                              ? item.build_image
                              : 'https://img.tfcs.cn/static/img/que.jpg'
                          " />
          </div>
          <div class="detail small-d">
            <div class="d-title row-l">
              <div class="d-t-l">{{ item.build_name }}</div>
              <div class="d-t-r" v-if="item.label">{{ item.label }}</div>
            </div>
            <div class="d-price">{{ item.build_avg_price }}元/㎡</div>
            <div class="d-address ">
              <span class="row-l">{{ item.b_region_0_name || "" }} &nbsp;{{
                              item.b_region_1_name || ""
                              }}</span>
            </div>
            <div class="d-type row-l">
              <div class="ttt" v-for="(type, index) in item.build_category_name" :key="index">
                {{ type }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="type === 2" class="big">
          <div class="big-img">
            <img :src="
                            item.build_image
                              ? item.build_image
                              : 'https://img.tfcs.cn/static/img/que.jpg'
                          " />
            <div class="big-build-name">{{ item.build_name }}</div>
          </div>
          <div class="detail big-d">
            <div class="d-price row-l">
              <div class="d-price">{{ item.build_avg_price + "元/㎡" }}</div>
              <div class="d-t-r" v-if="item.label">{{ item.label }}</div>
            </div>
            <div class="d-address ">
              <span class="row-l">{{ item.b_region_0_name || "" }} &nbsp;{{
                              item.b_region_1_name || ""
                              }}</span>
            </div>
            <div class="d-type row-l">
              <div class="ttt" v-for="(type, index) in item.build_category_name" :key="index">
                {{ type }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom row-l">
        <div class="b-l row-l">
          <img src="https://img.tfcs.cn/backup/static/h5index/youhuihuodong.png" />
          <span v-if="item.buy_coupon" style="color:#7A828D;margin-left:10rpx">优惠活动 | {{ item.buy_coupon }}</span>
          <span v-else style="color:#7A828D;margin-left:10rpx">靓盘好卖 佣金易拿</span>
        </div>
        <div class="b-r row-l">
          <div class="row-l" v-if="item.store_brokerage_rule || item.brokerage_rule">
            <span class="y-icon">佣</span>
            <span class="isdesc">
              {{ item.store_brokerage_rule || item.brokerage_rule }}</span>
          </div>
          <div v-else class="row-l">
            <span>我要推荐</span>
            <img src="https://img.tfcs.cn/backup/static/h5index/jiantou.png" />
          </div>
        </div>
      </div>
    </div>
    <loadMore :status="loading"></loadMore>
  </div>
</template>

<script>
import loadMore from "./loadMore";
export default {
  components: {
    loadMore,
  },
  props: {
    list: Array,
    // 是否显示佣金
    is_display: {
      type: [Boolean, Number, String],
      default: false,
    },
    // 列表模式  1小图 2大图
    type: {
      type: [Number, String],
      default: 1,
    },
    loading: {
      type: String,
      default: "",
    },
  },
  computed: {
    is_disabled() {
      return this.loading === "loading" ? false : true;
    },
  },
  data() {
    return {};
  },
  methods: {
    onClick(item) {
      this.$emit("click", item);
    },
    load() {
      this.$emit("load");
    },
  },
};
</script>

<style scoped lang="scss">
.row-l {
  display: flex;
}

.newbuild::-webkit-scrollbar {
  display: none;
}

.newbuild {
  width: 100%;
  height: 400px;
  overflow: auto;
  margin-top: 20px;
  margin-bottom: 40px;

  .list-item {
    margin-top: 16px;

    .top {
      .small {
        .s-img {
          width: 110px;
          height: 80px;
          border-radius: 5px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .small-d {
          margin-left: 18px;
        }
      }

      .detail {
        line-height: 20px;
        flex: 1;

        .d-title {
          align-items: baseline;
          justify-content: space-between;

          .d-t-l {
            font-size: 15px;
          }
        }

        .d-price {
          font-size: 14px;
          color: #fe4438;
        }

        .d-address {
          font-size: 11px;
          color: #191c2f;
        }

        .d-type {
          font-size: 11px;
          flex-wrap: wrap;

          .ttt {
            color: #7a828d;
            padding: 0 3px;
            margin-right: 6px;
            background: #f0f1f6;
            border-radius: 2px;
            margin-top: 5px;
          }
        }

        .d-t-r {
          font-size: 11px;
          background: #3172f6;
          color: #fff;
          padding: 0px 4px;
          border-radius: 2px;
        }
      }

      .big-d {
        margin-top: 10px;

        .d-price {
          justify-content: space-between;
        }
      }

      .big {
        .big-img {
          position: relative;

          img {
            height: 190px;
            width: 100%;
            border-radius: 5px;
          }

          .big-build-name {
            position: absolute;
            bottom: 0;
            width: 100%;
            left: 0;
            height: 28px;
            line-height: 28px;
            color: #fff;
            border-radius: 0 0 15px 5px;
            font-size: 15px;
            padding-left: 14px;
            background: rgba($color: #000000, $alpha: 0.4);
          }
        }
      }
    }

    .bottom {
      border-radius: 10px;
      background: #fff9f1;
      padding: 5px 8px;
      font-size: 12px;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0 16px;

      .b-l {
        align-items: center;

        img {
          width: 14px;
          margin-right: 4px;
          height: 14px;
        }
      }

      .b-r {
        color: #3172f6;
        align-items: center;

        .row {
          align-items: center;

          .isdesc {
            color: #ff9b15;
          }
        }

        .y-icon {
          background: #ff9b15;
          width: 17px;
          height: 17px;
          text-align: center;
          line-height: 17px;
          color: #fff;
          border-radius: 50%;
          margin-right: 5px;
        }

        img {
          margin-left: 4px;
          height: 10px;
          width: 10px;
        }
      }
    }
  }
}
</style>
