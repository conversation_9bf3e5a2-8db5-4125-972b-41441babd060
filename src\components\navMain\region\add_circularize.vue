<template>
    <div>
        <div class="addbackground">
            <div class="Add_Content">
                <el-form ref="form" :model="form" label-width="80px">
                    <el-form-item label="标题">
                        <el-input v-model="form.title"></el-input>
                    </el-form-item>
                </el-form>

            </div>
            <div class="Add_Content_right">
                <el-form ref="form" :model="form" label-width="80px">
                    <el-form-item label="联系电话">
                        <el-input v-model="form.phone"></el-input>
                    </el-form-item>
                </el-form>
                <el-form ref="form" :model="form" label-width="80px">
                    <el-form-item label="开始时间">
                        <el-input v-model="form.start_time"></el-input>
                    </el-form-item>
                </el-form>
                <el-form ref="form" :model="form" label-width="80px">
                    <el-form-item label="结束时间">
                        <el-input v-model="form.end_time"></el-input>
                    </el-form-item>
                </el-form>
                <div class="disjunctor">
                    <div class="position">
                        <div class="tagging">红包是否开启</div>
                        <div>
                            <el-switch v-model="value1"></el-switch>
                        </div>
                    </div>
                    <div class="position">
                        <div class="tagging">是否禁用海报</div>
                        <div>
                            <el-switch v-model="value2"></el-switch>
                        </div>
                    </div>
                </div>
                <div class="editor">
                    <div style="border: 1px solid #ccc">
                        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                            :mode="mode" />
                        <Editor style="height: 380px; overflow-y: hidden" v-model="html" :defaultConfig="editorConfig"
                            :mode="mode" @onCreated="onCreated" />
                    </div>
                </div>
                <div class="background_music">
                    <span>背景音乐</span>
                    <div>
                        <el-upload class="upload-demo" action="/api/common/file/upload/admin?category=103"
                            :on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove" multiple
                            :limit="3" :on-exceed="handleExceed" accept=".mp4">
                            <div class="music_right">
                                <span>上传</span>
                            </div>
                        </el-upload>
                    </div>
                </div>
                <div class="add_Circulation">
                    <el-button type="primary" @click="add">添加传阅</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
export default {
    components: { Editor, Toolbar },
    data() {
        return {
            form: {
                title: "",
                phone: "",
                start_time: "",
                end_time: ""
            },
            value1: false,
            value2: false,
            editor: null,
            html: '',
            toolbarConfig: {},
            editorConfig: { placeholder: '请输入内容测试...' },
            mode: 'default', // or 'simple'
            Circulation_data: {
                title: '',
                content: "",
                red_packet_status: "",
                status: '',
                phone: '',
                start_time: '',
                end_time: '',

            },
            packet_status: '',
            status: ''


        }
    },
    methods: {
        onCreated(editor) {
            this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
        },
        handleRemove(file, fileList) {
            console.log(file, fileList);
        },
        handlePreview(file) {
            console.log(file);
        },
        beforeRemove(file) {
            return this.$confirm(`确定移除 ${file.name}？`);
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        add() {
            this.Circulation_data.title = this.form.title
            //匹配html标签的正则表达式，"g"是搜索匹配多个符合的内容
            var re1 = new RegExp("<.+?>", "g")
            //执行替换成空字符
            var msg = this.html.replace(re1, '')
            this.html = msg
            this.Circulation_data.content = this.html
            console.log(this.Circulation_data.comtent);
            if (this.value1 == true) {
                this.packet_status = 1
            } else {
                this.packet_status = 0
            }
            this.Circulation_data.red_packet_status = this.packet_status
            if (this.value2 == true) {
                this.status = 1
            } else {
                this.status = 0
            }
            this.Circulation_data.status = this.status
            this.Circulation_data.phone = this.form.phone
            this.Circulation_data.start_time = this.form.start_time
            this.Circulation_data.end_time = this.form.end_time
            console.log(this.Circulation_data);
            this.$http.New_Reading(this.Circulation_data).then(res => {
                if (res.status == 200) {
                    this.$message({
                        type: 'success',
                        message: '添加成功!'
                    });
                    this.$goPath("perusal");
                }
            })
        },
    },
    mounted() {
    }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style scoped lang="scss">
.addbackground {
    width: 95%;
    height: 1200px;
    // background-color: pink;
    margin: 30px auto;

    .Add_Content {
        width: 900px;
        height: 50px;
        // background-color: palegoldenrod;

        /deep/.el-input__inner {
            width: 300px;
            margin-left: 28px;
        }

        /deep/.el-form-item__label {
            font-size: 17px;
            color: #000;
        }

        .editor {
            width: 800px;
            height: 380px;
            // background-color: cornflowerblue;
            margin: 40px auto;
            margin-left: 30px;
        }
    }

    .Add_Content_right {
        width: 600px;
        height: 1200px;
        margin-left: 30px;
        margin-top: 20px;

        // background-color: palevioletred;
        .disjunctor {
            width: 500px;
            height: 70px;
            // background-color: palegreen;
            display: flex;
            justify-content: space-between;
        }

        .position {
            width: 150px;
            height: 50px;
            // background-color: plum;
            display: flex;
            justify-content: space-between;
        }

        /deep/.el-input__inner {
            width: 300px;
            // margin-left: 30px;
        }

        /deep/.el-form-item__label {
            font-size: 16px;
            color: #000;
        }

        .background_music {
            width: 900px;
            height: 130px;
            // border: 1px solid #fff;
            // border-bottom-color: #dde1e9;
            //   background-color: #fe6c17;
            margin: 0 auto;
            margin-top: 100px;

            div {
                display: flex;

            }

            .music_right {
                width: 90px;
                height: 80px;
                background-color: #F1F4FA;
                margin-top: 10px;
                margin-left: 10px;

                span {
                    margin: 25px auto;
                }
            }
        }

        .add_Circulation {
            width: 600px;
            height: 40px;
            // background-color: paleturquoise;
            display: flex;
            justify-content: flex-end;
        }
    }



}
</style>