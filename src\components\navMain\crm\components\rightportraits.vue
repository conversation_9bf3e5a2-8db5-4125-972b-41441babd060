<template>
  <div class="portrait div row">
    <div class="left" v-if="moveright1">
      <el-button type="text" :disabled="disabled" @click="moveright()">《</el-button>
    </div>
    <div class="left" v-if="moveright2">
      <el-button type="text" :disabled="disabled1" @click="moveright(1)">《</el-button>
    </div>
    <div class="pl" v-if="fistThecard">
      <!-- 接待录入人 -->
      <div class="pl-list" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
        <div class="title">{{calculatecreate}}</div>
        <div class="pl-con div row align-center">
          <div class="img" v-if="l_list.create_user">
            <div v-if="l_list.create_user && l_list.create_user.user_name" class="icon">
              {{ l_list.create_user.user_name[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <!-- <span>公海</span> -->
          <div class="img" v-if="!l_list.create_user">
            <div class="icon">公</div>
          </div>
          <div v-if="!l_list.create_user" class="default-name">公海</div>
          <div class="img-r" v-if="l_list.create_user">
            <div class="img-r-naem">
              {{ l_list.create_user.user_name || "--" }}
            </div>
            <!-- <div class="img-r-dep">
                {{ l_list.create_user.department_name }}
              </div> -->
          </div>
        </div>
        <div class="flex-row Publictime">
          <div>
            {{ l_list.created_at || "--" }}
          </div>
          <div v-show="ModifyHidden">
            <i style="cursor: pointer;" class="el-icon-edit" @click="Enteredby"></i>
          </div>
        </div>
      </div>
      <!-- 跟进维护人 -->
      <div class="pl-list" @mouseenter="handleMouseEnter1" @mouseleave="handleMouseLeave1">
        <div class="title shareuser">{{weihuuser}}
          <el-link type="primary" v-show="ModifyHidden4" @click="Shared_maintenance">+共享</el-link>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.follow_user && l_list.follow_user.user_name" class="icon">
              {{ l_list.follow_user.user_name[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r" v-if="l_list.follow_user">
            <div class="img-r-naem">
              {{ l_list.follow_user.user_name || "--" }}
            </div>
            <!-- <div class="img-r-dep">
                {{ l_list.follow_user.department_name }}
              </div> -->
          </div>
          <div v-else style="margin-left: 12px">--</div>
        </div>
        <div class="flex-row Publictime">
          <div>
            {{ l_list.get_time || "--" }}
          </div>
          <div v-show="ModifyHidden1">
            <i style="cursor: pointer;" class="el-icon-edit" @click="Maintainer"></i>
          </div>
        </div>
      </div>
      <!-- 客户带看人 -->
      <div class="pl-list" @mouseenter="handleMouseEnter2" @mouseleave="handleMouseLeave2">
        <div class="title">{{diakanuser}}</div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.first_look_user && l_list.first_look_user.user_name" class="icon">
              {{ l_list.first_look_user.user_name[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r" v-if="l_list.first_look_user">
            <div class="img-r-naem first-take-look">
              {{ l_list.first_look_user.user_name }}
              <el-tag type="danger">首看</el-tag>
            </div>
            <!-- <div class="img-r-dep">
                {{ l_list.first_look_user.department_name }}
              </div> -->
          </div>
          <div v-else style="margin-left: 12px">--</div>
        </div>
        <div class="flex-row Publictime">
          <div>
            {{ l_list.take_time || "--" }}
          </div>
          <div v-show="ModifyHidden2">
            <i style="cursor: pointer;" class="el-icon-edit" @click="look_people"></i>
          </div>
        </div>
      </div>
      <!-- 客源成交人 -->
    </div>
    <div class="pl" v-if="The_card">
      <div class="pl-list" @mouseenter="handleMouseEnter3" @mouseleave="handleMouseLeave3">
        <div class="title">{{chnegjiaouser}}</div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.deal_user && l_list.deal_user.user_name" class="icon">
              {{ l_list.deal_user.user_name[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r" v-if="l_list.deal_user">
            <div class="img-r-naem">
              {{ l_list.deal_user.user_name || "--" }}
            </div>
            <!-- <div class="img-r-dep">
                {{ l_list.deal_user.department_name }}
              </div> -->
          </div>
          <div v-else style="margin-left: 12px">--</div>
        </div>
        <div class="flex-row Publictime">
          <div>
            {{ l_list.deal_at || "--" }}
          </div>
          <div v-show="ModifyHidden3">
            <i style="cursor: pointer;" class="el-icon-edit" @click="Trader"></i>
          </div>
        </div>
      </div>
      <template v-if="l_list.share_admins">
        <div class="pl-list" v-for="(item,index) in limitedShareAdmins" :key="index" @mouseover="handleMouseEnter4(index)"
          @mouseout="handleMouseLeave4(index)">
          <div class="title shareuser">共享维护人
            <i class="el-icon-delete" v-show="item.showDelete" @click="dellshare(item)"></i>
          </div>
          <div class="pl-con div row align-center">
            <div class="img">
              <div v-if="item.user_name" class="icon">
                {{ item.user_name[0] }}
              </div>
              <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
            </div>
            <div class="img-r">
              <div class="img-r-naem">
                {{ item.user_name || "--" }}
              </div>
              <div class="img-r-dep">
                {{ item.department_name }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="pl" v-if="Theshare_card">
      <template v-if="l_list.share_admins">
        <div class="pl-list" v-for="(item,index) in ShareAdmins" :key="index" @mouseover="handleMouseEnter5(item)"
          @mouseout="handleMouseLeave5(item)">
          <div class="title shareuser">共享维护人
            <i class="el-icon-delete" v-show="item.showDelete" @click="dellshare(item)"></i>
          </div>
          <div class="pl-con div row align-center">
            <div class="img">
              <div v-if="item.user_name" class="icon">
                {{ item.user_name[0] }}
              </div>
              <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
            </div>
            <div class="img-r">
              <div class="img-r-naem">
                {{ item.user_name || "--" }}
              </div>
              <div class="img-r-dep">
                {{ item.department_name }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="right" v-if="moveLeft1">
      <el-button type="text" :disabled="rightdisabled" @click="moveLeft()">》</el-button>
    </div>
    <div class="right" v-if="moveLeft2">
      <el-button type="text" :disabled="rightdisabled1" @click="moveLeft(1)">》</el-button>
    </div>
    <!-- 弹出框(选择成员) -->
    <el-dialog :visible.sync="show_add_member" width="400px" :title="membertitle" append-to-body>
      <div class="tips" v-if="membertitle=='请选择共享维护人'">
        <div>提示语：共享维护人，可使用【查看电话】跟进客户</div>
      </div>
      <div style="color: #e6a23c;"> <i class="el-icon-search"></i>可直接搜索成员或手机号</div>
      <el-input placeholder="请输入成员名字或手机号" style="width: 250px;" v-model="input2">
        <el-button slot="append" icon="el-icon-search" @click="userSearch"></el-button>
      </el-input>
      <div style="margin-top: 20px;">
        <memberListSingle v-if="show_add_memberA" :list="serverData" :defaultValue="selectedIds"
          @onClickItem="selecetedMember" :defaultExpandAll="Allfalse" :getCheckedNodes="false" ref="memberList">
        </memberListSingle>
        <!-- <el-tree :data="departmentMemberA" :props="defaultProps" @node-click="handleNodeClick"></el-tree> -->
        <div class="lookstyle" v-if="membertitle=='请选择带看人'">
          <el-checkbox v-model="lookchecked" true-label="1" false-label="0">是否同步添加带看记录</el-checkbox>
        </div>
        <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible" :loading="sharecompleted">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- <el-dialog
      :visible.sync="show_member_list"
      width="660px"
      :modal="false"
      :title="membertitle"
    >
      <memberListSingle
        v-if="show_member_list"
        :list="memberList"
        @onClickItem="selecetedMember"
        ref="memberList"
      >
      </memberListSingle>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_member_list = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
  
<script>
  import * as echarts from "echarts";
  // import memberListSingle from "../../site/components/memberList_single.vue";
//   import myEmpty from "@/components/components/my_empty.vue";
  import memberListSingle from "./portrait_single.vue";
  let _this = ''
  export default {
    components: {
    //   myEmpty,
      memberListSingle,
    },
    data() {
      return {
        chnegjiaouser:"成交人",
        diakanuser:"带看人",
        weihuuser:"维护人",
        calculatecreate:"录入人",
        show_add_member:false,//选择带看人模态框
        show_add_memberA:false,
        selectedIds: [], //默认勾选和展开的节点的 key 的数组
        defaultProps: {
          children: "subs",
          label: "name",
        },
        datalist: [], // 全部部门人员
        alllist:[],
        input2:"",
        userparams:{
          user_name:""
        },
        The_card:false,
        Theshare_card:false,
        moveLeft1:true,
        moveLeft2:false,
        moveright1:true,
        moveright2:false,
        show_member_list: false,
        memberList: [],
        membertitle: '',
        params: {
          type: "",
          admin_id: "",
          client_id: ""
        },
        ModifyHidden: false,
        ModifyHidden1: false,
        ModifyHidden2: false,
        ModifyHidden3: false,
        keyuanManger: 0,
        whr: 0,
        fistThecard:true,
        disabled:true,
        disabled1:false,
        rightdisabled:false,
        rightdisabled1:true,
        Allfalse:false,
        ModifyHidden4:false,
        Sharedparmas:{
        client_id:"",//客户id
        shared_admin_id:"",//要分配成员的id
        showsharecard:0,
      },
      lookchecked:0,//是否同步添加带看记录
      sharecompleted:false,
      };
    },
    props: {
      l_list: {
        type: Object,
        default: () => { },
      },
      label: {
        type: Array,
        default: () => [],
      },
      build_list: {
        type: Array,
        default: () => [
          { id: 1, name: "小区", address: "区域1", price: "1900㎡" },
          { id: 2, name: "小区", address: "区域1", price: "1900㎡" },
          { id: 3, name: "小区", address: "区域1", price: "1900㎡" },
          { id: 4, name: "小区", address: "区域1", price: "1900㎡" },
          { id: 5, name: "小区", address: "区域1", price: "1900㎡" },
        ],
      },
      // 是否显示标签按钮
      is_show_label_btn: {
        type: [String, Number],
        default: "",
      },
    },
    created() {
      this.websites_id = this.$route.query.website_id
      _this = this
      // _this.getDepartment();
    },
    watch: {
      'l_list.admin_list': {
        handler(newVal) {
          if(newVal) {
            this.getOneselfList();
          }
        }
      }
    },
    computed: {
      limitedShareAdmins() {
        const admins = this.l_list.share_admins;
        if (admins.length <= 2) {
          return admins;
        } else {
          return admins.slice(0, 2);
        }
      },
      ShareAdmins() {
        const admins = this.l_list.share_admins;
        if (admins.length > 2) {
          return admins.slice(2);
        } else {
          return [];
        }
      }
  },
    // filters: {
    //   dayFilter(val, type) {
    //     let value = '--'
    //     if (type == 'genke') {
    //       if (_this.l_list.last_follow_time && _this.l_list.get_time) {
    //         // console.log(_this.l_list.admin_list );
    //         let new_d = +new Date(_this.l_list.last_follow_time)
    //         let old_d = +new Date(_this.l_list.get_time)
    //         value = parseInt((new_d - old_d) / 1000 / 60 / 60 / 24)
    //       }
    //     }
    //     if (type == 'chengjiao') {
    //       if (_this.l_list.created_at && _this.l_list.deal_time) {
    //         let new_d = +_this.l_list.deal_time * 1000
    //         let old_d = +new Date(_this.l_list.created_at)
    //         console.log(new_d);
    //         value = parseInt((new_d - old_d) / 1000 / 60 / 60 / 24)
    //       }
    //     }
    //     return value
    //   }
    // },
    mounted(){
      let userData = localStorage.getItem( 'juesename');
    userData = JSON.parse(userData)
    if(userData){
      if(userData.diy_create_name){
      //录入人
      this.calculatecreate += `(${userData.diy_create_name})`
      }
      if(userData.diy_follow_name){
        //维护人
        this.weihuuser += `(${userData.diy_follow_name})`
      }
      if(userData.diy_take_name){
        //带看人
        this.diakanuser += `(${userData.diy_take_name})`
      }
      if(userData.diy_deal_name){
        //成交人
        this.chnegjiaouser += `(${userData.diy_deal_name})`
      }
    }else{
      this.getjuesename()
    }
    this.showAccompanyA()
    },
    methods: {
    // // 获取部门列表
    // async getDepartment() {
    //   let res = await this.$http.getCrmDepartmentList().catch((err) => {
    //     console.log(err);
    //   });
    //   if (res.status == 200) {
    //     this.memberList = res.data;
    //   }
    // },
    //获取角色名称
    getjuesename(){
      this.$http.getsetuprolename().then(res=>{
        if(res.status == 200){
          if(res.data.diy_create_name){
            //录入人
            this.calculatecreate += `(${res.data.diy_create_name})`
          }
          if(res.data.diy_follow_name){
            //维护人
            this.weihuuser += `(${res.data.diy_follow_name})`
          }
          if(res.data.diy_take_name){
            //带看人
            this.diakanuser += `(${res.data.diy_take_name})`
          }
          if(res.data.diy_deal_name){
            //成交人
            this.chnegjiaouser += `(${res.data.diy_deal_name})`
          }
        }
      })
    },
    userSearch(){
        this.userparams.user_name = this.input2
        this.show_add_memberA = false
        this.Allfalse = true
        this.showAccompanyA()

    },
    showAccompanyA(){
      // this.appointAccompany = []
      this.$http.getDepartmentMemberList(this.userparams).then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          console.log(this.serverData);
          this.show_add_memberA = true
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: *********,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key+=父级
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      })
    },
    // 递归数据处理
    recursionData(data) {
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    // 选中变化时触发
    selecetedMember(e) {
      // console.log(e.checkedKeys);
      _this.params.admin_id = e.checkedKeys.join("");
    },
    PersonnelChange(val) {
      this.selectedIds = val;
    },
    //树形结构
    handleNodeClick(data) {
        console.log(data);
    },
    //树形结构确定
    // selectMemberOk(){
    //   // console.log(this.selectedIds);
    //   this.appointAccompany = this.selectedIds
    //    this.show_add_member = false;
    // },
      //接待录入人
      Enteredby() {
        _this.show_add_member = true
        _this.membertitle = "请选择录入人"
        _this.params.type = 1
      },
      //跟进维护人
      Maintainer() {
        _this.show_add_member = true
        _this.membertitle = "请选择维护人"
        _this.params.type = 2
      },
      //带看人
      look_people() {
        _this.show_add_member = true
        _this.membertitle = "请选择带看人"
        _this.params.type = 3
      },
      //成交人
      Trader() {
        _this.show_add_member = true
        _this.membertitle = "请选择成交人"
        _this.params.type = 4
      },
      // //选中成员
      // selecetedMember(e) {
      //   console.log(e.checkedKeys);
      //   _this.params.admin_id = e.checkedKeys[0]
      // },
      //共享维护人
      Shared_maintenance(){
          this.sharecompleted = false
        if(_this.l_list.share_admins.length==4){
          this.$message({
            type: "warning",
            message: '最多添加4个共享维护人!'
          });
          return
        }
        _this.show_add_member = true
        _this.membertitle = "请选择共享维护人"
        this.share = 1
        _this.params.type = 5
      },
       //删除共享维护人
    dellshare(item){
      this.$confirm('此操作将永久删除该共享维护人, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.dellsharedmaintainer(item.share_id).then(res=>{
            if(res.status == 200){
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.$emit("getDataAgain", {})
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
      //确定修改
      dialogVisible() {
        this.sharecompleted = true
        _this.params.client_id = _this.l_list.id
        console.log(_this.params);
        if(_this.params.type==5){
        this.Sharedparmas.client_id = _this.params.client_id
        this.Sharedparmas.shared_admin_id = _this.params.admin_id
        _this.$http.addsharedmaintainer(this.Sharedparmas).then(res=>{
          if (res.status==200) {
            this.$message({
              type:"success",
              message:"共享成功"
            })
            this.sharecompleted = false
            _this.show_add_member = false
            this.$emit("getDataAgain", {})
          }else{
            _this.show_add_member = false
          }
        })
      }else{
        if(_this.params.type == 3){
          // console.log(this.lookchecked);
          if (typeof this.lookchecked === 'string') {  
            // 将字符串转换为数字  
            _this.params.is_sync = Number(this.lookchecked);  
          }else{
            _this.params.is_sync = this.lookchecked
          }
        }
        _this.$http.Modifycustomermaintainer(_this.params).then(res => {
          if (res.status == 200) {
            console.log(res);
            _this.$message({
              type: "success",
              message: "修改成功"
            })
            this.Allfalse = false
            _this.show_add_member = false;
            this.lookchecked = 0
            this.$emit("getDataAgain", {})
            // location.reload();
          }
        })
      } 
      },
      moveLeft(status){
        if(status==1){
          if(this.fistThecard == true){
              // 前三
              this.fistThecard = false
              // 中间三
              this.The_card = true
              //后三
              this.Theshare_card = false
              this.moveright1 = false
              this.moveright2 =true
              this.disabled = true
              this.disabled1 = false
              if(this.The_card == true &&_this.l_list.share_admins.length==2||_this.l_list.share_admins.length<2){
                this.rightdisabled1 = true
              }
              return
          }
          // 前三
          this.fistThecard = false
          // 中间三
          this.The_card = false
          //后三
          this.Theshare_card = true
          // 左箭头
          this.disabled = false
          // 右箭头
          this.disabled = false
          this.disabled1 = true
          this.rightdisabled = true
          this.moveright1 = true
          this.moveright2 =false
          this.rightdisabled1 = true
        }else{
          if(_this.l_list.share_admins.length==2||_this.l_list.share_admins.length<2){
          // 前三
          this.fistThecard = false
          // 中间三
          this.The_card = true
          // 左箭头
          this.disabled = false
          // 右箭头
          this.rightdisabled = true
          this.moveright1 = false
          this.moveright2 =true
          this.disabled = true
          this.disabled1 = false
          this.rightdisabled1 = true
        }else{
          // 前三
          this.fistThecard = false
          // 中间三
          this.The_card = true
          //后三
          this.Theshare_card = false
          // 左箭头
          this.disabled = false
          // 右箭头
          this.rightdisabled = true
          this.moveright1 = false
          this.moveright2 = true
          this.moveLeft1 = false
          this.moveLeft2 = true
          this.rightdisabled1 = false
        } 
        }
        
      },
      moveright(status){
        if(status == 1){
          if(_this.l_list.share_admins.length==2||_this.l_list.share_admins.length<2){
            this.disabled1 = true
          }
          this.fistThecard = true
          this.The_card = false
          this.rightdisabled = false
          this.disabled = false
          if(this.fistThecard == true){
            this.disabled1 = true
            this.rightdisabled1 = false
          }
        }else{
          if(this.The_card == true){
              // 前三
              this.fistThecard = true
              // 中间三
              this.The_card = false
              //后三
              this.Theshare_card = false
              this.moveright1 = true
              this.moveright2 =false
              this.disabled = true
              this.disabled1 = false
              return
          }
          // 前三
          this.fistThecard = false
          // 中间三
          this.The_card = true
          //后三
          this.Theshare_card = false
          // 左箭头
          this.disabled = false
          // 右箭头
          this.rightdisabled = true
          this.moveLeft1 = false
          this.moveLeft2 = true
          this.moveright1 = true
          this.moveright2 =false
          this.disabled = false
          this.disabled1 = true
          this.rightdisabled1 = false

        }

      },
      // 获取是否有权限查看客户操作（创始人、维护人、客户管理员可见）
      getOneselfList() {
        if(_this.l_list.share_admins){
          this.l_list.share_admins = _this.l_list.share_admins.map(item => ({
        ...item,
        showDelete: false
      }));
        }
        _this.$http.getAdmin().then((res) => {
          if (res.status == 200) {
            // this.AdminData = res.data; // 存储自己的信息
            _this.selfID = res.data.id; // 赋值自己的id
            // 如果是站长就显示
            if(this.l_list && this.l_list.admin_list) {
              let mangers = _this.l_list.admin_list
              // let keyuanManger = 0, whr = 0
              if (mangers.includes(_this.selfID + "")) {
                _this.keyuanManger = 1
              }
            }
            if(this.l_list&&this.l_list.follow_id){
            if(_this.selfID==this.l_list.follow_id){
              this.addShared = 1
            }else{
              console.log("我不是维护人");
            }
          }
            if (_this.selfID == _this.l_list.follow_id) {
              _this.whr = 1
            }
          }
        })
      },
      handleMouseEnter() {
        if (_this.keyuanManger == 1) {
          _this.ModifyHidden = true
        }
      },
      handleMouseLeave() {
        _this.ModifyHidden = false
      },
      handleMouseEnter1() {
        if (_this.keyuanManger == 1) {
          _this.ModifyHidden1 = true
        }
        if (_this.keyuanManger == 1|| this.addShared == 1) {
        _this.ModifyHidden4 = true
        }
        if(this.information==1){
        _this.ModifyHidden4 = false
      }
      },
      handleMouseLeave1() {
        _this.ModifyHidden1 = false
        _this.ModifyHidden4 = false
      },
      handleMouseEnter2() {
        if (_this.keyuanManger == 1) {
          _this.ModifyHidden2 = true
        }
      },
      handleMouseLeave2() {
        _this.ModifyHidden2 = false
      },
      handleMouseEnter3() {
        if (_this.keyuanManger == 1) {
          _this.ModifyHidden3 = true
        }
      },
      handleMouseLeave3() {
        _this.ModifyHidden3 = false
      },
      handleMouseEnter4(index) {
      this.l_list.share_admins[index].showDelete = true;
      },
      handleMouseLeave4(index) {
        this.l_list.share_admins[index].showDelete = false;
      },
      handleMouseEnter5(item) {
        item.showDelete = true;
      },
      handleMouseLeave5(item) {
        item.showDelete = false;
      },
      getData() {
        this.$nextTick(() => {
          var chartDom = document.getElementById("bing-main");
          var myChart = echarts.init(chartDom);
          var option;
  
          option = {
            tooltip: {
              trigger: "item",
            },
            series: [
              {
                name: "Access From",
                type: "pie",
                radius: ["40%", "70%"],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: "center",
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: "20",
                  },
                },
                labelLine: {
                  show: false,
                },
                data: [
                  { value: 1048, name: "Search Engine" },
                  { value: 735, name: "Direct" },
                  { value: 580, name: "Email" },
                  { value: 484, name: "Union Ads" },
                  { value: 300, name: "Video Ads" },
                ],
              },
            ],
          };
          option && myChart.setOption(option);
        });
      },
      // 复制客户编号
      copyCusID(id) {
        this.$onCopyValue(id);
      },
    },
  };
  </script>
  
<style scoped lang="scss">
.portrait {
  margin-top: 20px;

  .left {
    margin-top: 7%;
    margin-right: 15px;
  }

  .right {
    margin-top: 7%;
    margin-left: 5px;
  }

  .pl {
    // border-top: 1px solid rgba(221, 225, 233, 1);
    // border-bottom: 1px solid rgba(221, 225, 233, 1);
    // border-radius: 4px;
    // padding: 24px;
    width: 100%;
    height: 120px;
    padding: 10px;
    // margin-bottom: 78px;
    // background-color: palegoldenrod;
    font-size: 14px;
    display: flex;
    flex-direction: row;

    // overflow: auto;
    .pl-list {
      width: 30%;
      // margin-bottom: 24px;
      height: 100px;
      margin-right: 10px;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #D9D9D9;
      padding-bottom: 10px;
      transition: margin-left 0.5s ease-in-out;

      /* 添加过渡效果 */
      .title {
        color: #2e3c4e;
        margin-bottom: 12px;
      }

      .pl-con {
        .img {
          width: 32px;
          height: 32px;

          .icon {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: #2d84fb;
            color: #fff;
            font-size: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        img {
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }

        .default-name {
          margin-left: 12px;
          color: #8a929f;
        }

        .img-r {
          margin-left: 12px;
          color: #8a929f;

          .img-r-dep {
            margin-top: 8px;
            font-size: 12px;
          }
        }
      }

      .Publictime {
        margin-top: 10px;
        justify-content: space-between;
        font-size: 12px;
      }
    }
  }

  .pr {
    width: 100%;
    margin-left: 24px;

    .label-box {
      margin-top: 24px;
      flex: 1;
      flex-wrap: wrap;

      .label-item {
        display: inline-block;
        margin-bottom: 24px;
        margin-right: 24px;
        font-size: 18px;
        color: #2d84fb;
        padding: 7px 23px;
        background: rgba(45, 132, 251, 0.15);
        border: 1px solid rgba(45, 132, 251, 1);
        border-radius: 4px;
      }
    }

    .pr-l {
      margin-top: 24px;
    }

    .bing-main {
      width: 280px;
      height: 280px;
      background: #f8f8f8;
      border-radius: 4px;
      margin-top: 24px;
    }

    .build {
      padding: 24px 12px;

      .build-list {
        align-items: center;
        margin-bottom: 24px;

        .pm {
          width: 20px;
          height: 20px;
          color: #fff;
          background: #d8d8d8;
          text-align: center;
          line-height: 20px;
          border-radius: 2px;
        }

        .pm0 {
          background: #fb656a;
        }

        .pm1 {
          background: #fb8968;
        }

        .pm2 {
          background: #fbc365;
        }

        span {
          margin-right: 12px;
          font-size: 14px;
          color: #8a929f;
        }
      }
    }

    .pr-r {
      margin-left: 24px;

      .name {
        font-size: 14px;
        color: #2e3c4e;
      }
    }
  }

  .title {
    span {
      font-size: 11px;
      color: #8a929f;
    }
  }

  .shareuser {
    display: flex;
    justify-content: space-between;
  }
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
}

.lookstyle {
  margin-top: 20px;
  margin-left: 10px;

  .el-checkbox {
    color: rgb(230, 162, 60);
  }
}

.blable {
  align-items: center;
  justify-content: space-between;

  .btn {
    border: 1px solid rgba(138, 146, 159, 1);
    font-size: 16px;
    color: #8a929f;
    padding: 9px 15px;
    border-radius: 2px;
    margin-right: 10px;
    cursor: pointer;
    align-items: center;
  }
}

.labelname {
  margin-bottom: 10px;
  font-size: 12px;
  color: #8a929f;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;

    &.checked {
      background: rgba(45, 132, 251, 0.15);
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }
}

.title_con {
  font-weight: 600;
  font-size: 18px;
  padding: 10px 0;
}

.customer_info {
  border-bottom: 1px solid #f6f6f6;

  .customer_info_item {
    color: #8a929f;
    width: 33%;
    padding: 15px 5px;
    box-sizing: border-box;

    .customer_info_item_bottom {
      align-items: center;
      min-height: 24px;
      font-size: 13px;
      margin-top: 10px;
      color: #333;
    }
  }
}

.blue_color {
  color: #3d91ff;
  font-size: 18px;
}

.cus_id {
  width: 11px;
  margin-left: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.orange_color {
  color: #ffb84e;
  font-size: 18px;
}

.mt10 {
  margin-top: 10px;
}

.more_text_content {
  max-width: 380px;
  text-align: left;
  line-height: 18px;
}

.customer_info_content {
  max-width: 194px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.first-take-look {
  ::v-deep .el-tag {
    height: 19px;
    line-height: 19px;
    margin-left: 5px;
  }
}
</style>
  