<template>
    <div class="ai-analysis-table">
        <myTable
			:table-list="curList"
			:header="columns"
			tooltipEffect="light"
			v-loading="loading"
            :header-cell-style="{ background: '#EBF0F7' }"
			highlight-current-row
			:row-style="$TableRowStyle"
			:cellClassName="cellClassName"
			ref="myTable">
		</myTable> 

		<!-- 空数据提示 -->
		<div v-if="!loading && curList.length === 0" class="empty-data">
			<el-empty description="暂无AI分析数据" :image-size="100">
			</el-empty>
		</div>

		<!-- AI分析组件 -->
		<alanalysis ref="alanalysis" @getFollowData="handleRefreshData" @getClientTelephoneRecord="handleRefreshData"></alanalysis>
	</div>
</template>

<script>
import myTable from "@/components/components/my_table";
import alanalysis from '@/components/components/AI/alanalysis.vue';

export default {
	name: 'AiAnalysisTable',
    props: {
        params: { type: Object, default: () => ({}) },
        list: { type: Array, default: () => [] },
		loading: { type: Boolean, default: false }
    },
	components: {
		myTable,
		alanalysis
	},
	data() {
		return {
			curList: []
		}
	},
	watch: {
		list: {
			handler(data){
				this.curList = data.map(e => {
					return {...e};
				})
			},
			immediate: true
		}
	},
	computed: {
		columns(){
			const userColumn = {
				prop: "id",
				label: "客户信息",
				fixed: "left",
				minWidth: 220,
				render: (h, data) => {
					const avatarContent = data.row.dy_avatar ?
						h('img', {
							attrs: { src: data.row.dy_avatar },
							class: 'avatar-img'
						}) :
						h('span', {
							class: 'avatar-word'
						}, this.getFirstWord(data.row.cname));

					return h('div', {
						class: ['user-box', { client: data.row.client_id }],
						on: {
							click: () => this.goCustomerDetail(data.row)
						}
					}, [
						h('div', { class: 'avatar' }, [avatarContent]),
						h('div', { class: 'info' }, [
							h('p', {
								class: 'nickname',
								attrs: { title: data.row.cname }
							}, data.row.cname)
						])
					]);
				}
			};
			const opColumn = {
				label: "操作",
				minWidth: 180,
				fixed: 'right',
				render: (h, data) => {
					return !data.row.call_out_id ? 
						<div>--</div> :
						<div>
							<el-link type="primary" onClick={() => this.openAiAnalysis(data.row)}>详情</el-link>
						</div>;
				}
			};

			return [
				userColumn,
				{ prop: "call_summary", label: "AI总结",fixed: "left", minWidth: 280, render: (h, data) => this.renderSummary(h, data) },
				{ prop: "follow_name", label: "维护人", minWidth: 100, render: (h, data) => this.renderFollowName(h, data) },
				{ prop: "health", label: "健康度", minWidth: 80, render: (h, data) => this.renderHealth(h, data) },
				{ prop: "deal_level", label: "成交意向", minWidth: 100, render: (h, data) => this.renderDealLevel(h, data) },
				{ prop: "loss_level", label: "流失风险", minWidth: 100, render: (h, data) => this.renderLossLevel(h, data) },
				{ prop: "demand_level", label: "客户需求", minWidth: 100, render: (h, data) => this.renderDemandLevel(h, data) },
				{ prop: "keywords", label: "关键词", minWidth: 120, render: (h, data) => this.renderKeywords(h, data) },
				{ prop: "label", label: "标签", minWidth: 120, render: (h, data) => this.renderLabel(h, data) },
				{ prop: "intention_community", label: "兴趣偏好", minWidth: 120, render: (h, data) => this.renderIntentionCommunity(h, data) },
				{ prop: "price_range", label: "价格区间", minWidth: 120, render: (h, data) => this.renderPriceRange(h, data) },
				{ prop: "decision_impact", label: "决策影响", minWidth: 120, render: (h, data) => this.renderDecisionImpact(h, data) },
				{ prop: "is_cover", label: "资料维护", minWidth: 100, render: (h, data) => this.renderCoverStatus(h, data) },
				{ prop: "assign_operate", label: "AI分配", minWidth: 120, render: (h, data) => this.renderAssignOperate(h, data) },
				{ prop: "created_at", label: "分析时间",fixed: "right", minWidth: 160, render: (h, data) => this.renderTime(h, data) },
				opColumn
			];
		}
	},
	methods: {
		// 获取昵称首字
		getFirstWord(name) {
			return name ? name.charAt(0) : '';
		},
		


		// 渲染成交意向
		renderDealLevel(h, data) {
			const level = data.row.deal_level;

			// 容错处理：处理空值、null、undefined
			if (!level || level === '' || level === null || level === undefined) {
				return h('span', {
					class: ['deal-level-tag', 'deal-none']
				}, '无');
			}

			const classMap = {
				'A': 'deal-level-a',
				'B': 'deal-level-b',
				'C': 'deal-level-c',
				'D': 'deal-level-d',
				'无': 'deal-none',
				// 可能的值 兼容下
				'a': 'deal-level-a',
				'b': 'deal-level-b',
				'c': 'deal-level-c',
				'd': 'deal-level-d'
			};

			const cssClass = classMap[level] || 'deal-none';

			return h('span', {
				class: ['deal-level-tag', cssClass]
			}, level + '级');
		},

		// 渲染流失风险
		renderLossLevel(h, data) {
			const level = data.row.loss_level;

			if (!level || level === '' || level === null || level === undefined) {
				return h('span', '无');
			}

			return h('span', level);
		},

		// 渲染客户需求
		renderDemandLevel(h, data) {
			const level = data.row.demand_level;

			if (!level || level === '' || level === null || level === undefined) {
				return h('span', '无');
			}

			return h('span', level);
		},

		// 渲染健康度
		renderHealth(h, data) {
			const health = data.row.health;

			if (!health || health === '' || health === null || health === undefined) {
				return h('span', '未知');
			}

			// 根据健康度值设置文字颜色
			const colorMap = {
				'健康': '#000000',  // 黑色
				'违规': '#ef4444',  // 红色
				// 可能的值 兼容下
				'正常': '#000000',
				'良好': '#000000',
				'异常': '#ef4444',
				'不健康': '#ef4444',
				'风险': '#ef4444'
			};

			const color = colorMap[health] || '#000000';

			return h('span', {
				style: { color: color }
			}, health);
		},

		renderKeywords(h, data) {
			return h('div', [
				data.row.keywords ?
				h('el-popover', {
					props: {
						placement: 'top-start',
						trigger: 'hover',
						popperClass: 'adaptive-popover'
					}
				}, [
					h('div', {
						class: 'comment-popover adaptive-content',
						domProps: {
							innerHTML: data.row.keywords.replace(/\n/g, '<br>')
						}
					}),
					h('div', {
						slot: 'reference'
					}, [
						h('div', {
							class: 'comment'
						}, data.row.keywords)
					])
				]) : ''
			]);
		},

		renderLabel(h, data) {
			return h('div', [
				data.row.label ?
				h('el-popover', {
					props: {
						placement: 'top-start',
						trigger: 'hover',
						popperClass: 'adaptive-popover'
					}
				}, [
					h('div', {
						class: 'comment-popover adaptive-content',
						domProps: {
							innerHTML: data.row.label.replace(/\n/g, '<br>')
						}
					}),
					h('div', {
						slot: 'reference'
					}, [
						h('div', {
							class: 'comment'
						}, data.row.label)
					])
				]) : ''
			]);
		},

		renderSummary(h, data) {
			return h('div', [
				data.row.call_summary ?
				h('el-popover', {
					props: {
						placement: 'top-start',
						width: '380',
						trigger: 'hover'
					}
				}, [
					h('div', [
						// 添加标题区域
						h('div', {
							class: 'flex-row',
							style: { marginBottom: '1px' }
						}, [
							h('div', {
								class: 'colorAI'
							}, 'AI'),
							h('span', '智能总结')
						]),
						// 内容区域
						h('div', {
							class: 'comment-popover',
							domProps: {
								innerHTML: data.row.call_summary.replace(/\n/g, '<br>')
							}
						})
					]),
					h('div', {
						slot: 'reference'
					}, [
						h('div', {
							class: 'comment'
						}, data.row.call_summary)
					])
				]) : ''
			]);
		},

		// 渲染资料维护状态
		renderCoverStatus(h, data) {
			const isCover = data.row.is_cover;
			const statusText = isCover == 1 ? '已采纳' : '未采纳';
			
			return h('span', statusText);
		},

		// 渲染AI分配内容
		renderAssignOperate(h, data) {
			const assignOperate = data.row.assign_operate;
			
			if (!assignOperate || assignOperate === '' || assignOperate === null || assignOperate === undefined) {
				return h('span', '--');
			}

			return h('div', [
				h('el-popover', {
					props: {
						placement: 'top-start',
						trigger: 'hover',
						width: '300'
					}
				}, [
					h('div', {
						class: 'comment-popover',
						domProps: {
							innerHTML: assignOperate.replace(/\n/g, '<br>')
						}
					}),
					h('div', {
						slot: 'reference',
						class: 'assign-content',
						style: {
							maxHeight: '40px',
							overflow: 'hidden',
							textOverflow: 'ellipsis',
							display: '-webkit-box',
							webkitLineClamp: 2,
							webkitBoxOrient: 'vertical'
						}
					}, assignOperate)
				])
			]);
		},

		// 渲染普通文本
		renderText(h, data, field) {
			const text = data.row[field] || '';
			return text ? h('span', text) : h('span', '-');
		},

		renderIntentionCommunity(h, data) {
			return h('div', [
				data.row.intention_community ?
				h('el-popover', {
					props: {
						placement: 'top-start',
						trigger: 'hover',
						popperClass: 'adaptive-popover'
					}
				}, [
					h('div', {
						class: 'comment-popover adaptive-content',
						domProps: {
							innerHTML: data.row.intention_community.replace(/\n/g, '<br>')
						}
					}),
					h('div', {
						slot: 'reference'
					}, [
						h('div', {
							class: 'comment'
						}, data.row.intention_community)
					])
				]) : ''
			]);
		},

		renderPriceRange(h, data) {
			return h('div', [
				data.row.price_range ?
				h('el-popover', {
					props: {
						placement: 'top-start',
						trigger: 'hover',
						popperClass: 'adaptive-popover'
					}
				}, [
					h('div', {
						class: 'comment-popover adaptive-content',
						domProps: {
							innerHTML: data.row.price_range.replace(/\n/g, '<br>')
						}
					}),
					h('div', {
						slot: 'reference'
					}, [
						h('div', {
							class: 'comment'
						}, data.row.price_range)
					])
				]) : ''
			]);
		},

		renderDecisionImpact(h, data) {
			return h('div', [
				data.row.decision_impact ?
				h('el-popover', {
					props: {
						placement: 'top-start',
						trigger: 'hover',
						popperClass: 'adaptive-popover'
					}
				}, [
					h('div', {
						class: 'comment-popover adaptive-content',
						domProps: {
							innerHTML: data.row.decision_impact.replace(/\n/g, '<br>')
						}
					}),
					h('div', {
						slot: 'reference'
					}, [
						h('div', {
							class: 'comment'
						}, data.row.decision_impact)
					])
				]) : ''
			]);
		},

		// 渲染时间
		renderTime(h, data) {
			const time = data.row.created_at || '';
			return time ? h('span', time) : h('span', '-');
		},

		// 渲染维护人
		renderFollowName(h, data) {
			const followName = data.row.follow_name || '';
			return followName ? h('span', followName) : h('span', '--');
		},
		
		// 跳转客户详情
		goCustomerDetail(row) {
			this.$emit('goCustomerDetail', row);
		},

		// 打开AI分析详情
		openAiAnalysis(row) {
			if (!row.call_out_id) {
				this.$message.warning('暂无AI分析详情记录');
				return;
			}

			this.$refs.alanalysis.open({
				call_record_id: row.call_out_id,
				...row 
			});
		},

		// 刷新数据
		refreshData() {
			this.$emit('refreshData');
		},

		// 处理AI分析组件的刷新事件
		handleRefreshData() {
			//this.$emit('refreshData');
		},
		
		// 表格行样式
		cellClassName({row, column}) {
			if(column.property === 'id' && row.client_id) {
				return 'cursor-pointer';
			}
			return '';
		}
	}
}
</script>

<style scoped>

::v-deep .user-box {
	display: flex;
	flex-direction: row;
	min-height: 50px;
	align-items: flex-start;
	padding: 8px 0;
}

::v-deep .user-box.client {
	cursor: pointer;
}

::v-deep .user-box:hover {
	color: #409EFF;
}

::v-deep .avatar {
	height: 32px;
}

::v-deep .avatar-img {
	height: 32px;
	width: 32px;
	border-radius: 50%;
	object-fit: cover;
}

::v-deep .avatar-word {
	display: inline-block;
	height: 32px;
	width: 32px;
	border-radius: 50%;
	background-color: #f1f2f3;
	line-height: 32px;
	text-align: center;
	color: #c6c6c6;
	font-weight: 300;
}

::v-deep .info {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	padding: 0 0 0 12px;
	flex: 1;
}

::v-deep .nickname {
	text-align: left;
	font-weight: 500;
	color: #333;
	word-wrap: break-word;
	word-break: break-all;
	line-height: 1.4;
	margin: 0;
	max-width: 180px;
}

::v-deep .cursor-pointer {
	cursor: pointer;
}

.empty-data {
	padding: 40px 0;
	text-align: center;
}

::v-deep .status-tag {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 500;
	text-align: center;
	min-width: 40px;
}


::v-deep .deal-level-tag {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 500;
	text-align: center;
	min-width: 40px;
	color: #fff;
}

::v-deep .deal-level-a {
	background: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
}

::v-deep .deal-level-b {
	background: linear-gradient(180deg, #f8a707, #f85d02 100%);
}

::v-deep .deal-level-c {
	background: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
}

::v-deep .deal-level-d {
	background: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
}

::v-deep .deal-none {
	background-color: #f9fafb;
	color: #6b7280;
	border: 1px solid #e5e7eb;
}



/* 无状态样式 */
::v-deep .status-none {
	background-color: #f9fafb;
	color: #6b7280;
	border: 1px solid #e5e7eb;
}

/* comment样式 - 超出2行自动隐藏，文字左对齐 */
::v-deep .comment {
	text-align: left !important;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-word;
	word-wrap: break-word;
	white-space: normal;
	line-height: 1.8; 
	max-height: 3.6em;
}

::v-deep .comment-popover {
	line-height: 1.5;
}

::v-deep .el-table .cell {
	padding: 8px 10px;
	line-height: 1.8;
	vertical-align: middle;
}

::v-deep .el-table__row {
	height: auto;
}

::v-deep .el-table td {
	padding: 8px 0;
	vertical-align: middle;
}



</style>

<style>
.ai-analysis-table .user-box {
	display: flex;
	flex-direction: row;
	min-height: 50px;
	align-items: flex-start;
	padding: 8px 0;
}

.ai-analysis-table .user-box.client {
	cursor: pointer;
}

.ai-analysis-table .user-box:hover {
	color: #409EFF;
}

.ai-analysis-table .avatar {
	height: 32px;
}

.ai-analysis-table .avatar-img {
	height: 32px;
	width: 32px;
	border-radius: 50%;
	object-fit: cover;
}

.ai-analysis-table .avatar-word {
	display: inline-block;
	height: 32px;
	width: 32px;
	border-radius: 50%;
	background-color: #f1f2f3;
	line-height: 32px;
	text-align: center;
	color: #c6c6c6;
	font-weight: 300;
}

.ai-analysis-table .info {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	padding: 0 0 0 12px;
	flex: 1;
}

.ai-analysis-table .nickname {
	text-align: left;
	font-weight: 500;
	color: #333;
	word-wrap: break-word;
	word-break: break-all;
	line-height: 1.4;
	margin: 0;
	max-width: 180px;
}

/* AI总结Popover样式 */
.ai-summary-popover {
	border: 1px solid #FFFFFF;
	box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
	border-radius: 8px;
}

.ai-summary-popover .el-popover__title {
	color: #303133;
	font-weight: 600;
	margin-bottom: 8px;
}

.ai-summary-content {
	max-width: 300px;
	word-break: break-word;
	white-space: pre-wrap;
	line-height: 1.6;
	color: #303133;
	background-color: #f8faff;
	padding: 12px;
	border-radius: 6px;
	border-left: 3px solid #409EFF;
	font-size: 14px;
}

.summary-text {
	cursor: pointer;
	color: #409EFF;
	border-bottom: 1px dotted #409EFF;
}

.summary-text:hover {
	color: #66b1ff;
	border-bottom-color: #66b1ff;
}

/* AI标识样式 */
.colorAI {
	width: 20px;
	height: 20px;
	border: 2px solid #f85d02;
	border-radius: 50%;
	color: #f85d02;
	text-align: center;
	line-height: 19px;
	margin-right: 10px;
	margin-bottom: 10px;
	font-size: 12px;
	font-weight: bold;
}



/* AI分配内容样式 */
.assign-content {
	line-height: 1.5;
	word-break: break-word;
	word-wrap: break-word;
	white-space: normal;
}

/* 自适应宽度的 Popover 样式 */
.adaptive-popover {
	min-width: 120px !important;
	max-width: 380px !important;
	width: auto !important;
}

.adaptive-popover .el-popover__content {
	padding: 12px !important;
	word-break: break-word;
	white-space: pre-wrap;
	line-height: 1.5;
}

.adaptive-content {
	display: inline-block;
	width: auto;
	min-width: 100px;
	max-width: 360px;
	word-break: break-word;
	white-space: pre-wrap;
	line-height: 1.5;
}
</style>
