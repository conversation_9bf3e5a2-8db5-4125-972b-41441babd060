<template>
  <el-container>
    <el-header class="div row" style="justify-content: space-between;">
      <div class="div row">
        <div class="title">交易记录</div>
        <div class="title_number">
          <div>
            当前页面共(
            <i>{{ tableData.length }}</i>
            )条数据
          </div>
        </div>
      </div>
      <div class="div row">
        <el-input
          @change="onChange"
          v-model="params.sale_order_id"
          placeholder="请输入成交单ID"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <div class="pagination-box">
      <myPagination
        :total="params.total"
        :currentPage="params.page"
        :pagesize="params.per_page"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </div>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "transaction_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        sale_order_id: "",
      },
      filter_status: [
        { text: "已完成", value: 1 },
        { text: "未完成", value: 0 },
      ],
      table_header: [
        {
          expand: "expand",
          render: (h, data) => {
            return (
              <el-form label-position="left" inline class="demo-table-expand">
                {data.row.company_id !== 0 ? (
                  <el-form-item label="成交ID：">
                    <span>{data.row.batch_id}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
                {data.row.company_id !== 0 ? (
                  <el-form-item label="成交单号：">
                    <span>{data.row.batch_sn}</span>
                  </el-form-item>
                ) : (
                  ""
                )}

                <el-form-item label="收入/支出：">
                  <span>{data.row.category === 0 ? "收入" : "支出"}</span>
                </el-form-item>
                {data.row.company_id !== 0 ? (
                  <el-form-item label="交易公司：">
                    <span>{data.row.transaction_company_name}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
              </el-form>
            );
          },
        },
        { prop: "id", label: "ID", width: "80" },
        { prop: "amount", label: "交易金额/元" },
        { prop: "remark", label: "交易备注" },
        { prop: "u_name", label: "交易用户" },
        {
          label: "交易情况",
          filters: [
            { text: "已完成", value: 1 },
            { text: "未完成", value: 2 },
          ],
          filterMethods: (value, row) => {
            return row.status === value;
          },
          render: (h, data) => {
            return (
              <el-tag
                type={data.row.status === 1 ? "success" : "primary"}
                disable-transitions
              >
                {data.row.status === 1 ? "已完成" : "未完成"}
              </el-tag>
            );
          },
        },
        { prop: "u_phone", label: "联系方式" },
        { prop: "created_at", label: "交易日期" },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      if (!this.params.sale_order_id) {
        delete this.params.sale_order_id;
      }
      this.$http.getTransactionList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onChange() {
      this.search();
    },
    search() {
      this.params.page = 1;
      this.getDataList();
    },
    filterStatus(value, row) {
      return row.status === value;
    },
  },
};
</script>

<style lang="scss">
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.demo-table-expand {
  font-size: 0;
  flex-wrap: wrap;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
</style>
