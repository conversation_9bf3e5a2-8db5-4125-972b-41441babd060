<template>
  <!-- 违规提醒 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm padd0">
          <div class="div row align-center">
            <div class="title flex-1">违规提醒</div>
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px; padding: 0">
          <div
            class="bottom-border div row align-center"
            style="padding: 24px 0 0 24px"
          >
            <span class="text">发生时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="value1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </div>
          <div class="bottom-border div row" style="padding: 24px 0 0 24px">
            <span class="text">违规类型：</span>
            <div class="labels flex-row align-center">
              <span class="label active"> 全部</span>
              <span class="label"> 删除客户</span>
              <span class="label"> 撤回消息</span>
              <span class="label"> 不文明用于</span>
            </div>
          </div>

          <div class="bottom-border div row" style="padding: 24px 0 0 24px">
            <span class="text">负责人：</span>
            <el-select size="small" v-model="value" placeholder="请选择">
              <el-option
                v-for="item in group_manager_list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="bottom-border div row" style="padding: 24px 0 0 24px">
            <span class="text">所属部门：</span>
            <el-select size="small" v-model="value" placeholder="请选择">
              <el-option
                v-for="item in group_manager_list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right div row"></div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
            <el-table-column prop="name" label="二维码" v-slot="{ row }">
              <el-link type="primary" @click="onClickDetail(row.id)">{{
                row.name
              }}</el-link>
            </el-table-column>
            <el-table-column label="活码名称">
              <template slot-scope="scope">
                {{ scope.row.owner.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column label="创建人">
              <template slot-scope="scope">
                <span
                  style="margin-right: 10px"
                  v-for="(item, index) in scope.row.admin_list"
                  :key="index"
                  >{{ item }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="接待员工" prop="notice"> </el-table-column>
            <el-table-column label="添加好友数" v-slot="{ row }">
              {{ row.member ? row.member.member_count : 0 }}
            </el-table-column>

            <el-table-column
              label="创建时间"
              prop="created_at"
            ></el-table-column>
            <el-table-column label="操作" v-slot="{ row }">
              <el-link type="primary" @click="onChangeEdit(row)">编辑</el-link>
              <el-link type="primary" @click="download(row)">下载</el-link>
              <el-link type="primary" @click="copyLink(row)">复制链接</el-link>
              <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteMemberQrcode(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import myLabel from "../components/my_label";
export default {
  components: {
    myLabel
  },
  data() {
    return {
      time_list: [
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
      ],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      employees_list: [
        { id: 0, name: "跟进人正常" },
        { id: 1, name: "跟进人离职" },
        { id: 2, name: "离职继承中" },
        { id: 3, name: "离职继承完成" },
      ],
      group_manager_list: [
        {
          value: "选项1",
          label: "黄金糕",
        },
        {
          value: "选项2",
          label: "双皮奶",
        },
        {
          value: "选项3",
          label: "蚵仔煎",
        },
        {
          value: "选项4",
          label: "龙须面",
        },
        {
          value: "选项5",
          label: "北京烤鸭",
        },
      ],
      value: "",
      value1: "",



      tableData: [
        {
          notice: '',
          owner: {
            name: ''
          },
          admin_list: [

          ],
          member: []
        }
      ],
      is_table_loading: false,
      multipleSelection: [],
    }
  },
  methods: {
    onClickTime(item) {
      console.log(item.id);
    },
    getDataList() {

    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
  }
}
</script>

<style lang ="scss" scoped>
.bottom-border {
  .labels {
    .label {
      margin-right: 16px;
      padding: 3px 16px;
      color: #8a929f;
      font-size: 14px;
      &.active {
        border-radius: 4px;
        background: #e8f1ff;
        color: #2d84fb;
      }
    }
  }
}
</style>