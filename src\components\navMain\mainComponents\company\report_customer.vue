<template>
  <el-container>
    <el-header class="div row" style="justify-content: space-between">
      <myTopTips title="客户列表" :number="tableData.length"></myTopTips>
      <div class="div row">
        <!-- <el-dropdown @command="handleCommand" style="width:200px">
          <span class="el-dropdown-link">
            选择搜索方式<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dropdown_search"
              :key="item.value"
              :command="item.value"
              >{{ item.name }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown> -->

        <!-- <el-select
          clearable
          v-model="params.company_id"
          filterable
          remote
          placeholder="请输入公司名称"
          :remote-method="remoteMethod"
          :loading="loading_company"
          @change="changeCompany"
        >
          <el-option
            v-for="item in company_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select> -->
        <el-select clearable v-model="params.build_id" filterable remote placeholder="请输入楼盘名称"
          :remote-method="remoteMethodBuild" :loading="loading_build" @change="changeBuild">
          <el-option v-for="item in build_list" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <el-input @input="onInput" @change="onChange" v-model="input" placeholder="客户手机号搜索"></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
      </div>
    </el-header>
    <!-- <div class="div row" style="justify-content: flex-start;">
      <el-button
        :plain="btnPlain"
        @click="onClickAll"
        type="primary"
        style="border-radius: 4px; margin:5px"
        >全部</el-button
      >
      <el-button
        plain
        v-for="item in report_list"
        :key="item.value"
        type="primary"
        style="border-radius: 4px; margin:5px"
        @click="onClickStatus(item)"
      >
        {{ item.description }}({{ item.total || 0 }})
      </el-button>
    </div> -->
    <div class="browse-table">
      <div class="browse div row">
        <div style="justify-content: flex-start" class="browse-item div row" v-for="(item, index) in time_array"
          :key="index" :class="{ browse_active: item.value === time_data_type }" @click="onClickBrowse(index, item.id)">
          {{ item.desc }}
        </div>
      </div>
      <el-date-picker class="pickerDate" v-if="isCustomize" v-model="time_value" type="daterange" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="changeDate">
      </el-date-picker>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>报备客户量</p>
              <p class="desc">报备客户的总数量（单）</p>
            </div>
            <div class="right">{{ report_info.reported_total }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>已成交客户量</p>
              <p class="desc">成交客户的总数量（单）</p>
            </div>
            <div class="right">{{ report_info.deal_total }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>已成交金额</p>
              <p class="desc">有效成交的价格总金额(万元)</p>
            </div>
            <div class="right">{{ report_info.deal_amount }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>已成交佣金</p>
              <p class="desc">有效成交的佣金总金额(元)</p>
            </div>
            <div class="right">{{ report_info.brokerage_amount }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>未完成佣金</p>
              <p class="desc">待发放的佣金总金额(元)</p>
            </div>
            <div class="right">{{ report_info.not_settle }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>已发放佣金</p>
              <p class="desc">已发放的佣金总金额(元)</p>
            </div>
            <div class="right">{{ report_info.settled }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-tabs type="card" v-model="is_status" @tab-click="handleClick">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane v-for="item in report_list" :key="item.value" :label="item | fliterTabName"
        :name="item.value"></el-tab-pane>
    </el-tabs>
    <!-- 输入选择框 -->
    <!-- 数据列表 -->
    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
      v-loading="is_table_loading">
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column align="center" label="客户" width="auto" prop="customer_name">
        <template slot-scope="scope">
          <div class="scoped-box div row">
            <p style="width: 100px">{{ scope.row.customer_name }}</p>
            <reportTxt style="width: 100px" :type="scope.row.status"></reportTxt>
            <img v-if="scope.row.external_customer_id" style="
    width: 16px;
    height: 16px;
    margin-left: 5px;" src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png" alt="" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="customer_phone" label="联系方式" show-overflow-tooltip width="auto">
        <template slot-scope="scope">
          <p style="width: 100px">{{ scope.row.customer_phone }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="build_name" label="意向" width="auto">
        <template slot-scope="scope">
          <p>{{ scope.row.build_name }}</p>
          <!-- <el-tag v-if="scope.row.protected_day > 0" type="success">
              自动报备有效
            </el-tag>
            <el-tag type="danger" v-else>未开启自动报备有效</el-tag> -->
        </template>
      </el-table-column>
      <el-table-column prop="protected_day" label="报备保护天数">
        <template slot-scope="scope">
          <el-tag v-if="
                        scope.row.auto_reported_valid === 1 && scope.row.status !== 10
                      ">
            {{ scope.row.protected_day }}天
          </el-tag>
          <el-tag type="danger" v-else>未开启自动报备有效</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reported_valid_at" label="报备保护开始/结束" align="center">
        <template slot-scope="scope">
          <p v-if="
                        scope.row.auto_reported_valid === 1 && scope.row.status !== 10
                      ">
            <!-- <el-tag type="success">
                {{ scope.row.reported_valid_at || "未设置" }}
              </el-tag> -->
            <el-tag style="margin-top: 4px">
              {{ scope.row.protected_expired_at || "未设置" }}
            </el-tag>
          </p>
          <el-tag v-else type="danger">0天</el-tag>
          <!-- <el-tag v-if="scope.row.protected_day > 0">
              {{ scope.row.reported_valid_at }}
            </el-tag>
            <p v-else>无</p> -->
        </template>
      </el-table-column>
      <!-- <el-table-column prop="reported_valid_at" label="报备保护开始日期">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.protected_day > 0">
              {{ scope.row.reported_valid_at }}
            </el-tag>
            <p v-else>无</p>
          </template>
        </el-table-column>
        <el-table-column prop="protected_expired_at" label="报备保护结束日期">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.protected_day > 0">
              {{ scope.row.protected_expired_at }}
            </el-tag>
            <p v-else>无</p>
          </template>
        </el-table-column> -->
      <el-table-column width="auto" label="经纪人" show-overflow-tooltip>
        <template slot-scope="scope">
          <i style="margin-right: 8px" v-if="scope.row.u_name">{{
                      scope.row.u_name
                      }}</i>
          <i style="margin-right: 8px" v-else>{{ scope.row.u_nickname }}</i>

          <el-tag type="success" v-if="scope.row.sale_status === 1 && scope.row.status === 5">
            已结佣
          </el-tag>
          <el-tag type="danger" v-if="scope.row.sale_status === 0 && scope.row.status === 5">未结佣</el-tag>
          <el-tag size="mini" type="primary" v-if="scope.row.is_case === 1">案场方</el-tag>
          <p v-if="scope.row.u_company_name">{{ scope.row.u_company_name }}</p>
        </template>
      </el-table-column>
      <el-table-column width="auto" prop="u_phone" label="经纪人电话" show-overflow-tooltip>
      </el-table-column>
      <el-table-column width="auto" prop="created_at" label="创建时间" show-overflow-tooltip>
      </el-table-column>
      <el-table-column width="auto" prop="updated_at" label="操作时间" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="operating" label="操作" fixed="right" v-if="isDeal" width="200">
        <template slot-scope="scope">
          <el-dropdown>
            <span icon="el-icon-success" class="el-dropdown-link el-button--lightblue dropbutton"
              style="border-radius: 10px">已成交
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="scope.row.sale_status === 0"
                @click.native="dealEdit(scope.row)">成交编辑</el-dropdown-item>
              <el-dropdown-item @click.native="dealBill(scope.row)">成交账单</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="success" size="mini" @click="datadetail(scope.row)">报备详情</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="operating" label="操作" width="200" fixed="right" v-else>
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 5 && scope.row.sale_status === 1" type="success" size="mini"
            style="border-radius:5px" @click="$router.push(`/deal_orde?id=${scope.row.id}`)">成交账单</el-button>
          <el-button v-if="$hasShow('状态更新')" type="primary" size="mini" style="border-radius:5px"
            @click="editData(scope.row)">状态更新</el-button>
          <el-button v-if="$hasShow('报备详情')" type="success" size="mini" style="border-radius:5px"
            @click="datadetail(scope.row)">报备详情</el-button>
          <el-button v-if="
                        !/^[1][3,4,5,7,8,9][0-9]{9}$/.test(scope.row.customer_phone) &&
                          $hasShow('补全号码')
                      " type="success" size="mini" style="border-radius:5px"
            @click="fillCustomerPhone(scope.row)">补全号码</el-button>
          <!-- <el-button
            v-if="!scope.row.external_customer_id"
            type="primary"
            size="mini"
            style="border-radius:5px"
            @click="bindWxwork(scope.row)"
            >企微客户</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pagination-box">
      <myPagination :total="params.total" :pagesize="params.per_page" :currentPage="params.page"
        @handleCurrentChange="handleCurrentChange" @handleSizeChange="handleSizeChange"></myPagination>
    </div>
    <el-dialog title="状态更新" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false" @close="handleClose">
      <div>
        <el-form label-width="100px" :model="form_audit">
          <el-form-item label="客户名称：">
            <div>{{ customer_name }}</div>
          </el-form-item>
          <el-form-item label="审核状态：">
            <el-select @change="changeStatus" v-model="form_audit.status" :placeholder="status_name">
              <el-option v-for="item in report_list" :key="item.id" :disabled="item.value < status_id"
                :label="item.description" :value="+item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="无效原因：" v-if="cause_show">
            <el-input placeholder="请输入无效原因" v-model="invalid_reason"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="handleClose">取 消</el-button>
          <el-button type="success" @click="postData">确 定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="状态更新" :visible.sync="customDeal" width="60%" :close-on-click-modal="false"
      @close="handleCloseAlready">
      <div>
        <el-form label-width="200px" ref="form" :rules="formRules" :model="form_customer_deal">
          <el-form-item label="客户名称">
            <el-input :disabled="true" v-model="customer_name"></el-input></el-form-item>
          <el-form-item label="合同编号" prop="contract_no">
            <el-input v-model="form_customer_deal.contract_no"></el-input>
          </el-form-item>
          <el-form-item label="产权地址" prop="property_right_address">
            <el-input v-model="form_customer_deal.property_right_address"></el-input>
          </el-form-item>
          <el-form-item label="成交金额" prop="deal_amount">
            <el-input placeholder="" v-model="form_customer_deal.deal_amount">
              <template slot="append">元</template></el-input>
          </el-form-item>
          <el-form-item label="会员服务费">
            <el-input v-model="form_customer_deal.member_service_charge">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="已收会员服务费">
            <el-input v-model="form_customer_deal.earning_member_service_charge"><template
                slot="append">元</template></el-input>
          </el-form-item>
          <el-form-item label="会员服务费账期天数">
            <el-input v-model="form_customer_deal.customer_payment_days"><template slot="append">天</template></el-input>
          </el-form-item>
          <el-form-item label="开发商佣金账期天数">
            <el-input v-model="form_customer_deal.project_payment_days"><template slot="append">天</template></el-input>
          </el-form-item>
          <!-- <el-form-item label="成交时间" prop="deal_at">
							<el-input v-model="form_customer_deal.deal_at"></el-input>
						</el-form-item> -->
          <el-form-item label="分佣金额" prop="brokerage_amount">
            <el-input placeholder="0.00" v-model="form_customer_deal.brokerage_amount"><template
                slot="append">元</template></el-input>
          </el-form-item>
          <el-form-item label="扫码案场" prop="scan_code_user_id">
            <el-select filterable remote reserve-keyword v-model="form_customer_deal.scan_code_user_id"
              placeholder="请输入手机号" :loading="scan_code_user_loading" :remote-method="getScanData">
              <el-option v-for="item in client_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="跟进案场" prop="follow_up_user_id">
            <el-select filterable remote reserve-keyword v-model="form_customer_deal.follow_up_user_id"
              placeholder="请输入手机号" :loading="follow_up_user_loading" :remote-method="getScanData">
              <el-option v-for="item in client_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="成交时间">
            <el-date-picker v-model="form_customer_deal.deal_at" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form_customer_deal.remark"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="formCustomer">确认</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog title="报备详情" :visible.sync="dialogVisibleReport" width="60%" custom-class="dia-detail"
      @close="reportClose">
      <div class="nav div row">
        <div class="nav-item" v-for="(nav, index) in click_navs" :key="nav.type"
          :class="{ active: nav.type == data_type }" @click="onClickNav(index)">
          {{ nav.name }}
        </div>
        <!-- <reportTxt :type="1"></reportTxt> -->
      </div>
      <div class="detail" v-if="data_type == 1">
        <div class="box div row">
          <div class="label">报备时间：</div>
          <div class="label-ctn">
            {{ detail_customer.created_at || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">更新时间：</div>
          <div class="label-ctn">
            {{ detail_customer.updated_at || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">到访时间：</div>
          <div class="label-ctn">
            {{ detail_customer.visit_time || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">报备编号：</div>
          <div class="label-ctn">
            {{ detail_customer.reported_sn || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">来访人数：</div>
          <div class="label-ctn">
            {{ detail_customer.visit_people || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">来访方式：</div>
          <div class="label-ctn">
            {{ detail_customer.visit_category || "无" }}
          </div>
        </div>
        <div class="box div row">
          <div class="label">报备备注：</div>
          <div class="label-ctn">{{ detail_customer.remark }}</div>
        </div>
        <div class="box div row">
          <div class="label">当前状态：</div>
          <reportTxt :type="detail_customer.status" style="margin-right: 20px; display: table-cell">
          </reportTxt>
          <div class="row div" style="margin-left: 20px">
            <span v-if="detail_customer.pu_name">已被项目助理
              <span style="color: #409eff">【{{ detail_customer.pu_name }}】</span>
              领取</span>
            <span v-if="detail_customer.pu_name && detail_customer.dispatch_user" style="
                background: #409eff;
                width: 4px;
                height: 20px;
                display: inline-block;
                margin: 0 20px;
              "></span>
            <span style="display: table-cell" v-if="detail_customer.dispatch_user">已被分配给案场置业顾问
              <span style="color: #409eff">【{{ detail_customer.dispatch_user }}】</span>
            </span>
          </div>
        </div>
      </div>
      <div class="detail_step" v-if="data_type == 2">
        <div class="list div" v-loading="is_step_loading">
          <div class="reason" v-if="report_step.length > 0 && report_step[0].cancel_reason">
            【无效原因】{{ report_step[0].cancel_reason }}
          </div>
          <div class="box" v-for="item in report_step" :key="item.id">
            <div class="left"></div>
            <div class="right div">
              <div class="time">{{ item.created_at }}</div>
              <div class="content">{{ item.description }}</div>
              <div class="img-box div row" v-if="JSON.parse(item.data).length > 0">
                <el-image v-for="(img, index) in JSON.parse(item.data)" :key="index" :src="img" @click="clickImg(img)"
                  style="
                    width: 100px;
                    height: 100px;
                    margin: 5px;
                    cursor: pointer;
                  "></el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="img-big-box" v-if="big_img" style="cursor: pointer">
        <div class="close" @click="big_img = ''">关闭</div>
        <el-image class="img" :src="big_img" fit="fill"></el-image>
      </div>
      <div v-if="data_type == 3" style="cursor: pointer">
        <div class="img-detail">
          <el-image v-for="item in customer_img" :key="item.id" style="width: 100px; height: 100px; margin: 5px"
            @click="clickImg(item.file)" :src="item.file" fit="fill"></el-image>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="补全手机号" :visible.sync="dialogFillPhone" @close="form_fill_phone.part_number = ''">
      <el-form label-width="100px" label-position="left" :model="form_fill_phone" inline>
        <el-form-item label="补齐隐号四位">
          <el-input v-model="form_fill_phone.part_number" placeholder="请输入****内容"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button style="margin: 0" type="primary" @click="createFillPhone">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog :visible.sync="is_show_wxwork" title="绑定企业微信客户">
      <el-table :header-cell-style="{ background: '#EBF0F7' }" :data="wxwork_data" highlight-current-row border>
        <el-table-column label="企业名称" prop="corp_name"></el-table-column>
        <el-table-column label="客户名称" prop="name"></el-table-column>
        <el-table-column label="操作" v-slot="{ row }">
          <el-link @click="onClickRow(row)" type="primary">绑定</el-link>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="wxwork_params.total" :page-size="wxwork_params.per_page" :current-page="wxwork_params.page"
        @current-change="onPageChange">
      </el-pagination>
    </el-dialog>
  </el-container>
</template>

<script>
import reportTxt from "@/components/components/report-txt";
import myPagination from "@/components/components/my_pagination";
import config from "@/utils/config.js";
export default {
  name: "report_customer",
  components: {
    reportTxt,
    myPagination,
  },
  data() {
    return {
      // 无效原因
      invalid_reason: "",
      // <搜索栏
      // 表单数据
      tableData: [],
      is_status: "all",
      // 分页
      params: {
        page: 1,
        customer_phone: "",
        per_page: 10,
        total: 0,
        row: 0,
        reported_status: "",
        reception_status: "",
        project_id: "",
        updated_date_type: "day",
        company_id: "",
        build_id: "",
        status: "all",
      },
      report_list: [],
      reception_list: [],
      report_status: "",
      reception_status: "",
      form_audit: {
        customer_reported_id: "",
        status: "",
      },
      dialogVisible: false,
      dialogVisibleReport: false,
      audit_list: [],
      customer_name: "",
      input: "",
      // dropdown_search: [{ value: "customer_phone", name: "联系方式" }],
      customer_status: [],
      click_navs: [
        { type: 1, name: "报备详情" },
        { type: 2, name: "跟进列表" },
        { type: 3, name: "资料照片" },
      ],
      data_type: 1,
      detail_customer: {},
      customer_img: [],
      report_step: [],
      visit_category_list: [],
      status_name: "",
      status_id: "",
      // 成交状态
      isDeal: false,
      subComplete: false,
      // 客户报备列表如果将状态改为已成交显示内容
      customDeal: false,
      form_customer_deal: {
        customer_reported_id: "",
        contract_no: "",
        property_right_address: "",
        deal_amount: 0,
        deal_at: "",
        brokerage_amount: 0,
        remark: "",
        scan_code_user_id: "",
        follow_up_user_id: "",
        member_service_charge: 0, //会员服务费
        earning_member_service_charge: 0, //已收会员服务费
        customer_payment_days: 0, //会员服务费账期天数
        project_payment_days: 0, //开发商佣金账期天数
      },
      scan_code_user_loading: false,
      follow_up_user_loading: false,
      client_list: [],
      formRules: {
        property_right_address: [
          { required: true, message: "请输入产权地址", trigger: "blur" },
        ],
        contract_no: [
          { required: true, message: "请输入合同编号", trigger: "blur" },
        ],
        deal_amount: [
          { required: true, message: "请输入成交金额", trigger: "blur" },
        ],
        deal_at: [
          { required: true, message: "请输入成交时间", trigger: "blur" },
        ],
        brokerage_amount: [
          { required: true, message: "请输入分佣金额", trigger: "blur" },
        ],
        scan_code_user_id: [
          { required: true, message: "扫码案场不能为空", trigger: "blur" },
        ],
        follow_up_user_id: [
          { required: true, message: "跟进案场不能为空", trigger: "blur" },
        ],
      },
      time_array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
        { desc: "自定义", value: "customize", id: 8 },
      ],
      time_data_type: "day",
      report_info: {},
      big_img: "",
      dialogFillPhone: false,
      form_fill_phone: {
        id: "",
        part_number: "",
      },
      loading_company: false,
      company_list: [],
      time_value: "",
      isCustomize: false,
      loading_build: false,
      build_list: [],
      is_table_loading: true,
      is_step_loading: true,
      // 绑定企微客户
      wxwork_form: {
        external_id: "",
        customer_id: "",
      },
      is_show_wxwork: false,
      wxwork_params: {
        page: 1,
        per_page: 10,
        customer: 1,
      },
      wxwork_data: [],
      //失效原因开关
      cause_show: false,
    };
  },
  mounted() {
    this.getList();
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "REPORTED_STATUS":
            this.report_list = item.childs;
            this.getReportInfo();
            break;
          case "CUSTOMER_RECEPTION_STATUS":
            this.reception_list = item.childs;
            break;
          case "REPORTED_AUDIT_STATUS_TYPE":
            this.audit_list = item.childs;
            break;
          case "REPORTED_VISIT_CATEGORY":
            this.visit_category_list = item.childs;
            break;
        }
      });
    });
    this.params.project_id = this.$route.query.project_id;
  },
  watch: {},
  computed: {
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  filters: {
    fliterTabName(item) {
      if (item.total == undefined) {
        // 修改数量加载时显示undefined问题
        return item.description;
      } else {
        return item.description + "（" + item.total + "）";
      }
    },
  },
  methods: {
    bindWxwork(row) {
      this.wxwork_form.customer_id = row.customer_id;
      this.getWxworkData();
      this.is_show_wxwork = true;
    },
    onClickRow(row) {
      this.wxwork_form.external_id = row.id;
      this.$confirm("是否将此客户绑定为企业微信客户?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.setReportBindWxwork(this.wxwork_form).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "操作成功",
              });
              this.is_show_wxwork = false;
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    onPageChange(e) {
      this.wxwork_params.page = e;
      this.getWxworkData();
    },
    getWxworkData() {
      this.$http.getCustomerList({ params: this.wxwork_params }).then((res) => {
        if (res.status === 200) {
          this.wxwork_data = res.data.data;
          this.wxwork_params.total = res.data.total;
        }
      });
    },
    handleClick(e) {
      this.params.status = e.name;
      this.params.page = 1;
      if (e.name == 5) {
        this.isDeal = true;
      } else {
        this.isDeal = false;
      }
      this.getList();
    },
    // 获取模拟数据
    getList() {
      if (
        this.params.reception_status === "" ||
        this.params.reported_status === ""
      ) {
        delete this.params.reported_status;
        delete this.params.reception_status;
      }
      if (!this.params.company_id) {
        delete this.params.company_id;
      }
      if (!this.params.build_id) {
        delete this.params.build_id;
      }
      if (!this.params.updated_date_type) {
        delete this.params.updated_date_type;
      }
      if (this.params.status == "all") {
        delete this.params.status;
      }
      this.subComplete = false;
      this.is_table_loading = true;
      this.$http
        .companyGetReportList({ params: this.params }, this.time_value)
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.row;
            let statics = res.data.statistics;
            this.report_list.map((item) => {
              item.total = 0;
              statics.map((report) => {
                if (item.value == report.status) {
                  item.total = report.total || 0;
                }
              });
            });
          }
        });
    },
    changeDate() {
      this.params.updated_date_type = "";
      this.getList();
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getList();
    },
    editData(row) {
      console.log(row.status);
      if (row.status == 10) {
        this.cause_show = true;
      } else {
        this.cause_show = false;
      }
      this.invalid_reason = "";
      this.form_audit.customer_reported_id = row.id;
      this.form_customer_deal.customer_reported_id = row.id;
      this.customer_name = row.customer_name;
      this.status_id = row.status;
      this.form_audit.status = row.status;
      // this.report_list.map((item) => {
      // 	if (this.status_id == parseInt(item.value)) {
      // 		this.status_name = item.description;
      // 	}
      // });
      this.dialogVisible = true;
    },
    // 报备详情进度
    datadetail(row) {
      this.getList();
      this.detail_customer = row;
      this.visit_category_list.map((item) => {
        if ((this.detail_customer.visit_category = parseInt(item.value))) {
          this.detail_customer.visit_category = item.description;
        }
      });
      this.form_audit.customer_reported_id = row.id;
      this.dialogVisibleReport = true;
      for (var i in this.report_list) {
        if (parseInt(this.report_list[i].value) === row.reported_status) {
          this.report_status = this.report_list[i].description;
        }
      }
      for (var j in this.reception_list) {
        if (parseInt(this.reception_list[j].value) === row.reception_status) {
          this.reception_status = this.reception_list[j].description;
        }
      }
    },
    changeStatus(e) {
      if (e === 10) {
        this.cause_show = true;
      } else {
        this.cause_show = false;
      }
      if (e == 5) {
        this.dialogVisible = false;
        this.customDeal = true;
      } else {
        this.customDeal = false;
      }
    },
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
      this.cause_show = false;
      // this.customDeal = false;
      // for (var key in this.form_audit) {
      // 	this.form_audit[key] = "";
      // }
    },
    handleCloseAlready() {
      this.customDeal = false;
      if (this.subComplete) {
        this.dialogVisible = false;
      } else {
        this.dialogVisible = true;
      }
    },
    postData() {
      if (!this.form_audit.status) {
        this.$message({
          message: "提交失败",
          type: "error",
        });
        return;
      }
      if (
        this.status_id == this.form_audit.status &&
        this.form_audit.status != 5
      ) {
        this.$message({
          message: "报备状态未更新请重新选择",
          type: "error",
        });
        return;
      }
      this.$http.companyUpdataReport(this.form_audit).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });

          this.customDeal = false;
          this.dialogVisible = false;
          this.getList();
        }
      });
    },
    // 搜索下拉
    handleCommand(command) {
      for (let key in this.params) {
        if (key == "customer_phone") {
          this.params[command] = this.params[key]; //在对象中添加了myId键名,并把当前key值赋值给了myId
          delete this.params[key]; //删除当前键名为id的属性
        }
      }
    },
    onInput(e) {
      if (!e) {
        this.params.current_page = 1;
        this.params.customer_phone = "";
        this.getList();
      }
    },
    onChange() {
      this.search();
    },
    search() {
      this.params.customer_phone = this.input;
      this.params.page = 1;
      this.getList();
    },
    // 报备列表 =》报备详情
    onClickNav(index) {
      this.data_type = this.click_navs[index].type;
      if (this.data_type == 2) {
        this.getReportStep();
      }
      if (this.data_type == 3) {
        this.getCustomerImg();
      }
    },
    // 获取资料图片
    getCustomerImg() {
      this.$http
        .companyGetCustomerImg(
          this.detail_customer.project_id,
          this.detail_customer.id
        )
        .then((res) => {
          if (res.status === 200) {
            if (res.data) {
              this.customer_img = res.data;
            }
          }
        });
    },
    // 获取报备进度
    getReportStep() {
      this.is_step_loading = true;
      this.report_step = [];
      this.$http
        .companyGetReportStep(
          this.detail_customer.project_id,
          this.detail_customer.id
        )
        .then((res) => {
          this.is_step_loading = false;
          if (res.status === 200) {
            this.report_step = res.data.data;
          }
        });
    },
    // 报备详情弹出框关闭清除
    reportClose() {
      this.customer_img = "";
      this.data_type = 1;
    },
    // 报备成交提交合同编号
    formCustomer() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$http.createSale(this.form_customer_deal).then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "上传成功",
                type: "success",
              });
              this.subComplete = true;
              this.handleCloseAlready();
              for (let prop in this.form_customer_deal) {
                this.form_customer_deal[prop] = "";
              }
              setTimeout(() => {
                this.getList();
              }, 500);
            }
          });
        }
      });
    },
    dealEdit(row) {
      this.$goPath(`/deal_edit?id=${row.id}`);
    },
    dealBill(row) {
      this.$goPath(`/deal_orde?id=${row.id}`);
    },
    // 扫码案场搜索
    getScanData(query) {
      this.scan_code_user_loading = true;
      this.$http.searchUserByPhone(query).then((res) => {
        if (res.status === 200) {
          this.scan_code_user_loading = false;
          this.client_list = res.data.data.map((item) => {
            return {
              id: item.id,
              name: item.name || item.nickname || item.user_name,
            };
          });
        }
      });
    },
    // 点击选择日期数据
    onClickBrowse(index, id) {
      this.time_data_type = this.time_array[index].value;
      if (id !== 8) {
        this.params.updated_date_type = this.time_data_type;
        this.time_value = "";
        this.params.page = 1;
        this.getList();
        this.getReportInfo();
      } else {
        this.isCustomize = true;
      }
    },
    getReportInfo() {
      let params = {
        date_str: this.time_data_type,
        project_id: this.$route.query.project_id,
      };
      this.$http.companyGetReportData({ params: params }).then((res) => {
        if (res.status === 200) {
          this.report_info = res.data;
        }
      });
    },
    clickImg(img) {
      this.big_img = img;
    },
    // 补全客户号码
    fillCustomerPhone(row) {
      this.dialogFillPhone = true;
      this.form_fill_phone.id = row.id;
    },
    createFillPhone() {
      this.$http.fillCustomerPhone(this.form_fill_phone).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "已补全手机号",
            type: "success",
          });
          this.getList();
          this.dialogFillPhone = false;
        }
      });
    },
    // 公司搜索
    remoteMethod(query) {
      this.loading_company = true;
      this.$http.searchCompany(query).then((res) => {
        this.loading_company = false;
        if (res.status === 200) {
          this.company_list = res.data.data;
        }
      });
    },
    // 选择公司
    changeCompany() {
      this.getList();
    },
    remoteMethodBuild(query) {
      this.loading_build = true;
      this.$http.getcomapnyBuildListByname(query).then((res) => {
        this.loading_build = false;
        if (res.status === 200) {
          this.build_list = res.data.data;
        }
      });
    },
    changeBuild() {
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.reason {
  margin-left: 50px;
}

.el-date-editor {
  margin: 12px 20px;
}

.dialog-footer {
  margin-left: 100px;
}

.img-big-box {
  box-shadow: 2px 2px 3px #aaaaaa;
  width: 350px;
  height: 350px;
  background: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  z-index: 10;

  .close {
    box-shadow: 2px 2px 3px #aaaaaa;
    z-index: 11;
    right: -20px;
    top: -20px;
    width: 40px;
    height: 40px;
    background: #eee;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    position: absolute;
  }

  .close:hover {
    color: #fff;
    box-shadow: 0 1px 3px rgba(209, 40, 42, 0.5);
    background: #d1282a;
  }

  .img {
    width: 350px;
    height: 350px;
  }
}

// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;

  .browse {
    justify-content: flex-start;

    .browse-item {
      margin: 0 5px;
      font-size: 14px;
      padding: 2px 10px;
      border-radius: 50px;
      color: #333;

      &.browse_active {
        color: #fff;
        background: #0068e6;
      }
    }
  }

  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col {
    border-radius: 4px;
  }

  .bg-purple {
    border: 1px dashed #d3dce6;
  }

  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;

    .left {
      color: #999;
      font-size: 14px;
      text-align: start;

      p {
        color: #333;
      }

      .desc {
        color: #999;
      }
    }

    .right {
      color: #26bf8c;
    }
  }
}

.select-box {
  margin: 30px;
  margin-top: 0;

  .el-button {
    z-index: 1;
    width: 100px;
    margin: 0 15px;
  }

  .block {
    height: 22px;
  }
}

.table-list {
  height: 580px;

  .el-table {
    height: 100%;
  }

  .top-select {
    .el-tabs--border-card {
      border: none;

      .el-tabs__nav-scroll {
        border: 1px solid #d1dbe5;
        border-bottom: none;
      }
    }
  }
}

.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}

.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}

.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}

.el-input {
  border-left: none;
  border-radius: 0;
  // width: 200px;
}

.el-input,
.el-select,
.el-textarea {
  width: 200px;
}

.row {
  align-items: center;
  text-align: center;
  color: #999;

  .add-build {
    margin-left: 10px;

    .el-button {
      border-radius: 4px;
    }
  }
}

.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}

.scope-box {
  height: 40px;

  img {
    width: 100%;
  }
}

.dropbutton {
  cursor: pointer;
  width: 54px;
  height: 26px;
  font-size: 12px;
  margin: 5px;
  padding: 5px 15px;
  background: #409eff;
  color: #fff;
}

/deep/ .dia-detail {
  height: auto;
  overflow: hidden;
}

.img-detail {
  height: 500px;
  overflow: hidden;
  overflow-y: scroll;
}

.nav {
  .nav-item {
    cursor: pointer;
    font-size: 14px;
    align-items: center;
    margin-right: 80px;
    padding-bottom: 10px;
    border-bottom: 4px solid #fff;

    &.active {
      color: #0068e6;
      border-bottom: 4px solid #0068e6;
    }
  }
}

.detail {
  margin-top: 20px;

  .box {
    margin-top: 20px;
    display: table;

    .label {
      display: table-cell;
    }

    .label-ctn {
      text-align: left;
      width: 600px;
      margin-left: 10px;
    }
  }
}

.list {
  // margin-top: 40px;
  flex-direction: column;
  line-height: 50px;
  margin-bottom: 100px;
  overflow: hidden;
  height: 500px;
  overflow-y: scroll;

  .box {
    margin-left: 50px;
    position: relative;
    padding-left: 40px;
    border-left: 2px dashed #999;
  }

  .left {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 8px solid #999;
    left: -20px;
    background: #fff;
  }

  .right {
    margin-bottom: 40px;
    flex-direction: column;

    .time {
      font-size: 16px;
    }

    .content {
      color: #999;
      font-size: 13px;
    }

    .img-box {
      justify-content: flex-start;
      flex-wrap: wrap;

      &::after {
        content: "";
        width: auto;
      }
    }
  }
}
</style>
