<template>
    <div class="container">
        <el-tabs v-model="smsData_params.type" type="card" @tab-click="handleClick">
            <!-- 通用提醒 -->
            <el-tab-pane label="通用提醒" name="1">
                <div class="sms-sign" v-if="signature">当前短信签名：{{ signature }}</div>
                <div class="sms-model">
                    <span style="font-size: 18px;">你好，我是</span>
                    <div class="sms-name">
                        <el-input v-model="smsData_params.name" maxlength="10" placeholder="请输入名称"></el-input>
                    </div>
                    <span style="font-size: 18px;">您预约的信息已经收到，在您方便的时候可以随时联系我</span>
                    <div class="sms-phone">
                        <el-input v-model="smsData_params.mobile" maxlength="11" placeholder="请输入手机号"></el-input>
                    </div>
                    <span>。</span>
                </div>
                <div>
                    <i style="color: #00CEFF;" class="el-icon-info"></i>
                    每天只能发送1条
                </div>
            </el-tab-pane>
            <!-- 未接提醒 -->
            <el-tab-pane label="未接提醒" name="2">
                <div class="sms-sign">当前短信签名：{{ signature }}</div>
                <div class="sms-model">
                    <span style="font-size: 18px;">我是</span>
                    <div class="sms-name">
                        <el-input v-model="smsData_params.name" maxlength="10" placeholder="请输入名称"></el-input>
                    </div>
                    <span style="font-size: 18px;">您预约的信息已经收到，在您方便的时候可以随时联系我</span>
                    <div class="sms-phone">
                        <el-input v-model="smsData_params.mobile" maxlength="11" placeholder="请输入手机号"></el-input>
                    </div>
                    <span class="sms-other">为你发送资料并分析解答。</span>
                </div>
                <div>
                    <i style="color: #00CEFF;" class="el-icon-info"></i>
                    每天只能发送1条
                </div>
            </el-tab-pane>
        </el-tabs>
        <div class="select" @click="showAddTemplate" :loading="is_loading">保存当前模板</div>
    </div>
</template>
<script>
export default {
    props: {
        // 自己当前的信息
        AdminData: {
            type: Object,
            default: () => {}
        },
    },
    data() {
        return {
            signature: "", // 当前短信签名
            // 当前短信提交参数
            smsData_params: {
                type: "1", // tabs绑定值
                name: "", // 姓名
                mobile: "", // 手机号
                client_id: "", // 客户id
            },
            general: {}, // 通用提醒模板容器
            special: {}, // 未接提醒模板容器
            // 添加/编辑短信模板参数
            template_params: {
                id: "", // 不传ID为添加，传ID为编辑
                type: "", // 模板类型(1:通用模板 2:未接模板)
                value_1: "", // 自定义值1
                value_2: "", // 自定义值2
            },
            website_ids: "",
            templateList: [], // 短信模板列表
            is_loading: false, // loading加载
        }
    },
    created() {
        this.smsData_params.client_id = this.$route.query.id; // 赋值短信提醒客户id
        this.getSign(); // 获取短信签名
        this.getSmsTemplate(); // 获取短信模板列表
    },
    methods: {
        // 获取短信签名
        getSign() {
            this.$http.getSign().then((res) => {
                if(res.status == 200) {
                    this.signature = res.data.value;
                }
            })
        },
        // 保存当前模板
        showAddTemplate() {
            // 判断当前是通用提醒还是未接提醒
            if(this.smsData_params.type == 1) {
                if(this.general && this.general.id) {
                    this.template_params.id = this.general.id;
                }
                this.template_params.type = this.smsData_params.type;
                this.template_params.value_1 = this.smsData_params.name;
                this.template_params.value_2 = this.smsData_params.mobile;
            } else if(this.smsData_params.type == 2) {
                if(this.special && this.special.id) {
                    this.template_params.id = this.special.id;
                }
                this.template_params.type = this.smsData_params.type;
                this.template_params.value_1 = this.smsData_params.name;
                this.template_params.value_2 = this.smsData_params.mobile;
            }
            // 不传ID为添加，传ID为编辑
            if(this.template_params.id == "") {
                delete this.template_params.id;
            }
            this.is_loading = true;
            this.$http.saveSmsTemplate(this.template_params).then((res) => {
                if(res.status == 200) {
                    this.is_loading = false; //关闭loading
                    this.$message.success("保存成功");
                    this.getSmsTemplate();
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 获取短信模板列表
        getSmsTemplate() {
            this.$http.getSmsTemplate().then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"模板");
                    this.templateList = res.data;
                    if(this.templateList && this.templateList.length) {
                        this.templateList.map((item, index) => {
                            if(this.smsData_params.type == 2) {
                                if(item.type == 1) {
                                    this.general = this.templateList[index];
                                    this.general.type = this.general.type.toString();
                                } else if(item.type == 2) {
                                    this.special = this.templateList[index];
                                    this.special.type = this.special.type.toString();
                                    this.smsData_params.type = this.special.type; // 赋值模板类型
                                    this.smsData_params.name = this.special.value_1; // 自定义值1
                                    this.smsData_params.mobile = this.special.value_2; // 自定义值2
                                }
                            } else if(item.type == 1) {
                                this.general = this.templateList[index];
                                this.general.type = this.general.type.toString();
                                this.smsData_params.type = this.general.type; // 赋值模板类型
                                this.smsData_params.name = this.general.value_1; // 自定义值1
                                this.smsData_params.mobile = this.general.value_2; // 自定义值2
                            } else if(item.type == 2) {
                                this.special = this.templateList[index];
                                this.special.type = this.special.type.toString();
                            }
                        })
                    }
                } else {
                    this.smsData_params.name = this.AdminData.user_name; // 赋值短信提醒姓名
                    this.smsData_params.mobile = this.AdminData.phone; // 赋值短信提醒手机号
                }
            })
        },
        handleClick(e) {
            if(e.name == 1 && Object.keys(this.general).length) {
                this.smsData_params.name = this.general.value_1; // 自定义值1
                this.smsData_params.mobile = this.general.value_2; // 自定义值2
            } else if(e.name == 2 && Object.keys(this.special).length) {
                this.smsData_params.name = this.special.value_1; // 自定义值1
                this.smsData_params.mobile = this.special.value_2; // 自定义值2
            } else {
                this.smsData_params.name = this.AdminData.user_name; // 赋值短信提醒姓名
                this.smsData_params.mobile = this.AdminData.phone; // 赋值短信提醒手机号
            }
        }
    }
}
</script>
<style lang="scss" scoped>
    .container {
        position: relative;
        .select {
            position: absolute;
            right: 0px;
            top: 4px;
            padding: 8px 5px;
            border-radius: 3px;
            border: 1px solid #d3d4d6;
            cursor: pointer;
        }
        .select:hover {
            color: #909399;
            background: #f4f4f5;
            border-color: #d3d4d6;
        }
    }
    .sms-model {
        line-height: 60px;
        .sms-name, .sms-phone {
            display: inline-block;
            ::v-deep .el-input {
                font-size: 18px;
                .el-input__inner {
                    height: 24px;
                    border: none;
                    border-radius: 0px;
                    border-bottom: 1px solid #DCDFE6;    
                }
            }
        }
        .sms-other {
            font-size: 18px;
        }
    }
    .sms-sign {
        font-size: 18px;
    }
</style>