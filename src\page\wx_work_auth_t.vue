<template>
  <div></div>
</template>

<script>
export default {
  data() {
    return {
      params: {
        redirect_uri: "",
      },
    };
  },
  mounted() {
    // type=1工作台登录 ，type=2侧边栏登录
    var code = this.getUrlKey("code");
    var state = this.getUrlKey("state");
    let website_id = localStorage.getItem("website_id");
    sessionStorage.removeItem("is_other")  // 清除缓存
    if (code) {
      if (this.isMobile()) {
        let url =
          window.location.origin +
          `/fenxiao/admin/wx_work_auth?website_id=${website_id}&type=3&code=` +
          code;
          window.location.href = url;
      } else {
        this.setWxworkBycodeV2(code, state);
      }
    } else {
      this.getWxworkByCodeV2();
    }
  },
  methods: {
    // 获取路径中的参数（code等）
    getUrlKey(name) {
      return (
        decodeURIComponent(
          (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
            location.href
          ) || ["", ""])[1].replace(/\+/g, "%20")
        ) || null
      );
    },
    getWxworkByCodeV2() {
      this.params.redirect_uri = window.location.href;
      this.$http.getWxworkByCodeV2({ params: this.params }).then((res) => {
        if (res.status === 200) {
          window.location.href = res.data;
        }
      });
    },
    setWxworkBycodeV2(code, state) {
      // 第三方应用
      this.$http.setWxworkBycodeT(code).then(async (res) => {
        if (res.status === 200) {
          localStorage.setItem("website_id", res.data.website_id);
          localStorage.setItem("auth_way", res.data.auth_way || 0);
          localStorage.setItem("TOKEN", res.data.token);
          localStorage.setItem(
            "admin_token_" + res.data.website_id,
            res.data.token
          );
          // if (res.data.website_id == 176) {
          // let result = await this.$http.getInfoConfig()
          // if (result.status == 200) {
          //   console.log();
          // } else {
          //   this.$store.state.disableClick = result.data.message
          // }
          let url = `https://yun.tfcs.cn/admin/#/index?website_id=${res.data.website_id}`
          if (state.includes("house_detail") || state.includes("house_audit") || state.includes("crm_customer_audit") || state.includes("crm_information_detail")|| state.includes("crm_customer_information")) {
            sessionStorage.setItem("is_other", 1) //设置缓存防止被后边逻辑覆盖   
            url = `https://yun.tfcs.cn/admin/#/${decodeURIComponent(state)}`
          }
          window.location.href = url;
          // this.$http.getInfoConfig().then(res1 => {
          //   if (res1.status == 200) {
          //     let url = `https://yun.tfcs.cn/admin/#/index?website_id=${res.data.website_id}`
          //     if (state.includes("house_detail") || state.includes("house_audit") || state.includes("crm_customer_audit")) {
          //       sessionStorage.setItem("is_other", 1) //设置缓存防止被后边逻辑覆盖   
          //       url = `https://yun.tfcs.cn/admin/#/${decodeURIComponent(state)}`
          //     }
          //     window.location.href = url;
          //   } else {
          //     this.$store.state.disableClick = res1.data.message
          //   }
          // })
          // } else {
          //   let url = `https://yun.tfcs.cn/admin/#/index?website_id=${res.data.website_id}`
          //   if (state.includes("house_detail") || state.includes("house_audit") || state.includes("crm_customer_audit")) {
          //     sessionStorage.setItem("is_other", 1) //设置缓存防止被后边逻辑覆盖   
          //     url = `https://yun.tfcs.cn/admin/#/${decodeURIComponent(state)}`
          //   }
          //   window.location.href = url;
          // }
        }
      });
    },
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
  },
};
</script>

<style></style>
