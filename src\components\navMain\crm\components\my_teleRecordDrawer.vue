<template>
    <div
        class="follow-record outRecord"
        v-infinite-scroll="loadMorePhone"
        style="overflow: auto;height: 400px;"
        >
        <el-timeline
            v-if="PhoneRecord_list.length"
            style="margin-left: 10px"
        >
            <el-timeline-item
                v-for="(Phone, index) in PhoneRecord_list"
                :key="index"
                placement="top"
                color="#2D84FB"
            >
            <div class="FollowRecord">
                <div style="width: 100%">
                  <div class="agent_info flex-row align-center">
                      <div class="time">
                        {{ Phone.created_at }}
                      </div>
                      <div class="agent_name" v-if="Phone.admin&&Phone.admin.user_name&&Phone.admin.department_name">
                      {{ Phone.admin && Phone.admin.user_name }}/{{
                        Phone.admin && Phone.admin.department_name
                      }}
                    </div>
                    <div class="agent_name" v-else>
                      {{Phone.admin?Phone.admin.user_name?Phone.admin.user_name:Phone.admin.department_name:"" }}
                    </div>
                  </div>
                  <div v-if="Phone.analysis_desc" style="max-width: 507px;">
                  <div v-if="Phone.analysis_desc" class="flex-row">
                    <div class="colorAI">AI</div>智能总结
                  </div>
                  <div v-if="Phone.analysis_desc" class="AIstyle">
                    {{Phone.analysis_desc}}
                  </div>
                  <div
                      v-if="Phone.analysis_desc"
                      class="flex-row tipsAI">
                      <div
                       style="margin-top: 6px;" class="AIword">
                        以上内容由AI大模型智能生成
                      </div>
                      <div class="flex-row">
                        <el-link type="primary" :underline="false" @click="toai(Phone)">深度分析</el-link>
                        <div class="lanse" @click="toai(Phone)">></div>
                      </div>
                    </div>
                  </div>
                  <div class="FollowText">
                      <div
                          class="f_content"
                          :class="marginFilter(Phone) ? 'mb20' : ''"
                      >
                          <div slot="reference" class="flex-row">
                            <!-- 电话跟进内容 -->
                              <span v-if="Phone.content">
                                {{ Phone | stringSplice }}
                              </span>
                          </div>
                      </div>
                  </div>
                  <AudioPlayer
                      v-if="Phone.id && Phone.record_url != ''"
                      :activity="Phone"
                      select="CustomerFollow"
                      @toai="toai"
                  ></AudioPlayer>
                </div>
            </div>
            </el-timeline-item>
        </el-timeline>
        <myEmpty v-else :loading="loading"></myEmpty>
           <!-- AI分析 -->
    <alanalysis ref="alanalysis" :modalai="false" @getDataListA="getDataListA" :waihu="true"></alanalysis>
    </div>
</template>
<script>
import myEmpty from "@/components/components/my_empty.vue";
import AudioPlayer from "@/components/components/audioPlayer.vue"
import alanalysis from '@/components/components/AI/alanalysis.vue';
export default {
    components: {
        myEmpty,
        AudioPlayer,
        alanalysis
    },
    props: {
        PhoneRecord_list: {
            type: Array,
            default: () => {}
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            Phone_load: false,
        }
    },
    created() {
    },
    filters: {
      stringSplice(value) {
        // let str = value.content.split("通过");
        // return "通过外显号码" + "(" + value.call_show_phone + ")" + str[1].toString()
        if(value.call_show_phone){
          let str = value.content.split("通过");
          return "通过外显号码" + "(" + value.call_show_phone + ")" + str[1].toString()
        }else{
          return value.content
        }
      }
    },
    methods: {
        // 加载更多电话记录
        loadMorePhone() {
            this.$emit("morePhoneRecord", {}); // 获取更多电话记录
        },
        marginFilter(value) {
          if (value.content) { // 如果存在跟进内容
            if (value.record_url != "") {
              return true;
            } else {
              return false;
            }
          }
        },
        // 用于刷新跟进与外呼
        getDataListA() {
          this.$emit("getFollowData", {})
          this.$emit("changeShowAI", {})
        },
        //打开AI分析通话录音详情(侧边栏)
        toai(activity){
           this.$refs.alanalysis.open(activity)
        },
    }
}
</script>
<style lang="scss" scoped>
.follow-record {
  max-height: 400px;
  margin-bottom: 116px;
  // ==========
  .el-timeline {
    font-size: 15px;
    .el-timeline-item:hover {
      .el-timeline-item__wrapper {
        .el-timeline-item__content {
          .agent_info {
            .follow_info_box {
              display: flex;
            }
          }
        }
      }
    }
  }
  // ==========
  // position: relative;
  .agent_info {
    height: 25px;
    margin: -15px 0 24px 0;
    .time {
      margin-right: 10px;
      color: #8a929f;
    }
    .img {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .agent_name {
      font-size: 12px;
      color: #8a929f;
    }
    .show_is_top {
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
      color: #ffffff;
      padding: 2px 8px;
      border-radius: 4px;
      margin-left: 20px;
    }
    .follow_info_box {
      display: none;
      flex-direction: row;
      border: 1px solid #8a929f;
      border-radius: 3px;
      margin-left: 20px;
      .follow_info_praise,
      .follow_info_copy {
        width: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        text-align: center;
        padding: 6px;
        color: #8f959e;
        border-right: 1px solid #8f959e;
        cursor: pointer;
      }
      .follow_info_praise:active {
        background-color: #eff0f1;
      }
      .follow_info_copy:active {
        background-color: #eff0f1;
      }
      .follow_add_top {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #8f959e;
        padding: 6px;
        cursor: pointer;
      }
      .follow_add_top:active {
        background-color: #eff0f1;
      }
    }
  }
  .colorAI{
    width: 20px;
    height: 20px;
    border: 2px solid #f85d02;
    border-radius: 50%;
    color: #f85d02;
    text-align: center;
    line-height: 19px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  .AIstyle{
    // width: 90% !important;
    // max-width: 493px;
    background-color: #f4f4f5;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    line-height: 28px;

  }
  .AIword{
      color: #8a929f;
      font-size: 12px;
    }
    .tipsAI{
      justify-content: space-between;
      margin-bottom:15px;
    }
  .lanse{
    width: 12px;
    height: 12px;
    border: 1px solid #409eff;
    border-radius: 50%;
    color: #409eff;
    text-align: center;
    line-height: 12px;
    font-size: 13px;
    margin-left: 3px;
    margin-top: 7px;
    cursor: pointer;
  }
  .FollowText {
    display: flex;
    // width: 560px;
  }
  .follow-picture {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .follow-picture-box {
      width: 60px;
      height: 60px;
      margin-right: 10px;
      margin-bottom: 5px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .uploader-actions {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        border-radius: 4px;
        cursor: default;
        text-align: center;
        color: #fff;
        opacity: 0;
        font-size: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s;
        .uploader-actions-item {
          font-size: 20px;
          cursor: pointer;
          & i {
            color: #fff;
          }
        }
      }
      .uploader-actions:hover {
        opacity: 1;
      }
      .follow-delete-picture {
        display: none;
        position: absolute;
        top: -7px;
        right: -8px;
        cursor: pointer;
      }
    }
    .follow-picture-box:hover .follow-delete-picture {
      display: block;
    }
  }
  .follow-praise {
    display: inline-block;
    border-radius: 15px;
    color: #8a929f;
    background-color: #f1f4fa;
    padding: 5px 12px;
    box-sizing: border-box;
    margin-right: 20px;
    .follow-praise-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      .follow-praise-img {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 19px;
          height: 19px;
        }
      }
      .follow-praise-separate {
        width: 1px;
        height: 14px;
        background-color: #8a929f;
        margin: 0 5px;
        opacity: 0.5;
      }
      .follow-praise-text {
        padding: 0 5px;
      }
    }
  }
  .f_content {
    display: flex;
    align-items: center;
    max-width: 450px;
    // margin-bottom: 20px;
    .recode_Time {
      color: #409eff;
      margin-left: 5px;
    }
    &.red {
      color: #fc0606;
    }
    // & span {
    //   display: flex;
    //   align-items: center;
    // }
  }
  .mb20 {
    margin-bottom: 20px;
  }
  .infoFrame {
    display: none;
    width: 200px;
    position: fixed;
    top: 0px;
    left: 0px;
    background: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;
    border-radius: 7px;
    z-index: 2;
    .infoAngle {
      position: absolute;
      top: 74px;
      left: 95px;
      width: 0px;
      height: 0px;
      color: #fff;
    }
    .infoFrame-box {
      display: flex;
      padding: 10px;
      .infoFrame_icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #2d84fb;
        color: #fff;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 5px 12px 0;
      }
      .infoFrame-info {
        display: flex;
        flex-direction: column;
        color: #303133;
        & span {
          font-size: 12px;
          margin-top: 5px;
        }
        .infoFrame-phone {
          display: flex;
          align-items: center;
          .numberTop {
            background: #afb5b42e;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            margin-left: 5px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>