<template>
  <div >
    <!-- <el-tabs v-model="is_tabs_page">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
    </el-tabs> -->
    <div class="pages_content">
      <div class="check-box div row" id="pages_content">
      <div
        v-for="item in tabs"
        @click="onClickTabs(item)"
        :class="{ isactive: item.name === is_tabs_page }"
        :key="item.id"
        class="check-item"
      >
        {{ item.label }}
      </div>
    </div>
    <div :is="is_tabs_page" keep-alive  style="background: #fff; padding: 24px; min-height: 110vh"></div>
    </div>

  </div>
</template>

<script>
import Customer from "./cus_setting";
import Company from "./com_setting";
import LabelGroup from "./label_group";
import LabelList from "./label_list";
import Level from "./cus_level";
import Status from "./cus_status";
import From from "./cus_from";
import Type from "./cus_type";
import Seas from "./cus_seas";
import commonSetting from "./commonSetting";
// import TikTokA from "./cus_TikTok.vue";
// import TikTok from "./cus_TikTok_copy.vue";
// import clueAllot from "./cus_clueAllot.vue";
import clueAllot from "./cus_clueAllot_copy.vue";
import regulation from "./cus_regulation.vue";
import taskList from "./cus_taskList.vue";
import projectAdmin from "./cus_projectAdmin.vue"
import formnew from "./cus_from_copy.vue"
import Tassistant from "./Tassistant.vue";
import rolename from "./role_name.vue"
import channeloutreach from "./channel_outreach.vue"
export default {
  name: "crm_customer_setting",
  components: {
    Customer,
    Company,
    LabelGroup,
    LabelList,
    Level,
    Status,
    From,
    Type,
    Seas,
    commonSetting,
    // TikTok,
    clueAllot,
    regulation,
    projectAdmin,
    taskList,
    formnew,
    // TikTokA,
    // clueAllotA
    Tassistant,
    rolename,
    channeloutreach
  },
  data() {
    return {
      is_tabs_page: "commonSetting",
      tabs: [
        { id: 10, label: "基本配置", name: "commonSetting" },
        // { id: 1, label: "客户字段设置", name: "Customer" },
        // { id: 2, label: "公司字段设置", name: "Company" },
        { id: 3, label: "标签组", name: "LabelGroup" },
        // { id: 4, label: "标签列表", name: "LabelList" },
        { id: 5, label: "客户等级", name: "Level" },
        { id: 6, label: "客户状态", name: "Status" },
        { id: 13, label: "掉公规则", name: "regulation" },
        { id: 16, label: "角色名称", name: "rolename" },
        { id: 7, label: "客户来源", name: "formnew" },
        // { id: 8, label: "话术库", name: "Language" },
        { id: 9, label: "客户类型", name: "Type" },
        { id: 12, label: "线索分配", name: "clueAllot" },
        { id: 11, label: "线索推送", name: "TikTokA" },
        // { id: 16, label: "T+助手", name: "Tassistant" },
        { id: 15, label: "项目管理", name: "projectAdmin" },
        { id: 17, label: "渠道外联", name: "channeloutreach" },
        { id: 14, label: "操作记录", name: "taskList" },
        // { id: 14, label: "操作记录", name: "taskList" },
        // { id: 10, label: "公海规则", name: "Seas" },
      ],
      field_list: {},
      website_ids: "",
    };
  },
  created() {
    this.website_ids = this.$route.query.website_id;
    if(this.website_ids == 527) {
      // this.tabs[7] = { id: 12, label: "线索分配", name: "clueAllotA" }
      // this.tabs[9] = { id: 11, label: "线索推送", name: "TikTokA" }
      this.tabs.splice(9, 0,{ id: 16, label: "T+助手", name: "Tassistant" },);
    }
    // if(this.website_ids == 109||this.website_ids == 176){
    //   this.tabs.splice(11, 0,{ id: 17, label: "渠道外联", name: "channeloutreach" },);
    // }
  },  
  methods: {
    onClickTabs(e){
        if (e.name === "TikTokA") {
          this.$router.push("cluetransfer");
          return;
        }
        if (e.name === "regulation") {
          return  this.$router.push("newregulation");
        }
      this.is_tabs_page = e.name;
    }
  },
};
</script>

<style scoped lang="scss">
.pages {
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  margin-left: 20px;
  flex-wrap: wrap;
  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;
    white-space:nowrap;
    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}
.pages_content{
  max-height: calc(100vh - 209px); /* 限制最大高度为视口高度 */
  overflow-y: auto;  /* 添加垂直滚动条，按需显示 */
  margin-left: 56px;
  // margin-bottom: 100px;
}
</style>
