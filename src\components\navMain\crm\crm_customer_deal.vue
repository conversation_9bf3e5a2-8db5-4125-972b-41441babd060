<template>
  <div class="pages">
    <!-- 成交报告 -->
    <el-row :gutter="20">
      <el-col  :span="24" >
        <!-- <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <span class="text">是否分佣：</span>
            <myLabel
              :arr="finance_status_list"
              @onClick="onClickSource"
            ></myLabel>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">审批状态：</span>
            <myLabel :arr="is_status" @onClick="onClickAudit"></myLabel>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">分佣模式：</span>
            <myLabel
              :arr="commission_type_list"
              @onClick="onClickCommission"
            ></myLabel>
          </div>
        </div> -->
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <myCheck
                :type="is_status"
                class="status_check"
                :is_tabs="params.state"
                label="name"
                value="id"
                @onClick="onClickTabs"
              ></myCheck>
             
            </div>
            <div class="t-t-b-right div row" >
              <el-button
                style="margin-right:10px"
                type="primary"
                v-if="auth_transaction ||showAdd"
                
                class="el-icon-plus"
                @click="onCreateDataDeal"
                >添加</el-button
              >
              <el-cascader
              style="width:150px"
              szie="small"
              :options="memberLists"
              @change ="changeDepartment"
              placeholder="部门检索"
              :props="{
                value: 'id',
                label: 'name',
                children: 'subs',
                multiple: false,
              }"
              clearable
              v-model="params.department_id"
            ></el-cascader>
              <!-- <el-input
                
                placeholder="请输入搜索内容"
                style="width: 256px; margin-left: 12px"
                v-model="params.keywords"
                @change="onChangeKeywords"
              >
                <span slot="append" class="el-icon-search"></span>
              </el-input> -->
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
          >
            <el-table-column label="客户编号"  v-slot="{ row }">
              {{row.client.id?row.client.id:"--"}}
            </el-table-column>
            <el-table-column label="客户姓名" prop="client.name"> </el-table-column>
            <el-table-column label="手机号" prop="client.tel"></el-table-column>
            <el-table-column label="成交状态" v-slot="{ row }">
              <el-tag :type="row.state==1?'success':'warning'">{{row.state==1?'有效':'无效'}}</el-tag>
              <!-- {{ row.type == 1 ? "买方" : "卖方" }} -->
            </el-table-column>
            <el-table-column label="客户数量" width="80" prop="customer_count">
              
            </el-table-column>
            <el-table-column label="金额" prop="money" width="80">
            </el-table-column>
            
            <el-table-column label="成交备注" width="80" prop="remark">
              
            </el-table-column>
            <el-table-column label="发起人" v-slot="{ row }">
              
                {{ row.admin_name || "--" }}
              
            </el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }">
              <!-- <el-link type="primary" @click="onClickDialog(row)"
                >成交审批</el-link
              > -->
              <el-link v-if ="auth_transaction||row.edit_del" type="primary" @click="onEditData(row)">编辑</el-link>
              <el-link
                style="margin:0 10px"
                type="danger"
                v-if ="auth_transaction ||row.edit_del"
                @click="onDeleteData(row)"
                >删除</el-link
              >
               <el-link v-if="auth_transaction ||row.show_detal" type="warning" @click="showSee(row)"
                >查看</el-link
              >
              <el-link style="margin:0 10px" v-if="row.client.id!==0" type="info" @click="details(row.client.id)">详情</el-link>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
      <!-- <el-col v-if="!auth_transaction &&!showAdd &loadEnd" :span="24">
        <myEmpty desc="当前用户不可查看"></myEmpty>
      </el-col> -->
    </el-row>
    <el-dialog :visible.sync="is_dialog_show" title="成交审批">
      <div class="bottom-border div row" style="margin-top:20px">
        <span class="text"><span class="label">收入费用：</span></span>
        <el-input
          style="width: 240px"
          v-model="deal_form.money"
          placeholder="请填写"
        ></el-input>
        <span style="margin-left: 20px" class="text">元</span>
      </div>
      <div
        class="bottom-border div row"
        style="margin-top:20px;border-bottom:none"
      >
        <span class="text"><span class="label">销售分成：</span></span>
        <el-radio
          @change="onChangeRadio"
          v-model="deal_form.get_commission_type"
          :label="1"
          >独立成交</el-radio
        >
        <el-radio
          @change="onChangeRadio"
          v-model="deal_form.get_commission_type"
          :label="2"
          >合作分成</el-radio
        >
      </div>
      <el-form :model="deal_form">
        <div class="title-label div row tuandui">
          <span>
            团队项：
          </span>
          <el-input
            v-model="deal_form.group_radio"
            placeholder="比例"
            style="width: 160px; margin-left: 24px"
          >
            <template slot="append">%</template></el-input
          >
        </div>
        <div
          style="margin: 24px 0"
          v-for="(item, index) in deal_form.list"
          :key="index"
        >
          <el-input
            :disabled="deal_form.get_commission_type === 1"
            v-model="item.admin_name"
            placeholder="请输入"
            style="width: 160px; margin-right: 22px"
          ></el-input>

          <mySelect
            :disabled="deal_form.get_commission_type === 1"
            :optionSource="admin_list"
            v-model="item.admin_id"
            labelKey="user_name"
            valueKey="id"
            style="margin-right: 22px; display: inline"
            width="160px"
            @page-change="onPageChange"
            :paginationOption="{
              pageSize: admin_params.per_page, //每页显示条数
              currentPage: admin_params.page, //当前页
              pagerCount: 5, //按钮数，超过时会折叠
              total: admin_params.total, //总条数
            }"
          ></mySelect>
          <el-input
            v-model="item.radio_name"
            placeholder="请输入"
            :disabled="deal_form.get_commission_type === 1"
            style="width: 160px; margin-right: 22px"
          ></el-input>
          <el-input
            v-model="item.radio"
            placeholder="比例"
            style="width: 160px; margin-right: 22px"
          >
            <template slot="append">%</template></el-input
          >

          <template v-if="deal_form.get_commission_type === 2">
            <el-link
              style="margin-right: 24px"
              type="primary"
              @click.prevent="removeDomain(item)"
              >删除</el-link
            >
            <el-link
              v-if="deal_form.list.length === index + 1"
              type="primary"
              style="margin-right: 24px"
              @click.prevent="addDomain"
              >添加</el-link
            >
          </template>
          <el-link type="danger">{{ tuanduiradio(item.radio, 1) }}</el-link>
        </div>
      </el-form>
      <el-form :model="deal_form">
        <div class="title-label div row tuandui" style="margin-bottom:24px">
          <span>
            其他项：
          </span>
          <el-input
            v-model="deal_form.other_radio"
            placeholder="比例"
            style="width: 160px; margin-left: 24px"
          >
            <template slot="append">%</template></el-input
          >
        </div>
        <div
          style="margin: 24px 0"
          v-for="(item, index) in deal_form.list2"
          :key="index"
        >
          <el-input
            v-model="item.admin_name"
            placeholder="请输入"
            style="width: 160px; margin-right: 22px"
          ></el-input>

          <mySelect
            :optionSource="admin_list"
            v-model="item.admin_id"
            labelKey="user_name"
            valueKey="id"
            style="margin-right: 22px; display: inline"
            width="160px"
            @page-change="onPageChange"
            :paginationOption="{
              pageSize: admin_params.per_page, //每页显示条数
              currentPage: admin_params.page, //当前页
              pagerCount: 5, //按钮数，超过时会折叠
              total: admin_params.total, //总条数
            }"
          ></mySelect>
          <el-input
            v-model="item.radio_name"
            placeholder="请输入"
            style="width: 160px; margin-right: 22px"
          ></el-input>
          <el-input
            v-model="item.radio"
            placeholder="比例"
            style="width: 160px; margin-right: 22px"
          >
            <template slot="append">%</template></el-input
          >

          <template v-if="deal_form.get_commission_type === 2">
            <el-link
              style="margin-right: 24px"
              type="primary"
              @click.prevent="removeDomain2(item)"
              >删除</el-link
            >
            <el-link
              v-if="deal_form.list2.length === index + 1"
              type="primary"
              style="margin-right: 24px"
              @click.prevent="addDomain2"
              >添加</el-link
            >
          </template>
          <el-link type="danger">{{ tuanduiradio(item.radio, 2) }}</el-link>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_dialog_show = false">取 消</el-button>
        <el-button type="primary" @click="onCreateData">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_dialog_deal" width="600px" :title="titleMap[dialogTitle]">
      <el-form label-width="100px" :model="deal_setting_form">
        <el-form-item label="成员：">
          <el-input
            placeholder="请选择使用成员"
            v-model="to_username"
            @focus="show_member_list = true"
            style="width: 300px"
          >
            <i
              @click="delName"
              slot="suffix"
              class="el-input__icon el-icon-circle-close"
            ></i
          ></el-input>
        </el-form-item>


        <template v-for ="(item,index) in cus_form">
        <el-form-item label ="客户姓名" :key ="index+'_1'">
          <div class="cus_name flex-row"  >
            <el-input style ="width:180px;margin-right:5px"  v-model ="cus_form[index].name" placeholder="请输入客户姓名"></el-input>
            <el-select style ="width:160px;margin-right:5px"  v-model ="cus_form[index].type">
              <el-option v-for="item in type_list_form"
              :key="item.id"
              :label="item.name"
              :value ="item.id"></el-option>
            </el-select>
            <el-button @click ="operCus(index)" :type="index ==0?'primary':'warning'"> {{index==0?'添加':'删除'}}</el-button>
          </div>
          </el-form-item>
          <el-form-item label="客户手机号"  :key ="index+'_2'">
          <div class="cus_name flex-row">
            <el-input style ="width:180px;margin-right:5px"   v-model ="cus_form[index].tel" placeholder="请输入客户手机号"></el-input>
            <el-input style ="width:160px;margin-right:5px"   v-model ="cus_form[index].money" placeholder="请输入金额"></el-input>
          </div>
          </el-form-item>
          <el-form-item label="备注" :key ="index+'_3'" >
            <el-input style ="width:350px;"  type="textarea"  v-model ="cus_form[index].descp" placeholder="请输入客户备注内容"></el-input>
          
        </el-form-item>
      
      </template>
      <el-form-item label="成交状态：">
        <el-radio-group v-model="deal_setting_form.state">
          <el-radio border :label="1">有效</el-radio>
          <el-radio border :label="2">无效</el-radio>
        </el-radio-group>
       </el-form-item>
        <!-- <el-form-item label="客户类型：">
          <el-radio-group v-model="deal_setting_form.type">
            <el-radio
              v-for="item in type_list_form"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="客户：">
          <el-input
            placeholder="请输入"
            style="width:300px"
            v-model="deal_setting_form.name"
          ></el-input>
        </el-form-item>
        <el-form-item label="单号：">
          <el-input
            placeholder="请输入"
            style="width:300px"
            v-model="deal_setting_form.number"
          ></el-input> </el-form-item
        ><el-form-item label="手机号：">
          <el-input
            placeholder="请输入"
            style="width:300px"
            maxlength="11"
            v-model="deal_setting_form.mobile"
          ></el-input>
        </el-form-item>
        <el-form-item label="金额：">
          <el-input
            placeholder="请输入"
            style="width:300px"
            type="number"
            min="0"
            v-model="deal_setting_form.money"
          ></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="操作时间：">
          <el-date-picker
            v-model="deal_setting_form.add_date"
            style="width:300px"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="报告备注：">
          <el-input
            placeholder="请输入成交报告备注内容"
            style="width:300px"
            v-model="deal_setting_form.remark"
          ></el-input>
        </el-form-item>
        <el-form-item label="成交时间：">
          <el-date-picker
            v-model="deal_setting_form.complete_time"
            style="width:300px"
            type="datetime"
            placeholder="选择日期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_dialog_deal = false">取 消</el-button>
        <el-button type="primary" @click="onFormData">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="分佣" :visible.sync="is_fenyong_dialog">
      <el-form :model="fy_form" label-width="100px">
        <el-form-item label="金额：">
          <el-input
            placeholder="请输入"
            style="width:300px"
            v-model="fy_form.money"
          ></el-input>
        </el-form-item>
        <el-form-item label="分佣模式：">
          <el-radio-group v-model="fy_form.get_commission_type">
            <el-radio
              v-for="item in commission_type_list_form"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="客户资料：">
          <div v-for="(item, index) in fy_form.list" :key="index">
            <el-input
              v-model="item.admin_name"
              placeholder="请输入"
              style="width: 160px; margin-right: 22px"
            ></el-input>

            <mySelect
              :optionSource="admin_list"
              v-model="item.admin_id"
              labelKey="user_name"
              valueKey="id"
              style="margin-right: 22px; display: inline"
              width="160px"
              @page-change="onPageChange"
              :paginationOption="{
                pageSize: admin_params.per_page, //每页显示条数
                currentPage: admin_params.page, //当前页
                pagerCount: 5, //按钮数，超过时会折叠
                total: admin_params.total, //总条数
              }"
            ></mySelect>
            <el-input
              v-model="item.radio_name"
              placeholder="请输入"
              style="width: 160px; margin-right: 22px"
            ></el-input>
            <el-input
              v-model="item.radio"
              placeholder="比例"
              style="width: 160px; margin-right: 22px"
            >
              <template slot="append">%</template></el-input
            >

            <template>
              <el-link
                style="margin-right: 24px"
                type="primary"
                @click.prevent="removeDomainForm(item)"
                >删除</el-link
              >
              <el-link
                v-if="fy_form.list.length === index + 1"
                type="primary"
                style="margin-right: 24px"
                @click.prevent="addDomainForm"
                >添加</el-link
              >
            </template>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_fenyong_dialog = false">取 消</el-button>
        <el-button type="primary" @click="onFormDataFy">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <multipleTree
        ref="memberLists"
        v-if="show_member_list"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
        :defaultExpandAll="false"
      >
      </multipleTree>
    </el-dialog>

    <el-dialog :visible.sync="show_see" width="750px" title="查看客户">
      <el-table  
            :data="seeTableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle">
            <el-table-column label="ID" width="80" prop="id"></el-table-column>
            <el-table-column label="客户名称" prop="name"></el-table-column>
            <el-table-column label="客户手机号"  prop="tel"></el-table-column>
            <el-table-column label="客户备注"  prop="descp"></el-table-column>
            <el-table-column label="客户类型"  prop="descp" v-slot ="{row}">
              <el-tag type="primary" v-if ="row.type==1">买方</el-tag>
              <el-tag type="warning" v-if ="row.type==2">卖方</el-tag>
              <el-tag type="success" v-if ="row.type==3">合作方</el-tag>
            </el-table-column>

      </el-table>
    </el-dialog>
  </div>
</template>

<script>
// import myLabel from "./components/my_label";
import mySelect from "./components/my_select";
import myCheck from "./components/my_check";
// import myEmpty from "@/components/components/my_empty.vue";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
  name: "crm_customer_deal",
  components: {
    // myLabel,
    mySelect,
    // myEmpty,
    myCheck,
    multipleTree,
  },
  data() {
    return {
      params: {
        page: 1,
        total: 0,
        state:0,
        department_id:"",
        // status: "", // 是否完成分佣(0:未完成,1:已完成)
        // is_status: 0, // 财务审核状态(1:有效，2:无效)
        // get_commission_type: 0, //分佣模式(1:独立成交,2:合作分成)
        type: 1, //审批类型(1:客源审批,2:房源审批)
        // keywords: "",
        per_page: 10,
      },
      tableData: [],
      is_table_loading: true,
      finance_status_list: [
        { id: "", name: "全部" },
        { id: 0, name: "否" },
        { id: 1, name: "是" },
      ],
      is_status: [
        { id: 0, name: "全部" },
        { id: 1, name: "有效" },
        { id: 2, name: "无效" },
      ],
      commission_type_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "独立成交" },
        { id: 2, name: "合作分成" },
      ],
      type_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "买方" },
        { id: 2, name: "卖方" },
        { id: 3, name: "合作方" },
      ],
      is_dialog_show: false,
      deal_form: {
        approver_id: "",
        money: 0,
        get_commission_type: 1,
        group_radio: 0,
        other_radio: 0,
        list: [
          {
            admin_name: "",
            admin_id:0,
            radio_name: "",
            radio: "",
            type: 1,
          },
        ],
        list2: [
          {
            admin_name: "",
            admin_id: 0,
            radio_name: "",
            radio: "",
            type: 2,
          },
        ],
      },
      cus_form:[{
        name:'',
        tel:"",
        descp:"",
        money:'',
        type:'',
      }],
      deal_setting_form: {},
      admin_list: [],
      admin_params: {
        page: 1,
        per_page: 10,
        user_name: "",
        total: 0,
        type: 1,
      },
      user_detail: {},
      auth_transaction: 0, // 成交是否显示
      is_dialog_deal: false,
      titleMap: {
        addData: "成交报告",
        updateData: "修改数据",
      },
      dialogTitle: "",
      is_fenyong_dialog: false,
      fy_form: {},
      to_username: "",
      memberList:[],
      memberLists:[],
      show_member_list: false,
      selectedIds: [],
      show_see:false,
      seeTableData:[],
      showAdd:0,
      loadEnd:false,
      datalist: [], // 全部部门人员
      AllDepartment: [], // 全部部门列表
      Not_duplicate_datalist: [], // 没有重复部门成员的容器
    };
  },
  mounted() {
    this.getUserInfo();
    setTimeout(()=>{
      this.getDepartment()
    },200)
  },
  computed: {
    type_list_form() {
      return this.type_list.filter((item) => item.id);
    },
    commission_type_list_form() {
      return this.commission_type_list.filter((item) => item.id);
    },
  },
  methods: {
    changeDepartment(){
      this.params.page =1
      this.getDataList()
    },
    showSee(item){
      this.$http.getSeeCustomerList(item.id).then(res=>{
        if (res.status ==200){
          this.seeTableData =res.data
          this.show_see=true
        }
      })
    },
    details(id){
      let url = `/crm_customer_detail?id=${id}&type=seas`;
      this.$goPath(url);
    },
    getUserInfo() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          if (res.data.roles[0].name === "站长") {
            this.auth_transaction = 1;
            this.getDataList();
            this.getAdmin();
          } else {
            this.getDataList();
            this.getCheckShow(res.data.id);
          }
        }
      });
    },
    // 获取成交显示
    getCheckShow(id) {
      // this.$http.getAuthShow("deal_auth").then((res) => {
        this.$http.getCommonSettingRolesConf().then((res) => {
        if (res.status === 200) {
          let curr = res.data.find(item=>item.key=="deal_auth")
          if (curr&&(curr.value+'').indexOf(id) != -1){
          // if ((res.data+'').indexOf(id) != -1) {
            // this.auth_transaction = 1;
            this.showAdd =1
            this.getDataList();
            this.getAdmin();
            // this.loadEnd =true
          }else {
            this.is_table_loading =false
          }
        }
      });
    },
    delName() {
      this.deal_setting_form.admin_id = 0;
      this.to_username = "";
    }, // 获取部门列表
    async getDepartment() {
      // let res = await this.$http.getCrmDepartmentList().catch((err) => {
      //   console.log(err);
      // });
      // if (res.status == 200) {
      //   this.memberList = res.data;
      //   this.memberLists =JSON.parse(JSON.stringify( res.data))
      // }
      this.getDepartmentList(); // 获取部门
      // 获取部门成员
      if (!this.datalist.length) {
        this.getMemberList();
      }
    },
    //获取部门成员信息
    getMemberList() {
      this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data;
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user,
          });
          this.recursionData(this.memberList);
          this.Not_duplicate_datalist = JSON.parse(
            JSON.stringify(this.datalist)
          );
          this.filteredData();
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      });
    },
    // 部门成员去重
    filteredData() {
      const uniqueIds = {};
      const filtered = [];
      for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
        const item = this.Not_duplicate_datalist[i];
        if (!uniqueIds[item.id]) {
          uniqueIds[item.id] = true;
          filtered.push(item);
        }
      }
      this.filtrMember = filtered;
    },
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              });
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item);
            }
          });
          this.recursionData(data[key].subs);
        }
      }
    },
    // 获取部门
    getDepartmentList() {
      if (!this.AllDepartment.length) {
        this.$http.getCrmDepartmentList().then((res) => {
          if (res.status == 200) {
            this.AllDepartment = res.data;
          }
        });
      }
    },
    selecetedMember(e) {
      // if (e.checkedNodes && e.checkedNodes.length) {
      //   this.to_username = e.checkedNodes[e.checkedNodes.length - 1].name;
      //   this.deal_setting_form.admin_id =
      //     e.checkedNodes[e.checkedNodes.length - 1].id;
      //   // this.deal_setting_form.department_id =
      //   //   e.checkedNodes[e.checkedNodes.length - 1].department[0].id;
      // } else {
      //   this.to_username = "";
      //   this.deal_setting_form.admin_id = 0;
      //   // this.deal_setting_form.department_id = "";
      // }
      // this.show_member_list = false;
      console.log(e.checkedNodes, "成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.to_username = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.deal_setting_form.admin_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.deal_setting_form.admin_id = "";
      }
      this.show_member_list = false;
    },
    tuanduiradio(r, type) {
      let radio =
        type == 1 ? this.deal_form.group_radio : this.deal_form.other_radio;
      let r1 = this.toPoint(radio + "%");
      let r2 = this.toPoint(r + "%");
      let m = this.deal_form.money * r1;
      let m2 = m.toFixed(2) * r2;
      return "大约" + m2 + "元";
    },
    // 百分数转小数
    toPoint(percent) {
      var str = percent.replace("%", "");
      str = str / 100;
      return str;
    },
    onClickTabs(e) {
      this.params.state = e.id;
      this.getDataList();
    },
    getAdmin() {
      this.$http
        .getManagerAuthList({
          params: this.admin_params,
        })
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    getDataList() {
      // if (!this.params.status) {
      //   delete this.params.status;
      // }
      
      let params = Object.assign({},this.params)
      if (params.department_id&&params.department_id.length>0){
        params.department_id =params.department_id[params.department_id.length-1]
      }
      this.is_table_loading = true;
      this.$http.getCrmCustomerDealNew({ params}).then((res) => {
        this.is_table_loading = false;
        this.loadEnd=true
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    onFenyong(row) {
      this.$http.getDealDetailData(row.id).then((res) => {
        if (res.status === 200) {
          this.is_fenyong_dialog = true;
          this.fy_form = {
            id: res.data.id,
            money: res.data.money,
            get_commission_type: res.data.get_commission_type || 1,
            list: [],
          };
          if (res.data.list.length > 0) {
            this.fy_form.list = res.data.list.map((item) => {
              return {
                admin_id: item.id,
                admin_name: item.admin_name,
                radio_name: item.radio_name,
                radio: item.radio,
              };
            });
          } else {
            this.fy_form.list = [
              { admin_id: "", admin_name: "", radio_name: "", radio: "" },
            ];
          }
        }
      });
    },
    onChangeRadio(e) {
      if (e === 1) {
        this.deal_form.list = [
          {
            admin_name: "成交人",
            admin_id: this.user_detail.admin ? this.user_detail.admin.id : "",
            radio_name: "分佣比例",
            radio: 100,
            type: 1,
          },
        ];
      } else {
        this.deal_form.list = [
          {
            admin_name: "录入人",
            admin_id: this.user_detail.admin ? this.user_detail.admin.id : "",
            radio_name: "分佣比例",
            radio: 50,
            type: 1,
          },
          {
            admin_name: "跟进人",
            admin_id: "",
            radio_name: "分佣比例",
            radio: 50,
            type: 1,
          },
          {
            admin_name: "成交人",
            admin_id: "",
            radio_name: "分佣比例",
            radio: 0,
            type: 1,
          },
        ];
      }
      this.deal_form.list2 = [
        {
          admin_name: "",
          admin_id: "",
          radio_name: "",
          radio: 0,
          type: 2,
        },
      ];
    },
    getCurrentTime() {
      //获取当前时间并打印
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? "0" + new Date().getMinutes()
          : new Date().getMinutes();
      let ss =
        new Date().getSeconds() < 10
          ? "0" + new Date().getSeconds()
          : new Date().getSeconds();
      return yy + "-" + (mm+'').padStart(2,0) + "-" + (dd+'').padStart(2,0) + " " + hh + ":" + mf + ":" + ss;
    },
    onCreateDataDeal() {
      this.is_dialog_deal = true;
      this.dialogTitle = "addData";
      this.cus_form =[{
        name:"",
        tel:"",
        type:"",
        descp:"",
        money:""
      }]
      this.to_username =''
      this.deal_setting_form = {
        admin_id:0,
        name: "",
        type: 1,
        money: "",
        remark: "",
        customers:[],
        complete_time: this.getCurrentTime()
      
      };
    },
    onClickCommission(e) {
      this.params.get_commission_type = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onClickSource(e) {
      this.params.status = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onClickAudit(e) {
      this.params.is_status = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onClickType(e) {
      this.params.type = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    addDomain() {
      if (this.deal_form.list.length === 10) {
        return;
      }
      this.deal_form.list.push({
        admin_name: "",
        admin_id: 0,
        radio_name: "",
        radio: "",
        type: 1,
      });
    },
    addDomainForm() {
      if (this.fy_form.list.length === 10) {
        return;
      }
      this.fy_form.list.push({
        admin_name: "",
        admin_id: "",
        radio_name: "",
        radio: "",
      });
      console.log(this.fy_form.list);
    },
    addDomain2() {
      if (this.deal_form.list2.length === 10) {
        return;
      }
      this.deal_form.list2.push({
        admin_name: "",
        admin_id: "",
        radio_name: "",
        radio: "",
        type: 2,
      });
    },
    removeDomain(item) {
      if (this.deal_form.list.length === 1) {
        return;
      }
      var index = this.deal_form.list.indexOf(item);
      if (index !== -1) {
        this.deal_form.list.splice(index, 1);
      }
    },
    removeDomainForm(item) {
      if (this.fy_form.list.length === 1) {
        return;
      }
      var index = this.fy_form.list.indexOf(item);
      if (index !== -1) {
        this.fy_form.list.splice(index, 1);
      }
    },
    removeDomain2(item) {
      if (this.deal_form.list2.length === 1) {
        return;
      }
      var index = this.deal_form.list2.indexOf(item);
      if (index !== -1) {
        this.deal_form.list2.splice(index, 1);
      }
    },
    onClickDialog(e) {
      this.user_detail = e;
      this.is_dialog_show = true;
      this.deal_form.approver_id = e.id;
      if (this.deal_form.get_commission_type === 1) {
        this.deal_form.list[0].admin_name = "成交人";
        this.deal_form.list[0].admin_id = e.admin ? e.admin.id :0;
        this.deal_form.list[0].radio_name = "独立成交";
        this.deal_form.list[0].radio = 100;
        this.deal_form.list[0].type = 1;
      }
      // this.$goPath(`/crm_customer_deal_detail?id=${e.id}`);
    },
    getDealDetail(id){
      this.$http.getDealDetail(id).then(res=>{
        if(res.status==200){
          this.deal_setting_form =  res.data
          this.to_username =res.data.admin_name
          if (res.data.customers&& res.data.customers.length){
            this.cus_form = JSON.parse(JSON.stringify(res.data.customers) ) 
          }else {
            this.cus_form =[
              {
                name:"",
                tel:"",
                type:'',
                descp:"",
                money:''
              }
            ]
          }
          
          this.is_dialog_deal = true;
          this.dialogTitle = "updateData";
        }

      })
    },


    onEditData(row) {
      this.getDealDetail(row.id)
      // this.to_username =row.admin_name
      // this.is_dialog_deal = true;
      // this.dialogTitle = "updateData";
      // this.deal_setting_form = {
      //   id: row.id,
      //   admin_id: row.admin_id,
      //   name: row.name,
      //   mobile: row.mobile,
      //   type: row.type,
      //   money: row.money,
      //   number: row.number,
      //   remarks: row.remarks,
      //   department_id: row.department_id,
      //   add_date: row.add_date,
      // };
    },
    onDeleteData(row) {
      this.$confirm("此操作将删除该内容, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteDealDataNew(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.params.page = 1;
              this.getDataList();
            }
          });
        })
        .catch(() => {});
    },
    onCreateData() {
      let params = Object.assign({}, this.deal_form);
      params.list = params.list.concat(params.list2);
      delete params.list2;
      if (!params.money) {
        params.list = [];
      }
      this.$http.setCrmCustomerDealData(params).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.is_dialog_show = false;
          this.getDataList();
        }
      });
    },
    onFormData() {
      let params ={}
      if (this.dialogTitle === "addData") {
        params = Object.assign({}, this.deal_setting_form);
        params.customers = JSON.stringify(this.cus_form)
        this.$http.createDealDataNew(params).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.is_dialog_deal = false;
            this.getDataList();
          }
        });
      } else {
        params = Object.assign({}, this.deal_setting_form);
        let cusIds = [];
        this.cus_form.map(item=>{
          if(item.id){
            cusIds.push(item.id);
          }
        })
       let  delArr = []
        if (this.deal_setting_form.customers&& this.deal_setting_form.customers.length>0){

          this.deal_setting_form.customers.map(item=>{
            if (!cusIds.includes(item.id)){
              item.id_deleted =1
              delArr.push(item)
            }
          })
        }
        params.customers = JSON.stringify(this.cus_form.concat(delArr))
        this.$http.editDealDataNew(params).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.is_dialog_deal = false;
            this.getDataList();
          }
        });
      }
    },
    onFormDataFy() {
      this.$http.setFyData(this.fy_form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.is_fenyong_dialog = false;
          this.getDataList();
        }
      });
    },
    operCus(index){
      if (index ==0){
        this.cus_form.push({
          name:"",
          tel:"",
          type:'',
          descp:"",
          money:''
      })
      return 
      }
      this.cus_form.splice(index,1)
    }
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .bottom-border {
    align-items: center;
    padding-bottom: 24px;
    justify-content: flex-start;
    border-bottom: 1px dashed #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
      .label {
        width: 70px;
        display: inline-block;
        text-align: right;
      }
    }
  }
}
.username {
  align-items: center;
  .name {
  }
  .level {
    border-radius: 2px;
    background: linear-gradient(180deg, #ff4f3b 0%, #ff8f78 100%);
    font-size: 12px;
    margin: 0 4px;
    line-height: 1;
    color: #fff;
    padding: 2px 4px;
  }
  img {
    width: 16px;
    height: 16px;
  }
}
.audit {
  font-size: 14px;
  color: #fff;
  padding: 3px 10px;
  border-radius: 4px;
  &.audit1 {
    background: #3172f6;
  }
  &.audit2 {
    background: #ff8a00;
  }
  &.audit3 {
    background: #fa5c5c;
  }
  &.audit4 {
    background: #00b432;
  }
}
.title-label {
  margin: 0 -24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e2e2;
  span {
    margin-left: 24px;
    color: #2e3c4e;
  }
}
.tuandui {
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid #e2e2e2;
}
::v-deep .status_check.tabs{
  margin-bottom: 0;
}
.forminfo {
}
</style>
