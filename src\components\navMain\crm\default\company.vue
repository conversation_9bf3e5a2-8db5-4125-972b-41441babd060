<template>
    <div>
        <el-form ref="form" :model="formData" label-width="120px" label-position="right">
            <div class="message-type">公司信息</div>
            <el-form-item label="运营人姓名：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.name"
                    placeholder="请输入运营人姓名"
                ></el-input>
            </el-form-item>
            <el-form-item label="手机号：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.phone"
                    placeholder="请输入手机号"
                ></el-input>
            </el-form-item>
            <el-form-item label="企业名称：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.company_name"
                    placeholder="请输入企业名称"
                ></el-input>
            </el-form-item>
            <el-form-item label="营业执照编号：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.business_no"
                    placeholder="请输入营业执照编号："
                ></el-input>
            </el-form-item>
            <el-form-item label="营业执照照片：">
                <el-upload
                    multiple
                    action="/api/common/file/upload/admin?category=3"
                    list-type="picture-card"
                    accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif"
                    :file-list="blFileList"
                    :limit="3"
                    :headers="myHeader"
                    :on-success="(response, file, fileList) => {
                        onUploadBusinessSuccess({response, file, fileList})
                    }"
                    :on-remove="onRemoveBusiness"
                >
                    <i class="el-icon-plus"></i>
                </el-upload>
            </el-form-item>
            <el-form-item label="法人姓名：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.legal_name"
                    placeholder="请输入法人姓名"
                ></el-input>
            </el-form-item>
            <el-form-item label="注册地址：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.company_address"
                    placeholder="请输入注册地址"
                ></el-input>
            </el-form-item>
            <div class="message-type">发票信息</div>
            <el-form-item label="公司名称：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.invoice_company_name"
                    placeholder="请输入公司名称"
                ></el-input>
            </el-form-item>
            <el-form-item label="纳税人识别号：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.invoice_itin"
                    placeholder="请输入纳税人识别号"
                ></el-input>
            </el-form-item>
            <el-form-item label="公司地址：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.invoice_address"
                    placeholder="请输入公司地址"
                ></el-input>
            </el-form-item>
            <el-form-item label="开户行名称：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.invoice_bank"
                    placeholder="请输入开户行名称"
                ></el-input>
            </el-form-item>
            <el-form-item label="银行账号：">
                <el-input 
                    style="width: 300px;" 
                    v-model="formData.invoice_account"
                    placeholder="请输入银行账号"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="saveCorporation" :loading="is_loading">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import config from "@/utils/config";
export default {
    data() {
        return {
            formData: {
                name: "", // 运营人姓名(最多20字 必填)
                phone: "", // 手机号(必填)
                company_name: "", // 企业名称
                business_no: "", // 营业执照编号
                business_img: [], // 营业执照照片
                legal_name: "", // 法人姓名
                company_address: "", // 注册地址
                invoice_company_name: "", // 公司名称 invoice开头的变量 统一属于 【发票】 相关字段内容
                invoice_itin: "", // 纳税人识别号
                invoice_address: "", // 公司地址
                invoice_bank: "", // 开户行名称
                invoice_account: "", // 银行账号
            },
            blFileList: [], // 存放回显图片的数组
            is_loading: false, // loading加载
        }
    },
    created() {
        this.getCorporation();
    },
    computed: {
        myHeader() {
            return {
                Authorization: config.TOKEN,
            };
        },
    },
    methods: {
        // 上传营业执照图片成功的回调函数
        onUploadBusinessSuccess(option = {}) {
            let { response } = option;
            console.log(response, "this.formData.business_img",this.formData.business_img)
            this.formData.business_img.push(response.url);
            // console.log(this.formData.business_img,"上传formData.business_img");
        },
        // 删除营业执照图片回调函数
        onRemoveBusiness(file, fileList) {
            let numFile = [];
            if(fileList && fileList.length) {
                fileList.map((item) => {
                    // numFile.push(item.url);
                    numFile.push(item.response.url);
                })
            }
            this.formData.business_img = numFile;
            console.log(this.formData.business_img,"删除formData.business_img");
        },
        // 保存公司信息
        saveCorporation() {
            if(this.formData.name == "" || this.formData.name == undefined) {
                return this.$message.warning("请填写运营人姓名");
            }
            if(this.formData.phone == "" || this.formData.phone == undefined) {
                return this.$message.warning("请填写手机号");
            }
            if(this.formData && this.formData.key) {
                delete this.formData.key;
            }
            this.formData.business_img = this.formData.business_img && this.formData.business_img.length ? this.formData.business_img.join(",") : this.formData.business_img
            // 如果一张图片都没有
            if (!this.formData.business_img) {
                this.formData.business_img = [];
            }
            
            // 然后再进行长度判断
            if(this.formData.business_img.length == 0) {
                console.log("执行赋空");
                this.formData.business_img = "";
            }
            this.is_loading = true; // 开启loading
            this.$http.saveCorporationDetails(this.formData).then((res) => {
                if(res.status == 200) {
                    this.is_loading = false; // 关闭loading
                    this.$message.success("保存成功");
                    if(this.formData.business_img == "") {
                        this.formData.business_img = [];
                    } else if(this.formData.business_img.indexOf(",") >= 0) {
                        this.formData.business_img = this.formData.business_img.split(",");
                    }
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 获取公司信息详情
        getCorporation() {
            this.$http.getCorporationDetails().then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"数据");
                    if(res.data && Object.keys(res.data).length) {
                        this.formData = res.data;
                    }
                    if(this.formData.business_img.indexOf(",") >= 0) {
                        this.formData.business_img = this.formData.business_img.split(",");
                        this.formData.business_img.forEach(item => {
                            const obj = {
                                response: {url: item},
                                url: item
                            }
                            this.blFileList.push(obj); // 赋值回显图片
                        })
                        // console.log(this.blFileList,"回显this.blFileList")
                    } else if(this.formData.business_img && this.formData.business_img.length) {
                        const obj = {
                            response: {url: this.formData.business_img},
                            url: this.formData.business_img
                        }
                        this.blFileList.push(obj); // 回显图片
                        this.formData.business_img = [this.formData.business_img];
                        console.log(this.formData.business_img,"回显this.formData.business_img")
                    } else if(this.formData.business_img.length == 0) {
                        this.formData.business_img = [];
                    }
                }
            })
        }
    },
}
</script>
<style lang="scss" scoped>
.message-type {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 24px;
    margin-left: 20px;
}
</style>