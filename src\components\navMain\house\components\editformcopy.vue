<template>
  <el-form ref="form" :model="form" label-width="80px" label-position="left">
    <div class="form_block">
      <!-- <div class="title">基础信息</div> -->
      <div class="title">
        {{
          form.trade_type === 1
          ? "基础信息"
          : form.trade_type === 2
            ? "出租信息"
            : "出售信息"
        }}
      </div>
      <template v-if="form.house_manager">
        <el-form-item label="类型">
          <el-radio-group v-model="form.trade_type" size="mini">
            <el-radio v-for="item in options_tradeType" :key="item.values" :label="item.values" border
              @change="changeoptions">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
      <!--  v-if="auth_view_vip == 1" -->
      <template v-if="auth_view_vip == 1">
        <!-- <template> -->

        <el-form-item label="小区">
          <el-select v-model="form.community_id" filterable remote reserve-keyword :remote-method="searchCommunity"
            :loading="community_loading" disabled style="width: 240px; margin-right: 12px" placeholder="请搜索并选择"
            @change="onCommunityChange">
            <el-option v-for="item in community_list" :key="item.id" :value="item.id" :label="item.title">
              <div class="option-wrapper">
                {{ item.detail }}
              </div>
            </el-option>
            <el-option v-if="community_list.length === 0 && unsearch" :value="null">
              <div class="add_community" @click="showApplyCommunity">
                <span>没找到？申请添加</span>
                <i class="el-icon-circle-plus"></i>
              </div>
            </el-option>
          </el-select>
          <el-select v-model="form.usage_type_id" style="width: 140px; margin-right: 12px" disabled placeholder="请选择用途">
            <el-option v-for="item in form_options.usageType" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.usage_type_id != 4 && form.usage_type_id != 5 && form.usage_type_id != 6 && form.usage_type_id != 7 && form.usage_type_id != 8">
          <div class="flex-row items-center" slot="label">
            <span> 房号 </span>
            <el-tooltip placement="top-start" width="200" trigger="hover" effect="light" v-if="isOpenShowingSingle">
              <div slot="content" style="line-height: 1.5">
                单边代理已开启，仅同店和平台巡检可查看
              </div>
              <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
              <span icon="el-icon-info" type="danger"><i class="el-icon-info" style="color: #f56c6c"></i></span>
            </el-tooltip>
          </div>
          <div class="form-item-block">
            <el-select :disabled="!form.community_id" v-model="form.building_loudong_id"
              style="width: 140px; margin-right: 12px" placeholder="请选择楼号" filterable remote
              :remote-method="customerSearch" @change="onSelectLoudong">
              <el-option v-for="item in loudong_list" :key="item.loudong" :value="item.loudong + ''"
                :label="item.name + ''"></el-option>
            </el-select>
          </div>

          <div class="form-item-block" v-if="show_unit">
            <el-select :disabled="!form.community_id" v-model="form.building_danyuan_id"
              style="width: 140px; margin-right: 12px" placeholder="请选择单元" filterable remote :remote-method="(e) => {
                return customerSearch(e, 'danyuan');
              }
                " @change="onSelectDanyuan">
              <el-option v-for="item in danyuan_list" :key="item.danyuan" :value="item.danyuan + ''"
                :label="item.name + ''"></el-option>
            </el-select>
          </div>

          <div class="form-item-block">
            <el-select :disabled="!form.community_id" v-model="form.building_unit_id"
              style="width: 140px; margin-right: 12px" placeholder="请选择户号" filterable remote @change="checkRepeat"
              :remote-method="(e) => {
                return customerSearch(e, 'fanghao');
              }
                ">
              <el-option v-for="item in huhao_list" :key="item.value" :value="item.value + ''"
                :label="item.name"></el-option>
            </el-select>
          </div>
          <div class="form-item-block" v-if="form.community_id">
            <el-link type="primary" @click="showSelectTable">选择</el-link>
          </div>
        </el-form-item>
        <el-form-item label="楼层" v-if="(form.building_danyuan_id.split('_').length > 1 ||
          form.building_unit_id.split('_').length > 1 ||
          !show_unit || isShowFloorInput) &&
          form.building_danyuan_id != 0
          ">
          <el-input v-if="(form.building_danyuan_id.split('_').length != 1 || !show_unit || isShowFloorInput) &&
            form.building_danyuan_id != 0
            " v-model="form.total_floor" style="width: 130px; margin-right: 12px" placeholder="请输入总楼层"></el-input>
          <el-input v-if="form.building_unit_id.split('_').length != 1 || isShowFloorInput" v-model="form.sz_floor"
            style="width: 130px; margin-right: 12px" placeholder="请输入所在楼层"></el-input>
        </el-form-item>
      </template>
      <el-form-item label="户型"
        v-if="form.usage_type_id != 4 && form.usage_type_id != 5 && form.usage_type_id != 6 && form.usage_type_id != 7 && form.usage_type_id != 8">
        <el-input v-number_rang="[0]" v-model="form.shi" style="width: 127px; margin-right: 12px" placeholder="请输入室">
          <template slot="append">室</template></el-input>

        <el-input v-number_rang="[0]" v-model="form.ting" style="width: 127px; margin: 0 12px" placeholder="请输入厅">
          <template slot="append">厅</template>
        </el-input>

        <el-input v-number_rang="[0]" v-model="form.wei" style="width: 126px; margin: 0 12px" placeholder="请输入卫">
          <template slot="append">卫</template>
        </el-input>

        <el-input v-number_rang="[0]" v-model="form.kitchen" style="width: 126px; margin: 0 12px" placeholder="厨房数量">
          <template slot="append">厨</template></el-input>
        <el-input v-number_rang="[0]" v-model="form.balcony" style="width: 126px; margin: 0 12px" placeholder="阳台数量">
          <template slot="append">阳台</template></el-input>
        <!-- <div v-if="isOpenShowingSingle">
            <el-link type="danger">
              单边代理已开启，仅同店和平台巡检可查看</el-link
            >
          </div> -->
      </el-form-item>
      <el-form-item label="装修">
        <el-select v-model="form.zhuangxiu" style="width: 240px; margin-right: 12px" placeholder="请选择装修">
          <el-option v-for="item in form_options.decoration" :key="item.values" :value="item.values"
            :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="朝向">
        <el-select v-model="form.chaoxiang" style="width: 240px; margin-right: 12px" placeholder="请选择朝向">
          <el-option v-for="item in form_options.direction" :key="item.values" :value="item.values"
            :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="面积">
        <el-input v-model="form.mianji" v-number_rang:[2]="[0]" style="width: 240px; margin-right: 12px"
          placeholder="请输入面积">
          <template slot="append">m²</template>
        </el-input>
      </el-form-item>
      <!--商铺-->
      <div v-if="form.usage_type_id == 4">
        <el-form-item label="当前状态" prop="business_status">
          <el-radio-group v-model="form.business_status">
            <el-radio v-for="item in form_options.business_status" :key="item.values" :label="item.values">{{ item.name
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="经营行业" prop="business_trades">
          <!-- <el-select v-model="form.business_trades" style="width: 240px; margin-right: 12px" placeholder="请选择经营行业">
            <el-option v-for="item in form_options.business_trades" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select> -->
          <el-cascader placeholder="请选择经营行业" style="width: 240px; margin-right: 12px" v-model="form.business_trades"
            clearable :options="form_options.business_trades" :props="{
              value: 'values',
              label: 'name',
            }"></el-cascader>
        </el-form-item>
        <el-form-item label="类型" prop="business_type">
          <el-select v-model="form.business_type" style="width: 240px; margin-right: 12px" placeholder="请选择类型">
            <el-option v-for="item in form_options.business_type4" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配套" prop="facilities_sage">
          <div class="facility-box">
            <el-checkbox-group v-model="form.facilities_sage" style="width: 500px;">
              <el-checkbox v-for="item in form_options.facilities_sage4" :key="item.values" :label="item.values">{{
                item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form-item label="楼层" prop="floor_type">
          <el-select v-model="form.floor_type" style="width: 120px; margin-right: 12px" placeholder="请选择层">
            <el-option v-for="item in form_options.floor_type" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
          <el-input v-if="form.floor_type == 1 || form.floor_type == 2" v-number_rang:[2]="[0]" v-model="form.sz_floor"
            maxlength="13" style="width: 160px">
            <template slot="prepend">第</template>
            <template slot="append">层</template></el-input>
          <span v-if="form.floor_type == 2">至</span>
          <el-input v-if="form.floor_type == 2" v-number_rang:[2]="[0]" v-model="form.sz_floor_2" maxlength="13"
            style="width: 160px">
            <template slot="prepend">第</template>
            <template slot="append">层</template></el-input>
          <span style="margin: 0px 5px;"></span>
          <el-input v-number_rang:[2]="[0]" v-model="form.total_floor" maxlength="13" style="width: 160px">
            <template slot="prepend">共</template>
            <template slot="append">层</template></el-input>
        </el-form-item>
        <div style="display: flex;">
          <el-form-item label="面宽" prop="plane_width">
            <el-input v-number_rang:[2]="[0]" v-model="form.plane_width" maxlength="13" style="width: 160px"><template
                slot="append">米</template></el-input>
          </el-form-item>
          <el-form-item label="层高" prop="floor_height" style="margin: 0px 40px;">
            <el-input v-number_rang:[2]="[0]" v-model="form.floor_height" maxlength="13" style="width: 160px"><template
                slot="append">米</template></el-input>
          </el-form-item>
          <el-form-item label="进深" prop="depth">
            <el-input v-number_rang:[2]="[0]" v-model="form.depth" maxlength="13" style="width: 160px"><template
                slot="append">米</template></el-input>
          </el-form-item>
        </div>
        <el-form-item label="客流人群" prop="business_customer">
          <div class="facility-box">
            <el-checkbox-group v-model="form.business_customer" style="width: 500px;">
              <el-checkbox v-for="item in form_options.business_customer" :key="item.values" :label="item.values">{{
                item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <div v-if="radiotrade_type">
          <el-form-item label="是否面议" prop="is_negotiable">
            <el-switch :active-value="1" :inactive-value="0" v-model="form.is_negotiable"></el-switch>
          </el-form-item>
          <div style="display: flex;" v-if="form.is_negotiable == 0">
            <el-form-item label="转让费" prop="sale_price">
              <el-input v-number_rang:[2]="[0]" v-model="form.sale_price" maxlength="13" style="width: 160px"><template
                  slot="append">万元</template></el-input>
              <div style="color:orange">
                转让费为0时表示面议
              </div>
            </el-form-item>
          </div>
        </div>
        <el-form-item v-if="radiotrade_type" label="剩余租期" prop="remain_term">
          <el-input v-number_rang:[2]="[0]" v-model="form.remain_term" maxlength="13" style="width: 160px"><template
              slot="append">月</template></el-input>
        </el-form-item>
      </div>
      <!--写字楼-->
      <div v-if="form.usage_type_id == 5">
        <el-form-item label="类型" prop="business_type">
          <el-select v-model="form.business_type" style="width: 240px; margin-right: 12px" placeholder="请选择朝向">
            <el-option v-for="item in form_options.business_type5" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配套" prop="facilities_sage">
          <div class="facility-box">
            <el-checkbox-group v-model="form.facilities_sage" style="width: 500px;">
              <el-checkbox v-for="item in form_options.facilities_sage5" :key="item.values" :label="item.values">{{
                item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form-item label="楼层" prop="floor_type">
          <el-select v-model="form.floor_type" style="width: 120px; margin-right: 12px" placeholder="请选择层">
            <el-option v-for="item in form_options.floor_type" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
          <el-input v-if="form.floor_type == 1 || form.floor_type == 2" v-number_rang:[2]="[0]" v-model="form.sz_floor"
            maxlength="13" style="width: 160px">
            <template slot="prepend">第</template>
            <template slot="append">层</template></el-input>
          <span v-if="form.floor_type == 2">至</span>
          <el-input v-if="form.floor_type == 2" v-number_rang:[2]="[0]" v-model="form.sz_floor_2" maxlength="13"
            style="width: 160px">
            <template slot="prepend">第</template>
            <template slot="append">层</template></el-input>
          <span style="margin: 0px 5px;"></span>
          <el-input v-number_rang:[2]="[0]" v-model="form.total_floor" maxlength="13" style="width: 160px">
            <template slot="prepend">共</template>
            <template slot="append">层</template></el-input>
        </el-form-item>

        <el-form-item label="可注册" prop="is_reg">
          <el-radio-group v-model="form.is_reg">
            <el-radio v-for="item in form_options.is_reg" :key="item.values" :label="item.values">{{ item.name
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="可分割" prop="is_split">
          <el-radio-group v-model="form.is_split">
            <el-radio v-for="item in form_options.is_split" :key="item.values" :label="item.values">{{ item.name
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 
          <el-form-item label="装修情况" prop="chaoxiang"> 
            <el-select v-model="form.chaoxiang" style="width: 240px; margin-right: 12px" placeholder="请选择层">
            <el-option v-for="item in form_options.direction" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
          </el-form-item> -->

      </div>
      <!--厂房-->
      <div v-if="form.usage_type_id == 6">
        <el-form-item label="类型" prop="business_type">
          <el-select v-model="form.business_type" style="width: 240px; margin-right: 12px" placeholder="请选择类型">
            <el-option v-for="item in form_options.business_type6 " :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配套" prop="facilities_sage">
          <div class="facility-box">
            <el-checkbox-group v-model="form.facilities_sage" style="width: 500px;">
              <el-checkbox v-for="item in form_options.facilities_sage6" :key="item.values" :label="item.values">{{
                item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </div>
      <!--车库-->
      <div v-if="form.usage_type_id == 7">
        <el-form-item label="类型" prop="business_type">
          <el-select v-model="form.business_type" style="width: 240px; margin-right: 12px" placeholder="请选择类型">
            <el-option v-for="item in form_options.business_type7 " :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <!--土地-->
      <div v-if="form.usage_type_id == 8">
        <el-form-item label="区域" prop="business_trades">
          <el-cascader placeholder="请选择区域" style="width: 240px; margin-right: 12px" v-model="form.region_id" clearable
            :options="form_options.region" :props="{
              value: 'values',
              label: 'name',
            }"></el-cascader>
        </el-form-item>
        <el-form-item label="土地用途" prop="business_type">
          <el-select v-model="form.land_use" style="width: 240px; margin-right: 12px" placeholder="请选择类型">
            <el-option v-for="item in form_options.land_use" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="土地来源" prop="business_type">
          <el-select v-model="form.land_source" style="width: 240px; margin-right: 12px" placeholder="请选择类型">
            <el-option v-for="item in form_options.land_source" :key="item.values" :value="item.values"
              :label="item.name"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="土地使用权" prop="business_status">
          <el-radio-group v-model="form.land_certificate">
            <el-radio v-for="item in form_options.land_certificate" :key="item.values" :label="item.values">{{ item.name
            }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="所有权证" prop="business_status">
          <el-radio-group v-model="form.land_ownership">
            <el-radio v-for="item in form_options.land_ownership" :key="item.values" :label="item.values">{{ item.name
            }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="流转年限" prop="plane_width">
          <el-input v-number_rang:[2]="[0]" v-model="form.land_turnover_period" maxlength="13"
            style="width: 240px"><template slot="append">米</template></el-input>
        </el-form-item>
        <div v-if="form.usage_type_id == 8 && form.trade_type == 3">
          <el-form-item label="转让费" prop="sale_price">
            <el-input v-number_rang:[2]="[0]" v-model="form.sale_price" maxlength="13" style="width: 240px"><template
                slot="append">万元</template></el-input>
            <div style="color:orange">
              转让费为0时表示面议
            </div>
          </el-form-item>
          <el-form-item label="剩余租期" prop="remain_term">
            <el-input v-number_rang:[2]="[0]" v-model="form.remain_term" maxlength="13" style="width: 240px"><template
                slot="append">个月</template></el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="!radiotrade_type">
        <el-row>
          <el-col :span="10" v-if="form.trade_type == 1 || form.trade_type == 3">
            <el-form-item label="出售价格">
              <el-input v-number_rang:[2]="[0]" v-model="form.sale_price" maxlength="13" style="width: 240px">
                <template slot="append">万元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <el-row v-if="form.trade_type === 2 || form.trade_type === 3">
        <el-col :span="10">
          <el-form-item label="租金价格">
            <el-input v-number_rang:[2]="[0]" v-model="form.rent_price" maxlength="13" style="width: 240px">
              <template slot="append">元/月</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

    </div>
    <el-dialog width="1000px" :visible.sync="show_select_table" title="选择楼栋">
      <div class="sel_container">
        <div class="top flex-row">
          <div class="top_left flex-1"></div>
          <template v-if="!isOpenShowingSingle">
            <div class="top_right sale_b flex-row align-center">出售</div>
            <div class="top_right rent_b flex-row align-center">出租</div>
            <div class="top_right sale_rent_b flex-row align-center">租售</div>
          </template>
          <div class="top_right">
            <el-link type="primary" @click="current_status_type = !current_status_type">{{ current_status_type ? "横排" :
              "竖排" }}</el-link>
          </div>
        </div>
        <div class="flex-row select_box">
          <div class="se_ld">
            <div class="se_container se_th">
              <div class="se_box">
                <div class="se_item se_th">
                  <div class="shangsanjiao"></div>
                  <div class="xiasanjiao"></div>
                  座栋名称
                </div>
              </div>
            </div>
            <div class="se_container se_tr">
              <div class="se_box">
                <div class="shangsanjiao"></div>
                <div class="xiasanjiao"></div>
                <div class="se_item" :class="{ active: form.building_loudong_id == item.loudong }"
                  v-for="item in loudong_list" :key="item.loudong" @click="clickLoudong(item)">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="se_ld" v-if="show_unit">
            <div class="se_container se_th">
              <div class="se_box">
                <div class="se_item se_th">
                  <div class="shangsanjiao"></div>
                  <div class="xiasanjiao"></div>
                  单元名称
                </div>
              </div>
            </div>
            <div class="se_container se_tr">
              <div class="se_box">
                <div class="shangsanjiao"></div>
                <div class="xiasanjiao"></div>
                <div class="se_item" :class="{ active: form.building_danyuan_id == item.danyuan }"
                  v-for="item in danyuan_list" :key="item.danyuan" @click="clickUnit(item)">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="se_ld" v-if="show_louceng">
            <div class="se_container se_th">
              <div class="se_box">
                <div class="se_item se_th">
                  <div class="shangsanjiao"></div>
                  <div class="xiasanjiao"></div>
                  楼层名称
                </div>
              </div>
            </div>
            <div class="se_container se_tr">
              <div class="se_box">
                <div class="shangsanjiao"></div>
                <div class="xiasanjiao"></div>
                <div class="se_item" :class="{ active: se_louceng == item.value }" v-for="item in louceng_list"
                  :key="item.value" @click="clickLouceng(item)">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="se_ld flex-1">
            <div class="se_item se_th">房间号</div>
            <div class="se_tr fanghao">
              <div class="se_box" :class="{
                'flex-row': !current_status_type,
                'align-center': !current_status_type,
                row: !current_status_type,
              }">
                <div class="se_item flex-row align-center j-center" :class="{
                  active: form.building_unit_id == item.value,
                  sale: item.trade_type == 1 && !isOpenShowingSingle,
                  rent: item.trade_type == 2 && !isOpenShowingSingle,
                  sale_rent: item.trade_type == 3 && !isOpenShowingSingle,
                }" v-for="item in huhao_list" :key="item.value" @click="selectFloor(item)">
                  <span>{{ item.name }}</span>
                  <!-- <el-link type="primary"> 选择 </el-link> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog width="1000px" :visible.sync="show_apply_community" title="申请小区">
      <addCommunity v-if="show_apply_community" dialogTitle="addData" :form="c_form" @success="handleSuccess">
      </addCommunity>
    </el-dialog>
  </el-form>
</template>
  
<script>
// import discernHouse from '@/utils/discern_house'
import addCommunity from "@/components/navMain/house/components/addCommunity.vue"
export default {
  name: "LmAddForm1",
  components: { addCommunity },
  data() {
    return {
      upload_headers: {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      },
      c_form: {
        title: "",
        addr: "",
        lat: "",
        lng: "",
        region_id: '',
        region_name: ''
      },
      loudong_units: [],
      danyuan_units: [],
      loudong_list: [],
      danyuan_list: [],
      huhao_list: [],
      fanghao_custom_level: 0, //户号自定义级别1：楼栋 2：单元 3：户号
      up_loading1: false,
      up_loading2: false,
      up_loading3: false,
      up_loading4: false,
      up_loading5: false,
      indoor_imgs: [],
      plans_imgs: [],
      outdoor_imgs: [],
      other_imgs: [],
      params: {

      },
      shopsquare: false, //判断楼房对应类型显示的form表单
      options_tradeType: [],
      radiotrade_type: false, //用以判断出售价格在转让时是否显示
      id: 1,
      form: {
        land_use: '',
        land_source: '',
        land_certificate: '',
        land_ownership: '',
        land_turnover_period: '', //流转年限
        region_id: [],

        facilities_sage: [],//配套
        business_status: '',
        business_trades: [],
        business_type: '',
        floor_type: '',
        sz_floor: '',
        sz_floor_2: '',
        total_floor: '',
        plane_width: '',
        floor_height: '',
        depth: '',
        business_customer: [],
        is_negotiable: 0,
        remain_term: '',
        sale_price: '',
        is_reg: '',
        is_split: '',


        id: '',
        trade_type: 1,
        community_id: "",
        shi: "",
        ting: "",
        wei: "",
        kitchen: "",
        balcony: "",
        mianji: "",
        taoneimianji: "",
        zhuangxiu: "",
        chaoxiang: "",
        loudong: "",
        loudongdanwei: "",
        danyuan: "",
        danyuandanwei: "",
        fanghao: "",
        building_unit_id: "",
        building_loudong_id: "",
        building_danyuan_id: "",
        exclusive: "",
        is_repeat: "", //房源是否查重
        status: "",
        trade_status: "",
        // sz_floor: "",
        // total_floor: "",
        usage_type_id: "",
        // hand_over_date: '',
        // fangben: '',
        // chanquanleixing: '',
        // chanquannianxian: '',
        rent_expire_time: "",
        // open_time: '',
        // trade_status: '',
        // management: '',
        // management_fee: '',
        // beian_no: '',
        has_key: "",
        // exclusive: '',
        only_house: "",
        occupancy: "",
        // delegate_type: '',
        // sell_delegate_user: '',
        // rent_delegate_user: '',
        // sell_delegate_time: '',
        // rent_delegate_time: '',
        buy_payment_type: "",
        exclusive_voucher: "",
        payment_terms: "",
        // sale_price: "",
        rent_price: "",
        sale_base_price: "",
        rent_base_price: "",
        pic: [],
        house_manager: 0
      },
      unsearch: false,
      community_list: [],
      community_loading: false,
      show_apply_community: false,
      show_map: false,
      search_key: "",
      point_marker: {},
      map_address: "",
      map_search_list: [],
      copy_content: "",
      has_set_cover: false,
      loudong_list_back: [],
      auth_view_vip: 0,
      show_select_table: false,
      se_loudong: '',
      se_danyuan: '',
      se_louceng: '',
      se_floor: '',
      louceng_list: [],
      fanghao_list: [],
      current_status_type: true,
      show_unit: true,
      show_louceng: false,
      current_danyuan_name: "",
      current_loudong_name: '',
      current_loudong_value: ""
    };
  },
  props: {
    form_options: {
      type: Object,
      default: () => { },
    },
    house_type: {
      type: Number,
      default: 1,
    },
    form_data: {
      type: Object,
      default: () => { },
    },
    isOpenShowingSingle: {
      type: [String, Boolean, Number],
      default: false,
    }
  },
  watch: {
    form_data(new_val) {
      console.log(new_val, 'new_val---')
      this.form.facilities_sage = new_val.house_info.facilities.split(",").map(id => id * 1);  //配套
      this.form.business_customer = new_val.house_info.business_customer.split(",").map(id => id * 1); //客流人群
      this.form.business_status = new_val.house_info.business_status  //商铺当前状态
      this.form.business_trades = new_val.house_info.business_trades_new  //商铺经营行业
      this.form.business_type = new_val.house_info.business_type  //类型

      this.form.land_use = new_val.house_info.land_use
      this.form.land_source = new_val.house_info.land_source
      this.form.land_certificate = new_val.house_info.land_certificate
      this.form.land_ownership = new_val.house_info.land_ownership
      this.form.land_turnover_period = new_val.house_info.land_turnover_period
      this.form.region_id = new_val.region_new

      this.form.floor_type = new_val.floor_type   //楼层
      this.form.sz_floor = new_val.sz_floor  //第几层
      this.form.sz_floor_2 = new_val.sz_floor_2  //至第几层
      this.form.total_floor = new_val.total_floor  //共几层
      this.form.plane_width = new_val.house_info.plane_width  //面宽
      this.form.floor_height = new_val.house_info.floor_height  //层高
      this.form.depth = new_val.house_info.depth  //进深
      this.form.is_negotiable = new_val.is_negotiable  //是否面议
      this.form.sale_price = new_val.sale_price  //转让费
      this.form.remain_term = new_val.house_info.remain_term  //剩余租期
      this.form.is_reg = new_val.house_info.is_reg  //可注册
      this.form.is_split = new_val.house_info.is_split  //可分割
      console.log(new_val, this.form.facilities_sage, 'new_val')
      this.community_list = [
        { title: new_val.title, id: new_val.community_id },
      ];
      this.auth_view_vip = new_val.auth_view_vip;
      let community_id = new_val.community_id
      this.form.community_id = community_id
      // this.huhao_list = [
      //   {
      //     name: new_val.fanghao,
      //     value: new_val.building_unit_id
      //   }
      // ]
      if (new_val.community_id > 0) {
        this.getLoudong(new_val.community_id);
      }

      let lou = '',
        dan = '',
        fang = ''
      // this.params.loudong =
      for (let key in new_val) {

        if (
          this.form[key] !== undefined ||
          (this.form[key] && this.form[key].length === 0)
        ) {
          this.current_loudong_name = new_val.loudong_no
          this.current_danyuan_name = new_val.danyuan_no
          if (new_val['building_unit_id'] > 0) {
            if (key == "building_loudong_id") {
              // this.current_loudong_name = new_val.loudong_no
              // this.current_danyuan_name =this.form.danyuan_no
              if (new_val[key]) {
                this.form[key] = new_val[key];
                this.current_loudong_id = new_val[key];
                if (new_val['building_danyuan_id'] == 0) {
                  this.getHouseNum()
                } else {
                  this.getUnit(new_val[key]);
                }

              } else {
                this.form.building_loudong_id =
                  "customer_" + new_val["loudong_no"];

                // this.onSelectLoudong(new_val['loudong_no'])
                setTimeout(() => {
                  this.loudong_list = [
                    {
                      name: new_val["loudong"],
                      loudong: "customer_" + new_val["loudong_no"],
                      value: "customer_" + new_val["loudong_no"],
                    },
                  ];
                }, 100);

                // this.form.building_loudong_id = "customer_" + new_val['loudong_no']
              }
            } else if (key == "building_danyuan_id") {
              this.current_danyuan_name = this.form.danyuan_no
              if (new_val[key] > 0) {
                this.form[key] = new_val[key];
                this.getHouseNum(new_val[key]);
                // this.getLouceng(new_val[key]);
              } else {
                // this.form.building_danyuan_id = new_val['danyuan_no']
                if (new_val["danyuan_no"] != 0) {
                  this.form.building_danyuan_id =
                    "customer_" + new_val["danyuan_no"];
                  setTimeout(() => {
                    this.danyuan_list = [
                      {
                        name: new_val["danyuan"],
                        danyuan: "customer_" + new_val["danyuan_no"],
                        value: "customer_" + new_val["danyuan_no"],
                      },
                    ];
                  }, 100);
                } else {

                  this.show_unit = false
                }

              }
              // this.form[key] = new_val[key]
              // this.getHouseNum(new_val[key])
            } else if (key == "building_unit_id") {
              if (new_val[key] != 0) {
                this.form[key] = new_val[key];
              } else {
                // this.customerSearch(new_val[key], "fanghao")

                this.form.building_unit_id = "customer_" + new_val["fanghao"];
                setTimeout(() => {
                  this.huhao_list = [
                    {
                      name: new_val["fanghao"],
                      value: "customer_" + new_val["fanghao"],
                    },
                  ];
                }, 100);
              }
            } else {
              this.form[key] = new_val[key];
            }
          } else if (
            key == "loudong"
          ) {
            this.form[key] = new_val[key];
            this.current_loudong_name = this.form.loudong_no
            console.log(this.current_loudong_name, 1111111111111111111111112);
            lou =
              "customer_" + new_val["loudong_no"];

            // this.onSelectLoudong(new_val['loudong_no'])
            setTimeout(() => {
              this.loudong_list = [
                {
                  name: new_val["loudong"],
                  loudong: "customer_" + new_val["loudong_no"],
                  value: "customer_" + new_val["loudong_no"],
                },
              ];
            }, 200);
          } else if (key == 'danyuan') {
            this.form[key] = new_val[key];
            this.current_danyuan_name = this.form.danyuan_no
            if (new_val["danyuan_no"] != 0) {
              dan =
                "customer_" + new_val["danyuan_no"];
              setTimeout(() => {
                this.danyuan_list = [
                  {
                    name: new_val["danyuan"],
                    danyuan: "customer_" + new_val["danyuan_no"],
                    value: "customer_" + new_val["danyuan_no"],
                  },
                ];
              }, 200);
            }
            // dan =
            //   "customer_" + new_val["danyuan_no"];
            // setTimeout(() => {
            //   this.danyuan_list = [
            //     {
            //       name: new_val["danyuan_no"],
            //       danyuan: "customer_" + new_val["danyuan_no"],
            //       value: "customer_" + new_val["danyuan_no"],
            //     },
            //   ];
            // }, 200);
          } else if (key == "fanghao") {
            this.form[key] = new_val[key];
            fang = "customer_" + new_val["fanghao"];
            setTimeout(() => {
              this.huhao_list = [
                {
                  name: new_val["fanghao"],
                  value: "customer_" + new_val["fanghao"],
                },
              ];
            }, 200);
          } else {
            console.log(key, 111);
            this.form[key] = new_val[key];
          }
        }
      }
      if (new_val['building_unit_id'] == 0) {
        this.form.building_loudong_id = lou
        this.form.building_danyuan_id = dan
        this.form.building_unit_id = fang
      }
      // setTimeout(() => {
      //   this.loudong_list.map(item => {
      //     console.log(item);
      //     if (item.loudong == this.form.building_loudong_id) {
      //       if (item.is_unit) {
      //         this.show_unit = true
      //       }
      //     }
      //   })
      // }, 500);


      // if (new_val.building_loudong_id) {
      //   console.log(new_val.building_loudong_id);
      // }

      this.form.pic.forEach((item) => {
        switch (item.category_id) {
          case 1:
            this.indoor_imgs.push(item);
            break;
          case 2:
            this.plans_imgs.push(item);
            break;
          case 3:
            this.outdoor_imgs.push(item);
            break;
          case 4:
            this.other_imgs.push(item);
            break;
        }
      });
      this.tradeType()
    },
    form: {
      handler(n_val) {
        this.$emit("change", n_val);
      },
      deep: true,
    },
    'form.community_id'(val, old_value) {
      if (old_value) {
        // 小区有改变需要清空楼栋单元的值
        this.form.loudong = ''
        this.form.danyuan = ''
        this.form.fanghao = ''
        this.form.building_loudong_id = ''
        this.form.building_danyuan_id = ''
        this.form.building_unit_id = ''
        this.fanghao_custom_level = 0
        this.loudong_custom_level = 0
        this.danyuan_custom_level = 0
      }
      // 获取小区的楼栋
      if (val > 0) {
        this.getLoudong(val);
      }
    },
  },
  computed: {
    //是否显示楼层输入
    isShowFloorInput() {
      //auth_view_vip 有修改权限
      return this.form_data?.auth_view_vip === 1
    }
  },
  mounted() {

  },
  created() {

    this.form.trade_type = this.house_type;
  },
  methods: {
    showApplyCommunity() {
      this.show_apply_community = true;
    },
    searchCommunity(e) {
      if (!e) return;
      this.community_loading = true;
      this.unsearch = false;
      this.$ajax.house
        .searchCommunity(e)
        .then((res) => {
          console.log(res);
          this.community_loading = false;
          if (res.status === 200) {
            this.community_list = res.data;
            if (this.community_list.length === 0) {
              this.unsearch = true;
            }
          } else {
            this.unsearch = true;
            this.community_list = [];
          }
        })
        .catch(() => {
          this.community_loading = false;
        });
    },
    onCommunityChange(e) {
      let curr = this.community_list.find(item => item.id == e)
      if (curr) {
        if (curr.manual == 1) {
          this.noGetLong = true
          this.form.status = 2;
        } else {
          this.noGetLong = false
          this.form.status = '';
        }

      }
      // this.getLoudong(e)
    },
    getLoudong(community_id) {
      this.$ajax.house.getLoudong({ community_id }).then((res) => {
        if (res.status === 200) {
          this.loudong_list = res.data;
          this.loudong_list_back = res.data;
        }
      });
    },
    customerSearch(e, type = "loudong") {
      if (!e) return;
      setTimeout(() => {
        let list = [];
        switch (type) {
          case "loudong":
            if (this.loudong_list_back && this.loudong_list_back.length) {
              this.loudong_list_back.map((item) => {
                list.push(JSON.parse(JSON.stringify(item)));
              });
            }
            this.loudong_list_back.map((item) => {
              list.push(JSON.parse(JSON.stringify(item)));
            });
            list = list.filter((item) => item.name.includes(e));
            if (!list.length) {
              if (e) {
                let obj = {
                  name: e,
                  loudong: "customer_" + e,
                  value: "customer_" + e,
                };
                list.push(obj);
              }

              this.loudong_list = list;
            }
            this.loudong_list = list;

            break;
          case "danyuan":
            if (this.danyuan_list_back && this.danyuan_list_back.length) {
              this.danyuan_list_back.map((item) => {
                list.push(JSON.parse(JSON.stringify(item)));
              });
            }
            console.log(list);
            list = list.filter((item) => item.name.includes(e));
            if (!list.length) {
              if (e) {
                let obj = {
                  name: e,
                  danyuan: "customer_" + e,
                  value: "customer_" + e,
                };
                list.push(obj);
              }

            }
            this.danyuan_list = list;

            break;
          case "fanghao":
            if (this.huhao_list_back && this.huhao_list_back.length) {
              this.huhao_list_back.map((item) => {
                list.push(JSON.parse(JSON.stringify(item)));
              });
            }
            list = list.filter((item) => item.name.includes(e));
            if (!list.length) {
              //   list.map(item => {
              //     console.log(item.name.includes(e), 'qqq');
              //     if (!item.name.includes(e) && item.name != 'e') {
              //       let obj = {
              //         name: e,
              //         value: "customer_" + e
              //       }
              //       list.push(obj)
              //     }

              //   })
              // } else {
              if (e) {
                let obj = {
                  name: e,
                  value: "customer_" + e,
                };
                list.push(obj);
              }
            }
            console.log(this.huhao_list);
            this.huhao_list = list;

            break;

          default:
            break;
        }
      }, 200);

      // if (this.loudong_list.includes(e))
    },
    onSelectLoudong(loudong_value) {
      this.form.building_danyuan_id = "";
      this.form.building_unit_id = "";
      if ((loudong_value + "").split("_").length > 1) {
        this.danyuan_list = [];
        this.danyuan_list_back = [];
        this.huhao_list = [];
        this.show_unit = true
        this.checkRepeat();
        return;
      }
      var _current = this.loudong_list.find(
        (item) => item.loudong === loudong_value
      );
      if (_current) {
        this.form.loudongdanwei = _current.loudongdanwei;
        this.current_loudong_id = _current.loudong;
        this.current_loudong_value = _current.value;
        this.current_loudong_name = _current.value
        if (_current.is_unit) {
          this.checkRepeat();
          this.show_unit = true
          this.getUnit(_current.loudong);
        } else {
          this.show_unit = false
          this.getHouseNum();
        }

      }
    },
    getUnit(loudong_id) {
      this.$ajax.house
        .getUnit({ community_id: this.form.community_id, loudong: loudong_id })
        .then((res) => {
          if (res.status === 200) {
            this.danyuan_list = res.data;
            this.danyuan_list_back = res.data;
          }
        });
    },
    onSelectFanghao(e) {
      console.log(e);
      // let _current = this.huhao_list.find(item => item.name == e)
      // if (_current) {

      // }
    },
    // onSelectFanghao(fanghao_value, type) {
    //   var _current = this.huhao_list.find(
    //     (item) => item.value === fanghao_value
    //   )
    //   if (_current) {

    //     this.getUnit(_current.loudong)
    //   }
    //   // if (type) {

    //   // }
    //   this.checkRepeat()
    // },
    onSelectDanyuan(danyuan_value) {
      console.log(this.form);
      this.form.fanghao = "";
      this.form.building_unit_id = "";
      if ((danyuan_value + "").split("_").length > 1) {
        this.huhao_list_back = [];
        this.huhao_list = [];
        this.checkRepeat();
        return;
      }
      var _current = this.danyuan_list.find(
        (item) => item.danyuan === danyuan_value
      );
      if (_current) {
        this.current_danyuan_id = _current.danyuan;
        this.current_danyuan_value = _current.value;
        this.form.danyuandanwei = _current.danyuandanwei;
        this.form.building_danyuan_id = _current.value;
        this.form.total_floor = _current.floor
        this.current_danyuan_name = _current.value
        this.checkRepeat();
        this.getHouseNum(_current.danyuan);
      }
    },
    getHouseNum(danyuan_id) {
      this.$ajax.house
        .getFanghao({
          community_id: this.form.community_id,
          loudong: this.current_loudong_id,
          danyuan: danyuan_id,
        })
        .then((res) => {
          if (res.status === 200) {
            this.huhao_list = res.data;
            this.huhao_list_back = res.data;
          }
        });
    },
    showSelectTable() {
      this.show_select_table = true
    },
    clickLoudong(item) {
      this.form.building_loudong_id = item.loudong
      this.current_loudong_name = item.value
      // this.se_loudong_name = item.name
      this.danyuan_list = []
      this.louceng_list = []
      this.huhao_list = []
      if (item.is_unit) {
        this.show_unit = true
        this.getUnit(item.loudong)
      } else {
        this.show_unit = false
        this.show_louceng = false
        this.getFanghaoList()
      }
    },
    getLouceng(id) {
      this.$ajax.house.getLouceng({ community_id: this.form.community_id, loudong: this.form.building_loudong_id, danyuan: id }).then((res => {
        if (res.status == 200) {
          this.louceng_list = res.data
        }
      }))
    },
    clickUnit(item) {
      console.log(this.form);
      this.form.building_danyuan_id = item.danyuan
      this.current_danyuan_name = item.value
      this.form.total_floor = item.floor
      // this.se_danyuan_name = item.name
      this.louceng_list = []
      this.huhao_list = []
      if (item.floor > 0) {
        this.getLouceng(item.danyuan)
        this.show_louceng = true
      } else {
        this.show_louceng = false
        this.getFanghaoList()
      }
    },
    clickLouceng(item) {
      this.se_louceng = item.value
      // this.se_louceng_name = item.name
      // this.louceng_list = []
      this.huhao_list = []
      this.getFanghaoList(item.value)
    },
    getFanghaoList(id) {
      this.$ajax.house.getFanghaoList({ community_id: this.form.community_id, loudong: this.form.building_loudong_id, danyuan: this.form.building_danyuan_id, floor: id }).then(res => {
        if (res.status == 200) {
          this.huhao_list = res.data
        }
      })
    },
    selectFloor(item) {
      if (item.trade_type > 0 && item.id != this.form.id) {
        this.$confirm("该房源已存在 ", "提示", {
          confirmButtonText: "立即查看",
          cancelButtonText: "重新录入",
          type: "warning",
        })
          .then(() => {
            this.show_select_table = false
            this.$goPath("house_detail?id=" + item.house_id)
          })
          .catch(() => {
            this.se_floor = ''
            // this.form.building_unit_id = ''

          })
        return
      }
      this.se_floor = item.value
      this.form.building_unit_id = item.value
      // this.form.building_danyuan_id = this.se_danyuan
      // this.form.building_loudong_id = this.se_loudong
      this.show_select_table = false
    },
    checkRepeat() {
      if (
        !this.form.building_unit_id ||
        !this.form.building_loudong_id ||
        (this.show_unit && !this.form.building_danyuan_id)
      )
        return;
      let params = { community_id: this.form.community_id, id: this.form.id };
      if (this.form.building_unit_id.split("_").length == 1) {
        //  全是选择的
        params.building_unit_id = this.form.building_unit_id;
      } else {
        console.log(this.form.building_loudong_id, this.current_loudong_value);
        if (this.form.building_loudong_id.split("_").length > 1) {
          params.loudong = this.form.building_loudong_id.split("_")[1];
        } else {
          params.loudong = this.current_loudong_value ? this.current_loudong_value : this.form.building_loudong_id;
        }
        console.log(params.loudong);
        if (this.form.building_danyuan_id.split("_").length > 1) {
          params.danyuan = this.form.building_danyuan_id.split("_")[1];
        } else {
          params.danyuan = this.current_danyuan_value;
        }
        if (this.form.building_unit_id.split("_").length > 1) {
          params.fanghao = this.form.building_unit_id.split("_")[1];
        } else {
          var _current = this.huhao_list.find(
            (item) => item.value === this.this.form.building_unit_id
          );
          if (_current) {
            params.fanghao = _current.value;
          }
        }
      }
      // if (this.fanghao_custom_level) {
      //   params = {
      //     community_id: this.form.community_id,
      //     loudong: this.form.loudong,
      //     loudongdanwei: this.form.loudongdanwei,
      //     danyuan: this.form.danyuan,
      //     danyuandanwei: this.form.danyuandanwei,
      //     menpaihao: this.form.fanghao,
      //   }
      // } else if (this.form.building_unit_id) {
      //   params = {
      //     community_id: this.form.community_id,
      //     building_unit_id: this.form.building_unit_id,
      //   }
      // }
      params.id = this.form.id
      params.manual = this.noGetLong ? 1 : 0
      this.$ajax.house.checkRepeat(params).then((res) => {
        console.log(res);
        if (res.status == 200 && res.data.status == 1) {
          this.$confirm("该房源已存在 ", "提示", {
            confirmButtonText: "立即查看",
            cancelButtonText: "重新录入",
            type: "warning",
          })
            .then(() => {
              this.$goPath("house_detail?id=" + res.data.id);
            })
            .catch(() => {
              this.form.building_unit_id = "";
            });
          // this.$message.error('该房源已存在 请重新选择')
          // this.form.building_unit_id = ''
        }

        // if (res.data.status === 200) {
        //   this.$message.success(res.data.message)
        // }
        // if (res.data.status === 209) {
        //   this.$confirm('该房源已经存在，您可以去跟进联系', '提示', {
        //     confirmButtonText: '去跟进',
        //     cancelButtonText: this.form.exclusive
        //       ? '取消独家'
        //       : this.form.is_repeat
        //         ? '取消查重'
        //         : '取消',
        //     type: 'warning',
        //   })
        //     .then(() => {
        //       this.form.exclusive = 0
        //       this.form.is_repeat = ''
        //       // console.log(this.$parent)
        //       this.$parent.current_info_id = res.data.data.id
        //       this.$parent.show_detail = true
        //       // this.$router.push({
        //       //   name: 'LmList',
        //       //   query: {
        //       //     id: res.data.data.id,
        //       //   },
        //       // })
        //       console.log('去详情页')
        //     })
        //     .catch(() => {
        //       // 取消则恢复成非独家且不查重查重
        //       this.form.exclusive = 0
        //       this.form.is_repeat = ''
        //       console.log('已取消')
        //     })
        // } else if (res.data.status !== 200) {
        //   this.form.exclusive = ''
        //   this.form.is_repeat = ''
        // } else if (res.data.status === 200) {
        //   this.form.is_repeat = 1
        // }
      });
    },
    handleSuccess(e) {
      this.community_list = [e]
      this.form.community_id = e.id
      this.form.status = 2;
      this.show_apply_community = false
    },

    changeoptions() {
      this.id = this.form.usage_type_id
      if (this.id == 4 && this.form.trade_type == 3 || this.id == 8 && this.form.trade_type == 3) {
        this.radiotrade_type = true
      } else {
        this.radiotrade_type = false
      }
      console.log(this.id, this.form.usage_type_id, this.radiotrade_type, "asddddddddddd")
    },
    tradeType() {
      this.id = this.form.usage_type_id
      if (this.id == 4 && this.form.trade_type == 3 || this.id == 8 && this.form.trade_type == 3) {
        this.radiotrade_type = true
      } else {
        this.radiotrade_type = false
      }
      console.log(this.radiotrade_type, 'radiotrade_type')
      console.log(this.form.usage_type_id, 'chuancan')
      this.$http.getformrediotype(this.id).then((res) => {
        this.options_tradeType = res.data
        console.log(this.options_tradeType, "11111111111111")
      })
    },
  },
  // destroyed() {
  //   eventBus.$off('qqMapOnLoad')
  // },
};
</script>
  
<style scoped lang="scss">
.el-form {
  position: relative;
}

.form_block {
  >.title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;
  }

  .el-form-item {
    color: #8a929f;

    .tip {
      color: #fe6c17;
    }

    .upload-box {
      overflow: hidden;
      margin: 0 8px 8px 0;
      display: inline-block;
      vertical-align: middle;
    }

    ::v-deep>.el-input {
      width: 240px;
    }

    ::v-deep.el-form-item__label {
      font-weight: bold;
      color: #2e3c4e;
    }

    .img_item {
      margin: 0 8px 8px 0;
      display: inline-block;
      vertical-align: top;
      line-height: 1;
    }

    ::v-deep .el-upload-list__item {
      width: 120px;
      height: 120px;
      position: relative;
      margin: 0;

      .remark_img {
        padding: 0 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        height: 28px;
        width: 80%;
        border-radius: 6px;
        color: #fff;
        background-color: rgba($color: #000000, $alpha: 0.5);
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;

        .el-input {
          .el-input__inner {
            padding: 0 3px;
            background: none;
            border: none;
            color: #fff;
          }
        }
      }

      &:hover {
        .remark_img {
          opacity: 1;
        }
      }
    }

    .el-upload-list--picture-card {
      display: block;

      .el-upload-list__item-thumbnail {
        object-fit: cover;
      }
    }

    ::v-deep.el-upload {
      width: 120px;
      height: 120px;
      line-height: 1.5;

      .upload_placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: 12px;
        color: #2d84fb;

        i {
          color: #2d84fb;
        }
      }

      .upload_image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    ::v-deep.el-textarea textarea {
      resize: none;
    }
  }
}

.img_options {
  display: flex;
  justify-content: space-between;

  >span {
    padding: 10px 0;
    cursor: pointer;
  }

  .set_cover {
    color: #2d84fb;
  }
}

.add_community {
  color: #2d84fb;
}

.map-box {
  position: relative;

  .map-container {
    height: 500px;
  }

  .res_list {
    position: absolute;
    width: 100%;
    top: 40px;
    max-height: 400px;
    overflow-x: hidden;
    background-color: #fff;
    z-index: 10;
    box-shadow: 0 5px 5px #dedede;
    padding: 5px 0;

    .res_item {
      cursor: pointer;
      padding: 8px 5px;
      border-bottom: 1px solid #dedede;

      .title {
        font-size: 16px;
        color: #333;
      }

      .address {
        color: #666;
      }
    }
  }
}

.form-item-block {
  display: inline-block;
  vertical-align: top;

  .tip {
    padding-left: 10px;
    margin-top: 5px;
    line-height: 1;
  }
}

.el-form-item {
  color: #8a929f;

  .tip {
    color: #fe6c17;
  }

  .unit {
    margin-left: 12px;
  }

  ::v-deep .el-form-item__label {
    font-weight: bold;
    color: #2e3c4e;
  }

  // ::v-deep .el-input {
  //   width: 240px;
  // }
  ::v-deep .el-textarea textarea {
    resize: none;
  }

  .el-date-editor {
    width: 240px;
    // &:first-child {
    //   margin-right: 6px;
    //   width: 130px;
    // }
  }
}

.copy {
  display: inline-block;
  vertical-align: top;
  position: absolute;
  top: 48px;
  left: 700px;
  margin-left: 50px;
}

.sel_container {
  .top {
    padding: 10px 0;
  }

  .top_right {
    padding: 8px;
  }

  .sale_b {
    background: #fda148;
    border-radius: 4px;
    color: #fff;
    padding: 5px 15px;
    margin-right: 10px;
  }

  .rent_b {
    background: #f74c4c;
    border-radius: 4px;
    color: #fff;
    padding: 5px 15px;
    margin-right: 10px;
  }

  .sale_rent_b {
    background: #1cd300;
    border-radius: 4px;
    color: #fff;
    padding: 5px 15px;
    margin-right: 10px;
  }
}

.select_box {
  .se_ld {
    text-align: center;

    border: 1px solid #e6e7ea;

    .se_container {
      height: 500px;
      min-width: 117px;
      background: #ffffff;
      overflow-y: auto;
      overflow-x: hidden;

      &.se_th {
        height: 39px;
      }

      .se_box {
        position: relative;
        background: #fff;
        height: 100%;
        width: 100px;
        box-sizing: border-box;

        .shangsanjiao {
          height: 0;
          width: 0;
          z-index: 1;
          position: absolute;
          top: 3px;
          right: -14px;
          border: 5px solid transparent;
          border-bottom: 5px solid #a1a1a1;
        }

        .xiasanjiao {
          height: 0;
          width: 0;
          z-index: 1;
          position: absolute;
          bottom: 3px;
          right: -14px;
          border: 5px solid transparent;
          border-top: 5px solid #a1a1a1;
        }
      }
    }

    .se_tr {
      &.fanghao {
        .se_box {
          width: 100%;
          flex-wrap: wrap;

          .se_item {
            margin: 6px 12px;
            padding: 10px 20px;

            &.sale {
              background: #fda1481a;
              color: #fda148;
            }

            &.rent {
              background: #f74c4c1a;
              color: #f74c4c;
            }

            &.sale_rent {
              background: #1cd3001a;
              color: #1cd300;
            }
          }

          // justify-content: space-between;
        }
      }

      .se_box {
        padding: 6px 12px;

        .se_item {
          background: rgba(27, 93, 255, 0.1);
          border-radius: 4px;
          border-radius: 4px;
          color: #1b5dff;
          margin: 6px 0;

          &.active {
            background: #1b5dff;
            box-shadow: 2px 0px 6px 0px rgba(35, 90, 223, 0.4);
            color: #fff;
          }
        }
      }
    }

    +.se_ld {
      border-left: 0;
    }

    .row {
      // 横排
      padding: 10px;
      flex-wrap: wrap;

      .se_item {

        // &:nth-child(2n) {
        //   background: #fff;
        // }
        &.active {
          // background: #fff !important;
          border: 1px solid #bdcfe6;
          border-radius: 4px;
        }
      }
    }

    .se_item {
      padding: 10px;
      z-index: 2;
      cursor: pointer;

      &.se_th {
        background: #f8f8f9;
        box-sizing: border-box;

        .se_container {
          height: 39px;
        }
      }

      &.active {
        // background: #c8e9ff !important;
      }

      &:nth-child(2n) {
        background: #f8f8f9;
      }
    }
  }
}

::v-deep .el-link.is-underline:hover:after {
  content: none;
}
</style>
  