<template>
  <div class="portrait div row">
    <div class="pl">
      <div class="pl-list" v-if="l_list.cname">
        <div class="title flex-row align-center">
          <div class="title_name flex-1">录入人</div>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.cname" class="icon">
              {{ l_list.cname && l_list.cname[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r">
            <div class="img-r-naem">
              {{ l_list.cname && l_list.cname }}
            </div>
          </div>
        </div>
        <div class="time">{{ l_list.ctime }}</div>
      </div>
      <div class="pl-list">
        <div class="title flex-row align-center">
          <div class="title_name">维护人</div>
          <el-popover placement="top-start" width="180" trigger="hover">
            <span>{{ phone_number }}</span>
            <el-button size="mini" type="success" @click="copyPhone" style="margin-left: 20px">复制</el-button>
            <div class="numberTop flex-row align-center" slot="reference">
              <i class="el-icon-phone" ref="tel" style="font-size: 14px; color: #48ea0a" />
            </div>
          </el-popover>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.whr && l_list.whr.cname" class="icon">
              {{ l_list.whr && l_list.whr.cname && l_list.whr.cname[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r">
            <div class="img-r-naem">
              {{ (l_list.whr && l_list.whr.cname) || "--" }}
            </div>
            <div class="img-r-dep">{{ (l_list.whr && l_list.whr.department) || "--" }}</div>
          </div>
        </div>
        <div class="time">{{ l_list.whr && l_list.whr.ctime }}</div>
      </div>
      <div class="pl-list">
        <div class="title flex-row align-center">
          <div class="title_name flex-1">委托人</div>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.wtr && l_list.wtr.cname" class="icon">
              {{ l_list.wtr && l_list.wtr.cname && l_list.wtr.cname[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r">
            <div class="img-r-naem">
              {{ (l_list.wtr && l_list.wtr.cname) || "--" }}
            </div>
            <div class="img-r-dep">{{ (l_list.wtr && l_list.wtr.department) || "--" }}</div>
          </div>
        </div>
        <div class="time">{{ l_list.wtr && l_list.wtr.ctime }}</div>
      </div>
      <div class="pl-list">
        <div class="title flex-row align-center">
          <div class="title_name flex-1">VIP</div>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.djwtr && l_list.djwtr.cname" class="icon">
              {{ l_list.djwtr && l_list.djwtr.cname && l_list.djwtr.cname[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r">
            <div class="img-r-naem">
              {{ (l_list.djwtr && l_list.djwtr.cname) || "--" }}
            </div>
            <div class="img-r-dep">{{ (l_list.skr && l_list.djwtr.department) || "--" }}</div>
          </div>
        </div>
        <div class="time">{{ l_list.djwtr && l_list.djwtr.ctime }}</div>
      </div>
      <div class="pl-list">
        <div class="title flex-row align-center">
          <div class="title_name flex-1">实勘人</div>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.skr && l_list.skr.cname" class="icon">
              {{ l_list.skr && l_list.skr.cname && l_list.skr.cname[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r">
            <div class="img-r-naem">
              {{ (l_list.skr && l_list.skr.cname) || "--" }}
            </div>
            <div class="img-r-dep">{{ (l_list.skr && l_list.skr.department) || "--" }}</div>
          </div>
        </div>
        <div class="time">{{ l_list.skr && l_list.skr.ctime }}</div>
      </div>
      <div class="pl-list">
        <div class="title flex-row align-center">
          <div class="title_name flex-1">钥匙</div>
        </div>
        <div class="pl-con div row align-center">
          <div class="img">
            <div v-if="l_list.ysr && l_list.ysr.cname" class="icon">
              {{ l_list.ysr && l_list.ysr.cname && l_list.ysr.cname[0] }}
            </div>
            <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
          </div>
          <div class="img-r">
            <div class="img-r-naem">
              {{ (l_list.ysr && l_list.ysr.cname) || "--" }}
            </div>
            <div class="img-r-dep">{{ (l_list.ysr && l_list.ysr.department) || "--" }}</div>
          </div>
        </div>
        <div class="time">{{ l_list.ysr && l_list.ysr.ctime }}</div>
      </div>
    </div>
    <div class="pr">
      <div class="pr_top" v-if="l_list.price_change && l_list.price_change.num > 0">
        <div class="pr_label_item flex-row align-center">
          <div v-if="l_list.cj_type && l_list.cj_type <= 0" class="pr_label flex-row align-center">
            <div class="img">
              <img src="@/assets/icon/jiang.png" alt="" />
            </div>
            <div class="pr_label_name">
              近期业主调价{{ l_list.price_change.num }}次
              {{ l_list.price_change.direct_desc }}
              {{ l_list.price_change.range | formatPrice(l_list.trade_type) }}
            </div>
          </div>
          <div v-if="l_list.cj_type && l_list.cj_type > 0" class="pr_label flex-row align-center">
            <div class="img">
              <img src="@/assets/icon/jiang.png" alt="" />
            </div>
            <div class="pr_label_name">
              挂牌价{{ parseFloat(l_list.sale_price_original / 10000) }}万元，
              {{ l_list.cj_time_show | formConverDate(l_list.cj_time_show) }}成交
            </div>
          </div>
          <div class="pr_label_right flex-row flex-1"></div>
        </div>
      </div>
      <div class="pr_center">
        <div class="price flex-row">
          <template v-if="l_list.trade_type == 1 || l_list.trade_type == 3">
            <div class="price_total flex-row">
              <span v-if="l_list.cj_type && l_list.cj_type" class="deal">成交价</span>
              <span class="price"> {{ l_list.sale_price / 10000 }} </span>
              <span class="unit"> 万 </span>
              <template v-if="l_list.price_change && l_list.price_change.num > 0">
                <span :class="l_list.price_change.direct == 1 ? 'up' : 'down'">
                  {{ l_list.price_change.direct == 1 ? "↑" : "↓" }}
                </span>
              </template>
            </div>
            <div class="adv_price">
              <span class="price"> {{ l_list.danjia }} </span>
              <span class="unit"> 元/m² </span>
            </div>
          </template>
          <!-- :class="{
                price_total: l_list.trade_type == 2,
                adv_price: l_list.trade_type == 3,
              }" -->
          <template v-if="l_list.trade_type == 2 || l_list.trade_type == 3">
            <div class="price_total flex-row" :class="{
                            marleft5: l_list.trade_type == 3,
                          }">
              <span class="unit" v-if="l_list.trade_type == 3" style="color: #8a929f; margin: 0 5px">
                租
              </span>
              <span class="price"> {{ l_list.rent_price }} </span>
              <span class="unit">元/月</span>
              <!-- <template
                v-if="l_list.price_change && l_list.price_change.num > 0"
              >
                <span :class="l_list.price_change.direct == 1 ? 'up' : 'down'">
                  {{ l_list.price_change.direct == 1 ? "↑" : "↓" }}
                </span>
              </template> -->
            </div>
          </template>
          <div v-if="l_list.cj_type && l_list.cj_type" class="price_total flex-row">
            <span class="hangCard">挂牌价</span>
            <span class="price"> {{ parseFloat(l_list.sale_price_original / 10000) }} </span>
            <span class="unit"> 万 </span>
          </div>
        </div>
      </div>
      <div class="pr_block flex-box">
        <!-- ======= -->
        <div v-if="l_list.cj_type > 0" style="margin-bottom: 24px;" class="pr_block_con flex-row align-center">
          <div class="pr_block_con_item flex-1 div">
            <div class="pr_block_con_item_top">
              {{ l_list.cj_days }}天
            </div>
            <div class="pr_block_con_item_bottom">成交周期</div>
          </div>
          <div class="pr_block_con_item flex-1 border div">
            <div class="pr_block_con_item_top">{{ l_list.cj_price_time }}次</div>
            <div class="pr_block_con_item_bottom">调价</div>
          </div>
          <div class="pr_block_con_item flex-1 div">
            <div class="pr_block_con_item_top">{{ l_list.cj_follow_take }}次</div>
            <div class="pr_block_con_item_bottom">带看</div>
          </div>
        </div>
        <!-- ======= -->
        <div class="pr_block_con flex-row align-center">
          <div class="pr_block_con_item flex-1 div">
            <div>
              <div class="pr_block_con_item_top" v-if="l_list.shi != 0 || l_list.ting != 0 || l_list.wei != 0">
                {{ l_list.shi }}室{{ l_list.ting }}厅{{ l_list.wei }}卫
              </div>
              <div v-if="l_list.shi == 0 || l_list.ting == 0 || l_list.wei == 0">
                {{ l_list.usage_type }}
              </div>
            </div>
            <div class="pr_block_con_item_bottom">户型</div>
          </div>
          <div class="pr_block_con_item flex-1 border div">
            <div class="pr_block_con_item_top">{{ l_list.mianji }}平米</div>
            <div class="pr_block_con_item_bottom">面积</div>
          </div>
          <div class="pr_block_con_item flex-1 div">
            <div class="pr_block_con_item_top">{{ l_list.zhuangxiu }}</div>
            <div class="pr_block_con_item_bottom">装修</div>
          </div>
        </div>
      </div>
      <div class="pr_house_info">
        <div class="info_title">基本属性</div>
        <div class="pr_house_info_con flex-row flex-wrap align-center">
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">编号</div>
            <div class="value">{{ l_list.id }}</div>
          </div>
          <!-- <div class="pr_house_info_item flex-row align-center">
            <div class="label">小区名称</div>
            <div class="value">{{ l_list.title }}</div>
          </div> -->
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">楼层</div>
            <div class="value">
              {{ l_list.sz_floor }}/{{ l_list.total_floor }}层
            </div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">朝向</div>
            <div class="value">{{ l_list.chaoxiang }}</div>
          </div>
          <!-- <div class="pr_house_info_item flex-row align-center">
            <div class="label">户型</div>
            <div class="value">
              {{ l_list.shi }}室{{ l_list.ting }}厅{{ l_list.wei }}卫
            </div>
          </div> -->
          <!-- <div class="pr_house_info_item flex-row align-center">
            <div class="label">厅</div>
            <div class="value">{{ l_list.ting }}</div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">卫</div>
            <div class="value">{{ l_list.wei }}</div>
          </div> -->

          <div class="pr_house_info_item flex-row align-center">
            <div class="label">厨房</div>
            <div class="value">{{ l_list.kitchen }}</div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">阳台</div>
            <div class="value">{{ l_list.balcony }}</div>
          </div>
          <!-- <div class="pr_house_info_item flex-row align-center">
            <div class="label">面积</div>
            <div class="value">{{ l_list.mianji }}</div>
          </div> -->
          <!-- <div class="pr_house_info_item flex-row align-center">
            <div class="label">装修</div>
            <div class="value">{{ l_list.zhuangxiu }}</div>
          </div> -->

          <div class="pr_house_info_item flex-row align-center">
            <div class="label">区域</div>
            <div class="value">{{ l_list.area_name }}</div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">商圈</div>
            <div class="value" v-if="l_list.region_name != null">{{ l_list.region_name }}</div>
            <div class="value" v-else>--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">用途</div>
            <div class="value">{{ l_list.usage_type }}</div>
          </div>
          <!-- 新增 -->
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">户型结构</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.house_structure_show }}</div>
            <div class="value" v-if="!l_list.house_info.house_structure_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">建筑类型</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.building_type_show }}</div>
            <div class="value" v-if="!l_list.house_info.building_type_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">建筑结构</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.building_structure_show }}</div>
            <div class="value" v-if="!l_list.house_info.building_structure_show">--</div>
          </div>
          <!-- 房源有电梯执行 -->
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.house_info&&l_list.house_info.elevator==1">
            <div class="label">梯户比例</div>
            <div class="value">
              {{ l_list.house_info&&l_list.house_info.elevator_show + ' ' +
                            l_list.house_info.ladder+'梯'+l_list.house_info.households+'户' }}
            </div>
          </div>
          <!-- 房源无电梯执行 -->
          <div class="pr_house_info_item flex-row align-center"
            v-else-if="l_list.house_info&&l_list.house_info.elevator==0">
            <div class="label">电梯</div>
            <div class="value">{{ l_list.house_info&&l_list.house_info.elevator_show }}</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-else-if="!l_list.house_info.elevator_show">
            <div class="label">电梯</div>
            <div class="value">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">供暖方式</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.heating_type_show }}</div>
            <div class="value" v-if="!l_list.house_info.heating_type_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 1">
            <div class="label">入住时间</div>
            <div class="value" v-if="l_list.house_info.check_in == 0">{{l_list.house_info&&
                          l_list.house_info.check_in_time }}</div>
            <div class="value" v-if="l_list.house_info.check_in == 1">随时入住</div>
            <div class="value" v-if="l_list.house_info.check_in == 0 && !l_list.house_info.check_in_time">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center">
            <div class="label">车位</div>
            <div class="value" v-if="l_list.house_info.parking_check_show">{{l_list.house_info&&
                          l_list.house_info.parking_check_show }}</div>
            <div class="value" v-if="!l_list.house_info.parking_check_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 1">
            <div class="label">用水</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.water_show }}</div>
            <div class="value" v-if="!l_list.house_info.water_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 1">
            <div class="label">用电</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.electricity_show }}</div>
            <div class="value" v-if="!l_list.house_info.electricity_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 1">
            <div class="label">燃气</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.gas_show }}</div>
            <div class="value" v-if="!l_list.house_info.gas_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 1">
            <div class="label">租期</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.term_show }}</div>
            <div class="value" v-if="!l_list.house_info.term_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 1">
            <div class="label">看房</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.see_time_show }}</div>
            <div class="value" v-if="!l_list.house_info.see_time_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.usage_type_id == 4">
            <div class="label">当前状态</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.business_status_show }}</div>
            <div class="value" v-if="!l_list.house_info.business_status_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.usage_type_id == 4">
            <div class="label">经营行业</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.business_trades_show }}</div>
            <div class="value" v-if="!l_list.house_info.business_trades_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.usage_type_id == 4">
            <div class="label">经营行业</div>
            <div class="value">{{l_list.house_info&& l_list.house_info.business_type_show }}</div>
            <div class="value" v-if="!l_list.house_info.business_type_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.usage_type_id == 4">
            <div class="label">客流人群</div>
            <el-tooltip class="item" effect="dark" :content="l_list.house_info.business_customer_show"
              placement="top-start">
              <div style="width: 150px;" class="value">{{l_list.house_info&& l_list.house_info.business_customer_show }}
              </div>
            </el-tooltip>
            <div class="value" v-if="!l_list.house_info.business_customer_show">--</div>
          </div>
          <div v-if="l_list.is_house_code == 1" class="pr_house_info_item flex-row align-center">
            <div class="label">房源核验编码</div>
            <div class="value" style="width: 70px;" v-if="l_list.house_info && l_list.house_info.house_code">
              {{l_list.house_info.house_code}}
            </div>
            <div class="value" style="width: 70px;" v-if="l_list.house_info && !l_list.house_info.house_code">
              --
            </div>
            <el-link type="primary" @click="copyHouseCode" v-if="l_list.house_info && l_list.house_info.house_code">
              复制
            </el-link>
          </div>
        </div>
      </div>
      <div class="pr_house_transaction" v-if="l_list.trade_type != 2">
        <div class="info_title">交易属性</div>
        <div class="pr_house_info_con flex-row flex-wrap align-center">
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">挂牌时间</div>
            <div class="value">{{ l_list.house_info&&l_list.house_info.first_upload_at_show }}</div>
            <div class="value" v-if="!l_list.house_info.first_upload_at_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">交易权属</div>
            <div class="value">{{l_list.house_info&&l_list.house_info.ownership_show }}</div>
            <div class="value" v-if="!l_list.house_info.ownership_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">房屋年限</div>
            <div class="value">{{ l_list.house_info&&l_list.house_info.property_certificate_period_show }}</div>
            <div class="value" v-if="!l_list.house_info.property_certificate_period_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">上次交易</div>
            <div class="value">{{ l_list.house_info&&l_list.house_info.last_trade_at_show }}</div>
            <div class="value" v-if="!l_list.house_info.last_trade_at_show">--</div>
          </div>
          <div class="pr_house_info_item flex-row align-center" v-if="l_list.trade_type != 2">
            <div class="label">产权所属</div>
            <div class="value">{{ l_list.house_info&&l_list.house_info.property_rights_show }}</div>
            <div class="value" v-if="!l_list.house_info.property_rights_show">--</div>
          </div>
          <!-- 抵押信息-无抵押执行 -->
          <div v-if="l_list.house_info&&l_list.house_info.mortgage==2&&l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">抵押信息</div>
            <div class="value">{{ l_list.house_info.mortgage_show }}</div>
          </div>
          <!-- 抵押信息-有抵押执行 -->
          <div v-else-if="l_list.house_info&&l_list.house_info.mortgage==1&&l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">抵押信息</div>
            <div class="value">{{ l_list.house_info.mortgage_show }}</div>
            <span v-if="l_list.house_info.mortgage_price > 0"
              style="margin-left: 5px;">{{l_list.house_info.mortgage_price}}万元</span>
          </div>
          <!-- 当老房源house_info为空时执行 -->
          <div v-else-if="l_list.house_info&&!l_list.house_info.mortgage&&l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">抵押信息</div>
            <div class="value">--</div>
          </div>
          <div v-if="l_list.house_info && l_list.house_info.right_period && l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">产权年限</div>
            <div class="value">{{ l_list.house_info.right_period_show }}</div>
          </div>
          <div v-if="l_list.house_info && !l_list.house_info.right_period && l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">产权年限</div>
            <div class="value">--</div>
          </div>
          <div v-if="l_list.house_info && l_list.house_info.built_year != '' && l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">建造年代</div>
            <div class="value">{{ l_list.house_info.built_year }}</div>
          </div>
          <div v-if="l_list.house_info && l_list.trade_type != 2 && l_list.house_info.built_year == ''"
            class="pr_house_info_item flex-row align-center">
            <div class="label">建造年代</div>
            <div class="value">--</div>
          </div>
          <div v-if="l_list.house_info&&l_list.house_info.house_certificate==1&&l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">房本备件</div>
            <div class="value">已上传房本照片</div>
          </div>
          <div v-else-if="l_list.house_info&&l_list.house_info.house_certificate==0&&l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">房本备件</div>
            <div class="value">未上传房本照片</div>
          </div>
          <div v-else-if="l_list.house_info&&!l_list.house_info.house_certificate&&l_list.trade_type != 2"
            class="pr_house_info_item flex-row align-center">
            <div class="label">房本备件</div>
            <div class="value">--</div>
          </div>
        </div>
      </div>
      <div class="pr_facility">
        <div class="info_title">配套设施</div>
        <div class="pr_facilityMain" v-if="l_list.usage_type_id != 4">
          <div v-for="(item, index) in rentSale_list" :key="index">
            <div class="pr_facilityBox"
              v-if="l_list.house_info.facilities_show && l_list.house_info.facilities_show.indexOf(item.label) != -1">
              <i :class="item.icon"></i>
              <div class="facility_title">{{ item.label }}</div>
            </div>
            <div class="pr_facilityBox eliminate" v-else>
              <i :class="item.icon"></i>
              <div class="facility_title">{{ item.label }}</div>
            </div>
          </div>
        </div>
        <div class="pr_facilityMain" v-else>
          <div v-for="(item, index) in facility_list" :key="index">
            <div class="pr_facilityBox"
              v-if="l_list.house_info.facilities_show && l_list.house_info.facilities_show.indexOf(item.label) != -1">
              <i :class="item.icon"></i>
              <div class="facility_title">{{ item.label }}</div>
            </div>
            <div class="pr_facilityBox eliminate" v-else>
              <i :class="item.icon"></i>
              <div class="facility_title">{{ item.label }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="pr ma0">
        <div class="title div blable row">
          <div style="color: #2e3c4e;">房源标签</div>
          <div class="btn" @click="
                        () => {
                          $emit('onClickBtn');
                        }
                      ">
            更新标签
          </div>
        </div>
        <div class="label-box">
          <span class="label-item" v-for="(item, index) in label" :key="index">
            {{ item }}
          </span>
          <div v-if="label.length === 0">
            <myEmpty></myEmpty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import myEmpty from "@/components/components/my_empty.vue";
export default {
  components: {
    myEmpty,
  },
  data() {
    return {
      list:{},
      facility_list: [
        {label: '客梯', icon: 'el-icon-newfonta-elevator2x'},
        {label: '货梯', icon: 'el-icon-newfonta-cargoElevator2x'},
        {label: '中央空调', icon: 'el-icon-newfonta-centralAir2x'},
        {label: '停车位', icon: 'el-icon-newfonta-parking2x'},
        {label: '天然气', icon: 'el-icon-newfonta-gas2x'},
        {label: '电话/网络', icon: 'el-icon-newfonta-broadband2x'},
        {label: '暖气', icon: 'el-icon-newfonta-heating2x'},
        {label: '扶梯', icon: 'el-icon-newfonta-escalator2x'},
        {label: '上水', icon: 'el-icon-newfonta-upWater2x'},
        {label: '下水', icon: 'el-icon-newfonta-downWater2x'},
        {label: '排烟', icon: 'el-icon-newfonta-smoke2x'},
        {label: '排污', icon: 'el-icon-newfonta-pollution2x'},
        {label: '管煤', icon: 'el-icon-newfonta-coalTube2x'},
        {label: '380V', icon: 'el-icon-newfonta-330V2x'},
        {label: '可明火', icon: 'el-icon-newfonta-flame2x'},
        {label: '外摆区', icon: 'el-icon-newfonta-place2x'},
      ],
      rentSale_list: [
        {label: '洗衣机', icon: 'el-icon-newfonta-washing2x'},
        {label: '空调', icon: 'el-icon-newfonta-air2x'},
        {label: '衣柜', icon: 'el-icon-newfonta-wardrobe2x'},
        {label: '电视', icon: 'el-icon-newfonta-television2x'},
        {label: '冰箱', icon: 'el-icon-newfonta-refrigerator2x'},
        {label: '热水器', icon: 'el-icon-newfonta-waterHeater2x'},
        {label: '床', icon: 'el-icon-newfonta-bed2x'},
        {label: '暖气', icon: 'el-icon-newfonta-heating2x'},
        {label: '宽带', icon: 'el-icon-newfonta-broadband2x'},
        {label: '天然气', icon: 'el-icon-newfonta-gas2x'},
      ]
    };
  },
  filters: {
    formatPrice(val, type) {
      console.log(val, type);
      let unit = "元"

      if (Number(val) >= 10000) {
        if (type == 1 || type == 3) {
          unit = "万元"
        } else {
          unit = "万元/月"
        }
        return Number(val) / 10000 + unit
      } else {
        if (type == 1 || type == 3) {
          unit = "元"
        } else {
          unit = "元/月"
        }
        return val + unit
      }
    },
    // 2020-05-11转换2020年5月11日
    formConverDate(val) {
      const date = new Date(val);
      const year = date.getFullYear(); // 获取年份
      const month = date.getMonth() + 1; // 获取月份
      const day = date.getDate(); // 获取日期
      const formattedDate = `${year}年${month}月${day}日`; // 转换格式
      return formattedDate;
    }
  },
  props: {
    l_list: {
      type: Object,
      default: ()=>{
        return {
          house_info:{}
        }
      },
    },
    label: {
      type: Array,
      default: () => [],
    },
    phone_number: {
      type: String,
    }


  },
  watch:{
    l_list:{
      handler(val){
        // 如果l_list有值不为空或undefined
        if(val.house_info.id && val.house_info.id != undefined) {
          // 如果抵押金额 小数点后两位为0，则转换为整数
          if(this.l_list.house_info.mortgage_price[3]==0 && this.l_list.house_info.mortgage_price[4]==0) {
            this.l_list.house_info.mortgage_price = parseInt(this.l_list.house_info.mortgage_price);
          }
          // 将数字转中文数字
          this.l_list.house_info.ladder = this.toChinesNum(val.house_info.ladder);
          this.l_list.house_info.households = this.toChinesNum(val.house_info.households);
        }
      },
      immediate:true
    }
  },
  created() {
  },
  methods: {
    //复制维护人手机号
    copyPhone() {
      this.$onCopyValue(this.phone_number)
    },
    // 复制房源核验编码
    copyHouseCode() {
      this.$onCopyValue(this.l_list.house_info.house_code)
    },
    //数字转中文数字
    toChinesNum(num) {
      var chineseNum = "";
      var chineseUnit = ["", "十", "百", "千", "万", "亿"];
      var chineseDigit = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
      var numStr = num.toString();
      var len = numStr.length;
      var zeroFlag = true; // 是否在连续的零中
      for (var i = 0; i < len; i++) {
        var digit = parseInt(numStr[i]);
        var unit = len - i - 1;
        // 处理连续的零
        if (digit === 0) {
          if (!zeroFlag && chineseNum !== "") {
            chineseNum += chineseDigit[digit];
            zeroFlag = true;
          }
          continue;
        }
        zeroFlag = false;
        chineseNum += chineseDigit[digit];
        // 十位及以上需要加上单位
        if (unit > 0) {
          chineseNum += chineseUnit[unit];
        }
      }
      // 处理结尾连续的零
      if (zeroFlag) {
        chineseNum += chineseDigit[0];
      }
      return chineseNum;
    }
  },
};
</script>

<style scoped lang="scss">
.portrait {
  .pl {
    border: 1px solid rgba(221, 225, 233, 1);
    border-radius: 4px;
    padding: 24px;
    width: 130px;
    min-width: 100px;
    font-size: 14px;

    .pl-list {
      margin-bottom: 24px;
      border-bottom: 1px solid #f1f4fa;
      padding-bottom: 23px;

      .title {
        color: #2e3c4e;
        margin-bottom: 12px;
        font-size: 12px;
        color: #8a929f;

        .title_name {
          font-size: 14px;
          color: #2e3c4e;
          // font-weight: 600;
        }
      }

      .pl-con {
        .img {
          min-width: 32px;
          height: 32px;

          .icon {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: #2d84fb;
            color: #fff;
            font-size: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        img {
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }

        .img-r {
          margin-left: 12px;
          color: #8a929f;

          .img-r-dep {
            margin-top: 8px;
            font-size: 12px;
          }
        }
      }

      .time {
        font-size: 12px;
        color: #8a929f;
        margin-top: 10px;
      }
    }
  }

  .pr {
    width: 100%;
    margin-left: 24px;

    .pr_top {
      .pr_label {
        margin-bottom: 12px;
        background: #fff1f5;
        color: #fb656a;
        font-size: 11px;
        align-items: center;
        height: 24px;
        // width: 252px;
        white-space: nowrap;
        padding: 0 8px 0 4px;
        margin-top: 10px;
        margin-right: 10px;
        border-radius: 2px;
        font-weight: 700;

        .img {
          width: 14px;
          height: 14px;
          margin-right: 12px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .pr_label_name {
          color: #fb656a;
          font-size: 12px;
        }
      }
    }

    .pr_center {
      margin-top: 10px;
      padding-left: 20px;
      padding-bottom: 24px;
      border-bottom: 1px solid #dde1e9;

      .price {
        align-items: baseline;

        .price_total {
          color: #fe6c17;
          align-items: baseline;

          &.marleft5 {
            margin-left: 5px;
          }

          .deal {
            font-size: 20px;
            color: #8a929f;
          }

          .hangCard {
            font-size: 20px;
            color: #8a929f;
            margin-left: 24px;
          }

          .price {
            font-size: 38px;
          }

          .unit {
            font-size: 20px;
          }

          .up {
            font-weight: 600;
          }

          .down {
            font-weight: 600;
            color: #00caa7;
          }
        }

        .adv_price {
          margin-left: 24px;
          font-size: 20px;
          color: #8a929f;
        }
      }
    }

    .pr_block {
      padding: 40px 0;
      border-bottom: 1px solid #dde1e9;

      .pr_block_con {
        .pr_block_con_item {
          &.boder {
            border-left: 2px solid #dde1e9;
            border-right: 2px solid #dde1e9;
          }

          .pr_block_con_item_top {
            font-size: 18px;
            color: #333333;
            // font-weight: 600;
          }

          .pr_block_con_item_bottom {
            font-size: 14px;
            color: #8a929f;
            margin-top: 10px;
          }
        }
      }
    }

    .pr_house_info {
      margin: 24px 0;
      border-bottom: 1px solid #dde1e9;

      botto .title {
        font-size: 18px;
        color: #2e3c4e;
        font-weight: 600;
        margin-bottom: 18px;
      }

      .pr_house_info_con {
        flex-wrap: wrap;

        .pr_house_info_item {
          width: 33%;
          font-size: 14px;
          color: #8a929f;
          padding: 6px 0;
          margin-bottom: 12px;

          .label {
            min-width: 56px;
            margin-right: 16px;
          }

          .value {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .pr_house_transaction {
      margin: 24px 0;
      border-bottom: 1px solid #dde1e9;

      .pr_house_info_con {
        flex-wrap: wrap;

        .pr_house_info_item {
          width: 33%;
          font-size: 14px;
          color: #8a929f;
          padding: 6px 0;
          margin-bottom: 12px;

          .label {
            min-width: 56px;
            margin-right: 16px;
          }

          .value {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .pr_facility {
      display: flex;
      flex-direction: column;
      margin: 24px 0;
      border-bottom: 1px solid #dde1e9;
      padding-bottom: 24px;

      .pr_facilityMain {
        display: flex;
        flex-wrap: wrap;
        padding-top: 6px;

        .pr_facilityBox {
          // display: flex;
          // flex-direction: column;
          width: 60px;
          text-align: center;
          margin-right: 60px;
          margin-bottom: 18px;

          i {
            font-size: 28px;
            color: #2e3c4e;
          }

          img {
            width: 28px;
            height: 28px;
          }

          .facility_title {
            text-align: center;
            font-size: 12px;
            color: #2e3c4e;
            margin-top: 10px;
          }
        }
      }

      .pr_facilityFooter {
        margin-top: 24px;
      }
    }
  }

  .pr {
    &.ma0 {
      margin-left: 0;
    }

    .label-box {
      margin-top: 24px;
      flex: 1;
      flex-wrap: wrap;

      .label-item {
        display: inline-block;
        margin-bottom: 24px;
        margin-right: 24px;
        font-size: 18px;
        color: #2d84fb;
        padding: 7px 23px;
        background: rgba(45, 132, 251, 0.15);
        border: 1px solid rgba(45, 132, 251, 1);
        border-radius: 4px;
      }
    }

    .pr-l {
      margin-top: 24px;
    }

    .bing-main {
      width: 280px;
      height: 280px;
      background: #f8f8f8;
      border-radius: 4px;
      margin-top: 24px;
    }

    .build {
      padding: 24px 12px;

      .build-list {
        align-items: center;
        margin-bottom: 24px;

        .pm {
          width: 20px;
          height: 20px;
          color: #fff;
          background: #d8d8d8;
          text-align: center;
          line-height: 20px;
          border-radius: 2px;
        }

        .pm0 {
          background: #fb656a;
        }

        .pm1 {
          background: #fb8968;
        }

        .pm2 {
          background: #fbc365;
        }

        span {
          margin-right: 12px;
          font-size: 14px;
          color: #8a929f;
        }
      }
    }

    .pr-r {
      margin-left: 24px;

      .name {
        font-size: 14px;
        color: #2e3c4e;
      }
    }
  }

  .title {
    span {
      font-size: 11px;
      color: #8a929f;
    }
  }
}

.blable {
  align-items: center;
  justify-content: space-between;

  .btn {
    border: 1px solid rgba(138, 146, 159, 1);
    font-size: 16px;
    color: #8a929f;
    padding: 9px 15px;
    border-radius: 2px;
    margin-right: 10px;
    cursor: pointer;
    align-items: center;
  }
}

.numberTop {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #afb5b42e;
  text-align: center;
  cursor: pointer;
  margin-left: 8px;
  display: flex;
  justify-content: center;
  align-items: center;

  // i {
  //   position: absolute;
  //   top: 3px;
  //   left: 4px;
  // }
}

.eliminate {
  opacity: 0.5;
  text-decoration: line-through;
}

.info_title {
  height: 41px;
  line-height: 41px;
  color: #2e3c4e;
}
</style>
