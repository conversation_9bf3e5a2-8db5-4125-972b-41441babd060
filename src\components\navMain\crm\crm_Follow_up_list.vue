<template>
  <div class="pages">
    <div class="box-crm-head">
      <div class="box-crm-head">
        <div class="crm-head-list" :class="{ head_list: recordstype == 0 }" @click="Follow_up_records(4, 0)">
          全部
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 1 }" @click="Follow_up_records(1)">
          回访
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 2 }" @click="Take_look_record">
          带看
        </div>

        <div class="crm-head-list" :class="{ head_list: recordstype == 4 }" @click="Follow_up_records(4)">
          维护
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 'xiansuo' }"
          @click="Follow_up_records('xiansuo','-1')">
          线索
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 3 }" @click="Outbound_record">
          外呼
        </div>
        <div class="crm-head-list"  @click.stop="goDouyinLiveRoomUsersPage" v-if="perms.douyinRoomUsers">
          直播
        </div>
        <div class="crm-head-list"  @click.stop="goTransViewPage" v-if="perms.transViewPage">
          流转
        </div>
        <div class="crm-head-list" v-if="website_id==109||website_id==626" :class="{ head_list: recordstype == 6 }"
         @click="reportprepared">
          报备
        </div>

        <!-- <div
        class="crm-head-list"
        :class="{ head_list: recordstype == 5 }"
        @click="Follow_up_records(5)"
      >
        成交
      </div> -->

      </div>
      <div>
        <el-button type="danger" size="small" style="margin-right: 22px;" @click="tomianban">BI数据面板</el-button>
      </div>
    </div>
    <!-- 筛选条件 -->
    <div class="content-box-crm" id="filter" style="margin-bottom: 24px">
      <!-- <myCollapse :isActive="is_collapse"> -->
      <!-- <template v-slot:content> -->
      <div class="bottom-border div row" style="padding-top: 10px">
        <!-- 时间 -->
        <div class="div row" style="margin-top: 10px">
          <template v-if="recordstype != 'xiansuo'">
            <div :class="{'t-select-group':params.date_type}">
              <el-select class="crm-selected-label" clearable v-model="params.date_type" placeholder="时间类型" :style="{
                            minWidth: '30px',
                            width: getSelectWidth(customerLabelList),
                          }" @change="time_type">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <el-select v-model="params.date_sort" class="crm-selected-label short" style="width: 78px;"
                @change="handleSearch" v-show="params.date_type">
                <el-option label="降序" :value="1"></el-option>
                <el-option label="升序" :value="2"></el-option>
              </el-select>
            </div>
            
          </template>
          <div class="block" style="margin: 0px 10px 0px 10px">
            <!-- <span class="demonstration">带快捷选项</span> -->
            <el-date-picker style="width: 350px" v-model="timeValue" type="datetimerange" size="small" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss" @change="onChangeTime">
            </el-date-picker>
          </div>
        </div>
        <!-- 跟进 -->
        <template v-if="recordstype != 'xiansuo'">
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="all_Customers_value" placeholder="全部客户" :style="{
                            minWidth: '20px',
                            width: '110px',
                          }" @change="all_Customers_through">
              <el-option v-for="item in all_Customers_list" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="follow_up_value" placeholder="跟进" :style="{
                            minWidth: '20px',
                            width: '110px',
                          }" @change="follow_through">
              <el-option v-for="item in follow_up_list" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 邀约 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="Invitation_value" placeholder="邀约" :style="{
                            minWidth: '20px',
                            width: '110px',
                          }" @change="invite_type">
              <el-option v-for="item in Invitation_list" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 客户状态 -->
          <div class="head-list">
              <el-cascader
              size="small " class="crm-selected-label" v-model="customer_statusvalue" placeholder="客户状态"
                :options="customer_status_list" :style="{
                  minWidth: '20px',
                  width: '165px',
                }" :props="{
                  value: 'id',
                  label: 'name',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }" collapse-tags clearable @change="customer_status"></el-cascader>
          </div>
          <!-- 等级 -->
          <div class="head-list">
            <el-cascader
              size="small " class="crm-selected-label" v-model="grade_value" placeholder="客户等级"
                :options="level_listcopy" :style="{
                  minWidth: '20px',
                  width: '130px',
                }" :props="{
                  value: 'id',
                  label: 'title',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }" collapse-tags clearable @change="customer_grade"></el-cascader> 
          </div>
          <!-- 部门 -->
          <div class="head-list" style="margin-top:6px">
            <el-cascader class="crm-selected-label" v-model="params.department_id" placeholder="请选择部门" :style="{
                            minWidth: '20px',
                            width: '130px',
                          }" :options="AllDepartment" @change="changePopDepar" @focus="Reqdepartment" :clearable="true"
              :show-all-levels="false" :props="{
                                label: 'name',
                                value: 'id',
                                children: 'subs',
                                checkStrictly: true,
                                emitPath: false,
                              }">
            </el-cascader>
          </div>
          <!-- 成员 -->
          <div class="head-list">
            <el-cascader class="inp_no_border1 my-cascader crm-selected-label" size="small" v-model="member_value" :options="member_listNEW"
              clearable filterable placeholder="成员" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" :props="{
                              label: 'user_name',
                              value: 'id',
                              children: 'subs',
                              checkStrictly: true,
                            }" @change="loadFirstLevelChildren"></el-cascader>
          </div>
          <div class="head-list" style="margin-top: 10px;">
            <el-cascader class="crm-selected-label"
            size="small " 
             :style="{minWidth: '20px', width: '165px',}" v-model="source_value" placeholder="客户来源"
              :options="source_list" @change="customer_source"
               :props="{
                  label: 'title',
                  value: 'id',
                  children: 'children',
                  checkStrictly: false,
                  emitPath: false,
                  multiple:true,
                }" clearable
                collapse-tags>

            </el-cascader>
          </div>
          <!-- 客户类型 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="typeLabel_value" placeholder="客户类型" :style="{
                            minWidth: '20px',
                            width: '110px',
                          }" @change="client_type">
              <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="customerstatus_value" placeholder="通话状态" :style="{
                            minWidth: '20px',
                            width: '110px',
                          }" @change="customerstatus_type">
              <el-option v-for="item in customerstatus" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
          </div>
        </template>
        <template v-if="recordstype == 'xiansuo'">
          <div class="head-list div row">
            <el-select class="crm-selected-label" clearable v-model="source" placeholder="来源" :style="{
                            minWidth: '20px',
                            width: '110px',
                          }" @change="changeThirdSource">
              <el-option v-for="item in third_source_list" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
            <div style="margin: 0px 10px;">
              <el-input placeholder="请输入渠道号码" v-model="params.refer_id" size="small" class="input-with-select"
                style="width: 180px">
                <el-button slot="append" icon="el-icon-search" @click="Channel_number"></el-button>
              </el-input>
            </div>
            <div>
              <el-input placeholder="请输入渠道名称" v-model="params.refer_name" size="small" class="input-with-select"
                style="width: 180px">
                <el-button slot="append" icon="el-icon-search" @click="Channel_Name"></el-button>
              </el-input>
            </div>
          </div>
        </template>
        <template v-if="recordstype==2">
          <div class="head-list">
            <el-select class="crm-selected-label" size="small" clearable v-model="params.project_name" filterable placeholder="项目名称"
            :style="{minWidth: '50px', width: '150px',}" @change="customerproject_name">
              <el-option
                v-for="item in project_list"
                :key="item.id"
                :label="item.name"
                :value="item.name">
              </el-option>
            </el-select>

          </div>
        </template>
      </div>
      <!-- 客户标签 -->
      <template v-if="recordstype != 'xiansuo'">
        <div class="bottom-border" style="padding-top: 24px;border-bottom: 0px dashed #e2e2e2;">
        <div class="label_list div row">
              <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="clearlabel">
                客户标签
              </span>
              <div v-for="item in label_list" :key="item.id" class="label_item">
                    <el-dropdown trigger="click" @command="handleCommand($event,item)">
                      <div class="namedata">
                        <span class="selectedname">{{item.name}}<i class="el-icon-arrow-down el-icon--right"></i></span>
                      </div>
                      <el-dropdown-menu slot="dropdown" style="max-height: 300px !important;overflow-y: auto !important;">
                        <el-dropdown-item v-for="(label, index) in item.label" :key="index"
                        :command="label">{{ label.name }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
              </div>
            </div>
            <div class="alllabeldatastyle">
              <div class="div row">
                <div class="labeldatastyle" v-for="(arr,index) in labeldata" :key="index">
                <el-tag type="info">{{ arr.title+"/"+arr.name }}
                  <i class="el-icon-error" style="font-size:15px;" @click="dellabeld(index,arr.id)"></i></el-tag>
              </div>
              </div>
              <div class="labelerr">
                <el-button v-if="labeldata.length" type="primary" size="mini" icon="el-icon-delete"
                @click="clearlabel"></el-button>
              </div>
            </div>
        </div>
      </template>
    </div>
    <!-- 表格操作按钮 -->
    <div class="content-box-crm content-box-crm-pr pad0">
      <div class="table-top-box table-top-box-abs div row" :class="{ fixed: scrollTop > topHeight }" id="stickyId">
        <!-- <div class="zhanwei" v-if="scrollTop + 80 > 400"></div> -->
        <div class="t-t-b-left div b-tabs row" style="width: 100%; height: 30px">
          <el-input :placeholder="placeholder" v-model="select_params.keywords" class="input-with-select"
            style="width: 300px" clearable size="small" @keyup.enter.native="handleKeywordSearch"
            @clear="clearSelectKeyword">
            <el-select v-model="select_params.type" @change="changeSelectParams" slot="prepend" placeholder="请选择"
              size="small" style="width: 100px">
              <el-option label="客户电话" :value="1"></el-option>
              <template v-if="recordstype != 'xiansuo'">
                <el-option label="客户编号" :value="2"></el-option>

                <el-option label="线索名称" :value="3"></el-option>
                <el-option label="客户姓名" :value="4"></el-option>
                <el-option label="归属地" :value="6"></el-option>
                <el-option label="跟进内容" :value="7"></el-option>
              </template>
              <template v-if="recordstype == 'xiansuo'">
                <el-option label="客户姓名" :value="3"></el-option>
              </template>
              <template v-if="recordstype == 2">
                <el-option label="项目名称" :value="5"></el-option>
              </template>
             
            </el-select>
            <!-- <el-button slot="append" icon="el-icon-search"></el-button> -->
          </el-input>
        </div>
        <div class="t-t-b-right" :class="{ abs: is_small_system, show: show_right }">
          <el-button v-if="douyinview" style="font-size: 14px" size="mini" @click="douview">
            <div class="flex-row items-center">
              <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
              <span>数据视图</span>
            </div>

          </el-button>
          <template v-if="recordstype != 'xiansuo'">
            <el-button v-if="outview" style="font-size: 14px" size="mini" @click="outboundview">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
                <span>数据视图</span>
              </div>
            </el-button>
            <el-button v-if="recordstype == 2" style="font-size: 14px" size="mini" @click="takelook">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
                <span>数据视图</span>
              </div>
            </el-button>
            <el-button v-if="recordstype == 1" style="font-size: 14px" size="mini" @click="Followupview">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
                <span>数据视图</span>
              </div>
            </el-button>
            <el-button v-if="recordstype == 4" style="font-size: 14px" size="mini" @click="maintenanceview">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
                <span>数据视图</span>
              </div>
            </el-button>
            <el-button type="text" size="mini" class="el-icon-d-arrow-left" id="myButton" v-show="myButton1"
              @click="leftla"></el-button>
            <el-button style="font-size: 14px" type="primary" size="mini" @click="leading_out">
              导出
            </el-button>
            <el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="TransferCustomer(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" style="margin-right:10px;margin-top:0px;" @click="getCrmDepartmentList" class="search_loudong div row align-center">
                <div class="seach_value">操作</div>
                <div class="sanjiao" :class="{ transt: transfer_type }"></div>
              </div>
            </el-popover>
            <!-- <el-button style="font-size: 14px" type="primary" size="mini" @click="TransferCustomer">
              转交
            </el-button> -->
            <el-button v-show="buttonhidden" style="font-size: 14px" type="primary" @click="setCustomerLabel" size="mini">
              设置标签
            </el-button>
            <el-button type="text" size="mini" class="el-icon-d-arrow-right" id="myButton1" v-show="myButton"
              @click="Rightdrag" style="margin-left: 10px"></el-button>
          </template>

        </div>
      </div>
      <div style="padding: 0 24px">
        <tablecomponent :is_table_loading="is_table_loading" :tableData="tableData"
        :labels_list="labels_list"
        :recordstype="recordstype" :status_list="status_list" :characterNamedata="characterNamedata"
        @fastEditData="fastEditData" @fastLookTel="fastLookTel" @onClickDetailXiansuo="onClickDetailXiansuo"
        @copyCusID="copyCusID" @onClickFollowStatus="onClickFollowStatus" @setStatus="setStatus" @fastEditLabel="fastEditLabel"
        @fastFollowUp="fastFollowUp" @HomeAddress="HomeAddress" @onClickDetail="onClickDetail" @openproject="openproject"
        @play1="play1" @selectionChange="selectionChange" @getLabelGroupNoPageNew="getLabelGroupNoPageNew" @Quick_Edit="Quick_Edit"
        ></tablecomponent>
      </div>

      <div class="page_footer flex-row items-center">
        <div class="page_footer_l flex-row flex-1 items-center">
          <div class="head-list">
            <el-button type="primary" size="small" @click="empty">清空</el-button>
          </div>
          <div class="head-list">
            <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
          </div>
        </div>
        <div>
          <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
            :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
            @current-change="onPageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- <wxwork v-if="is_tabs === 'wxwork'"></wxwork> -->
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel"
      :close-on-click-modal="false">
      <!-- <myForm
          @clsoe="is_push_customer = false"
          :data1="n_client_field"
          :data2="n_company_field"
          :form="form"
          :form1="form1"
          @onClick="onClickForm"
        ></myForm> -->
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.source_id" placeholder="请选择">
                <el-option v-for="item in sourceLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                  :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt=""
                   @click="() => {push_form.sex = item.id;}" />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <div class="row input-box div">
              <div @click="onClickLevel(item)" class="l-item row" v-for="item in levelLabel" :key="item.id" :class="{
                                lisactive: item.id === push_form.level_id,
                              }">
                <div class="l-name">{{ item.title }}</div>
                <div class="l-name-1">{{ item.desc }}</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户标签：">
            <div class="input-box">
              <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签"
                :options="label_default_list" :props="{
                                  value: 'id',
                                  label: 'name',
                                  children: 'label',
                                  emitPath: false,
                                  multiple: true,
                                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.intention_community"></el-input>
            </div>
          </el-form-item>
          <!-- <el-form-item label="意向区域：">
              <div class="row input-box div">
                <el-input
                  placeholder="请输入"
                  v-model="push_form.intention_street"
                ></el-input>
              </div>
            </el-form-item> -->
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer flex-row align-center">
        <el-switch v-model="push_form.add_type" active-color="#13ce66" inactive-color="#2d84fb" inactive-value="1"
          active-value="2" active-text="公海" inactive-text="私客">
        </el-switch>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
            定</el-button>
        </div>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_follow_dialog" title="跟进记录">
      <el-table v-loading="is_follow_loading" :data="is_follow_data" border>
        <el-table-column label="ID" prop="client_follow_id"></el-table-column>
        <el-table-column label="跟进类型" prop="type_title"></el-table-column>
        <el-table-column label="跟进内容" prop="content"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="is_follow_params.total" :page-size="is_follow_params.per_page" :current-page="is_follow_params.page"
        @current-change="onPageChangeQwFollow">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_dialog_upload" title="导入" :before-close="cancels">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <!-- <el-select
          style="width: 200px; margin-bottom: 10px"
          v-model="upload_form.type"
          placeholder="请选择"
        >
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select> -->
      <div class="flex-row">
        <!-- 是否覆盖 -->
        <el-select style="width: 200px; margin-bottom: 10px" v-model="upload_form.type" placeholder="请选择是否覆盖数据">
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select>
        <!-- 选择分类 -->
        <el-select style="width: 150px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.type_id"
          placeholder="请选择分类" clearable>
          <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
        <!-- 选择客户来源 -->
        <el-select style="width: 200px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.source_id"
          placeholder="请选择客户来源" clearable>
          <!-- source_import -->
          <el-option v-for="item in source_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
      </div>
      <div class="flex-row">
        <!-- 选择成员 -->
        <el-input ref="focusMember" placeholder="请选择维护人" v-model="uploadAdmin_id" style="width: 200px; display: block"
          @focus="focusSelete">
          <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <!-- 选择标签 -->
        <!-- <el-select
            style="width: 362px; margin-bottom: 10px; margin-left: 12px;"
            v-model="upload_form.label"
            multiple
            placeholder="请选择标签"
          >
            <el-option label="标签1" :value="1"></el-option>
            <el-option label="标签2" :value="2"></el-option>
          </el-select> -->
        <el-cascader style="width: 362px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.label" clearable
          placeholder="请选择标签" :options="label_list" :props="{
                      value: 'id',
                      label: 'name',
                      children: 'label',
                      emitPath: false,
                      multiple: true,
                    }">
        </el-cascader>
      </div>
      <!-- 客户备注线索 -->
      <div class="clueRemark">
        <el-input ref="focusMember" placeholder="请选择录入人" v-model="uploadAdmin_id1" style="width: 200px; display: block"
          @focus="focusSelete1">
          <i v-show="uploadAdmin_id1 != '' && uploadAdmin_id1 != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <el-input type="textarea" :rows="2" placeholder="请输入客户备注线索" v-model="upload_form.remark"
          style="margin-left: 11px">
        </el-input>
      </div>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
          >添加文件</el-button
        > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancels">取 消</el-button>
        <el-button type="primary" @click="startImport" :loading="is_loading">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list1" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list1" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember1" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog title="客户标签" :visible.sync="show_Customer_label" width="660px">
      <div class="dialog_customer_label">
        <div v-for="(item, index) in labels_list" :key="item.id">
          <div class="labelname">{{ item.name }}</div>
          <div class="lables-box div row">
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.taggroup" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.label" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="allbtn">
         <div>
            <el-radio v-model="confirm_batch_list.type" label="1">覆盖</el-radio>
            <el-radio v-model="confirm_batch_list.type" label="2">追加 </el-radio>
         </div>
         <div>
           <el-button @click="show_Customer_label = false">取 消</el-button>
           <el-button type="primary" @click="confirmSelected" :loading="is_loading">确 定</el-button>
         </div>
      </div>
        
      </span>
    </el-dialog>
    <!-- 列表 -->
    <el-dialog :visible.sync="is_transfer_customer" title="转交客户">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border class="eltable" @sort-change="sortChange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum&&changetitle=='转交客户'"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="startProcessing(scope.row)">转交客户</el-link>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>
    <!-- 右侧 -->
    <el-dialog :visible.sync="right_transfer_customer" :title="changetitle">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border @sort-change="sortChange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum&&changetitle=='转交客户'"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row,1)">转交客户</el-link>
          </template>
        </el-table-column>


        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row,1)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>
    <!-- 快速编辑客户维护资料模态框 -->
    <my-Maintenance v-if="show_cus_Edit" :show_cus_Edit="show_cus_Edit" :source_list="ponent_maintain_source"
      :level_list="ponent_maintain_level" :type_list="ponent_maintain_type" :label_list="ponent_maintain_label"
      :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit" @submitMaintain="submitMaintain">
    </my-Maintenance>
    <!-- 快速查看客户手机号模态框 -->
    <my-LookTel v-if="show_look_Tel" :show_look_Tel="show_look_Tel" :ponent_Tel_data="ponent_Tel_data" :selfID="selfID"
      :nowDialData="nowDialData" @fastCloseTel="fastCloseTel" @fastSubmitTel="fastSubmitTel"></my-LookTel>
    <!-- 快速提交客户审批 -->
    <my-Examine v-if="show_Examine_dialog" :show_Examine_dialog="show_Examine_dialog"
      :ponent_Examine_data="ponent_Examine_data" :ponent_Examine_stutas="ponent_Examine_stutas"
      :ponent_Examine_type="Examine_type" :AllDepartment="AllDepartment" @closeExamine="closeExamine"
      @submitExamineAfter="submitExamineAfter"></my-Examine>
    <!-- 快速跟进客户内容模态框 -->
    <myFollowUp v-if="show_Follow_dialog" :show_Follow_dialog="show_Follow_dialog"
      :ponent_Follow_data="ponent_Follow_data" @addFollowSuccess="addFollowSuccess" @closeFollow="closeFollow">
    </myFollowUp>
    <!-- <input
        v-if="is_dialog_upload"
        type="file"
        ref="file"
        style="display: none"
        v-on:change="handleFileUpload($event)"
      /> -->
    <el-upload :limit="1" class="upload-demo" :headers="myHeader" :action="user_avatar"
      :on-success="handleSuccessAvatarTemporary" ref="upload" style="display: none" v-if="is_dialog_upload">
      <el-button class="el-icon-download" size="small">本地上传</el-button>
    </el-upload>

    <audio ref="musicMp3" id="musicMp3" :autoplay="false" controls="false" style="z-index: -1"></audio>
    <div v-if="showneedstestres">
      <el-dialog style="width: 1260px;left:16%" :visible.sync="showneedstestres" title="导出数据">
        <div class="texttestres">导出数据 创始人安全验证</div>
        <div class="testresinformation">
          <div class="founderinformation">当前系统创始人：{{founderdata.user_name}} &nbsp;&nbsp; 手机号：{{founderdata.phone}}</div>
        </div>
        <div class="textCode">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="短信验证码" prop="captcha">
              <el-input v-model="ruleForm.captcha" placeholder="请输入验证码">
                <el-button slot="append" @click="sendingcode">发送验证码
                  <p v-if="counting">( {{ countdown }})秒</p>
                </el-button>

              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showneedstestres=false">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </el-dialog>
    </div>
        <!-- 客户信息右侧边栏插件 -->
        <div class="kehuxinxi">
          <el-drawer :visible.sync="drawer" title="客户信息" v-cloak :modal="false" :direction="direction"
            :before-close="handleClose" custom-class="kehu">
            <div class="QuickEdit">
              <Drawerdetails ref="childRef" :ids="ids" :type="seas" :types="seastype" @getDataListA="getDataList" :recordName.sync="recordName">
              </Drawerdetails>
            </div>
            <div class="demo-drawer__footer">
              <div class="drawerfoot flex-row" style="justify-content: space-between;">
                <div class="flex-row" style="margin-left: 15px;">
                  <div class="crmstatus">客户状态: {{customstatus}}</div>
                  <!-- <el-button type="primary" size="small" style="margin-left:20px" v-if="ClaimCustomers" @click="onClickGet(c_detail.id)">立即认领</el-button> -->
                  <!-- <div>
                    <el-button type="primary" size="small" style="margin-left:20px" plain @click="TopA(ids)">{{ticky_post
                                      }}</el-button>
                  </div> -->
                
                  <el-input size="small" placeholder="关键词" v-model="followsearch" class="input-with-select short" v-if="recordName =='Follow'"
                  clearable @clear="getfollowsearch">
                    <el-button slot="append" icon="el-icon-search" @click="getfollowsearch"></el-button>
                  </el-input>
                </div>
                <div style="margin-right: 22px;">
                  <el-popover v-if="transmitstatus" v-model="Transfer_right" placement="bottom" width="500px" trigger="click">
                    <div class="f-list">
                      <div class="f-item" v-for="item in cus_list" :key="item.id" @click="onClickrightcopy(item)">
                        {{ item.name }}
                      </div>
                    </div>
                    <el-button style="margin-right: 10px;" type="primary" size="small" slot="reference">
                        操作
                      </el-button>
                  </el-popover>
                  <el-button type="warning" size="small" style="width: 100px;" @click="sureright">确定</el-button>
                </div>
              </div>
            </div>
          </el-drawer>
        </div>
     <!-- 项目库 -->
     <Project_library ref="Projectlibrary" ></Project_library>
     <modifylevel ref="modifylevel" :leveldata="ponent_maintain_level" @getDataList='getDataList'
     :member_listNEW="member_listNEW" :memberList="memberList" :status_list="status_list"></modifylevel>
     <customersource ref="customersource" :source_list="source_list" @getDataList='getDataList'></customersource>
     <automatic ref="automatic" @getDataList='getDataList'></automatic>
  </div>
</template>
  
<script>
// import my from "./components/my";
// import seas from "./components/seas";
// import wxwork from "./components/wxwork";
// import myForm from "./components/customer_form";
import { Loading } from "element-ui";
// import myTable from "@/components/components/my_table";
// import myLabel from "./components/my_label.vue";
// import myCollapse from "./components/collapse";
import myExamine from "@/components/components/my_Examine.vue";
import myMaintenance from "@/components/components/my_maintenance.vue";
import myLookTel from "@/components/components/my_lookTel.vue";
// import mySelect from "./components/my_select";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import myFollowUp from "@/components/components/my_followUp.vue";
import config from "@/utils/config";
import Project_library from '@/components/tplus/tSelect/Project_library.vue';
import Drawerdetails from "./components/Drawer_details.vue"
import tablecomponent from '@/components/tplus/tSelect/tablecomponent.vue';
import modifylevel from '@/components/tplus/bulkoperation/Batchlevels.vue'
import customersource from '@/components/tplus/bulkoperation/customersource.vue'
import automatic  from '@/views/crm/share_follower/automatic_allocation.vue'
export default {
  name: "crm_customer_seas_list",
  components: {
    // my,
    // seas,
    // myForm,
    // wxwork,
    // myTable,
    // mySelect,
    // myLabel,
    // myCollapse,
    multipleTree,
    myMaintenance,
    myLookTel,
    myExamine,
    myFollowUp,
    Project_library,
    Drawerdetails,
    tablecomponent,
    modifylevel,
    customersource,
    automatic
  },
  data() {
    return {
      //记录切换
      recordstype: 0,
      //视图图列表切换
      douyinview:false,
      outview:false,
      //时间类型
      options: [
        { value: 0, label: '全部' },
        { value: 1, label: '创建时间' },
        { value: 2, label: '跟进时间' },
        { value: 3, label: '线索时间' },
        { value: 4, label: '更新时间' },
        { value: 5, label: '掉公时间' },
        { value: 6, label: '转公时间' },
        { value: 7, label: '跟客天数' },
        { value: 8, label: '带看创建时间'},
        { value: 9, label: '带看时间' },
        { value: 10, label: '转交时间' }
      ],
      value: "",
      value2: '',
      // 跟进
      follow_up_value: "",
      follow_up_list: [
        { id: 1, name: "已跟进" },
        { id: 2, name: "已认领" },
        { id: 3, name: "未跟进" },
        { id: 4, name: "待分配" },
        { id: 5, name: "已转公" },
        { id: 6, name: "已掉公" },
        { id: 7, name: "语音跟进" },
        { id: 8, name: "普通跟进" },
        { id: 9, name: "电话跟进" },
        { id: 10, name: "多条线索" },
      ],
      all_Customers_value:"",
      all_Customers_list: [
        { id: 0, name: "全部客户" },
        { id: 1, name: "公海客户" },
        { id: 2, name: "潜在客户" },
        { id: 3, name: "成员私客" },

      ],
      counting:false,//倒计时
      countdown:60,//秒数
      drawer: false,//客户侧边栏信息
      direction: 'rtl',
      ids: "",
      seas:"my",
      seastype:"",
      recordName: '',
      transmitstatus:true,
      customstatus:"",
      transfer_type: false, // 显示/隐藏转交类型
      //操作功能类型
      cus_list: [
        { id: 1, name: "转交到同事" },
        { id: 2, name: "复制到同事的流转客" },
        { id: 3, name: "批量更新客户等级"},
        { id: 4, name: "批量更新客户来源"},
        { id: 5, name: "批量转公"},
        { id: 6, name: "批量变更角色"},
        { id: 7, name: "批量更新客户状态"},
        { id: 8, name: "批量自动分配"}

      ],
      changetitle:"转交客户",
      right_transfer_customer:false,
      ticky_post:"",
      Transfer_right:false,
      // 邀约
      Invitation_value: "",
      Invitation_list: [
        { id: 1, name: "查看电话" },
        { id: 2, name: "已接通（外呼）" },
        { id: 3, name: "未接通（外呼）" },
        { id: 4, name: "已带看" },
        { id: 5, name: "未带看" },
        { id: 6, name: "有复看" },
        { id: 7, name: "修改资料" },
        { id: 8, name: "修改标签" },
        { id: 9, name: "修改等级" },
        { id: 10, name: "转交同事" },
        { id: 11, name: "转交公海" },
      ],
      // 客户状态
      customer_statusvalue: "",
      customer_status_list: [
        { id: 1, name: "有效客户" },
        { id: 2, name: "无效客户" },
        { id: 3, name: "暂缓客户" },
        { id: 4, name: "我司成交" },
        { id: 5, name: "他司成交" },
        { id: 6, name: "未成交" },
      ],
      //等级
      grade_value: "",
      // grade_list: [],
      //成员
      member_value: "",
      member_list: [
        {
          id: 1, name: "录入人",
          subs: []
        },
        {
          id: 2, name: "维护人",
          subs: []
        },
        {
          id: 3, name: "带看人",
          subs: []
        },
        {
          id: 4, name: "成交人",
          subs: []
        },
      ],
      member_listNEW: [
        {
          id: 5, 
          user_name: "全部",
          subs: []
        },
        {
          id: 1, user_name: "录入人",
          subs: []
        },
        {
          id: 2, user_name: "维护人",
          subs: []
        },
        {
          id: 3, user_name: "带看人",
          subs: []
        },
        {
          id: 4, user_name: "成交人",
          subs: []
        },
      ],
      //客户来源
      source_value: '',
      typeLabel_value: "",
      customerstatus_value:"",
      //选择成员弹框控制
      show_member_list: false,
      show_member_list1: false,
      is_tabs: "all",
      labeldata:[], 
      customerLabelListcopy:[],
      selectedIds: [],
      tabs: [
        {
          id: 1,
          name: "所有客户",
          desc: "all",
        },
        {
          id: 2,
          name: "我的客户",
          desc: "my",
        },
        {
          id: 3,
          name: "公海客户",
          desc: "seas",
        },
        // {
        //   id: 4,
        //   name: "企微客户",
        //   desc: "wxwork",
        // },
      ],
      is_push_customer: false,
      push_form: {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        label: "", // 客户标签
        // intention_street: "",
        remark: "",
        add_type: "1",
      },
      sex_list: [
        { id: 1, name: "nan" },
        { id: 2, name: "nv3" },
      ],
      other_mobile: [{ mobile: "" }],
      type_list: [],
      timeValue: "",
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            end.setHours(0, 0, 0, 0);
            const start = new Date(end);
            start.setDate(start.getDate() - 1);
            end.setDate(end.getDate() - 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本周',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上周',
          onClick(picker) {
              const today = new Date(); // 获取当前日期
              const end = new Date(today); // 结束日期为当前日期
              const start = new Date(today); // 开始日期为当前日期
              const day = today.getDay(); // 获取当前是星期几
              const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
              const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
              end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
              end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
              start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
              start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
              // 将计算得到的时间范围传递给日期选择器
              picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上月',
          onClick(picker) {
            const end = new Date();
            end.setDate(0); // 获取上个月的最后一天
            end.setHours(23, 59, 59, 0);
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      level_list: [],
      level_listcopy:[],
      source_list: [],
      client_field: {
        // 获取客户字段
        type: 2,
      },
      n_client_field: {},
      n_company_field: {},
      form: {},
      form1: {},
      type: 1,
      tracking_list: [],
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      //成员部门信息
      memberList: [],
      multipleSelection: [],
      params: {
        page: 1,
        per_page: 10,
        type: 4,//(1:回访,2:带看,3:外呼4 全部 和维护 5 成交)
        keywords: '',//搜索关键词(客户姓名或备注)
        mobile: '',//手机号或者客户ID
        refer_id:'', //渠道号码
        refer_name:'',// 渠道名称
        date_sort: 1,   //1=倒序,2=升序
      },
      select_params: {
        type: 1,
        keywords: ""
      },
      show_xiansuo_search: false,
      is_table_loading: false,
      followsearch: '',
      tableData: [],
      is_follow_dialog: false,
      is_follow_data: [],
      is_follow_loading: false,
      is_follow_params: {
        page: 1,
        total: 0,
        per_page: 10,
      },
      label_list: [],
      multipleSelectionname: [],
      is_collapse: true,
      tracking_params: {
        type: 4,
      },
      is_dialog_upload: false,
      uploadAdmin_id: "",
      uploadAdmin_id1: "",
      upload_form: {
        type: 1, // 是否覆盖数据 1：不覆盖 2：覆盖
        admin_id: "", // 管理员id
        create_id: "",
        file: "", // 导入的文件
        type_id: "", // 客户类型id
        source_id: "", // 客户来源
        label: "", // 标签:字符串格式，多个用逗号隔开
        remark: "", // 客户备注线索
      },
      admin_params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
      },
      admin_list: [],
      is_button_loading: false,
      pull_params: {
        next_user_cursor: 0,
        next_customer_cursor: "",
      },
      list_tabs: [
        { id: 0, title: "全部" },
        { id: 1, title: "已认领" },
        { id: 2, title: "已跟进" },
        { id: 3, title: "未跟进" },
      ],
      customerstatus:[
        {id:1,title:"未联系"},
        {id:2,title:"未接通"},
        {id:3,title:"已接通"}
      ],
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过3000条",
        "标红标题对应内容为必填项，如不填写则过滤出去不进行导入",
        "同一个表格重复的手机号会导入1个客户，系统将按重复的顺序将客户线索内容追加到线索记录 ",
        "选择覆盖，导入的数据和已有数据重复时，会覆盖之前的数据，选择不覆盖则过滤掉重复数据",
      ],
      no_follow_number: "",
      show_tel_search: false,
      buttonhidden: true,
      myButton: false,
      myButton1: false,
      is_show_upload: false, // 是否显示导入按钮
      customer_list_type: [
        { id: 0, title: "新增线索", is_select: true },
        { id: 2, title: "最新跟进", is_select: true },
        { id: 3, title: "多条线索", is_select: true },
        // { id: 4, title: "最近活跃", is_select: false },
        // { id: 1, title: "已认领", is_select: false },
        // { id: 3, title: "未跟进", is_select: false },
        // { id: 4, title: "私客" },
        // { id: 5, title: "掉公" },
      ],
      selectedItemId: "",
      // 1:最近活跃,2:多条线索
      screen_list_type: [
        { id: 1, title: "已认领", is_select: true },
        { id: 5, title: "未跟进", is_select: true },
      ],
      screen_list_type1: [
        { id: "1_5", title: "待分配", is_select: true },
        { id: "2_5", title: "待跟进", is_select: true },
      ],
      screen_list_type2: [
        { id: "4", title: "最近活跃", is_select: true },
      ],
      customer_type: 0,
      notNewClue: true,
      is_show: 0, // 控制客户标签样式
      label_default_list: [], // 获取原始默认标签列表
      customerLabelList: {}, // 所选择的标签
      changeParentLabel: "", // 之前所选的上一级的标签
      is_all: true, // 控制客户标签，“全部”标签的样式
      datalist: [], // 全部部门人员
      show_Customer_label: false, // 控制客户标签模态框显示/隐藏
      labels_list: [], // 客户标签列表
      // 确定编辑客户标签的接口传参
      confirm_batch_list: {
        ids: "", // 客户id 多个用逗号隔开
        label: "", // 客户标签id 多个用逗号隔开
        type: "2", //添加标签时选择 2:追加 或 1:覆盖
      },
      pop_depart: false, // 显示/隐藏部门搜索popover
      AllDepartment: [], // 全部部门列表
      is_transfer_customer: false, // 转交客户模态框
      c_id: "", // 转让客户id：多个用，隔开
      selectedMember: "", // 选择搜索成员
      Not_duplicate_datalist: [], // 没有重复部门成员的容器
      filtrMember: [], // 选中部门后过滤出的部门成员
      status_id: "", // 登录后的个人id
      status_list: [], // 快速编辑客户状态数据容器
      copy_status_list: [], // 深拷贝客户状态
      getStutas_params: {
        type: 2,
      },
      is_loading: false,
      show_Examine_dialog: false, // 控制客户审批模态框
      ponent_Examine_data: {}, // 提交审批客户信息
      ponent_Examine_stutas: {}, // 选择的要修改的状态
      website_id: "", // 当前站点id
      show_cus_Edit: false, // 显示快速编辑客户维护资料模态框
      ponent_maintain_data: {}, // 客户信息
      ponent_maintain_source: [], // 深拷贝客户来源列表
      ponent_maintain_level: [], // 深拷贝客户来源列表
      ponent_maintain_type: [], // 深拷贝客户类型列表
      ponent_maintain_label: [], // 深拷贝客户标签列表
      show_look_Tel: false, // 显示快速查看客户手机号模态框
      ponent_Tel_data: {}, // 客户信息
      is_fast: false, // 是否是快速编辑标签
      fastEdit_params: {
        client_id: "", // 客户id
        label: "", // 标签id多个，隔开
      },
      Examine_type: 19, // 默认审批类型
      is_pullDown: false, // 是否展开下拉框
      is_pullDown1: false,
      ponent_Follow_data: {}, // 客户信息
      show_Follow_dialog: false, // 显示快速跟进客户模态框
      tabIndex: -1,
      is_small_system: false,
      is_pullDown2: false,
      show_right: false,
      nowDialData: {},
      placeholder: "请输入客户电话",
      user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_FILE_2}`,
      scrollTop: 0,
      topHeight: 330,
      source: "",
      third_source_list: [],
      // screenWidth: 1450
      shownum:false,
      founderdata:[],//创始人信息
      needs_testres:"",//导出是否需要短信验证，0是需要，1是不需要
      showneedstestres:false,//短信验证弹框\
      // founder:"",//创始人姓名，
      // founderphone:"",//创始人电话
      ruleForm:{
        captcha:""
      },
      rules: {
        captcha: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' },
        ],
      },
      perms: {
        douyinRoomUsers: false,     //抖音直播间用户权限
        transViewPage: false,       //流转视图
      },
      customerDetailChangedIds: [],
      characterNamedata:{},//角色名称数据
      batchSize: 10,
      interval: 1000, // 请求间隔时间（毫秒）
      loadingInstance:null,
      zuanjiaoid:"",
      project_list:[],//项目项目列表
    };
  },
  watch: {
    admin_list: {
      handler(newVal) {
        // 根据数据变化判断是否显示年龄列和性别列
        this.shownum = newVal.some(item => item.number);
        console.log(newVal.some(item => item.number));
      },
      deep: true // 监听对象内部属性的变化
    },
    recordstype(val){
      if(val != 2){
        if(this.select_params.type == 5){
          this.select_params.keywords = '';
          this.params.project_name = '';
          this.select_params.type = 1
          this.changeSelectParams(1)
        }
      }
    }
  },

  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    levelLabel() {
      return this.level_list.filter((item) => {
        return item.id > 0;
      });
    },
    sourceLabel() {
      return this.source_list.filter((item) => {
        return item.id > 0;
      });
    },
    typeLabel() {
      return this.type_list.filter((item) => {
        return item.id > 0;
      });
    },
    tableList_header() {
      if (this.params.type == 1) {
        return this.table_header;
      } else if (this.params.type == 2) {
        return this.table_header1
      } else {
        return this.table_header2
      }
    }

  },
  filters: {
    // 判断是否是掉公或转公客户
    publicStatusParse(row, row1) {
      // console.log(row,row1);
      if (row == 1) {
        return "已转公" + row1;
      } else if (row == 2) {
        return "已掉公" + row1;
      }
    },
    // 计算过去时间天数
    getPastDay(val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
  },
  created() {
    // 赋值website_id
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id;
    }
    // if(this.website_id==176||this.website_id==109){
    //     }
    if(this.$route.query.clue){
      this.params.refer_id = this.$route.query.clue
      this.Follow_up_records('xiansuo','-1')
    }
    if(this.$route.query.platform){
      this.params.source = this.$route.query.platform
      this.source = Number(this.params.source)
      this.Follow_up_records('xiansuo','-1')
    }
    if(this.$route.query.alltype){
      this.all_Customers_value = 3
      this.customer_statusvalue = 6
      this.params.tracking_type = 6
      this.params.client_type = this.$route.query.alltype
      this.member_value = [5,parseInt(this.$route.query.uid)]
      console.log( this.member_value,"=======================" );
      this.params.admin_type = 5
      this.params.admin_id = this.$route.query.uid
      // this.getDataList();
    }
    if (this.$route.query.status) {
      // this.params.c_type1 = this.$route.query.status;
      // if (this.$route.query.status == 0) {
      //   this.params.c_type1 = 1;
      // }
    } else {
      // this.params.c_type1 = 0;
    }
    let screenWidth = document.body.clientWidth
    if (screenWidth < 1355) {
      this.is_small_system = true
      this.myButton1 = true
    } else {
      this.is_small_system = false
      this.myButton1 = false
    }
    let userData = localStorage.getItem( 'juesename');
       userData = JSON.parse(userData)
      if(userData.diy_create_name){
      //录入人
      this.member_listNEW[1].user_name += `(${userData.diy_create_name})`
      }
      if(userData.diy_follow_name){
        //维护人
        this.member_listNEW[2].user_name += `(${userData.diy_follow_name})`
      }
      if(userData.diy_take_name){
        //带看人
        this.member_listNEW[3].user_name += `(${userData.diy_take_name})`
      }
      if(userData.diy_deal_name){
        //成交人
        this.member_listNEW[4].user_name += `(${userData.diy_deal_name})`
      }
    },
  mounted() {
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
    // this.screenWidth = document.body.clientWidth

    // window.onresize = () => {
    //   console.log(12323);
    //   return (() => {
    //     this.screenWidth = document.body.clientWidth
    //   })()
    // }
    this.getDataList();
    window.addEventListener('resize', this.handleResize);
    this.getScreenWidth();
    this.getTypelist();
    this.getTrackingList();
    this.getLevelData();
    this.getSourceData();
    this.getLabelList();
    this.getAdmin();
    this.getCrmCustomerFollowNumber();
    this.getadminUser();
    this.getfounderinfo()
    this.getStatus();
    this.getDepartmentList(); // 获取部门
    this.getMemberList();//获取成员
    this.tfybinding()
    this.verification()
    this.MembersNEW()
    this.getProjectList()
    let page = document.querySelector('.main-content');
    window.addEventListener('resize', this.handleResize);

    const filter = document.getElementById("filter")

    this.topHeight = filter.offsetHeight + 120
    page.addEventListener('scroll', this.debounce(this.handleScroll, 20))

    eventBus.$on('customerDetailChange', (id) => {
      !this.customerDetailChangedIds.includes(id) && this.customerDetailChangedIds.push(id);
    })
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('scroll', this.handleScroll)
    window.addEventListener("beforeunload", function() {
     document.cookie = "value=; expires=Thu, 01 Jan 1970 00:00:00 GMT";
   });

   eventBus.$off('customerDetailChange')
  },
  // 当该页面进入时触发
  async activated() {
    // if (
    //     this.params.c_type1 != this.$route.query.status &&
    //     this.$route.query.status != undefined
    // ) {
    //     this.params.c_type1 = this.$route.query.status;
    //     this.getDataList();
    // }
    // 判断是否要刷新数据
    // console.log(this.$route.query.status);
    // console.log(this.$store.state.allowUpdat);
    // if (this.$store.state.allowUpdate) {
    //     this.$store.state.allowUpdate = false;
    //this.getDataList(); // 刷新页面数据
    // }


    if(this.customerDetailChangedIds.length && this.tableData.find(e => this.customerDetailChangedIds.includes(e.id))){
      this.customerDetailChangedIds = [];
      this.getDataList();
    }
  },
  methods: {
    //打开抖音直播间用户页面
    goDouyinLiveRoomUsersPage(){
      this.$goPath('/crm_live_room_users');
    },
    //打开流转视图
    goTransViewPage(){
      this.$goPath('/crm_information_follow');
    },
    //打开报备表格
    reportprepared(){
      this.$goPath("/reportingList")
    },
    //腾房云是否绑定
    tfybinding(){
      this.$http.tfyshow().then(res=>{
        // console.log(res.data);
        document.cookie = `value=${res.data}`;
      })
    },
    changeSelectParams(e) {
      // console.log(e);
      if (e == 1) {
        this.placeholder = '请输入客户电话'
      } else if (e == 2) {
        this.placeholder = '请输入客户编号'

      } else if (e == 3) {
        if (this.recordstype == 'xiansuo') {
          this.placeholder = '请输入客户姓名'
        } else {
          this.placeholder = '请输入线索'
        }
      }else if ( e == 4){
        this.placeholder = '请输入客户姓名'
      }else if ( e == 5){
        this.placeholder = '请输入项目名称'
      }
      else if ( e == 6){
        this.placeholder = '请输入归属地'
      }
      else if ( e == 7){
        this.placeholder = '请输入跟进内容'
      }

    },
    clearSelectKeyword() {
      this.params.keywords = ''
      this.params.mobile = ''
      this.params.cname = ''
      this.params.number = ''
      this.params.project_name = ''
      this.params.mobile_place = ''
      this.params.follow_keywords = ''
      this.handleSearch()
    },
    setParams(key) {
      let arr = ['keywords', 'mobile', 'cname', 'number', 'project_name','mobile_place','follow_keywords']
      arr.map(item => {
        if (item !== key) {
          this.params[item] = ''
        }
      })
    },
    handleKeywordSearch() {
      if (this.select_params.type == 1) {
        this.setParams('mobile')
        this.placeholder = '请输入手机号码'
        this.params.mobile = this.select_params.keywords.replace(/\s+/g,"");
      } else if (this.select_params.type == 2) {
        this.setParams('number')
        this.placeholder = '请输入客户编号'
        this.params.number = this.select_params.keywords
      } else if (this.select_params.type == 3) {
        if(this.recordstype == 'xiansuo'){
          this.placeholder = '请输入客户姓名'
          this.setParams('keywords')
          this.params.keywords = this.select_params.keywords
        }else{
          this.placeholder = '请输入线索内容'
          this.setParams('keywords')
          this.params.keywords = this.select_params.keywords
        }
      } else if (this.select_params.type == 4) {
        this.setParams('cname')
        this.placeholder = '请输入客户姓名'
        this.params.cname = this.select_params.keywords
      } else if (this.select_params.type == 5) {
        this.setParams('project_name')
        this.placeholder = '请输入项目名称'
        this.params.project_name = this.select_params.keywords
      } else if (this.select_params.type == 6) {
        this.setParams('mobile_place')
        this.placeholder = '请输入归属地'
        this.params.mobile_place = this.select_params.keywords
      } else if (this.select_params.type == 7) {
        this.setParams('follow_keywords')
        this.placeholder = '请输入跟进内容'
        this.params.follow_keywords = this.select_params.keywords
      }
      this.handleSearch()
    },
    debounce(fn, delay, immediate = false) {
      // 1.定义一个定时器, 保存上一次的定时器
      let timer = null
      let isInvoke = false
      // 2.真正执行的函数
      const _debounce = function (...ages) {
        // 取消上一次的定时器
        if (timer) clearTimeout(timer)

        // 判断是否需要立即执行
        if (immediate && !isInvoke) {
          fn.apply(this, ages)
          isInvoke = true
        } else {
          // 延迟执行
          timer = setTimeout(() => {
            // 外部传入的真正要执行的函数
            fn.apply(this, ages)
            isInvoke = false
          }, delay)
        }
      }

      return _debounce
    },
    handleScroll() {
      const page = document.getElementsByClassName("main-content")[0];
      this.scrollTop = page.scrollTop



      // let scrollTop = page.scrollTop
      // console.log(scrollTop, '滚动距离')
      // if (scrollTop > 100) {
      //   console.log(scrollTop);
      //   // 滚动到导航栏附近则克隆元素
      //   // this.cloneDom()
      // } else {
      //   // 否则删除克隆层 还原原始dom
      //   var origin = document.getElementById('stickyId')
      //   var originNewBox = document.getElementById('testCopyBox')
      //   if (originNewBox.hasChildNodes()) {
      //     origin.appendChild(this.originNode)
      //     originNewBox.removeChild(originNewBox.firstChild)
      //   }
      // }
    },
    getThirdSource() {
      this.$http.getThirdSourceList().then((res) => {
        if (res.status == 200) {
          this.third_source_list = res.data
        }
      })
    },
    changeThirdSource() {
      this.handleSearch()
    },
    Channel_number(){
      // console.log(this.params);
      this.getDataList()
    },
    Channel_Name(){
    //  console.log(this.params);
      this.getDataList()
    },
    // cloneDom() {
    //   var origin = document.getElementsByClassId('stickyId')
    //   this.originNode = origin
    //   var originNewBox = document.getElementById('testCopyBox')
    //   // 若原本有则清空
    //   if (originNewBox.hasChildNodes()) {
    //     originNewBox.removeChild(originNewBox.firstChild)
    //   }
    //   // 否则进行克隆
    //   originNewBox.appendChild(origin)
    // },
    handleResize() {
      // 获取屏幕宽度
      const filter = document.getElementById("filter")

      this.topHeight = filter.offsetHeight + 120
      this.getScreenWidth();
    },
    formatTelTime(val) {
      let wholeTime = 0
      let sencond = parseInt(val) || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((sencond % 3600) % 60) % 60;
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
      return wholeTime
    },
    getScreenWidth() {
      // 获取屏幕宽度
      //  window.innerWidth;
      //  console.log(window.innerWidth);
      if (window.innerWidth <= 1350) {
        this.buttonhidden = false
        this.is_small_system = true
        this.myButton1 = true
      } else {
        this.is_small_system = false
        this.buttonhidden = true
        this.myButton1 = false
      }
    },
    //获取部门成员信息
    getMemberList() {
      this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data;
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user,
          });
          this.recursionData(this.memberList);
          this.Not_duplicate_datalist = JSON.parse(
            JSON.stringify(this.datalist)
          );
          this.filteredData();
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      });
    },
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              });
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item);
            }
          });
          this.recursionData(data[key].subs);
        }
      }
    },
    //选中部门人员
    selecetedMember(e) {
      // console.log(e.checkedNodes, "成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.admin_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.admin_id = "";
      }
      this.show_member_list = false;
    },
    selecetedMember1(e) {
      // console.log(e.checkedNodes, "成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id1 = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.create_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.create_id = "";
      }
      this.show_member_list = false;
    },
    //关闭弹窗之间的回调
    cancels() {
      this.is_dialog_upload = false;
      this.upload_form = {
        type: 1,
        admin_id: "",
        create_id: "",
        file: "",
      };
    },
    checkStatus(item) {
      if (item.push_type == 2) {
        // 手动认领  
        // 潜在用户 我司成交的不可认领
        if (item.tracking_identify == 1) {
          return true
        }
        // 掉工转公标记的可以领取 
        if (item.public2_status > 0) {
          return false
        }
        // 潜在客户可以领取
        if (this.type == "qianzai") {
          return false
        }
        // 其他情况 不可领取 
        return true
      }
      return false
    },
    checkPermission(admin){
      if(admin.roles && admin.roles.length && admin.roles[0].name == '站长'){
        this.perms.douyinRoomUsers = true;
        this.perms.transViewPage = true;
      }else{
        //检查是否有查看抖音直播间用户权限
        this.$http.douyinRoomUsersPermission().then(res=>{
          if(res.status == 200){
            this.perms.douyinRoomUsers = res.data?.privilege == 1;
          }
        }).catch(e=>{})

        //检查是否有经营视图查看权限
        this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            this.perms.transViewPage = res.data.split(',').includes(String(admin.id));
          }
        })
      }
    },
    // 获取信息展示
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.checkPermission(res.data);

          this.status_id = res.data.id;
          this.selfID = res.data.id;
          // console.log(res.data);
          if (res.data.roles[0].name === "站长") {
            // console.log(res.data.roles[0].name);
            this.is_show_upload = true;
            this.show_right = true
          } else {
            this.getSiteCrmSetting(res.data.id);
          }
        }
      }).catch(() => {
        this.show_right = true
      });
    },
    getfounderinfo(){
      this.$http.getfounderinfo().then((res)=>{
        if(res.status === 200){
          // console.log(res.data);
          this.founderdata = res.data
        }
      })
    },
    // filterTime(val) {
    //     var date = new Date(val * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    //     var Y = date.getFullYear() + '-';
    //     var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
    //     var D = date.getDate() + ' ';
    //     var h = date.getHours() + ':';
    //     var m = date.getMinutes() + ':';
    //     var s = date.getSeconds();
    //     return Y+M+D+h+m+s;
    // },
    // 获取批量导入的crm站点设置
    getSiteCrmSetting(id) {
      this.$http.getAuthShow("batch_import_uid").then((res) => {
        if (res.status === 200) {
          let num = [];
          num.push(res.data);
          // console.log(num,"num",id)
          if (num.indexOf(id) != -1 || !res.data) {
            this.is_show_upload = true;

          }
        }
        this.show_right = true
      }).catch(() => {
        this.show_right = true
      });
    },
    // 快速编辑标签
    async fastEditLabel(row) {
      this.multipleSelection = []; // 清空
      this.multipleSelection.push(row.id); // 赋值客户id
      this.is_fast = true; // 设置为快速编辑标签
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        });
      });
      //console.log(row,"row");
      this.fastEdit_params.client_id = row.id;
      new Promise((resolve) => {
        // 获取客户标签列表
        if (!this.labels_list.length) {
          this.getLabelGroupNoPageNew();
        }
        if (!this.labels_list.length) {
          setTimeout(() => {
            resolve();
          }, 500);
        } else {
          resolve();
        }
      }).then(() => {
        this.show_Customer_label = true; // 显示模态框
        // console.log(this.labels_list,"this.labels_list");
        row.label.map((item) => {
          this.labels_list.map((list) => {
            list.label.map((arr) => {
              if (arr.name == item) {
                arr.check = true;
              }
            });
          });
        });
        this.$forceUpdate();
      });
    },
    resetLoudongSearch() {
      this.params.mobile = "";
    },
    handleSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    getCrmCustomerFollowNumber() {
      this.$http.getCrmCustomerFollowNumber().then((res) => {
        if (res.status === 200) {
          this.no_follow_number = res.data;
        }
      });
    },
    //获取项目列表
    getProjectList() { 
      let keyword = ""
      this.$http.getCrmProjectByConfig(keyword).then((res) => {
        if (res.status === 200) {
          this.project_list = res.data;
          console.log(this.project_list);
        }
      });
    },
    //项目名称
    customerproject_name(e){
      this.params.project_name = e
      this.getDataList(); // 获取最新数据
    },
    // changeTab(e, type) {
    //     if (type == 1) {
    //         this.params.status = e.id;
    //     } else {
    //         this.params.type = e.id;
    //     }
    //     this.params.page = 1;
    //     this.getDataList();
    // },
    getTypelist() {
      this.$http.getCrmCustomerTypeDataNopage().then((res) => {
        if (res.status === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
          // this.push_form.type = res.data.filter((item) => {
          //     return item.is_default;
          // })[0].id;
          // let cus_type = parseInt(this.$route.query.cus_type);
          // res.data.map((item) => {
          //     if (cus_type == 1 && item.title == "求购") {
          //         // this.params.type = item.id;
          //     }
          //     if (cus_type == 2 && item.title == "求租") {
          //         // this.params.type = item.id;
          //     }
          // });
        }
        // this.ponent_maintain_type = JSON.parse(JSON.stringify(res.data));
        // let i = this.customer_list_type.findIndex(
        //     (item) => item.id == this.params.c_type1
        // );
        // if (i != -1) {
        //     this.getDataLists(this.customer_list_type[i]);
        // }
      });
    },
    getLabelList() {
      this.$http.getLabelGroupNoPage().then((res) => {
        if (res.status === 200) {
          // console.log(res.data);
          this.ponent_maintain_label = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户类型列表
          this.label_list = res.data;
          // console.log(this.label_list, "查看");
          this.label_default_list = res.data;
        }
      });
    },
    getDataLists(item, index) {
      this.tabIndex = index;
      // console.log(item);
      // console.log(this.params);
      // this.params.c_type1 = item.id;

      // if(this.params.c_type1 ==1){
      //   console.log(1111);
      //   // this.params.c_type5 = item.id;
      // }
      // this.params.type = 0;
      this.params.page = 1;
      this.getDataList();
    },
    //获取客户列表
    getDataList() {
      this.is_table_loading = true;
      let params = Object.assign({}, this.params);
      if (params.type == 'xiansuo') {
        this.getXiansuo(params)
      } else {
        this.getList(params)
      }

    },
    getXiansuo(o_params) {
      let { end_date, start_date, mobile, keywords, per_page, page,refer_id,refer_name} = o_params
      let params = {
        end_date,
        start_date,
        mobile,
        keywords,
        per_page,
        page,
        refer_id,
        refer_name,
      }
      params.platform = this.source || 0
      if (params.start_date == "") {
        delete params.label
      }
      if (params.end_date == "") {
        delete params.end_date
      }
      // console.log(params);
      this.$http.getJingyingViewList(params).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      }).catch(() => {
        this.is_table_loading = false;
      })
    },
    getList(params) {
      // date_type: 0,//时间类型(1:创建时间,2:跟进时间,3:线索时间,4:更新时间,5:掉公时间,6:转公时间,7:跟客天数,8:带看时间)
      if (params.date_type == '') {
        delete params.date_type
      }
      // follow_type: 0,//跟进类型(1:已跟进,2:已认领,3:未跟进,4:待分配,5:已转公,6:已掉公,7:语音跟进)
      if (params.follow_type == '') {
        delete params.follow_type
      }
      // client_type: 0,//客户类型(1:公海客户,2:潜在客户,3:成员私客)
      if (params.client_type == 0 || params.client_type == "") {
        delete params.client_type
      }
      // yaoyue_type: 0,//邀约类型(1:查看电话,2:外呼(已接通),3:外呼(未接通),4:已带看,5:未带看,6:有复看)
      if (params.yaoyue_type == '') {
        delete params.yaoyue_type
      }
      // tracking_type: 0,//	状态类型(1:有效客户,2:无效客户,3:暂缓客户,4:我司成交,:5他司成交,6:未成交)
      if (params.tracking_type == '') {
        delete params.tracking_type
      }
      // level_id: 0,//客户等级
      if (params.level_id == '') {
        delete params.level_id
      }
      // admin_type: 0,//管理员类型(1:录入人,2:维护人,3:带看人,4成交人）
      if (params.admin_type == ''||params.admin_type == 5) {
        delete params.admin_type
      }
      // admin_id: 0,//管理员id
      if (params.admin_id == '') {
        delete params.admin_id
      }
      // department_id: 0,//部门id
      if (params.department_id == '') {
        delete params.department_id
      }
      // source_id: 0, // 客户来源
      if (params.source_id == '') {
        delete params.source_id
      }
      // type_id: 0,// 客户类型
      if (params.type_id == '') {
        delete params.type_id
      }
      if (params.call_status == '') {
        delete params.call_status
      }
      // label: 0,//标签
      if (params.label == '') {
        delete params.label
      }
      if (params.start_date == "") {
        delete params.start_date
      }
      if (params.end_date == "") {
        delete params.end_date
      }
      if (params.number == "") {
        delete params.number
      }
      // console.log(params);
      this.$http.getCrmCustomerClientListlastNew({ params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          // console.log(res.data.data);
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      }).catch(() => {
        this.is_table_loading = false;
      })
    },
    //客户级别列表
    getLevelData() {
      this.$http.getCrmCustomerLevelNopage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_level = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户级别列表
            this.level_listcopy = [...res.data, { title: "空", id: 0}];
            this.level_list = [...res.data, { title: "空", id: 4 }];
        }
      });
    },
    //获取客户状态
    getTrackingList() {
      this.$http
        .getCrmCustomerFollowInfo({ params: this.tracking_params })
        .then((res) => {
          if (res.status === 200) {
            this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
            // console.log(this.tracking_list);
          }
        });
    },
    resetXiansuoSearch() {
      this.params.keywords = "";
    },
    onClickSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    //根据时间搜索
    // onClickType(e, type) {
    //     switch (type) {
    //         case 1:
    //             this.params.source_id = e.id;
    //             break;
    //         case 2:
    //             this.params.tracking_id = e.id;
    //             break;
    //         case 3:
    //             this.params.is_bind = e.id;
    //             break;
    //         case 4:
    //             this.params.date_type = e.id;
    //             delete this.params.start_date; // 清空开始时间
    //             delete this.params.end_date; // 清空结束时间
    //             this.timeValue = ""; // 清空自定义时间绑定值
    //             break;
    //         case 5:
    //             this.params.level_id = e.id;
    //             break;
    //         case 6:
    //             this.params.type = e.id;
    //             break;
    //         default:
    //             break;
    //     }
    //     this.params.page = 1;
    //     this.getDataList();
    // },
    onChangeTime(e) {
      if (e && e.length >= 2) {
        if (e[1].endsWith("00:00:00")) {
          e[1] = e[1].slice(0, -8) + "23:59:59";
        }
        this.params.start_date = e ? e[0] : ""; // 赋值开始时间
        this.params.end_date = e ? e[1] : ""; // 赋值结束时间
      } else {
        this.params.start_date = ""; // 赋值开始时间
        this.params.end_date = ""; // 赋值结束时间
      }
      // this.params.start_date = e ? e[0] : ""; // 赋值开始时间
      // this.params.end_date = e ? e[1] : ""; // 赋值结束时间
      // this.params.date_type = 0; // 清空筛选时间
      // this.$refs.childRef.clearScreening(); // 重置筛选时间为全部
      this.params.page = 1; // 显示第一页
      this.getDataList(); // 获取最新数据
    },
    getSourceData() {
      this.$http.listcustomersourcenew().then((res) => {
        if (res.status === 200) {
          let arr = JSON.parse(JSON.stringify(res.data));
          arr.map((item)=>{
            if(item.children==0){
              delete item.children
            }
          })
          this.ponent_maintain_source = JSON.parse(JSON.stringify(arr)) // 深拷贝客户来源列表
          this.source_list = [ ...res.data];
          this.source_list.map((item)=>{
            if(item.children==0){
              delete item.children
            }
          })
          this.push_form.source_id = res.data.filter((item) => {
            return item.is_default == 1;
          })[0].id;
        }
      });
    },
    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    addDomain() {
      this.other_mobile.push({
        mobile: "",
      });
    },
    onClickLevel(item) {
      this.push_form.level_id = item.id;
    },
    onClickTypeClient(item) {
      this.push_form.type = item.id;
    },
    // onChangeKeywords() {
    //     this.params.page = 1;
    //     this.getDataList();
    // },
    selectionChange(e) {
      console.log(e);
      this.multipleSelectionname = e; // 赋值当前客户信息
      let arr = e.map((item) => {
        return item.id;
      });
      this.multipleSelection = arr; // 赋值当前客户的id
      // 只有在客户标签列表为空时请求数据
      if (!this.labels_list.length) {
        this.getLabelGroupNoPageNew();
      }
    },
    onClickDetailXiansuo(row) {
      console.log(row);
      if(row.status&&row.status==1){
        this.$http.getClientIdByPhone({ mobile: row.data.mobile }).then(res => {
        if (res.status == 200) {
          this.Quick_Edit({ id: res.data ,order:row.order,deal_user:row.deal_user,push_type:row.push_type})
        }
      })
      }else{
        this.$http.getClientIdByPhone({ mobile: row.mobile }).then(res => {
        if (res.status == 200) {
          this.onClickDetail({ id: res.data })
          }
        })
      }
      
    },
    async onClickDetail(row) {
      let res = await this.$http.getForceFollow()
      if (res.status == 200 && res.data && res.data.id > 0) {
        this.$confirm("您有未跟进的客户确认去跟进吗？").then(() => {
          let url = `/crm_customer_detail?id=${res.data.client_id}&type=seas&tel_follow_id=${res.data.id}`;
          this.$goPath(url);
        })
        return
      }
      let url = `/crm_customer_detail?id=${row.id}&type=seas`;
      this.$goPath(url);
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    //每页几条
    handleSizeChange(e){
      this.params.per_page = e
      this.getDataList();
    },
    onPageChangeQwFollow(e) {
      this.is_follow_params.page = e;
      this.getFollow();
    },
    isShowFollow(row) {
      this.is_follow_dialog = true;
      this.is_follow_params.client_id = row.client_id;
      this.getFollow();
    },
    formatGenkeDay(row, type) {
      let value = '--'
      if (type == 'genke') {
        if (row.last_follow_time && row.get_time) {
          let new_d = +new Date(row.last_follow_time)
          let old_d = +new Date(row.get_time)
          value = parseInt((new_d - old_d) / 1000 / 60 / 60 / 24)
        }
      }
      if (type == 'chengjiao') {
        if (row.created_at && row.deal_time) {
          let new_d = +new Date(row.deal_time)
          let old_d = +new Date(row.created_at)
          // console.log(new_d);
          value = parseInt((new_d - old_d) / 1000 / 60 / 60 / 24)
        }
      }
      return value
    },
    // crm客户跟进
    getFollow() {
      this.is_follow_loading = true;
      this.$http
        .getCrmCustomerFollowData({ params: this.is_follow_params })
        .then((res) => {
          this.is_follow_loading = false;
          if (res.status === 200) {
            this.is_follow_data = res.data.data;
            this.is_follow_params.total = res.data.total;
          }
        });
    },
    // onClickForm(e) {
    //   this.$http.setCrmCustomerData(e).then((res) => {
    //     if (res.status === 200) {
    //       this.$message.success("操作成功");
    //       this.is_push_customer = false;
    //       this.form = {};
    //       this.form1 = {};
    //       this.params.page = 1;
    //       this.getDataList();
    //     }
    //   });
    // },
    onClickForm() {
      if (
        this.push_form.label &&
        this.push_form.label != undefined &&
        typeof this.push_form.label !== "string"
      ) {
        this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
      }
      if (this.other_mobile.length > 0) {
        let arr = this.other_mobile.map((item) => {
          return item.mobile;
        });
        let othertel = arr.filter((item, index) => {
          if (index) {
            return item;
          }
        });
        this.push_form.mobile = arr[0];
        this.push_form.subsidiary_mobile = othertel.join(",");
      }
      if (!this.push_form.mobile) {
        this.$message.error("请检查联系方式");
        return;
      }
      if (!this.push_form.cname) {
        this.$message.error("请检查客户姓名");
        return;
      }
      if (!this.push_form.sex) {
        this.$message.error("请检查客户性别");
        return;
      }
      if (!this.push_form.level_id) {
        this.$message.error("请检查客户等级");
        return;
      }
      if (!this.push_form.type) {
        this.$message.error("请检查客户类型");
        return;
      }
      if (!this.push_form.source_id) {
        this.$message.error("请检查客户来源");
        return;
      }
      this.is_button_loading = true;
      this.$http.setCrmCustomerDataV2(this.push_form).then((res) => {
        this.is_button_loading = false;
        this.is_push_customer = false;
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
        } else if (res.status === 422) {
          const cus_id =
            res.data.data &&
              res.data.data.id != "" &&
              res.data.data.id != undefined
              ? res.data.data.id
              : 0; // 赋值客户id
          // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
          if (
            res.data.data &&
            res.data.data.follow_id &&
            res.data.data.follow_id != undefined &&
            res.data.data.follow_id != 0
          ) {
            this.$confirm("客户号码已有维护人，无法重复录入。", "提示", {
              confirmButtonText: "立即查看",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                this.$goPath(url); // 跳转客户详情
              })
              .catch(() => {
                return;
              });
          } else {
            // 该客户没有维护跟进人时触发
            this.$confirm("客户号码已存在，认领后可跟进。", "提示", {
              confirmButtonText: "立即认领",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.$http
                  .getCrmCustomerPublick({ ids: cus_id + "" })
                  .then((res) => {
                    if (res.status === 200) {
                      this.$message.success("认领成功");
                      let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                      this.$goPath(url); // 跳转客户详情
                    }
                  });
              })
              .catch(() => {
                return;
              });
          }
        }
      });
    },
    // 折叠面板
    onChangeCollapse() {
      // this.is_collapse = !this.is_collapse;
    },
    onClickGet(row) {
      this.$confirm("是否认领该客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.getCrmCustomerPublick({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("认领成功");
              let url = `/crm_customer_detail?id=${row.id}&type=my`;
              this.$goPath(url);
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    // handleChangeLabel() {
    //   this.params.page = 1;
    //   this.getDataList();
    // },
    douview(){
      this.$goPath(`/ananchorpresent`);
    },
    outboundview(){
      this.$goPath(`/outbound`)
    },
    //带看数据视图
    takelook(){
      this.$goPath(`/lookdatalist`)
    },
    //回访数据视图
    Followupview(){
      this.$goPath(`/Followupview`)
    },
    //维护数据视图
    maintenanceview(){
      this.$goPath(`/maintenanceview`)
    },
    leftla() {
      this.buttonhidden = true
      this.myButton = true
      this.myButton1 = false
    },
    Rightdrag() {
      this.myButton1 = true
      this.buttonhidden = false
      this.myButton = false
    },
    getFile() {
      this.is_dialog_upload = true;
      this.getMemberList();
      // this.$refs.file.click();
    },
    handleSuccessAvatarTemporary(response) {

      this.handleFileUpload(response.url)
    },
    // 获取文件
    handleFileUpload(response) {
      // 阻止发生默认行为
      event.preventDefault();
      let url = response;
      let formData = new FormData();
      formData.append("url", url);
      formData.append("admin_id", this.upload_form.admin_id || 0);
      formData.append("type", this.upload_form.type);
      formData.append("type_id", this.upload_form.type_id);
      formData.append("source_id", this.upload_form.source_id);
      formData.append("create_id", this.upload_form.create_id || 0);
      if (Array.isArray(this.upload_form.label)) {
        formData.append("label", this.upload_form.label.join(","));
      } else {
        formData.append("label", this.upload_form.label);
      }
      formData.append("remark", this.upload_form.remark);
      // this.formData.get("file");
      this.onUpload(formData);
    },


    // 上传文件
    onUpload(formData) {
      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      // this.$message.success("正在上传...");
      //  修改为传url
      this.$http.uploadCrmCustomerDataURL(formData).then((res) => {
        // this.$http.uploadCrmCustomerData(formData).then((res) => {
        if (res.status === 200) {
          // console.log(res.data);
          // 如果只有新增
          let text =
            "导入成功，新增客户数量" +
            res.data.success_num +
            "条,系统重复客户数量" +
            res.data.crm_repeat_num +
            "条,表格重复客户数量" +
            res.data.excel_repeat_num +
            "条,失败客户数量" +
            res.data.error_num +
            "条";
          this.loading.close();
          this.$confirm(text, "提示", {
            confirmButtonText: "查看详情",
            cancelButtonText: "取消",
            type: "success",
          })
            .then(() => {
              // console.log(1111111)
              // console.log(res.data.task_id)
              this.params.page = 1;
              this.getDataList();
              this.is_dialog_upload = false;
              this.is_loading = false;
              let url = `/crm_customer_task_list?task_id=` + res.data.task_id;
              this.$goPath(url); // 跳转客户详情
            })
            .catch(() => {
              this.params.page = 1;
              this.getDataList();
              this.is_dialog_upload = false;
              this.is_loading = false;
            });
        } else {
          this.loading.close();
        }
      });
    },
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/client_template_new.xls?f=" +
        +new Date()
      );
    },
    //表单重置
    reset() {
      this.push_form = {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        label: "",
        // intention_street: "",
        remark: "",
      };
      this.other_mobile = [{ mobile: "" }];
    },
    //取消
    cancel() {
      this.reset();
      this.is_push_customer = false;
    },

    getSelectWidth(item, LabelList) {
      var text_temp = "",
        text_width = 0,
        text_el = null;
      var current_option = "";
      if (LabelList) {
        current_option =
          item.label && item.label.find((arr) => arr.id === LabelList[0]);
      } else {
        current_option = false;
      }
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${item.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text");
      text_width = text_el.offsetWidth;
      text_el.remove();
      return text_width + 57 + "px";
    },
    //回访记录
    Follow_up_records(type, other) {
      this.params.date_type = 0
      if(type==1){
        this.params.date_type = 2
      }
      if(type==6){
        this.recordstype = 6
        return 
      }
      if (other >= 0) {
        this.douyinview = false
        this.outview = false
        this.recordstype = other
      } else {
        this.douyinview = false
        this.outview = false
        this.recordstype = type
      }
      if(type == "xiansuo"&&other=="-1"){
        this.douyinview = true
        this.outview = false
      }
      if (type == "xiansuo" && this.third_source_list.length == 0) {
        this.getThirdSource()
      }
      this.params.type = type
      this.params.page = 1
      this.getDataList(); // 获取最新数据
    },
    //带看记录
    Take_look_record() {
      this.recordstype = 2
      this.params.type = 2
      this.params.page = 1
      this.params.date_type = 8
      this.douyinview = false
      this.outview = false
      this.getDataList(); // 获取最新数据
    },
    //外呼记录
    Outbound_record() {
      this.params.date_type = 0
      this.recordstype = 3
      this.params.type = 3
      this.params.page = 1
      this.douyinview = false
      this.outview = true
      this.getDataList(); // 获取最新数据
    },
    //时间类型
    time_type(e) {
      // console.log(e)
      this.params.date_type = e
      this.getDataList(); // 获取最新数据
    },
    //跟进类型
    follow_through(e) {
      // console.log(e);
      this.params.follow_type = e
      this.getDataList(); // 获取最新数据
    },
    //全部客户
    all_Customers_through(e){
      this.params.client_type = e
      if (this.params.tracking_type == 2 && this.params.client_type == 2) {
          // 当条件满足时，添加批量删除项
          this.cus_list.push({ id: 9, name: "批量删除" });
      } else {
          // 否则，过滤掉 id 为 9 的项
          this.cus_list = this.cus_list.filter(item => item.id !== 9);
      }
      this.getDataList(); // 获取最新数据
    },
    //邀约类型
    invite_type(e) {
      this.params.yaoyue_type = e
      this.getDataList(); // 获取最新数据
    },
    //状态类型
    customer_status(e) {
      this.params.tracking_type = e.join(",")||""
      if (this.params.tracking_type == 2 && this.params.client_type == 2) {
          // 当条件满足时，添加批量删除项
          this.cus_list.push({ id: 9, name: "批量删除" });
      } else {
          // 否则，过滤掉 id 为 9 的项
          this.cus_list = this.cus_list.filter(item => item.id !== 9);
      }
      this.getDataList(); // 获取最新数据
    },
    //客户等级
    customer_grade(e) {
      // if (e&&(this.website_id==109||this.website_id==176)) {
        this.params.level_id = e.join(",")||""
      // } else {
      //   delete this.params.level_type
      //   this.params.level_id = e
      // }
      // if (e == 4) {
      //   this.params.level_type = e
      //   delete this.params.level_id
      // } else {
      //   delete this.params.level_type
      //   this.params.level_id = e
      // }

      this.getDataList(); // 获取最新数据
    },
    //部门，成员
    loadFirstLevelChildren(value) {
      console.log(this.member_value,"122222222222222");
      if(value){
        this.params.admin_type = value[0]
      if(value[1]){
        this.params.admin_id = value[1]
      }else{
        this.params.admin_id = ''
      }
      }else{
        this.params.admin_type = ""
        this.params.admin_id = ""
      }
      this.params.department_id = ""
      this.getDataList(); // 获取最新数据
    },
    // 获取成员的接口（新）
    MembersNEW(){
      this.$http.getDepartmentMemberListNew().then((res)=>{
        if(res.status==200){
          this.member_listNEW.map(item => {
          item.subs = res.data
         })
        }
      })
    },
    //客户来源
    customer_source(e) {
      // this.params.source_id = e[e.length - 1]
      this.params.source_id = e.join(",")||""
      this.getDataList(); // 获取最新数据
    },
    //客户类型
    client_type(e) {
      this.params.type_id = e
      this.getDataList(); // 获取最新数据
    },
    //通话状态
    customerstatus_type(e){
      this.params.call_status = e
      this.getDataList(); // 获取最新数据
    },
    // 点击全部部门
    Reqdepartment(){
      this.getDepartmentList(); // 获取部门
    },
    //BI数据面板
    tomianban(){
      this.$goPath("Big_Data_Panel")
    },
    //清空
    empty() {      
      this.labeldata = [], 
      this.customerLabelListcopy = [],
      this.value = ''
      this.params.date_type = 0
      this.timeValue = ''
      this.params.follow_type = ''
      this.params.client_type = ''
      this.follow_up_value = ''
      this.params.yaoyue_type = ''
      this.Invitation_value = ''
      this.params.tracking_type = ''
      this.customer_statusvalue = ''
      this.grade_value = ''
      this.member_value = ''
      this.source_value = ''
      this.typeLabel_value = ''
      this.customerstatus_value = ''
      this.params.level_id = ''
      this.params.admin_type = ''
      this.params.admin_id = ''
      this.params.department_id = ''
      this.params.source_id = ''
      this.params.type_id = ''
      this.params.label = ''
      this.params.start_date = ""
      this.params.end_date = ""
      this.select_params.type = 1
      this.select_params.keywords = ''
      this.params.mobile = ""
      this.params.keywords = ''
      this.source = ''
      this.params.refer_id =''
      this.params.refer_name = '' 
      this.selectedMember = ""//清空搜索成员
      this.getDataList(); // 获取最新数据
    },
    Refresh() {
      //   window.location.reload()
      // this.params.page = 1
      this.getDataList(); // 获取最新数据
    },
    //一个个删除标签
    dellabeld(index,id){
        if (index > -1 && index <  this.labeldata.length) {
          // 如果索引值在数组长度范围内，则可以删除元素
          this.labeldata.splice(index, 1);
        }
       let labelid = this.customerLabelListcopy.indexOf(id);
        if (labelid > -1) {
          this.customerLabelListcopy.splice(labelid, 1);
        }
      this.params.label = this.customerLabelListcopy.join(",")
      this.params.page = 1;
      this.getDataList();
    },
    //清空标签
    clearlabel(){
      this.labeldata = []
      this.customerLabelListcopy = []
      this.params.label = ""
      this.params.page = 1;
      this.getDataList();
    },
    //选中客户标签
    handleCommand(item, label) {
      console.log(item, label);
      console.log(this.labeldata);
      console.log(this.customerLabelListcopy);
      // 检查是否已存在相同的 item 和 label 组合
      const isDuplicate = this.labeldata.some(data => data.id === item.id);
      console.log(isDuplicate);
      if (!isDuplicate) {
        this.labeldata.push({ name: item.name, title: label.name,id:item.id });
        this.customerLabelListcopy.push(item.id )
      }
     
        this.params.label = this.customerLabelListcopy.join(",")
        this.params.page = 1;
        console.log(this.params);
        this.getDataList();
    },
    //客户标签// 检索客户标签发生改变时触发
    changeCustomerLabel(val) {
      // console.log(val);
      this.is_all = false;
      // 父级标签id赋值
      let label_num = Object.keys(this.customerLabelList).length;
      // 如果选择的标签大于1就删除之前选择的标签
      if (label_num > 1) {
        delete this.customerLabelList[this.changeParentLabel];
      }
      this.changeParentLabel = val[2].id;
      // 更新数据
      this.params.label = val[0];
      this.params.page = 1;
      // console.log(this.params);
      this.getDataList();
    },


    getAllLabelList() {
      this.is_all = true;
      this.changeParentLabel = "";
      this.customerLabelList = {};
      delete this.params.label;
      this.params.page = 1;
      this.getDataList();
    },
    // 开始导入
    startImport() {
      this.is_loading = true;
      if (
        this.upload_form.type_id == "" ||
        this.upload_form.type_id == undefined
      ) {
        this.upload_form.type_id = 0;
      }
      if (
        this.upload_form.source_id == "" ||
        this.upload_form.source_id == undefined
      ) {
        this.upload_form.source_id = 0;
      }
      // 处理为正常部门成员id
      if (this.upload_form.admin_id.toString().length >= 6) {
        this.upload_form.admin_id = parseInt(
          this.upload_form.admin_id.toString().slice(0, 3)
        );
      }
      // console.log(this.upload_form);
      // this.$refs.file.click();
      //唤起上传文件组件
      // this.$refs.uploadBox.$children[0].$refs.input.click();
      this.$refs["upload"].$refs["upload-inner"].handleClick()
      this.is_loading = false;
    },
    // 清除当前选择成员
    delName() {
      this.uploadAdmin_id = "";
      this.upload_form.admin_id = 0;
    },
    // 点击设置客户标签按钮
    setCustomerLabel() {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      this.show_Customer_label = true; // 显示模态框
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        });
      });
    },
    // 选中标签
    checkChangeLabels(index0, index) {
      let that = this;
      that.labels_list[index0].label[index].check =
        !that.labels_list[index0].label[index].check;
      this.$forceUpdate();
    },
    // 确定更改客户标签
    confirmSelected() {
      this.is_loading = true;
      let num = []; // 已选客户标签容器
      // 遍历出勾选中的客户标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          if (list.check) {
            num.push(list.id);
          }
        });
      });
      // 赋值已选中的客户标签参数
      if (num && num.length) {
        this.confirm_batch_list.label = num.join(",");
      }
      // 赋值已选中的客户id参数
      if (this.multipleSelection && this.multipleSelection.length) {
        this.confirm_batch_list.ids = this.multipleSelection.join(",");
      }
      // 请求接口
      this.$http
        .newbatchSetLabelGroup(this.confirm_batch_list)
        .then((res) => {
          if (res.status == 200) {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.show_Customer_label = false; // 关闭模态框
            this.is_loading = false;
            this.getDataList(); // 获取最新数据
          } else {
            this.is_loading = false;
          }
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
    // 导入成员当获取焦点时触发
    focusSelete() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_member_list = true;
    },
    focusSelete1() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_member_list1 = true;
    },
    // 获取客户标签列表
    getLabelGroupNoPageNew() {
      // 获取客户标签列表
      this.$http.getLabelGroupNoPageNew().then((res) => {
        if (res.status == 200) {
          if (res.data.qiwei_tag && res.data.qiwei_tag.length) {
            res.data.qiwei_tag.map((item) => {
              item.label = item.taggroup;
              delete item.taggroup;
            });
          }
          this.labels_list = res.data.qiwei_tag.concat(res.data.system_tag);
        }
        // 格式化数据-添加check属性
        if (this.labels_list && this.labels_list.length) {
          this.labels_list.map((item) => {
            if (item.label) {
              item.label.map((list) => {
                list.check = false;
              });
            }
          });
        }
      });
    },
    // 查询归属地
    HomeAddress(row) {
      this.$http.inquireHomeAddress(row.id).then((res) => {
        if (res.status == 200) {
          this.getDataList(); // 获取最新数据
        }
      });
    },
    // 部门选取发生改变
    changePopDepar(val) {
      this.member_value = "";
      if (val != null) {
        this.filtrMember = [];
        this.filtrDepartMember(this.memberList, val);
        this.filtrMember = this.filtrMember.filter((item) => {
          return item.id.toString().length <= 4;
        });
        this.searchDepart() 
      } else {
        this.params.department_id = 0
        this.getDataList();
        this.filteredData();
      }
    },
    // 选中对应部门，遍历出部门成员
    filtrDepartMember(data, val) {
      for (let key in data) {
        // console.log(data[key].id,val,"执行",data[key].id == val)
        if (data[key].id == val) {
          if (data[key].subs) {
            data[key].subs.map((item) => {
              if (item.user) {
                item.user.map((list) => {
                  this.filtrMember.push(list);
                });
              } else if (item.user_name) {
                this.filtrMember.push(item);
              }
            });
          }
        }
        this.filtrDepartMember(data[key].subs, val);
      }
    },
    // 部门成员去重
    filteredData() {
      const uniqueIds = {};
      const filtered = [];
      for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
        const item = this.Not_duplicate_datalist[i];
        if (!uniqueIds[item.id]) {
          uniqueIds[item.id] = true;
          filtered.push(item);
        }
      }
      // console.log(filtered);
      this.filtrMember = filtered;
      this.member_list.map(item => {
        item.subs = filtered
      })
    },
    // 获取全部的部门
    getCrmDepartmentList() {
      // this.selectedMember = ""//清空搜索成员
      // this.params.admin_id = 0; // 清空搜索内容
      // this.params.department_id = 0; // 清空搜索内容
      this.getDepartmentList(); // 获取部门
      // 获取部门成员
      if (!this.datalist.length) {
        this.getMemberList();
      }
      // if (!this.AllDepartment.length) {
      //   this.$http.getCrmDepartmentList().then((res) => {
      //     if (res.status == 200) {
      //       this.AllDepartment = res.data;
      //     }
      //   })
      // }
    },
    // 获取部门
    getDepartmentList() {
      if (!this.AllDepartment.length) {
        this.$http.getCrmDepartmentList().then((res) => {
          if (res.status == 200) {
            this.AllDepartment = res.data;
          }
        });
      }
    },
    // 按照部门搜索
    searchDepart() {
      if (this.member_value == "" || this.member_value == undefined) {
        this.params.admin_id = 0; // 赋值0
        this.params.admin_type = 0
      }
      this.getDataList();
    },
    // 按照成员搜索
    searchMember() {
      // 处理id为前三位
      if (this.params.admin_id.toString().length >= 6) {
        this.params.admin_id = parseInt(
          this.params.admin_id.toString().slice(0, 3)
        );
      }
      this.getDataList();
    },
    // 搜索部门成员发生改变时触发
    changeSearchMember() {
      // 如果搜索的参数为空或undefined
      if (this.selectedMember == "" || this.selectedMember == undefined) {
        this.params.admin_id = 0; // 赋值0
      } else {
        this.params.admin_id = this.selectedMember; // 有值则赋值
      }
    },
    // 是否需要短信验证
    verification(){
      this.$http.determinetextmessage().then(res=>{
        if(res.status==200){
          this.needs_testres=res.data
        }
      })
    },
    //导出客户
    leading_out() {
        if(this.needs_testres==0){
          this.$confirm('此次导出需要短信验证, 是否验证?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.showneedstestres = true
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消验证'
          });          
        });
        return
        }
        // if(this.website_id==109){
      if( !this.params.start_date&&! this.params.end_date){
        this.$message({
          type:"warning",
          message:"请选择时间范围。"
        })
        return
      }
      // if (this.params.start_date && this.params.end_date) {
      //   const startDate = new Date(this.params.start_date.replace(/-/g, "/")); // 获取开始时间，并将其转换为Date对象
      //   const endDate = new Date(this.params.end_date.replace(/-/g, "/")); // 获取结束时间，并将其转换为Date对象、
        
        // // 获取开始时间和结束时间的年份和月份
        // const startYear = startDate.getFullYear();
        // const startMonth = startDate.getMonth();
        // const endYear = endDate.getFullYear();
        // const endMonth = endDate.getMonth();

        // // 如果开始时间和结束时间跨年或跨月，则返回null
        // if (startYear !== endYear || startMonth !== endMonth) {
        //   this.$message({
        //     type:"warning",
        //     message:" 范围不可跨月！ "
        //   })
        //   return
        // }

        // 计算两个日期对象之间的毫秒数差值
        // const timeDiff = endDate.getTime() - startDate.getTime();

        // 将毫秒数差值转换为天数
        // const days = Math.ceil(timeDiff / (1000 * 3600 * 24));
        // console.log(days);
        // if(days>189){
        //   this.$message({
        //     type:"warning",
        //     message:" 最多可选范围 189天 "
        //   })
        //   return
        // }
      // }
      this.$confirm("确定要导出吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Loading.service({
          text: "正在处理表格数据 请稍候。。。",
        });
        // console.log(this.params);
        if(this.params.keywords == ""){
          delete this.params.keywords
        }
        if(this.params.client_type == ""){
          delete this.params.client_type 
        }
        if(this.params.follow_type == ""){
          delete this.params.follow_type 
        }
        if(this.params.yaoyue_type == ""){
          delete this.params.yaoyue_type  
        }
        if(this.params.admin_type == ""){
          delete this.params.admin_type 
        }
        if(this.params.type_id == ""){
          delete this.params.type_id 
        }
        if(this.params.admin_id == ""){
          delete this.params.admin_id
        }
        if(this.params.mobile == ""){
          delete this.params.mobile
        }
        if(this.params.department_id == ""){
          delete this.params.department_id
        }
        if(this.params.refer_id==""){
          delete this.params.refer_id
        }
        if(this.params.refer_name ==""){
          delete this.params.refer_name 
        }   
        if(this.params.admin_type == 5){ 
          delete this.params.admin_type 
        }
        // let apiA = "leadingoutCustomers"
        // if(this.website_id==109){
          let apiA = "newleadingoutCustomers"
        // }
        this.$http[apiA](this.params).then((res) => {
          Loading.service().close()
          if (res.status == 200) {
            // if(this.website_id==109){
              this.$confirm('导出正在进行, 是否前往操作记录查看?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                   this.$goPath("/crm_customer_export_list")
                }).catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消查看'
                  });          
                });
                // this.$goPath("/crm_customer_export_list")
            // }else{
              // window.location.href = res.data.url;
            // }
          }
        }).catch(() => {
          Loading.service().close()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        });
      });
    },
    //倒计时
    updateCountdown() {
      if (this.countdown > 0) {
        setTimeout(() => {
          this.countdown -= 1;
          this.updateCountdown();
        }, 1000);
      } else {
        // 倒计时结束
        this.counting = false;
        this.countdown = 60;
      }
    },
    //发送短信验证
    sendingcode(){
       this.$http.sendingcode().then(res=>{
         if(res.status==200){
           this.$message({
             type:"success",
             message:"发送成功！"
           })
           if (!this.counting) {
            this.counting = true;
            // 开始倒计时
            this.updateCountdown();
          }
         }
       })
     },
    //短信验证框确定
    confirm(){
       this.$http.test_verifycode(this.ruleForm).then(res=>{
         if(res.status==200){
           this.$message({
             type:"success",
             message:"验证成功！"
           })
          this.needs_testres = 1
          this.showneedstestres = false
          this.leading_out()
         }
       })
     },
 
    // 转交客户
    TransferCustomer(item) {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      if(this.multipleSelection.length&&this.multipleSelection.length>50){
        return this.$message({
          message: "每次最多选择50个客户",
          type: "warning",
        });
      }
      
      this.transfer_type = false; // 隐藏pop框
      if (item.id == 1) {
        // 如果是转交指定维护人
        this.is_transfer_customer = true;
        this.changetitle = "转交客户",
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      } else if (item.id == 2) { 
        this.changetitle = "复制客户",
        this.is_transfer_customer = true;
      }else if(item.id == 3){
        this.$refs.modifylevel.open(this.multipleSelection,item.id)
      }else if(item.id == 4){
        console.log(1212);
        this.$refs.customersource.open(this.multipleSelection)
      }else if(item.id == 5){
        this.$refs.modifylevel.open(this.multipleSelection,item.id)
      }else if(item.id == 6){
        this.$refs.modifylevel.open(this.multipleSelection,item.id)
      }else if(item.id == 7){
        this.$refs.modifylevel.open(this.multipleSelection,item.id)
      }else if(item.id == 8){
        this.$refs.automatic.open(this.multipleSelection)
      }else if(item.id == 9){
        this.batchDelete()
      }
 
      // if(!item.id){
      //   this.is_transfer_customer = true;
      //   this.changetitle = "转交客户",
      //   this.admin_params.user_name = "";
      //   this.admin_params.page = 1;
      // }
    },
    //批量删除客户
    batchDelete(ids){
      let params = {}
      if(ids&&ids.length){
        params.ids = ids.join(",");
      }else{
        params.ids = this.multipleSelection.join(",");
      }
      this.$confirm('此操作将永久删除该客户, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.batchdeletecustomer(params).then(res=>{
            if(res.status==200){
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.getDataList(); // 获取最新数据 
              if(ids&&ids.length){
                this.drawer = false
              }
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    //复制客户到同事的流转客
    oncopycrm(e,id){
      this.$confirm(`是否将所选客户复制到【${e.user_name}】的流转客？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 将数组转换字符串
          if(id==1){
            this.c_id = this.multipleright.toString(); // 数组转换字符串
          }else{
            this.c_id = this.multipleSelection.join(",");
          }
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomercopy({
              user_ids: e.id.toString(),
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.right_transfer_customer = false;
                this.is_transfer_customer = false
                if (id==1) {
                  this.$refs.childRef.getDetail()
                }else{
                 this.getDataList(); // 获取最新数据 
                }
              }
            });
        })
        .catch((err) => {
          console.log(err);
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    // 搜索转交人
    onAdminSearch() {
      this.admin_params.page = 1;
      this.getAdmin();
    },
    getAdmin() {
      this.$http
        .getUserList(
          this.admin_params
        )
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    // 确定转让客户
    onZhuanrang(e,id) {
      this.$confirm(`是否将所选客户转交给【${e.user_name}】？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if(id==1){
            this.c_id = this.multipleright.toString(); // 数组转换字符串
          }else{
            this.c_id = this.multipleSelection.join(","); // 数组转换字符串
          }
          // 将数组转换字符串
          // this.c_id = this.multipleSelection.join(",");
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomerZhuanrang({
              be_transfer_id: e.id,
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.is_transfer_customer = false;
                this.getDataList(); // 获取最新数据
                this.$refs.childRef.getDetail()
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    //
    async startProcessing(row,id) {
      try {
        // 显示确认对话框
        await this.$confirm(`是否将所选客户转交给【${row.user_name}】？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        // 根据 id 确定 c_id
        if (id === 1) {
          this.c_id = this.multipleright.toString(); // 数组转换字符串
        } else {
          this.c_id = this.multipleSelection.join(","); // 数组转换字符串
        }
        this.zuanjiaoid = row.id;
        // 显示加载提示
        this.loadingInstance = Loading.service({ text: `加载中...` });
        // 处理批次
        await this.processBatches(0);
      } catch (error) {
        if (error === 'cancel') {
          // 点击取消时控制台打印已取消
          console.log("已取消");
        } else {
          // 处理过程中出现错误
          console.error('处理过程中出现错误:', error);
        }
      } finally {
        // 确保 loadingInstance 有效后再关闭
        if (this.loadingInstance && typeof this.loadingInstance.close === 'function') {
          this.$message.success("操作成功");
          this.is_transfer_customer = false;
          this.getDataList(); // 获取最新数据
          this.loadingInstance.close();
        }
      }
    },

    async processBatches(startIndex) {
      const totalClients = this.multipleSelection.length;
      // 计算总批次数
      const totalBatches = Math.ceil(totalClients / this.batchSize);
      let completedBatches = startIndex / this.batchSize;
      // 如果索引超出范围，返回
      if (startIndex >= totalClients) {
        return;
      }
      // 获取当前批次的客户端
      const batch = this.multipleSelection.slice(startIndex, startIndex + this.batchSize);
      try {
        // 处理当前批次的所有客户端请求
        await this.sendRequest(batch);
        // 更新完成的批次数
        completedBatches++;
        const progress = Math.round((completedBatches / totalBatches) * 100);
        this.$message({
          showClose: true,
          message: `已完成${progress}%`,
          type: 'success',
        });
      } catch (error) {
        throw error; // 重新抛出错误以停止处理
      }
    
      // 继续处理下一个批次
      if (startIndex + this.batchSize < totalClients) {
        await this.processBatches(startIndex + this.batchSize);
      }
    },
    async sendRequest(client) {
      console.log(client);
      // 这里是你请求的代码
      // 示例：
      return this.$http
            .setCrmCustomerZhuanrang({
              be_transfer_id: this.zuanjiaoid,
              ids: client.join(","),
            })
            .then((res) => {
              if (res.status === 200) {
              }
            });
    },
    PersPageChange(e) {
      this.admin_params.page = e;
      this.getAdmin();
    },
    // 关闭客户审批模态框
    closeExamine() {
      this.show_Examine_dialog = false; // 关闭模态框
    },
    // 客户审批提交成功后的回调函数
    submitExamineAfter() {
      this.show_Examine_dialog = false;
      this.getDataList(); // 刷新客户列表
    },
    // 获取客户状态
    getStatus() {
      if (!this.status_list.length) {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.getStutas_params })
          .then((res) => {
            if (res.status === 200) {
              this.status_list = res.data;
              this.status_list.map((item) => {
                if (item.title == "有效客户") {
                  item.value_name = 1;
                } else if (item.title == "无效客户") {
                  item.value_name = 2;
                } else if (item.title == "暂缓客户") {
                  item.value_name = 3;
                } else {
                  item.value_name = 1;
                }
                return item;
              });
              this.copy_status_list = JSON.parse(
                JSON.stringify(this.status_list)
              ); // 深拷贝客户状态
            }
          });
      }
    },
    setStatus(row) {
      this.status_list = JSON.parse(JSON.stringify(this.copy_status_list)); // 重新赋值客户状态
      let delIndex = "";
      this.status_list.map((item, index) => {
        if (item.id == row.tracking.id) {
          delIndex = index;
        }
      });
      if (typeof delIndex == "number") {
        this.status_list.splice(delIndex, 1);
      }
    },
    // 点击更改客户状态
    onClickFollowStatus(row, item) {
      // 审批无需审核 is_state == 2
      if (row.is_state == 2) {
        let examine = false;
        if (row.state_list && row.state_list.length) {
          // 如果state_list中有当前要更改的状态
          row.state_list.map((list) => {
            if (list == item.id) {
              examine = true;
            }
          });
        }
        if (examine) {
          // 判断是不是客户管理员， 是就不审批直接更改
          if (row.admin_list && row.admin_list.length) {
            // 遍历客户管理员
            const isLargeNumber = (item) => item == this.selfID;
            let is_admins = row.admin_list.findIndex(isLargeNumber);
            // 如果是客户管理员无需审批
            if (is_admins >= 0) {
              this.setCrmCustomerStatus(row, item); // 无需审批
            } else {
              this.requiresExamineStatus(row, item); // 需要审批
            }
          }
        } else {
          // 不走审批
          this.setCrmCustomerStatus(row, item); // 无需审批
        }
      } else {
        this.$message.warning("当前客户状态不可以进行审批");
      }
      // console.log(row, "row", item, 'item')
    },
    //打开项目库
    openproject(row){
      this.$refs.Projectlibrary.open(row);
    },
    // 弹框拨打电话详情
    play1(row) {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      if (this.currI !== row && this.currI) {
        audios.pause();
        this.$set(this.currI, 'isPlaying', false);
        audios.src = row.last_call_info.record_url
        this.$forceUpdate();
      }
      if (row.isPlaying) {
        audios.pause();
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      } else {
        if (this.currI !== row) {
          audios.src = row.last_call_info.record_url
        }
        audios.play();
        this.currI = row;
        this.$set(row, 'isPlaying', true);
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      }, row.last_call_info.duration * 1000);
    },
    // 无审批修改客户状态
    setCrmCustomerStatus(row, item) {
      this.$http
        .setCrmCustomerStatus({ id: row.id, tracking_id: item.id })
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
            this.getDataList(); // 刷新数据
          }
        });
    },
    // 需审批修改客户状态
    requiresExamineStatus(row, item) {
      document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
      this.Examine_type = 19; // 切换类型为修改客户状态
      this.show_Examine_dialog = true; // 显示审批模态框
      this.ponent_Examine_data = row; // 赋值客户信息
      this.ponent_Examine_stutas = item; // 选择的要修改的状态
      // 获取部门
      if (!this.AllDepartment.length) {
        this.getDepartmentList();
      }
    },
    // 快速编辑客户维护资料
    fastEditData(row) {
      this.ponent_maintain_data = row;
      this.show_cus_Edit = true;
    },
    // 关闭快速编辑客户维护资料
    fastCloseEdit() {
      this.show_cus_Edit = false;
    },
    // 刷新页面获取最新数据
    submitMaintain() {
      this.getDataList();
      this.show_cus_Edit = false;
    },
    // 快速查看客户手机号
    fastLookTel(row) {
      // 查看电话时跟进
      this.$http.setViewCrmCustomerTel(row.id).then((res) => {
        if (res.status === 200) {
          if (row.tracking && row.tracking.id) {
            row.tracking_id = row.tracking.id
          }
          this.ponent_Tel_data = row;
          this.nowDialData = res.data
          this.show_look_Tel = true; // 显示模态框
          this.getDataList(); // 刷新页面数据
        }
      });
    },
    // 关闭快速查看客户手机号回调函数
    fastCloseTel() {
      this.show_look_Tel = false;
    },
    // 提交查看手机号跟进成功回调函数
    fastSubmitTel() {
      this.getDataList();
      this.show_look_Tel = false;
    },
    // 阻止默认行为和事件传播
    showTypeStatus(e) {
      e.preventDefault();
      e.stopPropagation();
      this.is_pullDown1 = false;
      if (this.is_pullDown) {
        this.is_pullDown = false;
      } else {
        this.is_pullDown = true;
      }
    },
    showTypeStatus1(e) {
      e.preventDefault();
      e.stopPropagation();
      this.is_pullDown = false;
      if (this.is_pullDown1) {
        this.is_pullDown1 = false;
      } else {
        this.is_pullDown1 = true;
      }
    },
    showTypeStatus2(e) {
      e.preventDefault();
      e.stopPropagation();
      this.is_pullDown = false;
      this.is_pullDown1 = false;
      if (this.is_pullDown2) {
        this.is_pullDown2 = false;
      } else {
        this.is_pullDown2 = true;
      }
    },
    // 改变搜索类型
    changeScreenType(list, item) {
      // console.log(list, item, '1111111111');
      // list: 当前选择类型 , item: 选择之前的类型
      // console.log(list, "list", item, "item");
      this.is_pullDown = false; // 关闭popover
      let firstIndex = "";
      let secondIndex = "";
      this.customer_list_type.map((arr, index) => {
        if (arr.title == item.title) {
          firstIndex = index;
        }
      });
      this.customer_list_type.splice(firstIndex, 1, list);
      this.screen_list_type.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex = index;
        }
      });
      this.screen_list_type.splice(secondIndex, 1, item);
      this.getDataLists(list);
    },
    changeScreenType1(list, item) {
      this.is_pullDown1 = false; // 关闭popover
      let firstIndex1 = "";
      let secondIndex1 = "";
      this.customer_list_type.map((arr, index) => {
        // console.log(arr.title, '111');
        // console.log(item.title, '222');
        if (arr.title == item.title) {
          firstIndex1 = index;
        }
      });
      this.customer_list_type.splice(firstIndex1, 1, list);
      this.screen_list_type1.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex1 = index;
        }
      });
      this.screen_list_type1.splice(secondIndex1, 1, item);
      this.getDataLists(list);
    },
    changeScreenType2(list, item) {
      this.is_pullDown2 = false; // 关闭popover
      let firstIndex1 = "";
      let secondIndex1 = "";
      this.customer_list_type.map((arr, index) => {
        // console.log(arr.title, '111');
        // console.log(item.title, '222');
        if (arr.title == item.title) {
          firstIndex1 = index;
        }
      });
      this.customer_list_type.splice(firstIndex1, 1, list);
      this.screen_list_type2.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex1 = index;
        }
      });
      this.screen_list_type2.splice(secondIndex1, 1, item);
      this.getDataLists(list);
    },
    // 客户列表快速跟进客户内容
    fastFollowUp(row) {
      // console.log(row, "row");
      this.ponent_Follow_data = row; // 赋值客户信息
      this.show_Follow_dialog = true;
    },
    // 快速添加跟进成功执行
    addFollowSuccess() {
      this.$message.success("操作成功");
      this.getDataList(); // 刷新页面数据
    },
    // 关闭快速跟进客户内容模态框
    closeFollow() {
      this.show_Follow_dialog = false;
    },
    // 排序
    sortChangeData(column) {
      // console.log(column.column.label,column,"参数");
      // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
      if (column) {
        if (column.column.label === "客户名称") {
          this.params.c_type3 = 1;
        } else if (column.column.label === "更新时间") {
          this.params.c_type3 = 2;
        } else if (column.column.label === "客户线索") {
          this.params.c_type3 = 3;
        } else if (column.column.label === "创建时间") {
          this.params.c_type3 = 4;
        }
        // 判断升序
        if (column.order === "ascending") {
          this.params.c_type3_sort = 2;
        } else if (column.order === "descending") {
          // 判断倒序
          this.params.c_type3_sort = 1;
        } else {
          // 默认
          // this.params.c_type1 = 0;
          delete this.params.c_type3;
          delete this.params.c_type3_sort;
        }
        this.getDataList();
      }
    },
    sortChange(column) {
      // console.log(column.column.label,column,"参数");
      // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
      if (column) {
        // 判断升序
        if (column.order === "ascending") {
          this.admin_params.sort = 1;
        } else if (column.order === "descending") {
          // 判断倒序
          this.admin_params.sort = 2;
        } else {
          // // 默认
          // // this.params.c_type1 = 0;
          delete this.admin_params.sort;
          // delete this.params.c_type3_sort;
        }
        this.admin_params.page = 1
        this.getAdmin();
      }
    },
    // 赋值客户编号
    copyCusID(id) {
      this.$onCopyValue(id);
    },
    getfollowsearch(){
      this.$refs.childRef.getfollowsearch(this.followsearch);
    },
    Quick_Edit(row){
      console.log(row,"================dididididid");
        if(row.deal_user){
          this.customstatus = "已成交"
          this.transmitstatus = false
        }else{
          this.customstatus = "已认领"
          this.transmitstatus = true
        }
        this.ids = row.id
        console.log(row.id,"121212121212");
        // this.checkStatusA(row)
        this.seastype = row.push_type||0
        if(row.order==1){
          this.ticky_post = "取消列表置顶"
        }else{
          this.ticky_post = "列表置顶"
        }
        this.drawer = true//显示抽屉框
      // }
    },
    //控制抽屉的出现
    handleClose() {
      this.drawer = false
      this.multipleSelection = []
      // this.getDataList(); // 刷新页面数据
    },
    //右侧速编转交
    onClickrightcopy(item){
      this.multipleright = this.ids
      this.Transfer_right = false; // 隐藏pop框
       if (item.id == 1) {
        // 如果是转交指定维护人
        this.changetitle = "转交客户",
        this.getAdmin()
        this.right_transfer_customer = true;
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      } else if (item.id == 2) {
        this.changetitle = "复制客户",
        this.right_transfer_customer = true;
      }else if(item.id == 3){
        this.$refs.modifylevel.open([this.multipleright],item.id)
      }else if(item.id == 4){
        // console.log(1212);
        this.$refs.customersource.open([this.multipleright])
      }else if(item.id == 5){
        this.$refs.modifylevel.open([this.multipleright],item.id)
      }else if(item.id == 6){
        this.$refs.modifylevel.open([this.multipleright],item.id)
      }else if(item.id == 7){
        this.$refs.modifylevel.open([this.multipleright],item.id)
      }else if(item.id == 8){
        this.$refs.automatic.open([this.multipleright])
      }else if(item.id == 9){
        this.batchDelete([this.multipleright])
      }
    },
    //右侧速编转交
    onClickright(){
      this.multipleSelection = this.ids
      this.is_transfer_customer = true;
      this.admin_params.user_name = "";
      this.admin_params.page = 1;
    },
    //右侧速编确定提交
    sureright(){
      this.$refs.childRef.onClickForm();
      this.getDataList(); // 获取最新数据
    },
  },
};
</script>
  
<style lang="scss" scoped>
.items-center {
  align-items: center;
}

.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 20px 24px 80px;
  // overflow: hidden;

  .box-crm-head {
    width: 100%;
    // background-color: #ffff;
    // margin: 10px 20px 10px 0px;
    display: flex;

    .crm-head-list {
      width: 100px;
      height: 30px;
      color: #8a929f;
      text-align: center;
      padding: 10px;
      //   margin-right: 20px;
      cursor: pointer;

      &.head_list {
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        background-color: #ffff;
      }
    }
  }

  .cus-userName {
    text-align: left;
    margin: 5px 5px 5px 0;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.w100 {
      max-width: 100%;
    }

    &.cus-userName1 {
      //   max-width: 100%;
      color: #606266;
      //   white-space: normal;
    }
  }

  .head-list {
    margin-right: 10px;
    margin-top: 10px;
  }

  .my-cascader::placeholder {
    color: red;
  }

  .crm-selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        // border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        padding: 15px 27px !important;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
    &.short ::v-deep .el-input .el-input__inner {
      padding: 15px !important;
    }
  }

  //   .el-range-editor--small.el-input__inner {
  //     height: 26px;
  //   }

  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    margin-left: -24px;
    margin-right: -24px;

    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }

  .grid-content {
    background: #fff;
    height: 113px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
    }

    .left {
      font-size: 14px;
      color: #8a929f;
      margin-right: 64px;
    }

    .number {
      display: flex;
      align-items: center;

      .number1 {
        font-size: 24px;
        color: #2e3c4e;
        margin-right: 10px;
      }

      .number2 {
        font-size: 12px;
        color: #fe6c17;
      }
    }
  }
}
.kehuxinxi{
  .input-with-select.short{
    width: 128px;
    margin-left: 20px;
    button{
      padding: 0 0 0 3px;
      width: 36px
    }
  }
    ::v-deep .el-drawer.kehu {
    width: 54% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }
  }
  .QuickEdit {
    width: 100%;
    height: calc(100vh - 90px);
    // max-height: 90%;
    // min-height: 70%;
    margin-top: -10px;
    overflow-y: auto;
    overflow-x: hidden;

    // overflow: auto; /* 当内容超出限制大小时显示滚动条 */
    // margin-bottom: 50px;
    .kehu_footer {
      width: 93%;
      margin: 0 auto;
      justify-content: space-between;
    }
  }
}
.isbottom {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}
.namedata{
    margin-left: 16px;
    .selectedname{
      margin-right: 19px;
      color: #8a929f;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .alllabeldatastyle{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 4px;
    .labeldatastyle{
      margin-top: 8px;
      margin-right: 10px;
    }
    .labelerr{
      margin-top: 12px;
    }
  }
  
.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  flex-wrap: wrap;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
    white-space: nowrap;
  }

  .selected-header {
    font-size: 14px;
    color: #8a929f;
    margin-right: 8px;
    padding: 3px 16px;
    cursor: pointer;
    margin-bottom: 5px;
    white-space: nowrap;
  }

  .label_actions {
    border-radius: 4px;
    background: #e8f1ff;
    color: #2d84fb;
  }

  .selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
        //
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
  }
}
.allbtn{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;

  .text {
    font-size: 14px;
  }
}

.labelrow {
  margin-bottom: 20px;
}

.t-t-b-right {
  // width: 50%;
  // height: 30px;
  display: flex;
  justify-content: flex-end;
  position: relative;

  // background: #fff;
  // overflow: hidden;
  // display: none;
  // #myButton {
  //   display: none;
  //   /* 默认隐藏按钮 */
  // }

  // /* 当屏幕宽度小于600px时显示按钮 */
  // @media screen and (max-width: 1350px) {
  //   #myButton {
  //     display: block;
  //     /* 显示按钮 */
  //   }
  // }

  // #myButton1 {
  //   margin-left: 10px;
  //   display: none;
  //   /* 默认隐藏按钮 */
  // }

  // /* 当屏幕宽度小于600px时显示按钮 */
  // @media screen and (max-width: 1350px) {
  //   #myButton1 {
  //     display: block;
  //     /* 显示按钮 */
  //   }
  // }

  // &.show {
  // display: flex;
  // transition: 0.3s;
  // }
  &.abs {
    position: absolute;
    // width: 620px;
    right: 25px;
    // margin-bottom: 15px;
    overflow: hidden;

    // .el-button {
    //   // height: 30px;
    //   // margin-top: 2px;
    // }

    // &:hover {

    // }
  }
}

.crmuserbox {
  align-items: center;

  span {
    display: block;
  }

  img {
    border-radius: 50%;
  }

  .label {
    width: fit-content;
    padding: 0 6px;
    background: #2d84fb;
    border-radius: 4px;
    color: #fff;
  }
}

.cus-clue-text {
  width: 270px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;

  &.cus-clue-text_community {
    width: 100%;
  }
}

.table-top-box-abs {
  position: absolute;
  /* padding-top: 30px; */
  padding: 10px 24px;
  top: 0;
  left: 0;
  right: 0;
  height: 65px;

  // transition: 0.3s;
  &.fixed {
    position: fixed;
    top: 60px;
    left: 254px;
    right: 40px;
    padding: 10px 24px;
    background: #fff;
    z-index: 100;

    .abs {
      right: 25px;
    }
  }
}

.texttestres {
  text-align: center;
  font-size: 23px;
  margin-bottom: 20px;
}

.testresinformation {
  width: 90%;
  height: 80px;
  margin: 0 auto;
  border-radius: 5px;
  background-color: rgb(255, 235, 229);

  .founderinformation {
    text-align: center;
    line-height: 76px;
    font-size: 18px;
  }
}

.textCode {
  width: 64%;
  height: 50px;
  margin: 10px 24px;
  margin-top: 15px;

  /deep/.el-form-item__content {
    margin-left: 11px !important;
  }

  // /deep/.el-input__inner{
  //   width: 66% !important;
  // }
}

.content-box-crm {
  &.content-box-crm-pr {
    padding-top: 85px;
  }
}
</style>
<style lang="scss">
.cus-box {
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;
  padding-left: 5px;

  .cus-box-header {
    .cus-header-user {
      align-items: center;

      .cus-userName {
        color: #2d84fb;
        text-align: left;
        margin: 5px 5px 5px 0;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.w100 {
          max-width: 100%;
        }

        &.cus-userName1 {
          //   max-width: 100%;
          color: #606266;
          //   white-space: normal;
        }
      }

      .cus-sex {
        width: 16px;
        height: 16px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 12px;
      white-space: nowrap;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      color: #67c23a;
      border: 1px solid #67c23a;
    }

    .cus-icon-purchase {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }
  }

  .cus-img {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 14px;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      display: block;
      color: #67c23a;
      border: 1px solid #67c23a;
      cursor: pointer;
    }

    .cus-icon-purchase {
      display: block;
      color: #98a6c3;
      border: 1px solid #98a6c3;
      cursor: pointer;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }

    .cus-douyinIcon {
      width: 16px;
      height: 16px;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .fast-Edit-cus {
    display: none;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.b-tabs {
  cursor: pointer;

  .b-t-item {
    margin-right: 24px;
    color: #8a929f;
    position: relative;

    &.isactive {
      color: #00a3ff;

      &::after {
        position: absolute;
        content: "";
        left: 50%;
        transform: translateX(-50%);
        height: 3px;
        // background: #2d84fb;
        width: 100%;
        display: block;
        margin-top: 4px;
      }
    }
  }

  .config-customer {
    .el-button {
      padding: 7px 15px;
    }
  }
}

.abs {
  .search_loudong {
    height: 30px;
    margin-top: 2px;
    width: 65px;
  }
}

.search_loudong {
  background: #409eff;
  white-space: nowrap;
  padding: 0 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.clueRemark {
  display: flex;

  .el-textarea {
    width: 360px;

    .el-textarea__inner {
      min-height: 40px !important;
      height: 40px;
    }
  }
}

.table-btns {
  .search-Belong {
    .el-button {
      padding: 4px 7px;
      margin-top: 3px;
      border-radius: 2px;
    }
  }

  .fast-look-tel {
    display: none;
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;
    opacity: 0.8;
    line-height: 1;

    i {
      color: #409eff;
    }

    // img {
    //   width: 100%;
    //   height: 100%;
    // }
  }
}

// img {
//   width: 100%;
//   height: 100%;
// }

.audio_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
  background: #409eff;
}

.labelname {
  margin-bottom: 10px;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;
  }

  .checked {
    background: rgba(45, 132, 251, 0.15);
    border: 1px solid rgba(45, 132, 251, 1);
    color: #2d84fb;
  }
}

.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.inp_no_border {
  width: 155px;

  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
}

.dialog_customer_label {
  height: 560px;
  overflow-y: auto;
}

.search-member-box {
  .el-input {
    .el-input__inner {
      width: 155px;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }
}

.cus-clue-label {
  align-items: center;
  flex-wrap: wrap;
  line-height: 1;

  .cus-icon-label {
    color: #16a1bc;
    border: 1px solid #16a1bc;
    // color: #409eff;
    // border: 1px solid #b3d8ff;
    font-size: 14px;
    padding: 5px 11px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
  }
}

.clueLabel {
  width: 20px;
  height: 20px;
  display: none;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.last_call_follow {
  align-items: center;
  justify-content: center;

  &.w180 {
    width: 160px;
    overflow: hidden;
  }

  .cus-clue-text_c {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 3px;
      right: -5px;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #9edf2e;
    }
  }

  .cus-clue-text_u {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 3px;
      right: -5px;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #f56c6c;
    }
  }
}

.label_list {
  flex-wrap: wrap;

  .label_item {
    margin-bottom: 5px;
  }
}

.el-table__body {
  .el-table__row {
    .is-center:nth-child(4):hover .clueLabel {
      display: block;
    }

    .is-center:nth-child(2):hover .fast-Edit-cus {
      display: block;
    }

    .is-center:nth-child(3):hover .fast-look-tel {
      display: block;
    }

    .is-center:nth-child(6):hover .followLabel {
      display: block;
    }
  }
}

.screen-type {
  display: flex;
  flex-direction: column;
  margin: -12px;
  padding: 6px 0px;

  .screen-type-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .screen-type-content:hover {
    background-color: #f5f7fa;
  }
}

.public-status {
  // display: inline-block;
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  padding: 0 4px;
  border-radius: 4px;
}

.follow-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.followLabel {
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.el-date-editor {
  width: 280px !important;
}

#musicMp3 {
  position: absolute;
  left: 100000%;
  top: 10000%;
}

.sticky {
  position: sticky;
  top: 200px;
}

.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.content-box-crm {
  &.content-box-crm-pr {
    position: relative;
    padding: 24px 0;
    padding-top: 85px;
  }
}
</style>
  