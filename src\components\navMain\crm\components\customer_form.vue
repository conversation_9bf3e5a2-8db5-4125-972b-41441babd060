<template>
  <div>
    <el-tabs v-model="is_form_page" @tab-click="onTabClick">
      <el-tab-pane
        v-for="(item, index) in form_tabs"
        :key="index"
        :label="item.label"
        :name="item.name"
      ></el-tab-pane>
      <el-form
        v-if="is_form_page === 'client'"
        ref="client"
        inline
        label-width="100px"
        :model="form"
        label-position="left"
      >
        <div style="max-height:500px;overflow:auto;">
          <div v-for="item in data1" :key="item.id">
            <el-form-item
              v-if="
                (item.colum_type === 'text' || item.colum_type === 'mobile') &&
                  item.is_show === 1
              "
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请输入${item.colum_name}`,
                        trigger: 'blur',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-input
                  :disabled="isDisable"
                  style="width:200px"
                  v-model="form[item.colum_key]"
                  :placeholder="item.tips"
                ></el-input>
                <!-- <template
              v-if="item.colum_type === 'checkbox' && item.is_show === 1"
            >
              <el-checkbox-group @change="onChangeCheck(e, item)">
                <el-checkbox
                  v-for="r_i_c in item.colum_value"
                  :key="r_i_c.value"
                  :label="r_i_c.name"
                ></el-checkbox>
              </el-checkbox-group>
            </template> -->
              </div>
            </el-form-item>
            <el-form-item
              v-if="item.colum_type === 'radio' && item.is_show === 1"
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请选择${item.colum_name}`,
                        trigger: 'change',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-radio
                  :disabled="isDisable"
                  v-for="r_i in item.colum_value"
                  :key="r_i.value"
                  v-model="form[item.colum_key]"
                  :label="r_i.value"
                  >{{ r_i.name }}</el-radio
                >
              </div>
            </el-form-item>
            <el-form-item
              v-if="
                (item.colum_type === 'date' ||
                  item.colum_type === 'datetime') &&
                  item.is_show === 1
              "
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请选择${item.colum_name}`,
                        trigger: 'change',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-date-picker
                  :disabled="isDisable"
                  style="width:200px"
                  v-model="form[item.colum_key]"
                  :type="item.colum_type"
                  :placeholder="item.tips"
                  :value-format="
                    item.colum_type === 'date'
                      ? 'yyyy-MM-dd'
                      : 'yyyy-MM-dd HH:mm:ss'
                  "
                >
                </el-date-picker>
              </div>
            </el-form-item>
            <el-form-item
              v-if="item.colum_type === 'select' && item.is_show === 1"
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请选择${item.colum_name}`,
                        trigger: 'change',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-select
                  style="width:200px"
                  v-model="form[item.colum_key]"
                  :placeholder="item.tips"
                  :disabled="isDisable"
                >
                  <el-option
                    v-for="item in item.colum_value"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </el-form-item>

            <!-- <el-form-item :label="item.colum_name" :prop="item.colum_name">
          <div class="row input-box div"></div>
        </el-form-item> -->
          </div>
          <el-form-item
            label="客户来源："
            :rules="[
              {
                required: true,
                message: `请选择客户来源`,
                trigger: 'change',
              },
            ]"
          >
            <div class="row input-box div">
              <el-select
                style="width:200px"
                v-model="form.source_id"
                placeholder="请选择"
                :disabled="isDisable"
              >
                <el-option
                  v-for="item in from_data"
                  :key="item.source_id"
                  :label="item.title"
                  :value="item.source_id"
                >
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <el-form
        v-if="is_form_page === 'company'"
        ref="company"
        inline
        label-width="100px"
        :model="form"
        label-position="left"
      >
        <div style="max-height:500px;overflow:auto;">
          <div v-for="item in data2" :key="item.id">
            <el-form-item
              v-if="
                (item.colum_type === 'text' || item.colum_type === 'mobile') &&
                  item.is_show === 1
              "
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请输入${item.colum_name}`,
                        trigger: 'blur',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-input
                  :disabled="isDisable"
                  style="width:200px"
                  v-model="form[item.colum_key]"
                  :placeholder="item.tips"
                ></el-input>
                <!-- <template
              v-if="item.colum_type === 'checkbox' && item.is_show === 1"
            >
              <el-checkbox-group @change="onChangeCheck(e, item)">
                <el-checkbox
                  v-for="r_i_c in item.colum_value"
                  :key="r_i_c.value"
                  :label="r_i_c.name"
                ></el-checkbox>
              </el-checkbox-group>
            </template> -->
              </div>
            </el-form-item>
            <el-form-item
              v-if="item.colum_type === 'radio' && item.is_show === 1"
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请选择${item.colum_name}`,
                        trigger: 'change',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-radio
                  :disabled="isDisable"
                  v-for="r_i in item.colum_value"
                  :key="r_i.value"
                  v-model="form[item.colum_key]"
                  :label="r_i.value"
                  >{{ r_i.name }}</el-radio
                >
              </div>
            </el-form-item>
            <el-form-item
              v-if="
                (item.colum_type === 'date' ||
                  item.colum_type === 'datetime') &&
                  item.is_show === 1
              "
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请选择${item.colum_name}`,
                        trigger: 'change',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-date-picker
                  style="width:200px"
                  v-model="form[item.colum_key]"
                  :type="item.colum_type"
                  :placeholder="item.tips"
                  :value-format="
                    item.colum_type === 'date'
                      ? 'yyyy-MM-dd'
                      : 'yyyy-MM-dd HH:mm:ss'
                  "
                  :disabled="isDisable"
                >
                </el-date-picker>
              </div>
            </el-form-item>
            <el-form-item
              v-if="item.colum_type === 'select' && item.is_show === 1"
              :label="item.colum_name"
              :prop="[item.colum_key][0]"
              :disabled="isDisable"
              :rules="
                item.is_require
                  ? [
                      {
                        required: true,
                        message: `请选择${item.colum_name}`,
                        trigger: 'change',
                      },
                    ]
                  : [{ required: false }]
              "
            >
              <div class="row input-box div">
                <el-select
                  style="width:200px"
                  v-model="form[item.colum_key]"
                  :placeholder="item.tips"
                  :disabled="isDisable"
                >
                  <el-option
                    v-for="item in item.colum_value"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </el-form-item>

            <!-- <el-form-item :label="item.colum_name" :prop="item.colum_name">
          <div class="row input-box div"></div>
        </el-form-item> -->
          </div>
        </div>
      </el-form>
      <div class="row input-box div btn-box">
        <el-button @click="clsoe">取 消</el-button>
        <el-button v-if="is_form_page === 'client'" @click="onClickNext"
          >下一步</el-button
        >
        <el-button :disabled="isDisable" type="primary" @click="onClickForm"
          >确 定</el-button
        >
      </div>
    </el-tabs>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      form_tabs: [
        { id: 1, label: "基本信息", name: "client" },
        { id: 2, label: "公司资料", name: "company" },
      ],
      is_form_page: "client",
      from_data: [],
      admin_list: [],
    };
  },
  props: {
    data1: {
      type: Array,
      default: () => [],
    },
    data2: {
      type: Array,
      default: () => [],
    },
    form: {
      type: Object,
      default: () => {},
    },
    form1: {
      type: Object,
      default: () => {},
    },
    isDisable: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.getFrom();
  },
  methods: {
    onClickForm() {
      this.$refs[this.is_form_page].validate((valid) => {
        if (valid) {
          if (this.is_form_page === "client") {
            this.form.is_submit_company_field = 0;
          }
          // let newform1 = JSON.parse(JSON.stringify(this.form1));
          let newform2 = JSON.parse(JSON.stringify(this.form));
          // if (newform1.company_column_field) {
          //   this.data2.map((res) => {
          //     newform1.company_column_field.map((res1) => {
          //       if (res.id === res1.template_id) {
          //         res1.value = newform1[res.colum_key];
          //       }
          //     });
          //   });
          // }
          if (newform2.client_column_field) {
            this.data1.map((res) => {
              newform2.client_column_field.map((res1) => {
                if (res.id === res1.template_id) {
                  res1.value = newform2[res.colum_key];
                }
              });
            });
          }
          if (newform2.company_column_field) {
            this.data2.map((res) => {
              newform2.company_column_field.map((res1) => {
                if (res.id === res1.template_id) {
                  res1.value = newform2[res.colum_key];
                }
              });
            });
          }
          // let form = {};
          // if (this.is_form_page === "company") {
          //   form = Object.assign({}, newform2, newform1);
          // } else {
          //   form = Object.assign({}, newform1, newform2);
          // }
          this.$emit("onClick", newform2);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    onClickNext() {
      this.is_form_page = "company";
      this.form.is_submit_company_field = 1;
    },
    getFrom() {
      this.$http.getCrmCustomerFrom().then((res) => {
        if (res.status === 200) {
          this.from_data = res.data.data;
        }
      });
    },
    onTabClick() {
      this.$nextTick(() => {
        this.$refs[this.is_form_page].clearValidate();
      });
    },
    clsoe() {
      this.$emit("clsoe");
    },
  },
};
</script>

<style scoped lang="scss">
.el-form-item {
  display: flex;
  align-items: center;
}
.input-box {
  align-items: center;
  justify-content: space-between;
  img {
    width: 16px;
    height: 16px;
    margin-left: 12px;
  }
  .l-item {
    background: #f8f8f8;
    border-radius: 4px;
    width: 73px;
    height: 63px;
    line-height: 1;
    align-items: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: #8a929f;
    margin-right: 4px;
    cursor: pointer;
    .l-name {
      font-size: 14px;
      margin-bottom: 4px;
    }
    .l-name-1 {
      font-size: 12px;
    }
  }
  .lisactive {
    background: #fff;
    color: #2d84fb;
    border: 1px solid #2d84fb;
  }
}
.btn-box {
  justify-content: flex-end;
}

/*滚动条整体粗细样式*/
::-webkit-scrollbar {
  /*高宽分别对应横竖滚动条的尺寸*/
  width: 8px;
  height: 8px;
}

/*滚动条里面小方块*/
::-webkit-scrollbar-thumb {
  border-radius: 10px !important;
  /* 颜色 */
  background: #b6b6b6 !important;
  /* 线性渐变背景 */
}

/*滚动条轨道*/
::-webkit-scrollbar-track {
  border-radius: 10px !important;
  background: #ededed !important;
}
</style>
