<template>
    <div>
        <el-form ref="form" :model="form" label-width="100px" label-position="left">
            <div class="title">基本属性</div>
            <!-- <div v-if="usageType == 4">用途类型</div> -->
            <!-- <el-form-item label="当前状态" v-show="usageType == 4" prop="business_status">
                <el-radio-group v-model="form.business_status">
                    <el-radio :label="1">经营中</el-radio>
                    <el-radio :label="2">置空中</el-radio>
                </el-radio-group>
            </el-form-item> -->
            <!-- <el-form-item label="经营行业" v-show="usageType == 4" prop="business_trades">
                <el-cascader
                    style="width: 240px;"
                    v-model="form.business_trades"
                    :options="form_options.business_trades"
                    :show-all-levels="false"
                    placeholder="请选择经营行业"
                    :props="{
                        label: 'name',
                        value: 'values',
                        emitPath: false
                    }"
                    @change="changeTrades"
                >
                </el-cascader>
            </el-form-item> -->
            <!-- <el-form-item label="类型" v-show="usageType == 4" prop="business_type">
                <el-select 
                    style="width: 240px;"
                    v-model="form.business_type" 
                    placeholder="请选择户型结构"
                >
                    <el-option
                        v-for="item in form_options.business_type"
                        :key="item.values"
                        :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="配套" v-show="usageType == 4" prop="facilities">
                <div class="facility-box">
                    <el-checkbox-group v-model="form.facilities" @change="changeFacilities">
                        <el-checkbox 
                            v-for="item in form_options.facilities_sage4" 
                            :key="item.values" 
                            :label="item.values"
                        >{{ item.name }}
                    </el-checkbox>
                    </el-checkbox-group>
                </div>
            </el-form-item> -->
            <!-- <el-form-item label="客流人群" v-show="usageType == 4" prop="business_customer">
                <div class="facility-box">
                    <el-checkbox-group v-model="form.business_customer">
                        <el-checkbox 
                            v-for="item in form_options.business_customer" 
                            :key="item.values" 
                            :label="item.values"
                        >
                            {{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </el-form-item> -->
            <el-form-item label="户型结构" v-show="houseType != 2" prop="house_structure">
                <el-select style="width: 240px;" v-model="form.house_structure" placeholder="请选择户型结构">
                    <el-option v-for="item in form_options.house_structure" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="建筑类型" v-show="houseType != 2" prop="building_type">
                <el-select v-model="form.building_type" placeholder="请选择建筑类型" style="width: 240px;">
                    <el-option v-for="item in form_options.building_type" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="建筑结构" v-show="houseType != 2" prop="building_structure">
                <el-select v-model="form.building_structure" placeholder="请选择建筑结构" style="width: 240px;">
                    <el-option v-for="item in form_options.building_structure" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="房屋年限" v-show="houseType != 2" prop="property_certificate_period">
                <el-select v-model="form.property_certificate_period" placeholder="请选择房屋年限" style="width: 240px;">
                    <el-option v-for="(item) in form_options.property_certificate_period" :key="item.values"
                        :label="item.name" :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="供暖方式" prop="heating_type">
                <el-select v-model="form.heating_type" placeholder="请选择供暖方式" style="width: 240px;">
                    <el-option v-for="(item) in form_options.heating_type" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="燃气" prop="gas"
                v-show="(houseType == 2 || houseType == 3) && form_options.gas && form_options.gas.length">
                <el-radio v-for="item in form_options.gas" :key="item.values" v-model="form.gas" :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item label="电梯" prop="elevator" v-show="usageType != 4">
                <el-radio v-for="item in form_options.elevator" :key="item.values" v-model="form.elevator"
                    :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item style="display: inline-block;" label="梯户比例" prop="ladder" v-show="form.elevator == 1">
                <el-input placeholder="请输入" onkeyup="value=value.replace(/^0+|[^\d]/g,'')" v-model="form.ladder"
                    style="width: 127px; margin-right: 12px">
                    <template slot="append">梯</template>
                </el-input>
            </el-form-item>
            <el-form-item style="display: inline-block;" label-width="0px" prop="households" v-show="form.elevator == 1">
                <el-input placeholder="请输入" v-model="form.households" onkeyup="value=value.replace(/^0+|[^\d]/g,'')"
                    style="width: 127px; margin-right: 12px">
                    <template slot="append">户</template>
                </el-input>
            </el-form-item>
            <el-form-item label="用水" prop="water"
                v-show="(houseType == 2 || houseType == 3) && form_options.water && form_options.water.length">
                <el-radio v-for='item in form_options.water' :key="item.values" v-model="form.water" :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item label="用电" prop="electricity"
                v-show="(houseType == 2 || houseType == 3) && form_options.electricity && form_options.electricity.length">
                <el-radio v-for='item in form_options.electricity' :key="item.values" v-model="form.electricity"
                    :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item label="配套设施" prop="facilities" v-if="usageType != 4">
                <el-select v-model="form.facilities" multiple placeholder="请选择配套设施" style="width: 240px;">
                    <el-option v-for="(item) in form_options.facilities" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="车位" prop="parking_check" v-if="usageType != 4">
                <el-radio v-for='item in form_options.parking_check' :key="item.values" v-model="form.parking_check"
                    :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item label="看房时间" prop="see_time">
                <el-select v-model="form.see_time" placeholder="请选择看房时间" style="width: 240px;">
                    <el-option v-for="(item) in form_options.see_time" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="入住" prop="check_in"
                v-show="(houseType == 2 || houseType == 3) && form_options.check_in && form_options.check_in.length">
                <el-radio v-for='item in form_options.check_in' :key="item.values" v-model="form.check_in"
                    :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item label="入住时间" prop="check_in_time"
                v-show="(houseType == 2 || houseType == 3) && form.check_in == 0">
                <el-date-picker style="width: 240px;" v-model="form.check_in_time" type="date" placeholder="选择日期"
                    value-format="yyyy-MM-dd">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="租期" prop="term" v-show="houseType == 2 || houseType == 3">
                <el-select v-model="form.term" placeholder="请选择租期" style="width: 240px;">
                    <el-option v-for="(item) in form_options.term" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <!-- 仅出售、租售显示 -->
            <el-form-item label="房源标签" v-show="(houseType == 1 || houseType == 3) && showLabel">
                <el-select v-model="label_sale" multiple placeholder="请选择房源标签" style="width: 240px;"
                    @change="changeLabelSale">
                    <el-option v-for="(item) in form_options.label_sale" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
                <el-button type="primary" style="margin-left: 10px;" @click="loadMoreLabel">更多</el-button>
            </el-form-item>
            <!-- 仅出租显示 -->
            <el-form-item label="房源标签" v-show="houseType == 2 && showLabel">
                <el-select v-model="label_rent" multiple placeholder="请选择房源标签" style="width: 240px;"
                    @change="changeLabelSale">
                    <el-option v-for="(item) in form_options.label_rent" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
                <el-button type="primary" style="margin-left: 10px;" @click="loadMoreLabel">更多</el-button>
            </el-form-item>
            <div class="title" v-if="houseType != 2">交易属性</div>
            <el-form-item label="挂牌时间" prop="first_upload_at" v-show="houseType != 2">
                <div class="block">
                    <el-date-picker style="width: 240px;" v-model="form.first_upload_at" type="date" placeholder="请选择挂牌时间"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </div>
            </el-form-item>
            <el-form-item label="上次交易时间" prop="last_trade_at" v-show="houseType != 2">
                <div class="block">
                    <el-date-picker style="width: 240px;" v-model="form.last_trade_at" type="date" placeholder="请选择上次交易时间"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </div>
            </el-form-item>
            <el-form-item label="交易权属" prop="ownership" v-show="houseType != 2">
                <el-select v-model="form.ownership" placeholder="请选择交易权属" style="width: 240px;">
                    <el-option v-for="(item) in form_options.ownership" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="产权所属" prop="property_rights" v-show="houseType != 2">
                <el-select v-model="form.property_rights" placeholder="请选择产权所属" style="width: 240px;">
                    <el-option v-for="(item) in form_options.property_rights" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="房源核验编码" prop="house_code"
                v-if="form_options.house_code && form_options.house_code.values == 1">
                <el-input placeholder="请输入" v-model="form.house_code" style="width: 240px;">
                </el-input>
            </el-form-item>
            <el-form-item label="抵押信息" prop="mortgage"
                v-show="houseType != 2 && form_options.mortgage && form_options.mortgage.length">
                <el-radio v-for='item in form_options.mortgage' :key="item.values" v-model="form.mortgage"
                    :label="item.values">
                    {{ item.name }}
                </el-radio>
            </el-form-item>
            <el-form-item label="抵押金额" prop="mortgage_price" v-show="form.mortgage == 1 && houseType != 2">
                <el-input placeholder="请输入" v-model="form.mortgage_price" onkeyup="value=value.replace(/^0+|[^\d|(.)]/g,'')"
                    style="width: 140px; margin-right: 12px">
                    <template slot="append">万元</template>
                </el-input>
            </el-form-item>
            <el-form-item label="产权年限" prop="right_period"
                v-show="houseType != 2 && form_options.right_period && form_options.right_period.length">
                <el-select v-model="form.right_period" placeholder="请选择产权所属" style="width: 240px;">
                    <el-option v-for="(item) in form_options.right_period" :key="item.values" :label="item.name"
                        :value="item.values">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="建造年代" prop="built_year" v-show="houseType != 2">
                <el-input style="width: 240px;" v-model="form.built_year" placeholder="请输入"></el-input>
            </el-form-item>
        </el-form>
        <my-HouseLabel v-if="showHouseLabel" :showHouseLabel="showHouseLabel"
            :label_sale="houseType == 2 ? label_rent : label_sale" @closeHouseLabel="closeHouseLabel"
            @addHouseLabel="addHouseLabel"></my-HouseLabel>
    </div>
</template>
<script>
import myHouseLabel from "@/components/navMain/house/components/myHouseLabel";
export default {
    components: {
        myHouseLabel,
    },
    props: {
        // 完整信息可选择的参数
        form_options: {
            type: Object,
            default: () => { },
        },
        // 发布类型 出售1 出租2 租售3
        house_type: {
            type: Number,
            default: 1,
        },
        // 已选择的参数，在编辑中进行赋值
        form_data: {
            type: Object,
            default: () => { },
        },
        // 出售、租售房源标签
        form_options_rent: {
            type: Array,
            default: () => { }
        },
        // 出租房源标签
        form_options_sale: {
            type: Array,
            default: () => { }
        },
        // 控制房源标签显示/隐藏
        showLabel: {
            type: Boolean,
            default: true
        },
        usage_type: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            form: {
                house_structure: "", // 户型结构：平层1 复式2 错层3 跃层4 其他99
                building_type: "", // 建筑类型：板楼1 塔楼2 半塔结合3 砖楼4 其他99
                building_structure: "", // 建筑结构：钢混结构1 钢结构2 砖混结构3 砖木结构4 其他99
                elevator: 1, // 是否有电梯：1有 0没有
                ladder: "", // 电梯一共有几梯
                households: "", // 一梯有几户
                heating_type: "", // 供暖方式：集中供暖1 自采暖2 其他99
                first_upload_at: "", // 挂牌时间
                last_trade_at: "", // 上次交易时间
                property_certificate_period: "", // 房屋年限：不满二 满二 满五 其他
                mortgage: 1, // 是否有抵押：1有 2没有
                mortgage_price: "", // 抵押金额：单位 万元   最多支持保留2位小数
                ownership: "", // 交易权属：商品房1 公房2 央产房3 ...其他99
                property_rights: "", // 产权所属： 共有1 非共有2
                house_code: "", // 房源核验编码
                facilities: "", // 配套设施
                see_time: "", // 看房时间 1随时看房 2提前预约随时可看 ...6有租户需预约
                water: 1, // 用水 1民水 2商水
                electricity: 1, // 1民电 2商电
                gas: 1, // 燃气 1有燃气 0无燃气
                term: "", // 租期 1(1-3个月) 2(4-6个月) ... 99(租期可议)
                check_in_time: "", // 入住时间
                parking: "", // 车位
                label: [], // 房源标签
                check_in: 0, // 选择输入时间 0选择入住时间 1随时入住
                parking_check: 1, // 2暂无数据 1有车位 0无车位
                right_period: 1, // 产权年限 1 70年 2 40年 3 50年 4 其他
                built_year: "", // 建造年代
                // business_status: 1, // 当前状态 1经营中 2置空中
                // business_trades: '', // 经营行业
                // business_type: '', // 商铺类型
                // business_customer: [], // 客流人群类型
            },
            houseType: 1,
            showHouseLabel: false, // 控制房源标签模态框
            label_sale: [], // 房源户型标签
            label_rent: [], // 房源租房标签
            usageType: 1, // 用途类型
            // form_options:{}
        }
    },
    mounted() {
        console.log();
        // eslint-disable-next-line no-undef
        eventBus.$on('clearUsage', () => {
            this.form.facilities = []; // 清空配套设施
        });
        // 如果录入房源或编辑房源的挂牌时间为空或者0就赋值当前时间
        this.initialTime();
        // if (JSON.stringify(this.entrustData) != '{}') {
        //     this.isEntrust = true
        //     console.log(this.entrustData, 'entrustData---')
        // }
    },
    beforeDestroy() {
        // eslint-disable-next-line no-undef
        eventBus.$off('clearUsage');
    },
    watch: {
        form: {
            handler(n_val) {
                this.$emit("change", n_val);
            },
            deep: true,
        },
        // 选择 售卖的状态
        house_type: {
            handler(val) {
                if (val && val != undefined) {
                    this.houseType = val
                }
            }
        },
        // 编辑中已经填写的数据
        form_data: {
            handler(val) {
                if (val && val.house_info !== undefined) {
                    // 清空默认值为0的参数
                    if (val.house_info.ladder == 0) {
                        val.house_info.ladder = 1;
                    }
                    if (val.house_info.households == 0) {
                        val.house_info.households = 1;
                    }
                    // if(val.house_info.first_upload_at == 0) {
                    //     val.house_info.first_upload_at = "";
                    // } 
                    if (val.house_info.house_structure == 0) {
                        val.house_info.house_structure = "";
                    }
                    // if(val.house_info.business_status == 0) {
                    //     val.house_info.business_status = 1;
                    // }
                    if (val.house_info.building_type == 0) {
                        val.house_info.building_type = "";
                    }
                    if (val.house_info.building_structure == 0) {
                        val.house_info.building_structure = "";
                    }
                    if (val.house_info.heating_type == 0) {
                        val.house_info.heating_type = "";
                    }
                    if (val.house_info.last_trade_at == 0) {
                        val.house_info.last_trade_at = "";
                    }
                    if (val.house_info.property_certificate_period == 0) {
                        val.house_info.property_certificate_period = "";
                    }
                    if (val.house_info.ownership == 0) {
                        val.house_info.ownership = "";
                    }
                    if (val.house_info.facilities == 0) {
                        val.house_info.facilities = "";
                    }
                    // if(!val.house_info.business_customer) {
                    //     val.house_info.business_customer = [];
                    // }
                    // if(val.house_info.business_trades == 0) {
                    //     val.house_info.business_trades = "";
                    // }
                    if (val.house_info.see_time == 0) {
                        val.house_info.see_time = "";
                    }
                    if (val.house_info.term == 0) {
                        val.house_info.term = "";
                    }
                    if (val.house_info.property_rights == 0) {
                        val.house_info.property_rights = "";
                    }
                    if (val.house_info.mortgage_price == 0) {
                        val.house_info.mortgage_price = "";
                    }
                    if (val.house_info.check_in == 1) {
                        val.house_info.check_in_time = "";
                    }
                    if (val.house_info.right_period == 0) {
                        val.house_info.right_period = "";
                    }
                    // 将时间戳转换日期格式
                    if (val.house_info.first_upload_at != "" && val.house_info.first_upload_at != undefined) {
                        val.house_info.first_upload_at = this.timesToTime(val.house_info.first_upload_at)
                    }
                    // 将时间戳转换日期格式
                    if (val.house_info.last_trade_at != "" && val.house_info.last_trade_at != undefined) {
                        val.house_info.last_trade_at = this.timesToTime(val.house_info.last_trade_at)
                    }
                    // 将字符串转换为数组
                    // if(val.house_info.business_customer != "" && val.house_info.business_customer != undefined) {
                    //     val.house_info.business_customer = val.house_info.business_customer.split(',');
                    //     val.house_info.business_customer.map((item, index) => {
                    //         val.house_info.business_customer[index] = parseInt(item)
                    //     })
                    // }
                    if (val.house_info.facilities != "" && val.house_info.facilities != undefined) {
                        val.house_info.facilities = val.house_info.facilities.split(',');
                        val.house_info.facilities.map((item, index) => {
                            val.house_info.facilities[index] = parseInt(item)
                        })
                    }
                    for (let key in this.form) {
                        this.form[key] = val.house_info[key]
                    }
                    this.changeFacilities(this.form.facilities);
                    // console.log(this.form,"this.form", val.house_info)
                }
            }
        },
        // 房源用途类型
        usage_type: {
            handler(val) {
                if (val) {
                    this.usageType = val;
                }
            }
        }
    },
    methods: {
        changeTrades(value) {
            console.log(value, "value")
        },
        // 将时间戳转换日期格式
        timesToTime(times) {
            if (times != '' && times != undefined) {
                let date = new Date(times * 1000);
                let Y = date.getFullYear() + '-';
                let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                let D = date.getDate();
                return Y + M + D
            }
        },
        // 初始化挂牌时间，默认当前日期
        initialTime() {
            if (this.form.first_upload_at == "" || this.form.first_upload_at == 0) {
                let date = new Date();
                const Y = date.getFullYear() + "-"
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + "-";
                const D = date.getDate() + 1 < 10 ? '0' + (date.getDate()) : date.getDate();
                const sum = Y + M + D
                this.form.first_upload_at = sum;
            }
        },
        // 清空参数
        clearChildData() {
            this.$refs.form.resetFields();
            this.form_options.label_sale = JSON.parse(JSON.stringify(this.form_options_sale)); // 重置房源标签
            this.form_options.label_rent = JSON.parse(JSON.stringify(this.form_options_rent));
            this.label_sale = [];
            this.form.label = [];
            this.label_rent = [];
            this.initialTime();
        },
        loadMoreLabel() {
            this.showHouseLabel = true;
        },
        // 关闭模态框
        closeHouseLabel() {
            this.showHouseLabel = false;
        },
        // 添加选中的标签
        addHouseLabel(val) {
            console.log(val, "val");
            // 判断房源售卖状态
            if (this.houseType == 2) {
                this.label_rent = []; // 清空已选中标签
                this.form_options.label_rent = JSON.parse(JSON.stringify(this.form_options_rent)); // 重置标签
                val.map((item) => {
                    this.form_options.label_rent.map((list) => {
                        // 如果存在相同的values
                        if (item.values == list.values) {
                            list.check = true;
                        } else if (item.values != list.values) {
                            let arr = this.form_options.label_rent.find((arr) => arr.values == item.values);
                            if (!arr) {
                                this.form_options.label_rent.push(item); // 赋值出租房源标签
                            }
                        }
                    })
                })
                this.form_options.label_rent.map((item) => {
                    if (item.check) {
                        this.label_rent.push(item.values); // 赋值标签
                    }
                })
                this.form.label = this.label_rent; // 赋值接口参数
            } else {
                this.label_sale = []; // 清空已选中标签
                this.form_options.label_sale = JSON.parse(JSON.stringify(this.form_options_sale)); // 重置标签
                val.map((item) => {
                    this.form_options.label_sale.map((list) => {
                        // 如果存在相同的values
                        if (item.values == list.values) {
                            list.check = true;
                        } else if (item.values != list.values) {
                            let arr = this.form_options.label_sale.find((arr) => arr.values == item.values);
                            if (!arr) {
                                this.form_options.label_sale.push(item); // 赋值出租房源标签
                            }
                        }
                    })
                })
                this.form_options.label_sale.map((item) => {
                    if (item.check) {
                        this.label_sale.push(item.values) // 赋值标签
                    }
                })
                this.form.label = this.label_sale; // 赋值接口参数
            }
        },
        changeLabelSale(val) {
            this.form.label = val;
        },
        // 配套设施发生改变
        changeFacilities(value) {
            if (value) {
                // 判断是否有电梯
                let elevator = value.findIndex((item) => item == 11);
                if (elevator >= 0) {
                    this.form.elevator = 1; // elevator: 1有电梯 0没有电梯
                } else {
                    this.form.elevator = 0;
                }
                // 判断是否有停车位
                let parking_check = value.findIndex((item) => item == 14);
                if (parking_check) {
                    this.form.parking_check = 1; // parking_check: 2暂无数据 1有车位 0无车位
                } else {
                    this.form.parking_check = 0;
                }
            }
        },
    }
}
</script>
<style scoped lang="scss">
.el-form-item {
    ::v-deep.el-form-item__label {
        font-weight: bold;
        color: #2e3c4e;
    }
}

.title {
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;
}

.facility-box {
    width: 660px;
}
</style>