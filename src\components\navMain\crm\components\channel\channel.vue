<template>
  <div class="table">
    <div class="table_oper flex-row align-center">
      <div class="flex-1"></div>
      <div class="table_oper_item flex-row align-center">
        <el-button size="small" type="primary" @click="addChannel"
          >添加渠道</el-button
        >
      </div>
    </div>
    <el-table
      v-loading="channel_table_loading"
      :data="channel_tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column label="ID" width="100" prop="id"></el-table-column>
      <el-table-column label="渠道名称" prop="name"> </el-table-column>
      <!--  <el-table-column label="审核状态" prop="status" v-slot="{ row }">
        <el-tag :type="row.status == 1 ? 'success' : 'warning'">{{
          row.status == 1 ? "审核通过" : "待审"
        }}</el-tag>
      </el-table-column>
      <el-table-column label="禁用状态" prop="is_disable" v-slot="{ row }">
        <el-tag :type="row.is_disable == 0 ? 'success' : 'warning'">{{
          row.is_disable == 0 ? "已启用" : "已禁用"
        }}</el-tag>
      </el-table-column> -->
      <el-table-column label="添加时间" prop="created_at"> </el-table-column>

      <el-table-column label="操作" v-slot="{ row }">
        <el-button type="primary" class="mr10" @click="editChannel(row)"
          >编辑</el-button
        >
        <!-- <el-popconfirm
          title="确定更改此号码的禁用状态吗？"
          class="mr10"
          @onConfirm="changeChannelDisabled(row)"
        >
          <el-button
            slot="reference"
            :type="row.is_disable == 0 ? 'warning' : 'primary'"
            >{{ row.is_disable == 0 ? "禁用" : "启用" }}</el-button
          >
        </el-popconfirm> -->
        <el-popconfirm
          title="确定删除此渠道吗？"
          class="mr10"
          @onConfirm="delChannel(row)"
        >
          <el-button slot="reference" type="warning">删除</el-button>
        </el-popconfirm>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev,pager,next"
      :total="channelTotal"
      :page-size="channel_params.per_page"
      :current-page="channel_params.page"
      @current-change="onChannelPageChange"
    ></el-pagination>

    <el-dialog
      width="500px"
      :title="channelTitle"
      append-to-body
      :visible.sync="channel_edit"
    >
      <el-form label-width="120px">
        <!-- filterable
                  remote
                  :remote-method="getShowChannelNumber" -->
        <el-form-item label="渠道名称">
          <el-input v-model="channel_form.name" placeholder="请输入渠道名称">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancelAdd">取消</el-button>
        <el-button
          type="primary"
          :loading="is_add_loading"
          @click="submitChannel"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      sub_loading: false,
      channel_params: {
        page: 1,
        per_page: 10,
      },
      channelTotal: 0,
      channel_tableData: [],
      channel_table_loading: false,
      // 坐席添加编辑相关
      channel_form: {
        name: ''
      },
      channel_edit: false, //显示添加编辑坐席弹框
      channelTitle: '', //添加编辑坐席的弹框标题
      is_add_loading: false,
    }
  },
  created() {
    this.getChannel()
  },
  methods: {

    // 获取坐席列表
    getChannel() {
      this.channel_table_loading = true
      this.$http.getChannel(this.channel_params).then(res => {
        if (res.status == 200) {
          this.channel_tableData = res.data.data
          this.channelTotal = res.data.total
        }
        this.channel_table_loading = false
      }).catch(() => {
        this.channel_table_loading = false
      })
    },
    // 坐席列表页面更新
    onChannelPageChange(val) {
      this.channel_params.page = val
      this.getChannel()
    },
    // 添加编辑删除坐席
    addChannel() {
      this.channel_form = {
        name: ''
      }

      this.isAdd = true
      this.channelTitle = "添加号码"
      this.channel_edit = true


    },
    editChannel(row) {
      this.isAdd = false
      this.channel_form.name = row.name
      this.channel_form.id = row.id
      this.channelTitle = "编辑号码"
      this.channel_edit = true

    },
    delChannel(row) {
      this.$http.delChannel(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '删除成功')
          this.getChannel()
        }

      }).catch(() => {

      })
    },
    submitChannel() {
      if (this.isAdd) {
        this.addSubmit()
      } else {
        this.editSubmit()  //暂无此操作
      }
    },
    addSubmit() {

      this.is_add_loading = true
      this.$http.addChannel(this.channel_form).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '添加成功')
          this.getChannel()
          this.channel_edit = false
        }
        this.is_add_loading = false
      }).catch(() => {
        this.is_add_loading = false
      })
    },
    editSubmit() {
      this.is_add_loading = true
      this.$http.editChannel(this.channel_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '编辑成功')
          this.getChannel()
          this.channel_edit = false
        }
        this.is_add_loading = false
      }).catch(() => {
        this.is_add_loading = false
      })
    },
    cancelAdd() {
      this.channel_edit = false
    },


  }
}
</script>

<style>
</style>