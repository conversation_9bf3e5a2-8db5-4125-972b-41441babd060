<template>
    <div class="member-comm">
        <div class="header" v-if="!preview">
            <el-button type="primary" @click="add()">添加</el-button>
            <div class="commission-text">
                应收佣金 - 扣除款项 = 可分业绩提成，共计 <span class="commission-text-red">{{allCommission}}</span> 元
            </div>
        </div>
        <el-table v-loading="loading" :data="list" class="house_table" border
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
            <el-table-column label="操作时间" v-slot="{ row }" v-if="!preview">
                {{ row.created_at }}
            </el-table-column>
            <el-table-column label="员工姓名" v-slot="{ row }">
                {{ row.user_name }}
            </el-table-column>
            <el-table-column label="比例" v-slot="{ row }">
                {{ row.proportion }}
            </el-table-column>
            <el-table-column label="所得佣金" v-slot="{ row }">
                {{ row.commission }}
            </el-table-column>
            <el-table-column label="备注" v-slot="{ row }">
                {{ row.descp }}
            </el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }" v-if="!preview">
                <el-link style="margin-right: 20px;" type="primary" @click="edit(row)">编辑</el-link>
                <el-link type="danger" @click="del(row)">删除</el-link>
            </el-table-column>
        </el-table>
        <div class="footer">
            <div></div>
            <div class="commission-text" v-if="memberCommissionCount">
                业绩提成合计：<span class="commission-text-red">{{memberCommissionTotal}}</span> 元
            </div>
        </div>
    
        <addMemberCommission v-if="dialogs.add" :reportId="reportData.report_id" ref="add"/>
    </div>
</template>

<script>
import addMemberCommission from './add_member_commission.vue'
export default {
    name: 'crmDealReportMemberCommission',
    components: {
        addMemberCommission
    },
    props: {
        reportData: {type: Object, default: ()=>{return {}}},
        preview: {type: Boolean, default: false}
    },
    data(){
        return {
            loading: false,
            list: [],
            dialogs: {
                add: false
            }
        }
    },
    computed: {
        //可分业绩提成
        allCommission(){
            return Math.max(0,this.reportData.receive_commission.commission- this.reportData.company_commission.commission).toFixed(2);
        },
        //业绩提成合计
        memberCommissionCount(){
            return this.reportData?.member_commission?.count || 0;
        },
        //业绩提成合计
        memberCommissionTotal(){
            return this.reportData?.member_commission?.commission || 0;
        },
    },
    created(){
        this.getList();
    },
    onPageRefresh(){
        this.getList();
    },
    methods: {
        async getList(){
            this.loading = true;
            const res = await this.$http.memberCommissionListAPI(this.reportData.report_id);
            this.loading = false;
            if(res.status == 200){
                this.list = res.data;
                this.$emit('update:count', this.list.length);
            }
        },
        async add(data = {}){
            this.dialogs.add = true; 
            await this.$nextTick();
            data.report_id = this.reportData.report_id;
            data.amount = this.allCommission;
            this.$refs.add.open(data).onSuccess(()=>{
                this.getList();
                this.$emit('commissionChange');
            });
        },
        edit(data){
            this.add(data);
        },
        async del(data){
            const res = await this.$http.delMemberCommissionAPI(data.id);
            if(res.status == 200){
                this.$message.success( res.data?.msg || "删除成功");
                this.getList();
                this.$emit('commissionChange');
            }
        }
    }
        
}
</script>
<style lang="scss" scoped>
.member-comm{
    .header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
   .footer{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 10px;
    }
    .commission-text{
        font-size: 16px;
        color: #3c3c3c;
    }
    .commission-text-red{
        color: #f40;
        font-weight: 600;
    } 
}
</style>