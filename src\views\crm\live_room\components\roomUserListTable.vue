<template>
    <div>
        <myTable :table-list="curList" :header="columns" tooltipEffect="light"  v-loading="loading" select
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
			:cellClassName="cellClassName" ref="myTable"  @selection-change="selectionChange"></myTable>
		<sendPrivateMessage ref="sendPrivateMessage" v-if="dialogs.sendPrivateMessage"/>
	</div>
</template>
<script>
import myTable from "@/components/components/my_table";
import sendPrivateMessage from './sendPrivateMessage.vue';
import userClueRoomList from './userClueRoomList.vue';
export default {
    props: {
        params: { type: Object, default: () => ({}) },
        list: { type: Array, default: () => [] },
		liuzi:{ type: Boolean, default: false},
		loading: false,
    },
	components: {
		myTable, sendPrivateMessage, userClueRoomList
	},
	inject: ['getUserStatusList'],
	data() {
		return {
			multipleSelection: [],
			curList: [],
			dialogs: {
				sendPrivateMessage: false
			}
		}
	},
	watch: {
		list: {
			handler(data){
				this.curList = data.map(e => {
					return {...e, 
						//手机号编辑
						__newMobile: '',
						__mobileSaving: false,
						__showMobielEditor: false,			
						//用户标记状态编辑
						__newUserStatus: '',
						__userStatusSaving: false,
						__showUserStatusEditor: false,
						//用户所有留资直播间		
						__clueRooms: [],					
						__clueRoomsLoading: false,
						//手机号是否已显示全号
						__wholePhoneNumberDisplaied: false,
					};
				})
			},
			immediate: true
		}
	},
	computed: {
		//是否指定直播间
		isRoomId(){
			return this.params.room_id ? true : false
		},
		columns(){	
			const userCloumn = {
				prop: "id",
				label: "用户信息",
				fixed: "left",
				minWidth: 220,
				render: (h, data) => {
					return (
						<div class={['user-box',{client: data.row.client_id}]} onClick={()=>this.goCustomerDetail(data.row)}>
							<div class="avatar">
								{data.row.avatar ? 
								<img src={data.row.avatar} class="avatar-img"/> : 
								<span class="avatar-word">{this.getFirstWord(data.row.nick_name)}</span>
								}
							</div>
							<div class="info">
								<p class="nickname">{data.row.nick_name}</p>
								{data.row.ies_uniq_id && (data.row.user_pc ? <el-popover
										placement="right"
										trigger="hover">
										<div>
											用户主页 <el-button style="margin-left:12px" type="success" size="mini" onClick={()=>this.copyText(data.row.user_pc)}>复制</el-button>
										</div>
										<p slot="reference" class="douyin-id">{data.row.ies_uniq_id}</p>
									</el-popover>: 
									<p class="douyin-id">{data.row.ies_uniq_id}</p>
								)}
							</div>
						</div>
					);
				}
			};
			const userAllViewRoomrender = (h, data) => {
				return (
					<div>
						{data.row.times && !this.loading ? <div>
							<el-popover
								placement="left"
								title=""
								width="1000"
								popper-class="user-clue-room-popover"
								trigger="click">
								<div v-loading={data.row.__clueRoomsLoading}>
									<userClueRoomList data={data.row.__clueRooms} onSearch={this.handleRoomChange}></userClueRoomList>
								</div>
								<el-link slot="reference" type="primary" underline={false} onClick={()=>this.getUserAllClueRooms(data.row, data.index)}>{data.row.times}</el-link>
							</el-popover>
						</div>:data.row.times}
					</div>
				);
			}
			const mobileColumn = {
				prop: "mobile",
				label: "手机号",
				minWidth: 180,
				render: (h, data)=>{
					return (<div>{data.row.__showMobielEditor?
							<div>
								<el-input v-model={data.row.__newMobile} placeholder="请输入手机号" size="mini"  type="number"  disabled={data.row.__mobileSaving}/>
								<div class="edit-btns">
								{data.row.__mobileSaving?
									<el-link type="primary" underline={false} disabled={data.row.__mobileSaving}>正在保存...</el-link>
									:<div>
										<el-link type="text" underline={false} onClick={()=>this.toggleMobileEditor(data.row)}>取消</el-link>
										<el-link type="primary" underline={false} onClick={()=>this.saveMobile(data.row)}>保存</el-link>
									</div>
								}
								</div>
							</div>
							:<div>
								{data.row.mobile && <span class={['mobile',{client: !data.row.__wholePhoneNumberDisplaied}]} onClick={()=>this.displayWholePhoneNumber(data.row)}>{data.row.mobile}</span>}
								{!data.row.mobile && <div class="edit-btn" onClick={()=>this.toggleMobileEditor(data.row)}><img src={this.$imageDomain+"/static/admin/customer/<EMAIL>"}/></div>}
							</div>}
					</div>)
				}
			}
			const commentRoomrender = (h, data, filed)=>{
				return (
					<div>
						{data.row[filed] ?
						<el-popover
							placement="top-start"
							width="380"
							trigger="hover">
							<div class="comment-popover" domPropsInnerHTML={data.row[filed].replace(/\n/g, '<br>')}></div>
							<div slot="reference"><div class="comment">{data.row[filed]}</div></div>
						</el-popover>
						:''}
					</div>
				)
			}
			const capitalRoomrender = (h, data, filed)=>{
				return (
					<div>
						{data.row[filed]?"已有维护人":"暂无维护人"}
					</div>
				)
			}
			const userStatusRender = (h, data) => {
				if(data.row.__showUserStatusEditor){
					return (
						<div>
							<el-select v-model={data.row.__newUserStatus} placeholder="请选择" size="mini" disabled={data.row.__userStatusSaving}>
								{this.getUserStatusList().map(item => (
									<el-option key={item.value} label={item.label} value={item.value}></el-option>
								))}
							</el-select>
							<div class="edit-btns">
							{data.row.__userStatusSaving?
								<el-link type="primary" underline={false} disabled={data.row.__userStatusSaving}>正在保存...</el-link>
								:<div>
									<el-link type="text" underline={false} onClick={()=>this.toggleUserStatusEditor(data.row)}>取消</el-link>
									<el-link type="primary" underline={false} onClick={()=>this.saveUserStatus(data.row)}>保存</el-link>
								</div>
							}
							</div>
						</div>
					)
				}else{
					return (
						<div>
							{data.row.user_status_name}
							<div class="edit-btn" onClick={()=>this.toggleUserStatusEditor(data.row)}><img src={this.$imageDomain+"/static/admin/customer/<EMAIL>"}/></div>
						</div>
					)
				}
			}
			const opColumn = {
				label: "操作",
				minWidth: 180,
				fixed: 'right',
				render: (h, data) => {
					return (
						<el-link type="primary" onClick={()=>this.openSendPrivateMessage(data.row)}>发私信</el-link>
					);
				}
			}

			if(!this.isRoomId){
				if(this.liuzi){
					return [
					userCloumn,
					{ prop: "comment", label: "互动内容", minWidth: 220, render: (h, data)=>commentRoomrender(h, data, 'latest_chat')},
					{ prop: "desc", label: "留资描述", minWidth: 200, },
					{ prop: "follow_name", label: "留资状态", minWidth: 180, render: (h, data)=>capitalRoomrender(h, data, 'follow_name')},
					{ prop: "follow_name", label: "维护人", minWidth: 160 },
					mobileColumn,
					{ prop: "comments_num", label: "评论条数", minWidth: 160 },
					{ prop: "times", label: "互动场次", minWidth: 180, render: userAllViewRoomrender},
					{ prop: "times_fill", label: "直播间留资次数", minWidth: 180 },
					{ prop: "latest_time", label: "最新评论时间", minWidth: 180, },
					{ prop: "user_status", label: "标记状态", minWidth: 160, fixed: 'right', render: userStatusRender },
					opColumn
				];
				}else{
					return [
					userCloumn,
					{ prop: "comment", label: "互动内容", minWidth: 220, render: (h, data)=>commentRoomrender(h, data, 'latest_chat')},
					{ prop: "desc", label: "留资描述", minWidth: 200, },
					{ prop: "follow_name", label: "维护人", minWidth: 160 },
					mobileColumn,
					{ prop: "comments_num", label: "评论条数", minWidth: 160 },
					{ prop: "times", label: "互动场次", minWidth: 180, render: userAllViewRoomrender},
					{ prop: "times_fill", label: "直播间留资次数", minWidth: 180 },
					{ prop: "latest_time", label: "最新评论时间", minWidth: 180, },
					{ prop: "user_status", label: "标记状态", minWidth: 160, fixed: 'right', render: userStatusRender },
					opColumn
				];
				}
			}else{
                return [
					userCloumn,
					{ prop: "comment", label: "互动内容", minWidth: 220, render: (h, data)=>commentRoomrender(h, data, 'comment')},
					{ prop: "desc", label: "留资描述", minWidth: 200, },
					{ prop: "follow_name", label: "留资状态", minWidth: 180, render: (h, data)=>capitalRoomrender(h, data, 'follow_name')},
					{ prop: "follow_name", label: "维护人", minWidth: 160 },
					mobileColumn,
					{ prop: "client_province_name", label: "省份", minWidth: 180, },
					{ prop: "client_city_name", label: "城市", minWidth: 150, },
					{ prop: "add_time_begin", label: "第一条评论时间", minWidth: 200, },
					{ prop: "add_time_end", label: "最后一条评论时间", minWidth: 200, },
					{ prop: "comments_num", label: "评论条数", minWidth: 160 },
					{ prop: "times", label: "互动场次", minWidth: 180, render: userAllViewRoomrender },
					{ prop: "times_fill", label: "所有直播间留资次数", minWidth: 180 },
					{ prop: "client_tags", label: "特征标签", minWidth: 220, tooltip: true },
					{ prop: "client_clue_time", label: "留资时间", minWidth: 180, },
					{ prop: "user_status", label: "标记状态", minWidth: 160, fixed: 'right', render: userStatusRender },
					opColumn
				]
			}
		}
  	},
	methods: {
        getFirstWord(str){
			return str.trim().replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '').substring(0, 1);
		},
		cellClassName({column}){
			return column.property == 'mobile' ? 'cell-mobile' : 
				column.property == 'user_status' ? 'cell-user_status' : 
				''
		},
		//发私信
		openSendPrivateMessage(row){
			this.dialogs.sendPrivateMessage = true;
			this.$nextTick(()=>{
				this.$refs.sendPrivateMessage.open(row);
			})
		},
		//获取用户所有留资的直播间
		async getUserAllClueRooms(row, index){
			if(row.__clueRooms && row.__clueRooms.length){
				return;
			}
			if(row.__clueRoomsLoading){
				return;
			}
			row.__clueRoomsLoading = true;
			const res = await this.$http.getUserAllClueRooms(row.sec_uid).catch(()=>{});
			row.__clueRoomsLoading = false;
			if(res && res.status == 200){
				row.__clueRooms = res.data || [];
			}
		},
        handleSelectionChange(val) {
			this.multipleSelection = val
		},
		selectionChange(val){
			this.$emit('child-event',val);
		},
		//切换直播间
		handleRoomChange(row){
            this.$emit("roomChange", row.room_id);
		},
		//打开crm客户详情页
		goCustomerDetail(row){
			if(!row.client_id){
				return;
			}
			this.$goPath('/crm_customer_detail?id='+row.client_id+'&type='+(row.is_my_client == 1 ? 'my' : 'seas'));
		},
		//复制
		copyText(txt){
			this.$onCopyValue(txt);
		},
		//编辑直播间用户手机号
		toggleMobileEditor(row){
			row.__newMobile = '';
			row.__showMobielEditor = !row.__showMobielEditor;
		},
		//保存直播间用户手机号
		async saveMobile(row){
			if(row.__newMobile === ''){
				this.$message.warning('请输入手机号');
				return;
			}
			const reg = /^1[3-9][0-9]\d{8}$/;
			if(!reg.test(row.__newMobile)){
				this.$message.warning('手机号格式不正确');
				return;
			}
			row.__mobileSaving = true;
			const res = await this.$http.updateDouyinUserMobile({ base_id: row.base_id, mobile: row.__newMobile}).catch(()=>{});
			row.__mobileSaving = false;
			if(res.status == 200){
				row.mobile = row.__newMobile; //.substring(0,3)+'*****'+row.__newMobile.substring(8);
				row.__wholePhoneNumberDisplaied = true;
				row.__showMobielEditor = false;
				//等于1时 直接提示 msg的内容 等于2时 需要弹出确认框 点击确认后请求推送客户信息接口
				if(res.data.status == 1){
					this.$message.success(res.data.msg || '修改成功');
				}else if(res.data.status == 2){
					this.$confirm(res.data.msg, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {
						this.$http.pushDouyinUserToCRM({base_id: row.base_id}).then(res=>{
							if(res.status == 200){
								this.$message.success(res.data?.msg || '推送成功');
							}
						})
					})
				}
			}
		},
		//编辑用户标记状态
		toggleUserStatusEditor(row){
			row.__newUserStatus = row.user_status;
			row.__showUserStatusEditor = !row.__showUserStatusEditor;
		},
		async saveUserStatus(row){
			if(row.__newUserStatus === ''){
				this.$message.warning('请选择标记状态');
				return;
			}
			row.__userStatusSaving = true;
			const res = await this.$http.updateDouyinRoomUserStatus({ base_id: row.base_id, user_status: row.__newUserStatus}).catch(()=>{});
			row.__userStatusSaving = false;
			if(res.status == 200){
				this.$message.success(res.data?.msg || '修改成功');
				row.user_status = row.__newUserStatus;
				row.user_status_name = (this.getUserStatusList().find(e=>e.value == row.user_status) || {}).label;
				row.__showUserStatusEditor = false;
			}
		},
		//显示全号
		displayWholePhoneNumber(row){
			row.mobile_full && (row.mobile  = row.mobile_full);
			row.__wholePhoneNumberDisplaied = true;
		}
	}
}
</script>
<style  scoped lang="scss">
::v-deep{
	.user-box{
		display: flex;
		flex-direction: row;
		height: 50px;
		align-items: center;
		&.client{
			cursor: pointer;
		}
		.avatar{
			height: 32px;
			.avatar-word{
				display: inline-block;
				height: 32px;
				width: 32px;
				border-radius: 50%;
				background-color: #f1f2f3;
				line-height: 32px;
				text-align: center;
				color: #c6c6c6;
				font-weight: 300;
			}
			.avatar-img{
				height: 32px;
				width: 32px;
				border-radius: 50%;
			}
		}
		.info{
			white-space: nowrap;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			padding: 0 0 0 12px;
			.nickname{
				text-align: left;
			}
			.douyin-id{
				color: #a9a9a9;
				font-size: 13px;
				font-weight: 400;
				text-align: left;
			}
		}
	}
	.mobile.client{
		cursor: pointer;
	}
	.comment{
		text-align: left;
		display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical; 
	}

	.cell-mobile, .cell-user_status{
		position: relative;
		padding: 2px 0;
		.edit-btn{
			position: absolute;
			bottom: 5px;
			right: 5px;
			cursor: pointer;
			width: 22px;
			height: 22px;
			line-height: 22px;
			text-align: center;
		}
		&:hover .edit-btn{
			display: block;
			opacity: 1;
		}
		.edit-btns{
			text-align: right;
			.el-link{
				margin-left: 12px;
				font-size: 13px;
			}
		}
	}
	.cell-user_status .edit-btn{
		display: none;
	}
}
::v-deep {
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
</style>

<style lang="scss">
.user-clue-room-popover{
	min-height: 100px;
}

.comment-popover{
	line-height: 1.5;
}
</style>