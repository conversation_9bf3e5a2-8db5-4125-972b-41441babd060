<template>
	<div class="">
		<el-container>
			<!-- <el-header>
				<div class="btn-box">
					<router-link
						v-for="item in router_list"
						:key="item.id"
						tag="el-button"
						size="mini"
						:to="item.path"
						>{{ item.name }}</router-link
					>
				</div>
			</el-header> -->
			<router-view></router-view>
		</el-container>
	</div>
</template>

<script>
export default {
	data() {
		return {
			router_list: [
				{ path: "/system_msg", name: "系统消息" },
				{ path: "/add_system_msg", name: "添加消息" },
			],
		};
	},
};
</script>

<style scoped lang="scss">
.title {
	padding-bottom: 10px;
	border-bottom: 1px solid #999;
}
</style>
