// 把对象数据按照某一个属性进行分类
import store from "../store/index";
export function sortPro(data, keys = []) {
  //keys可以传一个数组
  var c = [];
  var d = {};
  for (var element of data) {
    let element_keyStr = "";
    let element_key = [];
    let element_keyObj = {};
    for (var key of keys) {
      element_key.push(element[key]);
      element_keyObj[key] = element[key];
    }
    element_keyStr = element_key.join("_");
    if (!d[element_keyStr]) {
      c.push({
        ...element_keyObj,
        children: [element],
      });
      d[element_keyStr] = element;
    } else {
      for (var ele of c) {
        let isTrue = keys.some((key) => {
          return ele[key] != element[key];
        });
        if (!isTrue) {
          ele.children.push(element);
        }
      }
    }
  }
  return c;
}
// 传地址栏参数名称拿数据
export function GetQueryString(name, search) {
  search =
    search ||
    window.location.search.substr(1) ||
    window.location.hash.split("?")[1];
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  let r = search.match(reg);
  if (r != null) return unescape(r[2]);
  return null;
}
export function sortArr(arr, str) {
  var _arr = [],
    _t = [],
    // 临时的变量
    _tmp;

  // 按照特定的参数将数组排序将具有相同值得排在一起
  arr = arr.sort(function (a, b) {
    var s = a[str],
      t = b[str];

    return s < t ? -1 : 1;
  });

  if (arr.length) {
    _tmp = arr[0][str];
  }
  // console.log( arr );
  // 将相同类别的对象添加到统一个数组
  for (var i in arr) {
    if (arr[i][str] === _tmp) {
      _t.push(arr[i]);
    } else {
      _tmp = arr[i][str];
      _arr.push(_t);
      _t = [arr[i]];
    }
  }
  // 将最后的内容推出新数组
  _arr.push(_t);
  return _arr;
}
//获取字典数组
export function getDictionary(type) {
  let list = [];
  this.$http.dictionaryFind(type).then((res) => {
    if (res.status === 200) {
      res.data.data.map((item) => {
        list.push(item);
      });
    }
  });
  return list;
}

// byte 转  MB
export function bytesToSize(bytes) {
  if (bytes === 0) return "0 B";
  var k = 1024,
    sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
    i = Math.floor(Math.log(bytes) / Math.log(k));

  return (bytes / Math.pow(k, i)).toPrecision(3) + " " + sizes[i];
}

// 树形结构   将传入的数组处理为树形结构
export function toTree(data) {
  let result = [];
  if (!Array.isArray(data)) {
    return result;
  }
  data.forEach((item) => {
    delete item.children;
  });
  let map = {};
  data.forEach((item) => {
    map[item.id] = item;
  });
  data.forEach((item) => {
    let parent = map[item.pid];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      result.push(item);
    }
  });
  return result;
}

// 判断数组中是否存在传入的按钮权限
// export function hasShow(val) {
//   return store.state.perms.some((item) => {
//     return item === val;
//   });
// }
export function hasShow() {
  return true;
}
//循环数组将数组中children放入数组
export function recursionArr(arr) {
  var new_arr = arr;
  for (let i in arr) {
    if (arr[i].children) {
      var data = arr[i].children;
      new_arr = new_arr.concat(data);
    }
  }
  return new_arr;
}
// 获取一个处理子路由的方法
export function getChildrenRouter(arr) {
  var router_children_table = arr.filter((item) => {
    return item.children;
  });
  var children = [];
  router_children_table.map((item) => {
    children = item.children;
  });
  var children_path = [];
  recursionArr(children).map((item) => {
    children_path.push(item);
  });

  return children_path;
}

/**
 *  @title 后台接口请求字典数据
 *  @per_page {200} 一页显示的总数量
 *  @description 页面需要字典数据类型多的情况下使用，other times use $getDictionaryList
 *  @method use this.$setDictionary(e=>{})
 * */

export function setDictionary(callback) {
  let dictionary_list = store.state.dictionary_list;
  const arr = [];
  if (dictionary_list.length === 0) {
    this.$http.queryDicData(localStorage.getItem("website_id")).then((res) => {
      if (res.status === 200) {
        store.commit("setDictionary_list", res.data);
        res.data.forEach((item) => {
          const parent = arr.find((cur) => cur.name === item.name);
          if (parent) {
            parent.childs.push(item);
          } else {
            const obj = {
              name: item.name,
              childs: [item],
            };
            arr.push(obj);
          }
        });
        callback(arr);
      }
    });
  } else {
    dictionary_list.forEach((item) => {
      const parent = arr.find((cur) => cur.name === item.name);
      if (parent) {
        parent.childs.push(item);
      } else {
        const obj = {
          name: item.name,
          childs: [item],
        };
        arr.push(obj);
      }
    });
    callback(arr);
  }
}

/**
 *  @description  匹配值对应数组参数
 *  @arr 匹配数组
 *  @type 匹配类型
 * */

export function computedValueType(arr, value) {
  if (arr.length > 0) {
    return arr[value]["description"];
  }
}

//  跳转路径
// 判断资源是不是远程资源
const isHttp = function (val) {
  let httpArr = ["http", "https"];
  return httpArr.includes(val.split("://")[0]);
};
export function goPath(path) {
  // this.$router.push(path + "?website_id=" + localStorage.getItem("website_id"));
  if (!path) {
    return;
  }
  if (store.state.is_qywx_expire) {
    //如果企业微信授权到期跳转首页
    this.$router.push("/crm_customer_index");
    return;
  }
  // 如果是超链接则打开webView页面
  if (isHttp(path)) {
    window.location.href = path;
    return;
  }
  const reg = /\?.+=.{0,}/;
  const website_id =
    store.state.website_info.website_id ||
    localStorage.getItem("website_id") ||
    1;
  //如果清除缓存从本地存储取得website_id放入缓存
  if (
    path.indexOf("?website_id=") === -1 &&
    path.indexOf("&website_id=") === -1
  ) {
    if (reg.test(path)) {
      path += `&website_id=${website_id}`;
    } else {
      path += `?website_id=${website_id}`;
    }
  }
  this.$router.push(path);
}

/**
 * @description  封装导出excel表格
 * @res 接口返回数据
 * */

export function exportExcelTable(res) {
  var disposition = res.headers["content-disposition"];
  var fileName = decodeURI(
    disposition.substring(disposition.indexOf("filename=") + 9),
    disposition.length
  );
  let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, fileName);
  } else {
    const link = document.createElement("a");
    link.style.display = "none";
    var href = URL.createObjectURL(blob);
    link.href = href;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(href); // 释放掉blob对象
  }
  this.$message({
    showClose: true,
    message: "生成完毕，请保存",
    type: "success",
  });
}

/**
 * @description 复制点击内容
 * @value 复制内容
 * */

export function onCopyValue(value) {
  const input = document.createElement("input");
  document.body.appendChild(input);
  input.setAttribute("value", value);
  input.select();
  if (document.execCommand("copy")) {
    document.execCommand("copy");
  }
  document.body.removeChild(input);
  this.$message.success("复制成功：" + value);
}

// 获取地址参数方法
export function queryUrlParams(url) {
  let result = {},
    reg1 = /([^?=&#]+)=([^?=&#]+)/g,
    reg2 = /#([^?=&#]+)/g;
  url.replace(reg1, (n, x, y) => (result[x] = y));
  url.replace(reg2, (n, x) => (result["HASH"] = x));
  return result;
}
// 获取当前时间
export const getTime = function (type) {
  var date = new Date(),
    year = date.getFullYear(),
    month = date.getMonth() + 1,
    day = date.getDate(),
    hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours(),
    minute =
      date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(),
    second =
      date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  month >= 1 && month <= 9 ? (month = "0" + month) : "";
  day >= 0 && day <= 9 ? (day = "0" + day) : "";
  // + " " + hour + ":" + minute;
  console.log(second);
  if (type === "YMDhm") {
    return year + "-" + month + "-" + day + " " + hour + ":" + minute;
  } else if (type === "YMD") {
    return year + "-" + month + "-" + day;
  }
};
export const imageDomain = "https://img.tfcs.cn/backup"
