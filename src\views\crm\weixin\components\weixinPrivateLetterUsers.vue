<template>
<div v-fixed-scroll="48">
	<div class="filter-wrapper">
		<el-form inline size="medium">
			<el-form-item label="视频号">
				<el-select v-model="params.account_id" clearable placeholder="选择视频号" @change="search" filterable>
					<el-option v-for="item in accountList" :key="item.id" :label="item.nickname" :value="item.id">
						<span>{{item.nickname}}</span><small class="option-room-name">{{item.uniqId}}</small>
					</el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="昵称">
				<el-input placeholder="请输入" v-model="params.nick_name"></el-input>
			</el-form-item>
			
			<el-form-item label="手机号">
				<el-input placeholder="请输入" v-model="params.mobile"></el-input>
			</el-form-item>

			<el-form-item label="标记状态">
				<el-select v-model="params.user_status" clearable placeholder="选择标记状态" @change="search">
					<el-option v-for="item in userStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="私信日期">
				<el-date-picker v-model="params.dates" value-format="yyyy-MM-dd"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期">
				</el-date-picker>
			</el-form-item>
			<el-form-item label="留资状态" v-if="params.fill == '1'">
				<el-select v-model="params.has_follow" clearable placeholder="选择留资状态" @change="search">
					<el-option label="全部" :value="0"></el-option>
					<el-option label="已有维护人" :value="1"></el-option>
					<el-option label="暂无维护人" :value="2"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="">
				<el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
			</el-form-item>
		</el-form>
	</div>
	<div class="operatebtn">
		<el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
            <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="TransferCustomer(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" style="margin-right:10px;" @click="getCrmDepartmentList" class="search_loudong div row align-center">
                <div class="seach_value">操作</div>
                <div class="sanjiao" :class="{ transt: transfer_type }"></div>
            </div>
        </el-popover>
	</div>
	<weixinPrivateLetterUserTable @child-event="handleChildEvent" :params="params" :list="list" :loading="loading" :roomid="roomid"></weixinPrivateLetterUserTable>
	
	<div class="tab-content-footer">
		<div>
			<el-radio-group v-model="params.fill" @change="search" size="small">
				<el-radio-button label="1">已留资</el-radio-button>
				<el-radio-button label="0">未留资</el-radio-button>
			</el-radio-group>
		</div>
		<el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="count"
		:page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
		@current-change="onPageChange" @size-change="handleSizeChange">
		</el-pagination>
	</div>
	<transmitassembly ref="transmitassembly" @getDataList="search"></transmitassembly>
</div>
</template>
<script>
import weixinPrivateLetterUserTable from "./weixinPrivateLetterUserTable.vue";
import transmitassembly from "@/views/crm/live_room/components/transmitassembly.vue"
import weixinHttp from "@/utils/weixinHttp";
export default {
	components: {
		weixinPrivateLetterUserTable,
		transmitassembly
	},
	provide() {
        return {
            getUserStatusList: this.getUserStatusList,  
        }
    },
	data() {
		return {
			loading: false,
			params: {
				page: 1,
				per_page: 10,
				nick_name: '',
				mobile: '',
				fill: '1',  // 默认选中已留资
				user_status: '',
				dates: [],
				account_id: '',
				has_follow: ''
			},
			userStatusList: [],  //用户标记状态列表
			count: 0,
			list: [],
			accountList: [],
			roomid:true,  // 默认显示留资状态列
			transfer_type: false, // 显示/隐藏转交类型
			// 转交类型
			cus_list: [
				{ id: 1, name: "转交到同事" },
				{ id: 2, name: "复制到同事的流转客" },
			],
			datalist:[],
			multipleSelection:[], 
		}
	},
	computed: {
	},
	watch:{
		"params.fill"(val){
			if(val==1){
				this.roomid = true
			}else{
				this.roomid = false
			}
		},
	},
	mounted(){
		let pagenum = localStorage.getItem( 'pagenum')
		this.params.per_page = Number(pagenum)||10
		this.getList();
		this.getCondition();
	},
	methods: {
		//获取私信用户搜索项
		async getCondition(){
			const res = await weixinHttp.getWeixinPrivateLetterSearchCondition();
			if(res.status == 200){
				this.accountList = res.data?.account_list || [];

				const obj = res.data?.user_status_list || {}
				this.userStatusList = Object.keys(obj).map(e=>(
					{label: obj[e], value: e * 1}
				))
			}
		},
		//获取私信用户列表
		async getList(){
			const params = {...this.params};
			if(params.dates && params.dates.length){
				[params.start_date, params.end_date] = params.dates;
			}
			delete params.dates;
			this.loading = true;
			const res = await weixinHttp.getWeixinPrivateLetterUsers(params).catch(()=>{});
			this.loading = false;
			if(res.status == 200){
				this.count = res.data?.total || 0;
				this.list = res.data?.data || [];
			}
		},
		getCrmDepartmentList() {
			// this.transfer_type = true
		},
		//选中的人员
		handleChildEvent(data){
			this.multipleSelection = data
		},
		// 转交客户
		TransferCustomer(item) {
			// 判断是否选中客户
			if (!this.multipleSelection.length) {
				return this.$message({
					message: "请选择客户",
					type: "warning",
				});
			}
			if(this.multipleSelection.length&&this.multipleSelection.length>50){
				return this.$message({
					message: "每次最多选择50个客户",
					type: "warning",
				});
			}
			const nonZeroClientIdData = this.multipleSelection.filter(item => item.client_id !== 0);
			const clientIds = nonZeroClientIdData.map(item => item.client_id);
			console.log(clientIds);
			if(!clientIds.length){
				return this.$message.warning("没有关联的客户")
			}
			this.$refs.transmitassembly.open(this.multipleSelection ,item)
		},
		search(){
			this.params.page = 1;
			this.getList();
		},
		handleRoomChange(room_id){
			this.$emit('room-change', room_id)
		},
		copyText(txt){
			this.$onCopyValue(txt);
		},
		getUserStatusList(){
			return this.userStatusList;
		},
		onPageChange(e){
			this.params.page = e;
			this.getList();
		},
		handleSizeChange(e){
			this.params.per_page = e;
			this.search();
		}
	}
}
</script>
<style  scoped lang="scss">
.filter-wrapper{
	padding: 16px 16px 12px;
}
::v-deep{
	.option-room-name{
		color: #a9a9a9;
		margin-left: 12px;
		float: right;
	}
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
.operatebtn{
	width: 100%;
	display: flex;
	justify-content: flex-end;
	margin-bottom: 10px;
}
.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}
.search_loudong {
  width: 70px;
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 30px;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
</style>