<template>
    <div>
        <div class="material_list">
            <div 
                class="material_main" 
                v-for="(item, index) in forData_list"
                :key="index"
            >
                <div class="material_edge">
                    <!-- 多选框 -->
                    <div class="material-checkbox">
                        <el-checkbox></el-checkbox>
                    </div>
                    <!-- 展示图 -->
                    <div class="material-pictures">
                        <img :src="item.thumb_path" alt="">
                    </div>
                    <!-- 标题栏 -->
                    <div class="material-title">
                        <div class="title">{{ item.filename }}</div>
                        <div class="modify">修改： {{ item.create_time }}</div>
                        <!-- <div class="title-size">大小： {{ (item.media_size/ 1024).toFixed(1) }} MB</div> -->
                        <div class="title-size">VR作品： {{ item.is_bind_project == 1 ? '已绑定' : '未绑定' }}</div>
                        <div class="switch" @click="onPreview(item)">
                            <span>预览</span>
                        </div>
                    </div>
                    <!-- 控制按钮 -->
                    <div class="material-Func flex-row">
                        <div class="material-anew">
                            <el-button plain @click="onCopyPhoto(item)">复制</el-button>
                        </div>
                        <!-- <div class="material-cover">
                            <el-button plain>更改封面</el-button>
                        </div> -->
                        <!-- <div class="material-complete">
                            <el-button plain>完善信息</el-button>
                        </div> -->
                        <div class="material-rename">
                            <el-button 
                                plain 
                                @click="materialRename(item)"
                            >
                                重命名
                            </el-button>
                        </div>
                        <div class="material-delete">
                            <el-button 
                                plain
                                @click="deleteMaterial(item)"   
                            >删除</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog
            title="重命名"
            :visible.sync="dialogMaterialRename"
            width="680px"
        >
            <el-form :model="params_rename" label-width="80px">
                <el-form-item label="图片名称">
                    <el-input v-model="params_rename.filename"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer Rename-footer">
                <el-button @click="dialogMaterialRename = false">取 消</el-button>
                <el-button type="primary" @click="confirmRename">确 定</el-button>
            </span>
        </el-dialog>
        <!-- <el-dialog 
            :visible.sync="dialogUploadPicture" 
            class="dialog-Photo"
            @close="closePicture"
        >
            <img width="100%" :src="dialogPhoto" alt="">
        </el-dialog> -->
        <!-- ====== -->
        <div class="mask1" v-if="dialogUploadPicture" @click="dialogUploadPicture = false">
            <div class="preview_img" @click.prevent.stop="() => {}">
                <img id="preImg" :src="dialogPhoto" alt="" />
            </div>
        </div>
        <!-- ====== -->
    </div>
</template>
<script>
export default {
    props: {
        forData_list: {
            type: Array,
            default: () => {}
        },
    },
    data() {
        return {
            // 素材重命名参数
            params_rename: {
                filename: "", // 素材名称
                id: "", // 素材id
            },
            dialogPhoto: "",
            dialogMaterialRename: false, // 素材重命名模态框
            dialogUploadPicture: false, // 预览图片模态框
        }
    },
    methods: {
        // 素材预览
        onPreview(item) {
            this.dialogUploadPicture = true;
            this.dialogPhoto = item.thumb_path
        },
        // 素材重命名
        materialRename(item) {
            this.dialogMaterialRename = true;
            // 赋值当前重命名的素材id
            this.params_rename.id = item.pk_img_main;
            this.params_rename.filename = item.filename;
        },
        // 确定将素材重命名
        confirmRename() {
            this.$http.setPanoramaName(this.params_rename).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    });
                    this.dialogMaterialRename = false;
                    // this.getDataList();
                    this.$emit("materialList", "");
                }
            })
        },
        // 关闭预览模态框
        closePicture() {
            this.dialogPhoto = "";
        },
        // 删除素材
        deleteMaterial(item) {
            this.$confirm('此操作将永久删除该素材, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.deletePanoramaPicture(item.pk_img_main).then((res) => {
                    if(res.status == 200) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.$emit("materialList", "");
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        // 复制图片路径
        onCopyPhoto(item) {
            this.$onCopyValue(item.thumb_path)
        },
    }
}
</script>
<style lang="scss" scoped>
.material_list {
    // box-sizing: border-box;
    // background-color: #F1F4FA;
    // max-height: calc(100vh - 335px);
    // overflow-y: auto;
    ::v-deep .material_main {
        padding: 24px 24px 0 24px;
        background-color: #FFFFFF;
        cursor: pointer;
        .material_edge {
            display: flex;
            padding-bottom: 24px;
            border-bottom: 1px solid #F1F4FA;
        }
        .material-checkbox {
            line-height: 160px;
        }
        .material-pictures {
            width: 160px;
            height: 160px;
            background-color: #F8F8F8;
            margin-left: 12px;
            position: relative;

            & img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        .material-title {
            display: flex;
            flex-direction: column;
            margin-left: 24px;
            .title {
                font-size: 16px;
                color: #2E3C4E;
                font-weight: 600;
            }
            .title-size, .modify {
                font-size: 14px;
                color: #8A929F;
                line-height: 20px;
                font-weight: 400;
            }
            .modify {
                margin: 12px 0;
            }
            .switch {
                width: 88px;
                text-align: center;
                box-sizing: border-box;
                background: #FFFFFF;
                font-size: 14px;
                color: #2D84FB;
                font-weight: 500;
                margin-top: auto;
                padding: 7px 19px;
                border-radius: 4px;
                border: 1px solid #DDE1E9;
                cursor: pointer;
            }
            .switch:hover {
                border-color: #2D84FB;
            }
        }
        .material-Func {
            margin-left: auto;
            align-items: center;
            .material-anew {
                .el-button {
                    border-color: #DDE1E9;
                    color: #8A929F;
                    padding: 8px 29px;
                }
                .el-button:hover {
                    color: #2D84FB;
                    border-color: #2D84FB;
                }
            }
            .material-rename, .material-delete {
                margin-left: 12px;
            }
            .material-rename, .material-delete {
                // .el-button {
                //     border-color: #DDE1E9;
                //     color: #8A929F;
                // }
                .el-button.is-plain:hover {
                    color: #2D84FB;
                    border-color: #2D84FB;
                }
            }
            .material-rename {
                .el-button {
                    color: #8A929F;
                    padding: 8px 22px;
                }
            }
            .material-delete {
                .el-button {
                    color: #8A929F;
                    padding: 8px 29px;
                }
            }
        }
    }
    .material_main:hover {
        background-color: #f7faff;
        transition: .36s;
    }
}
::v-deep .dialog-Photo {
    .el-dialog {
        .el-dialog__header {
            .el-dialog__title {
                border-left: none;
            }
        }
    }
}
.mask1 {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    top: 60px;
    bottom: 0;
    right: 0;
    left: 230px;
    padding: 10px;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    .preview_img {
        position: relative;
        img {
            max-width: 1000px;
            object-fit: cover;
        }
        .img {
            max-width: 800px;
            height: 600px;
            overflow-y: auto;

            img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            }
        }
    }
}
</style>