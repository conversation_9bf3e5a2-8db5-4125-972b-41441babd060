<template>
    <div>
        <el-select ref="select"  @change="onChange" v-model="selectVal" :allow-create="isAllowCreate" :multiple-limit="multipleLimitNum" :placeholder="placeholder" v-bind="$attrs" v-on="$listeners" :filterable="filterable" :remote="remote" :remote-method="search" 
            :style="{width:width}"
            :loading="loading">
            <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item[valueKey]"></el-option>
            <template #empty v-if="isEmpty && projectType == 2">
                <div class="add_community" @click="openAddCommunityPage">
                    <span data-v-1cdc7620="">没找到？申请添加</span>
                    <i data-v-1cdc7620="" class="el-icon-circle-plus"></i>
                </div>
            </template>
        </el-select>
        <el-dialog width="600px" :visible.sync="addCommunity.show" title="申请小区" append-to-body v-if="projectType == 2">
            <addCommunity  v-if="addCommunity.show"
                dialogTitle="addData"
                :form="addCommunity.params"
                @success="onAddCommunitySuccess"
            ></addCommunity>
        </el-dialog>
    </div>
</template>

<script>
let list = [];
import addCommunity from "@/components/navMain/house/components/addCommunity.vue"
import { env } from 'echarts';
export default {
    components: {
        addCommunity
    },
    props: {
        value: {type: [Array,String,Number], default: ''},
        datas: {type: Array, default: ()=>[]},
        filterable: {type: Boolean, default: true},
        remote: {type: Boolean, default: true},
        valueKey: {type: String, default: "id"},
        placeholder: {type: String, default: "请搜索并选择项目"},
        width: {type: String, default: "auto"},
        allowCreate: {type: Boolean, default: false},
        multipleLimit: {type: Number, default: 0}
    },
    data(){
        return {
            loading:false,
            isDataLoaded: false,
            selectVal: '',
            list: [],
            projectType: 0,
            isAllowCreate: false,
            multipleLimitNum: 0,
            isEmpty: false,
            searchKeyword: '',
            addCommunity: {
                show: false,
                params: {
                    title: "",
                    addr: "",
                    lat: "",
                    lng: "",
                    region_id: '',
                    region_name: ''
                }
            }
        }
    },
    computed: {
        currentProjectType(){
            return this.$store.state.crmConfigProjectType;
        }
    },
    watch: {
        datas: {
            handler(d){
                this.list = d;
            },
            immediate: true
        },
        value: {
            handler(val){
                this.selectVal = val;
            },
            immediate: true
        },
        currentProjectType: {
            handler(val){
                //默认项目为小区库：不使用自动创建，多选最多选10个
                if(val == 2){
                    this.isAllowCreate = false;
                    this.multipleLimitNum = 10;
                }else{
                    //自建项目库
                    this.isAllowCreate = this.allowCreate;
                    this.multipleLimitNum = this.multipleLimit;
                }
            },
            immediate: true
        }
    },
    created(){
        this.projectType = this.currentProjectType;
        this.initMenuData();
    },
    activated(){
        if(this.projectType != this.currentProjectType){
            this.projectType = this.currentProjectType;
            this.loadData();
        }
    },
    deactivated(){
        list = [];
    },
    beforeDestroy(){
        list = [];
    },
    methods: {
        //初始化菜单数据
        initMenuData(){
            if(!this.isDataLoaded){
                this.isDataLoaded =  this.list.length > 0;
            }
            if(!this.isDataLoaded){
                this.isDataLoaded = true;
                if(list.length){
                    this.list = list
                }else{
                    this.loadData();
                }
            }
        },
        async queryCrmConfigProjectType(){
            await this.$store.dispatch('queryCrmConfigProjectType');
        },
        //获取菜单数据
        async loadData(keyword = ''){
            this.searchKeyword = keyword;
            await this.queryCrmConfigProjectType();
            this.projectType = this.currentProjectType;
            if(!this.projectType){
                return;
            }

            this.loading = true;
            try{
                const res = await this.$http.getCrmProjectByConfig(keyword);
                if(res.status == 200){
                    this.list = res.data || [];
                    if(keyword === ''){
                        list = [...this.list];
                    }

                    this.isEmpty = this.list.length == 0;
                }
            }catch(e){
                console.error(e);
            }
            this.loading = false;
        },
        search(keyword){
            this.isEmpty = false;
            this.loadData(keyword);
        },
        getOptionData(){
            return this.list.map(e => {
                return {
                    id: e.id,
                    value: e[this.valueKey],
                    label: e.name
                }
            })
        },
        setOptionData(data){
            if(!this.list.find(e => e[this.valueKey] === data[this.valueKey])){
                this.list.push(data);
            }
        },
        getOptionLabel(value){
            const item = this.list.find(e => e[this.valueKey] === value)
            return item ? item.name : '';
        },
        getSelectOption(){
            if(Array.isArray(this.selectVal)){
                return this.selectVal.map(e => {
                    return this.list.find(item => item[this.valueKey] === e);
                })
            }
            //单选
            return this.list.find(e => e[this.valueKey] === this.selectVal);
        },
        onChange(val){
            this.$emit('input', val)
            this.$emit('change', val)
        },
        //打开添加小区页面
        openAddCommunityPage(){
            this.addCommunity.params.title = this.searchKeyword;
            this.addCommunity.show = true;
        },
        //添加小区成功回调
        onAddCommunitySuccess(e){
            const data = {...e, name: e.title};

            this.list.push(data);
            this.isEmpty = false;
            let value = this.selectVal;
            if(Array.isArray(value)){
                this.multipleLimitNum > value.length && value.push(data[this.valueKey]);
            }else{
                value = data[this.valueKey];
            }
            this.onChange(value);
            this.addCommunity.show = false;
        }
    },
}
</script>

<style scoped lang="scss">
 
::v-deep .el-tag--small{
    height: 24px;
    padding:0 8px;
    line-height: 22px;
    display: inline-flex;
    max-width: 100%;
    align-items: center;
    .el-select__tags-text{
    flex:1;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    }
}
.add_community{
    color: #2d84fb;
    height: 46px;
    line-height: 46px;
    font-size: 14px;
    padding: 0 20px;
    cursor: pointer;
}

</style>