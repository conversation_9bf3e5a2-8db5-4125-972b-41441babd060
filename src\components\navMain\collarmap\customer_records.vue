<template>
    <el-dialog  title="获客记录" :visible.sync="show" width="1400px">
        <div class="queryarea">
            <el-form :inline="true" :model="params" class="demo-form-inline">
              <el-form-item label="时间" >
                <el-date-picker
                  v-model="params.times"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="客户名称">
                <el-input v-model="params.name" placeholder="客户名称"></el-input>
              </el-form-item>
              <el-form-item label="客户电话">
                <el-input v-model="params.phone" placeholder="客户电话"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onSubmit">查询</el-button>
              </el-form-item>
            </el-form>
        </div>
        <el-table
          :data="tabledata"
          style="width: 100%"
          :header-cell-style="{ background: '#EBF0F7' }"
          border
          v-loading="is_table_loading">
          <el-table-column
            prop="name"
            label="客户名称"
            align="center"
            fixed="left"
            width="200px"
            v-slot="{ row }">
                <div class="headdata">
                    <img v-if="row.headimgurl" class="head-imgurl" :src="row.headimgurl" alt="">
                    <img v-else class="head-imgurl" src="http://wx.qlogo.cn/mmhead/AuCj2elJsicnBI8uyYcdCoBAia8ye4g2TFhCpdf0ThAFw/0" alt="">
                  <div>
                    {{row.name}}
                  </div>
                </div>
          </el-table-column>
          <el-table-column
            prop="phone"
            label="手机号"
            align="center"
            fixed="left"
            width="180px">
          </el-table-column>
          <el-table-column
            prop="access_time"
            label="访问时间"
            align="center"
            width="200px">
          </el-table-column>
          <el-table-column
            prop="login_out_time"
            label="离开时间"
            align="center"
            width="200px">
          </el-table-column>
          <el-table-column
            prop="ctime"
            label="添加时间"
            align="center"
            width="200px">
          </el-table-column>
          <el-table-column
            prop="browse_time"
            label="停留时间"
            align="center"
            v-slot="{ row }">
            {{row.browse_time + "秒"}}
          </el-table-column>
          <el-table-column
            prop="map_plugin"
            label="地图插件名称"
            align="center"
            width="260px">
          </el-table-column>
          <el-table-column
            prop="share_name"
            label="分享者"
            align="center">
          </el-table-column>
          <el-table-column
            prop="inner_user_id"
            label="内部客户分享" 
            align="center"
            width="150px"
            v-slot="{ row }">
            {{row.inner_user_id ? '是' : '否'}}
          </el-table-column>
          <el-table-column
            label="详情"
            fixed="right"
            v-slot="{ row }">
            <el-link @click="tableDetailMap(row)">详情</el-link>
          </el-table-column>
        </el-table>
        <div style="text-align: end; margin-top: 24px">
        <el-pagination background layout="total,prev, pager, next" :total="params.total"
          :page-size="params.per_page" :current-page="params.page" @current-change="recodePageChange">
        </el-pagination>
      </div>
    </el-dialog>
</template>
<script>
export default {
    props:{
        // tableData: {
        //     type: Array,
        //     default: () => []
        // },
    },
    data() {
        return {
            show: false,        //dialog是否显示
            params: {
                package_id: '',
                name: "",//客户名称
                phone:"",//客户电话
                times:"",//时间
                // admin_id:"",//暂时不用
                total:0,
                page: 1,
                per_page: 10,
            },
            tabledata:[],
            is_table_loading:false,
        }
    },
    mounted(){
    },
    methods:{
        //打开弹窗
        open(id){
            this.params.name = ""
            this.params.phone = ""
            this.params.times = ""
            this.params.package_id = id
            this.getcustomerrecords()
            this.show = true;
            return this
        },
        //获取获客记录
        getcustomerrecords(){
            this.is_table_loading = true; // 加载loading
            let paramscopy = JSON.parse(JSON.stringify(this.params)); // 拷贝
            if(paramscopy.times){
                // 处理每个日期字符串并格式化
                let formattedDates = paramscopy.times.map(dateStr => {
                // 创建 Date 对象
                let date = new Date(dateStr);
                // 格式化为 'YYYY-MM-DD HH:MM:SS'
                return date.getFullYear() + '-' + 
                        String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(date.getDate()).padStart(2, '0') + ' ' + 
                        String(date.getHours()).padStart(2, '0') + ':' + 
                        String(date.getMinutes()).padStart(2, '0') + ':' + 
                        String(date.getSeconds()).padStart(2, '0');
                });
                paramscopy.times = formattedDates.join(",")
            }
            // console.log(paramscopy);
            this.$http.datapackagecustomer(paramscopy).then(res=>{
            if(res.status == 200){
                this.is_table_loading = false; // 关闭loading
                this.tabledata = res.data.data
                this.params.total = res.data.total;
            //   console.log(res.data,"duanlainjei ");
            }
          })
        },
        // 改变获客记录当前页码
        recodePageChange(current_page) {
          this.params.page = current_page;
          this.getcustomerrecords();
        },
        //详情
        tableDetailMap(row) {
          this.$goPath("/crm_customer_detail?id=" + row.client_id + "&type=my")
        },
        //查询
        onSubmit(){
            // console.log(this.params,"=====");
            this.getcustomerrecords()
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
    border-radius: 8px;
}
.queryarea{
    margin-bottom: 20px;
}
.headdata{
  display: flex;
  align-items: center;
}
.head-imgurl{
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
</style>