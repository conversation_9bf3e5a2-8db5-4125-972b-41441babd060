<template>
  <el-button v-if="isShow" id="btn" type="primary" @click="prev"
    >返回</el-button
  >
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
    };
  },
  methods: {
    prev() {
      this.$router.back();
    },
  },
};
// import goBack from "@/components/components/goBack";
</script>

<style scoped lang="scss">
.el-button {
  position: fixed;
  z-index: 10000;
}
</style>
