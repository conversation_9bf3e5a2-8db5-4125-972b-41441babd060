<template>
    <div class="Newdetails">
        <div class="newhouse">
            <div class="headcontent">
                <div class="b-t-item" :class="{ isactive: show == true }" @click="new_house">新房</div>
                <div class="b-t-item" :class="{ isactive: show == false }" @click="Reporting_records">报备记录</div>
            </div>
            <div class="headcontent" v-if="newhouse">
                <div class="headsearch">
                    <div class="search">
                        <el-input placeholder="请输入内容" v-model="input3" class="input-with-select">
                            <el-button slot="append" icon="el-icon-search"></el-button>
                        </el-input>
                    </div>
                    <div class="search">
                        <el-button type="primary" size="medium">报备客户</el-button>
                    </div>
                </div>
            </div>
        </div>
    <div v-if="newhouse">
        <div class="Refresh">
            <div class="Refreshcontent">
                <div class="Refreshleft">
                    <div>
                        <el-select class="selected-label" v-model="value" placeholder="区域" :style="{
                            minWidth: '40px',
                            width: '80px',
                        }">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select class="selected-label" v-model="value" placeholder="类型" :style="{
                            minWidth: '40px',
                            width: '80px',
                        }">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="Refreshcontent">
                <div class="Refreshright">
                    <el-button type="primary" size="medium">刷新</el-button>
                </div>
            </div>
        </div>
        <div class="Property-Details" v-for="(item, index) in project_list" :key="index">
            <div class="Propertyleft">
                <div class="Detailsleft">
                    <div class="leftimg">
                        <img :src="item.build_image" alt="">
                    </div>
                </div>
                <div class="Detailscenter">
                    <div class="Building_name">{{ item.build_name }}</div>
                    <div>
                        <span>类型:</span>
                        <span>区域:</span>
                    </div>
                    <div>
                        <span>人脸识别:</span>
                        <span>回访客户:</span>
                        <span>报备保护:</span>
                        <span>带看保护:</span>
                    </div>
                    <div>规则内容:</div>
                </div>
            </div>
            <div class="Propertyright">
                <div class="Detailsright">{{item.build_avg_price}}<span>元/㎡</span>
                <div><el-link type="primary" :underline="false" @click="client_base">报备客户</el-link>
                    <el-link type="primary"  :underline="false" style="margin-left: 20px;">查看详情</el-link>
                </div>
            </div>
            </div>
        </div>
    </div>
    <div v-if="Reporting">
        <div class="headrecords">
            <div class="bottom-border div row">
                <span class="text">状态：</span>
                <span
              v-for="(item, index) in label_list"
              :key="index"
               class="label_item"
              :class="is_all==item.id ? 'label_actions' : ''"
              @click="getAllLabelList(item.id)"
            >
            {{item.name}}
            </span>
            </div>
        </div>
        <div class="listrecords">
            <div style="margin-top: 15px;">
                  <el-input placeholder="请输入手机号或楼盘名称搜索内容"
                  style="margin-left: 15px;width: 400px;"
                   v-model="input3" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search"></el-button>
                  </el-input>
            </div>
            <div class="listdata">
                <el-table
                  :data="tableData"
                  border
                  stripe
                  style="width: 100%"
                  :header-cell-style="{background:'#EBF0F7',color:'#606266'}">
                  <el-table-column
                    prop="date"
                    label="日期"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="name"
                    label="姓名"
                    width="180">
                  </el-table-column>
                  <el-table-column
                    prop="address"
                    label="地址">
                  </el-table-column>
                </el-table>
            </div>
            <div class="paging">
                <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="1000">
                </el-pagination>
            </div>
        </div>
    </div>
        <el-dialog
      width="500px"
      top="7vh"
      :visible.sync="is_push_customer"
      title="报备客户"
      :before-close="cancel"
      :close-on-click-modal="false">
        <div style="width: 370px;margin: 0 auto;">
          <el-form ref="form" :model="Singleproject" label-position="right">
              <el-form-item label="客户名称" label-width="68px">
                  <el-input v-model="Singleproject.name" placeholder="请填写"></el-input>
              </el-form-item>
              <el-form-item label="客户性别" label-width="68px">
                <el-radio v-model="Singleproject.gender" label="1">男</el-radio>
                <el-radio v-model="Singleproject.gender" label="2">女</el-radio>
              </el-form-item>
              <el-form-item label="是否陪同" label-width="68px">
                <el-radio v-model="Singleproject.accompany" label="1">是</el-radio>
                <el-radio v-model="Singleproject.accompany" label="2">否</el-radio>
              </el-form-item>
              <el-form-item label="手机号码" label-width="68px">
                  <el-input v-model="Singleproject.phone_number" placeholder="请填写"></el-input>
              </el-form-item>
              <el-form-item label="预计到场时间" style="margin-left: -27px;">
                <div class="block">
                    <el-date-picker
                      v-model="Singleproject.timevalue"
                      type="datetime"
                      placeholder="选择日期时间"
                      style="width: 300px;">
                    </el-date-picker>
                </div>
              </el-form-item>
              <el-form-item label="客户意向" label-width="68px">
                <el-tag type="info">住宅</el-tag>
              </el-form-item>
              <el-form-item label="来访方式" label-width="68px">
                <el-radio v-model="Singleproject.gender" label="1">自访</el-radio>
                <el-radio v-model="Singleproject.gender" label="2">带访</el-radio>
              </el-form-item>
              <el-form-item label="来访人数" label-width="68px">
                  <el-input v-model="Singleproject.phone_number" placeholder="请填写"></el-input>
              </el-form-item>
              <el-form-item label="描述" label-width="68px">
                  <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请填写其他要求说明"
                  v-model="Singleproject.phone_number">
                </el-input>
              </el-form-item>
          </el-form>
        </div>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            type="primary"
            @click="onClickForm"
            >确 定</el-button
          >
        </div>
    </el-dialog>
    <el-dialog
      width="1257px"
      top="7vh"
      :visible.sync="is_push_customer1"
      title="报备客户"
      :before-close="cancel"
      :close-on-click-modal="false">
      <myReporting>

      </myReporting>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            type="primary"
            @click="onClickForm"
            >确 定</el-button
          >
        </div>
    </el-dialog>
    </div>
</template>

<script>
import myReporting from "./report_client.vue"
export default {
    name:"my_report",
    components:{
        myReporting
    },
    data() {
        return {
            textarea:"",
            show: true,
            input3: "",
            options: [
            ],
            value: '',
            Singleproject:{
                form:"",
                //性别
                gender:'1',
                //陪同
                accompany:'1',
                //电话
                phone_number:"",
                timevalue:"",
            },
            label_list:[
                {id:1,name:"全部"},
                {id:2,name:"待审核"},
                {id:3,name:"报备有效"},
                {id:4,name:"已到访"},
                {id:5,name:"已认筹"},
                {id:6,name:"已认购"},
                {id:7,name:"已成交"},
                {id:8,name:"已无效"},
            ],
            is_button_loading: false,
            is_push_customer:false,
            is_push_customer1:false,
            project_list: [],
            params: {
                page: 1,
                customer_phone: "",
                per_page: 10,
                total: 0,
                row: 0,
                reported_status: "",
                reception_status: "",
                project_id: "",
                updated_date_type: "day",
                company_id: "",
                build_id: "",
                status: "all",
            },
            report_list: {},
            time_value: "",
            newhouse:true,
            Reporting:false,
            is_all:"1",
            tableData: [{
              date: '2016-05-02',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1518 弄'
            }, {
              date: '2016-05-04',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1517 弄'
            }, {
              date: '2016-05-01',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1519 弄'
            }, {
              date: '2016-05-03',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1516 弄'
            }]
        };
    },
    methods: {
        //新房
        new_house() {
            this.show = true
            this.newhouse = true
            this.Reporting = false
        },
        //报备记录
        Reporting_records() {
            this.show = false
            this.newhouse = false
            this.Reporting = true
        },
        getAllLabelList(id){
            this.is_all = id;
            console.log(id);
        },
        getProject() {
            this.$http.getBaobeiProjectList().then((res) => {
                if (res.status === 200) {
                    console.log(res.data.data);
                    this.project_list = res.data.data;
                    console.log(this.project_list);
                }
            });
        },
        //点击报备客户
        client_base(){
           if(this.project_list.length==1){
            this.is_push_customer = true
           }else{
            console.log(1111);
            this.is_push_customer1 = true
           }

        },
          //取消
        cancel() {
            this.is_push_customer = false
            this.is_push_customer1 = false
        },
        onClickForm() {

        },
        getDataList() {
            if (
                this.params.reception_status === "" ||
                this.params.reported_status === ""
            ) {
                delete this.params.reported_status;
                delete this.params.reception_status;
            }
            if (!this.params.company_id) {
                delete this.params.company_id;
            }
            if (!this.params.build_id) {
                delete this.params.build_id;
            }
            if (!this.params.updated_date_type) {
                delete this.params.updated_date_type;
            }
            if (this.params.status == "all") {
                delete this.params.status;
            }
            this.$http.my_report({ params: this.params }).then((res) => {
                console.log(res);
            })
        },

    },
    mounted() {
        this.getProject()
        this.getDataList()
        this.params.project_id = this.$route.query.project_id
    }

};
</script>

<style lang="scss" scoped>
.Newdetails {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 0 24px 24px;
    overflow: hidden;

    .newhouse {
        width: 100%;
        height: 68px;
        margin-top: 20px;
        background: #FFFFFF;
        border: 1px solid #ffff;
        display: flex;

        .headcontent {
            width: 50%;
            display: flex;

            .b-t-item {
                margin-left: 24px;
                color: #8a929f;
                position: relative;
                margin-top: 20px;
                cursor: pointer;

                &.isactive {
                    color: #00a3ff;

                    &::after {
                        position: absolute;
                        content: "";
                        left: 50%;
                        transform: translateX(-50%);
                        height: 3px;
                        background: #2d84fb;
                        width: 100%;
                        display: block;
                        margin-top: 4px;
                    }
                }
            }

            .el-input {
                width: 311px;
            }

            .headsearch {
                width: 100%;
                display: flex;
                justify-content: flex-end;
            }

            .search {
                margin-top: 15px;
                margin-right: 24px;
            }
        }

    }

    .Refresh {
        width: 100%;
        height: 94px;
        background-color: #F8F8F8;
        margin-top: 20px;
        display: flex;

        .Refreshcontent {
            width: 50%;

            .Refreshleft {
                display: flex;
                margin-top: 30px;

                .selected-label {
                    border-radius: 4px;

                    ::v-deep .el-input {
                        width: auto;

                        .el-input__inner {
                            // max-width: 75px;
                            // width: 75px;
                            // border: none;
                            // color: #409EFF;
                            border: none;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            background: none;
                            color: #2d84fb;
                            //
                            // background: #E8F1FF;
                            line-height: 25px;
                            height: 25px;
                            border-radius: 4px;
                            //
                        }

                        .el-input__suffix {
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                        }

                        ::-webkit-input-placeholder {
                            /* WebKit browsers */
                            color: #8a929f;
                        }
                    }
                }

            }

            .Refreshright {
                width: 97%;
                display: flex;
                justify-content: flex-end;
                margin-right: 20px;
                margin-top: 20px;
            }
        }
    }

    .Property-Details {
        width: 100%;
        height: 200px;
        background-color: #FFFFFF;
        display: flex;

        .Propertyleft {
            width: 50%;
            height: 177px;
            margin-top: 23px;
            // background-color: #2d84fb;
            display: flex;

            .Detailsleft {
                width: 25%;
                height: 177px;

                // background: linear-gradient(180deg, #FF6A3B 0%, #F52A00 100%);
                .leftimg {
                    width: 150px;
                    height: 150px;
                    margin: 0 auto;

                    // background-color: palegoldenrod;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .Detailscenter {
                // width: 300px;
                width: 70%;
                height: 177px;

                // background: linear-gradient(180deg, #53c411 0%, #fee212 100%);
                .Building_name {
                    font-weight: 700;
                    font-size: 20px;
                }
            }
        }

        .Propertyright {
            width: 50%;
            height: 177px;
            margin-top: 23px;
            // background-color: palegoldenrod;
            display: flex;
            justify-content: flex-end;

            .Detailsright {
                width: 30%;
                height: 177px;
                text-align: center;
                color: #FF2D2D;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 32px;
                line-height: normal;
                letter-spacing: 0px;
                // text-align: left;

                // background-color: paleturquoise;
                span{
                    color: #FF2D2D;
                    font-family: PingFang SC;
                    font-weight: regular;
                    font-size: 20px;
                    line-height: normal;
                    letter-spacing: 0px;
                    // text-align: left;

                }
            }
        }

    }
    .headrecords{
        width: 100%;
        height: 68px;
        background: #ffff;
        border: 1px solid #ffff;
        border-top-color: #f1f1f1;
        .bottom-border {
            margin-top: 23px;
            .text{
                margin-left: 15px;
                font-size: 14px;
                color: #8A929F;
                cursor: pointer;
            }
        .label_item {
            margin-left: 20px;
            font-size: 14px;
            color: #8A929F;
            cursor: pointer;
        }
        .label_actions {
             border-radius: 4px;
             background: #e8f1ff;
             color: #2d84fb;
        }
        }
    }
    .listrecords{
        width: 100%;
        height: 700px;
        border: 1px solid #ffff;
        border-top-color: #f1f1f1;
        background-color: #ffff;
        .listdata{
            width: 98%;
            margin: 20px auto;
        }
        .paging{
            text-align: right;
            margin-right: 8px;
        }
        
    }

}</style>
