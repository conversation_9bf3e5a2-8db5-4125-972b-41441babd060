<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <topTips from="my_welcome_words" @add="toAdd" :showBtn="true"></topTips>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">类型：</span>
            <div>
              <el-select
                v-model="params.type"
                placeholder="请选择"
                @change="changeType"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">创建时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 370px"
              size="small"
              v-model="create_time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="timeChange"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="欢迎语" prop="content">
              <template slot-scope="scope">
                <div class="name picurl flex-row">
                  {{
                    scope.row.welcome.text
                      ? scope.row.welcome.text.desc
                      : "暂未设置"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="附件类型" prop="username">
              <template slot-scope="scope">
                <template
                  v-if="scope.row.welcomeArr && scope.row.welcomeArr.length"
                >
                  <el-tag
                    v-for="(v, key, index) in scope.row.welcomeArr"
                    :key="index"
                    style="margin-right: 10px; margin-bottom: 5px"
                  >
                    {{ v.name }}
                  </el-tag>
                </template>
                <template v-else>暂未设置</template>
              </template>
            </el-table-column>
            <el-table-column
              label="修改时间"
              width="200"
              prop="updated_at"
            ></el-table-column>
            <el-table-column label="创建时间" width="200" prop="created_at">
            </el-table-column>
            <el-table-column label="操作" width="100" v-slot="{ row }">
              <el-link
                style="margin-right: 10px"
                type="primary"
                @click="toEdit(row)"
                >编辑</el-link
              >
              <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteWelcomeWords(row)"
              >
                <el-link slot="reference" type="danger">删除</el-link>
              </el-popconfirm>
              <!-- <el-link type="danger" style="margin-left: 20px">删除</el-link> -->
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>

    <el-dialog
      :visible.sync="show_edit_welcome_dia"
      width="660px"
      title="编辑欢迎语"
    >
      <editWelcome
        v-if="show_edit_welcome_dia"
        :form="form_params"
        :navList="memberList"
        @success="editOk"
        @cancel="cancelEdit"
      ></editWelcome>
    </el-dialog>

    <el-dialog
      :visible.sync="show_add_welcome_dia"
      width="660px"
      title="添加欢迎语"
    >
      <addWelcome
        v-if="show_add_welcome_dia"
        :navList="memberList"
        @success="addOk"
        @cancel="cancelAdd"
      ></addWelcome>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "@/components/navMain/crm/components/my_label.vue";
import addWelcome from "../components/my_welcome_words/addWelcome.vue";
import editWelcome from "../components/my_welcome_words/editWelcome.vue";
import topTips from "@/components/navMain/crm/components/top_tips.vue";
export default {
  name: "welcome_words",
  components: {
    myLabel,
    addWelcome,
    editWelcome,
    topTips,
  },
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        wx_work_user_id: "",
        create_time: "",
        type: "1",
      },
      create_time: "",
      show_edit_welcome_dia: false,
      show_add_welcome_dia: false,
      time_list: [
        { id: 0, name: "全部", value: "" },
        { id: 1, name: "今天", value: 1 },
        { id: 2, name: "昨天", value: 2 },
        { id: 3, name: "本周", value: 3 },
        { id: 4, name: "上周", value: 4 },
        { id: 5, name: "本月", value: 5 },
        { id: 6, name: "上月", value: 6 },
      ],
      form_params: {},
      multipleSelection: [],
      show_member_list: false,
      memberList: [],
      selectedIds: [],
      username: "",
      typeList: [
        {
          label: "全部",
          value: "1",
        },
        {
          label: "图片",
          value: " 2",
        },
        {
          label: "链接",
          value: "3",
        },

        {
          label: "小程序",
          value: "4",
        },
        {
          label: "视频",
          value: " 5",
        },
        {
          label: "文件",
          value: "6",
        },
      ],
    };
  },
  created() {
    this.getDataList();
  },
  computed: {},
  filters: {},
  methods: {
    onClickTime(item) {
      this.create_time = "";
      this.params.create_time = item.value;
      this.params.page = 1;
      this.getDataList();
    },
    timeChange(e) {
      this.create_time = e;
      this.params.page = 1;
      this.getDataList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    changeType() {
      this.params.page = 1;
      this.getDataList();
    },
    async getDataList() {
      this.is_table_loading = true;
      let params = Object.assign({}, this.params);
      if (Array.isArray(this.create_time) && this.create_time.length > 0) {
        params.create_time = this.create_time.join(",");
      }
      if (!params.create_time) params.create_time = "";
      let res = await this.$http
        .getCrmMyWelcomeWordsList({ params })
        .catch((err) => {
          console.log(err);
          this.is_table_loading = false;
        });
      this.is_table_loading = false;
      if (res.status == 200) {
        this.tableData = res.data.data.map((item) => {

          let welcomeArr = [];
          for (const key in item.welcome) {
            let obj = item.welcome[key];
            if (key == "image") {
              obj.name = "图片";
            } else if (key == "link") {
              obj.name = "链接";
            } else if (key == "miniprogram") {
              obj.name = "小程序";
            } else if (key == "video") {
              obj.name = "视频";
            } else if (key == "file") {
              obj.name = "文件";
            }
            // obj.name = key
            if (key !== "text") {
              welcomeArr.push(obj);
            }
          }
          item.welcomeArr = welcomeArr;

          return item;
        });
      }
    },
    toAdd() {
      this.show_add_welcome_dia = true;
    },
    toEdit(row) {
      this.form_params = row;
      this.show_edit_welcome_dia = true;
    },
    deleteWelcomeWords(row) {
      this.$http.delCrmMyWelcomeWord(row.id, {}).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message || "删除成功");
          this.getDataList();
        } else {
          this.$message.error(res.message || "删除失败");
        }
      });
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    cancelEdit() {
      this.show_edit_welcome_dia = false;
    },
    editOk() {
      this.show_edit_welcome_dia = false;
      this.params.page = 1;
      this.getDataList();
    },

    addOk() {
      this.show_add_welcome_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    cancelAdd() {
      this.show_add_welcome_dia = false;
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      min-width: 70px;
      color: #8a929f;
    }
  }
}
.name {
  &.picurl {
    align-items: flex-start;
    img {
      width: 20px;
      height: 20px;
      object-fit: cover;
      margin-right: 5px;
    }
  }
}
</style>
