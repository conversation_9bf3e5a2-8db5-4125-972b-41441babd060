<template>
  <div>
    <el-row>
      <el-col :span="24">
        <div class="weituo_a">
          <div class="weituo_key">
            <el-row>
              <el-col>
                <div class="title flex-row align-center">
                  <el-button
                    type="primary"
                    style="margin-right: 10px; margin-bottom: 10px"
                    @click="add"
                    >添加分组</el-button
                  >
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <div class="title">
                  <el-table
                    v-loading="is_table_loading"
                    :data="teamData"
                    border
                    :header-cell-style="{ background: '#EBF0F7' }"
                    highlight-current-row
                    :row-style="$TableRowStyle"
                  >
                    <el-table-column
                      prop="title"
                      label="团队名称"
                    ></el-table-column>

                    <el-table-column label="团队成员" v-slot="{ row }">
                      <el-tooltip
                        placement="top-start"
                        width="200"
                        trigger="hover"
                        effect="light"
                      >
                        <div slot="content" style="line-height: 1.5">
                          <div class="flex-row flex-wrap">
                            <span
                              v-for="(item, index) in row.member_list"
                              :key="index"
                              style="margin-right: 5px; margin-bottom: 5px"
                            >
                              {{ item.name || item.nickname }}
                            </span>
                          </div>
                        </div>
                        <!--    <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
                        <el-tag>查看详情 </el-tag>
                      </el-tooltip>
                      <!-- <div>
                        <el-popup> </el-popup>
                      </div> -->
                    </el-table-column>
                    <el-table-column label="操作" v-slot="{ row }" width="200">
                      <el-popconfirm
                        title="确定删除吗？"
                        style="margin: 0 10px"
                        @onConfirm="del(row)"
                      >
                        <el-link
                          slot="reference"
                          type="danger"
                          icon="el-icon-delete"
                          >删除</el-link
                        >
                      </el-popconfirm>
                      <el-link style="margin-right: 10px" @click="edit(row)"
                        >编辑</el-link
                      >
                    </el-table-column>
                  </el-table>

                  <el-pagination
                    style="text-align: end; margin-top: 24px"
                    background
                    layout="prev, pager, next"
                    :total="total"
                    :page-size="params.per_page"
                    :current-page="params.page"
                    @current-change="onPageChange"
                  >
                  </el-pagination>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog width="500px" :visible.sync="is_showDia" :title="settingTitle">
      <div>
        <el-form label-width="120px">
          <el-form-item label="团队名称">
            <el-input
              v-model="setting_form.title"
              style="width: 200px"
            ></el-input>
          </el-form-item>

          <el-form-item label="成员">
            <el-select
              v-model="setting_form.member"
              :multiple="true"
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option
                v-for="item in memberList"
                :key="item.id"
                :label="item.name"
                :value="item.id + ''"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button type="primary" @click="confirmSetting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      teamData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,

      },
      total: 0,
      is_showDia: false,
      settingTitle: '',
      department_list: [],
      depart_type: 0,
      is_edit: false,
      setting_form: {
        title: '',
        member: ''
      },
      memberList: []
    }
  },
  created() {
    this.getTeamList()
  },
  methods: {
    add() {
      this.settingTitle = "添加团队"
      this.is_edit = false
      this.clearData()
      if (!this.memberList.length) {
        this.getTeamMemberList()
      }
      this.is_showDia = true
    },
    edit(row) {
      this.is_edit = true
      this.settingTitle = "编辑团队"
      if (!this.memberList.length) {
        this.getTeamMemberList()
      }
      let memberlist = row.member ? row.member.split(",") : []
      this.setting_form = {
        id: row.id,
        title: row.title,
        member: memberlist
      }
      console.log(memberlist);
      this.is_showDia = true
    },
    getTeamMemberList() {
      this.$http.getTeamMemberList().then(res => {
        console.log(res);
        if (res.status == 200) {
          res.data.map(item => {
            item.id = item.id + ''
            return item
          })
          this.memberList = res.data
        }
      })
    },
    confirmSetting() {
      if (this.is_edit) {
        this.editTeam()
      } else {
        this.addTeam()
      }
    },
    editTeam() {
      let params = Object.assign({}, this.setting_form)
      if (params.member && params.member.length) {
        params.member = params.member.join(',')
      }
      this.$http.editTeam(params).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || '编辑成功')
          this.is_showDia = false
          this.getTeamList()
        }
      })
    },
    addTeam() {
      let params = Object.assign({}, this.setting_form)
      if (params.member && params.member.length) {
        params.member = params.member.join(',')
      }
      this.$http.addTeam(params).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || '添加成功')
          this.is_showDia = false
          this.getTeamList()
        }
      })
    },
    clearData() {
      this.setting_form = {
        title: '',
        member: ''
      }
    },

    del(row) {
      this.$http.deleteTeam({ ids: row.id + '' }).then(res => {
        if (res.status == 200) {
          this.$message.success("删除成功")
          this.getTeamList()
        }
      })
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getTeamList()
    },

    getTeamList() {
      this.is_table_loading = true
      this.$http.getTeamList(this.params).then(res => {

        if (res.status == 200) {
          this.teamData = res.data.data
          this.total = res.data.total
        }
        this.is_table_loading = false
      }).catch(() => {
        this.is_table_loading = false
      })
    },


  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}
.padd10 {
  padding: 10px 0 40px;
}
.weituo_a {
  padding-top: 20px;
  padding-left: 24px;

  .weituo_key {
    padding-bottom: 20px;
    border-bottom: 1px solid #f3f3f3;
    .title {
      padding-top: 20px;
    }
  }
}
</style>