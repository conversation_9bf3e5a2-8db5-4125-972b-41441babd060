<template>
    <el-dialog :visible.sync="show" title="话术排序" width="500px">   
        <div class="tab-list" v-if="show" v-loading="loading">
            <div class="tip">拖拽话术库进行排序</div>
            <draggable v-model="list" animation="300" style="width: 100%;">
                <transition-group>
                    <div v-for="row in list" :key="row.id" class="tab-item">
                        <span class="hand"><i class="el-icon-rank"></i></span>
                        <template>
                            <span class="name">{{row.package_name}}</span>
                            <!-- <span class="close" @click="remove(row)"><i class="el-icon-close"></i></span> -->
                        </template>
                    </div>
                </transition-group>
            </draggable>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="submiting">保 存</el-button>
        </span>
    </el-dialog>
</template>

<script>
import draggable from 'vuedraggable';
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
    },
    components: {
        draggable
    },
    data(){
        return {
            show: false,
            loading: false,
            submiting: false,
            list: [],
            originalList: []
        }
    },
    watch: {
        visible(val){
            this.show = val;
            if(val){
                this.getList();
            }
        },
        show(val){
            val != this.visible && this.$emit("update:visible", val)
        }
    },
    methods: {
        async getList(){
            this.loading = true;
            const res = await this.$http.allSpeechLibraryManagerList().catch(e => {});
            this.loading = false;
            if(res.status == 200){
                this.list = (res.data?.list || []).filter(e=>e.is_platform != 1);
                this.originalList = [...this.list];
            }
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            //this.submiting = true;
            try{
                //获取排序的tabs
                let sortData = [];
                for(let i = this.originalList.length-1; i >= 0; i--){
                    if(this.originalList[i] !== this.list[i]){
                        let srotValue = i == this.originalList.length-1 ? 0 : this.list[i].sort;
                        sortData = this.list.slice(0, i+1).reverse().map(e => {
                            return e.sort == ++srotValue  ? null : {
                                id: e.id,
                                sort: srotValue
                            }
                        }).filter(e=>e);
                        break;
                    }
                }


                const res = await this.$http.setSpeechLibraryBatchSort({sort_data: JSON.stringify(sortData)});    
                if(res.status == 200){
                    this.$message.success(res?.data?.msg || '设置成功');
                    this.show = false;
                    this.$emit('change');
                }
            }catch(e){
                console.error(e);
            }
            this.submiting = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.tab-list{
    display: flex;
    flex-wrap: wrap;
    .tip{
        height: 26px;
    }
    .tab-item{
        display: flex;
        align-items: center;
        height: 42px;
        margin: 10px 0 12px;
        border: 1px solid #e8e8e8;
        border-radius: 2px;
        font-size: 15px;
        color: #606266;
        cursor: move;
        padding-right: 12px;
        user-select: none;
        .hand{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 40px;
        }
        .name{
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .close{
            cursor: pointer;
            color: #999;
            &:hover{
                color: #3c3c3c;
            }
        }
    }
}
</style>