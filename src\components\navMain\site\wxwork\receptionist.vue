<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-header class="div row" style="justify-content: space-between">
          <!-- 标题 -->
          <div class="div row">
            <div class="title">微信客服_接待人员</div>
            <div class="title_number">
              <div>
                当前页面共（<i>{{ tableData.length }}</i
                >）条数据
              </div>
            </div>
          </div>
        </el-header>
        <div class="content-box-crm div row" style="margin-bottom: 24px">
          <el-button
            size="mini"
            class="el-icon-plus"
            type="primary"
            @click="openAddServicer"
            >添加接待人员</el-button
          >
        </div>
        <div class="receptionist">
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            highlight-current-row
          >
            <el-table-column
              prop="id"
              label="ID"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="open_kfid"
              label="客服ID"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="servicer.name"
              label="客服名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="wx_work_userid"
              label="成员ID"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="departmentid"
              label="部门ID"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="name"
              label="名称"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="接待状态" align="center">
              <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.status == 0"
                  >接待中</el-tag
                >
                <el-tag type="success" v-else>停止接待</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="updated_at"
              label="更新时间"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="created_at"
              label="创建时间"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-link
                  type="danger"
                  style="margin-left: 20px"
                  @click="onDelete(scope.row)"
                  >删除</el-link
                >
              </template>
            </el-table-column>
          </el-table>
          <myPagination
            :total="params.total"
            :currentPage="params.page"
            :pagesize="params.per_page"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
          ></myPagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog title="添加接待人员" :visible.sync="dialogCreatet">
      <el-form :model="servicerEdit" label-width="120px">
        <el-form-item label="客服ID：">
          <el-input
            maxlength="20"
            v-model="servicerEdit.open_kfid"
            placeholder="请填写客服ID（必填）"
          ></el-input>
        </el-form-item>
        <el-form-item label="接待人员ID：">
          <el-select v-model="servicerEdit.userid_list" placeholder="请选择">
            <el-option
              v-for="(v, i) in userOption"
              :key="i"
              :label="v.name"
              :value="v.wx_work_userid"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接待部门ID：">
          <el-select
            v-model="servicerEdit.department_id_list"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in departmentOption"
              :key="index"
              :label="item.name"
              :value="item.departmentid"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="addServicer"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
export default {
  name: "receptionist",
  components: {
    myPagination,
  },
  data() {
    return {
      servicerEdit: {
        type: "",
        open_kfid: "",
        userid_list: [],
        department_id_list: [],
      },
      tableData: [],
      userOption: [],
      departmentOption: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      is_table_loading: true,
      inputVal: "",
      dialogCreatet: false,
      userValue: "",
      departmentValue: "",
    };
  },
  computed: {},
  watch: {},
  methods: {
    getServicerList() {
      this.is_table_loading = true;
      this.$http.getServicerList({ params: this.params }).then((res) => {
        console.log(res, "接待人员");
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    getUserOption() {
      this.$http.userOption().then((res) => {
        console.log(res, "成员下拉选");
        if (res.status === 200) {
          this.userOption = res.data;
        }
      });
    },
    getDepartmentOption() {
      this.$http.departmentOption().then((res) => {
        console.log(res, "部门下拉选");
        if (res.status === 200) {
          this.department_option = res.data;
        }
      });
    },
    openAddServicer() {
      this.dialogCreatet = true;
      this.servicerEdit.type = "add";
    },
    addServicer() {
      if (!this.servicerEdit.open_kfid) {
        this.$message.error("请输入客服ID");
        return;
      }
      let msg = this.$message.success("请稍后...");
      this.$http.servicerAddOrDel(this.servicerEdit).then((res) => {
        if (res.status === 200) {
          this.$message.success("添加成功");
          this.getServicerList();
          this.dialogCreate = false;
          this.servicerEdit.open_kfid = "";
          this.servicerEdit.userid_list = [];
          this.servicerEdit.department_id_list = [];
          msg.close();
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
      this.getServicerList();
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
    },
  },
  created() {},
  mounted() {
    this.getServicerList();
    this.getUserOption();
    this.getDepartmentOption();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
.el-button {
  border-radius: 3px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 300px;
}
</style>
