<template>
  <el-container>
    <new_tips_list :tipsList="tips_list"></new_tips_list>
    <el-header
      ><el-button @click="createRules" type="primary" icon="el-icon-plus"
        >添加</el-button
      ></el-header
    >
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <!-- 弹窗 -->
    <el-dialog
      :title="is_title"
      :visible.sync="dialogEdit"
      width="60%"
      @close="onClose"
    >
      <el-form
        label-position="left"
        label-width="100px"
        ref="ruleForm"
        :model="form_company_rules"
      >
        <el-form-item label="分佣公司" label-width="100px" prop="company_name">
          <el-select
            v-if="!this.rules_id"
            ref="corporation"
            v-model="form_company_rules.company_name"
            multiple
            filterable
            remote
            :reserve-keyword="false"
            placeholder="请输入销售公司名称"
            :remote-method="getCompanyId"
            :loading="company_loading"
            @change="onChange"
            @click.native="clickUserJumpIdState"
          >
            <el-option
              v-for="item in company_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-input
            v-else
            v-model="form_company_rules.company_name"
            :disabled="true">
          </el-input>
        </el-form-item>
        <el-form-item label="佣金分成" prop="brokerage_rule">
          <el-input
            placeholder="不填写默认为0即可"
            v-model="form_company_rules.brokerage_rule"
          ></el-input>
          <!-- <i style="color:#f78989">元/套</i> -->
        </el-form-item>
        <el-form-item label="分佣规则" prop="brokerage_description">
          <el-input
            type="textarea"
            v-model="form_company_rules.brokerage_description"
            placeholder="请输入分佣规则"
          ></el-input>
        </el-form-item>
        <el-form-item label="带看奖励" prop="shopping_guide_reward">
          <el-input
            type="textarea"
            v-model="form_company_rules.shopping_guide_reward"
            placeholder="请输入带看奖励"
          ></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button type="primary" @click="onSubmit" :loading="is_loading">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import new_tips_list from "@/components/components/new_tips_list";
export default {
  name: "company_rules_list",
  components: {
    myPagination,
    myTable,
    new_tips_list
  },
  data() {
    return {
      tableData: [],
      company_id: "",
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      form_company_rules: {
        company_id: "",
        project_id: "",
        brokerage_rule: "",
        brokerage_description: "",
        shopping_guide_reward: "",
      },
      dialogEdit: false,
      rules_id: "",
      company_list: [],
      company_loading: false,
      table_header: [
        { prop: "id", label: "规则ID", width: "100" },
        { prop: "project_name", label: "项目名称" },
        { prop: "company_name", label: "公司名称" },
        { prop: "created_at", label: "创建时间" },
        { prop: "updated_at", label: "修改时间" },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="success"
                  icon="el-icon-edit"
                  onClick={() => {
                    this.brokerRules(data.row);
                  }}
                >
                  编辑
                </el-button>
                <el-button
                  size="mini"
                  icon="el-icon-delete"
                  type="danger"
                  onClick={() => {
                    this.deleteBrokerRules(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      tips_list: ["此处公司规则主要用于内渠公司单独设置不同的佣金规则显示，优先级高于项目中设置的佣金规则。"],
      is_title: '添加',
      is_loading: false, // loading加载
    };
  },
  mounted() {
    // this.company_id = this.$route.query.company_id;
    this.form_company_rules.project_id = this.$route.query.project_id;
    this.getDatalist();
  },
  methods: {
    getCompanyId(query) {
      this.company_loading = true;
      this.$http.porjectCompanyList(query, 2, 1000).then((res) => {
        this.company_loading = false;
        this.company_list = res.data.data.map((item) => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      });
    },
    onChange(e) {
      console.log(e)
      this.form_company_rules.company_id = e;
    },
    onClose() {
      this.$refs.ruleForm.resetFields();
      this.rules_id = "";
      this.getCompanyId() // 获取空数据
      this.company_loading = false; // 关闭下拉框
    },
    queryBrokerRules() {
      this.$http.queryBrokerRules(this.rules_id).then((res) => {
        if (res.status === 200) {
          this.form_company_rules = res.data;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    getDatalist() {
      this.$http
        .getCompanyRules(
          this.form_company_rules.project_id,
          this.params.currentPage,
          this.params.pagesize
        )
        .then((res) => {
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    brokerRules(row) {
      console.log(row,"row");
      this.is_title = '编辑';
      this.form_company_rules = {
        company_id: row.company_id,
        project_id: row.project_id,
      };
      this.rules_id = row.id;
      if (this.rules_id) {
        this.queryBrokerRules();
      }
      this.dialogEdit = true;
    },
    // 删除公司规则
    deleteBrokerRules(row) {
      this.$confirm("此操作将删除该公司规则, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteBrokerRules(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDatalist();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    onSubmit() {
      if(!this.form_company_rules.company_id && !this.form_company_rules.company_id.length) {
        return this.$message.warning("请选择分佣公司");
      }
      // 佣金分成、分佣规则、带看奖励不能同时为空
      if(this.form_company_rules.brokerage_rule == '' && this.form_company_rules.brokerage_description == '' && this.form_company_rules.shopping_guide_reward == '') {
        return this.$message.warning("佣金分成、分佣规则、带看奖励不能同时为空")
      }
      for (var prop in this.form_company_rules) {
        if (this.form_company_rules[prop] === "") {
          delete this.form_company_rules[prop];
        }
      }
      console.log(this.form_company_rules,"this.form_company_rules");
      // 处理公司id，隔开
      this.form_company_rules.company_id =
       this.form_company_rules.company_id && Array.isArray(this.form_company_rules.company_id) ? this.form_company_rules.company_id.join(",") : this.form_company_rules.company_id;
      // 处理公司名称，隔开
      this.form_company_rules.company_name =
       this.form_company_rules.company_name && Array.isArray(this.form_company_rules.company_name) ? this.form_company_rules.company_name.join(",") : this.form_company_rules.company_name;
      // 如果是编辑
      if (this.rules_id) {
        this.is_loading = true; // 开启loading
        this.$http.updataBrokerRules(this.form_company_rules).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.is_loading = false; // 关闭loading
            this.getDatalist();
            this.dialogEdit = false;
            this.$refs.ruleForm.resetFields();
          } else {
            this.is_loading = false;
          }
        }).catch(() => {
          this.is_loading = false;
        })
      } else {
        this.is_loading = true; // 开启loading
        this.$http.createBrokerRules(this.form_company_rules).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.is_loading = false; // 关闭loading
            this.getDatalist();
            this.dialogEdit = false;
            this.$refs.ruleForm.resetFields();
          } else {
            this.is_loading = false;
          }
        });
      }
    },
    createRules() {
      this.is_title = '添加';
      this.dialogEdit = true;
      this.$nextTick(() => {
        this.$refs.corporation.focus();
      })
      //   this.form_company_rules.company_id = this.company_id;
    },
    clickUserJumpIdState() {
      this.company_loading = true; // 显示下拉框
      this.getCompanyId(''); // 加载数据
    }
  },
};
</script>

<style></style>
