
<template>
  <div class="outbound">
    <div class="tabs">
      <div class="check-box div row" id="pages_content">
        <template v-for="item in tabs_list">
          <div
            @click="onClickTabs(item)"
            :class="{ isactive: item.title === is_tabs_page }"
            :key="item.id"
            class="check-item"
          >
            {{ item.name }}
          </div>
        </template>
      </div>
    </div>
    <div
      class="bor-t"
      style="background: #fff; padding: 24px 0"
      :is="is_tabs_page"
      keep-alive
    ></div>
  </div>
</template>

<script>

import weixinSetting from "./weixinSetting"
import weixinPageSetting from './weixinPageSetting'
// import weixinSetting from "./weixinSetting"
export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    weixinSetting,
    // eslint-disable-next-line vue/no-unused-components
    weixinPageSetting
  },
  props: {
    query_type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabs_list: [
        // {
        //   id: 2,
        //   title: "douyinSetting",
        //   name: '抖音小程序管理'
        // },
        {
          id: 2,
          title: "weixinSetting",
          name: '授权管理'
        },
        // {
        //   id: 3,
        //   title: "weixinPageSetting",
        //   name: '页面设置'
        // },
      ],
      is_tabs_page: "",

    }
  },
  created() {
    // if (this.query_type) {
    //   this.is_tabs_page = this.query_type

    // }
    this.website_id = this.$route.query.website_id || localStorage.getItem("website_id")
    this.tabs_list = [
      {
        id: 2,
        title: "weixinSetting",
        name: '授权管理'
      },
      {
        id: 3,
        title: "weixinPageSetting",
        name: '页面设置'
      },
      {
        id:4,
        title:"configure_user_list",
        name:"用户列表"
      }
    ]
    this.getSetting()
  },
  computed: {

  },
  methods: {
    onClickTabs(item) {
      if(item.name=="用户列表"){
       return this.$goPath("configure_user_list?platform=2");
      }
      this.is_tabs_page = item.title
      switch (this.is_tabs_page) {
        // case 'weixinSetting':
        //   this.$http.queryXiaoApp().then((res) => {
        //     if (res.status === 200) {
        //       if (res.data.id === 0 && res.data.updated_at === "") {
        //         this.$message({
        //           message: "暂未授权小程序",
        //           type: "warning",
        //         });
        //       } else {
        //         this.configuration();
        //       }
        //     }
        //   });
        //   break;
        // case 'seat':
        //   this.getZuoxi()
        //   break;

        default:
          break;
      }
    },
    async getSetting() {
      console.log(this.tabs_list);
      // let weixin_result = await this.$http.queryXiaoApp()
      let douyin_result = await this.$http.getWebsite(this.website_id)
      // if (weixin_result.data.id == 0) {
      //   this.tabs_list = this.tabs_list.filter(item => item.id != 3)
      // }
      if (douyin_result.data.open_mini_build_program_wx == 0 && douyin_result.data.open_mini_erp_program_wx == 0) {
        this.tabs_list = this.tabs_list.filter(item => item.id != 2)
      }
      this.is_tabs_page = this.tabs_list.length ? this.tabs_list[0].title : ""
    }
  }
}
</script>

<style scoped lang="scss">
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
.mr10 {
  margin-right: 10px;
}
.w-300 {
  width: 300px;
}
.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  // margin-left: 20px;
  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;
    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}
// .tabs {
//   margin-left: 44px;
// }
.w_300 {
  width: 300px;
}
// .tab_content {
//   padding: 40px 44px;
// }
.btns {
  padding-left: 44px;
}
.table_oper {
  padding: 16px 0;
}
.balance {
  padding: 0px 44px;
  .sms-border {
    margin-top: 10px;
    padding: 24px 0;
    border-bottom: 1px solid #eee;
    margin-left: -24px;
    margin-right: -24px;
    .sms-item {
      padding: 12px 0;
      flex: 1;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-right: 1px solid #eee;
      cursor: pointer;
      .max-w-160 {
        max-width: 160px;
      }
      .w-160 {
        width: 160px;
      }
      .t {
        color: #0083ff;
        font-size: 32px;
      }
      .b {
        font-size: 14px;
        color: #768196;
      }
      .btn {
        padding: 9px 0;
        color: #fff;
        text-align: center;
        border-radius: 5px;
      }
      .b1 {
        margin-bottom: 14px;
        background: #17b63a;
      }
      .b2 {
        background: #fd7979;
      }
    }
  }
}
</style>