<template>
    <el-dropdown @visible-change="onVisibleChange" v-bind="$attrs" v-on="$listeners">
        <slot></slot>
        <el-dropdown-menu slot="dropdown" class="t-dropdown-body" :class="customClass">
            <div class="t-dropdown-filterbar" v-if="filterable">
                <el-input :placeholder="filterInputPlaceholder" prefix-icon="el-icon-search" v-model="searchValue" size="medium" @input="onFilterInput"></el-input>
            </div>
            <div class="t-dropdown-cont" v-loading="loading" :style="{height: height}">
                <el-dropdown-item v-for="(item, index) in list" :key="index" :command="item">
                    {{item[labelKey]}}
                </el-dropdown-item>
                <slot name="empty" v-if="isEmpty">
                    <div class="empty-text">无数据</div>
                </slot>
            </div>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script>
import Utils from '@/utils/utils.js';
export default {
    name: 'tDropdown',
    props: {
        datas: {type: Array, default: ()=>[]},  
        props: {type: Object, default: ()=>{}},
        filterable: {type: Boolean, default: false},
        filterInputPlaceholder: {type: String, default: '请输入搜索内容'},
        filterMethod: {type: Function, default: null},
        remote: {type: Boolean, default: false},
        remoteMethod: {type: Function, default: function(){}},
        loading: {type: Boolean, default: false},
        height: {type: String, default: 'auto'},
        customClass: {type: String, default: ''},
    },
    data(){
        return {
            list: [],
            searchValue: '',
            labelKey: '',
            isDataLoaded: false
        }
    },
    computed: {
        isEmpty(){
            return !this.loading && this.isDataLoaded && this.list.length == 0;
        }
    },
    watch: {
        datas: {
            handler(d){
                this.list = d;
            },
            immediate: true
        }
    },
    created(){
        this.labelKey =  this?.props?.label || 'label';
    },
    methods: {
        //初始化菜单数据
        initMenuData(){
            if(this.remote){
                if(!this.isDataLoaded){
                    this.isDataLoaded =  this.list.length > 0;
                    if(!this.isDataLoaded){
                        this.isDataLoaded = true;
                        this.remoteFilter('');
                    }
                }
            }else{
                this.isDataLoaded = true;
            }
        },
        //菜单显示隐藏事件
        onVisibleChange(bool){
            //首次加载数据
            bool && this.initMenuData();
            this.$emit('visible-change', bool);
        },
        //搜索框 Input 事件
        onFilterInput: Utils.debounce(function(){
            const searchValue = this.searchValue;
            //使用远程搜索
            if(this.remote){
                this.remoteMethod && this.remoteMethod(searchValue);
            }else{
                //使用自定义过滤方法
                if(this.filterMethod){
                    this.filterMethod(searchValue);
                }else{
                //使用默认过滤方法
                    const labelKey = this.labelKey;
                    this.list = searchValue==='' ? this.datas : this.datas.filter( e => e[labelKey].includes(searchValue));          
                }
            }
        })
    },
}
</script>

<style scoped lang="scss">
.t-dropdown-body{
    &.el-dropdown-menu{
        padding: 0;
    }
    .t-dropdown-filterbar{
        padding: 15px 20px;
    }
    .t-dropdown-cont{
        min-height: 100px;
        min-width: 200px;
        overflow: auto;
        .empty-text{
            text-align: center;
            padding: 10px;
            color: #909399;
        }
    }
}
</style>