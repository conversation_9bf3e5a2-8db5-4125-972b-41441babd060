<template>
    <div>
      <!-- <el-button type="primary" @click="onClickTaskList(1)" v-if="website_id!=109">所有任务列表</el-button> -->
      <el-button type="primary" plain @click="onClickTaskList(1)">导入任务列表</el-button>
      <el-button type="primary" plain @click="onClickTaskList(2)">导出任务列表</el-button> 
      <!-- <el-button type="primary" plain @click="onClickTaskList(3)">流转客日志</el-button>  -->
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        tableData: [],
        is_table_loading: false,
        params: {
          page: 1,
          per_page: 10,
          total: 0,
        },
        website_id:""
      };
    },
    mounted() {
    if (this.$route.query.website_id) {
      // console.log(this.$route.query);
      this.website_id = this.$route.query.website_id;
    }
    },
    methods: {
      onClickTaskList(status){
        let url = `/crm_customer_all_task_list`;
        if(status==1){
          this.$goPath(url); // 跳转列表
        }else if(status==2){
          url = "/crm_customer_export_list"
          this.$goPath(url); // 跳转列表
        }else if(status==3){
          url = "/crm_information_daily_record"
          this.$goPath(url); // 跳转流转客日志列表
        }

      }
    },
  };
  </script>
  
  <style></style>
  