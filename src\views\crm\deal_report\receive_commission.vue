<template>
    <div class="receive-comm">
        <template v-if="!preview">
       
        
            <div class="header" v-if="!preview">
                <el-button type="primary" @click="add()">添加</el-button>
            </div>
            <el-table v-loading="loading" :data="isShowQuickAdd ? aquickList : list" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
                <el-table-column label="操作时间" prop="created_at" v-if="!preview"></el-table-column>
                <el-table-column label="佣金类型">应收佣金</el-table-column>
                <!-- <el-table-column label="比例">
                    <template v-slot="{row}">
                        <span>{{row.commissionPct}}</span>
                    </template>
                </el-table-column> -->
                <el-table-column label="佣金金额" prop="commission">
                    <template v-slot="{row}">
                        <div v-if="isShowQuickAdd">
                            <el-input v-model="row.commission" placeholder="">
                                <i slot="suffix">元</i>
                            </el-input>
                        </div>
                        <div v-else>{{row.commission}}</div>
                    </template>

                </el-table-column>
                <el-table-column label="备注" v-slot="{ row }">
                    {{ row.descp }}
                </el-table-column>
                <el-table-column label="操作" fixed="right" v-slot="{ row }" v-if="!preview">
                    <template v-if="isShowQuickAdd">
                        <el-link style="margin-right: 20px;" type="primary" @click="quickAdd(row)" :disabled="quickAdding">确认</el-link>
                    </template>
                    <template v-else>
                        <el-link style="margin-right: 20px;" type="primary" @click="edit(row)">编辑</el-link>
                        <el-link type="danger" @click="del(row)">删除</el-link>
                    </template>

                </el-table-column>
            </el-table>
        </template>
        <template v-else>
            <el-table v-loading="loading" :data="previewList" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
                <el-table-column label="" prop="label"></el-table-column>
                <el-table-column label="点位" prop="proportion"></el-table-column>
                <el-table-column label="金额" prop="amount"></el-table-column>
            </el-table>
        </template>

        
        <div class="footer">
            <div></div>
            <div class="commission-text" v-if="receiveCommissionCount">
                应收佣金合计：<span class="commission-text-red">{{receiveCommissionTotal}}</span> 元
            </div>
        </div>

        <addReceiveCommission v-if="dialogs.add" ref="add"/>
    </div>
</template>

<script>
import addReceiveCommission from './add_receive_commission.vue'
export default {
    name: 'crmDealReceiveCommission',
    components: {
        addReceiveCommission
    },
    props: {
        reportData: {type: Object, default: ()=>{return {}}},
        preview: {type: Boolean, default: false}
    },
    inject: {
        checkSaveBaseInfo: { from: 'checkSaveBaseInfo', default: () => {return {}} },
        commissions: { from: 'commissions', default: () => {}}
    },
    data(){
        return {
            loading: false,
            quickAdding: false,
            aquickList: [],
            list: [],
            dialogs: {
                add: false
            }
        }
    },
    computed: {
        previewList(){
            try{
                const list = [];
                const deal = this.reportData?.deal || {};
                //正常佣金
                const normalCommission = (()=>{
                    let amount = deal.amount * deal.proportion / 100;
                    amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
                    return amount; 
                })()
                //渠道分佣
                const channelCommission = (()=>{
                    let amount =  (deal.channel_proportion_type == 2 ? -1: 1) * normalCommission * deal.channel_proportion / 100;
                    amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
                    return amount; 
                })()

                //代扣税金额
                const withholdTaxAmount= (()=>{
                    let amount = (normalCommission + channelCommission) * deal.channel_rate / 100;
                    amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
                    return amount; 
                })();

                //当前计算佣金
                const calcCommission = (()=>{
                    let amount = normalCommission + channelCommission - withholdTaxAmount;
                    amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
                    return amount
                })();

                list.push({label: '佣金总额', proportion: (deal?.proportion || 0) +'%', amount: normalCommission })
                if(channelCommission != 0){
                    list.push({label: '渠道分成', proportion: (channelCommission > 0 ? '+' : '-')+deal.channel_proportion+'%', amount: Math.abs(channelCommission).toFixed(2) })
                }

                if(withholdTaxAmount != 0){
                    list.push({label: '渠道税点', proportion: '-'+deal.channel_rate+'%', amount: withholdTaxAmount.toFixed(2) })
                }

                for(const item of this.list){
                    list.push({label: '应收佣金', proportion: item.commissionPct+'%', amount: item.commission })
                }
                return list;
            }catch(e){
                return [];
            }
        },
        //应收佣金数量
        receiveCommissionCount(){
            return this.reportData?.receive_commission?.count || 0;
        },
        //应收佣金合计
        receiveCommissionTotal(){
            return this.reportData?.receive_commission?.commission || 0;
        },
        //是否显示快捷录入
        isShowQuickAdd(){
            return !this.preview && this.receiveCommissionCount == 0 && this.reportData?.deal?.commission > 0
                 && this.list.length == 0;
        }
    },
    watch: {
        //是否显示快捷输入
        isShowQuickAdd: {
            handler(val){
                if(val){
                    console.log(this.commissions);
                    this.aquickList= [{
                        cate_name: '应收佣金',
                        commission: this.commissions?.calcCommission || '',
                        commissionPct: this.reportData.deal.proportion,
                        created_at: '--',
                        descp: '--'
                    }]
                }
            },
            immediate: true
        },
        //实时输入计算后的佣金
        commissions: {
            handler(val){
                if(this.isShowQuickAdd){
                    this.aquickList[0].commission = this.commissions?.calcCommission || '';
                }
            },
            deep: true
        }
    },
    created(){
        this.getList();
    },
    methods: {
        //获取列表
        async getList(){
            this.loading = true;
            const res = await this.$http.getReceiveCommissionListAPI(this.reportData.report_id);
            this.loading = false;
            if(res.status == 200){
                this.list = (res.data || []).map(item => {
                    let pct = item.commission / this.reportData?.deal?.amount * 100 || 0;
                    item.commissionPct = pct.toFixed(2);
                    return item;
                });
            }
        },
        //快捷增加
        async quickAdd(){
            //检查是否需要保存基本内容
            if(!await this.checkSaveBaseInfo()){
                return;
            }

            const params = {
                report_id: this.reportData.report_id,
                commission: this.aquickList[0].commission,
                descp: ''
            };
            this.quickAdding = true;
            const res = await this.$http.addReceiveCommissionAPI(params);
            if(res.status == 200){
                this.$message.success( res.data?.msg || "添加成功");
                this.getList();
                this.$emit('commissionChange', 'receiveCommission');
            }
            this.quickAdding = false;
        },
        //增加
        add(){
            this.edit({});
        },
        //编辑
        async edit(data){
            //检查是否需要保存基本内容
            if(!await this.checkSaveBaseInfo()){
                return;
            }

            this.dialogs.add = true; 
            await this.$nextTick();
            data.report_id = this.reportData.report_id;
            data.amount = this.reportData.deal.amount;
            data.proportion = this.reportData.deal.proportion;

            this.$refs.add.open(data).onSuccess(()=>{
                this.getList();
                this.$emit('commissionChange', 'receiveCommission');
            });
        },
        //删除
        async del(data){
            const res = await this.$http.delReceiveCommissionAPI(data.id);
            if(res.status == 200){
                this.$message.success( res.data?.msg || "删除成功");
                this.getList();
                this.$emit('commissionChange', 'receiveCommission');
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.el-table .el-input{line-height: 40px;}
.receive-comm{
    .header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
   .footer{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 10px;
       .commission-text{
            font-size: 16px;
            color: #3c3c3c;
        }
        .commission-text-red{
            color: #f40;
            font-weight: 600;
        }   
    }
}
</style>