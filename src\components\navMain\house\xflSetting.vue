<template>
  <div style="margin-left: 78px">
    <el-form style="width: 500px">
      <el-form-item label="全量同步房源 :">
        <el-button
          type="primary"
          :disabled="is_all_add"
          size="small"
          @click="all_pub"
          >全量同步</el-button
        >
      </el-form-item>
      <el-form-item label="全量下架房源 :">
        <el-button
          type="primary"
          :disabled="is_add_remolves"
          size="small"
          @click="all_remove"
          >全量下架</el-button
        >
      </el-form-item>
      <el-form-item label="source_name" label-width="120px">
        <el-input
          v-model="xflDataSetting.source_name"
          style="width: 300px"
        ></el-input>
      </el-form-item>
      <el-form-item label="salt" label-width="120px">
        <el-input v-model="xflDataSetting.salt" style="width: 300px"></el-input>
      </el-form-item>
      <el-button type="primary" @click="settingXflSetting" style="float: right"
        >确定</el-button
      >
    </el-form>
    <new_tips_list
      style="margin-top: 80px; width: 610px"
      :tipsList="tips_list"
    ></new_tips_list>
  </div>
</template>

<script>
import { mapState } from "vuex";
import new_tips_list from "@/components/components/new_tips_list";
export default {
  data() {
    return {
      xflDataSetting: {
        source_name: "",
        salt: ""
      },
      //参数配置弹窗开关
      isxflSettingShow: false,
      //回调地址
      tips_list: ["【可选设置】为快速获取房源推送状态，可联系幸福里官方人员添加回调地址: https://yun.tfcs.cn/xflCallback/info"],
      is_all_add: false,
      is_add_remolves: false
    };
  },
  methods: {
    //获取幸福里参数设置
    getXflSetting() {
      this.$http.xflGetSetting(this.website_info.website_id).then((res) => {
        console.log(res);
        if (res.status == 200) {
          this.xflDataSetting = {
            source_name: res.data.source_name,
            salt: res.data.salt
          }
          //  this.tips_list=[`请联系幸福里官方设置审核回调地址: ${res.data.callback_url}`]
        }
      })
    },
    //参数设置
    dataSetting() {
      this.isxflSettingShow = true
    },
    //参数表单重置
    cancel() {
      this.isxflSettingShow = false;
      // this.xflDataSetting={
      //     source_name:"",
      //     salt:""
      // }
    },
    //设置幸福里参数
    settingXflSetting() {
      this.getXflSetting()
      if (!this.xflDataSetting.source_name) {
        this.$message.warning("请输入source_name")
      } else if (!this.xflDataSetting.salt) {
        this.$message.warning("请输入salt")
      } else {
        this.$http.xflSetting(this.xflDataSetting).then((res) => {
          if (res.status == 200) {
            this.$message.success("设置成功")
            this.getXflSetting()
          }
        })
      }
    },
    //全量同步
    all_pub() {
      console.log("全量同步")
      this.$confirm('此操作将全量同步房源, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.synAllPub().then((res) => {
          if (res.status == 200) {
            this.$message.success("操作成功")
            window.localStorage.setItem("is_all_pub", JSON.stringify({ data: 1, time: new Date().getTime() }))
            this.is_all_add = true
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      })
    },
    //全量下架
    all_remove() {
      this.$confirm('此操作将全量下架房源, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.removeAllPub().then((res) => {
          if (res.status == 200) {
            this.$message.success("操作成功")
            window.localStorage.setItem("is_all_remolve", JSON.stringify({ data: 1, time: new Date().getTime() }))
            this.is_add_remolves = true
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    //比较是否失效
    getLocalAndTime(key, exp = 86400000) {
      let data = window.localStorage.getItem(key);
      if (!data) return null;
      let dataObj = JSON.parse(data)
      if (new Date().getTime() - dataObj.time > exp) {
        window.localStorage.removeLocalStorage(key)
        this.is_all_add = false
      } else {
        this.is_all_add = true
      }
    },
    getLocalAndTimes(key, exp = 86400000) {
      let data = window.localStorage.getItem(key);
      if (!data) return null;
      let dataObj = JSON.parse(data)
      if (new Date().getTime() - dataObj.time > exp) {
        window.localStorage.removeLocalStorage(key)
        this.is_add_remolves = false
      } else {
        this.is_add_remolves = true
      }
    }
  },
  mounted() {
    this.getXflSetting()
    this.getLocalAndTime("is_all_pub")
    this.getLocalAndTimes("is_all_remolve")
  },
  computed: {
    ...mapState(["website_info"]),
  },
  components: { new_tips_list },
};
</script>

<style  scoped lang="scss">
</style>
