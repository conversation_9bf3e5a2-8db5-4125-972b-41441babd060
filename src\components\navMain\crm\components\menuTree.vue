<template>
  <div>
    <el-tree
      :data="menuData"
      node-key="id"
      ref="tree"
      :default-expanded-keys="keyData"
      :check-strictly="checkStrictly"
      highlight-current
      :props="defaultProps"
      :show-checkbox="showCheckbox"
      :default-expand-all="defaultExpandAll"
      @node-click="clickNode"
      @check="checkChange"
      @current-change="currentChange"
      @check-change="currentCheckChange"
      :default-checked-keys="defaultValue"
    >
      <span
        class="custom-tree-node flex-row flex-1"
        slot-scope="{ node, data }"
      >
        <i v-if="node.level"></i>

        <!-- <i v-else-if="node.level == 2" class="city" />
        <i v-else-if="node.level == 3" /> -->
        <div
          class="flex-1"
          @mouseenter="overRow($event, data)"
          @click.prevent.stop="clickItem(data, node)"
        >
          {{ data.name }}
        </div>
        <slideOper
          class="oprt"
          v-if="showOper && menu_list && menu_list.length"
          :ref="'showPop' + data.id"
          :item="data"
          :id="data.id"
          :showPop="currentId == data.id"
          :menu_list="menu_list"
          @onClickMenu="clickMenuItem($event, data)"
        ></slideOper>
      </span>
    </el-tree>
  </div>
</template>

<script>
import slideOper from "./slideOper.vue"
export default {
  props: {
    menuData: {
      type: Array,
    },
    menu_list: {
      type: Array,
    },
    showOper: {
      type: [Boolean, String],
      default: true
    },
    checkStrictly: {
      type: [Boolean],
      default: true
    },
    showCheckbox: {
      type: [Boolean],
      default: false,

    },
    defaultExpandAll: {
      type: [Boolean],
      default: false,

    },
    keyData: {
      type: Array,
      default: () => []
    },
    from: {
      type: [String],
      default: "personnel"
    },
    defaultValue: {
      type: Array,
      default: () => []
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'subs',
          label: 'name',
          value: "id",
        }
      },
    }
  },
  name: 'MenuTree',
  created() {
  },
  mounted() {


  },
  components: {
    slideOper
  },
  data() {
    return {
      currentId: "",
    }
  },
  methods: {
    overRow(e, data) {
      this.currentId = data.id
    },
    clickItem(e, node) {
      this.currentId = e.id
      if (node.expanded) {
        node.expanded = false
      }
      this.$emit('clickItem', e)
    },
    clickMenuItem(e, value) {
      let detail = e
      this.$emit('onClickMenu', { detail, item: value })
    },
    currentCheckChange() {

    },

    currentChange() {
    },
    checkChange() {

    },
    clickNode() {

    },
  }
}
</script>
<style scoped lang="scss">
/* .flex-row {
  display: flex;
  align-items: center;
} */
.row_item {
  .oprt {
    width: 20px;
    height: 50px;
    position: absolute;
    right: 20px;
  }
}
::v-deep .el-tree-node__content {
  padding-top: 8px;
  padding-bottom: 8px;
}
.custom-tree-node {
  color: #8a929f;
  font-size: 14px;
}
</style>