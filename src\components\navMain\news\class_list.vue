<template>
  <el-main>
    <el-header>
      <el-button type="primary" @click="$router.push('/add_list')"
        >添加</el-button
      >
    </el-header>
    <myTable :table-list="tableData" :header="table_header"></myTable>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog title="修改分类" :visible.sync="dialogNews">
      <el-form ref="form" :model="form">
        <el-form-item label="名称" label-width="100px">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <!-- <el-form-item label="所属分类" label-width="100px">
          <el-input v-model="form.name"></el-input>
        </el-form-item> -->
        <el-form-item label="类型" label-width="100px">
          <el-radio-group v-model="form.link_category">
            <el-radio
              v-for="item in category_list"
              :key="item.id"
              :label="item.value"
              >{{ item.description }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" label-width="100px">
          <el-input v-model="form.sort"></el-input>
        </el-form-item>
        <el-form-item label="链接地址" label-width="100px">
          <el-input
            placeholder="外链时有效"
            v-model="form.out_link_address"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogNews = false">取 消</el-button>
        <el-button type="primary" @click="updataNews">确 定</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>

<script>
/* eslint-disable */
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "class_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      news_id: "",
      dialogNews: false,
      news_loading: false,
      form: {
        name: "",
        link_category: "",
        sort: "",
        out_link_address: "",
        id: "",
      },
      category_list: [],
      table_header: [
        { prop: "id", label: "ID" },
        { prop: "name", label: "资讯名称" },
        { prop: "sort", label: "排序" },
        { prop: "created_at", label: "添加时间" },
        {
          prop: "link_category",
          label: "类型",
          formatter: (row) => {
            let link_category = row.link_category;
            if (link_category === 1) {
              return (link_category = "内链");
            }
            if (link_category === 2) {
              return (link_category = "外链");
            }
          },
        },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  type="success"
                  size="mini"
                  onClick={() => {
                    this.changeData(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  onClick={() => {
                    this.deleteData(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.getDataList();
    this.category_list = this.$getDictionary("LINK_CATEGORY");
  },
  watch: {},
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    // 获取列表数据
    getDataList() {
      this.$http
        .uploadCategory(this.params.currentPage, this.params.pagesize)
        .then((res) => {
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    //删除操作
    deleteData(row) {
      // deleteHouse
      this.$confirm("此操作将删除该户型, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCategory(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    changeData(row) {
      this.form = row;
      this.form.link_category = row.link_category + "";
      this.dialogNews = true;
    },
    // 修改
    updataNews() {
      this.$http.updataCategory(this.form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.dialogNews = false;
          this.getDataList();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pagination-box {
  margin-top: 20px;
}
</style>
