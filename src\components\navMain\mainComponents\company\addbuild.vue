<template>
  <el-container ref="table">
    <el-header>
      <tipsList :tips_content="tips_list"></tipsList>
    </el-header>
    <el-main>
      <!-- <div class="title">
				楼盘信息
      </div>-->
      <el-tabs v-model="activeName" type="card" class="tabs">
        <el-tab-pane label="基本信息" name="first">
          <el-form
            :rules="rules"
            :model="formInline"
            label-width="120px"
            class="demo-form-inline"
          >
            <!-- 项目名称 -->
            <el-form-item label="楼盘名称：" prop="build.name">
              <!-- <el-col :span="18"> -->
              <el-input
                maxlength="20"
                v-model="formInline.build.name"
                placeholder="请填写楼盘名称"
              ></el-input>
              <!-- </el-col> -->
            </el-form-item>
            <!-- 城市区域 -->
            <el-form-item label="选择城市：" prop="build.region_0">
              <!-- <el-col :span="9"> -->
              <div
                class="div row"
                style="justify-content: space-between;width:300px"
              >
                <el-select
                  @change="regionOne"
                  v-model="formInline.build.region_0"
                  placeholder="请选择城市"
                  class="select-city"
                >
                  <el-option
                    v-for="item in region_list_one"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="formInline.build.region_1"
                  v-if="region_list_two.length > 0"
                  placeholder="请选择区域"
                  class="select-address"
                >
                  <el-option
                    v-for="item in region_list_two"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </div>
              <!-- </el-col> -->
            </el-form-item>
            <!-- 列表排序 -->
            <el-form-item label="列表排序：" prop="sort">
              <el-input
                v-model="formInline.build.sort"
                type="number"
                placeholder="数值越大越靠前"
              ></el-input>
            </el-form-item>
            <!-- 楼盘地址 -->
            <el-form-item label="楼盘地址：" prop="build.build_address">
              <el-input
                v-model="formInline.build.build_address"
                placeholder=" "
              ></el-input>
            </el-form-item>
            <el-form-item label="楼盘状态：" prop="build.build_status">
              <el-select v-model="formInline.build.build_status">
                <el-option
                  v-for="item in attributes"
                  :key="item.id"
                  :label="item.description"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 楼盘属性
						<div class="lou-type">
							<div class="lou-type-s">楼盘属性</div>
              </div>-->
            <!-- 售楼热线 -->
            <el-form-item label="售楼热线：" prop="build.sales_office_phone">
              <el-input
                v-model="formInline.build.sales_office_phone"
                placeholder="请输入售楼处联系方式"
              ></el-input>
            </el-form-item>
            <!-- 地图坐标 -->
            <el-form-item label="地图坐标：" prop="build.build_location_lat">
              <el-input
                :disabled="true"
                type="number"
                v-model="formInline.build.build_location_lat"
                style="width:100px"
              ></el-input>
              <el-input
                :disabled="true"
                type="number"
                style="width:100px"
                v-model="formInline.build.build_location_long"
              ></el-input>
              <el-button icon="el-icon-location" type="primary" @click="markMap"
                >标注位置</el-button
              >
            </el-form-item>
            <el-form-item label="面积区间：" prop="area_interval">
              <el-input
                v-model="formInline.build_attr.area_space"
                placeholder="楼盘占地面积"
              ></el-input>
            </el-form-item>
            <!-- 售楼地址 -->
            <el-form-item label="售楼地址：">
              <el-input
                v-model="formInline.build.sales_office_address"
                placeholder="请输入售楼处地址"
              ></el-input>
            </el-form-item>
            <!-- 楼盘均价 -->
            <el-form-item label="楼盘均价：" prop="lou_price">
              <el-input
                v-model="formInline.build.build_avg_price"
                placeholder="用于价格排序,不填写为待定"
                ><template slot="append">元/㎡起</template></el-input
              >
            </el-form-item>
            <!-- 价格说明 -->
            <el-form-item label="价格说明：" prop="price_info">
              <el-input
                v-model="formInline.build.price_description"
                placeholder="对均价的补充说明"
              ></el-input>
            </el-form-item>
            <el-form-item label="VR看房：">
              <el-input
                v-model="formInline.build.vr_url"
                placeholder="请输入vr看房地址"
              ></el-input>
            </el-form-item>
            <el-form-item label="装修：">
              <el-checkbox-group
                v-model="formInline.build_attr.decoration_category"
              >
                <el-checkbox
                  v-for="item in decorationlist"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
            <!-- <div class="lou-type">
								<div class="lou-type-s">装修</div>
								<el-checkbox-group v-model="formInline.decoration">
									<el-checkbox
										v-for="item in decorationlist"
										:key="item.id"
										:label="item.value"
										>{{ item.description }}</el-checkbox
									>
								</el-checkbox-group>
              </div>-->
            <!-- 楼层 -->
            <el-form-item label="楼层：">
              <el-checkbox-group v-model="formInline.build_attr.floor_category">
                <el-checkbox
                  v-for="item in floorlist"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-checkbox
                >
              </el-checkbox-group>
              <p style="font-size:12px; color:#999 ">
                参考:1~3层为低层4-7层为多层8~12层为小高层13~25层为高层26层100米以上为超高层
              </p>
            </el-form-item>
            <!-- <div class="lou-type">
								<div class="lou-type-s">楼层</div>
								<el-checkbox-group v-model="formInline.floor">
									<el-checkbox
										v-for="item in floorlist"
										:key="item.id"
										:label="item.value"
										>{{ item.description }}</el-checkbox
									>
								</el-checkbox-group>
								<p style="font-size:12px; color:#999 ">
									参考:1~3层为低层4-7层为多层8~12层为小高层13~25层为高层26层100米以上为超高层
								</p>
              </div>-->
            <!-- 楼盘特色 -->
            <el-form-item label="特色标签：">
              <el-checkbox-group v-model="formInline.build_attr.build_feature">
                <el-checkbox
                  v-for="item in lou_feature_lsit"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="楼盘类型：">
              <el-checkbox-group v-model="formInline.build.build_category">
                <el-checkbox
                  v-for="item in lou_list"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="项目介绍：" v-if="website_mode_category === 2">
              <el-input
                placeholder="楼盘的项目信息介绍"
                :rows="3"
                :cols="100"
                type="textarea"
                v-model="formInline.build_attr.project_introduction"
              ></el-input>
            </el-form-item>
            <!-- 上传楼盘缩略图 -->

            <el-form-item label="楼盘卖点：">
              <el-input
                style="width:500px"
                placeholder="描述楼盘的卖点信息"
                :rows="6"
                :cols="100"
                type="textarea"
                maxlength="500"
                v-model="formInline.build.build_selling_points"
              ></el-input>
            </el-form-item>
            <el-form-item label="楼盘缩略图：">
              <el-upload
                :headers="myHeader"
                :action="build_img_upload"
                :on-success="handleSuccess"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  width="148rpx"
                  height="148rpx"
                  :src="formInline.build.img"
                  v-if="formInline.build.img"
                  alt=""
                />
                <i v-if="!formInline.build.img" class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt />
              </el-dialog>
              <i class="el-icon-info tips" style="color:#909399;"
                >建议图片尺寸：750*560</i
              >
            </el-form-item>

            <!-- 面积区间 -->

            <!-- <div class="lou-type">
								<div class="lou-type-s">特色标签</div>
								<el-checkbox-group v-model="formInline.lou_feature">
									<el-checkbox
										v-for="item in lou_feature_lsit"
										:key="item.id"
										:label="item.value"
										>{{ item.description }}</el-checkbox
									>
								</el-checkbox-group>
              </div>-->
            <el-form-item label="楼书模式：" v-if="website_mode_category === 1">
              <el-radio-group v-model="formInline.build.feature_show_category">
                <el-radio
                  @change="changeFeature"
                  v-for="item in rich_text_show_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              :label="'楼书：'"
              v-if="formInline.build.feature_show_category == 1"
            >
              <el-upload
                :headers="myHeader"
                :action="build_img_upload"
                :on-success="handleSuccessLoushu"
                list-type="picture-card"
                :on-preview="handlePictureCardPreviewLoushu"
                :on-remove="handleRemoveLoushu"
              >
                <img
                  width="148rpx"
                  height="148rpx"
                  :src="formInline.build.feature"
                  v-if="formInline.build.feature"
                  alt=""
                />
                <i v-if="!formInline.build.feature" class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisibleLoushu">
                <img width="100%" :src="dialogImageUrlLoushu" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="楼盘介绍" name="loupanjieshao">
          <el-form
            :rules="rules"
            :inline="true"
            :model="formInline"
            class="demo-form-inline"
            label-width="150px"
            label-position="left"
          >
            <el-form-item
              :label="website_mode_category === 1 ? '楼书：' : '楼盘介绍：'"
              class="ueditor"
            >
              <UE
                :value="ueditor.value"
                :config="ueditor.config"
                @input="inputUe"
                ref="ue"
                style="width:80%;"
              ></UE>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="楼盘数据" name="second">
          <el-form
            :rules="rules"
            :inline="true"
            :model="formInline"
            label-width="120px"
            class="demo-form-inline"
          >
            <el-row>
              <el-col :span="12">
                <!-- 总建筑面积 -->
                <el-form-item label="总建筑面积：">
                  <el-input
                    v-model="formInline.build_attr.total_build_area"
                    placeholder="例：100平米"
                  >
                    <template slot="append">平米</template></el-input
                  >
                </el-form-item>
                <!-- 占地面积 -->
                <el-form-item label="占地面积：">
                  <el-input
                    v-model="formInline.build_attr.land_occupancy_area"
                    placeholder="例：100平米"
                    ><template slot="append">平米</template></el-input
                  >
                </el-form-item>
                <!-- 总户数 -->
                <el-form-item label="总户数：">
                  <el-input
                    v-model="formInline.build_attr.total_land_occupancy"
                    placeholder
                    ><template slot="append">户</template></el-input
                  >
                </el-form-item>
                <!-- 绿化率 -->
                <el-form-item label="绿化率：">
                  <el-input
                    v-model="formInline.build_attr.greening_rate"
                    placeholder="例：20%"
                    ><template slot="append">%</template></el-input
                  >
                </el-form-item>
                <!-- 容积率 -->
                <el-form-item label="容积率：">
                  <el-input
                    v-model="formInline.build_attr.plot_ratio"
                    placeholder="例：2.00"
                  ></el-input>
                </el-form-item>
                <!-- 停车位 -->
                <el-form-item label="停车位：">
                  <el-input
                    v-model="formInline.build_attr.total_parking_space"
                  ></el-input>
                </el-form-item>
                <!-- 房屋产权 -->
                <el-form-item label="房屋产权：">
                  <el-input
                    v-model="formInline.build_attr.property_right_years"
                    placeholder="例：70年"
                  >
                    <template slot="append">年</template></el-input
                  >
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼间距：">
                  <el-input
                    v-model="formInline.build_attr.distance"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="电梯：">
                  <el-checkbox-group v-model="formInline.build_attr.elevator">
                    <el-checkbox label="电梯">电梯</el-checkbox>
                    <el-checkbox label="步梯">步梯</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  label="楼层状况："
                  v-if="website_mode_category === 2"
                >
                  <el-input
                    type="textarea"
                    v-model="formInline.build_attr.floor_description"
                    placeholder="请输入楼层状况"
                    :rows="3"
                    :cols="100"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="工程进度："
                  v-if="website_mode_category === 2"
                >
                  <el-radio-group
                    v-model="formInline.build_attr.project_progress_category"
                  >
                    <el-radio
                      v-for="item in project_progress_category_list"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.description }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="交房标准："
                  v-if="website_mode_category === 2"
                >
                  <el-radio-group
                    v-model="formInline.build_attr.delivery_standard_category"
                  >
                    <el-radio
                      v-for="item in delivery_standard_category_list"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.description }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
                <!-- 开盘时间 -->
                <el-form-item label="开盘时间：">
                  <el-date-picker
                    v-model="formInline.build.newest_opening_time"
                    type="date"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                  <p style="color:rgb(153, 153, 153)">
                    主要以开盘时间说明为准，这里用于开盘日历及列表排序
                  </p>
                </el-form-item>
                <!-- 竣工时间 -->
                <el-form-item label="竣工时间：">
                  <el-date-picker
                    v-model="formInline.build.completion_house_time"
                    type="date"
                    placeholder="选择日期"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="交房日期：">
                  <el-date-picker
                    v-model="formInline.build_attr.delivery_date"
                    type="date"
                    placeholder="请选择日期"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="分享设置" name="third">
          <el-form
            :rules="rules"
            :model="formInline"
            label-width="120px"
            class="demo-form-inline"
          >
            <el-form-item label="设置标题：">
              <el-input
                v-model="formInline.build.share_title"
                placeholder="楼盘分享时标题"
              ></el-input>
            </el-form-item>
            <el-form-item label="设置描述：">
              <el-input
                :rows="3"
                :cols="100"
                type="textarea"
                v-model="formInline.build.share_description"
                placeholder="楼盘描述信息"
              ></el-input>
            </el-form-item>
            <!-- 上传分享设置图片 -->
            <el-form-item label="分享图片：">
              <el-upload
                :headers="myHeader"
                :action="build_img_share"
                :on-success="handleSuccessShare"
                list-type="picture-card"
                :show-file-list="false"
                :on-preview="handlePictureCardPreviewShare"
                :on-remove="handleRemoveShare"
              >
                <img
                  v-if="formInline.build.share_img"
                  :src="formInline.build.share_img"
                  class="avatar"
                />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisibleShare">
                <img width="100%" :src="dialogImageUrlShare" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          label="品牌介绍"
          name="fourth"
          v-if="website_mode_category === 1"
        >
          <el-form :model="formInline">
            <el-form-item label="品牌介绍模式：">
              <el-radio-group
                v-model="formInline.build.brand_introduction_show_category"
              >
                <el-radio
                  @change="changeBrand"
                  v-for="item in rich_text_show_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item
              label="品牌介绍： "
              v-if="formInline.build.brand_introduction_show_category == 0"
            >
              <UE
                :value="ueditor_introduction.value"
                :config="ueditor_introduction.config"
                @input="inputUeBuild"
                ref="ue"
                style="width:1000px"
              ></UE>
            </el-form-item>

            <el-form-item
              label="品牌介绍："
              v-if="formInline.build.brand_introduction_show_category == 1"
            >
              <el-upload
                :headers="myHeader"
                :action="build_img_upload"
                :on-success="handleSuccessBrand"
                list-type="picture-card"
                :on-preview="handlePictureCardPreviewBrand"
                :on-remove="handleRemoveBrand"
              >
                <img
                  width="148rpx"
                  height="148rpx"
                  :src="formInline.build.brand_introduction"
                  v-if="formInline.build.brand_introduction"
                  alt=""
                />
                <i
                  v-if="!formInline.build.brand_introduction"
                  class="el-icon-plus"
                ></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisibleBrand">
                <img width="100%" :src="dialogImageUrlBrand" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="其它数据" name="fifth">
          <el-form
            :model="formInline"
            class="demo-form-inline"
            label-width="120px"
          >
            <!-- 购房优惠 -->
            <el-form-item label="购房优惠：" prop="buy_discount">
              <el-input
                v-model="formInline.build_attr.buy_coupon"
                placeholder="楼盘优惠信息"
                maxlength="10"
                show-word-limit
              ></el-input>
            </el-form-item>
            <!-- 开放商 -->
            <el-form-item label="开发商：" prop="developer">
              <el-input
                v-model="formInline.build_attr.developers_company_name"
                placeholder="开发商信息"
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
            <!-- 销售许可证 -->
            <el-form-item label="销售许可证：" prop="license">
              <el-input
                v-model="formInline.build_attr.sales_license"
                placeholder="楼盘的销售许可证"
              ></el-input>
            </el-form-item>
            <!-- 装修 -->
            <!-- 销售许可证 -->
            <el-form-item
              label="预售售许可证："
              prop="license"
              v-if="website_mode_category === 2"
            >
              <el-input
                disabled
                v-model="formInline.build_attr.presell_license"
                placeholder="楼盘的预售许可证"
              ></el-input>
            </el-form-item>
            <!-- 装修 -->
            <el-form-item label="投资商：" v-if="website_mode_category === 2">
              <el-input
                v-model="formInline.build_attr.investment_company"
                placeholder="请输入投资商名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="交通情况：" v-if="website_mode_category === 2">
              <el-input
                type="textarea"
                v-model="formInline.build_attr.traffic_description"
                placeholder="请输入交通情况"
                :rows="3"
                :cols="100"
              ></el-input>
            </el-form-item>
            <el-form-item label="周边配套：" v-if="website_mode_category === 2">
              <el-input
                type="textarea"
                v-model="formInline.build_attr.around_description"
                placeholder="请输入周边配套"
                :rows="3"
                :cols="100"
              ></el-input>
            </el-form-item>
            <el-form-item label="营销公司：" v-if="website_mode_category === 2">
              <el-input
                v-model="formInline.build_attr.marketing_company"
                placeholder="请输入营销公司"
              ></el-input>
            </el-form-item>
            <el-form-item label="物业费：" prop="property_costs">
              <el-input
                min="0"
                step="0.1"
                type="number"
                v-model="formInline.build_attr.real_estate_price"
                placeholder="楼盘物业费用"
                ><template slot="append">元（/㎡/月）</template></el-input
              >
            </el-form-item>
            <!-- 物业公司 -->
            <el-form-item label="物业公司：" prop="property_company">
              <el-input
                v-model="formInline.build_attr.real_estate_company"
                placeholder="楼盘物业公司信息"
              ></el-input>
            </el-form-item>
            <el-form-item label="物业描述：" v-if="website_mode_category === 2">
              <el-input
                type="textarea"
                v-model="formInline.build_attr.real_estate_description"
                placeholder="请输入物业描述"
                :rows="3"
                :cols="100"
              ></el-input>
            </el-form-item>
            <el-form-item label="物业类型：" v-if="website_mode_category === 2">
              <el-input
                type="textarea"
                v-model="formInline.build_attr.real_estate_category"
                placeholder="请输入物业类型"
                :rows="3"
                :cols="100"
                maxlength="50"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          label="买房交流群"
          name="sixth"
          v-if="website_mode_category == 2"
        >
          <el-form
            :model="formInline"
            class="demo-form-line"
            label-width="120px"
          >
            <el-col :span="16">
              <el-form-item label="选择模式">
                <el-radio-group
                  v-model="formInline.build.estate_purchase_wx_category"
                >
                  <el-radio-button
                    v-for="item in estate_purchase_wx_category_list"
                    :key="item.value"
                    :label="item.value"
                    >{{ item.description }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
              <el-col :span="16">
                <el-form-item label="客服微信">
                  <el-input
                    v-model="formInline.build.estate_purchase_service_wx"
                    placeholder="填写客服微信"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="交流群二维码">
                  <el-upload
                    :headers="myHeader"
                    :action="website_img"
                    :on-success="handleSuccessWX"
                    list-type="picture-card"
                    :show-file-list="false"
                    :on-preview="handlePictureCardPreviewWX"
                    :on-remove="handleRemoveWX"
                  >
                    <img
                      v-if="formInline.build.estate_purchase_wx_group_qrcode"
                      :src="formInline.build.estate_purchase_wx_group_qrcode"
                      class="avatar"
                    />
                    <i v-else class="el-icon-plus"></i>
                  </el-upload>
                  <el-dialog :visible.sync="dialogVisibleWX">
                    <img width="100%" :src="dialogImageUrlWX" alt />
                  </el-dialog>
                </el-form-item>
              </el-col>
            </el-col>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <el-form>
        <!-- 提交返回 -->
        <div class="btn-box">
          <el-form-item size="large">
            <el-button type="primary" @click="onSubmit">保存楼盘信息</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
    <!-- 地图容器 -->
    <el-dialog
      title="选择位置"
      :visible.sync="dialogVisibleMap"
      class="dialog-map"
    >
      <div>
        <el-input
          v-model="formInline.build.build_address"
          style="width:300px"
          placeholder="请输入市区/市县后在输入详细楼盘地址"
        ></el-input>
        <el-button type="primary" @click="getAddressKeyword">搜索</el-button>
      </div>
      <div slot="footer">
        <div
          id="container"
          style="width:auto; height:500px;margin:20px 0"
        ></div>
        <span class="dialog-footer">
          <el-button @click="dialogVisibleMap = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisibleMap = false"
            >确 定</el-button
          >
        </span>
      </div>
    </el-dialog>
    <goBack></goBack>
  </el-container>
</template>

<script>
/* eslint-disable */
import config from "@/utils/config";
import goBack from "@/components/components/goBack";
import UE from "@/components/ueditor";
import tipsList from "@/components/components/tips_list";
import { mapState } from "vuex";
export default {
  name: "addbuild",
  data() {
    return {
      dialogVisibleMap: false,
      markersArray: [],
      // 提交内容表单
      formInline: {
        build: {
          id: "",
          region_0: "",
          region_1: 0,
          build_address: "",
          build_status: ["1"],
          sales_office_address: "",
          build_location_lat: "",
          build_location_long: "",
          build_category: ["1"],
          sales_office_phone: "",
          build_avg_price: "0",
          price_description: "",
          newest_opening_time: "",
          completion_house_time: "",
          build_selling_points: "",
          img: "",
          feature: "",
          share_img: "",
          share_title: "",
          share_description: "",
          sort: "0",
          vr_url: "",
          brand_introduction: "",
          feature_show_category: "0",
          brand_introduction_show_category: "0",
          estate_purchase_service_wx: "",
          estate_purchase_wx_category: "0",
          estate_purchase_wx_group_qrcode: "",
        },
        build_attr: {
          real_estate_price: "0",
          real_estate_company: "",
          area_space: "",
          buy_coupon: "",
          developers_company_name: "",
          sales_license: "",
          decoration_category: ["1"],
          floor_category: ["1"],
          build_feature: ["1"],
          total_build_area: "0",
          land_occupancy_area: "0",
          total_land_occupancy: "0",
          greening_rate: "0",
          plot_ratio: "0",
          total_parking_space: "0",
          property_right_years: "70",
          real_estate_category: "",
          project_progress_category: "1",
          delivery_standard_category: "1",
          investment_company: "",
          traffic_description: "",
          around_description: "",
          marketing_company: "",
          real_estate_description: "",
          floor_description: "",
          delivery_date: "",
          project_introduction: "",
          presell_license: "",
          elevator: [],
          distance: "",
        },
      },
      // 楼盘类型数组
      lou_list: [],
      // 装修类型数组
      decorationlist: [],
      // 楼层类型数组
      floorlist: [],
      // 楼盘特色类型数组
      lou_feature_lsit: [],
      // 区域选择列表数组
      arealist: [{ name: "枣庄", id: 1 }],
      // 板块选择列表数组
      platelist: [{ name: "滕州", id: 1 }],
      // 楼盘属性列表数组
      attributes: [],
      // 输入框规则
      rules: {
        "build.name": [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        "build.build_location_lat": [
          { required: true, message: "请输入内容", trigger: "blur" },
        ],
        "build.sales_office_phone": [
          { required: true, message: "请输入联系方式", trigger: "blur" },
        ],
        "build.region_0": [
          { required: true, trigger: "blur", message: "请输入内容" },
        ],
        "build.build_address": [
          { required: true, trigger: "blur", message: "请输入地址" },
        ],
        "build.build_status": [
          { required: true, message: "请选择楼盘状态", trigger: "change" },
        ],
      },
      // 上传的内容
      dialogImageUrl: "",
      dialogVisible: false,
      city_list: [],
      region_list: [],
      region_list_one: [],
      region_list_two: [],
      //腾讯地图
      map: null,
      getAddress: null,
      getAddCode: null,
      getSelfPosition: null,
      lat: "",
      lng: "",
      select2: [],
      //腾讯地图
      ueditor: {
        value: "",
        config: {
          initialFrameWidth: "100%",
          autoHeightEnabled: false,
        },
      },
      ueditor_introduction: {
        value: "",
        config: {
          initialFrameWidth: "100%",
        },
      },
      ue: "ue",
      dialogImageUrlShare: "",
      dialogVisibleShare: false,
      activeName: "first",
      // 提示信息数组
      tips_list: [
        "新增内容后编辑可以增加楼盘地图坐标,增加坐标后即可在地图中显示地图坐标输入框依次对应 lng,lat (经纬度)带 * 为必填项",
      ],
      build_img_upload: `/api/common/file/upload/enterprise?category=${config.BUILD_IMG}`,
      build_img_share: `/api/common/file/upload/enterprise?category=${config.PROJECT_SHARE_IMG}`,
      website_img: `/api/common/file/upload/enterprise?category=${config.WEBSITE_IMG}`,
      website_mode_category: this.$store.state.website_info
        .website_mode_category,
      project_progress_category_list: [],
      delivery_standard_category_list: [],
      rich_text_show_category_list: [], // 选择富文本模式
      dialogImageUrlLoushu: "",
      dialogVisibleLoushu: false,
      dialogImageUrlBrand: "",
      dialogVisibleBrand: false,
      mapLoading: true,
      estate_purchase_wx_category_list: [
        { value: "0", description: "系统配置" },
        { value: "1", description: "自定义" },
      ],
      dialogVisibleWX: false,
      dialogImageUrlWX: "",
    };
  },
  mounted() {
    if (this.$route.query.build_id) {
      this.formInline.build.id = this.$route.query.build_id;
    }
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "BUILD_CATEGORY":
            this.lou_list = item.childs;
            break;
          case "BUILD_STATUS":
            this.attributes = item.childs;
            break;
          case "BUILD_FLOOR_CATEGORY":
            this.floorlist = item.childs;
            break;
          case "BUILD_FEATURE":
            this.lou_feature_lsit = item.childs;
            break;
          case "BUILD_DECORATION_CATEGORY":
            this.decorationlist = item.childs;
            break;
          case "BUILD_PROJECT_PROGRESS_CATEGORY":
            this.project_progress_category_list = item.childs;
            break;
          case "BUILD_DELIVERY_STANDARD_CATEGORY":
            this.delivery_standard_category_list = item.childs;
            break;
          case "RICH_TEXT_SHOW_CATEGORY":
            this.rich_text_show_category_list = item.childs;
            break;
        }
      });
    });
    this.getRegion();
    //监听页面滚动事件
  },
  components: { UE, goBack, tipsList },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("company_token"),
      };
    },
    ...mapState(["website_info"]),
  },
  watch: {},
  methods: {
    // 文件上传之前做处理
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 15;
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 15MB!");
      }
      return isLt2M;
    },
    getQueryBuildData() {
      this.$http.getComapnyBuildDetail(this.formInline.build.id).then((res) => {
        if (res.status === 200) {
          this.formInline.build = res.data;
          this.formInline.build.build_status = res.data.build_status + "";
          if (res.data.build_category) {
            this.formInline.build.build_category = res.data.build_category.split(
              ","
            );
          }
          this.regionOne(this.formInline.build.region_0, true);
          this.lat = this.formInline.build.build_location_lat;
          this.lng = this.formInline.build.build_location_long;
          this.formInline.build.feature_show_category =
            res.data.feature_show_category + "";
          this.formInline.build.brand_introduction_show_category =
            res.data.brand_introduction_show_category + "";
          this.ueditor.value = res.data.feature;
        }
      });
    },
    getQueryAttrData() {
      this.$http
        .getComapnyBuildDetailAttr(this.formInline.build.id)
        .then((res) => {
          if (res.status === 200) {
            this.formInline.build_attr = res.data;
            this.formInline.build_attr.real_estate_price = parseFloat(
              res.data.real_estate_price
            );
            if (res.data.floor_category) {
              this.formInline.build_attr.floor_category = res.data.floor_category.split(
                ","
              );
            } else {
              this.formInline.build_attr.floor_category = [];
            }
            if (res.data.build_feature) {
              this.formInline.build_attr.build_feature = res.data.build_feature.split(
                ","
              );
            } else {
              this.formInline.build_attr.build_feature = [];
            }
            if (res.data.decoration_category) {
              this.formInline.build_attr.decoration_category = res.data.decoration_category.split(
                ","
              );
            } else {
              this.formInline.build_attr.decoration_category = [];
            }
            this.formInline.build_attr.project_progress_category =
              res.data.project_progress_category + "";
            this.formInline.build_attr.delivery_standard_category =
              res.data.delivery_standard_category + "";
            this.formInline.build_attr.elevator = res.data.elevator.split(",");
          }
        });
    },
    // 获取城市
    getRegion() {
      this.$http.getCompanyRegion().then((res) => {
        if (res.status === 200) {
          this.region_list = this.$sortPro(res.data, ["pid"]);
          this.region_list.map((item, index) => {
            if (item.pid === 0) {
              this.region_list_one = item.children;
            }
          });
          if (this.formInline.build.id) {
            this.getQueryBuildData();
            this.getQueryAttrData();
          }
        }
      });
    },
    // 上传文件
    handleRemove(file, fileList) {},
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response.url;
      this.dialogVisible = true;
    },
    handleSuccess(response, file, fileList) {
      this.formInline.build.img = response.url;
    },

    handleRemoveLoushu(file, fileList) {},
    handlePictureCardPreviewLoushu(file) {
      this.dialogImageUrlLoushu = file.response.url;
      this.dialogVisibleLoushu = true;
    },
    handleSuccessLoushu(response, file, fileList) {
      this.formInline.build.feature = response.url;
    },
    // 上传分享文件
    handleRemoveShare(file, fileList) {},
    handlePictureCardPreviewShare(file) {
      this.dialogImageUrlShare = file.response.url;
      this.dialogVisibleShare = true;
    },
    handleSuccessShare(response, file, fileList) {
      this.formInline.build.share_img = response.url;
    },

    handleRemoveBrand(file, fileList) {},
    handlePictureCardPreviewBrand(file) {
      this.dialogImageUrlBrand = file.response.url;
      this.dialogVisibleBrand = true;
    },
    handleSuccessBrand(response, file, fileList) {
      this.formInline.build.brand_introduction = response.url;
    },
    // :http-request="uploadFile"
    // 提交
    onSubmit() {
      if (
        !this.formInline.build.name ||
        !this.formInline.build.build_location_lat ||
        !this.formInline.build.build_location_long ||
        !this.formInline.build.sales_office_phone
      ) {
        this.$message({
          message: "请输入必填字段",
          type: "error",
        });
      } else {
        this.formInline.build.build_location_lat =
          this.formInline.build.build_location_lat + "";
        this.formInline.build.build_location_long =
          this.formInline.build.build_location_long + "";
        this.formInline.build_attr.elevator = this.formInline.build_attr.elevator.join(
          ","
        );
        if (this.formInline.build.id) {
          this.$http
            .updateCompanyBuildData(this.formInline)
            .then((res) => {
              if (res.status === 200) {
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
                this.formInline.build_attr.elevator = this.formInline.build_attr.elevator.split(
                  ","
                );
                this.$goPath("/company_property_list");
              }
            })
            .catch(() => {
              this.formInline.build_attr.elevator = this.formInline.build_attr.elevator.split(
                ","
              );
            });
        } else {
          this.$http
            .setCompanyBuildData(
              this.formInline
              // 省市区
            )
            .then((res) => {
              if (res.status === 200) {
                this.$message({
                  message: "提交成功",
                  type: "success",
                });
                this.formInline.build_attr.elevator = this.formInline.build_attr.elevator.split(
                  ","
                );
                this.$goPath("/company_property_list");
              }
            })
            .catch(() => {
              this.formInline.build_attr.elevator = this.formInline.build_attr.elevator.split(
                ","
              );
            });
        }
      }
    },
    // 返回楼盘列表
    goBack() {
      this.$goPath("/company_property_list");
    },
    markMap() {
      // if (!this.website_info.map_lat && !this.website_info.map_long) {
      //   this.getTXLocation();
      // }
      setTimeout(() => {
        this.init();
        this.dialogVisibleMap = true;
      }, 400);
    },
    init() {
      var that = this;
      if (that.website_info.map_lat && that.website_info.map_long) {
        var myLatlng = new qq.maps.LatLng(
          that.lat || that.website_info.map_lat,
          that.lng || that.website_info.map_long
        );
      } else {
        myLatlng = new qq.maps.LatLng(26.326248944008693, 116.35539315640926);
      }
      var myOptions = {
        zoom: 16,
        center: myLatlng,
      };
      that.mapLoading = false;
      that.map = new qq.maps.Map(
        document.getElementById("container"),
        myOptions
      );
      //获取点击后的地址
      qq.maps.event.addListener(that.map, "click", function(event) {
        //通过坐标来显示地图地址
        that.getAddCode = new qq.maps.Geocoder({
          complete: function(result) {
            console.log(result);
            that.formInline.build.build_address =
              result.detail.addressComponents.city +
              result.detail.addressComponents.district +
              result.detail.addressComponents.town +
              result.detail.addressComponents.street;
          },
        });
        // 获取点击后的地图坐标
        that.formInline.build.build_location_lat = event.latLng.lat;
        that.formInline.build.build_location_long = event.latLng.lng;
        var marker = new qq.maps.Marker({
          map: that.map,
          position: new qq.maps.LatLng(
            that.formInline.build.build_location_lat,
            that.formInline.build.build_location_long
          ),
        });
        that.markersArray.push(marker);
        if (that.markersArray.length > 1) {
          for (let i = 0; i < that.markersArray.length - 1; i++) {
            that.markersArray[i].setMap(null); // 清除标记
          }
        }

        that.getAddressCode();
      });

      //调用地址显示地图位置并设置地址
      that.getAddress = new qq.maps.Geocoder({
        complete: function(result) {
          that.map.setCenter(result.detail.location);
          that.formInline.build.build_location_long =
            result.detail.location.lng;
          that.formInline.build.build_location_lat = result.detail.location.lat;
          var marker = new qq.maps.Marker({
            map: that.map,
            position: result.detail.location,
          });
        },
      });
    },
    //通过地址获得位置  搜索功能
    getAddressKeyword() {
      //通过getLocation();方法获取位置信息值
      this.getAddress.getLocation(this.formInline.build.build_address);
      // 调用自带的接口;
    },
    // 通过坐标获得地址
    getAddressCode() {
      var lat = parseFloat(this.formInline.build.build_location_lat);
      var lng = parseFloat(this.formInline.build.build_location_long);
      var latLng = new qq.maps.LatLng(lat, lng);
      //调用获取位置方法
      this.getAddCode.getAddress(latLng);
    },
    // 获取当前定位，/市
    getTXLocation() {
      var _this = this;
      var geolocation = new qq.maps.Geolocation(
        "GVZBZ-47RCP-D6LDP-LCA5V-PUQFQ-QMB2V",
        "yunbaobei"
      );
      var options = { timeout: 8000 };
      var latitude, longitude, address;
      if (_this.website_info.map_lat && _this.website_info.map_long) {
        _this.lat = _this.website_info.map_lat;
        _this.lng = _this.website_info.map_long;
        _this.mapLoading = false;
        this.init();
      } else {
        geolocation.getLocation(showPosition, showErr, options);
        function showPosition(position) {
          if (position) {
            _this.mapLoading = false;
          }
          latitude = position.lat;
          longitude = position.lng;
          address = position.nation + position.province + position.city;
          _this.getShopmsg(latitude, longitude, address); //获取到经纬度后的操作
        }
        function showErr(position) {}
      }
    },
    // 获取经纬度后的操作
    getShopmsg(lat, lng, address) {
      this.lat = lat;
      this.lng = lng;
      this.formInline.build.build_address = address;
      this.init();
    },
    regionOne(e, status) {
      if (!status) {
        this.formInline.build.region_1 = 0;
        this.region_list_two = [];
      }
      this.region_list.map((item, index) => {
        if (item.pid === 0) {
          this.region_list_one = item.children;
        } else {
          if (item.pid === e) {
            if (item.children) {
              this.region_list_two = item.children;
              // 如果status = undefined 说明选择了一级区域，二级区域赋值为数组第一位
              if (status === undefined) {
                this.formInline.build.region_1 = item.children[0].id;
              }
            }
          }
        }
      });
    },
    inputUe(obj) {
      // 编辑器内容有变动，具体处理自行修改
      this.formInline.build.feature = obj.content;
    },
    inputUeBuild(obj) {
      this.formInline.build.brand_introduction = obj.content;
    },
    changeFeature(e) {
      this.formInline.build.feature_show_category = e;
    },
    changeBrand(e) {
      this.formInline.build.brand_introduction_show_category = e;
    },
    handleSuccessWX(response) {
      this.formInline.build.estate_purchase_wx_group_qrcode = response.url;
    },
    handlePictureCardPreviewWX(file) {
      this.dialogImageUrlWX = file.response.url;
      this.dialogVisibleWX = true;
    },
    handleRemoveWX() {},
  },
};
</script>

<style lang="scss" scoped>
.ueditor {
  margin: 20px 0;
  display: flex;
}

.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
.title {
  color: #333;
  font-weight: bold;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
  }
  i {
    color: red;
  }
}
.el-main {
  margin-top: 40px;
  .title {
    margin: 0 0 10px;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
  .el-input-number,
  .el-input,
  .el-select,
  .el-textarea {
    width: 300px;
  }
  .select-city {
    flex: 1;
  }
  .select-address {
    width: 140px;
    margin-left: 20px;
  }
  .lou-type {
    margin: 20px 0;
    .lou-type-s {
      text-align: right;
      vertical-align: middle;
      float: left;
      font-size: 14px;
      color: #606266;
      padding: 0 12px 0 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }
  }
  .tips-box {
    margin: 10px 0;
    justify-content: flex-start;
    .tips-left {
      color: #2589ff;
    }
    .tips-right {
      margin-left: 20px;
      color: rgb(153, 153, 153);
    }
  }
}
.btn-box {
  margin-left: 75px;
}
.avatar {
  width: 148px;
  height: 148px;
}
.el-checkbox-group {
  width: 600px;
}
</style>
