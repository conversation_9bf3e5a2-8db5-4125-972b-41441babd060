<template>
    <div>
        <el-dialog title="批量修改来源" :visible.sync="dialogVisible" width="30%"
          :before-close="handleClose">
          <div class="levelstyle"> 
            <div>
                <el-tree
                  ref="tree"
                  :data="source_list"
                  show-checkbox
                  node-key="id"
                  :props="defaultProps"
                  @check="handleCheckChange"
                  :default-checked-keys="defaultCheckedKeys"
                  :check-strictly="true"
                  @check-change="currentCheckChange"
                ></el-tree>
            </div>
            <div class="prompt"> 
              <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"></i>
              <span>当前已选择{{multipleSelection.length}}条，执行后不可恢复，请确认后再执行操作</span>
            </div>
          </div>

 
       
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="sublevel">确 定</el-button>
          </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props:{
        source_list:{
            type:Array,
            default:()=>[]
        }
    },
    data() {
        return {
            dialogVisible:false,
            defaultProps: {
              children: 'children',
              label: 'title'
            },
            multipleSelection:[],
            defaultCheckedKeys: [],
            defaultIds: [],
            checkedKeys:[],
        }
    },
    methods:{
        open(params){
            this.multipleSelection = params
            this.dialogVisible = true
        },
        handleClose(){
            this.dialogVisible = false
        },
        sublevel(){
            // console.log(this.multipleSelection);
            // console.log(this.checkedKeys);
            let params = {
                ids:this.multipleSelection.join(","),
                source_id:this.checkedKeys.id,
                source2_id:this.checkedKeys.parent_id
            }
            this.$http.plcustomersource(params).then(res=>{
                if(res.status == 200){
                    this.$message.success("修改成功！")
                    this.dialogVisible = false;
                    this.$emit("getDataList");
                }
            })
        },
        handleCheckChange(checkedKeys, info) {
           this.checkedKeys =  checkedKeys
        },
        currentCheckChange(data, type) {
          if (type === false) {
            let index = this.defaultIds.findIndex((item) => item == data.id);
            this.defaultIds.splice(index, 1);
          } else {
            this.changeSelected([data.id]);
          }
        },
        changeSelected(ids = this.defaultValue, type = true) {
          this.$refs.tree.setCheckedKeys(ids, type);
          let nodes = this.$refs.tree.getCheckedNodes();
          let keys = this.$refs.tree.getCheckedKeys();
          let node = {
            checkedKeys: keys,
            checkedNodes: nodes,
          };
        },
    },
}
</script>
<style lang="scss" scoped>
.levelstyle{
    width: 90%;
    margin: 0 auto;
    text-align: center;
}
.prompt{
    display: flex;
    margin-top: 20px;
    span{
        color:red;
    }
}
</style>