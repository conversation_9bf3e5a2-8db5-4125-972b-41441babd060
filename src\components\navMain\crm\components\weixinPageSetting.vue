<template>
  <div>
    <el-tabs v-model="active_name">
      <el-tab-pane
        :label="item.title"
        :name="value"
        v-for="(item, value) in setting"
        :key="value"
      >
        <!-- v-for="(item, value) in setting"
                :key="value" -->
        <div class="info">
          <!-- <el-card shadow="never"> -->
          <!-- <div slot="header" class="clearfix">
                  <span>{{ item.title }}</span>
                </div> -->
          <el-form label-width="150px" size="medium">
            <el-form-item
              :label="sub.title"
              v-for="(sub, key) in item.sub"
              :key="key"
            >
              <template v-if="sub.type != 'json'">
                <template v-if="sub.type != 'image'">
                  <span slot="label">
                    {{ sub.title }}
                    <el-tooltip
                      v-if="sub.tips"
                      :content="sub.tips"
                      placement="top-start"
                    >
                      <el-link type="warning" :underline="false"
                        ><i class="el-icon-info"></i
                      ></el-link>
                    </el-tooltip>
                  </span>
                  <el-input
                    v-model="douyinIndexParam[sub.name]"
                    style="width: 50%"
                    v-if="sub.type == 'text'"
                    :maxlength="
                      sub.name == 'app_name'
                        ? 10
                        : sub.name == 'share_title'
                        ? 15
                        : 255
                    "
                    show-word-limit
                  ></el-input>
                  <el-input
                    v-model="douyinIndexParam[sub.name]"
                    style="width: 50%"
                    type="textarea"
                    rows="3"
                    v-if="sub.type == 'textarea'"
                    :maxlength="
                      sub.name == 'app_name'
                        ? 10
                        : sub.name == 'share_title'
                        ? 15
                        : 255
                    "
                    show-word-limit
                  ></el-input>
                  <el-radio-group
                    v-model="douyinIndexParam[sub.name]"
                    v-if="sub.type == 'radio'"
                  >
                    <el-radio
                      v-for="rItem in sub.option"
                      :key="rItem.value"
                      :label="rItem.value"
                      >{{ rItem.name }}</el-radio
                    >
                  </el-radio-group>
                  <template v-if="sub.type == 'rich_text'">
                    <UE
                      :value="douyinIndexParam[sub.name]"
                      :config="ueditor.config"
                      @input="inputUe($event, sub.name)"
                      :ids="'ueditor' + sub.name"
                      :ref="'ue' + sub.name"
                      style="width: 1000px"
                    ></UE>
                  </template>
                </template>
                <template v-if="sub.type == 'image'">
                  <el-row>
                    <el-col style="width: 200px">
                      <el-upload
                        action="/api/common/file/upload/admin?category=3"
                        list-type="picture-card"
                        :headers="myHeader"
                        :show-file-list="false"
                        :on-preview="handlePictureCardPreview"
                        :on-success="(e) => handleUploadSuccess(e, sub.name)"
                      >
                        <img
                          v-if="douyinIndexParam[sub.name]"
                          :src="
                            douyinIndexParam[sub.name] +
                            '?x-oss-process=style/w_240'
                          "
                          class="up_image"
                        />
                        <i v-else class="el-icon-plus"></i>
                      </el-upload>
                    </el-col>
                    <el-col :span="10" v-if="sub.tips">
                      <div
                        style="
                          background-color: #faecd8;
                          color: #f40;
                          padding: 10px;
                          line-height: 2;
                        "
                      >
                        {{ sub.tips }}
                      </div>
                    </el-col>
                  </el-row>
                </template>
              </template>
              <template v-if="sub.type == 'json'">
                <template v-if="sub.name == 'nav_setting'">
                  <!-- 菜单设置 -->
                  <el-form label-width="80px" size="mini">
                    <el-form-item label-width="0" label="">
                      <el-radio-group
                        v-model="setting_nav.status"
                        @change="radioChange"
                      >
                        <el-radio
                          v-for="nav_setting in setting_nav.value"
                          :key="nav_setting.value"
                          :label="nav_setting.value"
                          >{{ nav_setting.title }}</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>

                    <el-form-item label-width="0" v-if="setting_nav.status">
                      <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                      <el-form size="mini">
                        <ul v-if="setting_nav.value.length>0">
                          <li
                            style="
                              display: inline-block;
                              margin-right: 15px;
                              margin-bottom: 10px;
                            "
                            v-for="(row, rowi) in setting_nav.value[
                              setting_nav.status - 1
                            ].children"
                            :key="rowi"
                          >
                            <el-upload
                              action="/api/common/file/upload/admin?category=3"
                              list-type="picture-card"
                              style="margin-bottom: 10px"
                              :headers="myHeader"
                              :show-file-list="false"
                              disabled
                              :on-preview="handlePictureCardPreview"
                              :on-success="
                                (e) => handleUploadSuccess(e, row, 1)
                              "
                            >
                              <img
                                v-if="row.bg_pic"
                                :src="row.bg_pic"
                                class="up_image"
                              />
                              <i v-else class="el-icon-plus"></i>
                            </el-upload>
                            <el-form-item
                              label="标题"
                              label-width="50px"
                              class="img-input"
                              :rules="{
                                required: true,
                                message: '请填写标题',
                                trigger: 'blur',
                              }"
                            >
                              <el-input
                                v-model="row.title"
                                size="mini"
                                class="w100"
                              ></el-input>
                            </el-form-item>
                            <el-form-item
                              label="链接"
                              label-width="50px"
                              class="img-input"
                              :rules="{
                                required: true,
                                message: '请填写链接',
                                trigger: 'blur',
                              }"
                            >
                              <el-input
                                v-model="row.link"
                                size="mini"
                                disabled
                                class="w100"
                              ></el-input>
                            </el-form-item>
                            <el-form-item
                              label="数量"
                              v-if="setting_nav.status == 1"
                              label-width="50px"
                              class="img-input"
                            >
                              <el-radio-group
                                v-model="row.number_type"
                                size="mini"
                                class="radio_small"
                              >
                                <el-radio
                                  style="font-size: 12px"
                                  disabled
                                  v-for="r1 in row.number_type_option"
                                  :key="r1.value"
                                  :label="+r1.value"
                                  >{{ r1.name }}</el-radio
                                >
                              </el-radio-group>
                              <div>
                                <el-input
                                  v-model="row.number"
                                  disabled
                                  size="mini"
                                  placeholder="请输入数量"
                                  v-if="row.number_type == 2"
                                  class="w100"
                                ></el-input>
                                <el-select
                                  size="mini"
                                  v-else
                                  class="w100"
                                  v-model="row.number_select"
                                  disabled
                                  @change="
                                    handleOptionChange(row.number_select, row)
                                  "
                                >
                                  <el-option
                                    v-for="r2 in row.number_select_option"
                                    :key="r2.value"
                                    :label="r2.name"
                                    :value="+r2.value"
                                  >
                                  </el-option>
                                </el-select>
                                <!-- 选择器 -->
                                <div style="margin-top: 20px">
                                  <el-cascader
                                    disabled
                                    v-model="row.list"
                                    @change="handleChange(row)"
                                    :options="optionsList[row.number_select]"
                                    clearable
                                    :props="{ checkStrictly: true }"
                                  ></el-cascader>
                                </div>
                              </div>
                            </el-form-item>
                            <el-form-item
                              label="状态"
                              label-width="50px"
                              class="img-input"
                            >
                              <el-radio-group v-model="row.status">
                                <el-radio :label="1" style="margin-right: 10px"
                                  >显示</el-radio
                                >
                                <el-radio :label="0">隐藏</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </li>
                        </ul>
                      </el-form>
                    </el-form-item>
                    <!-- <el-button
                        type="primary"
                        size="mini"
                        v-if="
                          douyinIndexParam.nav.is_show &&
                          douyinIndexParam.nav.setting == 0
                        "
                        style="margin-left: 80px"
                        @click="openDouyinIndexMenu"
                        >去设置</el-button
                      > -->
                  </el-form>
                </template>
                <template v-if="sub.name == 'xinxi_setting'">
                  <el-form label-width="80px" size="mini">
                    <el-form-item
                      :label="child.title"
                      v-for="(child, childi) in setting_xinxi.value"
                      :key="childi"
                    >
                      <el-radio-group
                        v-model="child.status"
                        style="margin-right: 15px"
                      >
                        <el-radio
                          v-for="child_xin in child.status_option"
                          :key="child_xin.value"
                          :label="+child_xin.value"
                          >{{ child_xin.name }}</el-radio
                        >
                        <!-- <el-radio :label="0">关闭</el-radio> -->
                      </el-radio-group>
                      <span class="order" style="margin-right: 8px">排序</span>
                      <span class="order-inp" style="margin-right: 15px">
                        <el-input
                          v-model="child.sort"
                          type="number"
                          min="0"
                          style="width: 60px"
                        >
                        </el-input>
                      </span>

                      <span class="order" style="margin-right: 8px"
                        >自定义</span
                      >
                      <span class="order-inp" style="margin-right: 15px">
                        <el-input
                          v-model="child.title_set"
                          placeholder="自定义名称"
                          style="width: 120px"
                        >
                        </el-input>
                      </span>
                    </el-form-item>
                  </el-form>
                </template>
                <template v-if="sub.name == 'member_menu_setting'">
                  <el-form-item
                    label-width="0"
                    v-if="setting_member_menu.status"
                  >
                    <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                    <el-form size="mini">
                      <ul>
                        <li
                          style="
                            display: inline-block;
                            margin-right: 15px;
                            margin-bottom: 10px;
                          "
                          v-for="(row, rowi) in setting_member_menu.value"
                          :key="rowi"
                        >
                          <el-upload
                            action="/api/common/file/upload/admin?category=3"
                            list-type="picture-card"
                            :headers="myHeader"
                            disabled
                            :show-file-list="false"
                            :on-preview="handlePictureCardPreview"
                            :on-success="(e) => handleUploadSuccess(e, row, 1)"
                          >
                            <img
                              v-if="row.bg_pic"
                              :src="row.bg_pic"
                              class="up_image"
                            />
                            <i v-else class="el-icon-plus"></i>
                          </el-upload>
                          <el-form-item
                            label=""
                            label-width="50px"
                            class="img-input"
                          >
                            {{ row.title }}
                          </el-form-item>
                          <!-- 状态 -->
                          <el-form-item
                            label="状态"
                            label-width="50px"
                            class="img-input"
                          >
                            <el-radio-group v-model="row.status">
                              <el-radio
                                v-for="r in row.status_option"
                                :key="r.value"
                                :label="+r.value"
                                style="margin-right: 10px"
                                >{{ r.name }}</el-radio
                              >
                              <!-- <el-radio :label="0">隐藏</el-radio> -->
                            </el-radio-group>
                          </el-form-item>
                        </li>
                      </ul>
                    </el-form>
                  </el-form-item>
                </template>
                <template v-if="sub.name == 'new_house_menu_setting'">
                  <el-form-item
                    label-width="0"
                    v-if="new_house_menu_setting.status"
                  >
                    <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                    <el-form size="mini">
                      <ul>
                        <li
                          style="
                            display: inline-block;
                            margin-right: 15px;
                            margin-bottom: 10px;
                          "
                        >
                          <el-form-item
                            :label="row.title"
                            label-width="120px"
                            class="img-input"
                            v-for="(row, rowi) in new_house_menu_setting.value"
                            :key="rowi"
                          >
                            <el-radio-group
                              v-model="row.status"
                              @change="
                                ($event) => {
                                  new_house_radio_change($event, row, rowi);
                                }
                              "
                            >
                              <el-radio
                                v-for="r in row.status_option"
                                :key="r.value"
                                :label="+r.value"
                                style="margin-right: 10px"
                                >{{ r.name }}</el-radio
                              >
                            </el-radio-group>
                          </el-form-item>
                        </li>
                      </ul>
                    </el-form>
                  </el-form-item>
                </template>
                <template v-if="sub.name == 'nav_menu_setting'">
                  <el-form label-width="80px" size="mini">
                    <!-- <el-form-item label-width="0" label="">
                          <el-radio-group
                            v-model="setting_nav.status"
                            @change="radioChange"
                          >
                            <el-radio
                              v-for="nav_setting in setting_nav.value"
                              :key="nav_setting.value"
                              :label="nav_setting.value"
                              >{{ nav_setting.title }}</el-radio
                            >
                          </el-radio-group>
                        </el-form-item> -->

                    <el-form-item label-width="0">
                      <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                      <el-form size="mini">
                        <ul>
                          <li
                            style="
                              display: inline-block;
                              margin-right: 15px;
                              margin-bottom: 10px;
                            "
                            v-for="(row, rowi) in nav_menu_setting.value"
                            :key="rowi"
                          >
                            <el-upload
                              action="/api/common/file/upload/admin?category=3"
                              list-type="picture-card"
                              style="margin-bottom: 10px"
                              :headers="myHeader"
                              :show-file-list="false"
                              disabled
                              :on-preview="handlePictureCardPreview"
                              :on-success="
                                (e) => handleUploadSuccess(e, row, 1)
                              "
                            >
                              <img
                                v-if="row.bg_pic"
                                :src="row.bg_pic"
                                class="up_image"
                              />
                              <i v-else class="el-icon-plus"></i>
                            </el-upload>
                            <el-form-item
                              label="标题"
                              label-width="50px"
                              class="img-input"
                              :rules="{
                                required: true,
                                message: '请填写标题',
                                trigger: 'blur',
                              }"
                            >
                              <el-input
                                v-model="row.title"
                                size="mini"
                                class="w100"
                              ></el-input>
                            </el-form-item>
                            <el-form-item
                              label="链接"
                              label-width="50px"
                              class="img-input"
                              :rules="{
                                required: true,
                                message: '请填写链接',
                                trigger: 'blur',
                              }"
                            >
                              <el-input
                                v-model="row.link"
                                size="mini"
                                disabled
                                class="w100"
                              ></el-input>
                            </el-form-item>
                            <el-form-item
                              label="状态"
                              label-width="50px"
                              class="img-input"
                            >
                              <el-radio-group v-model="row.status">
                                <el-radio :label="1" style="margin-right: 10px"
                                  >显示</el-radio
                                >
                                <el-radio :label="0">隐藏</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </li>
                        </ul>
                      </el-form>
                    </el-form-item>
                    <!-- <el-button
                        type="primary"
                        size="mini"
                        v-if="
                          douyinIndexParam.nav.is_show &&
                          douyinIndexParam.nav.setting == 0
                        "
                        style="margin-left: 80px"
                        @click="openDouyinIndexMenu"
                        >去设置</el-button
                      > -->
                  </el-form>
                </template>
                <template v-if="sub.name == 'broker_menu_setting'">
                  <el-form-item label-width="0">
                    <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                    <el-form size="mini">
                      <ul>
                        <li
                          style="
                            display: inline-block;
                            margin-right: 15px;
                            margin-bottom: 10px;
                          "
                        >
                          <el-form-item
                            :label="row.title"
                            label-width="120px"
                            class="img-input"
                            v-for="(row, rowi) in broker_menu_setting.value"
                            :key="rowi"
                          >
                            <el-radio-group
                              v-model="row.status"
                              @change="
                                ($event) => {
                                  broker_menu_radio_change($event, row, rowi);
                                }
                              "
                            >
                              <el-radio
                                v-for="r in row.status_option"
                                :key="r.value"
                                :label="+r.value"
                                style="margin-right: 10px"
                                >{{ r.name }}</el-radio
                              >
                            </el-radio-group>
                          </el-form-item>
                        </li>
                      </ul>
                    </el-form>
                  </el-form-item>
                </template>
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div class="btns">
          <div class="submit" @click="submitDouyinIndexData">提交修改</div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import config from "@/utils/config";
export default {
  components: {},
  data() {
    return {
      link: "",
      show_dialog_list: false,
      inframe: "",
      website_id: "",
      activeName: "setting",
      douyinIndexParam: {
        app_name: "",
        nav_setting: {
          selected: 0,
        },
      },
      setting: {},
      active_name: "global",
      setting_member_menu: {
        value: [],
        status: 1,
      },
      setting_xinxi: {
        value: [],
        status: 1,
      },
      setting_nav: {
        value: [],
        status: 1,
      },
      new_house_menu_setting: {},
      ueditor: {
        // agreement: {
        //   initialFrameWidth: "100%",
        //   autoHeightEnabled: false,
        //   initialFrameHeight: 500, // 高度
        // },
        // aprivacy: {
        //   initialFrameWidth: "100%",
        //   autoHeightEnabled: false,
        //   initialFrameHeight: 500, // 高度
        // }
        // ,
        config: {
          initialFrameWidth: "100%",
          autoHeightEnabled: false,
          initialFrameHeight: 500, // 高度
        },
      },
      typeList: [
        {
          name: "T+ERP版",
          value: 1,
        },
        {
          name: "T+新房版",
          value: 2,
        },
      ],
      mini_version: 1,
      nav_menu_setting: {
        value: [],
        status: 1,
      },
      broker_menu_setting: {
        value: [],
        status: 1,
      },
      setting_company_form: {
        app_company: "",
        app_address: "",
        app_email: "",
      },
      douyin_user_params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      tableData: [],
      listHandler: [],
      values: [],
      optionsList: {},
      showCascader: false,
      cascaderOptions: [],
      listHand: [],
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
    configagreement() {
      return {};
    },
    configaprivacy() {
      return {
        initialFrameWidth: "100%",
        autoHeightEnabled: false,
        initialFrameHeight: 500, // 高度
      };
    },
    configperson_protection() {
      return {
        initialFrameWidth: "100%",
        autoHeightEnabled: false,
        initialFrameHeight: 500, // 高度
      };
    },
    website_info() {
      return this.$store.state.website_info;
    },
  },
  mounted() {
    let website_id =
      this.$route.query.website_id || localStorage.getItem("website_id");
    this.website_id = website_id;

    this.getSetting();
  },
  methods: {
    getCompanySetting() {
      let params = {
        mini_version: 3,
      };
      // if (this.website_info.open_mini_build_program == 1) {
      //   params.mini_version = 4
      // }
      console.log("4444", params);
      this.$http.getTtCompanySetting(params).then((res) => {
        if (res.status == 200) {
          console.log(res);
          if (!Array.isArray(res.data)) {
            this.setting_company_form = res.data;
          }
        }
      });
    },
    saveCompanyInfo() {
      let obj = {
        app_email: "请填写邮箱",
        app_address: "请填写公司地址",
        app_company: "请填写公司名称",
      };
      let params = Object.assign({}, this.setting_company_form);
      for (const key in params) {
        if (!params[key]) {
          this.$message.warning(obj[key]);
          return;
        }
      }

      this.$http.saveTtCompanySetting(params).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message || "保存成功");
        }
      });
    },
    // 下拉菜单
    handleOptionChange(row, value) {
      console.log(row, "row");
      console.log(value, "value");
      if (value.list.length) {
      }
      let optionsTemp = [];
      if (row !== 5) {
        this.$http.getTtSettingList1().then((res) => {
          console.log(res, "房源");
          if (res.status == 200) {
            this.$set(
              this.optionsList,
              row,
              Object.values(res.data).map((option) => {
                return {
                  ...option, // 复制原始对象的其他属性
                  value: option.key, // 使用 key 字段的值作为 value 的值
                };
              })
            );

            console.log(this.optionsList, "78");
          }
        });
      } else {
        this.$http.getTtSettingList().then((res) => {
          console.log(res, "楼盘");
          if (res.status == 200) {
            this.$set(
              this.optionsList,
              row,
              Object.values(res.data).map((option) => {
                return {
                  ...option, // 复制原始对象的其他属性
                  value: option.key, // 使用 key 字段的值作为 value 的值
                };
              })
            );

            console.log(this.optionsList, "89");
          }
        });
      }
    },
    handleChange(value) {
      console.log(value, "valiu2321");
      value.parameter_key = value.list[0];
      value.parameter_select = value.list[value.list.length - 1];
      console.log(value, "value123");
      return;
      // ---------------------------------------------
      // 兄弟这段代码别骂我为什么不加注释  因为我也不懂  纯纯科技与狠货  能用就行
      function findAndStoreMatchingItems(currentNode, list, index, results) {
        if (index >= list.length) {
          return;
        }

        const listItem = list[index];
        const matchedChild = currentNode.children.find(
          (child) => child.value === listItem
        );

        if (matchedChild) {
          results.push({ level: index + 1, node: matchedChild });
          findAndStoreMatchingItems(matchedChild, list, index + 1, results);
        }
      }

      const results = [];
      findAndStoreMatchingItems(
        { children: this.optionsList },
        value.list,
        0,
        results
      );
      for (let i in results) {
        // if (results[i].node.children) {
        //   delete results[i].node.children;
        //   results[i] = Object.assign(results[i], results[i].node);
        //   delete results[i].node;
        // } else {
        //   results[i] = Object.assign(results[i], results[i].node);
        //   delete results[i].node;
        // }
        if (results[results.length - 1].node) {
          results[i] = Object.assign(results[i], results[i].node);
          delete results[i].node;
        }
      }
      value.listHandler = results[results.length - 1];
    },
    getSetting() {
      let params = {
        mini_version: 3,
      };
      console.log(this.website_info, "this.website_info");
      if (this.website_info.open_mini_build_program_wx == 1) {
        console.log(111111);
        params.mini_version = 2;
      }
      this.$http.getTtSetting(params).then((res) => {
        if (res.status == 200) {
          console.log(this.website_info.open_mini_erp_program_wx, "在这");
          if (this.website_info.open_mini_erp_program_wx == 1) {
            this.setting_nav = res.data.nav?.sub.nav_setting || {};
            this.setting_nav.value = this.setting_nav.value.filter(
              (item) => item.title !== "风格2"
            );
            if (this.setting_nav && this.setting_nav.value) {
              this.setting_nav.value.map((item) => {
                if (item.selected == 1) {
                  this.setting_nav.status = item.value;
                }
              });
            }
          }

          console.log(this.setting_nav, "this.setting_nav1234");
          let list = this.setting_nav.value;
          let ogbItem = {};
          for (let is in list) {
            if (list[is].title == "风格1") {
              ogbItem = list[is];
            }
          }
          this.cascaderOptions = ogbItem.children;
          if (this.setting_nav.value.children) {
          }
          if (
            this.website_info.open_mini_build_program_wx == 1 &&
            res.data.nav_menu
          ) {
            this.nav_menu_setting = res.data.nav_menu.sub.nav_menu_setting;
            this.nav_menu_setting.status =
              res.data.nav_menu?.sub.nav_menu_status?.value;
          }
          if (res.data.broker_menu) {
            this.broker_menu_setting =
              res.data.broker_menu.sub?.broker_menu_setting;
            // this.broker_menu_setting.status = res.data.nav_menu?.sub?.nav_menu_status?.value
          }

          this.setting_xinxi = res.data.xinxi?.sub.xinxi_setting || {};
          this.setting_member_menu =
            res.data.member_menu?.sub.member_menu_setting || {};
          // this.new_house_menu_setting = res.data.new_house_menu.sub.new_house_menu_setting
          this.setting = res.data;
          this.setParams(this.setting);
          this.listHandlerList();
        }
      });
    },
    listHandlerList() {
      const requests = [];
      let request;
      this.cascaderOptions.forEach((item, index) => {
        index++;
        if (item.number_select < 5) {
          request = this.$http.getTtSettingList1();
        } else {
          request = this.$http.getTtSettingList();
        }
        requests.push(request);
      });
      Promise.all(requests).then((res) => {
        console.log(res, "请求了123");
        this.listHand = res;
        this.listHand.forEach((item1, index1) => {
          index1++;
          this.$set(
            this.optionsList,
            index1,
            Object.values(item1.data).map((option) => {
              return {
                ...option, // 复制原始对象的其他属性
                value: option.key, // 使用 key 字段的值作为 value 的值
              };
            })
          );
        });
      });
      console.log(this.optionsList, "this.optionsList3555");

      // this.$set(
      //   this.optionsList,
      //   row,
      //   Object.values(res.data).map((option) => {
      //     return {
      //       ...option, // 复制原始对象的其他属性
      //       value: option.key, // 使用 key 字段的值作为 value 的值
      //     };
      //   })
      // );
    },
    setParams(setting) {
      for (const key in setting) {
        if (setting[key].sub) {
          for (const j in setting[key].sub) {
            if (typeof setting[key].sub[j]["value"] == "number") {
              setting[key].sub[j]["value"] = setting[key].sub[j]["value"] + "";
            }
            this.$set(
              this.douyinIndexParam,
              setting[key].sub[j]["name"],
              setting[key].sub[j]["value"]
            );
          }
        }
      }
    },
    // handleClick(e) {
    //   if (e.name == 'tt_setting') {
    //     this.getSetting()
    //   } else if (e.name == 'setting') {
    //     this.getCompanySetting()
    //   } else if (e.name == 'user') {
    //     this.getTableData()
    //   }
    // },
    onPageChange(e) {
      this.douyin_user_params.page = e;
      this.getTableData();
    },
    getTableData() {
      let params = Object.assign({}, this.douyin_user_params);
      this.$http.getDouyinUser(params).then((res) => {
        if (res.status == 200) {
          this.tableData = res.data.data;
          this.total = res.data.total;
          // this.$message.success('保存成功')
        }
      });
    },
    submitDouyinIndexData() {
      let params = Object.assign({}, this.douyinIndexParam);
      if (this.website_info.open_mini_erp_program_wx == 1) {
        params.nav_setting = Object.assign([], this.setting_nav.value);
        params.nav_menu_setting = Object.assign(
          [],
          this.nav_menu_setting.value
        );
        params.xinxi_setting = Object.assign([], this.setting_xinxi.value);
        params.member_menu_setting = Object.assign(
          [],
          this.setting_member_menu.value
        );
        params.broker_menu_setting = Object.assign(
          [],
          this.broker_menu_setting.value
        );
        if (this.setting_nav.status == 1) {
          params.nav_setting[this.setting_nav.status - 1].selected = 1;
          if (params.nav_setting.length > 1) {
            params.nav_setting[this.setting_nav.status].selected = 0;
          }
        }
        if (this.setting_nav.status == 2) {
          params.nav_setting[this.setting_nav.status - 1].selected = 1;
          params.nav_setting[0].selected = 0;
        }
      }

      // params.new_house_menu_setting = Object.assign([], this.new_house_menu_setting.value)
      params.mini_version = 3;
      if (this.website_info.open_mini_build_program_wx == 1) {
        console.log(33333);
        params.mini_version = 4;
      }
      this.$http.saveTtSetting(params).then((res) => {
        if (res.status == 200) {
          this.$message.success("保存成功");
        }
      });
    },
    handlePictureCardPreview(e) {
      console.log(e);
    },
    beforeUpload() {},
    handleUploadSuccess(e, type, cate) {
      console.log(e, type, cate);
      if (cate) {
        this.$set(type, "bg_pic", e.url);
      } else {
        this.douyinIndexParam[type] = e.url;
      }
    },
    inputUe(e, type) {
      this.douyinIndexParam[type] = e.content;
    },
    radioChange() {
      this.$forceUpdate();
    },
    new_house_radio_change(e, type) {
      let other = {};
      if (type.name == "zxzx") {
        other = this.new_house_menu_setting.value.find(
          (item) => item.name == "yykf"
        );
      }
      if (type.name == "yykf") {
        other = this.new_house_menu_setting.value.find(
          (item) => item.name == "zxzx"
        );
      }
      if (type.name == "zxzx" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个");
        type.status = 0;
        this.$forceUpdate();
      }
      if (type.name == "yykf" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个");
        type.status = 0;
        this.$forceUpdate();
      }
    },
    broker_menu_radio_change(e, type) {
      console.log(type);
      let other = {};
      if (type.identify == "zxzx") {
        other = this.broker_menu_setting.value.find(
          (item) => item.identify == "yykf"
        );
      }
      if (type.identify == "yykf") {
        other = this.broker_menu_setting.value.find(
          (item) => item.identify == "zxzx"
        );
      }
      if (type.identify == "zxzx" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个");
        type.status = 0;
        this.$forceUpdate();
      }
      if (type.identify == "yykf" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个");
        type.status = 0;
        this.$forceUpdate();
      }
    },
  },
};
</script>

<style lang ="scss" scoped>
.el-upload {
  overflow: hidden;
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.img-input {
  white-space: nowrap;
}
.w100 {
  width: 100px;
}
.w240 {
  width: 240px;
}
.radio_small {
  ::v-deep .el-radio__label {
    font-size: 12px;
  }
}
.btns {
  text-align: center;
  position: fixed;
  top: 500px;
  right: 80px;
  z-index: 1000;
  .submit {
    padding: 10px 40px;
    background: #ff5b6a;
    color: #fff;
    cursor: pointer;
    border-radius: 40px;
  }
}
</style>