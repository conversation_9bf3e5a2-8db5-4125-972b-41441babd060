<template>
  <div class="add">
    <div class="tips">
      <div>1.一个二维码可对应多名接待员工，客户扫码随机均匀分配</div>
      <div>
        2.添加客户后欢迎语仅能发送一次，请勿在企业后台设置，企业后台设置后，此处的活码欢迎将
        不会发送
      </div>
    </div>
    <el-form label-width="100px">
      <div class="title">基本信息</div>

      <el-form-item label="活码名称">
        <div class="form-item-block">
          <el-input placeholder="请输入活码名称" v-model="form_params.name" style="width: 240px; margin-right: 12px">
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="使用员工">
        <div class="form-item-block flex-row align-center flex-wrap">
          <el-button size="big" class="member_button" v-for="item in form_selectedMember" :key="item.id">{{ item.name ||
                      item.user_name }}</el-button>
          <el-button size="big" class="el-icon-plus" @click="showAddMember">添加成员</el-button>
        </div>
      </el-form-item>
      <el-form-item label="企业客户标签">
        <div class="form-item-block align-center flex-wrap">
          <el-switch v-model="form_params.add_tag" active-color="#2D84FB">
          </el-switch>
        </div>
        <div class="tip">为扫码后添加员工好友的企微客户打上标签</div>
      </el-form-item>

      <el-form-item label="选择标签" v-if="form_params.add_tag">
        <div class="form-item-block">
          <el-cascader :options="tagList" v-model="form_params.tag_ids" style="width: 240px; margin-right: 12px"
            :show-all-levels="false" @change="changTags" :clearable="true" :props="{
                          emitPath: false,
                          value: 'id',
                          label: 'name',
                          children: 'taggroup',
                          multiple: true,
                        }"></el-cascader>
        </div>
      </el-form-item>
      <el-form-item label="好友验证">
        <div class="form-item-block line_height1">
          <el-switch v-model="form_params.open_check" active-color="#2D84FB">
          </el-switch>
        </div>
        <div class="tip">
          开启后，客户添加员工好友时会提交好友验证，员工通过后添加成功
        </div>
      </el-form-item>

      <div class="title">欢迎语</div>
      <el-form-item label="欢迎语">
        <welcome-mes style="padding-right: 20px" ref="member_qrcode_edit" :value="welcome_mes"></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
    <el-dialog :visible.sync="show_select_dia" width="600px" title="选择附件" append-to-body>
      <div class="imgLists">
        <div class="img_list flex-row" @scroll="handleScroll">
          <template v-if="params_type == 2 || params_type == 3 || params_type == 4">
            <div class="img" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <img :src="item.url" alt="" />
            </div>
          </template>
          <template v-if="params_type == 5">
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <video :src="item.url"></video>
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
          <template v-if="params_type == 6">
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_select_dia = false">取消</el-button>
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_add_member" width="600px" title="选择成员" append-to-body>
      <div class="member">
        <div class="member_box flex-row">
          <div class="left member_con flex-1">
            <memberList v-if="show_add_member" :list="memberList" :defaultValue="selectedIds"
              @onClickItem="selecetedMember" ref="memberList">
            </memberList>
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择成员</div>
            <div class="selected_list">
              <div class="selected_item flex-row align-center" v-for="item in selectedList" :key="item.id">
                <!-- <div class="prelogo">
                    <img :src="item.prelogo" alt="">
                  </div> -->
                <div class="name flex-1">{{ item.name || item.user_name }}</div>
                <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false">取消</el-button>
          <el-button type="primary" @click="selectMemberOk" :loading="isSubmiting">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import memberList from "../memberList";
import welcomeMes from "../welcome_mes";
import config from "@/utils/config";
export default {
  components: { memberList, welcomeMes },
  props: {
    form: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      form_params: {
        // open_check: true
      },
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      cover: "",
      imgList: [],
      show_select_dia: false,
      currentImg: "",
      form_selectedLabels: [],
      params_type: 2,
      filename: "",
      videoname: "",
      imgname: "",
      params_type_arr: [],
      img_params: {
        page: 1,
        per_page: 10,
        type: 1,
      },
      type_arr: ["text", "image", "link", "miniprogram", "video", "file"],
      tagList: []
    };
  },

  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  created() {
    this.getDepartment();
    for (const key in this.form) {
      if (key == "open_check" || key == 'add_tag') {
        this[key] = this.form[key]
      } else {
        this.form_params[key] = this.form[key];
      }
    }
    this.getTags()
    this.selectedIds = [];
    this.form_selectedMember = this.form_params.user;
    this.selectedList = this.form_params.user;
    // this.selectedIds =
    this.form_params.user.map((item) => {
      this.selectedIds.push(item.id);
    });
    if (this.open_check == 1) {
      this.$set(this.form_params, "open_check", true)
      // this.form_params.open_check = true;
    } else {
      this.$set(this.form_params, "open_check", false)
      // this.form_params.open_check = false;
    }
    if (this.add_tag == 1) {
      this.$set(this.form_params, "add_tag", true)
      // this.form_params.add_tag = true;
    } else {
      this.$set(this.form_params, "add_tag", false)
      // this.form_params.add_tag = false;
    }
    for (const key in this.form_params.welcome_msg) {
      this.$set(
        this.welcome_mes,
        key,
        JSON.parse(JSON.stringify(this.form_params.welcome_msg[key]))
      );
      // this.welcome_mes[key] = JSON.parse(JSON.stringify(this.form_params.welcome_msg))
    }
    if (this.form_params.welcome_msg.image) {

      delete this.welcome_mes.image.org_url;
      this.params_type_arr.push(this.form_params.welcome_msg.image.type);
    }
    if (this.form_params.welcome_msg.file) {
      this.filename = this.form_params.welcome_msg.file.org_url;
      delete this.welcome_mes.file.org_url;
      this.params_type_arr.push(this.form_params.welcome_msg.file.type);
    }
    if (this.form_params.welcome_msg.video) {
      this.videoname = this.form_params.welcome_msg.video.org_url;
      delete this.form_params.welcome_msg.video.org_url;
      delete this.welcome_mes.video.org_url;
      this.params_type_arr.push(this.form_params.welcome_msg.video.type);
    }
    if (this.form_params.welcome_msg.miniprogram) {
      this.welcome_mes.miniprogram.pic_url = this.form_params.welcome_msg.miniprogram.org_url;
      delete this.form_params.welcome_msg.miniprogram.org_url
      delete this.welcome_mes.miniprogram.org_url;
      this.params_type_arr.push(this.form_params.welcome_msg.miniprogram.type);
    }
    console.log(this.welcome_msg);
    if (this.form_params.welcome_msg.link) {
      this.params_type_arr.push(this.form_params.welcome_msg.link.type);
      delete this.welcome_mes.link.org_url;
    }
    this.params_type_arr_old = [];
    this.params_type_arr.map((item) => {
      this.params_type_arr_old.push(this.type_arr[item - 1]);
    });
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      if (params.open_check) {
        params.open_check = 1;
      } else {
        params.open_check = 0;
      }
      if (params.add_tag) {
        params.add_tag = 1;
      } else {
        params.add_tag = 0;
      }
      params.tag_ids = params.tag_ids.join(",")
      // let type_arr = [];
      // this.params_type_arr.map((item) => {
      //   type_arr.push(this.type_arr[item - 1]);
      // });
      let oldTypeArr = Object.keys(this.form_params.welcome_msg)
      let _welcome_mes = Object.assign({}, this.$refs.member_qrcode_edit.welcome_mes);
      for (const key in _welcome_mes) {
        if (_welcome_mes[key].org_url) {
          delete _welcome_mes[key].org_url
        }
      }
      // this.params_type_arr_old.map((item) => {
      //   if (!type_arr.includes(item)) {
      //     _welcome_mes[item].is_del = 1;
      //   }
      //   return item
      // });
      console.log(_welcome_mes);
      if (
        _welcome_mes.image &&
        !_welcome_mes.image.media_id
      ) {
        if (oldTypeArr.includes("image")) {
          _welcome_mes.image = this.form_params.welcome_msg.image
          _welcome_mes.image.is_del = 1
          delete _welcome_mes.image.name
        } else {
          delete _welcome_mes.image;
        }

      }
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key] && Object.keys(linkArr).includes(key)) {
            emptyLink.push(key);
          }
        }

        if (emptyLink.length == Object.keys(linkArr).length) {
          if (oldTypeArr.includes("link")) {
            _welcome_mes.link = this.form_params.welcome_msg.link
            delete _welcome_mes.link.appid
            _welcome_mes.link.is_del = 1

          } else {
            emptyLink.length = 0;
            delete _welcome_mes.link;
          }

          // emptyLink.length = 0;
          // delete _welcome_mes.link;
        } else if (emptyLink.length) {
          if (!oldTypeArr.includes("link")) {
            this.$message.warning(linkArr[emptyLink[0]]);
            return;
          }

        }
      }

      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (
            !_welcome_mes.miniprogram[key] && Object.keys(miniArr).includes(key)
          ) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          if (oldTypeArr.includes("miniprogram")) {
            _welcome_mes.miniprogram = this.form_params.welcome_msg.miniprogram
            _welcome_mes.miniprogram.is_del = 1
          } else {
            emptyMini.length = 0;
            delete _welcome_mes.miniprogram;
          }

        } else if (emptyMini.length) {
          if (!oldTypeArr.includes("miniprogram")) {
            this.$message.warning(miniArr[emptyMini[0]]);
            return;
          }
        }
      }

      if (
        _welcome_mes.video &&
        !_welcome_mes.video.media_id
      ) {
        if (oldTypeArr.includes("video")) {
          _welcome_mes.video = this.form_params.welcome_msg.video
          _welcome_mes.video.is_del = 1
        } else {
          delete _welcome_mes.video;
        }

      }
      if (
        _welcome_mes.file &&
        !_welcome_mes.file.media_id
      ) {
        if (oldTypeArr.includes("file")) {
          _welcome_mes.file = this.form_params.welcome_msg.file
          _welcome_mes.file.is_del = 1
        } else {
          delete _welcome_mes.file;
        }
      }

      // if (!_welcome_mes.text.desc) {
      //   this.$message.warning("欢迎语1不能为空");
      //   return;
      // }

      // if (
      //   _welcome_mes.image &&
      //   (!_welcome_mes.image.media_id) &&
      //   (!_welcome_mes.image.is_del)
      // ) {
      //   delete _welcome_mes.image;
      // }
      // if (_welcome_mes.link) {
      //   let linkArr = {
      //     title: "链接标题不能为空",
      //     pic_url: "链接封面不能为空",
      //     desc: "链接描述不能为空",
      //     url: "跳转链接不能为空",
      //   };
      //   let emptyLink = [];
      //   for (const key in _welcome_mes.link) {
      //     if (!_welcome_mes.link[key] && _welcome_mes.link.is_del != 1) {
      //       emptyLink.push(key);
      //     }
      //   }
      //   if (emptyLink.length == Object.keys(linkArr).length) {
      //     emptyLink.length = 0;
      //     delete _welcome_mes.link;
      //   } else if (emptyLink.length) {
      //     this.$message.warning(linkArr[emptyLink[0]]);
      //     return;
      //   }
      // }

      // if (_welcome_mes.miniprogram) {
      //   let miniArr = {
      //     title: "小程序标题不能为空",
      //     media_id: "小程序封面不能为空",
      //     appid: "小程序appid不能为空",
      //     url: "小程序链接不能为空",
      //   };
      //   let emptyMini = [];
      //   for (const key in _welcome_mes.miniprogram) {
      //     if (
      //       !_welcome_mes.miniprogram[key] &&
      //       _welcome_mes.miniprogram.is_del != 1
      //     ) {
      //       emptyMini.push(key);
      //     }
      //   }
      //   if (emptyMini.length == Object.keys(miniArr).length) {
      //     emptyMini.length = 0;
      //     delete _welcome_mes.miniprogram;
      //   } else if (emptyMini.length) {
      //     this.$message.warning(miniArr[emptyMini[0]]);
      //     return;
      //   }
      // }
      // if (
      //   _welcome_mes.video &&
      //   !_welcome_mes.video.media_id &&
      //   !_welcome_mes.video.is_del
      // ) {
      //   delete _welcome_mes.video;
      // }
      // if (
      //   _welcome_mes.file &&
      //   !_welcome_mes.file.media_id &&
      //   !_welcome_mes.file.is_del
      // ) {
      //   delete _welcome_mes.file;
      // }
      delete params.user

      params.welcome_msg = JSON.stringify(_welcome_mes);
      if (this.isSubmiting) return;
      this.isSubmiting = true;

      this.$http
        .editCrmMemberQrcode(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    cancel() {
      this.$emit("cancel");
    },
    showAddLabels() {
      this.show_add_labels = true;
    },
    showSelectDia() {
      this.img_params.page = 1;
      this.getImgList();
      this.show_select_dia = true;
    },
    onChangemediaType(e) {
      var category = config.CATEGORY_IM_IMAGE;
      switch (e) {
        case 2:
        case 3:
        case 4:
          category = config.CATEGORY_IM_IMAGE;
          break;
        case 5:
          category = config.CATEGORY_IM_VIDEO;
          break;
        case 6:
          category = config.CATEGORY_IM_FILE;
          break;
        default:
          break;
      }
      this.website_img = `/api/common/file/upload/admin?category=${category}`;
    },
    // 选中图片
    selectAvatar(e) {
      this.currentImg = e.url;
      // this.imgList.map(item => item.checked = false)
      // e.checked = true
    },
    // 选中图片确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      switch (this.params_type) {
        case 2:
          // 图片
          this.$set(this.welcome_mes.image, "media_id", current.media_id)
          // this.welcome_mes.image.media_id = current.media_id;
          this.imgname = current.url;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = current.url;
          // this.welcome_mes.link.pic_url = url;
          // this.welcome_mes.link.media_id = current.media_id;
          this.$set(this.welcome_mes.link, "media_id", current.media_id)
          break;
        case 4:
          // 小程序 miniprogram   media_id
          this.miniCover = current.url;
          // this.welcome_mes.miniprogram.pic_url = url;
          this.$set(this.welcome_mes.miniprogram, "media_id", current.media_id)
          break;
        case 5:
          // 视频
          this.videoname = current.url;
          console.log(this.welcome_mes);
          this.$set(this.welcome_mes.video, "media_id", current.media_id)
          console.log(this.welcome_mes, 111);
          // this.welcome_mes.video.media_id = current.media_id;
          break;
        case 6:
          // 文件
          this.filename = current.url;
          this.$set(this.welcome_mes.file, "media_id", current.media_id)
          // this.welcome_mes.file.media_id = current.media_id;
          break;

        default:
          break;
      }
      this.show_select_dia = false;
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      switch (this.params_type) {
        case 2:
        case 3:
        case 4:
          this.img_params.type = 1;
          break;
        case 5:
          this.img_params.type = 3;
          break;
        case 6:
          this.img_params.type = 4;
          break;

        default:
          break;
      }
      this.loadMore = false
      this.$http.getCrmServiceAvatarList(this.img_params).then((res) => {
        if (res.status == 200) {
          if (this.img_params.type == 4 || this.img_params.type == 3) {
            res.data.data.map((item) => {
              item.user_name = item.url.substring(
                item.url.lastIndexOf("/") + 1
              );
              return item;
            });
          }
          this.imgList = this.imgList.concat(res.data.data);
          if (res.data.data.length == this.img_params.per_page) {
            this.loadMore = true;
          } else {
            this.loadMore = false;
          }
        }
      }).catch(() => {
        this.loadMore = false
      });
    },
    checkChange(e, type) {
      console.log(e, type);
      if (e) {
        this.params_type = type;
        var category = config.CATEGORY_IM_IMAGE;
        switch (type) {
          case 2:
          case 3:
          case 4:
            category = config.CATEGORY_IM_IMAGE;
            break;
          case 5:
            category = config.CATEGORY_IM_VIDEO;
            break;
          case 6:
            category = config.CATEGORY_IM_FILE;
            break;
          default:
            break;
        }
        this.website_img = `/api/common/file/upload/admin?category=${category}`;
      }
    },

    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.params_type) {
        case 2:
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败"));
          this.imgname = url;
          this.welcome_mes.image.media_id = res.data.media_id;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = url;
          // this.welcome_mes.link.media_id = res.data.media_id;
          break;
        case 4:
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败"));
          // 小程序 miniprogram   media_id
          this.welcome_mes.miniprogram.pic_url = url;
          this.welcome_mes.miniprogram.media_id = res.data.media_id;
          break;
        case 5:
          // 视频
          params.type = 3;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败"));
          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case 6:
          // 文件
          params.type = 4;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败"));
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
    },
    // getImgId(url) {
    //   let params = { url }
    //   this.$http.getAvatarId(params).then(res => {
    //     if (res.status == 200) {
    //       this.form_params.media_id = res.data.mediaid
    //     }
    //   })
    // },
    // 移除选中的人员
    deleteSelected(e) {
      let idx = this.selectedList.findIndex((item) => item.id == e.id);
      this.selectedList.splice(idx, 1);
      this.selectedIds.splice(idx, 1);

      setTimeout(() => {
        this.$refs.memberList.changeSelected(this.selectedIds, true);
      }, 100);
    },
    // 超时按钮切换
    changChaoshi(e) {
      if (!e) {
        this.inactiveText = "未开启";
      } else {
        this.inactiveText = "已开启";
      }
    },
    // 显示选择会员弹框
    showAddMember() {
      this.show_add_member = true;
    },
    // 选中会员
    selecetedMember(e) {
      this.selectedIds = e.checkedKeys;
      this.selectedList = e.checkedNodes;
    },
    handleCoverSuccess(e) {
      this.cover = e.url;
    },
    selectMemberOk() {
      this.form_params.user_id = this.selectedIds.join(",");
      this.form_selectedMember = this.selectedList;
      this.show_add_member = false;
    },
    async getTags() {
      let res = await this.$http.getCrmGoupTags().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.tagList = res.data
      }
    },
    changTags(e) {
      console.log(e);
    }
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;

  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.imgurl {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
}

.tip {
  color: #8a929f;
  font-family: PingFang SC;
  font-weight: regular;
  font-size: 12px;
}

.add {
  max-height: 70vh;
  overflow-y: auto;

  .title {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 16px;
    margin: 20px 0;
    font-weight: 600;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}

.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}

.selected_list {
  padding: 10px 0;

  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;

    .delete {
      font-size: 22px;
      cursor: pointer;
    }

    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}

.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;

  &::-webkit-scrollbar {
    width: 0;
  }

  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;

    &.active {
      border: 5px solid #409eff;

      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    &:nth-child(4n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;

    &.active {
      border: 5px solid #409eff;
    }

    .file_img {
      width: 115px;
      height: 115px;
      text-align: center;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}

.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }

  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;

  &.active {
    border: 1px solid #a6e22e;
  }
}

.upload-demo {
  margin-right: 5px;
}
</style>
