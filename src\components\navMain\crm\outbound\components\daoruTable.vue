<template>
  <div class="table">
    <div class="table_oper flex-row align-center">
      <div class="table_oper_item flex-row align-center flex-1">
        <div class="mr10">
          <el-radio v-model="all" :label="1" border @change="changeRadioAll"
            >全部</el-radio
          >
          <el-checkbox
            border
            v-model="task_params.is_distribution"
            :false-label="0"
            :true-label="1"
            @change="changeCheckbox"
            >{{
              task_params.is_distribution === ""
                ? "分配状态"
                : task_params.is_distribution
                ? "已分配"
                : "未分配"
            }}</el-checkbox
          >
          <el-checkbox
            border
            v-model="task_params.is_repeat"
            :false-label="0"
            :true-label="1"
            @change="changeCheckbox"
            >{{
              task_params.is_repeat === ""
                ? "重复状态"
                : task_params.is_repeat
                ? "重复"
                : "未重复"
            }}</el-checkbox
          >
          <!-- <el-radio
            size="small"
            v-model="task_params.is_distribution"
            :label="1"
            border
            @change="changeRadio"
            >已分配</el-radio
          >
          <el-radio
            size="small"
            v-model="task_params.is_distribution"
            :label="0"
            border
            @change="changeRadio"
            >未分配</el-radio
          >
          <el-radio
            size="small"
            v-model="task_params.is_repeat"
            :label="1"
            border
            @change="changeRadio"
            >重复</el-radio
          > -->
          <!-- <el-radio
            size="small"
            v-model="task_params.is_repeat"
            :label="0"
            border
            @change="changeRadio"
            >不重复</el-radio
          > -->
        </div>
      </div>
      <div class="table_oper_item flex-row align-center">
        <el-button size="small" class="mr10" type="primary" @click="showFenpei"
          >人工分配</el-button
        >
        <el-popconfirm title="确定导出吗？" @onConfirm="exportExcel">
          <el-button slot="reference" size="small" type="primary"
            >导出</el-button
          >
        </el-popconfirm>
        <!-- <el-button size="small" type="warning">删除</el-button> -->
      </div>
    </div>
    <el-table
      v-loading="task_table_loading"
      :data="task_tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column label="ID" width="55" prop="id"></el-table-column>
      <el-table-column label="号码" prop="phone"> </el-table-column>
      <!-- <el-table-column label="审核状态" prop="status" v-slot="{ row }">
        <el-tag :type="row.status == 1 ? 'success' : 'warning'">{{
          row.status == 1 ? "审核通过" : "待审"
        }}</el-tag>
      </el-table-column> -->
      <el-table-column label="名称" prop="name"> </el-table-column>
      <el-table-column label="重复" v-slot="{ row }">
        <el-tag :type="row.is_repeat == 1 ? 'success' : 'warning'">{{
          row.is_repeat == 1 ? "重复" : "未重复"
        }}</el-tag>
      </el-table-column>
      <el-table-column label="分配状态" v-slot="{ row }">
        <el-tag :type="row.is_distribution == 1 ? 'success' : 'warning'">{{
          row.is_distribution == 1 ? "已分配" : "未分配"
        }}</el-tag>
      </el-table-column>
      <el-table-column label="添加时间" prop="created_at"> </el-table-column>

      <!-- <el-table-column label="操作" v-slot="{ row }">
        
        <el-popconfirm
          title="确定更改此号码的禁用状态吗？"
          class="mr10"
          @onConfirm="changeTaskDisabled(row)"
        >
          <el-button
            slot="reference"
            :type="row.is_disable == 0 ? 'warning' : 'primary'"
            >{{ row.is_disable == 0 ? "禁用" : "启用" }}</el-button
          >
        </el-popconfirm>
        
      </el-table-column> -->
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev,pager,next"
      :total="taskTotal"
      :page-size="task_params.per_page"
      :current-page="task_params.page"
      @current-change="onTaskPageChange"
    ></el-pagination>
    <el-dialog width="550px" title="人工分配" :visible.sync="fenpei">
      <div class="fenpei">
        <el-form label-width="120px">
          <el-form-item label="任务包名称">
            <el-input
              class="w220"
              :readonly="true"
              v-model="fenpei_params.name"
              placeholder="请输入任务包名称"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="分配部门">
            <el-cascader
              class="w220"
              :options="departmentList"
              :props="{
                value: 'id',
                label: 'name',
                children: 'subs',
                emitPath: false,
                multiple: false,
              }"
              clearable
              @change="changeDepartment"
              v-model="fenpei_params.department_id"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="分配方式">
            <div>
              <el-radio
                size="small"
                v-model="fenpei_params.type"
                :label="1"
                border
                >平均分配</el-radio
              >
              <el-radio
                size="small"
                v-model="fenpei_params.type"
                :label="2"
                border
                >自定义分配</el-radio
              >
            </div>
          </el-form-item>
          <el-form-item
            label="分配员工"
            v-if="
              fenpei_params.department_id &&
              memberList.length &&
              fenpei_params.type == 2
            "
          >
            <div>
              <div class="all">
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="checkAll"
                  @change="handleCheckAllChange"
                  >全选</el-checkbox
                >
              </div>
              <!-- <div class="tips">
                （已选 {{ fenpei_params.user_id.length }} 人，分配线索 0
                条，剩余可分配 6 条）
              </div> -->
              <div class="memberList">
                <el-checkbox-group
                  v-model="selectedUserId"
                  @change="handleCheckedMemberChange"
                >
                  <el-checkbox
                    v-for="member in memberList"
                    :label="member.id"
                    :key="member.id"
                  >
                    <div class="flex-row items-center align-center">
                      <el-input
                        v-model="member.count"
                        class="w80"
                        size="mini"
                        step="1"
                        type="number"
                        min="0"
                        @focus="inPutFocus($event, member)"
                        @blur="blurFocus($event, member)"
                        :max="unAssignClueCount - feipeiCount"
                        placeholder="请输入分配条数"
                      >
                      </el-input>
                      <!-- @input="changeInput($event, member)" -->
                      <div class="name ml5">
                        {{ member.user_name }}
                      </div>
                    </div>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>
          <el-form-item label-width="0">
            <div class="flex-row items-center">
              <el-button> 取消</el-button>
              <el-button
                type="primary"
                v-loading="loadingFenpei"
                @click="subFenpei"
                >确认分配</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    current: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      sub_loading: false,
      task_params: {
        page: 1,
        per_page: 10,
        clue_id: '',
        is_repeat: '',
        is_distribution: ""
      },
      all: 1,
      taskTotal: 0,
      task_tableData: [],
      task_table_loading: false,
      curr: {
        id: ''
      },
      task_edit: false, //显示添加编辑坐席弹框
      taskTitle: '', //添加编辑坐席的弹框标题
      is_add_loading: false,
      make_phone: false,
      step: '',
      selected_phone: '', //选中的外显号码id
      outShowPhoneList: [], //外显号码列表
      fenpei: false,
      checkAll: false,
      isIndeterminate: false,
      fenpei_params: {
        name: '',
        department_id: '',
        type: 2,
        assign: []
      },
      departmentList: [],
      memberList: [],
      selectedUserId: [],
      loadingFenpei: false,
      unAssignClueCount: 0
    }
  },
  watch: {
    current: {
      handler(val, oVal) {
        if (val !== oVal) {
          this.curr = val
          this.getTelList()
        }
      },
    },

  },
  computed: {
    feipeiCount() {
      let count = 0
      this.memberList.map(item => {
        if (this.selectedUserId.includes(item.id)) {
          count += +item.count
        }
      })
      return count
    }

  },

  created() {
  },
  methods: {
    // 获取号码列表
    getTelList() {
      this.task_params.clue_id = this.curr.id
      let params = Object.assign({}, this.task_params)
      if (this.all) {
        delete params.is_repeat
        delete params.is_distribution
      }
      this.task_table_loading = true
      this.$http.getTelList(params).then(res => {
        if (res.status == 200) {
          this.task_tableData = res.data.data
          this.taskTotal = res.data.total
        }
        this.task_table_loading = false
      }).catch(() => {
        this.task_table_loading = false
      })
    },
    // 坐席列表页面更新
    onTaskPageChange(val) {
      this.task_params.page = val
      this.getTelList()
    },
    changeTaskDisabled(row) {
      this.$http.changeTaskDisabled(row.id, { is_disable: 1 - (+row.is_disable) }).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '修改成功')
          this.getTelList()
        }
      })
    },

    changeCheckbox() {
      this.task_params.page = 1
      this.all = ''
      this.getTelList()
    },
    changeRadioAll() {
      this.task_params.page = 1
      this.task_params.is_repeat = ''
      this.task_params.is_distribution = ''
      this.getTelList()
    },

    getShowTelNumber() {
      this.$http.getShowTelNumber({
        status: 1,
        is_disable: 0,
        per_page: 1000
      }).then(res => {
        if (res.status == 200) {
          this.outShowPhoneList = res.data.data
        }
        // this.telNumberLoading = false
      }).catch(() => {
        // this.telNumberLoading = false
      })
    },
    handleCheckedMemberChange(e) {
      this.selectedUserId = e
      let checkedCount = e.length;
      this.checkAll = checkedCount === this.memberList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.memberList.length;

    },
    handleCheckAllChange(e) {
      let arr = []
      if (e && this.memberList.length) {
        this.memberList.map(item => {
          arr.push(item.id)
          // arr.push({
          //   admin_id: item.id,
          //   count: item.count
          // })
        })
      }
      this.selectedUserId = arr
      this.isIndeterminate = false;
    },
    async showFenpei() {
      let count = await this.getUnAssignClueCount()
      if (count == 0) {
        this.$message.warning("该任务包都已分配请重新选择")
        return
      }
      this.fenpei_params.name = this.current.name
      this.getDepartment()
      this.fenpei = true
    },
    changeDepartment(e) {
      this.getMemberList(e)
    },
    async getDepartment() {
      let res = await this.$http.getOutboundDepartmentList().catch((err) => {
        console.log(err);
      });

      if (res.status == 200) {
        this.departmentList = res.data;
      }
    },
    async getMemberList(department_id) {
      let res = await this.$http.getOutboundMemberList({ department_id }).catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        res.data.map(item => {
          item.count = 0
          return item
        })
        this.memberList = res.data;
      }
    },
    async getUnAssignClueCount() {
      let res = await this.$http.getUnAssignClueCount(this.current.id).catch(() => {
        console.log();
      })
      if (res.status == 200) {
        this.unAssignClueCount = res.data
      }
      return this.unAssignClueCount
    },
    subFenpei() {
      let params = Object.assign({}, this.fenpei_params)
      params.clue_id = this.current.id
      if (!this.selectedUserId.length && this.fenpei_params.type == 2) {
        this.$message.warning("请选择要分配的员工")
        return
      }
      if (!params.department_id) {
        this.$message.warning("请选择部门")
        return
      }
      if (params.type == 1) {
        // 自动分配
        delete params.assign
      } else {
        if (this.unAssignClueCount - this.feipeiCount < 0) {
          this.$message.warning("任务分配超出 请重新分配")
          return
        }
        let assignArr = []
        this.memberList.map(item => {
          if (this.selectedUserId.includes(item.id)) {

            let assign = {
              admin_id: item.id,
              count: item.count
            }
            assignArr.push(assign)
          }
        })
        params.assign = JSON.stringify(assignArr)
      }
      this.loadingFenpei = true
      this.$http.submitClueAssign(params).then(res => {
        if (res.status == 200) {
          this.$message.success(res.data?.message || res.message || '提交成功')
          this.fenpei = false
          this.getTelList()
        }
        this.loadingFenpei = false
      }).catch(() => {
        this.loadingFenpei = false
      })


    },
    exportExcel() {
      this.$http.exportZuoxi().then(res => {
        if (res.status == 200) {
          window.open(res.data)
        }
      })
    },
    blurFocus(e, member) {
      if (this.unAssignClueCount - this.feipeiCount < 0) {
        this.$message.warning("任务不足，请重新输入")
        member.count = this.backCount
      }
      this.$forceUpdate()
    },
    inPutFocus(e, member) {
      this.backCount = member.count || 0
      if (this.unAssignClueCount - this.feipeiCount < 0) {
        this.backCount = 0
      }
    }
  }
}
</script>

<style lang ="scss" scoped>
::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;
  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }
  .el-dialog__body {
    padding: 0 12px;
  }
}
.w80 {
  width: 80px;
}
.ml5 {
  margin-left: 5px;
}
.w220 {
  width: 220px;
}
.mr10 {
  margin-right: 10px;
}
.strong {
  font-weight: 600;
}
.blue {
  color: #2d84fb;
}
.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}
.el-radio {
  margin-right: 10px;
}

.maKe_phone_call {
  height: 500px;
  text-align: center;
  background: #fff;
  padding: 10px 20px;
  margin-bottom: 10px;
  box-sizing: border-box;
  .maKe_phone_title {
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
  }
  .submit_make_phone {
    margin-top: 65px;
  }
  .avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 50%;
    img {
      width: 100%;
      object-fit: cover;
    }
  }
  .telphone {
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
    margin-top: 16px;
  }
  .area {
    color: #8a929f;
    font-size: 14px;
    margin-top: 16px;
  }
  .waiting {
    margin: 15px 0 20px;
  }
  .link_step {
    border-radius: 13px;
    background: #f1f4fa;
    padding: 10px 16px;
    .link_step_title {
      color: #2e3c4e;
      font-size: 14px;
      margin-bottom: 15px;
    }
    .link_step_con {
      .link_step_left {
        width: 48px;
        .link_step_left_bottom {
          color: #2e3c4e;
          font-size: 12px;
        }
      }
      .join_line {
        margin-top: 5px;
        color: #2d84fb;
      }
    }
  }
  .to_back {
    margin-top: 65px;
    .to_back_img {
      width: 50px;
      height: 50px;
      margin: 0 auto 10px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .to_back_name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
</style>