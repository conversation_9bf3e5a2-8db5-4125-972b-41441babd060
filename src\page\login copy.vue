<template>
  <div class="login-container" :class="[isMobile ? 'mobile' : '']" v-if="showContent">
    <template v-if="!isMobile">
      <div class="login-box">
        <div class="ctn-img">
          <img src="https://img.tfcs.cn/backup/static/t%2B/tadd.png" alt="" />
        </div>
        <div class="l-r-form" :class="{'with-tip':isLowerBrower}">
          <div class="brower-tip" v-if="isLowerBrower">为系统使用体验流畅，请使用Chrome内核浏览器。</div>
          <div class="change-box div row">
            <div class="change-item" :class="{ isactive: item.id == is_change }" @click="onClickChange(item)"
              v-for="item in change_box" :key="item.id">
              {{ item.name }}
            </div>
          </div>
          <template v-if="is_change == 1">
            <!--  :rules="rules2" -->
            <el-form v-if="showType == 1" :model="ruleForm2" status-icon ref="ruleForm2" label-position="left"
              label-width="0px" class="demo-ruleForm login-page">
              <el-form-item prop="username">
                <el-input type="text" v-model="ruleForm2.username" auto-complete="off" @blur="checkTel"
                  placeholder="请输入手机号码"></el-input>
              </el-form-item>
              <el-form-item prop="password" v-if="showType == 1">
                <el-input type="password" v-model="ruleForm2.password" auto-complete="off" placeholder="请输入密码"></el-input>
              </el-form-item>
              <el-form-item prop="website" v-show="show_wesite">
                <el-select v-model="website_id">
                  <!-- :label="item.description"
          :value="item.value" -->
                  <el-option label="请选择企业" value=""> </el-option>
                  <el-option v-for="item in webList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>

              <!-- <valid-code :value.sync="validCode"></valid-code> -->
              <el-form-item>
                <el-button type="primary" style="width: 100%; height: 50px" @click="handleSubmit">登录</el-button>
              </el-form-item>
              <el-form-item style="text-align: left">
                <el-link type="primary" @click="changeType">{{
                                  showType == 1 ? "验证码登录" : "密码登录"
                                  }}</el-link>
              </el-form-item>
              <!-- <el-form-item style="text-align: end">
            <el-link type="primary">忘记密码？</el-link>
          </el-form-item> -->
            </el-form>
            <el-form v-if="showType == 2" :model="ruleForm3" status-icon ref="ruleForm3" label-position="left"
              label-width="0px" class="demo-ruleForm login-page">
              <el-form-item prop="username">
                <el-input type="text" v-model="ruleForm3.phone" auto-complete="off" @blur="checkTel"
                  placeholder="请输入手机号码"></el-input>
              </el-form-item>
              <el-form-item prop="website" v-show="show_wesite">
                <el-select v-model="website_id">
                  <!-- :label="item.description"
          :value="item.value" -->
                  <el-option label="请选择企业" value=""> </el-option>
                  <el-option v-for="item in webList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="captcha" v-if="showType == 2">
                <div class="captcha-box div row">
                  <el-input class="flex-1" style="margin-right: 5px" type="text" v-model="ruleForm3.captcha"
                    auto-complete="off" placeholder="验证码"></el-input>
                  <div class="send-code" :class="sending ? 'disable' : ''" @click="sendCode">
                    {{ time ? time + "s后重新获取" : "获取验证码" }}
                  </div>

                  <!-- <img
                  style="width: 150px; height: 50px; margin-left: 10px"
                  @click="changeCaptcha"
                  :src="` https://yun.tfcs.cn/api/auth/admin/login/captcha?time=${captchaImg}`"
                  alt
                /> -->
                </div>
              </el-form-item>

              <!-- <valid-code :value.sync="validCode"></valid-code> -->
              <el-form-item>
                <el-button type="primary" style="width: 100%; height: 50px" @click="handleSubmitCode">登录</el-button>
              </el-form-item>
              <el-form-item style="text-align: left">
                <el-link type="primary" style="text-align: start" @click="changeType">{{ showType == 1 ? "验证码登录" : "密码登录"
                                  }}</el-link>
              </el-form-item>
            </el-form>
          </template>
          <div class="login-page" v-else>
            <div class="code-desc">
              <div class="t">打开<span>企业微信</span></div>
              <div class="t">扫一扫快捷登录</div>
            </div>
            <img class="erweima" src="" alt="" />
          </div>
          <div class="bottom-c div row">
            <div class="b-c-l div row">
              <img src="https://img.tfcs.cn/backup/static/t%2B/qywx.png" alt="" class="logo" />
              <div class="tb">
                <div class="t">企业微信扫码登录</div>
                <div class="b">更方便，内部协同更高效</div>
              </div>
            </div>
            <el-button type="primary" @click="onQrcode">扫码登录</el-button>
          </div>
        </div>
      </div>
    </template>
    <!-- h5端登录页面 -->
    <template v-else>
      <div class="list">
        <div class="title row">快捷登录</div>

        <el-form ref="ruleForm3" label-position="left" label-width="0px" class="demo-ruleForm mobile-page">
          <el-form-item>
            <el-input type="text" v-model="ruleForm2.username" auto-complete="off" @blur="checkTel"
              placeholder="请输入手机号码"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input type="password" v-model="ruleForm2.password" auto-complete="off" placeholder="请输入密码"></el-input>
          </el-form-item>
          <el-form-item prop="website" v-show="show_wesite">
            <el-select v-model="website_id">
              <el-option label="请选择企业" value=""> </el-option>
              <el-option v-for="item in webList" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" style="
                width: 100%;
                height: 50px;
                border-radius: 50px;
                font-size: 16px;
              " @click="handleSubmit1">登录</el-button>
          </el-form-item>
        </el-form>

        <!-- <div class="login-box">
          <div class="login_phone flex-row">
            <input
              v-model="form.phone"
              type="number"
              placeholder-style="font-size:28rpx;color:#d8d8d8"
              maxlength="11"
              placeholder="请输入手机号码"
            />
          </div>
          <div class="login_phone flex-row">
            <input
              v-model="form.password"
              type="password"
              placeholder-style="font-size:28rpx;color:#d8d8d8"
              placeholder="请输入密码"
            />
          </div>
          <div class="login_phone flex-row">
            <input
              v-model="form.password"
              type="password"
              placeholder-style="font-size:28rpx;color:#d8d8d8"
              placeholder="请输入密码"
            />
          </div>

          <button
            v-if="is_phone_login"
            :class="checked ? 'hava-value' : 'btn'"
            class="btn"
            @click="onSubmit"
            style="margin-top: 20rpx"
          >
            立即登录
          </button>
          <button
            v-if="!is_phone_login"
            :class="checked ? 'hava-value' : 'btn'"
            class="btn btn-left"
            @click="getInfo"
          >
            <myIcon
              type="weixin"
              style="margin-right: 10rpx"
              color="#fff"
            ></myIcon>
            快捷登录
          </button>
          <view class="radio-box row">
            <view class="radio-content row">
              <radio
                class="radio-form"
                :checked="checked"
                @click="changeRadio"
              />
              <text class="row">
                我已阅读并同意<text
                  style="text-decoration: underline"
                  @click="openContent(3)"
                  >《隐私政策》</text
                >及<text
                  style="text-decoration: underline"
                  @click="openContent(4)"
                  >《用户服务协议》</text
                >
              </text>
            </view>
          </view>
          <view class="phonelogin" @click="is_phone_login = !is_phone_login">
            {{ is_phone_login ? "快捷" : "手机号验证" }}登录
          </view>
        </div> -->
      </div>
    </template>
  </div>
</template>

<script>
// import axios from "axios";
// import validCode from "@/components/validCode";
// import { mapMutations } from "vuex";

//先引入登录接口
import { Loading } from "element-ui";
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {
      //登录状态
      isLogin: false,
      // 加载状态
      logining: false,
      ruleForm2: {
        // 测试账号
        username: "",
        // 测试密码
        password: "",
        // captcha: "",
      },
      rules2: {
        username: [
          {
            required: true,
            message: "请输入用户名/手机号码",
            trigger: "blur",
          },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        // captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      ruleForm3: {
        phone: "",
        captcha: ""
      },
      rules3: {
        username: [
          {
            required: true,
            message: "请输入手机号码",
            trigger: "blur",
          },
        ],

        // captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      // https://fenxiao.zaodaoxiao.com/api/auth/admin/login/captcha
      imgUrl: "",
      captchaImg: null,
      change_box: [{ id: 1, name: "账号登录" }],
      is_change: 1,
      r_url: "",
      webList: [],
      show_wesite: false,
      website_id: "",
      isMobile: false,
      time: 0,
      sending: false,
      showType: 1,
      showContent: true,
      isLowerBrower: false,   //是否低版本浏览器
    };
  },
  created() {
    this.isMobile = window.navigator.userAgent.match(
      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    ); // 是否手机端
    !this.isMobile && this.checkBrower();
    // com-wx-mobile
    let website_id = this.getUrlKey("website_id");
    let auth_code = this.$route.query.auth_code;
    if (auth_code) {
      this.showContent = false
      Loading.service();
      this.setAuthCode(auth_code);
      return
    }
    if (website_id) {
      localStorage.setItem("website_id", website_id);
    }
  },
  mounted() {
    // window.localStorage.clear()
    // this.changeCaptcha();
  },
  computed: {
    ...mapState(["website_info"]),
  },
  components: {
    // validCode
  },
  methods: {
    ...mapMutations(["setSlideTopMenu"]),
    // 获取扫码登录
    onQrcode() {
      // let website_id = this.$route.query.website_id;
      // localStorage.setItem("website_id", website_id);
      // this.$http
      //   .setCrmQueryAuth()
      //   .then((res) => {
      //     if (res.status === 200) {
      let CorpID = "ww9d222841bce98584";
      let baseUrl = encodeURIComponent(
        "https://yun.tfcs.cn/admin/#/login"
      );
      this.r_url =
        "https://open.work.weixin.qq.com/wwopen/sso/3rd_qrConnect?appid=" +
        CorpID +
        "&redirect_uri=" +
        baseUrl +
        "&state=web_login@gyoss9&usertype=member";
      window.location.href = this.r_url;
      // }
      // })
      // .catch(() => {
      //   this.$message.error("功能未开启");
      // });
    },
    sendCode() {
      if (this.sending) {
        return
      }
      // if (!this.checkPhone(this.phone)) {
      //   return
      // }
      if (!this.ruleForm3.phone) {
        this.$message.warning("请输入手机号")
        return
      }
      if (!this.website_id) {
        this.$message.warning("请选择企业")
        return
      }
      localStorage.setItem('website_id', this.website_id)
      this.$http.sendLoginCode({ phone: this.ruleForm3.phone }).then(res => {
        if (res.status == 200) {
          this.time = 60
          this.timer()
          this.sending = true
        }
      })
      // this.$http.post('mini/user/send_phone_code', { url: this.url, phone: this.phone }, (res) => {
      //   if (res.status == 200) {
      //     this.time = 60
      //     this.timer()
      //     this.sending = true
      //   }
      // })
    },
    timer() { //倒计时
      if (timer) {
        clearInterval(timer)
      }
      let timer = setInterval(() => {
        if (this.time <= 0) {
          clearInterval(timer)
          this.sending = false
          return
        }
        this.time--
      }, 1000)
    },
    // 获取路径中的参数（code等）
    getUrlKey(name) {
      let url = window.location.href;
      let par = url.split("?")[1] || "";
      let p = par.split("&") || [];
      let res = "";
      p.map((item) => {
        if (item.includes(name)) {
          res = item.split("=")[1];
        }
        // obj[item.split("=")[0]] = item.split("=")[1]
      });
      return parseInt(res);
      // return (
      //   decodeURIComponent(
      //     (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|/#|;|$)").exec(
      //       location.href
      //     ) || ["", ""])[1].replace(/\+/g, "%20")
      //   ) || null
      // );
    },
    handleSubmit1() {
      if (!this.website_id) {
        this.ruleForm2.username = "" 
        // 测试密码
        this.ruleForm2.password = ""
        this.$message.warning("暂未查询到该手机号信息")
        return
      }
      localStorage.setItem("website_id", this.website_id)
      if (
        // 测试账号
        this.ruleForm2.username === "" ||
        // 测试密码
        this.ruleForm2.password === ""
      ) {
        this.$alert("用户名或密码输入错误", "警告", {
          confirmButtonText: "ok",
        });
        return
      }
      if (this.logining) return
      this.logining = true;
      this.$http
        .login({
          website_id: this.website_id,
          user_name: this.ruleForm2.username,
          password: this.ruleForm2.password,
        })
        .then((res) => {
          console.log(res,'88888000900');
          if (res.status === 200) {
            localStorage.setItem("website_id", this.website_id);
            localStorage.setItem("wxwork_id", this.website_id);
            localStorage.setItem("token" + this.website_id, res.data.token);
            localStorage.setItem("wxwork_token", res.data.token);
            localStorage.setItem("isLoadEnd", 1);
            this.$message({
              message: "登录成功！",
              type: "success",
            });
            let backUrl = localStorage.getItem('backUrl')
            let url = 'https://yun.tfcs.cn/fenxiao/customer/index?website_id=' + this.website_id
            if (backUrl) {
              url = backUrl
            }
            if(this.website_id == 0 || this.website_id== null || this.website_id == 'undefined'){
              alert('该站点不存在')
              return
            }
            localStorage.removeItem('backUrl')
            location.href = url
            return
          } else {
            this.logining = false
            localStorage.removeItem("website_id")
          }
        }).catch(() => {
          localStorage.removeItem("website_id")
        });
    },
    // 截取地址栏参数
    handleSubmit() {
      // let website_id = localStorage.getItem("website_id");
      if (!this.website_id) {
        this.$message.warning("暂未查询到该手机号信息")
        return
      }
      this.$refs.ruleForm2.validate((valid) => {
        localStorage.setItem("website_id", this.website_id)
        if (valid) {
          this.logining = true;
          if (
            // 测试账号
            this.ruleForm2.username === "" ||
            // 测试密码
            this.ruleForm2.password === ""
          ) {
            this.logining = false;
            this.$alert("用户名或密码输入错误", "警告", {
              confirmButtonText: "ok",
            });
            this.isLogin = false;
          } else {
            // let token = "Bearer " + localStorage.getItem("TOKEN");\
            let username = this.ruleForm2.username.trim()
            this.$http
              .login({
                website_id: this.website_id,
                user_name: username,
                password: this.ruleForm2.password,
                // captcha: this.ruleForm2.captcha,
              })
              .then((res) => {
                if (res.status === 200) {
                  //登陆成功后调用第2步store里面的login方法，并将username传递过去，并跳转到home主页面

                  localStorage.setItem("TOKEN", res.data.token);
                  localStorage.setItem("website_id", this.website_id);
                  // localStorage.setItem("admin_token_" + website_id);
                  this.$message({
                    message: "登录成功！",
                    type: "success",
                  });
                  this.$store.commit("login", res.data.token);
                  this.getPermissionSlideList();
                } else {
                  this.changeCaptcha();
                  localStorage.removeItem("website_id")
                }
              }).catch(() => {
                localStorage.removeItem("website_id")
              });

          }
        } else {
          this.$message({
            message: "请输入内容后提交",
            type: "error",
          });
          return false;
        }
      });
    },
    handleSubmitCode() {
      // let website_id = localStorage.getItem("website_id");
      if (!this.website_id) {
        this.$message.warning("暂未查询到该手机号信息")
        return
      }
      localStorage.setItem("website_id", this.website_id)
      this.logining = true;
      if (
        // 测试账号
        ((this.ruleForm2.username === "" ||
          this.ruleForm2.password === "") && this.showType == 1)
      ) {
        this.logining = false;
        this.$alert("用户名或密码输入错误", "警告", {
          confirmButtonText: "ok",
        });
        this.isLogin = false;
      } else if (
        // 测试账号
        ((this.ruleForm3.phone === "" ||
          this.ruleForm3.captcha === "") && this.showType == 2)
      ) {
        this.logining = false;
        this.$alert("手机号或验证码错误", "警告", {
          confirmButtonText: "ok",
        });
        this.isLogin = false;
      } else {
        // let token = "Bearer " + localStorage.getItem("TOKEN");
        this.$http
          .codeLogin({
            website_id: this.website_id,
            phone: this.ruleForm3.phone,
            captcha: this.ruleForm3.captcha,
            // captcha: this.ruleForm2.captcha,
          })
          .then((res) => {
            if (res.status === 200) {
              //登陆成功后调用第2步store里面的login方法，并将username传递过去，并跳转到home主页面

              localStorage.setItem("TOKEN", res.data.token);
              localStorage.setItem("website_id", this.website_id);
              // localStorage.setItem("admin_token_" + website_id);
              this.$message({
                message: "登录成功！",
                type: "success",
              });
              this.$store.commit("login", res.data.token);
              this.getPermissionSlideList();
            } else {
              this.changeCaptcha();
              localStorage.removeItem("website_id")
            }
          }).catch(() => {
            localStorage.removeItem("website_id")
          });

      }
    },
    changeType() {
      this.showType == 1 ? (this.showType = 2) : (this.showType = 1)
      this.website_id = ''
      localStorage.removeItem("website_id")
    },
    checkTel() {
      if (!this.ruleForm2.username && this.showType == 1) return
      if (!this.ruleForm3.phone && this.showType == 2) return
      this.website_id = ''
      this.webList = []
      this.$http.checkLoginTel({ phone: this.showType == 1 ? this.ruleForm2.username : this.ruleForm3.phone }).then((res) => {
        if (res.status === 200) {
          if (res.data.count > 1) {
            this.webList = res.data.list
            this.show_wesite = true
          } else if (res.data.count == 1) {
            this.website_id = res.data.list[0].id
            this.show_wesite = false
            localStorage.setItem("website_id", res.data.list[0].id)
          } else {
            this.show_wesite = false
          }
        }
      });
    },
    async getPermissionSlideList() {
      // await this.$http.getInfoConfig().then(res => {
      //   if (res.status == 200) {
      //     this.$store.state.disableClick = ''
      //   }
      // }).catch((err) => {
      //   this.$store.state.disableClick = err.message
      // })
      this.$http.getPermissionSlideList().then((res) => {
        if (res.status === 200) {
          this.$goPath(res.data[0].home_url);
          this.setSlideTopMenu(res.data);
        }
      });
    },
    changeCaptcha() {
      this.captchaImg = parseInt(Math.random() * (10000 + 1), 10);
    },
    onClickChange(e) {
      // if (e.id == 2) {
      //   this.$message.error("暂未开放扫码登录");
      //   return;
      // }
      this.is_change = e.id;
    },
    setAuthCode(code) {
      this.$http.setCrmLoginCodeData(code).then((res) => {
        if (res.status === 200) {
          Loading.service().close();
          localStorage.setItem("TOKEN", res.data.token);
          localStorage.setItem("website_id", res.data.website_id);
          localStorage.setItem("auth_way", res.data.auth_way || 0);
          let url = `https://yun.tfcs.cn/admin/#/index?website_id=${res.data.website_id}`;
          window.location.href = url;
        } else {
          Loading.service().close();
          this.showContent = true
          location.replace('https://yun.tfcs.cn/admin/#/login')
        }
      }).catch(() => {
        this.showContent = true
        Loading.service().close();
        location.replace('https://yun.tfcs.cn/admin/#/login')
      });
    },
    checkBrower(){
      let isIE = !!document.documentMode || /*@cc_on!@*/false;
      this.isLowerBrower = isIE;
    }
  },
};
</script>

<style scoped lang="scss">
.login-container {
  width: 100%;
  height: 100%;
  position: fixed;
  background-image: url("https://img.tfcs.cn/backup/static/t%2B/bg.jpg");
  background-size: 100% 100%;

  &.mobile {
    background: none;
  }

  .title {
    margin-bottom: 44px;
  }
}

.l-r-form {
  width: 408px;
  background: #fff;
  text-align: center;
  border-radius: 4px;
  position: relative;
  height: 505px;
  overflow: hidden;

  &.with-tip {
    height: 541px;
  }
}

.login-page {
  padding: 48px;
  margin-top: 60px;
  padding-top: 0;

  .code-desc {
    text-align: center;
    font-size: 14px;

    .t {
      span {
        color: #2d84fb;
      }
    }
  }

  .erweima {
    width: 180px;
    height: 180px;
    text-align: center;
    margin-top: 40px;
  }
}

.el-input {
  width: 312px;
}

.el-select {
  width: 312px;
}

/deep/.el-input__inner {
  height: 50px;
  line-height: 50px;
}

label.el-checkbox.rememberme {
  margin: 0px 0px 15px;
  text-align: left;
}

.captcha-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-box {
  display: flex;
  position: fixed;
  left: 50%;
  top: 50%;
  align-items: center;
  transform: translate(-50%, -50%);
}

.el-form-item {
  margin-bottom: 24px;
}

.ctn-img {
  margin-right: 230px;
}

.bottom-c {
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 24px;
  position: absolute;
  bottom: 0;
  background: #f0f0f0 100%;
  border-radius: 0px 0px 4px 4px;

  .b-c-l {
    align-items: center;

    .logo {
      width: 48px;
      height: 48px;
    }

    .tb {
      text-align: left;
      margin-left: 12px;
      line-height: 20px;

      .t {
        font-size: 16px;
        color: #333;
      }

      .b {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.change-box {
  justify-content: center;
  margin-top: 40px;
  font-size: 24px;
  padding: 0 64px;
  color: #333;
  cursor: pointer;

  .change-item {
    font-weight: 400;

    &.isactive {
      font-weight: bold;
    }
  }
}

// 移动端样式
.list {
  padding: 12px 24px;

  .title {
    font-size: 23px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    color: #333;
    margin: 49px 0;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
  }

  .mobile-page {

    .el-input,
    .el-select {
      width: 100%;
    }

    ::v-deep .el-input__inner {
      border: 0;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

.send-code {
  padding: 0 10px;
  border-radius: 5px;
  // background-color: $uni-color-primary;
  color: #333;
  font-weight: bold;
  cursor: pointer;
}

.send-code.disable {
  background-color: #f1f1f1;
  color: #666;
}

.brower-tip {
  box-sizing: border-box;
  padding: 8px 16px;
  margin: 0;
  background-color: #fdf6ec;
  color: #E6A23C;
  text-align: center;
  font-size: 15px;
}
</style>
