<template>
    <div>
        <div>
            <span>明源云客同步：</span>
            <el-radio v-model="openyuan" label="1" @change="openmy">开启</el-radio>
            <el-radio v-model="openyuan" label="0" @change="openmy">关闭</el-radio>
        </div>
        <div class="operatebtn">
            <el-button type="primary" size="small" @click="addkey">添加秘钥</el-button>
        </div>
            <el-table 
               class="mylist"
                v-loading="is_table_loading"
                  :data="tableData"
                  border
                  ref="table"
                  style="width: 100%"
                  :header-cell-style="{ background: '#EBF0F7' }">
                  <el-table-column
                    prop="title "
                    label="标题"
                    fixed
                    v-slot="{ row }"
                    min-width="170"> 
                    {{row.title?row.title:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="app_key "
                    label="租户应用AppKey"
                    v-slot="{ row }"
                    min-width="170"> 
                    {{row.app_key?row.app_key:"--"}}
                    <!-- <el-link type="primary" v-if="!row.showAppKey" :underline="false"  @click="handleClick(row)">点击查看</el-link>
                   <span v-if="row.showAppKey"> {{row.app_key?row.app_key:"--"}}</span> -->
                  </el-table-column>
                  <el-table-column
                    prop="app_secret "
                    label="租户应用AppSecret"
                    v-slot="{ row }"
                    min-width="170"> 
                    {{row.app_secret?row.app_secret:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="tenant_number "
                    label="租户号"
                    v-slot="{ row }"
                    min-width="150"> 
                    {{row.tenant_number?row.tenant_number:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="token "
                    label="运营主体token"
                    v-slot="{ row }"
                    min-width="150"> 
                    {{row.token?row.token:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="encrypt_key "
                    label="消息校验key"
                    v-slot="{ row }"
                    min-width="150"> 
                    {{row.encrypt_key?row.encrypt_key:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="encrypt_token "
                    label="消息校验token"
                    v-slot="{ row }"
                    min-width="150"> 
                    {{row.encrypt_token?row.encrypt_token:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="org_code "
                    label="消息校验租户号"
                    v-slot="{ row }"
                    min-width="150"> 
                    {{row.org_code?row.org_code:"--"}}
                  </el-table-column>
         
                  <el-table-column
                    prop="created_at "
                    label="拉取时间"
                    v-slot="{ row }"
                    min-width="160"> 
                    {{row.created_at?row.created_at:"--"}}
                  </el-table-column>
                  <el-table-column
                    prop="updated_at "
                    label="编辑时间"
                    v-slot="{ row }"
                    min-width="160"> 
                    {{row.updated_at?row.updated_at:"--"}}
                  </el-table-column>
                  <el-table-column
                    label="是否默认"
                    fixed="right"
                    v-slot="{ row }"
                    min-width="120"
                    align="center">
                    <el-link v-if="row.is_default==0"  type="success" :underline="false"
                    @click="defaultkey(row,1)">设为默认</el-link>
                    <el-link v-else type="warning" :underline="false"
                    @click="defaultkey(row,1)">取消默认</el-link>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    fixed="right"
                    v-slot="{ row }"
                    min-width="200"
                    align="center">
                    <el-link type="primary" :underline="false" style="margin-left:10px;"
                    @click="editKey(row)">编辑</el-link>
                  </el-table-column>
            </el-table>
                <!-- <el-pagination
                style="text-align:right;"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="params.per_page"
                    :current-page="params.page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total">
                  </el-pagination> -->
        <Showkey ref="Showkey" @getkeylist="getmykeylist"></Showkey>
    </div>
</template>
<script>
import Showkey from "./addkey.vue"
export default {
    components:{
        Showkey
    },
    data() {
        return {
            openyuan:"1",
            params:{
                page: 1,
                per_page: 10,
            },
            tableData:[],
            is_table_loading:false,
        }
    },
    mounted(){
        this.openyuan = String(this.$store.state.website_info.open_ming_yuan);
        this.getmykeylist()
    },
    methods:{
        openmy(e){
            let params = {
                open_ming_yuan:e
            }
            this.$http.setopenmingyuan(params).then(res=>{
                if(res.status==200){
                    this.$message.success("操作成功")
                }
            })
        },
         //获取秘钥列表
         getmykeylist(){
            this.is_table_loading = true
            this.$http.mykeymanagementlist().then(res=>{
                if(res.status==200){
                    this.tableData = res.data.data
                    this.is_table_loading = false
                    // this.params.total = res.data.total`
                }
            })
        },
        //添加秘钥
        addkey(){
            this.$refs.Showkey.open()
        },
        //编辑秘钥
        editKey(row){
            this.$refs.Showkey.open(row)
        },
        //
        handleClick(row) {
          // 反转显示状态
          this.$set(row, 'showAppKey', true);
          
        },
        //设置为默认秘钥
        defaultkey(row,status){
            let data ={
                id:row.id,
                is_default:status
            }
            this.$confirm('此操作会将该秘钥设为默认秘钥 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
                this.$http.setdefaultkeymy(data).then(res=>{
                    if(res.status==200){
                        this.$message({
                          type: 'success',
                          message: '设置成功!'
                        });
                        this.getmykeylist()
                    }
                })
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作'
              });          
            });
        },
        // //每页 条
        // handleSizeChange(val){
        //     this.params.per_page = val
        //     this.getList()
        // },
        // //当前页
        // handleCurrentChange(val) {
        //     this.params.page = val
        //     this.getList()
        // },
    },
}
</script>
<style lang="scss" scoped>
.operatebtn{
    margin-top: 20px;
}
.mylist{
    margin-top: 10px;
}
</style>