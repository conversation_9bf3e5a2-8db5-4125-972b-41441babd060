<template>
  <div>
    <div class="content-box-crm">
      <div class="table-top-box div row">
        <div class="t-t-b-left div row"></div>
        <div class="t-t-b-right div row">
          <el-button type="primary" @click="add"> 添加模板</el-button>
        </div>
      </div>
      <div>
        <el-table
          v-loading="is_table_loading"
          :data="templateData"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column prop="template_name" label="名称"></el-table-column>
          <el-table-column label="模板内容" v-slot="{ row }">
            <el-tooltip class="item" effect="light" placement="top">
              <div slot="content" style="max-width: 300px">
                {{ row.send_smg }}
              </div>
              <div class="msg_con">
                {{ row.send_smg }}
              </div>
            </el-tooltip>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" v-slot="{ row }">
            {{ row.created_at || "--" }}
          </el-table-column>
          <el-table-column label="操作" v-slot="{ row }">
            <el-link @click="edit(row)">编辑</el-link>
            <el-popconfirm
              title="确定删除吗？"
              style="margin: 0 10px"
              @onConfirm="cancelTemplate(row)"
            >
              <el-link slot="reference" type="danger">删除</el-link>
            </el-popconfirm>
          </el-table-column>
        </el-table>

        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="params.per_page"
          :current-page="params.page"
          @current-change="onPageChange"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      width="500px"
      append-to-body
      :visible.sync="is_showDia"
      :title="addTitle"
    >
      <div v-if="is_showDia">
        <el-form label-width="120px">
          <el-form-item label="名称">
            <el-input style="width: 260px" v-model="task_form.template_name">
            </el-input>
          </el-form-item>
          <el-form-item label="发送内容">
            <el-input
              type="textarea"
              v-model="task_form.send_smg"
              :maxlength="maxlen"
              rows="5"
              show-word-limit
              style="width: 260px"
            >
            </el-input>
            <el-tooltip class="item" effect="light" placement="top">
              <div slot="content" style="max-width: 200px">
                发送内容字数限制包含签名
              </div>
              <i
                style="margin-left: 10px; color: #e6a23c"
                class="el-icon-warning"
              ></i>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button v-loading="add_loading" type="primary" @click="confirmAdd"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      templateData: [],
      is_table_loading: false,
      is_table_loading1: false,
      params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      is_showDia: false,
      task_form: {
        template_name: "",
        send_smg: ""
      },
      add_loading: false,
      addTitle: "",
      maxlen: 65
    }
  },
  computed: {
  },
  created() {
    this.getSign()
    this.getList()
  },
  methods: {
    getList() {
      this.is_table_loading = true
      this.$http.getMaketingTemplateList(this.params).then(res => {
        if (res.status == 200) {
          this.templateData = res.data.data
        }
        this.is_table_loading = false
      }).catch(() => {
        this.is_table_loading = false
      })
    },
    // 获取签名配置
    getSign() {
      this.$http.getSign().then(res => {
        if (res.status == 200) {
          console.log();
          this.maxlen = 62 - res.data.value.length
        }
      })
    },
    add() {
      this.isAdd = true
      this.addTitle = "添加模板"
      this.task_form = {
        template_name: "",
        send_smg: ""
      }
      this.is_showDia = true
    },
    edit(row) {
      this.isAdd = false
      this.addTitle = "编辑模板"
      this.task_form = {
        template_name: row.template_name,
        send_smg: row.send_smg,
        id: row.id
      }
      this.is_showDia = true
    },
    editTemplate() {
      this.add_loading = true
      this.$http.editMarketTemplate(this.task_form).then(res => {
        if (res.status == 200) {
          this.$message.success("模板修改成功")
          this.is_showDia = false
          this.add_loading = false
          this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })
    },
    addTemplate() {
      this.add_loading = true
      this.$http.addMarketTemplate(this.task_form).then(res => {
        if (res.status == 200) {
          this.$message.success("模板创建成功")
          this.is_showDia = false
          this.add_loading = false
          this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })

    },
    cancelTemplate(row) {
      this.$http.cancelMarketTemplate(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success("模板删除成功")
          this.getList()
        }
      })
    },

    confirmAdd() {
      if (this.isAdd) {
        this.addTemplate()
      } else {
        this.editTemplate()
      }
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getList()
    },

  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}
.content-box-crm {
  padding: 0;
}
.ml30 {
  margin-left: 30px;
}
.padd10 {
  padding: 10px 0 40px;
}
.title {
  padding-top: 20px;
  padding-left: 75px;
}
.msg_con {
  width: 180px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>