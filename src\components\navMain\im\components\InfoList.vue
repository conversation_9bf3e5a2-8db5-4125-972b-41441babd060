<template>
    <div class="list">
      <div class="item flex-row" v-for="(item, index) in info_list" :key="index">
        <div class="img_box">
          <img :src="$imageFilter(item.pic,'w_240')" alt="" />
        </div>
        <div class="info_box">
          <p class="title">
            <span v-if="item.title">{{ item.title }}</span>
            <span v-if="item.shi">{{ item.shi }}室</span>
            <span v-if="item.ting">{{ item.ting }}厅</span>
            <span v-if="item.wei">{{ item.wei }}卫</span>
            <span v-if="item.mianji">{{ item.mianji }}m²</span>
            <span v-if="item.sale_price">{{ item.sale_price / 10000 }}万出售</span>
          </p>
          <p class="desc">
            <span v-if="item.shi">{{ item.shi }}室</span>
            <span v-if="item.ting">{{ item.ting }}厅</span>
            <span v-if="item.wei">{{ item.wei }}卫/</span>
            <span v-if="item.mianji">{{ item.mianji }}m²/</span>
          </p>
          <p class="price" v-if="item.sale_price">
            <span>{{ item.sale_price / 10000 }}万</span>
            <span class="time" v-if="item.ctime">{{ item.ctime }}</span>
          </p>
        </div>
        <div class="mask">
          <span class="btn" @click="$emit('viewitem', item)">查看房源</span>
          <span class="btn" @click="$emit('clickitem', item)">发送房源</span>
        </div>
      </div>
    </div>
</template>

<script>
export default {
  name: "",
  components: {},
  data() {
    return {};
  },
  props: {
    info_list: {
      type: Array,
      default: () => {
        return [];
      },
    }
  },
  filters: {
    setPastDateTime(item) {
      // 获取当前日期的年、月、日信息
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();
      const currentDay = currentDate.getDate();
      // 指定的日期（2022-09-06 14:06）
      const specificDate = new Date(item);
      const specificYear = specificDate.getFullYear();
      const specificMonth = specificDate.getMonth();
      const specificDay = specificDate.getDate();
      // 计算过去的天数
      const millisecondsPerDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      const currentDateTimestamp = Date.UTC(currentYear, currentMonth, currentDay);
      const specificDateTimestamp = Date.UTC(specificYear, specificMonth, specificDay);
      const daysPassed = Math.floor((currentDateTimestamp - specificDateTimestamp) / millisecondsPerDay);
      return daysPassed+'天前';
    }
  },
  methods: {
    
  },
};
</script>

<style scoped lang="scss">
.info_list_container {
  box-sizing: border-box;
  overflow-x: hidden;
  border-radius: 4px;
  background-color: #fff;
  &::-webkit-scrollbar {
    display: none;
  }
}
.list {
  padding: 12px 0;
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: space-between;
  .item {
    // width: 128px;
    // min-width: 128px;
    margin-bottom: 12px;
    position: relative;
    &:hover .mask {
      opacity: 1;
    }
    .mask{
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      border-radius: 4px;
      background-color: rgba($color: #000000, $alpha: 0.38);
      opacity: 0;
      transition: .36s;
      .btn {
        height: 30px;
        width: 78px;
        margin: 12px;
        text-align: center;
        line-height: 30px;
        border-radius: 4px;
        font-size: 12px;
        background-color: #fff;
        color: #1381f5;
        cursor: pointer;
      }
    }
    &.emoty{
      height: 0;
    }
    .img_box {
      width: 128px;
      height: 96px;
      margin-right: 12px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        border-radius: 6px;
        object-fit: cover;
      }
    }
    .info_box{
      flex: 1;
      overflow: hidden;
    }
    .title {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-top: 6px;
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: bold;
      color: #2e3c4e;
    }
    .desc{
      margin-bottom: 12px;
      color: #2e3c4e;
    }
    .labels{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 12px;
      .label{
        display: inline-block;
        padding: 2px 5px;
        border-radius: 2px;
        font-size: 12px;
        border: 1px solid #dedede;
        color: #666;
        ~.label{
          margin-left: 4px;
        }
      }
    }
    .price{
      font-weight: bold;
      color: #f44;
      .area{
        margin-right: 12px;
        font-weight: initial;
        font-size: 12px;
        color: #666;
      }
      .unit{
        margin-left: 4px;
        font-weight: initial;
        font-size: 12px;
      }
      .time{
        padding: 0 6px;
        float: right;
        font-size: 13px;
        font-weight: initial;
        color: #888;
      }
    }
  }
}

</style>
