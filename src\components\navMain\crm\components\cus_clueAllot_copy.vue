<template>
    <div>
      <!-- 线索分配 -->
      <div class="header">
        <div class="clue_title">线索分配
          <el-link type="primary" size="small" @click="groupchange(3)">分组管理</el-link>
        </div>
        <!-- <div class="managestaff">
           
        </div> -->
        <div class="flex-row">
          <!-- 手动认领 -->
          <div class="clue_manualAllot">
            <div>
              <el-radio v-model="totalConfig_params.push_type" :label="0"
                >手动认领</el-radio
              >
            </div>
            <div class="clue_prompt">通过公海客户手动认领获取客户线索</div>
          </div>
          <!-- 自动分配 -->
          <div class="clue_manualAllot" style="margin-left: 20px">
            <div>
              <el-radio v-model="totalConfig_params.push_type" :label="1"
                >自动分配</el-radio
              >
            </div>
            <div class="clue_prompt">
              适用于多人团队，支持按照所选基础规则自动分配所有线索
            </div>
          </div>
          <!-- 手动分配 -->
          <template>
            <div class="clue_manualAllot" style="margin-left: 20px">
              <div>
                <el-radio v-model="totalConfig_params.push_type" :label="2"
                  >手动分配</el-radio
                >
              </div>
              <div class="clue_prompt">公海客户统一由管理员转交成员</div>
            </div>
          </template>
        </div>
        <div v-show="totalConfig_params.push_type == 1">
          <div class="rule_title">分配规则</div>
          <div style="margin-bottom: 15px">
            <div class="allocation_rule">
              <el-radio v-model="totalConfig_params.push_style" :label="0">
                按员工顺序轮流分配
              </el-radio>
            </div>
            <div class="rule_text">
              当有新线索进来时，系统查看所有员工，按照顺序自动在员工间轮流分配线索；如在[设置员工账户]
              增加新员工或删除原有员工，会自动加入/剔除分配队列
            </div>
          </div>
          <div class="allocation_rule">
            <el-radio v-model="totalConfig_params.push_style" :label="1">
              按员工私客数量补齐分配
            </el-radio>
          </div>
          <div class="rule_text">
            当有新线索进来时，系统查看所有员工被分配的私客数量，然后优先分配给数量最少的员工
          </div>
          <div v-if="totalConfig_params.push_style==1" class="rule_text">
            <el-radio v-model="totalConfig_params.style_type" :label="1" style="color: #2e3c4e">按全部数量补齐</el-radio>
            <el-radio v-model="totalConfig_params.style_type" :label="2" style="color: #2e3c4e">按每周数量补齐</el-radio>
            <el-radio v-model="totalConfig_params.style_type" :label="3" style="color: #2e3c4e">按每月数量补齐</el-radio>
            <!-- (1:按全部数量补齐,2:按每周数量补齐,3:按每月数量补齐) -->
          </div>
          <div class="allocation_rule"  style="margin-top: 15px">
            <el-radio v-model="totalConfig_params.push_style" :label="2">
              按排期轮流分配
            </el-radio>
          </div>
          <div class="rule_text">
            当有新线索进来时，根据排期自动分配线索，每月循环，当日为空线索进入公海
          </div>

          <!-- <div class="allocation_rule" style="margin-top:15px">
            <el-radio v-model="totalConfig_params.push_style" :label="3">
              按分组权重分配
            </el-radio>
          </div> -->
          <!-- <div class="rule_text">
            当有新线索进来时，系统查看所有员工被分配的线索数量，然后优先分配给数量最少的员工
          </div> -->
          
          <div v-show="totalConfig_params.push_style == 2">
              <cusClueAllotCalendar :datas="totalConfig_params.day" ref="cusClueAllotCalendar"/>
          </div>

          <div v-show="totalConfig_params.push_style != 2">
          <div class="flex-row">
            <div class="choose_title" :class="{chooseA:push_way==1}" @click="groupchange(1)">选择员工</div>
            <div class="choose_title" :class="{chooseA:push_way==2}" @click="groupchange(2)" style="margin-left:10px">选择分组</div>
          </div>
          <!-- 员工 -->
          <div v-if="staff">
            <el-input
            v-model="searchMember"
            prefix-icon="el-icon-search"
            placeholder="请输入搜索词"
            class="choose_employee"
            @input="ChangeInput"
            ></el-input>
            <!-- 选择栏 -->
            <div class="flex-row">
            <!-- 员工列 -->
            <div class="choose_box_left flex-box">
              <div class="choose_box_header">
                <span>员工</span>
              </div>
              <div class="choose_box_content">
                <el-checkbox-group v-model="selected_Member">
                  <el-checkbox
                    v-for="(item, index) in search_user_list"
                    v-model="item.selected"
                    :key="index"
                    class="choose_Multiple_selection"
                    :label="item.id"
                    @change="selectedChange"
                  >
                    {{ item.user_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <!-- 已选择员工列 -->
            <div class="choose_box_right flex-box">
              <div class="choose_box_header">
                <span>已选{{ this.selected_Member.length }}项</span>
                <span style="margin-left: auto" @click="clearSelected">
                  清空
                </span>
              </div>
              <div class="selected_box_content">
                <div
                  class="selected_box_text"
                  v-for="(item, index) in member_detailed"
                  :key="index"
                >
                  <span>{{ item.user_name }}</span>
                  <i
                    class="el-icon-close cancel_selected"
                    @click="deleteEmployee(item, index)"
                  ></i>
                </div>
              </div>
            </div>
            </div>
          </div>
          <!-- 分组 -->
          <div v-if="!staff">
            <div class="addgroup">
              <el-button type="primary" size="small" @click="dialogVisible=true">创建分组</el-button>
            </div>
            <!-- 分组表格 -->
            <div>
              <el-table
              :data="GoupData"
              style="width: 60%"
              @row-click="singleElection"
              highlight-current-row>
              <el-table-column
                label="选择"
                width="100">
                <template slot-scope="scope">
                  <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
                  <el-radio class="radio" v-model="templateSelection" :label="scope.row.id"
                    >&nbsp;</el-radio
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="title"
                label="分组名"
                >
              </el-table-column>
              <el-table-column
                label="操作"
                align="right"
                >
                <template slot-scope="scope">
                  <el-link type="primary" @click="detailsgoup(scope.row)">编辑</el-link>
                  <!-- <el-link type="warning" class="operatebtn" @click="editGoup(scope.row)">编辑</el-link> -->
                  <el-popconfirm
                      title="确定删除该分组吗？"
                      @onConfirm="delGoup(scope.row)">
                      <el-link  slot="reference" class="operatebtn" type="danger">删除</el-link>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
            </div>
            <div class="gouppaging">
              <!-- <el-pagination
                background
                layout="prev, pager, next"
                :total="50">
              </el-pagination> -->
              <el-pagination
                layout="prev, pager, next"
                :total="allgoupdata.total"
                :page-size="allgoupdata.per_page"
                :current-page="allgoupdata.current_page"
                @current-change="onPageChange">
              </el-pagination>
            </div>
            <!-- 编辑组名 -->
            <!-- <el-dialog
              title="编辑分组"
              :visible.sync="Editshow"
              width="30%"
              >
              <el-form ref="form" :model="editGoupdata" label-width="85px">
                <el-form-item label="分组名称：">
                  <el-input v-model="editGoupdata.title"
                  style="width:255px"></el-input>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="Editshow = false">取 消</el-button>
                <el-button type="primary" @click="editttitle">确 定</el-button>
              </span>
            </el-dialog> -->
          </div>
          </div>
          <div>
          <div class="rule_title">线索去重</div>
          <div style="margin-bottom: 15px">
            <div class="allocation_rule">
              <el-radio v-model="totalConfig_params.push_repeat" :label="0">
                去重
              </el-radio>
            </div>
            <div class="rule_text">
              号码唯一、维护人唯一、线索回收自动分配、维护人提醒【默认】
            </div>
          </div>
          <div class="allocation_rule">
            <el-radio v-model="totalConfig_params.push_repeat" :label="1">
              不去重
            </el-radio>
          </div>
          <div class="rule_text">
            重复留资以新增号码线索分配至成员-流转客，不能回收【可选设置】
            </div>
          </div>
          <div v-if="totalConfig_params.push_repeat==1">
            <div class="rule_title">流转客分配模式</div>
            <div class="allocation_rule" style="margin-bottom: 15px">
            <el-radio v-model="totalConfig_params.private_type" :label="1">
              按线索推送规则指定维护人/分组分配
            </el-radio>
            <div class="rule_text">(如未设置，不再分配)</div>
          </div>
          <div class="allocation_rule" style="margin-bottom: 15px">
            <el-radio v-model="totalConfig_params.private_type" :label="2">
              按自动分配规则中员工/分组分配
            </el-radio>
            <div class="rule_text">(如未设置，不再分配)</div>
          </div>
          <div class="allocation_rule" style="margin-bottom: 15px">
            <el-radio v-model="totalConfig_params.private_type" :label="3">
              按选择成员/分组分配
            </el-radio>
            <div class="rule_text">(指定成员/分组将接收所有通过线索不去重规则分配到流转客的客资)</div>
          </div>
          <div v-if="totalConfig_params.private_type == 3">
            <automaticallocation :search_user_list="search_user_list" :user_list="user_list"
            :GoupData="GoupData" :allgoupdata="allgoupdata"  @onPageChange="onPageChange"
            @receivedata="receivedata" @receivegoupdata="receivegoupdata" 
            :totalConfig_params="totalConfig_params"
            ></automaticallocation>
          </div>
          </div>
        </div>
        <div v-show="totalConfig_params.push_type == 0">
          <Manualallocationrules ref="Manualallocationrules" v-show="totalConfig_params.push_type == 0"
          :serverData="serverData" :datalist="datalist" :receivedData="form_info"></Manualallocationrules>
        </div>
        <div v-show="totalConfig_params.push_type == 2">
          <manualallocation ref="manualallocation" v-show="totalConfig_params.push_type == 2"
          :serverData="serverData" :datalist="datalist" :receivedData="copy_status_list">
          </manualallocation>
        </div>
        <!-- 保存 -->
        <div class="footer_btn">
          <el-button type="primary" @click="onSave" v-if="showonSave">确认切换</el-button>
        </div>
        <!-- 添加分组模态框 -->
          <el-dialog
              title="创建分组"
              :visible.sync="dialogVisible"
              width="30%"
              >
              <el-form ref="form" :model="groupdata" label-width="80px">
                <el-form-item label="分组名称">
                  <el-input v-model="groupdata.title"
                  style="width:255px"></el-input>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submittitle">确 定</el-button>
              </span>
            </el-dialog>
        <!-- 管理分组详情 -->
        <el-dialog
              :title="goupname"
              :visible.sync="particularsgoup"
              width="50%"
              >
              <el-form ref="form" :model="editGoupdata" label-width="85px">
                <el-form-item label="分组名称：">
                  <el-input v-model="editGoupdata.title"
                  @blur="editttitle"
                  style="width:255px"></el-input>
                </el-form-item>
              </el-form>
              <div class="addstaff">
                <!-- <el-button type="primary" size="small" @click="editttitle">修改分组名</el-button> -->
              </div>
                <div class="addstaff" style="margin-top:20px">
                  <div style="margin-left: 10px;">
                    <el-input style="width: 200px;" v-model="searchText" placeholder="搜索成员"></el-input>
                  </div>
                  <div>
                    <el-radio-group style="margin-right:10px;" v-model="batchopen" size="small" @change="submitbatchopen">
                      <el-radio-button label="1">批量开启接单</el-radio-button>
                      <el-radio-button label="0">暂停接单</el-radio-button>
                    </el-radio-group>
                    <el-button type="primary" size="small" @click="addstaffshow">添加员工</el-button>
                  </div>
                   
                </div>
                <div class="stafftable">
                  <el-table
                    :data="filteredTableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column
                      type="selection"
                      width="55">
                    </el-table-column>
                    <el-table-column
                      prop="admin.user_name"
                      label="姓名"
                      width="180">
                      <template slot-scope="scope">
                        <div>
                          {{scope.row.admin.user_name}} <span style="color:red" v-if="scope.row.admin.delete>0">(该成员已删除)</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="status"
                      label="接单状态"
                      width="180">
                      <template slot-scope="scope">
                        <el-select
                        :disabled="scope.row.admin.delete>0"
                        :class="scope.row.status==1?'Noordersstatus':'ordersstatus'"
                         v-model="scope.row.status" placeholder="请选择状态"
                          size="small" style="width: 117px;"
                          @change="changegordersvalue(scope.row)">
                            <el-option
                              v-for="item in Receivingorders"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                            </el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="操作"
                      align="center"
                      >
                        <template slot-scope="scope">
                          <el-popconfirm
                              title="确定删除该成员吗？"
                              @onConfirm="deletstaff(scope.row)">
                              <el-button slot="reference" type="danger" size="mini">删除</el-button>
                          </el-popconfirm>
                        </template>
                    </el-table-column>
                  </el-table>
                </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="particularsgoup = false">取 消</el-button>
                <el-button type="primary" @click="particularsgoup = false">确 定</el-button>
              </span>
            </el-dialog>
            <!-- 添加员工模态框 -->
            <el-dialog
              title="成员"
              :visible.sync="show_member_list"
              width="30%"
              >
              <!-- <div style="color: #e6a23c;"> <i class="el-icon-search"></i>可直接搜索成员或手机号</div>
                <el-input
                  placeholder="请输入成员名字或手机号"
                  style="width: 250px;"
                  v-model="input2">
                  <el-button slot="append" icon="el-icon-search" @click="userSearch"></el-button>
                </el-input> -->
                <div class="SerialNumber">
                    <el-input
                      placeholder="请输入成员序号"
                      style="width: 250px;"
                      v-model="paramsstaff.order">
                    </el-input>
                </div>
                <div>
                    <!-- <memberListSingle
                      v-if="show_add_memberA"
                      :list="serverData"
                      :defaultValue="selectedIds"
                      @onClickItem="selecetedMember"
                      :defaultExpandAll="Allfalse"
                      :getCheckedNodes="false"
                      ref="memberList"
                    >
                    </memberListSingle> -->

                    <multipleTree
                      v-if="show_add_memberA"
                      :list="serverDataA"
                      :defaultValue="selectedIds"
                      @onClickItem="selecetedMember"
                      :defaultExpandAll="Allfalse"
                      :getCheckedNodes="false"
                      ref="memberList"
                    >
                    </multipleTree>
                </div>
              <span slot="footer" class="dialog-footer">
                <el-button @click="show_member_list = false">取 消</el-button>
                <el-button type="primary" @click="saveaddstaff">确 定</el-button>
              </span>
            </el-dialog>
            <!-- 分组管理 -->
        <el-dialog
          title="分组管理"
          :visible.sync="Manage_Grouping"
          width="60%"
          >
          <div class="addgroup" style="width: 90%;">
              <el-button type="primary" size="small" @click="dialogVisible=true">创建分组</el-button>
            </div>
            <!-- 分组表格 -->
            <div>
              <el-table
              :data="GoupData"
              style="width: 80%;margin: 0 auto;"
              highlight-current-row
              >
              <!-- <el-table-column
                label="选择"
                width="55">
                <template slot-scope="scope"> -->
                  <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
                  <!-- <el-radio class="radio" v-model="templateSelection" :label="scope.row.id"
                    >&nbsp;</el-radio
                  >
                </template>
              </el-table-column> -->
              <el-table-column
                prop="title"
                label="分组名"
                >
              </el-table-column>
              <el-table-column
                    label="分组总人数" prop="number">
              </el-table-column>
              <el-table-column
                label="操作"
                align="right"
                style="padding-right: 58px;"
                >
                <template slot-scope="scope">
                  <el-link type="primary" @click="detailsgoup(scope.row)">编辑</el-link>
                  <!-- <el-link type="warning" class="operatebtn" @click="editGoup(scope.row)">编辑</el-link> -->
                  <el-popconfirm
                      title="确定删除该分组吗？"
                      @onConfirm="delGoup(scope.row)">
                      <el-link  slot="reference" class="operatebtn" type="danger">删除</el-link>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
            </div>
            <div class="gouppaging">
              <!-- <el-pagination
                background
                layout="prev, pager, next"
                :total="50">
              </el-pagination> -->
              <el-pagination
                layout="prev, pager, next"
                :total="allgoupdata.total"
                :page-size="allgoupdata.per_page"
                :current-page="allgoupdata.current_page"
                @current-change="onPageChange">
              </el-pagination>
            </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="Manage_Grouping = false">取 消</el-button>
            <el-button type="primary" @click="Manage_Grouping = false">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </template>
  <script>
  // import memberListSingle from "./portrait_single.vue";
  import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
  import cusClueAllotCalendar from "./cus_clueAllot/cus_clueAllot_calendar.vue";
  import Manualallocationrules from "./cus_clueAllot/Manual_allocation_rules.vue"
  import manualallocation from "./cus_clueAllot/manualallocation.vue"
  import automaticallocation from "./cus_clueAllot/automaticallocation.vue"
  export default {
    components: {
    // memberListSingle, 
    cusClueAllotCalendar,
    multipleTree,
    Manualallocationrules,
    manualallocation,
    automaticallocation
   },
    data() {
      return {
        // 线索分配配置
        totalConfig_params: {
        },
        push_way:1,
        showonSave:false,
        employee_params: {
          page: 1,
          per_page: 100,
          type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
        },
        user_list: [], // 关联成员列表
        selected_Member: [], // 已选中的成员
        member_detailed: [], // 已选中的成员的详细信息
        searchMember: "", // 搜索成员关键词
        search_user_list: [], // 搜索成员列表
        website_id: "",
        staff:true,//员工
        dialogVisible:false,//创建分组
        Manage_Grouping:false,
        groupdata:{
          title:"",//分组名称
        },
        GoupData:[],//分组数据
        allgoupdata:{},//加分页的分组数据
        Editshow:false,//编辑组名
        editGoupdata:{},//储存编辑组信息
        particularsgoup:false,//分组详情
        goupname:"",//组名详情
        show_member_list:false,//成员模态框
        input2:"",
        show_add_memberA:false,
        selectedIds: [], //默认勾选和展开的节点的 key 的数组
        Allfalse:false,
        datalist: [], // 全部部门人员
        paramsstaff:{
            group_id:"",//组id
            admin_id:"",
        },
        groupingstaff:[],//全部组成员
        specificdata:[],//与组相同id的成员
        userparams:{},//搜索成员
        cluegrouppage:{
          page:1,
          per_page:10
        },
        templateSelection: "",//分组当前选择的行的id
        checkList: [],//分组当前选择的行的数据
        gordersvalue:1,//接单状态
        Receivingorders:[
        {value: 1,label: '正常接单'},
        {value: 0,label: '暂停接单'}
        ],
        grouptitle:"",
        searchText:"",//检索成员
        batchopen:"",
        userid:[],
        serverData: [],
        form_info:{},
        copy_status_list:{},
        serverDataA:[],
      }
    },
    async mounted() {
      this.website_id = this.$route.query.website_id;
      let res = await this.$http.getManagerAuthList({ params: this.employee_params }).catch(() => { });
      if (res.status == 200) {
        this.user_list = res.data.data;
        this.search_user_list = res.data.data;
      }
      this.cluegrouping()
      this.getClueAllocation();
      this.showAccompanyA()
      this.getSetting()
      // new Promise((resolve) => {
      //     resolve()
      // }).then(async () => {
      //     // 获取关联成员
      //     let res= await this.$http.getManagerAuthList({ params: this.employee_params });
      //     this.user_list =res.data.data;
      //     this.search_user_list = res.data.data;
      //     console.log(this.search_user_list,"search_user_list")
      // }).then(() => {
      //     this.getClueAllocation();
      // })
    },
  //   watch:{
  //     'totalConfig_params.push_type':{
  //       handler(newVal, oldVal) {
  //         if(newVal==0||newVal==2){
  //           if(!this.serverData.length){
  //             this.showAccompanyA()
  //           }
  //         }
  //     }
  //   },
  // },
    computed: {
      filteredTableData() {
        if (this.searchText) {
        
          return this.specificdata.filter(item => item.admin.user_name.includes(this.searchText));
        } else {
          return this.specificdata;
        }
      }
    },
    methods: {
      //获取基本配置
      getSetting() {
          this.$http.getSiteCrmSetting().then((res) => {
            if (res.status === 200) {
              this.form_info = res.data;
              this.copy_status_list = JSON.parse(
                JSON.stringify(this.form_info)
              ); // 深拷贝客户状态
            }
          });
        },
      // //配置项的数据
      // receiveDataFromChild(data) {
      //   console.log(data);
      //   this.receivedData = data; // 将从子组件接收到的数据存储在父组件中
      // },
      selectedChange() {
        this.member_detailed = [];
        this.getSelected_Member();
      },
      // 获取站点CRM线索分配配置
      getClueAllocation() {
        this.$http.getClueAllocation().then((res) => {
          if (res.status == 200) {
            this.totalConfig_params = res.data;
            if (this.totalConfig_params.push_way==1) {
              this.push_way = 1
              this.staff = true
            }else{
              this.push_way = 2
              this.staff = false
              if (res.data.push_group_id) {
                this.templateSelection = Number(res.data.push_group_id)
              }
            }
            if(!this.totalConfig_params.push_repeat){
              this.$set(this.totalConfig_params, 'push_repeat', 0);
            }
            if (this.totalConfig_params.push_uid != "") {
              let num = this.totalConfig_params.push_uid.split(',')
              for (let i = 0; i < num.length; i++) {
                num[i] = parseInt(num[i])
              }
              // 如果已经选中的员工不在员工列表中就删除
              let is_exist = [];
              this.search_user_list.map((item) => { //遍历员工列表
                // 遍历已选中的员工列表
                num.map((list) => {
                  // 如果员工存在
                  if (item.id == list) {
                    is_exist.push(item.id);
                  }
                })
              })
              this.selected_Member = is_exist; // 赋值选中并且存在的员工
              this.getSelected_Member();
            }
            this.showonSave = true
          }
        })
      },
      //切换员工与分组
      groupchange(status){
        if(status==1){
          this.staff = true
          this.push_way = 1
          console.log(this.totalConfig_params);
        }else if(status==2){
          this.staff = false
          this.push_way = 2
        }else{
          this.Manage_Grouping = true
        }
      },
      //分组表格单选
      singleElection(row) {
        this.templateSelection = row.id
        console.log(this.templateSelection );
        // this.checkList = this.GoupData.filter((item) => item.id == row.id)
        // console.log(this.selected_Member);
        this.totalConfig_params.push_group_id = String(this.templateSelection)
        // this.selected_Member = []
        // console.log(`该行的编号为${row.id}`)
        // console.log(this.checkList);
      },
      //获取组
      cluegrouping(){
        console.log(this.cluegrouppage);
        this.$http.cluegrouping(this.cluegrouppage.page,this.cluegrouppage.per_page).then((res)=>{
          if(res.status==200){
            console.log(res.data);
            this.allgoupdata = res.data
            this.allgoupdata.per_page = Number(res.per_page)
            this.GoupData = res.data.data
          }
        })
      },
      //分组表格分页
      onPageChange(e){
        this.cluegrouppage.page = e
        console.log(this.cluegrouppage);
        this.cluegrouping()
      },
      //确定提交分组名
      submittitle(){
        if(!this.groupdata.title){
          this.$message.warning("请填写分组!")
        }else{
          this.$http.addgrouptitle(this.groupdata).then((res)=>{
            if(res.status==200){
              this.$message({
                type:"success",
                message:"添加成功"
              })
              this.cluegrouping()
              this.dialogVisible = false
              this.groupdata.title = ''
            }
          })
        }
      },
      //编辑组名
      editGoup(row){
        this.Editshow = true
        this.editGoupdata = row
        this.grouptitle = this.editGoupdata.title
      },
      //确定编辑组名
      editttitle(){ 
        let from = {}
        if(this.editGoupdata.title){
          from.id = this.editGoupdata.id
          from.title = this.editGoupdata.title
        }else{
         this.editGoupdata.title = this.grouptitle 
          this.$message.warning("请输入分组名")
          this.cluegrouping()
          return
        }
        if(from.title){
            this.$http.editgoup(from).then((res)=>{
                if(res.status==200){
                    this.$message({
                        type:"success",
                        message:"修改成功"
                    })
                    this.cluegrouping()
                    // this.Editshow = false
                    // this.particularsgoup = false
                }
            })
        }
      },
      //删除组
      delGoup(row){
        console.log(row);
        this.$http.delegoup(row.id).then((res)=>{
            if(res.status==200){
                this.$message.success("删除成功!")
                this.cluegrouping()
            }
        })
      },
      //获取组内成员
      getstaff(ids){
        this.$http.getgroupEmployees(ids).then((res)=>{
            if(res.status==200){
                this.specificdata = res.data
                // let matchingItems =[]
                // this.groupingstaff.map((item)=>{
                //   if(item.group&&item.group.id === ids){
                //     matchingItems.push(item)
                //   }
                // })
                // console.log(matchingItems);
                // matchingItems.map(item=>{
                //   this.specificdata.push(item)
                // })
            }
        })
      },
      //分组详情
      detailsgoup(row){
        // if(style==1){
        // //  this.Manage_Grouping = false
        // }
        console.log(row);
        this.searchText = ""
        this.specificdata = []
        // this.serverData = []
        this.datalist= []
        this.editGoupdata = row
        this.goupname = row.title
        // this.showAccompanyA()
        this.getstaff(row.id)
        this.paramsstaff.group_id = row.id
        this.particularsgoup = true
      },
      // 获取已选中成员的详细信息
      getSelected_Member() {
        let num = [];
        console.log(this.selected_Member);
        for (let i = 0; i < this.selected_Member.length; i++) {
          // Member = this.user_list.find(item => {item.id == this.selected_Member[i]});
          let Member = "";
          this.user_list.map((item) => {
            if (item.id == this.selected_Member[i]) {
              Member = item;
            }
          })
          if (Member) {
            num.push(Member);
          }
        }
        num.map(item => {
          this.member_detailed.push(item)
        })

      },
      // 删除，指定已选择的员工
      deleteEmployee(item) {
        let subIndex = this.selected_Member.indexOf(item.id);
        // 删除已选中的成员 左部分选择
        this.selected_Member.splice(subIndex, 1);
        // 删除已选中的成员的详细信息 右部分选择
        this.member_detailed.splice(subIndex, 1);
      },
      // 清除已选员工
      clearSelected() {
        this.member_detailed = [];
        this.selected_Member = [];
      },
      //向组内添加成员
      addstaffshow(){
        // this.serverDataA = []
        if(this.serverDataA.length){
          this.show_add_memberA = true
        }else{
          this.showAccompany()
        }
        this.Allfalse = true
        this.show_member_list = true
      },
      // 删除员工
      deletstaff(row){
        this.$http.delstaff(row.id).then((res)=>{
          if(res.status==200){
            this.$message({
              type:"success",
              message:"删除成功"
            })
            this.specificdata = []
            this.getstaff(this.paramsstaff.group_id)
          }
        })
      },
     //批量选择人员
     handleSelectionChange(val){
      this.userid = val.map(item=>{
        return item.id
      })
     },
     //批量更改接单状态
     submitbatchopen(){
      if(!this.userid.length){
        this.batchopen = ""
        return this.$message.warning("请选择成员！")
      }
      let params = {
        id : this.userid.join(",") ,
        status : this.batchopen,
      }
      this.changegordersvalue(params)
     },
    //更改接单状态
    changegordersvalue(row){
      let text = '更改为正常接单状态，系统将正常分配客资信息, 是否继续?'
      let data = {
        id:"",
        status:''
      }
      if(row.status!= 1){
          text = '更改为暂停接单状态，系统将不在分配客资信息, 是否继续?'
        }
        this.$confirm(text, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (typeof row.id !== 'string') {
              // 如果不是字符串类型，则将其转换为字符串
              row.id = String(row.id);
          }

          data.id = row.id
          data.status = row.status
          this.$http.setstaffstatus(data).then(res=>{
            if(res.status==200){
              this.$message.success("修改成功！");
              this.batchopen = ""  
              this.getstaff(this.paramsstaff.group_id)
            }
          })
        }).catch(() => {
         if(row.status==1){
          row.status = 0
         } else{
          row.status = 1
         }
          this.$message({
            type: 'info',
            message: '已取消修改！'
          });          
        });
    
    },
      //搜索员工
      userSearch(){
        this.userparams.user_name = this.input2
        this.show_add_memberA = false
        this.Allfalse = true
        this.showAccompany()
      },
      // 选中变化时触发
      selecetedMember(e) {
        this.paramsstaff.admin_id = e.checkedKeys.join(",");
      },
      //确定添加员工
      saveaddstaff(){
        if(!this.paramsstaff.order){
            delete this.paramsstaff.order
        }else{
            this.paramsstaff.order = Number(this.paramsstaff.order)
        }
        this.$http.groupemployees(this.paramsstaff).then((res)=>{
            if(res.status==200){
                this.$message.success("添加成功!")
                this.paramsstaff.order = ""
                this.show_member_list = false
                this.specificdata = []
                this.getstaff(this.paramsstaff.group_id)
            }
        })
      },
      //获取员工
      showAccompanyA(){
      // this.appointAccompany = []
      this.$http.getDepartmentMemberList(this.userparams).then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          // console.log(this.l_list,"");
          // this.show_add_memberA = true
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: *********,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key+=父级
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      })
    },
    showAccompany(){
      // this.appointAccompany = []
      this.$http.getDepartmentMemberList(this.userparams).then((res) => {
        if (res.status == 200) {
          this.serverDataA = JSON.parse(JSON.stringify(res.data))
          // console.log(this.l_list,"");
          this.show_add_memberA = true
          this.serverDataA.push({
            id: 999,
            name: "未分配部门成员",
            order: *********,
            pid: 0,
            subs: this.serverDataA[0].user
          })
          this.recursionData(this.serverDataA);
          // 当键值key重复就更新key+=父级
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      })
    },
     // 递归数据处理
     recursionData(data) {
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    //接收流转客成员分配设置数据
    receivedata(e){
        // console.log(e);
        this.totalConfig_params.private_push_way = e.private_push_way
        if(e.private_push_uid.length){
          this.totalConfig_params.private_push_uid = e.private_push_uid.join(",")
        }else{
          this.totalConfig_params.private_push_uid = ""
        }
      },
    //接收流转客分组分配设置数据
    receivegoupdata(e){
        // console.log(e);
        this.totalConfig_params.private_push_way = e.private_push_way
        this.totalConfig_params.private_group_id = e.private_group_id
    },
      // 保存
      onSave() {
        if(this.totalConfig_params.push_type==0){
          this.$refs.Manualallocationrules.Confirmswitch()
        }
        if(this.totalConfig_params.push_type==2){
          this.$refs.manualallocation.Confirmswitch()
        }
        this.$set(this.totalConfig_params, "push_uid", this.selected_Member.toString())
        if(this.push_way == 1){
           this.totalConfig_params.push_way = 1
        }else(
          this.totalConfig_params.push_way = 2
        )
        
        const params = {...this.totalConfig_params};
        //按排期分配数据
        if(params.push_style == 2){
          const days = this.$refs.cusClueAllotCalendar.getCalendarData();
          params.day = days.map(e => {
            return {
              day: e.day,
              admin_id: e.admin_id || 0,
              group_id: e.group_id || 0
            }
          })
        }
    

        this.$http.setClueAllocationNew(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getSetting()
          }
        }).catch((res) => {
          this.$message.error(res);
        })
      },
      // 搜索员工
      ChangeInput(val) {
        this.search_user_list = this.user_list
        let num = [];
        this.search_user_list.map(item => {
          if (item.user_name.indexOf(val) != -1) {
            num.push(item)
          }
        })
        this.search_user_list = num;
      }
    }
  }
  </script>
  <style scoped lang="scss">
  ::v-deep.header {

    .clue_title {
      color: #2e3c4e;
      margin-bottom: 24px;
      display:flex;
      width: 61%;
      justify-content: space-between;
    }
    .rule_title {
      color: #2e3c4e;
      margin: 24px 0;
      font-size: 14px;
    }
    .managestaff{
      width: 952px;
      margin-bottom: 10px;
      display: flex;
      justify-content: flex-end;
    }
    .rule_text {
      font-size: 12px;
      color: #8a929f;
      margin-top: 10px;
      margin-left: 24px;
    }
    .choose_title {
      font-size: 14px;
      color: #2e3c4e;
      margin: 15px 0;
      cursor: pointer;
    }
    .chooseA{
      color: #409EFF;
    }
    .clue_manualAllot {
      width: 304px;
      background-color: #f8f8f9;
      padding: 20px;
      box-sizing: border-box;
      border-radius: 6px;
      .el-radio {
        .el-radio__label {
          color: #2e3c4e;
        }
      }
    }
    .choose_employee {
      margin-bottom: 15px;
      .el-input__inner {
        width: 400px;
      }
    }
    .choose_box_left {
      .choose_box_content {
        width: 100%;
        height: 310px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .el-checkbox-group {
          display: flex;
          flex-direction: column;
        }
        .choose_Multiple_selection {
          background-color: #f4f4f5;
          padding: 5px 8px;
          border-bottom: 1px solid #e3e3e5;
          margin-right: 0px;
          .el-checkbox__label {
            font-size: 12px;
            color: #2e3c4e;
          }
        }
        .choose_Multiple_selection:last-child {
          border-bottom: none;
        }
      }
    }
    .choose_box_right,
    .choose_box_left {
      width: 240px;
      height: 346px;
      border: 1px solid #e3e3e5;
      border-radius: 3px;
      .choose_box_header {
        display: flex;
        background-color: #f8f8f7;
        padding: 10px 0;
        border-bottom: 1px solid #e3e3e5;
        & span {
          color: #2e3c4e;
          font-size: 12px;
          font-weight: 600;
          margin-left: 10px;
        }
      }
    }
    .choose_box_right {
      margin-left: 15px;
      .choose_box_header {
        & span:nth-child(2) {
          font-weight: 500;
          color: #3961e4;
          margin-right: 10px;
          cursor: pointer;
        }
        & span:last-child {
          font-weight: 500;
          color: #2e3c4e;
          margin-right: 10px;
        }
      }
      .selected_box_content {
        width: 100%;
        height: 310px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .selected_box_text:first-child {
          margin-top: 10px;
        }
        .selected_box_text {
          display: flex;
          align-items: center;
          background: #f4f4f5;
          padding: 2px 5px;
          margin: 0 10px 10px 10px;
          border-radius: 3px;
          font-size: 12px;
          color: #2e3c4e;
          .cancel_selected {
            width: 12px;
            height: 10px;
            margin-left: auto;
            cursor: pointer;
          }
        }
      }
    }
    .allocation_rule {
      .el-radio {
        .el-radio__label {
          color: #2e3c4e;
        }
      }
    }
  }
  .addgroup{
    width: 50%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .addstaff{
    width: 92%;
    display: flex;
    justify-content: space-between;
  }
  .ordersstatus{
    ::v-deep .el-input__inner{
      color: #f56c6c;
      background: #fef0f0;
      border: 1px solid #fbc4c4;
    }
    ::v-deep .el-select__caret{
        color: #f56c6c;
     }
  }
  .Noordersstatus{
    ::v-deep .el-input__inner{
      background-color: #f0f9eb;
      border-color: #e1f3d8;
      color: #67c23a;
    }
    ::v-deep .el-select__caret{
        color: #67c23a;
     }
  }
  .stafftable{
    width: 90%;
    height: 550px;
    margin: 0 auto;
    overflow-y:auto;
  }
  /deep/ .el-table th>.cell{
    padding-right: 58px;

  }
  .gouppaging{
    width: 50%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
  .SerialNumber{
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .operatebtn{
    margin-left: 10px;
    margin-right: 10px;
  }
  .footer_btn {
    margin-top: 30px;
    .el-button {
      // color: #2e3c4e;
      padding: 10px 30px;
      // background-color: #F4F4F3;
      // border: none;
    }
  }
  .clue_prompt {
    font-size: 12px;
    color: #8a929f;
    margin-left: 24px;
    margin-top: 10px;
  }
  </style>