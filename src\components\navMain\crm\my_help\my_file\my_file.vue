<template>
  <div class="pages">
    <div class="header-title div row">
      <div class="ht-title">我的文件库</div>
      <div style="margin-right: 24px">
        <el-button
          size="mini"
          type="primary"
          @click="onCreateFile(2)"
          class="el-icon-plus"
          >上传临时素材</el-button
        >
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm div row top" style="margin-bottom: 24px">
          <!-- 写筛选条件暂时没有 -->
          <span class="text" style="font-size: 14px; color: #8a929f"
            >筛选条件：</span
          >
          <myLabel :arr="file_list" @onClick="onClickType"></myLabel>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right">
              <el-input
                size="small"
                placeholder="请输入标题"
                style="width: 256px; margin-left: 12px"
                v-model="params.keywords"
                @change="onChangeKeywords"
              >
                <span slot="append" class="el-icon-search"></span>
              </el-input>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
            <!-- <el-table-column prop="mediaid" label="企微素材ID">
            </el-table-column> -->
            <el-table-column prop="title" label="标题"></el-table-column>
            <el-table-column label="素材类型" v-slot="{ row }">
              {{ row.type | fileterType }}
            </el-table-column>
            <el-table-column label="有效状态" v-slot="{ row }">
              {{ row.status | filterStatus }}
            </el-table-column>
            <!-- <el-table-column prop="url" label="素材链接"> </el-table-column> -->
            <el-table-column
              prop="created_at"
              label="添加时间"
            ></el-table-column>
            <el-table-column label="操作" v-slot="{ row }">
              <el-link type="primary" @click="toEdit(row)">编辑</el-link>
              <el-popconfirm
                title="确定删除吗？"
                style="margin: 0 10px"
                @onConfirm="del(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      width="660px"
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
    >
      <el-form
        :model="form_file"
        label-position="left"
        label-width="128px"
        class="l_item"
      >
        <el-form-item label="素材类型：">
          <el-radio-group
            @change="onChangemedialinshi"
            v-model="form_file.type"
            :disabled="!isAdd"
          >
            <el-radio
              v-for="item in fileLabel"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="上传素材："
          v-if="form_file.type != 5 && form_file.type !== 6"
        >
          <el-upload
            :limit="1"
            :headers="myHeader"
            :action="user_avatar"
            :on-success="handleSuccessAvatarTemporary"
            :on-remove="handleRemoveAvatarTemporary"
            :on-error="handleError"
            :on-change="handleChange"
            :accept="fileaccept"
            ref="uploadfile"
            :file-list="fileList"
            :class="{
              'upload-demo': form_file.type == 2 || form_file.type == 4,
              'avatar-uploader': !(form_file.type == 2 || form_file.type == 4),
            }"
            :show-file-list="
              form_file.type == 2 || form_file.type == 4 ? true : false
            "
          >
            <!--  class="upload-demo" -->
            <!-- <el-button class="el-icon-download" size="small"
              >本地上传</el-button
            > -->
            <template v-if="form_file.type == 2 || form_file.type == 4">
              <el-button class="el-icon-download" size="small"
                >本地上传</el-button
              >
            </template>
            <template v-else-if="form_file.type == 3">
              <video
                class="file_img"
                v-if="form_file.url"
                :src="form_file.url"
                alt=""
              />
              <i v-else class="el-icon-plus"></i>
            </template>
            <template v-else>
              <img
                class="file_img"
                v-if="form_file.url"
                :src="form_file.url"
                alt=""
              />
              <i v-else class="el-icon-plus"></i>
            </template>
            <div slot="tip" class="el-upload__tip">
              <span v-if="form_file.type == 1">
                只能上传图片文件，且不超过10M
              </span>
              <span v-if="form_file.type == 2">
                只能上传音频文件，且不超过2M
              </span>
              <span v-if="form_file.type == 3">
                只能上传视频文件，且不超过10M
              </span>
              <span v-if="form_file.type == 4"> 上传文件，不超过20M </span>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="素材标题：">
          <el-input
            style="width: 300px"
            maxlength="20"
            v-model="form_file.title"
            placeholder="请填写素材标题"
          ></el-input>
        </el-form-item>
        <template v-if="form_file.type == 5">
          <el-form-item label="跳转链接：">
            <el-input
              style="width: 300px"
              v-model="msg.link"
              placeholder="请填写跳转链接"
            ></el-input>
            <el-tooltip
              placement="top-start"
              width="200"
              trigger="hover"
              effect="light"
            >
              <div slot="content" style="line-height: 1.5">
                请输入完整的链接例如:https://www.baidu.com
              </div>
              <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
              <span icon="el-icon-info" type="danger"
                ><i class="el-icon-info" style="color: #f56c6c"></i
              ></span>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="描述：">
            <el-input
              style="width: 300px"
              maxlength="20"
              v-model="msg.desc"
              placeholder="请填写描述"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="上传素材：">
            <el-upload
              class="avatar-uploader"
              :headers="myHeader"
              :action="user_avatar"
              :on-success="handleSuccessAvatarTemporary1"
              :on-remove="handleRemoveAvatarTemporary1"
              :on-error="handleError"
              :on-change="handleChange"
              :accept="fileaccept"
              ref="uploadfile"
              :show-file-list="false"
            >
              <img
                class="file_img"
                v-if="msg.imgUrl"
                :src="msg.imgUrl"
                alt=""
              />
              <i v-else class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span> 只能上传图片文件，且不超过10M </span>
              </div>
            </el-upload>
          </el-form-item>
        </template>
        <template v-if="form_file.type == 6">
          <el-form-item label="小程序页面地址：">
            <el-input
              style="width: 300px"
              maxlength="20"
              v-model="msg.page"
              placeholder="请填写小程序页面地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="小程序id：">
            <el-input
              style="width: 300px"
              maxlength="20"
              v-model="msg.appid"
              placeholder="请填写小程序appid"
            ></el-input>
          </el-form-item>
          <el-form-item label="上传素材：">
            <el-upload
              class="avatar-uploader"
              :headers="myHeader"
              :action="user_avatar"
              :on-success="handleSuccessAvatarTemporary1"
              :on-remove="handleRemoveAvatarTemporary1"
              :on-error="handleError"
              :on-change="handleChange"
              :accept="fileaccept"
              ref="uploadfile"
              :show-file-list="false"
            >
              <img
                class="file_img"
                v-if="msg.imgUrl"
                :src="msg.imgUrl"
                alt=""
              />
              <i v-else class="el-icon-plus"></i>
              <!-- <el-button class="el-icon-download" size="small"
                >本地上传</el-button
              > -->
              <div slot="tip" class="el-upload__tip">
                <span> 只能上传图片文件，且不超过10M </span>
              </div>
            </el-upload>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCreate = false">取 消</el-button>
        <el-button
          type="primary"
          @click="onCreatetemporary"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "@/components/navMain/crm/components/my_label";
import config from "@/utils/config";
export default {
  name: "file",
  components: {
    myLabel,
  },
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        type: 0,
      },
      dialogCreate: false,
      titleMap: {
        file: "素材",
        t_file: "临时素材",
      },
      dialogTitle: "t_file",
      form_file: {
        pic_url: ""
      },
      file_list: [
        { id: 0, name: "全部", type: "all" },
        { id: 1, name: "图片素材", type: "image" },
        { id: 2, name: "音频素材", type: "voice" },
        { id: 3, name: "视频素材", type: "video" },
        { id: 4, name: "文件素材", type: "file" },
        { id: 5, name: "H5素材", type: "h5" },
        { id: 6, name: "小程序素材", type: "min" },
      ],
      user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      multipleSelection: [],
      is_button_loading: false,
      msg: {
        imgUrl: ""
      },
      isAdd: true,
      fileList: []
    };
  },
  mounted() {
    this.getDataList();
  },
  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    fileLabel() {
      return this.file_list
        .filter((item) => {
          return item.id;
        })
        .concat([]);
    },
    fileaccept() {
      var val = "";
      switch (this.form_file.type) {
        case 1:
        case 5:
        case 6:
          val = ".jpg,.png";
          break;
        case 2:
          val = ".amr";
          break;
        case 3:
          val = ".mp4";
          break;
        default:
          break;
      }
      return val;
    },
  },
  filters: {
    fileterType(e) {
      let arr = [
        { id: 0, name: "未知" },
        { id: 1, name: "图片素材" },
        { id: 2, name: "录音素材" },
        { id: 3, name: "视频素材" },
        { id: 4, name: "文件素材" },
        { id: 5, name: "H5素材" },
        { id: 6, name: "小程序素材" },
      ];
      const form = arr.find((item) => {
        return item.id == e;
      });
      if (form) {
        return form.name;
      } else {
        return "未知";
      }
    },
    filterStatus(e) {
      let arr = [
        { id: 0, name: "临时有效" },
        { id: 1, name: "已过期" },
        { id: 2, name: "永久有效" },
      ];
      const form = arr.find((item) => {
        return item.id == e;
      });
      return form.name;
    },
  },
  methods: {
    handleChange(file) {
      //获取上传文件大小
      let imgSize = Number(file.size / 1024 / 1024);
      if (
        (this.form_file.type == 1 || this.form_file.type == 3) &&
        imgSize > 10
      ) {
        this.$message.error("图片或视频文件不能大于10M，请重新上传！");
        return;
      }
      if (this.form_file.type == 2 && imgSize > 2) {
        this.$message.error("音频文件不能大于2M，请重新上传！");
        return;
      }
      if (this.form_file.type == 4 && imgSize > 20) {
        this.$message.error("文件不能大于20M，请重新上传！");
        return;
      }
      if (
        (this.form_file.type == 5 || this.form_file.type == 6) &&
        imgSize > 10
      ) {
        this.$message.error("图片不能大于10M，请重新上传！");
        return;
      }
    },
    handleError() {
      this.$message.error("请检查上传文件类型是否正确");
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleClick() {
      this.params.page = 1;
      this.getDataList();
    },
    handleSuccessAvatarTemporary(response) {
      this.form_file.url = response.url;
      this.fileList = [{
        name: "",
        url: response.url,
        uid: 1
      }]
    },
    handleRemoveAvatarTemporary1(response) {
      console.log(response);
      this.msg.imgUrl = response.url;
      console.log(this.msg.imgUrl);
    },
    handleSuccessAvatarTemporary1(response) {
      this.msg.imgUrl = response.url;
    },
    handleRemoveAvatarTemporary(response) {
      this.form_file.url = response.url;
      this.fileList = []
    },
    onChangemedialinshi(e) {
      var category = config.CATEGORY_IM_IMAGE;
      switch (e) {
        case 1:
        case 5:
        case 6:
          category = config.CATEGORY_IM_IMAGE;
          break;
        case 2:
          category = config.CATEGORY_IM_VOICE;
          break;
        case 3:
          category = config.CATEGORY_IM_VIDEO;
          break;
        case 4:
          // category = config.CATEGORY_IM_FILE;
          category = config.CATEGORY_IM_FILE_2;
          break;
        default:
          break;
      }
      this.$nextTick(() => {
        this.$refs.uploadfile && this.$refs.uploadfile.clearFiles();
        this.form_file.url = ''
        this.fileList = []
      })

      this.user_avatar = `/api/common/file/upload/admin?category=${category}`;
      if (e == 5 || e == 6) {
        this.msg.imgUrl = "";
      }
    },
    toEdit(row) {
      console.log(row);
      this.isAdd = false
      this.form_file.type = row.type
      if (row.type == 2 || row.type == 4) {
        this.fileList = [{
          name: '',
          url: row.url,
          uid: 1
        }]

      }
      if (row.type != 6 && row.type != 5) {
        this.form_file = {
          id: row.id,
          url: row.url,
          type: row.type,
          title: row.title,
          mediaid: row.mediaid
        }
      } else {
        this.form_file = {
          id: row.id,
          type: row.type,
          title: row.title,
        }
        if (row.type == 5) {
          this.msg = {
            imgUrl: row.img_url,
            desc: row.desc,
            link: row.url,

          }
        }
        if (row.type == 6) {
          this.msg = {
            imgUrl: row.img_url,
            appid: row.appid,
            page: row.url

          }
        }
      }
      let category = config.CATEGORY_IM_IMAGE;
      switch (+this.form_file.type) {
        case 1:
        case 5:
        case 6:
          category = config.CATEGORY_IM_IMAGE;
          break;
        case 2:
          category = config.CATEGORY_IM_VOICE;
          break;
        case 3:
          category = config.CATEGORY_IM_VIDEO;
          break;
        case 4:
          // category = config.CATEGORY_IM_FILE;
          category = config.CATEGORY_IM_FILE_2;
          break;
        default:
          break;
      }

      this.user_avatar = `/api/common/file/upload/admin?category=${category}`;
      this.dialogCreate = true;

    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmMyCustomerFileData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    del(row) {
      this.$http.delCrmMyFileData(row.id).then((res) => {
        if (res.status === 200) {
          this.$message.success(res.message || '删除成功')
          this.getDataList()
        }
      });
    },

    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onCreateFile(type) {

      if (type == 1) {
        this.dialogTitle = "file";
        this.form_file = {
          url: "",
          title: "",
        };
      } else {
        this.dialogTitle = "t_file";
        this.form_file = {
          url: "",
          title: "",
          type: 1,
        };
      }
      this.isAdd = true
      this.form_file.type = this.params.type || 1
      this.onChangemedialinshi(+this.form_file.type)
      this.dialogCreate = true;
      setTimeout(() => {
        this.$refs.uploadfile && this.$refs.uploadfile.clearFiles();
      }, 100);
    },
    onCreatetemporary() {
      if (!this.form_file.title) {
        this.$message.error("请填写素材标题");
        return;
      }

      let file = {};
      if (this.dialogTitle !== "file") {
        file = Object.assign({}, this.form_file);
        if (this.form_file.type == 5 || this.form_file.type == 6) {
          if (this.form_file.type == 5) {
            if (!this.msg.link) {
              this.$message.warning("请输入跳转链接");
              return;
            }
            if (!this.msg.desc) {
              this.$message.warning("请输入描述");
              return;
            }
            if (!this.msg.imgUrl) {
              this.$message.warning("请上传素材");
              return;
            }
            delete this.msg.page;
            delete this.msg.appid;
          }
          if (this.form_file.type == 6) {
            if (!this.msg.page) {
              this.$message.warning("请输入小程序页面地址");
              return;
            }
            if (!this.msg.appid) {
              this.$message.warning("请输入小程序appid");
              return;
            }
            if (!this.msg.imgUrl) {
              this.$message.warning("请上传素材");
              return;
            }
            delete this.msg.desc;
            delete this.msg.link;
          }

          file.msg = JSON.stringify(this.msg);
          delete file.url;
        }
      }
      this.is_button_loading = true;
      if (this.isAdd) {
        this.$http
          .setCrmMyFileData(file)
          .then((res) => {
            this.is_button_loading = false;
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
              this.dialogCreate = false;
              this.form_file = {
                url: "",
                title: "",
                type: "image",
              };
            }
          })
          .catch(() => {
            this.is_button_loading = false;
          });
      } else {
        this.$http
          .editCrmMyFileData(file)
          .then((res) => {
            this.is_button_loading = false;
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
              this.dialogCreate = false;
              this.form_file = {
                url: "",
                title: "",
                type: "image",
              };
            }
          })
          .catch(() => {
            this.is_button_loading = false;
          });
      }


    },
    onClickType(e) {
      this.params.type = e.id;
      this.params.page = 1;
      this.getDataList();
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .top {
    align-items: center;
  }
  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }
  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    margin: -24px -24px 24px;
    justify-content: space-between;
    align-items: center;
    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }
}
.l_item ::v-deep .el-radio {
  margin-bottom: 10px;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  width: 178px;
  height: 178px;
  text-align: center;

  overflow: hidden;
}
::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep .avatar-uploader .el-icon-plus {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.file_img {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
