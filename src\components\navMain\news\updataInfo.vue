<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
    </div>-->
    <el-form label-width="100px" :model="form" ref="formRules" :rules="rules">
      <el-form-item label="标题：" prop="title">
        <el-input v-model="form.title"></el-input>
      </el-form-item>
      <el-form-item label="初始阅读量：" prop="read_total">
        <el-input v-model="form.read_total"></el-input>
      </el-form-item>
      <el-form-item label="排序：" prop="sort">
        <el-input v-model="form.sort"></el-input>
      </el-form-item>
      <el-form-item label="分类：">
        <el-select v-model="form.news_category_id">
          <el-option
            v-for="item in news_category_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="form.content_category">
          <el-option
            v-for="item in content_category_list"
            :key="item.value"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="缩略图模式："
        v-if="website_info.website_mode_category === 2"
      >
        <el-select v-model="form.list_category" placeholder="请选择">
          <el-option
            v-for="item in list_category_list"
            :key="item.id"
            :label="item.description"
            :value="+item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="tolink" v-if="Number(form.content_category) === 2">
        <el-form-item label="网址：" prop="out_link_address">
          <el-input
            style="width:500px"
            v-model="form.out_link_address"
          ></el-input>
        </el-form-item>
        <el-form-item label="简述：" prop="content">
          <el-input type="textarea" autosize v-model="wai_content"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="内容描述：">
        <el-input type="textarea" v-model="form.description"></el-input>
      </el-form-item>
      <el-form-item label="作者：">
        <el-input v-model="form.author"></el-input>
      </el-form-item>
      <el-form-item label="缩略图：">
        <img :src="form.img" alt="" />
        <img v-if="form.img_2" :src="form.img_2" alt="" />
        <img v-if="form.img_3" :src="form.img_3" alt="" />
        <div class="div row">
          <el-upload
            :headers="myHeader"
            :action="information_upload_type"
            :on-success="handleSuccessnews"
            list-type="picture-card"
            :on-preview="handlePictureCardPreviewnews"
            :on-remove="handleRemovenews"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="newsVisible">
            <img width="100%" :src="newsImageUrl" alt />
          </el-dialog>
        </div>
      </el-form-item>
      <el-form-item
        label="内容："
        class="ueditor"
        prop="cont"
        v-if="Number(form.content_category) === 1"
      >
        <UE
          :value="ueditor.value"
          :config="ueditor.config"
          @input="inputUe"
          ref="ue"
        ></UE>
      </el-form-item>
      <div class="btn-box">
        <el-form-item size="large">
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button @click="goBack">返回管理列表</el-button>
        </el-form-item>
      </div>
    </el-form>
  </el-main>
</template>

<script>
/* eslint-disable */
import UE from "@/components/ueditor";
import config from "@/utils/config";
import { mapState } from "vuex";
export default {
  name: "updataInfo",
  components: { UE },
  data() {
    return {
      is_link: false,
      form: {
        content: "",
        title: "",
        content_category: "1",
        img: "",
        img_2: "",
        img_3: "",
        news_category_id: "1",
        list_category: 1,
        author: "",
        read_total: "",
        sort: "",
        out_link_address: "",
        description: "",
        project_id: 0,
      },
      news_category_list: [],
      content_category_list: [],
      ueditor: {
        value: "",
        config: {
          initialFrameWidth: "100%",
        },
      },
      wai_content: "",
      ue: "ue",
      newsVisible: false,
      newsImageUrl: "",
      info_id: "",
      rules: {
        title: [{ required: true, trigger: "blur", message: "请输入标题" }],
        cont: [{ required: true, trigger: "blur", message: "请输入内容" }],
        content: [{ required: true, trigger: "blur", message: "请输入简述" }],
        out_link_address: [
          { required: true, trigger: "blur", message: "请输入外部链接" },
        ],
        sort: [{ required: true, trigger: "blur", message: "请输入排序" }],
        read_total: [
          { required: true, trigger: "blur", message: "请输入初始阅读量" },
        ],
      },
      information_upload_type: `/api/common/file/upload/admin?category=${config.INFORMATION_IMG}`,
      list_category_list: [],
      img_list: [],
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
    ...mapState(["website_info"]),
  },
  mounted() {
    this.info_id = this.$route.query.id;
    this.getData();
    this.content_category_list = this.$getDictionary("CONTENT_CATEGORY");
    this.list_category_list = this.$getDictionary("NEWS_LIST_CATEGORY");
  },
  methods: {
    // 获取楼盘资讯分类
    getNewsCategoryList() {
      if (this.website_info.website_mode_category == 2) {
        this.$http.getNewsCategoryList().then((res) => {
          if (res.status === 200) {
            this.news_category_list = res.data;
          }
        });
      } else {
        this.news_category_list.push({ id: 1, name: "楼盘动态" });
      }
    },
    getData() {
      this.$http.queryInfo(this.info_id).then((res) => {
        if (res.status === 200) {
          this.form = res.data;
          if (Number(this.form.content_category) === 1) {
            this.ueditor.value = this.form.content;
            // this.$refs.ue.setContent(this.form.content);
          } else {
            this.wai_content = res.data.content;
          }
          this.getNewsCategoryList();
          this.form.content_category = this.form.content_category.toString();
          this.form.list_category = this.form.list_category;
        }
      });
    },
    // 缩略图
    handleRemovenews(file, fileList) {
      this.img_list = fileList;
    },
    handlePictureCardPreviewnews(file) {
      this.newsVisible = true;
      this.newsImageUrl = file.response.url;
    },
    handleSuccessnews(response, file, fileList) {
      this.img_list = fileList;
    },
    goBack() {
      this.$router.back();
    },
    onSubmit() {
      if (this.img_list.length > 0) {
        this.form.img = this.img_list[0].response.url || this.form.img;
        this.form.img_2 = this.img_list[1].response.url || this.form.img_2;
        this.form.img_3 = this.img_list[2].response.url || this.form.img_3;
      }
      if (this.form.content === "" && this.form.content_category == 1) {
        this.$message.error("请输入内容");
        return;
      }
      if (this.form.content === "" && this.form.content_category == 2) {
        delete this.form.content;
      }
      // this.form.content = this.$refs.ue.getUEContent();
      this.$http.updataInfo(this.form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.$goPath("/management_page");
        }
      });
    },
    inputUe(obj) {
      if (Number(this.form.content_category) === 1) {
        this.form.content = obj.content;
      } else {
        this.form.content = this.wai_content;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.ueditor {
  margin: 20px 0;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
img {
  width: 100px;
  height: 100px;
  margin-right: 10px;
}
// .el-form {
// 	padding-top: 30px;
// }
.el-input,
.el-select,
.el-textarea {
  width: 300px;
}
</style>
