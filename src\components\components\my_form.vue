<template>
  <el-form
    :inline="form_create.inline"
    :model="form_create.model"
    :label-width="form_create.label_width"
    :rules="form_create.rules"
  >
    <el-form-item
      v-for="(item, index) in form_create.formLabel"
      :key="index"
      :label="item.label"
      :prop="item.model"
    >
      <!-- 根据type标签显示内容 -->
      <el-input
        v-model="form_create.model[item.model]"
        :placeholder="item.placeholder || '请输入'"
        v-if="item.type === 'input'"
        :type="item.inputType"
        :rows="item.inputRows"
        :style="{
          width: item.inputWidth,
        }"
        :step="1"
        min="0"
        :disabled="item.disabled"
      >
        <template :slot="item.inputSlot">{{ item.slotVal }}</template>
      </el-input>
      <el-select
        v-model="form_create.model[item.model]"
        :placeholder="item.placeholder || '请选择'"
        v-if="item.type === 'select'"
      >
        <!--如果是select或者checkbox 、Radio就还需要选项信息-->
        <el-option
          v-for="(item, index) in item.opts"
          :key="index"
          :label="item.description"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-switch
        v-model="form_create.model[item.model]"
        v-if="item.type === 'switch'"
      ></el-switch>
      <el-radio-group
        v-if="item.type === 'radio'"
        v-model="form_create.model[item.model]"
      >
        <el-radio-button
          v-for="(item, index) in item.opts"
          :key="index"
          :label="item.value"
          >{{ item.description }}</el-radio-button
        >
      </el-radio-group>
      <el-date-picker
        v-model="form_create.model[item.model]"
        type="date"
        placeholder="选择日期"
        v-if="item.type === 'date'"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
    </el-form-item>
    <!--留一个插槽-->
    <el-form-item><slot name="formItem"></slot></el-form-item>
    <el-form-item v-if="isClick">
      <el-button type="primary" @click="onClick">确认</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
/**
 * @date  2021年4月19日16:43:27
 * @form_create {Object} 父组件传入信息
 * @model {Object} 表单内容
 * @label_width {string} 表单域标签宽度
 * @rules {Arrary} 表单验证规则
 * @formLabel {Arrary} 表单内容
 * @inputType {String} 输入框组件类型  text textarea number
 * @inputRows {String | Number} 输入框行数
 * @inputWidth {String} 输入框宽度
 * @disabled {Boolean} 是否禁用输入框输入内容
 * @inputSlot {String} 可前置或后置元素，一般为标签或按钮 prefix：头部 suffix：尾部 prepend：前置 append：后置
 * @slotVal {String} 插槽插入的文本内容
 * @placeholder {String} 输入框默认显示内容
 * @type {String} 表单类型  input：输入框 select：选择器 switch：开关 radio：选择器
 * @opts {Arrary} 当type为select || radio 时传入数组 [{value:"",description:""}]
 * @model {String} 表单对应字段
 *
 * */

export default {
  props: {
    form_create: {
      type: Object,
      default: () => {},
    },
    isClick: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    onClick() {
      this.$emit("onClick", this.form_create);
    },
  },
};
</script>

<style></style>
