<template>
    <div class="pages">
        <div class="content-box-crm" style="margin-bottom: 24px">
            <!-- 折叠面板 -->
            <div class="div row loadmore" @click="onChangeCollapse">
                <span class="text"> 佣金分配 </span>
                <span :class="is_collapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
            </div>
            <myCollapse :isActive="is_collapse">
                <template v-slot:content>
                    <el-form :inline="true" :model="formInline" :rules="rules" class="demo-form-inline">
                        <el-form-item label="关联客户" prop="region" size="medium">
                            <el-select v-model="formInline.region" placeholder="关联客户">
                                <el-option label="张三" value="shanghai"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item prop="phone" size="medium">
                            <el-input v-model="formInline.user" placeholder="联系电话"></el-input>
                        </el-form-item>
                        <el-form-item size="medium">
                            <el-button type="primary" @click="looSubmit('formInline')">查看客户</el-button>
                        </el-form-item>
                    </el-form>

                    <el-form :inline="true" :model="salesmanform" class="demo-form-inline">
                        <div v-for="(every, index) in salesmanform.salesmanformList" :key="index">
                            <el-form-item label="销售员" size="medium" :prop="'salesmanformList.' + index + '.salesman'"
                                :rules="salesmans">
                                <el-select v-model="every.salesman" placeholder="销售员" style="margin-left:14px">
                                    <el-option label="张三" value="张三"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item size="medium" :prop="'salesmanformList.' + index + '.department'"
                                :rules="departments">
                                <el-select v-model="every.department" placeholder="部门">
                                    <el-option label="客服部" value="kefu"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item size="medium" :prop="'salesmanformList.' + index + '.proportion'">
                                <el-input v-model="every.proportion" placeholder="比例" :rules="proportiones">
                                    <i slot="suffix">%</i>
                                </el-input>
                                <div class="addDelete">
                                    <i v-if="index == salesmanform.salesmanformList.length - 1" @click="addOrHanlder"
                                        class="el-icon-circle-plus-outline"></i>
                                    <i v-if="index !== salesmanform.salesmanformList.length - 1"
                                        @click="deleteOrHandler(every, index)" class="el-icon-remove-outline"></i>
                                </div>
                            </el-form-item>
                        </div>
                    </el-form>
                </template>
            </myCollapse>
            <div class="div row loadmore" @click="onChangeCollapsetwo">
                <span class="text"> 基本信息 </span>
                <span :class="is_collapsetwo ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
            </div>
            <myCollapse :isActive="is_collapsetwo">
                <template v-slot:content>
                    <el-descriptions style="margin-top: 15px" :column="4" size="medium" border>
                        <el-descriptions-item>
                            <template slot="label">
                                成交客户
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                联系电话
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                上客主播
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                来源
                            </template>
                            --
                        </el-descriptions-item>

                        <el-descriptions-item>
                            <template slot="label">
                                籍贯
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                楼盘城市
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                联系地址
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                成交渠道
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>

                        <el-descriptions-item>
                            <template slot="label">
                                案场接待
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                接待电话
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                购买楼盘
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                楼栋
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                单元
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                房号
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                建筑面积
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                户型
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                朝向
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                套内面积
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                身份证号
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                身份证地址
                            </template>
                            --
                        </el-descriptions-item>
                    </el-descriptions>
                </template>
            </myCollapse>
            <div class="div row loadmore" @click="onChangeCollapsethree">
                <span class="text"> 成交信息 </span>
                <span :class="is_collapsethree ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
            </div>
            <myCollapse :isActive="is_collapsethree">
                <template v-slot:content>
                    <el-descriptions style="margin-top: 15px" :column="4" size="medium" border>
                        <el-descriptions-item>
                            <template slot="label">
                                成交状态
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                成交时间
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                成交总价
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                付款方式
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>

                        <el-descriptions-item>
                            <template slot="label">
                                原售价
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                原单价
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                联系地址
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                结算状态
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>

                        <el-descriptions-item>
                            <template slot="label">
                                签约时间
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                币种
                            </template>
                            <el-select v-model="value" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                合同编号
                            </template>
                            --
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions :column="4" size="medium" border>
                        <el-descriptions-item :span="12">
                            <template slot="label">
                                备注
                            </template>
                            --
                        </el-descriptions-item>
                    </el-descriptions>
                </template>
            </myCollapse>
            <div class="bottom-tabel-box">
                <div class="bottom-tabel-title">
                    <span style="font-weight: 600;margin-right: 50px;">统计属性</span>
                    <span>创建人：<span>张三</span></span>
                    <span style="margin:0px 20px;">创建时间：<span>2023-08-11</span></span>
                    <span>实收佣金 <span style="color: red;">50000元</span>；</span>
                    <span>让利 <span style="color: red;">0元</span>；</span>
                    <span>返现 <span style="color: red;">10000元</span>；</span>
                    <span>毛利 <span style="color: red;">40000元</span>；</span>
                    <span>毛利润 <span style="color: red;">4%</span>；</span>

                </div>
                <div class="bottom-tabel-tabs">
                    <div>
                        <div class="tab">
                            <div class="tab-item" :class="isTabActive == 0 ? 'is-active' : ''" @click="clickTab(0)">公司佣金
                                <span style="color: red;">1个</span>
                            </div>
                            <div class="tab-item" :class="isTabActive == 1 ? 'is-active' : ''" @click="clickTab(1)">业绩/提成
                                <span style="color: red;">2笔</span>
                            </div>
                            <div class="tab-item" :class="isTabActive == 2 ? 'is-active' : ''" @click="clickTab(2)">支出
                                <span style="color: red;">1笔</span>
                            </div>
                            <div class="tab-item" :class="isTabActive == 3 ? 'is-active' : ''" @click="clickTab(3)">开票/到账
                                <span style="color: red;">1笔</span>
                            </div>
                            <div class="tab-item" :class="isTabActive == 4 ? 'is-active' : ''" @click="clickTab(4)">文件管理
                                <span style="color: red;">1个</span>
                            </div>
                        </div>
                    </div>
                    <!-- <span>业绩/提成<span style="color: red;">2笔</span></span>
                    <span style="margin: 0px 50px;">支出/返现<span style="color: red;">2笔</span></span>
                    <span>让利<span style="color: red;">2笔</span></span>
                    <span style="margin:0px 50px">开票/到账<span style="color: red;">2笔</span></span>
                    <span>文件管理<span style="color: red;">2个</span></span> -->
                </div>
                <div class='tab-body'>
                    <!-- 这里可以加入一些动画之类的切换特效 -->
                    <!--公司佣金-->
                    <div v-show='isTabActive == 0' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-table v-loading="is_table_loading" :data="commData" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="佣金类型" v-slot="{ row }">
                                {{ row.client.name ? row.client.name : "--" }}
                            </el-table-column>
                            <el-table-column label="计算公式" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="佣金金额" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="备注" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                                <el-link type="primary" @click="deleteData(row)">删除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--业绩提成-->
                    <div v-show='isTabActive == 1' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-button type="primary" style="margin: 20px 20px" @click="commsplit">一键分佣</el-button>
                        <el-table v-loading="is_table_loading" :data="commData" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="部门" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">销售部</el-link>
                            </el-table-column>
                            <el-table-column label="姓名" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="佣金公式" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="提成点位" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="分成比例" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="所得佣金" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="业绩余额" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="备注" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                                <el-link type="primary" @click="deleteData(row)">删除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--支出内容-->
                    <div v-show='isTabActive == 2' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-table v-loading="is_table_loading" :data="commData" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="姓名" v-slot="{ row }">
                                {{ row.client.name ? row.client.name : "--" }}
                            </el-table-column>
                            <el-table-column label="类型" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="开户行" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="卡号" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="联系方式" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="税前 (元)" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="税后 (元)" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="申请状态" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="报销" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="备注" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                                <el-link type="primary" @click="deleteData(row)">删除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--开票内容-->
                    <div v-show='isTabActive == 3' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-table v-loading="is_table_loading" :data="commData" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="开票时间" v-slot="{ row }">
                                {{ row.client.name ? row.client.name : "--" }}
                            </el-table-column>
                            <el-table-column label="开票抬头" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="开票金额" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="开票类型" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="回款类型" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="是否到账" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="到账日期" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="提成发放时间" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="到账状态" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="发票内容" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                                <el-link type="primary" @click="deleteData(row)">删除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--文件管理的内容-->
                    <div v-show='isTabActive == 4' style="padding:0px  20px 20px 20px;">
                        <el-form ref="fileform" :model="fileform" label-width="80px" :inline="true"
                            style="margin-top: 20px">
                            <el-form-item label="文件类型">
                                <el-select v-model="fileform.region" placeholder="请选择">
                                    <el-option label="区域一" value="shanghai"></el-option>
                                    <el-option label="区域二" value="beijing"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-button type="primary" @click="comupload">上传文件</el-button>
                        </el-form>
                        <el-table v-loading="is_table_loading" :data="commData" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="创建人" v-slot="{ row }">
                                {{ row.client.name ? row.client.name : "--" }}
                            </el-table-column>
                            <el-table-column label="创建时间" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="文件类型" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="缩略图" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="文件名" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                                <el-link type="primary" @click="deleteData(row)">删除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
            <div style="margin-left: 88.7%;">
                <el-button type="primary">提交申请</el-button>
                <el-button type="primary">保存</el-button>
            </div>
        </div>
        <el-dialog title="添加佣金信息" :visible.sync="dialogVisible" width="786px" :before-close="handleClose">
            <el-form :model="newcommform" class="demo-form-inline" style="padding:10px 40px;">
                <el-form-item label="佣金类型:">
                    <el-select v-model="newcommform.region" placeholder="佣金" style="width: 400px;">
                        <el-option label="区域一" value="shanghai"></el-option>
                        <el-option label="区域二" value="beijing"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="计算方式:">
                    <el-radio-group v-model="newcommform.resource">
                        <el-radio label="公式"></el-radio>
                        <el-radio label="自定义"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="佣金金额:">
                    <el-select v-model="newcommform.region" placeholder="总价" style="width: 120px;">
                        <el-option label="区域一" value="shanghai"></el-option>
                        <el-option label="区域二" value="beijing"></el-option>
                    </el-select>
                    <el-input v-model="newcommform.user" placeholder="10000" style="width: 120px;"></el-input>
                    X
                    <el-input v-model="newcommform.user" style="width: 120px;">
                        <i slot="suffix">%</i></el-input>
                    =
                    <el-input v-model="newcommform.user" style="width: 120px;">
                        <i slot="suffix">元</i></el-input>
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input type="textarea" v-model="newcommform.user" placeholder="10000"
                        style="width: 550px;margin-left: 28px;"></el-input>
                </el-form-item>
                <el-form-item label="客户负责人:">
                    <el-switch v-model="newcommform.delivery"></el-switch> 如果已经收到款项，可以同步创建到账记录
                </el-form-item>
                <el-form-item label="负责人主管:">
                    <el-switch v-model="newcommform.delivery"></el-switch> 如果已经开票，可以同步创建开票记录
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false">保存</el-button>
            </span>
        </el-dialog>
    </div>
</template>
  
<script>
import myCollapse from "./components/collapse";
export default {
    name: "crm_customer_seas_list",
    components: {
        myCollapse,
    },
    data() {
        return {
            newcommform: {
                delivery: false,
                resource: '',
            }, //新增编辑佣金
            dialogVisible: false, //添加佣金弹窗
            fileform: {}, //文件类型的form
            commData: [], //公司佣金列表的表格
            isTabActive: 0, // 控制切换的变量
            options: [{
                value: '选项1',
                label: '黄金糕'
            }, {
                value: '选项2',
                label: '双皮奶'
            }, {
                value: '选项3',
                label: '蚵仔煎'
            }, {
                value: '选项4',
                label: '龙须面'
            }, {
                value: '选项5',
                label: '北京烤鸭'
            }],
            value: '',
            salesmans: [
                { required: true, trigger: "blur", message: '请选择销售员' },
            ],
            departments: [
                { required: true, trigger: "blur", message: '请选择部门' },
            ],
            proportiones: [
                { required: true, trigger: "blur", message: '请输入比例' },
            ],
            is_collapse: true,
            is_collapsetwo: true,
            is_collapsethree: true,
            //关联客户
            formInline: {
                user: '',
                region: ''
            },
            //销售员
            salesmanform: {
                salesmanformList: [
                    {
                        salesman: '',
                        department: '',
                        proportion: '',
                    }

                ]

            },
            rules: {
                region: [
                    { required: true, message: '请选择关联客户', trigger: 'change' }
                ],
                phone: [
                    { required: true, message: '请输入联系电话' },
                ],

            },
            rulestwo: {
                salesman: [
                    { required: true, message: '请选择销售员', trigger: 'change' }
                ],
                department: [
                    { required: true, message: '请选择部门', trigger: 'change' }
                ],
                proportion: [
                    { required: true, message: '请选择佣金比例' },
                ],
            }
        };
    },
    computed: {

    },
    mounted() {

    },
    methods: {
        //上传文件
        comupload() {
            console.log("上传文件")
        },
        //佣金添加
        commadd() {
            console.log("佣金添加了")
            this.dialogVisible = true
        },
        //一键分佣
        commsplit() {
            console.log("一键分佣")
            this.$router.push("crm_customer_tableDeatil_split");
        },
        //佣金业绩切换
        clickTab(number) {
            this.isTabActive = number;
            console.log('切换了', this.isTabActive)
        },
        // 折叠面板
        onChangeCollapse() {
            this.is_collapse = !this.is_collapse;
        },
        onChangeCollapsetwo() {
            this.is_collapsetwo = !this.is_collapsetwo;
        },
        onChangeCollapsethree() {
            this.is_collapsethree = !this.is_collapsethree;
        },
        onSubmit() {
            console.log('submit!');
        },
        looSubmit(formName) {
            console.log("查看客户")
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        //表单新增行
        addOrHanlder() {
            let obj = {
                name: "",
                logicRulesName: "",
                compareObject: "",
            };
            this.salesmanform.salesmanformList.push(obj);
        },
        //表单删除行
        deleteOrHandler(every, index) {
            this.salesmanform.salesmanformList.splice(index, 1);
        },

    },
};
</script>
  
<style lang="scss" scoped>
/deep/.el-descriptions--medium.is-bordered .el-descriptions-item__cell {
    padding: 10px;
    width: 199px;
    height: 50px;
    text-align: center;
}

.pages {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 0 24px 24px;


    .bottom-tabel-box {
        min-height: 200px;
        width: 100%;
        border: 1px solid #DDE1E9;
        margin: 20px 0px;

        .bottom-tabel-title {
            padding: 0px 40px;
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid #DDE1E9;
        }

        .bottom-tabel-tabs {
            height: 50px;
            background-color: #F2F3F5;
            border-bottom: 1px solid #DDE1E9;
            line-height: 50px;

            .tab {
                width: 800px;
                display: flex;
                justify-content: space-between;

                .tab-item {
                    width: 167px;
                    height: 50px;
                    text-align: center;
                    line-height: 50px;
                    cursor: pointer;
                }

                // .tab-itemdetil {
                //     padding:0px 20px;
                // }
                .is-active {
                    background-color: #fff;
                }
            }
        }
    }

    .addDelete {
        position: absolute;
        top: 8px;
        right: -30px;
        display: flex;
        flex-direction: column;
        cursor: pointer;


        i {
            font-size: 20px;
            flex: 1;
        }

        .el-icon-circle-plus-outline {
            color: #2d8cf0;
        }

        .el-icon-remove-outline {
            color: red;
        }
    }

}


.loadmore {
    border: none;
    width: 100%;
    text-align: end;
    line-height: 1;
    margin-top: 12px;
    color: #8a929f;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;

    .text {
        font-size: 14px;
    }
}
</style>
  