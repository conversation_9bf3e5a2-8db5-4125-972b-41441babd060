<template>
<div class="user-room-clues">
    <el-table :data="data" height="320">
        <el-table-column prop="name" label="直播间" show-overflow-tooltip v-slot="{row}">
            <div class="clue-room-info">
                <p class="name" @click="handleClickUserClueRoom(row)">{{row.title}}</p>
                <p class="title" @click="handleClickUserClueRoom(row)">{{row.name}}</p>
            </div>
        </el-table-column>
        <el-table-column prop="start_time" label="直播时间" width="200" align="center" v-slot="{row}">
            <div class="clue-room-info">
                <p>开始：{{row.start_time}}</p>
                <p>结束：{{row.end_time}}</p>
            </div>
        </el-table-column>
        <el-table-column prop="mobile" label="手机号" width="140" align="center"></el-table-column>
        <el-table-column prop="add_time_begin" label="用户评论时间" width="220" align="center" v-slot="{row}">
            <div class="clue-room-info">
                <p>第一条：{{row.add_time_begin}}</p>
                <p>最后一条：{{row.add_time_end}}</p>
            </div>
        </el-table-column>
        <el-table-column prop="desc" label="留资描述"  show-overflow-tooltip align="center"></el-table-column>
    </el-table>
</div>
</template>
<script>
export default {
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
	data() {
		return {  
            list: []
        }
	},
	methods: {
        handleClickUserClueRoom(row){
            this.$emit('search', {room_id: row.room_id});
        }
	}
}
</script>
<style  scoped lang="scss">
.clue-room-info{
    white-space: nowrap;
    .title{
        cursor: pointer;
        color: #aaa;
        font-size: 13px;
    }
    .name{
        cursor: pointer;
        color: #409EFF;
    }
}
</style>