<template>
  <div class="labels">
    <div class="label" v-for="item in labels" :key="item.id">
      <div class="label_title">{{ item.title }}</div>
      <div class="label_list flex-row align-center">
        <div
          v-for="i in item.child"
          :key="i.id"
          class="label_item flex-row align-center"
          :class="{ active: i.checked == true }"
          @click="clickItem(item.child, i)"
        >
          {{ i.name }}
        </div>
      </div>
    </div>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    defaultValue: {
      type: Array,
      default: () => [2, 6]
    }
  },
  data() {
    return {
      labels: [
        {
          title: "标签名称",
          id: 1,
          child: [
            {
              id: 2,
              name: '一般'
            },
            {
              id: 3,
              name: '意思'
            },
            {
              id: 4,
              name: '很强烈'
            }
          ]
        },
        {
          title: "标签名称1",
          id: 5,
          child: [
            {
              id: 6,
              name: '一般1'
            },
            {
              id: 7,
              name: '意思1'
            },
            {
              id: 8,
              name: '很强烈1'
            }
          ]
        }
      ]
    }
  },
  created() {
    if (this.defaultValue.length) {
      this.labels.map(item => {
        item.child.map(c => {
          if (this.defaultValue.includes(c.id)) {
            c.checked = true
          }
        })
      })
    }
  },
  methods: {
    clickItem(item, i) {
      console.log(item, i);
      item.map(i => {
        i.checked = false
      })
      this.$set(i, 'checked', true)
      this.$forceUpdate()
    },
    subform() {

    },
    cancel() {

    }
  }

}
</script>

<style lang ="scss" scoped>
.labels {
  padding: 0 20px;
  .label {
    .label_title {
      color: #8a929f;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 14px;
      margin-bottom: 14px;
    }
    .label_list {
      margin-bottom: 20px;
      .label_item {
        padding: 4px 10px;
        color: #8a929f;
        font-size: 14px;
        border-radius: 4px;
        background: #ededed;
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;
        &.active {
          background: #e8f1ff;
          color: #2d84fb;
        }
      }
    }
  }
}
</style>