<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->

    <el-form label-width="100px" :model="user_info">
      <div class="flex-box">
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            {{ chooseStyle }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="user_name">用户名</el-dropdown-item>
            <el-dropdown-item command="phone">手机号</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-select
          filterable
          v-model="params.id"
          remote
          reserve-keyword
          :remote-method="getUsers"
          :placeholder="`请输入并选择${chooseStyle}`"
          @change="handleChange"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <el-form-item label="真实姓名">
        <el-input
          :disabled="true"
          placeholder="请输入姓名"
          v-model="user_info.name"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户类型">
        <el-select v-model="user_info.category">
          <el-option
            :disabled="true"
            v-for="item in category_list"
            :key="item.id"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司" v-if="!type">
        <el-select
          v-model="company_name"
          filterable
          remote
          reserve-keyword
          placeholder="请输入公司名称"
          :remote-method="getCompanyList"
          :loading="company_loading"
          @change="onCompany"
        >
          <el-option
            v-for="item in company_list"
            :key="item.company_id"
            :label="item.name"
            :value="item.company_id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="联系方式">
        <el-input
          :disabled="true"
          maxlength="11"
          v-model="user_info.phone"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          placeholder="请输入个人描述"
          type="textarea"
          :disabled="true"
          v-model="user_info.description"
        ></el-input>
      </el-form-item>
      <el-form-item label="头像">
        <el-upload
          :disabled="true"
          :headers="myHeader"
          action="/api/common/file/upload/admin?category=5"
          :on-success="handleSuccessAvatar"
          :show-file-list="false"
          list-type="picture-card"
          :on-preview="handlePictureCardPreviewAvatar"
          :on-remove="handleRemoveAvatar"
        >
          <img
            v-if="user_info.avatar"
            :src="user_info.avatar"
            alt=""
            style="margin-top:5px"
            height="148px"
            width="148px"
          />
          <i v-else class="el-icon-plus"></i>
        </el-upload>

        <el-dialog :visible.sync="ImageVisible">
          <img width="100%" :src="imageUrl" alt="" />
        </el-dialog>
      </el-form-item>

      <el-form-item size="large">
        <el-button type="primary" @click="onSubmit">确认添加</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
import config from "@/utils/config.js";
export default {
  name: "add_user",
  data() {
    return {
      user_info: {
        id: "",
        category: "",
        company_id: "",
        phone: "",
        name: "",
        avatar: "",
        description: "",
      },
      company_name: "",
      category_list: [],
      company_list: [],
      ImageVisible: false,
      imageUrl: "",
      company_loading: false,
      type: false,
      chooseStyle: "用户名",
      chooseStyleStatus: "user_name",
      options: [],

      params: {
        id: "",
        company_id: 0,
      },
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  mounted() {
    this.params.company_id = +this.$route.query.company_id;
    this.type = Boolean(this.$route.query.type) || false;
    // this.getUserInfo();
    this.getCompanyList();
    this.$http.dictionaryFind("USER_CATEGORY").then((res) => {
      this.category_list = res.data.data;
    });
  },
  methods: {
    getUserInfo() {
      this.$http.queryUserInfo(this.params.id).then((res) => {
        if (res.status === 200) {
          if (res.data.category > 0) {
            this.user_info.category = res.data.category.toString();
          }
          if (res.data.company_id > 0) {
            this.user_info.company_id = res.data.company_id;
          }
          this.user_info.avatar = res.data.avatar;
          this.user_info.description = res.data.description;
          this.user_info.name = res.data.name;
          this.user_info.phone = res.data.phone;
          this.company_name = res.data.company_name;
        } else {
          this.$message({
            message: res.data.message || "获取失败",
            type: "error",
          });
        }
      });
    },
    getCompanyList(query) {
      this.company_loading = true;
      this.$http.searchCompany(query).then((res) => {
        this.company_loading = false;
        this.company_list = res.data.data.map((item) => {
          return { name: item.name, company_id: item.id };
        });
      });
    },
    goBack() {
      this.$router.back();
    },
    handleCommand(e) {
      console.log(e);
      this.chooseStyleStatus = e;
      if (e === "user_name") {
        this.chooseStyle = "用户名";
      } else if (e === "phone") {
        this.chooseStyle = "手机号";
      }
    },
    handleChange(e) {
      this.getUserInfo(e);
    },
    getUsers(query) {
      if (this.chooseStyleStatus == "user_name") {
        this.searchByUsername(query);
      } else {
        this.searchByphone(query);
      }
    },
    searchByUsername(query) {
      this.broker_loading = true;
      this.$http.searchUserListByName("100", query, 1).then((res) => {
        this.broker_loading = false;
        this.options = res.data.data.map((item) => {
          if (item.name) {
            return {
              id: item.id,
              name: item.name,
            };
          }
        });
      });
    },
    searchByphone(query) {
      this.broker_loading = true;
      this.$http.searchUserListByphone("100", query, 1).then((res) => {
        this.broker_loading = false;
        this.options = res.data.data.map((item) => {
          return {
            id: item.id,
            name: item.name || item.nickname || item.user_name,
          };
        });
      });
    },
    handleSuccessAvatar(response) {
      this.user_info.avatar = response.url;
    },
    handlePictureCardPreviewAvatar(file) {
      this.imageUrl = file.response.url;
      this.ImageVisible = true;
    },
    handleRemoveAvatar(response) {
      this.user_info.avatar = response.url;
    },
    onCompany(e) {
      this.user_info.company_id = e;
    },
    submitUser() {
      this.$http.addCompanyUser(this.params).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "添加成功",
            type: "success",
          });
          this.goBack();
        }
      });
    },
    onSubmit() {
      // if (this.company_name == "") {
      // 	this.user_info.company_id = 0;
      // }
      let _this = this;
      if (
        this.params.company_id != this.user_info.company_id &&
        (this.user_info.company_id != 0 || this.user_info.company_id != "")
      ) {
        this.$confirm(
          `此用户已经加入 ${_this.company_name} 确认加入到本公司?`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.submitUser();
          })
          .catch(() => {
            // this.$message({
            // 	type: "info",
            // 	message: "已取消删除",
            // });
          });
      } else {
        this.submitUser();
      }
    },
  },
};
</script>

<style scoped lang="scss">
// .el-input {
// 	width: 200px;
// }
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-form {
// 	padding-top: 30px;
// }
.el-input,
.el-select,
.el-textarea {
  width: 300px;
}
.flex-box {
  margin-bottom: 20px;
  padding-left: 33px;
}
</style>
