<template>
  <div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <el-table-column label="操作" fixed="right" v-slot="{ row }">
        <el-link type="primary" @click="onChangeEdit(row)">编辑</el-link>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog width="34%" :visible.sync="dialogCreate" title="违规提醒设置">
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCreate = false">取 消</el-button>
        <el-button type="primary" @click="onClickForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      is_table_loading: false,
      tableData: [{ id: 1 }],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        type: 1,
      },
      form_create: {},
      dialogCreate: false,
    };
  },
  methods: {
    getDataList() {},
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onChangeEdit(row) {
      this.dialogCreate = true;
      console.log(row);
    },
    onClickForm() {
      console.log(this.form_create);
    },
  },
};
</script>

<style></style>
