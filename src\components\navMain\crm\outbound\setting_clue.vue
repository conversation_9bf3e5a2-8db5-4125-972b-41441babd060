<template>
  <div class="outbount">
    <div class="tabs">
      <el-tabs v-model="tab_page" @tab-click="handleClick">
        <el-tab-pane
          v-for="item in tabList"
          :label="item.label"
          :name="item.name"
          :key="item.id"
        >
        </el-tab-pane>
        <div :is="tab_page" @daoruSuccess="daoruSuccess" keep-alive></div>
        <!-- <div v-if="tab_page === 'daoruClue'">
          <daoruClue @daoruSuccess="daoruSuccess"></daoruClue>
        </div>
        <div v-if="tab_page === 'daoruData'">
          <daoruData></daoruData>
        </div> -->
      </el-tabs>
    </div>
  </div>
</template>

<script>
import daoruData from "./components/daoruData"
import daoruClue from "./components/daoruClue"
import daoruCustomer from "./components/daoruCustomer.vue"
export default {
  components: {
    daoruClue,
    daoruData,
    daoruCustomer
  },
  data() {
    return {
      tab_page: "daoruClue",
      tabList: [
        {
          id: 1,
          name: "daoruClue",
          label: "导入线索"
        },
        {
          id: 2,
          name: "daoruData",
          label: "导入分配"
        },
        {
          id: 3,
          name: "daoruCustomer",
          label: "导入客户"
        }
      ]
    }
  },
  methods: {
    handleClick() {

    },
    daoruSuccess() {
      this.tab_page = "daoruData"
    }
  }
}
</script>

<style lang="scss" scoped>
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
</style>