<template>
  <div class="criculate">
    <div class="h_date flex-row">
      <div class="flex-row">
        <div>
          <!-- <span
          class="c2"
          :class="{ active: data_params.date_type == item.type }"
          @click="clickDate(item)"
          v-for="(item, index) in honor_date"
          :key="index"
          >{{ item.name }}
        </span> -->
          <!-- <el-select size="medium" class="crm-selected-label" v-model="source" placeholder="全部时间" :style="{
            minWidth: '70px',
            width: '150px',
          }" @change="clickDate">
            <el-option v-for="item in honor_date" :key="item.type" :label="item.name" :value="item.type">
            </el-option>
          </el-select> -->
          <!-- <span
          class="c2"
          :class="{ active:access_id == item.id}"
          @click="clicksource(item.id)"
          v-for="item in accessdata"
          :key="item.id"
          >{{ item.name }}
        </span> -->
          <el-select size="medium" class="crm-selected" v-model="accesslist" placeholder="全部渠道" :style="{
                      minWidth: '70px',
                      width: '150px',
                    }" @change="clicksource">
            <el-option v-for="item in accessdata" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-date-picker style="width: 390px;" v-model="pickerDate" size="medium" type="datetimerange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
            value-format="yyyy-MM-dd" @change="changePickerDate">
          </el-date-picker>
        </div>
      </div>
      <div>

        <el-button class="listbnt" size="mini" @click="doulist">
          <div class="flex-row items-center">
            <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinlist.png" alt="">

            <span>数据列表</span>
          </div>
        </el-button>
        <el-button type="primary" size="small" @click="onExport">导出</el-button>
      </div>
    </div>
    <div class="bg_fff box_top marbot28">
      <el-row :gutter="20">
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">全场景线索数量</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.total }}人</div>
                <!-- <div class="bottom_item_left_people_num">{{ clientData.leve_c_total }}人</div> -->
              </div>
              <div class="bottom_item_right">
                <img class="top_item_img" :src="$imageFilter(
                                  'https://img.tfcs.cn/backup/static/admin/douyin/statistics/user.png',
                                  'w_80'
                                )
                                  " alt="" />
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">跟进率</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.follow_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.follow_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart0" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">流入公海</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.public_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.public_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart1" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">流入私客</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.private_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.private_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart2" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">{{ repeat }}</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{
                                  clientData.deal_total_ratio?clientData.deal_total_ratio:clientData.total_repeat_ratio}}</div>
                <div class="bottom_item_left_people_num">{{
                                  clientData.deal_total?clientData.deal_total:clientData.total_repeat }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart3" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">时段峰值</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.segment_max_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.segment_max }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart4" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">有效客户</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.valid_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.valid_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart5" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">无效客户</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.invalid_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.invalid_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart6" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">暂缓客户</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.pending_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.pending_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart7" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">A级客户</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.level_a_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.level_a_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart8" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">B级客户</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.level_b_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.level_b_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart9" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="data_item">
            <div class="top_item">
              <div class="top_item_text">C级别客户</div>
              <img class="top_item_img" :src="$imageFilter(
                              'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                              'w_80'
                            )
                              " alt="" />
            </div>
            <div class="bottom_item">
              <div class="bottom_item_left">
                <div class="bottom_item_left_num">{{ clientData.level_c_total_ratio }}</div>
                <div class="bottom_item_left_people_num">{{ clientData.level_c_total }}人</div>
              </div>
              <div class="bottom_item_right2">
                <div class="top_item_img">
                  <div class="echart_l_con">
                    <div class="chart-box bing_echart10" id="chart-box" style="width: 100%; height: 100%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

      </el-row>
    </div>
    <div class="data_title_wrap">
      <div class="data_enter">数据流入</div>
      <div class="date_source">数据来源：本站</div>
    </div>
    <!-- <div class="echarts_wrap">
            <div class="echart_l_con">
                <div class="chart-box sale-chart" id="chart-box" style="width:100%; height: 390px"></div>
            </div>
        </div> -->

    <el-row>
      <el-col :span="24">
        <div class="tab-title" style="width: 100%; height: 390px">
          <div class="chart-box sale-chart" id="chart-box" style="width: 100%; height: 390px"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
  
<script>
import * as echarts from "echarts";
export default {
  name: "douyin_circulate_for_perusal",

  data() {
    return {
      // source: 1,
      accesslist: "0_1",
      repeat:"",
      honor_date: [
        { name: "全部客户", type: 1 },
        { name: "全部渠道", type: 2 },
      ],
      accessdata: [
        { id: "0_1", name: "全部渠道" },
        { id: "0_4", name: "抖音" },
        { id: "0_6", name: "快手" },
        { id: "0_7", name: "海豚知道" },
        { id: "0_8", name: "企业微信" },
      ],
      access_id: "0_1",
      mun_data: 12000,
      clientData: {}, // 获取客户数据统计
      clientChart: {}, // 获取客户数据统计底部chart
      statistics_list: [],
      data_params: {
        date_type: 1, // 1全部 2今天 3昨天 4本周 5上周 6本月 7上月
        opt_type: "", // opt_type值为export表示导出数据
        b_date: "", // 开始时间
        e_date: "", // 结束时间
        access_id: ""//数据来源
      },
      pickerDate: "",
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            end.setHours(0, 0, 0, 0);
            const start = new Date(end);
            start.setDate(start.getDate() - 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本周',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上周',
          onClick(picker) {
            const end = new Date(); // 获取当前日期
              end.setDate(end.getDate() - end.getDay() - 7); // 获取上周的最后一天
              end.setHours(23, 59, 59, 0);
              const start = new Date(end);
              start.setDate(start.getDate() - 6); // 获取上一周的第一天
              start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上月',
          onClick(picker) {
            const end = new Date();
            end.setDate(0); // 获取上个月的最后一天
            end.setHours(0, 0, 0, 0);
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      totalDate: [], // 全部echart时间
    };
  },
  created() {
    this.new_data()
    this.getClientChart1()
    // this.getClientData(); // 获取客户数据统计
    // this.getClientChart(); // 获取客户数据统计底部chart
  },
  beforeDestroy() {
    document.querySelector('.el-tabs__content').setAttribute('style', '')
  },
  mounted() {
    document.querySelector('.el-tabs__content').setAttribute('style', 'background:#f1f4fa');
    // 优化echart找不到clientWidth/clientHeight
    for (let i = 0; i < 11; i++) {
      Object.defineProperty(document.querySelector('.bing_echart' + i), 'clientWidth', { get: function () { return 66; } });
      Object.defineProperty(document.querySelector('.bing_echart' + i), 'clientHeight', { get: function () { return 72; } });
    }
  },
  methods: {
    // tab切换
    // clickDate(e) {
    //   this.accesslist = "0_1"
    //   if(e==1){
    //     console.log("11111111");
    //     this.data_params.date_type = e
    //     this.getClientData(); // 获取客户数据统计
    //     this.getClientChart();
    //   }else{
    //     console.log("2222222222");
    //     this.data_params.date_type = 1
    //     this.new_data()
    //     this.getClientChart1()
    //   }
    // },
    new_data(){
      this.$http.newgetClientStatistics(this.data_params).then((res)=>{
        if (res.status == 200) {
          this.clientData = res.data;
          this.repeat="重复号码"
          this.statistics_list = [];
          this.statistics_list.push({ value: res.data.follow_total, plus: "已跟进", minus: "未跟进" }); // 跟进率
          this.statistics_list.push({ value: res.data.public_total, plus: "公海", minus: "私客" }); // 公海
          this.statistics_list.push({ value: res.data.private_total, plus: "私客", minus: "公海" }); // 私客
          this.statistics_list.push({ value: res.data.total_repeat, plus: "重复号码", minus: "其他" }); // 成交
          this.statistics_list.push({ value: res.data.segment_max, plus: "时段峰值", minus: "其他" }); // 线索推送
          this.statistics_list.push({ value: res.data.valid_total, plus: "有效", minus: "其他" }); // 有效
          this.statistics_list.push({ value: res.data.invalid_total, plus: "无效", minus: "其他" }); // 无效
          this.statistics_list.push({ value: res.data.pending_total, plus: "暂缓", minus: "其他" }); // 暂缓
          this.statistics_list.push({ value: res.data.level_a_total, plus: "A级", minus: "其他" }); // 等级A
          this.statistics_list.push({ value: res.data.level_b_total, plus: "B级", minus: "其他" }); // 等级B
          this.statistics_list.push({ value: res.data.level_c_total, plus: "C级", minus: "其他" }); // 等级C
          this.initChartBing1();
        }
      })
    },
    clicksource(e) {
      // if(this.source==1){
      //   this.access_id = e;
      //     if (e == "0_1") {
      //       this.data_params.access_id = ''
      //     } else {
      //       this.data_params.access_id = e.split("_")[1];
      //     }
      //     this.getClientData(); // 获取客户数据统计
      //     this.getClientChart();
      // }else{
        this.access_id = e;
          if (e == "0_1") {
            this.data_params.access_id = ''
          } else {
            this.data_params.access_id = e.split("_")[1];
          }
          this.new_data()
          this.getClientChart1()
      // }
    
    },
    // 线索的12个echart
    initChartBing1() {
      var fixedColor = '#F1F4FA'; // 固定的颜色
      var dynamicColors = ['#FFA338', '#4A78FF', '#01DB00', '#FFCE21', '#9FB6FA', '#FE5958']; // 动态的颜色数组
      for (let i = 0; i < 11; i++) {
        var chartDom = document.querySelector(".bing_echart" + i);
        var myChart = echarts.getInstanceByDom(chartDom);
        if (myChart == null) {
          myChart = echarts.init(chartDom);
        }
        var option;
        let beLeft = "";
        let plus = "";
        plus = this.statistics_list[i].plus;
        let minus = "";
        minus = this.statistics_list[i].minus;
        if (this.clientData.total > this.statistics_list[i].value) {
          beLeft = this.clientData.total - this.statistics_list[i].value;
        }
        let value = this.statistics_list[i].value;
        option = {
          tooltip: {
            trigger: 'item',
            formatter: function (params) {
              let unit = "人";
              var relVal = params.name;
              relVal += params.value + unit;
              return relVal
            }
          },
          color: [dynamicColors[i % dynamicColors.length], fixedColor],
          series: [
            {
              type: 'pie',
              labelLine: {
                show: false
              },
              data: [
                {
                  value: value,
                  name: plus,
                },
                {
                  value: beLeft,
                  name: minus
                }
              ],
              radius: ['40%', '70%']
            }
          ]
        };
        option && myChart.setOption(option);
      }
    },
    
    // 数据折线echart
    initChartOne() {
      var chartDom = document.querySelector('.sale-chart');
      var myChart = echarts.getInstanceByDom(chartDom);
      if (myChart == null) {
        myChart = echarts.init(chartDom)
      }
      var option;
      let douyin = []; // 抖音数据
      let kuaishou = []; // 快手数据
      let haitun = []; // 海豚数据
      let weixin = [];//企业微信
      let time = this.totalDate; // x轴时间
      let typeName = []; // 顶部类型名
      const keys = Object.keys(this.clientChart.segment);
      const length = keys.length;
      if (length > 1) {
        this.clientChart.segment["6"].forEach((item) => {
          kuaishou.push(item.count);
        })
        this.clientChart.segment["7"].forEach((item) => {
          haitun.push(item.count);
        })
        this.clientChart.segment["4"].forEach((item) => {
          douyin.push(item.count);
        })
        this.clientChart.segment["8"].forEach((item) => {
          weixin.push(item.count);
        })
      }
      if (length == 1) {
        if (this.clientChart.segment["4"]) {
          this.clientChart.segment["4"].forEach((item) => {
            douyin.push(item.count);
          })
        }
        if (this.clientChart.segment["6"]) {
          this.clientChart.segment["6"].forEach((item) => {
            kuaishou.push(item.count);
          })
        }
        if (this.clientChart.segment["7"]) {
          this.clientChart.segment["7"].forEach((item) => {
            haitun.push(item.count);
          })
        }
        if (this.clientChart.segment["8"]) {
          this.clientChart.segment["8"].forEach((item) => {
            weixin.push(item.count);
          })
        }
      }
      Object.keys(this.clientChart.access_id).forEach((item) => {
        typeName.push(this.clientChart.access_id[item]);
      })
      option = {
        // 配置提示框
        tooltip: {
          trigger: 'axis', // 数据项提示框
          // 提示框样式
          axisPointer: {
            type: 'cross', // 十字准星指示器
            // 配置提示框标签的样式
            label: {
              backgroundColor: '#6a7985' // 提示框标签的背景颜色
            }
          },
          formatter: function (params) {
            let unit = "人";
            for (let i = 0; i < params.length; i++) {
              var relVal = params[i].name;
            }
            for (let i = 0; i < params.length; i++) {
              relVal += "<br/>" + params[i].seriesName + '：' + params[i].data + unit;
            }
            return relVal;
          }
        },
        legend: {
          data: typeName,
        },
        // x轴
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: time,
        },
        // y轴
        yAxis: {
          type: 'value',
          minInterval: 1,
          axisLabel: { formatter: '{value} 人' }, // 用于配置y坐标轴样式
        },
        grid: {
          left: '4%',
          right: '2%',
        },
        color: ["rgba(254, 89, 88, 0.80)", "#01DB00", "#FF9838",'#9FB6FA'],
        // 定义图表数据
        series: [
          {
            name: "抖音",
            data: douyin, // 具体数据
            type: 'line', // 数据显示类型为折线图(line)
            markPoint: {
              symbol: 'pin', // 标注点的形状，可以根据需求调整
              symbolSize: 20, // 标注点的大小，可以根据需求调整
              data: [
                {
                  type: 'max', // 标注类型为最大值
                  label: {
                    show: true, // 显示标注文字
                    position: 'top' // 标注文字显示在顶部
                  }
                }
              ]
            },
            areaStyle: {}, // 设置填充区域样式
            smooth: true, // 线条是否平滑
          },
          {
            name: "快手",
            data: kuaishou,
            type: 'line',
            markPoint: {
              symbol: 'pin', // 标注点的形状，可以根据需求调整
              symbolSize: 20, // 标注点的大小，可以根据需求调整
              data: [
                {
                  type: 'max', // 标注类型为最大值
                  label: {
                    show: true, // 显示标注文字
                    position: 'top' // 标注文字显示在顶部
                  },
                },
              ]
            },
            areaStyle: {},
            smooth: true,
          },
          {
            name: "海豚知道",
            data: haitun,
            type: 'line',
            markPoint: {
              symbol: 'pin', // 标注点的形状，可以根据需求调整
              symbolSize: 20, // 标注点的大小，可以根据需求调整
              data: [
                {
                  type: 'max', // 标注类型为最大值
                  label: {
                    show: true, // 显示标注文字
                    position: 'top' // 标注文字显示在顶部
                  }
                }
              ]
            },
            areaStyle: {},
            smooth: true,
          },
          {
            name: "企业微信",
            data: weixin,
            type: 'line',
            markPoint: {
              symbol: 'pin', // 标注点的形状，可以根据需求调整
              symbolSize: 20, // 标注点的大小，可以根据需求调整
              data: [
                {
                  type: 'max', // 标注类型为最大值
                  label: {
                    show: true, // 显示标注文字
                    position: 'top' // 标注文字显示在顶部
                  }
                }
              ]
            },
            areaStyle: {},
            smooth: true,
          },
        ],
      };
      option && myChart.setOption(option);
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    // 导出
    onExport() {
      let params = Object.assign({}, this.data_params);
      let exportdata = 'export'
      params.opt_type = exportdata; // 增加导出参数
      // if(this.source==1){
      //   this.$http.getClientStatistics(params).then((res) => {
      //   if (res.status == 200) {
      //     if (res.data.url) {
      //       window.open(res.data.url, '_blank');
      //     }
      //   }
      // })
      // }else{
        this.$http.newgetClientStatistics(params).then((res) => {
        if (res.status == 200) {
          if (res.data.url) {
            window.open(res.data.url, '_blank');
          }
        }
      })
      // }

   
    },
    // 获取客户数据统计
    getClientData() {
      this.$http.getClientStatistics(this.data_params).then((res) => {
        if (res.status == 200) {
          // console.log(res.data.url);
          // if (res.data.url) {
          //   window.open(res.data.url, '_blank');
          // }
          this.clientData = res.data;
          this.repeat="成交转化"
          this.statistics_list = [];
          this.statistics_list.push({ value: res.data.follow_total, plus: "已跟进", minus: "未跟进" }); // 跟进率
          this.statistics_list.push({ value: res.data.public_total, plus: "公海", minus: "私客" }); // 公海
          this.statistics_list.push({ value: res.data.private_total, plus: "私客", minus: "公海" }); // 私客
          this.statistics_list.push({ value: res.data.deal_total, plus: "成交", minus: "未成交" }); // 成交
          this.statistics_list.push({ value: res.data.segment_max, plus: "时段峰值", minus: "其他" }); // 线索推送
          this.statistics_list.push({ value: res.data.valid_total, plus: "有效", minus: "其他" }); // 有效
          this.statistics_list.push({ value: res.data.invalid_total, plus: "无效", minus: "其他" }); // 无效
          this.statistics_list.push({ value: res.data.pending_total, plus: "暂缓", minus: "其他" }); // 暂缓
          this.statistics_list.push({ value: res.data.level_a_total, plus: "A级", minus: "其他" }); // 等级A
          this.statistics_list.push({ value: res.data.level_b_total, plus: "B级", minus: "其他" }); // 等级B
          this.statistics_list.push({ value: res.data.level_c_total, plus: "C级", minus: "其他" }); // 等级C

          // opt_type值为export表示导出数据
          // if(this.data_params.opt_type == "export") {
          //   this.data_params.opt_type = "";
          // }
          this.initChartBing1();
        }
      })
    },
    // 获取客户数据统计底部chart
    // getClientChart() {
    //   this.$http.getClientChart(this.data_params).then((res) => {
    //     if (res.status == 200) {
    //       this.clientChart = res.data;
    //       let num = 0;
    //       let data = [];
    //       this.totalDate = [];
    //       for (let i in this.clientChart.segment) {
    //         if (this.clientChart.segment[i].length > num) {
    //           num = this.clientChart.segment[i].length;
    //           data = this.clientChart.segment[i];
    //         }
    //       }
    //       data.map((item) => {
    //         this.totalDate.push(item.time);
    //       })
          // this.clientChart.segment = {
          //   "4": [
          //     {
          //         time: "2023-07-17 00:00",
          //         count: 8
          //     },
          //     {
          //         time: "2023-07-17 01:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 02:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 03:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 04:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 05:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 06:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 07:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 08:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 09:00",
          //         count: 19
          //     },
          //     {
          //         time: "2023-07-17 10:00",
          //         count: 14
          //     },
          //     {
          //         time: "2023-07-17 11:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 12:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 13:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 14:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 15:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 16:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 17:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 18:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 19:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 20:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 21:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 22:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 23:00",
          //         count: 0
          //     }
          //   ],
          //   "6": [
          //   {
          //         time: "2023-07-17 00:00",
          //         count: 8
          //     },
          //     {
          //         time: "2023-07-17 01:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 02:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 03:00",
          //         count: 6
          //     },
          //     {
          //         time: "2023-07-17 04:00",
          //         count: 4
          //     },
          //     {
          //         time: "2023-07-17 05:00",
          //         count: 0
          //     },
          //     {
          //         time: "2023-07-17 06:00",
          //         count: 1
          //     },
          //     {
          //         time: "2023-07-17 07:00",
          //         count: 23
          //     },
          //     {
          //         time: "2023-07-17 08:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 09:00",
          //         count: 19
          //     },
          //     {
          //         time: "2023-07-17 10:00",
          //         count: 14
          //     },
          //     {
          //         time: "2023-07-17 11:00",
          //         count: 33
          //     },
          //     {
          //         time: "2023-07-17 12:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 13:00",
          //         count: 1
          //     },
          //     {
          //         time: "2023-07-17 14:00",
          //         count: 32
          //     },
          //     {
          //         time: "2023-07-17 15:00",
          //         count: 80
          //     },
          //     {
          //         time: "2023-07-17 16:00",
          //         count: 7
          //     },
          //     {
          //         time: "2023-07-17 17:00",
          //         count: 6
          //     },
          //     {
          //         time: "2023-07-17 18:00",
          //         count: 5
          //     },
          //     {
          //         time: "2023-07-17 19:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 20:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 21:00",
          //         count: 44
          //     },
          //     {
          //         time: "2023-07-17 22:00",
          //         count: 13
          //     },
          //     {
          //         time: "2023-07-17 23:00",
          //         count: 12
          //     }
          //   ],
          //   "7": [
          //   {
          //         time: "2023-07-17 00:00",
          //         count: 8
          //     },
          //     {
          //         time: "2023-07-17 01:00",
          //         count: 11
          //     },
          //     {
          //         time: "2023-07-17 02:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 03:00",
          //         count: 12
          //     },
          //     {
          //         time: "2023-07-17 04:00",
          //         count: 7
          //     },
          //     {
          //         time: "2023-07-17 05:00",
          //         count: 8
          //     },
          //     {
          //         time: "2023-07-17 06:00",
          //         count: 8
          //     },
          //     {
          //         time: "2023-07-17 07:00",
          //         count: 7
          //     },
          //     {
          //         time: "2023-07-17 08:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 09:00",
          //         count: 19
          //     },
          //     {
          //         time: "2023-07-17 10:00",
          //         count: 14
          //     },
          //     {
          //         time: "2023-07-17 11:00",
          //         count: 6
          //     },
          //     {
          //         time: "2023-07-17 12:00",
          //         count: 5
          //     },
          //     {
          //         time: "2023-07-17 13:00",
          //         count: 4
          //     },
          //     {
          //         time: "2023-07-17 14:00",
          //         count: 1
          //     },
          //     {
          //         time: "2023-07-17 15:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 16:00",
          //         count: 42
          //     },
          //     {
          //         time: "2023-07-17 17:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 18:00",
          //         count: 6
          //     },
          //     {
          //         time: "2023-07-17 19:00",
          //         count: 5
          //     },
          //     {
          //         time: "2023-07-17 20:00",
          //         count: 4
          //     },
          //     {
          //         time: "2023-07-17 21:00",
          //         count: 3
          //     },
          //     {
          //         time: "2023-07-17 22:00",
          //         count: 2
          //     },
          //     {
          //         time: "2023-07-17 23:00",
          //         count: 1
          //     }
          //   ]
          // }
    //       this.initChartOne()
    //     }
    //   })
    // },
    getClientChart1() {
      this.$http.newgetClientChart(this.data_params).then((res) => {
        if (res.status == 200) {
          this.clientChart = res.data;
          let num = 0;
          let data = [];
          this.totalDate = [];
          for (let i in this.clientChart.segment) {
            if (this.clientChart.segment[i].length > num) {
              num = this.clientChart.segment[i].length;
              data = this.clientChart.segment[i];
            }
          }
          data.map((item) => {
            this.totalDate.push(item.time);
          })
          this.initChartOne()
        }
      })
    },
    //跳转到数据列表页
    doulist() {
      this.$goPath(`/crm_Follow_up_list`);
    },
    // 改变查询时间
    changePickerDate(value) {
      // if(this.source == 1){
      //   if (value == null) {
      //   this.data_params.date_type = 1;
      //   this.data_params.b_date = ""; // 清空开始时间
      //   this.data_params.e_date = ""; // 清空结束时间
      // } else if (value.length) {
      //   this.data_params.b_date = value[0]; // 赋值开始时间
      //   this.data_params.e_date = value[1]; // 赋值结束时间
      //   // 删除tabs搜索参数
      //   if (this.data_params.date_type) {
      //     delete this.data_params.date_type;
      //   }
      // }
      // this.getClientData();
      // this.getClientChart();
      // }else{
        if (value == null) {
        this.data_params.date_type = 1;
        this.data_params.b_date = ""; // 清空开始时间
        this.data_params.e_date = ""; // 清空结束时间
      } else if (value.length) {
        this.data_params.b_date = value[0]; // 赋值开始时间
        this.data_params.e_date = value[1]; // 赋值结束时间
        // 删除tabs搜索参数
        if (this.data_params.date_type) {
          delete this.data_params.date_type;
        }
      }
      this.new_data()
      this.getClientChart1()
      // }

    }
  },



};
</script>
<style  lang="scss" scoped>
.items-center {
  align-items: center;
}

.criculate {
  background-color: #FFFFFF;

  min-height: 100%;
  padding: 24px;
  margin: 9px;

  .h_date {
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #8a929f;
    // height: 32px;
    margin-bottom: 20px;

    .c2 {
      margin-right: 12px;
      padding: 4px 10px;
      color: #8a929f;
      line-height: 16px;
      font-size: 14px;
      padding: 4px 16px;
      display: inline-block;
      cursor: pointer;

      &.active {
        color: #2d84fb !important;
        font-weight: 500;
        border-radius: 4px;
        background: rgba(45, 132, 251, 0.2);
      }
    }

    .crm-selected {
      // margin-left: 20px;
      margin-right: 20px;
    }

    .listbnt {
      font-size: 14px;
      margin-left: 20px;
      margin-right: 15px;
    }
  }
}

.bg_fff {
  width: 100%;
  height: 254px;

  // background-color: #2d84fb;
  .data_item {
    width: 100%;
    min-width: 210px;
    height: 117px;
    background: #F8F8F8;
    border-radius: 8px;
    padding: 12px;
    display: inline-block;
    // float: left;
    // text-align: center;
    // margin-right: 20px;
    margin-bottom: 20px;
    box-sizing: border-box;

    .top_item {
      height: 16px;
      width: 100%;
      line-height: 16px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      padding: 2px 0;
      margin-bottom: 4px;

      .top_item_text {
        height: 16px;
        margin-right: auto;

        font-size: 14px;
        font-family: PingFang SC, PingFang SC-Medium;
        font-weight: 500;
        text-align: LEFT;
        color: #8A929F;
      }

      .top_item_img {
        height: 16px;
        width: 16px;
      }
    }

    .bottom_item {
      height: 70px;

      .bottom_item_left {
        display: inline-block;
        float: left;
        height: 70px;
        width: 120px;

        .bottom_item_left_num {
          width: 140px;
          height: 45px;
          font-size: 32px;
          font-family: PingFang SC, PingFang SC-Medium;
          font-weight: 500;
          text-align: LEFT;
          color: #2E3C4E;
          line-height: 38px;
        }

        .bottom_item_left_people_num {
          min-width: 14px;
          height: 20px;
          font-size: 14px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: 400;
          text-align: LEFT;
          color: #2E3C4E;
          line-height: 16px;
        }
      }

      .bottom_item_right {
        // display: inline-block;
        width: 30px;
        height: 30px;
        float: right;
        margin-top: 26px;

        img {
          width: 30px;
          height: 30px;
        }
      }

      .bottom_item_right2 {
        // display: inline-block;
        width: 66px;
        height: 72px;
        margin-top: 0;
        float: right;
      }
    }
  }
}

.data_title_wrap {
  width: 100%;
  height: 28px;
  margin: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .data_enter {
    width: 80px;
    height: 28px;

    font-size: 20px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    text-align: LEFT;
    color: #ffffff;
    line-height: 23px;
    margin-right: auto;
  }

  .date_source {
    min-width: 60px;
    height: 16px;
    font-size: 12px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #ffffff;
    line-height: 16px;
    text-align: center;
  }
}

.echarts_wrap {
  width: 100%;

  .echart_l_con {
    width: 100%;
  }
}

.el-col-4-8 {
  width: 16%;
}

@media (max-width: 500px) {

  /*0~1000*/
  .el-col-4-8 {
    width: 16%;
  }
}

@media (min-width: 500px) {

  /*1000~+∞*/
  .el-col-4-8 {
    width: 266px;
  }
}
</style>
  