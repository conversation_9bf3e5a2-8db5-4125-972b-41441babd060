<template>
  <div class="list">
    <el-container>
      <el-header class="div row header-top">
        <myTopTips title="员工列表" :number="tableData.length"></myTopTips>

        <!-- <div class="div row">
          <el-input
            v-model="params.phone"
            placeholder="输入手机号搜索"
          ></el-input>
          <el-input v-model="params.name" placeholder="搜索相关用户"></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div> -->
      </el-header>
      <el-main v-loading="is_table_loading">
        <div class="browse-table">
          <div class="browse div row">
            <div
              class="browse-item"
              v-for="(item, index) in time_array"
              :key="index"
              :class="{ browse_active: item.value === list_params.date_str }"
              @click="onClickBrowse(index, item.id)"
            >
              {{ item.desc }}
            </div>
          </div>
          <div class="block-time div row" v-if="isCustomize">
            <el-date-picker
              v-model="list_params.start"
              type="date"
              placeholder="请选择开始日期"
              value-format="yyyy-MM-dd"
            >
              >
            </el-date-picker>
            <el-date-picker
              v-model="list_params.end"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择结束日期"
            >
              >
            </el-date-picker>
            <el-button type="primary" @click="clickTime">查询</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="grid-content bg-purple div row">
                <div class="left ">
                  <p>成交套数/套</p>
                </div>
                <div class="right">{{ company_employee_info.total }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content bg-purple div row">
                <div class="left ">
                  <p>成交金额/万</p>
                </div>
                <div class="right">{{ company_employee_info.deal_amount }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content bg-purple div row">
                <div class="left ">
                  <p>业绩金额/元</p>
                </div>
                <div class="right">
                  {{ company_employee_info.brokerage_amount }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content bg-purple div row">
                <div class="left ">
                  <p>待结金额/元</p>
                </div>
                <div class="right">
                  {{ 0 - company_brokerage_info.pending_amount }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content bg-purple div row">
                <div class="left ">
                  <p>已结金额/元</p>
                </div>
                <div class="right">
                  {{ company_brokerage_info.finish_amount }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="setMoney ">
          <p style="color: #409EFF">
            注意：该操作为公司进行结算，佣金结算给公司，由公司给员工分配佣金
          </p>
          <div class="div row brokerage_amount_total">
            <p>
              可分配金额
              <i style="color:#409EFF"> {{ brokerage_amount_total }}</i>
            </p>
            <el-input
              type="number"
              placeholder="请填写佣金金额"
              v-model="amount"
            />
            <el-button type="primary" @click="onWithdraw">确定</el-button>
          </div>
        </div>
        <myTable :table-list="tableData" :header="table_header"></myTable>
      </el-main>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :pagesize="params.pagesize"
            :currentPage="params.currentPage"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "statistics_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        phone: "",
        name: "",
        currentPage: 1,
        pagesize: 10,
      },
      time_array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
        { desc: "自定义", value: "customize", id: 8 },
      ],
      list_params: {
        date_str: "day",
        start: "",
        end: "",
        company_id: "",
      },
      isCustomize: false,
      company_employee_info: {},
      company_brokerage_info: {},
      amount: "",
      brokerage_amount_total: 0,
      table_header: [
        {
          label: "名称",
          render: (h, data) => {
            return (
              <p>{data.row.name || data.row.nickname || data.row.user_name}</p>
            );
          },
        },
        { prop: "phone", label: "联系方式" },
        { prop: "total", label: "成交套数" },
        { prop: "deal_amount", label: "成交金额/万" },
        { prop: "brokerage_balance_amount", label: "账户余额/元" },
        { prop: "brokerage_amount", label: "佣金总金额/元" },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.list_params.company_id = this.$route.query.id;
    this.getCompanyEmployeeData();
    this.getCompanyEmployeeList();
    this.getCompanyBrokerageData();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.params.currentPage = 1;
      this.getCompanyEmployeeList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      // this.getPageData();
      this.getCompanyEmployeeList();
    },
    // search() {
    //   if (this.params.phone || this.params.name) {
    //     this.getCompanyEmployeeList();
    //   }
    // },
    getCompanyEmployeeList() {
      this.$http
        .getCompanyEmployeeList(
          this.list_params.company_id,
          this.params.currentPage,
          this.params.pagesize,
          this.params.name,
          this.params.phone,
          this.list_params
        )
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
          }
        });
    },
    getCompanyBrokerageData() {
      this.$http.getCompanyBrokerageData(this.list_params).then((res) => {
        if (res.status === 200) {
          this.company_brokerage_info = res.data;
          this.brokerage_amount_total = res.data.finish_amount;
        }
      });
    },
    getCompanyEmployeeData() {
      this.$http.getCompanyEmployeeData(this.list_params).then((res) => {
        if (res.status === 200) {
          this.company_employee_info = res.data;
        }
      });
    },
    onClickBrowse(index, id) {
      this.list_params.date_str = this.time_array[index].value;
      if (id === 8) {
        this.isCustomize = true;
      } else {
        this.isCustomize = false;
        this.list_params.start = "";
        this.list_params.end = "";
        this.getCompanyEmployeeData();
        this.getCompanyEmployeeList();
        this.getCompanyBrokerageData();
      }
    },
    clickTime() {
      if (!this.list_params.start) {
        this.$message({
          message: "请选择开始时间",
          type: "error",
        });
      } else if (!this.list_params.end) {
        this.$message({
          message: "请选择结束时间",
          type: "error",
        });
      } else {
        this.getCompanyEmployeeData();
        this.getCompanyEmployeeList();
        this.getCompanyBrokerageData();
      }
    },
    onWithdraw() {
      if (!this.amount) {
        this.$message({
          message: "请输入内容提交",
          type: "error",
        });
        return;
      }
      let data = {
        id: this.list_params.company_id,
        amount: this.amount,
      };
      this.$http.settleBrokerageMoney(data).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "结佣成功",
            type: "success",
          });
          this.getCompanyEmployeeData();
          this.getCompanyEmployeeList();
          this.getCompanyBrokerageData();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;
  .browse {
    width: 480px;
    .browse-item {
      margin: 0 5px;
      font-size: 14px;
      padding: 2px 10px;
      border-radius: 50px;
      color: #333;
      &.browse_active {
        color: #fff;
        background: #0068e6;
      }
    }
  }
  .block-time {
    justify-content: flex-start;
    margin: 10px;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
  }
  .bg-purple {
    border: 1px dashed #d3dce6;
  }
  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;
    .left {
      color: #999;
      font-size: 14px;
      text-align: start;
      p {
        color: #333;
      }
      .desc {
        color: #999;
      }
    }
    .right {
      color: #26bf8c;
    }
  }
}
.setMoney {
  margin: 20px 0;
}
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 0;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  width: 40px;
  img {
    width: 100%;
  }
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-main {
// 	padding-top: 40px;
// }
.header-top {
  padding: 60px 20px 0;
}
.brokerage_amount_total {
  justify-content: flex-start;
  margin: 10px 0;
  p {
    margin-right: 5px;
  }
}
</style>
