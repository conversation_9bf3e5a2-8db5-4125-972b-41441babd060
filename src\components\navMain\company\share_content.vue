<template>
  <el-container>
    <el-header class="div row">
      <div class="div row">
        <div class="title">推广信息列表</div>
        <div class="title_number">
          <div>
            当前页面共(
            <i>{{ tableData.length }}</i>
            )条数据
          </div>
        </div>
        <div class="add-build">
          <el-button icon="el-icon-plus" type="primary" @click="createNewData"
            >添加推广内容</el-button
          >
        </div>
      </div>
      <!-- <div class="div row">
				<el-input v-model="input" placeholder="搜索相关推广信息"></el-input>
				<el-button type="primary" icon="el-icon-search" @click="search"
					>搜索</el-button
				>
			</div> -->
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form label-width="100px" :model="form_create">
        <el-form-item label="编辑图片：">
          <div class="div row img-row">
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess1"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_1"
                :src="form_create.img_1"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess2"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_2"
                :src="form_create.img_2"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess3"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_3"
                :src="form_create.img_3"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess4"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_4"
                :src="form_create.img_4"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess5"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_5"
                :src="form_create.img_5"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess6"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_6"
                :src="form_create.img_6"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess7"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_7"
                :src="form_create.img_7"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess8"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_8"
                :src="form_create.img_8"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
            <el-upload
              :headers="myHeader"
              action="/api/common/file/upload/admin?category=6"
              :show-file-list="false"
              :on-success="onSuccess9"
              list-type="picture-card"
              style="margin:5px"
            >
              <img
                v-if="form_create.img_9"
                :src="form_create.img_9"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="内容：">
          <el-input
            :rows="6"
            type="textarea"
            v-model="form_create.content"
            placeholder="请输入需要推广的内容"
            style="width:400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="是否推广：">
          <el-switch
            v-model="form_create.enable"
            active-color="#13ce66"
            inactive-color="#666666"
          >
          </el-switch>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onCreate">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
/* eslint-disable */
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import imgList from "@/components/components/show_img";
import config from "@/utils/config.js";
export default {
  name: "share_content",
  components: {
    myPagination,
    myTable,
    imgList,
  },
  data() {
    return {
      input: "",
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      project_id: "",
      table_header: [
        { prop: "content", label: "推广内容" },
        { prop: "created_at", label: "添加时间" },
        {
          label: "是否推广",
          formatter: (row) => {
            let enable = row.enable;
            if (enable === 1) {
              return (enable = "是");
            } else if (enable === 0) {
              return (enable = "否");
            }
          },
        },
        {
          label: "操作",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="success"
                  onClick={() => {
                    this.updataShare(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  onClick={() => {
                    this.handleDelete(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      dialogCreate: false,
      form_create: {},
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
  },
  mounted() {
    this.project_id = this.$route.query.id;
    this.getDataList();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.$http
        .allShare(this.project_id, this.params.currentPage)
        .then((res) => {
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    goBack() {
      this.$router.back();
    },
    // 搜索相关信息
    search() {
      this.$http
        .projectNews(this.project_id, this.params.currentPage, this.input)
        .then((res) => {
          if (this.input !== "") {
            this.tableData = res.data.data;
          } else {
            this.getDataList();
          }
        });
    },
    handleDelete(row) {
      this.$confirm("此操作将删除该操作, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteShare(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "success",
            message: "已取消删除",
          });
        });
    },
    updataShare(row) {
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
      this.form_create = row;
      row.enable === 1
        ? (this.form_create.enable = true)
        : (this.form_create.enable = false);
    },
    createNewData() {
      this.form_create = {
        id: "",
        project_id: this.project_id,
        enable: true,
        content: "",
        img_1: "",
        img_2: "",
        img_3: "",
        img_4: "",
        img_5: "",
        img_6: "",
        img_7: "",
        img_8: "",
        img_9: "",
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    onSuccess1(response) {
      this.form_create.img_1 = response.url;
    },
    onSuccess2(response) {
      this.form_create.img_2 = response.url;
    },
    onSuccess3(response) {
      this.form_create.img_3 = response.url;
    },
    onSuccess4(response) {
      this.form_create.img_4 = response.url;
    },
    onSuccess5(response) {
      this.form_create.img_5 = response.url;
    },
    onSuccess6(response) {
      this.form_create.img_6 = response.url;
    },
    onSuccess7(response) {
      this.form_create.img_7 = response.url;
    },
    onSuccess8(response) {
      this.form_create.img_8 = response.url;
    },
    onSuccess9(response) {
      this.form_create.img_9 = response.url;
    },
    onCreate() {
      if (!this.form_create.content) {
        this.$message.error("内容不能为空");
        return;
      }
      if (this.dialogTitle === "addData") {
        this.$http.uploadShare(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message.success("创建成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.updataShare(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message.success("修改成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.share-box {
  margin: 20px 0;

  img {
    width: 80px;
    height: 80px;
  }
}
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 0;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
.el-header {
  padding-top: 20px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
.el-main {
  padding: 0;
}
.img-row {
  flex-wrap: wrap;
  .avatar {
    width: 148px;
    height: 148px;
  }
}
</style>
