<template>
    <div>
         <div class="rule0_title">
            切换线索分配模式前请确认以下设置 
          </div>
          <div>
            <el-form :model="form_info" label-position="left">
              <el-form-item label="公海认领客户成员范围">
                <br>
                <div class="flex-row top" >
                    <div class="titlecolor">
                        按成员
                    </div>
                    <el-select ref="get_black_list" style="width: 300px" v-model="form_info.get_black_list" multiple placeholder="请选择"
                      @focus="showPersonnelAuthority('get_black_list')" @change="PersonnelChange">
                      <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
                      </el-option>
                    </el-select>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin: 12px 0px 0px 10px;">
                      <div slot="content" style="max-width: 300px">
                        指定人员可认领公海客户
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </div>
                <div class="flex-row" style="margin-top:10px;"> 
                    <div class="titlecolor">
                        按部门
                    </div>
                    <el-cascader style="width: 300px" :options="AllDepartment" :props="{
                      value: 'id',
                      label: 'name',
                      children: 'subs',
                      multiple: true,
                      checkStrictly: true,
                      emitPath: false
                    }" collapse-tags v-model="form_info.get_black_department"></el-cascader>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin: 12px 0px 0px 10px;">
                      <div slot="content" style="max-width: 300px">
                        指定部门范围成员可认领公海客户
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </div>
              </el-form-item>
              <el-form-item label="公海新增客户消息通知范围">
                <br>
                <div class="flex-row top">
                    <div class="titlecolor">
                        按成员
                    </div>
                       <el-select ref="add_notice_uid" style="width: 300px" v-model="form_info.add_notice_uid" multiple placeholder="请选择"
                          @focus="showPersonnelAuthority('add_notice_uid')" @change="PersonnelChange">
                          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
                          </el-option>
                        </el-select>
                        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin: 12px 0px 0px 10px;">
                          <div slot="content" style="max-width: 300px">
                            该范围内设置成员可以收到公海新增客户线索提醒
                          </div>
                          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                        </el-tooltip>
                </div>
                <div class="flex-row" style="margin-top:10px;">
                    <div class="titlecolor">
                        按部门
                    </div>
                    <el-cascader style="width: 300px" :options="AllDepartment" :props="{
                      value: 'id',
                      label: 'name',
                      children: 'subs',
                      multiple: true,
                      checkStrictly: true,
                      emitPath: false
                    }" collapse-tags v-model="form_info.add_notice_department"></el-cascader>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin: 12px 0px 0px 10px;">
                      <div slot="content" style="max-width: 300px">
                        该设置范围内的部门成员能够收到公海新增客户线索提醒
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <!-- <div style="margin-bottom:90px;">
            <el-button type="warning" size="medium" @click="Confirmswitch">确认切换</el-button>
          </div> -->
          
           <el-dialog :visible.sync="show_add_member" width="400px" :title="department_title" append-to-body>
               <div class="member" ref="memberList">
                 <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
                   :defaultExpandAll="false">
                 </multipleTree>
                 <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
                   <el-button type="text" @click="show_add_member = false">取消</el-button>
                   <el-button type="primary" @click="selectMemberOk">确定</el-button>
                 </div>
               </div>
           </el-dialog>
    </div>
</template>
<script>
 import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
    name: 'MyRecursiveComponent',
    components: {
    multipleTree,
   },
    props:{
        serverData: {
            type: Array,
            default: () => []
        },
        datalist: {
            type: Array,
            default: () => []
        },
        receivedData: {
            type: Object,
            default: () => {}
        }
    },

    data() {
        return {
            form_info:{},
            show_add_member: false, // 部门成员模态框
            department_title: "",
            selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
            AllDepartment:[],
        }
    },
    watch:{
        receivedData:{
            handler(newval,oldval){
                if(this.receivedData){
                  this.form_info = this.receivedData
                    this.form_info.get_black_department = this.receivedData.get_black_department.split(",")
                    this.form_info.add_notice_department = this.receivedData.add_notice_department.split(",")
                    this.form_info.get_black_list = this.receivedData.get_black_list
                      ? this.setArr(this.receivedData.get_black_list)
                      : "";
                    this.form_info.add_notice_uid = this.receivedData.add_notice_uid
                      ? this.setArr(this.receivedData.add_notice_uid)
                      : "";
                }
            }
        }
    },
    mounted(){
        this.getDepartmentList()
    },
    methods:{
        showPersonnelAuthority(val) {
          this.identification = val
          if (this.identification == 'get_black_list' && this.form_info.get_black_list != '') {
            this.selectedIds = this.form_info.get_black_list;
            this.department_title = '手动认领客户成员范围';
          } else if (this.identification == 'add_notice_uid' && this.form_info.add_notice_uid != '') {
            this.selectedIds = this.form_info.add_notice_uid;
            this.department_title = '新增客户通知范围';
          }  else {
            this.selectedIds = [];
          }
          this.$nextTick(() => {
            this.$nextTick(() => {
              if (this.$refs.get_black_list) {
                this.$refs.get_black_list.blur();
              }
              if(this.$refs.add_notice_uid){
                this.$refs.add_notice_uid.blur();
              }
            });
          });
          this.show_add_member = true;
        },
        // 选中变化时触发
        selecetedMember(e) {
          this.selectedIds = e.checkedKeys;
          // this.selectedList = e.checkedNodes;
        },
        selectMemberOk() {
          this.show_add_member = false;
          if (this.identification == 'get_black_list') {
            this.form_info.get_black_list = this.selectedIds;
          }  else if (this.identification == 'add_notice_uid') {
            this.form_info.add_notice_uid = this.selectedIds;
          } 
        },
        PersonnelChange(val) {
          this.selectedIds = val;
        },
        // 获取部门
        getDepartmentList() {
          this.$http.getCrmDepartmentList().then((res) => {
            if (res.status == 200) {
              this.AllDepartment = res.data;
            }
          });
        },
        // 处理部门成员数据
        setArr(arr) {
          let n_arr = arr.split(",");
          let n_arr_2 = n_arr.map((item) => {
            return parseInt(item);
          });
          // ====
          let i = 0;
          if (n_arr_2 != [] && n_arr_2 != undefined) {
            n_arr_2.map((item) => {
              this.$nextTick(() => {
                this.datalist.map((list) => {
                  if (item != list.id) {
                    i++;
                    if (i == this.datalist.length) {
                      n_arr_2.splice(n_arr_2.indexOf(item), 1);
                    }
                  }
                })
                i = 0;
              })
            })
          }
          // ====
          return n_arr_2;
        },
        //确认切换
        Confirmswitch(){
            let result = [];
            result = [];
            if (this.form_info.get_black_department != [] && this.form_info.get_black_department != '') {
              this.form_info.get_black_department.map((item) => {
                if (item.toString().length >= 6) {
                  result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                  result.push(item);
                }
              })
            }
            this.form_info.get_black_department = Array.from(new Set(result));
            this.form_info.get_black_department = this.form_info.get_black_department
              ? this.form_info.get_black_department.join(",")
              : "";

            result = [];
              if (this.form_info.add_notice_department != [] && this.form_info.add_notice_department != '') {
                this.form_info.add_notice_department.map((item) => {
                  if (item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                  } else {
                    result.push(item);
                  }
                })
              }
              this.form_info.add_notice_department = Array.from(new Set(result));
              this.form_info.add_notice_department = this.form_info.add_notice_department
                ? this.form_info.add_notice_department.join(",")
                : "";

            result = [];
            if (this.form_info.get_black_list != [] && this.form_info.get_black_list != '') {
              this.form_info.get_black_list.map((item) => {
                if (item.toString().length >= 6) {
                  result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                  result.push(item);
                }
              })
            }
            this.form_info.get_black_list = Array.from(new Set(result));
            this.form_info.get_black_list = this.form_info.get_black_list
              ? this.form_info.get_black_list.join(",")
              : "";

            result = [];
            if (this.form_info.add_notice_uid != [] && this.form_info.add_notice_uid != '') {
              this.form_info.add_notice_uid.map((item) => {
                if (item.toString().length >= 6) {
                  result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                  result.push(item);
                }
              })
            }
            this.form_info.add_notice_uid = Array.from(new Set(result));
            this.form_info.add_notice_uid = this.form_info.add_notice_uid
              ? this.form_info.add_notice_uid.join(",")
              : "";
        

            this.$http.setSiteCrmSetting(this.form_info).then((res) => {
                if (res.status === 200) {
                    this.form_info.get_black_department = res.data.get_black_department
                    ? this.setArr(res.data.get_black_department)
                    : "";
                  this.form_info.add_notice_department = res.data.add_notice_department
                    ? this.setArr(res.data.add_notice_department)
                    : "";
                 
                  this.form_info.get_black_list = this.form_info.get_black_list
                    ? this.setArr(this.form_info.get_black_list)
                    : "";
                
                  this.form_info.add_notice_uid = this.form_info.add_notice_uid
                    ? this.setArr(this.form_info.add_notice_uid)
                    : "";
                } else {
                    this.form_info.get_black_department = res.data.get_black_department
                    ? this.setArr(res.data.get_black_department)
                    : "";

                  this.form_info.add_notice_department = res.data.add_notice_department
                    ? this.setArr(res.data.add_notice_department)
                    : "";

                  this.form_info.get_black_list = this.form_info.get_black_list
                    ? this.setArr(this.form_info.get_black_list)
                    : "";
                  this.form_info.add_notice_uid = this.form_info.add_notice_uid
                    ? this.setArr(this.form_info.add_notice_uid)
                    : "";
                }
            });
        },
    },

}
</script>
<style lang="scss" scoped>
    .rule0_title{
      color: #2e3c4e;
      margin: 24px 0;
      font-size: 16px;
    }
    .titlecolor{
      margin-right: 10px;
        color: #606266;
    }
    .top{
        margin-top:5px;
    }
</style>