<template>
    <div class="tabel-all">
        <div class="title-box">
            <div class="button-all">
                <el-button type="primary" size="medium" @click="addcustomer()">添加成交单</el-button>
                <el-button type="primary" size="medium" @click="derive()">导出数据</el-button>
            </div>
            <div class="input-all">
                <el-input placeholder="请输入搜索内容" size="small" v-model="input" class="input-with-select">
                    <el-button slot="append" icon="el-icon-search"></el-button>
                </el-input>
            </div>
        </div>
        <el-table v-loading="is_table_loading" :data="tableData" class="house_table" border
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column label="报告id" v-slot="{ row }">
                {{ row.id ? row.id : "--" }}
            </el-table-column> -->
            <el-table-column label="发起人名称" v-slot="{ row }">
                {{ row.name ? row.name : "--" }}
            </el-table-column>
            <el-table-column label="发起人手机号" v-slot="{ row }" width="120">
                {{ row.phone ? row.phone : "--" }}
            </el-table-column>
            <!-- <el-table-column label="类型" prop="id" width="55"> </el-table-column> -->
            <el-table-column label="项目名称" v-slot="{ row }">
                {{ row.mianji ? row.project : "--" }}
                <!-- <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="row.number"
              >
                <el-button
                  size="mini"
                  slot="reference"
                  @click="copy(row.number)"
                  >复制编号</el-button
                >
              </el-popover> -->
            </el-table-column>
            <el-table-column label="面积" v-slot="{ row }">
                {{ row.mianji ? row.mianji : "--" }}
            </el-table-column>
            <el-table-column label="单价" v-slot="{ row }">
                {{ row.danjia ? row.danjia : "--" }}
                <!-- <el-tag type="warning" v-if="row.status==0">未审核</el-tag>
                    <el-tag type="success" v-if="row.status==1">通过审核</el-tag>
                    <el-tag type="danger" v-if="row.status==2">审核已拒绝</el-tag> -->
            </el-table-column>
            <el-table-column label="成交时间" v-slot="{ row }">
                {{ row.cjrq ? row.cjrq : "--" }}
            </el-table-column>
            <el-table-column label="佣金占比" v-slot="{ row }">
                {{ row.proportion ? row.proportion : "--" }}
            </el-table-column>
            <el-table-column label="成交金额" v-slot="{ row }">
                {{ row.amount ? row.amount : "--" }}
            </el-table-column>
            <el-table-column label="添加时间" v-slot="{ row }">
                {{ row.created_at ? row.created_at : "--" }}
            </el-table-column>
            <el-table-column label="修改时间" v-slot="{ row }">
                {{ row.updated_at ? row.updated_at : "--" }}
            </el-table-column>
            <el-table-column label="公司佣金" v-slot="{ row }">
                {{ row.company_commission ? row.company_commission : "--" }}
            </el-table-column>
            <el-table-column label="成员佣金占比" v-slot="{ row }">
                {{ row.member_commission.proportion ? row.member_commission.proportion : "--" }}
            </el-table-column>
            <el-table-column label="成员佣金名称" v-slot="{ row }">
                {{ row.member_commission.user_name ? row.member_commission.user_name : "--" }}
            </el-table-column>
            <el-table-column label="成员佣金描述" v-slot="{ row }">
                {{ row.member_commission.descp ? row.member_commission.descp : "--" }}
            </el-table-column>
            <!-- ctime -->
            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
            </el-table-column>
        </el-table>
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="params.total"
            :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
        </el-pagination>
    </div>
</template>
<script>
export default {
    name: "crm_customer_table",
    components: {},
    data() {
        return {
            is_table_loading: true,
            input: '',
            tableData: [],
            multipleSelection: [],
            params: {
                page: 1,
                total: 0,
                per_page: 10,
                cat_id: '',
            },

        }
    },
    mounted() {
        this.gettabelList()
    },
    methods: {
        addcustomer() {
            console.log("添加成交单")
            this.$router.push("crm_customer_tableDeatil");
        },
        derive() {
            console.log("导出数据了")
        },
        gettabelList() {
            this.$http.getdistributiontabel().then((res) => {
                this.tableData = res.data.data
                this.is_table_loading = false;
                console.log(this.tableData, "获取到的数据")
            }).catch()
        },
        onEditData(row) {
            this.$router.push("crm_customer_tableDeatil");
            console.log(row.id, "编辑")
        },
        onPageChange(e) {
            this.params.page = e;
            this.getDataList();
        },
        handleSelectionChange(val) {
            this.multipleSelection = val
        }
    }
}
</script>
<style  scoped lang="scss">
.title-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .input-all {
        width: 200px;
    }
}
</style>