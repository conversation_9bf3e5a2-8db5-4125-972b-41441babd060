<template>
  <div class="info_item flex-row" :class="{ current: current }">
    <!-- <div class="checkbox" v-show="show_select">
      <el-checkbox v-model="info.checked" @change="onSelectInfo"></el-checkbox>
    </div> -->
    <div class="cover">
      <img :src="this.$imageFilter(info.pic, 'w_240')" alt="" />
      <div v-if="this.info.vr_url != ''" class="cover_VR">
        <img src="https://img.tfcs.cn/backup/static/admin/crm/static/vr_icon.png" alt="" draggable="false">
      </div>
      <div v-if="this.info.cj_type > 0" class="cover_deal">
        <img src="https://img.tfcs.cn/backup/static/admin/house/<EMAIL>" alt="" draggable="false">
      </div>
      <div class="label_box">
        <span class="label" v-if="info.info_level > 1">精选</span>
        <!-- <span class="label label2" v-if="info.upgrade_type > 1">置顶</span> -->
      </div>
    </div>
    <div class="detail flex-row flex-1" @click="$emit('click', info)">
      <div class="detail_left flex-1">
        <div class="title flex-row">
          <span class="">{{ info.title || info.title_promotion }}</span>
          <span v-if="info.shi != 0 || info.ting != 0 || info.wei != 0" class="line"></span>
          <span v-if="info.shi != 0 || info.ting != 0 || info.wei != 0">{{ info.shi }}室{{ info.ting }}厅{{ info.wei
          }}卫</span>
          <span class="line" v-if="info.mianji"></span>
          <span v-if="info.mianji">{{ info.mianji }}平米</span>
          <span class="line" v-if="info.chaoxiang"></span>
          <span v-if="info.chaoxiang">{{ info.chaoxiang }}</span>
          <span class="line" v-if="info.sz_floor || info.total_floor"></span>
          <span v-if="info.sz_floor || info.total_floor">{{ info.sz_floor }}/{{ info.total_floor }}层</span>
          <span class="line" v-if="info.score" style="background: #06BFFF;"></span>
          <span v-if="info.score" style="color: #06BFFF;">{{ info.score }}</span>
          <!-- <span class="line" v-if="info.score"></span> -->

          <!-- <span class="time">{{ info.begintime }}</span> -->
        </div>
        <div class="spec_row flex-1">
          <div class="spec flex-row">
            <span class="mgr15">房源编号：{{ info.id }}</span>
            <span class="mgr15">类型：{{ info.trade_type }}</span>
            <span class="mgr15" v-if="info.yongtu">用途：{{ info.yongtu }}</span>
            <span class="mgr15">区域：{{ info.area_name }} - {{ info.region_name }}</span>
          </div>
        </div>
        <div class="spec_row mt5 flex-1">
          <div class="spec flex-row">
            <span class="mgr15">维护人：{{ (info.whr && info.whr.cname) || "--" }}</span>
            <span>全员最后维护： </span><span class="primary">{{ info.follow_all_ctime }}</span>
            <span class="mgr15 red">({{ info.follow_all_ctime | fiterDate }})</span>
            <span class="mgr15 span_last">维护人最后维护：<span class="red">({{ (info.whr && info.whr.follow_count) || 0
            }}条)</span>{{ info.whr && info.whr.follow_ctime }}
            </span>
            <span v-if="info.cj_type > 0">成交人：{{ info.cjr_name }}</span>
          </div>
        </div>
        <div class="spec_row mt5 flex-1" v-if="info.follow_memo">
          <div class="spec flex-row">
            <span class="mgr15">备注：{{ info.follow_memo }}</span>
          </div>
        </div>
        <div class="mini_button_group flex-row">
          <!-- <div class="sta is_top">顶</div>
        <div class="sta is_img">图</div>
        <div class="sta is_vip">VIP</div>
        <div class="sta is_key">钥</div>
        <div class="sta is_weituo">委</div> -->
          <div class="type jujiao flex-row align-center" v-if="info.is_top">
            <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
            <div class="type_name">聚焦</div>
          </div>
          <div class="type vip flex-row align-center" v-if="info.djwtr_id > 0">
            <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
            <div class="type_name">VIP</div>
          </div>
          <div class="type weituo flex-row align-center" v-if="info.wtr_id">
            <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
            <div class="type_name">委托</div>
          </div>
          <template v-if="info.trade_status">
            <div class="type youxiao flex-row align-center" :class="{
              wuxiao: info.trade_status_id == 8,
              zanhuan: info.trade_status_id == 5,
              chengjiao:
                info.trade_status_id == 100 || info.trade_status_id == 101,
              tingzhi: info.trade_status_id == 102,
            }">
              <div class="type_name">{{ info.trade_status }}</div>
            </div>
            <div class="type level flex-row align-center" :class="'level' + info.level" v-if="info.level">
              <!-- <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div> -->
              <div class="type_name">{{ info.level }}级</div>
            </div>
          </template>
          <div class="type yaoshi flex-row align-center" v-if="info.has_key">
            <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div>
            <div class="type_name">有钥匙</div>
          </div>
          <div class="type yaoshi flex-row align-center" v-if="info.pic_num">
            <!-- <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div> -->
            <div class="type_name">{{ info.pic_num }}图</div>
          </div>
          <div class="type yaoshi flex-row align-center" v-if="info.is_share">
            <!-- <div class="img">
              <img src="@/assets/<EMAIL>" alt="" />
            </div> -->
            <div class="type_name">公盘</div>
          </div>
          <div class="type yaoshi flex-row align-center" v-if="info.privacy_type == 2">
            <div class="type_name">隐</div>
          </div>

        </div>
      </div>
      <div class="detail_right flex-box j-center">
        <div class="price" v-if="info.trade_type_status == 1 || info.trade_type_status == 3">
          {{ info.sale_price / 10000 }}
          <span class="unit"> 万 </span>
          <template v-if="info.price_change.num > 0">
            <span :class="info.price_change.direct == 1 ? 'up' : 'down'">
              {{ info.price_change.direct == 1 ? "↑" : "↓" }}
            </span>
          </template>
        </div>
        <div class="adv_price" v-if="info.trade_type_status == 1 || info.trade_type_status == 3">
          {{ info.danjia }}元/m²
        </div>
        <div class="price" v-if="info.trade_type_status == 2">
          {{ info.rent_price }}<span class="unit"> 元/月</span>
          <template v-if="info.price_change && info.price_change.num > 0">
            <span :class="info.price_change.direct == 1 ? 'up' : 'down'">
              {{ info.price_change.direct == 1 ? "↑" : "↓" }}
            </span>
          </template>
        </div>
        <div class="adv_price" v-if="info.trade_type_status == 3">
          {{ info.rent_price }} 元/月
        </div>
        <!-- info.cj_type: 大于零表示成交 -->
        <div class="pr_label flex-row align-center j-end"
          v-if="info.price_change && info.price_change.num > 0 && info.cj_type <= 0">
          <div class="img">
            <img src="@/assets/icon/jiang.png" alt="" />
          </div>
          <div class="pr_label_name">
            近期业主调价{{ info.price_change.num || 0 }}次
            {{ info.price_change.direct_desc }}
            {{ info.price_change.range | formatPrice(info.trade_type_status) }}
          </div>
        </div>
        <!-- 成交记录提示 -->
        <div class="pr_label flex-row align-center j-end" v-if="info.cj_type && info.cj_type > 0">
          <div class="img">
            <img src="@/assets/icon/jiang.png" alt="" />
          </div>
          <div class="pr_label_name">
            挂牌价{{ parseFloat(info.sale_price_original / 10000) }}万元，
            {{ info.cj_time_show | formConverDate(info.cj_time_show) }}成交
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HouseItem',
  data() {
    return {
    }
  },
  filters: {
    filterTradeType(val) {
      let tradeTypeName = ''
      switch (Number(val)) {
        case 1:
          tradeTypeName = '出售'
          break;
        case 2:
          tradeTypeName = '出租'
          break;
        case 3:
          tradeTypeName = '租售'
          break;
        default:
          break;
      }
      return tradeTypeName
    },
    fiterDate(val) {
      if (!val) val = new Date()
      // 指定日期和时间
      var EndTime = new Date(val);
      // 当前系统时间
      var NowTime = new Date();
      var t = NowTime.getTime() - EndTime.getTime();
      var d = Math.floor(t / 1000 / 60 / 60 / 24);

      return (d + '天')
    },
    formatPrice(val, type) {
      console.log(val, type);
      let unit = "元"

      if (Number(val) >= 10000) {
        if (type == 1 || type == 3) {
          unit = "万元"
        } else {
          unit = "万元/月"
        }
        return Number(val) / 10000 + unit
      } else {
        if (type == 1 || type == 3) {
          unit = "元"
        } else {
          unit = "元/月"
        }
        return val + unit
      }
    },
    // 2020-05-11转换2020年5月11日
    formConverDate(val) {
      const date = new Date(val);
      const year = date.getFullYear(); // 获取年份
      const month = date.getMonth() + 1; // 获取月份
      const day = date.getDate(); // 获取日期
      const formattedDate = `${year}年${month}月${day}日`; // 转换格式
      return formattedDate;
    }
  },
  props: {
    info: {
      type: Object,
      default: () => { },
    },
    is_owner: Boolean,
    current: Boolean,
  },
  methods: {
    collectHouse(info) {
      this.$service.lm.collect({ info_id: info.id }).then((res) => {
        if (res.data.status === 200) {
          this.$emit('collect', 1)
          info.is_collect = 1
          this.$message.success(res.data.message)
        }
      })
    },
    cancelCollectHouse(info) {
      this.$service.lm.cancelCollect({ info_id: info.id }).then((res) => {
        if (res.data.status === 200) {
          this.$message.success(res.data.message)
          this.$emit('collect', 0)
          info.is_collect = 0
        }
      })
    },
    setShowing(info) {
      console.log(info)
      this.$emit('setshowing', info)
    },
    toCopy(info) {
      console.log(info)
      this.$router.push({
        name: 'LmEdit',
        query: {
          is_copy: 1,
          id: info.id,
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
.info_item {
  padding: 24px;
  align-items: flex-start;
  position: relative;
  background-color: #fff;
  cursor: pointer;
  transition: 0.36s;

  &.current {
    background-color: #f7faff;
  }

  &:after {
    content: "";
    height: 1px;
    position: absolute;
    left: 24px;
    right: 24px;
    bottom: 0;
    background-color: #dde1e9;
  }

  &:hover {
    background-color: #f7faff;
  }

  &.current {
    background-color: #f7faff;
  }

  >.checkbox {
    margin-right: 12px;
  }
}

.cover {
  width: 130px;
  height: 118px;
  min-width: 130px;
  margin-right: 24px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .cover_VR {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 80px;
    left: 10px;
  }

  .cover_deal {
    width: 60px;
    height: 60px;
    position: absolute;
    top: 0px;
    right: 0px;
  }

  .label_box {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
  }

  .label {
    display: block;
    line-height: 1;
    padding: 3px 5px;
    font-size: 12px;
    border-bottom-left-radius: 8px;
    color: #fff;
    background-color: #00caa7;
    background-image: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);

    ~.label {
      margin-top: 6px;
    }
  }

  .label2 {
    background-color: #f56c6c;
    background-image: linear-gradient(135deg, #f56c6c 0%, #f56c6c 100%);
  }
}

.detail {
  position: relative;

  .detail_left {
    width: calc(100% - 145px);
  }

  .buy_success {
    position: absolute;
    width: 100px;
    height: 100px;
    top: -18px;
    left: 240px;
  }

  .detail_right {
    padding: 10px 24px;
    align-items: flex-end;

    .price {
      font-size: 28px;
      color: #f56c6c;

      .unit {
        font-size: 14px;
      }

      .up {
        font-size: 14px;
        font-weight: 600;
      }

      .down {
        font-size: 14px;
        font-weight: 600;
        color: #00caa7;
      }
    }

    .adv_price {
      margin-top: 10px;
      font-size: 14px;
      color: rgba(92, 92, 92, 1);
    }
  }

  .title {
    align-items: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 12px;
    line-height: 1.5;

    .line {
      width: 2px;
      height: 20px;
      margin: 0 5px;
      background: #333;
    }

    .time {
      margin-left: 24px;
      font-size: 14px;
      font-weight: initial;
      color: #8a929f;
    }
  }

  .label_list {
    margin-bottom: 12px;
    height: 22px;

    .label {
      display: inline-block;
      margin-right: 12px;
      padding: 3px 10px;
      border-radius: 2px;
      font-size: 12px;
      background-color: #f1f4fa;
    }
  }

  .spec_row {
    font-size: 14px;
    color: #8a929f;

    &.mt5 {
      margin-top: 5px;
    }

    .spec {
      white-space: nowrap;
      overflow: hidden;

      .red {
        color: #f56c6c;
      }

      .primary {
        color: #3f8bed;
      }

      .mgr15 {
        margin-right: 15px;
      }

      .mgl24 {
        margin-left: 24px;
      }

      .span_last {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .price {
    align-items: flex-end;
    margin-left: 24px;

    .total_price {
      margin-left: 12px;
      line-height: 1;
      // width: 90px;
      text-align: right;
      // margin-left: 32px;
      font-size: 24px;
      font-weight: bold;
      color: #fe6c17;

      .unit {
        margin-left: 3px;
        font-weight: initial;
        font-size: 14px;
      }
    }
  }

  .mini_button_group {
    margin-top: 12px;

    .sta {
      padding: 1px 8px;
      margin-right: 10px;
      border-radius: 3px;
      font-size: 12px;

      &.is_top {
        color: #fa7e94;
        border: 1px solid #fa7e94;
        background: #f6e1f6;
      }

      &.is_img {
        color: #3f8bed;
        border: 1px solid #3f8bed;
        background: #7fffd4;
      }

      &.is_vip {
        color: #4d4eae;
        border: 1px solid #fdfb57;
        background: #fcffe6;
      }

      &.is_key {
        color: #3c8bff;
        border: 1px solid #aedfff;
        background: #e6f7ff;
      }

      &.is_weituo {
        color: #be664c;
        border: 1px solid #fc805a;
        background: #f7f7f7;
      }
    }

    .type {
      padding: 7px 18px 7px 15px;
      border-radius: 4px;
      margin-right: 10px;

      &.jujiao {
        color: #fb1d15;
        background: rgba(251, 29, 21, 0.2);
      }

      &.weituo {
        background: #dbe8fa;
        color: #2d84fb;
      }

      &.youxiao {
        background: #dbe8fa;
        color: #2d84fb;
      }

      &.wuxiao {
        background: #d1cbcb;
        color: #fff;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }

      &.zanhuan {
        background: #fda148;
        color: #fff;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }

      &.chengjiao {
        background: #2f84f7;
        color: #fff;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }

      &.tingzhi {
        background: #f74c4c;
        color: #fff;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }

      &.yaoshi {
        background: #dbe8fa;
        color: #2d84fb;
      }

      &.vip {
        background: #3a3f53;
        color: #f3c840;
      }

      .img {
        width: 18px;
        height: 18px;
        margin-right: 5px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .type_name {
        font-size: 12px;
      }
    }
  }

  .bottom_row {
    padding: 12px 0;
  }

  .agent_info {
    .avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    >span {
      font-size: 14px;
      margin-left: 12px;
      color: #8a929f;
    }

    .cert_info {
      margin-left: 6px;
      display: flex;
      align-items: center;
      font-size: 13px;

      &.no_cert {
        cursor: pointer;
        color: #9fa3aa;
      }

      &.is_cert {
        color: #f6c238;
      }

      .cert_icon {
        margin-right: 3px;
        width: 20px;
        height: 20px;
      }
    }
  }
}

.pr_label {
  margin-bottom: 12px;
  background: #fff1f5;
  color: #fb656a;
  font-size: 11px;
  align-items: center;
  height: 24px;
  // width: 252px;
  white-space: nowrap;
  padding: 0 8px 0 4px;
  margin-top: 10px;
  border-radius: 2px;
  font-weight: 700;

  .img {
    width: 14px;
    height: 14px;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .pr_label_name {
    display: inline-block;
    color: #fb656a;
    font-size: 12px;
  }
}
</style>
