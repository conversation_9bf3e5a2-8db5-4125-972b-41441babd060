<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm div row" style="margin-bottom: 24px">
          <div>
            <el-button
              type="primary"
              @click="addServicer"
              size="mini"
              class="el-icon-plus"
              >添加客服</el-button
            >
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="TableRowStyle"
          >
            <el-table-column prop="id" label="ID" width="100"></el-table-column>
            <el-table-column prop="open_kfid" label="账号ID"></el-table-column>
            <el-table-column prop="name" label="名称"></el-table-column>
            <el-table-column label="头像" width="100">
              <template slot-scope="scope">
                <el-avatar
                  :src="scope.row.avatar"
                  style="height: 40px; width: 40px"
                ></el-avatar>
              </template>
            </el-table-column>
            <el-table-column label="默认链接">
              <template slot-scope="scope">
                <el-link :href="scope.row.link" type="primary">
                  {{ scope.row.link }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="greeting" label="欢迎语"></el-table-column>
            <el-table-column
              prop="updated_at"
              label="更新时间"
            ></el-table-column>
            <el-table-column
              prop="created_at"
              label="创建时间"
            ></el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-link
                  style="margin-right: 10px"
                  type="primary"
                  @click="onChangeEdit(scope.row)"
                  >编辑</el-link
                >
                <el-link
                  type="danger"
                  style="margin-left: 20px"
                  @click="onDelete(scope.row)"
                  >删除</el-link
                >
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog title="账号修改" :visible.sync="dialogCreateEdit">
      <el-form label-width="120px" :model="editWelcome">
        <el-form-item label="账号ID：">
          <el-input
            maxlength="20"
            v-model="accountEdit.open_kfid"
            placeholder="请填写账号ID"
          ></el-input>
        </el-form-item>
        <el-form-item label="账号名称：">
          <el-input
            maxlength="20"
            v-model="accountEdit.name"
            placeholder="请填写账号名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="客服头像：">
          <el-select v-model="accountEdit.mediaid" placeholder="请选择">
            <el-option
              v-for="(v, i) in userOption"
              :key="i"
              :label="v.url"
              :value="v.mediaid"
            >
              <span style="float: left" @click="goUrl(v.url)">{{ v.url }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预览：">
          <el-avatar
            :src="this.avatarUrl"
            :size="200"
            shape="square"
          ></el-avatar>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="onCreatetemporaryEdit"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
    <el-dialog title="客服添加" :visible.sync="dialogCreateAdd">
      <el-form :model="addServicerUp" label-width="120px">
        <el-form-item label="客服名称：">
          <el-input
            maxlength="20"
            v-model="addServicerUp.name"
            placeholder="请填写客服名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="客服头像：">
          <el-select v-model="addServicerUp.mediaid" placeholder="请选择">
            <el-option
              v-for="(v, i) in userOption"
              :key="i"
              :label="v.url"
              :value="v.mediaid"
            >
              <span style="float: left" @click="goUrl(v.url)">{{ v.url }}</span>

              <!-- <el-avatar :src="v.url" :size="50" shape="square"></el-avatar> -->
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预览：">
          <el-avatar
            :src="this.avatarUrl"
            :size="200"
            shape="square"
          ></el-avatar>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="addSer"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "customer_service",
  components: {},
  data() {
    return {
      avatarUrl: "",
      addServicerUp: {
        name: "",
        mediaid: "",
      },
      accountEdit: {
        open_kfid: "",
        name: "",
        mediaid: "",
      },
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      userOption: [],
      ImageVisible: false,
      imageUrl: "",
      is_table_loading: true,
      inputVal: "",
      dialogCreate: false,
      dialogCreateEdit: false,
      dialogCreateAdd: false,
      editWelcome: {},
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
  },
  watch: {},
  methods: {
    getCustomerService() {
      this.is_table_loading = true;
      this.$http.getCustomerService({ params: this.params }).then((res) => {
        console.log(res, "客服账号");
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    getMediaOption() {
      this.$http.mediaOption().then((res) => {
        if (res.status === 200) {
          this.userOption = res.data;
        }
      });
    },
    goUrl(url) {
      this.avatarUrl = url;
    },
    onChangeEdit(row) {
      this.accountEdit = row;
      this.dialogCreateEdit = true;
    },
    onDelete(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.accountDel({ open_kfid: row.open_kfid }).then((res) => {
            console.log(row.open_kfid);
            if (res.status === 200) {
              this.$message.success("删除成功");
              this.getCustomerService();
            }
          });
        })
        .catch(() => {});
    },
    handleSuccessAvatar(response) {
      this.accountEdit.mediaid = response.url;
    },
    handlePictureCardPreviewAvatar(file) {
      this.imageUrl = file.response.url;
      this.ImageVisible = true;
    },
    handleRemoveAvatar(response) {
      this.accountEdit.mediaid = response.url;
    },
    addServicer() {
      this.dialogCreateAdd = true;
    },
    addSer() {
      if (!this.addServicerUp.name || !this.addServicerUp.mediaid) {
        this.$message.error("请补全信息后提交");
        return;
      }
      let msg = this.$message.success("请稍后...");
      this.$http.accountAdd(this.addServicerUp).then((res) => {
        if (res.status === 200) {
          this.$message.success("添加成功");
          this.getCustomerService();
          this.dialogCreateAdd = false;
          this.addServicerUp.name = "";
          this.addServicerUp.mediaid = "";
          msg.close();
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    TableRowStyle({ rowIndex }) {
      let rowBackground = {};
      if ((rowIndex + 1) % 2 === 0) {
        rowBackground.background = "#EFEFEF";
        return rowBackground;
      }
    },
    onChangeCome(row) {
      this.editWelcome = row;
      this.dialogCreate = true;
    },
    onCreatetemporaryEdit() {
      if (
        !this.accountEdit.open_kfid ||
        !this.accountEdit.name ||
        !this.accountEdit.mediaid
      ) {
        this.$message.error("请补全信息后提交");
        return;
      }
      let msg = this.$message.success("请稍后...");
      this.$http.accountEdit(this.accountEdit).then((res) => {
        if (res.status === 200) {
          this.$message.success("修改成功");
          this.getCustomerService();
          this.dialogCreateEdit = false;
          this.accountEdit.open_kfid = "";
          this.accountEdit.name = "";
          this.accountEdit.mediaid = "";
          msg.close();
        }
      });
    },
  },
  created() {},
  mounted() {
    this.getCustomerService();
    this.getMediaOption();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.service-edit {
  margin-bottom: 30px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
.el-button {
  border-radius: 3px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 300px;
}
</style>
