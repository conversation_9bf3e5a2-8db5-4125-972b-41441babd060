<template>
    <div class="allpage">
        <div class="first_left">
            <div class="title">
                当以下情况发生时：
            </div>
            <div class="step">
                <div class="circle"></div>
                <div style="margin-left:10px;"> 第1步</div>
            </div>
            <div class="condition">
                <div class="flex-row kuang">
                    <img src="https://img.tfcs.cn/yidongduan/tiaojian.png" alt="">
                  <span class="title_er">条件判断</span>
                </div>
              <div style="margin:0px 10px 10px 15px;">
                <el-form :inline="true" :rules="rules" :model="formInline" label-position="top">
                  <el-form-item label="时间类型">
                    <el-select v-model="formInline.time_type"  placeholder="时间类型">
                      <el-option 
                      v-for="(label, key) in conditiondata.time_type" 
                        :key="key" 
                        :label="label" 
                        :value="key">
                      </el-option>
                    </el-select>
                    <!-- <el-input placeholder="大于" :disabled="true" style="width: 90px;margin-left:10px;"></el-input> -->
                    <el-select style="width: 90px;margin-left:10px;" v-model="formInline.time_status"
                     placeholder="请选择行为">
                    <el-option label="大于" value="1"></el-option>
                    <el-option label="小于" value="2"></el-option>
                    </el-select>
                    <el-input  style="width: 163px;margin-left:10px;"
                     v-model="formInline.diy_time" placeholder="请输入天数"  @blur="validateInput">
                     <template slot="append">天</template></el-input>
                    <span style="margin-left:10px;">且</span>
                    <el-select style="width: 165px;margin-left:13px;" v-model="formInline.action"
                    clearable placeholder="请选择行为">
                      <el-option 
                      v-for="(label, key) in conditiondata.action" 
                        :key="key" 
                        :label="label" 
                        :value="key">
                      </el-option>
                    </el-select>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
                      <div slot="content" style="max-width: 300px; line-height: 20px;">
                        创建时间：录入到系统的创建时间<br>
                        更新时间：最近一次更新时间（包含基本的跟进、维护修改资料等全部操作）<br>
                        线索时间：最近一次进入系统的线索时间<br>
                        跟进时间：最近一次完成跟进的时间 （普通跟进、查看电话跟进，备注跟进，语音跟进，带看跟进，有实际填写内容的跟进，系统自动补增的不包括在内如“某某成为了维护人”）<br>
                        带看时间：最近一次带看日期的时间（带看/复看）<br>
                        时间天数：仅时间类型可选大于或小于时间天数； 行为 统一为 大于（超过）时间天数。<br>
                        未评级：周期内等级未标记(ABCD)<br>
                        未拨打：周期内最近一次查看电话并电话文字跟进(状态标记未接通/已接通) 含外呼通话<br>
                        未带看：周期内没有带看/复看记录<br>
                        未查看电话：周期内没有查看电话记录（查看电话/电话跟进）<br>
                        未跟进：周期内没有普通跟进、查看电话跟进，备注跟进，语音跟进，带看跟进（有实际填写内容的跟进，系统自动补增的不包括在内如：某某成为了维护人）<br>
                        未成交：周期内非我司成交状态  <br>
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>

                  </el-form-item>
                  <div class="title_er" style="margin-left:0px;">
                    + 同时满足(可选设置)
                  </div>
                  <el-form-item label="标签">
                    <el-cascader  style="width: 330px;" v-model="formInline.label"
                    multiple clearable placeholder="请选择标签"
                      :options="conditiondata.label" 
                      :props="{
                        value: 'id',
                        label: 'name',
                        children: 'label',
                        emitPath: false,
                        multiple: true,
                      }">
                    </el-cascader>
                  </el-form-item>
                  <el-form-item label="等级">
                    <el-select style="width: 330px;"  v-model="formInline.level"
                    multiple
                    clearable placeholder="请选择等级">
                      <el-option v-for="item,index in conditiondata.level" :key="index" :label="item.title+'级'"
                        :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="来源">
                      <el-cascader style="width: 330px;"
                       v-model="source_idvalue"
                      clearable placeholder="请选择来源" :options="conditiondata.source"
                        :props="{
                          label: 'title',
                          value: 'id',
                          children: 'children',
                          checkStrictly: true,
                          emitPath: false,
                          multiple:true,
                        }">
                      </el-cascader>
                  </el-form-item>
                  <el-form-item label="状态">
                      <el-select style="width: 330px;" 
                      multiple v-model="formInline.tracking"
                      clearable placeholder="请选择状态">
                        <el-option v-for="item in conditiondata.tracking" :key="item.id" :label="item.title" :value="item.id">
                        </el-option>
                      </el-select>
                  </el-form-item>
                  <el-form-item label="类型">
                      <el-select style="width: 330px;"
                      multiple v-model="formInline.type"
                      clearable placeholder="请选择类型">
                        <el-option v-for="item in conditiondata.type" :key="item.id" :label="item.title" :value="item.id">
                        </el-option>
                      </el-select>
                  </el-form-item>
                  <el-form-item label="带看">
                      <el-select style="width: 330px;" v-model="formInline.is_take"
                      clearable placeholder="请选择带看状态">
                        <el-option v-for="item in conditiondata.is_task" :key="item.id" :label="item.title" :value="item.id">
                        </el-option>
                      </el-select>
                  </el-form-item>
                </el-form>
              </div>
            </div>
        </div>
        <div class="separator">
            <div class="icon-wrapper">
              <i  style="font-size: 35px;color:#4E5969;" class="el-icon-right"></i>
            </div>
        </div> <!-- 分割线（箭头） -->
        <div class="tow_right">
            <div class="title title_right">
                就执行以下操作：
            </div>
            <div class="step">
                <div class="circle"></div>
                <div style="margin-left:10px;"> 第2步</div>
            </div>
            <div class="condition">
              <div class="flex-row kuang">
                    <img src="https://img.tfcs.cn/yidongduan/xingwei.png" alt="">
                  <span class="title_er">适用范围</span>
              </div>
              <div style="margin:0px 10px 10px 15px;">
                <el-form :inline="true" :rules="rules" :model="formInline" label-position="top">
                  <el-form-item label="执行范围" style="width: 88%;">
                    <el-select ref="execute_uid" style="width: 100%" v-model="formInline.selectway" placeholder="请选择成员/部门">
                      <el-option label="成员" value="1"></el-option>
                      <el-option label="部门" value="2"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="执行成员" style="width: 88%;" v-if="formInline.selectway==1">
                    <el-select ref="execute_uid" style="width: 100%" v-model="formInline.execute_uid" multiple placeholder="请选择成员"
                      :collapse-tags="SelectAlltags==1" @focus="showPersonnelAuthority('execute_uid')"
                      clearable @clear="clearSelectAll" @change="PersonnelChange">
                      <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="执行部门" style="width: 88%;" v-if="formInline.selectway==2">
                    <el-cascader style="width: 100%" :options="AllDepartment"
                    :disabled="SelectAll==1" clearable :props="{
                      value: 'id',
                      label: 'name',
                      children: 'subs',
                      multiple: true,
                      checkStrictly: true,
                      emitPath: false
                    }"  v-model="formInline.execute_department"></el-cascader>
                  </el-form-item>
                  <el-form-item label="执行周期" prop="execute_type">
                    <el-select style="width: 330px;" v-model="formInline.execute_type" placeholder="请选择类型"
                    @change="changeexecute_type">
                      <el-option 
                      v-for="item in timingtype" 
                        :key="item.id" 
                        :label="item.name" 
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="执行时间" prop="execute_hour" 
                  v-if="formInline.execute_type==4">
                    <el-select v-model="formInline.execute_hour"
                      style="width: 330px;" placeholder="请选择">
                      <el-option
                        v-for="item in interval_hourdata"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="执行时间 (设置后24小时后生效)" prop="execute_hour" 
                  v-if="formInline.execute_type!=4">
                    <el-select v-model="formInline.execute_hour"
                      style="width: 330px;" placeholder="请选择">
                      <el-option
                        v-for="item in timePickerOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="每周周几" prop="execute_week" v-if="formInline.execute_type==2">
                    <el-select style="width: 330px;" v-model="formInline.execute_week"
                    clearable placeholder="请选择周几" multiple>
                      <el-option 
                      v-for="item in everyweek" 
                        :key="item.id" 
                        :label="item.name" 
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="每月几号" prop="execute_month" v-if="formInline.execute_type==3">
                    <el-select style="width: 330px;" v-model="formInline.execute_month"
                    clearable placeholder="请选择日期" multiple>
                      <el-option  
                        v-for="day in days"  
                        :key="day"  
                        :label="day+'号'"  
                        :value="day"  
                      ></el-option> 
                    </el-select>
                  </el-form-item>
                  <el-form-item label="流程状态">
                    <el-select style="width: 330px;" v-model="formInline.status"
                    clearable placeholder="请选择流程状态">
                    <el-option label="禁用" value="0"></el-option>
                    <el-option label="启用" value="1"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="执行类型">
                    <el-select style="width: 330px;" v-model="formInline.types"
                     placeholder="请选择执行类型">
                    <el-option label="自动掉公" value="1"></el-option>
                    <el-option label="自动分配" value="2"></el-option> 
                    <el-option label="自动转交" value="3"></el-option>
                    <el-option label="自动提醒" value="4"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分配类型" v-if="formInline.types==2">
                    <el-select style="width: 330px;" v-model="formInline.allocationType"
                     placeholder="请选择执行类型">
                    <el-option label="成员" value="1"></el-option>
                    <el-option label="分组" value="2"></el-option> 
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分配成员" prop="uid" style="width: 330px;" v-show="formInline.types==2&&formInline.allocationType==1">
                    <el-select ref="fenpei_uid" style="width: 100%" v-model="formInline.fenpeiuid"
                      :collapse-tags="SelectAll==1" placeholder="请选择成员" multiple clearable
                      @clear="clearSelectAll"
                      @focus="showPersonnelAuthority('fenpei_uid')" @change="PersonnelChange">
                      <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分配组" prop="group_id" v-show="formInline.types==2&&formInline.allocationType==2">
                    <el-select style="width: 330px;" v-model="formInline.group_id"
                     placeholder="请选择分组">
                      <el-option 
                      v-for="item in respectuser_list" 
                        :key="item.id" 
                        :label="item.title" 
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="转交成员" style="width: 94%;" v-if="formInline.types==3">
                    <el-select ref="zuanjiao_uid" style="width: 100%" v-model="formInline.uid"
                      placeholder="请选择成员" multiple
                      @focus="showPersonnelAuthority('zuanjiao_uid')" @change="PersonnelChange">
                      <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
              <!-- <div class="submitsure">
                <el-button type="primary"  @click="submitdata">确定编辑</el-button>
              </div> -->
            </div>
        </div>
        <el-dialog :visible.sync="show_add_member" width="400px" :title="department_title" append-to-body>
          <div class="member" ref="memberList">
            <div style="margin-bottom:20px"> 
              <el-button  type="primary" size="small" @click="Select_All">全选</el-button>
            </div>
            <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
              :defaultExpandAll="false">
            </multipleTree>
            <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
              <el-button type="text" @click="show_add_member = false">取消</el-button>
              <el-button type="primary" @click="selectMemberOk">确定</el-button>
            </div>
          </div>
        </el-dialog>
        <el-dialog :visible.sync="danxuan_member" width="400px" :title="department_title" append-to-body>
          <div class="member" ref="memberList">
            <memberListSingle v-if="danxuan_member" :list="serverData" :defaultValue="danxuanIds" @onClickItem="danxuanMember"
              :defaultExpandAll="false" :searchshow="true">
            </memberListSingle>
            <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
              <el-button type="text" @click="danxuan_member = false">取消</el-button>
              <el-button type="primary" @click="selectMemberOk">确定</el-button>
            </div>
          </div>
        </el-dialog>
    </div>
</template>
<script>
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import memberListSingle from '@/components/navMain/crm/components/portrait_single.vue'
export default {
  components: {
    multipleTree,
    memberListSingle
  },
  props:{
    conditiondata:{
      type: Object,
      default: () => {},
    },
    editdata:{
      type: Object,
      default: () => {}
    }
  },
    data() {
        return {
          formInline:{
            time_type:"",//时间类型
            diy_time:"",//自定义时长(天数需转换为小时)
            action:"",//行为
            label:"",//标签
            level:"",//等级
            source:"",//来源
            source2:"",//二级来源
            tracking:"",//状态
            type:"",//类型
            execute_uid:"",//执行成员
            execute_department:"",//执行部门
            execute_type:1,//执行类型
            execute_week:"",//每周几执行
            execute_month:"",//每月几号执行
            execute_hour:"",//每天几点执行任务(0-23)
            status:"1",//流程状态
            types:"1",//执行类型  1="自动掉公" 2="自动分配" 3="自动转交" 4="提醒"
            selectway:"1",//前端自定义字段，无需传值
            allocationType:"1",//前端自定义字段，无需传值
            time_status:"1",//判断大于小于
            uid:"",//转交成员或者自动分配成员
            group_id:"",//自动分配选择分组
            fenpeiuid:"",//前端自定义字段，无需传值
          },
          danxuan_member:false,//单选成员选择框
          danxuanIds:[],
          source_idvalue:"",//来源
          show_add_member:false,//成员选择框
          department_title:"",
          serverData: [], // 部门成员数据
          selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
          datalist: [], // 全部部门成员列表
          AllDepartment: [],
          AllDepartmentA: [],
          timingtype:[
            {id:1,name:"每天"},
            {id:2,name:"每周"},
            {id:3,name:"每月"},
            {id:4,name:"每小时"}
          ],//执行类型
          everyweek:[
            {id:0,name:"周日"},
            {id:1,name:"周一"},
            {id:2,name:"周二"},
            {id:3,name:"周三"},
            {id:4,name:"周四"},
            {id:5,name:"周五"},
            {id:6,name:"周六"},
           

          ],//执行类型选择每周，须选择每周几
          days: Array.from({ length: 31 }, (_, i) => i + 1), // 生成1到31的数组 
          rules: {
            diy_time: [
              { required: true, message: '请输入天数', trigger: 'blur' },
            ],
            execute_type: [
              { required: true, message: '请选择执行类型', trigger: 'blur' },
            ], 
            execute_hour: [
              { required: true, message: '请选择执行时间', trigger: 'blur' },
            ],
            execute_month:[
              { required: true, message: '请选择执行时间', trigger: 'blur' },
            ],
            execute_week:[
              { required: true, message: '请选择执行时间', trigger: 'blur' },
            ],
            uid:[
            { required: true, message: '请选择成员', trigger: 'blur' },
            ],
            group_id:[
            { required: true, message: '请选择分组', trigger: 'blur' },
            ],
          },
          interval_hourdata:[
            {value:2,label:"间隔2小时"},
            {value:4,label:"间隔4小时"},
            {value:6,label:"间隔6小时"},
            {value:8,label:"间隔8小时"},
            {value:10,label:"间隔10小时"}
          ],//当执行周期为每小时时(execute_type==4),选择时间间隔
          selectedHour:"",//转后的时间，
          SelectAll:0,//是否全选
          SelectAlltags:0,//隐藏tags
          all_id:[],
          identification:"",
          respectuser_list:[],//分组
          website_id:"",//站点id
          allLabel:[],//所有来源
        }
    },
    created(){
      this.website_id = this.$route.query.website_id
      if(this.editdata){
        this.formInline = JSON.parse(JSON.stringify(this.editdata)); // 深拷贝客户类型列表
        this.dataformat(this.formInline)
      }
    },
    watch:{
      editdata: {  
        handler(newVal) {
          this.SelectAll = 0
          // console.log(newVal);
          this.dataformat(newVal)
          if(newVal.execute_department==""&&newVal.execute_uid==""){
            this.SelectAlltags = 1
            this.all_id = this.datalist.map(item => item.id); // 提取所有 ID
                  this.formInline.execute_uid = this.all_id;
            }
        }
      },
      "formInline.execute_type":{
        handler(newVal){
          // console.log(newVal); 
          if(newVal&&newVal==2){
            this.formInline.execute_month = ""
          }else if(newVal&&newVal==3){
            this.formInline.execute_week = ""
          }
        }
      }
    },
    computed: {
      timePickerOptions() {
        const options = [];
        for (let i = 0; i < 24; i++) {
          const startHour = String(i).padStart(2, '0');
          const endHour = String((i + 1) % 24).padStart(2, '0');
          options.push({
            label: `${startHour}:00--${endHour}:00`,
            value: `${i}`
          });
        }
        return options;
      },
    },
    mounted(){
      this.getMemberList();
      this.getDepartmentList() 
      this.getassociationsatff()
    },
    methods:{
      dataformat(newVal){
        if(newVal){
            if(newVal.execute_uid){
              newVal.selectway = "1"
            }
            if(newVal.execute_department){
              newVal.selectway = "2"
            }
            if(!newVal.execute_department&&!newVal.execute_uid){
              newVal.selectway = "1"
            }
            if(newVal.uid&&newVal.types==2){
              // newVal.fenpeiuid = newVal.uid.split(",").map(Number)
              newVal.fenpeiuid = newVal.uid
              ? this.setArr(newVal.uid)
              : "";;
              newVal.allocationType = "1"
            }else if(newVal.uid&&newVal.types==3){
              newVal.uid = newVal.uid
              ? this.setArr(newVal.uid)
              : "";
            }
            if(newVal.group_id){
              newVal.allocationType = "2"
            }
        this.formInline = JSON.parse(JSON.stringify(newVal)); // 深拷贝客户类型列表 
            if(this.formInline.time_type){
              this.formInline.time_type = this.formInline.time_type.toString()
            }else{
              this.formInline.time_type = ""
            }
            if(this.formInline.types){
              this.formInline.types = this.formInline.types.toString()
            }else{
              this.formInline.types = ""
            }
            if(!this.formInline.is_task){
              this.formInline.is_take = ""
            }
            if(this.formInline.execute_type!=4){
              this.formInline.execute_hour = this.formInline.execute_hour.toString()
            }
            this.formInline.status = this.formInline.status.toString()
            if(this.formInline.action&&this.formInline.action!=0){
              this.formInline.action = this.formInline.action.toString()
            }else{
              this.formInline.action = ""
            }
            if(this.formInline.time_status){
              this.formInline.time_status = this.formInline.time_status.toString()
            }
            if (this.formInline.source) {
                this.formInline.source = this.formInline.source.split(",").map(Number); // 转换为数字数组
            } else {
                this.formInline.source = []; // 如果为空，赋值为空数组
            }
            // 检查并转换 source2
            if (this.formInline.source2) {
                this.formInline.source2 = this.formInline.source2.split(",").map(Number); // 转换为数字数组
            } else {
                this.formInline.source2 = []; // 如果为空，赋值为空数组
            }
            // 将两个数组合并成一个字符串
            this.source_idvalue = this.formInline.source.concat(this.formInline.source2).join(',');
            this.source_idvalue = this.source_idvalue.split(",")
            if(this.formInline.label){
              this.formInline.label = this.formInline.label.split(",")
            }
            if(this.formInline.execute_department){
              this.SelectAll = 0
              this.formInline.execute_department = this.formInline.execute_department.split(",")
            }
            this.formInline.execute_uid = this.formInline.execute_uid
              ? this.setArr(this.formInline.execute_uid)
              : "";

            if(this.formInline.type){
              this.formInline.type = this.formInline.type.split(",").map(Number)
            }
            if(this.formInline.tracking){
              this.formInline.tracking = this.formInline.tracking.split(",").map(Number)
            }
            if (this.formInline.level) {
                this.formInline.level = this.formInline.level.split(",").map(Number); // 将字符串数组转换为数字数组
            }
            if(this.formInline.execute_month){
              this.formInline.execute_month = this.formInline.execute_month.split(",").map(Number)
            }
            // console.log(this.formInline.execute_week);
            if(this.formInline.execute_week){
              this.formInline.execute_week = this.formInline.execute_week.split(",").map(Number)
              
            }else{
              this.formInline.execute_week = ""
            }
            if(this.formInline.group_id&&this.formInline.group_id>0){
              this.formInline.allocationType = "2"
            }else{
              this.formInline.group_id = ""
            }
            this.formInline.diy_time =  this.formInline.diy_time / 24
            
          }
      },
      //确定提交编辑
      submitdata(){
        // console.log(this.formInline);
        let parmar = JSON.parse(JSON.stringify(this.formInline)); // 深拷贝客户类型列表
        if(!this.formInline.time_type){
          return this.$message.warning("请填写时间类型")
        }
        if((this.formInline.time_type||this.formInline.action)&&!this.formInline.diy_time){
              return this.$message.warning("请填写天数")
        }
        // console.log(this.formInline.action);
        if(!this.formInline.action){
          delete parmar.action
        }
        if(!this.formInline.execute_hour){
            return this.$message.warning("请填写执行时间")
          }
          // console.log(this.formInline.execute_week);
        if(this.formInline.execute_type==2&&this.formInline.execute_week===""){
          return this.$message.warning("请填写每周周几")
        }
        if(this.formInline.execute_type==3&&!this.formInline.execute_month){
          return this.$message.warning("请填写每月几号")
        }
        if(this.formInline.types==3&&!this.formInline.uid.length){
          return this.$message.warning("自动转交请选择转交成员")
        }
        if(this.formInline.allocationType==1&&!this.formInline.fenpeiuid.length){
          return this.$message.warning("自动分配请选择分配成员")
        }
        if(this.formInline.allocationType==2&&!this.formInline.group_id){
          return this.$message.warning("自动分配请选择分配分组")
        }
        if(this.formInline.types==2&&this.formInline.allocationType==1){
          if(Array.isArray(this.formInline.fenpeiuid)){
            parmar.uid = this.formInline.fenpeiuid.join(",")
          }else{
            parmar.uid = this.formInline.fenpeiuid
          }
          parmar.group_id = 0
        }
        if(this.formInline.types==2&&this.formInline.allocationType==2){
            parmar.group_id = this.formInline.group_id
            parmar.uid = ""
        }
        delete parmar.allocationType
        delete parmar.fenpeiuid
        if(this.formInline.types==1||this.formInline.types==4){
          parmar.uid = ""
          parmar.group_id = 0
        }
        parmar.execute_week = this.formInline.execute_week.toString()
        if(this.formInline.diy_time){
          parmar.diy_time =  this.formInline.diy_time * 24;
        }
        if(this.formInline.level){
          parmar.level = this.formInline.level.toString()
        }
        // 初始化 this.formInline.source 和 this.formInline.source2 为数组
        this.formInline.source = [];
        this.formInline.source2 = [];
        const traverseTree = (node, parentId = null) => {
            // 判断当前节点的 id 是否在 source_idvalue 中
            if (this.source_idvalue.map(String).includes(String(node.id))) {
                if (parentId === null) {
                    // 如果没有父级 id，说明是父节点
                    this.formInline.source.push(node.id);
                } else {
                    // 如果有父级 id，说明是子节点
                    this.formInline.source2.push(node.id);
                }
            }
            // 递归处理子节点
            if (node.children && node.children.length > 0) {
                node.children.forEach(childNode => {
                    traverseTree(childNode, node.id);  // 传递父级 id 给子节点
                });
            }
        };
        // 遍历根节点
        this.conditiondata.source.forEach(rootNode => {
            traverseTree(rootNode);  // 从根节点开始递归
        });
        // 去重处理
        this.formInline.source = [...new Set(this.formInline.source)];
        if(this.formInline.source){
            parmar.source = this.formInline.source.toString()
        }
        this.formInline.source2 = [...new Set(this.formInline.source2)];
        if(this.formInline.source2){
            parmar.source2 = this.formInline.source2.toString()
        }else{
            parmar.source2 = ""
        }
        if(this.formInline.tracking){
          parmar.tracking = this.formInline.tracking.toString()
        }
        if (this.formInline.execute_month.length&& Array.isArray(this.formInline.execute_month)) {
            // 将 execute_month 转换为字符串
            parmar.execute_month = this.formInline.execute_month.join(",")
        }

        if(this.formInline.execute_week.length&& Array.isArray(this.formInline.execute_week)){
          parmar.execute_week = this.formInline.execute_week.join(",")
        }
        if(this.formInline.execute_type!=2){
          parmar.execute_week = ""
        }
        if(!this.formInline.execute_uid.length&&!this.formInline.execute_department.length){
          return this.prompt()
        }
        if(this.formInline.execute_department&& Array.isArray(this.formInline.execute_department)){
          parmar.execute_department = this.formInline.execute_department.join(",")
        }
        if(this.formInline.execute_uid.length&&this.SelectAll==1){
          parmar.execute_uid = ""//当成员选中并且SelectAll=1，说明是全选，所以要传空值，并且清空部门的值
        }else if(this.formInline.execute_uid.length&&this.SelectAll==0){
          //如果成员长度存在并且SelectAll=0，说明是选中了成员
          parmar.execute_uid = this.formInline.execute_uid.join(",")
        }else{
          parmar.execute_uid = ""
        }
        if(this.formInline.selectway==1&&this.formInline.execute_uid.length){
          parmar.execute_department = ""
        }
        if(this.formInline.selectway==2&&this.formInline.execute_department.length){
          parmar.execute_uid = ""
        }
        if(this.formInline.type){
          parmar.type = this.formInline.type.toString()
        }
        parmar.is_take = this.formInline.is_take.toString()||0
        if(this.formInline.label && Array.isArray(this.formInline.label)){
          parmar.label = this.formInline.label.join(",")
        }
        if(this.formInline.types==3&&this.formInline.uid.length){
          if(Array.isArray(this.formInline.uid)){
            parmar.uid = this.formInline.uid.join(",")
          }else{
            parmar.uid = this.formInline.uid
          }
          parmar.group_id = 0
        }
        // console.log(parmar,"=======");
          this.finalsubmission(parmar)
      },
      //最后确定编辑
      finalsubmission(parmar){
        this.$confirm('流程将于次日按设置周期执行，编辑后成员将收到即将掉公提醒，可根据实际情况修改调整。是否继续?', '流程编辑提醒', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if(parmar.selectway){
            delete parmar.selectway
          }
          this.$http.editnewpublic(parmar).then(res=>{
            if(res.status==200){
              this.SelectAll = 0
              this.identification = ""
              this.$message.success("编辑成功！")
              this.$emit("handleClose","")
              this.$emit("gettabdata","")
            }
          })
        }).catch((err) => {
          console.log(err);
          this.$message({
            type: 'info',
            message: '已取消'
          });          
        });
      },
      //大于天数
      validateInput() {
        const value = this.formInline.diy_time;
        if (!value || isNaN(value) || Number(value) <= 0) {
          this.formInline.diy_time = ""
          this.$message.warning("请输入大于0的数字")
        }
      },
      // //选择来源分别赋值
      // sourceLabel_status(e){
      //   this.allLabel = e
      //   // if(e.length>1){
      //   //   this.formInline.source2 = e[1]
      //   //   this.formInline.source = e[0]
      //   // }else{
      //   //   this.formInline.source = e[0]
      //   //    this.formInline.source2= ""
      //   // }
      // },
      //提示
      prompt(){
        this.$confirm('成员和部门都为空的，默认全部成员, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.Select_All()
            .then(() => {
              // this.identification = 'execute_uid'
              this.selectMemberOk(); // 调用 selectMemberOk 方法
            })
            .catch(error => {
              // console.error("Error in Select_All:", error);
            });
        }).catch((err) => {
          console.log(err);
          this.$message({
            type: 'info',
            message: '已取消'
          });          
        });
      },
      // 全选
      Select_All() {
        return new Promise((resolve, reject) => {
          try {
            this.SelectAll = 1; // 设置全选标志
            this.SelectAlltags = 1
            let selectall_id = this.datalist; // 获取数据列表
            let all_id = selectall_id.map(item => item.id); // 提取所有 ID
            this.selectedIds = all_id; // 更新选中的 ID 列表
            resolve(); // 操作成功，调用 resolve
          } catch (error) {
            reject(error); // 如果出错，调用 reject
          }
        });
      },
      //取消全选
      clearSelectAll(){
        this.SelectAll = 0
        this.SelectAlltags = 0
        this.selectedIds = []
        if(this.identification == 'execute_uid'){
          this.formInline.execute_uid = ''
        }else if(this.identification == 'fenpei_uid'){
          this.formInline.fenpeiuid  = ''
        }
     
        
      },
      // 选中变化时触发
      selecetedMember(e) {
        this.selectedIds = e.checkedKeys;
      },
       // 处理部门成员数据
        setArr(arr) {
          let n_arr = arr.split(",");
          let n_arr_2 = n_arr.map((item) => {
            return parseInt(item);
          });
          // ====
          let i = 0;
          if (n_arr_2 != [] && n_arr_2 != undefined) {
            n_arr_2.map((item) => {
              this.$nextTick(() => {
                this.datalist.map((list) => {
                  if (item != list.id) {
                    i++;
                    if (i == this.datalist.length) {
                      n_arr_2.splice(n_arr_2.indexOf(item), 1);
                      // console.log(n_arr_2,"观察");
                    }
                  }
                })
                i = 0;
              })
            })
          }
          // ====
          return n_arr_2;
        },
      //单选成员
      danxuanMember(e){
        this.danxuanIds = e.checkedKeys;
      },
      selectMemberOk() {
        this.show_add_member = false;
        this.danxuan_member = false;
        console.log(this.identification);
        if (this.identification == 'execute_uid'){
          this.formInline.execute_uid = this.selectedIds;
        }else if(this.identification == 'zuanjiao_uid'){
          this.formInline.uid = this.danxuanIds;
        }else if(this.identification == 'fenpei_uid'){
          this.formInline.fenpeiuid = this.selectedIds;
        }
      },
      PersonnelChange(val) {  
        this.selectedIds = val;
      },
      //当执行周期改变时
      changeexecute_type(){
          this.formInline.execute_hour = ""
      },
      //打开选择成员弹框 
      showPersonnelAuthority(val) {
        this.identification = val
        if(this.identification == 'execute_uid'){
          this.selectedIds = this.formInline.execute_uid || [];
          this.department_title = '请选择执行成员范围';
        }else if(this.identification == 'zuanjiao_uid'){
          this.selectedIds = this.formInline.uid || [];
          this.department_title = '请选择转交成员';
        }else if(this.identification == 'fenpei_uid'){
          this.selectedIds = this.formInline.fenpeiuid || [];
          this.department_title = '请选择分配成员';
        }  else {
          this.selectedIds = [];
        }
        this.$nextTick(() => {
          // console.log(1111111);
          this.$nextTick(() => {
            if(this.$refs.execute_uid){
              this.$refs.execute_uid.blur();
            }
            if(this.$refs.zuanjiao_uid){
              this.$refs.zuanjiao_uid.blur();
            }
            if(this.$refs.fenpei_uid){
              this.$refs.fenpei_uid.blur();
            }
          });
        });
        if(this.identification == 'execute_uid'){
          this.show_add_member = true;
        }else if(this.identification == 'zuanjiao_uid'){
          this.danxuan_member = true;
        } else if(this.identification == 'fenpei_uid'){
          this.show_add_member = true;
        } 
     
      },
      //获取分组
      getassociationsatff(){
             this.$http.getcluegrouping().then((res)=>{
                 if (res.status==200){
                     this.respectuser_list = JSON.parse(JSON.stringify(res.data))
                 }
             })
      },
    // 获取部门成员列表
    async getMemberList() {
      await this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
          if(this.formInline.execute_department==""&&this.formInline.execute_uid==""){
            this.SelectAlltags = 1
            this.all_id = this.datalist.map(item => item.id); // 提取所有 ID
                  this.formInline.execute_uid = this.all_id;
            }
        }
      })
    },
     // 递归数据处理
     recursionData(data) {
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    // 获取部门
    getDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status == 200) {
          this.AllDepartment = res.data;
          this.AllDepartmentA = res.data;
          this.AllDepartmentA.map((item) => {
            if(item.subs && item.subs.length) {
                item.disabled = true;
            } else {
                item.disabled = false;
            }
            return item;
          })
        }
      });
    },
    },
}
</script>
<style lang="scss" scoped>
.allpage {
  display: flex;
  // height: 100vh; /* 让页面充满整个视口高度 */
  overflow-y: auto;   /* 超出部分可滚动 */
  // margin-bottom: 30px ;
  .title{
    color: #4E5969;
    font-size: 18px;
    margin-top: 10px;
  }
  .title_right{
        margin-left: 40px;
    }
  .step{
    display: flex;
    align-items: center;
    margin-top: 30px;
    .circle {
      width: 10px; /* 圆圈的宽度 */
      height: 10px; /* 圆圈的高度 */
      border: 2px solid #165DFF; /* 蓝色边框 */
      border-radius: 50%; /* 圆形 */

    }
  }
  .condition{
    width: 100%;
    height: 600px;
    margin-top: 20px; 
    border-radius: 8px;
    box-shadow: 0 4px 9px 2px rgba(0, 0, 0, 0.1); /* 阴影效果 */
    overflow: hidden;
    overflow-y: auto;   /* 超出部分可滚动 */
    .submitsure{
      text-align: right;
      margin: 25px 102px 30px 50px;
    }
  }
  .kuang{
    align-items: center;
    margin: 15px 15px;
  }
  .title_er{
    color: #4E5969;
    font-size: 16px;
    margin-left: 8px;
  }

}

.first_left,
.tow_right {
  flex: 1; /* 平分空间 */
  padding: 20px; /* 内边距 */
}

.separator {
  position: relative; /* 相对定位 */
  width: 1px; /* 分割线宽度 */
  background-color: #e7eaef; /* 分割线颜色 */
  // height: 100%; /* 分割线高度 */
}

.icon-wrapper {
  position: absolute;
  top: 10px; /* 距离顶部 20 像素 */
  left: -28px; /* 调整图标位置，适应设计 */
  background-color: white; /* 圆形背景色 */
  border-radius: 50%; /* 圆形 */
  padding: 10px; /* 内边距，增加圆的大小 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 阴影效果 */
}
::v-deep.el-form--label-top .el-form-item__label {
  padding: 0 0 0 !important;
}
</style>