<template>
  <div>
    <div class="content-box-crm">
      <div class="table-top-box div row">
        <div class="t-t-b-left div row"></div>
        <div class="t-t-b-right div row">
          <el-button type="success" @click="downloadXLS(1)">
            下载txt示例
          </el-button>
          <el-button type="success" @click="downloadXLS(2)">
            下载xlsx示例
          </el-button>
          <el-button type="primary" @click="add"> 添加号码库</el-button>
        </div>
      </div>
      <div>
        <el-table v-loading="is_table_loading" :data="telData" border :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row :row-style="$TableRowStyle">
          <!-- <el-table-column prop="id" label="订单ID"></el-table-column> -->
          <el-table-column prop="library_name" label="号码库名称" width="120"></el-table-column>
          <el-table-column width="80" prop="phone_count" label="号码数量"></el-table-column>
          <el-table-column prop="send_count" width="80" label="发送次数"></el-table-column>
          <el-table-column prop="created_at" label="创建时间"></el-table-column>
          <el-table-column prop="updated_at" label="更新时间"></el-table-column>
          <el-table-column label="操作" v-slot="{ row }">
            <el-link @click="edit(row)">编辑</el-link>
            <el-link style="margin: 0 10px" @click="telLog(row)">号码列表</el-link>
            <el-popconfirm title="确定删除吗？" style="margin: 0 10px" @onConfirm="cancelTel(row)">
              <el-link slot="reference" type="danger">删除</el-link>
            </el-popconfirm>
          </el-table-column>
        </el-table>

        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
          :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
        </el-pagination>
      </div>
    </div>
    <el-dialog width="500px" append-to-body :visible.sync="is_showDia" :title="telTitle">
      <div v-if="is_showDia">
        <el-form label-width="120px">
          <el-form-item label="名称">
            <el-input v-model="task_form.library_name"></el-input>
          </el-form-item>
          <el-form-item label="号码库" v-if="isAdd">
            <el-upload :limit="1" class="upload-demo" :on-change="change" :on-success="handleSuccessAvatarTemporary"
              :on-remove="handleRemoveAvatarTemporary" :accept="fileaccept" :action="user_avatar" :auto-upload="false"
              ref="uploadfile">
              <!-- :headers="myHeader"
              :action="user_avatar" -->
              <el-button class="el-icon-download" size="small">本地上传</el-button>
              <div slot="tip" class="el-upload__tip">
                <span>
                  可以是一行一个的纯手机号的.txt文本文件
                  或者是以.xlsx结尾的电子表格
                </span>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button v-loading="add_loading" type="primary" @click="confirmAdd">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="800px" append-to-body :visible.sync="is_tel_showDia" title="号码列表">
      <telLog :id="currentId" v-if="is_tel_showDia"></telLog>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config";
import axios from "axios"
import telLog from './telLog.vue'
export default {
  components: { telLog },
  data() {
    return {
      telData: [],
      is_table_loading: false,
      is_table_loading1: false,
      params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      is_showDia: false,
      task_form: {
        library_name: ''
      },
      add_loading: false,
      isAdd: false,
      telTitle: '',
      file: null,
      is_tel_showDia: false,
      currentId: ''
    }
  },
  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    fileaccept() {
      var val = ".txt,.xlsx";
      return val;
    },
  },
  created() {
    this.getList()
    this.user_avatar = `/api/admin/personnelMatters/sendSms`;
  },
  methods: {
    getList() {
      this.is_table_loading = true
      this.$http.getMaketingTelList(this.params).then(res => {
        if (res.status == 200) {
          this.telData = res.data.data
          this.total = res.data.total
        }
        this.is_table_loading = false

      }).catch(() => {
        this.is_table_loading = false
      })
    },
    // 添加编辑号码库开始
    add() {
      this.isAdd = true
      this.telTitle = "添加号码库"
      this.task_form = {
        library_name: ''
      }
      this.file = null
      this.is_showDia = true
    },
    edit(row) {
      this.isAdd = false
      this.telTitle = "编辑号码库名称"
      this.task_form = {
        id: row.id,
        library_name: row.library_name
      }
      this.is_showDia = true
    },
    addTel() {
      if (!this.task_form.library_name) {
        this.$message.warning("请输入名称")
        return
      }
      if (!this.file) {
        this.$message.warning("请上传电话库模板")
        return
      }

      this.add_loading = true
      let file = new FormData()
      file.append("file", this.file)
      file.append("library_name", this.task_form.library_name)
      axios.post('/api/admin/marketing_sms/addNumberLibrary', file, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") } }).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "提交成功")
          this.getList()
          this.is_showDia = false
        } else {
          this.$message.warning(res.message || "提交失败")
        }
        this.add_loading = false
      })
        .catch((err) => {
          this.add_loading = false
          this.$message.error(err.response?.data?.message || "提交失败")
        })

    },
    editTel() {
      this.add_loading = true
      this.$http.editMarketTel(this.task_form).then(res => {
        if (res.status == 200) {
          this.$message.success("修改成功")
          this.is_showDia = false
          this.add_loading = false
          this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })
    },
    cancelTel(row) {
      this.$http.cancelMarketTel(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success("删除成功")
          this.getList()
        }
      })
    },
    confirmAdd() {
      if (this.isAdd) {
        this.addTel()
      } else {
        this.editTel()
      }
    },

    handleSuccessAvatarTemporary(response) {
      this.task_form.file = response.url;
    },
    handleRemoveAvatarTemporary(response) {
      this.task_form.file = response.url;
    },
    // 添加编辑号码库结束
    telLog(row) {
      this.currentId = row.id
      this.is_tel_showDia = true
    },
    change(e) {
      this.file = e.raw
    },
    downloadXLS(type) {
      if (type == 1) {
        window.open("https://img.tfcs.cn/backup/static/admin/xls/phone_sample_template.rar")
      }
      if (type == 2) {
        window.open("https://img.tfcs.cn/backup/static/admin/xls/phone_sample_template_xlsx.xlsx")
      }
    },




    onPageChange(current_page) {
      this.params.page = current_page;
      this.getList()
    },




  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}

.content-box-crm {
  padding: 0;
}

.ml30 {
  margin-left: 30px;
}

.padd10 {
  padding: 10px 0 40px;
}

.title {
  padding-top: 20px;
  padding-left: 75px;
}

.pay_img {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.pay_tips {
  margin-top: 10px;
  text-align: center;
  color: #6bcc03;
  font-size: 28px;
}
</style>