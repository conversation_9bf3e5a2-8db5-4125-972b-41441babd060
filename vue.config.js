let publicPath = "/admin/";
if(process.env.NODE_ENV === "production"){
  if(process.env.npm_lifecycle_event == "build:oss"){
    publicPath = "https://img.tfcs.cn/static_pc/";
  }
}

module.exports = {
  runtimeCompiler: true,
  lintOnSave: false,
  publicPath: publicPath,
  productionSourceMap: false,
  outputDir: "dist/admin",
  css: {
    loaderOptions: {
      less: {
        //这里的选项会传递给less-loader
        javascriptEnabled: false,
      },
    },
  },
  devServer: {
    host: "webfenxiao.tfcs.cn",
    https: true,
    disableHostCheck: true,
    port: 8080,
    proxy: {
      "/api": {
        target: "https://yun.tfcs.cn",
        changeOrigin: true,
      },
    },
  },
  pwa: {
      themeColor: "#2a2b2e",
      manifestOptions: {
        "icons": [
          {
            "src": publicPath+"static/icons/pwa-512x512.png",
            "sizes": "512x512",
            "type": "image/png"
          },
          {
            "src": publicPath+"static/icons/pwa-192x192.png",
            "sizes": "192x192",
            "type": "image/png"
          },
        ],
        "name": "T+系统",
        "short_name": "T+系统",
        "orientation": "portrait",
        "display": "standalone",
        "display_override": [
          "window-controls-overlay"
        ],
        "start_url": "./admin",
        "description": "T+系统 PWA",
        "background_color": "#ffffff",
        "theme_color": "#2a2b2e"
    },
    iconPaths: {
      favicon32: publicPath+'static/icons/favicon-32x32.png', 
      favicon16: publicPath+'static/icons/favicon-16x16.png',
      maskIcon: null,
      msTileImage: null
    },
    workboxOptions: {
      skipWaiting: true,
      exclude: [/\.php$/,/\.html$/,/\.js$/,/\.css$/,/\.json$/,/\/ueditor\//],
    }
  }
};
