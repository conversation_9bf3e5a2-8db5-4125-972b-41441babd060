<template>
    <div>
      <el-form  v-if="is_show_crm" :model="form_info">
        <el-form-item label="报备记录是否开启：">
          <el-radio v-model="form_info.is_open_report" :label="1">开启</el-radio>
          <el-radio v-model="form_info.is_open_report" :label="0">关闭</el-radio>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 147px">
            <div slot="content" style="max-width: 300px">
              开启后将显示报备客户入口
            </div>
            <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="报备渠道审核专员：">
          <el-select ref="report_uid" style="width: 300px" v-model="form_info.report_uid" multiple placeholder="请选择"
            @focus="showPersonnelAuthority('report_uid')" @change="PersonnelChange">
            <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
            </el-option>
          </el-select>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
            <div slot="content" style="max-width: 300px">
              进入报备记录列表，可上传报备回执
            </div>
            <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="渠道专员权限控制：">
          <el-radio v-model="form_info.report_status" :label="1">开启</el-radio>
          <el-radio v-model="form_info.report_status" :label="0">关闭</el-radio>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 147px">
            <div slot="content" style="max-width: 300px">
                开启后渠道专员仅可查看报备客户/上传回执；
                <br>
                关闭权限控制同普通成员帐号一致；
            </div>
            <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onClickForm">确认</el-button>
        </el-form-item>
      </el-form>
      <myEmpty v-else desc="当前用户不可查看"></myEmpty>
      <new_tips_list
        style="margin-top: 80px; width: 610px"
        :tipsList="tips_list"
      ></new_tips_list>
      <el-dialog :visible.sync="show_add_member" width="400px" :title="department_title" append-to-body>
        <div class="member" ref="memberList">
          <div v-if="identification == 'potential_client_operation_uid'"
          style="margin-bottom:20px"> <el-button  type="primary" size="small" @click="Select_All">全选</el-button></div>
          <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
            :defaultExpandAll="false">
          </multipleTree>
          <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
            <el-button type="text" @click="show_add_member = false">取消</el-button>
            <el-button type="primary" @click="selectMemberOk">确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  // import mySelect from "./my_select";
  import myEmpty from "@/components/components/my_empty.vue";
  import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
  import new_tips_list from "@/components/components/new_tips_list";
  export default {
    components: {
      // mySelect,
      myEmpty,
      multipleTree,
      new_tips_list
    },
    data() {
      return {
        form_info: {
          report_uid:"",//报备渠道审核专员
          is_open_report:0,//报备记录是否开启
        },
        see_tel_data:[4],
        params: {
          page: 1,
          per_page: 100,
          type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
        },
        user_list: [],
        is_show_crm: false,
        show_add_member: false, // 部门成员模态框
        selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
        identification: "",
        department_title: "",
        datalist: [], // 全部部门成员列表
        serverData: [], // 部门成员数据
        website_id: '',
        AllDepartment: [],
        AllDepartmentA: [],
        tips_list: ["渠道外联适用于团队成员通过系统提交报备信息，由渠道专员 （外联）统一负责对接报备确认回执。（本功能为扩展应用插件，非必要可忽略）"],
      };
    },
    mounted() {
      this.website_id = this.$route.query.website_id
      // if(this.website_id==109||this.website_id ==176){
      //   this.Claimeddata.splice(2, 1,  { id:4,name:"角色范围" },);
      // }
      this.getSiteCrmSetting();
      this.getMemberList();
    //   this.getDepartmentList()
    },
    methods: {
      // 获取批量导入的crm站点设置
      getSiteCrmSetting() {
        this.is_show_crm = true;
        // this.getSetting();
        this.getManagerAuthList();
        // this.$http.getAuthShow("config_approver_uid").then((res) => {
        //   if (res.status === 200) {
        //     if (res.data.indexOf(id) != -1) {
        //       this.is_show_crm = true;
  
        //     }
        //   }
        // });
      },
      getManagerAuthList() {
        this.$http.getManagerAuthList({ params: this.params }).then((res) => {
          if (res.status === 200) {
            this.user_list = res.data.data;
          }
        });
      },
      getSetting() {
        this.$http.getSiteCrmSetting().then((res) => {
          if (res.status === 200) {
            this.form_info = res.data;
            this.form_info.report_uid = res.data.report_uid
              ? this.setArr(res.data.report_uid)
              : "";
          }
        });
      },
      // 处理简单数据
      setArrMini(arr) {
        let n_arr = arr.split(",");
        let n_arr_2 = n_arr.map((item) => {
          return parseInt(item);
        });
        return n_arr_2;
      },
      // 处理部门成员数据
      setArr(arr) {
        let n_arr = arr.split(",");
        let n_arr_2 = n_arr.map((item) => {
          return parseInt(item);
        });
        // ====
        let i = 0;
        if (n_arr_2 != [] && n_arr_2 != undefined) {
          n_arr_2.map((item) => {
            this.$nextTick(() => {
              this.datalist.map((list) => {
                if (item != list.id) {
                  i++;
                  if (i == this.datalist.length) {
                    n_arr_2.splice(n_arr_2.indexOf(item), 1);
                    // console.log(n_arr_2,"观察");
                  }
                }
              })
              i = 0;
            })
          })
        }
        // ====
        return n_arr_2;
      },
      onClickForm() {
        let result = [];
        if (this.form_info.report_uid != [] && this.form_info.report_uid != '') {
          this.form_info.report_uid.map((item) => {
            if (item.toString().length >= 6) {
              result.push(parseInt(item.toString().slice(0, 3)));
            } else {
              result.push(item);
            }
          })
        }
        this.form_info.report_uid = Array.from(new Set(result));
        this.form_info.report_uid = this.form_info.report_uid
          ? this.form_info.report_uid.join(",")
          : "";
       
        this.$http.setSiteCrmSetting(this.form_info).then((res) => {
          if (res.status === 200) {
            this.form_info.report_uid = this.form_info.report_uid
              ? this.setArr(this.form_info.report_uid)
              : "";
            this.$message.success("操作成功");
            // this.$router.push("/crm_customer_business");
            this.getSetting();
          } else {
            this.form_info.report_uid = this.form_info.report_uid
              ? this.setArr(this.form_info.report_uid)
              : "";
          }
        });
      },
    //   // 获取部门
    //   getDepartmentList() {
    //     this.$http.getCrmDepartmentList().then((res) => {
    //       if (res.status == 200) {
    //         this.AllDepartment = res.data;
    //         this.AllDepartmentA = res.data;
    //         this.AllDepartmentA.map((item) => {
    //           if(item.subs && item.subs.length) {
    //               item.disabled = true;
    //           } else {
    //               item.disabled = false;
    //           }
    //           return item;
    //         })
    //       }
    //     });
    //   },
      // 获取部门成员列表
      async getMemberList() {
        await this.$http.getDepartmentMemberList().then((res) => {
          if (res.status == 200) {
            this.serverData = JSON.parse(JSON.stringify(res.data))
            this.serverData.push({
              id: 999,
              name: "未分配部门成员",
              order: 100000000,
              pid: 0,
              subs: this.serverData[0].user
            })
            this.recursionData(this.serverData);
            // 当键值key重复就更新key
            for (let i = 0; i < this.datalist.length; i++) {
              for (let j = i + 1; j < this.datalist.length; j++) {
                if (this.datalist[i].id == this.datalist[j].id) {
                  this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
                }
              }
            }
          }
        })
        this.getSetting();
      },
      // 递归数据处理
      recursionData(data) {
        // console.log(data,"内容");
        // console.log(this.datalist,"全部人员");
        for (let key in data) {
          if (typeof data[key].subs == "object") {
            data[key].subs.map((item) => {
              if (item.user) {
                item.subs = item.user;
                item.subs.map((list) => {
                  list.Parent = item.id;
                })
              }
              if (item.user_name) {
                item.name = item.user_name;
                this.datalist.push(item)
              }
            })
            this.recursionData(data[key].subs);
          }
        }
      },
      // 全选
      Select_All(){
        console.log(this.datalist);
        let selectall_id = this.datalist
        console.log(selectall_id);
        let all_id = selectall_id.map(item=>{
          return item.id
        })
        this.selectedIds = all_id
      },
      // 选中变化时触发
      selecetedMember(e) {
        this.selectedIds = e.checkedKeys;
        // this.selectedList = e.checkedNodes;
      },
      selectMemberOk() {
        this.show_add_member = false;
         if (this.identification == 'report_uid') {
          this.form_info.report_uid = this.selectedIds;
        } 
      },
      showPersonnelAuthority(val) {
        this.identification = val
      if (this.identification == 'report_uid' && this.form_info.report_uid != '') {
          this.selectedIds = this.form_info.report_uid;
          this.department_title = '报备渠道审核专员';
        } else {
          this.selectedIds = [];
        }
        this.$nextTick(() => {
          this.$nextTick(() => {
            if(this.$refs.report_uid){
              this.$refs.report_uid.blur();
            }
          });
        });
        this.show_add_member = true;
      },
      PersonnelChange(val) {
        this.selectedIds = val;
      },
    },
  };
  </script>
  
  <style  lang="scss" scoped>
  .tail{
    color: #2e3c4e;
    margin-left: 10px;
  }
  </style>
  