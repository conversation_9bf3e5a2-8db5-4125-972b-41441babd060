<template>
<el-dialog :title="title" :visible.sync="show" width="786px">
    <el-form label-width="120px" style="padding-right: 40px">
        <el-alert title="默认公式：个人业绩 = (应收佣金总和 - 扣除款项总和) x 佣金点位。（如不满足业务需求请选择自定义输入）" type="warning"  :closable="false"/>
        <br>

        <div class="deal-users">
            <span class="info">
                录入人：<span class="username" v-if="dealUsers.createUser && dealUsers.createUser.id" @click="setAdminId(dealUsers.createUser.id)">{{dealUsers.createUser.user_name}}</span> 
                <template v-else>--</template>
            </span>
            <span class="info">
                维护人：<span class="username" v-if="dealUsers.followUser && dealUsers.followUser.id" @click="setAdminId(dealUsers.followUser.id)">{{dealUsers.followUser.user_name}}</span> 
                <template v-else>--</template>
            </span>
            <span class="info">
                带看人：<span class="username" v-if="dealUsers.takeUser && dealUsers.takeUser.id" @click="setAdminId(dealUsers.takeUser.id)">{{dealUsers.takeUser.user_name}}</span> 
                <template v-else>--</template>
            </span>
            <span class="info">
                成交人：<span class="username" v-if="dealUsers.dealUser && dealUsers.dealUser.id" @click="setAdminId(dealUsers.dealUser.id)">{{dealUsers.dealUser.user_name}}</span> 
                <template v-else>--</template>
            </span>
        </div>

        <el-form-item label="选择成员:">
            <tMemberSelect v-model="params.admin_id" placeholder="选择成员" ref="tMemberSelect"/>
        </el-form-item> 
        
        <el-form-item label="佣金金额:">
            {{params.amount}}<span> * </span>
            <el-input v-model="params.proportion" placeholder="员工占比" style="width: 120px;" @input="handleProportionInput"><i slot="suffix">%</i></el-input>
            <span> = </span>
            <el-input v-model="params.commission" placeholder="佣金金额" style="width: 120px;" @input="handleCommissionInput"><i slot="suffix">元</i></el-input>
        </el-form-item>

        <el-form-item label="描述:">
            <el-input v-model="params.descp" placeholder="请输入描述" type="textarea"></el-input>
        </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>  

<script>
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect.vue'
export default {
    name: 'addCrmDealMemberCommission',
    components: {
        tMemberSelect
    },
    props: {
        reportId: {type: Number, default: 0},
    },
    data(){
        return {
            isInited: false,
            show: false,        //dialog是否显示
            successFn: null, 
            isAdd: true,
            submiting: false,
            params: {},
            dealUsers: {}
        }
    },
    computed: {
        title(){
            return this.isAdd?'新增业绩提成':'编辑业绩提成';
        },
    },
    created(){
        this.getCrmDealClientInfo();
    },
    methods: {
        handleCommissionInput(val){
            let proportion = val / this.params.amount * 100;
            this.params.proportion = isNaN(proportion) ? '' : proportion.toFixed(2)*1;
        },
        handleProportionInput(val){
            let commission = this.params.amount * val  / 100;
            this.params.commission = isNaN(commission) ? '' : commission.toFixed(2)*1;
        },
        //获取当前成交人员信息
        getCrmDealClientInfo(){
            this.$http.crmDealClientInfo(this.reportId).then(res=>{
                if(res.status == 200){
                    this.dealUsers = res.data;
                }
            })
        },
        //设置成员id
        setAdminId(admin_id){
            const options = this.$refs.tMemberSelect.getOptionData()                 
            if(options.find(item => item.value == admin_id)){
                this.params.admin_id = admin_id;
            }
        },
        open(params){
            this.params = {
                id: 0,
                report_id: 0,
                admin_id: '',
                amount: 0,
                proportion: '',
                commission: '',
                descp: ''
            };
            for(const key in params){
                if(this.params[key] !== undefined){
                    this.params[key] = params[key];
                }
            }

            this.isAdd = params.id ? false : true;
            this.show = true;
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            const params = {
                report_id: this.params.report_id,
                admin_id: this.params.admin_id,
                commission: this.params.commission,
                descp: this.params.descp
            };
            if(!this.isAdd){
                params.id = this.params.id;
            }
            this.submiting = true;
            const res = await this.$http[this.isAdd?'addMemberCommissionAPI':'editMemberCommissionAPI'](params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }
}
</script>
<style lang="scss" scoped>  
.deal-users{
    padding: 0 12px 20px 49px;
    .info{
        padding-right: 20px;
        .username{
            cursor: pointer;
            color: #409EFF;
        }
    }
}
</style>