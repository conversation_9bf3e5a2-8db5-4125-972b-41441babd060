<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->
    <el-form label-width="50px" :model="form" label-position="left">
      <el-form-item label="类型">
        <div class="title">{{ this.type_name }}</div>
      </el-form-item>
      <el-form-item class="ueditor" label="内容">
        <UE
          :value="ueditor.value"
          @input="inputUe"
          :config="ueditor.config"
          ref="ue"
        ></UE>
      </el-form-item>
      <el-form-item size="large">
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
import UE from "@/components/ueditor";
export default {
  name: "spa_detail",
  components: { UE },
  data() {
    return {
      spa_type: "",
      type_name: "",
      form: { content: "" },
      ueditor: {
        value: "",
        config: {
          initialFrameWidth: "100%",
        },
      },
      ue: "ue",
    };
  },
  created(){
    console.log(1111);
 this.getData()
  },
  mounted() {
    this.spa_type = parseInt(this.$route.query.type);
    switch (this.spa_type) {
      case 1:
        this.type_name = "关于我们";
        break;
      case 2:
        this.type_name = "联系我们";
        break;
      default:
        break;
    }
    this.getData();
  },
  methods: {
    getData() {
      if (this.spa_type === 1) {
        this.$http.getAboutUs().then((res) => {
          if (res.status === 200) {
            this.form.content = res.data.content;
            this.ueditor.value = this.form.content;
          }
        });
      }
      if (this.spa_type === 2) {
        this.$http.getContactUs().then((res) => {
          if (res.status === 200) {
            this.form.content = res.data.content;
            this.ueditor.value = this.form.content;
          }
        });
      }
    },
    onSubmit() {
      // 1  关于我们
      if (this.spa_type === 1) {
        this.$http.putAboutUs(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "上传成功",
              type: "success",
            });
            this.getData()
            this.$goPath("/web_overview");
          }
        });
      }
      // 联系我们
      if (this.spa_type === 2) {
        this.$http.putContactUs(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "上传成功",
              type: "success",
            });
            this.getData()
            this.$goPath("/web_overview");
          }
        });
      }
    },
    inputUe(obj) {
      this.form.content = obj.content;
    },
    goBack() {
      this.$router.back();
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  color: #999;
}
.ueditor {
  margin: 20px 0;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-form {
// 	padding-top: 30px;
// }
.el-input {
  width: 300px;
}
.el-textarea {
  width: 300px;
}
</style>
