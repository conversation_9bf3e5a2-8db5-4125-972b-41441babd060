<template>  
      <!-- 线索推送 -->
      <div class="pages">
            <div class="content-box-crm">
              <div class="tabhead">
                <div class="tabtitle" 
                v-for="item,index in approval_type" :key="index"
                :class="{ tabisactive: item.id == tab_id }"
                @click="tabId(item)">
                {{item.name}}
              </div>
              </div>
              <TikTokA v-if="tab_id==1"  @sendValue="setValueFromChild1"></TikTokA> 
              <anchorlist v-else :user_list="valueFromChild1"></anchorlist>
            </div>
           
      </div>
    </template>
<script>
import TikTokA from "./cus_TikTok.vue";
import anchorlist from "./cus_TikTok/anchorlist.vue"
    export default {
      components: {
        TikTokA,
        anchorlist
      },
      data() {
        return {
          approval_type:[
            {id:1,name:"推送规则"},
            {id:2,name:"主播账号"}
          ],
          tab_id:1,
          valueFromChild1:[],
        };
      },
      created(){
        // 赋值website_id
          if (this.$route.query.website_id) {
            this.website_id = this.$route.query.website_id;
      }
      },
      mounted() {
    
      },
      
      methods: {
        tabId(item){
            this.tab_id = item.id
            // this.$refs.anchorlist.open()
        },
        setValueFromChild1(value) {
          this.valueFromChild1 = value;
        }
      },
    };
</script>
      
<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .content-box-crm{
    margin-bottom: 50px;
        .tabhead{
          color: #8a929f;
          display: flex;
          .tabtitle{
            // width: 90px;
            margin-right: 50px;
            margin-bottom: 20px;
            text-align: center;
            cursor: pointer;
            &.tabisactive {
            color: #2d84fb;
            &::after {
              // position: absolute;
              left: 4%;
              margin: 0 auto;
              // transform: translateX(-50%);
              content: "";
              height: 3px;
              background: #2d84fb;
              width: 51px;
              display: block;
              margin-top: 4px;
            }
          }
          }
        }
        .labels{
          display: flex;
          .label-box{
            flex: 1;
          }
          .op-group{
            .op-btn {
              font-size: 14px;
              cursor: pointer;
              color: #8a929f;
              &:focus{
                border: none;
                outline: none;
              }
            }
          }
        }
  }
}
    </style>
      