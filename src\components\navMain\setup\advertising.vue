<template>
  <div class="">
    <el-container>
      <!-- <el-header> -->
        <!-- <div class="btn-box">
          <router-link
            v-for="item in router_list"
            :key="item.id"
            tag="el-button"
            size="mini"
            :to="item.path"
            >{{ item.name }}</router-link
          >
        </div> -->
      <!-- </el-header> -->
      <router-view></router-view>
    </el-container>
  </div>
</template>

<script>
export default {
  data() {
    return {
      router_list: [
        { path: "/advertising_list", name: "广告列表" },
        {
          path: "/add_advertising",
          name: "添加广告",
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss">
.title {
  padding-bottom: 10px;
  border-bottom: 1px solid #999;
}
</style>
