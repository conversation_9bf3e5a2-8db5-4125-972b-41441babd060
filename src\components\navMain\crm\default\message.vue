<template>
  <div>
    <el-table :data="message_form">
      <el-table-column label="名称">
        <template slot-scope="scope">
          {{ scope.row.name === "ALI_SMS" ? "短信" : "微信通知" }}
        </template>
      </el-table-column>
      <el-table-column label="开关">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.value"
            active-value="1"
            inactive-value="0"
            @change="onChangeSwitch(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      message_form: [],
    };
  },
  mounted() {
    this.getMsgSwitchData();
  },
  methods: {
    getMsgSwitchData() {
      this.$http.getMshSwitchData().then((res) => {
        if (res.status === 200) {
          this.message_form = res.data;
        }
      });
    },
    // 微信/短信通知开关
    onChangeSwitch(e) {
      let form = {
        id: e.id,
        value: e.value,
      };
      this.$http.setMsgSwitchData(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
        }
      });
    },
  },
};
</script>

<style></style>
