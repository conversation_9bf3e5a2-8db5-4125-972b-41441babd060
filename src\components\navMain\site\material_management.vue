<template>
  <el-container>
    <el-header class="div row" style="justify-content: space-between">
      <!-- 标题 -->
      <div class="div row">
        <div class="title">素材管理</div>
        <div class="title_number">
          <div>
            当前页面共（<i>{{ tableData.length }}</i
            >）条数据
          </div>
        </div>
      </div>
    </el-header>
    <div class="upimg">
      <el-button type="primary" @click="uploadimg">上传素材</el-button>
      <el-button type="success" @click="uploadimgtemporary"
        >上传临时素材</el-button
      >
    </div>
    <myTable
      v-loading="is_table_loading"
      :tableList="tableData"
      :header="table_header"
    ></myTable>
    <div class="pagination-box">
      <myPagination
        :total="params.total"
        :currentPage="params.page"
        :pagesize="params.per_page"
        @handleCurrentChange="handleCurrentChange"
        @handleSizeChange="handleSizeChange"
      ></myPagination>
    </div>
    <el-dialog title="上传素材" :visible.sync="dialogCreate">
      <el-form :model="upimg" label-width="120px">
        <el-form-item label="素材类型：">
          <el-radio-group v-model="radio" @change="onChangemedia">
            <el-radio :label="1">图片素材</el-radio>
            <el-radio :label="2">音频素材</el-radio>
            <el-radio :label="3">视频素材</el-radio>
            <el-radio :label="4">文件素材</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上传素材：">
          <el-upload
            :limit="1"
            class="upload-demo"
            :headers="myHeader"
            :action="user_avatar"
            :on-success="handleSuccessAvatar"
            :on-remove="handleRemoveAvatar"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="素材标题：">
          <el-input
            maxlength="20"
            v-model="upimg.title"
            placeholder="请填写素材标题"
          ></el-input>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="onCreate"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
    <el-dialog title="上传临时素材" :visible.sync="dialogCreatetemporary">
      <el-form :model="upimgtemporary" label-width="120px">
        <el-form-item label="临时素材类型：">
          <el-radio-group v-model="radiolinshi" @change="onChangemedialinshi">
            <el-radio :label="1">图片素材</el-radio>
            <el-radio :label="2">音频素材</el-radio>
            <el-radio :label="3">视频素材</el-radio>
            <el-radio :label="4">文件素材</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上传临时素材：">
          <el-upload
            :limit="1"
            class="upload-demo"
            :headers="myHeader"
            :action="user_avatar"
            :on-success="handleSuccessAvatarTemporary"
            :on-remove="handleRemoveAvatarTemporary"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="临时素材标题：">
          <el-input
            maxlength="20"
            v-model="upimgtemporary.title"
            placeholder="请填写素材标题"
          ></el-input>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="onCreatetemporary"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import config from "@/utils/config";
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "material_management",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      radio: 1,
      radiolinshi: 1,
      upimg: {
        title: "",
        url: "",
        type: "",
      },
      upimgtemporary: {
        title: "",
        url: "",
        type: "",
      },
      ImageVisible: false,
      tableData: [],
      table_header: [
        { prop: "id", label: "ID" },
        // { prop: "mediaid", label: "素材ID" },
        {
          prop: "url",
          label: "素材URL",
          render: (h, data) => {
            if (data.row.url) {
              return (
                <el-link href={data.row.url} type="primary">
                  {data.row.url}
                </el-link>
              );
            }
          },
        },
        { prop: "title", label: "素材标题" },
        {
          prop: "type",
          label: "素材类型",
          render: (h, data) => {
            if (data.row.type == 0) {
              return (
                <el-tag type="danger" effect="dark">
                  未知
                </el-tag>
              );
            } else if (data.row.type == 1) {
              return <el-tag effect="dark">图片</el-tag>;
            } else if (data.row.type == 2) {
              return (
                <el-tag type="success" effect="dark">
                  音频
                </el-tag>
              );
            } else if (data.row.type == 3) {
              return (
                <el-tag type="warning" effect="dark">
                  视频
                </el-tag>
              );
            } else if (data.row.type == 4) {
              return (
                <el-tag type="info" effect="dark">
                  文件
                </el-tag>
              );
            }
          },
        },
        {
          prop: "status",
          label: "有效状态",
          render: (h, data) => {
            if (data.row.status == 0) {
              return <el-tag>临时有效</el-tag>;
            } else if (data.row.status == 1) {
              return <el-tag type="danger">已过期</el-tag>;
            } else if (data.row.status == 2) {
              return <el-tag type="success">永久有效</el-tag>;
            } else {
              return <el-tag type="warning">暂无状态</el-tag>;
            }
          },
        },
        { prop: "created_at", label: "上传时间" },
      ],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      dialogCreate: false,
      dialogCreatetemporary: false,
      is_table_loading: true,
      inputVal: "",
      user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      imageUrl: "",
    };
  },
  computed: {
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  watch: {},
  methods: {
    onChangemedia(e) {
      var category = config.CATEGORY_IM_IMAGE;
      var materialType = "image";
      switch (e) {
        case 1:
          category = config.CATEGORY_IM_IMAGE;
          materialType = "image";
          break;
        case 2:
          category = config.CATEGORY_IM_VOICE;
          materialType = "voice";
          break;
        case 3:
          category = config.CATEGORY_IM_VIDEO;
          materialType = "video";
          break;
        case 4:
          category = config.CATEGORY_IM_FILE;
          materialType = "file";
          break;
        default:
          break;
      }
      this.user_avatar = `/api/common/file/upload/admin?category=${category}`;
      this.upimg.type = materialType;
    },
    onChangemedialinshi(e) {
      var category = config.CATEGORY_IM_IMAGE;
      var materialType = "image";
      switch (e) {
        case 1:
          category = config.CATEGORY_IM_IMAGE;
          materialType = "image";
          break;
        case 2:
          category = config.CATEGORY_IM_VOICE;
          materialType = "voice";
          break;
        case 3:
          category = config.CATEGORY_IM_VIDEO;
          materialType = "video";
          break;
        case 4:
          category = config.CATEGORY_IM_FILE;
          materialType = "file";
          break;
        default:
          break;
      }
      this.user_avatar = `/api/common/file/upload/admin?category=${category}`;
      this.upimgtemporary.type = materialType;
    },
    getMaterialList() {
      this.is_table_loading = true;
      this.$http.getMaterialList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          console.log(res.data.data);
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
      this.getMaterialList();
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
    },
    uploadimg() {
      this.dialogCreate = true;
    },
    uploadimgtemporary() {
      this.dialogCreatetemporary = true;
    },
    handleSuccessAvatarTemporary(response) {
      this.upimgtemporary.url = response.url;
    },
    handleSuccessAvatar(response) {
      this.upimg.url = response.url;
    },
    handleRemoveAvatarTemporary(response) {
      this.upimgtemporary.url = response.url;
    },
    handleRemoveAvatar(response) {
      this.upimg.url = response.url;
    },
    onCreate() {
      if (!this.upimg.title) {
        this.$message.error("请输入素材标题");
        return;
      }
      let msg = this.$message.success("请稍后...");
      this.$http.uploadImg(this.upimg).then((res) => {
        if (res.status === 200) {
          this.$message.success("添加成功");
          this.getMaterialList();
          this.dialogCreate = false;
          this.upimg.url = "";
          this.upimg.title = "";
          this.radio = 1;
          msg.close();
        }
      });
    },
    onCreatetemporary() {
      if (!this.upimgtemporary.title) {
        this.$message.error("请输入临时素材标题");
        return;
      }
      let msg = this.$message.success("请稍后...");
      this.$http.upload(this.upimgtemporary).then((res) => {
        if (res.status === 200) {
          this.$message.success("添加成功");
          this.getMaterialList();
          this.dialogCreatetemporary = false;
          this.upimgtemporary.url = "";
          this.upimgtemporary.title = "";
          this.radiolinshi = 1;
          msg.close();
        }
      });
    },
  },
  created() {},
  mounted() {
    this.getMaterialList();
  },
};
</script>
<style lang="scss" scoped>
.upimg {
  margin-bottom: 30px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
.el-button {
  border-radius: 3px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 300px;
}
</style>
