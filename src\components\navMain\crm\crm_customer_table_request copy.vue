<template>
    <div>
        <div class="body-box">
        <div class="deatil-left">
            <div class="loadmor-box">
                <div class="div row loadmore" @click="onChangeCollapse">
                    <span class="text"> 申请单详情 </span>
                    <span :class="is_collapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
                </div>
            </div>
            <myCollapse :isActive="is_collapse" class=" apse-box">
                <template v-slot:content>
                    <el-descriptions :column="2" size="medium" border>
                        <el-descriptions-item :span="8">
                            <template slot="label">
                                申请人
                            </template>
                            <div class="proposer">
                                <div class="poser">1</div>
                                <div class="poser">2</div>
                                <div class="poser">3</div>
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions :column="2" size="medium" border>
                        <el-descriptions-item>
                            <template slot="label">
                                收款人
                            </template>
                            --
                        </el-descriptions-item>
                        <el-descriptions-item>
                            <template slot="label">
                                收款人卡号
                            </template>
                            --
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions :column="2" size="medium" border>
                        <el-descriptions-item>
                            <template slot="label">
                                备注
                            </template>
                            --
                        </el-descriptions-item>
                    </el-descriptions>
                </template>
            </myCollapse>
            <div class="loadmor-box">
                <div class="div row loadmore" @click="onChangeCollapsetwo">
                    <span class="text"> 申请单详情 </span>
                    <span :class="is_collapsetwo ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
                </div>
            </div>
            <myCollapse :isActive="is_collapsetwo" class="coll-box apse-box">
                <template v-slot:content>
                    <el-form :inline="true" :model="formInline" class="demo-form-inline">
                        <el-form-item label="">
                            <el-select v-model="formInline.region" placeholder="入职日期">
                                <el-option label="区域一" value="shanghai"></el-option>
                                <el-option label="区域二" value="beijing"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="">
                            <el-select v-model="formInline.region" placeholder="支出部门">
                                <el-option label="区域一" value="shanghai"></el-option>
                                <el-option label="区域二" value="beijing"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="">
                            <el-select v-model="formInline.region" placeholder="成交支出">
                                <el-option label="区域一" value="shanghai"></el-option>
                                <el-option label="区域二" value="beijing"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="">
                            <el-select v-model="formInline.region" placeholder="（税后）">
                                <el-option label="区域一" value="shanghai"></el-option>
                                <el-option label="区域二" value="beijing"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="addposer">添加</el-button>
                        </el-form-item>
                    </el-form>
                    <el-table :data="tableData" class="house_table" border :header-cell-style="{ background: '#fff' }"
                        highlight-current-row :row-style="$TableRowStyle">
                        <el-table-column label="入职日期" v-slot="{ row }">
                            {{ row.client.tel ? row.client.tel : "--" }}
                        </el-table-column>
                        <el-table-column label="支出部门" v-slot="{ row }">
                            {{ row.client.tel ? row.client.tel : "--" }}
                        </el-table-column>
                        <el-table-column label="报销项目" v-slot="{ row }">
                            {{ row.client.tel ? row.client.tel : "--" }}
                        </el-table-column>
                        <el-table-column label="报销金额" v-slot="{ row }">
                            {{ row.client.tel ? row.client.tel : "--" }}
                        </el-table-column>
                        <el-table-column label="操作" fixed="right" v-slot="{ row }">
                            <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                        </el-table-column>
                    </el-table>
                </template>
            </myCollapse>
            <div class="loadmor-box">
                <div class="div row loadmore1">
                    <span class="text"> 申请单详情 </span>
                </div>
            </div>
            <div class="upload-deatil">
                <el-upload action="#" list-type="picture-card" :auto-upload="false" style="margin: 5px 0px 0px 5px;">
                    <i slot="default" class="el-icon-plus"></i>
                    <div slot="file" slot-scope="{file}">
                        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
                        <span class="el-upload-list__item-actions">
                            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                <i class="el-icon-zoom-in"></i>
                            </span>
                            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
                                <i class="el-icon-download"></i>
                            </span>
                            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                <i class="el-icon-delete"></i>
                            </span>
                        </span>
                    </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible">
                    <img width="100%" :src="dialogImageUrl" alt="">
                </el-dialog>
            </div>
        </div>
        <div class="deatil-right">
            <div class="deatil-right-top">
                <div class="title-box">审核列表</div>
            </div>
            <div class="deatil-right-mix"></div>
            <div class="deatil-right-bottom">
                <div class="flow-text">流程</div>
                <el-input v-model="input" placeholder="请输入内容" style="width: 575px;height: 50px;"></el-input>
            </div>
            <div class="deatil-right-text">提示：找不到审核人，将自动通过</div>
        </div>
    </div>
    <div class="deatil-bottom">
        <el-button type="primary" style="margin:1% 0% 0% 95%;" @click="deatilmit">提交</el-button>
    </div>
    </div>
</template>
<script>
import myCollapse from "./components/collapse";
export default {
    name: "crm_customer_table_request",
    components: {
        myCollapse,
    },
    data() {
        return {
            input: '',
            dialogImageUrl: '',
            dialogVisible: false,
            disabled: false,
            tableData: [],
            formInline: {
                user: '',
                region: ''
            },
            is_collapse: true,
            is_collapsetwo: true,
        }
    },
    mounted() {

    },
    methods: {
        handleRemove(file) {
            console.log(file);
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        handleDownload(file) {
            console.log(file);
        },
        addposer() {
            console.log("添加了")
        },
        // 折叠面板
        onChangeCollapse() {
            this.is_collapse = !this.is_collapse;
        },
        onChangeCollapsetwo() {
            this.is_collapsetwo = !this.is_collapsetwo;
        },
        //提交
        deatilmit() {
            console.log("提交了")
        }
    }
}
</script>
<style lang="scss" scoped>
/deep/.el-descriptions--medium.is-bordered .el-descriptions-item__cell {
    padding: 10px;
    width: 224px;
    height: 50px;
    text-align: center;
}
// /deep/.el-input__inner {
//     height: 50px;
// }
/deep/.el-input--suffix .el-input__inner {
    padding-right: 10px;
}

.loadmor-box {
    width: 896px;
    height: 50px;
    background-color: #DDE1E9;
    padding-top: 10px;
}

.coll-box {
    padding: 10px;
    border: 1px solid #DDE1E9;
}

.apse-box {
    margin-bottom: 12px;
}

.upload-deatil {
    width: 894px;
    height: 162px;
    border: 1px solid #DDE1E9;
}

.proposer {
    height: 100%;
    width: 100%;
    // background-color: red;
    display: flex;
    justify-content: space-between;
    line-height: 200%;

    .poser {
        width: 30%;
        text-align: center;
    }
}

.body-box {
    display: flex;
    justify-content: space-between;

    .deatil-left {
        // background-color: blue;
        width: 54%;
        min-height: 700px;
    }

    .deatil-right {
        // background-color: red;
        width: 44%;
        height: 720 px;
        border: 1px solid #DDE1E9;
        .deatil-right-top{
            width: 100%;
            height: 40px;
            background-color: #DDE1E9;
            border-bottom: 1px solid #DDE1E9;
            .title-box{
                width: 156px;
                height: 40px;
                text-align: center;
                line-height: 40px;
                background-color: #fff;
            }
        }
        .deatil-right-mix{
            width: 100%;
            height: 581px;
            // background-color: red;
        }
        .deatil-right-bottom {
            width: 100%;
            height: 40px;
            background-color: #DDE1E9;
            display: flex;
            justify-content: space-between;
            .flow-text{
                width: 156px;
                height: 50px;
                text-align: center;
                line-height: 40px;
            }
        }
        .deatil-right-text {
            width: 100%;
            height: 50px;
            // background-color: #DDE1E9;
            color: red;
            text-align: center;
            line-height: 50px;
        }
    }
}

.deatil-bottom {
    width: 100%;
    height: 60px;
    margin-top: 10px;
    // background-color: red;
}

.loadmore {
    border: none;
    width: 100%;
    text-align: end;
    line-height: 1;
    margin-top: 12px;
    color: #8a929f;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 10px;

    .text {
        font-size: 14px;
    }
}

.loadmore1 {
    border: none;
    width: 100%;
    text-align: end;
    line-height: 1;
    margin-top: 12px;
    color: #8a929f;
    justify-content: center;
    // cursor: pointer;
    font-size: 14px;
    margin-bottom: 10px;

    .text {
        font-size: 14px;
    }
}
</style>