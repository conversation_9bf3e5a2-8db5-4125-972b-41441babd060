<template>
  <!-- 消息存档 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="tabs-box div row">
          <div
            class="tabs-item"
            :class="{ isactive: item.desc === is_tabs }"
            v-for="item in tabs"
            :key="item.id"
            @click="onClickTabs(item)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="tips">
            <p class="text-tips">1.开通消息存档功能的付费标准请见：付费规则</p>
            <p class="text-tips">
              2.违规操作监控仅针对开启存档的员工对外部联系人和外部群的操作
            </p>
            <p class="text-tips">3.修改设置后立马生生效，历史数据不变</p>
          </div>
        </div>
        <div class="content-box-crm">
          <div :is="is_tabs" keep-alive></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import tab1 from "./components/message/tabs1";
import tab2 from "./components/message/tabs2";
export default {
  name: "crm_customer_message",
  components: {
    tab1,
    tab2,
  },
  data() {
    return {
      is_tabs: "tab1",
      tabs: [
        { id: 1, name: "违规提醒设置", desc: "tab1" },
        { id: 2, name: "敏感词设置", desc: "tab2" },
      ],
    };
  },
  methods: {
    onClickTabs(item) {
      this.is_tabs = item.desc;
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .tabs-box {
    align-items: center;
    margin-bottom: 24px;
    font-size: 18px;
    color: #8a929f;
    cursor: pointer;
    .tabs-item {
      margin-right: 24px;
      border-bottom: 6px solid #f1f4fa;
      padding-bottom: 5px;
      &.isactive {
        color: #2d84fb;
        border-bottom: 6px solid #2d84fb;
      }
    }
  }
}
.tips {
  background: #e7f3fd;
  padding: 24px;
  .text-tips {
    font-size: 14px;
    color: #8a929f;
  }
}
</style>
