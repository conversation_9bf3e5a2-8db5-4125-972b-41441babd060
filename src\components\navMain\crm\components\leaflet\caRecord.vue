<template>
  <div>
    <el-table :data="caRecordList" style="width: 100%" height="400">
      <el-table-column fixed prop="created_at" label="日期" width="200">
      </el-table-column>
      <el-table-column prop="mobile" label="客户手机号" width="150">
      </el-table-column>
      <el-table-column label="分享链路" width="150">
        <template slot-scope="scope">
          <el-tag :type="scope.row.admin_info ? 'success' : 'danger'">{{ scope.row.admin_info ? '内部分享' : '外部分享'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" width="150">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type == 1 ? 'success' : ''">{{ scope.row.type == 1 ? '表单填写' : '授权登录'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="share_user_name" label="分享人" width="150">
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  props: {
    id: {
      type: Number || String,
      default: 0
    }
  },
  data() {
    return {
      caRecordList: []
    }
  },
  mounted() {
    this.getCaRecordList()
  },
  methods: {
    async getCaRecordList() {
      const res = await this.$http.getCaRecord(this.id)
      if (res.status == 200) {
        this.caRecordList = res.data.data
      }
    }
  }
}
</script>
<style lang="scss"></style>