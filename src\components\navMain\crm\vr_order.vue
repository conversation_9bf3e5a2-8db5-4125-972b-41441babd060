<template>
  <div>
    <el-row>
      <el-col :span="24">
        <div class="weituo_a">
          <div class="weituo_key">
            <el-row>
              <el-col>
                <div class="title flex-row align-center">
                  <el-select
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                    v-model="params.status"
                    placeholder="请选择订单状态"
                  >
                    <el-option
                      v-for="item in statusList"
                      :key="item.values"
                      :label="item.name"
                      :value="item.values"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                    v-model="params.camera_id"
                    placeholder="请选择相机"
                  >
                    <el-option
                      v-for="item in cameraList"
                      :key="item.values"
                      :label="item.name"
                      :value="item.values"
                    >
                    </el-option>
                  </el-select>
                  <el-date-picker
                    v-model="params.b_date"
                    type="date"
                    placeholder="选择开始日期"
                    value-format="yyyy-MM-dd"
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                  >
                  </el-date-picker>
                  <el-date-picker
                    v-model="params.e_date"
                    type="date"
                    placeholder="选择结束日期"
                    value-format="yyyy-MM-dd"
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                  >
                  </el-date-picker>
                  <el-input
                    v-model="params.house_id"
                    placeholder="请输入房源id"
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                  ></el-input>
                  <el-input
                    v-model="params.hid"
                    placeholder="请输入项目编号"
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                  ></el-input>
                  <el-input
                    v-model="params.name"
                    placeholder="请输入项目名称"
                    style="
                      margin-right: 10px;
                      margin-bottom: 10px;
                      width: 200px;
                    "
                  ></el-input>
                  <el-button
                    type="primary"
                    style="margin-right: 10px; margin-bottom: 10px"
                    plain
                    @click="search"
                    >搜索</el-button
                  >
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <div class="title">
                  <el-table
                    v-loading="is_table_loading"
                    :data="orderData"
                    border
                    :header-cell-style="{ background: '#EBF0F7' }"
                    highlight-current-row
                    :row-style="$TableRowStyle"
                  >
                    <el-table-column
                      prop="name"
                      label="订单名称"
                    ></el-table-column>
                    <el-table-column
                      prop="status_name"
                      label="订单状态"
                      v-slot="{ row }"
                    >
                      <el-tag v-if="row.status <= 3" type="info">{{
                        row.status_name
                      }}</el-tag>
                      <el-tag v-else-if="row.status <= 5" type="warning">{{
                        row.status_name
                      }}</el-tag>
                      <el-tag v-else-if="row.status <= 6" type="success">{{
                        row.status_name
                      }}</el-tag>
                      <el-tag v-else-if="row.status <= 7" type="danger">{{
                        row.status_name
                      }}</el-tag>
                      <el-tag v-else>{{ row.status_name }}</el-tag>
                    </el-table-column>
                    <el-table-column label="相机信息" v-slot="{ row }">
                      <div>
                        {{ row.photographer ? row.photographer.name : "--" }}
                      </div>
                    </el-table-column>

                    <el-table-column
                      label="添加时间"
                      prop="ctime"
                    ></el-table-column>
                    <el-table-column label="操作" v-slot="{ row }" width="200">
                      <el-link
                        type="success"
                        @click="preview(row)"
                        v-if="row.status == 6"
                      >
                        预览</el-link
                      >
                      <!--  <el-popconfirm
                        title="确定删除吗？"
                        style="margin: 0 10px"
                        @onConfirm="del(row)"
                      >
                        <el-link
                          slot="reference"
                          type="danger"
                          icon="el-icon-delete"
                          >删除</el-link
                        >
                      </el-popconfirm>-->
                    </el-table-column>
                  </el-table>

                  <el-pagination
                    style="text-align: end; margin-top: 24px"
                    background
                    layout="prev, pager, next"
                    :total="total"
                    :page-size="params.per_page"
                    :current-page="params.page"
                    @current-change="onPageChange"
                  >
                  </el-pagination>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- <el-dialog width="500px" :visible.sync="is_showDia" :title="settingTitle">
      <div>
        <el-form label-width="120px">
          <el-form-item label="相机名称">
            <el-input v-model="setting_form.name"></el-input>
          </el-form-item>
          <el-form-item label="账户用户名">
            <el-input v-model="setting_form.username"></el-input>
          </el-form-item>
          <el-form-item label="登陆密码" v-if="!is_edit">
            <el-input
              type="password"
              v-model="setting_form.password"
            ></el-input>
          </el-form-item>
          <el-form-item label="部门">
            <el-radio v-model="depart_type" :label="0">全部部门</el-radio>
            <el-radio v-model="depart_type" :label="1">自定义部门</el-radio>
          </el-form-item>
          <el-form-item label="" v-if="depart_type == 1">
            <el-cascader
              placeholder="请选择部门"
              style="width: 155px"
              v-model="setting_form.department_id"
              clearable
              :show-all-levels="false"
              :options="department_list"
              :props="{
                value: 'id',
                label: 'name',
                multiple: true,
                children: 'subs',
                emitPath: false,
                checkStrictly: true,
              }"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button type="primary" @click="confirmSetting">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        status: '',   //订单状态
        camera_id: '', //相机id
        b_date: '',  //拍摄开始时间
        e_date: "",  //拍摄结束时间
        house_id: '', //房源id精确查找
        hid: '',      //众趣项目编号模糊查询
        name: "",     //小区名称模糊查询 = 众趣项目名称模糊查询
      },
      statusList: [],
      total: 0,
      cameraList: []

      // is_showDia: false,
      // settingTitle: '',
      // department_list: [],
      // depart_type: 0,
      // is_edit: false,
      // setting_form: {
      //   name: '',
      //   username: '',
      //   password: '',
      //   department_id: ''
      // }
    }
  },
  computed: {
    statusListComputed() {
      return this.statusList
    }
  },
  created() {
    this.getOrderList()
    this.getOrderStatusList()
    this.getCameraList()
  },
  methods: {
    getOrderStatusList() {
      this.$http.getOrderStatusList().then(res => {
        if (res.status == 200) {
          this.statusList = res.data
          this.statusList.unshift({
            name: '全部订单状态',
            value: ""
          })
        }

      }).catch(() => {

      })
    },
    getCameraList() {
      this.$http.getCameraList().then(res => {

        if (res.status == 200) {
          this.cameraList = res.data
          this.cameraList.unshift({
            name: '全部相机',
            value: ""
          })
        }

      }).catch(() => {

      })
    },
    search() {
      this.params.page = 1
      this.getOrderList()
    },

    del() { },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getOrderList()
    },
    preview(row) {
      window.open(row.vr_url, "_blank")
    },

    getOrderList() {
      this.is_table_loading = true
      this.$http.getOrderList(this.params).then(res => {

        if (res.status == 200) {
          this.orderData = res.data.data
          this.total = res.data.total
        }
        this.is_table_loading = false
      }).catch(() => {
        this.is_table_loading = false
      })
    },


  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}
.padd10 {
  padding: 10px 0 40px;
}
.weituo_a {
  padding-top: 20px;
  padding-left: 24px;

  .weituo_key {
    padding-bottom: 20px;
    border-bottom: 1px solid #f3f3f3;
    .title {
      padding-top: 20px;
    }
  }
}
</style>