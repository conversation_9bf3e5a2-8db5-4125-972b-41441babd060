<template>
  <div class="add">
    <div class="tips">
      <div>1.一个二维码可对应多名接待员工，客户扫码随机均匀分配</div>
      <div>
        2.添加客户后欢迎语仅能发送一次，请勿在企业后台设置，企业后台设置后，此处的活码欢迎将
        不会发送
      </div>
    </div>
    <el-form label-width="100px">
      <div class="title">基本信息</div>

      <el-form-item label="活码名称">
        <div class="form-item-block">
          <el-input
            placeholder="请输入活码名称"
            v-model="form_params.name"
            style="width: 240px; margin-right: 12px"
          >
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="企业客户标签">
        <div class="form-item-block align-center flex-wrap">
          <el-switch v-model="form_params.add_tag" active-color="#2D84FB">
          </el-switch>
        </div>
        <div class="tip">为扫码后添加员工好友的企微客户打上标签</div>
      </el-form-item>

      <el-form-item label="选择标签" v-if="form_params.add_tag">
        <div class="form-item-block">
          <el-cascader
            :options="tagList"
            v-model="form_params.tag_ids"
            style="width: 240px; margin-right: 12px"
            :show-all-levels="false"
            @change="changTags"
            :clearable="true"
            :props="{
              emitPath: false,
              value: 'id',
              label: 'name',
              children: 'taggroup',
              multiple: true,
            }"
          ></el-cascader>
        </div>
      </el-form-item>
      <el-form-item label="好友验证">
        <div class="form-item-block line_height1">
          <el-switch v-model="form_params.open_check" active-color="#2D84FB">
          </el-switch>
        </div>
        <div class="tip">
          开启后，客户添加员工好友时会提交好友验证，员工通过后添加成功
        </div>
      </el-form-item>

      <div class="title">欢迎语</div>
      <el-form-item label="欢迎语">
        <welcome-mes
          style="padding-right: 20px"
          ref="member_qrcode_edit"
          :value="welcome_mes"
        ></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>
import welcomeMes from "@/components/navMain/crm/components/welcome_mes"
import config from "@/utils/config";
export default {
  components: { welcomeMes },
  props: {
    form: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      form_params: {
        // open_check: true
      },
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      isSubmiting: false,
      type_arr: ["text", "image", "link", "miniprogram", "video", "file"],
      tagList: []
    };
  },

  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  created() {
    for (const key in this.form) {
      if (key == "open_check" || key == 'add_tag') {
        this[key] = this.form[key]
      } else {
        this.form_params[key] = this.form[key];
      }
    }
    this.getTags()
    if (this.open_check == 1) {
      this.$set(this.form_params, "open_check", true)
      // this.form_params.open_check = true;
    } else {
      this.$set(this.form_params, "open_check", false)
      // this.form_params.open_check = false;
    }
    if (this.add_tag == 1) {
      this.$set(this.form_params, "add_tag", true)
      // this.form_params.add_tag = true;
    } else {
      this.$set(this.form_params, "add_tag", false)
      // this.form_params.add_tag = false;
    }
    for (const key in this.form_params.welcome_msg) {
      this.$set(
        this.welcome_mes,
        key,
        JSON.parse(JSON.stringify(this.form_params.welcome_msg[key]))
      );
      // this.welcome_mes[key] = JSON.parse(JSON.stringify(this.form_params.welcome_msg))
    }
    if (this.form_params.welcome_msg.image) {

      delete this.welcome_mes.image.org_url;
    }
    if (this.form_params.welcome_msg.file) {
      this.filename = this.form_params.welcome_msg.file.org_url;
      delete this.welcome_mes.file.org_url;
    }
    if (this.form_params.welcome_msg.video) {
      delete this.form_params.welcome_msg.video.org_url;
      delete this.welcome_mes.video.org_url;
    }
    if (this.form_params.welcome_msg.miniprogram) {
      this.welcome_mes.miniprogram.pic_url = this.form_params.welcome_msg.miniprogram.org_url;
      delete this.form_params.welcome_msg.miniprogram.org_url
      delete this.welcome_mes.miniprogram.org_url;
    }
    if (this.form_params.welcome_msg.link) {
      delete this.welcome_mes.link.org_url;
    }
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      if (params.open_check) {
        params.open_check = 1;
      } else {
        params.open_check = 0;
      }
      if (params.add_tag) {
        params.add_tag = 1;
      } else {
        params.add_tag = 0;
      }
      params.tag_ids = params.tag_ids.join(",")
      let oldTypeArr = Object.keys(this.form_params.welcome_msg)
      let _welcome_mes = Object.assign({}, this.$refs.member_qrcode_edit.welcome_mes);
      for (const key in _welcome_mes) {
        if (_welcome_mes[key].org_url) {
          delete _welcome_mes[key].org_url
        }
      }
      if (
        _welcome_mes.image &&
        !_welcome_mes.image.media_id
      ) {
        if (oldTypeArr.includes("image")) {
          _welcome_mes.image = this.form_params.welcome_msg.image
          _welcome_mes.image.is_del = 1
          delete _welcome_mes.image.name
        } else {
          delete _welcome_mes.image;
        }

      }
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key] && Object.keys(linkArr).includes(key)) {
            emptyLink.push(key);
          }
        }

        if (emptyLink.length == Object.keys(linkArr).length) {
          if (oldTypeArr.includes("link")) {
            _welcome_mes.link = this.form_params.welcome_msg.link
            delete _welcome_mes.link.appid
            _welcome_mes.link.is_del = 1

          } else {
            emptyLink.length = 0;
            delete _welcome_mes.link;
          }

          // emptyLink.length = 0;
          // delete _welcome_mes.link;
        } else if (emptyLink.length) {
          if (!oldTypeArr.includes("link")) {
            this.$message.warning(linkArr[emptyLink[0]]);
            return;
          }

        }
      }

      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (
            !_welcome_mes.miniprogram[key] && Object.keys(miniArr).includes(key)
          ) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          if (oldTypeArr.includes("miniprogram")) {
            _welcome_mes.miniprogram = this.form_params.welcome_msg.miniprogram
            _welcome_mes.miniprogram.is_del = 1
          } else {
            emptyMini.length = 0;
            delete _welcome_mes.miniprogram;
          }

        } else if (emptyMini.length) {
          if (!oldTypeArr.includes("miniprogram")) {
            this.$message.warning(miniArr[emptyMini[0]]);
            return;
          }
        }
      }

      if (
        _welcome_mes.video &&
        !_welcome_mes.video.media_id
      ) {
        if (oldTypeArr.includes("video")) {
          _welcome_mes.video = this.form_params.welcome_msg.video
          _welcome_mes.video.is_del = 1
        } else {
          delete _welcome_mes.video;
        }

      }
      if (
        _welcome_mes.file &&
        !_welcome_mes.file.media_id
      ) {
        if (oldTypeArr.includes("file")) {
          _welcome_mes.file = this.form_params.welcome_msg.file
          _welcome_mes.file.is_del = 1
        } else {
          delete _welcome_mes.file;
        }
      }
      delete params.user

      params.welcome_msg = JSON.stringify(_welcome_mes);
      if (this.isSubmiting) return;
      this.isSubmiting = true;

      this.$http
        .editCrmMyQrcode(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
    showAddLabels() {
      this.show_add_labels = true;
    },
    showSelectDia() {
      this.img_params.page = 1;
      this.getImgList();
      this.show_select_dia = true;
    },
    onChangemediaType(e) {
      var category = config.CATEGORY_IM_IMAGE;
      switch (e) {
        case 2:
        case 3:
        case 4:
          category = config.CATEGORY_IM_IMAGE;
          break;
        case 5:
          category = config.CATEGORY_IM_VIDEO;
          break;
        case 6:
          category = config.CATEGORY_IM_FILE;
          break;
        default:
          break;
      }
      this.website_img = `/api/common/file/upload/admin?category=${category}`;
    },
    async getTags() {
      let res = await this.$http.getCrmGoupTags().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.tagList = res.data
      }
    },
    changTags(e) {
      console.log(e);
    }
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.imgurl {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
}
.tip {
  color: #8a929f;
  font-family: PingFang SC;
  font-weight: regular;
  font-size: 12px;
}
.add {
  max-height: 70vh;
  overflow-y: auto;
  .title {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 16px;
    margin: 20px 0;
    font-weight: 600;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;
    &.active {
      border: 5px solid #409eff;
    }
    .file_img {
      width: 115px;
      height: 115px;
      text-align: center;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;
  &.active {
    border: 1px solid #a6e22e;
  }
}
.upload-demo {
  margin-right: 5px;
}
</style>
