<template>
  <el-main>
    <el-select
      @change="onChange"
      v-model="params.status"
      placeholder="审核状态"
    >
      <el-option
        v-for="item in review_list"
        :key="item.value"
        :label="item.description"
        :value="item.value"
      ></el-option>
    </el-select>
    <el-button
      v-if="create_form.ids.length > 0"
      style="margin-left:10px"
      type="success"
      @click="onSuccess"
      >审核通过</el-button
    >
    <el-button @click="onRefuse" v-if="create_form.ids.length > 0" type="danger"
      >审核拒绝</el-button
    >
    <myTable
      select
      @selection-change="handleSelectionChange"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
  </el-main>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "comment_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      params: {
        status: "0",
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      tableData: [],
      review_list: [
        { value: "0", description: "审核中" },
        { value: "1", description: "已发布" },
        { value: "2", description: "未通过" },
      ],
      create_form: {
        ids: [],
        status: "",
      },
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "build_name", label: "楼盘名称" },
        { prop: "created_at", label: "发布时间" },
        { prop: "content", label: "内容" },
        {
          label: "发布者",
          render: (h, data) => {
            return (
              <div class="div row" style="align-items:centet">
                <img
                  src={data.row.u_avatar}
                  style="width:30px;height:30px;border-radius:50%"
                />
                <el-tag type="success" size="mini">
                  {data.row.u_name ||
                    data.row.u_nickname ||
                    data.row.u_user_name}
                </el-tag>
                {data.row.u_phone}
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.$http.getCommentReviewList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.row;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    onChange(e) {
      this.params.page = 1;
      this.params.status = e;
      this.getDataList();
    },
    handleSelectionChange(e) {
      this.create_form.ids = e.map((item) => {
        return item.id;
      });
    },
    onSuccess() {
      this.create_form.status = "1";
      this.$http.commentReview(this.create_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "已通过",
            type: "success",
          });
          this.getDataList();
        }
      });
    },
    onRefuse() {
      this.create_form.status = "2";
      this.$http.commentReview(this.create_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "已拒绝",
            type: "success",
          });
          this.getDataList();
        }
      });
    },
  },
};
</script>

<style></style>
