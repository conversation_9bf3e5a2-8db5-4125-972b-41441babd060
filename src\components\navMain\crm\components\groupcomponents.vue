<template>
    <div>
           <!-- 分组表格 -->
           <div>
              <el-table
                    :data="GoupData"
                    style="width: 80%;margin: 0 auto;"
                    highlight-current-row
                    @selection-change="selectionChange"
                    >
                    <el-table-column
                      type="selection"
                      width="100">
                    </el-table-column>
                    <el-table-column
                      prop="title"
                      label="分组名"
                      >
                    </el-table-column>
                    <!-- <el-table-column
                      label="操作"
                      >
                      <el-button size="mini" type="primary">选择</el-button>
                    </el-table-column> -->
              </el-table>
            </div>
            <div class="gouppaging">
              <el-pagination
                layout="prev, pager, next"
                :total="allgoupdata.total"
                :page-size="allgoupdata.per_page"
                :current-page="allgoupdata.current_page"
                @current-change="onPageChange">
              </el-pagination>
            </div>
            <div> 

            </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            GoupData:[],//分组数据
            allgoupdata:[],//加分页的分组数据
            cluegrouppage:{
              page:1,
              per_page:10
            },
            multipleSelection: [],
        }
    },
    mounted(){
        this.cluegrouping()
    },
    methods:{
        //获取组
        cluegrouping(){
            this.$http.cluegrouping(this.cluegrouppage.page,this.cluegrouppage.per_page).then((res)=>{
              if(res.status==200){
                console.log(res.data);
                this.allgoupdata = res.data
                this.allgoupdata.per_page = Number(res.per_page)
                this.GoupData = res.data.data
              }
            })
        },
        //分组表格分页
        onPageChange(e){
            this.cluegrouppage.page = e
            console.log(this.cluegrouppage);
            this.cluegrouping()
        },
        // 点击表格的checkbox触发
        selectionChange(e) {
          // 遍历数据，返回客户id
          let arr = e.map((item) => {
            return item;
          });
          this.multipleSelection = arr; // 赋值当前客户的id
          this.$emit('child-event', this.multipleSelection); // 触发名为child-event的事件，并传递数据data
        },
    },
}
</script>
<style scope lang="scss">
.gouppaging{
    width: 50%;
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>