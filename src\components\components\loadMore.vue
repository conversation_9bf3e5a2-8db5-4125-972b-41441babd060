<template>
  <div class="load">
    <span>{{ filterText(status) }}</span>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    status: {
      type: String,
      default: "loading", // loading, loadend, nomore
    },
    load_text: {
      type: Object,
      default: () => {
        return {
          loading: "加载中...",
          loadend: "",
          nomore: "— 已经到底了 —",
        };
      },
    },
  },
  data() {
    return {};
  },
  methods: {
    filterText(val) {
      if (val) {
        return this.load_text[val] || "";
      } else {
        return "";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.load {
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 26rpx;
  color: #999;
}
</style>
