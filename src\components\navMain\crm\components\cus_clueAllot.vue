<template>
  <div>
    <!-- 线索分配 -->
    <div class="header">
      <div class="clue_title">线索分配</div>
      <div class="flex-row">
        <!-- 手动认领 -->
        <div class="clue_manualAllot">
          <div>
            <el-radio v-model="totalConfig_params.push_type" :label="0"
              >手动认领</el-radio
            >
          </div>
          <div class="clue_prompt">通过公海客户手动认领获取客户线索</div>
        </div>
        <!-- 自动分配 -->
        <div class="clue_manualAllot" style="margin-left: 20px">
          <div>
            <el-radio v-model="totalConfig_params.push_type" :label="1"
              >自动分配</el-radio
            >
          </div>
          <div class="clue_prompt">
            适用于多人团队，支持按照所选基础规则自动分配所有线索
          </div>
        </div>
        <!-- 手动分配 -->
        <template>
          <div class="clue_manualAllot" style="margin-left: 20px">
            <div>
              <el-radio v-model="totalConfig_params.push_type" :label="2"
                >手动分配</el-radio
              >
            </div>
            <div class="clue_prompt">公海客户统一由管理员转交成员</div>
          </div>
        </template>
      </div>
      <div v-show="totalConfig_params.push_type == 1">
        <div class="rule_title">分配规则</div>
        <div style="margin-bottom: 15px">
          <div class="allocation_rule">
            <el-radio v-model="totalConfig_params.push_style" :label="0">
              按员工顺序轮流分配
            </el-radio>
          </div>
          <div class="rule_text">
            当有新线索进来时，系统查看所有员工，按照顺序自动在员工间轮流分配线索；如在[设置员工账户]
            增加新员工或删除原有员工，会自动加入/剔除分配队列
          </div>
        </div>
        <div class="allocation_rule">
          <el-radio v-model="totalConfig_params.push_style" :label="1">
            按员工线索数量补齐分配
          </el-radio>
        </div>
        <div class="rule_text">
          当有新线索进来时，系统查看所有员工被分配的线索数量，然后优先分配给数量最少的员工
        </div>
        <div class="choose_title">选择员工</div>
        <el-input
          v-model="searchMember"
          prefix-icon="el-icon-search"
          placeholder="请输入搜索词"
          class="choose_employee"
          @input="ChangeInput"
        ></el-input>
        <!-- 选择栏 -->
        <div class="flex-row">
          <!-- 员工列 -->
          <div class="choose_box_left flex-box">
            <div class="choose_box_header">
              <span>员工</span>
            </div>
            <div class="choose_box_content">
              <el-checkbox-group v-model="selected_Member">
                <el-checkbox
                  v-for="(item, index) in search_user_list"
                  v-model="item.selected"
                  :key="index"
                  class="choose_Multiple_selection"
                  :label="item.id"
                  @change="selectedChange"
                >
                  {{ item.user_name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <!-- 已选择员工列 -->
          <div class="choose_box_right flex-box">
            <div class="choose_box_header">
              <span>已选{{ this.selected_Member.length }}项</span>
              <span style="margin-left: auto" @click="clearSelected">
                清空
              </span>
            </div>
            <div class="selected_box_content">
              <div
                class="selected_box_text"
                v-for="(item, index) in member_detailed"
                :key="index"
              >
                <span>{{ item.user_name }}</span>
                <i
                  class="el-icon-close cancel_selected"
                  @click="deleteEmployee(item, index)"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 保存 -->
      <div class="footer_btn">
        <el-button type="primary" @click="onSave">保存</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      // 线索分配配置
      totalConfig_params: {},
      employee_params: {
        page: 1,
        per_page: 100,
        type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
      },
      user_list: [], // 关联成员列表
      selected_Member: [], // 已选中的成员
      member_detailed: [], // 已选中的成员的详细信息
      searchMember: "", // 搜索成员关键词
      search_user_list: [], // 搜索成员列表
      website_id: ""
    }
  },
  async mounted() {
    this.website_id = this.$route.query.website_id;
    let res = await this.$http.getManagerAuthList({ params: this.employee_params }).catch(() => { });
    if (res.status == 200) {
      this.user_list = res.data.data;
      this.search_user_list = res.data.data;
    }
    this.getClueAllocation();
    // new Promise((resolve) => {
    //     resolve()
    // }).then(async () => {
    //     // 获取关联成员
    //     let res= await this.$http.getManagerAuthList({ params: this.employee_params });
    //     this.user_list =res.data.data;
    //     this.search_user_list = res.data.data;
    //     console.log(this.search_user_list,"search_user_list")
    // }).then(() => {
    //     this.getClueAllocation();
    // })
  },
  methods: {
    selectedChange() {
      this.member_detailed = [];
      this.getSelected_Member();
    },
    // 获取站点CRM线索分配配置
    getClueAllocation() {
      this.$http.getClueAllocation().then((res) => {
        if (res.status == 200) {
          this.totalConfig_params = res.data;
          if (this.totalConfig_params.push_uid != "") {
            let num = this.totalConfig_params.push_uid.split(',')
            for (let i = 0; i < num.length; i++) {
              num[i] = parseInt(num[i])
            }
            // 如果已经选中的员工不在员工列表中就删除
            let is_exist = [];
            this.search_user_list.map((item) => { //遍历员工列表
              // 遍历已选中的员工列表
              num.map((list) => {
                // 如果员工存在
                if (item.id == list) {
                  is_exist.push(item.id);
                }
              })
            })
            this.selected_Member = is_exist; // 赋值选中并且存在的员工
            this.getSelected_Member();
          }
        }
      })
    },
    // 获取已选中成员的详细信息
    getSelected_Member() {
      let num = [];
      for (let i = 0; i < this.selected_Member.length; i++) {
        // Member = this.user_list.find(item => {item.id == this.selected_Member[i]});
        let Member = "";
        this.user_list.map((item) => {
          if (item.id == this.selected_Member[i]) {
            Member = item;
          }
        })
        if (Member) {
          num.push(Member);
        }
      }
      num.map(item => {
        this.member_detailed.push(item)
      })
    },
    // 删除，指定已选择的员工
    deleteEmployee(item) {
      let subIndex = this.selected_Member.indexOf(item.id);
      // 删除已选中的成员 左部分选择
      this.selected_Member.splice(subIndex, 1);
      // 删除已选中的成员的详细信息 右部分选择
      this.member_detailed.splice(subIndex, 1);
    },
    // 清除已选员工
    clearSelected() {
      this.member_detailed = [];
      this.selected_Member = [];
    },
    // 保存
    onSave() {
      this.$set(this.totalConfig_params, "push_uid", this.selected_Member.toString())
      this.$http.setClueAllocation(this.totalConfig_params).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      }).catch((res) => {
        this.$message.error(res);
      })
    },
    // 搜索员工
    ChangeInput(val) {
      this.search_user_list = this.user_list
      let num = [];
      this.search_user_list.map(item => {
        if (item.user_name.indexOf(val) != -1) {
          num.push(item)
        }
      })
      this.search_user_list = num;
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep.header {
  .clue_title {
    color: #2e3c4e;
    margin-bottom: 24px;
  }
  .rule_title {
    color: #2e3c4e;
    margin: 24px 0;
    font-size: 14px;
  }
  .rule_text {
    font-size: 12px;
    color: #8a929f;
    margin-top: 10px;
    margin-left: 24px;
  }
  .choose_title {
    font-size: 14px;
    color: #2e3c4e;
    margin: 15px 0;
  }
  .clue_manualAllot {
    width: 304px;
    background-color: #f8f8f9;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 6px;
    .el-radio {
      .el-radio__label {
        color: #2e3c4e;
      }
    }
  }
  .choose_employee {
    margin-bottom: 15px;
    .el-input__inner {
      width: 400px;
    }
  }
  .choose_box_left {
    .choose_box_content {
      width: 100%;
      height: 310px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .el-checkbox-group {
        display: flex;
        flex-direction: column;
      }
      .choose_Multiple_selection {
        background-color: #f4f4f5;
        padding: 5px 8px;
        border-bottom: 1px solid #e3e3e5;
        margin-right: 0px;
        .el-checkbox__label {
          font-size: 12px;
          color: #2e3c4e;
        }
      }
      .choose_Multiple_selection:last-child {
        border-bottom: none;
      }
    }
  }
  .choose_box_right,
  .choose_box_left {
    width: 240px;
    height: 346px;
    border: 1px solid #e3e3e5;
    border-radius: 3px;
    .choose_box_header {
      display: flex;
      background-color: #f8f8f7;
      padding: 10px 0;
      border-bottom: 1px solid #e3e3e5;
      & span {
        color: #2e3c4e;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
      }
    }
  }
  .choose_box_right {
    margin-left: 15px;
    .choose_box_header {
      & span:nth-child(2) {
        font-weight: 500;
        color: #3961e4;
        margin-right: 10px;
        cursor: pointer;
      }
      & span:last-child {
        font-weight: 500;
        color: #2e3c4e;
        margin-right: 10px;
      }
    }
    .selected_box_content {
      width: 100%;
      height: 310px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .selected_box_text:first-child {
        margin-top: 10px;
      }
      .selected_box_text {
        display: flex;
        align-items: center;
        background: #f4f4f5;
        padding: 2px 5px;
        margin: 0 10px 10px 10px;
        border-radius: 3px;
        font-size: 12px;
        color: #2e3c4e;
        .cancel_selected {
          width: 12px;
          height: 10px;
          margin-left: auto;
          cursor: pointer;
        }
      }
    }
  }
  .allocation_rule {
    .el-radio {
      .el-radio__label {
        color: #2e3c4e;
      }
    }
  }
}
.footer_btn {
  margin-top: 30px;
  .el-button {
    // color: #2e3c4e;
    padding: 10px 30px;
    // background-color: #F4F4F3;
    // border: none;
  }
}
.clue_prompt {
  font-size: 12px;
  color: #8a929f;
  margin-left: 24px;
  margin-top: 10px;
}
</style>