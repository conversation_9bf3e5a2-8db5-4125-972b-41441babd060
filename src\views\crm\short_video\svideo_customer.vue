<template>
    <div v-fixed-scroll="47">
      <div class="head-check">
        <div class="head-list">
          <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
            @change="onChangeTime">
          </el-date-picker>
        </div>
        <div class="head-list">
          <el-input v-model="params.ies_uniq_id" placeholder="请输入抖音号" size="small"
          @change="onSubmit"></el-input>
        </div>
        <div class="head-list">
          <el-input v-model="params.nick_name" placeholder="请输入昵称" size="small"
          @change="onSubmit"></el-input>
        </div>
        <div class="head-list">
          <el-input v-model="params.mobile" placeholder="请输入手机号" size="small"
          @change="onSubmit"></el-input>
        </div>
        <div class="head-list">
          <el-select size="small" v-model="params.ies_uid_from" placeholder="请选择账号"
          @change="onSubmit" clearable>
          <el-option
              v-for="item in account_list"
              :key="item.id"
              :label="item.name+item.ies_uniq_id"
              :value="item.ies_uid">
              <span>{{item.name}}</span><small class="option-room-name">{{item.ies_uniq_id}}</small>
            </el-option>
          </el-select>
        </div>
        <div class="head-list">
			    	<el-select v-model="params.has_follow" size="small" clearable placeholder="选择留资状态" @change="onSubmit">
			    		<el-option label="全部" :value="0"></el-option>
			    		<el-option label="已有维护人" :value="1"></el-option>
			    		<el-option label="暂无维护人" :value="2"></el-option>
			    	</el-select>
        </div>
      </div>
      <div>
        <div class="operatebtn">
	      	<el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
                  <div class="f-list">
                      <div class="f-item" v-for="item in cus_list" :key="item.id" @click="TransferCustomer(item)">
                        {{ item.name }}
                      </div>
                    </div>
                    <div slot="reference" style="margin-right:10px;" @click="getCrmDepartmentList" 
	      		         class="search_loudong div row align-center">
                      <div class="seach_value">操作</div>
                      <div class="sanjiao" :class="{ transt: transfer_type }"></div>
                  </div>
              </el-popover>
	      </div>
      </div>
        <div>
            <el-table
            v-loading="is_table_loading" 
              :data="tableData"
              border
              style="width: 100%"
              :cell-style="rowStyle"
              :header-cell-style="{ background: '#EBF0F7' }"
              @cell-mouse-enter="handleMouseEnter"
              @cell-mouse-leave="handleMouseLeave"
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55"
                align="center"
                >
              </el-table-column>
              <el-table-column
                prop="nick_name"
                label="用户信息"
                fixed
                v-slot="{ row }"
                min-width="210">
                <div class="avatar"  :style="row.client_id>0 ? ' cursor: pointer;' : ''"
                      @click="goCustomerDetail(row)">
                    <div>
                        <img v-if="row.avatar" :src="row.avatar" class="avatar-img"/> 
                        <span v-else class="avatar-word">{{getFirstWord(row.nick_name)}}</span>
                    </div>
                    <div style="text-align:left;margin-left:5px;">
                      <div>{{row.nick_name?row.nick_name:"--"}}</div>
                      <div class="douyin">{{row.ies_uniq_id?row.ies_uniq_id:"--"}}</div>
                    </div>
                    
                </div>
              </el-table-column>
              <el-table-column
                prop="latest_content"
                label="最近评论内容"
                v-slot="{ row }"
                min-width="150"
                >
                <el-tooltip class="item" placement="top" effect="light">
                    <div slot="content" class="huanhang" style="width: 200px;"
                    >{{row.latest_content?row.latest_content:'--'}}</div>
                    <span class="clamp-text">
                      {{row.latest_content?row.latest_content:"--"}}
                    </span>
                </el-tooltip>
              </el-table-column>
              <el-table-column
                label="是否留资"
                prop="desc"
                v-slot="{ row }"
                min-width="120">
                    {{row.desc?row.desc:"--"}}
              </el-table-column>
              <el-table-column
              v-if="params.fill==1"
                label="留资状态"
                prop="desc"
                v-slot="{ row }"
                min-width="120">
                    {{row.follow_name?"已有维护人":"暂无维护人"}}
              </el-table-column>
              <el-table-column
                prop="mobile"
                label="手机号"
                min-width="150"
                v-slot="{ row }">
                <template v-if="row.mobile">
                  <span   
                    style="cursor: pointer"
                    @click="togglePhoneNumberDisplay(row)">
                      {{ row.mobile }}
                  </span>
                  
                </template>
                <template v-else-if="row.newmobile !== undefined">
                  <el-input v-model.number="row.newmobile" placeholder="请输入手机号" size="mini">
                  </el-input>
                  <div class="btn">
                    <el-link type="text" style="margin-right:5px;" :underline="false" @click="cancel(row)">取消</el-link>
									  <el-link type="primary" :underline="false" @click="savePhone(row)">保存</el-link>
                  </div>
                </template>
                <template v-else>
                  <i class="el-icon-edit" @click="editPhone(row)"></i>
                </template>
              </el-table-column>
              <el-table-column
                label="所属抖音账号"
                prop="account_name"
                v-slot="{ row }"
                min-width="200"
                >
                    {{row.account_name?row.account_name:"--"}}
                    <div class="douyin">
                      {{row.account_ies_uniq_id?row.account_ies_uniq_id:"--"}}
                    </div>
                   
              </el-table-column>
              <el-table-column
                label="短视频来源"
                prop="video_title"
                v-slot="{ row }"
                min-width="200">
                <el-tooltip class="item" placement="top" effect="light">
                    <div slot="content" class="huanhang">{{row.video_title?row.video_title:'--'}}</div>
                    <span class="clamp-text">
                      <el-link type="primary" @click="copyCusID(row.play_url)">  
                      <span class="clamp-text">
                        {{row.video_title?row.video_title:"--"}}
                    </span></el-link>
                    </span>
                </el-tooltip>
              </el-table-column>
              <el-table-column
                prop="latest_time"
                label="最近评论时间"
                v-slot="{ row }"
                min-width="200"
                >
                {{row.latest_time?row.latest_time:"--"}}
              </el-table-column>
              <el-table-column
                prop="user_status_name"
                label="标记状态"
                v-slot="{ row }"
                min-width="100"
              >
                <template v-if="!row.newuser_status_name">
                  {{ row.user_status_name }}
                </template>
                <template v-else>
                  <el-select v-model="row.user_status" placeholder="请选择状态" size="mini">
                    <el-option
                      v-for="item in user_status_list"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                  <div class="btn">
                    <el-link type="text" style="margin-right: 5px;" :underline="false" @click="cancel(row)">取消</el-link>
                    <el-link type="primary" :underline="false" @click="saveStatus(row)">保存</el-link>
                  </div> 
                </template>
                <template v-if="row.enterstatus_name&&row.enterstatus_name==1">
                  <div>
                    <i class="el-icon-edit" @click="editStatus(row)"></i>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                v-slot="{ row }">
                <el-link  type="primary" :underline="false"
                @click="viewcomments(row)">发私信</el-link>
              </el-table-column>
            </el-table> 
            <div class="tab-content-footer">
              <div class="flex-row">
                <el-radio-group v-model="params.fill" @change="onSubmit"
                size="small">
                  <el-radio-button label="0">未留资</el-radio-button>
                  <el-radio-button label="1">已留资</el-radio-button>
                </el-radio-group>
                <div style="margin-left:10px;"> <el-button type="primary" size="small" @click="empty">全部用户</el-button></div>
              </div>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="params.per_page"
                    :current-page="params.page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total">
                  </el-pagination>
          </div>
        </div>
        <transmitassembly ref="transmitassembly"></transmitassembly>
        <svideomessage ref="svideomessage" v-if="dialogs.svideomessage"/>
    </div>
</template>
<script>
import svideomessage from "./svideosendprivatemessage.vue"
import transmitassembly from "@/views/crm/live_room/components/transmitassembly.vue"
export default {
  components:{
    svideomessage,
    transmitassembly
  },
  props:{
    user_status_list:{
      type:Array,
      default:() => []
    },
    account_list:{
      type:Array,
      default:() => []
    },
    info_id:{
      type:String,
      default:() => ""
    }
  },
    data() {
        return {
            params:{
                page: 1,
                per_page: 10,
                fill:"0",
            },
            tableData:[],//表格数据
            is_table_loading:false,
            timeValue:"",//时间检索
            pickerOptions: {
              shortcuts: [{
                text: '今天',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '昨天',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setDate(start.getDate() - 1);
                  end.setDate(start.getDate());
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本周',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上周',
                onClick(picker) {
                    const today = new Date(); // 获取当前日期
                    const end = new Date(today); // 结束日期为当前日期
                    const start = new Date(today); // 开始日期为当前日期
                    const day = today.getDay(); // 获取当前是星期几
                    const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
                    const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
                    end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
                    // end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
                    start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
                    // start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
                    // 将计算得到的时间范围传递给日期选择器
                    picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上月',
                onClick(picker) {
                  const end = new Date();
                  end.setDate(0); // 获取上个月的最后一天
                  end.setHours(23, 59, 59, 0);
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              },]
            },
            fill_list:[
              {id:0,name:"未留资"},
              {id:1,name:"已留资"},
             
            ],//是否留资检索
            dialogs:{
              svideomessage:false
            },
            transfer_type: false, // 显示/隐藏转交类型
		      	// 转交类型
		      	cus_list: [
              		{ id: 1, name: "转交到同事" },
		      		{ id: 2, name: "复制到同事的流转客" },
            		],
		      	datalist:[],
		      	multipleSelection:[], 
        }
    },
    watch:{
      info_id(newVal, oldVal){
        if(newVal && newVal > "0"){
            this.params.info_id = this.info_id
            this.getList()
        }
		  },
    },
    mounted(){
        let pagenum = localStorage.getItem( 'pagenum')
    	  this.params.per_page = Number(pagenum)||10
        this.getList()
    },
    methods:{
        rowStyle(){
          return "text-align:center"
        } ,
        getList(){
          this.is_table_loading = true
            this.$http.getcommentuserslist(this.params).then(res=>{
                if(res.status==200){
                    this.is_table_loading = false
                    this.tableData = res.data.data
                    this.params.total = res.data.total
                }
            })
        },
        getFirstWord(str){
          return str.trim().replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '').substring(0, 1);
        },
        getCrmDepartmentList() {
	    		// this.transfer_type = true
	    	},
	    	//选中的人员
	    	handleSelectionChange(data){
	    		this.multipleSelection = data
	    	},
	    	// 转交客户
	    	TransferCustomer(item) {
        	    // 判断是否选中客户
        	    if (!this.multipleSelection.length) {
        	    return this.$message({
        	      message: "请选择客户",
        	      type: "warning",
        	    });
        	  }
        	  if(this.multipleSelection.length&&this.multipleSelection.length>50){
        	    return this.$message({
        	      message: "每次最多选择50个客户",
        	      type: "warning",
        	    });
        	  }
            const nonZeroClientIdData = this.multipleSelection.filter(item => item.client_id !== 0);
            const clientIds = nonZeroClientIdData.map(item => item.client_id);
		        if(!clientIds.length){
		        return this.$message.warning("没有关联的客户")
		        }
	    	  this.$refs.transmitassembly.open(this.multipleSelection ,item)
        	},
        //打开crm客户详情页
        goCustomerDetail(row){
		    	if(!row.client_id){
		    		return;
		    	}
		    	this.$goPath('/crm_customer_detail?id='+row.client_id+'&type='+(row.is_my_client == 1 ? 'my' : 'seas'));
		    },
        // 点击编辑图标时触发，切换到编辑模式
        editPhone(row) {
          this.$set(row, 'newmobile', row.mobile || '');
        },
        //编辑手机号取消
        cancel(row){
          if(row.newmobile||row.newmobile==""){
            this.$set(row, 'newmobile', undefined); // 取消手机号编辑状态
          }
          if(row.newuser_status_name){
            this.$set(row, 'newuser_status_name', null); // 取消状态编辑状态
          }
        },
        //查看手机号
        togglePhoneNumberDisplay(row) {
          this.$set(row, 'mobile', row.mobile_full); // 取消手机号编辑状态
        },
        //保存手机号编辑
        savePhone(row) {
          // 保存手机号
          let params = {
            id:row.id,
            mobile:row.newmobile
          }
          this.$http.saveshortvideophone(params).then(res=>{
            if(res.status==200){
              console.log(res.data);
              if(res.data.status==2){
                this.$confirm(res.data.msg, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.$http.addphoneenter({id:row.id}).then(res=>{
                  if(res.status==200){
                    this.$message({
                      type: 'success',
                      message: '录入成功!'
                    });
                    row.mobile = row.newmobile; // 更新手机号
                    this.$delete(row, 'newmobile'); // 删除新手机号字段，切换回显示模式
                  }
                })

              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消录入'
                });  
                row.mobile = row.newmobile; // 更新手机号
                this.$delete(row, 'newmobile'); // 删除新手机号字段，切换回显示模式        
              });
              }else{
                row.mobile = row.newmobile; // 更新手机号
                this.$message.success(res.data.msg)
                this.$delete(row, 'newmobile'); // 删除新手机号字段，切换回显示模式   
              }
              
                }
              })
        },
        // 复制用户主页
        copyCusID(id) {
          this.$onCopyValue(id);
        },
        //编辑状态
        editStatus(row) {
          this.$set(row, 'newuser_status_name', 1);
          this.$set(row, 'enterstatus_name', 0); // 隐藏编辑图标
        },
        //鼠标移入单元格
        handleMouseEnter(row, column, cell, event) {
          if(row.newuser_status_name==null&&column.label=="标记状态"){
            this.$set(row, 'enterstatus_name', 1); // 显示编辑图标
          }
        },
        //鼠标移出单元格
        handleMouseLeave(row, column, cell, event) {
          if(column.label=="标记状态"){
            this.$set(row, 'enterstatus_name', 0); // 隐藏编辑图标
          }
        }, 
        //保存状态
        saveStatus(row){
          let params = {
            id:row.id,
            user_status:row.user_status,
          }
          this.$http.setshortvideostatus(params).then(res=>{
            if(res.status==200){
              let selectedStatus = this.user_status_list.find(item => item.id === row.user_status);
              if (selectedStatus) {
                this.$set(row, 'user_status_name', selectedStatus.name); // 显示编辑图标
              }
              this.$message.success("修改成功！")
              this.$set(row, 'newuser_status_name', null)
            }
          })
        },
        //发私信
        viewcomments(row){
          this.dialogs.svideomessage = true;
          this.$nextTick(()=>{
            this.$refs.svideomessage.open(row);
          })
        },
        onChangeTime(e) {
          this.params.start_date = e ? e[0] : ""; // 赋值开始时间
          this.params.end_date = e ? e[1] : ""; // 赋值结束时间
          this.params.page = 1; // 显示第一页
          this.getList(); // 获取最新数据
        },
        //查询
        onSubmit(){
          this.params.page = 1; // 显示第一页
          this.getList()
        },
        //清空
        empty(){
          this.params = {
                page: 1,
                per_page: 10,
                fill:"0",
          }
          this.getList()
        },
        //每页 条
        handleSizeChange(val){
            this.params.per_page = val
            this.getList()
        },
        //当前页
        handleCurrentChange(val) {
            this.params.page = val
            this.getList()
        },
    },
}
</script>
<style scoped lang="scss">
.head-check{
  display:flex;
  margin-bottom: 15px;
  .head-list {
    margin-right: 10px;
  }
}
.avatar{
    white-space: nowrap;
    display: flex;
    align-items: center;
    .avatar-img{
        height: 32px;
        width: 32px;
        border-radius: 50%;
        margin-right: 7px;
    }
    .avatar-word{
        display: inline-block;
        height: 32px;
        width: 32px;
        border-radius: 50%;
        background-color: #f1f2f3;
        line-height: 32px;
        text-align: center;
        color: #c6c6c6;
        font-weight: 300;
    }
}
.douyin{
      // margin-left: 5px;
      color: #a9a9a9;
      font-size: 13px;
}
.el-icon-edit{
    position: absolute;
    bottom: 3px;
    right: 3px;
  }
.btn{
  display: flex;
  justify-content: flex-end;
}

.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 15px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
::v-deep{
	  .option-room-name{
		color: #a9a9a9;
		margin-left: 12px;
		float: right;
    }
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
	}
.clamp-text {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: left;
  }
  .operatebtn{
	width: 100%;
	display: flex;
	justify-content: flex-end;
	margin-bottom: 10px;
}
.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}
.search_loudong {
  width: 70px;
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 30px;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
</style>