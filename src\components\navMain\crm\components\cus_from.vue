<template>
  <div>
    <div class="addbtn">
      <el-button  type="primary" @click="addLabel">添加</el-button>
    </div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      highlight-current-row
    >
      <!-- <el-table-column prop="source_id" label="来源ID"></el-table-column> -->
      <el-table-column prop="title" label="名称"></el-table-column>
      <el-table-column label="开启状态">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status == 1">开启</el-tag>
          <el-tag type="danger" v-if="scope.row.status == 0">关闭</el-tag>
        </template>
      </el-table-column>
       <el-table-column prop="order" label="排序"></el-table-column>
       <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="EditSource(scope.row)">编辑</el-button>  
          <el-button type="danger" size="small" v-if="scope.row.is_system==0" @click="DelCustom(scope.row)">删除</el-button>
        </template>
       </el-table-column>
      <!-- <el-table-column prop="created_at" label="添加时间"></el-table-column> -->
    </el-table>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog
       title="编辑"
       :visible.sync="dialogVisible"
       width="400px">
       <div>
        <el-form ref="form" :model="sourceform" label-width="80px">
            <el-form-item label="名称：" v-if="sourceform.is_system==1">
              <el-input
                  placeholder="请输入内容"
                  v-model="sourceform.title"
                  :disabled="true"
                  style="width: 200px;">
              </el-input>
            </el-form-item>
            <el-form-item label="名称：" v-if="sourceform.is_system==0">
              <el-input
                  placeholder="请输入内容"
                  v-model="sourceform.title"
                  style="width: 200px;">
              </el-input>
            </el-form-item>
            <el-form-item label="状态：">
              <el-switch
                v-model="sourceform.status" :active-value="1" :inactive-value="0">
              </el-switch>
            </el-form-item>
            <el-form-item label="排序：">
                <el-input v-model="sourceform.order" style="width: 200px;"></el-input>
            </el-form-item>
        </el-form>
       </div>
       <span slot="footer" class="dialog-footer">
         <el-button @click="dialogVisible = false">取 消</el-button>
         <el-button type="primary" @click="determine">确 定</el-button>
       </span>
    </el-dialog>
    <el-dialog
       title="添加"
       :visible.sync="dialogadd"
       width="400px">
       <div>
        <el-form ref="form" :model="adddata" label-width="80px">
            <el-form-item label="名称：">
              <el-input
                  placeholder="请输入内容"
                  v-model="adddata.title"
                  style="width: 200px;">
              </el-input>
            </el-form-item>
            <el-form-item label="状态：">
              <el-switch
                v-model="adddata.status" :active-value="1" :inactive-value="0">
              </el-switch>
            </el-form-item>
            <el-form-item label="排序：">
                <el-input v-model="adddata.order" style="width: 200px;"></el-input>
            </el-form-item>
        </el-form>
       </div>
       <span slot="footer" class="dialog-footer">
         <el-button @click="dialogadd = false">取 消</el-button>
         <el-button type="primary" @click="addsure">确 定</el-button>
       </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      dialogVisible: false,
      dialogadd:false,
      sourceform:{
        input:"",
      },
      adddata:{
        title:"",
        status:"",
        order:"",
      }
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmCustomerFrom({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          console.log(this.tableData);
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    //编辑客户来源
    EditSource(row){
      this.dialogVisible = true
      let params = JSON.parse(JSON.stringify(row));
      this.sourceform = params
      this.sourceform.id=row.source_id
    },
    //添加客户来源
    addLabel(){
      this.dialogadd = true
    },
    //确定提交客户来源
    addsure(){
      this.$http.addCustomSource(this.adddata).then((res)=>{
        if(res.status==200){
          this.$message({
            type:"success",
            message:"添加成功！"
          })
          this.dialogadd = false
          this.getDataList();
        }
      })
    },
    //删除自定义客户来源
    DelCustom(row){
      this.$confirm('此操作将永久删除该来源, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.delCustomSource(row.source_id).then((res)=>{
            if(res.status==200){
              this.$message({
                type:"success",
                message:"删除成功！"
              })
              this.getDataList();
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    determine(){
    console.log(this.sourceform);
    let params = JSON.parse(JSON.stringify(this.sourceform));
    if(params.source_id){
      if(params.is_system==0){
        delete params.source_id
        delete params.created_at
      }else{
        delete params.source_id
        delete params.title
        delete params.created_at
      }
    }
    this.$http.Modifycustomersource(params).then(res=>{
      if(res.status==200){
        this.$message({
          type:"success",
          message:"编辑成功"
        })
        this.dialogVisible = false
        this.getDataList();
      }
    })
    },
  },
};
</script>

<style lang="scss" scoped>
.addbtn{
  text-align: right;
  margin: 0px 40px 15px;
}

</style>
