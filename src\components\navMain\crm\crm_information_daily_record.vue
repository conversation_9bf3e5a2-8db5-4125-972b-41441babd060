<template>
    <div class="all_task_list">

        <div class="header-title div row">
            <!-- 导入任务列表 -->
            <div class="ht-title" v-for="item in titlelist" :key="item.id"
            :class="{isitem:titleid == item.id}"
            @click="tabititle(item.id)">
            {{ item.title }} </div>
            <div style="margin-right: 24px">

            </div>
        </div>
        <!-- 任务详情(新) -->
        <el-row :gutter="20" v-if="show1">
            <el-col :span="24">

                <div class="content-box-crm">
                    <div class="table-top-box div row">

                    </div>
                    <el-table :data="tableDataA" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
                        :row-style="$TableRowStyle"
                        v-loading="loading">
                        <el-table-column label="操作人员" prop="admin.user_name" align="left">
                        </el-table-column>
                        <el-table-column label="类型" prop="type" align="left">
                            <template slot-scope="scope">
                                        <div v-if="scope.row.type == 1">CRM同步到流转客</div>
                                        <!-- <div v-if="scope.row.type == 2">抖音</div> -->
                                        <div v-if="scope.row.type== 3">流转客转私客</div>
                                        <div v-if="scope.row.type== 4">删除流转客</div>
                                        <!-- <div v-if="scope.row.type== 5">视频号</div> -->
                                    </template>
                        </el-table-column>
                         <el-table-column label="CRM" prop="client" width="140" align="left">
                            <template slot-scope="scope">
                                <div v-if="scope.row.client">
                                    {{scope.row.type== 4?"--":scope.row.client.cname}}
                                </div>
                                <div v-else>--</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="流转客" prop="private_client" width="140" align="left">
                            <template slot-scope="scope">
                                <div v-if="scope.row.private_client">
                                    {{scope.row.private_client.cname}}
                                </div>
                                <div v-else>--</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="接收者" prop="admin_receive" align="left">
                            <template slot-scope="scope">
                                <div v-if="scope.row.admin_receive">
                                    {{scope.row.admin_receive.user_name}}
                                </div>
                                <div v-else>--</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="内容" prop="content" align="left">
                        </el-table-column>
                        <!-- <el-table-column label="失败客户数量" prop="error_num" align="left">
                        </el-table-column> -->
                        <el-table-column label="创建时间" prop="created_at" align="left">
                        </el-table-column>
                        <!-- <el-table-column label="操作" align="center" scope="scope">
                            <template slot-scope="scope">
                                <el-link type="primary" @click="look_item(scope.row.id)">详情</el-link>
                                <el-link v-if="scope.row.status == 1" type="primary" @click="down_item(scope.row.file_url)" style="margin:0  10px;">下载</el-link>
                            </template>
                        </el-table-column> -->
                    </el-table>
                    <!-- 分页 -->
                    <div class="block">
                        <div> 
                            <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                        </div>
                        <el-pagination
                          @size-change="handleSizeChange"
                          @current-change="handleCurrentChange"
                          :current-page="telinfo_params.page"
                          :page-sizes="[10, 20, 30, 40,50,100]"
                          :page-size="telinfo_params.per_page"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="telinfoTotal">
                        </el-pagination>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name: "crm_customer_all_task_list",
    components: {
        // myLabel,
    },
    data() {
        return {
            telinfo_params: {
                page: 1,
                per_page: 10,
            },
            tableData: [],
            tableDataA:[],
            task_id: 0,
            website_id: 0,
            telinfoTotal: 0,
            loading:false,
            show:false,
            show1:false,
            typeid:"0",
            titlelist:[
                {id:1,title:'日志列表'},
            ],
            titleid:1,
        };
    },
    mounted() {
        if (this.$route.query.website_id) {
            this.website_id = this.$route.query.website_id
        }
        this.information()
    },
    created() {
    },
    computed: {

    },
    methods: {
        //流转客操作日志列表记录
        information(){
            let item = {
                page: this.telinfo_params.page,
                per_page: this.telinfo_params.per_page,
            }
                this.loading = true
                this.$http.informationdailyrecord(item).then((res) => {
                    if (res.status == 200) {
                        this.tableDataA = res.data.data;
                        console.log(this.tableDataA);
                        this.show1 = true
                        this.telinfoTotal = res.data.total
                        this.loading = false
                    }
                });
        },
        // 分页器-当前页
        handleCurrentChange(val) {
            this.telinfo_params.page = val
            this.information()
        },
        //查看详情
        look_item(id) {
            console.log(id);
            let url = `/crm_customer_task_list?task_id=` + id;
            if(this.titleid==2){
                url = `/crm_customer_task_list?task_id= + ${id}&information=1`;

            }
            this.$goPath(url); // 跳转客户详情
        },
        //刷新按钮，刷新表格
        Refresh(){
            this.information()
        },
        //下载
        down_item(url){
            window.open(url)
        },
        handleSizeChange(val) {
            this.telinfo_params.per_page = val 
            this.information()
        },
        //导入类型
        Search_Status(){
            if(this.typeid==""||this.typeid==undefined){
                this.$message.warning("请选择状态")
                return
            }else{
                this.telinfo_params.type = this.typeid
                if(this.titleid==1){
                  this.getData()  
                }else{
                    this.information()
                }
            }
       },
    //    头部列表切换
       tabititle(id){
        this.titleid = id
        if(this.titleid==1){
            this.getData();
        }else{
            this.information()
        }
       }
    },
};
</script>
  
<style scoped lang="scss">
.all_task_list {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 24px;

    .bottom-border {
        align-items: center;
        padding-bottom: 24px;
        justify-content: flex-start;
        border-bottom: 1px dashed #e2e2e2;

        .text {
            font-size: 14px;
            color: #8a929f;

            .label {
                width: 70px;
                display: inline-block;
                text-align: right;
            }
        }
    }

    .header-title {
        height: 50px;
        line-height: 50px;
        background: #fff;
        margin: -24px -24px 24px;
        // justify-content: space-between;
        align-items: center;
        cursor: pointer;

        .ht-title {
            margin-left: 43px;
            font-size: 18px;
            color: #2e3c4e;
            &.isitem{
              color: #165DFF;
              border: 2px solid white;
              border-bottom-color: #2d84fb;
            }
        }
    }
    .block{
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
    }
}
</style>
  