<template>
    <div>
        <myTable :table-list="curList" :header="columns" tooltipEffect="light" v-loading="loading" select
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
			:cellClassName="cellClassName" ref="myTable" @selection-change="selectionChange"></myTable>
	</div>
</template>
<script>
import myTable from "@/components/components/my_table";

export default {
    props: {
        params: { type: Object, default: () => ({}) },
        list: { type: Array, default: () => [] },
		liuzi: { type: Boolean, default: false},
		loading: { type: Boolean, default: false},
    },
	components: {
		myTable
	},

	data() {
		return {
			multipleSelection: [],
			curList: [],
		}
	},
	watch: {
		list: {
			handler(data){
				this.curList = data.map(e => {
					return {...e, 
						//手机号是否已显示全号
						__wholePhoneNumberDisplaied: false,
					};
				})
			},
			immediate: true
		}
	},
	computed: {
		//是否指定直播间
		isRoomId(){
			return this.params.room_id ? true : false
		},
		columns(){	
			const userCloumn = {
				prop: "id",
				label: "用户信息",
				fixed: "left",
				minWidth: 220,
				render: (h, data) => {
					return (
						<div class={['user-box',{client: data.row.client_id}]} onClick={()=>this.goCustomerDetail(data.row)}>
							<div class="avatar">
								{data.row.avatar ? 
								<img src={data.row.avatar} class="avatar-img"/> : 
								<span class="avatar-word">{this.getFirstWord(data.row.nick_name)}</span>
								}
							</div>
							<div class="info">
								<p class="nickname">{data.row.nick_name}</p>
								{data.row.user_name && <p class="user-name">{data.row.user_name}</p>}
							</div>
						</div>
					);
				}
			};

			const mobileColumn = {
				prop: "mobile",
				label: "手机号",
				minWidth: 180,
				render: (h, data)=>{
					return (
						<div>
							{data.row.mobile && <span class={['mobile',{client: !data.row.__wholePhoneNumberDisplaied}]} onClick={()=>this.displayWholePhoneNumber(data.row)}>{data.row.mobile}</span>}
							{!data.row.mobile && <span>--</span>}
						</div>
					)
				}
			}

			const commentRoomrender = (h, data, filed)=>{
				return (
					<div>
						{data.row[filed] ?
						<el-popover
							placement="top-start"
							width="380"
							trigger="hover">
							<div class="comment-popover" domPropsInnerHTML={data.row[filed].replace(/\n/g, '<br>')}></div>
							<div slot="reference"><div class="comment-multiline">{data.row[filed]}</div></div>
						</el-popover>
						:''}
					</div>
				)
			}

			const capitalRoomrender = (h, data, filed)=>{
				return (
					<div>
						{data.row[filed] ? "已有维护人" : "暂无维护人"}
					</div>
				)
			}

			// 根据liuzi状态(已留资/未留资)配置表格列
			if(this.liuzi){
				// 已留资状态：显示所有列
				return [
					userCloumn,
					{ prop: "comment", label: "互动内容", minWidth: 220, render: (h, data)=>commentRoomrender(h, data, 'comment')},
					{ prop: "desc", label: "留资描述", minWidth: 200 },
					{ prop: "follow_name", label: "留资状态", minWidth: 180, render: (h, data)=>capitalRoomrender(h, data, 'follow_name')},
					{ prop: "follow_name", label: "维护人", minWidth: 160 },
					mobileColumn,
					{ prop: "add_time_begin", label: "第一条评论时间", minWidth: 200 },
					{ prop: "add_time_end", label: "最后一条评论时间", minWidth: 200 },
					{ prop: "comments_num", label: "评论条数", minWidth: 160 }
				];
			}else{
				// 未留资状态：不显示留资状态、维护人、手机号列
				return [
					userCloumn,
					{ prop: "comment", label: "互动内容", minWidth: 220, render: (h, data)=>commentRoomrender(h, data, 'comment')},
					{ prop: "desc", label: "留资描述", minWidth: 200 },
					{ prop: "add_time_begin", label: "第一条评论时间", minWidth: 200 },
					{ prop: "add_time_end", label: "最后一条评论时间", minWidth: 200 },
					{ prop: "comments_num", label: "评论条数", minWidth: 160 }
				];
			}
		}
	},
	methods: {
        getFirstWord(str){
			return str.trim().replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '').substring(0, 1);
		},
		cellClassName({column}){
			return column.property == 'mobile' ? 'cell-mobile' : ''
		},
        handleSelectionChange(val) {
			this.multipleSelection = val
		},
		selectionChange(val){
			this.$emit('child-event',val);
		},
		//切换直播间
		handleRoomChange(row){
            this.$emit("roomChange", row.room_id);
		},
		//打开crm客户详情页
		goCustomerDetail(row){
			if(!row.client_id){
				return;
			}
			this.$goPath('/crm_customer_detail?id='+row.client_id+'&type='+(row.is_my_client == 1 ? 'my' : 'seas'));
		},
		//复制
		copyText(txt){
			this.$onCopyValue(txt);
		},

		//显示全号
		displayWholePhoneNumber(row){
			row.mobile_full && (row.mobile = row.mobile_full);
			row.__wholePhoneNumberDisplaied = true;
		}
	}
}
</script>
<style scoped lang="scss">
::v-deep{
	.user-box{
		display: flex;
		flex-direction: row;
		height: 50px;
		align-items: center;
		&.client{
			cursor: pointer;
		}
		.avatar{
			height: 32px;
			.avatar-word{
				display: inline-block;
				height: 32px;
				width: 32px;
				border-radius: 50%;
				background-color: #f1f2f3;
				line-height: 32px;
				text-align: center;
				color: #c6c6c6;
				font-weight: 300;
			}
			.avatar-img{
				height: 32px;
				width: 32px;
				border-radius: 50%;
			}
		}
		.info{
			flex: 1;
			margin-left: 12px;
			.nickname{
				font-size: 14px;
				color: #333;
				margin: 0;
				line-height: 18px;
				max-width: 160px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				text-align: left;
			}
			.user-name{
				font-size: 12px;
				color: #999;
				margin: 2px 0 0;
				line-height: 14px;
				max-width: 160px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
	.mobile{
		&.client{
			cursor: pointer;
			color: #409EFF;
		}
	}
	.comment{
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		line-height: 20px;
	}
	.comment-multiline{
		max-width: 200px;
		line-height: 20px;
		word-wrap: break-word;
		word-break: break-all;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
	}
	.comment-popover{
		max-height: 300px;
		overflow-y: auto;
		line-height: 1.5;
	}
	.cell-mobile{
		.cell{
			display: flex;
			align-items: center;
		}
	}
}
</style> 