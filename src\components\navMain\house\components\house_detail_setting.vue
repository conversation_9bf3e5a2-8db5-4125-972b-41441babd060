<template>
    <div>
        <div class="footer">
            <el-button type="danger" @click="dialogs.delHouse=true">删除房源</el-button>
        </div>

        <el-dialog width="600px" title="删除房源" :visible.sync="dialogs.delHouse">
            <el-form label-width="120px">
                <el-form-item label="删除原因">
                    <el-input v-model="delparams.content" type="textarea" placeholder="房源删除原因（选填）"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="dialogs.delHouse=false">取消</el-button>
                <el-button type="primary" @click="delHouse" :loading="loadings.delHouse">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        id: Number,
    },
    data(){
        return {
            delparams: {
                content: ''
            },
            loadings: {
                delHouse: false
            },
            dialogs: {
                delHouse: false
            }
        }
    },
    methods: {
        //删除房源
        async delHouse(){
            this.delparams.id = this.id;
            this.loadings.delHouse = true;
            const res = await this.$http.delHouse(this.delparams);
            this.loadings.delHouse = false;
            if(res.status == 200){
                this.$message.success(res.data?.msg || '删除成功');
                this.dialogs.delHouse = false;
                setTimeout(()=>{
                    this.goHouseList();
                    this.$emitPageRefresh('house_list');
                },300)
            }
        },
        //返回
        goHouseList(){
            let name = window.location.href.split("#")[1];
            this.$store.state.closeTab = true;
            eventBus.$emit("closeTab", name);
            this.$router.go(-1);
        }
    }
}
</script>

<style lang="scss" scoped>
.footer{
    padding: 0 24px 20px;
}
</style>