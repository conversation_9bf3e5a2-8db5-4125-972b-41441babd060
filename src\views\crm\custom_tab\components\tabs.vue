<template>
<div class="custom-tabs-wrapper">
    <div class="custom-tabs">
        <div class="custom-tabs-inner" :class="{'is-scrolling':isOverFlowed,'is-next-disabled':nextScrollAbled}">
            <div class="custom-tabs-scroll" ref="tabs">
                <slot></slot>
                <div v-for="tab in tabs" :key="tab.key" @click="onTabClick(tab)" class="b-t-item" 
                    :class="{ isactive: seledTab.id == tab.id }">
                    <div class="date_value">
                        {{ tab.name }}
                    </div>
                </div>
            </div>
        </div>
        <div class="custom-tabs-btns" v-if="perms.customBtn">
            <el-button type="text" @click="openTabIndexPage"><i class="el-icon-plus"></i> 自定义</el-button>
        </div>
    </div>
    
    <el-drawer append-to-body :title="tabIndexPage.title" :visible.sync="tabIndexPage.show" size="460px" @close="onTabIndexPageClose">
        <tabIndex :type="type" v-if="tabIndexPage.show" @dataChange="onTabDataChange" @close="tabIndexPage.show=false" style="height: calc( 100vh - 78px);"/>
    </el-drawer>
</div>
</template>

<script>
import tabIndex from '../index.vue'
export default {
    components: {
        tabIndex
    },
    props: {
        value: { type: [Number, String, Object], default: '' },
        contentLoaded: { type: Boolean, default: true },
        type: { type: Number, default: 1 },    //1:我的,2:公海，3：潜客
    },
    data(){
        return {
            tabs: [],                   //tabs
            seledTab: {},               //当前选中的tab
            isOverFlowed: false,        //tabs是否超出滚动
            nextScrollAbled: false,     //tabs是否可向右滚动
            isTabDataChange: false,     //tabs数据是否变化
            tabIndexPage: {
                show: false,
                title: '自定义导航'
            },
            //权限
            perms: {
                customBtn: false,        //自定义按钮
            },
            tabName:'',
        }
    },
    computed: {
        curSelectedTab(){
            switch(this.tabName){
                case '7daysNotFollow':
                    return this.tabs.find(e => e.is_default == 1);
                    
            }
        }
    },
    watch: {
        value(val, oldVal){
            //切回内置tab
            if(typeof oldVal === 'object' && typeof val !== 'object'){
                this.$emit('changeOut', this.type);
            }
            
            this.seledTab = typeof val == 'object'? val : {};
            this.$nextTick(()=>{
                this.scrollIntoView()
            })
        },
        contentLoaded(val){
            if(val){
                this.$nextTick(()=>{
                    this.scrollIntoView()
                })
            }
        },
        curSelectedTab(val){
            if(val){
                this.tabName = '';
                this.onTabChange(val);
            }
        }
    },
    activated(){
        this.onResize();
    },
    created(){
        this.getTabs();
        this.checkRole();
    },
    mounted(){
        window.addEventListener('resize', this.$Utils.debounce(this.onResize));
        eventBus.$on('crmTabDataChange', (data, tabType) => {
            if(tabType != this.type){
                return;
            }
            this.tabs = data;
            if(this.seledTab.id){
                const tab = this.tabs.find(e => e.id == this.seledTab.id);
                if(tab){
                    this.onTabChange(tab);
                }else{
                    const tabs = this.$refs.tabs;
                    tabs.children[0].click();
                }
            }
        })
    },
    beforeDestroy(){
        window.removeEventListener('resize', this.onResize);
        eventBus.$off('crmTabDataChange');
    },
    methods: {
        setSelectedTabName(tabName){
            this.tabName = tabName;
        },
        //检查权限
        async checkRole(){
            //公海客户仅创始人显示自定义
            if(this.type == 2){
                const roleInfo = await this.$store.dispatch("queryMyRoleInfo");
                if(roleInfo){
                    this.perms.customBtn = roleInfo.is_super ? true : false;
                }
            }else{
                this.perms.customBtn = true;
            }
        },
        //页面resize
        onResize(){
            const tabs = this.$refs.tabs;
			this.isOverFlowed = tabs && tabs.scrollWidth > tabs.clientWidth;
            this.nextScrollAbled = !this.isOverFlowed;
        },
        //打开自定义配置页
        openTabIndexPage(){
            this.tabIndexPage.show = true;
        },
        //获取tabs
        async getTabs(){
            const res = await this.$http.getCrmCustomTabs({tab_type: this.type});
            if(res.status == 200){
                this.tabs = res.data.map(e => {
                    e.key = 'custom_'+e.id;
                    return e;
                });
                
                this.$nextTick(()=>{
                    this.onResize();
                })
            }
        },
        //tab点击
        onTabClick(tab){
            if(this.seledTab.id === tab.id){
                return;
            }
            this.onTabChange(tab);
        },
        //tab切换
        onTabChange(tab){
            const query = tab.path.substring(tab.path.indexOf('?') + 1);
            const params = this.$Utils.parseUrlQuery(query);
            for(const key in params){
                if(this.$Utils.isNumeric(params[key])){
                    params[key] *= 1;
                }
            }
            if(this.type==3){
                params.c_type1 = 4
            }
            const value = {...tab, params};
            this.$emit('input', value);
            this.$emit('change', value, this.type);
        },
        //tabs滚动
        scrollIntoView(){
            if(!this.isOverFlowed){
                return;
            }
            const tabs = this.$refs.tabs;
            let curTabElem =  tabs.querySelector('.isactive') || tabs.children[0],
                clientWidth = tabs.clientWidth,
                scrollWidth = tabs.scrollWidth,
                scrollLeft = tabs.scrollLeft,
                maxScrollLeft = scrollWidth - clientWidth,
                scrollTo = curTabElem.offsetLeft + curTabElem.clientWidth/2 -clientWidth/2;
                
            if(scrollTo >= maxScrollLeft){
                this.nextScrollAbled = true;
                scrollTo = maxScrollLeft;
            }else{
                this.nextScrollAbled = false;
                if(scrollTo < 0){
                    scrollTo = 0;
                }
            }
            if(scrollTo != scrollLeft){
                this.$Utils.scrollLeft(tabs, scrollTo,200);
            }
        },
        //tabs数据变化
        onTabDataChange(){
            this.isTabDataChange = true;
        },
        //更新tabs
        async onTabIndexPageClose(){
            if(this.isTabDataChange){
                this.isTabDataChange = false;
                await this.getTabs();
                eventBus.$emit('crmTabDataChange', this.tabs, this.type);
            }   
        }
    }  
}
</script>
<style lang="scss" scoped>
.custom-tabs-wrapper{
    width: 100%
}
.custom-tabs{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .custom-tabs-inner{
        flex: 1;
        position: relative;
        overflow: hidden;
        width: 100%;
        flex-direction: row;
        align-items: stretch;
        flex-wrap: nowrap;
        flex-direction: row;
        &:after{
            content: " ";
            position: absolute;
            top: -50%;
            right: 0;
            width: 100%;
            height: 200%;
            pointer-events: none;
            z-index: 1;
            box-shadow: inset -3px 0 6px #252b3a1f;
            visibility: hidden;
        }
        &.is-scrolling:not(.is-next-disabled):after{
            visibility: visible;
        }
        .custom-tabs-scroll{
            display: flex;
            overflow: hidden;
            width: 100%;
        }
    }
    .custom-tabs-btns{
        width: 85px;
        padding: 12px 8px 0 0;
        display: flex;
        justify-content: flex-end;
    }
    .b-t-item,.custom-tabs-tab{
        box-sizing: border-box;
        cursor: pointer;
        padding: 0 10px;
        display: inline-block;
        min-width: 120px;
        height: 50px;
        text-align: center;
        line-height: 40px;
        color: #8a929f;
        position: relative;
        margin-top: 20px;
        border-top-right-radius: 4px;
        border-top-left-radius: 4px;
        &.isactive{
            color: #00a3ff;
            background-color: #fff;
        }
    }
}
</style>