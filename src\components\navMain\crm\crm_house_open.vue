<template>
  <div class="pages">
    <div class="t-form div row">
      <div class="l-form">
        <div class="l-f-t">
          <div class="t">系统初始化房源模块开通</div>
          <span>请配置基础信息</span>
        </div>
        <div class="c-form">
          <el-form :model="form_info" inline>
            <el-form-item label="业务城市：">
              <div class="i-row-box div row">
                <el-select @change="onChangeP" style="width:32%" v-model="p1" placeholder="省">
                  <el-option v-for="item in p_list" :key="item.id" :label="item.area_name" :value="item.id">
                  </el-option>
                </el-select>
                <el-select @change="onChangeP2" style="width:32%" v-model="p2" placeholder="市">
                  <el-option v-for="item in city_list" :key="item.id" :label="item.area_name" :value="item.id">
                  </el-option>
                </el-select>
                <el-select style="width:32%" v-model="p3" placeholder="县">
                  <el-option v-for="item in area_list" :key="item.id" :label="item.area_name" :value="item.id">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="公司名称：">
              <el-input style="width:270px" v-model="form_info.name" placeholder="请输入公司名称"></el-input>
            </el-form-item>
            <el-form-item label="您的姓名：">
              <el-input style="width:270px" v-model="form_info.user_name" placeholder="请输入您的姓名"></el-input>
            </el-form-item>
            <el-form-item label="手机号码：">
              <el-input maxlength="11" style="width:270px" v-model="form_info.tel" placeholder="请输入您的手机号码"></el-input>
            </el-form-item>
          </el-form>
          <div class="button" @click="onClickOpen">立即开通</div>
        </div>
      </div>
      <div class="r-desc">
        <div>欢迎使用，T+系统</div>
        <div>新一代房客源协同管理系统</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form_info: {
        area_id: "",
        name: "",
        user_name: "",
        tel: "",
      },
      p_list: [],
      city_list: [],
      area_list: [],
      p1: "",
      p2: "",
      p3: "",
    };
  },
  mounted() {
    this.getDataArea();
  },
  methods: {
    getDataArea() {
      this.$http.getOpenHouseProvinceList().then((res) => {
        if (res.status === 200) {
          this.p_list = res.data;
        }
      });
    },
    async getDataCity(pid, type) {
      let res = await this.$http.getCityListByPid(pid).catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        if (type === 1) {
          this.city_list = res.data;
        } else {
          this.area_list = res.data;
        }
      }
    },
    onChangeP(e) {
      this.getDataCity(e, 1);
    },
    onChangeP2(e) {
      this.getDataCity(e, 2);
      this.p3 = "";
    },
    onClickOpen() {
      this.form_info.area_id = this.p3 || this.p2 || this.p1;
      if (
        !this.form_info.name ||
        !this.form_info.user_name ||
        !this.form_info.tel
      ) {
        this.$message.error("请检查内容后提交");
        return;
      }
      this.$message.success("正在提交数据");
      this.$http.setOpenHouseData(this.form_info).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.$router.push("house_list?trade_type=0");
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: calc(100vh); // 添加高度防止切换组件时菜单闪烁
  background-image: url("https://img.tfcs.cn/backup/static/t%2B/bg.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .t-form {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    align-items: center;

    .l-form {
      background: #fff;
      width: 540px;
      border-radius: 10px;
      margin-right: 156px;

      .l-f-t {
        border-bottom: 1px solid #dcdcdc;
        text-align: center;
        margin-top: 34px;

        .t {
          font-size: 32px;
          color: #2e3c4e;
        }

        span {
          display: inline-block;
          padding: 12px 0;
          color: #8a929f;
        }
      }

      .c-form {
        padding: 26px 40px;
      }
    }

    .r-desc {
      color: #ffffff;
      font-size: 40px;
    }
  }

  .button {
    text-align: center;
    border-radius: 5px;
    background: #3f71ff;
    box-shadow: 0px 4px 20px 0px #3f71ff99;
    width: 370px;
    height: 50px;
    line-height: 50px;
    color: #fff;
    margin-top: 20px;
    cursor: pointer;
  }
}

.i-row-box {
  width: 270px;
  justify-content: space-between;
}
</style>
