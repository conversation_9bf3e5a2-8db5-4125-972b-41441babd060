<template>
    <div>
        <el-dialog
          :title="user_name"
          :visible.sync="dialogVisible"
          width="40%"
          :before-close="handleClose">

          <div class="sevenstyle">
            近7日客户量变动记录
          </div>
            <el-table
              :data="tableData"
              style="width: 100%"
              border
              v-loading="tableloading"
              :header-cell-style="{ background: '#EBF0F7' }">
                <el-table-column
                  prop="date"
                  label="日期">
                </el-table-column>
                <el-table-column
                  label="私客"
                  v-slot="{row}">
                  {{row.crm_client_num}}<span v-if="row.crm_client_num_status>0">（{{row.crm_client_num_status==1?"+"+row.crm_client_num_change :
                  row.crm_client_num_status==2?"-"+ row.crm_client_num_change : "持平"}}）</span>
                </el-table-column>
                  <el-table-column
                    prop="transfer_client_num"
                    label="流转客"
                    v-slot="{row}">
                  {{row.transfer_client_num}}<span v-if="row.transfer_client_num_status>0">（{{row.transfer_client_num_status==1?"+"+row.transfer_client_num_change :
                  row.transfer_client_num_status==2?"-"+ row.transfer_client_num_change : "持平"}}）</span>
                  </el-table-column>
            </el-table>
            <!-- <div class="block">
              <el-pagination
                layout="prev, pager, next"
                @current-change="handleCurrentChange"
                :total="datatotal.total">
              </el-pagination>
            </div> -->

          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
          </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            dialogVisible:false,
            params:{},
            tableData:[],
            user_name:"",
            datatotal:{
              total:"" ,
            },
            tableloading:false,
        }
    },
    methods:{
        open(params,name){
            this.params = params
            this.user_name = name
            this.getsevendata()
        },
        getsevendata(){
          this.tableloading = true
            this.$http.getsevencustomtotalchange(this.params).then(res=>{
              if(res.status==200){
                this.tableData = res.data.data
                this.datatotal.total = res.data.total
                this.tableloading = false
                this.dialogVisible = true
              }
            })
        },
        handleCurrentChange(val){
          this.params.page = val
          this.getsevendata()
        },
        //关闭弹窗
        handleClose(){
            this.dialogVisible = false
        },
    },
}
</script>
<style lang="scss" scoped>
.sevenstyle{
  text-align: center;
  line-height: 24px;
  font-size: 18px;
  color: #303133;
  font-weight: bold;
  margin-bottom: 15px;
}
.block{
  text-align:right;
}
</style>