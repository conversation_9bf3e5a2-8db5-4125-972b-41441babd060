<template>
    <div class="content">
        <el-container>
            <div class="page_title">系统提醒</div>
            <!-- 左侧 -->
            <el-aside width="480px">
            <div class="task_box">
                <!-- 头部 -->
                <div class="task_header flex-row">
                    <div class="cate_list flex-row">
                        <div 
                            class="cate" 
                            @click="checkCate(item)" 
                            v-for="(item, index) in cate_list" 
                            :key="index"
                            :class="{active: list_params.type===item.type}"
                        >
                            {{item.name}}
                        </div>
                    </div>
                    <div class="creat_btn" @click="showAddTask()">
                        <i class="el-icon-circle-plus-outline" style="margin-right: 5px;"></i>新建
                    </div>
                </div>
                <!-- 内容 -->
                <div class="infinite-list task_list" v-infinite-scroll="loadTask">
                    <div 
                        class="task_item" 
                        :class="item.id == messageID.id ? 'current' : ''"
                        @click="onSelectTask(item)" 
                        v-for="(item, index) in formData_list" 
                        :key="index" 
                    >
                        <div class="avatar" v-if="list_params.type">
                            <i class="el-icon-newfontyoujian"></i>
                        </div>
                        <div class="info">
                            <p class="to">
                                <span v-if="item.release_user">{{item.release_user.user_name}}
                                    <font v-for="(receive, idx) in item.receive_user" :key="idx">@{{receive.user_name}}</font>
                                </span>
                            </p>
                        <p class="to_time">
                            <span>日期：{{item.work_time}}</span>
                        </p>
                        <p class="cs">
                            <span>抄送：{{item.cc_user ? '' : '-'}}</span>
                            <span 
                                v-for="(list, index) in item.cc_user" 
                                :key="list.id"
                            >
                                {{ list.user_name }}{{ index !== item.cc_user.length - 1 ? ',' : '' }}
                            </span>
                            <span v-if="item.cc_user.length == 0">--</span>
                        </p>
                        <p class="descp">{{item.content}}</p>
                        <p class="complete" v-if="item.receive">
                            <span v-for="(receive_item, idx) in item.read" :key="idx">
                                <font>{{receive_item}}</font>已读
                            </span>
                        </p>
                        </div>
                    </div>
                    <div class="loading" v-loading="list_loading =='loading'" element-loading-text="加载中">
                        {{list_loading ==='nomore'? '没有更多了': ''}}
                    </div>
                </div>
            </div>
            </el-aside>
            <!-- 右侧 -->
            <el-main>
                <div class="task_detail">
                    <div class="title">
                        <span>提醒详情</span>
                        <el-button 
                            v-show="list_params.type == 2"
                            size="small" 
                            :disabled="messageID.is_read ? true : false"
                            type="primary" 
                            @click="setRead"
                            :loading="is_loadings"
                        >
                            标记已读
                        </el-button>
                        <el-button 
                            v-show="list_params.type == 3"
                            size="small" 
                            :disabled="messageID.id ? false : true"
                            type="danger" 
                            @click="delRead"
                            :loading="is_loadings"
                        >
                            删除提醒
                        </el-button>
                    </div>
                    <div class="user_info">
                        <div v-if="!messageID.release_user" class="avatar"></div>
                        <!-- <img v-if="!messageID.release_user" class="avatar" :src="item.is_true" alt="" /> -->
                        <img v-else class="avatar" src="@/assets/icon-tx.png" alt="" />
                        <div class="info">
                            <span class="label">发起人</span>
                            <span v-if="messageID.release_user" class="name">{{messageID.release_user.user_name}}</span>
                            <span v-else class="name">系统提醒</span>
                        </div>
                    </div>
                    <div class="detail">
                        <p>
                            <span class="label">日期</span>
                            <span class="value">{{messageID.work_time}}</span>
                        </p>
                        <p>
                            <span class="label">跟进人</span>
                            <span class="value" v-if="messageID.receive_user">
                                <font v-for="(receive, idx) in messageID.receive_user" :key="idx">@{{receive.user_name}}</font>
                            </span>
                        </p>
                        <p>
                            <span class="label">抄送</span>
                            <span>
                                <span 
                                    class="value" 
                                    v-for="(copys, index) in messageID.cc_user" 
                                    :key="index"
                                >
                                    {{ copys.user_name }}{{ index !== messageID.cc_user.length - 1 ? ',' : '' }}
                                </span>
                            </span>
                        </p>
                        <p>
                            <span class="label">内容</span>
                            <span class="value">{{messageID.content}}</span>
                        </p>
                    </div>
                </div>
            </el-main>
      </el-container>
      <!-- 添加提醒 -->
      <el-dialog title="系统提醒" :visible.sync="show_add_todo" width="400px">
        <el-form ref="ruleForm" :model="todo_params" label-width="80px">
            <el-form-item label="日期" prop="work_time">
                <el-date-picker 
                    style="width: 258px;"
                    v-model="todo_params.work_time" 
                    value-format="yyyy-MM-dd HH:mm:ss" 
                    type="datetime"
                    @change="changeTime"
                    placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="跟进人" prop="receive_uid">
                <el-select 
                    style="width: 258px;" 
                    v-model="todo_params.receive_uid" 
                    multiple 
                    placeholder="请选择"
                >
                    <el-option v-for="item in admin_list" :key="item.id" :label="item.user_name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="内容" prop="content">
                <el-input 
                    style="width: 258px;" 
                    v-model="todo_params.content" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入提醒内容"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="抄送" prop="cc_userid">
                <el-select 
                    style="width: 258px;" 
                    v-model="todo_params.cc_userid" 
                    multiple placeholder="请选择"
                >
                    <el-option v-for="item in admin_list" :key="item.id" :label="item.user_name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer addReminder">
          <span class="cancel" @click="show_add_todo = false">取消</span>
          <el-button class="submits" size="small" type="primary" @click="addTodo">提交</el-button>
        </div>
      </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            // 消息tabs切换
            cate_list: [
                {
                    type: 1,
                    name: "最新"
                },
                {
                    type: 2,
                    name: "我收到的"
                },
                {
                    type: 3,
                    name: "我发起的"
                },
                {
                    type: 4,
                    name: "历史"
                },
            ],
            // 系统提醒接口参数
            list_params: {
                page: 1,
                per_page: 10,
                type: 1, // 类型(1:最新,2:我收到的,3我发起的,4:历史)
            },
            formData_list: [],
            list_loading: "", // 判断加载状态
            messageID: {}, // 单条消息内容
            is_loadings: false, // 按钮loading动画
            show_add_todo: false, // 添加系统提醒模态框
            // 添加系统提示参数
            todo_params: {
                work_time: '', // 时间
                receive_uid: [], // 跟进人
                content: '', // 内容
                cc_userid: [], // 抄送人
            },
            admin_list: [], // 管理员列表
            follow_load: true, // 限制下拉加载数据
        }
    },
    created() {
        this.getSystemReminder();
    },
    methods: {
        changeTime(val) {
            console.log(val);
        },
        // 切换消息tabs
        checkCate(item) {
            this.list_params.type = item.type;
            this.list_params.page = 1; // 重置页码
            this.getSystemReminder(); // 获取系统消息
        },
        // 点击新建
        showAddTask() {
            this.show_add_todo = true; // 显示模态框
            this.getAdminList(); // 获取管理员列表
            // 选择时间赋值当前时间
            const nowTime = new Date(); // 获取当前时间的时间戳
            const currentYear = nowTime.getFullYear(); // 获取当前年份
            const currentMonth = this.padZero(nowTime.getMonth() + 1); // 获取当前月份
            const currentDay = this.padZero(nowTime.getDate()); // 获取当前日期
            const currentHour = this.padZero(nowTime.getHours()); // 获取当前小时
            const currentMinute = this.padZero(nowTime.getMinutes()); // 获取当前分钟
            const currentSecond = this.padZero(nowTime.getSeconds()); // 获取当前秒数
            this.todo_params.work_time = `${currentYear}-${currentMonth}-${currentDay} ${currentHour}:${currentMinute}:${currentSecond}`;
        },
        padZero(number) {
            return number.toString().padStart(2, '0');
        },
        // 确定提交新建提醒
        addTodo() {
            if(this.todo_params.work_time == '' || this.todo_params.work_time == undefined) {
                return this.$message({
                    message: '请选择时间',
                    type: 'warning'
                });
            }
            if(this.todo_params.receive_uid.length == 0 || this.todo_params.receive_uid == undefined) {
                return this.$message({
                    message: '请选择跟进人',
                    type: 'warning'
                });
            }
            if(this.todo_params.content == '' || this.todo_params.content == undefined) {
                return this.$message({
                    message: '请填写内容',
                    type: 'warning'
                });
            }
            this.todo_params.receive_uid = this.todo_params.receive_uid.join(",");
            this.todo_params.cc_userid = this.todo_params.cc_userid.length !== 0 ? this.todo_params.cc_userid.join(",") : '';
            this.$http.addSystemReminder(this.todo_params).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: '提醒成功',
                        type: 'success'
                    });
                    this.show_add_todo = false;
                    this.$refs.ruleForm.resetFields();
                    this.getSystemReminder();
                }
            }).catch(() => {
                this.$message.error('操作失败');
                this.$refs.ruleForm.resetFields();
            })
        },
        // 获取管理员列表
        getAdminList() {
            this.$http.getManagerAuthList().then((res) => {
                if(res.status == 200) {
                    this.admin_list = res.data.data;
                    // console.log(this.admin_list,"admin");
                }
            })
        },
        // 滚动消息栏
        loadTask() {
            if (!this.follow_load) {
                return this.list_loading ='nomore';
            }
            this.list_params.page++;
            this.getSystemReminder();
        },
        // 点击消息
        onSelectTask(item) {
            this.messageID = item;
            // console.log(item);
        },
        // 提醒标记已读
        setRead() {
            this.is_loadings = true; // 开启loading动画
            this.$http.setMarkSystemReminder(this.messageID.id).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: '标记成功',
                        type: 'success'
                    });
                    this.getSystemReminder();
                }
            })
        },
        // 删除提醒
        delRead() {
            this.$confirm('确定删除此提醒吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.is_loadings = true; // 开启loading动画
                this.$http.deleteSystemReminder(this.messageID.id).then((res) => {
                    if(res.status == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getSystemReminder();
                    }
                })
            })
        },
        // 系统提醒消息
        getSystemReminder() {
            this.list_loading = 'loading';
            if(this.list_params.page == 1) {
                this.formData_list = [];
            }
            this.$http.getSystemReminder(this.list_params).then((res) => {
                if(res.status == 200) {
                    this.formData_list = this.formData_list.concat(res.data.data);
                    if(this.formData_list.length == this.list_params.per_page) {
                        this.follow_load = true;
                    } else {
                        this.follow_load = false;
                    }
                    // console.log(this.formData_list,"formData_list");
                    // 如果没有数据
                    if(this.formData_list.length == 0) {
                        this.list_loading ='nomore';
                    } else { // 如果有数据
                        this.list_loading = '';
                    }
                    // 如果有值，切换tabs默认获取第一项
                    if(this.formData_list.length) {
                        this.messageID = this.formData_list[0];
                    } else {
                        this.messageID = {};
                    }
                    this.is_loadings = false; // 结束loading动画
                }
            })
        }
    }
}
</script>
<style scoped lang="scss">
    .content {
        width: 100%;
        height: 100%;
        background-color: #F1F4FA;
        margin: -15px;
        padding: 24px;
        .el-main {
            padding: 0;
            margin-right: 24px;
            overflow: initial;
            .task_detail {
                margin-left: 24px;
                padding: 24px;
                border-radius: 4px;
                background-color: #fff;
            }
            .task_detail {
                .title {
                    .el-button {
                        font-size: 16px;
                        padding: 7px 15px;
                    }
                }
            }
            .task_detail > .title {
                height: 32px;
                padding: 0 12px;
                border-left: 4px solid #2d84fb;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 24px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .task_detail .user_info {
                display: flex;
                align-items: center;
                margin-bottom: 24px;
            }
            .task_detail .user_info .avatar {
                width: 60px;
                height: 60px;
                margin-right: 24px;
                border-radius: 50%;
                object-fit: cover;
            }
            .task_detail .user_info .info .label {
                margin-right: 12px;
                font-size: 16px;
                color: #8a929f;
            }
            .task_detail .user_info .info .name {
                font-size: 16px;
                font-weight: bold;
            }
            .task_detail .detail {
                padding-left: 84px;
            }
            .task_detail .detail > p {
                line-height: 1.5;
                display: flex;
                justify-content: space-between;
                align-content: flex-start;
                padding: 18px 24px;
                border: 1px solid #dde1e9;
                border-radius: 4px;
                margin-bottom: 24px;
            }
            .task_detail .detail > p .label {
                width: 70px;
                min-width: 70px;
                margin-right: 24px;
                color: #8a929f;
            }
            .task_detail .detail > p .value {
                font-size: 14px;
                color: #2e3c4e;
            }
            .task_detail .detail > p .value font {
                margin-left: 3px;
                color: #fe6c17;
            }
        }
        .page_title {
            color: #2e3c4e;
            margin-bottom: 24px;
            font-size: 18px;
            font-weight: bold;
            position: absolute;
            top: 0;
            left: 0;
        }
        .el-container {
            padding-top: 48px;
            position: relative;
            height: 84vh;
        }
        .task_box {
            border-radius: 4px;
            height: 100%;
            background-color: #fff;
            position: relative;
            .task_header {
                font-size: 14px;
                padding: 24px 12px;
                justify-content: space-between;
                align-items: center;
                .cate_list {
                    align-items: center;
                    color: #2e3c4e;
                    .cate {
                        padding: 2px 10px;
                        cursor: pointer;
                    }
                    .active {
                        color: #2d84fb;
                    }
                }
                .creat_btn {
                    padding: 2px 10px;
                    color: #2d84fb;
                    cursor: pointer;
                }
            }
            .add_todo .user_info {
                display: flex;
                align-items: center;
                margin-bottom: 24px;
            }
            .add_todo .user_info .avatar {
                width: 64px;
                height: 64px;
                margin-right: 12px;
                border-radius: 50%;
                object-fit: cover;
            }
            .add_todo .user_info .info .label {
                margin-bottom: 12px;
                font-size: 16px;
                color: #8a929f;
            }
            .add_todo .user_info .info .name {
                font-size: 18px;
                font-weight: bold;
                color: #2e3c4e;
            }
            .add_todo .todo_content > p {
                padding: 10px 12px;
                display: flex;
                justify-content: space-between;
            }
            .add_todo .todo_content > p .label {
                display: inline-block;
                width: 50px;
                line-height: 40px;
                margin-right: 24px;
                color: #8a929f;
            }
            .add_todo .todo_content > p .label i {
                position: relative;
                top: 3px;
                color: #fe6c17;
            }
            .add_todo .todo_content > p .value {
                line-height: 40px;
                flex: 1;
                text-align: right;
                color: #2e3c4e;
            }
            .add_todo .todo_content > p .el-input {
                width: 258px;
            }
            .add_todo .todo_content > p .el-textarea {
                width: 258px;
            }
            .loading {
                position: relative;
                padding: 12px;
                height: 48px;
                line-height: 48px;
                text-align: center;
                font-size: 14px;
                color: #8a929f;
            }
            .loading .el-loading-spinner {
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .loading .circular {
                width: 24px;
                height: 24px;
            }
            .task_list {
                position: absolute;
                top: 71px;
                bottom: 0;
                width: 100%;
                left: 0;
                overflow-x: hidden;
            }
            .task_list .task_item {
                display: flex;
                padding: 12px 24px 0 24px;
                cursor: pointer;
                transition: 0.36s;
            }
            .task_list .task_item:hover {
                background-color: #f7faff;
            }
            .task_list .task_item .avatar {
                width: 44px;
                text-align: center;
                height: 44px;
                margin-right: 12px;
                border-radius: 50%;
                background-color: rgba(45, 132, 251, 0.1);
            }
            .task_list .task_item .avatar img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
            }
            .task_list .task_item .avatar i {
                line-height: 44px;
                font-size: 30px;
                color: #2d84fb;
            }
            .task_list .task_item .info {
                padding-bottom: 12px;
                flex: 1;
                overflow: hidden;
                border-bottom: 1px solid #dde1e9;
            }
            .task_list .task_item .info .from {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            .task_list .task_item .info .from .time {
                font-weight: initial;
                font-size: 12px;
                color: #8a929f;
            }
            .task_list .task_item .info .name {
                margin-bottom: 12px;
                font-size: 14px;
                color: #8a929f;
            }
            .task_list .task_item .info .descp {
                font-size: 16px;
                font-weight: bold;
                line-height: 1.5;
                color: #2e3c4e;
            }
            .task_list .task_item .info .to {
                margin-bottom: 12px;
                font-size: 12px;
            }
            .task_list .task_item .info .to font {
                margin-left: 3px;
                color: #fe6c17;
            }
            .task_list .task_item .info .to_time {
                margin-bottom: 12px;
                font-weight: initial;
                font-size: 12px;
                color: #8a929f;
            }
            .task_list .task_item .info .cs {
                margin-bottom: 12px;
                font-weight: initial;
                font-size: 12px;
                color: #8a929f;
            }
            .task_list .task_item .info .cs span {
                max-width: 100px;
                display: inline-block;
            }
            .task_list .task_item .info .complete {
                margin-top: 12px;
                font-size: 12px;
            }
            .task_list .task_item .info .complete font {
                margin-left: 5px;
                color: #2d84fb;
            }
        }
    }
    .addReminder {
        .cancel {
            margin-right: 12px;
            font-size: 14px;
            padding: 7px 15px;
            border-radius: 4px;
            color: #8a929f;
            cursor: pointer;
        }
        .submits {
            font-size: 14px;
            padding: 7px 15px;
            border-radius: 4px;
        }
    }
    .current {
        background-color: #f7faff;
    }
</style>