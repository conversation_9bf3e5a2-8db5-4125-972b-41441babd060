<template>
    <div v-fixed-scroll="62">
        <el-table
              v-loading="is_table_loading"
              :data="memberdata"
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }"
              border>
              <el-table-column
                prop="user_name"
                align="center"
                label="主播"
                fixed="left">
              </el-table-column>
                <el-table-column label="留资总量"
                align="center"
                prop="custom_num">
                </el-table-column>
                  <el-table-column
                    prop="public_custom_num"
                    align="center"
                    label="公海客户">
                  </el-table-column>
                  <el-table-column
                    prop="private_custom_num"
                    align="center"
                    label="私客">
                  </el-table-column>
                  <el-table-column
                    prop="potential_custom_num"
                    align="center"
                    label="潜客">
                  </el-table-column>
                  <el-table-column
                    prop="discard_custom_num"
                    align="center"
                    label="废客">
                  </el-table-column>
                  <el-table-column
                    prop="deal_custom_num"
                    align="center"
                    label="成交量">
                  </el-table-column>
                  <el-table-column
                    prop="discard_custom_public"
                    align="center"
                    label="掉公客户">
                  </el-table-column>
                  <el-table-column
                    prop="transmit_custom_public"
                    align="center"
                    label="转公客户">
                  </el-table-column>
                  <el-table-column
                    prop="effective_num"
                    align="center"
                    label="有效客户">
                  </el-table-column>
                  <el-table-column
                    prop="invalid_num"
                    align="center"
                    label="无效客户">
                  </el-table-column>
                  <el-table-column
                    prop="suspend_num"
                    align="center"
                    label="暂缓客户">
                  </el-table-column>
                  <el-table-column
                    prop="a_num"
                    align="center"
                    label="A级客户">
                  </el-table-column>
                  <el-table-column
                    prop="b_num"
                    align="center"
                    label="B级客户">
                  </el-table-column>
                  <el-table-column
                    prop="c_num"
                    align="center"
                    label="C级客户">
                  </el-table-column>
            </el-table>
    </div>
</template>
<script>
export default {
    props: {
        memberdata: {
            type: Array,
            default:() => []
        },
        // is_table_loading: {
        //   type: Boolean,
        //     default:true
        // }
    },
    data() {
        return {
            is_table_loading:true
        }
    },
    watch:{
      // is_table_loading:{
      //   handler(newVal) {
      //       if(newVal){
      //       this.is_table_loading = newVal
      //       }
      //   }
      // },
        memberdata: {  
        handler(newVal) {
            if(newVal){
            this.is_table_loading = false
            }
        }
    },
    },
    mounted() {
    },
}
</script>
<style lang="scss" scoped>
::v-deep .el-table .el-table__fixed{
          padding-bottom: 0px !important;
}
</style>