<template>
  <div>
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel">
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.source_id" placeholder="请选择">
                <el-option v-for="item in sourceLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }" :src="
                                    `https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`
                                  " alt="" @click="
                                      () => {
                                        push_form.sex = item.id;
                                      }
                                    " />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <div class="row input-box div">
              <div @click="onClickLevel(item)" class="l-item row" v-for="item in levelLabel" :key="item.id" :class="{
                                lisactive: item.id === push_form.level_id,
                              }">
                <div class="l-name">{{ item.title }}</div>
                <div class="l-name-1">{{ item.desc }}</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.intention_community"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer flex-row align-center">
        <el-switch v-model="push_form.add_type" active-color="#13ce66" inactive-color="#2d84fb" inactive-value="1"
          active-value="2" active-text="公海" inactive-text="私客">
        </el-switch>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
            定</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
    props: {
      cluePhone: {
        type: String,
        default: () => {}
      },
      clueName: {
        type: String,
        default: () => {}
      }
    },
    data() {
        return {
            is_button_loading: false,
            is_push_customer: true,
            push_form: {
                cname: "",
                source_id: "",
                level_id: 1,
                type: "",
                sex: 1,
                subsidiary_mobile: "",
                intention_community: "",
                // intention_street: "",
                remark: "",
                add_type: "1",
            },
            other_mobile: [{ mobile: "" }],
            sex_list: [
                { id: 1, name: "nan" },
                { id: 2, name: "nv3" },
            ],
            level_list: [],
            source_list: [],
            type_list: [],
        }
    },
    watch: {
      is_push_customer: {
        handler(newval) {
          this.$emit("handleShow",newval)
        }
      }
    },
    computed: {
        levelLabel() {
            return this.level_list.filter((item) => {
                return item.id > 0;
            });
        },
        sourceLabel() {
            return this.source_list.filter((item) => {
                return item.id > 0;
            });
        },
        typeLabel() {
            return this.type_list.filter((item) => {
                return item.id > 0;
            });
        },
    },
    methods: {
        //取消
        cancel() {
            this.reset();
            this.is_push_customer = false;
        },
        //表单重置
        reset() {
            this.push_form = {
                cname: "",
                source_id: "",
                level_id: 1,
                type: "",
                sex: 1,
                subsidiary_mobile: "",
                intention_community: "",
                // intention_street: "",
                remark: "",
            };
            this.other_mobile = [{ mobile: "" }];
        },
        // 点击输入电话的img
        addDomain() {
            this.other_mobile.push({
                mobile: "",
            });
        },
        removeDomain(item) {
            var index = this.other_mobile.indexOf(item);
            if (index !== -1) {
                this.other_mobile.splice(index, 1);
            }
        },
        onClickLevel(item) {
            this.push_form.level_id = item.id;
        },
        // 确定提交
        onClickForm() {
            if (this.other_mobile.length > 0) {
                let arr = this.other_mobile.map((item) => {
                return item.mobile;
                });
                let othertel = arr.filter((item, index) => {
                if (index) {
                    return item;
                }
                });
                this.push_form.mobile = arr[0];
                this.push_form.subsidiary_mobile = othertel.join(",");
            }
            if (!this.push_form.mobile) {
                this.$message.error("请检查联系方式");
                return;
            }
            if (!this.push_form.cname) {
                this.$message.error("请检查客户姓名");
                return;
            }
            if (!this.push_form.sex) {
                this.$message.error("请检查客户性别");
                return;
            }
            if (!this.push_form.level_id) {
                this.$message.error("请检查客户等级");
                return;
            }
            if (!this.push_form.type) {
                this.$message.error("请检查客户类型");
                return;
            }
            if (!this.push_form.source_id) {
                this.$message.error("请检查客户来源");
                return;
            }
            this.is_button_loading = true;
            this.$http.setCrmCustomerDataV2(this.push_form).then((res) => {
                this.is_button_loading = false;
                this.is_push_customer = false;
                if (res.status === 200) {
                this.$message.success("操作成功");
                // this.getDataList();
                }
            });
        },
        getLevelData() {
            this.$http.getCrmCustomerLevelNopage().then((res) => {
                if (res.status === 200) {
                this.level_list = [{ title: "全部", id: 0 }, ...res.data];
                }
            });
        },
        getSourceData() {
            this.$http.getCrmCustomerSourceNopage().then((res) => {
                if (res.status === 200) {
                this.source_list = [{ title: "全部", id: 0 }, ...res.data];
                this.push_form.source_id = res.data.filter((item) => {
                    return item.is_default == 1;
                })[0].id;
                }
            });
        },
        getTypelist() {
            this.$http.getCrmCustomerTypeDataNopage().then((res) => {
                if (res.status === 200) {
                    this.type_list = [{ id: 0, title: "全部" }, ...res.data];
                    this.push_form.type = res.data.filter((item) => {
                        return item.is_default;
                    })[0].id;
                    let cus_type = parseInt(this.$route.query.cus_type);
                    res.data.map((item) => {
                        if (cus_type == 1 && item.title == "求购") {
                            this.params.type = item.id;
                        }
                        if (cus_type == 2 && item.title == "求租") {
                            this.params.type = item.id;
                        }
                    });
                }
                // this.getDataList();
            });
        },
        // 父组件传入参数进行赋值
        getCluePhone() {
          console.log(this.cluePhone, this.clueName)
          // 赋值手机号
          this.other_mobile[0].mobile = this.cluePhone;
          // 赋值备注
          this.push_form.remark = "来源" + this.clueName + "外呼线索包";
          // 赋值客户姓名
          this.push_form.cname = this.clueName;
        }
    },
    mounted() {
        this.getLevelData();
        this.getSourceData();
        this.getTypelist();
        this.getCluePhone();
    }
}
</script>
<style lang="scss" scoped>
.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
