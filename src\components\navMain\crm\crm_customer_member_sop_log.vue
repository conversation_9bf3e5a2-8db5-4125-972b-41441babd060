<template>
  <!-- 客户群 -->
  <div class="pages" id="page">
    <el-row :gutter="20">
      <el-col :span="24">
        <topTips from="crm_customer_member_sop_log" :showBtn="false"></topTips>
        <!-- <div class="content-box-crm padd0" style="margin-bottom: 24px">
          <div class="div row align-center">
            <div class="title flex-1">群SOP</div>
            <div class="add">
              <el-button type="primary" @click="toAdd" size="mini"
                >添加</el-button
              >
            </div>
          </div>
        </div> -->
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right div row"></div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column width="100" prop="id" label="ID"></el-table-column>

            <el-table-column label="发送者" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.sender }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发送状态" width="120">
              <template slot-scope="scope">
                <el-tag
                  v-if="scope.row.status == 1 || scope.row.status == 2"
                  type="success"
                  >已发送</el-tag
                >
                <el-tag v-if="scope.row.status == 0" type="warning"
                  >未发送</el-tag
                >

                <!-- <span>{{ scope.row.status | filterStatus }}</span> -->
              </template>
            </el-table-column>
            <el-table-column label="发送成功客户" v-slot="{ row }">
              <template v-if="row.send_list">
                <el-tag
                  style="margin-right: 5px"
                  type="success"
                  v-for="(item, index) in row.send_list"
                  :key="index"
                >
                  {{ item.name || item.remark }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="发送失败客户" v-slot="{ row }">
              <el-tag
                type="warning"
                style="margin-right: 5px"
                v-for="(item, index) in row.fail_list"
                :key="index"
              >
                {{ item.name || item.remark }}
              </el-tag>
            </el-table-column>
            <el-table-column label="发送时间" prop="send_time" width="180">
            </el-table-column>
            <el-table-column
              label="创建时间"
              prop="created_at"
              width="180"
            ></el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import topTips from "./components/top_tips.vue";
export default {
  name: "crm_customer_member_sop_log",
  components: {
    topTips,
  },

  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        sop_id: "",
      },

      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.params.sop_id = this.$route.query.id;
    this.getDataList();
  },

  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmMemberSopLog(this.params).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },

    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .padd0 {
    padding: 0 24px;
    margin: 0 -20px;
    .title {
      padding: 15px 40px;
    }
  }
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
</style>
