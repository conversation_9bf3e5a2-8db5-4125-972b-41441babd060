<template>
  <div class="preview-box">
    <div class="preview-head">
      <div class="head-l">
        <i class="el-icon-arrow-left"></i>
        <span>{{ title }}</span>
      </div>
      <!-- <div class="head-r">
        <span>设置</span>
        <i class="el-icon-setting"></i>
      </div> -->
      <el-dropdown @command="handleCommand">
        <span class="head-r">
          设置<i class="el-icon-setting"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <!-- <el-dropdown-item>切换机型</el-dropdown-item>
          <el-dropdown-item>缩放 +</el-dropdown-item>
          <el-dropdown-item>缩放 -</el-dropdown-item> -->
          <el-dropdown-item command="1">模拟分享</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="preview-body">
      <img :src="top_pic" />
      <div class="preview-info">
        <span>{{ title }}</span>
        <span>{{ title_sub }}</span>
        <div class="preview-mobile">
          <span>手机号</span>
          <span>请输入您的手机号</span>
          <span>一键填写</span>
        </div>
        <div class="preview-agreement">
          <span>我已阅读并同意相关用户协议及隐私政策</span>
        </div>
      </div>
      <div class="preview-rich-text">
        <div v-html="content"></div>
      </div>
    </div>
    <div class="bottom-bar">{{ btn_name }}</div>

    <div class="popup" v-if="show">
      <div class="popup-box">
        <div class="close" @click="show = false">
          <i class="el-icon-circle-close"></i>
        </div>
        <div class="popup-title">分享预览</div>
        <div class="share-box">
          <div class="share-item">
            <img :src="share_pic" />
            <div class="share-info">
              <span>{{ share_title }}</span>
              <span>{{ share_desc }}</span>
            </div>
          </div>
          <div class="share-item">
            <img :src="share_pic" />
            <div class="share-info">
              <span>{{ share_title }}</span>
              <span>{{ share_desc }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['title', 'title_sub', 'content', 'btn_name', 'top_pic', 'share_title', 'share_pic', 'share_desc'],
  data() {
    return {
      show: false,
    }
  },
  methods: {
    handleCommand() {
      this.show = !this.show
    }
  }
}
</script>
<style lang="scss" scoped>
.preview-box {
  // position: relative;
  width: 432px * 0.8;
  height: 932px * 0.8;
  border-radius: 20px;
  overflow: scroll;
  border: 10px solid #000;
  box-shadow:
    5px 5px 5px #00000014,
    5px -5px 5px #00000014,
    -5px 5px 5px #00000014,
    -5px -5px 5px #00000014;

  .preview-head {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px;

    .head-l {
      i {
        margin-right: 5px;
      }
    }

    .head-r {
      display: flex;
      align-items: center;
      border: 1px solid #333;
      border-radius: 5px;
      padding: 2px 3px;
      cursor: pointer;

      span {
        margin-right: 5px;
        font-size: 12px;
      }

      i {
        font-size: 14px;
      }
    }

    .head-r:hover {
      border: 1px solid #e09672;
    }
  }

  .preview-body {
    img {
      width: 100%;
    }

    .preview-info {
      display: flex;
      flex-direction: column;
      padding: 0 10px;
      margin-top: 10px;

      &>span:nth-child(1) {
        font-size: 20px;
        color: #333;
      }

      &>span:nth-child(2) {
        font-size: 14px;
        color: #e09672;
      }

      .preview-mobile {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 15px 0;

        span {
          color: #333;
          font-size: 16px;
        }

        &>span:nth-child(2) {
          color: #a1a1a1;
        }

        &>span:nth-child(3) {
          font-size: 14px;
          color: #ea0d02;
        }
      }

      .preview-agreement {
        color: #a1a1a1;
        font-size: 12px;
        margin-bottom: 15px;

        span {
          display: flex;
          align-items: center;
          text-align: center;
        }

        span::before {
          content: "";
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-right: 5px;
          border-radius: 50%;
          border: 1px solid #a1a1a1;
        }
      }
    }

    .preview-rich-text {
      margin-bottom: 43px;
    }
  }

  .bottom-bar {
    position: absolute;
    bottom: 10px;
    left: 10px;
    z-index: 95;
    width: 432px * 0.8;
    padding: 10px 0;
    text-align: center;
    border-radius: 0 0 10px 10px;
    color: #fff;
    background: #ea0d02;
    box-shadow: 0 0 20upx 0 rgba(0, 0, 0, 0.3);
  }

  .popup {
    position: absolute;
    bottom: 40px;
    left: 10px;
    width: 432px * 0.8;
    z-index: 90;
    border-radius: 10px 10px 0 0;
    box-shadow: 5px 5px 5px #00000014,
      5px -5px 5px #00000014,
      -5px 5px 5px #00000014,
      -5px -5px 5px #00000014;
    ;
    background-color: #fff;

    .popup-box {
      position: relative;

      .close {
        position: absolute;
        right: 10px;
        top: 0;
        font-size: 20px;
        color: #a1a1a1;
      }

      .popup-title {
        width: 100%;
        margin-top: 10px;
        text-align: center;
      }

      .share-box {
        height: 200px;

        .share-item {
          display: flex;
          align-items: center;
          border-radius: 5px;
          margin: 10px 10px;
          background-color: #f3f3f3;

          img {
            width: 100px;
            height: 100%;
            border-radius: 7px 0 0 7px;
          }

          .share-info {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 70px;
            padding: 10px 10px;
            box-sizing: border-box;


            &>span:nth-child(1) {
              font-size: 14px;
              color: #333;
            }

            &>span:nth-child(2) {
              font-size: 12px;
              color: #a1a1a1;
            }
          }
        }
      }
    }
  }
}
</style>