<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="div row">
            <div class="title flex-1">标签分析</div>
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <!-- <el-tab-pane label="按日期" name="first"></el-tab-pane> -->
            <el-tab-pane label="按员工" name="second"></el-tab-pane>
            <el-tab-pane label="按部门" name="third"></el-tab-pane>
          </el-tabs>
          <div class="bottom-border div row align-center">
            <span class="text">日期：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 370px"
              size="small"
              v-model="params.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </div>
          <div
            class="bottom-border div row align-center"
            style="margin-top: 24px"
          >
            <span class="text">负责人</span>
            <el-input
              style="width: 200px; margin-left: 20px"
              size="small"
              v-model="params.username"
            ></el-input>
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="div row align-center">
            <span class="text">总览：</span>
            <span>企微客户数：101人</span>
            <span>实际客户数：91人</span>
          </div>
        </div>
        <div class="content-box-crm bg_hui" style="margin-bottom: 24px">
          <div class="flex-row charts_box">
            <div class="left flex-1">
              <div class="bottom-line div row align-center">
                <span class="text">客户等级</span>
              </div>
              <div
                class="chart-box"
                id="chart-box-left"
                style="width: 100%"
              ></div>
            </div>
            <div class="right flex-1">
              <div class="bottom-line div row align-center">
                <span class="text">客户意向</span>
              </div>
              <div
                class="chart-box"
                id="chart-box-right"
                style="width: 100%"
              ></div>
            </div>
          </div>
          <!-- <div
            class="chart-box"
            id="chart-box"
            style="width: 100%; height: 250px"
          ></div> -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import myLabel from "../../crm/components/my_label.vue";
import * as echarts from "echarts";
export default {
  name: "crm_labels_analysis",
  components: {
    myLabel,
  },
  data() {
    return {
      activeName: "first",
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      params: {},
      echartsData: {
        dateArr: [
          "2020-08-21",
          "2020-08-22",
          "2020-08-23",
          "2020-08-24",
          "2020-08-25",
          "2020-08-26",
          "2020-08-27",
          "2020-08-28",
          "2020-08-29",
        ],
        colorArr: ["#3D74FC", "#FEC94C", "#04D38A", "#FF655F"],
        legendArr: ["已成交金额", "已成交佣金", "未完成佣金", "已发放佣金"],
        series: [
          {
            name: "已成交金额",
            type: "line",
            smooth: true,
            data: [1, 2, 4, 4, 3, 6, 5, 10, 3],
            tooltip: {
              valueFormatter: (value) => value + "人",
            },
          },

          {
            name: "已成交佣金",
            type: "line",
            smooth: true,
            data: [2, 4, 4, 3, 1, 6, 5, 10, 3],
            tooltip: {
              valueFormatter: (value) => value + "人",
            },
          },

          {
            name: "未完成佣金",
            type: "line",
            smooth: true,
            data: [6, 5, 1, 2, 4, 4, 3, 10, 3],
            tooltip: {
              valueFormatter: (value) => value + "位",
            },
          },

          {
            name: "已发放佣金",
            type: "line",
            smooth: true,
            data: [10, 1, 2, 4, 4, 3, 6, 5, 3],
            tooltip: {
              valueFormatter: (value) => value + "人",
            },
          },
        ],
      },
    };
  },
  created() {},
  mounted() {
    this.initEchart(this.echartsData);
    this.initEchart1(this.echartsData);
  },
  methods: {
    handleClick(e) {
      console.log(e);
    },
    onClickTime(e) {
      console.log(e);
    },
    initEchart(res) {
      var chartDom = document.getElementById("chart-box-left");
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          //设置tip提示
          trigger: "axis",
          // trigger: "none",
          axisPointer: {
            type: "cross",
          },
        },
        lineStyle: {
          type: "solid",
        },
        grid: {
          left: "10px",
          bottom: "0",
          containLabel: true,
        },
        // title: {
        //   text: "资金信息",
        // },
        legend: {
          //设置区分（哪条线属于什么）
          data: res.legendArr,
        },
        color: res.colorArr, //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: res.dateArr,
        },
        yAxis: {
          type: "value",
        },
        series: res.series,
      };

      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    initEchart1(res) {
      console.log(res);
      var chartDom = document.getElementById("chart-box-right");
      var myChart1 = echarts.init(chartDom);
      var option;
      option = option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "5%",
          left: "center",
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "20",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 1048, name: "Search Engine" },
              { value: 735, name: "Direct" },
              { value: 580, name: "Email" },
              { value: 484, name: "Union Ads" },
              { value: 300, name: "Video Ads" },
            ],
          },
        ],
      };

      option && myChart1.setOption(option);
      window.addEventListener("resize", () => {
        myChart1.resize();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.charts_box {
  width: 100%;
  .chart-box {
    height: 300px;
  }
  .left {
    margin-right: 20px;
    background: #fff;
    // width: calc();
  }
  .right {
    background: #fff;
  }
}
.content-box-crm {
  &.bg_hui {
    background: #f7f7f7;
  }
  .charts_box {
    // height: 500px;
    .bottom-line {
      padding: 20px;
      border-bottom: 1px solid #eee;
      color: #333;
      font-weight: 600;
    }
  }
}
</style>
