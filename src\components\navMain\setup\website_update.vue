<template>
  <el-container>
    <el-header>
      <tipsList :tips_content="tipsList"></tipsList>
    </el-header>
    <el-main>
      <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
      </div>-->
      <el-tabs
        v-model="activeName"
        type="card"
        class="tabs"
        @tab-click="handleClick"
      >
        <el-tab-pane label="综合设置" name="first">
          <el-form label-width="140px" :model="form" label-position="left">
            <!-- <el-form-item label="系统名称">
              <el-input
                class="input-short"
                type="text"
                v-model="form.name"
              ></el-input>
            </el-form-item> -->
            <el-form-item label="系统经纬度">
              <el-input
                v-model="map_lng_lat"
                placeholder
                :disabled="true"
                style="width: 200px"
              ></el-input>
              <el-button type="primary" @click="markMap" icon="el-icon-location"
                >标注位置</el-button
              >
            </el-form-item>
            <el-form-item label="成交喜报">
              <el-input
                rows="5"
                type="textarea"
                v-model="form.default_deal_big_news"
              ></el-input>
            </el-form-item>
            <el-form-item label="邀请说明">
              <el-input
                rows="5"
                type="textarea"
                v-model="form.invite_description"
              ></el-input>
            </el-form-item>
            <el-form-item label="提现手续费率">
              <div class="div row">
                <el-input-number
                  v-model="form.withdraw_fee"
                  :precision="2"
                  :step="0.1"
                  :max="100"
                  :min="0"
                ></el-input-number>
                <p>%</p>
                <span style="color: red; margin-left: 20px"
                  >说明：经纪人提现需扣除的手续费率</span
                >
              </div>
            </el-form-item>
            <el-form-item label="姓名必填">
              <el-switch
                v-model="form.client_user_force_setting_name"
                active-color="#13ce66"
                inactive-color="#ff4949"
              >
              </el-switch>
            </el-form-item>
            <el-form-item label="实名认证">
              <el-switch
                v-model="form.open_real_name"
                active-color="#13ce66"
                inactive-color="#ff4949"
              >
              </el-switch>
              <span style="color: red; margin-left: 20px"
                >说明：是否开启经纪人实名认证</span
              >
            </el-form-item>

            <el-form-item
              label="小程序主题颜色"
              v-if="form.website_mode_category == 2"
            >
              <el-color-picker v-model="form.theme"> </el-color-picker>
            </el-form-item>
            <el-form-item
              label="小程序默认主题"
              v-if="form.website_mode_category == 2"
            >
              <div class="thems-color div row">
                <p
                  @click="form.theme = '#3da8f0'"
                  style="background: #3da8f0"
                ></p>
                <p
                  @click="form.theme = '#3bca9f'"
                  style="background: #3bca9f"
                ></p>
                <p
                  @click="form.theme = '#fc5f65'"
                  style="background: #fc5f65"
                ></p>
              </div>
            </el-form-item>

            <el-form-item
              label="首页强制登录"
              v-if="form.website_mode_category == 1"
            >
              <el-switch
                v-model="form.force_login"
                active-color="#13ce66"
                inactive-color="#ff4949"
              >
              </el-switch>
            </el-form-item>
            <!-- <el-form-item label="二级分销设置">
              <el-radio-group v-model="form.distribution_brokerage_type">
                <el-radio
                  @change="changeDistribution"
                  v-for="item in distribution_type"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.desc }}</el-radio
                >
              </el-radio-group>
            </el-form-item> -->
            <el-form-item
              label="分销内容"
              v-if="form.distribution_brokerage_type === 1"
            >
              <el-input-number
                v-model="form.distribution_brokerage_value"
                :precision="2"
                :step="step"
                :max="max_num"
                :min="0"
              ></el-input-number>
            </el-form-item>
            <!-- <el-form-item
              label="分销内容："
              v-if="form.distribution_brokerage_type === 2"
            >
              <el-input v-model="form.distribution_brokerage_value" style="width:300px"></el-input>
            </el-form-item> -->
            <el-form-item label="报备入口分享展示">
              <el-switch
                v-model="form.share_report_entrance"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
            </el-form-item>
            <!-- 系统logo -->
            <el-form-item label="系统logo">
              <el-upload
                :headers="myHeader"
                :action="website_img"
                :on-success="handleSuccessLogo"
                list-type="picture-card"
                :show-file-list="false"
                :on-preview="handlePictureCardPreviewLogo"
                :on-remove="handleRemoveLogo"
              >
                <img
                  height="146px"
                  width="146px"
                  v-if="form.logo"
                  :src="form.logo"
                  alt
                />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="logoVisible">
                <img width="100%" :src="logoImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <!-- 公众号二维码 -->
            <el-form-item label="公众号二维码">
              <el-upload
                :headers="myHeader"
                :action="website_img"
                :on-success="handleSuccessPublic"
                list-type="picture-card"
                :show-file-list="false"
                :on-preview="handlePictureCardPreviewPublic"
                :on-remove="handleRemovePublic"
              >
                <img
                  height="146px"
                  width="146px"
                  v-if="form.wx_pub_qr_code"
                  :src="form.wx_pub_qr_code"
                  alt
                />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="publicVisible">
                <img width="100%" :src="publicImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <!-- 小程序二维码 -->
            <el-form-item label="小程序二维码">
              <el-upload
                :headers="myHeader"
                :action="website_img"
                :on-success="handleSuccessWx"
                list-type="picture-card"
                :show-file-list="false"
                :on-preview="handlePictureCardPreviewWx"
                :on-remove="handleRemoveWx"
              >
                <img
                  height="146px"
                  width="146px"
                  v-if="form.wx_mini_program_qr_code"
                  :src="form.wx_mini_program_qr_code"
                  alt
                />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="wxVisible">
                <img width="250" :src="wxImageUrl" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="报备配置" name="fourth">
          <el-form :model="form" label-position="left" label-width="100px">
            <el-form-item label="报备楼盘可选数量">
              <el-input
                type="number"
                min="5"
                step="1"
                style="width: 300px"
                v-model="form.max_select_project_total"
              ></el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  快速报备最多每次能选择几个楼盘
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="佣金显示规则">
              <el-radio-group v-model="form.login_display_brokerage_rule">
                <el-radio
                  v-for="item in login_display"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.desc }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  前台佣金规则显示是否需要用户登录
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="项目佣金规则">
              <el-radio-group v-model="form.brokerage_rule_category">
                <el-radio
                  v-for="item in brokerage_rule_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  前台是否显示项目佣金规则
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="可报备用户">
              <el-radio-group v-model="form.customer_reported_user_category">
                <el-radio
                  v-for="item in customer_reported_user_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  允许哪些用户报备客户
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备是否陪同">
              <el-radio-group v-model="form.reported_go_with">
                <el-radio
                  v-for="item in reported_go_with_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  报备时是否填写客户是否陪同
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="到访日期格式">
              <el-radio-group v-model="form.visit_time_format_category">
                <el-radio
                  v-for="item in visit_time_format_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  前台通过哪种格式显示时间，一般保持默认即可，也可自定义配置显示
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备日期格式">
              <el-radio-group
                v-model="form.reported_created_at_format_category"
              >
                <el-radio
                  v-for="(
                    item, index
                  ) in reported_created_at_format_category_list"
                  :key="index"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  前台通过哪种格式显示时间，一般保持默认即可，也可自定义配置显示
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="手机号补全">
              <el-radio-group v-model="form.fill_customer_phone_category">
                <el-radio
                  v-for="item in fill_customer_phone_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  允许哪些人补全手机号码
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <!-- 报备规则设置 -->
            <el-form-item label="报备规则设置">
              <el-radio-group v-model="form.reported_rule_category">
                <el-radio
                  v-for="item in reported_rule_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  报备时规则设置可选是否显示或者自定义配置
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备未审核无效类型">
              <el-radio-group
                v-model="form.reported_not_audit_invalid_category"
              >
                <el-radio
                  v-for="item in reported_not_audit_invalid_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  如果项目配置未开启自动报备有效，前台报备的客户的状态会显示为待审核。如果配置为手动，则需要后台或项目助理手动设为无效，否则一直是待审核状态；如果设为自动，则该客户如果超出报备保护期自动设为无效
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item
              label="报备规则配置"
              v-if="form.reported_rule_category == 2"
            >
              <el-input
                placeholder="请输入报备内容"
                v-model="form_report_content.reported"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-input
                placeholder="请输入带看内容"
                v-model="form_report_content.visit"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-input
                placeholder="请输入成交内容"
                v-model="form_report_content.deal"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-input
                placeholder="请输入结佣内容"
                v-model="form_report_content.commission"
                type="textarea"
                rows="8"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="分享配置" name="second">
          <el-form label-width="100px" :model="form" label-position="left">
            <el-form-item label="分享标题">
              <el-input
                class="input-short"
                type="text"
                v-model="form.share_title"
              ></el-input>
            </el-form-item>
            <el-form-item label="分享描述">
              <el-input
                class="input-short"
                type="text"
                v-model="form.share_description"
              ></el-input>
            </el-form-item>

            <!-- -->
            <el-form-item label="分享图片">
              <el-upload
                :headers="myHeader"
                :action="website_img"
                :on-success="handleSuccessShareImg"
                list-type="picture-card"
                :limit="1"
                :file-list="shareImgList"
                :on-preview="handlePictureCardPreviewShareImg"
                :on-remove="handleRemoveShareImg"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="shareImgVisible">
                <img width="100%" :src="logoImageUrl" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <!-- 挪到站点配置里了 /crm_customer_business_setting  获客外网-->
        <!-- <el-tab-pane label="小程序配置" name="third">
          <el-button
            :class="{ active: isclass === 1 }"
            type="primary"
            @click="authorization"
            >授权小程序</el-button
          >
          <el-popover
            style="margin-left: 20px"
            placement="top-start"
            width="1300"
            trigger="click"
            @hide="closeWximg"
          >
            <p style="text-align: center; font-size: 25px">小程序二维码</p>
            <div style="text-align: center; margin-top: 10px">
              <el-image
                :style="imgSize"
                :src="appQrcode"
                fit="fit"
                ref="img_wxs"
              ></el-image>
            </div>
            <el-button slot="reference" type="success" @click="getQrCode"
              >获取小程序二维码</el-button
            >

            <div style="width: 700px; margin-left: 200px; margin-top: 50px">
              <p>其他尺寸(单位/px)：</p>
              <el-input
                v-model="change_imgSize"
                style="float: left; width: 500px"
                placeholder="最大不得超过1280(单位/px)"
              ></el-input>
              <el-button
                type="primary"
                style="margin-top: 10px; margin-left: 18px"
                @click="changeImgsize"
                >确定</el-button
              >
              <el-button
                type="success"
                style="margin-top: 10px; float: right"
                @click="downQRcode"
                >下载</el-button
              >
            </div>
            <div style="width: 700px; margin-left: 200px; margin-top: 20px">
              <el-table :data="tableData" style="width: 100%">
                <el-table-column
                  prop="leng_side"
                  label="小程序二维码边长（cm）"
                  width="210"
                >
                </el-table-column>
                <el-table-column
                  prop="leng_pixel"
                  label="建议像素（px）"
                  width="180"
                >
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template slot-scope="scopes">
                    <el-button
                      type="primary"
                      size="mini"
                      @click="preview(scopes)"
                      >预览</el-button
                    >
                    <el-button @click="down(scopes)" size="mini" type="success">
                      下载
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-popover>
          <el-tabs type="border-card" style="margin-top: 6px">
            <el-tab-pane label="查看小程序信息">
              <el-row>
                <el-col :span="12">
                  <p style="background: #eee; padding: 10px 20px">
                    微信认证信息
                  </p>
                  <el-form label-width="100px">
                    <el-form-item label="小程序ID">
                      <el-input
                        v-model="mini_info.appid"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="账号类型">
                      <el-input
                        v-model="mini_info.account_type"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item
                      label="主体类型"
                      v-if="mini_info.principal_type"
                    >
                      <el-input
                        v-model="mini_info.principal_type"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item
                      label="主体名称"
                      v-if="mini_info.principal_name"
                    >
                      <el-input
                        v-model="mini_info.principal_name"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item
                      label="实名认证状态"
                      v-if="mini_info.realname_status"
                    >
                      <el-input
                        v-model="mini_info.realname_status"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                  </el-form>
                </el-col>
                <el-col :span="11" style="margin-left: 20px">
                  <p style="background: #eee; padding: 10px 20px">
                    微信认证信息
                  </p>
                  <el-form v-if="mini_info.wx_verify_info">
                    <el-form-item
                      label="是否是资质认证，若是，拥有微信认证相关的权限"
                    >
                      <el-input
                        v-model="mini_info.wx_verify_info.qualification_verify"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="是否名称认证">
                      <el-input
                        v-model="mini_info.wx_verify_info.naming_verify"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item
                      label="是否需要年审"
                      v-if="mini_info.wx_verify_info.qualification_verify"
                    >
                      <el-input
                        v-model="mini_info.wx_verify_info.annual_review"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item
                      label="年审开始时间"
                      v-if="mini_info.wx_verify_info.qualification_verify"
                    >
                      <el-input
                        v-model="
                          mini_info.wx_verify_info.annual_review_begin_time
                        "
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item
                      label="年审结束时间"
                      v-if="mini_info.wx_verify_info.qualification_verify"
                    >
                      <el-input
                        v-model="
                          mini_info.wx_verify_info.annual_review_end_time
                        "
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <p style="background: #eee; padding: 10px 20px">
                    功能介绍信息
                  </p>
                  <el-form v-if="mini_info.signature_info">
                    <el-form-item label="小程序签名">
                      <el-input
                        v-model="mini_info.signature_info.signature"
                        :disabled="true"
                        type="textarea"
                        :rows="4"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="小程序名称">
                      <el-input
                        v-model="mini_info.nickname"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="功能介绍已使用修改次数（本月）">
                      <el-input
                        v-model="mini_info.signature_info.modify_used_count"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="功能介绍修改次数总额度（本月）">
                      <el-input
                        v-model="mini_info.signature_info.modify_quota"
                        :disabled="true"
                      ></el-input>
                    </el-form-item> </el-form
                ></el-col>
                <el-col :span="11" style="margin-left: 20px">
                  <p style="background: #eee; padding: 10px 20px">头像信息</p>
                  <el-form v-if="mini_info.head_image_info">
                    <el-form-item label="头像">
                      <img
                        width="100px"
                        :src="mini_info.head_image_info.head_image_url"
                        alt
                      />
                    </el-form-item>
                    <el-form-item label="头像已使用修改次数（今年）">
                      <el-input
                        v-model="mini_info.head_image_info.modify_used_count"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="头像修改次数总额度（今年）">
                      <el-input
                        v-model="mini_info.head_image_info.modify_quota"
                        :disabled="true"
                      ></el-input>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="配置小程序名称">
              <div style="padding: 5px; margin: 8px 0">
                <p style="background: #eee">配置小程序名称：</p>
                <el-tag type="warning" style="margin: 10px 0"
                  >说明1：小程序发布前可改名的次数为2，当改名次数上限后仍需继续改名时需要重新发起微信认证改名。</el-tag
                ><br />
                <el-tag type="warning" style="margin: 10px 0"
                  >说明2：组织号必填：组织机构代码证或营业执照
                </el-tag>

                <div class="row div" style="align-items: center">
                  <el-input
                    style="width: 330px"
                    @change="miniNameAudit"
                    placeholder="输入小程序状态码查询审核状态"
                    v-model="mini_program_audit_id"
                  >
                    <el-tooltip
                      slot="append"
                      class="item"
                      effect="dark"
                      content="提交小程序名称成功后，请复制状态码粘贴此处，进行审核状态查询。注意：请手动保存状态码！"
                      placement="right-end"
                    >
                      <i
                        style="font-size: 30px"
                        class="el-icon-warning-outline"
                      ></i>
                    </el-tooltip>
                  </el-input>
                </div>
                <el-form
                  label-width="130px"
                  :model="mini_program_form"
                  label-position="left"
                  style="border: 1px solid #eee; padding: 0 10px"
                >
                  <el-form-item label="小程序名称：">
                    <el-input
                      style="width: 300px"
                      v-model="mini_program_form.nick_name"
                      placeholder="请输入小程序名称"
                    ></el-input>
                    <el-button type="primary" @click="detectionMini"
                      >检测小程序名称</el-button
                    >
                  </el-form-item>
                  <el-form-item label="组织号必填：">
                    <el-upload
                      :headers="myHeader"
                      :action="myMaterialPath"
                      :data="dataObj"
                      name="media"
                      :on-success="handleSuccesslicense"
                      list-type="picture-card"
                      :on-preview="handlePreviewlicense"
                      :on-remove="handleRemovelicense"
                      :limit="1"
                    >
                      <i class="el-icon-plus"></i
                    ></el-upload>
                    <el-dialog :visible.sync="licenseVisible">
                      <img width="100px" :src="licenseImageUrl" alt />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="其他证明材料一：">
                    <el-upload
                      :headers="myHeader"
                      :action="myMaterialPath"
                      :data="dataObj"
                      name="media"
                      :on-success="handleSuccessStuff1"
                      list-type="picture-card"
                      :on-preview="handlePreviewStuff1"
                      :on-remove="handleRemoveStuff1"
                      :limit="1"
                    >
                      <i class="el-icon-plus"></i
                    ></el-upload>
                    <el-dialog :visible.sync="stuff1Visible">
                      <img width="100px" :src="stuff1ImageUrl" alt />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="其他证明材料二：">
                    <el-upload
                      :headers="myHeader"
                      :action="myMaterialPath"
                      :data="dataObj"
                      name="media"
                      :on-success="handleSuccessStuff2"
                      list-type="picture-card"
                      :on-preview="handlePreviewStuff2"
                      :on-remove="handleRemoveStuff2"
                      :limit="1"
                    >
                      <i class="el-icon-plus"></i
                    ></el-upload>
                    <el-dialog :visible.sync="stuff2Visible">
                      <img width="100px" :src="stuff2ImageUrl" alt />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="其他证明材料三：">
                    <el-upload
                      :headers="myHeader"
                      :action="myMaterialPath"
                      :data="dataObj"
                      name="media"
                      :on-success="handleSuccessStuff3"
                      list-type="picture-card"
                      :on-preview="handlePreviewStuff3"
                      :on-remove="handleRemoveStuff3"
                      :limit="1"
                    >
                      <i class="el-icon-plus"></i
                    ></el-upload>
                    <el-dialog :visible.sync="stuff3Visible">
                      <img width="100px" :src="stuff3ImageUrl" alt />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="其他证明材料四：">
                    <el-upload
                      :headers="myHeader"
                      :action="myMaterialPath"
                      :data="dataObj"
                      name="media"
                      :on-success="handleSuccessStuff4"
                      list-type="picture-card"
                      :on-preview="handlePreviewStuff4"
                      :on-remove="handleRemoveStuff4"
                      :limit="1"
                    >
                      <i class="el-icon-plus"></i
                    ></el-upload>
                    <el-dialog :visible.sync="stuff4Visible">
                      <img width="100px" :src="stuff4ImageUrl" alt />
                    </el-dialog>
                  </el-form-item>
                  <el-form-item label="其他证明材料五：">
                    <el-upload
                      :headers="myHeader"
                      :action="myMaterialPath"
                      :data="dataObj"
                      name="media"
                      :on-success="handleSuccessStuff5"
                      list-type="picture-card"
                      :on-preview="handlePreviewStuff5"
                      :on-remove="handleRemoveStuff5"
                      :limit="1"
                    >
                      <i class="el-icon-plus"></i
                    ></el-upload>
                    <el-dialog :visible.sync="stuff5Visible">
                      <img width="100px" :src="stuff5ImageUrl" alt />
                    </el-dialog>
                  </el-form-item>
                </el-form>
                <div class="div row btn-box" style="margin: 4px 0">
                  <el-button type="primary" @click="submitMini">提交</el-button>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="设置小程序服务器域名">
              <div style="padding: 5px; margin: 8px 0" v-if="!categories">
                <p style="background: #eee">设置小程序服务器域名：</p>
              </div>
              <el-form style="100%" label-width="200px">
                <el-form-item label="操作类型：">
                  <el-radio v-model="domain.action" label="add">添加</el-radio>
                  <el-radio v-model="domain.action" label="set" disabled
                    >覆盖</el-radio
                  >
                  <el-radio v-model="domain.action" label="delete" disabled
                    >删除</el-radio
                  >
                  <el-radio v-model="domain.action" label="get" disabled
                    >获取</el-radio
                  >
                </el-form-item>
                <el-form-item label="request 合法域名：">
                  <el-input
                    type="textarea"
                    disabled
                    :rows="5"
                    style="width: 300px"
                    v-model="domain.requestdomain"
                  ></el-input>
                </el-form-item>
                <el-form-item label="socket 合法域名：">
                  <el-input
                    type="textarea"
                    :rows="5"
                    disabled
                    style="width: 300px"
                    v-model="domain.wsrequestdomain"
                  ></el-input>
                </el-form-item>
                <el-form-item label="uploadFile 合法域名：">
                  <el-input
                    type="textarea"
                    :rows="5"
                    disabled
                    style="width: 300px"
                    v-model="domain.uploaddomain"
                  ></el-input>
                </el-form-item>
                <el-form-item label="downloadFile 合法域名：">
                  <el-input
                    type="textarea"
                    :rows="5"
                    style="width: 300px"
                    disabled
                    v-model="domain.downloaddomain"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button @click="createDomain" type="primary"
                    >提交</el-button
                  >
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="设置小程序业务域名">
              <div style="padding: 5px; margin: 8px 0" v-if="!categories">
                <p style="background: #eee">设置小程序业务域名：</p>
              </div>
              <el-form label-width="200px">
                <el-form-item label="操作类型：">
                  <el-radio v-model="webview_domain.action" label="add"
                    >添加</el-radio
                  >
                  <el-radio v-model="webview_domain.action" label="set"
                    >覆盖</el-radio
                  >
                  <el-radio
                    v-model="webview_domain.action"
                    disabled
                    label="delete"
                    >删除</el-radio
                  >
                  <el-radio v-model="webview_domain.action" disabled label="get"
                    >获取</el-radio
                  >
                </el-form-item>
                <el-form-item label="小程序业务域名：">
                  <el-input
                    style="width: 300px"
                    type="textarea"
                    :rows="5"
                    v-model="webview_domain.webviewdomain"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button @click="createWebviewDomain" type="primary"
                    >提交</el-button
                  ></el-form-item
                >
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="配置小程序头像">
              <div style="padding: 5px; margin: 8px 0">
                <p style="background: #eee">配置小程序头像：</p>
                <el-tag type="warning" style="margin: 10px 0"
                  >小程序头像一年可修改5次</el-tag
                >
                <div class="div row">
                  <el-upload
                    style="margin: 8px 0"
                    :headers="myHeader"
                    name="media"
                    :action="myMaterialPath"
                    :data="dataObj"
                    :on-success="handleSuccessnews"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreviewnews"
                    :on-remove="handleRemovenews"
                  >
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <el-dialog :visible.sync="newsVisible">
                    <img width="100px" :src="newsImageUrl" alt />
                  </el-dialog>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="修改小程序功能介绍">
              <div style="padding: 5px; margin: 8px 0">
                <p style="background: #eee">修改小程序功能介绍：</p>
                <div>
                  <el-tag type="warning" style="margin: 10px 0">
                    一个月中可以修改5次功能介绍，请注意修改次数
                  </el-tag>
                </div>
                <div>
                  <el-tag type="warning" style="margin-bottom: 10px">
                    错误码：53201（功能介绍内容命中黑名单关键字）53200（本月功能介绍修改次数已用完）
                  </el-tag>
                </div>
                <el-input
                  style="margin: 8px 0"
                  type="textarea"
                  rows="8"
                  v-model="signature"
                  placeholder="请输入小程序功能介绍"
                ></el-input>
                <div class="div row btn-box" style="margin: 4px 0">
                  <el-button type="primary" @click="submitSignature"
                    >提交</el-button
                  >
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="修改小程序类目">
              <div style="padding: 5px; margin: 8px 0" v-if="!categories">
                <p style="background: #eee">修改小程序类目：</p>
                <el-button type="primary" @click="addCategories"
                  >点击生成类目</el-button
                >
              </div>
              <div style="padding: 5px; margin: 8px 0" v-else>
                <p style="background: #eee">修改小程序类目：</p>
                <p>
                  更改周期内可以添加的类目次数：{{
                    categories.limit
                  }}，在这个周期内，还可以添加类目的次数：{{
                    categories.quota
                  }}，最多可以设置类目数量：{{ categories.category_limit }}
                </p>
                <p v-if="categories.first">
                  <i>已设置类目：</i>
                  <i
                    >序号：{{ categories.first }}，名称：{{
                      categories.first_name
                    }}</i
                  >
                </p>
                <p categories.second>
                  <i>已设置类目：</i>
                  <i
                    >序号：{{ categories.second }}，名称：{{
                      categories.second_name
                    }}</i
                  >
                </p>
                <p style="background: #eee" v-if="categories.audit_reason">
                  {{ categories.audit_reason }}
                </p>
                <p v-else style="background: #eee">
                  审核状态：{{
                    categories.audit_status === 1
                      ? "审核中"
                      : categories.audit_status === 2
                      ? "审核不通过"
                      : "审核通过"
                  }}
                  <i></i>
                </p>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane> -->
        <el-tab-pane label="微信公众号授权" name="authorization">
          <div class="label" v-if="wx_auth">已授权</div>
          <div class="label red" v-else @click="AuthorizationGX">未授权</div>
        </el-tab-pane>
        <el-tab-pane label="楼盘类型" name="fifth">
          <el-form label-width="100px" :model="form" label-position="left">
            <el-form-item label="楼盘类型显示">
              <el-radio-group
                @change="changeBuildLabels"
                v-model="form.build_category_show_category"
              >
                <el-radio-button
                  v-for="(item, index) in build_category_show_category_list"
                  :key="index"
                  :label="item.value"
                  >{{ item.description }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item>
          </el-form>
          <el-button
            v-if="form.build_category_show_category == 1"
            type="primary"
            @click="createBuild"
            style="margin-bottom: 10px"
            >添加自定义</el-button
          >
          <el-tooltip
            style="margin-bottom: 10px"
            class="item"
            placement="right"
          >
            <div slot="content">
              注意：<br />
              1.如果楼盘已经使用了系统默认楼盘类型，为了不影响原有数据的显示，请复制系统默认楼盘分类后再修改，注意：楼盘数据冲突请不要修改复制内容的
              ‘标识’ ！<br />
              2.
              未使用过的楼盘分类的标识可以自定义设置，但不要重复！建议从零开始依次增加。<br />
              3. 自定楼盘分类删除后，已设置的楼盘不再显示此分类
            </div>
            <el-button>小提示</el-button>
          </el-tooltip>
          <myTable
            :table-list="build_category_list"
            :header="build_category_list_header"
          ></myTable>
          <el-footer v-if="form.build_category_show_category == 1">
            <!-- 分页 -->
            <div class="pagination-box">
              <myPagination
                :total="params.total"
                :pagesize="params.pagesize"
                :currentPage="params.currentPage"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
              ></myPagination>
            </div>
          </el-footer>
        </el-tab-pane>
        <el-tab-pane label="敏感词管理" name="sixth">
          <el-button
            @click="createSensitieWords"
            type="primary"
            style="margin-bottom: 10px"
            >创建</el-button
          >
          <myTable
            :table-list="sensitiveWords_list"
            :header="sensitiveWords_list_header"
          ></myTable>
          <el-footer>
            <!-- 分页 -->
            <div class="pagination-box">
              <myPagination
                :total="params.total"
                :pagesize="params.pagesize"
                :currentPage="params.currentPage"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
              ></myPagination>
            </div>
          </el-footer>
        </el-tab-pane>
        <el-tab-pane
          label="贷款利率"
          name="seventh"
          v-if="
            form.website_mode_category === 1 || form.website_mode_category === 2
          "
        >
          <el-form :model="loan_form" label-width="200px">
            <el-form-item label="商贷利率">
              <el-input
                style="width: 200px"
                step="0.1"
                type="number"
                min="0"
                v-model="loan_form.provident_fund_rate"
                placeholder="请输入商贷利率"
              ></el-input>
            </el-form-item>
            <el-form-item label="公积金贷款利率">
              <el-input
                style="width: 200px"
                step="0.1"
                type="number"
                min="0"
                v-model="loan_form.commercial_loan_rate"
                placeholder="请输入公积金贷款利率"
              ></el-input>
            </el-form-item>
            <el-form-item label="LPR(一年以上利率)">
              <el-input
                style="width: 200px"
                step="0.1"
                type="number"
                min="0"
                v-model="loan_form.lpr_one_more_year_rate"
                placeholder="请输入LPR(一年以上利率)"
              ></el-input>
            </el-form-item>
            <el-form-item label="LPR(一年以内利率)">
              <el-input
                style="width: 200px"
                step="0.1"
                type="number"
                min="0"
                v-model="loan_form.lpr_in_a_year_rate"
                placeholder="请输入LPR(一年以内利率)"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmitLoan">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="楼盘状态" name="eighth">
          <el-button
            v-if="form.build_status_show_category == 1"
            type="primary"
            @click="createBuildStatus"
            style="margin-bottom: 10px"
            >添加自定义</el-button
          >
          <el-form label-width="100px" :model="form" label-position="left">
            <el-form-item label="楼盘状态显示">
              <el-radio-group
                @change="changeBuildStatus"
                v-model="form.build_status_show_category"
              >
                <el-radio-button
                  v-for="(item, index) in build_category_show_category_list"
                  :key="index"
                  :label="item.value"
                  >{{ item.description }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item>
          </el-form>
          <myTable
            :table-list="build_status_list"
            :header="build_status_list_header"
          ></myTable>
          <el-footer v-if="form.build_status_show_category == 1">
            <!-- 分页 -->
            <div class="pagination-box">
              <myPagination
                :total="build_status_params.total"
                :pagesize="build_status_params.pagesize"
                :currentPage="build_status_params.currentPage"
                @handleSizeChange="handleSizeChangeStatus"
                @handleCurrentChange="handleCurrentChangeStatus"
              ></myPagination>
            </div>
          </el-footer>
        </el-tab-pane>
        <el-tab-pane label="查看私有配置" name="ninth">
          <el-form :model="private_config" label-width="120px">
            <el-form-item label="分销报备：">
              <span>{{
                formMatEnable(private_config.config_support_reported)
              }}</span>
            </el-form-item>
            <el-form-item label="即时通讯：">
              <span>{{ formMatEnable(private_config.config_support_im) }}</span>
            </el-form-item>
            <el-form-item label="在线直播：">
              <span>{{
                formMatEnable(private_config.config_support_online_live)
              }}</span>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          label="买房交流群"
          name="tenth"
          v-if="form.website_mode_category == 2"
        >
          <el-form label-width="100px" :model="form" label-position="left">
            <el-form-item label="交流群客服微信">
              <el-input
                class="input-short"
                type="text"
                v-model="form.estate_purchase_service_wx"
              ></el-input>
            </el-form-item>
            <el-form-item label="交流群微信二维码">
              <el-upload
                :headers="myHeader"
                :action="website_img"
                :on-success="handleSuccessWXImg"
                list-type="picture-card"
                :limit="1"
                :file-list="WXImgList"
                :on-preview="handlePictureCardPreviewWXImg"
                :on-remove="handleRemoveWXImg"
              >
                <img
                  height="146px"
                  width="146px"
                  v-if="form.estate_purchase_wx_group_qrcode"
                  :src="form.estate_purchase_wx_group_qrcode"
                  alt
                />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="WXImgVisible">
                <img width="100%" :src="WXImageUrlBuild" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-form
          v-if="
            this.activeName === 'first' ||
            this.activeName === 'second' ||
            this.activeName === 'fourth' ||
            this.activeName === 'fifth' ||
            this.activeName === 'eighth' ||
            this.activeName === 'tenth'
          "
        >
          <el-form-item>
            <el-button
              v-loading="is_button_loading"
              :disabled="is_button_loading"
              type="primary"
              @click="onSubmit"
              >保存</el-button
            >
          </el-form-item>
        </el-form>
        <!-- 地图容器 -->
        <el-dialog title="选择位置" :visible.sync="dialogVisibleMap">
          <div>
            <el-input v-model="build_address" style="width: auto"></el-input>
            <el-button type="primary" @click="getAddressKeyword"
              >搜索</el-button
            >
          </div>
          <div slot="footer">
            <div
              id="container"
              style="width: auto; height: 500px; margin: 20px 0"
            ></div>
            <div class="list">
              <div
                class="list-item"
                v-for="(item, index) in nearPointList"
                :key="index"
                @click="selectAddress(item, index)"
              >
                <div class="flex-1">
                  <div class="title">{{ item.name }}</div>
                  <div class="content">{{ item.address }}</div>
                </div>
                <div v-if="index == selectTenIndex">
                  <i class="el-icon-check"></i>
                </div>
                <div v-else>
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
            <span class="dialog-footer">
              <el-button @click="dialogVisibleMap = false">取 消</el-button>
              <el-button type="primary" @click="subMit">确 定</el-button>
            </span>
          </div>
        </el-dialog>
        <!-- 创建图标 -->
        <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogBuild">
          <el-form :model="form_create" label-width="100px">
            <el-form-item label="类型名称">
              <el-input
                v-model="form_create.name"
                placeholder="请输入类型名称"
                maxlength="6"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="首页展示">
              <el-radio-group v-model="form_create.home_display">
                <el-radio-button
                  v-for="item in enable"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.desc }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item> -->
            <el-form-item label="排序">
              <el-input
                type="number"
                v-model="form_create.sort"
                placeholder="请输入类型名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="标识（整数）">
              <el-input
                type="number"
                v-model="form_create.value"
                placeholder="请输入标识"
                min="0"
                step="1"
                style="width: 200px"
              ></el-input>
              <span>不建议修改复制过来的标识</span>
            </el-form-item>
            <el-form-item label="上传图标">
              <el-upload
                :headers="myHeader"
                :action="build_labels_category"
                :on-success="handleSuccessBuildIcon"
                list-type="picture-card"
                :on-preview="previewBuildIcon"
                :on-remove="removeBuildIcon"
              >
                <img
                  height="146px"
                  width="146px"
                  v-if="form_create.icon"
                  :src="form_create.icon"
                  alt
                />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="buildIconVisible">
                <img width="100%" :src="buildIcon" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item>
              <el-button @click="onSubmitBuildType" type="primary"
                >提交</el-button
              >
            </el-form-item>
          </el-form>
        </el-dialog>
        <!-- 敏感词 -->
        <el-dialog
          :title="sensitiveWordsTitleMap[dialogSensitiveWordsTitle]"
          :visible.sync="dialogSensitiveWords"
          @close="closeSensitive"
        >
          <el-form :model="sensitiveWords_form_create" label-width="100px">
            <el-form-item label="名称">
              <el-input
                v-model="sensitiveWords_form_create.name"
                placeholder="请输入名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="是否开启">
              <el-radio-group v-model="sensitiveWords_form_create.enable">
                <el-radio-button
                  v-for="item in enable"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.desc }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="分类">
              <el-checkbox-group
                v-model="sensitiveWords_form_create.scene_category"
              >
                <el-checkbox
                  v-for="(item, index) in sensitiveWords_type_list"
                  :key="index"
                  :label="item.value"
                  >{{ item.description }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="敏感词">
              <el-input
                type="textarea"
                rows="4"
                v-model="sensitiveWords_form_create.words"
                placeholder="敏感词请以英文‘,’分割（测试1,测试2）"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onCreateSensitiveWords"
                >提交</el-button
              >
            </el-form-item>
          </el-form>
        </el-dialog>
      </el-tabs>
      <!-- 楼盘状态 -->
      <el-dialog
        :title="titleMapStatus[dialogTitleStatus]"
        :visible.sync="dialogBuildStatus"
      >
        <el-form :model="form_status_create" label-width="100px">
          <el-form-item label="名称">
            <el-input
              v-model="form_status_create.name"
              placeholder="请输入名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="排序">
            <el-input
              type="number"
              v-model="form_status_create.sort"
              placeholder="请输入类型名称"
              min="0"
              step="1"
            ></el-input>
          </el-form-item>
          <el-form-item label="标识（整数）">
            <el-input
              type="number"
              v-model="form_status_create.value"
              placeholder="请输入标识"
              min="0"
              step="1"
            ></el-input>
            <span>不建议修改复制过来的标识</span>
          </el-form-item>
          <el-form-item>
            <el-button @click="onSubmitBuildStatus" type="primary"
              >提交</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <a href=""></a>
    </el-main>
  </el-container>
</template>

<script>
/* eslint-disable */
import config from "@/utils/config";
import tipsList from "../../components/tips_list";
import myPagination from "../../components/my_pagination";
import myTable from "../../components/my_table";
export default {
  name: "website_update",
  components: {
    tipsList,
    myPagination,
    myTable,
  },
  data() {
    return {
      //二维码尺寸参照表
      tableData: [
        {
          leng_side: "8cm",
          leng_pixel: "258",
        },
        {
          leng_side: "12cm",
          leng_pixel: "344",
        },
        {
          leng_side: "15cm",
          leng_pixel: "430",
        },
        {
          leng_side: "30cm",
          leng_pixel: "860",
        },
        {
          leng_side: "50cm",
          leng_pixel: "1280",
        },
      ],
      //小程序尺寸
      imgSize: "width:180px;height:180px",
      change_imgSize: "",
      form: {
        // 开启分销报备开关
        share_report_entrance: 0,
        // name: "",
        logo: "",
        withdraw_fee: 0.0,
        wx_pub_qr_code: "",
        wx_mini_program_qr_code: "",
        default_deal_big_news: "",
        invite_description: "",
        map_lat: "",
        map_long: "",
        share_img: "",
        login_display_brokerage_rule: "",
        distribution_brokerage_type: 0,
        distribution_brokerage_value: "",
        fill_customer_phone_category: "4",
        customer_reported_user_category: "1",
        reported_rule: "",
        reported_rule_category: "1",
        reported_created_at_format_category: "Y-m-d H:i:s",
        // 强制客户设置姓名开关：
        client_user_force_setting_name: true,
        // 开启实名认证开关
        open_real_name: true,
        // 楼盘是否显示佣金规则
        brokerage_rule_category: 1,
        visit_time_format_category: "1",
        reported_not_audit_invalid_category: "1",
        build_category_show_category: "",
        reported_go_with: "1",
        force_login: 0,
        estate_purchase_service_wx: "", //买房交流群号
        estate_purchase_wx_group_qrcode: "", // 买房交流群二维码
        theme: "", // 小程序主题
        max_select_project_total: "5",
      },
      // 报备规则内容
      form_report_content: {
        reported: "",
        visit: "",
        deal: "",
        commission: "",
      },
      //   logo图片
      logoVisible: false,
      logoImageUrl: "",
      //   公众号图片
      publicVisible: false,
      publicImageUrl: "",
      // 小程序二维码
      wxVisible: false,
      shareImgVisible: false,
      wxImageUrl: "",
      login_display: [
        { value: 1, desc: "所有状态显示" },
        { value: 0, desc: "登录状态显示" },
      ],
      distribution_type: [
        { value: 0, desc: "关闭分销" },
        { value: 1, desc: "按比例分销" },
        { value: 2, desc: "按固定分销" },
      ],
      lat: "",
      lng: "",
      dialogVisibleMap: false,
      build_address: "",
      // map_lng_lat: "",
      nearPointList: [],
      markersArray: [],
      selectTenIndex: 0,
      activeName: "first",
      shareImgList: [],
      shareImg: [],
      step: 0,
      max_num: 1,
      // 小程序授权
      dialogMiniprogram: false,
      mini_program_form: {
        nick_name: "",
        license: "", //组织号必填
        naming_other_stuff_1: "", //其他证明材料：mediaid
        naming_other_stuff_2: "",
        naming_other_stuff_3: "",
        naming_other_stuff_4: "",
        naming_other_stuff_5: "",
      },
      newsVisible: false,
      newsImageUrl: "",
      dataObj: {
        type: "image",
        access_token_type: 1,
      },
      media_id: "",
      signature: "",
      switch_value: 0,
      categories: "",
      domain: {
        action: "add",
        requestdomain: "https://yun.tfcs.cn,https://apis.map.qq.com",
        wsrequestdomain: "https://yun.tfcs.cn, wss://imcloud.tengfangyun.com",
        uploaddomain: "https://yun.tfcs.cn",
        downloaddomain: "https://yun.tfcs.cn,https://img.tfcs.cn",
      },
      webview_domain: {
        action: "add",
        webviewdomain: "https://yun.tfcs.cn",
      },
      // 处理服务器域名
      requestdomain: "",
      wsrequestdomain: "",
      uploaddomain: "",
      downloaddomain: "",
      // 业务域名
      webviewdomain: "",
      mini_info: "",
      isclass: 0,
      tipsList: [
        "提现手续费率：填写则在移动端中显示费率，不填写则不显示",
        "分享配置：配置系统分享显示内容",
        "分销配置：选择分销比例内容大于等于0小于等于1，固定分销内容大于等于0，小于999999.99的值",
        "小程序授权:如未授权小程序，请点击小程序配置->授权小程序按钮",
        "成交喜报：如果没有成交信息，可以设置该内容，将自动在移动端我的页面->成交头条轮播",
        "手机号补全：经纪人报备时，对比手机号时对有问题的隐号号码，添加补全手机号功能",
        "首页楼盘类型展示：默认展示系统设置,修改完成后别忘了点击保存按钮哦（建议上传尺寸60*60）",
      ],
      website_img: `/api/common/file/upload/admin?category=${config.WEBSITE_IMG}`,
      build_labels_category: `/api/common/file/upload/admin?category=${config.BUILD_LABELS_CATEGORY}`,
      fill_customer_phone_category_list: [],
      customer_reported_user_category_list: [],
      reported_rule_category_list: [],
      brokerage_rule_category_list: [],
      // 到访时间格式
      visit_time_format_category_list: [],
      // 报备未审核无效类型
      reported_not_audit_invalid_category_list: [],
      reported_created_at_format_category_list: [], // 报备日期格式数组
      build_category_show_category_list: [],
      show_build_category: false,
      dialogBuild: false,
      form_create: {},
      titleMap: {
        addData: "添加数据",
        updataData: "修改数据",
      },
      dialogTitle: "",
      enable: [
        { value: 0, desc: "关闭" },
        { value: 1, desc: "开启" },
      ],
      buildIconVisible: false,
      buildIcon: "",
      build_category_list: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      build_status_params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      // 敏感词
      sensitiveWords_list: [],
      sensitiveWordsTitleMap: {
        addData: "添加数据",
        updataData: "修改数据",
      },
      dialogSensitiveWordsTitle: "",
      dialogSensitiveWords: false,
      sensitiveWords_form_create: {},
      sensitiveWords_type_list: [],
      loan_form: {}, //贷款
      build_status_list: [],
      form_status_create: {},
      titleMapStatus: {
        addData: "添加数据",
        updataData: "修改数据",
      },
      dialogTitleStatus: "",
      dialogBuildStatus: false,
      reported_go_with_list: [],
      appQrcode: "",
      private_config: {}, // 小程序私有配置
      website_id: localStorage.getItem("website_id"),
      WXImgList: [],
      WXImgVisible: false,
      WXImageUrlBuild: "",
      mini_program_audit_id: "",
      licenseVisible: false,
      licenseImageUrl: "",
      stuff1Visible: false,
      stuff1ImageUrl: "",
      stuff2Visible: false,
      stuff2ImageUrl: "",
      stuff3Visible: false,
      stuff3ImageUrl: "",
      stuff4Visible: false,
      stuff4ImageUrl: "",
      stuff5Visible: false,
      stuff5ImageUrl: "",
      wx_auth: false, // 微信公众号授权
      build_category_list_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "sort", label: "排序" },
        { prop: "value", label: "标识" },
        { prop: "name", label: "名称" },
        // {
        //   label: "首页展示",
        //   render: (h, data) => {
        //     return (
        //       <el-tag type="primary">
        //         {data.row.home_display === 1 ? "是" : "否"}
        //       </el-tag>
        //     );
        //   },
        // },
        {
          label: "图标",
          render: (h, data) => {
            return <img width="50" src={data.row.icon} />;
          },
        },
        { prop: "created_at", label: "创建日期" },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.form.build_category_show_category == 1 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.changeData(data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.form.build_category_show_category == 0 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.copyData(data.row);
                    }}
                  >
                    复制
                  </el-button>
                ) : (
                  ""
                )}
                {this.form.build_status_show_category == 1 ? (
                  <el-button
                    type="danger"
                    size="mini"
                    onClick={() => {
                      this.deleteData(data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      sensitiveWords_list_header: [
        { prop: "id", label: "ID", width: "100" },
        {
          label: "是否开启",
          render: (h, data) => {
            return <p>{data.row.enable === 1 ? "开启" : "关闭"}</p>;
          },
        },
        { prop: "name", label: "名称" },
        {
          label: "场景名称",
          render: (h, data) => {
            return (
              <p>
                {data.row.scene_category_formatter.map((item) => {
                  return item;
                })}
              </p>
            );
          },
        },
        { prop: "created_at", label: "创建日期" },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  type="success"
                  size="mini"
                  onClick={() => {
                    this.updateSensitive(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  onClick={() => {
                    this.deleteSensitive(data.row);
                  }}
                  size="mini"
                  type="danger"
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      build_status_list_header: [
        { prop: "name", label: "名称" },
        { prop: "sort", label: "排序" },
        { prop: "value", label: "标识" },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return (
              <div>
                {this.form.build_status_show_category == 1 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.changeDataStatus(data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.form.build_status_show_category == 0 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.copyDataStatus(data.row);
                    }}
                  >
                    复制
                  </el-button>
                ) : (
                  ""
                )}
                {this.form.build_status_show_category == 1 ? (
                  <el-button
                    type="danger"
                    size="mini"
                    onClick={() => {
                      this.deleteDataStatus(data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_button_loading: false,
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
    map_lng_lat() {
      if (this.lat && this.lng) {
        return this.lat + "," + this.lng;
      } else {
        return "";
      }
    },
    myMaterialPath() {
      return "https://yun.tfcs.cn/api/admin/wx_open/public/material/new/temp";
    },
  },
  created() {
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "FILL_CUSTOMER_PHONE_CATEGORY":
            this.fill_customer_phone_category_list = item.childs;
            break;
          case "CUSTOMER_REPORTED_USER_CATEGORY":
            this.customer_reported_user_category_list = item.childs;
            break;
          case "REPORTED_RULE_CATEGORY":
            this.reported_rule_category_list = item.childs;
            break;
          case "BROKERAGE_RULE_CATEGORY":
            this.brokerage_rule_category_list = item.childs;
            break;
          case "VISIT_TIME_FORMAT_CATEGORY":
            this.visit_time_format_category_list = item.childs;
            break;
          case "REPORTED_NOT_AUDIT_INVALID_CATEGORY":
            this.reported_not_audit_invalid_category_list = item.childs;
            break;
          case "REPORTED_CREATED_AT_FORMAT_CATEGORY":
            this.reported_created_at_format_category_list = item.childs;
            break;
          case "BUILD_CATEGORY_SHOW_CATEGORY":
            this.build_category_show_category_list = item.childs;
            break;
          case "SENSITIVE_WORDS_SCENE_CATEGORY":
            this.sensitiveWords_type_list = item.childs;
            break;
          case "REPORTED_GO_WITH_CATEGORY":
            this.reported_go_with_list = item.childs;
            break;
        }
      });
    });
    this.getWebsiteinfor();
  },
  mounted() {
    this.queryWx();
    // this.requestdomain = this.domain.requestdomain.toLocaleString();
    // this.wsrequestdomain = this.domain.wsrequestdomain.toLocaleString();
    // this.uploaddomain = this.domain.uploaddomain.toLocaleString();
    // this.downloaddomain = this.domain.downloaddomain.toLocaleString();
    // this.webviewdomain = this.webview_domain.webviewdomain.toLocaleString();
  },
  methods: {
    //下载二维码
    downQRcode() {
      console.log(this.change_imgSize);
      if (this.change_imgSize === "") {
        this.$message({
          message: "请输入下载尺寸",
          type: "warning",
        });
      } else {
        var path = "";
        if (this.form.website_mode_category == 0) {
          path = "/index/index";
        }
        if (this.form.website_mode_category == 1) {
          path = "/only_build/pages/index/index";
        }
        if (this.form.website_mode_category == 2) {
          path = "/weifangchan/pages/index/index";
        }
        this.$http
          .getAppQrcode({ path: path, width: this.change_imgSize * 1 })
          .then((res) => {
            if (res.status === 200) {
              this.appQrcode = `data: image/jpeg;base64,${btoa(
                new Uint8Array(res.data).reduce(
                  (data, byte) => data + String.fromCharCode(byte),
                  ""
                )
              )}`;
              let a = document.createElement("a");
              a.download = `小程序二维码（${this.change_imgSize}px）`;
              a.href = this.appQrcode;
              a.click();
            }
          });
      }
    },
    closeWximg() {
      this.imgSize = "width:180px;height:180px";
      this.change_imgSize = "";
    },
    //修改小程序二维码尺寸
    changeImgsize() {
      if (this.change_imgSize * 1 > 1280) {
        this.change_imgSize = 1280;
      }
      this.imgSize = `width:${this.change_imgSize}px;height:${this.change_imgSize}px`;
      var path = "";
      if (this.form.website_mode_category == 0) {
        path = "/index/index";
      }
      if (this.form.website_mode_category == 1) {
        path = "/only_build/pages/index/index";
      }
      if (this.form.website_mode_category == 2) {
        path = "/weifangchan/pages/index/index";
      }
      this.$http
        .getAppQrcode({ path: path, width: this.change_imgSize })
        .then((res) => {
          if (res.status === 200) {
            this.appQrcode = `data: image/jpeg;base64,${btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            )}`;
          }
        });
    },
    //不同尺寸二维码图片预览
    preview(e) {
      this.imgSize = `width:${this.tableData[e.$index].leng_pixel *
        1}px;height:${this.tableData[e.$index].leng_pixel * 1}px`;
      var path = "";
      if (this.form.website_mode_category == 0) {
        path = "/index/index";
      }
      if (this.form.website_mode_category == 1) {
        path = "/only_build/pages/index/index";
      }
      if (this.form.website_mode_category == 2) {
        path = "/weifangchan/pages/index/index";
      }
      this.$http
        .getAppQrcode({
          path: path,
          width: this.tableData[e.$index].leng_pixel * 1,
        })
        .then((res) => {
          if (res.status === 200) {
            this.appQrcode = `data: image/jpeg;base64,${btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            )}`;
          }
        });
    },
    //下载不同尺寸的小程序二维码
    down(e) {
      var path = "";
      if (this.form.website_mode_category == 0) {
        path = "/index/index";
      }
      if (this.form.website_mode_category == 1) {
        path = "/only_build/pages/index/index";
      }
      if (this.form.website_mode_category == 2) {
        path = "/weifangchan/pages/index/index";
      }
      this.$http
        .getAppQrcode({
          path: path,
          width: this.tableData[e.$index].leng_pixel * 1,
        })
        .then((res) => {
          if (res.status === 200) {
            this.appQrcode = `data: image/jpeg;base64,${btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            )}`;
            let a = document.createElement("a");
            a.download = `小程序二维码（${this.tableData[e.$index].leng_side}/${this.tableData[e.$index].leng_pixel
              }px）`;
            a.href = this.appQrcode;
            a.click();
          }
        });
    },
    // 获取楼盘类型列表
    getBuildCategory() {
      this.$http.getBuildSystem({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.build_category_list = res.data;
        }
      });
    },
    // 获取楼盘状态列表
    getBuildStatus() {
      this.$http.getBuildStatus().then((res) => {
        if (res.status === 200) {
          this.build_status_list = res.data;
        }
      });
    },
    // 获取小程序私有配置
    getMiniProgramPrivateConfig() {
      this.$http.getMiniProgramPrivateConfig(this.website_id).then((res) => {
        if (res.status === 200) {
          this.private_config = res.data;
        }
      });
    },
    // 获取敏感词数据列表
    getSensitiveWords() {
      this.$http.getSensitiveWords({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.sensitiveWords_list = res.data.data;
          // 添加新字段存入字典中匹配的数据信息
          this.sensitiveWords_list.map((item, index) => {
            this.sensitiveWords_list[index].scene_category_formatter = [];
            this.sensitiveWords_type_list.map((item2) => {
              if (item.scene_category.includes(item2.value)) {
                return this.sensitiveWords_list[
                  index
                ].scene_category_formatter.push(item2.description);
              }
            });
          });
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.build_category_list.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getBuildCategory();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getBuildCategory();
    },
    getPageDataStatus() {
      let start =
        (this.build_status_params.currentPage - 1) *
        this.build_status_params.pagesize;
      let end = start + this.build_status_params.pagesize;
      this.schArr = this.build_status_list.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChangeStatus(val) {
      this.build_status_params.pagesize = val;
      this.getPageDataStatus();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChangeStatus(val) {
      this.build_status_params.currentPage = val;
      this.getPageDataStatus();
    },
    getWebsiteinfor() {
      this.$http.getWebsite(this.website_id).then((res) => {
        if (res.status === 200) {
          this.form = res.data;
          this.form.fill_customer_phone_category = res.data.fill_customer_phone_category.toString();
          this.form.customer_reported_user_category = res.data.customer_reported_user_category.toString();
          this.form.reported_rule_category = res.data.reported_rule_category.toString();
          this.form.brokerage_rule_category = res.data.brokerage_rule_category.toString();
          this.form.visit_time_format_category = res.data.visit_time_format_category.toString();
          this.form.reported_not_audit_invalid_category = res.data.reported_not_audit_invalid_category.toString();
          this.form.build_category_show_category = res.data.build_category_show_category.toString();
          this.form.reported_go_with = res.data.reported_go_with.toString();
          if (this.form.build_category_show_category == 1) {
            this.getZiBuildLabels();
          } else {
            this.getBuildCategory();
          }
          if (this.form.build_status_show_category == 1) {
            this.getZibuildStatus();
          } else {
            this.getBuildStatus();
          }
          switch (this.form.visit_time_format_category) {
            case "1":
              this.form.visit_time_format_category = "Y-m-d H:i";
              break;
            case "2":
              this.form.visit_time_format_category = "Y-m-d";
              break;
            default:
              this.form.visit_time_format_category;
              break;
          }
          this.form.force_login === 1
            ? (this.form.force_login = true)
            : (this.form.force_login = false);
          this.form.client_user_force_setting_name === 1
            ? (this.form.client_user_force_setting_name = true)
            : (this.form.client_user_force_setting_name = false);
          this.form.open_real_name === 1
            ? (this.form.open_real_name = true)
            : (this.form.open_real_name = false);
          let obj = res.data.reported_rule;
          if (obj) {
            this.form_report_content = JSON.parse(obj);
          }
          if (this.form.share_img) {
            this.shareImgList = this.form.share_img.split(",").map((img) => {
              return {
                url: img,
                name: "",
              };
            });
          }

          this.lat = this.form.map_lat;
          this.lng = this.form.map_long;
        }
      });
    },
    init() {
      var that = this;
      var myLatlng = "";
      if (that.form.map_lat && that.form.map_long) {
        myLatlng = new qq.maps.LatLng(that.form.map_lat, that.form.map_long);
      } else {
        myLatlng = new qq.maps.LatLng(26.326248944008693, 116.35539315640926);
      }
      var myOptions = {
        zoom: 16,
        center: myLatlng,
        mapTypeId: qq.maps.MapTypeId.ROADMAP,
      };
      that.map = new qq.maps.Map(
        document.getElementById("container"),
        myOptions
      );
      //获取点击后的地址
      qq.maps.event.addListener(that.map, "click", function (event) {
        //通过坐标来显示地图地址
        that.getAddCode = new qq.maps.Geocoder({
          complete: function (result) {
            that.build_address = result.detail.address;
          },
        });
        // 获取点击后的地图坐标
        that.lng = event.latLng.lng.toFixed(6);
        that.lat = event.latLng.lat.toFixed(6);
        var marker = new qq.maps.Marker({
          map: that.map,
          position: new qq.maps.LatLng(that.lat, that.lng),
        });
        that.markersArray.push(marker);
        if (that.markersArray.length > 1) {
          for (let i = 0; i < that.markersArray.length - 1; i++) {
            that.markersArray[i].setMap(null); // 清除标记
          }
        }

        that.getAddressCode();
      });
      var marker = new qq.maps.Marker({
        map: that.map,
        position: myLatlng,
      });

      that.markersArray.push(marker);
      if (that.markersArray.length > 1) {
        for (let i = 0; i < that.markersArray.length - 1; i++) {
          that.markersArray[i].setMap(null); // 清除标记
        }
      }

      //调用地址显示地图位置并设置地址
      that.getAddress = new qq.maps.Geocoder({
        complete: function (result) {
          that.map.setCenter(result.detail.location);
          that.lng = result.detail.location.lng;
          that.lat = result.detail.location.lat;
          var marker = new qq.maps.Marker({
            map: that.map,
            position: result.detail.location,
          });
        },
      });
      // 经纬度解析类回调函数
      that.geocoder = new qq.maps.Geocoder({
        complete: function (result) {
          that.nearPointList = result.detail.nearPois;
          that.map.setCenter(result.detail.location);
          let marker = new qq.maps.Marker({
            map: that.map,
            position: result.detail.location,
          });
          that.markersArray.push(marker);
          if (that.markersArray.length > 1) {
            for (let i = 0; i < that.markersArray.length - 1; i++) {
              that.markersArray[i].setMap(null); // 清除标记
            }
          }
        },
      });
      // 地址解析回调函数
      that.geocoderLocation = new qq.maps.Geocoder({
        complete: function (result) {
          // 查找附近的点
          let latLng = new qq.maps.LatLng(
            result.detail.location.lat,
            result.detail.location.lng
          );
          that.geocoder.getAddress(latLng);
        },
      });
    },
    //通过地址获得位置  搜索功能
    getAddressKeyword() {
      //通过getLocation();方法获取位置信息值
      this.geocoderLocation.getLocation(this.build_address);
      // this.getAddress.getLocation(this.build_address);
      // 调用自带的接口;
    },
    // 通过坐标获得地址
    getAddressCode() {
      var lat = parseFloat(this.form.map_lat);
      var lng = parseFloat(this.form.map_lng);
      var latLng = new qq.maps.LatLng(lat, lng);
      //调用获取位置方法
      this.getAddCode.getAddress(latLng);
    },
    // 获取当前定位，/市
    getTXLocation() {
      var _this = this;
      var geolocation = new qq.maps.Geolocation(
        "GVZBZ-47RCP-D6LDP-LCA5V-PUQFQ-QMB2V",
        "yunbaobei"
      );
      var options = { timeout: 8000 };
      var latitude, longitude, address;
      geolocation.getLocation(showPosition, showErr, options);
      function showPosition(position) {
        latitude = position.lat;
        longitude = position.lng;
        address = position.nation + position.province + position.city;
        _this.getShopmsg(latitude, longitude, address); //获取到经纬度后的操作
      }
      function showErr(position) { }
    },
    selectAddress(e, index) {
      this.selectTenIndex = index;
      var marker = null;
      this.map.panTo(e.latLng);
      //  var label = new qq.maps.Label({
      //       position: e.latLng,
      //       map: this.Tenmap,
      //       content:'选中位置'
      //    });
      //  label.setPosition(e.latLng)
      //   label.setVisible(true);
      var anchor = new qq.maps.Point(10, 20), //标记图像位置
        size = new qq.maps.Size(100, 100), //标记图像大小
        origin = new qq.maps.Point(0, 0),
        markerIcon = new qq.maps.MarkerImage(
          "https://tfy.tengfun.com/static/skin/tfy/build/list_map_ico.png",
          size,
          origin,
          anchor
        );
      marker = new qq.maps.Marker({
        position: e.latLng,
        map: this.map,
        icon: markerIcon,
      });
      this.markersArray.push(marker);
      if (this.markersArray.length > 1) {
        for (let i = 0; i < this.markersArray.length - 1; i++) {
          this.markersArray[i].setMap(null); // 清除标记
        }
      }
      this.map.panTo(new qq.maps.LatLng(e.latLng.lat, e.latLng.lng));
      this.lat = e.latLng.lat.toFixed(6);
      this.lng = e.latLng.lng.toFixed(6);
    },
    // 获取经纬度后的操作
    getShopmsg(lat, lng, address) {
      this.lat = this.form.map_lat = lat;
      this.lng = this.form.map_long = lng;
      this.build_address = address;
    },
    regionOne(e) {
      let roles = [];
      for (var val of this.region_list) {
        if (e === val.pid) {
          roles.push({ id: val.id, name: val.name });
        }
        this.region_list_two = roles;
      }
    },
    markMap() {
      // if (!this.form.map_lat && !this.form.map_long) {
      //   this.getTXLocation();
      // }
      setTimeout(() => {
        this.init();
        this.dialogVisibleMap = true;
      }, 400);
    },
    subMit() {
      this.form.map_lat = this.lat.toString();
      this.form.map_long = this.lng.toString();
      this.dialogVisibleMap = false;
    },
    // 系统logo
    handleRemoveLogo(file, fileList) { },
    handlePictureCardPreviewLogo(file) {
      this.logoImageUrl = file.response.url;
      this.logoVisible = true;
    },
    handleSuccessLogo(response) {
      this.form.logo = response.url;
    },
    // 分享图片
    handleRemoveShareImg(file, fileList) {
      for (let i = 0; i < this.shareImgList.length; i++) {
        if (file.url == this.shareImgList[i].url) {
          this.shareImgList.splice(i, 1);
          this.shareImg = [];
          continue;
        }
      }
    },
    handlePictureCardPreviewShareImg(file) {
      this.logoImageUrl = file.url;
      this.shareImgVisible = true;
    },
    handleSuccessShareImg(response) {
      this.shareImg.push(response.url);
      this.shareImgList = this.shareImg.map((item) => {
        return {
          url: item,
          name: "",
        };
      });
    },
    // 公众号
    handleRemovePublic(file, fileList) { },
    handlePictureCardPreviewPublic(file) {
      this.publicImageUrl = file.response.url;
      this.publicVisible = true;
    },
    handleSuccessPublic(response) {
      this.form.wx_pub_qr_code = response.url;
    },
    goBack() {
      this.$router.back();
    },
    // 小程序
    handleClick(tab) {
      if (tab.paneName === "third") {
        this.$http.queryXiaoApp().then((res) => {
          if (res.status === 200) {
            if (res.data.id === 0 && res.data.updated_at === "") {
              this.$message({
                message: "暂未授权小程序",
                type: "warning",
              });
            } else {
              this.configuration();
            }
          }
        });
      } else if (tab.paneName === "fifth") {
        this.params = {
          currentPage: 1,
          pagesize: 10,
          total: 0,
          row: 0,
        };
      } else if (tab.paneName === "sixth") {
        this.params = {
          currentPage: 1,
          pagesize: 10,
          total: 0,
          row: 0,
        };
        this.getSensitiveWords();
      } else if (tab.paneName === "seventh") {
        this.$http.getLoan(this.website_id).then((res) => {
          if (res.status === 200) {
            this.loan_form = res.data;
          }
        });
      } else if (tab.paneName === "ninth") {
        this.getMiniProgramPrivateConfig();
      }
    },
    handleRemoveWx(file, fileList) { },
    handlePictureCardPreviewWx(file) {
      this.wxImageUrl = file.response.url;
      this.wxVisible = true;
    },
    handleSuccessWx(response) {
      this.form.wx_mini_program_qr_code = response.url;
    },
    changeDistribution() {
      if (this.form.distribution_brokerage_type === 1) {
        this.step = 0.01;
        this.max_num = 1;
      } else if (this.form.distribution_brokerage_type === 2) {
        this.step = 1;
        this.max_num = 1000000;
        this.form.distribution_brokerage_value = "";
      }
    },
    onSubmit() {
      this.is_button_loading = true;
      this.form.reported_rule = JSON.stringify(this.form_report_content);
      this.form.share_img = this.shareImg.join(",");
      if (
        this.form.distribution_brokerage_type !== 0 &&
        !this.form.distribution_brokerage_value
      ) {
        this.$message({
          message: "请输入分销内容",
          type: "error",
        });
        return;
      }
      this.$http.updateWebsite(this.form).then((res) => {
        this.is_button_loading = false;
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.$router.back();
        }
      }).catch(() => {
        this.is_button_loading = false;
      })
    },

    // 小程序授权
    // 缩略图
    handleRemovenews(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreviewnews(file) {
      this.newsVisible = true;
      this.newsImageUrl = file.response.url;
    },
    handleSuccessnews(response) {
      if (response.media_id) {
        this.media_id = response.media_id;
        this.$http
          .modifyAvatar({
            head_img_media_id: this.media_id,
            x1: "0",
            y1: "0",
            x2: "1",
            y2: "1",
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "上传成功",
                type: "success",
              });
            }
          });
      }
    },
    authorization() {
      let url = encodeURIComponent(
        `https://yun.tfcs.cn/admin/?website_id=${this.website_id}#/loading?is_loading=1`
      );
      if (this.mini_info.errcode === 0) {
        this.$message({
          message: "您已授权小程序请勿重复授权",
          type: "warning",
        });
      } else {
        this.$http.authorization({ url }).then((res) => {
          if (res.status === 200) {
            window.location.href = res.data.url;
          }
        });
      }
    },
    configuration() {
      this.dialogMiniprogram = true;
      // 查询小程序是否可以被搜索
      this.$http.querySearchStatus().then((res) => {
        if (res.status === 200) {
          this.switch_value = res.data.status;
        }
      });
      // 查询小程序类目分类
      this.$http.getCategory().then((res) => {
        if (res.status === 200) {
          this.categories = res.data.categories[0];
          this.categories.limit = res.data.limit;
          this.categories.quota = res.data.quota;
          this.categories.category_limit = res.data.category_limit;
        }
      });
      // 查询小程序基本信息
      this.$http.queryMiniInfo().then((res) => {
        if (res.status === 200) {
          this.mini_info = res.data;
          // 匹配数据类型
          switch (this.mini_info.account_type) {
            case 1:
              this.mini_info.account_type = "订阅号";
              break;
            case 2:
              this.mini_info.account_type = "服务号";
              break;
            default:
              this.mini_info.account_type = "小程序";
              break;
          }
          switch (this.mini_info.principal_type) {
            case 0:
              this.mini_info.principal_type = "个人";
              break;
            case 1:
              this.mini_info.principal_type = "企业";
              break;
            case 2:
              this.mini_info.principal_type = "媒体";
              break;
            case 3:
              this.mini_info.principal_type = "政府";
              break;
            default:
              this.mini_info.principal_type = "其他组织";
              break;
          }
          switch (this.mini_info.realname_status) {
            case 1:
              this.mini_info.realname_status = "实名验证成功";
              break;
            case 2:
              this.mini_info.realname_status = "实名验证中";
              break;
            default:
              this.mini_info.realname_status = "实名验证失败";
              break;
          }
          // 时间戳转换
          this.mini_info.wx_verify_info.annual_review_begin_time = this.etLocalTime(
            this.mini_info.wx_verify_info.annual_review_begin_time
          );
          this.mini_info.wx_verify_info.annual_review_end_time = this.etLocalTime(
            this.mini_info.wx_verify_info.annual_review_end_time
          );
          // 匹配数据类型
          switch (this.mini_info.wx_verify_info.qualification_verify) {
            case true:
              this.mini_info.wx_verify_info.qualification_verify = "是";
              break;
            default:
              this.mini_info.wx_verify_info.qualification_verify = "否";
              break;
          }
          switch (this.mini_info.wx_verify_info.naming_verify) {
            case true:
              this.mini_info.wx_verify_info.naming_verify = "是";
              break;
            default:
              this.mini_info.wx_verify_info.naming_verify = "否";
              break;
          }
          switch (this.mini_info.wx_verify_info.annual_review) {
            case true:
              this.mini_info.wx_verify_info.annual_review = "是";
              break;
            default:
              this.mini_info.wx_verify_info.annual_review = "否";
              break;
          }
        } else {
          this.mini_info = "";
        }
      });
    },
    // 时间戳转换方法
    etLocalTime(nS) {
      return new Date(parseInt(nS) * 1000)
        .toLocaleString()
        .replace(/:\d{1,2}$/, " ");
    },
    submitMini() {
      if (!this.mini_program_form.nick_name) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http.submitMini(this.mini_program_form).then((res) => {
        if (res.status === 200) {
          var audit_id = res.data.audit_id;
          this.$message({
            message: "提交成功,如需查询名称审核状态请复制状态码：" + audit_id,
            type: "success",
            showClose: true,
            duration: 0,
          });
        }
      });
    },
    detectionMini() {
      if (!this.mini_program_form.nick_name) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http
        .detectionMini({
          nick_name: this.mini_program_form.nick_name,
        })
        .then((res) => {
          if (res.status === 200) {
            if (res.data.wording) {
              this.$message({
                message: res.data.wording,
                type: "warning",
                duration: 0,
                showClose: true,
              });
            } else {
              this.$message({
                message: "提交成功",
                type: "success",
              });
            }
          }
        });
    },
    submitSignature() {
      if (!this.signature) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http
        .submitSignature({
          signature: this.signature,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "上传成功",
              type: "success",
            });
          }
        });
    },
    changeSwitch(e) {
      if (e === 1) {
        this.$http.searchStatus({ status: e }).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "小程序不可被搜索",
              type: "error",
            });
          }
        });
      } else {
        this.$http.searchStatus({ status: e }).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "小程序可被搜索",
              type: "success",
            });
          }
        });
      }
    },
    addCategories() {
      this.$http
        .addCategories({
          first: 135,
          second: 142,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "设置成功",
              type: "success",
            });
            this.$http.getCategory().then((res) => {
              if (res.status === 200) {
                this.categories = res.data.categories[0];
                this.categories.limit = res.data.limit;
                this.categories.quota = res.data.quota;
                this.categories.category_limit = res.data.category_limit;
              }
            });
          }
        });
    },
    createDomain() {
      let form = Object.assign({}, this.domain);
      form.requestdomain = form.requestdomain.split(",");
      form.wsrequestdomain = form.wsrequestdomain.split(",");
      form.uploaddomain = form.uploaddomain.split(",");
      form.downloaddomain = form.downloaddomain.split(",");
      this.$http.createDomain(form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
        }
      });
    },
    createWebviewDomain() {
      let form = Object.assign({}, this.webview_domain);
      form.webviewdomain = form.webviewdomain.split(",");
      this.$http.createWebviewDomain(form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
        }
      });
    },
    createBuild() {
      this.form_create = {
        home_display: 1,
        sort: 0,
      };
      this.dialogTitle = "addData";
      this.dialogBuild = true;
    },
    handleSuccessBuildIcon(response, file, fileList) {
      this.form_create.icon = response.url;
    },
    previewBuildIcon(file) {
      this.buildIcon = file.response.url;
      this.buildIconVisible = true;
    },
    removeBuildIcon() { },
    onSubmitBuildType() {
      if (this.dialogTitle === "addData") {
        this.$http.createBuildLabel(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getZiBuildLabels();
            this.dialogBuild = false;
          }
        });
      } else {
        this.$http.updateBuildLabel(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getZiBuildLabels();
            this.dialogBuild = false;
          }
        });
      }
    },
    changeData(row) {
      this.dialogTitle = "updateData";
      this.dialogBuild = true;
      this.form_create = row;
    },
    deleteData(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteBuildLabel(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getZiBuildLabels();
            }
          });
        })
        .catch(() => { });
    },
    getZiBuildLabels() {
      this.$http.gteBuildCategory({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.build_category_list = res.data.data;
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    changeBuildLabels(e) {
      if (e == 1) {
        this.getZiBuildLabels();
      } else {
        this.getBuildCategory();
      }
    },
    // 获取自定义楼盘状态列表
    getZibuildStatus() {
      this.$http.getZiBuildStatus({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.build_status_list = res.data.data;
          this.build_status_params = {
            currentPage: res.data.current_page,
            total: res.data.total,
            row: res.data.per_page,
          };
        }
      });
    },
    changeBuildStatus(e) {
      if (e == 1) {
        this.build_status_list = [];
        this.getZibuildStatus();
      } else {
        this.build_status_list = [];
        this.getBuildStatus();
      }
    },
    copyData(row) {
      this.$http.createBuildLabel(row).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "复制成功",
            type: "success",
          });
          this.getBuildCategory();
          this.dialogBuild = false;
        }
      });
    },
    createSensitieWords() {
      this.sensitiveWords_form_create = {
        enable: 1,
        scene_category: [],
      };
      this.dialogSensitiveWordsTitle = "addData";
      this.dialogSensitiveWords = true;
    },
    onCreateSensitiveWords() {
      this.sensitiveWords_form_create.scene_category = this.sensitiveWords_form_create.scene_category.toString();
      if (this.dialogSensitiveWordsTitle === "addData") {
        this.$http
          .createSensitiveWords(this.sensitiveWords_form_create)
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "创建成功",
                type: "success",
              });
              this.sensitiveWords_form_create.scene_category = this.sensitiveWords_form_create.scene_category.toString();

              this.getSensitiveWords();
              this.dialogSensitiveWords = false;
            }
          });
      } else {
        this.$http
          .updataSensitiveWords(this.sensitiveWords_form_create)
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "修改成功",
                type: "success",
              });
              this.sensitiveWords_form_create.scene_category = this.sensitiveWords_form_create.scene_category.toString();

              this.getSensitiveWords();
              this.dialogSensitiveWords = false;
            }
          });
      }
    },
    updateSensitive(row) {
      this.sensitiveWords_form_create = row;
      this.sensitiveWords_form_create.scene_category = row.scene_category.split(
        ","
      );
      this.dialogSensitiveWordsTitle = "updateData";
      this.dialogSensitiveWords = true;
    },
    deleteSensitive(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteSensitiveWords(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getSensitiveWords();
            }
          });
        })
        .catch(() => { });
    },
    closeSensitive() {
      this.sensitiveWords_form_create.scene_category = this.sensitiveWords_form_create.scene_category.toString();
    },
    onSubmitLoan() {
      if (
        !this.loan_form.provident_fund_rate ||
        !this.loan_form.lpr_in_a_year_rate ||
        !this.loan_form.lpr_one_more_year_rate ||
        !this.loan_form.commercial_loan_rate
      ) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http.createLoanForm(this.loan_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
        }
      });
    },
    changeDataStatus(row) {
      this.dialogTitleStatus = "updateData";
      this.dialogBuildStatus = true;
      this.form_status_create = row;
    },
    createBuildStatus() {
      this.form_status_create = {
        sort: 0,
      };
      this.dialogTitleStatus = "addData";
      this.dialogBuildStatus = true;
    },
    onSubmitBuildStatus() {
      if (this.dialogTitleStatus === "addData") {
        this.$http.createBuildSatus(this.form_status_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getZibuildStatus();
            this.dialogBuildStatus = false;
          }
        });
      } else {
        this.$http.updataBuildStatus(this.form_status_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getZibuildStatus();
            this.dialogBuildStatus = false;
          }
        });
      }
    },
    copyDataStatus(row) {
      this.$http.createBuildSatus(row).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "复制成功",
            type: "success",
          });
          this.getZibuildStatus();
          this.dialogBuildStatus = false;
        }
      });
    },
    deleteDataStatus(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteBuildStatus(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getZibuildStatus();
            }
          });
        })
        .catch(() => { });
    },
    getQrCode() {
      var path = "";
      if (this.form.website_mode_category == 0) {
        path = "/index/index";
      }
      if (this.form.website_mode_category == 1) {
        path = "/only_build/pages/index/index";
      }
      if (this.form.website_mode_category == 2) {
        path = "/weifangchan/pages/index/index";
      }
      this.$http.getAppQrcode({ path: path }).then((res) => {
        if (res.status === 200) {
          this.appQrcode = `data: image/jpeg;base64,${btoa(
            new Uint8Array(res.data).reduce(
              (data, byte) => data + String.fromCharCode(byte),
              ""
            )
          )}`;
        }
      });
    },
    formMatEnable(e) {
      if (e == 0) {
        return "未开启";
      } else {
        return "开启";
      }
    },
    handleSuccessWXImg(response) {
      this.form.estate_purchase_wx_group_qrcode = response.url;
    },
    handlePictureCardPreviewWXImg(file) {
      this.WXImageUrlBuild = file.response.url;
      this.WXImgVisible = true;
    },
    handleRemoveWXImg() { },
    // JumpMiniApp() {
    //   this.activeName = "third";
    //   this.isclass = 1;
    // },
    // 查询小程序名称审核状态
    miniNameAudit(e) {
      if (!e) {
        this.$message.error("请输入状态码！");
        return;
      }
      this.$http.miniNameAuditStatus({ audit_id: e }).then((res) => {
        if (res.status === 200) {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: ` <div>
                          <p>返回码（error）：${res.data.errcode};</p>
                          <p>错误信息（errmsg）：${res.data.errmsg};</p>
                          <p>审核昵称（nickname）：${res.data.nickname};</p>
                          <p>审核状态（audit_stat）： ${res.data.audit_stat === 1
                ? "审核中"
                : res.data.audit_stat === 2
                  ? "审核失败"
                  : "审核成功"
              };</p>
                          <p>失败原因（fail_reason）： ${res.data.fail_reason
              };</p>
                      </div>`,
            type: "success",
            duration: 0,
            showClose: true,
          });
        }
      });
    },
    handleSuccesslicense(res) {
      if (res.media_id) {
        this.mini_program_form.license = res.media_id;
      }
    },
    handlePreviewlicense(file) {
      this.licenseVisible = true;
      this.licenseImageUrl = file.response.url;
    },
    handleRemovelicense() {
      this.mini_program_form.license = "";
    },

    handleSuccessStuff1(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_1 = res.media_id;
      }
    },
    handlePreviewStuff1(file) {
      this.stuff1Visible = true;
      this.stuff1ImageUrl = file.response.url;
    },
    handleRemoveStuff1() {
      this.mini_program_form.naming_other_stuff_1 = "";
    },
    handleSuccessStuff2(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_2 = res.media_id;
      }
    },
    handlePreviewStuff2(file) {
      this.stuff2Visible = true;
      this.stuff2ImageUrl = file.response.url;
    },
    handleRemoveStuff2() {
      this.mini_program_form.naming_other_stuff_2 = "";
    },
    handleSuccessStuff3(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_3 = res.media_id;
      }
    },
    handlePreviewStuff3(file) {
      this.stuff3Visible = true;
      this.stuff3ImageUrl = file.response.url;
    },
    handleRemoveStuff3() {
      this.mini_program_form.naming_other_stuff_3 = "";
    },
    handleSuccessStuff4(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_4 = res.media_id;
      }
    },
    handlePreviewStuff4(file) {
      this.stuff4Visible = true;
      this.stuff4ImageUrl = file.response.url;
    },
    handleRemoveStuff4() {
      this.mini_program_form.naming_other_stuff_4 = "";
    },

    handleSuccessStuff5(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_5 = res.media_id;
      }
    },
    handlePreviewStuff5(file) {
      this.stuff5Visible = true;
      this.stuff5ImageUrl = file.response.url;
    },
    handleRemoveStuff5() {
      this.mini_program_form.naming_other_stuff_5 = "";
    },
    AuthorizationGX() {
      let website_id = localStorage.getItem("website_id");
      let url = encodeURIComponent(
        `https://yun.tfcs.cn/admin/?website_id=${website_id}#/loading?is_loading=1`
      );
      this.$http.openWX(url).then((res) => {
        if (res.status === 200) {
          // window.location.href = res.data.url;
          var url = res.data.url;
          this.setNewLink(url);
        }
      });
    },
    setNewLink(url) {
      let referLink = document.createElement("a");
      referLink.href = url;
      document.body.appendChild(referLink);
      referLink.click();
      parent.removeChild(referLink);
    },
    queryWx() {
      // 调用这个接口查询授权
      this.$http.queryGongZhong().then((res) => {
        if (res.status === 200) {
          if (res.data.id && res.data.updated_at) {
            this.wx_auth = true;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.input-short {
  width: 300px;
}
.el-textarea {
  width: 300px;
}
.el-main {
  margin-top: 150px;
  .title {
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
.tabs {
  padding-top: 30px;
  background: #fff;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
  }
  i {
    color: red;
  }
}
.list {
  padding: 10px;
  max-height: 260px;
  overflow-x: hidden;
}
.list .list-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #dedede;
}
.list .list-item .el-icon-check {
  padding: 3px;
  border-radius: 50%;
  background-color: #409eff;
  color: #fff;
}
.list .list-item .flex-1 {
  flex: 1;
}
.list-item .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}
.list-item .content {
  color: #666;
  font-size: 13px;
}
.el-form-item {
  background: #fff;
  padding: 10px;
}
.el-input {
  margin: 8px 0;
}
.thems-color {
  p {
    width: 40px;
    height: 40px;
    margin-right: 30px;
  }
}
.el-row {
  .el-form {
    padding: 10px;
    border: 1px solid #eee;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-input {
    width: 300px;
  }
}
.label {
  width: 64px;
  font-size: 12px;
  cursor: pointer;
  color: #fff;
  background: #00c800;
  border-radius: 2px;
  padding: 4px 7px;
  min-width: 50px;
  text-align: center;
  &.red {
    background: #ff6d6d;
  }
}
</style>
