<template>
    <div>
        <el-tree
          :data="list"
          show-checkbox
          node-key="id"
          ref="tree"
          :default-expand-all="defaultExpandAll"
          :check-strictly="checkStrictly"
          @check-change="currentCheckChange"
          :props="defaultProps">
        </el-tree>
    </div>
</template>
<script>
export default {
    props: {
        list: {
          type: Array,
          default: () => [],
        },
        defaultExpandAll: {
          type: [Boolean],
          default: true,
        },
        checkStrictly: {
          type: [Boolean],
          default: true,
        },
        defaultProps: {
          type: Object,
          default: () => {
            return {
              children: "subs",
              label: "name",
              value: "id",
                disabled: (data) => {
                  return data.subs;
                },
            };
          },
        },
    },
    data() {
        return {
            defaultIds: [],
        }
    },
    methods:{
        currentCheckChange(data, type) {
          if (type === false) {
            let index = this.defaultIds.findIndex((item) => item == data.id);
            this.defaultIds.splice(index, 1);
          } else {
            this.changeSelected([data.id]);
            // this.$emit("onClickItem", data)
          }
        },
        changeSelected(ids = this.defaultValue, type = true) {
          this.$refs.tree.setCheckedKeys(ids, type);
          let nodes = this.$refs.tree.getCheckedNodes();
          let keys = this.$refs.tree.getCheckedKeys();
          let node = {
            checkedKeys: keys,
            checkedNodes: nodes,
          };
          if (keys.length && nodes.length) {
            this.$emit("onClickItem", node);
          }
        },
    },

}
</script>
<style lang="scss" scoped>

</style>