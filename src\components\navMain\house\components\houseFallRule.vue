<template>
    <div class="container">
        <el-row>
            <el-col :span="24">
                <div class="config-prompt">
                    <i style="color: #00CEFF;" class="el-icon-info"></i>
                    新增掉公规则后，系统会在第二天凌晨立即按规则执行。如您是首次开启掉公规则，建议提前留足时间让员工维护后，再新增掉公规则。
                </div>
                <el-form ref="formData" :model="formData_list" label-width="200px">
                    <el-form-item label="跟进掉公策略限制：" :required="true">
                        <div class="flex-row items-center">
                            <el-input
                                style="width: 300px"
                                v-model="formData_list.days"
                                placeholder="请输入天数"
                                min="0"
                                step="1"
                                type="number"
                            >
                                <template slot="append">天</template>
                            </el-input>
                            <el-tooltip class="item" effect="light" placement="right">
                            <div slot="content" style="max-width: 300px">0为不开启</div>
                            <i
                            class="el-icon-info"
                            style="
                                color: #f56c6c;
                                font-size: 20px;
                                margin-top: 10px;
                                margin-left: 10px;
                            "
                            ></i>
                        </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="房源状态范围：" :required="true">
                        <div class="flex-row items-center">
                            <el-select 
                                v-model="formData_list.trade_status" 
                                multiple 
                                placeholder="请选择" 
                                style="width: 300px;"
                            >
                                <el-option
                                    v-for="item in trade_status"
                                    :key="item.values"
                                    :label="item.name"
                                    :value="item.values">
                                </el-option>
                            </el-select>
                            <el-tooltip class="item" effect="light" placement="right">
                                <div slot="content" style="max-width: 300px">
                                可多选，不在选中范围内的，不执行自动掉公策略。
                                </div>
                                <i
                                class="el-icon-info"
                                style="
                                    color: #f56c6c;
                                    font-size: 20px;
                                    margin-top: 10px;
                                    margin-left: 10px;
                                "
                                ></i>
                            </el-tooltip>
                        </div>
                    </el-form-item>
                    <el-form-item label="挂牌状态：">
                        <div class="flex-row items-center">
                            <el-select 
                                v-model="formData_list.trade_type" 
                                multiple 
                                placeholder="请选择" 
                                style="width: 300px;"
                            >
                                <el-option
                                    v-for="item in trade_type"
                                    :key="item.values"
                                    :label="item.name"
                                    :value="item.values">
                                </el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                    <el-form-item label="房源用途：">
                        <div class="flex-row items-center">
                            <el-select 
                                v-model="formData_list.usage_type" 
                                multiple 
                                placeholder="请选择" 
                                style="width: 300px;"
                            >
                                <el-option
                                    v-for="item in usage_type"
                                    :key="item.values"
                                    :label="item.name"
                                    :value="item.values">
                                </el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                    <el-form-item label="适用部门：" :required="true">
                        <div class="flex-row items-center">
                            <el-select 
                                ref="auth_select_department"
                                style="width: 300px;"
                                v-model="formData_list.department" 
                                multiple 
                                placeholder="请选择"
                                @focus="showPersonnelAuthority"
                                @change="PersonnelChange"
                            >
                                <el-option
                                    :disabled="true"
                                    v-for="list in select_department"
                                    :key="list.id"
                                    :label="list.name"
                                    :value="list.id">
                                </el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onClickForm">确认</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-dialog
            :visible.sync="show_add_member"
            width="400px"
            title="选择部门"
            append-to-body
        >
        <div class="member" ref="memberList">
            <multipleTree
                v-if="show_add_member"
                :list="departmentList"
                :defaultValue="selectedIds"
                @onClickItem="selecetedMember"
                :defaultExpandAll="false"
                :defaultProps="defaultProps"
            >
            </multipleTree>
            <div style="margin-top: 20px; justify-content: space-around;" class="footer flex-row align-center">
                <el-button type="text" @click="show_add_member = false">取消</el-button>
                <el-button type="primary" @click="selectMemberOk">确定</el-button>
            </div>
        </div>
        </el-dialog>
    </div>
</template>
<script>
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
    components: {
        multipleTree,
    },
    data() {
        return {
            // 接口提交数据列表
            formData_list: {
                days: '', // 跟进掉公策略限制 单位天 0表示不掉公
                trade_type: [], // 挂牌状态 多项,分隔
                trade_status: [], // 房源状态 多项,分隔
                usage_type: [], // 房源用途 多项,分隔
                department: [], // 适用部门 多项,分隔
                // type: 1, // 掉公类型 默认1 维护人掉公 当前仅该一项选项 不需要传递该值
            },
            trade_status: [], // 房源状态范围
            trade_type: [], // 挂牌状态
            usage_type: [], // 房源用途
            departmentList: [], // 部门成员列表
            // 成员列表数据展示规则
            defaultProps: {
                value: 'id',
                label: 'name',
                children: 'subs',
                disabled: (data) => {
                    return !data.is_disbled;
                }
            },
            select_department: [], // 适用部门选择框的下拉数据
            show_add_member: false, // 选择部门模态框
            selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
        }
    },
    created() {
        this.getHouseInfoList();
        this.getCrmDepartmentList();
        this.getHouseControl();
    },
    methods: {
        // 确定提交
        onClickForm() {
            if(this.formData_list.days == 0 || (this.formData_list.days != '' && this.formData_list.days != undefined && this.formData_list.days > -1)) {
                this.formData_list.days.toString();
            } else {
                return this.$message({
                    message: '请正确填写跟进掉公策略限制天数',
                    type: 'warning'
                });
            }
            if(this.formData_list.trade_status == '' || this.formData_list.trade_status == undefined) {
                return this.$message({
                    message: '请选择房源状态范围',
                    type: 'warning'
                });
            } else if(this.formData_list.department == '' || this.formData_list.department == undefined) {
                return this.$message({
                    message: '请选择适用部门',
                    type: 'warning'
                });
            }
            this.formData_list.trade_status = this.formData_list.trade_status 
              ? this.formData_list.trade_status.toString()
              : "";
            this.formData_list.trade_type = this.formData_list.trade_type 
              ? this.formData_list.trade_type.toString()
              : "";
            this.formData_list.usage_type = this.formData_list.usage_type 
              ? this.formData_list.usage_type.toString()
              : "";
            this.formData_list.department = this.formData_list.department 
              ? this.formData_list.department.toString()
              : "";
            // 请求接口
            this.$http.setHouseFallRule(this.formData_list).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    });
                    this.getHouseControl();
                }
            })
        },
        // 获取掉公规则参数
        getHouseInfoList() {
            this.$http.getHouseInfoList().then((res) => {
                if(res.status == 200) {
                    // 房源状态范围
                    this.trade_status = res.data.trade_status;
                    // 房源挂牌状态
                    this.trade_type = res.data.trade_type;
                    // 房源用途
                    this.usage_type = res.data.usage_type;
                }
            })
        },
        // 获取部门列表
        getCrmDepartmentList() {
            this.$http.getCrmDepartmentList().then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"res.data")
                    this.departmentList = res.data;
                    this.recursionData(this.departmentList);
                }
            })
        },
        // 递归处理数据
        recursionData(data) {
            // console.log(data,"内容");
            for(let key in data) {
                if(typeof data[key].subs == "object") {
                    data[key].subs.map((item) => {
                        // 如果没有subs下级
                        if(!item.subs) {
                            // 赋值id和name
                            let num = {id: item.id, name: item.name};
                            this.select_department.push(num);
                            // 新增参数is_disbled
                            item.is_disbled = true;
                        }
                    })
                    this.recursionData(data[key].subs)
                }
            }
            // console.log(this.select_department,"部门");
        },
        // 获取掉公规则配置参数
        getHouseControl() {
            this.$http.getHouseControl().then((res) => {
                if(res.status == 200 && res.data != '') {
                    // 获取需要的数据
                    this.formData_list.days = res.data.days;
                    this.formData_list.department = res.data.department;
                    this.formData_list.trade_status = res.data.trade_status;
                    this.formData_list.trade_type = res.data.trade_type;
                    this.formData_list.usage_type = res.data.usage_type;
                    // 处理数据
                    this.formData_list.trade_status = this.formData_list.trade_status 
                      ? this.setArrMini(this.formData_list.trade_status)
                      : "";
                    this.formData_list.trade_type = this.formData_list.trade_type 
                      ? this.setArrMini(this.formData_list.trade_type)
                      : "";
                    this.formData_list.usage_type = this.formData_list.usage_type 
                      ? this.setArrMini(this.formData_list.usage_type)
                      : "";
                    this.formData_list.department = this.formData_list.department 
                      ? this.setArrMini(this.formData_list.department)
                      : "";
                }
            })
        },
        // 处理数据转换为字符串
        setArrMini(arr) {
            let n_arr = (arr).split(",");
            let n_arr_2 = n_arr.map((item) => {
            return parseInt(item);
            });
            return n_arr_2;
        },
        // 适用部门获取焦点
        showPersonnelAuthority() {
            this.show_add_member = true;
            if(this.formData_list.department != '' && this.formData_list.department != undefined) {
                this.selectedIds = this.formData_list.department;
            }
            this.$nextTick(() => {
                this.$refs.auth_select_department.blur();
            })
        },
        // 适用部门参数发生变化
        PersonnelChange(val) {
            console.log(111111,val);
            this.selectedIds = val;
        },
        // 点击适用部门时触发
        selecetedMember(e) {
            this.selectedIds = e.checkedKeys;
        },
        // 确定选中部门
        selectMemberOk() {
            this.show_add_member = false;
            this.formData_list.department = this.selectedIds;
        },
    }
}
</script>
<style lang="scss" scoped>
.container {
    padding-top: 24px;
    min-height: 110vh;
    .config-prompt {
        background-color: #E6FCFF;
        color: #606266;
        padding: 12px;
        box-sizing: border-box;
        margin-bottom: 20px;
        margin-left: 80px;
        border-radius: 7px;
    }
}
</style>