<template>
  <div class="lsit">
    <el-container>
      <el-header class="div row">
        <myTopTips title="用户列表" :number="tableData.length"></myTopTips>

        <div class="div row">
          <el-input
            @input="onInput"
            @change="onChange"
            v-model="input"
            placeholder="搜索相关用户"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>
      <div style="margin-bottom: 10px">
        <el-button
          v-if="$hasShow('添加管理员')"
          icon="el-icon-plus"
          type="primary"
          @click="addUsers"
          >添加用户</el-button
        >
      </div>
      <myTable
        v-loading="is_table_loading"
        :table-list="tableData"
        :header="table_header"
      ></myTable>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :currentPage="params.currentPage"
            :pagesize="params.pagesize"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
          ></myPagination>
        </div>
      </el-footer>
      <el-dialog
        title="添加用户"
        :visible.sync="dialogCreate"
        :before-close="closeAdduser"
      >
        <myForm
          :form_create="form_create_obj"
          @onClick="createUser"
          ref="addUserref"
        ></myForm>
      </el-dialog>
      <el-dialog title="修改用户" :visible.sync="dialogUpdata">
        <myForm
          :form_create="form_create_obj_updata"
          @onClick="updataUser"
        ></myForm>
      </el-dialog>
      <el-dialog title="修改密码" :visible.sync="dialogPassword">
        <myForm
          :form_create="form_create_obj_password"
          @onClick="isModify"
        ></myForm>
      </el-dialog>
      <el-dialog
        :title="titleMap[dialogTitle]"
        :visible.sync="dialogCreateRoles"
      >
        <el-form :model="create_form" label-width="100px" inline>
          <el-form-item label="角色名称:" prop="role_name">
            <el-select
              v-model="create_form.role_names"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in roles_list"
                :key="item.id"
                :label="item.name === '站长' ? '创始人' : item.name"
                :value="item.name"
                :disabled="item.name === '站长'"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" style="margin: 0" @click="submitData"
              >提交</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-container>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import myForm from "@/components/components/my_form";
export default {
  name: "man_management",
  components: {
    myPagination,
    myTable,
    myForm,
  },
  data() {
    var validatePass5 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (
          this.form_create_obj_password.model.new_password_confirmation !== ""
        ) {
          this.$refs.form_create_obj_password.model.validateField(
            "new_password_confirmation"
          );
        }
        callback();
      }
    };
    var validatePass6 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.form_create_obj_password.model.new_password) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    var checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("手机号不能为空"));
      } else {
        const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/;
        if (reg.test(value)) {
          callback();
        } else {
          return callback(new Error("请输入正确的手机号"));
        }
      }
    };
    return {
      // 搜索框数据
      input: "",
      dialogCreate: false,
      dialogUpdata: false,
      dialogPassword: false,

      updata_loading: false,
      create_loading: false,
      password_loading: false,
      tableData: [],
      // 存放列表图片
      imgbox: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      rules3: {},
      dialogCreateRoles: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      create_form: {
        id: "",
        role_names: [],
      },
      roles_list: [],
      table_header: [
        { prop: "id", label: "ID", width: "80" },
        { prop: "user_name", label: "用户名" },
        {
          label: "职务",
          render: (h, data) => {
            return (
              <div>
                {data.row.post ? (
                  <el-tag
                    type="success"
                    domPropsInnerHTML={data.row.post}
                  ></el-tag>
                ) : (
                  <el-tag type="danger">暂无职务</el-tag>
                )}
              </div>
            );
          },
        },
        // {
        //   label: "角色",
        //   render: (h, data) => {
        //     return (
        //       <div>
        //         {/* {data.row.roles.map((item) => {
        //           return (
        //             <el-tag
        //               type={item.name === "站长" ? "success" : "primary"}
        //               domPropsInnerHTML={
        //                 item.name === "站长" ? "创始人" : item.name
        //               }
        //             ></el-tag>
        //           );
        //         })} */}
        //       </div>
        //     );
        //   },
        // },
        { prop: "phone", label: "联系方式" },
        // { prop: "created_at", label: "创建时间" },
        {
          label: "操作",
          width: "350",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改管理员信息") ? (
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="success"
                    style="width:120px;border-radius:5px;margin:5px"
                    onClick={() => {
                      this.upUser(data.row);
                    }}
                  >
                    修改用户信息
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("修改管理员密码") ? (
                  <el-button
                    size="mini"
                    type="primary"
                    style="width:120px;border-radius:5px;margin:5px"
                    onClick={() => {
                      this.updataPassword(data.row);
                    }}
                  >
                    修改密码
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除管理员") &&
                  data.row.user_name !== "admin" ? (
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    style="width:120px;border-radius:5px;margin:5px"
                    type="danger"
                    onClick={() => {
                      this.handleDelete(0, data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}

                {/* {data.row.roles.every((item) => {
                  return item.name !== "站长";
                }) && this.$hasShow("管理员绑定角色") ? (
                  <el-button
                    size="mini"
                    type="primary"
                    style="width:120px;border-radius:5px;margin:5px"
                    onClick={() => {
                      this.bindRoles(data.row);
                    }}
                  >
                    绑定角色
                  </el-button>
                ) : (
                  ""
                )} */}
              </div>
            );
          },
        },
      ],
      form_create_obj: {
        inline: false,
        label_width: "100px",
        model: {},
        rules: {
          user_name: [
            { required: true, message: "请输入用户名", trigger: "blur" },
          ],
          phone: [
            {
              required: true,
              validator: checkPhone,
              trigger: "blur",
              message: "请输入手机号",
            },
          ],
          password: [
            { required: true, trigger: "blur", message: "请输入密码" },
          ],
          password_confirmation: [
            { required: true, trigger: "blur", message: "请再次输入密码" },
          ],
        },
        formLabel: [
          {
            model: "user_name",
            label: "用户名：",
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入用户名",
          },
          {
            model: "post",
            label: "职务：",
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入职务",
          },
          {
            model: "phone",
            label: "手机号码：",
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入手机号码",
          },
          {
            model: "password",
            label: "密码：",
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入密码",
          },
          {
            model: "password_confirmation",
            label: "确认密码：",
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入确认密码",
          },
        ],
      },
      form_create_obj_updata: {
        inline: false,
        label_width: "100px",
        model: {
          user_name: "",
          post: "",
          phone: "",
        },
        rules: {
          user_name: [
            { required: true, message: "请输入用户名", trigger: "blur" },
          ],
          phone: [{ validator: checkPhone, trigger: "blur" }],
        },
        formLabel: [
          {
            label: "用户名：",
            inputWidth: "300px",
            model: "user_name",
            type: "input",
            placeholder: "请输入用户名",
          },
          {
            label: "职务：",
            inputWidth: "300px",
            model: "post",
            type: "input",
            placeholder: "请输入职务",
          },
          {
            label: "手机号码：",
            inputWidth: "300px",
            model: "phone",
            type: "input",
            placeholder: "请输入联系方式",
          },
        ],
      },
      form_create_obj_password: {
        inline: false,
        label_width: "100px",
        model: {
          id: "",
          old_password: "",
          new_password: "",
          new_password_confirmation: "",
        },
        rules: {
          old_password: [
            { required: true, message: "请输入原密码", trigger: "blur" },
          ],
          new_password: [{ validator: validatePass5, trigger: "blur" }],
          new_password_confirmation: [
            { validator: validatePass6, trigger: "blur" },
          ],
        },
        formLabel: [
          {
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入原密码",
            model: "old_password",
            label: "原密码：",
          },
          {
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入新密码",
            model: "new_password",
            label: "新密码：",
          },
          {
            type: "input",
            inputWidth: "300px",
            placeholder: "请输入确认密码",
            model: "new_password_confirmation",
            label: "确认密码：",
          },
        ],
      },
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    //关闭添加回调
    closeAdduser() {
      this.dialogCreate = false;
      this.$refs.addUserref.$children[0].resetFields()
    },
    // 获取列表数据
    getDataList() {
      this.$http
        .getUserList(this.params.currentPage, this.params.pagesize, this.input)
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
            this.getWebsiteRoles();
          }
        });
    },
    addUsers() {
      this.form_create_obj.model = {};
      this.dialogCreate = true;
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
      this.getDataList();
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
    },
    // 搜索用户
    search() {
      this.params.currentPage = 1;
      this.getDataList();
    },
    onChange() {
      this.search();
    },
    onInput(e) {
      if (!e) {
        this.params.currentPage = 1;
        this.getDataList();
      }
    },
    // 操作
    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteUser(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    upUser(row) {
      this.form_create_obj_updata.model.id = row.id;
      this.form_create_obj_updata.model.user_name = row.user_name;
      this.form_create_obj_updata.model.phone = row.phone;
      this.form_create_obj_updata.model.post = row.post;
      this.dialogUpdata = true;
    },
    // 修改操作
    updataUser(e) {
      if (!e.model.user_name || !e.model.id) {
        this.$message({
          message: "请输入内容提交",
          type: "error",
        });
        return false;
      }
      this.$http.updataUser(e.model).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.dialogUpdata = false;
          this.getDataList();
        }
      });
    },
    // 添加用户
    createUser(e) {
      if (
        !this.form_create_obj.model.user_name ||
        !this.form_create_obj.model.phone ||
        !this.form_create_obj.model.password ||
        !this.form_create_obj.model.password_confirmation
      ) {
        this.$message({
          message: "请输入内容提交",
          type: "error",
        });
        return false;
      } else {
        this.$http.createUser(e.model).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "添加用户成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    // 修改密码
    updataPassword(row) {
      this.form_create_obj_password.model = {
        id: row.id,
      };
      this.dialogPassword = true;
    },
    // 确认修改
    isModify(e) {
      this.$http.modifyPassword(e.model).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: res.data.message ? res.data.message : "修改成功",
          });
          this.form_create_obj_password.model.old_password = "";
          this.form_create_obj_password.model.new_password = "";
          this.form_create_obj_password.model.new_password_confirmation = "";
          this.dialogPassword = false;
        }
        // if (res.data === "") {
        // 	this.$message({
        // 		message: "修改成功",
        // 		type: "success",
        // 	});

        // }
      });
    },
    // 绑定角色
    bindRoles(row) {
      this.create_form.role_names = [];
      this.dialogTitle = "addData";
      this.dialogCreateRoles = true;
      this.create_form.id = row.id + "";
      this.queryAdminRoles();
    },

    getWebsiteRoles() {
      this.$http.getWebsiteRoles({ per_page: 100 }).then((res) => {
        if (res.status === 200) {
          this.roles_list = res.data.data;
        }
      });
    },
    // 提交绑定角色
    submitData() {
      this.$http.resetUserRole(this.create_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.params.current_page = 1;
          this.getDataList();
          this.dialogCreateRoles = false;
        }
      });
    },
    queryAdminRoles() {
      this.$http.queryAdminRoles(this.create_form.id).then((res) => {
        if (res.status === 200) {
          res.data.roles.map((item) => {
            this.create_form.role_names.push(item.name);
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
</style>
