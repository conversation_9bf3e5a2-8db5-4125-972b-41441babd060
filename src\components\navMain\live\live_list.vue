<template>
  <el-container>
    <el-header>
      <el-button type="primary" @click="getLastLiveList">{{
        isLast ? "获取直播列表" : "获取最后一次直播记录"
      }}</el-button>
      <el-button type="success" @click="createLiving">创建直播</el-button>
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer v-if="!isLast">
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_create" label-width="120px">
        <el-form-item label="开始时间：">
          <el-date-picker
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="form_create.living_start_at"
            type="datetime"
            placeholder="选择日期时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="直播类型：">
          <el-select v-model="form_create.type">
            <el-option
              v-for="item in type_list"
              :key="item.value"
              :label="item.description"
              :value="item.value"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
        <div v-if="form_create.type === 4">
          <el-form-item label="活动直播封面：">
            <el-upload
              :headers="myHeader"
              :action="live_category"
              :on-success="handleSuccessCover"
              :before-upload="beforeSuccessCover"
              :show-file-list="false"
              list-type="picture-card"
            >
              <img
                v-if="form_create.activity_cover_image"
                :src="form_create.activity_cover_image"/>
              <i v-else class="el-icon-plus"></i
            ></el-upload>
          </el-form-item>
          <el-form-item label="活动直播分享：">
            <el-upload
              :headers="myHeader"
              :action="live_category"
              :on-success="handleSuccssShare"
              :before-upload="beforeSuccessCover"
              :show-file-list="false"
              list-type="picture-card"
            >
              <img
                v-if="form_create.activity_share_image"
                :src="form_create.activity_share_image"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="活动直播附图：">
            <el-upload
              :headers="myHeader"
              list-type="picture-card"
              :action="live_category"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-success="handleSuccessList"
              :file-list="activityFileList"
              :limit="5"
              accept=".jpg,.png"
            >
              <i class="el-icon-plus"></i
            ></el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </el-form-item>
          <el-form-item label="活动直播描述：">
            <el-input
              type="textarea"
              v-model="form_create.activity_detail_description"
              :rows="4"
              style="width:300px"
              placeholder="请输入活动直播描述"
              maxlength="300"
              show-word-limit
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item label="主播用户ID：">
          <el-select v-model="form_create.anchor_userid">
            <el-option
              v-for="item in users_list"
              :key="item.open_userid"
              :label="item.name"
              :value="item.userid"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="直播标题：">
          <el-input
            style="width:300px"
            placeholder="请输入"
            v-model="form_create.theme"
            maxlength="60"
            show-word-limit
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="直播时长：">
          <el-input
            v-model="form_create.living_duration"
            type="number"
            step="1"
            min="0"
            style="width:300px"
          >
            <template slot="append">秒</template>
          </el-input>
        </el-form-item>
        <el-form-item label="预计开始：">
          <el-input
            v-model="form_create.remind_time"
            type="number"
            step="1"
            min="0"
            style="width:300px"
          >
            <template slot="append">秒</template>
          </el-input>
        </el-form-item>
        <el-form-item label="直播描述：" v-if="form_create.type !== 4">
          <el-input
            type="textarea"
            v-model="form_create.description"
            :rows="4"
            style="width:300px"
            placeholder="请输入直播描述"
            maxlength="300"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item>
          <div
            class=" tips"
            style="background:#eee;padding:4px 10px;width:300px"
          >
            <p>
              <i class="el-icon-info"></i
              >media_id：上传后系统返回media_id，注意：有效期仅三天，<i
                style="color:red"
                >点击‘查看图片’展示图片</i
              >
            </p>
            <p>
              <i class="el-icon-info"> </i>
              上传图片：支持JPG,PNG格式，大小为2MB，上传后自动弹出图片内容或者点击<i
                style="color:red"
                >查看图片</i
              >显示图片内容
            </p>
            <p>
              <i class="el-icon-info"></i> 直播时间只能选择当前时间之后的时间
            </p>
          </div>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="onCreate"
            >确认</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
    <el-dialog title="查看图片" :visible.sync="dialogImg" width="20%">
      <img :src="activity_img" alt="" />
    </el-dialog>
  </el-container>
</template>

<script>
import myTable from "@/components/components/my_table";
import myPagination from "@/components/components/my_pagination";
import config from "@/utils/config";
export default {
  name: "live_list",
  components: {
    myTable,
    myPagination,
  },
  data() {
    return {
      tableData: [],
      params: {
        currentPage: 1,
        total: 0,
        per_page: 10,
      },
      isLast: false,
      status_list: [
        //直播状态
        { value: 0, description: "预约中" },
        { value: 1, description: "直播中" },
        { value: 2, description: "已结束" },
        { value: 3, description: "已过期" },
        { value: 4, description: "已取消" },
      ],
      type_list: [
        //直播类型
        { value: 0, description: "通用直播" },
        { value: 1, description: "小班课", disabled: true },
        { value: 2, description: "大班课", disabled: true },
        { value: 3, description: "企业培训" },
        { value: 4, description: "活动直播" },
      ],
      table_header: [
        {
          expand: "expand",
          render: (h, data) => {
            return (
              <el-form label-position="left" inline class="demo-table-expand">
                {data.row.activity_cover_media_id ? (
                  <el-form-item label="活动直播封面：">
                    <el-button
                      size="mini"
                      onClick={() => {
                        this.queryMiediaId1(data.row.activity_cover_media_id);
                      }}
                    >
                      查看图片
                    </el-button>
                  </el-form-item>
                ) : (
                  ""
                )}
                {data.row.activity_share_media_id ? (
                  <el-form-item label="活动直播分享：">
                    <el-button
                      size="mini"
                      onClick={() => {
                        this.queryMiediaId1(data.row.activity_share_media_id);
                      }}
                    >
                      查看图片
                    </el-button>
                  </el-form-item>
                ) : (
                  ""
                )}
                {data.activity_detail_image_list ? (
                  <el-form-item label="活动直播附图：">
                    {data.row.activity_detail_image_list.indexOf("[") != -1 ? (
                      JSON.parse(data.row.activity_detail_image_list).map(
                        (item) => {
                          return (
                            <el-button
                              size="mini"
                              style="margin:4px"
                              onClick={() => {
                                this.queryMiediaId1(item);
                              }}
                            >
                              查看图片
                            </el-button>
                          );
                        }
                      )
                    ) : (
                      <el-button
                        size="mini"
                        onClick={() => {
                          this.queryMiediaId1(
                            data.row.activity_detail_image_list
                          );
                        }}
                      >
                        查看图片
                      </el-button>
                    )}
                  </el-form-item>
                ) : (
                  ""
                )}
                {data.row.activity_detail_description ? (
                  <el-form-item label="活动直播描述">
                    <span>{data.row.activity_detail_description}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
              </el-form>
            );
          },
        },
        { prop: "id", label: "ID", width: "100" },
        {
          label: "直播间id",
          render: (h, data) => {
            return (
              <el-tooltip
                class="item"
                effect="dark"
                content="直播id，通过此id可调用“进入直播”接口(包括小程序接口和JS-SDK接口)，以实现主播到点后的开播操作，以及观众进入直播详情预约和观看直播"
                placement="top-start"
              >
                <el-button
                  size="mini"
                  onClick={() => {
                    this.$onCopyValue(data.row.living_id);
                  }}
                  class="el-icon-copy-document"
                >
                  复制
                </el-button>
              </el-tooltip>
            );
          },
        },
        {
          label: "主播用户ID",
          prop: "anchor_userid",
        },
        {
          label: "直播标题",
          prop: "theme",
        },
        {
          label: "直播状态",
          formatter: (row) => {
            var arr = this.status_list.find((item) => {
              return item.value === row.status;
            });
            return arr.description;
          },
        },
        {
          label: "直播类型",
          formatter: (row) => {
            var arr = this.type_list.find((item) => {
              return item.value === row.type;
            });
            return arr.description;
          },
        },
        {
          label: "直播开始时间",
          prop: "living_start_at",
        },
        {
          label: "直播时长",
          render: (h, data) => {
            return <p>{data.row.living_duration / 60}分钟</p>;
          },
        },
        {
          label: "直播描述",
          prop: "description",
        },
        {
          label: "预计开始",
          render: (h, data) => {
            return <p>{data.row.remind_time / 60}分钟</p>;
          },
        },
        {
          label: "操作",
          fixed: "right",
          width: "400",
          render: (h, data) => {
            return (
              <div>
                {data.row.status === 0 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.updateLiving(data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                <el-button
                  type="danger"
                  size="mini"
                  onClick={() => {
                    this.deleteReplayLive(data.row);
                  }}
                >
                  删除回放
                </el-button>
                {data.row.status === 0 ? (
                  <el-button
                    type="danger"
                    size="mini"
                    onClick={() => {
                      this.cancelLiving(data.row);
                    }}
                  >
                    取消直播
                  </el-button>
                ) : (
                  ""
                )}
                <el-button
                  type="danger"
                  size="mini"
                  onClick={() => {
                    this.deleteLiving(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_create: {},
      users_list: [],
      activity_img: "",
      dialogImg: false,
      live_category: `/api/common/file/upload/admin?category=${config.QYWX_LIVE_CATEGORY}`,
      dialogImageUrl: "",
      dialogVisible: false,
      activityFileList: [],
    };
  },
  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
  },
  mounted() {
    this.getDataList();
    this.getQywxUsersList();
  },
  methods: {
    getDataList() {
      this.$http.getLiveList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    // 获取最后一次直播数据
    getLastLiveList() {
      this.tableData = [];
      this.isLast = !this.isLast;
      if (this.isLast) {
        this.$http.getLastLiveList().then((res) => {
          if (res.status === 200) {
            var arr = [];
            arr.push(res.data);
            this.tableData = arr;
          }
        });
      } else {
        this.getDataList();
      }
    },
    getQywxUsersList() {
      this.$http.getQywxUsersList().then((res) => {
        if (res.status === 200) {
          this.users_list = res.data.userlist;
        }
      });
    },
    // 分页
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    deleteReplayLive(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http
          .deleteReplayLive({ living_id: row.living_id })
          .then((res) => {
            if (res.status === 200) {
              this.$message.success("删除成功");
              this.getDataList();
            }
          });
      });
    },
    cancelLiving(row) {
      this.$confirm("直播马上开始是否取消", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.cancelLiving({ living_id: row.living_id }).then((res) => {
          if (res.status === 200) {
            this.$message.success("删除成功");
            this.getDataList();
          }
        });
      });
    },
    deleteLiving(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.deleteLiving(row.id).then((res) => {
          if (res.status === 200) {
            this.$message.success("删除成功");
            this.getDataList();
          }
        });
      });
    },
    createLiving() {
      this.form_create = {
        living_duration: 3600,
        type: 0,
        remind_time: 3600,
        activity_cover_image: "",
        activity_share_image: "",
        activity_detail_image_list_2: [],
      };
      this.activityFileList = [];
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    updateLiving(row) {
      this.form_create = row;
      this.dialogTitle = "updateData";
      this.activityFileList = JSON.parse(row.activity_detail_image_list_2).map(
        (item) => {
          return {
            url: item,
          };
        }
      );
      this.dialogCreate = true;
    },
    onCreate() {
      if (!this.form_create.living_start_at) {
        this.$message.error("请选择直播开始时间");
        return;
      }
      if (!this.form_create.theme) {
        this.$message.error("请填写直播标题");
        return;
      }
      if (!this.form_create.anchor_userid) {
        this.$message.error("请填写主播用户ID");
        return;
      }
      let msg = this.$message.success("请稍后...");
      // form.remind_time = form.remind_time * 60;
      // form.living_duration = form.living_duration * 60;
      var form = JSON.parse(JSON.stringify(this.form_create));
      if (this.dialogTitle === "addData") {
        this.$http.craeteLiving(form).then((res) => {
          if (res.status === 200) {
            this.$message.success("创建成功");
            this.getDataList();
            this.dialogCreate = false;
            msg.close();
          }
        });
      } else {
        this.$http.updataLiving(form).then((res) => {
          if (res.status === 200) {
            this.$message.success("修改成功");
            this.getDataList();
            this.dialogCreate = false;
            msg.close();
          }
        });
      }
    },
    // 上传活动直播封面
    getActivityCover(e, callback) {
      // type	媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件(file)
      var that = this;
      let file = e.target.files[0];
      var data = new FormData();
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (e) => {
        that.activity_img = e.target.result;
      };
      data.append("type", "image");
      data.append("media", file);
      this.$http.getUploadMediaId(data).then((res) => {
        if (res.status === 200) {
          callback(res.data.media_id);
        }
      });
    },
    // 根据media_id获取图片内容
    getImgForMediaId(media_id, callback) {
      var data = "";
      this.$http.getImgForMediaId(media_id).then((res) => {
        if (res.status === 200) {
          data =
            "data:image/png;base64," +
            btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            );
        }
        callback(data);
      });
    },
    queryMiediaId1(id) {
      let msg = this.$message.success("请稍后...");
      this.getImgForMediaId(id, (e) => {
        msg.close();
        this.dialogImg = true;
        this.activity_img = e;
      });
    },
    handleSuccessCover(response) {
      this.form_create.activity_cover_image = response.url;
    },
    handleSuccssShare(response) {
      this.form_create.activity_share_image = response.url;
    },
    handleSuccessList(response) {
      this.activityFileList.push(response);
      let formImgList = [];
      this.activityFileList.forEach((item) => {
        formImgList.push(item.url);
      });
      this.form_create.activity_detail_image_list_2 = formImgList;
    },
    handleRemove(file, fileList) {
      this.activityFileList = fileList;
      const list = [];
      this.activityFileList.forEach((item) => {
        list.push(item.url);
      });
      this.form_create.activity_detail_image_list_2 = list;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    beforeSuccessCover(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      return isLt2M;
    },
  },
};
</script>

<style scoped lang="scss">
img {
  width: 148px;
  height: 148px;
}
</style>
