<template>
  <el-container>
    <el-header class="div row">
      <div class="div row">
        <div class="title">动态列表</div>
        <div class="title_number">
          <div>
            当前页面共(
            <i>{{ tableData.length }}</i>
            )条数据
          </div>
        </div>
        <div class="add-build">
          <el-button type="primary" @click="createDynamic">添加动态</el-button>
        </div>
      </div>
      <div class="div row">
        <el-input
          @change="onChange"
          v-model="input"
          placeholder="搜索相关楼盘动态"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <myForm :form_create="form_create_obj" @onClick="onClick"></myForm>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import myForm from "@/components/components/my_form";
export default {
  name: "dynamic_list",
  components: {
    myPagination,
    myTable,
    myForm,
  },
  data() {
    return {
      input: "",
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      project_id: "",
      table_header: [
        { prop: "title", label: "标题" },
        { prop: "created_at", label: "添加时间" },
        {
          label: "操作",
          width: "400",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  icon="el-icon-edit"
                  size="mini"
                  type="success"
                  onClick={() => {
                    this.updataCompany(0, data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  size="mini"
                  icon="el-icon-delete"
                  type="danger"
                  onClick={() => {
                    this.handleDelete(0, data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      dialogCreate: false,
      form_create_obj: {
        model: {},
        inline: false,
        label_width: "100",
        formLabel: [
          {
            label: "动态标题：",
            model: "title",
            placeholder: "请输入标题内容",
            inputWidth: "400px",
            type: "input",
          },
          {
            label: "动态内容：",
            model: "content",
            placeholder: "请输入动态内容",
            inputWidth: "400px",
            inputType: "textarea",
            inputRows: "4",
            type: "input",
          },
        ],
      },
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
    };
  },
  mounted() {
    this.project_id = this.$route.query.id;
    this.getDataList();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.$http
        .companyProjectNews(
          this.project_id,
          this.params.currentPage,
          this.input
        )
        .then((res) => {
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    goBack() {
      this.$router.back();
    },

    onChange() {
      this.search();
    },
    // 搜索相关信息
    search() {
      this.params.current_page = 1;
      this.getDataList();
    },
    // 点击添加
    handleDelete(index, row) {
      this.$confirm("此操作将删除该动态, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.companyDeleteNews(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "success",
            message: "已取消删除",
          });
        });
    },
    updataCompany(index, row) {
      this.$http.companyQueryNews(row.id).then((res) => {
        if (res.status === 200) {
          this.form_create_obj.model = res.data;
          this.dialogTitle = "updateData";
          this.dialogCreate = true;
        }
      });
    },
    createDynamic() {
      this.form_create_obj.model = {
        build_id: this.project_id,
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    onClick(e) {
      if (this.dialogTitle === "addData") {
        this.$http.companyUploadNews(e.model).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.companyUpdataNews(e.model).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-header {
// 	padding-top: 45px;
// 	height: 100px !important;
// }
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
// .el-main {
//   padding: 0;
// }
</style>
