<template>
  <div class="pages" v-fixed-scroll="62">
    <template v-if="is_tabs !== 'wxwork'">
      <!--:is 的作用：会将div标签转换成 currentView 变量绑定的这个组件-->
      <!-- <div :is="is_tabs" keep-alive></div> -->
      <div id="filter">
        <div class="div b-tabs row">
          <customTabs ref="customTabs" v-model="params.c_type2" @change="handleCustomTabChange"
            @changeOut="handleCustomTabChangeOut" :type="1" :content-loaded="!is_table_loading">
            <div v-for="(date, index) in list_tabs" :key="index" @click="changeTab(date, index)"
              :class="{ isactive: params.c_type2 == date.id }" class="b-t-item">
              <el-badge v-if="no_follow_number && date.id == 3" :value="no_follow_number">
                <div class="date_value">{{ date.title }}</div>
              </el-badge>
              <el-badge v-if="informtiondata && date.id == '4_12'" :value="informtiondata">
                <div class="date_value">{{ date.title }}</div>
              </el-badge>
              <div class="date_value" v-else>
                {{ date.title }}

                <template v-if="index == 2">
                  <el-popover v-model="is_pullDown" placement="bottom" width="150" trigger="click">
                    <div class="screen-type">
                      <div class="screen-type-content" v-for="list in screen_list_type" :key="list.id"
                        @click="changeScreenType(list, date)">
                        {{ list.title }}
                      </div>
                    </div>
                    <span slot="reference">
                      <span @click="showTypeStatus($event)" :class="date.is_select === true ? 'el-icon-caret-bottom' : null">
                      </span>
                    </span>
                  </el-popover>
                </template>

                <template v-if="index == 3">
                  <el-popover v-model="participateDown" placement="bottom" width="150" trigger="click">
                    <div class="screen-type">
                      <div class="screen-type-content" v-for="list in participate_in_list" :key="list.id"
                        @click="participate_inType(list, date)">
                        {{ list.title }}
                      </div>
                    </div>
                    <span slot="reference">
                      <span @click="showTypeStatus1($event)" :class="date.is_select === true ? 'el-icon-caret-bottom' : null">
                      </span>
                    </span>
                  </el-popover>
                </template>
              </div>
            </div>
          </customTabs>
        </div>
        <!-- 检索条件 -->
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <div class="head-list" style="margin-left: 0px;" :class="{'t-select-group':datetime_type}">
              <el-select class="crm-selected-label" clearable v-model="datetime_type" placeholder="时间类型"
               :style="{
                  minWidth: '30px',
                  width: getSelectWidth(customerLabelList),
                }" @change="time_type">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-select v-model="params.date_sort" class="crm-selected-label short" style="width: 78px;"
                @change="handleSearch" v-show="datetime_type">
                <el-option label="降序" :value="1"></el-option>
                <el-option label="升序" :value="2"></el-option>
              </el-select>
            </div>
             <!-- 时间检索 -->
            <div class="block head-list">
              <!-- <span class="demonstration">带快捷选项</span> -->
              <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                @change="onChangeTime">
              </el-date-picker>

            </div>
            <!-- 客户来源 -->
            <div class="head-list" style="margin-top: 10px;">
              <el-cascader class="crm-selected-label"
              size="small " 
               :style="{
                 minWidth: '20px',
                 width: '165px',
               }" v-model="Source_statusvalue" placeholder="客户来源" :options="source_list" @change="Source_status"
                :props="{
                  label: 'title',
                  value: 'id',
                  children: 'children',
                  checkStrictly: false,
                  emitPath: false,
                  multiple:true,
                }" clearable
                collapse-tags>
              </el-cascader>
            </div>
            <!-- 客户状态 -->
            <div class="head-list">
              <el-cascader
              size="small " class="crm-selected-label" v-model="customer_statusvalue" placeholder="客户状态"
                :options="tracking_list" :style="{
                  minWidth: '20px',
                  width: '165px',
                }" :props="{
                  value: 'id',
                  label: 'title',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }" collapse-tags clearable  @change="onClickType($event, 2)"></el-cascader>
            </div>
            <!-- <div v-else class="head-list">
              <el-select class="crm-selected-label" clearable v-model="customer_statusvalue" placeholder="客户状态"
               :style="{
                 minWidth: '20px',
                 width: '130px',
               }" @change="customer_status">
                <el-option v-for="item in tracking_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div> -->
            <!-- 客户类型 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="typeLabel_value" placeholder="客户类型"
               :style="{
                 minWidth: '20px',
                 width: '130px',
               }" @change="client_type">
                <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 客户等级 -->
            <div class="head-list">
              <!-- <el-select class="crm-selected-label" clearable v-model="grade_value" placeholder="客户等级"
               :style="{
                 minWidth: '20px',
                 width: '130px',
               }" @change="customer_grade">
                <el-option v-for="item in level_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select> -->
              <el-cascader
              size="small " class="crm-selected-label" v-model="grade_value" placeholder="客户等级"
                :options="level_list" :style="{
                  minWidth: '20px',
                  width: '130px',
                }" :props="{
                  value: 'id',
                  label: 'title',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }" collapse-tags clearable @change="customer_grade"></el-cascader>
            </div>
            <!-- 绑定企微 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="typeqiwei_value" placeholder="绑定企微"
               :style="{
                  minWidth: '20px',
                  width: '130px',
                }" @change="clientqiwei_type">
                <el-option v-for="item in bind_list" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 通话状态 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="customerstatus_value" placeholder="通话状态"
               :style="{
                  minWidth: '20px',
                  width: '130px',
                }" @change="customerstatus_type">
                <el-option v-for="item in statusdata_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 带看状态 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="showstatus_value" placeholder="带看状态" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 9)">
                <el-option v-for="item in showstatus_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 成员 -->
            <div class="head-list" v-if="mymember">
              <el-cascader class="inp_no_border1 my-cascader" size="small" v-model="member_value"
                :options="member_listNEW" clearable filterable placeholder="成员"
                 :style="{
                     minWidth: '20px',
                     width: '110px',
                   }" :props="{
                   label: 'user_name',
                   value: 'id',
                   children: 'subs',
                   checkStrictly: true,
                 }" @change="loadFirstLevelChildren"></el-cascader>
            </div>
          </div>
          <!-- <div class="bottom-border div row" style="padding-top: 24px">
            <span class="text">客户状态：</span>
            <myLabel
              labelKey="title"
              :arr="tracking_list"
              :activeIndex="tracking_index"
              @onClick="onClickType($event, 2)"
            ></myLabel>
          </div> --> 
          <div class="bottom-border" style="padding-top: 24px;border-bottom: 0px dashed #e2e2e2;">
            <!-- <span class="text">客户标签：</span> -->
            <div class="label_list div row">
              <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="clearlabel">
                客户标签
              </span>
              <div v-for="item in label_list" :key="item.id" class="label_item">
                    <el-dropdown trigger="click" @command="handleCommand($event,item)">
                      <div class="namedata">
                        <span class="selectedname">{{item.name}}<i class="el-icon-arrow-down el-icon--right"></i></span>
                      </div>
                      <el-dropdown-menu slot="dropdown" style="max-height: 300px !important;overflow-y: auto !important;">
                        <el-dropdown-item v-for="(label, index) in item.label" :key="index"
                        :command="label">{{ label.name }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
              </div>
            </div>
            <div class="alllabeldatastyle">
              <div class="div row">
                <div class="labeldatastyle" v-for="(arr,index) in labeldata" :key="index">
                <el-tag type="info">{{ arr.title+"/"+arr.name }}
                  <i class="el-icon-error" style="font-size:15px;" @click="dellabeld(index,arr.id)"></i></el-tag>
              </div>
              </div>
              <div class="labelerr">
                <el-button v-if="labeldata.length" type="primary" size="mini" icon="el-icon-delete"
                @click="clearlabel"></el-button>
              </div>
            </div>
          </div>
          <!-- <div v-else class="bottom-border div row" style="padding-top: 24px;border-bottom: 0px dashed #e2e2e2;"> -->
            <!-- <span class="text">客户标签：</span> -->
            <!-- <div class="label_list div row">
              <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="getAllLabelList">
                客户标签
              </span>
              <div v-for="item in label_list" :key="item.id" class="label_item">
                <el-select class="selected-label" v-model="customerLabelList[item.id]" @change="changeCustomerLabel"
                  :placeholder="item.name"
                   :style="{
                      minWidth: '40px',
                      width: getSelectWidth(item, customerLabelList[item.id]),
                      background: changeParentLabel == item.id ? '#E8F1FF' : '',
                    }">
                  <el-option v-for="arr in item.label" :key="arr.id" :label="arr.name" :value="[arr.id, arr, item]">
                  </el-option>
                </el-select>
              </div>
            </div>
          </div> -->
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="content-box-crm" style="margin-bottom: 60px">
        <div class="table-top-box table-top-box-abs div row" :class="{ fixed: scrollTop > topHeight }" id="stickyId">
          <div class="t-t-b-left div b-tabs row" style="width: 100%">
            <el-input :placeholder="placeholder" v-model="select_params.keywords" class="input-with-select"
              style="width: 300px" clearable size="small" @keyup.enter.native="handleKeywordSearch"
              @clear="clearSelectKeyword">
              <el-select v-model="select_params.type" @change="changeSelectParams" slot="prepend" placeholder="请选择"
                size="small" style="width: 100px">
                <el-option label="手机号码" :value="1"></el-option>
                <!-- <template v-if="recordstype != 'xiansuo'"> -->
                <el-option label="客户编号" :value="2"></el-option>
                <el-option label="线索内容" :value="3"></el-option>
                <el-option label="客户姓名" :value="4"></el-option>
                <el-option label="跟进内容" :value="5"></el-option>
                <el-option label="归属地" :value="6"></el-option>
                <!-- </template> -->
              </el-select>
            </el-input>
          </div>

          <div class="t-t-b-right" :class="{ abs: is_small_system, show: show_right }">
            <el-button type="text" size="mini" class="el-icon-d-arrow-left" id="myButton" v-show="myButton1"
              @click="leftla"></el-button>

            <el-button style="font-size: 14px" @click="push_customerA" type="primary" size="mini"
              class="btn el-icon-plus">录入客户</el-button>
            <el-button style="font-size: 14px" v-if="is_show_upload" @click="getFile" type="primary"
              size="mini">导入</el-button>
            <!-- <el-popover v-model="transfer_type" placement="bottom" width="150" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="onClickCus(item)">
                  {{ item.name }}
                </div>
              </div>
              <el-button class="popover-f-btn" type="primary" size="mini" slot="reference">
                转交
              </el-button>
            </el-popover> -->
            <el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="onClickCus(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" @click="getCrmDepartmentList" class="search_loudong div row align-center">
                <div class="seach_value">操作</div>
                <div class="sanjiao" :class="{ transt: transfer_type }"></div>
              </div>
            </el-popover>
            <el-button v-show="buttonhidden" style="font-size: 14px; margin-left: 10px" type="primary"
              @click="setCustomerLabel" size="mini">
              设置标签
            </el-button>
            <el-button v-if="false" type="primary" size="mini">客户查重</el-button>
            <el-button v-show="buttonhidden" plain icon="el-icon-setting" size="mini"
              style="margin-left: 10px;font-size: 14px" @click="openCustomTableColumnSetting">自定义列表</el-button>
            <el-button type="danger" size="mini" style="margin-left: 10px" v-if="show_shitu"
              @click="toShitu">经营视图</el-button>
            <el-button type="text" size="mini" class="el-icon-d-arrow-right" id="myButton1" v-show="myButton"
              @click="Rightdrag" style="margin-left: 10px"></el-button>
          </div>
        </div>
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="table_header" select tooltipEffect="light"
          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
          @selection-change="selectionChange" :sort_change="sortChangeData" table-name="crm_customer_my_list"
          ref="myTable"></myTable>
        <div class="page_footer flex-row items-center">
          <div class="page_footer_l flex-row flex-1 items-center">
            <div class="head-list">
              <el-button type="primary" size="small" @click="empty">清空</el-button>
            </div>
            <div class="head-list">
              <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
            </div>
            <div class="head-list">
              <el-button type="primary" size="small" @click="Topping(1)">置顶</el-button>
            </div>
            <div class="head-list" v-if="showgorders">
              <el-select v-model="gordersvalue" placeholder="请选择状态" size="small" style="width: 103px;"
                @change="changegordersvalue">
                <el-option v-for="item in Receivingorders" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div>
            <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
              :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
              @current-change="onPageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
    </template>

    <!-- <wxwork v-if="is_tabs === 'wxwork'"></wxwork> -->
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel"
      :close-on-click-modal="false">
      <!-- <myForm
        @clsoe="is_push_customer = false"
        :data1="n_client_field"
        :data2="n_company_field"
        :form="form"
        :form1="form1"
        @onClick="onClickForm"
      ></myForm> -->
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"
                @blur="Validationphone(domain.mobile)"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <!-- <el-select
              v-if="website_id!=1"
                style="width: 100%"
                v-model="push_form.source_id"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in sourceLabel"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                >
                </el-option>
              </el-select> -->
              <el-cascader
               :style="{
                  minWidth: '20px',
                  width: '100%',
                }" v-model="source_idvalue" placeholder="请选择" :options="sourceLabel" @change="sourceLabel_status"
                :props="{
                  label: 'title',
                  value: 'id',
                  children: 'children',
                  checkStrictly: true 
                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                  :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt="" @click="() => {push_form.sex = item.id;} " />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <el-select v-model="push_form.level_id" placeholder="请选择客户等级" style="width: 235px;">
              <el-option v-for="item,index in level_list" :key="index" :label="item.title+'级'"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户标签：">
            <div class="input-box">
              <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签"
                :options="label_default_list" :props="{
                                  value: 'id',
                                  label: 'name',
                                  children: 'label',
                                  emitPath: false,
                                  multiple: true,
                                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="所在城市：" v-if="is_show_city==1">
            <div class="block">
              <el-cascader style="width: 240px;" v-model="provincesvalue" clearable :props="{
                                      value: 'id',
                                      label: 'name',
                                      children: 'children',
                                      emitPath: true,
                                      multiple: false,
                                    }" :options="provincesoptions" @change="provincesChange"></el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <t-crm-project-select multiple allow-create default-first-option value-key="name" placeholder="请选择或输入"
                v-model="push_form.intention_community" width="100%" />
            </div>
          </el-form-item>
          <!-- <el-form-item label="意向区域：">
            <div class="row input-box div">
              <el-input
                placeholder="请输入"
                v-model="push_form.intention_street"
              ></el-input>
            </div>
          </el-form-item> -->
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
          定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_follow_dialog" title="跟进记录">
      <el-table v-loading="is_follow_loading" :data="is_follow_data" border>
        <el-table-column label="ID" prop="client_follow_id"></el-table-column>
        <el-table-column label="跟进类型" prop="type_title"></el-table-column>
        <el-table-column label="跟进内容" prop="content"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="is_follow_params.total" :page-size="is_follow_params.per_page" :current-page="is_follow_params.page"
        @current-change="onPageChangeQwFollow">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_qw_dialog" title="企微客户">
      <el-input v-model="qw_params.keywords" placeholder="请输入" style="width: 200px"></el-input>
      <el-button type="primary" @click="onQwSearch">搜索</el-button>
      <el-button style="margin-left: 10px" type="primary" @click="onClickGetPull">拉取客户</el-button>
      <el-table v-loading="is_qw_loading" style="margin-top: 10px" :data="qw_tableData" border>
        <el-table-column label="ID" width="100" prop="id"> </el-table-column>
        <el-table-column label="用户" width="200">
          <template slot-scope="scope">
            <span class="name" style="margin: 0 4px">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户类型" v-slot="{ row }">
          <span class="label">{{
                      row.type == 1 ? "微信用户" : "企微用户"
                      }}</span>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-link type="primary" @click="onBindUser(scope.row)">绑定</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="qw_params.total" :page-size="qw_params.per_page" :current-page="qw_params.page"
        @current-change="onPageChangeQw">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_dialog_upload" title="导入" :before-close="cancels">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow red">
        [重要提示]每次导入前请下载最新模板表格整理客户信息后再执行导入。
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <div class="flex-row">
        <!-- 是否覆盖 -->
        <el-select style="width: 200px; margin-bottom: 10px" v-model="upload_form.type" placeholder="请选择是否覆盖数据">
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select>
        <!-- 选择分类 -->
        <el-select style="width: 150px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.type_id"
          placeholder="请选择分类" clearable>
          <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
        <!-- 选择客户来源 -->
        <el-cascader :style="{
                      width: '200px',
                    }" style=" margin-bottom: 10px; margin-left: 12px" v-model="source_idvalue" placeholder="请选择客户来源"
          :options="sourceLabel" @change="sourceLabelimport" :props="{
                          label: 'title',
                          value: 'id',
                          children: 'children',
                          checkStrictly: true 
                        }" clearable>
        </el-cascader>
      </div>
      <div class="flex-row">
        <!-- 选择成员 -->
        <el-input ref="focusMember" class="import-member" placeholder="请选择维护人" v-model="uploadAdmin_id"
          style="width: 200px; display: block" @focus="focusSelete">
          <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <el-cascader style="width: 362px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.label" clearable
          placeholder="请选择标签" :options="label_list" :props="{
                      value: 'id',
                      label: 'name',
                      children: 'label',
                      emitPath: false,
                      multiple: true,
                    }">
        </el-cascader>
      </div>
      <!-- 客户备注线索 -->
      <div class="clueRemark">
        <el-input ref="focusMember" class="import-member" placeholder="请选择录入人" v-model="uploadAdmin_id1"
          style="width: 200px; display: block" @focus="focusSelete1">
          <i v-show="uploadAdmin_id1 != '' && uploadAdmin_id1 != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <el-input type="textarea" :rows="2" placeholder="请输入客户备注线索" v-model="upload_form.remark"
          style="margin-left: 11px">
        </el-input>
      </div>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
        >添加文件</el-button
      > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancels">取 消</el-button>
        <el-button type="primary" @click="startImport" :loading="is_loading">开始导入</el-button>
      </span>
    </el-dialog>
    <!-- <input
      v-if="is_dialog_upload"
      type="file"
      ref="file"
      style="display: none"
      v-on:change="handleFileUpload($event)"
    /> -->

    <el-upload :limit="1" class="upload-demo" :headers="myHeader" :action="user_avatar"
      :on-success="handleSuccessAvatarTemporary" ref="upload" style="display: none" v-if="is_dialog_upload">
      <el-button class="el-icon-download" size="small">本地上传</el-button>
    </el-upload>
    <el-dialog title="客户标签" :visible.sync="show_Customer_label" width="660px">
      <div class="dialog_label_box">
        <div v-for="(item, index) in labels_list" :key="item.id">
          <div class="labelname">{{ item.name }}</div>
          <div class="lables-box div row">
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.label" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="allbtn">
          <div>
            <template v-if="!is_fast">
              <el-radio v-model="confirm_batch_list.type" label="1">覆盖</el-radio>
              <el-radio v-model="confirm_batch_list.type" label="2">追加 </el-radio>
            </template>
          </div>
          <div>
            <el-button @click="show_Customer_label = false">取 消</el-button>
            <el-button v-if="is_fast" type="primary" :loading="is_loading" @click="confirmFastSelected">确 定</el-button>
            <el-button v-else type="primary" :loading="is_loading" @click="confirmSelected">确 定</el-button>
          </div>
        </div>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_add_member" width="660px" title="选择成员" append-to-body>
      <div class="member">
        <multipleTree ref="memberLists" v-if="show_add_member" :list="memberList" :defaultValue="selectedIds"
          @onClickItem="selecetedMember" :defaultExpandAll="false">
        </multipleTree>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_add_member1" width="660px" title="选择成员" append-to-body>
      <div class="member">
        <multipleTree ref="memberLists" v-if="show_add_member1" :list="memberList" :defaultValue="selectedIds"
          @onClickItem="selecetedMember1" :defaultExpandAll="false">
        </multipleTree>
      </div>
    </el-dialog>
    <el-dialog title="转交至公海" :visible.sync="show_transfer_public" width="400px">
      <el-form ref="form" :model="transfer_public_params" label-width="70px">
        <el-form-item label="原因：">
          <el-input type="textarea" placeholder="请输入内容" v-model="transfer_public_params.content" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_transfer_public = false">取 消</el-button>
        <el-button type="primary" @click="confirmTransfer" :loading="confirmLoading">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_transfer_customer" :title="changetitle">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border @sort-change="sortChange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum&&changetitle=='转交客户'"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row)">转交客户</el-link>
          </template>
        </el-table-column>


        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>
    <el-dialog title="转交至公海" :visible.sync="show_transfer_right" width="400px">
      <el-form ref="form" :model="transfer_public_params" label-width="70px">
        <el-form-item label="原因：">
          <el-input type="textarea" placeholder="请输入内容" v-model="transfer_public_params.content" show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_transfer_right = false">取 消</el-button>
        <el-button type="primary" @click="confirmTransfer(1)" :loading="confirmLoading">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="right_transfer_customer"  :title="changetitle">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border @sort-change="sortChange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum&&changetitle=='转交客户'"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row,1)">转交客户</el-link>
          </template>
        </el-table-column>


        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row,1)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>

    <!-- 快速编辑客户维护资料模态框 -->
    <my-Maintenance v-if="show_cus_Edit" :show_cus_Edit="show_cus_Edit" :source_list="ponent_maintain_source"
      :level_list="ponent_maintain_level" :type_list="ponent_maintain_type" :label_list="ponent_maintain_label"
      :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit" @submitMaintain="submitMaintain">
    </my-Maintenance>

    <!-- 快速查看客户手机号模态框 -->
    <my-LookTel v-if="show_look_Tel" :show_look_Tel="show_look_Tel" :ponent_Tel_data="ponent_Tel_data"
      :nowDialData="nowDialData" @fastCloseTel="fastCloseTel" @fastSubmitTel="fastSubmitTel"
      :selfID="selfID"></my-LookTel>
    <!-- 快速提交客户审批 -->
    <my-Examine v-if="show_Examine_dialog" :show_Examine_dialog="show_Examine_dialog"
      :ponent_Examine_data="ponent_Examine_data" :ponent_Examine_stutas="ponent_Examine_stutas"
      :ponent_Examine_type="Examine_type" :AllDepartment="AllDepartment" @closeExamine="closeExamine"
      @submitExamineAfter="submitExamineAfter"></my-Examine>
    <!-- 快速跟进客户内容模态框 -->
    <myFollowUp v-if="show_Follow_dialog" :show_Follow_dialog="show_Follow_dialog"
      :ponent_Follow_data="ponent_Follow_data" @addFollowSuccess="addFollowSuccess" @closeFollow="closeFollow">
    </myFollowUp>
    <!-- 客户信息右侧边栏插件 -->
    <div class="kehuxinxi">
      <el-drawer :visible.sync="drawer" title="客户信息" v-cloak :modal="false" :direction="direction"
        :before-close="handleClose" custom-class="kehu">
        <div class="QuickEdit">
          <Drawerdetails ref="childRef" :ids="ids" :type="seas" :types="seastype" @getDataListA="getDataList" :recordName.sync="recordName">
          </Drawerdetails>
        </div>
        <div class="demo-drawer__footer">
          <div class="drawerfoot flex-row">
            <div class="flex-row" style="margin-left: 15px;">
              <div class="crmstatus">客户状态: {{customstatus}}</div>
              <!-- <el-button type="primary" size="small" style="margin-left:20px" v-if="ClaimCustomers" @click="onClickGet(c_detail.id)">立即认领</el-button> -->
              <div>
                <el-button type="primary" size="small" style="margin:0px 20px 0px 20px " plain @click="TopA(ids)">{{ticky_post
                                  }}</el-button>
              </div>

              <el-input size="small" placeholder="关键词" v-model="followsearch" class="input-with-select short"
              clearable v-if="recordName =='Follow'" @clear="getfollowsearch">
                <el-button slot="append" icon="el-icon-search" @click="getfollowsearch"></el-button>
              </el-input>
            </div>
            <div style="margin-right: 22px;">
              <el-popover v-model="Transfer_right" placement="top" width="150" trigger="click">
                <div class="f-list">
                  <div class="f-item" v-for="item in cus_list" :key="item.id" @click="onClickright(item)">
                    {{ item.name }}
                  </div>
                </div>
                <el-button v-if="transmitstatus" style="margin-right: 10px;" type="primary" size="small" slot="reference">
                  操作
                </el-button>
              </el-popover>
              <el-button type="warning" size="small" style="width: 100px;" @click="sureright">确定</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>

    <tCustomTableCloumnSetting table-name="crm_customer_my_list" ref="tCustomTableCloumnSetting"
      v-if="dialogs.tCustomTableCloumnSetting" />
    <!-- 导入客户插件 -->
    <div class="importdrawer">
      <el-drawer title="数据导入" :visible.sync="importdrawer" :direction="direction" :with-header="false">
        <div>
          <importinformation ref="importinformation" class="QuickEditA" :type_list="type_list" :sourceLabel="sourceLabel"
            :label_list="label_list" :memberList="memberList" @child-event="closesidebar" :typesof=2></importinformation>
        </div>
        <div class="isfooter">
          <div class="drawerfoot flex-row isfoot">
            <!-- <div> -->
            <el-button style="margin-right: 10px;" type="info" plain size="small" @click="importdrawer = false">
              取消
            </el-button>
            <el-button type="primary" size="small" style="width: 100px;" @click="Starting_import">开始导入</el-button>
            <!-- </div> -->
          </div>
        </div>
      </el-drawer>
    </div>
    <!-- 录入客户的插件 -->
    <div class="enterdrawer">
      <el-drawer title="录入客户" :visible.sync="enterdrawer" :direction="direction">
        <div>
          <entercustomer ref="enterinformation" class="QuickEditA" :type_list="type_list" :sourceLabel="sourceLabel"
            :label_list="label_list" :memberList="memberList" @child-event="enterdrawerfalse" :typesof=2
            :level_list="level_list" :typeLabel="typeLabel" :label_default_list="label_list"
            :radio1="radio1"></entercustomer>
        </div>
        <div class="isfooter">
          <div class="drawerfoot flex-row enterfoot">
            <div style="margin-left:20px;">
              <el-radio-group v-model="radio1" size="small">
                <el-radio-button v-for="item in radio1data" :key="item.id" :label="item.id">{{ item.name
                                  }}</el-radio-button>
              </el-radio-group>
            </div>
            <div>
              <el-button style="margin-right: 10px;" type="info" plain size="small" @click="enterdrawer=false">
                取消
              </el-button>
              <el-button type="primary" size="small" style="width: 100px;" v-loading="is_button_loading"
                @click="onClickForm">确定</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <!-- 修改客户资料的插件 -->
    <div class="enterdrawer">
      <el-drawer title="修改资料" :visible.sync="show_cus_EditA" :direction="direction">
        <div>
          <!-- v-if="show_cus_Edit" -->
          <myMaintenancecopy ref="myMaintenancecopy" class="QuickEditA" :show_cus_Edit="show_cus_EditA"
            :source_list="ponent_maintain_source" :level_list="ponent_maintain_level" :type_list="ponent_maintain_type"
            :label_list="ponent_maintain_label" :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit"
            @submitMaintain="submitMaintain">
          </myMaintenancecopy>
        </div>
        <div class="isfooter">
          <div class="drawerfoot flex-row enterfoot">
            <div style="margin-left:20px;">
              <!-- <el-radio-group v-model="radio1" size="small">
                <el-radio-button v-for="item in radio1data" :key="item.id" :label="item.id">{{ item.name
                                  }}</el-radio-button>
              </el-radio-group> -->
            </div>
            <div>
              <el-button style="margin-right: 10px;" type="info" plain size="small" @click="show_cus_EditA=false">
                取消
              </el-button>
              <el-button type="primary" size="small" style="width: 100px;" v-loading="is_button_loading"
                @click="onClickForm(1)">确定</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <scriptdetails ref="scriptdetails"></scriptdetails>
    <automatic ref="automatic"  @getDataList='getDataList'></automatic>
  </div>
</template>

<script>
// import my from "./components/my";
// import seas from "./components/seas";
// import wxwork from "./components/wxwork";
// import myForm from "./components/customer_form";
import myTable from "@/components/components/my_table";
// import myLabel from "./components/my_label.vue";
// import myCollapse from "./components/collapse";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import myMaintenance from "@/components/components/my_maintenance.vue";
import myMaintenancecopy from "@/components/components/my_maintenance_copy.vue";
import myLookTel from "@/components/components/my_lookTel.vue";
import myExamine from "@/components/components/my_Examine.vue";
import myFollowUp from "@/components/components/my_followUp.vue";
import config from "@/utils/config";
import Drawerdetails from "./components/Drawer_details.vue"
// import mySelect from "./components/my_select";
import tCustomTableCloumnSetting from "@/components/tplus/tCustomTableCloumn/setting.vue"
import TCrmProjectSelect from '@/components/tplus/tSelect/tCrmProjectSelect.vue';
import importinformation from '@/components/tplus/tSelect/importinformation.vue';
import entercustomer from '@/components/tplus/tSelect/entercustomer.vue';
import customTabs from '@/views/crm/custom_tab/components/tabs.vue';
import customTabsMixin from '@/views/crm/custom_tab/mixins/custom_tab.js';
import scriptdetails from '@/views/crm/share_follower/detailedinformation.vue'
import automatic  from '@/views/crm/share_follower/automatic_allocation.vue'
export default {
  name: "crm_customer_my_list",
  components: {
    // my,
    // seas,
    // myForm,
    // wxwork,
    myTable,
    // mySelect,
    // myLabel,
    // myCollapse,
    multipleTree,
    myMaintenance,
    myLookTel,
    myExamine,
    myFollowUp,
    Drawerdetails, tCustomTableCloumnSetting,
    importinformation,
    TCrmProjectSelect, customTabs,
    entercustomer,
    myMaintenancecopy,
    scriptdetails,
    automatic,
  },
  mixins: [customTabsMixin],
  data() {
    // const generateData =(headerlist)=> {
      
    //   console.log(headerlist);
    //     const data = [];
    //     for (let i = 1; i <= 15; i++) {
    //       data.push({
    //         key: i,
    //         label: `备选项 ${ i }`,
    //         // disabled: i % 4 === 0
    //       });
    //     }
    //     console.log(data);
    //     return data;
    //   };
    return {
      calculatecreate:"录入人",
      chnegjiaouser:"成交人",
      weihuuser:"维护人",
      diakanuser:"带看人",
      recordName: '',
      followsearch: '',
      transmitstatus:true,
      customstatus:"",
      seas:"my",
      drawer: false,
      seastype:"",
      ClaimCustomers:false,
      direction: 'rtl',
      ids: "",
      ticky_post:"",
      Transfer_right:false,
      headerlist:[],//表头数据
      // data: generateData(this.headerlist),
      value: [1, 4],
      checked3:false,
      is_tabs: "all",
      tabs: [
        {
          id: 1,
          name: "所有客户",
          desc: "all",
        },
        {
          id: 2,
          name: "我的客户",
          desc: "my",
        },
        {
          id: 3,
          name: "公海客户",
          desc: "seas",
        },
        // {
        //   id: 4,
        //   name: "企微客户",
        //   desc: "wxwork",
        // },
      ],
      radio1data:[ 
        {id: 1 ,name: "私客"},
        {id: 2 ,name: "公海"},
        // {id: 3 ,name: "流转客"}
      ],
      is_push_customer: false,
      push_form: {
        cname: "",
        source_id: "",
        source2_id:'',
        level_id: "",
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: [],
        label: "",
        // intention_street: "",
        remark: "",
      },
      source_idvalue:"",//录入客户的客户来源
      show_xiansuo_search: false,
      sex_list: [
        { id: 1, name: "nan" },
        { id: 2, name: "nv3" },
      ],
      other_mobile: [{ mobile: "" }],
      type_list: [],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date();
              const start = new Date(end);
              start.setDate(start.getDate() - 1);
              end.setDate(start.getDate());
              picker.$emit('pick', [start, end]);
            }
          },{
          text: '本周',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上周',
          onClick(picker) {
              const today = new Date(); // 获取当前日期
              const end = new Date(today); // 结束日期为当前日期
              const start = new Date(today); // 开始日期为当前日期
              const day = today.getDay(); // 获取当前是星期几
              const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
              const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
              end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
              // end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
              start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
              // start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
              // 将计算得到的时间范围传递给日期选择器
              picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上月',
          onClick(picker) {
            const end = new Date();
            end.setDate(0); // 获取上个月的最后一天
            end.setHours(23, 59, 59, 0);
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      timeValue: "",
      level_list: [],
      statusdata_list: [
        { id: 0, title: "全部" },
        { id: 1, title: "未联系" },
        { id: 2, title: "未接通" },
        { id: 3, title: "已接通" },
      ],
      source_list: [],
      source_index: 0,
      tracking_index: 0,
      type_index: 0,
      bind_index: 0,
      level_index: 0,
      statusdata_index: 0,
      time_index: 0,
      uploadAdmin_id: "", // 导入选择成员绑定值
      uploadAdmin_id1: "",
      show_add_member: false, //选择成员弹框控制
      show_add_member1: false,
      client_field: {
        // 获取客户字段
        type: 2,
      },
      n_client_field: {},
      n_company_field: {},
      form: {},
      form1: {},
      statistics_form: {},
      type: 1,
      //时间类型
      options: [
        { value: 0, label: '全部' },
        { value: 1, label: '创建时间' },
        { value: 2, label: '跟进时间' },
        { value: 3, label: '线索时间' },
        { value: 4, label: '更新时间' },
        { value: 5, label: '掉公时间' },
        { value: 6, label: '转公时间' },
        { value: 8, label: '带看时间' },
        { value: 10, label: '转交时间' },
      ],
      datetime_type:0,//时间类型
      Source_statusvalue:"",//客户来源
      customer_statusvalue: "",// 客户状态
      typeLabel_value: "",//客户类型
      typeqiwei_value:"",//绑定企微
      grade_value:"",//客户等级
      customerstatus_value:"",//通话状态
      showstatus_value: "",//通话状态
      showstatus_list: [
        { id: 0, title: "全部" },
        { id: 1, title: "未带看" },
        { id: 2, title: "已带看" },
        { id: 3, title: "有复看" },
      ],//1:未带看,2:已带看,3:有复看
      member_value:[],
       //成员
       member_listNEW: [
        {
          id: 5, 
          user_name: "全部",
          subs: []
        },
        {
          id: 1, user_name: "录入人",
          subs: []
        },
        {
          id: 2, user_name: "维护人",
          subs: []
        },
        {
          id: 3, user_name: "带看人",
          subs: []
        },
        {
          id: 4, user_name: "成交人",
          subs: []
        },
      ],
      mymember:false,//成员检索的显示
      tracking_list: [],
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      multipleSelection: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 2,
        date_style:0,
        // status: 0,
        c_type2: 0, // 认领状态 0：全部 1：已认领 2：已跟进 3：未跟进 4：私客 5：掉公
        department_id: 0,
        admin_id: 0,
        mobile: "",
        c_type6: 0,
        date_sort: 1,   //1=倒序,2=升序
      },
      is_table_loading: false,
      tableData: [],
      confirmLoading: false, // 加载动画
      table_header: [],
      // {!data.row.wxqy_id ? (
      //   <el-link
      //     onClick={() => {
      //       // 弹窗、客户id、调用获取企微用户方法
      //       this.is_qw_dialog = true;
      //       this.bind_qw.client_id = data.row.id;
      //       this.getWxworkData();
      //     }}
      //     type="primary"
      //   >
      //     绑定企微用户
      //   </el-link>
      // ) : (
      //   ""
      // )}
      is_follow_dialog: false,
      is_follow_data: [],
      is_follow_loading: false,
      is_follow_params: {
        page: 1,
        total: 0,
        per_page: 10,
      },
      qw_params: {
        page: 1,
        per_page: 10,
        keywords: "",
        total: 0,
        type: 2,
      },
      is_qw_loading: false,
      is_qw_dialog: false,
      qw_tableData: [],
      bind_qw: {
        client_id: "",
        openid: "",
      },
      label_list: [],
      multipleSelectionname: [],
      is_collapse: false,
      tracking_params: {
        type: 4,
      },
      is_dialog_upload: false,
      upload_form: {
        type: 1, // 导入类型(1:普通导入(自定义导入),2:抖音导入,3:快手导入,4:海豚导入)
        follow_id: "", //维护人id
        file: "", // 导入的文件
        type_id: "", // 客户类型id
        source_id: "", // 客户来源
        source2_id:'',
        label: "", // 标签:字符串格式，多个用逗号隔开
        remark: "", // 客户备注线索
        is_cover:0,//是否覆盖(0:否,1:是)
      },
      admin_params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
        type:1
      },
      admin_list: [],
      is_button_loading: false,
      pull_params: {
        next_user_cursor: 0,
        next_customer_cursor: "",
      },
      list_tabs: [
        { id: 0, title: "全部", is_select: false },
        { id: 1, title: "已认领", is_select: false },
        { id: 2, title: "已跟进", is_select: true },
        { id: 6, title: "我参与的", is_select: true },
        { id: "4_12", title: "流转客", is_select: false },
        { id: "4_11", title: "成交客", is_select: false },
      ],
      is_pullDown: false,
      participateDown: false,
      screen_list_type: [
        { id: 7, title: "已带看", is_select: true },
        // { id: 8, title: "已成交", is_select: true },
        { id: 3, title: "未跟进", is_select: true },
        { id: 4, title: "录入客户", is_select: true },
        { id: 5, title: "即将掉公", is_select: true },
      ],
      participate_in_list: [
        { id: "1_9", title: "我录入的", is_select: true },
        { id: "3_10", title: "我带看的", is_select: true },
        // { id: "4_11", title: "我成交的", is_select: true },
        { id: "5_12", title: "我转公的", is_select: true },
        { id: "6_13", title: "我掉公的", is_select: true },
        { id: "9_14", title: "共享给我的", is_select: true }, 
        { id: 15, title: "我共享的", is_select: true }
      ],
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过3000条",
        "标红标题对应内容为必填项，如不填写则过滤出去不进行导入",
        "同一个表格重复的手机号会导入1个客户，系统将按重复的顺序将客户线索内容追加到线索记录 ",
        "选择覆盖，导入的数据和已有数据重复时，会覆盖之前的数据，选择不覆盖则过滤掉重复数据",
      ],
      no_follow_number: "",
      informtiondata:"",
      is_show_upload: false, // 是否显示导入按钮
      is_show: 0, // 控制客户标签样式
      label_default_list: [], // 获取原始默认标签列表
      customerLabelList: {}, // 所选择的标签
      changeParentLabel: "", // 之前所选的上一级的标签
      is_all: true, // 控制客户标签，“全部”标签的样式
      show_Customer_label: false, // 控制客户标签模态框显示/隐藏
      labels_list: [], // 客户标签列表
      // 确定编辑客户标签的接口传参
      confirm_batch_list: {
        ids: "", // 客户id 多个用逗号隔开
        label: "", // 客户标签id 多个用逗号隔开
        type: "2", //添加标签时选择 2:追加 或 1:覆盖
      },
      selectedIds: [],
      memberList: [], // 全部成员列表
      datalist: [], // 全部部门人员
      transfer_type: false, // 显示/隐藏转交类型
      // 转交类型
      cus_list: [
        { id: 1, name: "转交到公海" },
        { id: 2, name: "转交到同事" },
        // { id: 4, name: "批量自动分配"},
        { id: 3, name: "复制到同事的流转客"},
      ],
      pop_depart: false, // 显示/隐藏部门搜索popover
      popdepart:false,
      AllDepartment: [], // 全部部门列表
      changetitle:"转交客户",
      is_transfer_customer: false, // 转交客户模态框
      show_transfer_right:false,
      right_transfer_customer: false, 
      c_id: "", // 转让客户id：多个用，隔开
      show_transfer_public: false, // 转移到公海模态框
      transfer_public_params: {
        ids: "", // 客户id 多个用，隔开
        content: "", // 转交公海原因
      },
      is_fast: false, // 是否是快速编辑标签
      fastEdit_params: {
        client_id: "", // 客户id
        label: "", // 标签id多个，隔开
      },
      show_tel_search: false, // 显示隐藏搜索电话popover
      buttonhidden: true,
      show_shitu: false,
      myButton: false,
      myButton1: false,
      is_small_system: false,
      show_right: false,
      selectedMember: "", // 选择搜索部门
      Not_duplicate_datalist: [], // 没有重复部门成员的容器
      filtrMember: [], // 选中部门后过滤出的部门成员
      selfID: "", // 登录后的个人id
      status_list: [], // 快速编辑客户状态数据容器
      copy_status_list: [], // 深拷贝客户状态
      getStutas_params: {
        type: 2,
      },
      is_loading: false, // loading加载动画
      show_cus_Edit: false, // 显示快速编辑客户维护资料模态框
      show_cus_EditA: false, // 显示快速编辑客户维护资料模态框
      ponent_maintain_data: {}, // 客户信息
      ponent_maintain_source: [], // 深拷贝客户来源列表
      ponent_maintain_level: [], // 深拷贝客户来源列表
      ponent_maintain_type: [], // 深拷贝客户类型列表
      ponent_maintain_label: [], // 深拷贝客户标签列表
      show_look_Tel: false, // 显示快速查看客户手机号模态框
      ponent_Tel_data: {}, // 客户信息
      website_id: "", // 获取站点信息
      show_Examine_dialog: false, // 显示快速提交客户审批模态框
      ponent_Examine_data: {}, // 提交审批客户信息
      ponent_Examine_stutas: {}, // 选择的要修改的状态
      Examine_type: 19, // 默认审批类型
      show_Follow_dialog: false, // 显示快速跟进客户模态框
      ponent_Follow_data: {}, // 客户信息
      nowDialData: {},
      user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_FILE_2}`,
      scrollTop: 0,
      topHeight: 330,
      //记录切换
      recordstype: 0,
      placeholder: "请输入手机号码",
      select_params: {
        type: 1,
        keywords: ""
      },
      shownum:false,
      multipleright:"",
      provincesoptions:[],//城市选择联动
      provincesvalue:[],//城市id
      is_show_city:"",
      dialogs: {
        tCustomTableCloumnSetting: false
      },
      title_status:"",//保存this.$route.query.status值
      importdrawer:false,//导入侧边栏
      isDataInited: false, // 是否已经初始化过数据
      searchParamsJoins: '',  //搜索参数拼接值
      enterdrawer:false,//录入客户的侧边栏
      radio1: 1,
      gordersvalue:1,//接单状态
      showgorders:true,//根据配置项控制的显示与否
      Receivingorders:[
        {value: 1,label: '正常接单'},
        {value: 0,label: '暂停接单'}
      ],
      labeldata:[],
      customerLabelListcopy: [], // 所选择的标签
      customerDetailChangedIds: [],
      // getpldata:[],//批量自动分配的成员id

    };
  },
  watch: {
    admin_list: {
      handler(newVal) {
        // console.log(newVal);
        // 根据数据变化判断是否显示数量列
        this.shownum = newVal.some(item => {
          if(item.number||item.number==0){
           return true // 监听对象内部属性的变化
          }else{
            return false
          }
        });
      },
    },
    title_status:{
      handler(newval){
        console.log(newval);
        this.params.c_type2 = parseInt(newval);
      }
    }
  },

  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    levelLabel() {
      return this.level_list.filter((item) => {
        return item.id > 0;
      });
    },
    sourceLabel() {
      return this.source_list.filter((item) => {
        return item.id > 0;
      });
    },
    typeLabel() {
      return this.type_list.filter((item) => {
        return item.id > 0;
      });
    }, 
  },
  created() {
    // 赋值website_id
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id;
    }
    let screenWidth = document.body.clientWidth
    if (screenWidth < 1355) {
      this.is_small_system = true
      this.myButton1 = true
    } else {
      this.is_small_system = false
      this.myButton1 = false
    }
    this.getPerssion()
    this.getjuesename()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
    this.getScreenWidth();
    
    this.getTypelist(() => {
      if(this.$route.query.shareuser==1){
        
        const participateId = '9_14', tabId = 6;
        this.params.c_type2 = tabId;
        const list = this.participate_in_list.find(e=>e.id == participateId);
        const date = this.list_tabs.find(e=>e.id == tabId);
        this.participate_inType(list, date);
      }else if(this.$route.query.c_type2==5){
        const participateId = 5, tabId = 2;
        this.params.c_type2 = tabId;
        const list = this.screen_list_type.find(e=>e.id == participateId);
        const date = this.list_tabs.find(e=>e.id == tabId);
        this.changeScreenType(list, date);
      }else{
        this.params.c_type2 = parseInt(this.$route.query.status) || 0;
        if(this.params.c_type2 ==11){
          this.params.c_type2  = "4_11"
        }
        if(this.params.c_type2 ==12){
          this.params.c_type2  = 0
        }
        if(this.params.c_type2 ==13){
          const participateId = '1_9', tabId = 6;
        this.params.c_type2 = tabId;
        const list = this.participate_in_list.find(e=>e.id == participateId);
        const date = this.list_tabs.find(e=>e.id == tabId);
        this.participate_inType(list, date);
        }

        //处理screen_list_type
        if(this.params.c_type2 == 5){
          let index = this.screen_list_type.findIndex(e=>e.id == this.params.c_type2);
          if(index !== -1){
            let tab = this.list_tabs.find(e => e.id == 2);
            if(tab){
              const firstTab = this.screen_list_type[index];
              let temp = {...tab};
              tab.id = firstTab.id;
              tab.title = firstTab.title;
              firstTab.id = temp.id;
              firstTab.title = temp.title;
            }
          }
        }


        if(this.$route.query.level){
          this.grade_value =  parseInt(this.$route.query.level)
          console.log(this.grade_value);
          this.params.level_id = this.grade_value
        } 
        let changecardParams = []
        if(this.$route.query.start){
          changecardParams.push(this.$route.query.start)
          changecardParams.push(this.$route.query.end)
          this.timeValue = changecardParams
          this.params.start_date = this.$route.query.start // 赋值开始时间
          this.params.end_date = this.$route.query.end // 赋值结束时间
        }
        if(this.$route.query.ids){
          this.datetime_type = 4
          this.params.date_style = 4
          this.member_value = [2,Number(this.$route.query.ids)]
          this.params.admin_type = 2
            this.params.admin_id = this.$route.query.ids
        }
        this.getDataList();
      }
    });

    this.getTrackingList();
    this.getLevelData();
    // this.getClientField();
    this.getSourceData();
    this.getLabelList();
    this.getAdmin();
    this.getCrmCustomerFollowNumber();
    this.informtionnew()
    // this.getStatistics();
    this.getadminUser();
    this.getStatus();
    // this.watch_head()
    this.tfybinding()
    this.MembersNEW()
    let page = document.querySelector('.main-content');


    const filter = document.getElementById("filter")

    this.topHeight = filter.offsetHeight + 120
    page.addEventListener('scroll', this.debounce(this.handleScroll, 20))

    eventBus.$on('customerDetailChange', (id) => {
      !this.customerDetailChangedIds.includes(id) && this.customerDetailChangedIds.push(id);
    })
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      const tabName = vm.$route.params.tabName;
      if (tabName) {
        const timer = setInterval(() => {
          if(vm.isDataInited){
            clearInterval(timer);
            vm.$refs.customTabs.setSelectedTabName(tabName);
          }
        }, 100)
      }
    })
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('scroll', this.handleScroll)
    window.addEventListener("beforeunload", function() {
     document.cookie = "value=; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    eventBus.$off('customerDetailChange')
   });
  },
  deactivated() {
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('scroll', this.handleScroll)
  },
  // 当该页面进入时触发
  async activated() {
    // if(this.website_id==109||this.website_id==176){
    //   this.$goPath("/crm_customer_my_list_copy")
    // }
    window.addEventListener('resize', this.handleResize);
    if (this.$route.query.status) {
      this.title_status = this.$route.query.status
      if(!this.params.c_type2){
        this.params.c_type2 = parseInt(this.$route.query.status);
      }
      // if(this.params.c_type2 ==11){
      //     this.params.c_type2  = "4_11"
      // }
      console.log(this.params.c_type2 );
      if( this.title_status == 13&&this.params.c_type2 == 13){
          this.params.c_type2  = "1_9"
      }
      if( this.title_status == 12&&this.params.c_type2 == "4_12"){
          this.params.c_type2  = 0
      }
      if( this.title_status == 11&&this.params.c_type2 == "4_12"){
          this.params.c_type2  = "4_11"
      }
      //this.getDataList();
    }
    // 判断是否要刷新数据
    if (this.$store.state.allowUpdate) {
      this.$store.state.allowUpdate = false;
      //this.getDataList(); // 刷新页面数据
    }
    
    if(this.customerDetailChangedIds.length && this.tableData.find(e => this.customerDetailChangedIds.includes(e.id))){
      this.customerDetailChangedIds = [];
      this.getDataList();
    }
  },
  filters: {
    // 计算过去时间天数
    getPastDay(val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
    // 计算跟客时间
    getFollowDay(val) {
      // 解决iOS时间格式问题
      if(val){
        val = val.replace(/-/g, "/");
        const currentDate = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差

      // const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      const timeDiff = currentDate - specifiedDate;
      // 将时间差转换为天数
      // (start, end) => Math.ceil(Math.abs(start-end)  / 86400000)
      const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      if (daysDiff == 0) {
        return "今天";
      } else {
        return daysDiff + "天";
      }
      }
    },
    // 计算带看次数
    getTakeLook(val) {
      switch (val) {
        case 1:
          val = "首看";
          break;
        case 2:
          val = "二看";
          break;
        default:
          if (val >= 3) {
            return "多次带看";
          }
          break;
      }
      return val;
    },
    // 是否是新客留资(24小时内新增的)
    getNewCustomer(val) {
      const currentDate = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      // 将时间差转换为天数
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      if (daysDiff == 0) {
        return "新客留资";
      } else {
        return null;
      }
    },
  },
  methods: {
    //获取角色名称
    getjuesename(){
      this.$http.getsetuprolename().then(res=>{
        if(res.status == 200){
          if(res.data.diy_create_name){
            //录入人
            this.calculatecreate = res.data.diy_create_name
          }
          if(res.data.diy_follow_name){
            //维护人
            this.weihuuser = res.data.diy_follow_name
          }
          if(res.data.diy_take_name){
            //带看人
            this.diakanuser = res.data.diy_take_name
          }
          if(res.data.diy_deal_name){
            //成交人
            this.chnegjiaouser = res.data.diy_deal_name
          }
          this.storingtables()
        }
      })
    },
    //渲染表格
    storingtables(){
      this.table_header = [
          // {
        //   prop: "id",
        //   label: "ID",
        //   width: "80px",
        // },

        {
          label: "客户名称", name: 'cname',
          width: "320px",
          fixed: "left",
          sortable: "custom",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-head"
                  onClick={() => {
                    this.Topping(data.row.id);
                  }}>
                  <el-popover
                    placement="right-start"
                    width="10"
                    trigger="hover"
                    content="点击取消置顶">
                    {data.row.order ? <div class="client-top" slot="reference">
                      <span>顶</span>

                      <img
                        src="https://img.tfcs.cn/backup/static/admin/crm/static/custom/subscript.png"
                        alt=""
                      />
                    </div> : ""}
                  </el-popover>
                </div>
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {data.row.cname ? (
                      <el-popover
                        placement="top-start"
                        width="200"
                        trigger="hover"
                      >
                        <div style="align-items: center;white-space: nowrap;">
                          <div>
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>

                          <div>客户名称：{data.row.cname}</div>
                        </div>
                        <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                              this.Quick_Edit(data.row);
                            }}>
                          {data.row.cname}
                        </div>
                      </el-popover>
                    ) : (
                      ""
                    )}
                    {data.row.sex
                      ?
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                      :
                      ""
                    }
                  </div>
                  <div class="cus-box-foot flex-row">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : null}
                    {data.row.is_has_share_follow&&data.row.is_has_share_follow==1 ? (
                      <span class="cus-share-follow">
                        共享
                      </span>
                    ) : null}
                    {data.row && data.row.create_id == this.selfID ? (
                      <span class="cus-icon-type">私客</span>
                    ) : null}
                 
                    {data.row.client_type ? (
                      <span class="cus-icon-purchase">
                        {data.row.client_type.title}
                      </span>
                    ) : null}
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="150"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title}
                              </div>
                            );
                          })
                          : null}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title}
                        </span>
                      ) : (
                        ""
                      )}
                    </el-popover>
                  </div>
                </div>

                {data.row.wxqy_id > 0 ? (
                  <img
                    class="cus-img"
                    src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                  />
                ) : (
                  ""
                )}
                <div
                  class="fast-Edit-cus"
                  onClick={() => {
                    this.fastEditData(data.row);
                  }}
                >
                  <img
                    src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                    alt=""
                  />
                </div>
              </div>
            );
          },
        },
        {
          label: "手机号", name: 'mobile',
          prop: "mobile",
          fixed: "left",
          minWidth: "200px",
          tooltip: true,
          render: (h, data) => {
            const mobileFilter = function (val) {
              let reg = /^(.{3}).*(.{3})$/;
              return val.replace(reg, "$1*****$2");
            };
            return (
              <div class="flex-box over_text table-btns">
                {data.row.last_call_follow
                  &&
                  data.row.last_call_follow.id
                  && data.row.call_open_crm > 0
                  ?
                  (<div class="last_call_follow div row">

                    <div class="cus-clue-text">
                      {data.row.last_call_follow
                        &&
                        data.row.last_call_follow.call_status == 1
                        ?
                        (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                        :
                        (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                    </div>
                  </div>)
                  :
                  (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                <span class="search-Belong">
                  {data.row.mobile_place == "" ||
                    data.row.mobile_place == undefined ? (
                    <el-button
                      type="primary"
                      plain
                      onClick={() => {
                        this.HomeAddress(data.row);
                      }}
                    >
                      归属地查询
                    </el-button>
                  ) : (
                    <span class="Address">{data.row.mobile_place}</span>
                  )}
                </span>
                <div
                  class="fast-look-tel"
                  onClick={() => {
                    this.fastLookTel(data.row);
                  }}
                >
                  <i class="el-icon-phone"></i>
                </div>
              </div>
            );
          },
        },
        {
          label: "跟进记录", name: 'follow',
          width: "247px",
          sortable: "custom",
          render: (h, data) => {
            return (
              <div class="flex-box">
                {data.row.last_follow && data.row.last_follow.content ?
                (<el-popover
						    	placement="top-start"
						    	width="380"
						    	trigger="hover">
						    	<div class="comment-popover" domPropsInnerHTML={data.row.last_follow.content.replace(/\n/g, '<br>')}></div>
						    	<div slot="reference"><div class="comment">
                    <span style="margin-right: 5px;" >
                            {this.$options.filters.getPastDay(
                              data.row.current_follow_day)}
                          </span>{data.row.last_follow && data.row.last_follow.content
                            ? data.row.last_follow.content
                            : null}</div></div>
						    </el-popover>)

                  : ("--")}
                <div class="flex-row f-wrap">
                  <div class="flex-row follow-up-label">
                    {data.row.take_num && data.row.take_num > 0 ? (
                      <span class="follow-up-takeLook">
                        {this.$options.filters.getTakeLook(data.row.take_num)}
                      </span>
                    ) : null}
                    {this.$options.filters.getNewCustomer(
                      data.row.created_at
                    ) ? (
                      <span class="follow-up-takeLook">
                        {this.$options.filters.getNewCustomer(
                          data.row.created_at
                        )}
                      </span>
                    ) : null}
                    {data.row.see_tel_num && data.row.see_tel_num > 2 ? (
                      <span class="follow-up-takeLook">多次通话</span>
                    ) : null}
                    {data.row.get_time != "" &&
                      data.row.get_time != undefined ? (
                      <span class="follow-up-takeLook">
                        跟客
                        {this.$options.filters.getFollowDay(data.row.get_time)}
                      </span>
                    ) : null}
                  </div>
                  <span class="follow-up-day">
                    跟进（
                    {data.row.last_follow_day > 0
                      ? (<span style="color:#ec272f">
                          {data.row.last_follow_day} 天前
                        </span>)
                      : data.row.last_follow_day === 0
                      ? (<span style="color:#ec272f">
                          今天
                        </span>)
                      : (<span style="color:#ec272f">
                          {this.$options.filters.getFollowDay(data.row.created_at)}
                        </span>)
                    }
                    ）
                  </span>
                </div>
                <div
                  class="followLabel"
                  onClick={() => {
                    this.fastFollowUp(data.row);
                  }}
                >
                  <img
                    src={
                      "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                    }
                    alt=""
                  />
                </div>
              </div>
            );
          },
        },
        {
          label: "线索标签", name: 'label',
          prop: "label",
          minWidth: "200px",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-foot flex-row">
                  {data.row.label.length
                    ? data.row.label.slice(0, 4).map((item, index) => {
                      return (
                        <div class="flex-row align-center">
                          <span class="cus-icon-label" key={index}>
                            {item}
                          </span>
                        </div>
                      );
                    })
                    : null}
                </div>
                {data.row.label && data.row.label.length ? (
                  <div
                    class="clueLabel"
                    onClick={() => {
                      this.fastEditLabel(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                ) : (
                  <div
                    class="is_null_clueLabel"
                    onClick={() => {
                      this.fastEditLabel(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                )}
              </div>
            );
          },
        },
        {
          label: "客户线索", name: 'behavior',
          minWidth: "300px",
          prop: "remark",
          sortable: "custom",
          // tooltip: true,
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.last_behavior_info && data.row.last_behavior_info.content || data.row.remark}
                    </div>
                    <div class="follow-content" style="width:235px">
                      {data.row.last_behavior_info && data.row.last_behavior_info.content || data.row.remark}
                    </div>
                  </el-tooltip>

                {/* <div class='over_text' >
                  {data.row.last_behavior_info && data.row.last_behavior_info.content || data.row.remark}
                </div> */}
                <div>{data.row.operation_at}</div>
              </div>
            );
          },
        },
        {
          label: "最后通话状态", name: 'call',
          prop: "",
          width: "160px",
          render: (j, data) => {
            return (
              <div>
                <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                  ? 'success'
                  : data.row.last_call_status && data.row.last_call_status === 2
                    ? 'danger'
                    : 'info'}>
                  {data.row.last_call_status && data.row.last_call_status === 1
                    ? '已接通'
                    : data.row.last_call_status && data.row.last_call_status === 2
                      ? '未接通'
                      : '未联系'}
                </el-tag>
              </div>
            )
          }
        },
        {
          label: "最新带看记录", name: 'take_log',
          prop: "last_take_info",
          width: "200px",
          // sortable: "custom",
          render: (j, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {
                      data.row.last_take_info && data.row.last_take_info.content ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.last_take_info.content}
                          </div>
                          <div class="follow-takecontent cus-userName1" slot="reference">
                            {data.row.last_take_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )
                    }
                  </div>
                </div>
              </div>
            );
          }
        },
        {
          label: "带看次数", name: 'take_num',
          prop: "client_type",
          minWidth: "100px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_take_info && data.row.last_take_info.take_num || 0}
              </div>
            )
          }
        },

        {
          label: "最近带看时间", name: 'take_date',
          prop: "client_type",
          minWidth: "135px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_take_info ? (
                  <span style="margin-right: 5px;">
                    {data.row.last_take_info.take_date}{data.row.last_take_info.take_time == 1 ? '上午' : (data.row.last_take_info.take_time == 2 ? '下午' : '晚上')}
                  </span>
                ) : '--'}
              </div>
            )
          }
        },
        {
          label:this.diakanuser, name: 'take_user',
          prop: "client_type",
          minWidth: "100px",
          render: (j, data) => {
            return (
              <div style="text-align: center;">
                {data.row.last_take_info ? (
                  <span style="margin-right: 5px;">
                    {data.row.last_take_info.admin ? data.row.last_take_info.admin.user_name : '--'}
                  </span>
                ) : '--'}
              </div>
            )
          }
        },
        {
          label: "陪看人", name: 'take_accompany_user',
          prop: "client_type",
          minWidth: "130px",
          render: (j, data) => {
            return (
              <div style="text-align: center;">
                {data.row.last_take_info ? (
                  <span style="margin-right: 5px;">
                    {data.row.last_take_info.accompany}
                  </span>
                ) : '--'}
              </div>
            )
          }
        },
        {
          label: this.calculatecreate, name: 'create_user',
          prop: "create_user",
          minWidth: "150px",
          render: (j, data) => {
            return (
              <div style="text-align:center">
                {data.row.create_user && data.row.create_user.user_name ? (
                  <span style="margin-right: 5px;">
                    {data.row.create_user.user_name}
                  </span>
                ) : (
                  "--"
                )}
              </div>
            )
          }
        },

        {
          label: this.weihuuser, name: 'follow_user',
          prop: "repeat_call_name",
          minWidth: "110px",
          // sortable: "custom",
          render: (j, data) => {
            return (
              <div style="text-align: center;">
                {data.row.follow_user && data.row.follow_user.user_name ? (
                  <span style="margin-right: 5px;">
                    {data.row.follow_user.user_name}
                  </span>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },
        {
          label:this.chnegjiaouser, name: 'deal_user',
          prop: "deal_user",
          minWidth: "150px",
          render: (j, data) => {
            return (
              <div style="text-align:center">
                {data.row.deal_user && data.row.deal_user.user_name ? (
                  <span style="margin-right: 5px;">
                    {data.row.deal_user.user_name}
                  </span>
                ) : (
                  "--"
                )}
              </div>
            )
          }
        },
        /* {
          prop: "created_at",
          label: "创建时间",
        }, */
        {
          width: "200px", name: 'source',
          label: "来源",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.source ? (
                  <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
        // {
        //   label: "状态",
        //   render: (h, data) => {
        //     return (
        //       <div>
        //         {data.row.tracking ? (
        //           <span>{data.row.tracking.title}</span>
        //         ) : (
        //           "--"
        //         )}
        //       </div>
        //     );
        //   },
        // },
        // {
        //   label: "类型",
        //   render: (h, data) => {
        //     return (
        //       <div>
        //         {data.row.client_type ? (
        //           <span>{data.row.client_type.title}</span>
        //         ) : (
        //           ""
        //         )}
        //       </div>
        //     );
        //   },
        // },
        // {
        //   prop: "follow_user.user_name",
        //   label: "跟进人",
        // },
        // {
        //   label: "成交时间",
        //   render: (h, data) => {
        //     return <span>{data.row.deal_at || "--"}</span>;
        //   },
        // },
        {
          label: "操作", name: 'operate',
          width: "120px",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.onClickDetail(data.row);
                  }}
                >
                  详情
                </el-link>
                {this.params.status != 1 && data.row.follow_user == null && !this.checkStatus(data.row) ? (
                  <el-link
                    style="margin-left:20px"
                    type="primary"
                    onClick={() => {
                      this.onClickGet(data.row);
                    }}
                  >
                    认领
                  </el-link>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ]
    },
    getfollowsearch(){
      this.$refs.childRef.getfollowsearch(this.followsearch);
    },
    async getPerssion() {
      let admin_roles = await this.$http.getAdmin().catch(() => {
        console.log();
      })
      this.shitu_list = admin_roles.data;
      this.gordersvalue = this.shitu_list.is_allocation
      if (this.shitu_list.roles && this.shitu_list.roles.length && this.shitu_list.roles[0].name == '站长') {
        this.show_shitu = true
      } else {
        this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            if ((res.data + '').indexOf(this.shitu_list.id) > -1) {
              this.show_shitu = true
            }
          }
        })
      }
      this.getpl()
    },
    //获取列表表头
    // watch_head(){
    //   this.$http.watchhead().then((res)=>{
    //     if(res.status==200){
    //       console.log(res.data,"表头");
          // this.headerlist = res.data.table_list
          // for (let key in res.data.table_list) {
            // console.log(res.data.table_list.hasOwnProperty(key));
            // if (res.data.table_list.hasOwnProperty(key)) {
            //   // console.log(res.data.table_list[key].toString(),"11111111");
            //   let singleObj = {};
            //   singleObj[key] = res.data.table_list[key].toString();
            //   this.headerlist.push(singleObj);
            // }
          // }
    //       console.log(this.headerlist);
    //     }
    //   })
    // }
    // ,
    //腾房云是否绑定
    tfybinding(){
      this.$http.tfyshow().then(res=>{
        // console.log(res.data);
        document.cookie = `value=${res.data}`;
      })
    },
    checkStatusA(row) {
      // types!=='my'|| !c_detail.follow_user.length && !this.checkStatus(c_detail)
      // ClaimCustomers
      if(this.seas!=='my' || !row.follow_user.length){
          if (this.push_type == 2) {
          // 手动认领  
          // 潜在用户 我司成交的不可认领
          if (row.tracking_identify == 1) {
            // return true
            return this.ClaimCustomers =  false
          }
          // 掉工转公标记的可以领取 
          if (row.public2_status > 0) {
            // return false
            return this.ClaimCustomers = true
          }
          // 潜在客户可以领取
          // if (this.type == "qianzai") {
          //   return false
          // }
          // 其他情况 不可领取 
          // return true
          return this.ClaimCustomers =  false
        }
        // return false
        return this.ClaimCustomers =  false
        }
        return this.ClaimCustomers = true
    },
    changeSelectParams(e) {
      // console.log(e);
      if (e == 1) {
        this.placeholder = '请输入手机号码'
      } else if (e == 2) {
        this.placeholder = '请输入客户编号'
      } else if (e == 3) {
        this.placeholder = '请输入线索内容'
      } else if (e == 4) {
        this.placeholder = '请输入客户姓名'
      } else if (e == 5) {
        this.placeholder = '请输入跟进内容'
      }else if (e == 6) {
        this.placeholder = '请输入归属地'
      }
    },
    clearSelectKeyword() {
      this.params.keywords = ''
      this.params.mobile = ''
      this.params.cname = ''
      this.params.number = ''
      this.params.follow_keywords  = ''
      this.params.mobile_place  = ''
      this.handleSearch()

    },
    setParams(key) {
      let arr = ['keywords', 'mobile', 'cname', 'number','follow_keywords','mobile_place']
      arr.map(item => {
        if (item !== key) {
          this.params[item] = ''
        }
      })
    },
    handleKeywordSearch() {
      if (this.select_params.type == 1) {
        this.setParams('mobile')
        this.placeholder = '请输入手机号码'
        this.params.mobile=this.select_params.keywords.replace(/\s+/g,"");
      } else if (this.select_params.type == 2) {
        this.setParams('number')
        this.placeholder = '请输入客户编号'
        // this.params.keywords = ''
        this.params.number = this.select_params.keywords
      } else if (this.select_params.type == 3) {
        this.placeholder = '请输入线索内容'
        this.setParams('keywords')
        // this.params.mobile = ''
        this.params.keywords = this.select_params.keywords
      } else if (this.select_params.type == 4) {
        this.setParams('cname')
        this.placeholder = '请输入客户姓名'
        // this.params.mobile = ''
        this.params.cname = this.select_params.keywords
      } else if (this.select_params.type == 5) {
        this.setParams('follow_keywords')
        this.placeholder = '请输入跟进内容'
        this.params.follow_keywords = this.select_params.keywords
      } else if (this.select_params.type == 6) {
        this.setParams('mobile_place')
        this.placeholder = '请输入归属地'
        this.params.mobile_place = this.select_params.keywords
      }
      this.handleSearch()
    },
    debounce(fn, delay, immediate = false) {
      // 1.定义一个定时器, 保存上一次的定时器
      let timer = null
      let isInvoke = false
      // 2.真正执行的函数
      const _debounce = function (...ages) {
        // 取消上一次的定时器
        if (timer) clearTimeout(timer)

        // 判断是否需要立即执行
        if (immediate && !isInvoke) {
          fn.apply(this, ages)
          isInvoke = true
        } else {
          // 延迟执行
          timer = setTimeout(() => {
            // 外部传入的真正要执行的函数
            fn.apply(this, ages)
            isInvoke = false
          }, delay)
        }
      }

      return _debounce
    },
    handleResize() {
      // 获取屏幕宽度
      const filter = document.getElementById("filter")
      if (filter) {

        this.topHeight = filter.offsetHeight + 120
        // 获取屏幕宽度
        this.getScreenWidth();
      }
    },
    handleScroll() {
      const page = document.getElementsByClassName("main-content")[0];
      this.scrollTop = page.scrollTop
    },
    getScreenWidth() {
      // 获取屏幕宽度
      //  window.innerWidth;
      //  console.log(window.innerWidth);
      if (window.innerWidth <= 1350) {
        this.buttonhidden = false
        this.is_small_system = true
        this.myButton1 = true
      } else {
        this.is_small_system = false
        this.buttonhidden = true
        this.myButton1 = false
      }
    },
    //导入弹窗关闭回调
    cancels() {
      this.is_dialog_upload = false;
      this.upload_form = {
        type: 1,
        admin_id: "",
        create_id: "",
        file: "",
      };
    },
    checkStatus(item) {
      if (item.push_type == 2) {
        // 手动认领  
        // 我司成交的不可认领
        if (item.tracking_identify == 1) {
          return true
        }
        //潜在用户不受影响，都可以领取
        return false
      }
      return false
    },
    // 获取信息展示
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.selfID = res.data.id;
          if (res.data.roles[0].name === "站长") {
            this.is_show_upload = true;
            this.show_right = true
          } else {
            // 判断自己是否客户的创建人
            this.getSiteCrmSetting(res.data.id);
          }
        }
      }).catch(() => {
        this.show_right = true
      });
    },
    // 获取批量导入的crm站点设置
    getSiteCrmSetting(id) {
      this.$http.getSiteCrmSetting().then((res) => {
        if (res.status === 200) {
          if (
            !res.data.batch_import_uid ||
            res.data.batch_import_uid.indexOf(id) != -1
          ) {
            this.is_show_upload = true;
          }
        }
        this.show_right = true
      }).catch(() => {
        this.show_right = true
      });
    },
    getCrmCustomerFollowNumber() {
      this.$http.getCrmCustomerFollowNumber().then((res) => {
        if (res.status === 200) {
          this.no_follow_number = res.data;
        }
      });
    },
    //流转客获取24小时内新增的流转客数量
    informtionnew(){
      this.$http.informtionnewNumber().then((res) => {
        if (res.status === 200) {
          this.informtiondata = res.data;
        }
      });
    },
    toShitu() {
      this.$goPath("/crm_Follow_up_list")
    },
    resetXiansuoSearch() {
      this.params.keywords = "";
      this.handleSearch()
      this.show_xiansuo_search = false
    },
    changeTab(e) {
      console.log(e);
        this.params.c_type2 = e.id;
        if(e.id=="4_12"){
          this.$goPath("/crm_customer_information?information=1")
        }
        this.params.page = 1;
        this.$nextTick(()=>{
          this.getDataList();
        })
    },
    changeScreenType(list, date) {
      this.is_pullDown = false; // 关闭popover
      let firstIndex = "";
      let secondIndex = "";
      this.list_tabs.map((arr, index) => {
        if (arr.title == date.title) {
          firstIndex = index;
        }
      });
      this.list_tabs.splice(firstIndex, 1, list);
      this.screen_list_type.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex = index;
        }
      });
      this.screen_list_type.splice(secondIndex, 1, date);
      this.changeTab(list);
    },
    // 阻止默认行为和事件传播
    showTypeStatus(e) {
      e.preventDefault();
      e.stopPropagation();
      // this.is_pullDown1 = false;
      if (this.is_pullDown) {
        this.is_pullDown = false;
      } else {
        this.is_pullDown = true;
        this.participateDown = false;
      }
    },
    //我参与的
    participate_inType(list, date) {
      console.log(list, date);
      this.mymember= false
      if(list.id=="1_9"){
        this.mymember= true
      }
      console.log(this.params);
     
      this.participateDown = false; // 关闭popover
      let firstIndex = "";
      let secondIndex = "";
      this.list_tabs.map((arr, index) => {
        if (arr.title == date.title) {
          firstIndex = index;
        }
      });
      this.list_tabs.splice(firstIndex, 1, list);
      this.participate_in_list.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex = index;
        }
      });
      this.participate_in_list.splice(secondIndex, 1, date);
      // console.log(list, date);
      this.changeTab(list);
    },
    // 阻止默认行为和事件传播
    showTypeStatus1(e) {
      e.preventDefault();
      e.stopPropagation();
      // this.is_pullDown1 = false;
      if (this.participateDown) {
        this.participateDown = false;
      } else {
        this.participateDown = true;
        this.is_pullDown = false;
      }
    },
    // 获取客户类型列表
    getTypelist(cb) {
      this.$http.getCrmCustomerTypeDataNopage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_type = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户类型列表
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
          this.push_form.type = res.data.filter((item) => {
            return item.is_default;
          })[0].id;
          let cus_type = parseInt(this.$route.query.cus_type);
          res.data.map((item) => {
            if (cus_type == 1 && item.title == "求购") {
              this.params.type = item.id;
            }
            if (cus_type == 2 && item.title == "求租") {
              this.params.type = item.id;
            }
          });
        }
        cb && cb();
        
      });
    },
    getAdmin() {
      this.$http
        .getUserList(
          this.admin_params
        )
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    // 获取客户标签列表
    getLabelList() {
      this.$http.getLabelGroupNoPage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_label = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户类型列表
          this.label_list = res.data;
          // this.label_list.unshift({name: "全部", id: 999})
          this.label_default_list = Object.assign([], this.label_list); // 获取原始默认标签列表
          this.label_default_list.shift();
        }
      });
    },
    getDataList() {
      console.log(this.params.c_type2,'getDataList');
      this.is_table_loading = true;
      let params = Object.assign({}, this.params, this.customTabParams);
      if (!params.source_id) {
        delete params.source_id;
      }
      if (!params.type) {
        params.type = 0
      }
      if (!params.level_id) {
            params.level_id = 0
      }
      if (!params.tracking_id) {
        delete params.tracking_id;
      }
      if(params.date_style==0){
        delete params.date_style
      }
      if (!params.is_bind) {
        delete params.is_bind;
      }
      if (params.call_status == 0) {
        delete params.call_status;
      }
      
      if (params.c_type2 == 6||params.c_type2 == "1_9") {
        delete params.c_type2
        params.c_type6 = 1
      } else if ((params.c_type2 + '').split("_").length > 1) {
        params.c_type6 = params.c_type2.split("_")[0];
        delete params.c_type2
      }
      if (params.c_type2) {
        delete params.c_type6
      }

      let apiName = "getCrmCustomerClientList";
      if(params.c_type6 == 9){
        apiName = "mysharelist";
        delete params.c_type6 == 9;
      }
      if(params.c_type2 == 15){
        apiName = "sharedwithme";
        delete params.c_type6
        delete params.c_type2
      }
      // console.log(params,"Hhhhhh");
      this.$http[apiName]({ params: params })
        .then((res) => {
          this.is_table_loading = false;
          this.isDataInited = true;
          if (res.status === 200) {
            this.tableData = res.data.data;
            if(this.tableData.length){
              this.is_show_city = this.tableData[0].is_show_city
            }
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
            this.getordersstatus()
            //搜索参数拼接值
            const url = res.data.first_page_url;
            this.searchParamsJoins = url.substring(url.indexOf('?') + 1);
          }
        });
    },
    onQwSearch() {
      this.qw_params.page = 1;
      this.getWxworkData();
    },
    getWxworkData() {
      this.is_qw_loading = true;
      this.$http.getWxWorkUserData({ params: this.qw_params }).then((res) => {
        this.is_qw_loading = false;
        if (res.status === 200) {
          this.qw_tableData = res.data.data;
          this.qw_params.total = res.data.total;
        }
      });
    },
    onBindUser(row) {
      this.bind_qw.openid = row.openid;
      this.$confirm("是否绑定该企业用户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http.setCrmCustomerBindWxUser(this.bind_qw).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.is_qw_dialog = false;
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onPageChangeQw(e) {
      this.qw_params.page = e;
      this.getWxworkData();
    },
    getStatistics() {
      this.$http.getCrmCustomerStatisticsData().then((res) => {
        if (res.status === 200) {
          this.statistics_form = res.data;
        }
      });
    },
    getClientField() {
      this.$http
        .getCrmGetClientField({ params: this.client_field })
        .then((res) => {
          if (res.status === 200) {
            this.n_client_field = res.data.client;
            this.n_company_field = res.data.company;
          }
        });
    },
    // 获取客户级别列表
    getLevelData() {
      this.$http.getCrmCustomerLevelNopage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_level = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户级别列表
          console.log( ...res.data);
          this.level_list = [ ...res.data];
        }
      });
    },
    getTrackingList() {
      this.$http
        .getCrmCustomerFollowInfo({ params: this.tracking_params })
        .then((res) => {
          if (res.status === 200) {
            // if(this.website_id==109||this.website_id==176){
              this.tracking_list = [ ...res.data];
            // }else{
            //   this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
            // }
          }
        });
    },
    onClickSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    onClickType(e, type) {
      // console.log(e, type);
      let source_i
      let tracking_i
      let type_i
      let bind_i
      let level_i
      let statusdata_i
      let time_i
      switch (type) {
        case 1:
          source_i = this.source_list.findIndex(item => item.id == e.id)
          if (source_i >= 0) {
            this.source_index = source_i
          }
          this.params.source_id = e.id;
          break;
        case 2:
        // if(this.website_id==109||this.website_id==176){
            this.params.tracking_id = e.join(",")||""
          // }else{
          //   tracking_i = this.tracking_list.findIndex(item => item.id == e.id)
          //     if (tracking_i >= 0) {
          //       this.tracking_index = tracking_i
          //     }
          //     this.params.tracking_id = e.id;
          // }
          break;
        case 3:
          bind_i = this.bind_list.findIndex(item => item.id == e.id)
          if (bind_i >= 0) {
            this.bind_index = bind_i
          }
          this.params.is_bind = e.id;
          break;
        case 4:
          time_i = this.time_list.findIndex(item => item.id == e.id)
          if (time_i >= 0) {
            this.time_index = time_i
          }
          this.params.date_type = e.id;
          delete this.params.start_date; // 自定义时间发生改变，清空开始时间
          delete this.params.end_date; // 清空结束时间
          this.timeValue = ""; // 清空自定义时间绑定值
          break;
        case 5:
          level_i = this.level_list.findIndex(item => item.id == e.id)
          if (level_i >= 0) {
            this.level_index = level_i
          }
          this.params.level_id = e.id;
          break;
        case 6:
          type_i = this.type_list.findIndex(item => item.id == e.id)
          if (type_i >= 0) {
            this.type_index = type_i
          }
          this.params.type = e.id;
          break;
        case 7:
          statusdata_i = this.statusdata_list.findIndex(item => item.id == e.id)
          if (statusdata_i >= 0) {
            this.statusdata_index = statusdata_i
          }
          this.params.call_status = e.id;
          break;
        case 9:
          //带看状态
          if(e==0){
           delete this.params.take_status
          }else{
            this.params.take_status = e;
          }
          break;
        default:
          break;
      }
      this.params.page = 1;
      this.getDataList();
    },
    // 自定义筛选时间发生改变时触发
    onChangeTime(e) {
      this.params.start_date = e ? e[0] : ""; // 赋值开始时间
      this.params.end_date = e? e[1] : ""; // 赋值结束时间
      // this.params.date_type = 0; // 清空筛选时间
      // this.$refs.childRef.clearScreening(); // 重置筛选时间为全部
      this.params.page = 1; // 显示第一页
      console.log(this.params);
      this.getDataList(); // 获取最新数据

    },
    // 获取客户来源列表
    getSourceData() {
      this.$http.listcustomersourcenew().then((res) => {
        if (res.status === 200) {
          let arr = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户来源列表
          arr.map((item)=>{
            if(item.children==0){
              delete item.children
            }
          })
          this.ponent_maintain_source = JSON.parse(JSON.stringify(arr))
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
          this.source_list.map((item)=>{
            if(item.children==0){
              delete item.children
            }
          })
          this.push_form.source_id = res.data.filter((item) => {
            return item.is_default == 1;
          })[0].id;
          console.log(this.ponent_maintain_source );
        }
      });
    },
    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    //验证手机号
    Validationphone(phoneNumber){
      console.log(phoneNumber);
      if(phoneNumber){
        const regex = /^(?:1[3-9]\d{9}|(?:\+?852|00852)?[5-9]\d{7}|(?:\+?853|00853)?[6-9]\d{7})$/;
        if (regex.test(phoneNumber)) {
          // this.$message.success('手机号格式正确')
        } else {
          this.$message.warning('请输入正确的手机号')
        }
      }

    },
    addDomain() {
      this.other_mobile.push({
        mobile: "",
      });
    },
    onClickLevel(item) {
      this.push_form.level_id = item.id;
    },
    onClickTypeClient(item) {
      this.push_form.type = item.id;
    },
    //时间类型
    time_type(e){
      console.log(e);
      this.params.date_style  = e
      this.params.date_sort = 1;  //重置排序
      this.getDataList(); // 获取最新数据

    },
    //客户来源
    Source_status(e){
      this.params.source_id = e.join(",")||""
      this.getDataList(); // 获取最新数据
    },
    //客户状态
    customer_status(e) {
      this.params.tracking_id = e
      this.getDataList(); // 获取最新数据

    },
    //客户类型
    client_type(e) {
      console.log(e);
      this.params.type = e
      this.getDataList(); // 获取最新数据
    },
    //绑定企微
    clientqiwei_type(e){
      this.params.is_bind = e
      this.getDataList(); // 获取最新数据
    },
    //客户等级
    customer_grade(e){
      this.params.level_id = e.join(",")||""
      this.getDataList(); // 获取最新数据
    },
    //通话状态
    customerstatus_type(e){
      this.params.call_status = e
      this.getDataList(); // 获取最新数据
    },
    //成员检索(仅我录入的)
    loadFirstLevelChildren(value) {
    console.log(this.member_value);
        if(value.length){
          this.params.admin_type = value[0]
          if(value[1]){
            this.params.admin_id = value[1]
          }
        }else{
          delete this.params.admin_type
          delete this.params.admin_id
        }
        this.getDataList(); // 获取最新数据
      },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    //录入客户，客户来源
    sourceLabel_status(e){
      if(e.length>1){
        this.push_form.source2_id = e[1]
        this.push_form.source_id = e[0]
      }else{
        this.push_form.source_id = e[0]
         this.push_form.source2_id = 0
      }
    },
    //导入客户来源
    sourceLabelimport(e){
      if (e.length) {
        if(e.length>1){
        this.upload_form.source2_id = e[1]
        this.upload_form.source_id = e[0]
      }else{
        this.upload_form.source_id = e[0]
        this.upload_form.source2_id = 0
      }
      }else{
        this.upload_form.source_id =''
        this.upload_form.source2_id = 0
      }
    },
    // 点击表格的checkbox触发
    selectionChange(e) {
      this.multipleSelectionname = e; // 赋值当前客户信息
      // 遍历数据，返回客户id
      let arr = e.map((item) => {
        return item.id;
      });
      this.multipleSelection = arr; // 赋值当前客户的id
      // 只有在客户标签列表为空时请求数据
      if (!this.labels_list.length) {
        this.getLabelGroupNoPageNew();
      }
    },
    // 获取成员的接口（新）
      MembersNEW(){
      this.$http.getDepartmentMemberListNew().then((res)=>{
        if(res.status==200){
          this.member_listNEW.map(item => {
          item.subs = res.data
         })
        }
      })
    },
    async onClickDetail(row) {
      // this.PreviousandNext()
      let res = await this.$http.getForceFollow()
      if (res.status == 200 && res.data && res.data.id > 0) {
        this.$confirm("您有未跟进的客户确认去跟进吗？", "提示").then(() => {
          this.goCustomerDetailPage({id: res.data.client_id, type:'my',tel_follow_id:res.data.id}, {page: row.page, per_page: row.per_page});
        })
        return
      }
      this.goCustomerDetailPage({ id: row.id, type: 'my' }, {page: row.page, per_page: row.per_page});
    },
    onPageChange(current_page) {
      // console.log(current_page);
      this.params.page = current_page;
      this.getDataList();
    },
    //每页几条
    handleSizeChange(e){
      this.params.per_page = e
      this.getDataList();
    },
    onPageChangeQwFollow(e) {
      this.is_follow_params.page = e;
      this.getFollow();
    },
    isShowFollow(row) {
      this.is_follow_dialog = true;
      this.is_follow_params.client_id = row.client_id;
      this.getFollow();
    },
    getFollow() {
      this.is_follow_loading = true;
      this.$http
        .getCrmCustomerFollowData({ params: this.is_follow_params })
        .then((res) => {
          this.is_follow_loading = false;
          if (res.status === 200) {
            this.is_follow_data = res.data.data;
            this.is_follow_params.total = res.data.total;
          }
        });
    },
    // onClickForm(e) {
    //   this.$http.setCrmCustomerData(e).then((res) => {
    //     if (res.status === 200) {
    //       this.$message.success("操作成功");
    //       this.is_push_customer = false;
    //       this.form = {};
    //       this.form1 = {};
    //       this.params.page = 1;
    //       this.getDataList();
    //     }
    //   });
    // },
    provincesChange(e){
          console.log(e,"e");
          this.push_form.province_id = e[0]
          this.push_form.city_id = e[1]
          this.push_form.area_id = e[2]
        },
    Cities_provinces(){
          this.$http.cities_and_provinces().then((res)=>{
            if(res.status==200){
              // console.log(res.data,"省市区");
              this.provincesoptions = res.data
            }
          })
    },
    //录入客户取消事件
    enterdrawerfalse(){
      this.enterdrawer = false
      this.params.page = 1;
      this.getDataList();
    },
    onClickForm(status) {
      // if(this.website_id==109||this.website_id==176){
        if(status==1){
          this.$refs.myMaintenancecopy.onClickForm();
        }else{
          this.$refs.enterinformation.onClickForm();
        }  
      // }else{
      //   if (
      //   this.push_form.label &&
      //   this.push_form.label != undefined &&
      //   typeof this.push_form.label !== "string"
      // ) {
      //   this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
      // }
      // if (this.other_mobile.length > 0) {
      //   let arr = this.other_mobile.map((item) => {
      //     return item.mobile;
      //   });
      //   let othertel = arr.filter((item, index) => {
      //     if (index) {
      //       return item;
      //     }
      //   });
      //   this.push_form.mobile = arr[0];
      //   this.push_form.subsidiary_mobile = othertel.join(",");
      // }
      // if (!this.push_form.mobile) {
      //   this.$message.error("请检查联系方式");
      //   return;
      // }
      // if (!this.push_form.cname) {
      //   this.$message.error("请检查客户姓名");
      //   return;
      // }
      // if (!this.push_form.sex) {
      //   this.$message.error("请检查客户性别");
      //   return;
      // }
      // if (!this.push_form.level_id) {
      //   this.push_form.level_id = 0
      //   // this.$message.error("请检查客户等级");
      //   // return;
      // }
      // if (!this.push_form.type) {
      //   this.$message.error("请检查客户类型");
      //   return;
      // }
      // if (!this.push_form.source_id) {
      //   this.$message.error("请检查客户来源");
      //   return;
      // }
      // if (!this.push_form.source2_id) {
      //   this.push_form.source2_id = 0
      // }
      // const params = {...this.push_form};
      // params.intention_community = params.intention_community.length ? params.intention_community.join(",") : '';

      // this.is_button_loading = true;
      // this.$http.setCrmCustomerDataV2(params).then((res) => {
      //   this.is_button_loading = false;

      //   if (res.status === 200) {
      //     this.$message.success("操作成功");
      //     this.getDataList();
      //     this.reset();
      //     this.is_push_customer = false;
      //     this.provincesvalue = []
      //     this.source_idvalue =""
      //   } else if (res.status === 422) {
      //     const cus_id =
      //       res.data.data &&
      //         res.data.data.id != "" &&
      //         res.data.data.id != undefined
      //         ? res.data.data.id
      //         : 0; // 赋值客户id
      //     // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
      //     if (
      //       res.data.data &&
      //       res.data.data.follow_id &&
      //       res.data.data.follow_id != undefined &&
      //       res.data.data.follow_id != 0
      //     ) {
      //       this.$confirm("客户号码已有维护人，无法重复录入。", "提示", {
      //         confirmButtonText: "立即查看",
      //         cancelButtonText: "取消",
      //         type: "warning",
      //       })
      //         .then(() => {
      //           let url = `/crm_customer_detail?id=${cus_id}&type=my`;
      //           this.$goPath(url); // 跳转客户详情
      //         })
      //         .catch(() => {
      //           return;
      //         });
      //       this.is_push_customer = false;
      //     } else {
      //       // 该客户没有维护跟进人时触发
      //       if (cus_id > 0) {
      //         this.$confirm("客户号码已存在，认领后可跟进。", "提示", {
      //           confirmButtonText: "立即认领",
      //           cancelButtonText: "取消",
      //           type: "warning",
      //         })
      //           .then(() => {
      //             this.$http
      //               .getCrmCustomerPublick({ ids: cus_id + "" })
      //               .then((res) => {
      //                 if (res.status === 200) {
      //                   this.$message.success("认领成功");
      //                   let url = `/crm_customer_detail?id=${cus_id}&type=my`;
      //                   this.$goPath(url); // 跳转客户详情
      //                 }
      //               });
      //           })
      //           .catch(() => {
      //             return;
      //           });
      //         this.is_push_customer = false;
      //       }
      //     }
      //   }
      // });
      // }
    },
    // 折叠面板
    onChangeCollapse() {
      this.is_collapse = !this.is_collapse;
    },
    
    onClickGet(row) {
      this.$confirm("是否认领该客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.getCrmCustomerPublick({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("认领成功");
              let url = `/crm_customer_detail?id=${row.id}&type=my`;
              this.$goPath(url);
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    handleChangeLabel() {
      this.params.page = 1;
      this.getDataList();
    },
    leftla() {
      this.buttonhidden = true
      this.myButton = true
      this.myButton1 = false
    },
    Rightdrag() {
      this.myButton1 = true
      this.buttonhidden = false
      this.myButton = false
    },
    //录入客户
    push_customerA(){
      this.Cities_provinces()
      this.getTypelist()
      // if(this.website_id==109||this.website_id==176){
        this.enterdrawer = true
      // }else{
      //   this.is_push_customer = true
      // }
    },
    getFile() {
      this.getMemberList();
      // if(this.website_id==109){
      this.importdrawer = true
      // }else{
        // this.is_dialog_upload = true;
      // }
      // this.$refs.file.click();
    },
     //开始导入
     Starting_import(){
      this.$refs.importinformation.Starting_import();
    },
    // 侧边栏合上
    closesidebar(){
      this.importdrawer = false
      this.params.page = 1;
      this.getDataList();
    },
    handleSuccessAvatarTemporary(response) {

      this.handleFileUpload(response.url)
    },
    // 获取文件
    handleFileUpload(response) {
      // 阻止发生默认行为
      // event.preventDefault();
      let url = response;
      let formData = new FormData();
      formData.append("url", url);
      formData.append("admin_id", this.upload_form.admin_id || 0);
      formData.append("type", this.upload_form.type);
      formData.append("type_id", this.upload_form.type_id);
      formData.append("source_id", this.upload_form.source_id);
      formData.append("source2_id", this.upload_form.source2_id);
      formData.append("create_id", this.upload_form.create_id || 0);
      if (Array.isArray(this.upload_form.label)) {
        formData.append("label", this.upload_form.label.join(","));
      } else {
        formData.append("label", this.upload_form.label);
      }
      formData.append("remark", this.upload_form.remark);
      // this.formData.get("file");
      this.onUpload(formData);
    },
    // 上传文件
    onUpload(formData) {
      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.$http.uploadCrmCustomerDataURL(formData).then((res) => {
        // this.$http.uploadCrmCustomerData(formData).then((res) => {
        if (res.status === 200) {

          // 如果只有新增
          let text =
            "导入成功，新增客户数量" +
            res.data.success_num +
            "条,系统重复客户数量" +
            res.data.crm_repeat_num +
            "条,表格重复客户数量" +
            res.data.excel_repeat_num +
            "条,失败客户数量" +
            res.data.error_num +
            "条";
          // if (res.data.add_num > 0 && res.data.edit_num == 0) {
          //   this.$message.success("导入成功，新增" + res.data.add_num + "条");
          // } else if (res.data.add_num == 0 && res.data.edit_num > 0) { // 如果只有编辑覆盖
          //   this.$message.success("导入成功，编辑覆盖" + res.data.edit_num + "条");
          // } else if (res.data.add_num > 0 && res.data.edit_num > 0) { // 如果新增和覆盖都有
          //   this.$message.success("导入成功，新增" + res.data.add_num + "条，编辑覆盖" + res.data.edit_num + "条");
          // } else if (res.data.add_num == 0 && res.data.edit_num == 0) { // 如果新增和覆盖都没有
          //   this.$message.error("导入失败，新增" + res.data.add_num + "条，编辑覆盖" + res.data.edit_num + "条");
          // }
          this.loading.close();
          this.$confirm(text, "提示", {
            confirmButtonText: "查看详情",
            cancelButtonText: "取消",
            type: "success",
          })
            .then(() => {
              this.params.page = 1;
              this.getDataList();
              this.is_dialog_upload = false;
              this.is_loading = false;
              let url = `/crm_customer_task_list?task_id=` + res.data.task_id;
              this.$goPath(url); // 跳转客户详情
            })
            .catch(() => {
              this.params.page = 1;
              this.getDataList();
              this.is_dialog_upload = false;
              this.is_loading = false;
            });
        } else {
          this.loading.close();
        }
      });
    },
    // 上传拉取最新模板
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/client_template_new.xls?f=" +
        +new Date()
      );
    },
    onClickGetPull() {
      // 拉取企微客户
      this.$message.success("正在拉取客户...");
      this.$http
        .getCrmWxworkPullData({ params: this.pull_params })
        .then((res) => {
          if (res.status === 200) {
            // 如果返回参数接着拉取
            if (res.data) {
              this.pull_params = res.data;
              this.onClickGetPull();
            } else {
              this.$message.success("拉取完成");
              this.qw_params.page = 1;
              this.getWxworkData();
            }
          } else {
            this.$message.error("拉取失败！");
          }
        });
    },
    //重置表单
    reset() {
      this.push_form = {
        cname: "",
        source_id: "",
        source2_id: "",
        level_id: "",
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: [],
        // intention_street: "",
        remark: "",
      };
      this.other_mobile = [{ mobile: "" }];
    },
    //取消
    cancel() {
      this.reset();
      this.is_push_customer = false;
    },
    //一个个删除标签
    dellabeld(index,id){
        if (index > -1 && index <  this.labeldata.length) {
          // 如果索引值在数组长度范围内，则可以删除元素
          this.labeldata.splice(index, 1);
        }
       let labelid = this.customerLabelListcopy.indexOf(id);
        if (labelid > -1) {
          this.customerLabelListcopy.splice(labelid, 1);
        }
      this.params.label = this.customerLabelListcopy.join(",")
      this.params.page = 1;
      this.getDataList();
    },
    //清空标签
    clearlabel(){
      this.labeldata = []
      this.customerLabelListcopy = []
      this.params.label = ""
      this.params.page = 1;
      this.getDataList();
    },
    //选中客户标签
    handleCommand(item, label) {
      // 检查是否已存在相同的 item 和 label 组合
      const isDuplicate = this.labeldata.some(data => data.id === item.id);
      if (!isDuplicate) {
        this.labeldata.push({ name: item.name, title: label.name,id:item.id });
        this.customerLabelListcopy.push(item.id )
      }
        this.params.label = this.customerLabelListcopy.join(",")
        this.params.page = 1;
        this.getDataList();
    },
    // 检索客户标签发生改变时触发
    changeCustomerLabel(val) {
      this.is_all = false;
      // 父级标签id赋值
      let label_num = Object.keys(this.customerLabelList).length;
      // 如果选择的标签大于1就删除之前选择的标签
      if (label_num > 1) {
        delete this.customerLabelList[this.changeParentLabel];
      }
      this.changeParentLabel = val[2].id;
      // 更新数据
      this.params.label = val[0];
      this.params.page = 1;
      this.getDataList(); // 获取最新数据
    },
    getAllLabelList() {
      this.is_all = true;
      this.changeParentLabel = "";
      this.customerLabelList = {};
      delete this.params.label;
      this.params.page = 1;
      this.getDataList(); // 获取最新数据
    },
    getSelectWidth(item, LabelList) {
      var text_temp = "",
        text_width = 0,
        text_el = null;
      var current_option = "";
      if (LabelList) {
        current_option =
          item.label && item.label.find((arr) => arr.id === LabelList[0]);
      } else {
        current_option = false;
      }
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${item.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text");
      text_width = text_el.offsetWidth;
      text_el.remove();
      return text_width + 57 + "px";
    },
    // 设置客户标签
    setCustomerLabel() {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      this.is_fast = false;
      this.show_Customer_label = true; // 显示模态框
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        });
      });
    },
    // 获取客户标签列表
    getLabelGroupNoPageNew() {
      // 获取客户标签列表
      this.$http.getLabelGroupNoPageNew().then((res) => {
        if (res.status == 200) {
          if (res.data.qiwei_tag && res.data.qiwei_tag.length) {
            res.data.qiwei_tag.map((item) => {
              item.label = item.taggroup;
              delete item.taggroup;
            });
          }
          this.labels_list = res.data.qiwei_tag.concat(res.data.system_tag);
        }
        // 格式化数据-添加check属性
        if (this.labels_list) {
          this.labels_list.map((item) => {
            item.label.map((list) => {
              list.check = false;
            });
          });
        }
      });
    },
    // 选中标签
    checkChangeLabels(index0, index) {
      let that = this;
      that.labels_list[index0].label[index].check =
        !that.labels_list[index0].label[index].check;
      this.$forceUpdate();
    },
    // 确定更改客户标签
    confirmSelected() {
      this.is_loading = true;
      let num = []; // 已选客户标签容器
      // 遍历出勾选中的客户标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          if (list.check) {
            num.push(list.id);
          }
        });
      });
      // 赋值已选中的客户标签参数
      if (num && num.length) {
        this.confirm_batch_list.label = num.join(",");
      }
      // 赋值已选中的客户id参数
      if (this.multipleSelection && this.multipleSelection.length) {
        this.confirm_batch_list.ids = this.multipleSelection.join(",");
      }
      // 请求接口
      this.$http
        .newbatchSetLabelGroup(this.confirm_batch_list)
        .then((res) => {
          if (res.status == 200) {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.show_Customer_label = false; // 关闭模态框
            this.is_loading = false;
            this.getDataList(); // 获取最新数据
          }
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
    confirmFastSelected() {
      this.is_loading = true;
      let num = []; // 已选客户标签容器
      // 遍历出勾选中的客户标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          if (list.check) {
            num.push(list.id);
          }
        });
      });
      // 赋值已选中的客户标签参数
      if (num && num.length) {
        this.fastEdit_params.label = num.join(",");
      }
      this.$http
        .setCrmCustomerLabelsData(this.fastEdit_params)
        .then((res) => {
          if (res.status == 200) {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.show_Customer_label = false; // 关闭模态框
            this.is_loading = false;
            this.getDataList(); // 获取最新数据
          }
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
    // 导入成员当获取焦点时触发
    focusSelete() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_add_member = true;
    },
    focusSelete1() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_add_member1 = true;
    },
    //选中部门人员
    selecetedMember(e) {
      // console.log(e.checkedNodes,"成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.admin_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.admin_id = "";
      }
      this.show_add_member = false;
    },
    selecetedMember1(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id1 = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.create_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.create_id = "";
      }
      this.show_add_member = false;
    },
    //获取部门成员信息
    getMemberList() {
      this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data;
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user,
          });
          this.recursionData(this.memberList);
          this.Not_duplicate_datalist = JSON.parse(
            JSON.stringify(this.datalist)
          );
          this.filteredData();
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      });
    },
    // 递归数据处理
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              });
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item);
            }
          });
          this.recursionData(data[key].subs);
        }
      }
    },
    // 清除当前选择成员
    delName() {
      this.uploadAdmin_id = "";
      this.upload_form.admin_id = 0;
    },
    // 开始导入
    startImport() {
      this.is_loading = true;
      if (
        this.upload_form.type_id == "" ||
        this.upload_form.type_id == undefined
      ) {
        this.upload_form.type_id = 0;
      }
      if (
        this.upload_form.source_id == "" ||
        this.upload_form.source_id == undefined
      ) {
        this.upload_form.source_id = 0;
      }
      if (
        this.upload_form.source2_id == "" ||
        this.upload_form.source2_id == undefined
      ) {
        this.upload_form.source2_id = 0;
      }
      // 处理为正常部门成员id
      if(this.upload_form.admin_id){
        if (this.upload_form.admin_id.toString().length >= 6) {
        this.upload_form.admin_id = parseInt(
          this.upload_form.admin_id.toString().slice(0, 3)
        );
      }
      }
      this.$refs["upload"].$refs["upload-inner"].handleClick()
      this.is_loading = false;
      // this.$refs.file.click();
      // //唤起上传组件
      // this.$refs["upload"].$refs["upload-inner"].handleClick()
    },
    // 查询归属地
    HomeAddress(row) {
      this.$http.inquireHomeAddress(row.id).then((res) => {
        if (res.status == 200) {
          row.mobile_place = res.data;
          //this.getDataList(); // 获取最新数据 
        }
      });
    },
    // 点击转交类型
    onClickCus(item) {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      if(this.multipleSelection.length&&this.multipleSelection.length>50){
        return this.$message({
          message: "每次最多选择50个客户",
          type: "warning",
        });
      }
      
      this.transfer_type = false; // 隐藏pop框
      // 如果是转交到公海
      console.log(item.id);
      if (item.id == 1) {
        this.show_transfer_public = true;
        this.transfer_public_params.content = "";
        this.transfer_public_params.ids = "";
      } else if (item.id == 2) {
        // 如果是转交指定维护人
        this.is_transfer_customer = true;
        this.changetitle = "转交客户",
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      } else if (item.id == 3) {
        this.changetitle = "复制客户",
        this.is_transfer_customer = true;
      } else if (item.id == 4){
        this.$refs.automatic.open(this.multipleSelection)
      }
    },
    // 搜索转交人
    onAdminSearch() {
      this.admin_params.page = 1;
      this.getAdmin();
    },
    // 确定转让客户
    onZhuanrang(e,id) {
      this.$confirm(`是否将所选客户转交给【${e.user_name}】？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 将数组转换字符串
          if(id==1){
            this.c_id = this.multipleright.toString(); // 数组转换字符串
          }else{
            this.c_id = this.multipleSelection.join(",");
          }
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomerZhuanrang({
              be_transfer_id: e.id,
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.right_transfer_customer = false;
                this.is_transfer_customer = false
                this.getDataList(); // 获取最新数据
                this.$refs.childRef.getDetail()
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    //复制客户
    oncopycrm(e,id){
      this.$confirm(`是否将所选客户复制到【${e.user_name}】的流转客？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 将数组转换字符串
          if(id==1){
            this.c_id = this.multipleright.toString(); // 数组转换字符串
          }else{
            this.c_id = this.multipleSelection.join(",");
          }
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomercopy({
              user_ids: e.id.toString(),
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.right_transfer_customer = false;
                this.is_transfer_customer = false
                if (id==1) {
                  this.$refs.childRef.getDetail()
                }else{
                 this.getDataList(); // 获取最新数据 
                }
              }
            });
        })
        .catch((err) => {
          console.log(err);
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    PersPageChange(e) {
      this.admin_params.page = e;
      this.getAdmin();
    },
    // 部门选取发生改变
    changePopDepar(val) {
      console.log(this.params,"q1111111");
      this.selectedMember = "";
      if (val != null) {
        this.filtrMember = [];
        this.filtrDepartMember(this.memberList, val);
        this.filtrMember = this.filtrMember.filter((item) => {
          return item.id.toString().length <= 4;
        });
      } else {
        this.filteredData();
      }
      this.handleSearch()
    },
    // 选中对应部门，遍历出部门成员
    filtrDepartMember(data, val) {
      for (let key in data) {
        // console.log(data[key].id,val,"执行",data[key].id == val)
        if (data[key].id == val) {
          if (data[key].subs) {
            data[key].subs.map((item) => {
              if (item.user) {
                item.user.map((list) => {
                  this.filtrMember.push(list);
                });
              } else if (item.user_name) {
                this.filtrMember.push(item);
              }
            });
          }
        }
        this.filtrDepartMember(data[key].subs, val);
      }
    },
    // 部门成员去重
    filteredData() {
      const uniqueIds = {};
      const filtered = [];
      for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
        const item = this.Not_duplicate_datalist[i];
        if (!uniqueIds[item.id]) {
          uniqueIds[item.id] = true;
          filtered.push(item);
        }
      }
      this.filtrMember = filtered;
    },
    // 获取全部的部门
    getCrmDepartmentList() {
      // this.params.admin_id = 0; // 清空搜索内容
      // this.params.department_id = 0; // 清空搜索内容
      // this.getDepartmentList(); // 获取部门
      // // 获取部门成员
      // if (!this.datalist.length) {
      //   this.getMemberList();
      // }
    },
    // 获取部门
    getDepartmentList() {
      if (!this.AllDepartment.length) {
        this.$http.getCrmDepartmentList().then((res) => {
          if (res.status == 200) {
            this.AllDepartment = res.data;
          }
        });
      }
    },
    // 搜索成员
    searchMember() {
      // 处理id为前三位
      if (this.params.admin_id.toString().length >= 6) {
        this.params.admin_id = parseInt(
          this.params.admin_id.toString().slice(0, 3)
        );
      }
      this.getDataList(); // 获取最新数据
    },
    // 搜索部门成员发生改变时触发
    changeSearchMember() {
      // 如果搜索的参数为空或undefined
      if (this.selectedMember == "" || this.selectedMember == undefined) {
        this.params.admin_id = 0; // 赋值0
      } else {
        this.params.admin_id = this.selectedMember; // 有值则赋值
      }
      this.handleSearch()
    },
    // 确定转交至公海
    confirmTransfer(id) {
     this.confirmLoading=true // 加载动画
      if (
        this.transfer_public_params.content == "" ||
        this.transfer_public_params.content == undefined
      ) {
        return this.$message({
          message: "请填写转交至公海原因",
          type: "warning",
        });
      }
      if(id==1){
        this.transfer_public_params.ids = this.multipleright.toString(); // 数组转换字符串
      }else{
        this.transfer_public_params.ids = this.multipleSelection.join(","); // 数组转换字符串
      }
      this.$http
        .giveupCrmCustomerData(this.transfer_public_params)
        .then((res) => {
          if (res.status == 200) {
            this.$message({
              type: "success",
              message: "转交至公海成功",
            });
            this.confirmLoading=false
            this.show_transfer_right = false; // 关闭模态框
            this.show_transfer_public = false
            this.getDataList(); // 获取最新数据
            this.$refs.childRef.getFollowData()
            this.$refs.childRef.getMaintainRecord()
          }
        });
    },
    // 快速编辑标签
    async fastEditLabel(row) {
      this.is_fast = true; // 设置为快速编辑标签
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        });
      });
      //console.log(row,"row");
      this.fastEdit_params.client_id = row.id;
      new Promise((resolve) => {
        // 获取客户标签列表
        if (!this.labels_list.length) {
          this.getLabelGroupNoPageNew();
        }
        if (!this.labels_list.length) {
          setTimeout(() => {
            resolve();
          }, 500);
        } else {
          resolve();
        }
      }).then(() => {
        this.show_Customer_label = true; // 显示模态框
        // console.log(this.labels_list,"this.labels_list");
        row.label.map((item) => {
          this.labels_list.map((list) => {
            list.label.map((arr) => {
              if (arr.name == item) {
                arr.check = true;
              }
            });
          });
        });
        this.$forceUpdate();
      });
    },
    // 重置搜索手机号
    resetLoudongSearch() {
      this.params.mobile = "";
      this.handleSearch()
      this.show_tel_search = false
    },
    handleSearch() {
      this.params.page = 1;
      this.getDataList(); // 获取最新数据
    },
    //
    

    empty() {
      // this.source_list
      // console.log(123);
      this.source_index = 0
      this.tracking_index = 0
      this.type_index = 0
      this.bind_index = 0
      this.level_index = 0
      this.statusdata_index = 0
      this.time_index = 0
      this.select_params.keywords = ''
      this.timeValue = ""; // 清空自定义时间绑定值
      this.is_all = true;
      this.changeParentLabel = "";
      this.customerLabelList = {};
      this.datetime_type = 0,//时间类型
      this.customer_statusvalue = "",// 客户状态
      this.Source_statusvalue = "",//客户来源
      this.typeLabel_value = "",//客户类型
      this.typeqiwei_value = "",//绑定企微
      this.grade_value = "",//客户等级
      this.customerstatus_value = "",//通话状态
      this.showstatus_value = "",//带看状态
      this.customerLabelListcopy = [], // 所选择的标签
      this.labeldata = [],
      // delete this.params.label;
      this.params = {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 2,
        // status: 0,
        c_type2: 0, // 认领状态 0：全部 1：已认领 2：已跟进 3：未跟进 4：私客 5：掉公
        department_id: 0,
        admin_id: 0,
        mobile: "",
        c_type6: 0,
        date_sort: 1
      },
        this.$forceUpdate()
      this.getDataList(); // 获取最新数据
    },
    //刷新
    Refresh() {
      this.getDataList(); // 获取最新数据
    },
    // 获取接单状态
    getordersstatus(){
      this.$http.getAuthShow("is_auto_allocation").then(res=>{
        if(res.status==200){
          // console.log(res.data);
          if(res.data==1){
            this.showgorders = true
          }else{
            this.showgorders = false
          }
        }
      })
    },
    // 获取是否开通批量自动分配的权限
    getpl(){
      this.$http.getAuthShow("auto_allocation_uid").then(res=>{
        if(res.status==200){
          // this.getpldata = res.data
          const dataArray = res.data.split(","); // 假设用逗号分隔
          const match = dataArray.includes(String(this.shitu_list.id));
          if (match) {
            this.cus_list.push({id: 4, name: "批量自动分配"})
          } else {
            if(this.show_shitu){
              this.cus_list.push({id: 4, name: "批量自动分配"})
            }
          }
        }
      })
    },
    //更改接单状态
    changegordersvalue(){
      let data = {
        is_allocation:""
      }
      let text = '更改为正常接单状态，系统将正常分配客资信息, 是否继续?'
      if(this.gordersvalue != 1){
        text = '更改为暂停接单状态，系统将不在分配客资信息, 是否继续?'
      }
      this.$confirm(text, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          data.is_allocation = this.gordersvalue
          this.$http.changegordersvalue(data).then(res=>{
            if(res.status==200){
              this.$message.success("修改成功！");
            }
          })
        }).catch(() => {
          this.gordersvalue = this.shitu_list.is_allocation
          this.$message({
            type: 'info',
            message: '已取消修改！'
          });          
        });
    },
    Topping(showtop) {
      if (showtop !== 1) {
        this.$http.setMyClientTop(showtop).then(res => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            this.getDataList(); // 刷新客户详情数据
          }
        })
      }
      if (showtop == 1) {
        // 判断是否选中客户
        if (!this.multipleSelection.length) {
          return this.$message({
            message: "请选择客户",
            type: "warning",
          });
        }
        // 判断是否选中客户
        if (this.multipleSelection.length > 1) {
          return this.$message({
            message: "请选择一位客户",
            type: "warning",
          });
        }
        this.multipleSelectionname.map(item => {
          // console.log(item.order);
          if (item.order == 1) {
            return this.$message({
              message: "您选中的是已置顶的客户，请重新选择",
              type: "warning",
            });
          }
          if (item.order == 0) {
            this.$http.setMyClientTop(item.id).then(res => {
              if (res.status == 200) {
                this.$message.success("操作成功");
                this.getDataList(); // 刷新客户详情数据
              }
            })
          }
        })
      }
    },
    // 右侧编辑置顶
    TopA(id) {
      // console.log(id,",id");
      if (this.ticky_post == "列表置顶") {
        this.$http.setMyClientTop(id).then(res => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            this.$refs.childRef.getDetail();
            this.ticky_post = "取消列表置顶"
            this.getDataList(); // 刷新客户详情数据
          }
        })
      }
      if (this.ticky_post == "取消列表置顶") {
            this.$http.setMyClientTop(id).then(res => {
              if (res.status == 200) {
                this.$message.success("操作成功");
                this.$refs.childRef.getDetail();
                this.ticky_post = "列表置顶"
                this.getDataList(); // 刷新客户详情数据
              }
            })
      }
    },
    //右侧速编转交
    onClickright(item){
      this.multipleright = this.ids
      this.Transfer_right = false; // 隐藏pop框
      // 如果是转交到公海
      if (item.id == 1) {
        this.show_transfer_right = true;
        this.transfer_public_params.content = "";
        this.transfer_public_params.ids = "";
      } else if (item.id == 2) {
        // 如果是转交指定维护人
        this.changetitle = "转交客户",
        this.getAdmin()
        this.right_transfer_customer = true;
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      } else if (item.id == 3) {
        this.changetitle = "复制客户",
        this.right_transfer_customer = true;
      }
    },
    //右侧速编确定提交
    sureright(){
      this.$refs.childRef.onClickForm();
      this.getDataList(); // 获取最新数据
    },
    // 排序
    sortChangeData(column) {
      // console.log(column.column.label,column,"参数");
      // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
      if (column) {
        if (column.column.label === "客户名称") {
          this.params.c_type3 = 1;
        } else if (column.column.label === "跟进记录") {
          this.params.c_type3 = 2;
        } else if (column.column.label === "客户线索") {
          this.params.c_type3 = 3;
        }
        // 判断升序
        if (column.order === "ascending") {
          this.params.c_type3_sort = 2;
        } else if (column.order === "descending") {
          // 判断倒序
          this.params.c_type3_sort = 1;
        } else {
          // 默认
          // this.params.sort_type = 0;
          delete this.params.c_type3;
          delete this.params.c_type3_sort;
        }
        this.getDataList();
      }
    },
    sortChange(column) {
      // console.log(column.column.label,column,"参数");
      // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
      if (column) {
        // 判断升序
        if (column.order === "ascending") {
          this.admin_params.sort = 1;
        } else if (column.order === "descending") {
          // 判断倒序
          this.admin_params.sort = 2;
        } else {
          // // 默认
          // // this.params.c_type1 = 0;
          delete this.admin_params.sort;
          // delete this.params.c_type3_sort;
        }
        this.admin_params.page = 1
        this.getAdmin();
      }
    },
    // 点击客户标签，更改客户状态
    configCustomerStatus(row) {
      console.log(row, "设置状态");
    },
    // 获取客户状态
    getStatus() {
      if (!this.status_list.length) {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.getStutas_params })
          .then((res) => {
            if (res.status === 200) {
              this.status_list = res.data;
              this.status_list.map((item) => {
                if (item.title == "有效客户") {
                  item.value_name = 1;
                } else if (item.title == "无效客户") {
                  item.value_name = 2;
                } else if (item.title == "暂缓客户") {
                  item.value_name = 3;
                } else {
                  item.value_name = 1;
                }
                return item;
              });
              this.copy_status_list = JSON.parse(
                JSON.stringify(this.status_list)
              ); // 深拷贝客户状态
            }
          });
      }
    },
    setStatus(row) {
      this.status_list = JSON.parse(JSON.stringify(this.copy_status_list)); // 重新赋值客户状态
      let delIndex = "";
      this.status_list.map((item, index) => {
        if (item.id == row.tracking.id) {
          delIndex = index;
        }
      });
      if (typeof delIndex == "number") {
        this.status_list.splice(delIndex, 1);
      }
    },
    // 点击更改客户状态
    onClickFollowStatus(row, item) {
      // 审批无需审核 is_state == 2
      if (row.is_state == 2) {
        let examine = false;
        if (row.state_list && row.state_list.length) {
          // 遍历客户状态审批范围
          row.state_list.map((list) => {
            // 如果state_list中有当前要更改的状态
            if (list == item.id) {
              examine = true;
            }
          });
        }
        if (examine) {
          // 判断是不是客户管理员，是就不审批直接更改
          if (row.admin_list && row.admin_list.length) {
            // 遍历客户管理员
            const isLargeNumber = (item) => item == this.selfID;
            let is_admins = row.admin_list.findIndex(isLargeNumber);
            // 如果是客户管理员无需审批
            if (is_admins >= 0) {
              this.setCrmCustomerStatus(row, item); // 无需审批
            } else {
              this.requiresExamineStatus(row, item); // 需要审批
            }
          } else {
            this.requiresExamineStatus(row, item); // 需要审批
          }
        } else {
          // 不走审批
          this.setCrmCustomerStatus(row, item);
        }
      } else {
        this.$message.warning("当前客户状态不可以进行审批");
      }
      // console.log(row, item);
    },
    // 无审批修改客户状态
    setCrmCustomerStatus(row, item) {
      this.$http
        .setCrmCustomerStatus({ id: row.id, tracking_id: item.id })
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
            this.getDataList(); // 刷新数据
          }
        });
    },
    // 需审批修改客户状态
    requiresExamineStatus(row, item) {
      document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
      this.Examine_type = 19; // 切换类型为修改客户状态
      this.show_Examine_dialog = true; // 显示审批模态框
      this.ponent_Examine_data = row; // 赋值客户信息
      this.ponent_Examine_stutas = item; // 选择的要修改的状态
      // 获取部门
      if (!this.AllDepartment.length) {
        this.getDepartmentList();
      }
    },
    // 快速编辑客户维护资料
    fastEditData(row) {
      this.ponent_maintain_data = row;
      // if(this.website_id==109||this.website_id==176){
        this.show_cus_EditA = true;
      // }else{
      //   this.show_cus_Edit = true;
      // }
      
      
    },
    // 关闭快速编辑客户维护资料
    fastCloseEdit() {
      this.show_cus_EditA = false;
    },
    // 刷新页面获取最新数据
    submitMaintain() {
      this.getDataList(); // 获取最新数据
      // if(this.website_id==109||this.website_id==176){
        this.show_cus_EditA = false;
      // }else{
      //   this.show_cus_Edit = false;
      // }
      
      
    },
    // 快速查看客户手机号
    async fastLookTel(row) {
      // const exists = row.admin_list.map(item => parseInt(item, 10)).includes(this.selfID);
      // if(exists==false){
      //   if(row.tracking_identify_name=="我司成交"){
      //   this.$message.warning(" 暂无权限，客户状态为我司成交。")
      //   return
      // }
      // if(row.tracking_identify_name=="他司成交"){
      //   this.$message.warning(" 暂无权限，客户状态为他司成交，如需操作可发起审批更改客户状态。")
      //   return
      // }
      // }
      // 查看电话时跟进
      let res = await this.$http.getForceFollow()
      if (res.status == 200 && res.data && res.data.id > 0) {
        this.$confirm("您有未跟进的客户确认去跟进吗？", "提示").then(() => {
          let url = `/crm_customer_detail?id=${res.data.client_id}&type=my&tel_follow_id=${res.data.id}`;
          this.$goPath(url);
        })
        return
      }
      this.$http.setViewCrmCustomerTel(row.id).then((res) => {
        if (res.status === 200) {
          this.ponent_Tel_data = row;
          this.nowDialData = res.data
          // console.log(this.nowDialData,"客户信息");
          // if(this)
          this.show_look_Tel = true; // 显示模态框
          this.getDataList(); // 获取最新数据
        }
      });
    },
    // 关闭快速查看客户手机号回调函数
    fastCloseTel() {
      this.show_look_Tel = false;
    },
    // 提交查看手机号跟进成功回调函数
    fastSubmitTel() {
      this.getDataList(); // 获取最新数据
      this.show_look_Tel = false;
    },
    // 关闭客户审批模态框
    closeExamine() {
      this.show_Examine_dialog = false;
    },
    // 客户审批提交成功后的回调函数
    submitExamineAfter() {
      this.getDataList();
      this.show_Examine_dialog = false;
    },
    // 客户列表快速跟进客户内容
    fastFollowUp(row) {
      // console.log(row, "row");
      this.ponent_Follow_data = row; // 赋值客户信息
      this.show_Follow_dialog = true;
    },
    // 快速添加跟进成功执行
    addFollowSuccess() {
      this.$message.success("操作成功");
      this.getDataList(); // 获取最新数据
    },
    // 关闭快速跟进客户内容模态框
    closeFollow() {
      this.show_Follow_dialog = false;
    },
    // 赋值客户编号
    copyCusID(id) {
      this.$onCopyValue(id);
    },
    Quick_Edit(row){
        if(row.deal_user){
          this.customstatus = "已成交"
          this.transmitstatus = false
        }else{
          this.customstatus = "已认领"
          this.transmitstatus = true
        }
        this.ids = row.id
        this.checkStatusA(row)
        this.seastype = row.push_type
        if(row.order==1){
          this.ticky_post = "取消列表置顶"
        }else{
          this.ticky_post = "列表置顶"
        }
        this.drawer = true//显示抽屉框
      // }
    },
    //控制抽屉的出现
    handleClose() {
      this.drawer = false
      this.multipleSelection = []
      // this.getDataList(); // 刷新页面数据
    },
    //自定义表头设置
    async openCustomTableColumnSetting(){
      this.dialogs.tCustomTableCloumnSetting = true;
      await this.$nextTick();
      this.$refs.tCustomTableCloumnSetting.open().onSuccess(res => {
        this.$refs.myTable.renderTableCustomHeader();
      })
    },
    //前往客户详情页面
    goCustomerDetailPage(query, params){
      const changePageParams = Object.assign(this.$Utils.parseUrlQuery(this.searchParamsJoins), params);
      this.$router.push({
        name: 'crm_customer_detail',
        query: {...query, website_id: this.website_id},
        params: {
          changePageParams: this.$Utils.buildHttpQuery(changePageParams)
        }
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 0 24px 24px;
  .input-with-select.short{
    width: 96px;
    button{
      padding: 0 0 0 3px;
      width: 36px
    }
  }
.input-with-select{
  /deep/ .el-input__inner{
    padding: 1px 9px !important;
  }
  /deep/.el-input-group__append, .el-input-group__prepend{
    padding: 0px 13px !important;
  }
}

  .b-tabs {
    // cursor: pointer;

    .b-t-item {
      width: 122px;
      height: 50px;
      text-align: center;
      line-height: 40px;
      color: #8a929f;
      position: relative;
      margin-top: 20px;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;

      &.isactive {
        color: #00a3ff;
        background-color: #fff;

        // &::after {
        //   position: absolute;
        //   content: "";
        //   left: 50%;
        //   transform: translateX(-50%);
        //   height: 3px;
        //   // background: #2d84fb;
        //   width: 100%;
        //   display: block;
        //   margin-top: 4px;
        // }
      }
    }

    .config-customer {
      .el-button {
        padding: 7px 15px;
      }
    }
  }

  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    margin-left: -24px;
    margin-right: -24px;

    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }

  .grid-content {
    background: #fff;
    height: 113px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
    }

    .left {
      font-size: 14px;
      color: #8a929f;
      margin-right: 64px;
    }

    .number {
      display: flex;
      align-items: center;

      .number1 {
        font-size: 24px;
        color: #2e3c4e;
        margin-right: 10px;
      }

      .number2 {
        font-size: 12px;
        color: #fe6c17;
      }
    }
  }
.kehuxinxi{
    ::v-deep .el-drawer.kehu {
    width: 54% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }
  }
}
  .QuickEdit {
    width: 100%;
    height: calc(100vh - 90px);
    // max-height: 90%;
    // min-height: 70%;
    margin-top: -10px;
    overflow-y: auto;
    overflow-x: hidden;

    // overflow: auto; /* 当内容超出限制大小时显示滚动条 */
    // margin-bottom: 50px;
    .kehu_footer {
      width: 93%;
      margin: 0 auto;
      justify-content: space-between;
    }
  }

  .demo-drawer__footer {
    width: 53%;
    height: 42px;
    margin-top: 10px;
    background-color: #ffff;
    position: fixed;
    bottom: 0px;
    z-index: 2;

    .drawerfoot {
      // margin-top: 35px;
      justify-content: space-between;

      .crmstatus {
        width: 135px;
        height: 30px;
        border: 1px solid rgb(222, 225, 231);
        text-align: center;
        border-radius: 3px;
        line-height: 29px;
        font-size: 15px;
      }
    }
  }
}

.QuickEditA {
  width: 100%;
  height: calc(100vh - 90px);
  overflow-y: auto;
  overflow-x: hidden;
}

.importdrawer {
  ::v-deep .el-drawer {
    width: 36% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }

    .isfooter {
      width: 35%;
      height: 42px;
      // margin-top: 10px;
      background-color: #ffff;
      position: fixed;
      bottom: 0px;
      z-index: 2;
    }

    .isfoot {
      justify-content: flex-end;
    }
  }

}

.enterdrawer {
  ::v-deep .el-drawer {
    width: 24% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }

    .isfooter {
      width: 23%;
      height: 42px;
      // margin-top: 10px;
      background-color: #ffff;
      position: fixed;
      bottom: 0px;
      z-index: 2;
    }

    .enterfoot {
      justify-content: space-between;
    }
  }
}

.screen-type {
  display: flex;
  flex-direction: column;
  margin: -12px;
  padding: 6px 0px;

  .screen-type-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .screen-type-content:hover {
    background-color: #f5f7fa;
  }
}

.isbottom {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  flex-wrap: wrap;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
    white-space: nowrap;
  }

  .selected-header {
    font-size: 14px;
    color: #8a929f;
    margin-right: 8px;
    padding: 3px 16px;
    cursor: pointer;
    white-space: nowrap;
  }

  .label_list {
    flex-wrap: wrap;
  }

  .label_actions {
    border-radius: 4px;
    background: #e8f1ff;
    color: #2d84fb;
  }

  .label_item {
    margin-bottom: 5px;
  }
  .namedata{
    margin-left: 16px;
    .selectedname{
      margin-right: 19px;
      color: #8a929f;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .alllabeldatastyle{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 4px;
    .labeldatastyle{
      margin-top: 8px;
      margin-right: 10px;
    }
    .labelerr{
      margin-top: 12px;
    }
  }
  
  .selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
        //
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
  }
  .custom-cascader{
   ::v-deep .el-input {
    .el-input__inner {
          border: 0px solid #DCDFE6;
          .el-input__suffix {
            color: #ffff !important;
          }
        }
    }
  }
  .custom-cascader{
   ::v-deep .el-input {
      .el-input__suffix {
        display: none !important;
      }
    }
  }
  .head-list {
    margin-right: 10px;
    margin-top: 10px;
  }

  .crm-selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        // border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        padding: 15px 27px !important;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }

    &.short ::v-deep .el-input .el-input__inner {
      padding: 15px !important;
    }
  }

}
.allbtn{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;

  .text {
    font-size: 14px;
  }
}

.labelrow {
  margin-bottom: 20px;

  &.red {
    color: #f85d02;
  }
}

.t-t-b-right {
  // width: 50%;
  display: flex;
  justify-content: flex-end;
  position: relative;

  &.abs {
    position: absolute;
    // width: 690px;
    right: 40px;
    overflow: hidden;
  }

  .abs {
    .search_loudong {
      height: 30px;
      margin-top: 2px;
      width: 65px;
    }
  }
}

.crmuserbox {
  align-items: center;

  span {
    display: block;
  }

  img {
    border-radius: 50%;
  }

  .label {
    width: fit-content;
    padding: 0 6px;
    background: #2d84fb;
    border-radius: 4px;
    color: #fff;
  }
}
</style>
<style lang="scss">
.cus-box {
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;

  .cus-box-header {
    .cus-header-user {
      align-items: center;

      .customname {
        height: 30px;
        position: absolute;
        top: 10px;
        left: 7px;
      }

      .cus-userName {
        color: #2d84fb;
        text-align: left;
        margin: 20px 5px 5px 0;

        &.cus-userName_nowrap {
          max-width: 280px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .cus-sex {
        width: 16px;
        height: 16px;
        margin-top: 12px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .cus-box-head {
    color: rgb(170, 238, 235);
    width: 35px;
    height: 35px;
    position: absolute;
    top: 0px;
    right: 0px;
    cursor: pointer;

    .client-top {
      color: red;

      span {
        position: absolute;
        top: 3px;
        right: 3px;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .cus-img {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }

  .clueLabel {
    width: 20px;
    height: 20px;
    display: none;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .is_null_clueLabel {
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .fast-Edit-cus {
    display: none;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.cus-box-foot {
  align-items: center;
  flex-wrap: wrap;

  .cus-icon-level,
  .cus-share-follow,
  .cus-icon-type,
  .cus-icon-customer,
  .cus-icon-purchase,
  .cus-icon-douyin {
    font-size: 12px;
    padding: 3px 9px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
  }

  .cus-icon-label {
    color: #16a1bc;
    border: 1px solid #16a1bc;
    font-size: 14px;
    padding: 5px 11px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
  }

  .cus-icon-level {
    color: #fff;
    background: linear-gradient(180deg, #f8a707, #f85d02 100%);
  }

  .cus-share-follow {
    color: #409EFF;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }

  .cus-icon-type {
    color: #98a6c3;
    border: 1px solid #98a6c3;
  }

  .cus-icon-customer {
    display: block;
    color: #67c23a;
    border: 1px solid #67c23a;
    cursor: pointer;
  }

  .cus-icon-purchase {
    display: block;
    color: #98a6c3;
    border: 1px solid #98a6c3;
    cursor: pointer;
  }

  .cus-icon-douyin {
    color: #16a1bc;
    border: 1px solid #16a1bc;
  }

  .cus-douyinIcon {
    width: 16px;
    height: 16px;
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.table-btns {
  .search-Belong {
    .el-button {
      padding: 4px 7px;
      margin-top: 3px;
      border-radius: 2px;
    }
  }

  .last_call_follow {
    align-items: center;
    justify-content: center;

    &.w180 {
      width: 180px;
    }

    .cus-clue-text_c {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 3px;
        right: -5px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #9edf2e;
      }
    }

    .cus-clue-text_u {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 3px;
        right: -5px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #f56c6c;
      }
    }
  }

  .fast-look-tel {
    display: none;
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;
    opacity: 0.8;
    line-height: 1;

    i {
      color: #409eff;
    }

    // img {
    //   width: 100%;
    //   height: 100%;
    // }
  }
}

.labelname {
  margin-bottom: 10px;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;
  }

  .checked {
    background: rgba(45, 132, 251, 0.15);
    border: 1px solid rgba(45, 132, 251, 1);
    color: #2d84fb;
  }
}

.clueRemark {
  display: flex;

  .el-textarea {
    width: 360px;

    .el-textarea__inner {
      min-height: 40px !important;
      height: 40px;
    }
  }
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
  // color: #EBF0F7;
}

.import-member {
  .el-input__inner {
    cursor: pointer;
  }
}

.search_loudong {
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.popover-f-btn {
  font-size: 14px;
  padding: 7px 15px;
  margin-left: 10px;
}

.inp_no_border {
  width: 155px;

  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.el-table__body {
  .el-table__row {
    .is-center:nth-child(5):hover .clueLabel {
      display: block;
    }

    .is-center:nth-child(2):hover .fast-Edit-cus {
      display: block;
    }

    .is-center:nth-child(3):hover .fast-look-tel {
      display: block;
    }

    .is-center:nth-child(4):hover .followLabel {
      display: block;
    }
  }
}

.dialog_label_box {
  height: 560px;
  overflow-y: auto;
}

.search-member-box {
  .el-input {
    .el-input__inner {
      width: 155px;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }
}
.comment{
		text-align: left;
		display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical; 
	}
  .comment-popover{
	line-height: 1.5;
}
.follow-content {
  // padding-left: 10px;
  width: 167px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
.follow-takecontent{
  width: 167px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.follow-up-label {
  line-height: 1;
  flex-wrap: wrap;

  .follow-up-takeLook {
    font-size: 14px;
    padding: 3px 9px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
    border: 1px solid #cecdcd;
    color: #6a6f76;
  }
}

.follow-up-day {
  display: flex;
  align-items: center;
}

.followLabel {
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.over_text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}

.table-top-box-abs {
  // position: absolute;
  // padding-top: 30px;
  // padding: 24px 0px;
  top: 0;
  left: 0;
  right: 0;
  height: 65px;

  // transition: 0.3s;
  &.fixed {
    position: fixed;
    top: 60px;
    left: 254px;
    right: 40px;
    padding: 10px 24px;
    background: #fff;
    z-index: 100;

    .abs {
      right: 25px;
    }
  }
}

.content-box-crm {
  &.content-box-crm-pr {
    position: relative;
    padding: 24px 0;
    padding-top: 50px;
  }
}
</style>
