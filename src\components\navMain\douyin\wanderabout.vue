<template>
    <div class="page">
      <div>
        <div class="Header">流转客</div>
      </div>
        <div class="pagecontent">
          <div class="SearchCondition">
            <div class="block">
              <!-- <el-select v-model="paramsdata.date_type" placeholder="时间类型"
                size="small" style="width: 135px;margin-right:10px;;" @change="Sortevents">
                  <el-option
                    v-for="item in lookoptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select> -->
              <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                @change="onChangeTime">
              </el-date-picker>
              <el-cascader placeholder="请选择部门" size="small" style="width: 210px; margin:0px 10px 0px 10px" v-model="paramsdata.department_id" clearable
                :options="department_list" :props="{
                  value: 'id',
                  label: 'name',
                  children: 'subs',
                  emitPath: false,
                }" @change="departmentChange"></el-cascader>
              <el-input style="width: 200px" placeholder="请输入姓名" size="small" v-model="paramsdata.user_name"></el-input>
              <el-button style="margin: 0 16px" size="small" type="primary" class="el-icon-search" @click="departmentChange">搜索</el-button>
              <!-- <el-cascader style="margin:0px 10px;" size="small" v-model="member_value"
                :options="member_listNEW" clearable filterable placeholder="成员" :style="{
                    minWidth: '20px',
                     width: '110px',
                    }" :props="{
                        label: 'user_name',
                        value: 'id',
                        children: 'subs',
                        checkStrictly: true,
                    }" @change="loadFirstLevelChildren">
                </el-cascader> -->
                <!-- <el-select v-model="paramsdata.sort" placeholder="排序"
                size="small" style="width: 145px;" @change="Sortevents"
                clearable>
                  <el-option
                    v-for="item in sortoptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.value">
                  </el-option>
                </el-select> -->
            </div>
            <div class="head-list">
              <el-button v-show="this.show>0" size="small" type="primary" @click="exporttakelook">导出</el-button>
                <!-- <el-button class="listbnt" size="mini" @click="doulist">
                  <div class="flex-row items-center">
                    <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinlist.png" alt="">
                    <span>数据列表</span>
                  </div>
                </el-button> -->
            </div>
          </div>
          <!-- <div class="SearchCondition">
            
          </div> -->
          <div class="taketable" v-fixed-scroll="62">
            <el-table
            v-loading="is_table_loading"
                :data="classifydata"
                style="width: 100%;margin-bottom: 20px;"
                :header-cell-style="{ background: '#EBF0F7' }"
                row-key="id"
                border
                show-summary
                :summary-method="handleSummary">
                <el-table-column
                  prop="user_name"
                  align="center"
                  label="姓名"
                  fixed
                  >
                </el-table-column>
                <el-table-column
                  prop="khzl"
                  align="center"
                  label="客户总量"
                  v-slot="{ row }">
                  <!-- <div style="font-weight: bold; color:red;"> -->
                     {{row.khzl}}
                  <!-- </div> -->
                
                </el-table-column>
                <el-table-column
                  prop="lr"
                  align="center"
                  label="录入量"
                  >
                </el-table-column>
                <el-table-column
                  prop="transmit_num"
                  align="center"
                  label="转交量"
                  >
                </el-table-column>
                <el-table-column
                  prop="my_transmit"
                  align="center"
                  label="转交次数"
                  v-slot="{ row }">
                  <!-- <div style="font-weight: bold;"> -->
                    {{row.my_transmit}}
                  <!-- </div> -->
                </el-table-column>
                <el-table-column
                  prop="copy_num"
                  align="center"
                  label="复制量"
                  >
                </el-table-column>
                <el-table-column
                  prop="my_copy"
                  align="center"
                  label="复制次数"
                  v-slot="{ row }">
                  {{row.my_copy}}
                </el-table-column>
                <el-table-column
                  prop="auto_assign"
                  align="center"
                  label="自动流转"
                  v-slot="{ row }">
                  {{row.auto_assign}}
                </el-table-column>
                <el-table-column
                  prop="hf"
                  align="center"
                  label="回访量"
                  v-slot="{ row }">
                  {{row.hf}}
                </el-table-column>
                <el-table-column
                  prop="dk"
                  align="center"
                  label="带看量"
                  v-slot="{ row }">
                  {{row.dk}}
                </el-table-column>
                <el-table-column
                  prop="system_assign_num"
                  align="center"
                  label="系统分配"
                  v-slot="{ row }">
                  {{row.system_assign_num}}
                </el-table-column>
                <el-table-column
                  prop="department"
                  align="center"
                  label="部门名称"
                  v-slot="{ row }">
                  {{row.department}}
                </el-table-column>
                <el-table-column
                label="流转客"
                v-slot="{ row }"
                fixed="right">
                <el-link type="primary" :underline="false" target="_blank" @click="Jumplink(row)">查看</el-link>
                </el-table-column>
            </el-table>
          <div class="page_footer flex-row items-center">
            <div class="page_footer_l flex-row flex-1 items-center">
              <div class="head-list">
                <el-button type="primary" size="small" @click="empty">清空</el-button>
              </div>
              <div class="head-list">
                <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
              </div>
            </div>
            <div style="margin-right:10px;">
              <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
                :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                @current-change="onPageChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
          </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            timeValue:"",//时间检索
            pickerOptions: {
                shortcuts: [{
                  text: '今天',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                    text: '昨天',
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date(end);
                      start.setDate(start.getDate() - 1);
                      end.setDate(start.getDate());
                      picker.$emit('pick', [start, end]);
                    }
                  },{
                  text: '本周',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上周',
                  onClick(picker) {
                    const end = new Date(); // 获取当前日期
                      end.setDate(end.getDate() - end.getDay() - 7); // 获取上周的最后一天
                      end.setHours(23, 59, 59, 0);
                      const start = new Date(end);
                      start.setDate(start.getDate() - 6); // 获取上一周的第一天
                      start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '本月',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上月',
                  onClick(picker) {
                    const end = new Date();
                    end.setDate(0); // 获取上个月的最后一天
                    end.setHours(23, 59, 59, 0);
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                },]
            },
            member_value:"",
            //成员
            member_listNEW: [],
            classifydata:[],//表格数据
            params:{
                total:0,
                per_page:10,
                page:1
            },
            paramsdata:{
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                // admin_id:"",
                // sort:"",
                // date_type:1
            },
            lookoptions:[
              {id:1,name:"带看日期"},
              {id:2,name:"带看创建时间"},
            ],
            sortoptions:[
              {id:1,value:"custom_num",name:"带看客户量降序"},
              {id:2,value:"first_take_num",name:"首看降序"},
              {id:3,value:"again_take_num",name:"复看降序"},
              {id:4,value:"total_take_num",name:"带看总量降序"},
              {id:5,value:"accompany_take_num",name:"陪看降序"},
            ],
            show:0,
            is_table_loading:false,
            department_list:[],
        }
    },
    mounted(){
      let pagenum = localStorage.getItem( 'pagenum')
        this.params.per_page = Number(pagenum)||10
        this.paramsdata.per_page = Number(pagenum)||10
      if(this.$store.state.ismanager){
        this.show =  this.$store.state.ismanager
      }else{
        this.btnexport()
      }
        
        this.getlookdata()
        this.MembersNEW()
        this.btnexport()
        this.getCrmDepartmentList()
    },
    methods:{
      handleSummary({ columns, data }) {
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          if (index === 11) {
            sums[index] = '--';
            return;
          }
          if (index === 12) {
            sums[index] = '--';
            return;
          }
          if (column.property === 'khzl' || column.property === 'lr' || column.property === 'transmit_num'
          || column.property === 'my_transmit' || column.property === 'copy_num' || column.property === 'my_copy'
          || column.property === 'auto_assign' || column.property === 'hf' || column.property === 'dk'
          || column.property === 'system_assign_num') {
            const values = data.map(item => parseFloat(item[column.property]));
            if (!isNaN(values[0])) {
              sums[index] = values.reduce((prev, curr) => {
                const value = parseFloat(curr);
                return isNaN(value) ? prev : prev + value;
              }, 0);
              sums[index] = Math.round(sums[index] * 100) / 100;
            } else {
              sums[index] = '';
            }
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
        //获取是否是可以显示导出按钮
        btnexport(){
          this.$http.determinecustomeradmin().then((res)=>{
            if(res.status==200){
              console.log(res.data," //判断是不是客户管理员");
              this.show = res.data.is_manager
            }
          })
        },
        // 获取部门列表
        getCrmDepartmentList() {
          this.$http.getCrmDepartmentList().then((res) => {
            if (res.status === 200) {
              this.department_list = res.data;
            }
          });
        },
        //获取表格数据
        getlookdata(){
          this.is_table_loading = true
            this.$http.getwanderaboutdata(this.paramsdata).then((res)=>{
                if(res.status==200){ 
                  this.is_table_loading = false
                  console.log(res.data);
                    this.classifydata = res.data.data
                    this.params.total = res.data.total
                }
            })
        },
        // 自定义筛选时间发生改变时触发
        onChangeTime(e) {
          this.paramsdata.start_date = e ? e[0] : ""; // 赋值开始时间
          this.paramsdata.end_date = e? e[1] : ""; // 赋值结束时间
          this.paramsdata.page = 1; // 显示第一页
          this.params.page = 1; // 显示第一页
          this.getlookdata(); // 获取最新数据
        },
        // 获取成员的接口（新）
        MembersNEW(){
          this.$http.getDepartmentMemberListNew().then((res)=>{
            if(res.status==200){
                console.log(res.data);
              this.member_listNEW = res.data
            }
          })
        },
        //成员
        loadFirstLevelChildren(value) {
          console.log(value);
          console.log(this.member_value);
          this.paramsdata.admin_id = value[0]
          this.getlookdata(); // 获取最新数据
        },
        Sortevents(){
          this.getlookdata(); // 获取最新数据
        },
        //跳转到数据列表页
        doulist() {
          this.$goPath(`/crm_Follow_up_list`);
        },
        exporttakelook(){
          this.$confirm('确定要导出数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
           this.$http.exportbusiness(this.paramsdata).then((res)=>{
            if(res.status == 200){
              window.open(res.data);
            }
           })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出'
            });          
          });
        },
        //部门检索
        departmentChange(){
          this.params.page = 1; // 显示第一页
          this.getlookdata(); // 获取最新数据
        },
        //分页
        onPageChange(current_page) {
          this.params.page = current_page;
          this.paramsdata.page = current_page;
          this.getlookdata();
        },
        //每页几条
        handleSizeChange(e){
          this.params.per_page = e
          this.paramsdata.per_page = e;
          this.getlookdata();
        },
        //跳转流转视图
        Jumplink(row){
          this.$goPath(`/crm_information_follow?uid=${row.id}`);
        },
        //清空
        empty(){
          this.timeValue =  ""
          this.member_value = ""
          this.paramsdata={
            per_page:this.params.per_page,
            page:1,
            start_date:"",
            end_date:"",
            // admin_id:"",
            // sort:"",
            // date_type:1
          }
          this.getlookdata();
        },
        //刷新
        Refresh(){
          this.getlookdata();
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 20px 24px 80px;
    .Header{
      width: 110px;
      height: 40px;
      background-color: #ffffff;
      text-align: center;
      line-height: 42px;
      color: #8a929f;
    }
    .pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .SearchCondition{
            margin: 20px;
            display: flex;
            justify-content: space-between;
        }
        .head-list{
          display: flex;
        }
        .taketable{
            width: 97%;
            margin: 0 auto;
            .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
           ::v-deep .el-table__fixed{
            padding-bottom: 0px !important;
           }
           ::v-deep .el-table__fixed-right{
            padding-bottom: 0px !important;
           }
        }
    }
}
</style>