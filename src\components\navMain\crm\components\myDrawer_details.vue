<template>
    <div
        class="follow-record"
        v-infinite-scroll="loadMoreMaintain"
        style="overflow: auto;height: 400px;"
        >
        <el-timeline
            v-if="MaintainRecord_list.length"
            style="margin-left: 10px"
        >
            <el-timeline-item
                v-for="(Phone, index) in MaintainRecord_list"
                :key="index"
                placement="top"
                color="#2D84FB"
            >
            <div class="FollowRecord">
                <div style="width: 100%">
                  <div class="agent_info flex-row align-center">
                      <div class="time">
                        {{ Phone.created_at }}
                      </div>
                      <div class="agent_name" v-if="Phone.admin">
                        {{ Phone.admin.user_name }}/{{ Phone.admin.department_name }}
                      </div>
                  </div>
                  <div class="FollowText">
                      <div
                          class="f_content"
                          :class="marginFilter(Phone) ? 'mb20' : ''"
                      >
                          <div slot="reference" class="flex-row">
                            <!-- 电话跟进内容 -->
                              <span v-if="Phone.call_phone_id">
                                {{ Phone | stringSplice }}
                              </span>
                              <span class="f_line" v-else>
                                <tContentOverflow> <span class="f_line" v-html="Phone.content"></span></tContentOverflow>
                              </span>
                              <div v-if="Phone.status==8&&Phone.follow_id==0" style="color:red;margin-top:1px;">(未跟进)</div>
                          </div>
                      </div>
                  </div>
                  <!-- 跟进语音 -->
                  <voiceAudioPlayer
                    v-if="Phone.url"
                    :activity="Phone"
                  ></voiceAudioPlayer>
                  <!-- 跟进图片 -->
                  <div
                    v-if="Phone.file_path_path"
                    class="follow-picture"
                  >
                    <div
                      class="follow-picture-box"
                      v-for="(item, index) in Phone.file_path_path"
                      :key="index"
                    >
                      <img :src="$imageFilter(item, 'w_240')" alt="" />
                      <span class="uploader-actions" v-if="item">
                        <span
                          class="uploader-actions-item"
                          @click="handlePictureCardPreview(item, index)"
                        >
                          <i class="el-icon-view"></i>
                        </span>
                      </span>
                    </div>
                  </div>
                  <AudioPlayer
                      v-if="Phone.id && Phone.record_url != ''"
                      :activity="Phone"
                      select="CustomerFollow"
                  ></AudioPlayer>
                </div>
            </div>
            </el-timeline-item>
        </el-timeline>
        <myEmpty v-else :loading="loading"></myEmpty>
        <!-- 查看已上传图片模态框 -->
        <div
            class="mask1"
            v-if="show_dialog_pictures"
            @click="show_dialog_pictures = false"
            >
            <div class="preview_img" @click.prevent.stop="() => {}">
                <img id="preImg" :src="dialog_pictures_src" alt="" />
            </div>
        </div>
    </div>
</template>
<script>
import myEmpty from "@/components/components/my_empty.vue";
import AudioPlayer from "@/components/components/audioPlayer.vue";
import voiceAudioPlayer from "@/components/components/voiceAudioPlayer.vue";
import tContentOverflow from '@/views/crm/live_room/components/tContentOverflow.vue'
export default {
    components: {
        myEmpty,
        AudioPlayer,
        voiceAudioPlayer,
        tContentOverflow
    },
    props: {
        MaintainRecord_list: {
            type: Array,
            default: () => {}
        },
        loading: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            show_dialog_pictures: false, // 查看已上传的图片
            dialog_pictures_src: '', // 查看已上传图片的src
        }
    },
    filters: {
      stringSplice(value) {
        if(value.call_phone_id) {
            let str = value.content.split("通过");
            return "通过外显号码" + "(" + value.call_show_phone + ")" + str[1].toString()
        } else {
            return value.content;
        }
      }
    },
    methods: {
        // 加载更多维护记录
        loadMoreMaintain() {
            this.$emit("loadMoreMaintain", {});
        },
        marginFilter(value) {
            if (value.content) { // 如果存在跟进内容
                if (value.url) {
                    return true;
                } if (value.record_url != "") {
                    return true;
                } else {
                    return false;
                }
            }
        },
        // 查看已上传的图片
        handlePictureCardPreview(item) {
            // console.log(item,'item');
            this.show_dialog_pictures = true;
            if (item.url) {
                this.dialog_pictures_src = item.url;
            } else {
                this.dialog_pictures_src = item;
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.follow-record {
  max-height: 400px;
  margin-bottom: 116px;
  // ==========
  .el-timeline {
    font-size: 15px;
    .el-timeline-item:hover {
      .el-timeline-item__wrapper {
        .el-timeline-item__content {
          .agent_info {
            .follow_info_box {
              display: flex;
            }
          }
        }
      }
    }
  }
  // ==========
  // position: relative;
  .agent_info {
    height: 25px;
    margin: -15px 0 24px 0;
    .time {
      margin-right: 10px;
      color: #8a929f;
    }
    .img {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .agent_name {
      font-size: 12px;
      color: #8a929f;
    }
    .show_is_top {
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
      color: #ffffff;
      padding: 2px 8px;
      border-radius: 4px;
      margin-left: 20px;
    }
    .follow_info_box {
      display: none;
      flex-direction: row;
      border: 1px solid #8a929f;
      border-radius: 3px;
      margin-left: 20px;
      .follow_info_praise,
      .follow_info_copy {
        width: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        text-align: center;
        padding: 6px;
        color: #8f959e;
        border-right: 1px solid #8f959e;
        cursor: pointer;
      }
      .follow_info_praise:active {
        background-color: #eff0f1;
      }
      .follow_info_copy:active {
        background-color: #eff0f1;
      }
      .follow_add_top {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #8f959e;
        padding: 6px;
        cursor: pointer;
      }
      .follow_add_top:active {
        background-color: #eff0f1;
      }
    }
  }
  .FollowText {
    display: flex;
    // width: 560px;
  }
  .follow-picture {
    max-width: 400px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-top: 20px;
    .follow-picture-box {
      width: 60px;
      height: 60px;
      margin-right: 10px;
      margin-bottom: 5px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .uploader-actions {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        border-radius: 4px;
        cursor: default;
        text-align: center;
        color: #fff;
        opacity: 0;
        font-size: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s;
        .uploader-actions-item {
          font-size: 20px;
          cursor: pointer;
          & i {
            color: #fff;
          }
        }
      }
      .uploader-actions:hover {
        opacity: 1;
      }
      .follow-delete-picture {
        display: none;
        position: absolute;
        top: -7px;
        right: -8px;
        cursor: pointer;
      }
    }
    .follow-picture-box:hover .follow-delete-picture {
      display: block;
    }
  }
  .follow-praise {
    display: inline-block;
    border-radius: 15px;
    color: #8a929f;
    background-color: #f1f4fa;
    padding: 5px 12px;
    box-sizing: border-box;
    margin-right: 20px;
    .follow-praise-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      .follow-praise-img {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 19px;
          height: 19px;
        }
      }
      .follow-praise-separate {
        width: 1px;
        height: 14px;
        background-color: #8a929f;
        margin: 0 5px;
        opacity: 0.5;
      }
      .follow-praise-text {
        padding: 0 5px;
      }
    }
  }
  .f_content {
    display: flex;
    align-items: center;
    max-width: 450px;
    // margin-bottom: 20px;
    .recode_Time {
      color: #409eff;
      margin-left: 5px;
    }
    &.red {
      color: #fc0606;
    }
    // & span {
    //   display: flex;
    //   align-items: center;
    // }
    .f_line {
    line-height: 24px;
    white-space: pre-wrap;
  }
  }
  .infoFrame {
    display: none;
    width: 200px;
    position: fixed;
    top: 0px;
    left: 0px;
    background: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;
    border-radius: 7px;
    z-index: 2;
    .infoAngle {
      position: absolute;
      top: 74px;
      left: 95px;
      width: 0px;
      height: 0px;
      color: #fff;
    }
    .infoFrame-box {
      display: flex;
      padding: 10px;
      .infoFrame_icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #2d84fb;
        color: #fff;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 5px 12px 0;
      }
      .infoFrame-info {
        display: flex;
        flex-direction: column;
        color: #303133;
        & span {
          font-size: 12px;
          margin-top: 5px;
        }
        .infoFrame-phone {
          display: flex;
          align-items: center;
          .numberTop {
            background: #afb5b42e;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            margin-left: 5px;
            cursor: pointer;
          }
        }
      }
    }
  }
}
.contenturkl {
      display: block;
      color:  #292C39;
      font-size: 32rpx;
      font-weight: 400;
      line-height: 1.8;
      white-space: normal; // 规定段落中的文本不进行换行
      word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
      word-wrap: break-word;
    }
.mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 60px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
      object-fit: cover;
    }

    .img {
      max-width: 800px;
      height: 600px;
      overflow-y: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
</style>