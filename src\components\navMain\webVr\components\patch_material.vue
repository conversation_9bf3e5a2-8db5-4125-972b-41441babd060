<template>
    <div>
        <el-table
            :data="forData_list"
            border
            style="width: 100%">
            <el-table-column
                prop="pk_media_res"
                label="素材id">
            </el-table-column>
            <el-table-column
                prop="media_name"
                label="素材名称">
            </el-table-column>
            <el-table-column
                prop="create_time"
                label="添加时间">
            </el-table-column>
            <el-table-column
                prop="media_size"
                label="素材大小"
                v-slot="{ row }">
                <template>
                    {{ (row.media_size / 1024).toFixed(1) }}MB
                </template>
            </el-table-column>
            <el-table-column
                prop="media_suffix"
                label="素材后缀">
            </el-table-column>
            <el-table-column
            label="操作" v-slot="{ row }">
            <el-link
                type="info"
                @click="onCopyVideo(row)">
                复制
            </el-link>
            <el-link
                type="success"
                style="margin-left: 12px"
                @click="onPreview(row)">
                预览
            </el-link>
            <el-link
                type="primary"
                style="margin-left: 12px"
                @click="materialRename(row)">
                重命名
            </el-link>
            <el-link
                type="danger"
                style="margin-left: 12px"
                @click="deleteMaterial(row)">
                删除
            </el-link>
            </el-table-column>
        </el-table>
        <el-dialog
            title="素材重命名"
            :visible.sync="dialogMaterialRename"
            width="680px"
        >
            <el-form :model="params_rename" label-width="80px">
                <el-form-item label="素材名称">
                    <el-input v-model="params_rename.media_name"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer Rename-footer">
                <el-button @click="dialogMaterialRename = false">取 消</el-button>
                <el-button type="primary" @click="confirmRename">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog 
            :visible.sync="dialogUploadPicture" 
            class="dialog-Photo"
            @close="closePicture"
        >
            <video 
                ref="videos"
                style="width: 100%; height: 600px;" 
                controls 
                :src="dialogVideo">
            </video>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        forData_list: {
            type: Array,
            default: () => {}
        }
    },
    data() {
        return {
            // 素材重命名参数
            params_rename: {
                media_name: "", // 素材名称
                id: "", // 素材id
            },
            dialogMaterialRename: false, // 素材重命名模态框
            dialogUploadPicture: false, // 视频素材模态框
            dialogVideo: "", // 视频src
        }
    },
    methods: {
        // 素材预览
        onPreview(row) {
            this.dialogVideo = row.absolutelocation;
            this.dialogUploadPicture = true;
        },
        // 素材复制
        onCopyVideo(row) {
            this.$onCopyValue(row.absolutelocation)
        },
        // 素材重命名
        materialRename(row) {
            this.dialogMaterialRename = true;
            // 赋值当前重命名的素材id
            this.params_rename.id = row.pk_media_res;
            this.params_rename.media_name = row.media_name;
        },
        // 确定将素材重命名
        confirmRename() {
            this.$http.editMaterialRename(this.params_rename).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    });
                    this.dialogMaterialRename = false;
                    // this.getDataList();
                    this.$emit("materialList", "");
                }
            })
        },
        // 删除素材
        deleteMaterial(row) {
            this.$confirm('此操作将永久删除该素材, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.deleteMaterial(row.pk_media_res).then((res) => {
                    if(res.status == 200) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.$emit("materialList", "");
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        // 关闭视频模态框
        closePicture() {
            this.dialogVideo = "";
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .dialog-Photo {
    .el-dialog {
        .el-dialog__header {
            .el-dialog__title {
                border-left: none;
            }
        }
    }
}
</style>