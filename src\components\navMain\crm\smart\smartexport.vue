<template>
    <el-dialog :visible.sync="show" title="自定义导出列表" width="750px" @close="onClose">
        <div class="column-wrap">
            <el-card shadow="never" class="column">
                <div slot="header" class="column-header">
                    可添加列
                    <span class="op" @click="restoreDefault">恢复默认</span>
                </div>
                <div class="column-body">
                    <el-checkbox-group v-model="seledColumnIds">
                        <el-checkbox v-for="col in allColumns" 
                            :key="col.id" :label="col.id" 
                            :disabled="col.must==1" class="column-check-item">
                            {{col.name}}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </el-card>
            <div class="column-separate"></div>
            <el-card shadow="never" class="column">
                <div slot="header" class="column-header">
                    已显示列 ( {{seledColumnLength}} )
                </div>
                <div class="column-body">
    
                    <div class="column-seled-item column-seled-item-disabled" v-for="col in topSeledColumns" :key="col.id">
                        <i class="el-icon-lock icon-lock"></i><span class="title">{{col.name}}</span>
                    </div>        
                    
                    <div class="column-seled-draggable">
                        <draggable v-model="middleSeledColumns" animation="300">
                            <transition-group>
                            <div class="column-seled-item column-seled-item-draggable" v-for="col in middleSeledColumns" :key="col.id">
                                <i class="el-icon-rank icon-drag"></i><span class="title">{{col.name}}</span>
                                <i class="el-icon-close icon-remove" @click="removeSeledColumn(col.id)" v-if="!col.must"></i>
                            </div>
                            </transition-group>
                        </draggable>
                    </div>
                    
                    <div class="column-seled-item column-seled-item-disabled" v-for="col in bottomSeledColumns" :key="col.id">
                        <i class="el-icon-lock icon-lock"></i><span class="title">{{col.name}}</span>
                    </div>   
                </div>
            </el-card>
        </div> 
        <div class="kexuan">
            <div class="kexuan-text">导出可选排序列</div>
            <el-radio-group v-model="checkList">
              <el-radio
                class="radio"
                v-for="item in updatedParams"
                :key="item.value"
                :label="item.value"
              >
                {{ item.name }}
              </el-radio>
            </el-radio-group>
        </div>    
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
    </el-dialog>
    
    
    </template>
    
    <script>
    import draggable from 'vuedraggable';
    export default {
        name: 'tCustomTableCloumnSetting',
        components: {
            draggable
        },
        props: {
            tableName: { type: String, default: ''},
            headerData:{
                type:Array,
                default:()=>[]
            },
            optionalsorting:{
                type:Array,
                default:()=>[]
            }
        },
        data(){
            return {
                show: false,        //dialog是否显示
                successFn: null,    //成功回调
                allColumns: [],
                seledColumnIds: [],
                sortedSeledColumnIds: [],//选中的header
                originalSeledColumnIds: [],
                fields:[],
                checkList: 'khzl',
                sortingdata:[],//排序数据
                updatedParams:[],
            }
        },
        computed: {
            //所有列id
            allColumnIds(){
                return this.allColumns.map(e => e.id);
            },
            //选中列
            seledColumns(){
                const columns = [];
                for(const id of this.sortedSeledColumnIds){
                    const item = this.allColumns.find(e=>e.id == id);
                    item && columns.push(item);
                }
                        // 处理逻辑
                        const filterParams = (A, B) => {
                            // 提取 data 中的 name 列表，如果 data 为空，则创建一个空的 Set
                            const dataNames = columns.length > 0 ? new Set(columns.map(item => item.field)) : new Set();
                            // 过滤 params，保留在 dataNames 中存在的项
                            return this.sortingdata.filter(item => dataNames.has(item.value));
                        };
                        // 调用处理函数
                        this.updatedParams = filterParams(columns, this.sortingdata);
                this.fields = columns.map(column => column.field);
                return columns;
            },
            topSeledColumns(){
                const columns = [];
                for(const col of this.seledColumns){
                    if(col.fixed != 'left'){
                        break;
                    }
                    columns.push(col);
                }
                return columns;
            },
            bottomSeledColumns(){
                const columns = [];
                let seledColumns = this.seledColumns.slice(this.topSeledColumns.length).reverse();
                for(const col of seledColumns){
                    if(col.fixed != 'right'){
                        break;
                    }
                    columns.push(col);
                }
                return columns.reverse();
            },
            middleSeledColumns:{
                set(cols){
                    this.sortedSeledColumnIds.splice(this.topSeledColumns.length, cols.length, ...cols.map(e=>e.id));
                },
                get(){
                    const columnIds = this.topSeledColumns.concat(this.bottomSeledColumns).map(e=>e.id);
                    return this.seledColumns.filter(col => !columnIds.includes(col.id))
                }
            },
            seledColumnLength(){
                return this.seledColumnIds.length
            },
            //是否全选
            isSeledAll(){
                return this.seledColumnIds.length == this.allColumnIds.length;
            }
        },
        watch: {
            seledColumnIds(){
                this.setSortedSeledColumnIds();
            }
        },
        created(){
            
        },
        methods: {
            open(){
                this.getSettingData();
                this.sortingdata = this.optionalsorting
                this.checkList='khzl'
                this.show = true;
                return this;
            },
            cancle(){
                this.show = false;
            },
            onClose(){
                this.seledColumnIds = this.originalSeledColumnIds;
                this.checkList = ''
            },
            confirm(){
                let data ={
                    fields:this.fields,
                    sort:this.checkList
                }
                this.$emit("exportData",data)
                
            },
            onSuccess(fn){
                fn && (this.successFn = fn);
                return this;
            },
            //设置选中的ids
            setSortedSeledColumnIds(){
                const ids = this.seledColumnIds;
                const diffIds = ids.filter(id => !this.sortedSeledColumnIds.includes(id));
                if(diffIds.length){
                    const pushIndex  = this.sortedSeledColumnIds.length - this.bottomSeledColumns.length;
                    this.sortedSeledColumnIds.splice(pushIndex, 0, ...diffIds);
                }else{
                    this.sortedSeledColumnIds = this.sortedSeledColumnIds.filter(id => ids.includes(id));
                }
            },
            //移除选中列
            removeSeledColumn(id){
                this.seledColumnIds = this.seledColumnIds.filter(e => e != id);
            },
            //恢复默认
            restoreDefault(){
                this.sortedSeledColumnIds = [];
                this.seledColumnIds = this.allColumnIds;
                if(this.isSeledAll){
                    this.setSortedSeledColumnIds();
                }
            },
            //获取配置数据
            getSettingData(){
                    //所有列
                    this.allColumns  = this.headerData.map((column, index) => ({
                        ...column,
                        id: index + 1
                      }));
                    //设置fixed="left"列
                    for(const col of this.allColumns){
                        if(col.fixed){
                            col.fixed = 'left'
                        }else{
                            break;
                        }
                    }
                    //设置fixed="right"列
                    for(const col of this.allColumns){
                        if(col.fixed){
                            col.fixed = 'right'
                        }else{
                            break;
                        }
                    }
                    //已选中列ID
                    this.seledColumnIds = [];
                    //已选中项
                    if(this.headerData.length){
                        //默认选中全部
                        this.seledColumnIds = this.allColumns.map(e => e.id)
                    }else{
                        //为空时选中全部
                        this.seledColumnIds = this.allColumns.map(e => e.id)
                    }
                    const seledColumns = this.seledColumnIds.map( id => this.allColumns.find(e => e.id == id)).filter(e=>e)
                    this.originalSeledColumnIds = seledColumns;
            },
        },
    }
    </script>
    
    <style scoped lang="scss">
    .column-wrap{
        display: flex;
        flex-direction: row;
        .column{
            flex: 1;
            .column-header{
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                .op{
                    color: #409EFF;
                    cursor: pointer;
                }
            }
            .column-body{
                height: 360px;
                overflow: auto;
                padding: 20px;
                .column-check-item{
                    box-sizing: border-box;
                    display: inline-block;
                    min-width: 50%;
                    margin: 0;
                    padding: 6px 10px 6px 0;
                }
                .column-seled-draggable{
                    margin: 10px 0;
                }
                .column-seled-item{
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    height: 28px;
                    border-radius: 8px;
                    background-color: #f6f6f6;
                    +.column-seled-item{
                        margin-top: 10px;
                    }
                    .title{
                        flex: 1;
                        display: inline-block;
                        overflow: hidden;
                        max-height: 100%;
                    }
                    .icon-drag,.icon-remove,.icon-lock{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        width: 30px;
                    }
                    .icon-remove{
                        cursor: pointer;
                        color:#999;
                        &:hover{
                            color:#333
                        }
                    }
                    &.column-seled-item-disabled{
                        .icon-lock,.title{
                            color: rgba(0,0,0,.35);
                        }
                    }
                    &.column-seled-item-draggable{
                        cursor: move;
                    }
                }
            }
        }
        .column-separate{
            width: 20px;
        }
    }
    .kexuan{
        margin-top: 20px;
        .kexuan-text{
            line-height: 24px;
            font-size: 18px;
            color: #303133;
            font-weight: bold;
        }
        .radio{
            margin-top: 15px;
        }
    }
    ::v-deep .el-card__header{
        padding: 10px 20px;
        background-color: #f4f4f4;
    }
    ::v-deep .el-card__body{
        padding: 0;
    }
    </style>