<template>
    <div>
      <div>
        <el-form :inline="true" :model="mingyuandata" class="demo-form-inline">
          <el-form-item label="明源账号">
            <el-select v-model="mingyuandata.id" placeholder="请选择明源账号">
              <el-option
                v-for="item in tableDatakey"
                :key="item.id"
                :label="item.title"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">拉取</el-button>
          </el-form-item>
        </el-form>
      </div>
        <el-table 
          v-loading="is_table_loading"
            :data="tableData"
            border
            ref="table"
            style="width: 100%"
            :header-cell-style="{ background: '#EBF0F7' }">
            <el-table-column
              prop="project_name "
              label="项目名称"
              fixed
              v-slot="{ row }"
              min-width="170"> 
              {{row.project_name?row.project_name:"--"}}
            </el-table-column>
            <el-table-column
              prop="city_name "
              label="项目城市"
              fixed
              v-slot="{ row }"
              min-width="170"> 
              {{row.city_name?row.city_name:"--"}}
            </el-table-column>
            <el-table-column
              prop="area_name "
              label="项目区域"
              v-slot="{ row }"
              min-width="170"> 
              {{row.area_name?row.area_name:"--"}}
            </el-table-column>
            <el-table-column
              prop="created_at "
              label="拉取时间"
              v-slot="{ row }"
              min-width="170"> 
              {{row.created_at?row.created_at:"--"}}
            </el-table-column>
            <el-table-column
              prop="updated_at "
              label="编辑时间"
              v-slot="{ row }"
              min-width="150"> 
              {{row.updated_at?row.updated_at:"--"}}
            </el-table-column>
            <el-table-column
              prop="updated_at "
              label="报备"
              v-slot="{ row }"
              min-width="150"> 
              {{row.hide_type==0?"不可以隐号报备":row.hide_type==1?"可隐号报备至到访":"可隐号报备至成交"}}
            </el-table-column>
            <el-table-column
              prop="updated_at "
              label="经纪人是否有该项目的合作关系"
              v-slot="{ row }"
              min-width="220"> 
              {{row.is_partner==0?"未合作":"合作"}}
            </el-table-column>
            <el-table-column
              prop="bind_system_project "
              label="是否绑定T+系统项目"
              v-slot="{ row }"
              min-width="150"> 
              {{row.bind_system_project==0?"未绑定":"已绑定"}}
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              v-slot="{ row }"
              min-width="200"
              align="center">
                  <template v-if="!row.newproject">
                    <el-link
                      type="primary"
                      :underline="false"
                      style="margin-left:10px;"
                      @click="bindingproject(row)">绑定项目
                    </el-link>
                  </template>
                  <template v-else>
                    <el-select
                      v-model="row.bingdproject"
                      placeholder="请选择项目">
                      <el-option
                        v-for="item in system"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                      </el-option>
                    </el-select>
                    <div class="btn">
                      <el-link v-if="!row.isSaving" type="text" style="margin-right:5px;" :underline="false" @click="cancel(row)">取消</el-link>
                      <el-link v-if="!row.isSaving" type="primary" :underline="false" @click="savePhone(row)">保存</el-link>
                      <el-link v-if="row.isSaving" type="primary" :underline="false" disabled>正在保存...</el-link>
                  </div>
                  </template>
            </el-table-column>
        </el-table>
        <div class="tab-content-footer">
                 <el-button type="primary" size="small" @click="empty">刷新</el-button>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="params.per_page"
                    :current-page="params.page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total">
                  </el-pagination>
          </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            params:{
                page: 1,
                per_page: 10,

            },
            mingyuandata:{
                id:"",
            },
            is_table_loading:false,//表格loading
            tableData:[],//明源云项目
            system:[],//系统项目
            tableDatakey:[],

        }
    },
    mounted(){
        this.getmykeylist()
        this.getmyprojectmanagement()
        this.getmysystemproject()
    },
    methods:{
        getmyprojectmanagement(){
          this.is_table_loading = true
            this.$http.myprojectmanagement(this.params).then(res=>{
                if(res.status==200){
                    console.log(res.data,"明源云项目");
                    this.tableData = res.data.data
                    this.params.total = res.data.total
                    this.is_table_loading = false
                }
            })
        },
        getmysystemproject(){
            this.$http.mysystemproject().then(res=>{
                if(res.status==200){
                  this.system = res.data.data
                    // console.log(res.data,"系统项目");
                }
            })
        },
        //绑定系统项目
        bindingproject(row){
          // console.log(row);
          this.$set(row, 'newproject', 1);
        },
        //取消
        cancel(row){
          this.$set(row, 'newproject', "");
        },
        //刷新
        empty(){
          this.getmyprojectmanagement()
        },
        //保存
        savePhone(row){
          // console.log(row);
          let params = {
            id:row.id,
            system_id:row.bingdproject
          }
          this.$set(row, 'isSaving', true);
          this.$http.mybindingsystem(params).then(res=>{
            if(res.status==200){
              this.$message.success("绑定成功")
              row.isSaving = false;
              this.getmyprojectmanagement()
            }else{
              this.$set(row, 'newproject', "");
              row.isSaving = false;
            }
          }).catch(() => {
            this.$set(row, 'newproject', "");
            row.isSaving = false;
          })
        },
        getmykeylist(){
            this.$http.mykeymanagementlist().then(res=>{
                if(res.status==200){
                    this.tableDatakey = res.data.data
                }
            })
        },
        //查询
        onSubmit(){
          // console.log(this.mingyuandata);
          this.getmingyuanyunproject(this.mingyuandata)
        },
        //拉取项目
        getmingyuanyunproject(data){
          if (data.id) {
            this.is_table_loading = true
            this.$http.mingyuanyunproject(data).then(res=>{
                if(res.status==200){
                    this.$message.success("拉取成功")
                    this.getmyprojectmanagement()
                }
            })
          }
        },
        //每页 条
        handleSizeChange(val){
            this.params.per_page = val
            this.getmyprojectmanagement()
        },
        //当前页
        handleCurrentChange(val) {
            this.params.page = val
            this.getmyprojectmanagement()
        },
    },
}
</script>
<style lang="scss" scoped>
.btn{
  display: flex;
  justify-content: flex-end;
}
</style>