<template>
  <div>
    <!-- 拨打按钮 -->
    <el-button class="phone_btn" type="primary" icon="el-icon-phone" @click="makeTel">拨打电话</el-button>
    <el-button class="phone_btn" type="info" @click="showBlacklist">黑名单</el-button>
    <!-- 拨打记录 -->
    <el-table :data="task_tableData" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
      :row-style="$TableRowStyle">
      <!-- <el-table-column label="ID" width="70" prop="id"></el-table-column> -->
      <!-- <el-table-column label="成员id" prop="admin_id"> </el-table-column> -->
      <el-table-column label="成员名称" prop="admin_name"> </el-table-column>
      <el-table-column label="主叫" prop="caller"> </el-table-column>
      <el-table-column label="被叫" prop="callee"> </el-table-column>
      <el-table-column label="来源" prop="source"> </el-table-column>
      <el-table-column label="通话总时长(秒)" prop="total_duration">
      </el-table-column>
      <el-table-column label="被叫时长(秒)" prop="callee_duration">
      </el-table-column>
      <el-table-column label="主叫时长(秒)" prop="caller_duration">
      </el-table-column>
      <el-table-column label="接通状态" prop="call_status">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.duration == 0" type="warning">
              未接听
            </el-tag>
            <el-tag v-else type="success"> 已接听 </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="拨打时间" prop="created_at"> </el-table-column>
      <el-table-column label="操作" v-slot="{ row }">
        <el-link style="margin-left: 5px" :underline="false" @click="play(row)" v-if="row.record_url">
          <div class="audio_img">
            <img style="width: 20px; object-fit: cover" v-if="row.isPlaying"
              :src="$imageDomain + '/static/admin/outbound/play_voice.gif'" alt="" />
            <img style="width: 20px; object-fit: cover" v-else
              :src="$imageDomain + '/static/admin/outbound/voice_icon.png'" alt="" />
          </div>
        </el-link>
      </el-table-column>
    </el-table>
    <audio ref="musicMp3" id="musicMp3" :autoplay="false" controls="false" style="z-index: -1"></audio>
    <!-- 分页 -->
    <div class="block">
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev,pager,next" :total="telinfoTotal"
        @current-change="handleCurrentChange" :current-page="telinfo_params.page" :page-size="telinfo_params.per_page">
      </el-pagination>
    </div>
    <!-- 拨打电话 -->
    <el-dialog width="330px" title="智能手机" custom-class="dialog" :visible.sync="make_phone" @close="closeFn">
      <div class="maKe_phone_call">
        <template v-if="step == 1">
          <div class="maKe_phone_title">拨打电话</div>
          <el-input v-model="calledPhone" placeholder="请输入被叫号码"></el-input>
          <el-select v-model="selected_phone" clearable placeholder="请选择外显号码">
            <el-option v-for="item in outShowPhoneList" :key="item.show_id" :label="item.phone" :value="item.show_id">
            </el-option>
          </el-select>
          <div class="flex-row align-center submit_make_phone">
            <el-button class="flex-1" type="primary" @click="makePhone()">
              确认拨打</el-button>
          </div>
        </template>
        <div v-if="call_route != 2 && call_route != 4 && call_route != 8">
          <template v-if="step == 2 || step == 3">
            <div class="avatar">
              <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
            </div>
            <div class="telphone">{{ calledPhone }}</div>
            <div class="area">归属地</div>
            <div class="telphone waiting blue strong">
              {{ step == 2 ? "运营商转接等待中..." : "运营商转接成功" }}
            </div>
            <div class="link_step">
              <div class="link_step_title strong">通话步骤</div>
              <div class="link_step_con flex-row j-between">
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/waihu/zhuanjiechenggong.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">线路转接成功</div>
                </div>
                <div class="join_line">>>></div>
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img src="https://img.tfcs.cn/backup/static/admin/waihu/laidian.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">手机来电</div>
                </div>
                <div class="join_line">>>></div>
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img src="https://img.tfcs.cn/backup/static/admin/waihu/jieting.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">客户接听通话</div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div v-if="call_route == 2 || call_route == 4|| call_route == 8">
          <template>
            <div style="margin-top: 10px;">
              <p style="color: brown; ">请使用手机自带的扫一扫拨打电话，呼叫失效时间为一分钟</p>
              <p style="color: brown; ">注意： 同一个被叫一天只能拨打五次 !!!!!</p>
            </div>
            <div id="qrcode" class="qrcode" ref="qrCodeUrl">
            </div>
          </template>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="黑名单" :visible.sync="showBlackTable" width="950px">
      <div style="margin-bottom: 24px">
        <div class="bottom-border div row">
          <span class="text">手机号</span>
          <el-input style="width: 200px;" v-model="blackTable_params.callee" placeholder="请输入被叫号码" @blur="seachHouseName">
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </div>
        <myTable :table-list="blackTable_list" :header="blackTable_header" :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row :row-style="$TableRowStyle"></myTable>
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="blackTable_total" :page-size="blackTable_params.per_page" :current-page="blackTable_params.page"
          @current-change="blackPageChange">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import myTable from "@/components/components/my_table";
export default {
  components: {
    myTable,
  },
  props: {
    activeName: {
      type: String,
      default: "first",
    },
  },
  data() {
    return {
      call_route:false,
      task_tableData: [],
      telinfo_params: {
        // 当前页
        page: 1,
        // 每页多少条
        per_page: 10,
      },
      // 总条数
      telinfoTotal: 0,
      // 拨打电话属性
      step: '',
      make_phone: false,
      selected_phone: "", //选中的外显号码id
      outShowPhoneList: [], //外显号码列表
      calledPhone: "", // 被叫号码
      blackTable_list: [], // 黑名单列表
      // 黑名单列表请求参数
      blackTable_params: {
        page: 1, // 当前页
        per_page: 10, // 每页多少条
        callee: '', // 手机号搜索
      },
      blackTable_total: 0, // 黑名单列表总条数
      showBlackTable: false, // 控制黑名单模态框
      // 黑名单表头
      blackTable_header: [
        {prop: 'admin_name', label: '用户名'},
        {prop: 'caller', label: '主叫号码'},
        {prop: 'callee', label: '被叫号码'},
        {prop: 'created_at', label: '呼叫时间'},
        {prop: 'error', label: '失败原因'},
      ]
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 拨打记录-直拨记录
    getData() {
      this.$http.DirectRecord(this.telinfo_params).then((res) => {
        if (res.status == 200) {
          this.task_tableData = res.data.data;
          // this.telinfo_params.page = res.data.current_page
          // this.telinfo_params.per_page = res.data.per_page
          this.telinfoTotal = res.data.total
        }
      });
    },
    // 分页器-当前页
    handleCurrentChange(val) {
      this.telinfo_params.page = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    makeTel() {
      this.calledPhone = "";
      this.selected_phone = "";
      this.getShowTelNumber();
      this.step = 1;
      this.make_phone = true;
    },
    makePhone() {
      if (this.calledPhone == '') {
        this.$message({
          message: '手机号不能为空',
          type: 'warning'
        })
      } else if (this.calledPhone.length < 11) {
        this.$message({
          message: '请输入完整手机号',
          type: 'warning'
        })
      } else {
        this.$http.DirectTelephone({
          show_id: this.selected_phone,
          phone: this.calledPhone
        }).then(res => {
          console.log(res.data,'0000000111111');
          if (res.status == 200) {
            this.step = 2
            setTimeout(() => {
              this.step = 3
              setTimeout(() => {
                this.make_phone = false
                this.call_route = false
              }, 1000);
            }, 60000);
            this.call_route = res.data.call_route
          if ([2, 4, 8].some(item => item == res.data.call_route)) {
            var that = this
            let phoneNumber = res.data.telX
            console.log(that.$refs.qrCodeUrl,'this.$refs.qrCodeUrl');
        setTimeout(() => {
                  new QRCode(that.$refs.qrCodeUrl, {
                  text: 'tel:'+ phoneNumber, // 需要转换为二维码的内容
                  width: 200,
                  height: 200,
                  colorDark: '#000000',
                  colorLight: '#ffffff' ,
                  correctLevel: QRCode.CorrectLevel.H
              })
               }, 0)
              }
        }
      })
      
      }
    },
    closeFn(){
      this.make_phone = false
                this.call_route = false
                this.calledPhone = "";
      this.selected_phone = "";
      this.getShowTelNumber();
    },
    // 获取拨打电话下拉信息
    getShowTelNumber() {
      this.$http.getExplicitNumber().then(res => {
        if (res.status == 200) {
          this.outShowPhoneList = res.data
        }
      })
    },
    // 弹框拨打电话详情
    play(row) {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      if (this.currI !== row && this.currI) {
        audios.pause();
        this.$set(this.currI, 'isPlaying', false);
        audios.src = row.record_url
        this.$forceUpdate();
      }
      if (row.isPlaying) {
        audios.pause();
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      } else {
        if (this.currI !== row) {
          audios.src = row.record_url
        }
        audios.play();
        this.currI = row;
        this.$set(row, 'isPlaying', true);
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      }, row.duration * 1000);
    },
    // 显示黑名单模态框
    showBlacklist() {
      this.showBlackTable = true;
      if(!this.blackTable_list.length) {
        this.getDialBlackList(); // 获取黑名单列表
      }
    },
    // 获取黑名单列表
    getDialBlackList() {
      this.$http.getDialBlackList(this.blackTable_params).then((res) => {
        if(res.status == 200) {
          this.blackTable_list = res.data.data;
          this.blackTable_total = res.data.total;
        }
      })
    },
    // 黑名单列表页面改变触发
    blackPageChange(value) {
      this.blackTable_params.page = value;
      this.getDialBlackList(); // 获取黑名单列表
    },
    seachHouseName() {
      this.blackTable_params.page = 1;
      this.getDialBlackList(); // 获取黑名单列表
    }
  },
};
</script>

<style scoped lang="scss">
.qrcode {
  width: 200px;
  height: 200px;
  text-align: center;
  margin-left: 30px;
  margin-top: 10px;
}

::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;

  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }

  .el-dialog__body {
    padding: 0 12px;
  }
}

.mr10 {
  margin-right: 10px;
}

.strong {
  font-weight: 600;
}

.blue {
  color: #2d84fb;
}

.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}

.maKe_phone_call {
  height: 500px;
  text-align: center;
  background: #fff;
  padding: 10px 20px;
  margin-bottom: 10px;
  box-sizing: border-box;

  .maKe_phone_title {
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
  }

  .submit_make_phone {
    margin-top: 65px;
  }

  .avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 50%;

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .telphone {
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
    margin-top: 16px;
  }

  .area {
    color: #8a929f;
    font-size: 14px;
    margin-top: 16px;
  }

  .waiting {
    margin: 15px 0 20px;
  }

  .link_step {
    border-radius: 13px;
    background: #f1f4fa;
    padding: 10px 16px;

    .link_step_title {
      color: #2e3c4e;
      font-size: 14px;
      margin-bottom: 15px;
    }

    .link_step_con {
      .link_step_left {
        width: 48px;

        .link_step_left_bottom {
          color: #2e3c4e;
          font-size: 12px;
        }
      }

      .join_line {
        margin-top: 5px;
        color: #2d84fb;
      }
    }
  }

  .to_back {
    margin-top: 65px;

    .to_back_img {
      width: 50px;
      height: 50px;
      margin: 0 auto 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .to_back_name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }

  .el-select {
    width: 100%;
    padding-top: 20px;
  }
}

.phone_btn {
  margin-bottom: 20px;
}

#musicMp3 {
  position: absolute;
  left: -200%;
  top: -200%;
  width: 0;
  height: 0;
}

.audio_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
  background: #409eff;
}

.bottom-border {
  margin-bottom: 12px;

  .text {
    color: #2e3c4e;
    font-size: 14px;
    line-height: 40px;
    padding-right: 10px;
  }
}
</style>
