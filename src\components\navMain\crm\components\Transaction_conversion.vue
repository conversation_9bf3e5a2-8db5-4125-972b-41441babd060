<template>
    <div class="outbount">
        <div class="bg_fff div row">
            <div class="div bottom-border row flex-1" style="padding: 20px">
                <div class="div row">
                    <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
                    <el-date-picker style="width: 350px" size="small" v-model="p_time" type="datetimerange"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss" @change="changeTimeRange">
                    </el-date-picker>
                </div>
                <div>
                    <el-button type="primary" size="small" @click="onExport">导出</el-button>
                </div>
            </div>

        </div>
        <!-- 顶部模块 -->
        <div v-loading="is_table_loading">
        <div class="bg_fff box_top marbot28">
            <el-row :gutter="20">
                <el-col class="el-col-4-8">
                    <div class="top_item" :style="xiansuoStyle">
                        <div class="top_item_left flex-row flex-1">
                            <div class="top_item_left_img">
                                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
                            </div>
                            <div class="top_item_left_name">客户评级</div>
                        </div>
                        <div class="top_item_right">
                            <div class="top_item_right_num">{{ topInfo.level_total }}</div>
                        </div>
                    </div>
                </el-col>
                <el-col class="el-col-4-8">
                    <div class="top_item" :style="zuoxiStyle">
                        <div class="top_item_left flex-row flex-1">
                            <div class="top_item_left_img">
                                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
                            </div>
                            <div class="top_item_left_name">邀约</div>
                        </div>
                        <div class="top_item_right">
                            <div class="top_item_right_num">{{ topInfo.see_tel_total }}</div>
                        </div>
                    </div>
                </el-col>
                <el-col class="el-col-4-8">
                    <div class="top_item" :style="bodaStyle">
                        <div class="top_item_left flex-row flex-1">
                            <div class="top_item_left_img">
                                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
                            </div>
                            <div class="top_item_left_name">首看</div>
                        </div>
                        <div class="top_item_right">
                            <div class="top_item_right_num">{{ topInfo.first_take_total }}</div>
                        </div>
                    </div>
                </el-col>

                <el-col class="el-col-4-8">
                    <div class="top_item" :style="botongStyle">
                        <div class="top_item_left flex-row flex-1">
                            <div class="top_item_left_img">
                                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
                            </div>
                            <div class="top_item_left_name">复看</div>
                        </div>
                        <div class="top_item_right">
                            <div class="top_item_right_num">{{ topInfo.repeat_take_total }}</div>
                        </div>
                    </div>
                </el-col>
                <el-col class="el-col-4-8">
                    <div class="top_item" :style="weiboStyle">
                        <div class="top_item_left flex-row flex-1">
                            <div class="top_item_left_img">
                                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
                            </div>
                            <div class="top_item_left_name">成交</div>
                        </div>
                        <div class="top_item_right">
                            <div class="top_item_right_num">{{ topInfo.we_total }}</div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 顶部模块结束 -->
        <!-- 图表 模块 -->
        <div class="marbot28 bg_fff charts">
            <!-- 搜索 -->

            <!-- 搜索结束 -->
            <!-- 图表模块 -->
            <div class="echart flex-row align-center">
                <!-- <el-row class ="" :gutter="20"> -->
                <div class="flex-1">
                    <el-col :span="24">
                        <el-col :span="12">
                            <div class="echart_left">
                                <div class="echart_l_top">概览</div>
                                <div class="echart_l_con">
                                    <div class="chart-box sale-chart" id="chart-box" style="width: 100%; height: 390px">
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="echart_left">
                                <div class="echart_l_top">统计(人数)</div>
                                <div class="echart_l_con">
                                    <div class="chart-box pie-chart" style="width: 100%; height: 390px"></div>
                                </div>
                            </div>
                        </el-col>
                    </el-col>
                </div>
                <div class="echart-right">
                    <div class="echart_l_top">转化率</div>
                    <div class="echart_l_con flex-row flex-wrap">
                        <div class="echart_l_con_item" v-for="(item, index) in left1Data" :key="index">
                            <div class="echart_l_con_item_top">{{ item.count }}</div>
                            <div class="echart_l_con_item_bottom">{{ item.name }}</div>
                        </div>
                    </div>
                </div>
                <!-- </el-row> -->
            </div>
        </div>
    </div>
            <div class="marbot28 bg_fff charts">
            <!-- 表格模块 -->
            <div class="table_con">
                <div class="table_search">
                    <div class="search_left flex-1 flex-row align-center">
                        <!-- <div class="table_title">坐席情况统计</div> -->
                    </div>
                    <div class="table_search_btns flex-row align-center">
                        <div v-for="item in Dimension" :key="item.id" :class="item.id == paramstype ? 'label_actions' : ''"
                            @click="dimensionality(item.id)">
                            {{ item.name }}</div>
                        <!-- <div style="margin-left: 100px;">日维度</div>
              <div>周纬度</div>
              <div>月纬度</div> -->
                    </div>
                </div>
                <div class="table" style="width: 100%; height: 390px">
                </div>
            </div>
            <div class="deallist" style="padding: 24px 0">
                <listdata ref="is_tabs_page" :house_params="house_params" :house_list='house_list'
                    @getData="pageChangeGetData" @sortData="sortChangeGetData" :is_listdata_show="auth_intelligent"
                    @getDataOk="getData"></listdata>
            </div>
        </div>

        <el-dialog :visible.sync="show_select_dia" width="660px" title="选择成员">
            <memberListSingle v-if="show_select_dia" :list="memberList" :isOutbound="true" @onClickItem="selecetedMember">
            </memberListSingle>
        </el-dialog>
    </div>
</template>
  
<script>
import * as echarts from "echarts";
import myLabel from '@/components/navMain/crm/components/my_label'
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
import listdata from "./transformlist.vue"
export default {
    components: {
        myLabel,
        memberListSingle,
        listdata
    },

    data() {
        return {
            is_tabs_page: "",
            house_params: {},
            house_list: {},
            auth_intelligent: 1, // 智慧经营是否显示
            xiansuoStyle: {},
            bodaStyle: {},
            zuoxiStyle: {},
            botongStyle: {},
            weiboStyle: {},
            time_list: [
                { id: 1, name: "全部", value: "" },
                { id: 2, name: "今天", value: "today" },
                { id: 3, name: "昨天", value: "yesterday" },
                { id: 4, name: "本周", value: "this_week" },
                { id: 5, name: "上周", value: "last_week" },
                { id: 6, name: "本月", value: "this_month" },
                { id: 7, name: "上月", value: "last_month" },
            ],
            Dimension: [
                { id: 1, name: "日维度" },
                { id: 2, name: "周维度" },
                { id: 3, name: "月纬度" }
            ],
            paramstype: "1",
            p_time: '',
            zuoxi_params: {
                times: ""
                // start_time: 0,
                // end_time: 0
            },
            is_table_loading: false,
            tableData: [],
            total: 0,
            params: {
                date_type: 1, // 1全部 2今天 3昨天 4本周 5上周 6本月 7上月
                opt_type: "", // opt_type值为export表示导出数据
                b_date: "", // 开始时间
                e_date: "", // 结束时间
                page: 1,
                per_page: 10,
                // access_id: ""//数据来源
            },
            topInfo: {
                "clueCount": 3,//客户总量
                "seatsCount": 6,//团队成员
                "callCount": 22,//人均客户量
                "unCallCount": 56,//废客率
                "onCallCount": 133,//有效占比
            },
            leftDataY: ['成交', '复看', '首看', '邀约', '客户评级'],
            leftDataX: [],
            middleInfo: {},
            left1DataY: ['跟进率', '邀约率', '首看率', '复看率', '评级率', '成交率'],
            left2DataX: ['跟进', '邀约', '首看', '复看', '评级', '成交'],
            left1Data: [],
            memberList: [],
            user_name: "",
            show_select_dia: false,
            totalDate: [], // 全部echart时间
            clientChart: {}, // 获取客户数据统计底部chart

        };
    },
    created() {

        // this.getMiddleInfo()
        // this.getBottomData()
        this.xiansuoStyle = {
            backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_red.png)`
        }
        this.zuoxiStyle = {
            backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_orange.png)`
        }
        this.bodaStyle = {
            backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_blue.png)`
        }
        this.botongStyle = {
            backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_green.png)`
        }
        this.weiboStyle = {
            backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_red.png)`
        }
        this.getTopInfo()
        this.line_chart()
    },
    mounted() {

    },
    methods: {
        //概览图
        initChartOne() {
            var myChart1 = echarts.init(document.querySelector(".sale-chart"));
            var textColorList = ['#FE5858', '#91cc75', '#5470c6', '#fac858', '#FF8F86'];

            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    // boundaryGap: [0, 0],
                    splitLine: {
                        show: false,
                    }
                },
                yAxis: {
                    type: 'category',

                    data: this.leftDataY
                },
                color: textColorList,
                series: [
                    {
                        type: 'bar',
                        itemStyle: {
                            normal: {
                                color: function (params) {
                                    // 给出颜色
                                    return textColorList[params.dataIndex]
                                },
                            }
                        },
                        data: this.leftDataX
                    },
                ],
            };
            myChart1.setOption(option);
            window.addEventListener("resize", function () {
                myChart1.resize();
            });
        },
        //时长统计图
        initChartTwo() {
            var myChart = echarts.init(document.querySelector(".pie-chart"));
            var textColorList = ['#73c0de', '#fac858', "#6378FF", '#01BB00', '#FF8F86', '#FE5858'];

            var option = {
                tooltip: {
                    trigger: "item",
                    formatter: "{b} : {c}",
                },
                color: textColorList,
                padding: [10, 100, 0, 0],
                series: [
                    {
                        name: '',
                        type: 'pie',
                        left: "0",
                        top: 14,
                        bottom: 30,

                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 18,
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {   //视觉引导线
                            show: true
                        },
                        data: this.left1Data,
                    },
                ],
            };
            myChart.setOption(option);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
        //页码变化时
        pageChangeGetData(e) {
            this.params.page = e
            this.getTopInfo()
        },
        // 上下排序
        sortChangeGetData(e) {
            // console.log(e);
            this.params.order = e.order
            this.getTopInfo()
        },
        //搜索成员，姓名
        getData(e) {
            // console.log(e)
            this.params.department_id = e.department_id
            this.params.user_name = e.user_name
            this.getTopInfo()
        },

        //折线统计
        initChartThree() {
            let time = this.totalDate; // x轴时间
            // console.log(this.clientChart.segment);
            var myChart2 = echarts.init(document.querySelector(".table"));
            let chengjiao = []; // '成交数量'
            let renjun = []; // '人均数量'
            this.clientChart.segment.forEach((item) => {
                chengjiao.push(item.count);
                renjun.push(item.client ? item.client : [])
            })
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                    formatter: function (params) {
                        let unit = "人";
                        let name11 = "成交人姓名：";
                        let intevaltime = "成交周期：";
                        for (let i = 0; i < params.length; i++) {
                            let name = renjun[params[i].dataIndex].length && renjun[params[i].dataIndex][0].name ? (name11 + renjun[params[i].dataIndex][0].name) : ''
                            let inteval = renjun[params[i].dataIndex].length && renjun[params[i].dataIndex][0].inteval ? (intevaltime + renjun[params[i].dataIndex][0].inteval) : ''
                            var relVal = params[i].name;
                            relVal += "<br/>" + params[i].seriesName + '：' + params[i].data + unit
                                + "<br/>" + name + "<br/>" + inteval;
                        }
                        return relVal;
                    }
                },
                legend: {
                    data: ['成交数量']
                },
                // x轴
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: time,
                },
                yAxis: [
                    {
                        type: 'value',
                        minInterval: 1,
                        axisLabel: { formatter: '{value} 人' }, // 用于配置y坐标轴样式
                    },
                ],
                grid: {
                    left: '5%',
                    right: '3%',
                },
                series: [
                    {
                        name: '成交数量',
                        type: 'line',
                        data: chengjiao,
                        markPoint: {
                            symbol: 'pin', // 标注点的形状，可以根据需求调整
                            symbolSize: 20, // 标注点的大小，可以根据需求调整
                            data: [
                                {
                                    type: 'max', // 标注类型为最大值
                                    label: {
                                        show: true, // 显示标注文字
                                        position: 'top' // 标注文字显示在顶部
                                    }
                                }
                            ]
                        },
                        areaStyle: {}, // 设置填充区域样式
                        smooth: true, // 线条是否平滑
                    }
                ]
            };
            myChart2.setOption(option);
            window.addEventListener("resize", function () {
                myChart2.resize();
            });
        },
        //上半部分图的接口
        getTopInfo() {
            this.is_table_loading=true
            this.$http.Transaction_conversion(this.params).then(res => {
                if (res.status == 200) {
                    this.is_table_loading=false
                    this.topInfo = res.data.census
                    this.house_list = res.data
                    // console.log(res.data);
                    let data1 = []
                    data1[0] = this.topInfo.we_total
                    data1[1] = this.topInfo.repeat_take_total
                    data1[2] = this.topInfo.first_take_total
                    data1[3] = this.topInfo.see_tel_total
                    data1[4] = this.topInfo.level_total
                    this.leftDataX = [...data1]
                    this.initChartOne()
                    let dataObj = { data1: { count: this.topInfo.follow_total_ratio, duration: this.topInfo.follow_total }, data2: { count: this.topInfo.see_tel_total_ratio, duration: this.topInfo.see_tel_total }, data3: { count: this.topInfo.first_take_total_ratio, duration: this.topInfo.first_take_total }, data4: { count: this.topInfo.repeat_take_total_ratio, duration: this.topInfo.repeat_take_total }, data5: { count: this.topInfo.level_total_ratio, duration: this.topInfo.level_total }, data6: { count: this.topInfo.we_total_ratio, duration: this.topInfo.we_total } }
                    this.middleInfo = dataObj
                    let data = Array.from(Object.values(this.middleInfo), x => x)
                    //    console.log(data);
                    for (let index = 0; index < this.left2DataX.length; index++) {
                        const ele = this.left2DataX[index];
                        data[index].name = ele
                        //  console.log(ele);
                        data[index].value = data[index].duration || 0
                    }
                    //    console.log(data);
                    this.left1Data = data
                    this.initChartTwo()

                }
            })
        },
        // 获取折线图数据
        line_chart() {
            // console.log(this.params);
            let params = JSON.parse(JSON.stringify(this.params));
            if (!params.type) {
                params.type = 1
            }
            // console.log(params);
            this.$http.Transaction_conversion_chart(params).then(res => {
                if (res.status == 200) {
                    // console.log(res.data);
                    this.clientChart = res.data;
                    //   console.log(this.clientChart);
                    let data = [];
                    this.totalDate = [];
                    for (let i in this.clientChart.segment) {
                        if (this.clientChart.segment[i]) {
                            data = this.clientChart.segment;

                        }

                    }
                    data.map((item) => {
                        this.totalDate.push(item.time);
                    })
                    this.initChartThree()
                }

            })

        },
        onClickTime(e) {
            this.params.date_type = e.id
            this.getTopInfo()
            this.line_chart()
        },
        changeTimeRange(e) {
            // console.log(e);
            if (e == null) {
                this.params.date_type = 1;
                this.params.b_date = ""; // 清空开始时间
                this.params.e_date = ""; // 清空结束时间
            } else if (e.length) {
                this.params.b_date = e[0]; // 赋值开始时间
                this.params.e_date = e[1]; // 赋值结束时间
                // 删除tabs搜索参数
                if (this.params.date_type) {
                    delete this.params.date_type;
                }
            }
            this.getTopInfo()
            this.line_chart()

        },
        onExport() {
            let params = JSON.parse(JSON.stringify(this.params));
            let exportdata = 'export'
            params.opt_type = exportdata; // 增加导出参数
            this.$http.Transaction_conversion(params).then((res) => {
                if (res.status == 200) {
                    if (res.data.url) {
                        window.open(res.data.url, '_blank');
                    }
                }
            })
        },
        dimensionality(id) {
            // console.log(id);
            this.paramstype = id
            this.params.type = id
            // this.getTopInfo()
            this.line_chart()
        },
    },
};
</script>
<style lang="scss" scoped>
.outbount {
    background: #f8faff;
    padding: 28px;
    margin: -15px;
    // min-width: 1390px;
    // overflow: auto;
}

.bg_fff {
    background: #fff;
}

.bottom-border {
    justify-content: space-between;
}

.box_top {
    padding: 20px;
}

.top_item {
    // background: #3333;
    padding: 30px;
    border-radius: 10px;
    height: 130px;
    background-repeat: no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;

    .top_item_left {
        .top_item_left_img {
            width: 28px;
            height: 28px;
            overflow: hidden;
            border-radius: 50%;
            align-self: flex-start;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .top_item_left_name {
            margin-left: 10px;
            font-size: 18px;
            color: #fff;
            white-space: nowrap;
        }
    }

    .top_item_right {
        display: flex;
        justify-content: flex-end;
        color: #fff;
        font-size: 48px;
        font-weight: 600;
        // margin-top: 52px;    
    }
}

.echart {
    padding: 20px 10px;
}

.echart-right {
    min-width: 340px;
    width: 340px;

    .echart_l_con {
        padding: 10px;
        flex-wrap: wrap;
        box-sizing: border-box;

        .echart_l_con_item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 155px;
            padding: 14px 0;
            margin-right: 10px;
            border-radius: 10px;
            background: #f1f4fa;
            height: 90px;
            margin-bottom: 10px;

            &.echart_l_con_item_one {
                width: 320px;
                margin-right: 0;
                margin-bottom: 0;
            }

            &:nth-child(2n) {
                margin-right: 0;
            }

            .echart_l_con_item_top {
                margin-bottom: 10px;
                font-weight: 600;
                color: #2d84fb;
                font-size: 20px;
            }

            .echart_l_con_item_bottom {
                color: #2e3c4e;
                font-size: 16px;
            }
        }
    }
}

.deallist {
    width: 97%;
    height: 900px;
    margin: 0 auto;
    // background-color: palegoldenrod;
}

.mr10 {
    margin-right: 10px;
}

.table_title {
    font-weight: 600;
}

.table_search_inp {
    margin-left: 20px;
}

.small_inp {
    width: 100px;
}

.table_con {
    .table_search {
        margin-bottom: 10px;

        .table_search_btns {
            margin-left: 20px;

            div {
                color: #9199a4;
                font-size: 16px;
                margin-right: 10px;
                cursor: pointer;
            }

            .label_actions {
                border-radius: 4px;
                background: #e8f1ff;
                color: #2d84fb;
            }
        }
    }
}

.el-col-4-8 {
    width: 20%;
}

.outbount_top_item {
    .task_name {
        color: #2e3c4e;
        font-size: 14px;
    }

    .select_name {
        width: 200px;
    }
}
</style>
  