<template>
  <div>
    <el-button
      style="margin-bottom:20px"
      type="primary"
      size="mini"
      class="el-icon-plus"
      @click="createLabel"
      >添加标签</el-button
    >
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      highlight-current-row
    >
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column prop="order" label="排序"></el-table-column>
      <el-table-column prop="taggroup.name" label="标签组"></el-table-column>
      <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <el-link type="primary" @click="onChangeEdit(scope.row)">编辑</el-link
          ><el-link
            type="danger"
            style="margin-left:20px"
            @click="onDelete(scope.row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_labels" label-position="left" label-width="100px">
        <el-form-item label="标签名称：">
          <el-input
            style="width:300px"
            v-model="form_labels.name"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签排序：">
          <el-input
            style="width:300px"
            v-model="form_labels.order"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签组：">
          <el-select
            style="width:300px"
            v-el-select-loadmore="getLoadmore"
            v-model="form_labels.parentid"
            placeholder="请选择"
          >
            <el-option
              v-for="item in group"
              :key="item.label_group_id"
              :label="item.name"
              :value="item.label_group_id + ''"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            v-loading="is_button_loading"
            :disabled="is_button_loading"
            type="primary"
            @click="submitData"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_labels: {},
      group_params: {
        page: 1,
      },
      load_group_more: true,
      group: [],
      is_button_loading: false,
    };
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 获取element-ui定义好的scroll盒子
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          /*   scrollHeight 获取元素内容高度(只读)
                     scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
                    clientHeight 读取元素的可见高度(只读)
                      */
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          // 判断滚动到底部  (condition:如果元素滚动到底, 下面等式返回true, 没有则返回false)
          if (condition) {
            // binding.value 为自定义指令绑定的值，因为绑定的是个方法，所以这里是方法调用，触发指令后执行加载数据
            binding.value();
          }
        });
      },
    },
  },
  mounted() {
    this.getDataList();
    this.getLabelsGroupData();
  },
  methods: {
    getLoadmore() {
      if (!this.load_group_more) {
        return;
      }
      this.group_params.page++;
      this.getLabelsGroupData();
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getLabelsList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    getLabelsGroupData() {
      this.is_table_loading = true;
      this.$http
        .getLabelsGroupData({ params: this.group_params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            if (res.data.data.length === 0) {
              this.load_group_more = false;
            }
            this.group = this.group.concat(res.data.data);
          }
        });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    createLabel() {
      this.form_labels = {
        order: 1,
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    onChangeEdit(row) {
      this.form_labels = {
        id: row.id,
        name: row.name,
        parentid: row.parentid,
        order: row.order,
      };
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    submitData() {
      if (!this.form_labels.name) {
        this.$message.error("请输入标题");
        return;
      }
      this.is_button_loading = true;
      if (this.dialogTitle === "addData") {
        this.$http.setLabelsList(this.form_labels).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.editLabelsList(this.form_labels).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    onDelete(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http.deleteLabels({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
  },
};
</script>

<style></style>
