<template>
		<div class="tab-content-body white topped">
			<div class="body-inner main-scroll" ref="scrollElem">
                <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                <el-tab-pane label="秘钥管理" name="keymanagement"></el-tab-pane>
                <el-tab-pane label="同步记录" name="synchronizeRecords"></el-tab-pane>
                <!-- <el-tab-pane label="变更记录" name="third"></el-tab-pane> -->
                <el-tab-pane label="项目管理" name="fourth"></el-tab-pane>
            </el-tabs>
            <keep-alive>
					<component :is="activeName" @room-change="handleRoomChange"
                    v-if="activeName=='keymanagement'"
					ref="list"></component>
			</keep-alive> 
 
			</div>
		</div>
</template>
<script>
import keymanagement from "./keymanagement.vue"
export default {
    components:{
        keymanagement,
    },
    data() {
        return {
            activeName: 'keymanagement'
        }
    },
    methods:{
        handleClick(tab, event) {
            if(this.activeName !="keymanagement"){
                if(this.activeName =="synchronizeRecords"){
                    this.$router.push("alldirectories?id=1");
                }else if (this.activeName =="fourth"){
                    this.$router.push("alldirectories?id=2");
                }
                
            }
      },
      handleRoomChange(){

      },
    },
}
</script>
<style lang="scss" scoped>
.tab-content-body{
	padding: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	padding-bottom: 48px;
}
.body-inner{
	flex: 1;
	overflow: auto;
	padding: 0 28px 28px;
}

	
.header-tabs{
	padding: 16px 0 0;
	::v-deep.el-tabs__item{
		font-size: 15px !important;
	}
}
</style>