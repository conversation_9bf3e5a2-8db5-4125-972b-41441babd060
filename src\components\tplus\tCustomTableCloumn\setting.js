import http from "@/utils/http";
const commonConfig = {
    middleColumnMinCount: 4,    //中间列最小数量（至少要选择数量）
},

tableOptions = {
    //crm我的客户表格
    crm_customer_my_list: {     
        //middleColumnMinCount: 4,          //优先级高于config.middleColumnMinCount，为0时表示不限制
        get: http.watchhead,                //获取配置api
        set: http.setCustomTableColumn      //设置配置api
    }
};



export default {
    //配置项
    config(tableName){
        const options = tableOptions[tableName], config = {...commonConfig};
        for(const key in config){
            if(options[key] !== undefined){
                config[key] = options[key];
            }
        }
        return config;
    },
    //获取设置数据
    async getSettingData(tableName, cb){
        const res = await tableOptions[tableName].get.call(http);
        if(res.status == 200){
            //所有列
            let allColumns = res.data.table_list || [];
            //设置fixed="left"列
            for(const col of allColumns){
                if(col.fixed){
                    col.fixed = 'left'
                }else{
                    break;
                }
            }
            //设置fixed="right"列
            const lastIndex = allColumns.length-1;
            for(const [index, col] of [...allColumns].reverse().entries()){
                if(col.fixed == 1){
                    allColumns[lastIndex-index].fixed = 'right'
                }else{
                    break;
                }
            }

            //已选中列ID
            let seledColumnIds = [];
            //已选中项
            if(res.data.my_set){
                seledColumnIds = res.data.my_set.split(',').map(e=>e*1);
                //如果必选项不在已选项中，将其加入
                allColumns.forEach(e => {
                    if(e.must && !seledColumnIds.includes(e.id)){
                        seledColumnIds.push(e.id)
                    }
                })
            }else{
                //为空时选中全部
                seledColumnIds = allColumns.map(e => e.id)
            }

            const seledColumns = seledColumnIds.map( id => allColumns.find(e => e.id == id)).filter(e=>e)
            cb({ allColumns, seledColumnIds, seledColumns });
        }
    },
    async setSettingData(tableName, data, cb){
        const res = await tableOptions[tableName].set.call(http, data);
        if(res.status == 200){
            cb(res.data)
        }
    }
}