<template>
  <el-container>
    <el-header>
      <new_tips_list :tipsList="tips_list"></new_tips_list>
    </el-header>
    <el-main>
      <el-form
        label-width="100px"
        :model="form"
        style="width: 500px"
        :rules="rules"
        ref="formRules"
      >
        <!-- <el-form-item label="公司分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择公司类型">
            <el-option
              v-for="item in category_list"
              :key="item.id"
              :label="item.description"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="机构名称" prop="name">
          <el-input
            placeholder="请输入机构名称"
            v-model="form.name"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="类型"
          prop="store_category"
          v-if="form.category === 2"
        >
          <el-radio
            :disabled="isStore"
            v-model="form.store_category"
            :border="true"
            size="small"
            v-for="item in store_category_list"
            :key="item.id"
            :label="item.value"
            @change="changeCategory"
            >{{ item.description }}</el-radio
          >
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio
            v-model="form.status"
            v-for="item in store_status_list"
            :border="true"
            size="small"
            :key="item.id"
            :label="item.value"
            >{{ item.description }}</el-radio
          >
        </el-form-item>
        <el-form-item
          label="选择公司"
          prop="pid"
          v-if="form.store_category == 0 && form.category === 2"
        >
          <el-select
            :disabled="isStore"
            value-key="pid"
            filterable
            v-model="form.pid"
            placeholder="请选择总公司"
          >
            <el-option
              v-for="item in all_sale_company"
              :key="item.pid"
              :label="item.name"
              :value="item.pid"
            ></el-option
          ></el-select>
        </el-form-item>
        <el-form-item
          :label="'选择' + label_name_category"
          prop="store_manager_user_id"
          v-if="form.category === 2"
        >
          <el-select
            filterable
            remote
            multiple
            reserve-keyword
            v-model="store_manager_user_id_list"
            placeholder="请输入手机号"
            :loading="broker_loading"
            :remote-method="getbrokerData"
          >
            <el-option
              v-for="item in store_manager_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作等级" prop="cooperation_level">
          <el-select v-model="form.cooperation_level" placeholder="请选择等级">
            <el-option
              v-for="item in cooperation_level_list"
              :key="item.id"
              :label="item.description"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择城市" prop="region_0">
          <div class="div row" style="width: 300px">
            <el-select
              @change="regionOne"
              v-model="form.region_0"
              placeholder="请选择城市"
              class="place-select place-select-city"
            >
              <el-option
                v-for="item in region_list_one"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-select
              v-if="region_list_two.length > 0"
              v-model="form.region_1"
              placeholder="请选择区域"
              class="place-select place-select-address"
            >
              <el-option
                v-for="item in region_list_two"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item :label="label_name + '地址'" prop="address">
          <el-input
            placeholder="请输入公司地址"
            v-model="form.address"
          ></el-input>
        </el-form-item>

        <!-- <el-form-item label="确认密码" v-if="form.password">
          <el-input
            v-model="passwordRe"
            placeholder="请输入确认密码"
            type="password"
          ></el-input>
        </el-form-item> -->
        <el-form-item
          label="员工提现"
          prop="allow_withdraw"
          v-if="form.category === 2"
        >
          <el-switch
            v-model="form.allow_withdraw"
            active-color="#13ce66"
            inactive-color="#999"
          >
          </el-switch>
        </el-form-item>
        <el-form-item :label="label_name + 'logo'">
          <el-upload
            :headers="myHeader"
            :action="company_upload_type"
            :on-success="handleSuccessLogo"
            :show-file-list="false"
            list-type="picture-card"
            :on-preview="handlePictureCardPreviewLogo"
            :on-remove="handleRemoveLogo"
            ><img v-if="form.logo" :src="form.logo" class="avatar" />

            <i v-else class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="logoVisible">
            <img width="100%" :src="logoImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item label="营业执照">
          <el-upload
            :headers="myHeader"
            :action="company_upload_type"
            :on-success="handleSuccessLicense"
            list-type="picture-card"
            :show-file-list="false"
            :on-preview="handlePictureCardPreviewLicense"
            :on-remove="handleRemoveLicense"
          >
            <img
              v-if="form.business_license_img"
              :src="form.business_license_img"
              class="avatar"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="LicenseVisible">
            <img width="100%" :src="LicenseImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item :label="label_name + '主图'">
          <el-upload
            :headers="myHeader"
            :action="company_upload_type"
            :on-success="handleSuccessCompany"
            :show-file-list="false"
            list-type="picture-card"
            :on-preview="handlePictureCardPreviewCompany"
            :on-remove="handleRemoveCompany"
          >
            <img
              v-if="form.company_img"
              :src="form.company_img"
              class="avatar"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="CompanyVisible">
            <img width="100%" :src="CompanyImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <div class="btn-box">
          <el-form-item size="large">
            <el-button type="primary" v-loading="loading" @click="onSubmit"
              >保存{{ label_name }}信息</el-button
            >
            <el-button @click="goBack">返回列表</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
/* eslint-disable */
import config from "@/utils/config";
import new_tips_list from "@/components/components/new_tips_list";
export default {
  components: { new_tips_list },
  name: "upload_company",
  data() {
    return {
      form: {
        category: "",
        cooperation_level: "",
        store_manager_user_id: "",
        region_0: 0,
        region_1: 0,
        // province_id: "",
        // city_id: "",
        // district_id: "",
        address: "",
        name: "",
        logo: "",
        business_license_img: "",
        company_img: "",
        allow_withdraw: false,
        store_category: "0",
        pid: "",
        status: 1
      },
      // passwordRe: '',
      // 店长id
      store_manager_user_id_list: [],
      cooperation_level_list: [],
      store_manager_list: [],
      region_list: [],
      region_list_one: [],
      region_list_two: [],
      category_list: [],
      // 上传的内容
      // logo
      logoImageUrl: "",
      logoVisible: false,
      // 营业执照 图
      LicenseVisible: false,
      LicenseImageUrl: "",
      // 公司主图
      CompanyVisible: false,
      CompanyImageUrl: "",
      city_list: [],
      broker_loading: false,
      broker_options: [],
      rules: {
        category: [{ required: true, trigger: "blur", message: "请选择分类" }],
        region_0: [{ required: true, trigger: "blur", message: "请选择地址" }],
        address: [{ required: true, trigger: "blur", message: "请输入地址" }],
        name: [{ required: true, trigger: "blur", message: "请输入名称" }],
        cooperation_level: [
          { required: true, trigger: "blur", message: "请选择等级" },
        ],
        store_manager_user_id: [
          { required: true, trigger: "blur", message: "请选择店长" },
        ],
      },
      loading: false,
      company_category_name: "",
      tips_list: [
        "请正确填写输入框信息",
        // "公司/门店结佣：设置公司是否统一给员工结佣",
      ],
      company_upload_type: `/api/common/file/upload/admin?category=${config.COMPANY_IMG}`,
      // 门店/公司
      store_category_list: this.$getDictionary("COMPANY_STORE_CATEGORY "),
      store_status_list: [
        {
          id: 2,
          value: 1,
          description: '正常'
        },
        {
          id: 1,
          value: 0,
          description: '禁用'
        },

      ],
      // 判断是否门店
      isStore: false,
      label_name: "公司",
      // 获取总公司列表
      all_sale_company: [],
      label_name_category: "店长",
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
  },
  mounted() {
    // this.getCity();
    this.cooperation_level_list = this.$getDictionary(
      "COMPANY_COOPERATION_LEVEL"
    );
    this.form.category = parseInt(this.$route.query.company_category);
    if (this.$route.query.store) {
      this.isStore = true;
      this.form.pid = parseInt(this.$route.query.store);
    }
    // if (this.form.category === 1) {
    //   this.company_category_name = "项目公司";
    // } else if (this.form.category === 2) {
    //   this.company_category_name = "销售公司";
    // }
    // this.getCompany();
    this.getRegion();
    this.getSaleCompanyList();
  },
  methods: {
    getRegion() {
      this.$http.getRegionList().then((res) => {
        if (res.status === 200) {
          this.region_list = this.$sortPro(res.data, ["pid"]);
          this.region_list.map((item) => {
            if (item.pid === 0) {
              this.region_list_one = [
                { id: 0, name: "请选择" },
                ...item.children,
              ];
            }
          });
        }
      });
    },
    // getCompany() {
    //   this.$http.dictionaryFind("COMPANY_CATEGORY").then((res) => {
    //     this.category_list = res.data.data;
    //   });
    // },
    // 上传文件
    // 上传logo
    handleRemoveLogo() { },
    handlePictureCardPreviewLogo(file) {
      this.logoImageUrl = file.response.url;
      this.logoVisible = true;
    },
    handleSuccessLogo(response) {
      this.form.logo = response.url;
    },
    // 上传执照
    handleRemoveLicense() { },
    handlePictureCardPreviewLicense(file) {
      this.LicenseImageUrl = file.response.url;
      this.LicenseVisible = true;
    },
    handleSuccessLicense(response) {
      this.form.business_license_img = response.url;
    },
    // 公司主图
    handleRemoveCompany() { },
    handlePictureCardPreviewCompany(file) {
      this.CompanyImageUrl = file.response.url;
      this.CompanyVisible = true;
    },
    handleSuccessCompany(response) {
      this.form.company_img = response.url;
    },
    onSubmit() {
      if (this.form.store_category == 1) {
        this.form.pid = 0;
      }
      this.form.store_manager_user_id = this.store_manager_user_id_list.toString();
      if (this.$route.query.store) {
        this.form.pid = this.$route.query.store;
      }
      this.loading = "true";
      this.$refs.formRules.validate((valid) => {
        if (valid) {
          for (var prop in this.form) {
            if (this.form[prop] === "") {
              delete this.form[prop];
            }
          }
          // if (this.form.password && this.form.password !== this.passwordRe) {
          //   this.$message.warning("密码不一致 请重新输入")
          // }
          this.$confirm("注意：门店/公司创建后类型不可修改！", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.$http
              .createCompany(this.form)
              .then((res) => {
                this.loading = false;
                if (res.status === 200) {
                  this.$message({
                    message: "提交成功",
                    type: "success",
                  });
                  this.goBack();
                }
              })
              .catch(() => {
                this.loading = false;
              });
          });
        } else {
          this.loading = false;
        }
      });
    },
    // 返回公司列表
    goBack() {
      if (this.$route.query.store) {
        this.$goPath(`/company_store?company_id=${this.$route.query.store}`);
      } else if (this.form.category == 1) {
        this.$goPath("/project_company_list");
      } else if (this.form.category == 2) {
        this.$goPath("/sale_company_list");
      }
    },
    regionOne(e) {
      this.form.region_1 = "";
      this.region_list_two = [];
      this.region_list.map((item) => {
        if (item.pid === 0) {
          this.region_list_one = item.children;
        } else {
          if (item.pid === e) {
            if (item.children) {
              this.region_list_two = item.children;
            }
          }
        }
      });
    },
    getbrokerData(query) {
      // 项目助理不能为店长
      this.broker_loading = true;
      this.$http.searchUserList("100", query, 1).then((res) => {
        this.broker_loading = false;
        this.store_manager_list = res.data.data.map((item) => {
          return {
            id: item.id,
            name: item.name || item.nickname || item.user_name || item.phone,
          };
        });
      });
    },
    getSaleCompanyList() {
      this.$http.getSaleCompanyList(100).then((res) => {
        if (res.status === 200) {
          this.all_sale_company = res.data.data.map((item) => {
            return {
              pid: item.id,
              name: item.name,
            };
          });
        }
      });
    },
    // 根据选择类型显示门店/公司
    changeCategory(e) {
      if (e == 0) {
        this.label_name = "门店";
        this.label_name_category = "店长";
        this.form.pid = "";
      } else if (e == 1) {
        this.label_name_category = "经理";
        this.label_name = "公司";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  color: #333;
  font-weight: bold;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
  }
  i {
    color: red;
  }
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-header {
// 	height: 80px !important;
// 	padding-top: 30px;
// }
.el-main {
  margin-top: 40px;
  .title {
    margin: 10px 0;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
}
.el-select {
  // margin-right: 20px;
  width: 300px;
}
.el-input {
  width: 300px;
}
.avatar {
  width: 148px;
  height: 148px;
}
.place-select {
  width: 140px;
}
.place-select-city {
  flex: 1;
}
.place-select-address {
  margin-left: 20px;
}
</style>
