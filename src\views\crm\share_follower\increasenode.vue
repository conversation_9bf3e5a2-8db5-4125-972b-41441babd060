<template>
    <el-dialog :visible.sync="show" width="75%" :modal="false" title="添加节点">
        <div class="addnode">
            <nodecomponents ref="nodecomponents" :row="row"  @custom-event="cancle"></nodecomponents>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import nodecomponents from '@/views/crm/share_follower/add_node_components.vue'
export default {
    components:{
        nodecomponents,
    },
    data(){
        return {
            show: false,        //dialog是否显示
            cancleFn: null,     //取消回调
            successFn: null,    //成功回调
            row: {} , //表单参数
        }
    },
    methods: {
        //打开弹层
        open(row){
            this.row = row;
            this.show = true;
            return this;
        },
        //取消
        cancle(){
            this.show = false;
        },
        //确定
        confirm(){
            //TODO: 验证+提交
            //在提交成功之后回调
            this.$refs.nodecomponents.submission()
            // this.show = false;
        }
    }
}
</script>
<style lang="scss" scoped>
   .addnode{
    height: 500px;
   } 
</style>