<template>
    <div class="material-box">
        <div class="material_frame">
            <div class="header flex-row">
                <el-form label-width="80px" :inline="true" :model="material" class="demo-form-inline">
                    <el-form-item label="搜索素材">
                        <el-input 
                            v-model="material.name" 
                            placeholder="请输入关键词" 
                            prefix-icon="el-icon-search" 
                            clearable
                            @blur="blurSearchName">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="时间筛选">
                        <el-date-picker 
                            style="width: 240px;"
                            v-model="SearchTime" 
                            type="daterange" 
                            range-separator="至" 
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="changeSearchTime">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
                <div class="header-menu">
                    <div class="flex-row">
                        <!-- 全景图片列表 -->
                        <!-- <div class="newly-category" @click="NewlyCategory">
                            <el-button icon="el-icon-newfontwenjianku" type="primary">全景图片列表</el-button>
                        </div> -->
                        <!-- 上传素材 -->
                        <div class="create-material">
                            <el-button icon="el-icon-plus" type="warning" @click="createMaterial">
                                上传素材
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="links-menu"></div>
            <!-- 图片素材 -->
            <div class="material_list">
                <pictureMaterial 
                    v-if="material.media_type == 0 || material.media_type == 2 || material.media_type == 3"
                    :forData_list="forData_list" 
                    :media_type="material.media_type"
                    @materialList="materialList">
                </pictureMaterial>
                <el-pagination
                    class="myPagination"
                    background
                    layout="total,prev, pager, next"
                    :total="data_count"
                    :page-size="material.rows"
                    :current-page="material.page"
                    @current-change="onPageChange"
                >
                </el-pagination>
            </div>
        </div>
        <el-dialog
            title="上传素材"
            :visible.sync="dialogUploadCopyright"
            width="680px"
            :close-on-click-modal="false"
            @close="closeUpload"
        >
            <!-- 版权提醒 -->
            <!-- <div class="upload-box" v-show="!is_upload">
                <div class="upload-title">为能最大限度保护版权利益，建议为本次上传的素材打上系统水印。</div>
                <div class="watermarking">查看水印效果</div>
            </div> -->
            <!-- 上传文件 -->
            <div>
                <el-form ref="formData" class="form_box" :model="uploadMaterial_params" label-width="120px">
                    <el-form-item label="素材名称：" prop="media_name">
                        <el-input style="width: 240px;" v-model="uploadMaterial_params.media_name" placeholder="请输入"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="上传视频：" v-if="uploadMaterial_params.media_type == 2">
                        <el-upload
                            ref="upload"
                            :limit="1"
                            class="upload-demo"
                            :data="{ type: upload_params.video_type }"
                            :headers="myHeader"
                            :action="video_upurl"
                            :on-success="UploadDataSuccess"
                            accept=".mp4"
                            :before-upload="UploadVideoBefore"
                        >
                        <el-button size="small"
                            >上传</el-button
                        >
                        <div slot="tip" class="el-upload__tip">
                            <span> 只能上传视频文件，且不超过10M </span>
                        </div>
                        </el-upload>
                    </el-form-item> -->
                    <!-- <el-form-item label="上传贴片视频：" v-if="uploadMaterial_params.media_type == 3">
                        <el-upload
                            ref="upload"
                            :limit="1"
                            class="upload-demo"
                            :data="{ type: upload_params.video_type }"
                            :headers="myHeader"
                            :action="video_upurl"
                            :on-success="UploadDataSuccess"
                            accept=".mp4"
                            :before-upload="UploadVideoBefore"
                        >
                        <el-button size="small"
                            >上传</el-button
                        >
                        <div slot="tip" class="el-upload__tip">
                            <span> 只能上传视频文件，且不超过10M </span>
                        </div>
                        </el-upload>
                    </el-form-item> -->
                    <!-- <el-form-item label="上传音频：" v-if="uploadMaterial_params.media_type == 1">
                        <el-upload
                            ref="upload"
                            class="uploader-audio"
                            :action="audio_upurl"
                            :headers="myHeader"
                            :on-success="UploadDataSuccess"
                            :limit="1"
                            accept=".mp3"
                        >
                        <el-button size="small"
                            >音频上传</el-button
                        >
                        </el-upload>
                    </el-form-item> -->
                    <el-form-item label="上传图片：" class="Photo-details" v-if="uploadMaterial_params.media_type == 0">
                        <div class="photo-item" v-for="(item, index) in imgList" :key="index">
                            <el-upload
                                :disabled="item.url != '' && item.url != undefined"
                                class="uploader-create"
                                list-type="picture-card" 
                                :headers="myHeader"
                                :action="picture_upurl"
                                :on-success="e => UploadParamsSuccess(e, index)"
                                accept=".jpg,.png"
                                :show-file-list="false" 
                                :multiple="true"
                            >
                                <img v-if="item.imgsrc" :src="item.imgsrc" class="photo-item-img">
                                <i v-else class="el-icon-plus photo-item-uploader-icon"></i>
                                <span class="uploader-actions" v-if="item.url" >
                                    <span class="uploader-actions-item" @click="handlePictureCardPreview($event, index)">
                                        <i class="el-icon-view"></i>
                                    </span>
                                    <span class="uploader-actions-item" @click="e => handleRemove(e, index)">
                                        <i class="el-icon-delete"></i>
                                    </span>
                                </span>
                            </el-upload>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <!-- <div class="flex-row upload-confirm" v-show="!is_upload">
                    <el-button type="primary" @click="onWatermark">上传并打水印</el-button>
                    <div class="notWatermark" @click="notWatermark">不打水印上传</div>
                </div> -->
                <div class="flex-row upload-footer">
                    <el-button @click="dialogUploadCopyright = false">取 消</el-button>
                    <el-button type="primary" @click="uploadMaterial">确 定</el-button>
                </div>
            </span>
        </el-dialog>
        
        <el-dialog :visible.sync="dialogUploadPicture" class="dialog-Photo">
            <img width="100%" :src="dialogPhoto" alt="">
        </el-dialog>
    </div>
</template>
<script>
import config from "@/utils/config.js";
import pictureMaterial from "@/components/navMain/webVr/components/picture_material.vue"
    export default {
        components: {
            pictureMaterial,
        },
        data() {
            return {
                // 搜索作品接口传参
                material: {
                    name: "", // 素材名称
                    media_type: 0, // 素材类型
                    time_s: "", // 筛选开始时间
                    time_e: "", // 筛选结束时间
                    rows: 20, // 每页条数
                    page: 1 // 当前页
                },
                imgList: [
                    {
                        path: "",
                        desc: ""
                    }
                ],
                dialogUploadCopyright: false, // 上传素材提示版权模态框
                dialogUploadPicture: false, // 查看已上传的图片模态框
                dialogPhoto: "", // 查看已上传的图片
                forData_list: [], // 素材数据
                data_count: 0, // 素材列表总条数
                SearchTime: "", // 存储筛选时间
                is_upload: true, // 控制上传素材模态框显示
                // is_Title: "上传素材", // 上传素材title
                video_upurl: "/api/common/file/upload/admin?category=402", // 上传视频素材的action
                picture_upurl: "/api/common/file/upload/admin?category=401", // 上传图片素材的action
                audio_upurl: "/api/common/file/upload/admin?category=403", // 上传音频素材的action
                dialogImageUrl: "https://www.bilibili.com/video/BV1qX4y197VK?spm_id_from=333.1007.tianma.1-1-1.click",
                // 上传文件接口数据
                upload_params: {
                    video_type: '',
                },
                // 确定上传素材接口参数
                uploadMaterial_params: {
                    media_type: 0, // 0:图片素材，1：音频素材，2：视频素材，3：贴片视频素材
                    media_path: "", // 素材url
                    media_name: "", //素材名称
                    media_suffix: "", // 素材后缀
                    media_size: "", // 素材大小
                }
                
            }
        },
        mounted() {
            this.getDataList(); // 获取素材列表
        },
        computed: {
            // 获取请求头
            myHeader() {
                return {
                    // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
                    Authorization: config.TOKEN,
                };
            },
        },
        methods: {
            // 搜索作品失去焦点时触发
            blurSearchName() {
                this.material.page = 1;
                this.getDataList();
            },
            // 上传素材版权提示
            createMaterial() {
                this.dialogUploadCopyright = true;
                this.$nextTick(() => {
                    this.$refs['formData'].resetFields()
                })
            },
            // 获取素材列表
            getDataList() {
                this.$http.getMaterialList(this.material).then((res) => {
                    if(res.status == 200) {
                        this.forData_list = res.data.data;
                        this.data_count = res.data.total;
                    }
                })
            },
            // 页码发生改变时触发
            onPageChange(val) {
                this.material.page = val;
                this.getDataList();
            },
            // 上传素材类型发生改变时触发
            // changeUploadType(val) {
            //     if(val == 2) {
            //         this.upload_params.video_type = 'video'
            //     } else if(val == 3) {
            //         this.upload_params.video_type = 'videotie'
            //     }
            //     // 清除图片
            //     this.imgList = [
            //         {
            //             path: "",
            //             desc: ""
            //         }
            //     ];
            //     this.uploadMaterial_params.media_name = "";
            //     this.uploadMaterial_params.media_path = "";
            //     this.uploadMaterial_params.media_suffix = "";
            //     this.uploadMaterial_params.media_size = "";
            //     this.$nextTick(() => {
            //         // 清除音频素材已上传文件
            //         if(this.$refs.upload != undefined) {
            //             this.$refs.upload.clearFiles();
            //         }
            //     })
            // },
            // 当筛选时间发生变化
            changeSearchTime(val) {
                if (val != '' && val != undefined) {
                    this.material.time_s = val[0];
                    this.material.time_e = val[1];
                } else {
                    this.material.time_s = "";
                    this.material.time_e = "";
                }
                this.material.page == 1;
                this.getDataList();
            },
            // 打水印上传
            // onWatermark() {
            //     // 显示上传素材
            //     this.is_upload = true;
            //     // 将模态框title赋值
            //     this.is_Title = "上传素材"
            // },
            // 不打水印上传
            // notWatermark() {},
            // 关闭上传模态框时触发
            closeUpload() {
                // 清除图片
                this.imgList = [
                    {
                        path: "",
                        desc: ""
                    }
                ];
                this.uploadMaterial_params.media_path = "";
                this.uploadMaterial_params.media_suffix = "";
                this.uploadMaterial_params.media_size = "";
                // 显示素材版权提示
                // this.is_upload = false;
                // 将模态框title赋值
                // this.is_Title = "版权保护提醒"
            },
            // 上传视频素材成功
            UploadDataSuccess(response) {
                console.log(response,"上传成功");
                // 测试
                this.uploadMaterial_params.media_path = response.url;
                this.uploadMaterial_params.media_suffix = response.media_suffix;
                this.uploadMaterial_params.media_size = response.media_size;
            },
            // 上传视频素材之前
            UploadVideoBefore(file) {
                let imgSize = Number(file.size / 1024 / 1024);
                if(imgSize > 10) {
                    this.$message.error("图片或视频文件不能大于10M，请重新上传！");
                    return false
                }
            },
            // 图片上传成功
            UploadParamsSuccess(e, index) {
                console.log(e,"上传成功");
                e.imgsrc = "http://tfyvr-test.oss-cn-hangzhou.aliyuncs.com/"+e.url
                // e.imgname = this.imgList[index].imgname
                this.imgList.unshift(e)
                // 清空 图片详情最开始的图片描述
                if(this.imgList[index].imgname) {
                    let end = this.imgList.length-1;
                    this.imgList[end].imgname = "";
                }
                this.uploadMaterial_params.media_path = e.url;
                this.uploadMaterial_params.media_suffix = e.media_suffix;
                this.uploadMaterial_params.media_size = e.media_size;
                this.imgList.pop(); // 删除上传文件选择框
            },
            // 查看已上传的图片
            handlePictureCardPreview(e, index) {
                this.dialogUploadPicture = true;
                this.dialogPhoto = this.imgList[index].imgsrc;
            },
            // 删除已上传的全景图片
            handleRemove(e) {
                e.stopPropagation();
                e.preventDefault();
                this.imgList.shift();
                this.imgList.push(
                    {
                        path: "",
                        desc: ""
                    }
                );
                this.uploadMaterial_params.media_path = '';
                this.uploadMaterial_params.media_suffix = '';
                this.uploadMaterial_params.media_size = '';
            },
            // 确定上传素材
            uploadMaterial() {
                if(this.uploadMaterial_params.media_name == "" || this.uploadMaterial_params.media_name == undefined) {
                    return this.$message.error('素材名称不能为空');
                }
                this.$http.confirmUploadMaterial(this.uploadMaterial_params).then((res) => {
                    if(res.status == 200) {
                        this.dialogUploadCopyright = false
                        this.$message({
                            message: '上传成功',
                            type: 'success'
                        });
                        this.getDataList();
                    }
                })
            },
            // 图片素材重命名回调函数
            materialList() {
                this.getDataList();
            },
            // 获取全景图片列表
            // getPanoramaList() {
            //     this.$http.getPanoramaList().then((res) => {
            //         if(res.status == 200) {
            //             console.log(res,"全景参数");
            //         }
            //     })
            // }
        }
    }
</script>
<style lang="scss" scoped>
.material-box {
    padding: 24px;
    margin: -15px;
    background: #F1F4FA;
    .material_frame {
        background: #FFFFFF;
        border-radius: 4px;
        ::v-deep .header {
            display: flex;
            justify-content: space-between;
            position: relative;
            padding: 24px;
            box-sizing: border-box;
            align-items: initial;
            .el-form {
                .el-form-item {
                    margin-right: 30px;
                    margin-bottom: 0px;
                    .el-form-item__label {
                        text-align: center;
                    }
                    .el-form-item__content {
                        .el-input {
                            .el-input__inner {
                                width: 240px;
                                height: 40px;
                            }
                        }
                    }
                }
                .el-form-item:first-child {
                    .el-form-item__content {
                        .el-input {
                            .el-input__inner {
                                width: 240px;
                                height: 40px;
                                padding-left: 36px;
                            }
                            .el-input__prefix {
                                left: 4px;
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
            .header-menu {
                display: flex;
                flex-direction: column;
                // .newly-category {
                //     .el-button {
                //         padding: 8px 13px;
                //     }
                //     .el-button--primary {
                //         background-color: #2D84FB;
                //         border-color: #2D84FB;
                //     }
                //     .el-button--primary:hover {
                //         background: #66b1ff;
                //         border-color: #66b1ff;
                //         color: #FFF;
                //     }
                //     .el-button:active {
                //         background-color: #3a8ee6;
                //         border-color: #3a8ee6;
                //         outline: 0;
                //     }
                //     & i {
                //         font-size: 12px;
                //     }
                // }
                .create-material, .newly-category {
                    margin-left: 24px;
                }
                .create-material {
                    .el-button {
                        padding: 8px 13.5px;
                    }
                    .el-button--warning {
                        background-color: #FE6C17;
                        border-color: #FE6C17;
                    }
                    .el-button--warning:hover {
                        background: #eba67e;
                        border-color: #eba67e;
                        color: #FFF;
                    }
                    .el-button:active {
                        background-color: #d35f1c;
                        border-color: #d35f1c;
                        outline: 0;
                    }
                }
            }
        }
        .myPagination {
            text-align: end; 
            padding: 24px;
            // background-color: #F1F4FA;
        }
        .links-menu {
            height: 24px;
            line-height: 24px;
            background-color: #F1F4FA;
        }
        .material_list {
            box-sizing: border-box;
            background-color: #F1F4FA;
            max-height: calc(100vh - 259px);
            overflow-y: auto;
        }
    }
    .upload-box {
        text-align: center;
        .upload-title {
            margin: 158px 110px 0 110px;
            font-size: 14px;
            color: #2E3C4E;
        }
        .watermarking {
            font-size: 14px;
            color: #2D84FB;
            margin-top: 24px;
            margin-bottom: 220px;
            cursor: pointer;
        }
    }
    .upload-confirm {
        padding-top: 26px;
        border-top: 1px solid #D8D8D8;;
        .notWatermark {
            line-height: 40px;
            font-size: 16px;
            color: #2D84FB;
            margin-left: auto;
            cursor: pointer;
        }
        .el-button {
            width: 128px;
            height: 40px;
            background: #2D84FB;
            border-color: #2D84FB;
            box-shadow: 0 2px 6px 0 rgba(45,132,251,0.40);
            margin-left: calc(50% - 64px);
            font-size: 16px;
        }
    }
    ::v-deep .Rename-footer {
        .el-button:nth-child(1) {
            border: none;
            font-size: 16px;
            padding: 13px 32px;
        }

        .el-button:nth-child(1):hover {
            background-color: #FFFFFF;
        }

        .el-button:last-child {
            font-size: 16px;
            padding: 12px 47px;
            background-color: #2D84FB;
            border-color: #2D84FB;
        }

        .el-button--primary:hover {
            background: #66b1ff;
            border-color: #66b1ff;
            color: #FFF;
        }

        .el-button--primary:active {
            background: #3a8ee6;
            border-color: #3a8ee6;
            color: #FFF;
        }
    }
    .form_box {
        ::v-deep .el-form-item {
            .el-form-item__label {
                text-align: left;
            }
        }
    }
    ::v-deep .dialog-Photo {
        .el-dialog {
            .el-dialog__header {
                .el-dialog__title {
                    border-left: none;
                }
            }
        }
    }
    .upload-footer {
        justify-content: end;
        .el-button:first-child {
            border: none;
            font-size: 16px;
            padding: 13px 32px;
        }

        .el-button:first-child:hover {
            background-color: #FFFFFF;
        }

        .el-button:last-child {
            font-size: 16px;
            padding: 12px 47px;
            background-color: #2D84FB;
            border-color: #2D84FB;
        }

        .el-button:last-child:hover {
            background: #66b1ff;
            border-color: #66b1ff;
            color: #FFF;
        }

        .el-button:last-child:active {
            background: #3a8ee6;
            border-color: #3a8ee6;
            color: #FFF;
        }
    }
}
::v-deep .Photo-details {
    .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
        .photo-item {
            margin-right: 20px;
            .uploader-create {
                .el-upload{
                    position: relative;
                    .uploader-actions {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        cursor: default;
                        text-align: center;
                        color: #fff;
                        opacity: 0;
                        font-size: 20px;
                        background-color: rgba(0,0,0,.5);
                        transition: opacity .3s;
                        .uploader-actions-item {
                            margin: 0 10px;
                            font-size: 14px;
                            cursor: pointer;
                            & i {
                                color: #fff;
                            }
                        }
                    }
                    .uploader-actions:hover {
                        opacity: 1;
                    }
                }
            }
            .el-form {
                .el-form-item {
                    margin-top: 10px
                }
            }
        }
    }
    .photo-item-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}
</style>