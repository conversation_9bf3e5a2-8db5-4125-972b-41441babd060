<template>
  <div>
    <div class="page">
      <div class="tabs_container flex-row">
        <el-tabs v-model="house_type" @tab-click="handleClickType">
          <el-tab-pane v-for="(item, index) in types" :key="index" :name="item.type">
            <span slot="label">
              {{ item.name }}
              <el-popover v-model="my_HouseDown" popper-class="houseDown-container" placement="bottom" width="150"
                trigger="click">
                <div class="myHouse-down-List">
                  <div v-for="(list, idx) in my_HouseDownList" :key="list.value" class="down-list-content"
                    @click="optHousefilter(list, item, idx)">
                    {{ list.name }}
                  </div>
                </div>
                <span slot="reference" v-if="item.is_select">
                  <i class="el-icon-caret-bottom" @click="getMoreSelfHouse($event)"></i>
                </span>
              </el-popover>
              <el-badge v-if="item.num" :value="item.num" class="item" :type="item.color">
              </el-badge>
            </span>
          </el-tab-pane>
        </el-tabs>
        <div class="flex-row items-center">
          <div class="flex-row align-center" style="margin-left: 24px">
            <div class="house-search">
              <template v-if="searchType == '小区名称'">
                <el-select v-model="params.keyword" clearable :placeholder="searchDefaultName" filterable remote
                  :loading="community_loading" :remote-method="getCommutityList" @change="handleSearch">
                  <el-option v-for="item in commutityList" :key="item.id" :value="item.title" :label="item.title">
                    <div class="option-wrapper">
                      {{ item.detail }}
                    </div>
                  </el-option>
                </el-select>
              </template>
              <template v-else-if="searchType == '房源编号'">
                <el-input v-model="params.hid" :placeholder="searchDefaultName" type="number" step="1"
                  @keyup.enter.native="handleSearch" @input="handleSearch">
                </el-input>
              </template>
              <template v-else-if="searchType == '业主电话'">
                <el-input :placeholder="searchDefaultName" v-model="params.tel" @keyup.enter.native="handleSearch"
                  @input="handleSearch($event, 'tel')"></el-input>
              </template>
              <el-dropdown @command="dropdownChange">
                <el-button style="border-radius: 0 5px 5px 0;border-left: 0;">
                  {{ searchType }}<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="小区名称">小区名称</el-dropdown-item>
                  <el-dropdown-item command="房源编号">房源编号</el-dropdown-item>
                  <el-dropdown-item command="业主电话">业主电话</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <el-popover placement="bottom" width="500px" v-model="show_loudong_search">
              <div>
                <div>搜索</div>
                <div class="inps flex-row align-center" style="margin: 10px 0">
                  <el-input placeholder="输入楼栋" v-model="params.loudong"
                    style="margin-right: 10px; width: 100px"></el-input>
                  <el-input placeholder="输入单元" v-model="params.danyuan"
                    style="margin-right: 10px; width: 100px"></el-input>
                  <el-input placeholder="输入房号" v-model="params.fanghao" style="width: 100px"></el-input>
                </div>
                <div class="btns" style="text-align: right">
                  <el-button @click="resetLoudongSearch('loudong')">重置</el-button>
                  <el-button type="primary" @click="handleSearch">确定</el-button>
                </div>
              </div>

              <div class="search_loudong flex-row align-center" slot="reference">
                <div class="seach_value">楼栋门牌查找</div>
                <div class="sanjiao" :class="{ transt: show_loudong_search }"></div>
              </div>
            </el-popover>
            <el-button type="warning" style="margin-left: 24px" icon="el-icon-plus" @click="toAdd">录入房源</el-button>
          </div>
        </div>
      </div>

      <div class="left-box">
        <!-- 筛选区域 -->
        <div class="filter_container flex-row items-center">
          <div class="filter_type flex-1">
            <div class="list">
              <span v-for="(filter, idx) in filters" :key="'_' + idx">
                <el-select v-if="filter.type && filter.type != 'label_type'" :multiple="filter.multiple === 1" :style="{
                  minWidth: '40px',
                  width: getSelectWidth(filter, params[filter.type]),
                }" v-model="params[filter.type]" class="super_mini_select" :placeholder="filter.label"
                  @change="onSelectFilterOption">
                  <div class="custom" style="padding: 5px 10px" v-if="filter.customizale">
                    <el-input v-model="filter.custom_value.min" style="width: 70px" size="mini"
                      @change="customValueChange(idx, 'min')" @keyup.enter.native="useCustomValue(idx)"
                      @blur="useCustomValue(idx)" placeholder="最小"></el-input>
                    <span>-</span>
                    <el-input v-model="filter.custom_value.max" @change="customValueChange(idx, 'max')"
                      @keyup.enter.native="useCustomValue(idx)" @blur="useCustomValue(idx)" style="width: 70px"
                      size="mini" placeholder="最大"></el-input>
                    <span style="font-size: 14px; color: #999">{{
                      filter.unit
                    }}</span>
                  </div>
                  <el-option v-for="option in filter.item" :key="option.values" :value="option.values"
                    :label="option.name">
                    <span :ref="`${filter.type}_${option.values}`">{{
                      option.name
                    }}</span>
                  </el-option>
                </el-select>
                <el-cascader ref="cascader" v-else-if="filter.type && filter.type == 'label_type'"
                  v-model="params[filter.type]" :options="filter.item" :props="{
                    value: 'values',
                    label: 'name',
                    emitPath: false,
                  }" :show-all-levels="false" @change="onLabelChange" :style="{
  minWidth: '40px',
  width: getCascaderWidth(filter.item, params[filter.type]),
}" class="super_mini_Cascader" :placeholder="filter.label">
                </el-cascader>
                <el-select v-else :multiple="filter.multiple === 1" collapse-tags :style="{
                  minWidth: '40px',
                  width: getSelectWidth(filter, labels),
                }" class="super_mini_select" :placeholder="filter.label" @change="onSelectFilterOption2"
                  v-model="labels">
                  <el-option v-for="option in filter.item" :key="`${option.type}_${option.values}`"
                    :value="`${option.type}__${option.values}`" :label="option.name">
                    <span :ref="`${option.type}_${option.values}`">{{
                      option.name
                    }}</span>
                  </el-option>
                </el-select>
              </span>
              <span v-for="(filter, index) in filters2" :key="index">
                <el-select :style="{
                  minWidth: '40px',
                  width: getSelectWidth(filter, params[filter.type]),
                }" v-model="params[filter.type]" class="super_mini_select" :placeholder="filter.label"
                  @change="onSelectFilterOption">
                  <el-option v-for="option in filter.item" :key="option.values" :value="option.values"
                    :label="option.name">
                    <span :ref="`${filter.type}_${option.values}`">{{
                      option.name
                    }}</span>
                  </el-option>
                </el-select>
              </span>
              <span>
                <el-popover style="display: inline-block" placement="bottom" width="200px"
                  v-model="show_department_search">
                  <div class="flex-row inp_no_border items-center">
                    <el-cascader placeholder="请选择部门" style="width: 155px" v-model="params.department_id" clearable
                      :show-all-levels="false" :options="department_list" :props="{
                        value: 'id',
                        label: 'name',
                        children: 'subs',
                        emitPath: false,
                        checkStrictly: true,
                      }">
                    </el-cascader>
                    <el-button slot="append" icon="el-icon-search" style="
                      background: #f5f7fa;
                      border-left: none;
                      border-top-left-radius: 0;
                      border-bottom-left-radius: 0;
                    " @click="onchangeDepartment"></el-button>
                  </div>
                  <div style="margin-top: 10px">
                    <el-input style="width: 210px" v-model="params.user_name" placeholder="请输入人员">
                      <el-button slot="append" icon="el-icon-search" @click="onchangeDepartment"></el-button>
                    </el-input>
                  </div>

                  <div class="search_loudong flex-row align-center" slot="reference"
                    style="padding: 0; margin-left: 0; margin-right: 5px" @click="setDefault">
                    <div class="seach_value" style="color: #c0c4cc; font-size: 12px">
                      {{ department_name }}
                      <i class="el-icon-arrow-down" v-if="!show_department_search"></i>
                      <i class="el-icon-arrow-up" v-if="show_department_search"></i>
                    </div>

                    <!-- <icon
                    type="xiala"
                    size="22rpx"
                    v-if="show_department_search"
                  ></icon>
                  <icon
                    type="shangla"
                    size="22rpx"
                    v-if="!show_department_search"
                  ></icon> -->
                    <!-- <div
                    class="sanjiao"
                    :class="{ transt: show_department_search }"
                  ></div> -->
                  </div>
                </el-popover>
              </span>
            </div>
          </div>
        </div>
        <div class="info_list" v-if="tableData.length" :disabled="loading !== 'loadend'">
          <HouseItem v-for="(info, index) in tableData" :key="index" :info="info" :is_owner="house_list_type == 'owner'"
            :current="info.id === current_info.id" @click="showInfoDetail" />
          <div class="loading" v-if="loading == 'loading'" v-loading="loading == 'loading'" element-loading-text="加载中">
          </div>
        </div>

        <div class="no_data flex-row align-center j-center" v-if="loading == 'loading'">
          <div class="no_data_name">加载中。。。</div>
        </div>

        <div class="no_data flex-row align-center j-center" v-if="tableData.length == 0 && loading == 'nomore'">
          <div class="no_data_name">暂无房源</div>
          <div class="no_data_add" @click="toAdd">开始录入</div>
        </div>
      </div>
    </div>
    <div class="house-bom-box">
      <div class="house-bom-btn" style="margin-left: 20px;">
        <el-button type="primary" size="mini" icon="el-icon-refresh-right" @click="refresh">刷新页面</el-button>
        <el-button type="primary" size="mini" icon="el-icon-delete" @click="clearFilter">清空筛选项</el-button>
      </div>
      <el-pagination style="margin-right: 20px;" :hide-on-single-page="true" background
        layout="total, sizes, prev, pager, next, jumper" :total="data_count" :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="params.rows" :current-page="params.page" @current-change="onPageChange" @size-change="onSizeChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { isArray } from "util";
import HouseItem from "./components/HouseItem.vue";
export default {
  name: "house_list",
  data() {
    return {
      searchType: '小区名称',
      tabs: [],
      types: [],
      house_type: "all",
      site_list: [],
      keyword: "",
      loading: "",
      tab_name: "1",
      params: {
        page: 1,
        keyword: "",
        rows: 20,
        user_name: ""
      },
      show_detail: false,
      current_info: {},
      data_count: 0,
      show_select: false,
      filters: [],
      filters2: [],
      status_list: [
        {
          name: "全部",
          values: "",
        },
        {
          name: "上架中",
          values: 1,
        },
        {
          name: "待审核",
          values: 2,
        },
      ],
      labels: [],
      tableData: [],
      cur_page: 1,
      multipleSelection: [],
      show_set_showing: false,
      lookDay: "",
      startTime: "",
      endTime: "",
      set_showing: false,
      showing_params: {
        showing_stime: "",
        showing_etime: "",
        welfare: "",
      },
      house_list_type: "default",
      area_name: "",
      house_statistics: {},
      show_loudong_search: false,
      show_tel_search: false,
      commutityList: [],
      department_list: [],
      department_name: '部门',
      community_loading: false,
      show_department_search: false,
      show_cumm_search: false,
      commu: '小区/编号',
      website_ids: "", // 站点id
      copy_filters: [], // 用于重置房源筛选参数
      hasUnfollow: {
        info: {}
      },
      my_HouseDown: false, // 控制我的房源下拉菜单
      // 我的房源下拉列表
      my_HouseDownList: [
        { value: 1, name: "我录入的", type: "is_owner-1", is_select: true },
        { value: 2, name: "我维护的", type: "is_owner-2", is_select: true },
        { value: 3, name: "我实勘的", type: "is_owner-3", is_select: true },
        { value: 4, name: "我带看的", type: "is_owner-4", is_select: true },
        { value: 5, name: "我成交的", type: "is_owner-5", is_select: true },
      ]
    };
  },
  components: { HouseItem },
  computed: {
    searchDefaultName() {
      return `请输入${this.searchType}`
    }
  },
  created() {
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
    this.website_ids = this.$route.query.website_id; // 获取站点id
    if (this.$route.query && this.$route.query.trade_type) {
      this.params.trade_type = this.$route.query.trade_type;
      // this.house_type =
      //   this.params.trade_type > 0
      //     ? "trade_type-" + this.params.trade_type
      //     : "all";

    }
    if (this.params.trade_type == 0) {
      this.params.trade_status = 9
    }
    if (this.$route.query && this.$route.query.is_owner) {
      this.params.is_owner = this.$route.query.is_owner;
      // this.house_type = "is_owner-" + this.params.is_owner;
    }

    this.getCrmDepartmentList()
    this.getFilterList();


    // eslint-disable-next-line no-undef
    eventBus.$on("getDataAgain", () => {
      this.params.page = 1;
      this.getList();
    });
  },
  onPageRefresh() {
    this.getList();
  },
  methods: {
    onSizeChange(e) {
      this.params.rows = e
      this.getList();
    },
    dropdownChange(e) {
      this.searchType = e
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    async getList() {

      this.tableData = [];
      // if (this.params.page === 1) {
      //   this.tableData = [];
      //   this.multipleSelection = [];
      // }
      this.loading = "loading";
      let params = Object.assign({}, this.params);
      if (isArray(params.department_id)) {
        params.department_id = params.department_id[params.department_id.length - 1]
      }
      // params.keyword = encodeURI(params.keyword)
      this.$ajax.house
        .getList(params)
        .then((res) => {
          this.loading = "loadend";
          if (res.status == 200) {
            let department_id = res.data?.current_login_user?.department_id
            if (department_id) {
              this.department_id = (department_id + '').split(",")
              this.department_id = this.department_id.map(item => Number(item))
            }
            this.defaultUser = res.data?.current_login_user?.user_name
          }
          if (res.status == 200 && res.data.data.length > 0) {
            if (res.data.data.length < this.params.rows) {
              this.loading = "nomore";
            }
            var list = res.data.data;
            // list[0].price_change = {
            //   direct: 2,
            //   direct_desc: "降价",
            //   num: 43,
            //   price_now: 405000,
            //   price_ori: 410000,
            //   range: 5000,
            // }
            this.tableData = list;
          } else {
            this.loading = "nomore";
          }
          this.data_count = res.data.total || 0;
        })
        .catch((err) => {
          console.log(err);
          this.loading = "loadend";
        });
    },

    getFilterList() {
      this.$ajax.house.getFilterList().then((res) => {
        if (res.status === 200) {
          //
          this.filters = res.data.first
            .filter((item) => item.type !== "trade_type")
            .map((e) => {
              // 区域传参是传的汉字，需要把valus的值设置为其name的值

              if (e.type === "districtName") {
                e.item.map((chil) => (chil.values = chil.name));
              }

              // 如果支持自定义数值
              if (e.customizale) {
                e.custom_value = {
                  min: "",
                  max: "",
                };
              }
              if (
                e.multiple == 0 &&
                e.item.findIndex((chil) => chil.values === "") === -1
              ) {
                // 追加一个不限
                e.item.unshift({
                  name: "不限",
                  values: "",
                });
              }

              return e;
            });
          let timeFilterType = {
            customizale: 0,
            item: [
              { name: '不限', values: '' },
              { name: '跟进时间', values: '1' },
              { name: '录入时间', values: '2' },
            ],
            label: '时间类型',
            multiple: 0,
            type: 'order_type',
            unit: ''
          }
          let timeFilter = {
            customizale: 0,
            item: [
              { name: '不限', values: '' },
              { name: '降序', values: 'desc' },
              { name: '升序', values: 'asc' },
            ],
            label: '排序方式',
            multiple: 0,
            type: 'order_sort',
            unit: ''
          }
          this.filters = [timeFilterType, timeFilter, ...this.filters]

          let type_list = res.data.first.find(
            (item) => item.type == "trade_type"
          );
          let trade_type = type_list.item;
          let houseListInfo = sessionStorage.getItem('top_menu_info')
          let houseList = [], houseListIds = []
          if (houseListInfo) {
            houseListInfo = JSON.parse(houseListInfo)
          }
          if (houseListInfo && houseListInfo.children && houseListInfo.children.length) {
            let houseLists = houseListInfo.children.filter(item => item.id == 186) || {}

            if (houseLists && houseLists.length) {
              console.log(houseLists);
              houseList = houseLists[0].children || []
            }
            houseList.map(item => {
              houseListIds.push(item.id)
            })
          }
          if (houseListIds.includes(187)) {
            this.types = [
              {
                name: "公盘房源",
                type: "all",
                num: 0,
                color: "primary",
              },
            ];
          }
          trade_type.map(item => {
            if (item.values == 1) {
              item.id = 195
            }
            if (item.values == 2) {
              item.id = 196
            }
            if (item.values == 3) {
              item.id = 197
            }
          })
          trade_type.map((item) => {
            let obj = {
              name: item.name,
              type: `trade_type-${item.values}`,
              num: 0,
              color: "primary",
            };
            if (houseListIds.includes(item.id)) {
              this.types.push(obj);
            }
          });
          // 如果Session中存在成交，房源tabs新增"成交列表
          houseList.map((item) => {
            if (item.id == 236) {
              this.types.push({
                name: "成交",
                type: "trade_type-4",
                num: 0,
                color: "primary",
              });
            }
          })
          // this.types.push({
          //   name: "我的房源",
          //   type: "is_owner-1",
          //   num: 0,
          //   color: "primary",
          //   is_select: true,
          // });
          this.filters2 = res.data.second.map((e) => {
            if (e.item.findIndex((chil) => chil.values === "") === -1) {
              // 追加一个不限
              e.item.unshift({
                name: "不限",
                values: "",
              });
            }
            return e;
          });
          if (this.params.is_owner) {
            this.house_type = "is_owner-" + this.params.is_owner;
          } else if (this.params.trade_type > 0) {
            this.house_type =
              "trade_type-" + this.params.trade_type
          } else {
            this.house_type = this.types[0].type
            let arr = this.house_type.split("-")
            this.params[arr[0]] = arr[1]
          }


          // this.house_type =
          //   this.params.trade_type > 0
          //     ? "trade_type-" + this.params.trade_type
          //     : "all";
          this.getList();
          this.getHouseSeachLabel(); // 获取房源标签
          this.copy_filters = JSON.parse(JSON.stringify(this.filters)); // 深拷贝数据
        }
      });
    },
    onSelectFilterOption() {
      console.log(this.params);
      this.params.page = 1;
      this.getList();
    },
    onSelectFilterOption2(e) {
      var multiple_select = this.filters.filter((item) => item.multiple);
      multiple_select.forEach((e) => {
        e.item.forEach((item) => {
          delete this.params[item.type];
        });
      });
      e.forEach((item) => {
        var splted = item.split("__");
        this.params[splted[0]] = splted[1];
      });
      this.onSelectFilterOption();
    },
    async showInfoDetail(e) {

      if (this.hasUnfollow.remindPhoneFollow && this.hasUnfollow.count > 0) {
        this.$confirm(`您有${this.hasUnfollow.count}条查看电话记录未跟进 请先跟进`, "提醒").then(() => {
          this.$goPath(`/house_detail?id=${this.hasUnfollow.info.info_id}&unfollow=1`)
        })
        return
      }
      // console.log(e);
      // window.open(`${window.location.origin}/#/house_detail?id=${e.id}&website_id=${localStorage.getItem('website_id')}`, '_blank')
      this.$goPath(`/house_detail?id=${e.id}`);
    },
    setDefault() {
      console.log(123);
      if (!this.params.department_id || (this.params.department_id && this.params.department_id.length == 0)) {
        this.params.department_id = this.department_id || ''
      }
      if (!this.params.user_name) {
        this.$set(this.params, "user_name", this.defaultUser)
        //  this.params.user_name =
      }


    },
    refresh() {
      this.params = { page: 1, rows: 10 };
      this.getList();
    },
    handleSearch(e, type) {
      if (type == 'tel') {
        if (e.length < 1) {
          this.params.page = 1;
          this.getList();
          return
        }
        if (e.length < 11 || e.length > 11) return
      }
      this.params.page = 1;
      this.getList();
      if (this.show_loudong_search) {
        this.show_loudong_search = false;
      }
      if (this.show_cumm_search) {
        this.show_cumm_search = false;
      }
    },
    getCommutityList(e) {
      console.log(e);
      if (!e) return
      this.community_loading = true
      this.unsearch = false
      this.$ajax.house
        .searchCommunity(e)
        .then((res) => {
          console.log(res);
          this.community_loading = false
          if (res.status === 200) {
            this.commutityList = res.data

          } else {

            this.community_list = []
          }
        })
        .catch(() => {
          this.community_loading = false
        })
    },
    resetLoudongSearch(type = "loudong") {
      if (type == "loudong") {
        this.params.loudong = "";
        this.params.danyuan = "";
        this.params.fanghao = "";
        this.show_loudong_search = false;
      } else {
        this.params.tel = "";
        this.show_tel_search = false;
      }
      this.params.page = 1;
      this.getList();
    },
    toAdd() {
      if (this.hasUnfollow.remindPhoneFollow && this.hasUnfollow.count > 0) {
        this.$confirm(`您有${this.hasUnfollow.count}条查看电话记录未跟进 请先跟进`, "提醒").then(() => {
          this.$goPath(`/house_detail?id=${this.hasUnfollow.info.info_id}`)
        })
        return
      }
      let trade_type = this.$route.query.trade_type || 1 // 赋值房源类型列表id 1出售 2出租 3租售
      if (trade_type == 1 || trade_type == 2 || trade_type == 3) {
        return this.$router.push({ path: "house_add", query: { trade_type: trade_type, website_id: this.website_ids } });
      }
      this.$goPath("house_add");
    },
    handleClickType(tab) {
      // 当tabs切换重置我的房源tabs
      // let judge = tab.name.split("-")[0];
      // let cut = {};
      // let exist = null;
      // if (judge != 'is_owner') {
      //   cut = this.types[this.types.length - 1];
      //   if (cut.name != '我的房源') {
      //     this.types.pop();
      //     exist = this.my_HouseDownList.findIndex(item => item.name == cut.name);
      //     if (exist == -1) {
      //       this.my_HouseDownList.push(cut);
      //     }
      //     this.types.push({
      //       name: "我的房源",
      //       type: "is_owner-1",
      //       num: 0,
      //       color: "primary",
      //       is_select: true,
      //     })
      //   }
      // }
      let houseTypeNum = JSON.parse(JSON.stringify(this.filters))
      // 如果tabs切换到 成交
      if (tab.name == "trade_type-4") {
        houseTypeNum[0].item.splice(1, 4); // 删除有效、无效等参数
        this.filters[0] = houseTypeNum[0]; // 重新赋值
        this.params["trade_status"] = ""; // 选中参数为不限
      } else {
        this.filters = JSON.parse(JSON.stringify(this.copy_filters)); // 重置参数
        this.params["trade_status"] = 9; // 重置参数为有效
      }
      const house_tab = tab.name;
      delete this.params.is_owner;
      delete this.params.is_follow;
      delete this.params.status;
      delete this.params.trade_type;
      var tab_split = house_tab.split("-");
      if (tab_split[0] === "all") {
        this.house_list_type = "default";
        // this.initData()
      } else {
        this.house_list_type = "owner";
        this.params[tab_split[0]] = tab_split[1];
      }
      this.handleSearch();
    },
    clearFilter() {
      // house_type: 当前tabs切换的 类型
      if (this.house_type == "trade_type-4") {
        // 如果类型为 成交
        this.params = {
          page: 1,
          keyword: "",
          rows: 10,
          trade_status: "",
        };
      } else {
        this.params = {
          page: 1,
          keyword: "",
          rows: 10,
          trade_status: 9,
        };
      }
      this.labels = [];
      this.house_list_type = "default";
      this.getList();
    },
    // 获取部门列表
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    onchangeDepartment() {
      this.params.page = 1
      this.getList();
      this.show_department_search = false
    },

    onCheckboxGroupChange() {
      console.log(this.params);
    },
    onCheckboxChange(e, params = {}) {
      let { value, type, item } = params;
      var options = Array.from(item);
      // 获取当前类型在options中所有当前类型的option
      var current_type_options = options.filter((item) => item.type === type);
      if (e) {
        // 判断选项中是不是有多个相同当前选中类型
        if (current_type_options.length > 1) {
          if (!this.params[type]) {
            this.params[type] = [];
          }
          // 将值添加到参数中
          this.params[type].push(value);
        } else {
          this.params[type] = value;
        }
      } else {
        if (this.params[type] === undefined) {
          return;
        }
        // 判断选项中是不是有多个相同当前选中类型
        if (current_type_options.length > 1) {
          // 将值从参数中删除
          let value_index = this.params[type].findIndex(
            (item) => item === value
          );
          if (value_index >= 0) {
            this.params[type].splice(value_index, 1);
            if (this.params[type].length === 0) {
              delete this.params[type];
            }
          }
        } else {
          delete this.params[type];
        }
      }
      this.params.page = 1;
      this.getData();
    },

    getSelectWidth(filter, param) {
      var text_temp = "",
        text_width = 0,
        text_el = null;
      var current_option =
        filter.item && filter.item.find((item) => item.values === param);
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${filter.label}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text");
      text_width = text_el.offsetWidth;
      text_el.remove();
      return text_width + 16 + "px";
    },
    // 处理二级菜单文字宽度
    getCascaderWidth(filter, param) {
      var text_temp = "", // 标签模板容器
        text_width = 0, // 标签宽度
        text_el = null; // 标签实例
      var current_option = ""; // 当前选择的菜单内容
      filter.map((item) => {
        if (item && item.children) {
          item.children.map((list) => {
            if (list.values == param) {
              current_option = list;
            }
          })
        }
      })
      // 如果找到层级就处理文字宽度，如果没有默认空
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`; // 生成模板
        document.body.insertAdjacentHTML("afterbegin", text_temp); // 插入DOM元素，内部的第一个子节点之前
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden"></span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text"); // 获取dom
      text_width = text_el.offsetWidth; // 赋值元素的布局宽度
      text_el.remove(); // DOM 树中删除
      return text_width + 16 + "px";
    },
    // 自定筛选数值改变时
    customValueChange(filter_index) {
      var min_value = this.filters[filter_index].custom_value.min;
      var max_value = this.filters[filter_index].custom_value.max;
      if (min_value !== "" && max_value !== "") {
        this.params[
          this.filters[filter_index].type
        ] = `${min_value},${max_value}`;
      }
    },
    useCustomValue(filter_index) {
      var min_value = this.filters[filter_index].custom_value.min;
      var max_value = this.filters[filter_index].custom_value.max;

      if (
        (min_value === "" || max_value === "") &&
        !(min_value === "" && max_value === "")
      )
        return;
      // if (min_value === '') {
      //   this.$message.warning('请输入最小值')
      //   return
      // }
      // if (max_value === '') {
      //   this.$message.warning('请输入最大值')
      //   return
      // }
      if (!(min_value === "" && max_value === "")) {
        this.params[
          this.filters[filter_index].type
        ] = `${min_value},${max_value}`;
      } else {
        this.params[this.filters[filter_index].type] = "";
      }

      this.params.page = 1;
      this.getList();
    },
    // 获取房源筛选标签
    getHouseSeachLabel() {
      this.$ajax.house.getHouseSeachLabel().then(res => {
        if (res.status == 200) {
          let seachoLabel_list = res.data.item; // 赋值标签列表
          seachoLabel_list[0].children.unshift({ name: '不限', values: "" }); // 追加筛选不限
          this.filters.map((item) => {
            if (item.type == "label_type") {
              item.item = seachoLabel_list;
            }
          })
        }
      })
    },
    // 标签筛选发生改变
    onLabelChange() {
      this.getList(); // 获取最新数据
    },
    // 获取更多我的房源
    getMoreSelfHouse(e) {
      e.preventDefault();
      e.stopPropagation();
      this.my_HouseDown = !this.my_HouseDown; // 显示我的房源下拉
      // 将列表按照value从小到大排序
      var min = {};
      for (var i = 0; i < this.my_HouseDownList.length; i++) {
        for (var j = i; j < this.my_HouseDownList.length; j++) {
          if (this.my_HouseDownList[j].value < this.my_HouseDownList[i].value) {
            min = this.my_HouseDownList[j];
            this.my_HouseDownList[j] = this.my_HouseDownList[i];
            this.my_HouseDownList[i] = min;
          }
        }
      }
      // 将‘我的房源置顶列表’
      this.my_HouseDownList.map((item, index) => {
        if (item.name == '我的房源') {
          this.my_HouseDownList.splice(index, 1);
          this.my_HouseDownList.splice(0, 0, item);
        }
      })

      console.log(this.my_HouseDownList, "this.my_HouseDownList")
    },
    // 选择更多的房源筛选
    optHousefilter(list, item, idx) {
      console.log(item, "items")
      this.house_type = list.type; // 赋值当前切换的筛选
      this.my_HouseDown = false; // 关闭popover框
      this.types.pop(); // 删除最后一项
      this.types.push(list); // 增加当前点击筛选
      this.my_HouseDownList.splice(idx, 1); // 删除当前点击筛选
      if (item.name != '我的房源') {
        this.my_HouseDownList.unshift(item); // 增加当前删除的筛选
      }
      const house_tab = list.type;
      delete this.params.is_owner;
      delete this.params.is_follow;
      delete this.params.status;
      delete this.params.trade_type;
      var tab_split = house_tab.split("-");
      this.house_list_type = "owner";
      this.params[tab_split[0]] = tab_split[1];
      this.handleSearch();
    }
  },
  beforeDestroy() {
    // eslint-disable-next-line no-undef
    eventBus.$off("getDataAgain");
  },
  async activated() {
    let followInfo = await this.$http.getFollowInfo().catch((err) => {
      console.log(err);
    })
    if (followInfo.status == 200) {
      this.hasUnfollow = followInfo.data
    }
    if (this.$store.state.allowUpdate) {
      this.$store.state.allowUpdate = false;
      this.params.page = 1;
      this.getList();
    }
  },
  async deactivated() {
    let followInfo = await this.$http.getFollowInfo().catch((err) => {
      console.log(err);
    })
    if (followInfo.status == 200) {
      this.hasUnfollow = followInfo.data
    }
  },
};
</script>

<style lang="scss" scoped>
.house-bom-box {
  position: fixed;
  left: 230px;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  width: calc(100vw - 230px);
  padding: 20px 0 10px 0;
  box-shadow: 0px -3px 6px rgba(0, 0, 0, .1);
  border-radius: 3px 3px 0 0;
  background-color: #fff;
}

.house-search {
  display: flex;
  align-items: center;
  margin-right: 10px;

  /deep/ .el-input__inner {
    width: 200px;
    border-radius: 5px 0 0 5px;
    border-right: 0;
  }
}

.page {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .bg_white {
    background: #fff;
  }
}

.tabs_container {
  justify-content: space-between;
  padding: 10px 26px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 9;
  margin-bottom: 20px;

  .el-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 0;
    }

    ::v-deep .el-tabs__nav-wrap::after {
      height: 0;
    }

    ::v-deep .el-tabs__active-bar {
      height: 3px;
      bottom: 4px;
    }
  }
}

.data_list {
  display: flex;
  padding-top: 24px;
  padding-bottom: 24px;

  .data_item {
    flex: 1;
    flex-shrink: 0;
    text-align: center;

    .value {
      font-size: 18px;
      font-weight: bold;
      color: #2d84fb;
    }

    .label {
      margin-top: 2px;
      font-size: 14px;
      color: #999;
    }
  }
}

.filter_container {
  padding: 12px;
  background-color: #f8f8f8;
  font-size: 12px;
  color: #8a929f;
  position: sticky;
  top: 10px;
  display: flex;
  align-items: center;
  height: 30px;

  .filter_type {
    display: flex;

    >.label {
      padding: 10px;
      line-height: 18px;
      // width: 48px;
    }

    >.list {
      flex: 1;
      padding: 10px 0;

      .item {
        padding: 5px 0;
        margin-right: 24px;
        cursor: pointer;

        &.active {
          color: #2d84fb;
        }
      }

      .el-select {
        cursor: pointer;
      }

      .el-checkbox {
        margin-right: 24px;

        ::v-deep .el-checkbox__label {
          padding-left: 8px;
          font-size: 12px;
          color: #8a929f;
        }
      }
    }

    .custom {
      display: inline-block;

      .el-input {
        margin: 0 8px;
        height: 16px;
        width: 48px;
        font-size: 11px;
        text-align: center;

        ::v-deep .el-input__inner {
          height: 16px;
          line-height: 16px;
          padding: 0 5px;
          text-align: center;
        }
      }

      ::v-deep .el-button {
        &.el-button--mini {
          padding: 3px 8px;
        }
      }
    }
  }

  .el-select {
    &.super_mini_select {
      margin-right: 24px;
      font-size: 12px;

      ::v-deep .el-input__inner {
        padding-right: 0;
        padding-left: 0;
        height: auto;
        line-height: 1;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        font-size: 12px;
        color: #8a929f;
      }

      ::v-deep .el-input__suffix {
        right: 0;
      }

      ::v-deep .el-input__icon {
        width: auto;
        line-height: 15px;
        font-size: 12px;
      }

      ::v-deep .el-select__tags {
        max-width: initial !important;
      }
    }
  }
}

// 加载中
.loading {
  position: relative;
  padding: 12px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-size: 14px;
  color: #8a929f;

  ::v-deep .el-loading-mask {
    z-index: 1000;
  }

  ::v-deep .el-loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ::v-deep .el-loading-spinner .circular {
    width: 24px;
    height: 24px;
  }
}

.left-box {
  .info_list {
    height: calc(100vh - 315px);
    overflow-y: auto;
  }
}

.search_loudong {
  background: #f8f8f8;
  height: 41px;
  padding: 0 11px;
  margin-left: 5px;
  font-size: 13px;
  color: #999;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #999;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #999;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #999;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.no_data {
  padding: 50px 0;
  font-size: 14px;
  color: #999;
  background: #fff;

  .no_data_add {
    margin-left: 10px;
    color: #2d84fb;
    cursor: pointer;
  }
}

.refresh {
  margin-top: 8px;
}

.inp_no_border ::v-deep .el-input__inner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

::v-deep .super_mini_Cascader {
  margin-right: 24px;
  font-size: 12px;

  .el-input {
    .el-input__inner {
      padding-right: 0;
      padding-left: 0;
      height: auto;
      line-height: 1;
      border: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: none;
      font-size: 12px;
      color: #8a929f;
    }

    .el-input__suffix {
      right: 0;

      .el-input__suffix-inner {
        .el-input__icon {
          width: auto;
          line-height: 15px;
          font-size: 12px;
          padding-top: 3px;
        }
      }
    }

    // .el-select__tags {
    //   max-width: initial !important;
    // }
  }
}

.myHouse-down-List {
  display: flex;
  flex-direction: column;

  .down-list-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .down-list-content:hover {
    background-color: #f5f7fa;
  }
}

::v-deep .houseDown-container {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .1) !important;
}
</style>
