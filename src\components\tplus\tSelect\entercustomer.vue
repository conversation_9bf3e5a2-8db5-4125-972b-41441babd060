<template>
  <div>
    <div class="page">
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：" class="linefeed">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：" class="linefeed">
            <div class="row input-box div isbottom">
              <div>
                <el-input placeholder="请输入电话号码" v-model="push_form.mobile" maxlength="11"
                @blur="Validationphone(push_form.mobile)"></el-input>
              </div>
                <div>
                  <img class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png" alt=""
                    @click.prevent="addDomain" />
                </div>
            </div>
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <div class="row div" v-if="other_mobile.length">
                <div class="phonerow flex-row">
                  <el-input style="margin-bottom:10px;" placeholder="请输入客户姓名" v-model="domain.name"
                    maxlength="11"></el-input>
                  <el-input placeholder="请输入电话号码" v-model="domain.tel" maxlength="11"
                    @blur="Validationphone(domain.tel)"></el-input>
                </div>
                <div>
                  <img class="add" @click.prevent="removeDomain(domain)"
                    src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="微信号：" v-if="radio1==3" class="linefeed">
            <div class="row input-box div">
              <el-input placeholder="请输入微信号" v-model="push_form.wx_account"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="抖音号：" v-if="radio1==3" class="linefeed">
            <div class="row input-box div">
              <el-input placeholder="请输入抖音号" v-model="push_form.dy_account"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户来源：" class="linefeed">
            <div class="input-box">
              <el-cascader :style="{minWidth: '20px',width: '100%',}" v-model="source_idvalue" placeholder="请选择" :options="sourceLabel" @change="sourceLabel_status"
                :props="{
                  label: 'title',
                  value: 'id',
                  children: 'children',
                  checkStrictly: true 
                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：" class="linefeed">
              <div class="sex-box">
                <div class="isactive" clas v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id == push_form.sex }"
                @click="() => {push_form.sex = item.id;}" >
                  <img
                    :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt=""
                    />
                    <div class="isactivesex">{{item.id==2?"女":"男"}}</div>
                </div>
              </div>
          </el-form-item>
          <el-form-item label="客户级别：" class="linefeed">
            <el-select v-model="push_form.level_id" placeholder="请选择客户等级" style="width: 235px;">
              <el-option v-for="item,index in level_list" :key="index" :label="item.title+'级'"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户类型：" class="linefeed">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户标签：" class="linefeed">
            <div class="input-box">
              <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签"
                :options="label_default_list" 
                :props="{
                  value: 'id',
                  label: 'name',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="所在城市：" v-if="is_show_city==1" class="linefeed">
            <div class="block">
              <el-cascader style="width: 240px;" v-model="provincesvalue" clearable 
              :props="{
                value: 'id',
                label: 'name',
                children: 'children',
                emitPath: true,
                multiple: false,
              }" :options="provincesoptions" @change="provincesChange"></el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：" class="linefeed">
            <div class="row input-box div">
              <t-crm-project-select multiple allow-create default-first-option value-key="name" placeholder="请选择或输入"
                v-model="push_form.intention_community" width="240px" />
            </div>
          </el-form-item>
          <el-form-item label="备注：" class="linefeed" v-if="showremark==1">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

  </div>
</template>
<script>
import TCrmProjectSelect from '@/components/tplus/tSelect/tCrmProjectSelect.vue';
export default {
  components:{
    TCrmProjectSelect, 
  },
  props:{
    sourceLabel:{
          type: Array,
          default: () => [],
      },
    level_list:{
      type:Array,
      default:() => [],
    },
    typeLabel:{
      type:Array,
      default:() => []
    },
    label_default_list:{
      type:Array,
      default:() => []
    },
    radio1:{
      type:Number,
      default:()=>""
    }
  },
    data() {
        return {
            push_form: {
              cname: "",
              source_id: "",
              source2_id:'',
              level_id: "",
              type: "",
              sex: 1,
              subsidiary_mobile: [],
              intention_community: [],
              label: "",
              mobile:"",
              // intention_street: "",
              remark: "",
              add_type:"",//1是私客，2是公海
            },
            // name:"",
            other_mobile: [],
            source_idvalue:"",//录入客户的客户来源
            sex_list: [
              { id: 1, name: "nan" },
              { id: 2, name: "nv3" },
            ],
            is_show_city:"",//城市
            showremark:0,//备注功能是否显示
        }
    },
    watch:{
      radio1: {  
        handler(newVal) {
          this.push_form.add_type = newVal
        }
    }
    },
    mounted(){
      this.getshowremark()
      this.push_form.add_type = this.radio1
    },
    methods: {
    //获取配置项中备注功能是否显示
    getshowremark() {
      this.$http.getAuthShow('is_open_remark').then(res => {
        if (res.status == 200) {
          this.showremark = res.data
        }
      })
    },
      //验证手机号
      Validationphone(phoneNumber){
        console.log(phoneNumber);
        if(phoneNumber){
          const regex = /^(?:1[3-9]\d{9}|(?:\+?852|00852)?[5-9]\d{7}|(?:\+?853|00853)?[6-9]\d{7})$/;
          if (regex.test(phoneNumber)) {
            // this.$message.success('手机号格式正确')
          } else {
            this.$message.warning('请输入正确的手机号')
          }
        }

      },
      //录入客户，客户来源
      sourceLabel_status(e){
        if(e.length>1){
          this.push_form.source2_id = e[1]
          this.push_form.source_id = e[0]
        }else{
          this.push_form.source_id = e[0]
           this.push_form.source2_id = 0
        }
      },
      //添加附属号码
      addDomain() {
      this.other_mobile.push({
        name:"",
        tel: "",
      });
    },
    //删除附属号码
    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    async onClickForm() {
      console.log(this.push_form,"提交数据===========");
      if (this.push_form.label &&this.push_form.label != undefined &&typeof this.push_form.label !== "string") {
        this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
      }
      this.push_form.add_type = this.radio1
      if (this.other_mobile.length > 0) {
        this.push_form.subsidiary_mobile = this.other_mobile
      }
      if( this.push_form.add_type!==3){
          if (!this.push_form.mobile) {
            this.$message.error("请检查联系方式");
            return;
          }
          if (!this.push_form.cname) {
            this.$message.error("请检查客户姓名");
            return;
          }
          if (!this.push_form.sex) {
            this.$message.error("请检查客户性别");
            return;
          }
          if (!this.push_form.type) {
            this.$message.error("请检查客户类型");
            return;
          }
          if (!this.push_form.source_id) {
            this.$message.error("请检查客户来源");
            return;
          }
      }else{
        if (!this.push_form.type) {
          this.push_form.type = 0
          }
        if (!this.push_form.source_id) {
          this.push_form.source_id = 0
        }
      }
      if (!this.push_form.level_id) {
        this.push_form.level_id = 0
        // this.$message.error("请检查客户等级");
        // return;
      }
      if (!this.push_form.source2_id) {
        this.push_form.source2_id = 0
      }
      const params = {...this.push_form}
      params.intention_community = params.intention_community.length ? params.intention_community.join(",") : '';
      console.log(params);

      //是否创建流转客
      let isCreateTransCustomer = this.push_form.add_type==3;  
      if(!isCreateTransCustomer){
        //录入客户时验证
        const res = await this.$http.verifyCrmCustomerCreate(params.mobile).catch(()=>{})
        if(!res || res.status != 200){
          return;
        }
        //客户已存在，不去重， 走添加流转客接口
        if(res.data.number > 0 && res.data.add_client_repeat == 1){
          isCreateTransCustomer = true;
        }
      }

      // 流转客
      if(isCreateTransCustomer){
        console.log(12121212,params);
        delete params.add_type
        // params.subsidiary_mobile = JSON.stringify(params.subsidiary_mobile);
        this.$http.addCustomerinformation(params).then((res) => {
          if (res.status === 200) {
            //this.$message.success("操作成功");
            this.$confirm(res.data?.msg || "录入成功，请前往我的客户-流转客查看", "提示", {
                confirmButtonText: "客户详情",
                cancelButtonText: "知道了",
                type: "success",
            }).then(()=>{
              let url = `/crm_information_detail?id=${res.data.id}&type=my&information=1`;
              this.$goPath(url); // 跳转客户详情
            }).catch(()=>{})
            
            this.$emit('child-event', '');
            this.reset();
            this.provincesvalue = []
            this.source_idvalue =""
            if(this.radio1==3){
              this.push_form.add_type = 3
            }
          } else if (res.status === 422) {
            const cus_id =
              res.data.data &&
                res.data.data.id != "" &&
                res.data.data.id != undefined
                ? res.data.data.id
                : 0; // 赋值客户id
            // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
            if (
              res.data.data &&
              res.data.data.follow_id &&
              res.data.data.follow_id != undefined &&
              res.data.data.follow_id != 0
            ) {
              this.$confirm("客户号码已有维护人，无法重复录入。", "提示", {
                confirmButtonText: "立即查看",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  let url = `/crm_information_detail?id=${cus_id}&type=my&information=1`;
                  this.$goPath(url); // 跳转客户详情
                })
                .catch(() => {
                  return;
                });
            } else {
              // 该客户没有维护跟进人时触发
              if (cus_id > 0) {
                this.$confirm("客户号码已存在，认领后可跟进。", "提示", {
                  confirmButtonText: "立即认领",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    this.$http
                      .getCrmCustomerPublick({ ids: cus_id + "" })
                      .then((res) => {
                        if (res.status === 200) {
                          this.$message.success("认领成功");
                          let url = `/crm_information_detail?id=${cus_id}&type=my&information=1`;
                          this.$goPath(url); // 跳转客户详情
                        }
                      });
                  })
                  .catch(() => {
                    return;
                  });
              }
            }
          }
        });
      }else{
        this.$http.newEntercustomer(params).then((res) => {
        if (res.status === 200) {
          //this.$message.success("操作成功");
          this.$confirm(res.data?.msg || "录入成功，请前往客户详情查看", "提示", {
              confirmButtonText: "客户详情",
              cancelButtonText: "知道了",
              type: "success",
          }).then(()=>{
            let url = `/crm_customer_detail?id=${res.data.id}&type=my`;
            this.$goPath(url); // 跳转客户详情
          }).catch(()=>{})

          this.$emit('child-event', '');
          this.reset();
          this.provincesvalue = []
          this.source_idvalue =""
        } else if (res.status === 422) {
          const cus_id =
            res.data.data &&
              res.data.data.id != "" &&
              res.data.data.id != undefined
              ? res.data.data.id
              : 0; // 赋值客户id
          // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
          if (
            res.data.data &&
            res.data.data.follow_id &&
            res.data.data.follow_id != undefined &&
            res.data.data.follow_id != 0
          ) {
            this.$confirm("客户号码已有维护人，无法重复录入。", "提示", {
              confirmButtonText: "立即查看",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                this.$goPath(url); // 跳转客户详情
              })
              .catch(() => {
                return;
              });
          } else {
            // 该客户没有维护跟进人时触发
            if (cus_id > 0) {
              this.$confirm("客户号码已存在，认领后可跟进。", "提示", {
                confirmButtonText: "立即认领",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  this.$http
                    .getCrmCustomerPublick({ ids: cus_id + "" })
                    .then((res) => {
                      if (res.status === 200) {
                        this.$message.success("认领成功");
                        let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                        this.$goPath(url); // 跳转客户详情
                      }
                    });
                })
                .catch(() => {
                  return;
                });
            }
          }
        }
      });
      }
    },
    //重置表单
    reset() {
      this.push_form = {
        cname: "",
        source_id: "",
        source2_id: "",
        level_id: "",
        type: "",
        sex: 1,
        subsidiary_mobile: [],
        intention_community: [],
        // intention_street: "",
        remark: "",
      };
      // this.other_mobile = [{ mobile: "" }];
    },
    },
}
</script>
<style lang="scss" scoped>
.page {
  width: 90%;
  // background-color: orange;
  margin: 0 auto;

 /deep/ .i-form-list {
    .el-form-item__label {
      color: #8a929f;
      font-size: 15px !important;
    }
.linefeed{
  white-space: nowrap;
}
    .input-box {
      width: 238px;
      justify-content: space-between;

      .l-item {
        background: #f0f3f9;
        cursor: pointer;
        border-radius: 4px;
        color: #8a929f;
        text-align: center;
        line-height: 1;
        padding: 10px;
        border: 1px solid #fff;

        .l-name-1 {
          font-size: 12px;
          margin-top: 12px;
        }
      }

      .l-item-type {
        width: 40%;
      }

      .lisactive {
        background: #fff;
        border: 1px solid rgba(45, 132, 251, 1);
        color: #2d84fb;
      }
    }
      .sex-box {
         width: 235px;
         height: 34px;
         display: flex;
        //  justify-content: space-between;
        img {
          width: 30px;
          height: 30px;
          border-radius: 4px;
          border: 1px solid #fff;
          margin-right: 14px;
        }
        .isactive{
          width: 120px;
          border: 1px solid  #dfe5ef;
          border-radius: 4px;
          margin-left: 3px; /* 添加右边距 */
          display: flex;
          align-items: center;
          .isactivesex{
            font-size: 15px;
            margin-left: 5px;
            color: #8a929f;
          }
        }
        .is_active {
            border: 1px solid rgba(45, 132, 251, 1);
            // border-radius: 4px;
            // margin-left: 3px; /* 添加右边距 */
          }
      }
    .isbottom {
      // align-items: center;
      // justify-content: space-between;
      margin-bottom: 10px;
      // flex-wrap: wrap;

      .el-input {
        width: 237px;
      }

      .add {
        width: 16px;
        height: 16px;
        margin-left: 10px;
      }
      .phonerow{
        flex-direction: column;
      }
    }
  }
}
</style>