<template>
    <el-dialog :visible.sync="show" title="复制给同事" width="600px">
        <el-form>
            <el-form-item label="要复制的同事">
                <tMemberSelect v-model="params.user_ids" multiple/>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="submiting">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect.vue'
export default {
    components: {
        tMemberSelect
    },
    data(){
        return {
            show: false,
            submiting: false,
            successFn: null,
            params: {}
        }
    },
    methods: {
        open(params){
            this.params = {
                user_ids: [],                        //同事id
                ids: String(params.ids)             //客户ids
            };
            this.show = true;
            return this;
        },
        onSuccess(fn){
            fn && (this.successFn = fn);
            return this;
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(this.params.user_ids.length == 0){
                this.$message.warning('请选择要复制的同事');
                return;
            }

            this.submiting = true;
            const params = this.params;
            params.user_ids = params.user_ids.join(',');
            try{
                const res = await this.$http.Transferredcustomerscopy(this.params);
                this.submiting = false;
                if(res.status == 200){
                    this.$message.success('复制给同事成功');
                    this.show = false;
                    this.successFn && this.successFn();
                }
            }catch(e){
                this.submiting = false;
            }
        }
    }
}
</script>
<style lang="scss" scoped>
    
</style>