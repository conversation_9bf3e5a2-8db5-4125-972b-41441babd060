<template>
  <div class="login-container">
    <div class="login-box">
      <div class="ctn-img">
        <img src="https://img.tfcs.cn/backup/static/t%2B/tadd.png" alt="" />
      </div>
      <div class="l-r-form">
        <div class="change-box div row">
          <div class="change-item" :class="{ isactive: item.id == is_change }" @click="onClickChange(item)"
            v-for="item in change_box" :key="item.id">
            {{ item.name }}
          </div>
        </div>
        <el-form v-if="is_change == 1" :model="ruleForm2" :rules="rules2" status-icon ref="ruleForm2"
          label-position="left" label-width="0px" class="demo-ruleForm login-page">
          <el-form-item prop="username">
            <el-input type="text" v-model="ruleForm2.username" auto-complete="off" placeholder="请输入企业名称"></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input type="password" v-model="ruleForm2.password" auto-complete="off" placeholder="请输入密码"></el-input>
          </el-form-item>
          <!-- <el-form-item prop="captcha">
            <div class="captcha-box div row">
              <el-input
                style="width:150px"
                type="text"
                v-model="ruleForm2.captcha"
                auto-complete="off"
                placeholder="验证码"
              ></el-input>
              <img
                style="width:150px;height:50px;margin-left:10px"
                @click="changeCaptcha"
                :src="
                  ` https://yun.tfcs.cn/api/auth/admin/login/captcha?time=${captchaImg}`
                "
                alt
              />
            </div>
          </el-form-item> -->

          <!-- <valid-code :value.sync="validCode"></valid-code> -->
          <el-form-item>
            <el-button type="primary" style="width: 100%; height: 50px" @click="handleSubmit">登录</el-button>
          </el-form-item>
          <el-form-item style="text-align: end">
            <el-link type="primary">忘记密码？</el-link>
          </el-form-item>
        </el-form>
        <div class="login-page" v-else>
          <div class="code-desc">
            <div class="t">打开<span>企业微信</span></div>
            <div class="t">扫一扫快捷登录</div>
          </div>
          <img class="erweima" src="" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import axios from "axios";
// import validCode from "@/components/validCode";
// import { mapMutations } from "vuex";

//先引入登录接口
import { mapState, mapMutations } from "vuex";
export default {
  data() {
    return {
      //登录状态
      isLogin: false,
      // 加载状态
      logining: false,
      ruleForm2: {
        // 测试账号
        username: "",
        // 测试密码
        password: "",
        // captcha: "",
      },
      rules2: {
        username: [
          {
            required: true,
            message: "请输入企业名称",
            trigger: "blur",
          },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        // captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      // https://fenxiao.zaodaoxiao.com/api/auth/admin/login/captcha
      imgUrl: "",
      captchaImg: null,
      change_box: [{ id: 1, name: "账号登录" }],
      is_change: 1,
      r_url: "",
    };
  },
  mounted() {
    let website_id = this.getUrlKey("website_id");
    if (website_id) {
      localStorage.setItem("website_id", website_id);
    }
    // this.changeCaptcha();
  },
  computed: {
    ...mapState(["website_info"]),
  },
  components: {
    // validCode
  },
  methods: {
    ...mapMutations(["setSlideTopMenu"]),

    // 获取路径中的参数（code等）
    getUrlKey(name) {
      let url = window.location.href;
      let par = url.split("?")[1] || "";
      let p = par.split("&") || [];
      let res = "";
      p.map((item) => {
        if (item.includes(name)) {
          res = item.split("=")[1];
        }
        // obj[item.split("=")[0]] = item.split("=")[1]
      });
      return parseInt(res);
      // return (
      //   decodeURIComponent(
      //     (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|/#|;|$)").exec(
      //       location.href
      //     ) || ["", ""])[1].replace(/\+/g, "%20")
      //   ) || null
      // );
    },
    // 截取地址栏参数
    handleSubmit() {
      let website_id = localStorage.getItem("website_id");
      this.$refs.ruleForm2.validate((valid) => {
        if (valid) {
          this.logining = true;
          if (
            // 测试账号
            this.ruleForm2.username === "" ||
            // 测试密码
            this.ruleForm2.password === ""
          ) {
            this.logining = false;
            this.$alert("用户名或密码输入错误", "警告", {
              confirmButtonText: "ok",
            });
            this.isLogin = false;
          } else {
            // let token = "Bearer " + localStorage.getItem("TOKEN");
            this.$http
              .setCompanyLogin({
                website_id: website_id,
                name: this.ruleForm2.username,
                password: this.ruleForm2.password,
                // captcha: this.ruleForm2.captcha,
              })
              .then((res) => {
                if (res.status === 200) {
                  //登陆成功后调用第2步store里面的login方法，并将username传递过去，并跳转到home主页面
                  localStorage.setItem("company_token", res.data.token);
                  this.$message({
                    message: "登录成功！",
                    type: "success",
                  });
                  this.$goPath("company_property_list");
                } else {
                  this.changeCaptcha();
                }
              });
            // this.$router.push({ path: "/home" });
          }
        } else {
          this.$message({
            message: "请输入内容后提交",
            type: "error",
          });
          return false;
        }
      });
    },
    changeCaptcha() {
      this.captchaImg = parseInt(Math.random() * (10000 + 1), 10);
    },
    onClickChange(e) {
      // if (e.id == 2) {
      //   this.$message.error("暂未开放扫码登录");
      //   return;
      // }
      this.is_change = e.id;
    },
  },
};
</script>

<style scoped lang="scss">
.login-container {
  width: 100%;
  height: 100%;
  position: fixed;
  background-image: url("https://img.tfcs.cn/backup/static/t%2B/bg.jpg");
  background-size: 100% 100%;

  .title {
    margin-bottom: 44px;
  }
}

.l-r-form {
  width: 408px;
  background: #fff;
  text-align: center;
  border-radius: 4px;
  position: relative;
  height: 533px;
}

.login-page {
  padding: 48px;
  margin-top: 60px;
  padding-top: 0;

  .code-desc {
    text-align: center;
    font-size: 14px;

    .t {
      span {
        color: #2d84fb;
      }
    }
  }

  .erweima {
    width: 180px;
    height: 180px;
    text-align: center;
    margin-top: 40px;
  }
}

.el-input {
  width: 312px;
}

/deep/.el-input__inner {
  height: 50px;
  line-height: 50px;
}

label.el-checkbox.rememberme {
  margin: 0px 0px 15px;
  text-align: left;
}

.captcha-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-box {
  display: flex;
  position: fixed;
  left: 50%;
  top: 50%;
  align-items: center;
  transform: translate(-50%, -50%);
}

.el-form-item {
  margin-bottom: 24px;
}

.ctn-img {
  margin-right: 230px;
}

.change-box {
  justify-content: center;
  margin-top: 40px;
  font-size: 24px;
  padding: 0 64px;
  color: #333;
  cursor: pointer;

  .change-item {
    font-weight: 400;

    &.isactive {
      font-weight: bold;
    }
  }
}
</style>
