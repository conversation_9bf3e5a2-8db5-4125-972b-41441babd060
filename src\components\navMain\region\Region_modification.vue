<template>
    <div class="expansion">
        <div class=" higher_ups">
            <!-- <span class="span1">返回上级</span>
            <span class="span1"> / </span> -->
            <span class="span2">新增拓客地图</span>
        </div>
        <div class="expansion_map">
            <div class="marking_map">
                <div class="search_for">
                    <div style="width: 240px;">
                        <!-- <el-input prefix-icon="el-icon-search" placeholder="请输入内容" v-model="input3"
                            class="input-with-select">
                        </el-input>
                    </div>
                    <div class="search_btn">
                        <el-button type="primary">搜索</el-button> -->
                    </div>
                    <div class="sign_icon">
                        <div class="sign">
                            <i class="el-icon-location-outline"></i>
                            <div>
                                <span @click="Mark_Location">标记位置</span>
                            </div>
                        </div>
                        <div class="sign">
                            <i class="el-icon-location-outline"></i>
                            <div>
                                <span @click="draw">绘制区域</span>
                            </div>
                        </div>
                        <div class="sign">
                            <i class="el-icon-location-outline"></i>
                            <div>
                                <span class="del" @click="del">删除</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="plat">
                    <div id="bai-du-map"></div>
                </div>
                <div class="Historical ">
                    <div>
                        <!-- <span>查看历史标注范围</span> -->
                    </div>
                    <div class="preserve">
                        <div @click="again">重新绘制</div>
                        <el-button type="primary" @click="mapsave">保存</el-button>
                    </div>
                </div>
            </div>
            <div class="marking_position">
                <div class="textbox">
                    <div class="position">
                        <div class="tagging">标注位置</div>
                        <div style="width: 350px;" class="input_position">
                            <el-input v-model="input" placeholder="标注范围经纬度"></el-input>
                        </div>
                    </div>
                    <div class="position">
                        <div class="tagging">区域名称</div>
                        <div style="width: 350px;" class="input_position">
                            <el-input v-model="coordinates.name" placeholder="在这里输入"></el-input>
                        </div>
                    </div>
                    <!-- <div class="position">
                        <div class="tagging">负责人 </div>
                        <div style="width: 350px;" class="input_position1">
                            <el-select v-model="select" slot="prepend" placeholder="请选择"> -->
                    <!-- <el-option label="餐厅名" value="1"></el-option>
                                <el-option label="订单号" value="2"></el-option>
                                <el-option label="用户电话" value="3"></el-option> -->
                    <!-- </el-select>
                        </div>
                    </div> -->
                    <!-- <div class="position">
                        <div class="tagging">楼盘选择</div>
                        <div style="width: 350px;" class="input_position">
                            <el-select v-model="select" slot="prepend" placeholder="请选择"> -->
                    <!-- <el-option label="餐厅名" value="1"></el-option>
                                <el-option label="订单号" value="2"></el-option>
                                <el-option label="用户电话" value="3"></el-option> -->
                    <!-- </el-select>
                        </div>
                    </div> -->
                    <div class="position">
                        <div class="tagging">红包开启</div>
                        <div>
                            <el-switch v-model="coordinates.status"></el-switch>
                        </div>
                    </div>
                </div>
                <div class="accomplish">
                    <div><el-button @click="cancellation">取消</el-button></div>
                    <div><el-button type="warning" @click="complete">完成</el-button></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'TdtMap',
    data() {
        return {
            input: "",
            // input1:'',
            input3: '',
            select: "",
            // value1: false,
            map: null,
            mouseTool: null,
            overlays: [],
            auto: null,
            placeSearch: null,
            config: {
                showLabel: true,
                color: "blue", weight: 3, opacity: 0.5, fillColor: "#FFFFFF", fillOpacity: 0.5
            },
            handler: {},
            coordinates: []
        }
    },
    methods: {
        // 初始化天地图
        initTdtMap() {
            var T = window.T
            // console.log(T);
            this.tdtMap = new T.Map('bai-du-map');
            //设置显示地图的中心点和级别
            this.tdtMap.centerAndZoom(new T.LngLat(117.163139, 35.079158), 12);
            // 7.创建坐标，通常是调取接口获得经纬度
            // const point = new T.LngLat(112.9388, 28.2280);
            // 8.创建覆盖使用的图标
            // const icon = new T.Icon({
            //     iconUrl: '../marker-red.png',
            //     iconSize: new T.Point(27, 27),
            //     iconAnchor: new T.Point(10, 25)
            // });
            // 9. 创建在该坐标上的一个图像标注实例
            // const marker = new T.Marker(point, icon);
            // 10.将覆盖物添加到地图中，一个覆盖物实例只能向地图中添加一次
            // this.tdtMap.addOverLay(marker);

            this.handler = new T.PolygonTool(this.tdtMap, this.config);
        },
        Mark_Location() {
            var T = window.T
            console.log(this.handler);
            if (this.handler) this.handler.close();
            this.handler = new T.MarkTool(this.tdtMap, { follow: true });
            this.handler.open();
            this.handler.addEventListener('mouseup', this.drawFinish)
        },
        draw() {
            var T = window.T
            // console.log(this.handler);
            if (this.handler) this.handler.close();
            this.handler = new T.PolygonTool(this.tdtMap);
            console.log(this.handler);
            this.handler.open();
            this.handler.addEventListener('draw', this.drawFinish)
        },
        drawFinish(e) {
            //当前位置
            console.log(e.currentLnglat);
            console.log("顶点坐标列表 ----------> ", e.currentLnglat)
            //画范围坐标
            this.coordinates.range = e.currentLnglat
            console.log(this.coordinates);
        },
        del() {
            this.handler.close();
            this.handler.clear()
        },
        again() {
            this.handler.clear()
            this.handler.open();
        },
        //保存
        mapsave() {
            console.log(Array.from(this.coordinates));
            let mapsave = []
            this.coordinates.map(item => {
                // console.log(item.lat);
                // console.log(item.lng);
                mapsave.push(item.lat)
                mapsave.push(item.lng)
            })
            this.input = mapsave.join(",")
            // console.log(this.input);
        },
        complete() {
            console.log(this.coordinates);
      
            let params = Object.assign({},this.coordinates)
            if(params.status==false){
                params.status=0
            }else{
                params.status=1
            }
            this.$http.Modify_Area(params).then(res=>{
                console.log(res);
                if(res.status==200){
                    this.$message({
                        type:"success",
                        message:"修改成功"
                    })
                    this.$goPath(`map_mode`)
                }
            })
        },
        //取消
        cancellation() {
            this.input = ""
            this.coordinates.name = ""
            this.coordinates.status = false
        }
    },
    created() {
        // eslint-disable-next-line no-undef
        eventBus.$on("reg1", (item) => {
            console.log(item.range);
            if(item.status==1){
                item.status=true
            }else{
                item.status=false
            }
            var data=[]
            item.range.map(item=>{
                console.log(item);
                data.push(item.lat)
                data.push(item.lng)
            })
            this.input=data.join(",")
            this.coordinates=item
            console.log(this.coordinates);
        });
    },
    mounted() {
        this.initTdtMap()
    },
    beforeDestroy() {
        // eslint-disable-next-line no-undef
        eventBus.$off("reg1");
    },
}
</script>
<style scoped lang="scss" >
.expansion {
    background: #f1f4fa;
    margin: -14px;
    padding: 24px;

    .higher_ups {
        width: 200px;
        height: 30px;
        margin-left: 24px;
        display: flex;
        justify-content: space-between;

        .span1 {
            color: #8A929F;
        }

        .span2 {
            color: #2D84FB;
        }
    }
}

.expansion_map {
    width: 97%;
    height: 710px;
    margin: 20px auto;
    // background-color: aquamarine;
    // margin-top: 20px;
    display: flex;
    justify-content: space-between;

    .marking_map {
        width: 900px;
        height: 700px;
        background-color: #FFFFFF;
        overflow: hidden;
        border-radius: 4px;

        .search_for {
            width: 90%;
            height: 50px;
            // background-color: coral;
            margin: 20px auto;
            display: flex;

            /deep/.el-input__inner {
                height: 32px;
            }

            /deep/.el-input__icon {
                line-height: 33px;
            }

            .el-button {
                width: 76px;
                height: 36px;
                margin-left: 10px;
                line-height: 0;
            }

            .sign_icon {
                width: 500px;
                height: 24px;
                display: flex;
                justify-content: flex-end;

            }

            .sign {
                width: 90px;
                height: 25px;
                display: flex;
                justify-content: space-around;

                i {
                    font-size: 25px;
                    color: #F1F4FA;
                }

                div {
                    font-size: 12px;
                    color: #8A929F;
                    margin-top: 5px;
                    cursor: pointer;
                }

                .del {
                    margin-left: -25px;
                }
            }
        }

        .plat {
            width: 800px;
            height: 500px;
            background-color: rgba(209, 105, 31, 0.485);
            margin: 0 auto;
        }

        .Historical {
            width: 800px;
            height: 40px;
            // background-color: #8A929F;
            margin: 40px auto;
            display: flex;
            justify-content: space-between;

            span {
                font-size: 14px;
                color: #2D84FB;
            }

            .preserve {
                width: 150px;
                height: 35px;
                display: flex;

                div {
                    margin-right: 20px;
                    line-height: 30px;
                    font-size: 14px;
                    color: #8A929F;
                    cursor: pointer
                }

                .el-button {
                    width: 65px;
                    height: 32px;
                    line-height: 0;
                }
            }
        }
    }

    .marking_position {
        width: 600px;
        height: 700px;
        background-color: #FFFFFF;
        border-radius: 4px;

        .textbox {
            width: 80%;
            height: 400px;
            margin: 25px auto;
            line-height: 80px;

            // background-color: rgba(97, 145, 235, 0.512);
            .position {
                display: flex;

                .el-switch {
                    margin-left: 30px;
                }

                .tagging {
                    line-height: 80px;
                    color: #2E3C4E;
                    font-size: 14px;

                }

                .input_position {
                    margin-left: 30px;

                    .el-select {
                        width: 350px;
                    }
                }

                .input_position1 {
                    margin-left: 44px;

                    .el-select {
                        width: 350px;
                    }
                }
            }
        }

        .accomplish {
            width: 80%;
            height: 35px;
            margin: 200px auto;
            display: flex;
            justify-content: space-between;

            .el-button {
                width: 175px;
                height: 35px;
                line-height: 0;
            }

        }
    }

    #bai-du-map {
        overflow: hidden;
        width: 100%;
        height: 100%;
        margin: 0;
        font-family: "微软雅黑";

        /* 隐藏高德logo  */
        .amap-logo {
            display: none !important;
        }

        /* 隐藏高德版权  */
        .amap-copyright {
            display: none !important;
        }
    }

}
</style>