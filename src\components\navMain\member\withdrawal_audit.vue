<template>
  <el-container>
    <el-header>
        <new_tips_list :tipsList="tips_list"></new_tips_list>
    </el-header>
    <div class="eldiv" v-loading="is_table_loading">
      <div class="browse-table">
        <div class="browse div row">
          <div class="browse-box div row">
            <div
              class="browse-item"
              v-for="(item, index) in time_array"
              :key="index"
              :class="{ browse_active: item.value === list_params.date_str }"
              @click="onClickBrowse(index, item.id)"
            >
              {{ item.desc }}
            </div>
          </div>
          <div class="div row">
            <el-input
              v-model="params.real_name"
              placeholder="请输入收款人名称"
              @input="onInput"
              @change="onChangeName"
            ></el-input>
            <el-button type="primary" icon="el-icon-search" @click="search"
              >搜索</el-button
            >
          </div>
        </div>
        <div class="block-time div row" v-if="isCustomize">
          <el-date-picker
            v-model="list_params.start"
            type="date"
            placeholder="请选择开始日期"
            value-format="yyyy-MM-dd"
          >
            >
          </el-date-picker>
          <el-date-picker
            v-model="list_params.end"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结束日期"
          >
            >
          </el-date-picker>
          <el-button type="primary" @click="clickTime">查询</el-button>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-purple div row">
              <div class="left ">
                <p>提现申请总额</p>
                <p class="desc">用户提现申请的总金额（元）</p>
              </div>
              <div class="right">{{ withdrawal_info.amount }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple div row">
              <div class="left ">
                <p>已审批金额</p>
                <p class="desc">已通过审批的提现总金额(元)</p>
              </div>
              <div class="right">{{ withdrawal_info.pass_amount }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple div row">
              <div class="left ">
                <p>未审批金额</p>
                <p class="desc">未通过审批的提现总金额(元)</p>
              </div>
              <div class="right">
                {{ withdrawal_info.reject_amount }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-select
        style="margin:0 10px 10px"
        @change="onChange"
        v-model="params.audit_status"
        placeholder="请选择审核状态"
      >
        <el-option
          v-for="item in audit_list"
          :key="item.value"
          :label="item.description"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </div>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :currentPage="params.currentPage"
          :pagesize="params.pagesize"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="60%">
      <el-form label-width="100px" :model="form">
        <el-form-item label="审核状态：">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="item in audit_list"
              :key="item.value"
              :label="item.value"
              >{{ item.description }}</el-radio
            >
          </el-radio-group>
          <el-button
            style="margin-left:30px;border-radius:4px;"
            type="primary"
            @click="onAudit"
            size="mini"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import new_tips_list from "@/components/components/new_tips_list"
export default {
  name: "withdrawal_audit",
  components: {  myPagination, myTable,new_tips_list},
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        real_name: "",
        audit_status: "",
      },
      form: {
        id: "",
        status: 1,
      },
      dialogVisible: false,
      audit_list: [
        { value: 1, description: "审核通过" },
        { value: 2, description: "审核不通过" },
      ],
      time_array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
        { desc: "自定义", value: "customize", id: 8 },
      ],
      list_params: {
        date_str: "day",
        start: "",
        end: "",
      },
      isCustomize: false,
      withdrawal_info: {},
      options: [
        { value: 1, desc: "bank_card_no", name: "银行卡号" },
        { value: 2, desc: "bank_name", name: "银行名称" },
      ],
      tips_list: ["可以浏览查看提现申请的详细信息"],
      table_header: [
        { prop: "id", label: "提现ID" },
        {
          label: "会员详情",
          formatter: (row) => {
            let audit_status = row.audit_status;
            if (audit_status == 0) {
              return (audit_status = "未审核");
            }
            if (audit_status == 1) {
              return (audit_status = "审核通过");
            }
            if (audit_status == 2) {
              return (audit_status = "审核未通过");
            }
          },
        },
        {
          label: "提现金额/元",
          render: (h, data) => {
            return <el-tag type="danger">{data.row.amount}元</el-tag>;
          },
        },
        {
          label: "预计到账/元",
          render: (h, data) => {
            return (
              <el-tag class="scope-success">
                {data.row.remittance_amount}元
              </el-tag>
            );
          },
        },
        {
          label: "提现费率",
          render: (h, data) => {
            return <div>{data.row.fee}%</div>;
          },
        },
        {
          label: "手续费/元",
          prop: "fee_amount",
        },
        { prop: "bank_name", label: "收款银行" },
        { prop: "bank_card_no", label: "收款账号" },
        { prop: "real_name", label: "收款人" },
        { prop: "created_at", label: "申请时间" },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return this.$hasShow("编辑提现审核") ? (
              <el-button
                icon="el-icon-edit"
                type="success"
                size="mini"
                onClick={() => {
                  this.editData(data.row);
                }}
              >
                编辑
              </el-button>
            ) : (
              ""
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getAuditData();
    this.getWithdrawalInfo();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.pagesize;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getAuditData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getAuditData();
    },
    getAuditData() {
      if (!this.params.audit_status) {
        delete this.params.audit_status;
      }
      this.$http.getAuditWithdrawalList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    formatAudit(row) {
      let audit_status = row.audit_status;
      [
        { id: 0, name: "未审核" },
        { id: 1, name: "审核通过" },
        { id: 2, name: "审核未通过" },
      ].findIndex((item) => {
        item.id === audit_status;
        return item.name;
      });
    },
    editData(row) {
      this.form.id = row.id;
      this.dialogVisible = true;
    },
    onAudit() {
      if (this.form.id) {
        this.$http.updateAuditWithdrawal(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.dialogVisible = false;
          }
          this.getAuditData();
        });
      }
    },
    // 点击选择日期数据
    onClickBrowse(index, id) {
      this.list_params.date_str = this.time_array[index].value;
      if (id === 8) {
        this.isCustomize = true;
      } else {
        this.isCustomize = false;
        this.list_params.start = "";
        this.list_params.end = "";
        this.getWithdrawalInfo();
      }
    },
    getWithdrawalInfo() {
      this.$http.getWithdrawalData(this.list_params).then((res) => {
        if (res.status === 200) {
          this.withdrawal_info = res.data;
        }
      });
    },
    clickTime() {
      if (!this.list_params.start) {
        this.$message({
          message: "请选择开始时间",
          type: "error",
        });
      } else if (!this.list_params.end) {
        this.$message({
          message: "请选择结束时间",
          type: "error",
        });
      } else {
        this.getWithdrawalInfo();
      }
    },
    onInput(e) {
      this.params.real_name = e;
      if (!this.params.real_name) {
        this.params.page = 1;
        this.getAuditData();
      }
    },
    onChangeName() {
      this.search();
    },
    onChange(e) {
      this.params.audit_status = e;
      this.params.page = 1;
      this.getAuditData();
    },
    search() {
      this.params.page = 1;
      this.getAuditData();
    },
    tableRowClassName(row) {
      if (row.row.audit_status === 1) {
        return "success-row";
      }
    },
  },
};
</script>

<style lang="scss">
// 标签
// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;
  .browse {
    justify-content: space-between;
    .browse-box {
      width: 480px;
      align-items: center;
      .browse-item {
        margin: 0 5px;
        font-size: 14px;
        padding: 2px 10px;
        border-radius: 50px;
        color: #333;
        &.browse_active {
          color: #fff;
          background: #0068e6;
        }
      }
    }
  }
  .block-time {
    justify-content: flex-start;
    margin: 10px;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
  }
  .bg-purple {
    border: 1px dashed #d3dce6;
  }
  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;
    align-items: center;
    .left {
      color: #999;
      font-size: 14px;
      text-align: start;
      p {
        color: #333;
      }
      .desc {
        color: #999;
      }
    }
    .right {
      color: #26bf8c;
    }
  }
}
.title {
  color: #333;
  font-weight: bold;
}
.eldiv {
  margin-top: 15px;
  .title {
    margin: 10px 0;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
}
.el-footer {
  margin: 0 auto;
}
</style>
