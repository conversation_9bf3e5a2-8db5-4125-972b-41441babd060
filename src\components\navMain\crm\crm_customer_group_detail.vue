<template>
  <!-- 客户群详情 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm div row">
          <div class="c-c-g-d-left">
            <div class="t div row">
              <span>{{ detail.name }}</span>
              <span v-if="false" class="label">正常</span>
            </div>
            <div class="t" style="margin-top: 24px">
              <span class="text">群主：{{ detail.owner }}</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">共<span class="blue">{{ detail.group_count || 0 }}</span>人</span>
            </div>
            <div class="t" style="margin-top: 24px">
              <span class="text">创建时间：{{ detail.create_time }}</span>
            </div>
          </div>
          <div class="c-c-g-d-right">
            <span class="text">群公告：</span>
            <div class="content">
              {{ detail.notice || "暂无公告内容！" }}
            </div>
          </div>
        </div>
        <div class="content-box-crm" style="margin-top: 24px">
          <el-tabs v-model="is_tabs">
            <el-tab-pane v-for="(item, index) in tabs" :key="index" :label="item.label" :name="item.name">
              <div class="data-box div row">
                <div class="data-item" style="
                    background-image: url('https://img.tfcs.cn/backup/static/admin/customer/dqq.png?x-oss-process=style/w_1200');
                  ">
                  <div class="number">{{ detail.group_count || 0 }}人</div>
                  <div class="number2">当前群人数</div>
                </div>
                <div class="data-item" style="
                    background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrjq.png?x-oss-process=style/w_1200');
                  ">
                  <div class="number">{{ detail.add_group || 0 }}人</div>
                  <div class="number2">今日进群人数</div>
                </div>
                <div class="data-item" style="
                    background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrtq.png?x-oss-process=style/w_1200');
                  ">
                  <div class="number">{{ detail.out_group || 0 }}人</div>
                  <div class="number2">今日退群人数</div>
                </div>
              </div>
              <div class="title-g-d">群成员</div>
              <div class="table-top-box div row" style="margin-top: 24px">
                <div class="t-t-b-left div row">
                  <span class="text">共</span>
                  <span class="blue">{{ params.total }}</span>
                  <span class="text">人</span>
                  <span class="text" style="margin: 0 12px">|</span>
                  <span class="text">已选</span>
                  <span class="blue">{{ multipleSelection.length }}</span>
                  <span class="text">人</span>
                </div>
                <div class="t-t-b-right div row"></div>
              </div>
              <el-table v-loading="is_table_loading" :data="tableData" border
                :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column label="群成员" v-slot="{ row }">
                  <el-link type="primary">{{ row.name }}</el-link>
                  <span v-if="row.type == 1" style="color:#ffbe3a">@企微</span>
                  <span v-else style="color:#1aad19">@微信</span>
                </el-table-column>
                <el-table-column label="类型" v-slot="{ row }">
                  {{ row.type == 1 ? "企业成员" : "外部联系人" }}
                </el-table-column>
                <el-table-column label="入群时间" prop="join_time"></el-table-column>
                <el-table-column label="入群方式" v-slot="{ row }">
                  {{ row.join_scene | filterJoin }}
                </el-table-column>
                <el-table-column label="邀请人" v-slot="{ row }">
                  {{ row.invitor }}
                </el-table-column>
              </el-table>
              <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
                :total="params.total" :page-size="params.per_page" :current-page="params.page"
                @current-change="onPageChange">
              </el-pagination>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "crm_customer_group_detail",
  data() {
    return {
      is_tabs: "info",
      detail: {},
      tabs: [
        { name: "info", label: "基本信息" },
        // { name: "logs", label: "操作日志" },
      ],
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      multipleSelection: [],
      detail_id: "",
      chatid: "",
    };
  },
  filters: {
    filterJoin(e) {
      let arr = [
        { id: 1, name: "直接邀请" },
        { id: 2, name: "邀请链接" },
        { id: 3, name: "二维码" },
      ];
      const form = arr.find((item) => {
        return item.id == e;
      });
      return form.name;
    },
  },
  mounted() {
    this.detail_id = this.$route.query.id;
    this.getDataDetail();
  },
  methods: {
    getDataDetail() {
      this.$http.getCrmCustomerGroupDeatail(this.detail_id).then((res) => {
        if (res.status === 200) {
          this.detail = res.data;
          this.chatid = res.data.chatid;
          this.getDataList();
          console.log(this.chatid);
        }
      });
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getCrmCustomerGroupList(this.chatid, this.params)
        .then((res) => {
          console.log(res);
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        });
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .title-g-d {
    margin-top: 24px;
    color: #2e3c4e;
    font-size: 14px;
  }

  .c-c-g-d-left {
    width: 69%;
    border-right: 1px dashed #eee;

    .t {
      align-items: center;

      span {
        font-size: 18px;
        color: #2e3c4e;
      }

      .label {
        padding: 4px;
        color: #8a929f;
        background: #f1f1f1;
        font-size: 11px;
        margin-left: 14px;
      }

      .text {
        color: #8a929f;
        font-size: 14px;

        .blue {
          font-size: 14px;
          color: #2d84fb;
        }
      }
    }
  }

  .c-c-g-d-right {
    width: 31%;
    border-radius: 4px;
    background: #e8f1ff;
    border: 1px solid #2d84fb;
    margin-left: 24px;
    padding: 24px;

    .text {
      font-size: 14px;
      font-size: #2e3c4e;
    }

    .content {
      text-align: center;
      font-size: 16px;
    }
  }
}

.data-box {
  align-items: center;
  justify-content: space-between;

  .data-item {
    display: flex;
    flex-direction: column;
    width: 25%;
    justify-content: center;
    padding-left: 60px;
    height: 140px;
    border-radius: 10px;
    background-size: 100% 100%;
    color: #fff;
    background-repeat: no-repeat;

    .number {
      font-size: 36px;
    }

    .number2 {
      font-size: 18px;
      margin-top: 6px;
    }
  }
}
</style>
