<template>
    <div v-fixed-scroll="47">
      <div class="head-check">
        <div class="head-list">
          <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
            @change="onChangeTime">
          </el-date-picker>
        </div>
        <div class="head-list">
          <el-input v-model="params.title" placeholder="请输入标题" size="small"
          @change="onSubmit"></el-input>
        </div>
        <div class="head-list">
          <el-select size="small" v-model="params.ies_uid_from" placeholder="请选择账号"
          clearable
          @change="onSubmit">
            <el-option
              v-for="item in account_list"
              :key="item.id"
              :label="item.name+item.ies_uniq_id"
              :value="item.ies_uid">
              <span>{{item.name}}</span><small class="option-room-name">{{item.ies_uniq_id}}</small>
            </el-option>
          </el-select>
        </div>
      </div>
        <div>
            <el-table
            v-loading="is_table_loading"
              :data="tableData"
              border
              ref="table"
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }"
              @sort-change="handerListSort">
              <el-table-column
                prop="title "
                label="标题"
                fixed
                min-width="300"
                v-slot="{ row }"
                > 
                  <el-tooltip class="item" placement="top" effect="light">
                    <div slot="content" class="huanhang">{{row.title?row.title:'--'}}</div>
                    <el-link type="primary" @click="copyCusID(row.play_url)">  
                      <span class="clamp-text">
                        {{row.title?row.title:"--"}}
                    </span></el-link>
                </el-tooltip>
                
              </el-table-column>
              <el-table-column
                prop="comment_count"
                label="评论数"
                v-slot="{ row }"
                sortable='custom'
                width="90"
                align="center">
                {{row.comment_count?row.comment_count:"--"}}
              </el-table-column>
              <el-table-column
                prop="digg_count"
                label="点赞数"
                v-slot="{ row }"
                width="90"
                align="center"
                sortable='custom'>
                {{row.digg_count?row.digg_count:"--"}}
              </el-table-column>
              <el-table-column
                prop="share_count"
                label="分享数"
                v-slot="{ row }"
                width="90"
                align="center"
                sortable='custom'>
                {{row.share_count?row.share_count:"--"}}
              </el-table-column>
              <el-table-column
                prop="play_count"
                label="播放数"
                v-slot="{ row }"
                sortable='custom'
                align="center"
                width="90">
                {{row.play_count?row.play_count:"--"}}
              </el-table-column>
              <el-table-column
                prop="add_time"
                label="发布时间"
                min-width="170"
                align="center">
              </el-table-column>
              <el-table-column 
              prop="latest_comment_content"
              label="最新评论内容"
              min-width="260"
              align="center"
              v-slot="{ row }">
                <el-tooltip class="item" placement="top" effect="light">
                  <div slot="content" class="huanhang" style="width: 200px;">
                    {{row.latest_comment_content?row.latest_comment_content:'--'}}</div>
                  <span class="clamp-text">
                    {{row.latest_comment_content?row.latest_comment_content:"--"}}
                  </span>
                </el-tooltip>
              </el-table-column>
              <el-table-column
                label="最新评论时间"
                prop="latest_comment_time"
                min-width="170"
                v-slot="{ row }"
                align="center">
                    {{row.latest_comment_time?row.latest_comment_time:"--"}}
              </el-table-column>
              <el-table-column
                prop="ies_uniq_id"
                label="账号"
                min-width="220"
                v-slot="{ row }"
                align="center">
                  <div class="avatar">
                  <div>{{row.name?row.name:"--"}}</div>
                </div>
                <div class="douyin">
                    {{ row.ies_uniq_id? row.ies_uniq_id:"--"}}
                  </div>
              </el-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                v-slot="{ row }"
                min-width="200"
                align="center">
                <el-link  type="primary" :underline="false"
                @click="viewcomments(row)">评论</el-link>
                <el-link type="warning" :underline="false" style="margin-left:10px;"
                @click="lookuser(row)">用户</el-link>
              </el-table-column>
            </el-table> 
            <div class="tab-content-footer">
              <div>
                  <el-button type="primary" size="small" @click="empty">清空</el-button>
              </div>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="params.per_page"
                    :current-page="params.page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total">
                  </el-pagination>
            </div>
        </div>
        <allcomments ref="allcomments" v-if="dialogs.allcomments"/>
    </div>
</template>
<script>
import allcomments from "./allcomments.vue"
export default {
  components:{
    allcomments
  },
  props:{
    account_list:{
      type:Array,
      default:() => []
    }
  },
    data() {
        return {
            params:{
                page: 1,
                per_page: 10,
            },
            tableData:[],//表格数据
            is_table_loading:false,
            timeValue:"",//时间检索
            pickerOptions: {
              shortcuts: [{
                text: '今天',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '昨天',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setDate(start.getDate() - 1);
                  end.setDate(start.getDate());
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本周',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上周',
                onClick(picker) {
                    const today = new Date(); // 获取当前日期
                    const end = new Date(today); // 结束日期为当前日期
                    const start = new Date(today); // 开始日期为当前日期
                    const day = today.getDay(); // 获取当前是星期几
                    const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
                    const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
                    end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
                    // end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
                    start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
                    // start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
                    // 将计算得到的时间范围传递给日期选择器
                    picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上月',
                onClick(picker) {
                  const end = new Date();
                  end.setDate(0); // 获取上个月的最后一天
                  end.setHours(23, 59, 59, 0);
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              },]
            },
            dialogs: {
              allcomments: false,
			      },
        }
    },
    mounted(){
        let pagenum = localStorage.getItem( 'pagenum')
    	  this.params.per_page = Number(pagenum)||10
        this.getList()
    },
    methods:{
        getList(){
          this.is_table_loading = true
            this.$http.getshortvideo(this.params).then(res=>{
                if(res.status==200){
                    this.is_table_loading = false
                    this.tableData = res.data.data
                    this.params.total = res.data.total
                }
            })
        },
        onChangeTime(e) {
          this.params.start_date = e ? e[0] : ""; // 赋值开始时间
          this.params.end_date = e ? e[1] : ""; // 赋值结束时间
          this.params.page = 1; // 显示第一页
          this.getList(); // 获取最新数据
        },
        //查看评论
        viewcomments(row){
          this.dialogs.allcomments = true;
			    this.$nextTick(()=>{
			    	this.$refs.allcomments.open(row);
			    })
        },
        //处理排序
		    handerListSort(	{ column, prop, order }){
          console.log(order,"12121212121");
		    	//排序方式降序 1=已留资 2=未留资 3=客资量 4=互动人数 5=评论
		    	if(order == 'ascending'){
		    		this.$refs.table.sort(prop, "descending")
		    		return;
		    	}
        
		    	if(order == 'descending'){
		    		switch(prop){
		    			case 'comment_count':
		    				this.params.order = '1';
		    				break;
		    			case 'digg_count':
		    				this.params.order = '2';
		    				break;
		    			case 'play_count':
		    				this.params.order = '3';
		    				break;
		    			case 'share_count':
		    				this.params.order = '4';
		    				break;
		    		}
		    	}else{
		    		this.params.order = '';
		    	}
		    	this.getList();
		    },
        // 检索单个视频的评论用户
        lookuser(row) {
         this.$emit("room-change",row)
        },
        // 复制用户主页
        copyCusID(id) {
          this.$onCopyValue(id);
        },
        //查询
        onSubmit(){
          this.params.page = 1; // 显示第一页
          this.getList()
        },
        //清空
        empty(){
          this.params = {
                page: 1,
                per_page: 10,
          }
          this.getList()
        },
        //每页 条
        handleSizeChange(val){
            this.params.per_page = val
            this.getList()
        },
        //当前页
        handleCurrentChange(val) {
            this.params.page = val
            this.getList()
        },
    },
}
</script>
<style scoped lang="scss">
.head-check{
  display:flex;
  margin-bottom: 15px;
  .head-list {
    margin-right: 10px;
  }
}
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 15px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
.douyin{
      margin-left: 5px;
      color: #a9a9a9;
      font-size: 13px;
    }
::v-deep{
	.option-room-name{
		color: #a9a9a9;
		margin-left: 12px;
		float: right;
	}
  input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
.clamp-text {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: left;
  }
</style>