<template>
  <div>
    <div class="tips">
      <div>
        1.在二维码处设置的欢迎语会被优先推送，如果成员在二维码处设置了欢迎语，在此设置的欢迎语
        不会生效
      </div>
      <!-- <div>2.一个成员如果被设置了多个欢迎语，将会使用最新设置或修改欢迎语</div>
      <div>
        3.新建欢迎语最多可发送两条消息，图片、小程序、链接三种类型，在1条群发消息中只能发送1种
      </div>
      <div>4.欢迎语1和2不能同时为空，当两者均填写时会收到两条消息</div> -->

      <div>
        2.欢迎语将在客户加为好友后20秒内发下，因网络延可能造成发送不成功
      </div>
    </div>
    <el-form label-width="100px">
      <el-form-item label="使用员工">
        <div class="form-item-block form_kefu flex-row f-wrap">
          <el-button size="big" class="member_button" v-for="item in form_selectedMember" :key="item.id">{{ item.name ||
                      item.user_name }}</el-button>
          <el-button size="big" class="el-icon-plus" @click="showAddMember">添加成员</el-button>
        </div>
      </el-form-item>
      <el-form-item label="欢迎语">
        <welcome-mes style="padding-right: 20px" ref="member_welcome" :value="welcome_mes"></welcome-mes>
      </el-form-item>

      <!-- <el-form-item label="欢迎语">
        <div class="form-item-block">
          <el-input
            v-model="welcome_mes.text.desc"
            style="margin-right: 12px"
            placeholder="请输入欢迎语1"
            type="textarea"
          ></el-input>
        </div>
      </el-form-item> -->

      <!-- <el-form-item label="附件">
        <el-checkbox-group v-model="params_type_arr" size="mini">
          <el-checkbox
            :label="2"
            border
            :class="{ active: params_type == 2 }"
            @change="checkChange($event, 2)"
            >图片</el-checkbox
          >
          <el-checkbox
            :label="5"
            border
            :class="{ active: params_type == 5 }"
            @change="checkChange($event, 5)"
            >视频</el-checkbox
          >
          <el-checkbox
            :label="6"
            border
            :class="{ active: params_type == 6 }"
            @change="checkChange($event, 6)"
            >文件</el-checkbox
          >
          <el-checkbox
            :label="3"
            border
            :class="{ active: params_type == 3 }"
            @change="checkChange($event, 3)"
            >链接</el-checkbox
          >
          <el-checkbox
            :label="4"
            :class="{ active: params_type == 4 }"
            border
            @change="checkChange($event, 4)"
            >小程序</el-checkbox
          >
        </el-checkbox-group>
        <div class="type_tips tips">
          <template
            v-if="params_type == 2 || params_type == 3 || params_type == 4"
          >
            图片仅支持png,jpg格式 最大10M</template
          >
          <template v-if="params_type == 5">
            视频仅支持mp4格式 最大10M</template
          >
          <template v-if="params_type == 6"> 文件最大20M</template>
        </div>
      </el-form-item>
      <template v-if="params_type == 2">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="图片" v-if="welcome_mes.image.pic_url">
          <div class="form-item-block">
            <img :src="welcome_mes.image.pic_url" class="avatar" />
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 3">
        <el-form-item label="跳转路径">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.link.url"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入跳转路径"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="链接标题">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.link.title"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入链接标题"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="链接描述">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.link.desc"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入链接地址"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="封面">
          <div class="form-item-block flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
            >
              <img
                v-if="welcome_mes.link.pic_url"
                :src="welcome_mes.link.pic_url"
                class="avatar"
              />
              <i v-else class="el-icon-plus"></i>
            </el-upload>
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 4">
        <el-form-item label="小程序标题">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.miniprogram.title"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入小程序标题"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="小程序appid">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.miniprogram.appid"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入小程序id"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="跳转路径">
          <div class="form-item-block">
            <el-input
              v-model="welcome_mes.miniprogram.url"
              style="width: 240px; margin-right: 12px"
              placeholder="请输入跳转路径"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="封面">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".jpg,.png"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="封面" v-if="miniCover">
          <div class="form-item-block">
            <img :src="miniCover" class="avatar" />
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 5">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              list-type="picture-card"
              :show-file-list="false"
              accept=".mp4,.AVI,.MPEG,.MOV,.WMV"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="视频" v-if="videoname">
          <div class="form-item-block">
            <video :src="videoname" class="avatar"></video>
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 6">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload
              class="upload-demo"
              :headers="myHeader"
              :action="website_img"
              :on-success="handleSuccess"
              :show-file-list="false"
            >
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>
            <el-button plain type="primary" @click="showSelectDia"
              >素材库上传</el-button
            >
          </div>
          <p class="tip">{{ filename }}</p>
        </el-form-item>

      </template> -->
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>

    <el-dialog :visible.sync="show_add_member" width="600px" title="选择成员" append-to-body
      :before-close="closeSelectMember">
      <div class="member">
        <div class="member_box flex-row">
          <div class="left member_con flex-1">
            <memberList v-if="show_add_member" :list="navList" :defaultValue="selectedIds" @onClickItem="selecetedMember"
              @setchecked="setChecked" value="wx_work_user_id" ref="memberList">
            </memberList>
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择成员</div>
            <div class="selected_list">
              <div class="selected_item flex-row align-center" v-for="item in selectedList" :key="item.id">
                <!-- <div class="prelogo">
                    <img :src="item.prelogo" alt="">
                  </div> -->
                <div class="name flex-1">{{ item.user_name }}</div>
                <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="closeSelectMember">取消</el-button>
          <el-button type="primary" @click="selectMemberOk" :loading="isSubmiting">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_select_dia" width="600px" title="选择附件" append-to-body>
      <div class="imgLists">
        <div class="img_list flex-row">
          <template v-if="params_type == 2 || params_type == 3 || params_type == 4">
            <div class="img" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <img :src="item.url" alt="" />
            </div>
          </template>
          <template v-if="params_type == 5">
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <video :src="item.url"></video>
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
          <template v-if="params_type == 6">
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_select_dia = false">取消</el-button>
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config";
import memberList from "../../crm/components/memberList";
import welcomeMes from "../../crm/components/welcome_mes";
export default {
  props: ["navList", "form"],
  components: {
    memberList,
    welcomeMes,
  },
  data() {
    return {
      form_params: {
        userid: "",
        title: "",
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      cover: "",
      imgList: [],
      show_select_dia: false,
      currentImg: "",
      params_type: 2,
      img_params: {
        page: 1,
        per_page: 20,
        type: 1,
      },
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
          pic_url: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      params_type_arr: [],
      filename: "",
      videoname: "",
      imgname: "",
      miniCover: "",
      type_arr: ["text", "image", "link", "miniprogram", "video", "file"],
      form_selectedIdsBack: [],
      form_selectedMemberBack: [],
    };
  },
  created() {
    console.log(this.form);
    for (const key in this.form) {
      this.form_params[key] = this.form[key];
    }

    this.selectedIds = [];
    this.form_selectedMember = this.form_params.staff;
    // this.form_selectedMemberBack = this.form_params.staff
    // this.selectedIds =
    this.form_params.staff.map((item) => {
      this.selectedIds.push(item.wx_work_user_id);
    });
    this.form_selectedIdsBack = JSON.parse(JSON.stringify(this.selectedIds));
    this.form_params.userid = this.selectedIds.join(",");
    for (const key in this.form_params.welcome) {
      this.$set(
        this.welcome_mes,
        key,
        JSON.parse(JSON.stringify(this.form_params.welcome[key]))
      );
      // this.welcome_mes[key] = JSON.parse(JSON.stringify(this.form_params.welcome))
    }

    if (this.form_params.welcome.image) {
      this.params_type_arr.push(this.form_params.welcome.image.type);
      delete this.welcome_mes.image.name;
    }
    if (this.form_params.welcome.file) {
      this.filename = this.form_params.welcome.file.media_id;
      delete this.welcome_mes.file.name;
      this.params_type_arr.push(this.form_params.welcome.file.type);
    }
    if (this.form_params.welcome.video) {
      this.videoname = this.form_params.welcome.video.url;
      delete this.welcome_mes.video.name;
      delete this.welcome_mes.video.url;
      this.params_type_arr.push(this.form_params.welcome.video.type);
    }
    if (this.form_params.welcome.miniprogram) {
      this.miniCover = this.form_params.welcome.miniprogram.org_url;
      delete this.welcome_mes.miniprogram.name;
      delete this.welcome_mes.miniprogram.org_url;
      this.params_type_arr.push(this.form_params.welcome.miniprogram.type);
    }
    if (this.form_params.welcome.link) {
      delete this.welcome_mes.link.name;
      this.params_type_arr.push(this.form_params.welcome.link.type);
    }
    delete this.welcome_mes.text.name;
    this.params_type_arr_old = [];
    this.params_type_arr.map((item) => {
      this.params_type_arr_old.push(this.type_arr[item - 1]);
    });
    this.selectedList = this.form_selectedMember;
    this.form_selectedMemberBack = JSON.parse(
      JSON.stringify(this.form_selectedMember)
    );
    this.$set(
      this.welcome_mes.text,
      "desc",
      this.form_params.welcome.text.content
    );
    console.log(this.welcome_mes, 11);
    this.$forceUpdate();
  },
  computed: {
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      let type_arr = [];
      this.params_type_arr.map((item) => {
        type_arr.push(this.type_arr[item - 1]);
      });
      // console.log(this.welcome_mes, 12);
      let _welcome_mes = this.$refs.member_welcome.welcome_mes;
      // let _welcome_mes = Object.assign({}, this.welcome_mes)
      this.params_type_arr_old.map((item) => {
        if (!type_arr.includes(item)) {
          let current = _welcome_mes[item];
          this.deleteWelcomeWords(current.id);
        }
      });

      if (!_welcome_mes.text.desc) {
        this.$message.warning("欢迎语不能为空");
        return;
      }
      // for (const key in _welcome_mes) {
      //   if (
      //     !type_arr.includes(key) &&
      //     !_welcome_mes[key].is_del &&
      //     key != "text"
      //   ) {
      //     delete _welcome_mes[key];
      //   }
      // }

      if (
        _welcome_mes.image &&
        !_welcome_mes.image.media_id &&
        !_welcome_mes.image.is_del
      ) {
        delete _welcome_mes.image;
      }
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key] && _welcome_mes.link.is_del != 1) {
            emptyLink.push(key);
          }
        }
        if (emptyLink.length == Object.keys(linkArr).length) {
          emptyLink.length = 0;
          delete _welcome_mes.link;
        } else if (emptyLink.length) {
          this.$message.warning(linkArr[emptyLink[0]]);
          return;
        }
      }

      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (
            !_welcome_mes.miniprogram[key] &&
            _welcome_mes.miniprogram.is_del != 1
          ) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          emptyMini.length = 0;
          delete _welcome_mes.miniprogram;
        } else if (emptyMini.length) {
          this.$message.warning(miniArr[emptyMini[0]]);
          return;
        }
      }

      if (
        _welcome_mes.video &&
        !_welcome_mes.video.media_id &&
        !_welcome_mes.video.is_del
      ) {
        delete _welcome_mes.video;
      }
      if (
        _welcome_mes.file &&
        !_welcome_mes.file.media_id &&
        !_welcome_mes.file.is_del
      ) {
        delete _welcome_mes.file;
      }

      params.welcome_msg = JSON.stringify(_welcome_mes);

      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .editCrmWelcomeWord(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    deleteWelcomeWords(id) {
      this.$http
        .delCrmWelcomeWord(id, {})
        .then(() => {
          // if (res.status == 200) {
          //   this.$message.success(res.message || "删除成功");
          //   this.getDataList();
          // } else {
          //   this.$message.error(res.message || "删除失败");
          // }
        })
        .catch(() => {
          this.$message.error("清空失败");
        });
    },
    showSelectDia() {
      this.getImgList();
      this.show_select_dia = true;
    },
    // 选中图片
    selectAvatar(e) {
      this.currentImg = e.url;
      // this.imgList.map(item => item.checked = false)
      // e.checked = true
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      switch (this.params_type) {
        case 2:
        case 3:
        case 4:
          this.img_params.type = 1;
          break;
        case 5:
          this.img_params.type = 3;
          break;
        case 6:
          this.img_params.type = 4;
          break;

        default:
          break;
      }
      this.loadMore = false;
      this.$http
        .getCrmServiceAvatarList(this.img_params)
        .then((res) => {
          if (res.status == 200) {
            if (this.img_params.type == 4 || this.img_params.type == 3) {
              res.data.data.map((item) => {
                item.user_name = item.url.substring(
                  item.url.lastIndexOf("/") + 1
                );
                return item;
              });
            }
            this.imgList = this.imgList.concat(res.data.data);
            if (
              res.data.data &&
              res.data.data.length == this.img_params.per_page
            ) {
              this.loadMore = true;
            } else {
              this.loadMore = false;
            }
          }
        })
        .catch(() => {
          this.loadMore = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
    setChecked() {
      this.$nextTick(() => {
        this.$refs.memberList.changeSelected(this.selectedIds, true);
      });
    },
    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.params_type) {
        case 2:
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          this.welcome_mes.image.pic_url = url;
          this.welcome_mes.image.media_id = res.data.media_id;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = url;
          // this.welcome_mes.link.media_id = res.data.media_id;
          break;
        case 4:
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          // 小程序 miniprogram   media_id
          this.miniCover = url;
          this.welcome_mes.miniprogram.media_id = res.data.media_id;
          break;
        case 5:
          // 视频
          params.type = 3;
          console.log(url);
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败 请检查素材大小");
            return;
          });

          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case 6:
          // 文件
          params.type = 4;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败");
          });
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
    },
    // 移除选中的人员
    deleteSelected(e) {
      let idx = this.selectedList.findIndex((item) => item.id == e.id);
      this.selectedList.splice(idx, 1);
      this.selectedIds.splice(idx, 1);

      setTimeout(() => {
        this.$refs.memberList.changeSelected(this.selectedIds, true);
      }, 100);
    },
    // 显示选择会员弹框
    showAddMember() {
      this.show_add_member = true;
    },
    closeSelectMember() {
      this.selectedList = this.form_selectedMember;
      this.selectedIds = [];
      this.selectedList.map((item) => {
        this.selectedIds.push(item.wx_work_user_id);
      });
      this.show_add_member = false;
    },
    // 选中会员
    selecetedMember(e) {
      if (e.checkedNodes.length) {
        let nodesIds = [];
        e.checkedNodes.map((item) => {
          nodesIds.push(item.wx_work_user_id);
        });
        this.form_selectedMemberBack.map((item) => {
          if (
            this.form_selectedIdsBack.includes(item.wx_work_user_id) &&
            !nodesIds.includes(item.wx_work_user_id)
          ) {
            e.checkedNodes.push(item);
          }
        });
      } else {
        this.form_selectedMemberBack.map((item) => {
          if (this.form_selectedIdsBack.includes(item.wx_work_user_id)) {
            e.checkedNodes.push(item);
          }
        });
      }
      // this.selectedIds = [...new Set(e.checkedKeys)]
      this.selectedList = this.newsetArr(e.checkedNodes);
      this.selectedIds = [];
      this.selectedList.map((item) => {
        this.selectedIds.push(item.wx_work_user_id);
      });
      this.$forceUpdate();
    },
    newsetArr(arr, key = "id") {
      var result = [];
      var obj = {};
      for (var i = 0; i < arr.length; i++) {
        if (!obj[arr[i][key]]) {
          result.push(arr[i]);
          obj[arr[i][key]] = true;
        }
      }
      return result;
    },
    handleCoverSuccess(e) {
      this.cover = e.url;
    },
    selectMemberOk() {
      let secretIds = [];
      this.selectedList.map((item) => {
        secretIds.push(item.wx_work_user_id);
      });
      this.form_params.userid = secretIds.join(",");
      // this.form_params.admin_user_id = this.selectedIds.join(",")
      this.form_selectedMember = this.selectedList;
      this.form_selectedMemberBack = this.selectedList;
      this.form_selectedIdsBack = secretIds;
      this.show_add_member = false;
    },
    // 选中图片确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      switch (this.params_type) {
        case 2:
          // 图片
          this.welcome_mes.image.media_id = current.media_id;
          this.welcome_mes.image.pic_url = current.url;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = current.url;
          // this.welcome_mes.link.pic_url = url;
          this.welcome_mes.link.media_id = current.media_id;
          break;
        case 4:
          // 小程序 miniprogram   media_id
          this.miniCover = current.url;
          // this.welcome_mes.miniprogram.pic_url = url;
          this.welcome_mes.miniprogram.media_id = current.media_id;
          break;
        case 5:
          // 视频
          this.videoname = current.url;
          this.welcome_mes.video.media_id = current.media_id;
          break;
        case 6:
          // 文件
          this.filename = current.url;
          this.welcome_mes.file.media_id = current.media_id;
          break;

        default:
          break;
      }
      this.show_select_dia = false;
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    checkChange(e, type) {
      if (e) {
        this.params_type = type;
        var category = config.CATEGORY_IM_IMAGE;
        switch (type) {
          case 2:
          case 3:
          case 4:
            category = config.CATEGORY_IM_IMAGE;
            break;
          case 5:
            category = config.CATEGORY_IM_VIDEO;
            break;
          case 6:
            category = config.CATEGORY_IM_FILE;
            break;
          default:
            break;
        }
        this.website_img = `/api/common/file/upload/admin?category=${category}`;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;

  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 30px;

  &.type_tips {
    margin-bottom: 0;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}

.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}

.selected_list {
  padding: 10px 0;

  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;

    .delete {
      font-size: 22px;
      cursor: pointer;
    }

    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}

.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    width: 0;
  }

  .img {
    width: 115px;
    height: 115px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;

    &.active {
      border: 5px solid #409eff;

      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    &:nth-child(4n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .files {
    margin-bottom: 10px;
    margin-right: 6px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;

    &.active {
      border: 5px solid #409eff;
    }

    &:nth-child(3n) {
      margin-right: 0;
    }

    .file_img {
      width: 120px;
      height: 120px;
      text-align: center;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}

.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }

  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;

  &.active {
    border: 1px solid #a6e22e;
  }
}

.upload-demo {
  margin-right: 5px;
}
</style>
