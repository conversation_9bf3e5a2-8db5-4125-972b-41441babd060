<template>
    <el-dialog :visible.sync="show" width="75%" :modal="false" title="编辑节点">
        <div class="addnode">
            <!-- 批量加入流转节点 -->
        <div class="page">
            <div class="job_name"> 
                <el-form ref="form" :model="from_data" label-width="105px">
                    <!-- <el-form-item label="指定生效范围:">
                        <el-radio-group v-model="radio1" size="small" @change="clickradio1">
                            <el-radio-button v-for="item in radio1data" :key="item.id" :label="item.id"
                            >{{ item.name }}</el-radio-button>
                        </el-radio-group>
                    </el-form-item> -->
                    <el-form-item label="流程:">
                          <el-table
                            :header-cell-style="{ background: '#EBF0F7' }"
                            :data="tableData"
                            style="width: 100%">
                            <el-table-column
                              label="任务节点名称"
                              prop="point_name">
                              <template slot-scope="scope">
                                <el-input v-model="scope.row.point_name" placeholder="请输入节点名称"></el-input>
                              </template>
                            </el-table-column>
                              <el-table-column label="节点负责人" v-if="member">
                                <template>
                                    <el-link type="primary" @click="memberpopFrame">+选择成员</el-link>
                                </template>
                            </el-table-column>
                            <el-table-column label="节点负责组" v-if="!member">
                                <template>
                                    <el-link type="primary" @click="grouppopFrame">+选择分组</el-link>
                                </template>
                            </el-table-column>
                            <el-table-column label="执行方式">
                                <el-link type="primary" @click="selectBy">+选择项</el-link>
                            </el-table-column>
                            <el-table-column label="执行时间"
                            prop="execute_time" v-if="from_data.execute_time">
                                <template slot-scope="scope">
                                    {{ scope.row.execute_time }}
                                </template>
                              
                            </el-table-column>
                            <el-table-column label="配置">
                              <el-link type="primary" @click="selectBy(1)">+配置项</el-link>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                    <el-form-item label="节点负责组:" v-if="!member&&goupdata.length" >
                        <el-tooltip placement="right" effect="light" v-if="from_data.assign_type==1">
                             <div slot="content" style="width: 600px;">
                                <div class="prompt">
                                    设置为0时为平均分配
                                </div>
                            <el-table
                              :data="goupdata"
                              style="width: 100%">
                              <el-table-column
                                prop="assign_name"
                                label="负责组">
                              </el-table-column>
                              <el-table-column
                                label="分配排序">
                                <template slot-scope="scope">
                                      <el-input-number style="width: 120px;" v-model="scope.row.assign_num" controls-position="right" :min="0" :step="1"></el-input-number>
                                </template>
                              </el-table-column>
                              <el-table-column
                                label="分配数量">
                                <template slot-scope="scope">
                                    <el-input-number style="width: 120px;" v-model="scope.row.sort" controls-position="right" :min="0"></el-input-number>
                                </template>
                              </el-table-column>
                              <el-table-column
                                label="操作">
                                <template slot-scope="scope">
                                  <el-button size="mini" type="danger" @click="delgoup(scope.$index)">删除</el-button>
                                </template>
                              </el-table-column>
                            </el-table>
                        </div>
                            <el-tag style="cursor: pointer;">共选择{{ goupdata.length }}个负责组</el-tag>
                        </el-tooltip>
                        <div v-else>
                          <el-tag>共选择{{ goupdata.length }}个负责组</el-tag>
                        </div>
                    </el-form-item>
                    <el-form-item label="节点负责人:" v-if="member&&selectedIds.length" >
                        <el-tooltip placement="right" effect="light" v-if="from_data.assign_type==1">
                             <div slot="content" style="width: 600px;">
                                <div class="prompt">
                                    设置为0时为平均分配
                                </div>
                            <el-table
                              :data="selectedIds"
                              style="width: 100%">
                              <el-table-column
                                prop="assign_name"
                                label="负责人">
                              </el-table-column>
                              <el-table-column
                                label="分配排序">
                                <template slot-scope="scope">
                                      <el-input-number style="width: 120px;" v-model="scope.row.assign_num" controls-position="right" :min="0" :step="1"></el-input-number>
                                </template>
                              </el-table-column>
                              <el-table-column
                                label="分配数量">
                                <template slot-scope="scope">
                                    <el-input-number style="width: 120px;" v-model="scope.row.sort" controls-position="right" :min="0"></el-input-number>
                                </template>
                              </el-table-column>
                              <el-table-column
                                label="操作">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="danger" @click="delmember(scope.$index)">删除</el-button>
                                </template>
                                
                              </el-table-column>
                            </el-table>
                        </div>
                            <el-tag style="cursor: pointer;">共选择{{ selectedIds.length }}个负责人</el-tag>
                        </el-tooltip>
                        <div v-else>
                          <el-tag>共选择{{ selectedIds.length }}个负责人</el-tag>
                        </div>
                    </el-form-item>
                </el-form>  
            </div>
            <!-- 选择成员弹框 -->
            <el-dialog :visible.sync="show_add_member" width="400px" title="请选择成员" append-to-body>
              <div class="member" ref="memberList">
                <!-- <div v-if="identification == 'potential_client_operation_uid'"
                style="margin-bottom:20px"> <el-button  type="primary" size="small" @click="Select_All">全选</el-button></div> -->
                <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClick="selecetedMember"
                  :defaultExpandAll="false">
                </multipleTree>
                <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
                  <el-button type="text" @click="show_add_member = false">取消</el-button>
                  <el-button type="primary" @click="selectMemberOk">确定</el-button>
                </div>
              </div>
            </el-dialog>
            <!-- 选择分组弹框 -->
            <el-dialog :visible.sync="show_add_group" width="500px" title="请选择分组" append-to-body>
                <div class="member" ref="groupList">
                    <groupcomponents v-if="show_add_group" @child-event="selectionChange">
                    </groupcomponents>
                    <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
                  <el-button type="text" @click="show_add_group = false">取消</el-button>
                  <el-button type="primary" @click="selectgroupOk">确定</el-button>
                </div>
                </div>
            </el-dialog>
            <!-- 执行方式的弹框 -->
            <el-dialog :visible.sync="show_Execution_method" width="500px" title="请选择执行方式" append-to-body>
                <div class="Execution" >
                    <div>
                        <el-radio v-model="Executionmethod" label="1">立即执行</el-radio>
                        <el-radio v-model="Executionmethod" label="2">选择时间</el-radio>
                          <el-date-picker
                            v-if="Executionmethod=='2'"
                            v-model="valuetime"
                            type="datetime"
                            placeholder="选择日期时间">
                          </el-date-picker>
                    </div>
                </div>
                <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
                  <el-button type="text" @click="show_Execution_method = false">取消</el-button>
                  <el-button type="primary" @click="executionmode">确定</el-button>
                </div>
            </el-dialog>
            <!-- 配置项弹框 -->
            <el-dialog :visible.sync="show_configuration_item" width="500px" title="请选择" append-to-body>
                <div class="configuration" >
                    <div>
                      <span>分配方式：</span>
                        <el-radio v-model="from_data.assign_type" label="1">手动自定义</el-radio>
                        <el-radio v-model="from_data.assign_type" label="2">平均分配</el-radio>
                    </div>
                    <div>
                      <span>是否同步标签：</span>
                        <el-radio v-model="from_data.sync_label" label="1">同步</el-radio>
                        <el-radio v-model="from_data.sync_label" label="2">不同步</el-radio>
                    </div>
                    <div>
                      <span>是否同步城市：</span>
                        <el-radio v-model="from_data.sync_city" label="1">同步</el-radio>
                        <el-radio v-model="from_data.sync_city" label="2">不同步</el-radio>
                    </div>
                    <div>
                      <span>是否同步客户意向：</span>
                        <el-radio v-model="from_data.sync_intention" label="1">同步</el-radio>
                        <el-radio v-model="from_data.sync_intention" label="2">不同步</el-radio>
                    </div>
                    <div>
                      <span>是否同步备注：</span>
                        <el-radio v-model="from_data.sync_remark" label="1">同步</el-radio>
                        <el-radio v-model="from_data.sync_remark" label="2">不同步</el-radio>
                    </div>
                    <div>
                      <span>是否同步附属手机号：</span>
                        <el-radio v-model="from_data.sync_sub_mobile" label="1">同步</el-radio>
                        <el-radio v-model="from_data.sync_sub_mobile" label="2">不同步</el-radio>
                    </div>
                    <div>
                      <span>是否可以查看历史跟进记录</span>
                        <el-radio v-model="from_data.allow_view_follow" label="1">同步</el-radio>
                        <el-radio v-model="from_data.allow_view_follow" label="2">不同步</el-radio>
                    </div>
                </div>
                <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
                  <el-button type="text" @click="show_configuration_item = false">取消</el-button>
                  <el-button type="primary" @click="show_configuration_item = false">确定</el-button>
                </div>
            </el-dialog>
        </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="submission">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import groupcomponents from "@/components/navMain/crm/components/groupcomponents.vue";
export default {
    components:{
        multipleTree,
        groupcomponents,
    },
    data(){
        return {
            show: false,        //dialog是否显示
            wanderabout:false,//流转列表
            radio1:"1",//指定生效范围值
            radio1data:[
              {id: 1 ,name: "分组"},
              {id: 2 ,name: "成员"},
            ],//指定生效范围
            tableData:[
                {point_name:"",execute_time:""},
            ],
            member:false,//控制选择成员或组
            // method:"",//控制分配方式，成员或组
            show_add_member:false,//控制选择成员的弹框
            serverData: [], // 部门成员数据
            datalist: [], // 全部部门成员列表
            selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
            show_add_group:false,//控制选择组的弹框
            show_Execution_method:false,//选择执行方式的弹框
            show_configuration_item:false,//配置项的弹框
            Executionmethod:"1",//执行方式，1.立即执行 2.选择时间
            valuetime:"",//时间
            goupdata:[],//分组id
            num:0,//分配数量
            num1:0,//分配排序
            //添加流转任务的最终提交from
            from_data:{ //节点详细参数
                member_range:"",//分配范围，1:分组，2：成员
                point_name:"",//节点名称
                assign_type:"",//分配方式,1:手动自定义，2: 平均分配
                assign_arr:[],//负责人或组范围json字符串，例：["assign id":700,"assign num":12,"sort":13]
                execute_type:"",//执行方式，1:立即执行，2: 时间
                execute_time:"",//执行时间 execute_type=2，此参数为必填,例：2023-12-20 14:59:00
                sync_label:"",//同步标签，1同步，
                sync_city:"",//同步城市，1同步，
                sync_intention:"",//同步客户意向，1同步，
                sync_remark:"",//同步备注，1同步，
                sync_sub_mobile:"",//同步附属手机号
                allow_view_follow:"",//是否可以查看历史跟进记录
                source:"4",//1:潜在客户，2：公海客户，3：我的客户，4：流转客
                auto_work_id:"",
            },
            wanderData:[],//流转任务列表
        }
    },
    mounted(){
        this.getMemberList()
    },
    methods: {
        //打开弹层
        open(row,member_range,auto_work_id){
            this.from_data = row;
            this.from_data.auto_work_id = auto_work_id
            if(member_range){
                    if(member_range==1){
                        this.member = false
                        this.from_data.member_range = 1
                        if(typeof(this.from_data.assign_arr) == String){
                            this.goupdata =  JSON.parse(this.from_data.assign_arr)
                        }else{
                            this.goupdata =  this.from_data.assign_arr
                        }
                    }else if(member_range==2){
                        this.member = true
                        this.from_data.member_range = 2
                        if(typeof(this.from_data.assign_arr) == String){
                            this.selectedIds =  JSON.parse(this.from_data.assign_arr)
                        }else{
                            this.selectedIds =  this.from_data.assign_arr
                        }
                        // this.selectedIds = this.from_data.assign_arr
                    }
                }
                if(this.from_data.execute_time){
                    this.tableData[0].execute_time = this.from_data.execute_time
                    this.Executionmethod = "2"
                    this.valuetime = this.from_data.execute_time

                }
            this.tableData[0].point_name = this.from_data.point_name
            this.show = true;
        },
           //指定生效范围
           clickradio1(){
            if(this.radio1==1){
                this.member = false
                this.from_data.member_range = 1
                this.selectedIds = []
            }else{
                this.member = true
                this.from_data.member_range = 2
                this.goupdata = []
            }
        },
        // 成员弹框
        memberpopFrame(){
            this.show_add_member = true
        },
        //选择组弹框
        grouppopFrame(){
            this.show_add_group = true
        },
        //执行方式弹框
        selectBy(status){
          if(status==1){
            this.show_configuration_item = true
          }else{
            this.show_Execution_method = true
          }
          
        },
        // 子组件传来的数据(已选择的组数据)
        selectionChange(data) {
          console.log('收到子组件传递的数据：', data);
          this.goupdata = data
        },
        // 获取部门成员列表
        async getMemberList() {
          await this.$http.getDepartmentMemberList().then((res) => {
            if (res.status == 200) {
              this.serverData = JSON.parse(JSON.stringify(res.data))
              this.serverData.push({
                id: 999,
                name: "未分配部门成员",
                order: 100000000,
                pid: 0,
                subs: this.serverData[0].user
              })
              this.recursionData(this.serverData);
              // 当键值key重复就更新key
              for (let i = 0; i < this.datalist.length; i++) {
                for (let j = i + 1; j < this.datalist.length; j++) {
                  if (this.datalist[i].id == this.datalist[j].id) {
                    this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
                  }
                }
              }
            }
          })
        },
        // 递归数据处理
        recursionData(data) {
          // console.log(data,"内容");
          // console.log(this.datalist,"全部人员");
          for (let key in data) {
            if (typeof data[key].subs == "object") {
              data[key].subs.map((item) => {
                if (item.user) {
                  item.subs = item.user;
                  item.subs.map((list) => {
                    list.Parent = item.id;
                  })
                }
                if (item.user_name) {
                  item.name = item.user_name;
                  this.datalist.push(item)
                }
              })
              this.recursionData(data[key].subs);
            }
          }
        },
        // 选中变化时触发
        selecetedMember(e) {
            console.log(e);
            this.selectedIds = e.map(item=>{
                return {
                  id: item.id,
                  assign_name: item.name,
                  assign_num: 0,
                  sort: 0
                };
            })
            console.log(this.selectedIds);
            // this.selectedList = e.checkedNodes;
        },
        // 成员确定
        selectMemberOk() {
          this.show_add_member = false
        },
        // 分组确定
        selectgroupOk(){
            this.goupdata = this.goupdata.map((item, index) => {
              // 在每个元素中添加assign_num和sort属性
              return {
                ...item, // 将原来的属性展开
                assign_name:item.title,
                assign_num: 0,
                sort: 0
              }
            })
            console.log(this.goupdata);
            this.show_add_group = false
        },
        //删除组
        delgoup(index){
          console.log(index);
          this.$confirm('此操作将删除该分组, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.goupdata.splice(index, 1);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
        },
        //删除成员
        delmember(index){
          console.log(index);
          this.$confirm('此操作将删除该成员, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.selectedIds.splice(index, 1);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
        },
        // 执行方式确定
        executionmode(){
          if(this.Executionmethod==2){
            if(!this.valuetime){
              this.$message({
                type:"warning",
                message:"请选择时间！"
              })
              return
            }
          }
          if(this.Executionmethod==1){
            this.from_data.execute_type = 1
            this.from_data.execute_time = ""
          }
          if(this.Executionmethod==2){
            if(this.valuetime){
              const updatedDateStr = this.valuetime.toLocaleString('zh-CN').replace(/\//g, '-');
              this.from_data.execute_type = 2
              this.from_data.execute_time = updatedDateStr
              this.tableData[0].execute_time = updatedDateStr
            }
          }
          console.log(this.from_data.execute_time);
          this.show_Execution_method = false
        },
        // 最后确定添加
        submission(){
          //如果选择的成员，遍历赋值
          if(this.from_data.member_range == 1){
            //如果选择的组，遍历赋值
            if (this.goupdata) {
                    // console.log("组组",this.goupdata);
                    const renamedItems = this.goupdata.map(item => {
                      return {
                        assign_id: item.id||item.assign_id,
                        assign_num: item.assign_num,
                        sort:item.sort
                      };
                    });
                    this.from_data.assign_arr = JSON.stringify(renamedItems);
                  }
          }else{
            if (this.selectedIds) {
                // console.log("成员",this.selectedIds);
              const renamedItems = this.selectedIds.map(item => {
                return {
                  assign_id: item.id||item.assign_id,
                  assign_num: item.assign_num,
                  sort:item.sort
                };
              });
              this.from_data.assign_arr = JSON.stringify(renamedItems);
            }
          }
          if(this.tableData){
            this.tableData.map(item =>{
              this.from_data.point_name =  item.point_name
            })
          }
            console.log(this.from_data,"1132134124");
            this.batchcirculation(this.from_data)
        },
        // 确定流转方法
        batchcirculation(from_data){
          this.$http.editwanderaboutnodes(from_data).then(res=>{
            if(res.status==200){
              this.$message({
                type:"success",
                message:"添加成功！"
              })
              this.show = false;
              this.$emit('childButtonClick');
              this.clealdata()
            }
          })
        },
        // 清空
        clealdata(){
          this.radio1 = "1"
          this.tableData = [{point_name:""}]
          this.goupdata = []
          this.selectedIds = []
          this.Executionmethodc = "1"
          this.from_data.execute_time = ""
        },
        //取消
        cancle(){
            this.show = false;
        },
        //确定
        // confirm(){
        //     //TODO: 验证+提交
        //     //在提交成功之后回调
        //     // this.$refs.nodecomponents.submission()
        //     // this.show = false;
        //     console.log(this.from_data,"1132134124");
        // }
    }
}
</script>
<style lang="scss" scoped>
   .addnode{
    height: 500px;
   } 
   .page{
    width:97%;
    height: 200px;
    margin: 0 auto;
    .job_name{
        margin-top: 20px;
        div{
           margin-bottom: 12px;
         }
    }
    /deep/.el-table th {
         padding: 0px 0 !important;
       } 
}  
.pagetable{
  // width: 90%;
  // margin: 0 auto;
  // margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  margin-right: 30px;
}

.Execution{
    height: 200px;
}
.configuration{
  height: 500px;
  div{
    margin-bottom: 50px;
  }
}
.Nodegroup{
    display: flex;
    .Nodegroupitem{
        margin-right: 20px;
        cursor: pointer;
    }
}
.prompt{
    height: 40px;
    color: #e6a23c;
    background: #fdf6ec;
    border-color: #f5dab1;
    font-size: 15px;
    text-align: center;
    line-height: 36px;
    margin-bottom: 30px;
}
</style>