<template>
  <el-container>
    <el-main>
      <el-button
        style="margin-bottom:20px"
        v-if="$hasShow('添加广告')"
        icon="el-icon-plus"
        type="primary"
        @click="add"
        >添加广告</el-button
      >
      <div class="back">
        <!-- <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button> -->
      </div>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
  </el-container>
</template>

<script>
import myTable from "@/components/components/my_table";
export default {
  name: "adv_management",
  components: {
    myTable,
  },
  data() {
    return {
      tableData: [],
      id: "",
      table_header: [
        { prop: "name", label: "广告名称" },
        {
          label:"广告图片",
          render:(h,data)=>{
            if (data.row.type==1){
              return(
              <div>
                <img class ="img" src={data.row.img} style="width:116px;height:77px"></img>
                

              </div>
            )
            }else {
              return(
              <div>
                <video class ="img" src={data.row.img} style="width:116px;height:77px"></video>
              </div>
            )
            }
            
          }
        },
        {
          prop: "sort",
          sortable:true,
          label: "排序方式",
          render: (h, data) => {
            return (
              <div>
                <el-input
                  style="width:80px"
                  v-model={data.row.sort}
                  onChange={() => {
                    this.onChangeSort(data.row);
                  }}
                ></el-input>
              </div>
            );
          },
        },
        { prop: "start_at", label: "开始时间" },
        { prop: "end_at", label: "结束时间" },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("编辑广告") ? (
                  <el-button
                    icon="el-icon-edit"
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.checkData(data.row);
                    }}
                  >
                    编辑
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除广告") ? (
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    onClick={() => {
                      this.deleteData(data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getDataList();
  },
  methods: {
    //修改排序
    onChangeSort(e){
      console.log(e.sort);
      this.$http
          .updataAdv({
            id: e.id,
            position_id: e.position_id,
            jump_category: e.jump_category,
            name: e.name,
            start_at: e.start_at,
            end_at: e.end_at,
            link_url: e.link_url,
            img: e.img,
            region_0: e.region_0,
            region_1: e.region_1,
            sort:e.sort
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "修改成功",
                type: "success",
              });
              this.getDataList()
            }
          });
    },
    //获取列表数据
    getDataList() {
      this.$http.advAll(this.id,0).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data;
          console.log(res.data);
        }
      });
    },
    goBack() {
      this.$router.back();
    },
    add() {
      this.$goPath(`add_advertising?position_id=${this.id}`);
    },
    checkData(row) {
      this.$goPath(`add_advertising?id=${row.id}&position_id=${this.id}`);
    },
    deleteData(row) {
      this.$confirm("此操作将删除该广告, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteAdv(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style scoped>

</style>
