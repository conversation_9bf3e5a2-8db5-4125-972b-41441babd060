<template>
  <div>
    <div class="page">
      <div class="tabs_container flex-row">
        <!-- 筛选区域 -->
        <div class="filter_container flex-row items-center">
          <div class="filter_type flex-1">
            <div class="list">
              <span v-for="(filter, index) in filters" :key="index">
                <el-select class="super_mini_select" :style="{
                  minWidth: '40px',
                  width: getSelectWidth(filter, params[filter.type]),
                }" v-model="params[filter.type]" :placeholder="filter.label"
                  @change="onSelectFilterOption($event, filter)">
                  <el-option v-for="option in filter.item" :key="option.values" :label="option.name"
                    :value="option.values">
                  </el-option>
                </el-select>
              </span>
            </div>
          </div>
        </div>
        <div class="flex-row items-center">
          <div class="flex-row align-center" style="margin-left: 24px">
            <div class="house-search">
              <template v-if="searchType == '房源名称'">
                <el-input v-model="params.title" :placeholder="searchDefaultName" step="1"
                  @keyup.enter.native="handleSearch" @input="handleSearch">
                </el-input>
              </template>
              <template v-else-if="searchType == '委托编号'">
                <el-input v-model="params.wt_no" :placeholder="searchDefaultName" type="number" step="1"
                  @keyup.enter.native="handleSearch" @input="handleSearch">
                </el-input>
              </template>
              <template v-else-if="searchType == '业主电话'">
                <el-input :placeholder="searchDefaultName" v-model="params.mobile" @keyup.enter.native="handleSearch"
                  @input="handleSearch($event, 'tel')"></el-input>
              </template>
              <el-dropdown @command="dropdownChange">
                <el-button style="border-radius: 0 5px 5px 0;border-left: 0;">
                  {{ searchType }}<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="房源名称">房源名称</el-dropdown-item>
                  <el-dropdown-item command="委托编号">委托编号</el-dropdown-item>
                  <el-dropdown-item command="业主电话">业主电话</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
      <div class="left-box">

        <div class="info_list" v-if="entrustList.length" :disabled="loading !== 'loadend'">
          <!-- <entrustHouseItem :data="item" v-for="(item, index) in entrustList" :key="index"></entrustHouseItem> -->
          <el-table :data="entrustList" border style="width: 100%">
            <el-table-column fixed prop="add_time" label="委托日期" width="200">
            </el-table-column>
            <el-table-column prop="title_promotion" label="房源名称" width="230">
            </el-table-column>
            <el-table-column label="图片" width="120">
              <template slot-scope="scope">
                <div style="display: flex;align-items: center;">
                  <!-- <img style="width: 50px; height: 50px;border-radius: 3px;" :src="scope.row.pic_list[0].url"> -->

                  <el-popover placement="right" width="670" trigger="click">
                    <div class="img-list" style='display: flex;flex-wrap: wrap;'>
                      <div class="img-item" v-for="(item, index) in scope.row.pic_list" :key="index">
                        <img style="width: 200px; height: 200px;border-radius: 5px;margin: 10px;" :src="item.url">
                      </div>
                    </div>
                    <div slot="reference" style="cursor:pointer">
                      <span>{{ scope.row.pic_list.length }} 图</span>
                      <i style="margin-left: 5px;" class="el-icon-arrow-down"></i>
                    </div>
                  </el-popover>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="房源描述" width="300">
              <template slot-scope="scope">
                <span>{{ scope.row.description ? scope.row.description : '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="owner" label="业主" width="120">
            </el-table-column>
            <el-table-column prop="owner_tel" label="电话" width="120">
            </el-table-column>
            <el-table-column label="交易" width="120">
              <template slot-scope="scope">
                <span>{{ typeFun(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="物业" width="120">
              <template slot-scope="scope">
                <span>{{ wuyeFun(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="小区" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.community_name ? scope.row.community_name : '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="区域" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.area_name ? scope.row.area_name : '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="户型" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.shi ? scope.row.shi : '--' }} 室 {{ scope.row.ting ? scope.row.ting : '--' }} 厅
                  {{ scope.row.wei ? scope.row.wei : '--' }} 卫</span>
              </template>
            </el-table-column>
            <el-table-column label="面积" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.mianji ? scope.row.mianji + ' m²' : '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="朝向" width="120">
              <template slot-scope="scope">
                <span>{{ chaoxiangFun(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="装修" width="120">
              <template slot-scope="scope">
                <span>{{ zhuangxiuFun(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="楼层" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.floor ? scope.row.floor + '楼' : '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所在楼层" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.sz_floor ? scope.row.sz_floor + '楼' : '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="价格" width="120">
              <template slot-scope="scope">
                <span v-if='scope.row.trade_type == 2'>{{ scope.row.rent_price ? scope.row.rent_price + ' /月' : '--'
                }}</span>
                <span v-if='scope.row.trade_type == 1'>{{ scope.row.sale_price ? scope.row.sale_price + ' /m²' : '--'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status != 0" :type="scope.row.status == 1 ? 'success' : 'danger'">{{
                  scope.row.status == 1 ? '已通过委托' : '已驳回委托'
                }}</el-tag>
                <div v-else>
                  <el-button @click="consent(scope.row)" type="text" size="small">录入</el-button>
                  <el-button style="color: red;" @click="reject(scope.row)" type="text" size="small">驳回</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="no_data flex-row align-center j-center" v-if="loading == 'loading'">
          <div class="no_data_name">加载中。。。</div>
        </div>
        <div class="no_data flex-row align-center j-center" v-if="entrustList.length == 0 && loading == 'nomore'">
          <div class="no_data_name">暂无委托房源</div>
        </div>
      </div>
    </div>
    <div class="house-bom-box">
      <div class="house-bom-btn" style="margin-left: 20px;">
        <el-button type="primary" size="mini" icon="el-icon-refresh-right" @click="refresh">刷新页面</el-button>
        <el-button type="primary" size="mini" icon="el-icon-delete" @click="clearFilter">清空筛选项</el-button>
      </div>
      <el-pagination style="margin-right: 20px;" :hide-on-single-page="true" background
        layout="total, sizes, prev, pager, next, jumper" :total="data_count" :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="params.rows" :current-page="params.page" @current-change="onPageChange" @size-change="onSizeChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
// import entrustHouseItem from './components/entrust_house_item.vue'
// import HouseItem from "./components/HouseItem.vue";
export default {
  name: "house_list",
  data() {
    return {
      data_count: 0,
      searchType: '房源名称',
      loading: '',
      entrustList: [],
      value: '',
      filters: [{
        customizale: 0,
        item: [
          { name: '不限', values: '' },
          { name: '出售', values: 1 },
          { name: '出租', values: 2 },
        ],
        label: '交易类型',
        multiple: 0,
        type: 'trade_type',
        unit: ''
      }, {
        customizale: 0,
        item: [
          { name: "不限", values: '' },
          { name: "住宅", values: 1 },
          { name: "别墅", values: 2 },
          { name: "商住", values: 3 },
          { name: "商铺", values: 4 },
          { name: "写字楼", values: 5 },
          { name: "厂房", values: 6 },
          { name: "车库", values: 7 },
          { name: "土地", values: 8 },
          { name: "其它", values: 99 },
        ],
        label: '物业类型',
        multiple: 0,
        type: 'usage',
        unit: ''
      }, {
        customizale: 0,
        item: [
          { name: '不限', values: '' },
          { name: '待审核', values: 0 },
          { name: '已通过', values: 1 },
          { name: '已驳回', values: 2 },
        ],
        label: '状态',
        multiple: 0,
        type: 'status',
        unit: ''
      }],
      params: {
        page: 1,
        per_page: 20,
      },
    };
  },
  // components: { entrustHouseItem },
  computed: {
    searchDefaultName() {
      return `请输入${this.searchType}`
    },
    chaoxiangFun() {
      let chaoxiang = [
        { name: "东", value: 1 },
        { name: "南", value: 2 },
        { name: "西", value: 3 },
        { name: "北", value: 4 },
        { name: "东南", value: 5 },
        { name: "东北", value: 6 },
        { name: "西南", value: 7 },
        { name: "西北", value: 8 },
        { name: "南北", value: 9 },
        { name: "东西", value: 10 }
      ]
      return (row) => {
        let item = chaoxiang.find((val) => val.value == row.chaoxiang)
        return item ? item.name : '--'
      }
    },
    wuyeFun() {
      let wuye = [
        { name: "住宅", value: 1 },
        { name: "别墅", value: 2 },
        { name: "商住", value: 3 },
        { name: "商铺", value: 4 },
        { name: "写字楼", value: 5 },
        { name: "厂房", value: 6 },
        { name: "车库", value: 7 },
        { name: "土地", value: 8 },
        { name: "其它", value: 99 },
      ]
      return (row) => {
        let item = wuye.find((val) => val.value == row.usage_type_id)
        return item ? item.name : '--'
      }
    },
    typeFun() {
      let type = [
        { name: '出租', value: 2 },
        { name: '出售', value: 1 }
      ]
      return (row) => {
        let item = type.find((val) => val.value == row.trade_type)
        return item ? item.name : '--'
      }
    },
    zhuangxiuFun() {
      let zhuangxiu = [
        { name: "毛坯", value: 1 },
        { name: "普通装修", value: 2 },
        { name: "精装修", value: 4 },
        { name: "豪华装修", value: 6 }
      ]
      return (row) => {
        let item = zhuangxiu.find((val) => val.value == row.zhuangxiu)
        return item ? item.name : '--'
      }
    },
  },
  created() {
    this.website_ids = this.$route.query.website_id; // 获取站点id
    this.getList()
    // this.searchCondition()
  },
  methods: {
    consent(row) {
      this.$confirm('确定通过并录入该房源吗', '通过提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // const res = await this.$http.confirmEntrustAPI(this.data.id)
        // if (res.status == 200) {
        //   this.getList()
        //   this.$message({
        //     message: '已通过委托申请',
        //     type: 'success'
        //   });
        // }
        let entrustData = JSON.stringify(row)
        this.$goPath(`house_add?entrustData=${entrustData}`);
      })
    },
    reject() {
      this.$confirm('确定驳回该委托吗', '驳回提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.$http.cancelEntrustAPI(this.data.id)
        if (res.status == 200) {
          this.getList()
          this.$message({
            message: '已驳回委托申请',
            type: 'success'
          });
        }
      })
    },
    onSizeChange(e) {
      this.params.rows = e
      this.getList();
    },
    dropdownChange(e) {
      this.searchType = e
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    async getList() {
      this.loading = 'loading'
      let params = this.params
      this.entrustList = []
      const res = await this.$http.getEntrustListAPI({ params })
      if (res.status == 200) {
        this.entrustList = res.data.data
        this.entrustList.forEach((item) => {
          item.pic_list.forEach((item2) => {
            item2.category_id = 1
            item2.descp = ''
          })
        })
        this.data_count = res.data.total
        if (res.data.data.length < this.params.per_page) {
          this.loading = "nomore";
        } else {
          this.loading = "loadend";
        }
      }
    },
    refresh() {
      this.params = { page: 1, per_page: 20 };
      this.getList();
    },
    clearFilter() {
      this.params = {
        page: 1,
        rows: 20
      }
      // this.getList();
    },
    onSelectFilterOption(e, item) {
      let val = e ? e : ''
      this.params.page = 1;
      this.params[item.type] = val
      this.getList();
    },
    getSelectWidth(filter, param) {
      var text_temp = "",
        text_width = 0,
        text_el = null;
      var current_option =
        filter.item && filter.item.find((item) => item.values === param);
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${filter.label}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text");
      text_width = text_el.offsetWidth;
      text_el.remove();
      return text_width + 16 + "px";
    },

    handleSearch(e, type) {
      if (type == 'tel') {
        if (e.length < 1) {
          this.params.page = 1;
          this.getList();
          return
        }
        if (e.length < 11 || e.length > 11) return
      }
      this.params.page = 1;
      this.getList();
    }
  }
};
</script>

<style lang="scss" scoped>
.house-bom-box {
  position: fixed;
  left: 230px;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  width: calc(100vw - 230px);
  padding: 20px 0 10px 0;
  box-shadow: 0px -3px 6px rgba(0, 0, 0, .1);
  border-radius: 3px 3px 0 0;
  background-color: #fff;
  z-index: 9;
}

.house-search {
  display: flex;
  align-items: center;
  margin-right: 10px;

  /deep/ .el-input__inner {
    width: 200px;
    border-radius: 5px 0 0 5px;
    border-right: 0;
  }
}

.page {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .bg_white {
    background: #fff;
  }
}

.tabs_container {
  justify-content: space-between;
  padding: 10px 26px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 9;
  margin-bottom: 20px;

  .el-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 0;
    }

    ::v-deep .el-tabs__nav-wrap::after {
      height: 0;
    }

    ::v-deep .el-tabs__active-bar {
      height: 3px;
      bottom: 4px;
    }
  }
}

.data_list {
  display: flex;
  padding-top: 24px;
  padding-bottom: 24px;

  .data_item {
    flex: 1;
    flex-shrink: 0;
    text-align: center;

    .value {
      font-size: 18px;
      font-weight: bold;
      color: #2d84fb;
    }

    .label {
      margin-top: 2px;
      font-size: 14px;
      color: #999;
    }
  }
}

.filter_container {
  padding: 12px;
  background-color: #fff;
  font-size: 12px;
  color: #333;
  position: sticky;
  top: 10px;
  display: flex;
  align-items: center;
  height: 30px;

  .filter_type {
    display: flex;

    >.label {
      padding: 10px;
      line-height: 18px;
      // width: 48px;
    }

    >.list {
      flex: 1;
      padding: 10px 0;

      .item {
        padding: 5px 0;
        margin-right: 24px;
        cursor: pointer;

        &.active {
          color: #2d84fb;
        }
      }

      .el-select {
        cursor: pointer;
      }

      .el-checkbox {
        margin-right: 24px;

        ::v-deep .el-checkbox__label {
          padding-left: 8px;
          font-size: 12px;
          color: #8a929f;
        }
      }
    }

    .custom {
      display: inline-block;

      .el-input {
        margin: 0 8px;
        height: 16px;
        width: 48px;
        font-size: 11px;
        text-align: center;

        ::v-deep .el-input__inner {
          height: 16px;
          line-height: 16px;
          padding: 0 5px;
          text-align: center;
        }
      }

      ::v-deep .el-button {
        &.el-button--mini {
          padding: 3px 8px;
        }
      }
    }
  }

  .el-select {
    &.super_mini_select {
      margin-right: 24px;
      font-size: 12px;

      ::v-deep .el-input__inner {
        padding-right: 0;
        padding-left: 0;
        height: auto;
        line-height: 1;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        font-size: 12px;
        color: #8a929f;
      }

      ::v-deep .el-input__suffix {
        right: 0;
      }

      ::v-deep .el-input__icon {
        width: auto;
        line-height: 15px;
        font-size: 12px;
      }

      ::v-deep .el-select__tags {
        max-width: initial !important;
      }
    }
  }
}

// 加载中
.loading {
  position: relative;
  padding: 12px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-size: 14px;
  color: #8a929f;

  ::v-deep .el-loading-mask {
    z-index: 1000;
  }

  ::v-deep .el-loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ::v-deep .el-loading-spinner .circular {
    width: 24px;
    height: 24px;
  }
}

.left-box {
  .info_list {
    height: calc(100vh - 275px);
    overflow-y: auto;
  }
}

.search_loudong {
  background: #f8f8f8;
  height: 41px;
  padding: 0 11px;
  margin-left: 5px;
  font-size: 13px;
  color: #999;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #999;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #999;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #999;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.no_data {
  padding: 50px 0;
  font-size: 14px;
  color: #999;
  background: #fff;

  .no_data_add {
    margin-left: 10px;
    color: #2d84fb;
    cursor: pointer;
  }
}

.refresh {
  margin-top: 8px;
}

.inp_no_border ::v-deep .el-input__inner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

::v-deep .super_mini_Cascader {
  margin-right: 24px;
  font-size: 12px;

  .el-input {
    .el-input__inner {
      padding-right: 0;
      padding-left: 0;
      height: auto;
      line-height: 1;
      border: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: none;
      font-size: 12px;
      color: #8a929f;
    }

    .el-input__suffix {
      right: 0;

      .el-input__suffix-inner {
        .el-input__icon {
          width: auto;
          line-height: 15px;
          font-size: 12px;
          padding-top: 3px;
        }
      }
    }

    // .el-select__tags {
    //   max-width: initial !important;
    // }
  }
}

.myHouse-down-List {
  display: flex;
  flex-direction: column;

  .down-list-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .down-list-content:hover {
    background-color: #f5f7fa;
  }
}

::v-deep .houseDown-container {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .1) !important;
}
</style>
