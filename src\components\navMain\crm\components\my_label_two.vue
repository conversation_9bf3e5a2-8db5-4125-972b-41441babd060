<template>
    <div class="label-box div row">
      <div
        class="label-item"
        v-for="(item, index) in arr"
        :key="index"
        :class="{ isactive: index == styleIndex }"
        @click="onClick(item, index)"
        :style="{ color: item.color }"
      >
        {{ item[labelKey] }}
        <span v-if="item.desc && isdesc">（{{ item.desc }}）</span>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        styleIndex: 0,
      };
    },
    props: {
      arr: {
        type: [Array, Object],
        default: () => [],
      },
      labelKey: {
        type: String,
        default: "name",
      },
      // 是否显示描述
      isdesc: {
        type: Boolean,
        default: false,
      },
      // 当切换客户标签时默认选中第一项
      is_show: {
        type: [Number, String],
        default: 0
      },
      activeIndex: {
        type: [Number, String],
        default: 0
      }
    },
    created() {
      // this.styleIndex = this.activeIndex;
    },
    watch: {
      activeIndex: {
        handler(nval) {
        //  console.log(this.activeIndex, "console.log(this.activeIndex);");
         this.styleIndex = nval
         },
         immediate: true
      }
      // is_show(newval) {
      //   if (newval) {
      //     this.styleIndex = 0;
      //   }
      // }
    },
    methods: {
      onClick(e, index) {
        // console.log(this.arr);
        if (this.styleIndex == index) {
          return;
        }
        this.styleIndex = index;
        this.$emit("onClick", e);
      },
      // 重置筛选时间为全部
      clearScreening() {
        this.styleIndex = 0;
      }
    },
  };
  </script>
  
  <style scoped lang="scss">
  .label-box {
    font-size: 14px;
    cursor: pointer;
    color: #8a929f;
    align-items: center;
    flex-wrap: wrap;
  
    .label-item {
      margin-right: 8px;
      padding: 3px 16px;
      background: #fff;
  
      &.isactive {
        border-radius: 4px;
        background: #e8f1ff;
        // color: #2d84fb !important;
        color: orange !important;
      }
    }
  }
  </style>
  