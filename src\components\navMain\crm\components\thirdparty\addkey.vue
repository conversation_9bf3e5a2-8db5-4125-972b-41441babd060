<template>
    <div>
    
        <el-dialog title="添加秘钥" :visible.sync="showkey"  width="30%"  :before-close="handleClose">
          <div class="head-width">
            <el-form ref="form" :model="keyform" label-width="150px">
                <el-form-item label="密钥标题">
                  <el-input v-model="keyform.title"></el-input>
                </el-form-item>
                <el-form-item label="租户应用AppKey">
                  <el-input v-model="keyform.app_key"></el-input>
                </el-form-item>
                <el-form-item label="租户应用AppSecret">
                  <el-input v-model="keyform.app_secret"></el-input>
                </el-form-item>
                <el-form-item label="租户号">
                  <el-input v-model="keyform.tenant_number"></el-input>
                </el-form-item>
                <el-form-item label="运营主体token">
                  <el-input v-model="keyform.token"></el-input>
                </el-form-item>
                <el-form-item label="消息校验key">
                  <el-input v-model="keyform.encrypt_key"></el-input>
                </el-form-item>
                <el-form-item label="消息校验token">
                  <el-input v-model="keyform.encrypt_token"></el-input>
                </el-form-item>
                <el-form-item label="消息校验租户号">
                  <el-input v-model="keyform.org_code"></el-input>
                </el-form-item>
            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="showkey = false">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
          </span>
        </el-dialog>  
     
    </div>
</template>
<script>
export default {
    data() {
        return {
            showkey:false,
            keyform:{
                title:"",                   //密钥标题
                app_key:"",                 //租户应用AppKey
                app_secret:"",              //租户应用AppSecret
                tenant_number:"",           //租户号
                token:"",                   //运营主体token
                encrypt_key:"",             //消息校验key
                encrypt_token:"",           //消息校验token
                org_code:"",                //消息校验租户号
            },
        }
    },
    methods:{
        open(params){
            // console.log(1212121212,params);
            if(params&&params.id){
                this.keyform.title = params.title
                this.keyform.app_key = params.app_key
                this.keyform.app_secret = params.app_secret
                this.keyform.tenant_number = params.tenant_number
                this.keyform.token = params.token
                this.keyform.encrypt_key = params.encrypt_key
                this.keyform.encrypt_token = params.encrypt_token
                this.keyform.org_code = params.org_code
                this.keyform.id = params.id
                this.showkey = true
            }else{
                this.keyform = {}
                this.showkey = true
            }
        },
        handleClose(){
            this.showkey = false 
        },
        //确定编辑添加秘钥
        confirm(){
            let params = JSON.parse(JSON.stringify(this.keyform))
            // console.log(params)
            let url = "addmingyuankey"
            if(params.id){
                url = "editmingyuankey"
            }
            if(!params.title){
              return this.$message.warning("秘钥标题不能为空")
            }
            if(!params.app_key){
              return this.$message.warning("租户应用AppKey不能为空")
            }
            if(!params.app_secret){
              return this.$message.warning("租户应用AppSecret不能为空")
            }
            if(!params.tenant_number){
              return this.$message.warning("租户号不能为空")
            }
            if(!params.token){
              return this.$message.warning("运营主体token不能为空")
            }
            if(!params.encrypt_key){
              return this.$message.warning("消息校验key不能为空")
            }
            if(!params.encrypt_token){
              return this.$message.warning("消息校验token不能为空")
            }
            if(!params.org_code){
              return this.$message.warning("消息校验租户号不能为空")
            }
            this.$http[url](params).then(res=>{
                if(res.status==200){
                    if(params.id){
                        this.$message.success("编辑成功"); 
                    }else{
                       this.$message.success("添加成功"); 
                    }
                    
                    this.showkey = false
                    this.$emit("getkeylist")
                }
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.head-width{
    width: 90%;
}
</style>