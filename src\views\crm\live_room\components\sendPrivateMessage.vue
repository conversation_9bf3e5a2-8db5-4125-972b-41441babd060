<template>
	<el-dialog :visible.sync="show" title="发私信" width="280px">
        <template #title><div></div></template>
        <!-- <div class="block">
            <span class="label">PC端用户主页</span>  
            <span class="none">{{params.user_pc || '暂无'}}</span>
            <span class="copy" v-if="params.user_pc" @click="copyLink(params.user_pc)">复制链接</span>
        </div> -->
        <div class="block">
            <span class="label">发私信</span>  
            <template v-if="params.ies_uniq_id">
                <div class="qrcode-wrapper" v-loading="getQrcoding">
                    <span class="qrcode" ref="qrcode"></span>
                </div>
                <div class="desc">打开抖音扫一扫</div>
            </template>
            <span class="none" v-else style="padding: 30px 0;">暂无私信二维码</span>
        </div>
    </el-dialog>
</template>
<script>
import QRCode from "qrcodejs2";
export default {
	data() {
		return {
            show: false,    
            getQrcoding: false,  
            params: {           
                user_pc: '',            //PC端用户主页
                ies_uniq_id: '',        //移动端二维码用
            }
        }
	},
	methods: {
        open(params){
            const ies_uniq_id = this.params.ies_uniq_id;
            this.params = params;
            this.show = true;
            if(this.params.ies_uniq_id && this.params.ies_uniq_id != ies_uniq_id){
                this.$nextTick(()=>{
                    this.createQrcode(this.params.ies_uniq_id);
                })
            }
            return this;
        },
        copyLink(link){
            this.$onCopyValue(link);
        },
        async createQrcode(uid){
            this.$refs.qrcode.innerHTML = '';
            this.getQrcoding = true;
            const res = await this.$http.getDouyinChatSchema({account: uid}).catch(()=>{});
            this.getQrcoding = false;
            const schema = res && res.data?.schema || '';
            if(schema){
                new QRCode(this.$refs.qrcode, {
                    text: schema,
                    height: 180,
                    width: 180,
                    correctLevel: QRCode.CorrectLevel.H,
                });
            }
        }

	}
}
</script>
<style  scoped lang="scss">
.block{
    padding: 0 0 12px 0;
    line-height: 1.5;
    display: flex;
    flex-direction: column;
    align-items: center;
    .label{
        display: block;
        color: #3c3c3c;
        font-size: 16px;
        font-weight: 600;
        padding: 0 0 16px;
    }
    .copy{
        cursor: pointer;
        display: inline-block;
        white-space: nowrap;
        margin-left: 12px;
        color: #409EFF;
        transition: opacity .3s;
        &:hover{
            opacity: .8;
        }
    }
    .desc{
        color: #a9a9a9;
        padding: 12px 0 0;
    }
}
.qrcode-wrapper{
    width: 180px;
    height: 180px;
}
::v-deep{
    .el-dialog__header{
        padding-bottom: 30px;
    }
    .el-dialog__body{
        padding-top: 0;
    }
    .el-dialog__headerbtn .el-dialog__close{
        font-size: 22px;
    }
}
</style>