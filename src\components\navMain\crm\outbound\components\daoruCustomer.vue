<template>
  <div>
    <div class="content-box-crm" style="margin-bottom: 24px">
      <div class="tabs">
        <el-tabs v-model="tabName" type="card">
          <el-tab-pane label="导入客户" name="daoru">
            <div class="daoru_con">
              <div class="daoru_title">导入客户</div>
              <div class="flex-box">
                <div class="daoru_Data">
                  <span class="daoru_label">选择数据源：</span>
                  <el-button 
                    @click="showSelectList"
                    type="primary" 
                    size="mini" 
                    plain
                  >
                  CRM
                  </el-button>
                </div>
                <div class="daoru_Data">
                  <span class="daoru_label">数据总量：</span>
                  {{userTotal}}条
                </div>
                <div class="daoru_select">
                  <!-- 线索获量人 -->
                  <div class="daoru_form_item flex-row align-center">
                    <div class="daoru_label">线索获量人：</div>
                    <el-select
                      class="w300"
                      filterable
                      remote
                      reserve-keyword
                      placeholder="请选择线索获量人"
                      :remote-method="getUserList"
                      v-model="retrieval.from_user"
                    >
                      <el-option
                        v-for="item in userList"
                        :key="item.id"
                        :label="item.user_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <!-- 任务包名 -->
                  <div class="daoru_form_item flex-row align-center">
                    <div class="daoru_label">任务包名：</div>
                    <el-input
                      class="w300"
                      placeholder="请输入任务包名"
                      v-model="retrieval.name"
                    >
                    </el-input>
                  </div>
                  <!-- 线索渠道 -->
                  <div class="daoru_form_item flex-row align-center">
                    <div class="daoru_label">线索渠道：</div>
                      <el-select class="w300" v-model="retrieval.channel_id">
                        <el-option
                          v-for="item in channelList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        >
                        </el-option>
                      </el-select>
                  </div>
                  <!-- 分配人员 -->
                  <div class="daoru_form_item flex-row align-center">
                    <div class="daoru_label">分配人员：</div>
                    <el-select
                      class="w300"
                      filterable
                      remote
                      reserve-keyword
                      :multiple="true"
                      placeholder="请选择分配人员"
                      :remote-method="getUserList"
                      v-model="distribution_id"
                    >
                      <el-option
                        v-for="item in userList"
                        :key="item.id"
                        :label="item.user_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="daoru_Data">
                  <span class="daoru_label">分配方式：</span>
                  <el-switch
                    v-model="distribution_type"
                    active-text="平均倒序"
                    inactive-text="平均随机">
                  </el-switch>
                </div>
                <div class="submit">
                  <el-button 
                    type="primary" 
                    :loading="add_loading"
                    @click="daoruTem"
                  >
                    导入
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 筛选框 -->
      <el-dialog
        title="筛选数据"
        :visible.sync="selectVisible"
        >
        <div class="bottom-border div row">
          <span class="text">客户来源：</span>
          <myLabel
            labelKey="title"
            :arr="source_list"
            @onClick="onClickType($event, 1)"
          ></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户状态：</span>
          <myLabel
            labelKey="title"
            :arr="tracking_list"
            @onClick="onClickType($event, 2)"
          ></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户类型：</span>
          <myLabel
            labelKey="title"
            :arr="type_list"
            :isdesc="true"
            @onClick="onClickType($event, 6)"
          ></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户标签：</span>
          <el-cascader
            clearable
            size="small"
            v-model="retrieval.label"
            :options="label_list"
            :props="{
              value: 'id',
              label: 'name',
              children: 'label',
              emitPath: false,
            }"
            @change="handleChangeLabel"
          ></el-cascader>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">筛选时间：</span>
          <myLabel
            :arr="time_list"
            @onClick="onClickType($event, 4)"
          ></myLabel>
          <span class="text">自定义：</span>
          <el-date-picker
            unlink-panels
            style="width: 250px"
            size="small"
            v-model="timeValue"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="onChangeTime"
          >
          </el-date-picker>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">是否认领：</span>
          <myLabel
            :arr="Claim_list"
            @onClick="onClickType($event, 3)"
          ></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">是否去重：</span>
          <myLabel
            :arr="repeat_list"
            @onClick="onClickType($event, 7)"
          ></myLabel>
        </div>
        <span slot="footer" class="dialog-footer">
          <span class="footer_Num">数据总量：
            <span class="footer_strip">{{userTotal}}条</span>
          </span>
          <el-button type="primary" @click="selectVisible = false">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import myLabel from "@/components/navMain/crm/components/my_label.vue";
export default {
  components: {
    myLabel,
  },
  data() {
    return {
      source_list: [], // 客户来源
      tracking_list: [], // 客户状态
      type_list: [], // 客户类型
      label_list: [], // 客户标签
      userTotal: "", // 客户量
      userList: [], // 线索获量人下拉
      channelList: [], // 线索渠道下拉
      // 客户状态接口请求的参数
      tracking_params: {
        type: 1,
      },
      // 线索渠道下拉请求参数
      channel_params: {
        page: 1,
        per_page: 1000,
      },
      // 绑定企微
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      // 是否认领
      Claim_list: [
        { id: 1, name: "待认领" },
        { id: 2, name: "已认领" },
      ],
      // 是否去重
      repeat_list: [
        {id: 0, name: "不去重"},
        {id: 1, name: "去重"},
      ],
      // 筛选时间
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      timeValue: "", // 自定义搜索日期
      add_loading: false,
      push_form: {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        // intention_street: "",
        remark: "",
        add_type: "1",
      },
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 3,
        status: 0,
        mobile: "",
      },
      // 资源客户检索请求参数
      retrieval: {
        label: 0, // 标签id
        source_id: "", // 客户来源
        date_type: "", // 时间类型
        start_date: "", //开始日期
        end_date: "", // 结束时间
        type: "", // 客户类型
        tracking_id: "", // 客户状态
        is_get: 1, // 是否认领
        distribution_id: "", // 线索获量人
        distribution_type: 1, // 分配规则
        from_user: "", // 线索获量人
        channel_id: "", // 线索渠道
        name: "", // 包名
        is_import: 0, // 是否去重0不去重/1去重
      },
      distribution_id: "",
      distribution_type: false,
      tabName: "daoru",
      selectVisible: false,
    };
  },
  mounted() {
    this.getSourceData();
    this.getTrackingList();
    this.getTypelist();
    this.getUserTotal();
    this.getLabelList();
    this.getUserList();
    this.getChannelList();
  },
  methods: {
    // 获取客户来源列表
    getSourceData() {
      this.$http.getCrmCustomerSourceNopage().then((res) => {
        if (res.status === 200) {
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
          this.push_form.source_id = res.data.filter((item) => {
            return item.is_default == 1;
          })[0].id;
        }
      });
    },
    // 获取客户状态
    getTrackingList() {
      this.$http
        .getCrmCustomerFollowInfo({ params: this.tracking_params })
        .then((res) => {
          if (res.status === 200) {
            this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
          }
        });
    },
    // 获取客户类型
    getTypelist() {
      this.$http.getCrmCustomerTypeDataNopage().then((res) => {
        if (res.status === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
          this.push_form.type = res.data.filter((item) => {
            return item.is_default;
          })[0].id;
          let cus_type = parseInt(this.$route.query.cus_type);
          res.data.map((item) => {
            if (cus_type == 1 && item.title == "求购") {
              this.params.type = item.id;
            }
            if (cus_type == 2 && item.title == "求租") {
              this.params.type = item.id;
            }
          });
        }
      });
    },
    // 获取客户标签
    getLabelList() {
      this.$http.getLabelGroupNoPage().then((res) => {
        if (res.status === 200) {
          console.log(res, "获取客户标签");
          this.label_list = res.data;
        }
      });
    },
    // 客户标签当前选中节点发成改变
    handleChangeLabel() {
      this.params.page = 1;
      this.getUserTotal();
    },
    // 自定义日期确认选中
    onChangeTime(e) {
      this.retrieval.start_date = e ? e[0] : "";
      this.retrieval.end_date = e ? e[1] : "";
      this.getUserTotal();
    },
    // 搜索点击事件
    onClickType(e, type) {
      switch (type) {
        case 1:
          this.retrieval.source_id = e.id;
          break;
        case 2:
          this.retrieval.tracking_id = e.id;
          break;
        case 3:
          this.retrieval.is_get = e.id;
          break;
        case 4:
          this.retrieval.date_type = e.id;
          break;
        case 5:
          this.retrieval.level_id = e.id;
          break;
        case 6:
          this.retrieval.type = e.id;
          break;
        case 7:
          this.retrieval.is_import = e.id;
          break;
        default:
          break;
      }
      this.params.page = 1;
      this.getUserTotal();
    },
    // 线索获量人远程方法
    getUserList(e) {
      this.$http.getUserList(1, 10, e ? e : "").then((res) => {
        if (res.status === 200) {
          this.userList = res.data.data;
        }
      });
    },
    // 线索渠道下拉菜单
    getChannelList() {
      this.$http.getChannel(this.channel_params).then((res) => {
        if (res.status === 200) {
          this.channelList = res.data.data;
        }
      });
    },
    // 资源客户总数
    getUserTotal() {
      this.$http.CustomerRetrieval(this.retrieval).then((res) => {
        if (res.status == 200) {
          this.userTotal = res.data.data;
        }
      });
    },
    // 导入
    daoruTem() {
      this.retrieval.distribution_type = Number(this.distribution_type)+1
      this.retrieval.distribution_id = this.distribution_id.toString()
      console.log(this.retrieval)
      this.$http.CustomerImportingClue(this.retrieval).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: '导入成功',
            type: 'success'
          });
        }
      });
    },
    // 弹出筛选框
    showSelectList() {
      this.selectVisible = true
    }
  },
};
</script>
<style scoped lang="scss">
.daoru_Data {
  margin-bottom: 20px;
}
::v-deep.content-box-crm {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
  .tabs {
    width: 900px;
    margin: 10px auto;
    .daoru_con {
      padding: 20px;
      background: #fff;
      .daoru_title {
        text-align: center;
        padding: 30px 0;
        color: #2e3c4e;
        font-size: 20px;
        font-weight: 500;
      }
    }
  }
}
.daoru_select {
  display: flex;
  flex-wrap: wrap;
  .daoru_form_item {
    margin: 0 70px 20px 0;
  }
  .daoru_form_item:nth-child(2), .daoru_form_item:last-child {
    margin-right: 0px;
  }
}
.w300 {
  width: 300px;
}
.submit {
  text-align: center;
}
.bottom-border {
  align-items: center;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
  }
}
.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: flex-end;
  cursor: pointer;
  font-size: 14px;
  .text {
    font-size: 14px;
  }
}
.daoru_form {
  justify-content: center;
  .daoru_form_item {
    margin-right: 70px;
    margin-bottom: 20px;
    // &:nth-child(2n) {
    //   margin-right: 0;
    // }
  }
}
.daoru_label {
  color: #2e3c4e;
  font-size: 14px;
  margin-right: 10px;
  width: 84px;
  display: inline-block;
  text-align: right;
}
.footer_Num {
  color: #8a929f;
  font-size: 14px;
  position: relative;
  top: -20px;
  left: -725px;
}
</style>
