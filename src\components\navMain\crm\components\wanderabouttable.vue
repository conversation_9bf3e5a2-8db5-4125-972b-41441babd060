<template>
    <div class="container">
      <div class="body" v-loading="loading">
        <el-table
          :data="wanderData"
          style="width: 100%"
          border>
          <el-table-column
            prop="id"
            label="ID">
          </el-table-column>
          <el-table-column
            prop="work_name"
            label="任务名称">
          </el-table-column>
          <el-table-column prop="customer_count" label="客户数量" align="center"></el-table-column>
          <el-table-column
            prop="poin_count"
            label="节点数" align="center">
          </el-table-column>
          <el-table-column
            prop="finish_poin_count"
            label="已完成节点数" align="center">
          </el-table-column>
          <el-table-column
            prop="undo_poin_count"
            label="未完成节点数" align="center">
          </el-table-column>
          <el-table-column
            prop="work_status"
            label="任务状态" align="center">
            <template slot-scope="scope">
              <el-tag size="mini" type="success" v-if="scope.row.work_status==1">正常</el-tag>
              <el-tag size="mini" type="danger" v-if="scope.row.work_status==2">禁用</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="operate_name"
            label="操作人" align="center">
          </el-table-column>
          <el-table-column
            prop="created_at"
            min-width="170px"
            label="添加时间" align="center">
          </el-table-column>
          
          <el-table-column
            label="操作"
            width="140px" fixed="right">
            <template slot-scope="scope">
                <el-link type="primary" class="op-btn" @click="edit(scope.row)">编辑</el-link>
                <el-popconfirm class="op-btn" :title="`确定要${scope.row.work_status==1?'禁用':'启用'}该任务吗？`" @onConfirm="setstatus(scope.row)">
                  <el-link type="warning" slot="reference">{{scope.row.work_status==1?'禁用':'启用'}}</el-link>
                </el-popconfirm>
                <el-popconfirm class="op-btn" title="确定要删除该任务吗？" @onConfirm="deletask(scope.row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="footer">
        <div class="paging">
          <div>
              <el-pagination
                background
                layout="total,sizes,prev, pager, next, jumper"
                :total="params.total"
                :page-sizes="[10, 20, 30, 50,100]"
                :page-size="params.per_page"
                :current-page="params.page"
                @current-change="onPageChange"
                @size-change="handleSizeChange"
              >
              </el-pagination>
            </div>
        </div>
      </div>
 
      
        <edit ref="edit" v-if="dialogs.edit"></edit>
    </div>
</template>
<script>
import edit from '@/views/crm/customer/components/add_trans_task.vue';
export default {
  components:{
    edit
  },
    data() {
        return {
            wanderData:[],//流转任务的列表
            params:{},
            datatable:{
              page:1,
              per_page:10,
            },
            loading: false,
            dialogs: {
              edit: false
            }
        }
    },
    created(){
      this.getTransfertasklistA()
    },
    methods:{
      /**
       * 编辑任务
       */
      async edit(row){
        this.dialogs.edit = true;
        await this.$nextTick();
        this.$refs.edit.open(row);
      },
      //获取流转任务列表
      getTransfertasklistA(){
        this.loading = true;
        this.$http.getTransfertasklist(this.datatable).then(res=>{
          this.loading = false;
          if(res.status == 200){
            this.params = res.data
            console.log(res.data.data,"流转任务的列表");
            this.wanderData  = res.data.data
          }
        }).catch(err=>{
          this.loading = false;
          console.log(err);
        })
      },
      onPageChange(current_page) {
        // console.log(current_page);
        this.datatable.page = current_page;
        this.getTransfertasklistA();
      },
      //每页几条
      handleSizeChange(e){
        this.datatable.per_page = e
        this.getTransfertasklistA();
      },
      //修改状态
      setstatus(row){
        let params = {
            auto_work_id : row.id,
        }
        console.log(row);
        if(row.work_status==1){
                params.work_status = 2
                this.$http.setbatchcirculationstatus(params).then(res=>{
                    if(res.status == 200){
                       this.$message({
                        type: 'success',
                        message: '禁用成功!'
                      }); 
                      this.getTransfertasklistA()
                    }
                })
        }else if(row.work_status == 2){
      
                params.work_status = 1
                this.$http.setbatchcirculationstatus(params).then(res=>{
                    if(res.status == 200){
                       this.$message({
                        type: 'success',
                        message: '启用成功!'
                      }); 
                      this.getTransfertasklistA()
                    }
                })
        }
      },
      //删除流转任务
      deletask(row){
                this.$http.delebatchcirculation(row.id).then(res=>{
                    if(res.status == 200){
                       this.$message({
                        type: 'success',
                        message: '删除成功!'
                      }); 
                      this.getTransfertasklistA()
                    }
                })
      },
    }
}
</script>
<style lang="scss" scoped>
.container{
  height: 100%;
  display: flex;
  flex-direction: column;
  .body{
    flex: 1;
    overflow: auto;
    .op-btn{
      line-height: 1;
      +.op-btn{
        margin-left: 10px;
      }
    }
  }
}
.paging{
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>