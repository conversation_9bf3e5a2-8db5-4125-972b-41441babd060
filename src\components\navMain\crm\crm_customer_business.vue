<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm">
          <div class="div row top_row">
            <div class="div row time_search">
              <myLabel ref="childRef" :arr="time_list" :activeIndex="activeIndex" @onClick="onClickType($event, 4)">
              </myLabel>
              <!-- <span class="text">自定义：</span> -->
            </div>
            <div class="div row">
              <el-date-picker style="width: 320px; margin-right: 16px" v-model="p_time" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                @change="changeTimeRange">
              </el-date-picker>
              <el-button v-if="show_export_data > 0" type="primary" @click="newexportData">
                数据导出</el-button>
              <el-button v-if="show_shitu" type="danger" @click="toShitu">
                经营视图
              </el-button>
            </div>
          </div>

          <!-- <div class="zhihui-title"> -->
          <!-- <span> 智慧经营 </span> -->
          <!-- <span @click="onSetting" class="el-icon-newfontCRMshezhi"
              >平台配置</span
            > -->
          <!-- <span
              v-if="is_tabs_page === 'house' && is_show_house"
              @click="onSetting('house')"
              class="el-icon-newfontCRMshezhi"
              >房源设置</span
            > -->
          <!-- </div> -->
          <!-- <div class="check-box-list row div">
            <div class="check-box div row">
              <div
                class="check-item"
                v-for="item in check_list"
                :key="item.id"
                @click="onClickTabs(item)"
                :class="{ isactive: item.id === is_type }"
              >
                {{ item.name }}
              </div>
            </div>
          </div> -->

          <div class="zhibiao-box">
            <!-- v-if="show_export_data > 0 && is_tabs_page != 'marketingSms'" -->
            <!-- <div class="zhibiao-title div row">
              <div class="flex-1">核心指标</div>
            </div> -->
            <div class="" v-if="is_type == 1">
              <div class="bg_fff box_top">
                <el-row class="top_row" :class="{ small: is_small_sys }" :gutter="20">
                  <el-col class="el-col-4-8" v-for="item in customer_form" :key="item.id">
                    <div class="top_item" :style="item.bg_style" @click="clickcard(item)">
                      <div class="top_item_left div row flex-1">
                        <div class="top_item_left_img">
                          <img :src="`${item.images}`" alt="" />
                        </div>
                        <div class="top_item_left_name">
                          {{ item.desc }}
                        </div>
                      </div>
                      <div class="top_item_right div row">
                        <div class="top_item_right_num">
                          {{ item.num | numFilter }}
                        </div>
                        <div class="top_item_right_ratio">
                          <div>{{ item.ratio }}%</div>
                          <div class="unit">占比</div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="titlestyle">
                <div class="tabtitle" v-for="item in tabtitledata" :key="item.id"
                 :class="{addunderline: tabtitleuid==item.id}" @click="tabchange(item.id)">
                  {{ item.name }}
                </div>

              </div>
              <!-- <div class="zhibiao" v-for="item in customer_form" :key="item.id">
                <div class="top div row">
                  <span class="radio">{{ item.ratio }}%</span>
                  <span v-if="item.num" class="r-r-icon up el-icon-caret-top">{{
                    item.num
                  }}</span>
                  <span v-else class="nochange"></span>
                </div>
                <div class="bottom">
                  当前{{ item.title }}级{{ item.desc }}（占比）
                </div>
              </div> -->
            </div>
            <!-- v-else -->
            <div class="zhibiao-item" v-else>
              <!-- A级房源 -->
              <div class="bg_fff box_top">
                <el-row class="top_row" :class="{ small: is_small_sys }" :gutter="20">
                  <el-col class="el-col-4-8" v-for="item in house_form" :key="item.id">
                    <div class="top_item" :style="item.bg_style">
                      <div class="top_item_left div row flex-1">
                        <div class="top_item_left_img">
                          <img :src="`${$imageDomain}/static/admin/waihu/level_${item.title}.png`" alt="" />
                        </div>
                        <div class="top_item_left_name">
                          {{ item.desc }}
                        </div>
                      </div>
                      <div class="top_item_right div row">
                        <div class="top_item_right_num">
                          {{ item.num | numFilter }}
                        </div>
                        <div class="top_item_right_ratio">
                          <div>{{ item.ratio }}%</div>
                          <div class="unit">占比</div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>

          <div class="content-box-crm" style="padding: 24px 0">
            <div :is="is_tabs_page" keep-alive :ref="is_tabs_page" :house_params="house_params"
              @getData="pageChangeGetData" @sortData="sortChangeGetData" :is_house_show="auth_intelligent"
              @getDataOk="getDataOk" :admin_list="admin_list" :show_shitu="show_shitu"
              @custom-event="handleChildEvent" @getheaderData="getheaderData" :optionalsorting="optionalsorting"></div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div v-if="showneedstestres">
      <el-dialog style="width: 1260px;left:16%" :visible.sync="showneedstestres" title="导出数据">
        <div class="texttestres">导出数据 创始人安全验证</div>
        <div class="testresinformation">
          <div class="founderinformation">当前系统创始人：{{admin_list.user_name}} &nbsp;&nbsp; 手机号：{{admin_list.phone}}</div>
        </div>
        <div class="textCode">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="短信验证码" prop="captcha">
              <el-input v-model="ruleForm.captcha"
              placeholder="请输入验证码">
                <el-button slot="append" 
                @click="sendingcode">发送验证码</el-button>
              </el-input>
          </el-form-item>
        </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="showneedstestres=false">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
    </el-dialog>
    </div>
    <smartexport table-name="crm_customer_my_list" ref="smartexport"
    :headerData="headerData" @exportData="exportData" :optionalsorting="optionalsorting"
      v-if="dialogs.smartexport" />
  </div>
</template>

<script>
import house from "./business/house";
import customer from "./business/customer";
import myLabel from "./components/my_label.vue";
import smartexport from "./smart/smartexport.vue"
export default {
  name: "crm_customer_business",
  components: {
    house,
    customer,
    myLabel,
    smartexport
  },
  data() {
    return {
      is_tabs_page: "",
      check_list: [
        { id: 1, name: "客源量化", title: "customer" },
        { id: 2, name: "房源量化", title: "house" },
      ],
      is_type: 1,
      house_zb: {},
      house_sk: {},
      house_vip: {},
      customer_form: [],
      house_form: [],
      auth_intelligent: 0, // 智慧经营是否显示
      house_params: { page: 1 },
      admin_list:{},
      p_time: "",
      show_export_data: 0,
      time_list: [
        { id: 1, name: "全部" },
        { id: 2, name: "今天" },
        { id: 3, name: "昨天" },
        { id: 4, name: "本周" },
        { id: 5, name: "上周" },
        { id: 6, name: "本月" },
        { id: 7, name: "上月" },

        // date_type  1全部 2今天 3昨天 4本周 5上周 6本月 7上月
      ],
      is_small_sys: false,
      website_id: '',
      show_shitu: false,
      activeIndex: 2,
      needs_testres:"",//导出是否需要短信验证，0是需要，1是不需要
      showneedstestres:false,//短信验证弹框\
      // founder:"",//创始人姓名，
      // founderphone:"",//创始人电话
      ruleForm:{
        captcha:""
      },
      rules: {
        captcha: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' },
        ],
      },
      cardstatus:"",
      tabtitleuid:"1",
      tabtitledata:[
        {id:1,name:"分客"},
      ],
      dialogs:{
        smartexport:false,
      },
      headerdata:[],//导出自定义字段
      optionalsorting:[],//导出可选排序列
    };
  },
  created() {
    let query = this.$route.query
    this.website_id = query.website_id
    if (query && query.type) {
      // console.log(query, 'query');
      this.is_type = query.type
      // console.log(this.is_type, 'is_type');//1=crm智慧经营，2=房源智慧经营
      this.check_list = this.check_list.filter(item => item.id == this.is_type)
    }
    this.getPerssion()
    this.determineadmin()
    // this.getIPAddress() 
    this.sortingfieldsdata()
  },
  filters: {
    numFilter(val) {
      let value = +val
      // if (value >= 10000) {
      //   return (value / 10000).toFixed(2) + "W"
      // }
      return value
    },
    percentum(value) {
      let num = value.split("%");
      num[0] = num[0] + ".00";
      return num[0].toString() + '%'
    }
  },
  mounted() {
    let clientWidth = document.body.clientWidth
    if (clientWidth < 1366) {
      this.is_small_sys = true
    } else {
      this.is_small_sys = false
    }
    this.$http.getWebsite(localStorage.getItem("website_id")).then((res) => {
      if (res.status === 200) {
        if (res.data.open_house) {
          // 查询站点是否拥有房源权限
          this.getCheckShow();
        } else {
          // 如果没有开启房源默认展示客源量化页面
          this.is_tabs_page = "customer";
          this.is_type = 1;
        }
        // 获取字典数据
      }
    });

    window.addEventListener('resize', () => {
      let clientWidth = document.body.clientWidth
      if (clientWidth < 1366) {
        this.is_small_sys = true
      } else {
        this.is_small_sys = false
      }
    });
  },
  methods: {
    async getPerssion() {
      let admin_roles = await this.$http.getAdmin().catch(() => {
        console.log();
      })
      this.admin_list = admin_roles.data;
      if (this.admin_list.roles && this.admin_list.roles.length && this.admin_list.roles[0].name == '站长') {
        this.show_shitu = true
        this.tabtitledata.push(      
          {id:2,name:"带看"},
          {id:3,name:"回访"},
          {id:4,name:"维护"},
          {id:5,name:"主播"},
          {id:6,name:"流转"},
          {id:7,name:"成交"})
      } else {
        this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            if ((res.data + '').indexOf(this.admin_list.id) > -1) {
              this.show_shitu = true
              this.tabtitledata.push(      
                {id:2,name:"带看"},
                {id:3,name:"回访"},
                {id:4,name:"维护"},
                {id:5,name:"主播"},
                {id:6,name:"流转"},
                {id:7,name:"成交"})
            }
          }
        })
      }
    },
    //判断是不是客户管理员
    determineadmin(){
      this.$http.determinecustomeradmin().then((res)=>{
        if(res.status==200){
          console.log(res.data," //判断是不是客户管理员");
          this.show_export_data = res.data.is_manager
          this.$store.commit('setismanager', res.data.is_manager);
        }
      })
    },
    // 获取智慧经营显示
    getCheckShow() {
      this.$http.getCheckShow().then((res) => {
        if (res.status === 200) {
          // console.log(res, 'res輸出了');
          this.auth_intelligent = res.data.auth_intelligent;
          this.is_tabs_page = this.check_list[0].title; // 判断当前是客户/房源智慧经营
          this.is_type = this.check_list[0].id; // 判断当前是客户/房源智慧经营
          // this.getHouseBusiness();
        }
      });
    },
    getDataOk(e) {
      // console.log(e, "e");
      if (e.data && e.data.length) {
        this.customer_form = e.data.map((item) => {
          item.ratio = parseFloat(item.ratio * 100).toFixed(2);
          item.bg_style = { backgroundImage: `url(${item.background})` }
          return item;
        });
        // console.log(this.customer_form,"111111");
        // this.show_export_data = 2
      }
      if (e.census && e.census.length) {
        this.house_form = e.census.map((item) => {
          item.ratio = parseFloat(item.ratio * 100).toFixed(2);
          item.bg_style = { backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_${item.title}.png)` }
          return item;
        });
        // this.show_export_data = e.is_export
      }
      // console.log(this.house_form, 'this.house_form');
      // console.log(this.customer_form, 'this.customer_form12312312');
      // console.log(this.is_type, 'is_type121212');
    },
    //接收header数据
    getheaderData(data){
      this.headerData = data
    },
    //导出(打开自定义导出)
    async newexportData(){
      this.dialogs.smartexport = true;
      await this.$nextTick();
      this.$refs.smartexport.open().onSuccess(res => {
        // console.log(res);
      })
    },
    //获取分客数据排序字段
    sortingfieldsdata(){
      this.$http.sortingfieldsdata().then(res=>{
        if(res.status==200){
          this.optionalsorting = res.data.sorts
        }
      })
    },
    //确定导出
    exportData(fieldsdata) {
      let data = this.$refs[this.is_tabs_page].$data
      if(fieldsdata){
        if(fieldsdata.fields){
        data.params.custom_export_field = fieldsdata.fields.join(',')
      }
      if(fieldsdata.sort){
        data.params.sort = fieldsdata.sort
      }
      }
      let params = Object.assign({}, data.params, this.house_params, { "opt_type": "export" })
      this.is_table_loading = true;
      if (this.is_type == 2) {
        this.$http
          .getCrmIntelligentRateList_new({ params })
          .then((res) => {

            this.is_table_loading = false;
            if (res.status === 200) {
              window.open(res.data.url)
            }
          });
      } else {
        this.$http.determinetextmessage().then(res=>{
        if(res.status==200){
          this.needs_testres=res.data
          if(this.is_type==1){
          if(params.date_type==1){
          this.$message({
            type:"warning",
            message:"请选择时间范围，支持单次导出≤189日数据。"
          })
          this.dialogs.smartexport = false;
          return
        }
        if(params.start_date&&params.end_date){
              delete params.date_type
              const startDate = new Date(params.start_date.replace(/-/g, "/")); // 获取开始时间，并将其转换为Date对象
              const endDate = new Date(params.end_date.replace(/-/g, "/")); // 获取结束时间，并将其转换为Date对象、
              // 计算两个日期对象之间的毫秒数差值
              const timeDiff = endDate.getTime() - startDate.getTime();
              // 将毫秒数差值转换为天数
              const days = Math.ceil(timeDiff / (1000 * 3600 * 24));
              console.log(days);
              if(days>189){
                  this.$message({
                  type:"warning",
                  message:" 最多可选范围 189天 "
              })
              this.dialogs.smartexport = false;
            return
              }
          }
        }
        if(this.is_type==2){
          if(!params.department_id){
          delete params.department_id
          }
        }
          this.$http
          .newgetCrmIntelligentRateListCrm({ params })
          .then((res) => {
            this.is_table_loading = false;
            if (res.status === 200) {
              window.open(res.data.file)
            }
          });
        }
      })
      }
    },
    //卡片跳转
    clickcard(item){
      console.log(item);
      console.log(this.activeIndex);
      let text_time = {start:"",end:""}
      let end ="" 
      let start = ""
      if(this.activeIndex>0){
        if(this.activeIndex==1){
         end = new Date();
         start = new Date(end);
        start.setHours(0, 0, 0, 0);
      }else if(this.activeIndex==2){
        end = new Date();
        end.setHours(0, 0, 0, 0);
        start = new Date(end);
        start.setDate(start.getDate() - 1);
      }else if(this.activeIndex==3){
        end = new Date();
        start = new Date(end);
        start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
        start.setHours(0, 0, 0, 0);
      }else if(this.activeIndex==4){
        end = new Date();
        end.setDate(end.getDate() - end.getDay()); // 获取当前周的最后一天
        end.setHours(23, 59, 59, 0);
        start = new Date(end);
        start.setDate(start.getDate() - 6); // 获取上一周的第一天
        start.setHours(0, 0, 0, 0);
      }else if(this.activeIndex==5){
        end = new Date();
        start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
        start.setHours(0, 0, 0, 0);
      }else if(this.activeIndex==6){
        end = new Date();
        end.setDate(0); // 获取上个月的最后一天
        end.setHours(23, 59, 59, 0);
        start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
        start.setHours(0, 0, 0, 0);
      }
      const formatDate = (date) => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        }
        const formattedEnd = formatDate(end);
        const formattedStart = formatDate(start);
        text_time.start = formattedStart
        text_time.end = formattedEnd
      
      }
      const formattedDateArr = {start:"",end:""};
      if(this.p_time){
          const startDate = this.p_time[0].split(' ')[0];
          const endDate = this.p_time[1].split(' ')[0];
          formattedDateArr.start = startDate;
          formattedDateArr.end = endDate;
      }
      const params = this.p_time? formattedDateArr : text_time ;
      console.log(params);
      if(item&&this.cardstatus==1){
         if(item.desc=="客户总量"){
          window.open(`https://yun.tfcs.cn/admin/#/crm_customer_my_list?type=2&status=13&website_id=${this.website_id}`, "_blank");
         }
         if(item.desc=="A级客户"){
          const routeData = this.$router.resolve({
              name: 'crm_customer_my_list',
              query: { type: 2, status: 13, level: item.level_id, website_id: this.website_id, ...params  },
            });
          window.open(routeData.href, '_blank');
         }
         if(item.desc=="B级客户"){
          const routeData = this.$router.resolve({
              name: 'crm_customer_my_list',
              query: { type: 2, status: 13, level: item.level_id, website_id: this.website_id, ...params},
            });
          window.open(routeData.href, '_blank');
         }
         if(item.desc=="C级客户"){
          const routeData = this.$router.resolve({
              name: 'crm_customer_my_list',
              query: { type: 2, status: 13, level: item.level_id, website_id: this.website_id, ...params },
            });
          window.open(routeData.href, '_blank');
         }
      }
    },
    //tab改变
    tabchange(id){
      this.tabtitleuid = id
      if(id==2){
        this.$goPath(`/lookdatalist`)//带看
      }else if(id==3){
        this.$goPath(`/Followupview`)//回访
      }else if(id==4){
        this.$goPath(`/maintenanceview`)//维护
      }else if(id==5){
          this.$goPath("/ananchorpresent")//主播
      }else if(id==6){
          this.$goPath("/wanderabout")//流转
      }else if(id==7){
          this.$goPath("/reportforms")//报表
      }
    },
    handleChildEvent(value) {
      // 处理从子组件传递过来的值
      this.cardstatus = value
    },
    //发送短信验证
    sendingcode(){
      this.$http.sendingcode().then(res=>{
        if(res.status==200){
          this.$message({
            type:"success",
            message:"发送成功！"
          })
        }
      })
    },
    //短信验证框确定
    confirm(){
      this.$http.test_verifycode(this.ruleForm).then(res=>{
        if(res.status==200){
          this.$message({
            type:"success",
            message:"验证成功！"
          })
         this.exportData()
        }
      })
    },

    onClickType(e) {
      // console.log(e, 'e');
      // let params = Object.assign({}, this.house_params)
      if (e) {
        this.house_params.date_type = e.id
        this.p_time = null
        delete this.house_params.end_date
        delete this.house_params.start_date
      }
      this.activeIndex = this.time_list.findIndex(item => item.id == e.id) >= 0 ? this.time_list.findIndex(item => item.id == e.id) : 0
      this.house_params.page = 1;
      this.$refs[this.is_tabs_page].getCrmIntelligentRateList && this.$refs[this.is_tabs_page].getCrmIntelligentRateList();
      this.$refs[this.is_tabs_page].getCrmIntelistFn && this.$refs[this.is_tabs_page].getCrmIntelistFn();
      // this.getDataList();
      // console.log(this.$refs[this.is_tabs_page], 'this.$refs[this.is_tabs_page]');
      // console.log(this.time_list, 'time_list');
    },
    sortChangeGetData() {
      this.house_params.page = 1;
      this.$refs[this.is_tabs_page].getCrmIntelligentRateList && this.$refs[this.is_tabs_page].getCrmIntelligentRateList();
    },
    // 获取信息展示
    getCrmIntelligentRateCrm() {
      // if(this.website_id==176||this.website_id==109){
        this.$http.newgetCrmIntelligentRateListcst().then((res) => {
        if (res.status === 200) {
          // console.log(res, 'res45454545454');
          this.customer_form = res.data.map((item) => {
            item.ratio = parseFloat(item.ratio * 100).toFixed(2);
            item.bg_style = { backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_${item.title}.png)` }
            return item;
          });
        }
      });
      // }else{
      //   this.$http.getCrmIntelligentRateListcst().then((res) => {
      //   if (res.status === 200) {
      //     // console.log(res, 'res45454545454');
      //     this.customer_form = res.data.map((item) => {
      //       item.ratio = parseFloat(item.ratio * 100).toFixed(2);
      //       item.bg_style = { backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_${item.title}.png)` }
      //       return item;
      //     });
      //   }
      // });
      // }
     
    },
    // getHouseBusiness() {
    //   this.$http.getCrmIntelligentRateHouse().then((res) => {
    //     if (res.status === 200) {
    //       console.log(res.data);
    //       this.house_zb = res.data.house_a;
    //       this.house_sk = res.data.house_sk;
    //       this.house_vip = res.data.house_vip;
    //     }
    //   });
    // },
    onClickTabs(e) {
      this.is_type = e.id;
      this.is_tabs_page = e.title;
    },
    changeTimeRange(e) {
      if(e){
        if (e[1].endsWith("00:00:00")) {
        e[1] = e[1].slice(0, -8) + "23:59:59";
      }
      }
      delete this.house_params.date_type
      this.house_params.start_date = e ? e[0] : "";
      this.house_params.end_date = e ? e[1] : "";
      this.house_params.page = 1;
      // console.log( this.house_params);
      if(this.house_params.start_date&&this.house_params.end_date){
        this.activeIndex = 0
      }else{
        this.activeIndex = 2
      }
      this.$forceUpdate()
      this.$refs[this.is_tabs_page].getCrmIntelligentRateList && this.$refs[this.is_tabs_page].getCrmIntelligentRateList();
      this.$refs[this.is_tabs_page].getCrmIntelistFn && this.$refs[this.is_tabs_page].getCrmIntelistFn();
      // this.getCrmIntelligentRateList();
    },
    toShitu() {
      this.$goPath("/crm_Follow_up_list")
    },
    pageChangeGetData(e) {
      this.house_params.page = e
      this.$forceUpdate()
      this.$refs[this.is_tabs_page].getCrmIntelligentRateList && this.$refs[this.is_tabs_page].getCrmIntelligentRateList();
    },
    onSetting() {
      let topSelect = sessionStorage.getItem("top_menu_info") ? JSON.parse(sessionStorage.getItem("top_menu_info")) : {}
      if (topSelect && topSelect.home_url && topSelect.home_url.includes("/house_list")) {
        this.$router.push(`crm_customer_business_setting?type=setting`);
      } else {
        if (this.is_tabs_page === "customer") {
          this.$router.push(`crm_customer_business_setting?type=crm`);
        }
        if (this.is_tabs_page === "house") {
          this.$router.push(`crm_customer_business_setting?type=house`);
        }
      }

    },
  },
};
</script>

<style scoped lang="scss">
.top_row {
  justify-content: space-between;
  padding-bottom: 10px;
}

.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .zhihui-title {
    display: flex;
    cursor: pointer;
    font-size: 24px;
    align-items: center;
    justify-content: space-between;
    color: #1c212a;
  }

  .check-box-list {
    cursor: pointer;
    justify-content: space-between;
    align-items: center;

    .pingtai {
      font-size: 20px;
      color: #607080;
    }
  }

  .check-box {
    margin-top: 24px;
    margin-left: 1rem;
    margin-bottom: 22px;
    font-size: 20px;
    color: #607080;

    .check-item {
      margin-right: 68px;
      position: relative;

      &.isactive {
        color: #0083ff;

        &::after {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          content: "";
          height: 4px;
          background: #2d84fb;
          width: 106px;
          display: block;
          margin-top: 18px;
        }
      }
    }
  }

  .zhibiao-box {
    border-top: 1px solid #dde1e9;
    border-bottom: 1px solid #dde1e9;
    padding: 26px 28px;
    margin-left: -24px;
    margin-right: -24px;

    .zhibiao-title {
      align-items: center;
      color: #1c212a;
      font-size: 20px;
    }
    .titlestyle{
      display: flex;
      margin-left:13px ;
      .tabtitle{
        width: 39px;
        height: 30px;
        border: 2px solid #ffff;
        margin-bottom: -26px;
        text-align: center;
        color:#909399;
        cursor: pointer;
        margin-right: 35px;
      }
      .addunderline{
        border-bottom-color:  #409EFF;
        color: #409EFF;
      }
    }
    .zhibiao-item {
      .bg_fff {
        background: #fff;
      }

      .box_top {
        padding: 20px;
      }

      .el-col-4-8 {
        width: 20%;
      }

      .small {
        .el-col-4-8 {
          width: 25%;
        }

        .top_item {
          margin-bottom: 20px;
        }
      }

      .top_item {
        // background: #3333;
        padding: 30px;
        border-radius: 10px;
        height: 130px;
        margin-bottom: 10px;
        background-repeat: no-repeat;
        box-sizing: border-box;
        background-size: 100% 100%;

        .top_item_left {
          .top_item_left_img {
            width: 28px;
            height: 28px;
            overflow: hidden;
            border-radius: 50%;
            align-self: flex-start;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .top_item_left_name {
            margin-left: 10px;
            font-size: 18px;
            color: #fff;
          }
        }

        .top_item_right {
          align-self: center;
          justify-content: flex-end;
          align-items: center;
          color: #fff;
          font-size: 30px;
          font-weight: 800;

          .top_item_right_ratio {
            font-size: 12px;
            color: #fff;
            margin-left: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .unit {
              margin-top: 3px;
            }
          }
        }
      }
    }
  }
}

.nochange {
  color: #2d84fb;
}

.time_search {
  align-items: center;

  ::v-deep .label-item {
    margin-right: 0;
  }

  .text {
    font-size: 14px;
  }
}

.marbot28 {
  margin-bottom: 28px;
}

.bg_fff {
  background: #fff;
}

.box_top {
  padding: 20px;
}

.el-col-4-8 {
  width: 20%;
}

.small {
  .el-col-4-8 {
    width: 25%;
  }

  .top_item {
    margin-bottom: 20px;
  }
}

.top_item {
  // background: #3333;
  padding: 30px;
  border-radius: 10px;
  height: 130px;
  margin-bottom: 10px;
  background-repeat: no-repeat;
  box-sizing: border-box;
  background-size: 100% 100%;

  .top_item_left {
    .top_item_left_img {
      width: 28px;
      height: 28px;
      overflow: hidden;
      border-radius: 50%;
      align-self: flex-start;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .top_item_left_name {
      margin-left: 10px;
      font-size: 18px;
      color: #fff;
    }
  }

  .top_item_right {
    align-self: center;
    justify-content: flex-end;
    align-items: center;
    color: #fff;
    font-size: 30px;
    font-weight: 800;

    .top_item_right_ratio {
      font-size: 12px;
      color: #fff;
      margin-left: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .unit {
        margin-top: 3px;
      }
    }
  }
}
.texttestres{
  text-align: center;
  font-size: 23px;
  margin-bottom: 20px;
}
.testresinformation{
  width: 90%;
  height: 80px;
  margin: 0 auto;
  border-radius:5px;
  background-color: rgb(255, 235, 229);
  .founderinformation{
    text-align: center;
    line-height: 76px;
    font-size: 18px;
  }
}
.textCode{
  width: 64%;
  height: 50px;
  margin: 10px 24px;
  margin-top: 15px;
  /deep/.el-form-item__content{
    margin-left: 11px !important;
  }
  // /deep/.el-input__inner{
  //   width: 66% !important;
  // }
}
</style>
