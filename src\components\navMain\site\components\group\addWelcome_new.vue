<template>
  <div>
    <!-- <div class="tips">
      <div>
        1.在二维码处设置的欢迎语会被优先推送，如果成员在二维码处设置了欢迎语，在此设置的欢迎语
        不会生效
      </div>

      <div>
        2.欢迎语将在客户加为好友后20秒内发下，因网络延可能造成发送不成功
      </div>
    </div> -->
    <el-form label-width="100px">
      <el-form-item label="群欢迎语">
        <div class="form-item-block">
          <el-input
            v-model="welcome_mes.text.desc"
            style="margin-right: 12px"
            placeholder="请输入群欢迎语"
            type="textarea"
          ></el-input>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="是否通知">
        <div class="form-item-block align-center flex-wrap">
          <el-switch v-model="notify" active-color="#2D84FB"> </el-switch>
        </div>
      </el-form-item>
      <el-form-item label="欢迎语">
        <welcome-mes
          style="padding-right: 20px"
          ref="group_welcome"
          :value="welcome_mes"
        ></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>

import config from "@/utils/config";
import welcomeMes from "../../../crm/components/welcome_mes"
export default {
  components: {
    welcomeMes
  },
  data() {
    return {
      form_params: {

      },
      notify: true,
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      cover: "",
      imgList: [],
      show_select_dia: false,
      currentImg: "",
      params_type: 2,
      img_params: {
        page: 1,
        per_page: 20,
        type: 1,
      },
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
          pic_url: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      params_type_arr: [],
      filename: "",
      videoname: "",
      imgname: "",
      miniCover: "",
    };
  },
  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      // _welcome_mes.text = JSON.parse(JSON.stringify(this.welcome_mes.text))
      let _welcome_mes = this.$refs.group_welcome.welcome_mes

      if (_welcome_mes.text && (!_welcome_mes.text.desc)) {
        this.$message.warning("欢迎语不能为空");
        return;
      }
      if (_welcome_mes.image) {
        if (!_welcome_mes.image.media_id) {
          delete _welcome_mes.image;
        }
      }
      console.log(_welcome_mes);
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key]) {
            emptyLink.push(key);
          }
        }
        if (emptyLink.length == Object.keys(linkArr).length) {
          emptyLink.length = 0;
          delete _welcome_mes.link;
        } else if (emptyLink.length) {
          console.log(12323);
          this.$message.warning(linkArr[emptyLink[0]]);
          return;
        }
      }
      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (!_welcome_mes.miniprogram[key]) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          emptyMini.length = 0;
          delete _welcome_mes.miniprogram;
        } else if (emptyMini.length) {
          this.$message.warning(miniArr[emptyMini[0]]);
          return;
        }
      }

      if (_welcome_mes.video && (!_welcome_mes.video.media_id)) {
        delete _welcome_mes.video;
      }
      if (_welcome_mes.file && (!_welcome_mes.file.media_id)) {
        delete _welcome_mes.file;
      }
      params.welcome_msg = JSON.stringify(_welcome_mes);
      if (this.notify) {
        params.notify = 1
      } else {
        params.notify = 0
      }
      params.welcome_msg = JSON.stringify(_welcome_mes);

      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .addCrmGroupWelcomeWord(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success(res.data.msg || "添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error(res.data.msg || "添加失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    showSelectDia() {
      this.getImgList();
      this.show_select_dia = true;
    },
    // 选中图片
    selectAvatar(e) {
      this.currentImg = e.url;
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      this.loadMore = "";
      switch (this.params_type) {
        case 2:
        case 3:
        case 4:
          this.img_params.type = 1;
          break;
        case 5:
          this.img_params.type = 3;
          break;
        case 6:
          this.img_params.type = 4;
          break;

        default:
          break;
      }
      this.$http.getCrmServiceAvatarList(this.img_params).then((res) => {
        if (res.status == 200) {
          if (this.img_params.type == 4) {
            res.data.data.map((item) => {
              item.user_name = item.url.substring(
                item.url.lastIndexOf("/") + 1
              );
              return item;
            });
          }
          this.imgList = this.imgList.concat(res.data.data);
          if (res.data.data.length == this.img_params.per_page) {
            this.loadMore = true;
          } else {
            this.loadMore = false;
          }
        }
      }).catch(() => {
        this.loadMore = false
      });
    },
    cancel() {
      this.$emit("cancel");
    },
    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.params_type) {
        case 2:
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          this.welcome_mes.image.pic_url = url;
          this.welcome_mes.image.media_id = res.data.media_id;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.picurl = url;

          break;
        case 4:
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          // 小程序 miniprogram   media_id
          this.miniCover = url;
          this.welcome_mes.miniprogram.pic_media_id = res.data.media_id;
          break;
        case 5:
          // 视频
          params.type = 3;
          console.log(url);
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败 请检查素材大小");
            return;
          });

          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case 6:
          // 文件
          params.type = 4;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败");
          });
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
    },
    // 选中图片确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      switch (this.params_type) {
        case 2:
          // 图片
          this.welcome_mes.image.media_id = current.media_id;
          this.welcome_mes.image.pic_url = current.url;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.picurl = current.url;

          break;
        case 4:
          // 小程序 miniprogram   media_id
          this.miniCover = current.url;
          // this.welcome_mes.miniprogram.pic_url = url;
          this.welcome_mes.miniprogram.pic_media_id = current.media_id;
          break;
        case 5:
          // 视频
          this.videoname = current.url;
          this.welcome_mes.video.media_id = current.media_id;
          break;
        case 6:
          // 文件
          this.filename = current.url;
          this.welcome_mes.file.media_id = current.media_id;
          break;

        default:
          break;
      }
      this.show_select_dia = false;
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    radioChange(e) {
      console.log(e
      );
      if (e) {
        this.params_type = e;
        var category = config.CATEGORY_IM_IMAGE;
        switch (e) {
          case 2:
          case 3:
          case 4:
            category = config.CATEGORY_IM_IMAGE;
            break;
          case 5:
            category = config.CATEGORY_IM_VIDEO;
            break;
          case 6:
            category = config.CATEGORY_IM_FILE;
            break;
          default:
            break;
        }
        this.website_img = `/api/common/file/upload/admin?category=${category}`;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 30px;
  &.type_tips {
    margin-bottom: 0;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 115px;
    height: 115px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;
    &.active {
      border: 5px solid #409eff;
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    .file_img {
      width: 120px;
      height: 120px;
      text-align: center;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.el-form-item ::v-deep .el-radio {
  margin-right: 0;
  &.active {
    border: 1px solid #a6e22e;
  }
}
.upload-demo {
  margin-right: 5px;
}
</style>
