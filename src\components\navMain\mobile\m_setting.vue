<template>
  <el-row>
    <el-col :span="8">
      <div class="fix-box">
        <div class="top">
          <img src="https://img.tfcs.cn/backup/static/admin/h5/wx_head_bg.png" alt="" />
          <img v-show="setting_list.button_style == 1" src="https://img.tfcs.cn/backup/static/admin/h5/fg1.png" alt="" />
          <img v-show="setting_list.button_style == 2" src="https://img.tfcs.cn/backup/static/admin/h5/fg2.png" alt="" />
        </div>
        <div class="title">{{ setting_list.build_menu_name }}</div>
        <div class="content" v-show="setting_list.build_menu_style == 1">
          <img v-for="(img, index) in type1" :key="index" :src="img" alt="" />
        </div>
        <div class="content" v-show="setting_list.build_menu_style == 2">
          <img v-for="(img, index) in type2" :key="index" :src="img" alt="" />
        </div>
        <img src="https://img.tfcs.cn/backup/static/admin/h5/4.png" alt="" />
      </div>
    </el-col>
    <el-col :span="16">
      <el-tabs type="border-card">
        <el-tab-pane v-for="item in tab_list" :key="item.id" :label="item.name">
          <el-form :model="setting_list" label-width="100px">
            <el-card class="box-card">
              <div slot="header">
                <span>轮播设置</span>
              </div>
              <el-form-item label="开启轮播：">
                <el-radio v-model="setting_list.carousel" :label="1">开启</el-radio>
                <el-radio v-model="setting_list.carousel" :label="2">关闭</el-radio>
              </el-form-item>
              <el-form-item label="轮播高度：">
                <el-input v-model="setting_list.carousel_height" placeholder="请输入轮播高度" style="width:300px" type="number"
                  min="210" step="1">
                </el-input>
              </el-form-item>
            </el-card>
            <el-card class="box-card">
              <div slot="header">操作按钮</div>
              <el-form-item label="样式：">
                <el-radio v-model="setting_list.button_style" :label="1">风格一</el-radio>
                <el-radio v-model="setting_list.button_style" :label="2">风格二</el-radio>
              </el-form-item>
            </el-card>
            <el-card class="box-card">
              <div slot="header">
                <span>项目设置</span>
              </div>
              <el-form-item label="自定义名称：">
                <el-input v-model="setting_list.build_menu_name" placeholder="请输入" style="width:300px"></el-input>
              </el-form-item>
              <!-- <el-form-item label="菜单风格：">
                <el-radio v-model="setting_list.build_menu_style" :label="1"
                  >风格一</el-radio
                >
                <el-radio v-model="setting_list.build_menu_style" :label="2"
                  >风格二</el-radio
                >
              </el-form-item> -->
            </el-card>
            <el-button style="margin-top:20px" type="primary" v-loading="is_button_loading" :disabled="is_button_loading"
              @click="onCreate">确认</el-button>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: "m_setting",
  data() {
    return {
      type1: [
        "https://img.tfcs.cn/backup/static/admin/h5/3.png",
        "https://img.tfcs.cn/backup/static/admin/h5/3.png",
        "https://img.tfcs.cn/backup/static/admin/h5/3.png",
        "https://img.tfcs.cn/backup/static/admin/h5/3.png",
        "https://img.tfcs.cn/backup/static/admin/h5/3.png",
      ],
      type2: [
        "https://img.tfcs.cn/backup/static/admin/h5/2.png",
        "https://img.tfcs.cn/backup/static/admin/h5/2.png",
        "https://img.tfcs.cn/backup/static/admin/h5/2.png",
        "https://img.tfcs.cn/backup/static/admin/h5/2.png",
        "https://img.tfcs.cn/backup/static/admin/h5/2.png",
      ],
      setting_list: {
        carousel: 1,
        carousel_height: "180",
        build_menu_style: 1,
        build_menu_name: "推荐项目",
        button_style: 1,
      },
      tab_list: [{ id: 1, name: "首页设置" }],
      is_button_loading: false,
    };
  },
  mounted() {
    this.getSetting();
  },
  methods: {
    getSetting() {
      this.$http.getWebsiteIndexCustom().then((res) => {
        if (res.status === 200) {
          if (res.data.id) {
            this.setting_list = res.data;
          }
        }
      });
    },
    onCreate() {
      this.is_button_loading = true;
      this.$http.setWebsiteIndexCustom(this.setting_list).then((res) => {
        this.is_button_loading = false;
        if (res.status === 200) {
          this.$message.success("设置成功");
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.fix-box {
  width: 303px;
  margin-left: 100px;

  img {
    width: 100%;
  }

  .top {}

  .title {
    padding: 10px 20px;
    font-size: 15px;
  }

  .content {
    height: 300px;
    overflow: auto;
  }

  /* for Chrome */

  .content::-webkit-scrollbar {
    display: none;
  }
}
</style>
