<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->
    <el-form
      label-width="100px"
      :model="form"
      :rules="rules"
      label-position="right"
    >
      <el-form-item label="广告名称" prop="name">
        <el-input
          class="input-short"
          placeholder="请输入名称"
          type="text"
          v-model="form.name"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="广告时间">
        <div class="block">
          <el-date-picker
            v-model="form.start_at"
            type="date"
            placeholder="选择开始日期"
            value-format="yyyy-MM-dd"
            style="width:200px"
          >
          </el-date-picker>
          <i style="font-style:normal; color:#748a8f; margin:0 10px">至</i>
          <el-date-picker
            v-model="form.end_at"
            type="date"
            placeholder="选择结束日期"
            value-format="yyyy-MM-dd"
            style="width:200px"
          >
          </el-date-picker>
        </div>
      </el-form-item>
      <el-form-item label="选择城市" v-if="website_info.city_type == 2">
        <el-cascader
          clearable
          :props="props"
          @change="handleChange"
          v-model="region_value"
          :options="region_list"
          style="width: 300px"
        ></el-cascader>
      </el-form-item>
      <template v-if="form.position_id!=6">
      <el-form-item label="跳转类型">
        <el-select v-model="form.jump_category" placeholder="请选择跳转类型">
          <el-option
            v-for="item in jump_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  请谨慎添加外部网站链接，小程序内不支持打开非业务域名链接。
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
      </el-form-item>
      
      <el-form-item label="跳转地址">
        <el-input
          class="input-short"
          placeholder="请输入地址"
          type="text"
          v-model="form.link_url"
        >
        </el-input>
      </el-form-item>

      <el-form-item label="排序方式">
        <el-input placeholder="请输入排序方式"  v-model="form.sort"></el-input>
      </el-form-item>
    </template>

      <el-form-item label="广告类型">
        <el-select v-model="form.type" placeholder="请选择广告类型" @change ='typeChange'>
          <el-option
            v-for="item in type_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="form.type==2?'广告视频':'广告图片'">
        <div class="div row upload">
          <el-upload
            class="avatar-uploader"
            :show-file-list="false"
            :headers="myHeader"
            :action="adv_img"
            :on-success="handleSuccessAdv"
            list-type="picture-card"
            :accept="accept"
            :on-preview="handlePictureCardPreviewAdv"
            :on-remove="handleRemoveAdv"
          >
            <img v-if="form.img &&form.type==1" width="148px" :src="form.img" class="avatar" />
            <video v-if="form.img &&form.type==2" style="width:148px;height:148px"  :src="form.img"></video>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <!-- <img v-if="form.img" width="148px" :src="form.img" alt=""/> -->
          </el-upload>
          <el-dialog :visible.sync="advVisible">
            <img width="100%" :src="advImageUrl" alt="" />
          </el-dialog>
          <!-- <i class="el-icon-info tips" style="color:#909399;"
            >请上传9:16竖屏广告素材，为不影响加载速度文件大小控制在3m左右。</i
          > -->
        </div>
      </el-form-item>
      <el-form-item size="large">
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
/* eslint-disable */
import config from "@/utils/config";
import { mapState } from "vuex";
export default {
  name: "add_advertising",
  data() {
    return {
      form: {
        position_id: "",
        name: "",
        start_at: "",
        end_at: "",
        jump_category: 0,
        link_url: "",
        img: "",
        id: 0,
        region_0: "",
        region_1: "",
        sort:0,
        type:1
      },
      advVisible: false,
      advImageUrl: "",
      jump_list: [
        { id: 0, name: "H5页面" },
        { id: 1, name: "小程序地址" },
        { id: 2, name: "外部网站" },
      ],
      type_list:[
      { id: 1, name: "图片" },
        { id: 2, name: "视频" },
      ],
      id: "",
      rules: {
        name: [{ required: true, trigger: "blur", message: "请输入广告名称" }],
      },
      accept:'.jpg,.jpeg,.png,.gif,.blob',
      adv_img: `/api/common/file/upload/admin?category=${config.ADV_IMG}`,
      region_list: [],
      props: {
        label: "name",
        value: "id",
      },
      region_value: [],
    };
  },
  mounted() {
    let params = this.$route.query;
    this.form.position_id = params.position_id;
    if (params.id) {
      this.form.id = params.id;
      this.getQueryList(params.id);
    }
    this.getRegion();
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
    ...mapState(["website_info"]),
  },
  methods: {
    getRegion() {
      this.$http.getRegionList().then((res) => {
        if (res.status === 200) {
          this.region_list = this.$toTree(res.data);
        }
      });
    },
    // 公司主图
    getQueryList(id) {
      this.$http.queryAdv(id).then((res) => {
        if (res.status === 200) {
          this.form = res.data;
          if (res.data.region_1) {
            this.region_value = [res.data.region_0, res.data.region_1];
          } else {
            this.region_value = [res.data.region_0];
          }
        }
      });
    },
    handleChange(e) {
      this.form.region_0 = e[0] || 0;
      this.form.region_1 = e[1] || 0;
    },

    typeChange(e){
      if(this.form.type==1){
        this.adv_img =`/api/common/file/upload/admin?category=${config.ADV_IMG}`
        this.accept ='.jpg,.jpeg,.png,.gif,.blob'
      }else if (this.form.type==2) {
        this.adv_img =`/api/common/file/upload/admin?category=${config.ADV_VIDEO}`
        this.accept ='.mp4,.avi,.flv'
      }
    },
    handleRemoveAdv(file, fileList) {},
    handlePictureCardPreviewAdv(file) {
      this.advImageUrl = file.response.url;
      this.advVisible = true;
    },
    handleSuccessAdv(response) {
      this.form.img = response.url;
    },
    goBack() {
      this.$router.back();
    },
    onSubmit() {
      if (!this.form.name) {
        this.$message({
          message: "请输入广告名称",
          type: "error",
        });
        return;
      }
      for (var prop in this.form) {
        if (this.form[prop] === "") {
          delete this.form[prop];
        }
      }
      if (this.form.id) {
        this.$http
          .updataAdv({
            id: this.form.id,
            position_id: this.form.position_id,
            jump_category: this.form.jump_category,
            name: this.form.name,
            start_at: this.form.start_at,
            end_at: this.form.end_at,
            link_url: this.form.link_url,
            img: this.form.img,
            region_0: this.form.region_0,
            region_1: this.form.region_1,
            sort:this.form.sort,
            type:this.form.type
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.$router.back();
            }
          });
      } else {
        this.$http.createAdv(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.$router.back();
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.input-short {
  width: 300px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-form {
// 	padding-top: 30px;
// }
.el-input {
  width: 300px;
}
.el-select {
  width: 300px;
}
.el-textarea {
  width: 300px;
}
.el-date-editor {
  width: 133px;
}
.upload {
  align-items: flex-end;
  .avatar-uploader{
    overflow: hidden;
  }
  .tips {
    margin-left: 10px;
  }
}
</style>
