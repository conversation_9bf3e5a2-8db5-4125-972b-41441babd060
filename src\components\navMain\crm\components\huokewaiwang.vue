
<template>
  <div class="outbound">
    <div class="tabs">
      <div class="check-box div row" id="pages_content">
        <template v-for="item in tabs_list">
          <div @click="onClickTabs(item)" :class="{ isactive: item.title === is_tabs_page }" :key="item.id"
            class="check-item">
            {{ item.name }}
          </div>
        </template>
      </div>
    </div>
    <div class="bor-t" style="background: #fff; padding: 24px 65px" :is="is_tabs_page" keep-alive></div>
  </div>
</template>

<script>

import douyinSetting from "./douyinSetting"
import weixinManger from './weixinManger'
import leaflet from './leaflet/leafletIndex.vue'
import customer_record from "./customer_record/index.vue"
// import weixinSetting from "./weixinSetting"
import mingyun from "./thirdparty/mingyun.vue"
export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    // weixinSetting,
    weixinManger,
    // eslint-disable-next-line vue/no-unused-components
    douyinSetting,
    leaflet,
    customer_record,
    mingyun
  },
  props: {
    query_type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabs_list: [
        {
          id: 2,
          title: "douyinSetting",
          name: '抖音小程序管理'
        },
        {
          id: 3,
          title: "weixinManger",
          name: '微信小程序管理'
        },
        {
          id: 4,
          title: "",
          name: '领资料插件'
        },
        {
          id: 5,
          title: "leaflet",
          name: '挂载单页'
        },
        {
          id: 6,
          title: "customer_record",
          name: '获客记录'
        },
        // { id: 7,
        //   title: "mingyun",
        //   name: '云客同步'}

      ],
      is_tabs_page: "",

    }
  },
  created() {
    // if (this.query_type) {
    //   this.is_tabs_page = this.query_type

    // }
    this.website_id = this.$route.query.website_id || localStorage.getItem("website_id")
     if(this.website_id==1||this.website_id==701){
      this.tabs_list.push(
        { id: 7,
          title: "mingyun",
          name: '云客同步'}
      ) 
    }
    this.getSetting()
  },
  computed: {

  },
  methods: {
    onClickTabs(item) {
      if (item.id == 4) {
        this.$goPath('/collarmaplist')
        return
      }
      this.is_tabs_page = item.title
    },
    async getSetting() {
      console.log(this.tabs_list);
      // let weixin_result = await this.$http.queryXiaoApp()
      let douyin_result = await this.$http.getWebsite(this.website_id)
      console.log(douyin_result.data.website_mode_show
        , '777');
      let creArray = douyin_result.data.website_mode_show.split(',')
      console.log(creArray,'0000');
      let ee = creArray.includes('2')
      console.log(ee, '7777');
      if (ee) {
        console.log(11);
      } else {
        console.log(22);
      }
      if(this.website_id!=701){
        if (!ee) {
          this.tabs_list.pop()
        }
      }
      // if (weixin_result.data.id == 0) {
      //   this.tabs_list = this.tabs_list.filter(item => item.id != 3)
      // }
      if (douyin_result.data.open_mini_build_program == 0 && douyin_result.data.open_mini_erp_program == 0) {
        this.tabs_list = this.tabs_list.filter(item => item.id != 2)
      }
      this.is_tabs_page = this.tabs_list.length ? this.tabs_list[0].title : ""
    }
  }
}
</script>

<style scoped lang="scss">
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}

.mr10 {
  margin-right: 10px;
}

.w-300 {
  width: 300px;
}

.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  margin-left: 20px;

  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;

    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}

.tabs {
  margin-left: 44px;
}

.w_300 {
  width: 300px;
}

.tab_content {
  padding: 40px 44px;
}

.btns {
  padding-left: 44px;
}

.table_oper {
  padding: 16px 0;
}

.balance {
  padding: 0px 44px;

  .sms-border {
    margin-top: 10px;
    padding: 24px 0;
    border-bottom: 1px solid #eee;
    margin-left: -24px;
    margin-right: -24px;

    .sms-item {
      padding: 12px 0;
      flex: 1;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-right: 1px solid #eee;
      cursor: pointer;

      .max-w-160 {
        max-width: 160px;
      }

      .w-160 {
        width: 160px;
      }

      .t {
        color: #0083ff;
        font-size: 32px;
      }

      .b {
        font-size: 14px;
        color: #768196;
      }

      .btn {
        padding: 9px 0;
        color: #fff;
        text-align: center;
        border-radius: 5px;
      }

      .b1 {
        margin-bottom: 14px;
        background: #17b63a;
      }

      .b2 {
        background: #fd7979;
      }
    }
  }
}
</style>