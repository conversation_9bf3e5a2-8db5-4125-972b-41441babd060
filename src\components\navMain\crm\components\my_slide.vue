<template>
  <div class="rightNav div row">
    <ul class="tab-nav">
      <li v-for="(i, index) in navList" :key="index">
        <div class="nav" @click="openNav(index, i.list.length)">
          <i style="margin-right: 12px" class="el-icon-caret-bottom"></i>
          <span>{{ i.title }}</span>
          <!--
            !!!!:
              elementui中特殊组件阻止事件冒泡：特殊组件外层添加标签 加入@click.stop事件阻止事件冒泡 
          -->
          <div class="endp" @click.stop>
            <el-popover placement="right" trigger="click">
              <ul class="menu-list">
                <li
                  @click="onClickMenu(item)"
                  v-for="item in menu_list"
                  :key="item.id"
                  class="menu-item"
                >
                  {{ item.name }}
                </li>
              </ul>
              <i slot="reference" id="right-btn" class="el-icon-more"></i>
            </el-popover>
          </div>
        </div>
        <div class="nav-n-box">
          <div
            @click.stop="onClickItem(n)"
            class="nav-n div row"
            v-for="(n, index) in i.list"
            :key="index"
          >
            <span>
              {{ n.title }}
            </span>
            <div @click.stop class="c-more">
              <el-popover placement="right" trigger="click">
                <ul class="menu-list">
                  <li
                    @click="onClickMenu(item)"
                    v-for="item in menu_list"
                    :key="item.id"
                    class="menu-item"
                  >
                    {{ item.name }}
                  </li>
                </ul>
                <i slot="reference" id="right-btn" class="el-icon-more"></i>
              </el-popover>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <el-dialog width="548px" :visible.sync="delete_dialog" title="提示">
      <div class="text-center">
        请删除此部门下的成员或子部门后，再删除此部门
      </div>
      <span class="dialog-footer" slot="footer">
        <el-button @click="delete_dialog = false">取 消</el-button>
        <el-button @click="onCreateDelete" type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      delete_dialog: false,
    };
  },
  props: {
    navList: {
      type: Array,
      default: () => [],
    },
    menu_list: {
      type: Array,
      default: () => [
        { id: 1, name: "添加子部门" },
        { id: 2, name: "修改名称" },
        { id: 3, name: "删除" },
        { id: 4, name: "下移" },
      ],
    },
  },
  methods: {
    openNav (index, num) {
      let nav = document.querySelectorAll(".nav"); //获取父级菜单栏，以便添加选中样式
      let items = document.querySelectorAll(".nav-n-box"); //获取容纳子级菜单栏的容器，以便动态设置高度，实现下拉效果

      //-----------------可注释部分开始------注释后则不是手风琴效果------------------
      // 遍历菜单栏，移除所有选中后的样式   添加此段可实现手风琴效果，注释则实现多展示效果
      for (let i = 0; i < nav.length; i++) {
        // nav[index].classList.contains("nav-n-box-active") 判断标签内是否含有该class属性，以布尔值类型返回
        if (
          items[i].style.height == "" ||
          items[i].style.height == "0rem" ||
          nav[index].classList.contains("nav-n-box-active")
        ) {
          let height = items[index].style.height;
          items[index].style.height = height;
        } else {
          items[i].style.height = "0rem";
        }
        nav[i].classList.remove("nav-n-box-active");
      }
      //-----------------可注释部分结束------------------------

      //根据子菜单栏的高度判断，是否展开菜单栏，若有进行遍历操作，那么每次点击某个菜单栏的时候 height 都为 0
      if (
        items[index].style.height == "" ||
        items[index].style.height == "0rem"
      ) {
        //num 为子菜单栏的个数，根据子菜单栏确定容器的高度
        items[index].style.height = num * 2 + "rem";
        //添加右箭头旋转样式
        nav[index].classList.add("nav-n-box-active");
      } else {
        items[index].style.height = "0rem";
        //移除右箭头旋转样式
        nav[index].classList.remove("nav-n-box-active");
      }
      //------------------------------------------
    },
    onClickMenu (e) {
      if (e.id === 3) {
        this.delete_dialog = true;
      }
    },
    onClickItem (item) {
      this.$emit("onClickItem", item);
    },
    onCreateDelete () {
      console.log("删除");
    },
  },
};
</script>

<style scoped lang="scss">
.rightNav {
  z-index: 100;
  padding: 12px;
  .tab-nav {
    list-style: none;
    width: 100%;
    li {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      cursor: pointer;
      user-select: none;
      .nav {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 12px 0;
        position: relative;
        i {
          transition: 0.3s;
        }
        span {
          display: inline-block;
          text-align: left;
          color: #808080;
          font-size: 0.88rem;
        }
        .endp {
          position: absolute;
          right: 0;
        }
      }
      .nav-n-box {
        transition: 0.3s;
        width: 100%;
        height: 0;
        overflow: hidden;
        .nav-n {
          width: 100%;
          font-size: 0.88rem;
          color: #808080;
          height: 2rem;
          padding-left: 2rem;
          line-height: 2rem;
          transition: 0.3s;
          position: relative;
          align-items: center;
          .c-more {
            position: absolute;
            right: 0;
          }
          &:hover {
            background: #e8f1ff;
            color: #2d84fb;
          }
        }
      }
    }
  }
}
#right-btn {
  transform: rotate(90deg) !important;
}
.menu-list {
  cursor: pointer;
  .menu-item {
    font-size: 14px;
    color: #8a929f;
    line-height: 35px;
    padding-left: 12px;
    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}
.text-center {
  display: flex;
  justify-content: center;
}
</style>
