<template>
<div class="comm-container">
    <div class="comm-statistics">
        <span class="comm-statistics-label">统计属性</span>
        <span>成交人：<span>{{ reportData.admin ? reportData.admin.user_name: '--' }}</span></span>
        <span style="margin:0px 20px;">成交时间：<span>{{ reportData.deal ? reportData.deal.cjrq:'--' }}</span></span>
        <span>应收佣金 <span class="color-red">{{ receiveCommissionTotal || '--' }}</span>；</span>
        <span>公司佣金 <span class="color-red">{{pureCompanyCommissionTotal || '--'}}</span>；</span>
        <span>扣除款项 <span class="color-red">{{companyDeductionTotal|| '--'}}</span>；</span>
        <span>业绩提成 <span class="color-red">{{memberCommissionTotal|| '--'}}</span>；</span>
        <span>回款 <span class="color-red">{{paymentAmountTotal || '--'}}</span></span>
    </div>

    <div class="tabs" v-loading="loading" v-if="!loading">
        <div class="tab-header">
            <div class="tab-header-item" :class="{'current': currentTabName == tab.name}" @click="onTabClick(tab.name)" v-for="tab in tabs" :key="tab.name">
                {{tab.label}} <span class="color-red">{{ tab.count }}</span> {{tab.unit}}
            </div>
        </div>
        <div class='tab-body'>
            <keep-alive>
                <component :is="currentTabName" :report-data="reportData" v-on="$listeners" class="tab-body-content" ref="commComponent"></component>
            </keep-alive>
        </div>
    </div>
</div>
</template>
  
<script>
import receiveCommission from "@/views/crm/deal_report/receive_commission.vue";
import companyCommission from "@/views/crm/deal_report/company_commission.vue";
import memberCommission from "@/views/crm/deal_report/member_commission.vue";
import paymentLogs from "@/views/crm/deal_report/payment_logs.vue";
import reportFiles from "@/views/crm/deal_report/report_files.vue";
export default {
    components: {
        receiveCommission, companyCommission, memberCommission, paymentLogs, reportFiles
    },
    props: {
        reportData: { type: Object, default: ()=>{return {}} }
    },
    data() {
        return {
            loading: true,
            currentTabName: 'receiveCommission',  //当前选中的tabName
            tabs: [
                { label: '应收佣金', name: 'receiveCommission', count: 0, unit: '个' },
                { label: '扣除款项', name: 'companyCommission', count: 0, unit: '笔' },
                { label: '业绩提成', name: 'memberCommission', count: 0, unit: '笔' },
                { label: '回款记录', name: 'paymentLogs', count: 0, unit: '个' },
                { label: '附件凭证', name: 'reportFiles', count: 0, unit: '个' }
            ]   
        };
    },
    computed: {
        //当前选中的tab
        currentTab(){
            return this.tabs.find(e=>e.name == this.currentTabName);
        },
        //应收佣金数量
        receiveCommissionCount(){
            return this.reportData?.receive_commission?.count || 0;
        },
        //公司佣金量
        companyCommissionCount(){
            return this.reportData?.company_commission?.count || 0;
        },
        //应收佣金合计
        receiveCommissionTotal(){
            return this.reportData?.receive_commission?.commission || 0;
        },
        //公司佣金合计
        companyCommissionTotal(){
            return this.reportData?.company_commission?.commission || 0;
        },
        //公司佣金-纯公司佣金
        pureCompanyCommissionTotal(){
            return this.reportData?.reality_commission || 0;
        },
        //公司佣金-扣除款项合计
        companyDeductionTotal(){
            let amount = this.companyCommissionTotal - this.pureCompanyCommissionTotal;
            return amount ? amount.toFixed(2) : 0
        },
        //业绩提成
        memberCommissionTotal(){
            return this.reportData?.member_commission?.commission || 0;
        },
        //回款金额
        paymentAmountTotal(){
            return this.reportData?.payment?.commission || 0;
        }
    },    
    watch: {
        reportData: {
            handler(val){ 
                if(val){
                    const countRels = [
                        {name: 'receiveCommission', count: this.receiveCommissionCount},
                        {name: 'companyCommission', count: this.companyCommissionCount},
                        {name:'memberCommission', count: this.reportData?.member_commission?.count || 0},
                        {name: 'paymentLogs', count: this.reportData?.payment?.count || 0},
                        {name: 'reportFiles', count: this.reportData?.files?.count || 0}
                    ];
                    for(const rel of countRels){
                        const index = this.tabs.findIndex(e=>e.name == rel.name);
                        if(index !== -1){
                            this.tabs[index].count = rel.count;
                        }
                    }
                    if(val.report_id){
                        this.loading = false;
                    }
                }
            },
            immediate: true
        },
        currentTabName: {
            handler(val){
                this.$emit('update:currentTabName', val);
            },
            immediate: true
        }
    },
    methods: {
        onTabClick(name){
            this.currentTabName = name;
        }
    },
};
</script>
  
<style lang="scss" scoped>
.comm-container {
    min-height: 200px;
    width: 100%;
    border: 1px solid #DDE1E9;
    margin: 0 0 6px;

    .comm-statistics {
        display: flex;
        flex-wrap: wrap;
        padding: 14px 35px;
        align-items: center;
        border-bottom: 1px solid #DDE1E9;
        .comm-statistics-label{
            font-weight: 600;
            margin-right: 28px;
        }
    }
    .tabs {
        position: relative;
        .tab-header {
            height: 50px;
            background-color: #F2F3F5;
            border-bottom: 1px solid #DDE1E9;
            line-height: 50px;
            display: flex;
            .tab-header-item {
                width: 167px;
                height: 50px;
                text-align: center;
                line-height: 50px;
                cursor: pointer;
                overflow: hidden;
                white-space: nowrap;
                &.current {
                    background-color: #fff;
                }
            }
        }
        .tab-body{
            position: relative;
            padding:0px 20px 20px;
            box-sizing: border-box;
            .tab-body-header{
                position: absolute;
                left: 100px;
                right: 25px;
                top: 28px;
                text-align: right;
            }
            .tab-body-content{
                .footer{
                    height: auto;
                    padding-top: 10px;
                }
            }
        }
        .tab-bottom{
            position: absolute;
            left: 0;
            right: 250px;
            bottom: -45px;
            padding-left: 100px;
            text-align: center;
        }
        
    }

    .color-red{
        color: #f40;
    }
    .commission-text{
        font-size: 16px;
        color: #6c6c6c;
        .commission-text-red{
            color: #f40;
            font-weight: 600;
        }
    }
}
</style>