<template>
  <div class="page">
    <div class="container">
      <div class="container-header">
        <el-button type="primary" @click="addMapPlugin">添加资料</el-button>
        <el-button type="primary" @click="shareRank">分享排行总榜</el-button>
        <el-button type="primary" @click="customer">总获客记录</el-button>
      </div>
      <div class="container-main">
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="table_header"></myTable>
        <div class="paging-box">
          <el-pagination background :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
            :row-style="$TableRowStyle" @current-change="handleCurrentChange" :current-page="search_params.page"
            :page-size="search_params.per_page" layout="total, prev, pager, next" :total="total">
          </el-pagination>
        </div>
      </div>
      <!-- 新增地图插件 -->
      <el-dialog title="添加" :visible.sync="addDialog" width="900px" :close-on-click-modal="false">
        <el-form ref="form" :model="add_params" label-width="110px">
          <el-form-item label="标题资料" prop="title">
            <template slot="label">
              <div>
                资料标题<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input style="width: 460px" v-model="add_params.title" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="头部背景图片(可为空)" prop="top_pic">
            <div class="flex-row">
              <div :style="add_params.top_pic ? 'border: 1px solid #8c939d;' : ''" v-if="add_params.top_pic"
                class="uploader-box">
                <div class="uploader-content">
                  <img :src="add_params.top_pic" alt="" />
                  <div class="uploader-shade flex-row">
                    <span class="uploader-show">
                      <i @click="showMapDialog(add_params.top_pic)" class="el-icon-zoom-in"></i>
                    </span>
                    <span class="uploader-delete">
                      <i @click="deleteHeaderBG" class="el-icon-delete"></i>
                    </span>
                  </div>
                </div>
              </div>
              <el-upload v-if="!add_params.top_pic" class="uploader-create" :headers="myHeader"
                :on-error="(e) => headerBGerror(e)" :action="picture_upurl" :on-success="(e) => headerBGSuccess(e)"
                accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf" :show-file-list="false">
                <div class="uploader-box">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <div class="hint_text">
            <i class="el-icon-info" style="color: rgb(0, 206, 255)"></i>
            上传资料图片后，可通过拖动图片改变图片位置
          </div>
          <el-form-item label="上传地图" prop="maps">
            <template slot="label">
              <div>
                上传锚点图片<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <div class="flex-row" style="flex-wrap: wrap">
              <div :style="item.url ? 'border: 1px solid #8c939d;' : ''" style="margin-bottom: 60px" class="uploader-box"
                v-for="(item, index) in picture_list" :key="index">
                <div v-if="item.url" class="uploader-content flex-box">
                  <img :src="item.url" alt="" @dragover="onDragOver" @dragend="onDrageEnd(index)" />
                  <div class="mini-delete" @click="deleteMap(item.url)">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                  </div>
                </div>
                <div class="anchor-box">
                  <el-input v-model="item.title" placeholder="请输入"></el-input>
                </div>
              </div>
              <el-upload class="uploader-create" :headers="myHeader" :on-error="(e) => UploadParamserror(e)"
                :action="picture_upurl" :on-success="(e) => UploadParamsSuccess(e)"
                accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf" :show-file-list="false" :multiple="true">
                <div class="uploader-box">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <!-- <el-form-item label="领取模式" prop="receive_type">
            <el-radio-group v-model="add_params.receive_type">
              <el-radio :label="1">直接保存</el-radio>
              <el-radio :label="2">发送网盘地址</el-radio>
              <el-radio :label="3">浏览放大</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <!-- <template v-if="add_params.receive_type != 3">
            <el-form-item label="浏览限制" prop="browse_auth">
              <el-radio-group v-model="add_params.browse_auth">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">开启</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="浏览限制时间"
              v-show="add_params.browse_auth == 1"
              prop="browse_time"
            >
              <template slot="label">
                <div>
                  浏览限制时间<span style="margin-left: 5px; color: #f56c6c"
                    >*</span
                  >
                </div>
              </template>
              <el-input
                style="width: 460px"
                v-model="add_params.browse_time"
                placeholder="请输入"
              >
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
          </template> -->
          <el-form-item label="授权失效时间" prop="auth_expire_time">
            <el-input style="width: 460px" v-model="add_params.auth_expire_time" placeholder="请输入">
              <template slot="append">分钟</template>
            </el-input>
          </el-form-item>
          <!-- <el-form-item
            label="网盘地址"
            v-show="add_params.receive_type == 2"
            prop="netdisk_url"
          >
            <template slot="label">
              <div>
                网盘地址<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input
              @input="inputUpdate"
              style="width: 460px"
              v-model="add_params.netdisk_url"
              placeholder="请输入"
            ></el-input>
          </el-form-item> -->
          <!-- <el-form-item
            label="网盘密码"
            v-show="add_params.receive_type == 2"
            prop="netdisk_code"
          >
            <template slot="label">
              <div>
                网盘密码<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input
              @input="inputUpdate"
              style="width: 460px"
              v-model="add_params.netdisk_code"
              placeholder="请输入"
            ></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="线索类型" prop="customer_type">
            <el-radio-group v-model="add_params.customer_type">
              <el-radio :label="1">进公海</el-radio>
              <el-radio :label="2">进私客</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="分享标题" prop="share_title">
            <template slot="label">
              <div>
                分享标题<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input style="width: 460px" v-model="add_params.share_title" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="分享描述" prop="share_desc">
            <template slot="label">
              <div>
                分享描述<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input style="width: 460px" type="textarea" :rows="2" v-model="add_params.share_desc" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item label="分享图片" prop="share_pic">
            <template slot="label">
              <div>
                分享图片<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <div class="flex-row">
              <div :style="add_params.share_pic ? 'border: 1px solid #8c939d;' : ''
                              " v-if="add_params.share_pic" class="uploader-box">
                <div class="uploader-content">
                  <img :src="add_params.share_pic" alt="" />
                  <div class="uploader-shade flex-row">
                    <span class="uploader-show">
                      <i @click="showMapDialog(add_params.share_pic)" class="el-icon-zoom-in"></i>
                    </span>
                    <span class="uploader-delete">
                      <i @click="deleteShareImg" class="el-icon-delete"></i>
                    </span>
                  </div>
                </div>
              </div>
              <el-upload v-if="!add_params.share_pic" class="uploader-create" :on-error="(e) => uploadShareerror(e)"
                :headers="myHeader" :action="picture_upurl" :on-success="(e) => uploadShareSuccess(e)"
                accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf" :show-file-list="false">
                <div class="uploader-box">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="获客公海成员">
            <el-input placeholder="请选择成员" v-model="zuoxi_user.name" size="small" style="width: 150px;margin: 0px 0px;"
              @focus="showMemberList">
              <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close"></i></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="addDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmAdd" :loading="is_loading">确 定</el-button>
        </span>
        <el-dialog class="inlay" width="950px" :visible.sync="previewDialog" append-to-body>
          <div>
            <img :src="preview_url" alt="" style="width: 100%; height: 100%; object-fit: cover" />
          </div>
        </el-dialog>
      </el-dialog>
      <!-- 编辑地图插件 -->
      <el-dialog title="编辑" :visible.sync="editDialog" width="900px" :close-on-click-modal="false">
        <el-form ref="editform" :model="edit_params" label-width="110px">
          <el-form-item label="资料标题" prop="title">
            <template slot="label">
              <div>
                资料标题<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input style="width: 460px" v-model="edit_params.title" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="头部背景图片" prop="top_pic">
            <div class="flex-row">
              <div :style="edit_params.top_pic ? 'border: 1px solid #8c939d;' : ''" v-if="edit_params.top_pic"
                class="uploader-box">
                <div class="uploader-content">
                  <img :src="edit_params.top_pic" alt="" />
                  <div class="uploader-shade flex-row">
                    <span class="uploader-show">
                      <i @click="showMapDialog(edit_params.top_pic)" class="el-icon-zoom-in"></i>
                    </span>
                    <span class="uploader-delete">
                      <i @click="editDeleteHeaderBG" class="el-icon-delete"></i>
                    </span>
                  </div>
                </div>
              </div>
              <el-upload v-if="!edit_params.top_pic" class="uploader-create" :on-error="(e) => editHeaderBGerror(e)"
                :headers="myHeader" :action="picture_upurl" :on-success="(e) => editHeaderBGSuccess(e)"
                accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf" :show-file-list="false">
                <div class="uploader-box">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <div class="hint_text">
            <i class="el-icon-info" style="color: rgb(0, 206, 255)"></i>
            上传地图后，可通过拖动图片改变图片位置
          </div>
          <el-form-item label="上传地图" prop="maps">
            <template slot="label">
              <div>
                上传锚点图片<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <div class="flex-row" style="flex-wrap: wrap">
              <div :style="item.url ? 'border: 1px solid #8c939d;' : ''" style="margin-bottom: 60px" class="uploader-box"
                v-for="(item, index) in edit_params.maps" :key="index">
                <div v-if="item.url" class="uploader-content flex-box">
                  <img :src="item.url" alt="" @dragover="editDragOver" @dragend="editDrageEnd(index)" />
                  <div class="mini-delete" @click="editDeleteMap(item.url)">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                  </div>
                </div>
                <div class="anchor-box">
                  <el-input v-model="item.title" placeholder="请输入"></el-input>
                </div>
              </div>
              <el-upload class="uploader-create" :headers="myHeader" :action="picture_upurl"
                :on-success="(e) => editUploadParamsSuccess(e)" :on-error="(e) => editUploadParamserror(e)"
                accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf" :show-file-list="false" :multiple="true">
                <div class="uploader-box">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <!-- <el-form-item label="领取模式" prop="receive_type">
            <el-radio-group v-model="edit_params.receive_type">
              <el-radio :label="1">直接保存</el-radio>
              <el-radio :label="2">发送网盘地址</el-radio>
              <el-radio :label="3">浏览放大</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <!-- <template v-if="edit_params.receive_type != 3">
            <el-form-item label="浏览限制" prop="browse_auth">
              <el-radio-group v-model="edit_params.browse_auth">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">开启</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="浏览限制时间"
              v-show="edit_params.browse_auth == 1"
              prop="browse_time"
            >
              <template slot="label">
                <div>
                  浏览限制时间<span style="margin-left: 5px; color: #f56c6c"
                    >*</span
                  >
                </div>
              </template>
              <el-input
                style="width: 460px"
                v-model="edit_params.browse_time"
                placeholder="请输入"
              >
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
          </template> -->
          <el-form-item label="授权失效时间" prop="auth_expire_time">
            <el-input style="width: 460px" v-model="edit_params.auth_expire_time" placeholder="请输入">
              <template slot="append">分钟</template>
            </el-input>
          </el-form-item>

          <!-- <el-form-item
            label="网盘地址"
            v-show="edit_params.receive_type == 2"
            prop="netdisk_url"
          >
            <template slot="label">
              <div>
                网盘地址<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input
              @input="inputUpdate"
              style="width: 460px"
              v-model="edit_params.netdisk_url"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="网盘密码"
            v-show="edit_params.receive_type == 2"
            prop="netdisk_code"
          >
            <template slot="label">
              <div>
                网盘密码<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input
              @input="inputUpdate"
              style="width: 460px"
              v-model="edit_params.netdisk_code"
              placeholder="请输入"
            ></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="线索类型" prop="customer_type">
            <el-radio-group v-model="edit_params.customer_type">
              <el-radio :label="1">进公海</el-radio>
              <el-radio :label="2">进分享成员私客</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="分享标题" prop="share_title">
            <template slot="label">
              <div>
                分享标题<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input style="width: 460px" v-model="edit_params.share_title" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="分享描述" prop="share_desc">
            <template slot="label">
              <div>
                分享描述<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-input style="width: 460px" type="textarea" :rows="2" v-model="edit_params.share_desc" placeholder="请输入">
            </el-input>
          </el-form-item>
          <el-form-item label="分享图片" prop="share_pic">
            <template slot="label">
              <div>
                分享图片<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <div class="flex-row">
              <div :style="edit_params.share_pic ? 'border: 1px solid #8c939d;' : ''
                              " v-if="edit_params.share_pic" class="uploader-box">
                <div class="uploader-content">
                  <img :src="edit_params.share_pic" alt="" />
                  <div class="uploader-shade flex-row">
                    <span class="uploader-show">
                      <i @click="showMapDialog(edit_params.share_pic)" class="el-icon-zoom-in"></i>
                    </span>
                    <span class="uploader-delete">
                      <i @click="editDeleteShareImg" class="el-icon-delete"></i>
                    </span>
                  </div>
                </div>
              </div>
              <el-upload v-if="!edit_params.share_pic" class="uploader-create" :on-error="(e) => editUploadSharerror(e)"
                :headers="myHeader" :action="picture_upurl" :on-success="(e) => editUploadShareSuccess(e)"
                accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf" :show-file-list="false">
                <div class="uploader-box">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <!-- <el-form-item label="公海成员">
            <template slot="label">
              <div>
                获客进公海成员范围<span style="margin-left: 5px; color: #f56c6c">*</span>
              </div>
            </template>
            <el-select v-model="edit_id" placeholder="公海成员" style="width: 460px;">
              <el-option v-for="item in project_list" :key="item.id" :label="item.user_name" :value="item.id"></el-option>
            </el-select>
          </el-form-item> -->

          <el-form-item label="获客公海成员">
            <el-input placeholder="请选择成员" v-model="zuoxi_user.name" size="small" style="width: 150px;margin: 0px 0px;"
              @focus="showMemberList">
              <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close"></i></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="editDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmEdit" :loading="is_loading">确 定</el-button>
        </span>
        <el-dialog class="inlay" width="950px" :visible.sync="previewDialog" append-to-body>
          <div>
            <img :src="preview_url" alt="" style="width: 100%; height: 100%; object-fit: cover" />
          </div>
        </el-dialog>
      </el-dialog>
    </div>
    <el-dialog title="小程序二维码" :visible.sync="showQR" width="560px">
      <div>
        <el-radio-group v-model="getQR_params.env_version">
          <el-radio label="release">正式版</el-radio>
          <!-- <el-radio label="trial">体验版</el-radio>
          <el-radio label="develop">备选项</el-radio> -->
        </el-radio-group>
        <el-button style="margin-left: 20px" type="primary" @click="getQRcode">获取二维码</el-button>
      </div>
    </el-dialog>
    <el-dialog class="inlay" width="300px" :visible.sync="codeVisible">
      <div class="QR-box">
        <img :src="QRcode" alt="" />
      </div>
    </el-dialog>
    <el-dialog :title="sharetitle" :visible.sync="share_dialog" width="960px">
      <myTable v-loading="share_table_loading" :table-list="share_tableData" :header="share_table_header"></myTable>
      <div style="text-align: end; margin-top: 24px">
        <el-pagination background layout="total,prev, pager, next" :total="share_params.total"
          :page-size="share_params.per_page" :current-page="share_params.page" @current-change="sharePageChange">
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog :title="contitle" :visible.sync="recode_dialog" width="1400px">
      <div class="picker-all-box" v-if="this.picker == true">
        <el-date-picker style="width: 220px" v-model="p_time" type="datetimerange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" size="small"
          @change="changeTimeRange">
        </el-date-picker>
        <el-input placeholder="请选择成员" v-model="zuoxi_user.name" size="small" style="width: 150px;margin: 0px 15px;"
          @focus="showMemberList">
          <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close"></i></el-input>
        <el-button size="small" type="primary" @click="exportExcel">导出</el-button>
      </div>
      <myTable v-loading="recode_table_loading" :table-list="recode_tableData" :header="recode_table_header"></myTable>
      <div style="text-align: end; margin-top: 24px">
        <el-pagination background layout="total,prev, pager, next" :total="recode_params.total"
          :page-size="recode_params.per_page" :current-page="recode_params.page" @current-change="recodePageChange">
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_select_dia" width="660px" title="选择成员">
      <memberListSingle v-if="show_select_dia" :list="memberList" @onClickItem="selecetedMember"></memberListSingle>
    </el-dialog>
  </div>
</template>
<script>
import config from "@/utils/config.js";
import myTable from "@/components/components/my_table";
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
export default {
  components: {
    myTable,
    memberListSingle,
  },
  data() {
    return {
      zuoxi_user: {  //选择人员信息
        id: '',
        name: ''
      },
      memberList: [], //成员树弹框绑定的值
      show_select_dia: false, //成员筛选树弹框
      user_name: "", //总榜成员筛选input绑定
      p_time: "",
      picker: false, //总获客记录搜索条件绑定的状态
      contitle: '',  //获客与总获客绑定的title
      order_by: 'share_count', //排行总榜传的值
      addDialog: false, // 添加地图插件模态框
      editDialog: false, // 编辑地图插件模态框
      // 添加地图插件接口参数
      add_params: {
        title: '', // 地图标题
        top_pic: '', // 头部背景
        // browse_auth: 1, // 浏览限制：0:关闭，1:开启
        // browse_time: 1, // 开启浏览限制后，此参数是必填（单位分钟）
        auth_expire_time: '', // 授权失效时间（单位分钟）
        // receive_type: 1, // 地图模式;1:直接保存，2：发送网盘地址 3：浏览放大
        // netdisk_url: '', // 网盘地址；开启网盘领取此参数必填
        // netdisk_code: '', // 网盘密码；开启网盘领取此参数必填
        customer_type: 1, // 1:所有客户线索进公海 2:按分享成员进私客(授权或表单提交的手机号和名称)
        share_title: '', // 分享标题
        share_pic: '', // 分享图片
        share_desc: '', // 分享描述
        maps: [], // 地图，json字符串

      },
      // picture_upurl: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
      picture_upurl: `/api/admin/map_plugin/upload_pic?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
      picture_list: [], // 地图图片列表(最大三个)
      previewDialog: false, // 预览dialog
      preview_url: '', // 预览图片地址
      targetSrc: '', // 当前要交换位置的图片
      is_loading: false, // laoding加载
      is_table_loading: false, // 表格loading
      tableData: [], // 表格数据
      total: 0, // 表格总条数
      // 地图插件列表请求参数
      search_params: {
        page: 1,
        per_page: 10,
      },
      edit_id: '',
      edit_params: {}, // 编辑地图插件参数
      showQR: false, // 控制二维码模态框
      QRcode: '', // 微信小程序二维码链接
      share_dialog: false, // 控制分享排行模态框
      recode_dialog: false, // 控制获客记录模态框
      share_table_loading: false, // 分享表格loading
      share_tableData: [], // 分享表格数据
      sharetitle: '', //分享弹框title
      share_state: 1,
      share_params: {
        plugin_id: '',
        page: 1,
        per_page: 10,
        total: 0
      },
      recode_params: {
        plugin_id: '',
        name: '',
        phone: '',
        page: 1,
        per_page: 10,
        total: 0,
        times: '',
        admin_id: '',
      },
      export_params: {
        times: '',
        plugin_id: '',
        admin_id: ''
      }, //导出传参
      recode_table_loading: false, // 获客记录表格loading
      recode_tableData: [], // 获客记录表格参数
      // 获取微信二维码参数
      getQR_params: {
        env_version: 'release', // 正式版:release 体验版:trial 开发版: develop
      },
      nowlist_data: {}, // 当前点击的列表数据
      codeVisible: false,
      // 表格头部数据
      table_header: [
        { prop: "title", label: "资料标题" },
        // {
        //   prop: 'browse_auth',
        //   label: '浏览限制',
        //   width: 100,
        //   render: (h, data) => {
        //     const types = data.row.browse_auth == 0 ? '关闭' : data.row.browse_auth == 1 ? '开启' : '-----';
        //     return (<div>{types}</div>)
        //   }
        // },
        // {
        //   prop: 'browse_time',
        //   label: '浏览限制时间',
        //   width: 120,
        //   render: (h, data) => {
        //     return (
        //       <div>{data.row.browse_time ? data.row.browse_time + '分钟' : '无限制'}</div>
        //     )
        //   }
        // },
        {
          prop: 'auth_expire_time',
          label: '权限失效时间',
          render: (h, data) => {
            return (
              <div>{data.row.auth_expire_time ? data.row.auth_expire_time + '分钟' : '永久有效'}</div>
            )
          }
        },
        // {
        //   prop: 'receive_type',
        //   label: '地图模式',
        //   width: 120,
        //   render: (h, data) => {
        //     const types = data.row.receive_type == 1 ? '直接保存' : data.row.receive_type == 2 ? '发送网盘地址' : data.row.receive_type == 3 ? '浏览放大' : '-----';
        //     return (<div>{types}</div>)
        //   }
        // },
        {
          prop: 'created_at',
          label: '创建时间',
          render: (h, data) => {
            return (<div>{data.row.created_at}</div>)
          }
        },
        {
          label: '操作',
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-link type="primary" style="margin-right: 10px;" onClick={() => { this.tableEditMap(data.row) }}>编辑</el-link>
                <el-link type="success" style="margin-right: 10px;" onClick={() => { this.getQRcode(data.row) }}>微信小程序</el-link>
                <el-link type="warning" style="margin-right: 10px;" onClick={() => { this.showGetRacode(data.row) }}>获客记录</el-link>
                <el-link type="success" style="margin-right: 10px;" onClick={() => { this.showBank(data.row) }}>分享排行</el-link>
                <el-link type="danger" onClick={() => { this.tabledeleteMap(data.row) }}>删除</el-link>
              </div>
            )
          }
        },
      ],
      // 分享表格头数据
      share_table_header: [
        { prop: 'share_name', label: '分享者' },
        { prop: 'share_count', label: '分享次数', sortable: true },
        { prop: 'share_users', label: '获客数量', sortable: true },
        { prop: 'browse_count', label: '浏览次数', sortable: true },
      ],
      // build_loading: false, //公海成员
      // customer_type: 1,
      // project_list: [], //编辑公海成员
      // gonghai_user: {  //选择公海信息
      //   id: '',
      //   name: ''
      // },
      public_admin_id: '', //公海成员id
      // 获客记录表格头数据
      recode_table_header: [
        {
          prop: 'name',
          width: "200px",
          fixed: "left",
          label: '客户名称',
        },
        {
          prop: 'phone',
          fixed: "left",
          width: "200px",
          label: '客户手机号'
        },
        {
          prop: 'browse_count',
          width: '120px',
          label: '浏览次数',
        },
        // {
        //   prop: 'is_receive',
        //   width: '120px',
        //   label: '领取状态',

        //   render: (h, data) => {
        //     return (
        //       <div>
        //         {data.row.is_receive == 1 ? '已经领取地图' : '未领取地图'}
        //       </div>
        //     )
        //   }
        // },
        {
          prop: 'access_time',
          width: '200px',
          label: '访问时间',
        },
        {
          prop: 'browse_time',
          width: '120px',
          label: '停留时间',
          render: (h, data) => {
            return (
              <div>
                {data.row.browse_time + '秒'}
              </div>
            )
          }
        },
        {
          prop: 'login_out_time',
          width: '200px',
          label: '离开时间'
        },
        {
          prop: 'ctime',
          width: '200px',
          label: '添加时间'
        },
        {
          prop: 'share_name',
          width: '120px',
          label: '分享者',
          render: (h, data) => {
            return (
              <div>
                {data.row.share_name ? data.row.share_name : '无'}
              </div>
            )
          }
        },
        {
          prop: 'inner_user_id',
          width: '150px',
          label: '内部客户分享',
          render: (h, data) => {
            return (
              <div>
                {data.row.inner_user_id ? '是' : '否'}
              </div>
            )
          }
        },
        {
          prop: 'map_plugin',
          width: '200px',
          label: '资料名称'
        },
        // {
        //   prop: 'map_title',
        //   width: '200px',
        //   label: '地图名称'
        // },
        {
          prop: '',
          width: '120px',
          fixed: "right",
          label: '操作',
          render: (h, data) => {
            return (
              <div>
                <el-link onClick={() => { this.tableDetailMap(data.row) }}> 详情</el-link>
              </div>
            )
          }
        },
      ]
    }
  },
  created() {
    this.getMapPluginList(); // 获取地图插件列表
    // this.$http.getMapPluginDetail(this.customer_type).then((res) => {
    //   console.log(res,"获取公海人员详情")
    // })
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  methods: {
    // delNametwo() {
    //   this.gonghai_user.id = '',
    //   this.gonghai_user.name = '',
    //   console.log('清除公海成员')
    // },
    // onBuild(e) {
    //   this.edit_id = e;
    // },
    // //获客进入公海成员列表
    // getCommonProjectList(build_name) {
    //   this.build_loading = true;
    //   this.$http
    //     .getResignedUsersData({ params: { name: build_name } })
    //     .then((res) => {
    //       this.build_loading = false;
    //       if (res.status === 200) {
    //         this.project_list = res.data;
    //       }
    //     });
    // },
    // 获取地图插件列表
    getMapPluginList() {
      this.is_table_loading = true; // 开启loading
      this.$http.getMapPluginList(this.search_params).then((res) => {
        if (res.status == 200) {
          console.log(res.data, "地图插件数据");
          this.tableData = res.data.data;
          this.total = res.data.total;
          this.is_table_loading = false; // 关闭loading
        }
      })
    },
    //显示排行总榜
    shareRank() {
      this.sharetitle = '分享排行总榜'
      this.share_state = 1
      this.share_dialog = true; // 显示排行榜
      this.share_params.page = 1;
      this.shareRankes();
    },
    //获取分享排行总榜
    shareRankes() {
      console.log("排行总榜给传的参", this.share_params)
      this.share_table_loading = true; // 加载loading
      this.$http.shareRanking(this.share_params).then((res) => {
        if (res.status == 200) {
          this.share_table_loading = false; // 关闭loading
          this.share_tableData = res.data.data;
          this.share_params.total = res.data.total;
        } else {
          this.share_table_loading = false;
        }
      }).catch(() => {
        this.share_table_loading = false;
      })
      console.log(this.share_state, "排行状态+++++")
    },
    // 显示排行榜
    showBank(row) {
      console.log(row, "row");
      this.share_state = 2
      this.sharetitle = '分享排行';
      this.share_params.plugin_id = row.id;
      this.share_dialog = true; // 显示排行榜
      this.share_params.page = 1;
      this.getShareBankList();
    },
    // 获取分享排行榜
    getShareBankList() {
      this.share_table_loading = true; // 加载loading
      this.$http.getMapShareBank(this.share_params).then((res) => {
        if (res.status == 200) {
          this.share_table_loading = false; // 关闭loading
          this.share_tableData = res.data.data;
          this.share_params.total = res.data.total;
        } else {
          this.share_table_loading = false;
        }
      }).catch(() => {
        this.share_table_loading = false;
      })
      console.log(this.share_state, "排行状态+++++")
    },
    // 改变分享排行榜当前页码
    sharePageChange(current_page) {
      this.share_params.page = current_page;
      if (this.share_state == 1) {
        this.shareRankes()
      } else if (this.share_state == 2) {
        this.getShareBankList();
      }
    },
    // 添加地图插件
    addMapPlugin() {
      this.zuoxi_user.name = ''
      this.addDialog = true;
      // this.getCommonProjectList()
    },
    // 确定添加地图插件
    confirmAdd() {
      if (!this.add_params.title) {
        return this.$message.warning('请填写资料标题');
      }
      // if (this.add_params.browse_auth == 1 && !this.add_params.browse_time) {
      //   return this.$message.warning('请填写浏览限制时间');
      // }
      // if(!this.add_params.auth_expire_time) {
      //     return this.$message.warning('请填写授权失效时间')
      // }
      // if (this.add_params.receive_type == 2 && !this.add_params.netdisk_url) {
      //   return this.$message.warning('请填写网盘地址');
      // }
      // if (this.add_params.receive_type == 2 && !this.add_params.netdisk_code) {
      //   return this.$message.warning('请填写网盘密码');
      // }
      if (!this.add_params.share_title) {
        return this.$message.warning('请填写分享标题');
      }
      if (!this.add_params.share_desc) {
        return this.$message.warning('请填写分享描述');
      }
      if (!this.add_params.share_pic) {
        return this.$message.warning('请上传分享图片');
      }
      // if(!this.add_params.top_pic) {
      //     return this.$message.warning('请上传头部背景图片');
      // }
      if (!this.picture_list.length) {
        return this.$message.warning('请上传地图图片');
      }
      // 处理上传的地图图片为JSON格式
      this.picture_list.map((item, index) => {
        let num = {
          url: item.url,
          title: item.title,
          sort: index,
        }
        this.add_params.maps.push(num);
      })
      let params = Object.assign({}, this.add_params);
      params.public_admin_id = JSON.stringify(this.public_admin_id)
      if (!params.top_pic) {
        delete params.top_pic
      }
      params.maps = JSON.stringify(params.maps);
      // 删除多余参数
      // if (params.receive_type != 2) {
      //   delete params.netdisk_url; // 删除网盘地址参数
      //   delete params.netdisk_code; // 删除网盘密码参数
      // }
      if (params.browse_auth != 1) {
        delete params.browse_time; // 删除浏览限制时间参数
      }
      this.is_loading = true; // 显示loading
      // console.log(params, "params");
      
      this.$http.addMapPlugin(params).then((res) => {
        console.log(params,"传的公海成员..........")
        if (res.status == 200) {
          this.$message.success('添加成功');
          this.$refs.form.resetFields(); // 重置表单参数
          this.picture_list = []; // 重置地图图片列表
          this.is_loading = false; // 关闭loading
          this.addDialog = false; // 关闭添加模态框
          this.search_params.page = 1;
          this.getMapPluginList(); // 刷新地图插件列表
        } else {
          this.is_loading = false;
        }
      }).catch(() => {
        this.is_loading = false;
      })
    },
    // 新增地图插件,上传地图成功回调函数
    UploadParamsSuccess(e) {
      console.log(e)
      let lengths = this.picture_list.length;
      this.picture_list.push({ url: e.url, title: '锚点' + (lengths + 1) });
    },
    //上传锚点失败回调函数
    UploadParamserror(e) {
      console.log(JSON.parse(e.message), "失败头部")
      let a = JSON.parse(e.message)
      this.$message.error({
        message: a.message,
        type: 'warning'
      });
    },
    // 编辑地图插件,上传地图成功回调函数
    editUploadParamsSuccess(e) {
      console.log(e);
      let lengths = this.edit_params.maps.length;
      this.edit_params.maps.push({ url: e.url, title: '锚点' + (lengths + 1) });
    },
    //编辑地图插件，上传失败回调函数
    editUploadParamserror(e) {
      let a = JSON.parse(e.message)
      this.$message.error({
        message: a.message,
        type: 'warning'
      });
    },
    // 新增地图插件,删除已上传的地图图片
    deleteMap(url) {
      let Index = null;
      this.picture_list.map((item, index) => {
        if (item.url == url) {
          Index = index
        }
      })
      if (Index != null) {
        this.picture_list.splice(Index, 1);
      }
      // 删除地图后重置title
      this.picture_list.map((item, index) => {
        if (item.title.indexOf('锚点') >= 0) {
          item.title = '锚点' + (index + 1);
        }
      })
    },
    // 编辑地图插件,删除已上传的地图图片
    editDeleteMap(url) {
      let Index = null;
      this.edit_params.maps.map((item, index) => {
        if (item.url == url) {
          Index = index
        }
      })
      if (Index != null) {
        this.edit_params.maps.splice(Index, 1);
      }
      // 删除地图后重置title
      this.edit_params.maps.map((item, index) => {
        if (item.title.indexOf('锚点') >= 0) {
          item.title = '锚点' + (index + 1);
        }
      })
    },
    tableDetailMap(row) {
      this.$goPath("/crm_customer_detail?id=" + row.client_id + "&type=my")
    },
    // 新增地图插件,上传头部背景图片成功回调函数
    headerBGSuccess(e) {
      this.add_params.top_pic = e.url;
    },
    //上传头部背景图片失败回调
    headerBGerror(e) {
      console.log(JSON.parse(e.message), "失败头部")
      let a = JSON.parse(e.message)
      this.$message.error({
        message: a.message,
        type: 'warning'
      });
    },
    // 编辑地图插件,上传头部背景图片成功回调函数
    editHeaderBGSuccess(e) {
      this.edit_params.top_pic = e.url;
    },
    //编辑地图插件上传头部背景图片失败回调
    editHeaderBGerror(e) {
      let a = JSON.parse(e.message)
      this.$message.error({
        message: a.message,
        type: 'warning'
      });
    },
    // 新增地图插件,删除头部背景已上传图片
    deleteHeaderBG() {
      this.add_params.top_pic = '';
    },
    // 编辑地图插件,删除头部背景已上传图片
    editDeleteHeaderBG() {
      this.edit_params.top_pic = '';
    },
    //上传失败的回调
    // editUploadShareerror() {
    //   console.log("失败的")
    // },
    // 上传分享图片成功回调函数
    uploadShareSuccess(e) {
      this.add_params.share_pic = e.url;
      console.log(e, "%%%%%%")
    },
    uploadShareerror(e) {
      let a = JSON.parse(e.message)
      this.$message.error({
        message: a.message,
        type: 'warning'
      });
    },
    // 编辑地图插件，上传分享图片成功回调函数
    editUploadShareSuccess(e) {
      this.edit_params.share_pic = e.url;
    },
    //编辑地图插件，上传分享图片失败回调函数
    editUploadSharerror(e) {
      let a = JSON.parse(e.message)
      this.$message.error({
        message: a.message,
        type: 'warning'
      });
    },
    // 新增地图插件，,删除分享图片
    deleteShareImg() {
      this.add_params.share_pic = '';
    },
    // 编辑地图插件,删除分享图片
    editDeleteShareImg() {
      this.edit_params.share_pic = '';
    },
    // 显示地图图片dialog
    showMapDialog(url) {
      this.previewDialog = true; // 显示预览dialog
      this.preview_url = url; // 赋值预览图片地址
    },
    // 新增地图插件,当拖动的图片移动到其他图片的位置
    onDragOver(event) {
      event.preventDefault();
      const { src } = event.target;
      this.targetSrc = src;
    },
    // 新增地图插件,将拖动的图片从原位置移动到该位置
    onDrageEnd(index) {
      const targetIdx = this.picture_list.findIndex(item => item.url == this.targetSrc);
      [this.picture_list[index], this.picture_list[targetIdx]] = [this.picture_list[targetIdx], this.picture_list[index]];
      this.picture_list = [...this.picture_list];
    },
    // 编辑地图插件,拖动的图片移动到其他图片的位置
    editDragOver(event) {
      event.preventDefault();
      const { src } = event.target;
      this.targetSrc = src;
    },
    // 编辑地图插件,将拖动的图片从原位置移动到该位置
    editDrageEnd(index) {
      const targetIdx = this.edit_params.maps.findIndex(item => item.url == this.targetSrc);
      [this.edit_params.maps[index], this.edit_params.maps[targetIdx]] = [this.edit_params.maps[targetIdx], this.edit_params.maps[index]];
      this.edit_params.maps = [...this.edit_params.maps];
    },
    // 编辑地图插件
    tableEditMap(row) {
      this.getMapPluginDetail(row.id); // 请求地图插件详情
      this.editDialog = true; // 显示编辑模态框
    },
    // 删除地图插件
    tabledeleteMap(row) {
      this.$confirm('此操作将永久删除该地图插件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.deleteMapPlugin(row.id).then((res) => {
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getMapPluginList(); // 刷新地图插件列表
          }
        })
      }).catch(() => { return; });
    },
    // 获取地图插件详情
    getMapPluginDetail(id) {
      this.$http.getMapPluginDetail(id).then((res) => {
        if (res.status == 200) {
          console.log(res.data);
          this.edit_params = res.data;
          this.zuoxi_user.name = res.data.admin_user[0].user_name
          this.project_list = res.data.admin_user
          console.log(this.zuoxi_user.name, '11111111111')
          // 删除多余参数
          if (this.edit_params.updatd_at) {
            delete this.edit_params.updatd_at; // 删除创建时间
          }
          if (this.edit_params.website_id) {
            delete this.edit_params.website_id; // 删除站点id
          }
          // 对地图插件数组进行排序,从小到大
          this.edit_params.maps.sort((a, b) => a.sort - b.sort);
        }
      })
    },
    // 确定编辑地图插件
    confirmEdit() {
      if (!this.edit_params.title) {
        return this.$message.warning('请填写资料标题');
      }
      // if (this.edit_params.browse_auth == 1 && !this.edit_params.browse_time) {
      //   return this.$message.warning('请填写浏览限制时间');
      // }
      // if(!this.edit_params.auth_expire_time) {
      //     return this.$message.warning('请填写授权失效时间')
      // }
      // if (this.edit_params.receive_type == 2 && !this.edit_params.netdisk_url) {
      //   return this.$message.warning('请填写网盘地址');
      // }
      // if (this.edit_params.receive_type == 2 && !this.edit_params.netdisk_code) {
      //   return this.$message.warning('请填写网盘密码');
      // }
      if (!this.edit_params.share_title) {
        return this.$message.warning('请填写分享标题');
      }
      if (!this.edit_params.share_desc) {
        return this.$message.warning('请填写分享描述');
      }
      if (!this.edit_params.share_pic) {
        return this.$message.warning('请上传分享图片');
      }
      // if(!this.edit_params.top_pic) {
      //     return this.$message.warning('请上传头部背景图片');
      // }
      if (!this.edit_params.maps.length) {
        return this.$message.warning('请上传地图图片');
      }
      // 处理上传地图数据
      this.edit_params.maps.map((item, index) => {
        item.sort = index + 1;
      })
      let params = Object.assign({}, this.edit_params);
      if (!params.top_pic) {
        delete params.top_pic
      }
      // 删除多余参数
      if (params.browse_auth != 1) {
        delete params.browse_time; // 删除浏览限制时间参数
      }
      if (params.admin_user) {
        delete params.admin_user
      }
      params.maps = JSON.stringify(params.maps);
      params.public_admin_id = JSON.stringify(this.edit_id)
      this.is_loading = true; // 开启loading
      console.log(params, '编辑提交的参数')
      this.$http.editMapPlugin(params).then((res) => {
        if (res.status == 200) {
          this.$message.success("编辑成功");
          this.is_loading = false; // 关闭loading
          this.editDialog = false; // 关闭模态框
          this.getMapPluginList(); // 刷新地图插件列表
        } else {
          this.is_loading = false;
        }
      }).catch(() => {
        this.is_loading = false;
      })
    },
    // 改变tabs当前页码
    handleCurrentChange(value) {
      this.search_params.page = value;
    },
    // 显示微信二维码
    showQRcode(row) {
      console.log(row, "row");
      this.showQR = true;
      this.nowlist_data = row;
    },
    //部门成员筛选清除
    delName() {
      this.zuoxi_user.id = '',
        this.zuoxi_user.name = '',
        this.recode_params.admin_id = '',
        this.getRecodeList() //列表数据
      console.log("清除了")
    },
    selecetedMember(e) {
      this.recode_params.page = 1;
      console.log(e, "eeeee")
      if (e.checkedNodes && e.checkedNodes.length) {
        this.public_admin_id = e.checkedNodes[e.checkedNodes.length - 1].id,
        this.recode_params.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id,
          this.zuoxi_user = {
            id: e.checkedNodes[e.checkedNodes.length - 1].id,
            name: e.checkedNodes[e.checkedNodes.length - 1].name
          }
        console.log(this.public_admin_id, "aaaaaaacacacacac")
        this.getRecodeList()  //列表数据
      } else {
        this.recode_params.admin_id = ''
        this.public_admin_id = ''
      }
      this.show_select_dia = false;
    },
    // 获取部门列表
    async getDepartment() {
      // if(this.zuoxi_user.name == ''){
      //   let res = await this.$http.getCrmDepartmentList().catch((err) => {
      //     console.log(err);
      //   });
      //   if (res.status == 200) {
      //     this.memberList = res.data;
      //     console.log(this.memberList, "获取到的部门数据")
      //   }
      // }
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
        console.log(this.memberList, "获取到的部门数据")
      }
    },
    showMemberList() {
      this.getDepartment()
      this.show_select_dia = true;
      console.log("聚焦了")
    },
    //总获客日期时间选择
    changeTimeRange(e) {
      this.recode_params.page = 1;
      let a = e ? e[0] : "";
      let b = e ? e[1] : "";
      this.recode_params.times = a + "," + b;
      this.getRecodeList()  //列表数据
      console.log(this.recode_params.times, "&&&&&")
      if (this.recode_params.times == ',') {
        this.recode_params.times = ""
        this.getRecodeList()
        console.log(this.recode_params.times, "aaaaaaaa")
      }
    },
    //导出
    exportExcel() {
      this.export_params = {
        times: this.recode_params.times,
        plugin_id: this.recode_params.plugin_id,
        admin_id: this.recode_params.admin_id,
      }
      console.log(this.export_params, "export")
      this.$confirm('确认导出坐席列表吗？', '提示').then(() => {
        this.loading = this.$loading({
          lock: true,
          text: "导出中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        this.$http.exportMapRecorde(this.export_params).then((res) => {
          this.$message({
            showClose: true,
            message: '导出成功',
            type: 'success'
          });
          this.loading.close();
          if (res.status == 200) {
            window.open(res.data)
          } else {
            this.$message.error(res.message || '导出失败')
            this.loading.close();
          }

        })
      })
    },
    //总获客记录
    customer() {
      this.contitle = '总获客记录'
      this.recode_dialog = true;
      this.recode_params.plugin_id = ''
      this.recode_table_loading = true
      this.picker = true
      this.p_time = ''
      this.zuoxi_user.id = '',
        this.zuoxi_user.name = '',
        this.recode_params.admin_id = '',
        this.recode_params.times = ''
      this.recode_params.page = 1
      console.log("总获客记录")
      this.getRecodeList();
    },

    // 显示获客记录
    showGetRacode(row) {
      this.picker = false
      this.contitle = '获客记录'
      console.log(row, "row");
      this.recode_params.times = ''
      this.recode_params.plugin_id = row.id;
      this.recode_dialog = true; // 显示排行榜
      this.recode_params.page = 1;
      this.getRecodeList();
    },
    // 获取获客记录
    getRecodeList() {
      this.recode_table_loading = true; // 加载loading
      this.$http.getMapRecorde(this.recode_params).then((res) => {
        if (res.status == 200) {
          this.recode_table_loading = false; // 关闭loading
          this.recode_tableData = res.data.data;
          this.recode_params.total = res.data.total;
        } else {
          this.recode_table_loading = false;
        }
      }).catch(() => {
        this.recode_table_loading = false;
      })
    },
    // 改变获客记录当前页码
    recodePageChange(current_page) {
      this.recode_params.page = current_page;
      this.getRecodeList();
    },
    // 处理输入不显示问题
    inputUpdate() {
      this.$forceUpdate();
    },
    getQRcode(row) {
      this.nowlist_data = row;
      this.$http.getMapPluginQRcode(this.nowlist_data.id, this.getQR_params).then((res) => {
        if (res.status == 200) {
          this.codeVisible = true;
          this.QRcode = res.data; // 赋值二维码链接
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
  margin: -15px;
  padding: 24px;

  .container {
    .container-header {
      margin-bottom: 24px;
    }

    .container-main {
      .paging-box {
        text-align: end;
        margin-top: 24px;
      }
    }

    .uploader-create {
      ::v-deep .el-upload {
        display: flex;
        flex-direction: row;
      }
    }

    .uploader-box {
      width: 146px;
      height: 146px;
      line-height: 146px;
      border: 1px dashed #8c939d;
      border-radius: 4px;
      margin-right: 20px;
      position: relative;

      .avatar-uploader-icon {
        font-size: 28px;
        color: #606266;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .anchor-box {
        min-width: 80px;
        height: 20px;
        line-height: 20px;
        position: absolute;
        bottom: -30px;
        left: 0px;

        ::v-deep .el-input {
          .el-input__inner {
            height: 20px;
            border: none;
            border-bottom: 1px solid #dcdfe6;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }
        }
      }

      .uploader-content {
        width: 100%;
        height: 100%;
        position: relative;

        .uploader-shade {
          position: absolute;
          top: 0;
          width: 146px;
          height: 146px;
          background-color: rgba(0, 0, 0, 0.5);
          transition: opacity 0.3s;
          opacity: 0;

          .uploader-show,
          .uploader-delete {
            flex: 1;
            color: #fff;
            font-size: 24px;
            text-align: center;
            cursor: pointer;
          }
        }

        .mini-delete {
          display: none;
          position: absolute;
          top: -10px;
          right: -10px;
          width: 20px;
          height: 20px;
          cursor: pointer;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .uploader-content:hover {
        .uploader-shade {
          opacity: 1;
        }

        .mini-delete {
          display: flex;
        }
      }
    }

    .uploader-box:focus {
      border-color: #409eff;
    }

    .uploader-box:hover {
      border-color: #409eff;
    }

    .uploader-headerBG {
      .el-upload {
        .headerBG-box {
          width: 100%;
          height: 100%;
        }
      }
    }

    .hint_text {
      margin-bottom: 22px;
      margin-left: 110px;
    }
  }
}

.picker-all-box {
  width: 100%;
  margin-bottom: 10px;
}

.inlay {
  ::v-deep .el-dialog {
    .el-dialog__header {
      .el-dialog__title {
        border-left: 0;
      }
    }
  }
}

.QR-box {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>