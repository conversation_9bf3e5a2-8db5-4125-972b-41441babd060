<template>
  <!-- 群活码 -->
  <div class="pages" id="page">
    <el-row :gutter="20">
      <el-col :span="24">
        <topTips
          from="crm_customer_group_qrcode"
          @add="toAdd"
          :showBtn="true"
        ></topTips>
        <!-- <div class="content-box-crm padd0" style="margin-bottom: 24px">
          <div class="div row align-center">
            <div class="title flex-1">群活码</div>
            <div class="add">
              <el-button type="primary" @click="toAdd" size="mini"
                >添加</el-button
              >
            </div>
          </div>
        </div> -->
        <div class="content-box-crm" style="margin-bottom: 24px; padding: 0">
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">创 建 人：</span>
            <div>
              <el-input
                placeholder="请选择创建人"
                v-model="creat_name"
                @focus="showMemberList"
              >
                <i
                  @click="delName"
                  slot="suffix"
                  class="el-input__icon el-icon-circle-close"
                ></i
              ></el-input>
            </div>
          </div>
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">绑定员工：</span>
            <div>
              <el-input
                placeholder="请选择绑定员工"
                v-model="user_name"
                @focus="showMemberList(1)"
              >
                <i
                  @click="delName(1)"
                  slot="suffix"
                  class="el-input__icon el-icon-circle-close"
                ></i
              ></el-input>
            </div>
          </div>
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">创建时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="p_time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="changeTimeRange"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right div row"></div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
            <el-table-column prop="name" label="二维码" v-slot="{ row }">
              <template>
                <div class="row_img" @click="previewImg(row.qr_code)">
                  <img :src="row.qr_code" alt="" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="活码名称">
              <template slot-scope="scope">
                {{ scope.row.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column label="创建人">
              <template slot-scope="scope">
                <span>{{ scope.row.create_user }}</span>
              </template>
            </el-table-column>
            <el-table-column label="绑定员工">
              <template slot-scope="scope">
                <span
                  style="margin-right: 10px"
                  v-for="(item, index) in scope.row.user"
                  :key="index"
                  >{{ item }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="绑定群">
              <template slot-scope="scope">
                <span
                  style="margin-right: 10px"
                  v-for="(item, index) in scope.row.group_chat"
                  :key="index"
                  >{{ item }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="扫码进群人数" v-slot="{ row }">
              {{ row.add_count }}
            </el-table-column>

            <!-- <el-table-column
              label="创建时间"
              prop="created_at"
            ></el-table-column> -->
            <el-table-column label="操作" v-slot="{ row }">
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="onChangeEdit(row)"
                >编辑</el-link
              >
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="download(row)"
                >下载</el-link
              >
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="copyLink(row)"
                >复制链接</el-link
              >
              <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteGroupQrcode(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="hintShow" title="提示" :modal="false">
      <p style="fontSize:18px">请联系客服授权待开发应用</p>
      <span slot="footer" class="dialog-footer">
      <el-button @click="hintShow = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_download_dia" width="545px" title="下载尺寸">
      <div class="download">
        <div
          class="download_item flex-row align-center j-between"
          @click="load(1)"
        >
          <div class="left">
            <div class="tip1">小尺寸：适用于屏幕类、宣传册等</div>
            <div class="tip2">二维码变长8cm、建议扫码距离0.5cm</div>
          </div>
          <div class="right">
            <img src="@/assets/download.png" alt="" />
          </div>
        </div>
        <div
          class="download_item flex-row align-center j-between"
          @click="load(2)"
        >
          <div class="left">
            <div class="tip1">中尺寸：适用于海报、展架等</div>
            <div class="tip2">二维码变长15cm、建议扫码距离1m</div>
          </div>
          <div class="right">
            <img src="@/assets/download.png" alt="" />
          </div>
        </div>
        <div
          class="download_item flex-row align-center j-between"
          @click="load(3)"
        >
          <div class="left">
            <div class="tip1">大尺寸：适用于幕布、广告等</div>
            <div class="tip2">二维码变长50cm、建议扫码距离2.5m</div>
          </div>
          <div class="right">
            <img src="@/assets/download.png" alt="" />
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="show_add_dia" width="660px" title="添加群活码">
      <add
        v-if="show_add_dia"
        @success="addOk"
        @cancel="show_add_dia = false"
      ></add>
    </el-dialog>
    <el-dialog :visible.sync="show_edit_dia" width="660px" title="编辑群活码">
      <edit
        v-if="show_edit_dia"
        @success="editOk"
        :form="form_params"
        @cancel="show_edit_dia = false"
      ></edit>
    </el-dialog>
    <el-dialog :visible.sync="show_select_dia" width="660px" :title="title">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog>
    <el-dialog :visible.sync="show_preview" width="660px" title="图片预览">
      <div class="flex-row j-center align-center">
        <img :src="imgSrc" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "./components/my_label";
import QRCode from "qrcodejs2";
import add from "./components/group_qrcode/add";
import edit from "./components/group_qrcode/edit";
import memberListSingle from "../site/components/memberList_single.vue";
import topTips from "./components/top_tips.vue";
export default {
  name: "crm_customer_group_qrcode",
  components: {
    myLabel,
    add,
    topTips,
    memberListSingle,
    edit,
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        times: "",
        create_user: "",
        user_id: "",
      },
      time_list: [
        { id: 1, name: "全部", value: "" },
        { id: 1, name: "今天", value: "today" },
        { id: 2, name: "昨天", value: "yesterday" },
        { id: 3, name: "本周", value: "this_week" },
        { id: 4, name: "上周", value: "last_week" },
      ],

      value: "",
      p_time: "",
      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
      show_download_dia: false,
      show_add_dia: false,
      show_edit_dia: false,
      show_select_dia: false,
      user_name: "",
      creat_name: "",
      title: "选择创建人",
      selectedIds: [],
      memberList: [],
      imgSrc: "",
      show_preview: false,
      hintShow:false,
    };
  },
  mounted() {
    this.getDataList();
    this.getDepartment();
    if(this.$store.state.website_info.self_auth_create===0){
      this.hintShow=true
    }else if(this.$store.state.website_info.self_auth_create===1){
      this.hintShow=false
    }
  },
  deactivated(){
    if(this.$store.state.website_info.self_auth_create===0){
      this.hintShow=true
    }else if(this.$store.state.website_info.self_auth_create===1){
      this.hintShow=false
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmGroupQrcodeList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
     //关闭提示
     closeHint(){
        this.hintShow=false
      },
    previewImg(src) {
      console.log(src);
      this.imgSrc = src;
      this.show_preview = true;
    },
    addOk() {
      this.show_add_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    editOk() {
      this.show_edit_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    toAdd() {
      this.show_add_dia = true;
    },
    onClickTime(item) {
      this.params.times = item.value;
      this.params.page = 1;
      this.getDataList();
    },
    changeTimeRange(e) {
      if (e && e.length) {
        this.params.times = e.join(",");
        this.params.page = 1;
        this.getDataList();
      } else {
        this.params.times = "";
        this.params.page = 1;
        this.getDataList();
      }
    },
    onClickEmployees(item) {
      console.log(item.id);
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onClickDetail(id) {
      this.$router.push(`/crm_customer_group_detail?id=${id}`);
    },
    deleteGroupQrcode(row) {
      this.$http
        .delCrmGroupQrcode(row.id)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("删除成功");
            this.params.page = 1;
            this.getDataList();
          } else {
            this.$message.error("删除失败");
          }
        })
        .catch(() => { });
    },
    copyLink(row) {
      this.$onCopyValue(row.qr_code);
    },
    download(row) {
      var link = document.createElement("a");

      link.href = row.qr_code;
      link.target = "_blank";
      // link.download = fileName;  //a标签的下载属性
      document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
      link.click();
      setTimeout(function () {
        document.body.removeChild(link);
      }, 100);
      // this.currentRow = row
      // this.show_download_dia = true
    },
    load(type) {
      let url =
        "https://work.weixin.qq.com/kfid/kfcbee406830c07097a?enc_scene=ENC2GuVNmhXUVzuJRkG8LtkYH";

      let width = 200,
        height = 200;
      switch (type) {
        case 1:
          width = 200;
          height = 200;
          break;
        case 2:
          width = 425;
          height = 425;
          break;
        case 3:
          width = 1417;
          height = 1417;
          break;
        default:
          width = 200;
          height = 200;
          break;
      }
      let fileName = "qrcode_" + +new Date();
      var div = document.createElement("div");
      var code = new QRCode(div, {
        text: url,
        height: height,
        width: width,
        // 425  1417
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
      //这里如果需要在页面上展示的话，就将div节点给添加到dom树上去；node.appendChild(div)
      let canvas = code._el.querySelector("canvas"); //获取生成二维码中的canvas，并将canvas转换成base64
      var base64Text = canvas.toDataURL("image/png");
      var blob = this.getBlob(base64Text); //将base64转换成blob对象

      //接下来就是下载了，主要的思路就是通过URL.createURL()方法把blob对象转换成url，然后绑定到a标签中的href上，通过a标签的下载属性来进行下载。
      if (navigator.msSaveBlob) {
        // IE的Blob保存方法
        navigator.msSaveBlob(blob, fileName);
      } else {
        var link = document.createElement("a");
        var href = window.URL.createObjectURL(blob);
        link.href = href;
        link.download = fileName; //a标签的下载属性
        document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
        link.click();

        // 延时保证下载成功执行，否则可能下载未找到文件的问题
        setTimeout(function () {
          window.URL.revokeObjectURL(href); // 释放Url对象
          document.body.removeChild(link);
        }, 100);
      }
      this.show_download_dia = false;
    },
    getBlob(base64) {
      var mimeString = base64
        .split(",")[0]
        .split(":")[1]
        .split(";")[0]; // mime类型
      var byteString = atob(base64.split(",")[1]); //base64 解码
      var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
      var intArray = new Uint8Array(arrayBuffer); //创建视图
      for (var i = 0; i < byteString.length; i += 1) {
        intArray[i] = byteString.charCodeAt(i);
      }
      return new Blob([intArray], {
        type: mimeString,
      });
    },
    async onChangeEdit(row) {
      let res = await this.$http.crmGroupQrcodeDetail(row.id).catch(() => {
        this.$message.error("获取详情失败");
        return;
      });
      if (res.status == 200) {
        this.form_params = res.data;
        this.show_edit_dia = true;
      }
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    showMemberList(type = 0) {
      if (type == 1) {
        this.title = "选择绑定成员";
      } else {
        this.title = "选择创建人";
      }
      this.currentType = type;
      this.show_select_dia = true;
    },
    delName(type = 0) {
      if (type == 1) {
        //使用员工
        this.params.user_id = "";
        this.user_name = "";
      } else {
        this.params.create_user = "";
        this.creat_name = "";
      }
      // this.params.wx_work_user_id = ''
      // this.username = ''
      this.params.page = 1;
      this.getDataList();
    },
    selecetedMember(e) {
      if (this.currentType == 1) {
        //使用员工
        if (e.checkedNodes && e.checkedNodes.length) {
          this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.user_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.user_name = "";
          this.params.user_id = "";
        }
      } else {
        if (e.checkedNodes && e.checkedNodes.length) {
          this.creat_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.create_user =
            e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.creat_name = "";
          this.params.create_user = "";
        }
      }

      this.show_select_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .padd0 {
    padding: 0 24px;
    margin: 0 -20px;
    .title {
      padding: 15px 40px;
    }
  }
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
.row_img {
  width: 60px;
  height: 60px;
  cursor: pointer;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.download {
  .download_item {
    border-radius: 4px;
    border: 1px solid #dde1e9;
    padding: 24px;
    margin-bottom: 10px;
    cursor: pointer;
    .left {
      .tip1 {
        color: #2e3c4e;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 16px;
      }
      .tip2 {
        color: #8a929f;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
      }
    }
    .right {
      img {
        width: 40px;
        height: 40px;
        object-fit: cover;
      }
    }
  }
}
</style>
