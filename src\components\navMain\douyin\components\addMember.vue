<template>
  <div>
    <el-form label-width="100px">
      <el-form-item label="姓名">
        <div class="form-item-block">
          <el-input
            v-model="form_params.name"
            style="width: 300px; margin-right: 12px"
            placeholder="请输入姓名"
          >
          </el-input>

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>

      <el-form-item label="手机号">
        <div class="form-item-block">
          <el-input
            v-model="form_params.phone"
            style="width: 300px; margin-right: 12px"
            placeholder="请输入手机号"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="项目名称">
        <div class="form-item-block flex-row items-center">
          <el-select
            v-model="form_params.project_id"
            placeholder="请输入项目名称"
            style="width: 190px; margin-right: 12px"
            clearable
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-button type="primary" @click="toProject"> 操作项目 </el-button>
        </div>
      </el-form-item>
      <el-form-item label="团队成员">
        <div class="form-item-block">
          <el-input
            placeholder="请选择团队成员"
            v-model="creat_name"
            style="width: 300px; margin-right: 12px"
            @focus="showMemberList"
          >
            <i
              @click="delName"
              slot="suffix"
              class="el-input__icon el-icon-circle-close"
            ></i
          ></el-input>
        </div>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>

    <el-dialog
      :visible.sync="show_select_dia"
      width="660px"
      :title="title"
      :modal="false"
    >
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        source="douyin"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog>
  </div>
</template>

<script>
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
export default {
  props: {
    form: {
      type: Object,
      default: () => { }
    },
    projectList: {
      type: Array,
      default: () => { }
    }

  },
  components: {
    memberListSingle
  },
  data() {
    return {
      form_params: {

      },
      creat_name: "",
      show_select_dia: false,
      title: "",
      memberList: [],
      selectedIds: [],

      isSubmiting: false,
    };
  },
  created() {
    this.form_params = this.form
    if (this.form_params.id) {
      this.creat_name = this.form_params.user_name || ''
    }
    this.getDepartment()
  },
  methods: {
    subform() {
      if (this.form_params.id) {
        this.edit()
      } else {
        this.add()
      }
    },
    add() {
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .addDouyinMember(this.form_params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success(res.message || "添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("添加失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    edit() {
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      let params = Object.assign(this.form_params)
      delete params.user_name
      this.$http
        .editDouyinMember(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success(res.message || "编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
    toProject() {
      this.$goPath('/douyin_project')
    },
    delName() {
      this.creat_name = ''
    },
    async getDepartment() {
      // 获取部门列表
      let res = await this.$http.getCrmDepartmentList()
        .catch(err => {
          console.log(err);
        })
      if (res.status == 200) {
        this.memberList = res.data
      }
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.creat_name = e.checkedNodes[e.checkedNodes.length - 1].name
        this.form_params.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id
      } else {
        this.creat_name = ''
        this.form_params.admin_id = ''
      }
      this.show_select_dia = false
    },
    showMemberList() {
      this.title = "选择团队成员"
      this.show_select_dia = true
    }

  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.form-item-block {
  .el-button {
    margin-right: 5px;
  }
  &.form_customer {
    .el-button {
      margin-bottom: 5px;
    }
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
</style>
