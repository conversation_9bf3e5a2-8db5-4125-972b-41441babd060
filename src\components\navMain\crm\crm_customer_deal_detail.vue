<template>
  <!-- 成交报告详情 -->
  <div class="pages">
    <div class="content-box-crm">
      <div class="title-label">
        <span> 基本信息 </span>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">客户名称：</span>张三</span>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">手机号：</span>18912341234</span>
      </div>
      <div class="bottom-border div row">
        <span class="text"
          ><span class="label">成交时间：</span>2022-01-01 12:12:12</span
        >
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">收入费用：</span>1000</span>
      </div>
      <div class="bottom-border div row" style="border: none">
        <span class="text"><span class="label">销售分成：</span></span>
        <el-radio v-model="radio" :label="1">独立成交</el-radio>
        <el-radio v-model="radio" :label="2">合作分成</el-radio>
      </div>
      <el-form :model="addForm" ref="addForm">
        <div
          style="margin-bottom: 24px"
          v-for="(item, index) in addForm.deal_list"
          :key="index"
        >
          <el-input
            v-model="item.type"
            placeholder="请输入"
            style="width: 160px; margin-right: 22px"
          ></el-input>
          <el-select
            v-model="item.user"
            placeholder="请选择"
            style="width: 160px; margin-right: 22px"
          >
            <el-option label="用户1" :value="1"></el-option>
          </el-select>
          <el-select
            v-model="item.bili_type"
            placeholder="请选择"
            style="width: 160px; margin-right: 22px"
          >
            <el-option label="分佣比例" :value="1"></el-option>
            <el-option label="销售比例" :value="2"></el-option>
          </el-select>
          <el-input
            v-model="item.bili"
            placeholder="比例"
            style="width: 160px; margin-right: 22px"
          ></el-input>
          <el-link
            style="margin-right: 24px"
            type="primary"
            @click.prevent="removeDomain(item)"
            >删除</el-link
          >
          <el-link
            v-if="addForm.deal_list.length === index + 1"
            type="primary"
            @click.prevent="addDomain"
            >添加</el-link
          >
        </div>
      </el-form>
      <span>
        <el-button size="small" type="primary">提 交</el-button>
        <el-button size="small" @click="goBack">取 消</el-button>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "crm_customer_deal_detail",
  data() {
    return {
      radio: 1,
      addForm: {
        deal_list: [
          {
            type: "",
            user: "",
            bili_type: "",
            bili: "",
          },
        ],
      },
    };
  },
  methods: {
    addDomain() {
      this.addForm.deal_list.push({
        type: "",
        user: "",
        bili_type: "",
        bili: "",
      });
    },
    goBack() {
      this.$router.back();
    },
    removeDomain(item) {
      if (this.addForm.deal_list.length === 1) {
        return;
      }
      var index = this.addForm.deal_list.indexOf(item);
      if (index !== -1) {
        this.addForm.deal_list.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    margin-top: 24px;
    border-bottom: 1px dashed #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
      .label {
        width: 70px;
        display: inline-block;
        text-align: right;
      }
    }
    .el-radio::v-deep {
      color: #8a929f;
    }
  }

  .title-label {
    margin: 0 -24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    span {
      margin-left: 24px;
      color: #2e3c4e;
    }
  }
}
</style>
