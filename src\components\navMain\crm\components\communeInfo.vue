<template>
    <div class="communeInfo">
        <div class="main-top">
            <div style="margin-left: 8px;" class="flex-row">
                <div 
                    class="main-top-tabs"
                    v-for="(item, index) in tabs_list"
                    :key="index"
                    @click="clickTabs(item)"
                >
                    {{ item.label }}
                    <div class="selected" v-if="item.value == is_selected"></div>
                </div>
            </div>
            <div class="tabs-info" v-if="is_selected == 1">
                <el-row>
                    <el-col :span="10">
                        <div class="flex-box tabs-info-box">
                            <div class="title">机构</div>
                            <div class="content-box">
                                <span 
                                    class="content"
                                    v-for="(item, index) in fetchData_list.institution"
                                    :key="index"
                                >
                                    {{ item }}
                                    <i class="el-icon-location-outline"></i>
                                </span>
                                <span 
                                    class="notList"
                                    v-if="!fetchData_list.institution.length"
                                >
                                    -----
                                </span>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="flex-box tabs-info-box">
                            <div class="title">人名</div>
                            <div class="content-box">
                                <span 
                                    class="content"
                                    v-for="(item, index) in fetchData_list.name"
                                    :key="index"
                                >
                                    {{ item }}
                                </span>
                                <span 
                                    class="notList"
                                    v-if="!fetchData_list.name.length"
                                >
                                    -----
                                </span>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="flex-box tabs-info-box">
                            <div class="title">人群</div>
                            <div class="content-box">
                                <span 
                                    class="content"
                                    v-for="(item, index) in fetchData_list.people"
                                    :key="index"
                                >
                                    {{ item }}
                                </span>
                                <span 
                                    class="notList"
                                    v-if="!fetchData_list.people.length"
                                >
                                    -----
                                </span>
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-row style="margin-top: 20px;">
                    <el-col :span="10">
                        <div class="flex-box tabs-info-box">
                            <div class="title">地点</div>
                            <div class="content-box">
                                <span 
                                    class="content"
                                    v-for="(item, index) in fetchData_list.locations"
                                    :key="index"
                                >
                                    {{ item }}
                                </span>
                                <span 
                                    class="notList"
                                    v-if="!fetchData_list.locations.length"
                                >
                                    -----
                                </span>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="flex-box tabs-info-box">
                            <div class="title">价格</div>
                            <div class="content-box">
                                <span 
                                    class="content"
                                    v-for="(item, index) in fetchData_list.price"
                                    :key="index"
                                >
                                    {{ item }}
                                </span>
                                <span 
                                    class="notList"
                                    v-if="!fetchData_list.price.length"
                                >
                                    -----
                                </span>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="flex-box tabs-info-box">
                            <div class="title">性别</div>
                            <div class="content-box">
                                <span
                                    v-for="(item, index) in fetchData_list.sex"
                                    :key="index"
                                >
                                    <span 
                                        class="content"
                                        v-if="item"
                                    >
                                        {{ item }}
                                    </span>
                                </span>
                                <span 
                                    class="notList"
                                    v-if="!fetchData_list.sex.length"
                                >
                                    -----
                                </span>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div 
                class="tabs-content flex-row" 
                :class="!checkData_list.length ? 'j-center' : ''"
                v-else-if="is_selected == 2"
            >
                <div 
                    style="margin-right: 10px;"
                    v-for="(item, index) in checkData_list"
                    :key="index"
                >
                    {{ item }}
                </div>
                <myEmpty v-if="!checkData_list.length"></myEmpty>
            </div>
            <div 
                v-else-if="is_selected == 3 && this.frequency_list.length"
                class="statistics-content"
            >
                <div class="flex-row statistics-box">
                    <div 
                        v-for="(item, index) in this.frequency_list"
                        :key="index"
                        class="flex-row column">
                        <div>{{ item.word + '：' }}</div>
                        <div>{{ item.count }}</div>
                    </div>
                </div>
            </div>
            <div v-else-if="is_selected == 3 && !this.frequency_list.length" class="tabs-content">
                <myEmpty v-if="!frequency_list.length"></myEmpty>
            </div>
        </div>
        <div class="main-bottom flex-row">
            <div class="bottom-left flex-box">
                <div class="bottom-left-header flex-row">
                    <div class="icon"></div>
                    <div class="title">分析结果</div>
                </div>
                <!-- <div class="bottom-left-ratio">
                    总执行率 40.28% | 负向话术命中率 0%
                </div> -->
                <div class="bottom-left-main flex-box">
                    <div class="bottom-left-box">
                        <div class="main-header">
                        <el-row>
                            <el-col :span="6" class="global-center"><span>话术</span></el-col>
                            <el-col :span="6" class="global-center"><span>评测结果</span></el-col>
                            <el-col :span="6" class="global-center"><span>话术分类</span></el-col>
                            <el-col :span="6" class="global-center"><span>匹配度</span></el-col>
                        </el-row>
                    </div>
                    <div class="main-content">
                        <el-row 
                            class="content-row" 
                            v-for="(item, index) in analyseResult_list" 
                            :key="index"
                        >
                            <el-tooltip class="item" effect="dark" :content="item.text" placement="top">
                                <el-col :span="6" class="global-center verbal"><span>{{ item.text }}</span></el-col>
                            </el-tooltip>
                            <el-col :span="6" class="global-center"><span>{{ item.is_do == 1 ? '执行' : '未执行' }}</span></el-col>
                            <el-col :span="6" class="global-center"><span>{{ item.cate_name }}</span></el-col>
                            <el-col :span="6" class="global-center"><span>{{ item.score }}%</span></el-col>
                        </el-row>
                    </div>
                    </div>
                </div>
            </div>
            <div class="bottom-right flex-box">
                <div class="bottom-right-title flex-row">
                    <div class="bottom-right-header flex-row">
                        <div class="icon"></div>
                        <div class="title">智能完善</div>
                    </div>
                    <div class="bottom-right-button">
                        <el-button type="primary" size="mini" @click="setRecordUserInfo" :loading="save_loading">保存修改</el-button>
                    </div>
                </div>
                <div class="bottom-right-main flex-box">
                    <div class="main-box flex-row">
                        <div class="content">问题内容</div>
                        <div class="content">问题答案</div>
                    </div>
                    <div 
                        class="main-box flex-row" 
                        v-for="(item, index) in smartForm_list" 
                        :key="index"
                    >
                        <div class="content">{{ item.name }}</div>
                        <div class="content" v-if="item.value.length && conceals != index" @click="amendAnswer(index, item.value)">
                            <div v-if="Array.isArray(item.value)">
                                <div
                                    v-for="(list, idx) in item.value"
                                    :key="idx+'i'"
                                >
                                    <span v-if="list">
                                        {{ list }}
                                        <span v-show="item.value.length > 1 && idx != item.value.length">,</span>
                                    </span>
                                </div>
                            </div>
                            <div v-else>{{ item.value }}</div>
                        </div>
                        <div v-else-if="conceals != index" @click="amendAnswer(index)" class="content">-----</div>
                        <div v-else-if="conceals == index" class="content">
                            <el-input :ref="'answer' + index" v-model="answerInput" placeholder="请输入" @blur="concealInput(item)"></el-input>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import myEmpty from "@/components/components/my_empty.vue";
export default {
    components: {
        myEmpty,
    },
    props: {
        // 当前选中的录音id
        record_id: {
            type: [String, Number],
            default: ''
        },
    },
    data() {
        return {
            // tabs切换列表
            tabs_list: [
                {label: "实体抽取", value: "1"},
                {label: "内容质检", value: "2"},
                {label: "词频统计", value: "3"},
            ],
            is_selected : 1, // 当前选中tabs
            // 实体抽取数据
            fetchData_list: {
                institution: [], // 机构
                name: [], // 名称
                locations: [], // 地点
                people: [], // 人群
                price:[], // 价格
                sex: [], // 性别
            },
            checkData_list: [], // 内容质检数据
            moreWord: true, // 控制词频统计接口请求
            moreCheck: true, // 控制内容质检接口请求
            frequency_list: [], // 词频统计数据
            smartForm_list: [], // 智能表单数据
            smartForm_list_copy: [],
            analyseResult_list: [], // 分析结果列表
            // 修改用户信息接口传参
            setUserInfo_params: {
                client_id: '', // 客户id
                cname: '', // 客户名称
                sex: '', // 客户性别
                intention: '', // 客户意向
                budget: '', // 客户预算
            },
            conceals: 999, // 用于判断修改回答输入框显示/隐藏
            answerInput: '',
            save_loading: false,
        }
    },
    created() {
        this.setUserInfo_params.client_id = this.$route.query.u_id;
        this.getEntityFetch(); // 获取实体抽取数据
        this.getSmartForm(); // 获取智能表单
        this.getAnalyseResult(); // 获取分析结果
    },
    methods: {
        clickTabs(item) {
            this.is_selected = item.value;
            if(item.value == 2 && this.moreCheck) {
                this.getContentCheck(); // 获取内容质检数据
            } else if(item.value == 3 && this.moreWord) {
                this.getWordFrequency(); // 获取词频统计数据
            }
        },
        // 获取实体抽取数据
        getEntityFetch() {
            this.$http.getEntityFetch({record_id: this.record_id}).then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"实体抽取");
                    this.fetchData_list.institution = res.data['机构'];
                    this.fetchData_list.name = res.data['名称'];
                    this.fetchData_list.locations = res.data['地点'];
                    this.fetchData_list.people = res.data['人群'];
                    this.fetchData_list.price = res.data['价格'];
                    this.fetchData_list.sex = res.data['性别'];
                }
            })
        },
        // 获取内容质检数据
        getContentCheck() {
            this.$http.getContentCheck({record_id: this.record_id}).then((res) => {
                if(res.status == 200) {
                    this.moreCheck = false; // 控制内容质检接口请求
                    console.log(res.data, "内容质检");
                    this.checkData_list = res.data;
                }
            })
        },
        // 获取词频统计
        async getWordFrequency() {
            await this.$http.getWordFrequency({record_id: this.record_id}).then((res) => {
                if(res.status == 200) {
                    this.moreWord = false; // 控制词频统计接口请求
                    console.log(res.data,"词频统计");
                    this.frequency_list = res.data;
                }
            });
        },
        // 获取智能完善列表
        getSmartForm() {
            this.$http.getSmartForm({record_id: this.record_id}).then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"智能表单数据");
                    this.smartForm_list_copy = JSON.parse(JSON.stringify(res.data.field))
                    this.smartForm_list = res.data.field;
                }
            })
        },
        // 获取分析结果列表
        getAnalyseResult() {
            this.$http.getAnalyseResult({record_id: this.record_id}).then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"分析结果");
                    this.analyseResult_list = res.data.speech;
                }
            })
        },
        // 修改客户问题答案信息
        setRecordUserInfo() {
            let first_sex = this.setUserInfo_params.sex.indexOf('男');
            let second_sex = this.setUserInfo_params.sex.indexOf('女');
            this.setUserInfo_params.sex = first_sex >= 0 ? 1 : second_sex >= 0 ? 2 : ''; // 赋值客户性别

            if(!this.setUserInfo_params.sex) {
                return this.$message.warning('请填写客户性别');
            }
            this.save_loading = true; // 开启loading加载
            this.$http.setRecordUserInfo(this.setUserInfo_params).then((res) => {
                if(res.status == 200) {
                    this.$message.success('操作成功');
                    this.save_loading = false; // 关闭loading加载
                    this.resetParams(); // 重置问题答案参数
                } else {
                    this.save_loading = false;
                }
            }).catch(() => {
                this.save_loading = false;
            })
        },
        // 重置问题答案参数
        resetParams() {
            this.setUserInfo_params.cname = '';
            this.setUserInfo_params.sex = '';
            this.setUserInfo_params.intention = '';
            this.setUserInfo_params.budget = '';

            this.smartForm_list = JSON.parse(JSON.stringify(this.smartForm_list_copy)); // 重新赋值智能表单
        },
        // 选择要编辑的问题答案
        amendAnswer(index, value='') {
            this.conceals = index; // 赋值当前点击的下标
            this.$nextTick(() => {
                this.$refs['answer' + index][0].focus(); // 获取焦点
            })
            if(value && Array.isArray(value)) {
                this.answerInput = value.join(",");
            } else if(value) {
                this.answerInput = value;
            } else {
                this.answerInput = '';
            }
        },
        // 修改回答失去焦点
        concealInput(item) {
            this.conceals = 999; // 隐藏输入框
            this.setUserInfo_params[item.key] = this.answerInput;
            this.smartForm_list.map((list) => {
                if(list.key == item.key) {
                    list.value = this.answerInput;
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.communeInfo {
    width: 100%;
    .main-top {
        height: 313px;
        padding: 20px 22px 25px 22px;
        box-sizing: border-box;
        background: #FFFFFF;
        .main-top-tabs {
            color: #2E3C4E;
            font-size: 20px;
            font-weight: bold;
            margin-right: 30px;
            position: relative;
            cursor: pointer;
        }
        .selected {
            width: 67px;
            height: 6px;
            position: absolute;
            bottom: 0px;
            right: 0px;
            border-radius: 3px;
            background: linear-gradient(89.82deg, #2A89F8 0%, #71B1FB87 64%, #C1DEFF00 100%);
        }
        .tabs-info {
            margin-top: 20px;
            .tabs-info-box {
                .title {
                    color: #2E3C4E;
                    font-size: 18px;
                    margin-left: 8px;
                }
                .content-box {
                    font-size: 14px;
                    .content {
                        max-width: 240px;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        overflow: hidden;
                        margin-top: 7px;
                        display: inline-block;
                        color: #2D84FB;
                        border-radius: 5px;
                        padding: 4px 9px;
                        border: 1px solid #2D84FB;
                        margin-right: 10px;
                    }
                    .notList {
                        display: inline-block;
                        height: 29px;
                        margin-left: 10px;
                        color: #8A929F;
                        margin-top: 8px;
                    }
                }
            }
        }
        .tabs-content {
            color: #2E3C4E;
            font-size: 18px;
            margin-top: 20px;
            margin-left: 8px;
            flex-wrap: wrap;
        }
        .statistics-content {
            height: 230px;
            overflow-y: auto;
            margin-top: 20px;
            .statistics-box {
                flex-wrap: wrap;
                color: #2E3C4E;
                font-size: 18px;
                margin-left: 8px;
                .column {
                    min-width: 130px;
                    padding-bottom: 20px;
                    padding-right: 20px;
                }
            }
        }
    }
    .main-bottom {
        margin-top: 20px;
        justify-content: space-between;
        .bottom-left, .bottom-right {
            flex: 1;
            background: #FFFFFF;
            height: 396px;
            padding: 20px;
            box-sizing: border-box;
        }
        .bottom-left {
            margin-right: 20px;
            .bottom-left-header {
                align-items: center;
                .icon {
                    width: 6px;
                    height: 16px;
                    background: #2D84FB;
                }
                .title {
                    color: #2E3C4E;
                    font-size: 20px;
                    font-weight: bold;
                    margin-left: 5px;
                }
            }
            .bottom-left-ratio {
                color: #2E3C4E;
                font-size: 16px;
                margin-top: 11px;
            }
            .bottom-left-main {
                margin-top: 22px;
                .bottom-left-box {
                    height: 273px;
                    overflow-y: auto;
                    border: 1px solid #DDE1E9;
                    position: relative;
                    padding-bottom: 20px;
                    .main-header {
                        background: #E8F1FF;
                        color: #2E3C4E;
                        font-size: 12px;
                        padding: 12px 10px;
                        border-bottom: 1px solid #DDE1E9;
                    }
                    .main-content {
                        color: #2E3C4E;
                        .content-row {
                            padding: 27px 10px 0 10px;
                            font-size: 12px;
                            text-align: center;
                            .verbal {
                                display: inline-block;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }
            }
        }
        .bottom-right {
            padding: 20px 20px 32px 20px;
            box-sizing: border-box;
            .bottom-right-title {
                justify-content: space-between;
                .bottom-right-header {
                    align-items: center;
                    .icon {
                        width: 6px;
                        height: 16px;
                        background: #2D84FB;
                    }
                    .title {
                        color: #2E3C4E;
                        font-size: 20px;
                        font-weight: bold;
                        margin-left: 5px;
                    }
                }
                .bottom-right-button {
                    .el-button {
                        padding: 5px 10px;
                    }
                }
            }
            .bottom-right-main {
                height: 293px;
                overflow-y: auto;
                position: relative;
                margin-top: 22px;
                border: 1px solid #DDE1E9;
                .main-box {
                    background: #FFFFFF;
                    .content {
                        height: 50px;
                        box-sizing: border-box;
                        color: #2E3C4E;
                        font-size: 12px;
                        text-align: center;
                        padding: 17px 0;
                        flex: 1;
                        ::v-deep .el-input {
                            .el-input__inner {
                                height: 20px;
                                border: none;
                                background: none;
                                text-align: center;
                            }
                        }
                    }
                    .content:last-child {
                        border-left: 1px solid #DDE1E9;
                    }
                }
                .main-box:nth-child(odd) {
                    background: #E8F1FF;
                }
            }
            // .bottom-right-main::after {
            //     position: absolute;
            //     top: 0;
            //     content: "";
            //     width: 200%;
            //     height: 200%;
            //     border: 1px solid #DDE1E9;
            //     transform-origin: 0 0;
            //     transform: scale(0.5);
            // }
        }
    }
    .global-center {
        text-align: center;
    }
}
</style>