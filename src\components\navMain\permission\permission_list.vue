<template>
  <el-container>
    <el-header class="div row">
      <el-button type="primary" @click="createData">添加</el-button>
      <div class="div row search-box">
        <el-input
          @input="onInput"
          v-model="params.name"
          placeholder="请输入角色名称"
        ></el-input>
        <el-button @click="search" type="primary" icon="el-icon-search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate" :before-close="closeAdd">
      <el-form :model="form_create" label-width="150px" :rules="rules" ref="addRef">
        <el-form-item label="角色名称：" prop="name">
          <el-input
            style="width:300px"
            v-model="form_create.name"
            placeholder="角色名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="允许企业微信登录：">
          <el-radio-group v-model="form_create.allow_qywx_login">
            <el-radio
              v-for="item in radio_list"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="允许浏览器端登录：">
          <el-radio-group v-model="form_create.allow_wap_login">
            <el-radio
              v-for="item in radio_list"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item
          v-if="switch_type"
          label="初始化授权："
          prop="init_permission"
        >
          <el-switch
            v-model="form_create.init_permission"
            active-color="#13ce66"
            inactive-color="#ff4949"
          >
          </el-switch>
        </el-form-item> -->
        <el-form-item>
          <el-button style="margin:0" type="primary" @click="onCreate"
            >确定</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="权限分配" :visible.sync="dialogRole">
      <el-tabs
        v-if="per_tabs.length > 1"
        v-model="permission_form.menu_mode_id"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="item in per_tabs"
          :key="item.menu_mode_id"
          :label="item.title"
          :name="item.menu_mode_id"
        ></el-tab-pane>
      </el-tabs>
      <el-form :model="permission_form">
        <el-form-item label="权限分配：" class="tree-box">
          <el-tree
            @check="onCheckChangeReport"
            style="margin-left:80px"
            :data="new_permission_list"
            show-checkbox
            default-expand-all
            node-key="id"
            ref="tree"
            :default-expanded-keys="default_expanded_keys"
            highlight-current
            :props="defaultProps"
          >
          </el-tree>
        </el-form-item>
        <el-form-item label="选择后重置权限：">
          <el-button type="primary" @click="resetPer" size="mini"
            >重置权限</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "permission_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
      },
      form_create: {
        allow_qywx_login: 1,
        allow_wap_login: 1,
        name: "",
      },
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      dialogCreate: false,
      switch_type: true,
      permission_list: [],
      // 重置权限
      permission_form: {
        menu_mode_id: "",
        id: "",
        permission_names: [],
      },
      dialogRole: false,
      role_permission_list: [],
      filter_permission_list: [],
      defaultProps: {
        children: "children",
        label: "title",
      },
      submit_permission: false,
      default_expanded_keys: [],
      checked_permission_list: [],
      checkAll: false,
      table_header: [
        // { prop: "id", label: "ID", width: "80" },
        { prop: "website_id", label: "系统ID", width: 80 },
        {
          label: "角色名称",
          render: (h, data) => {
            return (
              <el-tag type={data.row.name === "站长" ? "success" : "primary"}>
                {data.row.name === "站长" ? "创始人" : data.row.name}
              </el-tag>
            );
          },
        },
        { prop: "created_at", label: "创建时间" },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {data.row.name !== "站长" ? (
                  <div>
                    <el-button
                      type="success"
                      size="mini"
                      onClick={() => {
                        this.changeData(data.row);
                      }}
                    >
                      修改
                    </el-button>
                    <el-button
                      type="primary"
                      size="mini"
                      onClick={() => {
                        this.onShow(data.row);
                      }}
                    >
                      分配
                    </el-button>
                    <el-button
                      type="danger"
                      size="mini"
                      onClick={() => {
                        this.deleteData(data.row);
                      }}
                    >
                      删除
                    </el-button>
                  </div>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
      radio_list: [
        { id: 1, name: "允许" },
        { id: 0, name: "拒绝" },
      ],
      per_tabs: [],
      report_form: [],
      //输入框验证
      rules:{
        name:[{ required: true, message: "请输入角色名称", trigger: "blur" },]
      },
    };
  },
  computed: {
    new_permission_list() {
      let per1 = this.checked_permission_list.map((item) => {
        if (item.type.includes(this.permission_form.menu_mode_id)) {
          return item;
        }
      });
      let arr = this.$toTree(this.arrFilter(per1));
      return arr;
    },
  },
  mounted() {
    this.getDataList();
    this.getPermissionList();
  },
  methods: {
    //关闭添加之前的回调
    closeAdd(){
      this.dialogCreate=false;
      this.$refs.addRef.resetFields();
      this.form_create={
        allow_qywx_login: 1,
        allow_wap_login: 1,
        name: "",
      }
    },
    arrFilter(arr) {
      var newarr = [];
      arr.forEach((item) => {
        if (item !== undefined) {
          newarr.push(item);
        }
      });
      return newarr;
    },
    // 获取权限列表
    getPermissionList() {
      this.$http.getRolesPermissionSlide().then((res) => {
        if (res.status === 200) {
          this.checked_permission_list = res.data.permissions;
          this.per_tabs = res.data.modes.map((item) => {
            return {
              menu_mode_id: item.menu_mode_id + "",
              title: item.title,
            };
          });
          this.permission_form.menu_mode_id = this.per_tabs[0].menu_mode_id;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      if (!this.params.name) {
        delete this.params.name;
      }
      this.tableData = [];
      this.$http.getWebsiteRoles({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    createData() {
      this.form_create.name = "";
      this.dialogTitle = "addData";
      this.dialogCreate = true;
      // this.switch_type = true;
    },
    onInput() {
      if (this.company_name === "") {
        this.params.currentPage = 1;
        this.getDataList();
      }
    },
    search() {
      this.params.currentPage = 1;
      this.getDataList();
    },
    onCreate() {
      this.form_create.init_permission = 0;
      if (this.dialogTitle === "addData") {
        this.$http.createWebsiteRole(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功，点击分配权限",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.updataWebsiteRole(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    changeData(row) {
      console.log(row);
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
      this.form_create = JSON.parse(JSON.stringify(row));
      this.switch_type = false;
    },
    deleteData(row) {
      this.$confirm("此操作将删除该角色，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteWebsiteRole(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleClick() {
      // this.expandChange(this.permission_form.id);
      // let arr = [];
      // this.role_permission_list.map((item) => {
      //   arr.push(item.id);
      // });
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(this.default_expanded_keys);

      });
    },
    onShow(row) {
      this.permission_form.id = row.id;
      this.expandChange(row.id);
      this.dialogRole = true;
    },
    // 设置权限
    // 点击展开列
    expandChange(id) {
      // 设置进入权限表设置为空防止显示出错
      this.default_expanded_keys = [];
      this.$http.queryRolesPermission(id).then((res) => {
        if (res.status === 200) {
          this.role_permission_list = res.data;
          const pids = [];
          this.role_permission_list.map((item) => {
            if(item.pid != 0) {
              this.default_expanded_keys.push(item.id);
              pids.push(item.pid);
            }
          });

          //独立的一级权限
          this.role_permission_list.forEach((item) => {
            if(item.pid == 0 && !pids.includes(item.id)) {
              this.default_expanded_keys.push(item.id);
            }
          })

          this.$nextTick(() => {
            this.$refs.tree.setCheckedKeys(this.default_expanded_keys);
            this.$forceUpdate();
          });
        }
      });
    },
    onCheckChangeReport(data, node) {
      this.checked_permission_list.map((i1) => {
        if (i1.type.includes(this.permission_form.menu_mode_id)) {
          let isnode = node.checkedKeys.includes(i1.id);
          let index = this.role_permission_list.findIndex(
            (item) => item.id == i1.id
          );
          if (isnode) {
            if (index == -1) {
              this.role_permission_list.push(i1);
            }
          } else {
            if (index != -1) {
              this.role_permission_list.splice(index, 1);
            }
          }
        }
      });
      // this.uniqueArr(this.default_expanded_keys, node.checkedKeys);
    },
    resetPer() {
      var arr = [];
      let num = [];
      // console.log(this.role_permission_list, "role_permission_list1");
      // 遍历选中参数中的pid
      this.role_permission_list.forEach((item) => {
        if(item.pid != 0) {
          num.push(item.pid);
        }
      })
      num = Array.from(new Set(num));
      // 如果没有父级id，则添加父级id
      num.map((item) => {
        let pid = this.role_permission_list.findIndex(list => list.id == item);
        if(pid == -1) {
          let parment = this.checked_permission_list.find(arr => arr.id == item);
          this.role_permission_list.push(parment);
        }
      })
      // console.log(this.role_permission_list, "role_permission_list2");
      this.role_permission_list.forEach((item) => {
        arr.push(item.name);
      });
      this.permission_form.permission_names = arr;

      this.$http.resetPermission(this.permission_form).then((res) => {
        if (res.status === 200) {
          this.dialogRole = false;
          this.$message({
            message: "重置成功",
            type: "success",
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.el-header {
  align-items: center;
  justify-content: space-between;
  .search-box {
    align-items: center;
    .el-input {
      width: 300px;
    }
  }
}
.demo-table-expand {
  font-size: 0;
  align-items: center;
  display: flex;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.el-radio {
  margin: 5px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.el-tree {
  height: 350px;
  overflow-y: auto !important;
  .el-tree-node__content span {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
// 添加虚线tree
.el-tree-node {
  position: relative;
  padding-left: 16px;
}
//节点有间隙，隐藏掉展开按钮就好了,如果觉得空隙没事可以删掉
.el-tree-node__expand-icon.is-leaf {
  display: none;
}
.el-tree-node__children {
  padding-left: 16px;
}

.el-tree-node :last-child:before {
  height: 38px;
}

.el-tree > .el-tree-node:before {
  border-left: none;
}

.el-tree > .el-tree-node:after {
  border-top: none;
}

.el-tree-node:before {
  content: "";
  left: -4px;
  position: absolute;
  right: auto;
  border-width: 1px;
}

.el-tree-node:after {
  content: "";
  left: -4px;
  position: absolute;
  right: auto;
  border-width: 1px;
}

.el-tree-node:before {
  border-left: 1px dashed #4386c6;
  bottom: 0px;
  height: 100%;
  top: -26px;
  width: 1px;
}

.el-tree-node:after {
  border-top: 1px dashed #4386c6;
  height: 20px;
  top: 12px;
  width: 24px;
}
</style>
