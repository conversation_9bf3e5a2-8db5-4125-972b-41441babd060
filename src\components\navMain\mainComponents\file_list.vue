<template>
  <el-container>
    <el-header class="div row">
      <div class="row div">
        <div class="title">附件列表</div>
        <div class="title_number">
          <div>
            当前页面共（
            <i>{{ tableData.length }}</i>
            ）条数据
          </div>
        </div>
        <el-button
          size="mini"
          type="success"
          icon="el-icon-upload2"
          style="margin-left:10px"
          @click.native="uploadFile"
          >上传附件</el-button
        >
      </div>
      <div class="div row">
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            附件名称<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="附件名称">附件名称</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-input
          @change="onChange"
          v-model="input"
          placeholder="搜索相关附件"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <el-main>
      <myTable v-loading="is_table_loading"  :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <!-- 分页 -->
    <div class="pagination-box">
      <myPagination
        :total="params.total"
        :pagesize="params.pagesize"
        :currentPage="params.currentPage"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="50%">
      <el-form label-width="100px" :model="form" label-position="right">
        <el-form-item label="附件名称：">
          <el-input type="text" v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="附件资料：">
          <el-upload
            class="upload-demo"
            :headers="myHeader"
            action="/api/common/file/upload/admin?category=13"
            :on-success="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            multiple
            :limit="1"
            drag
            :file-list="fileList"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div slot="tip" class="el-upload__tip">
              建议上传doc, xls, ppt, pdf, docx, xlsx, pptx格式文件
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item
          ><el-button type="primary" @click="uploadData"
            >提交</el-button
          ></el-form-item
        >
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "file_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      input: "",
      build_id: "",
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
        name: "",
      },
      dialogVisible: false,
      form: {
        build_id: "",
        name: "",
        file: "",
      },
      fileList: [],
      disableFile: false,
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "build_id", label: "楼盘ID", width: "100" },
        { prop: "name", label: "附件名称" },
        {
          label: "附件地址",
          render: (h, data) => {
            return (
              <a href={data.row.file} target="_blank">
                跳转附件
              </a>
            );
          },
        },
        {
          prop: "created_at",
          label: "创建时间",
        },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return (
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="danger"
                onClick={() => {
                  this.handleDelete(data.row);
                }}
              >
                删除
              </el-button>
            );
          },
        },
      ],
      is_table_loading:false,
    };
  },

  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
  },
  mounted() {
    this.build_id = this.$route.query.build_id;
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.is_table_loading = true
      this.$http
        .getFileList(this.build_id, { params: this.params })
        .then((res) => {
          if (res.status === 200) {
            this.is_table_loading = false
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    // getPageData() {
    //   let start = (this.params.currentPage - 1) * this.params.pagesize;
    //   let end = start + this.params.pagesize;
    //   this.schArr = this.tableData.slice(start, end);
    // },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      // this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      console.log(val);
      this.params.page = val;
      // this.getPageData();
      this.getDataList();
    },
    onChange() {
      this.search();
    },
    search() {
      this.params.name = this.input;
      this.params.page = 1;
      this.getDataList();
    },
    handleCommand(command) {
      this.$message(command);
    },
    handleDelete(row) {
      this.$confirm("此操作将删除该附件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteFile(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    uploadFile() {
      this.dialogVisible = true;
    },
    // 上传
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      this.form.file = file.url;
      if (this.form.file) {
        this.disableFile = true;
      }
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    uploadData() {
      if (!this.form.name) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
      } else if (this.disableFile === false) {
        this.$message({
          message: "上传成功后在提交",
          type: "error",
        });
        return;
      } else {
        this.form.build_id = this.build_id;
        this.$http.uploadFile(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.form.name = "";
            this.form.file = "";
            this.fileList = [];
            this.getDataList();
            this.dialogVisible = false;
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
</style>
