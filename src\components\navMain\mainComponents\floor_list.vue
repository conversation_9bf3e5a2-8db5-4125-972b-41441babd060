<template>
  <el-container>
    <el-main>
      <el-button type="primary" size="mini" @click="createData">添加</el-button>
      <myTable :table-list="tableData" :header="table_header"></myTable>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :pagesize="params.per_page"
            :currentPage="params.page"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-main>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_create" label-width="100px">
        <el-form-item label="楼号名称">
          <el-input
            v-model="form_create.name"
            placeholder="请输入楼号名称（例：1#）"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onCreate">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="绑定户型" :visible.sync="dialogHouse">
      <el-form :model="form_create_house">
        <el-form-item label="选择户型">
          <el-select
            size="mini"
            v-model="form_create_house.build_house_type_id"
            placeholder="请选择户型"
          >
            <el-option
              v-for="item in house_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="createHouse"
            >添加户型</el-button
          >
        </el-form-item>
      </el-form>
      <myTable
        :table-list="floor_house_list"
        :header="table_header_floor"
      ></myTable>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="bind_params.total"
            :pagesize="bind_params.per_page"
            :currentPage="bind_params.page"
            @handleSizeChange="handleSizeChangeBind"
            @handleCurrentChange="handleCurrentChangeBind"
          ></myPagination>
        </div>
      </el-footer>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import { mapState } from "vuex";
export default {
  name: "floor_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      bind_params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      dialogCreate: false,
      form_create: {},
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      dialogHouse: false,
      form_create_house: {},
      house_list: [],
      floor_house_list: [],
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "name", label: "楼号" },
        { prop: "created_at", label: "创建时间" },
        { prop: "updated_at", label: "修改时间" },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
              {this.website_info.website_mode_category===1?<el-button size="mini" type="danger" onClick={()=>{
                  this.delBuilding(data.row)
                }}>删除</el-button>:""}
                <el-button
                  size="mini"
                  type="success"
                  onClick={() => {
                    this.updataData(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  size="mini"
                  type="primary"
                  onClick={() => {
                    this.bindHouseType(data.row);
                  }}
                >
                  绑定户型
                </el-button>
              </div>
            );
          },
        },
      ],
      table_header_floor: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "build_no_name", label: "楼号" },
        { prop: "build_house_type_name", label: "户型" },
        {
          label: "操作",
          fixed: "right",
          width: "120",
          render: (h, data) => {
            return (
              <el-button
                icon="el-icon-delete"
                type="danger"
                size="mini"
                onClick={() => {
                  this.deleteFloorForHouse(data.row);
                }}
              >
                解除绑定
              </el-button>
            );
          },
        },
      ],
    };
  },
  computed:{
    ...mapState(["website_info"])
  },
  mounted() {
    this.getDataList();
    this.getHouseList();
    this.getFloorForHouse();
  },
  methods: {
    getDataList() {
      this.params.build_id = this.$route.query.build_id;
      this.$http.getFloorList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    //删除楼号
    delBuilding(e){
      this.$confirm('此操作将删除该楼号, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.deleteFloorData(e.id).then((res)=>{
            if(res.status===200){
              this.$message({
              type: 'success',
              message: '删除成功!'
          });
            this.getDataList()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    //获取户型
    getHouseList() {
      this.$http.getBuildList(this.$route.query.build_id).then((res) => {
        if (res.status === 200) {
          this.house_list = res.data;
        }
      });
    },
    // 获取楼号绑定户型
    getFloorForHouse() {
      this.$http.getFloorForHouse(this.$route.query.build_id).then((res) => {
        if (res.status === 200) {
          this.floor_house_list = res.data.data;
          this.bind_params.total = res.data.total;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageDataBind() {
      let start = (this.bind_params.page - 1) * this.bind_params.per_page;
      let end = start + this.bind_params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChangeBind(val) {
      this.bind_params.per_page = val;
      this.getPageDataBind();
      this.getFloorForHouse();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChangeBind(val) {
      this.bind_params.page = val;
      this.getPageDataBind();
      this.getFloorForHouse();
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    createData() {
      this.dialogCreate = true;
      this.form_create = {
        build_id: this.$route.query.build_id,
      };
      this.dialogTitle = "addData";
    },
    updataData(row) {
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
      this.form_create = row;
    },
    // 提交
    onCreate() {
      if (this.dialogTitle === "addData") {
        this.$http.createFloorData(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.updataFloorData(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    // 删除
    deleteData(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteFloorData(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {});
    },
    bindHouseType(row) {
      this.form_create_house = {
        build_no_id: row.id,
      };
      this.dialogHouse = true;
    },
    createHouse() {
      if (!this.form_create_house.build_house_type_id) {
        this.$message({
          message: "请选择户型提交",
          type: "error",
        });
        return;
      }
      this.$http.bindHouseForFloor(this.form_create_house).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "绑定成功",
            type: "success",
          });
          this.getFloorForHouse();
        }
      });
    },
    deleteFloorHouse(row) {
      this.$confirm("此操作将解除绑定, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteFloorForHouse(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "已解除",
              });
              this.getFloorForHouse();
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style></style>
