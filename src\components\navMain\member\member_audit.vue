<template>
  <el-container>
    <el-header style="margin-bottom: 20px;">
      <new_tips_list :tipsList="tips_list"></new_tips_list>
    </el-header>
    <div class="div row table-title">
     <div class="badgeContent">
      <span class="badgeItem">{{unrevTotal}}</span>
      <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="item.description" :name="item.value" v-for="item in audit_list" :key="item"></el-tab-pane>
      </el-tabs>
     </div>
      <div class="div row search">
        <el-input
          v-model="params.name"
          placeholder="请输入用户姓名"
          @input="onInput"
          @change="onChangeName"
          style="margin-right:10px"
        ></el-input>
        <el-button type="primary" @click="searchName">搜索</el-button>
      </div>
    </div>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleCurrentChange="handleCurrentChange"
          @handleSizeChange="handleSizeChange"
        ></myPagination>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
import new_tips_list from "@/components/components/new_tips_list"
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "member_audit",
  components: { myPagination, myTable,new_tips_list },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        audit_status: 1,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
      },
      audit_status: "",
      audit_list: [
        { value: "0", description: "待审核", id: 0 },
        { value: "1", description: "审核通过", id: 1 },
        { value: "2", description: "审核不通过", id: 2 },
      ],
      form: {
        id: "",
        audit_status: "",
      },
      verified_list: [],
      tips_list: [
        "管理人员可以在此页面进行用户的审核，审核某位用户实名认证是否通过",
      ],
      table_header: [
        {
          label: "申请用户",
          render: (h, data) => {
            return (
              <div>
                <p>
                  ID：{data.row.id}</p>   
                <p
                domPropsInnerHTML={
                  data.row.name 
                }
              ></p></div>
             
            );
          },
        },
        { prop: "name", label: "真实姓名" },
        { prop: "credentials_category", label: "证件类型" },
        { prop: "credentials_number", label: "证件号码" },
        {
          label: "证件图片（正）",
          render: (h, data) => {
            return (
              <el-popover placement="right" width="500px" trigger="hover">
                <img
                  width="300px"
                  height="300px"
                  src={data.row.img_front}
                  class="imgStyle"
                />
                <img
                  slot="reference"
                  src={this.$imageFilter(
                    data.row.img_front ||
                      "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                  style="max-height:50px;max-width:100px"
                />
              </el-popover>
            );
          },
        },
        {
          label: "证件图片（反）",
          render: (h, data) => {
            return (
              <el-popover placement="right" width="500px" trigger="hover">
                <img
                  width="300px"
                  height="300px"
                  src={data.row.img_back}
                  class="imgStyle"
                />
                <img
                  slot="reference"
                  src={this.$imageFilter(
                    data.row.img_back ||
                      "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                  style="max-height:50px;max-width:100px"
                />
              </el-popover>
            );
          },
        },
        { prop: "start_valid_at", label: "证件有效期/开始" },
        { prop: "end_valid_at", label: "证件有效期/结束" },
        { prop: "created_at", label: "添加时间" },
        {
          label: "操作",
          width: "250",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {(this.params.audit_status === 2 &&
                data.row.audit_status === 2 &&
                this.$hasShow("会员审核通过"))||(this.params.audit_status===0&&data.row.audit_status === 0)?(
                  <el-button
                    type="success"
                    size="mini"
                    style="border-radius:5px"
                    onClick={() => {
                      this.onAudit(data.row);
                    }}
                  >
                    审核通过
                  </el-button>
                ) : (
                  ""
                )}
                {(this.params.audit_status === 1 &&
                data.row.audit_status === 1 &&
                this.$hasShow("会员审核不通过"))||(this.params.audit_status===0&&data.row.audit_status === 0)?(
                  <el-button
                    type="danger"
                    size="mini"
                    style="border-radius:5px"
                    onClick={() => {
                      this.onAuditRefuse(data.row);
                    }}
                  >审核不通过</el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
      activeName: "1",
      unrevTotal:"",
    };
  },
  mounted() {
    this.getVerified();
    this.getUnrevTotal()
    this.getAuditData();
  },
  methods: {
    //tab切换
    handleClick(tab){
      this.params.audit_status = tab.name*1;
      this.getAuditData();
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getAuditData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getAuditData();
    },
    getVerified() {
      this.verified_list = this.$getDictionary("CREDENTIALS_CATEGORY");
    },
    // 获取列表
    getAuditData() {
      // this.audit_list = this.$getDictionary("SALE_ORDER_REFUND_AUDIT_STATUS");
      this.$http.getAuditList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          setTimeout(() => {
            res.data.data.map((item, index) => {
              this.verified_list.map((item2, index2) => {
                if (item.credentials_category === parseInt(item2.value)) {
                  res.data.data[index][
                    "credentials_category"
                  ] = this.verified_list[index2]["description"];
                  return;
                }
              });
            });
            this.tableData = res.data.data;
          }, 500);
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
        if(this.params.audit_status===0){
          this.unrevTotal=this.params.total
        }
      });
      
    },
    onAudit(row) {
      this.$confirm('此操作将审核通过, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.id = row.id;
      this.form.audit_status = 1;
      if (this.form) {
        this.$http.updateAudit(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
          }
          this.getAuditData();
        });
      }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });          
        });

    },
    onAuditRefuse(row) {
      this.$confirm('此操作将审核不通过, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.id = row.id;
          this.form.audit_status = 2;
          if (this.form) {
          this.$http.updateAudit(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
          }
          this.getAuditData();
        });
      }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });          
        });
    },
    onInput(e) {
      if (!e) {
        this.params.name = "";
        this.getAuditData();
      }
    },
    searchName() {
      this.params.audit_status;
      this.params.current_page = 1;
      this.getAuditData();
    },
    onChangeName() {
      this.searchName();
    },
    getUnrevTotal(){
      let newParams={
        page: 1,
        audit_status: 0,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
      }
      this.$http.getAuditList({ params: newParams }).then((res)=>{
        this.is_table_loading = false;
        if(res.status==200){
          
          this.unrevTotal=res.data.total
        }
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.table-title {
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}
.el-select {
  margin: 5px 0;
}
.img-box {
  width: 100px;
  height: 50px;
  el-image {
    width: 100%;
  }
}
.title {
  color: #333;
  font-weight: bold;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
  }
  i {
    color: red;
  }
}
.el-main {
  margin-top: 30px;
  .title {
    margin: 10px 0;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
}
.badgeContent{
  position: relative;
  .badgeItem{
    position: absolute;
    top:-6px;
    left: 40px;
    background: #F56C6C;
    padding: 1px 5px;
    color: #fff;
    font-size: 12px;
    border-radius: 50%;
  }
}
</style>
