<template>
    <div class="import-container">
        <el-form ref="form" :model="params" label-width="120px">
            <el-form-item label="导入类型">
                <el-select v-model="params.cate" placeholder="请选择">
                    <el-option v-for="item in importCateList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="excel 文件">
                <el-upload class="upload-demo" accept=".xls,.xlsx" action :auto-upload="false" :limit="1" :class="{'has-file':params.file}"
                    :on-remove="clearUploadFile" :on-change="onUploadChange" ref="upload">
                    <el-button v-if="!params.file">选择文件</el-button>
                </el-upload>
            </el-form-item>
            <el-form-item label="">
                <el-button type="primary" @click="submit" :loading="submiting">确认导入</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data(){
        return {
            params: {           //表单参数
                cate: '',
                file: null
            },
            importCateList: [
                {value: 2, label: '导入组织机构'},
                {value: 1, label: '导入客户'},
                {value: 3, label: '导入跟进'},
            ],
            submiting: false
        }
    },
    methods: {
        async submit(){
            if(!this.params.cate){
                this.$message.error('请选择上传分类');
                return;
            }
            if(!this.params.file){
                this.$message.error('请上传 excel 文件');
                return;
            }

            const formData = new FormData();
            formData.append('cate', this.params.cate);
            formData.append('file', this.params.file.raw);
            formData.append('website_id', localStorage.getItem("website_id"));
            this.submiting = true;
            try{
                const res = await this.$http.commonDawuImport(formData);    
                if(res.status == 200){
                    this.$message.success(res?.data?.msg || '上传成功');
                    this.clearUploadFile();
                }
            }catch(e){
                console.error(e);
            }
            this.submiting = false;
        },
        onUploadChange(file){
            this.params.file = file;
        },
        clearUploadFile(){
            this.$refs.upload.clearFiles();
            this.params.file = null;
        }
    }
}
</script>
<style lang="scss" scoped>
.import-container{
    display: flex;
    justify-content: center;
    padding-top: 10vh;
}
::v-deep .el-form-item__content{
    line-height: 1;    
}
.has-file::v-deep .el-upload{
    display: none;
}
::v-deep .el-upload-list__item{
    transition: none;
}
</style>