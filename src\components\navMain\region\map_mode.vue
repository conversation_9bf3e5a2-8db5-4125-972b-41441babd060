<template>
  <div class="header_catalogue">
    <div class="higher_ups">
      <span class="span1" @click="to_superior">返回上级</span>
      <span class="span1"> / </span>
      <span class="span2">春风里区域拓客</span>
    </div>
    <div class="dynatown">
      <div class="Real_area">
        <div class="area—_consultant"><span>置业顾问</span></div>
        <div><span>8</span></div>
      </div>
      <div class="Real_area">
        <div class="area—_consultant"><span>裂变用户</span></div>
        <div><span>3887</span></div>
      </div>
      <div class="Real_area">
        <div class="area—_consultant"><span>客户线索</span></div>
        <div><span>3887</span></div>
      </div>
      <div class="Real_area">
        <div class="area—_consultant"><span>红包数</span></div>
        <div><span>2000</span></div>
      </div>
    </div>
    <div class="Expansion_area">
      <span class="span1">拓客区域</span>
    </div>
    <div class="map_type">
      <div class="search_for">
        <div style="width: 240px">
          <el-input prefix-icon="el-icon-search" placeholder="请输入内容" v-model="tableres.keywords"
            class="input-with-select">
          </el-input>
        </div>
        <div class="select">
          <el-select v-model="tableres.status" slot="prepend" placeholder="是否禁用红包 ">
            <el-option label="全部" value=""></el-option>
            <el-option label="已禁用" :value="0"></el-option>
            <el-option label="已启用" :value="1"></el-option>
          </el-select>
        </div>
        <div class="search_btn">
          <el-button type="primary" @click="search_for">搜索</el-button>
        </div>
        <div class="sign_icon">
          <div class="sign" @click="list">
            <i class="el-icon-s-unfold" :class="color"></i>
            <div class="div" :class="div1">
              <span>列表模式</span>
            </div>
          </div>
          <div class="sign1" @click="plat">
            <i class="el-icon-location-outline" :class="color1"></i>
            <div class="div" :class="div2">
              <span>地图模式</span>
            </div>
          </div>
        </div>
      </div>
      <div class="map_map">
        <div id="bai-du-map" v-show="show"></div>
        <div class="List_Mode" v-show="show1">
          <el-table :data="tableData" border style="width: 100%" :header-cell-style="{
              background: '#DDE1E9',
              color: '#2E3C4E',
            }">
            <el-table-column prop="id" label="ID" align="center">
            </el-table-column>
            <el-table-column prop="name" label="区域名称" align="center">
            </el-table-column>
            <el-table-column prop="range" label="区域范围" align="center">
              <template slot-scope="scope">
                <i class="el-icon-location-outline" style="cursor: pointer;" @click="to_view(scope.row.range)">点击查看</i>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="红包状态" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status == 0 ? 'danger' : 'success'">{{
                  scope.row.status === 0 ? "禁用" : "启用" }}</el-tag>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="num" label="参与数" align="center">
            </el-table-column> -->
            <el-table-column prop="address" label="操作" align="center">
              <template slot-scope="scope">
                <el-button plain @click="modify(scope.row)">编辑</el-button>
                <el-button plain @click="Region_del(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="paging">
            <el-pagination background layout="pager" :total="tableres.total" @current-change="handleCurrentChange"
              :current-page="tableres.page" :page-size="tableres.per_page">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <div class="Expansion_area">
      <span class="span1">客户线索</span>
    </div>
    <div class="Customer_Leads">
      <div class="search_for">
        <div style="width: 240px">
          <el-input prefix-icon="el-icon-search" placeholder="请输入内容" v-model="input3" class="input-with-select">
          </el-input>
        </div>
        <div class="search_btn">
          <el-button type="primary">搜索</el-button>
        </div>
        <div class="lead">
          <div>
            <span>裂变客户</span>
            <span class="lead_number">3887</span>
          </div>
          <div>
            <span>客户线索</span>
            <span class="lead_number">3887</span>
          </div>
          <div>
            <span>有效客户</span>
            <span class="lead_number">2000</span>
          </div>
        </div>
        <div class="lead_select">
          <div>
            <span>置业顾问</span>
            <el-select v-model="value" filterable placeholder="全部" style="width: 120px">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div>
            <span>是否有效</span>
            <el-select v-model="value" filterable placeholder="全部" style="width: 120px">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="List_Mode">
        <el-table :data="CustomerData" border style="width: 100%" :header-cell-style="{
            background: '#DDE1E9',
            color: '#2E3C4E',
          }">
          <el-table-column prop="id" label="ID" align="center">
          </el-table-column>
          <el-table-column prop="Area_Name" label="客户姓名" align="center">
          </el-table-column>
          <el-table-column prop="date" label="手机号" align="center">
          </el-table-column>
          <el-table-column prop="name" label="微信昵称" align="center">
          </el-table-column>
          <el-table-column prop="num" label="所属置业顾问" align="center">
          </el-table-column>
          <el-table-column prop="effective" label="是否有效" align="center">
          </el-table-column>
          <el-table-column prop="Filing_date" label="报备日期" align="center">
          </el-table-column>
        </el-table>
      </div>
      <div class="leading-out">
        <div>
          <i class="el-icon-location-outline"></i>
          <span>导出数据</span>
        </div>
        <div>
          <el-pagination background layout="pager" :total="50"> </el-pagination>
        </div>
      </div>
    </div>
    <div class="Expansion_area">
      <span class="span1">红包记录</span>
    </div>
    <div class="Red_envelope_record">
      <div class="search_for">
        <div style="width: 240px">
          <el-input prefix-icon="el-icon-search" placeholder="请输入内容" v-model="input3" class="input-with-select">
          </el-input>
        </div>
        <div class="search_btn">
          <el-button type="primary">搜索</el-button>
        </div>
        <div class="lead">
          <div>
            <span>红包数量</span>
            <span class="lead_number">3887</span>
          </div>
          <div>
            <span>活动总数额</span>
            <span class="lead_number">3887元</span>
          </div>
          <div>
            <span>活动剩余金额</span>
            <span class="lead_number">2000元</span>
          </div>
        </div>
      </div>
      <div class="List_Mode">
        <el-table :data="red_packet" border style="width: 100%" :header-cell-style="{
            background: '#DDE1E9',
            color: '#2E3C4E',
          }">
          <el-table-column prop="id" label="ID" align="center">
          </el-table-column>
          <el-table-column prop="Area_Name" label="微信昵称" align="center">
          </el-table-column>
          <el-table-column prop="date" label="红包金额" align="center">
          </el-table-column>
          <el-table-column prop="name" label="客户姓名" align="center">
          </el-table-column>
          <el-table-column prop="phone_number" label="手机号" align="center">
          </el-table-column>
          <el-table-column prop="Filing_date" label="发放时间" align="center">
          </el-table-column>
        </el-table>
        <div class="paging">
          <el-pagination background layout="pager" :total="50"> </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'TdtMap',
  data() {
    return {
      tdtMap: {},
      input3: "",
      show: false,
      show1: true,
      color: 'c1',
      div1: "c2",
      color1: '',
      div2: '',
      tableData: [{}],
      tableres: {
        keywords: "",
        ststus: ""
      },
      CustomerData: [{
        id: "01",
        Area_Name: "王先生",
        name: "快乐一生",
        date: '133 0089 7786',
        num: '陈圆圆',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "02",
        Area_Name: "李女士",
        name: "足球先生",
        date: '133 0089 7786',
        num: '万泽名',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "03",
        Area_Name: "刘先生",
        name: "潇洒一生",
        date: '133 0089 7786',
        num: '李玉刚',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "04",
        Area_Name: "马先生",
        name: "潇洒一生",
        date: '133 0089 7786',
        num: '马芳芳',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "05",
        Area_Name: "万先生",
        name: "小黄鸭",
        date: '133 0089 7786',
        num: '马芳芳',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "06",
        Area_Name: "刘先生",
        name: "清风",
        date: '133 0089 7786',
        num: '马婉茹',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "07",
        Area_Name: "李女士",
        name: "明月江南",
        date: '133 0089 7786',
        num: '马婉茹',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "08",
        Area_Name: "李女士",
        name: "明月江南",
        date: '133 0089 7786',
        num: '马婉茹',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "09",
        Area_Name: "李女士",
        name: "明月江南",
        date: '133 0089 7786',
        num: '马婉茹',
        effective: "有效",
        Filing_date: "2023.03.08 12:00"
      }],
      red_packet: [{
        id: "01",
        Area_Name: "快乐一生",
        date: "1.98",
        name: '王先生',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "02",
        Area_Name: "足球先生",
        date: "0.98",
        name: '李先生',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "03",
        Area_Name: "潇洒一生",
        date: "22",
        name: '刘先生',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "04",
        Area_Name: "潇洒一生",
        date: "10.9",
        name: '刘女士',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "05",
        Area_Name: "小黄鸭",
        date: "3",
        name: '马女士',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "06",
        Area_Name: "清风",
        date: "5",
        name: '王先生',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }, {
        id: "07",
        Area_Name: "明月江南",
        date: "8",
        name: '刘女士',
        num: '陈圆圆',
        phone_number: "133 0098 7786",
        Filing_date: "2023.03.08 12:00"
      }],
      options1: [{
        value: '选项1',
        label: '黄金糕'
      }],
      value: '',
      options: []
    }
  },
  methods: {
    // 初始化天地图
    initTdtMap() {
      var T = window.T
      console.log(T);
      this.tdtMap = new T.Map('bai-du-map');
      //设置显示地图的中心点和级别
      this.tdtMap.centerAndZoom(new T.LngLat(117.163139, 35.079158), 12);
      // 7.创建坐标，通常是调取接口获得经纬度
      // const point = new T.LngLat(112.9388, 28.2280);
      // 8.创建覆盖使用的图标
      // const icon = new T.Icon({
      //   iconUrl: '../marker-red.png',
      //   iconSize: new T.Point(27, 27),
      //   iconAnchor: new T.Point(10, 25)
      // });
      // 9. 创建在该坐标上的一个图像标注实例
      // const marker = new T.Marker(point, icon);
      // 10.将覆盖物添加到地图中，一个覆盖物实例只能向地图中添加一次
      // this.tdtMap.addOverLay(marker);
      var points = []
      this.options.map(item => {
        points.push(item)
        // console.log(item);
      })
      // console.log(this.options);
      var polygon = new T.Polygon(points, { color: "#2D84FB", weight: 1, opacity: 0.5, fillColor: "#2D84FB", fillOpacity: 0.51, lineStyle: "dashed" });
      this.tdtMap.addOverLay(polygon)
    },
    //列表模式
    list() {
      this.show = false
      this.show1 = true
      this.color = "c1"
      this.div1 = "c2"
      this.color1 = "el-icon-location-outline"
      this.div2 = "div1"
    },
    //地图模式
    plat() {
      this.show = true
      this.show1 = false
      // console.log(this.tableData);
      var regionData = []
      this.tableData.map(item => {
        // console.log(item.range);
        regionData.push(item.range)
        // console.log(regionData);
        this.options = []
        this.options = regionData
      })
      setTimeout(() => {
        this.color = "el-icon-location-outline"
        this.div1 = "div1"
        this.color1 = "c1"
        this.div2 = "c2"
        this.initTdtMap()
      }, 200)
    },
    //拓客列表
    Tuo_KeList() {
      this.$http.Expansion_arealist(this.tableres).then(res => {
        // console.log(res);
        var { data } = res.data
        if (res.status == 200) {
          // console.log(res.data);
          this.tableres.current_page = res.data.current_page
          this.tableres.per_page = res.data.per_page
          this.tableres.total = res.data.total
          // console.log(this.tableres);
          this.tableData = data
          // console.log(this.tableData);
        }
      })
    },
    //分页
    handleCurrentChange(val) {
      this.tableres.page = val
      this.Tuo_KeList()
    },
    //模糊搜索
    search_for() {
      this.tableres.per_page=""
      this.Tuo_KeList()
    },
    //点击查看区域范围
    to_view(range) {
      this.options = range
      // this.plat()
      this.show = true
      this.show1 = false
      setTimeout(() => {
        this.color = "el-icon-location-outline"
        this.div1 = "div1"
        this.color1 = "c1"
        this.div2 = "c2"
        this.initTdtMap()
      }, 200)
    },
    //区域信息修改
    modify(regdata) {
      // console.log(123);
      this.$goPath(`Region_modification`)
      setTimeout(()=>{
        window.eventBus.$emit("reg1", regdata)
      },300)
     
    },
    //删除区域
    Region_del(id) {
      this.$confirm('此操作将永久删除该区域, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableres.per_page=""
        this.$http.Region_deletion(id).then(res => {
          // console.log(res);
          if (res.status == 200) {
            this.$message({
              type: "success",
              message: "删除成功"
            })
            this.Tuo_KeList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    //返回添加区域
    to_superior(){
      this.$goPath(`Regional_expansion`)
    }
  },
  mounted() {
    this.Tuo_KeList()
  }
}
</script>
<style scoped lang="scss" >
.header_catalogue {
  background: #f1f4fa;
  margin: -14px;
  padding: 24px;

  .higher_ups {
    width: 200px;
    height: 30px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;

    .span1 {
      color: #8a929f;
    }

    .span2 {
      color: #2d84fb;
    }
  }

  .dynatown {
    width: 96%;
    height: 200px;
    // background-color: cornflowerblue;
    margin: 14px auto;
    display: flex;
    justify-content: space-between;

    .Real_area {
      width: 320px;
      height: 200px;
      background-color: #ffffff;
      border-radius: 4px;
      text-align: center;
      line-height: 60px;

      .area—_consultant {
        margin-top: 30px;
        font-size: 17px;
        color: #8a929f;
      }

      div {
        font-size: 32px;
        color: #2e3c4e;
      }
    }
  }

  .Expansion_area {
    .span1 {
      color: #2e3c4e;
      font-weight: 600;
      font-size: 16px;
      margin-left: 35px;
    }
  }

  .map_type {
    width: 96%;
    height: 750px;
    background-color: #ffffff;
    margin: 20px auto;
    overflow: hidden;
    border-radius: 4px;

    .search_for {
      width: 96%;
      height: 50px;
      // background-color: coral;
      margin: 20px auto;
      display: flex;

      .select {
        margin-left: 20px;
      }

      /deep/.el-input__inner {
        height: 32px;
      }

      /deep/.el-input__icon {
        line-height: 33px;
      }

      .el-button {
        width: 76px;
        height: 36px;
        margin-left: 10px;
        line-height: 0;
      }

      .sign_icon {
        width: 1200px;
        height: 24px;
        display: flex;
        justify-content: flex-end;
      }

      .sign {
        width: 90px;
        height: 25px;
        display: flex;
        // background-color: aquamarine;
        justify-content: space-around;
        cursor: pointer;
      }

      .el-icon-location-outline {
        font-size: 25px;
        color: #f1f4fa;
      }

      .el-icon-s-unfold {
        font-size: 25px;
        color: #f1f4fa;
      }

      .div {
        font-size: 12px;
        color: #8a929f;
        margin-top: 5px;
      }

      .c1 {
        color: #2d84fb;
      }

      .c2 {
        color: #2d84fb;
      }

      .sign1 {
        width: 90px;
        height: 25px;
        display: flex;
        // background-color: aqua;
        justify-content: space-around;
        cursor: pointer;
      }
    }

    .map_map {
      width: 96%;
      height: 700px;
      // background-color: cornflowerblue;
      margin: 0 auto;
      overflow: hidden;

      #bai-du-map {
        overflow: hidden;
        width: 100%;
        height: 600px;
        margin: 0;
        font-family: "微软雅黑";
      }

      /* 隐藏高德logo  */
      .amap-logo {
        display: none !important;
      }

      /* 隐藏高德版权  */
      .amap-copyright {
        display: none !important;
      }

      .List_Mode {
        width: 96%;
        height: 500px;
        margin: 30px auto;

        .el-button {
          border-color: #2d84fb;
          color: #2d84fb;
        }

        .paging {
          display: flex;
          justify-content: flex-end;
          margin-top: 20px;
        }
      }
    }
  }

  .Customer_Leads {
    width: 96%;
    height: 670px;
    background-color: #fff;
    margin: 20px auto;
    border-radius: 4px;
    overflow: hidden;

    .search_for {
      width: 96%;
      height: 35px;
      // background-color: coral;
      margin: 20px auto;
      display: flex;

      /deep/.el-input__inner {
        height: 32px;
      }

      /deep/.el-input__icon {
        line-height: 33px;
      }

      .el-button {
        width: 76px;
        height: 36px;
        margin-left: 10px;
        line-height: 0;
      }

      .lead {
        width: 400px;
        height: 35px;
        // background-color: aqua;
        display: flex;
        justify-content: space-between;
        margin-left: 50px;

        div {
          font-size: 14px;
          line-height: 35px;
          color: #2e3c4e;
          margin-left: 10px;

          .lead_number {
            color: #8a929f;
            margin-left: 10px;
          }
        }
      }

      .lead_select {
        width: 450px;
        height: 35px;
        margin-left: 300px;
        // background-color: aqua;
        display: flex;
        justify-content: space-between;

        span {
          margin-right: 10px;
          color: #8a929f;
          font-size: 14px;
        }
      }
    }

    .List_Mode {
      width: 96%;
      height: 500px;
      margin: 30px auto;
    }

    .leading-out {
      width: 96%;
      height: 50px;
      // background-color: aqua;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;

      .el-icon-location-outline {
        color: rgb(163, 160, 152);
        font-size: 20px;
      }

      span {
        color: #2d84fb;
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }

  .Red_envelope_record {
    width: 96%;
    height: 670px;
    background-color: #fff;
    margin: 20px auto;
    border-radius: 4px;
    overflow: hidden;

    .search_for {
      width: 96%;
      height: 35px;
      // background-color: coral;
      margin: 20px auto;
      display: flex;

      /deep/.el-input__inner {
        height: 32px;
      }

      /deep/.el-input__icon {
        line-height: 33px;
      }

      .el-button {
        width: 76px;
        height: 36px;
        margin-left: 10px;
        line-height: 0;
      }

      .lead {
        width: 450px;
        height: 35px;
        // background-color: aqua;
        display: flex;
        justify-content: space-between;
        margin-left: 50px;

        div {
          font-size: 14px;
          line-height: 35px;
          color: #2e3c4e;
          margin-left: 10px;

          .lead_number {
            color: #8a929f;
            margin-left: 10px;
          }
        }
      }

      .lead_select {
        width: 450px;
        height: 35px;
        margin-left: 300px;
        // background-color: aqua;
        display: flex;
        justify-content: space-between;

        span {
          margin-right: 10px;
          color: #8a929f;
          font-size: 14px;
        }
      }
    }

    .List_Mode {
      width: 96%;
      height: 500px;
      margin: 30px auto;

      .el-button {
        border-color: #2d84fb;
        color: #2d84fb;
      }

      .paging {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }
    }
  }
}
</style>