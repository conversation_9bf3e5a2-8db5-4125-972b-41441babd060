<template>
  <div class="pages" id="page">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm flex-row align-center" style="padding: 0 12px">
          <div class="select_box flex-row align-center">
            <span class="text">项目名称：</span>
            <!-- filterable
              remote
              reserve-keyword 
              :remote-method="getProjectData"
              :loading="project_loading"-->
            <el-select v-model="params.project_id" placeholder="请输入项目名称" clearable size="mini" @change="onProject">
              <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
              <!-- <el-option label="" :value="-1">
                <el-button type="primary" icon="el-icon-plus">添加</el-button>
              </el-option> -->
            </el-select>
          </div>
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker style="width: 250px" size="small" v-model="p_time" type="datetimerange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
              @change="changeTimeRange">
            </el-date-picker>
          </div>
          <div style="margin-left: auto; color: #fb656a">每日12点前更新</div>
        </div>
        <div class="content-box-crm" style="background: #f1f4fa">
          <div class="datas_list flex-row items-center">
            <div class="datas_item flex-row items-center flex-1" v-for="(item, index) in statistics" :key="index">
              <div class="datas_item_right">
                <div class="datas_item_right_name flex-row items-center">
                  <span>
                    {{ item.name }}
                  </span>
                  <el-tooltip class="item" effect="light" placement="top" v-if="item.tips">
                    <div slot="content" style="max-width: 300px">
                      {{ item.tips }}
                    </div>
                    <div class="img">
                      <img :src="warning" alt="" />
                    </div>
                  </el-tooltip>
                </div>
                <div class="datas_item_right_top flex-row">
                  <span>{{ item.total | formatNum }} </span>
                  <span class="small"> {{ item.unit }}</span>
                </div>
                <div class="datas_item_right_bottom flex-row">
                  <span> 昨日新增： </span>
                  <span class="change" :class="{ jian: setClass(item.title) }">
                    {{ item.title }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="flex-row align-center mb20">
            <div class="select_box flex-row align-center mr10">
              <span class="">手机号码：</span>
              <el-input size="mini" class="flex-1" v-model="params.phone"></el-input>
            </div>
            <div class="select_box flex-row align-center mr10">
              <span class="">姓名：</span>
              <el-input size="mini" class="flex-1" v-model="params.name"></el-input>
            </div>
            <div class="select_box flex-row align-center mr10">
              <el-button size="mini" type="primary" @click="getList">搜索</el-button>
            </div>
            <!-- <div class="select_box flex-row align-center mr10">
              <span class="">授权状态：</span>
              <el-select
                class="flex-1"
                size="mini"
                v-model="params.status"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div> -->
            <!-- <div class="select_box flex-row align-center mla">
              <el-button size="mini"> 导出 </el-button>
            </div> -->
          </div>
          <el-table v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row :row-style="$TableRowStyle" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
            <el-table-column label="名称" prop="name"> </el-table-column>
            <el-table-column label="抖音" width="150" v-slot="{ row }">
              <div class="d_info flex-row items-center">
                <div class="row_img" v-if="row.avatar" @click="previewImg(row.avatar)">
                  <img :src="row.avatar" alt="" />
                </div>
                <div class="row_img" v-else>
                  <div class="d_name_bg">{{ row.name && row.name[0] }}</div>
                </div>
                <div class="d_name">
                  {{ row.nickname }}
                  <!-- <img :src="row.nickename" alt="" /> -->
                </div>
              </div>
            </el-table-column>
            <!-- <el-table-column label="昵称" prop="nickname"> </el-table-column> -->
            <el-table-column label="电话" prop="phone"> </el-table-column>
            <el-table-column label="播放" prop="total_play"> </el-table-column>
            <el-table-column label="点赞" prop="total_like"> </el-table-column>
            <el-table-column label="评论" prop="total_comment">
            </el-table-column>
            <el-table-column label="分享" prop="total_share"> </el-table-column>
            <el-table-column label="访问" prop="total_profile_uv">
            </el-table-column>
            <el-table-column label="作品" prop="total_issue"> </el-table-column>
            <el-table-column label="粉丝" prop="total_fans"> </el-table-column>

            <el-table-column label="授权状态" v-slot="{ row }">
              <el-tag v-if="row.auth_type == 2" type="danger">
                授权失败
              </el-tag>
              <el-tag v-if="row.auth_type == 0" type="warning"> 未授权 </el-tag>
              <el-tag v-if="row.auth_type == 1" type="success"> 已授权 </el-tag>
              <!-- {{
                row.auth_type == 0
                  ? "未授权"
                  : row.auth_type == 1
                  ? "授权"
                  : "授权失败"
              }} -->
            </el-table-column>

            <el-table-column label="操作" v-slot="{ row }">
              <el-link type="primary" style="margin-right: 10px" @click="onClickData(row)">数据</el-link>
              <!-- <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteMemberQrcode(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm> -->
            </el-table-column>
          </el-table>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
            :total="params.total" :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <!-- <el-dialog :visible.sync="show_select_dia" width="660px" :title="title">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog> -->
    <el-dialog :visible.sync="show_detail" width="70vw" :title="title">
      <douyinInfo v-if="show_detail" :id="currentId"></douyinInfo>
    </el-dialog>
    <!-- <el-dialog :visible.sync="show_add_project" width="500px" title="添加项目">
      <el-form label-width="100px">
        <el-form-item label="项目名称">
          <el-input style="width: 220px" v-model="pro_form.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-loading="submiting" @click="onCreate"
            >确认添加</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog> -->
  </div>
</template>

<script>
import myLabel from "@/components/navMain/crm/components/my_label";
import douyinInfo from "./components/douyinInfo"
// import memberListSingle from "../site/components/memberList_single.vue";
export default {
  name: "crm_customer_member_qrcode",
  components: {
    myLabel,
    douyinInfo
    // memberListSingle,
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        time_type: 1,
      },
      time_list: [
        { id: 1, name: "全部", value: "1" },
        { id: 2, name: "昨天", value: "2" },
        { id: 3, name: "近7天", value: "3" },
        { id: 4, name: "近一月", value: "4" },
      ],
      p_time: "",
      value: "",
      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
      memberList: [],
      selectedIds: [],
      show_select_dia: false,
      value1: "",
      "statistics": [],
      warning: 'https://img.tfcs.cn/backup/static/admin/douyin/douyin_warning.png',
      projectList: [],
      options: [
        {
          value: 1,
          label: '项目1'
        },
        {
          value: 2,
          label: '项目2'
        },
      ],
      currentId: "",
      title: "",
      show_detail: false,
      project_loading: false,
      pro_form: {
        name: '',
        id: ""
      },
      submiting: false

    };
  },
  created() {
    this.getData()
    this.getProjectData()

    // this.getDepartment();
  },
  filters: {
    filterName(val) {
      let name = ''
      switch (val) {
        case 'hotVideo':
          name = '热门视频'
          break;
        case 'live':
          name = '直播榜单'
          break;
        case 'sentence':
          name = '热门词'
          break;
        case 'trending_sentences':
          name = '热度榜'
          break;
        case 'topic':
          name = '话题榜'
          break;

        default:
          break;
      }
      return name

    },
    formatNum(val) {
      if (val < 100000) return val
      return (val / 10000).toFixed(2) + "万"
    }

  },
  mounted() {
    // this.getDataList();
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    previewImg(src) {
      console.log(123);
      this.imgSrc = src;
      this.show_preview = true;
    },
    setClass(val) {
      if (val && val.indexOf("-") > -1) return true
      return false
    },
    getData() {
      this.getDatas()
      this.getList()
    },
    // 获取统计信息
    getDatas() {
      if (this.p_time && this.p_time.length) {
        this.params.custom_stime = this.p_time[0]
        this.params.custom_etime = this.p_time[1]
      } else {
        this.params.custom_stime = ''
        this.params.custom_etime = ''
      }
      this.$http.getDouyinStatics(this.params).then(res => {
        if (res.status == 200) {
          let arr = []
          for (const key in res.data) {
            switch (key) {
              case 'new_fans':
              case 'total_fans':
                arr[0] = {}
                arr[0]['total'] = res.data['total_fans'] || 0
                arr[0]['title'] = res.data['new_fans'] || 0
                // arr[0][key] = res.data[key]
                arr[0].name = '粉丝总数'
                arr[0].unit = '人'
                arr[0]['tips'] = res.data['fans_tips'] || ''
                break;
              case 'new_issue':
              case 'total_issue':
                arr[1] = {}
                arr[1]['total'] = res.data['total_issue'] || 0
                arr[1]['title'] = res.data['new_issue'] || 0
                arr[1].name = '作品总量'
                arr[1].unit = '个'
                arr[1]['tips'] = res.data['issue_tips'] || ''
                break;
              case 'new_profile_uv':
              case 'total_profile_uv':
                arr[2] = {}
                arr[2]['total'] = res.data['total_profile_uv'] || 0
                arr[2]['title'] = res.data['profile_uv'] || 0
                arr[2].name = '访问总量'
                arr[2].unit = '次'
                arr[2]['tips'] = res.data['profile_uv_tips'] || ''
                break;
              case 'new_share':
              case 'total_share':
                arr[3] = {}
                arr[3]['total'] = res.data['total_share'] || 0
                arr[3]['title'] = res.data['new_share'] || 0
                arr[3].name = '分享总量'
                arr[3].unit = '次'
                arr[3]['tips'] = res.data['share_tips'] || ''
                break;
              case 'new_comment':
              case 'total_comment':
                arr[4] = {}
                arr[4]['total'] = res.data['total_comment'] || 0
                arr[4]['title'] = res.data['new_comment'] || 0
                arr[4].name = '评论总量'
                arr[4].unit = '次'
                arr[4]['tips'] = res.data['comment_tips'] || ''
                break;
              case 'new_like':
              case 'total_like':
                arr[5] = {}
                arr[5]['total'] = res.data['total_like'] || 0
                arr[5]['title'] = res.data['new_like'] || 0
                arr[5].name = '点赞总量'
                arr[5].unit = '次'
                arr[5]['tips'] = res.data['like_tips'] || ''
                break;
              case 'new_play':
              case 'total_play':
                arr[6] = {}
                arr[6]['total'] = res.data['total_play'] || 0
                arr[6]['title'] = res.data['new_play'] || 0
                arr[6].name = '播放总量'
                arr[6].unit = '次'
                arr[6]['tips'] = res.data['play_tips'] || ''
                break;

              default:
                break;
            }
          }
          this.statistics = arr
        }
      })
    },
    // onCreate() {
    //   this.submiting = true
    //   this.$http.saveDouyinProjectList(this.pro_form).then(res => {
    //     console.log(res);
    //     if (res.status == 200) {
    //       this.$message.success(res.message || "添加成功")
    //     }
    //     this.submiting = false
    //   }).catch(() => {
    //     this.submiting = false
    //   })
    // },
    getList() {
      if (this.p_time && this.p_time.length) {
        this.params.custom_stime = this.p_time[0]
        this.params.custom_etime = this.p_time[1]
      } else {
        this.params.custom_stime = ''
        this.params.custom_etime = ''
      }
      this.is_table_loading = true
      this.$http.getDouyinKanbanList(this.params).then(res => {
        if (res.status == 200) {
          this.tableData = res.data.data
          this.params.total = res.data.total;
        }
        this.is_table_loading = false
      }).catch(() => {
        this.is_table_loading = false
      })
    },
    getProjectData() {
      this.$http.getDouyinProjectList().then(res => {
        console.log(res);
        if (res.status == 200) {
          this.projectList = res.data
        }
      })
    },
    onProject() {
      // if (e == -1) {
      //   this.proform = {
      //     name: ""
      //   }
      //   this.show_add_project = true
      //   return
      // }
      this.params.page = 1
      this.getDatas()
    },
    selecetedMember(e) {
      if (this.currentType == 1) {
        //使用员工
        if (e.checkedNodes && e.checkedNodes.length) {
          this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.user_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.user_name = "";
          this.params.user_id = "";
        }
      } else {
        if (e.checkedNodes && e.checkedNodes.length) {
          this.creat_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.create_user =
            e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.creat_name = "";
          this.params.create_user = "";
        }
      }

      this.show_select_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    showMemberList(type = 0) {
      if (type == 1) {
        this.title = "选择使用成员";
      } else {
        this.title = "选择创建人";
      }
      this.currentType = type;
      this.show_select_dia = true;
    },
    delName(type = 0) {
      if (type == 1) {
        //使用员工
        this.params.user_id = "";
        this.user_name = "";
      } else {
        this.params.create_user = "";
        this.creat_name = "";
      }
      // this.params.wx_work_user_id = ''
      // this.username = ''
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getCrmMemberQrcodeList({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },

    onClickTime(item) {
      this.params.time_type = item.value;
      this.p_time = []
      this.params.page = 1;
      this.getDatas()
    },
    changeTimeRange(e) {
      this.params.time_type = ''
      if (e && e.length) {
        this.p_time = e;
        this.params.page = 1;
        this.getDatas()
        // this.getDataList();
      } else {
        this.params.p_time = [];
        this.params.page = 1;
        this.getDatas()
        // this.getDataList();
      }
    },
    onClickEmployees(item) {
      console.log(item.id);
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    onClickData(row) {
      this.currentId = row.open_id
      this.title = '详情'
      this.show_detail = true
    },
    copyLink(row) {
      this.$onCopyValue(row.qr_code);
    },
  },
};
</script>

<style scoped lang="scss">
.items-center {
  align-items: center;
}

.mla {
  margin-left: auto;
}

.mr10 {
  margin-right: 10px;
}

.mb20 {
  margin-bottom: 20px;
}

.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .padd0 {
    padding: 0 24px;
    margin: 0 -20px;

    .title {
      padding: 15px 40px;
    }
  }

  .select_box {
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }

  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;

    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}

// ::v-deep .el-link {
//   ~ .el-link {
//     margin-right: 10px;
//   }
// }
.row_img {
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  overflow: hidden;

  .d_name_bg {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    color: #fff;
    background: #409eff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.datas_list {
  margin-top: 32px;

  .datas_item {
    margin-right: 24px;
    background: #fff;
    border-radius: 10px;
    padding: 24px;

    &.point {
      cursor: pointer;
    }

    &:last-child {
      margin-right: 0;
    }

    .datas_item_left {
      width: 35px;
      height: 35px;
      margin-right: 25px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .change {
      color: #fa6060;

      &.jian {
        color: #37e780;
      }
    }

    .datas_item_right {
      .datas_item_right_name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #2e3c4e;

        .img {
          width: 16px;
          height: 16px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .datas_item_right_top {
        color: #464d59;
        font-size: 18px;
        align-items: flex-end;
        font-family: PingFangSC-Medium, sans-serif;
        font-weight: 600;

        .small {
          font-size: 16px;
          font-weight: normal;
        }
      }

      .datas_item_right_bottom {
        color: #768196;
        margin-top: 5px;
        font-size: 13px;
      }
    }
  }
}

.item_title {
  color: #000000;
  font-family: PingFang-Medium, sans-serif;
  font-weight: 600;
  font-size: 22px;
}

.datas .item_title {
  margin-top: 25px;
}
</style>
