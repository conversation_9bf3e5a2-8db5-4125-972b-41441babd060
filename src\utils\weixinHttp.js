// 微信视频号相关API接口
import axios from "axios";
import { Message } from "element-ui";
import router from "../router";
import store from "../store/index";

class WeixinChannelsAPI {
  constructor() {
    this.$http = axios.create({
      baseURL: "/api",
      timeout: 10000,
    });

    // 请求拦截
    this.$http.interceptors.request.use(
      function (config) {
        let auth_way = localStorage.getItem("auth_way");
        config.headers["authway"] = auth_way ? auth_way : 0;
        config.headers["From"] = "computer";
        
        if (localStorage.getItem("company_token")) {
          config.headers["Authorization"] = "Bearer " + localStorage.getItem("company_token");
        } else {
          config.headers["Authorization"] = "Bearer " + localStorage.getItem("TOKEN");
        }

        // 请求方式 post/get
        if (config.method == "post") {
          config.data = {
            ...config.data,
            website_id: localStorage.getItem("website_id"),
          };
        } else if (config.method == "get") {
          config.params = {
            website_id: localStorage.getItem("website_id"),
            ...config.params,
          };
        }
        return config;
      },
      function (error) {
        return Promise.reject(error);
      }
    );

    // 响应拦截
    this.$http.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response?.status === undefined) {
          Message.error("请检查网络连接");
          return;
        }
        
        if (error.response.status === 422) {
          if (error.response.data.message) {
            if (error.response.data.errors) {
              let promise = Promise.resolve();
              for (var key in error.response.data.errors) {
                error.response.data.errors[key].forEach((item) => {
                  promise = promise.then(() => {
                    return new Promise((resolve) => {
                      resolve(item);
                    });
                  });
                  promise.then((err) => {
                    Message.error(err);
                  });
                });
              }
            } else {
              Message.error(error.response.data.message);
            }
          } else {
            Message.error("请求错误");
          }
          return error.response;
        }
        
        if (error.response.status === 401) {
          if (!store.state.hasMessage) {
            store.state.hasMessage = true;
            Message.error(error.response.data.message || 'token失效');
            setTimeout(() => {
              store.state.hasMessage = false;
            }, 800);
          }

          setTimeout(() => {
            if (localStorage.getItem("company_token")) {
              localStorage.removeItem("company_token");
              router.push({ path: "/companyLogin" });
            } else {
              localStorage.removeItem("TOKEN");
              localStorage.removeItem("admin_token_" + localStorage.getItem("website_id"));
              router.push({ path: "/login" });
            }
            localStorage.removeItem("user_name");
            localStorage.removeItem("website_id");
            localStorage.removeItem("website_crm");
            sessionStorage.removeItem("top_select_id");
            sessionStorage.removeItem("top_menu_info");
          }, 1000);
        }
        
        if (error.response.status === 500) {
          Message.error("网络连接错误，请检查网络连接： " + error.response.data.message);
        }
        
        if (error.response.status === 404) {
          Message.error("请求出错404！");
        }

        return Promise.reject(error);
      }
    );
  }

  // 获取微信视频号账号列表
  getWeixinChannelsAccountList(params) {
    return this.$http.get('admin/weixin/channels/account_list', { params });
  }

  // 微信视频号关联用户
  updateWeixinChannelsAccount(data) {
    return this.$http.post('admin/weixin/channels/account/update', data);
  }

  // 获取微信视频号直播场次列表
  getWeixinLivingRoomList(params) {
    return this.$http.get('admin/weixin/living_room/list', { params });
  }

  // 获取直播间列表-微信视频号按直播场次检索条件
  getWeixinLivingRooms(params) {
    return this.$http.get('admin/weixin/living_room/rooms', { params });
  }

  // 获取检索条件-微信视频号按直播场次
  getWeixinLivingRoomSearchCondition() {
    return this.$http.get('admin/weixin/living_room/search_condition');
  }

  // 获取直播间客户列表-微信视频号按直播场次
  getWeixinLivingRoomUsers(params) {
    return this.$http.get('admin/weixin/living_room/users', { params });
  }

  // 修改微信视频号用户标记状态
  updateWeixinUserStatus(data) {
    return this.$http.post('admin/weixin/living_room/user_status_update', data);
  }

  // 修改微信视频号用户手机号
  updateWeixinUserMobile(data) {
    return this.$http.post('admin/weixin/living_room/user_mobile_update', data);
  }

  // 推送微信视频号用户到CRM
  pushWeixinUserToCRM(params) {
    return this.$http.get('admin/weixin/living_room/push_user_to_crm', { params });
  }

  // 获取微信视频号私信检索条件
  getWeixinPrivateLetterSearchCondition() {
    return this.$http.get('admin/weixin/channels/private_letter/search_condition');
  }

  // 获取微信视频号私信用户列表
  getWeixinPrivateLetterUsers(params) {
    return this.$http.get('admin/weixin/channels/private_letter/users', { params });
  }

  // 获取指定客户私信内容
  getWeixinPrivateLetterUserChat(params) {
    return this.$http.get('admin/weixin/channels/private_letter/user/chat', { params });
  }

  // 修改微信视频号私信客户标记状态
  updateWeixinPrivateLetterUserStatus(data) {
    return this.$http.post('admin/weixin/channels/private_letter/user/status/update', data);
  }

  // 修改微信视频号私信客户手机号码
  updateWeixinPrivateLetterUserMobile(data) {
    return this.$http.post('admin/weixin/channels/private_letter/user/mobile/update', data);
  }

  // 推送微信视频号私信客户信息到CRM
  pushWeixinPrivateLetterUserToCRM(data) {
    return this.$http.post('admin/weixin/channels/private_letter/user/push/crm', data);
  }

  // ==================== 小店客服相关接口 ====================
  
  // 获取微信小店客服账号列表
  getWeixinShopKfAccountList(params) {
    return this.$http.get('admin/weixin/shop_kf/account_list', { params });
  }

  // 微信小店客服账号关联用户
  updateWeixinShopKfAccount(data) {
    return this.$http.post('admin/weixin/shop_kf/account/update', data);
  }

  // 获取检索条件-微信小店客服私信客户列表
  getWeixinShopKfPrivateLetterSearchCondition() {
    return this.$http.get('admin/weixin/shop_kf/private_letter/search_condition');
  }

  // 获取客户列表-微信小店客服私信
  getWeixinShopKfPrivateLetterUsers(params) {
    return this.$http.get('admin/weixin/shop_kf/private_letter/users', { params });
  }

  // 获取指定客户私信内容-微信小店客服私信
  getWeixinShopKfPrivateLetterUserChat(params) {
    return this.$http.get('admin/weixin/shop_kf/private_letter/user/chat', { params });
  }

  // 修改客户标记状态-微信小店客服私信
  updateWeixinShopKfPrivateLetterUserStatus(data) {
    return this.$http.post('admin/weixin/shop_kf/private_letter/user/status/update', data);
  }

  // 修改客户手机号码-微信小店客服私信
  updateWeixinShopKfPrivateLetterUserMobile(data) {
    return this.$http.post('admin/weixin/shop_kf/private_letter/user/mobile/update', data);
  }

  // 客户信息推送到CRM-微信小店客服私信
  pushWeixinShopKfPrivateLetterUserToCRM(data) {
    return this.$http.post('admin/weixin/shop_kf/private_letter/user/push/crm', data);
  }
}

export default new WeixinChannelsAPI(); 