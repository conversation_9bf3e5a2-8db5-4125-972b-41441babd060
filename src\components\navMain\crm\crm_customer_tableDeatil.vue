<template>
    <div class="pages">
        <div class="content-box-crm" style="margin-bottom: 24px">
            <el-steps :active="stepsActive" align-center
                style="margin:20px 0 20px 0;padding-bottom: 20px; border-bottom: 1px solid #f4f4f4;">
                <el-step title="编辑基础信息"></el-step>
                <el-step title="保存基础信息"></el-step>
                <el-step title="编辑佣金信息"></el-step>
            </el-steps>
            <!-- 折叠面板 -->
            <div class="div row loadmore" @click="onChangeCollapsetwo">
                <span class="text"> 基本信息 </span>
                <span :class="is_collapsetwo ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
            </div>
            <myCollapse :isActive="is_collapsetwo" style="border-bottom: 1px solid #f4f4f4;">
                <template v-slot:content>
                    <el-form ref="form" :rules="reportFromRules" :model="form" label-width="100px">
                        <div style="display: flex;flex-wrap: wrap;">
                            <el-form-item label="客户名称" prop="name">
                                <el-input v-model="form.name"></el-input>
                            </el-form-item>
                            <el-form-item label="客户手机号" prop="phone">
                                <el-input v-model="form.phone"></el-input>
                            </el-form-item>
                            <el-form-item label="成交人">
                                <el-button style="width: 185px;" @click="changeAdmin">{{ operationType == 'add' ?
                                    adminBtnText :
                                    reportDetailData.admin.user_name }}</el-button>
                            </el-form-item>
                            <el-form-item label="成交时间" prop="cjrq">
                                <el-date-picker v-model="form.cjrq" type="datetime" placeholder="选择日期时间"
                                    value-format="yyyy-MM-dd HH:mm:ss">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item label="项目名称" prop="project">
                                <el-input v-model="form.project"></el-input>
                            </el-form-item>
                            <el-form-item label="佣金占比" prop="proportion">
                                <el-input v-model="form.proportion">
                                    <i slot="suffix">%</i></el-input>
                            </el-form-item>
                            <el-form-item label="成交金额" prop="amount">
                                <el-input v-model="form.amount">
                                    <i slot="suffix">元</i></el-input>
                            </el-form-item>
                            <el-form-item label="面积" prop="mianji">
                                <el-input v-model="form.mianji">
                                    <i slot="suffix">m²</i></el-input>
                            </el-form-item>
                            <el-form-item label="单价" prop="danjia">
                                <el-input v-model="form.danjia"><i slot="suffix">元</i></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                </template>
            </myCollapse>
            <div class="bottom-tabel-box" v-if="operationType != 'add'">
                <div class="bottom-tabel-title">
                    <span style="font-weight: 600;margin-right: 50px;">统计属性</span>
                    <span>成交人：<span>{{ reportDetailData.admin.user_name }}</span></span>
                    <span style="margin:0px 20px;">成交时间：<span>{{ reportDetailData.deal.cjrq }}</span></span>
                    <span>实收佣金 <span style="color: red;">{{ reportDetailData.reality_commission }}</span>；</span>
                    <span>让利 <span style="color: red;">--</span>；</span>
                    <span>返现 <span style="color: red;">--</span>；</span>
                    <span>毛利 <span style="color: red;">--</span>；</span>
                    <span>毛利润 <span style="color: red;">--</span>；</span>
                </div>
                <div class="bottom-tabel-tabs">
                    <div>
                        <div class="tab">
                            <div class="tab-item" :class="isTabActive == 0 ? 'is-active' : ''" @click="clickTab(0)">应收佣金
                                <span style="color: red;">{{ receiveCommissionList.length }}个</span>
                            </div>
                            <div class="tab-item" :class="isTabActive == 1 ? 'is-active' : ''" @click="clickTab(1)">扣除款项
                                <span style="color: red;">{{ deductList.length }}笔</span>
                            </div>
                            <div class="tab-item" :class="isTabActive == 2 ? 'is-active' : ''" @click="clickTab(2)">业绩提成
                                <span style="color: red;">{{ memberCommissionList.length }}笔</span>
                            </div>
                            <!-- <div class="tab-item" :class="isTabActive == 3 ? 'is-active' : ''" @click="clickTab(3)">开票/到账
                                <span style="color: red;">1笔</span>
                            </div> -->
                            <div class="tab-item" :class="isTabActive == 4 ? 'is-active' : ''" @click="clickTab(4)">附件凭证
                                <span style="color: red;">{{ reportFilesList.length }}个</span>
                            </div>
                        </div>
                    </div>
                    <!-- <span>业绩/提成<span style="color: red;">2笔</span></span>
                    <span style="margin: 0px 50px;">支出/返现<span style="color: red;">2笔</span></span>
                    <span>让利<span style="color: red;">2笔</span></span>
                    <span style="margin:0px 50px">开票/到账<span style="color: red;">2笔</span></span>
                    <span>文件管理<span style="color: red;">2个</span></span> -->
                </div>
                <div class='tab-body'>
                    <!-- 这里可以加入一些动画之类的切换特效 -->
                    <!--应收佣金-->
                    <div v-show='isTabActive == 0' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-table v-loading="is_table_loading" :data="receiveCommissionList" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="操作时间" v-slot="{ row }">
                                {{ row.created_at }}
                            </el-table-column>
                            <el-table-column label="佣金类型" v-slot="{ row }">
                                应收佣金
                            </el-table-column>
                            <el-table-column label="计算公式" v-slot="{ row }">
                                成交价格 X 佣金占比
                            </el-table-column>
                            <el-table-column label="佣金金额" v-slot="{ row }">
                                {{ row.commission }}
                            </el-table-column>
                            <el-table-column label="备注" v-slot="{ row }">
                                {{ row.descp }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link style="margin-right: 20px;" type="primary" @click="onEditData(row)">编
                                    辑</el-link>
                                <el-link type="danger" @click="deleteData(row)">删 除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--支出内容-->
                    <div v-show='isTabActive === 1' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-table v-loading="is_table_loading" :data="deductList" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="操作时间" v-slot="{ row }">
                                {{ row.created_at }}
                            </el-table-column>
                            <el-table-column label="扣除项类型" v-slot="{ row }">
                                {{ row.cate_name }}
                            </el-table-column>
                            <el-table-column label="扣除项占比" v-slot="{ row }">
                                {{ row.proportion }}
                            </el-table-column>
                            <el-table-column label="计算公式" v-slot="{ row }">
                                应收佣金总和 X 扣除项占比
                            </el-table-column>
                            <el-table-column label="扣除项金额" v-slot="{ row }">
                                {{ row.commission }}
                            </el-table-column>
                            <el-table-column label="备注" v-slot="{ row }">
                                {{ row.descp }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link style="margin-right: 20px;" type="primary" @click="onEditData(row)">编
                                    辑</el-link>
                                <el-link type="danger" @click="deleteData(row)">删 除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!--业绩提成-->
                    <div v-show='isTabActive == 2' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <!-- <el-button type="primary" style="margin: 20px 20px" @click="commsplit">一键分佣</el-button> -->
                        <el-table v-loading="is_table_loading" :data="memberCommissionList" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="操作时间" v-slot="{ row }">
                                {{ row.created_at }}
                            </el-table-column>
                            <el-table-column label="员工姓名" v-slot="{ row }">
                                {{ row.user_name }}
                            </el-table-column>
                            <el-table-column label="计算公式" v-slot="{ row }">
                                可分配额度 X 佣金占比
                            </el-table-column>
                            <el-table-column label="分成比例" v-slot="{ row }">
                                {{ row.proportion }}
                            </el-table-column>
                            <el-table-column label="所得佣金" v-slot="{ row }">
                                {{ row.commission }}
                            </el-table-column>
                            <el-table-column label="备注" v-slot="{ row }">
                                {{ row.descp }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link style="margin-right: 20px;" type="primary" @click="onEditData(row)">编
                                    辑</el-link>
                                <el-link type="danger" @click="deleteData(row)">删 除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!--开票内容-->
                    <!-- <div v-show='isTabActive == 3' style="padding:0px  20px 20px 20px;">
                        <el-button type="primary" style="margin: 20px 0px" @click="commadd">添加</el-button>
                        <el-table v-loading="is_table_loading" :data="commData" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="开票时间" v-slot="{ row }">
                                {{ row.client.name ? row.client.name : "--" }}
                            </el-table-column>
                            <el-table-column label="开票抬头" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="开票金额" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="开票类型" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="回款类型" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="是否到账" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="到账日期" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="提成发放时间" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="到账状态" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="发票内容" v-slot="{ row }">
                                {{ row.client.tel ? row.client.tel : "--" }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link type="primary" @click="onEditData(row)">编辑</el-link>
                                <el-link type="primary" @click="deleteData(row)">删除</el-link>
                            </el-table-column>
                        </el-table>
                    </div> -->
                    <!--文件管理的内容-->
                    <div v-show='isTabActive == 4' style="padding:0px  20px 20px 20px;">
                        <el-form ref="fileform" style="position: relative; margin: 20px 0;" :model="fileform"
                            label-width="80px" :inline="true">
                            <el-upload multiple drag action="/api/common/file/upload/admin?category=13" ref="upload"
                                accept=".jpeg,.jpg,.png,.pdf,.docx,.txt,.text,.xls,.xlsx,.doc,.ppt,.pptx"
                                :headers="upload_headers" :limit="1" :files="attenchmentList" list-type="picture"
                                :on-success="(response, file, fileList) => {
                                    onUploadAttechmentSuccess({
                                        response,
                                        file,
                                        fileList,
                                    });
                                }" :on-remove="onRemoveAttechment">
                                <i class="el-icon-upload"></i>
                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            </el-upload>
                            <el-form-item label="凭证描述" style="position:absolute;top: 30px;left: 400px;">
                                <el-input v-model="add_report_files.file_desc" placeholder="请输入凭证描述" />
                                <el-button type="primary" style="margin-top: 30px;" @click="confirmUpload">确认上传</el-button>
                            </el-form-item>
                        </el-form>
                        <el-table v-loading="is_table_loading" :data="reportFilesList" class="house_table" border
                            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
                            @selection-change="handleSelectionChange">
                            <el-table-column label="操作时间" v-slot="{ row }">
                                {{ row.created_at }}
                            </el-table-column>
                            <el-table-column label="缩略图" v-slot="{ row }">
                                <el-image style="width: 80px; height: 80px;border-radius: 8px;" :src="row.file"
                                    :preview-src-list="srcList">
                                </el-image>
                            </el-table-column>
                            <el-table-column label="凭证描述" v-slot="{ row }">
                                {{ row.file_desc }}
                            </el-table-column>
                            <el-table-column label="操作" fixed="right" v-slot="{ row }">
                                <el-link style="margin-right: 20px;" type="primary" @click="onEditData(row)">编
                                    辑</el-link>
                                <el-link type="danger" @click="deleteData(row)">删 除</el-link>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
            <div style="margin-left: 88.7%;">
                <el-button v-if="operationType == 'add'" type="primary" @click="submitReport('form')">下一步</el-button>
                <el-button v-if="operationType == 'edit'" type="primary" @click="submitReport('form')">保存并返回</el-button>
            </div>
        </div>
        <el-dialog v-if="operationType !== 'add'" :title="dialogTitle[isTabActive].label" :show-close="false"
            :visible.sync="dialogVisible" width="786px" :before-close="handleClose">
            <el-form :model="newcommform" class="demo-form-inline" style="padding:10px 40px;" v-if="isTabActive < 4">
                <el-form-item label="佣金类型:">
                    <el-select v-model="isTabActive" style="width: 400px;">
                        <el-option v-for=" item  in  commissionType " :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-alert v-if="isTabActive === 0" title="默认公式：应收佣金 = 成交金额 x 佣金占比。（如不满足业务需求请选择自定义输入）" type="warning" />
                <el-alert v-show="isTabActive === 1" title="默认公式：扣除款项 = 应收佣金总和 x 佣金占比。（如不满足业务需求请选择自定义输入）" type="warning" />
                <el-alert v-show="isTabActive === 2" title="默认公式：个人业绩 = 扣除款项总和 - 扣除款项总和 x 佣金占比。（如不满足业务需求请选择自定义输入）"
                    type="warning" />
                <el-form-item label="计算方式:">
                    <el-radio-group v-model="calculationMethod" @input="changeGroup">
                        <el-radio :label="0">默认公式</el-radio>
                        <el-radio :label="1" v-if="isTabActive != 2">自定义输入</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="isTabActive === 1" label="款项类型:">
                    <el-radio-group v-model="deductType">
                        <el-radio :disabled="radioDisabled1" :label="1">公司佣金</el-radio>
                        <el-radio :disabled="radioDisabled" :label="2">返现</el-radio>
                        <el-radio :disabled="radioDisabled" :label="3">补助</el-radio>
                        <el-radio :disabled="radioDisabled" :label="4">其它</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="isTabActive === 2" label="选择成员:">
                    <el-button @click="changeAdmin">{{ adminBtnText }}</el-button>
                </el-form-item>
                <el-form-item label="佣金计算:">
                    <el-select v-show="calculationMethod || (isTabActive === 1 && !calculationMethod)" v-model="isTabActive"
                        style="width: 120px;">
                        <el-option v-for=" item  in  options " :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>

                    <template v-if="calculationMethod == 0">
                    <el-input v-if="isTabActive === 0" v-model="reportDetailData.deal.amount"
                        :readonly="calculationMethod === 0 ? true : false" placeholder="成交金额"
                        style="width: 120px;"></el-input>
                    <el-input v-if="isTabActive === 1" v-model="reportDetailData.receive_commission.commission"
                        placeholder="应收佣金" style="width: 120px;"></el-input>
                    <el-input v-if="isTabActive === 2" v-model="distributable"
                        :readonly="calculationMethod === 0 ? true : false" placeholder="可分配额度"
                        style="width: 120px;"></el-input>
                    <span> X </span>
                    <el-input v-if="isTabActive === 0" v-model="reportDetailData.deal.proportion"
                        :readonly="calculationMethod === 0 ? true : false" placeholder="佣金占比" style="width: 120px;">
                        <i slot="suffix">%</i></el-input>
                    <el-input v-if="isTabActive === 1" v-model="deductProportion" placeholder="扣除占比" style="width: 120px;">
                        <i slot="suffix">%</i></el-input>
                    <el-input v-if="isTabActive === 2" v-model="deductProportion" placeholder="员工占比" style="width: 120px;">
                        <i slot="suffix">%</i></el-input>
                    <span> = </span>
                    <el-input v-if="isTabActive === 0" v-model="commissionReceivable" readonly placeholder="扣除款项金额"
                        style="width: 120px;">
                        <i slot="suffix">元</i></el-input>
                    <el-input v-if="isTabActive === 1" v-model="payrollDeduction" readonly placeholder="扣除款项金额"
                        style="width: 120px;">
                        <i slot="suffix">元</i></el-input>
                    <el-input v-if="isTabActive === 2" v-model="staffCommission" readonly placeholder="扣除款项金额"
                        style="width: 120px;">
                        <i slot="suffix">元</i></el-input>
                    <!-- <el-input v-if="isTabActive === 1 && calculationMethod" v-model="payrollDeductionFixed"
                        placeholder="扣除款项固定金额" style="width: 200px;">
                        <i slot="suffix">元</i></el-input> -->
                    </template>
                    
                    
                    <!--自定义输入-->
                    <template v-else-if="calculationMethod == 1">
                        <el-input v-if="isTabActive === 0" v-model="custom_commission"  placeholder="扣除款项金额"
                            style="width: 120px;">
                            <i slot="suffix">元</i></el-input>
                        <el-input v-if="isTabActive === 1" v-model="custom_commission"  placeholder="扣除款项金额"
                            style="width: 120px;">
                            <i slot="suffix">元</i></el-input>
                    </template>
                </el-form-item>
                <el-form-item label="描述:">
                    <el-input type="textarea" v-model="receiveCommission.descp" placeholder="请输入描述"
                        style="width: 550px;margin-left: 28px;"></el-input>
                </el-form-item>
                <!-- <el-form-item label="客户负责人:">
                    <el-switch v-model="newcommform.delivery"></el-switch> 如果已经收到款项，可以同步创建到账记录
                </el-form-item>
                <el-form-item label="负责人主管:">
                    <el-switch v-model="newcommform.delivery"></el-switch> 如果已经开票，可以同步创建开票记录
                </el-form-item> -->
            </el-form>
            <el-form v-else>
                <el-form-item label="">
                    <el-upload multiple action="/api/common/file/upload/admin?category=13" ref="upload"
                        accept=".jpeg,.jpg,.png,.pdf,.docx,.txt,.text,.xls,.xlsx,.doc,.ppt,.pptx" :headers="upload_headers"
                        :limit="1" :files="attenchmentList" list-type="picture" :on-success="(response, file, fileList) => {
                            onUploadAttechmentSuccess({
                                response,
                                file,
                                fileList,
                            });
                        }" :on-remove="onRemoveAttechment">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="operateCommissionInfo">{{ isAdd ? '添加' : '保存' }}</el-button>
            </span>
        </el-dialog>

        <el-dialog :visible.sync="show_select_dia" width="660px" title="选择成员">
            <memberListSingle v-if="show_select_dia" :defaultExpandAll="false" :list="memberListwo"
                @onClickItem="selecetedMember"></memberListSingle>
        </el-dialog>
    </div>
</template>
  
<script>
import config from "@/utils/config";
import myCollapse from "./components/collapse";
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
export default {
    name: "crm_customer_seas_list",
    components: {
        myCollapse,
        memberListSingle
    },
    data() {
        var checkPhone = (rule, value, callback) => {
            if (!value) {
                return callback(new Error("手机号不能为空"));
            } else {
                const reg = /^1[3-9][0-9]\d{8}$/;
                if (reg.test(value)) {
                    callback();
                } else {
                    return callback(new Error("请输入正确的手机号"));
                }
            }
        };
        return {
            unitPrice: 0,//单价
            stepsActive: 1,
            userDefined: {
                amount: null,//自定义应得佣金
                distributable: null//自定义员工佣金
            },
            payrollDeductionFixed: null,//扣除款项固定金额
            radioDisabled1: false,//公司佣金类型（扣除款项禁用状态）
            radioDisabled: true,//扣除款项禁用状态
            calculationMethod: 0,
            editFiles: {},
            srcList: [],//附件大图预览
            add_report_files: {
                report_id: null,
                file: '',
                file_desc: '',
            },
            upload_headers: {
                Authorization: config.TOKEN,
            },
            attenchmentList: [],
            receivablesTotal: null,//应收佣金总和
            deductTotal: null,//扣除款项总和
            distributable: null,//可分配额度
            staffCommissionTotal: null,//员工佣金总额
            rstaffCommissionTotalRatio: null,//员工佣金占比总和
            deductType: 1,//扣除款项类型
            deductProportion: null,//扣除款项占比
            dialogTitle: [{
                value: 0,
                label: '应得佣金'
            }, {
                value: 1,
                label: '扣除款项'
            }, {
                value: 2,
                label: '个人业绩'
            }, {
                value: 3,
                label: '回款记录'
            }, {
                value: 4,
                label: '附件凭证'
            }],
            options: [{
                value: 0,
                label: '成交总价'
            }, {
                value: 1,
                label: '应得总和'
            }, {
                value: 2,
                label: '可分配'
            }],
            commissionType: [{
                value: 0,
                label: '应得佣金'
            }, {
                value: 1,
                label: '扣除款项'
            }, {
                value: 2,
                label: '个人业绩'
            }],
            reportId: null,//报告id
            isAdd: true,//是否添加佣金
            operationType: '',//操作类型（编辑，添加）
            adminBtnText: "选择成交人",
            zuoxi_user: {  //选择人员信息
                id: '',
                name: ''
            },
            public_admin_id: '',
            memberListwo: [],
            show_select_dia: false, //成员筛选树弹框
            memberList: [], //部门列表
            isShowStatistics: false,
            reportDetailData: {},//报告详情数据
            newcommform: {
                delivery: false,
                resource: '公式',
            }, //新增编辑佣金
            dialogVisible: false, //添加佣金弹窗
            fileform: {}, //文件类型的form
            commData: [], //公司佣金列表的表格
            isTabActive: 0, // 控制切换的变量
            form: {
                name: '',//客户名称
                phone: null,//客户手机号
                admin_id: null,//成交人
                amount: null,//成交金额
                cjrq: '',//成交日期
                proportion: null,//占比
                project: '',//项目名称
                mianji: null,//面积
                danjia: null,//单价
                operate_from: 1//操作来源
            },
            recode_params: {
                plugin_id: '',
                name: '',
                phone: '',
                page: 1,
                per_page: 10,
                total: 0,
                times: '',
                admin_id: '',
            },
            is_table_loading: false,
            // 佣金基础表单
            receiveCommission: {
                report_id: '',//报告id
                commission: '',//佣金金额
                descp: ''//描述
            },
            receiveCommissionList: [],//应收佣金数据
            deductList: [],//扣除款项数据
            memberCommissionList: [],//成员佣金列表
            reportFilesList: [],//附件列表
            value: '',
            is_collapse: true,
            is_collapsetwo: true,
            is_collapsethree: true,
            reportFromRules: {
                name: [
                    { required: true, message: '请输入客户名称', trigger: 'blur' },
                    { min: 2, max: 5, message: '长度在 2 到 5 个字符', trigger: 'blur' }
                ],
                phone: [
                    { min: 11, message: "手机号不足11位", trigger: "blur" },
                    { validator: checkPhone, trigger: "blur" }
                ],
                amount: [
                    { required: true, message: '请输入成交金额', trigger: 'blur' },
                ],
                cjrq: [
                    { required: true, message: '请选择成交日期', trigger: 'blur' },
                ],
                proportion: [
                    { required: true, message: '请填写佣金占比', trigger: 'blur' },
                ],
                project: [
                    { required: true, message: '请填写项目名称', trigger: 'blur' },
                ],
                mianji: [
                    { required: true, message: '请填写面积', trigger: 'blur' },
                ],
                danjia: [
                    { required: true, message: '请填写单价', trigger: 'blur' },
                ],
            },
            custom_commission: ''   //自定义佣金金额
        };
    },
    watch: {
        "form": {
            handler(newVal) {
                let price = newVal.amount / newVal.mianji
                this.unitPrice = price.toFixed(2)
            },
            deep: true
        },
        "form.amount"(val){
            this.calcFormDanjia();
        },
        "form.mianji"(val){
            this.calcFormDanjia();
        },
        isTabActive(val){
            if(val == 2){
                this.calculationMethod = 0
            }
        }
    },
    computed: {
        // 计算应得佣金
        commissionReceivable() {
            let money = Number(this.reportDetailData.deal.amount) * (this.reportDetailData.deal.proportion / 100)
            return money.toFixed(2)
        },
        // 计算扣除款项
        payrollDeduction() {
            let money = Number(this.reportDetailData.receive_commission.commission) * (this.deductProportion / 100)
            return money.toFixed(2)
        },
        // 计算员工佣金
        staffCommission() {
            let money = this.distributable * (this.deductProportion / 100)
            return money.toFixed(2)
        },
    },
    created() {
        this.reportId = this.$route.query.id //报告id
        this.add_report_files.report_id = this.reportId
        if (this.$route.query.operationType) {
            this.operationType = this.$route.query.operationType
        }
        if (this.operationType == 'edit') {
            this.getReportDetail(this.reportId)
            this.getReportFilesList(this.reportId)
        }
        this.getDepartmentwo()
    },
    methods: {
        //计算form.danjia
        calcFormDanjia(){
            let price = this.form.amount / this.form.mianji
            price = isNaN(price) || !isFinite(price) ? '' : price.toFixed(2)
            this.form.danjia = price;
        },
        // 获取人员列表
        async getDepartmentwo() {
            let res = await this.$http.getCrmDepartmentList()
            if (res.status == 200) {
                this.memberListwo = res.data;
            }
        },
        // 添加 / 编辑成交报告
        async addOrEditReport(form) {
            //form.danjia = this.unitPrice
            if (this.operationType == 'add') {
                let res = await this.$http.addReportAPI(form)
                if (res.status == 200) {
                    this.stepsActive = 3
                    this.operationType = 'edit'
                    this.reportId = res.data.id
                    await this.getReportDetail(this.reportId)
                }
            } else {
                form['id'] = this.reportId
                this.$http.editReportAPI(form)
                this.$router.push('/crm_customer_table');
            }
        },
        // 获取报告详情
        getReportDetail(id) {
            this.$http.getReportDetailAPI(id).then(async (res) => {
                this.reportDetailData = res.data
                console.log(res.data, '报告详情')
                const { admin: { admin_id }, customer: { name, phone }, deal: { amount, cjrq, proportion, project, mianji, danjia }, receive_commission: { commission: receive_commission }, company_commission: { commission: company_commission } } = this.reportDetailData
                this.form = { admin_id, name, phone, amount, cjrq, proportion, project, mianji, danjia }
                this.receiveCommissionList = await this.getReceiveCommissionList(this.reportId)
                this.deductList = await this.getCompanyCommissionList(this.reportId)
                this.memberCommissionList = await this.getMemberCommissionList(this.reportId)
                const receivablesTotal = Number(receive_commission), deductTotal = Number(company_commission)
                this.distributable = receivablesTotal - deductTotal
            })
        },
        // 获取应收佣金列表
        async getReceiveCommissionList(id) {
            const { data: res } = await this.$http.getReceiveCommissionListAPI(id)
            return res
        },
        // 添加 / 编辑应收佣金
        addOrEditReceiveCommission() {
            if(this.calculationMethod == 0){
                this.receiveCommission.commission = this.commissionReceivable;
            }else{
                this.receiveCommission.commission = this.custom_commission;
            }
            if (this.isAdd) {
                return this.$http.addReceiveCommissionAPI(this.receiveCommission)
            } else {
                return this.$http.editReceiveCommissionAPI(this.receiveCommission)
            }
        },
        // 获取扣除款项列表
        async getCompanyCommissionList(id) {
            const { data: res } = await this.$http.getCompanyCommissionListAPI(id)
            return res
        },
        // 添加 / 编辑扣除款项
        addOrEditCompanyCommission() {
            this.receiveCommission.commission = this.payrollDeduction
            this.receiveCommission.proportion = this.deductProportion
            this.receiveCommission.cate_id = this.deductType
            if(this.calculationMethod == 1){
                this.receiveCommission.commission = this.custom_commission;
                this.receiveCommission.proportion = 0
            }
            if (this.isAdd) {
                return this.$http.addCompanyCommissionAPI(this.receiveCommission)
            } else {
                return this.$http.editCompanyCommissionAPI(this.receiveCommission)
            }
        },
        // 获取员工佣金列表
        async getMemberCommissionList(id) {
            const { data: res } = await this.$http.memberCommissionListAPI(id)
            return res
        },
        // 添加 / 编辑员工佣金
        addMemberCommission() {
            this.receiveCommission.proportion = this.deductProportion
            this.receiveCommission.admin_id = this.zuoxi_user.id
            if (this.isAdd) {
                return this.$http.addMemberCommissionAPI(this.receiveCommission)
            } else {
                return this.$http.editMemberCommissionAPI(this.receiveCommission)
            }
        },
        // 删除佣金 / 扣除款项
        delBrokerageOrDeduction(id) {
            if (this.isTabActive === 0) {
                this.$http.delReceiveCommissionAPI(id)
            } else if (this.isTabActive === 1) {
                this.$http.delCompanyCommissionAPI(id)
            } else if (this.isTabActive === 2) {
                this.$http.delMemberCommissionAPI(id)
            } else if (this.isTabActive === 4) {
                this.$http.delReportFilesAPI(id)
                this.getReportFilesList(this.reportId)
            }
        },
        // 附件列表
        getReportFilesList(id) {
            this.$http.getReportFilesListAPI(id).then((result) => {
                this.reportFilesList = result.data
                this.reportFilesList.forEach(e => {
                    this.srcList.push(e.file)
                });
            })
        },
        // 添加附件
        addReportFiles(form) {
            this.$http.addReportFilesAPI(form).then(() => {
                this.$message({
                    type: 'success',
                    message: '上传凭证成功!'
                })
                this.add_report_files.file_desc = ''
                this.$refs['upload'].clearFiles()
                this.getReportFilesList(this.reportId)
            })
        },
        // 编辑附件
        editReportFiles(form) {
            this.$http.editReportFilesAPI(form).then(() => {

            })
        },
        selecetedMember(e) {
            if (e.checkedNodes && e.checkedNodes.length) {
                this.zuoxi_user = {
                    id: e.checkedNodes[e.checkedNodes.length - 1].id,
                    name: e.checkedNodes[e.checkedNodes.length - 1].name
                }
                this.adminBtnText = this.zuoxi_user.name
                this.form.admin_id = this.zuoxi_user.id
            }
            this.show_select_dia = false;
        },
        //佣金添加
        commadd() {
            this.calculationMethod = 0
            if (this.isTabActive === 2) {
                this.radioDisabled1 = false
                this.radioDisabled = true
            }
            this.receiveCommission.descp = ''
            this.deductProportion = null
            this.deductType = 1
            this.isAdd = true
            this.dialogVisible = true
        },
        // 添加 / 修改佣金信息
        async operateCommissionInfo() {
            let res;
            this.receiveCommission.report_id = this.reportId
            if (this.isTabActive === 0) {
                res = await this.addOrEditReceiveCommission()
            }
            if (this.isTabActive === 1) {
                res = await this.addOrEditCompanyCommission()
            }
            if (this.isTabActive === 2) {
                res = await this.addMemberCommission()
            }
            if(res.status == 200){
                this.getReportDetail(this.reportId)
                this.dialogVisible = false
                this.$message({
                    type: 'success',
                    message: '操作成功!'
                })
            }
        },
        // //一键分佣
        // commsplit() {
        //     console.log("一键分佣")
        //     this.$router.push("crm_customer_tableDeatil_split");
        // },
        //佣金业绩切换
        clickTab(number) {
            this.isTabActive = number;
        },
        handleSelectionChange() {

        },
        // 折叠面板
        onChangeCollapse() {
            this.is_collapse = !this.is_collapse;
        },
        onChangeCollapsetwo() {
            this.is_collapsetwo = !this.is_collapsetwo;
        },
        onChangeCollapsethree() {
            this.is_collapsethree = !this.is_collapsethree;
        },
        // 选择成交人
        changeAdmin() {
            this.show_select_dia = true
        },
        // 保存成交单
        submitReport(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.addOrEditReport(this.form)
                }
            });
        },
        handleClose() {

        },
        // 编辑佣金信息
        onEditData(item) {console.log(item);
            this.isAdd = false
            this.dialogVisible = true
            this.receiveCommission.descp = item.descp
            this.receiveCommission.id = item.id
            this.calculationMethod = 0;
            if (this.isTabActive === 1) {
                this.receiveCommission.cate_id = item.cate_id
            }
            if (this.isTabActive === 1 || this.isTabActive === 2) {
                this.deductProportion = item.proportion
                this.adminBtnText = item.user_name
            }
            if (this.isTabActive === 4) {
                this.editFiles = item
            }else if(this.isTabActive != 2){
                //判断是否自定义佣金
                let isCustomCommission = false;
                if(this.isTabActive == 0 ){
                    if(item.commission != this.commissionReceivable){
                        isCustomCommission = true;
                    }
                }else if(!parseFloat(item.proportion)){
                    isCustomCommission = true;
                }

                //设置自定义佣金选择
                if(isCustomCommission){
                    this.calculationMethod = 1;
                    this.custom_commission = item.commission;
                }
            }
        },
        // 删除佣金信息
        deleteData(item) {
            this.$confirm('此操作将删除该佣金信息, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                await this.delBrokerageOrDeduction(item.id)
                await this.getReportDetail(this.reportId)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            })
        },
        // 上传附件
        onUploadAttechmentSuccess(options = {}) {
            let { response } = options;
            this.add_report_files.file = response.url
            // this.attenchmentList = []
            this.attenchmentList.push(response.url)
        },
        onRemoveAttechment(file, fileList) {
            let numFile = [];
            if (fileList && fileList.length) {
                fileList.map((item) => {
                    numFile.push(item.response.url);
                })
            } else {
                this.attenchmentList = [];
            }
            this.attenchmentList = numFile;
        },

        // 确认上传
        confirmUpload() {
            if (!this.add_report_files.file_desc) return this.$message({
                message: '请填写凭证描述',
                type: 'warning'
            });
            if (!this.add_report_files.file) return this.$message({
                message: '请选择要上传的凭证',
                type: 'warning'
            });
            this.addReportFiles(this.add_report_files)
        },

        //公式&自定义切换
        changeGroup(e) {
            if (e === 0) {
                this.deductType = 1
                this.radioDisabled1 = false
                this.radioDisabled = true
            } else {
                this.deductType = 2
                this.radioDisabled1 = true
                this.radioDisabled = false
            }
        }
    },
};
</script>
  
<style lang="scss" scoped>
/deep/.el-descriptions--medium.is-bordered .el-descriptions-item__cell {
    padding: 10px;
    width: 199px;
    height: 50px;
    text-align: center;
}

.pages {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 0 24px 24px;


    .bottom-tabel-box {
        min-height: 200px;
        width: 100%;
        border: 1px solid #DDE1E9;
        margin: 20px 0px;

        .bottom-tabel-title {
            padding: 0px 40px;
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid #DDE1E9;
        }

        .bottom-tabel-tabs {
            height: 50px;
            background-color: #F2F3F5;
            border-bottom: 1px solid #DDE1E9;
            line-height: 50px;

            .tab {
                width: 800px;
                display: flex;
                justify-content: space-between;

                .tab-item {
                    width: 167px;
                    height: 50px;
                    text-align: center;
                    line-height: 50px;
                    cursor: pointer;
                }

                // .tab-itemdetil {
                //     padding:0px 20px;
                // }
                .is-active {
                    background-color: #fff;
                }
            }
        }
    }

    .addDelete {
        position: absolute;
        top: 8px;
        right: -30px;
        display: flex;
        flex-direction: column;
        cursor: pointer;


        i {
            font-size: 20px;
            flex: 1;
        }

        .el-icon-circle-plus-outline {
            color: #2d8cf0;
        }

        .el-icon-remove-outline {
            color: red;
        }
    }

}


.loadmore {
    border: none;
    width: 100%;
    text-align: end;
    line-height: 1;
    padding: 12px 0 20px 0;
    color: #a1a1a1;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;

    .text {
        font-size: 14px;
    }
}
</style>
  