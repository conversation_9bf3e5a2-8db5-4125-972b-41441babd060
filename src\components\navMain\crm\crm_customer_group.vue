<template>
  <!-- 客户群 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <span class="text">创建时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="p_time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="changeTimeRange"
            >
            </el-date-picker>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">员工状态：</span>
            <myLabel
              :arr="employees_list"
              @onClick="onClickEmployees"
            ></myLabel>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">选择群主：</span>
            <div>
              <el-input
                placeholder="请选择群主"
                v-model="username"
                @focus="showMemberList"
              >
                <i
                  @click="delName"
                  slot="suffix"
                  class="el-input__icon el-icon-circle-close"
                ></i
              ></el-input>
            </div>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row flex-1">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right div row">
              <el-button type="primary" size="mini" @click="importGroup"
                >导入群</el-button
              >
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
            <el-table-column prop="name" label="群名称" v-slot="{ row }">
              <el-link type="primary" @click="onClickDetail(row.id)">{{
                row.name
              }}</el-link>
            </el-table-column>
            <el-table-column label="群主">
              <template slot-scope="scope">
                {{ scope.row.owner || "--" }}
              </template>
            </el-table-column>
            <el-table-column label="管理员">
              <template slot-scope="scope">
                <span
                  style="margin-right: 10px"
                  v-for="(item, index) in scope.row.admin_list"
                  :key="index"
                  >{{ item }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="群公告" prop="notice"> </el-table-column>
            <el-table-column label="群人数" v-slot="{ row }">
              {{ row.group_count || 0 }}
            </el-table-column>
            <el-table-column label="今日入群人数" v-slot="{ row }">
              {{ row.add_group || 0 }}
            </el-table-column>
            <el-table-column label="今日退群人数" v-slot="{ row }">
              {{ row.out_group || 0 }}
            </el-table-column>
            <el-table-column label="群状态">
              <template slot-scope="scope">
                {{ filterStatus(scope.row.status) }}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-link type="primary" @click="onClickDetail(scope.row.id)">
                  详情
                </el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择群主">
      <memberListSingle
        v-if="show_member_list"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
        ref="memberList"
      >
      </memberListSingle>
    </el-dialog>
  </div>
</template>

<script>
import memberListSingle from "../site/components/memberList_single.vue";
import myLabel from "./components/my_label";
import { Loading } from "element-ui";
export default {
  name: "crm_customer_group",
  components: {
    myLabel,
    memberListSingle,
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        times: "",
        status: 0,
        owner: "",
      },
      time_list: [
        { id: 1, name: "全部", value: "" },
        { id: 1, name: "今天", value: "today" },
        { id: 2, name: "昨天", value: "yesterday" },
        { id: 3, name: "本周", value: "this_week" },
        { id: 4, name: "上周", value: "last_week" },
      ],
      employees_list: [
        { id: 0, name: "跟进人正常" },
        { id: 1, name: "跟进人离职" },
        { id: 2, name: "离职继承中" },
        { id: 3, name: "离职继承完成" },
      ],
      value: "",
      p_time: "",
      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
      username: "",
      show_member_list: false,
      memberList: [],
      selectedIds: [],
    };
  },
  created() {
    this.getDepartment();
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    filterStatus(e) {
      return this.employees_list[e].name;
    },
    delName() {
      this.params.owner = "";
      this.username = "";
      this.params.page = 1;
      this.getDataList();
    },
    showMemberList() {
      this.show_member_list = true;
    },

    selecetedMember(e) {
      console.log(e.checkedNodes);
      if (e.checkedNodes && e.checkedNodes.length) {
        this.username = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.params.owner =
          e.checkedNodes[e.checkedNodes.length - 1].wx_agent_userid;
      } else {
        this.username = "";
        this.params.owner = "";
      }
      this.show_member_list = false;
      this.params.page = 1;
      this.getDataList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getCrmCustomerGroupData({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        });
    },
    onClickTime(item) {
      this.params.times = item.value;
      this.params.page = 1;
      this.getDataList();
    },
    changeTimeRange(e) {
      if (e && e.length) {
        this.params.times = e.join(",");
        this.params.page = 1;
        this.getDataList();
      } else {
        this.params.times = "";
        this.params.page = 1;
        this.getDataList();
      }
    },
    onClickEmployees(item) {
      console.log(item.id);
      this.params.status = item.id;
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onClickDetail(id) {
      this.$router.push(`/crm_customer_group_detail?id=${id}`);
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    importGroup() {
      this.$confirm("此操作将导入群聊, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.loading = Loading.service({
          target: ".el-table",
          lock: true,
          text: "正在同步中 请稍后...", //可以自定义文字
          spinner: "el-icon-loading", //自定义加载图标类名
        });
        this.$http
          .importGroupchatList()
          .then((res) => {
            if (res.status == 200) {
              this.loading.close();
              this.loading = null;
              this.params.page = 1;
              this.getDataList();
              this.$message.success(res.message || "同步成功");
            }
          })
          .catch(() => {
            this.loading.close();
            this.loading = null;
          });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
</style>
