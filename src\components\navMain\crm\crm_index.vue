<template>
  <div>
    <div class="web-box" :class="{'web-boxA': isBold}">
      <el-row>
        <el-col>
          <el-row :gutter="20">
            <el-col :span="4">
              <div class="tab-title flex-row snum">
                <div class="top-right">
                  <div class="increase flex-row" v-if="newcrm_index_form.xzkh">
                    <img src="../../../assets/webimg/sz.png" alt="" />
                    <span>+{{ newcrm_index_form.xzkh.day_num }}</span>
                  </div>
                  <div class="num">
                    <!-- {{ crm_index_form.sea_client.total }} -->
                    {{ newtopdata.khzl }}
                  </div>
                  <div class="desc">客户总量</div>
                </div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="tab-title flex-row snum">
                <div class="top-right">
                  <div class="increase flex-row"></div>
                  <div class="num">
                    {{ newtopdata.yxkh}}
                  </div>
                  <div class="desc">有效客户</div>
                </div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="tab-title flex-row snum" @click="clickMyclient">
                <div class="top-right">
                  <div class="increase flex-row"></div>
                  <div class="num">{{ newtopdata.myke }}</div>
                  <div class="desc">我的客户</div>
                </div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="tab-title flex-row snum">
                <div class="top-right">
                  <div class="increase flex-row"></div>
                  <div class="num">{{ newtopdata.tdcy }}</div>
                  <div class="desc">团队人员</div>
                </div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="tab-title flex-row snum" @click="clickMyFall">
                <div class="top-right">
                  <div class="increase flex-row"></div>
                  <div class="num">{{ newtopdata.jjdg }}</div>
                  <div class="desc">即将掉公</div>
                </div>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="tab-title flex-row snum" @click="clickMyNotFollow">
                <div class="top-right">
                  <div class="increase flex-row"></div>
                  <div class="num">{{ newtopdata.wgj}}</div>
                  <div class="desc">7日未跟进</div>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="s_title">客户状态</div>
              <div class="tab-title" style="padding: 41px 28px">
                <div class="content-box flex-row" style="align-items: center">
                  <!-- <div
                  class="chart-box"
                  id="chart-box2"
                  style="width: 70%; height: 285px"
                ></div> -->
                  <div class="left-img div">
                    <div class="num-box">
                      <div class="l-t" v-if="newcrm_index_form.xzkh">
                        {{ newcrm_index_form.xzkh.num || 0 }}
                      </div>
                      <p class="p1">客户</p>
                    </div>
                    <div class="num-box">
                      <div class="l-c" v-if="newcrm_index_form.yxkh">
                        {{ newcrm_index_form.yxkh.num || 0 }}
                      </div>
                      <p class="p2">有效</p>
                    </div>
                    <div class="num-box">
                      <div class="l-b" v-if="newcrm_index_form.wscj">
                        {{ newcrm_index_form.wscj.num || 0 }}
                      </div>
                      <p class="p3">成交</p>
                    </div>
                  </div>
                  <div class="right-content">
                    <table class="t">
                      <thead>
                        <tr>
                          <th></th>
                          <th>新增客户</th>
                          <th>有效客户</th>
                          <th>无效客户</th>
                          <th>暂缓客户</th>
                          <th>带看客户</th>
                          <th>我司成交</th>
                          <!-- <th>他司成交</th> -->
                        </tr>
                      </thead>
                      <tbody v-if="newcrm_index_form">
                        <tr>
                          <th>今日新增</th>
                          <td v-if="newcrm_index_form.xzkh ">{{ newcrm_index_form.xzkh.day_num }}</td>
                          <td v-if="newcrm_index_form.yxkh ">{{ newcrm_index_form.yxkh.day_num }}</td>
                          <td v-if="newcrm_index_form.wxkh ">{{ newcrm_index_form.wxkh.day_num}}</td>
                          <td v-if="newcrm_index_form.zhkh ">{{ newcrm_index_form.zhkh.day_num}}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.dkkh.day_num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.wscj.day_num }}</td>
                          <!-- <td v-if="newcrm_index_form.tscj ">{{ newcrm_index_form.tscj.day_num}}</td> -->
                        </tr>
                        <tr>
                          <th>7日新增</th>
                          <td v-if="newcrm_index_form.xzkh ">{{ newcrm_index_form.xzkh.seven_num }}</td>
                          <td v-if="newcrm_index_form.yxkh ">{{ newcrm_index_form.yxkh.seven_num }}</td>
                          <td v-if="newcrm_index_form.wxkh ">{{ newcrm_index_form.wxkh.seven_num }}</td>
                          <td v-if="newcrm_index_form.zhkh ">{{ newcrm_index_form.zhkh.seven_num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.dkkh.seven_num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.wscj.seven_num }}</td>
                          <!-- <td v-if="newcrm_index_form.tscj ">{{ newcrm_index_form.tscj.seven_num }}</td> -->
                        </tr>
                        <tr>
                          <th>30日新增</th>
                          <td v-if="newcrm_index_form.xzkh ">{{ newcrm_index_form.xzkh.thirty_num }}</td>
                          <td v-if="newcrm_index_form.yxkh ">{{ newcrm_index_form.yxkh.thirty_num }}</td>
                          <td v-if="newcrm_index_form.wxkh ">{{ newcrm_index_form.wxkh.thirty_num }}</td>
                          <td v-if="newcrm_index_form.zhkh ">{{ newcrm_index_form.zhkh.thirty_num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.dkkh.thirty_num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.wscj.thirty_num }}</td>
                          <!-- <td v-if="newcrm_index_form.tscj ">{{ newcrm_index_form.tscj.thirty_num }}</td> -->
                        </tr>
                        <tr>
                          <th>客户总量</th>
                          <td v-if="newcrm_index_form.xzkh ">{{ newcrm_index_form.xzkh.num }}</td>
                          <td v-if="newcrm_index_form.yxkh ">{{ newcrm_index_form.yxkh.num }}</td>
                          <td v-if="newcrm_index_form.wxkh ">{{ newcrm_index_form.wxkh.num }}</td>
                          <td v-if="newcrm_index_form.zhkh ">{{ newcrm_index_form.zhkh.num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.dkkh.num }}</td>
                          <td v-if="newcrm_index_form.wscj ">{{ newcrm_index_form.wscj.num }}</td>
                          <!-- <td v-if="newcrm_index_form.tscj ">{{ newcrm_index_form.tscj.num }}</td> -->
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="huan_box flex-row">
                <div class="huan">
                  <div class="ranking">
                    <div class="s_title">客户来源排行</div>
                    <div class="tab-title">
                      <div class="honor_list">
                        <!-- <div class="h_sort flex-row">
                        <span
                          class="c2"
                          :class="{ active: honor_params.type == item.type }"
                          @click="clickHonor(item, index)"
                          v-for="(item, index) in honor_list"
                          :key="index"
                          >{{ item.name }}</span
                        >
                      </div> -->
                        <div class="h_date flex-row">
                          <span class="c2" :class="{ active: Sourcedate_type == index }"
                            @click=" SourceclickDate(item, index)" v-for="(item, index) in Source_data" :key="index">{{
                                                        item.name }}</span>
                        </div>
                        <div class="h_list" v-if="Source_data_list.length > 0">
                          <div class="info flex-row" v-for="(item, index) in Source_data_list" :key="index">
                            <div class="left flex-row">
                              <span class="on" :class="'on' + index">{{
                                                              index + 1
                                                              }}</span>
                              <!-- <img
                              src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80"
                            /> -->
                              <div v-if="item.title" style="margin-left:15px">
                                {{ item.title }}
                              </div>
                              <!-- <div v-if="item.follow_user">
                              {{ item.follow_user.user_name }}
                            </div>
                            <div v-if="item.deal_user">
                              {{ item.deal_user.user_name }}
                            </div> -->
                            </div>
                            <!-- <div class="right" v-show="item.number||item.number==0">
                            数量：{{ item.number }}
                          </div> -->
                            <!-- <div class="right" v-show="honor_params.type == 2">
                            认领：{{ item.num }}
                          </div>
                          <div class="right" v-show="honor_params.type == 3">
                            跟进：{{ item.num }}
                          </div>
                          <div class="right" v-show="honor_params.type == 4">
                            成交：{{ item.num }}
                          </div> -->
                          </div>
                        </div>
                        <div class="h_list" v-else>
                          <myEmpty></myEmpty>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="huan">
                  <!-- <div class="s_title">跟进数据统计</div>
                <div class="tab-title">
                  <div
                    class="chart-box"
                    id="chart-box1"
                    style="width: 100%; height: 350px"
                  ></div>
                </div> -->
                  <div class="ranking">
                    <div class="s_title">团队排行</div>
                    <div class="tab-title">
                      <div class="honor_list">
                        <div class="h_sort flex-row">
                          <span class="c2" :class="{ active: honor_params.type == item.type }"
                            @click="clickHonor(item, index)" v-for="(item, index) in honor_list" :key="index">{{ item.name
                                                        }}</span>
                        </div>
                        <div class="h_date flex-row">
                          <span class="c2" @click="clickDate(item, index)" v-for="(item, index) in honor_date"
                            :key="item.type" :class="{ active: date_type == item.type }">{{ item.name }}</span>
                        </div>
                        <div class="h_list" v-if="honor_data_list.length > 0">
                          <div class="info flex-row" v-for="(item, index) in honor_data_list" :key="index">
                            <div class="left flex-row">
                              <span class="on" :class="'on' + index">{{
                                                              index + 1
                                                              }}</span>
                              <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" />
                              <div v-if="item">
                                {{ item.user_name }}
                              </div>
                              <div v-if="item.follow_user">
                                {{ item.follow_user.user_name }}
                              </div>
                              <div v-if="item.deal_user">
                                {{ item.deal_user.user_name }}
                              </div>
                            </div>
                            <!-- <div class="right" v-show="honor_params.type == 1">
                            获客：{{ item.number }}
                          </div>
                          <div class="right" v-show="honor_params.type == 2">
                            认领：{{ item.number }}
                          </div>
                          <div class="right" v-show="honor_params.type == 3">
                            跟进：{{ item.number }}
                          </div>
                          <div class="right" v-show="honor_params.type == 4">
                            成交：{{ item.number }}
                          </div> -->
                          </div>
                        </div>
                        <div class="h_list" v-else>
                          <myEmpty></myEmpty>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-col>
        <!-- <el-col :span="8">
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="calendar">
              <div class="tab-title">
                <div class="s_title">工作日历</div>
                <div class="date_box">
                  <date @onClickTime="onClickTime" />
                </div>
                <div class="remind flex-row">
                  <span class="s_title">提醒事项</span>
                  <div>标已读</div>
                  <div class="num flex-row">
                    <i class="el-icon-arrow-left" @click="onClickPage(1)"></i>
                    <div>
                      <span>{{ page }}</span
                      >/{{ remind_list.length }}
                    </div>
                    <i class="el-icon-arrow-right" @click="onClickPage(2)"></i>
                  </div>
                </div>
                <div class="remind_info">
                  <div v-for="item in remind_list[page - 1]" :key="item.id">
                    {{ item.content }}
                  </div>
                </div>
                <div class="remind flex-row">
                  <span class="s_title">待处理事项</span>
                </div>
                <div class="pending">
                  <div class="flex-row color1">
                    <div class="flex-row tit"><span></span>即将掉公海客户</div>
                    <span>{{ processed_data.the_discard_num }}</span>
                  </div>
                  <div class="flex-row color2">
                    <div class="flex-row tit"><span></span>7日未跟进客户</div>
                    <span>{{ processed_data.seven_day_num }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="ranking">
              <div class="s_title">荣誉榜</div>
              <div class="tab-title">
                <div class="honor_list">
                  <div class="h_sort flex-row">
                    <span
                      class="c2"
                      :class="{ active: honor_params.type == item.type }"
                      @click="clickHonor(item, index)"
                      v-for="(item, index) in honor_list"
                      :key="index"
                      >{{ item.name }}</span
                    >
                  </div>
                  <div class="h_date flex-row">
                    <span
                      class="c2"
                      :class="{ active: date_type == index }"
                      @click="clickDate(item, index)"
                      v-for="(item, index) in honor_date"
                      :key="index"
                      >{{ item.name }}</span
                    >
                  </div>
                  <div class="h_list" v-if="honor_data_list.length > 0">
                    <div
                      class="info flex-row"
                      v-for="(item, index) in honor_data_list"
                      :key="index"
                    >
                      <div class="left flex-row">
                        <span class="on" :class="'on' + index">{{
                          index + 1
                        }}</span>
                        <img
                          src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80"
                        />
                        <div v-if="item.admin">{{ item.admin.user_name }}</div>
                        <div v-if="item.follow_user">
                          {{ item.follow_user.user_name }}
                        </div>
                        <div v-if="item.deal_user">
                          {{ item.deal_user.user_name }}
                        </div>
                      </div>
                      <div class="right">跟进：{{ item.num }}</div>
                    </div>
                  </div>
                  <div class="h_list" v-else>
                    <myEmpty></myEmpty>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col> -->
      </el-row>
    </div>
    <div class="gsname" v-if="showname">{{ username }} , 欢迎你！</div>
  </div>
</template>
<script>
// import * as echarts from "echarts";
// import date from "@/components/components/date";
import myEmpty from "@/components/components/my_empty.vue";
export default {
  name: "crm_index",
  components: {
    // date,
    myEmpty,
  },
  data() {
    return {
      showname:false,
      isBold: false,
      honor_list: [
        { name: "跟进榜", type: 1 },
        { name: "获客榜", type: 2 },
        { name: "认领榜", type: 3 },
        { name: "成交榜", type: 4 },
      ],
      honor_date: [
        { name: "昨日", type: 2},
        { name: "今天", type:  1 },
        { name: "7日", type: 3 },
        { name: "本月", type: 4 },
        { name: "上月", type: 5 },
      ],
      Source_data:[
        { name: "昨日", type: 2 },
        { name: "今天", type: 1},
        { name: "7日", type: 3 },
        { name: "本月", type: 4 },
        { name: "上月", type: 5 },
      ],
      Source_data_list:[],
      date_type: 2,
      Sourcedate_type:0,
      Source_parmas:{
        date_type:2
      },
      honor_params: {
        type: 1,
        date_type: 2,
      },
      honor_data_list: [],
      crm_index_form: {},
      date_params: {
        date: "",
      },
      processed_data: {},
      remind_list: [],
      page: 1,
      website_ids: "", // 存储站点id
      username:"",//储存公司名字
      newtopdata:{
        // khzl:0,
        // yxkh:0,
        // myke:0,
        // tdcy:0,
        // jjdg:0,
        // wgj:0,
      },
      newcrm_index_form: {},
    };
  },
  created() {
    this.username = sessionStorage.getItem('gsname');
    this.$http.Workbenchdata().then((res)=>{
      console.log(res.data,"工作台数据");
      if(res.status==200){
        if(res.data==0){
          console.log(this.username);
          this.isBold = true
          this.showname = true
          return
        }
        return
      }

    })
    this.website_ids = this.$route.query.website_id;
    // this.honor_params.date = this.currentTimeYesterday();
    this.date_params.date = this.curentTime().slice(0, 10);
    this.$nextTick(() => {
      // this.getCrmIndexData();
      this.getCrmIndexPieData();
    });
    this.getHonorData();
    // this.getCrmIndexDateData();
    this.TopgetCrmIndexDate()//顶部数据
    this.newgetCrmIndex()//中间数据
    this.getjuesename()//获取角色名称
    this.pagecustomization()//获取自定义页面每页多少条
  },
  methods: {

    //获取角色名称(缓存，用于列表)
    getjuesename(){
      this.$http.getsetuprolename().then(res=>{
        if(res.status == 200){
          localStorage.setItem('juesename', JSON.stringify(res.data)); // 将数据存储到localStorage中
        }
      })
    },
    //获取列表每页多少条
    pagecustomization() {
      this.$http.getAuthShow('list_limit').then(res => {
        if (res.status == 200) {
          localStorage.setItem('pagenum', JSON.stringify(res.data)); // 将数据存储到localStorage中
        }
      })
    },
    // 获取当前日期
    curentTime() {
      var now = new Date();
      var year = now.getFullYear(); //年
      var month = now.getMonth() + 1; //月
      var day = now.getDate(); //日
      var hh = now.getHours(); //时
      var mm = now.getMinutes(); //分
      var ss = now.getSeconds(); //分
      var clock = year + "-";
      if (month < 10) clock += "0";
      clock += month + "-";
      if (day < 10) clock += "0";
      clock += day + " ";
      if (hh < 10) clock += "0";
      clock += hh + ":";
      if (mm < 10) clock += "0";
      clock += mm + ":";
      if (ss < 10) clock += "0";
      clock += ss;
      return clock;
    },
    // curentTime1() {
    //   var now = new Date();
    //   var year = now.getFullYear(); //年
    //   var month = now.getMonth() + 1; //月
    //   var day = now.getDate(); //日
    //   var hh = "0"; //时
    //   var mm = "0"; //分
    //   var ss = "0"; //分
    //   var clock = year + "-";
    //   if (month < 10) clock += "0";
    //   clock += month + "-";
    //   if (day < 10) clock += "0";
    //   clock += day + " ";
    //   if (hh < 10) clock += "0";
    //   clock += hh + ":";
    //   if (mm < 10) clock += "0";
    //   clock += mm + ":";
    //   if (ss < 10) clock += "0";
    //   clock += ss;
    //   return clock;
    // },
    // 获取昨日
    // currentTimeYesterday() {
    //   var now = new Date();
    //   var year = now.getFullYear(); //年
    //   var month = now.getMonth() + 1; //月
    //   var day = now.getDate() - 1; //日
    //   var clock = year + "-";
    //   if (month < 10) clock += "0";
    //   clock += month + "-";
    //   if (day < 10) clock += "0";
    //   clock += day ? day + " 00:00:00" : day + 1 + " 00:00:00";
    //   return clock;
    // },
    // weekCheckingIn() {
    //   this.weekChecking_inloading = true;
    //   var now = new Date();
    //   var nowTime = now.getTime();
    //   // var day = now.getDay() || 7; // 不加 || 7.周末会变成下周一
    //   var oneDayTime = 24 * 60 * 60 * 1000;
    //   var MondayTime = nowTime - 7 * oneDayTime; //显示周一
    //   //调用方法
    //   return this.formatDate(new Date(MondayTime)) + " 00:00:00";
    // },
    //格式化日期：yyyy-MM-dd
    formatDate(date) {
      var myyear = date.getFullYear();
      var mymonth = date.getMonth() + 1;
      var myweekday = date.getDate();

      if (mymonth < 10) {
        mymonth = "0" + mymonth;
      }
      if (myweekday < 10) {
        myweekday = "0" + myweekday;
      }
      return myyear + "-" + mymonth + "-" + myweekday;
    },
    // 获取当前月份第一天 type=1当月 type=2上月
    // getCurrentMonthFirst(type) {
    //   var date = new Date();
    //   date.setDate(1);
    //   var month = "";
    //   if (type === 1) {
    //     month = parseInt(date.getMonth() + 1);
    //   } else {
    //     month = parseInt(date.getMonth());
    //   }
    //   var day = date.getDate();
    //   if (month < 10) {
    //     month = "0" + month;
    //   }
    //   if (day < 10) {
    //     day = "0" + day;
    //   }
    //   return date.getFullYear() + "-" + month + "-" + day + " 00:00:00";
    // },
    // getCrmIndexDateData() {
    //   this.$http
    //     .getCrmIndexDateData({ params: this.date_params })
    //     .then((res) => {
    //       if (res.status === 200) {
    //         this.processed_data = {
    //           seven_day_num: res.data.seven_day_num,
    //           the_discard_num: res.data.the_discard_num,
    //         };
    //         let data = JSON.parse(JSON.stringify(res.data.remind_list));
    //         this.remind_list = this.oneArrToTwoArr(data);
    //       }
    //     });
    // },
    //获取顶部统计数据
    TopgetCrmIndexDate(){
      this.$http.topgetCrmIndexDate().then((res)=>{
        if(res.status==200){
          console.log(res.data,"新数据");
          this.newtopdata=res.data
        }
      })
    },
    //获取中间数据
    newgetCrmIndex(){
      this.$http.newgetCrmIndex().then((res)=>{
        if(res.status==200){
          console.log(res.data,"新中间数据");
          this.newcrm_index_form = res.data
        }
      })
    },
    getCrmIndexPieData() {
      this.$http.sourcestatistics({params:this.Source_parmas}).then((res) => {
        if (res.status === 200) {
          // console.log(res.data,"来源");
          this.Source_data_list = res.data
          // this.echartsInitMoney();
          // this.echartsInitMoney1(res.data);
        }
      });
    },
    SourceclickDate(e, i){
      this.Sourcedate_type = i
      this.Source_parmas.date_type = e.type
      this.getCrmIndexPieData()
    },
    oneArrToTwoArr(data) {
      var newData = [];
      var zyf = 1; //一维数组转二维数组长度(此处是二维数组每一个长度控制)
      for (var i = 0; i < Math.ceil(data.length / zyf); i++) {
        newData[i] = [];
        newData[i].push(data[i * zyf]);
        for (var j = 1; j < zyf; j++) {
          if (data[i * zyf + j] == undefined) {
            //超出长度控住
            return newData;
          } else {
            newData[i].push(data[i * zyf + j]);
          }
        }
      }
      return newData;
    },
    onClickPage(type) {
      if (type === 1 && this.page !== 1) {
        this.page--;
      }
      if (
        type === 2 &&
        this.remind_list.length !== this.page &&
        this.remind_list.length > 0
      ) {
        this.page++;
      }
    },
    // getCrmIndexData() {
    //   this.$http.getCrmIndexData().then((res) => {
    //     if (res.status === 200) {
    //       this.crm_index_form = res.data;
    //       console.log(this.crm_index_form,"this.crm_index_form");
    //     }
    //   });
    // },
    onClickTime(e) {
      this.date_params.date = e;
      this.page = 1;
      this.getCrmIndexDateData();
    },
    getHonorData() {
      this.$http
        .getCrmIndexHonorData({ params: this.honor_params })
        .then((res) => {
          if (res.status === 200) {
            this.honor_data_list = res.data
          }
        });
    },
    // echartsInitMoney(data) {
    //   var chartDom = document.getElementById("chart-box");
    //   var myChart = echarts.init(chartDom);
    //   var option;
    //   var arr = data.source_list.map((item) => {
    //     return {
    //       id: item.id,
    //       name: item.title,
    //       value: item.num,
    //     };
    //   });
    //   option = {
    //     legend: {
    //       top: "5%",
    //       right: "right",
    //     },
    //     color: ["#3D74FC", "#FEC94C", "#04D38A", "#FF655F", "#ffc0cb"], //设置区分（每条线是什么颜色，和 legend 一一对应）
    //     series: [
    //       {
    //         type: "pie",
    //         label: {
    //           show: true,
    //           formatter: "{c}",
    //         },
    //         data: arr,
    //         radius: ["40%", "70%"],
    //       },
    //     ],
    //   };

    //   option && myChart.setOption(option);
    // },
    // echartsInitMoney1(data) {
    //   var chartDom = document.getElementById("chart-box1");
    //   var myChart = echarts.init(chartDom);
    //   var option;
    //   var arr = data.follow_list.map((item) => {
    //     return {
    //       id: item.id,
    //       name: item.title,
    //       value: item.num,
    //     };
    //   });
    //   option = {
    //     legend: {
    //       top: "5%",
    //       right: "right",
    //     },
    //     color: ["#3D74FC", "#FEC94C", "#04D38A", "#FF655F", "#ffc0cb"], //设置区分（每条线是什么颜色，和 legend 一一对应）
    //     series: [
    //       {
    //         type: "pie",
    //         label: {
    //           show: true,
    //           formatter: "{c}",
    //         },
    //         data: arr,
    //         radius: ["40%", "70%"],
    //       },
    //     ],
    //   };

    //   option && myChart.setOption(option);
    // },
    clickHonor(e) {
      this.honor_params.type = e.type;
      this.getHonorData();
    },
    clickDate(e) {
      console.log(e.type);
      this.honor_params.date_type = e.type
      this.date_type = e.type;
      // switch (e.type) {
      //   case 1:
      //     this.honor_params.date_type = this.currentTimeYesterday();
      //     break;
      //   case 2:
      //     this.honor_params.date_type = this.curentTime1();
      //     break;
      //   case 3:
      //     this.honor_params.date_type = this.weekCheckingIn();
      //     break;
      //   case 4:
      //     this.honor_params.date_type = this.getCurrentMonthFirst(1);
      //     break;
      //   case 5:
      //     this.honor_params.date_type = this.getCurrentMonthFirst(2);
      //     break;
      //   default:
      //     break;
      // }
      this.getHonorData();
    },
    // 点击我的客户
    clickMyclient() {
      this.$goPath(`crm_customer_my_list?type=2&status=1&website_id=${this.website_ids}`);
    },
    // 点击即将掉公海
    clickMyFall() {
      this.$goPath(`crm_customer_my_list?type=2&status=5&website_id=${this.website_ids}`);
    },
    // 点击7日内未跟进
    clickMyNotFollow() {
      this.$router.push({ 
        name: 'crm_customer_my_list', 
        query: { type: 2, website_id: this.website_ids }, 
        params: {tabName: '7daysNotFollow'}
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.web-box {
  background: #f1f4fa;
  padding: 28px 0 28px 28px;
  margin: -15px;
  cursor: pointer;

  // position: absolute;
  .snum {
    padding: 32px 24px !important;
  }

  .ranking {
    .honor_list {
      background: #fff;
      border-radius: 6px;

      .h_list {
        height: 271px;
        overflow-y: auto;

        .info {
          align-items: center;
          margin-top: 18px;
          margin-right: 10px;

          .left {
            flex: 1;
            align-items: center;

            div {
              font-weight: 500;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              width: 100px;
            }

            span {
              border-radius: 2px;
              width: 16px;
              height: 16px;
              line-height: 16px;
              text-align: center;
              font-size: 12px;

              &.on {
                color: #2e3c4e;
                background: #f1f4fa;
              }

              &.on0 {
                background: #f95742;
                color: #fff;
              }

              &.on1 {
                background: #ff9a06;
                color: #fff;
              }

              &.on2 {
                background: #ffd26e;
                color: #fff;
              }
            }

            img {
              width: 32px;
              height: 32px;
              margin: 0 12px;
            }
          }

          .right {
            color: #8a929f;
            font-size: 14px;
          }
        }
      }

      .h_date {
        justify-content: left;
        margin: 17px 0;
        font-size: 12px;
        color: #8a929f;

        span {
          margin-right: 12px;
          padding: 4px 10px;
          background: #f6f6f6;
          border-radius: 4px;

          &.active {
            color: #2d84fb;
            font-weight: 500;
            background: #c0dafe;
          }
        }
      }

      .h_sort {
        justify-content: left;
        font-size: 14px;
        color: #8a929f;
        margin-top: 1px;

        span {
          margin-right: 25px;

          &.active {
            color: #2d84fb;
            font-weight: 500;
          }
        }
      }
    }
  }

  .calendar {
    .tab-title {
      position: relative;
    }

    .pending {
      margin-top: 12px;
      font-size: 14px;

      >.flex-row {
        justify-content: space-between;
        padding: 8px 16px;
        margin-bottom: 2px;
        border-radius: 4px;
        align-items: center;

        .tit {
          align-items: center;

          span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            margin-right: 3px;
          }
        }

        span {
          font-weight: 500;
        }

        &.color1 {
          background: #fef0e7;
          color: #fe6c17;

          .tit {
            span {
              background: #fe6c17;
            }
          }
        }

        &.color2 {
          background: #fef8e5;
          color: #f5bd04;

          .tit {
            span {
              background: #f5bd04;
            }
          }
        }
      }
    }

    .remind_info {
      background: #f6f6f6;
      border-radius: 6px;
      padding: 12px;
      margin: 12px 0;
      min-height: 80px;
      font-size: 14px;
      box-sizing: border-box;
    }

    .remind {
      align-items: center;
      margin-top: 22px;

      >div {
        color: #8a929f;
        font-size: 14px;

        span {
          color: #2d84fb;
        }
      }

      .num {
        align-items: center;
        margin-left: 15px;

        >div {
          margin: 0 5px;
        }

        i {
          position: relative;
          top: 1px;
        }
      }

      >span {
        flex: 1;
        margin-bottom: 0;
      }
    }
  }

  .huan_box {
    justify-content: space-between;

    .huan {
      width: 49%;

      .tab-title {
        padding: 17px 24px;
      }
    }
  }

  .s_title {
    font-size: 18px;
    font-weight: bold;
    color: #2e3c4e;
    margin-bottom: 12px;
  }

  .tab-title {
    padding: 24px;
    border-radius: 4px;
    background: #ffffff;
    align-items: center;
    position: relative;
    justify-content: center;

    .top-right {
      .increase {
        height: 16px;
        justify-content: center;
        align-items: center;

        span {
          color: #fe6c17;
          font-size: 12px;
        }

        img {
          width: 16px;
        }
      }

      .num {
        margin: 12px 0;
        color: #1c212b;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 28px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: center;
      }

      .desc {
        color: #8a929f;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: left;
      }
    }
  }
}

.web-boxA {
  filter: blur(8px);
}

.gsname {
  position: relative;
  font-size: 36px;
  font-family: 600;
  top: -656px;
  left: 35%;
  // z-index: 9999
}

.flex-row {
  display: flex;
}

.el-row {
  margin-bottom: 20px;
  width: 100%;

  &:last-child {
    margin-bottom: 0;
  }
}

.content-box {
  // align-items: center;
  width: 100%;

  .right-content {
    margin-left: 66px;
    width: 100%;

    .t {
      font-size: 13px;
      width: 100%;

      thead {
        color: #8a929f;
        font-family: PingFang SC;
        font-size: 13px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: left;
        font-weight: 400;

        th {
          text-align: center;
        }
      }

      tbody {
        margin-top: 16px;

        tr {
          th {
            color: #8a929f;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 13px;
            line-height: 32px;
            letter-spacing: 0px;
            text-align: right;
          }
        }

        td {
          padding: 8px 16px;
          color: #062047;
          font-family: PingFang SC;
          font-weight: semibold;
          font-size: 13px;
          line-height: 32px;
          letter-spacing: 0px;
          text-align: center;

          .name {
            width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .t {
    font-size: 13px;
    width: 100%;

    thead {
      color: #6b7283;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 13px;
      line-height: 28px;
      letter-spacing: 0px;
      text-align: left;

      th {
        text-align: center;
      }
    }

    tbody {
      margin-top: 16px;

      tr {
        th {
          color: #6b7283;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 13px;
          line-height: 28px;
          letter-spacing: 0px;
          text-align: right;
        }
      }

      td {
        padding: 8px 16px;
        color: #062047;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 13px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: center;

        .name {
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.left-img {
  align-items: center;

  .num-box {
    position: relative;

    div {
      text-align: center;
      line-height: 47px;
      font-size: 13px;
      color: #fff;
    }

    .l-t {
      background-image: url("../../../assets/webimg/blue.png");
      height: 47px;
      background-repeat: no-repeat;
      width: 238px;
      margin-bottom: 4px;
    }

    .l-c {
      position: relative;
      background-image: url("../../../assets/webimg/red.png");
      height: 47px;
      background-repeat: no-repeat;
      width: 177px;
      margin-bottom: 4px;
    }

    .l-b {
      position: relative;
      background-image: url("../../../assets/webimg/huang.png");
      height: 47px;
      width: 113px;
      background-repeat: no-repeat;
    }

    p {
      position: absolute;
      top: 10px;
      right: -30px;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 13px;
      line-height: 28px;
      letter-spacing: 0px;
      text-align: left;
    }

    .p1 {
      color: #0083ff;
    }

    .p2 {
      color: #f95741;
    }

    .p3 {
      color: #ffb108;
    }
  }
}</style>
