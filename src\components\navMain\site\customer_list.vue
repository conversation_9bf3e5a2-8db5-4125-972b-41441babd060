<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ tableData.length }}</span>
              <span class="text">条</span>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="TableRowStyle"
          >
            <el-table-column
              prop="id"
              label="记录ID"
              width="100"
            ></el-table-column>
            <el-table-column prop="website_id" label="系统ID"></el-table-column>

            <el-table-column prop="name" label="客户姓名" v-slot="{ row }">
              <span>{{ row.name }}</span>
              <el-link
                style="margin-left:10px"
                @click="$onCopyValue(row.external_userid)"
                type="primary"
              >
                ID
              </el-link>
            </el-table-column>
            <el-table-column prop="gender" label="性别">
              <template slot-scope="scope">
                <el-tag type="danger" v-if="scope.row.gender == 0">未知</el-tag>
                <el-tag effect="dark" v-else-if="scope.row.gender == 1"
                  >男性</el-tag
                >
                <el-tag
                  effect="dark"
                  type="danger"
                  v-else-if="scope.row.gender == 2"
                  >女性</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型">
              <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.type == 1"
                  >微信用户</el-tag
                >
                <el-tag v-else>企业微信用户</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="头像">
              <template slot-scope="scope">
                <el-avatar
                  v-if="scope.row.avatar"
                  style="height: 40px; width: 40px"
                  :src="scope.row.avatar"
                ></el-avatar>
                <el-avatar
                  v-else
                  style="height: 40px; width: 40px"
                  src="https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg"
                ></el-avatar>
              </template>
            </el-table-column>
            <el-table-column
              prop="corp_name"
              label="企业名称"
            ></el-table-column>
            <el-table-column prop="user.name" label="添加人"></el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "customer_list",
  components: {},
  data() {
    return {
      tableData: [],
      table_header: [
        { prop: "id", label: "记录ID" },
        { prop: "website_id", label: "系统ID" },
        { prop: "external_userid", label: "客户ID" },
        { prop: "name", label: "客户姓名" },
        {
          prop: "gender",
          label: "性别",
          render: (h, data) => {
            if (data.row.gender == 0) {
              return <el-tag type="danger">未知</el-tag>;
            } else if (data.row.gender == 1) {
              return <el-tag effect="dark">男性</el-tag>;
            } else if (data.row.gender == 2) {
              return (
                <el-tag effect="dark" type="danger">
                  女性
                </el-tag>
              );
            }
          },
        },
        {
          prop: "type",
          label: "类型",
          render: (h, data) => {
            if (data.row.type == 1) {
              return <el-tag type="success">微信用户</el-tag>;
            } else if (data.row.type == 2) {
              return <el-tag>企业微信用户</el-tag>;
            }
          },
        },
        {
          label: "头像",
          render: (h, data) => {
            return data.row.avatar ? (
              <img style="height:40px;width:40px" src={data.row.avatar} />
            ) : (
              <img
                style="height:40px;width:40px"
                src="https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg"
              />
            );
          },
        },
        { prop: "corp_name", label: "企业名称" },
        { prop: "user.name", label: "添加人" },
      ],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      is_table_loading: true,
      inputVal: "",
    };
  },
  computed: {},
  watch: {},
  methods: {
    getCustomerList() {
      this.is_table_loading = true;
      this.$http.getCustomerList({ params: this.params }).then((res) => {
        console.log(res, "客户列表");
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    TableRowStyle({ rowIndex }) {
      let rowBackground = {};
      if ((rowIndex + 1) % 2 === 0) {
        rowBackground.background = "#EFEFEF";
        return rowBackground;
      }
    },
  },
  created() {},
  mounted() {
    this.getCustomerList();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
</style>
