<template>
    <el-cascader :placeholder="placeholder" v-bind="$attrs" v-on="$listeners" :filterable="filterable"
        :style="{width:width}" :options="list" :props="{label:'shortname', value: 'id'}"
        :loading="loading">
    </el-cascader>
</template>

<script>
let list = [];
export default {
    name: 'tRegionCascader',
    props: {
        datas: {type: Array, default: ()=>[]},
        filterable: {type: Boolean, default: true},
        remote: {type: Boolean, default: true},
        placeholder: {type: String, default: "请选择区域"},
        width: {type: String, default: "auto"},
        api: {type: String, default: "getRegionAll"},
    },
    data(){
        return {
            loading:false,
            isDataLoaded: false,
            list: []
        }
    },
    watch: {
        datas: {
            handler(d){
                this.list = d;
            },
            immediate: true
        }
    },
    created(){
        this.initMenuData();
    },
    deactivated(){
        list = [];
    },
    beforeDestroy(){
        list = [];
    },
    methods: {
        //初始化菜单数据
        initMenuData(){
            if(!this.isDataLoaded){
                this.isDataLoaded =  this.list.length > 0;
            }
            if(!this.isDataLoaded){
                this.isDataLoaded = true;
                if(list.length){
                    this.list = list
                }else{
                    this.loadData();
                }
            }
        },
        //获取菜单数据
        async loadData(){
            this.loading = true;
            try{
                const res = await this.$http[this.api]();
                if(res.status == 200){
                    this.list = list = res.data || [];
                }
            }catch(e){
                console.error(e);
            }
            this.loading = false;
        },
        getOptionData(){
            return this.list.map(item => {
                return {
                    label: item.name,
                    value: item.values
                }
            });
        }
    },
}
</script>

<style scoped lang="scss">

</style>