<template>
    <div class="container">
      <div class="project_title">角色名称</div>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="录入人">
          <el-input placeholder="请填写自定义名称 如：主播" class="text" v-model="form.diy_create_name"></el-input>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            长度不能超过4个字符,录入人和维护人的名称不可一致！
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
        </el-form-item>
        <el-form-item  label="维护人">
          <el-input placeholder="请填写自定义名称 如：选房师" class="text" v-model="form.diy_follow_name"></el-input>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            长度不能超过4个字符,录入人和维护人的名称不可一致！
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
        </el-form-item>
        <el-form-item label="带看人">
          <el-input placeholder="请自定义名称" class="text" v-model="form.diy_take_name" :disabled="true"></el-input>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            不建议自定义更改 
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
        </el-form-item>
        <el-form-item label="成交人">
          <el-input placeholder="请自定义名称" class="text" v-model="form.diy_deal_name" :disabled="true"></el-input>
          <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            不建议自定义更改 
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onClickForm">确认</el-button>
        </el-form-item>
      </el-form>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        form:{},
      };
    },
    mounted() {
      this.getprolename()
    },
    methods: {
      //获取配置
      getprolename(){
        this.$http.getSiteCrmSetting().then((res)=>{
          if(res.status==200){
            this.form = res.data
            localStorage.setItem('juesename', JSON.stringify(res.data)); // 将数据存储到localStorage中
          }
        })
      },
      //更新配置
      onClickForm(){
        if (this.form.diy_create_name || this.form.diy_follow_name) {
          const createNameLength = this.form.diy_create_name.toString().length;
          const followNameLength = this.form.diy_follow_name.toString().length;

          if (createNameLength > 4 || followNameLength > 4) {
            return this.$message.warning("录入人和维护人的名称长度不能超过4个字符！");
          }

          if (this.form.diy_create_name === this.form.diy_follow_name) {
            return this.$message.warning("录入人和维护人的名称不可一致！");
          }
        }
        this.$http.setSiteCrmSetting(this.form).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getprolename()
          }
        })
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
     .project_title {
        color: #2e3c4e;
        margin-bottom: 24px;
    }
    .text{
      width: 300px;
    }
  </style>
  