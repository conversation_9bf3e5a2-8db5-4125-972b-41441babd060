<template>
  <el-container>
    <div class="flex-1">
      <div class="page-top">
        <el-button type="primary" @click="toAdd" size="mini" class="el-icon-plus">
          添加
        </el-button>
        <el-button type="primary" @click="toImport" size="mini" class="el-icon-plus">
          导入微信客服
        </el-button>
      </div>
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick" class="flex-1">
          <el-tab-pane v-for="item in params_list" :key="item.id" :label="item.name" :name="item.id">
          </el-tab-pane>

          <div v-if="activeName === 'zhanghao'">
            <el-table v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
              highlight-current-row :row-style="$TableRowStyle">
              <!-- @selection-change="handleSelectionChange" -->
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <!-- <el-table-column
                label="ID"
                min-width="30px"
                align="center"
                prop="id"
              >
              </el-table-column> -->
              <el-table-column label="客服名称" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.name }}</span>
              </el-table-column>
              <el-table-column label="头像" min-width="60px" align="center" v-slot="{ row }">
                <div class="row-img">
                  <img v-if="row.avatar_url" :src="row.avatar_url" alt="" />
                  <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
                </div>
              </el-table-column>
              <el-table-column label="作席人数" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.reception_count }}人</span>
              </el-table-column>
              <el-table-column label="创建时间" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.created_at }}</span>
              </el-table-column>
              <el-table-column label="操作" min-width="60px" align="center" fixed="right" v-slot="{ row }">
                <el-link type="primary" @click="toReceive(row)">接待人员</el-link>
                <el-link type="primary" @click="toEdit(row)">编辑</el-link>

                <el-popconfirm title="确定删除吗？" @onConfirm="deleteService(row)">
                  <el-link slot="reference" type="danger">删除</el-link>
                </el-popconfirm>
              </el-table-column>
            </el-table>
            <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
              :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
            </el-pagination>
          </div>
          <div class="content" v-if="activeName == 'add'">
            <div class="title">接入渠道</div>
            <el-col :span="12">
              <el-form label-width="80px" label-position="left">
                <el-form-item label="客服账号">
                  <el-select v-model="form.kf_id">
                    <el-option v-for="item in user_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="渠道分类">
                  <el-select v-model="form.cate_id">
                    <el-option v-for="item in qudao_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submit" :loading="isSubmiting">创建</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </div>
          <div v-if="activeName === 'qudaoguanli'">
            <el-table v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
              highlight-current-row :row-style="$TableRowStyle">
              <!-- @selection-change="handleSelectionChange" -->
              <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
              <!-- <el-table-column
                label="ID"
                min-width="30px"
                align="center"
                prop="id"
              >
              </el-table-column> -->
              <el-table-column label="客服名称" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.kf_name }}</span>
              </el-table-column>
              <el-table-column label="渠道类型" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.cate_name }}</span>
              </el-table-column>
              <el-table-column label="访问数量" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.visitors }}</span>
              </el-table-column>
              <el-table-column label="已接待客户" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.reception }}</span>
              </el-table-column>

              <el-table-column label="创建时间" min-width="60px" align="center" v-slot="{ row }">
                <span class="name">{{ row.created_at }}</span>
              </el-table-column>
              <el-table-column label="操作" min-width="60px" align="center" v-slot="{ row }">
                <el-link type="primary" @click="copy(row)">渠道链接</el-link>
                <el-link type="primary" @click="showTongji(row)">数据统计</el-link>
                <el-link type="primary" @click="toEditQudao(row)">编辑</el-link>
                <el-link type="primary" @click="showQrcode(row)">渠道码</el-link>
                <el-popconfirm title="确定删除吗？" @onConfirm="deleteQudao(row)">
                  <el-link slot="reference" type="danger">删除</el-link>
                </el-popconfirm>
              </el-table-column>
            </el-table>
            <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
              :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
            </el-pagination>
          </div>
        </el-tabs>
      </div>
    </div>

    <el-dialog :visible.sync="show_edit_dia" width="660px" title="编辑客服">
      <editService v-if="show_edit_dia" :form="form_params" @success="editOk" @cancel="cancelEdit"></editService>
    </el-dialog>
    <el-dialog :visible.sync="show_qrcode_dia" width="400px" position="center" title="渠道码">
      <div class="qrcode_dia div align-center j-center" v-if="show_qrcode_dia">
        <div id="qrcode" class="qrcode" ref="qrCodeUrl"></div>
        <div class="tips">保存或扫码查看</div>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="show_edit_qudao_dia" width="660px" title="编辑渠道">
      <editQudao v-if="show_edit_qudao_dia" :form="currentQudao" :userList="user_list" :qudaoList="qudao_list"
        @success="editQudaoOk" @cancel="cancelQudaoEdit"></editQudao>
    </el-dialog>
    <el-dialog :visible.sync="show_qudao_static_dia" width="990px" title="渠道数据统计">
      <qudaoStatic v-if="show_qudao_static_dia" :id="currentId"></qudaoStatic>
    </el-dialog>

    <el-dialog :visible.sync="show_add_dia" width="660px" title="添加客服">
      <addService v-if="show_add_dia" @success="addOk" @cancel="cancelAdd"></addService>
    </el-dialog>
  </el-container>
</template>

<script>
import editService from "./components/service/editService.vue";
import editQudao from "./components/qudao/editQudao.vue";
import qudaoStatic from "./components/qudao/qudaoStatic.vue";
import addService from "./components/service/addService.vue";
import QRCode from "qrcodejs2";
import { Loading } from "element-ui";
export default {
  name: "crm_customer_service",
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        name: "",
      },
      activeName: "zhanghao",
      params_list: [
        { id: "zhanghao", name: "客服管理" },
        { id: "add", name: "接入渠道" },
        { id: "qudaoguanli", name: "渠道管理" },
      ],
      is_table_loading: false,
      form: {
        kf_id: "",
        cate_id: "",
      },
      user_list: [],
      total: 0,
      show_add_dia: false,
      show_edit_dia: false,
      form_params: {},
      qudao_list: [],
      show_edit_qudao_dia: false,
      currentQudao: {},
      show_qrcode_dia: false,
      currentId: "",
      show_qudao_static_dia: false,
      isSubmiting: false,
    };
  },
  components: {
    editService,
    addService,
    editQudao,
    qudaoStatic,
  },
  created() {
    this.getDataList();
  },
  methods: {
    handleClick() {
      this.getDataList();
    },
    toAdd() {
      this.show_add_dia = true;
    },
    // 获取列表数据
    getDataList() {
      switch (this.activeName) {
        case "zhanghao":
          this.is_table_loading = true;
          this.$http.getCrmServiceList({ params: this.params }).then((res) => {
            this.is_table_loading = false;
            if (res.status === 200) {
              this.tableData = res.data.data;
              this.params.total = res.data.total;
            }
          });
          break;
        case "add":
          if (this.qudao_list.length == 0) {
            this.getQudaoTypeList();
          }
          if (this.user_list.length == 0) {
            this.getUserList();
          }
          break;
        case "qudaoguanli":
          this.$http.getCrmSceneList({ params: this.params }).then((res) => {
            this.is_table_loading = false;
            if (res.status === 200) {
              this.tableData = res.data.data;
            }
          });

          break;
        default:
          break;
      }
    },
    getUserList() {
      let params = Object.assign({}, this.params);
      params.per_page = 10000;
      this.$http.getCrmServiceList({ params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.user_list = res.data.data;
        }
      });
    },
    getQudaoTypeList() {
      this.$http.crmSceneType().then((res) => {
        if (res.status == 200) {
          this.qudao_list = res.data;
        }
      });
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },

    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该公司, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCompany(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    submit() {
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .addCrmScene(this.form)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success(res.message || "添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 500);
          } else {
            this.isSubmiting = false;
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    editOk() {
      this.show_edit_dia = false;
      this.getDataList();
    },
    cancelEdit() {
      this.show_edit_dia = false;
    },
    addOk() {
      this.show_add_dia = false;
      this.getDataList();
    },
    cancelAdd() {
      this.show_add_dia = false;
    },
    // 去编辑
    toEdit(row) {
      this.$http.getCrmServiceDetail(row.id).then((res) => {
        if (res.status == 200) {
          this.form_params = res.data;
          this.show_edit_dia = true;
        }
      });
    },
    toEditQudao(row) {
      if (this.qudao_list.length == 0) {
        this.getQudaoTypeList();
      }
      if (this.user_list.length == 0) {
        this.getUserList();
      }
      this.currentQudao = row;
      this.show_edit_qudao_dia = true;
    },
    cancelQudaoEdit() {
      this.show_edit_qudao_dia = false;
    },
    editQudaoOk() {
      this.show_edit_qudao_dia = false;
      this.getDataList();
    },
    // 删除
    deleteService(row) {
      this.$http.delCrmService(row.id).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message || "删除成功");
          this.getDataList();
        } else {
          this.$message.error(res.message || "删除失败");
        }
      });
    },
    // 删除
    deleteQudao(row) {
      this.$http.delCrmScene(row.id).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message || "删除成功");
          this.getDataList();
        } else {
          this.$message.error(res.message || "删除失败");
        }
      });
    },
    toImport() {
      this.$confirm("导入微信客服 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = Loading.service({
            target: ".el-table",
            lock: true,
            text: "正在导入中 请稍后...", //可以自定义文字
            spinner: "el-icon-loading", //自定义加载图标类名
          });
          this.$http
            .importCrmService()
            .then((res) => {
              if (res.status == 200) {
                this.$message.success(res.message || "导入成功");
              }
              this.loading.close();
              this.loading = null;
            })
            .catch(() => {
              this.loading.close();
              this.loading = null;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消导入",
          });
        });
    },
    toReceive(row) {
      this.$goPath(`/crm_customer_receiver?id=${row.id}`);
    },
    copy(row) {
      this.$onCopyValue(row.link);
    },
    showQrcode(row) {
      this.show_qrcode_dia = true;
      this.$nextTick(() => {
        let url = row.link;
        new QRCode(this.$refs.qrCodeUrl, {
          text: url, // 需要转换为二维码的内容
          width: 132,
          height: 132,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.H,
        });
      });
    },
    showTongji(row) {
      this.currentId = row.kf_id;
      this.show_qudao_static_dia = true;
    },
  },
};
</script>
<style scoped lang="scss">
.el-input,
.el-select {
  width: 200px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  padding: 20px 0;
}

.content {
  padding-left: 20px;
}

.row-img {
  width: 50px;
  height: 50px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 50%;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}

.page-top {
  padding: 0 10px 10px;
  border-bottom: 20px solid #f1f4fa;
}

.el-link {
  margin-right: 10px;
}

.qrcode_dia {
  .qrcode {
    width: 132px;
    height: 132px;
  }

  .tips {
    margin-top: 20px;
    font-size: 18px;
    color: #666;
    font-weight: 600;
  }
}
</style>
