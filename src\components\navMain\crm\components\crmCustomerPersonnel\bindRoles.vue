<template>
<el-dialog :visible.sync="show" width="500px" title="绑定角色">
    <el-form label-width="120px" class="form">
        <el-form-item label="绑定类型">
            <el-radio-group v-model="params.type">
                <el-radio :label="1">追加</el-radio>
                <el-radio :label="2">覆盖</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="角色名称">
            <el-select v-model="params.role_names" multiple placeholder="请选择">
                <el-option v-for="item in roleList" :key="item.value" :label="item.label" :value="item.label" :disabled="item.disabled"></el-option>
            </el-select>
        </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" :loading="submiting" @click="confirm">提 交</el-button>
    </span>
</el-dialog>
</template>
<script>




export default {
    data() {
        return {
            show: false,
            submiting: false,
            successFn: null,
            roleList: [],
            params: {
                ids: '',
                type: 1,
                role_names: [],
            }
        }
    },
    created() {
        this.getRoles();
    },
    methods: {
        async getRoles(){
            const res = await this.$http.getRolelist().catch(()=>{});
            if(res.status == 200){
                this.roleList = (res.data || []).map(item => {
                    let disabled = false;
                    if(item.name === '站长') {
                        item.name ='创始人';
                        disabled = true;
                    }
                    return {
                        label: item.name,
                        value: item.id,
                        disabled
                    }
                });
            }
        },
        open(ids){
            this.params.ids = ids || '';
            this.params.type = 1;
            this.params.role_names = [];
            this.show = true;
            return this;
        },
        onSuccess(fn){
            this.successFn = fn;
        },
        cancle(){
            this.show = false;
        },
        async confirm() {
            if(this.params.type == 1){
                if(this.params.role_names.length == 0){
                    this.$message.error('请选择要追加的角色');
                    return;
                }
            }
            this.submiting = true
            const res = await this.$http.bindAdminRoles(this.params).catch(()=>{});
            this.submiting = false;
            if(res.status ==  200){
                this.$message.success(res.data?.msg || '设置成功')
                this.show = false;
                this.successFn && this.successFn();
            }
        }
    }
}

</script>

<style lang="scss" scoped>
.form{
    padding: 0 32px 0 0;
}
</style>