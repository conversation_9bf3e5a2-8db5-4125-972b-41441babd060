<template>
  <div>
    <el-form label-width="100px">
      <!-- <el-form-item label="所属部门">
        <div class="form-item-block">
          <el-select v-model="form_params.pid" filterable placeholder="请选择">
            <el-option
              v-for="item in departList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </el-form-item> -->
      <el-form-item label="所属部门">
        <div class="form-item-block">
          <el-cascader
            style="width: 240px; margin-right: 12px"
            :options="optArr"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'name',
              emitPath: false,
              children: 'subs',
            }"
            v-model="form_params.parentid"
            clearable
          ></el-cascader>
          <!-- <el-input
            v-model="form_params.parentid"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入姓名"
          >
          </el-input> -->

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>

      <el-form-item label="部门名称">
        <div class="form-item-block">
          <el-input
            v-model="form_params.name"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入姓名"
          >
          </el-input>
        </div>
      </el-form-item>

      <el-form-item label="排序">
        <div class="form-item-block">
          <el-input
            v-model="form_params.order"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入排序"
          ></el-input>
        </div>
      </el-form-item>

      <el-form-item
        label="是否同步微信"
        v-if="website_info.self_auth_create_all"
      >
        <el-radio-group v-model="form_params.syn_wx" size="mini">
          <el-radio :label="1" border>同步</el-radio>
          <el-radio :label="0" border>不同步</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform" :loading="isSubmiting"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  props: ["form", "website_info"],
  data() {
    return {
      form_params: {},
      isSubmiting: false,
      optArr: [],
      showCas: true
    };
  },
  created() {
    this.form_params = Object.assign({}, this.form);
    this.form_params.parentid = this.form.pid;
    console.log(123123);
    this.optArr = [];
    this.getDepartmentList()
  },


  methods: {
    subform() {
      if (!this.website_info.self_auth_create_all) {
        this.form_params.syn_wx = 0;
      }
      let params = Object.assign({}, this.form_params);
      // params.parentid = params.pid;
      delete params.subs;
      delete params.pid;
      params.website_id = localStorage.getItem("website_id");

      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .editCrmDepartment(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    getDepartmentList() {
      this.is_table_loading = true;
      this.$http
        .getCrmDepartmentList()
        .then((res) => {
          if (res.status == 200) {
            this.optArr = this.reFormData(res.data);
            this.setDisable(this.optArr)
            if (res.data.length) {
              this.keyData.push(res.data[0].id);
              this.defaultMenu = res.data[0].id;
              if (res.data[0].subs && res.data[0].subs.length) {
                this.keyData.push(res.data[0].subs[0].id);
              }
            }

            this.params.page = 1;
            delete this.params.department_id;
            this.getMemberList();
          } else {
            this.is_table_loading = false;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    reFormData(data, line = "") {
      data.map((item) => {
        item.pArr = `${line ? line + "," : ""}${item.pid}`;
        if (item.subs && item.subs instanceof Array && item.subs.length) {
          let nameLine = `${line ? line + "," : ""}${item.pid}`;
          this.reFormData(item.subs, nameLine);
        }
      });
      return data;
      // for (let index = 0; index < data.length; index++) {
      //   this.pidArr.push()

      // }
    },
    setDisable(options) {
      if (options && options.length) {

        for (let index = 0; index < options.length; index++) {
          if (options[index].id == this.form_params.id) {
            options[index].disabled = true
            if (options[index].subs && options[index].subs.length) {
              delete options[index].subs
              // options[index].subs.map(item => {
              //   item.disabled = true
              //   return item
              // })
            }
            break
          } else {

            if (options[index].subs && options[index].subs.length) {
              this.setDisable(options[index].subs)
            }
          }

        }
        console.log(this.options);
        // for (let index = 0; index < this.options.length, index++) {


        // }

        // this.options.map(item => {
        //   if (item.department_id == this.form_params.department_id) {
        //     this.department_id = true
        //   }
        // })
      }
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
</style>
