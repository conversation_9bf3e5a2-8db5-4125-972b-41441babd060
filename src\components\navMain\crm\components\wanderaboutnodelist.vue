<template>
    <el-dialog :visible.sync="show" width="80%" :modal="false" title="节点列表">
      <div class="page">
        <el-table
          :data="wanderData"
          style="width: 100%"
          border>
          <el-table-column
            prop="point_name"
            label="节点名称">
          </el-table-column>
          <el-table-column
            prop="execute_type"
            label="执行方式">
            <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.execute_type==1">立即执行</el-tag>
                <el-tag type="warning" v-if="scope.row.execute_type==2">计划执行</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="execute_time"
            width="170px"
            label="执行时间">
          </el-table-column>
          <el-table-column
            prop="is_do"
            label="执行状态">
            <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.is_do==1">已执行</el-tag>
                <el-tag type="warning" v-if="scope.row.is_do==0">未执行</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="finish_status"
            label="执行结果">
            <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.finish_status==1">执行成功</el-tag>
                <el-tag type="warning" v-if="scope.row.finish_status==2">执行失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="assign_type"
            label="分配方式">
            <template slot-scope="scope">
                <div>{{scope.row.assign_type==1?"手动自定义":"平均分配"}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="point_status"
            label="节点状态">
            <template slot-scope="scope">
                <el-tooltip placement="right" effect="light">
                    <div slot="content" style="width:100px;">
                        点击可修改状态
                    </div>
                    <el-tag style="cursor: pointer;" type="success" v-if="scope.row.point_status==1" @click="setstatus(scope.row)">正常</el-tag>
                    <el-tag style="cursor: pointer;" type="danger" v-if="scope.row.point_status==2" @click="setstatus(scope.row)">禁用</el-tag>
                </el-tooltip>
                
            </template>
          </el-table-column>
          <el-table-column
            prop="created_at"
            width="170px"
            label="添加时间">
          </el-table-column>
          <el-table-column
            label="操作"
            width="170px">
            <template slot-scope="scope">
              <div class="flex-row">
                <el-button type="warning" size="small" @click="increasenode(scope.row)">编辑节点</el-button>
                <el-button type="danger" size="small" @click="deletask(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="paging">
          <div>
              <el-pagination
                background
                layout="total,sizes,prev, pager, next, jumper"
                :total="datatable.total"
                :page-sizes="[10, 20, 30, 50,100]"
                :page-size="datatable.per_page"
                :current-page="datatable.page"
                @current-change="onPageChange"
                @size-change="handleSizeChange"
              >
              </el-pagination>
            </div>
        </div>
      </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
        <Editnodes ref="editnodes"  @childButtonClick="getnodelistdata" ></Editnodes>
    </el-dialog>
</template>

<script>
import Editnodes from "@/components/navMain/crm/components/Editnodes.vue"
export default {
    name: 'wanderaboutnodelist',
    components:{
      Editnodes,
    },
    data(){
        return {
            show: false,        //dialog是否显示
            params: {},//流转任务详情
            wanderData:[],//节点详情列表
            paramsA:{
              page:1,
              per_page:10,
            },
            datatable:[]
        }
    },
    methods: {
        //获取流转任务节点列表
        getnodelistdata(){
            this.$http.getnodelist(this.params.id,this.paramsA).then(res=>{
              if(res.status==200){
                this.datatable = res.data
                this.wanderData = res.data.data
              }  
            })
        },
        onPageChange(current_page) {
          this.paramsA.page = current_page;
          this.getnodelistdata();
        },
       //每页几条
       handleSizeChange(e){
        this.paramsA.per_page = e
        this.getnodelistdata();
       },
        //打开弹层
        open(row){
            this.params = row;
            this.getnodelistdata()
            this.show = true;
            return this;
        },
        //修改节点状态
        setstatus(row){
          let params = {
              work_point_id : row.id,
          }
          console.log(row);
          if(row.point_status==1){
              this.$confirm('此操作会将该节点禁用, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                  params.point_status = 2
                  this.$http.setnodestatus(params).then(res=>{
                      if(res.status == 200){
                         this.$message({
                          type: 'success',
                          message: '修改成功!'
                        }); 
                        this.getnodelistdata()
                      }
                  })
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消操作'
                });          
              });
          }else if(row.point_status == 2){
              this.$confirm('此操作会将该节点启用, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                  params.point_status = 1
                  this.$http.setnodestatus(params).then(res=>{
                      if(res.status == 200){
                         this.$message({
                          type: 'success',
                          message: '修改成功!'
                        }); 
                        this.getnodelistdata()
                      }
                  })
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消操作'
                });          
              });
          }
        },
        //删除节点
        deletask(row){
          this.$confirm('此操作会将该节点删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                  this.$http.deletasknode(row.id).then(res=>{
                      if(res.status == 200){
                         this.$message({
                          type: 'success',
                          message: '删除成功!'
                        }); 
                        this.getnodelistdata()
                      }
                  })
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消操作'
                });          
              });
        },
        //编辑节点
        increasenode(row){
          this.$refs.editnodes.open(row,this.params.member_range,this.params.id)
        },
        //取消
        cancle(){
            this.show = false;
        },
        //确定
        confirm(){
            //TODO: 验证+提交
            //在提交成功之后回调
            this.show = false;
        }
    }
}
</script>
<style lang="scss" scoped>
    .page{
        height: 500px;
    }
.paging{
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>