<template>
  <div>
    <el-header class="div row" style="justify-content: space-between">
      <!-- 标题 -->
      <div class="div row">
        <div class="title">企业标签</div>
      </div>
    </el-header>
    <!-- 标签 -->
    <el-tag v-for="item in tagList" :key="item.id" class="label">{{
      item.name
    }}</el-tag>
  </div>
</template>

<script>
export default {
  name: "enterprise_label",
  components: {},
  data() {
    return {
      tagList: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    getTagList() {
      this.$http.getTagList().then((res) => {
        console.log(res, "企业标签");
        if (res.status === 200) {
          this.tagList = res.data;
        }
      });
    },
  },
  created() {},
  mounted() {
    this.getTagList();
  },
};
</script>
<style lang="scss" scoped>
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
}
.label {
  margin-right: 20px;
}
</style>
