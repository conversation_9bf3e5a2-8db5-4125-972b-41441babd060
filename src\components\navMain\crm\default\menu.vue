<template>
  <div>
    <el-form :model="form_info" label-width="100px">
      <div v-for="item in menu_list" :key="item.id">
        <el-form-item :label="item.title + '：'">
          <el-input
            placeholder="请输入"
            v-model="item.title"
            style="width: 300px; margin-bottom: 10px"
          ></el-input>
          <el-input
            placeholder="排序"
            v-model="item.order"
            style="width: 300px; margin-bottom: 10px"
          ></el-input>
          <div>
            <el-radio v-model="item.is_show_pc" :label="1">PC端显示</el-radio>
            <el-radio v-model="item.is_show_pc" :label="0">PC端不显示</el-radio>
          </div>
          <div>
            <el-radio v-model="item.is_show_app" :label="1"
              >手机端显示</el-radio
            >
            <el-radio v-model="item.is_show_app" :label="0"
              >手机端不显示</el-radio
            >
          </div>
        </el-form-item>
      </div>

      <el-form-item>
        <el-button type="primary" @click="onClickForm">确认</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      menu_list: [],
      form_info: {},
    };
  },
  mounted() {
    this.getMenuData();
  },
  methods: {
    getMenuData() {
      this.$http.getSiteMenuConfig().then((res) => {
        if (res.status === 200) {
          this.menu_list = res.data;
        }
      });
    },
    onClickForm() {
      let arr = [];
      this.menu_list.forEach((item) => {
        arr.push(item);
      });
      let arr2 = JSON.stringify(arr);
      let form = {
        menus: arr2,
      };
      this.$http.setSiteMenuConfig(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          // this.$goPath("/crm_customer_business");
          location.reload()
          // this.getMenuData();
        }
      });
    },
  },
};
</script>

<style></style>
