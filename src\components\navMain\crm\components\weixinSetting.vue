<template>
  <div>
    <el-button
      :class="{ active: isclass === 1 }"
      type="primary"
      @click="authorization"
      >授权小程序</el-button
    >
    <el-button type="success" @click="getQrCode">获取小程序二维码</el-button>
    <el-button type="primary" @click="authorizationnew">更新小程序授权</el-button>

    <el-tabs type="border-card" style="margin-top: 6px">
      <el-tab-pane label="查看小程序信息">
        <el-row>
          <el-col :span="12">
            <p style="background: #eee; padding: 10px 20px">微信认证信息</p>
            <el-form label-width="100px">
              <el-form-item label="小程序ID">
                <el-input v-model="mini_info.appid" :disabled="true"></el-input>
              </el-form-item>
              <el-form-item label="账号类型">
                <el-input
                  v-model="mini_info.account_type"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="主体类型" v-if="mini_info.principal_type">
                <el-input
                  v-model="mini_info.principal_type"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="主体名称" v-if="mini_info.principal_name">
                <el-input
                  v-model="mini_info.principal_name"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="实名认证状态"
                v-if="mini_info.realname_status"
              >
                <el-input
                  v-model="mini_info.realname_status"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="11" style="margin-left: 20px">
            <p style="background: #eee; padding: 10px 20px">微信认证信息</p>
            <el-form v-if="mini_info.wx_verify_info">
              <el-form-item
                label="是否是资质认证，若是，拥有微信认证相关的权限"
              >
                <el-input
                  v-model="mini_info.wx_verify_info.qualification_verify"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="是否名称认证">
                <el-input
                  v-model="mini_info.wx_verify_info.naming_verify"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="是否需要年审"
                v-if="mini_info.wx_verify_info.qualification_verify"
              >
                <el-input
                  v-model="mini_info.wx_verify_info.annual_review"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="年审开始时间"
                v-if="mini_info.wx_verify_info.qualification_verify"
              >
                <el-input
                  v-model="mini_info.wx_verify_info.annual_review_begin_time"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="年审结束时间"
                v-if="mini_info.wx_verify_info.qualification_verify"
              >
                <el-input
                  v-model="mini_info.wx_verify_info.annual_review_end_time"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <p style="background: #eee; padding: 10px 20px">功能介绍信息</p>
            <el-form v-if="mini_info.signature_info">
              <el-form-item label="小程序签名">
                <el-input
                  v-model="mini_info.signature_info.signature"
                  :disabled="true"
                  type="textarea"
                  :rows="4"
                ></el-input>
              </el-form-item>
              <el-form-item label="小程序名称">
                <el-input
                  v-model="mini_info.nickname"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="功能介绍已使用修改次数（本月）">
                <el-input
                  v-model="mini_info.signature_info.modify_used_count"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="功能介绍修改次数总额度（本月）">
                <el-input
                  v-model="mini_info.signature_info.modify_quota"
                  :disabled="true"
                ></el-input>
              </el-form-item> </el-form
          ></el-col>
          <el-col :span="11" style="margin-left: 20px">
            <p style="background: #eee; padding: 10px 20px">头像信息</p>
            <el-form v-if="mini_info.head_image_info">
              <el-form-item label="头像">
                <img
                  width="100px"
                  :src="mini_info.head_image_info.head_image_url"
                  alt
                />
              </el-form-item>
              <el-form-item label="头像已使用修改次数（今年）">
                <el-input
                  v-model="mini_info.head_image_info.modify_used_count"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="头像修改次数总额度（今年）">
                <el-input
                  v-model="mini_info.head_image_info.modify_quota"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="配置小程序名称">
        <div style="padding: 5px; margin: 8px 0">
          <p style="background: #eee">配置小程序名称：</p>
          <el-tag type="warning" style="margin: 10px 0"
            >说明1：小程序发布前可改名的次数为2，当改名次数上限后仍需继续改名时需要重新发起微信认证改名。</el-tag
          ><br />
          <el-tag type="warning" style="margin: 10px 0"
            >说明2：组织号必填：组织机构代码证或营业执照
          </el-tag>

          <div class="row div" style="align-items: center">
            <el-input
              style="width: 330px"
              @change="miniNameAudit"
              placeholder="输入小程序状态码查询审核状态"
              v-model="mini_program_audit_id"
            >
              <el-tooltip
                slot="append"
                class="item"
                effect="dark"
                content="提交小程序名称成功后，请复制状态码粘贴此处，进行审核状态查询。注意：请手动保存状态码！"
                placement="right-end"
              >
                <i style="font-size: 30px" class="el-icon-warning-outline"></i>
              </el-tooltip>
            </el-input>
          </div>
          <el-form
            label-width="130px"
            :model="mini_program_form"
            label-position="left"
            style="border: 1px solid #eee; padding: 0 10px"
          >
            <el-form-item label="小程序名称：">
              <el-input
                style="width: 300px"
                v-model="mini_program_form.nick_name"
                placeholder="请输入小程序名称"
              ></el-input>
              <el-button type="primary" @click="detectionMini"
                >检测小程序名称</el-button
              >
            </el-form-item>
            <el-form-item label="组织号必填：">
              <el-upload
                :headers="myHeader"
                :action="myMaterialPath"
                :data="dataObj"
                name="media"
                :on-success="handleSuccesslicense"
                list-type="picture-card"
                :on-preview="handlePreviewlicense"
                :on-remove="handleRemovelicense"
                :limit="1"
              >
                <i class="el-icon-plus"></i
              ></el-upload>
              <el-dialog :visible.sync="licenseVisible">
                <img width="100px" :src="licenseImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item label="其他证明材料一：">
              <el-upload
                :headers="myHeader"
                :action="myMaterialPath"
                :data="dataObj"
                name="media"
                :on-success="handleSuccessStuff1"
                list-type="picture-card"
                :on-preview="handlePreviewStuff1"
                :on-remove="handleRemoveStuff1"
                :limit="1"
              >
                <i class="el-icon-plus"></i
              ></el-upload>
              <el-dialog :visible.sync="stuff1Visible">
                <img width="100px" :src="stuff1ImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item label="其他证明材料二：">
              <el-upload
                :headers="myHeader"
                :action="myMaterialPath"
                :data="dataObj"
                name="media"
                :on-success="handleSuccessStuff2"
                list-type="picture-card"
                :on-preview="handlePreviewStuff2"
                :on-remove="handleRemoveStuff2"
                :limit="1"
              >
                <i class="el-icon-plus"></i
              ></el-upload>
              <el-dialog :visible.sync="stuff2Visible">
                <img width="100px" :src="stuff2ImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item label="其他证明材料三：">
              <el-upload
                :headers="myHeader"
                :action="myMaterialPath"
                :data="dataObj"
                name="media"
                :on-success="handleSuccessStuff3"
                list-type="picture-card"
                :on-preview="handlePreviewStuff3"
                :on-remove="handleRemoveStuff3"
                :limit="1"
              >
                <i class="el-icon-plus"></i
              ></el-upload>
              <el-dialog :visible.sync="stuff3Visible">
                <img width="100px" :src="stuff3ImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item label="其他证明材料四：">
              <el-upload
                :headers="myHeader"
                :action="myMaterialPath"
                :data="dataObj"
                name="media"
                :on-success="handleSuccessStuff4"
                list-type="picture-card"
                :on-preview="handlePreviewStuff4"
                :on-remove="handleRemoveStuff4"
                :limit="1"
              >
                <i class="el-icon-plus"></i
              ></el-upload>
              <el-dialog :visible.sync="stuff4Visible">
                <img width="100px" :src="stuff4ImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item label="其他证明材料五：">
              <el-upload
                :headers="myHeader"
                :action="myMaterialPath"
                :data="dataObj"
                name="media"
                :on-success="handleSuccessStuff5"
                list-type="picture-card"
                :on-preview="handlePreviewStuff5"
                :on-remove="handleRemoveStuff5"
                :limit="1"
              >
                <i class="el-icon-plus"></i
              ></el-upload>
              <el-dialog :visible.sync="stuff5Visible">
                <img width="100px" :src="stuff5ImageUrl" alt />
              </el-dialog>
            </el-form-item>
          </el-form>
          <div class="div row btn-box" style="margin: 4px 0">
            <el-button type="primary" @click="submitMini">提交</el-button>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="设置小程序服务器域名">
        <div style="padding: 5px; margin: 8px 0" v-if="!categories">
          <p style="background: #eee">设置小程序服务器域名：</p>
        </div>
        <el-form style="100%" label-width="200px">
          <!-- <el-form-item>
                  <el-tag type="success" style="margin:10px 0;height:auto">
                    1：初次配置小程序操作类型为'添加'，之后配置类型为'覆盖'，
                    <br />
                    2：多个域名请用英文逗号分割，
                    <br />
                    3：配置小程序服务器合法的域名，点击提交上传配置，
                  </el-tag>
                </el-form-item> -->
          <el-form-item label="操作类型：">
            <el-radio v-model="domain.action" label="add">添加</el-radio>
            <el-radio v-model="domain.action" label="set" disabled
              >覆盖</el-radio
            >
            <el-radio v-model="domain.action" label="delete" disabled
              >删除</el-radio
            >
            <el-radio v-model="domain.action" label="get" disabled
              >获取</el-radio
            >
          </el-form-item>
          <el-form-item label="request 合法域名：">
            <el-input
              type="textarea"
              :rows="5"
              disabled
              style="width: 300px"
              v-model="domain.requestdomain"
            ></el-input>
          </el-form-item>
          <el-form-item label="socket 合法域名：">
            <el-input
              type="textarea"
              :rows="5"
              disabled
              style="width: 300px"
              v-model="domain.wsrequestdomain"
            ></el-input>
          </el-form-item>
          <el-form-item label="uploadFile 合法域名：">
            <el-input
              type="textarea"
              :rows="5"
              disabled
              style="width: 300px"
              v-model="domain.uploaddomain"
            ></el-input>
          </el-form-item>
          <el-form-item label="downloadFile 合法域名：">
            <el-input
              type="textarea"
              :rows="5"
              style="width: 300px"
              disabled
              v-model="domain.downloaddomain"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="createDomain" type="primary">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="设置小程序业务域名">
        <div style="padding: 5px; margin: 8px 0" v-if="!categories">
          <p style="background: #eee">设置小程序业务域名：</p>
        </div>
        <el-form label-width="200px">
          <!-- <el-form-item>
                  <el-tag type="success" style="margin:10px 0;height:auto">
                    1：初次配置小程序操作类型为'添加'，之后配置类型为'覆盖'，
                    <br />
                    2：多个域名请用英文逗号分割，
                    <br />
                    3：配置小程序服务器合法的业务域名，点击提交上传配置，
                  </el-tag>
                </el-form-item> -->
          <el-form-item label="操作类型：">
            <el-radio v-model="webview_domain.action" label="add"
              >添加</el-radio
            >
            <el-radio v-model="webview_domain.action" label="set"
              >覆盖</el-radio
            >
            <el-radio v-model="webview_domain.action" disabled label="delete"
              >删除</el-radio
            >
            <el-radio v-model="webview_domain.action" disabled label="get"
              >获取</el-radio
            >
          </el-form-item>
          <el-form-item label="小程序业务域名：">
            <el-input
              style="width: 300px"
              type="textarea"
              :rows="5"
              v-model="webview_domain.webviewdomain"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="createWebviewDomain" type="primary"
              >提交</el-button
            ></el-form-item
          >
        </el-form>
      </el-tab-pane>
      <!-- <el-tab-pane label="小程序是否可以被搜索">
              <div style="padding:5px ;margin:8px 0">
                <p style="background:#eee">小程序是否可以被搜索：</p>
                <el-tag type="warning" style="margin:10px 0"
                  >说明：微信中小程序是否可被搜索</el-tag
                >
                <div>
                  <el-switch
                    @change="changeSwitch"
                    v-model="switch_value"
                    :active-value="0"
                    :inactive-value="1"
                  ></el-switch>
                </div>
              </div>
            </el-tab-pane> -->
      <el-tab-pane label="配置小程序头像">
        <div style="padding: 5px; margin: 8px 0">
          <p style="background: #eee">配置小程序头像：</p>
          <el-tag type="warning" style="margin: 10px 0"
            >小程序头像一年可修改5次</el-tag
          >
          <div class="div row">
            <el-upload
              style="margin: 8px 0"
              :headers="myHeader"
              name="media"
              :action="myMaterialPath"
              :data="dataObj"
              :on-success="handleSuccessnews"
              list-type="picture-card"
              :on-preview="handlePictureCardPreviewnews"
              :on-remove="handleRemovenews"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="newsVisible">
              <img width="100px" :src="newsImageUrl" alt />
            </el-dialog>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="修改小程序功能介绍">
        <div style="padding: 5px; margin: 8px 0">
          <p style="background: #eee">修改小程序功能介绍：</p>
          <div>
            <el-tag type="warning" style="margin: 10px 0">
              一个月中可以修改5次功能介绍，请注意修改次数
            </el-tag>
          </div>
          <div>
            <el-tag type="warning" style="margin-bottom: 10px">
              错误码：53201（功能介绍内容命中黑名单关键字）53200（本月功能介绍修改次数已用完）
            </el-tag>
          </div>
          <el-input
            style="margin: 8px 0"
            type="textarea"
            rows="8"
            v-model="signature"
            placeholder="请输入小程序功能介绍"
          ></el-input>
          <div class="div row btn-box" style="margin: 4px 0">
            <el-button type="primary" @click="submitSignature">提交</el-button>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="修改小程序类目">
        <div style="padding: 5px; margin: 8px 0" v-if="!categories">
          <p style="background: #eee">修改小程序类目：</p>
          <el-button type="primary" @click="addCategories"
            >点击生成类目</el-button
          >
        </div>
        <div style="padding: 5px; margin: 8px 0" v-else>
          <p style="background: #eee">修改小程序类目：</p>
          <p>
            更改周期内可以添加的类目次数：{{
              categories.limit
            }}，在这个周期内，还可以添加类目的次数：{{
              categories.quota
            }}，最多可以设置类目数量：{{ categories.category_limit }}
          </p>
          <p v-if="categories.first">
            <i>已设置类目：</i>
            <i
              >序号：{{ categories.first }}，名称：{{
                categories.first_name
              }}</i
            >
          </p>
          <p categories.second>
            <i>已设置类目：</i>
            <i
              >序号：{{ categories.second }}，名称：{{
                categories.second_name
              }}</i
            >
          </p>
          <p style="background: #eee" v-if="categories.audit_reason">
            {{ categories.audit_reason }}
          </p>
          <p v-else style="background: #eee">
            审核状态：{{
              categories.audit_status === 1
                ? "审核中"
                : categories.audit_status === 2
                ? "审核不通过"
                : "审核通过"
            }}
            <i></i>
          </p>
        </div>
      </el-tab-pane>
      <el-tab-pane label="违规申诉">
        <div class="table_div">
          <el-table
            height="600"
            max-height="850"
            :data="tableDataList"
            border
            style="width: 100%"
          >
            <el-table-column prop="create_time" label="处罚时间" width="180">
            </el-table-column>
            <el-table-column
              prop="illegal_record_id"
              label="违规ID"
              width="180"
            >
            </el-table-column>
            <el-table-column prop="rule_name" label="违规规则名称">
            </el-table-column>
            <el-table-column prop="rule_url" label="违规链接">
            </el-table-column>
            <el-table-column prop="illegal_reason" label="违规原因">
            </el-table-column>
            <el-table-column prop="illegal_content" label="违规内容">
            </el-table-column>
          </el-table>
        </div>
        <div class="table_pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      :visible.sync="showQrcode"
      style="margin-left: 20px"
      width="1300px"
      title="小程序二维码"
      @close="closeWximg"
    >
      <p style="text-align: center; font-size: 25px">小程序二维码</p>
      <div style="text-align: center; margin-top: 10px">
        <el-image
          :style="imgSize"
          :src="appQrcode"
          fit="fit"
          ref="img_wxs"
        ></el-image>
      </div>
      <!-- <el-button slot="reference" type="success" @click="getQrCode"
        >获取小程序二维码</el-button
      > -->

      <div style="width: 700px; margin-left: 200px; margin-top: 50px">
        <p>其他尺寸(单位/px)：</p>
        <el-input
          v-model="change_imgSize"
          style="float: left; width: 500px; margin: 8px 0"
          placeholder="最大不得超过1280(单位/px)"
        ></el-input>
        <el-button
          type="primary"
          style="margin-top: 10px; margin-left: 18px"
          @click="changeImgsize"
          >确定</el-button
        >
        <el-button
          type="success"
          style="margin-top: 10px; float: right"
          @click="downQRcode"
          >下载</el-button
        >
      </div>
      <div style="width: 700px; margin-left: 200px; margin-top: 20px">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column
            prop="leng_side"
            label="小程序二维码边长（cm）"
            width="210"
          >
          </el-table-column>
          <el-table-column prop="leng_pixel" label="建议像素（px）" width="180">
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scopes">
              <el-button type="primary" size="mini" @click="preview(scopes)"
                >预览</el-button
              >
              <el-button @click="down(scopes)" size="mini" type="success">
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      switch_value: 0,
      categories: "",
      domain: {
        action: "add",
        requestdomain:
          "https://cloud.tfcs.cn,https://wai.tfcs.cn,https://yun.tfcs.cn,https://apis.map.qq.com",
        wsrequestdomain: "https://yun.tfcs.cn, wss://imcloud.tengfangyun.com",
        uploaddomain: "https://cloud.tfcs.cn,https://wai.tfcs.cn,https://yun.tfcs.cn",
        downloaddomain:
          "https://cloud.tfcs.cn,https://wai.tfcs.cn,https://yun.tfcs.cn,https://img.tfcs.cn",
      },
      webview_domain: {
        action: "add",
        webviewdomain: "https://yun.tfcs.cn",
      },
      // 处理服务器域名
      requestdomain: "",
      wsrequestdomain: "",
      uploaddomain: "",
      downloaddomain: "",
      // 业务域名
      webviewdomain: "",
      mini_info: {},
      private_config: {}, // 小程序私有配置
      website_id: localStorage.getItem("website_id"),
      // 小程序授权
      dialogMiniprogram: false,
      mini_program_form: {
        nick_name: "",
        license: "", //组织号必填
        naming_other_stuff_1: "", //其他证明材料：mediaid
        naming_other_stuff_2: "",
        naming_other_stuff_3: "",
        naming_other_stuff_4: "",
        naming_other_stuff_5: "",
      },
      isclass: 0,
      //小程序尺寸
      imgSize: "width:180px;height:180px",
      change_imgSize: "",
      appQrcode: "",
      tableData: [
        {
          leng_side: "8cm",
          leng_pixel: "258",
        },
        {
          leng_side: "12cm",
          leng_pixel: "344",
        },
        {
          leng_side: "15cm",
          leng_pixel: "430",
        },
        {
          leng_side: "30cm",
          leng_pixel: "860",
        },
        {
          leng_side: "50cm",
          leng_pixel: "1280",
        },
      ],
      tableDataList: [
        {
          create_time: "2016-05-02",
          illegal_record_id: "1",
          rule_name: "上海市普陀区金沙江路 1518 弄",
          rule_url: "1",
          // rule_name: "2",
          illegal_reason: "3",
          illegal_content: "4",
        },
      ],
      currentPage4: 4,
      mini_program_audit_id: "",
      dataObj: {
        type: "image",
        access_token_type: 1,
      },
      licenseVisible: false,
      licenseImageUrl: false,
      stuff1Visible: false,
      stuff1ImageUrl: "",
      stuff2Visible: false,
      stuff2ImageUrl: "",
      stuff3Visible: false,
      stuff3ImageUrl: "",
      stuff4Visible: false,
      stuff4ImageUrl: "",
      stuff5Visible: false,
      stuff5ImageUrl: "",
      signature: "",
      newsVisible: false,
      newsImageUrl: "",
      form: {
        // 开启分销报备开关
        share_report_entrance: 0,
        // name: "",
        logo: "",
        withdraw_fee: 0.0,
        wx_pub_qr_code: "",
        wx_mini_program_qr_code: "",
        default_deal_big_news: "",
        invite_description: "",
        map_lat: "",
        map_long: "",
        share_img: "",
        login_display_brokerage_rule: "",
        distribution_brokerage_type: 0,
        distribution_brokerage_value: "",
        fill_customer_phone_category: "4",
        customer_reported_user_category: "1",
        reported_rule: "",
        reported_rule_category: "1",
        reported_created_at_format_category: "Y-m-d H:i:s",
        // 强制客户设置姓名开关：
        client_user_force_setting_name: true,
        // 开启实名认证开关
        open_real_name: true,
        // 楼盘是否显示佣金规则
        brokerage_rule_category: 1,
        visit_time_format_category: "1",
        reported_not_audit_invalid_category: "1",
        build_category_show_category: "",
        reported_go_with: "1",
        force_login: 0,
        estate_purchase_service_wx: "", //买房交流群号
        estate_purchase_wx_group_qrcode: "", // 买房交流群二维码
        theme: "", // 小程序主题
        max_select_project_total: "5",
      },
      showQrcode: false,
    };
  },
  created() {
    this.website_id = this.$route.query.website_id;
    this.getWxInfo();
    this.getWebsiteinfor();
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
    // map_lng_lat() {
    //   if (this.lat && this.lng) {
    //     return this.lat + "," + this.lng;
    //   } else {
    //     return "";
    //   }
    // },
    myMaterialPath() {
      return "https://yun.tfcs.cn/api/admin/wx_open/public/material/new/temp";
    },
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    getWebsiteinfor() {
      this.$http.getWebsite(this.website_id).then((res) => {
        if (res.status === 200) {
          this.form = res.data;
        }
      });
    },
    getWxInfo() {
      this.$http.queryXiaoApp().then((res) => {
        if (res.status === 200) {
          if (res.data.id === 0 && res.data.updated_at === "") {
            this.$message({
              message: "暂未授权小程序",
              type: "warning",
            });
          } else {
            this.configuration();
          }
        }
      });
    },
    etLocalTime(nS) {
      return new Date(parseInt(nS) * 1000)
        .toLocaleString()
        .replace(/:\d{1,2}$/, " ");
    },
    configuration() {
      this.dialogMiniprogram = true;
      // 查询小程序是否可以被搜索
      this.$http.querySearchStatus().then((res) => {
        if (res.status === 200) {
          this.switch_value = res.data.status;
        }
      });
      // 查询小程序类目分类
      this.$http.getCategory().then((res) => {
        if (res.status === 200) {
          this.categories = res.data.categories[0];
          this.categories.limit = res.data.limit;
          this.categories.quota = res.data.quota;
          this.categories.category_limit = res.data.category_limit;
        }
      });
      // 查询小程序基本信息
      this.$http.queryMiniInfo().then((res) => {
        if (res.status === 200) {
          this.mini_info = res.data;
          // 匹配数据类型
          switch (this.mini_info.account_type) {
            case 1:
              this.mini_info.account_type = "订阅号";
              break;
            case 2:
              this.mini_info.account_type = "服务号";
              break;
            default:
              this.mini_info.account_type = "小程序";
              break;
          }
          switch (this.mini_info.principal_type) {
            case 0:
              this.mini_info.principal_type = "个人";
              break;
            case 1:
              this.mini_info.principal_type = "企业";
              break;
            case 2:
              this.mini_info.principal_type = "媒体";
              break;
            case 3:
              this.mini_info.principal_type = "政府";
              break;
            default:
              this.mini_info.principal_type = "其他组织";
              break;
          }
          switch (this.mini_info.realname_status) {
            case 1:
              this.mini_info.realname_status = "实名验证成功";
              break;
            case 2:
              this.mini_info.realname_status = "实名验证中";
              break;
            default:
              this.mini_info.realname_status = "实名验证失败";
              break;
          }
          // 时间戳转换
          this.mini_info.wx_verify_info.annual_review_begin_time =
            this.etLocalTime(
              this.mini_info.wx_verify_info.annual_review_begin_time
            );
          this.mini_info.wx_verify_info.annual_review_end_time =
            this.etLocalTime(
              this.mini_info.wx_verify_info.annual_review_end_time
            );
          // 匹配数据类型
          switch (this.mini_info.wx_verify_info.qualification_verify) {
            case true:
              this.mini_info.wx_verify_info.qualification_verify = "是";
              break;
            default:
              this.mini_info.wx_verify_info.qualification_verify = "否";
              break;
          }
          switch (this.mini_info.wx_verify_info.naming_verify) {
            case true:
              this.mini_info.wx_verify_info.naming_verify = "是";
              break;
            default:
              this.mini_info.wx_verify_info.naming_verify = "否";
              break;
          }
          switch (this.mini_info.wx_verify_info.annual_review) {
            case true:
              this.mini_info.wx_verify_info.annual_review = "是";
              break;
            default:
              this.mini_info.wx_verify_info.annual_review = "否";
              break;
          }
        } else {
          this.mini_info = "";
        }
      });
    },
    authorization() {
      let url = encodeURIComponent(
        `https://yun.tfcs.cn/admin/#/loading?website_id=${this.website_id}&is_loading=1`
      );
      if (this.mini_info.errcode === 0) {
        this.$message({
          message: "您已授权小程序请勿重复授权",
          type: "warning",
        });
      } else {
        this.$http.authorization({ url }).then((res) => {
          if (res.status === 200) {
            window.location.href = res.data.url;
          }
        });
      }
    },
    authorizationnew(){
      let redirect_uri = encodeURIComponent(
        `https://yun.tfcs.cn/admin/#/newloading?website_id=${this.website_id}`
      );
        this.$http.Newauthorization({ redirect_uri }).then((res) => {
          if (res.status === 200) {
            window.open(res.data.url, '_blank');
          }
        });
        // const paramName = "123";
        // const paramValue = "456";
        // const queryString = `auth_code=${encodeURIComponent(paramName)}&expires_in=${encodeURIComponent(paramValue)}`;
        // const path = `https://yun.tfcs.cn/admin/?website_id=${this.website_id}#/newloading?${queryString}`;
        // // this.$goPath(path);
        // window.open(path, '_blank');
    },
    closeWximg() {
      this.imgSize = "width:180px;height:180px";
      this.change_imgSize = "";
    },
    getQrCode() {
      this.showQrcode = true;
      var path = "";
      if (this.form.open_mini_build_program_wx == 1) {
        if (this.form.website_mode_category == 0) {
          path = "index/index";
        }
        if (this.form.website_mode_category == 1) {
          path = "only_build/pages/index/index";
        }
        if (this.form.website_mode_category == 2) {
          path = "weifangchan/pages/index/index";
        }
        if (this.form.website_mode_category == 3) {
          path = "index/index";
        }
      }
      if (this.form.open_mini_erp_program_wx == 1) {
        path = "fenxiao_new/index/index";
      }

      this.$http.getAppQrcode({ path: path }).then((res) => {
        if (res.status === 200) {
          this.appQrcode = `data: image/jpeg;base64,${btoa(
            new Uint8Array(res.data).reduce(
              (data, byte) => data + String.fromCharCode(byte),
              ""
            )
          )}`;
        }
      });
    },
    changeImgsize() {
      if (this.change_imgSize * 1 > 1280) {
        this.change_imgSize = 1280;
      }
      this.imgSize = `width:${this.change_imgSize}px;height:${this.change_imgSize}px`;
      var path = "";
      if (this.form.open_mini_build_program_wx == 1) {
        if (this.form.website_mode_category == 0) {
          path = "index/index";
        }
        if (this.form.website_mode_category == 1) {
          path = "only_build/pages/index/index";
        }
        if (this.form.website_mode_category == 2) {
          path = "weifangchan/pages/index/index";
        }
        if (this.form.website_mode_category == 3) {
          path = "index/index";
        }
      }
      if (this.form.open_mini_erp_program_wx == 1) {
        path = "fenxiao_new/index/index";
      }
      this.$http
        .getAppQrcode({ path: path, width: this.change_imgSize })
        .then((res) => {
          if (res.status === 200) {
            this.appQrcode = `data: image/jpeg;base64,${btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            )}`;
          }
        });
    },
    //下载二维码
    downQRcode() {
      console.log(this.change_imgSize);
      if (this.change_imgSize === "") {
        this.$message({
          message: "请输入下载尺寸",
          type: "warning",
        });
      } else {
        var path = "";
        if (this.form.open_mini_build_program_wx == 1) {
          if (this.form.website_mode_category == 0) {
            path = "index/index";
          }
          if (this.form.website_mode_category == 1) {
            path = "only_build/pages/index/index";
          }
          if (this.form.website_mode_category == 2) {
            path = "weifangchan/pages/index/index";
          }
          if (this.form.website_mode_category == 3) {
            path = "index/index";
          }
        }
        if (this.form.open_mini_erp_program_wx == 1) {
          path = "fenxiao_new/index/index";
        }
        this.$http
          .getAppQrcode({ path: path, width: this.change_imgSize * 1 })
          .then((res) => {
            if (res.status === 200) {
              this.appQrcode = `data: image/jpeg;base64,${btoa(
                new Uint8Array(res.data).reduce(
                  (data, byte) => data + String.fromCharCode(byte),
                  ""
                )
              )}`;
              let a = document.createElement("a");
              a.download = `小程序二维码（${this.change_imgSize}px）`;
              a.href = this.appQrcode;
              a.click();
            }
          });
      }
    },
    // 查询小程序名称审核状态
    miniNameAudit(e) {
      if (!e) {
        this.$message.error("请输入状态码！");
        return;
      }
      this.$http.miniNameAuditStatus({ audit_id: e }).then((res) => {
        if (res.status === 200) {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: ` <div>
                          <p>返回码（error）：${res.data.errcode};</p>
                          <p>错误信息（errmsg）：${res.data.errmsg};</p>
                          <p>审核昵称（nickname）：${res.data.nickname};</p>
                          <p>审核状态（audit_stat）： ${
                            res.data.audit_stat === 1
                              ? "审核中"
                              : res.data.audit_stat === 2
                              ? "审核失败"
                              : "审核成功"
                          };</p>
                          <p>失败原因（fail_reason）： ${
                            res.data.fail_reason
                          };</p>
                      </div>`,
            type: "success",
            duration: 0,
            showClose: true,
          });
        }
      });
    },
    detectionMini() {
      if (!this.mini_program_form.nick_name) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http
        .detectionMini({
          nick_name: this.mini_program_form.nick_name,
        })
        .then((res) => {
          if (res.status === 200) {
            if (res.data.wording) {
              this.$message({
                message: res.data.wording,
                type: "warning",
                duration: 0,
                showClose: true,
              });
            } else {
              this.$message({
                message: "提交成功",
                type: "success",
              });
            }
          }
        });
    },
    handleSuccesslicense(res) {
      if (res.media_id) {
        this.mini_program_form.license = res.media_id;
      }
    },
    handlePreviewlicense(file) {
      this.licenseVisible = true;
      this.licenseImageUrl = file.response.url;
    },
    handleRemovelicense() {
      this.mini_program_form.license = "";
    },

    handleSuccessStuff1(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_1 = res.media_id;
      }
    },
    handlePreviewStuff1(file) {
      this.stuff1Visible = true;
      this.stuff1ImageUrl = file.response.url;
    },
    handleRemoveStuff1() {
      this.mini_program_form.naming_other_stuff_1 = "";
    },
    handleSuccessStuff2(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_2 = res.media_id;
      }
    },
    handlePreviewStuff2(file) {
      this.stuff2Visible = true;
      this.stuff2ImageUrl = file.response.url;
    },
    handleRemoveStuff2() {
      this.mini_program_form.naming_other_stuff_2 = "";
    },
    handleSuccessStuff3(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_3 = res.media_id;
      }
    },
    handlePreviewStuff3(file) {
      this.stuff3Visible = true;
      this.stuff3ImageUrl = file.response.url;
    },
    handleRemoveStuff3() {
      this.mini_program_form.naming_other_stuff_3 = "";
    },
    handleSuccessStuff4(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_4 = res.media_id;
      }
    },
    handlePreviewStuff4(file) {
      this.stuff4Visible = true;
      this.stuff4ImageUrl = file.response.url;
    },
    handleRemoveStuff4() {
      this.mini_program_form.naming_other_stuff_4 = "";
    },

    handleSuccessStuff5(res) {
      if (res.media_id) {
        this.mini_program_form.naming_other_stuff_5 = res.media_id;
      }
    },
    handlePreviewStuff5(file) {
      this.stuff5Visible = true;
      this.stuff5ImageUrl = file.response.url;
    },
    handleRemoveStuff5() {
      this.mini_program_form.naming_other_stuff_5 = "";
    },
    submitMini() {
      if (!this.mini_program_form.nick_name) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http.submitMini(this.mini_program_form).then((res) => {
        if (res.status === 200) {
          var audit_id = res.data.audit_id;
          this.$message({
            message:
              "提交成功" +
              (audit_id
                ? ",如需查询名称审核状态请复制状态码：" + audit_id
                : ""),
            type: "success",
            showClose: true,
            duration: 0,
          });
        }
      });
    },
    addCategories() {
      this.$http
        .addCategories({
          first: 135,
          second: 142,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "设置成功",
              type: "success",
            });
            this.$http.getCategory().then((res) => {
              if (res.status === 200) {
                this.categories = res.data.categories[0];
                this.categories.limit = res.data.limit;
                this.categories.quota = res.data.quota;
                this.categories.category_limit = res.data.category_limit;
              }
            });
          }
        });
    },
    submitSignature() {
      if (!this.signature) {
        this.$message({
          message: "请输入内容",
          type: "error",
        });
        return;
      }
      this.$http
        .submitSignature({
          signature: this.signature,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "上传成功",
              type: "success",
            });
          }
        });
    },
    createDomain() {
      let form = Object.assign({}, this.domain);
      form.requestdomain = form.requestdomain.split(",");
      form.wsrequestdomain = form.wsrequestdomain.split(",");
      form.uploaddomain = form.uploaddomain.split(",");
      form.downloaddomain = form.downloaddomain.split(",");
      this.$http.createDomain(form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
        }
      });
    },
    createWebviewDomain() {
      let form = Object.assign({}, this.webview_domain);
      form.webviewdomain = form.webviewdomain.split(",");
      this.$http.createWebviewDomain(form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
        }
      });
    },
    // 小程序授权
    // 缩略图
    handleRemovenews(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreviewnews(file) {
      this.newsVisible = true;
      this.newsImageUrl = file.response.url;
    },
    handleSuccessnews(response) {
      if (response.media_id) {
        this.media_id = response.media_id;
        this.$http
          .modifyAvatar({
            head_img_media_id: this.media_id,
            x1: "0",
            y1: "0",
            x2: "1",
            y2: "1",
          })
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "上传成功",
                type: "success",
              });
            }
          });
      }
    },
    //不同尺寸二维码图片预览
    preview(e) {
      this.imgSize = `width:${
        this.tableData[e.$index].leng_pixel * 1
      }px;height:${this.tableData[e.$index].leng_pixel * 1}px`;
      var path = "";
      if (this.form.open_mini_build_program_wx == 1) {
        if (this.form.website_mode_category == 0) {
          path = "index/index";
        }
        if (this.form.website_mode_category == 1) {
          path = "only_build/pages/index/index";
        }
        if (this.form.website_mode_category == 2) {
          path = "weifangchan/pages/index/index";
        }
        if (this.form.website_mode_category == 3) {
          path = "index/index";
        }
      }
      if (this.form.open_mini_erp_program_wx == 1) {
        path = "fenxiao_new/index/index";
      }
      this.$http
        .getAppQrcode({
          path: path,
          width: this.tableData[e.$index].leng_pixel * 1,
        })
        .then((res) => {
          if (res.status === 200) {
            this.appQrcode = `data: image/jpeg;base64,${btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            )}`;
          }
        });
    },
    //下载不同尺寸的小程序二维码
    down(e) {
      var path = "";
      if (this.form.open_mini_build_program_wx == 1) {
        if (this.form.website_mode_category == 0) {
          path = "index/index";
        }
        if (this.form.website_mode_category == 1) {
          path = "only_build/pages/index/index";
        }
        if (this.form.website_mode_category == 2) {
          path = "weifangchan/pages/index/index";
        }
        if (this.form.website_mode_category == 3) {
          path = "index/index";
        }
      }
      if (this.form.open_mini_erp_program_wx == 1) {
        path = "fenxiao_new/index/index";
      }
      this.$http
        .getAppQrcode({
          path: path,
          width: this.tableData[e.$index].leng_pixel * 1,
        })
        .then((res) => {
          if (res.status === 200) {
            this.appQrcode = `data: image/jpeg;base64,${btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            )}`;
            let a = document.createElement("a");
            a.download = `小程序二维码（${this.tableData[e.$index].leng_side}/${
              this.tableData[e.$index].leng_pixel
            }px）`;
            a.href = this.appQrcode;
            a.click();
          }
        });
    },
  },
};
</script>

<style lang ="scss" scoped>
.el-form-item {
  background: #fff;
  padding: 10px;
}
.table_div {
  width: 100%;
  height: 600px;
}
.table_pagination {
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>