<template>
<div>
<el-dialog title="主播历史数据变更" :visible.sync="show" :close-on-click-modal="!submiting" width="600px">
    <div class="container">
        <div class="title">{{detail.name}}</div>

        <div class="account" v-for="(item,index) in detail.list" :key="index">
            <div class="account-item">
                <span class="label">{{detail.platformLabel}}号：</span>
                <span class="value">{{item.key}}</span>
            </div>
            <div class="account-item account-op-item">
                <div class="left">
                    <el-tooltip content="录入人正常设置为主播对应的成员" placement="top" effect="light">
                        <span class="label">
                            <i class="el-icon-info icon-info"></i> 当前规则设定录入人：
                        </span>
                    </el-tooltip>
                    <span class="value">
                        <template v-if="item.create_id">{{item.create_name}}</template>
                        <span class="opacity" v-else>未设置</span>
                    </span>
                </div>
                <div class="right">
                    <div class="success" v-if="item.done">
                        <i class="el-icon-check"></i> 处理完成<span v-if="item.doneCount">，共处理 {{item.doneCount}} 条</span>
                    </div>
                    <template v-else>
                        <div class="account-progress-wrapper" v-if="submitProgress.show && submitIndex == index">
                            <p>正在处理</p>
                            <el-progress :percentage="submitProgress.pct"></el-progress>
                        </div>
                        <el-button v-else size="small" type="primary" @click="submit(index)" :disabled="!item.create_id || submiting && submitIndex != index">确认变更</el-button>
                    </template>
                </div>
            </div>
            <div class="account-item">
                <span class="label">上次更新时间：</span>
                <span class="value">{{item.lastUpdateTime}}</span>
            </div>
        </div>

        <div class="warning">
            注意：本项仅用于系统历史数据同步，如初始化设置正确，可忽略。执行后与该帐号关联的留资推送，录入人将变更。
        </div>
    </div>
</el-dialog>
</div>
</template>

<script>
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect'
export default {
    components: {
        tMemberSelect
    },
    data(){
        return {
            show: false,        //dialog是否显示
            submiting: false,
            successFn: null,
            platformList: [
                { label: '抖音', value: 1 },
                { label: '快手', value: 2 },
                { label: '微信视频号', value: 4 },
                { label: '飞鱼', value: 5 },
                { label: '海豚知道', value: 3 },
            ],
            adminList: [],
            lastUpdateTimeRels: {},
            detail: {},
            submitIndex: -1,
            submitProgress: {
                show: false,
                pct: 0,
            },
        }
    },
    methods: {
        //获取并设置成员姓名
        async getAdminName(){
            if(!this.adminList.length){
                const res = await this.$http.getColleagueDetailsList().catch(e=>{});
                if(res.status == 200){
                    this.adminList = res.data || [];
                }
            }
            for(const item of this.detail.list){
                if(item.create_id){
                    let admin = this.adminList.find(e=>e.values == item.create_id);
                    item.create_name = admin ? admin.name : '';
                }
            }
        },
        //获取上次更新时间
        async getLastUpdateTime(){
            const dones = [];
            for(const item of this.detail.list){
                dones[item.key] = false;
                let i = 0, timer = setInterval(()=>{
                    if(dones[item.key]){
                        clearInterval(timer);
                    }else{
                        item.lastUpdateTime = ['.','.','.'].slice(0, ++i%4).join('');
                    }
                }, 300)
            }
            for(const item of this.detail.list){
                if(item.key && !this.lastUpdateTimeRels[item.key]){
                    const res = await this.$http.getCrmRepeatLog(item.key).catch(e=>{});
                    if(res && res.status == 200){
                        let data = (res.data || []).find(e=>e && e.key == item.key)
                        this.lastUpdateTimeRels[item.key] = data && data.created_at || '无'
                    }
                }
                dones[item.key] = true;
                item.lastUpdateTime = this.lastUpdateTimeRels[item.key] || '无'
            }
        },
        //列表项
        detailListPushItem(key, create_id){
            this.detail.list.push({
                key,
                create_id,          //成员ID 
                create_name: '',    //成员姓名
                done: false,        //是否处理完成
                doneCount: 0,        //处理完成数量
                lastUpdateTime: ''
            })
        },
        //打开弹窗
        open(data){
            const platform = this.platformList.find(e=>e.value == data.platform);
            this.detail = {
                name: data.name,
                platform: data.platform,
                platformLabel: platform ? platform.label : '',
                list: []
            }

            if(data.platform == 1){
                for(const item of data.list){
                    this.detailListPushItem(item.dy_id, item.create_id);
                }
            }else{
                this.detailListPushItem(data.key, data.create_id);
            }

            this.getAdminName();
            this.getLastUpdateTime();
            this.submitProgress.show = false;
            this.submiting = false;
            this.show = true;
            return this
        },
        //提交
        async submit(index){   
            const params = this.detail.list[index];
            this.submitIndex = index;
            this.submiting = true;
            let isFail = false, 
                isDone = false;
            setTimeout(async ()=>{
                this.submitProgress.pct = 0;
                this.submitProgress.show = true;
                while(this.submitProgress.show && this.submitProgress.pct < 100){
                    await this.$Utils.sleep(100);
                    let pct = this.submitProgress.pct + Math.ceil(Math.random()*5),
                        max = 99;
                    pct > max && (pct = max);
                    this.submitProgress.pct = pct;
                    if(isDone){
                        if(pct > max-5){
                            this.submitProgress.pct = 100;
                            await this.$Utils.sleep(600);
                            this.$message.success("操作成功");
                            this.submitProgress.show = false;
                            this.submitProgress.pct = 0;
                            this.submiting = false;
                            this.detail.list[index].done = true;
                        }
                    }else if(isFail){
                        this.submitProgress.show = false;
                        this.submiting = false;
                    }
                }
            })

            
            const res = await this.$http.crmRepeatCreateConfig({
                key: params.key,
                new_admin_id: params.create_id,
                platform: this.detail.platform,
            }).catch(e => {});
            if(res && res.status == 200){   
                isDone = true;
                this.detail.list[index].doneCount = res.data?.number || 0;
                this.lastUpdateTimeRels[params.key] = res.data?.time || '';
                //设置主播
                this.setAnchor(params.create_id);
            }
            isFail = !isDone;
        },
        //设置主播
        async setAnchor(admin_id){
            const res = await this.$http.crmSetAnchor({admin_id}).catch(e => {});
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
    }  
}
</script>
<style lang="scss" scoped>
.title{
    font-size: 16px;
}
.account{
    padding: 18px 0 6px;
    .account-op-item{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 30px;
    }
    .opacity{
        color: #9c9c9c;
        opacity: .7;
    }
    .icon-info{
        color: #b2b2b2;
    }
    .success{
        color: #67C23A;
    }
    .tip{
        display: inline-block;
        margin-top: 12px;
        padding: 6px 12px;
        color: #f60;
        border: 1px solid #f60;
        border-radius: 2px;
    }
    
}
.warning{
    color: #f60;
    padding-top: 16px
}
.account-progress-wrapper{
    display: flex;
    flex-direction: row;
    width: 240px;
    .el-progress{
        padding: 2px 0 0 12px;
        flex: 1;
    }
}
</style>