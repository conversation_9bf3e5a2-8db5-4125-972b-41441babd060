<template>
  <ol class="newmenu">
    <li class="m-list" v-for="i1 in menu" :key="i1.id">
      <dl class="m1-title">
        <dt class="m1__title">
          <img
            v-if="i1.extend_list.img"
            width="20px"
            height="20px"
            :src="i1.extend_list.img"
            alt=""
          />
          <i v-else :class="i1.extend_list.icon"></i>
          <span class="t-span">
            {{ i1.title }}
          </span>
        </dt>
        <dd class="m-item">
          <div
            @click="onClick(i2)"
            class="m-i-name"
            v-for="i2 in i1.children"
            :key="i2.id"
            :style="{
              width: isWidth ? '100%' : '40%',
            }"
          >
            <span class="m-i-name-span" :class="{ isactive: i2.id == isid }">
              {{
                i2.id == 37 && website_info.city_type == 2
                  ? "城市管理"
                  : i2.title
              }}
              <i v-if="i2.children.length > 0" class="el-icon-arrow-down"></i>
            </span>
            <myCollapse :isActive="is_collapse">
              <template v-slot:content>
                <div class="m-i3-box">
                  <dd
                    class="m-i3-name"
                    v-for="i3 in i2.children"
                    :key="i3.id"
                    @click="onClick(i3)"
                  >
                    <span
                      class="m-i-name-span"
                      :class="{ isactive: i3.id == isid }"
                    >
                      {{ i3.title }}
                    </span>
                  </dd>
                </div>
              </template>
            </myCollapse>
          </div>
        </dd>
      </dl>
    </li>
  </ol>
</template>

<script>
import myCollapse from "./navMain/crm/components/collapse";
import { mapState } from "vuex";
export default {
  props: {
    menu: Array,
    // 是否占满宽度
    isWidth: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    myCollapse,
  },
  computed: {
    ...mapState(["slideNavs", "website_info", "is_qywx_expire"]),
  },
  data() {
    return {
      isid: "",
      is_collapse: false,
    };
  },
  methods: {
    // type = 1 下级折叠
    onClick(item) {
      if (this.is_qywx_expire || this.$store.state.disableClick) {
        // 企业微信到期禁止点击事件  
        return;
      }
      // let auth_way = localStorage.getItem("auth_way"); // 测试来源
      // // 如果不是自建应用
      // if (auth_way != 2) {
      //   // 批量加好友，群活ma
      //   if (item.id === 214 || item.id === 189) {
      //     this.$message.error("企业微信第三方不支持");
      //     return;
      //   }
      // }
      if (item.children.length) {
        //   折叠面板
        this.is_collapse = !this.is_collapse;
      } else {
        let website_id =
          localStorage.getItem("website_id") || this.website_info.website_id;
        this.isid = item.id;
        this.is_collapse = false;
        let url = item.extend_list.type
          ? item.extend_list.componentName +
          `?website_id=${website_id}&type=${item.extend_list.type}`
          : item.extend_list.componentName + `?website_id=${website_id}`;
        this.$emit("onClick", url);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.newmenu {
  padding: 24px 15px;
  color: #c5c5cf;
  height: calc(100vh - 80px); // 添加高度防止切换组件时菜单闪烁
  .m-list {
    font-size: 14px;
    .m1__title {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      flex-direction: row;
      margin-bottom: 10px;
      color: #fff;
    }
    .m1-title {
      margin-bottom: 24px;
    }
    .t-span {
      font-size: 15px;
      margin-left: 7px;
    }
    .m-item {
      display: flex;
      flex-wrap: wrap;
      // width: 100%;
      flex: 1;
      padding-left: 1rem;
      cursor: pointer;
      justify-content: space-between;
      .m-i-name {
        text-align: left;
        padding: 5px;
        white-space: nowrap;
        &:hover {
          background: #409eff !important;
        }
      }
    }
  }
  .m-i-name-span {
    padding: 5px;
    &.isactive {
      color: #00a3ff;
    }
    &:hover {
      color: #fff;
    }
  }
  .m-i3-name {
    padding-left: 0.5rem;
    padding-top: 4px;
    padding-bottom: 4px;
  }
}
</style>
