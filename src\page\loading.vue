<template>
  <div></div>
</template>

<script>
import { Loading } from "element-ui";
export default {
  data() {
    return {
      auth_code: "",
      expires_in: "",
      website_id: localStorage.getItem("website_id"),
    };
  },
  mounted() {
    Loading.service();
    var params = this.$queryUrlParams(window.location.href);
    if (params.auth_code && params.state === "qywxnew") {
      var data = {
        auth_code: params.auth_code,
      };
      this.sendCodeQyWx(data);
    } else {
      //  && params.auth_type == 1
      this.auth_code = params.auth_code;
      this.expires_in = params.expires_in;
      this.sendCode();
    }
    if (params.ticket) {
      this.operationStart(params.ticket);
    }
  },
  methods: {
    operationStart(value) {
      this.$http.fastRegister(value).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "授权成功",
            type: "success",
          });
          Loading.service().close();
          this.$goPath("/website_update");
        }
      });
    },
    sendCodeQyWx(data) {
      this.$http.sendCodeQyWxNew(data).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "授权成功",
            type: "success",
          });
          Loading.service().close();
          window.location.href = `https://yun.tfcs.cn/admin?website_id=${this.website_id}#/index`;
        }
      });
    },
    sendCode() {
      this.$http
        .sendWxCode({
          auth_code: this.auth_code,
          expires_in: this.expires_in,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "授权成功",
              type: "success",
            });
            Loading.service().close();
            window.location.href = `https://yun.tfcs.cn/admin?website_id=${this.website_id}#/index`;
          }
        });
    },
  },
};
</script>

<style></style>
