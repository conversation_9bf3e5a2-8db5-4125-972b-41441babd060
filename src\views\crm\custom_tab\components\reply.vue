<template>
    <el-dialog width="30%" :visible.sync="show" :modal="false" title="批注">
        <el-input
            type="textarea"
            :rows="10"
            placeholder="请输入内容"
            v-model="textarea">
        </el-input>
         
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'reply',
    data(){
        return {
            show: false,        //dialog是否显示
            cancleFn: null,     //取消回调
            successFn: null,    //成功回调
            params: {           //表单参数
          
            },
            textarea: ''
        }
    },
    methods: {
        //打开弹层
        open(params){
            this.params = params;
            this.show = true;
            return this;
        },
        //注册取消方法
        onCancle(fn){
            fn && (this.cancleFn = fn);
            return this;
        },
        //注册成功方法
        onSuccess(fn){
            fn && (this.successFn = fn);
            return this;
        },

        //取消
        cancle(){
            this.show = false;
            this.cancleFn && this.cancleFn();
        },
        //确定
        confirm(){
            //TODO: 验证+提交
            let params = {}
            params.parentid = this.params.id
            params.content = this.textarea
            this.$http.addFollowreply(params).then(res=>{
                if(res.status==200){
                    this.$message.success("批注成功！")
                    this.show = false;
                    this.textarea = ""
                    this.$emit('getFollowData',1);
                }
            })
            //在提交成功之后回调
            this.successFn && this.successFn();
        }
    }
}
</script>
<style lang="scss" scoped>
    
</style>