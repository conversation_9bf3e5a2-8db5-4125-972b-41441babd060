<template>
  <div class="contents">
    <div style="box-sizing: content-box;" class="im-box" :class="{ has_info: document_width >= 1900 && has_info }"
      @click="onClickChat()">
      <el-container>
        <el-aside width="420px">
          <div class="chat_bar">
            <div class="tabs_box">
              <div class="flex-row tabs_box_leftTop">
                <!-- 搜索好友 -->
                <!-- <el-input
                  type="text"
                  prefix-icon="el-icon-search"
                  placeholder="请输入好友昵称或手机号"
                  v-model="keywords"
                  @keyup.enter.native="handleKeyUp"
                ></el-input> -->
                <div class="chat_titles">消息列表</div>
                <div class="clearUnread" @click="clearAllUnread">清除未读</div>
              </div>
            </div>
            <!-- 好友列表 -->
            <div class="friend_list">
              <div class="friend" v-for="(friend, index) in friend_lsit" :key="index" @click="selectFriend(index)"
                :class="{ active: now_chat.id === friend.id }">
                <div class="img_box">
                  <!-- 用户头像 -->
                  <img v-if="friend.from_user && friend.from_user.avatar" :src="friend.from_user.avatar" alt="" />
                  <!-- 默认头像 -->
                  <img v-else src="@/assets/icon-tx.png" alt="">
                  <!-- 未读取消息数量 -->
                  <div class="unread" v-if="friend.unread_msg_total > 0">
                    {{ friend.unread_msg_total }}
                  </div>
                </div>
                <div class="friend_info">
                  <p class="nickname">
                    <!-- 名称 -->
                    <span class="name"
                      v-if="friend.from_user && (friend.from_user.name != '' && friend.from_user.name != undefined)">
                      {{ friend.from_user.name }}
                    </span>
                    <span v-else class="name">匿名好友</span>
                  </p>
                  <!-- 最后发送消息 -->
                  <p class="chat" v-if="friend.last_msg && friend.last_msg.content">{{ friend.last_msg.content |
                                      formatChat }}</p>
                </div>
                <div class="right_info">
                  <!-- 时间 -->
                  <div class="time">{{ friend.updated_at | getPastTime(friend.updated_at) }}</div>
                </div>
              </div>
              <div v-if="!nodata && !loading" @click="getMoreFriends()" class="load_more">
                点击加载更多
              </div>
              <div v-if="!nodata && loading" class="load_more">加载中...</div>
              <div v-if="friend_lsit.length > 0 && nodata && !loading" class="load_more">
                没有更多了
              </div>
              <div class="nodata" v-if="friend_lsit.length === 0 && nodata">
                暂无好友~~
              </div>
            </div>
          </div>
        </el-aside>
        <el-main>
          <div class="chat_content" v-if="now_chat.id">
            <!-- 聊天窗口顶部好友信息 -->
            <div class="friend_top">
              <div class="friend_info">
                <!-- 头像图片 -->
                <div class="header_box">
                  <img v-if="now_chat.from_user && now_chat.from_user.avatar" :src="now_chat.from_user.avatar" alt="" />
                  <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
                </div>
                <!-- 名称 -->
                <div class="info_box flex-box">
                  <div class="name_box">
                    <span class="name"
                      v-if="now_chat.from_user && (now_chat.from_user.name != '' && now_chat.from_user.name != undefined)">{{
                                            now_chat.from_user.name }}</span>
                    <span class="name" v-else>匿名好友</span>
                  </div>
                </div>
              </div>
              <div class="menu_box">
                <!-- 查看对方手机号 -->
                <!-- <div class="menu_item" @click="getVirtualNumber()">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="查看对方手机号"
                    placement="top-start"
                  >
                    <i class="el-icon-newfontshouji"></i>
                  </el-tooltip>
                </div> -->
                <!-- 查看对方微信号 -->
                <!-- <div class="menu_item" @click="applyWechat()">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="申请查看对方微信号"
                    placement="top-start"
                  >
                    <i class="el-icon-newfontweixin"></i>
                  </el-tooltip>
                </div> -->
                <!-- 查看对方信息 -->
                <!-- <div class="menu_item" @click="infoToogle()">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="查看对方信息"
                    placement="top-start"
                  >
                    <i
                      class="el-icon-newfontkehuziliao"
                      :class="{ active: show_info }"
                    ></i>
                  </el-tooltip>
                </div> -->
              </div>
            </div>
            <div class="chat_body">
              <!-- 聊天消息列表 -->
              <div class="chat_list_box" ref="chat_list_box">
                <div class="no_more" v-if="getmore === 'nomore'">没有更多了</div>
                <div class="no_more" v-else-if="getmore === 'loading'">
                  加载中...
                </div>
                <div class="get_more" v-else @click="getMoreInfo()">点击查看更多</div>
                <div class="chat_list" ref="chat_list" id="chat_list">
                  <div class="chat_item" v-for="(chat, index) in now_chat.chat_logs" :key="index">
                    <!-- 过去多久时间 -->
                    <div class="time">
                      <span>{{ chat.created_at | getPastTime(chat.updated_at) }}</span>
                    </div>
                    <!-- 好友消息  chat.to_id == admin_id"-->
                    <div class="friend_chat" v-if="chat.from_user_id !== chat.self_uid">
                      <!-- 头像图片 -->
                      <div class="header_box">
                        <img v-if="now_chat.from_user && now_chat.from_user.avatar" :src="now_chat.from_user.avatar "
                          alt="" />
                        <img v-else src="@/assets/icon-tx.png" alt="" />
                      </div>
                      <!-- 文本消息 -->
                      <div class="info"
                        v-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'text'">
                        <span v-html="formatTextMsg(chat.content.content)"></span>
                        <i class="point"></i>
                      </div>
                      <!-- 图片消息 -->
                      <div class="img"
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'img'">
                        <img :src="chat.content.content" alt="" @click="viewImage(chat.content.content)" />
                        <i class="point"></i>
                      </div>
                      <!-- 地图消息 -->
                      <div class="info"
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'map'">
                        <span>[位置消息]：{{ chat.content.address }}</span>
                        <i class="point"></i>
                      </div>
                      <!-- 消息类型为房源 -->
                      <div class="issue"
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'build'">
                        <div class="issue-box" @click="skipHouseDetails(chat.content.content.id)">
                          <div class="issue-img">
                            <img :src="chat.content.content.image" alt="" />
                          </div>
                          <div class="issue-text-content">{{ chat.content.content.title }}</div>
                        </div>
                      </div>
                      <!-- 其他消息 -->
                      <div
                        v-else-if="['build', 'ershou', 'renting', 'commercial', 'commercial_rent', 'commercial_transfer'].includes(chat.content.content.type)"
                        class="build-card" @click="toDetail(chat.content.content, chat.content.content.type)">
                        <div class="build-img-box">
                          <img class="build-img" mode="aspectFill" :src="chat.content.content.image " />
                        </div>
                        <div class="build-title">{{ chat.content.title }}</div>
                      </div>
                      <!-- 微信名片消息 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'wechat'"
                        class="wechat-card">
                        <div class="wechat-info-box">
                          <div class="wechat-icon">
                            <i class="el-icon-tfy-weixin1"></i>
                          </div>
                          <div class="wechat-info">
                            <div class="name">
                              {{ chat.content.name }}的微信号
                            </div>
                            <div class="wechat">{{ chat.content.wechat }}</div>
                          </div>
                        </div>
                        <div class="options">
                          <!-- <div class="left-btn" @click="copyWechat(chat.content.wechat)">复制微信号</div> -->
                          <div class="right-btn" @click="viewQrcode(chat.content.qrcode)">
                            查看二维码
                          </div>
                        </div>
                      </div>
                      <!-- 对方申请查看微信号 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'apply_wx'"
                        class="apply-wechat">
                        <span class="cont">
                          <span class="system_tip">对方申请查看您的微信,是否同意?</span>
                          <span class="agree" @click="handleAgree('wechat')">同意</span>
                        </span>
                      </div>
                      <!-- 手机号消息 -->
                      <div v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'tel'"
                        class="wechat-card">
                        <div class="wechat-info-box">
                          <div class="wechat-icon">
                            <i class="el-icon-tfy-dianhua1"></i>
                          </div>
                          <div class="wechat-info">
                            <div class="name">
                              {{ chat.content.name }}的手机号
                            </div>
                            <div class="wechat">{{ chat.content.tel }}</div>
                          </div>
                        </div>
                      </div>
                      <!-- 对方申请查看手机号 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'apply_tel'"
                        class="apply-tel">
                        <span class="cont">
                          <span class="system_tip">对方申请查看您的手机号,是否同意?</span>
                          <span class="agree" @click="handleAgree('tel')">同意</span>
                        </span>
                      </div>
                      <div v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'voice'"
                        class="info" @click="playVoice(chat, index)">
                        <div class="voice">
                          <img class="play_vioce_icon" :src="
                                                        index === current_voice_index
                                                          ? publicPath + 'images/play_voice_black.gif'
                                                          : publicPath + 'images/voice_icon_black.png'
                                                      " />
                          <span>{{ parseInt(chat.content.duration / 1000) }}''</span>
                        </div>
                        <i class="point"></i>
                      </div>
                      <div v-else class="not-message">[不支持的消息格式]</div>
                      <div style="clear:both"></div>
                    </div>
                    <!-- 自己的消息 -->
                    <div class="my_chat" v-else-if="chat.from_user_id === chat.self_uid">
                      <!-- 头像图片 -->
                      <div class="header_box" v-if="chat.from_user && chat.from_user.avatar">
                        <img :src="chat.from_user.avatar" alt="" />
                      </div>
                      <!-- 消息类型为文本 -->
                      <div class="info"
                        v-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'text'">
                        <span v-html="formatTextMsg(chat.content.content)"></span>
                        <i class="point"></i>
                      </div>
                      <!-- 消息类型为图片 -->
                      <div class="img"
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'img'">
                        <img :src="chat.content.content " alt="" @click="viewImage(chat.content.content)" />
                        <i class="point"></i>
                      </div>
                      <!--消息类型为位置 -->
                      <div class="info"
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'map'">
                        <span>[位置消息]：{{ chat.content.address }}</span>
                        <i class="point"></i>
                      </div>
                      <!-- 消息类型为楼盘 -->
                      <div class="issue"
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'build'">
                        <div class="issue-box" @click="skipHouseDetails(chat.content.content.id)">
                          <div class="issue-img">
                            <img :src="chat.content.content.image" alt="" />
                          </div>
                          <div class="issue-text-content">{{ chat.content.content.title }}</div>
                        </div>
                      </div>
                      <!-- 其他消息类型 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && ['ershou', 'renting', 'commercial', 'commercial_rent', 'commercial_transfer'].includes(chat.content.type)"
                        class="build-card" @click="toDetail(chat.content.content, chat.content.type)">
                        <div class="build-img-box">
                          <img class="build-img" mode="aspectFill"
                            :src="chat.content.content.image | imgFormater('w_8001')" />
                        </div>
                        <div class="build-title">{{ chat.content.content.title }}</div>
                      </div>
                      <!-- 发送微信名片 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'wechat'"
                        class="apply-wechat">
                        <span class="cont">您已将微信号发送给对方</span>
                      </div>
                      <!-- 接收微信名片 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'apply_wx'"
                        class="apply-wechat">
                        <span class="cont">您申请查看对方微信，等待对方同意</span>
                      </div>
                      <!-- 发送手机号 -->
                      <div v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'tel'"
                        class="apply-tel">
                        <span class="cont">您已将手机号发送给对方</span>
                      </div>
                      <!-- 查看手机号 -->
                      <div
                        v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'apply_tel'"
                        class="apply-tel">
                        <span class="cont">您申请查看对方手机号，等待对方同意</span>
                      </div>
                      <!-- 音频类型消息 -->
                      <div v-else-if="(chat.content && Object.keys(chat.content).length) && chat.content.type === 'voice'"
                        class="info" @click="playVoice(chat, index)">
                        <div class="voice">
                          <img class="play_vioce_icon" :src="
                                                        index === current_voice_index
                                                          ? publicPath + 'images/play_voice.gif'
                                                          : publicPath + 'images/voice_icon.png'
                                                      " />
                          <span>{{ parseInt(chat.content.duration / 1000) }}''</span>
                        </div>
                        <i class="point"></i>
                      </div>
                      <div class="not-message" v-else>[不支持的消息格式]</div>
                      <div style="clear:both"></div>
                    </div>
                    <!-- 其他消息 -->
                    <div v-else class="system-info">
                      <div v-if="chat.type === 'wechat'" class="wechat-card">
                        <div class="wechat-info-box">
                          <div class="wechat-icon">
                            <i class="el-icon-tfy-weixin1"></i>
                          </div>
                          <div class="wechat-info">
                            <div class="name">
                              {{ chat.content.name }}的微信号
                            </div>
                            <div class="wechat">{{ chat.content.wechat }}</div>
                          </div>
                        </div>
                        <div class="options">
                          <!-- <div class="left-btn" @click="copyWechat(chat.content.wechat)">复制微信号</div> -->
                          <div class="right-btn" @click="viewImage(chat.content.qrcode, 300)">
                            查看二维码
                          </div>
                        </div>
                      </div>
                      <div class="apply-tel" v-if="chat.type === 'applyTel'">
                        <span class="cont">
                          <span class="tip">对方申请查看您的手机号，是否同意？</span>
                          <!-- <text class="no">拒绝</text> -->
                          <span class="agree" @click="sendTel()">同意</span>
                        </span>
                      </div>
                      <div class="apply-tel" v-if="chat.type === 'giveTel'">
                        <span class="cont">
                          <span class="tel">对方手机号码：{{ chat.content.tel }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 底部发送部分 -->
              <div class="send_box">
                <!-- 表情弹出层 -->
                <div class="face_list" v-show="show_face_list">
                  <div class="face_item" v-for="(face, index) in face_list" :key="index" @click.stop="selectFace(face)">
                    <img :src="
                                            'https://images.tengfangyun.com/images/face/' +
                                              index +
                                              '.png'
                                          " alt="" />
                  </div>
                </div>
                <!-- 快捷回复 -->
                <div class="fast_reply_box" v-show="show_fast_reply_list">
                  <div class="fast_reply_chil_box">
                    <div class="fast_reply_list">
                      <div class="fast_reply_item" v-for="(item, index) in fast_reply_list" :key="index"
                        @click="sentText(item.content)">
                        <span>{{ item.content }}</span>
                      </div>
                      <div v-if="fast_reply_list.length === 0" style="text-align:center;padding:10px;color:#888">
                        暂无快捷用语
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 表情 -->
                <div class="send_bar">
                  <i class="icon el-icon-newfontbiaoqing" @click.stop="show_face_list = !show_face_list"></i>
                  <div style="display: inline-block;">
                    <el-upload :action="picture_upurl" :headers="upload_headers" :limit="1"
                      accept=".jpg,.jpeg,.png,.JPG,.JPEG"
                      :on-success="(response, file, fileList) => { uploadSuccessPicture({response, file, fileList}) }"
                      :show-file-list="false">
                      <i class="icon el-icon-newfonttianjiafujian"></i>
                    </el-upload>
                  </div>
                  <div class="show_house_btn" v-if="document_width < 1900 && has_info" @click="show_house_info = true">
                    <i class="el-icon-tfy-shouye"></i>
                    <span>发送房源</span>
                  </div>
                  <div class="fast_reply" @click.stop="show_fast_reply_list = !show_fast_reply_list">
                    <i class="icon el-icon-newfontkuaijiehuifu"></i>
                    <span class="text">快捷回复</span>
                  </div>
                </div>
                <!-- 输入框 -->
                <div class="send_input" ref="inp" @input="handleInput" @keydown="handleKeydown" placeholder="想说点什么？"
                  contentEditable="true"></div>
                <el-button class="send_btn" type="primary" @click="sentText()">发送</el-button>
              </div>
              <div class="mask" @click="onClickMask" :class="{ show: show_info||show_house_info }"></div>
              <!-- 对方的房源或楼盘信息 -->

              <!-- 对方的资料和访问轨迹 -->
              <div class="chat_info" :class="{ show: show_info }">
                <div class="card">
                  <div class="info_box">
                    <img class="avatar" :src="friend_user_info.prelogo" alt="" />
                    <p class="name">{{ friend_user_info.nickname }}</p>
                  </div>
                </div>
                <div class="card">
                  <div class="title">访客轨迹</div>
                  <div class="time_line">
                    <div class="line_item" v-for="(item, index) in visitor_detail" :key="index">
                      <p class="time">{{ item.time }}</p>
                      <p class="desc">{{ item.content }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="chat_content" v-else>
            <div class="tip">即时通讯</div>
          </div>
        </el-main>
        <el-aside width="420px" v-if="document_width >= 1900 && has_info">
          <div class="house_list">
            <div class="info_search_row">
              <el-input v-model="info_list_key" @keyup.enter.native="onSearchInfo" prefix-icon="el-icon-search"
                placeholder="请输入房源名称或编号"></el-input>
            </div>
            <div class="list_container infinite-list" v-infinite-scroll="onInfoListScroll" style="overflow:auto">
              <InfoList ref="infolist" :info_list="myHouseFormData_list" @clickitem="sendPage" @viewitem="viewPage" />
              <div class="loading" v-loading="loading_info =='loading'" element-loading-text="加载中">
                {{loading_info ==='nomore'? '没有更多了': ''}}
              </div>
            </div>
          </div>
        </el-aside>
      </el-container>
      <el-dialog :width="img_width" :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
      <el-dialog title="邀请评价" :visible.sync="showEvaluate">
        <el-rate v-model="evaluate" show-text :texts="texts"> </el-rate>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showEvaluate = false">取 消</el-button>
          <el-button type="primary" @click="subEvaluate()">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="showTelNum" title="查看电话号码" @close="closeTelNum()" width="500px">
        <div class="layer_tel-box">
          <p class="build_name">{{ friend_user_info.user_name }}的电话，请拨打</p>
          <div style="background: url(https://tfy.tengfun.com/static/skin/tfy/img/tel_icon.png) 0 32px no-repeat;"
            class="tel">
            <p>
              <span class="color-red" id="tel_num">{{ friend_user_info.phone }}</span>
            </p>
            <p v-if="ter_telcall && show_count_down && virtual_number">
              已为您提供隐私保护服务，对方将看不到您的真实号码
            </p>
          </div>
          <div v-if="ter_telcall && show_count_down && virtual_number" class="tel_time" id="tel_time"
            @click="getVirtualNumberAgain()">
            <span v-if="time_down > 0">电话{{ time_down }}s后过期，记得重新获取</span>
            <span v-else>重新获取</span>
          </div>
          <div class="tel_tip">
            <p>
              拨通后，请说明<span>来自 腾房云</span>
            </p>
            <p>将获得更好的接待服务</p>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import { mapState } from "vuex";
import playVoice from "@/mixin/play_voice";
import InfoList from "./components/InfoList"
import config from "@/utils/config.js";
export default {
  name: "Im",
  mixins: [playVoice],
  data() {
    return {
      // 好友列表请求参数
      getData_params: {
        page: 1, // 页数
        per_page: 20, // 条数
      },
      // 会话详情请求参数
      Conversation_params: {
        page: 1, // 页数
        per_page: 20, // 条数
        to_user_id: "", // 聊天用户ID
        start_id: 0, // 默认值
      },
      publicPath: process.env.BASE_URL,
      keywords: "",
      is_black: 0,
      friend_user_info: {},
      friend_lsit: [], // 好友成员列表
      nodata: false, // 控制加载等提示问题显示/ 隐藏
      loading: false,
      getmore: "loading", // 控制加载数据提示文字
      my_chat_info: {}, // 自身信息
      friend_chat_info: {}, // 好友信息
      // 当前好友的信息
      now_chat: {
        chat_logs: [],
      },
      text_content: "", // 聊天输入框内容
      show_face_list: false, // 显示/隐藏标签弹出框
      face_list: [
        "微笑",
        "大笑",
        "笑哭",
        "开心",
        "呲牙",
        "坏笑",
        "欣慰",
        "鄙视",
        "白眼",
        "飞吻",
        "鬼脸",
        "酷",
        "爱财",
        "调皮",
        "惊讶",
        "无表情",
        "思考",
        "亲亲",
        "喜欢",
        "低沉",
        "怒",
        "生气",
        "超爱",
        "大哭",
        "小声",
        "惊恐",
        "爱心",
        "心碎",
        "偷看",
        "OK",
        "耶",
        "大拇指",
        "握拳",
        "强壮",
      ],
      show_info: false, //控制类名show的显示隐藏
      visitor_detail: [],
      fast_reply_list: [
        {id: 7, content: "请您稍等片刻，正在为您查询。"},
        {id: 10, content: "你好，在的。"},
        {id: 12, content: "亲，你想了解哪方面的信息？"},
        {id: 16, content: "您好，请问有什么可以帮助您的！"},
        {id: 23, content: "方便的话留个联系方式，我稍后详细向您介绍~"}
      ],
      show_fast_reply_list: false, // 显示/隐藏快捷回复
      dialogVisible: false,
      showEdit: false,
      showSetAuto: false,
      autoplay:{
        content: "",
        id: ""
      },
      fast_reply: {
        id: "",
        content: "",
      },
      dialogImageUrl: "",
      img_width: "800px",
      evaluate: 5,
      texts: ["差评", "失望", "一般", "好评", "非常好"],
      showEvaluate: false,
      showTelNum: false,
      time_down: 30,
      real_number: "", //真实号码
      virtual_number: "", //虚拟号码
      ter_telcall: 0,
      show_count_down: true, //是否显示倒计时
      document_width: "",
      show_house_info: false,
      loading_info: "",
      info_list_type: '', // build 或 ershou 或 renting
      info_list_page: 1,
      info_list_key: "",
      my_info_list: [], //自己的房源列表
      has_info: false, // 控制页面样式
      chat_log_rows:20,
      admin_id:window.localStorage.getItem('admin_id'),
      // 我的房源列表接口请求参数
      myHouseList_params: {
        page: 1, // 页码
        rows: 20, // 条数
        keyword: '', // 搜索房源名称
        hid: '', // 搜索房源编号
        is_owner: 1,
        trade_status: 9,
      },
      myHouseFormData_list: [], // 我的房源参数
      is_roll: true, // 限制滚动请求接口
      upload_headers: {
        Authorization: config.TOKEN,
      },
      picture_upurl: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
      website_ids: "", // 站点id
      platform_id_prefix: "", 
      from_platform_id: "",
      to_platform_id: "",
    };
  },
  components: {
    InfoList
  },
  created() {
    this.website_ids = this.$route.query.website_id; // 赋值站点id
    this.document_width = document.body.clientWidth
    this.$store.state.im_badge = 0;
    this.getSystemToken();
    this.getSystemImToken();
  },
  filters: {
    formatChat(chat) {
      // console.log(chat)
      switch (chat.type) {
        case "text":
          return chat.content;
        case "img":
          return "[图片]";
        case "map":
          return "[位置]";
        case "issue":
          return "[项目]";
        case "build":
          return "[楼盘]";
        case "ershou":
          return "[二手房]";
        case "renting":
          return "[出租房]";
        case "commercial":
          return "[商业出售]";
        case "commercial_rent":
          return "[商业出租]";
        case "commercial_transfer":
          return "[生意转让]";
        case "wechat":
          return "[微信名片]";
        case "apply_wx":
          return "[申请查看微信名片]";
        case "tel":
          return "[手机号码]";
        case "apply_tel":
          return "[申请查看手机号]";
        case "voice":
          return "[语音]";
        case "":
          return "";
        default:
          return "[不支持的消息]";
      }
    },
    numFormater(val, max_num = 10000) {
      if (!val) {
        return "";
      }
      if (val < max_num) {
        return val;
      }
      if (val >= max_num) {
        return "1万+";
      }
    },
    imgFormater(val, param = "w_1200") {
      if (!val || val == window.location.origin) {
        return "";
      }
      var isVideo = function(url) {
        let videoArr = ["mp4", "avi"];
        let varr = url.split(".");
        let name = varr[varr.length - 1];
        return videoArr.includes(name);
      };
      if (isVideo(val)) {
        return val + "?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast";
      }
      return val + "?x-oss-process=style/" + param;
    },
    getPastTime(val) {
      // 获取当时的时间戳
      let givenDate = new Date(val);
      let givenTimestamp = givenDate.getTime(); // 获取时间戳
      // 获取现在的时间戳
      let currentDate = new Date();
      let currentTimestamp = currentDate.getTime();
      // 计算时间差
      let timeDiff = currentTimestamp - givenTimestamp;
      if(timeDiff < (1000 * 60 * 60 * 24)) {
        let hourDiff = Math.floor(timeDiff / (1000 * 60 * 60)); // 计算过去多少小时
        // 如果时间差小于1小时，则计算过去多少分钟
        if(timeDiff < (1000 * 60 * 60)) {
          let minutesDiff = Math.floor(timeDiff / (1000 * 60)); // 计算过去了多少分钟
          // 如果分钟小于1 就显示刚刚
          if(minutesDiff < 1) {
            return '刚刚';
          } else {
            return minutesDiff + '分钟前';
          }
        } else {
          return hourDiff + '小时前';
        }
      } else if(timeDiff < (1000 * 60 * 60 * 24 * 30)) {
        // 如果时间差小于30天按照天计算
        let daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        return daysDiff + '天前';
      } else if(timeDiff < (1000 * 60 * 60 * 24 * 30 * 12)) {
        // 如果时间小于1年按照月计算
        let daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24 * 30));
        return daysDiff + '月前';
      } else {
        // 按照年计算
        let daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24 * 30 * 12));
        return daysDiff + '年前';
      }
    }
  },
  methods: {
    // 获取user_Token
    getSystemToken() {
      this.$http.getSystemToken().then((res) => {
        if(res.status == 200) {
          this.userToken = res.data.token;
          this.platform_id_prefix = res.data.platform_id_prefix;
        }
      })
    },
    // 获取im_Token
    getSystemImToken() {
      this.$http.getSystemImToken().then((res) => {
        if(res.status == 200) {
          console.log(res.data,"获取im_Token")
          this.imToken = res.data.token;
          this.getData_params.page = 1; // 请求页码为1
          this.friend_lsit = []; // 清空已有的好友列表
          this.getFriend();
        }
      })
    },
    // 打开socket
    connectSocket() {
      // 如果已经连接socket就return
      if (this.socket_is_open) {
        return;
      }
      // 创建webSocket对象
      this.ws = new WebSocket(
        `wss://imcloud.tengfangyun.com:9002?imToken=${this.imToken}&userToken=${
          this.userToken
        }`
      );
      // onopen:当WebSocket连接成功建立时触发
      this.ws.onopen = (event) => {
        this.close_type = null;
        console.log("socket已连接",event);
        this.initSocket();
      };
    },
    // 重新链接聊天
    connectSocketAgain() {
      this.getSystemImToken(); // 获取imToken
      this.getSystemToken(); // 获取userToken
      this.connectSocket(); // 获取WebSocket连接
    },
    // 初始化聊天连接
    initSocket() {
      // send: 向服务器发送消息
      // JSON.stringify: 将JavaScript 对象或值转换为 JSON 字符串后通过websocket发送
      this.ws.send(
        JSON.stringify({
          flag: "init",
          from_id: this.my_chat_info.id,
        })
      );
      this.socket_is_open = true; // 已连接socket
      this.onMessage();
      this.onSocketClose();
      this.heartbeat();
    },
    heartbeat() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        if (!this.socket_is_open && this.timer) {
          clearInterval(this.timer);
          return;
        }
        this.ws.send(JSON.stringify({ flag: "Heartbeat" }));
      }, 30000);
    },
    handleKeyUp() {
      this.nodata = false;
      this.getData_params.page = 1;
      this.getData_params.keyword = this.keywords;
      this.friend_lsit = []; // 清空已有的好友列表
      this.getFriend();
    },
    // 获取好友列表
    getFriend() {
      this.loading = true; // 开启loading
      this.$http.getChatMessage(this.getData_params).then((res) => {
        if(res.status == 200) {
          this.loading = false; // 关闭loading
          this.userToken = res.data.data?.userToken ||  localStorage.getItem('TOKEN'); // 赋值TOKEN
          this.connectSocket() // 获取WebSocket连接
          if (res.data.data && res.data.data.length > 0) {
            // let to_friend_index;
            let friends = res.data.data.map((item) => {
              item.chat_logs = []; // 追加聊天日志参数
              return item;
            });
            // 合并好友列表数据
            if(this.getData_params) {
              this.friend_lsit = this.friend_lsit.concat(friends);
            }
            if(res.data.data.length == this.getData_params.per_page){
              this.loading = false;
            }else {
              this.nodata = true
            }
          } else {
            this.nodata = true;
          }
        } else {
          this.loading = false;
        }
      }).catch(() => {
          this.loading = false;
        });
    },
    getMoreFriends() {
      if(this.nodata) {
        this.getData_params.page++; // 页码增加1
        this.getFriend(); // 获取好友列表
      }
    },
    // 选择需要聊天的好友
    selectFriend(index) {
      // 重置房源列表搜索参数
      this.myHouseList_params = {
        page: 1, // 页码
        rows: 20, // 条数
        keyword: '', // 搜索房源名称
        hid: '', // 搜索房源编号
        is_owner: 1,
        trade_status: 9,
      }
      this.myHouseFormData_list = []; // 当切换聊天对象时清空我的房源数据
      this.has_info = true; // 展开页面样式
      this.text_content = ""; // 清空聊天输入框内容
      if (this.$refs.inp) {
        this.$refs.inp.innerHTML = ""; // 清空输入框内容
      }
      this.now_chat = this.friend_lsit[index]; // 赋值当前选择的好友信息给now_chat
      this.Conversation_params.to_user_id = this.friend_lsit[index].from_user.id; // 赋值当前聊天用户ID
      this.getMyHouseList(); // 获取我的房源数据
      this.chat_index = index; // 赋值当前选中好友列表的index
      this.show_info = false; // 控制类名show的显示隐藏
      this.Conversation_params.page = 1; // 重置请求页码
      this.getChatLogs(); // 获取聊天记录
      // 如果当前有未读消息，就将未读消息赋值为0
      if (this.friend_lsit[index].unread_msg_total > 0) {
        this.friend_lsit[index].unread_msg_total = 0;
        this.clearUnread();
      }
    },
    // 获取聊天记录
    getChatLogs() {
      this.getmore = "loading"; // 文字提示数据加载中
      let preHeight;
      if (this.Conversation_params.page === 1) {
        this.now_chat.chat_logs = []; // 清空会话详情内容
      } else {
        preHeight = this.$refs.chat_list.scrollHeight; // 获取之前的高度
      }
      // 获取聊天会话详情
      this.$http.getConversationDetails(this.Conversation_params).then((res) => {
        if(res.status == 200) {
          console.log(res.data,"数据");
          // 获取自己、好友信息
          if(res.data[0] && (res.data[0].self_uid == res.data[0].from_user_id)) {
            this.from_platform_id = this.platform_id_prefix + res.data[0].self_uid;
            this.to_platform_id = this.platform_id_prefix + res.data[0].to_user_id;
            this.friend_chat_info = res.data[0].to_user; // 赋值发送人id
            this.my_chat_info = res.data[0].from_user; // 赋值自己身id
          } else if(res.data[0]) {
            this.from_platform_id = this.platform_id_prefix + res.data[0].from_user_id;
            this.to_platform_id = this.platform_id_prefix + res.data[0].self_uid;
            this.my_chat_info = res.data[0].to_user;
            this.friend_chat_info = res.data[0].from_user;
          }
          // 判断数据是否加载到底了
          if(res.data.length < this.chat_log_rows){
            this.getmore ="nomore" // 无更多数据
          }else {
            this.getmore ="loadend" // 继续加载
          }
          // 合并数据
          this.now_chat.chat_logs = res.data.concat(this.now_chat.chat_logs);
          // 滚动到最底部
          if (this.Conversation_params.page === 1) {
            setTimeout(() => {
              this.scroolBottom();
            }, 300);
          }
          // 保持在原来的位置
          if (this.Conversation_params.page > 1) {
            this.$nextTick(() => {
              let chat_list_box = this.$refs.chat_list_box;
              let chat_list = this.$refs.chat_list;
              chat_list_box.scrollTop = chat_list.scrollHeight - preHeight;
            });
          }
        }
      }).catch(() => {
        console.log("执行catch")
        this.getmore = "loadend";
      })
    },
    // 修改当前会话为已读
    clearUnread() {
      this.$http.setConversationRead(this.now_chat.from_user.id).then(res => {
        if(res.status == 200) {
          return;
        }
      })
    },
    // 清除所有未读
    clearAllUnread() {
      this.$http.clearAllUnread().then(res => {
        if(res.status == 200) {
          this.friend_lsit.map((item) => {
            item.unread_msg_total = 0;
          })
        }
      }).catch(() => {
        this.$message.error("操作失败");
      })
    },
    getMoreInfo() {
      this.Conversation_params.page++;
      this.getChatLogs();
    },
    // 获取快捷回复
    getFastReply() {
    },
    subFastReply() {
      this.$userApi.editExpressLanguage(this.fast_reply).then((res) => {
        if (res.data.code === 1) {
          this.$message.success("修改成功");
          this.showEdit = false;
          this.getFastReply();
        } else {
          this.$message.error(res.data.msg || "修改失败");
        }
      });
    },
    // 设置自动回复
    subAutoReply(){
      if(this.autoplay.id){
        this.editAutoReply()
      }else{
        this.addAutoReply()
      }
    },
    addAutoReply(){
      this.$userApi.addAutoReplay({content:this.autoplay.content}).then((res) => {
        if (res.data.code === 1) {
          this.$message.success("添加成功");
          this.showSetAuto = false;
        } else {
          this.$message.error(res.data.msg || "添加失败");
        }
      });
    },
    editAutoReply(){
      this.$userApi.editAutoReplay(this.autoplay).then((res) => {
        if (res.data.code === 1) {
          this.$message.success("修改成功");
          this.showSetAuto = false;
        } else {
          this.$message.error(res.data.msg || "修改失败");
        }
      });
    },
    // 输入内容触发
    handleInput() {
      // this.text_content = this.formatTextMsg(this.$refs.inp.innerHTML);
      this.text_content = this.$refs.inp.innerHTML;
    },
    // 按下键盘触发
    handleKeydown(e){
      // 如果按下回车
      if(e.keyCode === 13){
        // this.text_content = this.formatTextMsg(this.$refs.inp.innerHTML);
        this.text_content = this.$refs.inp.innerHTML;
        // 发送消息
        this.sentText(this.text_content);
        // 清空消息
        setTimeout(()=>{
          this.$refs.inp.innerHTML = "";
        }, 20)
      }
    },
    // 点击发送菜单后的回调函数
    sendMore(e){
      if(e == 1){
        this.showSetAuto = true
        this.getAutoReplay()
      }
    },
    // 获取自动回复
    getAutoReplay(){
      console.log("获取自动回复数据");
      // this.$userApi.getAutoReplay().then((res) => {
      //   if (res.data.code === 1) {
      //     this.autoplay = res.data.data;
      //   }
      // });
    },
    // 发送文字消息
    sentText(content = "") {
      if(!content && this.formatTextMsg(this.$refs.inp.innerHTML)){
        // content = this.formatTextMsg(this.$refs.inp.innerHTML)
        content = this.text_content;
      }
      // 是否存在会话id
      if (this.now_chat.chat_logs.im_session_id) {
        this.$message("请选择要发给的好友");
        return;
      }
      const regex = new RegExp(/<div>|<\/p>/, "gi");
      const regex2 = new RegExp(/<br>|<\/div>|<\/p>/, "gi");
      let text_content = this.text_content
        .replace(regex, "\n")
        .replace(regex2, "");
      if (text_content.trim("") === "" && content.trim("") === "") {
        this.$message("请输入要发送的内容");
        return;
      }
      // 检测敏感词
      let msg = { type: "text", content: content, dialog_id: this.now_chat.id };
      let msg1= Object.assign({},msg)
      msg1.content  = msg1.content.replace(/https:\/\/images.tengfangyun.com/g,'')
      if(msg.type === 'text'){
        // for (let i = 0; i < this.badwords.length; i++){
        //   if (msg1.content.includes(this.badwords[i]) && this.badwords[i]){
        //     this.$message.warning(`发送内容包含敏感词："${this.badwords[i]}"，请删除后发送`)
        //     return
        //   }
        // }
      }
      if (this.socket_is_open) {
        this.sendMessage(msg);
        this.text_content = "";
        this.$refs.inp.innerHTML = "";
      } else {
        this.$message("聊天已断开");
      }
    },
    /**
     * 检查消息接收到的消息是否是当前好友列中的消息
     * @param {Object} msg 接收到的消息
     */
    checkMsg(msg) {
        let chat_id = msg.chat_id
        let friend_index;
        // 检测收到的消息是不是属于当前好友列表中的
        for (let i = 0; i < this.friend_lsit.length; i++) {
            if (this.friend_lsit[i].chat_id === chat_id) { //后台返回的platformid是字符串,消息的from_id是数字
                friend_index = i;
                break;
            }
        }
        // 如果消息不是当前好友列表中的好友发送的
        if (friend_index === undefined) {
            // 将好友插入到好友列表
            let user_id = ""
            if(msg.user_id){
                user_id = msg.user_id
            }else{
                user_id = msg.from_id.split('_')[2]
            }
            let friend = { headimage: msg.from_headimage, nickname: msg.from_nickname, chat_id: msg.chat_id, platform_id: msg.from_id, user_id: user_id, time: msg.time, owner_id: 0, passive_id:0, uncount: 0 }
            this.friend_lsit.unshift(friend)
            friend_index = 0
        }
        this.addChat(msg, 0, friend_index)
    },
    // 发送消息
    sendMessage(msg) {
      console.log(this.my_chat_info,"123")
      let sendNum = {
        content: msg,
        dialog_id: msg.dialog_id,
        flag: "sendMessage",
        from_id: this.from_platform_id,
        to_id: this.to_platform_id,
        from_headimage: this.my_chat_info.avatar,
        from_user_id: this.my_chat_info.id,
        from_nickname: this.my_chat_info.name,
        type: "text",
      }
      let nowNum = {
        content: msg,
        created_at: this.getNowDate(),
        from_user: this.my_chat_info,
        from_user_id: this.my_chat_info.id,
        self_uid: this.my_chat_info.id,
        to_user: this.friend_chat_info,
        to_user_id: this.friend_chat_info.id,
        website_id: this.website_ids,
      }
      // msg.flag = "sendMessage";
      // msg.from_nickname = this.now_chat.chat_logs.nickname;
      // // msg.headimage = this.now_chat.chat_logs.headimage;
      // msg.from_id = this.now_chat.chat_logs.id;
      // msg.to_id = this.now_chat.chat_logs.im_session_id;
      // msg.chat_id = this.now_chat.chat_logs.from_user.id;
      if (this.socket_is_open) {
        this.ws.send(JSON.stringify(sendNum));
        this.addChat(nowNum);
      } else {
        this.socket_is_open = false;
        this.$message("聊天已断开");
      }
    },
    saveSendMessage(id, msg) {
        this.$http.saveSendMessage({im_session_id: id, content: msg}).then((res) => {
          if(res.status == 200) {
            let msgIndex;
            this.friend_lsit.map((item, index) => {
              if(item.id == id) {
                msgIndex = index; // 赋值当前下标
                // 提交成功后赋值当前会话的最后一条消息
                item.last_msg.content = JSON.parse(msg); // 赋值提交的参数
                item.updated_at = this.getNowDate(); // 赋值当前时间
              }
            })
            // 提交成功置顶当前会话
            let second = this.friend_lsit.splice(msgIndex, 1); // 取出元素
            this.friend_lsit.unshift(second[0]); // 排到第一位
          }
        })
    },
    // 将发送的消息添加到聊天页面
    addChat(msg) {
      this.now_chat.chat_logs.push(msg);
      console.log(this.now_chat.chat_logs)
      this.now_chat.chat = {
        type: msg.type,
        time: msg.time || "刚刚",
        content: msg.content,
      };
      this.$nextTick(() => {
        this.scroolBottom();
      });

      let from_friend_index;
      // 获取好友在好友列表中的位置
      for (let i = 0; i < this.friend_lsit.length; i++) {
        if (msg.to_id === this.friend_lsit[i].platform_id) {
          from_friend_index = i;
          break;
        }
      }
      // 将好友移动到好友列表最上面
      if (from_friend_index > 0) {
        let new_chat = this.friend_lsit.splice(from_friend_index, 1);
        this.friend_lsit.unshift(new_chat[0]);
      }
    },
    // 监听收到消息
    onMessage() {
      // onmessage: 当接收到来自服务器的消息时触发。
      this.ws.onmessage = (event) => {
        let msg = JSON.parse(event.data); // 将接收到的消息解析 JSON 字符串
        console.log(msg,"msg");
        switch (msg.flag) {
          // 发送消息的状态
          case "sendMessageStatus":
            // chat_id: 标识聊天会话的唯一标识符
            // type: 发送内容的类型
            // content: 发送的内容
            // is_online: 0不在线  1在线
            // this.pushMsg(msg.chat_id, msg.type, msg.type === 'text' ? msg.content:JSON.stringify(msg.content), msg.is_online)
            console.log("接收：", msg);
            this.saveSendMessage(this.now_chat.id, JSON.stringify(msg.content))
            break;
          // 发送消息
          case "sendMessage":
            this.handleMessage(msg);
            break;
          // 对方把自己加入黑名单消息
          case "pullBlack":
            this.handleBlack(msg.black_user_id);
            break;
          // 对方把自己从黑名单恢复的消息
          case "removeBlack":
            this.handleRemoveBlack(msg.black_user_id);
            break;
          // 对方邀请评价
          case "inviteEvaluate":
            this.handleInviteEvaluate(msg);
            break;
          case "applyTel":
            if (msg.tel) {
              let sys_message = {
                content: {
                  tel: msg.tel,
                },
                type: "giveTel",
              };
              this.now_chat.chat_logs.push(sys_message);
            } else {
              let sys_message = {
                content: {},
                type: "applyTel",
              };
              this.now_chat.chat_logs.push(sys_message);
            }
            this.$nextTick(() => {
              this.scroolBottom();
            });
            break;
          default:
            break;
        }
      };
    },
    // 上传消息
    pushMsg(chat_id, type, content, is_online,is_auto=0) {
      this.now_chat.chat_logs.push();
      this.$http
        .saveMessage({
          chat_id,
          type,
          content: content,
          is_online,
          is_auto
        })
        .then((res) => {
          console.log(res.data.msg);
        });
    },
    // 处理接收的好友消息
    handleMessage(msg) {
      console.log(msg,"处理接收的好友消息");
      let from_friend_index;
      // 获取好友在好友列表中的位置
      for (let i = 0; i < this.friend_lsit.length; i++) {
        if (msg.from_id === this.friend_lsit[i].platform_id) {
          from_friend_index = i;
          break;
        }
      }
      // 如果是当前正在聊天的好友发过来的消息
      if (msg.from_id === this.now_chat.platform_id) {
        this.addChat(msg);
        return;
      }
      // 如果是好有列表中的好友发来的消息
      if (from_friend_index !== undefined) {
        console.log(msg.content);
        this.friend_lsit[from_friend_index].chat = {
          type: msg.type,
          time: msg.time,
          content: msg.content,
        };
        this.friend_lsit[from_friend_index].uncount++;
      } else {
        // 如果是一个新的好友发过来的消息
        var user_id = 0
        if(msg.from_id.split("_").length===3){
          user_id = parseInt(msg.from_id.split("_")[2])
        }
        let new_friend = {
          nickname: msg.nickname||msg.from_nickname,
          headimage: msg.headimage||msg.from_headimage,
          platform_id: msg.from_id,
          chat_id: msg.chat_id,
          user_id: user_id||'',
          uncount: 1,
          owner_id: 0,
          passive_id: 0,
          chat: {
            content: msg.content,
            type: msg.type,
            time: msg.time,
          },
        };
        this.friend_lsit.unshift(new_friend);
        from_friend_index = 0;
      }
      // 将好友移动到好友列表最上面
      if (from_friend_index > 0) {
        let new_chat = this.friend_lsit.splice(from_friend_index, 1);
        this.friend_lsit.unshift(new_chat[0]);
      }
    },
    // 显示表情集
    showFaceList() {},
    // 选择表情
    selectFace(face) {
      this.text_content += "[" + face + "]";
      this.$refs.inp.innerHTML = this.formatTextMsg(this.text_content);
    },
    // 在选择图片
    onChooseFile(event) {
      console.log(event)
    },
    // pushBlackMsg() {
    //   let blackMesage = {
    //     flag: "pullBlack",
    //     to_id: this.now_chat.platform_id,
    //     from_id: this.my_chat_info.platform_id,
    //   };
    //   this.ws.send(JSON.stringify(blackMesage));
    // },
    // 接收到被拉黑的消息
    handleBlack(from_id) {
      console.log(from_id,"黑名单消息");
    },
    handleRemoveBlack(from_id) {
      // 获取好友在好友列表中的位置
      for (let i = 0; i < this.friend_lsit.length; i++) {
        if (from_id === this.friend_lsit[i].platform_id) {
          // 去除黑名单标记
          this.friend_lsit[i].owner_id = 0;
          this.friend_lsit[i].passive_id = 0;
          break;
        }
      }
    },
    // 查看对方信息
    infoToogle() {
      console.log('查看对方信息')
      // this.show_info = !this.show_info;
      // this.getVisitorDetail();
    },
    /**
     * 发送对方给予评价的邀请消息
     */
    inviteEvaluate() {
      let msg = {
        flag: "inviteEvaluate",
        to_id: this.now_chat.id,
        from_id: this.my_chat_info.id,
      };
      this.ws.send(JSON.stringify(msg));
      this.$message.success("评价邀请已发出");
    },
    // 申请查看对方微信号
    applyWechat() {
      console.log('申请查看对方微信号');
      // var now_time = Date.parse(new Date());
      // if (this.apply_time && now_time - this.apply_time < 30000) {
      //   this.$message.warning("您的操作过于频繁");
      //   return;
      // }
      // this.apply_time = now_time;
      // this.sendMessage({ content: "apply_wechat", type: "apply_wx" });
      // this.statisticsApply(2);
      // var _this = this
      // var sys_info = { time:"刚刚", type: 'wechat', content: { name: this.friend_user_info.nickname, qrcode: this.friend_user_info.wechat_img, wechat: this.friend_user_info.wechat || '' } }
      // this.now_chat.chat_logs.push(sys_info)
      // this.$nextTick(function () {
      //     _this.scroolBottom()
      // })
    },
    sendWechat() {
      console.log("执行sendWechat")
      // this.$userApi.$http
      //   .get("/instantChat/confirmWechat.html", {
      //     params: { apply_id: this.friend_user_info.id },
      //   })
      //   .then(
      //     (res) => {
      //       if (res.data.code === 1) {
      //         var content = {
      //           name: this.my_chat_info.nickname,
      //           qrcode:
      //             this.my_chat_info.wechat_img || "",
      //           wechat:
      //             this.my_chat_info.wechat || "",
      //         };
      //         this.sendMessage({ content: content, type: "wechat" });
      //         this.staticAgree(2);
      //       } else {
      //         this.$message.warning(res.data.msg);
      //       }
      //     },
      //     (err) => {
      //       console.log(err);
      //     }
      //   );
    },
    // 申请查看手机号码
    applyTel: function() {
      // 如果当前登录用户是优选置业顾问可以直接
      if(this.my_user_info.is_optimization===1){
          if(!this.friend_user_info||!this.friend_user_info.tel){
              this.$message.warning('对方还没有绑定手机号')
              return
          }
          this.real_number = this.friend_user_info.tel
          this.showTelNum = true
          this.show_count_down = false
          return
      }
      var now_time = Date.parse(new Date())
      if (this.apply_time && now_time - this.apply_time < 30000) {
          this.$message.warning('您的操作过于频繁')
          return
      }
      this.apply_time = now_time
      this.sendMessage({content:'apply_tel',type:'apply_tel'})
      this.statisticsApply(1)

      now_time = Date.parse(new Date());
      if (this.apply_time && now_time - this.apply_time < 30000) {
        this.$message.warning("您的操作过于频繁");
        return;
      }
      this.apply_time = now_time;
      this.$userApi.$http
        .get("/instantChat/confirmTel.html", {
          params: { apply_id: this.friend_user_info.id },
        })
        .then(
          (res) => {
            if (res.data.code === 1) {
              this.real_number = this.friend_user_info.tel;
              this.showTelNum = true;
              this.show_count_down = false;
            } else {
              this.sendMessage({ content: "apply_tel", type: "apply_tel" });
              this.statisticsApply(1);
            }
          },
          (err) => {
            console.log(err);
          }
        );
    },
    /**
     * 将手机码发送给对方
     */
    sendTel: function() {
      console.log("将手机码发送给对方")
      // this.sendMessage({
      //   content: {
      //     name: this.my_chat_info.nickname,
      //     tel: this.my_chat_info.mobile,
      //   },
      //   type: "tel",
      // });
      // this.staticAgree(1);
    },
    statisticsApply: function(type) {
      console.log("统计申请", type);
      this.$userApi.statisticsApply(type, this.friend_user_info.id);
    },
    staticAgree: function(type) {
      console.log("统计同意", type);
      this.$userApi.statisticsAgree(type, this.friend_user_info.id);
    },
    handleAgree(type) {
      if (type === "tel") {
        this.sendTel();
      }
      if (type === "wechat") {
        this.sendWechat();
      }
    },
    /**
     * 获取访客轨迹
     */
    getVisitorDetail() {
      this.$userApi.visitorDetail(this.now_chat.user_id).then((res) => {
        if (res.data.list && res.data.list.length > 0) {
          this.visitor_detail = res.data.list;
        }
      });
    },
    toDetail(info, type) {
      console.log(info,"info")
      var commercial_catids = [
            'shangpu',
            'xiezilou',
            'changfang',
            'chewei',
            'tudizhuanrang'
          ]
      switch (type) {
        case "build":
          window.open(`https://yun.tfcs.cn/admin/#/house_detail?id=${info.id}&website_id=${this.website_ids}`);
          break;
        case "ershou":
          window.open("/info/" + info.id + ".html");
          break;
        case "renting":
          window.open("/info/" + info.id + ".html");
          break;
        case 'commercial':
            if(!info.catid){
              return
            }
            window.open("/"+commercial_catids[info.catid-1]+"/" + info.id + ".html");
            break
        case 'commercial_rent':
            if(!info.catid){
              return
            }
            window.open("/"+commercial_catids[info.catid-1]+"/" + info.id + ".html");
            break
        case 'commercial_transfer':
            if(!info.catid){
              return
            }
            window.open("/"+commercial_catids[info.catid-1]+"/" + info.id + ".html");
            break
        default:
          break;
      }
    },
    viewQrcode(img) {
      if (!img) {
        this.$message.warning("该用户还没有上传微信二维码");
        return;
      }
      this.viewImage(img, 300, 300);
    },
    /**
     * 预览图片
     */
    viewImage(img, width = 600) {
      if (width > 600) {
        width = 600;
      }
      this.img_width = width + "px";
      this.dialogImageUrl = img;
      setTimeout(() => {
        this.dialogVisible = true;
      }, 100);
    },
    /**
     * 对方邀请评价
     * @param {Object} e
     */
    handleInviteEvaluate(e) {
      if (e.from_id == this.now_chat.platform_id) {
        this.showEvaluate = true;
      }
    },
    subEvaluate() {
      this.$userApi
        .subEvaluate(this.now_chat.user_id, this.evaluate, this.content)
        .then((res) => {
          console.log(res.data);
          if (res.data.code === 1) {
            this.$message.success("提交成功");
            this.showEvaluate = false;
          } else {
            this.$message.warning(res.data.msg || "提交失败");
          }
        });
    },
    // 查看对方手机号
    getVirtualNumber() {
      this.showTelNum = true;
    },
    getVirtualNumberAgain() {
      if (this.time_down > 0) {
        return;
      }
    },
    // 关闭电话弹窗
    closeTelNum() {
      this.show_count_down = true;
      clearInterval(this.timer);
    },
    timeDown(time_out = 1000) {
      this.timer = setInterval(() => {
        if (this.time_down <= 0) {
          clearInterval(this.timer);
          return;
        }
        this.time_down--;
      }, time_out);
    },
    // 监听socket断开
    onSocketClose() {
      this.ws.onclose = (event) => {
        console.log("socket已断开：", event);
        this.socket_is_open = false;
        // this.$message("聊天已断开")
        if (this.close_type === "initiative") {
          return;
        }
        this.$confirm("聊天已断开，是否重新连接？", "提示", {
          confirmButtonText: "重新连接",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.connectSocketAgain();
        });
      };
    },
    // 将聊天内容滚动到最底部
    scroolBottom() {
      let chat_list_box = this.$refs.chat_list_box;
      let chat_list = this.$refs.chat_list;
      if (chat_list_box) {
        chat_list_box.scrollTop = chat_list.scrollHeight;
      }
    },
    // 点击最外层元素触发
    onClickChat() {
      this.show_face_list = false; // 关闭聊天标签弹出框
      this.show_fast_reply_list = false; // 关闭快捷回复弹出框
    },
    // 处理含有表情的消息
    formatTextMsg(content) {
      const regex = new RegExp(/\[(.+?)\]/, "gi");
      let html = content.replace(regex, (item, face_name) => {
        let face_index = this.face_list.indexOf(face_name);
        if (face_index > -1) {
          return `<img width="22" style="position:relative;top:5px;margin:0 3px" src="https://images.tengfangyun.com/images/face/${face_index}.png" />`;
        } else {
          return item;
        }
      });
      return html;
    },
    handleClickInfoer(){
      this.house_type = "ershou"
      this.info_list_page = 1;
      this.info_list_key = "";
      this.getHouseInfoList();
    },
    // 房源列表搜索房源
    onSearchInfo(){
      this.myHouseList_params.keyword = ""; // 清空房源名
      this.myHouseList_params.hid = ""; // 清空房源编号
      // 如果是NaN,代表搜索房源名
      if(isNaN(parseInt(this.info_list_key))) {
        this.myHouseList_params.keyword = this.info_list_key; // 赋值搜索参数
        this.myHouseList_params.page = 1;
        this.myHouseFormData_list = []; // 清空已有房源数据
        this.getMyHouseList()
      } else { 
        // 不是NaN,搜索房源编号
        this.myHouseList_params.hid = this.info_list_key; // 赋值搜索参数
        this.myHouseList_params.page = 1;
        this.myHouseFormData_list = []; // 清空已有房源数据
        this.getMyHouseList()
      }
    },
    // 房源列表滚动到底部触发的回调函数
    onInfoListScroll(){
      if(this.is_roll) {
        return;
      }
      this.myHouseList_params.page++;
      this.getMyHouseList();
    },
    onClickMask(){
      this.show_info = false
      this.show_house_info = false
    },
    // 发送房源
    sendPage(detail){
      console.log(detail,"detail");
      let msg = {}
      msg = {
        id: detail.id,
        title: detail.title + detail.shi + '室' + detail.ting + '厅' + detail.wei + '卫' + detail.mianji + 'm²' + detail.sale_price / 10000 + '万' + detail.trade_type,
        type: 'build',
        image: detail.pic
      }
      this.show_house_info = false
      this.sendMessage({content: msg, type: msg.type})
    },
    // 查看房源
    viewPage(detail){
      // 如果存在房源id
      if(!detail.id){
        return
      }
      // 打开房源链接
      // this.$goPath("house_detail?id=" + detail.id);
      window.open(`https://yun.tfcs.cn/admin/#/house_detail?id=${detail.id}&website_id=${this.website_ids}`);
    },
    // 会话详情 查看房源
    skipHouseDetails(id) {
      if(!id) {
        return
      }
      window.open(`https://yun.tfcs.cn/admin/#/house_detail?id=${id}&website_id=${this.website_ids}`);
    },
    // 获取我的房源列表
    getMyHouseList() {
      this.loading_info = "loading";
      this.$ajax.house.getList(this.myHouseList_params).then((res) => {
        if(res.status == 200) {
          // 关闭下拉请求接口
          if(res.data.data.length != this.myHouseList_params.rows) {
            this.is_roll = true;
          } else {
            this.is_roll = false;
          }
          this.myHouseFormData_list = this.myHouseFormData_list.concat(res.data.data);
          this.loading_info="nomore";
        }
      })
    },
    getNowDate() {
      let now = new Date();
      let year = now.getFullYear(); // 获取年份
      let month = now.getMonth() + 1; // 获取月份
      let day = now.getDate(); // 获取当前日
      let hour = now.getHours(); // 获取当前小时
      let minute = now.getMinutes(); // 获取当前分钟数
      let second = now.getSeconds(); // 获取当前秒
      let currentTime = year + '-' + month + '-' + day + ' ' + hour + ":" + minute + ":" + second;
      return currentTime;
    },
    // 上传成功回调
    uploadSuccessPicture(options = {}) {
      let msg = { type: "img", content: options.response.url, dialog_id: this.now_chat.id };
      this.sendMessage(msg);
    },
  },
  destroyed() {
    this.close_type = "initiative";
    this.ws.close();
  },
  activated() {
    this.scroolBottom();
  },
};
</script>

<style lang="scss" scoped>
.contents {
  background-color: #F1F4FA;
  margin: -15px;
}

.im-box {
  background: #f1f4fa;
  padding-top: 24px;

  .chat_bar {
    .tabs_box {
      ::v-deep .tabs_box_leftTop {
        padding: 10px 0;

        .el-input {
          width: 230px;

          .el-input__inner {
            width: 100%;
            height: 32px;
            line-height: 32px;
          }

          .el-input__prefix {
            i {
              line-height: 32px;
            }
          }
        }

        .chat_titles {
          display: inline-block;
          font-size: 18px;
          font-weight: bold;
          color: #333;
          flex: 1;
        }

        .clearUnread {
          display: inline-block;
          padding: 5px 10px;
          margin-left: 66px;
          font-size: 14px;
          color: #8a929f;
          cursor: pointer;
        }
      }
    }
  }
}

.el-container {
  height: 100%;
}

.el-main {
  padding: 0;
  margin-left: 24px;
  margin-right: 24px;
  border-radius: 4px;
  background: #fff;
}

@import "../css/im.scss";

.el-input {
  ::v-deep .el-input__prefix {
    i {
      font-size: 24px;
      color: #8a929f;
    }
  }
}

.friend_chat {
  position: relative;

  .not-message {
    position: absolute;
    top: 15px;
    left: 80px;
  }

  .issue {
    display: flex;
    flex-direction: column;
    padding: 15px;
    box-sizing: border-box;
    align-items: flex-start;

    .issue-box {
      width: 240px;
      // height: 300px;
      background-color: #F5F5F5;
      border-radius: 15px;
      padding: 10px;
      box-sizing: border-box;
      cursor: pointer;

      .issue-img {
        width: 100%;
        height: 200px;

        border-radius: 15px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 10px;
          object-fit: cover;
        }
      }

      .issue-text-content {
        margin-top: 8px;
        font-size: 14px;
        color: #333;
      }
    }
  }
}

.my_chat {
  position: relative;

  .not-message {
    position: absolute;
    top: 15px;
    right: 80px;
  }

  .issue {
    display: flex;
    flex-direction: column;
    padding: 15px;
    box-sizing: border-box;
    align-items: flex-end;

    .issue-box {
      width: 240px;
      // height: 300px;
      background-color: #F5F5F5;
      border-radius: 15px;
      padding: 10px;
      box-sizing: border-box;
      cursor: pointer;

      .issue-img {
        width: 100%;
        height: 200px;

        border-radius: 15px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 10px;
          object-fit: cover;
        }
      }

      .issue-text-content {
        margin-top: 8px;
        font-size: 14px;
        color: #333;
      }
    }
  }
}

.el-aside {
  overflow: hidden;
  padding: 0;

  .house_list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 24px;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
  }

  .list_container {
    height: 552px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.tabs_group {
  display: flex;
  justify-content: space-between;

  ::v-deep .tabs_house_type {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        .el-tabs__nav-scroll {
          .el-tabs__nav {
            .el-tabs__active-bar {
              height: 3px;
              background-color: rgba(45, 132, 251, 0);
            }

            .el-tabs__active-bar:after {
              content: "";
              height: 100%;
              width: 100%;
              position: absolute;
              background-color: #2d83fb;
              transform: scaleX(.7);
            }

            .el-tabs__item {
              color: #8a929f;
            }

            .el-tabs__item.is-active {
              color: #409EFF;
            }
          }
        }
      }

      .el-tabs__nav-wrap::after {
        height: 0px;
      }
    }
  }
}

// 加载中
.loading {
  position: relative;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-size: 14px;
  color: #8a929f;
}

.loading ::v-deep .el-loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading ::v-deep .el-loading-spinner .circular {
  width: 24px;
  height: 24px;
}

.friend_info {
  display: flex;
  flex-direction: row;

  .nickname {
    .levelname {
      margin-top: 2px;
    }
  }
}

.friend_top {
  display: flex;
  justify-content: space-between;
}

::v-deep .send_btn {
  color: #fff;
  background-color: #2d83fb;
  border-color: #2d83fb;
  font-size: 16px;
  padding: 7px 20px;

  &:hover {
    background: #579cfc;
    border-color: #579cfc;
    color: #fff;
    border-right-color: hsla(0, 0%, 100%, .5);
  }

  &:active {
    background: #2976e2;
    border-color: #2976e2;
    color: #fff;
  }
}

::v-deep .el-dropdown-menu__item {
  line-height: 27px;
  padding: 0 15px;
  font-size: 13px;
}

.el-dropdown-menu {
  padding: 6px 0;
}
</style>
