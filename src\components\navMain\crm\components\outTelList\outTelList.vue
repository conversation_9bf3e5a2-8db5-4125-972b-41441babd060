<template>
  <div class="table">
    <div style="margin-bottom: 20px;">
      <el-button :type="primary" @click="addExplicit">添加外显号码</el-button>
      <el-button :type="primary1" @click="BatchImport">批量导入</el-button>
    </div>
    <div class="add_tel" v-show="show1">
      <!-- <div class="add_title">添加外显号码：</div> -->
      <div class="tips mt10 mb10">
        <el-alert title="外显号码就是手机号码，客户接听电话时看到的手机号码就是该外显手机号码" type="warning" show-icon>
        </el-alert>
      </div>
      <el-form label-width="65px">
        <el-form-item label="姓名">
          <el-input class="w2220" v-model="tel_form.name" placeholder="请输入姓名">
          </el-input>
        </el-form-item>
      </el-form>
      <el-form label-width="89px">
        <!-- filterable
                  remote
                  :remote-method="getShowTelNumber" -->
        <el-form-item label="外显号码">
          <el-input class="w220" v-model="tel_form.phone" placeholder="请输入外显号码">
          </el-input>
        </el-form-item>
        <el-form-item label="关闭状态">
          <!-- <el-select v-model="tel_form.status">                   active-color="#13ce66"
                  inactive-color="#ff4949" -->
          <el-switch v-model="tel_form.is_disable" :active-value="0" :inactive-value="1">
          </el-switch>
        </el-form-item>
      </el-form>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancelAdd">取消</el-button> -->
      <div class="flex-row items-center j-center">
        <el-button class="mt20" type="primary" :loading="is_add_loading" @click="submitTel">提交</el-button>
      </div>

      <!-- </span> -->
    </div>
    <div class="Batch-Import" v-show="show">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <div>
        <el-button @click="onbatchimport">点击批量导入</el-button>
      </div>
    </div>
    <input v-if="is_dialog_upload" type="file" ref="file" style="display: none" v-on:change="handleFileUpload($event)" />
    <div class="add_title mt30">外显号码列表：</div>
    <div class="table_oper flex-row align-center">
      <div class="flex-1"></div>
      <div class="table_oper_item flex-row align-center">
        <div class="mr10">
          <el-radio size="small" v-model="all" :label="1" border @change="changeRadioAll">全部</el-radio>
          <el-checkbox border v-model="tel_params.is_disable" :false-label="1" :true-label="0" @change="changeCheckbox">{{
                      tel_params.is_disable === ""
                      ? "开启状态"
                      : tel_params.is_disable
                      ? "未开启"
                      : "已开启"
                      }}</el-checkbox>
          <el-checkbox border v-model="tel_params.status" :false-label="0" :true-label="1" @change="changeCheckbox">{{
                      tel_params.status === ""
                      ? "审核状态"
                      : tel_params.status
                      ? "通过"
                      : "未通过"
                      }}</el-checkbox>
          <!-- <el-radio
            size="small"
            v-model="tel_params.is_disable"
            :label="0"
            border
            @change="changeRadio"
            >已开启</el-radio
          > -->
        </div>
        <!-- <el-button size="small" type="primary" @click="addTel"
          >添加号码</el-button
        > -->
      </div>
    </div>
    <el-table v-loading="tel_table_loading" :data="tel_tableData" border :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row :row-style="$TableRowStyle">
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column label="ID" width="100" prop="id"></el-table-column>
      <el-table-column label="姓名" prop="name"> </el-table-column>
      <el-table-column label="号码" prop="phone"> </el-table-column>
      <el-table-column label="审核状态" prop="status" v-slot="{ row }">
        <el-tag :type="row.status == 1 ? 'success' : 'warning'">{{
                  row.status == 1 ? "审核通过" : "待审"
                  }}</el-tag>
      </el-table-column>
      <el-table-column label="禁用状态" prop="is_disable" v-slot="{ row }">
        <el-tag :type="row.is_disable == 0 ? 'success' : 'warning'">{{
                  row.is_disable == 0 ? "已启用" : "已禁用"
                  }}</el-tag>
      </el-table-column>
      <el-table-column label="添加时间" prop="created_at"> </el-table-column>

      <el-table-column label="操作" v-slot="{ row }">
        <!-- <el-button type="primary" class="mr10" @click="editTel(row)"
          >编辑</el-button
        > -->
        <!-- <el-popconfirm title="确定更改此号码的禁用状态吗？" class="mr10" @onConfirm="changeTelDisabled(row)"> -->
        <el-button slot="reference" :type="row.is_disable == 0 ? 'warning' : 'primary'" @click="changeTelDisabled(row)">{{
                  row.is_disable == 0 ? "禁用" :
                  "启用" }}</el-button>
        <!-- </el-popconfirm> -->
        <!-- <el-popconfirm
          title="确定删除此号码吗？"
          class="mr10"
          @onConfirm="delTel(row)"
        >
          <el-button slot="reference" type="warning">删除</el-button>
        </el-popconfirm> -->
      </el-table-column>
    </el-table>
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev,pager,next" :total="telTotal"
      :page-size="tel_params.per_page" :current-page="tel_params.page" @current-change="onTelPageChange"></el-pagination>

    <el-dialog width="500px" :title="telTitle" append-to-body :visible.sync="tel_edit">
      <el-form label-width="120px">
        <!-- filterable
                  remote
                  :remote-method="getShowTelNumber" -->
        <el-form-item label="外显号码">
          <el-input v-model="tel_form.phone" placeholder="请输入外显号码">
          </el-input>
        </el-form-item>
        <el-form-item label="状态">
          <!-- <el-select v-model="tel_form.status">                   active-color="#13ce66"
                  inactive-color="#ff4949" -->
          <el-switch v-model="tel_form.is_disable" :active-value="1" :inactive-value="2">
          </el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancelAdd">取消</el-button>
        <el-button type="primary" :loading="is_add_loading" @click="submitTel">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      primary: "primary",
      primary1: '',
      show1: true,
      show: false,
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过500条",
      ],
      file: "",
      is_dialog_upload: false,
      sub_loading: false,
      tel_params: {
        page: 1,
        per_page: 10,
        is_disable: '',
        status: '',
        phone: ""
      },
      telTotal: 0,
      tel_tableData: [],
      tel_table_loading: false,
      // 坐席添加编辑相关
      selectedIds: [], //默认选中
      memberList: [],  //部门列表
      show_select_dia: false, //选择人员弹框 
      show_number_list: [],  //外显号码列表
      tel_form: {
        name: '',
        is_disable: 1, //状态
        phone: ''
      },
      tel_user: {  //选择人员信息
        id: '',
        name: ''
      },
      tel_edit: false, //显示添加编辑坐席弹框
      telTitle: '', //添加编辑坐席的弹框标题
      telNumberLoading: false,
      is_add_loading: false,
      show_out_tel: false,
      isAdd: true,
      all: 1,
    }
  },
  created() {
    this.getTel()
  },
  methods: {
    //添加外显号码
    addExplicit() {
      this.show1 = true
      this.show = false
      this.primary = "primary"
      this.primary1 = ''
    },
    //批量导入
    BatchImport() {
      this.primary = ''
      this.primary1 = "primary"
      this.show = true
      this.show1 = false
      this.is_dialog_upload = true
    },
    //下载导入模板
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/Outbound_call.xlsx?f=" +
        +new Date()
      );
    },
    onbatchimport() {
      this.$refs.file.click();
    },
    handleFileUpload(event) {
      this.file = event.target.files[0];
      this.onUpload();
    },
    onUpload() {
      if (this.file) {
        let formData = new FormData();
        formData.append('file', this.file);
        this.$http.BatchImport(formData).then((res) => {
          if (res.status == 200) {
            // console.log(res.data);
            let text =
              "导入成功,总共" +
              res.data.data_cn +
              "条,成功" +
              res.data.data_success +
              "条,号码已存在" +
              res.data.data_exist +
              "条,失败" +
              res.data.data_error +
              "条";
            this.$message({
              type: "success",
              message: text
            })
          }
          this.tel_params.page = 1
          this.getTel()
          this.$refs.file.value = ''; // 清空已选择的文件
          this.is_dialog_upload = true


        })
      }

    },
    // 获取坐席列表
    getTel() {
      this.tel_table_loading = true
      let params = Object.assign({}, this.tel_params)
      if (this.tel_params.status === '') {
        delete params.status
      }
      if (this.tel_params.is_disable === '') {
        delete params.is_disable
      }
      this.$http.getShowTelNumber(params).then(res => {
        if (res.status == 200) {
          // console.log(res.data.data);
          this.tel_tableData = res.data.data
          this.telTotal = res.data.total
        }
        this.tel_table_loading = false
      }).catch(() => {
        this.tel_table_loading = false
      })
    },
    // 坐席列表页面更新
    onTelPageChange(val) {
      this.tel_params.page = val
      this.getTel()
    },
    // 添加编辑删除坐席
    addTel() {
      this.tel_form = {}
      this.tel_form.is_disable = 0
      this.tel_form.phone = ''
      // this.tel_from.name = ''
      this.isAdd = true
      this.telTitle = "添加号码"
      this.tel_edit = true


    },
    editTel(row) {
      this.isAdd = false
      this.tel_form.is_disable = row.is_disable
      this.tel_form.phone = row.phone

      this.tel_form.id = row.id


      this.telTitle = "编辑号码"
      this.tel_edit = true

    },
    delTel(row) {
      this.$http.delZuoxzi(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '删除成功')
          this.getTel()
        }

      }).catch(() => {

      })
    },
    submitTel() {
      if (this.isAdd) {
        this.addSubmit()
      } else {
        this.editSubmit()  //暂无此操作
      }
    },
    addSubmit() {
      this.is_add_loading = true
      // console.log(this.tel_form);
      this.$http.addOutTel(this.tel_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '添加成功')
          this.tel_form.phone = ''
          this.tel_form.name = ''
          this.all = 1
          this.tel_params.status = ''
          this.tel_params.is_disable = ''
          this.getTel()
          this.tel_edit = false
        }
        this.is_add_loading = false
      }).catch(() => {
        this.is_add_loading = false
      })
    },
    editSubmit() {

      this.is_add_loading = true
      this.$http.editOutTel(this.tel_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '编辑成功')
          this.getTel()
          this.tel_edit = false
        }
        this.is_add_loading = false
      }).catch(() => {
        this.is_add_loading = false
      })
    },
    cancelAdd() {
      this.tel_edit = false
    },
    changeTelDisabled(row) {
      console.log(row);0//启用  1// 禁用
      if(row.is_disable==0){
        this.$confirm('此操作将禁用此坐席, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.changeTelDisabled(row.id, { is_disable: 1 - (+row.is_disable) }).then(res => {
              console.log(res);
            if (res.status == 200) {
               this.$message.success(res.message || res.data?.message || '修改成功')
               this.getTel()
            }
           })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
      }else{
        this.$confirm('此操作将启用此坐席, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.changeTelDisabled(row.id, { is_disable: 1 - (+row.is_disable) }).then(res => {
              console.log(res);
            if (res.status == 200) {
               this.$message.success(res.message || res.data?.message || '修改成功')
               this.getTel()
            }
           })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
      }
      // row.is_disable
      // this.$http.changeTelDisabled(row.id, { is_disable: 1 - (+row.is_disable) }).then(res => {
      //   console.log(res);
      //   if (res.status == 200) {
      //     this.$message.success(res.message || res.data?.message || '修改成功')
      //     this.getTel()
      //   }
      // })
    },
    changeRadio() {
      this.tel_params.page = 1
      this.getTel()
    },
    changeCheckbox() {
      this.tel_params.page = 1
      this.all = ''
      this.getTel()
    },
    changeRadioAll() {
      this.tel_params.page = 1
      this.tel_params.status = ''
      this.tel_params.is_disable = ''
      this.getTel()
    },

  }
}
</script>

<style>
.mr10 {
  margin-right: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mt10 {
  margin-top: 10px;
}

.mt30 {
  margin-top: 30px;
}

.mb10 {
  margin-bottom: 10px;
}

.mt20 {
  margin-top: 10px;
}

.w220 {
  width: 220px;
}

.w2220 {
  width: 220px;
  margin-left: 24px;
}

.add_title {
  font-size: 16px;
  font-weight: 500;
}

.add_tel {
  padding-bottom: 30px;
  border-bottom: 1px solid #f1f1f1;
}

.Batch-Import {
  height: 300px;
}

.text {
  font-size: 14px;
  color: #8a929f;
  width: 70px;
  text-align: right;
  white-space: nowrap;
}

.labelrow {
  margin-bottom: 20px;
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  /* background: #e7f3fd; */
  padding: 15px 30px;
  /* margin-bottom: 30px; */
  font-size: 14px;
  color: #8a929f;
  /* color: #EBF0F7; */
}

.table {
  padding: 20px;
}

.table_oper {
  padding: 16px 0;
}
</style>