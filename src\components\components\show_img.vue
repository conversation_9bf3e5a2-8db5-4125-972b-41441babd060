<template>
  <div class="img_list div row">
    <div class="img-box " v-for="item in category_img" :key="item.id">
      <img width="148px" height="148px" :src="item.img" alt="" />
      <i @click="deleteImg(item.id)" class="el-icon-delete"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    build_id: [String, Number],
    category: [String, Number],
    categoryImgList: [Array],
  },
  data() {
    return {};
  },
  computed: {
    category_img() {
      return this.categoryImgList.filter((item) => {
        return item.category == this.category;
      });
    },
  },
  mounted() {},
  methods: {
    deleteImg(id) {
      this.$emit("deleteImg", id);
    },
  },
};
</script>

<style scoped lang="scss">
.img_list {
  justify-content: flex-start;
}
.img-box {
  position: relative;
  flex-direction: column;
  img {
    margin: 10px;
  }
  .el-icon-delete {
    padding: 4px;
    background: #999;
    border-radius: 10px;
    color: #fff;
    position: absolute;
    left: 0;
  }
}
</style>
