<template>
  <div class="content-box-crms" style="margin-bottom: 24px">
    <div class="div row align-center">
      <div class="img" v-if="img">
        <img :src="img" alt="" />
      </div>
      <div class="title_con flex-1">
        <div class="title flex-1">{{ name }}</div>
        <div class="tips" v-if="tips && tips.length">
          <div v-for="(item, index) in tips" :key="index">{{ item }}</div>
        </div>
      </div>
      <div class="add" v-if="showBtn">
        <el-button type="primary" @click="toAdd" size="mini">添加</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['from', 'showBtn'],
  data() {
    return {
      img: '',
      name: "",
      tips: []
    }
  },
  created() {
    switch (this.from) {
      case 'welcome_words':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/hyy.png'
        this.name = '员工欢迎语'
        this.tips = [
          "新建欢迎语后，客户通过搜索手机号等非员工活码渠道添加成员为好友时，系统自动回复欢迎语。"
        ]
        break;
      case 'group_welcome_words':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/hyy.png'
        this.name = '群欢迎语'
        this.tips = [
          "在这里创建入群欢迎语，群主可在群聊中选择并配置，客户进入群聊后，将收到欢迎语。"
        ]
        break;
      case 'crm_customer_member_qrcode':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/yuangonghuoma/yghm.png'
        this.name = '员工活码'
        this.tips = [
          "1. 新建渠道活码最多可发送10条消息（文字消息仅可发送1条，附加消息可发送9条）。",
          "2. 员工活码将在客户加为好友后20秒内发送，网络延迟可能造成发送失败。"
        ]
        break;
      case 'crm_customer_group_qrcode':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/yuangonghuoma/qhm.png'
        this.name = '群活码'
        this.tips = [
          "群活码最多只能选择5个群聊，群聊满200人后自动建群。"
        ]
        break;
      case 'crm_customer_group_sop':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/bzhsop.png'
        this.name = '群SOP'
        this.tips = [
          "创建群发消息，通知企微员工群发到客户群。注意：每个客户群每天可接收1条群发消息，不限为企业发布的群发还是个人发布的群发。"
        ]
        break;
      case 'crm_customer_member_sop':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/bzhsop.png'
        this.name = '员工SOP'
        this.tips = [
          "创建群发消息，通知企微员工群发给客户。注意：每位客户每天可以接收1条群发消息，不限企业发布的群发还是个人发布的群发。"
        ]
        break;
      case 'crm_customer_sop_log':
        this.img = ''
        this.name = '群SOP发送记录'
        // this.tips = [
        //   "创建群发消息，通知企微员工群发到客户群。注意：每个客户群每天可接收1条群发消息，不限为企业发布的群发还是个人发布的群发。"
        // ]
        break;
      case 'crm_customer_member_sop_log':
        this.img = ''
        this.name = 'SOP发送记录'
        // this.tips = [
        //   "创建群发消息，通知企微员工群发到客户群。注意：每个客户群每天可接收1条群发消息，不限为企业发布的群发还是个人发布的群发。"
        // ]
        break;
      case 'my_sop':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/bzhsop.png'
        this.name = '我的SOP'
        this.tips = [
          "创建群发消息，通知企微员工群发给客户。注意：每位客户每天可以接收1条群发消息，不限企业发布的群发还是个人发布的群发。"
        ]
        break;
      case 'crm_customer_my_qrcode':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/yuangonghuoma/yghm.png'
        this.name = '我的活码'
        this.tips = [
          "1. 新建渠道活码最多可发送10条消息（文字消息仅可发送1条，附加消息可发送9条）。",
          "2. 员工活码将在客户加为好友后20秒内发送，网络延迟可能造成发送失败。"
        ]
        break;
      case 'crm_my_group_sop':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/bzhsop.png'
        this.name = '我的群SOP'
        this.tips = [
          "创建群发消息，通知企微员工群发到客户群。注意：每个客户群每天可接收1条群发消息，不限为企业发布的群发还是个人发布的群发。"
        ]
        break;
      case 'crm_my_group_qrcode':
        this.img = 'https://img.tfcs.cn/backup/static/admin/crm/yuangonghuoma/qhm.png'
        this.name = '我的群活码'
        this.tips = [
          "群活码最多只能选择5个群聊，群聊满200人后自动建群。"
        ]
        break;



      default:
        break;
    }

  },
  methods: {
    toAdd() {
      this.$emit("add")
    }
  }
}
</script>

<style lang="scss" scoped>
.content-box-crms {
  padding: 24px;
  background: #fff;
  border-radius: 4px;

  .img {
    width: 80px;
    height: 80px;
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .title_con {
    align-self: flex-start;

    .title {
      font-size: 18px;
      margin-right: 10px;
      font-weight: 600;
    }

    .tips {
      margin-top: 10px;
      color: #a7a7a7;
      font-size: 14px;
    }
  }
}</style>