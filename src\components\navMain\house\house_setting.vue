<template>
  <div class="">
    <div class="pages_contents">
    <div class="check-box div row" id="pages_content">
      <div
        v-for="item in tabs_list"
        :class="{ isactive: item.title === is_tabs_page }"
        @click="onClickTabs(item)"
        :key="item.id"
        class="check-item"
      >
        {{ item.name }}
      </div>
    </div>
    <div
      v-if="is_tabs_page != 'defaultSetting'"
      style="background-color: #fff; padding-top: 24px; min-height: 110vh"
      :is="is_tabs_page"
    ></div>
    <el-row
      v-if="is_tabs_page == 'defaultSetting' && is_show_house"
      keep-alive
      :gutter="20"
      style="padding-top: 24px"
    >
      <el-col :span="24">
        <el-form :model="setting_form" label-width="200px">
          <!-- <el-form-item label="权限可见范围：">
            <div class="flex-row items-center">
              <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_setting"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  平台配置入口可见
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item> -->
          <el-form-item label="维护资料门牌号：">
            <div class="flex-row items-center">
              <!-- <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_house_room"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect> -->
              <el-select
                ref="auth_house_room"
                style="width: 300px"
                v-model="setting_form.auth_house_room"
                multiple
                placeholder="请选择"
                @focus="showPersonnelAuthority('auth_house_room')"
                @change="PersonnelChange"
              >
                <el-option
                  :disabled="true"
                  v-for="list in datalist"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  可直接修改门牌号
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="查看电话跟进异常提醒:">
            <div class="flex-row items-center">
              <el-select
                ref="auth_intelligent"
                style="width: 300px"
                v-model="setting_form.remind_phone_follow"
                placeholder="请选择"
              >
                <el-option
                  v-for="list in [
                    {
                      id: 1,
                      user_name: '开启',
                    },
                    {
                      id: 0,
                      user_name: '关闭',
                    },
                  ]"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  开启后如果有查看电话但未跟进的房源则强制去跟进
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <!-- <el-form-item label="聚焦房源权限：">
            <div class="flex-row items-center">
              <el-select
                ref="auth_is_top"
                style="width: 300px"
                v-model="setting_form.auth_is_top"
                multiple
                placeholder="请选择"
                @focus="showPersonnelAuthority('auth_is_top')"
                @change="PersonnelChange"
              >
                <el-option
                  :disabled="true"
                  v-for="list in datalist"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  可直接设置聚焦房源
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item> -->
          <el-form-item label="房源审批权限：">
            <div class="flex-row items-center">
              <!-- <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_approve"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect> -->
              <el-select
                ref="auth_approve"
                style="width: 300px"
                v-model="setting_form.auth_approve"
                multiple
                placeholder="请选择"
                @focus="showPersonnelAuthority('auth_approve')"
                @change="PersonnelChange"
              >
                <el-option
                  :disabled="true"
                  v-for="list in datalist"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">审批人</div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <!-- <el-form-item label="成交管理可见范围：">
            <div class="flex-row items-center">
              <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_transaction"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect>
              <el-tooltip class="item" effect="light" placement="right">
                  <div slot="content" style="max-width: 300px">
                    范围内数据可见
                  </div>
                  <i class="el-icon-info" style="color:#F56C6C;font-size: 20px;margin-top: 10px;margin-left: 10px;"></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="财务报告可见范围：">
            <div class="flex-row items-center">
              <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_finance"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect>
              <el-tooltip class="item" effect="light" placement="right">
                  <div slot="content" style="max-width: 300px">
                    范围内数据可见
                  </div>
                  <i class="el-icon-info" style="color:#F56C6C;font-size: 20px;margin-top: 10px;margin-left: 10px;"></i>
              </el-tooltip>
            </div>
          </el-form-item> -->
          <el-form-item label="智慧运营可见范围：">
            <div class="flex-row items-center">
              <!-- <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_intelligent"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect> -->
              <el-select
                ref="auth_intelligent"
                style="width: 300px"
                v-model="setting_form.auth_intelligent"
                multiple
                placeholder="请选择"
                @focus="showPersonnelAuthority('auth_intelligent')"
                @change="PersonnelChange"
              >
                <el-option
                  :disabled="true"
                  v-for="list in datalist"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  范围内数据可见
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="数据报告发送范围：">
            <div class="flex-row items-center">
              <!-- <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_report"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect> -->
              <el-select
                ref="auth_report"
                style="width: 300px"
                v-model="setting_form.auth_report"
                multiple
                placeholder="请选择"
                @focus="showPersonnelAuthority('auth_report')"
                @change="PersonnelChange"
              >
                <el-option
                  :disabled="true"
                  v-for="list in datalist"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  平台运营数据报告
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="房源管理员：">
            <div class="flex-row items-center">
              <!-- <mySelect
                :optionSource="user_list"
                v-model="setting_form.auth_house_manage"
                labelKey="user_name"
                valueKey="id"
                multiple
                width="300px"
                @page-change="onPageChange"
                :paginationOption="{
                  pageSize: params.per_page, //每页显示条数
                  currentPage: params.page, //当前页
                  pagerCount: 5, //按钮数，超过时会折叠
                  total: params.total, //总条数
                }"
              ></mySelect> -->
              <el-select
                ref="auth_house_manage"
                style="width: 300px"
                v-model="setting_form.auth_house_manage"
                multiple
                placeholder="请选择"
                @focus="showPersonnelAuthority('auth_house_manage')"
                @change="PersonnelChange"
              >
                <el-option
                  :disabled="true"
                  v-for="list in datalist"
                  :key="list.id"
                  :label="list.user_name"
                  :value="list.id"
                >
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  可修改房源角色
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="自定义外网标题推广卖点：">
            <el-radio v-model="setting_form.house_promotion" :label="1">开启</el-radio>
            <el-radio v-model="setting_form.house_promotion" :label="0">关闭</el-radio>
          </el-form-item>
          <el-form-item label="发送成员数据报告：">
            <el-radio v-model="setting_form.open_user_report" :label="1"
              >发送</el-radio
            >
            <el-radio v-model="setting_form.open_user_report" :label="0"
              >不发送</el-radio
            >
          </el-form-item>
          <el-form-item label="查看电话单日上限：">
            <el-input
              placeholder="请输入天数"
              style="width: 300px"
              v-model="setting_form.num_tel"
              min="0"
              step="1"
              type="number"
            ></el-input>
            <el-tooltip class="item" effect="light" placement="right">
              <div slot="content" style="max-width: 300px">0为不限制</div>
              <i
                class="el-icon-info"
                style="
                  color: #f56c6c;
                  font-size: 20px;
                  margin-top: 10px;
                  margin-left: 10px;
                "
              ></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="查看门牌单日上限：">
            <el-input
              placeholder="请输入天数"
              style="width: 300px"
              v-model="setting_form.num_room"
              min="0"
              step="1"
              type="number"
            ></el-input>
            <el-tooltip class="item" effect="light" placement="right">
              <div slot="content" style="max-width: 300px">0为不限制</div>
              <i
                class="el-icon-info"
                style="
                  color: #f56c6c;
                  font-size: 20px;
                  margin-top: 10px;
                  margin-left: 10px;
                "
              ></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="业主电话录入上限条数：">
            <div class="flex-row items-center">
              <el-input
                placeholder="请输入天数"
                style="width: 300px"
                v-model="setting_form.num_tel_house"
                min="0"
                step="1"
                type="number"
              ></el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">同一个手机号</div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="单日分享复制上限：">
            <div class="flex-row items-center">
              <el-input
                placeholder="请输入天数"
                style="width: 300px"
                v-model="setting_form.num_share"
                min="0"
                step="1"
                type="number"
              ></el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">复制文案次数</div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="隐私保护配置：">
            <div class="flex-row items-center">
              <el-input
                placeholder="请输入条数"
                style="width: 300px"
                v-model="setting_form.num_privacy"
                min="0"
                step="1"
                type="number"
              ></el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  成员可自主设置隐藏房源业主电话的条数
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="房源跟进最大置顶条数：">
            <div class="flex-row items-center">
              <el-input
                placeholder="请输入条数"
                style="width: 300px"
                v-model="setting_form.num_follow_top"
                min="1"
                max="5"
                step="1"
                type="number"
              ></el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  设置默认【2】最大【5】
                </div>
                <i
                  class="el-icon-info"
                  style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 10px;
                  "
                ></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="房源核验编码：">
            <el-radio v-model="setting_form.house_code" :label="1"
              >开启</el-radio
            >
            <el-radio v-model="setting_form.house_code" :label="0"
              >禁用</el-radio
            >
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onClickForm">确认</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <myEmpty v-if="is_show_house == false" desc="当前用户不可查看"></myEmpty>
    <el-dialog
      :visible.sync="show_add_member"
      width="400px"
      :title="department_title"
      append-to-body
    >
      <div class="member" ref="memberList">
        <multipleTree
          v-if="show_add_member"
          :list="serverData"
          :defaultValue="selectedIds"
          @onClickItem="selecetedMember"
          :defaultExpandAll="false"
        >
        </multipleTree>
        <div
          style="margin-top: 20px; justify-content: space-around"
          class="footer flex-row align-center"
        >
          <el-button type="text" @click="show_add_member = false"
            >取消</el-button
          >
          <el-button type="primary" @click="selectMemberOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
  </div>
</template>

<script>
import mySelect from "../crm/components/my_select";
import myEmpty from "@/components/components/my_empty.vue";
import vrSetting from "../crm/crm_customer_vr_setting"; //vr
import xflSetting from "./xflSetting.vue"
import auditCommunity from "./components/auditCommunity.vue"
import houseSetting from "./components/houseSetting.vue"
import houseFallRule from "./components/houseFallRule.vue"
import { mapState } from "vuex";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
  components: {
    mySelect,
    myEmpty,
    vrSetting,
    xflSetting,
    auditCommunity,
    houseSetting,
    multipleTree,
    houseFallRule
  },
  data() {
    return {
      tabs_list: [{ id: 1, name: "基本配置", title: "defaultSetting" },
      { id: 2, name: "幸福里配置", title: "xflSetting" },
      {
        id: 3, name: "VR配置", title: "vrSetting"
      },
      {
        id: 4, name: "小区申请", title: "auditCommunity"
      },
      {
        id: 5, name: "房源评分", title: "houseSetting"
      },
      {
        id: 6, name: '掉公规则', title: "houseFallRule"
      }
      ],
      is_tabs_page: "defaultSetting",
      setting_form: {
        auth_setting: "", //权限设置可见范围：默认为空（创始人可见，其他人按钮不可见 ， 可选多人，范围内显示按钮）
        auth_house_room: "", // 维护资料门牌号 ：默认创始人（可选多人）
        auth_is_top: "", //聚焦房源权限 ：默认创始人（可选多人）
        auth_approve: "", //成交指定审批： 默认空 为全局（延续前台指定谁 谁审批），有指定审批人，发起审批模版审批人直接带出，不可选择。
        auth_transaction: "", //成交管理可见范围：默认为空（为空全局都可进，可选多人
        auth_finance: "", //财务报告可见范围：默认为空（为空全局都可进，可选多人
        auth_intelligent: "", //智慧运营可见范围：默认为空（为空全局都可进，可选多人）
        num_tel: 30, //查看电话单日上限：默认30 （0为不限制
        num_room: 50, //查看门牌单日上限：默认50 （0不限制）
        num_tel_house: 3, //业主电话录入上限条数 默认3
        num_share: 20, //单日分享复制上限 默认20次 ，0不限制
        house_promotion: 0, // 自定义外网房源推广标题 0 关闭 1 开启
        open_user_report: 1, // 发送成员数据报告 1 发送 0 不发送
        auth_report: "", // 数据报告发送范围
        auth_house_manage: "", // 房源管理员
        num_privacy: 3, //隐私保护条数
        num_follow_top: "", // 单房源跟进最大置顶条数
        house_code: 0, // 房源核验编码 1开启 0 关闭
        remind_phone_follow: 0,  //开启后 如果有查看电话但未跟进的房源强制跳转去跟进
      },
      params: {
        page: 1,
        per_page: 1000,
      },
      user_list: [],
      is_show_house: false,
      show_add_member: false, // 部门成员模态框
      selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
      selectedDeal: [], // 成交权限范围 默认勾选和展开的节点的 key 的数组
      storageKey: "",
      identification: "",
      department_title: "",
      datalist: [], // 全部部门人员
      serverData: [], // 部门成员数据
    };
  },
  mounted() {
    this.getHouseBusiness();
    if (this.website_info.open_vr != 1) {
      this.tabs_list.splice(2, 1)
      // this.tabs_list.push({
      //   id: 3, name: "VR配置", title: "vrSetting"
      // })
    }
    this.getMemberList();
  },
  computed: {
    ...mapState(["website_info"]),
  },
  methods: {
    getHouseBusiness() {
      this.$http.getCrmIntelligentRateHouse().then((res) => {
        if (res.status === 200) {
          if (res.data.auth_setting) {
            this.is_show_house = res.data.auth_setting;
            // this.getHouseSettingData();
            this.getHousePersonList();
          }
        }
      });
    },
    //切换Tables
    onClickTabs(item) {
      if (item.name != "基本配置") {
        this.is_show_house = true
      }
      this.is_tabs_page = item.title
    },
    getHouseSettingData() {
      this.$http.getHouseSettingData().then((res) => {
        if (res.status === 200) {
          this.setting_form = res.data;
          this.setting_form.auth_setting = res.data.auth_setting
            ? this.setArr(res.data.auth_setting)
            : "";
          this.setting_form.auth_house_room = res.data.auth_house_room
            ? this.setArr(res.data.auth_house_room)
            : "";
          this.setting_form.auth_is_top = res.data.auth_is_top
            ? this.setArr(res.data.auth_is_top)
            : "";
          this.setting_form.auth_approve = res.data.auth_approve
            ? this.setArr(res.data.auth_approve)
            : "";
          this.setting_form.auth_transaction = res.data.auth_transaction
            ? this.setArr(res.data.auth_transaction)
            : "";
          this.setting_form.auth_finance = res.data.auth_finance
            ? this.setArr(res.data.auth_finance)
            : "";
          this.setting_form.auth_intelligent = res.data.auth_intelligent
            ? this.setArr(res.data.auth_intelligent)
            : "";
          this.setting_form.auth_report = res.data.auth_report
            ? this.setArr(res.data.auth_report)
            : "";
          this.setting_form.auth_house_manage = res.data.auth_house_manage
            ? this.setArr(res.data.auth_house_manage)
            : "";
        }
      });
    },
    onPageChange(e) {
      this.params.page = e;
      this.getHousePersonList();
    },
    getHousePersonList() {
      this.$http.getHousePersonList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.user_list = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    // 处理简单数据
    setArrMini(arr) {
      let n_arr = (arr).split(",");
      let n_arr_2 = n_arr.map((item) => {
        return parseInt(item);
      });
      return n_arr_2;
    },
    // 处理多选成员数据
    setArr(arr) {
      let n_arr = (arr).split(",");
      let n_arr_2 = n_arr.map((item) => {
        return parseInt(item);
      });
      // ====
      let i = 0;
      if (n_arr_2 != [] && n_arr_2 != undefined) {
        n_arr_2.map((item) => {
          this.$nextTick(() => {
            this.datalist.map((list) => {
              if (item != list.id) {
                i++;
                if (i == this.datalist.length) {
                  n_arr_2.splice(n_arr_2.indexOf(item), 1);
                  // console.log(n_arr_2,"观察");
                }
              }
            })
            i = 0;
          })
        })
      }
      // ====
      return n_arr_2;
    },
    onClickForm() {
      this.setting_form.auth_setting = this.setting_form.auth_setting
        ? this.setting_form.auth_setting.join(",")
        : "";
      let result = [];
      if (this.setting_form.auth_house_room != [] && this.setting_form.auth_house_room != '') {
        this.setting_form.auth_house_room.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.setting_form.auth_house_room = Array.from(new Set(result));
      this.setting_form.auth_house_room = this.setting_form.auth_house_room
        ? this.setting_form.auth_house_room.join(",")
        : "";
      result = [];
      if (this.setting_form.auth_is_top != [] && this.setting_form.auth_is_top != '') {
        this.setting_form.auth_is_top.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.setting_form.auth_is_top = Array.from(new Set(result));
      this.setting_form.auth_is_top = this.setting_form.auth_is_top
        ? this.setting_form.auth_is_top.join(",")
        : "";
      result = [];
      if (this.setting_form.auth_approve != [] && this.setting_form.auth_approve != '') {
        this.setting_form.auth_approve.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.setting_form.auth_approve = Array.from(new Set(result));
      this.setting_form.auth_approve = this.setting_form.auth_approve
        ? this.setting_form.auth_approve.join(",")
        : "";
      this.setting_form.auth_transaction = this.setting_form.auth_transaction
        ? this.setting_form.auth_transaction.join(",")
        : "";
      this.setting_form.auth_finance = this.setting_form.auth_finance
        ? this.setting_form.auth_finance.join(",")
        : "";
      result = [];
      if (this.setting_form.auth_intelligent != [] && this.setting_form.auth_intelligent != '') {
        this.setting_form.auth_intelligent.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.setting_form.auth_intelligent = Array.from(new Set(result));
      this.setting_form.auth_intelligent = this.setting_form.auth_intelligent
        ? this.setting_form.auth_intelligent.join(",")
        : "";
      result = [];
      if (this.setting_form.auth_report != [] && this.setting_form.auth_report != '') {
        this.setting_form.auth_report.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.setting_form.auth_report = Array.from(new Set(result));
      this.setting_form.auth_report = this.setting_form.auth_report
        ? this.setting_form.auth_report.join(",")
        : "";
      result = [];
      if (this.setting_form.auth_house_manage != [] && this.setting_form.auth_house_manage != '') {
        this.setting_form.auth_house_manage.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.setting_form.auth_house_manage = Array.from(new Set(result));
      this.setting_form.auth_house_manage = this.setting_form.auth_house_manage
        ? this.setting_form.auth_house_manage.join(",")
        : "";
      this.$http.setHouseSettingData(this.setting_form).then((res) => {
        if (res.status === 200) {
          this.setting_form.auth_setting = this.setting_form.auth_setting
            ? this.setArr(this.setting_form.auth_setting)
            : "";
          this.setting_form.auth_house_room = this.setting_form.auth_house_room
            ? this.setArr(this.setting_form.auth_house_room)
            : "";
          this.setting_form.auth_is_top = this.setting_form.auth_is_top
            ? this.setArr(this.setting_form.auth_is_top)
            : "";
          this.setting_form.auth_approve = this.setting_form.auth_approve
            ? this.setArr(this.setting_form.auth_approve)
            : "";
          this.setting_form.auth_transaction = this.setting_form
            .auth_transaction
            ? this.setArr(this.setting_form.auth_transaction)
            : "";
          this.setting_form.auth_finance = this.setting_form.auth_finance
            ? this.setArr(this.setting_form.auth_finance)
            : "";
          this.setting_form.auth_intelligent = this.setting_form
            .auth_intelligent
            ? this.setArr(this.setting_form.auth_intelligent)
            : "";
          this.setting_form.auth_report = this.setting_form.auth_report
            ? this.setArr(this.setting_form.auth_report)
            : "";
          this.setting_form.auth_house_manage = this.setting_form
            .auth_house_manage
            ? this.setArr(this.setting_form.auth_house_manage)
            : "";
          this.$message.success("操作成功");
          this.getHouseSettingData();
          // this.$router.push("/crm_customer_business");
        } else {
          this.setting_form.auth_setting = this.setting_form.auth_setting
            ? this.setArr(this.setting_form.auth_setting)
            : "";
          this.setting_form.auth_house_room = this.setting_form.auth_house_room
            ? this.setArr(this.setting_form.auth_house_room)
            : "";
          this.setting_form.auth_is_top = this.setting_form.auth_is_top
            ? this.setArr(this.setting_form.auth_is_top)
            : "";
          this.setting_form.auth_approve = this.setting_form.auth_approve
            ? this.setArr(this.setting_form.auth_approve)
            : "";
          this.setting_form.auth_transaction = this.setting_form
            .auth_transaction
            ? this.setArr(this.setting_form.auth_transaction)
            : "";
          this.setting_form.auth_finance = this.setting_form.auth_finance
            ? this.setArr(this.setting_form.auth_finance)
            : "";
          this.setting_form.auth_intelligent = this.setting_form
            .auth_intelligent
            ? this.setArr(this.setting_form.auth_intelligent)
            : "";
          this.setting_form.auth_report = this.setting_form.auth_report
            ? this.setArr(this.setting_form.auth_report)
            : "";

          this.setting_form.auth_house_manage = this.setting_form
            .auth_house_manage
            ? this.setArr(this.setting_form.auth_house_manage)
            : "";
        }
      });
    },
    showPersonnelAuthority(val) {
      this.identification = val
      if (this.identification == 'auth_house_room' && this.setting_form.auth_house_room != '') {
        this.selectedIds = this.setting_form.auth_house_room;
        this.department_title = '维护资料门牌号';
      } else if (this.identification == 'auth_is_top' && this.setting_form.auth_is_top != '') {
        this.selectedIds = this.setting_form.auth_is_top;
        this.department_title = '聚焦房源权限';
      } else if (this.identification == 'auth_approve' && this.setting_form.auth_approve != '') {
        this.selectedIds = this.setting_form.auth_approve;
        this.department_title = '房源审批权限';
      } else if (this.identification == 'auth_intelligent' && this.setting_form.auth_intelligent != '') {
        this.selectedIds = this.setting_form.auth_intelligent;
        this.department_title = '智慧运营可见范围';
      } else if (this.identification == 'auth_report' && this.setting_form.auth_report != '') {
        this.selectedIds = this.setting_form.auth_report;
        this.department_title = '数据报告发送范围';
      } else if (this.identification == 'auth_house_manage' && this.setting_form.auth_house_manage != '') {
        this.selectedIds = this.setting_form.auth_house_manage;
        this.department_title = '房源管理员';
      } else {
        this.selectedIds = [];
      }
      // this.storageKey = item.key;
      this.$nextTick(() => {
        this.$refs.auth_house_room.blur();
        // this.$refs.auth_is_top.blur();
        this.$refs.auth_approve.blur();
        this.$refs.auth_intelligent.blur();
        this.$refs.auth_report.blur();
        this.$refs.auth_house_manage.blur();
      })
      this.show_add_member = true;
    },
    PersonnelChange(val) {
      this.selectedIds = val;
    },
    // 获取部门成员列表
    async getMemberList() {
      await this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
          // console.log(this.datalist);
        }
      })
      this.getHouseSettingData();
    },
    // 递归数据处理
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    selectMemberOk() {
      this.show_add_member = false;
      if (this.identification == 'auth_house_room') {
        this.setting_form.auth_house_room = this.selectedIds;
      } else if (this.identification == 'auth_is_top') {
        this.setting_form.auth_is_top = this.selectedIds;
      } else if (this.identification == 'auth_approve') {
        this.setting_form.auth_approve = this.selectedIds;
      } else if (this.identification == 'auth_intelligent') {
        this.setting_form.auth_intelligent = this.selectedIds;
      } else if (this.identification == 'auth_report') {
        this.setting_form.auth_report = this.selectedIds;
      } else if (this.identification == 'auth_house_manage') {
        this.setting_form.auth_house_manage = this.selectedIds;
      }
    },
    // 选中变化时触发
    selecetedMember(e) {
      this.selectedIds = e.checkedKeys;
      // this.selectedList = e.checkedNodes;
    },
  },
};
</script>

<style scoped lang="scss">
.pages_contents{
  max-height: calc(100vh - 262px); /* 限制最大高度为视口高度 */
  overflow-y: auto;  /* 添加垂直滚动条，按需显示 */
  // margin: -15px;
  padding: 24px;
  // .bor-t{
  //   margin-right: 0px !important;
  // }
}
.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  margin-left: 20px;
  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;
    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}
#pages_content {
  margin-left: 80px;
}
</style>
