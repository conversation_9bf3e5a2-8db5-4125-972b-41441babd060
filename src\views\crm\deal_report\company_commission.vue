<template>
    <div class="company-comm">
        <div class="header" v-if="!preview">
            <el-button type="primary" @click="add()">添加</el-button>
        </div>
        <el-table v-loading="loading" :data="list" class="house_table" border
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
            <el-table-column label="操作时间" v-slot="{ row }"  v-if="!preview">
                {{ row.created_at }}
            </el-table-column>
            <el-table-column label="扣除项类型" v-slot="{ row }">
                {{ row.cate_name }}
            </el-table-column>
            <el-table-column label="比例" v-slot="{ row }">
                {{ row.proportion }}
            </el-table-column>
            <el-table-column label="扣除项金额" v-slot="{ row }">
                {{ row.commission }}
            </el-table-column>
            <el-table-column label="备注" v-slot="{ row }">
                {{ row.descp }}
            </el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }" v-if="!preview">
                <el-link style="margin-right: 20px;" type="primary" @click="edit(row)">编辑</el-link>
                <el-link type="danger" @click="del(row)">删除</el-link>
            </el-table-column>
        </el-table>
        <div class="footer">
            <div></div>
            <div class="commission-text" v-if="companyCommissionCount">
                扣除款项合计：<span class="commission-text-red">{{companyCommissionTotal}}</span> 元
            </div>
        </div>
    
        <addCompanyCommission v-if="dialogs.add" ref="add"/>
    </div>
</template>

<script>
import addCompanyCommission from './add_company_commission.vue'
export default {
    name: 'crmDealCompanyCommission',
    components: {
        addCompanyCommission
    },
    props: {
        reportData: {type: Object, default: ()=>{return {}}},
        preview: {type: Boolean, default: false}
    },
    data(){
        return {
            loading: false,
            list: [],
            dialogs: {
                add: false
            }
        }
    },
    computed: {
        //公司佣金量
        companyCommissionCount(){
            return this.reportData?.company_commission?.count || 0;
        },
        //公司佣金合计
        companyCommissionTotal(){
            return this.reportData?.company_commission?.commission || 0;
        },
    },
    created(){
        this.getList();
    },
    methods: {
        async getList(){
            this.loading = true;
            const res = await this.$http.getCompanyCommissionListAPI(this.reportData.report_id);
            this.loading = false;
            if(res.status == 200){
                this.list = res.data;
            }
        },
        async add(data = {}){
            this.dialogs.add = true; 
            await this.$nextTick();

            data.report_id = this.reportData.report_id;
            let amount = this.reportData.receive_commission.commission;
            if(isNaN(amount)) {
                amount = 0
            }
            data.amount = amount;

            this.$refs.add.open(data).onSuccess(()=>{
                this.getList();
                this.$emit('commissionChange', 'companyCommission');
            });
        },
        edit(data){
            this.add(data);
        },
        async del(data){
            const res = await this.$http.delCompanyCommissionAPI(data.id);
            if(res.status == 200){
                this.$message.success( res.data?.msg || "删除成功");
                this.getList();
                this.$emit('commissionChange', 'companyCommission');
            }
        }
    }
        
}
</script>
<style lang="scss" scoped>
.company-comm{
    .header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
   .footer{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 10px;
       .commission-text{
            font-size: 16px;
            color: #3c3c3c;
        }
        .commission-text-red{
            color: #f40;
            font-weight: 600;
        }   
    }
}
</style>