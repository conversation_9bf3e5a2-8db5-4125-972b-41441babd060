<template>
  <div v-loading.fullscreen.lock="fullscreenLoading">
    <!-- <el-dialog
      :visible.sync="show_dialog_list"
      width="300px"
      title="绑定到抖音"
    >
      <iframe
        style="border: none"
        width="300px"
        height="300px"
        v-bind:src="inframe"
      ></iframe>
    </el-dialog> -->
  </div>
</template>

<script>
export default {
  created() {
    this.isMobile = window.navigator.userAgent.match(
      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    );
    // let from = this.getUrlKey("from");
    this.from = this.getUrlKey("from");
    let a = location.href
    if (a.indexOf("?code=") >= 0) {
      let params = this.$route.query
      // let params = this.$queryUrlParams(a.split("#")[0]);
      // let params1 = this.$queryUrlParams(a.split("#")[1]);
      if (params.code && params.id) {
        this.website_id = params.website_id
        this.bindDouyin(params.id, params.code)
        return
      }
    }
    if (this.from == 'authorization_douyin') {
      // if (a.indexOf('?authorization_code=' >= 0) || (this.$route.query && this.$route.query.authorization_code)) {
      //不确定 authorization_code 返回的位置 判断下
      if (a.indexOf("?authorization_code=") >= 0) {
        // let params = this.$queryUrlParams(a.split("#")[0]);
        let params = this.$route.query
        if (params.authorization_code) {

          this.authorizationDouyin(params.authorization_code)
          return
        }
      }
      // 暂时用不到 链接拼接到了前边
      // if (this.$route.query && this.$route.query.authorization_code) {
      //   this.authorizationDouyin(this.$route.query.authorization_code)
      //   return
      // }
      return
      // }
    }
    if (this.from == 'tDouyinShouquan') {
      if (a.indexOf("?authorization_code=") >= 0) {
        let params = this.$queryUrlParams(a.split("#")[0]);
        if (params.authorization_code) {

          this.authorizationTDouyin(params.authorization_code)
          return
        }
      }
      // 暂时用不到 链接拼接到了前边
      // if (this.$route.query && this.$route.query.authorization_code) {
      //   this.authorizationDouyin(this.$route.query.authorization_code)
      //   return
      // }
      return
      // }
    }
    if (this.from == 'douyin') {
      this.getDouyinInfo()
      return
    }
    var token = this.getUrlKey("token");
    if (token) {
      this.getInfo(token)
    }

  },
  data() {
    return {
      fullscreenLoading: false,
      isMobile: false,
      douyinImg: "",
      show_dialog_list: false,
      inframe: '',
      from: ""
    }
  },

  methods: {
    // 获取路径中的参数（code等）
    getUrlKey(name) {
      return (
        decodeURIComponent(
          (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
            location.href
          ) || ["", ""])[1].replace(/\+/g, "%20")
        ) || null
      );
    },
    getInfo(token) {
      // token
      this.$http.getTokenInfo({ token }).then((res) => {
        if (res.status == 200) {
          localStorage.setItem("TOKEN", res.data.token)
          localStorage.setItem("website_id", res.data.website_id)
          // console.log(res.data.type);

          let type = (res.data.type + '').split("-"), url = ''
          // 1新房 2 CRM 绑定坐席 3 房源 4 企微助理 6 抖音 5 呼叫中心
          switch (+type[0]) {
            case 0:
              sessionStorage.removeItem("top_select_id")
              url = '/'
              this.$goPath(url)
              break;
            case 1:

              break;
            case 2:
            sessionStorage.setItem("top_select_id", +type[0])
              if(type[1]&&type[1]=="chrome_extention"){
                url = "/crm_index?website_id=" + res.data.website_id
              }else{
              url = "/crm_customer_business_setting?&type=outbound&website_id=" + res.data.website_id
              for (let index = 1; index < type.length; index++) {
                url += '&query_type' + index + '=' + type[index]
              }
              } 
              this.$goPath(url)
              break;
            case 3:

              break;
            case 4:

              break;
            case 6:
              url = '/' + type[1]
              sessionStorage.setItem("top_select_id", +type[0])
              this.$goPath(url)
              break;

            default:
              break;
          }
        }
      })
    },
    getDouyinInfo() {
      let href = location.href
      href = href.replace("#", "@@@@")
      href = href.replace("&from=", "@@@@@@from@@@@@@")
      href = href.replace("&website_id=", "@@@@@@website_id@@@@@@")
      href = href.replace("&id=", "@@@@@@id@@@@@@")
      this.$http.getDouyinShouquanNoLogin(href).then(res => {
        console.log(res);
        if (res.status == 200) {
          const url = res.data
          this.$confirm("确定授权抖音吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            // this.show_dialog_list = true
            window.location.href = url
          }).catch(() => {
            this.$message.info("已取消")
          })
          // console.log(url);
          // this.douyinImg = url
        }
      })
    },
    bindDouyin(id, code) {
      this.$http.bindDouyinMember(id, code).then(res => {
        if (res.status == 200) {
          this.$message.success('绑定成功')
          setTimeout(() => {
            let url = ''
            if (this.isMobile) {
              url = location.origin + location.pathname + "#/douyin_index?website_id=" + this.website_id;
              window.open(url)
            } else {
              sessionStorage.setItem("top_select_id", 6)
              url = location.origin + location.pathname + "#/douyin_index?website_id=" + this.website_id;
              window.open(url)
            }
          }, 1000);
        }
      }).catch(() => {
        this.is_table_loading = false
      })
    },
    authorizationDouyin(authorization_code) {
      this.fullscreenLoading = true
      this.$http.authorizationDouyin(authorization_code).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success('授权成功')
        }
        this.fullscreenLoading = false
      }).catch(() => {
        this.fullscreenLoading = false
      })
    },
    authorizationTDouyin(authorization_code) {
      this.fullscreenLoading = true
      this.$http.authorizationTDouyin(authorization_code).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success('授权成功')
          setTimeout(() => {
            history.go(-1)
          }, 300);

        }
        this.fullscreenLoading = false
      }).catch(() => {
        this.fullscreenLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>