<template>
    <div>
        <el-dialog :title="tip" :visible.sync="dialogVisible" width="30%"
        class="dialog"
          :before-close="handleClose">
          <div class="levelstyle"> 
            <!-- 批量修改客户等级 -->
            <div v-if="show_id==3">
                <el-radio-group v-model="level_id" v-for="item in leveldata" :key="item.id">
                    <el-radio style="margin-right:20px;" :label="item.id" border>{{item.title}}</el-radio>
                </el-radio-group>
            </div>
            <!-- 批量转公 -->
            <div v-if="show_id==5">
              <el-input type="textarea" placeholder="请输入内容" v-model="content" show-word-limit>
              </el-input>
            </div>
            <!-- 批量变更角色 -->
            <div v-if="show_id==6">
              <el-radio-group v-model="changerole.type" v-for="item in member_list" :key="item.id">
                    <el-radio style="margin-right:20px;" :label="item.id" border>{{item.user_name}}</el-radio>
              </el-radio-group>
              <portrait_single style="margin-top:20px;" :list="memberList" @onClickItem="selecetedMember" :getCheckedNodes="false"></portrait_single>
              <div class="lookstyle" v-if="changerole.type==3">
                <el-checkbox v-model="changerole.is_sync" true-label="1" false-label="0">是否同步添加带看记录</el-checkbox>
              </div>
            </div>
            <!-- 批量更新客户状态 -->
            <div v-if="show_id==7">
              <el-radio-group v-model="tracking_id" v-for="item in status_list" :key="item.id">
                    <el-radio style="margin-right:10px;" :label="item.id" border>{{item.title}}</el-radio>
              </el-radio-group>
            </div>
            <div class="prompt">
              <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"></i>
              <span>当前已选择{{multipleSelection.length}}条，执行后不可恢复，请确认后再执行操作</span>
            </div>
            <!-- <div>
                <el-progress v-show="showProgressBar" :percentage="progressPercentage"></el-progress>
            </div> -->
          </div>


       
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="sublevel">确 定</el-button>
          </span>
        </el-dialog>
    </div>
</template>
<script>
import portrait_single from "@/components/navMain/crm/components/portrait_single.vue"
export default {
  components:{
    portrait_single
  },
    props:{
        leveldata:{
            type:Array,
            default:()=>[]
        },
        member_listNEW:{
          type:Array,
          default:()=>[]
        },
        memberList:{
          type:Array,
          default:()=>[]
        },
        status_list:{
          type:Array,
          default:()=>[]
        }
    },
    data(){
        return{
            dialogVisible:false,
            level_id:"",//要修改的等级id
            multipleSelection:[],//选中的客户
            showProgressBar: false, // 是否显示进度条
            progressPercentage: 0, // 进度条百分比
            show_id:"",
            content:"",//转公原因
            member_list:[],
            tip:"",
            userData:{},
            changerole:{
              type:"",//角色id 
              admin_id:"",//成员id
              is_sync:0,//是否同步
            },
            tracking_id:"",//客户状态id
           
        }
    },
    created(){
      this.member_list = this.member_listNEW.filter(item => item.user_name !== "全部");
    },
    methods: {
        open(data,id){
            this.show_id = id
            if(id==3){
              this.tip = "批量修改等级"
            }else if(id==5){
              this.tip = "批量转交公海"
            }else if(id==6){
              this.tip = "批量变更角色"
            }else if(id==7){
              this.tip = "批量变更状态"
            }
            
      // console.log(data,"===============");
            this.multipleSelection = data
            this.dialogVisible = true
        },
       handleClose() {
         this.dialogVisible = false
       },
       // 选中变化时触发
       selecetedMember(e) {
         // console.log(e);
         this.changerole.admin_id = e.checkedKeys.join("");
       },
       //批量转交公海
       plzhuangong(){
        if(!this.content){
          return this.$message.warning("请填写转公原因！")
        }
        let params = {
          ids: this.multipleSelection.join(","),
          content:this.content
        }
        this.$http.plzhuangong(params).then(res=>{
          if (res.status == 200) {
                this.$message.success("转交成功！");
                this.dialogVisible = false;
                this.content = ""
                this.$emit("getDataList");
              }
        })
       },
       //批量修改角色
       pljuese(){
        if(!this.changerole.admin_id){
          return this.$message.warning("请选择成员！")
        }
        if(!this.changerole.type){
          return this.$message.warning("请选择角色！")
        }
        this.changerole.ids = this.multipleSelection.join(",")
        this.$http.plchangerole(this.changerole).then(res=>{
          if (res.status == 200) {
                this.$message.success("修改成功！");
                this.dialogVisible = false;
                this.changerole={
                  type:"",//角色id 
                  admin_id:"",//成员id
                  is_sync:0,//是否同步
                },
                this.$emit("getDataList");
              }
        })
       },
       //批量修改客户状态
       plzhuantai(){
        if(!this.tracking_id){
          return this.$message.warning("请选择变更状态！")
        }
        let params = {
          ids: this.multipleSelection.join(","),
          tracking_id:this.tracking_id
        }
        this.$http.plmodifystate(params).then(res=>{
          if (res.status == 200) {
                this.$message.success("变更成功！");
                this.dialogVisible = false;
                this.tracking_id = ""
                this.$emit("getDataList");
              }
        })
       },
       //确定批量修改等级
       sublevel(){
        //批量转公
        if(this.show_id==5){
         return this.plzhuangong()
        }
        //修改客户角色
        if(this.show_id==6){
         return this.pljuese()
        }
        //修改客户状态
        if(this.show_id==7){
         return this.plzhuantai()
        }
        // this.showProgressBar = true; // 显示进度条
        //   this.progressPercentage = 0; // 重置进度条百分比
          let params = {
            ids: this.multipleSelection.join(","),
            level_id: this.level_id,
          };

          // 设置一个最短显示时间，确保进度条至少显示一段时间
        //   const minimumDisplayTime = 1000; // 最短显示时间，单位毫秒

          // 发送请求并开始计时
        //   const startTime = Date.now();
          this.$http.plcustomerlevels(params).then((res) => {
            // const endTime = Date.now();
            // const requestTime = endTime - startTime;
            // const remainingTime = Math.max(minimumDisplayTime - requestTime, 0);

            // setTimeout(() => {
            //   this.showProgressBar = false; // 隐藏进度条
            //   this.progressPercentage = 0; // 重置进度条百分比
            
              if (res.status === 200) {
                this.$message.success("修改成功！");
                this.dialogVisible = false;
                this.level_id = ""
                this.$emit("getDataList");
              }
            // }, remainingTime);
          });

        //   // 模拟进度增加
        //   const interval = setInterval(() => {
        //     this.progressPercentage += 10; // 每次增加10%
        //     if (this.progressPercentage >= 100) {
        //       clearInterval(interval); // 达到100%时清除定时器
        //       this.progressPercentage = 100; // 确保进度条显示完整
        //     }
        //   }, 100); // 每隔100ms增加一次进度
       }, 
    }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog{
  min-width: 665px !important;
}
.levelstyle{
    width: 90%;
    min-width: 513px;
    margin: 0 auto;
    // text-align: center;
    .lookstyle {
      margin-top: 20px;
      margin-left: 10px;

      .el-checkbox {
        color: rgb(230, 162, 60);
      }
    }
}
.prompt{
    display: flex;
    margin-top: 20px;
    span{
        color:red;
    }
}
</style>