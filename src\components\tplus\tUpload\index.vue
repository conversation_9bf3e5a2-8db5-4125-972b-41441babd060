<template>
	<el-upload v-loading="loading" class="pic-uploader" :disabled="hasFile" action :auto-upload="false" :show-file-list="false" :limit="1" :on-remove="clearUploadFile" :on-change="onUploadChange" ref="upload" v-bind="$attrs">
		<template v-if="hasFile">
			<img v-if="isImage" :src="fileUrl"/>
			<div v-else class="filename">
				<span class="title">{{originFileName}}</span>
			</div>
		</template>
		<i v-else class="el-icon-plus pic-uploader-icon"></i>
		<span class="uploader-actions" v-if="hasFile" >
			<span class="uploader-actions-item" @click.stop="handleRemove()"><i class="el-icon-delete"></i></span>
		</span>
	</el-upload>
</template>

<script>
	export default {
		props: { value: { type: String, default: '' } },
		data() {
			return {
				loading: false,
				fileUrl: '',
				originFileName: '',
			}
		},
		computed: {
			hasFile(){
				return !!this.fileUrl;
			},
			isImage(){
				const pos = this.fileUrl.lastIndexOf('.');
				if(pos !== -1) {
					const fix = this.fileUrl.substring(pos+1);
					console.log(fix);
					return ['png','jpg','jpeg','gif','webp','bmp'].includes(fix);
				}
				return false;
			}
		},
		watch: {
			value: {
				handler(val){
					this.fileUrl = val;
					this.originFileName = val;
				},
				immediate: true
			}
		},
		methods: {
			async onUploadChange(file){
				this.originFileName = file.name;

				const formData = new FormData();
				formData.append('file', file.raw);
				this.loading = true;
				const res = await this.$http.commonFileUpload(formData);
				if(res.status == 200){
					this.fileUrl = res.data?.url || '';
					this.clearUploadFile();
					this.$emit('input', this.fileUrl);
				}
				this.loading = false;
			},
			clearUploadFile(){
				this.$refs.upload?.clearFiles();
			},
			handleRemove(){
				this.fileUrl = '';
				this.$emit('input', this.fileUrl);
			}
		}
	}
</script>

<style lang="scss" scoped>
::v-deep .el-upload{
	width: 100%;
	height: 100%;
}
.pic-uploader{
	width: 128px;
	height:128px;
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	background-color:#fff;
	position:relative;
	z-index:1;
	overflow: hidden;
	&:hover {
		border-color: #409EFF;
	}
	.filename{
		display: flex;
		align-items: center;
		height: 100%;
		.title{
			display: inline-block;
			width: 100%;
			max-height: 100%;
			padding: 8px 5px;
			text-align: center;
			line-height: 1.5;
			font-size: 14px;
			color: #8c939d;
			overflow: hidden;
		}
	}
}
.pic-uploader img{width:100%;height:100%}
.pic-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 128px;
    text-align: center;
}
.uploader-actions{
	position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0,0,0,.5);
    transition: opacity .3s;
}
.el-upload .uploader-actions:hover{
	z-index:1;
	opacity: 1;
}
.uploader-actions:after {
    display: inline-block;
    content: "";
    height: 100%;
    vertical-align: middle;
}
.uploader-actions span{cursor:pointer}
</style>
