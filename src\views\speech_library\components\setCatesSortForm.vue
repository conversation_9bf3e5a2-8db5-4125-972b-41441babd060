<template>
<el-dialog :visible.sync="show" title="话术分类设置" width="500px">   
    <div class="tab-list" v-if="show">
        <div class="tip">拖拽分类进行排序</div>
        <draggable v-model="list" animation="300" style="width: 100%;">
            <transition-group>
                <div v-for="(row, index) in list" :key="row.id" class="tab-item">
                    <span class="hand"><i class="el-icon-rank"></i></span>
                    <template>
                        <span class="name">{{row.cate_name}}</span>
                        <span class="close" @click="remove(row, index)">
                            <i class="el-icon-close"></i>
                        </span>
                    </template>
                </div>
            </transition-group>
        </draggable>
    </div>
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" @click="confirm" :loading="submiting">保 存</el-button>
    </span>
</el-dialog>
</template>

<script>
import draggable from 'vuedraggable';
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        value: {
            type: Array,
            default: () => []
        },
        package_id: ''
    },
    components: {
        draggable
    },
    data(){
        return {
            show: false,
            submiting: false,
            list: []
        }
    },
    watch: {
        visible: {
            handler(val){
                this.show = val;
                if(val){
                    this.list = JSON.parse(JSON.stringify(this.value)).map(e => {
                        return {...e, removing: false}
                    })
                }
            },
            immediate: true
        },
        show(val){
            val != this.visible && this.$emit("update:visible", val)
        },
    },
    methods: {
        async remove(item, index){
            if(item.removing){
                return;
            }
            const confirm = await this.$confirm("确认要删除吗", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).catch(() => {});
            if(!confirm) return;

            item.removing = true;
            const res = await this.$http.delscriptlibrar(item.id).catch(() => {});
            item.removing = false;
            if (res.status === 200) {
                this.list.splice(index, 1)
                this.$message.success("删除成功");
                this.$emit('itemRemove', item);
            }
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            this.submiting = true;
            try{
                const params = {
                    sort_data: JSON.stringify(this.list.map(e => e.id)),
                    package_id: this.package_id
                };
                const res = await this.$http.setSpeechLibraryCateSort(params);    
                if(res.status == 200){
                    this.$message.success(res?.data?.msg || '设置成功');
                    this.show = false;
                    this.$emit('input', this.list);
                }
            }catch(e){
                console.error(e);
            }
            this.submiting = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.tab-list{
    display: flex;
    flex-wrap: wrap;
    .tip{
        height: 26px;
    }
    .tab-item{
        display: flex;
        align-items: center;
        height: 42px;
        margin: 10px 0 12px;
        border: 1px solid #e8e8e8;
        border-radius: 2px;
        font-size: 15px;
        color: #606266;
        cursor: move;
        padding-right: 12px;
        user-select: none;
        .hand{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 40px;
        }
        .name{
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .close{
            cursor: pointer;
            color: #999;
            margin-left: 6px;
            &:hover{
                color: #3c3c3c;
            }
        }
    }
}
</style>