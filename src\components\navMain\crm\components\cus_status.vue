<template>
  <div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      highlight-current-row
    >
      <!-- <el-table-column prop="tracking_id" label="来源ID"></el-table-column> -->
      <el-table-column prop="title" label="名称"></el-table-column>
      <el-table-column prop="desc" label="描述"></el-table-column>
      <!-- <el-table-column label="开启状态">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status == 1">开启</el-tag>
          <el-tag type="danger" v-if="scope.row.status == 2">关闭</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="order" label="排序"></el-table-column>
      <el-table-column label="操作" fixed="right" v-slot="{ row }">
        <el-link type="primary" @click="onChangeEdit(row)">编辑</el-link>
      </el-table-column>
      <!-- <el-table-column prop="created_at" label="添加时间"></el-table-column> -->
    </el-table>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog
      width="500px"
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
    >
      <el-form :model="form_data" label-position="right" label-width="100px">
        <el-form-item label="名称：">
          <el-input
            disabled
            style="width:300px"
            v-model="form_data.title"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序：">
          <el-input
            style="width:300px"
            v-model="form_data.order"
            placeholder="请输入"
            type="number"
            min="0"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="启用状态：">
          <el-radio v-model="form_data.status" :label="1">启用</el-radio>
          <el-radio v-model="form_data.status" :label="0">禁用</el-radio>
        </el-form-item> -->
        <el-form-item label="描述：">
          <el-input
            style="width:300px"
            v-model="form_data.desc"
            placeholder="请输入"
            type="textarea"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCreate = false">取 消</el-button>
        <el-button
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          type="primary"
          @click="submitData"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_data: {},
      is_button_loading: false,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmCustomerStatus({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onChangeEdit(row) {
      this.form_data = {
        id: row.tracking_id,
        title: row.title,
        desc: row.desc,
        // status: row.status,
        order: row.order,
      };
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    submitData() {
      this.is_button_loading = true;
      if (this.dialogTitle === "updateData") {
        this.$http.updateCrmCustomerStatus(this.form_data).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
  },
};
</script>

<style></style>
