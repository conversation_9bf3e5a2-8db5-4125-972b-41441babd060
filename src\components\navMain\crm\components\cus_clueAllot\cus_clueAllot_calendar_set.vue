<template>
    <el-dialog :title="title" :visible.sync="show" width="800px" append-to-body @closed="handclaosed">
        <el-form label-width="120px">
            <el-form-item label="所属日:">
                {{params.day}} 日
            </el-form-item>
        </el-form>
        <el-form label-width="120px">
            <el-form-item label="关联类型:">
                <el-radio-group v-model="params.type">
                    <el-radio :label="1">成员</el-radio>
                    <el-radio :label="2">分组</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <el-form label-width="120px" v-show="params.type===1">
            <el-form-item label="选择成员:">
                <tMemberSelect v-model="params.admin_id" clearable :datas="memberList" ref="tMemberSelect"/>
            </el-form-item>
        </el-form>
        <el-form label-width="120px" v-show="params.type===2">
            <el-form-item label="选择分组:">
                <admSelect v-model="params.group_id" filterable clearable :remote-filter="false"  ref="tGroupSelect"
                    :data="groupList" api="getcluegrouping" props="{label:'title',value:'id',keyword:'title'}" placeholder="请选择分组"/>
                <p class="form-item-tip">分组内的成员，将按顺序轮流分配</p>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="submit">确认</el-button>
        </span>
    </el-dialog>
    </template>
    
<script>
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect.vue';
export default {
    components: {
        tMemberSelect
    },
    data(){
        return {
            show: false,        //dialog是否显示
            successFn: null, 
            cancleFn: null, 
            params: {},
            memberList: [],
            groupList: []
        }
    },
    computed: {
        title(){
            return '设置排期';
        }
    },
    methods: {
        open(params){
            this.params = {
                day: params.day,
                type: params.type || 1,  //1 会员，2分组
                admin_id: params.admin_id,
                group_id: params.group_id,
                admin_name: params.admin_name,
                group_name: params.group_name,
            };
            if(params.type === 1){
                if(params.admin_name){
                    this.memberList = [{values:params.admin_id,name:params.admin_name}];
                }
            }else if(params.type === 2){
                if(params.group_name){
                    this.groupList = [{id:params.group_id,title:params.group_name}];
                }
            }

            this.show = true;
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        onCancel(fn){
            this.cancelFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        handclaosed(){
            this.cancelFn && this.cancelFn()
        },
        async submit(){
            const params = {...this.params};
            if(params.type == 1){
                params.group_id = '';
                params.group_name = '';
                params.admin_name = params.admin_id ? this.$refs.tMemberSelect.getOptionLabel(params.admin_id) : '';
                if(!params.admin_id){
                    params.type = 0;
                }
            }else{
                params.admin_id = '';
                params.admin_name = '';
                params.group_name = params.group_id? this.$refs.tGroupSelect.getOptionLabel(params.group_id) : '';
                if(!params.group_id){
                    params.type = 0;
                }
            }
            this.show = false;
            this.successFn && this.successFn(params)
        }
    }  
}
</script>
<style lang="scss" scoped>
.form-item-tip{
    color: #f40;
}
</style>