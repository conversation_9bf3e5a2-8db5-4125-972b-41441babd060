<template>
  <div class="pages">
    <div class="open-top row div">
      <img class="jg" src="https://img.tfcs.cn/backup/static/admin/crm/static/kt-ts.png" alt="" />
      <span class="miaosu">您还未接入企业微信，按照提示完成接入流程，即可使用SCRM完整功能。</span>
      <el-button type="primary" size="mini" @click="openCrm">立即接入</el-button>
    </div>
    <div class="content-open row div" v-if="false">
      <div class="open-box">
        <div class="top div row">
          <div class="l-top div row">
            <span class="span">欢迎试用</span>
            <span class="blue">SCRM</span>
          </div>
          <div class="label">未授权</div>
        </div>
        <div class="title">使用之前请先授权接入企业微信</div>
        <img src="https://img.tfcs.cn/backup/static/admin/crm/static/kt-1.png" class="kt" alt="" />
        <div class="tips">
          接入腾房云不影响企业自己通过官方API接口开发功能
        </div>
        <div class="open-btn">已有企业微信，开始接入</div>
        <div class="no-tips">还没企业微信，立即注册</div>
      </div>
      <div class="open-box">
        <div class="top div row">
          <div class="l-top div row">
            <span class="span">请完善</span>
            <span class="blue">系统规则</span>
          </div>
          <div class="label1">已授权</div>
        </div>
        <div class="title">系统基础配置</div>
        <img src="https://img.tfcs.cn/backup/static/admin/crm/static/kt-2.png" class="kt" alt="" />
        <div class="tips">
          新一代房产私域客户增长系统
        </div>
        <div class="open-btn">正常使用</div>
      </div>
    </div>
  </div>
</template>

<script>
// import getWxWorkjssdk from "@/utils/getWxWorkjssdk.js";
export default {
  name: "crm_customer_open",
  data() {
    return {};
  },
  // mixins: [getWxWorkjssdk],
  created() {
    // this.getWxQyWxConfig();
    // this.openCrm();
  },
  methods: {
    openCrm() {
      this.$confirm("是否开通系统CRM", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.openCrmData().then((res) => {
            if (res.status === 200) {
              this.$message.success("开通成功");
              localStorage.setItem("website_crm", 1);
              this.$goPath("/crm_index");
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;

  .open-top {
    align-items: center;
    font-size: 14px;
    padding: 17px 40px;
    color: #434343;
    background: #ffdfdf;

    .miaosu {
      margin: 0 24px 0 8px;
    }
  }

  .content-open {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .open-box {
      padding: 30px;
      border-radius: 4px;
      background: #ffffff;
      width: 450px;
      box-shadow: 0px 0px 20px 0px #00000026;

      .top {
        justify-content: space-between;
        align-items: center;

        .l-top {
          font-size: 26px;

          .blue {
            color: #2d84fb;
          }
        }

        .label {
          padding: 2px 6px;
          font-size: 14px;
          color: #fd3636;
          background: #ffdfdf;
        }

        .label1 {
          padding: 2px 6px;
          font-size: 14px;
          color: #019f11;
          background: #cdffc0;
        }
      }

      .title {
        font-size: 26px;
        margin: 14px 0 24px;
      }

      .kt {
        display: block;
        height: 206px;
        width: 100%;
      }

      .tips {
        font-size: 14px;
        color: #8b8b8b;
        text-align: center;
        margin: 10px 0 20px;
      }

      .open-btn {
        color: #fff;
        text-align: center;
        line-height: 58px;
        background: #2d84fb;
        cursor: pointer;
      }

      .no-tips {
        color: #2d84fb;
        text-align: center;
        margin-top: 20px;
      }
    }
  }
}
</style>
