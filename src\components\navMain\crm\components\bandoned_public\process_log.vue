<template>
    <div class="page">
        <el-form :inline="true" :model="params" class="demo-form-inline">
            <el-form-item label="客户编号">
                <el-input size="small" v-model="params.client_id"></el-input>
            </el-form-item>
            <el-form-item label="姓名">
                <el-input size="small" v-model="params.cname"></el-input>
            </el-form-item>
            <el-form-item label="手机号">
                <el-input size="small" v-model="params.mobile"></el-input>
            </el-form-item>
            <el-form-item label="所属成员">
                <el-select size="small" v-model="params.last_dg_uid"
                  filterable 
                  clearable placeholder="请选择成员"> 
                    <el-option v-for="item in member_listNEW" :key="item.id" :label="item.user_name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="query">查询</el-button>
            </el-form-item>
        </el-form>
        <div>
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#EBF0F7' }"
              v-loading="is_table_loading"
              border
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>
              <el-table-column
                fixed
                prop="client_id"
                label="客户编号">
              </el-table-column>
              <el-table-column
                fixed
                prop="cname"
                label="客户名称">
              </el-table-column>
              <el-table-column
                prop="mobile"
                label="手机号">
              </el-table-column>
              <el-table-column
                prop="dg_user_name"
                label="所属成员"
                v-slot="{row}">
                {{row.dg_user_name?row.dg_user_name:"--"}}
              </el-table-column>
              <el-table-column
                prop="types"
                label="执行类型"
                v-slot="{row}">
                {{row.types==1?"自动掉公":row.types==2?"自动分配":row.types==3?"自动转交":"--"}}
              </el-table-column>
              <el-table-column
                prop="created_at"
                label="执行时间">
              </el-table-column>
              <el-table-column
                prop="time_type"
                label="条件判断"
                v-slot="{row}"
                min-width="160">
                <div>{{ getTimeType(row.time_type) }}{{row.time_status==1?"大于":"小于"}}{{  row.diy_time / 24 +"天"}},
                  {{ getTimeaction(row.action) }}大于{{  row.diy_time / 24 +"天"}}</div>
              </el-table-column>
              <el-table-column
                label="操作"
                v-slot="{row}">
                <el-link type="primary" @click="opendetails(row)">详情</el-link>
              </el-table-column>
            </el-table>
            <div class="page_footer ">
              <div class="tab-content-footer">
                <div class="page_footer_l flex-row flex-1 items-center">
                  <div class="head-list">
                    <el-button type="primary" size="small" @click="empty">清空</el-button>
                  </div>
                  <div class="head-list">
                    <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                  </div>
                </div>
                <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
                  :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                  @current-change="onPageChange" @size-change="handleSizeChange">
                </el-pagination>
              </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props:{
        processlogdata: {
            type: Object,
            required: true,
            default: () => {}  // 确保有一个默认值
        },
        member_listNEW:{
          type: Array,
          default: () => []
        },
        conditiondata: {
            type: Object,
            required: true,
            default: () => ({ time_type: {} })  // 确保有一个默认值
      },
    },
    data() {
        return {
            params:{
                page:1,
                per_page:10,
                recovery_id:"",//流程id
                // mobile:"",//手机号
                // cname:"",//姓名
            },
            tableData:[],//流程信息
            is_table_loading:false,
        }
    },
    watch:{
        processlogdata:{
        handler(newVal){
            this.getprocesslog()
        }
      }
    },
    computed: {
      //时间类型
      timeTypeData() {
            return this.conditiondata?.time_type || {}; // 防止未定义错误
      },
      //行为
      timeactionData(){
        return this.conditiondata?.action || {}; // 防止未定义错误
      },
    },
    mounted(){
        this.getprocesslog()
    },
    methods:{
        //获取流程日志
        getprocesslog(){
            let form_data = JSON.parse(JSON.stringify(this.params)) // 深拷贝客户来源列表
            form_data.recovery_id = this.processlogdata.id
            this.is_table_loading = true
            this.$http.getprocesslogpublic(form_data).then(res=>{
                if(res.status==200){
                    this.is_table_loading = false
                    this.tableData = res.data.data
                    this.params.total = res.data.total
                }
            })
        },
        //多选
        handleSelectionChange(){

        },
        //分页
        onPageChange(current_page) {
            this.params.page = current_page;
            this.getprocesslog();
        },
        handleSizeChange(e){
            this.params.per_page = e
            this.getprocesslog(); 
        },
        //查询
        query(){
            this.getprocesslog(); 
        },
        //返回时间类型
        getTimeType(timeType) {
            return this.timeTypeData[timeType] || '--'; // 返回默认值
        },
        //返回行为
        getTimeaction(action){
          return this.timeactionData[action] || '--'; // 返回默认值
        },
        //跳转到详情
        opendetails(row){
          let url = `/crm_customer_detail?id=${row.client_id}&type=seas`;
          this.$goPath(url);
        },
        //刷新
        Refresh(){
            this.getprocesslog()
        },
        //清空
        empty(){
          this.params = {
            page:1,
            per_page:10,
            client_id:"",//时间类型
            cname:"",//行为
            mobile:"",
            last_dg_uid:"",
          }
          this.getprocesslog()
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
    margin-top: 20px;
    margin-bottom: 60px;
}
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  // padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
</style>