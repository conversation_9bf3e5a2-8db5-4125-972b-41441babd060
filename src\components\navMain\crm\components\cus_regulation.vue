<template>
    <div>
        <el-form
            :model="form_info"
            label-width="240px"
        >
            <el-form-item label="是否开启掉公策略：">
            <el-radio v-model="form_info.is_auto_recycle" :label="1">开启</el-radio>
            <el-radio v-model="form_info.is_auto_recycle" :label="0">关闭</el-radio>
            <el-radio v-model="form_info.is_auto_recycle" :label="2" v-if="website_id==176||website_id==109">新掉公规则</el-radio>
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="margin-left: 145px"
                >
                <div slot="content" style="max-width: 300px">
                    开启后系统将根据设定规则将线索掉入公海 
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="无跟进逾期天数：" v-if="form_info.is_auto_recycle==1">
            <template>
                <el-radio v-model="Maximumclaim" label="0">关闭</el-radio>
                <el-radio v-model="Maximumclaim" label="1">开启</el-radio>
                <el-input
                v-if="Maximumclaim>0"
                style="width: 150px;margin-left:-24px"
                placeholder="请输入天数"
                class="overdue"
                v-model="form_info.max_overdue_day"  
                min="0"
                step="1"
                type="number"
                >
                <template slot="append">
                    <el-select v-model="form_info.max_overdue_unit">
                      <el-option label="天" value="1"></el-option>
                      <el-option label="小时" value="2"></el-option>
                      <el-option label="分钟" value="3"></el-option>
                    </el-select>
                </template>
            </el-input>
            </template>
            <!-- <template v-else>
                <el-input
                placeholder="请输入天数"
                class="overdue"
                v-model="form_info.max_overdue_day"
                min="0"
                step="1"
                type="number"
                >
                <template slot="append">
                    <el-select v-model="form_info.max_overdue_unit">
                      <el-option label="天" value="1"></el-option>
                      <el-option label="小时" value="2"></el-option>
                      <el-option label="分钟" value="3"></el-option>
                    </el-select>
                </template>
            </el-input>
            </template> -->
           
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="margin-left: 10px"
                >
                <div slot="content" style="max-width: 300px">
                   规则一： 超出逾期天数将自动掉公,设置为0则不触发
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="未拨打电话掉公时长：" v-if="form_info.is_auto_recycle==1">
            <template>
                <el-radio v-model="duration" label="0">关闭</el-radio>
                <el-radio v-model="duration" label="1">开启</el-radio>
                <el-input
                v-if="duration>0"
                style="width: 150px;margin-left:-24px"
                placeholder="请输入天数"
                class="overdue"
                v-model="form_info.max_no_tel_time"
                min="0"
                step="1"
                type="number"
                >
                <template slot="append">
                    <el-select v-model="form_info.max_no_tel_unit">
                      <el-option label="天" value="1"></el-option>
                      <el-option label="小时" value="2"></el-option>
                      <el-option label="分钟" value="3"></el-option>
                    </el-select>
                </template>
            </el-input>
            </template>
            <!-- <template v-else>
                <el-input
                placeholder="请输入时间"
                class="overdue"
                v-model="form_info.max_no_tel_time"
                min="0"
                step="1"
                type="number"
            >
            <template slot="append">
                    <el-select v-model="form_info.max_no_tel_unit">
                      <el-option label="天" value="1"></el-option>
                      <el-option label="小时" value="2"></el-option>
                      <el-option label="分钟" value="3"></el-option>
                    </el-select>
                </template>
            </el-input>
            </template> -->
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="display: inline-block; margin-left: 10px"
            >
                <div slot="content" style="max-width: 300px">
                    规则二：超出未拨打电话时长的客户，执行自动掉公策略,设置为0则不触发。
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="掉公规则：" v-if="form_info.is_auto_recycle==1">
            <el-radio v-model="form_info.discard_rule" :label="1">同时满足</el-radio>
            <el-radio v-model="form_info.discard_rule" :label="2">单一满足</el-radio>
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="margin-left: 89px"
                >
                <div slot="content" style="max-width: 300px">
                   1.同时满足: 指上边两条规则同时满足才会触发掉公。<br/>
                   2.单一满足: 指满足其中一个条件就会触发掉公规则。
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="掉公执行范围：" v-if="form_info.is_auto_recycle==1">
            <el-select v-model="form_info.overdue_tracking_id" multiple placeholder="请选择" style="width: 300px;">
                <el-option
                    v-for="item in overdue_tracking_name"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                </el-option>
            </el-select>
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="margin-left: 10px"
            >
                <div slot="content" style="max-width: 300px">
                    可多选，默认“有效客户”，不在此状态范围内的，不再执行自动掉公策略。
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="掉公执行成员：" v-if="form_info.is_auto_recycle==1">
        <el-select ref="overdue_uid" style="width: 300px" v-model="form_info.overdue_uid" multiple placeholder="请选择"
        @focus="showPersonnelAuthority('overdue_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            默认为空【全部成员将执行掉公规则】
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="掉公提醒周期：" v-if="form_info.is_auto_recycle==1" >
        <div>
            <span style="color: #606266;">
              一次提醒  
            </span>
             <el-input
                placeholder="请输入天数"
                class="overdue"
                v-model="form_info.one_reminder_cycle"
                min="0"
                step="1"
                type="number"
                disabled
                style="width: 226px;"
                >
                <template slot="append">
                    天前
                </template>
            </el-input>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">一次提醒为系统默认，不可更改！
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
        </div>
       <div style="margin-top: 10px;">
            <span style="color: #606266;">
             二次提醒 
            </span>
          <el-input
                placeholder="请输入天数"
                class="overdue"
                v-model="form_info.two_reminder_cycle"
                min="0"
                step="1"
                type="number"
                @change="remind"
                style="width: 226px;"
                >
                <template slot="append">
                    天前
                </template>
            </el-input>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">设置为0则不触发,不得大于无跟进逾期天数！
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
       </div>
      </el-form-item>
        <el-form-item v-if="form_info.is_auto_recycle==1||form_info.is_auto_recycle==0" >
            <el-button type="primary" @click="onClickForm">确认</el-button>
        </el-form-item>
        </el-form>
        <!-- <newdrop_rule v-if="opennewdrop"></newdrop_rule> -->
        <el-dialog :visible.sync="show_add_member" width="400px" title="执行掉公成员" append-to-body>
      <div class="member" ref="memberList">
        <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
          :defaultExpandAll="false">
        </multipleTree>
        <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false">取消</el-button>
          <el-button type="primary" @click="selectMemberOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
    </div>
</template>
<script>
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import newdrop_rule from "@/components/navMain/crm/components/bandoned_public/newdrop_the_public_rule.vue"
export default {
    components: {
    multipleTree,
    newdrop_rule
  },
    data() {
        return {
            hours:"",
            remaining_minutes:"",
            user_list: [], // 管理员列表
            params: {
                page: 1,
                per_page: 100,
                type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
            },
            form_info: {
                is_auto_recycle: 0, //是否逾期自动回收(1:开启,0:关闭)
                max_overdue_day: 10, //最大逾期天数
                max_overdue_unit:"",//最大逾期时间单位(1:天,2:小时,3:分钟)
                max_no_tel_unit:"",//最大未拨打电话时间单位(1:天,2:小时,3:分钟)
                overdue_tracking_id: "", //支持掉公的客户状态
                overdue_uid:"",//执行掉公的成员
                max_get_num: 2, //成员每天最大认领条数(0:不限制)
                max_look_num: 10, //成员每天最大查看电话数量(0:不限制)
                deal_approver_uid: "", //成交审批用户范围
                admin_list: "", // 客户管理员
                deal_commission_uid: "", //成交分佣用户范围(默认管理员)
                mark_inherit_approver_uid: "", //标记无效审批用户id
                batch_import_uid: "", //批量导入客户权限范围(为空全员可导入操作)
                max_get_total: 30, //成员最大认领条数(0不限制)
                get_black_list: "", //领取客户黑名单
                add_notice_uid: "", //新增客户通知范围
                only_auth: 0, // 客户手机号双向核验
                is_get_show: 0, // 公海已领取客户是否可见(1:可见,0不可见)
                is_del: 0, // 是否可以删除(0:关闭,1:开启)
                all_follow_status: 0, // 客户管理员
                del_client_list: "", // 删除客户无需审核权限范围
                up_client_state_list: [35, 36], // 更改客户状态无需审核范围
                max_no_tel_time: "", // 未拨打电话掉公时长
                discard_rule:"",
            },
            overdue_tracking_name: [], // 掉公范围状态
            datalist: [], // 全部部门成员列表
            serverData: [], // 部门成员数据
            show_add_member:false,//执行掉公成员
            selectedIds: [], // 默认勾选和展开的节点的 key 的数组
            Maximumclaim:"0",//无跟进逾期天数
            duration:"0",//未拨打电话掉公时长
            // opennewdrop:false,//控制新掉公规则的显示
        }
    },
    watch:{
        "form_info.is_auto_recycle":{
            handler(newVal) {
                console.log(newVal,"========");
                if(newVal==2){
                    // this.opennewdrop = true
                    // this.onClickForm()
                    return  this.$router.push("newregulation");
                }
                // else{
                //     this.opennewdrop = false
                // }
                
            }
        },
    },
    created() {
        this.website_id = this.$route.query.website_id
        this.getSiteCrmSetting();
        this.getCustomerStatus();
        this.getMemberList();
    },
    methods: {
        // 获取客户状态
        getCustomerStatus() {
            this.$http.getCrmCustomerFollowInfo({params: { type: 4 }}).then((res) => {
                if(res.status == 200) {
                this.overdue_tracking_name = res.data
                }
            })
        },
        // 获取批量导入的crm站点设置
        getSiteCrmSetting() {
            this.getManagerAuthList();
        },
        // 获取管理员列表
        getManagerAuthList() {
            this.$http.getManagerAuthList({ params: this.params }).then((res) => {
                if (res.status === 200) {
                this.user_list = res.data.data;
                }
            });
        },
        // 获取部门成员列表
        async getMemberList() {
            await this.$http.getDepartmentMemberList().then((res) => {
                if(res.status == 200) {
                    this.serverData = JSON.parse(JSON.stringify(res.data))
                    this.serverData.push({
                        id: 999,
                        name: "未分配部门成员",
                        order: 100000000,
                        pid: 0,
                        subs: this.serverData[0].user
                    })
                    this.recursionData(this.serverData); 
                    // 当键值key重复就更新key
                    for (let i = 0; i < this.datalist.length; i++) {
                        for (let j = i + 1; j < this.datalist.length; j++) {
                        if (this.datalist[i].id == this.datalist[j].id) {
                            this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
                        }
                        }
                    }
                    // console.log(this.datalist);
                }
            })
            this.getSetting();
        },
        // 递归数据处理
        recursionData(data) {
        // console.log(data,"内容");
        // console.log(this.datalist,"全部人员");
            for (let key in data) {
                if (typeof data[key].subs == "object") {
                    data[key].subs.map((item) => {
                        if(item.user) {
                            item.subs = item.user;
                            item.subs.map((list) => {
                                list.Parent = item.id;
                            })
                        }
                        if(item.user_name) {
                            item.name = item.user_name;
                            this.datalist.push(item)
                        }
                    })
                    this.recursionData(data[key].subs);
                }
            }
        },
        //二次提醒的区间判断
        remind(e){
            if(e){
               if(e==1){
                this.form_info.two_reminder_cycle = 2
                    return this.$message.warning("1 为系统默认提醒")
                }
                if(e>this.form_info.max_overdue_day){
                    this.form_info.two_reminder_cycle = 2
                    return this.$message.warning("不得大于无跟进逾期天数")
                } 
            } 
        },
        // 确认提交参数
        onClickForm() {
            // if(this.form_info.is_auto_recycl==1||this.form_info.max_no_tel_time==0){
            //     this.$message.warning("未拨打电话掉公时长必须大于0")
            //      delete this.form_info.max_no_tel_time
            //      return
            // }
            let result = [];
            if(this.form_info.deal_approver_uid != [] && this.form_info.deal_approver_uid != '') {
                this.form_info.deal_approver_uid.map((item) => {
                if(item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                    result.push(item);
                }
                })
            }
            this.form_info.deal_approver_uid = Array.from(new Set(result));
            this.form_info.deal_approver_uid = this.form_info.deal_approver_uid
                ? this.form_info.deal_approver_uid.join(",")
                : "";
            result = [];
            if(this.form_info.admin_list != [] && this.form_info.admin_list != '') {
                this.form_info.admin_list.map((item) => {
                if(item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                    result.push(item);
                }
                })
            }
            this.form_info.admin_list = Array.from(new Set(result));
            this.form_info.admin_list = this.form_info.admin_list
                ? this.form_info.admin_list.join(",")
                : "";
            this.form_info.overdue_tracking_id = this.form_info.overdue_tracking_id
                ? this.form_info.overdue_tracking_id.join(",")
                : "";
            this.form_info.overdue_uid = this.form_info.overdue_uid
                ? this.form_info.overdue_uid.join(",")
                : "";
            this.form_info.up_client_state_list = this.form_info.up_client_state_list
                ? this.form_info.up_client_state_list.join(",")
                : "";
            this.form_info.deal_commission_uid = this.form_info.deal_commission_uid
                ? this.form_info.deal_commission_uid.join(",")
                : "";
            this.form_info.mark_inherit_approver_uid = this.form_info
                .mark_inherit_approver_uid
                ? this.form_info.mark_inherit_approver_uid.join(",")
                : "";
            result = [];
            if(this.form_info.batch_import_uid != [] && this.form_info.batch_import_uid != '') {
                this.form_info.batch_import_uid.map((item) => {
                if(item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                    result.push(item);
                }
                })
            }
            this.form_info.batch_import_uid = Array.from(new Set(result));
            this.form_info.batch_import_uid = this.form_info.batch_import_uid
                ? this.form_info.batch_import_uid.join(",")
                : "";
            // this.form_info.config_approver_uid = this.form_info.config_approver_uid
            //   ? this.form_info.config_approver_uid.join(",")
            //   : "";
            result = [];
            if(this.form_info.get_black_list != [] && this.form_info.get_black_list != '') {
                this.form_info.get_black_list.map((item) => {
                if(item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                    result.push(item);
                }
                })
            }
            this.form_info.get_black_list = Array.from(new Set(result));
            this.form_info.get_black_list = this.form_info.get_black_list
                ? this.form_info.get_black_list.join(",")
                : "";
            result = [];
            if(this.form_info.add_notice_uid != [] && this.form_info.add_notice_uid != '') {
                this.form_info.add_notice_uid.map((item) => {
                if(item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                    result.push(item);
                }
                })
            }
            this.form_info.add_notice_uid = Array.from(new Set(result));
            this.form_info.add_notice_uid = this.form_info.add_notice_uid
                ? this.form_info.add_notice_uid.join(",")
                : "";
            result = [];
            if(this.form_info.del_client_list != [] && this.form_info.del_client_list != '') {
                this.form_info.del_client_list.map((item) => {
                if(item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                } else {
                    result.push(item);
                }
                })
            }
            this.form_info.del_client_list = Array.from(new Set(result));
            this.form_info.del_client_list = this.form_info.del_client_list
                ? this.form_info.del_client_list.join(",")
                : "";
            if(this.form_info && Object.keys(this.form_info).indexOf('look_tel_list')) {
                delete this.form_info.look_tel_list;
            }
            // this.form_info.max_no_tel_time=""
            // if(this.remaining_minutes>= 60){
            //     let remainingMinutes = this.remaining_minutes;
            //     if (remainingMinutes >= 60) {
            //        const additionalHours = Math.floor(remainingMinutes / 60);
            //        this.hours=  Number(this.hours)+ Number(additionalHours);
            //        remainingMinutes %= 60;
            //        this.form_info.max_no_tel_time= this.hours * 60 + remainingMinutes
            //      }
            // }else{
            //          this.form_info.max_no_tel_time= Number(this.hours * 60) +  Number(this.remaining_minutes)
            //      }
            if(this.Maximumclaim == "0"){
                this.form_info.max_overdue_day = 0
            }
            if(this.duration == "0"){
                this.form_info.max_no_tel_time = 0
            }
                 console.log(this.form_info);
            this.$http.setSiteCrmSetting(this.form_info).then((res) => {
                if (res.status === 200) {
                   // if(this.form_info.is_auto_recycle==2){
                    //   return  this.$router.push("newregulation");
                    // }
                this.form_info.deal_approver_uid = this.form_info.deal_approver_uid
                    ? this.setArr(this.form_info.deal_approver_uid)
                    : "";
                this.form_info.admin_list = this.form_info.admin_list
                    ? this.setArr(this.form_info.admin_list)
                    : "";
                this.form_info.deal_commission_uid = this.form_info
                    .deal_commission_uid
                    ? this.setArr(this.form_info.deal_commission_uid)
                    : "";
                this.form_info.mark_inherit_approver_uid = this.form_info
                    .mark_inherit_approver_uid
                    ? this.setArr(this.form_info.mark_inherit_approver_uid)
                    : "";
                this.form_info.batch_import_uid = this.form_info.batch_import_uid
                    ? this.setArr(this.form_info.batch_import_uid)
                    : "";
                this.form_info.get_black_list = this.form_info.get_black_list
                    ? this.setArr(this.form_info.get_black_list)
                    : "";

                this.form_info.add_notice_uid = this.form_info.add_notice_uid
                    ? this.setArr(this.form_info.add_notice_uid)
                    : "";
                this.form_info.del_client_list = this.form_info.del_client_list
                    ? this.setArr(this.form_info.del_client_list)
                    : "";
                this.$message.success("操作成功");
                this.getSetting();
                } else {
                this.form_info.deal_approver_uid = this.form_info.deal_approver_uid
                    ? this.setArr(this.form_info.deal_approver_uid)
                    : "";
                this.form_info.admin_list = this.form_info.admin_list
                    ? this.setArr(this.form_info.admin_list)
                    : "";
                this.form_info.deal_commission_uid = this.form_info
                    .deal_commission_uid
                    ? this.setArr(this.form_info.deal_commission_uid)
                    : "";
                this.form_info.mark_inherit_approver_uid = this.form_info
                    .mark_inherit_approver_uid
                    ? this.setArr(this.form_info.mark_inherit_approver_uid)
                    : "";
                this.form_info.batch_import_uid = this.form_info.batch_import_uid
                    ? this.setArr(this.form_info.batch_import_uid)
                    : "";
                this.form_info.get_black_list = this.form_info.get_black_list
                    ? this.setArr(this.form_info.get_black_list)
                    : "";
                this.form_info.add_notice_uid = this.form_info.add_notice_uid
                    ? this.setArr(this.form_info.add_notice_uid)
                    : "";
                this.form_info.del_client_list = this.form_info.del_client_list
                    ? this.setArr(this.form_info.del_client_list)
                    : "";
                }
            });
        },
        // 选中变化时触发
        selecetedMember(e) {
          this.selectedIds = e.checkedKeys;
        },
        selectMemberOk(){
            this.show_add_member = false;
            // if (this.identification == 'admin_list') {
               this.form_info.overdue_uid = this.selectedIds;
            // } 
        },
        showPersonnelAuthority(val) {
          this.identification = val
          if (this.identification == 'overdue_uid' && this.form_info.overdue_uid != '') {
            this.selectedIds = this.form_info.overdue_uid;
            // this.department_title = '手动认领客户成员范围';
          } else {
            this.selectedIds = [];
          }
            this.$nextTick(() => {
              if (this.$refs.overdue_uid) {
                this.$refs.overdue_uid.blur();
              }
            });
          this.show_add_member = true;
        },
        PersonnelChange(val) {
          this.selectedIds = val;
        },
        // 处理部门成员数据
        setArr(arr) {
            let n_arr = arr.split(",");
            let n_arr_2 = n_arr.map((item) => {
                return parseInt(item);
            });
            let i = 0;
            if(n_arr_2 != [] && n_arr_2 != undefined) {
                n_arr_2.map((item) => {
                this.$nextTick(() => {
                    this.datalist.map((list) => {
                    if(item != list.id) {
                        i++;
                        if(i == this.datalist.length) {
                        n_arr_2.splice(n_arr_2.indexOf(item), 1);
                        // console.log(n_arr_2,"观察");
                        }
                    }
                    })
                    i = 0;
                })
                })
            }
            return n_arr_2;
        },
        getSetting() {
            this.$http.getSiteCrmSetting().then((res) => {
                if (res.status === 200) {
                   this.hours =  Math.floor(res.data.max_no_tel_time / 60);
                   this.remaining_minutes = res.data.max_no_tel_time % 60
                this.form_info = res.data;
                    if(this.form_info.max_overdue_day>0){
                        this.Maximumclaim = "1"
                    }else if (this.form_info.max_overdue_day==0){
                        this.Maximumclaim = "0" 
                    }
                    if(this.form_info.max_no_tel_time>0){
                        this.duration = "1"
                    }else if (this.form_info.max_no_tel_time==0){
                        this.duration = "0" 
                    }
                   if(res.data.max_overdue_unit==1){
                    this.form_info.max_overdue_unit = "1"
                   }
                   if(res.data.max_overdue_unit==2){
                    this.form_info.max_overdue_unit = "2"
                   }
                   if(res.data.max_overdue_unit==3){
                    this.form_info.max_overdue_unit = "3"
                   }
                   if(res.data.max_no_tel_unit==1){
                    this.form_info.max_no_tel_unit = "1"
                   }
                   if(res.data.max_no_tel_unit==2){
                    this.form_info.max_no_tel_unit = "2"
                   }
                   if(res.data.max_no_tel_unit==3){
                    this.form_info.max_no_tel_unit = "3"
                   }
              
                // console.log(this.form_info);
                this.form_info.deal_approver_uid = res.data.deal_approver_uid
                    ? this.setArr(res.data.deal_approver_uid)
                    : "";
                this.form_info.admin_list = res.data.admin_list
                    ? this.setArr(res.data.admin_list)
                    : "";
                this.form_info.deal_commission_uid = res.data.deal_commission_uid
                    ? this.setArr(res.data.deal_commission_uid)
                    : "";
                this.form_info.mark_inherit_approver_uid = res.data
                    .mark_inherit_approver_uid
                    ? this.setArr(res.data.mark_inherit_approver_uid)
                    : "";
                this.form_info.batch_import_uid = res.data.batch_import_uid
                    ? this.setArr(res.data.batch_import_uid)
                    : "";
                // this.form_info.config_approver_uid = res.data.config_approver_uid
                //   ? this.setArr(res.data.config_approver_uid)
                //   : "";
                this.form_info.get_black_list = res.data.get_black_list
                    ? this.setArr(res.data.get_black_list)
                    : "";
                this.form_info.add_notice_uid = res.data.add_notice_uid
                    ? this.setArr(res.data.add_notice_uid)
                    : "";
                this.form_info.del_client_list = res.data.del_client_list
                    ? this.setArr(res.data.del_client_list)
                    : "";
                this.form_info.overdue_tracking_id = res.data.overdue_tracking_id
                    ? this.setArrMini(res.data.overdue_tracking_id)
                    : "";
                    this.form_info.overdue_uid = res.data.overdue_uid
                    ? this.setArrMini(res.data.overdue_uid)
                    : "";
                this.form_info.up_client_state_list = res.data.up_client_state_list
                    ? this.setArrMini(res.data.up_client_state_list)
                    : "";
                }
            });
        },
        // 处理简单数据
        setArrMini(arr) {
            let n_arr = arr.split(",");
            let n_arr_2 = n_arr.map((item) => {
                return parseInt(item);
            });
            return n_arr_2;
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .overdue{
    width: 300px;
    .el-input-group__append,
    .el-input-group__prepend {
     width: 36px; /* 将宽度设置为您所需的值 */
    }
}
</style>