<template>
  <div>
    <!-- 查看电话模态框 -->
    <el-dialog :visible.sync="showViewPhone" width="530px" :show-close="close_button" :close-on-click-modal="false"
      @close="ShowPhoneClose" :modal="false" custom-class="vertical-center-dialog" @opened="viewPhoneOpened=true">
      <div slot="title" class="warn-title">
        <div class="flex-row">
          <div>
            <img src="https://img.tfcs.cn/backup/static/admin/default_person.png" alt="" />
          </div>
          <div style="margin-left: 15px; cursor: pointer;flex: 1;">
            <el-popover placement="bottom-start" width="280" trigger="hover" :content="
                            (this.c_detail.last_behavior_info &&
                              this.c_detail.last_behavior_info.content) ||
                            this.c_detail.remark
                          ">
              <span class="flex-1" slot="reference">{{ this.c_detail.cname }}<i class="el-icon-info"></i></span>
            </el-popover>
          </div>
          
        </div>
      </div>
      <div class="flex-box">
        <div>
          <div>
            <div class="flex-row callPhoneList items-center" v-for="(item, index) in nowDialData.tel" :key="index">
              <div @click="copyAllPhone($event, item)" class="flex-1"
                style="font-weight: 700; color: red;margin-top: 3px;">
                {{ item | cellPhoneDispose }}
              </div>
              <div class="Belonging" v-if="ponent_Tel_data.mobile_place&&item==ponent_Tel_data.mobile">
                {{ ponent_Tel_data.mobile_place }}
              </div>
              <div class="Belonging" v-if="!ponent_Tel_data.mobile_place&&showphone&&item==ponent_Tel_data.mobile">
                {{ showphone }}
              </div>
              <div class="callBtn">
                <el-button type="warning" size="mini" v-if="showCall != 0" @click="showCallPhone(item)">
                  外呼
                </el-button>

                <el-select v-model="formInline.region" placeholder="接通状态" size="mini" clearable
                  style="margin-left:  10px;">
                  <el-option label="已接通" value="1"></el-option>
                  <el-option label="未接通" value="2"></el-option>
                </el-select>
              </div>
            </div>
          </div>
          <!-- <div>
            <el-form :model="formInline">
              <el-form-item>
                <el-select
                  v-model="formInline.region"
                  placeholder="接通状态"
                  size="mini"
                  clearable
                >
                  <el-option label="已接通" value="1"></el-option>
                  <el-option label="未接通" value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div> -->
        </div>
        <!-- 不是我司成交的才显示 -->
        <div class="customer-status-box" v-if="!is_deal">
          <div style="color: #666; font-size: 16px; margin-bottom: 20px">
            客户状态
          </div>
          <el-radio-group v-model="follow_params1.tracking_id" size="mini">
            <el-radio v-for="item in status_list" :key="item.id" :label="item.id" border>
              {{ item.title }}
            </el-radio>
          </el-radio-group>
        </div>
        <div class="msg" style="margin: 20px 0">
          <el-input :rows="3" v-model="follow_params1.content" type="textarea" placeholder="请输入跟进内容（企业内公开）"></el-input>
        </div>
        <div class="footer flex-row align-center j-center">
          <el-button type="primary" @click="onChangeStatus1" style="width: 479px">确定</el-button>
        </div>
        <div class="footer-attached">
          <speechLibraryTrigger :visible="viewPhoneOpened" class="footer-attached-btn" :enable.sync="is_open_speech" :class="{enabled: is_open_speech}"/>
          <telAttachedReportCustomer class="footer-attached-btn" :mobiles="nowDialData.tel" :customer="c_detail" v-if="is_open_report" @success="showViewPhone=false"/>
        </div>
        
      </div>
    </el-dialog>
    <!-- 智能电话模态框 -->
    <el-dialog width="330px" title="智能手机" custom-class="dialog" :visible.sync="showPhone">
      <my-CallPhone v-if="showPhone" :autoPhonenNumber="autoPhonenNumber" :CallPhoneOwner="CallPhoneOwner"
        :concealPhoneNumber="concealPhoneNumber" @phoneClose="closeCallPhone" @getCallId="getCallId"
        :phonePlace="ponent_Tel_data.mobile_place" :sitelinedata="sitelinedata" ></my-CallPhone>
    </el-dialog>


    
  </div>
</template>
<script>
import myCallPhone from "@/components/navMain/crm/components/myCallPhone";
import telAttachedReportCustomer from '@/views/crm/customer/components/telAttachedReportCustomer.vue'
import speechLibraryTrigger from '@/views/speech_library/components/speechLibraryTrigger.vue'
let that;
export default {
  components: {
    myCallPhone,
    telAttachedReportCustomer,
    speechLibraryTrigger
  },
  props: {
    // 控制显示隐藏模态框
    show_look_Tel: {
      type: Boolean,
      default: false
    },
    // 客户信息
    ponent_Tel_data: {
      type: Object,
      default: (() => { })
    },
    // 获取站点报备信息
    webInfo: {
      type: Object,
      default: (() => { })
    },
    selfID: {
      type: [String, Number],
      default: ""
    },
    nowDialData: {
      type: Object,
      default: (() => { })
    }
  },
  data() {
    return {
      close_button:false,//关闭按钮是否显示
      showViewPhone: false, // 查看电话模态框显示/隐藏
      c_detail: {}, //客户详情
      showCall: 0, // 控制外呼按钮是否显示
      showPhoneName: "", // 存储客户姓名
      showPhone: false, // 控制手机拨打电话组件显示
      follow_params1: {
        type: 1,
        content: "",
        tracking_id: "", // 客户状态: 默认选中有效客户
      },
      autoPhonenNumber: "", // 查看电话-外呼自动填入的手机号
      CallPhoneOwner: "", // 客户名称
      call_id: [], // 存储外呼成功，跟进记录call_phone_id参数的值
      c_id: "", // 客户id
      // 获取客户跟进状态参数
      tracking_params: {
        type: 2,
      },
      status_list: [], // 客户状态列表 
      concealPhoneNumber: "", // 查看电话-隐号手机号
      // nowDialData: {}, // 当前查看电话的数据
      formInline: {
        region: '1',
      },//电话接通状态
      Statusdisplay: 1,//切换显示电话接通状态
      is_deal: false,
      showphone:"",
      timeoutId: null, //防抖
      website_ids:"",//站点信息
      viewPhoneOpened: false,
      is_open_speech: false,
      sitelinedata:[],//站点绑定的线路列表
    }
  },
  computed: {
    is_open_report(){
      return this.c_detail.is_open_report || this.c_detail.config && this.c_detail.config.is_open_report == 1
    }
  },
  watch: {
    showViewPhone(val){
      !val && (this.viewPhoneOpened = false)
    }
  },
  beforeCreate() {
    that = this;
  },
  created() {
    this.website_ids = this.$route.query.website_id
    if(this.ponent_Tel_data.mobile_place){
      // console.log(this.ponent_Tel_data,"哈哈哈哈哈");
    }else{
      if(this.ponent_Tel_data.information==1){
        this.$http.lookphoneLocation(this.ponent_Tel_data.id).then((res) => {
        if (res.status == 200) {
          this.showphone = res.data
        }
      });
      }else{
        this.$http.inquireHomeAddress(this.ponent_Tel_data.id).then((res) => {
          if (res.status == 200) {
            this.showphone = res.data
          }
        });
      }
    }
    // this.getJudgmentPhone();
    this.close_buttonshow()
  },
  mounted() {
    this.getStatus();
    this.c_id = this.$route.query.id; // 赋值客户id
    this.initialization();
  },
  filters: {
    cellPhoneDispose(value) {
      // 1: 全号 2: 隐号


      // let mangers = that.ponent_Tel_data.admin_list || []
      // let keyuanManger = 0, whr = 0
      // if (mangers.includes(that.selfID + "")) {
      //   keyuanManger = 1
      // }
      // console.log(keyuanManger);
      // if (that.selfID == that.ponent_Tel_data.follow_id) {
      //   whr = 1
      // }
      // if (keyuanManger == 1 || whr == 1 || (that.ponent_Tel_data.follow_id > 0 && that.ponent_Tel_data.follow_id == that.selfID)) {
      //   that.has_roles = true
      // }
      // if (that.has_roles) {
      //   value = value.substring(0, 3) + " " + value.substring(3, 7) + " " + value.substring(7);
      //   return value;
      // } else {
      //   return value.replace(/^(.{3}).*(.{3})$/, "$1*****$2");
      // }
      if (that.nowDialData.type == 1) {
        value = value.substring(0, 3) + " " + value.substring(3, 7) + " " + value.substring(7);
        return value;
      } else if (that.nowDialData.type == 2) {
        return value.replace(/^(.{3}).*(.{3})$/, "$1*****$2");
      }
    }
  },
  methods: {
    close_buttonshow() {
      this.$http.getAuthShow("is_see_tel_follow").then((res) => {
        console.log(res.data);
        if (res.status == 200) {
          if (res.data == 1) {
            this.close_button = false
          } else {
            this.close_button = true
          }
        }
      })
    },
    // 数据初始化
    initialization() {
      // console.log("客户信息",this.ponent_Tel_data);
      this.c_id = this.ponent_Tel_data.id // 赋值id
      this.c_detail = this.ponent_Tel_data
      this.showCall = this.ponent_Tel_data.call_open_crm
      this.Statusdisplay = this.ponent_Tel_data.call_open_crm
      this.showViewPhone = this.show_look_Tel; // 模态框赋值
      if (!this.ponent_Tel_data.call_status) {
        //默认接通未接通
        this.formInline.region = '1'
      } else {
        this.formInline.region = this.ponent_Tel_data.call_status
      }
    },
    // 关闭查看电话回调
    ShowPhoneClose() {
      this.call_id = [];
      this.$set(this.follow_params1, "content", "");
      this.$set(this.follow_params1, "call_phone_id", "");
      this.$set(this.follow_params1, "call_name", "");
      this.$set(this.follow_params1, "call_phone", "");
      this.$set(this.follow_params1, "call_show_phone", "");

      this.showViewPhone = false;
      setTimeout(()=>{
        this.$emit("fastCloseTel", "")
      },300)
    },
    // 点击外呼显示智能手机模态框
    showCallPhone(item) {
      //获取站点绑定的线路
      this.$http.getsiteline().then((res) => {
        if (res.status == 200) {
          this.sitelinedata = res.data.list
          console.log(res.data.list);
        } 
      });
      this.CallPhoneOwner = this.showPhoneName; // 赋值当前客户名称
      this.autoPhonenNumber = item; // 赋值全号
      // call_open_crm == 2、3 拨打号码显示为隐号
      if (this.c_detail.call_open_crm == 2 || this.c_detail.call_open_crm == 3) {
        this.concealPhoneNumber = item.replace(/^(.{3}).*(.{3})$/, "$1****$2"); // 赋值隐号
      }
      this.showPhone = true; // 显示智能手机拨号框
    },
    // 确定跟进
    onChangeStatus1() {
      clearTimeout(this.timeoutId); // 清除之前的定时器 
      this.timeoutId = setTimeout(() => {  
        this.is_loading = true;
      if (this.follow_params1.content.length < 5) {
        return this.$message.warning("最少输入不能小于五个文字");
      }
      if(this.nowDialData.tel_log_id!==0){
        this.follow_params1.tel_log_id = this.nowDialData.tel_log_id
      }
        if(this.nowDialData.follow_id){
          this.follow_params1.follow_id = this.nowDialData.follow_id
      }
      if(this.ponent_Tel_data.deal_user){
        console.log(this.ponent_Tel_data.tracking.id);
        this.follow_params1.tracking_id = this.ponent_Tel_data.tracking.id
      }else{
        if (this.follow_params1.tracking_id == '') {
          this.follow_params1.tracking_id = 0  
      }
      }
      if (this.call_id != []) {
        this.$set(this.follow_params1, "client_id", this.c_id); // 客户ID
        this.$set(this.follow_params1, "contact_id", 0); // 跟进记录id
        this.$set(this.follow_params1, "call_phone_id", this.call_id[0]); // 电话记录ID
        this.$set(this.follow_params1, "call_name", this.call_id[1]); // 外呼拨打的客户姓名
        this.$set(this.follow_params1, "call_phone", this.call_id[2]); // 外呼拨打的客户号码
        this.$set(this.follow_params1, "call_show_phone", this.call_id[3]); // 外呼拨打的外显号码
      }
      this.follow_params1.call_status = this.formInline.region
      if (this.follow_params1.call_status == '') {
        delete this.follow_params1.call_status
      }
      // console.log(this.follow_params1);
      if(this.ponent_Tel_data.information==1){
        this.$http.lookphoneFollow(this.follow_params1).then(res => {
        if (res.status == 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.is_loading = true;
          this.showViewPhone = false;
          setTimeout(()=>{
            this.$emit("fastCloseTel", "")
          },300)
          this.call_id = [];
          this.$set(this.follow_params1, "content", "");
          this.$set(this.follow_params1, "call_phone_id", "");
          this.$set(this.follow_params1, "call_name", "");
          this.$set(this.follow_params1, "call_phone", "");
          this.$set(this.follow_params1, "call_show_phone", "");
        }
      }).catch(res => {
        this.$message({
          message: res,
          type: 'warning'
        });
      })
      }else{
        this.$http.setCrmCustomerFollowData(this.follow_params1).then(res => {
        if (res.status == 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.showViewPhone = false;
          setTimeout(()=>{
            this.$emit("fastCloseTel", "")
          },300)
          this.call_id = [];
          this.$set(this.follow_params1, "content", "");
          this.$set(this.follow_params1, "call_phone_id", "");
          this.$set(this.follow_params1, "call_name", "");
          this.$set(this.follow_params1, "call_phone", "");
          this.$set(this.follow_params1, "call_show_phone", "");
        }
      }).catch(res => {
        this.$message({
          message: res,
          type: 'warning'
        });
      })
      }
    }, 500); // 延迟500毫秒执行  
    },
    // 关闭智能手机模态框
    closeCallPhone() {
      this.showPhone = false
    },
    // 拨打电话成功后返回的数据
    getCallId(e) {
      this.call_id = e;
      this.postData(this.call_id)
    },
    postData(call) {
      let params = {
        call_phone_id: call[0],
        call_name: call[1],
        call_phone: call[2],
        call_show_phone: call[3],
        type: 1,
        client_id: this.c_id
      }
      this.$http.sendPhoneTel(params).then(res => {
        console.log(res);
      })
    },
    // 获取客户状态
    getStatus() {
      if (!this.status_list.length) {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.tracking_params })
          .then((res) => {
            if (res.status === 200) {
              this.status_list = res.data;
              this.status_list.map(item => {
                if (item.title == '有效客户') {
                  item.value_name = 1;
                } else if (item.title == '无效客户') {
                  item.value_name = 2;
                } else if (item.title == '暂缓客户') {
                  item.value_name = 3;
                } else {
                  item.value_name = 1;
                }
                return item;
              })
              let trackingObj = this.status_list.find(item => item.id == this.ponent_Tel_data.tracking_id);
              let effective = this.status_list.find(item => item.title == "有效客户");
              if (trackingObj) {
                this.follow_params1.tracking_id = trackingObj.id;
              } else {
                this.follow_params1.tracking_id = this.status_list.length ? this.status_list[0].id : 0;
              }
              // 他司成交默认赋值为有效 
              if (this.ponent_Tel_data.tracking && this.ponent_Tel_data.tracking.title == "他司成交") {
                this.follow_params1.tracking_id = effective.id; // 默认赋值为有效客户状态
                this.is_deal = true;
              } else if (this.ponent_Tel_data.tracking && this.ponent_Tel_data.tracking.title == "我司成交") {
                this.is_deal = true;
                // this.follow_params1.tracking_id = effective.id;
              }

            }
          });
      }
    },
    // 获取当前客户可拨打手机号和状态
    getJudgmentPhone() {
      this.$http.getJudgmentPhone(this.ponent_Tel_data.id).then((res) => {
        if (res.status == 200) {
          this.nowDialData = res.data;
        }
      })
    },
    copyAllPhone(e, item) {
      e.stopPropagation();
      e.preventDefault();
      // 1: 全号 2: 隐号
      if (that.nowDialData.type == 2) {
        item = item.replace(/^(.{3}).*(.{3})$/, "$1*****$2");
      }
      this.$onCopyValue(item)
    },
  }
}
</script>
<style lang="scss" scoped>
 .el-dialog__wrapper{
    right: 30%;
    padding-left: 30%;
    overflow: visible;
    ::v-deep{
      .el-dialog.vertical-center-dialog{
        top: 50%;
        margin: 0 auto!important;
        transform: translateY(-50%);
      }
    }
 }
.el-icon-info {
  font-size: 1px;
  color: rgb(245, 108, 108);
  margin-left: 5px;
}

.el-dialog__body {
  .PhoneTitle {
    display: block;
    padding: 0 0 24px 0;
    font-size: 18px;
    color: #2e3c4e;
    font-weight: 500;
  }
}

.items-center {
  align-items: center;
}

.itemCenter {
  align-items: center;
  margin-top: -20px;

  .callPhoneList {
    margin-bottom: 10px;
    align-content: center;
    justify-content: space-around;
  }

  .callPhoneList:last-child {
    margin-bottom: 0;
  }
}

.callPhoneList {
  margin-bottom: 10px;

  .Belonging {
    color: #8a929f;
    font-weight: 500;
    margin-left: 10px;
    margin-top: 4px;
  }
}

.callBtn {
  .el-button {
    padding: 4px 8px;
    margin-left: 5px;
  }
}

.customer-status-box {
  margin-top: 20px;

  ::v-deep .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .el-radio {
      margin: 0 0 10px 0;
    }

    .el-radio:last-child {
      margin: 0;
    }
  }
}

.footer-attached{
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-top: 18px;
  
  .footer-attached-btn{
    line-height: 1;
  }
  .footer-attached-btn.enabled + .footer-attached-btn{
    position: relative;
    margin-left: 10px;
    padding-left: 10px;
    &::before{
      content: " ";
      position: absolute;
      left: 0;
      top: 50%;
      width: 1px;
      height: 13px;
      margin-top: -6px;
      background-color: #86c0fb;
    }
  }
}
</style>