<template>
  <div class="detail pages">
    <div class="content-box-crm flex-row align-center" style="padding: 0 12px">
      <div class="bottom-border div row" style="padding: 24px 0; flex: 1">
        <span class="text">时间：</span>
        <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
        <span class="text">自定义：</span>
        <el-date-picker style="width: 250px" size="small" v-model="p_time" type="datetimerange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" @change="changeTimeRange">
        </el-date-picker>
      </div>
      <!-- <div style="margin-left: auto; color: #fb656a">每日12点前更新</div> -->
    </div>
    <div>
      <div class="video_list" v-if="tableData && tableData.length" v-infinite-scroll="loadMoreVideo"
        :infinite-scroll-disabled="video_load">
        <div class="video_item flex-row" v-for="item in tableData" :key="item.id">
          <div class="video_left" @click="goVideo(item)">
            <div class="video_img" :class="[item.share_url ? 'cursor' : '']">
              <img :src="item.cover" alt="" />
            </div>
          </div>
          <div class="video_right">
            <div class="video_title">
              {{ item.title }}
            </div>
            <div class="video_tags flex-row">
              <div class="video_tag flex-row">
                <div class="icon">
                  <img :src="
                                        $imageFilter(
                                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_my/zan_num.png',
                                          'w_240'
                                        )
                                      " alt="" />
                </div>
                <div class="tag_num">
                  {{ item.digg_count }}
                </div>
              </div>
              <div class="video_tag flex-row">
                <div class="icon">
                  <img :src="
                                        $imageFilter(
                                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_my/down_load_num.png',
                                          'w_240'
                                        )
                                      " alt="" />
                </div>
                <div class="tag_num">
                  {{ item.download_count }}
                </div>
              </div>

              <div class="video_tag flex-row">
                <div class="icon">
                  <img :src="
                                        $imageFilter(
                                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_my/play_num.png',
                                          'w_240'
                                        )
                                      " alt="" />
                </div>
                <div class="tag_num">
                  {{ item.play_count }}
                </div>
              </div>

              <div class="video_tag flex-row">
                <div class="icon">
                  <img :src="
                                        $imageFilter(
                                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_my/share_num.png',
                                          'w_240'
                                        )
                                      " alt="" />
                </div>
                <div class="tag_num">
                  {{ item.share_count }}
                </div>
              </div>

              <div class="video_tag flex-row">
                <div class="icon">
                  <img :src="
                                        $imageFilter(
                                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_my/zhuanfa_num.png',
                                          'w_240'
                                        )
                                      " alt="" />
                </div>
                <div class="tag_num">
                  {{ item.forward_count }}
                </div>
              </div>

              <div class="video_tag flex-row">
                <div class="icon">
                  <img :src="
                                        $imageFilter(
                                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_my/comment_num.png',
                                          'w_240'
                                        )
                                      " alt="" />
                </div>
                <div class="tag_num">
                  {{ item.comment_count }}
                </div>
              </div>
            </div>
            <div class="time">
              {{ item.create_time }}
            </div>
            <div class="btns">
              <div class="btn">
                <el-button type="primary" @click="copyLink(item)">
                  复制视频链接
                </el-button>
                <el-button type="primary" @click="commentList(item)">
                  评论列表
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="!tableData.length && loadEnd">
        <div class="flex-row items-center no_data">暂无数据</div>
      </div>
    </div>
    <!-- <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      ref="detrail"
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <el-table-column width="300px" label="视频信息" v-slot="{ row }">
        <div
          class="video_img flex-row"
          :class="[row.share_url ? 'cursor' : '']"
          @click="goVideo(row)"
        >
          <div class="video_img_con">
            <img :src="row.cover" alt="" />
          </div>
          <div class="video_img_info">
            <div class="video_img_title">
              {{ row.title }}
            </div>
            <div class="video_img_tag">
              <el-tag v-if="row.media_type == 2"> 图集 </el-tag>
              <el-tag type="success" v-if="row.media_type == 4"> 视频 </el-tag>
            </div>
          </div>
        </div>
      </el-table-column>
      <el-table-column label="点赞数" prop="digg_count"> </el-table-column>
      <el-table-column label="下载数" prop="download_count"> </el-table-column>
      <el-table-column label="播放量" prop="play_count"> </el-table-column>
      <el-table-column label="分享数" prop="share_count"> </el-table-column>
      <el-table-column label="转发数" prop="forward_count"> </el-table-column>
      <el-table-column label="评论数" prop="comment_count"> </el-table-column>
      <el-table-column width="200px" label="发布时间" prop="create_time">
      </el-table-column>
      <el-table-column label="操作" width="300px">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.share_url"
            @click="copyLink(scope.row)"
            type="primary"
          >
            复制视频链接
          </el-button>
          <el-button type="primary" @click="commentList(scope.row)">
            评论列表
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination> -->

    <el-dialog :visible.sync="show_comment" title="评论列表" width="1000px">
      <commentList v-if="show_comment" :id="current_id"></commentList>
    </el-dialog>
  </div>
</template>

<script>
import commentList from "./components/commentList.vue";
import myLabel from "@/components/navMain/crm/components/my_label";
export default {
  components: {
    commentList,
    myLabel
  },
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      show_comment: false,
      current_id: "",
      time_list: [
        { id: 1, name: "全部", value: "1" },
        { id: 2, name: "近一周", value: "2" },
        { id: 3, name: "近三月", value: "3" },
        { id: 4, name: "近六月", value: "4" },
      ],
      p_time: [],
      loadEnd: false

    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (this.p_time && this.p_time.length) {
        this.params.custom_stime = this.p_time[0]
        this.params.custom_etime = this.p_time[1]
      } else {
        this.params.custom_stime = ''
        this.params.custom_etime = ''
      }
      if (this.params.page == 1) {
        this.tableData = []
      }
      this.video_load = true;
      this.loadEnd = false
      // this.is_table_loading = true
      this.$http.getDouyinVideosList(this.params).then(res => {
        console.log(res);
        // this.is_table_loading = false
        if (res.status == 200) {
          this.tableData = this.tableData.concat(res.data.data)
          if (res.data.data.length < (this.params.per_page || 10)) {
            this.video_load = true;
          } else {
            this.video_load = false;
          }
          // this.params.total = res.data.total;
          // this.$set(this.tableData, res.data.data)
        }
        this.loadEnd = true
      }).catch(() => {
        this.loadEnd = true
      })
    },
    loadMoreVideo() {
      if (this.video_load) {
        return;
      }
      this.params.page++;
      this.getList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    copyLink(row) {
      if (!row.share_url) return
      this.$onCopyValue(row.share_url)
    },
    commentList(row) {
      this.current_id = row.item_id
      this.show_comment = true
    },
    onClickTime(item) {
      this.params.time_type = item.value;
      this.p_time = []
      this.params.page = 1;
      this.getList()
    },
    changeTimeRange(e) {
      this.params.time_type = ''
      if (e && e.length) {
        this.p_time = e;
        this.params.page = 1;
        this.getList()
        // this.getDataList();
      } else {
        this.params.p_time = [];
        this.params.page = 1;
        this.getList()
        // this.getDataList();
      }
    },
    goVideo(row) {
      if (!row.share_url) {
        return
      }
      window.open(row.share_url)
    }
  }
}
</script>

<style lang ="scss" scoped>
/* .detail {
  max-height: 70vh;
  overflow-y: auto;
} */
.bottom-border {
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e2e2;

  .text {
    font-size: 14px;
    color: #8a929f;
  }
}

.video_img {
  &.cursor {
    cursor: pointer;
  }

  .video_img_con {
    width: 80px;
    height: 80px;
    min-width: 80px;
    margin-right: 5px;
    overflow: hidden;
    border-radius: 5px;

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .video_img_tag {
    margin-top: 10px;
  }
}

.video_list {
  .video_item {
    margin-bottom: 24px;
    padding: 24px;

    .video_left {
      margin-right: 24px;

      .video_img {
        width: 150px;
        min-width: 150px;
        background: #8a929f;
        height: 200px;
        overflow: hidden;
        border-radius: 4px;

        img {
          width: 100%;
          object-fit: cover;
        }
      }
    }

    .video_right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;

      .video_title {
        color: #2e3c4e;
        font-size: 14px;
      }

      .video_tags {
        .video_tag {
          align-items: center;

          ~.video_tag {
            margin-left: 24px;
          }

          .icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;

            img {
              width: 100%;
              object-fit: cover;
            }
          }

          .tag_num {
            color: #2e3c4e;
            font-size: 14px;
          }
        }
      }

      .time {
        color: #8a929f;
        font-size: 12px;
      }
    }
  }
}

.no_data {
  justify-content: center;
  margin-top: 20px;
  color: #8a929f;
}
</style>