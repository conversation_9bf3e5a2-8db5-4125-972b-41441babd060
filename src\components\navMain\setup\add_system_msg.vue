<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->
    <el-form label-width="100px" :model="form" label-position="left">
      <el-form-item label="发送人">
        <p style="color:#999">系统提示消息</p>
      </el-form-item>
      <el-form-item label="接收消息">
        <el-select
          v-model="form.receiver_user_id"
          filterable
          remote
          reserve-keyword
          placeholder="请输入手机号"
          :remote-method="getUserData"
          :loading="user_loading"
          :clearable="true"
        >
          <el-option
            v-for="item in user_list"
            :key="item.id"
            :label="item.name + item.phone"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <i style="color:#999;margin-left:20px">不选默认发送所有人</i>
      </el-form-item>
      <el-form-item label="消息内容">
        <el-input
          type="textarea"
          v-model="form.msg"
          placeholder="请输入发送的消息内容"
          :rows="5"
        ></el-input>
      </el-form-item>
      <el-form-item size="large">
        <el-button
          type="primary"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          @click="onSubmit"
          >发送</el-button
        >
        <el-button @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
export default {
  name: "add_system_msg",
  data() {
    return {
      form: {
        // 发送者id
        sender_user_id: "",
        // 接收者
        receiver_user_id: "",
        msg: "",
      },
      user_loading: false,
      user_list: [],
      user_options: [],
      is_button_loading: false,
    };
  },
  mounted() {},
  // deactivated(){
  //   this.reset()
  // },
  methods: {
    getUserData(query) {
      this.user_loading = true;
      this.$http.searchUserByPhone(query).then((res) => {
        this.user_loading = false;
        if (res.status === 200) {
          this.user_list = res.data.data.map((item) => {
            return {
              phone: item.phone,
              id: item.id,
              name: item.name || item.nickname,
            };
          });
          this.user_options = this.user_list.filter((item) => {
            return item.phone.indexOf(query) > -1;
          });
        }
      });
    },
    onSubmit() {
      delete this.form.sender_user_id;
      if (!this.form.msg) {
        this.$message({
          message: "请输入内容提交",
          type: "error",
        });
        return false;
      } else {
        this.is_button_loading = true;
        if (!this.form.receiver_user_id) {
          delete this.form.receiver_user_id;
          this.$http.createMsgCtn(this.form).then((res) => {
            this.is_button_loading = false;
            if (res.status === 200) {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.$goPath("/system_msg");
            }
          });
        } else {
          this.$http.createMsgCtn(this.form).then((res) => {
            this.is_button_loading = false;
            if (res.status === 200) {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.$goPath("/system_msg");
            }
          });
        }
      }
    },
    goBack() {
      this.$goPath("/system_msg");
    },
    //重置表单
    reset(){
      this.form={
         // 发送者id
         sender_user_id: "",
        // 接收者
        receiver_user_id: "",
        msg: "",
      }
    }
  },
};
</script>

<style scoped>
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
/* .el-form {
	padding-top: 30px;
} */
.el-input {
  width: 300px;
}
.el-select {
  width: 300px;
}
.el-textarea {
  width: 300px;
}
</style>
