<template>
  <div class="pages">
    <template v-if="is_tabs !== 'wxwork'">
      <!--:is 的作用：会将div标签转换成 currentView 变量绑定的这个组件-->
      <!-- <div :is="is_tabs" keep-alive></div> -->
      <div class="content-box-crm" style="margin-bottom: 24px">
        <div class="bottom-border div row">
          <span class="text">客户来源：</span>
          <myLabel labelKey="title" :arr="source_list" @onClick="onClickType($event, 1)"></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户状态：</span>
          <myLabel labelKey="title" :arr="tracking_list" @onClick="onClickType($event, 2)"></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户类型：</span>
          <myLabel labelKey="title" :arr="type_list" :isdesc="true" @onClick="onClickType($event, 6)"></myLabel>
        </div>
        <!-- 折叠面板 -->
        <myCollapse :isActive="is_collapse">
          <template v-slot:content>
            <div class="bottom-border div row" style="padding-top: 24px">
              <span class="text">客户标签：</span>
              <el-cascader clearable size="small" v-model="params.label" :options="label_list" :props="{
                                value: 'id',
                                label: 'name',
                                children: 'label',
                                emitPath: false,
                              }" @change="handleChangeLabel"></el-cascader>
            </div>

            <div class="bottom-border div row" style="padding-top: 24px">
              <span class="text">绑定企微：</span>
              <myLabel labelKey="name" :arr="bind_list" @onClick="onClickType($event, 3)"></myLabel>
            </div>

            <div class="bottom-border div row" style="padding-top: 24px">
              <span class="text">筛选时间：</span>
              <myLabel :arr="time_list" @onClick="onClickType($event, 4)"></myLabel>
              <span class="text">自定义：</span>
              <el-date-picker style="width: 250px" size="small" v-model="timeValue" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="onChangeTime">
              </el-date-picker>
            </div>
          </template>
        </myCollapse>
        <div class="div row loadmore" @click="onChangeCollapse">
          <span class="text"> 更多 </span>
          <span :class="is_collapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
        </div>
      </div>
      <div class="content-box-crm">
        <div class="table-top-box div row">
          <div class="t-t-b-left div b-tabs row">
            <!-- <div
              v-for="(date, index) in type_list"
              :key="index"
              @click="changeTab(date, 2)"
              :class="{ isactive: params.type == date.id }"
              class="b-t-item"
            >
              <div class="date_value">
                {{ date.title }}
              </div>
            </div> -->
            <!-- 新线索 -->
            <div v-for="(item) in customer_list_type" :key="item.id" @click="getDataList(item)" class="b-t-item"
              :class="{ isactive: customer_type == item.id }">
              {{item.title}}
            </div>
          </div>
          <div class="t-t-b-right div row">
            <el-button @click="is_push_customer = true" type="primary" size="mini"
              class="btn el-icon-plus">录入客户</el-button>
            <el-button v-if="is_show_upload" @click="getFile" type="primary" size="mini">导入</el-button>
            <el-popover placement="bottom" width="500px" v-model="show_tel_search">
              <div>
                <div>搜索</div>
                <div class="inps div row align-center" style="margin: 10px 0">
                  <el-input placeholder="请输入手机号" v-model="params.mobile"
                    style="margin-right: 10px; width: 180px"></el-input>
                </div>
                <div class="btns" style="text-align: right">
                  <el-button @click="resetLoudongSearch()">重置</el-button>
                  <el-button type="primary" @click="handleSearch">确定</el-button>
                </div>
              </div>
              <div class="search_loudong div row align-center" slot="reference">
                <div class="seach_value">电话</div>
                <div class="sanjiao" :class="{ transt: show_tel_search }"></div>
              </div>
            </el-popover>
            <el-input size="small" placeholder="请输入客户线索" style="width: 256px; margin-left: 12px" v-model="params.keywords"
              @change="onChangeKeywords">
              <span slot="append" class="el-icon-search"></span>
            </el-input>
          </div>
        </div>
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="table_header" select
          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
          @selection-change="selectionChange"></myTable>
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="params.total" :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
        </el-pagination>
      </div>
    </template>

    <!-- <wxwork v-if="is_tabs === 'wxwork'"></wxwork> -->
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel">
      <!-- <myForm
        @clsoe="is_push_customer = false"
        :data1="n_client_field"
        :data2="n_company_field"
        :form="form"
        :form1="form1"
        @onClick="onClickForm"
      ></myForm> -->
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.source_id" placeholder="请选择">
                <el-option v-for="item in sourceLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <!-- <el-select
                style="width:100%"
                v-model="push_form.sex"
                placeholder="请选择"
              >
                <el-option label="男" :value="1"></el-option>
                <el-option label="女" :value="2"></el-option>
              </el-select> -->
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                  :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt="" @click="
                                      () => {
                                        push_form.sex = item.id;
                                      }
                                    " />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <div class="row input-box div">
              <div @click="onClickLevel(item)" class="l-item row" v-for="item in levelLabel" :key="item.id" :class="{
                                lisactive: item.id === push_form.level_id,
                              }">
                <div class="l-name">{{ item.title }}</div>
                <div class="l-name-1">{{ item.desc }}</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.intention_community"></el-input>
            </div>
          </el-form-item>
          <!-- <el-form-item label="意向区域：">
            <div class="row input-box div">
              <el-input
                placeholder="请输入"
                v-model="push_form.intention_street"
              ></el-input>
            </div>
          </el-form-item> -->
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer flex-row align-center">
        <el-switch v-model="push_form.add_type" active-color="#13ce66" inactive-color="#2d84fb" inactive-value="1"
          active-value="2" active-text="公海" inactive-text="私客">
        </el-switch>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
            定</el-button>
        </div>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_follow_dialog" title="跟进记录">
      <el-table v-loading="is_follow_loading" :data="is_follow_data" border>
        <el-table-column label="ID" prop="client_follow_id"></el-table-column>
        <el-table-column label="跟进类型" prop="type_title"></el-table-column>
        <el-table-column label="跟进内容" prop="content"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="is_follow_params.total" :page-size="is_follow_params.per_page" :current-page="is_follow_params.page"
        @current-change="onPageChangeQwFollow">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_dialog_upload" title="导入" :before-close="cancels">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <el-select style="width: 200px; margin-bottom: 10px" v-model="upload_form.type" placeholder="请选择">
        <el-option label="不覆盖" :value="1"></el-option>
        <el-option label="覆盖" :value="2"></el-option>
      </el-select>
      <!-- <mySelect
        :optionSource="admin_list"
        v-model="upload_form.admin_id"
        labelKey="user_name"
        valueKey="id"
        width="200px;"
        @page-change="onPageChangeAdmin"
        :paginationOption="{
          pageSize: admin_params.per_page, //每页显示条数
          currentPage: admin_params.page, //当前页
          pagerCount: 5, //按钮数，超过时会折叠
          total: admin_params.total, //总条数
        }"
      ></mySelect> -->
      <el-input placeholder="请选择成员" v-model="upload_form.admin_id" style="width: 200px; display: block"
        @focus="show_member_list = true">
        <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
      </el-input>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
        >添加文件</el-button
      > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancels">取 消</el-button>
        <el-button type="primary" @click="$refs.file.click()">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <memberListSingle v-if="show_member_list" :list="memberList" :defaultValue="selectedIds" ref="memberList"
        @onClickItem="selecetedMember">
      </memberListSingle>
    </el-dialog>
    <input type="file" ref="file" style="display: none" v-on:change="handleFileUpload($event)" />
  </div>
</template>

<script>
// import my from "./components/my";
// import seas from "./components/seas";
// import wxwork from "./components/wxwork";
// import myForm from "./components/customer_form";
import myTable from "@/components/components/my_table";
import myLabel from "./components/my_label.vue";
import myCollapse from "./components/collapse";
// import mySelect from "./components/my_select";
import memberListSingle from "../../navMain/site/components/memberList_single.vue"
export default {
  name: "crm_customer_seas_list",
  components: {
    // my,
    // seas,
    // myForm,
    // wxwork,
    myTable,
    // mySelect,
    myLabel,
    myCollapse,
    memberListSingle
  },
  data() {
    return {
      //选择成员弹框控制
      show_member_list: false,
      is_tabs: "all",
      selectedIds: [],
      tabs: [
        {
          id: 1,
          name: "所有客户",
          desc: "all",
        },
        {
          id: 2,
          name: "我的客户",
          desc: "my",
        },
        {
          id: 3,
          name: "公海客户",
          desc: "seas",
        },
        // {
        //   id: 4,
        //   name: "企微客户",
        //   desc: "wxwork",
        // },
      ],
      is_push_customer: false,
      push_form: {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        // intention_street: "",
        remark: "",
        add_type: "1",
      },
      sex_list: [
        { id: 1, name: "nan" },
        { id: 2, name: "nv3" },
      ],
      other_mobile: [{ mobile: "" }],
      type_list: [],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      timeValue: "",
      level_list: [],
      source_list: [],
      client_field: {
        // 获取客户字段
        type: 2,
      },
      n_client_field: {},
      n_company_field: {},
      form: {},
      form1: {},
      type: 1,
      tracking_list: [],
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      //成员部门信息
      memberList: [],
      multipleSelection: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 3,
        status: 0,
        mobile: "",
        sort_type: 0,
      },
      is_table_loading: false,
      tableData: [],
      table_header: [
        {
          prop: "id",
          label: "ID",
          width: "80px",
        },

        {
          label: "客户名称",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <span>{data.row.cname}</span>
                {data.row.wxqy_id > 0 ? (
                  <img
                    class="cus-img"
                    src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                  />
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
        {
          label: "手机号",
          prop: "mobile",
          width: "120px",
          render: (h, data) => {
            const mobileFilter = function (val) {
              let reg = /^(.{3}).*(.{3})$/;
              return val.replace(reg, "$1*****$2");
            };
            return <span>{mobileFilter(data.row.mobile)}</span>;
          },
        },
        {
          label: "客户线索",
          prop: "remark",
          render: (j, data) => {
            return (
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width:300px">
                  {data.row.remark}
                </div>
                <div style=" overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                  {data.row.remark}
                </div>
              </el-tooltip>
            );
          },
        },
        {
          prop: "created_at",
          label: "创建时间",
        },
        {
          prop: "operation_at",
          label: "更新时间",
        },
        {
          prop: "source.title",
          label: "来源",
        },
        {
          label: "状态",
          render: (h, data) => {
            return (
              <div>
                {data.row.tracking ? (
                  <span>{data.row.tracking.title}</span>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },

        {
          label: "操作",
          width: "120",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.onClickDetail(data.row);
                  }}
                >
                  详情
                </el-link>
                {this.params.status != 1 && !data.row.follow_id ? (
                  <el-link
                    style="margin-left:20px"
                    type="primary"
                    onClick={() => {
                      this.onClickGet(data.row);
                    }}
                  >
                    认领
                  </el-link>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_follow_dialog: false,
      is_follow_data: [],
      is_follow_loading: false,
      is_follow_params: {
        page: 1,
        total: 0,
        per_page: 10,
      },
      label_list: [],
      multipleSelectionname: [],
      is_collapse: false,
      tracking_params: {
        type: 1,
      },
      is_dialog_upload: false,
      upload_form: {
        type: 1,
        admin_id: "",
        file: "",
      },
      admin_params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
      },
      admin_list: [],
      is_button_loading: false,
      pull_params: {
        next_user_cursor: 0,
        next_customer_cursor: "",
      },
      list_tabs: [
        { id: 0, title: "全部" },
        { id: 1, title: "已认领" },
        { id: 2, title: "已跟进" },
        { id: 3, title: "未跟进" },
      ],
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过3000条",
        "标红标题对应内容为必填项，如不填写则过滤出去不进行导入",
        "导入内容如有重复的手机号则只会导入第一条重复数据",
        "选择覆盖，导入的数据和已有数据重复时，会覆盖之前的数据，选择不覆盖则过滤掉重复数据",
      ],
      no_follow_number: "",
      show_tel_search: false,
      is_show_upload: false, // 是否显示导入按钮
      customer_list_type: [
        {
          id: 0, title: "新增线索"
        },
        {
          id: 1, title: "最近活跃"
        },
        {
          id: 2, title: "多条线索"
        },
        {
          id: 3, title: "最新跟进",
        },
      ],
      customer_type: 0,
      notNewClue:true,
    };
  },
  computed: {
    levelLabel() {
      return this.level_list.filter((item) => {
        return item.id > 0;
      });
    },
    sourceLabel() {
      return this.source_list.filter((item) => {
        return item.id > 0;
      });
    },
    typeLabel() {
      return this.type_list.filter((item) => {
        return item.id > 0;
      });
    },
  },
  mounted() {
    this.params.status = parseInt(this.$route.query.status) || 0;
    this.getTypelist();
    this.getTrackingList();
    this.getLevelData();
    this.getSourceData();
    this.getLabelList();
    this.getAdmin();
    this.getCrmCustomerFollowNumber();
    this.getadminUser();
    this.getMemberList()
  },
  methods: {
    //删除部门人员
    delName() {
      this.upload_form.admin_id = ""
    },
    //获取部门成员信息
    getMemberList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data
        }
      })
    },
    //选中部门人员
    selecetedMember(e) {
      console.log(e);
      console.log(e.checkedNodes);
      if (e.checkedNodes && e.checkedNodes.length) {
        this.upload_form.admin_id = e.checkedNodes[e.checkedNodes.length - 1].name;

      } else {
        this.upload_form.admin_id = ""
      }
      this.show_member_list = false
    },
    //关闭弹窗之间的回调
    cancels() {
      this.is_dialog_upload = false;
      this.upload_form = {
        type: 1,
        admin_id: "",
        file: "",
      };
    },
    // 获取信息展示
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          if (res.data.roles[0].name === "站长") {
            this.is_show_upload = true;
          } else {
            this.getSiteCrmSetting(res.data.id);
          }
        }
      });
    },
    // 获取批量导入的crm站点设置
    getSiteCrmSetting(id) {
      this.$http.getAuthShow("batch_import_uid").then((res) => {
        if (res.status === 200) {
          if (res.data.indexOf(id) != -1 || !res.data) {
            this.is_show_upload = true;
          }
        }
      });
    },
    resetLoudongSearch() {
      this.params.mobile = "";
    },
    handleSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    getCrmCustomerFollowNumber() {
      this.$http.getCrmCustomerFollowNumber().then((res) => {
        if (res.status === 200) {
          this.no_follow_number = res.data;
        }
      });
    },
    changeTab(e, type) {
      if (type == 1) {
        this.params.status = e.id;
      } else {
        this.params.type = e.id;
      }
      this.params.page = 1;
      this.getDataList();
    },
    getTypelist() {
      this.$http.getCrmCustomerTypeDataNopage().then((res) => {
        if (res.status === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
          this.push_form.type = res.data.filter((item) => {
            return item.is_default;
          })[0].id;
          let cus_type = parseInt(this.$route.query.cus_type);
          res.data.map((item) => {
            if (cus_type == 1 && item.title == "求购") {
              this.params.type = item.id;
            }
            if (cus_type == 2 && item.title == "求租") {
              this.params.type = item.id;
            }
          });
        }
        this.getDataList(this.customer_list_type[0]);
      });
    },
    onPageChangeAdmin(e) {
      this.admin_params.page = e;
      this.getAdmin();
    },
    getAdmin() {
      this.$http
        .getUserList(
          this.admin_params.page,
          this.admin_params.per_page,
          this.admin_params.user_name
        )
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    getLabelList() {
      this.$http.getLabelGroupNoPage().then((res) => {
        if (res.status === 200) {
          this.label_list = res.data;
        }
      });
    },
    getDataList(item) {
      // 如果是“新增线索”就删除更新时间列
      if(item && item.title == '新增线索') {
        let isNewClue_update = this.table_header.filter((remo) => {
          return remo.label == "更新时间";
        })
        if(isNewClue_update.length != 0) {
          if(this.notNewClue) {
            this.table_header.splice(5,1);
          }else {
            this.table_header.splice(4,1);
          }
        }
        let isNewClue_create = this.table_header.filter((remo) => {
          return remo.label == "创建时间";
        })
        if(isNewClue_create.length == 0) {
          this.table_header.splice(4, 0, {prop: "created_at", label: "创建时间"});
        }
      } 
      // 如果不是“新增线索”就删除创建时间，添加更新时间
      else if(item && item.title != '新增线索') {
        this.notNewClue = false;
        let notNewClue_create = this.table_header.filter((remo) => {
          return remo.label == "创建时间";
        })
        if(notNewClue_create.length != 0) {
          this.table_header.splice(4,1);
        }
        let notNewClue_update = this.table_header.filter((remo) => {
          return remo.label == "更新时间";
        })
        if(notNewClue_update.length == 0) {
          this.table_header.splice(4, 0, {prop: "operation_at", label: "更新时间"});
        }
      }
      if(item) {
        this.customer_type = item.id;
        this.params.sort_type = item.id;
      }
      this.is_table_loading = true;
      if (!this.params.source_id) {
        delete this.params.source_id;
      }
      if (!this.params.tracking_id) {
        delete this.params.tracking_id;
      }
      if (!this.params.is_bind) {
        delete this.params.is_bind;
      }
      this.$http
        .getCrmCustomerClientList({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
          }
        });
    },
    getLevelData() {
      this.$http.getCrmCustomerLevelNopage().then((res) => {
        if (res.status === 200) {
          this.level_list = [{ title: "全部", id: 0 }, ...res.data];
        }
      });
    },
    getTrackingList() {
      this.$http
        .getCrmCustomerFollowInfo({ params: this.tracking_params })
        .then((res) => {
          if (res.status === 200) {
            this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
          }
        });
    },
    onClickSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    onClickType(e, type) {
      switch (type) {
        case 1:
          this.params.source_id = e.id;
          break;
        case 2:
          this.params.tracking_id = e.id;
          break;
        case 3:
          this.params.is_bind = e.id;
          break;
        case 4:
          this.params.date_type = e.id;
          break;
        case 5:
          this.params.level_id = e.id;
          break;
        case 6:
          this.params.type = e.id;
          break;
        default:
          break;
      }
      this.params.page = 1;
      this.getDataList();
    },
    onChangeTime(e) {
      this.params.start_date = e ? e[0] : "";
      this.params.end_date = e ? e[1] : "";
      this.params.page = 1;
      this.getDataList();
    },
    getSourceData() {
      this.$http.getCrmCustomerSourceNopage().then((res) => {
        if (res.status === 200) {
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
          this.push_form.source_id = res.data.filter((item) => {
            return item.is_default == 1;
          })[0].id;
        }
      });
    },
    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    addDomain() {
      this.other_mobile.push({
        mobile: "",
      });
    },
    onClickLevel(item) {
      this.push_form.level_id = item.id;
    },
    onClickTypeClient(item) {
      this.push_form.type = item.id;
    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    selectionChange(e) {
      this.multipleSelectionname = e;
      let arr = e.map((item) => {
        return item.id;
      });
      this.multipleSelection = arr;
    },
    onClickDetail(row) {
      let url = `/crm_customer_detail?id=${row.id}&type=seas`;
      this.$goPath(url);
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onPageChangeQwFollow(e) {
      this.is_follow_params.page = e;
      this.getFollow();
    },
    isShowFollow(row) {
      this.is_follow_dialog = true;
      this.is_follow_params.client_id = row.client_id;
      this.getFollow();
    },
    getFollow() {
      this.is_follow_loading = true;
      this.$http
        .getCrmCustomerFollowData({ params: this.is_follow_params })
        .then((res) => {
          this.is_follow_loading = false;
          if (res.status === 200) {
            this.is_follow_data = res.data.data;
            this.is_follow_params.total = res.data.total;
          }
        });
    },
    // onClickForm(e) {
    //   this.$http.setCrmCustomerData(e).then((res) => {
    //     if (res.status === 200) {
    //       this.$message.success("操作成功");
    //       this.is_push_customer = false;
    //       this.form = {};
    //       this.form1 = {};
    //       this.params.page = 1;
    //       this.getDataList();
    //     }
    //   });
    // },
    onClickForm() {
      if (this.other_mobile.length > 0) {
        let arr = this.other_mobile.map((item) => {
          return item.mobile;
        });
        let othertel = arr.filter((item, index) => {
          if (index) {
            return item;
          }
        });
        this.push_form.mobile = arr[0];
        this.push_form.subsidiary_mobile = othertel.join(",");
      }
      if (!this.push_form.mobile) {
        this.$message.error("请检查联系方式");
        return;
      }
      if (!this.push_form.cname) {
        this.$message.error("请检查客户姓名");
        return;
      }
      if (!this.push_form.sex) {
        this.$message.error("请检查客户性别");
        return;
      }
      if (!this.push_form.level_id) {
        this.$message.error("请检查客户等级");
        return;
      }
      if (!this.push_form.type) {
        this.$message.error("请检查客户类型");
        return;
      }
      if (!this.push_form.source_id) {
        this.$message.error("请检查客户来源");
        return;
      }
      this.is_button_loading = true;
      this.$http.setCrmCustomerDataV2(this.push_form).then((res) => {
        this.is_button_loading = false;
        this.is_push_customer = false;
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
        }
      });
    },
    // 折叠面板
    onChangeCollapse() {
      this.is_collapse = !this.is_collapse;
    },
    onClickGet(row) {
      this.$confirm("是否认领该客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.getCrmCustomerPublick({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("认领成功");
              let url = `/crm_customer_detail?id=${row.id}&type=my`;
              this.$goPath(url);
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    handleChangeLabel() {
      this.params.page = 1;
      this.getDataList();
    },
    getFile() {
      this.is_dialog_upload = true;
      // this.$refs.file.click();
    },
    // 获取文件
    handleFileUpload(event) {
      // 阻止发生默认行为
      event.preventDefault();
      let file = this.$refs.file.files[0];
      let formData = new FormData();
      formData.append("file", file);
      formData.append("admin_id", this.upload_form.admin_id || 0);
      formData.append("type", this.upload_form.type);
      // this.formData.get("file");
      this.onUpload(formData);
    },
    // 上传文件
    onUpload(formData) {
      this.$message.success("正在上传...");
      this.$http.uploadCrmCustomerData(formData).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.params.page = 1;
          this.getDataList();
          this.is_dialog_upload = false;
        }
      });
    },
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/client_template.xls"
      );
    },
    //表单重置
    reset() {
      this.push_form = {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        // intention_street: "",
        remark: "",
      };
      this.other_mobile = [{ mobile: "" }];
    },
    //取消
    cancel() {
      this.reset();
      this.is_push_customer = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 0 24px 24px;

  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    margin-left: -24px;
    margin-right: -24px;

    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }

  .grid-content {
    background: #fff;
    height: 113px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
    }

    .left {
      font-size: 14px;
      color: #8a929f;
      margin-right: 64px;
    }

    .number {
      display: flex;
      align-items: center;

      .number1 {
        font-size: 24px;
        color: #2e3c4e;
        margin-right: 10px;
      }

      .number2 {
        font-size: 12px;
        color: #fe6c17;
      }
    }
  }
}

.isbottom {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-border {
  align-items: center;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
  }
}

.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: flex-end;
  cursor: pointer;
  font-size: 14px;

  .text {
    font-size: 14px;
  }
}

.labelrow {
  margin-bottom: 20px;
}

.crmuserbox {
  align-items: center;

  span {
    display: block;
  }

  img {
    border-radius: 50%;
  }

  .label {
    width: fit-content;
    padding: 0 6px;
    background: #2d84fb;
    border-radius: 4px;
    color: #fff;
  }
}
</style>
<style lang="scss">
.cus-box {
  align-items: center;
  line-height: 1;

  .cus-img {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.b-tabs {
  cursor: pointer;

  .b-t-item {
    margin-right: 24px;
    color: #8a929f;
    position: relative;

    &.isactive {
      color: #00a3ff;

      &::after {
        position: absolute;
        content: "";
        left: 50%;
        transform: translateX(-50%);
        height: 3px;
        background: #2d84fb;
        width: 100%;
        display: block;
        margin-top: 4px;
      }
    }
  }
}

.search_loudong {
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
</style>
