<template>
  <div class="pages">
    <div class="content-box-crm padd0">
      <el-button type="primary" @click="is_upload_dialog = true" size="mini">导入</el-button>
      <input type="file" ref="file_p" accept=".xls,.doc,.txt,.xlsx" style="display: none"
        v-on:change="handleFileUpload($event)" />
    </div>
    <el-dialog width="660px" :visible.sync="is_upload_dialog" title="导入">
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_upload_dialog = false">取 消</el-button>
        <el-button type="primary" @click="$refs.file_p.click()">开始导入</el-button>
      </span>
    </el-dialog>


  </div>
</template>
  
<script>
  import { mapState } from "vuex";
  // import mySlide from './components/new_slide';
  export default {
    name: "crm_customer_personnel",
    components: {
      // mySlide,
    },
    data() {
      return {
        is_upload_dialog: false,
        website_id: '',
   
      };
    },
    computed: {
      ...mapState(["website_info"]),
    },
    created() {
      this.website_id = this.$route.query.website_id
   
    },
    methods: {
     
   
      onUploadTem() {
        window.open(
          "https://img.tfcs.cn/backup/static/admin/xls/personal_template.xlsx"
        );
      },
      handleFileUpload(event) {
        // 阻止默认行为
        event.preventDefault();
        let file = this.$refs.file_p.files[0];
        let formData = new FormData();
        formData.append("excel", file);
        formData.append("type", 1);
        this.onUpload(formData);
      },
      onUpload(formData) {
        this.$message.success("正在上传...");
        //  self_auth_create_all区分是否是企业微信自建应用的导入程序
        this.$http.uploadCrmCustomerPersonnelData(formData).then((res) => {
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.is_upload_dialog = false;
          }
        });
      },
    },
  };
  </script>
  
<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .top {
    align-items: center;
  }

  .table-top-box {
    justify-content: flex-start;
    flex-wrap: wrap;

    .text {
      margin-right: 10px;
    }

    .blue {
      margin-right: 10px;
    }
  }

  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }

  .bmtitle {
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 24px;
    border-bottom: 1px solid #eee;

    &.add_department {
      justify-content: flex-end;

      .add_depart {
        font-size: 30px;
        cursor: pointer;
        justify-self: flex-end;
      }
    }
  }

  .el-tag.el-tag--danger {
    margin-left: 5px;
  }

  .el-tag.el-tag--success {
    margin-left: 5px
  }

  .title-bm {
    font-size: 18px;
  }
}

.labelrow {
  margin-bottom: 20px;
}

.content-crm {
  min-height: 600px;
}

.table {
  .name {
    ~.name {
      margin-left: 8px;
    }
  }

  .row-info {
    .row-img {
      width: 30px;
      height: 30px;
      overflow: hidden;
      border-radius: 50%;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .row_no_img {
        width: 100%;
        height: 100%;
        line-height: 1;
        font-weight: 600;
        font-size: 16px;
        color: #fff;
        background: #2d84fb;
      }
    }

    .info {
      .row-name {
        .wechat {
          width: 15px;
          height: 15px;
          margin-left: 8px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
}

.padd0 {
  padding: 10px;
}

.cur_point {
  cursor: pointer;
}
</style>
  
  