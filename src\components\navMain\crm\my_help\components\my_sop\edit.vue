<template>
  <div class="add">
    <!-- <div class="tips">
      <div>
        1.什么是群活码：通过多个群聊配置一个二维码，客户通过扫描二维码加入群聊，当前面的群人数
        达到上限后，自动发送后面的群二维码，从而突破群聊人数限制，实现一码多群功能。
      </div>
      <div>
        2.怎么用：首先给群活码配置接待员工，客户通过群活码添加员工为好友，添加通过后自动向客户
        发送入群引导语和群聊二维码，再通过扫描群聊二维码入群，当二维码到期后可手动更新二维码。
      </div>
    </div> -->
    <el-form label-width="100px">
      <div class="title">基本信息</div>

      <el-form-item label="sop名称">
        <div class="form-item-block">
          <el-input
            placeholder="请输入sop名称"
            v-model="form_params.task_name"
            style="width: 240px; margin-right: 12px"
          >
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="送达客户">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-button
            size="big"
            class="member_button"
            v-for="item in form_selectedMember"
            :key="item.id"
            >{{ item.name || item.user_name }}</el-button
          >
          <el-button size="big" class="el-icon-plus" @click="showAddMember"
            >添加客户</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="立即群发">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-radio-group v-model="form_params.opt_cate" size="mini">
            <el-radio :label="1" border>立即发送</el-radio>
            <el-radio :label="2" border>定时发送</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item label="执行时间" v-if="form_params.opt_cate == 2">
        <div class="form-item-block flex-row align-center f-wrap">
          <el-radio-group v-model="form_params.time_type" size="mini">
            <el-radio :label="1" border>自定义</el-radio>
            <el-radio :label="2" border>每天</el-radio>
            <el-radio :label="3" border>每周</el-radio>
            <el-radio :label="4" border>每月</el-radio>
          </el-radio-group>
        </div>
        <div v-if="form_params.time_type == 1">
          <el-date-picker
            style="width: 250px"
            size="small"
            v-model="form_params.send_time"
            type="datetime"
            placeholder="请选择日期"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
          >
          </el-date-picker>
        </div>
      </el-form-item>

      <div class="title">发送内容设置</div>
      <div class="tips warning">
        温馨提示 ： 因企业微信限制客户每天只能接收一条群发消息
      </div>
      <el-form-item label="文案">
        <welcome-mes
          style="padding-right: 20px"
          ref="member_sop_edit"
          :value="welcome_mes"
        ></welcome-mes>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
    <el-dialog
      :visible.sync="show_add_member"
      width="600px"
      title="选择送达的客户"
      append-to-body
    >
      <div class="member">
        <div class="member_box flex-row">
          <div class="right left member_con flex-1">
            <div class="select_title">选择客户</div>
            <div class="selected_list">
              <memberList
                v-if="show_add_member"
                :list="customerList"
                :defaultValue="selectedIds"
                @onClickItem="selecetedCustomer"
                :checkStrictly="false"
                ref="customerList"
                :defaultProps="{
                  children: 'subs',
                  label: 'user_name',
                  value: 'id',
                }"
              >
                <!-- disabled: (data) => {
                    return !data.pid;
                  }, -->
              </memberList>
            </div>
          </div>
          <div class="right left member_con flex-1">
            <div class="select_title">已选择客户</div>
            <div class="selected_list">
              <div
                class="selected_item flex-row align-center"
                v-for="item in selectedList"
                :key="item.id"
              >
                <div class="name flex-1">
                  {{ item.name || item.user_name }}
                </div>
                <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="selectMemberOk"
            :loading="isSubmiting"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import memberList from "@/components/navMain/crm/components/memberList";
import config from "@/utils/config";
import welcomeMes from "@/components/navMain/crm/components/welcome_mes";
export default {
  components: { memberList, welcomeMes },
  props: ['form'],
  data() {
    return {
      form_params: {
        sender: '',
        time_type: '',
        send_time: '',
        is_open: true,
      },
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      show_select_dia: false,
      group_list: [],
      value1: "",
      params_type: 2,
      params_type_arr: [],
      welcome_mes: {
        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      miniCover: "",
      filename: "",
      videoname: "",
      imgname: "",
      img_params: {
        page: 1,
        per_page: 10,
        type: 1,
      },
      imgList: [],
      currentImg: '',
      type_arr: ["text", "image", "link", "miniprogram", "video", "file"],
      customerList: [],
      selectedCustomerIds: [],
      form_send_selectedMember: [],
      show_add_send_member: false,
      selectedSendList: [],
      selectedSendIds: [],
      memberList: []

    }
  },
  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  created() {
    this.form_params = Object.assign({}, this.form)
    if (this.form_params.is_open == 1) {
      this.form_params.is_open = true
    } else {
      this.form_params.is_open = false
    }
    if (this.form_params.time_type != 1) {
      this.form_params.send_time = this.formatDateTime(new Date())
    }

    // this.form_send_selectedMember = this.form_params.sender
    // console.log(this.form_send_selectedMember, this.form_params.sender);
    for (const key in this.form_params.media) {
      this.$set(
        this.welcome_mes,
        key,
        JSON.parse(JSON.stringify(this.form_params.media[key]))
      );
      // this.welcome_mes[key] = JSON.parse(JSON.stringify(this.form_params.media))
    }
    if (this.form_params.media.image) {
      this.imgname = this.form_params.media.image.org_url;
      delete this.welcome_mes.image.org_url;
      this.params_type_arr.push(this.form_params.media.image.type);
    }
    if (this.form_params.media.file) {
      this.filename = this.form_params.media.file.org_url;
      delete this.welcome_mes.file.org_url;
      this.params_type_arr.push(this.form_params.media.file.type);
    }
    if (this.form_params.media.video) {

      this.welcome_mes.video.pic_url = this.form_params.media.video.org_url;
      delete this.welcome_mes.video.org_url;
      this.params_type_arr.push(this.form_params.media.video.type);
    }
    if (this.form_params.media.miniprogram) {
      this.welcome_mes.miniprogram.pic_url = this.form_params.media.miniprogram.org_url;
      delete this.welcome_mes.miniprogram.org_url;
      this.params_type_arr.push(this.form_params.media.miniprogram.type);
    }
    if (this.form_params.media.link) {
      this.params_type_arr.push(this.form_params.media.link.type);
      delete this.welcome_mes.link.org_url;
    }
    this.params_type_arr_old = [];
    this.params_type_arr.map((item) => {
      this.params_type_arr_old.push(this.type_arr[item - 1]);
    });
    this.selectedIds = []
    this.selectedIdsOld = []
    this.halfSelectedIds = []
    this.allSelected = []
    this.halfSelectedIdsOld = []
    this.allSelectedOld = []
    this.form_params.external_userid.map(item => {
      if (item.is_all == 1) {  //全部
        item.sender.map(send => {
          this.selectedIds.push(send.id)
          this.selectedIdsOld.push(send.id)
          this.allSelected.push(send.id)
          this.allSelectedOld.push(send.id)
        })
        // this.form_selectedMember.push(...item.sender)

      } else {
        item.sender.map(send => {
          this.halfSelectedIds.push(send.id)
          this.halfSelectedIdsOld.push(send.id)
          // let id = send.id
          item.external_user.map(user => {
            user.user_name = user.remark
            // item.send_id = send.id + "_" + item.id
            this.selectedIdsOld.push(send.id + "_" + user.id)

            this.selectedIds.push(send.id + "_" + user.id)
            this.form_selectedMember.push(user)
            return user
          })
          // let name ='userid_'+send.id
          // this.selectedCustomerIds.push(send.id)
        })
      }

    })
    if (this.selectedSendList.length) {
      // this.selectSendMemberOk()
      this.selectedSendList.map(item => {
        if (timer) clearTimeout(timer)
        let timer = setTimeout(() => {
          this.getCustomerList(item)
        }, 300);
        return item

      })
    }
    this.getCustomerList(this.form.sender[0])
    // this.getDepartment()

  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params)
      if (params.is_open) {
        params.is_open = 1;
      } else {
        params.is_open = 0;
      }
      if (params.time_type != 1) {
        delete params.send_time
      }
      let oldTypeArr = Object.keys(this.form_params.media)
      let _welcome_mes = Object.assign({}, this.$refs.member_sop_edit.welcome_mes);
      for (const key in _welcome_mes) {
        if (_welcome_mes[key].org_url) {
          delete _welcome_mes[key].org_url
        }
      }
      if (
        _welcome_mes.image &&
        !_welcome_mes.image.media_id
      ) {
        if (oldTypeArr.includes("image")) {
          _welcome_mes.image = this.form_params.media.image
          _welcome_mes.image.is_del = 1
          delete _welcome_mes.image.name
        } else {
          delete _welcome_mes.image;
        }

      }
      if (_welcome_mes.link) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key] && Object.keys(linkArr).includes(key)) {
            emptyLink.push(key);
          }
        }

        if (emptyLink.length == Object.keys(linkArr).length) {
          if (oldTypeArr.includes("link")) {
            _welcome_mes.link = this.form_params.media.link
            delete _welcome_mes.link.appid
            _welcome_mes.link.is_del = 1

          } else {
            emptyLink.length = 0;
            delete _welcome_mes.link;
          }

          // emptyLink.length = 0;
          // delete _welcome_mes.link;
        } else if (emptyLink.length) {
          if (!oldTypeArr.includes("link")) {
            this.$message.warning(linkArr[emptyLink[0]]);
            return;
          }

        }
      }

      if (_welcome_mes.miniprogram) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          url: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (
            !_welcome_mes.miniprogram[key] && Object.keys(miniArr).includes(key)
          ) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length == Object.keys(miniArr).length) {
          if (oldTypeArr.includes("miniprogram")) {
            _welcome_mes.miniprogram = this.form_params.media.miniprogram
            _welcome_mes.miniprogram.is_del = 1
          } else {
            emptyMini.length = 0;
            delete _welcome_mes.miniprogram;
          }

        } else if (emptyMini.length) {
          if (!oldTypeArr.includes("miniprogram")) {
            this.$message.warning(miniArr[emptyMini[0]]);
            return;
          }
        }
      }

      if (
        _welcome_mes.video &&
        !_welcome_mes.video.media_id
      ) {
        if (oldTypeArr.includes("video")) {
          _welcome_mes.video = this.form_params.media.video
          _welcome_mes.video.is_del = 1
        } else {
          delete _welcome_mes.video;
        }

      }
      if (
        _welcome_mes.file &&
        !_welcome_mes.file.media_id
      ) {
        if (oldTypeArr.includes("file")) {
          _welcome_mes.file = this.form_params.media.file
          _welcome_mes.file.is_del = 1
        } else {
          delete _welcome_mes.file;
        }
      }

      params.media = JSON.stringify(_welcome_mes);
      params.sender = this.form.sender[0].id + ''
      let external_userid = []
      if (this.selectedIdsOld.length) {
        this.selectedIdsOld.map(item => {
          if ((!this.selectedIds.includes(item + '')) && (!this.selectedIds.includes(item + ''))) {
            this.selectedIds.push(item)
          }
        })
      }
      if (this.halfSelectedIdsOld.length) {
        this.halfSelectedIdsOld.map(item => {
          if ((!this.halfSelectedIds.includes(item + '')) && (!this.halfSelectedIds.includes(+item))) {
            this.halfSelectedIds.push(item)
          }
        })
        // this.halfSelectedIds = this.halfSelectedIdsOld.concat(this.halfSelectedIds)
      }
      this.halfSelectedIds.map(id => {
        if (id) {
          let name = "userid_" + id
          this.selectedIds.filter(item => item && (item + '').split("_").length == 2).map(item => {
            if (id == item.split("_")[0]) {
              name += "/" + item.split("_")[1]
            }
            return item
          })
          external_userid.push(name)
        }

        return id
      })
      let selce = this.selectedIds.filter(item => item && (item + '').split("_").length == 1).map(item => item && ('userid_' + item))
      params.external_userid = external_userid.concat(selce).join(',')
      // params.external_userid = params.external_userid.join(',')
      if (this.isSubmiting) return
      this.isSubmiting = true

      this.$http.editCrmMySop(params).then(res => {
        if (res.status == 200) {
          this.$message.success("编辑成功");
          setTimeout(() => {
            this.isSubmiting = false
          }, 200);
          this.$emit("success")
        } else {
          this.isSubmiting = false
          this.$message.error("编辑失败");
        }
      }).catch(() => {
        this.isSubmiting = false
      })
    },
    formatDateTime(date) {
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      let hour = date.getHours()
      let minute = date.getMinutes()
      // let second = date.getSeconds()
      if (month < 10) {
        month = `0${month}`
      }
      if (day < 10) {
        day = `0${day}`
      }
      hour = hour.toString().padStart(2, '0')
      minute = minute.toString().padStart(2, '0')
      // second = second.toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    },
    changeSendMember() {
      this.$nextTick(() => {
        this.$refs.sendMemberList.changeSelected(this.selectedSendIds)
      })

    },


    // // 获取部门列表
    // async getDepartment() {
    //   let res = await this.$http.getCrmDepartmentList()
    //     .catch(err => {
    //       console.log(err);
    //     })
    //   if (res.status == 200) {
    //     this.memberList = res.data
    //   }
    // },

    cancel() {
      this.$emit('cancel')
    },

    // 移除选中的人员
    deleteSendSelected(e) {
      let idx = this.selectedSendList.findIndex(item => item.id == e.id)
      this.selectedSendList.splice(idx, 1)
      this.selectedSendIds.splice(idx, 1)

      setTimeout(() => {
        this.$refs.sendMemberList.changeSelected(this.selectedSendIds, true)
      }, 100);


    },
    // 显示选择会员弹框
    showAddSendMember() {
      this.show_add_send_member = true
    },
    // 选中会员
    selecetedSendMember(e) {
      this.selectedSendIdsOld = this.selectedSendIdsOld.filter(item => !e.checkedKeys.includes(item + '') && (!e.checkedKeys.includes(Number(item))))
      this.selectedSendListOld = this.selectedSendListOld.filter(item => !e.checkedKeys.includes(item.id + '') && (!e.checkedKeys.includes(Number(item.id))))
      e.checkedKeys = e.checkedKeys.concat(this.selectedSendIdsOld)
      e.checkedNodes = e.checkedNodes.concat(this.selectedSendListOld)
      this.selectedSendIds = [...new Set(e.checkedKeys)]
      this.selectedSendList = this.newsetArr(e.checkedNodes)

    },
    newsetArr(arr, key = "id") {
      var result = [];
      var obj = {};
      for (var i = 0; i < arr.length; i++) {
        if (!obj[arr[i][key]]) {
          result.push(arr[i]);
          obj[arr[i][key]] = true;
        }
      }
      return result
    },
    selectSendMemberOk() {
      this.form_params.sender = this.selectedSendIds
      this.form_send_selectedMember = this.selectedSendList
      this.show_add_send_member = false
      this.selectedSendList.map(item => {
        if (timer) clearTimeout(timer)
        var timer = setTimeout(() => {
          this.getCustomerList(item)
        }, 200);

      })
    },

    // 移除选中的人员
    deleteSelected(e) {
      console.log(e.id);
      let curr = this.selectedList.find(item => item.id == e.id)
      console.log(curr);
      if (curr && (!curr.pid)) {
        let subIds = []
        curr.subs.map(item => {
          subIds.push(item.id)
        })
        let selIds = this.selectedIds
        let selList = this.selectedList
        this.selectedIds = subIds.reduce((total, current) => {
          (!selIds.includes(current)) && total.push(current)
          return total
        }, [])
        this.selectedList = selList.reduce((total, current) => {
          (!selIds.includes(current.id)) && total.push(current)
          return total
        }, [])
        setTimeout(() => {
          this.$refs.customerList.changeSelected(this.selectedIds, false)
        }, 100);

      } else {
        let idx = this.selectedList.findIndex(item => item.id == e.id)
        this.selectedList.splice(idx, 1)
        this.selectedIds.splice(idx, 1)

        this.$refs.customerList.changeSelected(this.selectedIds, false)
      }
      this.selectedIdsOld = this.selectedIdsOld.filter(old => {
        console.log(old);
        return old != e.id
      })
      console.log(this.selectedIds, this.selectedIdsOld, 1111);
    },
    // 显示选择会员弹框
    showAddMember() {
      this.show_add_member = true
    },
    selecetedCustomer(e) {

      this.selectedIds = e.checkedKeys
      this.selectedList = e.checkedNodes
      this.halfSelectedIds = e.halfCheckedKeys
      this.allSelected = this.selectedIds.filter(item => (item + '').split("_").length
        > 1)
      this.selectedIdsOld = this.allSelected.filter(item => (!this.allSelected.includes(item + '')) && (!this.allSelected.includes(Number(item))))
    },
    currentCheckChange(e) {
      console.log(e);
      // let { data, type } = e
      // if (type == true) {
      //   this.getCustomerList(data)
      // }

    },
    getCustomerList(data) {
      this.$http.getCrmMemberCustomer(data.id).then(res => {
        if (res.status == 200) {
          let cIndex = this.customerList.findIndex(item => item.id == data.id)
          if (cIndex < 0 && res.data.data.length) {
            res.data.data.map(item => {
              item.user_name = item.remark
              item.pid = data.id
              item.old_id = item.id
              item.id = data.id + '_' + item.id
              return item
            })
            this.customerList.push({
              id: data.id,
              user_name: data.name || data.user_name,
              subs: res.data.data
            })
          }
        }

      })
    },
    // 选中会员
    selecetedMember() {
      // this.selectedIds = e.checkedKeys
      // this.selectedList = e.checkedNodes
    },
    selectMemberOk() {

      this.form_selectedMember = this.selectedList.filter(item => (item.id + '').split("_").length > 1)
      this.show_add_member = false
    },

    checkChange(e, type) {
      if (e) {
        this.params_type = type;
        var category = config.CATEGORY_IM_IMAGE;
        switch (type) {
          case 2:
          case 3:
          case 4:
            category = config.CATEGORY_IM_IMAGE;
            break;
          case 5:
            category = config.CATEGORY_IM_VIDEO;
            break;
          case 6:
            category = config.CATEGORY_IM_FILE;
            break;
          default:
            break;
        }
        this.website_img = `/api/common/file/upload/admin?category=${category}`;
      }
    },
    showSelectDia() {
      this.img_params.page = 1;
      this.getImgList();
      this.show_select_dia = true;
    },

    // 选中图片
    selectAvatar(e) {
      this.currentImg = e.url;
      // this.imgList.map(item => item.checked = false)
      // e.checked = true
    },
    // 选中图片确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      switch (this.params_type) {
        case 2:
          // 图片
          this.welcome_mes.image.media_id = current.media_id;
          this.imgname = current.url;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = current.url;
          // this.welcome_mes.link.pic_url = url;
          this.welcome_mes.link.media_id = current.media_id;
          break;
        case 4:
          // 小程序 miniprogram   media_id
          this.miniCover = current.url;
          // this.welcome_mes.miniprogram.pic_url = url;
          this.welcome_mes.miniprogram.media_id = current.media_id;
          break;
        case 5:
          // 视频
          this.videoname = current.url;
          this.welcome_mes.video.media_id = current.media_id;
          break;
        case 6:
          // 文件
          this.filename = current.url;
          this.welcome_mes.file.media_id = current.media_id;
          break;

        default:
          break;
      }
      this.show_select_dia = false;
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      this.loadMore = false
      switch (this.params_type) {
        case 2:
        case 3:
        case 4:
          this.img_params.type = 1;
          break;
        case 5:
          this.img_params.type = 3;
          break;
        case 6:
          this.img_params.type = 4;
          break;

        default:
          break;
      }
      this.$http.getCrmServiceAvatarList(this.img_params).then((res) => {
        if (res.status == 200) {
          if (this.img_params.type == 4 || this.img_params.type == 3) {
            res.data.data.map((item) => {
              item.user_name = item.url.substring(
                item.url.lastIndexOf("/") + 1
              );
              return item;
            });
          }
          this.imgList = this.imgList.concat(res.data.data);
          if (res.data.data.length == this.img_params.per_page) {
            this.loadMore = true;
          } else {
            this.loadMore = false;
          }
        }
      }).catch(() => {
        this.loadMore = false
      });
    },
    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.params_type) {
        case 2:
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          this.imgname = url;
          this.welcome_mes.image.media_id = res.data.media_id;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = url;
          // this.welcome_mes.link.media_id = res.data.media_id;
          break;
        case 4:
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          // 小程序 miniprogram   media_id
          this.miniCover = url;
          this.welcome_mes.miniprogram.media_id = res.data.media_id;
          break;
        case 5:
          // 视频
          params.type = 3;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败 请检查素材大小");
            return;
          });

          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case 6:
          // 文件
          params.type = 4;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败");
          });
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
    },


  }
}
</script>

<style lang ="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.imgurl {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  .el-button {
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .el-button + .el-button {
    margin-left: 0;
  }
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
}
.tip {
  color: #8a929f;
  font-family: PingFang SC;
  font-weight: regular;
  font-size: 12px;
}
.add {
  max-height: 70vh;
  overflow-y: auto;
  .title {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 16px;
    margin: 20px 0;
    font-weight: 600;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;
    &.active {
      border: 5px solid #409eff;
    }
    &:nth-child(3n) {
      margin-right: 0;
    }
    .file_img {
      width: 115px;
      height: 115px;
      text-align: center;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.el-form-item ::v-deep .el-checkbox {
  margin-right: 0;
  &.active {
    border: 1px solid #a6e22e;
  }
}
.upload-demo {
  margin-right: 5px;
}
</style>