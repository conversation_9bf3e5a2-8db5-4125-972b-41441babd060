<template>
    <div>
        <div style="margin-bottom: 10px;">
            <el-button type="primary" @click="clickAdd">添加模板</el-button>
        </div>
        <div class="tableStyle">
            <myTable
                :table-list="templateList"
                :header="table_header"
                :header-cell-style="{ background: '#EBF0F7' }"
                highlight-current-row
                :row-style="$TableRowStyle"
            >
            </myTable>
        </div>
        <el-dialog
            :title="titles"
            :visible.sync="showDialog"
            width="500px"
            append-to-body
        >
            <div>
                <el-form ref="form" :model="form_params" label-width="80px">
                    <el-form-item label="名称">
                        <el-input v-model="form_params.value_1" maxlength="10" placeholder="请输入名称"></el-input>
                    </el-form-item>
                    <el-form-item label="电话">
                        <el-input v-model="form_params.value_2" maxlength="11" placeholder="请输入电话"></el-input>
                    </el-form-item>
                    <el-form-item label="模板类型">
                        <el-radio v-model="form_params.type" :label="1">通用模板</el-radio>
                        <el-radio v-model="form_params.type" :label="2">未接提醒</el-radio>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showDialog = false">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :loading="is_loading">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import myTable from "@/components/components/my_table";
export default {
    components: {
        myTable,
    },
    data() {
        return {
            templateList: [], // 短信模板列表
            // 表头
            table_header: [
                {
                    prop: "value_1",
                    label: "名称",
                },
                {
                    prop: "value_2",
                    label: "电话",
                },
                {
                    label: "模板类型",
                    render: (h, data) => {
                        return (
                            <div>
                                {data.row.type == 1 ? '通用模板' : '未接提醒'}
                            </div>
                        )
                    }
                },
                {
                    prop: "created_at",
                    label: "创建时间",
                },
                {
                    label: "操作",
                    render: (h, data) => {
                        return (
                            <div>
                                <el-link 
                                    style="padding: 0 5px;" 
                                    onClick={() => {this.selected(data.row)}} 
                                    type="success"
                                >
                                    选择
                                </el-link>
                                <el-link 
                                    style="padding: 0 5px;" 
                                    onClick={() => {this.onEdit(data.row)}} 
                                    type="warning"
                                >
                                    编辑
                                </el-link>
                            </div>
                        )
                    }
                },
            ],
            titles: "添加模板",
            form_params: {
                id: "", // 不传ID为添加，传ID为编辑
                type: 1, // 模板类型(1:通用模板, 2:未接提醒)
                value_1: "", // 自定义值1
                value_2: "" // 自定义值2
            },
            showDialog: false,
            is_loading: false, // loading加载
        }
    },
    created() {
        this.getSmsTemplate();
    },
    methods: {
        // 获取短信模板列表
        getSmsTemplate() {
            this.$http.getSmsTemplate().then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"模板");
                    this.templateList = res.data
                }
            })
        },
        // 点击添加模板
        clickAdd() {
            this.showDialog = true; // 显示模态框
            this.titles = "添加模板";
            this.recover(); // 重置接口参数
        },
        // 提交数据
        onSubmit() {
            if(this.form_params.value_1 == "" || this.form_params.value_1 == undefined) {
                return this.$message.warning("请输入姓名");
            }
            if(this.form_params.value_2 == "" || this.form_params.value_2 == undefined) {
                return this.$message.warning("请输入电话");
            }
            this.is_loading = true; // 开启loading
            // 不传ID为添加，传ID为编辑
            if(this.form_params.id == "") {
                // 添加
                delete this.form_params.id;
                this.$http.saveSmsTemplate(this.form_params).then((res) => {
                    if(res.status == 200) {
                        this.is_loading = false; //关闭loading
                        this.showDialog = false; // 显示模态框
                        this.$message.success("添加成功");
                        this.recover(); // 重置接口传参数据
                        this.getSmsTemplate(); // 刷新table数据
                    } else {
                        this.is_loading = false;
                    }
                }).catch(() => {
                    this.is_loading = false;
                })
            } else {
                // 编辑
                this.$http.saveSmsTemplate(this.form_params).then((res) => {
                    if(res.status == 200) {
                        this.is_loading = false; //关闭loading
                        this.showDialog = false; // 显示模态框
                        this.$message.success("编辑成功");
                        this.recover(); // 重置接口传参数据
                        this.getSmsTemplate(); // 刷新table数据
                    } else {
                        this.is_loading = false;
                    }
                }).catch(() => {
                    this.is_loading = false
                })
            }
        },
        // 重置接口参数
        recover() {
            this.form_params = {
                id: "",
                type: 1,
                value_1: "",
                value_2: ""
            }
        },
        // 点击选择
        selected(row) {
            // console.log(row,"row");
            this.$emit("onSelected", row);
        },
        // 点击编辑
        onEdit(row) {
            // console.log(row,"row")
            this.showDialog = true; // 显示模态框
            this.titles = "编辑模板";
            this.form_params = JSON.parse(JSON.stringify(row)); // 赋值当前模板
            // 删除多余参数
            if(this.form_params.created_at) {
                delete this.form_params.created_at;
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.tableStyle {
    max-height: 500px;
    overflow-y: auto;
}
</style>