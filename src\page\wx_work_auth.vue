<template>
  <div></div>
</template>

<script>
export default {
  mounted() {
    var code = this.getUrlKey("code");
    var token = this.getUrlKey("token");
    if (this.isMobile()) {
      let url =
        window.location.origin +
        "/fenxiao/admin/wx_work_auth?website_id=1&code=" +
        code;
      window.location.href = url;
    } else {
      if (code) {
        this.getWxworkByCode(code);
      }
      if (token) {
        this.setTfyToken(token);
      }
    }
  },
  // https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww9d222841bce98584&redirect_uri=https%3A%2F%2Fyun.tfcs.cn%2Fadmin%2F%23%2Fwx_work_auth&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect
  methods: {
    setTfyToken(t) {
      this.$http.getTfyTokenData({ params: { token: t } }).then((res) => {
        if (res.status === 200) {
          //登陆成功后调用第2步store里面的login方法，并将username传递过去，并跳转到home主页面
          localStorage.setItem("TOKEN", res.data.token);
          this.$message({
            message: "登录成功！",
            type: "success",
          });
          this.$goPath("/web_overview");
        }
      });
    },
    // 获取路径中的参数（code等）
    getUrlKey(name) {
      return (
        decodeURIComponent(
          (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
            location.href
          ) || ["", ""])[1].replace(/\+/g, "%20")
        ) || null
      );
    },
    getWxworkByCode(code) {
      // 第三方应用
      this.$http.getWxworkByCode(code).then(async (res) => {
        // let result = await this.$http.getInfoConfig()
        // if (result.status == 200) {
        //   console.log();
        // } else {
        //   this.$store.state.disableClick = result.data.message
        // }
        if (res.status === 200) {
          localStorage.setItem("TOKEN", res.data.token);
          localStorage.setItem("website_id", res.data.website_id);
          localStorage.setItem("auth_way", res.data.auth_way || 0);
          let url = `https://yun.tfcs.cn/admin?website_id=${res.data.website_id}/#/index`;
          window.location.href = url;
        }
      });
    },
    //App.vue
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
  },
};
</script>

<style></style>
