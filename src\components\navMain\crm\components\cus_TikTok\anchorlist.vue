<template>
    <div>
        <div>
            <el-table
            v-loading="is_table_loading"
              :data="tableData"
              :header-cell-style="{ background: '#EBF0F7' }"
              border
              style="width: 100%">
              <el-table-column
                prop="refer_dy_name"
                label="名称">
              </el-table-column>
              <el-table-column
                prop="user_name"
                label="成员"
                v-slot="{ row }">
                {{row.user_name?row.user_name:"--"}}
              </el-table-column>
                <el-table-column
                  prop="refer_dy_id"
                  label="账号">
                </el-table-column>
         
              <el-table-column
                label="操作"
                v-slot="{ row }">
                <el-link v-if="row.user_name" type="warning" @click="deleteanchor(row)">取消关联</el-link>
                <el-link v-else type="primary" @click="Relatedpersonnel(row)">关联人事</el-link>
              </el-table-column>
            </el-table>
            <div class="page_footer flex-row items-center">
            <div class="page_footer_l flex-row flex-1 items-center">
              <div class="head-list">
                <el-radio-group v-model="params.is_related" size="small" @change="submitbatchopen">
                  <el-radio-button label="1">已关联</el-radio-button>
                  <el-radio-button label="0">未关联</el-radio-button>
                </el-radio-group>
              </div>
              <div class="head-list">
                <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
              </div>
            </div>
            <div style="margin-right:10px;">
                <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="params.total"
                :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                @current-change="handleCurrentChange" @size-change="handleSizeChange">
              </el-pagination>
            </div>
          </div>
        </div>
        <el-dialog
         title="关联人事"
         :visible.sync="editdialogVisible"
         width="30%"
         :before-close="handleClose"
         append-to-body>
         <el-form ref="form" :model="form_edit" label-position="right" label-width="130px">
            <el-form-item label="人事成员">
                <el-select 
                    style="width: 300px;"
                     v-model="form_edit.admin_id" 
                     placeholder="请选择成员"
                     clearable
                     filterable>
                     <el-option
                         v-for="list in user_list"
                         :key="list.values"
                         :label="list.name"
                         :value="list.values">
                     </el-option>
                </el-select>
            </el-form-item>
         </el-form>
         
         <span slot="footer" class="dialog-footer">
           <el-button @click="editdialogVisible = false">取 消</el-button>
           <el-button type="primary" @click="subeditanchor">确 定</el-button>
         </span>
        </el-dialog>
    </div>
    </template>
    
    <script>
    export default {
        components: {
        },
        props:{
            user_list:Array,
            default:()=>[]
        },
        data(){
            return {
                show: false,        //dialog是否显示
                params:{
                  is_related:"0",
                },//获取主播列表字段
                tableData:[],//表格数据
                form_created:{
                    admin_id:"",//人事成员id
                    dy_account:"",//抖音主播账号id
                    wx_video_account:"",//视频号主播账号id
                },//添加主播
                editdialogVisible:false,
                form_edit:{
                    id:"",//主播账号id
                    admin_id:"",//关联人事成员id
                },//编辑主播
                is_table_loading:false,
                
            }
        },
        mounted(){
          let pagenum = localStorage.getItem( 'pagenum')
          this.params.per_page = Number(pagenum)||10
            this.getanchorlistshow()
        },
        methods: {
            //获取主播账号
            getanchorlistshow(){
                this.is_table_loading = true
                this.$http.getobtainanchoraccountpage(this.params).then((res)=>{
                    if(res.status==200){
                        this.is_table_loading = false
                        this.tableData = res.data.data
                        this.show = true;
                        this.params.total = res.data.total
                    }
                })
            },
            //关闭添加主播的弹框
            handleClose(){
                this.editdialogVisible = false
            },
           //刷新
           Refresh(){
            this.getanchorlistshow()
           },
           //检索已关联，未关联
           submitbatchopen(){
            this.getanchorlistshow()
           },
            //主播关联人事
            Relatedpersonnel(row){
                this.form_edit.id = row.id
                this.editdialogVisible = true
            },
            //确定关联人事
            subeditanchor(){
                if(this.form_edit.admin_id==""){
                    return this.$message.warning("关联人事不能为空！")
                }
                this.$http.editanchor(this.form_edit).then((res)=>{
                    if(res.status==200){
                        this.$message.success("添加成功")
                        this.editdialogVisible = false
                        this.getanchorlistshow()
                    }
                })
            },
            //删除主播
            deleteanchor(row){
                let params = {
                        id:"",
                    }
                this.$confirm('此操作将取消关联人事, 是否继续?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                    params.id = row.id
                    this.$http.deletanchor(params).then((res)=>{
                        if(res.status==200){
                            this.getanchorlistshow()
                            this.$message({
                              type: 'success',
                              message: '删除成功!'
                            });
                        }
                    })
                }).catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消删除'
                  });          
                });
            },
            //分页
            handleSizeChange(val){
                this.params.per_page = val
                this.getanchorlistshow()

            },
            handleCurrentChange(val){
                this.params.page = val
                this.getanchorlistshow()
            },
        }  
    }
    </script>
    <style lang="scss" scoped>
    .heard{
        margin: 0px 0px 10px 0px;
        display: flex;
        justify-content: flex-end;
    }
    .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
    </style>