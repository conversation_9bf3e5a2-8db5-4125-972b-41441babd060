<template>
    <div>
        <!-- 通话套餐 -->
        <div class="phoneMenu">
            <template>
                <el-button type="success" size="medium" @click="SeatsBuy">坐席购买</el-button>
                <el-button type="primary" size="medium" @click="buyRecord">购买记录</el-button>
            </template>
        </div>
        <!-- 坐席购买对话框 -->
        <el-dialog
            title="套餐列表"
            :visible.sync="dialogVisible"
            width="800px"
        >

            <el-table
             v-loading="tel_table_loading"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            :row-style="$TableRowStyle"
            :data="tableData"
            style="width: 100%">
                <el-table-column
                prop="id"
                label="套餐id"
                width="80"
                >
                </el-table-column>
                <el-table-column
                prop="name"
                label="套餐名称"
                width="120"
                >
                </el-table-column>
                <el-table-column
                prop="price"
                label="套餐价格"
                >
                </el-table-column>
                <el-table-column
                prop="seat_total"
                label="坐席数量"
                >
                </el-table-column>
                <el-table-column
                prop="description"
                label="套餐描述"
                width="140"
                >
                </el-table-column>
                <el-table-column
                prop="duration"
                label="有效期"
                width="80"
                >
                </el-table-column>
                <el-table-column
                prop="status"
                label="套餐状态"
                >
                <template slot-scope="scope">
                    <el-tag type="success">{{ scope.row == 0 ? '已下架' : '正常' }}</el-tag>
                </template>
                </el-table-column>
                <el-table-column label="操作" v-slot="{ row }">
                    <el-link type="success" @click="buyMenu(row)">购买套餐</el-link>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-pagination
                style="text-align: end; margin-top: 24px"
                background
                layout="prev, pager, next"
                :total="callMenu.total"
                :page-size="callMenu.per_page"
                :current-page="callMenu.page"
                @current-change="onPackagePageChange"
                >
            </el-pagination>
        </el-dialog>
        <!-- 购买记录 -->
        <el-dialog
            title="购买记录"
            :visible.sync="buyVisible"
            width="70%"
        >

            <el-table
             v-loading="tel_table_loading"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            :row-style="$TableRowStyle"
            :data="recharge_list"
            highlight-current-row
            style="width: 100%">
                <!-- 列表下拉 -->
                <el-table-column type="expand">
                    <template slot-scope="props">
                    <el-form label-position="left" inline class="demo-table-expand">
                        <el-form-item label="创建时间：">
                        <span>{{ props.row.created_at }}</span>
                        </el-form-item>
                        <el-form-item label="支付时间：" v-if="props.row.status == 1">
                        <span>{{ props.row.payment_at }}</span>
                        </el-form-item>
                        <el-form-item label="支付方式：" v-if="props.row.status == 1">
                        <span>{{
                            props.row.payment_category_id == 0
                            ? "暂未支付"
                            : props.row.payment_category_id == 1
                            ? "微信小程序支付"
                            : props.row.payment_category_id == 2
                            ? "微信扫码支付"
                            : props.row.payment_category_id == 2
                            ? "微信APP支付"
                            : "微信H5支付"
                        }}</span>
                        </el-form-item>

                        <el-form-item label="订单支付状态：">
                        <span>{{
                            props.row.payment_status === 0 ? "未付款" : "已付款"
                        }}</span>
                        </el-form-item>
                        <el-form-item label="成交单号" v-if="props.row.status == 1">
                        <span>{{ props.row.payment_trade_sn }}</span>
                        </el-form-item>
                        <el-form-item label="备注信息：">
                        <span>{{ props.row.remark }}</span>
                        </el-form-item>
                    </el-form>
                    </template>
                </el-table-column>
                <el-table-column
                prop="id"
                label="订单ID"
                width="100"
                >
                </el-table-column>
                <el-table-column
                prop="order_sn"
                label="订单号"
                align="center"
                width="200"
                >
                </el-table-column>
                <el-table-column
                prop="payment_amount"
                label="订单金额"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="seat_total"
                label="坐席数量"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="duration"
                label="有效期/月"
                align="center"
                >
                </el-table-column>
                <el-table-column
                prop="status"
                label="支付状态"
                align="center"
                v-slot="{row}"
                >
                <el-tag type="success" v-if="row.payment_status === 1">已付款</el-tag>
                <el-tag type="warning" v-if="row.payment_status === 0 && row.status !== 2">未付款</el-tag>
                <el-tag class="ml5" type="danger" v-if="row.status === 2">已取消</el-tag>
                </el-table-column>
                <el-table-column label="操作" align="center" v-slot="{row}" width="210">
                    <el-popconfirm
                    title="确定取消订单吗？"
                    v-if="row.payment_status === 0 && row.status == 0"
                    @onConfirm="cancelOrder(row)"
                    >
                    <el-button slot="reference" type="primary" size="small"
                        >取消订单</el-button
                    >
                    </el-popconfirm>
                    <el-button 
                    v-if="row.payment_status === 0 && row.status !== 2"
                    type="success" size="small" @click="erweiCode(row.id)"
                    style="margin: 0 10px;"
                    >微信支付</el-button>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-pagination
                style="text-align: end; margin-top: 24px"
                background
                layout="prev, pager, next"
                :total="buyRecode.total"
                :page-size="buyRecode.per_page"
                :current-page="buyRecode.page"
                @current-change="buyRecodePageChange"
                >
            </el-pagination>
        </el-dialog>
        <!-- 二维码 -->
        <div>
            <el-dialog width="400px" :visible.sync="dialogPayCode" title="支付">
                <div class="code-box">
                    <img :src="codeImg" alt="" />
                    <p>请打开微信扫码支付</p>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return{
            dialogPayCode: false, // 控制支付二维码显示
            dialogVisible: false, // 控制坐席购买对话框显示
            tel_table_loading:false,
            tableData: [], // 表格绑定值
            codeImg: "", // 二维码图片
            recharge_list: [], // 充值记录列表
            buyVisible: false, // 控制购买记录对话框显示
            callMenu: {
                page: 1,
                per_page: 10,
                total: 0,
                row : 0,
            },
            buyRecode: {
                page: 1,
                per_page: 10,
                total: 0,
                row : 0,
            }
        }
    },
    created() {

    },
    methods: {
        // 点击坐席购买
        SeatsBuy() {
            this.dialogVisible = true
            this.$http.getSeatsMenuList(this.callMenu).then(res => {
                if(res.status == 200) {
                    this.tableData = res.data.data
                    this.callMenu.total = res.data.total
                }
            })
        },
        // 坐席套餐购买记录列表
        buyRecord() {
            this.$http.getSeatsRecordList(this.buyRecode).then(res => {
                if(res.status == 200) {
                    this.buyVisible = true
                    this.recharge_list = res.data.data
                    this.buyRecode.total = res.data.total
                }
            })
        },
        // 分页器当前页发生改变
        onPackagePageChange(val) {
            this.callMenu.page = val
            this.CallRecharge()
        },
        // 点击购买套餐
        buyMenu(row) {
            this.$http.getBuySeatsMenuOrder({package_id:row.id}).then(res => {
                if(res.status == 200) {
                    this.erweiCode(res.data.id)
                }
            })
        },
        // 支付二维码
        erweiCode(id) {
            this.$http.getQRcode(id).then(res => {
                if(res.status == 200) {
                    this.dialogPayCode = true
                    this.codeImg =
                    "data:image/png;base64," +
                    btoa(
                    new Uint8Array(res.data).reduce(
                        (data, byte) => data + String.fromCharCode(byte),
                        ""
                    )
                    );
                }
            })
        },
        // 购买记录当前页发生改变
        buyRecodePageChange(val) {
            this.buyRecode.page = val
            this.buyRecord()
        },
        // 购买记录取消订单
        cancelOrder(row) {
            this.$http.cancelSeatsRecord(row.id).then(res => {
                if (res.status == 200) {
                this.$message.success(res.message || "订单取消成功")
                this.buyRecord()
                }
            })
        }
    }
}
</script>
<style scoped lang="scss">
.phoneMenu {
    .el-button:first-child {
        background-color: #17b63a;
    }
    .el-button:first-child,.el-button:nth-child(2) {
        width: 160px;
        height: 40px;
        box-sizing: border-box;
        padding: 9px 0px !important;
        font-size: 16px;
    }
}
.code-box {
  text-align: center;
  p {
    text-align: center;
    color: #6bcc03;
    font-size: 28px;
  }
}
</style>