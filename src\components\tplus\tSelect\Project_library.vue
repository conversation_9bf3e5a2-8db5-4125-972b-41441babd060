<template>
<div>
    <el-drawer
        title="项目库"
        :visible.sync="show"
        :direction="direction"
        :before-close="cancle" ref="portrait">
        <div class="retrieval" v-if="authority">
          <tCrmProjectSelect  allow-create default-first-option v-model="form_appoint.project_id"
              width="100%" />
        </div>
        
        <div class="page">
            <div class="flex-row">
              <div class="thumbnail" v-if="xiangmudata&&xiangmudata.cover_image"> 
              <img :src="xiangmudata.cover_image" alt="">
              </div>
              <div class="namedata" v-if="xiangmudata&&xiangmudata.name">
                <div class="quartersname">{{ xiangmudata.name }}</div>
                <div v-if="xiangmudata.province&&xiangmudata.city&&xiangmudata.area">
                  <div class="quartersregion">{{ xiangmudata.province.name}}-{{ xiangmudata.city.name }}-{{ xiangmudata.area.name }}</div>
                </div>
                <div v-else>--</div>
              </div>
            </div>
            <div class="addproject">
              <addprojectupdates ref="addprojectupdates" @parentMethod="Takelookprojectlibrary"
                  :params="params" :form_appoint="form_appoint"></addprojectupdates>
            </div>
                <div>
                  <div class="dongtai">动态</div>
                  <div class="follow-record" v-infinite-scroll="loadMoreFollow"
                    :infinite-scroll-disabled="follow_load" style="overflow: auto" :style="{ height: followList_height + 'px' }"
                      @click="hideColleague">
                        <el-timeline v-if="follow_list.length" style="margin-left: 10px">
                          <el-timeline-item v-for="(activity, index) in follow_list" :key="index" placement="top" color="#2D84FB">
                            <div class="agent_info flex-row align-center">
                              <div class="time">
                                {{ activity.created_at }}
                              </div>
                              <div class="agent_name"
                                v-if="activity.user_name&&activity.department">
                                {{ activity.user_name }}/{{activity.department[1]?activity.department[1]:activity.department[0]}}
                              </div>
                              <div class="agent_name" v-else>
                                {{activity.user_name?activity.user_name:""}}
                              </div>
                              <div class="show_is_top" v-if="activity.is_top == 1">
                                已置顶
                              </div>
                              <div class="follow_info_box">
                                <!-- 点赞 -->
                                <!-- <div class="follow_info_praise" @click="setFollowPraise(activity)">
                                  <i class="el-icon-newfontic_zan"></i>
                                </div> -->
                                <!-- 复制 -->
                                <!-- <div class="follow_info_copy" @click="onCopyValues(activity)">
                                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                                </div> -->
                                <!-- 置顶 -->
                                <div class="follow_add_top" v-if="authority">
                                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                                  <span v-if="activity.is_top == 0" @click="setFollowTop(activity)">设为置顶</span>
                                  <span v-if="activity.is_top == 1" @click="cancellationTop(activity)">取消置顶</span>
                                </div>
                                <!-- 批注 -->
                                <!-- <div class="follow_info_reply" @click="setFollowreply(activity)">
                                  <div class="follow_info_huifu">
                                    <img src="https://img.tfcs.cn/xinfang/huifu.png" alt="" />
                                  </div>
                                </div>  -->
                                <!-- 删除 -->
                                <div class="follow_info_reply" v-if="authority">
                                  <i class="el-icon-delete" @click="deleteitem(activity)"></i>
                                </div>
                              </div>
                            </div>
                              <div>
                                  <div class="f_content f_line">
                                      <div style="width: 100%;word-wrap: break-word;"  v-html="activity.content"></div>
                                  </div>
                                <!-- <div v-if="activity.reply.length" class="Replycomments">
                                  <div v-for="(itereply,index) in activity.replydata" :key="index">
                                      <div> 
                                        <div>{{ itereply.admin.user_name }}  {{ itereply.created_at }}</div>
                                        <div style="margin-top:10px;">{{itereply.content}}</div>
                                      </div>
                                      <el-divider></el-divider>
                                    </div>
                                    <el-link type="primary" v-if="activity.replydata.length == 2||activity.replydata.length < 2" :underline="false" @click="See_All(activity)">
                                     <div v-if="activity.replycopy>2">
                                      查看全部{{activity.replycopy}}条回复 <i class="el-icon-arrow-down"></i>
                                     </div> 
                                     <div v-else>
                                      <el-link style="font-size:15px" type="primary" :underline="false" @click="setFollowreply(activity)">+批注
                                      </el-link>
                                     </div>
                                    </el-link>
                                    <el-link v-else type="primary" :underline="false" @click="retractAll(activity)">收起
                                      <i class="el-icon-arrow-up"></i></el-link>
                                </div> -->
                              </div>
                            <!-- 跟进语音 -->
                            <!-- <voiceAudioPlayer v-if="activity.url && activity.voice_duration" :activity="activity">
                            </voiceAudioPlayer> -->
                            <!-- 跟进图片 -->
                            <div v-if="activity.image && activity.image.length
                             " class="follow-picture">
                              <div class="follow-picture-box" v-for="(item, index) in activity.image" :key="index">
                                <img :src="$imageFilter(item, 'w_240')" alt="" />
                                <span class="uploader-actions" v-if="item">
                                  <span class="uploader-actions-item" @click="handlePictureCardPreview(item)">
                                    <i class="el-icon-view"></i>
                                  </span>
                                </span>
                              </div>
                            </div>
                            <!-- <AudioPlayer v-if="activity.record_url" :activity="activity" :info_id="c_detail.id"
                              :type="getClient_params.type" select="CustomerFollow"></AudioPlayer> -->
                            <!-- <div class="follow-praise" v-if="activity.top_list.length > 0">
                              <div class="follow-praise-box">
                                <span class="follow-praise-img">
                                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                                </span>
                                <span class="follow-praise-separate"></span>
                                <span class="follow-praise-text">
                                  <span>{{ activity.top_list.join("，") }}</span>
                                </span>
                              </div>
                            </div> -->
                          </el-timeline-item>
                        </el-timeline>
                        <myEmpty v-else :loading="loadings.followList"></myEmpty>
                  </div>
                </div>
            
        </div>
            <!-- <span slot="footer" class="dialog-footer">
                <el-button @click="cancle">取 消</el-button>
                <el-button type="primary" @click="confirm">确 定</el-button>
            </span> -->
    </el-drawer>
    <!-- 查看已上传图片模态框 -->
    <div class="mask1" v-if="show_dialog_pictures" @click="show_dialog_pictures = false">
      <div class="preview_img" @click.prevent.stop="() => { }">
        <img id="preImg" :src="dialog_pictures_src" alt="" />
      </div>
    </div>
</div>
</template>

<script>
import config from "@/utils/config.js";
import addprojectupdates from "@/components/tplus/tSelect/Addprojectupdates.vue";
import myEmpty from "@/components/components/my_empty.vue";
import tCrmProjectSelect from "@/components/tplus/tSelect/tCrmProjectSelect.vue";
export default {
    name: 'Projectlibrary',
    components: {
        addprojectupdates,
        myEmpty,
        tCrmProjectSelect
    },
    data(){
        return {
            show: false,        //dialog是否显示
            direction: 'rtl',
            cancleFn: null,     //取消回调
            successFn: null,    //成功回调
            params: {},         //表单参数     
            followList_height: "470",
            follow_load: true,  
            follow_list: [],
            loadings: {
              followList: false,
            },
            show_dialog_pictures: false, // 查看已上传的图片
            xiangmudata:{},//项目信息
            form_appoint: {
              project_id: "",  // 项目id
            },
            authority:false,//判断检索项目查看动态与置顶和删除的权限
        }
    },
    computed: {
    // 获取请求头
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    },
    watch:{
          'form_appoint.project_id': {
            handler(newVal, oldVal) {
              if(newVal){
                // this.params.id = newVal
                this.Takelookprojectlibrary(newVal)
                this.getProjectDetails(newVal)
              }
            }
        },
    },
    methods: {
        //打开弹层
        open(params,c_detail,selfID){
          console.log(params,"1212121212");
          if(c_detail){
            c_detail.map(item=>{
            if(item==selfID){
              console.log(121111111);
              this.authority = true
            }
          })
          }else{
            this.authority = true
          }
            this.params = params;
            this.show = true;
            this.Takelookprojectlibrary()
            this.getProjectDetails()
            return this;
        },
        //获取项目动态的具体信息
        Takelookprojectlibrary(id){
            let data = {}
            if(id){
              data.project_id = id
            }else{
              data.project_id = this.params.id
            }
            this.$http.takelookprojectlibrary(data).then((res)=>{
                if(res.status==200){
                    console.log(res.data);
                    this.follow_list = res.data.data
                    console.log(this.follow_list);
                    this.$nextTick(() => {
                      this.followList_height = +this.$refs.portrait.$el.offsetHeight - 420
                    })
                }
            })
        },
        //获取项目信息
        getProjectDetails(id){
          let data = this.params.id
          if(id){
            data = id
          }
            this.$http.getProjectDetails(data).then((res)=>{
                if(res.status==200){
                    console.log(res.data);
                    this.xiangmudata = res.data.detail
                    console.log(this.xiangmudata);
                }
            })
        },
        loadMoreFollow() {
          if (this.follow_load) {
            return;
          }
          this.f_params.page++;
          this.getFollowData();
        },
        // 隐藏跟进记录同事信息框
        hideColleague() {
          let popover = document.querySelector(".infoFrame");
          popover.style.display = "none";
        },
        // 查看已上传的图片
        handlePictureCardPreview(item) {
          // console.log(item,'item');
          this.show_dialog_pictures = true;
          if (item.url) {
            this.dialog_pictures_src = item.url;
          } else {
            this.dialog_pictures_src = item;
          }
        },
        // 项目动态点击设为置顶
        setFollowTop(val) {
          this.$http.projectupdatesTop(val.id).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              // this.f_params.page = 1;
              if(this.form_appoint.project_id){
                this.Takelookprojectlibrary(this.form_appoint.project_id); // 刷新
              }else{
                this.Takelookprojectlibrary(); // 刷新
              } 
            }
          })
        },
        // 项目动态点击取消置顶
        cancellationTop(val){
          this.$http.projectcancellationTop(val.id).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              // this.f_params.page = 1;
              if(this.form_appoint.project_id){
                this.Takelookprojectlibrary(this.form_appoint.project_id); // 刷新
              }else{
                this.Takelookprojectlibrary(); // 刷新
              } 
            }
          })
        },
        //删除项目
        deleteitem(val){
           this.$confirm('此操作将永久删除该动态, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.delprojectcancellation(val.id).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              // this.f_params.page = 1;
              if(this.form_appoint.project_id){
                this.Takelookprojectlibrary(this.form_appoint.project_id); // 刷新
              }else{
                this.Takelookprojectlibrary(); // 刷新
              } 
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
         
        },
        //取消
        cancle(){
          console.log(121212121222);
            this.show = false;
            this.form_appoint.project_id = ""
            this.params = {}
            this.cancleFn && this.cancleFn();
        },
        //确定
        confirm(){
            //TODO: 验证+提交
            

            //在提交成功之后回调
            this.show = false;
            this.successFn && this.successFn();
        }
    }
}
</script>
<style lang="scss" scoped>
 ::v-deep .el-drawer {
    width: 35% !important;
 }
 .retrieval{
  display: flex;
  position: absolute;
  top: 22px;
  right: 84px
 }
.page{
   width: 90%;
   margin: 0 auto;
   height: calc(100vh - 90px);
   overflow-y: auto;
   overflow-x: hidden;
    .additem{
       display: flex;
       justify-content: flex-end;
    }
    .thumbnail{
      width: 90px;
      height: 90px;
      border-radius: 8px;
      overflow: hidden;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .namedata{
      margin-left: 10px;
      line-height: 31px;
      color: #72767b;
      .quartersname{
        font-weight: bold;
      }
      .quartersregion{
        font-size: 14px;
      }
    }
    .addproject{
      margin-top: 30px;
    }
    .dongtai{
      color:#606266;
      margin-bottom: 20px;
    }
}
.follow-record {

// ==========
.el-timeline {
  font-size: 15px;

  .el-timeline-item:hover {
    .el-timeline-item__wrapper {
      .el-timeline-item__content {
        .agent_info {
          .follow_info_box {
            display: flex;
          }
        }
      }
    }
  }
}

// ==========
.agent_info {
  height: 34px;
  margin: -10px 0 24px 0;

  .time {
    margin-right: 10px;
    color: #8a929f;
  }

  .show_is_top {
    background-image: linear-gradient(180deg, #f9762e, #fc0606);
    color: #fff;
    padding: 2px 8px;
    border-radius: 4px;
    margin-left: 20px;
    white-space: nowrap;
  }

  .follow_info_box {
    display: none;
    flex-direction: row;
    // border: 1px solid #8a929f;
    border-radius: 3px;
    margin-left: 20px;

    .follow_info_praise,
    .follow_info_copy {
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      text-align: center;
      padding: 6px;
      color: #8f959e;
      // border-right: 1px solid #8f959e;
      cursor: pointer;
    }

    .follow_info_praise:active {
      background-color: #eff0f1;
    }

    .follow_info_copy:active {
      background-color: #eff0f1;
    }

    .follow_add_top {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #8f959e;
      padding: 6px;
      cursor: pointer;
      white-space: nowrap;
    }
    .follow_info_reply{
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      text-align: center;
      padding: 6px;
      color: #8f959e;
      // border-left:  1px solid #8f959e;
      cursor: pointer;
    }
    .follow_info_reply:active {
      background-color: #eff0f1;
    }
    .follow_info_huifu{
      img{
        width: 25px;
        height: 25px;
      }
    }

    .follow_add_top:active {
      background-color: #eff0f1;
    }
  }

  .img {
    margin-right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .agent_name {
    font-size: 12px;
    color: #8a929f;
  }
}

.f_content {
  display: flex;
  align-items: center;
  max-width: 450px;
  // margin-bottom: 20px;

  &.red {
    color: #fc0606;
  }

  // & span {
  //   display: flex;
  //   align-items: center;
  // }
  .f-item-cont {
    word-break: break-all;
  }
}

.f_line {
  line-height: 24px;
  white-space: pre-wrap;
}
.Replycomments{
  width: 94%;
  // height: 200px;
  margin-top: 20px;
  background-color: #F7F8FA;
  color: #4E5969;
  border-radius: 8px;
  padding: 10px;
  /deep/.el-divider--horizontal{
    margin: 15px 0 !important;
  }
}
.follow-picture {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  margin-top: 13px;

  .follow-picture-box {
    width: 60px;
    height: 60px;
    margin-right: 10px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .uploader-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 4px;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      transition: opacity 0.3s;

      .uploader-actions-item {
        font-size: 20px;
        cursor: pointer;

        & i {
          color: #fff;
        }
      }
    }

    .uploader-actions:hover {
      opacity: 1;
    }
  }
}

.follow-praise {
  display: inline-block;
  border-radius: 15px;
  color: #8a929f;
  background-color: #f1f4fa;
  padding: 5px 12px;
  box-sizing: border-box;
  margin-right: 20px;
  margin-top: 13px;

  .follow-praise-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .follow-praise-img {
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 19px;
        height: 19px;
      }
    }

    .follow-praise-separate {
      width: 1px;
      height: 14px;
      background-color: #8a929f;
      margin: 0 5px;
      opacity: 0.5;
    }

    .follow-praise-text {
      padding: 0 5px;
    }
  }
}
}
.mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 0px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
      object-fit: cover;
    }

    .img {
      max-width: 800px;
      height: 600px;
      overflow-y: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
</style>