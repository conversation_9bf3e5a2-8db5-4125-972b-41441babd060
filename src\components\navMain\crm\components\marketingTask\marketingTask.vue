<template>
  <div>
    <div class="content-box-crm">
      <div class="check-box div row" id="pages_content">
        <div
          v-for="item in tabs_list"
          :class="{ isactive: item.id === is_tabs_page }"
          @click="onClickTabs(item)"
          :key="item.id"
          class="check-item"
        >
          {{ item.name }}
        </div>
      </div>
      <!-- 任务 -->
      <template v-if="is_tabs_page == 3">
        <div class="table-top-box div row">
          <div class="t-t-b-left div row"></div>
          <div class="t-t-b-right div row">
            <el-button type="primary" @click="exportCustom">
              导入至公海</el-button
            >
            <el-button type="success" @click="sendTask"> 发送任务 </el-button>
            <el-button type="primary" @click="add"> 创建任务</el-button>
          </div>
        </div>
        <div>
          <el-table
            v-loading="is_table_loading"
            :data="taskData"
            border
            class="table"
            ref="table"
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            @selection-change="selectionChange"
            :row-style="$TableRowStyle"
          >
            <!-- 选择框 -->
            <el-table-column
              type="selection"
              fixed="left"
              width="55"
              align="center"
            />
            <el-table-column prop="id" width="70" label="ID"></el-table-column>
            <el-table-column
              prop="task_name"
              label="任务名称"
            ></el-table-column>
            <el-table-column
              prop="number_library_name"
              label="号码库名称"
            ></el-table-column>
            <el-table-column
              prop="template_library_name"
              label="模板库名称"
            ></el-table-column>
            <!-- <el-table-column
              prop="msg"
              label="短信内容"
              width="200"
              v-slot="{ row }"
            >
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  {{ row.msg }}
                </div>
                <div class="msg_con">
                  {{ row.msg }}
                </div>
              </el-tooltip>
            </el-table-column> -->

            <el-table-column
              prop="send_count"
              label="发送次数"
              width="90"
            ></el-table-column>
            <!-- <el-table-column
              prop="no_sms_send"
              label="未发送条数"
            ></el-table-column>
            <el-table-column
              prop="has_been_count"
              label="已发送条数"
            ></el-table-column> -->
            <!-- <el-table-column
              prop="err_sms_send"
              label="发送失败条数"
            ></el-table-column> -->
            <!-- <el-table-column prop="msg" label="短信内容"></el-table-column> -->
            <el-table-column label="操作" v-slot="{ row }">
              <el-link type="success" @click="sendLog(row)">发送记录</el-link>
              <el-link style="margin-left: 5px" @click="edit(row)"
                >编辑</el-link
              >
              <el-link
                style="margin-left: 5px"
                type="success"
                @click="copyCon(row)"
                >复制</el-link
              >
            </el-table-column>
          </el-table>

          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </template>
      <!-- 模板 -->
      <template v-if="is_tabs_page == 2">
        <templateList></templateList>
      </template>
      <!-- 号码库 -->
      <template v-if="is_tabs_page == 1">
        <telList></telList>
      </template>
    </div>

    <el-dialog
      width="500px"
      append-to-body
      :visible.sync="is_showDia"
      :title="add_title"
    >
      <div v-if="is_showDia">
        <el-form label-width="120px">
          <el-form-item label="任务名称">
            <el-input
              style="width: 200px"
              placeholder="请输入任务名称"
              v-model="task_form.task_name"
            ></el-input>
          </el-form-item>
          <el-form-item label="模板库">
            <el-select
              v-model="task_form.template_library_id"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="searchTemplate"
              :loading="loading"
            >
              <el-option
                v-for="item in templateList"
                :key="item.id"
                :label="item.template_name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="号码库">
            <el-select
              v-model="task_form.number_library_id"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="searchTel"
              :loading="loading_tel"
            >
              <el-option
                v-for="item in telLists"
                :key="item.id"
                :label="item.library_name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button v-loading="add_loading1" type="primary" @click="confirmAdd"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      width="800px"
      append-to-body
      :visible.sync="show_log"
      title="发送记录"
    >
      <div v-if="show_log">
        <el-table
          v-loading="is_task_loading"
          :data="logList"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column
            prop="number_library_name"
            label="所属号码库"
          ></el-table-column>
          <el-table-column
            prop="template_library_name"
            label="所属模板库"
          ></el-table-column>

          <el-table-column prop="phone" label="手机号"></el-table-column>
          <el-table-column prop="status" label="状态" v-slot="{ row }">
            <el-tag v-if="row.status == 0" type="success"> 发送成功 </el-tag>
            <el-tag v-if="row.status == -1" type="warning"> 未发送 </el-tag>
            <el-tag v-if="row.status == 2" type="danger"> 发送失败 </el-tag>
          </el-table-column>
          <el-table-column
            prop="report_time"
            label="发送时间"
          ></el-table-column>

          <!-- <el-table-column prop="desc" label="描述"></el-table-column> -->
        </el-table>

        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="logTotal"
          :page-size="log_params.per_page"
          :current-page="log_params.page"
          @current-change="onLogPageChange"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config";
import templateList from "./templaList"
import telList from "./telList"
import { Loading } from "element-ui";
// import axios from "axios"
// import template_list from '../../../customer/copy_template_list.vue';
export default {
  components: {
    templateList,
    telList
  },
  data() {
    return {
      taskData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
      },
      log_params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      logTotal: 0,
      is_showDia: false,
      task_form: {
        task_name: '',
        number_library_id: '',
        template_library_id: ''
      },
      add_loading: false,
      add_loading1: false,
      show_log: false,
      logList: [],
      is_task_loading: false,
      tabs_list: [
        { id: 3, name: "任务列表", },
        { id: 1, name: "号码库", },
        { id: 2, name: "模板库", }
      ],
      is_tabs_page: 3,
      is_show_sign_Dia: false,
      sign_form: {
        value: ''
      },
      telLists: [],
      loading_tel: false,
      templateList: [],
      loading: false,
      tel_params: {
        page: 1,
        per_page: 50,
        library_name: ''
      },
      template_params: {
        page: 1,
        per_page: 50,
        template_name: ""
      },
      isAdd: false,
      add_title: "",
      multipleSelection: []

    }
  },
  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    fileaccept() {
      var val = ".txt,.xlsx";
      return val;
    },
  },
  created() {
    this.website_id = this.$route.query.website_id
    this.getList()

    this.user_avatar = `/api/admin/personnelMatters/sendSms`;
  },
  methods: {
    getList() {
      this.$http.getMaketingTaskList(this.params).then(res => {
        if (res.status == 200) {
          this.taskData = res.data.data
          this.total = res.data.total
        }
      })
    },
    // 签名
    signs() {
      this.getSign()
      this.is_show_sign_Dia = true
    },
    getSign() {
      this.$http.getSign().then(res => {
        if (res.status == 200) {
          this.sign_form = res.data
        }
      })
    },
    // 添加任务
    add() {
      this.isAdd = true
      this.add_title = '添加任务'
      // this.$refs.uploadfile && this.$refs.uploadfile.clearFiles();
      this.task_form = {
        task_name: '',
        template_library_id: '',
        number_library_id: ''
      }

      this.searchTel()
      this.searchTemplate()
      this.is_showDia = true
    },
    // 编辑任务
    edit(row) {
      this.isAdd = false
      this.add_title = '编辑任务'
      this.templateList = []
      this.telLists = []
      this.searchTel()
      this.searchTemplate()
      // this.$refs.uploadfile && this.$refs.uploadfile.clearFiles();
      this.task_form = {
        id: row.id,
        task_name: row.task_name,
        template_library_id: row.template_library_id,
        number_library_id: row.number_library_id
      }
      let temp = {
        id: row.template_library_id,
        template_name: row.template_library_name

      }
      this.templateList.push(temp)
      let tel = {
        id: row.number_library_id,
        library_name: row.number_library_name
      }
      this.telLists.push(tel)
      this.is_showDia = true
    },
    confirmSign() {
      this.add_loading = true
      this.$http.saveSign(this.sign_form)
        .then(res => {
          if (res.status == 200) {
            this.$message.success(res.$message || '保存成功')
            this.is_show_sign_Dia = false
          }
          this.add_loading = false

        })
        .catch(() => {
          this.add_loading = false
        })
    },
    change(e) {
      this.file = e.raw
    },
    addTask() {
      this.add_loading1 = true
      this.$http.addMarketTask(this.task_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.data?.message || res.message || '任务创建成功')
          this.is_showDia = false
          this.getList()
        }
        this.add_loading1 = false
      })
        .catch(() => {
          this.add_loading1 = false
        })
    },
    editTask() {
      this.add_loading1 = true
      this.$http.editMarketTask(this.task_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.data?.message || res.message || '任务编辑成功')
          this.is_showDia = false
          this.getList()
        }
        this.add_loading1 = false
      })
        .catch(() => {
          this.add_loading1 = false
        })
    },
    confirmAdd() {
      if (this.isAdd) {
        this.addTask()
      } else {
        this.editTask()
      }
    },
    onClickTabs(item) {
      this.is_tabs_page = item.id
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getList()
    },
    onLogPageChange(current_page) {
      this.log_params.page = current_page;
      this.getTaskLog()
    },
    // 发送记录
    sendLog(row) {
      this.show_log = true
      this.log_params.page = 1
      this.getTaskLog(row.id)
    },
    getTaskLog(id) {
      this.log_params.task_id = id
      this.$http.getMarketSendLog(this.log_params).then(res => {
        if (res.status == 200) {
          this.logList = res.data.data
          this.logTotal = res.data.total
        }
      })
    },
    exportCustom() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请选择要导入的手机号所在的任务")
        return
      }
      let ids = []
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      this.$confirm("确认导入手机号到公海吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.startExport(ids.join(","))
        })
        .catch(() => { })
    },
    startExport(task_id) {
      this.loading = Loading.service({
        lock: true,
        text: "正在导入中 请稍后...", //可以自定义文字
        spinner: "el-icon-loading", //自定义加载图标类名
      });
      this.$http.importPhoneToCrm({ task_id }).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success(res.data?.message || res.message || '手机号已导入')
          // this.getList()
          this.loading.close();
          this.$refs.table.clearSelection()

          // this.$set(this.multipleSelection, [])
        } else {
          this.loading.close();
        }

      }).catch(() => {
        this.loading.close();
      })
    },

    // 获取发送模板列表
    searchTemplate(query) {
      this.template_params.template_name = query
      console.log(query);
      this.loading = true
      this.$http.getMaketingTemplateList(this.template_params).then(res => {
        if (res.status == 200) {
          this.templateList = res.data.data
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 获取号码库列表
    searchTel(query) {
      this.tel_params.library_name = query
      this.loading_tel = true
      this.$http.getMaketingTelList(this.tel_params).then(res => {
        if (res.status == 200) {
          this.telLists = res.data.data
        }
        this.loading_tel = false
      }).catch(() => {
        this.loading_tel = false
      })
    },
    selectionChange(val) {
      this.multipleSelection = val;
    },
    sendTask() {
      if (!this.multipleSelection.length) {
        this.$message.warning("请选择要发送的任务")
        return
      }
      let ids = []
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      this.$confirm("确认开始执行所选任务吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.startSendTask(ids.join(","))
        })
        .catch(() => { })

    },
    startSendTask(task_id) {
      this.$http.sendMarketTask({ task_id }).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success(res.data?.message || res.message || '任务正在执行')
          this.getList()
        }
      })
    },
    copyCon(row) {
      this.$onCopyValue(row.send_msg);
    }


  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}

.padd10 {
  padding: 10px 0 40px;
}
.ml50 {
  margin-left: 50px;
}
.title {
  padding-top: 20px;
  padding-left: 75px;
}
.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  // margin-left: 20px;
  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;
    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}
.msg_con {
  width: 180px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>