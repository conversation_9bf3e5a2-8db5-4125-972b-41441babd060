<template>
  <div class="sucai_list">
    <div class="sucai_list_t flex-row">
      <div class="search div row">
        <!-- @blur="
            params.page = 1;
            getList();
          " -->
        <el-input
          v-model="params.title"
          placeholder="请输入素材名称"
          style="margin-right: 10px"
        >
          <!-- <template slot="append">搜索</template> -->
        </el-input>
        <el-button @click="resetData"> 搜索 </el-button>
      </div>
      <div class="right flex-row flex-1">
        <template v-if="is_edit">
          <div class="sucai_label" @click="selectAll">全选</div>
          <div class="sucai_label" @click="toDelete">删除</div>
          <div class="sucai_label" @click="resetAll">取消</div>
        </template>
        <template v-if="!is_edit">
          <el-button
            style="margin-right: 10px"
            @click="oper"
            icon="el-icon-s-operation"
            >批量操作</el-button
          >
        </template>

        <!-- width="100" -->

        <el-popover
          placement="bottom-start"
          trigger="click"
          width="50"
          v-model="showAdd"
        >
          <div class="shares_title align-center">
            <div class="share_title_label" @click="showUpload(2)">上传视频</div>
            <div class="share_title_label" @click="showUpload(1)">上传图片</div>
          </div>
          <el-button slot="reference" type="primary">+ 上传</el-button>
        </el-popover>
      </div>
    </div>
    <div class="filter mt24 flex-row">
      <div class="filter_list flex-1 flex-row items-center">
        <div
          class="filter_item"
          :class="{ active: params.type == 0 }"
          @click="
            params.page = 1;
            params.type = 0;
            getList();
          "
        >
          全部
        </div>
        <div class="line"></div>
        <div
          class="filter_item"
          :class="{ active: params.type == 1 }"
          @click="
            params.page = 1;
            params.type = 1;
            getList();
          "
        >
          图片
        </div>
        <div class="line"></div>
        <div
          class="filter_item"
          :class="{ active: params.type == 2 }"
          @click="
            params.page = 1;
            params.type = 2;
            getList();
          "
        >
          视频
        </div>
      </div>
    </div>
    <div class="list flex-row" v-infinite-scroll="loadMore">
      <div class="card" v-for="item in sucaiList" :key="item.id">
        <div class="card_img">
          <img :src="item.url" alt="" />
          <div
            v-if="is_edit"
            class="check_info"
            @click="item.check = !item.check"
            :class="{ checked: item.check == true }"
          >
            <i
              v-if="item.check"
              class="el-icon-check"
              style="color: #2d84fb"
            ></i>
            <!-- <img src="" alt=""> -->
          </div>
        </div>
        <div class="card_title flex-row">
          <div class="title_name">
            {{ item.title }}
          </div>
          <div class="title_r">已有{{ item.use_count }}人使用</div>
        </div>
        <!-- <div class="card_xiangmu">项目名称：{{ item.build }}</div> -->
        <div class="card_user flex-row">
          <div class="name">上传人：{{ item.admin_name }}</div>
          <div class="time">
            {{ item.created_at }}
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="show_upload_dialog"
      width="500px"
      title="上传素材"
    >
      <div class="upload_sucai">
        <!-- <div class="form flex-row">
          <el-form inline> -->
        <!-- <el-form-item label="素材名称">
              <el-input v-model="form.title"> </el-input>
            </el-form-item> -->
        <!-- <el-form-item label="" label-width="0" style="margin-right: 0"
                ><el-button type="primary">开始上传</el-button></el-form-item
              > -->
        <!-- </el-form>
        </div> -->
        <div class="upload_con">
          <el-upload
            class="upload-demo upload-file"
            drag
            :headers="headers"
            :accept="accept"
            :action="uploadUrl"
            multiple
            :auto-upload="false"
            :before-upload="beforeUpload"
            :show-file-list="false"
            :on-change="changeFile"
            :file-list="upList"
            :on-success="uploadDone"
            :on-progress="progress"
            ref="upload"
          >
            <template v-if="showUpList.length">
              <div class="upList flex-row flex-wrap">
                <template v-if="form.type == 1">
                  <div
                    v-for="(item, index) in showUpList"
                    :key="item.uid"
                    style="width: 80px; margin-right: 10px"
                    @click.prevent.stop="() => {}"
                  >
                    <div class="img">
                      <img :src="item.url" alt="" />
                      <div class="hover">
                        <div class="delete flex-row">
                          <i
                            class="el-icon-delete mr12"
                            @click.prevent.stop="removeImg(index)"
                          ></i>
                          <!-- <i class="el-icon-edit"></i> -->
                        </div>
                      </div>
                    </div>
                    <div class="inp">
                      <el-input :ref="'ref' + index" v-model="item.title">
                      </el-input>
                    </div>
                  </div>
                </template>
                <template v-if="form.type == 2">
                  <div
                    v-for="(item, index) in showUpList"
                    :key="item.uid"
                    style="width: 80px; margin-right: 10px"
                    @click.prevent.stop="() => {}"
                  >
                    <div class="img">
                      <video :src="item.url" alt="" preload="none"></video>
                      <div class="hover">
                        <div class="delete flex-row">
                          <i
                            class="el-icon-delete mr12"
                            @click.prevent.stop="removeImg(index)"
                          ></i>
                          <!-- <i class="el-icon-edit"></i> -->
                        </div>
                      </div>
                    </div>
                    <div class="inp">
                      <el-input :ref="'ref' + index" v-model="item.title">
                      </el-input>
                    </div>
                  </div>
                </template>
              </div>
            </template>
            <template v-else>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </template>

            <!-- <div class="el-upload__tip" slot="tip">
            只能上传jpg/png文件，且不超过500kb
          </div> -->
          </el-upload>

          <div class="progress">
            <!--   -->
            <div class="done_progress" :style="{ width: percent + '%' }"></div>
          </div>
        </div>

        <el-tabs v-model="type" class="record_content" @tab-click="setUpdata">
          <el-tab-pane name="0">
            <span slot="label">
              <el-link type="primary" :underline="false"
                >正在上传 {{ starting_num }}</el-link
              >
            </span>
          </el-tab-pane>
          <el-tab-pane name="1">
            <span slot="label">
              <el-link type="primary" :underline="false"
                >已上传 {{ done_num }}</el-link
              >
            </span>
          </el-tab-pane>
        </el-tabs>
        <el-table v-if="type == 0" :data="undone_list" max-height="200px">
          <el-table-column
            label="名称"
            align="center"
            prop="name"
          ></el-table-column>
          <el-table-column
            label="大小(kb)"
            align="center"
            prop="size"
          ></el-table-column>
          <el-table-column label="状态" align="center" v-slot="{ row }">
            {{ row.is_load == 1 ? "已上传" : "正在上传" }}
          </el-table-column>
        </el-table>
        <el-table v-if="type == 1" :data="done_list" max-height="200px">
          <el-table-column
            label="名称"
            align="center"
            prop="name"
          ></el-table-column>
          <el-table-column
            label="大小(kb)"
            align="center"
            prop="size"
          ></el-table-column>
          <el-table-column label="状态" align="center" v-slot="{ row }">
            {{ row.is_load == 1 ? "已上传" : "正在上传" }}
          </el-table-column>
        </el-table>
        <div class="footer flex-row">
          <div class="footer_left flex-1">本次共上传{{ done_num }}个素材</div>
          <el-button size="small" type="primary" @click="submitSucai">
            完成
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from "axios"
export default {
  data() {
    return {
      params: {
        title: "",
        type: 0,
        page: 1,
        page_size: 10
      },
      headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") },
      uploadUrl: "/api/common/file/upload/admin?category=101", //图片上传链接
      sucaiList: [
      ],
      type: "0",
      done_num: 0,
      done_list: [],
      undone_list: [],
      starting_num: 0,
      showAdd: false,
      show_upload_dialog: false,
      form: {
        type: 1,
        title: ""
      },
      accept: ".jpg,.png",
      upList: [
      ],
      addNum: 0,
      percent: 0,
      showUpList: [],
      load_status: false,
      is_edit: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (this.params.page == 1) {
        this.sucaiList = []
      }
      this.load_status = "loading"
      this.$http.getDouyinSucaiList(this.params).then(res => {
        if (res.status == 200) {
          res.data.data.map(item => {
            item.check = false
          })
          this.sucaiList = this.sucaiList.concat(res.data.data)
          if (res.data.data.length < this.params.page_size) {
            this.load_status = false
          } else {
            this.load_status = true
          }
        }
      })
    },
    oper() {
      this.is_edit = true

    },
    resetData() {
      console.log(123);
      this.params.page = 1;
      this.getList();
    },
    resetAll() {
      for (let index = 0; index < this.sucaiList.length; index++) {
        const element = this.sucaiList[index];
        this.$set(element, 'check', false)
      }
      this.is_edit = false
    },
    toDelete() {
      let ids = []
      this.sucaiList.map(item => {
        if (item.check) {
          ids.push(item.id)
        }
      })
      if (ids.length == 0) {
        this.$message.warning("请选择要删除的素材")
        return
      }
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {

          let id = ids.join(",")
          this.$http.delDouyinSucai(id).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.params.page = 1
              this.getList();
            }
          });
        })
    },
    selectAll() {
      // this.sucaiList.map(item => {
      //   item.check = true
      //   return item
      // })
      for (let index = 0; index < this.sucaiList.length; index++) {
        const element = this.sucaiList[index];
        this.$set(element, 'check', true)
      }
      this.$forceUpdate()
      // this.is_edit = true
    },
    showUpload(type) {
      this.form.type = type
      if (type == 1) {
        this.uploadUrl = '/api/common/file/upload/admin?category=101'
        this.accept = '.jpg,.png'
      } else if (type == 2) {
        this.uploadUrl = '/api/admin/byte_dance/upload_file'
        this.accept = '.mp4'
      }
      this.upList = []
      this.starting_num = 0
      this.done_num = 0
      this.showUpList = []
      this.isSubmiting = false
      this.show_upload_dialog = true
    },
    beforeUpload(file, fileList) {
      console.log(file, fileList);
    },
    progress(e, v) {
      console.log(e, v);
    },

    loadMore() {
      if (!this.load_status || this.load_status == "loading") {
        return;
      }
      console.log(this.load_status);
      this.params.page++;
      this.getList();
    },
    async changeFile(file, fileList) {

      var upload_img = document.getElementsByClassName('upload-file')
      let uploadNum = 0
      if (upload_img && upload_img.length > 0) {
        var upload = upload_img[0].getElementsByTagName('input')
        console.log(upload);
        if (upload && upload.length > 0 && upload[0].files && upload[0].files.length > 0) {
          uploadNum = upload[0].files.length
        }
      }

      this.addNum++

      if (this.addNum == uploadNum) {
        console.log('可以上传', fileList)
        fileList.map(item => {
          item.is_load = 0
        })
        this.upList = fileList;
        console.log(fileList);
        this.setUpdata()
        this.starting_num = fileList.length
        this.uploadFile()

        // this.$message(this.upList[upResult.index].name)

      }
      // console.log(file, fileList);
      // file.is_load = 0
      // // console.log(this.upList);
      // this.upList.push(file)
      // let files = fileList.map(item => {
      //   item.is_load = 0
      //   return item
      // })
      // console.log(file, fileList);
      // this.upList = files
      // // this.showUpList = this.upList
      // // this.uploadFile(file)


    },
    removeImg(idx) {
      this.showUpList = this.upList = this.upList.filter((item, index) => index != idx)
      this.setUpdata()
      this.setNum()
    },
    async uploadFile() {
      var tasks = [], upNum = 0
      for (let index = 0; index < this.upList.length; index++) {
        const file = this.upList[index];
        if (file.size > 1024 * 1024 * 20 && this.type == 2) {
          this.$message.warning(this.upList[index].name + '文件太大跳过上传')
          continue
        }
        if (file.size > 1024 * 1024 * 10 && this.type == 1) {
          this.$message.warning(this.upList[index].name + '图片太大跳过上传')
          continue
        }
        tasks.push(
          new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file.raw, file.name);  // 块数据
            axios
              .post(this.uploadUrl, formData, { headers: this.headers, timeout: 600000 }).then(async res => {
                if (res.status == 200) {
                  this.upList[index].is_load = 1
                  this.upList[index].url = res.data.url
                  this.showUpList.push(this.upList[index])
                  let mediaIdRes = await this.$http.uploadVideoTomedia({ url: res.data.url, type: +this.form.type })
                  console.log(mediaIdRes);
                  if (mediaIdRes.status == 200) {
                    this.setNum()
                    upNum++
                    this.percent = Math.floor((upNum / this.upList.length) * 100)
                    this.setUpdata()
                    this.upList[index].media_id = mediaIdRes.data.image_id || mediaIdRes.data.video_id
                    this.upList[index].width = mediaIdRes.data.width
                    this.upList[index].height = mediaIdRes.data.height
                    this.$message.success(this.upList[index].name + '上传成功')
                    resolve({ data: mediaIdRes.data, index })
                  } else {
                    reject(mediaIdRes.data)
                  }
                } else {
                  reject(res.data)
                }
              }).catch((err) => {
                reject(err)
              })
          })
        )


      }
      await Promise.all(tasks).then((res) => {
        this.addNum = 0
        // console.log(res, 1111);
        // this.$message.success(this.upList[index].name + '上传成功')
        this.$refs.upload.clearFiles()
      })
    },
    uploadDone(response, file) {
      // this.upList.push(file)
      console.log(response, "12332");
      if (response) {
        let index = this.upList.findIndex(item => item.name == file.name)
        this.getMediaId(response.url, index)
      }


    },
    setNum() {
      this.starting_num--
      this.done_num++
    },
    setUpdata() {
      this.done_list = this.upList.filter(item => item.is_load == 1)
      this.undone_list = this.upList.filter(item => item.is_load == 0)
      // this.showUpList = this.upList.filter(item => item.is_load == this.type)
    },
    getMediaId(url, index) {
      this.$http.uploadVideoTomedia({ url, type: +this.form.type }).then(res => {
        if (res.status == 200) {
          this.setNum()
          this.upList[index].is_load = 1
          this.percent = Math.floor((this.done_num / this.upList.length) * 100)
          console.log(this.percent);
          this.setUpdata()
          this.$refs.upload.clearFiles()
        }
      })
    },
    submitSucai() {
      if (this.isSubmiting) return
      this.isSubmiting = true
      let tasks = []

      for (let index = 0; index < this.upList.length; index++) {
        const element = this.upList[index];
        if (!element.title) {
          this.$message.warning('请输入素材标题')
          break
        }

      }

      for (let index = 0; index < this.upList.length; index++) {
        const element = this.upList[index];
        tasks.push(new Promise((resove, rej) => {
          let obj = {
            type: this.form.type,
            url: element.url,
            media_id: element.media_id,
            title: element.title,
            width: element.width,
            height: element.height
          }
          this.$http.submitDouyinsuCai(obj).catch(() => {
            rej({})
            this.isSubmiting = false
          }).then((res) => {
            if (res.status != 200) {
              this.isSubmiting = false
              rej(res)
              return
            }
            resove(res)
          })

        }))
        Promise.all(tasks).then((res) => {
          console.log(res);
          this.isSubmiting = false
          this.show_upload_dialog = false;
          this.params.page = 1;
          this.getList();
          // this.$message.success("上传成功")
        })

      }

      // let urls = []
      // this.upList.map(item => {
      //   // media_ids.push(item.media_id)
      //   urls.push(item.url)
      //   return item
      // })
      //   this.form.media_id = this.upList[0].media_id
      //   this.form.url = urls.join(",")
      //   this.$http.submitDouyinsuCai(this.form).then(res => {
      //     if (res.status == 200) {
      //       this.show_upload_dialog = false;
      //       this.params.page = 1;
      //       this.isSubmiting = false
      //       this.getList();
      //     } else {
      //       this.isSubmiting = false
      //     }
      //   })
      //     .catch(() => {
      //       this.isSubmiting = false
      //     })
      // }
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-row {
  display: flex;
  flex-direction: row;
}
.mt24 {
  margin-top: 24px;
}
.flex-1 {
  flex: 1;
}
.sucai_list {
  padding: 24px;
  margin: -15px;
  background: #f1f4fa;
  .sucai_list_t {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    .right {
      justify-content: flex-end;
      align-items: center;
    }
    .sucai_label {
      padding: 8px 12px;
      background: #ffffff;
      border: 1px solid #dde1e9;
      border-radius: 8px;
      font-size: 12px;
      margin-right: 12px;
      color: #8a929f;
      cursor: pointer;
    }
  }
  .filter {
    .filter_list {
      align-items: center;
      .filter_item {
        font-weight: 500;
        font-size: 16px;
        color: #8a929f;
        margin: 0 12px;
        cursor: pointer;
        &:first {
          margin-left: 0;
        }

        &.active {
          color: #2d84fb;
        }
      }
      .line {
        background: #8a929f;
        width: 2px;
        height: 18px;
      }
    }
  }
  .list {
    // position: relative;
    flex-wrap: wrap;
    .card {
      padding: 24px;
      margin-top: 20px;
      background: #ffffff;
      border-radius: 8px;
      margin-right: 24px;
      .card_img {
        width: 229px;
        height: 150px;
        overflow: hidden;
        position: relative;
        background: linear-gradient(
          0deg,
          rgba(0, 0, 0, 0.2),
          rgba(0, 0, 0, 0.2)
        );
        .check_info {
          position: absolute;
          top: 5px;
          left: 5px;
          width: 18px;
          height: 18px;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #8a929f;
          &.checked {
            border-color: #2d84fb;
          }
        }
        .img {
          width: 100%;
          object-fit: cover;
        }
      }
      .card_title {
        align-items: center;
        margin-top: 12px;
        .title_name {
          font-weight: 500;
          font-size: 16px;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #2e3c4e;
        }
        .title_r {
          min-width: 80px;
          font-size: 12px;
          color: #2d84fb;
        }
      }
      .card_user {
        align-items: center;
        margin-top: 12px;
        .name {
          font-size: 12px;
          color: #8a929f;
          flex: 1;
        }
        .time {
          font-size: 12px;
          color: #8a929f;
          justify-content: flex-end;
        }
      }
    }
  }
  .upload-demo ::v-deep .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
    }
  }
}
.upload_sucai {
  .upload_con {
    position: relative;
    .progress {
      position: absolute;
      bottom: -5px;
      left: 0;
      right: 0;
      height: 2px;
      border-radius: 2px;
      background: #8a929f;
      .done_progress {
        background: #2d84fb;
        height: 2px;
      }
    }
  }
  .upList {
    flex-wrap: wrap;
    padding: 10px;
    .img {
      width: 80px;
      height: 80px;
      // padding: 5px;
      position: relative;
      overflow: hidden;
      &:hover {
        .hover {
          display: flex;
        }
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      video {
        width: 80px;
        height: 80px;
        object-fit: cover;
      }
    }

    .inp {
      margin-top: 8px;
    }
    .hover {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      background: rgba(0, 0, 0, 0.3);
      display: none;
      justify-content: center;
      align-items: center;
      flex-direction: row;
      .el-icon-delete {
        color: #f00;
        cursor: pointer;
      }
    }
  }
  .footer {
    padding: 10px 0;
    align-items: center;
  }
}
.shares_title {
  .share_title_label {
    padding: 10px 5px;
    cursor: pointer;
  }
}
</style>