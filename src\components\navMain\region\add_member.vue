<template>
    <div>
        <div class="Add">
            <span>类型 :</span>
            <el-select v-model="Member.type" slot="prepend" placeholder="请选择" style="width: 300px;">
                <el-option label="置业顾问" value="1"></el-option>
                <el-option label="经纪人" value="2"></el-option>
            </el-select>
            <!-- <div class="uname">
                <span>姓名 :</span>
                <el-input v-model="Member.name" placeholder="请输入内容"
                    style="width: 300px; margin-left: 32px;"></el-input>
            </div> -->
            <!-- <div class="uname">
                <span>用户名 :</span>
                <el-input v-model="Member.name" placeholder="请输入内容" style="width: 300px;"></el-input>
            </div> -->
            <div class="uname">
                <span>手机号 :</span>
                <el-input v-model="Member.phone" placeholder="请输入内容" style="width: 300px;"></el-input>
            </div>
            <div class="password">
                <el-form :model="Member" status-icon :rules="rules" ref="ruleForm" label-width="80px" class="demo-ruleForm">
                    <el-form-item label="密码:" prop="password" style="width: 375px;">
                        <el-input type="password" v-model="Member.password" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="确认密码:" prop="repassword" style="width:375px;">
                        <el-input type="password" v-model="Member.repassword" autocomplete="off"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div class="status">
                <span>状态 ：</span>
                <el-switch v-model="value" active-color="#13ce66">
                </el-switch>
            </div>
            <el-button type="warning" plain @click="AddMember">添加会员</el-button>
        </div>


    </div>
</template>
<script>
export default {
    data() {
        var validatePass = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入密码'));
            } else {
                if (this.Member.repassword !== '') {
                    this.$refs.Member.validateField('repassword');
                }
                callback();
            }
        };
        var validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else if (value !== this.Member.password) {
                callback(new Error('两次输入密码不一致!'));
            } else {
                callback();
            }
        };
        return {
            rules: {
                password: [
                    { validator: validatePass, trigger: 'blur' }
                ],
                repassword: [
                    { validator: validatePass2, trigger: 'blur' }
                ],
            },
            value: false,
            Member: {
                type: "",
                // name: "",
                phone: "",
                password: '',
                repassword: '',
                status: "",

            }

        };
    },
    methods: {
        AddMember() {
            // console.log(123);
            if(this.value==true){
                this.Member.status=1
            }else{
                this.Member.status=0
            }
            this.$http.AddMember_List(this.Member).then(res=>{
                console.log(res);
                if(res.status==200){
                    this.$message({
                        type: 'success',
                        message: '添加成功!'
                    });
                    this.$goPath("Backstage_Members");
                }
            })
        }
    }
}
</script>
<style scoped lang="scss">
.Add {
    // width: 500px;
    height: 760px;
    margin-left: 90px;
    margin-top: 30px;

    span {
        font-size: 17px;
    }

    .el-select {
        margin-left: 30px;
    }

    .uname {
        margin-top: 20px;

        .el-input {
            margin-left: 15px;
        }

        .el-select {
            margin-left: 30px;
        }
    }

    .password {
        margin-top: 20px;

        /deep/.el-form-item__label {
            text-align: left;
            font-size: 16px;
            color: #000;
        }
    }

    .status {
        .el-switch {
            margin-left: 70px;
        }
    }

    .el-button {
        margin-left: 350px;
        margin-top: 30px;
    }

}
</style>