<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="授权设置" name="setting">
        <el-form label-width="120px" label-position="left">
          <!-- <el-form-item label="小程序版本">
            <div class="flex-row items-center">
              <el-radio-group v-model="mini_version">
                <el-radio-button
                  v-for="item in typeList"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.name }}</el-radio-button
                >
              </el-radio-group>
            </div>
          </el-form-item> -->
          <el-form-item label="授权小程序">
            <div class="flex-row items-center">
              <el-button type="primary" @click="getLink">获取授权链接</el-button>
            </div>
          </el-form-item>
          <el-form-item label="公司名称">
            <div class="flex-row items-center">
              <el-input class="w240" v-model="setting_company_form.app_company">
              </el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  与小程序主体一致 必填
                </div>
                <i class="el-icon-info" style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 15px;
                  "></i>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="公司地址">
            <div class="flex-row items-center">
              <el-input class="w240" type="textarea" rows="3" v-model="setting_company_form.app_address">
              </el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  用于隐私协议中公司地址 必填
                </div>
                <i class="el-icon-info" style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 30px;
                    margin-left: 15px;
                  "></i>
              </el-tooltip>
              <!-- <el-link type="warning">用于隐私协议中公司地址 必填</el-link> -->
            </div>
          </el-form-item>
          <el-form-item label="邮箱">
            <div class="flex-row items-center">
              <el-input class="w240" v-model="setting_company_form.app_email">
              </el-input>
              <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  用于隐私协议中联系邮箱 必填
                </div>
                <i class="el-icon-info" style="
                    color: #f56c6c;
                    font-size: 20px;
                    margin-top: 10px;
                    margin-left: 15px;
                  "></i>
              </el-tooltip>
              <!-- <el-link type="warning">用于隐私协议中联系邮箱 必填</el-link> -->
            </div>
          </el-form-item>
          <el-form-item>
            <div class="flex-row items-center">
              <el-button type="primary" @click="saveCompanyInfo">保存</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="抖音小程序配置" name="tt_setting">
        <el-tabs v-model="active_name" @tab-click="tabClick">
          <el-tab-pane :label="item.title" :name="value" v-for="(item, value) in setting" :key="value">
            <!-- v-for="(item, value) in setting"
                :key="value" -->
            <div class="info">
              <!-- <el-card shadow="never"> -->
              <!-- <div slot="header" class="clearfix">
                  <span>{{ item.title }}</span>
                </div> -->
              <el-form label-width="150px" size="medium">
                <el-form-item :label="sub.title" v-for="(sub, key) in item.sub" :key="key">
                  <template v-if="sub.type != 'json'">
                    <template v-if="sub.type != 'image'">
                      <span slot="label">
                        {{ sub.title }}
                        <el-tooltip v-if="sub.tips" :content="sub.tips" placement="top-start">
                          <el-link type="warning" :underline="false"><i class="el-icon-info"></i></el-link>
                        </el-tooltip>
                      </span>
                      <el-input v-model="douyinIndexParam[sub.name]" style="width: 50%" v-if="sub.type == 'text'"
                        :maxlength="sub.name == 'app_name'
      ? 10
      : sub.name == 'share_title'
        ? 15
        : 255
      " show-word-limit></el-input>
                      <el-input v-model="douyinIndexParam[sub.name]" style="width: 50%" type="textarea" rows="3"
                        v-if="sub.type == 'textarea'" :maxlength="sub.name == 'app_name'
      ? 10
      : sub.name == 'share_title'
        ? 15
        : 255
      " show-word-limit></el-input>
                      <el-radio-group v-model="douyinIndexParam[sub.name]" v-if="sub.type == 'radio'">
                        <el-radio v-for="rItem in sub.option" :key="rItem.value" :label="rItem.value">{{ rItem.name
                          }}</el-radio>
                      </el-radio-group>
                      <template v-if="sub.type == 'rich_text'">
                        <UE :value="douyinIndexParam[sub.name]" :config="ueditor.config"
                          @input="inputUe($event, sub.name)" :ids="'ueditor' + sub.name" :ref="'ue' + sub.name"
                          style="width: 1000px"></UE>
                      </template>
                    </template>
                    <template v-if="sub.type == 'image'">
                      <el-row>
                        <el-col style="width: 200px">
                          <el-upload action="/api/common/file/upload/admin?category=3" list-type="picture-card"
                            :headers="myHeader" :show-file-list="false" :on-preview="handlePictureCardPreview"
                            :on-success="(e) => handleUploadSuccess(e, sub.name)
      ">
                            <img v-if="douyinIndexParam[sub.name]" :src="douyinIndexParam[sub.name] +
      '?x-oss-process=style/w_240'
      " class="up_image" />
                            <i v-else class="el-icon-plus"></i>
                          </el-upload>
                        </el-col>
                        <el-col :span="10" v-if="sub.tips">
                          <div style="
                              background-color: #faecd8;
                              color: #f40;
                              padding: 10px;
                              line-height: 2;
                            ">
                            {{ sub.tips }}
                          </div>
                        </el-col>
                      </el-row>
                    </template>
                    <template v-if='sub.type == "checkbox"'>
                      <!-- <el-checkbox v-model="item.value" v-for="(item, index) in sub.option" :key="index">{{ item.name
                        }}</el-checkbox> -->
                      <el-checkbox-group v-model="sub.defaultArr" @change="checkboxChange($event, sub)">
                        <el-checkbox :label="item.name" v-for="(item, index) in sub.option" :key="index"></el-checkbox>
                      </el-checkbox-group>
                    </template>
                  </template>
                  <template v-if="sub.type == 'json'">
                    <template v-if="sub.name == 'nav_setting'">
                      <!-- 菜单设置 -->
                      <el-form label-width="80px" size="mini">
                        <el-form-item label-width="0" label="">
                          <el-radio-group v-model="setting_nav.status" @change="radioChange">
                            <el-radio v-for="nav_setting in setting_nav.value" :key="nav_setting.value"
                              :label="nav_setting.value">{{ nav_setting.title }}</el-radio>
                          </el-radio-group>
                        </el-form-item>

                        <el-form-item label-width="0" v-if="setting_nav.status">
                          <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                          <el-form size="mini">
                            <ul>
                              <li style="
                                  display: inline-block;
                                  margin-right: 15px;
                                  margin-bottom: 10px;
                                " v-for="(row, rowi) in setting_nav.value[
      setting_nav.status - 1
    ].children" :key="rowi">
                                <el-upload action="/api/common/file/upload/admin?category=3" list-type="picture-card"
                                  style="margin-bottom: 10px" :headers="myHeader" :show-file-list="false" disabled
                                  :on-preview="handlePictureCardPreview" :on-success="(e) => handleUploadSuccess(e, row, 1)
      ">
                                  <img v-if="row.bg_pic" :src="row.bg_pic" class="up_image" />
                                  <i v-else class="el-icon-plus"></i>
                                </el-upload>
                                <el-form-item label="标题" label-width="50px" class="img-input" :rules="{
      required: true,
      message: '请填写标题',
      trigger: 'blur',
    }">
                                  <el-input v-model="row.title" size="mini" class="w100"></el-input>
                                </el-form-item>
                                <el-form-item label="链接" label-width="50px" class="img-input" :rules="{
      required: true,
      message: '请填写链接',
      trigger: 'blur',
    }">
                                  <el-input v-model="row.link" size="mini" disabled class="w100"></el-input>
                                </el-form-item>
                                <el-form-item label="数量" v-if="setting_nav.status == 1" label-width="50px"
                                  class="img-input">
                                  <el-radio-group v-model="row.number_type" size="mini" class="radio_small">
                                    <!-- <el-radio label="2" style="margin-right: 10px"
                                    >载入数据</el-radio
                                  > -->
                                    <el-radio style="font-size: 12px" v-for="r1 in row.number_type_option"
                                      :key="r1.value" :label="+r1.value">{{ r1.name }}</el-radio>
                                  </el-radio-group>
                                  <div>
                                    <el-input v-model="row.number" size="mini" placeholder="请输入数量"
                                      v-if="row.number_type == 2" class="w100"></el-input>
                                    <el-select size="mini" v-else class="w100" v-model="row.number_select">
                                      <el-option v-for="r2 in row.number_select_option" :key="r2.value" :label="r2.name"
                                        :value="+r2.value">
                                      </el-option>
                                    </el-select>
                                    <!-- <el-cascader
                                    v-else-if="row.parameter.type == 2"
                                    class="w100"
                                    v-model="row.parameter.name"
                                    :options="douyinSubnavCountOptions"
                                  ></el-cascader> -->
                                  </div>
                                </el-form-item>
                                <el-form-item label="状态" label-width="50px" class="img-input">
                                  <el-radio-group v-model="row.status">
                                    <el-radio :label="1" style="margin-right: 10px">显示</el-radio>
                                    <el-radio :label="0">隐藏</el-radio>
                                  </el-radio-group>
                                </el-form-item>
                              </li>
                            </ul>
                          </el-form>
                        </el-form-item>
                        <!-- <el-button
                        type="primary"
                        size="mini"
                        v-if="
                          douyinIndexParam.nav.is_show &&
                          douyinIndexParam.nav.setting == 0
                        "
                        style="margin-left: 80px"
                        @click="openDouyinIndexMenu"
                        >去设置</el-button
                      > -->
                      </el-form>
                    </template>
                    <template v-if="sub.name == 'xinxi_setting'">
                      <el-form label-width="80px" size="mini">
                        <el-form-item :label="child.title" v-for="(child, childi) in setting_xinxi.value" :key="childi">
                          <el-radio-group v-model="child.status" style="margin-right: 15px">
                            <el-radio v-for="child_xin in child.status_option" :key="child_xin.value"
                              :label="+child_xin.value">{{ child_xin.name }}</el-radio>
                            <!-- <el-radio :label="0">关闭</el-radio> -->
                          </el-radio-group>
                          <span class="order" style="margin-right: 8px">排序</span>
                          <span class="order-inp" style="margin-right: 15px">
                            <el-input v-model="child.sort" type="number" min="0" style="width: 60px">
                            </el-input>
                          </span>

                          <span class="order" style="margin-right: 8px">自定义</span>
                          <span class="order-inp" style="margin-right: 15px">
                            <el-input v-model="child.title_set" placeholder="自定义名称" style="width: 120px">
                            </el-input>
                          </span>
                        </el-form-item>
                      </el-form>
                    </template>
                    <template v-if="sub.name == 'member_menu_setting'">
                      <el-form-item label-width="0" v-if="setting_member_menu.status">
                        <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                        <el-form size="mini">
                          <ul>
                            <li style="
                                display: inline-block;
                                margin-right: 15px;
                                margin-bottom: 10px;
                              " v-for="(row, rowi) in setting_member_menu.value" :key="rowi">
                              <el-upload action="/api/common/file/upload/admin?category=3" list-type="picture-card"
                                :headers="myHeader" disabled :show-file-list="false"
                                :on-preview="handlePictureCardPreview" :on-success="(e) => handleUploadSuccess(e, row, 1)
      ">
                                <img v-if="row.bg_pic" :src="row.bg_pic" class="up_image" />
                                <i v-else class="el-icon-plus"></i>
                              </el-upload>
                              <el-form-item label="" label-width="50px" class="img-input">
                                {{ row.title }}
                              </el-form-item>
                              <el-form-item label="状态" label-width="50px" class="img-input">
                                <el-radio-group v-model="row.status">
                                  <el-radio v-for="r in row.status_option" :key="r.value" :label="+r.value"
                                    style="margin-right: 10px">{{ r.name }}</el-radio>
                                  <!-- <el-radio :label="0">隐藏</el-radio> -->
                                </el-radio-group>
                              </el-form-item>
                            </li>
                          </ul>
                        </el-form>
                      </el-form-item>
                    </template>
                    <template v-if="sub.name == 'new_house_menu_setting'">
                      <el-form-item label-width="0" v-if="new_house_menu_setting.status">
                        <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                        <el-form size="mini">
                          <ul>
                            <li style="
                                display: inline-block;
                                margin-right: 15px;
                                margin-bottom: 10px;
                              ">
                              <el-form-item :label="row.title" label-width="120px" class="img-input" v-for="(
                                  row, rowi
                                ) in new_house_menu_setting.value" :key="rowi">
                                <el-radio-group v-model="row.status" @change="($event) => {
      new_house_radio_change($event, row, rowi);
    }
      ">
                                  <el-radio v-for="r in row.status_option" :key="r.value" :label="+r.value"
                                    style="margin-right: 10px">{{ r.name }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </li>
                          </ul>
                        </el-form>
                      </el-form-item>
                    </template>
                    <template v-if="sub.name == 'nav_menu_setting'">
                      <el-form label-width="80px" size="mini">
                        <!-- <el-form-item label-width="0" label="">
                          <el-radio-group
                            v-model="setting_nav.status"
                            @change="radioChange"
                          >
                            <el-radio
                              v-for="nav_setting in setting_nav.value"
                              :key="nav_setting.value"
                              :label="nav_setting.value"
                              >{{ nav_setting.title }}</el-radio
                            >
                          </el-radio-group>
                        </el-form-item> -->

                        <el-form-item label-width="0">
                          <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                          <el-form size="mini">
                            <ul>
                              <li style="
                                  display: inline-block;
                                  margin-right: 15px;
                                  margin-bottom: 10px;
                                " v-for="(row, rowi) in nav_menu_setting.value" :key="rowi">
                                <el-upload action="/api/common/file/upload/admin?category=3" list-type="picture-card"
                                  style="margin-bottom: 10px" :headers="myHeader" :show-file-list="false" disabled
                                  :on-preview="handlePictureCardPreview" :on-success="(e) => handleUploadSuccess(e, row, 1)
      ">
                                  <img v-if="row.bg_pic" :src="row.bg_pic" class="up_image" />
                                  <i v-else class="el-icon-plus"></i>
                                </el-upload>
                                <el-form-item label="标题" label-width="50px" class="img-input" :rules="{
      required: true,
      message: '请填写标题',
      trigger: 'blur',
    }">
                                  <el-input v-model="row.title" size="mini" class="w100"></el-input>
                                </el-form-item>
                                <el-form-item label="链接" label-width="50px" class="img-input" :rules="{
      required: true,
      message: '请填写链接',
      trigger: 'blur',
    }">
                                  <el-input v-model="row.link" size="mini" disabled class="w100"></el-input>
                                </el-form-item>
                                <el-form-item label="状态" label-width="50px" class="img-input">
                                  <el-radio-group v-model="row.status">
                                    <el-radio :label="1" style="margin-right: 10px">显示</el-radio>
                                    <el-radio :label="0">隐藏</el-radio>
                                  </el-radio-group>
                                </el-form-item>
                              </li>
                            </ul>
                          </el-form>
                        </el-form-item>
                        <!-- <el-button
                        type="primary"
                        size="mini"
                        v-if="
                          douyinIndexParam.nav.is_show &&
                          douyinIndexParam.nav.setting == 0
                        "
                        style="margin-left: 80px"
                        @click="openDouyinIndexMenu"
                        >去设置</el-button
                      > -->
                      </el-form>
                    </template>
                    <template v-if="sub.name == 'broker_menu_setting'">
                      <el-form-item label-width="0">
                        <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                        <el-form size="mini">
                          <ul>
                            <li>
                              <el-form-item :label="row.title" label-width="" class="img-input"
                                v-for="(row, rowi) in broker_menu_setting.value" :key="rowi">
                                <el-radio-group v-model="row.status" @change="($event) => {
      broker_menu_radio_change(
        $event,
        row,
        rowi
      );
    }
      ">
                                  <el-radio v-for="r in row.status_option" :key="r.value" :label="+r.value"
                                    style="margin-right: 10px">{{ r.name }}</el-radio>
                                </el-radio-group>
                              </el-form-item>

                              <el-form-item label="经纪人挂载" style="display: flex;">
                                <el-select v-model="selectInnerUser" filterable multiple placeholder="请选择置业顾问（支持搜索）"
                                  @change="changeInnerUser">
                                  <el-option v-for="item in innerUserList" :key="item.id" :label="item.user_name"
                                    :value="item.id">
                                  </el-option>
                                </el-select>
                              </el-form-item>
                              <el-form-item label="">
                                <el-button type="primary" style="margin-right: 10px;"
                                  @click="setInnerUser">确认设置</el-button>
                                <el-popover placement="right" width="300" trigger="click">
                                  <el-table :data="setUpInnerUserList">
                                    <el-table-column width="150" property="user_name" label="姓名"></el-table-column>
                                    <el-table-column width="150" property="phone" label="电话"></el-table-column>
                                  </el-table>
                                  <el-button slot="reference">已挂载的经纪人<i
                                      class="el-icon-caret-bottom el-icon--right"></i></el-button>
                                </el-popover>
                              </el-form-item>
                            </li>
                          </ul>
                        </el-form>

                      </el-form-item>
                    </template>
                  </template>
                </el-form-item>
              </el-form>
              <!-- </el-card> -->

              <!-- <div v-if="douyinIndexParam.search">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>搜索框</span>
              </div>
              <el-form label-width="120px" size="medium">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.search_status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>

                <template v-if="douyinVersion == 1">
                  <el-form-item
                    label="风格"
                    v-if="douyinIndexParam.search.is_show"
                  >
                    <el-radio-group v-model="douyinIndexParam.search_style">
                      <el-radio label="1">圆角</el-radio>
                      <el-radio label="2">矩形</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </template>
                
              </el-form>
            </el-card>
          </div>

          <div>
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>顶部图片设置</span>
              </div>
              <el-form label-width="80px" size="medium">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.lunbo_status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <template v-if="douyinIndexParam.lunbo_status">
                  <template>
                    <el-form-item label="">
                      <el-row>
                        <el-col style="width: 200px">
                          <el-upload
                            action="/api/common/file/upload/admin?category=3"
                            list-type="picture-card"
                            :show-file-list="false"
                            :on-preview="handlePictureCardPreview"
                            :before-upload="beforeUpload"
                            :on-success="
                              (e) =>
                                handleUploadSuccess(
                                  e,
                                  douyinIndexParam.lunbo_pic
                                )
                            "
                          >
                            <img
                              v-if="douyinIndexParam.lunbo_pic"
                              :src="
                                douyinIndexParam.lunbo_pic +
                                '?x-oss-process=style/w_240'
                              "
                              class="up_image"
                            />
                            <i v-else class="el-icon-plus"></i>
                          </el-upload>
                        </el-col>
                        <el-col :span="10">
                          <div
                            style="
                              background-color: #faecd8;
                              color: #f40;
                              padding: 10px;
                              line-height: 2;
                            "
                          >
                            禁止出现微信及二维码、禁止出现电话号码、禁止出现广告内容，只能为自有品牌形象，不需要可关闭。推荐尺寸860*650像素。
                          </div>
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </template>
                </template>
              </el-form>
            </el-card>
          </div>

          <div v-if="douyinIndexParam.nav">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>菜单设置</span>
              </div>
              <el-form label-width="80px" size="mini">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.nav_status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item v-if="douyinIndexParam.nav_status == 1">
                  <el-form :model="douyinIndexParam.nav_setting" size="mini">
                    <ul>
                      <li
                        style="display: inline-block; margin-right: 15px"
                        v-for="(row, i) in douyinIndexParam.nav_setting"
                        :key="i"
                      >
                        <el-upload
                          action="{:url('Upload/uploadBuild')}"
                          list-type="picture-card"
                          :show-file-list="false"
                          :on-preview="handlePictureCardPreview"
                          :before-upload="beforeUpload"
                          :on-success="
                            (e) => handleUploadSuccess(e, row, 'icon')
                          "
                        >
                          <img
                            v-if="row.bg_pic"
                            :src="row.bg_pic"
                            class="up_image"
                          />
                          <i v-else class="el-icon-plus"></i>
                        </el-upload>
                        <el-form-item
                          label="标题"
                          label-width="50px"
                          class="img-input"
                          :prop="'[' + i + '].title'"
                          :rules="{
                            required: true,
                            message: '请填写标题',
                            trigger: 'blur',
                          }"
                        >
                          <el-input
                            v-model="row.title"
                            size="mini"
                            class="w100"
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="链接"
                          label-width="50px"
                          class="img-input"
                          :prop="'[' + i + '].path'"
                          :rules="{
                            required: true,
                            message: '请填写链接',
                            trigger: 'blur',
                          }"
                        >
                          <el-input
                            v-model="row.link"
                            size="mini"
                            class="w100"
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="数量"
                          label-width="50px"
                          class="img-input"
                        >
                          <el-radio-group v-model="row.parameter.type">
                            <el-radio label="2" style="margin-right: 10px"
                              >载入数据</el-radio
                            >
                            <el-radio label="1">自定义</el-radio>
                          </el-radio-group>
                          <div>
                            <el-input
                              v-model="row.number_select"
                              v-if="row.parameter.type == 1"
                              size="mini"
                              class="w100"
                            ></el-input>
                            <el-cascader
                              v-else-if="row.parameter.type == 2"
                              class="w100"
                              v-model="row.parameter.name"
                              :options="douyinSubnavCountOptions"
                            ></el-cascader>
                          </div>
                        </el-form-item>
                        <el-form-item
                          label="状态"
                          label-width="50px"
                          class="img-input"
                        >
                          <el-radio-group v-model="row.is_show">
                            <el-radio :label="1" style="margin-right: 10px"
                              >显示</el-radio
                            >
                            <el-radio :label="0">隐藏</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </li>
                    </ul>
                  </el-form>
                </el-form-item>
                <el-button
                  type="primary"
                  size="mini"
                  v-if="
                    douyinIndexParam.nav.is_show &&
                    douyinIndexParam.nav.setting == 0
                  "
                  style="margin-left: 80px"
                  @click="openDouyinIndexMenu"
                  >去设置</el-button
                >
              </el-form>
            </el-card>
          </div>

          <div v-if="douyinIndexParam.gundongzixun && douyinVersion == 2">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>资讯设置</span>
              </div>
              <el-form label-width="120" size="medium">
                <el-form-item label="是否开启">
                  <el-radio-group
                    v-model="douyinIndexParam.gundongzixun.is_show"
                  >
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
          <div v-if="douyinIndexParam.xinxilan">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>信息设置</span>
              </div>
              <el-form label-width="80px" size="mini">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.xinxilan.is_show">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  :label="child.title"
                  v-for="(child, i) in douyinIndexParam.xinxilan.children"
                  :key="i"
                >
                  <el-radio-group v-model="child.is_show">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                  <span class="order">排序</span>
                  <span class="order-inp">
                    <el-input v-model="child.sort" type="number"> </el-input>
                  </span>

                  <span class="order">自定义</span>
                  <span class="order-inp">
                    <el-input v-model="child.value" placeholder="自定义名称">
                    </el-input>
                  </span>
                </el-form-item>
              </el-form>
            </el-card>

            <el-card shadow="never" v-if="douyinIndexParam.member_menu">
              <div slot="header" class="clearfix">
                <span>会员菜单</span>
              </div>
              <el-form
                :model="douyinIndexParam.member_menu.children"
                size="mini"
              >
                <ul>
                  <li
                    style="
                      display: inline-block;
                      margin-right: 15px;
                      text-align: center;
                    "
                    v-for="(row, i) in douyinIndexParam.member_menu.children"
                    :key="i"
                  >
                    <el-form-item label="" label-width="50px" class="img-input">
                      <img :src="row.icon" width="65" class="up_image" />
                    </el-form-item>
                    <el-form-item label="" label-width="50px" class="img-input">
                      {{ row.title }}
                    </el-form-item>
                    <el-form-item label="" label-width="50px" class="img-input">
                      <el-radio-group v-model="row.is_show">
                        <el-radio
                          :label="1"
                          style="margin-right: 10px"
                          @change="onMemberMenuChange(row)"
                          >显示</el-radio
                        >
                        <el-radio :label="0">隐藏</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </li>
                </ul>
              </el-form>
            </el-card>
          </div> -->
            </div>
            <div class="btns" v-if="isSubmitDouyinIndexData">
              <!-- <el-button type="primary" @click="submitDouyinIndexData"
            >提交修改
          </el-button> -->
              <div class="submit" @click="submitDouyinIndexData">提交修改</div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <el-tab-pane label="用户列表" name="user">
        <el-table :data="tableData" border>
          <el-table-column label="用户头像" width="100" v-if="false">
            <template slot-scope="scope">
              <img width="50" :src="scope.row.avatar
      ? scope.row.avatar
      : 'https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg'
      " />
            </template>
          </el-table-column>
          <el-table-column label="用户名" prop="user_name"></el-table-column>
          <el-table-column label="昵称" prop="nickname"></el-table-column>
          <el-table-column label="手机号" prop="phone"></el-table-column>
          <el-table-column label="注册时间" prop="created_at"></el-table-column>
        </el-table>

        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
          :page-size="douyin_user_params.per_page" :current-page="douyin_user_params.page"
          @current-change="onPageChange">
        </el-pagination>
      </el-tab-pane>
    </el-tabs>
    <el-dialog :visible.sync="show_dialog_list" width="300px" title="授权小程序">
      <iframe style="border: none" width="300px" height="300px" v-bind:src="inframe"></iframe>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config"
import UE from "@/components/ueditor"
import { color } from "echarts";
export default {
  components: { UE },
  data() {
    return {
      checkList: [],
      isSubmitDouyinIndexData: true,
      setUpInnerUserList: [],
      selectInnerUser: [],
      link: "",
      show_dialog_list: false,
      inframe: "",
      website_id: "",
      activeName: "setting",
      douyinIndexParam: {
        app_name: "",
        nav_setting: {
          selected: 0
        }
      },
      setting: {

      },
      defaultArr: [],
      active_name: "global",
      setting_member_menu: {
        value: [],
        status: 1,
      },
      setting_xinxi: {
        value: [],
        status: 1,
      },
      setting_nav: {
        value: [],
        status: 1,
      },
      new_house_menu_setting: {},
      ueditor: {
        // agreement: {
        //   initialFrameWidth: "100%",
        //   autoHeightEnabled: false,
        //   initialFrameHeight: 500, // 高度
        // },
        // aprivacy: {
        //   initialFrameWidth: "100%",
        //   autoHeightEnabled: false,
        //   initialFrameHeight: 500, // 高度
        // }
        // ,
        config: {
          initialFrameWidth: "100%",
          autoHeightEnabled: false,
          initialFrameHeight: 500, // 高度
        }
      },
      typeList: [
        {
          name: 'T+ERP版',
          value: 1
        },
        {
          name: 'T+新房版',
          value: 2
        }
      ],
      mini_version: 1,
      nav_menu_setting: {
        value: [],
        status: 1,
      },
      broker_menu_setting: {
        value: [],
        status: 1,
      },
      setting_company_form: {
        app_company: "",
        app_address: "",
        app_email: ""
      },
      douyin_user_params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      tableData: [],
      innerUserList: []
    }
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
    configagreement() {
      return {

      }
    }
    ,
    configaprivacy() {
      return {
        initialFrameWidth: "100%",
        autoHeightEnabled: false,
        initialFrameHeight: 500, // 高度
      }
    }
    ,
    configperson_protection() {
      return {
        initialFrameWidth: "100%",
        autoHeightEnabled: false,
        initialFrameHeight: 500, // 高度
      }
    },
    website_info() {
      return this.$store.state.website_info
    }
  },
  mounted() {
    let website_id = this.$route.query.website_id || localStorage.getItem("website_id")
    this.website_id = website_id

    this.getCompanySetting()
  },
  methods: {
    checkboxChange(e, sub) {
      // console.log(e);
      let arr = []
      e.forEach(item => {
        sub.option.forEach(item2 => {
          if (item == item2.name) {
            arr.push(item2.value)
          }
        })
      })
      let newArr = Array.from(new Set(arr))
      this.douyinIndexParam[sub.name] = newArr.join(',')
    },
    tabClick(e) {
      if (e._props.name == 'broker_menu') {
        this.isSubmitDouyinIndexData = false
        this.setInnerUserList()
        this.getInnerUserList()
      } else {
        this.isSubmitDouyinIndexData = true
      }
    },
    async setInnerUser() {
      let ids = this.selectInnerUser.join(',');
      if (this.selectInnerUser.length > 0) {
        await this.sendSetInnerUser(ids)
        this.setInnerUserList()
      }
      this.submitDouyinIndexData()
    },
    changeInnerUser(e) {
      this.selectInnerUser = e
    },
    async setInnerUserList() {
      const res = await this.$http.setInnerUserListAPI()
      if (res.status == 200) {
        this.setUpInnerUserList = res.data
        const ids = res.data.map(item => item.id)
        this.selectInnerUser = ids
      }
    },
    async sendSetInnerUser(ids) {
      const res = await this.$http.setInnerUserAPI({ ids })
      if (res.status == 200) {
        // this.$message({
        //   message: '设置成功！',
        //   type: 'success'
        // });
      }
    },
    async getInnerUserList() {
      const res = await this.$http.getInnerUserListAPI()
      if (res.status == 200) {
        this.innerUserList = res.data
      }
    },
    getCompanySetting() {
      let params = {
        mini_version: 1
      }
      if (this.website_info.open_mini_build_program == 1) {
        params.mini_version = 2
      }
      this.$http.getTtCompanySetting(params).then(res => {
        if (res.status == 200) {
          if (!Array.isArray(res.data)) {
            this.setting_company_form = res.data
          }

        }
      })
    },
    saveCompanyInfo() {
      let obj = {
        app_email: "请填写邮箱",
        app_address: "请填写公司地址",
        app_company: "请填写公司名称"
      }
      let params = Object.assign({}, this.setting_company_form)
      for (const key in params) {
        if (!params[key]) {
          this.$message.warning(obj[key])
          return
        }
      }

      this.$http.saveTtCompanySetting(params).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || '保存成功')
        }
      })
    },
    getSetting() {
      let params = {
        mini_version: 1
      }
      if (this.website_info.open_mini_build_program == 1) {
        params.mini_version = 2
      }
      this.$http.getTtSetting(params).then(res => {
        if (res.status == 200) {
          if (this.website_info.open_mini_erp_program == 1) {
            this.setting_nav = res.data.nav?.sub.nav_setting || {}
            if (this.setting_nav && this.setting_nav.value) {
              this.setting_nav.value.map((item) => {
                if (item.selected == 1) {
                  this.setting_nav.status = item.value
                }
              })
            }
          }
          if (this.website_info.open_mini_build_program == 1 && res.data.nav_menu) {
            this.nav_menu_setting = res.data.nav_menu.sub.nav_menu_setting
            this.nav_menu_setting.status = res.data.nav_menu?.sub.nav_menu_status?.value
          }
          if (res.data.broker_menu) {
            this.broker_menu_setting = res.data.broker_menu.sub?.broker_menu_setting
            // this.broker_menu_setting.status = res.data.nav_menu?.sub?.nav_menu_status?.value
          }
          this.setting_xinxi = res.data.xinxi?.sub.xinxi_setting || {}
          this.setting_member_menu = res.data.member_menu?.sub.member_menu_setting || {}
          // this.new_house_menu_setting = res.data.new_house_menu.sub.new_house_menu_setting
          this.setting = res.data
          console.log(this.setting, 'setting--');
          this.setCheckboxData()
          this.setParams(this.setting)
        }
      })
    },
    // 兼容checkbox
    setCheckboxData() {
      let obj = {}, newKey = ''
      Object.keys(this.setting).forEach(key => {
        Object.keys(this.setting[key]).forEach(key2 => {
          if (this.setting[key][key2] == '全局配置') {
            obj = this.setting[key].sub
            newKey = key
          }
        })
      })
      Object.keys(obj).forEach(key => {
        if (obj[key].type == 'checkbox') {
          let arr = [];
          let valueArr = obj[key].value.split(',')
          valueArr.forEach(item => {
            obj[key].option.forEach(item2 => {
              if (item == item2.value) {
                arr.push(item2.name)
                let isItem = this.checkList.find(f => f == item2.name)
                if (!isItem) {
                  this.checkList.push(item2.name)
                }
              }
            })
          })
          this.$set(obj[key], "defaultArr", arr)
          // obj[key].defaultArr = arr
        }
      })
      this.setting[newKey].sub = obj
    },
    setParams(setting) {
      for (const key in setting) {
        if (setting[key].sub) {
          for (const j in setting[key].sub) {
            if (typeof (setting[key].sub[j]["value"]) == "number") {
              setting[key].sub[j]["value"] = setting[key].sub[j]["value"] + ''
            }
            this.$set(this.douyinIndexParam, setting[key].sub[j]["name"], setting[key].sub[j]["value"])
          }
        }
      }
    },
    getLink() {
      if (this.website_info.open_mini_erp_program == 1) {
        // let href = window.location.href
        let href = location.origin + location.pathname + "@@@@/crm_customer_business_setting?website_id=" + this.$route.query.website_id
        href += '@@@@@@from@@@@@@tDouyinShouquan'
        href = href.replace("#", "@@@@")
        this.$http.getTDouyinLink(href).then((res) => {
          const url = res.data
          if(res.status==200){
            window.open(url)
          }else{
            console.log(res);
          }
          // // this.show_dialog_list = true
          // // this.inframe = url
        })
      } else if (this.website_info.open_mini_build_program == 1) {
        let href = location.origin + location.pathname + "@@@@/crm_customer_business_setting?website_id=" + this.$route.query.website_id
        href += '@@@@@@from@@@@@@xinfangDouyinShouquan'
        href = href.replace("#", "@@@@")
        this.$http.getXinfangDouyinLink(href).then((res) => {
          const url = res.data
          window.open(url)
          // this.show_dialog_list = true
          // this.inframe = url
        })
      }

    },
    handleClick(e) {
      if (e.name == 'tt_setting') {
        this.getSetting()
      } else if (e.name == 'setting') {
        this.getCompanySetting()
      } else if (e.name == 'user') {
          this.$goPath("configure_user_list");
          // this.getTableData()
      }
    },
    onPageChange(e) {
      this.douyin_user_params.page = e;
      this.getTableData();
    },
    getTableData() {
      let params = Object.assign({}, this.douyin_user_params)
      this.$http.getDouyinUser(params).then(res => {
        if (res.status == 200) {
          this.tableData = res.data.data
          this.total = res.data.total
          // this.$message.success('保存成功')
        }

      })
    },
    submitDouyinIndexData() {
      console.log(this.douyinIndexParam, 'douyinIndexParam');
      let params = Object.assign({}, this.douyinIndexParam)
      if (this.website_info.open_mini_erp_program == 1) {
        params.nav_setting = Object.assign([], this.setting_nav.value)
        params.nav_menu_setting = Object.assign([], this.nav_menu_setting.value)
        params.xinxi_setting = Object.assign([], this.setting_xinxi.value)
        params.member_menu_setting = Object.assign([], this.setting_member_menu.value)
        params.broker_menu_setting = Object.assign([], this.broker_menu_setting.value)
        if (this.setting_nav.status == 1) {
          params.nav_setting[this.setting_nav.status - 1].selected = 1
          if (params.nav_setting.length > 1) {
            params.nav_setting[this.setting_nav.status].selected = 0;
          }
          // params.nav_setting[this.setting_nav.status].selected = 0
        }
        if (this.setting_nav.status == 2) {
          params.nav_setting[this.setting_nav.status - 1].selected = 1
          params.nav_setting[0].selected = 0
        }
      }

      // params.new_house_menu_setting = Object.assign([], this.new_house_menu_setting.value)
      params.mini_version = 1
      if (this.website_info.open_mini_build_program == 1) {
        params.mini_version = 2
      }

      this.$http.saveTtSetting(params).then(res => {
        if (res.status == 200) {
          this.$message.success('保存成功')
        }

      })

    },
    handlePictureCardPreview(e) {
      console.log(e);
    },
    beforeUpload() {

    },
    handleUploadSuccess(e, type, cate) {
      console.log(e, type, cate);
      if (cate) {
        this.$set(type, "bg_pic", e.url)
      } else {
        this.douyinIndexParam[type] = e.url
      }


    },
    inputUe(e, type) {
      this.douyinIndexParam[type] = e.content;
    },
    radioChange() {
      this.$forceUpdate()
    },
    new_house_radio_change(e, type) {
      let other = {}
      if (type.name == 'zxzx') {
        other = this.new_house_menu_setting.value.find(item => item.name == 'yykf')
      }
      if (type.name == 'yykf') {
        other = this.new_house_menu_setting.value.find(item => item.name == 'zxzx')
      }
      if (type.name == "zxzx" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个")
        type.status = 0
        this.$forceUpdate()
      }
      if (type.name == "yykf" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个")
        type.status = 0
        this.$forceUpdate()
      }
    },
    broker_menu_radio_change(e, type) {
      let other = {}
      if (type.identify == 'zxzx') {
        other = this.broker_menu_setting.value.find(item => item.identify == 'yykf')
      }
      if (type.identify == 'yykf') {
        other = this.broker_menu_setting.value.find(item => item.identify == 'zxzx')
      }
      if (type.identify == "zxzx" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个")
        type.status = 0
        this.$forceUpdate()
      }
      if (type.identify == "yykf" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个")
        type.status = 0
        this.$forceUpdate()
      }
    }

  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-input__inner {
  // width: 100px !important;
  height: auto !important;

}

.el-upload {
  overflow: hidden;

  img {
    width: 146px;
    height: 146px;
    object-fit: cover;
  }
}

.img-input {
  white-space: nowrap;
}

.w100 {
  width: 100px;
}

.w240 {
  width: 240px;
}

.radio_small {
  ::v-deep .el-radio__label {
    font-size: 12px;
  }
}

.btns {
  text-align: center;
  position: fixed;
  top: 500px;
  right: 80px;
  z-index: 1000;

  .submit {
    padding: 10px 40px;
    background: #ff5b6a;
    color: #fff;
    cursor: pointer;
    border-radius: 40px;
  }
}
</style>