<template>
  <div>
    <div class="table-top-box div row">
      <div class="t-t-b-left div row flex-1">
        <el-cascader
          placeholder="请选择部门"
          style="width: 210px; margin-right: 16px"
          v-model="params.department_id"
          clearable
          :options="department_list"
          :props="{
            value: 'id',
            label: 'name',
            children: 'subs',
            emitPath: false,
          }"
          @change="onPageChange(1)"
        ></el-cascader>
        <el-input
          style="width: 200px"
          placeholder="请输入姓名"
          v-model="params.user_name"
        ></el-input>
        <el-button
          style="margin: 0 16px"
          type="primary"
          class="el-icon-search"
          @click="onPageChange(1)"
          >搜索</el-button
        >
        <!-- <el-date-picker
          style="width: 210px; margin-right: 16px"
          v-model="p_time"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="changeTimeRange"
        >
        </el-date-picker> -->
        <!-- <el-button type="primary" v-if="false"> 数据导出</el-button> -->
      </div>
      <div class="t-t-b-right div row f-c">
        <span class="f f1"></span>第一 <span class="f f2"></span>第二
        <span class="f f3"></span>第三
      </div>
    </div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle"
      :cell-style="cellStyle"
      @sort-change="onSortChange"
    >
      <el-table-column label="成员" v-slot="{ row }" width="200px" fixed="left">
        <div class="name-box-cy div row">
          <img
            class="left"
            v-if="row.wx_work_avatar"
            :src="row.wx_work_avatar"
            alt=""
          />
          <div class="left" v-else>{{ row.user_name[0] }}</div>
          <div class="right">
            <div class="c">{{ row.user_name }}</div>
            <div class="b">
              {{ row.department ? row.department[0].name : "--" }}
            </div>
          </div>
        </div>
      </el-table-column>
      <el-table-column
        label="客户总量"
        align="center"
        sortable="custom"
        width="180px"
        prop="khzl_num"
      ></el-table-column>
      <el-table-column
        label="认领"
        align="center"
        sortable="custom"
        prop="rl_num"
      ></el-table-column>
      <el-table-column
        label="录入"
        align="center"
        width="180px"
        sortable="custom"
        prop="lr_num"
      ></el-table-column>
      <el-table-column
        label="跟进次数"
        align="center"
        sortable="custom"
        prop="gj_num"
        width="110px"
      ></el-table-column>
      <el-table-column
        label="跟进客户"
        align="center"
        sortable="custom"
        prop="hfkh_num"
        width="110px"
      ></el-table-column>
      <el-table-column
        label="带看记录"
        align="center"
        width="110px"
        sortable="custom"
        prop="dkjl_num"
      >
      </el-table-column>
      <el-table-column
        label="标无效"
        align="center"
        width="110px"
        sortable="custom"
        prop="bwx_num"
      ></el-table-column>
      <el-table-column
        label="转交客户"
        align="center"
        width="110px"
        sortable="custom"
        prop="zjkh_num"
      ></el-table-column>
      <!-- <el-table-column
        label="合并客户"
        align="center"
        sortable="custom"
        prop="hbkh_num"
        :index="13"
      ></el-table-column> -->
      <el-table-column
        label="转公数量"
        align="center"
        sortable="custom"
        width="110px"
        prop="zgsl_num"
      ></el-table-column>
      <el-table-column
        label="最后回访时间"
        align="center"
        prop="zhhfsj"
        v-slot="{ row }"
        width="200px"
      >
        <template v-if="row.zhhfsj">
          <el-tooltip class="item" effect="light" placement="top">
            <div slot="content" style="max-width: 300px">
              {{ row.zhhfsj }}
            </div>
            <div class="item_c">
              {{ row.zhhfsj }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="最近回访记录"
        align="center"
        prop="zjhfjl"
        width="220px"
        v-slot="{ row }"
      >
        <template v-if="row.zjhfjl">
          <el-tooltip class="item" effect="light" placement="top">
            <div slot="content" style="max-width: 300px">
              {{ row.zjhfjl }}
            </div>
            <div class="item_c w200">
              {{ row.zjhfjl }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="最近回访时间"
        align="center"
        prop="zjhfsj"
        width="110px"
        v-slot="{ row }"
      >
        <template v-if="row.zjhfsj">
          <el-tooltip class="item" effect="light" placement="top">
            <div slot="content" style="max-width: 300px">
              {{ row.zjhfsj }}
            </div>
            <div class="item_c w90">
              {{ row.zjhfsj }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
 
      <el-table-column
        label="带看时间"
        align="center"
        width="120px"
        prop="dksj"
        v-slot="{ row }"
      >
        <template v-if="row.dksj">
          <el-tooltip class="item" effect="light" placement="top">
            <div slot="content" style="max-width: 300px">
              {{ row.dksj }}
            </div>
            <div class="item_c">
              {{ row.dksj }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="成交审批"
        align="center"
        sortable="custom"
        prop="cjsp_num"
        width="110px"
      ></el-table-column>

      <el-table-column
        label="A级"
        align="center"
        sortable="custom"
        prop="dja_num"
      ></el-table-column>
      <el-table-column
        label="B级"
        align="center"
        sortable="custom"
        prop="djb_num"
      ></el-table-column>
      <el-table-column
        label="C级"
        align="center"
        sortable="custom"
        prop="djc_num"
      ></el-table-column>
      <el-table-column
        label="查看电话"
        align="center"
        sortable="custom"
        prop="ckdh_num"
        width="110px"
      ></el-table-column>
      <el-table-column
        label="维护资料"
        align="center"
        width="110px"
        sortable="custom"
        prop="whzl_num"
      ></el-table-column>
      <el-table-column
        label="更新标签"
        align="center"
        sortable="custom"
        width="110px"
        prop="gxbq_num"
      ></el-table-column>

    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="total,prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        start_date: "",
        end_date: "",
        department_id: "",
        user_name: "",
        date_type: 1
      },
      p_time: "",
      tableData: [],
      is_table_loading: false,
      department_list: [],
    };
  },
  props: {
    house_params: {
      type: [Object],
      default: () => { return { page: 1 } },
    }
  },
  watch: {
    "house_params.page": {
      handler(val) {
        console.log(val, "111");
        if (val) {
          this.params.page = val

        } else {
          this.params.page = 1
        }
        this.$forceUpdate()


      },
      immediate: true
    }
  },
  mounted() {
    this.getCrmIntelligentRateList();
    this.getCrmDepartmentList();
  },
  methods: {
    /* eslint-disable */

    // 自定义排序
    onSortChange({ column, prop, order }) {
      let sort = '',
        sortArr = []
      if (column && column.property) {
        sortArr = column.property.split('_')
        if (sortArr.length > 1) {
          sort = sortArr[0]
        }
      }
      // this.params.type = column.index;
      if (order === "descending") {
        this.params.order = sort + "_down";
        // 大->小
      } else if (order === "ascending") {
        this.params.order = sort + "_up";
      } else {
        this.params.order = '';
      }
      this.$emit("sortData")
      // this.params.page = 1;
      // this.getCrmIntelligentRateList();
    },
    // 获取部门列表
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column && column.property) {
        let idxArr = column.property.split('_'), idx = ''
        if (idxArr.length > 1) {
          idx = idxArr[0]
          return this.setStyle(row[idx].rank);
        }
      }
    },
    setStyle(rank) {
      if (rank === 1) {
        return `background-color:#ffb88d;`;
      }
      if (rank === 2) {
        return `background-color:#bedcff;`;
      }
      if (rank === 3) {
        return `background-color:#ffeba5;`;
      }
    },
    changeTimeRange(e) {
      this.params.start_date = e ? e[0] : "";
      this.params.end_date = e ? e[1] : "";
      this.params.page = 1;
      this.getCrmIntelligentRateList();
    },

    getCrmIntelligentRateList() {
      this.is_table_loading = true;
      let params = Object.assign({}, this.params, this.house_params)
      console.log(params);
      this.$http
        .getCrmIntelligentRateListCrm({ params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            console.log(res.data.data,'表格');
            this.tableData = res.data.data;
            this.params.total = res.data.total;
            if (params.page == 1) {
              this.$emit("getDataOk", res)
            }
          }
        });
    },
    onPageChange(e) {
      this.params.page = this.house_params.page;
      this.$emit("getData", e)
      // this.getCrmIntelligentRateList();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-table .cell {
  line-height: 30px;
}
.f-c {
  color: #2e3c4e;
  font-size: 14px;
  display: flex;
  align-items: center;
  .f {
    width: 20px;
    height: 8px;
    margin: 0 10px;
    &.f1 {
      background: #ffb88d;
    }
    &.f2 {
      background: #bedcff;
    }
    &.f3 {
      background: #ffeba5;
    }
  }
}
.name-box-cy {
  align-items: center;
  width: 180px;
  .left {
    margin-right: 7px;
    background: #2d84fb;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: #fff;
    line-height: 24px;
    text-align: center;
    flex-shrink: 0;
    font-size: 14px;
  }
  .right {
    font-size: 12px;
    .c {
      color: #2e3c4e;
    }
    .b {
      color: #8a929f;
    }
  }
}
.item_c {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  &.w200 {
    width: 200px;
  }
  &.w90 {
    width: 90px;
  }
}
</style>
