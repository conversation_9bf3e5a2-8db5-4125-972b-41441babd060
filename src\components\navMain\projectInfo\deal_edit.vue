<template>
  <el-container>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->
    <el-main>
      <el-form label-width="100px" v-model="order_list">
        <el-form-item label="成交单号">
          <el-input
            style="width:400px"
            v-model="order_list.contract_no"
          ></el-input>
        </el-form-item>
        <el-form-item label="产权地址">
          <el-input
            style="width:400px"
            v-model="order_list.property_right_address"
          ></el-input>
        </el-form-item>
        <el-form-item label="成交金额">
          <el-input
            style="width:400px"
            v-model="order_list.deal_amount"
          ></el-input>
          <i>万元</i>
        </el-form-item>
        <el-form-item label="成交时间">
          <el-input style="width:400px" v-model="order_list.deal_at"></el-input>
        </el-form-item>
        <el-form-item label="佣金分成">
          <el-input
            style="width:400px"
            v-model="order_list.brokerage_amount"
          ></el-input
          ><i>元</i>
        </el-form-item>
        <el-form-item label="扫码案场">
          <el-select
            filterable
            remote
            reserve-keyword
            v-model="scan_code_user_name"
            placeholder="请输入手机号"
            :loading="scan_code_user_loading"
            :remote-method="getScanData"
            @change="onScan"
          >
            <el-option
              v-for="item in client_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="跟进案场">
          <el-select
            filterable
            remote
            reserve-keyword
            v-model="follow_up_user_name"
            placeholder="请输入手机号"
            :loading="follow_up_user_loading"
            :remote-method="getScanData"
            @change="onFollow"
          >
            <el-option
              v-for="item in client_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input style="width:400px" v-model="order_list.remark"></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button type="primary" @click="onSubmit">编辑</el-button>
        </el-form-item>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
export default {
  name: "deal_edit",
  data() {
    return {
      customer_id: "",
      order_list: {
        id: "",
        customer_reported_id: "",
        contract_no: "",
        property_right_address: " ",
        deal_amount: "",
        deal_at: "",
        brokerage_amount: "",
        scan_code_user_id: "",
        follow_up_user_id: "",
        remark: "",
      },
      scan_code_user_loading: false,
      follow_up_user_loading: false,
      client_list: [],
      scan_code_user_name: "",
      follow_up_user_name: "",
    };
  },
  mounted() {
    this.customer_id = this.$route.query.id;
    this.getQueryOrder();
  },
  methods: {
    getQueryOrder() {
      this.$http.QueryOrder(this.customer_id).then((res) => {
        if (res.status === 200) {
          this.order_list = res.data;
          this.scan_code_user_name =
            res.data.sc_name || res.data.sc_nickname || res.data.sc_user_name;
          this.follow_up_user_name =
            res.data.fu_name || res.data.fu_nickname || res.data.fu_user_name;
        }
      });
    },
    onSubmit() {
      this.order_list.customer_reported_id = this.customer_id;
      this.$http.editOrder(this.order_list).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "编辑成功",
            type: "success",
          });
          this.getQueryOrder();
        }
      });
    },
    // 扫码案场搜索
    getScanData(query) {
      this.scan_code_user_loading = true;
      this.$http.searchUserByPhone(query).then((res) => {
        this.scan_code_user_loading = false;
        if (res.status === 200) {
          this.client_list = res.data.data.map((item) => {
            return {
              id: item.id,
              name: item.name || item.nickname || item.user_name,
            };
          });
        }
      });
    },
    onScan(e) {
      this.order_list.scan_code_user_id = e;
    },
    onFollow(e) {
      this.order_list.follow_up_user_id = e;
    },
    goBack() {
      this.$router.back();
    },
  },
};
</script>

<style>
/* .back {
	position: absolute;
	top: 78px;
	left: 240px;
	z-index: 10;
} */
/* .el-main {
	padding-top: 50px;
} */
</style>
