<template>
    <tDropdown @visible-change="onVisibleChange" :props="{label: 'name'}" 
    :loading="loading" 
    :filter-input-placeholder="filterInputPlaceholder"
    :datas="list" height="300px" v-bind="$attrs" v-on="$listeners">
        <slot></slot>
    </tDropdown>
</template>

<script>
import tDropdown from './index.vue';
export default {
    name: 'tMemberDropdown',
    components: {
        tDropdown
    },
    props: {
        datas: {type: Array, default: ()=>[]},
        filterInputPlaceholder: {type: String, default: "请输入同事姓名"}
    },
    data(){
        return {
            loading:false,
            isDataLoaded: false,
            list: []
        }
    },
    watch: {
        datas: {
            handler(d){
                this.list = d;
            },
            immediate: true
        }
    },
    methods: {
        //初始化菜单数据
        initMenuData(){
            if(!this.isDataLoaded){
                this.isDataLoaded =  this.list.length > 0;
            }
            if(!this.isDataLoaded){
                this.isDataLoaded = true;
                this.loadData();
            }
        },
        //获取菜单数据
        async loadData(keyword = ''){
            this.loading = true;
            try{
                const res = await this.$http.getColleagueDetailsList({ keywords: keyword });
                if(res.status == 200){
                    this.list = res.data || [];
                }
            }catch(e){
                console.error(e);
            }
            this.loading = false;
        },
        //菜单显示隐藏事件
        onVisibleChange(bool){
            //首次加载数据
            bool && this.initMenuData();
            this.$emit('visible-change', bool);
        },
    },
}
</script>

<style scoped lang="scss">

</style>