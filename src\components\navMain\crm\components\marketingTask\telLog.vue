<template>
  <div>
    <div class="content-box-crm">
      <div class="table-top-box div row">
        <div class="t-t-b-left div row"></div>
        <div class="t-t-b-right div row">
          <el-button type="primary" @click="add"> 添加号码</el-button>
        </div>
      </div>
      <div>
        <el-table
          v-loading="is_table_loading"
          :data="telData"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <el-table-column prop="id" label="ID"></el-table-column>

          <el-table-column prop="phone" label="电话号码"></el-table-column>
          <el-table-column prop="created_at" label="创建时间"></el-table-column>
          <el-table-column prop="updated_at" label="更新时间"></el-table-column>
          <el-table-column label="操作" v-slot="{ row }">
            <el-link @click="edit(row)">编辑</el-link>

            <el-popconfirm
              title="确定删除吗？"
              style="margin: 0 10px"
              @onConfirm="cancelTel(row)"
            >
              <el-link slot="reference" type="danger">删除</el-link>
            </el-popconfirm>
          </el-table-column>
        </el-table>

        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="params.per_page"
          :current-page="params.page"
          @current-change="onPageChange"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      width="500px"
      append-to-body
      :visible.sync="is_showDia"
      :title="telTitle"
    >
      <div v-if="is_showDia">
        <el-form label-width="120px">
          <el-form-item label="电话号码">
            <el-input
              :type="isAdd ? 'textarea' : 'text'"
              v-model="task_form.phones"
              style="width: 220px"
            ></el-input>
            <el-tooltip
              v-if="isAdd"
              class="item"
              effect="light"
              placement="right"
            >
              <div slot="content" style="max-width: 300px">
                可以添加多个号码 请用英文逗号分隔
              </div>
              <i
                style="margin-left: 10px; color: #f56c6c"
                class="el-icon-info"
              ></i>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button v-loading="add_loading" type="primary" @click="confirmAdd"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    id: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      telData: [],
      is_table_loading: false,
      is_table_loading1: false,
      params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      is_showDia: false,
      task_form: {
        phones: '',
        library_id: ''
      },
      add_loading: false,
      isAdd: false,
      telTitle: '',
      library_id: ""

    }
  },
  computed: {

  },
  created() {
    this.task_form.library_id = this.id
    this.getList()

  },
  methods: {
    getList() {
      this.is_table_loading = true
      this.$http.getMaketingTellogList(this.id).then(res => {
        if (res.status == 200) {
          this.telData = res.data.data
          this.total = res.data.total
        }
        this.is_table_loading = false

      }).catch(() => {
        this.is_table_loading = false
      })
    },
    // 添加编辑号码开始
    add() {
      this.isAdd = true
      this.telTitle = "添加号码"
      this.task_form = {
        library_id: this.id,
        phones: ""
      }
      this.is_showDia = true
    },
    edit(row) {
      this.isAdd = false
      this.telTitle = "编辑号码"
      this.task_form = {
        id: row.id,
        library_id: this.id,
        phones: row.phone
      }
      this.is_showDia = true
    },
    addTel() {
      this.add_loading = true
      this.$http.addMarketTellog(this.task_form).then(res => {
        if (res.status == 200) {
          this.$message.success("添加成功")
          this.is_showDia = false
          this.add_loading = false
          this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })

    },
    editTel() {
      this.add_loading = true
      let form = {
        id: this.task_form.id,
        phone: this.task_form.phones,
        library_id: this.task_form.library_id,
      }

      this.$http.editMarketTellog(form).then(res => {
        if (res.status == 200) {
          this.$message.success("修改成功")
          this.is_showDia = false
          this.add_loading = false
          this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })
    },
    cancelTel(row) {
      this.$http.cancelMarketTellog(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success("删除成功")
          this.getList()
        }
      })
    },
    confirmAdd() {
      if (this.isAdd) {
        this.addTel()
      } else {
        this.editTel()
      }
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getList()
    },



  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}
.content-box-crm {
  padding: 0;
}
.ml30 {
  margin-left: 30px;
}
.padd10 {
  padding: 10px 0 40px;
}
.title {
  padding-top: 20px;
  padding-left: 75px;
}
.pay_img {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.pay_tips {
  margin-top: 10px;
  text-align: center;
  color: #6bcc03;
  font-size: 28px;
}
</style>