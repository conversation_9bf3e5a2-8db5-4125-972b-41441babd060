<template>
    <div class="base-info">
        <div class="base-info-item">
            <span class="label">客户名称：</span>
            <span class="value">{{params.name}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">客户手机号：</span>
            <span class="value">{{params.phone}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">成交人：</span>
            <span class="value">{{params.admin_id}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">成交时间：</span>
            <span class="value">{{params.cjrq}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">项目名称：</span>
            <span class="value">{{params.project}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">房号：</span>
            <span class="value">{{params.room_number}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">购买方式：</span>
            <span class="value">{{params.buy_type}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">客户住址：</span>
            <span class="value">{{params.address}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">佣金点位：</span>
            <span class="value">{{params.proportion}}%</span>
        </div>
        <div class="base-info-item">
            <span class="label">面积：</span>
            <span class="value">{{params.mianji}} m²</span>
        </div>
        <div class="base-info-item">
            <span class="label">单价：</span>
            <span class="value">{{params.danjia}} 元</span>
        </div>
        <div class="base-info-item">
            <span class="label">成交总额：</span>
            <span class="value">{{params.amount}} 元</span>
        </div>
        <div class="base-info-item">
            <span class="label">渠道名称：</span>
            <span class="value">{{params.channel_name}}</span>
        </div>
        <div class="base-info-item">
            <span class="label">渠道分成：</span>
            <span class="value" v-if="params.channel_proportion">{{params.channel_proportion}}%</span>
        </div>
        <div class="base-info-item">
            <span class="label">渠道税点：</span>
            <span class="value" v-if="params.channel_rate">{{params.channel_rate}}%</span>
        </div>
    </div>  
</template>
  
<script>
export default {
    props: {
        reportData: { type: Object, default: () => {return {}} }
    },
    data() {
        return {
            params: {},
            buyTypes: [
                { label: '按揭', value: 1 },
                { label: '抵押', value: 2 },
                { label: '全款', value: 3 },
            ],
        };
    },
    watch: {
        reportData: {
            handler(data) {
                this.params = { 
                    id: data.report_id || 0,
                    admin_id : data.admin?.user_name ?? '', 
                    name: data.customer?.name ?? '', 
                    phone: data.customer?.phone ?? '', 
                    amount: data.deal?.amount ?? '', 
                    cjrq: data.deal?.cjrq ?? '', 
                    proportion: data.deal?.proportion ?? '', 
                    project: data.deal?.project ?? '', 
                    mianji: data.deal?.mianji ?? '', 
                    danjia: data.deal?.danjia ?? '',
                    room_number: data.deal?.room_number ?? '',

                    buy_type: data.deal?.buy_type || '',
                    address: data.deal?.address ?? '',
                    channel_name: data.deal?.channel_name ?? '',
                    channel_proportion_type: data.deal?.channel_proportion_type || 2, //2减，1加
                    channel_proportion: data.deal?.channel_proportion || '',
                    channel_rate: data.deal?.channel_rate || '',
                }

                if(this.params.buy_type){
                    const item  = this.buyTypes.find(e => e.value === this.params.buy_type);    
                    item && (this.params.buy_type = item.label); 
                }

                this.params.channel_proportion === '0.00' && (this.params.channel_proportion = '')
                this.params.channel_rate === '0.00' && (this.params.channel_rate = '')

                if(this.params.channel_proportion){
                    if(this.params.channel_proportion_type == 2){
                        this.params.channel_proportion = '-'+this.params.channel_proportion;
                    }else{
                        this.params.channel_proportion = '+'+this.params.channel_proportion;
                    }
                }


            },
            immediate: true
        }
    },
    methods: {
        
    },
};
</script>
  
<style lang="scss" scoped>
.base-info{
   display: flex;
   flex-wrap: wrap;
   .base-info-item{
        display: flex;
        height: 40px;
        width: 340px;
        align-items: center;
       .label{
            display: inline-block;
            width: 120px;
            text-align: right;
            margin-right: 10px;
            font-size: 14px;
            color: #9c9c9c;
        }
       .value{
            flex: 1;
            font-size: 14px;
            color: #3c3c3c
        }
    }
}
</style>