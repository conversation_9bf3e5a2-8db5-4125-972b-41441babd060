<template>
  <div class="Circulation_List">
    <div class="Circulation_search">
      <div class="search">
        <div class="search_left">
          <div>
            <div>
              <el-input placeholder="请输入标题" v-model="search_data.title" class="input-with-select" prefix-icon="el-icon-search">
              </el-input>
            </div>
            <div class="select">
              <el-select v-model="search_data.red_packet_status" slot="prepend" placeholder="红包是否开启">
                <el-option label="全部" value=''></el-option>
                <el-option label="已开启" :value="1"></el-option>
                <el-option label="未开启" :value="0"></el-option>
              </el-select>
              <el-select v-model="search_data.status" slot="prepend" placeholder="是否禁用海报">
                <el-option label="全部" value=""></el-option>
                <el-option label="已禁用" :value="0"></el-option>
                <el-option label="已启用" :value="1"></el-option>
              </el-select>
            </div>
            <div>
              <el-button type="primary" @click="search">搜索</el-button>
            </div>
          </div>
        </div>
          <div class="search_left_centre">
            <div>
              <i class="el-icon-location-outline" :class="color"></i>
              <span :class="color" @click="list">列表模式</span>
            </div>
            <div>
              <i class="el-icon-location-outline" :class="color1"></i>
              <span :class="color1" @click="card">卡片模式</span>
            </div>
            <div>
              <el-button type="warning" @click="Add_circulation">新增传阅</el-button>
            </div>
          </div>
      </div>
      <div class="Circulation_list" v-show="show1">
        <el-table :data="tableData1" border style="width: 100%" :header-cell-style="{
          background: '#DDE1E9',
          color: '#2E3C4E',
        }">
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column prop="id" label="ID" align="center" width="80px">
          </el-table-column>
          <el-table-column prop="title" label="标题" align="center">
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" align="center">
            <!-- <i class="el-icon-location-outline"></i>点击查看 -->
          </el-table-column>
          <el-table-column prop="red_packet_status" label="红包是否开启" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.red_packet_status == 0 ? 'danger' : 'success'">{{
                scope.row.red_packet_status === 0 ? "未开启" : "已开启" }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="是否禁用海报" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status == 0 ? 'danger' : 'success'">{{ scope.row.status === 0 ? "已禁用" : "已启用"
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="start_time_show" label="开始时间" align="center">
          </el-table-column>
          <el-table-column prop="end_time_show" label="结束时间" align="center">
          </el-table-column>
          <el-table-column prop="address" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="Editorial(scope.row.id)">修改</el-button>
              <el-button type="text" @click="delet(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="paging">
          <el-pagination background layout="pager" :total="res.total" @current-change="handleCurrentChange"
            :current-page="res.current_page" :page-size="res.per_page"> </el-pagination>
        </div>
      </div>
      <div class="Circulation_card flex-row items-center" v-show="show">
        <div class="card" v-for="item in tableData1" :key="item.id">
          <div class="card_top">
            <div class="fiche">
              <div class="fiche_top">
                <img :src="item.bg_pic" alt="">
              </div>
              <div class="fiche_bottom">
                <span>{{ item.title }}</span>
                <div class="fiche_bottom_figure">
                  <span>2023.03.08 12:00</span>
                  <span class="figure_span">...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      input1: "",
      select: '',
      select1: '',
      color: 'c1',
      color1: 'c2',
      show1: true,
      show: false,
      tableData1: [],
      res: [],
      value1: false,
      value2: false,
      search_data: {
        title: '',
        red_packet_status: "",
        status: '',
        page:"",
        per_page:'',
      }
    }
  },
  methods: {
    list() {
      this.color = "c1"
      this.color1 = "c2"
      this.show1 = true
      this.show = false
    },
    card() {
      this.color = "c2"
      this.color1 = "c1"
      this.show1 = false
      this.show = true
    },
    //模糊搜索,列表显示,分页获取
    search() {
      console.log(this.search_data.status);
      let params = Object.assign({},this.search_data)
      if(params.red_packet_status===""){
        delete params.red_packet_status
      }
      if(params.status===""){
        delete params.status
      }
      this.$http.Circular_Fuzzy_Search(params).then(res => {
        console.log(res,12);
        var {data} = res.data
        if (res.status == 200) {
          console.log(res.data);
          this.res.total=res.data.total
          this.res.per_page=res.data.per_page
          this.res.current_page=res.data.current_page
          this.tableData1=data
        }
      })
    },
    //分页获取
    handleCurrentChange(val) {
      this.search_data.page=val
      this.search()
     
    },
    //跳转到添加传阅页面
    Add_circulation() {
      this.$goPath("add_circularize");
    },
    //跳转修改页面
    Editorial(id) {
      this.$goPath(`Editorial_circulation?id=${id}`)
    },
    //删除传阅
    delet(id) {
      this.$confirm('此操作将永久删除该篇海报, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // console.log(id);
        this.$http.Delete_circulation(id).then(res => {
          console.log(res);
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
          }
          this.CirculationList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
  },
  mounted() {
    this.search()
  }
}
</script>
<style scoped lang="scss">
.Circulation_List {
  background: #f1f4fa;
  margin: -14px;
  padding: 24px;

  .Circulation_search {
    width: 96%;
    height: 900px;
    background-color: #fff;
    border-radius: 4px;
    margin: 0 auto;
    overflow: hidden;

    .search {
      width: 90%;
      height: 70px;
      margin: 20px auto;
      display: flex;
      justify-content: space-between;

      .search_left {
        width: 50%;
        height: 100px;

        div {
          height: 34px;
          // background-color: palegreen;
          display: flex;
          justify-content: space-between;
          margin-top: 10px;

          .select {
            width: 280px;
            height: 100px;
            // background-color: palevioletred;
            margin-right: 50px;

            /deep/.el-input {
              width: 130px;
              height: 34px;
            }

            /deep/.el-input__icon {
              height: 115%;
            }
          }
        }



        .el-button {
          width: 76px;
          height: 40px;
          margin-top: 10px;
          margin-right: 60px;
        }

        .el-dropdown-link {
          cursor: pointer;
          color: #2e3c4e;
        }

        /deep/.el-dropdown {
          font-size: 16px;
        }
      }

        .search_left_centre {
          width: 350px;
          height: 70px;
          // background-color: aqua;
          display: flex;
          justify-content: space-between;
          margin-left: 370px;
          margin-top: 21px;

          span {
            color: #8A929F;
            font-size: 14px;
            cursor: pointer;
          }

          .el-icon-location-outline {
            font-size: 25px;
            color: #8A929F;
            margin-right: 10px;
          }

          .c1 {
            color: #2D84FB;
          }

          .c2 {
            color: #8A929F;
          }

          .el-button {
            width: 140px;
            height: 36px;
            line-height: 0;
          }
        }
    }

    .Circulation_list {
      width: 90%;
      height: 650px;
      // background-color: aquamarine;
      margin: 0 auto;

      .paging {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }

      .el-button {
        width: 65px;
        text-align: center;
        border-color: #2d84fb;
        color: #2d84fb;
      }
    }

    .Circulation_card {
      width: 90%;
      height: 680px;
      border: 1px solid #fff;
      border-top-color: #dde1e9;
      // background-color: #2d84fb;
      margin: 0 auto;
      flex-wrap: wrap;
      overflow: hidden;

      .card {
        width: calc((100% - 100px)/5);
        margin-right: 20px;
        margin-top: 25px;

        &:nth-child(5n) {
          margin-right: 0;
        }

        .card_top {
          overflow: hidden;

          .fiche {
            height: 300px;
            // background-color: #c3c336;
            margin-top: 10px;

            .fiche_top {
              height: 240px;
              background-color: burlywood;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .fiche_bottom {
              width: 200px;
              height: 50px;
              margin-top: 10px;

              // background-color: cadetblue;
              span {
                color: #2e3c4e;
                font-size: 14px;
              }

              .fiche_bottom_figure {
                span {
                  color: #8a929f;
                  font-size: 13px;
                }

                .figure_span {
                  margin-left: 75px;
                }
              }
            }
          }
        }
      }
    }
  }
}</style>