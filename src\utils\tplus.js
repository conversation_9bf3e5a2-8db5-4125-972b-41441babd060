import Utils from './utils.js';
import Validate from './validate.js';
import admSelect from '../components/tplus/tSelect/admSelect.vue';

function findComponentDownward (context, componentName) {
	const childrens = context.$children;
	let children = null;
	if (childrens.length) {
		for (const child of childrens) {
			const name = child.$options.name;
			if (name === componentName) {
				children = child;
				break;
			} else {
				children = findComponentDownward(child, componentName);
				if (children) break;
			}
		}
	}
	return children;
}

export default {
	install(app) {
		app.prototype.Validate = Validate;
		app.prototype.$Utils = Utils;
		app.component('admSelect', admSelect);
		

		app.prototype.$emitPageRefresh = function(compName, options){
			const compObj = findComponentDownward(this.$root, compName);
			if(compObj && compObj.$options.onPageRefresh){
				compObj.$options.onPageRefresh.call(compObj, options)
			}
		};

		app.prototype.$emitPageOpen = function(compName, route){
			const compObj = findComponentDownward(this.$root, compName);
			if(compObj && compObj?.$route?.fullPath === route && compObj.$options.onPageOpen){
				compObj.$options.onPageOpen.call(compObj)
			}
		}

		app.mixin({
			beforeRouteEnter(to, from, next) {
				next(vm => {
					//还原滚动条位置
					try{
						if(to.meta.scrollTop){
							const scrollTop = to.meta.scrollTop;
							const scrollElem = to.meta.scrollElem;
							let maxCount = 100, 
								tryCount = 0,
								timer = setInterval(()=>{
									if(scrollElem.scrollHeight - scrollElem.offsetHeight >= scrollTop){
										scrollElem.scrollTop = scrollTop;
										to.meta.scrollTop = 0;
										clearInterval(timer);
									}else if(++tryCount > maxCount ){
										clearInterval(timer);
									}
								}, 50)
						}else{
							const scrollElem = document.querySelector('.main-scroll') || document.querySelector('main');
							if(scrollElem){
								scrollElem.scrollTop = 0;
							}
						}
					}catch(e){}
				})
			},
			beforeRouteLeave(to, from, next) {
				//记录页面滚动条位置
				try{
					if(from.meta.scrollTop !== undefined){
						const scrollElem = document.querySelector('.main-scroll') || document.querySelector('main');
						if(scrollElem){
							from.meta.scrollElem = scrollElem;
							from.meta.scrollTop = scrollElem.scrollTop;
						}
					}
				}catch(e){}
				next();
			},
		});
	}
}