<template>
    <div>
        <div class="step_box">
            <div class="step_item" :class="{ success: curStep >= ops.step }" v-for="ops in options">
              <div class="line"></div>
              <div class="number">{{ops.step}}</div>
              <div class="title">{{ops.title}}</div>
            </div>
        </div>

        <div class="options_btn">
            <el-form label-width="80px">
              <el-form-item>
                <el-button size="base" style="margin-left: 24px" @click="prevStep()"  v-show="hasPrevStep">
                  <i class="el-icon-arrow-left"></i>上一步
                </el-button>
                <el-button
                  v-show="step < 3"
                  size="base"
                  type="primary"
                  @click="nextStep()"
                  >下一步 <i class="el-icon-arrow-right"></i>
                </el-button>
                <el-button
                  v-show="step === 3"
                  type="primary"
                  size="base"
                  :loading="subing"
                  style="margin-left: 24px"
                  @click="onSubmit"
                  >提交</el-button
                >
                <el-button
                  size="base"
                  style="margin-left: 24px"
                  @click="onCancel"
                >
                  {{ step === 4 ? "返回" : "取消" }}
                </el-button
                >
                <el-button
                  v-show="step === 2"
                  size="base"
                  style="margin-left: 24px"
                  @click="onSkip"
                  >跳过</el-button
                >
              </el-form-item>
            </el-form>
          </div>
    </div>
</template>

<script>
export default {
    name: 'tStepForm',
    props: {
        //定义初始步骤
        step: {type: Number, default: 1},   
        //步骤选项数据
        options: {type: Array, default: ()=>[]},  
        //是否（最后一步）有结果步骤
        hasResultStep: {type: Boolean, default: true},
    },
    data(){
        return {
            //当前步骤
            curStep: 1,
        }
    },
    computed: {
        //是否有一步
        hasPrevStep(){
            return this.curStep > 1 && this.hasResultStep ? this.curStep ;
        },
        //是否有下一步
        hasNextStep(){
            //最后一步是结果步骤时，倒数第二步为提交，此时没有下一步
            return this.curStep < this.options.length - (this.hasResultStep ? 2 : 1);
        }
    },
    methods: {
        //上一步
        prevStep(){
            if(this.hasPrevStep){
                this.curStep--;
            }
        },
        //下一步
        nextStep(){
            if(!this.hasNextStep){
                this.curStep++;
            }
        }
        
    },
}
</script>

<style scoped lang="scss">
.step_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 950px;
  margin: auto;
  padding: 40px 0;
  > .step_item {
    flex-shrink: 1;
    flex-basis: 50%;
    display: flex;
    align-items: center;
    .number {
      width: 40px;
      text-align: center;
      height: 40px;
      line-height: 40px;
      margin-right: 12px;
      margin-left: 20px;
      border-radius: 50%;
      font-size: 18px;
      background-color: #dde1e9;
      color: #fff;
    }
    .title {
      margin-right: 20px;
      font-size: 14px;
      color: #dde1e9;
    }
    .line {
      flex: 1;
      height: 2px;
      background-color: #dde1e9;
    }
    &:first-of-type {
      flex-shrink: 0;
      flex-basis: auto;
      .line {
        display: none;
      }
    }
    &.success {
      .number {
        background: #2d84fb;
        box-shadow: 0 2px 6px 0 rgba(#2d84fb, 0.4);
      }
      .title {
        color: #2d84fb;
      }
      .line {
        background-color: #2d84fb;
      }
    }
  }
}

.options_btn {
  margin-top: 24px;
  padding: 12px 0;
  border-top: 1px solid #dde1e9;
}
</style>