<template>
  <div>
    <div class="c-more">
      <el-popover placement="right" trigger="click" v-if="showPop">
        <ul class="menu-list">
          <li
            @click.prevent.stop="clickMenu(it)"
            v-for="it in menu_list"
            :key="it.id"
            class="menu-item m-item"
          >
            {{ it.name }}
          </li>
        </ul>
        <i
          slot="reference"
          id="right-btn"
          @click.prevent.stop="show"
          class="el-icon-more"
        ></i>
      </el-popover>
    </div>
    <!-- <div v-if ="item.subs &&item.subs.length">
   <slideOper :item ="item.subs" :menu_list ="menu_list"></slideOper>
 </div> -->
  </div>
</template>

<script>
export default {
  name: 'slideOper',
  props: ['item', 'menu_list', 'id', "showPop"],
  data () {
    return {
      // showPop: false,
    }
  },
  methods: {
    clickMenu (e) {
      this.$emit('onClickMenu', e)
    },
    show () {
      // this.showPop = !this.showPop
    }
  },
}
</script>

<style lang="scss" scoped>
#right-btn {
  transform: rotate(90deg) !important;
  color: #333;
}
.m-item {
  padding: 10px 0;
  cursor: pointer;
}
</style>