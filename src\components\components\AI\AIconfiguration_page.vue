<template>
    <div class="content">
        <div class="tips">
            <span style="color:#2589ff;">提醒：</span>
            <div class="tips_content">
                <div class="tips_title">
                    AI外呼智能助理功能开启后外呼通话录音可使用AI大模型深度分析，计费0.002元/token。
                    包含通话录音文件分析费用。参考示例时长3分钟左右的通话录音分析约计费0.01元；功能开启后将通过话费余额自动扣除。
                </div>
                <span style="color:#2589ff;">AI外呼智能助理 功能介绍：</span>
                <div>
                   <span style="color:red;">*</span> AI通话内容总结： 根据成员和客户的通话录音沟通内容自动分析并生成本次通话摘要总结，便于成员直观获取客户意向。 
                </div>
                <div>
                    <span style="color:red;">*</span> 成交意向等级分析：根据成员和客户的通话录音沟通内容，分析客户有无意向，并给出分析理由。
                </div>
                <div>
                    <span style="color:red;">*</span> 客户状态等级分析：根据成员和客户的通话录音沟通内容，分析判定客户状态，并给出理由。
                </div>
                <div>
                    <span style="color:red;">*</span> 流失风险等级分析：根据成员和客户的通话情绪和内容，分析判定客户沟通状态，并给出理由。
                </div>
                <div>
                    <span style="color:red;">*</span> AI智能画像： 根据成员和客户的通话，自动获取有效核心的信息，如客户称呼、关键词、决策影响等内容。根据提取的核心信息自动维护并修改客户资料。
                </div>
                <i class="el-icon-warning"></i>本功能使用于通话录音大于60秒的录音文件
            </div>
        </div>
        <el-form ref="form" :model="params" label-width="200px" v-loading="loading">
            <el-form-item label="AI外呼智能助理">
                <el-radio v-model="params.auto_analysis" :label="0">关闭</el-radio>
                <el-radio v-model="params.auto_analysis" :label="1">开启</el-radio>
                <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;">
                  <div slot="content" style="max-width: 300px">
                    开启后外呼通话录音可使用AI大模型深度分析
                  </div>
                  <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="AI自动分析通话录音">
                <el-radio v-model="params.auto_analysis_record" :label="0">关闭</el-radio>
                <el-radio v-model="params.auto_analysis_record" :label="1">开启</el-radio>
                <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;">
                  <div slot="content" style="max-width: 300px">
                    开启后AI助理将自动分析外呼通话后新增加的通话录音，并将通话内容摘要增加至跟进记录
                  </div>
                  <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="AI智能客户画像/修改客户资料" v-if="params.auto_analysis == 1">
                <el-radio v-model="params.info_cover" :label="0">关闭</el-radio>
                <el-radio v-model="params.info_cover" :label="1">开启</el-radio>
                <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;">
                  <div slot="content" style="max-width: 300px">
                    开启后系统将根据AI助理分析的客户等级、意向标签、客户画像信息，智能覆盖修改原有的客户资料。
                  </div>
                  <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                </el-tooltip>
            </el-form-item>
            <div class="split">
                <!-- 分割线 -->
            </div>
            <el-form-item label="AI智能潜客挖掘">
                <el-radio v-model="params.auto_dig" :label="0">关闭</el-radio>
                <el-radio v-model="params.auto_dig" :label="1">开启</el-radio>
                <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;">
                  <div slot="content" style="max-width: 300px">
                    开启后AI智能助理将每天自动分析潜在客户里的通话记录，根据AI分析结果满足条件的将自动再次分配客户，提高潜在客户资源利用效率。
                  </div>
                  <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                </el-tooltip>
            </el-form-item>
            <el-form-item label="成交意向等级分析" v-if="params.auto_dig == 1">
                <el-checkbox-group v-model="dealtype" @change="handleCheckboxChange('dealtype')">
                    <el-checkbox label="0">关闭</el-checkbox>
                    <el-checkbox label="1">A级</el-checkbox>
                    <el-checkbox label="2">B级</el-checkbox>
                    <el-checkbox label="3">C级</el-checkbox>
                    <el-checkbox label="4">D级</el-checkbox>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;margin-left: 10px">
                      <div slot="content" style="max-width: 300px">
                        勾选后满足分析结果条件的客资，系统将自动再次分配。
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="客户沟通状态分析" v-if="params.auto_dig == 1">
                <el-checkbox-group v-model="clienttype" @change="handleCheckboxChange('clienttype')">
                    <el-checkbox label="0">关闭</el-checkbox>
                    <el-checkbox label="1">有效</el-checkbox>
                    <el-checkbox label="2">无效</el-checkbox>
                    <el-checkbox label="3">暂缓</el-checkbox>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;margin-left: 10px">
                      <div slot="content" style="max-width: 300px">
                        勾选后满足分析结果条件的客资，系统将自动再次分配。
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="流失风险等级分析" v-if="params.auto_dig == 1">
                <el-checkbox-group v-model="losstype" @change="handleCheckboxChange('losstype')">
                    <el-checkbox label="0">关闭</el-checkbox>
                    <el-checkbox label="1">低</el-checkbox>
                    <el-checkbox label="2">中</el-checkbox>
                    <el-checkbox label="3">高</el-checkbox>
                    <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;margin-left: 10px">
                      <div slot="content" style="max-width: 300px">
                        勾选后满足分析结果条件的客资，系统将自动再次分配。
                      </div>
                      <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script> 
export default {
    data() {
        return {
            params: {
                auto_analysis: 0,              //AI外呼智能助理
                info_cover: 0,               //AI智能客户画像/修改客户资料
                auto_dig: 0,                //流失风险等级分析
            },
            loading:false,
            dealtype:[],//等级多选
            clienttype:[],//状态多选
            losstype:[],//流失风险等级多选
        }
    },
    created() {
    },
    mounted() {
        this.getAIdata()
    },
    methods: {
        getAIdata() {
            this.$http.getAiConfig().then(res => {
                if (res.status == 200) {
                    this.params = res.data
                    this.dealtype = res.data.dig_deal_level ? res.data.dig_deal_level.split(',') : ["0"];
                    this.clienttype = res.data.dig_client_level ? res.data.dig_client_level.split(',') : ["0"];
                    this.losstype = res.data.dig_loss_level ? res.data.dig_loss_level.split(',') : ["0"];
                }
            })
        },
        handleCheckboxChange(data) {
            if(data == "dealtype"){
                if (this.dealtype.includes('0')&& this.dealtype[0] != '0') {
                    // 如果选择了关闭，清空其他选择
                     this.dealtype = ['0'];
                } else if (this.dealtype.length > 1 && this.dealtype[0] === '0') {
                    this.dealtype.shift(); // 删除第一个元素
                } 
            }else if(data == "clienttype"){
                if (this.clienttype.includes('0')&& this.clienttype[0] != '0') {
                    // 如果选择了关闭，清空其他选择
                     this.clienttype = ['0'];
                } else if (this.clienttype.length > 1 && this.clienttype[0] === '0') {
                    this.clienttype.shift(); // 删除第一个元素
                }   
            }else if(data == "losstype"){
                if (this.losstype.includes('0')&& this.losstype[0] != '0') {
                    // 如果选择了关闭，清空其他选择
                     this.losstype = ['0'];
                } else if (this.losstype.length > 1 && this.losstype[0] === '0') {
                    this.losstype.shift(); // 删除第一个元素
                }
            }
          
        },
        // 确定提交配置
        submitForm() {
            let params = JSON.parse(JSON.stringify(this.params))
            if(params.auto_analysis==0){
                params.info_cover = 0
            }
            if(params.auto_dig==0){
                params.dig_deal_level = ""
                params.dig_client_level = ""
                params.dig_loss_level = ""
            }else{
                //去空字符串
                this.dealtype = this.dealtype.filter(item => item !== '')
                this.clienttype = this.clienttype.filter(item => item !== '')
                this.losstype = this.losstype.filter(item => item !== '')
                //成交等级
                params.dig_deal_level = this.dealtype.length === 1 && this.dealtype[0] === '0' ? '' : this.dealtype.join(','),
                //有效客户
                params.dig_client_level = this.clienttype.length === 1 && this.clienttype[0] === '0' ? '' : this.clienttype.join(',');
                //流失风险等级低
                params.dig_loss_level = this.losstype.length === 1 && this.losstype[0] === '0' ? '' : this.losstype.join(','); 
            }
            // console.log(params);
            this.$http.saveAiConfig(params).then(res => {
                if(res.status == 200){
                    this.$message({
                        message: '保存成功',
                        type: 'success'
                    })
                }
            })
        },
    },
}
</script>
<style scoped lang="scss">
.content {
    padding: 0 20px;
    .vertical-radio-group{
        .el-radio{
            margin-top: 13px;
        }
    }
    .tips{
        max-width: 1350px;
        // height: 100px;
        background: #2589ff14;
        margin-bottom: 20px;
        border-radius:8px;
        .tips_content{
            font-size: 14px;
            color: #748a8f;
            margin-top: 10px;
            line-height: 30px;
        }
        .tips_title{
            width: 100%;
            background: rgb(253, 246, 236);
            color: rgb(230, 162, 60);
            border-radius:8px;
            margin-bottom: 10px;
            padding: 10px;
        }
    }
    .split{
        width: 50%;
        height: 1px;
        background: #ebeef5;
    }
}
</style>