<template>
    <div>
        <el-form ref="form" :model="form" label-width="80px">
            <el-form-item label="一键分佣" style="font-weight: 800;">
                <el-select v-model="form.region" placeholder="请选择">
                    <el-option label="A" value="shanghai"></el-option>
                    <el-option label="B" value="beijing"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" class="house_table" border :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row >
            <el-table-column label="人员" v-slot="{ row }" width="180">
                <template>
                    <div class="person-box">{{ row.person ? row.person : "--" }}</div>
                    <div class="person-box">{{ row.department ? row.department : "--" }}</div>
                </template>
            </el-table-column>
            <el-table-column label="结算类型" v-slot="{ row }" width="160">
                {{ row.setType ? row.setType : "--" }}
            </el-table-column>
            <el-table-column label="佣金公式">
                <template slot-scope="scope">
                    <div style="margin-bottom: 15px;">
                        <span>佣金金额</span>
                        <el-select style="width: 145px;margin-left: 20px;" v-model="scope.row.type"
                            @change="handleCell(scope.$index, scope.row)">
                            <el-option label="A" value="shanghai"></el-option>
                            <el-option label="B" value="beijing"></el-option>
                        </el-select>
                        <el-input v-model="scope.row.comms" disabled style="width: 145px;"></el-input>
                        <el-input v-model="scope.row.prortion" style="width: 145px;margin:0px 15px ;"></el-input>
                        <el-input v-model="scope.row.prortions" style="width: 145px;"></el-input>
                        <el-input v-model="scope.row.money" style="width: 145px;margin-left: 15px;"></el-input>
                    </div>
                    <div>
                        <span>业绩金额</span>
                        <el-select style="width: 145px;margin-left: 20px;" v-model="scope.row.type"
                            @change="handleCell(scope.$index, scope.row)">
                            <el-option label="A" value="shanghai"></el-option>
                            <el-option label="B" value="beijing"></el-option>
                        </el-select>
                        <el-input v-model="scope.row.comms" disabled style="width: 145px;"></el-input>
                        <el-input v-model="scope.row.prortion" style="width: 145px;margin:0px 15px ;"></el-input>
                        <el-input v-model="scope.row.prortions" style="width: 145px;"></el-input>
                        <el-input v-model="scope.row.money" style="width: 145px;margin-left: 15px;"></el-input>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="备注" width="260">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.remark" disabled :autosize="{ minRows: 4, maxRows: 4 }"
                        @change="changeInput" type="textarea" placeholder="请输入内容"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }" width="120">
                <el-link type="primary" @click="deleteData(row)">删除</el-link>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    name: "crm_customer_tableDeatil_split",
    components: {

    },
    data() {
        return {
            form: {},
            tableData: [
                {
                    person: '张三', //人员
                    department: '后勤保障部门',//部门
                    setType: '单笔提成结算', //结算类型
                    remark: '这是备注',
                    comms: '900000',
                    prortion: '15',
                    prortions: '100',
                    money: '00000'
                },
                {
                    person: '张三', //人员
                    department: '后勤保障部门',//部门
                    setType: '单笔提成结算', //结算类型
                    remark: '这是备注',
                    comms: '900000',
                    prortion: '15',
                    prortions: '100',
                    money: '00000'
                },
                {
                    person: '张三', //人员
                    department: '后勤保障部门',//部门
                    setType: '单笔提成结算', //结算类型
                    remark: '这是备注',
                    comms: '900000',
                    prortion: '15',
                    prortions: '100',
                    money: '00000'
                },
                {
                    person: '张三', //人员
                    department: '后勤保障部门',//部门
                    setType: '单笔提成结算', //结算类型
                    remark: '这是备注',
                    comms: '900000',
                    prortion: '15',
                    prortions: '100',
                    money: '00000'
                },
            ],
        }
    },
    mounted() { },
    methods: {
        deleteData() {
            console.log("删除了")
        }
    }
}
</script>
<style lang="scss" scoped>
.person-box {
    width: 160px;
    height: 41px;
    border: 1px solid #9B9DA1;
    text-align: center;
    line-height: 41px;
    margin-bottom: 10px;
    border-radius: 4px;
}
</style>