<template>
    <div>
        <div class="rule0_title">
            切换线索分配模式前请确认以下设置 
          </div>
          <div>
            <el-form :model="form_info" label-position="left">
              <el-form-item label="客户管理员：">
                  <el-select ref="admin_list" style="width: 300px" v-model="form_info.admin_list" multiple placeholder="请选择"
                    @focus="showPersonnelAuthority('admin_list')" @change="PersonnelChange">
                    <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
                    </el-option>
                  </el-select>
                  <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
                    <div slot="content" style="max-width: 300px">
                    客户管理员手动分配模式下可操作客户转交;将接收客户进线提醒。
                    </div>
                    <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="创始人 :">
                  <el-radio v-model="form_info.is_founder_msg" :label="1">接收消息提醒(默认)</el-radio>
                  <el-radio v-model="form_info.is_founder_msg" :label="0">不接收消息提醒</el-radio>
                  <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 17px">
                    <div slot="content" style="max-width: 300px">
                      <!-- 开启后将显示报备客户入口 -->
                    </div>
                    <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                  </el-tooltip>
                </el-form-item>
            </el-form>
          </div>
             <!-- <div style="margin-bottom:90px;">
            <el-button type="warning" size="medium" @click="Confirmswitch">确认切换</el-button>
          </div> -->
          
           <el-dialog :visible.sync="show_add_member" width="400px" title="客户管理员" append-to-body>
               <div class="member" ref="memberList">
                 <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
                   :defaultExpandAll="false">
                 </multipleTree>
                 <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
                   <el-button type="text" @click="show_add_member = false">取消</el-button>
                   <el-button type="primary" @click="selectMemberOk">确定</el-button>
                 </div>
               </div>
           </el-dialog>
           <Manualallocationrules ref="Manualallocationrules" v-show="false"></Manualallocationrules>
    </div>
</template>
<script>
import Manualallocationrules from "./Manual_allocation_rules.vue"
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
    name: 'manualallocation',
    components: {
    multipleTree,
    Manualallocationrules
   },
    props:{
        serverData: {
            type: Array,
            default: () => []
        },
        datalist: {
            type: Array,
            default: () => []
        },
        receivedData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            form_info:{
                is_founder_msg:1,
            },
            show_add_member: false, // 部门成员模态框
            department_title: "",
            selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
            AllDepartment:[],
        }
    },
    watch:{
        receivedData:{
            handler(newval,oldval){ 
                if (oldval) {
                    this.form_info = this.receivedData
                }else{
                   if(this.receivedData){
                    this.form_info = this.receivedData
                    }
                } 
                this.form_info.admin_list = this.receivedData.admin_list
                ? this.setArr(this.receivedData.admin_list): "";
            }
        }
    },
    methods:{
        // 处理部门成员数据
        setArr(arr) {
          let n_arr = arr.split(",");
          let n_arr_2 = n_arr.map((item) => {
            return parseInt(item);
          });
          // ====
          let i = 0;
          if (n_arr_2 != [] && n_arr_2 != undefined) {
            n_arr_2.map((item) => {
              this.$nextTick(() => {
                this.datalist.map((list) => {
                  if (item != list.id) {
                    i++;
                    if (i == this.datalist.length) {
                      n_arr_2.splice(n_arr_2.indexOf(item), 1);
                    }
                  }
                })
                i = 0;
              })
            })
          }
          // ====
          return n_arr_2;
        },
        PersonnelChange(val) {
          this.selectedIds = val;
        },
        selectMemberOk() {
          this.show_add_member = false;
          if (this.identification == 'admin_list') {
            this.form_info.admin_list = this.selectedIds;
          }
        },
        showPersonnelAuthority(val) {
          this.identification = val
          if (this.identification == 'admin_list' && this.form_info.admin_list != '') {
            this.selectedIds = this.form_info.admin_list;
            this.department_title = '客户管理员'
        }  else {
            this.selectedIds = [];
          }
          this.$nextTick(() => {
            this.$nextTick(() => {
                if(this.$refs.admin_list){
                  this.$refs.admin_list.blur();
                }
            });
          });
          this.show_add_member = true;
        },
        // 选中变化时触发
        selecetedMember(e) {
          this.selectedIds = e.checkedKeys;
          // this.selectedList = e.checkedNodes;
        },
        //确认切换
        Confirmswitch(){
            let result = [];
            result = [];
              if (this.form_info.admin_list != [] && this.form_info.admin_list != '') {
                this.form_info.admin_list.map((item) => {
                  if (item.toString().length >= 6) {
                    result.push(parseInt(item.toString().slice(0, 3)));
                  } else {
                    result.push(item);
                  }
                })
              }
              this.form_info.admin_list = Array.from(new Set(result));
              this.form_info.admin_list = this.form_info.admin_list
                ? this.form_info.admin_list.join(",")
                : "";
        

            this.$http.setSiteCrmSetting(this.form_info).then((res) => {
                if (res.status === 200) {
                    this.form_info.admin_list = this.form_info.admin_list
                    ? this.setArr(this.form_info.admin_list)
                    : "";
                } else {
                    this.form_info.admin_list = this.form_info.admin_list
                    ? this.setArr(this.form_info.admin_list)
                    : "";
                }
            });
        },
    },

}
</script>
<style lang="scss" scoped>
 .rule0_title{
      color: #2e3c4e;
      margin: 24px 0;
      font-size: 16px;
    }
</style>