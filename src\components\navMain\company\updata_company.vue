<template>
  <el-container>
    <el-header>
      <div class="tipsContainer">
        <b style="color: #5e6d82">提示：</b><br />
        <p class="tipsContent" v-for="item in tips_list" :key="item">
          {{ item }}
        </p>
      </div>
    </el-header>
    <el-main>
      <el-form
        label-width="100px"
        :model="form"
        style="width: 500px"
        :rules="rules"
        ref="formRules"
      >
        <!-- <el-form-item label="公司分类" prop="category">
          <el-select v-model="form.category" :placeholder="form.category_name">
            <el-option
              v-for="item in category_list"
              :key="item.id"
              :label="item.description"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item :label="label_name + '名称'" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item
          label="类型"
          prop="store_category "
          v-if="form.category === 2"
        >
          <el-radio
            :disabled="true"
            :border="true"
            size="small"
            v-model="form.store_category"
            v-for="item in store_category_list"
            :key="item.id"
            :label="item.value"
            >{{ item.description }}</el-radio
          >
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio
            v-model="form.status"
            v-for="item in store_status_list"
            :border="true"
            size="small"
            :key="item.id"
            :label="item.value"
            >{{ item.description }}</el-radio
          >
          <el-tooltip class="item" effect="light" placement="right">
                <div slot="content" style="max-width: 300px">
                  总公司停止合作后其下属所有门店经纪人将不能报备客户，如需单独禁用某个门店，请在门店修改内操作。
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
        </el-form-item>
        <el-form-item
          label="选择公司"
          prop="pid"
          v-if="form.store_category == 0 && form.category === 2"
        >
          <el-select
            value-key="pid"
            filterable
            v-model="form.pid"
            placeholder="请选择总公司"
          >
            <el-option
              v-for="item in all_sale_company"
              :key="item.pid"
              :label="item.name"
              :value="item.pid"
            ></el-option
          ></el-select>
        </el-form-item>
        <el-form-item
          :label="'选择' + label_name_category"
          v-if="form.category === 2"
          prop="store_manager_user_id"
        >
          <el-select
            filterable
            remote
            multiple
            reserve-keyword
            value-key="u_id"
            v-model="store_manager_user_id_list"
            placeholder="请输入手机号"
            :loading="broker_loading"
            :remote-method="getbrokerData"
            @change="changeStore"
          >
            <el-option
              v-for="item in store_manager_list"
              :key="item.u_id"
              :label="item.u_name"
              :value="item.u_id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作等级" prop="cooperation_level">
          <el-select v-model="form.cooperation_level" placeholder="请选择等级">
            <el-option
              v-for="item in cooperation_level_list"
              :key="item.id"
              :label="item.description"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择城市" prop="region_0">
          <div class="div row" style="width: 300px">
            <el-select
              @change="regionOne"
              v-model="form.region_0"
              placeholder="请选择城市"
              class="place-select place-select-city"
            >
              <el-option
                v-for="item in region_list_one"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-select
              v-if="form.region_1 !== 0"
              v-model="form.region_1"
              placeholder="请选择区域"
              class="place-select place-select-address"
            >
              <el-option
                v-for="item in region_list_two"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item :label="label_name + '地址'" prop="address">
          <el-input v-model="form.address"></el-input>
        </el-form-item>
        <el-form-item
          label="员工提现"
          prop="allow_withdraw"
          v-if="form.category === 2"
        >
          <el-switch
            v-model="form.allow_withdraw"
            active-color="#13ce66"
            inactive-color="#999"
          >
          </el-switch>
        </el-form-item>
        <el-form-item :label="label_name + 'logo'">
          <el-upload
            :headers="myHeader"
            :action="company_upload_type"
            :on-success="handleSuccessLogo"
            :show-file-list="false"
            list-type="picture-card"
            :on-preview="handlePictureCardPreviewLogo"
            :on-remove="handleRemoveLogo"
          >
            <img
              v-if="form.logo"
              :src="form.logo"
              alt=""
              style="margin-top: 5px"
              height="148px"
              width="148px"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="logoVisible">
            <img width="100%" :src="logoImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item label="营业执照">
          <el-upload
            :headers="myHeader"
            :action="company_upload_type"
            :on-success="handleSuccessLicense"
            list-type="picture-card"
            :show-file-list="false"
            :on-preview="handlePictureCardPreviewLicense"
            :on-remove="handleRemoveLicense"
          >
            <img
              v-if="form.business_license_img"
              :src="form.business_license_img"
              alt=""
              style="margin-top: 5px"
              height="148px"
              width="148px"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="LicenseVisible">
            <img width="100%" :src="LicenseImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item :label="label_name + '主图'">
          <el-upload
            :headers="myHeader"
            :show-file-list="false"
            :action="company_upload_type"
            :on-success="handleSuccessCompany"
            list-type="picture-card"
            :on-preview="handlePictureCardPreviewCompany"
            :on-remove="handleRemoveCompany"
          >
            <img
              v-if="form.company_img"
              :src="form.company_img"
              alt=""
              style="margin-top: 5px"
              height="148px"
              width="148px"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="CompanyVisible">
            <img width="100%" :src="CompanyImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <div class="btn-box">
          <el-form-item size="large">
            <el-button type="primary" @click="onSubmit"
              >保存{{ label_name }}信息</el-button
            >
            <el-button @click="goBack">返回列表</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
/* eslint-disable */
import config from "@/utils/config";
export default {
  name: "updata_company",
  data() {
    return {
      form: {
        category: "",
        category_name: "",
        cooperation_level: "",
        store_manager_user_id: "",
        region_0: 0,
        region_1: 0,
        address: "",
        name: "",
        logo: "",
        business_license_img: "",
        company_img: "",
        store_category: "",
        allow_withdraw: false,
        pid: "",
      },
      category_list: [],
      cooperation_level: "",
      store_manager_user_id: "",
      store_manager_user_id_list: [],
      cooperation_level_list: [],
      store_manager_list: [],
      // passwordRe: '',
      region_list: [],
      region_list_one: [],
      region_list_two: [],
      // 上传的内容
      // logo
      logoImageUrl: "",
      logoVisible: false,
      // 营业执照 图
      LicenseVisible: false,
      LicenseImageUrl: "",
      // 公司主图
      CompanyVisible: false,
      CompanyImageUrl: "",
      city_list: [],
      id: null,
      broker_loading: false,
      broker_options: [],
      rules: {
        category: [{ required: true, trigger: "blur", message: "请选择分类" }],
        region_0: [{ required: true, trigger: "blur", message: "请选择地址" }],
        address: [{ required: true, trigger: "blur", message: "请输入地址" }],
        name: [{ required: true, trigger: "blur", message: "请输入名称" }],
        cooperation_level: [
          { required: true, trigger: "blur", message: "请选择等级" },
        ],
        store_manager_user_id: [
          { required: true, trigger: "blur", message: "请选择店长" },
        ],
      },
      // 监听城市选择旧值
      region_0_old: "",
      tips_list: [
        "请正确填写输入框信息",
        // "公司结佣：设置公司是否统一给员工结佣",
      ],
      company_upload_type: `/api/common/file/upload/admin?category=${config.COMPANY_IMG}`,
      store_manager_name: "",
      store_category_list: this.$getDictionary("COMPANY_STORE_CATEGORY "), // 门店/公司
      store_status_list: [
        {
          id: 2,
          value: 1,
          description: '正常'
        },
        {
          id: 1,
          value: 0,
          description: '停止合作'
        },

      ],
      category_disabled: false,
      label_name: "公司",
      // 获取总公司列表
      all_sale_company: [],
      label_name_category: "店长",
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
  },
  watch: {
    // 监听一级区域变化，
    "form.region_0": {
      handler: function (val) {
        // console.log(val, oldVal);
        this.region_0_old = val;
        //do something
      },
    },
  },

  created() {
    if (this.$route.query.store) {
      this.category_disabled = true;
    }
    this.id = this.$route.query.id;



    // this.getCompany();
  },
  activated() {
    this.getDataList();
    this.getSaleCompanyList();
    this.cooperation_level_list = this.$getDictionary(
      "COMPANY_COOPERATION_LEVEL"
    );
  },
  methods: {
    getRegion() {
      this.$http.getRegionList().then((res) => {
        if (res.status === 200) {
          this.region_list = this.$sortPro(res.data, ["pid"]);
          this.city_list = res.data;
          this.region_list.map((item) => {
            if (item.pid === 0) {
              this.region_list_one = item.children;
            }
          });

          this.regionOne(this.form.region_0);
        }
      });
    },
    // getCompany() {
    //   this.$http.dictionaryFind("COMPANY_CATEGORY").then((res) => {
    //     this.category_list = res.data.data;
    //   });
    // },
    // 获取展示页面
    getDataList() {
      this.$http.queryCompany(this.id).then((res) => {
        if (res.status === 200) {
          this.form = res.data;
          // 获取经纪人信息id存入数组
          // 设置默认选中数组和请求一致
          if (res.data.store_category === 0) {
            this.label_name_category = "店长";
            this.label_name = "门店";
            if (res.data.pid == 0) {
              this.form.pid = "";
            }
          } else {
            this.label_name = "公司";
            this.label_name_category = "经理";
          }
          this.form.store_category = res.data.store_category.toString();
          this.store_manager_list = res.data.u_store_manager.map((item) => {
            return {
              u_id: item.u_id,
              tel: item.u_phone,
              u_name:
                item.u_name ||
                item.u_nickname ||
                item.u_user_name ||
                item.u_phone,
            };
          });
          this.store_manager_user_id_list = res.data.u_store_manager.map(
            (item) => {
              return item.u_id;
            }
          );
          this.getRegion();
          this.getbrokerData();
          this.form.cooperation_level = res.data.cooperation_level.toString();
          if (this.form.allow_withdraw === 1) {
            this.form.allow_withdraw = true;
          } else {
            this.form.allow_withdraw = false;
          }
        }
      });
    },
    // 上传文件
    // 上传logo
    handleRemoveLogo() { },
    handlePictureCardPreviewLogo(file) {
      this.logoImageUrl = file.response.url;
      this.logoVisible = true;
    },
    handleSuccessLogo(response) {
      this.form.logo = response.url;
    },
    // 上传执照
    handleRemoveLicense() { },
    handlePictureCardPreviewLicense(file) {
      this.LicenseImageUrl = file.response.url;
      this.LicenseVisible = true;
    },
    handleSuccessLicense(response) {
      this.form.business_license_img = response.url;
    },
    // 公司主图
    handleRemoveCompany() { },
    handlePictureCardPreviewCompany(file) {
      this.CompanyImageUrl = file.response.url;
      this.CompanyVisible = true;
    },
    handleSuccessCompany(response) {
      this.form.company_img = response.url;
    },
    onSubmit() {
      // 将店长数组id转成字符串
      this.$refs.formRules.validate((valid) => {
        if (valid) {
          for (var prop in this.form) {
            if (this.form[prop] === "") {
              delete this.form[prop];
            }
          }
          // if (this.form.password && this.form.password !== this.passwordRe) {
          //   this.$message.warning("密码不一致，请重新输入")
          //   return
          // }
          // console.log(this.form);
          this.$http
            .updataCompany({
              id: this.id,
              category: this.form.category,
              cooperation_level: this.form.cooperation_level,
              store_manager_user_id: this.form.store_manager_user_id,
              pid: this.form.pid,
              region_0: this.form.region_0,
              region_1: this.form.region_1,
              address: this.form.address,
              name: this.form.name,
              logo: this.form.logo,
              business_license_img: this.form.business_license_img,
              company_img: this.form.company_img,
              allow_withdraw: this.form.allow_withdraw,
              status: this.form.status,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
                this.goBack();
              }
            });
        }
      });
    },
    // 返回公司列表
    goBack() {
      if (this.$route.query.store) {
        this.$goPath(`/company_store?company_id=${this.$route.query.store}`);
      } else if (this.form.category == 1) {
        this.$goPath("/project_company_list");
      } else if (this.form.category == 2) {
        this.$goPath("/sale_company_list");
      }
    },

    regionOne(e) {
      /**
       * @date 2020/10/17
       * @title 根据后台接口筛选城市一级以及二级区域
       * @description 循环城市列表区分一级二级区域，如果item.pid=0（一级），二级区域的pid === 一级区域的pid ，根据
       * 这个筛选出二级区域，两个select列表分别放入，一级二级列表，
       * 一级区域选项添加change时间，如果change改变，那么二级区域select选择显示为二级区域数组的第一个id内容
       * 最后判断如果二级区域长度为零，那么二级区域赋值为0
       * */
      this.region_list_two = [];
      this.region_list.map((item) => {
        if (item.pid === 0) {
          this.region_list_one = item.children;
        }
        if (item.pid === e) {
          this.region_list_two = item.children;
          if (this.form.region_0 !== this.region_0_old) {
            this.form.region_1 = this.region_list_two[0].id;
          }
          // console.log(item);
        }
      });
      if (this.region_list_two.length === 0) {
        this.form.region_1 = 0;
      }
      // console.log(this.region_list_two.length);
      // console.log(this.form.region_0, this.form.region_1);
    },
    getbrokerData(query) {
      this.$http.searchUserList("100", query, 1).then((res) => {
        this.store_manager_list = res.data.data.map((item) => {
          return {
            u_id: item.id,
            tel: item.phone,
            u_name: item.name || item.nickname || item.user_name || item.phone,
          };
        });
      });
    },
    getSaleCompanyList() {
      this.$http.getSaleCompanyList(100).then((res) => {
        if (res.status === 200) {
          this.all_sale_company = res.data.data.map((item) => {
            return {
              pid: item.id,
              name: item.name,
            };
          });
        }
      });
    },
    changeStore() {
      this.form.store_manager_user_id = this.store_manager_user_id_list.toString();
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  color: #333;
  font-weight: bold;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
  }
  i {
    color: red;
  }
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-header {
//   // height: 80px !important;
//   padding-top: 30px;
// }
.el-main {
  margin-top: 40px;
  .title {
    margin: 10px 0;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
  .el-input {
    width: 300px;
  }
  .lou-type {
    margin: 20px 0;
    .lou-type-s {
      text-align: right;
      vertical-align: middle;
      float: left;
      font-size: 14px;
      color: #606266;
      padding: 0 12px 0 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }
  }
  .tips-box {
    margin: 10px 0;
    justify-content: flex-start;
    .tips-left {
      color: #2589ff;
    }
    .tips-right {
      margin-left: 20px;
      color: rgb(153, 153, 153);
    }
  }
}
.el-select {
  // margin-right: 20px;
  width: 300px;
}
.place-select {
  width: 140px;
}
.place-select-city {
  flex: 1;
}
.place-select-address {
  margin-left: 20px;
}
.tipsContainer {
  padding: 8px 16px;
  background-color: #ecf8ff;
  border-radius: 4px;
  border-left: 5px solid #50bfff;
  margin: 20px 0px;
  .tipsContent {
    font-size: 16px;
    color: #5e6d82;
    line-height: 1.5em;
  }
}
.tips {
  margin-left: 5px;
  color: #f52727;
  font-size: 12px;
}
</style>
