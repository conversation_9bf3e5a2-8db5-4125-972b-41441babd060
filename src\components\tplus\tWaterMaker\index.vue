<template>
	<div class="water-mark" ref="waterMark">
		<slot></slot>
	</div>
</template>

<script>
	export default {
		props: {
			text: { type: String, default: "" },
			color: { type: String, default: "rgba(128,128,128,0.2)" }
		},
		data() {
			return {

			}
		},
		mounted() {
			if(this.text){
				this.create()
			}
		},
		methods: {
			create(text = ''){
				if(!text){
					text = this.text
				}

				this.clear()
				//创建画板
				var canvas = document.createElement('canvas')
				canvas.width = 200
				canvas.height = 150
				canvas.style.display = 'none'
				//绘制文字
				var ctx = canvas.getContext('2d')
				ctx.rotate(-35 * Math.PI / 180)
				ctx.translate(-75, 25)
				ctx.fillStyle = this.color
				ctx.font = "14px Microsoft YaHei"
				ctx.textAlign = "center"
				ctx.fillText(text, canvas.width / 2, canvas.height / 2)
				//创建水印容器
				var watermark = document.createElement('div')
				watermark.setAttribute('class', 'watermark')
				const styleStr = `position:absolute;top:0;left:0;right:0;bottom:0;z-index:99;pointer-events:none;background-repeat:repeat;background-image:url('${canvas.toDataURL("image/png")}');`
				watermark.setAttribute('style', styleStr);
				this.$refs.waterMark.appendChild(watermark)
			},
			clear(){
				var wmDom = this.$refs.waterMark.querySelector('.watermark')
				wmDom && wmDom.remove()
			}
		}
	}
</script>

<style scoped>
	.water-mark {position: relative;display: inherit;width: 100%;height: 100%;}
</style>
