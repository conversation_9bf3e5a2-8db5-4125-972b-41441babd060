<template>
  <div>
    <el-row>
      <el-col :span="24">
        <div class="weituo_a">
          <el-tag type="warning">
            app下载链接：
            <el-tooltip
              placement="top-start"
              width="200"
              trigger="hover"
              effect="light"
            >
              <div slot="content" style="line-height: 1.5">
                <div>
                  <div>IOS： appstore搜索"众趣VR"</div>
                  <div>
                    安卓：
                    <a href="https://www.pgyer.com/L100_Android_User"
                      >点击下载</a
                    >
                  </div>
                </div>
              </div>
              <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
              <span icon="el-icon-info" type="danger"
                ><i class="el-icon-info" style="color: #f56c6c"></i
              ></span>
            </el-tooltip>
          </el-tag>
          <new_tips_list :tipsList="tips_list" :tipsTitle="tipsTitle" v-if="(is_system==0)"></new_tips_list>
          <div class="weituo_key">
            <el-row>
              <el-col>
                <div class="title flex-row align-center">
                  <el-button type="primary" @click="add"> 添加相机 </el-button>
                  <el-button type="warning" @click="settingVr">
                    参数设置
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <div class="title">
                  <el-table
                    v-loading="is_table_loading"
                    :data="VrData"
                    border
                    :header-cell-style="{ background: '#EBF0F7' }"
                    highlight-current-row
                    :row-style="$TableRowStyle"
                  >
                    <el-table-column
                      width="100"
                      prop="name"
                      label="相机名称"
                    ></el-table-column>
                    <el-table-column
                      prop="username"
                      label="登录账号"
                    ></el-table-column>
                    <el-table-column
                      width="100"
                      prop="password"
                      label="登录密码"
                    ></el-table-column>
                    <el-table-column v-slot="{ row }">
                      <template v-if="row.department">
                        <el-tag
                          v-for="(item, index) in row.department.split(',')"
                          :key="index"
                          style="margin-right: 5px; margin-bottom: 5px"
                        >
                          {{ item }}
                        </el-tag>
                      </template>
                    </el-table-column>

                    <!-- <el-table-column label="添加人" prop="user_name">
                    </el-table-column> -->

                    <el-table-column
                      label="添加时间"
                      prop="ctime"
                    ></el-table-column>
                    <el-table-column label="操作" v-slot="{ row }" width="200">
                      <!-- <el-popconfirm
                        title="确定删除吗？"
                        style="margin: 0 10px"
                        @onConfirm="del(row)"
                      >
                        <el-link
                          slot="reference"
                          type="danger"
                          icon="el-icon-delete"
                          >删除</el-link
                        >
                      </el-popconfirm> -->

                      <el-link type="primary" @click="edit(row)">编辑</el-link>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog width="500px" :visible.sync="is_showDia" :title="settingTitle">
      <div>
        <el-form label-width="120px">
          <el-form-item label="相机名称">
            <el-input v-model="setting_form.name"></el-input>
          </el-form-item>
          <el-form-item label="账户用户名">
            <el-input v-model="setting_form.username"></el-input>
          </el-form-item>
          <el-form-item label="登陆密码" v-if="!is_edit">
            <el-input
              type="password"
              v-model="setting_form.password"
            ></el-input>
          </el-form-item>
          <el-form-item label="类型">
            <el-radio-group v-model="setting_form.is_new">
                <el-radio :label="1">新创建账户</el-radio>
                <el-radio :label="0">已有众趣账户</el-radio>
              </el-radio-group>
          </el-form-item>
          <el-form-item label="部门">
            <el-radio v-model="depart_type" :label="0">全部部门</el-radio>
            <el-radio v-model="depart_type" :label="1">自定义部门</el-radio>
          </el-form-item>
          <el-form-item label="" v-if="depart_type == 1">
            <el-cascader
              placeholder="请选择部门"
              style="width: 155px"
              v-model="setting_form.department_id"
              clearable
              :show-all-levels="false"
              :options="department_list"
              :props="{
                value: 'id',
                label: 'name',
                multiple: true,
                children: 'subs',
                emitPath: false,
                checkStrictly: true,
              }"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button type="primary" @click="confirmSetting">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="600px" :visible.sync="setting" title="参数设置">
      <div>
        <el-form label-width="150px" style="transition: 1s">
          <!-- is_system -->
          <el-form-item label="是否使用系统配置">
            <el-radio
              v-model="setting_params.is_system"
              :label="1"
              @change="changeIssystem"
              >使用默认配置</el-radio
            >
            <el-radio
              v-model="setting_params.is_system"
              :label="0"
              @change="changeIssystem"
            >
              不使用默认配置
            </el-radio>
          </el-form-item>
          <template v-if="(setting_params.is_system==0)">
            <el-form-item label="appid">
              <el-input
                v-model="setting_params.app_id"
                style="width: 300px"
                placeholder="请输入appid"
              ></el-input>
            </el-form-item>
            <el-form-item label="app_secret">
              <el-input
                v-model="setting_params.app_secret"
                style="width: 300px"
                placeholder="请输入app_secret"
              ></el-input>
            </el-form-item>
            <el-form-item label="展示url格式">
              <el-input
                type="textarea"
                v-model="setting_params.url"
                style="width: 300px"
                placeholder="请输入访问url"
              ></el-input>
              <el-tooltip
                placement="top-start"
                width="200"
                trigger="click"
                effect="light"
              >
                <div slot="content" style="line-height: 1.5">
                  填写示例 https://beyond.3dnest.cn/house/?m={mid}&hid={hid}
                </div>
                <!-- slot="reference"   <el-button icon="el-icon-info"  type="danger" plain ></el-button> -->
                <span icon="el-icon-info" type="danger"
                  ><i class="el-icon-info" style="color: #f56c6c"></i
                ></span>
              </el-tooltip>
            </el-form-item>
          </template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="setting = false">取 消</el-button>
        <el-button type="primary" @click="confirmVrSetting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import new_tips_list from "@/components/components/new_tips_list";
export default {
  data() {
    return {
      VrData: [],
      aa:1,
      is_table_loading: false,
      is_showDia: false,
      settingTitle: '',
      department_list: [],
      depart_type: 0,
      is_edit: false,
      setting_form: {
        name: '',
        username: '',
        password: '',
        department_id: '',
        is_new:1,
      },
      //上方提示文字
      tips_list:[],
      tipsTitle:"参数配置为不使用默认配置时，请联系众趣官方设置以下信息:",
      setting: false,
      disabeSetting: false,
      setting_params: {
        is_system: 1,
        app_secret: '',
        app_id: '',
        url: ''
      },
      isSubing: false,
      is_system:"",
      app_id:""
    }
  },
  components:{new_tips_list},
  created() {
    this.getCrmDepartmentList()
    this.getPhotoList()
    this.getVrSetting()
  },
  methods: {
    //切换系统配置
    changeIssystem(e){
     if(e==1){
      if(!this.app_id){
        this.setting_params={
        is_system:e,
        app_secret: '',
        app_id: '',
        url: ''
        }
      }
     }else{
      if(this.setting_params.app_id){
        // this.getVrSetting()
      }
     }
    },
    // 获取部门列表
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    settingVr() {
      this.getVrSetting()
      this.setting = true
    },
    getVrSetting() {
      this.$ajax.house.getVrSetting().then(res => {
        console.log(res);
        if (res.status == 200) {
          this.setting_params = res.data;
          this.app_id=res.data.app_id
          this.is_system=res.data.is_system;
          this.tips_list=[
         `1.获取房源信息接口: ${res.data.house_detail_url}`,`2.订单数据回调地址: ${res.data.order_callback_url}`,
        `3.实勘数据回调地址: ${res.data.model_callback_url}`]
          // if (this.setting_params.app_id && this.setting_params.app_secret && this.setting_params.url) {
          //   this.disabeSetting = true
            // this.$set(this.setting_params, 'is_system', 0)
            // this.setting_params.is_system = 0
          // } else {
          //   this.disabeSetting = false
            // this.$set(this.setting_params, 'is_system', 1)
         // }
        }
      })
    },
    confirmVrSetting() {
      if (this.isSubing) return
      this.isSubing = true
      this.$ajax.house.setVrSetting(this.setting_params).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "设置成功")
          this.setting = false
          this.getVrSetting()
        }
        this.isSubing = false
      })
        .catch(() => {
          this.isSubing = false
        })
    },
    add() {
      this.is_edit = false
      this.settingTitle = "添加相机"
      this.clearData()
      this.is_showDia = true
    },
    edit(row) {
      this.is_edit = true
      this.settingTitle = "编辑相机"
      this.clearData()
      this.$http.getPhotoDetail(row.id).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.setting_form = res.data
          if (this.setting_form.department_id) {
            this.setting_form.department_id = this.setting_form.department_id.split(",")
          }
          this.depart_type = res.data.use_type
          this.is_showDia = true
        }
      })
    },
    del() { },
    clearData() {
      this.setting_form = {
        name: '',
        username: '',
        password: '',
        department_id: '',
        is_new:1
      }
    },
    getPhotoList() {
      this.is_table_loading = true
      this.$http.getPhotoList().then(res => {
        console.log(res);

        if (res.status == 200) {
          this.VrData = res.data.data
        }
        this.is_table_loading = false
      }).catch(() => {
        this.is_table_loading = false
      })
    },

    confirmSetting() {
      if (this.is_edit) {
        this.editPhoto()
      } else {
        this.addPhoto()
      }
    },
    editPhoto() {
      let params = Object.assign({}, this.setting_form)
      if (this.depart_type == 0) {
        params.department_id = "0"
      } else {
        if (params.department_id && params.department_id.length) {
          params.department_id = params.department_id.join(",")
        } else {
          params.department_id = ''
        }

      }
      if (this.isSubing) return
      this.isSubing = true
      this.$http.editPhoto(params).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success(res.message || "编辑成功")
          this.clearData()
          this.is_showDia = false
          this.getPhotoList()
        }
        this.isSubing = false
      }).catch(() => {
        this.isSubing = false
      })
    },
    addPhoto() {
      console.log(this.setting_form.department_id);
      let params = Object.assign({}, this.setting_form)
      if (this.depart_type == 0) {
        params.department_id = "0"
      } else {
        if (params.department_id && params.department_id.length) {
          params.department_id = params.department_id.join(",")
        } else {
          params.department_id = ''
        }

      }
      if (this.isSubing) return
      this.isSubing = true
      this.$http.addPhoto(params).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success(res.message || "添加成功")
          this.clearData()
          this.is_showDia = false
          this.getPhotoList()
          this.isSubing = false
        }
      }).catch(() => {
        this.isSubing = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}
.padd10 {
  padding: 10px 0 40px;
}
.weituo_a {
  padding-top: 20px;
  padding-left: 84px;

  .weituo_key {
    padding-bottom: 20px;
    border-bottom: 1px solid #f3f3f3;
    .title {
      padding-top: 20px;
    }
  }
}
</style>