/* .chat{width:820px;  margin: 20px auto;margin-left:20%;margin-right:22%;
 border:1px solid #ccc; 
position:relative;overflow: hidden;} */

		.yyj_sidebar,.c_main{height:100%;
		position:relative;
		    width: 538px;
		    float: right;
		}

.chat {
    width: 1072px;
    position: relative;
    overflow: hidden;
	   background:#fff;
	height:640px;
	/* background: #f5f7fa; */
	   border-radius: 10px 10px 0px 0px;
	   padding-left:15px;
	   
	   
}
.f14{font-size:14px;}
		ul li{list-style: none;margin-top: 10px;}
		.yyj_sidebar{width:324px;
		/* background:#f5f7fa; */
		overflow: hidden;float:left;margin-bottom: -5000px;
  padding-bottom: 5000px;
  border-right:1px solid #F6FAFE}
		.toolbar{height:40px;position:relative}
		.span{display: inline-block;width:100px;overflow:hidden;white-space: nowrap;text-overflow: ellipsis;}
		
		.m_view{height:504px;}
		.m_input{}
		.toolbar{height:30px;}
		.over{background:#E2E2E2}
		.iconfont{}
		.t_left{float:left;}
		.t_right{float:right;position: relative;font-size:12px;}
		.t_right>span{margin-right:10px;font-size:10px;color:#ccc}
		
		.fs{position:relative;height:40px}
		.fs>button{position:absolute;bottom:30px;right:20px;}
		.bottom textarea{padding: 10px;
    height: 100%;
    width: 100%;
    border: none;
    outline: 0;
    font-family: Micrsofot Yahei;
    resize: none;}
	.m_view{position:relative;overflow: hidden;}
	.headimg{position: absolute;
    top: 10px;
    left: 20px;
    border-radius: 50%;
    width: 50px;
    height: 50px;}
	
	.span2{position: absolute;left: 80px;top: 20px;}
	.span3{font-size:10px;color:#6DE0C3;margin-left:10px;}
	.span1{background: #F96063;
    border-radius: 3px;
    color: #FEEC39;
    padding: 2px 4px;
    font-size: 8px;
    margin-left: 10px;}
	.span0{    width: 100px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
	font-weight: bold;
	font-size: 14px;}
	.message{overflow-y: scroll;height: 398px; background: #FEF2F2;padding-left: 15px;
	   }
	.center{text-align: center;}
	.yyj_left{float:left}
	.text{margin-left:40px;}
	.text2{margin-right:40px;}
	.yyj_right{float:right;}
	.t_r{text-align: right;}
	.my{margin-right:20px;}
	.ml{margin-left:10px;}
	.mr{margin-right:10px;}
	.expression{    position: absolute;
    /* top: -47px; */
    bottom: 45px;
    left: 5px;
    width: 454px;
    height: 300px;
    border: 1px solid #ccc;
    background: #fff;}
	.aaa{border:1px solid #000000}
	#local-message-container {
		height: 45px;
		font-size: 14px;
		background-color: transparent;
		resize: none;
		outline: 0;
		border: none;
		/*padding-left: 10px;*/
		margin-left:10px;
		line-height: 1.6em;
		white-space: pre-wrap;
		overflow: hidden;
		overflow-y: auto;
		overflow-x: hidden;
		font-family: arial;
		    margin-right: 20px;
		/*padding-top: 0;*/
	}
	.chatimage {
		max-width: 150px;
		max-height: 200px;
		white-space:nowrap;
	}
	.chatimageBox {
		width: 100%;
	}
	.placeholder:before {
		content: attr(placeholder);
		display: block;
		color: #c7c7c7;
		cursor: text;
	},
	.icon-tupian{position:relative}
	.file{position: absolute;
    width: 25px;
    left: -6px;
    opacity: 0;}
	.suoxiao{       
	border: 1px solid #ccc;
    width: 200px;
    text-align: center;
    position: fixed;
    left: 0px;
    top: 0px;
    background: #fff;}
	.s_img{    border-radius: 50%;
    vertical-align: middle;}
	.trangleRight{
		　　width: 100px;
		　　height: 100px;
		　　border: 100px solid #000;
		　　border-top-color: red;
		　　border-bottom-color: yellow;
		　　border-left-color: blue;
		　　border-right-color: green;
	}
	.rightbox {
		padding-left: 15px;
		padding-right: 15px;
		padding-top: 8px ;
		padding-bottom: 8px;
		background: #FF706F;
		float:right;
		    border-radius: 10px;
		    color: #fff;
		    font-size: 12px;
			position:relative;
			margin-right:20px;
	}
	.leftbox {
		margin-left: 10px;
		padding-left: 15px;
		padding-right: 15px;
		padding-top: 10px ;
		padding-bottom: 10px;
		background: #ffffff;
		border-radius: 10px;
		float:left;
		color: #fff;
		font-size: 12px;
		position:relative;
		margin-left: 20px;
	}
	.m_top{margin-top:10px;}
	.new{font-size:13px;margin-left: 45px;width:150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;  color: #E0DFE0;margin-top: -12px;}
	.sign{font-size:13px!important;overflow: hidden;white-space: nowrap;text-overflow: ellipsis; float: right;margin-right: 10px;color: #E0DFE0;}
	.redPoint {
		border-radius: 50%;
		    background: red;
		    color: white;
		    width: 16px;
		    height: 16px;
		    display: inline-block;
		    font-size: 12px;
		    text-align: center;
			position:absolute;
			right: 4px;
	}
	.ul1>li:hover {
	     background-color: #f7f9fb;
	 }
	.ul1>li.active {
	     background-color: #FFF8F8;
		 box-shadow: -1px 2px 0px #f5f2f2;
	 }
	 .ul1>li:first-child{margin-top:22px;}
	 
	 .common{
	 			          position: absolute;
	 			          right: 5px;
	 			          bottom: 53px;
	 			          background: #fff;
	 			          font-size: 14px;
	 			          color: #2B333F;
	 			          width: 484px;
	 			          height: 92px;
						 overflow-y:auto;
						  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.11);
	 			}
				 
	 			    
	 		.common>p{
	 			    margin-left: 20px;
	 			    margin-right: 10px;
	 				overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					border-bottom: 1px solid #EEEEEE;
	 				
	 		}
	 		.common>p:hover{color:green;}
	 		.setMsg{
	 			    width: 400px;
	 			    height: 120px;
	 			    border: 1px solid #ccc;
	 			    position: absolute;
	 			    top: 55%;
	 				left:9%;
	 			    background: #fff;
	 			    border-radius: 7px;
	 		}
	 		.dw{position:absolute;top:10px;right:10px;font-size:12px;}
	 		.s_head>p{font-size: 12px;margin-left: 10px;}
	 		.s_head>p>select{ float: right;margin-right: 30px; width: 90px;}
	 		.mainLeft{float:left;}
	 		.mainLeft>textarea{width: 280px;margin-left: 10px;height: 60px;}
	 		.mainRigh{float:right;width: 65px;height: 60px;margin-right: 30px;line-height: 30px;}
	 		.personMsg{width: 450px;position: absolute;color: #666;top: 55px;left: 79px;overflow: hidden;white-space: nowrap;
	     text-overflow: ellipsis;
	     font-size: 10px;}
	 	.newWith{width:560px;} 
	/* 	.newWith{
			width: 580px;
			position: fixed;
			top: 0px;
			right: -905px;
		} */
	 	.center>span{font-size:12px;color:#555}
	 	.p0{width:180px;overflow: hidden;white-space: nowrap;
	     text-overflow: ellipsis;}
	 	.span4{
	     display: inline-block;
	     position: absolute;
	      right: 18px;
	      top: 41px;}
	 	.span4 i{font-size:32px;}
	 	.weixin{    width: 200px;
	     height: 100px;
	     border:1px solid #ccc;
	     border-radius: 5px;
	     font-size: 14px;}
	 	.weixin>p>i{margin-left:20px;cursor: pointer;}
	 	.weixin>p{text-align: center;margin-top:-10px;clear: both;}
	 	.weixin>p+p{margin-top:20px;}
	 	.erVode{width:100px;height:100px;position: absolute;
	     top: 50%;
	     left: 35%;
	 }
	 .tel2{
	 	    position: absolute;
	 	    top: 50%;
	 	    left: 35%;
	 	    width: 200px;
	 	    height: 40px;
	 	    background: #ccc;
	 	    text-align: center;
	 	    line-height: 40px;
	 	    border-radius: 5px;
	 }
	 .telbox2{  position: absolute;
	     /* top: 50%; */
	     top: 0px;
	     left: 0px;
	     width: 100%;
	     height: 100%;
	   
	     text-align: center;
	     line-height: 40px;
	     border-radius: 5px;
	    background:rgba(0,0,0,0.2);
	    }
	 	.wxpng{margin-top: 10px;}
	 	.real{
height:81px;
background:rgba(255,255,255,1);
box-shadow:0px 2px 6px 0px rgba(0, 0, 0, 0.11);
border-radius:4px;
font-size:12px}
	 	.real>span{vertical-align: top; margin-top: 10px;
    margin-left: 10px;}
	 	.real>button{float: right;
    margin-right: 20px;
    border: 0px;
    padding: 4px 10px;
    color: #F37176;
    margin-top: 20px;
	font-size: 10px;
	background:rgba(254,242,242,1);
	border-radius:2px;}
	 	.ptitle{text-align: left;margin:0}
	 	.message>ul>li+li{clear:both}
	 	.icon-dianhua11{color:#47A3F6;margin-right:5px;font-size:20px;vertical-align: sub;cursor:pointer}
	 	.icon-weixin31{color:#66D7B1;margin-right:5px;font-size:20px;vertical-align: sub;cursor:pointer}
	 	.icon-gengduo1{color:#F9683D;margin-right:5px;font-size:20px;vertical-align: sub;cursor:pointer}
	 	.send{
			float: right;
    margin-bottom: 20px;
    margin-right: 20px;
    background: #ff706f;
    color: #fff;
    border: 0px;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 13px;
    /* width: 87px; */
    font-size: 12px;}   
	.icon-biaoqing{font-size:24px;color: #ccc;
    margin-right: 10px;
	margin-left:10px;}
.icon-biaoqing:hover{
	font-size: 24px;
	   background: #f1f1f1;
	 border-radius: 2px;
/* 	//padding: 2px 4px; */
}	    
.icon-jiahao1{font-size:24px;color: #ccc;}	 
.icon-jiahao1:hover{
	font-size: 24px;
	   background: #f1f1f1;
	 border-radius: 2px;
/* 	padding: 2px 4px; */
}
/*悬浮按钮样式 start*/
#on{
	 position: absolute;
	 top: 40%;
	 right: 100%;
	 width: 30px;
	 height: 30px;
	 cursor: pointer;
	 border-radius: 15px;
	 background-color: rgba(13, 143, 143, 0.2);
		 }
 #on p{
  font-size:30px;
  text-align:center;
  margin-top:-6px;
  color:#01E290;
  }
/*悬浮按钮样式 end*/


/*新加样式 start*/
.head_img{
	     height: 100px;
	     border: 1px solid #FFF4F4;
	     border-radius: 10px;
	     margin-top: 15px;
		margin-right: 10px;
}
.memberImg{
	    width: 40px;
	    float: left;
		margin-top: 20px;
		margin-left:10px;
}
.memberImg>img{
	border-radius: 50%;
}
.memberMessage{
	    width: 262px;
	    float: right;
		margin-top:8px;
}
.memberMessage>p:nth-child(2) span{margin-right: 20px;}
.memberMessage>p:last-child{margin-top:10px;}
.nickname{   
    font-size: 15px;
    display: inline-block;
    width: auto;
    max-width: 80px;
    margin-top: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-left: 10px;
	font-weight: bold;}
.tname{ 
    font-size: 8px;
    width: auto;
    max-width: 50px;
    margin-left: 6px;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
	color:#716e6e}
.level_name{    
	font-size: 8px;
    padding: 2px 4px;
    border-radius: 4px;
	vertical-align: super;
	background:#FF706F;
	color:#FFCA48;
	margin-left:6px;
	}
.font8{font-size:8px;
margin-left:10px;}
.font8>span{margin-right:10px;}
.head_img2{
	
	height:82px;
	border:1px solid #F6FAFE;
}
.wd{width:313px;}

.kehu{
width:208px;
height:100%;
/* background:#f5f7fa; */
float:right;
text-align: center;
 border-left:1px solid #F6FAFE}
.kehu>p{
	       font-size: 12px;
	       background: aqua;
	       border-radius: 4px;
	       padding: 2px 4px;
	       margin-top: 10px;
	       width: 60px;
	       text-align: center;
	       margin-left: 20px;
	       cursor: pointer;
}
.newWith2{width:863px}
.friendList{font-size:12px;margin-top:40px;cursor: pointer;}
.backList{font-size:12px;margin-top:10px;cursor: pointer;}
.icon-jiantou3{font-size:12px;}
.icon-jiantou1{font-size:12px;}
.ul1>li{padding:20px;margin-top:0px;position:relative;cursor: pointer;}
.ul1>li>img{
	border-radius: 50%;
	width: 40px;
	height: 40px;
	vertical-align: top;
}
.ul1>li>span{margin-left:4px;font-size: 12px;}
.color8{color:#BEBDC2}
.color9{color:#716e6e}
.remove{
	       background: #99bfe6;
	       border-radius: 4px;
	       padding: 2px 4px;
	       cursor: pointer;
	       font-size: 12px;
	       float: right;
	       margin-right: 10px;
}
h4{
	font-weight: normal;
	font-size: 12px;
	  height: 40px;
	  border-bottom: 1px solid #F6FAFE;
	  margin-top: 46px;
} 
   .customerCard{
	       font-size: 12px;
	       color: #A2A2A2;
		   text-align: left;
		   line-height: 28px;
		   margin-top:28px;
		   padding-left:20px;
		   border-bottom:1px solid #F6FAFE;
   }

  .last-child{    margin-left: 30px;color:#2DA1DE;cursor: pointer;}
    .visitor{
		text-align: left;
		    font-size: 12px;
		    color: #333;
		    padding-left: 40px;
		    /* padding-right: 40px; */
		    margini-right: 20px;
		    /* background: green; */
		    width: 130px;
			height: 220px;
			overflow-y: scroll;
	}
	  .visitor>h4{margin-bottom:30px;margin-top: 50px;}
	  .visitor>ul>li{
		      text-align: left;
		      line-height: 22px;
		      border-left: 2px solid #E4E7ED;
		      position: relative;
		      padding-left: 15px;
		      padding-bottom: 11px;
		      margin-top: 0px;
	  }
	  .visitor>ul>li:last-child{border-left:0px;}
	  .visitor>ul>li:before{content:"";width:4px;height:4px;background:#E4E7ED}
	  .visitor>ul>li>p{
		 
		  overflow: hidden;
		  white-space: nowrap;
		  text-overflow: ellipsis;
	  }
	  
	  /*滚动条样式*/
::-webkit-scrollbar  
{  
    width: 1px;    
    background-color: #fff;  
}   
 .visitorList{
	     font-size: 12px;
	     margin-top: 10px;
		 cursor: pointer;
 }
 .f14{font-size:15px!important}
 .button_button{position:absolute;top: 18px;
    right: 24px;z-index: 10;}
 
 .button_button>i{font-size:10px;color:#666;margin-right:10px;margin-top:10px;}
 .button_button>i:first-child{margin-right: 30px;}
 .f30{font-size:25px;margin-left: 18px;}
 .icon-shandian1{font-size:23px;}
 .list{
	     height: 401px;
		 margiin-top:40px;
	     overflow-y: scroll;
		 width:100%;
 }
.wd>p:last-child{margin-top:0px;}
.wd>p:first-child{margin-top:6px;}
.mescroll-bar{margin-top:34px;}
.message>ul>li{    
	padding: 20px 10px;
    height: 50px;}
	
	.visitor>ul>li:before {
	    content: "";
	    width: 12px;
	    height: 13px;
	    background: #E4E7ED;
	    display: inline-block;
	    position: absolute;
	    top: 0px;
	    left: -6px;
	    border-radius: 50%;
	}
	.trangleRight{
		height:0;
		width:0;
		border:9px solid;
		border-color:#FF706F transparent transparent #FF706F;
		position:absolute;
		top:8px;
		right:-7px;
	}
	.trangleLeft{
		       height: 0;
		       width: 0;
		       border: 5px solid;
		       border-color: #fff #fff transparent transparent;
		       position: absolute;
		       top: 5px;
		       left: -6px;
	}
	.yyj_middle{
		width:538px;
		height:100%;
		background:#FEF2F2;
		float: right;
	}
	.wei_left{    
		float: left;
    width: 45px;}
	.wei_right{    
		float: right;
    width: 150px;
    margin-top: 20px;
    text-align: left;}
	.dw2{
		    position: absolute;
		    top: 10px;
		    right: -24px;
		    font-size: 12px;
	}
	