<template>
  <div class="outbount">
    <div class="outbount_content flex-row">
      <div class="outbount_content_left">
        <div class="outbount_content_left_date">
          <el-date-picker
            class="select_name"
            v-model="task_date"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </div>
        <div class="outbount_content_left_btn flex-row">
          <el-button plain type="primary" class="flex-1" @click="getTaskList"
            >刷新记录状态</el-button
          >
        </div>
        <!-- 任务包列表 -->
        <div class="task_list">
          <div
            class="task_item"
            v-for="item in search_list"
            :key="item.id"
            @click="clickTask(item)"
            :class="{ active: current.id == item.id }"
          >
            <div class="task_title flex-row align-center">
              <div class="task_title_name flex-1">{{ item.name }}</div>
              <div class="task_title_status">导入成功</div>
            </div>
            <div class="task_preview flex-row align-center">
              <div class="task_preview_item flex-1">
                <div class="task_preview_item_top">{{ item.clue }}</div>
                <div class="task_preview_item_bottom">线索</div>
              </div>
              <div class="task_preview_item_line"></div>
              <div class="task_preview_item flex-1">
                <div class="task_preview_item_top">{{ item.repeat }}</div>
                <div class="task_preview_item_bottom">重复</div>
              </div>
              <div class="task_preview_item_line"></div>
              <div class="task_preview_item flex-1">
                <div class="task_preview_item_top">{{ item.blacklist }}</div>
                <div class="task_preview_item_bottom">黑名单</div>
              </div>
            </div>
            <div class="task_info">
              <div class="task_info_item flex-row align-center">
                <div class="task_info_item_value">创建时间：</div>
                <div class="task_info_item_name">{{ item.created_at }}</div>
              </div>
              <!-- <div class="task_info_item flex-row align-center">
                <div class="task_info_item_value">分配来源</div>
                <div class="task_info_item_name">表格</div>
              </div>
              <div class="task_info_item flex-row align-center matb10">
                <div class="task_info_item_value">分配人</div>
                <div class="task_info_item_name">表格</div>
              </div>
              <div class="task_info_item flex-row align-center">
                <div class="task_info_item_value">分配时间</div>
                <div class="task_info_item_name">表格</div>
              </div> -->
            </div>
            <div class="sanjiao"></div>
          </div>
        </div>
      </div>
      <div class="outbount_content_right flex-1">
        <div class="outbount_content_right_title flex-row align-center">
          <div class="outbount_content_right_title_con flex-1">
            {{ current.name }}
          </div>
          <div class="outbount_content_right_title_info flex-row align-center">
            <span>创建时间：</span>
            <span class="mr10">{{ current.created_at }}</span>
            <span>导入人：</span>
            <span class="mr10">{{ current.admin_name }}</span>
          </div>
        </div>
        <div class="outbount_content_right_info flex-row items-center">
          <div class="outbount_content_right_info_item flex-row items-center">
            <div class="outbount_content_right_info_value">渠道获量人：</div>
            <div class="outbount_content_right_info_name">
              {{ current.from_user }}
            </div>
          </div>
          <div class="outbount_content_right_info_item flex-row items-center">
            <div class="outbount_content_right_info_value">渠道来源：</div>
            <div class="outbount_content_right_info_name">
              {{ current.channel_name }}
            </div>
          </div>
        </div>
        <!-- <div class="outbount_content_right_nav flex-row align-center">
          <div class="outbount_content_right_nav_item">
            <div class="outbount_content_right_nav_item_t">未拨/已拨/总量</div>
            <div class="outbount_content_right_nav_item_b">10/20/30</div>
          </div>
          <div class="outbount_content_right_nav_item">
            <div class="outbount_content_right_nav_item_t">通话总时长</div>
            <div class="outbount_content_right_nav_item_b">01:02:45</div>
          </div>
          <div class="outbount_content_right_nav_item">
            <div class="outbount_content_right_nav_item_t">平均通话时长</div>
            <div class="outbount_content_right_nav_item_b">30秒</div>
          </div>
        </div> -->
        <daoruTable :current="current"></daoruTable>
      </div>
    </div>
  </div>
</template>

<script>
import daoruTable from "./daoruTable.vue";
export default {
  components: { daoruTable },
  data() {
    return {
      search_list: [

      ],
      current: {
        id: ''
      },
      params: {
        name: '',
        stime: '',
        etime: ''

      },
      task_date: [],
      // isFirebox: false, //是否火狐浏览器 处理兼容问题

    }
  },
  created() {
    // let agent = navigator.userAgent
    // if (agent.indexOf("Firefox") >= 0) {
    //   this.isFirebox = true
    // }
    this.getTaskList()
  },
  methods: {
    getTaskList() {
      if (this.task_date && this.task_date.length) {
        this.params.stime = this.task_date[0]
        this.params.etime = this.task_date[1]
      }
      this.$http.getCallClues(this.params).then(res => {
        if (res.status == 200) {
          this.search_list = res.data.data
          if (res.data.data.length) {
            this.current = this.search_list[0]
          }

        }
      })
    },
    clickTask(item) {
      this.current = item
    }
  },

}
</script>

<style scoped lang="scss">
.mr10 {
  margin-right: 10px;
}
.mr60 {
  margin-right: 60px;
}
.mb12 {
  margin-bottom: 12px;
}
.matb10 {
  margin: 10px 0;
}
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
.outbount_search {
  padding: 20px;
  background: #fff;
}
.outbount_top {
  width: 1200px;
  flex-wrap: wrap;
  .outbount_top_item {
    .task_name {
    }
    .select_name {
      width: 260px;
    }
  }
}
.outbount_content {
  background: #fff;
  margin-top: 5px;
  padding-top: 10px;
  .outbount_content_left {
    .outbount_content_left_date {
    }
    .outbount_content_left_btn {
      margin: 20px 0;
    }
  }
}
.outbount_content_left {
  padding: 5px 20px;
}
.task_list {
  max-height: 625px;
  overflow-y: auto;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    width: 0;
  }
  .task_item {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border: 1px solid #e1e1e1;
    position: relative;
    margin-bottom: 10px;
    .sanjiao {
      position: absolute;
      top: 50%;
      right: -20px;
      transform: translate(0, -50%);
      width: 0;
      height: 0;
      border: 10px solid transparent;
    }
    &.active {
      .task_title {
        background: #2d84fb;
        color: #fff;
      }
      border: 1px solid #2d84fb;
      .sanjiao {
        border-left: 10px solid #2d84fb;
      }
    }
    .task_title {
      background: #e1e1e1;
      padding: 10px 14px;
      color: #2e3c4e;
      // .task_title_name {
      // }
      // .task_title_status {
      // }
    }
    .task_preview {
      margin-top: 20px;
      border-bottom: 1px solid #e1e1e1;
      .task_preview_item {
        text-align: center;
        padding: 16px 0;
        .task_preview_item_top {
          color: #2e3c4e;
          font-weight: 600;
          font-size: 14px;
          text-align: center;
        }
        .task_preview_item_bottom {
          margin-top: 4px;
          color: #8a929f;
          font-size: 14px;
        }
      }
      .task_preview_item_line {
        height: 30px;
        width: 1px;
        min-width: 1px;
        margin-bottom: 20px;
        align-self: flex-end;
        background: #e1e1e1;
      }
    }
    .task_info {
      padding: 16px;
      .task_info_item {
        color: #8a929f;
        font-size: 14px;
        .task_info_item_value {
          margin-right: 5px;
        }
        // .task_info_item_name {
        // }
      }
    }
  }
}
.outbount_content_right {
  padding: 10px 25px;
  .outbount_content_right_title {
    .outbount_content_right_title_con {
      color: #2e3c4e;
      font-size: 20px;
      font-weight: 500;
    }
    .outbount_content_right_title_info {
      color: #8a929f;
      font-size: 14px;
    }
  }
  .outbount_content_right_info {
    padding: 25px 0 10px;
    font-size: 14px;
    color: #8a929f;
    .outbount_content_right_info_item {
      margin-right: 10px;
      .outbount_content_right_info_value {
        margin-right: 10px;
      }
    }
  }
  .outbount_content_right_nav {
    padding: 20px;
    &_item {
      width: 33%;
      max-width: 333px;
      &_t {
        color: #8a929f;
        font-size: 14px;
      }
      &_b {
        margin-top: 10px;
        font-weight: 600;
        color: #2e3c4e;
        font-size: 24px;
      }
    }
  }
}
</style>