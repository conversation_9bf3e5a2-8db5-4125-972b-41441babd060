<template>
    <div class="flex-row" style="margin-top: 13px;">
        <div class="follow-voice">
            <div class="follow-voice-box">
                <!-- 播放按钮 -->
                <div class="follow-voice-play el-icon-video-plays" ref="is_play" @click="clickAudioPlay">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="">
                </div>
                <!-- 暂停按钮 -->
                <div class="follow-voice-pause" ref="is_pause" @click="clickAudioPause">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="">
                </div>
                <!-- 进度条 -->
                <div class="follow-voice-progressBar">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="">
                </div>
                <!-- 语音时长 -->
                <div class="follow-voice-time">{{ activity.voice_duration }}"</div>
            </div>
        </div>
        <!-- 播放图标 -->
        <div class="follow-pla-icon" ref="is_icon" v-if="(activity.url != '' && activity.url != undefined)">
            <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="">
        </div>
        <!-- 语音播放组件 -->
        <audio v-if="activity.url != '' && activity.url != undefined" class="voice-module" style="display: none"
            ref="voicePlays" :src="activity.url" @ended="onAudioEnded" @loadedmetadata="getAudioDuration">
        </audio>
    </div>
</template>
<script>
export default {
    props: {
        // 当前客户数据
        activity: {
            type: Object,
            default: () => {}
        },
    },
    data() {
        return {

        }
    },
    mounted() {

    },
    methods: {
        // 播放按钮
        clickAudioPlay() {
            // 显示所有播放按钮
            let allPlay = document.querySelectorAll(".follow-voice-play");
            allPlay.forEach((item) => {
                item.className = "follow-voice-play el-icon-video-plays";
            })
            // 隐藏所有暂停按钮
            let allPause = document.querySelectorAll(".follow-voice-pause");
            allPause.forEach((item) => {
                item.className = "follow-voice-pause";
            })
            // 隐藏当前点击的语音播放按钮
            let voicePlay = this.$refs.is_play;
            voicePlay.className = "follow-voice-play";
            // 显示当前点击的语音暂停按钮
            let voicePause = this.$refs.is_pause;
            voicePause.className = "follow-voice-pause el-icon-video-plays";
            // 隐藏所有播放icon图标
            let allIcon = document.querySelectorAll(".follow-pla-icon");
            allIcon.forEach((item) => {
                item.className = "follow-pla-icon";
            })
            // 显示当前点击的播放icon图标
            let voiceIcon = this.$refs.is_icon;
            voiceIcon.className = "follow-pla-icon el-voice-icon";
            // 点击播放后让所有的语音按钮都暂停播放
            let audios = document.querySelectorAll(".voice-module");
            audios.forEach(item => {
                item.pause();
            })
            const voicePlays = this.$refs.voicePlays; // 获取refs
            voicePlays.play(); // 播放
            this.$forceUpdate();
        },
        // 暂停按钮
        clickAudioPause() {
            let voicePlay = document.querySelectorAll(".follow-voice-play");
            voicePlay.forEach((item) => {
                item.className = "follow-voice-play el-icon-video-plays";
            })
            let voicePause = this.$refs.is_pause;
            voicePause.className = "follow-voice-pause";
            // 点击暂停按钮隐藏当前点击的播放icon图标
            let voiceIcon = this.$refs.is_icon;
            voiceIcon.className = "follow-pla-icon"
            this.$forceUpdate();
            const voicePlays = this.$refs.voicePlays; // 获取ref
            voicePlays.pause(); // 暂停播放
        },
        // 获取语音播放控件
        getAudioDuration() {
            const voicePlays = this.$refs.voicePlays; // 获取refs
            let voice_time = "";
            voice_time = Math.floor(voicePlays.duration); // 获取录音时长
            this.activity.voice_duration = voice_time;
            this.$forceUpdate();
        },
        // 监听audio播放完毕
        onAudioEnded() {
            // 播放完成后显示播放按钮
            let voicePlay = document.querySelectorAll(".follow-voice-play");
            voicePlay.forEach((item) => {
                item.className = "follow-voice-play el-icon-video-plays";
            })
            // 播放完成后隐藏当前点击的暂停按钮
            let voicePause = this.$refs.is_pause;
            voicePause.className = "follow-voice-pause";
            // 播放完成后隐藏当前点击的播放icon图标
            let voiceIcon = this.$refs.is_icon;
            voiceIcon.className = "follow-pla-icon"
            this.$forceUpdate();
        },
    }
}
</script>
<style lang="scss" scoped>
.follow-voice {
    display: inline-block;
    // display: flex;
    // flex-direction: row;
    // margin-bottom: 13px;

    .follow-voice-box {
        display: flex;
        flex-direction: row;
        border-radius: 10px;
        background: #2D84FB;
        align-items: center;
        box-sizing: border-box;
        padding: 2px 6px 2px 2px;

        .follow-voice-play,
        .follow-voice-pause {
            display: none;
            width: 16px;
            height: 16px;
            margin-right: 6px;
            cursor: pointer;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .el-icon-video-plays {
            display: block;
        }

        .follow-voice-progressBar {
            width: 58px;
            height: 10px;
            line-height: 10px;
            margin-right: 5px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .follow-voice-time {
            color: #FFFFFF;
            font-size: 10px;
        }
    }
}

.follow-pla-icon {
    display: none;
    width: 20px;
    height: 20px;
    line-height: 20px;
    margin-left: 4px;
}

.el-voice-icon {
    display: inline-block;
}
</style>