<template>
  <el-main>
    <myForm :form_create="form_create" @onClick="onSubmit"></myForm>
  </el-main>
</template>

<script>
import myForm from "@/components/components/my_form";
export default {
  name: "updata_rules",
  components: {
    myForm,
  },
  data() {
    return {
      form_create: {
        model: {},
        formLabel: [
          {
            label: "此处设置内容仅在独立经纪人或游客登录时展示",
          },
          {
            model: "brokerage_rule",
            label: "佣金分成：",
            placeholder: "不设置佣金填0即可",
            type: "input",
            inputWidth: "300px",
          },
          {
            model: "brokerage_description",
            label: "分佣规则：",
            type: "input",
            inputType: "textarea",
            inputWidth: "300px",
          },
          {
            model: "shopping_guide_reward",
            label: "带看奖励：",
            type: "input",
            inputType: "textarea",
            inputWidth: "300px",
          },
          {
            label:
              "此处设置内容仅在门店经纪人登录时展示，留空时将与独立经纪人展示一样的内容",
          },
          {
            model: "store_brokerage_rule",
            label: "佣金分成：",
            placeholder: "不设置默认填0即可",
            type: "input",
            inputWidth: "300px",
          },
          {
            model: "store_brokerage_description",
            label: "分佣规则：",
            type: "input",
            inputType: "textarea",
            inputWidth: "300px",
          },
          {
            model: "store_shopping_guide_reward",
            label: "带看奖励：",
            type: "input",
            inputType: "textarea",
            inputWidth: "300px",
          },
        ],
      },
    };
  },
  mounted() {
    this.form_create.model.id = this.$route.query.id;
    this.getDataDetail();
  },
  methods: {
    getDataDetail() {
      this.$http.queryProject(this.form_create.model.id).then((res) => {
        if (res.status === 200) {
          this.form_create.model = res.data;
        }
      });
    },
    goBack() {
      this.$router.back();
    },
    onSubmit() {
      for (var prop in this.form_create.model) {
        if (this.form_create.model[prop] === "") {
          delete this.form_create.model[prop];
        }
      }
      this.$http.updataRules(this.form_create.model).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.$router.back();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.personal /deep/ .el-form-item__label {
  color: #2589ff;
  background: #f4f9ff;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-form {
// 	padding-top: 25px;
// }
.store /deep/ .el-form-item__label {
  color: #a125ff;
  background: #f9f2ff;
}
.el-input,
.el-select,
.el-textarea {
  width: 300px;
}
</style>
