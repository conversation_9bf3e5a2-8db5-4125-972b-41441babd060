import Vue from "vue";
import Router from "vue-router";
import { fullPageRouter as customerFullPageRouter, default as customerRouter } from './customRouter';

Vue.use(Router);

// 在VUE中路由遇到Error: Avoided redundant navigation to current location:报错路由重复，
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
import login from "@/page/login";
import login1 from "@/page/login1";
import companyLogin from "@/page/company_login.vue";
// 路由懒加载

// 客户管理

let routerLists = [
  {
    path: "/",
    redirect: "/index",
    meta: { needLogin: true },
  },
  {
    path: "/",
    component: (resolve) => require(["@/page/home"], resolve),
    meta: { title: "首页" },
    // name: "首页",
    children: [
      {
        path: "/index",
        component: (resolve) =>
          require(["@/components/navMain/setup/web_overview"], resolve),
        meta: { title: "工作台", id: 1, keepAlive: true },
      },
      {
        path: "/notice",
        component: (resolve) =>
          require(["@/components/navMain/crm/notice"], resolve),
        meta: { title: "系统提醒" },
        name: "notice"
      },
      {
        path: "/property_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/property_list",
          ], resolve),
        meta: { title: "楼盘列表", id: 2, keepAlive: false },
        name: "buildlist",
      },
      {
        path: "/company_property_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/property_list",
          ], resolve),
        meta: { title: "楼盘列表", id: 2, keepAlive: false },
        name: "company_property_list",
      },
      {
        path: "/addbuild",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/addbuild"], resolve),
        meta: { title: "发布楼盘", keepAlive: false },
        name: "addbuild",
      },
      {
        path: "/company_addbuild",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/addbuild",
          ], resolve),
        meta: { title: "发布楼盘", keepAlive: false },
        name: "company_addbuild",
      },
      {
        path: "/dynamic_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/dynamic_list",
          ], resolve),
        meta: { title: "动态列表", keepAlive: false },
        name: "dynamic_list",
      },
      {
        path: "/company_dynamic_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/dynamic_list",
          ], resolve),
        meta: { title: "动态列表", keepAlive: false },
        name: "dynamic_list",
      },
      {
        path: "/setup_type",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/setup_type"], resolve),
        meta: { title: "编辑户型", keepAlive: false },
        name: "setup_type",
      },
      {
        path: "/company_setup_type",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/setup_type",
          ], resolve),
        meta: { title: "编辑户型", keepAlive: false },
        name: "company_setup_type",
      },

      {
        path: "/updata_type",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/updata_type"], resolve),
        meta: { title: "户型添加", keepAlive: true },
        name: "updata_type",
      },
      {
        path: "/company_updata_type",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/updata_type",
          ], resolve),
        meta: { title: "户型添加", keepAlive: true },
        name: "company_updata_type",
      },
      {
        path: "/company_setup_photo",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/setup_photo",
          ], resolve),
        meta: { title: "编辑相册", keepAlive: true },
        name: "setup_photo",
      },
      {
        path: "/setup_photo",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/setup_photo"], resolve),
        meta: { title: "编辑相册", keepAlive: true },
        name: "setup_photo",
      },
      {
        path: "/build_ranking",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/build_ranking",
          ], resolve),
        meta: { title: "楼盘榜单", keepAlive: false },
        name: "build_ranking",
      },
      {
        path: "/bind_ranking",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/bind_ranking",
          ], resolve),
        meta: { title: "绑定榜单", keepAlive: false },
        name: "bind_ranking",
      },
      // 客户
      {
        path: "/report_customer",
        component: (resolve) =>
          require([
            "@/components/navMain/projectInfo/report_customer",
          ], resolve),
        meta: { title: "报备客户", id: 16, keepAlive: false },
        name: "report_customer",
      },
      {
        path: "/company_report_customer",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/report_customer.vue",
          ], resolve),
        meta: { title: "报备客户", id: 16, keepAlive: false },
        name: "report_customer",
      },
      {
        path: "/deal_edit",
        component: (resolve) =>
          require(["@/components/navMain/projectInfo/deal_edit"], resolve),
        meta: { title: "成交编辑", keepAlive: true },
        name: "deal_edit",
      },
      {
        path: "/deal_orde",
        component: (resolve) =>
          require(["@/components/navMain/projectInfo/deal_orde"], resolve),
        meta: { title: "成交账单", keepAlive: true },
        name: "deal_orde",
      },
      // 客户
      {
        path: "/man_management",
        component: (resolve) =>
          require(["@/components/navMain/customer/man_management"], resolve),
        meta: { title: "管理员信息", id: 9, keepAlive: false },
        name: "man_management",
      },
      {
        path: "/user_list",
        component: (resolve) =>
          require(["@/components/navMain/customer/user_list"], resolve),
        meta: { title: "用户列表", id: 10, keepAlive: true },
        name: "user_list",
      },

      // 设置
      {
        path: "/web_overview",
        component: (resolve) =>
          require(["@/components/navMain/setup/web_overview"], resolve),
        meta: { title: "工作台", keepAlive: true },
        name: "web_overview",
      },
      {
        path: "/site_setup",
        component: (resolve) =>
          require(["@/components/navMain/setup/site_setup"], resolve),
        meta: { title: "参数设置", keepAlive: true },
        name: "参数设置",
        redirect: "/set_our",
        children: [
          {
            path: "/set_our",
            component: (resolve) =>
              require(["@/components/navMain/setup/set_our"], resolve),
            meta: { title: "单页设置", id: 4, keepAlive: true },
            name: "set_our",
          },
          {
            path: "/spa_detail",
            component: (resolve) =>
              require(["@/components/navMain/setup/spa_detail"], resolve),
            meta: { title: "单页详情", keepAlive: true },
            name: "spa_detail",
          },
          {
            path: "/system_config",
            component: (resolve) =>
              require(["@/components/navMain/setup/system_config"], resolve),
            meta: { title: "系统配置", keepAlive: true },
            name: "system_config",
          },
          {
            path: "/website_update",
            component: (resolve) =>
              require(["@/components/navMain/setup/website_update"], resolve),
            meta: { title: "修改系统", keepAlive: true },
            name: "website_update",
          },
        ],
      },
      {
        path: "/oss_list",
        component: (resolve) =>
          require(["@/components/navMain/package/oss_list"], resolve),
        meta: { title: "oss配置", keepAlive: true },
        name: "oss_list",
      },
      {
        path: "/sms_list",
        component: (resolve) =>
          require(["@/components/navMain/package/sms_list"], resolve),
        meta: { title: "短信配置", keepAlive: true },
        name: "sms_list",
      },
      {
        path: "/mes_setup",
        component: (resolve) =>
          require(["@/components/navMain/setup/mes_setup"], resolve),
        meta: { title: "消息设置", keepAlive: true },
        name: "mes_setup",
        redirect: "/system_msg",
        children: [
          {
            path: "/system_msg",
            component: (resolve) =>
              require(["@/components/navMain/setup/system_msg"], resolve),
            meta: { title: "系统消息", id: 5, keepAlive: true },
            name: "system_msg",
          },
          {
            path: "/add_system_msg",
            component: (resolve) =>
              require(["@/components/navMain/setup/add_system_msg"], resolve),
            meta: { title: "添加消息", keepAlive: true },
            name: "add_system_msg",
          },
        ],
      },
      {
        path: "/city_setup",
        component: (resolve) =>
          require(["@/components/navMain/setup/city_setup"], resolve),
        meta: { title: "城市管理", id: 6, keepAlive: false },
        name: "city_setup",
      },
      {
        path: "/advertising",
        component: (resolve) =>
          require(["@/components/navMain/setup/advertising"], resolve),
        meta: { title: "广告管理", keepAlive: false },
        name: "advertising",
        redirect: "/advertising_list",
        children: [
          {
            path: "/advertising_list",
            component: (resolve) =>
              require(["@/components/navMain/setup/advertising_list"], resolve),
            meta: { title: "广告位列表", id: 7, keepAlive: true },
            name: "advertising_list",
          },
          {
            path: "/adv_management",
            component: (resolve) =>
              require(["@/components/navMain/setup/adv_management"], resolve),
            meta: { title: "广告位管理", keepAlive: true },
            name: "adv_management",
          },
          {
            path: "/add_advertising",
            component: (resolve) =>
              require(["@/components/navMain/setup/add_advertising"], resolve),
            meta: { title: "添加广告", keepAlive: true },
            name: "add_advertising",
          },
        ],
      },
      // 公司

      {
        path: "/updata_company",
        component: (resolve) =>
          require(["@/components/navMain/company/updata_company"], resolve),
        meta: { title: "修改公司信息", keepAlive: true },
        name: "updata_company",
      },
      {
        path: "/upload_company",
        component: (resolve) =>
          require(["@/components/navMain/company/upload_company"], resolve),
        meta: { title: "添加公司", keepAlive: true },
        name: "upload_company",
      },
      {
        path: "/project_list",
        component: (resolve) =>
          require(["@/components/navMain/company/project_list"], resolve),
        meta: { title: "项目列表", id: 13, keepAlive: true },
        name: "project_list",
      },
      {
        path: "/company_project_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/project_list.vue",
          ], resolve),
        meta: { title: "项目列表", id: 13, keepAlive: true },
        name: "company_project_list",
      },
      {
        path: "/updata_project",
        component: (resolve) =>
          require(["@/components/navMain/company/updata_project"], resolve),
        meta: { title: "项目", keepAlive: true },
        name: "updata_project",
      },
      {
        path: "/company_updata_project",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/updata_project.vue",
          ], resolve),
        meta: { title: "项目", keepAlive: false },
        name: "company_updata_project",
      },
      {
        path: "/binding_broker",
        component: (resolve) =>
          require(["@/components/navMain/company/binding_broker"], resolve),
        meta: { title: "经纪人列表", keepAlive: false },
        name: "binding_broker",
      },
      {
        path: "/binding_manager",
        component: (resolve) =>
          require(["@/components/navMain/company/binding_manager"], resolve),
        meta: { title: "项目经理列表", keepAlive: false },
        name: "binding_manager",
      },
      {
        path: "/company_share_content",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/share_content.vue",
          ], resolve),
        meta: { title: "设置推广", keepAlive: false },
        name: "share_content",
      },
      {
        path: "/share_content",
        component: (resolve) =>
          require(["@/components/navMain/company/share_content"], resolve),
        meta: { title: "设置推广", keepAlive: false },
        name: "share_content",
      },
      {
        path: "/updata_rules",
        component: (resolve) =>
          require(["@/components/navMain/company/updata_rules"], resolve),
        meta: { title: "修改佣金规则", keepAlive: true },
        name: "updata_rules",
      },
      {
        path: "/company_updata_rules",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/updata_rules.vue",
          ], resolve),
        meta: { title: "修改佣金规则", keepAlive: true },
        name: "updata_rules",
      },
      {
        path: "/company_store",
        component: (resolve) =>
          require(["@/components/navMain/company/company_store"], resolve),
        meta: { title: "公司门店", keepAlive: true },
        name: "company_store",
      },
      {
        path: "/company_manager",
        component: (resolve) =>
          require(["@/components/navMain/company/company_manager"], resolve),
        meta: { title: "公司经理", keepAlive: false },
        name: "company_manager",
      },
      {
        path: "/binding_store",
        component: (resolve) =>
          require(["@/components/navMain/company/binding_store"], resolve),
        meta: { title: "门店管理", keepAlive: true },
        name: "binding_store",
      },
      {
        path: "/news_management",
        component: (resolve) =>
          require(["@/components/navMain/news/news_management"], resolve),
        meta: { title: "资讯管理", keepAlive: false },
        name: "资讯管理",
        redirect: "/management_page",
        children: [
          {
            path: "/class_list",
            component: (resolve) =>
              require(["@/components/navMain/news/class_list"], resolve),
            meta: { title: "分类列表", keepAlive: false },
            name: "class_list",
          },
          {
            path: "/management_page",
            component: (resolve) =>
              require(["@/components/navMain/news/management_page"], resolve),
            meta: { title: "管理列表", id: 18, keepAlive: false },
            name: "management_page",
          },
          {
            path: "/add_news",
            component: (resolve) =>
              require(["@/components/navMain/news/add_news"], resolve),
            meta: { title: "增加资讯", keepAlive: true },
            name: "add_news",
          },
          {
            path: "/add_list",
            component: (resolve) =>
              require(["@/components/navMain/news/add_list"], resolve),
            meta: { title: "增加分类", keepAlive: true },
            name: "add_list",
          },
        ],
      },
      {
        path: "/updataInfo",
        component: (resolve) =>
          require(["@/components/navMain/news/updataInfo"], resolve),
        meta: { title: "修改资讯", keepAlive: true },
        name: "updataInfo",
      },
      {
        path: "/member_audit",
        component: (resolve) =>
          require(["@/components/navMain/member/member_audit"], resolve),
        meta: { title: "身份审核", id: 11, keepAlive: true },
        name: "member_audit",
      },
      {
        path: "/withdrawal_audit",
        component: (resolve) =>
          require(["@/components/navMain/member/withdrawal_audit"], resolve),
        meta: { title: "提现审核", id: 19, keepAlive: true },
        name: "withdrawal_audit",
      },
      {
        path: "/transaction_list",
        component: (resolve) =>
          require(["@/components/navMain/member/transaction_list"], resolve),
        meta: { title: "交易记录", id: 21, keepAlive: false },
        name: "transaction_list",
      },
      {
        path: "/bill_list",
        component: (resolve) =>
          require(["@/components/navMain/member/bill_list"], resolve),
        meta: { title: "账单列表", keepAlive: false },
        name: "bill_list",
      },
      {
        path: "/payment_records_list",
        component: (resolve) =>
          require([
            "@/components/navMain/member/payment_records_list",
          ], resolve),
        meta: { title: "付款记录", keepAlive: false },
        name: "payment_records_list",
      },
      {
        path: "/batch_pay_list",
        component: (resolve) =>
          require(["@/components/navMain/member/batch_pay_list"], resolve),
        meta: { title: "批次已付记录", keepAlive: false },
        name: "batch_pay_list",
      },
      {
        path: "/invoice_list",
        component: (resolve) =>
          require(["@/components/navMain/member/invoice_list"], resolve),
        meta: { title: "开票记录", keepAlive: false },
        name: "invoice_list",
      },
      {
        path: "/invoice_batch_list",
        component: (resolve) =>
          require(["@/components/navMain/member/invoice_batch_list"], resolve),
        meta: { title: "开票记录", keepAlive: false },
        name: "invoice_batch_list",
      },
      {
        path: "/batch_list",
        component: (resolve) =>
          require(["@/components/navMain/member/batch_list"], resolve),
        meta: { title: "批次记录", keepAlive: false },
        name: "batch_list",
      },
      {
        path: "/sale_settlement",
        component: (resolve) =>
          require(["@/components/navMain/customer/sale_settlement"], resolve),
        meta: { title: "佣金结算", id: 20, keepAlive: false },
        name: "sale_settlement",
      },
      {
        path: "/copy_template_list",
        component: (resolve) =>
          require([
            "@/components/navMain/customer/copy_template_list",
          ], resolve),
        meta: { title: "模板信息", id: 17, keepAlive: false },
        name: "copy_template_list",
      },
      {
        path: "/edit_user",
        component: (resolve) =>
          require(["@/components/navMain/user/edit_user"], resolve),
        meta: { title: "修改信息", keepAlive: true },
        name: "edit_user",
      },
      {
        path: "/apply_list",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/apply_list"], resolve),
        meta: { title: "报名列表", keepAlive: false },
        name: "apply_list",
      },
      {
        path: "/company_apply_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/apply_list",
          ], resolve),
        meta: { title: "报名列表", keepAlive: false },
        name: "apply_list",
      },
      {
        path: "/company_users",
        component: (resolve) =>
          require(["@/components/navMain/company/company_users"], resolve),
        meta: { title: "员工列表", keepAlive: false },
        name: "company_users",
      },
      {
        path: "/add_user",
        component: (resolve) =>
          require(["@/components/navMain/user/add_user"], resolve),
        meta: { title: "添加用户", keepAlive: true },
        name: "add_user",
      },
      {
        path: "/statistics_list",
        component: (resolve) =>
          require(["@/components/navMain/company/statistics_list"], resolve),
        meta: { title: "业绩统计", keepAlive: false },
        name: "statistics_list",
      },
      {
        path: "/sale_company_list",
        component: (resolve) =>
          require(["@/components/navMain/company/sale_company_list"], resolve),
        meta: { title: "销售公司", id: 14, keepAlive: false },
        name: "sale_company_list",
      },
      {
        path: "/project_company_list",
        component: (resolve) =>
          require([
            "@/components/navMain/company/project_company_list",
          ], resolve),
        meta: { title: "项目公司", id: 15, keepAlive: false },
        name: "project_company_list",
      },
      {
        path: "/company_rules_list",
        component: (resolve) =>
          require(["@/components/navMain/company/company_rules_list"], resolve),
        meta: { title: "公司规则", keepAlive: true },
        name: "company_rules_list",
      },
      // 企微路由
      {
        path: "/enterprise_label",
        component: (resolve) =>
          require(["@/components/navMain/site/enterprise_label"], resolve),
        meta: { title: "企业标签", keepAlive: true },
        name: "enterprise_label",
      },
      {
        path: "/department_list",
        component: (resolve) =>
          require(["@/components/navMain/site/department_list"], resolve),
        meta: { title: "部门列表", keepAlive: false },
        name: "department_list",
      },
      {
        path: "/member_list",
        component: (resolve) =>
          require(["@/components/navMain/site/member_list"], resolve),
        meta: { title: "成员列表", keepAlive: false },
        name: "member_list",
      },
      {
        path: "/customer_label",
        component: (resolve) =>
          require(["@/components/navMain/site/customer_label"], resolve),
        meta: { title: "客户标签", keepAlive: false },
        name: "customer_label",
      },
      {
        path: "/customer_list",
        component: (resolve) =>
          require(["@/components/navMain/site/customer_list"], resolve),
        meta: { title: "客户列表", keepAlive: false },
        name: "customer_list",
      },
      {
        path: "/customer_base",
        component: (resolve) =>
          require(["@/components/navMain/site/customer_base"], resolve),
        meta: { title: "客户群", keepAlive: false },
        name: "customer_base",
      },
      {
        path: "/app_message",
        component: (resolve) =>
          require(["@/components/navMain/site/app_message"], resolve),
        meta: { title: "应用消息", keepAlive: false },
        name: "app_message",
      },
      {
        path: "/material_management",
        component: (resolve) =>
          require(["@/components/navMain/site/material_management"], resolve),
        meta: { title: "素材管理", keepAlive: false },
        name: "material_management",
      },
      // {
      //   path: "/customer_service",
      //   component: () =>
      //     import("@/components/navMain/site/wxwork/customer_service"),
      //   meta: { title: "素材管理", keepAlive: true },
      //   name: "微信客服",
      // },
      {
        path: "/customer_service",
        component: () =>
          import("@/components/navMain/site/wxwork/customer_service"),
        meta: { title: "账号管理", keepAlive: false },
        name: "customer_service",
      },
      {
        path: "/receptionist",
        component: () =>
          import("@/components/navMain/site/wxwork/receptionist"),
        meta: { title: "接待人员", keepAlive: false },
        name: "receptionist",
      },
      {
        path: "/welcome_words",
        component: (resolve) =>
          require(["@/components/navMain/site/welcome_words"], resolve),
        meta: { title: "欢迎语", keepAlive: false },
        name: "welcome_words",
      },
      {
        path: "/group_welcome_words",
        component: (resolve) =>
          require(["@/components/navMain/site/group_welcome_words"], resolve),
        meta: { title: "群欢迎语", keepAlive: false },
        name: "group_welcome_words",
      },
      {
        path: "/auth_app_list",
        component: (resolve) =>
          require(["@/components/navMain/setup/auth_app_list"], resolve),
        meta: { title: "应用授权", id: 8, keepAlive: false },
        name: "auth_app_list",
      },
      {
        path: "/file_list",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/file_list"], resolve),
        meta: { title: "附件列表", keepAlive: false },
        name: "file_list",
      },
      {
        path: "/company_file_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/company/file_list",
          ], resolve),
        meta: { title: "附件列表", keepAlive: false },
        name: "file_list",
      },
      {
        path: "/permission_list",
        component: (resolve) =>
          require(["@/components/navMain/permission/permission_list"], resolve),
        meta: { title: "角色管理", keepAlive: false },
        name: "permission_list",
      },
      {
        path: "/newregulation",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/bandoned_public/newdrop_the_public_rule"], resolve),
        meta: { title: "掉公规则", keepAlive: false },
        name: "newregulation",
      },
      {
        path: "/cluetransfer",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/cluetransfer"], resolve),
        meta: { title: "线索推送", keepAlive: false },
        name: "cluetransfer",
      },
      {
        path: "/alldirectories",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/thirdparty/alldirectories"], resolve),
        meta: { title: "同步记录", keepAlive: false },
        name: "alldirectories",
      },
      {
        path: "/floor_list",
        component: (resolve) =>
          require(["@/components/navMain/mainComponents/floor_list"], resolve),
        meta: { title: "楼号管理", keepAlive: false },
        name: "floor_list",
      },
      {
        path: "/feedback",
        component: (resolve) =>
          require(["@/components/navMain/setup/feedback"], resolve),
        meta: { title: "问题反馈", keepAlive: false },
        name: "feedback",
      },
      {
        path: "/live_list",
        component: (resolve) =>
          require(["@/components/navMain/live/live_list"], resolve),
        meta: { title: "直播列表", keepAlive: false },
        name: "live_list",
      },
      {
        path: "/comment_list",
        component: (resolve) =>
          require(["@/components/navMain/comment/comment_list"], resolve),
        meta: { title: "评论审核", keepAlive: false },
        name: "comment_list",
      },
      {
        path: "/presell_list",
        component: (resolve) =>
          require(["@/components/navMain/presell/presell_list"], resolve),
        meta: { title: "预售管理", keepAlive: false },
        name: "presell_list",
      },
      {
        path: "/custom_nav_link",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/custom_nav_link",
          ], resolve),
        meta: { title: "导航跳转", keepAlive: false },
        name: "custom_nav_link",
      },
      {
        path: "/price_range_list",
        component: (resolve) =>
          require([
            "@/components/navMain/mainComponents/price_range_list",
          ], resolve),
        meta: { title: "价格管理", keepAlive: false },
        name: "price_range_list",
      },
      {
        path: "/m_setting",
        component: (resolve) =>
          require(["@/components/navMain/mobile/m_setting"], resolve),
        meta: { title: "移动端设置", keepAlive: false },
        name: "m_setting",
      },
      {
        path: "/crm_customer_information",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_information"], resolve),
        meta: { title: "流转客", keepAlive: true },
        name: "crm_customer_information",
      },
      {
        path: "/crm_information_detail",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_information_detail"], resolve),
        meta: { title: "流转客客户详情", keepAlive: true },
        name: "crm_information_detail",
      },
      {
        path: "/crm_information_follow",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_infromation_follow"], resolve),
        meta: { title: "流转视图", keepAlive: true },
        name: "crm_information_follow",
      },
      {
        path: "/reportingList",
        component: (resolve) =>
          require(["@/components/tplus/tSelect/reportingList"], resolve),
        meta: { title: "报备视图", keepAlive: true },
        name: "reportingList",
      },
      // crm
      {
        path: "/crm_customer_my_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_my_list"], resolve),
        meta: { title: "我的客户", keepAlive: true, scrollTop: 0 },
        name: "crm_customer_my_list",
      },
      {
        path: "/crm_customer_my_list_copy",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_my_list_copy"], resolve),
        meta: { title: "我的客户", keepAlive: true },
        name: "crm_customer_my_list_copy",
      },
      {
        path: "/crm_customer_seas_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_seas_list"], resolve),
        meta: { title: "公海客户", keepAlive: false },
        name: "crm_customer_seas_list",
      },
      {
        path: "/crm_customer_clue_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_clue_list"], resolve),
        meta: { title: "新增线索", keepAlive: true, scrollTop: 0 },
        name: "crm_customer_clue_list",
      },
      {
        path: "/crm_Follow_up_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_Follow_up_list"], resolve),
        meta: { title: "经营视图", keepAlive: true, scrollTop: 0 },
        name: "crm_Follow_up_list",
      },

      {
        path: "/crm_potential_customers",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_potential_customers"], resolve),
        meta: { title: "潜在客户", keepAlive: true, scrollTop: 0 },
        name: "crm_potential_customers",
      },
      {
        path: "/crm_customer_setting",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_setting"], resolve),
        meta: { title: "客户设置", keepAlive: true },
        name: "crm_customer_setting",
      },
      {
        path: "/crm_customer_detail",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_detail"], resolve),
        meta: { title: "客户详情", keepAlive: false },
        name: "crm_customer_detail",
      },
      {
        path: "/recording_Details",
        component: (resolve) => require(["@/components/navMain/crm/recording_Details"], resolve),
        meta: { title: "录音详情" },
        name: "recording_Details"
      },
      {
        path: "/crm_data_analysis",
        component: (resolve) => require(["@/components/navMain/crm/crm_data_analysis"], resolve),
        meta: { title: "数据分析" },
        name: "crm_data_analysis"
      },
      {
        path: "/crm_verbal_trick",
        component: (resolve) => require(["@/components/navMain/crm/crm_verbal_trick"], resolve),
        meta: { title: "话术设置" },
        name: "crm_verbal_trick"
      },
      {
        path: "/crm_customer_task_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_task_list"], resolve),
        meta: { title: "导入任务详情列表", keepAlive: false },
        name: "crm_customer_task_list",
      },
      {
        path: "/crm_customer_all_task_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_all_task_list"], resolve),
        meta: { title: "导入任务列表", keepAlive: false },
        name: "crm_customer_all_task_list",
      },
      {
        path: "/crm_information_daily_record",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_information_daily_record"], resolve),
        meta: { title: "流转客日志", keepAlive: false },
        name: "crm_information_daily_record",
      },
      {
        path: "/crm_customer_export_list",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_export_list"], resolve),
        meta: { title: "导出任务列表", keepAlive: false },
        name: "crm_customer_export_list",
      },
      {
        path: "/crm_customer_export_details",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_export_details"], resolve),
        meta: { title: "导出任务详情列表", keepAlive: false },
        name: "crm_customer_export_details",
      },
      {
        path: "/settings_page",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/set_page/settings_page"], resolve),
          meta: {title:"配置", keepAlive: false},
          name:"settings_page"
      },
      {
        path: "/configure_user_list",
        component: (resolve) =>
          require(["@/views/crm/live_room/userlist/configure_user_list"], resolve),
        meta: { title: "用户列表", keepAlive: false },
        name: "configure_user_list",
      },
      {
        path: "/crm_customer_open",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_open"], resolve),
        meta: { title: "开通", keepAlive: true },
        name: "crm_customer_open",
      },
      {
        path: "/language",
        component: (resolve) =>
          require(["@/components/navMain/crm/language"], resolve),
        meta: { title: "话术库", keepAlive: false },
        name: "language",
      },
      {
        path: "/language_copy",
        component: (resolve) =>
          require(["@/components/navMain/crm/language_copy"], resolve),
        meta: { title: "话术库", keepAlive: false },
        name: "language_copy",
      },
      {
        path: "/file",
        component: (resolve) =>
          require(["@/components/navMain/crm/file"], resolve),
        meta: { title: "文件库", keepAlive: false },
        name: "file",
      },
      {
        path: "/crm_customer_group",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_group"], resolve),
        meta: { title: "客户群", keepAlive: false },
        name: "crm_customer_group",
      },
      {
        path: "/crm_customer_group_detail",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_group_detail",
          ], resolve),
        meta: { title: "客户群消息", keepAlive: false },
        name: "crm_customer_group_detail",
      },
      // 消息存档页面
      {
        path: "/crm_customer_message",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_message"], resolve),
        meta: { title: "消息存档", keepAlive: false },
        name: "crm_customer_message",
      },
      // 企微客户列表
      {
        path: "/crm_customer_crmlist",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_crmlist"], resolve),
        meta: { title: "企微客户", keepAlive: false },
        name: "crm_customer_crmlist",
      },
      {
        path: "/crm_customer_department",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_department",
          ], resolve),
        meta: { title: "部门列表", keepAlive: false },
        name: "crm_customer_department",
      },
      {
        path: "/crm_customer_labels",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_labels"], resolve),
        meta: { title: "标签库", keepAlive: false },
        name: "crm_customer_labels",
      },
      {
        path: "/crm_customer_index",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_index"], resolve),
        meta: { title: "企微工作台", keepAlive: true },
        name: "crm_customer_index",
      },
      {
        path: "/crm_customer_personnel",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_personnel"], resolve),
        meta: { title: "人事管理", keepAlive: false },
        name: "crm_customer_personnel",
      },
      {
        path: "/crm_customer_deal",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_deal"], resolve),
        meta: { title: "成交报告", keepAlive: false },
        name: "crm_customer_deal",
      },
      {
        path: "/crm_customer_table",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_table"], resolve),
        meta: { title: "成交管理列表", keepAlive: false },
        name: "crm_customer_table",
      },
      {
        path: "/crm_customer_tableDeatil",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_tableDeatil"], resolve),
        meta: { title: "成交管理列表详情", keepAlive: false },
        name: "crm_customer_tableDeatil",
      },
      {
        path: "/crm_customer_tableDeatil_split",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_tableDeatil_split"], resolve),
        meta: { title: "一键分佣", keepAlive: false },
        name: "/crm_customer_tableDeatil_split",
      },
      {
        path: "/crm_customer_table_request",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_table_request"], resolve),
        meta: { title: "申请单详情", keepAlive: false },
        name: "/crm_customer_table_request",
      },
      {
        path: "/crm_customer_financial",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_financial"], resolve),
        meta: { title: "财务报告", keepAlive: false },
        name: "crm_customer_financial",
      },
      {
        path: "/crm_customer_audit",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_audit"], resolve),
        meta: { title: "审批管理", keepAlive: false },
        name: "crm_customer_audit",
      },
      {
        path: "/crm_batch_add",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_batch_add"], resolve),
        meta: { title: "批量添加好友", keepAlive: false },
        name: "crm_batch_add",
      },
      {
        path: "/crm_customer_deal_detail",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_deal_detail",
          ], resolve),
        meta: { title: "成交报告", keepAlive: false },
        name: "crm_customer_deal_detail",
      },
      {
        path: "/crm_customer_service",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_service"], resolve),
        meta: { title: "客服管理", keepAlive: false },
        name: "crm_customer_service",
      },
      {
        path: "/crm_customer_audit_detail",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_audit_detail",
          ], resolve),
        meta: { title: "审批报告", keepAlive: false },
        name: "crm_customer_audit_detail",
      },
      {
        path: "/crm_customer_audit_house",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_audit_house",
          ], resolve),
        meta: { title: "房源审批", keepAlive: false },
        name: "crm_customer_audit_house",
      },
      {
        path: "/crm_index",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_index"], resolve),
        meta: { title: "CRM工作台", keepAlive: true },
        name: "crm_index",
      },
      {
        path: "/crm_customer_receiver",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_receiver"], resolve),
        meta: { title: "接待人员", keepAlive: false },
        name: "crm_customer_receiver",
      },
      {
        path: "/crm_customer_member_qrcode",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_member_qrcode",
          ], resolve),
        meta: { title: "员工活码", keepAlive: false },
        name: "crm_customer_member_qrcode",
      },
      {
        path: "/member_qrcode_data",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/data/member_qrcode_data",
          ], resolve),
        meta: { title: "员工活码数据", keepAlive: false },
        name: "member_qrcode_data",
      },
      {
        path: "/crm_customer_group_qrcode",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_group_qrcode",
          ], resolve),
        meta: { title: "群活码", keepAlive: true },
        name: "crm_customer_group_qrcode",
      },
      {
        path: "/crm_customer_count_analysis",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/analysis/crm_customer_count_analysis",
          ], resolve),
        meta: { title: "企微客户数分析" },
        name: "crm_customer_count_analysis",
      },
      {
        path: "/crm_labels_analysis",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/analysis/crm_labels_analysis",
          ], resolve),
        meta: { title: "标签分析" },
        name: "crm_labels_analysis",
      },
      {
        path: "/crm_source_analysis",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/analysis/crm_source_analysis",
          ], resolve),
        meta: { title: "来源分析" },
        name: "crm_source_analysis",
      },
      {
        path: "/crm_message_8.1.1",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/message/crm_message_8.1.1",
          ], resolve),
        meta: { title: "消息存档" },
        name: "crm_message_8",
      },
      {
        path: "/crm_message_detail",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/message/crm_message_detail",
          ], resolve),
        meta: { title: "消息详情" },
        name: "crm_message_detail",
      },
      {
        path: "/crm_violate_remind",
        component: (resolve) =>
          require(["@/components/navMain/crm/remind/violate_remind"], resolve),
        meta: { title: "违规提醒" },
        name: "crm_violate_remind",
      },
      {
        path: "/crm_qiwei_help",
        component: (resolve) =>
          require(["@/components/navMain/crm/qiwei_help/index"], resolve),
        meta: { title: "企微助手" },
        name: "crm_qiwei_help",
      },
      {
        path: "/crm_customer_loss",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_loss"], resolve),
        meta: { title: "流失管理" },
        name: "crm_customer_loss",
      },
      {
        path: "/crm_slide",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_slide"], resolve),
        meta: { title: "侧边栏配置" },
        name: "crm_slide",
      },
      {
        path: "/house_list",
        component: (resolve) =>
          require(["@/components/navMain/house/house_list"], resolve),
        meta: { title: "房源列表", keepAlive: true },
        name: "house_list",
      },
      {
        path: "/my_house_list",
        component: (resolve) =>
          require(["@/components/navMain/house/my_house_list"], resolve),
        meta: { title: "我的房源列表", keepAlive: true },
        name: "my_house_list",
      },
      {
        name: "entrust_house",
        path: "/entrust_house",
        component: (resolve) => require(["@/components/navMain/house/entrust_house.vue"], resolve),
        meta: { title: "委托房源", keepAlive: true, keepAliveName: 'entrust_house' },
      },
      {
        path: "/house_setting",
        component: (resolve) =>
          require(["@/components/navMain/house/house_setting"], resolve),
        meta: { title: "房源设置" },
        name: "house_setting",
      },
      {
        path: "/house_audit",
        component: (resolve) =>
          require(["@/components/navMain/house/house_audit"], resolve),
        meta: { title: "房源审批", keepAlive: true, keepAliveName: 'house_audit' },
        name: "house_audit",
      },
      {
        path: "/house_detail",
        component: (resolve) =>
          require(["@/components/navMain/house/house_detail"], resolve),
        meta: { title: "房源详情" },
        name: "house_detail",
      },
      {
        path: "/house_add",
        component: (resolve) =>
          require(["@/components/navMain/house/house_add"], resolve),
        meta: { title: "房源添加" },
        name: "LmAdd",
      },
      {
        path: "/house_edit",
        component: (resolve) =>
          require(["@/components/navMain/house/house_edit"], resolve),
        meta: { title: "房源维护" },
        name: "house_edit",
      },
      {
        path: "/crm_customer_group_sop",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_group_sop"], resolve),
        meta: { title: "群SOP" },
        name: "crm_customer_group_sop",
      },
      {
        path: "/crm_customer_user_sop",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_member_sop",
          ], resolve),
        meta: { title: "员工SOP" },
        name: "crm_customer_user_sop",
      },
      {
        path: "/crm_customer_sop_log",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_sop_log"], resolve),
        meta: { title: "群SOP发送记录" },
        name: "crm_customer_sop_log",
      },
      {
        path: "/crm_customer_member_sop_log",
        component: (resolve) =>
          require([
            "@/components/navMain/crm/crm_customer_member_sop_log",
          ], resolve),
        meta: { title: "SOP发送记录" },
        name: "crm_customer_member_sop_log",
      },
      {
        path: "/crm_customer_business",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_customer_business"], resolve),
        meta: { title: "智慧经营" },
        name: "crm_customer_business",
      },
      {
        path: "/crm_customer_business_setting",
        component: (resolve) =>
          require(["@/components/navMain/crm/business/setting"], resolve),
        meta: { title: "智慧经营" },
        name: "crm_customer_business_setting",
      },
      {
        path: "/crm_house_open",
        component: (resolve) =>
          require(["@/components/navMain/crm/crm_house_open"], resolve),
        meta: { title: "开通房源" },
        name: "crm_house_open",
      },
      {
        path: "/vr_order",
        component: (resolve) =>
          require(["@/components/navMain/crm/vr_order"], resolve),
        meta: { title: "vr订单" },
        name: "vr_order",
      },
      // crm

      //我的报备客户
      {
        path: "/my_report",
        component: (resolve) =>
          require(["@/components/navMain/report/my_report"], resolve),
        meta: { title: "新房查看详情" },
        name: "my_report"
      },
      //报备客户
      {
        path: "/report_client",
        component: (resolve) =>
          require(["@/components/navMain/report/report_client"], resolve),
        meta: { title: "报备客户" },
        name: "report_client"
      },
      //团队分组
      {
        path: "/team_group",
        component: (resolve) =>
          require(["@/components/navMain/report/team_group"], resolve),
        meta: { title: "团队分组" },
        name: "team_group"
      },
      {
        path: "/foreverMedias",
        component: (resolve) =>
          require(["@/components/navMain/foreverMedias/foreverMedias"], resolve),
        meta: { title: "永久素材" },
        name: "foreverMedias"
      },

      // 我的企微助理
      {
        path: "/my_sop",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_sop/my_sop"], resolve),
        meta: { title: "我的sop" },
        name: "my_sop"
      }
      ,
      {
        path: "/my_language",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_language/my_language"], resolve),
        meta: { title: "我的话术库" },
        name: "my_language"
      }
      ,
      {
        path: "/my_welcome_words",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_welcome_words/my_welcome_words"], resolve),
        meta: { title: "我的欢迎语" },
        name: "my_welcome_words"
      }
      ,
      {
        path: "/my_qrcode",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_qrcode/my_qrcode"], resolve),
        meta: { title: "我的活码" },
        name: "my_qrcode"
      }
      ,
      {
        path: "/my_customer",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_customer/my_customer"], resolve),
        meta: { title: "我的客户" },
        name: "my_customer"
      }
      ,
      {
        path: "/my_loss_customer",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_loss_customer/my_loss_customer"], resolve),
        meta: { title: "我的流失客户" },
        name: "my_loss_customer"
      }
      ,
      {
        path: "/my_customer_group",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_customer_group/my_customer_group"], resolve),
        meta: { title: "我的客户群" },
        name: "my_customer_group"
      }
      ,
      {
        path: "/my_group_sop",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_group_sop/my_group_sop"], resolve),
        meta: { title: "我的群SOP" },
        name: "my_group_sop"
      }
      ,
      {
        path: "/my_group_qrcode",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_group_qrcode/my_group_qrcode"], resolve),
        meta: { title: "我的群活码" },
        name: "my_group_qrcode"
      }
      ,
      {
        path: "/my_foreverMedias",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_foreverMedias/my_foreverMedias"], resolve),
        meta: { title: "我的永久素材" },
        name: "my_foreverMedias"
      }
      ,
      {
        path: "/my_file",
        component: (resolve) =>
          require(["@/components/navMain/crm/my_help/my_file/my_file"], resolve),
        meta: { title: "我的文件库" },
        name: "my_file"
      },
      {
        path: "/Data_center",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/Data_center"], resolve),
        meta: { title: "数据中台" },
        name: "Data_center"
      },
      {
        path: "/Big_Data_Panel",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/Big_Data_Panel"], resolve),
        meta: { title: "BI数据面板" },
        name: "Big_Data_Panel"
      },
      {
        path: "/Transaction_conversion",
        component: (resolve) =>
          require(["@/components/navMain/crm/components/Transaction_conversion"], resolve),
        meta: { title: "成交转化" },
        name: "Transaction_conversion"
      },
      {
        path: "/outbound",
        component: (resolve) =>
          require(["@/components/navMain/crm/outbound/index"], resolve),
        meta: { title: "数据看板" },
        name: "outbound"
      }
      ,
      {
        path: "/outbound1",
        component: (resolve) =>
          require(["@/components/navMain/crm/outbound/index1"], resolve),
        meta: { title: "数据看板" },
        name: "outbound"
      }
      ,
      {
        path: "/outbound_task",
        component: (resolve) =>
          require(["@/components/navMain/crm/outbound/outbound_task"], resolve),
        meta: { title: "我的线索包" },
        name: "outbound_task"
      }
      ,
      {
        path: "/setting_clue",
        component: (resolve) =>
          require(["@/components/navMain/crm/outbound/setting_clue"], resolve),
        meta: { title: "导入线索" },
        name: "setting_clue"
      }
      ,
      {
        path: "/tel_record",
        component: (resolve) =>
          require(["@/components/navMain/crm/outbound/tel_record"], resolve),
        meta: { title: "录音质检" },
        name: "tel_record"
      }
      ,
      {
        path: "/dial_record",
        component: (resolve) =>
          require(["@/components/navMain/crm/outbound/dial_record"], resolve),
        meta: { title: "我的拨打" },
        name: "dial_record"
      }
      ,
      {
        path: "/douyin_index",
        component: (resolve) =>
          require(["@/components/navMain/douyin/index"], resolve),
        meta: { title: "风向标" },
        name: "douyin_index"
      }
      ,
      {
        path: "/douyin_add",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_add"], resolve),
        meta: { title: "抖音发布" },
        name: "douyin_index"
      },
      {
        path: "/douyin_add_sucai",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_add_sucai"], resolve),
        meta: { title: "抖音素材" },
        name: "douyin_index"
      }
      ,
      {
        path: "/douyin_data_list",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_data_list"], resolve),
        meta: { title: "数据中台" },
        name: "douyin_data_list"
      }
      ,
      {
        path: "/douyin_circulate_for_perusal",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_circulate_for_perusal"], resolve),
        meta: { title: "客户线索" },
        name: "douyin_circulate_for_perusal"
      }
      ,
      {
        path: "/lookdatalist",
        component: (resolve) =>
          require(["@/components/navMain/douyin/lookdatalist"], resolve),
        meta: { title: "带看视图" },
        name: "lookdatalist"
      }
      ,
      {
        path: "/Followupview",
        component: (resolve) =>
          require(["@/components/navMain/douyin/Followupview"], resolve),
        meta: { title: "回访视图" },
        name: "Followupview"
      }
      ,
      {
        path: "/maintenanceview",
        component: (resolve) =>
          require(["@/components/navMain/douyin/maintenanceview"], resolve),
        meta: { title: "维护视图" },
        name: "maintenanceview"
      }
      ,
      {
        path: "/ananchorpresent",
        component: (resolve) =>
          require(["@/components/navMain/douyin/ananchorpresent"], resolve),
        meta: { title: "主播视图" },
        name: "ananchorpresent"
      }
      ,
      {
        path: "/ananchorpresent_copy",
        component: (resolve) =>
          require(["@/components/navMain/douyin/ananchorpresent_copy"], resolve),
        meta: { title: "主播视图copy" },
        name: "ananchorpresent_copy"
      }
      ,
      {
        path: "/wanderabout",
        component: (resolve) =>
          require(["@/components/navMain/douyin/wanderabout"], resolve),
        meta: { title: "流转数据" },
        name: "wanderabout"
      }
      ,
      {
        path: "/reportforms",
        component: (resolve) =>
          require(["@/components/navMain/douyin/reportforms"], resolve),
        meta: { title: "报表数据" },
        name: "reportforms"
      }
      ,
      {
        path: "/douyin_authorize",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_authorize"], resolve),
        meta: { title: "账号矩阵" },
        name: "douyin_authorize"
      }
      ,
      {
        path: "/douyin_project",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_project"], resolve),
        meta: { title: "项目列表" },
        name: "douyin_project"
      }
      ,
      {
        path: "/douyin_videos",
        component: (resolve) =>
          require(["@/components/navMain/douyin/douyin_videos"], resolve),
        meta: { title: "我的作品" },
        name: "douyin_videos"
      },
      {
        path: "/data_board",
        component: (resolve) => require(["@/components/navMain/region/data_board"], resolve),
        meta: { title: "活动名称" },
        name: "data_board"
      },
      {
        path: "/Regional_expansion",
        component: (resolve) => require(["@/components/navMain/region/regional_expansion"], resolve),
        meta: { title: "区域拓客地图" },
        name: "Regional_expansion"
      },
      {
        path: "/map_mode",
        component: (resolve) => require(["@/components/navMain/region/map_mode"], resolve),
        meta: { title: "区域拓客地图模式" },
        name: "map_mode"
      },
      {
        path: "/spring_breeze",
        component: (resolve) => require(["@/components/navMain/region/spring_breeze"], resolve),
        meta: { title: "春风里拓客" },
        name: "spring_breeze"
      },
      {
        path: "/new_circulation",
        component: (resolve) => require(["@/components/navMain/region/new_circulation"], resolve),
        meta: { title: "新增" },
        name: "new_circulation"
      },
      {
        path: "/perusal",
        component: (resolve) => require(["@/components/navMain/region/perusal"], resolve),
        meta: { title: "传阅" },
        name: "perusal"
      },
      {
        path: "/tDouyinSetting",
        component: (resolve) => require(["@/components/navMain/tdouyin_setting/index"], resolve),
        meta: { title: "抖音设置" },
        name: "tDouyinSetting"
      },
      {
        path: "/webVr_list",
        component: (resolve) => require(["@/components/navMain/webVr/webVr_list"], resolve),
        meta: { title: "全景视频" },
        name: "webVr_list"
      },
      {
        //   path: "/material_list",
        //   component: (resolve) => require(["@/components/navMain/webVr/material_list"], resolve),
        //   meta: { title: "素材管理" },
        //   name: "material_list"
        // }, {
        path: "/panoramaMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/panoramaMaterial_list"], resolve),
        meta: { title: "全景图片" },
        name: "panoramaMaterial_list"
      },
      {
        path: "/pictureMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/pictureMaterial_list"], resolve),
        meta: { title: "图片素材" },
        name: "pictureMaterial_list"
      },
      {
        path: "/Region_modification",
        component: (resolve) => require(["@/components/navMain/region/Region_modification"], resolve),
        meta: { title: "拓客区域修改" },
        name: "Region_modification"
      },
      {
        path: "/spring_breeze",
        component: (resolve) => require(["@/components/navMain/region/spring_breeze"], resolve),
        meta: { title: "春风里拓客" },
        name: "spring_breeze"
      },
      {
        path: "/audioMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/audioMaterial_list"], resolve),
        meta: { title: "音频素材" },
        name: "audioMaterial_list"
      },
      {
        path: "/videoMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/videoMaterial_list"], resolve),
        meta: { title: "视频素材" },
        name: "videoMaterial_list"
      },
      {
        path: "/patchMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/patchMaterial_list"], resolve),
        meta: { title: "贴片视频素材" },
        name: "patchMaterial_list"
      },
      {
        path: "/im",
        component: (resolve) => require(["@/components/navMain/im/Im"], resolve),
        meta: { title: "聊天" },
        name: "im"
      },
      // {
      //   path: "/tDouyinSetting",
      //   component: (resolve) => require(["@/components/navMain/tdouyin_setting/index"], resolve),
      //   meta: { title: "抖音设置" },
      //   name: "tDouyinSetting"
      // },
      {
        path: "/webVr_list",
        component: (resolve) => require(["@/components/navMain/webVr/webVr_list"], resolve),
        meta: { title: "全景视频" },
        name: "webVr_list"
      },
      {
        path: "/vr_edit",
        component: (resolve) => require(["@/components/navMain/webVr/vr_edit"], resolve),
        meta: { title: "vr编辑" },
        name: "vr_edit"
      },
      {
        path: "/panoramaMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/panoramaMaterial_list"], resolve),
        meta: { title: "全景图片" },
        name: "panoramaMaterial_list"
      },
      {
        path: "/pictureMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/pictureMaterial_list"], resolve),
        meta: { title: "图片素材" },
        name: "pictureMaterial_list"
      },
      {
        path: "/audioMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/audioMaterial_list"], resolve),
        meta: { title: "音频素材" },
        name: "audioMaterial_list"
      },
      {
        path: "/videoMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/videoMaterial_list"], resolve),
        meta: { title: "视频素材" },
        name: "videoMaterial_list"
      },
      {
        path: "/patchMaterial_list",
        component: (resolve) => require(["@/components/navMain/webVr/patchMaterial_list"], resolve),
        meta: { title: "贴片视频素材" },
        name: "patchMaterial_list"
      },
      {
        path: "/add_circularize",
        component: (resolve) => require(["@/components/navMain/region/add_circularize"], resolve),
        meta: { title: "添加传阅" },
        name: "add_circularize"
      },
      {
        path: "/Editorial_circulation",
        component: (resolve) => require(["@/components/navMain/region/Editorial_circulation"], resolve),
        meta: { title: "修改传阅" },
        name: "Editorial_circulation"
      },
      {
        path: "/Backstage_Members",
        component: (resolve) => require(["@/components/navMain/region/Backstage_Members"], resolve),
        meta: { title: "后台会员" },
        name: "Backstage_Members"
      },
      {
        path: "/add_member",
        component: (resolve) => require(["@/components/navMain/region/add_member"], resolve),
        meta: { title: "添加会员" },
        name: "add_member"
      },
      {
        path: "/Member_Editor",
        component: (resolve) => require(["@/components/navMain/region/Member_Editor"], resolve),
        meta: { title: "修改会员" },
        name: "Member_Editor"
      },
      {
        path: "/collarmaplist",
        component: (resolve) => require(["@/components/navMain/collarmap/collarmaplist"], resolve),
        meta: { title: "资料插件列表" },
        name: "collarmaplist"
      },
      {
        path: "/packagepage",
        component: (resolve) =>
          require(["@/components/navMain/collarmap/zhiliaowrap"], resolve),
        meta: { title: "资料包", keepAlive: false },
        name: "packagepage",
      },
      {
        path: "/a",
        component: (resolve) => require(["@/components/navMain/region/a"], resolve),
        meta: { title: "测试" },
        name: "a"
      },
      // {
      //   path: "/test",
      //   name: "测试",
      //   component: (resolve) => require(["@/components/navMain/crm/components/test"], resolve)
      // },test
    ],


  },


  {
    path: "/login",
    name: "登录",
    component: login,
  },
  {
    path: "/login1",
    name: "测试123",
    component: login1,
  },
  {
    path: "/companyLogin",
    name: "企业登录",
    component: companyLogin,
  },
  {
    path: "/middle_page",
    component: (resolve) =>
      require(["@/components/navMain/house/middle"], resolve),
    meta: { title: "中转页" },
    name: "middle_page",
  },
  {
    path: "/jump",
    component: (resolve) =>
      require(["@/page/jump"], resolve),
    meta: { title: "跳转中" },
    name: "授权登录",
    title: "授权登录"
  },
  {
    path: "/403",
    component: (resolve) => require(["@/components/navMain/user/403"], resolve),
    meta: { title: "403" },
    name: "403",
  },
  {
    path: "/404",
    component: (resolve) => require(["@/components/navMain/user/404"], resolve),
    meta: { title: "404" },
    name: "404",
  },
  {
    path: "/loading",
    component: (resolve) => require(["@/page/loading"], resolve),
    meta: { title: "请稍后" },
    name: "请稍后",
  },
  {
    path: "/newloading",
    component: (resolve) => require(["@/page/newloading"], resolve),
    meta: { title: "请稍后" },
    name: "请稍后",
  },
  {
    path: "/wx_work_auth",
    component: (resolve) => require(["@/page/wx_work_auth"], resolve),
    meta: { title: "企业微信授权" },
    name: "企业微信授权",
  },
  {
    path: "/wx_work_auth_t",
    component: (resolve) => require(["@/page/wx_work_auth_t"], resolve),
    meta: { title: "企业微信授权" },
    name: "企业微信授权",
  },
  {
    path: "/wx_work_auth_report",
    component: (resolve) => require(["@/page/wx_work_auth_report"], resolve),
    meta: { title: "企业微信授权" },
    name: "企业微信授权",
  },
  {
    path: "/wx_work_auth_three",
    component: (resolve) => require(["@/page/wx_work_auth_three"], resolve),
    meta: { title: "企业微信授权" },
    name: "企业微信授权",
  },
  {
    path: "/auth_third_crm",
    component: (resolve) => require(["@/page/auth_third_crm"], resolve),
    meta: { title: "第三方授权" },
    name: "第三方授权",
  },


];

export default new Router({
  mode: "hash",
  routes: routerLists.map(list => {
    if (list.path == "/" && list.children) {
      list.children = list.children.concat(customerRouter)
    }
    return list;
  }).concat(customerFullPageRouter),
});
