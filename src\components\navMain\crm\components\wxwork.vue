<template>
  <div>
    <div class="div row" style="margin-bottom:20px">
      <el-input
        style="width:200px"
        v-model="params.keywords"
        placeholder="请输入"
      ></el-input>
      <el-button type="primary" @click="onSearch">搜索</el-button>
    </div>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
import myTable from "@/components/components/my_table.vue";
export default {
  components: {
    myTable,
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        keywords: "",
        total: 0,
      },
      is_table_loading: false,
      tableData: [],
      table_header: [
        {
          prop: "id",
          label: "ID",
          width: "100px",
        },
        {
          label: "用户",
          render: (h, data) => {
            return (
              <div class="div crmuserbox row" style="justify-content: center;">
                <img width="60" height="60" src={data.row.avatar} />
                <span>{data.row.name}</span>
              </div>
            );
          },
        },
        {
          label: "性别",
          render: (h, data) => {
            return (
              <span>
                {data.row.gender == 1 ? "男" : data.row.gender == 2 ? "女" : ""}
              </span>
            );
          },
        },
        {
          label: "类型",
          render: (h, data) => {
            return (
              <span>{data.row.type == 1 ? "微信用户" : "企业微信用户"}</span>
            );
          },
        },
        {
          label: "企业名称",
          prop: "corp_name",
        },
      ],
    };
  },
  created() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.is_table_loading = true;
      this.$http.getWxWorkUserData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    onSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
  },
};
</script>

<style></style>
