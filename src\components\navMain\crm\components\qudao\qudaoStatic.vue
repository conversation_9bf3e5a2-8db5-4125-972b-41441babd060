<template>
  <div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55"> </el-table-column>   
      -->
      <!-- <el-table-column label="ID" min-width="30px" align="center" prop="id">
      </el-table-column> -->
      <el-table-column
        label="咨询会话数"
        min-width="60px"
        align="center"
        v-slot="{ row }"
      >
        <span class="name">{{ row.session_cnt }}</span>
      </el-table-column>
      <el-table-column
        label="咨询客户数"
        min-width="60px"
        align="center"
        v-slot="{ row }"
      >
        <span class="name">{{ row.customer_cnt }}</span>
      </el-table-column>
      <el-table-column
        label="咨询消息总数"
        min-width="60px"
        align="center"
        v-slot="{ row }"
      >
        <span class="name">{{ row.customer_msg_cnt }}</span>
      </el-table-column>
      <el-table-column
        label="升级服务客户数"
        min-width="60px"
        align="center"
        v-slot="{ row }"
      >
        <span class="name">{{ row.upgrade_service_customer_cnt }}</span>
      </el-table-column>
      <el-table-column
        label="智能回复会话数"
        min-width="60px"
        align="center"
        v-slot="{ row }"
      >
        <span class="name">{{ row.ai_session_reply_cnt }}</span>
      </el-table-column>
      <el-table-column
        label="统计时间"
        min-width="60px"
        align="center"
        v-slot="{ row }"
      >
        <span class="name">{{ row.stat_time }}</span>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10
      },
      total: 0,
      is_table_loading: false
    }
  },
  props: ['id'],
  created () {
    this.getData()
  },
  methods: {
    onPageChange (current_page) {
      this.params.page = current_page;
      this.getData();
    },
    getData () {
      this.is_table_loading = true
      // 没有分页  
      this.$http.getCrmSceneStaticList({ params: { kf_id: this.id } }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data
        }
      });
    }
  }
}
</script>

<style>
</style>