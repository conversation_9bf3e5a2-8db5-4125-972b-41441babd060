<template>
  <div class="div row empty" v-loading="loading">
    <template v-if="!loading">
      <img class="img" :src="img" alt="" />
      <div class="desc">{{ desc }}</div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    img: {
      type: String,
      default:
        "https://img.tfcs.cn/backup/static/admin/crm/index/empty.png",
    },
    desc: {
      type: String,
      default: "暂无数据",
    },
    loading: { type: Boolean, default: false }
  },
};
</script>

<style scoped lang="scss">
.empty {
  flex-direction: column;
  align-items: center;
  min-height: 80px;

  .img {}

  .desc {
    color: #8a929f;
  }
}
</style>
