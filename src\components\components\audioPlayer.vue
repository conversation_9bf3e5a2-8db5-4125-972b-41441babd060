<template>
    <div>
        <!-- 如果是房源详情-跟进记录 -->
        <audio v-if="select == 'houseFollow'" :src="activity.house_follow.record_url" @timeupdate="updateProgress" controls
            ref="audioRef" style="display: none" class="audios"></audio>
        <!-- 如果是客户详情-跟进记录 -->
        <audio v-if="select == 'CustomerFollow'" :src="activity.call_record.record_url" @timeupdate="updateProgress"
            controls ref="audioRef" style="display: none" class="audios"></audio>
        <div class="allaudio">
            <div class="audio-right flex-1">
                <i @click="playAudio" class="dialogAudioPlay el-icon-video-play">
                </i>
                <span class="audio_text">通话录音</span>
                <div class="audio-time" style="min-height: 10px">
                    <span class="audio-length-current" id="audioCurTime">
                        {{ audioStart }}
                    </span>
                </div>
                <div class="progress-bar-bg_c">
                    <div class="progress-bar-bg" id="progressBarBg" v-dragto="setAudioIcon"></div>
                    <div class="progress-bar" id="progressBar" @click="setProgress"></div>
                    <div class="progress-btn" id="progressBtn" @mousedown="handleProgress"></div>
                </div>
                <div class="audio-time" style="min-height: 10px">
                    <span v-if="select == 'CustomerFollow'" class="audio-length-total">{{ activity.call_record.duration |
                                            formatTime }}</span>
                    <span v-if="select == 'houseFollow'" class="audio-length-total">{{ duration }}</span>
                </div>
                <div class="volume">
                    <div @click.stop="()=>{return false}" class="volume-progress" v-show="audioHuds">
                        <div class="volume-bar-bg" id="volumeBarBg" v-adjuster="handleShowMuteIcon">
                            <div class="volume-bar" id="volumeBar"></div>
                        </div>
                    </div>
                </div>
                <div v-if="healthdata" class="HealthQuality">
                    <div :class="{
                          'green': healthdata.health.value === '健康',
                          'red': healthdata.health.value === '违规',
                        }"></div>
                        <span class="HealthQuality_text">
                            {{healthdata.health.value}}
                        </span>
                        <el-tooltip placement="bottom" effect="light">
                            <div slot="content" style="width: 300px;line-height: 20px;" >
                                {{healthdata.health.desc}}
                            </div>
                            <i style="margin-left: 8px;cursor: pointer;" :class="{
                               'greyicon': healthdata.health.value === '健康',
                               'redicon': healthdata.health.value === '违规',
                            }" class="el-icon-warning-outline"></i>
                        </el-tooltip>
                      
                </div>
                <div v-else class="AIstyle">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/AIhuise.png" alt="" @click="toai(activity)">
                </div>
            </div>
            <div class="audiobtn">
                <!-- <div class="flex-row items-center" v-if="activity.call_record.is_recognition==1">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/Frame.png" alt=""> -->
                    <!-- 可以跳转，但样式未修改 -->
                <div class="flex-row items-center" v-if="activity.call_record.call_type==4">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/Frame.png" alt="" @click="gorobot">
                </div>
            </div>
        </div>

    </div>
</template>
<script>
export default {
    props: {
        activity: {
            type: Object,
            default: () => {}
        },
        detail: {
            type: Object,
            default: () => {}
        },
        select: {
            type: String,
            default: ""
        },
        // 当前客户id
        info_id: {
            type: [String, Number],
            default: ''
        },
        // 当前客户是1公海/2私客
        type: {                                                                          
            type: [String, Number],
            default: ''
        },
        healthdata: {
            type: Object,
            default: () => {}
        },

    },
    data() {
        return {
            audioStatus: 'play',
            audioStart: '00:00',
            duration: '00:00',
            audioVolume: 0.5,
            audioHuds: false,
            componentIndex: 0,
        }
    },
    directives: {
        dragto: {
            inserted: function(el, binding, vnode) {
                el.addEventListener('click', (e) => {
                    vnode.context.componentIndex = e.target.index
                    let wdiv = document.querySelectorAll(".progress-bar-bg")[vnode.context.componentIndex].clientWidth;
                    let audio = vnode.context.$refs.audioRef;
                    // 只有录音开始播放后才可以调节，已经播放过但暂停了的也可以
                    let ratemin = e.offsetX / wdiv;
                    let rate = ratemin * 100;
                    document.querySelectorAll('.progress-bar')[vnode.context.componentIndex].style.width = rate + '%';
                    audio.currentTime = audio.duration * ratemin;
                    audio.pause();
                    document.querySelectorAll(".dialogAudioPlay")[vnode.context.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                    binding.value();
                }, false)
            }
        },
        adjuster: {
            inserted: function(el, binding, vnode) {
                el.addEventListener('click', (e) => {
                    let hdiv = document.getElementById('volumeBaeBg').clientHeight;
                    let audio = vnode.context.$refs.audioRef;
                    // 只有音乐开始播放后才可以调节，已经播放过但暂停了的也可以
                    let ratemin = e.offsetY / hdiv;
                    let rate = ratemin * 100;
                    document.getElementById('volumeBar').style.height = rate + '%';
                    audio.volume = ratemin;
                    binding.value(rate / 100);
                }, false)
            }
        }
    },
    mounted() {
        this.fetch();
        // console.log(this.activity,"activety");
        // console.log(this.detail.house_manager, "detail");
        // console.log(this.index)
        let detail = document.getElementsByClassName("dialogAudioPlay");
        let barBg = document.querySelectorAll(".progress-bar-bg");
        let Bar = document.querySelectorAll(".progress-bar");
        let Btn = document.querySelectorAll(".progress-btn");
        for(let i = 0; i < detail.length; i++) {
            detail[i].index = i;
            barBg[i].index = i;
            Bar[i].index = i;
            Btn[i].index = i;
        }
    },
    filters: {
        formatTime(value) {
            // 将数字转换为分钟和秒钟
            var minutes = Math.floor(value / 60);
            var seconds = value % 60;
            
            // 将分钟和秒钟转换为字符串，并在需要时添加前导零
            var minutesString = minutes.toString().padStart(2, '0');
            var secondsString = seconds.toString().padStart(2, '0');
            
            // 将时间字符串格式化为00:00格式
            return minutesString + ':' + secondsString;
        },
    },
    methods: {
        fetch() {
            let that = this;
            var myVid = this.$refs.audioRef;
            myVid.loop = false;
            // 监听音频播放完毕
            myVid.addEventListener('ended', function() {
                // 显示播放icon
                document.querySelectorAll(".dialogAudioPlay")[that.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                document.querySelectorAll('.progress-bar')[that.componentIndex].style.transition = 'none';
                document.querySelectorAll('.progress-btn')[that.componentIndex].style.transition = 'none';
                document.querySelectorAll('.progress-bar')[that.componentIndex].style.width = '0%'; // 初始化进度条
                document.querySelectorAll('.progress-btn')[that.componentIndex].style.left = '0%';
                that.audioStart = '00:00';
            }, false)
            if(myVid != null) {
                myVid.oncanplay = function() {
                    that.duration = that.transTime(myVid.duration) // 计算音频时长
                }
            }
        },
          // 跳转录音详情
        //   getDeatil() {
        //     console.log(this.activity.record_id,'33333');
        //     this.$goPath(`recording_Details?r_id=${this.activity.record_id}&u_id=${this.info_id}&type=${this.type}`);
        //     sessionStorage.setItem('record_Detail', JSON.stringify(this.activity)); // 存储当前录音信息
        // },
        updateProgress(e) {
            var value = e.target.currentTime / e.target.duration;
            if(document.querySelectorAll('.progress-bar')[this.componentIndex]) {
                document.querySelectorAll('.progress-bar')[this.componentIndex].style.width = value * 100 + '%';
                document.querySelectorAll('.progress-btn')[this.componentIndex].style.left = value * 100 + '%'
                if(e.target.currentTime === e.target.duration) {
                    document.querySelectorAll(".dialogAudioPlay")[this.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                }
            } else {
                document.querySelectorAll(".dialogAudioPlay")[this.componentIndex].className = "dialogAudioPlay el-icon-video-play";
            }
            this.audioStart = this.transTime(this.$refs.audioRef.currentTime)
        },
        playAudio(e) {
            let admin_id = localStorage.getItem("admin_id");
            if(this.detail) {
                if(admin_id == this.activity.crm_user.id || this.detail.house_manager == 1) {
                    this.componentIndex = e.target.index;
                    let recordAudio = this.$refs.audioRef; // 获取audio元素
                    if(recordAudio.paused) {
                        // 让所有播放按钮暂停
                        let audios = document.querySelectorAll(".audios");
                        audios.forEach(item =>{
                            item.pause();
                        })
                        // 让所有按钮样式为暂停样式
                        let audioClass = document.querySelectorAll(".dialogAudioPlay");
                        audioClass.forEach(item => {
                            item.className = "dialogAudioPlay el-icon-video-play";
                        })
                        recordAudio.play();
                        e.target.className = "dialogAudioPlay el-icon-video-pause";
                        document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'width 0.3s linear';
                        document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'left 0.3s linear';
                    } else {
                        recordAudio.pause();
                        e.target.className = "dialogAudioPlay el-icon-video-play";
                        document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
                        document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
                    }
                    this.audioStart = this.transTime(this.$refs.audioRef.currentTime);
                } else {
                    this.$message({
                        message: '暂无权限',
                        type: 'warning'
                    });
                }
            } else {
                this.componentIndex = e.target.index;
                let recordAudio = this.$refs.audioRef; // 获取audio元素
                if(recordAudio.paused) {
                    // 让所有播放按钮暂停
                    let audios = document.querySelectorAll(".audios");
                    audios.forEach(item =>{
                        item.pause();
                    })
                    // 让所有按钮样式为暂停样式
                    let audioClass = document.querySelectorAll(".dialogAudioPlay");
                    audioClass.forEach(item => {
                        item.className = "dialogAudioPlay el-icon-video-play";
                    })
                    recordAudio.play();
                    e.target.className = "dialogAudioPlay el-icon-video-pause";
                    document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'width 0.3s linear';
                    document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'left 0.3s linear';
                } else {
                    recordAudio.pause();
                    e.target.className = "dialogAudioPlay el-icon-video-play";
                    document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
                    document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
                }
                this.audioStart = this.transTime(this.$refs.audioRef.currentTime);
            }
            
        },
        /*
            音频播放时间换算
            @param {number} value - 音频当前播放时间，单位秒
        */ 
       transTime(time) {
        var duration = parseInt(time);
        var minute = parseInt(duration / 60);
        var sec = (duration % 60) + '';
        var isM0 = ':';
        if(minute === 0) {
            minute = '00';
        } else if(minute < 10) {
            minute = '0' + minute;
        }
        if(sec.length === 1) {
            sec = '0' + sec;
        }
        return minute + isM0 + sec
       },
        setAudioIcon() {
            document.querySelectorAll(".dialogAudioPlay")[this.componentIndex].className = "dialogAudioPlay el-icon-video-play";
            document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
            document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
            setTimeout(() => {
                document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
                document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
            },100)
        },
        handleShowMuteIcon(val) {
            console.log(val);
            this.audioVolume = val;
        },
        handleProgress(e) {
            this.componentIndex = e.target.index;
            e.stopPropagation();
            e.preventDefault();
            // 进度条
            let audio = this.$refs.audioRef;
            let progressBar = document.querySelectorAll(".progress-bar")[e.target.index];
            let progressBtn = document.querySelectorAll(".progress-btn")[e.target.index];
            audio.pause();
            document.querySelectorAll(".dialogAudioPlay")[e.target.index].className = "dialogAudioPlay el-icon-video-play";
            document.querySelectorAll(".progress-bar")[e.target.index].style.transition = 'none';
            document.querySelectorAll(".progress-btn")[e.target.index].style.transition = 'none';
            // 获取圆点偏移量
            let progressLeft = e.clientX - progressBtn.offsetLeft;
            // 圆圈绑定拖拽
            let progressX
            document.onmousemove = function(event) {
                event.stopPropagation();
                event = event || window.event;
                // 获取鼠标坐标
                progressX = event.clientX - progressLeft;
                if(progressX <= 0) {
                    // 如果超出范围停止拖动
                    progressX = 0;
                } else if(progressX >= 335) {
                    progressX = 335;
                }
                let BarBgWidth = document.querySelectorAll('.progress-bar-bg')[e.target.index].offsetWidth;
                let ratemin = progressX / BarBgWidth
                if(!isNaN(ratemin)) {
                    audio.currentTime = audio.duration * ratemin;
                }
                progressBtn.style.left = progressX + 'px';
                progressBar.style.width = progressX + 'px';
            }
            document.onmouseup = function(event) {
                event.stopPropagation();
                document.querySelectorAll('.progress-bar')[e.target.index].style.width = progressX + 'px';
                document.querySelectorAll('.progress-btn')[e.target.index].style.left = progressX + 'px';
                // 获取元素的灰色进度条宽度
                let BarBgWidth = document.querySelectorAll('.progress-bar-bg')[e.target.index].offsetWidth;
                let ratemin = progressX / BarBgWidth
                if(!isNaN(ratemin)) {
                    audio.currentTime = audio.duration * ratemin;
                }
                document.querySelectorAll('.progress-bar')[e.target.index].style.transition = 'width 0.3s linear';
                document.querySelectorAll('.progress-btn')[e.target.index].style.transition = 'left 0.3s linear';
                // 取消鼠标移动事件
                document.onmousemove = null;
                // 取消鼠标抬起事件
                document.onmouseup = null;
            }
        },
        setProgress(e) {
            document.querySelectorAll('.progress-bar')[e.target.index].style.transition = 'none';
            document.querySelectorAll('.progress-btn')[e.target.index].style.transition = 'none';
            let wdiv = document.querySelectorAll('.progress-bar-bg')[e.target.index].clientWidth;
            let audio = this.$refs.audioRef;
            let ratemin = e.offsetX / wdiv
            let rate = ratemin * 100;
            document.querySelectorAll('.progress-bar')[e.target.index].style.width = rate + '%';
            audio.currentTime = audio.duration * ratemin;
            audio.pause();
            document.querySelectorAll(".dialogAudioPlay")[e.target.index].className = "dialogAudioPlay el-icon-video-play";
            setTimeout(() => {
                document.querySelectorAll('.progress-bar')[e.target.index].style.transition = 'width 0.3s linear'
                document.querySelectorAll('.progress-btn')[e.target.index].style.transition = 'left 0.3s linear'
            },100)
        },
        //点击跳转到详情页
        gorobot(){
            console.log(this.activity,'33333');
            this.$goPath(`recording_Details?r_id=${this.activity.call_record.record_id}&u_id=${this.info_id}&type=${this.type}`);
            sessionStorage.setItem('record_Detail', JSON.stringify(this.activity)); // 存储当前录音信息
        },
        //打开AI分析通话
        toai(activity){
            this.$emit("toai",activity);
        },
    }
}
</script>
<style scoped lang="scss">
.volume {
    position: relative;

    .volume-progress {
        position: absolute;
        top: -150px;
        width: 32px;
        height: 140px;
        background: #f6f6f6;
        border-radius: 4px;
        padding-top: 10px;

        .volume-bar-bg {
            margin: 0 auto;
            width: 6px;
            height: 120px;
            background: #dcdcdc;
            border-radius: 100px;
            flex: 1;
            position: relative;
            transform: rotate(180deg);
            cursor: pointer;

            .volume-bar {
                width: 6px;
                height: 50%;
                background: #56bf8b;
                border-radius: 100px;
            }
        }
    }
}

.allaudio {
    width: 100%;
    display: flex;
    align-items: center;
}
.HealthQuality{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 10px;
    .green{
        width: 6px;
        height: 6px;
        background: #56bf8b;
        border-radius: 50%;
        margin-right: 6px;
    }
    .red{
        width: 6px;
        height: 6px;
        background: red;
        border-radius: 50%; 
        margin-right: 6px;
    }
    .HealthQuality_text{
        font-size: 14px;   
        color: #8a929f
    }
    .greyicon{
        color: #8a929f;
    }
    .redicon{
        color: red;
    }
}
.AIstyle{
        img{
            margin-left: 10px;
            width: 25px;
            height: 25px;
            margin-top: 11px;
            cursor: pointer;
        }
    }
.audio-right {
    max-width: 478px;
    height: 49px;
    line-height: 49px;
    background: #F8F8F9;
    border-radius: 6px;
    display: flex;
    padding: 0 15px;
    margin-right: 10px;
    margin-top: 13px;

    .audio_text {
        font-size: 12px;
        margin: 0 5px 0 15px;
    }

    .dialogAudioPlay {
        cursor: pointer;
        color: #5c5e66;
        font-size: 24px;
        line-height: 49px;
    }

    .audio-time {
        overflow: hidden;
        font-size: 14px;

        .audio-length-current,
        .audio-length-total {
            color: #8a929f;
        }
    }

    .progress-bar-bg_c {
        flex: 1;
        position: relative;
        margin: 0 10px;

        .progress-bar-bg {
            position: absolute;
            height: 2px;
            top: 50%;
            background-color: #D7D7DA;
            left: 0;
            right: 0;
            transform: translateY(-50%);
            margin-top: -1px;
            cursor: pointer;
        }

        .progress-bar {
            background-color: #315AE6;
            width: 0%;
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            height: 2px;
            border-radius: 5px;
            cursor: pointer;
        }

        .progress-btn {
            width: 16px;
            height: 16px;
            background-color: #FFFFFF;
            border: 4px solid #2350E5;
            border-radius: 50%;
            position: absolute;
            top: 34%;
            box-sizing: border-box;
            cursor: pointer;
        }
    }
}

.audiobtn {
    margin-top: 10px;

    img {
        width: 20px;
        height: 20px;
    }
}
</style>