<template>
	<el-dialog :visible.sync="show" :title="title" width="680px" :modal="showmodal==0">
		<div class="message-logs" ref="messageLogs">
			<div class="loading-wrapper">
				<template v-if="loading">
					<div class="loading" v-loading="loading" element-loading-spinner="el-icon-loading" element-loading-background="transparent"></div>
				</template>
				<template v-else-if="!loaded">
					<span class="btn-more" @click="loadMore">查看更多消息</span>
				</template>
				<template v-else>
					<span>没有更多了</span>
				</template>
			</div>

			<div class="message-list">
				<div class="message-list-item" v-for="item in list" :key="item._key">
					<div class="message-time-line">
						<span class="time" v-if="item.time">{{item.time}}</span>
					</div>
					<div class="message-row" :class="{right: !item.isUser}">
						<div class="user">
							<div class="avatar">
								<img v-if="item.avatar" :src="item.avatar">
								<span v-else class="avatar-word">{{getFirstWord(item.nickname)}}</span>
							</div>
						</div>
						<div class="popper">
							<div class="popper-title" v-if="!item.isUser">{{item.nickname}}</div>
							<div class="popper-content">
								{{item.content}}
								<div class="popper-arrow"></div>
							</div>
						</div>
					</div>
				</div>
			</div>    

		</div>
	</el-dialog>
</template>

<script>
import weixinHttp from "@/utils/weixinHttp";
import Utils from '@/utils/utils';

export default {
	data() {
		return {
			show: false,    
			loading: false,  
			loaded: false,
			title: '',
			params: {          
				per_page: 0,
				last_time: 0,
				wx_client_id: '',
				mobile: '',
				view_from: ''
			},
			list: [],
			showmodal: 0,
			currentRow: null,
			userInfo: null,
			adminList: {}
		}
	},
	mounted(){
		setTimeout(()=>{
			this.$refs.messageLogs.addEventListener('scroll', this.onScroll)
		}, 500)
	},
	beforeDestroy(){
		this.$refs.messageLogs.removeEventListener('scroll', this.onScroll)
	},
	methods: {
		onScroll: Utils.debounce(async function(e){
			if(this.loading || this.loaded){
				return;
			}
			let scrollTop = this.$refs.messageLogs.scrollTop;
			if(scrollTop < 5){
				this.loadMore();
			}
		}, 16.7),
		
		open(row, status){
			if(status){
				this.showmodal = status
			}
			this.title = (row.nick_name || row.kf_uniqid) + '的会话信息';
			this.currentRow = row;
			this.params.wx_client_id = String(row.id) || '';		
			this.params.mobile = String(row.mobile_full) || '';
			this.params.view_from = String(row.view_from) || '';
			this.params.last_time = 0;
			this.loaded = false;
			this.list = [];
			this.userInfo = null;
			this.adminList = {};
			this.getList();
			this.show = true;
			return this;
		},
		
		async loadMore(){
			this.getList();
		},
		
		async getList(){
			if(!this.currentRow) return;
			
			this.loading = true;
			const params = {
				wx_client_id: this.params.wx_client_id,
				mobile: this.params.mobile,
				view_from: this.params.view_from,
				last_time: this.params.last_time
			};
			
			try{
				const res = await weixinHttp.getWeixinShopKfPrivateLetterUserChat(params);
				if(res.status == 200){
					this.loaded = res.data.has_more == 0 ? true : false;
					this.params.last_time = res.data.last_time;
					
					this.userInfo = res.data.wx_client_user || {};
					this.adminList = res.data.admin_wx_list || {};
					
					let keyIndex = this.list.length;
					let lastTime = '', timeReg = new RegExp('^(.*)\:.*$');
					let scrollHeight = this.$refs.messageLogs.scrollHeight;
					
					this.list = (res.data.list || []).map(e => {
						e._key = keyIndex++;
						e.isUser = e.type == 1;
						
						//用户
						if(e.isUser){
							e.avatar = this.userInfo.avatar || '';
							e.nickname = this.userInfo.nick_name || '';
						}else{ //管理员
							let admin = this.adminList[e.kf_uniqId] || {}
							e.avatar = admin.avatar || '';
							e.nickname = admin.nick_name || '客服'
						}
						
						//时间
						let time = e.add_time.replace(timeReg, '$1');
						e.time = lastTime !== time ? time : '';
						lastTime = time;
						return e;
					}).concat(this.list);

					this.$nextTick(()=>{
						this.$refs.messageLogs.scrollTop = this.$refs.messageLogs.scrollHeight - scrollHeight;
					})
				}
			}catch(err){
				console.error('加载私信记录失败:', err);
			}
			this.loading = false;
		},
		
		getFirstWord(str){
			if (!str) return '';
			return str.trim().replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '').substring(0, 1);
		}
	}
}
</script>

<style  scoped lang="scss">
.message-logs{
	height: 80vh;
	max-height: 580px;
	overflow: auto;
	background-color: #f7f7f7;
	border-top: 1px solid #f2f3f5;
	
	.loading-wrapper{
		position: relative;
		color: #b9b9b9;
		font-size: 12px;
		text-align: center;
		height: 42px;
		line-height: 32px;
		
		.btn-more{
			cursor: pointer;
			color: #288ef8;
		}
		
		.loading{
			height: 100%;
		}
	}

	.message-time-line{
		text-align: center;
		margin: 1px 0 15px;
		
		.time{
			cursor: default;
			font-size: 12px;
			font-weight: 400;
			color: #fff;
			padding: 2px 4px;
			border-radius: 2px;
			background-color: #e0e1e2;
		}
	}
	
	.message-row{
		display: flex;
		flex-direction: row;
		box-sizing: border-box;
		padding: 0 30px;
		margin-bottom: 24px;
		
		&.right{
			flex-direction: row-reverse;
			
			.popper{
				margin-right: 12px;
				
				.popper-title{
					text-align: right;
					font-weight: 400;
				}
				
				.popper-content{
					color: #2c2c2c;
					background-color: rgba(229, 238, 255, 1);
				}
				
				.popper-arrow:after{
					left: auto;
					top: 13px;
					right: -6px;
					border-left-width: 6px;
					border-right-color: transparent;
					border-right-width: 0;
					border-left-color: rgba(229, 238, 255, 1);
				}
			}
		}
		
		.avatar{
			width: 36px;
			height: 36px;
			border-radius: 50%;
			background-color: #eee;
			
			img{
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
			
			.avatar-word{
				display: inline-block;
				width: 36px;
				height: 36px;
				border-radius: 50%;
				background-color: #f1f2f3;
				line-height: 36px;
				text-align: center;
				color: #c6c6c6;
				font-weight: 300;
			}
		}
		
		.popper{
			max-width: 58%;
			margin-left: 12px;
			
			.popper-title{
				padding-bottom: 6px;
				font-size: 12px;
				color: #999;
			}
			
			.popper-content{
				display: flex;
				align-content: center;
				position: relative;
				min-height: 36px;
				color: #2c2c2c;
				line-height: 22px;
				padding: 7px 12px;
				word-break: break-all;
				background-color: #fff;
				border-radius: 5px;
				box-sizing: border-box;
			}
			
			.popper-arrow:after{
				position: absolute;
				display: block;
				width: 0;
				height: 0;
				border-color: transparent;
				border-style: solid;
				top: 13px;
				left: -12px;
				border-right-color: #fff;
				border-left-width: 0;
				content: " ";
				border-width: 6px;
			}
		}
	}
}

::v-deep{
	.el-dialog__body{
		padding: 0;
	}
	.el-loading-spinner i{
		color: #999;
	}
}
</style> 