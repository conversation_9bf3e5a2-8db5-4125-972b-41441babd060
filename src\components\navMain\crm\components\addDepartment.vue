<template>
  <div>
    <el-form label-width="100px">
      <el-form-item label="部门名称">
        <div class="form-item-block">
          <el-input
            v-model="form_params.name"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入部门名称"
          >
          </el-input>

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="所属部门" v-if="!department_pid">
        <div class="form-item-block">
          <el-cascader
            style="width: 240px; margin-right: 12px"
            :options="options"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'name',
              emitPath: false,
              children: 'subs',
            }"
            v-model="form_params.parentid"
            clearable
          ></el-cascader>
          <!-- <el-input
            v-model="form_params.parentid"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入姓名"
          >
          </el-input> -->

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>

      <el-form-item label="排序">
        <div class="form-item-block">
          <el-input
            v-model="form_params.order"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入排序"
          ></el-input>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <!-- 企业微信自建应用代开 -->
      <el-form-item
        label="是否同步微信"
        v-if="website_info.self_auth_create_all"
      >
        <el-radio-group v-model="form_params.syn_wx" size="mini">
          <el-radio :label="1" border>同步</el-radio>
          <el-radio :label="0" border>不同步</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform" :loading="isSubmiting"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  props: ["department_pid", "options", "website_info"],
  data() {
    return {
      form_params: {
        name: "",
        order: 0,
        parentid: "",
        syn_wx: 0,
      },
      isSubmiting: false,
    };
  },
  watch: {
    department_pid(val) {
      this.form_params.parentid = val;
    },
  },
  created() {
    this.form_params.parentid = this.department_pid;
  },

  methods: {
    subform() {
      //  是否为企业微信自建应用代开发
      if (!this.website_info.self_auth_create_all) {
        this.form_params.syn_wx = 0;
      }
      let params = Object.assign({}, this.form_params);
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      // params.website_id = localStorage.getItem('website_id')
      this.$http
        .addCrmDepartment(params)
        .then((res) => {
          console.log(res);
          if (res.status == 200) {
            this.$message.success("添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("添加失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
</style>
