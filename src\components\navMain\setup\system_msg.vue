<template>
  <el-container>
    <el-main>
      <div class="add" v-if="$hasShow('添加消息')">
        <el-button icon="el-icon-plus" @click="add">添加消息</el-button>
      </div>

      <myTable
        v-loading="is_table_loading"
        :table-list="tableData"
        :header="table_header"
      ></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "system_msg",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      table_header: [
        { prop: "msg", label: "消息内容" },
        { prop: "created_at", label: "发送时间" },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  computed: {},
  methods: {
    getDataList() {
      this.$http
        .remindMsgList(this.params.currentPage, this.params.pagesize)
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    add() {
      this.$goPath("add_system_msg");
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
  },
};
</script>

<style scoped>
.add {
  margin-bottom: 10px;
}
</style>
