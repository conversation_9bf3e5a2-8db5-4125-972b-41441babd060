<template>
    <el-dialog  title="添加资料包" :visible.sync="show" width="1600px">
        
        <div class="content">
            <!-- 示例图 -->
            <div class="examplediagram">
                <div class="title">
                    <div class="blue-rectangle"></div>
                    <div class="title-characters">
                        示例图
                    </div>
                </div>
                <div class="click-image">
                    <div class="top_picstyle">
                        <div class="time">
                            <div>
                                {{times}}
                            </div>
                            <div>
                                <i style="font-size:13px;margin-right:3px;" class="el-icon-newfonthulianwangxinhaotubiao"></i>
                                <i style="margin-right:3px;" class="el-icon-newfontwuxianwang"></i>
                                <i class="el-icon-newfont80dianliang"></i>
                            </div>
                           
                        </div>
                        <img v-if="formdata.top_pic" :src="formdata.top_pic" />
                    </div>
                    
                    <div class="information-style" v-if="informationdata.length" >
                        <div v-for="id in informationdata" :key="id.id" class="card">
                            <img :src="id.top_pic" alt="Image" class="card-image" />
                            <span class="card-title">{{ id.title }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 添加条件 -->
            <div class="addconditions">
                <div class="title">
                    <div class="blue-rectangle"></div>
                    <div class="title-characters">
                        基础设置
                    </div>
                </div>
                <div>
                    <el-form ref="form" :model="formdata" label-width="100px" label-position="left">
                        <el-form-item label="资料包标题">
                          <el-input style="width: 70%;" v-model="formdata.title" placeholder="资料包标题(10字内)"></el-input>
                        </el-form-item>
                        <el-form-item label="分享标题">
                          <el-input style="width: 70%;" v-model="formdata.share_title" placeholder="微信卡片分享标题(20字内)"></el-input>
                        </el-form-item>
                        <el-form-item label="分享描述">
                          <el-input style="width: 70%;" v-model="formdata.share_desc" placeholder="微信页面分享描述(可选填)"></el-input>
                        </el-form-item>
                        <el-form-item label="资料绑定">
                            <div v-if="informationdata.length">
                                <!-- <div v-if="informationdata.length>1" style=" width:70%;text-align:right;">
                                    <i class="el-icon-sort" @click="sortdata">排序</i>
                                </div> -->
                                <draggable v-model="informationdata" animation="300" style="width: 100%;">
                                    <transition-group>
                                        <div v-for="row in informationdata" :key="row.id"  class="informationdata-style" >
                                            <template>
                                                <div class="topdata  flex-row">
                                                    <span class="hand"><i class="el-icon-rank"></i></span>
                                                        <img :src="row.top_pic" alt="Image" class="card-image" />
                                                    <div style="margin-left:20px">
                                                        <span  class="name-disabled" style="font-weight:600;color:#1f2f3d;">{{row.title}}</span>
                                                        <div style="margin-top:-10px;">
                                                        创建时间：{{row.created_at}}
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </transition-group>
                                </draggable>
                            </div>
                            <el-button icon="el-icon-plus" @click="addzldialog">资料绑定</el-button>
                        </el-form-item>
                        <el-form-item label="活动主题大图">
                            <el-upload 
                              ref="upload_Ref"
                              :limit="1" 
                              :headers="myHeader" 
                              action="/api/common/file/upload/admin?category=6" 
                              :on-success="LeafletTopSuccess"
                              :on-remove="removeTop"
                              list-type="picture-card"
                              :clearFiles="clearFilespic"
                            >
                              <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color:red;font-size:14px;">(上传限制最大2M 推荐4:3 或 16:9比例 JPG格式图片)</div>
                        </el-form-item>
                        <el-form-item label="分享图片">
                            <el-upload 
                              ref="uploadRef"
                              :limit="1" 
                              :headers="myHeader" 
                              action="/api/common/file/upload/admin?category=6" 
                              :on-success="myshare_picSuccess"
                              :on-remove="removeshare_pic"
                              list-type="picture-card"
                              :clearFiles="clearFiles_pic"
                            >
                              <i class="el-icon-plus"></i>
                            </el-upload>
                            <div style="color:red;font-size:14px;">(上传限制最大2M 推荐4:3比例 JPG格式图片)</div>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    <span slot="footer" class="dialog-footer"> 
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span> 
    <selectprofile ref="selectprofile" v-if="dialogs.selectprofile" :tableData="tableData" @informationids="informationids" />
</el-dialog>
</template>
<script>
import config from "@/utils/config.js";
import selectprofile from './select_profile.vue';
import draggable from 'vuedraggable';
export default {
    components:{
        selectprofile,
        draggable
    },
    props:{
        // tableData: {
        //     type: Array,
        //     default: () => []
        // },
    },
    data() {
        return {
            show: false,        //dialog是否显示    
            loading: false,
            submiting: false,
            params: {
                id: '',
                type: 1,
                num: 3
            },
            formdata:{
                title:"",//资料包标题
                share_title:"",//分享标题
                share_desc:"",//分享描述
                top_pic:"",//头部背景大图
                plugin_ids:"",//资料包id
            },
            times:"",//当前时间
            tableData:[],//绑定资料
            dialogs:{
                selectprofile:false,
            },
            informationdata:[],
            sortshow:false,//排序图标
        }
    },
    mounted(){
        let currentDate = new Date();
        let hours = String(currentDate.getHours()).padStart(2, '0'); // 小时补零
        let minutes = String(currentDate.getMinutes()).padStart(2, '0'); // 分钟补零
        this.times = `${hours}:${minutes}`
        this.getMapPluginList()
    },
    computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
    },
    methods:{
        //获取资料
        getMapPluginList(){
            this.$http.getMapPluginList().then(res=>{
                if(res.status==200){
                    this.tableData = res.data.data
                }
            })
        },
        //打开弹窗
        open(){
            this.show = true;
            return this
        },
        //保存
        submit(){
            let formcopy = JSON.parse(JSON.stringify(this.formdata)); // 拷贝
            formcopy.plugin_ids = this.informationdata.map(item => item.id).join(',');
            if(!formcopy.title){
                return this.$message.warning("请填写资料包标题")
            }
            if(!formcopy.share_title){
                return this.$message.warning("请填写分享标题")
            }
            if(!formcopy.plugin_ids.length){
                return this.$message.warning("请绑定资料")
            }
            if(!formcopy.top_pic){
                return this.$message.warning("请上传活动主题大图")
            }
            if(!formcopy.share_pic){
                return this.$message.warning("请上传分享图片")
            }
            // console.log(formcopy);
            this.$http.adddatapackage(formcopy).then(res=>{ 
                if(res.status==200){
                    this.$message.success("添加成功")
                    this.formdata = {}
                    this.informationdata = []
                    this.clearFiles_pic()
                    this.clearFilespic()
                    this.$emit("getzhiliaolist")
                    this.show = false;
                }
            })
        },
        //添加绑定资料
        async addzldialog(){
            this.dialogs.selectprofile = true
            await this.$nextTick();  // 等待 DOM 更新完成
            this.$refs.selectprofile.open();
        },
        //子组件传过来的资料id
        informationids(data){
            this.formdata.plugin_ids = data
            // console.log(data,"ziuijianids");
            // 遍历 data 数组，查找每个对应的 item
            this.informationdata = data.map(id => {
                // 在 tableData 中查找对应 id 的项
                return this.tableData.find(item => item.id === id);
            });
        },
        //排序
        sortdata(){
            this.sortshow = true
            // console.log(121212121);
        },
        //取消
        cancel(){
            this.show = false;
        },
        //上传头部背景照片
        LeafletTopSuccess(response) {
          this.formdata.top_pic = response.url
        },
        //删除头部背景照片
        removeTop(){
            this.formdata.top_pic = ""
        },
        //删除分享图片
        removeshare_pic(){
            this.formdata.share_pic = ""
        },
        //清除上传照片列表
        clearFiles_pic(){
            this.$refs.uploadRef.clearFiles();
        },
        //清除上传照片列表
        clearFilespic(){
            this.$refs.upload_Ref.clearFiles()
        },
        //上传分享图片
        myshare_picSuccess(response){
            this.formdata.share_pic = response.url 
        },
        // getImageById(id) {
        //   // 返回根据 ID 获取的图片路径
        //   const item = this.tableData.find(item => item.id === id);
        // //   console.log(item);
        //   return item ? item.top_pic:"https://img.tfcs.cn/backup/static/admin/crm/index/empty.png"
        // },
        // getTitleById(id) {
        //   // 返回根据 ID 获取的标题
        //   const item = this.tableData.find(item => item.id === id);
        //   return item ? item.title : '';
        // }
    },
}
</script>
<style lang="scss" scoped>
.content{
    width: 100%;
    // height: 750px;
    // border: 1px black solid;
    display: flex;
    .examplediagram{
        width: 60%;
        height: 100%;
        // border: 1px red solid; 
        .click-image{
            width: 53%;
            // height: 800px;
            min-height: 800px;
            box-shadow: 0 0 4px rgba(128, 128, 128, 0.5); /* 模糊的灰色阴影 */
            border: 1px solid #dcdcdc; /* 边框颜色 */
            // background-color: aqua;
            // border: 1px aqua solid; 
            margin: auto;
            .top_picstyle{
                width: 100%;
                height: 370px;
                position: relative; /* 使子元素可以进行绝对定位 */
                // border: 1px red solid; 
                img{
                    width: 100%;
                    height: 100%;
                }
                .time {
                    width: 90%;
                    position: absolute; /* 绝对定位 */
                    top: 10px; /* 距离上边缘10px */
                    left: 10px; /* 距离左边缘10px */
                    font-size: 16px; /* 设置时间字体大小 */
                    color: black; /* 设置时间字体颜色 */
                    // background-color: rgba(0, 0, 0, 0.6); /* 设置背景颜色和透明度，使文字更加可见 */
                    padding: 5px; /* 给文字添加一些内边距 */
                    display: flex;
                    justify-content: space-between;
                }
            }
            .information-style {
              width: 90%;
              display: flex;
              flex-wrap: wrap;
              gap: 50px; /* 使卡片之间有间距 */
              margin: auto;
              margin-top: 20px;
              margin-bottom: 20px;
            }

            .card {
              display: flex;
              align-items: center;
              width: 150px;
              height: 50px;
              border: 1px solid #ddd;
              border-radius: 5px;
              padding: 5px;
              background-color: #fff;
              background: linear-gradient(to top, rgba(222, 222, 222, 0.5), rgba(235, 251, 251, 0.6));  /* 渐变背景 */
            }

            .card-image {
              width: 30px; /* 图片宽度 */
              height: 30px; /* 图片高度 */
              object-fit: cover; /* 保持图片比例 */
              margin-right: 10px; /* 图片和标题之间的间距 */
            }

            .card-title {
              font-size: 14px;
              color: #333;
              white-space: nowrap; /* 防止标题换行 */
              overflow: hidden;
              text-overflow: ellipsis; /* 超长标题显示省略号 */
            }
        }
    }
    .addconditions{
        width: 50%;
        height: 100%;
        // border: 1px gold solid; 
      .informationdata-style{
        width: 70%;
        height: 80px;
        border: 1px #DCDFE6 solid; 
        margin-bottom: 15px;
        border-radius: 4px;
        .topdata{
            margin: 4px 0px 0px 18px;
            display: flex;
            align-items: center;
            .hand{
                font-size: 22px;
                height: 100%;
                width: 40px;
            }
        }
        .card-image {
          width: 35px; /* 图片宽度 */
          height: 35px; /* 图片高度 */
        //   object-fit: cover; /* 保持图片比例 */
        //   margin-right: 10px; /* 图片和标题之间的间距 */
        }
      }
    }
    .title{
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            .blue-rectangle {
              width: 6px;
              height: 30px;
              background-color: #1989fa;
            }
            .title-characters{
                font-weight: 400;
                color: #1f2f3d;
                font-size: 21px;
                margin-left: 10px;
            }
        }
}

</style>