<template>
    <div class="container">
        <div class="form-main">
            <el-form style="width: 450px;" label-width="120px">
                <el-form-item label="A级：">
                    <el-input
                        v-model="DataList.level_a"
                        style="width: 300px"
                    >
                        <template slot="append">分</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="B级：">
                    <el-input
                        v-model="DataList.level_b"
                        style="width: 300px"
                    >
                        <template slot="append">分</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="C级：">
                    <el-input
                        v-model="DataList.level_c"
                        style="width: 300px"
                    >
                        <template slot="append">分</template>
                    </el-input>
                </el-form-item>
            </el-form>
            <!-- 跟进提醒 -->
            <el-form style="width: 500px;" label-width="90px">
                <el-form-item label="跟进提醒：">
                    <el-input
                        style="width: 130px"
                        v-model="DataList.level_a_day_follow"
                        placeholder="请输入"
                        type="number"
                        min="0"
                        max="255"
                    >
                        <template slot="append">天</template>
                    </el-input>
                    <el-tooltip
                        class="item"
                        effect="light"
                        placement="right"
                        style="margin-left: 10px"
                    >
                    <div slot="content" style="max-width: 300px">
                        系统将自动发送模板消息，提醒维护人跟进，如果不开启消息提醒可设置为0天。
                    </div>
                    <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </el-form-item>
                <el-form-item>
                    <el-input
                        style="width: 130px"
                        v-model="DataList.level_b_day_follow"
                        placeholder="请输入"
                        type="number"
                        min="0"
                        max="255"
                    >
                        <template slot="append">天</template>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        style="width: 130px"
                        v-model="DataList.level_c_day_follow"
                        placeholder="请输入"
                        type="number"
                        min="0"
                        max="255"
                    >
                        <template slot="append">天</template>
                    </el-input>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <span class="score_prompt">调整后评分数据将于次日生效</span>
            <el-button style="margin-left: 10px;" type="primary" @click="onSubmit"
            >确定</el-button>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            DataList: [],
            level_list: [
                {
                    id: "A",
                    title: "A",
                },
                {
                    id: "B",
                    title: "B",
                },
                {
                    id: "C",
                    title: "C",
                },
            ],
        }
    },
    created() {
        this.getHouseScore();
    },
    methods: {
        // 获取评分
        getHouseScore() {
            this.$http.getHouseScore().then((res) => {
                if(res.status == 200) {
                    this.DataList = res.data;
                }
            })
        },
        // 确定修改
        onSubmit() {
            this.$http.setHouseScore(this.DataList).then((res) => {
                if(res.status == 200) {
                    this.$message({
                        message: res.data.msg,
                        type: 'success'
                    });
                    this.getHouseScore();
                }
            })
        }
    }
}
</script>
<style scoped lang="scss">
::v-deep.container {
    margin-left: 78px;
    padding-top: 24px;
    min-height: 110vh;
    .form-main {
       display: flex;
       flex-direction: row; 
    }
}
.score_prompt {
    margin-left: 235px;
    color: #606266;
    font-size: 14px;
}
</style>