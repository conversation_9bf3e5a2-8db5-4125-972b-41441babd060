<template>
  <div class="content_page">
    <div class="content_container" ref="content">
      <div class="step_box">
        <div class="step_item" :class="{ success: step >= 1 }">
          <div class="line"></div>
          <div class="number">1</div>
          <div class="title">基础信息</div>
        </div>
        <div class="step_item" :class="{ success: step >= 2 }">
          <div class="line"></div>
          <div class="number">2</div>
          <div class="title">完善信息</div>
        </div>
        <div class="step_item" :class="{ success: step >= 3 }">
          <div class="line"></div>
          <div class="number">3</div>
          <div class="title">业主信息</div>
        </div>
        <div class="step_item" :class="{ success: step >= 4 }">
          <div class="line"></div>
          <div class="number">4</div>
          <div class="title">编辑成功</div>
        </div>
      </div>
      <!-- <component :is="currentStepComponent"></component> -->
      <!-- <Form4
        v-show="step == 1"
        :form_options="form_options"
        :house_type="params.trade_type"
        :form_data="params"
        @change="onForm1Change"
        :isOpenShowingSingle="isOpenShowingSingle"
        ref="form4"
      /> -->
      <Form1 v-show="step == 1" :form_options="form_options" :house_type="params.trade_type" :form_data="params"
        @change="onForm1Change" :isOpenShowingSingle="isOpenShowingSingle" ref="form1" />
      <Form3 v-show="step == 2" :showLabel="false" :form_options="form_options" :form_data="params"
        :house_type="params.trade_type" :usage_type="params.usage_type_id" ref="form3" />
      <Form2 v-if="step == 3" :form_options="form_options" :form_data="params" :house_type="params.trade_type"
        :isOpenShowingSingle="isOpenShowingSingle" ref="form2" />
      <div v-show="step == 4">
        <div class="success_info">
          <img src="@/assets/success_res.png" alt="" />
          <p>房源编辑成功，请及时跟进维护</p>
        </div>
        <!-- <div class="success_tip">
          <p><i class="el-icon-info"></i> 您可以复制房源地址分享给好友或客户</p>
        </div>
        <div class="flex-box copy-link">
          <el-input :value="mobile_link" disabled> </el-input>
          <el-button
            icon="el-icon-document-copy"
            type="primary"
            @click="copyLink"
            >点击复制</el-button
          >
        </div> -->
      </div>
      <!-- 底部操作菜单 -->
      <div class="options_btn">
        <el-form label-width="80px">
          <el-form-item>
            <el-button v-show="step === 2 || step === 3" size="base" style="margin-left: 24px" @click="prevStep()"><i
                class="el-icon-arrow-left"></i>上一步</el-button>
            <el-button v-show="step < 3" size="base" type="primary" @click="nextStep()">下一步 <i
                class="el-icon-arrow-right"></i>
            </el-button>
            <el-button v-show="step === 3" type="primary" size="base" :loading="subing" style="margin-left: 24px"
              @click="onSubmit">提交</el-button>
            <el-button size="base" style="margin-left: 24px" @click="onCancel">
              {{ step === 4 ? "返回" : "取消" }}
            </el-button>
            <el-button v-show="step === 2" size="base" style="margin-left: 24px" @click="onSkip">跳过</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
// import { Drawer } from 'element-ui'
// import breadcrumb from '@/components/Breadcrumb'
// import Form1 from "./components/editForm1";      //二手房
import Form1 from "./components/editformcopy";  //商业地产
import Form2 from "./components/editForm2";
import Form3 from './components/Form_perfect'
// import { mapState } from 'vuex'
// import copyText from '@/utils/copy_text'
// import HouseDetail from '@/components/house/Detail'
export default {
  name: "house_edit",
  components: { Form1, Form2, Form3 },
  data() {
    return {
      breadcrumb: [],
      step: 1,
      subing: false,
      params: {
        tel: [],
      },
      form_options: {},
      mobile_link: "",
      show_detail: false,
      current_info_id: null,
      isOpenShowingSingle: false,
      stepFormList: [
        {
          step: 1,
          form_options: "form_options",
          house_type: "params.trade_type",
          form_data: "params",
          change: "onForm1Change",
          isOpenShowingSingle: "isOpenShowingSingle",
          ref: "form4",
        }
      ]
    };
  },
  computed: {
    // currentStepComponent(){
    //   if (this.step == 1) {
    //     return {
    //       name :"Form1",
    //       form_options:this.form_options,
    //       house_type:this.params.trade_type,
    //       form_data:this.params,
    //       change:this.onForm1Change,
    //       isOpenShowingSingle:this.isOpenShowingSingle,

    //     }
    //   }
    //  },
  },
  created() {
    this.id = this.$route.query.id;
    this.getDetail();
    // this.params.trade_type = parseInt(this.$route.query.trade_type) || 1
    this.getOptions();
    this.getOPenShowSingle();
    this.step = this.$route.query.step || 1;
  },
  beforeDestroy() {
    // window.eventBus.$off("closeTab")
    // // eslint-disable-next-line no-undef
    // eventBus.$off("getDataAgain")
    // // eslint-disable-next-line no-undef
    // eventBus.$off("getDetailAgain")
  },
  methods: {
    getOptions() {
      this.$ajax.house.options(this.trade_type).then((res) => {
        if (res.status === 200) {
          this.form_options = res.data;
        }
      });
    },
    getOPenShowSingle() {
      this.$ajax.house.getOPenShowSingle().then((res) => {
        if (res.status === 200) {
          this.isOpenShowingSingle = res.data.status
        }
      })
    },
    getDetail() {
      this.$ajax.house.getEditDetail(this.id).then((res) => {
        if (res.status == 200) {
          this.params = res.data
          this.params.region_id_arr = res.data.region_id_arr.split(',')
          if (this.params.sale_price) {
            this.params.sale_price = +this.params.sale_price / 10000;
          }
        }
      });
    },
    nextStep() {
      this.$refs.content.scrollTop = 0;
      this.step++;
    },
    prevStep() {
      if (this.step > 1) {
        this.step--;
        this.$refs.content.scrollTop = 0;
      }
    },
    onForm1Change(e) {
      this.params.trade_type = e.trade_type;
      this.params.usage_type_id = e.usage_type_id
    },
    onSubmit() {
      let params = Object.assign(
        this.params,
        this.$refs.form1.form,
        this.$refs.form2.form,
        this.$refs.form3.form
      );
      // 如果没有设置封面  默认第一张图为封面
      // let hasSetCover = this.$refs.form1.has_set_cover;
      // if (!hasSetCover && params.pic.length > 0) {
      //   params.pic[0].is_cover = 1;
      // }
      const form = this.$refs.form2.form
      const new_tel = JSON.stringify(form.tel)
      if (params.tel.length) {
        let ownerError = ''
        for (let index = 0; index < params.tel.length; index++) {
          const element = params.tel[index];
          if (element.owner.length > 5 && (!element.id)) {
            ownerError = true
            this.$message.warning("业主姓名不能大于5个字符")
            break;
          }
          if (element.owner_tel && (element.owner_tel.length !== 11 || element.owner_tel[0] != 1) && (!element.id)) {
            ownerError = true
            this.$message.warning("业主手机号格式错误")
            break;
          }

        }
        if (ownerError) return
      }
      if (params.sale_price) {
        params.sale_price = params.sale_price * 10000;
      }
      params.tel = params.tel.filter((item) => {
        return !item.change;
      });
      // params.picture = JSON.stringify(params.picture)
      for (let key in params) {
        if (Object.prototype.toString.call(params[key]) === "[object Array]") {
          if (params[key].length > 0 && typeof params[key][0] === "object") {
            params[key] = JSON.stringify(params[key]);
          } else {
            params[key] = params[key].join(",");
          }
        }
        if (params[key] === "" || params[key] === null) {
          if (key == 'building_loudong_id' || key == 'building_danyuan_id' || ['title_promotion', 'description'].includes(key)) {
            console.log();
          } else {
            delete params[key]
          }
        }
      }
      // 选择房号后只需要传building_unit_id，清除选择的楼栋和单元数据
      // 选择房号后只需要传building_unit_id，清除选择的楼栋和单元数据
      if (params.building_unit_id.split("_").length == 1 || params.usage_type_id == 4 || params.usage_type_id == 5 || params.usage_type_id == 6) {
        delete params.loudong;
        delete params.loudongdanwei;
        delete params.danyuan;
        delete params.danyuandanwei;
      } else {
        console.log(params.building_loudong_id, this.$refs.form1.current_loudong_name, params.usage_type_id, "编辑提交走到的步骤");
        if (params.building_loudong_id.split("_").length > 1) {
          params.loudong = parseInt(params.building_loudong_id.split("_")[1])
          if (isNaN(params.loudong)) {
            this.$message("楼栋请以数字开头")
            return
          }
          params.loudong_no = (params.loudong + '').replace(/[\u4e00-\u9fa5]/g, '')
          delete params.building_loudong_id;
        } else {
          params.loudong = this.$refs.form1.current_loudong_name
          params.loudong_no = (params.loudong + '').replace(/[\u4e00-\u9fa5]/g, '')
        }
        if (params.building_danyuan_id.split("_").length > 1) {
          params.danyuan = parseInt(params.building_danyuan_id.split("_")[1])
          if (isNaN(params.danyuan)) {
            this.$message("单元请以数字开头")
            return
          }
          params.danyuan_no = (params.danyuan + '').replace(/[\u4e00-\u9fa5]/g, '')
          delete params.building_danyuan_id;
        } else {
          params.danyuan = this.$refs.form1.current_danyuan_name
          params.danyuan_no = (params.danyuan + '').replace(/[\u4e00-\u9fa5]/g, '')
        }
        if (params.building_unit_id.split("_").length > 1) {
          params.fanghao = params.building_unit_id.split("_")[1];
          delete params.building_unit_id;
        }
      }
      delete params.memo_edit
      this.subing = true;
      params.new_tel = new_tel
      this.$ajax.house
        .edit(params)
        .then((res) => {
          this.subing = false;
          if (res.status === 200) {
            this.$message.success(res.data.message || "编辑成功");
            this.step = 4;

            // eslint-disable-next-line no-undef
            eventBus.$emit("getDataAgain");
            // eslint-disable-next-line no-undef
            eventBus.$emit("getDetailAgain");

            // this.mobile_link = `${process.env.VUE_APP_DOMAIN}/m${this.user_info.pinyin ? '/' + this.user_info.pinyin : ''
            //   }/house/detail?id=${res.data.data.id || ''}`
          }
        })
        .catch(() => {
          this.subing = false;
        });
    },
    copyLink() {
      if (!this.mobile_link) {
        this.$message.warning("复制失败");
        return;
      }
      // copyText(this.mobile_link, () => {
      //   this.$message.success('复制成功')
      // })
    },
    onCancel() {
      let name = window.location.href.split("#")[1];
      this.$store.state.closeTab = true;
      // eslint-disable-next-line no-undef
      eventBus.$emit("closeTab", name);
      this.$router.go(-1);
    },
    // 跳过
    onSkip() {
      this.step = 3
    }
  },
  // // 路由独享守卫
  // beforeRouteLeave(to, from, next) {
  //   // 设置下一个路由的 meta
  //   if (to.path == '/house_detail' && this.step == 3) {
  //     this.$store.state.allowUpdate = true
  //     // to.meta.keepAlive = false; // C 跳转到 A 时让 A 不缓存，即刷新
  //   }

  //   next();
  // }
};
</script>

<style scoped lang="scss">
.content_page {
  height: 100%;
  overflow-x: hidden;
  background-color: #f1f4fa;
  display: flex;
  flex-direction: column;

  .breadcrumb_box {
    position: sticky;
    top: 0;
    z-index: 10;
  }
}

.content_container {
  flex: 1;
  margin: 24px;
  padding: 24px 32px;
  border: 2px;
  background-color: #fff;
  overflow-x: hidden;
}

// 步骤条
.step_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 950px;
  margin: auto;
  padding: 40px 0;

  >.step_item {
    flex-shrink: 1;
    flex-basis: 50%;
    display: flex;
    align-items: center;

    .number {
      width: 40px;
      text-align: center;
      height: 40px;
      line-height: 40px;
      margin-right: 12px;
      margin-left: 20px;
      border-radius: 50%;
      font-size: 18px;
      background-color: #dde1e9;
      color: #fff;
    }

    .title {
      margin-right: 20px;
      font-size: 14px;
      color: #dde1e9;
    }

    .line {
      flex: 1;
      height: 2px;
      background-color: #dde1e9;
    }

    &:first-of-type {
      flex-shrink: 0;
      flex-basis: auto;

      .line {
        display: none;
      }
    }

    &.success {
      .number {
        background: #2d84fb;
        box-shadow: 0 2px 6px 0 rgba(#2d84fb, 0.4);
      }

      .title {
        color: #2d84fb;
      }

      .line {
        background-color: #2d84fb;
      }
    }
  }
}

.options_btn {
  margin-top: 24px;
  padding: 12px 0;
  border-top: 1px solid #dde1e9;
}

.el-form {
  .title {
    margin-top: 42px;
    margin-bottom: 32px;
    font-size: 18px;
    font-weight: bold;

    .remarks {
      font-size: 14px;
      font-weight: initial;
    }
  }
}

.success_info {
  width: 360px;
  margin: auto;
  margin-top: 12px;
  margin-bottom: 32px;
  text-align: center;

  img {
    margin-bottom: 12px;
    width: 120px;
  }

  p {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #2d84fb;
  }
}

.copy-link {
  margin: auto;
  width: 600px;
}

.success_tip {
  width: 600px;
  margin: auto;
  margin-bottom: 24px;
  padding: 12px 20px;
  border-radius: 4px;
  font-size: 13px;
  color: #2d84fb;
  background-color: rgba(#2d84fb, 0.2);

  i {
    margin-right: 3px;
    font-size: 18px;
  }
}
</style>
