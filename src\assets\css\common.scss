.tab-content-container{
    margin: -15px;
    height: calc(100vh - 99px);
    display: flex;
    flex-direction: column;
    .tab-content-body{
        flex: 1;
        overflow: auto;
        padding: 15px;
        background: #f1f4fa;
        &.white{
            background: #fff;
        }
        &.topped{
            padding-top: 0;
        }
        .tab-content-body-inner{
            background: #fff;
            padding: 15px;
        }
    }
    .tab-content-footer{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 15px;
        z-index: 666;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    }
}

.t-select-group{
    .el-select{
        &:not(:first-child){
            .el-input__inner{
                border-top-left-radius: 0!important;
                border-bottom-left-radius: 0!important;
                margin-left: -1px;
            }
        }
        &:not(:last-child){
            .el-input__inner{
                border-top-right-radius: 0!important;
                border-bottom-right-radius: 0!important;
            }
        }
        .el-input{
            &:hover{
                z-index: 1;
            }
            &.is-focus{
                z-index: 2;
                .el-input__inner:focus{
                    border-color: #409EFF;
                }
            }
            .el-input__inner:focus{
                border-color: #DCDFE6;
            }
        }
    }
}

.el-drawer:focus,.el-drawer__header > span:focus{
    border: none;
    outline: none;
}