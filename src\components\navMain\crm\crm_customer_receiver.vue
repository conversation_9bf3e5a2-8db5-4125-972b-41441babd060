<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col>
        <div class="content-box-crm">
          <div class="table-top-box div row align-center">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>

            <div class="content-box-crm padd0">
              <el-button type="primary" @click="toAdd" size="mini" class="el-icon-plus">添加</el-button>
            </div>
          </div>
          <el-table v-loading="is_table_loading" :data="tableData" border class="table"
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="ID" min-width="30px" align="center" prop="id">
            </el-table-column>
            <el-table-column label="姓名" min-width="60px" align="center" v-slot="{ row }">
              <span class="name">{{ row.user_name }}</span>
            </el-table-column>
            <el-table-column label="手机号" min-width="60px" align="center" v-slot="{ row }">
              <span class="name">{{ row.phone }}</span>
            </el-table-column>
            <el-table-column label="所属部门" min-width="60px" align="center" v-slot="{ row }">
              <span class="name" v-for="(item, index) in row.department" :key="index + '_'">{{ item.name }}</span>
            </el-table-column>
            <el-table-column label="头像" min-width="60px" align="center" v-slot="{ row }">
              <div class="row-img">
                <img v-if="row.wx_work_avatar" :src="row.wx_work_avatar" alt="" />
                <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
              </div>
            </el-table-column>
            <el-table-column label="创建时间" min-width="60px" align="center" v-slot="{ row }">
              <span class="name">{{ row.created_at }}</span>
            </el-table-column>

            <el-table-column label="操作" min-width="60px" align="center" v-slot="{ row }">
              <el-popconfirm title="确定删除吗？" @onConfirm="deleteReceiver(row)">
                <el-link slot="reference" type="danger" icon="el-icon-delete">删除</el-link>
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
            :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="show_add_dia" width="660px" title="添加接待人员">
      <add-receiver v-if="show_add_dia" @success="addOk" :id="id" @cancel="cancelAdd" :selected="[]"
        :selectedLists="[]"></add-receiver>
    </el-dialog>
  </div>
</template>

<script>
import AddReceiver from "./components/receiver/addReceiver.vue";
// import mySlide from './components/new_slide';
export default {
  name: "crm_customer_receiver",
  components: {
    AddReceiver,
  },
  data() {
    return {
      navList: [],
      params: {
        page: 1,
        per_page: 5,
      },
      total: 0,
      is_table_loading: false,
      multipleSelection: [],
      tableData: [],
      id: "",
      show_add_dia: false,
      selected: [],
    };
  },
  created() {
    this.id = this.$route.query?.id;
    //  获取部门
    this.getData();
  },

  methods: {
    getData() {
      this.is_table_loading = true;
      this.$http
        .getCrmReceiverList(this.id, { params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status == 200) {
            this.tableData = res.data.data;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    search() {
      this.params.page = 1;
      this.getData();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getData();
    },
    toAdd() {
      this.show_add_dia = true;
    },
    addOk() {
      this.getData();
      this.show_add_dia = false;
    },
    cancelAdd() {
      this.show_add_dia = false;
    },

    deleteReceiver(row) {
      let params = {
        kf_id: this.id,
        id: row.id + "",
      };
      this.$http.delCrmReceiver(params).then((res) => {
        if (res.status == 200) {
          this.$message.success("删除成功");
          this.getData();
        } else {
          this.$message.success(res.message || "删除失败");
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .top {
    align-items: center;
  }

  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }

  .bmtitle {
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 24px;
    border-bottom: 1px solid #eee;
  }

  .title-bm {
    font-size: 18px;
  }
}

.content-crm {
  min-height: 600px;
}

.table {
  .name {
    ~.name {
      margin-left: 8px;
    }
  }

  .row-img {
    width: 60px;
    height: 60px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.padd0 {
  padding: 10px;
}
</style>
