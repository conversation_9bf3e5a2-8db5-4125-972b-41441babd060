<template>
  <div class="list">
    <el-container>
      <el-header class="div row">
        <div class="row div">
          <div class="title">客户列表</div>
          <div class="title_number">
            <div>
              当前页面共（
              <i>{{ tableData.length }}</i>
              ）条数据
            </div>
          </div>
        </div>
        <div class="div row">
          <el-input v-model="params.name" placeholder="搜索相关客户"></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>
      <el-main v-loading="is_table_loading">
        <myTable :table-list="tableData" :header="table_header"></myTable>
      </el-main>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-container>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "apply_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
        build_id: "",
        website_id: localStorage.getItem("website_id"),
      },
      table_header: [
        { prop: "id", label: "报名ID", width: "100" },
        { prop: "build_name", label: "报名楼盘" },
        { prop: "name", label: "报名客户" },
        { prop: "phone", label: "客户电话" },
        { prop: "created_at", label: "报名时间" },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
    this.params.build_id = this.$route.query.build_id;
  },
  methods: {
    getDataList() {
      this.$http.companyGetEnrollList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    search() {
      this.params.page = 1;
      this.getDataList();
    },
  },
};
</script>

<style scoped lang="scss">
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 0;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
</style>
