<template>
  <div class="content">
    <template v-if="false">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in tabs_list" :key="index" :label="item.label" :name="item.name">
        </el-tab-pane>
      </el-tabs>
      <!-- 通话套餐 -->
      <div class="phoneMenu" v-if="activeName == 'phone'">
        <template>
          <el-button type="success" size="medium" @click="CallRecharge">通话充值</el-button>
          <el-button type="primary" size="medium" @click="RechargeRecord">充值记录</el-button>
        </template>
      </div>
      <!-- 通话充值对话框 -->
      <el-dialog title="套餐列表" :visible.sync="dialogVisible" width="800px">
        <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
          :row-style="$TableRowStyle" :data="tableData" style="width: 100%">
          <el-table-column prop="id" label="套餐id" width="80">
          </el-table-column>
          <el-table-column prop="name" label="套餐名称" width="140">
          </el-table-column>
          <el-table-column prop="price" label="套餐价格"> </el-table-column>
          <el-table-column prop="time_total" label="通话时长/分钟" width="140">
            <template slot-scope="scope">
              {{ scope.row.time_total / 60 }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="套餐描述" width="140">
          </el-table-column>
          <el-table-column prop="status" label="套餐状态">
            <template slot-scope="scope">
              <el-tag type="success">{{
                scope.row == 0 ? "已下架" : "正常"
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" v-slot="{ row }">
            <el-link type="success" @click="buyMenu(row)">购买套餐</el-link>
          </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="callMenu.total" :page-size="callMenu.per_page" :current-page="callMenu.page"
          @current-change="onPackagePageChange">
        </el-pagination>
      </el-dialog>
      <!-- 充值记录 -->
      <el-dialog title="充值记录" :visible.sync="rechargeVisible" width="70%">
        <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
          :row-style="$TableRowStyle" :data="recharge_list" highlight-current-row style="width: 100%">
          <!-- 列表下拉 -->
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="创建时间：">
                  <span>{{ props.row.created_at }}</span>
                </el-form-item>
                <el-form-item label="支付时间：" v-if="props.row.status == 1">
                  <span>{{ props.row.payment_at }}</span>
                </el-form-item>
                <el-form-item label="支付方式：" v-if="props.row.status == 1">
                  <span>{{
                    props.row.payment_category_id == 0
                    ? "暂未支付"
                    : props.row.payment_category_id == 1
                      ? "微信小程序支付"
                      : props.row.payment_category_id == 2
                        ? "微信扫码支付"
                        : props.row.payment_category_id == 2
                          ? "微信APP支付"
                          : "微信H5支付"
                  }}</span>
                </el-form-item>

                <el-form-item label="订单支付状态：">
                  <span>{{
                    props.row.payment_status === 0 ? "未付款" : "已付款"
                  }}</span>
                </el-form-item>
                <el-form-item label="成交单号" v-if="props.row.status == 1">
                  <span>{{ props.row.payment_trade_sn }}</span>
                </el-form-item>
                <el-form-item label="备注信息：">
                  <span>{{ props.row.remark }}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="订单ID" width="100">
          </el-table-column>
          <el-table-column prop="order_sn" label="订单号" align="center">
          </el-table-column>
          <el-table-column prop="payment_amount" label="订单金额" align="center">
          </el-table-column>
          <!-- <el-table-column
            prop="time_total"
            label="通话时长/分钟"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.time_total / 60 }}
            </template>
          </el-table-column> -->
          <el-table-column prop="status" label="支付状态" align="center" v-slot="{ row }">
            <el-tag type="success" v-if="row.payment_status === 1">已付款</el-tag>
            <el-tag type="warning" v-if="row.payment_status === 0 && row.status !== 2">未付款</el-tag>
            <el-tag class="ml5" type="danger" v-if="row.status === 2">已取消</el-tag>
          </el-table-column>
          <el-table-column label="操作" align="center" v-slot="{ row }">
            <el-popconfirm title="确定取消订单吗？" v-if="row.payment_status === 0 && row.status == 0"
              @onConfirm="cancelOrder(row)">
              <el-button slot="reference" type="primary" size="small">取消订单</el-button>
            </el-popconfirm>
            <el-button v-if="row.payment_status === 0 && row.status !== 2" type="success" size="small"
              @click="erweiCode(row.id)" style="margin: 0 10px">微信支付</el-button>
          </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="RechargeCode.total" :page-size="RechargeCode.per_page" :current-page="RechargeCode.page"
          @current-change="RechargePageChange">
        </el-pagination>
      </el-dialog>
      <!-- 二维码 -->
      <div>
        <el-dialog width="400px" :visible.sync="dialogPayCode" title="支付">
          <div class="code-box">
            <img :src="codeImg" alt="" />
            <p>请打开微信扫码支付</p>
          </div>
        </el-dialog>
      </div>
      <!-- 坐席套餐 -->
      <div v-if="activeName == 'seat'">
        <seats-menu></seats-menu>
      </div>
    </template>
    <el-tabs v-model="activeName1" @tab-click="handleClickLog">
      <el-tab-pane v-for="(item, index) in tabs_list_new" :key="index" :label="item.label" :name="item.name">
      </el-tab-pane>
    </el-tabs>
    <div class="div row search_box" v-if="log_params.type == 2">
      <el-input v-model="log_params.phone" placeholder="请输入手机号" style="width: 220px; margin-right: 10px">
      </el-input>
      <el-button type="primary" @click="searchLog">搜索</el-button>
    </div>
    <div v-if="log_params.type == 4" class="conditionstyle">
      <div>
             <!-- <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy,MM,dd"
                @change="onChangeTime">
      </el-date-picker> -->
      <el-select v-model="datevalue" placeholder="日期"
          size="small" style="width: 100px;"
          clearable>
            <el-option
              v-for="item in quantitydate"
              :key="item.id"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
        <el-date-picker
        v-if="datevalue==7"
        size="small"
        style="width: 150px" 
          v-model="timeValue"
          type="date"
          placeholder="选择日期"
          value-format="yyyy,MM,dd"
          @change="onChangeTime">
        </el-date-picker>
      <tMemberDropdown trigger="click" filterable @command="MemberClick">
          <el-select clearable 
          class="backgauge" size="small" style="width: 120px;" v-model="membername" 
          placeholder="请选择成员" ref="get_black_list" @focus="showPersonnelAuthority"
          @clear="clearshowPersonne">
          </el-select>
      </tMemberDropdown>
      <el-select v-model="sortvalue" placeholder="排序"
          size="small" style="width: 160px;" clearable>
            <el-option
              v-for="item in quantitysort"
              :key="item.id"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
      <el-button class="backgauge" size="small" type="primary" @click="QueryRecords">查询</el-button>
      </div>
      <div><el-button class="backgauge" size="small" type="primary" @click="exportmember">导出</el-button></div>
    </div>
    <div v-if="log_params.type == 6" class="conditionstyle">
      <div>
        <el-select v-model="AIvalue" placeholder="日期"
          size="small" style="width: 100px;"
          clearable>
            <el-option
              v-for="item in quantitydate"
              :key="item.id"
              :label="item.name"
              :value="item.value">
            </el-option>
        </el-select>
        <el-date-picker
        v-if="AIvalue==7"
        size="small"
        style="width: 150px" 
          v-model="AItimeValue"
          type="date"
          placeholder="选择日期"
          value-format="yyyy,MM,dd">
        </el-date-picker>
      <tMemberDropdown trigger="click" filterable @command="AIMemberClick">
          <el-select clearable 
          class="backgauge" size="small" style="width: 120px;" v-model="AImembername" 
          placeholder="请选择成员" ref="get_black_list" @focus="showPersonnelAuthority"
          @clear="AIclearshowPersonne">
          </el-select>
      </tMemberDropdown>
      <el-select v-model="AImemberparams.order" placeholder="排序"
          size="small" style="width: 160px;" clearable>
            <el-option
              v-for="item in AImembersort"
              :key="item.id"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button class="backgauge" size="small" type="primary" @click="AIQueryRecords">查询</el-button>
      </div>
      <div><el-button class="backgauge" size="small" type="primary" @click="exportAImember">导出</el-button></div>
    </div>
    <div v-if="log_params.type == 1 || log_params.type == 2 || log_params.type == 3" >
    <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="log_list" highlight-current-row style="width: 100%">
      <!-- 列表下拉 -->

      <el-table-column v-if="log_params.type == 2" prop="time_total" label="通话时长/秒" align="center">
        <template slot-scope="scope">
          {{ scope.row.total_duration }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.money }}
        </template>
      </el-table-column>
      <template v-if="log_params.type == 2">
        <el-table-column label="手机号" align="center">
          <template slot-scope="scope">
            {{ scope.row.phone }}
          </template>
        </el-table-column>
      </template>

      <el-table-column prop="time_total" label="备注" align="center">
        <template slot-scope="scope">
          {{ scope.row.remark }}
        </template>
      </el-table-column>
      <el-table-column prop="ctime" :label="log_params.type == 1
          ? '充值时间'
          : log_params.type == 2
            ? '通话时间'
            : '时间'
        " align="center">
        <!-- <template slot-scope="scope">
          {{ scope.row.remark }}
        </template> -->
      </el-table-column>
      <template v-if="log_params.type == 2">
        <el-table-column label="操作" align="center" v-slot="{ row }">
          <el-button type="success" size="small" @click="seeLogDetail(row)" style="margin: 0 10px">详情</el-button>
        </el-table-column>
      </template>
    </el-table>
    <!-- 分页器 -->
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="log_params.total" :page-size="log_params.per_page" :current-page="log_params.page"
      @current-change="logPageChange">
    </el-pagination>
    </div>
    <div v-else-if="log_params.type == 5">
      <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="AIdata" highlight-current-row style="width: 100% "
      :key="'ai-' + AIparams.page">
      <el-table-column prop="input_token" label="输入token" align="center">
        <template slot-scope="scope">
          {{ scope.row.input_token }}
        </template>
      </el-table-column>
      <el-table-column prop="output_tokens" label="输出token" align="center">
        <template slot-scope="scope">
          {{ scope.row.output_tokens}}
        </template>
      </el-table-column>
      <el-table-column prop="use_token" label="总token量" align="center">
        <template slot-scope="scope">
          {{ scope.row.use_token }}
        </template>
      </el-table-column>
      <el-table-column prop="money" label="费用" align="center">
        <template slot-scope="scope">
          {{ scope.row.money }}
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="时间" align="center">
        <template slot-scope="scope"> 
          {{ scope.row.created_at }} 
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="AIparams.total" :page-size="AIparams.per_page" :current-page="AIparams.page"
      @current-change="logPageChangeB">
    </el-pagination>
    </div>
    <div v-else-if="log_params.type == 6">
      <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="AImemberdata" highlight-current-row style="width: 100% "
      :key="'aimember-' + AImemberparams.page">
      <el-table-column prop="user_name" label="成员名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.user_name }}
        </template>
      </el-table-column>
      <el-table-column prop="output_tokens" label="总输出token量" align="center">
        <template slot-scope="scope">
          {{ scope.row.output_tokens }}
        </template>
      </el-table-column>
      <el-table-column prop="input_token" label="总输入token量" align="center">
        <template slot-scope="scope">
          {{ scope.row.input_token}}
        </template>
      </el-table-column>
      <el-table-column prop="use_token" label="总token量" align="center">
        <template slot-scope="scope">
          {{ scope.row.use_token}}
        </template>
      </el-table-column>
      <el-table-column prop="count" label="拨打数量" align="center">
        <template slot-scope="scope">
          {{ scope.row.count }}
        </template>
      </el-table-column>
      <el-table-column prop="money" label="消费金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.money}}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="AImemberparams.total" :page-size="AImemberparams.per_page" :current-page="AImemberparams.page"
      @current-change="logPageChangeC">
    </el-pagination>
    </div>
    <div v-else-if="log_params.type == 4">
      <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="Memberrank" highlight-current-row style="width: 100%"
      :key="'member-' + rankparams.page">
      <!-- 列表下拉 -->
      <el-table-column prop="user_name" label="成员名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.user_name }}
        </template>
      </el-table-column>
      <el-table-column prop="cost" label="金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.cost }}
        </template>
      </el-table-column>
      <el-table-column prop="total_duration" label="通话时长" align="center">
        <template slot-scope="scope">
          {{ scope.row.total_duration | filterSen2Hour }}
        </template>
      </el-table-column>
      <el-table-column prop="count" label="拨打数量" align="center">
        <template slot-scope="scope">
          {{ scope.row.count }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="rankparams.total" :page-size="rankparams.per_page" :current-page="rankparams.page"
      @current-change="logPageChangeA">
    </el-pagination>
    </div>
    <el-dialog title="通话详情" :visible.sync="show_detail" width="400px" @close="show_detail = false">
      <!-- <span class="PhoneTitle">拨打信息</span> -->
      <div class="flex-box detail_box">
        <el-form label-width="150px" label_position="left">
          <el-form-item label="主叫号码："> {{ detail.caller }} </el-form-item>
          <el-form-item label="被叫号码："> {{ detail.callee }} </el-form-item>
          <el-form-item label="接通状态：">
            {{ detail.call_status == 1 ? "已接通" : "未接通" }}
          </el-form-item>

          <el-form-item label="通话录音：" v-if="detail.record_url">
            <div class="audio_img" @click="play">
              <img style="width: 20px; object-fit: cover" v-if="isPlaying"
                :src="$imageDomain + '/static/admin/outbound/play_voice.gif'" alt="" />
              <img style="width: 20px; object-fit: cover" v-else
                :src="$imageDomain + '/static/admin/outbound/voice_icon.png'" alt="" />
              <span class="c_white"> {{ detail.duration }}"</span>
            </div>
            <!-- {{ detail.record_url }} -->
          </el-form-item>

          <el-form-item label="被叫通话时长：">
            {{ detail.callee_duration }}"
          </el-form-item>

          <el-form-item label="主叫通话时长：">
            {{ detail.caller_duration }}"
          </el-form-item>
          <el-form-item label="总通话时长：">
            {{ detail.total_duration }}"
          </el-form-item>
          <el-form-item label="计费："> {{ detail.cost }}元 </el-form-item>
          <el-form-item label="呼出时间：">
            {{ detail.call_time }}
          </el-form-item>
          <el-form-item label="结束时间：">
            {{ detail.end_time }}
          </el-form-item>
          <el-form-item label="被叫接通时间：">
            {{ detail.callee_answered_time || "未接听" }}
          </el-form-item>
          <el-form-item label="主叫接通时间：">
            {{ detail.caller_answered_time || "未接听" }}
          </el-form-item>
        </el-form>

        <audio ref="musicMp3" id="musicMp3" :autoplay="false" controls="false" style="z-index: -1"></audio>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import seatsMenu from "@/components/navMain/crm/components/setMenu/seatsMenu.vue"
import tMemberDropdown from "@/components/tplus/tDropdown/tMemberDropdown.vue";
export default {
  components: {
    seatsMenu,
    tMemberDropdown
  },
  filters: {
    filterSen2Hour(val) {
      let wholeTime = 0
      let sencond = parseInt(val) || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((sencond % 3600) % 60) % 60;
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
      return wholeTime
    }
  },
  data() {
    return {
      dialogPayCode: false, // 控制支付二维码显示
      codeImg: "", // 二维码图片
      tableData: [], // 表格绑定值
      activeName: 'phone', // tabs选中值
      activeName1: "1",
      dialogVisible: false, // 控制通话充值对话框显示
      rechargeVisible: false, // 控制充值记录对话框显示
      tel_table_loading: false,
      // tabs绑定值
      tabs_list: [
        {
          id: 1,
          name: 'phone',
          label: '通话套餐',
        },
        {
          id: 2,
          name: 'seat',
          label: '坐席套餐'
        }
      ],
      tabs_list_new: [
        {
          id: 1,
          name: '1',
          label: '充值记录',
        },
        {
          id: 2,
          name: '2',
          label: '通话账单'
        },
        {
          id: 3,
          name: '3',
          label: '月租记录'
        },
        {
          id: 4,
          name: '4',
          label: '成员消费统计'
        },
        {
          id: 5,
          name: '5',
          label: 'AI消费记录'
        },
        {
          id: 6,
          name: '6',
          label: 'AI成员消费记录'
        },
      ],


      // 通话充值接口传参
      callMenu: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      // 充值记录接口传参
      RechargeCode: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      log_list: [],
      log_params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        type: 1
      },
      recharge_list: [], // 充值记录列表
      detail: {},
      show_detail: false,
      isPlaying: false,
      membername:"",//用于检索成员
      memberid:"",//储存成员id
      sortvalue:"",
      quantitysort:[
        {id:1, name:"拨打数量排序" , value:"count"},
        {id:2, name:"通话时长排序" , value:"duration"},
        // {id:3, name:"总通话时长" , value:"total_duration"},
        {id:4, name:"消费金额" , value:"cost"}
      ],//排序
      timeValue:"",
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - 1);
            end.setDate(start.getDate());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本周',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上周',
          onClick(picker) {
            const end = new Date(); // 获取当前日期
              end.setDate(end.getDate() - end.getDay() - 7); // 获取上周的最后一天
              end.setHours(23, 59, 59, 0);
              const start = new Date(end);
              start.setDate(start.getDate() - 6); // 获取上一周的第一天
              start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上月',
          onClick(picker) {
            const end = new Date();
            end.setDate(0); // 获取上个月的最后一天
            end.setHours(23, 59, 59, 0);
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
      datevalue:"",//日期
      quantitydate:[
        {id:1, name:"今天" , value:"today"},
        {id:2, name:"昨天" , value:"yesterday"},
        {id:3, name:"本周" , value:"this_week"},
        {id:4, name:"上周" , value:"last_week"},
        {id:5, name:"本月" , value:"this_month"},
        {id:6, name:"上月" , value:"last_month"},
        {id:7, name:"自定义" ,value:"7" }
      ],
      Memberrank:[],//成员消费列表
      rankparams: {
        page: 1,
        per_page: 10,
        total: 0,
        times:"",//时间
        admin_id:"",//成员id
        order:"",//count:拨打数量排序，duration：通话时长排序，total_duration：总通话时长，cost：消费金额
      },
      AIdata:[],//AI消费记录
      AIparams:{
        page: 1,
        per_page: 10,
        total: 0,
      },
      AImemberparams:{
        page: 1,
        per_page: 10,
        total: 0,
        times:"",//时间检索
        order:"",// 数量降序:count 金额降序:money token总量降序:use_token 输入token量降序:input_token output_tokens:输出token量降序
      },
      AImemberdata:[],//AI成员消费记录
      AImembername:"",//AI成员消费记录成员检索
      AImembersort:[
        {id:1, name:"数量降序" , value:"count"},
        {id:2, name:"金额降序" , value:"money"},
        {id:3, name:"token总量降序" , value:"use_token"},
        {id:4, name:"输入token量降序" , value:"input_token"},
        {id:5, name:"输出token量降序" , value:"output_tokens"},
      ],//排序
      AItimeValue:"",//AI成员消费统计时间检索
      AIvalue:"",//AI成员消费统计时间检索
    }
  },
  created() {
    // this.getSeatsFees()
    // this.RechargeRecord()
    this.getLog()
  },
  methods: {
    // 点击tabs
    handleClick(e) {
      this.activeName = e._props.name
    },
    handleClickLog(e) {
      this.activeName1 = e._props.name
      this.log_params.type = e._props.name
      // this.log_list = []
      this.log_params.page = 1
      if(e._props.name==4){
        this.memberexpend()
      }else if(e._props.name==5){
        this.AIexpend()
      }else if(e._props.name==6){
        this.AImemberconsumption()
      }else{
         this.getLog()
      }
    },
    //选择成员
    MemberClick(item) {
      this.membername = item.name
      this.memberid = item.values
    },
    //点击触发选择框
    showPersonnelAuthority(){
      this.$nextTick(() => {
          if (this.$refs.get_black_list) {
            this.$refs.get_black_list.blur();
          }
        })
    },
    clearshowPersonne(e){
      if(e==undefined){
        this.rankparams.admin_id = ""
        this.memberid = ""
      }
    },
    //查询成员消费记录
    QueryRecords(){
      if(this.datevalue==7){
        this.rankparams.times = this.timeValue
      }else{
        this.rankparams.times = this.datevalue
      }
      if(this.memberid){
        this.rankparams.admin_id = this.memberid
      }
      if(this.sortvalue){
        this.rankparams.order = this.sortvalue
      }else{
        this.rankparams.order = ""
      }
      // console.log(this.rankparams,"didididididid");
      this.memberexpend()
    },
    memberexpend(){
      this.tel_table_loading =  true
      this.$http.memberexpend(this.rankparams).then((res)=>{
        if(res.status==200){
          this.Memberrank = res.data.data
          this.rankparams.total = res.data.total
          this.tel_table_loading = false
        }
      })
    },
    //AI消费记录
    AIexpend(){
      this.tel_table_loading =  true
      this.$http.aiexpend(this.AIparams).then((res)=>{
        if(res.status==200){
          // console.log(res.data.data);
          this.AIdata = res.data.data
          this.AIparams.total = res.data.total
          this.tel_table_loading = false
        }
      })
    },
    AImemberconsumption() {
      this.tel_table_loading =  true
      this.$http.aiexpandmember(this.AImemberparams).then((res)=>{
        if(res.status==200){
          // console.log(res.data.data);
          this.AImemberdata = res.data.data
          this.AImemberparams.total = res.data.total 
          this.tel_table_loading = false
        }
      })
    },
    //查询成员消费记录
    AIQueryRecords(){
      if(this.AIvalue==7){
        this.AImemberparams.times = this.AItimeValue
      }else{
        this.AImemberparams.times = this.AIvalue
      }
      // console.log(this.AImemberparams);
      this.AImemberconsumption()
    },
    //导出AI分析成员消费记录
    exportAImember(){ 
      // console.log(1323);
      this.$http.exportaiexpandmember(this.AImemberparams).then((res)=>{
        if(res.status==200){
          window.open(res.data);
        }
      })
    },
    //成员消费记录成员检索
    AIclearshowPersonne(e){
      if(e==undefined){
        this.AImemberparams.admin_id = ""
      }
    },
    //选择成员
    AIMemberClick(item) {
      this.AImembername = item.name
      this.AImemberparams.admin_id = item.values
    },
    //导出成员消费统计
    exportmember(){ 
      this.$http.exportmemberexpend(this.rankparams).then((res)=>{
        if(res.status==200){
          window.open(res.data);
        }
      })
    },
    onChangeTime(e) {
      console.log(e);
    },
    searchLog() {
      this.log_params.page = 1
      this.getLog()
    },
    getLog() {
    //  console.log(1111122222);
    this.tel_table_loading =  true
      this.$http.getOutbondTaocanLog(this.log_params).then(res => {
        if (res.status == 200) {
          console.log(res)
          this.log_list = res.data.data
          this.log_params.total = res.data.total
          this.tel_table_loading = false
        }
      })
    },
    // 点击通话充值
    CallRecharge() {
      this.dialogVisible = true
      this.$http.getCallMenu(this.callMenu).then(res => {
        if (res.status == 200) {
          this.tableData = res.data.data
          this.callMenu.total = res.data.total
        }
      })
    },
    seeLogDetail(row) {
      // this.detail = row
      this.getDetail(row.id)
      // this.$router.goPath("/bound_tel_detail?id=" + row.id)
    },
    getDetail(id) {
      this.$http.getBoundTelDetail(id).then(res => {
        if (res.status == 200) {
          this.detail = res.data
          this.show_detail = true
        }
      })
    },
    // 点击购买套餐
    buyMenu(row) {
      this.$http.getBuyMenuOrder({ package_id: row.id }).then(res => {
        if (res.status == 200) {
          this.erweiCode(res.data.id)
        }
      })
    },
    // 支付二维码
    erweiCode(id) {
      this.$http.buyCallMenuQRcode(id).then(res => {
        if (res.status == 200) {
          this.dialogPayCode = true
          this.codeImg =
            "data:image/png;base64," +
            btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            );
        }
      })
    },
    play() {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      audios.src = this.detail.record_url
      if (this.isPlaying) {
        audios.pause();
        this.isPlaying = false
      } else {
        audios.play();
        this.isPlaying = true
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.isPlaying = false
        this.$forceUpdate();
        console.log("结束执行" + this.detail.duration);
      }, this.detail.duration * 1000);
    },
    // 分页器当前页发生改变
    onPackagePageChange(val) {
      this.callMenu.page = val
      this.CallRecharge()
    },
    // 充值记录列表
    RechargeRecord() {
      this.$http.getRechargeRecord(this.RechargeCode).then(res => {
        if (res.status == 200) {
          this.rechargeVisible = true
          console.log(res)
          this.recharge_list = res.data.data
          this.RechargeCode.total = res.data.total
        }
      })
    },
    // 充值记录当前页发生变化
    RechargePageChange(val) {
      this.RechargeCode.page = val
      this.RechargeRecord()
    },
    logPageChange(val) {
      this.log_params.page = val
      this.getLog()
    },
    logPageChangeA(val){
      this.rankparams.page = val
      this.memberexpend()
    },
    logPageChangeB(val){
      this.AIparams.page = val
      this.AIexpend()
    },
    logPageChangeC(val){
      this.AImemberparams.page = val
      this.AImemberconsumption()
    },
    // 充值记录取消订单
    cancelOrder(row) {
      this.$http.deleteRechargeOrder(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "订单取消成功")
          this.RechargeRecord()
        }
      })
    },
    // 是否开启坐席收费
    getSeatsFees() {
      this.$http.getSeatsFees().then(res => {
        if (res.status == 200) {
          if (res.data == 0) {
            this.tabs_list.splice(1, 1)
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  padding: 20px;
}

.phoneMenu {
  .el-button:first-child {
    background-color: #17b63a;
  }

  .el-button:first-child,
  .el-button:nth-child(2) {
    width: 160px;
    height: 40px;
    box-sizing: border-box;
    padding: 9px 0px !important;
    font-size: 16px;
  }
}

.code-box {
  text-align: center;

  p {
    text-align: center;
    color: #6bcc03;
    font-size: 28px;
  }
}

.ml5 {
  margin-left: 5px;
}

.search_box {
  margin-bottom: 12px;
}
.conditionstyle{
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}
.backgauge{
  margin: 0px 15px 0px 15px;
}
.detail_box {
  height: 70vh;
  overflow-y: auto;

  .el-form-item {
    margin-bottom: 10px;
  }

  ::v-deep .el-form-item__label {
    text-align: left;
    // justify-content: flex-start;
  }
}

.audio_img {
  width: 100px;
  height: 20px;
  display: flex;
  background: #409eff;
  margin-top: 10px;

  // justify-content: center;
  align-items: center;

  .c_white {
    margin-left: 5px;
    color: #fff;
  }
}
</style>