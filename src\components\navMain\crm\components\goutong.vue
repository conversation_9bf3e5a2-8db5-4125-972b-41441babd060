<template>
    <div class="container">
        <!-- <div class="mini-tabs">
            <div
                v-for="item in tabs_value"
                :key="item.value"
                class="tabs-text"
                :class="{select: is_select == item.value}"
                @click="clickMiniTabs(item)"
            >
                {{ item.label }}
            </div>
        </div> -->
        <div
            class="timeline-record"
            v-infinite-scroll="loadMoreRecord"
        >
            <el-timeline v-if="callRecode_list && callRecode_list.length">
                <el-timeline-item 
                    v-for="(item, index) in callRecode_list"
                    :key="index"
                    color="#2D84FB">
                    <div class="agent_info flex-row align-center">
                        <div class="agent_name flex-row">
                            {{ item.admin && item.admin.department_name ? item.admin.department_name : '' }}
                            <span v-if="item.admin && item.admin.department_name">：</span>
                            {{ item.admin && item.admin.user_name ? item.admin.user_name : '' }}
                            <div class="agent_name_icon">录音</div>
                        </div>
                        <!-- <div class="agent_coordinate">
                            <i class="el-icon-location-outline"></i>
                            <span style="margin-left: 5px;">21世纪房产</span>
                        </div> -->
                        <div class="time">
                            <i class="el-icon-time"></i>
                            <span style="margin-left: 5px;">{{ item.created_at }}</span>
                        </div>
                    </div>
                    <div class="agent_content flex-row">
                        <div>
                            <span>话术执行率：{{ item.text_ratio }}</span>
                            <span style="margin-left: 11px;">录音量（条数）：{{ item.number }}</span>
                            <!-- duration -->
                            <span style="margin-left: 13px;">录音时长：{{ item.duration | formatTime }}（{{ item.duration | branchTime }}min）</span>
                        </div>
                    </div>
                    <div class="agent_communication">
                        <communicationPlayer
                            ref="player"
                            v-if="item.id && item.record_url"
                            :activity="item"
                            :record_url="item.record_url"
                            :duration_time="item.duration"
                            :indexs="index"
                            :info_id = 'info_id'
                            :type = 'type'
                            :show_analysis="item.number == 0 ? false : true"
                            @resetPlay="resetPlay"
                        ></communicationPlayer>
                    </div>
                <div class="separate"></div>
                </el-timeline-item>
            </el-timeline>
            <myEmpty v-else></myEmpty>
        </div>
    </div>
</template>
<script>
import myEmpty from "@/components/components/my_empty.vue";
import communicationPlayer from "@/components/components/communicationPlayer.vue"
export default {
    components: {
        myEmpty,
        communicationPlayer,
    },
    props: {
        // 当前客户id
        info_id: {
            type: [String, Number],
            default: ''
        },
        // 当前客户是1公海/2私客
        type: {
            type: [String, Number],
            default: ''
        },
    },
    data() {
        return {
            tabs_value:[
                {label: "全部", value: 1},
                {label: "外呼录音", value: 2},
                {label: "工牌录音", value: 3},
            ],
            is_select: 1,
            timeline: [], // 沟通记录列表容器
            // 沟通记录接口请求参数
            callRecode_params: {
                page: 1, // 当前页
                per_page: 10, // 每页多少条
                client_id: '', // 客户id
            },
            callRecode_list: [], // 沟通记录数据
            loadMore: false, // 加载更多沟通记录
        }
    },
    created() {
        this.callRecode_params.client_id = this.info_id; // 赋值当前客户id
        this.getCallRecode(); // 获取沟通记录列表
    },
    filters: {
        formatTime(seconds) {
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        },
        branchTime(seconds) {
            const minutes = seconds / 60;
            return minutes.toFixed(2); // 如果想要保留小数点后两位
        }
    },
    methods: {
        // 点击顶部tabs切换
        clickMiniTabs(item) {
            console.log(item,"item");
            this.is_select = item.value;
        },
        // 通过下拉加载更多沟通记录
        loadMoreRecord() {
            if(!this.loadMore) {
                return;
            }
            console.log("下拉加载");
            this.getCallRecode(); // 获取沟通记录列表
        },
        // 重置播放按钮为暂停
        resetPlay() {
            this.callRecode_list.map((item) => {
                this.$set(item, 'is_play', false);
            })
            // 触发子组件forceUpdate更新页面显示
            this.$refs.player.map((item) => {
                item.upPlay()
            })
        },
        // // 跳转录音详情
        // getDeatil(item) {
        //     this.$goPath(`recording_Details?r_id=${item.record_id}&u_id=${this.info_id}&type=${this.type}`);
        //     sessionStorage.setItem('record_Detail', JSON.stringify(item)); // 存储当前录音信息
        // },
        // 获取沟通记录列表
        getCallRecode() {
            this.$http.getCallRecode(this.callRecode_params).then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"沟通记录数据");
                    if(res.data.data.length < this.callRecode_params.per_page) {
                        this.loadMore = false;
                    } else {
                        this.loadMore = true;
                    }
                    this.callRecode_list = this.callRecode_list.concat(res.data.data);
                    this.callRecode_list.map((item) => {
                        item.is_play = false;
                    })
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.container {
    .mini-tabs {
        display: flex;
        flex-direction: row;
        .tabs-text {
            padding: 2px 8px;
            color: #8A929F;
            font-size: 12px;
            margin-right: 18px;
            cursor: pointer;
        }
        .select {
            color: #2D84FB;
            background: #E8F1FF;
            border-radius: 5px;
        }
    }
    .timeline-record {
        max-height: 600px;
        overflow-y: auto;
        margin-top: 14px;
        box-sizing: border-box;
        padding: 3px 0px 0 1px;
        .agent_info {
            .agent_name {
                color: #2E3C4E;
                .agent_name_icon {
                    line-height: 19px;
                    font-size: 12px;
                    color: #2D84FB;
                    border-radius: 5px;
                    background: #E8F1FF;
                    margin-left: 12px;
                    padding: 0 3px;
                }
            }
            .agent_coordinate {
                line-height: 1;
                color: #8A929F;
                font-size: 12px;
                margin-left: 22px;
            }
            .time {
                line-height: 1;
                color: #8A929F;
                font-size: 12px;
                margin-left: 21px;
            }
        }
        .agent_content {
            align-items: center;
            justify-content: space-between;
            color: #2E3C4E;
            margin-top: 15px;
            .agent_content_button {
                color: #2D84FB;
                font-size: 14px;
                cursor: pointer;
                margin-right: 20px;
            }
        }
        .agent_communication {
            margin-top: 9px;
        }
        .el-timeline {
            .el-timeline-item {
                padding-bottom: 14px;
            }
        }
        .separate {
            // border: 1px solid #DDE1E9;
            position: relative;
            margin-top: 18px;
        }
        .separate:after {
            content: " ";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background-image: linear-gradient(0deg, #DDE1E9 50%, transparent 50%);
        }
    }
}
</style>