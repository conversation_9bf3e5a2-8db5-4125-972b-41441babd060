import { Message } from 'element-ui';

class Validate {
    constructor(rules) { 
        this.rules = rules;
        this.errors = [];
        this.bacthCheck = false;
    }
    
    batch(flag){
        this.bacthCheck = flag;
    }

    displayErrors(){
        if(this.bacthCheck){
            //TODO
        }else{
            Message.error(this.errors[0].msg); 
        }
    }

    check(data, customHandleErrorFn){
        let flag = true;
        for(const field in this.rules){
            const value = data[field];
            const rule = this.rules[field];
    
            if(rule.require){
                flag = this.checkRequire(value)
                if(!flag){
                    this.errors.push({ filed: field, msg: typeof rule.require === 'string' ? rule.require : field +'为必填项'});
                    
                }
            }
            
         
            if(flag || this.bacthCheck){
                //TODO
            }
            if(!flag && !this.bacthCheck){
                break;
            }
        }
        if(customHandleErrorFn){
            customHandleErrorFn(this.errors);
        }else{
            if(!flag){
                this.displayErrors();
            }
        }
        return flag;
    }

    checkRequire(value){console.log(value)
        if(value == null) return false;
        if(typeof value === 'string' && value.trim() === ''){
            return false;
        }
        if(Array.isArray(value) && value.length == 0){
            return false;
        }
        return true;
    }

    static create(rules) { 
        return new Validate(rules); 
    }
}


export default Validate.create;