<template>
    <div v-fixed-scroll="62">
      <div class="pagecontent">
          <div class="SearchCondition">
                <div class="block">
                  <el-select style="width: 100px;margin-right:10px;" size="small" 
                  v-model="paramsdata.date_type" key="zhubooptions" placeholder="请选择" @change="Refresh">
                    <el-option
                      v-for="item in zhubooptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                    </el-option>
                  </el-select>
                  <el-date-picker style="width: 300px" v-model="timeValue" type="datetimerange" size="small" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                    @change="onChangeTime">
                  </el-date-picker>
                  <el-cascader style="margin:0px 10px;" size="small" v-model="memberA_value"
                    :options="member_listNEW" clearable filterable placeholder="成员" :style="{
                        minWidth: '20px',
                         width: '110px',
                        }" :props="{ 
                            label: 'user_name',
                            value: 'id',
                            children: 'subs',
                            checkStrictly: true,
                        }" @change="loadFirstLevelChildren">
                    </el-cascader>
                </div>

                <div class="head-list">
                  <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;margin: 7px 10px 0px 0px;">
                    <div slot="content" style="max-width: 300px">
                      数据统计来源于:财务-成交报告  &nbsp;&nbsp;  <el-link type="primary" @click="tochengjiao">去补全></el-link>
                    </div>
                    <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                  </el-tooltip>
                  <el-button v-show="show" size="small" type="primary" @click="exporttakelook">导出</el-button>
                </div>
          </div>
        <el-table
              v-loading="is_table_loading"
              :data="anchoroperationdata"
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }"
              border>
              <el-table-column
                prop="user_name"
                align="center"
                label="成员名称"
                fixed="left">
              </el-table-column>
                <el-table-column label="账号名称"
                align="center"
                prop="refer_dy_name">
                </el-table-column>
                  <el-table-column
                    prop="refer_dy_id"
                    align="center"
                    label="账号id">
                  </el-table-column>
                  <el-table-column
                    prop="total_custom_num"
                    align="center"
                    label="线索总量">
                  </el-table-column>
                  <el-table-column
                    prop="dk_dy_count"
                    align="center"
                    label="抖音带看总量"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    prop="dk_video_count"
                    align="center"
                    label="视频号带看总量"
                    width="150">
                  </el-table-column>
                  <el-table-column
                    prop="cj_dy"
                    align="center"
                    label="抖音成交量"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    prop="cj_video"
                    align="center"
                    label="视频号成交量"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    prop="amount_dy"
                    align="center"
                    label="抖音成交金额"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    prop="amount_video"
                    align="center"
                    label="视频号成交金额"
                    width="120">
                  </el-table-column>
                  <el-table-column
                    prop="un_yj_dy"
                    align="center"
                    label="抖音佣金(返佣前)"
                    width="150">
                  </el-table-column>
                  <el-table-column
                    prop="un_yj_video"
                    align="center"
                    label="视频号佣金(返佣前)"
                    width="150">
                  </el-table-column>
                  <el-table-column
                    prop="yj_dy"
                    align="center"
                    label="抖音佣金(返佣后)"
                    width="150">
                  </el-table-column>
                  <el-table-column
                    prop="yj_video"
                    align="center"
                    label="视频号佣金(返佣后)"
                    width="150">
                  </el-table-column>
            </el-table>
            <div class="page_footer flex-row items-center">
                <div class="page_footer_l flex-row flex-1 items-center">
                  <div class="head-list">
                    <el-button type="primary" size="small" @click="empty">清空</el-button>
                  </div> 
                  <div class="head-list">
                    <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                  </div>
                </div> 
                <div style="margin-right:10px;">
                  <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="paramsdata.total"
                    :page-sizes="[10, 20, 30, 50,100]" :page-size="paramsdata.per_page" :current-page="paramsdata.page"
                    @current-change="onPageChange" @size-change="handleSizeChange">
                  </el-pagination>
                </div>
              </div>
      </div>
    </div>
</template>
<script>
export default {
    props: {
      member_listNEW: {
            type: Array,
            default:() => []
        },
        show:{
          type: Number,
          default: false
        }
    },
    data() {
        return {
          timeValue:"",//时间检索
          zhubooptions:[
              {id:1,name:"周"},
              {id:2,name:"月"},
          ],
          is_table_loading:false,//表格加载
          anchoroperationdata:[],//主播运营数据
          paramsdata:{
              per_page:10,
              page:1,
              start_date:"",
              end_date:"",
              date_type:1,
              admin_id:"",//成员id
          },
          memberA_value:"",
        }
    },
    mounted() {
      let pagenum = localStorage.getItem( 'pagenum')
          this.paramsdata.per_page = Number(pagenum)||10
      this.getAnchorOperationData()
    },
    methods:{
      //获取主播运营数据列表
      getAnchorOperationData(){
          this.is_table_loading = true
            this.$http.getanchoroperationdata(this.paramsdata).then((res)=>{
                if(res.status==200){
                  // console.log(res);
                  this.is_table_loading = false
                    this.anchoroperationdata = res.data.data
                    this.paramsdata.total = res.data.total
                }
            })
      },
      //成员切换检索
      loadFirstLevelChildren(value) {
          this.paramsdata.admin_id = value.toString();
          this.Refresh()
      },
      // 自定义筛选时间发生改变时触发
      onChangeTime(e) {
          this.paramsdata.date_type = ""
          if (e && e.length >= 2) {
            if (e[1].endsWith("00:00:00")) {
              e[1] = e[1].slice(0, -8) + "23:59:59";
            }
          }
          if(e){
            this.paramsdata.start_date = e[0]
            this.paramsdata.end_date = e[1]
          }else{
            this.paramsdata.start_date = ""
            this.paramsdata.end_date = ""
          }
          this.Refresh()
      },
      //清空
      empty(){
          this.paramsdata={
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                date_type:1,
                admin_id:"",// 部门id
            }
         this.Refresh()
      },
      //刷新
      Refresh(){
        this.getAnchorOperationData()
      },
      //分页
      onPageChange(current_page) {
          this.paramsdata.page = current_page
          this.Refresh()
      },
      //每页几条
      handleSizeChange(e){
          this.paramsdata.per_page = e
          this.Refresh()
      },
      //导出
      exporttakelook(){
          this.$confirm('确定要导出数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.exportanchoroperationdata(this.paramsdata).then(res=>{
            if(res.status==200){
              window.open(res.data);
            }
          })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出'
            });          
          });
      },
      //跳转到成交报告
      tochengjiao(){
        this.$goPath('/crm_deal_report_index')
      },
    },
}
</script>
<style lang="scss" scoped>
::v-deep .el-table .el-table__fixed{
          padding-bottom: 0px !important;
}
.pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .SearchCondition{
            margin: 20px;
            display: flex;
            justify-content: space-between;
            .block{
              display: flex;
            }
        }
        .head-list{
          display: flex;
        }
        .taketable{
            width: 97%;
            margin: 0 auto;
            margin-bottom: 20px;
            .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
            ::v-deep .el-table thead.is-group th{
              text-align: center;
            }
            .card-container {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              gap: 15px;
              padding: 0px 0px 20px 0px;
            }

            .data-card {
              background-color: #e6f7ff; // 浅蓝色背景
              border-left: 5px solid #409EFF; // 左侧边栏颜色
              padding: 15px;
              border-radius: 5px;
              width: calc(16% - 5px); // 每行显示5个卡片，
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
              // transition: transform 0.2s;
            
              // &:hover {
              //   transform: scale(1.02);
              // }
            
              p {
                margin: 5px 0;
              }
            }
        }
}
</style>