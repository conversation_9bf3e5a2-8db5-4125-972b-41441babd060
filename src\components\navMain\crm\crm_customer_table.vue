<template>
  <div class="tabel-all">
    <div class="content-box-crm">
      <div class="bottom-border div row" style="padding-top: 10px; display: flex; align-items: center;">
        <p style="margin-top: 10px;color: #a1a1a1;">筛选条件：</p>
        <div class="div row" style="margin-top: 10px">
          <el-select class="crm-selected-label" v-model="params.times_cate"  placeholder="时间类型"
            style="width: 100px;" @change="search">
            <el-option v-for="item in timeTypeObj" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <div class="block" style="margin-left: 10px; background-color: aqua;">
            <!-- <span class="demonstration">带快捷选项</span> -->
            <el-date-picker style="width: 350px;height: 100%;" v-model="params.times" type="datetimerange" size="small"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
              @change="search">
            </el-date-picker>
          </div>
        </div>
        <div class="head-list">
          <el-select class="crm-selected-label" v-model="params.payment_status" clearable placeholder="回款状态" :style="{
            minWidth: '20px',
            width: '110px',
          }" @change="search">
            <el-option v-for="item in collectionState" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="head-list">
          <el-cascader :options="memberList" placeholder='部门筛选' :props="{
            value: 'id',
            label: 'name',
            children: 'subs',
            emitPath: false,
            multiple: false,
          }" clearable filterable v-model="params.department_id" @change="search"></el-cascader>
        </div>
        <div class="head-list">
          <el-select class="crm-selected-label" clearable :placeholder="zuoxi_user.name" :style="{
            minWidth: '20px',
            width: '180px',
          }" v-model="params.cj_admin_id" filterable @change="search">
            <el-option v-for="item in trader" :key="item.id" :label="item.user_name" :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div class="title-box">
      <div class="input-all">
        <el-select class="crm-selected-label" v-model="params.searchKey" placeholder="搜索类型"
          style="width: 100px;margin-right: 10px;">
          <el-option v-for="item in searchType" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-input placeholder="请输入搜索内容" v-model="params.keywords" class="input-with-select" style="width: 200px;">
          <el-button slot="append" icon="el-icon-search" @click="search" clearable></el-button>
        </el-input>
      </div>
      <div class="button-all">
        <el-button type="primary" size="medium" @click="addcustomer()">添加成交单</el-button>
        <el-button type="primary" size="medium" @click="exportReport">导出</el-button>
        <el-button type="danger" size="medium" @click="cancelReport">作废</el-button>
      </div>
    </div>
    <el-table v-loading="is_table_loading" :data="tableData" class="house_table" border
      :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" fixed="left" width="55" align="center"/>
      <el-table-column fixed label="成交时间" v-slot="{ row }" width="180">
        {{ row.cjrq ? row.cjrq : "--" }}
      </el-table-column>
      <el-table-column fixed label="客户名称" v-slot="{ row }" width="180">
        {{ row.name ? row.name : "--" }}
      </el-table-column>
      <el-table-column fixed label="客户手机号" v-slot="{ row }" width="180">
        {{ row.phone ? row.phone : "--" }}
      </el-table-column>
      <el-table-column label="项目名称" v-slot="{ row }" width="180">
        {{ row.mianji ? row.project : "--" }}
      </el-table-column>
      <el-table-column label="面积" v-slot="{ row }" width="180">
        {{ row.mianji ? row.mianji : "--" }} m²
      </el-table-column>
      <el-table-column label="单价" v-slot="{ row }" width="180">
        {{ row.danjia ? row.danjia : "--" }} 元
      </el-table-column>
      <el-table-column label="佣金占比" v-slot="{ row }" width="180">
        {{ row.proportion ? row.proportion : "--" }}
      </el-table-column>
      <el-table-column label="成交金额" v-slot="{ row }" width="100">
        {{ row.amount ? row.amount : "--" }}
      </el-table-column>
      <el-table-column label="回款时间" v-slot="{ row }" width="180">
        {{ row.payment_status_time ? row.payment_status_time : "--" }}
      </el-table-column>
      <el-table-column label="应收佣金" v-slot="{ row }" width="180">
        {{ row.receive_commission.commission ? row.receive_commission.commission : "--" }}
      </el-table-column>
      <el-table-column label="扣除款项" v-slot="{ row }" width="180">
        {{ row.company_commission.commission ? row.company_commission.commission : "--" }}
      </el-table-column>
      <el-table-column label="成员佣金" v-slot="{ row }" width="180">
        {{ row.member_commission.commission ? row.member_commission.commission : "--" }}
      </el-table-column>
      <el-table-column label="操作" fixed="right" v-slot="{ row }" width="180">
        <el-link style="margin-right: 20px;" type="primary" @click="onEditData(row)">编
          辑</el-link>
        <el-link type="danger" @click="deleteData(row)">删 除</el-link>
      </el-table-column>
    </el-table>
    <el-col :span="24" class="toolbar" style="margin:12px 0 0;text-align: right;">
      <el-pagination background
          layout="total,sizes,prev, pager, next, jumper"
          :total="params.total"
          :page-sizes="[10, 20, 30, 50,100]"
          :page-size="params.per_page"
          :current-page="params.page"
          @current-change="onPageChange"
          @size-change="handleSizeChange"
        >
      </el-pagination>
    </el-col>

  </div>
</template>
<script>
import myPagination from "@/components/components/my_pagination";
export default {
  name: "crm_customer_table",
  components: {
    myPagination
  },
  data() {
    return {
      show_select_dia: false,
      is_table_loading: true,
      input: '',
      tableData: [],
      multipleSelection: [],
      memberList: [],
      deptMemberList: [],
      //时间类型
      timeTypeObj: [
        { value: 1, label: '成交时间' },
        { value: 2, label: '创建时间' },
        { value: 3, label: '回款时间' },
      ],
      //搜索类型
      searchType: [
        //{ value: 0, label: '全部' },
        { value: 'name', label: '客户名称' },
        { value: 'phone', label: '客户手机号' },
        { value: 'cj_num', label: '客户编号' },
        { value: 'project', label: '认购项目' },
      ],
      //回款类型
      collectionState: [
        { value: -1, label: '全部' },
        { value: 1, label: '已回款' },
        { value: 0, label: '未回款' },
      ],
      
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: '',//搜索关键词(客户姓名或备注)
        times: [],
        searchKey: 'name',
        times_cate: 1,
        payment_status: -1,
        department_id: '',
        cj_admin_id: ''

      },
      timeValue: "",
      zuoxi_user: {
        id: null,
        name: '成交人筛选'
      },
      screenParam: {
        times_cate: 0,//时间类型
        times: '',//时间范围
        payment_status: -1,//回款状态
        department_id: 0,//部门筛选
        cj_admin_id: 0,//成交人筛选
        project: ''//成交人
      }
    }
  },
  watch: {
    'params.department_id'(){
      console.log();
      if(this.params.cj_admin_id){
        if(!this.trader.find(e => e.id == this.params.cj_admin_id)){
          this.params.cj_admin_id = '';
        }
      } 
    }
  },
  computed: {
    //成交人
    trader(){
      return this.deptMemberList.filter(e => {
        return this.params.department_id ? e.wx_work_department_id.split(",").includes(String(this.params.department_id)): true;
      })
    }
  },
  mounted() {
    this.getDepartment()
    this.getDepartmentMemberList()
    this.gettabelList(this.screenParam)
  },
  methods: {
    addcustomer() {
      this.$goPath('/crm_customer_tableDeatil?operationType=add');
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList()
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    async getDepartmentMemberList() {
      let res = await this.$http.getDepartmentMemberList()
      if (res.status == 200) {
        this.setDeptMemberList(res.data);
        console.log(this.deptMemberList);
      }
    },
    setDeptMemberList(data){
      data.forEach(e => {
        if(e.subs && e.subs.length){
          this.setDeptMemberList(e.subs);
        }
        if(e.user && e.user.length){
          this.deptMemberList = this.deptMemberList.concat(e.user.filter(user => {
            return !this.deptMemberList.find(d => d.id == user.id)
          }));
        }
      })
    },
    gettabelList() {
      const params = this.getQueryParams();
      this.$http.getReportListAPI(params).then((res) => {
        this.tableData = res.data.data
        this.is_table_loading = false;
        this.params.total = res.data.total
      })
    },
    getQueryParams(){
      const params = {...this.params};
      params.times = params.times?.length ? params.times.join(',') : '';
      params[params.searchKey] = params.keywords;

      delete params.total;
      delete params.searchKey;
      delete params.keywords;
      return params;
    },
    search(){
      this.params.page = 1;
      this.gettabelList();
    },
    delReport(id) {
      this.$http.delReportAPI(id).then(() => {
        this.gettabelList(this.screenParam)
      })
    },
    onEditData(row) {
      this.$goPath('/crm_deal_report_detail?id='+row.id+'&operationType=edit');
   /*    this.$router.push({
        path: 'crm_customer_tableDeatil', query: {
          id: row.id, operationType: 'edit'
        }
      }); */
    },
    onPageChange(e) {
      this.params.page = e;
      this.gettabelList()
    },
    handleSizeChange(e){
      this.params.per_page = e;
      this.search();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 删除成交单
    deleteData(item) {
      this.$confirm("确认要删除吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.delReport(item.id)
      })
    },
    //导出成交报告
    async exportReport(){
      const params = this.getQueryParams();
      const res = await this.$http.exportCrmDealReport(params);
      if(res.status == 200){
        window.open(res.data);
      }
    },
    //作废
    async cancelReport(){
      if (!this.multipleSelection.length) {
        return this.$message({
          message: '请选择要作废的数据',
          type: 'warning'
        })
      }
      
      const ids = this.multipleSelection.map(e=>e.id).join(',');
      const res = await this.$http.cancelCrmDealReport({ids});
      if(res.status == 200){
        this.gettabelList()
        this.$message.success(res.data?.msg || '作废成功');
      }
    }
  }
}
</script>
<style  scoped lang="scss">
.title-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 20px;

  .input-all {
    display: flex;
  }
}

.head-list {
  margin-left: 10px;
  margin-top: 10px;
}

.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  flex-wrap: wrap;
}
</style>