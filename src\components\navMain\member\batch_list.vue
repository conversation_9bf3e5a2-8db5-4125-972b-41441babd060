<template>
  <el-container>
    <el-header class="div row" style="justify-content:space-between">
      <div class="div row">
        <div class="title">批次列表</div>
        <div class="title_number">
          <div>
            当前页面共（<i>{{ tableData.length }}</i
            >）条数据
          </div>
        </div>
      </div>

      <div class="div row">
        <el-button
          style="margin-right:20px"
          type="primary"
          @click="dialogCreateBatch = true"
          >添加新批次</el-button
        >
        <el-cascader
          @change="changeTableCascader"
          :options="bill_search_options"
          clearable
        >
        </el-cascader>
        <el-select
          v-if="params.category"
          v-model="table_search_value"
          filterable
          remote
          clearable
          reserve-keyword
          :placeholder="placeholderTableVal"
          :remote-method="remoteMethodTable"
          :loading="loading_search"
          @change="changeSelectTable"
        >
          <el-option
            v-for="(item, index) in table_search_value_list"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </el-header>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <div class="pagination-box">
      <myPagination
        :total="params.total"
        :currentPage="params.page"
        :pagesize="params.per_page"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </div>
    <el-dialog
      :title="titleMap[dialogTitleBatch]"
      :visible.sync="dialogCreateBatchData"
      :close-on-click-modal="false"
    >
      <el-form :model="form_create_data" label-width="100px">
        <el-form-item label="金额">
          <el-input
            placeholder="请输入具体金额"
            v-model="form_create_data.paid_amount"
            step="1"
            type="number"
            min="0"
            ><template slot="append">元</template></el-input
          >
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="form_create_data.paid_date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="onClickForm"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="开票"
      :visible.sync="dialogCreate"
      @close="invoiceClose"
    >
      <el-form :model="form_create_invoice" label-width="100px">
        <el-form-item label="金额">
          <el-input
            type="number"
            min="0"
            step="1"
            v-model="form_create_invoice.amount"
            placeholder="请输入金额"
            ><template slot="append">元</template></el-input
          >
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="form_create_invoice.operation_date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            placeholder="备注信息"
            v-model="form_create_invoice.remark"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="onClickInvoiceForm"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-drawer
      :wrapperClosable="false"
      :visible.sync="dialogCreateBatch"
      size="50%"
      @close="onDrawerClose"
    >
      <el-form :model="form_create_batch" style="padding:20px">
        <el-form-item label="批次名称：">
          <el-input
            placeholder="请输入批次名称"
            v-model="form_create_batch.name"
          ></el-input>
        </el-form-item>
        <el-form-item label="收/付类型：">
          <el-select
            style="width:100px"
            @change="changeCategory"
            v-model="form_create_batch.category"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item, index) in sale_order_bill_category_list"
              :key="index"
              :label="item.description"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-select
            style="width:120px"
            v-if="form_create_batch.category"
            clearable
            v-model="form_create_batch.object_category"
            placeholder="请选择"
            @change="changeCategoryType"
          >
            <el-option
              v-for="(item, index) in category_list"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-select
            v-if="form_create_batch.category"
            v-model="search_value"
            filterable
            remote
            clearable
            reserve-keyword
            :placeholder="placeholderVal"
            :remote-method="getRemoteMethod"
            :loading="loading_search"
            @change="changeSelect"
          >
            <el-option
              v-for="(item, index) in table_category_type_list"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择账单" v-if="istableselect">
          <myTable
            :table-list="bill_tableData"
            :header="bill_tableData_header"
            select
            :max_height="400"
            @selection-change="handleSelectionChange"
          ></myTable>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="onClickBatch"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-drawer>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "batch_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      form_create_batch: {},
      dialogCreateBatch: false,
      payment_list: [], // 获取账单列表
      sale_order_bill_category_list: [], // 获取分类列表
      broker_loading: false,
      form_create_data: {},
      dialogCreateBatchData: false,
      titleMap: {
        earning: "应收金额",
        disburse: "应付金额",
      },
      dialogTitleBatch: "",
      dialogTitle: "",
      form_create_invoice: {},
      dialogCreate: false,
      form_create_batch_params: {},
      bill_params: {
        page: 1,
        per_page: 100,
        batch_id: 0,
      },
      bill_tableData: [],
      //  0 应收  1 应付
      bill_search_options: [
        {
          value: 0,
          label: "应收",
          children: [
            { value: 1, label: "客户" },
            { value: 2, label: "项目公司" },
          ],
        },
        {
          value: 1,
          label: "应付",
          children: [
            { value: 3, label: "经纪人" },
            { value: 4, label: "销售公司" },
          ],
        },
      ],
      // 远程搜索值
      search_value: "",
      loading_search: false,
      cascader_value: "",
      // 搜索存数据
      search_value_list: [],
      placeholderVal: "请输入内容",
      placeholderTableVal: "请输入内容",
      // 列表数据
      table_search_value: "",
      table_cascader_value: "",
      table_search_value_list: [],
      /**
       * 修改选择收付类型
       * */
      category_list: [],
      table_category_type_list: [],
      table_category_cascader_value: "",
      istableselect: false,
      object_category_list: [],
      table_header: [
        {
          label: "账单类型",
          render: (h, data) => {
            return (
              <div>
                {data.row.category === 0 ? "应收" : "应付"} /{" "}
                {this.$computedValueType(
                  this.object_category_list,
                  data.row.object_category
                )}
              </div>
            );
          },
        },
        {
          prop: "name",
          label: "批次名称",
        },
        {
          label: "应付金额/元",
          render: (h, data) => {
            return <el-tag type="primary">{data.row.amount}</el-tag>;
          },
        },
        {
          label: "已付金额/元",
          render: (h, data) => {
            return (
              <el-tag
                onClick={() => {
                  this.jumpPayment(data.row);
                }}
                type={data.row.paid_amount === "0.00" ? "danger" : "success"}
              >
                {data.row.paid_amount}
              </el-tag>
            );
          },
        },
        {
          label: "已开票金额/元",
          render: (h, data) => {
            return (
              <el-tag
                onClick={() => {
                  this.jumpInvoice(data.row);
                }}
                type={
                  data.row.make_invoice_amount === "0.00" ? "danger" : "success"
                }
              >
                {data.row.make_invoice_amount}
              </el-tag>
            );
          },
        },
        {
          label: "交易",
          more_tab_header: [
            { prop: "project_company_name", label: "项目公司" },
            { prop: "sale_company_name", label: "销售公司" },
            {
              label: "经纪人",
              render: (h, data) => {
                return (
                  <div>
                    {data.row.bu_name ||
                      data.row.bu_nickname ||
                      data.row.bu_user_name}
                    {data.row.bu_phone ? (
                      <el-tag size="mini" type="success">
                        {data.row.bu_phone}
                      </el-tag>
                    ) : (
                      ""
                    )}
                  </div>
                );
              },
            },
            { prop: "customer_phone", label: "客户" },
          ],
        },
        {
          label: "批次单号",
          render: (h, data) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goBillList(data.row.bill_batch_sn);
                }}
              >
                {data.row.bill_batch_sn}
              </el-link>
            );
          },
        },
        {
          label: "操作",
          width: "120",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.params.category == 1 || this.params.category == 0 ? (
                  <el-button
                    onClick={() => {
                      this.onClickPay(data.row);
                    }}
                    type="success"
                    size="mini"
                  >
                    {this.params.category == 1 ? "付款" : "收款"}
                  </el-button>
                ) : (
                  ""
                )}
                <el-button
                  type="success"
                  size="mini"
                  onClick={() => {
                    this.createBatch(data.row);
                  }}
                >
                  开票
                </el-button>
              </div>
            );
          },
        },
      ],
      bill_tableData_header: [
        { prop: "id", label: "ID", width: "100" },
        {
          label: "账单类型",
          render: (h, data) => {
            return (
              <P>
                {data.row.category === 0 ? "应收" : "应付"}/
                {data.row.object_category === 1
                  ? "客户"
                  : data.row.object_category === 2
                  ? "项目公司"
                  : data.row.object_category === 3
                  ? "经纪人"
                  : data.row.object_category === 4
                  ? "销售公司"
                  : "位置"}
              </P>
            );
          },
        },
        { prop: "project_company_name", label: "项目公司" },
        { prop: "sale_company_name", label: "销售公司" },
        {
          label: "客户",
          render: (h, data) => {
            return (
              <div>
                {data.row.so_c_name}
                <el-tag size="mini" type="success">
                  {data.row.so_c_phone}
                </el-tag>
              </div>
            );
          },
        },
        {
          label: "经纪人",
          render: (h, data) => {
            return (
              <div>
                {data.row.bu_name ||
                  data.row.bu_nickname ||
                  data.row.bu_user_name}
                <el-tag size="mini" type="success">
                  {data.row.bu_phone}
                </el-tag>
              </div>
            );
          },
        },
        {
          label: "已开票金额",
          render: (h, data) => {
            return (
              <el-tag
                type={
                  data.row.make_invoice_amount === "0.00" ? "danger" : "success"
                }
                size="mini"
              >
                {data.row.make_invoice_amount}
              </el-tag>
            );
          },
        },
        {
          label: "已付金额",
          render: (h, data) => {
            return (
              <el-tag
                size="mini"
                type={data.row.paid_amount === "0.00" ? "danger" : "success"}
              >
                {data.row.paid_amount}
              </el-tag>
            );
          },
        },
        {
          label: "应付金额",
          render: (h, data) => {
            return (
              <el-tag
                size="mini"
                type={data.row.amount === "0.00" ? "danger" : "success"}
              >
                {data.row.amount}
              </el-tag>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "SALE_ORDER_BILL_CATEGORY":
            this.sale_order_bill_category_list = item.childs;
            break;
          case "SALE_ORDER_BILL_OBJECT_CATEGORY":
            this.object_category_list = item.childs;
            break;
        }
      });
    });
    if (this.$route.query.batch_sn) {
      this.params.bill_batch_sn = this.$route.query.batch_sn;
    }
    this.getDataList();
    this.getBillList();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      if (!this.params.category) {
        delete this.params.category;
      }
      if (!this.params.customer_phone) {
        delete this.params.customer_phone;
      }
      if (!this.params.project_company_id) {
        delete this.params.project_company_id;
      }
      if (!this.params.broker_user_id) {
        delete this.params.broker_user_id;
      }
      if (!this.params.sale_company_id) {
        delete this.params.sale_company_id;
      }
      if (!this.params.object_category) {
        delete this.params.object_category;
      }
      if (!this.params.bill_batch_sn) {
        delete this.params.bill_batch_sn;
      }
      this.$http.getBatchData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    // 获取账单列表
    getBillList() {
      if (!this.bill_params.category) {
        delete this.bill_params.category;
      }
      if (!this.bill_params.customer_phone) {
        delete this.bill_params.customer_phone;
      }
      if (!this.bill_params.project_company_id) {
        delete this.bill_params.project_company_id;
      }
      if (!this.bill_params.broker_user_id) {
        delete this.bill_params.broker_user_id;
      }
      if (!this.bill_params.sale_company_id) {
        delete this.bill_params.sale_company_id;
      }
      if (!this.bill_params.object_category) {
        delete this.bill_params.object_category;
      }
      this.$http.getBillList({ params: this.bill_params }).then((res) => {
        if (res.status === 200) {
          this.bill_tableData = res.data.data;
        }
      });
    },
    onClickBatch() {
      this.$http.createBatchData(this.form_create_batch).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "添加成功",
            type: "success",
          });
          this.getDataList();
          this.bill_tableData = [];
          this.dialogCreateBatch = false;
        }
      });
      this.form_create_batch = {
        name: "",
        bill_ids: "",
        category: "",
        object_category: "",
      };
      this.search_value = "";
    },
    changePayStatus() {
      this.getDataList();
    },
    onClickPay(row) {
      this.form_create_data.id = row.id;
      this.dialogCreateBatchData = true;
      this.params.category == 1
        ? (this.dialogTitleBatch = "disburse")
        : (this.dialogTitleBatch = "earning");
    },
    onClickForm() {
      if (this.dialogTitleBatch === "disburse") {
        this.$http
          .createBatchDisburseData(this.form_create_data)
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.getDataList();
              this.dialogCreateBatchData = false;
            }
          });
      } else if (this.dialogTitleBatch === "earning") {
        this.$http.createBatchEarningData(this.form_create_data).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreateBatchData = false;
          }
        });
      }
    },

    createBatch(row) {
      this.form_create_invoice.batch_id = row.id;
      this.dialogCreate = true;
    },
    onClickInvoiceForm() {
      this.$http
        .createInvoiceBatchData(this.form_create_invoice)
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.form_create_invoice = {};
            this.getDataList();
            this.dialogCreate = false;
          }
        });
    },
    invoiceClose() {
      this.form_create_invoice = {};
    },
    jumpInvoice(row) {
      this.$goPath(`/invoice_batch_list?batch_id=${row.id}`);
    },
    remoteMethodTable(e) {
      this.table_search_value_list = [];
      this.loading_search = true;
      switch (this.table_cascader_value) {
        case "0,1":
          var params = {
            customer_phone: e,
          };
          this.$http.getReportList({ params: params }).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.customer_name,
                  value: item.customer_phone,
                };
              });
            }
          });
          break;
        case "0,2":
          this.$http.searchCompanyCategory(e, 1).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
          });
          break;
        case "1,3":
          var params1 = {
            phone: e,
          };
          this.$http.getUserBroker({ params: params1 }).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.name || item.nickname || item.user_name,
                  value: item.id,
                };
              });
            }
          });
          break;
        case "1,4":
          this.$http.searchCompanyCategory(e, 2).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
          });
          break;
      }
    },
    // 多选添加批次
    handleSelectionChange(e) {
      this.form_create_batch.bill_ids = e.map((item) => {
        return item.id;
      });
    },
    changeTableCascader(e) {
      this.table_cascader_value = e.toString();
      this.table_search_value_list = [];
      this.params = {
        page: 1,
        category: e[0] + "",
        customer_phone: "",
        project_company_id: "",
        broker_user_id: "",
        sale_company_id: "",
        object_category: e[1] + "",
      };
      if (!this.table_cascader_value) {
        this.params.category = "";
        this.params.object_category = "";
        this.getDataList();
      }
      switch (this.table_cascader_value) {
        case "0,1":
          this.placeholderTableVal = "请输入客户联系方式";
          break;
        case "0,2":
          this.placeholderTableVal = "请输入项目公司名称";
          break;
        case "1,3":
          this.placeholderTableVal = "请输入经纪人联系方式";
          break;
        case "1,4":
          this.placeholderTableVal = "请输入销售公司名称";
          break;
      }
    },
    changeSelectTable(e) {
      switch (this.table_cascader_value) {
        case "0,1":
          this.params.customer_phone = e;
          break;
        case "0,2":
          this.params.project_company_id = e;
          break;
        case "1,3":
          this.params.broker_user_id = e;
          break;
        case "1,4":
          this.params.sale_company_id = e;
          break;
      }
      this.getDataList();
    },
    onDrawerClose() {
      this.bill_tableData = [];
      this.form_create_batch = {
        name: "",
        bill_ids: "",
        category: "",
        object_category: "",
      };
      this.search_value = "";
    },
    jumpPayment(row) {
      this.$goPath(`/batch_pay_list?batch_id=${row.id}`);
    },
    goBillList(value) {
      this.$goPath(`/bill_list?batch_sn=${value}`);
    },

    /**
     *   ---------- *** ----------
     * */
    changeSelect(e) {
      this.istableselect = true;
      this.bill_params.category = this.form_create_batch.category;
      this.bill_params.object_category = this.form_create_batch.object_category;
      switch (this.table_category_cascader_value) {
        case "01":
          this.bill_params.customer_phone = e;
          this.form_create_batch.customer_phone = e;
          break;
        case "02":
          this.bill_params.project_company_id = e;
          this.form_create_batch.project_company_id = e;
          break;
        case "13":
          this.bill_params.broker_user_id = e;
          this.form_create_batch.broker_user_id = e;
          break;
        case "14":
          this.bill_params.sale_company_id = e;
          this.form_create_batch.sale_company_id = e;
          break;
      }
      this.getBillList();
    },
    changeCategory(e) {
      this.category_list = [];
      this.bill_params = {
        page: 1,
        per_page: 10,
      };
      this.form_create_batch.object_category = "";
      if (e == 0) {
        this.category_list = [
          { value: 1, label: "客户" },
          { value: 2, label: "项目公司" },
        ];
      }
      if (e == 1) {
        this.category_list = [
          { value: 3, label: "经纪人" },
          { value: 4, label: "销售公司" },
        ];
      }
      this.search_value = "";
    },
    changeCategoryType(e) {
      this.table_category_cascader_value =
        this.form_create_batch.category + e + "";
      this.table_category_type_list = [];
      this.bill_params = {
        page: 1,
        per_page: 10,
      };
      this.search_value = "";
      switch (this.table_category_cascader_value) {
        case "01":
          this.placeholderVal = "请输入客户联系方式";
          break;
        case "02":
          this.placeholderVal = "请输入项目公司名称";
          break;
        case "13":
          this.placeholderVal = "请输入经纪人联系方式";
          break;
        case "14":
          this.placeholderVal = "请输入销售公司名称";
          break;
      }
    },
    getRemoteMethod(e) {
      this.loading_search = true;
      switch (this.table_category_cascader_value) {
        case "01":
          var params = {
            customer_phone: e,
          };
          this.$http.getReportList({ params: params }).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_category_type_list = res.data.data.map((item) => {
                return {
                  label: item.customer_name,
                  value: item.customer_phone,
                };
              });
            }
          });
          break;
        case "02":
          this.$http.searchCompanyCategory(e, 1).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_category_type_list = res.data.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
          });
          break;
        case "13":
          var params1 = {
            phone: e,
          };
          this.$http.getUserBroker({ params: params1 }).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_category_type_list = res.data.data.map((item) => {
                return {
                  label: item.name || item.nickname || item.user_name,
                  value: item.id,
                };
              });
            }
          });
          break;
        case "14":
          this.$http.searchCompanyCategory(e, 2).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_category_type_list = res.data.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
          });
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
div {
  align-items: center;
  justify-content: center;
}
.el-button {
  margin: 10px;
}
</style>
