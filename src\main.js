// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import App from "./App";
import router from "./router";
import store from "./store";
import ElementUI from "element-ui";
import directives from "./utils/directives";
import './registerServiceWorker'
import {
  sortPro,
  GetQueryString,
  sortArr,
  getDictionary,
  bytesToSize,
  hasShow,
  toTree,
  // getChildrenRouter,
  setDictionary,
  computedValueType,
  goPath,
  exportExcelTable,
  onCopyValue,
  queryUrlParams,
  imageDomain
} from "./utils/tools";
// 引入工具类
import "element-ui/lib/theme-chalk/index.css";

import http from "@/utils/http";
import * as ajax from "@/utils/ajax";
import "../public/static/ueditor/ueditor.config.js";
import "../public/static/ueditor/ueditor.all.min.js";
import "../public/static/ueditor/lang/zh-cn/zh-cn.js";
import "../public/static/ueditor/ueditor.parse.min.js";
// 引入图标
import "./assets/icon/iconfont.css";
import { Message } from "element-ui";
import myTopTips from "@/components/components/top-tips.vue";
import Tplus from "@/utils/tplus.js";
Vue.component("myTopTips", myTopTips);
// 请求数据
Vue.prototype.$http = http;
Vue.prototype.$ajax = ajax;
Vue.prototype.$sortPro = sortPro;
Vue.prototype.$GetQueryString = GetQueryString;
Vue.prototype.$sortArr = sortArr;
Vue.prototype.$getDictionary = getDictionary;
Vue.prototype.$bytesToSize = bytesToSize;
Vue.prototype.$hasShow = hasShow;
Vue.prototype.$toTree = toTree;
Vue.prototype.$setDictionary = setDictionary;
Vue.prototype.$computedValueType = computedValueType;
Vue.prototype.$goPath = goPath;
Vue.prototype.$exportExcelTable = exportExcelTable;
Vue.prototype.$onCopyValue = onCopyValue;
Vue.prototype.$queryUrlParams = queryUrlParams;
Vue.prototype.$imageDomain = imageDomain;
Vue.use(ElementUI);
Vue.use(directives);
const isHttp = function (val) {
  let httpArr = ["http", "https"];
  return httpArr.includes(val.split("://")[0]);
};
const imageFilter = function (url, param = "w_8601") {
  if (!url || typeof url !== "string") {
    return;
  }
  if (isHttp(url)) {
    let reg = new RegExp(/\?.+=/);
    if (reg.test(url)) {
      // 链接中有参数直接返回不需要加参数
      return url;
    }
    let isGif = new RegExp(/\.gif/)
    if (isGif.test(url)) {
      // 链接中有。gif返回不需要加参数
      return url;
    }
    let thumbParam = "?x-oss-process=style/";
    if (param) {
      return url + thumbParam + param;
    }
    return url;
  }
};
Vue.prototype.$imageFilter = imageFilter;
// 判断当前应用环境
const envjudge = function () {
  var isMobile = window.navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
  ); // 是否手机端
  var isWx = /micromessenger/i.test(navigator.userAgent); // 是否微信
  var isComWx = /wxwork/i.test(navigator.userAgent); // 是否企业微信

  if (isComWx && isMobile) {
    //手机端企业微信
    return "com-wx-mobile";
  } else if (isComWx && !isMobile) {
    //PC端企业微信
    return "com-wx-pc";
  } else if (isWx && isMobile) {
    // 手机端微信
    return "wx-mobile";
  } else if (isWx && !isMobile) {
    // PC端微信
    return "wx-pc";
  } else {
    return "other";
  }
};
Vue.prototype.$envjudge = envjudge;
// 企微助手列表表格背景颜色
// eslint-disable-next-line
const TableRowStyle = function ({ row, rowIndex }) {
  let rowBackground = {};
  if ((rowIndex + 1) % 2 === 0) {
    rowBackground.background = "#EFEFEF";
    return rowBackground;
  }
};
Vue.prototype.$TableRowStyle = TableRowStyle;
const getCurrentTime = function () {
  //获取当前时间并打印
  let yy = new Date().getFullYear();
  let mm = new Date().getMonth() + 1;
  let dd = new Date().getDate();
  let hh = new Date().getHours();
  let mf =
    new Date().getMinutes() < 10
      ? "0" + new Date().getMinutes()
      : new Date().getMinutes();
  let ss =
    new Date().getSeconds() < 10
      ? "0" + new Date().getSeconds()
      : new Date().getSeconds();
  return yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
};
Vue.prototype.$getCurrentTime = getCurrentTime;
// =========

// 监视是否登录，没有登录返回到登录界面
document.title = "T+系统";
router.beforeEach((to, from, next) => {
  // console.log(location.href);
  console.log(to, from);
  // if (store.state.disableClick) {
  //   Message.error(store.state.disableClick)
  // }
  let h = decodeURIComponent(location.href)
  let href = location.href
  if (h.indexOf('/@@@@/') > -1) {
    h = h.replace('/@@@@/', "/#/")
    h = h.replace('@@@@@@from@@@@@@', "&from=")
    h = h.replace('@@@@@@id@@@@@@', "&id=")
    h = h.replace('@@@@@@website_id@@@@@@', "&website_id=")
    if (h.endsWith('#/')) {
      h = h.substring(0, h.length - 2)
    }
    location.replace(h)
    return
  }
  let params = to.query || {}
  if (params.code && to.path == '/douyin_authorize' && to.query && to.query.id && from.fullPath === '/') {
    // to.query.id = ''
    // let params = queryUrlParams(href.split("#")[0]);
    // let params1 = queryUrlParams(href.split("#")[1]);
    if (params.code && params.id) {

      http.bindDouyinMember(params.id, params.code).then(res => {
        if (res.status == 200) {
          Message.success('绑定成功')

        }
        setTimeout(() => {
          location.replace("https://yun.tfcs.cn/admin/#/douyin_authorize?website_id=" + to.query.website_id)
          location.reload()

        }, 800);
      }).catch(() => {
        setTimeout(() => {
          console.log(123);
          location.replace("https://yun.tfcs.cn/admin/#/douyin_authorize?website_id=" + to.query.website_id)
          location.reload()
        }, 500);
      })
    } else {
      next()
    }

    return
  }

  if (to.query && params.authorization_code && to.query.from == 'tDouyinShouquan') {

    http.authorizationTDouyin(params.authorization_code).then(res => {
      if (res.status == 200) {
        Message.success('绑定成功')

      }
      setTimeout(() => {
        location.replace("https://yun.tfcs.cn/admin/#/crm_customer_business_setting?website_id=" + to.query.website_id)
        location.reload()

      }, 500);
    }).catch(() => {
      setTimeout(() => {
        location.replace("https://yun.tfcs.cn/admin/#/crm_customer_business_setting?website_id=" + to.query.website_id)
        location.reload()

      }, 500);
    })

    return
  }
  if (to.query && params.authorization_code && to.query.from == 'xinfangDouyinShouquan') {

    http.authorizationXinfangDouyin(params.authorization_code).then(res => {
      if (res.status == 200) {
        Message.success('绑定成功')

      }
      setTimeout(() => {
        location.replace("https://yun.tfcs.cn/admin/#/crm_customer_business_setting?website_id=" + to.query.website_id)
        location.reload()
      }, 500);
    }).catch(() => {
      setTimeout(() => {
        location.replace("https://yun.tfcs.cn/admin/#/crm_customer_business_setting?website_id=" + to.query.website_id)
        location.reload()
      }, 500);
    })

    return
  }
  // if (to.query && params.code && to.query.from == 'crm_customer_personnel') {

  //   http.authorizationXinfangDouyin(params.authorization_code).then(res => {
  //     if (res.status == 200) {
  //       Message.success('绑定成功')

  //     }
  //     setTimeout(() => {
  //       location.replace("https://yun.tfcs.cn/admin/#/TDouyinSetting?website_id=" + to.query.website_id)
  //     }, 500);
  //   }).catch(() => {
  //     setTimeout(() => {
  //       location.replace("https://yun.tfcs.cn/admin/#/TDouyinSetting?website_id=" + to.query.website_id)
  //     }, 500);
  //   })
  //   return
  // }


  // }
  // 如果是企业微信授权地址
  if (to.query && to.query.website_id) {
    if (to.query.website_id != localStorage.getItem("website_id")) {
      localStorage.removeItem("TOKEN")
    }
    localStorage.setItem("website_id", to.query.website_id);
  }
  if (to.query && !to.query.website_id) {
    to.query.website_id = localStorage.getItem("website_id");
  }
  if (to.params && !to.params.website_id) {
    to.params.website_id = localStorage.getItem("website_id");
  }
  let token = window.localStorage.TOKEN || window.localStorage.company_token;
  console.log(token,'token');
  if (to.path === "/companyLogin") {
    next();
  } else if (to.path === "/middle_page") {
    next();
  } else if (to.path === "/wx_work_auth_three") {
    next();
  } else if (to.path === "/wx_work_auth") {
    next();
  } else if (to.path === "/wx_work_auth_report") {
    next();
  } else if (to.path === "/wx_work_auth_t") {
    next();
  } else if (to.path === "/jump") {
    // to.meta.title && (document.title = to.meta.title);
    next();
  } else if (to.path === "/login1") {
    // to.meta.title && (document.title = to.meta.title);
    next();
  } else if (to.path === "/map_plugin/map_index") {
    to.meta.title && (document.title = to.meta.title);
    next();
  }else if (!token && to.path !== "/login") {
    let backUrl = '';
    if(to.path == '/train/video'){
      //手机微信/企微端不验证登录
      let env = envjudge(), t = to.query.t;
      if(t && (env == "com-wx-mobile" || env == "wx-mobile")){
        next();
        return;
      }
      backUrl = to.path;
    }
    if(backUrl){
      let searchParams = new URLSearchParams(), querys = to.query || {};
      for(const key in querys){
        if(key != 'website_id'){
          searchParams.set(key, querys[key]);
        }
      }
      localStorage.setItem('backUrl',to.path+'?'+searchParams.toString());
    }

    router.push({
      path: "/login",
      // query: {
      //   website_id: localStorage.getItem("website_id"),
      // },
    });
  } else {
    next();
    // to.meta.title && (document.title = to.meta.title);
  }
});
router.onError((error) => {
  console.log(error);
  const pattern = /Loading chunk (\w)+ failed/g;
  const isError = error.message.match(pattern);
  const targetPath = router.history.pending.fullPath;
  if (isError) {
    router.replace(targetPath);
  }
});
/* eslint-disable no-new */
const app = new Vue({
  el: "#app",
  router,
  store,
  components: { App },
  template: "<App/>",
});

Vue.use(Tplus);
window.eventBus = app;
