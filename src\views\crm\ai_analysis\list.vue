<template>
<div class="tab-content-container">
	<div class="tab-content-body white topped">
		<div class="body-inner main-scroll">
			<aiAnalysisContent
				:params="params"
				:list="list"
				:loading="loading"
				:count="count"
				@search="handleSearch"
				@reset="resetSearch"
				@pageChange="onPageChange"
				@sizeChange="handleSizeChange"
				@goCustomerDetail="goCustomerDetail"
				@refreshData="getList"
			/>
		</div>
	</div>
</div>
</template>

<script>
import aiAnalysisContent from "./components/aiAnalysisContent";

export default {
	name: 'AiAnalysisList',
	components: {
		aiAnalysisContent
	},
	data() {
		return {
			loading: false,
			params: {
				page: 1,
				per_page: 10,
				deal_level: '',      // 成交意向等级
				loss_level: '',      // 流失风险等级  
				demand_level: '',    // 客户需求等级
				is_cover: '',        // 是否覆盖
				is_assign: '',       // 是否分配
				is_follow: '',       // 留资状态
				is_health: '',       // 健康状态
				admin_id: '',       // 成员ID
				department_id: '',  // 部门ID
				start_date: '',     // 开始日期
				end_date: '',       // 结束日期
				keywords: '',       // 关键词
				intention_community: '' // 兴趣偏好
			},
			count: 0,
			list: []
		}
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表数据
		async getList() {
			this.loading = true;
			try {
				const res = await this.$http.getAiAnalysisList(this.params);
				if (res.status === 200) {
					this.count = res.data?.total || 0;
					this.list = res.data?.data || [];
				} else {
					this.$message.error(res.data?.message || '获取数据失败');
					this.list = [];
					this.count = 0;
				}
			} catch (error) {
				console.error('获取AI分析列表失败:', error);
				this.$message.error('网络请求失败，请检查网络连接后重试');
				this.list = [];
				this.count = 0;
			} finally {
				this.loading = false;
			}
		},
		
		// 处理筛选条件变更
		handleSearch(newParams) {
			Object.assign(this.params, newParams);
			this.params.page = 1;
			this.getList();
		},
		
		// 重置搜索
		resetSearch() {
			this.params = {
				page: 1,
				per_page: this.params.per_page,
				deal_level: '',
				loss_level: '',
				demand_level: '',
				is_cover: '',
				is_assign: '',
				is_follow: '',
				is_health: '',
				admin_id: '',
				department_id: '',
				start_date: '',
				end_date: '',
				keywords: '',
				intention_community: ''
			};
			this.getList();
		},
		
		// 页码变更
		onPageChange(page) {
			this.params.page = page;
			this.getList();
		},
		
		// 每页数量变更
		handleSizeChange(size) {
			this.params.per_page = size;
			this.params.page = 1;
			this.getList();
		},
		
		// 跳转客户详情
		goCustomerDetail(row) {
			if (row.client_id) {
				// 跳转方法到客户详情
				this.$goPath(`/crm_customer_detail?id=${row.client_id}&type=seas`);
			}
		}
	}
}
</script>

<style scoped>
.tab-content-body{
	padding: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	padding-bottom: 48px;
}

.body-inner{
	flex: 1;
	overflow: auto;
	padding: 0 28px 28px;
}
</style>
<style>
	.tab-content-footer{
		position: absolute;
		z-index: 99;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding-left: 28px;
		padding-right: 28px;
		white-space: nowrap;
		overflow: hidden;
	}
</style>
