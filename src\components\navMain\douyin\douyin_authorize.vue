<template>
  <!-- 客户群 -->
  <div class="pages" id="page">
    <el-row :gutter="20">
      <el-col :span="24">
        <div
          class="content-box-crm flex-row align-center"
          style="padding: 20px 12px 0"
        >
          <div class="select_box flex-row align-center mr10">
            <span class="text">项目名称：</span>
            <el-select
              v-model="params.project_id"
              placeholder="请输入项目名称"
              clearable
              size="mini"
              @change="search"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <!-- <div class="select_box flex-row align-center mr10">
            <span class="">手机号码：</span>
            <el-input
              size="mini"
              class="flex-1"
              v-model="params.phone"
            ></el-input>
          </div>
          <div class="select_box flex-row align-center mr10">
            <span class="">姓名：</span>
            <el-input
              size="mini"
              class="flex-1"
              v-model="params.name"
            ></el-input>
          </div> -->
          <div class="select_box flex-row align-center mr10">
            <span class="">授权状态：</span>
            <el-select
              class="flex-1"
              size="mini"
              v-model="params.auth_type"
              clearable
              placeholder="请选择"
              @change="search"
            >
              <el-option
                v-for="item in authList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <el-popover
            placement="bottom"
            width="500px"
            v-model="show_tel_search"
          >
            <div>
              <div>搜索</div>
              <div class="inps div row align-center" style="margin: 10px 0">
                <el-input
                  placeholder="请输入手机号"
                  v-model="params.phone"
                  style="margin-right: 10px; width: 180px"
                ></el-input>
              </div>
              <div class="btns" style="text-align: right">
                <el-button @click="params.phone = ''">重置</el-button>
                <el-button type="primary" @click="search">确定</el-button>
              </div>
            </div>
            <div class="search_loudong div row align-center" slot="reference">
              <div class="seach_value">电话</div>
              <div class="sanjiao" :class="{ transt: show_tel_search }"></div>
            </div>
          </el-popover>
          <el-input
            size="small"
            placeholder="请输入姓名"
            style="width: 256px; margin-left: 12px"
            v-model="params.name"
            @change="search"
          >
            <!-- @change="onChangeKeywords" -->
            <span slot="append" class="el-icon-search"></span>
          </el-input>
          <!-- <div class="select_box flex-row align-center mr10">
            <el-button typw="primary" size="mini" @click="search">
              搜索</el-button
            >
          </div> -->
          <div class="select_box flex-row align-center mla">
            <el-button size="mini" @click="toProject"> 项目列表 </el-button>
            <el-button size="mini" @click="showAdd"> 添加抖音用户 </el-button>
            <!-- <el-button size="mini">导出 </el-button> -->
          </div>
          <!-- <div class="bottom-border div row" style="padding: 24px">
            <span class="text">时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="p_time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="changeTimeRange"
            >
            </el-date-picker>
          </div> -->
        </div>
        <div class="content-box-crm">
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
          >
            <!--  @selection-change="handleSelectionChange" -->
            <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
            <el-table-column prop="name" label="名称"></el-table-column>
            <el-table-column label="抖音" v-slot="{ row }">
              <div class="d_info flex-row items-center">
                <div
                  class="row_img"
                  v-if="row.avatar"
                  @click="previewImg(row.avatar)"
                >
                  <img :src="row.avatar" alt="" />
                </div>
                <div class="row_img" v-else>
                  <div class="d_name_bg">{{ row.name && row.name[0] }}</div>
                </div>
                <div class="d_name">
                  {{ row.nickname }}
                  <!-- <img :src="row.nickename" alt="" /> -->
                </div>
              </div>
            </el-table-column>
            <el-table-column label="手机号" prop="phone"> </el-table-column>
            <el-table-column label="项目名称" prop="project_name">
            </el-table-column>
            <!-- <el-table-column label="加入时间" prop="created_at">
            </el-table-column>
            <el-table-column label="授权到期时间" v-slot="{ row }">
              {{ row.auth_time || "--" }}
            </el-table-column> -->
            <el-table-column label="授权状态" v-slot="{ row }">
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  <div>加入时间：{{ row.created_at }}</div>
                  <div style="margin-top: 5px" v-if="row.auth_type == 1">
                    授权到期时间:{{ row.auth_time }}
                  </div>
                </div>
                <el-tag v-if="row.auth_type == 1" type="success"
                  >授权有效</el-tag
                >
                <el-tag v-if="row.auth_type == 2" type="warning"
                  >授权无效</el-tag
                >
                <el-tag v-if="row.auth_type == 3" type="error">未授权</el-tag>
              </el-tooltip>
            </el-table-column>

            <!-- <el-table-column
              label="创建时间"
              prop="created_at"
            ></el-table-column> -->
            <el-table-column label="操作" v-slot="{ row }">
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="onChangeEdit(row)"
                >编辑</el-link
              >
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="authorize(row, 1)"
                >抖音授权</el-link
              >
              <!-- <el-link
                type="primary"
                style="margin-right: 10px"
                @click="authorize(row, 2)"
                >授权链接</el-link
              > -->
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="copyAuthorizeLink(row)"
                >复制授权链接</el-link
              >
              <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteMember(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <!-- <el-dialog :visible.sync="show_select_dia" width="660px" :title="title">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog> -->
    <el-dialog :visible.sync="show_add" width="660px" :title="title">
      <addMember
        v-if="show_add"
        :projectList="projectList"
        :form="detail"
        @success="addOk"
        @cancel="show_add = false"
      ></addMember>
    </el-dialog>
    <el-dialog
      :visible.sync="show_dialog_list"
      width="300px"
      title="绑定到抖音"
    >
      <iframe
        style="border: none"
        width="300px"
        height="300px"
        v-bind:src="inframe"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
// import myLabel from "@/components/navMain/crm/components/my_label";
import addMember from "./components/addMember.vue";
export default {
  name: "crm_customer_member_qrcode",
  components: {
    // myLabel,
    // memberListSingle,
    addMember
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        auth_type: "",
        phone: "",
        name: "",
        project_id: ""

      },
      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
      projectList: [],
      authList: [
        {
          value: 1,
          label: '授权有效'
        },
        {
          value: 2,
          label: '授权无效'
        },
        {
          value: 3,
          label: '未授权'
        },
      ],
      show_add: false,
      show_edit: false,
      title: "",
      show_dialog_list: false,
      inframe: "",
      show_tel_search: false
    };
  },
  created() {
    this.getProjectData();
    this.getList()
    // let a = location.href
    // if (a.indexOf("?code=") >= 0) {
    //   let params = this.$queryUrlParams(a.split("#")[0]);
    //   let params1 = this.$queryUrlParams(a.split("#")[1]);
    //   if (params.code && params1.id) {
    //     this.bindDouyin(params1.id, params.code)
    //   }
    // }
  },
  mounted() {
  },
  methods: {
    filterStatus(e) {
      return this.employees_list[e].name;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    previewImg(src) {
      console.log(123);
      this.imgSrc = src;
      this.show_preview = true;
    },
    getProjectData() {
      this.$http.getDouyinProjectList().then(res => {
        console.log(res);
        if (res.status == 200) {
          this.projectList = res.data
        }
      })
    },
    search() {
      if (this.show_tel_search) {
        this.show_tel_search = false
      }
      this.params.page = 1
      this.getList()
    },
    onProject() {

    },
    copyAuthorizeLink(row) {
      let website_id = this.$route.query.website_id
      // if (a.indexOf("?code=") >= 0) {
      //   let params1 = this.$queryUrlParams(a.split("#")[1]);
      //   website_id = params1.website_id
      // } else {
      //   website_id = this.$queryUrlParams(a) ? this.$queryUrlParams(a).website_id : ''
      // }

      let url = location.origin + location.pathname + "#/jump?id=" + row.id + "&from=douyin&website_id=" + website_id;
      this.$onCopyValue(url);
    },
    authorize(row, type) {
      let website_id = this.$route.query.website_id
      let href = location.origin + location.pathname + '@@@@/douyin_authorize?website_id=' + website_id
      // douyin_authorize

      href += '@@@@@@id@@@@@@' + row.id
      this.$http.getDouyinShouquan(href).then(res => {
        if (res.status == 200) {
          if (type == 2) {
            this.$onCopyValue(res.data);
            return
          }
          const url = res.data
          // const params = this.$queryUrlParams(res.data);
          // params.redirect_uri = encodeURIComponent(href)
          // let url = 'https://open.douyin.com/qrconnect?'
          // for (const key in params) {
          //   url += `${key}=${params[key]}&`
          // }
          // url = url.substring(0, url.length - 1)
          // console.log(url);
          this.show_dialog_list = true
          this.inframe = url
        }
      })
    },
    showAdd() {
      this.detail = {
        project_id: "",
        name: "",
        phone: "",
        admin_id: 0
      }
      this.title = '添加抖音授权'
      this.show_add = true
    },
    addOk() {

      this.params.page = 1
      this.getList()
      this.show_add = false
    },
    // bindDouyin(id, code) {
    //   this.$http.bindDouyinMember(id, code).then(res => {
    //     if (res.status == 200) {
    //       this.$message.success('绑定成功')
    //       sessionStorage.setItem("isRefesh", "1")
    //       this.getList()

    //     }
    //   }).catch(() => {
    //     this.is_table_loading = false
    //   })
    // },
    getList() {
      this.is_table_loading = true
      this.$http.getAuthorizeList(this.params).then(res => {
        console.log(res);
        this.is_table_loading = false
        if (res.status == 200) {
          this.tableData = res.data.data
          this.params.total = res.data.total;
        }
      }).catch(() => {
        this.is_table_loading = false
      })
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    deleteMember(row) {
      this.$http
        .delDouyinMember(row.id)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("删除成功");
            this.params.page = 1;
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        })
        .catch(() => { });
    },
    EditOk() {
      this.params.page = 1;
      this.getList();
      this.show_add = false;
    },
    toProject() {
      this.$goPath('/douyin_project')
    },

    async onChangeEdit(row) {
      this.detail = row
      this.title = "编辑抖音用户"
      this.show_add = true;
      // let res = await this.$http.crmMemberQrcodeDetail(row.id).catch(() => {
      //   this.$message.error("获取详情失败");
      // });
      // if (res.status == 200) {
      //   this.detail = res.data;
      //   this.title = "编辑抖音用户"
      //   this.show_edit_dia = true;
      // }
    },
  },
};
</script>

<style scoped lang="scss">
.items-center {
  align-items: center;
}
.mla {
  margin-left: auto;
}
.mr10 {
  margin-right: 10px;
}
.mb20 {
  margin-bottom: 20px;
}
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .padd0 {
    padding: 0 24px;
    margin: 0 -20px;
    .title {
      padding: 15px 40px;
    }
  }
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
// ::v-deep .el-link {
//   ~ .el-link {
//     margin-right: 10px;
//   }
// }
.row_img {
  width: 40px;
  height: 40px;
  text-align: center;
  margin-right: 5px;
  border-radius: 50%;
  cursor: pointer;
  overflow: hidden;
  .d_name_bg {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    color: #fff;
    background: #409eff;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.datas_list {
  .datas_item {
    margin-right: 24px;
    background: #fff;
    border-radius: 10px;
    padding: 24px;
    &.point {
      cursor: pointer;
    }
    &:last-child {
      margin-right: 0;
    }
    .datas_item_left {
      width: 35px;
      height: 35px;
      margin-right: 25px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .datas_item_right {
      .datas_item_right_name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #2e3c4e;
        .img {
          width: 16px;
          height: 16px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
      .datas_item_right_top {
        color: #464d59;
        font-size: 18px;
        font-family: PingFangSC-Medium, sans-serif;
        font-weight: 600;
        .small {
          font-size: 16px;
          font-weight: normal;
        }
      }
      .datas_item_right_bottom {
        color: #768196;
        margin-top: 5px;
        font-size: 13px;
      }
    }
  }
}
.item_title {
  color: #000000;
  font-family: PingFang-Medium, sans-serif;
  font-weight: 600;
  font-size: 22px;
}

.datas .item_title {
  margin-top: 25px;
}
.search_loudong {
  background: #409eff;
  padding: 5px 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;
  .seach_value {
    font-size: 14px;
    color: #fff;
  }
  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;
    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      // transform: rotate(180deg);
    }
  }
}
</style>
