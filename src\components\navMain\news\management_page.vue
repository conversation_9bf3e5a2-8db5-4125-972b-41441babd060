<template>
  <el-container>
    <el-header class="div row">
      <div class="div row">
        <el-button
          v-if="$hasShow('添加资讯')"
          type="primary"
          icon="el-icon-plus"
          @click="toAdd"
          >添加资讯</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="$router.push('/class_list')"
          >资讯分类管理</el-button
        >
      </div>

      <div class="div row ">
        <el-input
          @input="onInput"
          v-model="params.title"
          @change="onChange"
          placeholder="请输入标题"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :currentPage="params.currentPage"
          :pagesize="params.pagesize"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "management_page",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
        title: "",
      },
      table_header: [
        { prop: "id", label: "ID", width: 80 },
        { prop: "sort", label: "排序" },
        {
          label: "作者",
          render: (h, data) => {
            return (
              <p>
                {data.row.author ? (
                  <p>{data.row.author}</p>
                ) : (
                  <p style="color:#999">未填写作者</p>
                )}
              </p>
            );
          },
        },
        { prop: "created_at", label: "添加时间" },
        { prop: "title", label: "标题" },
        {
          label: "资讯图",
          render: (h, data) => {
            return (
              <el-popover width="500px" trigger="hover" placement="right">
                <el-image
                  src={data.row.img}
                  style="width:300px;height:300px"
                  fit="contain"
                ></el-image>
                <img
                  slot="reference"
                  style="max-height:50px;max-width:100px"
                  src={this.$imageFilter(
                    data.row.img || "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                />
              </el-popover>
            );
          },
        },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改资讯") ? (
                  <el-button
                    icon="el-icon-edit"
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.changeData(data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除资讯") ? (
                  <el-button
                    icon="el-icon-delete"
                    type="danger"
                    size="mini"
                    onClick={() => {
                      this.deleteData(data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    changeData(row) {
      this.$goPath(`/updataInfo?id=${row.id}`);
    },
    deleteData(row) {
      this.$confirm("此操作将删除该资讯, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteInfo(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 全选
    toAdd() {
      this.$goPath("add_news");
    },

    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    formatType(row) {
      let news_category_name = row.news_category_id;
      if (news_category_name === 1) {
        return (news_category_name = "楼盘动态");
      }
      // if(){}
    },
    onInput(e) {
      if (!e) {
        this.params.current_page = 1;
        this.params.title = "";
        this.getDataList();
      }
    },
    onChange() {
      this.search();
    },
    search() {
      this.params.currentPage = 1;
      this.getDataList();
    },
    // 获取列表数据
    getDataList() {
      this.$http
        .searchInfo(
          this.params.currentPage,
          this.params.pagesize,
          this.params.title
        )
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
  display: flex;
  width: calc(100% - 280px);
  justify-content: space-between;
  align-items: center;
}
.el-header {
  align-items: center;
  justify-content: space-between;
}
.el-pagination {
  width: 100%;
  margin-top: 20px;
  text-align: center;
}
</style>
