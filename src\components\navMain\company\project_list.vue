<template>
  <div class="list">
    <el-container>
      <el-container v-loading="is_table_loading">
        <el-header class="div row">
          <div class="div row">
            <div class="title">项目列表</div>
            <div class="title_number">
              <div>
                当前页面共(
                <i>{{ tableData.length }}</i>
                )条数据
              </div>
            </div>
            <div class="add-build" v-if="$hasShow('添加项目')">
              <el-button
                type="primary"
                @click="addData(website_info.website_mode_category)"
                icon="el-icon-plus"
                >添加项目</el-button
              >
            </div>
          </div>
        </el-header>
        <div style="margin-bottom: 10px">
          <el-cascader
            clearable
            :props="props"
            @change="handleChange"
            v-model="region_value"
            :options="region_list"
            style="width: 200px"
          ></el-cascader>
          <el-input
            @input="onInput"
            @change="onChange"
            v-model="input"
            placeholder="搜索相关的项目"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
        <myTable :table-list="tableData" :header="table_header"></myTable>
        <!-- 绑定公司 -->
        <el-footer>
          <!-- 分页 -->
          <div class="pagination-box">
            <myPagination
              :total="params.total"
              :pagesize="params.pagesize"
              :currentPage="params.currentPage"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            ></myPagination>
          </div>
        </el-footer>
      </el-container>
      <el-dialog
        :title="'项目助理（ ' + user_manager_list.length + ' ）'"
        :visible.sync="dialogView"
      >
        <myTable
          :table-list="user_manager_list"
          :header="user_manager_list_table_header"
        ></myTable>
      </el-dialog>
      <el-dialog title="置业顾问" :visible.sync="dialogCase">
        <myTable
          :table-list="user_is_case_manager_list"
          :header="user_is_case_manager_list_table_header"
        ></myTable>
        <div style="padding: 20px 0">
          <myPagination
            :total="cparams.total"
            :pagesize="cparams.per_page"
            :currentPage="cparams.page"
            @handleSizeChange="handleSizeChangec"
            @handleCurrentChange="handleCurrentChangec"
          ></myPagination>
        </div>
      </el-dialog>
    </el-container>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import { mapState } from "vuex";
export default {
  name: "project_list",
  components: { myPagination, myTable },
  data() {
    return {
      // 搜索框数据
      input: "",
      tableData: [],
      // 存放列表图片
      imgbox: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
        region_0: 0,
        region_1: 0,
      },
      formCompany: {
        company_id: "",
        project_id: "",
      },
      dialogCompany: false,
      dialogView: false,
      user_manager_list: [],
      table_header: [
        { prop: "id", label: "项目ID", width: "80" },
        {
          prop: "build_name",
          width: "300px",
          label: "楼盘名称",
          render: (h, data) => {
            return (
              <div class="build-name">
                <img
                  style="height:50px;width:65px"
                  src={this.$imageFilter(
                    data.row.build.img ||
                    "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                />
                <div class="right" style="margin-left:10px">
                  <div class="title">
                    {data.row.build_name}
                    <el-tag size="mini" type="primary">
                      {data.row.name}
                    </el-tag>
                  </div>
                  <div
                    class="bottom"
                    onClick={() => {
                      this.viewManager(data.row);
                    }}
                  >
                    {data.row.company_name} / 项目助理{" "}
                    <span style="text-decoration:underline">
                      {data.row.manager |
                        this.$options.filters.formatCategory(data.row.manager)}
                    </span>{" "}
                    位
                  </div>
                </div>
              </div>
            );
          },
        },
        {
          label: "报备",
          render: (h, data) => {
            return (
              <div>
                <el-tag size="mini" type="success">
                  {data.row.full_num_reported == 1 ? "全号报备" : "隐号报备"}
                </el-tag>
                <p>
                  {data.row.full_num_reported == 0 &&
                    data.row.hide_num_mode == 1
                    ? "前三后四"
                    : data.row.full_num_reported == 0 &&
                      data.row.hide_num_mode == 2
                      ? "前三后五"
                      : ""}

                  {data.row.reported_id_no_category === 1
                    ? ""
                    : "身份证后六位显示"}
                </p>
              </div>
            );
          },
        },
        {
          label: "是否显示",
          width: "100",
          render: (h, data) => {
            return (
              <el-tag type={data.row.display === 1 ? "success" : "danger"}>
                {data.row.display === 1
                  ? "显示"
                  : data.row.display === 0
                    ? "不显示"
                    : ""}
              </el-tag>
            );
          },
        },
        {
          label: "列表排序",
          prop: "sort",
          render: (h, data) => {
            return (
              <div>
                <el-input
                  style="width:80px"
                  v-model={data.row.sort}
                  onChange={() => {
                    this.onChangeSort(data.row);
                  }}
                ></el-input>
              </div>
            );
          },
        },
        {
          label: "重复报备保护期",
          render: (h, data) => {
            return (
              <el-tag>
                {data.row.repeat_reported_protected_day +
                  "天" +
                  data.row.reported_protected_hour +
                  "小时"}
              </el-tag>
            );
          },
        },
        {
          label: "到访客户保护天数",
          render: (h, data) => {
            return (
              <el-tag>
                {data.row.visit_protected_day +
                  "天" +
                  data.row.visit_protected_hour +
                  "小时"}
              </el-tag>
            );
          },
        },
        {
          label: "报备保护天数",
          render: (h, data) => {
            return (
              <el-tag>
                {data.row.reported_protected_day +
                  "天" +
                  data.row.reported_protected_hour +
                  "小时"}
              </el-tag>
            );
          },
        },
        { prop: "created_at", label: "创建日期" },
        {
          label: "操作",
          width: "250",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改项目") ? (
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="success"
                    style="margin:5px;width:80px;border-radius:5px"
                    onClick={() => {
                      this.updataCompany(0, data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("规则管理") ? (
                  <el-button
                    size="mini"
                    style="margin:5px;width:80px;border-radius:5px"
                    type="primary"
                    onClick={() => {
                      this.setupRules(0, data.row);
                    }}
                  >
                    规则管理
                  </el-button>
                ) : (
                  ""
                )}

                {this.$hasShow("项目推广") ? (
                  <el-button
                    style="margin:5px;width:80px;border-radius:5px"
                    icon="el-icon-s-promotion"
                    size="mini"
                    type="success"
                    onClick={() => {
                      this.shareClient(0, data.row);
                    }}
                  >
                    推广
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("项目绑定助理") ? (
                  <el-button
                    size="mini"
                    style="margin:5px;width:80px;border-radius:5px"
                    type="primary"
                    onClick={() => {
                      this.bindingManager(data.row);
                    }}
                  >
                    绑定助理
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("项目公司规则") ? (
                  <el-button
                    size="mini"
                    style="margin:5px;width:80px;border-radius:5px"
                    type="success"
                    onClick={() => {
                      this.brokerRules(data.row);
                    }}
                  >
                    公司规则
                  </el-button>
                ) : (
                  ""
                )}
                {
                  <el-button
                    size="mini"
                    type="primary"
                    style="margin:5px;width:80px;border-radius:5px"
                    onClick={() => {
                      this.viewManager(data.row);
                    }}
                  >
                    查看助理
                  </el-button>
                }
                {this.website_info.website_mode_category === 1 ? (
                  <el-button
                    size="mini"
                    style="margin:5px;width:80px;border-radius:5px"
                    type="success"
                    onClick={() => {
                      this.bindingIscaseManager(data.row);
                    }}
                  >
                    置业顾问
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除项目") ? (
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    style="margin:5px;width:80px;border-radius:5px"
                    onClick={() => {
                      this.handleDelete(0, data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      user_manager_list_table_header: [
        { prop: "user_id", label: "ID" },
        {
          label: "姓名",
          render: (h, data) => {
            return (
              <p>
                {data.row.user.name ||
                  data.row.user.nickname ||
                  data.row.user.user_name}
              </p>
            );
          },
        },
        {
          label: "头像",
          render: (h, data) => {
            return <img src={data.row.user.avatar} width="50" />;
          },
        },
        {
          prop: "user.phone",
          label: "联系方式",
        },
        {
          prop: "user.brokerage_total",
          label: "报备客户数",
        },
      ],
      user_is_case_manager_list: [],
      user_is_case_manager_list_table_header: [
        { prop: "id", label: "ID" },
        {
          label: "姓名",
          render: (h, data) => {
            return <p>{data.row.name || data.row.nickname}</p>;
          },
        },
        {
          label: "头像",
          render: (h, data) => {
            return <img src={data.row.avatar} width="50px"></img>;
          },
        },
        { prop: "phone", label: "联系方式" },
      ],
      dialogCase: false,
      cparams: {
        page: 1,
        is_case: 1,
      },
      is_table_loading: true,
      region_list: [],
      props: {
        label: "name",
        value: "id",
      },
      region_value: [],
    };
  },
  mounted() {
    this.getRegion();
    this.getDataList();
  },
  filters: {
    formatCategory(e) {
      let arr = e.filter((item) => {
        return item.user !== null && item.user.category === 3;
      });
      return arr.length;
    },
  },
  methods: {
    handleChange(e) {
      this.params.region_0 = e[0] || 0;
      this.params.region_1 = e[1] || 0;
      this.getDataList();
    },
    getRegion() {
      this.$http.getRegionList().then((res) => {
        if (res.status === 200) {
          this.region_list = this.$toTree(res.data);
        }
      });
    },
    // 获取列表数据
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getProjectList({
          params: {
            page: this.params.currentPage,
            row: this.params.pagesize,
            name: this.input,
            region_0: this.params.region_0,
            region_1: this.params.region_1,
          },
        })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    onInput(e) {
      if (!e) {
        this.params.currentPage = 1;
        this.getDataList();
      }
    },
    // 搜索项目
    search() {
      this.params.currentPage = 1;
      this.getDataList();
    },
    onChange() {
      this.search();
    },
    onChangeSort(row) {
      let form = {
        id: row.id,
        sort: row.sort,
      };
      this.$http.setProjectSimpleData(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
        }
      });
    },
    // 点击进入添加楼盘界面
    addData(mode) {
      if (mode === 1 && this.tableData.length >= 1) {
        this.$message.error("您已添加了项目");
      } else {
        this.$goPath("/updata_project");
      }
    },

    // 删除操作
    handleDelete(index, row) {
      // deleteProject
      this.$confirm("此操作将删除该项目, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteProject(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 修改操作
    updataCompany(index, row) {
      this.$goPath(`/updata_project?id=${row.id}`);
    },
    setupDynamic(index, row) {
      this.$goPath(`/dynamic_list?id=${row.id}`);
    },
    shareClient(index, row) {
      this.$goPath(`/share_content?id=${row.id}`);
    },
    bindingBroker(row) {
      this.$goPath(`/binding_broker?id=${row.id}`);
    },
    bindingManager(row) {
      this.$goPath(`/binding_manager?id=${row.id}`);
    },
    setupRules(index, row) {
      this.$goPath(`/updata_rules?id=${row.id}`);
    },
    brokerRules(row) {
      this.$goPath(
        `/company_rules_list?company_id=${row.company_id}&project_id=${row.id}`
      );
    },
    viewManager(row) {
      //  this.user_manager_list
      this.dialogView = true;
      this.user_manager_list = row.manager.filter((item) => {
        return item.user !== null && item.user.category === 3;
      });
    },
    bindingIscaseManager() {
      this.dialogCase = true;
      this.getIsCaseManagerData();
    },
    getIsCaseManagerData() {
      this.$http.getIsCaseManagerData({ params: this.cparams }).then((res) => {
        if (res.status === 200) {
          this.user_is_case_manager_list = res.data.data;
          this.cparams.total = res.data.total;
        }
      });
    },

    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChangec(val) {
      this.cparams.per_page = val;
      this.getPageData();
      this.getIsCaseManagerData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChangec(val) {
      this.cparams.page = val;
      this.getPageData();
      this.getIsCaseManagerData();
    },
  },
  computed: {
    ...mapState(["website_info"]),
  },
  activated() {
    console.log(1231232);
    if (this.$store.state.reloadCon) {
      this.getDataList();
      this.$store.state.reloadCon = false
    }
  },

};
</script>
<style lang="scss" scoped>
.build-name {
  display: flex;
  align-items: center;
  .title {
    text-align: start;
  }
  .bottom {
    text-align: start;
    cursor: pointer;
  }
}
</style>
<style scoped lang="scss">
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
.dropbutton {
  width: 54px;
  height: 26px;
  font-size: 12px;
  margin: 5px;
  padding: 5px 15px;
  background: #409eff;
  color: #fff;
}
</style>
