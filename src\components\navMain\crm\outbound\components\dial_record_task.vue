<template>
  <div>
    <!-- 拨打按钮 -->
    <el-button class="phone_btn" type="primary" icon="el-icon-phone" @click="makeTel">拨打电话</el-button>
    <el-button class="phone_btn" type="info" @click="showBlacklist">黑名单</el-button>
    <!-- 拨打记录 -->
    <el-table :data="task_tableData" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
      :row-style="$TableRowStyle">
      <!-- <el-table-column label="ID" width="70" prop="id"></el-table-column> -->
      <!-- <el-table-column label="成员id" prop="admin_id"> </el-table-column> -->
      <el-table-column label="成员名称" prop="admin_name"> </el-table-column>
      <el-table-column label="主叫" prop="caller"> </el-table-column>
      <el-table-column label="被叫" prop="callee"> </el-table-column>
      <el-table-column label="来源" prop="source"> </el-table-column>
      <el-table-column label="通话总时长(秒)" prop="total_duration">
      </el-table-column>
      <el-table-column label="被叫时长(秒)" prop="callee_duration">
      </el-table-column>
      <el-table-column label="主叫时长(秒)" prop="caller_duration">
      </el-table-column>
      <el-table-column label="接通状态" prop="call_status">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.duration == 0" type="warning">
              未接听
            </el-tag>
            <el-tag v-else type="success"> 已接听 </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="拨打时间" prop="created_at"> </el-table-column>
      <el-table-column label="操作" v-slot="{ row }">
        <el-link style="margin-left: 5px" :underline="false" @click="play(row)" v-if="row.record_url">
          <div class="audio_img">
            <img style="width: 20px; object-fit: cover" v-if="row.isPlaying"
              :src="$imageDomain + '/static/admin/outbound/play_voice.gif'" alt="" />
            <img style="width: 20px; object-fit: cover" v-else
              :src="$imageDomain + '/static/admin/outbound/voice_icon.png'" alt="" />
          </div>
        </el-link>
      </el-table-column>
    </el-table>
    <audio ref="musicMp3" id="musicMp3" :autoplay="false" controls="false" style="z-index: -1"></audio>
    <!-- 分页 -->
    <div class="block">
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev,pager,next" :total="telinfoTotal"
        @current-change="handleCurrentChange" :current-page="telinfo_params.page" :page-size="telinfo_params.per_page">
      </el-pagination>
    </div>
    <!-- 拨打电话 -->
    <el-dialog width="420px" title="智能手机" custom-class="dialog" :visible.sync="make_phone" style="margin-top: 15vh;"
      @close="PhoneClose">
      <div class="maKe_phone_call">
        <template v-if="step == 1">
          <div class="maKe_phone_title">拨打电话</div>
          <el-input v-model="calledPhone" placeholder="请输入被叫号码"></el-input>
          <el-select v-model="selected_phone" clearable placeholder="请选择外显号码">
            <el-option v-for="item in outShowPhoneList" :key="item.show_id" :label="item.phone" :value="item.show_id">
            </el-option>
          </el-select>
          <div class="flex-row align-center submit_make_phone">
            <el-button class="flex-1" type="primary" @click="makePhone()">
              确认拨打</el-button>
          </div>
        </template>
        <div v-if="call_route != 2 && call_route != 4 && call_route != 8">
          <template v-if="step == 2 || step == 3">
            <div class="avatar">
              <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
            </div>
            <div class="telphone">{{ calledPhone }}</div>
            <div class="area">归属地</div>
            <div class="telphone waiting blue strong">
              {{ step == 2 ? "运营商转接等待中..." : "运营商转接成功" }}
            </div>
            <div class="link_step">
              <div class="link_step_title strong">通话步骤</div>
              <div class="link_step_con flex-row j-between">
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/waihu/zhuanjiechenggong.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">线路转接成功</div>
                </div>
                <div class="join_line">>>></div>
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img src="https://img.tfcs.cn/backup/static/admin/waihu/laidian.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">手机来电</div>
                </div>
                <div class="join_line">>>></div>
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img src="https://img.tfcs.cn/backup/static/admin/waihu/jieting.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">客户接听通话</div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div v-if="call_route == 2 || call_route == 4|| call_route == 8">
          <div v-if="callRouteShow">
            <!-- 文本 -->
            <!-- <div class="success_left">
              一个被叫号每天只能拨打五次如果无法拨通尝试更换主叫
            </div> -->
            <div class="zhujihaoall">
              <!-- 主机号 -->
              <div class="zhujibohao">
                请使用
                <span class="zhujibohao_number">{{ callers }}</span>
                拨打
              </div>
              <!-- 隐私号 -->
              <div class="yinshi">
                <div style="color: #FF1607; font-size: 24px;">
                  {{ phoneNumber }}
                </div>
                <div class="yinshi_number">
                  隐私号码
                </div>
              </div>
              <!-- 倒计时 -->
              <div class="tiem">
                <div class="tiem_image">
                  <img src="../../../../../assets/tiem (1).png" alt="" class="tiem_image_img">
                </div>
                <div style="color: rgba(46, 60, 78, 0.70); font-size: 12px;margin-left: 4px;">隐私号码
                  <span style="color: #FF3D3D; font-size: 12px;">{{ convertedTime }}s</span>
                  后失效
                </div>
              </div>

            </div>
            <!-- 扫码 -->
            <!-- <div class="success_right"> -->
            <div class="tiem_images">
              <img src="../../../../../assets/tiem (2).png" alt="" class="tiem_image_imgs">
            </div>

            <!-- <el-popover
    placement="top-start"
    title="标题"
    width="200"
    trigger="hover"
    content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
    <div style="color: #FFF; font-size: 14px;margin-left: 12px;margin-bottom: 1px;">
        扫码拨打
       </div> -->
            <el-popover placement="top-start" width="212" trigger="hover">
              <div style="margin-top:10px;">
                <el-alert title="请使用手机自带的扫一扫,不能使用微信,支付宝!!!" type="warning" show-icon :closable="false">
                </el-alert>
              </div>
              <div id="qrcode" class="qrcode" ref="qrCodeUrl"></div>
              <el-button slot="reference" class='button'>扫码拨打</el-button>
            </el-popover>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="黑名单" :visible.sync="showBlackTable" width="950px">
      <div style="margin-bottom: 24px">
        <div class="bottom-border div row">
          <span class="text">手机号</span>
          <el-input style="width: 200px;" v-model="blackTable_params.callee" placeholder="请输入被叫号码" @blur="seachHouseName">
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </div>
        <myTable :table-list="blackTable_list" :header="blackTable_header" :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row :row-style="$TableRowStyle"></myTable>
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="blackTable_total" :page-size="blackTable_params.per_page" :current-page="blackTable_params.page"
          @current-change="blackPageChange">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import myTable from "@/components/components/my_table";
export default {
  components: {
    myTable,
  },
  props: {
    activeName: {
      type: String,
      default: "first",
    },
  },
  data() {
    return {
      call_route:'',
      task_tableData: [],
      telinfo_params: {
        // 当前页
        page: 1,
        // 每页多少条
        per_page: 10,
      },
      // 总条数
      telinfoTotal: 0,
      // 拨打电话属性
      step: '',
      make_phone: false,
      selected_phone: "", //选中的外显号码id
      outShowPhoneList: [], //外显号码列表
      calledPhone: "", // 被叫号码
      blackTable_list: [], // 黑名单列表
      callRouteShow:true,
      // 黑名单列表请求参数
      blackTable_params: {
        page: 1, // 当前页
        per_page: 10, // 每页多少条
        callee: '', // 手机号搜索
      },
      blackTable_total: 0, // 黑名单列表总条数
      showBlackTable: false, // 控制黑名单模态框
      call_route: '',
      phoneNumber: '',// 小号
      callers: '',// 主号
      tiemexpires: 0,//倒计时
      convertedTime: 0,//处理后的时间
      intervalId: null,// 计时器
      // 黑名单表头
      blackTable_header: [
        {prop: 'admin_name', label: '用户名'},
        {prop: 'caller', label: '主叫号码'},
        {prop: 'callee', label: '被叫号码'},
        {prop: 'created_at', label: '呼叫时间'},
        {prop: 'error', label: '失败原因'},
      ]
    };
  },
  created() {
    this.getData();
  },
  methods: {
    // 拨打记录-直拨记录
    getData() {
      this.$http.DirectRecord(this.telinfo_params).then((res) => {
        if (res.status == 200) {
          this.task_tableData = res.data.data;
          // this.telinfo_params.page = res.data.current_page
          // this.telinfo_params.per_page = res.data.per_page
          this.telinfoTotal = res.data.total
        }
      });
    },
    // 分页器-当前页
    handleCurrentChange(val) {
      this.telinfo_params.page = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    makeTel() {
      this.calledPhone = "";
      this.selected_phone = "";
      this.getShowTelNumber();
      this.step = 1;
      this.make_phone = true;
    },
    makePhone() {
      if (this.calledPhone == '') {
        this.$message({
          message: '手机号不能为空',
          type: 'warning'
        })
      } else if (this.calledPhone.length < 11) {
        this.$message({
          message: '请输入完整手机号',
          type: 'warning'
        })
      } else {
        this.$http.DirectTelephone({
          show_id: this.selected_phone,
          phone: this.calledPhone
        }).then(res => {
          console.log(res.data,'0000000111111');
          if (res.status == 200) {
            try{
              let call_show_phone = this.outShowPhoneList.find(item => item.show_id == this.selected_phone)
              this.$http.sendPhoneTel({
                call_phone_id: res.data.call_id,
                call_name: '',
                call_phone: this.calledPhone,
                call_show_phone: call_show_phone ? call_show_phone['phone'] : '',
                type: 4
              })
            }catch(e){}

            this.make_phone = true
            this.callRouteShow = true
            this.step = 2
          this.callers = res.data.caller
          this.call_route = res.data.call_route
          this.tiemexpires = res.data.expires
          this. convertedTime =  this.tiemexpires / 2
          // 倒计时处理
      this.intervalId = setInterval(() => {
        this.step = 3;
        if (this. convertedTime > 1) {
          this. convertedTime--;
        } else {
          clearInterval(this.intervalId);
          this.make_phone = false
          this.callRouteShow = false
        }
      }, 1000);
// 显示二维码
          if ([2, 4, 8].some(item => item == res.data.call_route)) {
            var that = this
            that.phoneNumber = res.data.telX
            console.log(that.$refs.qrCodeUrl,'this.$refs.qrCodeUrl');
        setTimeout(() => {
                  new QRCode(that.$refs.qrCodeUrl, {
                  text: 'tel:'+ that. phoneNumber, // 需要转换为二维码的内容
                  width: 170,
                  height: 170,
                  colorDark: '#000000',
                  colorLight: '#ffffff' ,
                  correctLevel: QRCode.CorrectLevel.H
              })
               }, 0)

      
          }else{
              setTimeout(() => {
              this.step = 3;
              setTimeout(() => {
                that.make_phone = false
              }, 1000);
            }, 30000);
          }
        }
      })
      
      }
    },
    // 获取拨打电话下拉信息
    getShowTelNumber() {
      this.$http.getExplicitNumber().then(res => {
        if (res.status == 200) {
          this.outShowPhoneList = res.data
        }
      })
    },
    PhoneClose(){
      clearInterval(this.intervalId);
          this.make_phone = false
          this.callRouteShow = false
    },
    // 弹框拨打电话详情
    play(row) {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      if (this.currI !== row && this.currI) {
        audios.pause();
        this.$set(this.currI, 'isPlaying', false);
        audios.src = row.record_url
        this.$forceUpdate();
      }
      if (row.isPlaying) {
        audios.pause();
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      } else {
        if (this.currI !== row) {
          audios.src = row.record_url
        }
        audios.play();
        this.currI = row;
        this.$set(row, 'isPlaying', true);
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      }, row.duration * 1000);
    },
    // 显示黑名单模态框
    showBlacklist() {
      this.showBlackTable = true;
      if(!this.blackTable_list.length) {
        this.getDialBlackList(); // 获取黑名单列表
      }
    },
    // 获取黑名单列表
    getDialBlackList() {
      this.$http.getDialBlackList(this.blackTable_params).then((res) => {
        if(res.status == 200) {
          this.blackTable_list = res.data.data;
          this.blackTable_total = res.data.total;
        }
      })
    },
    // 黑名单列表页面改变触发
    blackPageChange(value) {
      this.blackTable_params.page = value;
      this.getDialBlackList(); // 获取黑名单列表
    },
    seachHouseName() {
      this.blackTable_params.page = 1;
      this.getDialBlackList(); // 获取黑名单列表
    }
  },
};
</script>

<style scoped lang="scss">
::v-deep .button {
  color: #ffff;
  border-color: #12D367;
  background-color: #12D367;
  width: 100%;
}

::v-deep .el-button:active .button {
  background-color: #12D367;
  border-color: #12D367;
  color: #ffff;
}

::v-deep .el-button .button {
  position: relative;
  background: #12D367;
  border: 1px solid #12D367;
  width: 100%;
  color: #fff;
}

.zhujihaoall {
  width: 100%;
  text-align: center;
}

.success_right {
  // width: 92px;
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  background: #12D367;
}

.success_left {
  padding: 8px 10px;
  // width: 70%;
  color: #12D367;
  font-size: 12px;
  border-radius: 4px;
  // text-align: left;s
  border: 1px solid #12D367;
  background: rgba(18, 211, 103, 0.10);
  // margin-right: 12px;
}

.yinshi_number {
  color: rgba(46, 60, 78, 0.40);
  font-size: 12px;
  padding: 4px;
  border-radius: 4px;
  line-height: 22px;
  background: #F1F4FA;
  margin-left: 8px;

}

.tiem_image_img {
  width: 100%;
  height: 100%;
}

.tiem_image_imgs {
  width: 100%;
  height: 100%;
  margin: 0 4px;
}

.tiem_images {
  position: absolute;
  left: 10px;
  z-index: 999;
  top: 212px;
  width: 16px;
  height: 16px;
  margin-left: 145px;
}

.tiem_image {
  width: 16px;
  height: 16px;
}

.tiem {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  margin-bottom: 24px;
  justify-content: center;
}

.yinshi {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: 8px;
  justify-content: center;
}

.zhujibohao_number {
  color: #2D84FB;
  font-size: 14px;
}

.zhujibohao {
  color: rgba(46, 60, 78, 0.70);
  font-size: 14px;
  // text-align: left;
  margin-bottom: 24px;
  margin-top: 24px;
}

.qrcode {
  width: 200px;
  height: 200px;
  text-align: center;
  margin-left: 20px;
  margin-top: 40px;
}

::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;

  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }

  .el-dialog__body {
    padding: 0 12px;
  }
}

.mr10 {
  margin-right: 10px;
}

.strong {
  font-weight: 600;
}

.blue {
  color: #2d84fb;
}

.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}

.maKe_phone_call {
  // height: 500px;
  text-align: center;
  background: #fff;
  padding: 20px 20px;
  margin-bottom: 10px;
  box-sizing: border-box;

  .maKe_phone_title {
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
  }

  .submit_make_phone {
    margin-top: 65px;
  }

  .avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 50%;

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .telphone {
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
    margin-top: 16px;
  }

  .area {
    color: #8a929f;
    font-size: 14px;
    margin-top: 16px;
  }

  .waiting {
    margin: 15px 0 20px;
  }

  .link_step {
    border-radius: 13px;
    background: #f1f4fa;
    padding: 10px 16px;

    .link_step_title {
      color: #2e3c4e;
      font-size: 14px;
      margin-bottom: 15px;
      white-space: nowrap;
    }

    .link_step_con {
      .link_step_left {
        width: 48px;

        .link_step_left_bottom {
          color: #2e3c4e;
          font-size: 12px;
          white-space: nowrap;
          margin-left: -10px;
        }
      }

      .join_line {
        margin-top: 5px;
        color: #2d84fb;
      }
    }
  }

  .to_back {
    margin-top: 65px;

    .to_back_img {
      width: 50px;
      height: 50px;
      margin: 0 auto 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .to_back_name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }

  .el-select {
    width: 100%;
    padding-top: 20px;
  }
}

.phone_btn {
  margin-bottom: 20px;
}

#musicMp3 {
  position: absolute;
  left: -200%;
  top: -200%;
  width: 0;
  height: 0;
}

.audio_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
  background: #409eff;
}

.bottom-border {
  margin-bottom: 12px;

  .text {
    color: #2e3c4e;
    font-size: 14px;
    line-height: 40px;
    padding-right: 10px;
  }
}
</style>
