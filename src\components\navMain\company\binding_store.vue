<template>
  <el-container>
    <el-header class="div row">
      <div class="div row">
        <el-button type="primary" @click="addData" icon="el-icon-plus"
          >添加</el-button
        >
      </div>
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <myPagination
        :total="params.total"
        :pagesize="params.pagesize"
        :currentPage="params.currentPage"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </el-footer>
    <el-dialog
      :title="store_title_map[store_dialog_title]"
      :visible.sync="store_dialog_create"
      width="50%"
    >
      <el-form :model="store_form" label-width="100px">
        <el-form-item label="选择门店">
          <el-select
            filterable
            v-model="store_form.store_id"
            placeholder="请选择门店"
          >
            <el-option
              v-for="item in store_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="storeForm">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "binding_store",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        id: "",
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      store_dialog_create: false,
      store_title_map: {
        addData: "添加",
        updataData: "修改",
      },
      store_dialog_title: "",
      store_form: {
        company_id: "",
        store_id: "",
        company_manager_user_id: "",
      },
      store_list: [],
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "company_name", label: "公司名称" },
        { prop: "store_name", label: "门店名称" },
        {
          label: "员工",
          render: (h, data) => {
            return (
              <div class="div row" style="align-items:center">
                <img
                  src={data.row.u_avatar}
                  style="width:50px;height:50px;margin-right:5px"
                />
                <i>
                  {data.row.u_name ||
                    data.row.u_nickname ||
                    data.row.u_user_name}{" "}
                  : {data.row.u_phone}
                </i>
              </div>
            );
          },
        },
        {
          label: "操作",
          fixed: "right",
          render: (h, data) => {
            return (
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="danger"
                onClick={() => {
                  this.handleDelete(data.row);
                }}
              >
                取消绑定
              </el-button>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.params.id = this.$route.query.company_id;
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.$http.getManagerStore(this.params).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
          this.getStoreList(this.params.id);
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    // 获取门店列表
    getStoreList(id) {
      this.$http.queryStoreList(id).then((res) => {
        if (res.status === 200) {
          this.store_list = res.data.data;
        }
      });
    },
    addData() {
      this.store_form = {};
      this.store_form.company_id = this.params.id;
      this.store_form.company_manager_user_id = this.$route.query.manager_id;
      this.store_dialog_create = true;
      this.store_dialog_title = "addData";
    },
    storeForm() {
      this.$http.managerBindStore(this.store_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "绑定成功",
            type: "success",
          });
          this.getDataList();
          this.store_dialog_create = false;
        }
      });
    },
    handleDelete(row) {
      this.$confirm("此操作将解除绑定, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.cancelManagerStore(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "已解除!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>

<style lang="scss">
.el-header {
  align-items: center;
}
.el-table {
  .el-button {
    border-radius: 4px;
    margin: 5px;
  }
}
</style>
