<template>
<el-dialog :title="title" :visible.sync="show" width="800px" append-to-body>
    <div v-loading="loading">
        <el-form label-width="120px">
            <el-form-item label="时间类型:">
                <el-row :gutter="10">
                    <el-col :span="6" style="padding-left: 0;">
                        <admSelect v-model="params.date_style" :data="dateStyleList" placeholder="选择时间类型"/>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="params.date_sort">
                            <el-option label="降序" :value="1"></el-option>
                            <el-option label="升序" :value="2"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="8">
                        <el-input v-model="params.date_range" @input="setTabName">
                            <template #prepend>周期</template>
                            <template #append>天</template>
                        </el-input>
                    </el-col>
                    <el-col :span="3">
                        <admSelect v-model="params.date_type" :data="dataTypes" @change="setTabName"/>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="行为类型">
                <div class="filter-block">
                    <span class="filter-item" v-for="item in behaviorFilters" :key="item.name">
                        {{item.label}}
                        <span class="filter-item-del" @click="clearFilter(item.name)"><i class="el-icon-close"></i></span>
                    </span>

                    <el-popover placement="bottom" width="660" trigger="click" class="filter-item filter-item-add">
                        <span slot="reference" class="filter-item-add-btn"><i class="el-icon-plus"></i>&nbsp;添加项</span>
                        <el-form label-width="120px" inline class="filter-form">
                            <el-form-item label="跟进类型:">
                                <admSelect v-model="params.follow_type" :data="followTypes" placeholder="选择跟进类型" @change="setTabName" clearable/>
                            </el-form-item>
                            <el-form-item label="邀约类型:">
                                <admSelect v-model="params.invite_type" :data="inviteList" placeholder="选择邀约类型" props="{label:'name',value:'id'}" clearable/>
                            </el-form-item>
                        </el-form>
                    </el-popover>
                </div>
            </el-form-item>
            <el-form-item label="筛选项">
                <div class="filter-block">
                    <span class="filter-item" v-for="item in screenFilters" :key="item.name">
                        {{item.label}}
                        <span class="filter-item-del" @click="clearFilter(item.name)"><i class="el-icon-close"></i></span>
                    </span>

                    <el-popover placement="bottom" width="660" trigger="click" class="filter-item filter-item-add">
                        <span slot="reference" class="filter-item-add-btn"><i class="el-icon-plus"></i>&nbsp;添加项</span>
                        <el-form label-width="120px" inline class="filter-form">
                            <el-form-item label="客户来源:">
                                <el-cascader :options="customerSourceList" v-model="params.source_id" 
                                    :props="{ checkStrictly: true, emitPath: false, label:'title',value:'id' }" clearable></el-cascader>
                            </el-form-item>
                            <el-form-item label="客户状态:">
                                <admSelect v-model="params.tracking_id" :data="customerTrackingList" placeholder="选择客户状态" 
                                    props="{label:'title',value:'id'}" clearable/>
                            </el-form-item>
                            <el-form-item label="客户类型:">
                                <admSelect v-model="params.type" :data="customerTypeList" placeholder="选择客户类型" 
                                    props="{label:'title',value:'id'}" clearable/>
                            </el-form-item>
                            <el-form-item label="客户等级:">
                                <admSelect v-model="params.level_id" :data="customerLevelList" placeholder="选择客户等级" 
                                    props="{label:'label',value:'id'}" clearable/>
                            </el-form-item>
                            <el-form-item label="通话状态:">
                                <admSelect v-model="params.call_status" :data="callStatusList" placeholder="选择通话状态" props="{label:'title',value:'id'}" clearable/>
                            </el-form-item>
                        </el-form>
                    </el-popover>
                </div>
            </el-form-item>
        </el-form>

        <el-form label-width="120px">
            <el-form-item label="名称:">
                <el-input v-model="params.name" placeholder="请输入名称"></el-input>
            </el-form-item>
        </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>

<script>
import Options from '../config/filterOptions.js'
export default {
    props: {
        type: { type: Number, default: 1 }    //1:我的,2:公海，3：潜客
    },
    data(){
        return {
            show: false,        //dialog是否显示
            loading: false,
            successFn: null, 
            isAdd: true,
            submiting: false,
            params: {},
            dateStyleList: [],          //时间类型
            dataTypes: [],              //时间周期类型
            followTypes: [],            //跟进类型
            inviteList: [],             //邀约类型
            callStatusList: [],         //通话状态
            customerSourceList: [],     //客户来源
            customerTrackingList: [],   //客户状态
            customerTypeList: [],       //客户类型
            customerLevelList: []       //客户等级
        }
    },
    computed: {
        title(){
            return this.isAdd? '新增自定义导航' : '编辑自定义导航';
        },
        //打平客户来源列表
        flattenCustomerSourceList(){
            return this.flattenTreeArray(this.customerSourceList);
        },
        //已选的行为类型项
        behaviorFilters(){
            return this.formatSelectedFilters([
                { data: this.followTypes, key: 'follow_type', value: 'value', label: 'label'},
                { data: this.inviteList, key: 'invite_type', value: 'id', label: 'name'}
            ]);
        },
        //已选的筛选项
        screenFilters(){
            return this.formatSelectedFilters([
                { data: this.flattenCustomerSourceList, key: 'source_id', value: 'id', label: 'title'},
                { data: this.customerTrackingList, key: 'tracking_id', value: 'id', label: 'title'},
                { data: this.customerTypeList, key: 'type', value: 'id', label: 'title'},
                { data: this.customerLevelList, key: 'level_id', value: 'id', label: 'label'},
                { data: this.callStatusList, key: 'call_status', value: 'id', label: 'title'},
            ]);
        },
    },
    created(){
        //获取筛选项列表
        const options = Options.getOptions(this.type);
        this.dateStyleList = options.dateStyleList;
        this.dataTypes = options.dataTypes;
        this.followTypes = options.followTypes;
        this.inviteList = options.inviteList;
        this.callStatusList = options.callStatusList;
        Options.getCustomerSourceList(d => this.customerSourceList = d);
        Options.getCustomerTrackingList(d => this.customerTrackingList = d);
        Options.getCustomerTypeList(d => this.customerTypeList = d);
        Options.getCustomerLevelList(d => this.customerLevelList = d);
    },
    methods: {
        //格式化筛选项
        formatSelectedFilters(fileterRels){
            const filters = [];
            for(const rel of fileterRels){
                const value = this.params[rel.key];
                if(value){
                    const item = rel.data.find(e => e[rel.value] === value);
                    filters.push({
                        name: rel.key,
                        value: value,
                        label: item && item[rel.label]
                    })
                }
            }
            return filters;
        },
        //打平树结构数组
        flattenTreeArray(arr) {
            return arr.reduce((prev, cur) => {
                return Array.isArray(cur.children) && cur.children.length ? prev.concat(this.flattenTreeArray(cur.children)).concat(cur)
                : prev.concat(cur)
            }, []);
        },
        //清楚筛选项
        clearFilter(name){
            this.params[name] = '';
        },
        //设置tab名称
        setTabName(){
            let tabName = '',
                date_range = this.params.date_range,
                date_type = '',
                follow_type = '';
            if(date_range){
                date_type = (this.dataTypes.find(e => e.value === this.params.date_type) || {}).label || '',
                follow_type = (this.followTypes.find(e => e.value === this.params.follow_type) || {}).label || '';
                tabName = `${date_range}天${date_type}${follow_type}`;
                this.params.name = tabName;
            }
        },
        //获取tab详情
        async getDetail(id){
            this.loading = true;
            const res = await this.$http.getCrmCustomTabDetail(id, this.type);
            this.loading = false;
            if(res.status == 200){
                const data = res.data.tab;
                for(const key in data){
                    this.params[key] !== undefined && (this.params[key] = data[key] || '');
                }
                this.params.id = data.id;
            }
        },
        open(params){
            this.isAdd = params.id ? false : true;
            this.params = {
                name: '',
                date_style: 1,      //时间类型
                date_sort: 1,       //时间排序
                date_range: '',     //时间周期天数
                date_type: 2,       //时间周期类型
                follow_type: '',    //跟进类型
                invite_type: '',    //邀约类型
                source_id: '',      //客户来源
                tracking_id: '',    //客户状态
                type: '',           //客户类型
                level_id: '',       //客户等级
                call_status: '',    //通话状态
            };
            if(!this.isAdd){
                this.getDetail(params.id);
            }
            this.params.tab_type = this.type;

            this.show = true;
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            const params = {...this.params};
            if(!this.Validate({
                name: {require: '请填写名称'}
            }).check(params)){
                return;
            }

            this.submiting = true;
            const res = await this.$http[this.isAdd?'addCrmCustomTab':'editCrmCustomTab'](params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }  
}
</script>
<style lang="scss" scoped>
.el-input{
    width: 220px;
}
.el-col{
    .el-input{
        width: 100%;
    }
}
::v-deep {
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
.filter-block{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    .filter-item{
        display: inline-flex;
        justify-content: center;
        align-items: center;
        height: 30px;
        min-width: 60px;
        line-height: 1;
        padding: 0 15px;
        margin: 5px 12px 3px 0;
        border: 1px solid #cce1f7;
        background: rgb(232, 241, 255);
        color: #409eff;
        .filter-item-del{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 16px;
            width: 16px;
            margin-left: 8px;
            cursor: pointer;
            &:hover{
                background: #bed4f7;
            }
        }
    }
    .filter-item-add{
        border:none;
        padding: 0;
        background: #fff;
        .filter-item-add-btn{
            cursor: pointer;
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 15px;
            color: #9c9c9c;
            border: 1px dashed #DCDFE6;
            border-radius: 2px;
            transition: all .3s;
            &:hover{
                color:#409eff;
                border-color: #409eff;
            }
        }
    }
}
.filter-form{
    margin-top: 20px;
}
</style>
<style lang="scss">
    .el-drawer:focus,.el-drawer__header > span:focus{
        border: none;
        outline: none;
    }
</style>