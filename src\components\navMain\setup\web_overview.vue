<template>
  <div class="web-box">
    <el-row>
      <el-col class="content" :span="24" v-loading="is_row_loading">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="tab-title flex-row tab-point" @click="$goPath('/property_list')">
              <img class="top-img" src="@/assets/webimg/loupan.png" alt="" />
              <div class="top-right">
                <div class="num">{{ total.build }}</div>
                <div class="desc">楼盘总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="tab-title flex-row tab-point" @click="$goPath('/project_list')">
              <img class="top-img" src="@/assets/webimg/xiangmu.png" alt="" />
              <div class="top-right">
                <div class="num">{{ total.project }}</div>
                <div class="desc">项目总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="tab-title flex-row tab-point" @click="$goPath('/user_list')">
              <img class="top-img" src="@/assets/webimg/huiyuan.png" alt="" />
              <div class="top-right">
                <div class="num">{{ total.user }}</div>
                <div class="desc">会员总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="tab-title flex-row tab-point" @click="$goPath('/report_customer')">
              <img class="top-img" src="@/assets/webimg/kehu.png" alt="" />
              <div class="top-right">
                <div class="num">{{ total.customer }}</div>
                <div class="desc">客户总数</div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="tab-title" v-if="report_info.overview">
              <h3>报备信息</h3>
              <div class="content-box flex-row">
                <div class="left-img div">
                  <div class="num-box">
                    <div class="l-t">
                      {{ report_info.overview.report || 0 }}
                    </div>
                    <p class="p1">报备</p>
                  </div>
                  <div class="num-box">
                    <div class="l-c">
                      {{ report_info.overview.enable || 0 }}
                    </div>
                    <p class="p2">有效</p>
                  </div>
                  <div class="num-box">
                    <div class="l-b">{{ report_info.overview.sign || 0 }}</div>
                    <p class="p3">签约</p>
                  </div>
                </div>
                <div class="right-content">
                  <table class="t">
                    <thead>
                      <tr>
                        <th></th>
                        <th>有效客户</th>
                        <th>无效客户</th>
                        <th>已到访</th>
                        <th>已成交</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <th>今日新增</th>
                        <td>{{ report_info.detail.today.enable || 0 }}</td>
                        <td>{{ report_info.detail.today.disable || 0 }}</td>
                        <td>{{ report_info.detail.today.visited || 0 }}</td>
                        <td>{{ report_info.detail.today.deal || 0 }}</td>
                      </tr>
                      <tr>
                        <th>7日新增</th>
                        <td>{{ report_info.detail.week.enable || 0 }}</td>
                        <td>{{ report_info.detail.week.disable || 0 }}</td>
                        <td>{{ report_info.detail.week.visited || 0 }}</td>
                        <td>{{ report_info.detail.week.deal || 0 }}</td>
                      </tr>
                      <tr>
                        <th>30日新增</th>
                        <td>{{ report_info.detail.month.enable || 0 }}</td>
                        <td>{{ report_info.detail.month.disable || 0 }}</td>
                        <td>{{ report_info.detail.month.visited || 0 }}</td>
                        <td>{{ report_info.detail.month.deal || 0 }}</td>
                      </tr>
                      <tr>
                        <th>客户总量</th>
                        <td>{{ report_info.detail.total.enable || 0 }}</td>
                        <td>{{ report_info.detail.total.disable || 0 }}</td>
                        <td>{{ report_info.detail.total.visited || 0 }}</td>
                        <td>{{ report_info.detail.total.deal || 0 }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="top-expansion">
                <el-button @click.stop.prevent="showFrontDesk" type="primary" size="mini" round>
                  外网预览
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="tab-title">
              <div class="month">当月</div>
              <div ref="chartBox" class="chart-box" id="chart-box" style="width: 100%; height: 250px"></div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6" v-if="$store.state.website_info.website_mode_category !== 1">
            <div class="tab-title">
              <h3>热卖楼盘</h3>
              <div class="content-box">
                <table class="t-build t">
                  <tbody>
                    <tr>
                      <td class="t-t">
                        <p>楼盘名称</p>
                      </td>
                      <td class="t-t" style="text-align: center">
                        <p style="margin-left: 0">客户量</p>
                      </td>
                    </tr>
                    <tr v-for="(item, index) in build_rank" :key="index">
                      <td class="flex-row">
                        <img v-if="index === 0" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 1" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 2" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <span class="rank" v-if="index > 2">{{
                                                  index + 1
                                                  }}</span>
                        <span class="name">{{ item.build_name }}</span>
                      </td>
                      <td>{{ item.hot }}/个</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-col>
          <el-col :span="6" v-if="$store.state.website_info.website_mode_category !== 1">
            <div class="tab-title">
              <h3 class="row is-row-title">
                业绩排行
                <span class="tab-point" @click="is_company_rank = !is_company_rank">销售公司/经纪人</span>
              </h3>
              <div class="content-box">
                <table class="t-build t">
                  <tbody v-if="is_company_rank">
                    <tr>
                      <td class="t-t">
                        <p>名称</p>
                      </td>

                      <td class="t-t" style="text-align: center">
                        <p style="margin-left: 0">成交量</p>
                      </td>
                    </tr>
                    <tr v-for="(item, index) in companyRank_list" :key="index">
                      <td class="flex-row">
                        <img v-if="index === 0" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 1" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 2" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <span class="rank" v-if="index > 2">{{
                                                  index + 1
                                                  }}</span>
                        <span class="name">{{ item.co_name || "--" }}</span>
                      </td>
                      <td>{{ item.achievement }}/套</td>
                    </tr>
                  </tbody>
                  <tbody v-else>
                    <tr>
                      <td class="t-t">
                        <p>名称</p>
                      </td>

                      <td class="t-t" style="text-align: center">
                        <p style="margin-left: 0">成交量</p>
                      </td>
                    </tr>
                    <tr v-for="(item, index) in user_rank" :key="index">
                      <td class="flex-row">
                        <img v-if="index === 0" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 1" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 2" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <span class="rank" v-if="index > 2">{{
                                                  index + 1
                                                  }}</span>
                        <span class="name">{{
                                                  item.u_name || item.u_nickname
                                                  }}</span>
                      </td>
                      <td>{{ item.achievement }}/套</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-col>
          <el-col :span="6" v-if="$store.state.website_info.website_mode_category === 1">
            <div class="tab-title">
              <h3>销售公司</h3>
              <div class="content-box">
                <table class="t-build t">
                  <tbody>
                    <tr>
                      <td class="t-t">
                        <p>名称</p>
                      </td>
                      <td class="t-t" style="text-align: center">
                        <p style="margin-left: 0">成交量</p>
                      </td>
                    </tr>
                    <tr v-for="(item, index) in companyRank_list" :key="index">
                      <td class="flex-row">
                        <img v-if="index === 0" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 1" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 2" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <span class="rank" v-if="index > 2">{{
                                                  index + 1
                                                  }}</span>
                        <span class="name">{{ item.co_name }}</span>
                      </td>
                      <td>{{ item.achievement }}/套</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-col>
          <el-col :span="6" v-if="$store.state.website_info.website_mode_category === 1">
            <div class="tab-title">
              <h3>经纪人</h3>
              <div class="content-box">
                <table class="t-build t">
                  <tbody>
                    <tr>
                      <td class="t-t">
                        <p>名称</p>
                      </td>
                      <td class="t-t" style="text-align: center">
                        <p style="margin-left: 0">成交量</p>
                      </td>
                    </tr>
                    <tr v-for="(item, index) in user_rank" :key="index">
                      <td class="flex-row">
                        <img v-if="index === 0" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 1" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <img v-if="index === 2" src="../../../assets/webimg/<EMAIL>" alt="" />
                        <span class="rank" v-if="index > 2">{{
                                                  index + 1
                                                  }}</span>
                        <span class="name">{{
                                                  item.u_name || item.u_nickname
                                                  }}</span>
                      </td>
                      <td>{{ item.achievement }}/套</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="tab-title">
              <h3 v-if="$store.state.website_info.website_mode_category === 0" style="margin-bottom: 0; margin: 10px 0">
                系统地址：
              </h3>
              <div class="site-box" v-if="$store.state.website_info.website_mode_category === 0">
                <div class="site-top flex-row">
                  <div class="site">
                    {{ `https://yun.tfcs.cn/fenxiao?website_id=${website_id}` }}
                  </div>
                  <div class="btn" @click="
                                        $onCopyValue(
                                          `https://yun.tfcs.cn/fenxiao?website_id=${website_id}`
                                        )
                                      ">
                    复制
                  </div>
                </div>
                <p class="tips">
                  微信前台地址，可挂入公众号菜单，或微信文章阅读原文中
                </p>
              </div>
              <h3 style="margin-bottom: 0; margin: 10px 0">后台地址：</h3>
              <div class="site-box">
                <div class="site-top flex-row">
                  <div class="site">
                    {{
                                        `https://yun.tfcs.cn/admin/#/index?website_id=${website_id}`
                                        }}
                  </div>
                  <div class="btn" @click="
                                        $onCopyValue(
                                          `https://yun.tfcs.cn/admin/#/index?website_id=${website_id}`
                                        )
                                      ">
                    复制
                  </div>
                </div>
                <p class="tips">
                  商家后台管理地址，只可用电脑访问，请不要挂到公众号菜单中
                </p>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col>
        <div class="mobile" v-if="$store.state.website_info.website_mode_category == 0">
          <div class="top-header">
            <span>前台预览</span>
          </div>
          <div class="wx-header">
            <span>{{ $store.state.website_info.name }}</span>
            <img src="@/assets/webimg/Wechatsystemdefault@<EMAIL>" alt="" />
          </div>
          <div class="input-box">
            <span class="el-icon-search icon"></span>
            <input disabled type="text" placeholder="请输入楼盘名称" />
          </div>
          <div class="swiper" @click="onBannerPath(1)" v-if="$store.state.website_info.carousel != 2">
            <el-carousel indicator-position="none" height="150px" class="swiper-style" v-if="m_banner_data.length > 0">
              <el-carousel-item v-for="item in m_banner_data" :key="item.id">
                <img :src="item.img" alt="" />
              </el-carousel-item>
            </el-carousel>

            <div class="swiper-style" v-else>
              <img src="https://img.tfcs.cn/backup/static/admin/h5/default-header.png" alt="" />
            </div>
          </div>
          <div class="report" v-if="$store.state.website_info.button_style == 2">
            <div class="report-item" v-for="item in m_report_list" :key="item.index" :style="{
                            backgroundImage: `url(${item.icon}?x-oss-process=style/w_220)`,
                          }">
              <div class="report-font">{{ item.name }}</div>
            </div>
          </div>
          <div class="build_list" v-else>
            <div class="build-item" v-for="item in m_new_type" :key="item.id">
              <div class="img-box-status">
                <img :src="item.img" />
              </div>
              <div class="font">{{ item.name }}</div>
            </div>
          </div>
          <div class="build-title">
            <div class="t-l">
              {{ $store.state.website_info.build_menu_name || "推荐楼盘" }}
            </div>
            <div class="t-r">
              更多
              <img src="@/assets/webimg/更多@1x.png" alt="" />
            </div>
          </div>

          <myBuildItem v-if="build_list.length > 0" :list="build_list"
            :type="$store.state.website_info.build_menu_style || 1" @load="loadPage" @click="onBannerPath(2)"
            :loading="is_loading"></myBuildItem>
          <div v-else class="default-list">
            <img src="https://img.tfcs.cn/backup/static/admin/h5/list-default.png" alt="" />
          </div>
          <div class="tab-box">
            <img style="object-fit: none" src="	https://img.tfcs.cn/backup/static/admin/h5/tabbar.png" alt="" />
          </div>
        </div>
        <!-- <div
          class="mobile"
          v-if="$store.state.website_info.website_mode_category == 1"
        >
          <div class="top-header">
            <span>前台预览</span>
          </div>
          <div class="wx-header" style="justify-content: flex-end">
            <img
              src="@/assets/webimg/Wechatsystemdefault@<EMAIL>"
              alt=""
            />
          </div>
          <div
            class="dan-banner"
            v-if="$store.state.website_info.carousel != 2"
          >
            <el-carousel
              indicator-position="none"
              height="272px"
              class="swiper-style"
              v-if="m_banner_data.length > 0"
            >
              <el-carousel-item v-for="item in m_banner_data" :key="item.id">
                <img :src="item.img" alt="" />
              </el-carousel-item>
            </el-carousel>
            <div class="swiper-style" v-else>
              <img
                src="https://img.tfcs.cn/backup/static/admin/h5/default-header.png"
                alt=""
              />
            </div>
            <div class="d-content">
              <div class="d-top">
                <div class="build-title">
                  <div class="name">{{ dan_build_info.build_name }}</div>
                  <div class="ckfj">查看附件</div>
                </div>
                <div class="build-label-box">
                  <div
                    class="label"
                    v-for="(item, index) in build_category_label"
                    :key="index"
                  >
                    {{ item }}
                  </div>
                </div>
                <div class="price-box">
                  <div class="left">
                    <span class="label-left">均价</span>
                    <div class="price">
                      {{ dan_build_info.build_avg_price + "元/㎡" || "--" }}
                    </div>
                  </div>
                  <div class="right">
                    <img
                      class="fdjs"
                      src="https://img.tfcs.cn/backup/static/admin/h5/d-fdjs.png"
                      alt=""
                    />
                    <span style="margin-left: 12rpx">房贷计算器</span>
                  </div>
                </div>
                <div class="address">
                  <div class="left">地址</div>
                  <div class="right">
                    {{ dan_build_info.build_address || "暂未更新" }}
                  </div>
                </div>
                <div class="address">
                  <div class="left">建筑面积</div>
                  <div class="right">{{ open_time || "暂未更新" }}</div>
                </div>
                <div class="address address-btn">
                  <div style="align-items: flex-end; display: flex">
                    <div class="left">开盘时间</div>
                    <div class="right">
                      {{ dan_build_info.newest_opening_time || "暂未更新" }}
                    </div>
                  </div>
                  <div v-if="dan_build_info.build_id" class="project-detail">
                    项目详情
                  </div>
                </div>
                <div class="avatars">
                  <div style="display: flex">
                    <img
                      v-for="(item, index) in view_user_list"
                      :key="index"
                      class="pre_logo"
                      :src="
                        item.u_avatar ||
                        'https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80'
                      "
                    /><img
                      style="background: #fff"
                      class="pre_logo"
                      src="https://img.tfcs.cn/backup/static/admin/h5/d-txsl.png"
                    />
                  </div>
                  <div class="browse-count">共{{ view_user_count }}人浏览</div>
                </div>
              </div>
              <div class="coupon-box">
                <div class="coupon">
                  <div class="left">{{ dan_build_info.buy_coupon }}</div>
                  <div class="right-btn">立即领取</div>
                </div>
              </div>
              <div class="click-box">
                <div class="click-box-ctn">
                  <div
                    class="click-box-left"
                    :class="{ isrow: !is_setting.config_support_reported }"
                    :style="{
                      justifyContent: !is_setting.config_support_reported
                        ? 'space-between'
                        : '',
                      width: !is_setting.config_support_reported ? '100%' : '',
                    }"
                  >
                    <div
                      class="left-top"
                      :class="{
                        clickBox3: !is_setting.config_support_reported,
                      }"
                      :style="{
                        background: item.bg,
                      }"
                      v-for="item in click_box"
                      :key="item.id"
                    >
                      <img class="icon-f" :src="item.icon" alt="" />
                      <span class="label-text">{{ item.desc }}</span>
                      <img class="icon-1" :src="item.img_path" />
                    </div>
                  </div>
                  <div
                    class="click-box-right"
                    v-if="is_setting.config_support_reported"
                  >
                    <span class="label-text">报备客户</span>
                    <img
                      src="https://img.tfcs.cn/static/img/only_baobeikehu.png"
                    />
                  </div>
                </div>
              </div>

              <div class="content-box-dan">
                <div class="title">
                  <div class="label">楼盘周边</div>
                  <div class="loadmore row">查看更多</div>
                </div>
                <img
                  class="map"
                  style="margin-top: 10px"
                  src="https://img.tfcs.cn/backup/static/admin/h5/zhoubian.png"
                  alt=""
                />
                <div class="title">
                  <div class="label">楼盘相册</div>
                  <div class="loadmore row">查看更多</div>
                </div>
                <div class="photos-box" v-if="photos_list.length > 0">
                  <div
                    class="photos-item"
                    v-for="(item, index) in photos_list"
                    :key="index"
                  >
                    <img :src="item.photos[0]" />
                    <div class="right-d">
                      <div class="title-d">{{ item.description }}</div>
                      <div class="bottom">共{{ item.photos.length }}张</div>
                    </div>
                  </div>
                </div>
                <div class="title">
                  <div class="label">楼盘动态</div>
                  <div class="loadmore row">查看更多</div>
                </div>
                <div
                  class="dynamic-list bottom-line row"
                  v-for="item in dynamic_list"
                  :key="item.id"
                >
                  <div class="left row">
                    <span class="title-p">{{ item.title }}</span>
                    <span class="time">{{ item.created_at }}</span>
                  </div>
                  <div class="right">
                    <img :src="item.img" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="tab-box">
            <img
              src="	https://img.tfcs.cn/backup/static/admin/h5/tabbar_dan.png"
              alt=""
            />
          </div>
        </div>
        <div class="code-box">
          <div
            class="tab-title"
            v-if="$store.state.website_info.website_mode_category === 0"
          >
            <div
              @click="handleQrcode"
              id="qrcode"
              class="qrcode"
              ref="qrCodeUrl"
            ></div>
            <div class="qrcode-box">
              <div class="qr-t">下载H5链接二维码</div>
              <p
                class="qr-tips"
                style="color: #0083ff; cursor: pointer"
                @click="copyLink"
              >
                复制链接
              </p>
            </div>
          </div>
          <div class="tab-title" v-if="appQrcode">
            <el-popover placement="top" width="300" trigger="click">
              <img width="300px" height="300px" :src="appQrcode" alt="" />
              <img slot="reference" :src="appQrcode" class="qrcode" alt="" />
            </el-popover>
            <div class="qrcode-box">
              <div class="qr-t">小程序二维码</div>
              <p class="qr-tips">更多精彩</p>
            </div>
          </div>
          <div class="tab-title" v-if="!appQrcode">
            <el-popover placement="top" width="300" trigger="click">
              <img
                width="300px"
                height="300px"
                src="https://img.tfcs.cn/backup/static/admin/header/customer_service_qrcode_qy.jpg"
                alt=""
              />
              <img
                src="https://img.tfcs.cn/backup/static/admin/header/customer_service_qrcode_qy.jpg"
                class="qrcode"
                alt=""
                slot="reference"
              />
            </el-popover>

            <div class="qrcode-box" style="text-align: start">
              <div class="qr-t">小程序接入</div>
              <div class="lxjl">请联系专属客户经理</div>
              <p class="qr-tips">联系电话：13581811263</p>
            </div>
          </div>
        </div> -->
      </el-col>
    </el-row>
    <el-drawer :visible.sync="show_drawer" direction="rtl" @opened="handlerOpenDrawer">
      <div class="preview-box">
        <div style="display: block; margin: 0 auto" class="mobile"
          v-if="$store.state.website_info.website_mode_category == 0">
          <div class="top-header">
            <span>前台预览</span>
          </div>
          <div class="wx-header">
            <span>{{ $store.state.website_info.name }}</span>
            <img src="@/assets/webimg/Wechatsystemdefault@<EMAIL>" alt="" />
          </div>
          <div class="input-box">
            <span class="el-icon-search icon"></span>
            <input disabled type="text" placeholder="请输入楼盘名称" />
          </div>
          <div class="swiper" @click="onBannerPath(1)" v-if="$store.state.website_info.carousel != 2">
            <el-carousel indicator-position="none" height="150px" class="swiper-style" v-if="m_banner_data.length > 0">
              <el-carousel-item v-for="item in m_banner_data" :key="item.id">
                <img :src="item.img" alt="" />
              </el-carousel-item>
            </el-carousel>

            <div class="swiper-style" v-else>
              <img src="https://img.tfcs.cn/backup/static/admin/h5/default-header.png" alt="" />
            </div>
          </div>
          <div class="report" v-if="$store.state.website_info.button_style == 2">
            <div class="report-item" v-for="item in m_report_list" :key="item.index" :style="{
                            backgroundImage: `url(${item.icon}?x-oss-process=style/w_220)`,
                          }">
              <div class="report-font">{{ item.name }}</div>
            </div>
          </div>
          <div class="build_list" v-else>
            <div class="build-item" v-for="item in m_new_type" :key="item.id">
              <div class="img-box-status">
                <img :src="item.img" />
              </div>
              <div class="font">{{ item.name }}</div>
            </div>
          </div>
          <div class="build-title">
            <div class="t-l">
              {{ $store.state.website_info.build_menu_name || "推荐楼盘" }}
            </div>
            <div class="t-r">
              更多
              <img src="@/assets/webimg/更多@1x.png" alt="" />
            </div>
          </div>

          <myBuildItem v-if="build_list.length > 0" :list="build_list"
            :type="$store.state.website_info.build_menu_style || 1" @load="loadPage" @click="onBannerPath(2)"
            :loading="is_loading"></myBuildItem>
          <div v-else class="default-list">
            <img src="https://img.tfcs.cn/backup/static/admin/h5/list-default.png" alt="" />
          </div>
          <div class="tab-box">
            <img style="object-fit: none" src="	https://img.tfcs.cn/backup/static/admin/h5/tabbar.png" alt="" />
          </div>
        </div>
        <div style="display: block; margin: 0 auto" class="mobile"
          v-if="$store.state.website_info.website_mode_category == 1">
          <div class="top-header">
            <span>前台预览</span>
          </div>
          <div class="wx-header" style="justify-content: flex-end">
            <img src="@/assets/webimg/Wechatsystemdefault@<EMAIL>" alt="" />
          </div>
          <div class="dan-banner" v-if="$store.state.website_info.carousel != 2">
            <el-carousel indicator-position="none" height="272px" class="swiper-style" v-if="m_banner_data.length > 0">
              <el-carousel-item v-for="item in m_banner_data" :key="item.id">
                <img :src="item.img" alt="" />
              </el-carousel-item>
            </el-carousel>
            <div class="swiper-style" v-else>
              <img src="https://img.tfcs.cn/backup/static/admin/h5/default-header.png" alt="" />
            </div>
            <div class="d-content">
              <div class="d-top">
                <div class="build-title">
                  <div class="name">{{ dan_build_info.build_name }}</div>
                  <div class="ckfj">查看附件</div>
                </div>
                <div class="build-label-box">
                  <div class="label" v-for="(item, index) in build_category_label" :key="index">
                    {{ item }}
                  </div>
                </div>
                <div class="price-box">
                  <div class="left">
                    <span class="label-left">均价</span>
                    <div class="price">
                      {{ dan_build_info.build_avg_price + "元/㎡" || "--" }}
                    </div>
                  </div>
                  <div class="right">
                    <img class="fdjs" src="https://img.tfcs.cn/backup/static/admin/h5/d-fdjs.png" alt="" />
                    <span style="margin-left: 12rpx">房贷计算器</span>
                  </div>
                </div>
                <div class="address">
                  <div class="left">地址</div>
                  <div class="right">
                    {{ dan_build_info.build_address || "暂未更新" }}
                  </div>
                </div>
                <div class="address">
                  <div class="left">建筑面积</div>
                  <div class="right">{{ open_time || "暂未更新" }}</div>
                </div>
                <div class="address address-btn">
                  <div style="align-items: flex-end; display: flex">
                    <div class="left">开盘时间</div>
                    <div class="right">
                      {{ dan_build_info.newest_opening_time || "暂未更新" }}
                    </div>
                  </div>
                  <div v-if="dan_build_info.build_id" class="project-detail">
                    项目详情
                  </div>
                </div>
                <div class="avatars">
                  <div style="display: flex">
                    <img v-for="(item, index) in view_user_list" :key="index" class="pre_logo" :src="
                                            item.u_avatar ||
                                            'https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80'
                                          " /><img style="background: #fff" class="pre_logo"
                      src="https://img.tfcs.cn/backup/static/admin/h5/d-txsl.png" />
                  </div>
                  <div class="browse-count">共{{ view_user_count }}人浏览</div>
                </div>
              </div>
              <!-- 购房优惠券显示 -->
              <div class="coupon-box">
                <div class="coupon">
                  <div class="left">{{ dan_build_info.buy_coupon }}</div>
                  <div class="right-btn">立即领取</div>
                </div>
              </div>
              <div class="click-box">
                <div class="click-box-ctn">
                  <div class="click-box-left" :class="{ isrow: !is_setting.config_support_reported }" :style="{
                                        justifyContent: !is_setting.config_support_reported
                                          ? 'space-between'
                                          : '',
                                        width: !is_setting.config_support_reported ? '100%' : '',
                                      }">
                    <div class="left-top" :class="{
                                            clickBox3: !is_setting.config_support_reported,
                                          }" :style="{
                                              background: item.bg,
                                            }" v-for="item in click_box" :key="item.id">
                      <img class="icon-f" :src="item.icon" alt="" />
                      <span class="label-text">{{ item.desc }}</span>
                      <img class="icon-1" :src="item.img_path" />
                    </div>
                  </div>
                  <div class="click-box-right" v-if="is_setting.config_support_reported">
                    <span class="label-text">报备客户</span>
                    <img src="https://img.tfcs.cn/static/img/only_baobeikehu.png" />
                  </div>
                </div>
              </div>

              <div class="content-box-dan">
                <div class="title">
                  <div class="label">楼盘周边</div>
                  <div class="loadmore row">查看更多</div>
                </div>
                <img class="map" style="margin-top: 10px" src="https://img.tfcs.cn/backup/static/admin/h5/zhoubian.png"
                  alt="" />
                <div class="title">
                  <div class="label">楼盘相册</div>
                  <div class="loadmore row">查看更多</div>
                </div>
                <div class="photos-box" v-if="photos_list.length > 0">
                  <div class="photos-item" v-for="(item, index) in photos_list" :key="index">
                    <img :src="item.photos[0]" />
                    <div class="right-d">
                      <div class="title-d">{{ item.description }}</div>
                      <div class="bottom">共{{ item.photos.length }}张</div>
                    </div>
                  </div>
                </div>
                <div class="title">
                  <div class="label">楼盘动态</div>
                  <div class="loadmore row">查看更多</div>
                </div>
                <div class="dynamic-list bottom-line row" v-for="item in dynamic_list" :key="item.id">
                  <div class="left row">
                    <span class="title-p">{{ item.title }}</span>
                    <span class="time">{{ item.created_at }}</span>
                  </div>
                  <div class="right">
                    <img :src="item.img" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="tab-box">
            <img src="	https://img.tfcs.cn/backup/static/admin/h5/tabbar_dan.png" alt="" />
          </div>
        </div>
        <div class="code-box showBlock">
          <div class="tab-title" v-if="$store.state.website_info.website_mode_category === 0">
            <div @click="handleQrcode" id="qrcode" class="qrcode" ref="qrCodeUrl"></div>
            <div class="qrcode-box">
              <div class="qr-t">下载H5链接二维码</div>
              <p class="qr-tips" style="color: #0083ff; cursor: pointer" @click="copyLink">
                复制链接
              </p>
            </div>
          </div>
          <div class="tab-title" v-if="appQrcode">
            <el-popover placement="top" width="300" trigger="click">
              <img width="300px" height="300px" :src="appQrcode" alt="" />
              <img slot="reference" :src="appQrcode" class="qrcode" alt="" />
            </el-popover>
            <div class="qrcode-box">
              <div class="qr-t">小程序二维码</div>
              <p class="qr-tips">更多精彩</p>
            </div>
          </div>
          <div class="tab-title" v-if="!appQrcode">
            <el-popover placement="top" width="300" trigger="click">
              <img width="300px" height="300px"
                src="https://img.tfcs.cn/backup/static/admin/header/customer_service_qrcode_qy.jpg" alt="" />
              <img src="https://img.tfcs.cn/backup/static/admin/header/customer_service_qrcode_qy.jpg" class="qrcode"
                alt="" slot="reference" />
            </el-popover>

            <div class="qrcode-box" style="text-align: start">
              <div class="qr-t">小程序接入</div>
              <div class="lxjl">请联系专属客户经理</div>
              <p class="qr-tips">联系电话：13581811263</p>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import * as echarts from "echarts";
import myBuildItem from "@/components/mobile/new_build_list.vue";
export default {
  name: "web_overview",
  components: { myBuildItem },
  data() {
    return {
      total: [],
      arr: [],
      website_id: localStorage.getItem("website_id"),
      report_info: {},
      build_rank: [],
      appQrcode: "",
      user_rank: [],
      is_row_loading: true,
      companyRank_list: [],
      is_company_rank: false, // true：销售公司 false：经纪人
      m_banner_data: [],
      m_report_list: [
        {
          index: 1,
          name: "我的客户",
          icon:
            "https://img.tfcs.cn/backup/static/h5index/wdkh1.png",
          path: "/client/list?type=-1",
        },
        {
          index: 2,
          name: "快速报备",
          icon:
            "https://img.tfcs.cn/backup/static/h5index/ksbb1.png",
          path: "/report/report_client?currentTel=1",
        },
      ],
      m_new_type: [
        {
          id: 1,
          name: "我的客户",
          img:
            "https://img.tfcs.cn/backup/static/h5index/wdkh.png",
          path: "/client/list?type=-1",
        },
        {
          id: 2,
          name: "快速报备",
          img:
            "https://img.tfcs.cn/backup/static/h5index/ksbb.png",
          path: "/report/report_client?currentTel=1",
        },
        {
          id: 3,
          name: "佣金明细",
          img:
            "https://img.tfcs.cn/backup/static/h5index/yjmx.png",
          path: "/commission/commission_detail",
        },
        {
          id: 4,
          name: "推荐好友",
          img:
            "https://img.tfcs.cn/backup/static/h5index/tjhy.png",
        },
      ],
      build_list: [],
      is_loading: "",
      list_params: {
        page: 1,
      },
      dan_build_info: { build_avg_price: "" },
      build_category_label: [],
      open_time: "",
      view_user_list: [],
      view_user_count: "",
      is_setting: {
        config_support_im: 1,
        config_support_online_live: 1,
        config_support_reported: 1,
      },
      click_box: [],
      project_list: [],
      photos_list: [],
      dynamic_list: [],
      dynamic_params: { page: 1 },
      show_drawer: false, // 显示/隐藏抽屉模态框
      qrCodeCreated: false, // 限制二維碼觸發次數
    };
  },
  mounted() {
    this.getTotalData();
    this.getWebsiteMoneyInfo();
  },
  created() {
    this.$http.getWebsite(localStorage.getItem("website_id")).then((res) => {
      if (res.status === 200) {
        this.$store.commit("setWebsiteInfo", res.data);
        this.$store.state.website_info = res.data;
        // 储存系统是否开启crm
        localStorage.setItem("website_crm", res.data.is_open_crm);
        if (this.$envjudge() === 'com-wx-pc') {
          if (res.data.wx_work_expired_time) {
            if (
              // 时间对比到期菜单无法点击
              this.compare(this.getCurrentTime(), res.data.wx_work_expired_time)
            ) {
              // this.$store.state.is_qywx_expire = true
              // this.$store.commit('setExprie', true)
              // this.setExprie(true);
              // this.$message.warning("当前系统已过试用期，请联系官方进行操作");
            }
          }
        }
        // 获取字典数据
      }
    });

    this.getWebsiteUserRank();
    this.getWebsiteReportInfo();
    if (this.$store.state.website_info.website_mode_category !== 1) {
      this.getWebsiteBuildRank();
    }
    this.getCompanyRank();
    this.getMobileBannerData();
    this.getMobileListData();
    this.queryXiaoApp();
  },
  methods: {
    //复制H5链接
    copyLink() {
      let website_id = this.$store.state.website_info.website_id
      this.$onCopyValue(
        `https://yun.tfcs.cn/fenxiao/?website_id=${website_id}`
      )
    },
    //  获取当前日期用于对比七天试用期是否到期
    getCurrentTime() {
      const nowDate = new Date();
      const date = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        date: nowDate.getDate(),
        hours: nowDate.getHours(),
        minutes: nowDate.getMinutes(),
        seconds: nowDate.getSeconds(),
      };
      const newmonth = date.month > 10 ? date.month : "0" + date.month;
      const newday = date.date > 10 ? date.date : "0" + date.date;
      const newminutes = date.minutes > 10 ? date.minutes : "0" + date.minutes;
      const newseconds = date.seconds > 10 ? date.seconds : "0" + date.seconds;
      const newhours = date.hours > 10 ? date.hours : "0" + date.hours;
      //   const newminutes = date.minutes < 10 ? "0" + date.minutes : date.minutes;
      //   const newseconds = date.seconds < 10 ? "0" + date.seconds : date.seconds;

      let dateTime =
        date.year +
        "-" +
        newmonth +
        "-" +
        newday +
        " " +
        newhours +
        ":" +
        newminutes +
        ":" +
        newseconds;
      return dateTime;
    },

    //日期比较的方法
    compare(date1, date2) {
      let dates1 = new Date(date1);
      let dates2 = new Date(date2);
      if (dates1 > dates2) {
        return true;
      } else {
        return false;
      }
    },
    // 获取楼盘动态
    getDataDynamic() {
      if (this.dynamic_params.page === 1) {
        this.dynamic_list = [];
      }
      this.$http
        .getBuildDynamicData({ params: this.dynamic_params })
        .then((res) => {
          if (res.status === 200) {
            this.dynamic_list = this.dynamic_list.concat(res.data.data);
          }
        });
    },
    // 获取图片相册
    getPhotosList(build_id) {
      this.$http.getBuildPhotosData(build_id).then((res) => {
        if (res.status === 200) {
          this.photos_list.map((item, index) => {
            this.photos_list[index].photos = [];
            res.data.map((item1) => {
              if (item.value == item1.category) {
                this.photos_list[index].photos.push(item1.img);
              }
            });
          });
          this.photos_list = this.photos_list.slice(0, 4);
        }
      });
    },
    getMobileBannerData() {
      this.$http
        .getMobileBannerData({ params: { region_0: 0, region_1: 0 } })
        .then((res) => {
          if (res.status === 200) {
            this.m_banner_data = res.data;
          }
        });
    },
    getBuildByWebsiteId(id) {
      this.$http.getBuildByWebsiteId(id).then((res) => {
        if (res.status === 200) {
          if (!res.data.id) {
            return;
          }
          this.getBuildInfoByid(res.data.id);
          this.queryViewUser(res.data.id);
        }
      });
    },
    // 小程序配置
    getMiniProgramPrivateConfig(id) {
      this.$http.getMiniProgramPrivateConfig(id).then((res) => {
        if (res.status === 200) {
          this.is_setting = res.data || {
            config_support_im: 1,
            config_support_online_live: 1,
            config_support_reported: 0,
          };
        }
      });
    },
    // 查询浏览数
    queryViewUser(build_id) {
      this.$http.queryViewUser(build_id).then((res) => {
        if (res.status === 200) {
          this.view_user_list = res.data.data;
          this.view_user_count = res.data.total;
        }
      });
    },
    getBuildInfoByid(id) {
      this.$http.getBuildInfoByid(id).then((res) => {
        if (res.status === 200) {
          this.dan_build_info = res.data;
          if (!this.is_setting.config_support_reported) {
            this.click_box = [
              {
                icon:
                  "https://img.tfcs.cn/backup/static/admin/h5/d-bbkh.png",
                id: 1,
                icon_color: "#fff",
                desc: "开盘通知我",
                bg: "linear-gradient(90deg, #5EE89C 0%, #0DDD86 100%)",
                img_path: "https://img.tfcs.cn/static/img/kaipantongzhi.png",
              },
              {
                icon:
                  "https://img.tfcs.cn/backup/static/admin/h5/d-bbkh.png",
                id: 2,
                icon_color: "#fff",
                desc: "调价通知我",
                bg: "linear-gradient(90deg, #FFC042 0%, #FDB31E 100%)",
                img_path: "https://img.tfcs.cn/static/img/bianjiatongzhi.png",
              },
            ];
          } else {
            this.click_box = [
              {
                icon: "https://img.tfcs.cn/static/admin/h5/d-ppjs.png",
                id: 1,
                icon_color: "#FDB31E",
                desc: "品牌介绍",
                img_path: "https://img.tfcs.cn/static/img/pingpai.png",
              },
              {
                icon: "https://img.tfcs.cn/static/admin/h5/d-jdslc.png",
                id: 2,
                icon_color: "#28d28b",
                desc: "致电售楼处",
                img_path: "https://img.tfcs.cn/static/img/tel.png",
              },
            ];
          }
          this.open_time =
            res.data.build_min_area + " -" + res.data.build_max_area + "㎡";
          this.$setDictionary((e) => {
            e.find((item) => {
              switch (item.name) {
                case "BUILD_CATEGORY":
                  var build_category_list = item.childs;
                  var arr = res.data.build_category.split(",");
                  arr.map((item) => {
                    build_category_list.map((item1) => {
                      if (item == item1.value) {
                        this.build_category_label.push(item1.description);
                      }
                    });
                  });
                  break;
                case "BUILD_IMG_CATEGORY":
                  this.photos_list = item.childs;
                  this.getPhotosList(id);
                  break;
              }
            });
          });
        }
      });
    },
    getMobileListData() {
      this.is_loading = "loading";
      if (this.list_params.page === 1) {
        this.build_list = [];
      }
      this.$http.getMobileListData({ params: this.list_params }).then((res) => {
        if (res.status === 200) {
          this.build_list = this.build_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.is_loading = "nomore";
          }
        }
      });
    },
    getCompanyRank() {
      this.$http.setAdminCompanyRank().then((res) => {
        if (res.status === 200) {
          this.companyRank_list = res.data;
        }
      });
    },
    queryXiaoApp() {
      this.$http.queryXiaoApp().then((res) => {
        if (res.status === 200) {
          if (res.data.id && res.data.updated_at) {
            this.getQrCode();
          }
        }
      });
    },
    creatQrCode() {
      let url = `https://yun.tfcs.cn/fenxiao?website_id=${this.website_id}`;
      new QRCode(this.$refs.qrCodeUrl, {
        text: url, // 需要转换为二维码的内容
        width: 132,
        height: 132,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    getTotalData() {
      this.$http.getTotalData().then((res) => {
        this.is_row_loading = false;
        if (res.status === 200) {
          this.total = res.data;
        }
      });
    },
    getWebsiteReportInfo() {
      this.$http.getWebsiteReportInfo().then((res) => {
        if (res.status === 200) {
          this.report_info = res.data;
        }
      });
    },
    getWebsiteBuildRank() {
      this.$http.getWebsiteBuildRank().then((res) => {
        if (res.status === 200) {
          this.build_rank = res.data;
        }
      });
    },
    getWebsiteUserRank() {
      this.$http.getWebsiteUserRank().then((res) => {
        if (res.status === 200) {
          this.user_rank = res.data;
        }
      });
    },
    getWebsiteMoneyInfo() {
      this.$http.getWebsiteMoneyInfo().then((res) => {
        if (res.status === 200) {
          // setTimeout(() => {
          this.echartsInitMoney(res.data);
          // }, 1000)
        }
      });
    },
    getQrCode() {
      var path = "/index/index";
      let mode = this.$store.state.website_info.website_mode_category;
      if (mode == 0) {
        path = "/index/index";
      }
      if (mode == 1) {
        path = "/only_build/pages/index/index";
      }
      if (mode == 2) {
        path = "/weifangchan/pages/index/index";
      }
      this.$http.getAppQrcode({ path: path }).then((res) => {
        if (res.status === 200) {
          this.appQrcode = `data: image/jpeg;base64,${btoa(
            new Uint8Array(res.data).reduce(
              (data, byte) => data + String.fromCharCode(byte),
              ""
            )
          )}`;
        }
      });
    },
    echartsInitMoney(res) {
      // var chartDom = document.getElementById("chart-box");
      var chartDom = this.$refs.chartBox;
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          //设置tip提示
          trigger: "none",
          axisPointer: {
            type: "cross",
          },
        },
        lineStyle: {
          type: "solid",
        },
        grid: {
          left: "10px",
          bottom: "0",
          containLabel: true,
        },
        title: {
          text: "资金信息",
        },
        legend: {
          //设置区分（哪条线属于什么）
          data: ["已成交金额", "已成交佣金", "未完成佣金", "已发放佣金"],
        },
        color: ["#3D74FC", "#FEC94C", "#04D38A", "#FF655F"], //设置区分（每条线是什么颜色，和 legend 一一对应）
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: Object.keys(res.brokerage_amount),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "已成交金额",
            type: "line",
            smooth: true,
            data: Object.values(res.deal_amount),
          },
          {
            name: "已成交佣金",
            type: "line",
            smooth: true,
            data: Object.values(res.brokerage_amount),
          },
          {
            name: "未完成佣金",
            type: "line",
            smooth: true,
            data: Object.values(res.not_settle),
          },
          {
            name: "已发放佣金",
            type: "line",
            smooth: true,
            data: Object.values(res.settled),
          },
        ],
      };

      option && myChart.setOption(option);
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    loadPage() {
      if (this.is_loading === "nomore") {
        return;
      }
      this.list_params.page++;
      this.getMobileListData();
    },
    onBannerPath(type) {
      this.$confirm(
        `${type == 1 ? "是否修改首页轮播" : "是否修改推荐楼盘"} `,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$goPath(type == 1 ? "/adv_management?id=1" : "/property_list");
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    handleQrcode() {
      // //找到canvas标签
      let myCanvas = document
        .getElementById("qrcode")
        .getElementsByTagName("canvas");
      let img = document.getElementById("qrcode").getElementsByTagName("img");
      // // //创建一个a标签节点
      let a = document.createElement("a");
      // //设置a标签的href属性（将canvas变成png图片）
      let imgURL = myCanvas[0].toDataURL("image/jpg");
      let ua = navigator.userAgent;
      if (ua.indexOf("Trident") != -1 && ua.indexOf("Windows") != -1) {
        // IE内核 并且  windows系统 情况下 才执行;
        var bstr = atob(imgURL.split(",")[1]);
        var n = bstr.length;
        var u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        var blob = new Blob([u8arr]);
        window.navigator.msSaveOrOpenBlob(blob, this.params.name + "." + "png");
      } else if (ua.indexOf("Firefox") > -1) {
        //火狐兼容下载
        let blob = this.base64ToBlob(imgURL); //new Blob([content]);
        let evt = document.createEvent("HTMLEvents");
        evt.initEvent("click", true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
        a.download = " "; //下载图片名称，如果填内容识别不到，下载为未知文件，所以我这里就不填为空
        a.href = URL.createObjectURL(blob);
        a.dispatchEvent(
          new MouseEvent("click", {
            bubbles: true,
            cancelable: true,
            view: window,
          })
        ); //兼容火狐
      } else {
        //谷歌兼容下载
        img.src = myCanvas[0].toDataURL("image/jpg");
        // a.href = myCanvas[0].toDataURL('image/png').replace('image/png', 'image/octet-stream')
        a.href = img.src;
        //设置下载文件的名字
        a.download = `${this.$store.state.website_info.name}_二维码`;
        //点击
        a.click();
      }
    }, //base64转blob
    base64ToBlob(code) {
      let parts = code.split(";base64,");
      let contentType = parts[0].split(":")[1];
      let raw = window.atob(parts[1]);
      let rawLength = raw.length;
      let uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      return new Blob([uInt8Array], { type: contentType });
    },
    // 显示前台展示
    showFrontDesk() {
      this.show_drawer = true; // 显示抽屉模态框
    },
    // 打开抽屉模态框的回调
    handlerOpenDrawer() {
      if (!this.qrCodeCreated) {
        if (this.$store.state.website_info.website_mode_category == 0) {
          this.creatQrCode();
        }
        this.qrCodeCreated = true;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.tab-point {
  cursor: pointer;
}

.el-tabs--border-card>.el-tabs__content {
  // padding: 0;
}

.web-box {
  background: #f8faff;
  padding: 28px;
  margin: -15px;

  h3 {
    color: #1c212b;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 18px;
    line-height: 28px;
    letter-spacing: 0px;
    text-align: left;
    margin-bottom: 10px;
  }

  .tab-title {
    background: #fff;
    padding: 30px;
    border-radius: 5px;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px #0000000c;
    align-items: center;
    position: relative;

    .month {
      position: absolute;
      right: 30px;
      border-radius: 16px;
      background: #3c74fd;
      font-size: 14px;
      color: #fff;
      width: 54px;
      height: 28px;
      text-align: center;
      line-height: 28px;
    }

    .top-img {
      width: 60px;
      height: 60px;
    }

    .top-right {
      margin-left: 30px;

      .num {
        color: #1c212b;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 28px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: left;
      }

      .desc {
        color: #768196;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: left;
      }
    }

    .top-expansion {
      position: absolute;
      top: 30px;
      right: 30px;

      .el-button {
        background-color: #3c74fd;
        border-color: #3c74fd;
      }
    }
  }
}

.flex-row {
  display: flex;
}

.qrcode {
  display: inline-block;

  img {
    background-color: #fff; //设置白色背景色
    padding: 6px; // 利用padding的特性，挤出白边
    box-sizing: border-box;
  }
}

.el-row {
  margin-bottom: 20px;
  width: 100%;

  &:last-child {
    margin-bottom: 0;
  }
}

.content-box {
  align-items: center;
  width: 100%;

  .left-img {
    align-items: center;

    .num-box {
      position: relative;

      div {
        text-align: center;
        line-height: 47px;
        font-size: 13px;
        color: #fff;
      }

      .l-t {
        background-image: url("../../../assets/webimg/blue.png");
        height: 47px;
        background-repeat: no-repeat;
        width: 238px;
        margin-bottom: 4px;
      }

      .l-c {
        position: relative;
        background-image: url("../../../assets/webimg/red.png");
        height: 47px;
        background-repeat: no-repeat;
        width: 177px;
        margin-bottom: 4px;
      }

      .l-b {
        position: relative;
        background-image: url("../../../assets/webimg/huang.png");
        height: 47px;
        width: 113px;
        background-repeat: no-repeat;
      }

      p {
        position: absolute;
        top: 10px;
        right: -30px;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 13px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: left;
      }

      .p1 {
        color: #0083ff;
      }

      .p2 {
        color: #f95741;
      }

      .p3 {
        color: #ffb108;
      }
    }
  }

  .right-content {
    margin-left: 66px;
    width: 100%;

    .t {
      font-size: 13px;
      width: 100%;

      thead {
        color: #6b7283;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 13px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: left;

        th {
          text-align: center;
        }
      }

      tbody {
        margin-top: 16px;

        tr {
          th {
            color: #6b7283;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 13px;
            line-height: 28px;
            letter-spacing: 0px;
            text-align: right;
          }
        }

        td {
          padding: 8px 16px;
          color: #062047;
          font-family: PingFang SC;
          font-weight: semibold;
          font-size: 13px;
          line-height: 28px;
          letter-spacing: 0px;
          text-align: center;

          .name {
            width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .t {
    font-size: 13px;
    width: 100%;

    thead {
      color: #6b7283;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 13px;
      line-height: 28px;
      letter-spacing: 0px;
      text-align: left;

      th {
        text-align: center;
      }
    }

    tbody {
      margin-top: 16px;

      tr {
        th {
          color: #6b7283;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 13px;
          line-height: 28px;
          letter-spacing: 0px;
          text-align: right;
        }
      }

      td {
        padding: 8px 16px;
        color: #062047;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 13px;
        line-height: 28px;
        letter-spacing: 0px;
        text-align: center;

        .name {
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .t-build {
    thead {
      th {
        text-align: left;
      }
    }

    tbody {
      td {
        align-items: center;
        text-align: center;
        padding: 0;

        .name {
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .rank {
          margin-right: 6px;
          display: inline-block;
          width: 12px;
        }

        img {
          width: 12px;
          height: 17px;
          margin-right: 6px;
        }
      }

      .flex-row {
        text-align: left;
        padding-left: 0;
      }

      .t-t {
        p {
          margin-left: 20px;
          color: #6b7283;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 13px;
          line-height: 28px;
        }

        text-align: left;
      }
    }
  }
}

.site-box {
  font-size: 14px;

  .site {
    width: 470px;
    height: 44px;
    line-height: 44px;
    color: #3c74fd;
    font-family: PingFang SC;
    font-weight: medium;
    letter-spacing: 0px;
    border-radius: 5px;
    padding-left: 16px;
    background: #f6f7ff;
  }

  .btn {
    color: #fff;
    width: 90px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    margin-left: 22px;
    cursor: pointer;
    border-radius: 5px;
    background: #3c74fd;
  }

  .tips {
    color: #6b7283;
    font-family: PingFang SC;
    font-weight: regular;
    font-size: 13px;
    line-height: 28px;
    letter-spacing: 0px;
    text-align: left;
  }
}

.code-box {
  display: none;
  width: 375px;

  .tab-title {
    display: flex;
    margin-bottom: 20px;
    padding: 20px;
  }
}

.qrcode-box {
  text-align: center;
  margin-left: 24px;

  .qr-t {
    color: #062047;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 18px;
    line-height: 28px;
    letter-spacing: 0px;
  }

  .lxjl {
    color: #0083ff;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .qr-tips {
    color: #a0a0a0;
    font-family: PingFang SC;
    font-weight: regular;
    font-size: 13px;
    line-height: 19.5px;
    letter-spacing: 0px;
  }
}

::v-deep .qrcode {
  width: 85px;
  height: 85px;

  img {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 6px;
    box-sizing: border-box;
  }
}

.is-row-title {
  display: flex;
  align-items: baseline;
  justify-content: space-between;

  span {
    font-weight: normal;
    color: #6b7283;
    font-size: 14px;
    // margin-right: 50px;
  }
}

// 手机端样式
.mobile {
  display: none;
  box-sizing: border-box;
  min-height: 800px;
  background: #fff;
  border-radius: 22px;
  box-shadow: 0px 2px 4px 0px #0000000c;
  margin-bottom: 20px;
  padding: 24px;
  min-width: 375px;
  width: 375px;
  position: relative;

  .top-header {
    line-height: 44px;
    font-size: 12px;
    background-image: url("../../../assets/webimg/qiantai-top.png");
    height: 44px;
    background-size: 100%;
    background-repeat: no-repeat;
  }

  .wx-header {
    font-size: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 99;
    position: relative;

    img {
      width: 87px;
      background: #fff;
      border-radius: 30px;
      height: 32px;
    }
  }

  .input-box {
    margin: 12px 0;
    position: relative;

    input::-webkit-input-placeholder {
      position: relative;
      left: 40px;
    }

    .icon {
      position: absolute;
      color: #d1d1d1;
      line-height: 40px;
      left: 20px;
    }

    input {
      width: 100%;
      border-radius: 20px;
      background: #f3f3f3;
      -web-kit-appearance: none;
      -moz-appearance: none;
      border: none;
      outline: 0;
      height: 40px;
    }

    //此命令有效
  }

  .swiper {
    .swiper-style {
      border-radius: 5px;

      img {
        height: 100%;
        object-fit: cover;
        width: 100%;
      }
    }
  }

  .build_list {
    margin-top: 16px;
    margin-bottom: 18px;
    display: flex;
    justify-content: space-around;
    text-align: center;

    .build-swiper {
      width: 100%;
      height: 80px;
    }
  }

  .build-item {
    align-items: center;
  }

  .img-box-status {
    height: 46px;
    width: 46px;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .font {
    font-size: 12px;
    margin-top: 8px;
  }
}

.report {
  justify-content: space-between;
  color: #fff;
  font-size: 14px;
  display: flex;
  margin-top: 16px;
  margin-bottom: 18px;

  .report-item {
    width: 154px;
    height: 48px;
    line-height: 48px;
    border-radius: 10px;
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .report-font {
      position: absolute;
      right: 40px;
    }
  }
}

.build-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;

  .t-r {
    color: #6f6f6f;
    font-size: 12px;
  }
}

.tab-box {
  height: 86px;
  margin-left: -24px;
  margin-right: -24px;
  position: absolute;
  bottom: 0;

  img {
    width: 375px;
    border-radius: 20px;
    // height: 100%;
    // object-fit: none;
  }
}

.dan-banner::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.dan-banner {
  position: relative;
  top: -40px;
  margin-left: -24px;
  margin-right: -24px;
  max-height: 650px;
  overflow: auto;

  img {
    width: 100%;
  }

  .d-content {
    padding: 0 24px;
    position: relative;
    z-index: 999;
    top: -65px;

    .d-top {
      padding: 24px;
      background: #ffffff;
      border: 1px solid #f9f9f9;
      box-shadow: 1px 4px 12px 0px rgba(0, 0, 0, 0.02);
      border-radius: 8px;

      .build-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .name {
          font-size: 24px;
          font-weight: bold;
        }

        .ckfj {
          color: #9a9a9a;
        }
      }

      .build-label-box {
        display: flex;
        margin: 14px 0;
        flex: 1;
        flex-wrap: wrap;

        .label {
          margin: 4px;
          font-size: 12px;
          color: #9a9a9a;
          padding: 5px;
          border-radius: 2px;
          border: 1px solid #d8d8d8;

          &:first-child {
            margin-left: 0;
          }
        }
      }

      .price-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .left {
          display: flex;
          align-items: center;

          .label-left {
            color: #666;
            font-size: 12px;
            margin-right: 12px;
          }

          .price {
            line-height: 16px;
            font-size: 24px;
            color: #fb666a;
          }
        }

        .right {
          display: flex;
          align-items: center;
          color: #2e8cef;
          font-size: 14px;

          .fdjs {
            width: 14px;
            height: 16px;
            margin-right: 5px;
          }
        }
      }

      .address {
        margin: 11px 0 0;
        font-size: 12px;
        align-items: flex-end;
        display: flex;

        .left {
          color: #666;
          margin-right: 12px;
        }

        .right {
          font-size: 14px;
          font-weight: 500;
          color: #343434;
        }
      }

      .address-btn {
        align-items: flex-end;
        justify-content: space-between;

        .project-detail {
          align-items: center;
          font-size: 14px;
          color: #2e8cef;
        }
      }

      .avatars {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 23px;

        .pre_logo {
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }

        .pre_logo {
          margin-left: -7px;
        }

        .icon {
          background: #eaf3fd;
          z-index: 1;
        }

        .browse-count {
          font-size: 12px;
          color: #9a9a9a;
        }
      }
    }

    .coupon-box {
      margin-top: 24px;

      .coupon {
        display: flex;
        height: 80px;
        width: 100%;
        background-image: url("https://img.tfcs.cn/static/img/only_build_youhui.png");
        background-size: 100% 100%;
        align-items: center;
        justify-content: space-between;
        color: #fff;

        .left {
          width: 200px;
          margin-left: 24px;
          font-size: 16px;
          font-weight: bold;
          line-height: 25px;
        }
      }
    }

    .right-btn {
      margin-right: 20px;
      height: 22px;
      background: #ffffff;
      border-radius: 11px;
      color: #f65e17;
      font-size: 12px;
      line-height: 22px;
      padding: 0 5px;
    }

    .click-box {
      display: flex;
      justify-content: space-between;

      .click-box-ctn {
        color: #fff;
        font-size: 16px;
        width: 100%;
        display: flex;

        .click-box-left {
          .left-top {
            margin-top: 10px;
            position: relative;
            width: 191px;
            height: 49px;
            background: linear-gradient(90deg, #ffc042 0%, #fdb31e 100%);
            box-shadow: 0px 2px 6px 0px rgba(253, 180, 32, 0.04);
            border-radius: 4px;
            overflow: hidden;
            align-items: center;
            display: flex;

            .icon-f {
              width: 20px;
              margin-left: 10px;
              height: 20px;
            }

            .icon-1 {
              position: absolute;
              transform: rotate(-20deg);
              bottom: -5px;
              right: 20px;
              width: 46px;
              height: 47px;
              opacity: 0.5;
            }

            .label-text {
              margin-left: 11px;
            }

            &:last-child {
              background: linear-gradient(90deg, #5ee89c 0%, #0ddd86 100%);
              box-shadow: 0px 4px 12px 0px rgba(15, 221, 134, 0.04);
            }
          }

          .clickBox3 {
            width: 150px;

            img {
              right: -5px;
            }
          }
        }

        .isrow {
          display: flex;
        }

        .click-box-right {
          display: flex;
          margin-top: 10px;
          margin-left: 24px;
          width: 112px;
          height: 108px;
          background: linear-gradient(90deg, #62b1fb 0%, #6d8dfd 100%);
          box-shadow: 0px 2px 6px 0px rgba(103, 161, 252, 0.06);
          border-radius: 4px;
          justify-content: center;
          overflow: hidden;
          position: relative;

          img {
            position: absolute;
            width: 52px;
            height: 60px;
            opacity: 0.16;
            transform: rotate(-20deg);
            bottom: 0;
            right: 0;
          }

          .label-text {
            margin-left: 24px;
            margin-top: 12px;
          }
        }
      }
    }

    .content-box-dan {
      .title {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        align-items: center;

        .label {
          font-size: 18px;
          font-weight: 500;
        }

        .loadmore {
          color: #9a9a9a;
          font-size: 12px;
        }
      }
    }

    .photos-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .photos-item {
        display: flex;
        margin-top: 23px;
        align-items: center;
        width: 152px;
        height: 52px;
        background: #ffffff;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
        border-radius: 4px;

        img {
          width: 72px;
          height: 52px;
          border-radius: 4px;
        }

        .right-d {
          margin-left: 10px;

          .title-d {
            font-size: 14px;
            color: #333;
          }

          .bottom {
            margin-top: 10px;
            color: #999;
            font-size: 12px;
          }
        }
      }
    }

    .dynamic-list {
      display: flex;
      justify-content: space-between;
      padding: 23px 0;
      font-weight: 500;

      .left {
        flex-direction: column;
        justify-content: space-between;
        line-height: 24px;

        .title-p {
          align-items: flex-start;
          font-size: 18px;
          font-weight: 500;
          color: #343434;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }

        .time {
          font-weight: 500;
          color: #9a9a9a;
        }
      }

      .right {
        width: 90px;
        height: 68px;

        img {
          width: 90px;
          height: 68px;
        }
      }
    }
  }
}

.default-list {
  width: 327px;
  height: 400px;
  overflow: auto;
  margin-top: 20px;
  margin-bottom: 40px;

  img {
    width: 327px;
    height: 100%;
  }
}

.default-list::-webkit-scrollbar {
  display: none;
}

.preview-box {
  overflow-y: auto;
  height: 840px;
}

::v-deep .el-drawer {
  background-color: #f8faff;
}

.showBlock {
  display: block;
  margin: 0 auto;
  margin-top: 20px;
}
</style>
