<template>
  <div>
    <div class="message_container">
      <div class="tabs">
        <!-- <div
            class="item"
            :class="{ active: message_type === 'chat' }"
            @click="onClickTab('chat')"
          >
            聊天消息
          </div> -->
        <div class="item" :class="{ active: message_type === 'notice' }" @click="onClickTab('notice')">
          系统提醒
        </div>
      </div>
      <!-- 聊天消息 -->
      <div class="chat_list" v-show="message_type === 'chat'">
        <div class="item" v-for="(item, index) in defaultSystemIMList" :key="index" @click="toIm(item.id)">
          <div class="avatar_box">
            <img class="avatar" :src="item.from_user.avatar" alt="" />
            <!-- <span class="badge">{{item.content.dialog_id}}</span> -->
          </div>
          <div class="info">
            <div class="user">
              <div class="name">{{item.from_user.name}}</div>
              <div class="text">{{item.content | chatType}}</div>
            </div>
            <span class="time">{{item.created_at}}</span>
          </div>
        </div>
        <div v-if="defaultSystemIMList.length == 0" class="no_message">
          <img src="https://img.tfcs.cn/backup/static/admin/house/<EMAIL>" alt="">
          <p>暂无消息</p>
        </div>
      </div>
      <!-- 系统提醒 -->
      <div class="notice_list" v-show="message_type === 'notice'">
        <div class="item" v-for="(item, index) in defaultSystemList" :key="index" @click="showNoticeDetail(item.id)">
          <div class="title">{{item.content}}</div>
          <div class="from">
            <!-- <span v-if="item.cc_user[0]">来自：{{item.cc_user[0].user_name}}</span> -->
            <span :class="item.cc_user.length !== 0 ? 'personnel' : ''">
              <span v-for="(list, idx) in item.cc_user" in :key="list.id">
                {{ list.user_name }}{{ idx != item.cc_user.length -1 ? ',' : '' }}
              </span>
            </span>
            <span class="time">{{item.created_at}}</span>
          </div>
        </div>
        <div v-if="defaultSystemList.length == 0" class="no_message">
          <img src="https://img.tfcs.cn/backup/static/admin/house/<EMAIL>" alt="">
          <p>暂无消息</p>
        </div>
      </div>
      <div class="more" v-if="message_type==='chat'">
        <el-link type="primary" @click="toIm()">查看更多</el-link>
      </div>
      <div class="more" v-if="message_type==='notice'">
        <el-link type="primary" @click="toNotice()">查看更多</el-link>
      </div>
    </div>
    <el-dialog title="消息详情" :visible.sync="dialogVisible" append-to-body width="32%">
      <p v-html="notice_detail"></p>
    </el-dialog>
  </div>
</template>
  
<script>
  export default {
    name: "MessageList",
    components: {},
    data() {
      return {
        message_type: "notice",
        dialogVisible: false,
        notice_detail: "",
        // 系统提醒接口参数
        reminder_params: {
          page: 1,
          per_page: 10,
          type: 4, // 类型(1:最新,2:我收到的,3我发起的,4:历史)
        },
        defaultSystemList: [], // 三条未读系统消息容器
        defaultSystemIMList: [], // 三条未读的聊天消息容器
        is_bind: true, // 是否绑定前台用户
      };
    },
    props: {
      chat_list: {
        type: Array,
        default: ()=>{
          return []
        }
      },
      notice_list: {
        type: Array,
        default: ()=>{
          return []
        }
      },
      chat_total:{
        type: Number,
        default: 0
      },
      notice_total:{
        type: Number,
        default: 0
      }
    },
    filters: {
      chatType(value){
        switch(value.type){
          case 'text':
              return value.content
          case 'image':
              return "[图片]"
          case 'map':
              return "[位置]"
          case 'voice':
              return "[语音]"
          case 'build':
              return "[楼盘]"
          case 'ershou':
              return "[二手房]"
          case 'renting':
              return "[出租房]"
          case 'wechat':
              return "[微信名片]"
          case 'apply_wx':
              return "[申请查看微信名片]"
          case 'tel':
              return "[手机号码]"
          case 'apply_tel':
              return "[申请查看手机号]"
          case '':
              return ""
          default:
              return "[不支持的消息类型]"
      }
      }
    },
    created() {
      this.getSystemUnreadReminder();
      // this.getIMWhetherBind(); // 获取当前用户是否绑定前台用户
    },
    mounted(){
    },
    methods: {
      // 获取当前用户是否绑定前台用户
      getIMWhetherBind() {
        this.$http.getIMWhetherBind().then(res => {
          if(res.status == 200) {
            this.getThreeNewMessage(); // 获取IM三条未阅读消息
            this.is_bind = true; // 已绑定前台用户
          } else {
            this.is_bind = false; // 未绑定前台用户
          }
        }).catch(() => {
          this.is_bind = false;
        })
      },
      // 获取最新系统提示三条未阅读的消息
      getSystemUnreadReminder() {
        this.$http.getSystemUnreadReminder().then((res) => {
          if(res.status == 200) {
            this.defaultSystemList = res.data;
          }
        })
      },
      // 获取最新IM三条未阅读的消息
      getThreeNewMessage() {
        this.$http.getThreeNewMessage().then((res) => {
          if(res.status == 200) {
            this.defaultSystemIMList = res.data;
            // console.log(this.defaultSystemIMList,"this.defaultSystemIMList")
          }
        })
      },
      // 点击tabs切换 
      onClickTab(type) {
        this.message_type = type;
      },
      toIm(){
        // 如果绑定了前台用户
        if(this.is_bind) {
          this.$goPath("/Im");
          this.$emit('onclickMessage');
        } else {
          this.$message.warning("当前管理员未绑定前台用户，请联系创始人进行绑定。");
        }
        // if(chat_id){
        //   this.$router.push({
        //     path: "/im",
        //     query: {
        //       chat_id: chat_id
        //     }
        //   })
        // }else{
        //   this.$router.push({
        //     path: "/im"
        //   })
        // }
        // this.$emit('onclickMessage')
      },
      toNotice(){
        this.$goPath("/notice")
        this.$emit('onclickMessage')
      },
      showNoticeDetail(id){
        console.log(id);
        // this.$emit('onclickMessage')
        // this.$userApi.noticeDetail(id).then(res=>{
        //   if(res.data.code == 1){
        //       this.notice_detail = res.data.content
        //       this.dialogVisible = true
        //       eventBus.$emit("getNoticeUnread")
        //   }else{
        //       this.$message.error(res.data.msg)
        //   }
        // }).catch(err=>{
        //   console.log(err)
        //   this.$message.error('获取数据失败请重试')
        // })
      },
    },
  };
  </script>
  
<style scoped lang="scss">
.message_container {
  width: 374px;
  overflow-y: auto;

  .tabs {
    display: flex;
    border-bottom: 1px solid #dde1e9;

    .item {
      flex: 1;
      padding: 22px;
      text-align: center;
      background-color: #f1f4fa;
      cursor: pointer;
      transition: 0.36s;

      &.active {
        color: #2d84fb;
        background-color: #fff;
      }
    }
  }
}

.chat_list {
  min-height: 300px;
  background-color: #fff;

  .item {
    padding: 24px;
    display: flex;
    cursor: pointer;
    transition: 0.36s;
    border-bottom: 1px solid #dde1e9;

    &:last-child {
      border: none;
    }

    // &:hover{
    //   background-color: #f1f4fa;
    // }
    .avatar_box {
      position: relative;
      width: 56px;
      height: 56px;
      margin-right: 12px;

      .badge {
        position: absolute;
        top: -3px;
        right: -3px;
        background-color: #ff6d29;
        border-radius: 10px;
        color: #fff;
        display: inline-block;
        font-size: 12px;
        height: 18px;
        min-width: 18px;
        padding: 0 6px;
        box-sizing: border-box;
        line-height: 18px;
        text-align: center;
        white-space: nowrap;
        border: 1px solid #fff;
      }
    }

    .avatar {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      object-fit: cover;
      background-color: #dde1e9;
    }

    .info {
      flex: 1;
      display: flex;
      position: relative;

      .user {
        flex: 1;
        overflow: hidden;

        .name {
          margin-bottom: 12px;
          font-size: 16px;
          font-weight: bold;
          color: #2e3c4e;
        }

        .text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          color: #8a929f;
        }
      }

      .time {
        font-size: 12px;
        color: #8a929f;
      }
    }
  }
}

.more {
  // margin-top: 50px;
  padding: 16px;
  color: #2d84fb;
  background-color: #f1f4fa;
  text-align: center;
}

.notice_list {
  min-height: 300px;

  .item {
    padding: 24px;
    position: relative;
    cursor: pointer;
    transition: 0.36s;
    border-bottom: 1px solid #dde1e9;

    &:last-child {
      border: none;
    }

    // &:hover{
    //   background-color: #f1f4fa;
    // }
    .title {
      margin-bottom: 12px;
      font-size: 14px;
      color: #2e3c4e;
    }

    .from {
      font-size: 12px;
      color: #8a929f;

      .time {
        display: inline-block;
        max-width: 120px;
      }

      .personnel {
        margin-right: 24px;
      }
    }
  }
}

.no_message {
  // margin-top: 48px;
  padding: 32px 24px;
  text-align: center;
  color: #8A929F;

  >img {
    width: 64px;
    height: 64px;
  }
}</style>
  