<template>
<div v-fixed-scroll="48">
	<div class="room-info" v-if="isRoomId">
		<div class="room-info-item">
			<template  v-if="curRoom.room_id">
				<span class="title">{{curRoom.title}}</span>
				<small class="name" v-if="curRoom.name">（{{curRoom.name}}）</small>
				<span class="live-status" :class="['live-status'+curRoomLiveStatusValue]">{{curRoomLiveStatusLabel}}</span>
			</template>
			<span class="title disabled" v-else>直播间主题暂未同步</span>
		</div>
		<div class="room-info-item">
			<span class="label">直播地址：</span>
			<span class="value">
				<el-link type="primary" :underline="false" v-if="curRoom.pc_url" @click="copyText(curRoom.pc_url)">复制链接</el-link>
				<span v-else>暂无</span>
			</span>
		</div>
		<div class="room-info-item">
			<span class="label">直播时间：</span>
			<template v-if="curRoom.room_id">
				<span class="value">{{curRoom.start_time}}</span>
				<span class="value separator">至</span>
				<span class="value">{{curRoom.end_time}}</span>
			</template>
			<span v-else>暂无</span>
		</div>
	</div>
	<div class="filter-wrapper">
		<el-form inline size="medium">
			<el-form-item label="直播间">
				<el-select v-model="params.room_id" clearable placeholder="全部直播间" @change="clearsearch"
				 filterable>
					<el-option v-for="(item,index) in roomList" :key="index" :label="item.name+item.title" :value="item.room_id">
						<span>{{item.title}}</span><small class="option-room-name">{{item.name}}</small>
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="直播帐号" v-if="!isRoomId">
				<el-select v-model="params.ies_uid_from" clearable placeholder="选择直播帐号" @change="search" filterable>
					<el-option v-for="item in roomAccountList" :key="item.ies_uid" :label="item.name+item.ies_uniq_id" :value="item.ies_uid">
						<span>{{item.name}}</span><small class="option-room-name">{{item.ies_uniq_id}}</small>
					</el-option>
				</el-select>
			</el-form-item>
			
			<el-form-item label="昵称">
				<el-input placeholder="请输入" v-model="params.nick_name"></el-input>
			</el-form-item>
			<el-form-item label="抖音号">
				<el-input placeholder="请输入" v-model="params.ies_uniq_id"></el-input>
			</el-form-item>
			
			<el-form-item label="手机号">
				<el-input placeholder="请输入" v-model="params.mobile"></el-input>
			</el-form-item>
			<el-form-item label="号码状态" v-if="displayHasMobieFilter">
				<el-select v-model="params.has_mobile" placeholder="" @change="search">
					<el-option label="全部" value=""></el-option>
					<el-option label="已有号码" value="1"></el-option>
					<el-option label="未有号码" value="2"></el-option>
				</el-select>
			</el-form-item>

			<template v-if="isRoomId">
				<el-form-item label="省份">
					<el-input placeholder="请输入" v-model="params.province_name"></el-input>
				</el-form-item>
				<el-form-item label="城市">
					<el-input placeholder="请输入" v-model="params.city_name"></el-input>
				</el-form-item>
			</template>

			<el-form-item label="标记状态">
				<el-select v-model="params.user_status" clearable placeholder="选择标记状态" @change="search">
					<el-option v-for="item in userStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="同步状态">
				<el-select v-model="params.dy_syn" clearable placeholder="选择同步状态" @change="search">
					<el-option label="抖音已同步" :value="1"></el-option>
					<el-option label="抖音未同步" :value="0"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="互动场次">
				<el-input placeholder="请输入" v-model="params.room_number" type="number"></el-input>
			</el-form-item>
			<el-form-item label="留资状态"  v-if="params.room_id||(!params.room_id&&params.fill==1)">
				<el-select v-model="params.has_follow" clearable placeholder="选择留资状态" @change="search">
					<el-option label="全部" :value="0"></el-option>
					<el-option label="已有维护人" :value="1"></el-option>
					<el-option label="暂无维护人" :value="2"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="">
				<el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
			</el-form-item>
			<!-- <el-form-item label="">
				<el-button size="small" style="width: 103px;height: 33px;" type="primary">导入抖音线索
                        <input ref="excel-upload-input" class="excel-upload-input tablefile" type="file" accept=".xlsx, .xls" @change="getFileData">
                    </el-button>
			</el-form-item> -->
		</el-form>
	</div>
	<div class="operatebtn">
		<el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
            <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="TransferCustomer(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" style="margin-right:10px;" @click="getCrmDepartmentList" 
		       class="search_loudong div row align-center">
                <div class="seach_value">操作</div>
                <div class="sanjiao" :class="{ transt: transfer_type }"></div>
            </div>
        </el-popover>
	</div>
	<roomUserListTable  @child-event="handleChildEvent" :params="params" :list="list" @roomChange="handleRoomChange" :loading="loading"
	:liuzi="liuzi"></roomUserListTable>
	
	<div class="tab-content-footer">
		<div>
			<el-radio-group v-model="params.fill" @change="searchlz" size="small">
				<el-radio-button label="0">未留资</el-radio-button>
				<el-radio-button label="1">已留资</el-radio-button>
				<el-radio-button label="2" v-if="params.room_id">之前留资</el-radio-button>
			</el-radio-group>
		</div>
		<el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="count"
		:page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
		@current-change="onPageChange" @size-change="handleSizeChange">
		</el-pagination>
	</div>
	<transmitassembly ref="transmitassembly" @getDataList="search"></transmitassembly>
	<automatic ref="automatic" @getDataList="search"></automatic>
</div>
</template>
<script>
import roomUserListTable from "./roomUserListTable";
import transmitassembly from "@/views/crm/live_room/components/transmitassembly.vue"
import automatic  from '@/views/crm/share_follower/automatic_allocation.vue'
export default {
	components: {
		roomUserListTable,
		transmitassembly,
		automatic
	},
	props: {
		roomId: { type: [Number, String], default: '' } 
	},
	provide() {
        return {
            getUserStatusList: this.getUserStatusList,  
        }
    },
	data() {
		return {
			loading: false,
			params: {
				page: 1,
				per_page: 10,
				room_id: '',
				nick_name: '',
				ies_uniq_id: '',
				ies_uid_from: '',
				mobile: '',
				fill: '0',
				province_name: '',
				city_name: '',
				user_status: '',
				room_number: '',  //互动场次
				dy_syn: 1,			//抖音号是否已同步
				has_mobile: ''
			},
			roomList: [],			//直播间列表
			roomAccountList: [],    //直播间账号列表
			userStatusList: [],  //用户标记状态列表
			count: 0,
			list: [],
			roomid:false,
			transfer_type: false, // 显示/隐藏转交类型
			// 转交类型
			cus_list: [
        		{ id: 1, name: "转交到同事" },
				{ id: 2, name: "复制到同事的流转客" },
				{id: 3, name: "批量自动分配"}
      		],
			datalist:[],
			multipleSelection:[], 
			liuzi:false,
			files:{},
			website_id:"",
		}
	},
	computed: {
		//号码状态搜索
		displayHasMobieFilter(){
			return this.isRoomId && this.params.fill == 0;
		},
		//是否指定直播间
		isRoomId(){
			return this.params.room_id ? true : false
		},
		curRoom(){
			return this.isRoomId ? this.roomList.find(e=> e.room_id === this.params.room_id) || {} : {}
		},
		curRoomLiveStatusValue(){
			if(!this.isRoomId) return '';
			let now = new Date().getTime(), 
				stime = new Date(this.curRoom.start_time).getTime(),
				etime = new Date(this.curRoom.end_time).getTime();

			//已结束
			if(etime <= now){
				return 2;
			}else if(etime > now && stime <= now){ //进行中
				return 1;
			}else{ //未开始
				return 0
			}
		},
		curRoomLiveStatusLabel(){
			switch(this.curRoomLiveStatusValue){
				case 0:
					return '未开始';
				case 1: 
					return '进行中';
				case 2: 
					return '已结束'
				default:
					return '';
			}
		},
  	},
	watch: {
		roomId(val){
			this.params.room_id = val;
		},
		'params.room_id'(val){
			val != this.roomId && this.handleRoomChange(val)
		},
		"params.fill"(val){
			if(val==1){
				this.liuzi = true
			}else{
				this.liuzi = false
			}
		},
	},
	mounted(){
		this.website_id = this.$route.query.website_id;
		let pagenum = localStorage.getItem( 'pagenum')
    	this.params.per_page = Number(pagenum)||10
		this.params.room_id = this.roomId
		this.getList();
		this.getCondition();
		this.getDouyinRooms();
	},
	methods: {
		//获取直播用户搜索项
		async getCondition(){
			const res = await this.$http.getLiveRoomUserCondition();
			if(res.status == 200){
				this.roomAccountList = res.data?.account_list || [];

				const obj = res.data?.user_status_list || {}
				this.userStatusList = Object.keys(obj).map(e=>(
					{label: obj[e], value: e * 1}
				))
			}
		},
		//获取所有直播间
		async getDouyinRooms(){
			const res = await this.$http.getDouyinRooms();
			if(res.status == 200){
				this.roomList = res.data || []
			}
		},
		async getList(){
			if(!this.isRoomId){
				this.params.province_name = '';
				this.params.city_name = '';
			}
			const params = {...this.params};
			if(!this.displayHasMobieFilter){
				delete params.has_mobile
			}
			
			this.loading = true;
			const res = await this.$http[this.isRoomId ? 'getLiveRoomUserList' : 'getAllLiveRoomUserList'](params).catch(()=>{});
			this.loading = false;
			if(res.status == 200){
				this.count = res.data?.total || 0;
				this.list = res.data?.data || [];
			}
		},
		getCrmDepartmentList() {
			// this.transfer_type = true
		},
		//选中的人员
		handleChildEvent(data){
			this.multipleSelection = data
		},
		// 转交客户
		TransferCustomer(item) {
    	    // 判断是否选中客户
    	    if (!this.multipleSelection.length) {
    	    return this.$message({
    	      message: "请选择客户",
    	      type: "warning",
    	    });
    	  }
    	  if(this.multipleSelection.length&&this.multipleSelection.length>50){
    	    return this.$message({
    	      message: "每次最多选择50个客户",
    	      type: "warning",
    	    });
    	  }
		  const nonZeroClientIdData = this.multipleSelection.filter(item => item.client_id !== 0);
          const clientIds = nonZeroClientIdData.map(item => item.client_id);
		  if(!clientIds.length){
			return this.$message.warning("没有关联的客户")
		  }

		  if(item.id==3){
			return this.$refs.automatic.open(clientIds)
		  }
		  this.$refs.transmitassembly.open(this.multipleSelection ,item)
    	},
		clearsearch(){
			if(!this.params.room_id){
				this.params.fill = "0"
				this.params.has_follow =""
			}
			this.params.page = 1;
			this.getList();
		},
		searchlz(){
			if(this.params.fill==0){
				this.params.has_follow =""
			}
			this.params.page = 1;
			this.getList();
		},
		search(){
			this.params.page = 1;
			this.getList();
		},
		//选择需要上传导入的表格
		getFileData(e){
            this.files = e.target.files[0];
			if(this.files) {
				this.confirmimport()
			}
        },
		//导入
		confirmimport(){
			const loading = this.$loading({
        	  lock: true,
        	  text: '导入中....',
        	  spinner: 'el-icon-loading',
        	  background: 'rgba(0, 0, 0, 0.7)'
        	});
			const formData = new FormData()
                formData.append('file',this.files)
			this.$http.livebroadcastimport(formData).then(res=>{
				if(res.status==200){
					loading.close();
					this.$message.success("导入成功！")	
				}
			})
			
		},
		handleRoomChange(room_id){
			this.$emit('room-change', room_id)
		},
		copyText(txt){
			this.$onCopyValue(txt);
		},
		getUserStatusList(){
			return this.userStatusList;
		},
		onPageChange(e){
			this.params.page = e;
			this.getList();
		},
		handleSizeChange(e){
			this.params.per_page = e;
			this.search();
		}
	}
}
</script>
<style  scoped lang="scss">
.room-info{
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: center;
	padding: 0 16px 16px;
	border-bottom: 1px solid #f0f0f0;
	.room-info-item{
		position: relative;
		display: inline-flex;
		height: 30px;
		align-items: center;
		color: #606266;
		font-size: 15px;
		padding: 0 24px;
		&:first-child{
			padding-left: 0;
		}
		+.room-info-item::before{
			content: " ";
			position: absolute;
			display: inline-block;
			width: 1px;
			height: 14px;
			left: 0;
			top: 8px;
			background-color: #e0e0e0;
		}
		.separator{
			padding: 0 12px;
		}
	}
	.el-link{
		font-size: 15px;
	}
	.title{
		color: #333;
		font-size: 18px;
		&.disabled{
			color: #aaa;
		}
	}
	.live-status{
		display: inline-block;
		position: relative;
		color:#a9a9a9;
		padding-left: 10px;
		margin-left: 10px;
		&::before{
			content: " ";
			display: inline-block;
			width: 6px;
			height: 6px;
			border-radius: 50%;
			background-color: #a9a9a9;
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
		}
		&.live-status1{
			color:#409EFF;
			&::before{
				background-color:#409EFF
			}
		}
		&.live-status2{
			color:#ed3b3b;
			&::before{
				background-color:#ed3b3b
			}
		}
	}
}
.filter-wrapper{
	padding: 16px 16px 12px;
	.tablefile{
            float: left;
            opacity: 0;
            width: 84px;
            margin-top: -19px
        }
}
::v-deep{
	.option-room-name{
		color: #a9a9a9;
		margin-left: 12px;
		float: right;
	}
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
.operatebtn{
	width: 100%;
	display: flex;
	justify-content: flex-end;
	margin-bottom: 10px;
}
.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}
.search_loudong {
  width: 70px;
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 30px;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
</style>