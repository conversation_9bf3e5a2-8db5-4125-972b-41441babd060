<template>
    <el-select v-model="selectVal"  v-bind="$attrs" :remote="remote" :remote-method="getData"
        :loading = "loading" @focus="onFocus" @change="onChange"
    >
        <el-option
            v-for="item in formatOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        ><slot v-bind="item"></slot></el-option>
    </el-select>
</template>
<script>

export default {
    name: 'admTable',
    emits: ['input', 'change'],
    data(){
        return {
            loading: false,
            selectVal: '',
            remote: false,
            options: [],
            alais: {},
            keywordEmptyOptions: null,
            isLoaded: false
        }
    },
    props: {
        value: {type: [String, Number, Array], default: ''},
        data: { type: Array, default: () => [] },
        api: { type: String, default: '' },
        remoteFilter: { type: Boolean, default: true },
        props: { type: String, default: '' },
        params: { type: Object, default: null },
        lazy: { type: <PERSON>ole<PERSON>, default: true },
    },
    watch: {
        data: {
            handler(val){
                this.options = val;
                this.keywordEmptyOptions = val && val.length ? val : null;
            },
            immediate: true
        },
        value: {
            handler(val){
                this.selectVal = val;
            },
            immediate: true
        }
    },
    created(){
        if(this.props){
            this.alais = this.parseObjString(this.props)
        }
        if(this.api){
            if(!this.remoteFilter || !this.lazy){
                this.isLoaded = true;
                this.getData();
            }
            this.remote = this.remoteFilter;
        }
    },
    computed: {
        formatOptions(){
            return this.options.map(ops => {
                if(this.alais){
                    if(this.alais.label){
                        ops.label = ops[this.alais.label]
                    }
                    if(this.alais.value){
                        ops.value = ops[this.alais.value]
                    }
                }
                return ops
            })
        }
    },
    methods: {
        async getData(keyword = ''){
            try{
                this.loading = true;    
                let params = Object.assign({per_page: 10}, this.params);
                params[this.alais && this.alais.keyword || 'keyword'] = keyword;

                let res = await this.$http[this.api]({params});
                this.loading = false;
                if(res.status ==  200){
                    res = this.parseResult(res.data);
                    this.options = res.list || res || [];
                }
            }catch(e){
                this.loading = false;
                console.error(e);
            }
        },
        onFocus(){
            if(!this.isLoaded){
                this.initRemoteData();
            }
        },
        onChange(val){
            this.$emit('input', val)
            this.$emit('change', val)
        },
        async initRemoteData(){
            if(!this.remote){
                return;
            }
            if(this.selectVal === ''){
                if(this.keywordEmptyOptions){
                    this.options = this.keywordEmptyOptions;
                }else{
                    await this.getData();
                    this.keywordEmptyOptions = Object.assign([], this.options);
                }
            }
        },
        parseResult(res){
            if(this.alais && this.alais.list){
                res.list =  this.parseObjJoinedKeys(res, this.alais.list)
            }else if(!res.list && res.data){
                res.list = res.data
            }
            return res;
        },
        parseObjJoinedKeys(obj, keys){
            for(let key of keys.split('.')){
                obj = obj[key] || null;
                if(!obj){
                    break;
                }
            }
            return obj;
        },
        parseObjString(str){
            str = str.trim().slice(1,-1);
            str = ','+str+',';
            let patt = /,\s*(\w+)\s*:\s*('|")?(.*?)\2\s*(?=,)/g,
                value = '',
                obj = {},
                matches = null;
            //eslint-disable-next-line
            while(matches = patt.exec(str)){
                value = matches[3];
                obj[matches[1]] = value;
            }
            return obj;
        },
        getOptionLabel(val){
            const item =this.options.find(e=>e.value === val);
            return item? item.label : '';
        }
    }
}
</script>
<style scoped>
</style>