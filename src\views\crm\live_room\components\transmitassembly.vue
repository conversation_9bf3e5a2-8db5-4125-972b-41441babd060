<template>
    <div>
        <el-dialog :visible.sync="is_transfer_customer" :title="changetitle">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border>
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row,1)">转交客户</el-link>
          </template>
        </el-table-column>


        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>
    </div>
</template>
<script>
export default {
    data(){
        return{
            is_transfer_customer:false,
            changetitle:"转交客户",
            admin_params: {
              page: 1,
              per_page: 10,
              total: 0,
              user_name: "",
              type: 0
            },
            multipleSelection:[],
            admin_list:[]
        }
    },
    mounted(){
        this.getAdmin()
    },
    methods:{
        open(multipleSelection,data){
          if(data.id==2){
            this.changetitle = "复制客户"
          }
            this.multipleSelection = multipleSelection
            this.is_transfer_customer = true
        },
        //获取转交的成员
        getAdmin() {
          this.$http.getUserList(this.admin_params)
            .then((res) => {
              if (res.status === 200) {
                this.admin_list = res.data.data;
                this.admin_params.total = res.data.total;
              }
            });
        },
        PersPageChange(e) {
          this.admin_params.page = e;
          this.getAdmin();
        },
        // 搜索转交人
        onAdminSearch() {
        this.admin_params.page = 1;
        this.getAdmin();
        },
        //转交客户
        onZhuanrang(e,id) {
        this.$confirm(`是否将所选客户转交给【${e.user_name}】？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
            .then(() => {
            const nonZeroClientIdData = this.multipleSelection.filter(item => item.client_id !== 0);
            const clientIds = nonZeroClientIdData.map(item => item.client_id);
                this.c_id = clientIds.join(","); // 数组转换字符串
            // 将数组转换字符串
            // this.c_id = this.multipleSelection.join(",");
            // 点击确认调用公众号授权
            this.$http
                .setCrmCustomerZhuanrang({
                be_transfer_id: e.id,
                ids: this.c_id,
                })
                .then((res) => {
                if (res.status === 200) {
                    this.$message.success("操作成功");
                    this.is_transfer_customer = false;
                    this.$emit("getDataList");
                }
                });
            })
            .catch((err) => {
              console.log(err);
            // 点击取消控制台打印已取消
            this.$message.success("已取消操作");
            console.log("已取消");
            });
        },
        //复制客户
        oncopycrm(e,id){
          this.$confirm(`是否将所选客户复制到【${e.user_name}】的流转客？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              // 将数组转换字符串
            const nonZeroClientIdData = this.multipleSelection.filter(item => item.client_id !== 0);
            const clientIds = nonZeroClientIdData.map(item => item.client_id);
                this.c_id = clientIds.join(","); // 数组
              // 点击确认调用公众号授权
              this.$http
                .setCrmCustomercopy({
                  user_ids: e.id.toString(),
                  ids: this.c_id,
                })
                .then((res) => {
                  if (res.status === 200) {
                    this.$message.success("操作成功");
                    this.is_transfer_customer = false
                    this.$emit("getDataList");
                  }
                });
            })
            .catch((err) => {
              console.log(err);
              this.$message.success("已取消操作");
              // 点击取消控制台打印已取消
              console.log("已取消");
            });
        },
    }
    
}
</script>
<style lang="scss" scoped>
.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
}
</style>