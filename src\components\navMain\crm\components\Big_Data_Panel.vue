<template>
  <div class="big_data">
    <div class="panel">BI数据面板</div>
    <div class="panel_top">
      <div class="panel_center panelleft" @click="Quantification">
        <div class="centerdata flex-row">
          <div class="flex-row">
            <!-- <div class="tourists_top"></div> -->
            <div class="tourists_buttom">客源量化数据看板</div>
          </div>
          <img class="top_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                          'w_80'
                        )
                          " alt="" />
        </div>

        <div class="buttom_item div row">
          <div class="buttomdata">成员量化指标</div>
          <img class="buttom_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/keyuan.png',
                          'w_80'
                        )
                          " alt="">
        </div>
      </div>
      <div class="panel_center panelleft" @click="Clues_per_capita">
        <div class="centerdata flex-row">
          <div class="flex-row">
            <!-- <div class="tour_top"></div> -->
            <div class="tourists_buttom">线索人均指标看板</div>
          </div>
          <img class="top_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                          'w_80'
                        )
                          " alt="" />
        </div>
        <div class="buttom_item div row">
          <div class="buttomdata">客户线索统计</div>
          <img class="buttom_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/zhibaio.png',
                          'w_80'
                        )
                          " alt="">
        </div>
      </div>
      <div class="panel_center" @click="Clues_flow">
        <div class="centerdata flex-row">
          <div class="flex-row">
            <!-- <div class="tourists_top"></div> -->
            <div class="tourists_buttom">线索流入统计看板</div>
          </div>
          <img class="top_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                          'w_80'
                        )
                          " alt="" />
        </div>
        <div class="buttom_item div row">
          <div class="buttomdata">抖音/快手/视频号…</div>
          <img class="buttom_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/xiansuo.png',
                          'w_80'
                        )
                          " alt="">
        </div>
      </div>
    </div>
    <div class="panel_top">
      <div class="panel_center panelleft" @click="Transformation">
        <div class="centerdata flex-row">
          <div class="flex-row">
            <!-- <div class="tour_top"></div> -->
            <div class="tourists_buttom">客户数据转化看板</div>
          </div>
          <img class="top_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                          'w_80'
                        )
                          " alt="" />
        </div>
        <div class="buttom_item div row">
          <div class="buttomdata">成员量化指标</div>
          <img class="buttom_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/kehu.png',
                          'w_80'
                        )
                          " alt="">
        </div>
      </div>
      <div class="panel_center panelleft" @click="outboundviwe">
        <div class="centerdata flex-row">
          <div class="flex-row">
            <!-- <div class="ists_top"></div> -->
            <div class="tourists_buttom">呼叫中心统计看板</div>
          </div>
          <img class="top_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                          'w_80'
                        )
                          " alt="" />
        </div>
        <div class="buttom_item div row">
          <div class="buttomdata">客户线索统计</div>
          <img class="buttom_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/hujiao.png',
                          'w_80'
                        )
                          " alt="">
        </div>
      </div>
      <div class="panel_center" @click="Matrix">
        <div class="centerdata flex-row">
          <div class="flex-row">
            <!-- <div class="tourists"></div> -->
            <div class="tourists_buttom">矩阵账号统计看板</div>
          </div>
          <img class="top_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/prompt.png',
                          'w_80'
                        )
                          " alt="" />
        </div>
        <div class="buttom_item div row">
          <div class="buttomdata">团队抖音汇总统计</div>
          <img class="buttom_item_img" :src="$imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/statistics/juzhen.png',
                          'w_80'
                        )
                          " alt="">
        </div>
      </div>
    </div>

  </div>
</template>
<script>
export default{
    data() {
    return {
        
    }
   },
   methods:{
    //客源量化
     Quantification(){
      this.$goPath("crm_customer_business")
     },
     //线索人均
     Clues_per_capita(){
      this.$goPath("Data_center")
     },
     //线索流入
     Clues_flow(){
      this.$goPath("douyin_circulate_for_perusal")
     },    //成交转化
    Transformation(){
      this.$goPath("Transaction_conversion")
    },
    //外呼
    outboundviwe(){
      this.$goPath("outbound")
    },
    //矩阵
    Matrix(){this.$goPath("douyin_data_list");
    }
   }
}
</script>
<style scoped lang="scss" >
.big_data {
  background: rgb(241, 244, 250);
  padding: 28px;
  margin: -15px;

  .panel {
    margin-left: 15px;
    font-size: 20px;
    font-weight: 600;
  }

  .panel_top {
    width: 98%;
    height: 170px;
    margin: 0 auto;
    margin-top: 70px;
    display: flex;
    justify-content: space-between;

    // margin-bottom: 30px;
    .panel_center {
      width: 100%;
      height: 150px;
      background-color: #ffff;
      border-radius: 6px;
      cursor: pointer;

      .top_item_img {
        height: 16px;
        width: 16px;
      }

      .centerdata {
        width: 90%;
        margin: 10px auto;
        justify-content: space-between;

        .tourists_buttom {
          color: #2E3C4E;
          font-size: 20px;
          font-weight: 600;
          margin-top: 2px;
          margin-left: 5px;
        }
      }

      .buttomdata {
        font-size: 18px;
        color: #8A929F;
        margin-left: 40px;
      }

      .buttom_item {
        width: 95%;
        // display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;

        .buttom_item_img {
          width: 70px;
          height: 70px;
        }
      }
    }

    .panelleft {
      margin-right: 20px;
    }

    // .tourists_top{
    //     width: 10px;
    //     height: 35px;
    //     background: #2D84FB;

    // }
    // .tour_top{
    //   width: 10px;
    //   height: 35px;
    //   background: #FF9838;
    // // }
    // .ists_top{
    //   width: 10px;
    //   height: 35px;
    //   background: #01DB00;
    // }
    // .tourists{
    //   width: 10px;
    //   height: 35px;
    //   background: #FE5958;
    // }
  }

}</style>