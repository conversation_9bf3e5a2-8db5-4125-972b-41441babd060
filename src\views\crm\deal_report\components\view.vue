<template>
<div class="view-container" ref="view">
    <div class="report-title">成交报告</div>
    <div class="report-row">
        <div class="report-row-left"></div>
        <div class="report-row-right">
            <div class="report-row-right-item">
                <span class="title">编号：</span><span class="value">{{reportData.deal.cj_num || '--'}}</span>
            </div>
            <div class="report-row-right-item">
                <span class="title">添加人：</span><span class="value">{{reportData.deal.operate_name || '--'}}</span>
            </div>
        </div>
    </div>

    <detailBaseInfo :report-data="reportData" class="report-base-info"/>
    
    <div class="report-comm" v-if="reportData.report_id">
        <div class="report-comm-item">
            <div class="report-comm-item-title">应收佣金</div>
            <receiveCommission :report-data="reportData" preview/>
        </div>
        <div class="report-comm-item">
            <div class="report-comm-item-title">扣除款项</div>
            <companyCommission :report-data="reportData" preview/>
        </div>
        <div class="report-comm-item">
            <div class="report-comm-item-title">业绩提成</div>
            <memberCommission :report-data="reportData" preview/>
        </div>
        <div class="report-comm-item">
            <div class="report-comm-item-title">回款记录</div>
            <paymentLogs :report-data="reportData" preview/>
        </div>
    </div>
</div>
</template>
    
<script>
import detailBaseInfo from "./detail_base_info.vue";
import receiveCommission from "@/views/crm/deal_report/receive_commission.vue";
import companyCommission from "@/views/crm/deal_report/company_commission.vue";
import memberCommission from "@/views/crm/deal_report/member_commission.vue";
import paymentLogs from "@/views/crm/deal_report/payment_logs.vue";

import Print from "@/utils/print.js";
export default {
    components: {
        detailBaseInfo, 
        receiveCommission, companyCommission, memberCommission, paymentLogs
    },
    props: {
        id: {type: Number, default: 0 }
    },
    data() {
        return {
            loading: false,
            printing: false,
            reportData: {   //报告数据
                admin: {},
                deal: {}
            },                 
        };
    },
    watch: {
        id(val){
            this.getReportDetail(val)
        }
    },
    methods: {
        //获取报详情
        async getReportDetail(id){
            const res = await this.$http.getReportDetailAPI(id);
            if(res.status == 200){
                this.reportData = res.data
                return res.data;
            }
        },
        //打印报告
        async printReport(id = 0){
            if(this.printing){
                return;
            }
            this.printing = true
            try{
                if(id && id != this.reportData.report_id){
                    const res = await this.getReportDetail(id);
                    if(!res){
                        throw new Error('获取报告信息失败')
                    }
                    await this.$nextTick();
                }

                const domClone = this.$refs.view.cloneNode(true);
                const tables = domClone.querySelectorAll('.el-table')
                for (let k4 = 0; k4 < tables.length; k4++) {
                    const newTable = document.createElement('table');
                    const trs = tables[k4].querySelectorAll('tr');
                    for (let k5 = 0; k5 < trs.length; k5++) {
                        newTable.appendChild(trs[k5])
                    }

                    const wrappers = tables[k4].querySelectorAll('.el-table__header-wrapper,.el-table__footer-wrapper');
                    for (let k6 = 0; k6 < wrappers.length; k6++) {
                        tables[k4].removeChild(wrappers[k6])
                    }

                    newTable.className = 'el-table__body';
                    newTable.style.width = '100%'
                    newTable.setAttribute("cellspacing", 0);
                    newTable.setAttribute("cellpadding", 0);
                    newTable.setAttribute("border", 0);
                    const bodyWapper = tables[k4].querySelector('.el-table__body-wrapper');
                    bodyWapper.replaceChild(newTable, bodyWapper.firstElementChild)
                }
                Print(domClone)
            }catch(e){
                console.error(e)
            }
            this.printing = false;
        }
    },
};
</script>
    
<style lang="scss" scoped>
@media print {
    @page {
        mso-header:none;
        mso-footer:none;
        margin: 0;
        size: A4;
    }
    /* 告诉浏览器在渲染它时不要对框进行颜色或样式调整 */
    * {
        -webkit-print-color-adjust: exact !important;
        -moz-print-color-adjust: exact !important;
        -ms-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    .view-container { padding: .2cm .8cm .2cm; } 
    ::v-deep .el-table__empty-block{height: auto!important;width: auto!important;}
    ::v-deep .el-table,
    ::v-deep .el-table__body-wrapper{
        overflow: visible!important;
    }
}


.view-container{
    background: #fff;
    .report-title{
        display: flex;
        height: 60px;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
    .report-row{
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        .report-row-right{
            display: flex;
            .report-row-right-item{
                padding-left: 24px;
                .title{
                    color: #9c9c9c;
                }
                .value{
                    color: #3c3c3c;
                }
            }
        }
    }
    .report-base-info{
        padding: 24px 0 0;
    }
    .report-comm{
        .report-comm-item{
            padding: 24px 0 12px;
            page-break-inside: avoid;
            .report-comm-item-title{
                font-size: 18px;
                font-weight: 600;
                color: #6c6c6c;
                padding: 0 12px;
                margin: 12px 0;
                border-left: 6px solid #2d84fb;
                page-break-inside: avoid;
            }
        }
    }
    .report-bottom{
        display: flex;
        height: 60px;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>