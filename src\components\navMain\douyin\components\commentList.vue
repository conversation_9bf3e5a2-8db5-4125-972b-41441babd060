<template>
  <div class="detail page">
    <div class="content-box-crm flex-row align-center" style="padding: 0 12px">
      <div class="bottom-border div row" style="padding: 0 0 24px 0">
        <span class="text">时间：</span>
        <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
        <span class="text">自定义：</span>
        <el-date-picker
          style="width: 250px"
          size="small"
          v-model="p_time"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="changeTimeRange"
        >
        </el-date-picker>
      </div>
      <!-- <div style="margin-left: auto; color: #fb656a">每日12点前更新</div> -->
    </div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      ref="detrail"
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <!-- <el-table-column label="ID" prop="item_id"> </el-table-column> -->

      <el-table-column label="评论内容" prop="content"> </el-table-column>
      <!-- <el-table-column label="类别" prop="media_type">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.media_type == 2"> 图集 </el-tag>
          <el-tag type="success" v-if="scope.row.media_type == 4">
            视频
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="点赞数" prop="digg_count"> </el-table-column>
      <el-table-column label="回复数量" prop="reply_comment_total">
      </el-table-column>
      <el-table-column label="评论时间" prop="create_time"> </el-table-column>
      <!-- <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.media_type == 4"
            @click="copyLink(scope.row)"
          >
            复制视频链接
          </el-button>
          <el-button @click="commentList(scope.row)"> 评论列表 </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
import myLabel from "@/components/navMain/crm/components/my_label";
export default {
  components: { myLabel },
  props: {
    id: {
      type: [String, Number],
      default: ""
    }
  },
  created() {
    this.params.item_id = this.id
    this.getList()
  },
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        item_id: ""
      },
      time_list: [
        { id: 1, name: "全部", value: "1" },
        { id: 2, name: "近一周", value: "2" },
        { id: 3, name: "近三月", value: "3" },
        { id: 4, name: "近六月", value: "4" },
      ],
      p_time: []

    }
  },
  methods: {
    getList() {
      this.is_table_loading = true
      this.$http.getDouyinVideosCommentList(this.params).then(res => {
        this.is_table_loading = false
        if (res.status == 200) {
          this.tableData = res.data.data
          this.params.total = res.data.total;
          // this.$set(this.tableData, res.data.data)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    onClickTime(item) {
      this.params.time_type = item.value;
      this.p_time = []
      this.params.page = 1;
      this.getList()
    },
    changeTimeRange(e) {
      this.params.time_type = ''
      if (e && e.length) {
        this.p_time = e;
        this.params.page = 1;
        this.getList()
        // this.getDataList();
      } else {
        this.params.p_time = [];
        this.params.page = 1;
        this.getList()
        // this.getDataList();
      }
    },
  }
}
</script>

<style lang ="scss" scoped>
.detail {
  max-height: 70vh;
  overflow-y: auto;
}
.bottom-border {
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e2e2;
  .text {
    font-size: 14px;
    color: #8a929f;
  }
}
</style>