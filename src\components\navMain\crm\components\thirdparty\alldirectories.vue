<template>
    <div class="tab-content-container">
  		<div class="tab-content-body white topped">
			<div class="body-inner main-scroll" ref="scrollElem">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="同步记录" name="synchronizeRecords"></el-tab-pane>
                <!-- <el-tab-pane label="变更记录" name="third"></el-tab-pane> -->
                <el-tab-pane label="项目管理" name="projectmanagement"></el-tab-pane>
                <!-- <el-tab-pane label="账号设置" name="accountset"></el-tab-pane> -->
            </el-tabs>
            <keep-alive>
					<component :is="activeName" @room-change="handleRoomChange"
					ref="list"></component>
			</keep-alive> 
 
			</div>
		</div>
    </div>
</template>
<script>
import synchronizeRecords from "./synchronizeRecords.vue"
import projectmanagement from "./projectmanagement.vue"
export default {
    components:{
        synchronizeRecords,
        projectmanagement
    },
    data() {
        return {
            activeName: 'synchronizeRecords'
        }
    },
    mounted(){
        this.Name = this.$route.query.id
        if(this.Name==1){
            this.activeName = "synchronizeRecords"
        }else{
            this.activeName =  'projectmanagement'
        }
    },
    methods:{
        handleClick(tab, event) {
            // if(this.activeName =="synchronizeRecords"){
            //     this.$router.push("synchronizeRecords");
            // }
        // console.log(tab, event);
      },
      handleRoomChange(){

      },
    },
}
</script>
<style lang="scss" scoped>
.tab-content-body{
	padding: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	padding-bottom: 48px;
}
.body-inner{
	flex: 1;
	overflow: auto;
	padding: 0 28px 28px;
}

	
.header-tabs{
	padding: 16px 0 0;
	::v-deep.el-tabs__item{
		font-size: 15px !important;
	}
}
::v-deep{
	.tab-content-footer{
		position: absolute;
		z-index: 99;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding-left: 28px;
		padding-right: 28px;
		white-space: nowrap;
		overflow: hidden;
	}
}
</style>