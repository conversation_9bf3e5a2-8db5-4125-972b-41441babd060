<template>
  <el-container>
    <el-container>
      <el-header class="div row">
        <myTopTips title="经纪人列表" :number="tableData.length">
          <div class="add-build">
            <el-button type="primary" @click="dialogBroker = true"
              >添加经纪人</el-button
            >
          </div></myTopTips
        >
        <div class="div row">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              经纪人名称<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">经纪人名称</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-input v-model="input" placeholder="搜索相关的经纪人"></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>
      <el-dialog title="选择经纪人" :visible.sync="dialogBroker">
        <el-form :model="fromBroker">
          <el-form-item label="联系方式" label-width="100px">
            <el-select
              v-model="fromBroker.user_id"
              filterable
              remote
              reserve-keyword
              placeholder="请输入联系方式"
              :remote-method="getbrokerData"
              :loading="broker_loading"
            >
              <el-option
                v-for="item in broker_list"
                :key="item.user_id"
                :label="item.name"
                :value="item.user_id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogBroker = false">取 消</el-button>
          <el-button type="primary" @click="bindingBroker">确 定</el-button>
        </div>
      </el-dialog>
      <el-main>
        <myTable :table-list="tableData" :header="table_header"></myTable>
      </el-main>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :pagesize="params.pagesize"
            :currentPage="params.currentPage"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-container>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "binding_broker",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      // 搜索框数据
      input: "",
      tableData: [],
      project_id: null,
      broker_list: [],
      broker_loading: false,
      fromBroker: {
        project_id: "",
        user_id: "",
      },
      dialogBroker: false,
      table_header: [
        { prop: "id", label: "用户ID", width: "100" },
        { prop: "project_id", label: "项目ID", width: "100" },
        { prop: "user_id", label: "用户ID", width: "100" },
        {
          label: "用户头像",
          render: (h, data) => {
            return (
              <img
                width="50"
                src={
                  data.row.avatar
                    ? data.row.avatar
                    : "https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg"
                }
              />
            );
          },
        },
        { prop: "user_name", label: "用户名" },
        { prop: "phone", label: "联系方式" },
        {
          label: "操作",
          render: (h, data) => {
            return (
              <el-button
                size="mini"
                type="danger"
                onClick={() => {
                  this.handleDelete(data.row);
                }}
              >
                删除
              </el-button>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.project_id = this.$route.query.id;
    this.fromBroker.project_id = this.project_id;
    this.getDataList();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.$http
        .showBrokerList(this.project_id, this.params.currentPage)
        .then((res) => {
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    // 搜索
    search() {
      this.$http.searchBroker(this.project_id, this.input).then((res) => {
        if (this.input !== "") {
          this.tableData = res.data.data;
        } else {
          this.getDataList();
        }
      });
    },
    // 搜索下拉
    handleCommand(command) {
      this.$message("click on item " + command);
    },
    handleDelete(row) {
      this.$confirm("此操作将删除该用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteBroker(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    getbrokerData(query) {
      this.broker_loading = true;
      this.$http.searchUserByPhone(query).then((res) => {
        this.broker_loading = false;
        this.broker_list = res.data.data.map((item) => {
          return {
            user_id: item.id,
            name: item.name || item.nickname || item.user_name,
          };
        });
      });
    },
    bindingBroker() {
      this.$http.projectBroker(this.fromBroker).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.getDataList();
        }
      });
      this.dialogBroker = false;
    },
  },
};
</script>
<style scoped lang="scss">
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 0;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;

  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  width: 40px;
  img {
    width: 100%;
  }
}
</style>
