<template>
<div>
  <!-- <div
    style="margin-left: 10px; max-height: 500px; overflow-y: auto"
    v-infinite-scroll="loadMoreFollow"
    :infinite-scroll-disabled="guiji_busy"
  >
    <el-timeline v-if="guijiInfo.length > 0">
      <el-timeline-item
        v-for="(activity, index) in guijiInfo"
        :key="index"
        :size="activity.size"
        placement="top"
        color="#2D84FB"
      >
        <div class="agent_info flex-row align-center">
          <div class="time">
            {{ activity.ctime }}
          </div>
          <div class="agent_name" v-if="activity.admin_user">
            {{ activity.admin_user && activity.admin_user.user_name }}/{{
              activity.admin_user && activity.admin_user.department_name
            }}
          </div>
        </div>
        <div class="spancolor">
          {{ activity.content }}
        </div>
      </el-timeline-item>
    </el-timeline>
    <myEmpty v-if="guijiInfo.length == 0 && !guiji_busy"></myEmpty>
  </div> -->
  <div class="trackSelect-box">
     
    <!-- <div class="trackContent" @click="trackClick(1)" :class="{active: trackValue == 1}" v-if="tfyshow">腾房云</div> -->
    <div class="trackContent" @click="trackClick(1)" :class="{active: trackValue == 1}">小程序</div>
    <div class="trackContent" @click="trackClick(0)" :class="{active: trackValue == 0}" v-if="tfyshow">腾房云</div>
  

  </div>
  <!-- 腾房云列表 -->
  <div
    style="margin-left: 10px; max-height: 400px; overflow-y: auto;margin-bottom: 84px;"
    v-infinite-scroll="loadMoreFollow"
    :infinite-scroll-disabled="guiji_busy"
    v-show="trackValue == 0"
  >
    <el-timeline v-if="guijiInfo.length > 0">
      <el-timeline-item
        v-for="(activity, index) in guijiInfo"
        :key="index"
        :size="activity.size"
        placement="top"
        color="#2D84FB"
      >
        <div class="agent_info flex-row align-center">
          <div class="time">
            {{ activity.ctime }}
          </div>
          <div class="agent_name" v-if="activity.admin_user">
            {{ activity.admin_user && activity.admin_user.user_name }}/{{
              activity.admin_user && activity.admin_user.department_name
            }}
          </div>
        </div>
        <div class="spancolor">
          {{ activity.content }}
        </div>
      </el-timeline-item>
    </el-timeline>
    <!-- <myEmpty v-if="guijiInfo.length == 0 && !guiji_busy"></myEmpty> -->
    <myEmpty v-else :loading="loading"></myEmpty>
  </div>
  <!-- 楼盘小程序列表 -->
  <div
    style="margin-left: 10px; max-height: 400px; overflow-y: auto;margin-bottom: 84px;" 
    v-infinite-scroll="loadMoreApp"
    v-show="trackValue == 1"
  >
    <el-timeline>
      <el-timeline-item
        v-for="(activity, index) in houseAppList"
        :key="index"
        placement="top"
        size="normal"
        color="#2D84FB"
      >
        <div class="agent_info flex-row align-center">
          <div class="time">
            {{ activity.created_at }}
          </div>
        </div>
        <div class="spancolor">
          <el-tooltip class="item" effect="dark" :content="activity.content" placement="top-start">
            <div v-if="activity.map_plugin==null" class="clue-content">{{ activity.page_name + ' ' + activity.platform }}</div>
          </el-tooltip>
          
          <el-collapse v-model="activeNames" @change="handleChange" v-if="activity.map_plugin&&activity.map_plugin.length!=0">
            <el-collapse-item :title="activity.page_name + ' ' + activity.platform" name="1">
              <template v-slot:title>
                  <el-tooltip class="item" effect="dark" :content="activity.page_name + ' ' + activity.platform" placement="top-start">
                    <div class="clue-content"> {{ activity.page_name + ' ' + activity.platform }}</div>
                  </el-tooltip>
              </template>
              <div class="content"> 
                <div v-if="activity.map_plugin.title!==''">浏览页面：{{activity.map_plugin.title}}</div>
                <div v-if="activity.browse_time!==''">停留时长：{{activity.browse_time}}秒</div>
                <div v-if="activity.users&&activity.users.name!==''">分享人员：{{activity.users.name}}</div>
                <div v-if="activity.type">
                  客户行为：{{ activity.type == 1 ? '授权手机号' : activity.type == 2 ? '浏览页面' : '----' }}
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-timeline-item>
    </el-timeline>
    <div>
      <myEmpty v-if="!houseAppList.length" :loading="loading"></myEmpty>
    </div>
  </div>
</div>
</template>

<script>
import myEmpty from "@/components/components/my_empty.vue";
export default {
  components: {
    myEmpty,
  },
  props: {
    guijiInfo: {
      type: Array,
      default: () => [],
    },
    guijiPage: {
      type: Number,
      default: 1,
    },
    guiji_busy: {
      type: Boolean,
      default: true
    },
    ids: {
      type: Number,
      default: () =>"",
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: 'first',
      c_id: "",
      houseAppList: [],
      AppParams: {
        page: 0,
      },
      website_ids: 0,
      trackValue: 1,
      WeixinLoadMore: true,
      getAppData: true,
      tfyshow:false,
      activeNames: "1",
    }
  },
  mounted() {
    this.c_id = this.$route.query.id;
    let cookies = document.cookie.split(';');
    if(cookies[0]=="value=1"){
      this.tfyshow = true
    }else{
      this.tfyshow = false
    }
  },
  methods: {
    loadMoreFollow() {
      this.$emit("loadmoreGuiji", this.guijiPage);
    },
    getAppDataList() {
      if(this.c_id==undefined){
          this.c_id = this.ids
      }
      this.$http.getRealEstateXiaoApp(this.c_id, this.AppParams).then(res => {
        if(res.status == 200) {
          if(res.data.data.length == 0) {
            this.WeixinLoadMore = false;
          }
          this.getAppData = false
          this.houseAppList = this.houseAppList.concat(res.data.data);
          console.log(this.houseAppList);
        }
      })
    },
    loadMoreApp() {
      if(this.WeixinLoadMore) {
        this.AppParams.page++;
        this.getAppDataList();
      }
     
    },
    trackClick(val) {
      this.trackValue = val;
      if(val == 0 && this.getAppData) {
        this.getAppDataList();
      }
    },
    handleChange(val) {
        console.log(val);
        console.log(this.activeNames);
      }
  },
};
</script>

<style scoped lang="scss">
.spancolor {
  color: #8a929f;
  white-space: pre-line;
  ::v-deep .el-collapse{
    border: 0px solid #ffffff;
  }
  ::v-deep .el-collapse-item__header {
              border: 0px solid #ffffff;
          }
  ::v-deep .el-collapse-item__arrow {
    margin: 5px;
  }
    .content{
      width: 96%;
      padding: 13px;
      border-radius:8px;
      background-color: #f6f6f6;
    }
}
.agent_info {
  margin: -10px 0 24px 0;
  .time {
    margin-right: 10px;
    color: #8a929f;
  }
  .img {
    margin-right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .agent_name {
    font-size: 12px;
    color: #8a929f;
  }
}
.f_content {
  &.red {
    color: #fc0606;
  }
}
::v-deep.el-tabs {
  .el-tabs__header {
    width: 152px;
  }
  .el-timeline {
    padding: 1px;
  }
}
::v-deep.trackSelect-box {
  display: flex;
  margin-bottom: 20px;
  .trackContent{
    font-size: 14px;
    background-color: #F5F5F5;
    padding: 5px 10px;
    margin: 5px;
    color: #7A7C7F;
    border-radius: 3px;
    border: 1px solid #F5F5F5;
    cursor: pointer;
  }
  .trackContent:first-child {
    margin-left: 0px;
  }
  .active {
    background-color: #FFEFF0;
    border: 1px solid #FF6D73;
    color: #FF6A70;
  }
}
</style>
