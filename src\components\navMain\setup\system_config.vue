<template>
  <el-main>
    <!-- <div class="back">
        <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
    </div>-->

    <myTable :table-list="tableData" :header="table_header"></myTable>
    <el-dialog
      :close-on-click-modal="false"
      width="50%"
      title="短信配置"
      :visible.sync="dialogShow"
    >
      <el-form label-width="auto" :model="config_obj" style="800px">
        <el-form-item label="短信配置">
          <el-radio-group
            v-model="config_obj.config_category"
            @change="changeConfigCategory"
          >
            <el-radio-button
              v-for="item in config_category_list"
              :key="item.value"
              :label="item.value"
              :disabled="config_obj.config_category == 1"
              >{{ item.desc }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否开启">
          <el-radio-group v-model="config_obj.enable">
            <el-radio
              v-for="item in enable_list"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="短信签名 " v-if="config_obj.config_category == 0">
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.sign_name"
          ></el-input>
        </el-form-item>
        <el-form-item label="access_key_id">
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.access_key_id"
          ></el-input>
        </el-form-item>
        <el-form-item label="access_secret">
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.access_secret"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="验证码短信模板编号"
          v-if="config_obj.config_category == 0"
        >
          <el-input
            :disabled="config_obj.config_category == 1 ? true : false"
            v-model="config_obj.captcha_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button type @click="goHide">返回</el-button>
          <el-button type="primary" @click="onSubmit">提交修改</el-button>
        </el-form-item>
      </el-form>
      <!-- <myForm :form_create="form_create_obj" @onClick="onSubmit"></myForm> -->
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      width="50%"
      title="微信消息配置"
      :visible.sync="dialogShowWX"
    >
      <el-form label-width="auto" :model="config_obj_wx" ref="config_obj_wx">
        <el-form-item label="是否开启" prop="enable">
          <el-radio-group v-model="config_obj_wx.enable">
            <el-radio
              v-for="item in enable_list"
              :key="item.id"
              :label="item.id"
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="客户报备成功提醒"
          prop="new_reported_status_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.new_reported_status_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="客户状态更新通知"
          prop="change_reported_status_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.change_reported_status_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="客户跟进提醒"
          prop="customer_follow_up_remind_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.customer_follow_up_remind_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item label="客户成交通知" prop="deal_status_template_code">
          <el-input
            type="textarea"
            v-model="config_obj_wx.deal_status_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="佣金发放通知"
          prop="settle_brokerage_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.settle_brokerage_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="账户变更通知"
          prop="brokerage_balance_change_template_code"
        >
          <el-input
            type="textarea"
            v-model="config_obj_wx.brokerage_balance_change_template_code"
          ></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button type @click="goBackWx">返回</el-button>
          <el-button type="primary" @click="onSubmitWx">提交修改</el-button>
        </el-form-item>
      </el-form>
      <!-- <myForm :form_create="form_create_obj_wx" @onClick="onSubmitWx"></myForm> -->
    </el-dialog>
  </el-main>
</template>

<script>
import myTable from "@/components/components/my_table";
// import myForm from "@/components/components/my_form";
export default {
  name: "system_config",
  components: {
    myTable,
    // myForm,
  },
  data() {
    return {
      tableData: [],
      dialogShow: false,
      config_list: [],
      dialogShowWX: false,
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "name", label: "配置名称" },
        {
          label: "操作",
          render: (h, data) => {
            return (
              <el-button
                type="success"
                size="mini"
                onClick={() => {
                  this.checkData(data.row);
                }}
              >
                配置详情
              </el-button>
            );
          },
        },
      ],
      config_obj: {
        enable: "1",
        config_category: "1",
        access_key_id: "",
        access_secret: "",
        captcha_template_code: "",
        sign_name: "",
      },
      config_obj_wx: {
        enable: "1",
        new_reported_status_template_code: "",
        change_reported_status_template_code: "",
        deal_status_template_code: "",
        settle_brokerage_template_code: "",
        brokerage_balance_change_template_code: "",
        customer_follow_up_remind_template_code: "",
      },
      config_category_list: [
        { value: "0", desc: "自定义配置" },
        { value: "1", desc: "系统配置" },
      ],
      enable_list: [
        { id: "1", name: "开启" },
        { id: "0", name: "关闭" },
      ],
      form_create_obj: {
        // inline: false,
        // label_width: "160px",
        // model: {
        //   enable: "1",
        //   config_category: "",
        //   access_key_id: "",
        //   access_secret: "",
        //   captcha_template_code: "",
        //   sign_name: "",
        // },
        // formLabel: [
        //   {
        //     type: "radio",
        //     model: "config_category",
        //     opts: [
        //       { value: "0", description: "自定义配置" },
        //       { value: "1", description: "系统配置" },
        //     ],
        //     label: "短信配置：",
        //   },
        //   {
        //     type: "radio",
        //     model: "enable",
        //     label: "是否开启：",
        //     opts: [
        //       { value: "1", description: "开启" },
        //       { value: "0", description: "关闭" },
        //     ],
        //   },
        //   {
        //     type: "input",
        //     label: "短信签名：",
        //     model: "sign_name",
        //     inputWidth: "400px",
        //   },
        //   {
        //     type: "input",
        //     label: "access_key_id：",
        //     model: "access_key_id",
        //     inputWidth: "400px",
        //     disabled: "",
        //   },
        //   {
        //     type: "input",
        //     label: "access_secret：",
        //     model: "access_secret",
        //     inputWidth: "400px",
        //     disabled: "",
        //   },
        //   {
        //     type: "input",
        //     label: "验证码短信模板编号：",
        //     model: "captcha_template_code",
        //     inputWidth: "400px",
        //   },
        // ],
      },
      form_create_obj_wx: {
        // inline: false,
        // label_width: "160px",
        // model: {
        //   enable: "1",
        //   new_reported_status_template_code: "",
        //   change_reported_status_template_code: "",
        //   deal_status_template_code: "",
        //   settle_brokerage_template_code: "",
        //   brokerage_balance_change_template_code: "",
        //   customer_follow_up_remind_template_code: "",
        // },
        // formLabel: [
        //   {
        //     type: "radio",
        //     model: "enable",
        //     label: "是否开启：",
        //     opts: [
        //       { value: "1", description: "开启" },
        //       { value: "0", description: "关闭" },
        //     ],
        //   },
        //   {
        //     type: "input",
        //     model: "new_reported_status_template_code",
        //     label: "客户报备成功提醒：",
        //     inputType: "textarea",
        //   },
        //   {
        //     type: "input",
        //     model: "change_reported_status_template_code",
        //     label: "客户状态更新通知：",
        //     inputType: "textarea",
        //   },
        //   {
        //     type: "input",
        //     model: "customer_follow_up_remind_template_code",
        //     label: "客户跟进提醒：",
        //     inputType: "textarea",
        //   },
        //   {
        //     type: "input",
        //     model: "deal_status_template_code",
        //     label: "客户成交通知：",
        //     inputType: "textarea",
        //   },
        //   {
        //     type: "input",
        //     model: "settle_brokerage_template_code",
        //     label: "佣金发放通知：",
        //     inputType: "textarea",
        //   },
        //   {
        //     type: "input",
        //     model: "brokerage_balance_change_template_code",
        //     label: "账户变更通知：",
        //     inputType: "textarea",
        //   },
        // ],
      },
    };
  },
  mounted() {
    let arr = [
      { id: 1, name: "短信配置", isShow: this.$hasShow("短信配置") },
      { id: 2, name: "微信消息配置", isShow: this.$hasShow("微信消息配置") },
      { id: 3, name: "oss订单", isShow: this.$hasShow("oss订单") },
      { id: 4, name: "短信订单", isShow: this.$hasShow("短信订单") },
    ];
    this.tableData = arr.filter((item) => {
      return item.isShow === true;
    });
  },
  methods: {
    checkData(row) {
      if (row.id === 1) {
        this.dialogShow = true;
        this.getConfigList();
      } else if (row.id === 2) {
        this.dialogShowWX = true;
        this.getWxConfig();
      } else if (row.id === 3) {
        this.$goPath("oss_list");
      } else if (row.id === 4) {
        this.$goPath("sms_list");
      }
    },
    getConfigList() {
      this.$http.getSystemConfig().then((res) => {
        if (res.status === 200) {
          this.config_list.push(res.data);
          this.config_obj = res.data;
        }
      });
    },
    changeConfigCategory(e) {
      if (e == 1) {
        this.config_obj.sign_name = "腾房云";
        this.config_obj.captcha_template_code = "SMS_163052744";
        this.config_obj.access_key_id = "请勿修改";
        this.config_obj.access_secret = "请勿修改";
      } else {
        this.config_obj.sign_name = "";
        this.config_obj.captcha_template_code = "";
        this.config_obj.access_key_id = "";
        this.config_obj.access_secret = "";
      }
    },
    getWxConfig() {
      this.$http.getWxconfig().then((res) => {
        if (res.status === 200) {
          this.config_obj_wx = res.data;
        }
      });
    },
    onSubmit() {
      if (this.config_obj.config_category == 1) {
        this.config_obj.access_secret = "请勿修改";
        this.config_obj.access_key_id = "请勿修改";
      }
      this.config_obj.enable = this.config_obj.enable.toString();
      this.$http.editConfig(this.config_obj).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.config_obj.enable = parseInt(this.config_obj.enable);
          this.dialogShow = false;
        }
      });
    },
    goBack() {
      this.$router.back();
    },
    goHide() {
      this.dialogShow = false;
    },
    goBackWx() {
      this.dialogShowWX = false;
    },
    onSubmitWx() {
      let formRules = {
        enable: "请选择是否开启",
        new_reported_status_template_code: "请填写内容",
        change_reported_status_template_code: "请填写内容",
        deal_status_template_code: "请填写内容",
        settle_brokerage_template_code: "请填写内容",
        brokerage_balance_change_template_code: "请填写内容",
        customer_follow_up_remind_template_code: "请填写内容",
      };
      for (var key in this.form_create_obj_wx) {
        if (this.form_create_obj_wx === "") {
          this.$message({
            message: formRules[key],
            type: "error",
          });
          return;
        }
      }
      this.config_obj_wx.enable = this.config_obj_wx.enable.toString();
      this.$http.createWxMsg(this.config_obj_wx).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.config_obj_wx.enable = parseInt(this.config_obj_wx.enable);
          this.dialogShowWX = false;
        }
      });
    },
  },
};
</script>

<style scoped>
/* .el-main{
  padding-top: 50px;
} */
.back {
  position: absolute;
  top: 85px;
  left: 240px;
  margin-bottom: 10px;
  z-index: 10;
}
</style>
