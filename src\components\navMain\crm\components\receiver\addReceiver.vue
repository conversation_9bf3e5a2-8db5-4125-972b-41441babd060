<template>
  <div style="max-height: 70vh; overflow-y: auto">
    <div class="member">
      <div class="member_box flex-row">
        <div class="left member_con flex-1">
          <memberList
            v-if="memberList.length"
            :list="memberList"
            :defaultValue="selected"
            @onClickItem="selecetedMember"
            @setchecked="setchecked"
            ref="memberList"
          >
          </memberList>
        </div>
        <div class="right member_con flex-1">
          <div class="select_title">已选择成员</div>
          <div class="selected_list">
            <div
              class="selected_item flex-row align-center"
              v-for="item in selectedList"
              :key="item.id"
            >
              <!-- <div class="prelogo">
                    <img :src="item.prelogo" alt="">
                  </div> -->
              <div class="name flex-1">{{ item.name }}</div>
              <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
            </div>
          </div>
        </div>
      </div>
      <el-form
        label-width="80px"
        style="margin: 20px 0; border-top: 1px solid #f8f8f8"
      >
        <el-form-item label="是否作席">
          <div class="form-item-block line_height1">
            <el-switch v-model="is_online" active-color="#2D84FB"> </el-switch>
          </div>
        </el-form-item>
      </el-form>
      <div class="footer flex-row align-center">
        <el-button type="text" @click="cancel">取消</el-button>
        <el-button type="primary" @click="selectMemberOk" :loading="isSubmiting"
          >确定</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import memberList from "../memberList"
export default {
  props: ["id", 'selected', 'selectedLists'],
  components: {
    memberList
  },
  data() {
    return {
      memberList: [],
      selectedIds: [],
      selectedList: [],
      isSubmiting: false,
      is_online: true
    }
  },
  computed: {
  },
  created() {
    this.selectedIds = this.selected.length ? JSON.parse(JSON.stringify(this.selected)) : []
    this.selectedList = this.selectedLists.length ? JSON.parse(JSON.stringify(this.selectedLists)) : []
    this.oldIds = this.selected.length ? JSON.parse(JSON.stringify(this.selected)) : []
    this.defaultIds = this.selected.length ? JSON.parse(JSON.stringify(this.selected)) : []
    this.old_selectedMember = this.selectedLists.length ? JSON.parse(JSON.stringify(this.selectedLists)) : []
    this.default_selectedMember = this.selectedLists.length ? JSON.parse(JSON.stringify(this.selectedLists)) : []
    this.getDepartment()
  },

  methods: {
    subform() {
      // kfid this.id 
      let params = {
        kf_id: this.id,
        id: this.selectedIds.join(','),
        is_online: this.is_online ? 1 : 0
      }
      this.isSubmiting = true
      this.$http.addCrmReceiver(params).then(res => {
        if (res.status == 200) {
          this.$message.success("添加成功");
          setTimeout(() => {
            this.isSubmiting = false
          }, 200);
          this.$emit("success")
        } else {
          this.isSubmiting = false
          this.$message.error("添加失败");
        }
      }).catch(() => {
        this.isSubmiting = false
      })
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList()
        .catch(err => {
          console.log(err);
        })
      if (res.status == 200) {
        this.memberList = res.data
      }
    },
    // 移除选中的人员
    deleteSelected(e) {
      this.selectedIds = [...new Set(this.selectedIds)]
      this.selectedList = this.newsetArr(this.selectedList, 'id')
      let idx = this.selectedList.findIndex(item => item.id == e.id)
      this.selectedList.splice(idx, 1)
      this.selectedIds.splice(idx, 1)
      setTimeout(() => {
        // this.$refs.memberList.changeSelected([e.id], false)
        this.setchecked()
      }, 100);


    },
    setchecked(type = true) {
      this.$refs.memberList.changeSelected(this.selectedIds, type)
    },
    // 选中会员
    selecetedMember(e) {
      this.selectedIds = [...new Set(e.checkedKeys)]
      if (e.checkedNodes.length) {
        e.checkedNodes.map(ch => {
          this.default_selectedMember.map(item => {
            if (e.checkedKeys.includes(item.id) && !e.checkedKeys.includes(item.id)) {
              e.checkedNodes.push(item)
            }
          })
          return ch
        })
      } else {
        this.default_selectedMember.map(item => {
          if (this.selectedIds.includes(item.id)) {
            e.checkedNodes.push(item)
          }
        })
      }
      this.selectedList = this.newsetArr(e.checkedNodes, 'id')
    },
    newsetArr(arr, key = "id") {
      var result = [];
      var obj = {};
      for (var i = 0; i < arr.length; i++) {
        if (!obj[arr[i][key]]) {
          result.push(arr[i]);
          obj[arr[i][key]] = true;
        }
      }
      return result
    },
    selectMemberOk() {
      this.subform()
    },
    cancel() {
      this.$emit("cancel")
    }
  }
}
</script>

<style lang ="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
</style>