<template>
    <div class="task_list">

        <div class="header-title div row">
      <div class="ht-title">当前任务详情</div>
      <div style="margin-right: 24px">
        <el-link type="primary" @click="toAllTaskList()">全部任务列表</el-link>

      </div>
    </div>
    <!-- <div style="margin-bottom: 15px;">
        <el-select v-model="status" slot="prepend" placeholder="可根据状态搜索">
          <el-option label="全部" value="0"></el-option>  
          <el-option label="成功" value="1"></el-option>
          <el-option label="CRM客户重复" value="2"></el-option>
          <el-option label="表格重复" value="3"></el-option>
          <el-option label="失败" value="4"></el-option>
        </el-select>
        <el-button slot="append" icon="el-icon-search" @click="Search_Status"></el-button>
    </div> -->
        <!-- 任务详情 -->
        <el-row :gutter="20">
            <el-col :span="24">

                <div class="content-box-crm">
                    <div class="table-top-box div row">

                    </div>
                    <el-table :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
                        :row-style="$TableRowStyle" v-loading="loading">
                        <!-- <el-table-column label="详情id" width="80" prop="id" align="left">

                        </el-table-column>
                        <el-table-column label="任务id" width="80" prop="task_id" align="left">

                        </el-table-column> -->
                        <el-table-column label="id" prop="id" align="left">
                        </el-table-column>
                        <el-table-column label="页码" prop="page" align="left">
                            <!-- <template slot-scope="scope">
                                {{scope.row.cname?scope.row.cname:"--"}}
                            </template> -->
                        </el-table-column>
                        <!-- <el-table-column label="录入人姓名" prop="create_name" align="left">
                            <template slot-scope="scope">
                                {{scope.row.create_name?scope.row.create_name:"--"}}
                            </template>
                        </el-table-column> -->
                        <!-- <el-table-column label="维护人姓名" prop="follow_name" align="left">
                            <template slot-scope="scope">
                                {{scope.row.follow_name?scope.row.follow_name:"--"}}
                            </template>
                        </el-table-column>
                        <el-table-column label="手机号" prop="mobile" align="left">
                        </el-table-column> -->
                        <!-- <el-table-column label="状态" prop="status">
                        </el-table-column> -->
                        <el-table-column prop="status" label="状态"  align="left">
                                    <template slot-scope="scope">
                                        <el-tag type="success" size="default" v-if="scope.row.status == 1">导出完成</el-tag>
                                        <el-tag type="warning" size="default" v-if="scope.row.status == 2">导出中</el-tag>
                                        <el-tag type="warning" size="default" v-if="scope.row.status== 3">导出失败</el-tag>
                                        <el-tag type="danger" size="default" v-if="scope.row.status== 0">等待导出</el-tag>
                                     
                                    </template>
                                </el-table-column>

                        <!-- <el-table-column label="内容" prop="scope" align="left">
                            <template slot-scope="scope">
                                <div>{{scope.row.error_msg?scope.row.error_msg:"--"}}</div>
                            </template>
                        </el-table-column> -->
                        <!-- <el-table-column label="客户id" width="80" prop="client_id" align="left">

                        </el-table-column> -->
                        <el-table-column label="创建时间" prop="created_at" align="left">

                        </el-table-column>
                        <el-table-column label="操作" width="100" align="center" scope="scope">
                            <template slot-scope="scope">
                                <el-link v-if="scope.row.status == 1"  type="primary" @click="down_item(scope.row.url)" style="margin:0  10px;">下载</el-link>
                            </template>
                        </el-table-column>

                    </el-table>
      <!-- 分页 -->
      <div class="block">
        <div>
            <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
        </div>
      <el-pagination
        background
        layout="prev,pager,next"
        :total="telinfoTotal"
        @current-change="handleCurrentChange"
        :current-page="telinfo_params.page"
        :page-size="telinfo_params.per_page"
      >
      </el-pagination>
    </div>
                </div>
            </el-col>
            <!-- <el-col v-if="!auth_transaction &&!showAdd &loadEnd" :span="24">
          <myEmpty desc="当前用户不可查看"></myEmpty>
        </el-col> -->
        </el-row>

    </div>
</template>
  
<script>

export default {
    name: "crm_customer_task_list",
    components: {
        // myLabel,

    },
    data() {
        return {
            telinfo_params: {
                page: 1,
                status:0,
                per_page: 10,
            },
            status:"",
            tableData: [],
            task_id: 0,
            website_id: 0,
            telinfoTotal:0,
            loading:false,

        };
    },
    mounted() {
        if (this.$route.query.website_id) {
            this.website_id = this.$route.query.website_id
        }
        if (this.$route.query.information) {
            this.information = this.$route.query.information
        }
        if (this.$route.query.task_id) {
            this.task_id = this.$route.query.task_id*1

            if(this.$route.query.information&&this.$route.query.information==1){
                this.informationrecord()
            }else{
                this.getData();
            }

        }
        console.log(this.$route.query)

    },
    created() {
   
  },
    computed: {
        
    },
    methods: {
        // 拨打记录-直拨记录(crm操作记录)
        getData() {
            let item = {
                page: this.telinfo_params.page,
                per_page: this.telinfo_params.per_page,
            }
            let id = this.task_id
            console.log(item)
            this.loading = true
            this.$ajax.house.getxeportlistdetails(id,item).then((res) => {
                if (res.status == 200) {
                    this.loading = false
                    this.tableData = res.data.data;
                    // this.telinfo_params.page = res.data.current_page
                    // this.telinfo_params.per_page = res.data.per_page
                    this.telinfoTotal = res.data.total
                }
            });
        },
        //流转客的记录
        informationrecord(){
            let item = {
                page: this.telinfo_params.page,
                per_page: this.telinfo_params.per_page,
            }
            console.log(item);
            let id = this.task_id
            this.loading = true
            this.$http.newinformationexportlist(id,item).then((res) => {
                if (res.status == 200) {
                    this.loading = false
                    this.tableData = res.data.data;
                    this.telinfoTotal = res.data.total
                }
            });
        },
        //刷新按钮，刷新表格
        Refresh(){
            if(this.information==1){
               this.informationrecord() 
            }else{
                this.getData()
            }
        },
        // 分页器-当前页
        handleCurrentChange(val) {
            this.telinfo_params.page = val
               if(this.information==1){
               this.informationrecord() 
            }else{
                this.getData()
            }
            console.log(`当前页: ${val}`);
        },
       toAllTaskList(){
        let url = `/crm_customer_export_list`;
          this.$goPath(url); // 跳转客户详情
       },
       Search_Status(){
        if(this.status==""||this.status==undefined){
            this.$message.warning("请选择状态")
            return
        }else{
            this.telinfo_params.status = this.status
            this.getData()
        }

       },
        //下载
        down_item(url){
            console.log(url);
            window.open(`https://${url}`)
        },

    },
};
</script>
  
<style scoped lang="scss">
.task_list {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 24px;

    .bottom-border {
        align-items: center;
        padding-bottom: 24px;
        justify-content: flex-start;
        border-bottom: 1px dashed #e2e2e2;

        .text {
            font-size: 14px;
            color: #8a929f;

            .label {
                width: 70px;
                display: inline-block;
                text-align: right;
            }
        }
    }
    .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    margin: -24px -24px 24px;
    justify-content: space-between;
    align-items: center;
    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }
  .block{
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
}
}


</style>
  