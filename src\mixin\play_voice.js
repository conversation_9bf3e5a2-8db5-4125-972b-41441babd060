export default {
  data() {
    return {
      current_voice_index: -1,
    };
  },
  created() {
    this.audio = new Audio();
  },
  methods: {
    /**
     * 播放语音
     */
    playVoice(chat, index) {
      if (this.current_voice_index === index) {
        this.current_voice_index = -1;
        this.audio.pause();
        return;
      }
      this.current_voice_index = index;
      this.audio.src = chat.content.voice;
      this.audio.play();
    },
  },
  destroyed() {
    this.audio.pause();
    this.audio = null;
  },
};