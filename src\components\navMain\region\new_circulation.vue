<template>
    <div class="header_catalogue">
        <div class="new_overall">
            <div class="new_one">
                <div class="Basic_Settings">
                    <div class="Settings_one" :class="color_one">
                        <span @click="ettings_one">基础设置 &nbsp;(第一步)</span>
                    </div>
                    <div class="Settings_two" :class="color_two">
                        <span @click="Settings_two">基础设置 &nbsp;(第二步)</span>
                    </div>
                    <div class="Settings_two" :class="color_three">
                        <span @click="Settings_three">红包设置 &nbsp;(第三步)</span>
                    </div>
                    <div class="Settings_Optional" :class="color_four">
                        <span @click="Settings_Optional">榜单设置 &nbsp;(可选)</span>
                    </div>
                </div>
                <div class="Edit_box" v-show="show_one">
                    <div class="editor">
                        <div style="border: 1px solid #ccc">
                            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                                :mode="mode" />
                            <Editor style="height: 380px; overflow-y: hidden" v-model="html" :defaultConfig="editorConfig"
                                :mode="mode" @onCreated="onCreated" />
                        </div>
                    </div>
                    <div class="background_music">
                        <span>背景音乐</span>
                        <div>
                            <div class="music_left">
                                <div class="music_center">

                                </div>
                            </div>
                            <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                                :on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove" multiple
                                :limit="3" :on-exceed="handleExceed">
                                <div class="music_right">
                                    <span>替换</span>
                                </div>
                            </el-upload>
                        </div>
                    </div>
                    <div class="next_step">
                        <el-button type="primary" @click="Settings_two">下一步</el-button>
                    </div>
                </div>
                <div class="Forward_Title" v-show="show_two">
                    <div class="Forward_Title_scope">
                        <div>
                            <span>标题</span>
                            <el-input v-model="input" placeholder="请在这里输入"></el-input>
                            <!-- <span class="span1">用户转发时显示的标题</span> -->
                        </div>
                        <div>
                            <span>电话</span>
                            <el-input v-model="input" placeholder="请在这里输入"></el-input>
                            <!-- <span class="span1">用户转发时显示的描述</span> -->
                        </div>
                        <div>
                            <el-form ref="form" :model="form" label-width="80px">
                                <el-form-item label="开始时间">
                                    <el-input v-model="form.start_time"></el-input>
                                </el-form-item>
                            </el-form>
                            <el-form ref="form" :model="form" label-width="80px">
                                <el-form-item label="结束时间">
                                    <el-input v-model="form.end_time"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                        <div class="disjunctor">
                            <div class="position">
                                <div class="tagging">红包是否开启</div>
                                <div>
                                    <el-switch v-model="value1"></el-switch>
                                </div>
                            </div>
                            <div class="position">
                                <div class="tagging">是否禁用海报</div>
                                <div>
                                    <el-switch v-model="value2"></el-switch>
                                </div>
                            </div>
                        </div>
                        <!-- <div>
                            <span>底部导航</span>
                            <el-select v-model="select" slot="prepend" placeholder="请选择"> -->
                        <!-- <el-option label="餐厅名" value="1"></el-option>
                                <el-option label="订单号" value="2"></el-option>
                                <el-option label="用户电话" value="3"></el-option> -->
                        <!-- </el-select>
                            <span class="span1">是否显示导航</span>
                        </div> -->
                        <!-- <div>
                            <span>绑定楼盘</span>
                            <el-select v-model="select" slot="prepend" placeholder="请选择"> -->
                        <!-- <el-option label="餐厅名" value="1"></el-option>
                                <el-option label="订单号" value="2"></el-option>
                                <el-option label="用户电话" value="3"></el-option> -->
                        <!-- </el-select>
                            <span class="span1">选择绑定的楼盘</span>
                        </div> -->
                    </div>
                    <div class="Forward_Title_button">
                        <el-button type="primary" plain @click="ettings_one">上一步</el-button>
                        <el-button type="primary" @click="Settings_three">下一步</el-button>
                    </div>
                </div>
                <div class="Red_envelope_setting" v-show="show_three">
                    <div class="Forward_Title_scope">
                        <!-- <span>红包使用方案</span> -->
                        <!-- <el-select v-model="select" slot="prepend" placeholder="请选择"> -->
                        <!-- <el-option label="餐厅名" value="1"></el-option>
                            <el-option label="订单号" value="2"></el-option>
                            <el-option label="用户电话" value="3"></el-option> -->
                        <!-- </el-select>
                        <span class="span">管理员控制发放规则，类型保存后不可修改</span> -->
                    </div>
                    <div class="Forward_Title_least">
                        <div class="minimum_all">
                            <div class="minimum">
                                <div>
                                    <div class="minimum_span">
                                        <span>单笔最小红包</span>
                                    </div>
                                    <div class="minimum_first">
                                        <div class="first" style="height: 30px">
                                            <el-input placeholder="请输入内容" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="minimum_span">
                                        <span>阅读单笔最小红包</span>
                                    </div>
                                    <div class="minimum_first">
                                        <div class="first" style="height: 30px">
                                            <el-input placeholder="请输入内容" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="minimum_span">
                                        <span>首推单笔最小红包</span>
                                    </div>
                                    <div class="minimum_first">
                                        <div class="first" style="height: 30px">
                                            <el-input placeholder="请输入内容" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="maximum">
                                <div>
                                    <div class="minimum_span">
                                        <span>单笔最大红包</span>
                                    </div>
                                    <div class="minimum_first">
                                        <div class="first" style="height: 30px">
                                            <el-input placeholder="请输入内容" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="minimum_span">
                                        <span>阅读单笔最大红包</span>
                                    </div>
                                    <div class="minimum_first">
                                        <div class="first" style="height: 30px">
                                            <el-input placeholder="请输入内容" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="minimum_span">
                                        <span>首推单笔最大红包</span>
                                    </div>
                                    <div class="minimum_first">
                                        <div class="first" style="height: 30px">
                                            <el-input placeholder="请输入内容" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="Send_daily">
                            <div class="each">
                                <div class="each_day">
                                    <span>每日发送</span>
                                </div>
                                <div class="minimum_first">
                                    <div class="first">
                                        <el-input placeholder="请输入" v-model="input2">
                                            <template slot="append">元</template>
                                        </el-input>
                                    </div>
                                </div>
                            </div>
                            <div class="first_launch">
                                <span>首推红包后，随机出现</span>
                                <div>
                                    <div class="minimum_first">
                                        <div class="first">
                                            <el-input placeholder="请输入" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                    —
                                    <div class="minimum_first">
                                        <div class="first">
                                            <el-input placeholder="请输入" v-model="input2">
                                                <template slot="append">元</template>
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                                <span> 随机大额红包 </span>
                            </div>
                        </div> -->
                        <!-- <div class="propose">
                            <span>建议：如果您充值金额较大，建议您先设置较高金额的首推、阅读等红包，积攒足够的传播种子用户后，再调低红包金额。即：先高后低原则。</span>
                        </div> -->
                    </div>
                    <div class="Forward_Title_button">
                        <el-button type="primary" plain @click="Settings_two">上一步</el-button>
                        <el-button type="primary">下一步</el-button>
                    </div>
                </div>
            </div>
            <div class="new_png">
                <div class="Activity_Title">
                    <span>活动标题</span>
                </div>
                <div class="Activity_photos"></div>
                <div class="Activity_renovate">
                    <el-button type="primary" plain>刷新</el-button>
                    <el-button type="primary">扫码预览</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
export default {
    components: { Editor, Toolbar },
    data() {
        return {
            value1: "",
            value2: "",
            input2: '',
            input: '',
            select: '',
            editor: null,
            html: '',
            toolbarConfig: {},
            editorConfig: { placeholder: '请输入内容测试...' },
            mode: 'default', // or 'simple'
            color_one: 'c1',
            color_two: 'c2',
            color_three: 'c2',
            color_four: 'c2',
            show_one: true,
            show_two: false,
            show_three: false,
            form:{
                start_time:"",
                end_time:""

            }
        }
    },
    methods: {
        onCreated(editor) {
            this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
        },
        ettings_one() {
            this.color_one = "c1"
            this.color_two = "c2"
            this.color_three = "c2"
            this.color_four = "c2"
            this.show_one = true
            this.show_two = false
            this.show_three = false
            // this.show_four =false
        },
        Settings_two() {
            this.color_one = "c2"
            this.color_two = "c1"
            this.color_three = "c2"
            this.color_four = "c2"
            this.show_one = false
            this.show_two = true
            this.show_three = false
            // this.show_four =false
        },
        Settings_three() {
            this.color_one = "c2"
            this.color_two = "c2"
            this.color_three = "c1"
            this.color_four = "c2"
            this.show_one = false
            this.show_two = false
            this.show_three = true
            // this.show_four =false
        },
        Settings_Optional() {
            this.color_one = "c2"
            this.color_two = "c2"
            this.color_three = "c2"
            this.color_four = "c1"
            this.show_one = false
            this.show_two = false
            this.show_three = false
            // this.show_four =true
        },
        handleRemove(file, fileList) {
            console.log(file, fileList);
        },
        handlePreview(file) {
            console.log(file);
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        beforeRemove(file) {
            return this.$confirm(`确定移除 ${file.name}？`);
        }
    },
    mounted() {
        // 模拟 ajax 请求，异步渲染编辑器
        // setTimeout(() => {
        //     this.html = '<p>模拟 Ajax 异步设置内容 HTML</p>'
        // }, 1500)
    },
    beforeDestroy() {
        const editor = this.editor
        if (editor == null) return
        editor.destroy() // 组件销毁时，及时销毁编辑器
    }
}
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style scoped lang="scss" >
.header_catalogue {
    background: #f1f4fa;
    margin: -14px;
    padding: 24px;

    .new_overall {
        width: 96%;
        height: 750px;
        margin: 10px auto;
        display: flex;
        justify-content: space-between;

        .new_one {
            width: 65%;
            height: 770px;
            background-color: #fff;

            .Basic_Settings {
                width: 100%;
                height: 60px;
                background-color: rgb(232, 237, 246);
                display: flex;

                .c1 {
                    border-color: transparent transparent #fff;
                }

                .c2 {
                    border-color: transparent transparent rgb(232, 237, 246);
                }

                .Settings_one {
                    text-align: center;
                    width: 250px;
                    border-width: 0px 50px 60px 0px;
                    border-style: none solid solid;
                }

                .Settings_two {
                    text-align: center;
                    width: 250px;
                    border-width: 50px 50px 60px 50px;
                    border-radius: 4px;
                    border-style: none solid solid;
                }

                span {
                    line-height: 60px;
                    color: #2d84fb;
                    position: relative;
                    cursor: pointer;
                }

                .Settings_Optional {
                    text-align: center;
                    width: 250px;
                    border-width: 0px 0px 60px 50px;
                    border-style: none solid solid;

                    span {
                        line-height: 60px;
                        color: #2d84fb;
                        position: relative;
                    }
                }
            }

            .Edit_box {
                width: 900px;
                height: 680px;
                // background-color: #782f2f;
                margin: 10px auto;
                overflow: hidden;

                .editor {
                    width: 900px;
                    height: 380px;
                    background-color: cornflowerblue;
                    margin: 20px auto;
                }

                .background_music {
                    width: 900px;
                    height: 130px;
                    border: 1px solid #fff;
                    border-bottom-color: #dde1e9;
                    //   background-color: #fe6c17;
                    margin: 0 auto;
                    margin-top: 100px;

                    div {
                        display: flex;

                    }

                    .music_left {
                        width: 90px;
                        height: 80px;
                        background-color: #F1F4FA;
                        margin-top: 10px;

                        .music_center {
                            width: 45px;
                            height: 45px;
                            background-color: #FFBF9A;
                            margin: 17px auto;
                            border-radius: 4px;
                        }
                    }

                    .music_right {
                        width: 90px;
                        height: 80px;
                        background-color: #F1F4FA;
                        margin-top: 10px;
                        margin-left: 10px;

                        span {
                            margin: 25px auto;
                        }
                    }
                }

                .next_step {
                    width: 900px;
                    height: 70px;
                    //    background-color: aquamarine;
                    margin: 0 auto;
                    display: flex;
                    justify-content: flex-end;

                    .el-button {
                        width: 90px;
                        height: 35px;
                        line-height: 0;
                        margin-top: 10px;
                    }
                }
            }

            .Forward_Title {
                width: 900px;
                height: 630px;
                // background-color: #b02222;
                margin: 30px auto;

                .Forward_Title_scope {
                    width: 900px;
                    height: 580px;
                    // background-color: #2D84FB;
                    border: 1px solid #fff;
                    border-bottom-color: #dde1e9;

                    /deep/.el-input__inner {
                        width: 410px;
                        height: 36px;
                        margin-left: 41px;
                        margin-top: 20px;
                    }

                    /deep/.el-input {
                        width: 450px;
                    }

                    // div {
                    //     display: flex;
                    //     line-height: 70px;
                    //     text-align: center;

                    //     span {
                    //         color: #2e3c4e;
                    //     }

                    //     .span1 {
                    //         margin-left: 20px;
                    //         color: #8a929f;
                    //         font-size: 14px;
                    //     }
                    // }

                    .disjunctor {
                        width: 500px;
                        height: 70px;
                        // background-color: palegreen;
                        margin-top: 30px;
                        // display: flex;
                        // justify-content: space-between;
                    }

                    .position {
                        width: 150px;
                        height: 50px;
                        // background-color: plum;
                        display: flex;
                        margin-top: 20px;
                        justify-content: space-between;
                    }
                }

                .Forward_Title_button {
                    width: 900px;
                    height: 40px;
                    margin-top: 10px;
                    display: flex;
                    justify-content: flex-end;
                }
            }

            .Red_envelope_setting {
                width: 900px;
                height: 630px;
                // background-color: #b02222;
                margin: 30px auto;

                .Forward_Title_scope {
                    width: 900px;
                    height: 50px;
                    // background-color: #2D84FB;

                    /deep/.el-input__inner {
                        height: 32px;
                        margin-left: 20px;
                    }

                    /deep/.el-input__icon {
                        line-height: 33px;
                    }

                    span {
                        color: #2e3c4e;
                    }

                    .span {
                        margin-left: 50px;
                        color: #fe6c17;
                        font-size: 14px;
                    }
                }

                .Forward_Title_least {
                    width: 900px;
                    height: 480px;
                    border: 1px solid #fff;
                    border-bottom-color: #dde1e9;

                    .minimum_all {
                        display: flex;
                        justify-content: space-between;
                    }

                    .minimum {
                        width: 420px;
                        height: 130px;
                        margin-top: 50px;

                        div {
                            width: 400px;
                            height: 50px;
                            display: flex;
                        }

                        .minimum_span {
                            width: 150px;
                            height: 32px;
                            line-height: 30px;
                            color: #2e3c4e;
                        }

                        .minimum_first {
                            width: 260px;
                            height: 32px;
                        }

                        .first {
                            width: 180px;
                            height: 30px;
                            border: 1px solid #dcdfe6;
                            border-radius: 4px;
                            margin-left: 30px;
                            line-height: 30px;

                            /deep/.el-input__inner {
                                height: 30px;
                                line-height: 0px;
                                border: none;
                            }

                            /deep/.el-input-group__append {
                                height: 30px;
                                border: none;
                                line-height: 30px;
                                background-color: #fff;
                            }
                        }
                    }

                    .maximum {
                        width: 420px;
                        height: 130px;
                        margin-top: 50px;

                        // background-color: #FE6C17;
                        div {
                            width: 400px;
                            height: 50px;
                            display: flex;
                        }

                        .minimum_span {
                            width: 150px;
                            height: 32px;
                            line-height: 30px;
                            color: #2e3c4e;
                        }

                        .minimum_first {
                            width: 260px;
                            height: 32px;
                        }

                        .first {
                            width: 180px;
                            height: 30px;
                            border: 1px solid #dcdfe6;
                            border-radius: 4px;
                            margin-left: 30px;
                            line-height: 30px;

                            /deep/.el-input__inner {
                                height: 30px;
                                line-height: 0px;
                                border: none;
                            }

                            /deep/.el-input-group__append {
                                height: 30px;
                                border: none;
                                line-height: 30px;
                                background-color: #fff;
                            }
                        }
                    }

                    .Send_daily {
                        width: 900px;
                        height: 50px;
                        margin-top: 25px;
                        // background-color: #FE6C17;
                        display: flex;

                        .each {
                            width: 200px;
                            height: 50px;
                            // background-color: #2D84FB;
                            display: flex;
                        }

                        .each_day {
                            width: 100px;
                            height: 20px;
                            margin-top: 7px;
                        }

                        .first {
                            width: 130px;
                            height: 30px;
                            border: 1px solid #dcdfe6;
                            border-radius: 4px;

                            /deep/.el-input__inner {
                                border: none;
                                // width: 120px;
                                height: 30px;
                            }

                            /deep/.el-input-group__append {
                                height: 30px;
                                border: none;
                                line-height: 30px;
                                background-color: #fff;
                            }
                        }

                        .first_launch {
                            width: 600px;
                            height: 50px;
                            // background-color: #2D84FB;
                            display: flex;

                            div {
                                display: flex;
                                // margin-left: 10px;
                            }

                            span {
                                margin-top: 7px;
                                margin-left: 20px;
                                margin-right: 10px;
                            }

                            /deep/.el-input__inner {
                                // width: 120px;
                                // height: 34px;
                                border: none;
                            }

                            /deep/.el-input-group__append {
                                height: 30px;
                                border: none;
                                line-height: 30px;
                                background-color: #fff;
                            }
                        }
                    }

                    .propose {
                        width: 900px;
                        height: 30px;
                        // background-color: #17fe17;
                        color: #fe6c17;
                        font-size: 12px;
                    }
                }

                .Forward_Title_button {
                    width: 900px;
                    height: 40px;
                    margin-top: 10px;
                    display: flex;
                    justify-content: flex-end;
                }
            }
        }

        .new_png {
            width: 30%;
            height: 750px;
            background-color: #fff;

            .Activity_Title {
                width: 400px;
                height: 50px;
                border: 1px solid #f1f4fa;
                margin: 0px auto;
                border-radius: 4px;
                margin-top: 20px;
                text-align: center;
                line-height: 50px;
            }

            .Activity_photos {
                width: 400px;
                height: 600px;
                margin: 0 auto;
                // background-color: #2D84FB;
            }

            .Activity_renovate {
                width: 400px;
                height: 50px;
                margin: 10px auto;
                // background-color: #FE6C17;
                display: flex;
                justify-content: space-between;

                .el-button {
                    width: 170px;
                    height: 34px;
                    line-height: 0;
                }
            }
        }
    }
}
</style>