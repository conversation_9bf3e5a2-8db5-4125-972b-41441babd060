<template>
  <el-container>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->
    <el-main>
      <div class="content div row">
        <div class="left">成交单号：</div>
        <div class="right">{{ order_list.sale_order_sn }}</div>
      </div>
      <div class="content div row">
        <div class="left">合同编号：</div>
        <div class="right">{{ order_list.contract_no }}</div>
      </div>
      <div class="content div row">
        <div class="left">产权地址：</div>
        <div class="right">{{ order_list.property_right_address }}</div>
      </div>
      <div class="content div row">
        <div class="left">成交时间：</div>
        <div class="right">{{ order_list.deal_at }}</div>
      </div>
      <div class="content div row">
        <div class="left">成交金额：</div>
        <div class="right">{{ order_list.deal_amount }}万</div>
      </div>
      <div class="content div row">
        <div class="left">报备会员：</div>
        <div class="right">
          {{ order_list.s_name || order_list.s_nickname }}
        </div>
      </div>
      <div class="content div row">
        <div class="left">佣金分成：</div>
        <div class="right">{{ order_list.brokerage_amount }}元</div>
      </div>
      <div class="content div row">
        <div class="left">成交备注：</div>
        <div class="right">{{ order_list.remark || "--" }}</div>
      </div>
      <div class="content div row">
        <div class="left">扫码案场：</div>
        <div v-if="order_list.scan_code_user_id !== 0" class="right">
          {{
            order_list.sc_name ||
              order_list.sc_nickname ||
              order_list.sc_user_name
          }}
        </div>
        <div class="right" v-else>暂无</div>
      </div>
      <div class="content div row">
        <div class="left">会员服务费：</div>
        <div class="right">
          {{ order_list.member_service_charge }}
        </div>
      </div>
      <div class="content div row">
        <div class="left">已收会员服务费：</div>
        <div class="right">
          {{ order_list.earning_member_service_charge }}
        </div>
      </div>
      <div class="content div row">
        <div class="left">会员服务费账期天数：</div>
        <div class="right">
          {{ order_list.customer_payment_days }}
        </div>
      </div>
      <div class="content div row">
        <div class="left">开发商佣金账期天数：</div>
        <div class="right">
          {{ order_list.project_payment_days }}
        </div>
      </div>
      <div class="content div row">
        <div class="left">跟进案场：</div>
        <div v-if="order_list.follow_up_user_id !== 0" class="right">
          {{
            order_list.fu_name ||
              order_list.fu_nickname ||
              order_list.fu_user_name
          }}
        </div>
        <div class="right" v-else>暂无</div>
      </div>
    </el-main>
  </el-container>
</template>

<script>
export default {
  name: "deal_orde",
  data() {
    return {
      customer_id: "",
      order_list: {},
    };
  },
  mounted() {
    this.customer_id = this.$route.query.id;
    this.getQueryOrder();
  },

  methods: {
    getQueryOrder() {
      this.$http.QueryOrder(this.customer_id).then((res) => {
        if (res.status === 200) {
          this.order_list = res.data;
        }
      });
    },
    goBack() {
      this.$router.back();
    },
  },
};
</script>

<style scoped lang="scss">
.content {
  padding-bottom: 20px;
  padding-top: 20px;
  border-bottom: 1px solid #f0f0f0;
  .right {
    color: #999;
  }
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-main {
// 	padding-top: 80px;
// }
</style>
