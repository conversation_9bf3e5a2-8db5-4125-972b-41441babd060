<template>
    <el-dialog  title="绑定资料包" :visible.sync="show" width="700px" :modal="false">
        <el-table
            :data="tableData"
            style="width: 100%"
            :header-cell-style="{ background: '#EBF0F7' }"
            border
            v-loading="is_table_loading"
            @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="title"
              label="标题">
            </el-table-column>
            <el-table-column
              prop="created_at"
              label="创建时间">
            </el-table-column>
        </el-table>


        <span slot="footer" class="dialog-footer"> 
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
        </span> 
    </el-dialog>
</template>
<script>
export default {
    props:{
        tableData: {
            type: Array,
            default: () => []
        },
    },
    data() {
        return {
            show:false,
            submiting:false,
            is_table_loading:false,
            create_form:"",//选中的资料id
        }
    },
    methods:{
        //打开弹窗
        open(){
          this.show = true;
          return this
        },
        //选中的资料
        handleSelectionChange(e){
            this.create_form = e.map((item) => {
                return item.id;
            });
        },
        //保存
        submit(){
            if(this.create_form.length){
                this.$emit("informationids",this.create_form)
                this.show = false;
            }else{
                this.$message.warning("请选择资料！")
            }
          
        },
        //取消
        cancel(){
            this.show = false
        },
    },

}
</script>