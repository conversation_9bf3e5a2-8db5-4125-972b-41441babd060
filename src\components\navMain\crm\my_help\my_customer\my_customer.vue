<template>
  <!-- 客户列表 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <span class="text">类型：</span>
            <myLabel
              :arr="type_list"
              @onClick="onClickType"
              labelKey="title"
            ></myLabel>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">创建时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="addTime"
              type="daterange"
              range-separator="至"
              @change="onChangeTime"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px">
            <span class="text">客户标签：</span>
            <el-cascader
              clearable
              size="small"
              v-model="params.tag_id"
              :options="tag_list"
              :props="{
                value: 'tagid',
                label: 'name',
                children: 'taggroup',
                emitPath: false,
              }"
              @change="onChangeKeywords"
            ></el-cascader>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right">
              <!-- <el-button
                style="margin-left: 10px"
                type="primary"
                @click="onClickGetPull"
                size="mini"
                >拉取客户</el-button
              > -->
              <el-input
                size="small"
                placeholder="请输入搜索内容"
                style="width: 256px; margin-left: 12px"
                v-model="params.keyword"
                @change="onChangeKeywords"
              >
                <span slot="append" class="el-icon-search"></span>
              </el-input>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              label="记录ID"
              width="100"
              prop="id"
            ></el-table-column>
            <el-table-column label="客户名称" v-slot="{ row }">
              <div class="div row img-box">
                <img v-if="row.avatar" :src="row.avatar" alt="" />
                <div class="round" v-else>{{ row.name[0] }}</div>
                <el-link
                  class="userid"
                  type="primary"
                  @click="$onCopyValue(row.external_userid || row.userid)"
                  >{{ row.name }}</el-link
                >
                <span v-if="row.type == 1" style="color: #1aad19">@微信</span>
                <span v-if="row.type == 2" style="color: #ffbe3a"
                  >@{{ row.corp_name }}</span
                >
              </div>
            </el-table-column>
            <el-table-column
              v-if="false"
              label="公司名称"
              prop="corp_name"
              v-slot="{ row }"
            >
              {{ row.corp_name || "--" }}
            </el-table-column>
            <!-- <el-table-column label="系统ID" prop="website_id"></el-table-column> -->
            <el-table-column prop="type" label="类型" v-slot="{ row }">
              <span v-if="row.type == 1">微信用户</span>
              <span v-if="row.type == 2">企业微信用户</span>
              <el-tag
                style="margin-left: 5px"
                type="success"
                v-if="row.unionid"
                >{{ website_info.name }}</el-tag
              >
              <el-tag
                style="margin-left: 5px"
                type="success"
                v-if="row.agent_tag.length > 0"
              >
                {{ row.agent_tag[0].name }}
              </el-tag>
            </el-table-column>
            <el-table-column label="添加人" prop="user.name"></el-table-column>
            <el-table-column label="crm" v-slot="{ row }">
              <span v-if="row.relation_crm == 0" style="color: red"
                >未关联</span
              >
              <span
                v-if="row.relation_crm > 0"
                @click="goClientDetail(row)"
                style="cursor: pointer; color: #409eff"
                >已关联</span
              >
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev,pager,next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          ></el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import myLabel from "@/components/navMain/crm/components/my_label";
export default {
  name: "my_customer",
  components: {
    myLabel,
  },
  computed: {
    website_info() {
      return this.$store.state.website_info || {}
    }
  },
  data() {
    return {
      type_list: [],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
      ],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keyword: "",
        add_time: "",
        tag_id: "",
        add_way: "",
      },
      multipleSelection: [],
      is_table_loading: false,
      tableData: [],
      tag_list: [],
      pull_params: {
        next_user_cursor: 0,
        next_customer_cursor: "",
      },
      addTime: [],
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    onChangeTime(e) {
      this.params.add_time = e ? e.join(",") : 0;
      this.params.page = 1;
      this.getDataList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onClickType(e) {
      this.params.add_way = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onClickTime(e) {
      this.params.add_time = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },

    getDataList() {
      if (!this.params.add_way) {
        delete this.params.add_way;
      }
      this.is_table_loading = true;
      this.$http.getMyCustomerList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    onClickGetPull() {
      // 拉取企微客户
      this.$message.success("正在拉取客户...");
      this.$http
        .getCrmWxworkPullData({ params: this.pull_params })
        .then((res) => {
          if (res.status === 200) {
            // 如果返回参数接着拉取
            // if (res.data) {
            //   this.pull_params = res.data;
            //   this.onClickGetPull();
            // } else {
            this.$message.success("拉取完成");
            this.params.page = 1;
            this.getDataList();
            // }
          } else {
            this.$message.error("拉取失败！");
          }
        });
    },
    //已关联跳转到客户详情
    goClientDetail(row) {
      let url = `/crm_customer_detail?id=${row.relation_crm}&type=seas`;
      this.$goPath(url);
    }
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .img-box {
    align-items: center;
    img {
      width: 30px;
      height: 30px;
      border-radius: 50%;
    }
    .round {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      color: #fff;
      background: #0271ff;
    }
    .userid {
      color: #0271ff;
      margin-left: 14px;
    }
  }
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px dashed #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
      width: 80px;
    }
  }
  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }
}
</style>
