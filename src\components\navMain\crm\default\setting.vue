<template>
  <div>
    <el-form
      v-loading="is_show"
      v-if="is_show_crm"
      :model="form_info"
      label-width="120px"
    >
      <div v-for="item in form_list" :key="item.id">
        <template v-if="item.type != 1">
        <!-- 人事权限范围 -->
          <el-form-item
            v-if="item.key === 'personnel_auth'"
            :label="item.descp + '：'"
          >
            <!-- <mySelect
              :optionSource="user_list"
              v-model="item.value"
              labelKey="user_name"
              valueKey="id"
              :multiple="item.type == 2 ? false : true"
              width="300px"
              @page-change="onPageChange"
              :paginationOption="{
                pageSize: params.per_page, //每页显示条数
                currentPage: params.page, //当前页
                pagerCount: 5, //按钮数，超过时会折叠
                total: params.total, //总条数
              }"
            ></mySelect> -->
            <el-select
              ref="Personnel"
              style="width: 300px"
              v-model="item.value"
              multiple
              placeholder="请选择"
              @focus="showPersonnelAuthority(item)"
              @change="PersonnelChange"
            >
              <el-option
                :disabled="true"
                v-for="list in datalist"
                :key="list.id"
                :label="list.user_name"
                :value="list.id"
              >
              </el-option>
            </el-select>
            <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">拥有人事通讯录团队成员管理、成员增删修改、设置部门、角色权限。</div>
          <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "></i>
        </el-tooltip>
          </el-form-item>
          <el-form-item
            v-else-if="item.key === 'default_department'"
            :label="item.descp + '：'"
          >
            <el-cascader
              style="width: 300px"
              :options="memberList"
              :props="{
                value: 'id',
                label: 'name',
                children: 'subs',
                multiple: false,
              }"
              clearable
              v-model="item.value"
            ></el-cascader>
          </el-form-item>
          <el-form-item
            v-else-if="item.key === 'default_role'"
            :label="item.descp + '：'"
          >
            <el-select
              style="width: 300px"
              v-model="item.value"
              :multiple="item.type == 2 ? false : true"
              placeholder="请选择"
            >
              <el-option
                v-for="i in roles_list"
                :key="i.id"
                :label="i.name === '站长' ? '创始人' : i.name"
                :value="i.name"
                :disabled="i.name === '站长'"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 成交权限范围 -->
          <el-form-item
            v-else-if="item.key === 'deal_auth'"
            :label="item.descp + '：'"
          >
            <el-select
              ref="Personnel"
              style="width: 300px"
              v-model="item.value"
              multiple
              placeholder="请选择"
              @focus="showPersonnelAuthority(item)"
              @change="PersonnelChange"
            >
              <el-option
                :disabled="true"
                v-for="list in datalist"
                :key="list.id"
                :label="list.user_name"
                :value="list.id"
              >
              </el-option>
            </el-select>
            <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">拥有成交报告管理、添加、作废、数据导出权限。</div>
          <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "></i>
        </el-tooltip>
          </el-form-item>
          <!-- 财务权限范围 -->
          <el-form-item
            v-else-if="item.key === 'finance_auth'"
            :label="item.descp + '：'"
          >
            <el-select
              ref="Personnel"
              style="width: 300px"
              v-model="item.value"
              multiple
              placeholder="请选择"
              @focus="showPersonnelAuthority(item)"
              @change="PersonnelChange"
            >
              <el-option
                :disabled="true"
                v-for="list in datalist"
                :key="list.id"
                :label="list.user_name"
                :value="list.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 平台配置权限范围 -->
          <el-form-item
            v-else-if="item.key === 'config_auth_uid'"
            :label="item.descp + '：'"
          >
            <el-select
              ref="Personnel"
              style="width: 300px"
              v-model="item.value"
              multiple
              placeholder="请选择"
              @focus="showPersonnelAuthority(item)"
              @change="PersonnelChange"
            >
              <el-option
                :disabled="true"
                v-for="list in datalist"
                :key="list.id"
                :label="list.user_name"
                :value="list.id"
              >
              </el-option>
            </el-select>
            
        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">【 谨慎添加 !】 仅次于创始人帐号，可进入并获取系统核心权限配置。</div>
          <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "></i>
        </el-tooltip>
          </el-form-item>
          <el-form-item
            v-else-if="
              item.key === 'house_change_radio' ||
              item.key === 'client_change_radio' ||
              item.key === 'client_follow_radio' ||
              item.key === 'approver_radio'
            "
            :label="item.descp + '：'"
          >
            <el-radio v-model="item.value" label="0">关闭</el-radio>
            <el-radio v-model="item.value" label="1"
              >开启</el-radio
            ></el-form-item
          >
          <el-form-item
            v-else-if="item.key == 'website_name'"
            :label="item.descp + '：'"
          >
            <el-input
              style="width: 300px"
              v-model="item.value"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item v-else :label="item.descp + '：'">
            <el-select
              style="width: 300px"
              v-model="item.value"
              :multiple="item.type == 2 ? false : true"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="i in user_list"
                :key="i.id"
                :label="i.user_name"
                :value="i.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
      </div>
      <!-- <el-form-item label="人事权限范围：">
        <mySelect
          :optionSource="user_list"
          v-model="form_info.personnel_auth"
          labelKey="user_name"
          valueKey="id"
          multiple
          width="300px"
          @page-change="onPageChange"
          :paginationOption="{
            pageSize: params.per_page, //每页显示条数
            currentPage: params.page, //当前页
            pagerCount: 5, //按钮数，超过时会折叠
            total: params.total, //总条数
          }"
        ></mySelect>
      </el-form-item>
      <el-form-item label="财务权限范围：">
        <mySelect
          :optionSource="user_list"
          v-model="form_info.financial_auth"
          labelKey="user_name"
          valueKey="id"
          multiple
          width="300px"
          @page-change="onPageChange"
          :paginationOption="{
            pageSize: params.per_page, //每页显示条数
            currentPage: params.page, //当前页
            pagerCount: 5, //按钮数，超过时会折叠
            total: params.total, //总条数
          }"
        ></mySelect>
      </el-form-item> -->
      <el-form-item label="企业微信授权：" class="WXauthorization">
        <div class="WXbox">
          <div class="label" v-if="website_info.crm_auth">已授权</div>
          <div class="label red" @click="getQyWxAppid" v-else>未授权</div>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onClickForm">确认</el-button>
      </el-form-item>
    </el-form>
    <myEmpty v-else desc="当前用户不可查看"></myEmpty>
    <el-dialog
      :visible.sync="show_add_member"
      width="400px"
      :title="department_title"
      append-to-body
    >
      <div class="member">
        <multipleTree
          v-if="show_add_member"
          :list="serverData"
          :defaultValue="selectedIds"
          @onClickItem="selecetedMember"
          :defaultExpandAll="false"
          ref="memberList"
        >
        </multipleTree>
        <div
          style="margin-top: 20px; justify-content: space-around"
          class="footer flex-row align-center"
        >
          <el-button type="text" @click="show_add_member = false"
            >取消</el-button
          >
          <el-button type="primary" @click="selectMemberOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import mySelect from "@/components/navMain/crm/components/my_select.vue";
import myEmpty from "@/components/components/my_empty.vue";
import { mapState } from "vuex";
// import memberList from "@/components/navMain/crm/components/memberList.vue";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
  components: {
    // mySelect,
    myEmpty,
    // memberList,
    multipleTree,
  },
  data() {
    return {
      form_info: {
        // financial_auth: [], //财务权限范围
      },
      params: {
        page: 1,
        per_page: 100,
        type: 4, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表 4 不包含创始人的权限列表
      },
      user_list: [],
      is_show_crm: true,
      form_list: [],
      roles_list: [],
      memberList: [],
      is_show: true,
      show_add_member: false, // 部门成员模态框
      selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
      selectedDeal: [], // 成交权限范围 默认勾选和展开的节点的 key 的数组
      storageKey: "",
      department_title: "",
      datalist: [], // 全部部门人员
      serverData: [], // 部门成员数据
    };
  },
  created() {
    this.getDepartment();
  },
  mounted() {
    this.getMemberList();
    this.getManagerAuthList();
    // this.getCommonSettingRoles();

    this.getWebsiteRoles();
  },
  computed: {
    ...mapState(["website_info"]),
  },
  methods: {
    getCommonSettingRoles() {
      this.$http.getCommonSettingRolesConf(1).then((res) => {
        this.is_show = false;
        if (res.status === 200) {
          console.log(res,"res")
          // this.form_info.personnel_auth = res.data.personnel_auth.split(",").map(item => Number(item))
          this.form_list = res.data.map((item) => {
            if (
              item.value &&
              (item.type == 3 || item.key == "default_department")
            ) {
              item.value = item.value.split(",").map((i) => Number(i));
              // =====
              let i = 0;
              if (item.value != [] && item.value != undefined && item.key != "default_department") {
                item.value.map((arr) => {
                  this.$nextTick(() => {
                    this.datalist.map((list) => {
                      if (arr != list.id) {
                        i++;
                        if (i == this.datalist.length) {
                          item.value.splice(item.value.indexOf(arr), 1);
                          // if(item.key == 'finance_auth') {
                          //   console.log(item.value,"观察");
                          // }
                        }
                      }
                    })
                    i = 0;
                  })
                })
              }
              // =====
            }
            // if(item.value && item.key == 'personnel_auth') {
            //   this.selectedIds = item.value;
            // } else if(item.value && item.key == 'deal_auth') {
            //   this.selectedDeal = item.value;
            // }
            return item;
          });
        }
      });
    },
    onPageChange(e) {
      this.params.page = e;
      this.getManagerAuthList();
    },
    getManagerAuthList() {
      this.$http.getManagerAuthList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.user_list = res.data.data;
        }
      });
    },
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    getWebsiteRoles() {
      this.$http.getWebsiteRoles({ per_page: 100 }).then((res) => {
        if (res.status === 200) {
          this.roles_list = res.data.data;
        }
      });
    },
    onClickForm() {
      var arr = [];
      this.form_list.forEach((item) => {
        var value = "";
        let result = [];
        // 处理key和重复的数据
        if (item.key == "personnel_auth" || item.key == "deal_auth" || item.key == "finance_auth" || item.key == "config_auth_uid") {
          Array.from(item.value).map((list) => {
            if (list.toString().length >= 6) {
              result.push(parseInt(list.toString().slice(0, 3)));
            } else {
              result.push(list);
            }
          })
          item.value = Array.from(new Set(result));
        }
        if (item.type == 2 || item.type == 4) {
          value = item.value;
        }
        if (item.type == 3) {
          value = item.value ? item.value.join(",") : "";
        }
        let obj = {
          id: item.id,
          value: value,
          key: item.key,
        };
        arr.push(obj);
      });
      var form = {
        data: arr,
      };
      this.$http.setCommonSettingRolesConf(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          // 将站点名称赋值给vuex中website_info
          if (typeof (form.data[0].value) == 'string' && form.data[0].value != "") {
            this.$store.state.website_info.name = form.data[0].value;
          }
          this.getCommonSettingRoles();
          // this.$goPath("/crm_customer_business");
        }
      });
    },
    getQyWxAppid() {
      this.$http.getWxWorkAppId().then((res) => {
        if (res.status === 200) {
          let website_id = localStorage.getItem("website_id");
          let url = encodeURIComponent(
            `https://yun.tfcs.cn/admin/?website_id=${website_id}#/loading?is_loading=1`
          );
          let qywxurl = `https://open.work.weixin.qq.com/3rdapp/install?suite_id=${res.data.app_id}&pre_auth_code=${res.data.pre_auth_code}&redirect_uri=${url}&state=qywxnew`;
          this.setNewLink(qywxurl);
        }
      });
    },
    setNewLink(url) {
      let referLink = document.createElement("a");
      referLink.href = url;
      document.body.appendChild(referLink);
      referLink.click();
      parent.removeChild(referLink);
    },
    showPersonnelAuthority(item) {
      console.log(item,'item');
      console.log(this.$refs.Personnel,'this.$refs.Personnel');
      this.storageKey = item.key;
      this.$refs.Personnel[0].blur();
      this.$refs.Personnel[1].blur();
      this.$refs.Personnel[2].blur();
      this.$refs.Personnel[3].blur();
      this.show_add_member = true;
      console.log(item);
      if (item.key == 'personnel_auth') {
        if(typeof item.value === "string"){
          this.selectedIds = item.value.split("");
        }else{
          this.selectedIds = item.value
        }
        this.department_title = '人事权限范围';
      } else if (item.key == 'deal_auth') {
        if(typeof item.value === "string"){
          this.selectedIds = item.value.split("");
        }else{
          this.selectedIds = item.value
        }
        this.department_title = '成交权限范围';
      } else if (item.key == 'finance_auth') {
        if(typeof item.value === "string"){
          this.selectedIds = item.value.split("");
        }else{
          this.selectedIds = item.value
        }
        this.department_title = '财务权限范围';
      } else if (item.key == 'config_auth_uid') {
        if(typeof item.value === "string"){
          this.selectedIds = item.value.split("");
        }else{
          this.selectedIds = item.value
        }
        this.department_title = '平台配置权限范围';
      }
    },
    // 获取部门成员列表
    async getMemberList() {
      await this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          console.log(this.serverData,'this.serverData');
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key+=父级
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      })
      this.getCommonSettingRoles();
    },
    // 递归数据处理
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    // 选中变化时触发
    selecetedMember(e) {
      this.selectedIds = e.checkedKeys;
      // this.selectedList = e.checkedNodes;
    },
    selectMemberOk() {
      this.show_add_member = false;
      // console.log(this.selectedIds,"已选中");
      this.form_list.map((item) => {
        if (this.storageKey == item.key) {
          item.value = this.selectedIds;
        }
      })
      console.log(this.form_list,'this.form_list');
    },
    PersonnelChange(val) {
      this.selectedIds = val;
    }
  },
};
</script>

<style scoped lang="scss">
::v-deep.WXauthorization {
  .el-form-item__content {
    .WXbox {
      margin: 7px 0;
    }
  }
  .label {
    width: 50px;
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    cursor: pointer;
    color: #fff;
    background: #00c800;
    border-radius: 2px;
    padding: 4px 7px;
    min-width: 50px;
    text-align: center;
    &.red {
      background: #ff6d6d;
    }
  }
}
</style>
