<template>
    <el-dialog :visible.sync="show" title="转交到同事" width="600px">
        <el-form>
            <el-form-item label="要转交的同事">
                <tMemberSelect v-model="params.user_id"/>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="submiting">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect.vue'
export default {
    components: {
        tMemberSelect
    },
    data(){
        return {
            show: false,
            submiting: false,
            successFn: null,
            params: {}
        }
    },
    methods: {
        open(params){
            this.params = {
                user_id: '',                        //同事id
                ids: String(params.ids)             //客户ids
            };
            this.show = true;
            return this;
        },
        onSuccess(fn){
            fn && (this.successFn = fn);
            return this;
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(this.params.user_id == ''){
                this.$message.warning('请选择要转交的同事');
                return;
            }

            this.submiting = true;
            try{
                const res = await this.$http.informationTransToMember(this.params);
                this.submiting = false;
                if(res.status == 200){
                    this.$message.success('转交成功');
                    this.show = false;
                    this.successFn && this.successFn();
                }
            }catch(e){
                this.submiting = false;
            }
        }
    }
}
</script>
<style lang="scss" scoped>
    
</style>