<template>
  <div class="list">
    <el-container>
      <el-header class="div flex-right row">
        <div class="div row">
          <div class="add-build">
            <el-button type="primary" icon="el-icon-plus" @click="addUser"
              >添加成员</el-button
            >
            <el-button type="danger" icon="el-icon-delete" @click="deleteUsers"
              >批量删除成员</el-button
            >
          </div>
        </div>
        <div class="div  row">
          <el-input
            @change="onChange"
            v-model="input"
            placeholder="搜索相关成员"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>
      <el-main v-loading="is_table_loading">
        <myTable
          :table-list="tableData"
          :header="table_header"
          select
          @selection-change="handleSelectionChange"
        ></myTable>
      </el-main>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :pagesize="params.pagesize"
            :currentPage="params.currentPage"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "company_users",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      // 搜索框数据
      input: "",
      tableData: [],
      multipleSelection: [],
      // 存放列表图片
      imgbox: [],
      company_id: "",
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        {
          label: "成员名称",
          render: (h, data) => {
            return (
              <div>
                {data.row.name || data.row.nickname || data.row.user_name}
              </div>
            );
          },
        },
        {
          label: "联系方式",
          render: (h, data) => {
            return (
              <p>{data.row.phone ? data.row.phone : "暂未绑定联系方式"}</p>
            );
          },
        },
        { prop: "updated_at", label: "更新时间" },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="success"
                  onClick={() => {
                    this.updataCompany(0, data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  type="danger"
                  onClick={() => {
                    this.handleDelete(0, data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  created() {
    this.company_id = this.$route.query.company_id;
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.$http
        .showCompanyUserList(
          this.params.currentPage,
          this.params.pagesize,
          this.company_id,
          this.input
        )
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    goBack() {
      this.$router.back();
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    addUser() {
      this.$goPath(`/user_list?company_id=${this.company_id}`);
    },
    // 搜索楼盘
    search() {
      this.params.current_page = 1;
      this.getDataList();
    },
    onChange() {
      this.search();
    },
    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该公司成员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http
            .delCompanyUser({ id: row.id, company_id: 0 })
            .then((res) => {
              if (res.status === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getDataList();
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 修改操作
    updataCompany(index, row) {
      this.$goPath(`/edit_user?id=${row.id}&type=true`);
    },
    deleteUsers() {
      if (this.multipleSelection.length == 0) {
        this.$message.error("请先选择数据进行操作");
        return;
      }
      let ids = this.multipleSelection.map((item) => {
        return item.id;
      });
      this.$confirm("此操作将批量删除公司成员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http
            .delCompanyUsers({ ids: ids.join(","), company_id: 0 })
            .then((res) => {
              if (res.status === 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getDataList();
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
  computed: {},
};
</script>

<style scoped lang="scss">
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
.flex-right {
  margin-top: 30px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
</style>
