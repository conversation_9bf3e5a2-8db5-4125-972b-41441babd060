<template>
  <div class="tab-content-container" v-fixed-scroll="48">
    <div class="tab-content-body white topped main-scroll">
      <div class="content-box-crm">
        <div class="bottom-border div row" style="padding-top: 10px; display: flex; align-items: center;">
          <p style="margin-top: 10px;color: #a1a1a1;">筛选条件：</p>
          <div class="div row" style="margin-top: 10px">
            <el-select class="crm-selected-label" v-model="params.times_cate"  placeholder="时间类型"
              style="width: 130px;" @change="search">
              <el-option v-for="item in timeTypeObj" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <div class="block" style="margin-left: 10px;">
              <!-- <span class="demonstration">带快捷选项</span> -->
              <el-date-picker style="width: 350px;height: 100%;" v-model="params.times" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                @change="search">
              </el-date-picker>
            </div>
          </div>
          <div class="head-list">
            <el-select class="crm-selected-label" v-model="params.status"  placeholder="报告状态" :style="{
              minWidth: '20px',
              width: '110px',
            }" @change="search">
              <el-option label="正常" :value="1"></el-option>
              <el-option label="作废" :value="2"></el-option>
            </el-select>
          </div>
          
          <div class="head-list">
            <el-select class="crm-selected-label" v-model="params.payment_status" clearable placeholder="回款状态" :style="{
              minWidth: '20px',
              width: '110px',
            }" @change="search">
              <el-option v-for="item in collectionState" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>

          <div class="head-list">
            <el-cascader :options="memberList" placeholder='部门筛选' :props="{
              value: 'id',
              label: 'name',
              children: 'subs',
              emitPath: false,
              multiple: false,
            }" clearable filterable v-model="params.department_id" @change="search"></el-cascader>
          </div>
          <div class="head-list">
            <el-select class="crm-selected-label" clearable placeholder="选择成交人" :style="{
              minWidth: '20px',
              width: '180px',
            }" v-model="params.cj_admin_id" filterable @change="search">
              <el-option v-for="item in traderList" :key="item.id" :label="item.user_name" :value="item.id">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="title-box">
        <div class="input-all">
          <el-select class="crm-selected-label" v-model="params.searchKey" placeholder="搜索类型"
            style="width: 120px;margin-right: 10px;">
            <el-option v-for="item in searchType" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-input placeholder="请输入搜索内容" v-model="params.keywords" class="input-with-select" style="width: 260px;">
            <el-button slot="append" icon="el-icon-search" @click="search" clearable></el-button>
          </el-input>
        </div>
        <div class="button-all" v-if="isReportManager">
          <el-button type="primary" size="medium" @click="addcustomer()">添加成交单</el-button>
          <el-button type="primary" size="medium" :loading="exporting" @click="exportReport">导出</el-button>
          <!-- <el-button type="danger" size="medium" @click="cancelReport">作废</el-button> -->
        </div>
      </div>
      <el-table v-loading="loading" :data="tableData" class="report-table" border
        :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row stripe
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" fixed="left" width="55" align="center"/>
        <el-table-column fixed label="客户名称" v-slot="{ row }" width="150">
          <div class="customer-name" @click="goCustomerDetail(row)">{{ row.name || ''}}</div>
        </el-table-column>
        <el-table-column fixed label="手机号码" v-slot="{ row }" width="150">
          {{ row.phone ? row.phone : "--" }}
        </el-table-column>
        <el-table-column label="成交日期" v-slot="{ row }" width="180">
          {{ row.cjrq ? row.cjrq : "--" }}
        </el-table-column>
        <el-table-column label="回款状态" v-slot="{ row }" width="120">
          <el-tag type="success" v-if="row.payment_status==1">已回款</el-tag>  
          <el-tag type="danger" v-if="row.payment_status==0">未回款</el-tag>
          <el-tag type="warning" v-if="row.payment_status==2">未完成</el-tag>
        </el-table-column>
        <el-table-column label="成交人" v-slot="{ row }" width="180">
          {{ row.admin_user? row.admin_user.user_name : "" }}
        </el-table-column>
        <el-table-column label="项目名称" v-slot="{ row }" width="180">
          {{ row.mianji ? row.project : "--" }}
        </el-table-column>
        <el-table-column label="面积" v-slot="{ row }" width="180">
          {{ row.mianji ? row.mianji : "--" }} m²
        </el-table-column>
        <el-table-column label="单价" v-slot="{ row }" width="180">
          {{ row.danjia ? row.danjia : "--" }} 元
        </el-table-column>
        <el-table-column label="佣金点位" v-slot="{ row }" width="120">
          <div class="report-num">{{ row.proportion || "--" }}%</div>
        </el-table-column>
        <el-table-column label="成交金额" v-slot="{ row }" width="180">
          {{ row.amount ? row.amount : "--" }}
        </el-table-column>
        
        <el-table-column label="应收佣金" v-slot="{ row }" width="180">
          <div class="report-num">{{ row.receive_commission.commission || "--" }}</div>
        </el-table-column>
        <el-table-column label="公司佣金" v-slot="{ row }" width="180">
          <div class="report-num">{{ row.reality_commission || "--" }}</div>
        </el-table-column>
        <el-table-column label="扣除款项" v-slot="{ row }" width="180">
          <div class="report-num">{{ row._deduction || "--" }}</div>
        </el-table-column>
        <el-table-column label="成员佣金" v-slot="{ row }" width="180">
          <div class="report-num">{{ row.member_commission.commission || "--" }}</div>
        </el-table-column>
        <el-table-column label="回款时间" v-slot="{ row }" width="180">
          {{ row.payment_status_time}}
        </el-table-column>
        <el-table-column label="创建时间" v-slot="{ row }" width="180">
          {{ row.created_at }}
        </el-table-column>
        <el-table-column label="创建人" v-slot="{ row }" width="120">
          {{ row.operate_name }}
        </el-table-column>
        <el-table-column label="报告编号" v-slot="{ row }" width="180">
          {{ row.cj_num || "--" }}
        </el-table-column>
        <el-table-column label="操作" fixed="right" v-slot="{ row }" width="180">
          <el-link style="margin-right: 20px;" type="primary" @click="viewReport(row)">查看</el-link>
          
          <el-link type="primary" @click="printReport(row)">打印</el-link>
        </el-table-column>
      </el-table>
      <div class="summary" v-if="isReportManager && listLength">
          当前页合计共<span class="summary-num">{{listLength}}</span>项：
          成交金额GMV<span class="summary-num">{{amountTenThousandTotal}}</span>万元；
          佣金点位均值<span class="summary-num">{{avgCommissionPoint}}%</span>；
          成交均价<span class="summary-num">{{avgDealPrice}}</span>元/㎡；
          应收佣金总计<span class="summary-num">{{receiveCommissionTotal}}</span>元；
          公司佣金总计<span class="summary-num">{{pureCompanyCommissionTotal}}</span>元；
          扣除款项总计<span class="summary-num">{{deductionTotal}}</span>元；
          成员佣金总计<span class="summary-num">{{memberCommissionTotal}}</span>元
      </div>
    </div>
    <div class="tab-content-footer toolbar">
      <div>
        <el-button type="primary" @click="search" size="small" :loading="loading">刷新</el-button>
      </div>
      <el-pagination background
          layout="total,sizes,prev, pager, next, jumper"
          :total="params.total"
          :page-sizes="[10, 20, 30, 50,100]"
          :page-size="params.per_page"
          :current-page="params.page"
          @current-change="onPageChange"
          @size-change="handleSizeChange"
        >
      </el-pagination>
    </div>
    <div style="display: none;">
      <viewReport ref="viewReport"/>
    </div>
  </div>
</template>
<script>
import viewReport from './components/view.vue'
export default {
  name: 'crm_deal_report_index',
  components: {
    viewReport
  },
  data() {
    return {
      loading: true,
      exporting: false,
      tableData: [],
      multipleSelection: [],
      memberList: [],
      deptMemberList: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: '',//搜索关键词(客户姓名或备注)
        times: [],
        searchKey: 'name',
        times_cate: 1,
        payment_status: '',
        department_id: '',
        cj_admin_id: '',
        status: 1,   //1正常，2作废
      },
      //时间类型
      timeTypeObj: [
        { value: 1, label: '成交时间' },
        { value: 2, label: '创建时间' },
        { value: 3, label: '回款时间' },
      ],
      //搜索类型
      searchType: [
        //{ value: 0, label: '全部' },
        { value: 'name', label: '客户名称' },
        { value: 'phone', label: '客户手机号' },
        { value: 'cj_num', label: '报告编号' },
        { value: 'project', label: '认购项目' },
      ],
      //回款类型
      collectionState: [
        { value: 1, label: '已回款' },
        { value: 0, label: '未回款' },
        { value: 2, label: '未完成' },
      ],
      isReportManager: false,          //是否报告管理员
      needRefresh: false
    }
  },
  watch: {
    //更改部门时，清空所选成交人
    'params.department_id'(){
      if(this.params.cj_admin_id){
        if(!this.traderList.find(e => e.id == this.params.cj_admin_id)){
          this.params.cj_admin_id = '';
        }
      } 
    }
  },
  computed: {
      listLength(){
        return this.tableData.length
      },
      //应收佣金总计
      receiveCommissionTotal(){
        return (this.tableData.reduce((total,item)=>{
          return total + item.receive_commission.commission*1
        },0) || 0).toFixed(2)
      },
      //纯公司佣金总计
      pureCompanyCommissionTotal(){
        return (this.tableData.reduce((total,item)=>{
          return total + item.reality_commission*1
        },0) || 0).toFixed(2)
      },
      companyCommissionTotal(){
        return (this.tableData.reduce((total,item)=>{
          return total + item.company_commission.commission*1
        },0) || 0).toFixed(2)
      },
      //扣除款项总计
      deductionTotal(){
          return (this.companyCommissionTotal - this.pureCompanyCommissionTotal).toFixed(2)
      },
      //业绩提成
      memberCommissionTotal(){
        return (this.tableData.reduce((total,item)=>{
          return total + item.member_commission.commission*1
        },0) || 0).toFixed(2)
      },
      //成交总价
      amountTotal(){
        return (this.tableData.reduce((total,item)=>{
          return total + item.amount*1
        },0) || 0).toFixed(2);
      },
      //成交总价(万元)
      amountTenThousandTotal(){
        let amount = this.amountTotal * 1;
        return (amount > 10000 ? amount/10000 : amount).toFixed(2)
      },
      //均佣金点位
      avgCommissionPoint(){
        let point = this.tableData.reduce((total,item)=>{
          return total + item.proportion*1
        },0) || 0;
        let count = this.tableData.filter(e=>e.proportion > 0).length;
        return (count? point/count : 0).toFixed(2)
      },
      //成交均价
      avgDealPrice(){
        let sizeTotal = 0, amountTotal = 0;
        for(const item of this.tableData){
          if(item.mianji > 0 && item.amount > 0){
            sizeTotal +=  item.mianji*1;
            amountTotal +=  item.amount*1;
          }
        }
        let price = sizeTotal ? amountTotal / sizeTotal : 0;
        return (price || 0).toFixed(2);
      },

    //所选部门下的成交人列表
    traderList(){
      return this.deptMemberList.filter(e => {
        return this.params.department_id ? e.wx_work_department_id.split(",").includes(String(this.params.department_id)): true;
      })
    }
  },
  mounted() {
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
    this.gettabelList()
    this.getDepartment()
    this.getDepartmentMemberList()
    this.getReportOpPermission();
  },
  onPageRefresh(){
    this.needRefresh = true;
  },
  activated(){
    if(this.needRefresh){
      this.needRefresh = false;
      this.gettabelList();
    }
  },
  methods: {
    addcustomer() {
      this.$goPath('/add_crm_deal_report');
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList()
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    async getDepartmentMemberList() {
      let res = await this.$http.getDepartmentMemberList()
      if (res.status == 200) {
        this.setDeptMemberList(res.data);
        console.log(this.deptMemberList);
      }
    },
    setDeptMemberList(data){
      data.forEach(e => {
        if(e.subs && e.subs.length){
          this.setDeptMemberList(e.subs);
        }
        if(e.user && e.user.length){
          this.deptMemberList = this.deptMemberList.concat(e.user.filter(user => {
            return !this.deptMemberList.find(d => d.id == user.id)
          }));
        }
      })
    },
    async gettabelList() {
      this.loading = true;
      const params = this.getQueryParams();
      await this.$http.getReportListAPI(params).then((res) => {
        this.tableData = (res.data?.data || []).map(item => {
          const deduction = item.company_commission.commission - item.reality_commission || 0;
          item._deduction = Math.max(0, deduction)
          return item;
        })
        this.params.total = res.data.total
      })
      this.loading = false;
    },
    getQueryParams(){
      const params = {...this.params};
      params.times = params.times?.length ? params.times.join(',') : '';
      params[params.searchKey] = params.keywords;
      params.payment_status === '' && (params.payment_status = -1);

      delete params.total;
      delete params.searchKey;
      delete params.keywords;
      return params;
    },
    search(){
      this.params.page = 1;
      this.gettabelList();
    },
    viewReport(row) {
      this.$goPath('/crm_deal_report_detail?id='+row.id);
    },
    onPageChange(e) {
      this.params.page = e;
      this.gettabelList()
    },
    handleSizeChange(e){
      this.params.per_page = e;
      this.search();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    //导出成交报告
    async exportReport(){
      const params = this.getQueryParams();
      if(!params.times){
        this.$message.warning('请选择要导出的报告日期');
        return;
      }
      if(new Date(this.params.times[1]) - new Date(this.params.times[0]) > 86400*31*1000){
        console.log(new Date(this.params.times[1]) - new Date(this.params.times[0]));
        this.$message.warning('报告日期跨度不能超过1个月');
        return;
      }
      this.exporting = true;
      const res = await this.$http.exportCrmDealReport(params);
      this.exporting = false;
      if(res.status == 200){
        window.open(res.data);
      }
    },
    //作废
    async cancelReport(){
      if (!this.multipleSelection.length) {
        return this.$message({
          message: '请选择要作废的数据',
          type: 'warning'
        })
      }
      
      const ids = this.multipleSelection.map(e=>e.id).join(',');
      const res = await this.$http.cancelCrmDealReport({ids});
      if(res.status == 200){
        this.gettabelList()
        this.$message.success(res.data?.msg || '作废成功');
      }
    },
    //获取报告操作权限
    async getReportOpPermission(){
        //判断是否报告管理员
        const roleInfo = await this.$store.dispatch("queryMyRoleInfo");
        if(roleInfo){
            this.isReportManager = roleInfo.is_deal_auth_or_super ? true : false;
        }
    },
    //打印
    printReport({id}){
      this.$refs.viewReport.printReport(id);
    },
    goCustomerDetail({bind_type, bind_id, type}){
      switch(bind_type){
        case 1:
          this.$goPath('/crm_customer_detail?id='+bind_id+'&type='+(type==1 ? 'my' : 'seas'));
          break;
        case 2:
          this.$goPath('/house_detail?id='+bind_id);
          break;
      }
    }
  }
}
</script>
<style  scoped lang="scss">
::v-deep .el-table{
  td,th{
    padding: 16px 0;
  }
}
.title-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 20px;

  .input-all {
    display: flex;
  }
}

.head-list {
  margin-left: 10px;
  margin-top: 10px;
}

.report-table{
  .customer-name{
    color: #2d84fb;
    cursor: pointer;
  }
  .report-num{
    color: #f40;
    font-weight: 600;
  }
}


.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.summary{
  display: flex;
  min-height: 50px;
  align-content: center;
  flex-wrap: wrap;
  border: 1px solid #EBEEF5;
  border-top: none;
  padding: 5px 18px;
  .summary-num{
    color: #f40;
    padding: 0 3px;
  }
}
</style>