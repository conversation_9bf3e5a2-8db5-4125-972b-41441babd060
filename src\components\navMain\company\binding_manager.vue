<template>
  <el-container>
    <el-container>
      <el-header class="div row">
        <myTopTips title="项目助理" :number="tableData.length">
          <div class="add-build">
            <el-button
              type="primary"
              style="margin-right: 5px"
              @click="dialogManager = true"
              >添加项目助理</el-button
            >
            <el-button
              type="primary"
              style="margin-right: 5px"
              @click="toMangerTeam"
              >分组管理</el-button
            >
            <el-button
              type="primary"
              style="margin-right: 5px"
              @click="dialogExport = true"
              >导入项目助理</el-button
            >
          </div>
        </myTopTips>

        <div class="div row">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              项目助理名称<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a">项目助理名称</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-input
            @change="onChange"
            v-model="input"
            placeholder="搜索相关的项目助理"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" @click="search"
            >搜索</el-button
          >
        </div>
      </el-header>
      <el-dialog title="选择项目助理" :visible.sync="dialogManager">
        <el-form :model="fromManager">
          <el-form-item label="联系方式" label-width="100px">
            <el-select
              v-model="fromManager.user_id"
              filterable
              remote
              reserve-keyword
              placeholder="请输入联系方式"
              :remote-method="getManagerData"
              :loading="manager_loading"
            >
              <el-option
                v-for="item in manager_list"
                :key="item.user_id"
                :label="item.name"
                :value="item.user_id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogManager = false">取 消</el-button>
          <el-button type="primary" @click="bindingManager">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="导入助理" :visible.sync="dialogExport">
        <el-form>
          <el-form-item label="选择分组" label-width="100px">
            <el-select v-model="team_id" placeholder="请选择分组">
              <el-option
                v-for="item in team_list"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogExport = false">取 消</el-button>
          <el-button type="primary" @click="exportManager">确 定</el-button>
        </div>
      </el-dialog>
      <el-main>
        <el-table :data="tableData" border>
          <el-table-column
            label="项目ID"
            prop="project_id"
            width="100"
          ></el-table-column>
          <el-table-column
            label="用户ID"
            prop="user_id"
            width="100"
          ></el-table-column>
          <el-table-column label="用户头像" width="100">
            <template slot-scope="scope">
              <img
                width="50"
                :src="
                  scope.row.avatar
                    ? scope.row.avatar
                    : 'https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg'
                "
              />
            </template>
          </el-table-column>
          <el-table-column label="姓名" prop="user_name"></el-table-column>
          <el-table-column label="联系方式" prop="phone"></el-table-column>
          <el-table-column label="排序" prop="sort">
            <template slot-scope="{ row, $index }">
              <div
                @dblclick="
                  {
                    {
                      chengenum($index);
                    }
                  }
                "
              >
                <el-input
                  style="width: 300px"
                  v-if="editable[$index]"
                  v-model.number="row.sort"
                  type="number"
                  min="0"
                  step="1"
                  size="small"
                  @blur="sortBlue($index, row)"
                  @keyup.enter.native="sortBlue($index, row)"
                />
                <span v-else>{{ row.sort }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            :show-overflow-tooltip="true"
            label="是否显示"
            width="100"
          >
            <template slot-scope="scope">
              <el-switch
                :active-value="1"
                :inactive-value="0"
                @change="stateChanged(scope.row)"
                v-model="scope.row.is_display"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-delete"
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination
            :total="params.total"
            :pagesize="params.pagesize"
            :currentPage="params.currentPage"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></myPagination>
        </div>
      </el-footer>
    </el-container>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import { Loading } from "element-ui";
export default {
  name: "binding_manager",
  components: {
    myPagination,
  },
  data() {
    return {
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      // 搜索框数据
      input: "",
      tableData: [],
      project_id: null,
      manager_list: [],
      manager_loading: false,
      fromManager: {
        project_id: "",
        user_id: "",
      },
      dialogManager: false,
      editable: [], // 编辑排序的表
      dialogExport: false,
      team_list: [],
      team_id: ''
    };
  },
  mounted() {
    this.project_id = this.$route.query.id;
    this.fromManager.project_id = this.project_id;
    this.getDataList();
    this.getManagerData();
    this.getTeamList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.$http
        .showManagerList(this.project_id, this.params.currentPage, this.input)
        .then((res) => {
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.currentPage = res.data.current_page;
            this.params.total = res.data.total;
            this.params.row = res.data.per_page;
          }
        });
    },
    getTeamList() {
      this.$http.teamList().then(res => {
        if (res.status == 200) {
          this.team_list = res.data
        }
      })
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    goBack() {
      this.$router.back();
    },
    onChange() {
      this.search();
    },
    // 搜索
    search() {
      this.params.current_page = 1;
      this.getDataList();
    },
    // 搜索下拉
    handleCommand(command) {
      this.$message("click on item " + command);
    },
    handleDelete(row) {
      this.$confirm("此操作将删除该用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteManager(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    getManagerData(query) {
      this.manager_loading = true;
      this.$http.searchUserByPhone(query).then((res) => {
        this.manager_loading = false;
        this.manager_list = res.data.data.map((item) => {
          return {
            user_id: item.id,
            name: item.name || item.nickname || item.user_name,
          };
        });
      });
    },
    bindingManager() {
      this.$http.projectManager(this.fromManager).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.getDataList();
        }
      });
      this.dialogManager = false;
    },
    exportManager() {
      Loading.service({
        text: "正在导入 请稍候。。。",
      });
      this.$http.exportTeam({ project_id: this.project_id, team_id: this.team_id }).then(res => {
        if (res.status == 200) {
          Loading.service().close();
          this.dialogExport = false
          this.$message.success("导入成功")
          this.getDataList();
        }
      }).catch(() => {
        Loading.service().close();

      })
    },
    toMangerTeam() {
      this.$goPath('/team_group')
    },
    /**
     * 商品排序设置
     */
    chengenum(row) {
      this.editable[row] = true;
      this.$set(this.editable, row, true);
    },
    // 失焦 完成
    sortBlue(row, val) {
      this.$set(this.editable, row, false);
      this.$http
        .setProjectManagerSort({
          id: val.id,
          sort: val.sort,
        })
        .then((res) => {
          if (res.status === 200) {
            this.getDataList();
          }
        });
    },
    stateChanged(row) {
      let form = {
        id: row.id,
        is_display: row.is_display,
      };
      this.$http.setProjectManagerDisplay(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.scope-box {
  height: 40px;
  img {
    width: 100%;
  }
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}

.el-dropdown {
  color: #c0c4cc;
  width: 110px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;

  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  width: 40px;
  img {
    width: 100%;
  }
}
</style>
