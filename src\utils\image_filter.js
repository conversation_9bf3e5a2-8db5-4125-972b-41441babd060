// 判断资源是不是远程资源
const isHttp = function(val) {
  let httpArr = ["http", "https"];
  return httpArr.includes(val.split("://")[0]);
};
const formatImg = function(url, param = "w_8601") {
  if (!url || typeof url !== "string") {
    return;
  }
  if (isHttp(url)) {
    let reg = new RegExp(/\?.+=/);
    if (reg.test(url)) {
      // 链接中有参数直接返回不需要加参数
      return url;
    }
    let thumbParam = "?x-oss-process=style/";
    if (param) {
      return url + thumbParam + param;
    }
    return url;
  }
};

const imageFilter = {
  filters: {
    imageFilter(value, param = "w_1300") {
      return formatImg(value, param);
    },
  },
};

module.exports = imageFilter;
