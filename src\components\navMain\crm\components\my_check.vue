<template>
  <div class="tabs div row">
    <div class="row div tabs-item-box">
      <div
        class="tabs-item"
        :class="{ isactive: item[value] === is_tabs }"
        @click="onClickTabs(item)"
        v-for="item in type"
        :key="item[value]"
        :style="{
          fontSize: size,
        }"
      >
        {{ item[label] }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    type: Array,
    is_tabs: [String, Number],
    label: String,
    value: [String, Number],
    size: {
      type: String,
      default: "14px",
    },
  },
  methods: {
    onClickTabs(e) {
      this.$emit("onClick", e);
    },
  },
};
</script>

<style scoped lang="scss">
.tabs {
  justify-content: space-between;
  align-items: center;
  color: #8a929f;
  background: #fff;
  margin-bottom: 14px;
  cursor: pointer;
  .tabs-item-box {
    .tabs-item {
      margin-right: 24px;
      position: relative;
      &.isactive {
        color: #2d84fb;
        &::after {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          content: "";
          height: 3px;
          background: #2d84fb;
          width: 24px;
          display: block;
          margin-top: 4px;
        }
      }
    }
  }
}
</style>
