export default {
    data(){
        return {
            customTabParams: {}
        }
    },
    watch: {
   
    },
    methods: {
        //设置搜索参数
        setSearchParams(params, tabType){
            //时间类型
            this.params.date_style = this.datetime_type = params.date_style ?? '';
            //时间排序
            this.params.date_sort = params.date_sort || 1;
            //开始-结束日期
            if(params.start_date && params.end_date){
                this.params.start_date = params.start_date;
                this.params.end_date = params.end_date; 
                this.timeValue = [params.start_date, params.end_date];
            }else{
                //开始或结束日期可以只有一个
                this.params.start_date = params.start_date || '';
                this.params.end_date = params.end_date || ''; 
                this.timeValue = [];
            }
            //客户来源
            this.params.source_id = this.Source_statusvalue = params.source_id || '';
            //客户状态
            this.params.status_id = this.customer_statusvalue = params.tracking_id || '';
            //客户类型
            this.params.type = this.typeLabel_value = params.type || '';
            //客户等级
            this.params.level_id = this.grade_value = params.level_id || '';
            //通话状态
            this.params.call_status = this.customerstatus_value = params.call_status || '';
            //清空绑定企微
            this.typeqiwei_value = '';
            this.params.is_bind = '';
            //清空客户标签
            this.is_all = true;
            this.changeParentLabel = "";
            this.customerLabelList = {};
            this.params.label = 0;
            //清空input组合搜索
            this.select_params.type = 1;
            this.select_params.keywords = '';
            this.params.keywords = ''
            this.params.mobile = ''
            this.params.cname = ''
            this.params.number = ''
            this.params.follow_keywords  = ''
            //其他
            this.params.department_id = 0;
            this.params.admin_id = 0;
            this.params.last_zg_uid = 0;
            this.params.last_dg_uid = 0;
            this.selectedMember = '';

            this.params.admin_type = 0;
            this.member_value = '';

            this.params.page = 1;
            this.customTabParams = {};
        },
        //自定义tab切出事件
        handleCustomTabChangeOut(tabType){
            this.setSearchParams({}, tabType);
            switch(tabType){
                case 2:
                    this.params.c_type2 = 0;
                    if(this.params.c_type1 == 2){  //最新跟进
                        this.params.date_style = this.datetime_type = 2
                    }else if(this.params.c_type1 == 3){ //多条线索
                        this.params.date_style = this.datetime_type = 3
                    }
                    break;
                case 3:
                    this.params.c_type2 = 0;
                    break;
            }
        },
        //自定义tab切换事件
        handleCustomTabChange(tab, tabType){
            const params = tab.params;
            this.setSearchParams(params, tabType);
            switch(tabType){
                case 1:
                    this.customTabParams = {
                        c_type2: params.c_type2 || 0,  
                    }
                    break;
                case 2:
                    this.params.c_type2 = params.c_type2 || 0;
                    this.customTabParams = {
                        c_type1: params.c_type1 || 0,
                        c_type4: params.c_type4 || 0,
                    }
                    break;
                case 3:
                    this.params.c_type2 = params.c_type2 || 0;
                    this.customTabParams = {
                        c_type1: params.c_type1 || 0,
                        c_type4: params.c_type4 || 0,
                    }
                    break;
            }
            this.getDataList();
        },
    }

};