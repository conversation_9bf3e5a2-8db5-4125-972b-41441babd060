<template>
  <!-- 
  ymm
  13299923366
  jinke888
 -->
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import { mapMutations } from "vuex";
export default {
  data() {
    return {};
  },
  computed: {},
  mounted() {
    // 获取系统信息
    var params = this.$queryUrlParams(window.location.href);
    //  检查是否是企业微信链接
    if (params.HASH === "/wx_work_auth") {
      return;
    }
    if (params.website_id) {
      localStorage.setItem("website_id", params.website_id);
    }
    // if (!params.website_id) {
    //   params.website_id = localStorage.getItem("website_id")
    // }
    /* if (params.auth_code && params.expires_in) {
      this.$http
        .sendWxCode({
          auth_code: params.auth_code,
          expires_in: params.expires_in,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "授权成功",
              type: "success",
            });
            Loading.service().close();
            setTimeout(() => {
              window.location.href = `https://yun.tfcs.cn/admin?website_id=${params.website_id}#/index`;
            }, 1000);
          }
        });
    } */
    // Vue路由——ie上地址栏输入路由页面不更新
    if (!!window.ActiveXObject || "ActiveXObject" in window) {
      window.addEventListener(
        "hashchange",
        () => {
          let currentPath = window.location.hash.slice(1);
          if (this.$route.path !== currentPath) {
            this.$router.push(currentPath);
          }
        },
        false
      );
    }
  },
  methods: {
    ...mapMutations(["setExprie"]),
  },
};
</script>

<style lang="scss">
@import "@/assets/css/common.scss";
.p-20 {
}
.el-main {
  // border-top: 1px solid #f0f0f0;
  // height: calc(100vh - 68px);
  height: auto;
}
.div {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  &.row {
    flex-direction: row;
  }
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-box {
  display: flex;
  flex-direction: column;
}
.align-center {
  align-items: center;
}
.align-end {
  align-items: flex-end;
}
.j-center {
  justify-content: center;
}

.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.j-end {
  justify-content: flex-end;
}
.f-wrap {
  flex-wrap: wrap;
}
.j-between {
  justify-content: space-between;
}

* {
  padding: 0;
  margin: 0;
  font-family: PingFangSC-Medium, sans-serif;
}
html,
body {
  width: 100%;
  height: 100%;
  background: #f9f9f9;
}
ul,
ol {
  list-style: none;
}
#app {
  height: 100%;
}
i {
  font-style: normal;
}

//弹窗
//弹窗
.el-dialog__title {
  border-left: 6px solid #2d84fb;
}
.el-dialog {
  border-radius: 8px;
  overflow: hidden;
}
.el-dialog__title {
  padding: 3px 12px;
  font-weight: bold;
  color: #2e3c4e;
}
.el-dialog--center {
  .el-dialog__title {
    padding: 3px 12px;
    border-left: 0;
    font-weight: bold;
    color: #2e3c4e;
  }
}
.el-dialog__headerbtn {
  font-size: 24px;
}
.el-dialog__body {
  position: relative;
}

.content-box-crm {
  padding: 24px;
  background: #fff;
  border-radius: 4px;
  .title {
    font-size: 18px;
    padding-left: 24px;
    color: #2e3c4e;
    border-bottom: 1px solid #eee;
    padding-bottom: 24px;
    margin-left: -24px;
    margin-right: -24px;
  }
  .table-top-box {
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
    .t-t-b-left {
      align-items: center;
      .text {
        color: #8a929f;
        font-size: 14px;
      }
      .blue {
        color: #2d84fb;
      }
    }
  }
}
.hometabs {
  box-shadow: none !important;
  border: none !important;
}
.pages {
  // height: inherit !important;
}

.level {
  color: #8a929f;
  background: #f1f4fa;
  &.levelA {
    color: #fff;
    background: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
  }
  &.levelB {
    color: #fff;
    background: linear-gradient(180deg, #f8a707, #f85d02 100%);
  }
  &.levelC {
    color: #fff;
    background: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
  }
  &.levelD {
    color: #fff;
    background: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
  }
}

.tribute-container {
  position: absolute;
  top: 0;
  left: 0;
  height: auto;
  max-height: 300px;
  overflow: auto;
  display: block;
  z-index: 999999;
}
.tribute-container ul {
  margin: 0;
  margin-top: 2px;
  padding: 0;
  list-style: none;
  background: #fff;
  margin: 3px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.tribute-container li {
  padding: 5px 24px;
  cursor: pointer;
}
.tribute-container li.highlight {
  background: #2d84fb;
  color: #fff;
}
.tribute-container li span {
  font-weight: bold;
}
.tribute-container li.no-match {
  cursor: default;
}
.tribute-container .menu-highlighted {
  font-weight: bold;
}


.virtual-scrollable-x.mac{
  &::-webkit-scrollbar {
    -webkit-appearance: none;
    -webkit-overflow-scrolling: auto
  }
  &::-webkit-scrollbar:horizontal {
    height: 16px
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: rgba(50,50,50, .3)
  }

}

.is-custom-table-header.is-fixed{
  .fixed-left, .fixed-right{
    box-shadow: 0 0 10px rgba(0,0,0,.12);
  }
  &.is-scrolling-left .fixed-left, &.is-scrolling-right .fixed-right{
    box-shadow: none;
  }
}


</style>
