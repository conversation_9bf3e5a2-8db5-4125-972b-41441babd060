<template>
  <div>
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      highlight-current-row
    >
      <!-- <el-table-column prop="level_id" label="ID"></el-table-column> -->
      <el-table-column prop="title" label="名称"></el-table-column>
      <el-table-column prop="desc" label="等级描述"></el-table-column>
      <el-table-column label="开启状态">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status == 1">开启</el-tag>
          <el-tag type="danger" v-if="scope.row.status == 0">关闭</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="day" :render-header="renderTableHeader">
        <template slot-scope="scope">
          <span>{{ scope.row.day }}天</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" v-slot="scope">
        <el-link type="warning" @click="informationEdit(scope.row)">编辑</el-link>
      </el-table-column>
      <!-- <el-table-column prop="created_at" label="添加时间"></el-table-column> -->
    </el-table>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog
      title="编辑"
      :visible.sync="show_Edit_dialog"
      width="540px"
      class="edit_dialog"
    >
      <el-form ref="formEdit" :model="tableData_edit" label-width="120px">
        <el-form-item label="跟进提醒：">
          <el-input
            placeholder="请输入天数"
            style="width: 300px"
            v-model="tableData_edit.day"
            min="0"
            max="255"
            type="number"
          >
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="等级描述：">
          <el-input v-model="tableData_edit.desc" style="width: 300px"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_Edit_dialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmEdit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      show_Edit_dialog: false, // 编辑模态框显/隐
      // 编辑数据
      tableData_edit: {
        id: 0, // 客户等级id
        day: 0, // 跟进提醒时间
        desc:"",
      }
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmCustomerLevel({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    renderTableHeader(h) {
      return h('div', {}, [
        h('el-popover', {
          ref: 'popover1',
          props: {
            placement: 'top-start',
            width: '300',
            trigger: 'hover',
            content: '系统将自动发送模板消息，提醒维护人跟进，如果不开启消息提醒可设置为0天。'
          }
        }),
        h('span', {}, [
          '跟进提醒',
          h('i', {
            'class': 'el-icon-info',
            'style': 'color: #f56c6c; font-size: 16px; margin-left: 5px;',
            // 自定义指令
            directives: [
              {
                name: 'popover',
                arg: 'popover1'
              }
            ]
          })
        ])
      ])
    },
    // 确定编辑
    confirmEdit() {
      if(this.tableData_edit.day == '' || this.tableData_edit.day == undefined) {
        this.tableData_edit.day = 0;
      }
      this.$http.editCrmCustomerLevel(this.tableData_edit).then((res) => {
        if(res.status == 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.show_Edit_dialog = false;
          this.getDataList();
        }
      })
    },
    // 点击编辑
    informationEdit(row) {
      this.tableData_edit.id = row.level_id; // 赋值id
      this.tableData_edit.day = row.day; // 赋值跟进提醒天数
      this.tableData_edit.desc = row.desc
      this.show_Edit_dialog = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .edit_dialog{
  .el-dialog {
    .el-dialog__footer {
      text-align: center;
    }
  }
}
</style>
