<template>
    <div v-fixed-scroll="62">
        <div class="pagecontent">
          <div class="tablestyle">
            <div class="Statusretrieval">
                <el-form :inline="true" :model="params" class="demo-form-inline">
                    <el-form-item label="项目名称" >
                        <el-input v-model="params.project_name" placeholder="请填写项目名称"></el-input>
                    </el-form-item>
                    <el-form-item label="报备手机号">
                        <el-input v-model="params.user_phone" placeholder="请填写报备手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="客户手机号">
                        <el-input v-model="params.customer_phone" placeholder="请填写客户手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="秘钥系统">
                        <el-input v-model="params.system_key" placeholder="请填写秘钥系统"></el-input>
                    </el-form-item>
                    <el-form-item label="报备类型">
                        <el-select v-model="params.cst_type" placeholder="请选择报备类型"
                        clearable >
                            <el-option
                                v-for="item in reporttypeData"
                                :key="item.id"
                                :label="item.title"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="Refresh">搜索</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table
                v-loading="is_table_loading"
              :data="tableData"
              border
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }">
              <el-table-column
                prop="customer_name"
                label="客户名称"
                fixed>
              </el-table-column>
              <el-table-column
                prop="project_name"
                label="项目名称">
              </el-table-column>
              <el-table-column
                prop="cst_gender"
                label="客户性别"
                v-slot="{ row }">
                {{row.cst_gender==0?"男":"女"}}
              </el-table-column>
              <el-table-column
                prop="cst_tels"
                label="客户手机号">
              </el-table-column>
              <el-table-column
                prop="created_at"
                label="报备时间">
              </el-table-column>
              <el-table-column
                prop="user_name"
                label="报备人">
              </el-table-column>
              <el-table-column
                prop="cst_type"
                label="报备类型"
                v-slot="{ row }">
                {{row.cst_type==1?"隐号":"全号"}}
              </el-table-column>
            </el-table> 
            <div class="page_footer flex-row items-center">
              <div class="page_footer_l flex-row flex-1 items-center">
                <div class="head-list">
                  <el-button type="primary" size="small" @click="empty">清空</el-button>
                </div>
                <div class="head-list">
                  <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                </div>
              </div>
              <div>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="params.per_page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total">
                  </el-pagination>
              </div>
            </div>
          </div>
            
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            tableData:[],//报备列表数据
            params:{
                per_page:10,
                page:1,
            },//请求字段
            is_table_loading:false,
            reporttypeData:[
                {id:1,title:"全号"},
                {id:2,title:"隐号"},
            ],//报备类型
        }
    },
    mounted(){
        this.getdatalist()
    },
    methods:{
        getdatalist(){
            if(this.params.status!==0){
                if(this.params.status==""){
                    delete this.params.status
                }
            }
            this.is_table_loading = true
            this.$http.myreportinglist(this.params).then((res)=>{
                if(res.status==200){
                    this.tableData = res.data.data
                    this.is_table_loading = false
                    this.params.total = res.data.total;
                }
            })
        },
        //每页 条
        handleSizeChange(val){
            this.params.per_page = val
            this.getdatalist()
        },
        //当前页
        handleCurrentChange(val) {
            this.params.page = val
            this.getdatalist()
        },
        //清空
        empty(){
            this.params={
                per_page:10,
                page:1,
            }
            this.getdatalist()
        },
        //刷新
        Refresh(){
            this.getdatalist()
        },
    },
}
</script>
<style lang="scss" scoped>
    .pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .tablestyle{
            width: 97%;
            margin: 0 auto;
            margin-top: 20px;
            margin-bottom: 30px;
        }
        .Statusretrieval{
            margin-bottom: 20px;
        }
    }
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
</style>