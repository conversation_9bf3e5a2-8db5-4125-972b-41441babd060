{"name": "T", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:oss": "vue-cli-service build", "lint": "eslint --ext .js,.vue src", "deploy": "node ./upload/upload.js"}, "dependencies": {"@dcloudio/uni-webview-js": "0.0.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.19.2", "core-js": "^3.6.5", "echarts": "^5.2.2", "element-ui": "^2.13.2", "moment": "^2.27.0", "qqmap": "^1.0.1", "qrcodejs2": "^0.0.2", "register-service-worker": "^1.7.2", "tributejs": "^5.1.3", "v-distpicker": "^1.2.7", "vue": "^2.6.11", "vue-router": "^3.3.4", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-pwa": "^4.5.19", "@vue/cli-service": "~4.4.0", "ali-oss": "^6.19.0", "babel-eslint": "^10.1.0", "chalk": "^4.1.2", "compressing": "^1.10.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "inquirer": "^7.3.3", "node-sass": "^4.14.1", "node-ssh": "^11.1.1", "ora": "^5.4.1", "sass-loader": "^9.0.2", "scp2": "^0.5.0", "shelljs": "^0.8.5", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}