const fullPageRouter = [
	{
		name: 'mapPluginMapIndex',
		path: "/map_plugin/map_index",
		component: (resolve) => require(["@/views/map_plugin/map_index"], resolve),
		meta: { title: "领取资料" },
	}, {
		name: 'importDawu',
		path: "/import/dawu",
		component: (resolve) => require(["@/views/import/dawu"], resolve),
		meta: { title: "大武表格数据导入" },
	}, {
		name: 'trainVideo',
		path: "/train/video",
		component: (resolve) => require(["@/views/train/video"], resolve),
		meta: { title: "培训视频" },
	}

];

const router = [
	{
		name: 'crm_deal_report_index',
		path: "/crm_deal_report_index",
		component: (resolve) => require(["@/views/crm/deal_report/index"], resolve),
		meta: { title: "成交报告", keepAlive: true, keepAliveName: 'crm_deal_report_index', scrollTop: 0 },
	}, {
		name: "crm_deal_report_detail",
		path: "/crm_deal_report_detail",
		component: (resolve) => require(["@/views/crm/deal_report/detail"], resolve),
		meta: { title: "成交报告详情", keepAlive: true, keepAliveName: 'crm_deal_report_detail' },
	}, {
		name: "add_crm_deal_report",
		path: "/add_crm_deal_report",
		component: (resolve) => require(["@/views/crm/deal_report/add"], resolve),
		meta: { title: "新增成交报告", keepAlive: true, keepAliveName: 'add_crm_deal_report' },
	}, {
		name: "crm_live_room_users",
		path: "/crm_live_room_users",
		component: (resolve) => require(["@/views/crm/live_room/users"], resolve),
		meta: { title: "直播间用户", keepAlive: true, keepAliveName: 'crm_live_room_users', scrollTop: 0 },
	}, {
		name: "douyin_private_letter_users",
		path: "/douyin_private_letter_users",
		component: (resolve) => require(["@/views/crm/live_room/private_letter_users"], resolve),
		meta: { title: "抖音私信用户", keepAlive: true, keepAliveName: 'douyin_private_letter_users', scrollTop: 0 },
	}, {
		name: "short_video_view",
		path: "/short_video_view",
		component: (resolve) => require(["@/views/crm/short_video/shortvideo_data"], resolve),
		meta: { title: "短视频", keepAlive: true, keepAliveName: 'short_video_view', scrollTop: 0 },
	}, {
		name: "crm_weixin_users",
		path: "/crm_weixin_users",
		component: (resolve) => require(["@/views/crm/weixin/users"], resolve),
		meta: { title: "视频号直播间用户", keepAlive: true, keepAliveName: 'crm_weixin_users', scrollTop: 0 },
	}, {
		name: "ai_analysis_list",
		path: "/ai_analysis_list",
		component: (resolve) => require(["@/views/crm/ai_analysis/list"], resolve),
		meta: { title: "AI分析", keepAlive: true, keepAliveName: 'ai_analysis_list', scrollTop: 0 },
	},
];


export { fullPageRouter, router as default }; 