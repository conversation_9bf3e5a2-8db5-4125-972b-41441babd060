<template>
  <div>
    <el-header class="div row" style="justify-content: space-between">
      <!-- 标题 -->
      <div class="div row">
        <div class="title">客户标签</div>
      </div>
    </el-header>
    <!-- 标签 -->
    <el-tag
      :key="index"
      v-for="(tag, index) in groupsList"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag.name }}
    </el-tag>
    <el-input
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      ref="saveTagInput"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    >
    </el-input>
    <el-button v-else class="button-new-tag" size="small" @click="showInput"
      >+ 新建标签</el-button
    >
  </div>
</template>

<script>
export default {
  name: "customer_label",
  components: {},
  data() {
    return {
      groupsList: [],
      inputVisible: false,
      inputValue: "",
    };
  },
  computed: {},
  watch: {},
  methods: {
    getGroups() {
      this.$http.getGroups().then((res) => {
        console.log(res, "客户标签组");
        if (res.status === 200) {
          this.groupsList = res.data;
        }
      });
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
  },
  created() {},
  mounted() {
    this.getGroups();
  },
};
</script>
<style lang="scss" scoped>
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
