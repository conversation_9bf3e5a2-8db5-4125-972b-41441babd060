<template>
    <div class="navigation ">
        <div>链接电话与导航设置</div>
        <div style="margin-top: 20px;margin-left:40px">
            <el-radio v-model="type" label="1" border size="medium" @change="phoneadd">添加网站链接（或电话号码</el-radio>
            <el-radio v-model="type" label="0" border size="medium" @change="Map_navigation">添加地图导航</el-radio>
        </div>
        <div class="Link-Phone" v-for="(item, index) in phonedata" :key="index">
            <div style="margin-top: 20px;">
                <span style="font-size: 14px;color:chocolate;">添加图标：</span>
                <div class="vravatar">
                    <el-upload class="avatar-uploader" action="/api/common/file/upload/admin?category=401"
                        :headers="upload_headers" :show-file-list="false" :on-success="handleAvatarSuccess(item, index)"
                        accept=".jpg,.png,.jepg">
                        <img v-if="item.imgPath" :src="item.imgPath" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </div>
            </div>
            <div style="margin-top: 60px;margin-left: 10px;">
                <el-input v-model="item.title" placeholder="请输入名称"></el-input>
            </div>
            <div style="margin-top: 60px;margin-left: 10px;" v-show="item.type == 1">
                <el-input v-model="item.content" placeholder="请输入链接或电话"></el-input>
            </div>
            <div style="margin-top: 60px;margin-left: 10px;" v-show="item.type == 0">
                <!-- 出现地图 -->
                <el-button @click="Navigationend(item, index)" style="width: 185px;">设置导航终点</el-button>
            </div>
            <div style="margin-top: 60px;margin-left: 10px;"><el-button type="info" size="medium"
                    @click="delphone(item, index)">删除</el-button></div>
        </div>
        <div class="Upload">
            <el-button type="warning" @click="uploadlinknavigation">确定上传</el-button>
        </div>
        <!-- 地图 -->
        <div class="mask1" v-if="dialogUploadPicture">
            <div class="preview_img" @click.prevent.stop="() => { }">
                <div id="bai-du-map"></div>
                <div class="punctuation">
                    <el-button type="warning" size="medium" @click="Mark_Location">标点</el-button>
                    <el-button type="primary" size="medium" @click="conserve">保存</el-button>
                    <el-button type="info" size="medium" @click="delMarkLocation">清除</el-button>
                    <el-button size="medium" @click="shutdown">关闭</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import config from "@/utils/config.js";
export default {
    name: 'TdtMap',
    props: {
        Propertydata: {
            type: Object,
            default: () => { }
        },
    },
    data() {
        return {
            type: 0,
            content: {},
            vrlinkdata: {
                linkSettings: [],
            },
            dialogUploadPicture: false,
            handler: {},
            coordinates: {},
            dialogVisible: false,
            upload_headers: {
                Authorization: config.TOKEN,
            },
            phonedata: [
                { imgPath: "", title: "", content: "", type: 1 },
                { imgPath: "", title: "", content: "", type: 0 }
            ],

        }
    },
    methods: {
        handleAvatarSuccess(item, index) {
            // console.log(item, index);
            return (response, file,) => {
                console.log('上传成功！额外的数据是：', item, index);
                console.log('响应数据是：', response, file);
                // console.log(URL.createObjectURL(file.raw));
                // console.log();
                this.phonedata[index].imgPath = URL.createObjectURL(file.raw);
                console.log(this.phonedata[index].imgPath);
            }
        },
        //
        Navigationend(item, index) {
            // console.log(item,index);
            this.dialogUploadPicture = true;
            // 定时器检测那个地图弹窗是否关闭
            let lng = setInterval(() => {
                if (this.dialogUploadPicture == false && this.content.lng != '' && this.content.lat != '') {
                    // 如果关闭了并且经纬度都不是零那就代表已经选了地址
                    // console.log(this.content);
                    // 然后才开始执行让经纬度放到指定索引的位置
                    this.phonedata[index].content = this.content
                    // 执行完清除定时器
                    clearInterval(lng)
                }
            }, 1000);
            setTimeout(() => {
                this.initTdtMap()
            }, 150);
        },
        initTdtMap() {
            var T = window.T
            console.log(T);
            this.tdtMap = new T.Map('bai-du-map');
            // console.log(this.tdtMap);
            //设置显示地图的中心点和级别
            this.tdtMap.centerAndZoom(new T.LngLat(117.163139, 35.079158), 12);
            this.handler = new T.PolygonTool(this.tdtMap, this.config);
        },

        //标点
        Mark_Location() {
            var T = window.T
            // console.log(this.handler);
            if (this.handler) this.handler.close();
            this.handler = new T.MarkTool(this.tdtMap, { follow: true });
            this.handler.open();
            this.handler.addEventListener('mouseup', this.drawFinish)
            // this.handler.close();
        },
        //经纬度
        drawFinish(e) {
            //当前位置
            // console.log(e.currentLnglat);
            // console.log("顶点坐标列表 ----------> ", e.currentLnglat)
            //画范围坐标
            this.coordinates = e.currentLnglat
            // console.log(this.coordinates);
        },
        //保存
        conserve() {
            this.content.lat = this.coordinates.lat
            this.content.lng = this.coordinates.lng
        },
        //清除
        delMarkLocation() {
            this.handler.clear();
        },
        //关闭
        shutdown() {
            this.dialogUploadPicture = false
        },
        //添加电话链接。。
        phoneadd() {
                if (this.phonedata.length >= 5) {
                    this.$message({
                        type: "warning",
                        message: "可添加数量已达上限"
                    })
                    this.type = ""
                    return
                }
                this.phonedata.push({
                    type: 1,
                    content: ""

                })
            this.type = ""
        },
        //添加地图导航
        Map_navigation() {
                if (this.phonedata.length >= 5) {
                    this.$message({
                        type: "warning",
                        message: "可添加数量已达上限"
                    })
                    this.type = ""
                    return
                }
                this.phonedata.push({
                    type: 0,
                    title: "",
                })

            this.type = ""
        },
        //删除
        delphone(item, index) {
            this.phonedata.splice(index, 1)
        },
        //确认上传
        uploadlinknavigation() {
            this.phonedata.map((item) => {
                if (item.title == "" || item.content == "" || item.imgPath == "") {
                    this.$message({
                        type: "warning",
                        message: "上传内容不可为空"
                    })

                } else {
                    this.vrlinkdata.linkSettings.push(item)
                    this.vrlinkdata.pk_works_main = this.Propertydata.pk_works_main
                    console.log(this.vrlinkdata);
                    this.$http.uploadlinknavigation(this.vrlinkdata).then((res) => {
                        // console.log(res);
                        if (res.status == 200) {
                            this.$message({
                                type: "success",
                                message: "上传成功"
                            })
                        }
                    })
                }
            })

        },
    },
}

</script>
<style lang="scss" scoped>
.navigation {
    margin: 10px;

    .vravatar {
        width: 50px;
        height: 50px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        margin-top: 10px;
        margin-left: 20px;

        .avatar-uploader .el-upload {
            // border: 1px dashed #d9d9d9;
            // border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }

        .avatar-uploader-icon {
            font-size: 20px;
            color: #8c939d;
            width: 50px;
            height: 50px;
            line-height: 50px;
            text-align: center;
        }

        .avatar {
            width: 50px;
            height: 50px;
            display: block;
        }


    }

    .Link-Phone {
        display: flex;
    }

    .Upload {
        margin-top: 20px;
        text-align: right;
    }

    .mask1 {
        position: fixed;
        background: rgba(0, 0, 0, 0.8);
        top: 60px;
        bottom: 0;
        right: 0;
        left: 230px;
        padding: 10px;
        overflow: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10;

        .preview_img {
            position: relative;
            width: 800px;
            height: 500px;
            margin-bottom: 250px;

            #bai-du-map {
                overflow: hidden;
                width: 100%;
                height: 100%;
                margin: 0;
                font-family: "微软雅黑";
            }
        }

        .punctuation {
            margin-top: 20px;
            text-align: right;
        }
    }

}
</style>