<template>
  <div class="pages">
    <div class="title padt24 flex-row align-center">
      <span class="">{{ detail.title || detail.title_promotion }}</span>
      <span v-if="detail.shi != 0 || detail.ting != 0 || detail.wei != 0" class="line"></span>
      <span v-if="detail.shi != 0 || detail.ting != 0 || detail.wei != 0">{{ detail.shi }}室{{ detail.ting }}厅{{ detail.wei
      }}卫</span>
      <span class="line" v-if="detail.mianji"></span>
      <span v-if="detail.mianji">{{ detail.mianji }}平米</span>
      <span class="line" v-if="detail.chaoxiang"></span>
      <span v-if="detail.chaoxiang">{{ detail.chaoxiang }}</span>
      <span class="line" v-if="detail.sz_floor || detail.total_floor"></span>
      <span v-if="detail.sz_floor || detail.total_floor">{{ detail.sz_floor }}/{{ detail.total_floor }}层</span>
      <span v-if="detail.score && detail.cj_type <= 0" class="line house_score" style="background: #1dbfff"
        @click="houseScoreClick">
      </span>
      <span v-if="detail.score && detail.cj_type <= 0" @click="houseScoreClick" class="house_score">
        {{ detail.score }}
      </span>
      <span v-if="detail.score && detail.cj_type > 0" class="line house_dealTime" style="background: #da4c44">
      </span>
      <span v-if="detail.score && detail.cj_type > 0" class="house_dealTime">
        {{ detail.cj_time_show }}成交
      </span>
      <!-- <div class="title_con">{{ detail.title }}</div>
      <div class="line"></div>
      <div class="id">{{ detail.id }}</div> -->
    </div>
    <div class="cont">
      <div class="tabs flex-row align-center">
        <div class="flex-row align-center flex-1">
          <div class="tab" v-for="item in tabList" :key="item.id" :class="{ active: currentTab == item.id }"
            @click="clickTab(item)">
            {{ item.name }}
          </div>
        </div>
        <div class="tab_right flex-row" v-if="currentTab == 2">
          <el-popover placement="left-start" width="300" trigger="click" v-model="showAddPopo">
            <div class="shares_title flex-row align-center">
              <div class="share_line"></div>
              <div class="share_title">新增</div>
            </div>
            <div class="flex-row f-wrap">
              <el-popover placement="left" width="180" trigger="click" v-if="uploadJudge == 1 &&
                currentTab == 2 &&
                detail.protect == 0 &&
                !(
                  detail.unilateral_agent == 1 &&
                  detail.unilateral_agent_auth == 0
                )
                " v-model="mobile_up">
                <div class="up_qrcode">
                  <div class="min_img flex-row align-center j-center">
                    <div id="qrcode" class="u_qrcode" ref="qrCodeUrl"></div>
                  </div>
                  <div class="up_tip flex-row align-center j-center">
                    扫码上传中 请不要关闭本页面
                  </div>
                  <div class="up_tip cancel flex-row align-center j-center" @click="cancelUp">
                    取消上传
                  </div>
                </div>

                <div slot="reference" class="f-items1" @click="makeMiniCode">
                  <div class="share_img">
                    <img src="https://img.tfcs.cn/backup/static/admin/house/phone_up.png" alt="" />
                    <div class="share_tip">手机</div>
                  </div>
                  <div class="share_name">手机上传</div>
                </div>
                <!-- <span-- style="cursor: pointer; padding-bottom: 15px"
                  >手机上传</span-->
              </el-popover>
              <!--<el-popover placement="right" width="180" trigger="click"> -->
              <div class="tab tab_right" v-if="uploadJudge == 1 &&
                currentTab == 2 &&
                detail.protect == 0 &&
                !(
                  detail.unilateral_agent == 1 &&
                  detail.unilateral_agent_auth == 0
                )
                ">
                <el-upload class="upload-demo" v-loading="up_loading5" multiple
                  action="/api/common/file/upload/admin?category=6" :headers="upload_headers" :show-file-list="false"
                  :limit="100" :file-list="fileLists" accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif"
                  list-type="picture-card" :on-success="(response, file, fileList) => {
                    onUploadSuccess1({
                      response,
                      file,
                      fileList,
                      type: 1,
                    });
                  }
                    " :before-upload="(e) => {
    return beforeUpload(e, 5);
  }
    ">
                  <div class="f-items1">
                    <div class="share_img">
                      <img src="https://img.tfcs.cn/backup/static/admin/house/add_img.png" alt="" />
                      <div class="share_tip">电脑</div>
                    </div>
                    <div class="share_name">新增图片</div>
                  </div>
                </el-upload>
              </div>
              <div v-else class="f-items1" @click="housePhotoSurvey">
                <div class="share_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/house/add_img.png" alt="" />
                  <div class="share_tip">电脑</div>
                </div>
                <div class="share_name">新增图片</div>
              </div>
              <!-- <div
                slot="reference"
                style="cursor: pointer; padding-bottom: 15px"
              >
                <span>新增图片</span>
              </div> -->
              <!-- </el-popover> -->
              <!-- <span
                style="
                  display: inline-block;
                  cursor: pointer;
                  padding-bottom: 15px;
                "
                @click="onShoot"
                >相机拍摄</span
              > -->
              <div class="f-items1" @click="onShoot">
                <div class="share_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/house/phone_photo.png" alt="" />
                  <div class="share_tip">相机</div>
                </div>
                <div class="share_name">VR拍摄</div>
              </div>
              <div class="f-items1" @click="addVrUrl">
                <div class="share_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/house/add_vr.png" alt="" />
                  <div class="share_tip">链接</div>
                </div>
                <div class="share_name">添加vr链接</div>
              </div>
              <div class="f-items1" @click="addHouseVideoShow">
                <div class="share_img">
                  <img src="https://img.tfcs.cn/backup/static/admin/house/add_video.png" alt="" />
                </div>
                <div class="share_name">添加视频</div>
              </div>
              <!-- <div
                style="cursor: pointer; padding-bottom: 15px"
                @click="addVrUrl"
              >
                添加vr链接
              </div> -->
              <!-- <div
                style="cursor: pointer; padding-bottom: 15px"
                @click="addHouseVideoShow"
              >
                添加视频
              </div> -->
            </div>
            <el-button slot="reference" type="primary" size="small" style="margin-bottom: -9px; border-radius: none">+
              新增</el-button>
          </el-popover>
        </div>
      </div>
      <div class="con" id="csgojintian">
        <el-row v-if="currentTab == 1">
          <el-col :span="14" style="border-right: 2px solid #f1f4fa; padding: 20px 12px">
            <div class="left-content div row">
              <div class="avatar" style="display: none"></div>
              <div class="avatar">
                <el-carousel :autoplay="false" height="130px" indicator-position="none" v-if="TimeLine_picList.photo_first &&
                  !Array.isArray(TimeLine_picList.photo_first)
                  " @change="onPicChange">
                  <el-carousel-item v-for="(img, index) in showPhotosList" :key="index" :label="index + 1">
                    <img class="house_img" :src="$imageFilter(img.url, 'w_240')" alt="" />
                    <div class="cover_deal" v-if="detail.cj_type && detail.cj_type > 0">
                      <img src="https://img.tfcs.cn/backup/static/admin/house/<EMAIL>" alt=""
                        draggable="false" />
                    </div>
                  </el-carousel-item>
                </el-carousel>
                <img v-else :src="this.$imageFilter(
                  'https://img.tfcs.cn/backup/static/admin/house/<EMAIL>',
                  'w_240'
                )
                  " alt="" />
                <div class="indicator" v-if="showPhotosList.length">
                  {{ current_pic_index }}/{{ showPhotosList.length }}
                </div>
              </div>
              <div class="detail-box div flex-1">
                <div class="name div row align-center">
                  <span class="n flex-row flex-1">{{ detail.title || detail.title_promotion }}</span>
                  <div class="div row">
                    <div class="wh div row" @click="toEdit">
                      <img src="https://img.tfcs.cn/backup/static/admin/customer/ziliao.png" alt="" />
                      维护资料
                    </div>
                    <div class="wh div row" @click="showRemind">
                      <img src="https://img.tfcs.cn/backup/static/admin/customer/tx.png" alt="" />
                      提醒
                    </div>
                  </div>
                </div>
                <div class="bind-box div row" v-if="detail.loudong != 0 || detail.danyuan != 0 || detail.fanghao != 0">
                  <div class="b-n flex-row align-center">
                    {{ detail.loudong }}-{{ detail.danyuan }}-{{
                      detail.fanghao
                    }}
                    <span v-if="!show_fanghao &&
                        !(
                          detail.unilateral_agent == 1 &&
                          detail.unilateral_agent_auth == 0
                        )
                        " @click="showFanghao" class="icon flex-row align-center">
                      <img src="@/assets/eye.png" alt="" />
                    </span>
                  </div>
                </div>
                <div class="label-box div row">
                  <div class="lab level" :class="'level' + detail.level" v-if="detail.level">
                    {{ detail.level + "级" }}
                  </div>
                  <div class="left lab" v-else>暂未定级</div>
                  <div class="lab status" :class="'status' + detail.trade_status_id">
                    {{ detail.trade_status }}
                  </div>
                  <!-- <div class="right lab">{{ detail.trade_status }}</div> -->
                  <div class="type jujiao flex-row align-center" v-if="detail.is_top">
                    <div class="img">
                      <img src="@/assets/<EMAIL>" alt="" />
                    </div>
                    <div class="type_name">聚焦</div>
                  </div>
                  <div class="type vip flex-row align-center" v-if="detail.djwtr_id > 0">
                    <div class="img">
                      <img src="@/assets/<EMAIL>" alt="" />
                    </div>
                    <div class="type_name">VIP</div>
                  </div>
                  <div class="type weituo flex-row align-center" v-if="detail.wtr_id">
                    <div class="img">
                      <img src="@/assets/<EMAIL>" alt="" />
                    </div>
                    <div class="type_name">委托</div>
                  </div>

                  <div class="type yaoshi flex-row align-center" v-if="detail.has_key">
                    <div class="img">
                      <img src="@/assets/<EMAIL>" alt="" />
                    </div>
                    <div class="type_name">有钥匙</div>
                  </div>
                </div>
              </div>
            </div>
            <div :is="is_tabs" keep-alive :label="detail.label" :l_list="detail" @onClickBtn="updateLabels"
              :is_show_label_btn="1" :phone_number="detail.whr.tel"></div>
          </el-col>
          <el-col :span="10">
            <div class="right-content">
              <!-- 房源维护完成度-开始 -->
              <el-dialog title="维护详情" :visible.sync="houseScoreVisible" width="30%">
                <houseMaintain :detailScore="detail.score" :MaintainData="MaintainData" @foldTitlePhoto="foldTitlePhoto"
                  @scollView="scollView">
                </houseMaintain>
              </el-dialog>
              <!-- 房源维护完成度-结束 -->
              <div class="name-box div row name-box-title">
                <span class="title mr30"> 业主信息 </span>
                <div class="row div name-box-tem">
                  <!-- <template
                    v-if="
                      detail.current_login_user &&
                      detail.current_login_user.privilege &&
                      detail.current_login_user.privilege.auth_is_top == 1
                    "
                  >
                    <div class="btn-row" @click="setTop">
                      {{ detail.is_top == 1 ? "取消聚焦" : "设置聚焦" }}
                    </div>
                    <span class="row-label mr30">/</span>
                  </template> -->
                  <div class="btn-row" @click="setWuxiao">标无效</div>
                  <span class="row-label mr30">/</span>
                  <div class="btn-row" @click="onClickCus">审批</div>
                  <!-- <span class="row-label">/</span>
                  <div class="btn-row" @click="houseGroup">群发房源</div> -->
                  <span class="row-label mr30">/</span>
                  <div class="btn-row">
                    <el-popover placement="left" width="300" trigger="click" v-model="share_pop">
                      <div class="shares_title flex-row align-center">
                        <div class="share_line"></div>
                        <div class="share_title">分享</div>
                      </div>
                      <div class="f-list f-list1 flex-row">
                        <div class="f-items" v-for="item in share_status_list" :key="item.values"
                          @click="onClickShare(item)">
                          <div class="share_img">
                            <img :src="item.img" alt="" />
                          </div>
                          <div class="share_name">
                            {{ item.name }}
                          </div>
                          <div class="share_desc">
                            {{ item.desc }}
                          </div>
                        </div>
                      </div>
                      <div slot="reference" class="row align-center div">
                        <span>分享</span>
                        <!-- <img
                        src="https://img.tfcs.cn/backup/static/admin/customer/zhankai.png"
                        alt=""
                      /> -->
                      </div>
                    </el-popover>
                  </div>
                </div>
              </div>
              <div class="name-box div row align-center" v-for="(item, index) in detail.tel" :key="item.id">
                <span class="flex-1 mr30">{{ item.owner }}
                  (
                  <template v-if="item.sex_name">{{ item.sex == 1 ? "先生" : "女士" }}-
                  </template>
                  {{ item.type_name }})</span>

                <span class="mr30">{{ item.owner_tel }}</span>
                <div class="btn b-tel" :class="{ hidden: index != 0 }" @click="viewCustomerTel1">
                  {{ index == 0 ? "查看电话" : "" }}
                </div>

                <!-- <div class="btn" v-if="is_type_detail != 3" @click="updateLabels">
                更新标签
              </div> -->
              </div>
              <div class="title padt24">房源跟进</div>
              <!-- <div class="follow-box div row">
                <span>房源等级</span>
                <div
                  class="follow-item level"
                  v-for="item in level_list"
                  :key="item.id"
                  :class="[
                    item.id == detail.level ? `level${detail.level}` : '',
                  ]"
                  @click="onClickLevel(item)"
                >
                  {{ item.title }}级
                </div>
              </div> -->
              <div class="houseFollowBtn">
                <div class="f-l row div">
                  <el-popover placement="top" width="240" trigger="click" v-model="visiblepop">
                    <div class="f-list">
                      <div class="f-item" v-for="item in follow_status_list" :key="item.values"
                        @click="onClickStatus(item)">
                        {{ item.name }}
                      </div>
                    </div>
                    <div slot="reference" class="row align-center div">
                      <span>{{ visiblepop_title }}</span>
                      <img src="https://img.tfcs.cn/backup/static/admin/customer/zhankai.png" alt="" />
                    </div>
                  </el-popover>
                </div>
                <el-button @click="appointmentLook" type="primary" style="margin-right: 10px" size="mini" plain>
                  预约带看
                </el-button>
                <el-button type="primary" plain @click="houseSurvey" size="mini">
                  房屋实勘
                </el-button>
              </div>
              <div class="follow-input div row" :class="{ isborder: is_view_tel_desc }">
                <div class="popover" v-show="is_view_tel_desc">
                  {{ is_view_tel_desc }}
                  <div class="triangle"></div>
                </div>
                <div class="f-r div row" slot="reference">
                  <div id="Go" class="paperview-input-text" ref="gain" contenteditable="true"
                    placeholder="请输入跟进内容(企业内公开)"></div>
                  <el-button @click="onChangeStatus" style="margin-left: auto" type="primary" size="mini">提交</el-button>
                </div>
              </div>
              <!-- 同事列表下拉 -->
              <div class="colleague-box" v-show="showDropdown">
                <div class="colleague_content" v-for="(item, index) in departmentMember" :key="index"
                  @click="MemberClick(item)">
                  {{ item.name }}
                </div>
              </div>
              <div class="flex-row" style="margin-bottom: 32px;">
                <!-- 同事按钮 -->
                <tMemberDropdown trigger="click" filterable @command="MemberClick">
                  <div class="FriendsPiece">@同事</div>
                </tMemberDropdown>
                <!-- 添加图片 -->
                <div class="addPicture addPicture-btn">
                  <el-upload class="uploader-create" :disabled="disabled_picture" :headers="myHeader"
                    :action="picture_upurl" :on-success="(e) => UploadParamsSuccess(e)" accept=".jpg,jpeg,.png"
                    :show-file-list="false" :multiple="true">
                    <span @click="addPictures($event)"><i class="el-icon-plus"></i>图片</span>
                  </el-upload>
                </div>
                <!-- 图片列表 -->
                <div class="picture_list_box" v-for="(item, index) in FollowImgList" :key="index">
                  <img v-if="item.url" :src="item.url" class="photo-item-img" />
                  <div class="delete-picture" @click="deletePicture(index)">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                  </div>
                  <span class="uploader-actions" v-if="item.url">
                    <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                      <i class="el-icon-view"></i>
                    </span>
                  </span>
                </div>
              </div>
              <el-tabs v-model="recordName" class="record_content" @tab-click="recordClick">
                <el-tab-pane label="跟进记录" name="Follow"></el-tab-pane>
                <el-tab-pane label="带看记录" name="TakeLook"></el-tab-pane>
                <el-tab-pane label="电话记录" name="Phone"></el-tab-pane>
              </el-tabs>
              <!-- <div class="title">跟进记录</div> -->
              <div v-show="recordName == 'Follow'" class="follow-record" v-infinite-scroll="loadMoreFollow"
                style="overflow: auto; height: 500px" @click="hideColleague">
                <el-timeline style="margin-left: 10px" v-if="follow_list.length">
                  <el-timeline-item v-for="(follow, index) in follow_list" :key="index" ref="record" placement="top"
                    color="#2D84FB">
                    <div class="FollowRecord">
                      <div style="width: 100%">
                        <div class="agent_info flex-row align-center">
                          <div class="time">
                            {{ follow.ctime }}
                          </div>
                          <div class="agent_name" v-if="follow.crm_user">
                            {{
                              follow.crm_user && follow.crm_user.user_name
                            }}/{{
  follow.crm_user && follow.crm_user.department
}}
                          </div>
                          <!-- 置顶图标 -->
                          <!-- <div class="TopIcon" v-if="follow.is_top == 1">
                            顶
                          </div> -->
                          <div class="show_is_top" v-if="follow.is_top == 1">
                            已置顶
                          </div>
                          <div class="follow_info_box">
                            <div class="follow_info_praise" @click="setFollowPraise(follow)">
                              <i class="el-icon-newfontic_zan"></i>
                            </div>
                            <div class="follow_info_copy" @click="onCopyValues(follow)">
                              <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                            </div>
                            <div class="follow_add_top" @click="setFollowTop(follow)">
                              <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                              <span v-if="follow.is_top == 0">设为置顶</span>
                              <span v-if="follow.is_top == 1">取消置顶</span>
                            </div>
                          </div>
                        </div>
                        <div class="FollowText">
                          <div class="f_content" :class="{ red: follow.effect_type == 8 }">
                            <!-- {{ follow.content }} -->
                            <div slot="reference" v-html="follow.content"></div>
                          </div>
                        </div>
                        <!-- 跟进图片 -->
                        <div v-if="follow.images && follow.images.length" class="follow-picture">
                          <div class="follow-picture-box" v-for="(item, index) in follow.images" :key="index">
                            <img :src="$imageFilter(item, 'w_240')" alt="" />
                            <span class="uploader-actions" v-if="item">
                              <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                                <i class="el-icon-view"></i>
                              </span>
                            </span>
                            <div class="follow-delete-picture" @click="followDelPicture(follow, item)">
                              <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                            </div>
                          </div>
                        </div>
                        <div class="follow-praise" v-if="follow.star.length > 0">
                          <div class="follow-praise-box">
                            <span class="follow-praise-img">
                              <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                            </span>
                            <span class="follow-praise-separate"></span>
                            <span class="follow-praise-text">
                              <span>{{ follow.star.join("，") }}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <myEmpty v-else></myEmpty>
                <!-- 同事信息框 -->
                <div class="infoFrame" @click.stop.prevent="">
                  <div class="infoFrame-box">
                    <!-- 头像框 -->
                    <div class="infoFrame_icon">
                      {{ this.colleagueDetails.headPortrait }}
                    </div>
                    <!-- 同事信息 -->
                    <div class="infoFrame-info">
                      <span>{{ this.colleagueDetails.user_name }}</span>
                      <span>{{ this.colleagueDetails.department }}</span>
                      <div class="infoFrame-phone">
                        <span>{{ this.colleagueDetails.phone }}</span>
                        <div class="numberTop flex-row align-center" @click="copyPhone">
                          <i class="el-icon-phone" ref="tel" style="font-size: 14px; color: #48ea0a" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 小三角 -->
                  <div class="infoAngle">
                    <i class="el-icon-caret-bottom"></i>
                  </div>
                </div>
              </div>
              <!-- 带看记录 -->
              <div class="follow-record" v-show="recordName == 'TakeLook'" v-infinite-scroll="loadMoreTakeLook"
                style="overflow: auto; height: 500px">
                <el-timeline v-if="TakeLookRecord_list.length" style="margin-left: 10px">
                  <el-timeline-item v-for="(TakeLook, index) in TakeLookRecord_list" :key="index" placement="top"
                    color="#2D84FB">
                    <div class="FollowRecord">
                      <div style="width: 100%">
                        <div class="agent_info flex-row align-center">
                          <div class="time">
                            {{ TakeLook.ctime }}
                          </div>
                          <div class="agent_name" v-if="TakeLook.crm_user">
                            {{
                              TakeLook.crm_user && TakeLook.crm_user.user_name
                            }}/{{
  TakeLook.crm_user && TakeLook.crm_user.department
}}
                          </div>
                        </div>
                        <div class="FollowText">
                          <div class="f_content" :class="{ red: TakeLook.effect_type == 8 }">
                            <div slot="reference">
                              <div>
                                <span>{{ TakeLook.take_date }}</span>
                                <span v-if="TakeLook.take_time == 1" style="margin-left: 5px">上午</span>
                                <span v-if="TakeLook.take_time == 2" style="margin-left: 5px">下午</span>
                                <span v-if="TakeLook.take_time == 3" style="margin-left: 5px">晚上</span>
                              </div>
                              {{ TakeLook.content + "," }}客户:
                              {{ TakeLook.name + " " + TakeLook.mobile }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <myEmpty v-else></myEmpty>
              </div>
              <!-- 电话记录 -->
              <div class="follow-record" v-show="recordName == 'Phone'" v-infinite-scroll="loadMorePhone"
                style="overflow: auto; height: 500px">
                <el-timeline v-if="PhoneRecord_list.length" style="margin-left: 10px">
                  <el-timeline-item v-for="(Phone, index) in PhoneRecord_list" :key="index" placement="top"
                    color="#2D84FB">
                    <div class="FollowRecord">
                      <div style="width: 100%">
                        <div class="agent_info flex-row align-center">
                          <div class="time">
                            {{ Phone.ctime }}
                          </div>
                          <div class="agent_name" v-if="Phone.crm_user">
                            {{ Phone.crm_user && Phone.crm_user.user_name }}/{{
                              Phone.crm_user && Phone.crm_user.department
                            }}
                            <span v-if="Phone.house_follow.id &&
                                Phone.house_follow.call_show_phone != ''
                                ">
                              打给{{ " " + Phone.house_follow.call_name }}
                            </span>
                            <span style="margin-left: 5px" v-if="Phone.house_follow.id &&
                              Phone.house_follow.call_show_phone != ''
                              ">
                              通过中间号码({{
                                Phone.house_follow.call_show_phone
                              }})拨打
                            </span>
                          </div>
                        </div>
                        <div class="FollowText">
                          <div class="f_content" :class="{ red: Phone.effect_type == 8 }">
                            <div slot="reference" class="flex-row">
                              {{ Phone.content }}
                              <span v-if="Phone.house_follow.content">,{{ Phone.house_follow.content }}</span>
                            </div>
                          </div>
                        </div>
                        <AudioPlayer v-if="Phone.house_follow.id &&
                          Phone.house_follow.record_url != ''
                          " :activity="Phone" :detail="detail" select="houseFollow">
                        </AudioPlayer>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <myEmpty v-else></myEmpty>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="currentTab == 2" class="tab_tops">
          <el-tabs v-model="activeName" class="tabHead">
            <el-tab-pane :label="'图片(' + housePhoVidVRnumber.photo_num + ')'" name="first">
              <div style="height: 500px; overflow-y: auto" class="imgList flex-row"
                v-infinite-scroll="loadMoreHousePhone">
                <el-timeline v-if="TimeLine_picList.photo_first &&
                  !Array.isArray(TimeLine_picList.photo_first)
                  " style="margin-left: 10px">
                  <el-timeline-item ref="record" placement="top" color="#2D84FB">
                    <div class="TimeLineRecord">
                      <div style="width: 100%">
                        <div class="TimeLine_info flex-row align-center">
                          <div class="maintainer-name">
                            {{ TimeLine_picList.photo_first.cname }} /
                            {{ TimeLine_picList.photo_first.department }}
                          </div>
                          <div class="maintainer-label">
                            {{ TimeLine_picList.photo_first.identity }}
                          </div>
                          <div class="maintainer-first">首次实勘</div>
                        </div>
                        <div class="TimeLine_time">
                          上传时间：{{ TimeLine_picList.photo_first.ctime }}
                        </div>
                        <div style="flex-wrap: wrap" class="flex-row">
                          <div class="img_item" v-for="(item, idx) in TimeLine_picList.photo_first
                            .list" :key="item.id">
                            <div class="img" @mouseleave="typepop = false" @click="
                              operImg(
                                item,
                                idx,
                                TimeLine_picList.photo_first.list
                              )
                              ">
                              <img :src="$imageFilter(item.url, 'w_240')" alt="" />
                              <div class="type">
                                {{ item.category_id | filterImgType }}
                              </div>

                              <div class="mask"></div>
                              <div class="opers j-center align-center">
                                <div class="preview"></div>
                                <div v-if="!(
                                  detail.unilateral_agent == 1 &&
                                  detail.unilateral_agent_auth == 0
                                )
                                  " class="change_type flex-row align-center j-center"
                                  @click.prevent.stop="setHouseCover(item)">
                                  {{ item.is_cover == 1 ? "封面" : "设为封面" }}
                                </div>
                                <div class="change_type" v-if="!(
                                  detail.unilateral_agent == 1 &&
                                  detail.unilateral_agent_auth == 0
                                )
                                  ">
                                  <el-popover placement="bottom" width="260" trigger="manual" v-model="typepop"
                                    :append-to-body="false" :popper-options="{
                                      boundariesElement: 'body',
                                      gpuAcceleration: true,
                                      positionFixed: true,
                                      preventOverflow: true,
                                    }">
                                    <div class="f-list f-list1">
                                      <div class="f-item" v-for="type in imgTypeList" :key="type.values"
                                        @click.stop.prevent="
                                          onClickImgType(type, item)
                                          ">
                                        {{ type.name }}
                                      </div>
                                    </div>
                                    <div slot="reference" class="row align-center div j-center"
                                      style="position::absolute;z-index:3" @click.stop.prevent="typepop = true">
                                      <span>标记类型</span>
                                      <i class="el-icon-arrow-right" style="color: #fff"></i>
                                    </div>
                                  </el-popover>
                                </div>
                              </div>
                              <div class="hover-delete-Photo" v-show="show_shitu"
                                @click.prevent.stop="onClickImgType({ is_del: 1 }, item)">
                                <i class="el-icon-delete-solid"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                  <el-timeline-item v-for="(firstItm, index) in TimeLine_picList_photos" :key="'new_' + index"
                    ref="record" placement="top" color="#2D84FB">
                    <div class="TimeLineRecord">
                      <div style="width: 100%">
                        <div class="TimeLine_info flex-row align-center">
                          <div class="maintainer-name">
                            {{ firstItm.cname }} / {{ firstItm.department }}
                          </div>
                          <div class="maintainer-label">
                            {{ firstItm.identity }}
                          </div>
                          <div class="maintainer-approve">
                            <el-button v-if="firstItm.approve_sk == 1" type="success" plain size="mini"
                              @click="surveyApprove(firstItm)">
                              实勘图审批
                            </el-button>
                          </div>
                        </div>
                        <div class="TimeLine_time">
                          上传时间：{{ firstItm.ctime }}
                        </div>
                        <div style="flex-wrap: wrap" class="flex-row">
                          <div class="img_item" v-for="(item, index) in firstItm.list" :key="item.id">
                            <div class="img" @mouseleave="typepop = false" @click="operImg(item, index, firstItm.list)">
                              <img :src="$imageFilter(item.url, 'w_240')" alt="" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <template v-if="!TimeLine_picList.photo_first &&
                  !Array.isArray(TimeLine_picList.photo_first) &&
                  !(
                    detail.unilateral_agent == 1 &&
                    detail.unilateral_agent_auth == 0
                  )
                  ">
                  <div class="flex-row align-center j-center flex-1">
                    <el-upload v-loading="up_loading6" v-if="detail.protect == 0" multiple
                      action="/api/common/file/upload/admin?category=6" :headers="upload_headers" :show-file-list="false"
                      :limit="10" accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif" list-type="picture-card" :on-success="(response, file, fileList) => {
                        onUploadSuccess1({
                          response,
                          file,
                          fileList,
                          type: 1,
                          empty: 1,
                        });
                      }
                        " :before-upload="(e) => {
    return beforeUpload(e, 6);
  }
    ">
                      <i class="el-icon-plus"></i></el-upload>
                    <div v-else class="el-upload el-upload--picture-card" @click="beforeUploadShowMessage">
                      <i class="el-icon-plus"></i>
                    </div>
                  </div>
                </template>
              </div>
            </el-tab-pane>
            <el-tab-pane :label="'视频(' + housePhoVidVRnumber.video_num + ')'" name="second">
              <el-table :data="videoList" v-loading="isVideoLoading" border>
                <el-table-column label="id" prop="id"> </el-table-column>
                <el-table-column label="添加人" prop="crm_user_name">
                </el-table-column>
                <el-table-column label="添加人部门" prop="crm_user_department">
                </el-table-column>
                <el-table-column label="添加时间" prop="ctime">
                </el-table-column>
                <el-table-column label="链接" v-slot="{ row }">
                  <el-link type="info" @click="onCopyVideo(row.url)">复制</el-link>
                  <el-link type="success" style="margin-left: 12px" @click="browseVideo(row.url)">浏览</el-link>
                  <el-link type="primary" style="margin-left: 12px" @click="editVideo(row)">修改</el-link>
                  <el-link type="danger" style="margin-left: 12px" @click="delVideo(row.id)">删除</el-link>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="'vr(' + housePhoVidVRnumber.vr_num + ')'" name="third">
              <el-table border :data="vrList" v-loading="isVrListLoading" :header-cell-style="{ background: '#EBF0F7' }"
                highlight-current-row :row-style="$TableRowStyle" class="vrTable">
                <el-table-column label="添加时间" prop="ctime">
                </el-table-column>
                <el-table-column label="类型" prop="vr_type_name">
                </el-table-column>
                <el-table-column label="vr描述" prop="memo"> </el-table-column>
                <el-table-column label="添加人" prop="crm_user_name" v-slot="{ row }">
                  <div class="operator">
                    <div class="operator_left">
                      <div class="surName">
                        <span>{{ row.crm_user_name.charAt(0) }}</span>
                      </div>
                    </div>
                    <div class="operator_right">
                      <span>{{ row.crm_user_name }}</span>
                      <span>{{ row.crm_user_department }}</span>
                    </div>
                  </div>
                </el-table-column>
                <el-table-column label="链接" v-slot="{ row }">
                  <el-link type="info" @click="copyVrLink(row.vr_url)">复制</el-link>
                  <el-link style="margin-left: 12px" type="success" @click="browseVr(row)">浏览</el-link>
                  <el-link style="margin-left: 12px" type="primary" @click="editVr(row.id)">修改</el-link>
                  <el-link style="margin-left: 12px" type="danger" @click="delHousrVr(row.id)">删除</el-link>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
          <!-- 图片 -->
        </el-row>
        <template v-if="currentTab == 3">
          <el-row>
            <el-col :span="24">
              <div class="weituo_a">
                <!-- 解决地图问题  -->
                <div class="weituo_key"></div>
                <!-- 解决地图问题  -->
                <div class="weituo_key">
                  <el-row>
                    <el-col :span="18">
                      <div class="title flex-row align-center j-between">
                        <span> 添加钥匙 </span>
                        <div class="key_button">
                          <el-button type="primary" :loading="keyLoading" :disabled="detail.protect == 1 ||
                            (detail.unilateral_agent == 1 &&
                              detail.unilateral_agent_auth == 0)
                            " @click="setWeituo(2)">立即添加</el-button>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                  <el-form :inline="true">
                    <el-form-item label="钥匙编号">
                      <el-input style="width: 240px" v-model="key_num"></el-input>
                    </el-form-item>
                    <el-form-item label="门禁密码">
                      <el-input class="codeGate" style="width: 240px" v-model="key_pass"
                        placeholder="电子密码锁可输入"></el-input>
                    </el-form-item>
                  </el-form>

                  <el-row>
                    <el-col :span="18">
                      <div class="title">
                        <div class="padd10">钥匙添加记录</div>
                        <el-table v-loading="is_table_loading" :data="keyData" border
                          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
                          :row-style="$TableRowStyle">
                          <!--  @selection-change="handleSelectionChange" -->
                          <!-- <el-table-column type="selection" width="55">
                          </el-table-column> -->
                          <el-table-column width="100" prop="key_num" label="钥匙编号"></el-table-column>

                          <el-table-column label="添加人" prop="user_name">
                          </el-table-column>
                          <el-table-column label="门禁密码" prop="key_pass"></el-table-column>
                          <el-table-column label="添加时间" prop="ctime"></el-table-column>
                          <el-table-column label="操作" v-slot="{ row }">
                            <el-popconfirm v-if="row.is_del" title="确定删除吗？" style="margin: 0 10px"
                              @onConfirm="deleteKeyLog(row)">
                              <el-link slot="reference" type="danger" icon="el-icon-delete">删除</el-link>
                            </el-popconfirm>
                          </el-table-column>
                        </el-table>
                        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
                          :total="key_params.total" :page-size="key_params.rows" :current-page="key_params.page"
                          @current-change="onKeyPageChange">
                        </el-pagination>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <div class="weituo_weituo weituo_key">
                  <!-- <div class="title">设置委托</div> -->
                  <el-row>
                    <el-col :span="18">
                      <div class="title flex-row align-center j-between">
                        <span>
                          设置普通委托
                          <span style="
                              font-size: 14px;
                              margin-left: 5px;
                              color: #dc4545;
                            ">
                            <!--（仅添加人可见）-->
                          </span>
                        </span>
                        <div class="key_button">
                          <el-button type="primary" :loading="weituoLoading" :disabled="detail.protect == 1 ||
                            detail.wtr_id > 0 ||
                            weituoDiabled ||
                            (detail.unilateral_agent == 1 &&
                              detail.unilateral_agent_auth == 0)
                            " @click="setWeituo(1)">设置普通委托</el-button>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                  <div class="up_img flex-row" style="text-align: center" v-if="detail.auth_view_wt == 1 &&
                    detail.wtr_id > 0 &&
                    weituo_img &&
                    weituo_img.length
                    ">
                    <ul class="el-upload-list el-upload-list--picture-card">
                      <li class="img_item" v-for="(img, index) in weituo_img" :key="index + '_w'">
                        <div class="el-upload-list__item is-success">
                          <img :src="$imageFilter(img, 'w_240')" alt="" class="el-upload-list__item-thumbnail" />
                        </div>
                      </li>
                      <!--<li
                        class="upload-box"
                        v-if="
                          weituo_img.length < 10 &&
                          !(
                            detail.protect == 1 ||
                            detail.wtr_id > 0 ||
                            weituoDiabled
                          ) &&
                          !(
                            detail.unilateral_agent == 1 &&
                            detail.unilateral_agent_auth == 0
                          )
                        "
                      >
                        <el-upload
                          v-loading="up_loading2"
                          multiple
                          action="/api/common/file/upload/admin?category=6"
                          :headers="upload_headers"
                          :show-file-list="false"
                          :limit="10"
                          accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif"
                          list-type="picture-card"
                          :disabled="
                            detail.protect == 1 ||
                            detail.wtr_id > 0 ||
                            weituoDiabled ||
                            (detail.unilateral_agent == 1 &&
                              detail.unilateral_agent_auth == 0)
                          "
                          :on-success="
                            (response, file, fileList) => {
                              onUploadSuccess({
                                response,
                                file,
                                fileList,
                                type: 2,
                              });
                            }
                          "
                          :on-exceed="
                            (file, fileList) => {
                              onExceed({
                                file,
                                fileList,
                                type: 2,
                              });
                            }
                          "
                          :before-upload="
                            (e) => {
                              return beforeUpload(e, 2);
                            }
                          "
                        >
                          <div class="upload_placeholder">
                            <p><i class="el-icon-picture-outline"></i></p>
                          </div>
                          <div class="el-upload__tip" slot="tip">凭证</div>
                        </el-upload>
                      </li>-->
                    </ul>
                  </div>
                </div>
                <div class="weituo_weituo weituo_key" id="cont">
                  <el-row>
                    <el-col :span="18">
                      <div class="title">
                        <div class="padd10">委托记录</div>
                        <el-table :data="weituoData" border :header-cell-style="{ background: '#EBF0F7' }"
                          highlight-current-row :row-style="$TableRowStyle">
                          <!--  @selection-change="handleSelectionChange" -->
                          <!-- <el-table-column type="selection" width="55">
                          </el-table-column> -->

                          <el-table-column label="委托人" prop="cname">
                          </el-table-column>

                          <el-table-column label="委托时间" prop="ctime"></el-table-column>
                          <template v-if="detail.house_manager">
                            <el-table-column label="操作" v-slot="{ row }">
                              <!-- <el-popconfirm
                                title="确定撤销吗？"
                                style="margin: 0 10px"
                                @onConfirm="deleteWeituo(row)"
                              > -->
                              <el-link slot="reference" type="danger" icon="el-icon-delete"
                                @click="showCancelReason(row, 'common')">撤销</el-link>
                              <!-- </el-popconfirm> -->
                            </el-table-column>
                          </template>
                        </el-table>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <div class="weituo_weituo weituo_key">
                  <!--  v-if="
                    (detail.auth_view_vip == 1 || detail.djwtr_id == 0) &&
                    !(
                      detail.unilateral_agent == 1 &&
                      detail.unilateral_agent_auth == 0
                    )
                  "-->
                  <el-row>
                    <el-col :span="18">
                      <div class="title flex-row align-center j-between">
                        <span>
                          设置VIP委托<span style="
                              fontsize: 14px;
                              margin-left: 5px;
                              color: #dc4545;
                            ">
                            <!--（仅添加人可见）-->
                          </span>
                        </span>

                        <div class="key_button">
                          <el-button type="primary" :loading="djweituoLoading" :disabled="detail.protect == 1 ||
                            detail.djwtr_id > 0 ||
                            djWeituoDisable ||
                            (detail.unilateral_agent == 1 &&
                              detail.unilateral_agent_auth == 0)
                            " @click="setWeituo(3)">设置VIP委托</el-button>
                        </div>
                      </div>
                    </el-col>
                  </el-row>

                  <el-form v-if="(detail.auth_view_vip == 1 || detail.djwtr_id > 0) &&
                    !(
                      detail.unilateral_agent == 1 &&
                      detail.unilateral_agent_auth == 0
                    )
                    ">
                    <el-form-item label="时间">
                      <el-date-picker v-model="vip_date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" disabled value-format="yyyy-MM-dd HH:mm:ss">
                      </el-date-picker>
                    </el-form-item>
                    <el-form-item label="VIP总价">
                      <el-input style="width: 240px" type="number" disabled v-model="vip_price">
                        <template slot="append">万元</template></el-input>
                    </el-form-item>
                  </el-form>
                  <div class="up_img flex-row" style="text-align: center" v-if="(detail.auth_view_vip == 1 || detail.djwtr_id > 0) &&
                    !(
                      detail.unilateral_agent == 1 &&
                      detail.unilateral_agent_auth == 0
                    ) &&
                    dj_weituo_img &&
                    dj_weituo_img.length
                    ">
                    <ul class="el-upload-list el-upload-list--picture-card">
                      <li class="img_item" v-for="(img, index) in dj_weituo_img" :key="index">
                        <div class="el-upload-list__item is-success">
                          <img :src="$imageFilter(img, 'w_240')" alt="" class="el-upload-list__item-thumbnail" />
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="weituo_weituo weituo_key">
                  <el-row>
                    <el-col :span="18">
                      <div class="title">
                        <div class="padd10">vip记录</div>
                        <el-table :data="vipData" border :header-cell-style="{ background: '#EBF0F7' }"
                          highlight-current-row :row-style="$TableRowStyle">
                          <!--  @selection-change="handleSelectionChange" -->
                          <!-- <el-table-column type="selection" width="55">
                          </el-table-column> -->

                          <el-table-column label="vip委托人" prop="user_name">
                          </el-table-column>

                          <el-table-column label="委托开始时间" prop="dj_btime"></el-table-column>
                          <el-table-column label="委托结束时间" prop="dj_etime"></el-table-column>
                          <el-table-column label="状态" v-slot="{ row }">
                            <el-tag v-if="+new Date(row.dj_etime) > +new Date()" type="success">正常</el-tag>
                            <el-tag v-if="+new Date(row.dj_etime) <= +new Date()" type="warning">已过期</el-tag>
                          </el-table-column>
                          <template v-if="detail.house_manager == 1">
                            <el-table-column label="操作" v-slot="{ row }">
                              <el-link slot="reference" type="danger" icon="el-icon-delete"
                                @click="showCancelReason(row, 'vip')">撤销</el-link>
                            </el-table-column>
                          </template>
                        </el-table>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-col>
          </el-row>
        </template>

        <template v-if="currentTab == 4">
          <el-row>
            <el-col :span="24">
              <div class="map flex-row">
                <div id="map_container" ref="map_container" style="width: 100%; height: 100%"></div>
                <div class="search_container flex-row">
                  <div class="left">
                    <div class="item" :class="{ active: index === search_type_index }"
                      v-for="(item, index) in search_types" :key="index" @click="onClickSearchType(index)">
                      <img :src="item.icon" alt="" />
                      <p>{{ item.name }}</p>
                    </div>
                  </div>
                  <div class="right flex-box column" v-show="show_search_res">
                    <div class="title flex-row j-between align-center">
                      <span>{{ search_types[search_type_index].name }}({{
                        marker_list.length
                      }})</span>
                      <i @click="show_search_res = false" class="close el-icon-close"></i>
                    </div>
                    <div class="marker_list flex-1">
                      <div class="item flex-row align-center" v-for="(item, index) in marker_list" :key="index"
                        @click="onClickSearchRes(item, index)">
                        <span class="num">{{ index + 1 }}</span>
                        <span class="title flex-1">{{ item.name }}</span>
                        <span class="distance">{{ item.dist }}m</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </template>
        <template v-if="currentTab == 5">
          <el-row>
            <el-col :span="24">
              <div class="content-box-crm">
                <div class="table-top-box div row"></div>
                <div class="table-top-box div row">
                  <div class="t-t-b-left div row">
                    <myLabel :arr="opt_type_list" @onClick="changeOptType"></myLabel>
                    <!-- <el-select
                      v-model="log_params.opt_type"
                      size="small"
                      placeholder="请选择操作类型"
                      style="width: 180px; margin-right: 10px"
                      clearable
                      @change="changeOptType"
                    >
                      <el-option
                        v-for="item in opt_type_list"
                        :label="item.name"
                        :key="item.values"
                        :value="item.values"
                      ></el-option>
                    </el-select> -->
                  </div>
                </div>
                <div style="margin-bottom: 22px">
                  <el-button type="primary" v-if="is_online != '26_s' && is_online != ''" @click="exportLogs"
                    :loading="is_loading">导出excel</el-button>
                </div>
                <el-table v-if="is_online != '26_s'" :data="tableData" border ref="bbbb"
                  :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
                  <el-table-column label="时间" prop="ctime" width="180px">
                  </el-table-column>
                  <el-table-column label="类型" prop="opt_type_name" width="100px">
                  </el-table-column>
                  <el-table-column label="资源类型" prop="trade_type_name" width="100px">
                  </el-table-column>
                  <el-table-column label="操作人" prop="remarks" v-slot="{ row }">
                    <div class="agent_info flex-row align-center">
                      <div class="agent_header flex-row align-center j-center" v-if="row.crm_user">
                        {{ row.crm_user && row.crm_user.user_name[0] }}
                      </div>
                      <div class="gent_inf_con">
                        <div>
                          <span>{{ row.crm_user && row.crm_user.user_name }}
                          </span>
                          <span style="margin-left: 10px; color: #f56c6c">{{ row.crm_user && row.crm_user.post }}
                          </span>
                        </div>
                        <div>
                          <span>
                            {{
                              row.crm_user && row.crm_user.company_name
                            }}</span>
                        </div>
                        <div>
                          <span>
                            {{ row.crm_user && row.crm_user.department }}</span>
                        </div>
                      </div>
                    </div>
                  </el-table-column>
                  <!-- <el-table-column label="所在部门" prop="remarks">
                </el-table-column> -->
                  <el-table-column label="操作内容" v-slot="{ row }">
                    {{ row.content }}
                  </el-table-column>
                </el-table>
                <el-pagination v-if="is_online != '26_s'" style="text-align: end; margin-top: 24px" background
                  layout="prev, pager, next" :total="total" :page-size="log_params.rows" :current-page="log_params.page"
                  @current-change="onPageChange">
                </el-pagination>
                <el-form v-if="is_online == '26_s'" :inline="true" :model="appointmentPage" label-width="80px">
                  <el-form-item label="客户姓名">
                    <el-input v-model="appointmentPage.name" placeholder="请输入客户姓名" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="手机号">
                    <el-input v-model="appointmentPage.mobile" placeholder="请输入手机号" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="带看编号">
                    <el-input v-model="appointmentPage.take_no" placeholder="请输入带看编号" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="筛选日期">
                    <div class="block">
                      <el-date-picker v-model="appointmentDate" value-format="yyyy-MM-dd" type="daterange"
                        start-placeholder="开始日期" end-placeholder="结束日期" @change="appointmentDateChange">
                      </el-date-picker>
                    </div>
                  </el-form-item>
                  <el-form-item>
                    <el-button v-if="detail.house_manager == 1" type="primary" @click="exportMakeLogs"
                      :loading="is_loading">导出excel</el-button>
                    <el-button type="info" plain @click="resetSeach">重置</el-button>
                    <el-button type="primary" @click="customerSeach">查询</el-button>
                  </el-form-item>
                </el-form>
                <el-table v-if="is_online == '26_s'" :data="WithShowTabData" border ref="aaaa"
                  :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
                  <el-table-column label="时间" prop="ctime" width="180px">
                  </el-table-column>
                  <el-table-column label="类型" prop="opt_type_name" width="100px">
                  </el-table-column>
                  <el-table-column label="资源类型" prop="trade_type_name" width="100px">
                  </el-table-column>
                  <el-table-column label="操作人" prop="crm_user" v-slot="{ row }">
                    <div class="agent_info flex-row align-center">
                      <div class="agent_header flex-row align-center j-center" v-if="row.crm_user">
                        {{ row.crm_user && row.crm_user.user_name[0] }}
                      </div>
                      <div class="gent_inf_con">
                        <div>
                          <span>{{ row.crm_user && row.crm_user.user_name }}
                          </span>
                          <span style="margin-left: 10px; color: #f56c6c">{{ row.crm_user && row.crm_user.post }}
                          </span>
                        </div>
                        <div>
                          <span>
                            {{
                              row.crm_user && row.crm_user.company_name
                            }}</span>
                        </div>
                        <div>
                          <span>
                            {{ row.crm_user && row.crm_user.department }}</span>
                        </div>
                      </div>
                    </div>
                  </el-table-column>
                  <el-table-column label="操作内容" v-slot="{ row }">
                    <div class="flex-box">
                      <!-- 时间 -->
                      <div>
                        <span>时间：{{ row.take_date + " " }}</span>
                        <span v-if="row.take_time == 1">上午</span>
                        <span v-if="row.take_time == 2">下午</span>
                        <span v-if="row.take_time == 3">晚上</span>
                      </div>
                      <!-- 姓名手机号 -->
                      <div>
                        <span>客户：{{ row.name + " " + row.mobile }}</span>
                      </div>
                      <!-- 陪看 -->
                      <div v-if="row.accompany != ''">
                        <span>陪看人员：{{ row.accompany }}</span>
                      </div>
                      <!-- 单号 -->
                      <div v-if="row.take_no != null && row.view_auth == 1">
                        <span>单号：{{ row.take_no }}</span>
                      </div>
                      <!-- 备注 -->
                      <div v-if="row.content != '' && row.view_auth == 1">
                        <span>备注：{{ row.content }}</span>
                      </div>
                    </div>
                  </el-table-column>
                </el-table>
                <el-pagination v-if="is_online == '26_s'" style="text-align: end; margin-top: 24px" background
                  layout="prev, pager, next" :total="appointmentTotal" :page-size="appointmentPage.per_page"
                  :current-page="appointmentPage.page" @current-change="appointmentPageChange">
                </el-pagination>
              </div>
            </el-col>
          </el-row>
        </template>
        <template v-if="currentTab == 6">
          <el-row>
            <el-col :span="24">
              <!-- 解决地图问题  -->
              <div class="setting" style="display: none"></div>
              <div class="setting">
                <div class="setting_title">角色管理</div>
                <div class="setting_content">
                  <el-form class="el_form" label-width="150px">
                    <el-form-item label="录入人管理">
                      <div class="form_item_row flex-row">
                        <!--<el-select v-model="setting_info.agent"  clearable style="width:300px">
                          <el-option
                            v-for="item in user_list"
                            :key="item.id"
                            :label="item.user_name"
                            :value="item.flm_id"
                           
                          ></el-option>
                        </el-select>-->
                        <el-input placeholder="请选择录入人" v-model="rulesInfo.agent.name" style="width: 300px"
                          @focus="showMemberList('agent')">
                          <i @click="delName($event, 'agent')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>

                    <el-form-item label="维护人管理">
                      <div class="form_item_row flex-row">
                        <!-- <el-select v-model="setting_info.whr" clearable style="width:300px">
                          <el-option
                            v-for="item in user_list"
                            :key="item.id"
                            
                            :label="item.user_name"
                            :value="item.flm_id"
                          ></el-option>
                        </el-select>-->

                        <el-input placeholder="请选择维护人" v-model="rulesInfo.whr.name" style="width: 300px"
                          @focus="showMemberList('whr')">
                          <i @click="delName($event, 'whr')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>
                    <el-form-item label="委托人管理">
                      <div class="form_item_row flex-row">
                        <el-input placeholder="请选择委托人" v-model="rulesInfo.wtr.name" style="width: 300px"
                          @focus="showMemberList('wtr')">
                          <i @click="delName($event, 'wtr')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>

                    <el-form-item label="独家委托人管理">
                      <div class="form_item_row flex-row">
                        <el-input placeholder="请选择独家委托人" v-model="rulesInfo.djwtr.name" style="width: 300px"
                          @focus="showMemberList('djwtr')">
                          <i @click="delName($event, 'djwtr')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>
                    <el-form-item label="钥匙持有人管理">
                      <div class="form_item_row flex-row">
                        <!--<el-select v-model="setting_info.ysr" clearable style="width:300px">
                          <el-option
                            v-for="item in user_list"
                            :key="item.id"
                            :label="item.user_name"
                            :value="item.flm_id"
                          ></el-option>
                        </el-select>-->

                        <el-input placeholder="请选择钥匙持有人" v-model="rulesInfo.ysr.name" style="width: 300px"
                          @focus="showMemberList('ysr')">
                          <i @click="delName($event, 'ysr')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>
                    <el-form-item label="实勘人管理">
                      <div class="form_item_row flex-row">
                        <!--
                          <el-select v-model="setting_info.skr" clearable style="width:300px">
                          <el-option
                            v-for="item in user_list"
                            :key="item.flm_id"
                            :label="item.user_name"
                            :value="item.flm_id"
                          ></el-option>
                        </el-select>-->
                        <el-input placeholder="请选择实勘人" v-model="rulesInfo.skr.name" style="width: 300px"
                          @focus="showMemberList('skr')">
                          <i @click="delName($event, 'skr')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>
                    <el-form-item label="成交人管理">
                      <div class="form_item_row flex-row">
                        <!--<el-select v-model="setting_info.cjr" clearable style="width:300px">
                          <el-option
                            v-for="item in user_list"
                            :key="item.id"
                            :label="item.user_name"
                            :value="item.flm_id"
                          ></el-option>
                        </el-select>-->

                        <el-input placeholder="请选择成交人" v-model="rulesInfo.cjr.name" style="width: 300px"
                          @focus="showMemberList('cjr')">
                          <i @click="delName($event, 'cjr')" slot="suffix"
                            class="el-input__icon el-icon-circle-close"></i></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="setting_title">房源状态管理</div>
                <div class="setting_content">
                  <el-form class="el_form" label-width="150px">
                    <el-form-item label="聚焦房源">
                      <el-select v-model="is_top_data.is_top">
                        <el-option v-for="item in top_list" :key="item.value" :value="item.value" :label="item.des">
                        </el-option>
                      </el-select>
                      <el-button style="margin-left: 12px" type="primary" @click="changeIsTop">修改</el-button>
                    </el-form-item>
                    <el-form-item label="房源状态设置">
                      <div class="form_item_row flex-row">
                        <el-select v-model="house_status.trade_status" clearable style="width: 300px">
                          <el-option v-for="item in house_status_list" :key="item.values" :label="item.name"
                            :value="item.values"></el-option>
                        </el-select>

                        <div class="ml15">
                          <el-button type="primary" @click="changeRole('trade_status')">修改</el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>

                <div class="setting_title">
                  <div class="setting_row flex-row align-center">
                    <div class="setting" style="width: 370px; margin-right: 20px">
                      业主电话
                    </div>
                    <div class="edit_tel">
                      <el-button type="primary" @click="editTel">提交修改</el-button>
                    </div>
                  </div>
                </div>
                <div class="setting_content">
                  <el-form v-if="ownerList && ownerList.length" label-width="150px">
                    <div v-for="(item, index) in ownerList" :key="index">
                      <el-form-item label="业主姓名">
                        <el-input style="width: 180px; margin-right: 8px" type="text" v-model="item.owner"
                          placeholder="请输入">
                        </el-input>
                        <el-select placeholder="请选择" style="width: 100px; margin-right: 8px" v-model="item.sex">
                          <!--如果是select或者checkbox 、Radio就还需要选项信息-->
                          <el-option label="先生" :value="1"></el-option>
                          <el-option label="女士" :value="2"></el-option>
                        </el-select>
                        <template v-if="index == 0 && ownerList.length < 3">
                          <el-button type="primary" style="margin-left: 10px" @click="addOwner">添加</el-button>
                        </template>
                        <template v-if="index !== 0">
                          <el-button type="warning" style="margin-left: 10px" @click="delOwner(index)">删除</el-button>
                        </template>
                      </el-form-item>
                      <el-form-item label="业主手机号">
                        <el-input style="width: 180px; margin-right: 8px" v-model="item.owner_tel" placeholder="请输入"
                          maxlength="11"></el-input>
                        <el-select v-model="item.type" style="width: 100px" placeholder="请选择">
                          <!--如果是select或者checkbox 、Radio就还需要选项信息-->
                          <el-option v-for="(it, index) in typeList" :key="index" :label="it.name"
                            :value="it.values"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </el-form>
                </div>
              </div>
              <houseDetailSetting :id="id"></houseDetailSetting>
            </el-col>
          </el-row>
        </template>
      </div>
    </div>
    <el-dialog :title="imgTitle" width="800px" :append-to-body="false">
      <!-- :visible.sync="showPreview" -->
      <div class="preview_img" @contextmenu.prevent="openMenu($event)">
        <div class="save" @click.prevent.stop="saveImg">保存</div>
        <!-- <div class="img"> -->
        <img id="preImg" :src="currentImg.url" alt="" />
        <!-- </div> -->
        <div class="pre" @click.prevent.stop="showPre">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="next" @click.prevent.stop="showNext">
          <i class="el-icon-arrow-right"></i>
        </div>

        <!-- 右键菜单部分 -->
        <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
          <!-- <li @click="handleDelete">删除</li> -->
          <li @click="saveImg">保存图片</li>
          <!-- <li @click="handlePreviewFile">预览</li> -->
        </ul>
      </div>
    </el-dialog>
    <div class="mask1" v-if="showPreview">
      <div class="close_pre flex-row align-center j-center" @click="showPreview = false">
        <span>×</span>
      </div>
      <div class="save" @click.prevent.stop="saveImg">保存</div>
      <div class="preview_img" @contextmenu.prevent.stop="openMenu($event)">
        <!-- <div class="img"> -->
        <img id="preImg" :src="currentImg.url" alt="" />
        <!-- </div> -->
        <div class="pre" @click.prevent.stop="showPre">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="next" @click.prevent.stop="showNext">
          <i class="el-icon-arrow-right"></i>
        </div>

        <!-- 右键菜单部分 -->
        <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
          <!-- <li @click="handleDelete">删除</li> -->
          <li @click="saveImg">保存图片</li>
          <!-- <li @click="handlePreviewFile">预览</li> -->
        </ul>
      </div>
    </div>

    <el-dialog width="660px" :visible.sync="is_remind_customer" title="添加提醒">
      <!-- <div class="div row">
        <el-radio v-model="remind_form.type" :label="1">公众号提醒</el-radio>
        <el-radio v-model="remind_form.type" :label="2">短信提醒</el-radio>
      </div> -->
      <div class="div row" style="margin-top: 24px">
        <el-input :rows="6" type="textarea" show-word-limit v-model="remind_content" placeholder="请在这里输入"
          maxlength="40"></el-input>
      </div>
      <div class="div row" style="margin-top">
        <!-- <el-tabs @tab-click="onChangeDate">
          <el-tab-pane v-for="(date, index) in date_range" :key="index">
            <div slot="label" class="date_item">
              <div class="date_name">
                {{ index == 0 ? "今天" : date.weekDate }}
              </div>
              <div class="date_value">{{ date.shortDate }}</div>
            </div>
          </el-tab-pane>
        </el-tabs> -->
        <section class="theme-list">
          <div class="fixed-nav" ref="fixednav">
            <div class="fixed-nav-content flex-row align-center flex-1">
              <div v-for="(date, index) in date_range" :key="index"
                :class="['tab-title', current_index === index && 'select-tab']" @click="changeTab(index, date)">
                <div>
                  <div class="date_name">
                    {{ index == 0 ? "今天" : date.weekDate }}
                  </div>
                  <div class="date_value">{{ date.shortDate }}</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <div class="div row">
        <div class="t_time div row">
          <div class="t_time_item" :class="{ active: currentTimeIndex == index }" v-for="(item, index) in timeArr"
            :key="index" @click="changeCurrentTime(index)">
            {{ item }}
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_remind_customer = false">取 消</el-button>
        <el-button type="primary" @click="onClickRemind">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="is_invalid_dialog" title="标注无效">
      <div class="label-invalid">无效原因：</div>
      <el-input type="textarea" placeholder="请输入" :rows="8" v-model="invalid_content"></el-input>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_invalid_dialog = false">取 消</el-button>
        <el-button type="primary" @click="onCreateDataInvalid">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_labels_customer" title="房源标签" width="660px">
      <div class="lab_list">
        <div v-for="(item, index) in labels_list" :key="item.id">
          <div class="labelname">{{ item.name }}</div>
          <div class="lables-box div row">
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.sub" :key="i2"
              @click="checkChangeLabels(index, i2, i1)">
              {{ i1.name }}
            </div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="is_labels_customer = false">取 消</el-button>
        <el-button type="primary" @click="onClickLabels">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_img_dia" title="手机传图" width="703px" :close-on-click-modal="false"
      :before-close="cancelSubmitImg">
      <div class="img_list flex-row f-wrap">
        <template v-if="img_list.length">
          <div v-for="item in img_list" :key="item.id" class="img_item">
            <img :src="item.path" alt="" />
            <div class="del"></div>
          </div>
        </template>
        <!-- <template v-else>
          <div class="noImg">等待上传中</div>
        </template> -->
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSubmitImg">取 消</el-button>
        <el-button type="primary" @click="submitImg">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_share" title="复制文案" width="500px" :close-on-click-modal="false">
      <div class="share_info">
        <p style="margin-bottom: 10px">
          {{
            `🎉 ${detail.title}${detail.shi}室${detail.ting}厅${detail.wei}卫 / 公盘编号：${detail.id}`
          }}
        </p>
        <p style="margin-bottom: 10px">
          {{ `💎 小区：${detail.title}` }}
        </p>
        <p style="margin-bottom: 10px">
          {{ `🛏 户型：${detail.shi}室${detail.ting}厅${detail.wei}卫` }}
        </p>
        <p style="margin-bottom: 10px">
          {{ `👍 楼层：${detail.sz_floor}/${detail.total_floor}层` }}
        </p>
        <p style="margin-bottom: 10px">
          {{ ` 🛏 装修：${detail.zhuangxiu} ` }}
        </p>
        <p style="margin-bottom: 10px">
          {{ `🏡面积：${detail.mianji}㎡` }}
        </p>
        <p style="margin-bottom: 10px">
          {{
            `🎁 价格：${detail.trade_type == 1 || detail.trade_type == 3
              ? detail.sale_price / 10000 + "万"
              : detail.rent_price + "元/月"
              }`
          }}
        </p>
        <template v-if="detail.unilateral_agent == 0">
          <p style="margin-bottom: 10px">
            {{
              `👩 联系人：${detail.current_login_user
                ? detail.current_login_user.user_name
                : ""
              }`
            }}
          </p>
        </template>
        <p style="margin-bottom: 10px">
          {{
            `☎️ 联系电话：${detail.current_login_user ? detail.current_login_user.phone : ""
              }`
          }}
        </p>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="copyShareText">复制文案</el-button>
      </span>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="show_group_house" title="房源群发">
      <div class="house_send">
        <div class="tips">请选择企业</div>
        <div class="site_list">
          <div class="site_item flex-box" v-for="site in siteList" :key="site.id" @click="toBinding(site)">
            <div class="checked">
              <img v-if="site.is_checked" src="@/assets/checked.png" />
              <img v-else src="@/assets/unchecked.png" />
            </div>
            <div class="top flex-row items-center j-between">
              <div class="name flex-row items-center">
                <span>{{ site.name }}</span>
              </div>
              <div class="service">
                <span>{{ site.services }} </span>
                <span class="open_status" :class="{ is_open: site.is_open }">{{
                  site.is_open ? "已开通" : "未开通"
                }}</span>
              </div>
            </div>
            <div class="bottom flex-row items-center j-between">
              <div class="img">
                <!-- <img :src="site.logo" mode="aspectFill" /> -->
              </div>
              <div class="status" :class="{ status1: site.is_binding == 0 }">
                {{ site.is_binding == 1 ? "账号已绑定" : "未绑定账号" }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="show_group_house = false">取 消</el-button>
        <el-button type="primary" @click="houseGroupSend">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="bind_site" title="绑定账号">
      <el-input placeholder="请输入" v-model="bind_phone" v-if="bind_site_phone"></el-input>
      <el-input v-if="!bind_site_phone" placeholder="请输入" :disabled="true" v-model="bind_phone"></el-input>
      <div class="flex-row" style="margin-top: 10px" v-if="!bind_site_phone">
        <el-input placeholder="请输入验证码" v-model="form.captcha"></el-input>
        <el-button class="highlight" :class="{ disabled: time_down }" @click="getVerifyImg">
          {{ time_down ? `${second}秒后重新获取` : "获取验证码" }}
        </el-button>
      </div>

      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="cancelBindTel">取 消</el-button>
        <el-button type="primary" @click="bindTel">确 定</el-button>
      </span>
    </el-dialog>
    <!--设置权限弹框 -->
    <el-dialog width="500px" :visible.sync="setting_confirm" :title="settingTitle">
      <div>
        <el-form label-width="80px">
          <el-form-item label="成交类型" v-if="setting_form.type == 'cjr'">
            <el-select style="width: 300px" v-model="setting_form.cj_type">
              <el-option value="1" label="我司成交"> </el-option>
              <el-option value="2" label="他司成交"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="设置原因">
            <el-input type="textarea" v-model="setting_form.content" rows="5"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="cancelSettingCom">取 消</el-button>
        <el-button type="primary" @click="confirmRoles">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="show_audit" title="提交审批">
      <div class="audit_form" v-if="show_audit">
        <el-form label-width="80px">
          <el-form-item label="房源名称">
            <el-input class="houseNameStyle" v-model="detail.title" :readonly="true"></el-input>
          </el-form-item>
          <el-form-item label="审批类型">
            <el-select style="width: 260px" v-model="audit_form.cat_id" @change="changeAudit">
              <el-option v-for="item in auditList" :key="item.values" :value="item.values" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
          <template v-for="(item, index) in keyAuditList">
            <el-form-item :label="item.title" :key="index" v-if="item.approve_type_id != 14">
              <template v-if="item.show_type == '1' && item.type != 'date'">
                <el-input style="width: 360px" v-model="audit_form[item.name]">
                  <template v-if="item.units" slot="append">{{
                    item.units
                  }}</template>
                </el-input>
              </template>
              <template v-if="item.show_type == '1' && item.type == 'date'">
                <el-date-picker v-model="audit_form[item.name]" type="date" style="width: 260px"
                  :valueFormat="item.date_format" :format="item.date_format" placeholder="选择日期">
                </el-date-picker>
              </template>

              <template v-if="item.show_type == '4'">
                <el-input style="width: 360px" type="textarea" v-model="audit_form[item.name]"></el-input>
              </template>
              <template v-if="item.show_type == '2'">
                <el-select style="width: 260px" v-model="audit_form[item.name]" @change="handlerChangeSelect">
                  <el-option v-for="op in item.options" :key="op.values" :label="op.name" :value="op.values">
                  </el-option>
                </el-select>
              </template>
              <template v-if="item.show_type == '3'">
                <el-select style="width: 260px" :multiple="true" clearable v-model="audit_form[item.name]"
                  @change="handlerChangeSelect">
                  <el-option v-for="op in item.options" :key="op.values" :label="op.name" :value="op.values">
                  </el-option>
                </el-select>
              </template>
            </el-form-item>
            <template v-else>
              <el-form-item :label="item.title" :key="index + '_2'" v-if="item.name == 'f_price'">
                <div class="flex-row items-center">
                  <span>
                    <el-input style="width: 170px" v-model="audit_form[item.name]">
                      <template v-if="item.units" slot="append">{{
                        item.units
                      }}</template>
                    </el-input></span>
                  <span style="margin-left: 10px">
                    <el-form-item label="佣金" label-width="40px">
                      <el-input class="commission-Width" style="width: 140px" v-model="audit_form.f_commission">
                        <template v-if="item.units" slot="append">{{
                          "元"
                        }}</template>
                      </el-input>
                    </el-form-item>
                  </span>
                </div>
              </el-form-item>
              <el-form-item :label="item.title" :key="index + '_2'" v-if="item.name == 'f_name'">
                <div class="flex-row items-center">
                  <el-input style="width: 130px" v-model="audit_form[item.name]">
                    <template v-if="item.units" slot="append">{{
                      item.units
                    }}</template>
                  </el-input>

                  <el-form-item style="margin-left: 10px" label="电话" label-width="40px">
                    <el-input class="Phone-width" style="width: 180px" v-model="audit_form.f_tel">
                    </el-input>
                  </el-form-item>
                </div>
              </el-form-item>
              <el-form-item :label="item.title" :key="index + '_3'" v-if="item.name != 'f_tel' &&
                item.name !== 'f_commission' &&
                item.name !== 'f_price' &&
                item.name !== 'f_name'
                ">
                <template v-if="item.show_type == '1' && item.type != 'date'">
                  <el-input style="width: 360px" v-model="audit_form[item.name]">
                    <template v-if="item.units" slot="append">{{
                      item.units
                    }}</template>
                  </el-input>
                </template>
                <template v-if="item.show_type == '1' && item.type == 'date'">
                  <el-date-picker v-model="audit_form[item.name]" type="date" style="width: 360px"
                    :valueFormat="item.date_format" :format="item.date_format" placeholder="选择日期">
                  </el-date-picker>
                </template>

                <template v-if="item.show_type == '4'">
                  <el-input style="width: 360px" type="textarea" v-model="audit_form[item.name]"></el-input>
                </template>
                <template v-if="item.show_type == '2'">
                  <el-select style="width: 260px" v-model="audit_form[item.name]" @change="handlerChangeSelect">
                    <el-option v-for="op in item.options" :key="op.values" :label="op.name" :value="op.values">
                    </el-option>
                  </el-select>
                </template>
                <template v-if="item.show_type == '3'">
                  <el-select style="width: 360px" :multiple="true" clearable v-model="audit_form[item.name]"
                    @change="handlerChangeSelect">
                    <el-option v-for="op in item.options" :key="op.values" :label="op.name" :value="op.values">
                    </el-option>
                  </el-select>
                </template>
              </el-form-item>
            </template>
          </template>
          <el-form-item label="备注">
            <el-input style="width: 360px" type="textarea" v-model="audit_form.remarks"></el-input>
          </el-form-item>
          <el-form-item label="审批人">
            <div class="audit_person flex-row items-center">
              <template v-if="auditPersonList.length">
                <div class="audit_person_name" v-for="item in auditPersonList" :key="item.values">
                  {{ item.user_name }}
                </div>
              </template>

              <template v-else>
                <template v-if="auditPersonSelect.length">
                  <div class="audit_person_name" v-for="item in auditPersonSelect" :key="item.values">
                    {{ item.name }}
                  </div>
                </template>
                <div class="audit_person_name audit_person_add" @click="showMemberList('spr')">
                  添加审批人
                </div>
              </template>
            </div>
          </el-form-item>
          <el-form-item label="凭证">
            <div class="attachment">
              <el-upload ref="certificates" multiple action="/api/common/file/upload/admin?category=6"
                :headers="upload_headers" :limit="10" :file-list="alreadyUploadList" :files="attenchmentList"
                accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif" list-type="picture-card" :on-success="(response, file, fileList) => {
                  onUploadAttechmentSuccess({
                    response,
                    file,
                    fileList,
                  });
                }
                  ">
                <i class="el-icon-picture-outline"></i>
              </el-upload>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="show_audit = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="show_member_list" width="660px" title="选择人员">
      <multipleTree ref="memberLists" v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>

    <el-dialog :visible.sync="show_audit_member" width="660px" title="选择审批人员">
      <div class="member" v-if="show_audit_member">
        <div class="member_box flex-row">
          <div class="left member_con flex-1">
            <multipleTree ref="auditMemberList" :list="memberList" :defaultValue="selectedAuditIds"
              @onClickItem="selecetedAuditMember" :defaultExpandAll="true">
            </multipleTree>
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择审批人</div>
            <div class="selected_list">
              <div class="selected_item flex-row align-center" v-for="item in auditPersonSelect" :key="item.id">
                <div class="name flex-1">{{ item.name }}</div>
                <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="show_audit_member = false">取消</el-button>
          <el-button type="primary" @click="selectAuditOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog width="400px" :visible.sync="show_tel_dialog" title="查看电话" :show-close="false"
      :close-on-press-escape="false" :close-on-click-modal="false" @close="ShowPhoneClose">
      <div class="name-box div row name-box-title">
        <span class="title mr30"> 业主信息 </span>
      </div>
      <div class="houseInformation-box">
        <span>{{ this.detail.title }}-</span>
        <span>{{
          this.detail.loudong +
          "-" +
          this.detail.danyuan +
          "-" +
          this.fanghao_view
        }}</span>
      </div>
      <div class="name-box div row align-center OwnerInfo" v-for="item in showTel.tel" :key="item.id">
        <span class="flex-1 mr30">{{ item.owner }}
          (
          <template v-if="item.sex_name">{{ item.sex == 1 ? "先生" : "女士" }}-
          </template>
          {{ item.type_name }})</span>

        <span class="mr30">{{ item.owner_tel }}</span>
        <div>
          <el-button type="primary" size="mini" v-if="showCall == 1" @click="showCallPhone(item)">
            外呼
          </el-button>
        </div>
      </div>

      <div class="msg" style="margin: 20px 0">
        <el-input :rows="3" v-model="follow_params1.content" type="textarea" placeholder="请输入跟进内容(企业内公开)"></el-input>
      </div>
      <div class="footer flex-row align-center j-center">
        <el-button type="primary" @click="onChangeStatus1">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog width="500px" :visible.sync="show_vr_setting" title="添加相机拍摄">
      <div>
        <el-form label-width="80px">
          <el-form-item label="选择相机">
            <el-select style="width: 300px" v-model="vr_setting_form.photographer_id">
              <el-option v-for="item in vr_photo_list" :key="item.id" :value="item.id" :label="item.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="截止日期" v-if="isShoot == false">
            <el-date-picker v-model="vr_setting_form.endtime" style="width: 300px" type="date" placeholder="选择日期时间"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="描述">
            <el-input style="width: 300px" v-model="vr_setting_form.memo">
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="show_vr_setting = false">取 消</el-button>
        <el-button type="primary" @click="confirmVrSetting">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="isVrAdd ? '增加vr链接' : '修改vr链接'" width="500px" :visible.sync="addUrl" :before-close="cancelAddUrl">
      <el-form label-width="128px" label-position="left">
        <el-form-item label="url链接地址">
          <el-input placeholder="请输入url链接地址" style="width: 300px" v-model="vr_url_form.vr_url"></el-input>
        </el-form-item>
        <el-form-item label="链接类型">
          <el-select v-model="vr_url_form.vr_type" placeholder="请选择链接类型" style="width: 300px">
            <el-option v-for="item in vrTypeOptions" :key="item.values" :label="item.name" :value="item.values">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="VR描述">
          <el-input style="width: 300px" type="textarea" placeholder="请输入VR描述" v-model="vr_url_form.memo" maxlength="200">
          </el-input>
        </el-form-item>
        <el-form-item label="是否为主链接">
          <el-radio v-model="vr_url_form.is_primary" :label="1">是</el-radio>
          <el-radio v-model="vr_url_form.is_primary" :label="0">否</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelAddUrl">取 消</el-button>
        <el-button type="primary" @click="onAddVrUrl">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="vr链接浏览" :visible.sync="isVrLook" width="50%">
      <iframe height="600px" width="100%" :src="current_vr"></iframe>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="isVrLook = false">取 消</el-button>
        <el-button type="primary" @click="isVrLook = false">确 定</el-button>
      </span> -->
    </el-dialog>
    <el-dialog title="视频浏览" :visible.sync="isVideoLook" width="50%">
      <video style="width: 100%; height: 600px" controls :src="isVideoLookUrl"></video>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="isVideoLook = false">取消</el-button>
        <el-button type="parmary" @click="isVideoLook = false">确定</el-button>
      </span> -->
    </el-dialog>
    <el-dialog :title="isVideoAdd ? '添加视频' : '编辑视频'" :visible.sync="isAddVideoShow" width="50%"
      :before-close="cancelVideo">
      <el-form label-width="128px" label-position="left">
        <el-form-item label="视频描述">
          <el-input v-model="add_video_form.memo" type="textarea" width="300px" maxlength="200"
            placeholder="请输入视频描述"></el-input>
        </el-form-item>
        <el-form-item label="是否为主视频">
          <el-radio v-model="add_video_form.is_primary" :label="1">是</el-radio>
          <el-radio v-model="add_video_form.is_primary" :label="0">否</el-radio>
        </el-form-item>
        <el-form-item label="上传视频">
          <el-upload class="upload-demo" action="/api/common/file/upload/admin?category=103" :headers="upload_headers"
            :on-preview="handlePreviewVideo" :on-success="handleVideoSuccess" :on-remove="handVideoRemove"
            :on-change="handleVideoChange" :before-upload="handVideoBefore" :file-list="videoFileList" accept=".mp4,.MP4"
            multiple :limit="1">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelVideo">取消</el-button>
        <el-button type="parmary" @click="addHouseVideo">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="762px" :visible.sync="showSendDouyin" title="发送到抖音">
      <div class="douyin_info flex-row">
        <div class="douyin_right">
          <div class="douyin_left flex-row">
            <div class="douyin_type" @click="douyin_type = 'image'" :class="{ active: douyin_type == 'image' }">
              图片
            </div>
            <div class="douyin_type" @click="douyin_type = 'video'" :class="{ active: douyin_type == 'video' }">
              视频
            </div>
          </div>

          <div class="sucai_list" v-if="douyin_type == 'image'">
            <div class="img_list flex-row">
              <div class="douyin_img" v-for="(item, index) in douyinInfo.photos" :key="index"
                @click="selectDouyinImg(item)">
                <div class="img_check flex-row align-center" :class="{ checked: item.checked }">
                  <img src="@/assets/icon/gouxuan.png" alt="" />
                </div>
                <img :src="item.url" alt="" />
              </div>
            </div>
          </div>
          <div class="sucai_list" v-if="douyin_type == 'video'">
            <div class="img_list flex-row">
              <div class="douyin_img" v-for="(item, index) in douyinInfo.videos" :key="index"
                @click="selectDouyinImg(item)">
                <div class="img_check flex-row align-center" :class="{ checked: item.checked }">
                  <img src="@/assets/icon/gouxuan.png" alt="" />
                </div>
                <video :src="item.url" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="showSendDouyin = false">取 消</el-button>
        <el-button type="primary" @click="sendToDouyin">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog class="dialog_border" :visible.sync="show_qrcode" width="550px" title="发布到抖音">
      <div class="flex-row align-center j-between" style="padding: 10px 30px">
        <div class="qrcode_left">
          <img src="https://img.tfcs.cn/backup/static/admin/house/douyin_left.jpg" alt="" />
        </div>
        <div class="douyin_line"></div>
        <div class="flex-row align-center j-center pic-uploader">
          <div id="qrcode1" class="u_qrcode" ref="qrCode"></div>
          <span class="uploader-actions" @click="previewQrcode">
            <span class="uploader-actions-item"><i class="el-icon-zoom-in"></i></span>
          </span>
        </div>
      </div>
      <div class="flex-row align-center j-center douyin_bottom">
        <div>打开</div>
        <div class="douyin_logo">
          <img src="https://img.tfcs.cn/backup/static/admin/house/douyin_logo.png" alt="" />
        </div>
        <div class="saoyisao">扫一扫 二维码</div>

        <!-- <div class="flex-row align-center j-center"></div> -->
      </div>

      <div></div>
    </el-dialog>
    <el-dialog title="设置聚焦房源" width="500px" :visible.sync="is_top_show" :before-close="is_top_cancel">
      <el-form>
        <el-form-item label="设置原因" label-width="80px">
          <el-input v-model="is_top_data.content" type="textarea" placeholder="请输入设置原因" rows="5">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_top_cancel">取消</el-button>
        <el-button type="primary" @click="onChangeIsTop">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="420px" title="智能手机" custom-class="dialog" :visible.sync="showPhone" style="margin-top: 15vh;">
      <myCallPhone v-if="showPhone" :autoPhonenNumber="autoPhonenNumber" :CallPhoneOwner="CallPhoneOwner"
        @phoneClose="closeCallPhone" @getCallId="getCallId"></myCallPhone>
    </el-dialog>
    <el-dialog class="appointment_dialog" title="预约带看" :visible.sync="appointmentVisible" width="500px"
      :close-on-click-modal="false" @close="appointmentDown">
      <div class="appointment-box">
        <!-- 客户姓名 -->
        <div class="appointment-customer">
          <span class="appointment-title">客户姓名</span>
          <el-input v-model="appointment.name" placeholder="请输入客户姓名"></el-input>
          <span class="appointment-select" @click="selectDialog">选择</span>
        </div>
        <!-- 手机号 -->
        <div class="appointment-phone">
          <span class="appointment-title">手机号</span>
          <el-input v-model="appointment.mobile" placeholder="请输入手机号" maxlength="11">
          </el-input>
        </div>
        <!-- 陪看人员 -->
        <div class="appointment-accompany">
          <span class="appointment-title">陪看人员</span>
          <el-select v-model="personnel_list" multiple placeholder="请选择陪看人员">
            <el-option v-for="item in departmentMember" :key="item.values" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </div>
        <!-- 跟进内容 -->
        <div class="appointment-content">
          <span class="appointment-title">跟进内容</span>
          <el-input type="textarea" autosize placeholder="请输入跟进内容( 企业内公开)" v-model="appointment.content">
          </el-input>
        </div>
        <!-- 带看日期 -->
        <div class="appointment-date">
          <span class="appointment-title">带看日期</span>
          <el-date-picker v-model="appointment.take_date" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <!-- 带看时间 -->
        <div class="appointment-time">
          <span class="appointment-title">带看时间</span>
          <div class="time-box">
            <div class="time-boxList" :class="{ 'time-active': item.id == appointment.take_time }"
              v-for="(item, index) in timeValue" :key="index" @click="appointmentTime(item)">
              <span>{{ item.value }}</span>
            </div>
          </div>
        </div>
        <!-- 带看单号 -->
        <div class="appointment-formNumber">
          <span class="appointment-title">带看单号</span>
          <el-input v-model="appointment.take_no" placeholder="请输入带看单号"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="appointmentVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmitAppoint">提 交</el-button>
      </span>
      <el-dialog title="选择客户" :visible.sync="selectVisible" width="950px" :modal="false">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <span class="text">客户来源：</span>
            <myLabel labelKey="title" :arr="source_list" @onClick="onClickType($event, 1)"></myLabel>
          </div>
          <div class="bottom-border div row" style="padding-top: 24px">
            <span class="text">筛选时间：</span>
            <el-date-picker style="width: 250px" size="small" v-model="TimeScreening" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="onChangeTime">
            </el-date-picker>
          </div>
          <div class="bottom-border div row" style="padding-top: 24px">
            <span class="text">搜索客户：</span>
            <el-input width="200" v-model="chooseCustomer.keywords" placeholder="请输入客户姓名" @blur="seachCustomer">
              <el-button slot="append" icon="el-icon-search"></el-button>
            </el-input>
          </div>
          <myTable :table-list="customerList" :header="table_header" :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row :row-style="$TableRowStyle"></myTable>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
            :total="chooseCustomer.total" :page-size="chooseCustomer.per_page" :current-page="chooseCustomer.page"
            @current-change="customerPageChange">
          </el-pagination>
        </div>
      </el-dialog>
    </el-dialog>
    <el-dialog :visible.sync="dialogImageVisible" title="预览" :width="dialogImageWith ? dialogImageWith + 'px' : ''">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
    <!-- 房屋实勘 -->
    <el-dialog class="survey_dialog" title="房屋实勘跟进" :visible.sync="surveyVisible" width="660px"
      :close-on-click-modal="false">
      <div class="survey">
        <el-form ref="surveyForm" :model="survey_form" label-width="80px">
          <el-form-item label="房源特色">
            <div class="feature">
              <div class="feature_box" v-for="(item, index) in detail.label" :key="index">
                <span>
                  {{ item }}
                </span>
              </div>
            </div>
            <span style="color: #c0c4cc; cursor: pointer" @click="is_labels_customer = true">
              选择
            </span>
          </el-form-item>
          <el-form-item label="跟进内容" prop="content">
            <template slot="label">
              <span style="color: #f56c6c; margin-right: 4px">*</span>跟进内容
            </template>
            <el-input style="width: 460px" v-model="survey_form.content" type="textarea" autosize
              placeholder="请输入跟进内容( 企业内公开)">
            </el-input>
          </el-form-item>
          <el-form-item label="核心卖点" prop="selling_point">
            <el-input style="width: 460px" v-model="survey_form.selling_point" type="textarea" autosize
              placeholder="请输入核心卖点">
            </el-input>
          </el-form-item>
          <el-form-item label="小区介绍" prop="community_introduction">
            <el-input style="width: 460px" v-model="survey_form.community_introduction" type="textarea" autosize
              placeholder="请输入小区介绍">
            </el-input>
          </el-form-item>
          <el-form-item label="户型介绍" prop="structure_introduction">
            <el-input style="width: 460px" v-model="survey_form.structure_introduction" type="textarea" autosize
              placeholder="请输入户型介绍">
            </el-input>
          </el-form-item>
          <el-form-item label="交通出行" prop="transportation">
            <el-input style="width: 460px" v-model="survey_form.transportation" type="textarea" autosize
              placeholder="请输入交通出行">
            </el-input>
          </el-form-item>
          <el-form-item label="上传图片">
            <el-upload ref="surveyUpload" multiple action="/api/common/file/upload/admin?category=6"
              :headers="upload_headers" :limit="5" accept=".jpg,.jpeg,.png,.JPG,.JPEG" list-type="picture-card"
              :on-success="(response, file, fileList) => {
                onUploadSurveySuccess({
                  response,
                  file,
                  fileList,
                });
              }
                ">
              <i class="el-icon-picture-outline"></i>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer survey_footer">
        <el-button @click="surveyVisible = false">取 消</el-button>
        <el-button type="primary" @click="addSurveyList" :loading="is_loading">提 交</el-button>
      </span>
    </el-dialog>
    <div class="mask1" v-if="show_dialog_pictures" @click="show_dialog_pictures = false">
      <div class="preview_img" @click.prevent.stop="() => { }">
        <img id="preImg" :src="dialog_pictures_src" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
// import myForm from "./components/customer_form";
import QRCode from "qrcodejs2";
// import QQMap from './utils/qqmap'
import myEmpty from "@/components/components/my_empty.vue";
import myLabel from "../crm/components/my_label";
import mySelect from "../crm/components/my_select";
import portrait from "./components/house_info";
import { Loading } from "element-ui";
import config from "@/utils/config.js";
import myCallPhone from "@/components/navMain/crm/components/myCallPhone";
import houseMaintain from "./components/houseMaintain.vue";
import Tribute from "tributejs";
import myTable from "@/components/components/my_table";
import AudioPlayer from "@/components/components/audioPlayer.vue";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import tMemberDropdown from "@/components/tplus/tDropdown/tMemberDropdown.vue";
import houseDetailSetting from './components/house_detail_setting.vue'
export default {
  name: "house_detail",
  components: {
    myEmpty,
    portrait,
    myLabel,
    mySelect,
    myCallPhone,
    houseMaintain,
    myTable,
    AudioPlayer,
    multipleTree, tMemberDropdown, houseDetailSetting
  },
  data() {
    return {
      //聚焦房源弹框开关
      is_top_show: false,
      //聚焦房源开关
      is_top_data: {
        is_top: "",
        content: "",
      },
      //添加vr链接弹窗开关
      addUrl: false,
      //增加vr链接表单输数据
      vr_url_form: {
        vr_url: "", //url链接地址
        vr_type: "", //url链接类型
        memo: "",//VR描述
        is_primary: 0
      },
      //视频文件
      videoFileList: [],
      //vr浏览弹窗控制
      isVrLook: false,
      detail: {
        photos: [],
        whr: {},
        house_info: {}
      },
      //上传视频参数
      add_video_form: {
        video_url: "",
        memo: "",
        is_primary: 0
      },
      //上传视频弹窗开关
      isAddVideoShow: false,
      follow_list_params: {
        page: 1,
        rows: 10,
      },
      TakeLook_list_params: {
        page: 1,
        rows: 10,
      },
      Phone_list_params: {
        page: 1,
        rows: 10,
      },
      //判断添加编辑视频
      isVideoAdd: true,
      //视频浏览开关
      isVideoLook: false,
      // 相册  钥匙委托   周边配套  附加信息
      tabList: [
        {
          id: 1,
          name: "房源信息",
        },
        {
          id: 2,
          name: "房源相册",
        },
        {
          id: 3,
          name: "钥匙委托",
        },
        {
          id: 4,
          name: "周边配套",
        },
        {
          id: 5,
          name: "房源日志",
        },
      ],
      tabListDefault: [
        {
          id: 1,
          name: "房源信息",
        },
        {
          id: 2,
          name: "房源相册",
        },
        {
          id: 3,
          name: "钥匙委托",
        },
        {
          id: 4,
          name: "周边配套",
        },
        {
          id: 5,
          name: "房源日志",
        },
      ],
      follow_params: {
        info_id: "",
        content: "",
        type: 1,
        effect_type: 1,
        images: "", // 图片列表多个，隔开
      },
      follow_params1: {
        info_id: "",
        content: "",
        type: 1,
        effect_type: 1,
      },
      current_pic_index: 1,
      currentTab: 1,
      status_list: [],
      follow_list: [],
      PhoneRecord_list: [],
      TakeLookRecord_list: [],
      visiblepop: false,
      visiblepop_title: "了解信息",
      is_view_tel_desc: "",
      cus_list: [
        { id: 1, name: "转交客户" },
        { id: 2, name: "合并客户" },
        { id: 3, name: "放弃客户" },
      ],
      status_audit_list: [],
      is_view_tel: false,
      follow_status_list: [],
      level_list: [
        {
          id: "A",
          title: "A",
        },
        {
          id: "B",
          title: "B",
        },
        {
          id: "C",
          title: "C",
        },
      ],
      is_tabs: "portrait",
      behavior_list: "",
      showPreview: false,
      previewImg: "",
      currentType: "",
      currentTypeId: "",
      show_select_type: false,
      show_cover: false,
      show_delete: false,
      show_fanghao: false,
      currentImg: {},
      keyLoading: false,
      weituoLoading: false,
      djweituoLoading: false,
      key_num: "",
      key_pass: "",
      up_loading4: false,
      up_loading3: false,
      up_loading2: false,
      up_loading1: false,
      id_card: [],
      dj_weituo_img: [],
      weituo_img: [],
      budongchan_img: [],
      vip_price: "",
      vip_date: [],
      map: null,
      typepop: false,
      upload_headers: {
        Authorization: config.TOKEN,
      },
      search_type_index: 0,
      search_types: [
        {
          name: "教育",
          icon: require("@/assets/icon/jiaoyu.png"),
          keys: ["小学", "中学", "幼儿园"],
          keywords: "学校",
        },
        {
          name: "餐饮",
          icon: require("@/assets/icon/canyin.png"),
          keys: ["餐饮"],
          keywords: "餐饮",
        },
        {
          name: "医疗",
          icon: require("@/assets/icon/yiliao.png"),
          keys: ["医疗"],
          keywords: "医院",
        },
        {
          name: "交通",
          icon: require("@/assets/icon/jiaotong.png"),
          keys: ["公交", "地铁"],
          keywords: "交通",
        },
        {
          name: "周边小区",
          icon: require("@/assets/icon/xiaoqu.png"),
          keys: ["周边小区"],
          keywords: "周边小区",
        },
      ],
      marker_list: [],
      show_search_res: true,
      is_remind_customer: false,
      currentTimeIndex: "",
      date_range: [],
      timeArr: [],
      imgTypeList: [
        {
          values: 1,
          name: "室内图",
        },
        {
          values: 2,
          name: "户型图",
        },
        {
          values: 3,
          name: "室外图",
        },
      ],
      tableData: [],
      WithShowTabData: [],
      is_table_loading: false,
      log_params: {
        pages: 1,
        rows: 10,
        opt_type: "",
        info_id: "",
      },
      up_loading5: false,
      up_loading6: false,
      opt_type_list: [],
      trade_type_list: [],
      total: 0,
      imgTitle: "图片预览",
      photos: [],
      is_invalid_dialog: false,
      invalid_content: "",
      files: [],
      is_labels_customer: false,
      labels_list: [],
      current_index: 0,
      remind_content: "",
      key_params: {
        page: 1,
        rows: 20,
        total: 0,
      },
      vipWeituo_params: {
        page: 1,
        rows: 100,
        total: 0,
      },
      keyData: [],
      mobile_up: false,
      show_img_dia: false,
      img_list: [],
      weituoData: [],
      vipData: [],
      fileLists: [],
      visible: false,
      top: 0,
      left: 0,
      show_share: false,
      share_pop: false,
      share_status_list: [
        {
          name: "复制文案",
          values: "copy",
          desc: '分享给好友或朋友圈',
          img: "https://img.tfcs.cn/backup/static/house/fangyuan_share/copy.png"
        },
        {
          name: "房源群发",
          values: "group_send",
          desc: '海量房源，一键群发',
          img: "https://img.tfcs.cn/backup/static/house/fangyuan_share/group_send.png"
        },
        {
          name: "发布到抖音",
          values: "douyin_send",
          desc: '房源短视频剪辑',
          img: "https://img.tfcs.cn/backup/static/house/fangyuan_share/douyin_send.png"
        }
      ],
      second: 0,
      show_group_house: false,
      siteList: [],
      bind_phone: "",
      form: {
        captcha: "",
      },
      time_down: false,
      bind_site: false,
      weituoDiabled: false,
      djWeituoDisable: false,
      user_list: [],
      setting_params: {
        page: 1,
        per_page: 100,
      },
      setting_info: {
        whr: "",
        agent: "",
        wtr: "",
        djwtr: "",
        cjr: "",
        ysr: "",
        skr: "",
      },
      setting_confirm: false,
      settingTitle: "设置权限",
      setting_form: {
        content: "",
      },
      rulesInfo: {
        agent: {},
        skr: {},
        wtr: {},
        whr: {},
        djwtr: {},
        ysr: {},
        cjr: {},
      },
      rulesForm: {
        agent: {
          id: "",
          name: "",
        },
      },
      house_status: {
        trade_status: "",
      },
      house_status_list: [],
      //聚焦房源列表
      top_list: [{ value: 1, des: "开启" }, { value: 0, des: "关闭" }],
      show_member_list: false,
      memberList: [],
      selectedIds: [],
      tel_data: [{}, {}, {}],
      ownerList: [],
      typeList: [],
      website_id: 1,
      show_audit: false, // 显示/ 隐藏提交审批模态框
      auditList: [],
      audit_form: {
        cat_id: 11,
      },
      attenchmentList: [],
      show_audit_member: false,
      auditPersonList: [],
      auditPersonSelect: [],
      selectedAuditIds: [],
      defaultProps: {
        children: "subs",
        label: "name",
      },
      keyAuditList: [],
      show_tel_dialog: false,
      vr_setting_form: {
        id: '',
        photographer_id: '',
        endtime: "",
        memo: "",
        from: 1
      },
      vr_photo_list: [],
      show_vr_setting: false,
      delType: '',// 撤销委托区分vip委托和普通委托
      deleteId: '',// 撤销委托的id
      //房源相册选项卡
      activeName: 'first',
      //房源图片视频vr数量
      housePhoVidVRnumber: {},
      //是否为相机拍摄
      isShoot: false,
      //VR链接类型
      vrTypeOptions: [],
      //房源视频列表
      videoList: [],
      //vr列表
      vrList: [],
      //vr表格loading
      isVrListLoading: true,
      //判断vr链接是否是增加
      isVrAdd: true,
      //编辑vrid
      editVrId: "",
      //视频列表loading
      isVideoLoading: true,
      //视频浏览链接
      isVideoLookUrl: "",
      //图片上传loading
      videoLoading: "",
      videoId: "",
      showSendDouyin: false,
      douyinInfo: {},
      douyin_type: "image",
      douyinFileLists: [],
      video_path: '',
      douyinVideoLists: [],
      up_loading10: false,
      up_loading9: false,
      show_qrcode: false,
      current_vr: '',
      curr_video: '',
      //绑定账号手机号控制
      bind_site_phone: false,
      new_trade_status: "",
      showTel: '', // 查看手机号-显示全号
      is_whr: 0, // 是否是维护人
      showPhone: false, // 控制手机拨打电话组件显示
      autoPhonenNumber: "", // 查看电话-外呼自动填入的手机号
      MaintainData: {}, // 存储房源维护数据
      houseScoreVisible: false, // 控制房源得分显示
      appointmentVisible: false, // 控制预约带看显示
      // 预约带看提交参数
      appointment: {
        info_id: "", // 房源id
        name: "", // 客户姓名
        mobile: "", // 客户电话
        content: "", // 跟进内容
        accompany: "", // 陪看人员
        take_date: "", // 带看日期
        take_time: 1, // 带看时间
        take_no: "", // 带看单号 可为空
      },
      departmentMember: [], // 获取同部门成员
      showDropdown: false, // 控制同部门成员列表显示
      controlVisible: true, // 控制同事列表接口请求
      fanghao_view: "", // 查看电话显示房号
      record_url: "", // 跟进记录录音url
      // 带看时间
      timeValue: [
        {
          value: '上午',
          id: 1
        },
        {
          value: '下午',
          id: 2
        },
        {
          value: '晚上',
          id: 3
        }
      ],
      personnelVisible: true, // 控制陪看人员列表显示隐藏
      personnel_id: [], // 存储陪看人员id
      personnel_list: [], // 存储陪看人员名称
      selectVisible: false, // 预约带看客户选择
      source_list: [], // 选择客户-客户来源
      TimeScreening: "", // 选择客户-筛选时间
      is_online: "",
      chooseCustomer: {
        page: 1,
        per_page: 10,
        total: 0,
        source_id: "",
        keywords: "",
        type: 0,
        form: 2,
        status: 0,
      },
      customerList: [], // 客户表格内容
      userScrollTop: 0,
      colleagueDetails: {
        headPortrait: "", // 头像
        user_name: "", // 姓名
        department: "", // 部门
        phone: "", // 手机号
      },
      appointmentPage: {
        page: 1,
        per_page: 10,
        name: "",
        mobile: "",
        take_date_b: "",
        take_date_e: "",
        take_no: "",
      },
      appointmentTotal: 0, // 预约带看客户总条数
      appointmentDate: "", // 预约带看筛选时间
      showCall: 0, // 控制外呼按钮是否显示
      call_id: [], // 存储外呼成功，跟进记录call_phone_id参数的值
      control: false,
      colleagueID: [], // 存储同事id
      dialogImageVisible: false,
      dialogImageUrl: "",
      dialogImageWith: "",
      recordName: "Follow",
      table_header: [
        // {
        //   prop: "id",
        //   label: "ID",
        //   width: "80px",
        // },
        {
          label: "客户名称",
          prop: "cname",
        },
        {
          label: "手机号",
          prop: "mobile",
          width: "200px",
        },
        {
          prop: "created_at",
          label: "创建时间",
          width: "200px",
        },
        {
          prop: "source.title",
          label: "来源",
        },
        {
          label: "操作",
          fixed: "right",
          width: "80px",
          render: (h, data) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.onClickDetail(data.row);
                  }}
                >
                  选择
                </el-link>

              </div>
            );
          },
        },
      ],
      surveyVisible: false, // 控制房屋实勘模态框显示
      // 房屋实勘表单参数以及 房源详情内容
      survey_form: {
        content: "", // 跟进内容
        transportation: "", //交通出行  最长200个字符
        structure_introduction: "", //小区户型  最长200个字符
        community_introduction: "", //小区介绍  最长200个字符
        selling_point: "", // 核心卖点  最长200个字符
        info_id: "", // 房源编号
        type: 1, // 有效报备1 无效报备2
        effect_type: "2", // 房屋实勘跟进id
        images: "", // 多张图片 , 分割 选填 最多5张图片
      },
      picture_upurl: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
      disabled_picture: false, // 是否禁用上传图片
      FollowImgList: [], // 跟进图片列表
      show_dialog_pictures: false, // 查看已上传的图片
      dialog_pictures_src: '', // 查看已上传图片的src
      // 删除跟进图片参数
      delPic_parame: {
        id: '', // id
        pic: '' // 图片URL地址
      },
      TimeLine_picList: [], // 房源相册时间线图片列表
      showPhotosList: [], // 房源相册全部图片列表
      firstItm_size: [], // 房源相册当前跟进时间的图片列表
      // 房源相册接口传参
      house_pic_params: {
        page: 1, // 当前页
        per_page: 100, // 每页多少条
      },
      TimeLine_picList_photos: [], // 实勘图片数据据列
      stopMoreHousePhone: false, // 控制房源相册下拉加载
      alreadyUploadList: [], // 已经上传的房源图片
      surveyFileList: [], // 房屋实勘上传图片url
      is_loading: false, // loading加载动画
      uploadJudge: 0, // 房源相册上传判断，等于1表示当前会员 可以正常上传首勘图片 等于0 表示弹出房源实勘跟进上传图片
      showAddPopo: false, // 控制房源相册新增popover
      datalist: [], // 全部部门人员
      AllDepartment: [], // 全部部门列表
      Not_duplicate_datalist: [], // 没有重复部门成员的容器
      show_shitu: false,
    };
  },
  mounted() {
    document.addEventListener("visibilitychange", this.handleVisiable);
    document.getElementById("Go").addEventListener('input', this.ChangeMyContent);
    // 赋文本初始化
    this.$nextTick(() => {
      this.FollowTextContent();
    })
    // this.ceshiData();
  },
  computed: {
    website_info() {
      return this.$store.state.website_info
    },
    // 获取请求头
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
  },
  beforeDestroy() {
    // eslint-disable-next-line no-undef
    eventBus.$off("getDetailAgain");
    this.clearImgInfo();
    document.removeEventListener("visibilitychange", this.handleVisiable);
    document.removeEventListener('input', this.ChangeMyContent);
  },

  created() {
    this.id = this.$route.query.id * 1 || 0;
    this.website_id = this.$route.query.website_id;
    if (this.id) {
      this.follow_params.info_id = this.id;
      this.follow_params1.info_id = this.id;
      this.log_params.info_id = this.id;
      this.survey_form.info_id = this.id;
      this.getDetail();
      this.getHouseDataNumber()
      this.getMaintainDetails()
      this.TimeLine_picList_photos = []; // 清空房源图片列表
      this.getHousePicture(); // 获取房源相册图片
    }

    // eslint-disable-next-line no-undef
    eventBus.$on("getDetailAgain", () => {
      this.getDetail();
    });
    this.getPerssion()
  },

  filters: {
    // mobileFilter(val) {
    //   let reg = /^(.{3}).*(.{3})$/;
    //   return val.replace(reg, "$1*****$2");
    // },
    filterImgType(val) {
      let name = "室内图";
      switch (val) {
        case 1:
          name = "室内图";
          break;
        case 3:
          name = "室外图";
          break;
        case 2:
          name = "户型图";
          break;
        case 4:
          name = "客厅";
          break;
        case 5:
          name = "餐厅";
          break;
        case 6:
          name = "主卧";
          break;
        case 7:
          name = "次卧";
          break;
        case 8:
          name = "厨房";
          break;
        case 9:
          name = "卫生间";
          break;
        case 10:
          name = "阳台";
          break;
        case 11:
          name = "入户";
          break;
        case 12:
          name = "书房";
          break;
        case 13:
          name = "花园";
          break;
        case 14:
          name = "房间外景";
          break;

        case 15:
          name = "小区环境";
          break;

        default:
          break;
      }
      return name;
    },
  },
  beforeRouteLeave(to, from, next) {
    this.clearImgInfo();
    // to.meta.keepAlive = true; // 缓存
    next();
  },
  watch: {
    // 监听 visible，来触发关闭右键菜单，调用关闭菜单的方法
    visible(value) {
      if (value) {
        document.body.addEventListener("click", this.closeMenu);
      } else {
        document.body.removeEventListener("click", this.closeMenu);
      }
    },
    // 监听showDropdown,来触发下拉列表
    showDropdown(value) {
      if (value) {
        document.body.addEventListener('click', () => {
          this.showDropdown = false
        })
      } else {
        document.body.addEventListener('click', () => { })
      }
    },
  },
  methods: {
    async getPerssion() {
      let admin_roles = await this.$http.getAdmin().catch(() => {
        console.log();
      })
      this.shitu_list = admin_roles.data;
      if (this.shitu_list.roles && this.shitu_list.roles.length && this.shitu_list.roles[0].name == '站长') {
        this.show_shitu = true
      } else {
        this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            if ((res.data + '').indexOf(this.shitu_list.id) > -1) {
              this.show_shitu = true
            }
          }
        })
      }
    },
    //聚焦房源弹框确定
    onChangeIsTop() {
      if (!this.is_top_data.content) {
        this.$message.warning("请输入设置原因")
      } else {
        this.$http.houseTopEdit({ ...this.is_top_data, id: this.detail.id }).then((res) => {
          if (res.status == 200) {
            this.$message.success("设置成功")
            this.is_top_cancel()
            this.getDetail()
          }
        })
      }

    },
    //聚焦房源取消按钮
    is_top_cancel() {
      this.is_top_show = false;
      this.is_top_data = {
        is_top: this.detail.is_top,
        content: "",
      }
    },
    //修改聚焦房源
    changeIsTop() {
      this.is_top_show = true
    },
    //获取房源图片视频VR数量
    getHouseDataNumber() {
      this.$http.housePhotoVideoVrNum(this.id).then((res) => {
        if (res.status == 200) {
          console.log(res.data, "res.data");
          this.housePhoVidVRnumber = res.data
        }
      })
    },
    //相机拍摄
    onShoot() {
      if (this.detail.vr_order) {
        this.$message.warning('当前房源已创建订单，等待订单完成')
        return
      }
      this.getVrPhotoList()
      this.show_vr_setting = true
      this.isShoot = true
    },
    //房源VR链接类型
    vrType() {
      this.$http.vrType().then((res) => {
        if (res.status == 200) {
          this.vrTypeOptions = res.data
        }
      })
    },
    //房源视频列表
    houseVideoList() {
      this.isVideoLoading = true
      this.$http.houseVideo(this.id).then((res) => {
        if (res.status == 200) {
          this.isVideoLoading = false
          this.videoList = res.data
        }
      })
    },
    //VR列表
    houseVr() {
      this.isVrListLoading = true;
      this.$http.houseVr(this.id).then((res) => {
        if (res.status == 200) {
          this.isVrListLoading = false
          this.vrList = res.data
        }
      })
    },
    //添加VR链接
    addVrUrl() {
      this.addUrl = true
      this.isVrAdd = true
    },
    //添加VR链接确定
    onAddVrUrl() {
      if (!this.vr_url_form.vr_url) {
        this.$message.warning("请输入url链接地址")
      }
      if (!this.vr_url_form.vr_type) {
        this.$message.warning("请选择链接类型")
      }
      //添加房源vr
      if (this.isVrAdd) {
        this.$http.houseAddVr({ ...this.vr_url_form, id: this.id }).then((res) => {
          if (res.status == 200) {
            this.$message.success("添加成功")
            this.cancelAddUrl()
            this.houseVr()
            this.getHouseDataNumber()
          }
        })
      } else {
        this.$http.houseEditVr(this.vr_url_form, this.editVrId).then((res) => {
          if (res.status == 200) {
            this.$message.success("修改成功")
            this.cancelAddUrl()
            this.houseVr()
            this.getHouseDataNumber()
          }
        })
      }
    },
    //关闭添加VR链接
    cancelAddUrl() {
      this.addUrl = false;
      this.reset()
    },
    //重置添加VR链接表单
    reset() {
      this.vr_url_form = {
        vr_url: "",
        vr_type: "",
        memo: "",
        is_primary: 0
      }
    },
    //删除房源VR信息
    delHousrVr(e) {
      this.$confirm('此操作将删除该vr信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delHouseVr(e).then((res) => {
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.houseVr()
            this.getHouseDataNumber()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    //复制Vr链接
    copyVrLink(e) {
      this.$onCopyValue(e)
    },
    //复制视频链接
    onCopyVideo(e) {
      this.$onCopyValue(e)
    },
    //编辑Vr信息
    editVr(e) {
      let obj = {};
      obj = this.vrList.filter((item) => {
        return item.id == e
      });
      this.vr_url_form = {
        vr_url: obj[0].vr_url,
        vr_type: obj[0].vr_type,
        memo: obj[0].memo,
        is_primary: obj[0].is_primary
      }
      this.editVrId = obj[0].id
      this.addUrl = true;
      this.isVrAdd = false;
    },
    //浏览VR
    browseVr(row) {
      this.current_vr = row.vr_url
      this.isVrLook = true
    },
    //取消房源状态设置弹框
    cancelSettingCom() {
      this.setting_confirm = false;
      this.house_status.trade_status = this.new_trade_status;
    },
    //浏览视频
    browseVideo(e) {
      this.isVideoLookUrl = e
      this.isVideoLook = true

    },
    //检查上传视频不大于25mb
    handleVideoChange(file) {
      let fileSize = Number(file.size / 1024 / 1024)
      if (fileSize > 25) {
        this.$message.error("图片不能大于10M，请重新上传");
        return
      }
    },
    //删除房源视频
    delVideo(e) {
      this.$confirm('此操作将删除改视频, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.privateHousesDelVideo(e).then((res) => {
          if (res.status == 200) {
            this.houseVideoList()
            this.getHouseDataNumber()
            this.$message({
              type: 'success',
              message: `删除成功`
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    //添加视频弹窗
    addHouseVideoShow() {
      this.isAddVideoShow = true
      this.isVideoAdd = true
    },
    //添加视频
    addHouseVideo() {
      if (!this.add_video_form.memo) {
        this.$message.warning("请输入描述")
      }
      if (this.isVideoAdd) {
        this.$http.privateHousesAddVideo(this.add_video_form, this.id).then((res) => {
          if (res.status == 200) {
            this.$message.success("添加成功")
            this.houseVideoList()
            this.cancelVideo()
            this.getHouseDataNumber()
          }
        })
      } else {
        this.$http.privateHousesEditVideo(this.add_video_form, this.videoId).then((res) => {
          if (res.status == 200) {
            this.$message.success("修改成功")
            this.houseVideoList()
            this.cancelVideo()
          }
        })
      }

    },
    //上传视频点击选择文件时
    handlePreviewVideo(file) {
      console.log(file);
    },
    //上传文件成功时
    handleVideoSuccess(response) {
      this.add_video_form.video_url = response.url
      this.videoLoading.close()
    },
    //移除文件时
    handVideoRemove() {
      this.add_video_form.video_url = ""
    },
    //上传成功之前
    handVideoBefore() {
      this.videoLoading = Loading.service({
        text: '视频上传中...',
        spinner: 'el-icon-loading',
        customClass: 'lb-loading-icon-cls',
        background: 'transparent'
      })
    },
    //重置添加视频表单
    cancelVideo() {
      this.isAddVideoShow = false;
      this.videoFileList = []
      this.add_video_form = {
        video_url: "",
        memo: "",
        is_primary: 0
      }
    },
    //修改视频
    editVideo(e) {
      this.isVideoAdd = false;
      this.add_video_form.memo = e.memo;
      this.add_video_form.is_primary = e.is_primary;
      this.add_video_form.video_url = e.url;
      this.videoId = e.id
      this.videoFileList = [e.url].map(item => {
        return {
          name: item,
          url: item
        }
      })
      this.isAddVideoShow = true;
    },
    // 获取部门列表
    async getDepartment() {
      this.getDepartmentList(); // 获取部门
      // 获取部门成员
      if (!this.datalist.length) {
        this.getMemberList();
      }
    },
    // 获取部门
    getDepartmentList() {
      if (!this.AllDepartment.length) {
        this.$http.getCrmDepartmentList().then((res) => {
          if (res.status == 200) {
            this.AllDepartment = res.data;
          }
        });
      }
    },
    // 部门成员去重
    filteredData() {
      const uniqueIds = {};
      const filtered = [];
      for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
        const item = this.Not_duplicate_datalist[i];
        if (!uniqueIds[item.id]) {
          uniqueIds[item.id] = true;
          filtered.push(item);
        }
      }
      this.filtrMember = filtered;
    },
    //获取部门成员信息
    getMemberList() {
      this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data;
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user,
          });
          this.recursionData(this.memberList);
          this.Not_duplicate_datalist = JSON.parse(
            JSON.stringify(this.datalist)
          );
          this.filteredData();
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id + "_" + this.datalist[i].Parent + ''
              }
            }
          }
        }
      });
    },
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              });
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item);
            }
          });
          this.recursionData(data[key].subs);
        }
      }
    },
    reFormData(data, line = "") {
      data.map((item) => {
        item.pArr = `${line ? line + "," : ""}${item.pid}`;
        if (item.subs && item.subs instanceof Array && item.subs.length) {
          let nameLine = `${line ? line + "," : ""}${item.pid}`;
          this.reFormData(item.subs, nameLine);
        }
      });
      return data;
      // for (let index = 0; index < data.length; index++) {
      //   this.pidArr.push()

      // }
    },

    showMemberList(type) {
      // this.selectedIds =[this.rulesForm[type].id]
      if (type == "spr") {
        this.show_audit_member = true;
      } else {
        this.setting_form.type = type;
        this.show_member_list = true;
      }
    },
    selecetedMember(e) {
      this.rulesForm[this.setting_form.type] = e.checkedNodes[0];
      this.settingTitle = "设置权限"
      this.setting_form.content = ''
      // this.setting_info[this.setting_form.type] =e.checkedNodes[0].id
      // this.selectedIds =[this.rulesForm[this.setting_form.type].id]
      this.show_member_list = false;
      this.setting_confirm = true;
    },
    delName(e, type) {
      if (!this.rulesInfo[type].id) return;
      this.rulesInfo[type] = {
        name: "",
        id: "",
      };
      this.setting_form.type = type;
      this.rulesForm[this.setting_form.type] = "";
      this.rulesForm["opt"] = "delete";
      this.setting_confirm = true;
    },
    // 房源设置
    getManagerAuthList() {
      this.$ajax.house
        .getManagerAuthList({ params: this.params })
        .then((res) => {
          if (res.status === 200) {
            this.user_list = res.data.data;
          }
        });
    },
    editTel() {
      this.setting_form.type = "editTel";
      this.setSettingTitle("editTel");
      this.setting_form.content = "";
      this.setting_confirm = true;
    },
    confirmEditTel() {
      let ownerList = Object.assign([], this.ownerList);
      if (ownerList.length) {
        let ownerError = "";
        for (let index = 0; index < ownerList.length; index++) {
          const element = ownerList[index];
          if (ownerList.length > 5 && !element.id) {
            ownerError = true;
            this.$message.warning("业主姓名不能大于5个字符");
            break;
          }
          if (
            element.owner_tel &&
            (element.owner_tel.length !== 11 || element.owner_tel[0] != 1) &&
            !element.id
          ) {
            ownerError = true;
            this.$message.warning("业主手机号格式错误");
            break;
          }
        }
        if (ownerError) return;
      }
      let ids = [],
        newIds = [];
      this.tel_data.map((item) => {
        if (item.id) {
          ids.push(item.id);
        }
      });

      ownerList.map((item) => {
        if (item.id) {
          newIds.push(item.id);
        }
      });
      for (let index = 0; index < ids.length; index++) {
        if (!newIds.includes(ids[index])) {
          let obj = this.tel_data.find((item) => item.id == ids[index]);
          obj.is_del = 1;
          ownerList.push(obj);
        }
      }
      let params = {
        id: this.detail.id,
        tel: JSON.stringify(ownerList),
        content: this.setting_form.content,
      };
      this.$ajax.house.editTel(params).then((res) => {
        if (res.status == 200) {
          this.setting_confirm = false;
          this.has_see = false;
          this.$message.success("提交成功");
        }
      });
    },
    getDetailManagers() {
      this.$ajax.house.getDetailManagers(this.detail.id).then((res) => {
        if (res.status === 200) {
          for (const key in res.data) {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              const element = res.data[key];

              if (element.user_name) {
                element.name = element.user_name;
              } else {
                element.name = "";
                element.user_name = "";
                element.id = "";
              }
            }
          }
          this.rulesInfo = res.data;
          this.rulesForm = Object.assign({}, res.data);
          for (const key in res.data) {
            this.setting_info[key] = res.data[key].id || "";
          }
        }
      });
    },
    onSettingPageChange(e) {
      this.setting_params.page = e;
      this.getManagerAuthList();
    },
    confirmRoles() {
      if (this.setting_form.type == "trade_status") {
        this.submitHouseStatus();
        return;
      }
      if (this.setting_form.type == "editTel") {
        this.confirmEditTel();
        return;
      }
      if (this.setting_form.type == "deleteWeituo") {
        this.deleteWeituo();
        return;
      }
      let params = Object.assign(
        {
          opt: "update",
          id: this.detail.id,
          user_id: this.rulesForm[this.setting_form.type].id,
          user_type: 2,
        },
        this.setting_form
      );
      if (params.content.length < 4) {
        this.$message.warning("原因描述不能少于4个字符");
        return;
      }
      for (const key in this.rulesInfo) {
        if (key == this.setting_form.type) {
          if (
            this.rulesInfo[key] &&
            this.rulesInfo[key].id &&
            !this.setting_info[this.setting_form.type]
          ) {
            params.opt = "delete";
          }
          break;
        }
      }
      if (!params.user_id) params.opt = "delete";
      this.$ajax.house.setHouseRoles(params).then((res) => {
        if (res.status === 200) {
          this.$message.success(res.message || "设置成功");
          this.rulesInfo[this.setting_form["type"]] =
            this.rulesForm[this.setting_form.type];
          this.setting_confirm = false;
          this.getDetailManagers();
        }
      });
    },
    submitHouseStatus() {
      let params = {
        id: this.detail.id,
        content: this.setting_form.content,
        trade_status: this.house_status.trade_status,
      };
      this.$ajax.house.setHouseStatus(params).then((res) => {
        if (res.status == 200) {
          this.setting_confirm = false;
          this.$message.success("提交成功");
        }
      });
    },

    // 房源管理员获取业主电话
    getHouseTel() {
      this.$ajax.house.getHouseTel(this.detail.id).then((res) => {
        if (res.status == 200) {
          this.tel_data = res.data;
          this.ownerList = [];
          if (this.tel_data.length) {
            for (var i = 0; i < this.tel_data.length; i++) {
              this.ownerList.push(JSON.parse(JSON.stringify(this.tel_data[i])));
            }
          } else {
            this.ownerList.push({
              owner: "",
              owner_tel: "",
              type: 1,
              sex: 1,
            });
          }
        }
      });
    },
    addOwner() {
      if (this.ownerList.length < 3) {
        let obj = {
          owner: "",
          owner_tel: "",
          type: 1,
          sex: 1,
        };

        this.ownerList.push(obj);

        // this.$forceUpdate();
      }
    },
    delOwner(index) {
      this.ownerList.splice(index, 1);
      // this.tel_data.splice(index, 1);
    },
    getTelTypeList() {
      this.$ajax.house.getTelTypeList().then((res) => {
        if (res.status == 200) {
          this.typeList = res.data;
        }
      });
    },

    changeRole(type) {
      this.setting_form.type = type;
      this.setting_form.content = "";
      this.setting_confirm = true;
      this.setSettingTitle(type);
    },
    setSettingTitle(type = this.setting_form.type) {
      switch (type) {
        case "agent":
          this.settingTitle = "设置录入人";
          break;
        case "whr":
          this.settingTitle = "设置维护人";
          break;
        case "djwtr":
          this.settingTitle = "设置独家委托人";
          break;
        case "ysr":
          this.settingTitle = "设置钥匙持有人";
          break;
        case "cjr":
          this.settingTitle = "设置成交人";
          break;
        case "skr":
          this.settingTitle = "设置实勘人";
          break;

        case "wtr":
          this.settingTitle = "设置委托人";
          break;
        case "trade_status":
          this.settingTitle = "设置房源状态";
          break;
        case "editTel":
          this.settingTitle = "设置业主手机号";
          break;
      }
    },

    getHouseStatus() {
      this.$ajax.house.getHouseStatus(this.detail.id).then((res) => {
        if (res.status === 200) {
          this.house_status = res.data;
          this.new_trade_status = res.data.values;
          this.$set(this.house_status, "trade_status", res.data.values);
          // this.house_status.trade_status =  res.data.values
        }
      });
    },
    getHouseStatusList() {
      this.$ajax.house.getHouseStatusList().then((res) => {
        if (res.status === 200) {
          this.house_status_list = res.data;
        }
      });
    },

    openMenu(e, item) {
      this.visible = true;
      this.top = e.layerY;
      this.left = e.layerX;
      this.rightClickItem = item;
    },
    // 关闭右键菜单
    closeMenu() {
      this.visible = false;
    },

    onChangeRemark(e) {
      let form = {
        remark: e,
        id: this.c_detail.id,
      };
      if (!form.remark) {
        this.$message.error("请检查备注内容");
        return;
      }
    },
    getDetail(type = true) {
      this.$ajax.house.getDetail(this.id).then((res) => {
        if (res.status == 200) {
          this.detail = res.data;
          this.is_top_data.is_top = res.data.is_top
          // 有设置权限
          if (this.detail.house_manager == 1) {
            this.tabList = this.tabListDefault.concat([
              {
                id: 6,
                name: "设置",
              },
            ])

          }
          // if (this.website_info.open_vr) {
          //   this.tabList = this.tabList.concat([
          //     {
          //       id: 7,
          //       name: "添加vr",
          //     },
          //   ])
          // }
          if (
            this.detail.unilateral_agent == 1 &&
            this.detail.unilateral_agent_auth == 0
          ) {
            if (this.detail.loudong) {
              this.detail.loudong = this.detail.loudong.replace(/\d/g, "*");
            }
            if (this.detail.danyuan) {
              this.detail.danyuan = this.detail.danyuan.replace(/\d/g, "*");
            }
            if (this.detail.fanghao) {
              this.detail.fanghao = this.detail.fanghao.replace(/\d/g, "***");
            }
            // this.$message.warning("请联系房源维护人")

          } else {
            if (this.detail.fanghao) {
              this.fanghao = this.detail.fanghao;
              this.fanghao_view = this.detail.fanghao;
              this.detail.fanghao = this.detail.fanghao[0] + "**";
            }
          }
          if (this.detail.show_whr_tel == 1) {
            this.viewCustomerTel(false);
          }
          // 获取有没有 没跟进的信息
          this.$http.getFollowInfo().catch((err) => {
            console.log(err);
          }).then((followInfo) => {
            if (followInfo.status == 200) {
              if (followInfo.data.remindPhoneFollow && followInfo.data.count > 0 && followInfo.data.info && followInfo.data.info.info_id == this.id) {
                this.viewCustomerTel1()
              } else if (followInfo.data.remindPhoneFollow && followInfo.data.count > 0 && followInfo.data.info && followInfo.data.info.info_id !== this.id) {
                this.$goPath("/house_detail?id=" + followInfo.data.info.info_id)
              }
            }
          })


          if (this.detail.wtr && this.detail.wtr.cname) {
            this.id_card = this.detail.wtr.house_id_card || [];
            this.weituo_img = this.detail.wtr.house_wtxy;
            this.budongchan_img = this.detail.wtr.house_cert || [];
            if (this.id_card.length) {
              this.weituo_img = this.weituo_img.concat(this.id_card);
            }
            if (this.budongchan_img.length) {
              this.weituo_img = this.weituo_img.concat(this.budongchan_img);
            }
          }
          if (this.detail.djwtr && this.detail.djwtr.cname) {
            this.dj_weituo_img = this.detail.djwtr.house_wtxy;
          }
          if (this.detail.wtr_id > 0) {
            this.weituoData = [
              {
                cname: this.detail.wtr.cname,
                ctime: this.formatDate(this.detail.wtr_ctime),
                id: this.detail.wtr.id,
              },
            ];
          } else {
            this.weituoData = []
          }
          if (this.detail.djwtr_id > 0) {
            this.vip_date = [
              this.detail.djwtr.ctime + " 00:00:00",
              this.detail.djwtr.etime + " 00:00:00",
            ];
            this.vip_price = this.detail.djwtr.lowest_price / 10000;
            // this.vipData = [
            //   {
            //     cname: this.detail.djwtr.cname,
            //     ctime: this.detail.djwtr.ctime,
            //     etime: this.detail.djwtr.etime,
            //     id: 1,
            //   },
            // ];
          }

          if (this.imgTypeList.length == 3) {
            this.imgTypeList = this.imgTypeList.concat(this.detail.photo_cat);
          }
          this.telBack = Object.assign([], res.data.tel);
          this.labels_list = this.detail.photo_label.map((item) => {
            item.sub.map((sub) => {
              if (this.detail.label_id.includes(sub.values + "")) {
                sub.check = true;
              }
              return sub;
            });
            return item;
          });
          this.getLabels();

          // this.detail.photos.map(item => {
          //   item.ctime = "2020-08-22"
          //   item.crm_user_name = "磊"
          //   return item
          // })
          if (type) {
            this.getFollowData();
            setTimeout(() => {
              this.getStatusList();
            }, 300);
          }
        }
      });
    },
    formatDate(time, type = "") {


      let timestr = new Date(time * 1000);
      let year = timestr.getFullYear();

      let month = (timestr.getMonth() + 1 + "").padStart(2, "0");
      let day = (timestr.getDate() + "").padStart(2, "0");
      let hour = (timestr.getHours() + "").padStart(2, "0");
      let min = (timestr.getMinutes() + "").padStart(2, "0");
      let sec = (timestr.getSeconds() + "").padStart(2, "0");
      if (type == 'yyyy-MM-dd HH:mm:ss') {
        return (
          year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + sec
        );
      } else if (type == 'yyyy-MM-dd HH:mm') {
        return (
          year + "-" + month + "-" + day + " " + hour + ":" + min
        );
      } else if (type == 'yyyy-MM-dd') {
        return (
          year + "-" + month + "-" + day
        );
      } else {
        return (
          year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + sec)
      }

    },
    getStatusList() {
      this.$ajax.house.getStatusList().then((res) => {
        if (res.status == 200) {
          this.follow_status_list = res.data[0]?.label;
        }
      });
    },
    onClickStatus(item) {
      if (item.name == "预约带看") {
        this.visiblepop = false;
        return this.appointmentVisible = true
      } else if (item.name == "房屋实勘") {
        this.visiblepop = false;
        return this.surveyVisible = true
      }
      this.follow_params.effect_type = item.values;
      this.visiblepop = false;
      this.visiblepop_title = item.name;
    },
    onPicChange(e) {
      this.current_pic_index = e + 1;
    },
    showFanghao() {
      if (this.show_fanghao) return;
      this.$ajax.house.seeRoomNumber(this.id).then((res) => {
        if (res.status == 200) {
          this.show_fanghao = true;
          this.$set(this.detail, "fanghao", this.fanghao);
        }
      });
      // this.show_fanghao = !this.show_fanghao
    },
    loadMoreFollow() {
      if (!this.follow_load) {
        return;
      }
      this.follow_list_params.page++;
      this.getFollowData();
    },
    loadMoreTakeLook() {
      if (!this.TakeLook_load) {
        return;
      }
      this.TakeLook_list_params.page++;
      this.getTakeLookRecord();
    },
    loadMorePhone() {
      if (!this.Phone_load) {
        return;
      }
      this.Phone_list_params.page++;
      this.getPhoneRecord();
    },
    getFollowData() {
      if (this.follow_list_params.page == 1) {
        this.follow_list = [];
      }
      this.follow_load = false;
      this.$ajax.house
        .getFollowList(this.id, { params: this.follow_list_params })
        .then((res) => {
          if (res.status == 200) {
            this.follow_list = this.follow_list.concat(res.data);
            // this.follow_list.map(item => {
            //   item.isPlaying = false;
            // })
            if (res.data.length == this.follow_list_params.rows) {
              this.follow_load = true;
            } else {
              this.follow_load = false;
            }
            // ====================
            this.$nextTick(() => {
              let user_follow = document.getElementsByClassName("user_follow");
              for (let i = 0; i < user_follow.length; i++) {
                user_follow[i].style.cursor = "pointer";
                user_follow[i].onclick = (e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  this.colleagueOnMouseDown(e)
                }
              }
            })
            // ====================
          } else {
            this.follow_load = false;
          }
        })
        .catch(() => {
          this.follow_load = false;
        });
    },
    // 隐藏跟进记录同事信息框
    hideColleague() {
      let popover = document.querySelector(".infoFrame");
      popover.style.display = "none";
    },
    colleagueOnMouseDown(e) {
      let my_clientX = e.clientX;
      let my_clientY = e.clientY;
      let popover = document.querySelector(".infoFrame");
      popover.style.display = "block";
      popover.style.top = my_clientY - 108 + "px";
      popover.style.left = my_clientX - 110 + "px";
      // 通过id获取同事信息
      this.$ajax.house.getFollowColleagueDetails(e.target.dataset.id).then((res) => {
        if (res.status == 200) {
          this.colleagueDetails.department = res.data.department;
          this.colleagueDetails.user_name = res.data.user_name;
          this.colleagueDetails.phone = res.data.phone;
          this.colleagueDetails.headPortrait = this.colleagueDetails.user_name.substring(0, 1);
        }
      })
    },
    onChangeStatus() {
      this.follow_params.content = this.$refs.gain.innerText;
      if (this.follow_params.content.length < 5) {
        return this.$message.error("最少输入不能小于五个文字");
      }
      // 每点击一次清空同事id
      this.follow_params.remind = [];
      // 格式化跟进图片数据
      let nums = []; // 跟进图片接口参数容器
      // 遍历已上传跟进图片数量
      if (this.FollowImgList.length != 0 && this.FollowImgList != undefined) {
        this.FollowImgList.map((item) => {
          nums.push(item.url);
        })
        nums = nums.join(','); // 将数组转换字符串
      } else {
        nums = '';
      }
      this.follow_params.images = nums;
      // 将用户id传入接口参数follow_params
      this.$set(this.follow_params, 'remind', this.colleagueID.toString());
      this.$ajax.house.addFollow(this.follow_params).then((res) => {
        if (res.status == 200) {
          this.FollowImgList = []; // 清空上传跟进图片列表
          this.colleagueID = []; // 清空同事id
          this.$refs.gain.innerText = ""
          this.$message.success("添加跟进成功");
          this.$set(this.follow_params, "content", "");
          this.follow_list_params.page = 1;
          this.getFollowData();
          this.is_view_tel_desc = false;
        } else {
          this.$message.error("添加跟进失败");
        }
      });
    },
    onChangeStatus1() {
      if (this.call_id != []) {
        this.$set(this.follow_params1, "call_phone_id", this.call_id[0]); //电话记录ID
        this.$set(this.follow_params1, "call_name", this.call_id[1]); // 外呼拨打的客户姓名
        this.$set(this.follow_params1, "call_phone", this.call_id[2]);  // 外呼拨打的客户号码
        this.$set(this.follow_params1, "call_show_phone", this.call_id[3]); // 外呼拨打的外显号码
      }
      this.$ajax.house.addFollow(this.follow_params1).then((res) => {
        if (res.status == 200) {
          this.$message.success("添加跟进成功");
          // 电话记录列表设置为空
          this.PhoneRecord_list = [];
          // 电话列表请求第一页参数
          this.Phone_list_params.page = 1;
          // 调用电话列表请求接口
          this.getPhoneRecord();
          // this.follow_list_params.page = 1;
          // this.getFollowData();
          this.show_tel_dialog = false;
          this.call_id = [];
          this.$set(this.follow_params1, "content", "");
          this.$set(this.follow_params1, "call_phone_id", "");
          this.$set(this.follow_params1, "call_name", "");
          this.$set(this.follow_params1, "call_phone", "");
          this.$set(this.follow_params1, "call_show_phone", "");
        } else {
          this.$message.error("添加跟进失败");
        }
      });
    },
    onCreateDataInvalid() {
      this.follow_params.type = 2;
      let params = Object.assign({}, this.follow_params);
      params.content = this.invalid_content;
      this.$ajax.house.addFollow(params).then((res) => {
        if (res.status == 200) {
          this.$message.success("标记无效成功");
          this.invalid_content = "";
          this.is_invalid_dialog = false;
        }
      });
    },
    onClickImgType(type, item) {
      let con = "",
        current = JSON.parse(JSON.stringify(item));
      if (!type.is_del) {
        current.category_id = type.values;
        con = "确认更改图片类型吗?";
      } else {
        current.is_del = type.is_del;
        con = "确认删除该图片吗?";
      }
      this.$confirm(con)
        .then(() => {
          let params = {
            pic: JSON.stringify([current]),
          };
          this.$ajax.house.editPic(this.id, params).then((res) => {
            if (res.status == 200) {
              if (type.is_del) {
                this.TimeLine_picList_photos = []; // 清空房源相册
                this.getHousePicture();
                // this.detail.photos = this.detail.photos.filter(phote => phote.url !== item.url)
              } else {
                item.category_id = type.values;
              }
              this.typepop = false;
              this.$forceUpdate();
            }
          });
        })
        .catch(() => { });
    },
    setHouseCover(item) {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      if (item.is_cover == 1) return;
      this.$confirm("确认设置该图片为封面图吗?")
        .then(() => {
          item.is_cover = 1;
          let params = {
            pic: JSON.stringify([item]),
          };
          this.$ajax.house.editPic(this.id, params).then((res) => {
            if (res.status == 200) {
              this.typepop = false;
              this.TimeLine_picList_photos = []; // 清空房源相册列表
              this.getHousePicture();
              this.$message.success("设置成功");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    async setWeituo(type) {
      let params = {
        id: this.id,
        type,
      };
      if (type == 2) {
        params.key_num = this.key_num;
        params.key_pass = this.key_pass;
        this.keyLoading = true;
      }
      if (type == 1) {
        await this.onClickCus({}, 16);
        return
        // await this.getModelList(13);
        // this.audit_form.f_trade_status = 8;
        // wtxy_dj wtxy  id_card
        // params.wtxy_dj = this.dj_weituo_img.join(",");
        // params.wtxy = this.weituo_img.join(",");
        // params.id_card = this.id_card.join(",");
        // params.house_cert = this.budongchan_img.join(",");
        // this.weituoLoading = true;
      }
      if (type == 3) {
        await this.onClickCus({}, 17);
        await this.getModelList(17);
        return
        // this.audit_form.f_trade_status = 8;
        // params.wtxy_dj = this.dj_weituo_img.join(",");
        // params.dj_etime = this.vip_date[1];
        // params.dj_btime = this.vip_date[0];
        // params.dj_lowest_price = this.vip_price * 10000;
        // this.djweituoLoading = true;
      }
      this.$ajax.house
        .setWeituo(params)
        .then((res) => {
          if (type == 2) {
            this.keyLoading = false;
            if (res.status == 200) {
              this.key_num = "";
              this.key_pass = "";
              this.$message.success("添加钥匙成功");
              this.key_params.page = 1;
              this.getKeyList();
            }
          }
          if (type == 1) {
            if (res.status == 200) {
              this.$message.success("添加委托成功");
              // this.getDetail(false)
              this.weituoDiabled = true;
              this.weituoLoading = false;
            } else {
              this.weituoLoading = false;
            }
          }
          if (type == 3) {
            if (res.status == 200) {
              this.$message.success("添加成功");
              // this.vip_price = "";
              // this.getDetail(false)
              this.djWeituoDisable = true;
              this.djweituoLoading = false;
            } else {
              this.djweituoLoading = false;
            }
          }
        })
        .catch(() => {
          this.djweituoLoading = false;
          this.weituoLoading = false;
          this.keyLoading = false;
        });
    },
    beforeUpload(e, type) {
      if (type == 5) {
        this.clearImgInfo();
      }
      if (type == 5 || type == 6) {
        Loading.service({
          text: "正在上传中 请稍候。。。",
        });
      }
      this["up_loading" + type] = true;
    },
    onUploadSuccess(options = {}) {
      let { response, type } = options;
      this.up_loading1 = false;
      this.up_loading2 = false;
      this.up_loading3 = false;
      this.up_loading4 = false;
      // id_font_card  : '',
      // id_back_card: '',
      // weituo_img: '',
      // dj_weituo_img: ''
      switch (type) {
        case 1:
          this.id_card.push(response.url);
          // this.id_font_card = response.url
          break;
        case 2:
          this.weituo_img.push(response.url);

          break;
        case 3:
          this.dj_weituo_img.push(response.url);
          break;
        case 4:
          this.budongchan_img.push(response.url);
          break;
        default:
          break;
      }
    },
    onExceed(res) {
      let { file, type } = res;
      if (type == 2) {
        if (file.length + this.weituo_img.length > 10) {
          this.$message.warning("最多上传10张图片");
        }
      }
      if (type == 3) {
        if (file.length + this.dj_weituo_img.length > 10) {
          this.$message.warning("最多上传10张图片");
        }
      }
    },
    viewCustomerTel1() {
      // API判断外呼按钮是否显示
      this.$http.getOutboundWeChat().then((res) => {
        if (res.status == 200) {
          this.showCall = res.data.call_open_erp
        }
      })
      this.$ajax.house.seeOwnerTelNew({ id: this.id }).then((res) => {
        if (res.status == 200) {
          this.showTel = JSON.parse(JSON.stringify(this.detail))
          this.is_whr = res.data.is_whr
          if (
            res.data.is_whr == 1
          ) {
            // this.has_see = true;
            this.showTel.tel = JSON.parse(JSON.stringify(this.telBack.concat(res.data.tel)))
            this.showTel.tel.forEach(item => {
              item.owner_tel = item.owner_tel.substring(0, 3) + " " + item.owner_tel.substring(3, 7) + " " + item.owner_tel.substring(7)
            })
            console.log(this.showTel.tel)
          } else {
            // this.has_see = true;
            this.$set(this.showTel, "tel", res.data.tel);
            this.showTel.tel.forEach(item => {
              item.owner_tel = item.owner_tel.substring(0, 3) + " " + item.owner_tel.substring(3, 7) + " " + item.owner_tel.substring(7)
            })
            console.log(this.showTel.tel)
            // console.log(this.detail.tel);
            // this.is_view_tel_desc = "不要忘记写跟进呦";
          }
          if (res.data.log_id > 0) {
            this.follow_params1.log_id = res.data.log_id
          }

          // this.detail.tel = res.data
          this.$forceUpdate();
        }
      });
      this.show_tel_dialog = true;
      // item.is_view_tel = true
      // this.$forceUpdate()
    },
    viewCustomerTel(show = true) {
      if (show) {
        if (
          this.detail.show_whr_tel == 1
        ) {
          this.$message.warning("请联系房源维护人");
        }
      }

      if (this.has_see == true) return;
      this.$ajax.house.seeOwnerTel({ id: this.id }).then((res) => {
        if (res.status == 200) {
          if (
            this.detail.show_whr_tel == 1
          ) {
            this.has_see = true;
            this.detail.tel = this.telBack.concat(res.data);
          } else {
            this.has_see = true;
            this.$set(this.detail, "tel", res.data);
            // console.log(this.detail.tel);
            // this.is_view_tel_desc = "不要忘记写跟进呦";
          }

          // this.detail.tel = res.data
          this.$forceUpdate();
        }
      });
      // item.is_view_tel = true
      // this.$forceUpdate()
    },
    toEdit() {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      this.$goPath("/house_edit?id=" + this.id);
    },
    onClickRemind() {
      let form = {
        id: this.id,
        type: 1,
        ptime: this.currentDate + " " + this.currentTime,
        content: this.remind_content,
      };
      // this.remind_form.id = this.c_id;
      // this.remind_form.remind_time =
      //   this.currentDate + " " + this.currentTime + ":00";
      this.$ajax.house.setRemind(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.follow_list_params.page = 1;
          this.getFollowData();
          this.is_remind_customer = false;
        }
      });
    },
    uploadError() { },
    updateLabels() {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      this.is_labels_customer = true;
      // this.getLabels();
    },
    onClickLabels() {
      this.labels_choose = [];
      this.labels_list.map((item) => {
        item.sub.map((i) => {
          if (i.check) {
            this.labels_choose.push(i.values);
          }
        });
      });
      let form = {
        label: this.labels_choose.join(","),
        id: this.id,
      };
      this.$ajax.house.edit(form).then((res) => {
        if (res.status === 200) {
          this.is_labels_customer = false;
          this.getLabels();
        }
      });
    },
    getLabels() {
      this.$ajax.house.getLabels(this.id).then((res) => {
        if (res.status === 200) {
          this.is_labels_customer = false;

          this.$set(this.detail, "label", res.data);
          // this.labels_list = res.data
        }
      });
    },
    // 获取房源图片(new!新接口)
    getHousePicture() {
      console.log("触发")
      this.$ajax.house.newGetHousePicture(this.id, this.house_pic_params).then((res) => {
        if (res.status == 200) {
          // 如果接口获取的数据不足100就停止请求
          if (this.house_pic_params.per_page == res.data.photos.data.length) {
            this.stopMoreHousePhone = true;
          } else {
            this.stopMoreHousePhone = false;
          }
          this.uploadJudge = res.data.can_up; // 赋值房源相册上传判断
          // console.log(res,"newRes");
          this.TimeLine_picList_photos = this.TimeLine_picList_photos.concat(res.data.photos.data); // 合并实勘图片数据列表
          this.TimeLine_picList = res.data; // 整体实勘数据赋值
          console.log(this.TimeLine_picList, "zzzzxxx")
          // 处理数据以供图片模态框切换图片使用
          let photo_firsts = []; // 首次实勘图片数据列表容器
          let photos_list = []; // 实勘数据图片列表容器
          // 处理首次实勘数据,res.data.photo_first如果没有参数就为[]，如果有参数就是对象{}
          if (res.data.photo_first && !Array.isArray(res.data.photo_first)) {
            res.data.photo_first.list.map((item) => {
              photo_firsts.push(item);
            });
          }
          // 处理实勘数据
          if (res.data.photos.data && res.data.photos.data.length > 0) {
            res.data.photos.data.map((item) => {
              item.list.map((list) => {
                photos_list.push(list);
              })
            });
          }
          if (photo_firsts && photo_firsts.length > 0) {
            this.showPhotosList = photo_firsts.concat(photos_list);
          }
          // console.log(this.showPhotosList,"this.showPhotosList");
        }
      })
    },
    // 房源相册下拉追加相册图片
    loadMoreHousePhone() {
      if (!this.stopMoreHousePhone) {
        return;
      }
      this.house_pic_params.page++;
      this.getHousePicture();
    },
    clickTab(item) {
      this.tabListID = item.id
      if (
        item.id == 5 &&
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      // // vr下订单
      // if (item.id == 7) {
      //   if (this.detail.vr_order) {
      //     this.$message.warning('当前房源已创建订单，等待订单完成')
      //     return
      //   }
      //   this.getVrPhotoList()
      //   this.show_vr_setting = true
      //   this.isShoot = false
      // }
      this.currentTab = item.id;
      if (item.id == 3) {
        this.getKeyList();
        this.getVIPWeituoList();
      }
      this.clearImgInfo();
      if (item.id == 1) {
        this.getDetail();
      }
      if (item.id == 2) {
        this.TimeLine_picList_photos = []; // 清空房源相册
        this.getHousePicture(); // 获取房源相册图片列表
        this.vrType(); //获取vr类型
        this.houseVideoList(); //获取视频列表
        this.houseVr(); //获取VR列表
      }

      if (item.id == 4) {
        // if (!this.map) {
        setTimeout(() => {
          this.initMap();
        }, 100);
        // }
      }
      if (item.id == 5) {
        this.getFilterList();
        this.getLog();
      }
      if (item.id == 6) {
        this.setting_params.page == 1;
        if (this.memberList.length == 0) {
          this.getDepartment();
        }
        this.getManagerAuthList();
        this.getDetailManagers();
        this.getHouseStatusList();
        this.getHouseStatus();
        setTimeout(() => {
          this.getHouseTel();
          this.getTelTypeList();
        }, 200);
      }

    },
    getVrPhotoList() {
      this.$ajax.house.getVrPhotoList(this.id).then((res) => {
        if (res.status == 200) {
          this.vr_photo_list = res.data;
        }
      });
    },
    getKeyList() {
      this.$ajax.house.getKeyList(this.id, this.key_params).then((res) => {
        if (res.status == 200) {
          this.keyData = res.data.list;
        }
      });
    },
    getVIPWeituoList() {
      this.$ajax.house
        .getVIPWeituoList(this.id, this.vipWeituo_params)
        .then((res) => {
          if (res.status == 200) {
            this.vipData = res.data.list;
          }
        });
    },
    filterCag(c_id) {
      let name = "室内图";
      switch (c_id) {
        case 1:
          name = "室内图";
          break;
        case 3:
          name = "室外图";
          break;
        case 2:
          name = "户型图";
          break;
        default:
          break;
      }
      return name;
    },
    operImg(item, index, firstItm) {
      // console.log(item,index,"item,index");
      // console.log(firstItm,"firstItm");
      this.firstItm_size = firstItm; // 点击图片，获取当前时间的全部图片列表
      let name = this.filterCag(item.category_id); // 赋值name属性
      this.imgTitle = "图片预览-" + name; // 赋值模态框title
      this.currentImgIndex = index; // 赋值当前图片index
      this.currentImg = item; // 赋值当前图片item属性
      this.showPreview = true; // 显示模态框
    },
    downloadIamge(imgsrc, name) {
      //下载图片地址和图片名
      var image = new Image();
      // 解决跨域 Canvas 污染问题
      var oImg = document.getElementById("preImg");
      let w = oImg.naturalWidth;
      let h = oImg.naturalHeight;
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        var canvas = document.createElement("canvas");
        canvas.width = w;
        canvas.height = h;
        var context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, w, h);
        var url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        var a = document.createElement("a"); // 生成一个a元素
        var event = new MouseEvent("click"); // 创建一个单击事件
        a.download = name || "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = imgsrc;
    },
    // downloadImg(src, name) {
    //   // 禁止浏览器缓存；否则会报跨域的错误
    //   var x = new XMLHttpRequest()
    //   x.open('GET', src + '?t=' + new Date().getTime(), true)
    //   x.responseType = 'blob'
    //   x.onload = function () {
    //     var url = window.URL.createObjectURL(x.response)
    //     var a = document.createElement('a')
    //     a.href = url
    //     a.download = name
    //     a.click()
    //   }
    //   x.send()
    // },
    // 改downs这个函数就行，downloadIamge（‘图片下载地址’，图片名字）
    saveImg() {
      console.log(this.currentImg, "this.currentImg")
      // 主要是为了名字不重复
      var name = new Date().getTime();
      console.log(name, "name");
      this.downloadIamge(this.currentImg.url, `${name}`);
    },

    // saveImg() {
    //   let href = this.currentImg.url
    //   var link = document.createElement('a');
    //   // var href = window.URL.createObjectURL(blob);
    //   link.href = href;
    //   // link.download = fileName;  //a标签的下载属性
    //   document.body.appendChild(link);
    //   link.click();
    // },
    showNext() {

      this.currentImgIndex++;
      let current_index = this.currentImgIndex;
      if (current_index > this.firstItm_size.length - 1) {
        current_index = 0;
        this.currentImgIndex = 0;
      }
      let name = this.filterCag(this.firstItm_size[current_index].category_id);
      this.imgTitle = "图片预览-" + name;
      this.currentImg = this.firstItm_size[current_index];
    },
    showPre() {
      this.currentImgIndex--;
      let current_index = this.currentImgIndex;
      if (current_index < 0) {
        current_index = this.firstItm_size.length - 1;
        this.currentImgIndex = this.firstItm_size.length - 1;
      }
      let name = this.filterCag(this.firstItm_size[current_index].category_id);
      this.imgTitle = "图片预览-" + name;
      this.currentImg = this.firstItm_size[current_index];
    },
    setImgType(type) {
      this.currentImg.category_id = type;

      this.show_select_type = false;
    },
    selectImgOk() {
      // let photos = []
      // for (let index = 0; index < this.detail.photos.length; index++) {
      //   const element = JSON.parse(JSON.stringify(this.detail.photos[index]))
      //   photos.push(element)
      // }
      // this.$set(photos, photos[this.currentImgIndex], this.currentImg)
      let params = {
        id: this.id,
        pic: JSON.stringify([this.currentImg]),
      };
      this.$ajax.house.edit(params).then((res) => {
        if (res.status == 200) {
          this.$set(
            this.photos,
            this.photos[this.currentImgIndex],
            this.currentImg
          );
          this.showPreview = false;
        }
      });
    },
    setCover() {
      this.show_cover = false;
      if (!this.currentImg.is_cover || this.currentImg.is_cover == 0) {
        this.currentImg.is_cover = 1;
      } else {
        this.currentImg.is_cover = 0;
      }

      this.$forceUpdate();
    },
    deleteImg() {
      this.show_delete = false;
      this.showPreview = false;
    },
    initMap() {
      var that = this;
      // eslint-disable-next-line no-undef
      let myLatlng = new qq.maps.LatLng(this.detail.lat, this.detail.lng);

      var myOptions = {
        zoom: 16,
        center: myLatlng,
        mapTypeControl: false,
        panControl: false,
        scaleControl: false,
        zoomControl: false,
      };
      // eslint-disable-next-line no-undef
      that.map = new qq.maps.Map(
        document.getElementById("map_container"),
        myOptions
      );
      this.searchMap("教育");
    },
    onClickSearchType(index) {
      this.show_search_res = true;
      this.search_type_index = index;
      this.marker_list = this.marker_list.map((item) => {
        item.show_window = false;
        return item;
      });
      this.$nextTick(() => {
        this.searchMap(this.search_types[index].keywords);
      });
    },
    onClickSearchRes(item) {
      this.setMapCenter(item.latLng.lat, item.latLng.lng);
      this.onClickMarker(item);
    },
    setMapCenter(lat, lng) {
      // eslint-disable-next-line no-undef
      this.map.panTo(new qq.maps.LatLng(lat, lng));
    },
    processSearchRes(list) {
      return (
        list
          // .reduce((res, item) => [...res, ...item])
          .sort((a, b) => a.dist - b.dist)
      );
    },
    searchMap(keywords = "") {
      this.marker_list = [];
      var that = this;
      // eslint-disable-next-line no-undef
      let myLatlng = new qq.maps.LatLng(this.detail.lat, this.detail.lng);
      // var point = new this.BMap.Point(this.center.lng, this.center.lat)
      // this.setMapCenter(this.center.lng, this.center.lat)
      // eslint-disable-next-line no-undef
      var searchService = new qq.maps.SearchService({
        complete: function (results) {
          //设置回调函数参数
          var pois = results.detail.pois;
          that.marker_list = that
            .processSearchRes(pois)
            .filter((item) => !isNaN(item.dist));
        },
        //若服务请求失败，则运行以下函数
        error: function (err) {
          console.log(err);
        },
      });
      searchService.setPageCapacity(100);
      searchService.searchNearBy(keywords, myLatlng, 3000);
    },
    clearOverlays(marker) {
      if (marker) {
        marker.setMap(null);
      }
    },
    onUploadSuccess1(option = {}) {
      let { response, type, fileList, empty } = option;
      let img = {
        category_id: type,
        is_cover: 0,
        descp: "",
        url: response.url,
      };
      this.files.push(img);
      if (this.files.length == fileList.length) {
        let params = {
          pic: JSON.stringify(this.files),
        };
        this.fileLists = [];
        this.$ajax.house
          .editPic(this.id, params)
          .then((res) => {
            if (empty) {
              this.up_loading6 = false;
            } else {
              this.up_loading5 = false;
            }
            Loading.service().close();
            this.files = [];
            if (res.status == 200) {
              this.TimeLine_picList_photos = []; // 清空房源相册
              this.getHousePicture();
            }
          })
          .catch(() => {
            Loading.service().close();
            if (empty) {
              this.up_loading6 = false;
            } else {
              this.up_loading5 = false;
            }
            this.files = [];
          });
      }
    },
    onClickLevel(item) {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      this.$confirm("确定更改房源等级吗？").then(() => {
        let form = {
          level: item.id,
          id: this.id,
        };
        this.$ajax.house.edit(form).then((res) => {
          if (res.status === 200) {
            this.$set(this.detail, "level", item.id);
            // this.is_labels_customer = false;
            // this.getLabels();
          }
        });
      });
    },

    onClickMarker(marker) {
      this.clearOverlays(this.marker);
      // eslint-disable-next-line no-undef
      this.marker = new qq.maps.Marker({
        position: marker.latLng,
        map: this.map,
        content: marker.name,
      });
      // eslint-disable-next-line no-undef
      var markerIcon = new qq.maps.MarkerImage(
        require("@/assets/icon/marker2.png")
      );
      this.marker.setIcon(markerIcon);
      this.marker.setPosition(marker.latLng);
    },
    changeTradeType() {
      this.log_params.page = 1;
      this.getLog();
    },
    changeOptType(e) {
      this.is_online = e.values;
      // console.log(this.is_online,"is_online")
      if (e.values != '26_s') {
        this.log_params.opt_type = e.values;
        this.log_params.page = 1;
        this.getLog();
      }
      if (e.values == '26_s') {
        this.WithShowList()
      }
    },
    WithShowList() {
      this.$ajax.house.getShowRegisterList(this.id, this.appointmentPage).then((res) => {
        if (res.status == 200) {
          this.WithShowTabData = res.data.data
          this.appointmentTotal = res.data.total;
        }
      })
    },
    onPageChange(e) {
      this.log_params.page = e;
      this.getLog();
    },
    appointmentPageChange(e) {
      this.appointmentPage.page = e;
      this.WithShowList();
    },
    appointmentDateChange(e) {
      if (e == null || e == 0) {
        this.appointmentPage.take_date_b = "";
        this.appointmentPage.take_date_e = "";
      } else {
        this.appointmentPage.take_date_b = e[0];
        this.appointmentPage.take_date_e = e[1];
      }
    },
    resetSeach() {
      this.appointmentPage.name = "";
      this.appointmentPage.mobile = "";
      this.appointmentPage.take_no = "";
      this.appointmentPage.take_date_b = "";
      this.appointmentPage.take_date_e = "";
      this.appointmentDate = "";
    },
    customerSeach() {
      this.appointmentPage.page = 1;
      this.WithShowList();
    },
    onKeyPageChange(e) {
      this.key_params.page = e;
      this.getKeyList();
    },
    getFilterList() {
      this.opt_type_list = [];
      this.$ajax.house.getLogFilterList().then((res) => {
        if (res.status == 200) {
          let obj = {
            name: "全部",
            values: "",
          };
          let register = {
            name: "预约带看",
            values: "26_s",
          }
          this.trade_type_list = res.data.trade_type;
          this.trade_type_list.unshift(obj);
          for (let i = 0; i < 24; i++) {
            if (res.data.opt_type[i] == undefined) {
              console.log("")
            } else {
              this.opt_type_list.push(res.data.opt_type[i])
            }
          }
          this.opt_type_list.unshift(obj);
          this.opt_type_list.push(register);
        }
      });
    },
    // 获取房源日志
    getLog() {
      this.is_table_loading = true;
      this.$ajax.house.getLog(this.log_params).then((res) => {
        this.is_table_loading = false;
        if (res.status == 200) {
          this.tableData = res.data.data;
          this.total = res.data.count;
          this.is_loading = false; // 关闭loading
          // 下载excel文档
          if (this.log_params && this.log_params.export == 1) {
            // window.open(res.data)
            this.downloadFile(res.data);
            delete this.log_params.export; // 删除导出excel参数
            this.getLog();
          }
        } else {
          this.is_loading = false;
        }
      }).catch(() => {
        this.is_loading = false;
        delete this.log_params.export;
      })
    },
    downloadFile(file) {
      const link = document.createElement('a');
      link.href = file;
      link.click();
    },
    onClickCus(e, id = 11) {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      // if (this.website_id == 176 || this.website_id == 208) {
      this.audit_form = { cat_id: id }; // 赋值审批类型id
      this.attenchmentList = [];
      this.auditPersonSelect = [];
      this.selectedAuditIds = [];
      this.keyAuditList = [];
      this.getAuditTypeList(); // 获取审批类型列表
      if (!this.memberList.length) {
        this.getDepartment(); // 获取部门成员列表
      }
      this.show_audit = true;
      return;
      // }

      // this.$goPath(
      //   `crm_customer_audit_house?id=${this.id}&name=${this.detail.owner}&tel=${this.detail.owner_tel}`
      // );
    },
    onUploadAttechmentSuccess(options = {}) {
      let { response } = options;
      this.attenchmentList.push(response.url);
    },

    // 审批类型
    getAuditTypeList() {
      //获取审批类型
      this.$ajax.house.getAuditTypeList(2).then((res) => {
        if (res.status == 200) {
          this.auditList = res.data;
          if (this.auditList.length) {
            this.getAuditPerson(this.auditList[0].values);
          }
          if (this.auditList.length && this.auditList[0].is_model == 1) {
            this.getModelList(this.auditList[0].values);
          }
        }
      });
    },
    getModelList(id) {
      this.$ajax.house.getModalList(id, this.detail.id).then((res) => {
        res.data.map((item) => {
          if (item.type == "date") {
            this.$set(this.audit_form, item.name, this.formatDate(+new Date() / 1000, item.date_format))
            // this.audit_form[item.name] = new Date();
          }
        });
        this.keyAuditList = res.data;
        // console.log(this.keyAuditList,"this.keyAuditList");
      });
    },
    changeAudit(e) {
      if (e == 14) {
        this.audit_form.f_price = this.detail.trade_type == 1 || this.detail.addPicture == 3 ? this.detail.sale_price / 10000 : this.detail.rent_price / 10000
      }
      let curr = this.auditList.find((item) => item.values == e);
      if (curr && curr.is_model) {
        this.getModelList(e);
      } else {
        this.keyAuditList = [];
      }
    },
    selecetedAuditMember(e) {
      this.selectedAuditIds = e.checkedKeys;
      this.auditPersonSelect = e.checkedNodes;
    },
    selectAuditOk() {
      this.audit_form.approver_uid = this.selectedAuditIds.join(",");
      // this.selecetedAuditMember = this.selectedList
      this.show_audit_member = false;
    },

    getAuditPerson(id) {
      this.$ajax.house.getAuditPerson(id).then((res) => {
        this.auditPersonList = res.data;
      });
    },
    // 确定提交审批
    submitAudit() {
      // 如果提交审批时审批类型不是改盘申请
      if (this.audit_form.cat_id != 15 && this.audit_form.follow_id) {
        delete this.audit_form.follow_id; // 删除参数follow_id：跟进记录id
      }
      let params = Object.assign({}, this.audit_form);
      params.sys_hid = this.detail.id;
      if (this.auditPersonList.length) {
        params.approver_uid = [];
        this.auditPersonList.map((item) => {
          params.approver_uid.push(item.id);
        });
        // params.approver_uid = approver_uid.join(",")
      } else {
        params.approver_uid = this.selectedAuditIds;
      }

      params.attachment = this.attenchmentList;

      for (let key in params) {
        if (Object.prototype.toString.call(params[key]) === "[object Array]") {
          if (params[key].length > 0 && typeof params[key][0] === "object") {
            params[key] = JSON.stringify(params[key]);
          } else {
            params[key] = params[key].join(",");
          }
        }
      }
      this.$ajax.house.addHouseAudit(params).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.mesage || "提交成功");
          this.$refs.certificates.clearFiles(); // 清空已上传的文件
          this.show_audit = false;
        }
      });
    },

    checkChangeLabels(index0, index, item) {
      item.check = !item.check;
      this.$forceUpdate();
    },
    changeCurrentTime(index) {
      this.currentTimeIndex = index;
      this.currentTime = this.timeArr[index];
    },
    // 获取几天前 几天后 的日期 传递正数时是几天后的 参数为整数 返回对象  年月日   月日 和周几 因为传递给后端需要年所以加了个fullDate 带年的日期
    getDay(day) {
      var today = new Date();
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
      today.setTime(targetday_milliseconds); //注意，这行是关键代码

      var tYear = today.getFullYear();

      var tMonth = today.getMonth();
      var tDate = today.getDate();
      let weekDay = this.doHandleWeek(today.getDay());
      tMonth = this.doHandleMonth(tMonth + 1);
      tDate = this.doHandleMonth(tDate);

      return {
        fullDate: tYear + "-" + tMonth + "-" + tDate,
        shortDate: tMonth + "月" + tDate + "日",
        weekDate: weekDay,
      };
    },
    // 组合最近7天的日期
    getDateArray() {
      this.date_range = [];
      let arr = [0, 1, 2, 3, 4, 5, 6];
      arr.map((item) => {
        this.date_range.push(this.getDay(item));
      });
      if (this.current_index < 0) {
        this.current_index = 1;
      }
      this.currentDate = this.date_range[this.current_index].fullDate;
    },
    // 不足十 补0
    doHandleMonth(month) {
      var m = month;

      if (month.toString().length == 1) {
        m = "0" + month;
      }
      return m;
    },
    // 设置星期
    doHandleWeek(num) {
      let week = "";
      switch (num) {
        case 0:
          week = "周日";

          break;
        case 1:
          week = "周一";

          break;
        case 2:
          week = "周二";

          break;
        case 3:
          week = "周三";

          break;
        case 4:
          week = "周四";

          break;
        case 5:
          week = "周五";

          break;
        case 6:
          week = "周六";

          break;
        default:
          week = "";
          break;
      }
      return week;
    },
    showRemind() {
      this.$ajax.house.getRemindDate(this.id).then((res) => {
        if (res.status == 200) {
          if (res.data.last_msg && res.data.last_msg.id) {
            this.$message.warning("已有设置的跟进提醒");
            return;
          }
          this.getDateArray();
          this.timeArr = res.data.list;
          this.remind_content = res.data.sms_content;
          this.currentTime = this.timeArr[0];
          this.is_remind_customer = true;
        }
      });
    },
    changeTab(index, item) {
      // 如果选择的和当前激活的不同
      this.current_index = index;
      this.currentDate = item.fullDate;
    },
    makeMiniCode() {
      if (this.token) {
        this.clearImgInfo(this.token);
      }
      this.$refs.qrCodeUrl.innerHTML = "";

      this.$ajax.house
        .getUpToken({
          info_id: this.id,
          type: 1,
          category: 3,
        })
        .then((res) => {
          if (res.status == 200) {
            this.identify = res.data.identify;
            this.token = res.data.token;
            this.$nextTick(() => {
              // http://localhost:81/fenxiao/    https://yun.tfcs.cn/fenxiao/
              let url = `https://yun.tfcs.cn/fenxiao/house/uploadImg?tenant_id=1&website_id=${window.localStorage.getItem(
                "website_id"
              )}&identify=${res.data.identify}`;
              // let url = res.data.url

              this.img = new QRCode(this.$refs.qrCodeUrl, {
                text: url, // 需要转换为二维码的内容
                width: 160,
                height: 160,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H,
              });
              if (this.mobile_up) {
                this.watchUploadImg(this.token);
              }
            });
          }
        });
    },
    watchUploadImg(token) {
      this.timer = setInterval(() => {
        this.getUploadImg(token);
      }, 1500);
    },
    getUploadImg(token) {
      this.$ajax.house.getUploadImg(token).then((res) => {
        if (res.status == 200 && res.data.length) {
          this.show_img_dia = true;
          res.data.map((item) => {
            item.url = item.path;
            return item;
          });
          this.img_list = res.data;
        } else if (res.status !== 200) {
          if (this.timer) {
            clearInterval(this.timer);
          }
        }
      });
    },
    handleVisiable(e) {
      if (e.target.visibilityState === "visible") {
        // 要执行的方法
      } else {
        this.clearImgInfo();
      }
    },
    // handleScroll(e) {
    //   console.log(e.target.scrollTop);
    //   this.userScrollTop = e.target.scrollTop;
    //   this.userScrollLeft = e.target.offsetLeft
    // },
    //清除定时器并且执行清除数据接口
    clearImgInfo() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (this.token) {
        this.setClear();
      }
    },
    cancelUp() {
      this.clearImgInfo();
      if (this.mobile_up) {
        this.mobile_up = false;
      }
    },
    setClear() {
      this.$ajax.house
        .clearImg(this.token)
        .then(() => {
          this.token = "";
        })
        .catch(() => { });
    },
    cancelSubmitImg() {
      this.clearImgInfo();
      setTimeout(() => {
        this.show_img_dia = false;
      }, 500);
    },

    submitImg() {
      let imgArr = [];
      this.img_list.map((item) => {
        let obj = {
          category_id: 1,
          descp: "",
          is_cover: 0,
          url: item.url,
        };
        imgArr.push(obj);
      });
      this.$ajax.house
        .editPic(this.id, { pic: JSON.stringify(imgArr) })
        .then((res) => {
          if (res.status == 200) {
            this.clearImgInfo();
            this.TimeLine_picList_photos = []; // 清空房源相册
            this.getHousePicture();
            setTimeout(() => {
              this.show_img_dia = false;
            }, 500);
          }
        });
    },
    setTop() {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      let text = "";
      let form = {
        is_top: 0,
        id: this.id,
      };
      if (this.detail.is_top == 1) {
        form.is_top = 0;
        text = "确定取消聚焦吗";
      } else {
        form.is_top = 1;
        text = "确定设置该房源为聚焦房源吗";
      }
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$ajax.house.edit(form).then((res) => {
            if (res.status === 200) {
              this.$set(this.detail, "is_top", form.is_top);
              // this.is_labels_customer = false;
              // this.getLabels();
            }
          });
        })
        .catch(() => {
          console.log("取消");
        });
    },
    async setWuxiao() {
      if (
        this.detail.unilateral_agent == 1 &&
        this.detail.unilateral_agent_auth == 0
      ) {
        this.$message.warning("请联系房源维护人");
        return;
      }
      // if (this.website_id == 176 || this.website_id == 208) {
      await this.onClickCus({}, 13);
      await this.getModelList(13);
      this.audit_form.f_trade_status = 8;
      //   return;
      // }
      // this.is_invalid_dialog = true;
    },
    beforeUploadShowMessage() {
      if (this.detail.protect == 1) {
        this.$message.warning("房源处于保护期内，只有录入人可操作")
        return false
      }
    },
    showShare() {
      this.show_share = true;
    },
    copyShareText() {
      let text = "";
      // , concatWho = '';
      // if (this.detail.unilateral_agent == 0) {
      //   concatWho = `👩联系人：${this.detail.current_login_user
      //     ? this.detail.current_login_user.user_name
      //     : ""
      //     }`
      // } else {
      //   concatWho = ''
      // }
      text = `🎉${this.detail.title}${this.detail.shi}室${this.detail.ting}厅${this.detail.wei
        }卫 / 公盘编号：${this.detail.id}
💎小区：${this.detail.title}   
🛏户型：${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫
👍楼层：${this.detail.sz_floor}/${this.detail.total_floor}层
🛏装修：${this.detail.zhuangxiu}
🏡面积：${this.detail.mianji}㎡
🎁价格：${this.detail.trade_type == 1 || this.detail.trade_type == 3
          ? this.detail.sale_price / 10000 + "万"
          : this.detail.rent_price + "元/月"
        }${this.detail.unilateral_agent == 0
          ? `
👩联系人：${this.detail.current_login_user
            ? this.detail.current_login_user.user_name
            : ""
          }`
          : ""
        }
☎️联系电话：${this.detail.current_login_user
          ? this.detail.current_login_user.phone
          : ""
        }`;
      let oInput = document.createElement("textarea");
      oInput.value = text;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message.success("复制成功");
      oInput.blur();
      oInput.remove();
      this.show_share = false;
    },
    houseGroup() {
      this.$ajax.house.getSiteList().then((res) => {
        if (res.status == 200) {
          this.siteList = res.data.sites.map((item) => {
            item.checked = false;
            return item;
          });
          let sites = this.siteList.filter(item => item.is_open && item.is_binding)
          if (sites && sites.length) {
            sites[0].is_checked = true
          }
          this.bind_phone = res.data.user.phone;
          this.show_group_house = true;
        }
      });
    },
    toBinding(site) {
      if (site.is_open == 0) {
        this.$message.warning("当前未开通，可联系管理员配置开启");
        return;
      }
      if (site.is_binding == 0) {
        this.current_site = site;
        this.bind_site = true;
        this.show_group_house = false;
        if (site.id == 0) {
          this.bind_site = true;
          this.bind_site_phone = true
        } else {
          this.bind_site_phone = false
        }
        return;
      }
      site.is_checked = !site.is_checked;
      this.$forceUpdate()
    },
    onClickShare(item) {
      if (item.values == "copy") {
        this.$ajax.house.shareHouse(this.detail.id).then((res) => {
          if (res.status == 200) {
            this.show_share = true;
            this.share_pop = false;
          }
        });
      } else if (item.values == "group_send") {
        this.houseGroup();
      } else if (item.values == "douyin_send") {
        //  && !this.videoList.length
        console.log(this.TimeLine_picList, "this.TimeLine_picList")
        if (!this.TimeLine_picList.photo_first.agent_id) {
          this.$message.warning("发布到抖音需上传房源实勘图片")
          return
        }

        this.getSendInfos()
        this.handle('https://lf3-static.bytednsdoc.com/obj/eden-cn/fljpeh7hozbf/douyin_open/cdn/dy_open_util_v0.0.6.umd.js', 'douyin_share_script')
        this.showSendDouyin = true
      }
    },
    getSendInfos() {
      this.$ajax.house.getSucai(this.detail.id).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.douyinInfo = res.data
          if (this.douyinInfo.photos && this.douyinInfo.photos.length) {
            this.douyinInfo.photos.map(item => {
              item.checked = false
              return item
            })
          }
          if (this.douyinInfo.videos && this.douyinInfo.videos.length) {
            this.douyinInfo.videos.map(item => {
              item.checked = false
              return item
            })
          }
        }
      })
    },
    timeDown(s) {
      if (this.time_down) {
        return;
      }
      this.timer && clearInterval(this.timer);
      this.second = s;
      this.time_down = true;
      this.timer = setInterval(() => {
        if (this.second <= 0) {
          this.time_down = false;
          clearInterval(this.timer);
          return;
        }
        this.second--;
      }, 1000);
    },
    getVerifyImg() {
      this.$ajax.house
        .sendPhoneMsg()
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("发送成功！");
            this.code_focus = true;
            this.timeDown(60);
          }
        })
        .catch(() => {
          this.$message.error("发送失败，请重试");
        });
    },
    cancelBindTel() {
      this.bind_site = false;
      this.show_group_house = true;
    },
    bindTel() {
      if (!this.bind_site_phone) {
        this.$ajax.house
          .bindSiteByPhone(this.current_site.id, this.form)
          .then((res) => {
            if (res.status == 200) {
              this.$message.success("绑定成功！");
              this.$set(this.current_site, "is_binding", true);
              this.bind_site = false;
              this.show_group_house = true;
            }
          })
          .catch(() => {
            this.$message.error("发送失败，请重试");
          });
      } else {
        this.$ajax.house
          .bindSiteByPhone(this.current_site.id, { phone: this.bind_phone })
          .then((res) => {
            if (res.status == 200) {
              this.$message.success("绑定成功！");
              this.$set(this.current_site, "is_binding", true);
              this.bind_site = false;
              this.show_group_house = true;
            }
          })
          .catch(() => {
            this.$message.error("发送失败，请重试");
          });
      }

    },
    houseGroupSend() {
      if (this.detail.protect == 1) {
        this.$message.warning("当前房源在保护期内不能群发");
        return;
      }
      let pushArr = [];
      this.siteList.map((item) => {
        if (item.is_checked) {
          pushArr.push(item.id);
        }
        return item;
      });
      if (pushArr.length == 0) {
        this.$message.warning("请选择企业");
        return
      }
      let push_ids = pushArr.join(",");
      if (this.subing) return;
      this.subing = true;
      this.loading = Loading.service({
        lock: true,
        text: "正在推送中 请稍后...", //可以自定义文字
        spinner: "el-icon-loading", //自定义加载图标类名
      });
      this.$ajax.house
        .houseSendGroup(this.detail.id, { push_ids })
        .then((res) => {
          if (res.status == 200) {
            this.loading.close();
            this.subing = false;
            this.$message.success("发送成功");
            this.show_group_house = false;
          } else {
            this.$message.error("发送失败");
            this.subing = false;
            this.loading.close();
          }
        })
        .catch(() => {
          this.subing = false;
          this.loading.close();
        });
    },
    deleteKeyLog(row) {
      this.$ajax.house.delKeyLog(row.id).then((res) => {
        if (res.status == 200) {
          this.$message.success(res.message || "删除成功");
          this.getKeyList();
        } else {
          this.$message.error(res.message || "删除失败");
        }
      });
    },
    confirmVrSetting() {
      this.vr_setting_form.id = this.id
      this.$ajax.house.setVrOrder(this.vr_setting_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || '创建订单成功')
          this.show_vr_setting = false
          this.getDetail()
        }
      })
    },
    showCancelReason(row, type) {
      this.deleteId = row.id
      if (type) {
        this.delType = type
      } else {
        this.delType = ''
      }
      this.settingTitle = "撤销委托"
      this.setting_form.type = 'deleteWeituo'
      this.setting_confirm = true
    },
    deleteWeituo() {
      this.$ajax.house.deleteWeituo({ id: this.deleteId, memo: this.setting_form.content }).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || '撤销成功')
          this.setting_confirm = false
          if (this.delType == 'vip') {
            this.getVIPWeituoList()
          }
          this.getDetail()
        }
      })
    },
    selectDouyinImg(item) {
      if (this.douyin_type == 'image') {
        item.checked = !item.checked
      }
      if (this.douyin_type == 'video') {
        this.douyinInfo.videos.map(i => {
          i.checked = false
          if (i.url == item.url) {
            i.checked = true
          }
        })
      }
      this.$forceUpdate()

    },
    previewQrcode() {
      const path = this.$refs.qrCode.querySelector('img').src;
      this.previewImage(path);
      this.dialogImageWith = 600;
    },
    previewImage(path) {
      this.dialogImageWith = 0;
      this.dialogImageUrl = path;
      this.dialogImageVisible = true;
    },
    shareDouyin() {
      this.$http.shareDouyin({ id: this.detail.id, type: 'house', user_id: this.detail.current_login_user.crm_user_id }).then(res => {
        res.data.image_path = ''
        res.data.image_list_path = JSON.stringify(this.image_list_path)
        res.data.video_path = this.video_path
        const schema = window.dy_open_util.serialize(res.data);
        this.show_qrcode = true
        this.$nextTick(() => {
          this.$refs.qrCode.innerHTML = "";
          new QRCode(this.$refs.qrCode, {
            text: schema, // 需要转换为二维码的内容
            width: 200,
            height: 200,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.M,
          });
        });
        setTimeout(() => {
          this.showSendDouyin = false
        }, 2000);
      })
    },
    handle(link, type = '') {
      document.getElementById(type) && document.getElementById(type).remove();
      var hm = document.createElement("script");
      hm.src = link;
      hm.id = type || (+new Date() + '')
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    },
    sendToDouyin() {
      this.image_list_path = []
      this.douyinInfo.photos.map(item => {
        if (item.checked) {
          this.image_list_path.push(item.url)
        }
      })
      let chekedVideo = this.douyinInfo.videos.find(item => item.checked)
      if (chekedVideo) {
        this.video_path = chekedVideo.url
      } else {
        this.video_path = ''
      }
      this.shareDouyin()
    },
    showCallPhone(item) {
      this.CallPhoneOwner = item.owner;
      // 将带空格的手机号处理成不带空格
      this.autoPhonenNumber = item.owner_tel.replace(/\s*/g, "")
      this.showPhone = true
    },
    closeCallPhone() {
      this.showPhone = false
    },
    getCallId(e) {
      // console.log(e);
      this.call_id = e
    },
    // 房源维护度接口
    getMaintainDetails() {
      this.$ajax.house.getMaintainDetails(this.id).then(res => {
        if (res.status == 200) {
          res.data.score_list.map(item => item.isOpen = true)
          this.MaintainData = res.data

        }
      })
    },
    // 子组件点击房源相册
    foldTitlePhoto(num, idx) {
      if (num.id == 3) {
        this.clickTab(num)
        this.houseScoreVisible = false
      } else if (num.id == 2) {
        this.clickTab(num)
        this.houseScoreVisible = false
        if (idx == 2) {
          this.activeName = 'third'
          this.houseScoreVisible = false
        } else if (idx == 3) {
          this.activeName = 'second'
          this.houseScoreVisible = false
        }
      }
    },
    scollView(selector, num) {
      this.clickTab(num)
      this.houseScoreVisible = false
      this.$nextTick(() => {
        this.$el.querySelector(selector).scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      })
      // setTimeout(() => {
      //   this.$el.querySelector(selector).focus()
      // }, 1000)
      // this.follow_params.effect_type = 4;
      // this.visiblepop_title = "带看预约";
      // this.houseScoreVisible = false
    },
    // 点击房源得分
    houseScoreClick() {
      if (this.tabListID == 1 || this.tabListID == undefined) {
        this.houseScoreVisible = true
      }
    },
    // 点击预约带看
    appointmentLook() {
      this.appointmentVisible = true;
      this.chongzhi()
      // this.$nextTick(() => {
      //   this.$refs.PersonnelList.innerText = ""
      // })
      this.showPersonnelList();
    },
    // 点击@同事
    addColleague() {
      this.showDropdown = true
      if (this.controlVisible) {
        this.$http.getColleagueList().then((res) => {
          if (res.status == 200) {
            console.log(res)
            this.controlVisible = false
            this.departmentMember = res.data
          }
        })
      }
    },
    MemberClick(item) {
      if (this.colleagueID.includes(item.values)) {
        this.$message({
          message: '请勿重复@同事',
          type: 'warning'
        });
        return
      }
      this.colleagueID.push(item.values);
      // 获取输入框的dom
      let sel = this.$refs.gain;
      // 获取点击同事名
      let tpl = `<a 
                    href="javascript:void(0)"
                    class="myTS"
                    name='${item.values}'
                    style='color: #0088ff; text-decoration: none;'
                    contenteditable= 'false'
                  >
                    @${item.name}
                  </a> &nbsp;`
      sel.innerHTML += tpl;
      let range = window.getSelection()
      range.selectAllChildren(sel);
      range.collapseToEnd();
    },
    // 点击房屋实勘
    houseSurvey() {
      this.surveyVisible = true;
    },
    // 房源相册点击新增图片类型
    housePhotoSurvey() {
      this.surveyVisible = true;
      this.showAddPopo = false; // 关闭popover
    },
    FollowTextContent() {
      var tribute = new Tribute({
        values: (text, cb) => {
          this.remoteSearch(text, users => cb(users));
        }
      })
      tribute.attach(document.getElementById('Go'));
    },
    remoteSearch() {
      this.time = true
      if (this.time) {
        this.time = false
        // this.addColleague()
      }
    },
    // 点击带看时间
    appointmentTime(item) {
      this.appointment.take_time = item.id
    },
    // 点击陪看人员下拉列表
    showPersonnelList() {
      this.$http.getColleagueList().then((res) => {
        if (res.status == 200) {
          this.departmentMember = res.data;
        }
      })
    },
    // 点击陪看人员列表
    // personnelListClick(item,index) {
    //   let enter = true;
    //   this.personnel_id.map((arr,idx) => {
    //     if(arr == item.values) {
    //       enter = false
    //       this.$refs.personnel[index].style= '';
    //       this.personnel_list.splice(idx, 1);
    //       this.personnel_id.splice(idx,1);
    //     }
    //   })
    //   if(enter) {
    //     this.personnel_list.push(item.name);
    //     this.personnel_id.push(item.values);
    //     this.$refs.personnel[index].style.background = '#2D84FB';
    //     this.$refs.personnel[index].style.color = '#fff';
    //   }
    //   this.$refs.PersonnelList.innerText = this.personnel_list.toString();
    //   this.appointment.accompany = this.personnel_list.toString();
    // },
    selectDialog() {
      this.selectVisible = true;
      // 客户渠道筛选
      this.$http.getCrmCustomerSourceNopage().then(res => {
        if (res.status == 200) {
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
        }
      })
      this.getDataList()
    },
    // 预约带看-客户选择
    onClickType(e, type) {
      switch (type) {
        case 1:
          this.chooseCustomer.source_id = e.id;
          break;
        default:
          break;
      }
      this.chooseCustomer.page = 1;
      this.getDataList();
    },
    getDataList() {
      if (!this.chooseCustomer.source_id) {
        delete this.chooseCustomer.source_id;
      }
      this.$http
        .getCrmCustomerClientList({ params: this.chooseCustomer })
        .then((res) => {
          if (res.status === 200) {
            this.customerList = res.data.data;
            this.chooseCustomer.page = res.data.current_page;
            this.chooseCustomer.total = res.data.total;
          }
        });
    },
    onChangeTime(e) {
      this.chooseCustomer.start_date = e ? e[0] : "";
      this.chooseCustomer.end_date = e ? e[1] : "";
      this.chooseCustomer.page = 1;
      this.getDataList();
    },
    seachCustomer() {
      this.getDataList();
    },
    customerPageChange(e) {
      this.chooseCustomer.page = e;
      this.getDataList();
    },
    onClickDetail(row) {
      this.appointment.name = row.cname;
      this.appointment.mobile = row.mobile;
      this.selectVisible = false;
    },
    onSubmitAppoint() {
      this.appointment.accompany = this.personnel_list.toString();
      this.appointment.info_id = this.id
      this.$http.addHouseRegister(this.appointment).then(res => {
        if (res.status == 200) {
          this.$message({
            message: '提交成功',
            type: 'success'
          });
          this.appointmentVisible = false;
          this.follow_list_params.page = 1;
          this.getFollowData();
          this.TakeLookRecord_list = []
          this.TakeLook_list_params.page = 1;
          this.getTakeLookRecord();
        }
      }).catch((res) => {
        this.$message.error(res);
      })
    },
    chongzhi() {
      this.appointment.info_id = "";
      this.appointment.name = "";
      this.appointment.mobile = "";
      this.appointment.content = "";
      this.appointment.accompany = "";
      this.appointment.take_date = "";
      this.appointment.take_time = 1;
      this.appointment.take_no = "";
    },
    //复制同事手机号
    copyPhone(e) {
      e.stopPropagation();
      e.preventDefault();
      this.$onCopyValue(this.colleagueDetails.phone)
    },
    appointmentDown() {
      this.personnel_list = [];
      this.personnel_id = [];
      if (this.$refs.personnel != undefined) {
        this.$refs.personnel.forEach((item) => {
          item.style = "";
        })
      }
    },
    // 关闭查看电话回调
    ShowPhoneClose() {
      this.call_id = [];
      this.$set(this.follow_params1, "content", "");
      this.$set(this.follow_params1, "call_phone_id", "");
      this.$set(this.follow_params1, "call_name", "");
      this.$set(this.follow_params1, "call_phone", "");
      this.$set(this.follow_params1, "call_show_phone", "");
    },
    ChangeMyContent() {
      let user = document.querySelectorAll(".myTS");
      let colleagueID = [];
      user.forEach(item => {
        colleagueID.push(Number(item.name));
      })
      this.colleagueID = colleagueID
    },
    recordClick(e) {
      if (e.label == '电话记录' && this.PhoneRecord_list == '') {
        this.getPhoneRecord();
      } else if (e.label == '带看记录' && this.TakeLookRecord_list == '') {
        this.getTakeLookRecord();
      }
    },
    getPhoneRecord() {
      this.Phone_load = false;
      this.$ajax.house.getNewPhoneRecord(this.id, this.Phone_list_params).then(res => {
        if (res.status == 200) {
          this.PhoneRecord_list = this.PhoneRecord_list.concat(res.data.data);
          // console.log(this.PhoneRecord_list)
          if (res.data.data.length == this.Phone_list_params.rows) {
            this.Phone_load = true;
          } else {
            this.Phone_load = false;
          }
        }
      })
    },
    getTakeLookRecord() {
      this.TakeLook_load = false;
      this.$ajax.house.getTakeLookRecord(this.id, this.TakeLook_list_params).then(res => {
        if (res.status == 200) {
          this.TakeLookRecord_list = this.TakeLookRecord_list.concat(res.data.data);
          if (res.data.data.length == this.TakeLook_list_params.rows) {
            this.TakeLook_load = true;
          } else {
            this.TakeLook_load = false;
          }
        } else {
          this.TakeLook_load = false;
        }
      }).catch(() => {
        this.TakeLook_load = false;
      })
    },
    // 添加房屋实勘
    addSurveyList() {
      if (this.survey_form.content == "") {
        return this.$message.error('请输入跟进内容');
      }
      this.is_loading = true; // 开启loading动画
      this.survey_form.images = this.surveyFileList.join(","); // 将数组转换为，分隔字符串
      this.$ajax.house.addFollow(this.survey_form).then(res => {
        if (res.status == 200) {
          this.$message({
            message: '提交成功',
            type: 'success'
          });
          this.$refs.surveyForm.resetFields(); // 将表单参数重置
          this.$refs.surveyUpload.clearFiles(); // 清除上传的图片
          this.surveyFileList = []; // 清空图片url
          this.surveyVisible = false; // 关闭模态框
          this.is_loading = false; // 关闭Laoding动画
          this.getFollowData(); // 获取最新跟进数据
          // 如果当前的在房源相册页面
          if (this.tabListID == 2) {
            this.TimeLine_picList_photos = []; // 清空房源相册
            this.getHousePicture(); // 请求最新房源相册列表数据
          }
        } else {
          this.is_loading = false;
        }
      })
    },
    // 点击添加图片
    addPictures() {
      // console.log(this.FollowImgList.length,"length")
      // 判断图片列表的个数
      if (this.FollowImgList.length == 5) {
        this.disabled_picture = true; // 禁用上传
        this.$message({
          message: '最多只能上传5张图片',
          type: 'warning'
        });
      } else {
        this.disabled_picture = false; // 启用上传
      }
    },
    // 上传图片成功的回调函数
    UploadParamsSuccess(e) {
      console.log(e, "上传成功");
      this.FollowImgList.unshift(e);
      // console.log(this.FollowImgList,"this.FollowImgList");
    },
    // 删除已上传的图片
    deletePicture(index) {
      this.FollowImgList.splice(index, 1);
    },
    // 查看已上传的图片
    handlePictureCardPreview(item) {
      this.show_dialog_pictures = true;
      if (item.url) {
        this.dialog_pictures_src = item.url;
      } else {
        this.dialog_pictures_src = item;
      }
    },
    // 跟进记录点赞
    setFollowPraise(val) {
      // console.log(val);
      this.$ajax.house.addFollowPraise({ follow_id: val.id }).then((res) => {
        if (res.status == 200) {
          // this.$message({
          //   message: res.data.msg,
          //   type: 'success'
          // });
          this.follow_list_params.page = 1;
          this.getFollowData();
        }
      })
    },
    // 跟进记录内容复制
    onCopyValues(val) {
      // 将所有 <span> 标签替换为指定格式
      const convertedString = val.content.replace(/<span[^>]*>(@.*?)<\/span>/g, '$1');
      this.$onCopyValue(convertedString);
    },
    // 设置/取消置顶
    setFollowTop(val) {
      if (val.is_top == 0) {
        this.$ajax.house.changeTopButton(val.id, 'top').then(res => {
          if (res.status == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.follow_list_params.page = 1;
            this.getFollowData();
          }
        })
      } else if (val.is_top == 1) {
        this.$ajax.house.changeTopButton(val.id, 'cancel_top').then(res => {
          if (res.status == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.follow_list_params.page = 1;
            this.getFollowData();
          }
        })
      }
    },
    // 跟进记录删除单个图片
    followDelPicture(val, item) {
      if (val && item) {
        this.$confirm('此操作将永久删除该图片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delPic_parame.id = val.id;
          this.delPic_parame.pic = item;
          this.$ajax.house.deleteFollowPraise(this.delPic_parame).then((res) => {
            if (res.status == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.follow_list_params.page = 1;
              this.getFollowData();
            }
          })
        }).catch(() => {
          return;
        });
      }
    },
    // 实勘图审批
    surveyApprove(e, id = 15) {
      // console.log(e,"成员信息");
      this.audit_form = { cat_id: id, follow_id: e.id }; // 赋值审批类型id
      this.attenchmentList = []; // 清空上传的图片路径
      // this.auditPersonSelect = []; // 清空审批人列表
      // this.selectedAuditIds = []; // 清空部门成员列表
      this.keyAuditList = []; // 清空审批类型列表
      this.alreadyUploadList = []; // 清空上传的文件列表
      this.getAuditTypeList(); // 获取审批类型列表
      if (!this.memberList.length) {
        this.getDepartment(); // 获取部门成员列表
      }
      e.list.map((item) => {
        this.alreadyUploadList.push(item); // 赋值上传的文件列表
        this.attenchmentList.push(item.url); // 赋值图片路径
      })
      // console.log(this.alreadyUploadList,"alreadyUploadList")

      this.getModelList(id); // 获取审批类型的输入栏
      this.audit_form.f_role_ori = ["skr"]; // 赋值变更角色为实勘人
      this.audit_form.f_role_new = e.crm_user_id; // 赋值变更为 e.crm_user_id:当前房源相册上传人id
      this.show_audit = true; // 显示提交审批模态框
      return;
    },
    // 处理选中后页面不更新问题
    handlerChangeSelect() {
      this.$forceUpdate();
    },
    // 删除房源相册
    deleteHousePhoto(firstItm, item) {
      // console.log(firstItm,"firstItm",item,"item");
      if (firstItm.id && item) {
        this.$confirm('此操作将永久删除该图片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delPic_parame.id = firstItm.id; // 赋值跟进记录的id
          this.delPic_parame.pic = item.url; // 赋值要删除图片的url
          this.$ajax.house.deleteFollowPraise(this.delPic_parame).then((res) => {
            if (res.status == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.TimeLine_picList_photos = []; // 清空房源相册
              this.getHousePicture(); // 获取最新房源相册列表
              // 重置删除图片接口参数
              this.delPic_parame = {
                id: '', // id
                pic: '' // 图片URL地址
              }
            }
          })
        }).catch(() => {
          return;
        });
      }
    },
    // 房屋实勘图片上传成功回调函数
    onUploadSurveySuccess(options = {}) {
      console.log(options, "上传成功");
      let { response } = options;
      this.surveyFileList.push(response.url);
    },
    // 导出房源日志
    exportLogs() {
      console.log(this.is_online, "is_online");
      this.is_loading = true; // 开启loading
      this.$set(this.log_params, "export", 1); // export = 1 导出房源日志
      this.getLog(); // 通过房源日志接口导出excel
    },
    exportMakeLogs() {
      this.is_loading = true;
      this.$ajax.house.exportMakeData(this.id).then(res => {
        if (res.status == 200) {
          this.downloadFile(res.data);
          this.is_loading = false; // 取消loading
        } else {
          this.is_loading = false;
        }
      }).catch(() => {
        this.is_loading = false;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.operator {
  width: 105px;
  display: flex;
  height: 60px;
  justify-content: space-between;

  .operator_left {
    .surName {
      position: relative;
      width: 35px;
      height: 35px;
      top: 11px;
      border-radius: 50%;
      background-color: #2d84fb;

      span {
        position: absolute;
        top: 5px;
        left: 11px;
        color: #fff;
      }
    }
  }

  .operator_right {
    display: flex;
    width: 70px;
    flex-direction: column;
    align-items: center;
    height: 60px;
    justify-content: center;
  }
}

.vrTable {
  .agent_info {
    margin: -15px 0 24px 0;

    .time {
      margin-right: 10px;
      color: #8a929f;
    }

    .img {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .agent_name {
      font-size: 12px;
      color: #8a929f;
    }
  }
}

.tab_tops {
  .tabHead {
    margin-left: 20px;
    margin-top: 20px;

    /deep/.el-tabs__nav-wrap::after {
      background-color: transparent !important;
    }
  }
}

::v-deep.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;

  .house_score {
    color: #1dbfff;
    cursor: pointer;
  }

  .house_dealTime {
    color: #da4c44;
  }
}

.label {
  font-size: 18px;
  color: #2e3c4e;
  font-weight: 500;
  padding-left: 8px;
  border-left: 4px solid #2d84fb;
}

.title {
  font-family: PingFangSC-Medium;
  padding: 0 0 24px 0;
  font-size: 18px;
  color: #2e3c4e;
  font-weight: 500;

  &.padt24 {
    padding-top: 24px;
  }

  // background: #f1f4fa 100%;

  .id {
    padding: 0 8px;
  }

  .line {
    width: 2px;
    background: #333;
    height: 20px;
    margin: 0 5px;
  }

  .title_con {
    padding: 0 8px;
    // border-right: 2px solid #2e3c4e;
  }
}

.el-timeline-item {
  padding: 20px 0;
}

.paperview-input-text {
  line-height: 1;

  ::v-deep .el-input__inner {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 0px;
    width: 100%;
  }

  ::v-deep .el-textarea__inner {
    border: 1px solid #fff;
    padding: 0;
  }
}

.tabs {
  background: #f1f4fa 100%;

  .tab {
    &.active {
      color: #2d84fb;
    }

    &.tab_right {

      // .tab_right_sp {
      // }
      ::v-deep .el-upload--picture-card {
        width: auto;
        height: auto;
        line-height: 1;
        border: 0;
        margin-right: 5px;
      }
    }

    cursor: pointer;
    color: #999999;
    font-size: 16px;
    font-weight: 500;
    background: #fff;
    margin-right: 1px;
    padding: 12px 24px;
  }
}

.con {
  background: #fff;
}

.left-content {
  margin: 24px 0;

  .avatar {
    width: 150px;
    min-width: 150px;
    height: 130px;
    overflow: hidden;
    border-radius: 8px;
    position: relative;

    .indicator {
      position: absolute;
      z-index: 8;
      bottom: 12px;
      right: 12px;
      line-height: 1;
      padding: 4px 10px;
      border-radius: 10px;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
    }

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }

    .cover_deal {
      width: 60px;
      height: 60px;
      position: absolute;
      top: 0px;
      right: 0px;
    }
  }

  .detail-box {
    margin-left: 24px;

    .name {
      .n {
        font-size: 24px;
        color: #2e3c4e;
        font-weight: 500;
      }
    }

    .wh {
      margin-left: 12px;
      align-items: center;
      cursor: pointer;
      white-space: nowrap;
      font-size: 13px;
      color: #8a929f;

      img {
        width: 16px;
        height: 14px;
        margin-right: 10px;
      }
    }

    .bind-box {
      margin-top: 13px;
      font-size: 14px;
      color: #2e3c4e;

      .b-n {
        margin-right: 24px;
        margin-top: 5px;
        font-size: 14px;
        display: flex;
        align-items: center;

        .icon {
          margin-left: 5px;
          cursor: pointer;
        }
      }
    }

    .label-box {
      margin-top: 20px;

      .type {
        padding: 7px 18px 7px 15px;
        white-space: nowrap;
        border-radius: 4px;
        margin-right: 10px;

        &.jujiao {
          color: #fb1d15;
          background: rgba(251, 29, 21, 0.2);
        }

        &.weituo {
          background: #dbe8fa;
          color: #2d84fb;
        }

        &.yaoshi {
          background: #dbe8fa;
          color: #2d84fb;
        }

        &.vip {
          background: #3a3f53;
          color: #f3c840;
        }

        .img {
          width: 18px;
          height: 18px;
          margin-right: 5px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .type_name {
          font-size: 12px;
        }
      }

      .lab {
        padding: 6px 12px;
        color: #fff;
        border-radius: 2px;
        font-size: 14px;
        white-space: nowrap;
        margin-right: 24px;

        &.level {
          margin-right: 12px;
          font-size: 12px;
        }

        &.left {
          background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
        }

        &.right {
          background: #2d84fb;
          cursor: pointer;
        }

        &.status {
          margin-right: 12px;
          font-size: 12px;

          &.status9 {
            background: #2d84fb;
            color: #fff;
          }

          &.status8 {
            background: #f1f4fb;
            color: #dee1ea;
          }

          &.status5 {
            background: #f85d02;
            color: #fff;
          }

          &.status102 {
            background: #f2f7fd;
            color: #a7c9ed;
          }
        }
      }
    }

    .desc-box {
      font-size: 14px;
      color: #8a929f;
      margin-top: 24px;
      align-items: center;

      span {
        margin-right: 24px;
      }

      .ovel-1 {
        overflow: hidden; // 溢出部分隐藏
        white-space: nowrap; // 文字不换行
        text-overflow: ellipsis; // 显示省略号
      }
    }
  }
}

::v-deep.label-box {
  // margin-top: 25px;
  margin: 12px 0 12px;

  & .label-item:last-child {
    margin-right: 0;
    padding: 3px 11px;
  }

  .type {
    padding: 7px 18px 7px 15px;
    white-space: nowrap;
    border-radius: 4px;
    margin-right: 10px;

    &.jujiao {
      color: #fb1d15;
      background: rgba(251, 29, 21, 0.2);
    }

    &.weituo {
      background: #dbe8fa;
      color: #2d84fb;
    }

    &.yaoshi {
      background: #dbe8fa;
      color: #2d84fb;
    }

    &.vip {
      background: #3a3f53;
      color: #f3c840;
    }

    .img {
      width: 18px;
      height: 18px;
      margin-right: 5px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .type_name {
      font-size: 12px;
    }
  }

  .lab {
    padding: 6px 12px;
    color: #fff;
    border-radius: 2px;
    font-size: 14px;
    white-space: nowrap;
    margin-right: 24px;

    &.level {
      margin-right: 12px;
      font-size: 12px;
      line-height: 20px;
    }

    &.left {
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
    }

    &.right {
      background: #2d84fb;
      cursor: pointer;
    }

    &.status {
      margin-right: 12px;
      font-size: 12px;
      line-height: 20px;

      &.status9 {
        background: #2d84fb;
        color: #fff;
      }

      &.status8 {
        background: #f1f4fb;
        color: #dee1ea;
      }

      &.status5 {
        background: #f85d02;
        color: #fff;
      }

      &.status102 {
        background: #f2f7fd;
        color: #a7c9ed;
      }
    }
  }
}

// .follow-item {
//   // background: #f1f4fa;
//   border-radius: 4px;
//   padding: 5px 22px;
//   font-size: 16px;
//   margin-right: 24px;
//   cursor: pointer;
//   // color: #8a929f;
// }
.right-content {
  // margin-left: 60px;
  padding: 44px 12px 24px 12px;

  .name-box {
    margin-bottom: 12px;

    // align-items: center;
    .name-box-img {
      width: 100px;
      height: 40px;
      margin-right: 24px;
    }

    span {
      display: inline-block;
      font-size: 13px;
      padding-bottom: 0;
      color: #8a929f;

      &.mr30 {
        margin-right: 30px;
      }
    }

    .btn {
      border: 1px solid rgba(138, 146, 159, 1);
      font-size: 16px;
      color: #8a929f;
      padding: 9px 15px;
      border-radius: 2px;
      margin-right: 10px;
      cursor: pointer;
      align-items: center;

      &.b-tel {
        background: #2d84fb;
        border: 1px solid #2d84fb;
        border: none;
        min-width: 80px;
        white-space: nowrap;
        color: #fff;
        // margin-bottom: 5px;
        font-size: 12px;
        box-sizing: border-box;

        &.hidden {
          visibility: hidden;
        }
      }
    }
  }

  .name-box-title {
    justify-content: space-between;
    // align-items: center;
    margin-top: 0;

    .name-box-tem {
      align-items: center;
      cursor: pointer;

      .btn-row {
        color: #8a929f;
        font-size: 13px;
      }

      .row-label {
        color: #8a929f;
        margin-right: 0;
        font-size: 14px;
        padding: 0 10px;
      }
    }

    .title {
      font-size: 16px;
      color: #2e3c4e;
      font-weight: 500;
    }
  }

  .follow-box {
    margin: 24px 0;
    align-items: center;

    span {
      margin-right: 24px;
      color: #8a929f;
      font-size: 16px;
    }

    .follow-item {
      // background: #f1f4fa;
      border-radius: 4px;
      padding: 5px 22px;
      font-size: 16px;
      margin-right: 24px;
      cursor: pointer;
      // color: #8a929f;、
    }
  }

  .follow-input {
    width: 100%;
    padding: 17px 23px;
    border: 1px solid rgba(221, 225, 233, 1);
    border-radius: 4px;
    margin-bottom: 10px;
    position: relative;

    .popover {
      position: absolute;
      padding: 8px 10px;
      background: rgba(34, 34, 34, 0.8);
      border-radius: 4px;
      pointer-events: none; //防止穿透
      z-index: 220;
      color: #fff;
      top: -50px;
      left: 50%;
      transform: translateX(-50%);

      .triangle {
        position: absolute;
        display: block;
        left: 50%;
        transform: translateX(-40%);
        bottom: -10px;
        width: 0px;
        height: 0px;
        content: "";
        border-color: transparent;
        border-style: solid;
        border-width: 6px;
        border-top: 6px solid rgba(34, 34, 34, 0.8);
        z-index: 220;
      }
    }

    .f-r {
      margin-left: 12px;
      width: 100%;
      align-items: flex-start;

      .paperview-input-text {
        width: 100%;
        height: 100%;
        outline: none;
        margin-top: 5px;
        cursor: text;
        max-width: 537px;
      }

      .paperview-input-text:empty::before {
        color: lightgrey;
        content: attr(placeholder);
      }

      // .el-button {
      // }
    }
  }
}

.imgList {
  flex-wrap: wrap;
  width: 100%;
  min-height: calc(100vh - 325px);
  padding: 20px;

  .img_item {
    position: relative;
    margin-right: 10px;
    margin-bottom: 10px;
    // overflow: hidden;

    .img {
      width: 250px;
      height: 220px;
      border-radius: 4px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &:hover {
        .opers {
          display: block;
          z-index: 2;
        }

        .mask {
          display: block;
          z-index: 1;
        }

        .hover-delete-Photo {
          display: block;
          z-index: 1;
        }
      }

      .mask {
        display: none;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;

        background: rgba(0, 0, 0, 0.4);
      }

      .opers {
        position: absolute;
        display: none;
        top: calc(50% - 40px);
        left: calc(50% - 50px);
        width: 100px;

        .change_type {
          margin-top: 10px;
          cursor: pointer;
          color: #fff;
          padding: 5px 0;
          border-radius: 5px;
          border: 1px solid #fff;
        }

        .preview {
          cursor: pointer;
          text-align: center;
        }

        // margin: auto;
      }

      .hover-delete-Photo {
        display: none;
        padding: 10px 0;
        color: #ffffff;
        position: absolute;
        bottom: 0px;
        left: calc(50% - 13px);
        font-size: 26px;
        cursor: pointer;
      }
    }

    .desc {
      background: #f8f8f8;
      padding: 10px 5px;
      box-sizing: border-box;
      text-align: center;
      width: 320px;

      div {
        line-height: 1.7;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .type {
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0px 2px 0px;
      position: absolute;
      left: 0px;
      top: 0px;
      color: #fff;
      padding: 5px 10px;
    }

    .del {
      background: rgba(0, 0, 0, 0.6);
      border-radius: 4px 0px 2px 0px;
      position: absolute;
      right: 0px;
      top: 0px;
      cursor: pointer;
      color: #f00;
      padding: 5px 10px;
      z-index: 2;
    }
  }
}

.mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 60px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;

  .save {
    position: fixed;
    top: 115px;
    color: #fff;
    background: #3a3f53;
    border-radius: 5px;
    font-size: 14px;
    padding: 7px 20px;
    right: 80px;
    cursor: pointer;
  }

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
    }

    // top: 100px;
  }

  .close_pre {
    position: fixed;
    top: 110px;
    right: 20px;
    background: #3a3f53;
    // color: #999999;
    color: #fff;
    padding: 10px 20px;
    border-radius: 50%;
    padding: 10px;
    font-size: 20px;
    // color: #333;
    // background: #eee;
    cursor: pointer;

    span {
      display: inline-block;
      width: 18px;
      line-height: 18px;
      text-align: center;
      height: 18px;
    }
  }
}

.preview_img {
  position: relative;
  // height: 70vh;
  // overflow: auto;

  .save {
    display: none;
    position: absolute;
    top: -70px;
    color: #2d84fb;
    font-size: 18px;
    padding: 5px 10px;
    right: 30px;
    cursor: pointer;
  }

  img {
    // width: 100%;
    // height: 100%;
    object-fit: cover;
  }

  .img {
    max-width: 800px;
    height: 600px;
    overflow-y: auto;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .pre {
    position: fixed;
    // left: 20px;
    left: 251px;
    top: 50%;
    padding: 10px;
    font-size: 20px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.4);
    color: #fff;
  }

  .next {
    position: fixed;
    right: 20px;
    top: 50%;
    padding: 10px;
    font-size: 20px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.4);
    color: #fff;
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  }

  .contextmenu li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    white-space: nowrap;
  }

  .contextmenu li:hover {
    background: #eee;
  }
}

.up_img {
  padding: 10px 20px;
}

.isborder {
  animation: glow 800ms ease-out infinite alternate;
}

@keyframes glow {
  0% {
    border-color: #2d84fb;
    box-shadow: 0 0 5px #2d84fb, inset 0 0 5px rgba(221, 225, 233, 1),
      0 1px 0 #2d84fb;
  }

  100% {
    border-color: rgba(221, 225, 233, 1);
    box-shadow: 0 0 20px #2d84fb, inset 0 0 10px rgba(221, 225, 233, 1),
      0 1px 0 #2d84fb;
  }
}

.shares_title {
  padding: 10px 5px;

  .share_line {
    width: 5px;
    height: 30px;
    background: #2d84fb;
  }

  .share_title {
    margin-left: 20px;
    font-size: 18px;
    font-weight: bold;
  }
}

.f-list {
  cursor: pointer;

  &.f-list1 {
    flex-wrap: wrap;
    display: flex;

    .f-items {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 150px;
      height: 150px;

      .share_img {
        width: 48px;
        height: 48px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .share_name {
        margin-top: 15px;
        font-size: 14px;
        color: #333;
      }

      .share_desc {
        margin-top: 15px;
        font-size: 12px;
        color: #666;
      }
    }

    .f-item {
      width: 58px;
      text-align: center;
    }
  }

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.f-items1 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 144px;
  height: 115px;
  cursor: pointer;

  .share_img {
    width: 48px;
    height: 48px;
    position: relative;

    // overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .share_tip {
      position: absolute;
      top: -7px;
      right: -20px;
      background: #ff3d3d;
      padding: 3px 9px;
      border-radius: 12px;
      color: #fff;
      font-size: 10px;
    }
  }

  .share_name {
    margin-top: 15px;
    font-size: 14px;
    color: #333;
  }

  .share_desc {
    margin-top: 15px;
    font-size: 12px;
    color: #666;
  }
}

.follow-record {

  // ==========
  .el-timeline {
    .el-timeline-item:hover {
      .el-timeline-item__wrapper {
        .el-timeline-item__content {
          .agent_info {
            .follow_info_box {
              display: flex;
            }
          }
        }
      }
    }
  }

  // ==========
  // position: relative;
  .agent_info {
    height: 25px;
    margin: -15px 0 24px 0;

    .time {
      margin-right: 10px;
      color: #8a929f;
    }

    .img {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .agent_name {
      font-size: 12px;
      color: #8a929f;
    }

    .show_is_top {
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
      color: #ffffff;
      padding: 2px 8px;
      border-radius: 4px;
      margin-left: 20px;
    }

    .follow_info_box {
      display: none;
      flex-direction: row;
      border: 1px solid #8a929f;
      border-radius: 3px;
      margin-left: 20px;

      .follow_info_praise,
      .follow_info_copy {
        width: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        text-align: center;
        padding: 6px;
        color: #8f959e;
        border-right: 1px solid #8f959e;
        cursor: pointer;
      }

      .follow_info_praise:active {
        background-color: #eff0f1;
      }

      .follow_info_copy:active {
        background-color: #eff0f1;
      }

      .follow_add_top {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #8f959e;
        padding: 6px;
        cursor: pointer;
      }

      .follow_add_top:active {
        background-color: #eff0f1;
      }
    }
  }

  .FollowText {
    display: flex;
    // width: 560px;
  }

  .follow-picture {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 13px;

    .follow-picture-box {
      width: 60px;
      height: 60px;
      margin-right: 10px;
      margin-bottom: 5px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .uploader-actions {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        border-radius: 4px;
        cursor: default;
        text-align: center;
        color: #fff;
        opacity: 0;
        font-size: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s;

        .uploader-actions-item {
          font-size: 20px;
          cursor: pointer;

          & i {
            color: #fff;
          }
        }
      }

      .uploader-actions:hover {
        opacity: 1;
      }

      .follow-delete-picture {
        display: none;
        position: absolute;
        top: -7px;
        right: -8px;
        cursor: pointer;
      }
    }

    .follow-picture-box:hover .follow-delete-picture {
      display: block;
    }
  }

  .follow-praise {
    display: inline-block;
    border-radius: 15px;
    color: #8a929f;
    background-color: #f1f4fa;
    padding: 5px 12px;
    box-sizing: border-box;
    margin-right: 20px;
    margin-top: 13px;

    .follow-praise-box {
      display: flex;
      flex-direction: row;
      align-items: center;

      .follow-praise-img {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 19px;
          height: 19px;
        }
      }

      .follow-praise-separate {
        width: 1px;
        height: 14px;
        background-color: #8a929f;
        margin: 0 5px;
        opacity: 0.5;
      }

      .follow-praise-text {
        padding: 0 5px;
      }
    }
  }

  .f_content {
    display: flex;
    align-items: center;
    max-width: 450px;

    // margin-bottom: 20px;
    .recode_Time {
      color: #409eff;
      margin-left: 5px;
    }

    &.red {
      color: #fc0606;
    }

    // & span {
    //   display: flex;
    //   align-items: center;
    // }
  }

  .infoFrame {
    display: none;
    width: 200px;
    position: fixed;
    top: 0px;
    left: 0px;
    background: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;
    border-radius: 7px;
    z-index: 2;

    .infoAngle {
      position: absolute;
      top: 74px;
      left: 95px;
      width: 0px;
      height: 0px;
      color: #fff;
    }

    .infoFrame-box {
      display: flex;
      padding: 10px;

      .infoFrame_icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #2d84fb;
        color: #fff;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 5px 12px 0;
      }

      .infoFrame-info {
        display: flex;
        flex-direction: column;
        color: #303133;

        & span {
          font-size: 12px;
          margin-top: 5px;
        }

        .infoFrame-phone {
          display: flex;
          align-items: center;

          .numberTop {
            background: #afb5b42e;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            margin-left: 5px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.oper {
  padding: 10px;
}

.weituo_a {
  // height: calc(100vh - 275px);
  // position: relative;
  // overflow-y: auto;
  padding-top: 20px;
  padding-left: 24px;

  .weituo_key {
    padding-bottom: 20px;
    border-bottom: 1px solid #f3f3f3;

    &:first-child {
      display: none;
    }

    .title {
      padding-top: 20px;
    }
  }

  // .key_button {
  //   margin-bottom: 10px;
  // }
}

.upload-box {
  overflow: hidden;
  margin: 0 8px 8px 0;
  display: inline-block;
  vertical-align: middle;
}

.el-upload-list--picture-card {
  display: flex;
  flex-wrap: wrap;

  .el-upload-list__item-thumbnail {
    object-fit: cover;
  }
}

.search_container {
  position: absolute;
  top: 30%;
  left: 24px;

  >.left {
    margin-right: 12px;
    display: inline-block;
    padding: 12px;
    background-color: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

    >.item {
      padding: 9px 0;
      text-align: center;
      color: #999999;
      cursor: pointer;

      &.active {
        color: #2d84fb;
      }

      img {
        width: 24px;
        height: 24px;
        margin-bottom: 2px;
      }

      p {
        font-size: 12px;
      }
    }
  }

  >.right {
    padding: 12px;
    width: 280px;
    height: 345px;
    background-color: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border-radius: 4px;

    >.title {
      padding-bottom: 10px;
    }

    .close {
      padding: 10px;
      cursor: pointer;
    }

    >.marker_list {
      overflow-x: hidden;

      .item {
        padding: 10px 0;
        cursor: pointer;

        .num {
          width: 20px;
          text-align: center;
          height: 20px;
          line-height: 20px;
          margin-right: 10px;
          border-radius: 50%;
          display: inline-block;
          font-size: 12px;
          color: #ff8021;
          background: rgba(255, 128, 33, 0.15);
        }

        .title {
          font-size: 14px;
          padding-bottom: 0;
        }

        .distance {
          font-size: 13px;
          color: #999999;
        }
      }
    }
  }
}

.map {
  height: calc(100vh - 275px);
  position: relative;
}

.agent_info {
  .agent_header {
    padding: 5px;
    color: #fff;
    width: 33px;
    height: 33px;
    background: #2d84fb;
    font-size: 14px;
    border-radius: 50%;
    margin-right: 10px;
  }

  // .gent_inf_con {
  // }
}

.content-box-crm .table-top-box {
  margin-bottom: 28px;
}

.label-invalid {
  color: #8a929f;
  margin-bottom: 12px;
}

.lab_list {
  max-height: 480px;
  overflow-y: auto;

  .labelname {
    margin-bottom: 10px;
  }

  .lables-box {
    flex-wrap: wrap;
    flex: 1;

    .labels-item {
      margin-bottom: 10px;
      cursor: pointer;
      background: #f1f4fa;
      border-radius: 4px;
      padding: 5px 22px;
      min-width: 80px;
      font-size: 16px;
      border: 1px solid #f1f4fa;
      text-align: center;
      margin-right: 24px;
      color: #8a929f;

      &.checked {
        background: rgba(45, 132, 251, 0.15);
        border: 1px solid rgba(45, 132, 251, 1);
        color: #2d84fb;
      }
    }
  }
}

.theme-list {
  margin-top: 12px;
  cursor: pointer;
}

// .fixed-nav {
// overflow-x: scroll;
// -webkit-overflow-scrolling: touch;
// }
.fixed-nav-content {
  display: flex;
}

.tab-title {
  padding: 14px 0;
  margin-right: 20px;
  flex-shrink: 0;
  font-size: 14px;
  color: #8a929f 100%;
  position: relative;

  &.select-tab {
    color: #2d84fb;

    &::after {
      content: "";
      position: absolute;
      bottom: -0;
      left: 30%;
      width: 24px;
      background: #2d84fb;
      height: 2px;
    }
  }
}

.date_name {
  padding: 4px 12px;
  font-weight: 500;
}

.date_value {
  font-size: 11px;
}

.t_time {
  flex-wrap: wrap;
  margin-top: 24px;
  cursor: pointer;

  .t_time_item {
    padding: 4px 6px;
    background: #f8f8f8;
    border-radius: 2px;
    font-size: 11px;
    color: #8a929f;
    margin-right: 8px;
    margin-bottom: 12px;

    &.active {
      color: #2d84fb;
    }
  }
}

.padd10 {
  padding: 10px 0 40px;
}

.up_qrcode {
  .min_img {
    width: 160px;
    height: 160px;
    margin: 0 auto;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .up_tip {
    font-size: 12px;

    &.cancel {
      margin-top: 10px;
      color: #2d84fb;
      cursor: pointer;
    }
  }
}

.img_list {
  min-height: 400px;
  max-height: 500px;
  overflow-y: auto;

  .img_item {
    width: 210px;
    height: 210px;
    margin-right: 15px;
    margin-bottom: 10px;
    overflow: hidden;

    &:nth-child(3n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.house_send {
  padding: 12px 24px;

  .tips {
    padding: 12px 0;
  }

  .site_list {
    .site_item {
      background: #fff;
      position: relative;
      border: 1px solid rgba(221, 225, 233, 1);
      box-shadow: 0 0 4px 0 rgba(221, 225, 233, 0.2);
      border-radius: 4px;
      margin-bottom: 12px;
      padding: 15px 12px;

      .img {
        width: 160px;
        height: 30px;
        color: #f0f;
        border-radius: 3px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #2e3c4e;
      }

      .status {
        font-size: 14px;
        color: #2d84fb;

        &.status1 {
          color: #8a929f;
        }
      }

      .checked {
        position: absolute;
        right: 0;
        top: 0;
        width: 16px;
        height: 12px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .service {
        .open_status {
          color: #dbe8fa;

          &.is_open {
            color: #2d84fb;
          }
        }
      }
    }
  }

  .btns {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 24px;
    background: #fff;
  }
}

.setting {
  padding: 10px 24px;

  .setting_title {
    font-size: 16px;
    padding: 15px 0;
  }

  .setting_content {

    // .form_item_row{
    //   padding: 5px 0;
    //   border-bottom: 1px solid #e9e9e9;
    // }
    .ml15 {
      margin-left: 15px;
    }
  }
}

.member {
  height: 500px;
  overflow-y: auto;

  .left,
  .right {
    padding: 10px;
    border-top: 1px solid #e9e9e9;
  }

  .left {
    border-right: 1px solid #e9e9e9;
  }

  .select_title {
    font-size: 16px;
    font-weight: 600;
    color: #666;
  }

  .selected_list {
    padding: 10px 0;

    .selected_item {
      padding: 5px 10px;
      color: #2e3c4e;
      font-size: 14px;

      .delete {
        font-size: 22px;
        cursor: pointer;
      }

      .name {
        color: #2e3c4e;
        font-size: 14px;
      }
    }
  }
}

.audit_person {
  .audit_person_name {
    padding: 0 10px;
    margin-right: 5px;
    border-radius: 4px;
    border: 1px solid #999;

    &.audit_person_add {
      cursor: pointer;
      margin-right: 10px;
    }
  }
}

.douyin_info {
  border: 1px solid #f8f8f8;
  border-radius: 10px;

  .douyin_left {
    border-right: 1px solid #f8f8f8;

    .douyin_type {
      padding: 10px 40px;
      cursor: pointer;

      &.active {
        color: #2d84fb;
      }
    }
  }

  .douyin_right {
    flex: 1;
    padding: 10px;

    .up_type {
      ::v-deep .el-upload--picture-card {
        width: auto;
        height: auto;
        line-height: 1;
        border: 0;
        margin-right: 5px;
      }
    }

    .sucai_list {
      margin-top: 10px;

      .img_list {
        flex-wrap: wrap;
        padding: 10px;

        .douyin_img {
          width: 150px;
          height: 150px;
          position: relative;
          margin-right: 10px;
          margin-bottom: 10px;
          border-radius: 5px;
          overflow: hidden;

          .img_check {
            position: absolute;
            left: 5px;
            top: 5px;
            width: 24px;
            height: 24px;
            border-radius: 24px;
            background: #999;
            border: 1px solid #fff;
            justify-content: center;

            img {
              display: none;
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            &.checked {
              img {
                display: block;
              }
            }
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
}

.tab_right ::v-deep .el-upload--picture-card {
  line-height: 1;
  border: 0;
}

.qrcode_left {
  width: 200px;
  height: 200px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.douyin_bottom {
  font-size: 18px;
  font-weight: 600;
  margin-top: 25px;

  .douyin_logo {
    width: 70px;
    margin: 0 20px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .saoyisao {
    color: #67a6fc;
  }
}

.douyin_line {
  width: 1px;
  height: 200px;
  background: #f6f6f6;
}

.dialog_border ::v-deep .el-dialog {
  border-radius: 10px;
}

.OwnerInfo {
  margin-bottom: 10px;

  .el-button--mini {
    padding: 4px 8px;
    margin-left: 5px;
  }
}

::v-deep .FollowRecord {
  display: flex !important;

  .TopIcon {
    color: #ffffff;
    background: linear-gradient(180deg, #fe4727d1, #fe4727);
    padding: 3px 5px;
    margin-left: 5px;
    font-size: 12px;
  }
}

.TimeLineRecord {
  display: flex;

  .TimeLine_info {
    margin: -15px 0 8px 0;

    .maintainer-name {
      color: #000000;
      font-weight: regular;
      font-size: 16px;
      line-height: normal;
    }

    .maintainer-label {
      height: 18px;
      line-height: 18px;
      border-radius: 2px;
      border: 0.5px solid #2d84fb;
      margin-left: 10px;
      color: #2d84fb;
      font-size: 14px;
      padding: 0px 3px;
    }

    .maintainer-approve {
      margin-left: 10px;

      .el-button {
        font-size: 14px;
        padding: 3px 9px;
      }
    }

    .maintainer-first {
      height: 16px;
      padding: 1px 3px;
      margin-left: 10px;
      border-radius: 2px;
      background: #ff7d7d;
      color: #ffffff;
      font-size: 14px;
      line-height: 16px;
    }
  }

  .TimeLine_time {
    color: #8a929f;
    font-size: 14px;
    margin-bottom: 20px;
  }
}

.houseFollowBtn {
  display: flex;
  margin: 0 0 24px 0px;

  .f-l {
    font-size: 16px;
    width: 130px;
    align-items: center;
    color: #8a929f;
    cursor: pointer;

    img {
      width: 16px !important;
      height: 16px;
      margin-left: 12px;
    }
  }

  .el-button {
    padding: 8px 12px;
    color: #8a929f;
    background-color: #f1f4fa;
    border: none;

    &:active {
      color: #fff;
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
    }
  }
}

.colleague-box {
  display: flex;
  width: 240px;
  height: 300px;
  overflow-y: auto;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid #e6e7e8;
  box-sizing: content-box;
  padding: 12px;
  position: absolute;
  z-index: 5;
  border-radius: 7px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .colleague_content {
    display: flex;
    padding: 8px 10px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;

    &:hover {
      background: #f2f2f3;
    }
  }
}

.FriendsPiece,
.addPicture {
  display: inline-block;
  background: #f4f4f5;
  font-size: 14px;
  border: 1px solid #eaeaec;
  border-radius: 5px;
  color: #7a7c7f;
  padding: 3px 10px;
  margin-bottom: 32px;
  cursor: pointer;
}

.FriendsPiece,
.addPicture.addPicture-btn {
  margin-bottom: 0;
}

.addPicture {
  margin-left: 10px;
}

.picture_list_box {
  width: 48px;
  height: 48px;
  border: 1px solid #eaeaec;
  border-radius: 4px;
  background-color: #e2e2e2;
  margin-left: 12px;
  position: relative;

  .photo-item-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .delete-picture {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    z-index: 2;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .uploader-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 4px;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;

    .uploader-actions-item {
      font-size: 20px;
      cursor: pointer;

      & i {
        color: #fff;
      }
    }
  }

  .uploader-actions:hover {
    opacity: 1;
  }
}

.appointment-box {
  display: flex;
  flex-direction: column;
  padding: 0 20px;

  .appointment-phone {
    display: flex;
    margin-bottom: 20px;

    .el-input {
      width: 330px;
    }
  }

  .appointment-title {
    width: 70px;
    height: 40px;
    line-height: 40px;
    text-align: right;
    margin-right: 10px;
  }

  .appointment-customer {
    display: flex;
    margin-bottom: 20px;

    .el-input {
      width: 290px;
    }

    .appointment-select {
      line-height: 40px;
      margin-left: 10px;
      color: #c0c4cc;
      cursor: pointer;
    }
  }

  .appointment-accompany,
  .appointment-content,
  .appointment-date,
  .appointment-time,
  .appointment-formNumber {
    display: flex;
    margin-bottom: 20px;
  }

  .appointment-content {
    .el-textarea {
      width: 330px;

      ::v-deep .el-textarea__inner {
        min-height: 80px !important;
      }
    }
  }

  .appointment-accompany {
    .el-select {
      width: 330px;
      // .el-input {
      //   .el-input__inner {
      //     width: 330px;
      //   }
      // }
    }
  }

  .appointment-date {
    .el-date-editor {
      width: 330px;
    }

    .block {
      position: relative;
      margin-left: auto;

      .el-date-editor {
        width: 100px;

        .el-input__inner {
          border: none;
          height: 20px;
          line-height: 20px;
          cursor: pointer;
          padding: 0;
          color: #c0c4cc;
        }

        .el-input__prefix {
          display: none;
          padding-left: 125px;
          padding-right: 0;
        }

        .el-input__suffix {
          display: none;
        }
      }

      .show_date {
        position: absolute;
        top: 3px;
        left: 90px;
      }
    }
  }

  .appointment-time {
    align-items: center;

    // & span {
    //   margin: auto 0;
    // }
    .time-box {
      display: flex;
      // align-items: center;
      height: 30px;
      background: #f0eff4;
      border: 1px solid #f0eff4;
      border-radius: 6px;
      cursor: pointer;

      .time-boxList {
        background: #f0eff4;
        padding: 4px 42px;
        border: 1px solid #f0eff4;

        & span {
          font-size: 12px;
        }
      }

      .time-active {
        background: #ffffff;
        border: 1px solid #f0eff4;
        border-radius: 6px;
      }
    }
  }

  .appointment-formNumber {
    border-bottom: none;
    margin-bottom: 0px;

    .el-input {
      width: 330px;
    }
  }
}

::v-deep .el-dialog {
  .el-dialog__body {
    padding: 30px 20px !important;

    .houseInformation-box {
      display: flex;
      font-size: 12px;
      color: #2e3c4e;
      margin-bottom: 10px;
    }
  }
}

.el-popover {
  .personnel_list {
    padding: 8px 12px;
    cursor: pointer;
    border: 1px solid #fff;
  }

  & .personnel_list:hover {
    background: #2d84fb;
    color: #ffffff;
  }
}

::v-deep.bottom-border {
  align-items: center;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
  }

  .el-input {
    width: 250px;
  }
}

::v-deep .el-form {
  .el-form-item:nth-child(4) {
    display: block;

    .el-form-item__content {
      .block {
        .el-date-editor {
          width: 260px;
        }
      }
    }
  }

  .el-form-item:nth-child(1) {
    .el-form-item__content {
      .el-input {
        .el-input__inner {
          width: 200px;
        }
      }
    }
  }

  .el-form-item:nth-child(2),
  .el-form-item:nth-child(3) {
    .el-form-item__content {
      .el-input {
        .el-input__inner {
          width: 260px;
        }
      }
    }
  }
}

.pic-uploader {
  position: relative;
}

.pic-uploader .uploader-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
}

.pic-uploader .uploader-actions:hover {
  z-index: 1;
  opacity: 1;
}

.pic-uploader .uploader-actions:after {
  display: inline-block;
  content: "";
  height: 100%;
  vertical-align: middle;
}

.pic-uploader .uploader-actions span {
  cursor: pointer;
}

::v-deep.record_content {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      .el-tabs__nav-scroll {
        .el-tabs__nav {
          .el-tabs__item {
            font-size: 18px;
          }
        }
      }
    }

    .el-tabs__nav-wrap::after {
      height: 0px;
    }
  }
}

::v-deep.codeGate {
  .el-input__inner {
    width: 200px !important;
  }
}

::v-deep .survey {
  height: 560px;
  overflow-y: auto;

  .el-form {
    .el-form-item {
      margin-bottom: 0px;
      padding: 15px 20px;

      .feature {
        display: flex;
        flex-wrap: wrap;
        min-width: 450px;

        .feature_box {
          & span {
            display: inline-block;
            width: 56px;
            height: 20px;
            text-align: center;
            line-height: initial;
            color: #2d84fb;
            border: 1px solid #2d84fb;
            background-color: rgba(45, 132, 251, 0.15);
            padding: 5px 10px;
            border-radius: 3px;
            margin-right: 10px;
          }
        }
      }

      .el-form-item__label {
        color: #8a929f;
      }

      .el-form-item__content {
        .el-textarea {
          width: 330px;

          .el-textarea__inner {
            min-height: 70px !important;
          }
        }
      }
    }

    .el-form-item {
      .el-form-item__label {
        color: #606266;
      }
    }
  }
}

::v-deep .survey_dialog {
  .el-dialog__footer {
    text-align: center;
  }
}

::v-deep .appointment_dialog {
  .el-dialog__footer {
    text-align: center;
  }
}

::v-deep .commission-Width {
  .el-input__inner {
    width: 85px !important;
  }
}

::v-deep .Phone-width {
  .el-input__inner {
    width: 180px !important;
  }
}

::v-deep .houseNameStyle {
  .el-input__inner {
    width: 260px !important;
  }
}
</style>
