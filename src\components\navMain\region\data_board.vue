<template>
    <div class="data_board">
        <div>
            <div>
                <span class="play">活动名称</span>
                <el-select v-model="select" slot="prepend" placeholder="春风里区域拓客" class="data_paly">
                    <!-- <el-option label="餐厅名" value="1"></el-option>
                    <el-option label="订单号" value="2"></el-option>
                    <el-option label="用户电话" value="3"></el-option> -->
                </el-select>
                <el-button type="primary" class="data_paly_btn">活动详情</el-button>
            </div>
            <div class="data_board_chart">
                <div class="board_chart">
                    <div class="board_chart_use">
                        <span class="span">用户画像</span>
                        <span class="span1">总参与人数</span>
                        <span class="span1">2622人</span>
                    </div>
                        <div id="main"></div>
                </div>
                <div class="board_chart">
                    <div class="board_chart_adviser">
                        <div class="board_chart_use">
                            <span class="span">置业顾问推广数</span>
                        </div>
                        <div id="main1"></div>
                    </div>
                </div>
                <div class="board_chart">
                    <div class="board_chart_use">
                        <span class="span">置业顾问报备数</span>
                        <span class="span1">总报备</span>
                        <span class="span1">800人</span>
                    </div>
                    <div id="main2"></div>
                </div>
            </div>
            <div class="data_board_table">
                <div class="data_board_table1">
                    <div class="Customer_Leads">
                        <div><span>客户线索</span></div>
                        <div><el-button type="primary" plain>全部客户</el-button></div>
                    </div>
                    <div class="board_table">
                        <table v-for="(item, index) in list" :key="index" border="1">
                            <tr>
                                <th>{{ item.date }}</th>
                                <td>{{ item.name }}</td>
                                <td>{{ item.adds }}</td>
                                <td>{{ item.addname }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="data_board_table1">
                    <div class="Customer_Leads">
                        <div><span>客户线索</span></div>
                        <div><el-button type="warning" plain>全部客户</el-button></div>
                    </div>
                    <div class="board_table">
                        <table v-for="(item, index) in list1" :key="index" border="1">
                            <tr>
                                <th>{{ item.date }}</th>
                                <td>{{ item.name }}</td>
                                <td>{{ item.adds }}</td>
                                <td>{{ item.addname }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
export default {
    data() {
        return {
            select: '',
            list: [
                { date: "客户姓名", name: "王先生", adds: "李女士", addname: "马先生" },
                { date: "所属置业顾问", name: "曼丽久", adds: "李秀研", addname: "马李园园" },
                { date: "微信昵称", name: "快乐一生", adds: "李女士", addname: "足球先生" },
                { date: "报备手机号", name: "133 9907 8869", adds: "133 9907 8869", addname: "133 9907 8869" },
                { date: "报备日期", name: "2023.03.08", adds: "2023.03.08", addname: "2023.03.08" },
                { date: "是否有效", name: "有效", adds: "有效", addname: "有效" }
            ],
            list1: [
                { date: "微信昵称", name: "快乐一生", adds: "李女士", addname: "足球先生" },
                { date: "红包金额", name: "3.78", adds: "4.99", addname: "29" },
                { date: "客户姓名", name: "王先生", adds: "李女士", addname: "马先生" },
                { date: "报备手机号", name: "133 9907 8869", adds: "133 9907 8869", addname: "133 9907 8869" },
                { date: "发放状态", name: "已发", adds: "已发", addname: "已发" },
                { date: "发放时间", name: "2023.03.08 14:18", adds: "2023.03.08 14:18", addname: "2023.03.08 14:18" },

            ]
        }
    },
    methods: {
        chart() {
            var chartDom = document.getElementById('main');
            var myChart = echarts.init(chartDom);
            var option;
            const chartObserver = new ResizeObserver(() => {
                myChart.resize();
            });
            chartObserver.observe(chartDom);
            option = {
                xAxis: {
                    type: 'category',
                    data: ['龙泉街道', '北辛街道', '善南街道', '荆河街道', '龙泉街道', '北新街道'],
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    }
                },
                yAxis: {
                    type: 'value',
                    show: false,
                },
                series: [
                    {
                        data: [120, 200, 150, 80, 70, 110],
                        type: 'bar',
                        showBackground: true,
                        // showBackground: false,
                        backgroundStyle: {
                            color: 'rgba(45,132,251,0.3)'
                        },
                        itemStyle: {
                            color: "#2D84FB"
                        },
                        barWidth: 26,
                    }
                ]
            };
            option && myChart.setOption(option);
        },
        chart1() {
            var chartDom = document.getElementById('main1');
            var myChart = echarts.init(chartDom);
            var option;
            const chartObserver = new ResizeObserver(() => {
                myChart.resize();
            });
            chartObserver.observe(chartDom);
            option = {
                xAxis: {
                    type: 'category',
                    data: ['曼丽久', '李秀燕', '马芳芳', '李园园', '李玉新', '常泽龙'],
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    }
                },
                yAxis: {
                    type: 'value',
                    show: false,
                },
                series: [
                    {
                        data: [120, 200, 150, 80, 70, 110],
                        type: 'bar',
                        showBackground: false,
                        // showBackground: false,
                        backgroundStyle: {
                            color: 'rgba(45,132,251,0.3)'
                        },
                        itemStyle: {
                            color: "#2D84FB"
                        },
                        barWidth: 26,
                        label: {
                            show: true, //开启显示
                            position: 'top', //在上方显示
                            textStyle: { //数值样式
                                color: 'rgba(138,146,159,1)',
                                fontSize: 14
                            }
                        }
                    }
                ]
            };
            option && myChart.setOption(option);
        },
        chart2() {
            var chartDom = document.getElementById('main2');
            var myChart = echarts.init(chartDom);
            var option;
            const chartObserver = new ResizeObserver(() => {
                myChart.resize();
            });
            chartObserver.observe(chartDom);
            option = {
                xAxis: {
                    type: 'category',
                    data: ['曼丽久', '李秀燕', '马芳芳', '李园园', '李玉新', '常泽龙'],
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    }
                },
                yAxis: {
                    type: 'value',
                    show: false,
                },
                series: [
                    {
                        data: [120, 200, 150, 80, 70, 110],
                        type: 'bar',
                        showBackground: false,
                        // showBackground: false,
                        backgroundStyle: {
                            color: 'rgba(45,132,251,0.3)'
                        },
                        itemStyle: {
                            color: "rgba(254,108,23,1)"
                        },
                        barWidth: 26,
                        label: {
                            show: true, //开启显示
                            position: 'top', //在上方显示
                            textStyle: { //数值样式
                                color: 'rgba(138,146,159,1)',
                                fontSize: 14
                            }
                        }
                    }
                ]
            };
            option && myChart.setOption(option);
        }
    },
    mounted() {
        this.chart()
        this.chart1()
        this.chart2()
    }
}
</script>

<style scoped lang="scss">
.data_board {
    background: #f1f4fa;
    margin: -14px;
    padding: 24px;

    .play {
        color: rgba(46, 60, 78, 1);
        margin-left: 27px;
    }
}

.data_paly {
    margin-left: 8px;
    font-size: 12px;
}

.data_paly_btn {
    margin-left: 8px;
}

.data_board_chart {
    display: flex;
    margin-top: 20px;
    overflow: hidden;
    justify-content: space-around;

    .board_chart {
        width: 30%;
        height: 350px;
        background-color: #fff;
        border-radius: 4px;
        overflow: hidden;
        min-width: 300px;
        #main {
            width: 100%;
            height: 350px;
        }

        #main1 {
            width: 100%;
            height: 350px;
        }

        #main2 {
            width: 100%;
            height: 350px;
        }
    }

    .board_chart_use {
        width: 349px;
        height: 20px;
        // background-color:cornflowerblue;
        margin: 12px 20px;

        .span {
            font-size: 15px;
            line-height: 16px;
            color: rgba(46, 60, 78, 1);
        }

        .span1 {
            margin-left: 12px;
            font-size: 13px;
            color: #8a929f;
        }
    }
}

.data_board_table {
    display: flex;
    justify-content: space-around;
    overflow: hidden;

    .data_board_table1 {
        width: 46.5%;
        height: 400px;
        background-color: #fff;
        border-radius: 4px;
        margin-top: 24px;
        overflow: hidden;

        .Customer_Leads {
            width: 90%;
            display: flex;
            justify-content: space-between;
            margin: 18px auto;

            span {
                font-size: 16px;
                color: #2E3C4E;
            }

            el-button {
                border: none;
            }
        }

        .board_table {
            width: 90%;
            height: 150px;
            margin: 20px auto;

            table {
                width: 680px;
                border-right: 0.5px solid #dde1e9;
                border-bottom: 0.5px solid #dde1e9;
                border-collapse: collapse;

                th {
                    width: 98px;
                    height: 40px;
                    border-right: 0.5px solid #dde1e9;
                    border-bottom: 0.5px solid #dde1e9;
                    border-left: 0.5px solid #dde1e9;
                    border-top: 0.5px solid #dde1e9;
                    background-color: #f1f4fa;
                    color: #2E3C4E;
                    font-size: 14px;
                }

                td {
                    width: 150px;
                    height: 40px;
                    text-align: center;
                    border-left: 0.5px solid #dde1e9;
                    border-top: 0.5px solid #dde1e9;
                    color: #8A929F;
                    font-size: 14px;
                }
            }
        }
    }
}
</style>