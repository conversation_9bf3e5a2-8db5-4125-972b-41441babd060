<template>
  <div class="pages">
    <div class="check-box-list row div">
      <div class="check-box div row">
        <div class="check-item" v-for="(item, index) in tabs_list" :key="index" @click="onClickTabs(item)"
          :class="{ isactive: item.title === is_tabs_page }">
          {{ item.name }}
        </div>
      </div>
    </div>

    <div class="bor-t" style="background: #fff;padding: 24px 24px 0px 24px;" v-if="is_tabs_page != 'qiwei_setting'" :is="is_tabs_page"
      :query_type="query_type" keep-alive></div>
  </div>
</template>

<script>
import house from "../../house/house_setting";
import crm from "../components/cus_seas";
import setting from "../crm_customer_setting";
import vrSetting from "../crm_customer_vr_setting"; //vr
import sms from "../crm_customer_sms";  //短信
import newHouse from "../newHouse";  //新房
import outbound from "../components/outbound"; // 呼叫中心
import huokewaiwang from "../components/huokewaiwang"; // 获客外网
import { mapState } from "vuex";
export default {
  components: {
    house,
    huokewaiwang,
    crm,
    setting,
    sms,
    vrSetting,
    newHouse,
    outbound
  },
  data() {
    return {
      tabs_list: [
        { id: 4, name: "通用配置", title: "setting" },
        { id: 2, name: "房源配置", title: "house" },
        { id: 3, name: "客源配置", title: "crm" },
        { id: 5, name: "短信配置", title: "sms" },
        { id: 1, name: "新房配置", title: "newHouse" },
        { id: 7, name: "获客外网", title: "huokewaiwang" },


      ],
      is_tabs_page: "setting",
      query_type: "",
      website_id: ""
    };
  },
  computed: {
    ...mapState(["website_info"]),
  },
  mounted() {
    /**
     * @date 2022年11月16日16:26:25
     * @description 根据总控/登录用户是否是站长/如果不是当前登录用户是否拥有配置项
     * 主要是房源和客源配置项的显示，先根据总控是否开启判断，在判断是否是站长
     * */
    this.website_id = this.$route.query.website_id
    this.is_tabs_page = this.$route.query.type;
    this.query_type = this.$route.query.query_type1;
    if (this.$route.query.type === "crm") {
      this.is_tabs_page = "setting";
    }

    // 站点开启模式-》站点用户是否是站长=》不是站长查询用户是否有权限
    // 根据站点开通模式进行过滤
    if (this.website_info.website_mode_show.indexOf("2") == -1) {
      // 根据配置过滤客源配置
      this.filterCustomer();
    }
    if (this.website_info.website_mode_show.indexOf("3") == -1) {
      // 根据配置过滤房源配置
      this.filterHouse();
    }
    if (this.website_info.website_mode_show.indexOf("1") == -1) {
      // 根据配置过滤房源配置
      this.filterNewHouse();
    }
    if (
      this.website_info.website_mode_show.indexOf("2") != -1 ||
      this.website_info.website_mode_show.indexOf("3") != -1
    ) {
      // 站点开通包含客源模块或者开通房源模块
      // 查询用户信息判断是否为站长
      this.getadminUser();
    }

    this.tabs_list.push({
      id: 6, name: "呼叫中心", title: "outbound"
    })
    if (this.$store.state.has_qw_menu) {
      this.tabs_list.push({ id: 8, name: "企微配置", title: "qiwei_setting" })
    }

    if (this.$route.query.title) {
      this.is_tabs_page = this.$route.query.title;
    }
    // 获取站点信息判断是否显示获客外网
    // this.checkShowHuokewaiwang()
  },
  methods: {
    // 过滤客源
    filterCustomer() {
      this.tabs_list = this.tabs_list.filter((item) => item.id != 3);
    },
    // 过滤房源
    filterHouse() {
      this.tabs_list = this.tabs_list.filter((item) => item.id != 2);
    },
    filterNewHouse() {
      this.tabs_list = this.tabs_list.filter((item) => item.id != 1);
    },
    /* eslint-disable */
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          // 判断当前登录用户不是站长，不是站长查询当前用户的权限
          if (res.data.roles && res.data.roles[0].name !== "站长") {
            this.getSiteCrmSetting(res.data.id);
            // this.getHouseBusiness();
          }
        }
      });
    },
    async checkShowHuokewaiwang() {
      let weixin_result = await this.$http.queryXiaoApp().catch(() => { })

      if (!(this.website_info.open_mini_erp_program || this.website_info.open_mini_build_program || (weixin_result && weixin_result.data && weixin_result.data.id > 0))) {
        this.tabs_list = this.tabs_list.filter(item => item.id != 7)
      }
      // if (weixin_result.data.id == 0) {
      //   this.tabs_list = this.tabs_list.filter(item => item.id != 3)
      // }
    },
    // 获取当前用户是否有权限查看
    getSiteCrmSetting(id) {
      this.$http.getAuthCrmShow("config_auth_uid").then((res) => {
        if (res.status === 200) {
          if (res.data.indexOf(id) == -1) {
            this.filterCustomer();
            this.filterHouse();
          }
        }
      });
    },
    // 获取当前用户是否有权限查看房源
    getHouseBusiness() {
      this.$http.getAuthCrmShow('config_auth_uid').then((res) => {
        if (res.status === 200) {
          if (!res.data.auth_setting) {
            this.filterHouse();
          }
        } else {
          this.filterHouse();
        }
      });
    },
    onClickTabs(e) {
      this.is_tabs_page = e.title;
      console.log(e);
      if (e.title == "newHouse") {
        this.$goPath("website_update");
      }
      if (e.title == "qiwei_setting") {
        this.$http.getNewSideBar(4).then(res => {
          console.log(res);
          let menuList = {
            home_url: "/crm_customer_index",
            id: 4,
            is_selected: 0,
            is_show: 1,
            name: "企微",
            title: "企微",
            new_set_id: "new_qw",
            children: res.data
          }
          sessionStorage.setItem("new_qw", 1)
          sessionStorage.setItem("top_menu_info_new", JSON.stringify(menuList))
          eventBus.$emit("setNewSideBar", menuList)
          this.$goPath('/crm_customer_crmlist')
        })
        // this.$goPath("website_update");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.check-box-list {
  padding: 0 28px;
  cursor: pointer;
  justify-content: space-between;
  align-items: center;

  .pingtai {
    font-size: 20px;
    color: #607080;
  }
}

.bor-t {
  border-top: 1px solid #dde1e9;
  margin-left: -24px;
  margin-right: -24px;
}

.check-box {
  margin-top: 24px;
  margin-left: 60px;
  margin-bottom: 20px;
  font-size: 20px;
  color: #607080;

  .check-item {
    margin-right: 40px;
    position: relative;

    &.isactive {
      color: #0083ff;

      &::after {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        content: "";
        height: 4px;
        background: #2d84fb;
        width: 106px;
        display: block;
        margin-top: 18px;
      }
    }
  }
}
</style>
