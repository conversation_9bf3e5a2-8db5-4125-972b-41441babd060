<template>
  <div class="outbount">
    <!-- 顶部模块 -->
    <div class="bg_fff box_top marbot28">
      <el-row :gutter="20">
        <el-col class="el-col-4-8">
          <div class="top_item flex-row" :style="xiansuoStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img
                  :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`"
                  alt=""
                />
              </div>
              <div class="top_item_left_name">线索总量</div>
            </div>
            <div class="top_item_right">
              <div class="top_item_right_num">{{ topInfo.clueCount }}</div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="top_item flex-row" :style="zuoxiStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img
                  :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`"
                  alt=""
                />
              </div>
              <div class="top_item_left_name">坐席数量</div>
            </div>
            <div class="top_item_right">
              <div class="top_item_right_num">{{ topInfo.seatsCount }}</div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="top_item flex-row" :style="bodaStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img
                  :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`"
                  alt=""
                />
              </div>
              <div class="top_item_left_name">拨打总量</div>
            </div>
            <div class="top_item_right">
              <div class="top_item_right_num">{{ topInfo.callCount }}</div>
            </div>
          </div>
        </el-col>

        <el-col class="el-col-4-8">
          <div class="top_item flex-row" :style="botongStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img
                  :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`"
                  alt=""
                />
              </div>
              <div class="top_item_left_name">拨通总量</div>
            </div>
            <div class="top_item_right">
              <div class="top_item_right_num">{{ topInfo.onCallCount }}</div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="top_item flex-row" :style="weiboStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img
                  :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`"
                  alt=""
                />
              </div>
              <div class="top_item_left_name">未拨总量</div>
            </div>
            <div class="top_item_right">
              <div class="top_item_right_num">{{ topInfo.unCallCount }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 顶部模块结束 -->
    <!-- 图表 模块 -->
    <div class="marbot28 bg_fff charts">
      <!-- 搜索 -->
      <div class="charts_top flex-row">
        <div class="flex-1"></div>
        <div class="bottom-border div row" style="padding: 24px">
          <!-- <span class="text">创建时间：</span> -->
          <div class="outbount_top_item align-center flex-row mr20 mb12">
            <div class="task_name mr10">成员姓名</div>
            <!-- admin_id -->
            <div class="task_sel select_name align-center flex-row">
              <div>
                <el-input
                  placeholder="请选择成员"
                  v-model="user_name"
                  size="small"
                  @focus="showMemberList"
                >
                  <i
                    @click="delName"
                    slot="suffix"
                    class="el-input__icon el-icon-circle-close"
                  ></i
                ></el-input>
              </div>
            </div>
          </div>
          <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
          <!-- <span class="text">自定义：</span> -->
          <el-date-picker
            style="width: 250px"
            size="small"
            v-model="p_time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="changeTimeRange"
          >
          </el-date-picker>
        </div>
      </div>
      <!-- 搜索结束 -->
      <!-- 图表模块 -->
      <div class="echart flex-row align-center">
        <!-- <el-row class ="" :gutter="20"> -->
        <div class="flex-1">
          <el-col :span="24">
            <el-col :span="12">
              <div class="echart_left">
                <div class="echart_l_top">概览</div>
                <div class="echart_l_con">
                  <div
                    class="chart-box sale-chart"
                    id="chart-box"
                    style="width: 100%; height: 390px"
                  ></div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="echart_left">
                <div class="echart_l_top">时长统计</div>
                <div class="echart_l_con">
                  <div
                    class="chart-box pie-chart"
                    style="width: 100%; height: 390px"
                  ></div>
                </div>
              </div>
            </el-col>
          </el-col>
        </div>
        <div class="echart-right">
          <div class="echart_l_top">通话统计</div>
          <div class="echart_l_con flex-row flex-wrap">
            <div
              class="echart_l_con_item"
              v-for="(item, index) in left1Data"
              :key="index"
            >
              <div class="echart_l_con_item_top">{{ item.count }}</div>
              <div class="echart_l_con_item_bottom">{{ item.name }}</div>
            </div>
            <!-- <div class="echart_l_con_item">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div>
            <div class="echart_l_con_item">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div>
            <div class="echart_l_con_item">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div>
            <div class="echart_l_con_item echart_l_con_item_one">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div> -->
          </div>
        </div>
        <!-- </el-row> -->
      </div>

      <!-- 表格模块 -->
      <div class="table_con">
        <div class="table_search flex-row align-center">
          <div class="search_left flex-1 flex-row align-center">
            <div class="table_title">坐席情况统计</div>
            <!-- <div class="table_search_inp flex-row align-center">
              <el-input
                class="small_inp"
                size="small"
                v-model="zuoxi_params.start_time"
              >
                <template slot="append">秒</template>
              </el-input>
              <span>-</span>
              <el-input
                class="small_inp"
                size="small"
                v-model="zuoxi_params.end_time"
              >
                <template slot="append">秒</template>
              </el-input>
            </div> -->
          </div>
          <div class="table_search_btns flex-row align-center">
            <!-- <el-button
              size="small"
              type="primary"
              class="mr10"
              @click="searchTable"
            >
              搜索
            </el-button> -->
            <!-- <el-button size="small" type="primary"> 导出 </el-button> -->
          </div>
        </div>
        <div class="table">
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
          >
            <!-- @selection-change="handleSelectionChange" -->
            <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
            <el-table-column
              label="记录ID"
              width="100"
              prop="id"
            ></el-table-column>

            <el-table-column label="成员名称" prop="admin_name">
            </el-table-column>
            <el-table-column label="成员手机号" prop="admin_phone">
            </el-table-column>
            <el-table-column label="外显手机号" prop="show_phone">
            </el-table-column>
            <el-table-column label="通话次数" prop="onCallCount">
            </el-table-column>
            <el-table-column label="拨打次数" prop="callCount">
            </el-table-column>
            <el-table-column label="通话时长" prop="duration">
            </el-table-column>
            <el-table-column label="平均时长" prop="avg_duration">
            </el-table-column>
            <el-table-column label="添加时间" prop="created_at">
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev,pager,next"
            :total="total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="show_select_dia" width="660px" title="选择成员">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :isOutbound="true"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import myLabel from '@/components/navMain/crm/components/my_label'
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
export default {
  components: { myLabel, memberListSingle },

  data() {
    return {
      xiansuoStyle: {},
      bodaStyle: {},
      zuoxiStyle: {},
      botongStyle: {},
      weiboStyle: {},
      time_list: [
        { id: 1, name: "全部", value: "" },
        { id: 1, name: "今天", value: "today" },
        { id: 2, name: "昨天", value: "yesterday" },
        { id: 3, name: "本周", value: "this_week" },
        { id: 4, name: "上周", value: "last_week" },
        { id: 5, name: "本月", value: "this_month" },
        { id: 6, name: "上月", value: "last_month" },
      ],
      p_time: '',
      zuoxi_params: {
        times: ""
        // start_time: 0,
        // end_time: 0
      },
      is_table_loading: false,
      tableData: [],
      total: 0,
      params: {
        page: 1,
        per_page: 10
      },
      topInfo: {
        "clueCount": 0,//线索总量
        "seatsCount": 0,//坐席数量
        "callCount": 0,//拨打总量
        "unCallCount": 0,//未拨打数量
        "onCallCount": 0//拨通总量
      },
      leftDataY: ['线索总量', '坐席数量', '拨打总量', '未拨数量', '拨通总量'],
      leftDataX: [],
      middleInfo: {},
      left1DataY: ['小于30秒', '30-60秒', '60-120秒', '120-300秒', '300-600秒', '600秒以上'],
      left1Data: [],
      memberList: [],
      user_name: "",
      show_select_dia: false

    };
  },
  created() {
    this.getTopInfo()
    this.getMiddleInfo()
    this.getBottomData()
    this.xiansuoStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_red.png)`
    }
    this.zuoxiStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_orange.png)`
    }
    this.bodaStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_blue.png)`
    }
    this.botongStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_green.png)`
    }
    this.weiboStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_red.png)`
    }
  },
  methods: {
    initChartOne() {
      var myChart1 = echarts.init(document.querySelector(".sale-chart"));
      var textColorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];

      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          splitLine: {
            show: false,
          }
        },
        yAxis: {
          type: 'category',

          data: this.leftDataY
        },
        color: textColorList,
        series: [
          {
            type: 'bar',
            itemStyle: {
              normal: {
                color: function (params) {
                  // 给出颜色
                  return textColorList[params.dataIndex]
                },
              }
            },
            data: this.leftDataX
          },
        ],
      };
      myChart1.setOption(option);
      window.addEventListener("resize", function () {
        myChart1.resize();
      });
    },
    initChartTwo() {
      var myChart = echarts.init(document.querySelector(".pie-chart"));
      var textColorList = ["#6378FF", "#6C9BFF", "#54D89D", "#FFA338", '#FF8F86', '#FE5858'];

      var option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        color: textColorList,
        padding: [10, 100, 0, 0],
        series: [
          {
            name: '',
            type: 'pie',
            left: "0",
            top: 14,
            bottom: 30,

            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {   //视觉引导线
              show: true
            },
            data: this.left1Data,
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    getTopInfo() {
      this.$http.getOutboundIndexTopInfo().then(res => {
        if (res.status == 200) {
          this.topInfo = res.data
          let data = []
          data[0] = this.topInfo.clueCount
          data[1] = this.topInfo.seatsCount
          data[2] = this.topInfo.callCount
          data[3] = this.topInfo.unCallCount
          data[4] = this.topInfo.onCallCount
          this.leftDataX = [...data]
          this.initChartOne()

        }
      })
    },
    getMiddleInfo() {
      this.$http.getOutboundIndexMiddleInfo(this.zuoxi_params).then(res => {
        console.log(res);
        if (res.status == 200) {

          let dataObj = { data1: { count: 0, duration: 0 }, data2: { count: 0, duration: 0 }, data3: { count: 0, duration: 0 }, data4: { count: 0, duration: 0 }, data5: { count: 0, duration: 0 }, data6: { count: 0, duration: 0 } }

          if (res.data) {
            this.middleInfo = res.data
            // data = Array.from(Object.values(res.data), x => x)
          }
          else {
            this.middleInfo = dataObj
          }

          let data = Array.from(Object.values(this.middleInfo), x => x)
          console.log(data);
          for (let index = 0; index < this.left1DataY.length; index++) {
            const ele = this.left1DataY[index];
            data[index].name = ele
            data[index].value = data[index].duration || 0
            // const ele = this.left1DataY[index];
            // ele.name =
          }
          this.left1Data = data
          this.initChartTwo()

        }
      })
    },
    getBottomData() {
      this.$http.getOutboundIndexBottomInfo(this.params).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.tableData = res.data.data
          this.total = res.data.total
        }
      })
    },
    onClickTime(e) {
      this.zuoxi_params.times = e.value
      this.getMiddleInfo()
    },
    changeTimeRange(e) {
      if (e && e.length) {
        this.zuoxi_params.times = e.join(",")
      } else {
        this.zuoxi_params.times = ''
      }
      this.getMiddleInfo()

    },
    onPageChange(e) {
      this.params.page = e;
      this.getBottomData();
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getOutboundDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    showMemberList() {
      this.getDepartment()
      this.show_select_dia = true;
    },
    delName() {
      this.zuoxi_params.admin_id = ''
      // this.params.wx_work_user_id = ''
      this.user_name = ''
      // this.zuoxi_params.page = 1;
      this.getMiddleInfo();
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.zuoxi_params.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id;
      } else {
        this.user_name = "";
        this.zuoxi_params.admin_id = "";
      }
      this.getMiddleInfo();
      this.show_select_dia = false;
    },
    searchTable() {
      this.params.page = 1
      this.getBottomData()
    }

  },
};
</script>
<style lang="scss">
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
  // min-width: 1390px;
  // overflow: auto;
}
.marbot28 {
  margin-bottom: 28px;
}
.bg_fff {
  background: #fff;
}
.box_top {
  padding: 20px;
}
.top_item {
  // background: #3333;
  padding: 30px;
  border-radius: 10px;
  height: 130px;
  background-repeat: no-repeat;
  box-sizing: border-box;
  .top_item_left {
    .top_item_left_img {
      width: 28px;
      height: 28px;
      overflow: hidden;
      border-radius: 50%;
      align-self: flex-start;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .top_item_left_name {
      margin-left: 10px;
      font-size: 18px;
      color: #fff;
    }
  }
  .top_item_right {
    align-self: center;
    color: #fff;
    font-size: 48px;
    font-weight: 800;
  }
}
.echart {
  padding: 20px 10px;
}
.echart-right {
  min-width: 340px;
  width: 340px;

  .echart_l_con {
    padding: 10px;
    flex-wrap: wrap;
    box-sizing: border-box;
    .echart_l_con_item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 155px;
      padding: 14px 0;
      margin-right: 10px;
      border-radius: 10px;
      background: #f1f4fa;
      height: 90px;
      margin-bottom: 10px;
      &.echart_l_con_item_one {
        width: 320px;
        margin-right: 0;
        margin-bottom: 0;
      }
      &:nth-child(2n) {
        margin-right: 0;
      }
      .echart_l_con_item_top {
        margin-bottom: 10px;
        font-weight: 600;
        color: #2d84fb;
        font-size: 20px;
      }
      .echart_l_con_item_bottom {
        color: #2e3c4e;
        font-size: 16px;
      }
    }
  }
}
.mr10 {
  margin-right: 10px;
}
.table_title {
  font-weight: 600;
}
.table_search_inp {
  margin-left: 20px;
}
.small_inp {
  width: 100px;
}
.table_con {
  .table_search {
    margin-bottom: 10px;
  }
}
.el-col-4-8 {
  width: 20%;
}
.outbount_top_item {
  .task_name {
    color: #2e3c4e;
    font-size: 14px;
  }
  .select_name {
    width: 200px;
  }
}
</style>
