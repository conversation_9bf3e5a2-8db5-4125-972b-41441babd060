<template>
  <el-container>
    <el-header>
      <el-button type="primary" @click="createOssOrder">创建短信订单</el-button>
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
      <el-dialog width="400px" :visible.sync="dialogPayCode" title="支付">
        <div class="code-box">
          <img :src="codeImg" alt="" />
          <p>请打开微信扫码支付</p>
        </div>
      </el-dialog>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog title="产品列表" :visible.sync="smsDisplay">
      <myTable
        :table-list="sms_product_list"
        :header="sms_product_list_header"
      ></myTable>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "sms_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      dialogPayCode: false,
      codeImg: "",
      smsDisplay: false,
      sms_product_list: [],
      table_header: [
        {
          expand: "expand",
          render: (h, data) => {
            return (
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="创建时间：">
                  <span>{data.row.created_at}</span>
                </el-form-item>
                {data.row.status === 1 ? (
                  <el-form-item label="支付时间：">
                    <span>{data.row.payment_at}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
                {data.row.status === 1 ? (
                  <el-form-item label="支付方式：">
                    {data.row.payment_category_id === 0
                      ? "暂未支付"
                      : data.row.payment_category_id === 1
                      ? "微信小程序支付"
                      : data.row.payment_category_id === 2
                      ? "微信扫码支付"
                      : data.row.payment_category_id === 3
                      ? "微信APP支付"
                      : "微信H5支付"}
                  </el-form-item>
                ) : (
                  ""
                )}
                <el-form-item label="订单支付状态：">
                  <span>
                    {data.row.payment_status === 0 ? "未付款" : "已付款"}
                  </span>
                </el-form-item>
                {data.row.status === 1 ? (
                  <el-form-item label="成交单号">
                    <span>{data.row.payment_trade_sn}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
                <el-form-item label="备注信息：">
                  <span>{data.row.remark}</span>
                </el-form-item>
              </el-form>
            );
          },
        },
        { prop: "id", label: "id", width: "100" },
        { prop: "order_sn", label: "订单编号" },
        { prop: "sms_total", label: "短信数量/条" },
        { prop: "payment_amount", label: "支付金额/元" },
        {
          prop: "status",
          label: "订单状态",
          formatter: (row) => {
            var status = row.status;
            if (status === 0) {
              return "未完成";
            } else if (status === 1) {
              return "已完成";
            } else {
              return "已取消";
            }
          },
        },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {data.row.status === 0 ? (
                  <el-button
                    type="primary"
                    size="mini"
                    onClick={() => {
                      this.cancelOrder(data.row);
                    }}
                  >
                    取消订单
                  </el-button>
                ) : (
                  ""
                )}
                {data.row.status === 0 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.wxPay(data.row);
                    }}
                  >
                    微信支付
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      sms_product_list_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "description", label: "套餐描述" },
        { prop: "price", label: "套餐价格/元" },
        { prop: "sms_total", label: "短信数量/条" },
        {
          label: "操作",
          fixed: "right",
          render: (h, data) => {
            return (
              <el-button
                type="success"
                size="mini"
                onClick={() => {
                  this.buySms(data.row);
                }}
              >
                创建订单
              </el-button>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    createOssOrder() {
      this.$http.getSmsProductList().then((res) => {
        if (res.status === 200) {
          this.smsDisplay = true;
          this.sms_product_list = res.data.data;
        }
      });
    },
    buySms(row) {
      this.$http.createSmsOrder({ package_id: row.id }).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "创建成功",
            type: "success",
          });
          this.getDataList();
          this.smsDisplay = false;
        }
      });
    },
    getDataList() {
      this.$http.SmsOrderList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    cancelOrder(row) {
      this.$confirm("是否取消订单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.cancelSmsOrder(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "已取消",
                type: "success",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {});
    },
    wxPay(row) {
      this.codeImg = "";
      this.dialogPayCode = true;
      this.$http.getPaySmsQrCode(row.id).then((res) => {
        if (res.status === 200) {
          this.codeImg =
            "data:image/png;base64," +
            btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            );
        }
      });
    },
  },
};
</script>

<style lang="scss">
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 120px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.code-box {
  text-align: center;
  p {
    text-align: center;
    color: #6bcc03;
    font-size: 28px;
  }
}
</style>
