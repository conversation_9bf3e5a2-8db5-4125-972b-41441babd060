<template>
  <div v-fixed-scroll="48">
    <div class="filter-wrapper">
      <el-form inline size="medium">
        <el-form-item label="客服账号">
          <el-input placeholder="请输入" v-model="params.nick_name"></el-input>
        </el-form-item>

        <el-form-item label="姓名">
          <el-input placeholder="请输入" v-model="params.user_name"></el-input>
        </el-form-item>

        <el-form-item label="">
          <el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <myTable 
      :table-list="list" 
      :header="columns" 
      tooltipEffect="light" 
      v-loading="loading"
      :header-cell-style="{ background: '#EBF0F7' }" 
      highlight-current-row 
      :row-style="$TableRowStyle"
      ref="myTable">
    </myTable>
    
    <div class="tab-content-footer">
      <div></div>
      <el-pagination 
        background 
        layout="total,sizes,prev, pager, next, jumper" 
        :total="count"
        :page-sizes="[10, 20, 30, 50,100]" 
        :page-size="params.per_page" 
        :current-page="params.page"
        @current-change="onPageChange" 
        @size-change="handleSizeChange">
      </el-pagination>
    </div>

    <updateWeixinShopKfAccount v-if="dialogs.updateWeixinShopKfAccount" ref="updateWeixinShopKfAccount"/>
  </div>
</template>

<script>
import myTable from "@/components/components/my_table";
import weixinHttp from "@/utils/weixinHttp";
import updateWeixinShopKfAccount from "./updateWeixinShopKfAccount.vue";

export default {
  components: {
    myTable, updateWeixinShopKfAccount
  },
  data() {
    return {
      loading: false,
      params: {
        page: 1,
        per_page: 10,
        nick_name: '',
        user_name: '',
      },
      count: 0,
      list: [],
      dialogs: {
        updateWeixinShopKfAccount: false
      }
    }
  },
  computed: {
    columns() {
      return [
        { 
          prop: "nick_name", 
          label: "客服账号", 
          minWidth: 200,
          render: (h, data) => {
            return (
              <div class="account-info">
                <div class="avatar">
                  {data.row.avatar ? 
                    <img src={data.row.avatar} class="avatar-img"/> : 
                    <span class="avatar-word">{this.getFirstWord(data.row.nick_name)}</span>
                  }
                </div>
                <div class="info">
                  <p class="nickname">{data.row.nick_name}</p>
                  <p class="openid">{data.row.openid}</p>
                </div>
              </div>
            );
          }
        },
        { 
          prop: "shop_name", 
          label: "店铺信息", 
          minWidth: 200,
          render: (h, data) => {
            return (
              <div class="shop-info">
                <p class="shop-name">{data.row.shop_name}</p>
              </div>
            );
          }
        },
        { 
          prop: "user_name", 
          label: "姓名", 
          minWidth: 200,
          render: (h, data) => {
            return (
              <div class="anchor-info">
                <p class="user-name">{data.row.user_name || '--'}</p>
              </div>
            );
          }
        },
        { prop: "id", label: "操作", minWidth: 240, render: (h, {row})=>{
            return (
              <div>
                { row.admin_id_bind? 
                  <el-popconfirm title="确定取消关联吗？" onOnConfirm={()=>this.cancelWeixinShopKfAccount(row)}>
                    <el-link type="primary" slot="reference">取消关联人事账号</el-link>
                  </el-popconfirm>
                  : <el-link type="primary" onClick={()=>this.openUpdateWeixinShopKfAccountPage(row)}>关联人事账号</el-link>
                }
              </div>
            )
          } 
        },
      ];
    }
  },
  mounted() {
    let pagenum = localStorage.getItem('pagenum');
    this.params.per_page = Number(pagenum) || 10;
    this.getList();
  },
  methods: {
    getFirstWord(str) {
      if (!str) return '';
      return str.trim().replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '').substring(0, 1);
    },
    async openUpdateWeixinShopKfAccountPage(row){
      this.dialogs.updateWeixinShopKfAccount = true;
      this.$nextTick(()=>{
        this.$refs.updateWeixinShopKfAccount.open(row).onSuccess(()=>{
          this.getList();
        });
      })
    },
    async cancelWeixinShopKfAccount({id}){
      const res = await weixinHttp.updateWeixinShopKfAccount({
        account_id: id,
        admin_id: 0
      }).catch(()=>{})
      if(res.status == 200){
        this.$message.success(res.data?.msg || '取消关联成功');
        this.getList();
      }
    },
    async getList() {
      this.loading = true;
      try {
        const res = await weixinHttp.getWeixinShopKfAccountList(this.params);
        if (res.status == 200) {
          this.count = res.data?.total || 0;
          this.list = res.data?.data || [];
        }
      } catch (error) {
        console.error('获取账号列表失败:', error);
      }
      this.loading = false;
    },
    search() {
      this.params.page = 1;
      this.getList();
    },
    onPageChange(page) {
      this.params.page = page;
      this.getList();
    },
    handleSizeChange(size) {
      this.params.per_page = size;
      this.search();
    }
  }
}
</script>

<style scoped lang="scss">
.filter-wrapper {
  padding: 16px 16px 12px;
}

::v-deep {
  .account-info {
    display: flex;
    flex-direction: row;
    height: 50px;
    align-items: center;
    
    .avatar {
      height: 32px;
      
      .avatar-word {
        display: inline-block;
        height: 32px;
        width: 32px;
        border-radius: 50%;
        background-color: #f1f2f3;
        line-height: 32px;
        text-align: center;
        color: #c6c6c6;
        font-weight: 300;
      }
      
      .avatar-img {
        height: 32px;
        width: 32px;
        border-radius: 50%;
      }
    }
    
    .info {
      flex: 1;
      margin-left: 12px;
      
      .nickname {
        font-size: 14px;
        color: #333;
        margin: 0;
        line-height: 18px;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .openid {
        font-size: 12px;
        color: #999;
        margin: 2px 0 0;
        line-height: 14px;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .shop-info {
    .shop-name {
      font-size: 14px;
      color: #333;
      margin: 0;
      line-height: 18px;
      font-weight: 500;
    }
  }

  .anchor-info {
    .user-name {
      font-size: 14px;
      color: #333;
      margin: 0 0 4px 0;
      line-height: 18px;
    }
    
    .mobile {
      font-size: 12px;
      color: #999;
      margin: 0;
      line-height: 14px;
    }
  }
}
</style> 