<template>
  <el-container>
    <el-header class="div row">
      <div class="div row">
        <div class="title">门店列表</div>
        <div class="title_number">
          当前页面共(
          <i>{{ tableData.length }}</i>
          )条数据
        </div>
        <div class="add-build">
          <el-button type="primary" @click="addData" icon="el-icon-plus"
            >添加门店</el-button
          >
        </div>
      </div>

      <div class="div row">
        <el-input
          v-model="params.name"
          @change="onChange"
          placeholder="请输入公司名称"
        ></el-input>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜索</el-button
        >
      </div>
    </el-header>
    <el-main v-loading="is_table_loading">
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "company_store",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
        category: 2,
        pid: "",
      },
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "name", label: "公司名称" },
        {
          label: "店长信息",
          render: (h, data) => {
            return (
              <div>
                {data.row.u_store_manager.map((item) => {
                  return (
                    <el-tag style="margin:4px">
                      {item.u_name || item.u_user_name || item.u_nickname}：
                      {item.u_phone}
                    </el-tag>
                  );
                })}
              </div>
            );
          },
        },
        {
          label: "标识",
          render: (h, data) => {
            return (
              <el-popover width="500px" trigger="hover" placement="right">
                <el-image
                  style="width:300px;height:300px"
                  fit="contain"
                  src={data.row.logo}
                />
                <img
                  slot="reference"
                  src={data.row.logo}
                  style="max-height:50px;max-width:100px"
                />
              </el-popover>
            );
          },
        },
        { prop: "category_name", label: "公司分类" },
        { prop: "employee_total", label: "员工总数" },
        {
          label: "门店码",
          render: (h, data) => {
            return <el-tag type="success">{data.row.store_code}</el-tag>;
          },
        },
        { prop: "full_address", label: "详细地址" },
        { prop: "updated_at", label: "添加时间" },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改销售公司") ? (
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="primary"
                    style="margin:4px"
                    onClick={() => {
                      this.updataCompany(data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("公司成员") ? (
                  <el-button
                    icon="el-icon-user-solid"
                    size="mini"
                    type="success"
                    style="margin:4px"
                    onClick={() => {
                      this.seeUsers(data.row);
                    }}
                  >
                    查看成员
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("业绩统计") ? (
                  <el-button
                    icon="el-icon-data-analysis"
                    size="mini"
                    style="margin:4px"
                    type="primary"
                    onClick={() => {
                      this.statistics(data.row);
                    }}
                  >
                    业绩统计
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("刷新门店码") ? (
                  <el-button
                    plain
                    icon="el-icon-refresh"
                    size="mini"
                    style="margin:4px"
                    type="success"
                    onClick={() => {
                      this.refreshCode(data.row);
                    }}
                  >
                    刷新门店码
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除销售公司") ? (
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    style="margin:4px"
                    type="danger"
                    onClick={() => {
                      this.handleDelete(0, data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    addData() {
      this.$goPath(
        "/upload_company?company_category=2&store=" +
          this.$route.query.company_id
      );
    },
    updataCompany(row) {
      this.$goPath(
        `/updata_company?id=${row.id}&store=${this.$route.query.company_id}`
      );
    },
    getDataList() {
      this.params.pid = this.$route.query.company_id;
      this.$http.showCompanyLists({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    // 搜索楼盘
    search() {
      this.params.page = 1;
      this.getDataList();
      // this.$http.searchCompany(this.input).then((res) => {
      // 	if (res.status === 200) {
      // 		this.tableData = res.data.data;
      // 	}
      // });
    },
    onChange() {
      this.search();
    },

    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该门店, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCompany(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    refreshCode(row) {
      this.$http.refreshCompanyCode(row.id).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "刷新成功",
            type: "success",
          });
          this.getDataList();
        }
      });
    },
    statistics(row) {
      this.$goPath(`/statistics_list?id=${row.id}`);
    },
    seeUsers(row) {
      this.$goPath("/company_users?company_id=" + row.id);
    },
  },
};
</script>

<style scoped lang="scss">
.el-select {
  margin-left: 10px;
}
// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;
  .browse {
    width: 480px;
    .browse-item {
      margin: 0 5px;
      font-size: 14px;
      padding: 2px 10px;
      border-radius: 50px;
      color: #333;
      &.browse_active {
        color: #fff;
        background: #0068e6;
      }
    }
  }
  .block-time {
    justify-content: flex-start;
    margin: 10px;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
  }
  .bg-purple {
    border: 1px dashed #d3dce6;
  }
  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;
    .left {
      color: #999;
      font-size: 14px;
      text-align: start;
      p {
        color: #333;
      }
      .desc {
        color: #999;
      }
    }
    .right {
      color: #26bf8c;
    }
  }
}
.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}
.el-button {
  border-radius: 10px;
  margin: 5px;
}
.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}
.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
  .add-build {
    margin-left: 10px;
    .el-button {
      border-radius: 4px;
    }
  }
}
.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}
.scope-box {
  height: 40px;
  .el-image {
    height: 40px;
    width: 100%;
  }
}
.title-ctn {
  margin-left: 10px;
  margin-bottom: 5px;
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
    justify-content: flex-start;
  }
  i {
    margin-right: 5px;
    display: block;
    text-align: center;
    border-radius: 50%;
    line-height: 20px;
    width: 20px;
    height: 20px;
    background: #26bf8c;
    color: #fff;
  }
}
</style>
