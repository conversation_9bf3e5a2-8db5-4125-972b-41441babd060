<template>
  <el-container>
    <el-header>
      <el-button size="mini" type="primary" @click="createData">添加</el-button>
      <el-radio-group
        @change="changeBind"
        v-model="params.category"
        size="mini"
        style="margin-left:30px"
      >
        <el-radio-button
          v-for="(item, index) in build_category_show_category_list"
          :key="index"
          :label="item.value"
          >{{ item.description }}</el-radio-button
        >
      </el-radio-group>
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_create_bind" label-width="100px">
        <el-form-item label="选择榜单：">
          <el-select
            v-model="form_create_bind.build_rank_list_id"
            filterable
            placeholder="请选择"
            :filter-method="filterRanking"
          >
            <el-option
              v-for="item in ranking_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择楼盘：">
          <el-select
            v-model="form_create_bind.build_id"
            filterable
            placeholder="请选择"
            :filter-method="filterBuild"
          >
            <el-option
              v-for="item in build_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设置排名：">
          <el-input
            v-model="form_create_bind.ranking"
            type="number"
            step="1"
            min="0"
            placeholder="请输入排名"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onCreate">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "bind_ranking",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        category: 0,
      },
      ranking_params: {
        name: "",
      },
      build_params: {
        name: "",
      },
      ranking_list: [],
      build_list: [],
      form_create_bind: {},
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      dialogCreate: false,
      build_category_show_category_list: [
        { value: "0", description: "系统默认" },
        { value: "1", description: "自定义" },
      ],
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "ranking", label: "排名" },
        { prop: "brl_name", label: "榜单名称" },
        { prop: "build_name", label: "楼盘名称" },
        { prop: "created_at", label: "添加时间" },
        {
          label: "操作",
          width: "120",
          fixed: "right",
          render: (h, data) => {
            return (
              <el-button
                size="mini"
                type="danger"
                onClick={() => {
                  this.cancelBind(data.row);
                }}
              >
                解除绑定
              </el-button>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.getDataList();
    this.getBuildList();
    if (this.$route.query.ranking_id) {
      this.params.category = 1;
      this.getRankingList();
    } else {
      this.params.category = 0;
      this.getDefaultRanking();
    }
  },
  methods: {
    getDataList() {
      this.$http.getRankingBindBuild({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    // 获取自定义列表
    getRankingList() {
      if (!this.ranking_params.name) {
        delete this.ranking_params.name;
      }
      this.$http
        .getCustomizeRanking({ params: this.ranking_params })
        .then((res) => {
          if (res.status === 200) {
            this.ranking_list = res.data.data;
          }
        });
    },
    getDefaultRanking() {
      this.$http.getDefaultRanking().then((res) => {
        if (res.status === 200) {
          this.ranking_list = res.data;
        }
      });
    },
    getBuildList() {
      if (!this.build_params.name) {
        delete this.build_params.name;
      }
      this.$http.queryBuild({ params: this.build_params }).then((res) => {
        if (res.status === 200) {
          this.build_list = res.data.data;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    createData() {
      if (parseInt(this.$route.query.ranking_id) && this.params.category == 1) {
        this.form_create_bind = {
          category: this.params.category,
          build_rank_list_id: parseInt(this.$route.query.ranking_id),
        };
      } else {
        this.form_create_bind = {
          category: this.params.category,
        };
      }
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    filterRanking(e) {
      this.ranking_params.name = e;
      this.getRankingList();
    },
    filterBuild(e) {
      this.build_params.name = e;
      this.getBuildList();
    },
    onCreate() {
      if (this.dialogTitle === "addData") {
        this.$http.createRankingForBuild(this.form_create_bind).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "绑定成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    cancelBind(row) {
      this.$confirm("此操作将取消该绑定，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.cancelRankingForBuild(row.id).then((res) => {
          if (res.status === 200) {
            this.$message({
              type: "success",
              message: "已解绑",
            });
            this.getDataList();
          }
        });
      });
    },
    changeBind(e) {
      this.params.category = e;
      if (e == 1) {
        this.getRankingList();
      } else {
        this.getDefaultRanking();
      }
      this.getDataList();
    },
  },
};
</script>

<style></style>
