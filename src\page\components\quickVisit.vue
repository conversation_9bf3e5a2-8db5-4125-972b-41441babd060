<template>
<div class="quick-container" v-if="!isPWAEnv">
    <el-popover placement="right-start" width="230" trigger="hover">
        <div class="quick-btn" slot="reference">
            <svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.333 4.688c.516-.594 1.492-.23 1.492.557v4.775h2.108c1.08 0 1.651 1.277.932 2.083l-6.897 7.721c-.552.618-1.571.162-1.478-.662l.585-5.179H8.006c-1.071 0-1.646-1.26-.944-2.07l6.271-7.225zm-.008 2.298l-4.77 5.497h3.199l-.545 4.82 5.165-5.783h-3.049V6.986z" fill="#000"></path></svg>
             快捷访问
        </div>
        <div class="quick-content">
            <div class="install" v-if="installAbled">
                <img :src="$imageDomain + '/icons/down-logo.png'" class="down-logo">
                <div class="title">添加T+到电脑桌面</div>
                <div class="desc">下次可以在桌面快捷访问T+系统</div>
                <el-button class="btn" type="primary" size="medium" @click="installPWA">立即添加</el-button>
                <div class="line"></div>
            </div>
            <div class="collect">
                <div class="left">
                    <img :src="$imageDomain + '/icons/icon-collect.png'" class="icon-collect">
                </div>
                <div class="cont">
                    <div class="title">添加网页收藏夹</div>
                    <div class="desc">ctrl+D 添加T+至收藏夹</div>
                </div>
            </div>
        </div>
    </el-popover>
</div>
</template>

<script>
    export default {
        data() {
            return {
                installing: false,
                installAbled: false,
                pwaPromptEvent: null
            }
        },
        created() {
            this.isPWAEnv = (window.matchMedia('(display-mode: standalone)').matches) || (window.navigator.standalone) || document.referrer.includes('android-app://');
            if (!this.isPWAEnv && 'serviceWorker' in navigator) {
                window.addEventListener('beforeinstallprompt',  (e) => {
                    e.preventDefault();
                    this.pwaPromptEvent = e;
                    this.installAbled = true;
                });

                window.addEventListener('appinstalled',  () => {
                    this.installAbled = false;
                });
            }else{
                console.log('浏览器不支持PWA');
            }
        },
        methods: {
            async installPWA(){
                if (this.pwaPromptEvent !== null) {
                    this.pwaPromptEvent.prompt();
                    this.pwaPromptEvent.userChoice.then(result => {
                        if (result.outcome === 'accepted') {
                            console.log('同意安装应用');
                        } else {
                            console.log('不同意安装应用');
                        }
                        this.pwaPromptEvent = null;
                    }).catch(e=>{
                    
                    });
                }
            },    
        }
    }
</script>

<style lang="scss" scoped>
.quick-container{
    display: inline-block;
    width: auto;
}
.quick-btn{
    color: #d9d9d9;
    font-size: 14px;
    display: inline-flex;
    height: 38px;
    align-items: center;
    cursor: pointer;
    .icon path{
        fill: #d9d9d9
    }
}
.quick-content{
    text-align: center;
    padding: 12px;
    .install{
        .down-logo{
            width: 64px;
            margin: 0 auto
        }
        .title{
            color: #131315;
            font-size: 20px;
            margin-top: 20px;
        }
        .desc{
            color: #4E5969;
            font-size: 14px;
            padding-top: 12px;
        }
        .btn{
            font-size: 16px;
            height: 40px;
            width: 100%;
            margin-top: 24px;
        }
        .line{
            margin: 24px 0;
            height: 1px;
            background-color: #f2f3f5;
        }
    }
    .collect{
        height: 44px;
        .left{
            float: left;
            height: 100%;
            margin: 10px 0 0;
            .icon-collect{
                width: 24px;
            }
        }
        .cont{
            text-align: left;
            padding-left: 36px;
            .title{
                color:#4E5969;
                font-size: 16px;
            }
            .desc{
                color: #86909C;
                font-size: 12px;
                padding-top: 2px;
            }
        }
    }
}
</style>