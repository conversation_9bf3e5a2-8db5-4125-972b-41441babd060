<template>
  <div style="max-height: 70vh; overflow-y: auto">
    <el-form label-width="80px" label-position="left">
      <el-form-item label="客服账号">
        <el-select v-model="form_params.kf_id">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道分类">
        <el-select v-model="form_params.cate_id">
          <el-option
            v-for="item in qudaoList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform" :loading="isSubmiting"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>

export default {
  props: ['form', 'qudaoList', 'userList'],
  components: {
  },
  data () {
    return {
      form_params: {
        cate_id: '',
        kf_id: ''
      },
      isSubmiting: false
    }
  },
  created () {
    // this.form_params = Object.assign({}, this.form)
    for (const key in this.form) {
      if (Object.hasOwnProperty.call(this.form, key)) {
        this.form_params[key] = this.form[key]
      }
    }
  },
  methods: {
    subform () {
      let params = Object.assign({}, this.form_params)

      if (this.isSubmiting) return
      this.isSubmiting = true

      this.$http.editCrmScene(params).then(res => {
        if (res.status == 200) {
          this.$message.success("编辑成功");
          setTimeout(() => {
            this.isSubmiting = false
          }, 200);
          this.$emit("success")
        } else {
          this.isSubmiting = false
          this.$message.error("编辑失败");
        }
      }).catch(() => {
        this.isSubmiting = false
      })
    },
    cancel () {
      this.$emit("cancel")
    }


  }
}
</script>

<style lang ="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.el-input,
.el-select,
.el-textarea {
  width: 260px;
}
.title {
  padding: 0 28px 28px 0;
  font-size: 16px;
  color: #2e3c4e;
  font-weight: 600;
  &.padt20 {
    padding-top: 20px;
  }
}
.line {
  height: 1px;
  width: 100%;
  background: #d6d6d6;
}
.form-item-block {
  &.form_kefu {
    .el-button {
      margin-bottom: 5px;
    }
  }
  .avatar {
    width: 150px;
    height: 150px;
    object-fit: cover;
  }
  .to_select {
    display: inline-block;
    width: 60px;
    text-align: center;
    cursor: pointer;
    line-height: 1;
    padding: 7px 5px;
    border: 1px solid #409eff;
    color: #409eff;
    border-radius: 5px;
  }
  .tip {
    color: #8a929f;
    font-size: 12px;
    line-height: 1;
  }
  .tips {
    margin-top: 10px;
  }
}

.img_list {
  flex-wrap: wrap;
  .img {
    width: 120px;
    height: 120px;
    margin-right: 26px;
    position: relative;
    overflow: hidden;
    &.active {
      border: 1px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
</style>