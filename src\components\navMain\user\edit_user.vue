<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
		</div> -->
    <el-form label-width="130px" :model="user_info">
      <el-form-item label="真实姓名：">
        <el-input placeholder="请输入姓名" v-model="user_info.name"></el-input>
      </el-form-item>
      <el-form-item label="用户类型：">
        <el-select v-model="user_info.category">
          <el-option
            v-for="item in category_list"
            :key="item.id"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="案场方："
        v-if="website_info.website_mode_category == 0"
      >
        <el-radio v-model="user_info.is_case" :label="0">否</el-radio>
        <el-radio v-model="user_info.is_case" :label="1">是</el-radio>
        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">
            案场方经纪人所报备客户将直接为到访状态，项目助理也可把普通经纪人报备客户分配给案场经纪人跟进，一般案场经纪人为项目方的置业顾问，根据实际需求设置。
          </div>
          <el-button
            style="margin-left: 10px"
            type="danger"
            size="mini"
            class="el-icon-info"
            >说明</el-button
          >
        </el-tooltip>
      </el-form-item>
      <el-form-item label="置业顾问：" v-else>
        <el-radio v-model="user_info.is_case" :label="0">否</el-radio>
        <el-radio v-model="user_info.is_case" :label="1">是</el-radio>
        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">
            置业顾问报备客户直接为到访状态，项目助理可以把普通经纪人报备客户分配给置业顾问进行跟进。
          </div>
          <el-button
            style="margin-left: 10px"
            type="danger"
            size="mini"
            class="el-icon-info"
            >说明</el-button
          >
        </el-tooltip>
      </el-form-item>
      <el-form-item label="是否资料查询员：">
        <el-radio v-model="user_info.is_searcher" :label="0">否</el-radio>
        <el-radio v-model="user_info.is_searcher" :label="1">是</el-radio>
      </el-form-item>
      <el-form-item label="是否渠道经理：">
        <el-radio v-model="user_info.is_channel_manager" :label="0"
          >否</el-radio
        >
        <el-radio v-model="user_info.is_channel_manager" :label="1"
          >是</el-radio
        >
        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">
            渠道经理可以查看所有公司及下属门店的统计数据
          </div>
          <el-button
            style="margin-left: 10px"
            type="danger"
            size="mini"
            class="el-icon-info"
            >说明</el-button
          >
        </el-tooltip>
      </el-form-item>
      <el-form-item
        label="绑定项目："
        v-if="user_info.is_case === 1 || user_info.category == 3"
      >
        <el-select
          v-model="user_info.project_ids"
          filterable
          multiple
          value-key="name"
          remote
          placeholder="请输入项目名称"
          :remote-method="getCommonProjectList"
          :loading="build_loading"
          @change="onBuild"
        >
          <el-option
            v-for="item in project_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司：">
        <el-select
          v-model="company_name"
          filterable
          remote
          reserve-keyword
          placeholder="请输入公司名称"
          :remote-method="getCompanyList"
          :loading="company_loading"
          @change="onCompany"
        >
          <el-option
            v-for="item in company_list"
            :key="item.company_id"
            :label="item.name"
            :value="item.company_id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="联系方式：">
        <el-input maxlength="11" v-model="user_info.phone"></el-input>
      </el-form-item>

      <el-form-item label="描述：">
        <el-input
          placeholder="请输入个人描述"
          type="textarea"
          v-model="user_info.description"
        ></el-input>
      </el-form-item>
      <el-form-item label="头像：">
        <el-upload
          :headers="myHeader"
          :action="user_avatar"
          :on-success="handleSuccessAvatar"
          :show-file-list="false"
          list-type="picture-card"
          :on-preview="handlePictureCardPreviewAvatar"
          :on-remove="handleRemoveAvatar"
        >
          <img
            v-if="user_info.avatar"
            :src="user_info.avatar"
            alt=""
            style="margin-top: 5px"
            height="148px"
            width="148px"
          />
          <i v-else class="el-icon-plus"></i>
        </el-upload>

        <el-dialog :visible.sync="ImageVisible">
          <img width="100%" :src="imageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item size="large">
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
import config from "@/utils/config";
import { mapState } from "vuex";
export default {
  name: "edit_user",
  data() {
    return {
      user_info: {
        id: "",
        category: "",
        company_id: "",
        phone: "",
        name: "",
        avatar: "",
        description: "",
        project_ids: "",
        is_case: 0,
        is_searcher: 0,
        is_channel_manager: 0,
      },
      company_name: "",
      category_list: [],
      company_list: [],
      ImageVisible: false,
      imageUrl: "",
      company_loading: false,
      type: false,
      user_avatar: `/api/common/file/upload/admin?category=${config.CLIENT_USER_AVATAR}`,
      project_list: [],
      build_loading: false,
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
    ...mapState(["website_info"]),
  },
  mounted() {
    this.user_info.id = this.$route.query.id;
    this.type = Boolean(this.$route.query.type) || false;
    this.userType = this.website_info.website_mode_category;
    this.getUserInfo();
    this.getCommonProjectList();
    console.log(this.userType);
  },
  methods: {
    getUserInfo() {
      this.$http.queryUserInfo(this.user_info.id).then((res) => {
        if (res.status === 200) {
          this.user_info.category = res.data.category + "";
          this.user_info.company_id = res.data.company_id;
          this.is_sotre = res.data.company_store_manager_user_id;
          //  如果用户id == 店长id 用户类型只能选择经纪人
          this.$http.dictionaryFind("USER_CATEGORY").then((res) => {
            if (this.user_info.id == this.is_sotre) {
              this.category_list = [
                { value: "1", description: "经纪人", id: 3 },
              ];
            } else {
              this.category_list = res.data.data;
            }
            // this.category_list = res.data.data;
            //   console.log(this.category_list);
          });
          this.user_info.avatar = res.data.avatar;
          this.user_info.description = res.data.description;
          this.user_info.name = res.data.name;
          this.user_info.phone = res.data.phone;
          this.company_name = res.data.company_name;
          this.user_info.is_case = res.data.is_case;
          this.user_info.is_searcher = res.data.is_searcher;
          this.user_info.is_channel_manager = res.data.is_channel_manager;
          this.user_info.project_ids = res.data.projects.map((item) => {
            return item.id;
          });
        }
      });
    },
    getCompanyList(query) {
      this.company_loading = true;
      this.$http.searchCompanyCategory(query, 2).then((res) => {
        this.company_loading = false;
        this.company_list = res.data.data.map((item) => {
          return { name: item.name, company_id: item.id };
        });
      });
    },
    getCommonProjectList(build_name) {
      this.build_loading = true;
      this.$http
        .getCommonProjectList({ params: { name: build_name } })
        .then((res) => {
          this.build_loading = false;
          if (res.status === 200) {
            this.project_list = res.data;
          }
        });
    },
    goBack() {
      this.$router.back();
    },
    handleSuccessAvatar(response) {
      this.user_info.avatar = response.url;
    },
    handlePictureCardPreviewAvatar(file) {
      this.imageUrl = file.response.url;
      this.ImageVisible = true;
    },
    handleRemoveAvatar(response) {
      this.user_info.avatar = response.url;
    },
    onCompany(e) {
      this.user_info.company_id = e;
    },
    onBuild(e) {
      this.user_info.project_ids = e;
    },
    onSubmit() {
      if (this.company_name == "") {
        this.user_info.company_id = 0;
      }
      if (this.user_info.category == 1) {
        this.user_info.project_ids = "";
      } else {
        this.user_info.project_ids = this.user_info.project_ids.join(",") || "";
      }
      this.postData(this.user_info);
    },
    postData(form) {
      this.$http.updataUserInfo(form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "编辑成功",
            type: "success",
          });
          this.$store.state.reloadCon = true
          this.goBack();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-input {
  width: 200px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-form {
// 	padding-top: 30px;
// }
.el-input,
.el-select,
.el-textarea {
  width: 300px;
}
.tip {
  color: red;
}
</style>
