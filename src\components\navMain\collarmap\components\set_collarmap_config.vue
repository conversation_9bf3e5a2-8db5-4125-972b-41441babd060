<template>
<el-dialog title="设置规则" :visible.sync="show" width="786px">
    <el-form label-width="120px" style="padding-right: 40px" v-loading="loading">
 
        <el-form-item label="触达方式:" class="radio-block-wrapper">
            <el-radio-group v-model="params.type">
                <div v-for="type in typeList" :key="type.value" class="radio-block-row">
                    <div class="radio-block-row-radio"><el-radio :label="type.value">{{ type.label }}</el-radio></div>
                    <div class="radio-block-row-tips">{{ type.tips }}</div>
                </div>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="" v-if="params.type == 2">
            <span>转公/掉公时长超过&nbsp;</span>
            <el-input v-model.trim.number="params.num" style="width:100px"></el-input>
            <span>&nbsp;天，客户授权手机号，获客自动成为维护人。</span>
        </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>

<script>
export default {
    data(){
        return {
            show: false,        //dialog是否显示
            loading: false,
            successFn: null, 
            submiting: false,
            configData: null,
            params: {
                id: '',
                type: 1,
                num: 3
            },
            typeList: [
                { value: 1, label: '实时触达', tips: '客户授权手机号，新增或触达消息通知+分享获客（维护人）' },
                { value: 2, label: '沉睡唤醒', tips: '客户授权手机号，按规则新增或触达消息通知+分享获客（维护人）' },
                { value: 3, label: '静默触达', tips: '客户授权手机号，不发送消息，不成为维护人，只增加线索/轨迹记录' }
            ],
        }
    },
    methods: {
        //加载配置数据
        async loadConfig(fn){
            if(!this.configData){
                this.loading = true;
                const res = await this.$http.getMapPluginConfig();
                this.loading = false;
                if(res.status == 200){
                    this.configData = res.data;
                }
            }
            this.configData && fn(this.configData); 
        },
        open(){
            this.loadConfig((data) => {
                this.params.id = data.id;
                this.params.type = data.type;
                this.params.num = data.num || 3;
            });
            
            this.show = true;
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            if(this.params.type == 2){
                if(this.params.num === ''){
                    this.$message.error('转公/掉公时长超过天数不能为空');
                    return;
                }
                if(this.params.num <= 0){
                    this.$message.error('转公/掉公时长超过天数请填写大于0的数字');
                    return;
                }
            }

            const params = {...this.params};
            !params.id && delete params.id;
            this.submiting = true;
            const res = await this.$http.saveMapPluginConfig(params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.configData = null;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }  
}
</script>
<style lang="scss" scoped>
.radio-block-wrapper{
    ::v-deep .el-form-item__label{
        line-height: 1;
        padding-top: 10px;
    }
    .radio-block-row{
        font-size: 14px;
        padding: 10px 0;
        .radio-block-row-tips{
            color: #9c9c9c;
            padding: 5px 0 0 24px;
        }
    }
}
</style>