<template>
<div class="container">

    <div class="header">
        <el-button type="primary" @click="add" icon="el-icon-plus">添加</el-button>
    </div>

    <div class="body">
        <div class="tab-list">
            <draggable v-model="tabList" animation="300" style="width: 100%;">
                <transition-group>
                    <div v-for="row in tabList" :key="row.id" class="tab-item">
                        <span class="hand"><i class="el-icon-rank"></i></span>
                        <template v-if="row.is_default==0">
                            <span class="name" @click="edit(row)">{{row.name}}</span>
                            <span class="close" @click="remove(row)"><i class="el-icon-close"></i></span>
                        </template>
                        <template v-else>
                            <span class="name-disabled">{{row.name}}</span>
                            <span class="close-disabled"><small>内置</small></span>
                        </template>
                    </div>
                </transition-group>
            </draggable>
        </div>
    </div>

    <div class="footer">
        <div>
            <el-button type="primary" @click="coverTab" v-if="this.perms.coverTab" :loading="covering">应用到全员</el-button>
        </div>
        <div>
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="save" :loading="submiting">保存</el-button>
        </div>
    </div>
    

    <addTab v-if="dialogs.add" ref="add" :type="type"/>
</div>
</template>
<script>
import draggable from 'vuedraggable';
import addTab from './components/add_tab.vue';
export default {
    components: {
        addTab, draggable
    },
    props: {
        type: { type: Number, default: 1 }    //1:我的,2:公海，3：潜客
    },
    data() {
        return {
            loading: false,
            submiting: false,
            covering: false,
            list: [],
            originalList: [],
            dialogs: {
                add: false
            },
            //权限
            perms: {
                coverTab: false,        //覆盖tab
            }
        }
    },
    computed: {
        tabList: {
            get(){
                return this.list;
            },
            set(val){
                this.list = val;
            }
        }
    },
    created(){
        this.getList();
        this.checkRole();
    },
    methods: {
        //检查权限
        async checkRole(){
            const roleInfo = await this.$store.dispatch("queryMyRoleInfo");
            if(roleInfo){
                this.perms.coverTab = roleInfo.is_super ? true : false;
            }
        },
        //获取列表数据
        async getList(){
            this.loading = true;
            const res = await this.$http.getCrmCustomTabs({tab_type: this.type});
            this.loading = false;
            if(res.status == 200){
                this.list = res.data;
                this.originalList = [...this.list];
            }
        },
        remove(row){
            const index = this.list.findIndex(e=>e.id==row.id);
            if(index !== -1){
                this.list.splice(index, 1);
            }
        },
        //新增
        add(){
            this.edit({});
        },
        //编辑
        async edit(row){
            this.dialogs.add = true;
            await this.$nextTick();
            this.$refs.add.open(row).onSuccess(()=>{
                this.getList();
                this.$emit('dataChange');
            });
        },
        //应用到全员
        async coverTab(){
            try{
                if(this.list.length == 0){
                    this.$message.warning("请至少添加一个导航");
                    return;
                }
                await this.$confirm("确定要应用到全员吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })

                this.covering = true;
                const res = await this.$http.coverCrmCustomTab({tab_type: this.type});
                this.covering = false;
                if(res.status == 200){
                    this.$message.success(res.data?.msg || "应用全员成功");
                }
            }catch(e){
                this.covering = false;
            }
        },
        //保存
        async save(){
            //已删除的tabs
            const deletedTabs = this.originalList.filter(e=>!this.list.includes(e));
            //获取排序的tabs
            let sortTabs = [];
            let originalSortTabs = this.originalList.filter(e=>!deletedTabs.includes(e));
            for(let i = originalSortTabs.length-1; i >= 0; i--){
                if(originalSortTabs[i] !== this.list[i]){
                    let srotValue = i == originalSortTabs.length-1 ? 0 : this.list[i].sort;
                    sortTabs = this.list.slice(0, i+1).reverse().map(e => {
                        return e.sort == ++srotValue  ? null : {
                            id: e.id,
                            sort: srotValue
                        }
                    }).filter(e=>e);
                    break;
                }
            }

            
            //提交删除的 tabs
            this.submiting = true;
            let saveSuccessed = true,
                isDataChange = false;
            if(deletedTabs.length){
                const ids = deletedTabs.map(e=>e.id).join(',');
                const res = await this.$http.deleteCrmCustomTab(ids, this.type);
                saveSuccessed = res.status == 200
                isDataChange = true;
            }

            //保存排序的tabs
            if(sortTabs.length){
                const res = await this.$http.sortCrmCustomTab({sorts: JSON.stringify(sortTabs), tab_type: this.type});
                saveSuccessed = res.status == 200
                isDataChange = true;
            }
            this.submiting = false;
            if(saveSuccessed){
                this.$message.success('保存成功');
                this.$emit('close');
                //if(isDataChange){
                    this.$emit('dataChange');
                //}
            }
        }
    }
}
</script>
<style  scoped lang="scss">
.container{
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
    .header{
        padding: 12px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .body{
        flex: 1;
        overflow: auto;
        padding: 0 20px;
        .tab-list{
            display: flex;
            flex-wrap: wrap;
            .tab-item{
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 42px;
                margin: 10px 0 12px;
                border: 1px solid #e8e8e8;
                border-radius: 2px;
                font-size: 15px;
                color: #606266;
                cursor: move;
                padding-right: 12px;
                .hand{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    width: 40px;
                }
                .name, .name-disabled{
                    flex: 1;
                    display: flex;
                    align-items: center;
                    height: 100%;
                    overflow: hidden;
                    border-left: 1px solid #eee;
                    padding: 0px 12px;
                    margin-right: 12px;
                }
                .name{
                    cursor: pointer;
                    color: #409EFF;
                    transition: all .3s;
                    &:hover{
                        color: #409EFF;
                    }
                }
                .name-disabled{
                    color: rgba(0, 0, 0, 0.6);
                }
                .close-disabled{
                    color: rgba(0, 0, 0, 0.35);
                }
               .close{
                    cursor: pointer;
                    color: #999;
                    &:hover{
                        color: #3c3c3c;
                    }
                }
            }
        }
        
        .el-link{
            margin-right: 15px;
        }
    }
    .footer{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 15px;
        z-index: 666;
        border-top: 1px solid #eee;
    }
}

</style>