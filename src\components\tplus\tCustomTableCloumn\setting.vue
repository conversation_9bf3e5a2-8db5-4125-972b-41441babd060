<template>
<el-dialog :visible.sync="show" title="自定义列表" width="750px" @close="onClose">
    <div class="column-wrap">
        <el-card shadow="never" class="column">
            <div slot="header" class="column-header">
                可添加列
                <span class="op" @click="restoreDefault">恢复默认</span>
            </div>
            <div class="column-body">
                <el-checkbox-group v-model="seledColumnIds">
                    <el-checkbox v-for="col in allColumns" 
                        :key="col.id" :label="col.id" 
                        :disabled="col.must==1" class="column-check-item">
                        {{col.title}}
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </el-card>
        <div class="column-separate"></div>
        <el-card shadow="never" class="column">
            <div slot="header" class="column-header">
                已显示列 ( {{seledColumnLength}} )
            </div>
            <div class="column-body">

                <div class="column-seled-item column-seled-item-disabled" v-for="col in topSeledColumns" :key="col.id">
                    <i class="el-icon-lock icon-lock"></i><span class="title">{{col.title}}</span>
                </div>        
                
                <div class="column-seled-draggable">
                    <draggable v-model="middleSeledColumns" animation="300">
                        <transition-group>
                        <div class="column-seled-item column-seled-item-draggable" v-for="col in middleSeledColumns" :key="col.id">
                            <i class="el-icon-rank icon-drag"></i><span class="title">{{col.title}}</span>
                            <i class="el-icon-close icon-remove" @click="removeSeledColumn(col.id)" v-if="!col.must"></i>
                        </div>
                        </transition-group>
                    </draggable>
                </div>
                
                <div class="column-seled-item column-seled-item-disabled" v-for="col in bottomSeledColumns" :key="col.id">
                    <i class="el-icon-lock icon-lock"></i><span class="title">{{col.title}}</span>
                </div>   
            </div>
        </el-card>
    </div>     
         
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
</el-dialog>


</template>

<script>
import draggable from 'vuedraggable';
import Setting from './setting.js';
export default {
    name: 'tCustomTableCloumnSetting',
    components: {
        draggable
    },
    props: {
        tableName: { type: String, default: ''}
    },
    data(){
        return {
            show: false,        //dialog是否显示
            successFn: null,    //成功回调
            allColumns: [],
            seledColumnIds: [],
            sortedSeledColumnIds: [],
            originalSeledColumnIds: []
        }
    },
    computed: {
        //所有列id
        allColumnIds(){
            return this.allColumns.map(e => e.id);
        },
        //选中列
        seledColumns(){
            const columns = [];
            for(const id of this.sortedSeledColumnIds){
                const item = this.allColumns.find(e=>e.id == id);
                item && columns.push(item);
            }
            return columns;
        },
        topSeledColumns(){
            const columns = [];
            for(const col of this.seledColumns){
                if(col.fixed != 'left'){
                    break;
                }
                columns.push(col);
            }
            return columns;
        },
        bottomSeledColumns(){
            const columns = [];
            let seledColumns = this.seledColumns.slice(this.topSeledColumns.length).reverse();
            for(const col of seledColumns){
                if(col.fixed != 'right'){
                    break;
                }
                columns.push(col);
            }
            return columns.reverse();
        },
        middleSeledColumns:{
            set(cols){
                this.sortedSeledColumnIds.splice(this.topSeledColumns.length, cols.length, ...cols.map(e=>e.id));
            },
            get(){
                const columnIds = this.topSeledColumns.concat(this.bottomSeledColumns).map(e=>e.id);
                return this.seledColumns.filter(col => !columnIds.includes(col.id))
            }
        },
        seledColumnLength(){
            return this.seledColumnIds.length
        },
        //是否全选
        isSeledAll(){
            return this.seledColumnIds.length == this.allColumnIds.length;
        }
    },
    watch: {
        seledColumnIds(){
            this.setSortedSeledColumnIds();
        }
    },
    created(){
        
    },
    methods: {
        open(){
            this.getSettingData();
            this.show = true;
            return this;
        },
        cancle(){
            this.show = false;
        },
        onClose(){
            this.seledColumnIds = this.originalSeledColumnIds;
        },
        confirm(){
            this.saveSettingData();
        },
        onSuccess(fn){
            fn && (this.successFn = fn);
            return this;
        },
        //设置选中的ids
        setSortedSeledColumnIds(){
            const ids = this.seledColumnIds;
            const diffIds = ids.filter(id => !this.sortedSeledColumnIds.includes(id));
            if(diffIds.length){
                const pushIndex  = this.sortedSeledColumnIds.length - this.bottomSeledColumns.length;
                this.sortedSeledColumnIds.splice(pushIndex, 0, ...diffIds);
            }else{
                this.sortedSeledColumnIds = this.sortedSeledColumnIds.filter(id => ids.includes(id));
            }
        },
        //移除选中列
        removeSeledColumn(id){
            this.seledColumnIds = this.seledColumnIds.filter(e => e != id);
        },
        //恢复默认
        restoreDefault(){
            this.sortedSeledColumnIds = [];
            this.seledColumnIds = this.allColumnIds;
            if(this.isSeledAll){
                this.setSortedSeledColumnIds();
            }
        },
        //获取配置数据
        getSettingData(){
            Setting.getSettingData(this.tableName, (data) =>{
                this.allColumns = data.allColumns;
                this.seledColumnIds = data.seledColumnIds;
                this.originalSeledColumnIds = data.seledColumnIds;
            });
        },
        //保存配置数据
        saveSettingData(){
            if(!this.isSeledAll){
                const config = Setting.config(this.tableName);
                if(config.middleColumnMinCount > 0){
                    if(this.middleSeledColumns.length < config.middleColumnMinCount){
                        const count = config.middleColumnMinCount + this.topSeledColumns.length + this.bottomSeledColumns.length;
                        this.$message.error('至少选择 '+count+' 列');
                        return;
                    }
                }
            }

            const my_set = this.sortedSeledColumnIds.join(',');
            Setting.setSettingData(this.tableName, {my_set}, (res) =>{
                this.originalSeledColumnIds = this.sortedSeledColumnIds;
                this.$message.success(res?.data || '设置成功');
                this.successFn && this.successFn();
                this.show = false;
            });
        }
    },
}
</script>

<style scoped lang="scss">
.column-wrap{
    display: flex;
    flex-direction: row;
    .column{
        flex: 1;
        .column-header{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            .op{
                color: #409EFF;
                cursor: pointer;
            }
        }
        .column-body{
            height: 360px;
            overflow: auto;
            padding: 20px;
            .column-check-item{
                box-sizing: border-box;
                display: inline-block;
                min-width: 50%;
                margin: 0;
                padding: 6px 10px 6px 0;
            }
            .column-seled-draggable{
                margin: 10px 0;
            }
            .column-seled-item{
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                height: 28px;
                border-radius: 8px;
                background-color: #f6f6f6;
                +.column-seled-item{
                    margin-top: 10px;
                }
                .title{
                    flex: 1;
                    display: inline-block;
                    overflow: hidden;
                    max-height: 100%;
                }
                .icon-drag,.icon-remove,.icon-lock{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    width: 30px;
                }
                .icon-remove{
                    cursor: pointer;
                    color:#999;
                    &:hover{
                        color:#333
                    }
                }
                &.column-seled-item-disabled{
                    .icon-lock,.title{
                        color: rgba(0,0,0,.35);
                    }
                }
                &.column-seled-item-draggable{
                    cursor: move;
                }
            }
        }
    }
    .column-separate{
        width: 20px;
    }
}
::v-deep .el-card__header{
    padding: 10px 20px;
    background-color: #f4f4f4;
}
::v-deep .el-card__body{
    padding: 0;
}
</style>