<template>
  <div class="pages">
    <div class="bg-row div row">
      <div class="bg-col div row">
        <div class="left div row div row">
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/xinzen.png" alt="" />
          <span class="desc">7日新增客户</span>
        </div>
        <div class="right div row">
          <span class="number">{{ data_detail.agentDays7Count || 0 }}</span>
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/jt.png" alt="" />
        </div>
      </div>
      <div class="bg-col div row">
        <div class="left div row">
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/xinzen.png" alt="" />
          <span class="desc">上月新增客户</span>
        </div>
        <div class="right div row">
          <span class="number">{{ data_detail.agentLastMonthCount || 0 }}</span>
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/jt.png" alt="" />
        </div>
      </div>
      <div class="bg-col div row">
        <div class="left div row">
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/xinzen.png" alt="" />
          <span class="desc">累计新增客户</span>
        </div>
        <div class="right div row">
          <span class="number">{{ data_detail.agentCount || 0 }}</span>
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/jt.png" alt="" />
        </div>
      </div>
      <div class="bg-col div row">
        <div class="left div row">
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/khq.png" alt="" />
          <span class="desc">客户群</span>
        </div>
        <div class="right div row">
          <span class="number">{{ data_detail.agentGroupCount || 0 }}</span>
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/jt.png" alt="" />
        </div>
      </div>
      <div class="bg-col div row">
        <div class="left div row">
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/tdcy.png" alt="" />
          <span class="desc">团队成员</span>
        </div>
        <div class="right div row">
          <span class="number">{{ data_detail.adminUserCount || 0 }}</span>
          <img src="https://img.tfcs.cn/backup/static/admin/crm/index/jt.png" alt="" />
        </div>
      </div>
    </div>
    <div v-for="item in menu_list" :key="item.id">
      <div class="c-title">{{ item.name }}</div>
      <div class="bg-row div row">
        <div class="bg-col bg-box div row" v-for="i in item.sub" :key="i.id" @click.stop="onClick(i)">
          <div class="l-img">
            <img :src="i.icon" alt="" />
          </div>
          <div class="r-content">
            <div class="r-c-title">{{ i.name }}</div>
            <div class="r-c-desc">{{ i.desc }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "crm_customer_index",
  data() {
    return {
      menu_list: [
        {
          id: 1,
          name: "活码管理",
          sub: [
            {
              id: 1,
              name: "员工活码",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/yuangonghuoma/yghm.png",
              desc: "客户扫描该活码，系统会随机",
              path: "/crm_customer_member_qrcode",
            },
            {
              id: 2,
              name: "群活码",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/yuangonghuoma/qhm.png",
              desc: "客户扫描该活码，系统会随机",
              path: "/crm_customer_group_qrcode",
            },
          ],
        },
        {
          id: 2,
          name: "互动留存",
          sub: [
            {
              id: 1,
              name: "欢迎语",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/hyy.png",
              desc: "一键设置及发送信息",
              path: "/welcome_words",
            },
            {
              id: 2,
              name: "入群欢迎语",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/hyy.png",
              desc: "当外部联系人加入公司的客户...",
              path: "/group_welcome_words",
            },
            {
              id: 3,
              name: "批量加好友",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/pljhy.png",
              desc: "管理员可批量上传",
              path: "",
            },
            {
              id: 4,
              name: "文件库",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/wjk.png",
              desc: "管理员可维护企业文件库...",
              path: "/file",
            },
            {
              id: 5,
              name: "话术库",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/hsk.png",
              desc: "可统一配置公司的话术库",
              path: "/language",
            },
            {
              id: 6,
              name: "聊天侧边栏",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/ltcbl.png",
              desc: "配置到企业微信的聊天侧边栏",
              path: "/crm_slide",
            },
            {
              id: 7,
              name: "营销素材",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/hudongliucun/yxsc.png",
              desc: "营销素材的格式可以为文本...",
              path: "",
            },
          ],
        },
        {
          id: 3,
          name: "数据运营",
          sub: [
            {
              id: 1,
              name: "智能打标签",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/zndbq.png",
              desc: "根据需要可以给客户选择",
              path: "/crm_customer_labels",
            },
            {
              id: 2,
              name: "客户详情",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/khxq.png",
              desc: "查看客户的所有详细信息",
              path: "/crm_customer_group",
            },
            {
              id: 3,
              name: "管理CRM",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/glcrm.png",
              desc: "将企微中加的好友关联到CRM",
              path: "/crm_index",
            },
            {
              id: 4,
              name: "标准化SOP",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/bzhsop.png",
              desc: "帮助企业触达客户的流程标准化",
              path: "/crm_customer_user_sop",
            },
            {
              id: 5,
              name: "企微客户分析",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujuyunying/qwkhfx.png",
              desc: "统计企微客户数量变化、标签",
              path: "/crm_customer_crmlist",
            },
          ],
        },
        {
          id: 4,
          name: "数据风控",
          sub: [
            {
              id: 1,
              name: "消息存档",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujufengkong/xxcd.png",
              desc: "存储开通了消息存档的员工",
              path: "/crm_customer_message",
            },
            {
              id: 2,
              name: "违规提醒",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujufengkong/wgtx.png",
              desc: "记录监控的违规行为",
              path: "",
            },
            {
              id: 3,
              name: "流失提醒",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujufengkong/lstx.png",
              desc: "流失后的客户资料会提醒",
              path: "",
              message: "流失后的客户资料会提醒",
            },
            {
              id: 4,
              name: "删人提醒",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujufengkong/srtx.png",
              desc: "员工把客户删除好友后提醒",
              path: "",
              message: "员工把客户删除好友后提醒",
            },
            {
              id: 5,
              name: "超时未回复提醒",
              icon:
                "https://img.tfcs.cn/backup/static/admin/crm/shujufengkong/cswhftx.png",
              desc: "当客户发送了问题，超时会提醒",
              path: "",
            },
          ],
        },
      ],
      data_detail: {},
    };
  },
  mounted() {
    this.getWxWorkIndexData();
  },
  methods: {
    getWxWorkIndexData() {
      this.$http.getWxWorkIndexData().then((res) => {
        if (res.status === 200) {
          this.data_detail = res.data;
        }
      });
    },
    onClick(item) {
      if (item.message) {
        this.$message.warning(item.message);
        return;
      }
      if (!item.path) {
        this.$message.warning("后续即将开放...");
        return;
      }
      this.$goPath(item.path);
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .c-title {
    font-size: 18px;
    color: #2e3c4e;
    margin: 0 0 24px;
  }

  .bg-row {
    align-content: flex-start;
    flex-wrap: wrap;
  }

  .bg-col {
    margin-right: 10px;
    background: #fff;
    padding: 0 24px;
    width: 24%;
    height: 100px;
    border-radius: 4px;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-between;
    overflow: hidden;

    .left {
      align-items: center;
      color: #2e3c4e;

      img {
        width: 42px;
        height: 42px;
        margin-right: 16px;
      }
    }

    .right {
      align-items: center;
      font-size: 24px;

      img {
        margin-left: 8px;
      }
    }

    .l-img {
      img {
        width: 60px;
        height: 60px;
      }
    }

    .r-content {
      margin-left: 16px;

      .r-c-title {
        font-size: 18px;
        color: #2e3c4e;
      }

      .r-c-desc {
        color: #8a929f;
        margin-top: 10px;
        font-size: 14px;
        /* 文本不换行显示   nowrap  */
        white-space: nowrap;
        /* 文本超出部分隐藏 */
        overflow: hidden;
        /* 文本省略，必须配合 nowrap 和 hidden 使用*/
        text-overflow: ellipsis;
      }
    }

    // &:nth-child(5n) {
    //   margin-right: 0;
    // }
  }

  .bg-box {
    justify-content: flex-start;
  }
}
</style>
