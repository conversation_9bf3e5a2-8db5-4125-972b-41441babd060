<template>
<el-dialog :title="isAdd?'新增应收佣金':'编辑应收佣金'" :visible.sync="show" width="786px">
    <el-form label-width="120px" style="padding-right: 40px">
        <el-alert title="默认公式：应收佣金 = 成交金额 x 佣金点位 ± 渠道分佣 - 代扣税点。（如不满足业务需求请选择自定义输入）" type="warning"  :closable="false"/>
        <br>
        <el-form-item label="佣金金额:">
            {{params.amount}}<span> * </span>{{params.proportion}}%
            <template v-if="commissions.channelCommission != 0">
                {{ commissions.channelCommission > 0 ? '+' : '-'}} {{Math.abs(commissions.channelCommission).toFixed(2)}}(渠道分佣)
            </template>
            <template v-if="commissions.withholdTaxAmount != 0">
                - <span class="commission-text-red">{{ commissions.withholdTaxAmount.toFixed(2) }}</span>(代扣税点)
            </template>

            <span> = </span>
            <el-input v-model="params.commission" placeholder="佣金金额" style="width: 120px;">
            <i slot="suffix">元</i></el-input>
        </el-form-item>
        <el-form-item label="描述:">
            <el-input v-model="params.descp" placeholder="请输入描述" type="textarea"></el-input>
        </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>

<script>
export default {
    name: 'addCrmDealReceiveCommission',
    inject: {
        commissions: { from: 'commissions', default: () => {}}
    },
    data(){
        return {
            show: false,        //dialog是否显示
            successFn: null, 
            isAdd: true,
            submiting: false,
            params: {}
        }
    },
    methods: {
        open(params){
            this.params = {
                id: params.id || 0,
                report_id: params.report_id || 0,
                amount: params.amount || 0,
                proportion: params.proportion ?? '',
                commission: params.id ? params.commission : this.commissions.calcCommission,
                descp: params.descp ?? ''
            };
            
            this.isAdd = params.id ? false : true;
            this.show = true;
            console.log(params, this.params);
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            const params = {
                report_id: this.params.report_id,
                commission: this.params.commission,
                descp: this.params.descp
            };
            if(!this.isAdd){
                params.id = this.params.id;
            }
            this.submiting = true;
            const res = await this.$http[this.isAdd?'addReceiveCommissionAPI':'editReceiveCommissionAPI'](params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }
}
</script>
<style lang="scss" scoped>  
</style>