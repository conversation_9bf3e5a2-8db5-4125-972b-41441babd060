<template>
  <div>
    <div class="leaflet-tab">
      <el-tabs v-model="activeTab" type="card" :closable="isActiveTab" @tab-click="handleClick" @tab-remove="removeTab">
        <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name" />
      </el-tabs>
    </div>
    <div class="leaflet-body">
      <!-- <leafletList v-if="activeTab == '0'" @addTab="addTab"></leafletList>
      <addLeaflet v-if="activeTab == '1'" @changeLeafletStatus="changeLeafletStatus"></addLeaflet> -->
      <component :is="componentName" :sole="sole" :id="id" :leafletList="editLeafletList" @addTab="addTab"
        @changeLeafletStatus="changeLeafletStatus"></component>
    </div>
  </div>
</template>
<script>
import leafletList from './leafletList'
import addLeaflet from './addLeaflet'
import editLeaflet from './editLeaflet'
import caRecord from './caRecord'
export default {
  components: {
    leafletList,
    addLeaflet,
    editLeaflet,
    caRecord
  },
  data() {
    return {
      sole: '',
      id: 0,
      activeTab: '0',
      editableTabs: [{
        title: '挂载页列表',
        name: '0',
      }, {
        title: '添加挂载页',
        name: '1',
      }],
      componentName: 'leafletList',
      subTabName: '',
      editLeafletList: [],
      // caRecordList: []
    }
  },
  computed: {
    isActiveTab() {
      let activeTab = Number(this.activeTab)
      return activeTab >= 2 ? true : false
    }
  },
  // mounted() {

  // },
  methods: {
    handleClick(e) {
      if (e._props.label.indexOf('编辑') != -1) {
        this.componentName = 'editLeaflet'
        this.id = Number(e._props.name)
      } else if (e._props.label.indexOf('获客') != -1) {
        this.componentName = 'caRecord'
      }
      this.activeTab = e._props.name
      switch (this.activeTab) {
        case '0':
          this.componentName = 'leafletList'
          break;
        case '1':
          this.componentName = 'addLeaflet'
          break;

        default:
          break;
      }
    },
    changeLeafletStatus() {
      this.$message({
        message: '操作成功',
        type: 'success'
      });
      this.activeTab = '0'
      this.componentName = 'leafletList'
    },
    async addTab(e, type) {
      await this.setTabsList(e, type)
      if (type == 'editLeaflet') {
        this.subTabName = '编辑'
      }
      this.componentName = type
      this.id = e.id
      this.leafletEditData = e
      // let activeTab = this.editableTabs.length - 1
      // let newTabName = ++activeTab + '';
      let newTabName = e.id + '';
      this.activeTab = newTabName
      this.editableTabs.push({
        title: `${this.subTabName} ${e.title}`,
        name: newTabName
      });
    },

    removeTab(targetName) {
      let tabs = this.editableTabs;
      let activeName = this.activeTab;
      if (activeName == targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name == targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.name;
            }
          }
        });
      }
      this.activeTab = activeName;
      this.editableTabs = tabs.filter(tab => tab.name !== targetName);
    },
    // 管理编辑数据
    setTabsList(data, type) {
      const isNull = this.editLeafletList.some((item) => item.id == data.id)
      if (!isNull) {
        if (type == 'editLeaflet') {
          this.subTabName = '编辑'
          this.editLeafletList.push(data)
        }
      }
    }
  }
}
</script>
<style lang="scss"></style>