<template>
  <el-container>
    <el-header style="height:auto">
      <div class="title">操作提示</div>
      <div class="title-ctn">
        <p>所属户型为扩展内容，仅在类型为"样板间"时设置有效，可不填</p>
        <p>楼盘缩略图（位置、配套、实景、样板、规划、效果）</p>
        <p>图片上传建议尺寸：750*560</p>
      </div>
      <div class="btn-box">
        <el-button type="primary" @click="goBack">新盘列表</el-button>
        <!-- <el-button type="primary" @click="photoList">相册列表</el-button> -->
      </div>
    </el-header>
    <el-main>
      <div class="content" v-for="item in photo_list" :key="item.id">
        <div class="title">{{ item.description }}</div>
        <imgList
          :categoryImgList="category_img_list"
          :build_id="build_id"
          :category="item.value"
          @deleteImg="deleteImg"
        ></imgList>
        <el-upload
          :headers="myHeader"
          :action="build_img_type"
          :on-success="
            (res, file) => {
              return handleSuccess(res, file, item.value);
            }
          "
          multiple
          list-type="picture-card"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :show-file-list="false"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </div>
    </el-main>
  </el-container>
</template>

<script>
/* eslint-disable */
import imgList from "@/components/components/show_img";
import config from "@/utils/config";
export default {
  name: "setup_photo",
  components: { imgList },
  data() {
    return {
      form: {
        img: "",
      },
      photo_list: [],
      build_id: null,
      category: null,
      newArr: [],
      isShowImgs: false,
      dialogImageUrl: "",
      dialogVisible: false,
      build_img_type: `/api/common/file/upload/enterprise?category=${config.BUILD_IMG}`,

      // 图片分类内容
      category_img_list: [],
    };
  },
  mounted() {
    this.build_id = this.$route.query.id;
    this.photoList();
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("company_token"),
      };
    },
  },
  methods: {
    // 返回列表页
    goBack() {
      this.$goPath("/company_property_list");
    },
    photoList() {
      this.$http.companyGetImgList(this.build_id).then((res) => {
        this.category_img_list = res.data;
        this.getPhotoList();
      });
    },
    getPhotoList() {
      this.$http.getphototypeList("BUILD_IMG_CATEGORY").then((res) => {
        if (res.status === 200) {
          this.photo_list = res.data.data;
        }
      });
    },
    goPlanPhoto(id, value) {
      this.$goPath(`/plan_photo?id=${id}&category=${value}`);
    },
    // 上传文件
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response.url;
      this.dialogVisible = true;
    },
    handleSuccess(response, file, value) {
      this.form.img = response.url;
      if (this.form.img === "") {
        this.$message({
          message: "请上传图片",
          type: "error",
        });
        return;
      }
      this.$http
        .companyCreateBuildImg({
          build_id: this.build_id,
          category: value,
          img: this.form.img,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "上传成功",
              type: "success",
            });
            this.photoList();
            this.form.img = "";
            // this.$router.push("/property_list");
            return;
          }
        });
    },
    // 删除
    deleteImg(id) {
      this.$confirm("此操作将删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.companyDeleteImg(id).then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.photoList();
            }
          });
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-box {
  margin-top: 10px;
}
.title {
  color: #333;
  font-weight: bold;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;
  p {
    color: #748a8f;
    margin: 4px 0;
  }
  i {
    color: red;
  }
}
.el-main {
  .title {
    margin: 10px 0;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .bg-purple {
    background: #d3dce6;
  }
}
</style>
