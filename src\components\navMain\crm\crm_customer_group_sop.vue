<template>
  <!-- 客户群 -->
  <div class="pages" id="page">
    <el-row :gutter="20">
      <el-col :span="24">
        <topTips
          from="crm_customer_group_sop"
          @add="toAdd"
          :showBtn="true"
        ></topTips>
        <!-- <div class="content-box-crm padd0" style="margin-bottom: 24px">
          <div class="div row align-center">
            <div class="title flex-1">群SOP</div>
            <div class="add">
              <el-button type="primary" @click="toAdd" size="mini"
                >添加</el-button
              >
            </div>
          </div>
        </div> -->
        <div class="content-box-crm" style="margin-bottom: 24px; padding: 0">
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">发 送 者：</span>
            <div>
              <el-input
                placeholder="请选择发送者"
                v-model="creat_name"
                @focus="showMemberList"
              >
                <i
                  @click="delName"
                  slot="suffix"
                  class="el-input__icon el-icon-circle-close"
                ></i
              ></el-input>
            </div>
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px; padding: 0">
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">时间类型：</span>
            <div>
              <myLabel
                :arr="time_type_list"
                @onClick="onClickTimeType"
              ></myLabel>
            </div>
          </div>
          <div class="bottom-border div row" style="padding: 24px">
            <span class="text">时间筛选：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="p_time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="changeTimeRange"
            >
            </el-date-picker>
          </div>
          <!-- <div class="bottom-border div row" style="padding: 24px">
            <span class="text">触发条件：</span>
            <myLabel :arr="filter_list" @onClick="onClickFilter"></myLabel>
          </div> -->
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
            <div class="t-t-b-right div row"></div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
            <el-table-column label="任务名称">
              <template slot-scope="scope">
                {{ scope.row.task_name }}
              </template>
            </el-table-column>
            <el-table-column label="发送者">
              <template slot-scope="scope">
                <template v-if="scope.row.sender && scope.row.sender.length">
                  <span
                    v-for="(item, index) in scope.row.sender"
                    :key="index"
                    style="margin-right: 5px"
                    >{{ item }}</span
                  >
                </template>
              </template>
            </el-table-column>

            <el-table-column label="是否开启" v-slot="{ row }">
              <el-tag v-if="row.is_open == 1" type="success"> 已开启</el-tag>
              <el-tag v-if="row.is_open == 0" type="success"> 已关闭</el-tag>
            </el-table-column>
            <el-table-column label="员工发送情况" v-slot="{ row }">
              <template v-if="row.send_has_see">
                <div class="send_status">
                  <span> 已送达： </span>
                  <span class="success">{{ row.sendSuccessCount }}</span>
                  <span>人</span>
                </div>
                <div class="send_status">
                  <span> 未送达： </span>
                  <span class="warning">{{ row.sendFailCount }}</span>
                  <span>人</span>
                </div>
              </template>
              <template v-else>
                <el-link type="primary" @click="showsendLog(row, 1)">
                  查看数据
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="送达群聊">
              <template slot-scope="scope">
                <template v-if="scope.row.has_see">
                  <div class="send_status">
                    <span> 已送达： </span>
                    <span class="success">{{ scope.row.SuccessCount }}</span>
                    <span>个</span>
                  </div>
                  <div class="send_status">
                    <span> 未送达： </span>
                    <span class="warning">{{ scope.row.FailCount }}</span>
                    <span> 个 </span>
                  </div>
                </template>
                <template v-else>
                  <el-link type="primary" @click="showsendLog(scope.row, 2)">
                    查看数据
                  </el-link>
                </template>
              </template>
            </el-table-column>

            <el-table-column label="发送时间" prop="time_type" v-slot="{ row }">
              {{ row.time_type | filterTimeType(row) }}
            </el-table-column>
            <el-table-column
              label="创建时间"
              prop="created_at"
            ></el-table-column>
            <el-table-column label="操作" v-slot="{ row }">
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="onChangeEdit(row)"
                >编辑</el-link
              >
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="toSopLog(row)"
                >发送记录</el-link
              >

              <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteGroupSop(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>

    <el-dialog :visible.sync="show_add_dia" width="660px" title="添加群SOP">
      <add
        v-if="show_add_dia"
        @success="addOk"
        @cancel="show_add_dia = false"
      ></add>
    </el-dialog>
    <el-dialog :visible.sync="show_edit_dia" width="660px" title="编辑群SOP">
      <edit
        v-if="show_edit_dia"
        @success="editOk"
        :form="form_params"
        @cancel="show_edit_dia = false"
      ></edit>
    </el-dialog>
    <el-dialog :visible.sync="show_select_dia" width="660px" :title="title">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog>
    <el-dialog :visible.sync="show_preview" width="660px" title="图片预览">
      <div class="flex-row j-center align-center">
        <img :src="imgSrc" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "./components/my_label";
import QRCode from "qrcodejs2";
import add from "./components/group_sop/add";
import edit from "./components/group_sop/edit";
import memberListSingle from "../site/components/memberList_single.vue";
import topTips from "./components/top_tips.vue";
export default {
  name: "crm_customer_group_sop",
  components: {
    myLabel,
    add,
    topTips,
    memberListSingle,
    edit,
  },
  filters: {
    filterTimeType(val, params) {
      let type = "自定义";
      switch (val) {
        case 1:
          type = params.send_time;
          break;
        case 2:
          type = "每天";
          break;
        case 3:
          type = "每周";
          break;
        case 4:
          type = "每月";
          break;
        default:
          break;
      }
      return type;
    },
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        times: "",
        time_type: "",
      },
      time_list: [
        { id: 1, name: "全部", value: "" },
        { id: 1, name: "今天", value: "today" },
        { id: 2, name: "昨天", value: "yesterday" },
        { id: 3, name: "本周", value: "this_week" },
        { id: 4, name: "上周", value: "last_week" },
      ],
      // 1：自定义2：每天，3：每周，4：每月
      time_type_list: [
        { id: "", name: "全部" },
        { id: 1, name: "自定义" },
        { id: 2, name: "每天" },
        { id: 3, name: "每周" },
        { id: 4, name: "每月" },
      ],

      value: "",
      p_time: "",
      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
      show_download_dia: false,
      show_add_dia: false,
      show_edit_dia: false,
      show_select_dia: false,
      user_name: "",
      creat_name: "",
      title: "选择创建人",
      selectedIds: [],
      memberList: [],
      imgSrc: "",
      show_preview: false,
    };
  },
  mounted() {
    this.getDataList();
    this.getDepartment();
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmGroupSopList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    previewImg(src) {
      console.log(src);
      this.imgSrc = src;
      this.show_preview = true;
    },
    addOk() {
      this.show_add_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    editOk() {
      this.show_edit_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    toAdd() {
      this.show_add_dia = true;
    },
    onClickTime(item) {
      console.log(item.value);
      this.params.times = item.value;
      this.params.page = 1;
      this.getDataList();
    },
    onClickTimeType(e) {
      this.params.time_type = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onClickFilter(item) {
      console.log(item.id);
    },
    onClickEmployees(item) {
      console.log(item.id);
    },
    changeTimeRange(e) {
      if (e && e.length) {
        this.params.times = e.join(",");
        this.params.page = 1;
        this.getDataList();
      } else {
        this.params.times = "";
        this.params.page = 1;
        this.getDataList();
      }
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onClickDetail(id) {
      this.$router.push(`/crm_customer_group_detail?id=${id}`);
    },
    showsendLog(row, type) {
      this.$http
        .getCrmGroupSopStatus({ sop_type: type, sop_id: row.id })
        .then((res) => {
          if (res.status == 200) {
            if (type == 1) {
              row.send_has_see = true;
              this.$set(row, "sendFailCount", res.data.sendFailCount);
              this.$set(row, "sendSuccessCount", res.data.sendSuccessCount);
              // row.sendFailCount = res.data.sendFailCount
              // row.sendSuccessCount = res.data.sendSuccessCount
            } else if (type == 2) {
              row.has_see = true;
              this.$set(row, "FailCount", res.data.sendFailCount);
              this.$set(row, "SuccessCount", res.data.sendSuccessCount);
              // row.FailCount = res.data.sendFailCount
              // row.SuccessCount = res.data.sendSuccessCount
            }
            this.$forceUpdate();
          }
        });
    },
    deleteGroupSop(row) {
      this.$http
        .delCrmGroupSop(row.id)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("删除成功");
            this.params.page = 1;
            this.getDataList();
          } else {
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },
    copyLink(row) {
      this.$onCopyValue(row.qr_code);
    },
    download(row) {
      var link = document.createElement("a");

      link.href = row.qr_code;
      link.target = "_blank";
      // link.download = fileName;  //a标签的下载属性
      document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
      link.click();
      setTimeout(function() {
        document.body.removeChild(link);
      }, 100);
      // this.currentRow = row
      // this.show_download_dia = true
    },
    load(type) {
      let url =
        "https://work.weixin.qq.com/kfid/kfcbee406830c07097a?enc_scene=ENC2GuVNmhXUVzuJRkG8LtkYH";

      let width = 200,
        height = 200;
      switch (type) {
        case 1:
          width = 200;
          height = 200;
          break;
        case 2:
          width = 425;
          height = 425;
          break;
        case 3:
          width = 1417;
          height = 1417;
          break;
        default:
          width = 200;
          height = 200;
          break;
      }
      let fileName = "qrcode_" + +new Date();
      var div = document.createElement("div");
      var code = new QRCode(div, {
        text: url,
        height: height,
        width: width,
        // 425  1417
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
      //这里如果需要在页面上展示的话，就将div节点给添加到dom树上去；node.appendChild(div)
      let canvas = code._el.querySelector("canvas"); //获取生成二维码中的canvas，并将canvas转换成base64
      var base64Text = canvas.toDataURL("image/png");
      var blob = this.getBlob(base64Text); //将base64转换成blob对象

      //接下来就是下载了，主要的思路就是通过URL.createURL()方法把blob对象转换成url，然后绑定到a标签中的href上，通过a标签的下载属性来进行下载。
      if (navigator.msSaveBlob) {
        // IE的Blob保存方法
        navigator.msSaveBlob(blob, fileName);
      } else {
        var link = document.createElement("a");
        var href = window.URL.createObjectURL(blob);
        link.href = href;
        link.download = fileName; //a标签的下载属性
        document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
        link.click();

        // 延时保证下载成功执行，否则可能下载未找到文件的问题
        setTimeout(function() {
          window.URL.revokeObjectURL(href); // 释放Url对象
          document.body.removeChild(link);
        }, 100);
      }
      this.show_download_dia = false;
    },
    getBlob(base64) {
      var mimeString = base64
        .split(",")[0]
        .split(":")[1]
        .split(";")[0]; // mime类型
      var byteString = atob(base64.split(",")[1]); //base64 解码
      var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
      var intArray = new Uint8Array(arrayBuffer); //创建视图
      for (var i = 0; i < byteString.length; i += 1) {
        intArray[i] = byteString.charCodeAt(i);
      }
      return new Blob([intArray], {
        type: mimeString,
      });
    },
    async onChangeEdit(row) {
      let res = await this.$http.crmGroupSopDetail(row.id).catch(() => {
        this.$message.error("获取详情失败");
        return;
      });
      if (res.status == 200) {
        this.form_params = res.data;
        this.show_edit_dia = true;
      }
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    showMemberList(type = 0) {
      if (type == 1) {
        this.title = "选择绑定成员";
      } else {
        this.title = "选择创建人";
      }
      this.currentType = type;
      this.show_select_dia = true;
    },
    delName(type = 0) {
      if (type == 1) {
        //使用员工
        this.params.user_id = "";
        this.user_name = "";
      } else {
        this.params.sender = "";
        this.creat_name = "";
      }
      // this.params.wx_work_user_id = ''
      // this.username = ''
      this.params.page = 1;
      this.getDataList();
    },
    selecetedMember(e) {
      if (this.currentType == 1) {
        //使用员工
        if (e.checkedNodes && e.checkedNodes.length) {
          this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.user_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.user_name = "";
          this.params.user_id = "";
        }
      } else {
        if (e.checkedNodes && e.checkedNodes.length) {
          this.creat_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.sender = e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.creat_name = "";
          this.params.sender = "";
        }
      }

      this.show_select_dia = false;
      this.params.page = 1;
      this.getDataList();
    },
    toSopLog(row) {
      this.$goPath("/crm_customer_sop_log?id=" + row.id);
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .padd0 {
    padding: 0 24px;
    margin: 0 -20px;
    .title {
      padding: 15px 40px;
    }
  }
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
.send_status {
  color: #8a929f;
  font-size: 14px;
}
.success {
  color: #67c23a;
}
.warning {
  color: #e6a23c;
}
</style>
