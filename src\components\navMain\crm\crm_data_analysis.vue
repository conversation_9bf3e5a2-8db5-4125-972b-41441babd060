<template>
    <div class="container">
        <div class="header-box">
            <div class="header-title">
                数据分析
            </div>
            <!-- <div class="header-search flex-row">
                <div class="searchName">
                    <el-select style="width: 212px;" v-model="search_params.name" placeholder="案场名称">
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="searchTime">
                    <el-date-picker
                        v-model="search_params.time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
            </div> -->
            <div class="trend-title">接待录音量变化趋势</div>
            <div id="main" style="width: 100%; height: 300px;"></div>
        </div>
        <div class="footer">
            <el-row :gutter="21">
                <el-col :span="8">
                    <div class="footer-box">
                        <div class="footer-title flex-row">
                            <div class="flex-row align-center">
                                <div class="icon"></div>
                                排行榜（员工）
                            </div>
                            <div class="screen flex-row">
                                <div
                                    v-for="(item, index) in screen_day"
                                    :key="index"
                                    class="screen-box"
                                    :class="{'select': executeRate_params.times == item.value}"
                                    :style="executeRate_params.times == 'yesterday' 
                                    ? 'border-top-left-radius: 5px;border-bottom-left-radius: 5px;' : executeRate_params.times == 'month' 
                                    ? 'border-top-right-radius: 5px;border-bottom-right-radius: 5px;' : ''"
                                    @click="changExecuteRate(item)"
                                >
                                    {{ item.label }}
                                </div>
                            </div>
                        </div>
                        <div class="screen-main">
                            <el-row>
                                <el-col :span="6" style="text-align: center;">
                                    <div>排名</div>
                                </el-col>
                                <el-col :span="12" style="text-align: center;">
                                    <div>成员名称</div>
                                </el-col>
                                <el-col :span="6" style="text-align: center;">
                                    <div>执行率</div>
                                </el-col>
                            </el-row>
                            <el-row 
                                class="screen-content"
                                v-for="(item, index) in executeRate_list"
                                :key="index"
                            >
                                <el-col :span="6" style="text-align: center;">
                                    <div class="flex-row j-center">
                                        <div 
                                            class="screen-count"
                                            :class="index == 0 ? 'count1' : index == 1 ? 'count2' : index == 2 ? 'count3' : ''"
                                        >{{ index+1 }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="12" style="text-align: center;">
                                    <div>{{ item.admin_name }}</div>
                                </el-col>
                                <el-col :span="6" style="text-align: center;">
                                    <div>{{ item.p }}%</div>
                                </el-col>
                            </el-row>
                            <myEmpty v-show="!executeRate_list.length"></myEmpty>
                        </div>
                        <div>
                            
                        </div>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="footer-box">
                        <div class="footer-title flex-row">
                            <div class="flex-row align-center">
                                <div class="icon"></div>
                                排行榜（录音量）
                            </div>
                            <div class="screen flex-row">
                                <div
                                    v-for="(item, index) in screen_day"
                                    :key="index"
                                    class="screen-box"
                                    :class="{'select': recordQuantity_params.times == item.value}"
                                    :style="recordQuantity_params.times == 'yesterday' 
                                    ? 'border-top-left-radius: 5px;border-bottom-left-radius: 5px;' : recordQuantity_params.times == 'month' 
                                    ? 'border-top-right-radius: 5px;border-bottom-right-radius: 5px;' : ''"
                                    @click="changeRecordQuantity(item)"
                                >
                                    {{ item.label }}
                                </div>
                            </div>
                        </div>
                        <div class="screen-main">
                            <el-row>
                                <el-col :span="6" style="text-align: center;">
                                    <div>排名</div>
                                </el-col>
                                <el-col :span="12" style="text-align: center;">
                                    <div>成员名称</div>
                                </el-col>
                                <el-col :span="6" style="text-align: center;">
                                    <div>录音量</div>
                                </el-col>
                            </el-row>
                            <el-row 
                                class="screen-content"
                                v-for="(item, index) in recordQuantity_list"
                                :key="index"
                            >
                                <el-col :span="6" style="text-align: center;">
                                    <div class="flex-row j-center">
                                        <div 
                                            class="screen-count"
                                            :class="index == 0 ? 'count1' : index == 1 ? 'count2' : index == 2 ? 'count3' : ''"
                                        >{{ index+1 }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="12" style="text-align: center;">
                                    <div>{{ item.admin_name }}</div>
                                </el-col>
                                <el-col :span="6" style="text-align: center;">
                                    <div>{{ item.num }}</div>
                                </el-col>
                            </el-row>
                            <myEmpty v-show="!recordQuantity_list.length"></myEmpty>
                        </div>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="footer-box">
                        <div class="footer-title flex-row">
                            <div class="flex-row align-center">
                                <div class="icon"></div>
                                排行榜（录音时长）
                            </div>
                            <div class="screen flex-row">
                                <div
                                    v-for="item in screen_day"
                                    :key="item.value"
                                    class="screen-box"
                                    :class="{'select': recordDuration_params.times == item.value}"
                                    :style="recordDuration_params.times == 'yesterday' 
                                    ? 'border-top-left-radius: 5px;border-bottom-left-radius: 5px;' : recordDuration_params.times == 'month' 
                                    ? 'border-top-right-radius: 5px;border-bottom-right-radius: 5px;' : ''"
                                    @click="changeRecordDuration(item)"
                                >
                                    {{ item.label }}
                                </div>
                            </div>
                        </div>
                        <div class="screen-main">
                            <el-row>
                                <el-col :span="6" style="text-align: center;">
                                    <div>排名</div>
                                </el-col>
                                <el-col :span="12" style="text-align: center;">
                                    <div>成员名称</div>
                                </el-col>
                                <el-col :span="6" style="text-align: center;">
                                    <div>录音时长</div>
                                </el-col>
                            </el-row>
                            <el-row 
                                class="screen-content"
                                v-for="(item, index) in recordDuration_list"
                                :key="index"
                            >
                                <el-col :span="6" style="text-align: center;">
                                    <div class="flex-row j-center">
                                        <div 
                                            class="screen-count"
                                            :class="index == 0 ? 'count1' : index == 1 ? 'count2' : index == 2 ? 'count3' : ''"
                                        >{{ index+1 }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="12" style="text-align: center;">
                                    <div>{{ item.admin_name }}</div>
                                </el-col>
                                <el-col :span="6" style="text-align: center;">
                                    <div>{{ item.num | formatTime }}</div>
                                </el-col>
                            </el-row>
                            <myEmpty v-show="!recordDuration_list.length"></myEmpty>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script>
import * as echarts from "echarts";
import myEmpty from "@/components/components/my_empty.vue";
export default {
    components: {
        myEmpty,
    },
    data() {
        return {
            search_params: {
                name: "",
                time: "",
            },
            options: [],
            // 排行榜 按照天筛选参数
            screen_day: [
                {label: '昨日', value: 'yesterday'},
                {label: '近7日', value: 'week'},
                {label: '近30日', value: 'month'},
            ],
            executeRate_list: [], // 话术执行率排行榜
            // 话术执行率排行榜请求参数
            executeRate_params: {
                times: 'yesterday', // yesterday：昨日，week：近七天，month：近30天
            },
            recordQuantity_list: [], // 录音量排行榜
            // 录音量排行榜请求参数
            recordQuantity_params: {
                times: 'yesterday',
            },
            recordDuration_list: [], // 录音时长排行榜
            // 录音时长排行榜请求参数
            recordDuration_params: {
                times: 'yesterday',
            },
            echarts_time: [], // echarts中x轴数据
            echarts_duration: [], // echarts中y轴录音总时长
            echarts_item: [], // echarts中y轴录音数量
        }
    },
    created() {
        this.getVerbalExecuteRate(); // 获取话术执行率排行榜
        this.getRecordQuantity(); // 获取录音量排行榜
        this.getRecordDuration(); // 获取录音时长排行榜
        this.getAnalyseChart(); // 获取数据分析折线图
    },
    filters: {
        formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    },
    methods: {
        // 初始化数据分析折线图
        initialize() {
            var chartDom = document.getElementById("main");
            var myChart = echarts.getInstanceByDom(chartDom);
            if(myChart == null) {
                myChart = echarts.init(chartDom)
            }
            var option;
            option = {
                tooltip: {
                    show: true,
                    trigger: 'axis',
                    textStyle: {
                        fontSize: 12,
                    },
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '10%',
                    containLabel: false
                },
                legend: {
                    data: ['录音量', '录音时长'],
                    x: '1160',
                    y: '28'
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisTick: {
                        show: true,
                        length: 5,
                        lineStyle: {
                            color: "#8A8A8A"
                        }
                    },
                    data: this.echarts_time
                },
                yAxis: [
                    {
                        type: 'value', 
                        name: "录音量（条数）",
                        // 坐标轴轴线相关设置
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#8A8A8A"
                            }
                        },
                        // 坐标轴刻度相关设置
                        axisTick: {
                            show: true,
                            length: 5,
                            lineStyle: {
                                color: "#8A8A8A"
                            }
                        },
                        splitLine: {
                            show: false, // 去掉折线图中的横线
                        },
                    },
                    {
                        type: 'value',
                        name: '录音时长（分钟）',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#8A8A8A'
                            }
                        },
                        axisTick: {
                            show: true,
                            length: 5,
                            lineStyle: {
                                color: "#8A8A8A"
                            }
                        },
                        splitLine: {
                            show: false
                        },
                    }
                ],
                series: [
                    {
                        name: '录音量',
                        data: this.echarts_item,
                        type: 'line',
                        symbol: 'none',
                        lineStyle: {
                            color: '#2D84FB', // 折线线条颜色:红色
                        }
                    },
                    {
                        name: '录音时长',
                        yAxisIndex: 1,
                        data: this.echarts_duration,
                        symbol: 'none',
                        type: 'line',
                        lineStyle: {
                            color: '#0BDF6D'
                        }
                    }
                ]
            }
            option && myChart.setOption(option);
            window.addEventListener('resize', function () {
                myChart.resize()
            })
        },
        // 筛选录音时长排行榜
        changeRecordDuration(item) {
            this.recordDuration_params.times = item.value; // 赋值当前点击检索
            this.getRecordDuration();
        },
        // 筛选话术执行率排行榜
        changExecuteRate(item) {
            this.executeRate_params.times = item.value;
            this.getVerbalExecuteRate();
        },
        // 筛选录音量排行榜
        changeRecordQuantity(item) {
            this.recordQuantity_params.times = item.value;
            this.getRecordQuantity();
        },
        // 获取话术执行率排行榜
        getVerbalExecuteRate() {
            this.$http.getVerbalExecuteRate(this.executeRate_params).then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"执行率数据");
                    this.executeRate_list = res.data.list;
                }
            })
        },
        // 获取录音量排行榜
        getRecordQuantity() {
            this.$http.getRecordQuantity(this.recordQuantity_params).then((res) => {
                if(res.status == 200) {
                    // console.log(res.data, "录音量");
                    this.recordQuantity_list = res.data.list;
                }
            })
        },
        // 获取录音时长排行榜
        getRecordDuration() {
            this.$http.getRecordDuration(this.recordDuration_params).then((res) => {
                if(res.status == 200) {
                    this.recordDuration_list = res.data.list;
                }
            })
        },
        // 获取数据分析折线图
        async getAnalyseChart() {
            await this.$http.getAnalyseChart().then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"折线图数据");
                    res.data.list.map((item) => {
                        this.echarts_time.push(item.start_time); // 赋值x轴时间数据
                        this.echarts_duration.push(item.total_duration); // 赋值y轴总时长
                        this.echarts_item.push(item.total_item); // 赋值y轴录音数量
                    })
                }
            })
            this.initialize(); // 初始化数据分析折线图
        }
    }
}
</script>
<style lang="scss" scoped>
.container {
    margin: -15px;
    padding: 20px;
    background: #F1F4FA;
    .header-box {
        padding: 26px 35px 20px;
        box-sizing: border-box;
        background: #FFFFFF;
        .header-title {
            color: #1C212A;
            font-size: 24px;
        }
        .header-search {
            margin-top: 25px;
            .searchName {
                margin-right: 20px;
                .el-select {
                    .el-input {
                        .el-input__inner {
                            width: 212px;
                        }
                    }
                }
            }
            .searchTime {
                .el-input__inner {
                    width: 257px;
                }
            }
        }
        .trend-title {
            color: #2E3C4E;
            font-size: 16px;
            margin-top: 20px;
        }
    }
    .footer {
        margin-top: 20px;
        .footer-box {
            background: #FFFFFF;
            .footer-title {
                height: 80px;
                padding: 0 20px 0 35px;
                justify-content: space-between;
                align-items: center;
                font-size: 18px;
                color: #2E3C4E;
                border-bottom: 1px solid #DDE1E9;
                .icon {
                    width: 6px;
                    height: 16px;
                    background: #2D84FB;
                    margin-right: 7px;
                }
                .screen {
                    border: 1px solid #DDE1E9;
                    border-radius: 5px;
                    .screen-box {
                        color: #2E3C4E;
                        font-size: 14px;
                        padding: 10px 20px;
                        cursor: pointer;
                    }
                    .screen-box:nth-child(2) {
                        border-left: 1px solid #DDE1E9;
                        border-right: 1px solid #DDE1E9;
                    }
                    .select {
                        background: #2D84FB;
                        color: #FFFFFF;
                    }
                }
            }
            .screen-main {
                height: 260px;
                overflow-y: auto;
                margin-top: 20px;
                color: #464D59;
                font-size: 15px;
                padding-bottom: 17px;
                .screen-content {
                    margin-top: 20px;
                    color: #2E3C4E;
                    font-size: 15px;
                }
                .screen-count {
                    width: 16px;
                    height: 16px;
                    font-size: 12px;
                    border-radius: 2px;
                    background: #AFAFAF;
                    color: #FFFFFF;
                }
                .count1 {
                    background: #FA6060;
                }
                .count2 {
                    background: #FA8560;
                }
                .count3 {
                    background: #FAAA60;
                }
            }
        }
    }
}
</style>