<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm flex-row align-center" style="padding: 0 0 15px 24px">
          <div class="crm_top div row flex-1">
            <div class="title flex-1">消息存档</div>
          </div>
          <div class="search flex-row align-center">
            <el-input v-model="params.username" placeholder="搜索参与人" size="mini"
              style="width: 200px; margin-right: 15px"></el-input>
            <el-input v-model="params.huihua" placeholder="搜索会话" size="mini"
              style="width: 200px; margin-right: 15px"></el-input>
            <el-input v-model="params.log" placeholder="搜索聊天记录" size="mini"
              style="width: 200px; margin-right: 15px"></el-input>
            <el-button size="mini" icon="el-icon-search"></el-button>
          </div>
        </div>
        <div class="content-box-crm bg_hui flex-row" style="height: calc(100vh - 44px - 48px - 15px - 105px - 30px)">
          <div class="left">
            <div class="left_top flex-row align-center">
              <div class="img">
                <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
              </div>
              <div class="name">张三</div>
            </div>
            <div class="menu">
              <el-menu default-active="2" class="el-menu-vertical-demo">
                <!-- @open="handleOpen"
                @close="handleClose" -->
                <el-submenu :index="item.id" v-for="item in menuData" :key="item.id">
                  <template slot="title">
                    <img class="group_img" src="@/assets/webimg/group.png" alt="" />
                    <!-- <i class="el-icon-location"></i> -->
                    <span> {{ item.title }}</span>
                  </template>
                  <template v-if="item.child && item.child.length">
                    <el-menu-item :index="child.id" v-for="child in item.child" :key="child.id" @click="clickItem(child)">
                      <template slot="title">
                        <img class="customer_img"
                          src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
                        <span> {{ child.title }}</span>
                      </template>
                    </el-menu-item>
                  </template>
                </el-submenu>
              </el-menu>
            </div>
          </div>
          <div class="right flex-1">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="right_top flex-row align-center">
                  <div class="user_img">
                    <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
                  </div>
                  <div class="user_info">
                    <div class="user_name flex-row">
                      <div class="name_con">张三</div>
                      <div class="name_after">@XSE测试</div>
                    </div>
                    <div class="label flex-row">
                      <div class="label_con">张三</div>
                      <div class="label_con">@XSE测试</div>
                    </div>
                  </div>
                </div>
                <div class="message_box flex-row">
                  <div class="message_box_list_box flex-1" ref="chat_list_box">
                    <div class="status flex-row align-center">
                      <div class="line flex-1"></div>
                      <div class="no_more status_con" v-if="getmore === 'nomore'">
                        没有更多了
                      </div>
                      <div class="no_more status_con" v-else-if="getmore === 'loading'">
                        加载中...
                      </div>
                      <div class="get_more status_con" v-else @click="getMore()">
                        点击查看更多
                      </div>
                      <div class="line flex-1"></div>
                    </div>

                    <div class="message_list" ref="chat_list">
                      <div class="message_item" v-for="chat in chatLogs" :key="chat.id">
                        <!-- <div
                          class="time"
                          v-if="
                            index === 0 ||
                            (index > 1 &&
                              chat.ctime - chatLogs[index - 1].ctime > 60)
                          "
                        >
                          <span>{{ chat.time }}</span>
                        </div> -->
                        <div class="friend_chat" v-if="chat.mess_id != id">
                          <div class="header_box flex-row">
                            <!-- <img :src="chat.headimage" alt="" /> -->
                            <div class="name">{{ chat.username }}</div>
                            <div class="time">{{ chat.time }}</div>
                          </div>
                          <div class="info" v-if="chat.type === 'text'">
                            <span v-html="chat.content"></span>
                            <i class="point"></i>
                          </div>
                          <div class="img" v-if="chat.type === 'image'">
                            <img :src="chat.content.img" alt="" :style="{
                                                            width:
                                                              (chat.content.width > 220
                                                                ? 220
                                                                : chat.content.width) + 'px',
                                                            height:
                                                              (chat.content.width > 220
                                                                ? (220 * chat.content.height) /
                                                                  chat.content.width
                                                                : chat.content.height) + 'px',
                                                          }" />
                            <i class="point"></i>
                          </div>
                          <div style="clear: both"></div>
                        </div>
                        <div class="my_chat" v-if="chat.mess_id == id">
                          <div class="header_box flex-row">
                            <!-- <img :src="chat.headimage" alt="" /> -->
                            <div class="name">{{ chat.username }}</div>
                            <div class="time">{{ chat.time }}</div>
                          </div>
                          <div class="info" v-if="chat.type === 'text'">
                            <span v-html="chat.content"></span>
                            <!-- <i class="point"></i> -->
                          </div>
                          <div class="img" v-if="chat.type === 'image'">
                            <img :src="chat.content.img" alt="" :style="{
                                                            width:
                                                              (chat.content.width > 220
                                                                ? 220
                                                                : chat.content.width) + 'px',
                                                            height:
                                                              (chat.content.width > 220
                                                                ? (220 * chat.content.height) /
                                                                  chat.content.width
                                                                : chat.content.height) + 'px',
                                                          }" />
                            <i class="point"></i>
                          </div>
                          <div style="clear: both"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="message_history" style="width: 432px">
                    <div class="filters flex-row">
                      <div class="filter_item">日期</div>
                      <div class="filter_item">文件</div>
                      <div class="filter_item">图片/视频</div>
                    </div>
                    <div class="search flex-row align-center">
                      <el-input style="width: 280px; margin-right: 10px" v-model="content">
                        <i slot="suffix" @click="search" class="el-input__icon el-icon-search search_icon"></i>
                      </el-input>
                      <div class="date">日期</div>
                    </div>
                    <div class="status flex-row align-center">
                      <div class="line flex-1"></div>
                      <div class="no_more status_con" v-if="getmore === 'nomore'">
                        没有更多了
                      </div>
                      <div class="no_more status_con" v-else-if="getmore === 'loading'">
                        加载中...
                      </div>
                      <div class="get_more status_con" v-else @click="getMore()">
                        点击查看更多
                      </div>
                      <div class="line flex-1"></div>
                    </div>
                    <div class="message_list" ref="chat_list">
                      <div class="message_item" v-for="chat in chatLogs" :key="chat.id">
                        <div class="friend_chat">
                          <div class="header_box flex-row">
                            <!-- <img :src="chat.headimage" alt="" /> -->
                            <div class="name">{{ chat.username }}</div>
                            <div class="time">{{ chat.time }}</div>
                          </div>
                          <div class="info" v-if="chat.type === 'text'">
                            <span v-html="chat.content"></span>
                            <i class="point"></i>
                          </div>
                          <div class="img" v-if="chat.type === 'image'">
                            <img :src="chat.content.img" alt="" :style="{
                                                            width:
                                                              (chat.content.width > 220
                                                                ? 220
                                                                : chat.content.width) + 'px',
                                                            height:
                                                              (chat.content.width > 220
                                                                ? (220 * chat.content.height) /
                                                                  chat.content.width
                                                                : chat.content.height) + 'px',
                                                          }" />
                            <i class="point"></i>
                          </div>
                          <div style="clear: both"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "crm_message_detail",
  data() {
    return {
      params: {
        username: "",
        huihua: "",
        log: "",
      },
      menuData: [
        {
          title: "企微客户",
          id: "1",
          child: [
            {
              title: "张三",
              id: "2",
            },
            {
              title: "李四",
              id: "3",
            },
          ],
        },
        {
          title: "企微客户1",
          id: "4",
          child: [
            {
              title: "张三1",
              id: "5",
            },
            {
              title: "李四1",
              id: "6",
            },
          ],
        },
      ],
      getmore: "",
      id: 2, //当前页面用户id
      to_id: "", //左侧菜单选中的id
      chatLogs: [
        {
          username: "张三",
          time: "2020-08-21 08:15:10",
          type: "text",
          content: "12323213",
          mess_id: 1,
          id: 1,
        },
        {
          username: "李四",
          time: "2020-08-21 08:15:10",
          type: "text",
          content:
            "测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复",
          mess_id: 2,
          id: 2,
        },
        {
          username: "张三",
          time: "2020-08-21 08:15:10",
          type: "image",
          content: {
            img:
              "https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80",
          },
          mess_id: 1,
          id: 3,
        },
      ],
      content: "",
    };
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.scroolBottom();
    });
  },
  methods: {
    clickItem(item) {
      console.log(item);
    },
    search() {
      console.log(1233123);
    },
    // 将聊天内容滚动到最底部
    scroolBottom: function() {
      var chat_list_box = this.$refs.chat_list_box;
      var chat_list = this.$refs.chat_list;
      console.log(chat_list, chat_list_box);
      if (chat_list_box) {
        chat_list_box.scrollTop = chat_list.scrollHeight;
      }
    },
    getMore() {
      setTimeout(() => {
        let data = [
          {
            username: "张三",
            time: "2020-08-21 08:15:10",
            type: "text",
            content: "12323213",
            mess_id: 1,
            id: Math.random(),
          },
          {
            username: "李四",
            time: "2020-08-21 08:15:10",
            type: "text",
            content:
              "测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复测试自动回复",
            mess_id: 2,
            id: Math.random(),
          },
          {
            username: "张三",
            time: "2020-08-21 08:15:10",
            type: "image",
            content: {
              img:
                "https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80",
            },
            mess_id: 1,
            id: Math.random(),
          },
        ];
        var preHeight;
        preHeight = this.$refs.chat_list.scrollHeight || 0;
        this.chatLogs = data.concat(this.chatLogs);
        this.$nextTick(function() {
          var chat_list_box = this.$refs.chat_list_box;
          var chat_list = this.$refs.chat_list;
          chat_list_box.scrollTop = chat_list.scrollHeight - preHeight;
        });
      }, 300);
    },
  },
};
</script>

<style lang="scss" scoped>
.crm_top {
  .title {
    padding-bottom: 0;
    border: 0;
  }
}

.content-box-crm {
  &.bg_hui {
    background: #f1f4fa;
  }

  .left {
    background: #fff;
    padding: 24px;
    margin-right: 24px;

    .left_top {
      .img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .name {
        color: #2e3c4e;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .menu {
      min-width: 220px;
      margin-top: 15px;
      min-height: 300px;

      ::v-deep .el-menu {
        border-right: 0;
      }

      .group_img {
        height: 20px;
        width: 20px;
        margin-right: 5px;
        object-fit: cover;
      }

      .customer_img {
        width: 30px;
        height: 30px;
        margin-right: 5px;
        object-fit: cover;
        border-radius: 50%;
      }
    }
  }

  .right {
    background: #fff;

    .right_top {
      padding: 24px;
      border-bottom: 1px solid #e2e2e2;

      .user_img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right: 15px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .user_info {
        .user_name {
          .name_con {
            color: #2e3c4e;
            font-family: PingFang SC;
            font-weight: medium;
            font-size: 18px;

            font-weight: 600;
          }

          .name_after {
            color: #8a929f;
            font-family: PingFang SC;
            font-weight: regular;
            margin-left: 10px;
            font-size: 14px;
          }
        }

        .label {
          margin-top: 10px;

          .label_con {
            color: #8a929f;
            padding: 1px 6px;
            margin-right: 6px;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 14px;
            border-radius: 2px;
            background: #f1f1f1;
          }
        }
      }
    }
  }
}

.status {
  padding: 20px 24px;

  .line {
    height: 1px;
    background: #e2e2e2;
  }

  .status_con {
    padding: 0 20px;
    color: #8a929f;
    font-size: 14px;
  }
}

.message_list {
  padding: 10px 24px;
  // height: calc(100vh - 44px - 48px - 15px - 105px - 30px - 158px - 59px);

  .message_item {
    .header_box {
      margin: 10px 0;

      .name {
        color: #2e3c4e;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
        margin-right: 15px;
      }

      .time {
        color: #8a929f;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
      }
    }

    .info {
      width: 80%;

      span {
        color: #2e3c4e;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 14px;
        display: inline-block;
        text-align: left;
        padding: 20px 33px;
        border-radius: 4px;
        background: #e9e9e9;
      }
    }

    .img {
      width: 220px;
      height: 220px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .my_chat {
      text-align: right;

      .header_box {
        justify-content: flex-end;
      }

      .info {
        float: right;
        // float: right;
      }

      .img {
        // float: right;
      }
    }
  }
}

.message_box {
  .message_box_list_box {
    height: calc(100vh - 44px - 48px - 15px - 105px - 30px - 158px);
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */

    &::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    border-right: 1px solid #e2e2e2;
  }
}

.message_history {
  padding: 24px 0;

  .filters {
    margin-left: 80px;
    margin-bottom: 10px;
    padding: 0 24px;

    .filter_item {
      padding: 10px 20px;
      border: 1px solid #dde1e9;
      border-radius: 4px;
      color: #8a929f;
      font-size: 14px;
      cursor: pointer;

      &.active {
        border: 1px solid #2d84fb;
        color: #2d84fb;
      }

      ~.filter_item {
        margin-left: 10px;
      }
    }
  }

  .search {
    margin-right: 10px;
    padding: 0 24px;

    .search_icon {
      cursor: pointer;
    }
  }

  .date {
    padding: 10px 12px;
    border: 1px solid #dde1e9;
    color: #8a929f;
    font-family: PingFang SC;
    font-weight: regular;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
  }

  .message_list {
    height: calc(100vh - 44px - 48px - 15px - 105px - 30px - 158px - 200px);
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */

    &::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }
  }
}
</style>
