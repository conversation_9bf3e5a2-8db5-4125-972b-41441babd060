<template>
  <!-- 部门列表 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="4">
        <div class="content-box-crm" style="padding: 0">
          <div class="bmtitle div row">
            <div class="title-bm">部门</div>
            <span @click="addDepartment" class="el-icon-plus" style="font-size: 20px"></span>
          </div>
          <div>
            <menuTree :menuData="navList" :keyData="keyData" :menu_list="menu_list" @clickItem="onClickNavItem"
              @onClickMenu="onClickMenu"></menuTree>
            <!-- <mySlide :navList="navList" @onClickItem="onClickNavItem"></mySlide> -->
          </div>
        </div>
      </el-col>
      <el-col :span="20">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <span class="text" style="font-size: 14px; color: #8a929f">添加成员：</span>
          <el-button type="primary" size="mini" class="el-icon-plus" @click="toAdd">添加</el-button>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
            </div>
          </div>
          <el-table v-loading="is_table_loading" :data="tableData" border class="table"
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="ID" min-width="30px" align="center" prop="id">
            </el-table-column>
            <el-table-column label="会员信息" min-width="120px" align="center" v-slot="{ row }">
              <div class="row-info flex-row align-center">
                <div class="row-img">
                  <img v-if="row.wx_work_avatar" :src="row.wx_work_avatar" alt="" />
                  <img v-else src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
                </div>
                <div class="info">
                  <div class="row-name flex-row align-center">
                    <div class="name">
                      {{ row.user_name }}
                    </div>
                    <div class="wechat" v-if="row.wx_work_user_id">
                      <img src="@/assets/wechat.png" alt="" />
                    </div>
                  </div>
                  <div class="row-info flex-row">
                    <div class="tel">
                      {{ row.phone }}
                    </div>
                  </div>
                </div>
              </div>
            </el-table-column>
            <el-table-column label="所属部门" min-width="60px" align="center" v-slot="{ row }">
              <span class="name" v-for="(item, index) in row.department" :key="index + '_'">{{ item.name }}</span>
            </el-table-column>
            <el-table-column label="职位" min-width="60px" align="center" v-slot="{ row }">
              <span class="name">{{ row.post }}</span>
            </el-table-column>
            <el-table-column label="操作" min-width="60px" align="center" v-slot="{ row }">
              <el-link type="primary" icon="el-icon-edit" @click="toEdit(row)">编辑</el-link>

              <el-popconfirm title="确定删除吗？" @onConfirm="deleteMember(row)">
                <el-link slot="reference" type="danger" icon="el-icon-delete">删除</el-link>
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next" :total="total"
            :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="show_edit_member_dia" width="660px" title="编辑员工">
      <editMember v-if="show_edit_member_dia" :form="form_params" :navList="navList" @success="editOk"
        @cancel="cancelEdit"></editMember>
    </el-dialog>

    <el-dialog :visible.sync="show_add_member_dia" width="660px" title="添加员工">
      <addMember :navList="navList" @success="addOk" @cancel="cancelAdd"></addMember>
    </el-dialog>

    <el-dialog :visible.sync="show_add_department" width="660px" title="添加子部门">
      <addDepartment v-if="show_add_department" :department_pid="department_pid" :options="navList"
        @success="addDepartmentOk" @cancel="cancelAddDepartment"></addDepartment>
    </el-dialog>
    <el-dialog :visible.sync="show_edit_department" width="660px" title="编辑部门">
      <editDepartment v-if="show_edit_department" :form="currentDepartment" @success="editDepartmentOk"
        @cancel="cancelEditDepartment"></editDepartment>
    </el-dialog>
  </div>
</template>

<script>
import menuTree from "./components/menuTree.vue";
import editMember from "./components/editMember.vue";
import addMember from "./components/addMember.vue";
import addDepartment from "./components/addDepartment.vue";
import editDepartment from "./components/editDepartment.vue";
import { Loading } from "element-ui";
export default {
  name: "crm_customer_department",
  components: {
    menuTree,
    editMember,
    addMember,
    addDepartment,
    editDepartment,
  },
  data() {
    return {
      navList: [],
      params: {
        page: 1,
        per_page: 10,
        user_name: "",
        phone: "",
        department_id: "",
      },
      form_params: {},
      total: 0,
      is_table_loading: false,
      multipleSelection: [],
      tableData: [],
      show_edit_member_dia: false,
      show_add_member_dia: false,
      menu_list: [
        { id: 1, name: "添加子部门" },
        { id: 2, name: "编辑" },
        { id: 3, name: "删除" },
      ],
      show_add_department: false,
      show_edit_department: false,
      department_pid: "",
      currentDepartment: {},
      defaultMenu: "",
      keyData: [],
    };
  },
  created() {
    //  获取部门
    this.getDepartmentList();
  },

  methods: {
    getDepartmentList() {
      this.is_table_loading = true;
      this.$http
        .getCrmDepartmentList()
        .then((res) => {
          if (res.status == 200) {
            this.navList = this.reFormData(res.data);
            if (res.data.length) {
              this.keyData.push(res.data[0].id);
              if (res.data[0].subs && res.data[0].subs.length) {
                this.keyData.push(res.data[0].subs[0].id);
              }
            }
            this.defaultMenu = res.data[0].id;
            this.params.page = 1;
            this.getMemberList();
          } else {
            this.is_table_loading = false;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    reFormData(data, line = "") {
      data.map((item) => {
        item.pArr = `${line ? line + "," : ""}${item.pid}`;
        if (item.subs && item.subs instanceof Array && item.subs.length) {
          let nameLine = `${line ? line + "," : ""}${item.pid}`;
          this.reFormData(item.subs, nameLine);
        }
      });
      return data;
      // for (let index = 0; index < data.length; index++) {
      //   this.pidArr.push()

      // }
    },
    // 获取员工列表  per_page=10&department_id=44&phone=13791416566&user_name=刘
    getMemberList() {
      let params = Object.assign({}, this.params);
      if (params.department_id == this.defaultMenu || !params.department_id) {
        delete params.department_id;
      }

      this.$http
        .getCrmMemberList({ params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status == 200) {
            this.tableData = res.data.data;
            this.total = res.data.total;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    search() {
      this.params.page = 1;
      this.is_table_loading = true;
      this.getMemberList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getMemberList();
    },
    onClickNavItem(e) {
      this.params.page = 1;
      this.params.department_id = e.id;
      this.openedArr = [e.pid + ""];
      this.getMemberList();
    },
    onClickMenu(e) {
      if (e.detail.id == 1) {
        // 添加子部门
        this.addDepartment(e.item);
      } else if (e.detail.id == 3) {
        this.delDepartment(e.item);
      } else if (e.detail.id == 2) {
        this.editDepartment(e.item);
      } else if (e.detail.id == 4) {
        // 员工列表
        this.params.department_id = e.item.id;
        this.getMemberList();
        // this.editDepartment(e.item)
      }
    },
    addDepartment(item) {
      if (item.id) {
        this.department_pid = item.id;
      }

      this.show_add_department = true;
    },
    editDepartment(item) {
      this.currentDepartment = item;
      // this.department_id = item.id
      this.show_edit_department = true;
    },
    editDepartmentOk() {
      this.getDepartmentList();
      this.show_edit_department = false;
    },
    cancelEditDepartment() {
      this.show_edit_department = false;
    },
    delDepartment(item) {
      this.$confirm(
        "请删除此部门下的成员或子部门后，再删除此部门, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.deleteDepartment(item.id);
        })
        .catch(() => { });
    },
    deleteMember(row) {
      this.$http.delCrmMember(row.id).then((res) => {
        if (res.status == 200) {
          this.$message.success("删除成功");
          this.getMemberList();
        } else {
          this.$message.success(res.message || "删除失败");
        }
      });
    },
    deleteDepartment(id) {
      this.$http.delCrmDepartment(id).then((res) => {
        if (res.status == 200) {
          this.$message.success("删除成功");
          this.getDepartmentList();
        } else {
          this.$message.success(res.message || "删除失败");
        }
      });
    },
    // 编辑
    toEdit(row) {
      let arr = [];
      row.department.map((item) => {
        arr.push(item.id);
      });
      this.form_params = row;
      this.form_params.wx_work_department_id = arr;
      this.title = "编辑员工";
      this.show_edit_member_dia = true;
    },
    cancelEdit() {
      this.show_edit_member_dia = false;
    },
    editOk() {
      this.getMemberList();
      this.show_edit_member_dia = false;
    },
    toAdd() {
      this.show_add_member_dia = true;
    },
    cancelAdd() {
      this.show_add_member_dia = false;
    },
    addOk() {
      this.getMemberList();
      this.show_add_member_dia = false;
    },
    cancelAddDepartment() {
      this.show_add_department = false;
    },
    addDepartmentOk() {
      this.show_add_department = false;
      this.getDepartmentList();
    },

    importConcat() {
      this.$confirm("此操作将同步企微通讯录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.loading = Loading.service({
          target: ".el-table",
          lock: true,
          text: "正在同步中 请稍后...", //可以自定义文字
          spinner: "el-icon-loading", //自定义加载图标类名
        });
        this.$http
          .importWxDepartment()
          .then((res) => {
            if (res.status == 200) {
              this.loading.close();
              this.loading = null;
              this.$message.success(res.message || "同步成功");
            }
          })
          .catch(() => {
            this.loading.close();
            this.loading = null;
          });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .top {
    align-items: center;
  }

  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }

  .bmtitle {
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 24px;
    border-bottom: 1px solid #eee;
  }

  .title-bm {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 18px;
  }
}

.table {
  .name {
    ~.name {
      margin-left: 8px;
    }
  }

  .row-info {
    .row-img {
      width: 30px;
      height: 30px;
      overflow: hidden;
      border-radius: 50%;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info {
      .row-name {
        .wechat {
          width: 15px;
          height: 15px;
          margin-left: 8px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
}
</style>
