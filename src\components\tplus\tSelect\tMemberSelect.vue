<template>
    <el-select :placeholder="placeholder" v-bind="$attrs" v-on="$listeners" :filterable="filterable" :loading="loading">
        <el-option v-for="item in list" :key="item.values" :label="item.name" :value="item.values"></el-option>
    </el-select>
</template>

<script>
let memberList = [];
export default {
    name: 'tMemberSelect',
    props: {
        datas: {type: Array, default: ()=>[]},
        filterable: {type: Boolean, default: true},
        placeholder: {type: String, default: "请选择同事"},
    },
    data(){
        return {
            loading:false,
            isDataLoaded: false,
            list: []
        }
    },
    watch: {
        datas: {
            handler(d){
                this.list = d;
            },
            immediate: true
        }
    },
    created(){
        this.initMenuData();
    },
    deactivated(){
        memberList = [];
    },
    methods: {
        //初始化菜单数据
        initMenuData(){
            /* if(!this.isDataLoaded){
                this.isDataLoaded =  this.list.length > 0;
            } */
            if(!this.isDataLoaded){
                this.isDataLoaded = true;
                if(memberList.length){
                    this.list = memberList
                }else{
                    this.loadData();
                }
            }
        },
        //获取菜单数据
        async loadData(keyword = ''){
            this.loading = true;
            try{
                const res = await this.$http.getColleagueDetailsList({ keywords: keyword });
                if(res.status == 200){
                    this.list = memberList = res.data || [];
                }
            }catch(e){
                console.error(e);
            }
            this.loading = false;
        },
        getOptionData(){
            return this.list.map(item => {
                return {
                    label: item.name,
                    value: item.values
                }
            });
        },
        getOptionLabel(val){
            const item =this.list.find(e=>e.values === val);
            return item? item.name : '';
        }
    },
}
</script>

<style scoped lang="scss">

</style>