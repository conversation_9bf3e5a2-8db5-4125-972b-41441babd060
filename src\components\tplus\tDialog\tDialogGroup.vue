<template>
<div class="dialog-group" :style="{'--left-width':leftWidth,'--right-width':rightWidth}" v-show="show" >
    <div class="left" :class="{'flex-none':!!leftWidth,'center':dialogCenter && rightWidth}">
        <div class="inner">
            <slot name="left"></slot>
        </div>
    </div>
    <div class="right" :class="{'flex-none':!!rightWidth,'center':dialogCenter && leftWidth}">
        <div class="inner">
            <slot name="right"></slot>
        </div>
    </div>
</div>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        leftWidth: {
            type: String,
            default: ''
        },
        rightWidth: {
            type: String,
            default: ''
        },
        dialogCenter: {
            type: Boolean,
            default: true
        },
    },
    watch: {
        visible(val){
            if(val === false){
                setTimeout(()=>{
                    this.show = val;
                }, 300)
            }else{
                this.show = val;
            }
        }
    },
    data(){
        return {
            show: false
        }
    }
}
</script>

<style lang="scss" scoped>
$left-width: var(--left-width, '');
$right-width: var(--right-width, '');
.dialog-group{
    position:fixed;
    left: 0;
    right:0;
    top:0;
    bottom:0;
    z-index: 2000;
    display: flex;
    flex-direction: row;
    .left, .right{
        position: relative;
        flex: 1;
        .inner{
            position: relative;
            width: 100%;
            height: 100%;
        }
        &.center{
            ::v-deep{
                .el-dialog{
                    margin: 0 auto!important;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
        }
    }
    .left.center .inner{
        width: calc( 100vw - #{$right-width} * 2 );
        margin-left: $right-width;
    }
    .right.center .inner{
        width: calc( 100vw - #{$left-width} * 2 );
        margin-right: $left-width;
    }
    .left.flex-none{
        flex: none;
        width: $left-width;
    }
    .right.flex-none{
        flex: none;
        width: $right-width;
    }

    .el-drawer__wrapper,.el-dialog__wrapper{
        position: absolute;
    }
}
::v-deep{
    .el-drawer{
        width: 100%!important;
    }
}
</style>

