<template>
  <div class="pages" v-fixed-scroll="62">
    <div class="box-crm-head">
      <div class="box-crm-head">
        <div class="crm-head-list" :class="{ head_list: recordstype == 0 }" @click="Follow_up_records(4, 0)">
          流转
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 1 }" @click="Follow_up_records(1)">
          回访
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 2 }" @click="Take_look_record">
          带看
        </div>

        <div class="crm-head-list" :class="{ head_list: recordstype == 4 }" @click="Follow_up_records(4)">
          维护
        </div>
        <div class="crm-head-list" :class="{ head_list: recordstype == 3 }" @click="Outbound_record">
          外呼
        </div>
       <div v-if="auths.transTask"
          class="crm-head-list"
          :class="{ head_list: recordstype == 'liuzhuan' }"
          @click="Follow_up_records('liuzhuan','-1')"
        >
          计划
        </div>
        <!-- <div
          class="crm-head-list"
          :class="{ head_list: recordstype == 5 }"
          @click="Transfertask"
        >
          流转任务
        </div>
   -->
      </div>
      <div>
        <!-- <el-button
                type="danger"
                size="small"
                style="margin-right: 22px;"
                @click="tomianban"
                >BI数据面板</el-button
              > -->
      </div>
    </div>
    <!-- 筛选条件 -->
    <div class="content-box-crm" id="filter" style="margin-bottom: 24px" v-if="recordstype != 'liuzhuan'">
      <!-- <myCollapse :isActive="is_collapse"> -->
      <!-- <template v-slot:content> -->
      <div class="bottom-border div row" style="padding-top: 10px">
        <!-- 时间 -->
        <div class="div row" style="margin-top: 10px">
          <template v-if="recordstype != 'xiansuo'">
            <el-select class="crm-selected-label" clearable v-model="params.date_type" placeholder="时间类型" :style="{
                              minWidth: '30px',
                              width: getSelectWidth(customerLabelList),
                            }" @change="time_type">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </template>
          <div class="block" style="margin: 0px 10px 0px 10px">
            <!-- <span class="demonstration">带快捷选项</span> -->
            <el-date-picker style="width: 350px" v-model="timeValue" type="datetimerange" size="small" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss" @change="onChangeTime">
            </el-date-picker>
          </div>
        </div>
        <!-- 跟进 -->
        <template v-if="recordstype != 'xiansuo'">
          <!-- <div class="head-list">
              <el-select
                class="crm-selected-label"
                clearable
                v-model="all_Customers_value"
                placeholder="全部客户"
                :style="{
                  minWidth: '20px',
                  width: '110px',
                }"
                @change="all_Customers_through"
              >
                <el-option
                  v-for="item in all_Customers_list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </div> -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="follow_up_value" placeholder="跟进" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="follow_through">
              <el-option v-for="item in follow_up_list" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 邀约 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="Invitation_value" placeholder="邀约" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="invite_type">
              <el-option v-for="item in Invitation_list" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 客户状态 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="customer_statusvalue" placeholder="客户状态" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="customer_status">
              <el-option v-for="item in customer_status_list" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 等级 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="grade_value" placeholder="等级" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="customer_grade">
              <el-option v-for="item in level_list" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
          </div>
            <!-- 客户来源 -->
          <div class="head-list" style="margin-top: 6px;">
            <el-cascader class="crm-selected-label" :style="{
                minWidth: '20px',
                 width: '110px',}" v-model="source_value" placeholder="客户来源" :options="source_list" @change="customer_source"
              :props="{label: 'title',
                        value: 'id',
                        children: 'children',
                        checkStrictly: true }" clearable>
            </el-cascader>
          </div>
          <!-- 客户类型 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="typeLabel_value" placeholder="客户类型" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="client_type">
              <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 通话状态 -->
          <div class="head-list">
            <el-select class="crm-selected-label" clearable v-model="customerstatus_value" placeholder="通话状态" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="customerstatus_type">
              <el-option v-for="item in customerstatus" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 部门 -->
          <div class="head-list" style="margin-top:6px">
            <el-cascader class="crm-selected-label" v-model="params.department_id" placeholder="请选择部门" :style="{
              minWidth: '20px',
               width: '130px',}" :options="AllDepartment" @change="changePopDepar" @focus="Reqdepartment" :clearable="true"
              :show-all-levels="false" :props="{
                label: 'name',
                value: 'id',
                children: 'subs',
                checkStrictly: true,
                emitPath: false,
              }">
            </el-cascader>
          </div>
          <!-- 成员 -->
          <div class="head-list">
              <el-cascader
                class="inp_no_border1 my-cascader"
                size="small"
                v-model="member_value"
                :options="member_listNEW"
                clearable
                filterable
                placeholder="成员"
                :style="{
                  minWidth: '20px',
                  width: '110px',
                }"
                :props="{
                  label: 'user_name',
                  value: 'id',
                  children: 'subs',
                  checkStrictly: true,
                }"
                @change="loadFirstLevelChildren"
                @focus="loadFirstLevelChildrenA"
              ></el-cascader>
            </div>
        </template>
        <template v-if="recordstype == 'xiansuo'">
          <div class="head-list div row">
            <el-select class="crm-selected-label" clearable v-model="source" placeholder="来源" :style="{
                              minWidth: '20px',
                              width: '110px',
                            }" @change="changeThirdSource">
              <el-option v-for="item in third_source_list" :key="item.id" :label="item.title" :value="item.id">
              </el-option>
            </el-select>
            <div style="margin: 0px 10px;">
              <el-input placeholder="请输入渠道号码" v-model="params.refer_id" size="small" class="input-with-select"
                style="width: 180px">
                <el-button slot="append" icon="el-icon-search" @click="Channel_number"></el-button>
              </el-input>
            </div>
            <div>
              <el-input placeholder="请输入渠道名称" v-model="params.refer_name" size="small" class="input-with-select"
                style="width: 180px">
                <el-button slot="append" icon="el-icon-search" @click="Channel_Name"></el-button>
              </el-input>
            </div>
          </div>
        </template>
      </div>
      <!-- 客户标签 -->
      <template v-if="recordstype != 'xiansuo'">
        <div class="bottom-border div row" style="padding-top: 24px;border-bottom: 0px dashed #e2e2e2;">
          <div class="label_list div row">
            <!-- <span class="text">客户标签：</span> -->
            <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="getAllLabelList">
              客户标签
            </span>
            <span v-for="(item, index) in label_list" :key="index" class="label_item">
              <el-select class="selected-label" v-model="customerLabelList[item.id]" @change="changeCustomerLabel"
                :placeholder="item.name" :style="{
                                    minWidth: '40px',
                                    width: getSelectWidth(item, customerLabelList[item.id]),
                                    background: changeParentLabel == item.id ? '#E8F1FF' : '',
                                  }">
                <el-option v-for="arr in item.label" :key="arr.id" :label="arr.name" :value="[arr.id, arr, item]">
                </el-option>
              </el-select>
            </span>
          </div>
        </div>
      </template>
    </div>
    <!-- 表格操作按钮 -->
    <div class="content-box-crm content-box-crm-pr pad0" :class="{'pt24':recordstype == 'liuzhuan'}">
      <div class="table-top-box table-top-box-abs div row" :class="{ fixed: scrollTop > topHeight }" id="stickyId"  v-if="recordstype != 'liuzhuan'">
        <!-- <div class="zhanwei" v-if="scrollTop + 80 > 400"></div> -->
        <div class="t-t-b-left div b-tabs row" style="width: 100%; height: 30px">
          <el-input :placeholder="placeholder" v-model="select_params.keywords" class="input-with-select"
            style="width: 300px" clearable size="small" @keyup.enter.native="handleKeywordSearch"
            @clear="clearSelectKeyword">
            <el-select v-model="select_params.type" @change="changeSelectParams" slot="prepend" placeholder="请选择"
              size="small" style="width: 100px">
              <el-option label="客户电话" :value="1"></el-option>
              <template v-if="recordstype != 'xiansuo'">
                <el-option label="客户编号" :value="2"></el-option>

                <el-option label="线索名称" :value="3"></el-option>
                <el-option label="客户姓名" :value="4"></el-option>
              </template>
              <template v-if="recordstype == 'xiansuo'">
                <el-option label="客户姓名" :value="3"></el-option>
              </template>
            </el-select>
            <!-- <el-button slot="append" icon="el-icon-search"></el-button> -->
          </el-input>
        </div>
        <div class="t-t-b-right" :class="{ abs: is_small_system, show: show_right }">
          <el-button v-if="douyinview" style="font-size: 14px" size="mini" @click="douview">
            <div class="flex-row items-center">
              <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
              <span>数据视图</span>
            </div>

          </el-button>
          <template v-if="recordstype != 'xiansuo'">
            <el-button v-if="outview" style="font-size: 14px" size="mini" @click="outboundview">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
                <span>数据视图</span>
              </div>
            </el-button>
            <el-button style="font-size: 14px" size="mini" @click="datastatistics">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinview.png" alt="">
                <span>数据统计</span>
              </div>
            </el-button>
            <el-button type="text" size="mini" class="el-icon-d-arrow-left" id="myButton" v-show="myButton1"
              @click="leftla"></el-button>
            <el-button style="font-size: 14px" type="primary" size="mini" @click="leading_out">
              导出
            </el-button>
            <!-- <el-button
                style="font-size: 14px"
                type="primary"
                size="mini"
                @click="TransferCustomer"
              >
                转交
              </el-button> -->
              <el-popover v-show="buttonhidden" v-model="pop_depart" placement="bottom" width="500px" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in operatelist" :key="item.id" @click="operateonClick(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" @click="getCrmDepartmentList" class="search_loudong div row align-center" style="margin-right:10px;">
                <div class="seach_value">操作</div>
                <div class="sanjiao" :class="{ transt: pop_depart }"></div>
              </div>
            </el-popover>
            <!-- <el-button v-show="buttonhidden" style="font-size: 14px" type="primary" @click="setCustomerLabel" size="mini">
              设置标签
            </el-button> -->

            <!-- <el-popover
              v-show="buttonhidden"
              placement="bottom"
              width="500px"
              v-model="show_tel_search"
            >
              <div>
                <div>搜索</div>
                <div class="inps div row align-center" style="margin: 10px 0">
                  <el-input
                    placeholder="请输入手机号或客户编号"
                    v-model="params.mobile"
                    style="margin-right: 10px; width: 190px"
                  ></el-input>
                </div>
                <div class="btns" style="text-align: right">
                  <el-button @click="resetLoudongSearch()">重置</el-button>
                  <el-button type="primary" @click="handleSearch">确定</el-button>
                </div>
              </div>
              <div
                class="search_loudong div row align-center"
                slot="reference"
                style="height: 30px"
              >
                <div class="seach_value">电话</div>
                <div class="sanjiao" :class="{ transt: show_tel_search }"></div>
              </div>
            </el-popover> -->
            <!-- <el-popover
                v-show="buttonhidden"
                v-model="pop_depart"
                placement="bottom"
                width="500px"
                trigger="click"
              >
                <div class="flex-box">
                  <div>搜索</div>
                  <div style="margin: 10px 0" class="flex-row">
                    <el-cascader
                      class="inp_no_border"
                      v-model="params.department_id"
                      placeholder="请选择部门"
                      :options="AllDepartment"
                      @change="changePopDepar"
                      :clearable="true"
                      :show-all-levels="false"
                      :props="{
                        label: 'name',
                        value: 'id',
                        children: 'subs',
                        checkStrictly: true,
                        emitPath: false,
                      }"
                    >
                    </el-cascader>
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      style="
                        background: #f5f7fa;
                        border-left: none;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                      "
                      @click="searchDepart"
                    ></el-button>
                  </div>
                  <div class="flex-row">
                    <el-select
                      class="search-member-box"
                      v-model="selectedMember"
                      placeholder="请选择成员"
                      @change="changeSearchMember"
                      filterable
                      clearable
                    >
                      <el-option
                        v-for="item in filtrMember"
                        :key="item.id"
                        :label="item.user_name"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      style="
                        background: #f5f7fa;
                        border-left: none;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                      "
                      @click="searchMember"
                    >
                    </el-button>
                  </div>
                </div>
                <div
                  slot="reference"
                  @click="getCrmDepartmentList"
                  class="search_loudong div row align-center"
                >
                  <div class="seach_value">部门</div>
                  <div class="sanjiao" :class="{ transt: pop_depart }"></div>
                </div>
              </el-popover> -->

            <!-- <el-popover
              v-show="buttonhidden"
              placement="bottom"
              width="500px"
              v-model="show_xiansuo_search"
            >
              <div>
                <div>搜索</div>
                <div class="inps div row align-center" style="margin: 10px 0">
                  <el-input
                    placeholder="请输入客户线索"
                    v-model="params.keywords"
                    style="margin-right: 10px; width: 180px"
                  ></el-input>
                </div>
                <div class="btns" style="text-align: right">
                  <el-button @click="resetXiansuoSearch()">重置</el-button>
                  <el-button type="primary" @click="handleSearch">确定</el-button>
                </div>
              </div>
              <div class="search_loudong div row align-center" slot="reference">
                <div class="seach_value">线索</div>
                <div
                  class="sanjiao"
                  :class="{ transt: show_xiansuo_search }"
                ></div>
              </div>
            </el-popover> -->
            <el-button type="text" size="mini" class="el-icon-d-arrow-right" id="myButton1" v-show="myButton"
              @click="Rightdrag" style="margin-left: 10px"></el-button>
          </template>

        </div>
      </div>
      <div style="padding: 0 24px">
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="this['table_header' + recordstype]" select
          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
          :sort_change="sortChangeData" @selection-change="selectionChange"></myTable>
      </div>

      <div class="page_footer flex-row items-center">
        <div class="page_footer_l flex-row flex-1 items-center">
          <div class="head-list">
            <el-button type="primary" size="small" @click="empty">清空</el-button>
          </div>
          <div class="head-list">
            <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
          </div>
        </div>
        <div>
          <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
            :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
            @current-change="onPageChange" @size-change="handleSizeChange">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- <wxwork v-if="is_tabs === 'wxwork'"></wxwork> -->
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel"
      :close-on-click-modal="false">
      <!-- <myForm
            @clsoe="is_push_customer = false"
            :data1="n_client_field"
            :data2="n_company_field"
            :form="form"
            :form1="form1"
            @onClick="onClickForm"
          ></myForm> -->
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.source_id" placeholder="请选择">
                <el-option v-for="item in sourceLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <!-- <el-select
                    style="width:100%"
                    v-model="push_form.sex"
                    placeholder="请选择"
                  >
                    <el-option label="男" :value="1"></el-option>
                    <el-option label="女" :value="2"></el-option>
                  </el-select> -->
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                  :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt="" @click="
                                        () => {
                                          push_form.sex = item.id;
                                        }
                                      " />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <div class="row input-box div">
              <div @click="onClickLevel(item)" class="l-item row" v-for="item in levelLabel" :key="item.id" :class="{
                                  lisactive: item.id === push_form.level_id,
                                }">
                <div class="l-name">{{ item.title }}</div>
                <div class="l-name-1">{{ item.desc }}</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户标签：">
            <div class="input-box">
              <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签"
                :options="label_default_list" :props="{
                                    value: 'id',
                                    label: 'name',
                                    children: 'label',
                                    emitPath: false,
                                    multiple: true,
                                  }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.intention_community"></el-input>
            </div>
          </el-form-item>
          <!-- <el-form-item label="意向区域：">
                <div class="row input-box div">
                  <el-input
                    placeholder="请输入"
                    v-model="push_form.intention_street"
                  ></el-input>
                </div>
              </el-form-item> -->
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer flex-row align-center">
        <el-switch v-model="push_form.add_type" active-color="#13ce66" inactive-color="#2d84fb" inactive-value="1"
          active-value="2" active-text="公海" inactive-text="私客">
        </el-switch>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
            定</el-button>
        </div>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_follow_dialog" title="跟进记录">
      <el-table v-loading="is_follow_loading" :data="is_follow_data" border>
        <el-table-column label="ID" prop="client_follow_id"></el-table-column>
        <el-table-column label="跟进类型" prop="type_title"></el-table-column>
        <el-table-column label="跟进内容" prop="content"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="is_follow_params.total" :page-size="is_follow_params.per_page" :current-page="is_follow_params.page"
        @current-change="onPageChangeQwFollow">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_dialog_upload" title="导入" :before-close="cancels">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <!-- <el-select
            style="width: 200px; margin-bottom: 10px"
            v-model="upload_form.type"
            placeholder="请选择"
          >
            <el-option label="不覆盖" :value="1"></el-option>
            <el-option label="覆盖" :value="2"></el-option>
          </el-select> -->
      <div class="flex-row">
        <!-- 是否覆盖 -->
        <el-select style="width: 200px; margin-bottom: 10px" v-model="upload_form.type" placeholder="请选择是否覆盖数据">
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select>
        <!-- 选择分类 -->
        <el-select style="width: 150px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.type_id"
          placeholder="请选择分类" clearable>
          <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
        <!-- 选择客户来源 -->
        <el-select style="width: 200px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.source_id"
          placeholder="请选择客户来源" clearable>
          <!-- source_import -->
          <el-option v-for="item in source_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
      </div>
      <div class="flex-row">
        <!-- 选择成员 -->
        <el-input ref="focusMember" placeholder="请选择维护人" v-model="uploadAdmin_id" style="width: 200px; display: block"
          @focus="focusSelete">
          <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <!-- 选择标签 -->
        <!-- <el-select
              style="width: 362px; margin-bottom: 10px; margin-left: 12px;"
              v-model="upload_form.label"
              multiple
              placeholder="请选择标签"
            >
              <el-option label="标签1" :value="1"></el-option>
              <el-option label="标签2" :value="2"></el-option>
            </el-select> -->
        <el-cascader style="width: 362px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.label" clearable
          placeholder="请选择标签" :options="label_list" :props="{
                        value: 'id',
                        label: 'name',
                        children: 'label',
                        emitPath: false,
                        multiple: true,
                      }">
        </el-cascader>
      </div>
      <!-- 客户备注线索 -->
      <div class="clueRemark">
        <el-input ref="focusMember" placeholder="请选择录入人" v-model="uploadAdmin_id1" style="width: 200px; display: block"
          @focus="focusSelete1">
          <i v-show="uploadAdmin_id1 != '' && uploadAdmin_id1 != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <el-input type="textarea" :rows="2" placeholder="请输入客户备注线索" v-model="upload_form.remark"
          style="margin-left: 11px">
        </el-input>
      </div>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
            >添加文件</el-button
          > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancels">取 消</el-button>
        <el-button type="primary" @click="startImport" :loading="is_loading">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list1" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list1" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember1" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog title="客户标签" :visible.sync="show_Customer_label" width="660px">
      <div class="dialog_customer_label">
        <div v-for="(item, index) in labels_list" :key="item.id">
          <div class="labelname">{{ item.name }}</div>
          <div class="lables-box div row">
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.taggroup" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.label" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_Customer_label = false">取 消</el-button>
        <el-button type="primary" @click="confirmSelected" :loading="is_loading">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_transfer_customer" title="请选择接收人员">
      <!-- <div class="tips">
        <div>提示语：转交后维护人将变更</div>
      </div> -->
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table  ref="capytable" style="margin-top: 10px" :data="admin_list" border class="eltable" @sort-change="sortChange"
      @selection-change="copyselecchange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <!-- <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum"></el-table-column> -->
        <!-- <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row)">转交客户</el-link>
          </template>
        </el-table-column> -->
      </el-table>
      <div style="text-align: end; margin-top: 10px">
          <el-button type="primary" @click="oncopy">复制客户</el-button>
        </div>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>

    <!-- 快速编辑客户维护资料模态框 -->
    <my-Maintenance v-if="show_cus_Edit" :show_cus_Edit="show_cus_Edit" :source_list="ponent_maintain_source"
      :level_list="ponent_maintain_level" :type_list="ponent_maintain_type" :label_list="ponent_maintain_label"
      :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit" @submitMaintain="submitMaintain">
    </my-Maintenance>
    <!-- 快速查看客户手机号模态框 -->
    <my-LookTel v-if="show_look_Tel" :show_look_Tel="show_look_Tel" :ponent_Tel_data="ponent_Tel_data" :selfID="selfID"
      :nowDialData="nowDialData" @fastCloseTel="fastCloseTel" @fastSubmitTel="fastSubmitTel"></my-LookTel>
    <!-- 快速提交客户审批 -->
    <my-Examine v-if="show_Examine_dialog" :show_Examine_dialog="show_Examine_dialog"
      :ponent_Examine_data="ponent_Examine_data" :ponent_Examine_stutas="ponent_Examine_stutas"
      :ponent_Examine_type="Examine_type" :AllDepartment="AllDepartment" @closeExamine="closeExamine"
      @submitExamineAfter="submitExamineAfter"></my-Examine>
    <!-- 快速跟进客户内容模态框 -->
    <myFollowUp v-if="show_Follow_dialog" :show_Follow_dialog="show_Follow_dialog"
      :ponent_Follow_data="ponent_Follow_data" @addFollowSuccess="addFollowSuccess" @closeFollow="closeFollow">
    </myFollowUp>
    <!-- <input
          v-if="is_dialog_upload"
          type="file"
          ref="file"
          style="display: none"
          v-on:change="handleFileUpload($event)"
        /> -->
    <el-upload :limit="1" class="upload-demo" :headers="myHeader" :action="user_avatar"
      :on-success="handleSuccessAvatarTemporary" ref="upload" style="display: none" v-if="is_dialog_upload">
      <el-button class="el-icon-download" size="small">本地上传</el-button>
    </el-upload>

    <audio ref="musicMp3" id="musicMp3" :autoplay="false" controls="false" style="z-index: -1"></audio>
    <addTransTask ref="addTransTask" v-if="dialogs.addTransTask" @closed="dialogs.addTransTask = false"></addTransTask>
    <transToMember v-if="dialogss.transToMember" ref="transToMember"/>
  </div>
</template>
    
<script>
  // import my from "./components/my";
  // import seas from "./components/seas";
  // import wxwork from "./components/wxwork";
  // import myForm from "./components/customer_form";
  import { Loading } from "element-ui";
  import myTable from "@/components/components/my_table";
  // import myLabel from "./components/my_label.vue";
  // import myCollapse from "./components/collapse";
  import myExamine from "@/components/components/my_Examine.vue";
  import myMaintenance from "@/components/components/my_maintenance.vue";
  import myLookTel from "@/components/components/my_lookTel.vue";
  // import mySelect from "./components/my_select";
  import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
  import myFollowUp from "@/components/components/my_followUp.vue";
  import config from "@/utils/config";
  import addTransTask from '@/views/crm/customer/components/add_trans_task.vue';
  import transWorkListMixin from '@/views/crm/customer/mixins/trans_work_list.js';
  import transToMember from "@/views/crm/customer/components/trans_to_member.vue";
  export default {
    name: "crm_customer_seas_list",
    components: {
      // my,
      // seas,
      // myForm,
      // wxwork,
      myTable,
      // mySelect,
      // myLabel,
      // myCollapse,
      multipleTree,
      myMaintenance,
      myLookTel,
      myExamine,
      myFollowUp, addTransTask,
      transToMember
    },
    mixins: [transWorkListMixin],
    data() {
      return {
        dialogs: {
          addTransTask: false    //新增或编辑流转任务弹层
        },
        auths: {
          transTask: false,  //流转任务权限
        },
        //记录切换
        recordstype: 0,
        //视图图列表切换
        douyinview:false,
        outview:false,
        //时间类型
        options: [
          { value: 0, label: '全部' },
          { value: 1, label: '创建时间' },
          { value: 2, label: '跟进时间' },
          { value: 3, label: '线索时间' },
          { value: 4, label: '更新时间' },
          // { value: 5, label: '掉公时间' },
          // { value: 6, label: '转公时间' },
          { value: 7, label: '跟客天数' },
          { value: 8, label: '带看创建时间'},
          { value: 9, label: '带看时间' }
        ],
        value: "",
        value2: '',
        // 跟进
        follow_up_value: "",
        follow_up_list: [
          { id: 1, name: "已跟进" },
          // { id: 2, name: "已认领" },
          { id: 3, name: "未跟进" },
          // { id: 4, name: "待分配" },
          // { id: 5, name: "已转公" },
          // { id: 6, name: "已掉公" },
          { id: 7, name: "语音跟进" },
          { id: 8, name: "普通跟进" },
          { id: 9, name: "电话跟进" },
        ],
        all_Customers_value:"",
        all_Customers_list: [
          { id: 0, name: "全部客户" },
          { id: 1, name: "公海客户" },
          { id: 2, name: "潜在客户" },
          { id: 3, name: "成员私客" },
  
        ],
        // 邀约
        Invitation_value: "",
        Invitation_list: [
          { id: 1, name: "查看电话" },
          { id: 2, name: "已接通（外呼）" },
          { id: 3, name: "未接通（外呼）" },
          { id: 4, name: "已带看" },
          { id: 5, name: "未带看" },
          { id: 6, name: "有复看" },
          { id: 7, name: "修改资料" },
          { id: 8, name: "修改标签" },
          { id: 9, name: "修改等级" },
        ],
        // 客户状态
        customer_statusvalue: "",
        customer_status_list: [
          { id: 1, name: "有效客户" },
          { id: 2, name: "无效客户" },
          { id: 3, name: "暂缓客户" },
          { id: 4, name: "我司成交" },
          { id: 5, name: "他司成交" },
          { id: 6, name: "未成交" },
        ],
        //等级
        grade_value: "",
        // grade_list: [],
        //成员
        member_value: "",
        member_list: [
          {
            id: 1, name: "录入人",
            subs: []
          },
          {
            id: 2, name: "维护人",
            subs: []
          },
          {
            id: 3, name: "带看人",
            subs: []
          },
          {
            id: 4, name: "成交人",
            subs: []
          },
        ],
        member_listNEW: [
          {
            id: 1, user_name: "录入人",
            subs: []
          },
          {
            id: 2, user_name: "维护人",
            subs: []
          },
          {
            id: 3, user_name: "带看人",
            subs: []
          },
          {
            id: 4, user_name: "成交人",
            subs: []
          },
        ],
        //客户来源
        source_value: '',
        typeLabel_value: "",
        customerstatus_value:"",
        //选择成员弹框控制
        show_member_list: false,
        show_member_list1: false,
        is_tabs: "all",
        selectedIds: [],
        tabs: [
          {
            id: 1,
            name: "所有客户",
            desc: "all",
          },
          {
            id: 2,
            name: "我的客户",
            desc: "my",
          },
          {
            id: 3,
            name: "公海客户",
            desc: "seas",
          },
          // {
          //   id: 4,
          //   name: "企微客户",
          //   desc: "wxwork",
          // },
        ],
        is_push_customer: false,
        push_form: {
          cname: "",
          source_id: "",
          level_id: 1,
          type: "",
          sex: 1,
          subsidiary_mobile: "",
          intention_community: "",
          label: "", // 客户标签
          // intention_street: "",
          remark: "",
          add_type: "1",
        },
        sex_list: [
          { id: 1, name: "nan" },
          { id: 2, name: "nv3" },
        ],
        other_mobile: [{ mobile: "" }],
        type_list: [],
        timeValue: "",
        pickerOptions: {
          shortcuts: [{
            text: '今天',
            onClick(picker) {
              const end = new Date();
              const start = new Date(end);
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '昨天',
            onClick(picker) {
              const end = new Date();
              end.setDate(end.getDate() - 1);
              end.setHours(0, 0, 0, 0);
              const start = new Date(end);
              start.setDate(start.getDate());
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '本周',
            onClick(picker) {
              const end = new Date();
              const start = new Date(end);
              start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '上周',
            onClick(picker) {
              const today = new Date(); // 获取当前日期
              const end = new Date(today); // 结束日期为当前日期
              const start = new Date(today); // 开始日期为当前日期
              const day = today.getDay(); // 获取当前是星期几
              const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
              const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
              end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
              // end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
              start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
              // start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
              // 将计算得到的时间范围传递给日期选择器
              picker.$emit('pick', [start, end]);
          }
          }, {
            text: '本月',
            onClick(picker) {
              const end = new Date();
              const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '上月',
            onClick(picker) {
              const end = new Date();
              end.setDate(0); // 获取上个月的最后一天
              end.setHours(23, 59, 59, 0);
              const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
              start.setHours(0, 0, 0, 0);
              picker.$emit('pick', [start, end]);
            }
          },]
        },
        level_list: [],
        source_list: [],
        client_field: {
          // 获取客户字段
          type: 2,
        },
        n_client_field: {},
        n_company_field: {},
        form: {},
        form1: {},
        type: 1,
        tracking_list: [],
        bind_list: [
          { id: 0, name: "全部" },
          { id: 1, name: "已绑定" },
          { id: 2, name: "未绑定" },
        ],
        operatelist:[
          // {id:1,name:"导入"},
          {id:2,name:"删除"},
          {id:3,name:"转交给同事"},
          {id:4,name:"复制给同事"},
          // {id:5,name:"批量加入自动流转"}

        ],//操作
        //成员部门信息
        memberList: [],
        multipleSelection: [],
        params: {
          page: 1,
          per_page: 10,
          type: 4,//(1:回访,2:带看,3:外呼4 全部 和维护 5 成交)
          keywords: '',//搜索关键词(客户姓名或备注)
          mobile: '',//手机号或者客户ID
          refer_id:'', //渠道号码
          refer_name:'',// 渠道名称
        },
        select_params: {
          type: 1,
          keywords: ""
        },
        show_xiansuo_search: false,
        is_table_loading: false,
        tableData: [],
        //回访记录
        table_header0: [
          // {
          //   prop: "id",
          //   label: "ID",
          //   width: "80px",
          // },
  
          {
            label: "客户名称",
            width: "200px",
            fixed: "left",
            // sortable: "custom",
            render: (h, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.cname ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div style="align-items: center;" class="flex-row">
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>
                          <div class="flex-row">
                            客户名称：{data.row.cname}
  
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.cname}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                    </div>
                    <div class="cus-box-foot flex-row">
  
                      {data.row && data.row.create_id == this.status_id ? (
                        <span class="cus-icon-type">私客</span>
                      ) : null}
  
  
                    </div>
                  </div>
                  {data.row.wxqy_id > 0 ? (
                    <img
                      class="cus-img"
                      src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                    />
                  ) : (
                    ""
                  )}
                  {/* <div
                    class="fast-Edit-cus"
                    onClick={() => {
                      this.fastEditData(data.row);
                    }}
                  >
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      alt=""
                    />
                  </div> */}
                </div>
              );
            },
          },
       {
          label: "手机号",
          fixed: "left",
          width: "200px",
          prop: "mobile",
          render: (h, data) => {
            // const mobileFilter = function (val) {
            //   let reg = /^(.{3}).*(.{3})$/;
            //   return val.replace(reg, "$1*****$2");
            // };
            if(!data.row.mobile){
              return('');
            }
            return (
              <div class="flex-box table-btns">
                {data.row.last_call_follow
                  &&
                  data.row.last_call_follow.id
                  ?
                  (<div class="last_call_follow div row">
                    <el-tooltip
                      class="item"
                      style="display:flex;flex-direction:row;justify-content:center"
                      effect="light"
                      placement="top"
                    >
                      <div slot="content" style="max-width:300px">
                        {data.row.last_call_follow.content}
                      </div>
                      <div class="cus-clue-text">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.call_status == 1
                          ?
                          (<span class='cus-clue-text_c'>{data.row.mobile}</span>)
                          :
                          (<span class='cus-clue-text_u'>{data.row.mobile}</span>)}
                      </div>
                    </el-tooltip>
                  </div>)
                  :
                  (<span class='cus-clue-text_n'>{data.row.mobile}</span>)}
                {/* <div
                  class="fast-look-tel"
                  onClick={() => {
                    this.fastLookTel(data.row);
                  }}
                >
                  <i class="el-icon-phone"></i>
                </div> */}
              </div>
            );
          },
        },
          {
            label: "最新跟进记录",
            prop: "last_follow_info",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.last_follow_info ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
  
                          <div class="flex-row">
                            {data.row.last_follow_info.content}
  
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.last_follow_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        "--"
                      )}
                    </div>
                  </div>
                </div>
              );
            },
          },
          {
            label: "流转计划",
            prop: "auto_work_log",
            width: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.auto_work_log && data.row.auto_work_log.auto_work_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.auto_work_log.auto_work_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
          {
            label: "流转时间",
            prop: "auto_work_log",
            width: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.auto_work_log && data.row.auto_work_log.created_at ? (
                    <span style="margin-right: 5px;">
                      {data.row.auto_work_log.created_at}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
          {
            label: "流转量",
            prop: "auto_count",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: left;" >
                  {data.row.auto_count && data.row.auto_count || 0}
                </div>
              )
            }
          },
          {
            label: "复制量",
            prop: "copy_count",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: left;" >
                  {data.row.copy_count && data.row.copy_count || 0}
                </div>
              )
            }
          },
          {
            label: "成交人",
            prop: "deal_user",
            width: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.deal_user && data.row.deal_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.deal_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
          {
            label: "成交周期",
            prop: "",
            width: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                </div>
              )
            }
          },
  
          {
            label: "跟客天数",
            prop: "",
            width: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.follow_day !== '' && data.row.last_follow_info.follow_day >= 0 ? (data.row.last_follow_info.follow_day + '天') : '--'}
  
                </div>
              )
            }
          },
  
          {
            label: "客户等级",
            prop: "",
            width: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : "空"}
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户意向",
            prop: "intention_community",
            width: "100px",
            render: (j, data) => {
              return (
                     <div class="flex-row cus-header-user">
                      {data.row.intention_community ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.intention_community}
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.intention_community}
                          </div>
                        </el-popover>
                      ) : (
                        "--"
                      )}
                    </div>
              )
            }
          },
          {
            label: "客户状态",
            prop: "tracking",
            width: "130px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot" style="width:80px">
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="100"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title || '--'}
                              </div>
                            );
                          })
                          : "--"}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title || '--'}
                        </span>
                      ) : (
                        <span
                          slot="reference"
  
  
                        >
                          {"--"}
                        </span>
                      )}
                    </el-popover>
                  </div>
                </div>
              )
            }
          },
          {
            label:"最后通话状态",
            prop:"",
            width:"160px",
            render:(j,data)=>{
              return (
                <div>
                  <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                    ? 'success'
                    : data.row.last_call_status && data.row.last_call_status === 2
                      ? 'danger'
                      : 'info'}>
                    {data.row.last_call_status && data.row.last_call_status === 1
                      ? '已接通'
                      : data.row.last_call_status && data.row.last_call_status === 2
                        ? '未接通'
                        : '未联系'}
                  </el-tag>
                </div>
              )
            }
          },
          {
            label: "客户标签",
            prop: "label",
            width: "220px",
            render: (j, data) => {
              return (
  
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user cus-clue-label">
                      {data.row.label && data.row.label.length > 0 ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.label.join(",")}
  
                          </div>
                          <div class="cus-userName w100 flex-row" slot="reference">
                            {data.row.label.length > 0
                              ? data.row.label.slice(0, 2).map((item, index) => {
                                return (
                                  <div class="flex-row align-center">
                                    <span class="cus-icon-label" key={index}>
                                      {item}
                                    </span>
                                  </div>
                                );
                              })
                              : '--'}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
  
                    </div>
                  </div>
                  {data.row.label && data.row.label.length > 0 ? (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  ) : (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  )}
                </div>
              )
            }
          },
          {
            label: "录入人",
            prop: "create_user",
            width: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.create_user && data.row.create_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.create_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
  
          {
            label: "维护人",
            prop: "repeat_call_name",
            width: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.follow_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "带看人",
            width: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.take_user && data.row.take_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.take_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "跟进状态",
            prop: "public_status",
            width: "120px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.public_status == 2 && data.row.public2_status ? (
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(
                        data.row.public2_status, data.row.go_sea_day
  
                      )}
                    </span>
                  ) : (
                    "正常"
                  )}
                </div>
              )
            }
          },
          {
            label: "客户线索",
            prop: "remark",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="flex-box">
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.remark}
                    </div>
                    <div class="cus-clue-text">
                      <span>{data.row.remark || '--'}</span>
                    </div>
                  </el-tooltip>
  
                </div>
              );
            },
          },
          {
            label: "客户类型",
            prop: "client_type",
            width: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.client_type ? (
                    <el-tag type="info">{data.row.client_type.title}</el-tag>
                  ) : "--"}
                </div>
              )
            }
          },
          {
            label: "创建时间",
            prop: "created_at",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  </div>
                </div>
              );
            },
          },
          {
            prop: "updated_at",
            label: "更新时间",
            // sortable: "custom",
            width: "200px",
            render: (j, data) => {
              return (
                <div>
                  {data.row.last_follow_day >= 0 &&
                    data.row.last_follow_day != "" ? (
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content" style="max-width:300px">
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                      <div class="follow-content">
                        <span style="margin-right: 5px;">
                          {this.$options.filters.getPastDay(
                            data.row.last_follow_day
                          )}
                        </span>
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                    </el-tooltip>
                  ) : (
                    ""
                  )}
                  <div style="text-align: left;">{data.row.updated_at}</div>
                  <div
                    class="followLabel"
                    onClick={() => {
                      this.fastFollowUp(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                </div>
              );
            },
          },
          {
            width: "190px",
            label: "来源",
            render: (h, data) => {
              return (
                <div>
                  {data.row && data.row.source ? (
                    <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                  ) : (
                    ""
                  )}
                </div>
              );
            },
          },
          {
            label: "归属地",
            width: "200px",
            prop: "mobile",
            render: (h, data) => {
              return (
                <div class="flex-box table-btns">
                  <span class="search-Belong">
                    {data.row.mobile_place == "" ||
                      data.row.mobile_place == undefined ? (
                      <el-button
                        type="primary"
                        plain
                        onClick={() => {
                          this.HomeAddress(data.row);
                        }}
                      >
                        归属地查询
                      </el-button>
                    ) : (
                      <span class="Address">{data.row.mobile_place}</span>
                    )}
                  </span>
                </div>
              );
            },
          },
  
          {
            label: "操作",
            width: "150",
            fixed: "right",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    详情
                  </el-link>
  
                </div>
              );
            },
          },
  
        ],
        table_header1: [
          // {
          //   prop: "id",
          //   label: "ID",
          //   width: "80px",
          // },
  
          {
            label: "客户名称",
            width: "200px",
            fixed: "left",
            // sortable: "custom",
            render: (h, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.cname ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div style="align-items: center;" class="flex-row">
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>
                          <div class="flex-row">
                            客户名称：{data.row.cname}
  
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.cname}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                    </div>
                    <div class="cus-box-foot flex-row">
  
                      {data.row && data.row.create_id == this.status_id ? (
                        <span class="cus-icon-type">私客</span>
                      ) : null}
  
  
                    </div>
                  </div>
                  {data.row.wxqy_id > 0 ? (
                    <img
                      class="cus-img"
                      src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                    />
                  ) : (
                    ""
                  )}
                  {/* <div
                    class="fast-Edit-cus"
                    onClick={() => {
                      this.fastEditData(data.row);
                    }}
                  >
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      alt=""
                    />
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "手机号",
            fixed: "left",
            width: "200px",
            prop: "mobile",
            render: (h, data) => {
              // const mobileFilter = function (val) {
              //   let reg = /^(.{3}).*(.{3})$/;
              //   return val.replace(reg, "$1*****$2");
              // };
              if(!data.row.mobile){
                return('');
              }
              return (
                <div class="flex-box table-btns">
                  {data.row.last_call_follow
                    &&
                    data.row.last_call_follow.id
                    ?
                    (<div class="last_call_follow div row">
                      <el-tooltip
                        class="item"
                        style="display:flex;flex-direction:row;justify-content:center"
                        effect="light"
                        placement="top"
                      >
                        <div slot="content" style="max-width:300px">
                          {data.row.last_call_follow.content}
                        </div>
                        <div class="cus-clue-text">
                          {data.row.last_call_follow
                            &&
                            data.row.last_call_follow.call_status == 1
                            ?
                            (<span class='cus-clue-text_c'>{data.row.mobile}</span>)
                            :
                            (<span class='cus-clue-text_u'>{data.row.mobile}</span>)}
                        </div>
                      </el-tooltip>
                    </div>)
                    :
                    (<span class='cus-clue-text_n'>{data.row.mobile}</span>)}
                  {/* <div
                    class="fast-look-tel"
                    onClick={() => {
                      this.fastLookTel(data.row);
                    }}
                  >
                    <i class="el-icon-phone"></i>
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "最新回访记录",
            prop: "last_follow_info",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.last_follow_info ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
  
                          <div class="flex-row">
                            {data.row.last_follow_info.content}
  
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.last_follow_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                </div>
              );
            },
          },
          {
            label: "跟客天数",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.follow_day !== '' && data.row.last_follow_info.follow_day >= 0 ? (data.row.last_follow_info.follow_day + '天') : '--'}
  
                </div>
              )
            }
          },
  
          {
            label: "客户等级",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : "空"}
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户意向",
            prop: "",
            width: "100px",
            render: (j, data) => {
              return (
                <el-tooltip class="item" effect="light" placement="top">
                  <div slot="content" >
                    {data.row.intention_community}
                  </div>
                  <div class="cus-clue-text cus-clue-text_community">
                    <span>{data.row.intention_community || '--'}</span>
                  </div>
                </el-tooltip>
              )
            }
          },
          {
            label: "客户状态",
            prop: "tracking",
            width: "130px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot" style="width:80px">
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="100"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title || '--'}
                              </div>
                            );
                          })
                          : "--"}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title || '--'}
                        </span>
                      ) : (
                        <span
                          slot="reference"
  
  
                        >
                          {"--"}
                        </span>
                      )}
                    </el-popover>
                  </div>
                </div>
              )
            }
          },
          {
            label:"最后通话状态",
            prop:"",
            width:"160px",
            render:(j,data)=>{
              return (
                <div>
                  <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                    ? 'success'
                    : data.row.last_call_status && data.row.last_call_status === 2
                      ? 'danger'
                      : 'info'}>
                    {data.row.last_call_status && data.row.last_call_status === 1
                      ? '已接通'
                      : data.row.last_call_status && data.row.last_call_status === 2
                        ? '未接通'
                        : '未联系'}
                  </el-tag>
                </div>
              )
            }
          },
          {
            label: "客户标签",
            prop: "label",
            width: "220px",
            render: (j, data) => {
              return (
  
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user cus-clue-label">
                      {data.row.label && data.row.label.length > 0 ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.label.join(",")}
  
                          </div>
                          <div class="cus-userName w100 flex-row" slot="reference">
                            {data.row.label.length > 0
                              ? data.row.label.slice(0, 2).map((item, index) => {
                                return (
                                  <div class="flex-row align-center">
                                    <span class="cus-icon-label" key={index}>
                                      {item}
                                    </span>
                                  </div>
                                );
                              })
                              : '--'}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
  
                    </div>
                  </div>
                  {data.row.label && data.row.label.length > 0 ? (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  ) : (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  )}
                </div>
              )
            }
          },
          {
            label: "录入人",
            prop: "create_user",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.create_user && data.row.create_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.create_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
  
          {
            label: "维护人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.follow_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "带看人",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.take_user && data.row.take_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.take_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
  
          {
            label: "成交人",
            prop: "deal_user",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.deal_user && data.row.deal_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.deal_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
          {
            label: "成交周期",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                </div>
              )
            }
          },
          {
            label: "跟进状态",
            prop: "public_status",
            minWidth: "120px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.public_status == 2 && data.row.public2_status ? (
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(
                        data.row.public2_status, data.row.go_sea_day
                      )}
                    </span>
                  ) : (
                    "正常"
                  )}
                </div>
              )
            }
          },
          {
            label: "客户线索",
            prop: "remark",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="flex-box">
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.remark}
                    </div>
                    <div class="cus-clue-text">
                      <span>{data.row.remark || '--'}</span>
                    </div>
                  </el-tooltip>
  
                </div>
              );
            },
          },
          {
            label: "客户类型",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.client_type ? (
                    <el-tag type="info">{data.row.client_type.title}</el-tag>
                  ) : "--"}
                </div>
              )
            }
          },
          {
            label: "创建时间",
            prop: "created_at",
            minWidth: "200",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  </div>
                </div>
              );
            },
          },
          {
            prop: "updated_at",
            label: "更新时间",
            // sortable: "custom",
            width: "200px",
            render: (j, data) => {
              return (
                <div>
                  {data.row.last_follow_day >= 0 &&
                    data.row.last_follow_day != "" ? (
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content" style="max-width:300px">
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                      <div class="follow-content">
                        <span style="margin-right: 5px;">
                          {this.$options.filters.getPastDay(
                            data.row.last_follow_day
                          )}
                        </span>
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                    </el-tooltip>
                  ) : (
                    ""
                  )}
                  <div style="text-align: left;">{data.row.updated_at}</div>
                  <div
                    class="followLabel"
                    onClick={() => {
                      this.fastFollowUp(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                </div>
              );
            },
          },
          {
  
            minWidth: "190",
            label: "来源",
            render: (h, data) => {
              return (
                <div>
                  {data.row && data.row.source ? (
                    <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                  ) : (
                    ""
                  )}
                </div>
              );
            },
          },
          {
            label: "归属地",
            minWidth: "200",
            prop: "mobile",
            render: (h, data) => {
              return (
                <div class="flex-box table-btns">
                  <span class="search-Belong">
                    {data.row.mobile_place == "" ||
                      data.row.mobile_place == undefined ? (
                      <el-button
                        type="primary"
                        plain
                        onClick={() => {
                          this.HomeAddress(data.row);
                        }}
                      >
                        归属地查询
                      </el-button>
                    ) : (
                      <span class="Address">{data.row.mobile_place}</span>
                    )}
                  </span>
                </div>
              );
            },
          },
  
          {
            label: "操作",
            minWidth: "150",
            fixed: "right",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    详情
                  </el-link>
  
                </div>
              );
            },
          },
  
        ],
        //带看记录 
        table_header2: [
          // {
          //   prop: "id",
          //   label: "ID",
          //   width: "80px",
          // },
  
          {
            label: "客户名称",
            width: "200px",
            fixed: "left",
            // sortable: "custom",
            render: (h, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.cname ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div style="align-items: center;" class="flex-row">
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>
                          <div class="flex-row">
                            客户名称：{data.row.cname}
  
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.cname}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                    </div>
                    <div class="cus-box-foot flex-row">
  
                      {data.row && data.row.create_id == this.status_id ? (
                        <span class="cus-icon-type">私客</span>
                      ) : null}
  
  
                    </div>
                  </div>
                  {data.row.wxqy_id > 0 ? (
                    <img
                      class="cus-img"
                      src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                    />
                  ) : (
                    ""
                  )}
                  {/* <div
                    class="fast-Edit-cus"
                    onClick={() => {
                      this.fastEditData(data.row);
                    }}
                  >
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      alt=""
                    />
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "手机号",
            fixed: "left",
            width: "200",
            prop: "mobile",
            render: (h, data) => {
              // const mobileFilter = function (val) {
              //   let reg = /^(.{3}).*(.{3})$/;
              //   return val.replace(reg, "$1*****$2");
              // };
              if(!data.row.mobile){
                return('');
              }
              return (
                <div class="flex-box table-btns">
                  {data.row.last_call_follow
                    &&
                    data.row.last_call_follow.id
                    ?
                    (<div class="last_call_follow w180 div row">
                      <el-tooltip
                        class="item"
                        style="display:flex;flex-direction:row;justify-content:center"
                        effect="light"
                        placement="top"
                      >
                        <div slot="content" style="width:300px">
                          {data.row.last_call_follow.content}
                        </div>
                        <div class="cus-clue-text">
                          {data.row.last_call_follow
                            &&
                            data.row.last_call_follow.call_status == 1
                            ?
                            (<span class='cus-clue-text_c'>{data.row.mobile}</span>)
                            :
                            (<span class='cus-clue-text_u'>{data.row.mobile}</span>)}
                        </div>
                      </el-tooltip>
                    </div>)
                    :
                    (<span class='cus-clue-text_n'>{data.row.mobile}</span>)}
                  {/* <div
                    class="fast-look-tel"
                    onClick={() => {
                      this.fastLookTel(data.row);
                    }}
                  >
                    <i class="el-icon-phone"></i>
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "最新带看记录",
            prop: "last_take_info",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {
                        data.row.last_take_info && data.row.last_take_info.content ? (
                          <el-popover
                            placement="top-start"
                            width="200"
                            trigger="hover"
                          >
                            <div class="flex-row">
                              {data.row.last_take_info.content}
                            </div>
                            <div class="cus-userName cus-userName1" slot="reference">
                              {data.row.last_take_info.content}
                            </div>
                          </el-popover>
                        ) : (
                          ""
                        )
                      }
                    </div>
                  </div>
                </div>
              );
            }
          },
          {
            label: "带看次数",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.last_take_info && data.row.last_take_info.take_num || 0}
                </div>
              )
            }
          },
  
          {
            label: "最近带看时间",
            prop: "client_type",
            minWidth: "135px",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.last_take_info ? (
                    <span style="margin-right: 5px;">
                      {data.row.last_take_info.take_date}{data.row.last_take_info.take_time == 1 ? '上午' : (data.row.last_take_info.take_time == 2 ? '下午' : '晚上')}
                    </span>
                  ) : '--'}
                </div>
              )
            }
          },
          {
                  label: "项目名称",
                  prop: "last_take_info",
                  minWidth: "135px",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_take_info&&data.row.last_take_info.project_name
                          ? data.row.last_take_info.project_name
                          : '--'}
                      </div>
                    )
                  }
                },
          {
            label: "带看人",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.last_take_info ? (
                    <span style="margin-right: 5px;">
                      {data.row.last_take_info.admin ? data.row.last_take_info.admin.user_name : '--'}
                    </span>
                  ) : '--'}
                </div>
              )
            }
          },
          {
            label: "陪看人",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.last_take_info&&data.row.last_take_info.accompany ? (
                    <span style="margin-right: 5px;">
                      {data.row.last_take_info.accompany}
                    </span>
                  ) : '--'}
                </div>
              )
            }
          },
  
          {
            label: "客户等级",
            prop: "level",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: center;">
                  <div class="cus-box-foot">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : '空'}
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户状态",
            prop: "tracking",
            width: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot" style="width:80px">
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="100"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title}
                              </div>
                            );
                          })
                          : null}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title}
                        </span>
                      ) : (
                        ""
                      )}
                    </el-popover>
                  </div>
                </div>
              )
            }
          },
          {
            label:"最后通话状态",
            prop:"",
            width:"160px",
            render:(j,data)=>{
              return (
                <div>
                  <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                    ? 'success'
                    : data.row.last_call_status && data.row.last_call_status === 2
                      ? 'danger'
                      : 'info'}>
                    {data.row.last_call_status && data.row.last_call_status === 1
                      ? '已接通'
                      : data.row.last_call_status && data.row.last_call_status === 2
                        ? '未接通'
                        : '未联系'}
                  </el-tag>
                </div>
              )
            }
          },
          {
            label: "客户标签",
            prop: "label",
            width: "200px",
            render: (j, data) => {
              return (
  
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user cus-clue-label">
                      {data.row.label && data.row.label.length ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.label.join(",")}
  
                          </div>
                          <div class="cus-userName w100 flex-row" slot="reference">
                            {data.row.label.length
                              ? data.row.label.slice(0, 2).map((item, index) => {
                                return (
                                  <div class="flex-row align-center">
                                    <span class="cus-icon-label" key={index}>
                                      {item}
                                    </span>
                                  </div>
                                );
                              })
                              : '--'}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
  
                    </div>
                  </div>
                  {data.row.label && data.row.label.length ? (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  ) : (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  )}
                </div>
              )
            }
          },
          {
            label: "录入人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.create_user && data.row.create_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.create_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "维护人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.follow_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "成交人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.deal_user && data.row.deal_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.deal_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "跟进状态",
            prop: "public_status",
            minWidth: "120px",
            render: (j, data) => {
              return (
                <div style="text-align:center">
                  {data.row.public_status == 2 && data.row.public2_status ? (
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(
                        data.row.public2_status, data.row.go_sea_day
                      )}
                    </span>
                  ) : (
                    <span
                      class="cus-icon-customer"
                    >
                      正常
                    </span>
                  )}
                </div>
              )
            }
          },
          {
            label: "客户线索",
            prop: "remark",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="flex-box">
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.remark}
                    </div>
                    <div class="cus-clue-text">
                      <span>{data.row.remark}</span>
                    </div>
                  </el-tooltip>
                </div>
              );
            },
          },
          {
            label: "客户类型",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: center;">
                  {data.row.client_type ? (
                    <el-tag type="info">{data.row.client_type.title}</el-tag>
                  ) : null}
                </div>
              )
            }
          },
          {
            label: "创建时间",
            prop: "created_at",
            minWidth: "200",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  </div>
                </div>
              );
            },
          },
          {
            prop: "updated_at",
            label: "更新时间",
            // sortable: "custom",
            width: "200px",
            render: (j, data) => {
              return (
                <div>
                  {data.row.last_follow_day >= 0 &&
                    data.row.last_follow_day != "" ? (
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content" style="max-width:300px">
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                      <div class="follow-content">
                        <span style="margin-right: 5px;">
                          {this.$options.filters.getPastDay(
                            data.row.last_follow_day
                          )}
                        </span>
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                    </el-tooltip>
                  ) : (
                    ""
                  )}
                  <div style="text-align: left;">{data.row.updated_at}</div>
                  <div
                    class="followLabel"
                    onClick={() => {
                      this.fastFollowUp(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                </div>
              );
            },
          },
          {
            minWidth: "190",
            label: "来源",
            render: (h, data) => {
              return (
                <div>
                  {data.row && data.row.source ? (
                    <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                  ) : (
                    ""
                  )}
                </div>
              );
            },
          },
          {
            label: "归属地",
            minWidth: "200",
            prop: "mobile",
            render: (h, data) => {
  
              return (
                <div class="flex-box table-btns">
                  <span class="search-Belong">
                    {data.row.mobile_place == "" ||
                      data.row.mobile_place == undefined ? (
                      <el-button
                        type="primary"
                        plain
                        onClick={() => {
                          this.HomeAddress(data.row);
                        }}
                      >
                        归属地查询
                      </el-button>
                    ) : (
                      <span class="Address">{data.row.mobile_place}</span>
                    )}
                  </span>
                </div>
              );
            },
          },
  
          {
            label: "操作",
            minWidth: "150",
            fixed: "right",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    详情
                  </el-link>
  
                </div>
              );
            },
          },
        ],
        //外呼记录
        table_header3: [
          {
            label: "客户名称",
            width: "200px",
            fixed: "left",
            // sortable: "custom",
            render: (h, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.cname ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div style="align-items: center;" class="flex-row">
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>
                          <div class="flex-row">
                            客户名称：{data.row.cname}
  
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.cname}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                    </div>
                    <div class="cus-box-foot flex-row">
  
                      {data.row && data.row.create_id == this.status_id ? (
                        <span class="cus-icon-type">私客</span>
                      ) : null}
  
  
                    </div>
                  </div>
                  {data.row.wxqy_id > 0 ? (
                    <img
                      class="cus-img"
                      src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                    />
                  ) : (
                    ""
                  )}
                  {/* <div
                    class="fast-Edit-cus"
                    onClick={() => {
                      this.fastEditData(data.row);
                    }}
                  >
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      alt=""
                    />
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "手机号",
            fixed: "left",
            width: "200px",
            prop: "mobile",
            render: (h, data) => {
              // const mobileFilter = function (val) {
              //   let reg = /^(.{3}).*(.{3})$/;
              //   return val.replace(reg, "$1*****$2");
              // };
              if(!data.row.mobile){
                return('');
              }
              return (
                <div class="flex-box table-btns">
                  {data.row.last_call_info
                    &&
                    data.row.last_call_info.id
                    ?
                    (<div class="last_call_follow w180 div row">
                      <el-tooltip
                        class="item"
                        style="display:flex;flex-direction:row;justify-content:center"
                        effect="light"
                        placement="top"
                      >
                        <div slot="content" style="max-width:300px">
                          {data.row.last_call_info.content}
                        </div>
                        <div class="cus-clue-text">
                          {data.row.last_call_info
                            &&
                            data.row.last_call_info.call_status == 1
                            ?
                            (<span class='cus-clue-text_c'>{data.row.mobile}</span>)
                            :
                            (<span class='cus-clue-text_u'>{data.row.mobile}</span>)}
                        </div>
                      </el-tooltip>
                    </div>)
                    :
                    (<span class='cus-clue-text_n'>{data.row.mobile}</span>)}
  
                  {/* <div
                    class="fast-look-tel"
                    onClick={() => {
                      this.fastLookTel(data.row);
                    }}
                  >
                    <i class="el-icon-phone"></i>
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "最近拨打记录",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.last_call_info ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
  
                          <div class="flex-row">
                            {data.row.last_call_info.content}
  
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.last_call_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        <span></span>
                      )}
  
                    </div>
                  </div>
                </div>
              );
            },
          },
  
          {
            label: "拨打次数",
            prop: "call_num",
            minWidth: "100px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info && data.row.call_info.call_num || "--"}
                  </div>
                </div>
              );
            },
          },
          {
            label: "接通次数",
            prop: "call_open_num",
            minWidth: "100px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info && data.row.call_info.call_open_num || '--'}
                  </div>
                </div>
              );
            },
          },
          {
            label: "最近状态",
            prop: "last_call_status",
            minWidth: "100px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <el-tag type={data.row.call_info && data.row.call_info.last_call_status == 1 ? 'success ' : 'danger'}>{data.row.call_info && data.row.call_info.last_call_status == 1 ? "接通" : "未接通"}</el-tag>
                  </div>
                </div>
              );
            },
          },
          {
            label: "通话录音",
            prop: "",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.last_call_info && data.row.last_call_info.record_url ? (<el-link
                    style="margin-left: 5px"
                    onClick={() => { this.play1(data.row) }}>
                    <div class="audio_img">
  
                      <img
                        style="width: 20px; object-fit: cover"
                        src={data.row.isPlaying ? `${this.$imageDomain}/static/admin/outbound/play_voice.gif` : `${this.$imageDomain}/static/admin/outbound/voice_icon.png`}
                        alt=""
                      />
  
                    </div>
                  </el-link>) : '--'}
  
                </div >
              );
            },
          },
  
          {
            label: "最近通话时长",
            prop: "last_call_duration",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {this.formatTelTime(data.row.call_info && data.row.call_info.last_call_duration || 0)}
                  </div>
                </div>
              );
            },
          },
          {
            label: "首电响应时长",
            prop: "first_call_day",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info ? (data.row.call_info.first_call_day == "" ? "" : data.row.call_info.first_call_day) : ''}
                  </div>
                </div>
              );
            },
          },
          {
            label: "复电响应时长",
            prop: "repeat_call_day",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info ? (data.row.call_info.repeat_call_day == "" ? "" : data.row.call_info.repeat_call_day) : ''}
                  </div>
                </div>
              );
            },
          },
          {
            label: "最近拨打成员",
            prop: "last_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info && data.row.call_info.last_call_name || '--'}
                  </div>
                </div>
              );
            },
          },
          {
            label: "首次拨打成员",
            prop: "first_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info && data.row.call_info.first_call_name || '--'}
                  </div>
                </div>
              );
            },
          }
          ,
          {
            label: "二次复电成员",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    {data.row.call_info && data.row.call_info.repeat_call_name || '--'}
                  </div>
                </div>
              );
            },
          },
          {
            label: "录入人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.create_user && data.row.create_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.create_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "维护人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.follow_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "带看人",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.take_user && data.row.take_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.take_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "成交人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.deal_user && data.row.deal_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.deal_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "客户类型",
            prop: "client_type",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.client_type ? (
                    <el-tag type="info">{data.row.client_type.title}</el-tag>
                  ) : null}
                </div>
              )
            }
          },
          {
            label: "客户等级",
            prop: "level",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : '空'}
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户状态",
            prop: "tracking",
            width: "200px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot" style="width:80px">
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="100"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title}
                              </div>
                            );
                          })
                          : null}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title}
                        </span>
                      ) : (
                        ""
                      )}
                    </el-popover>
                  </div>
                </div>
              )
            }
          },
          {
            label: "创建时间",
            prop: "created_at",
            minWidth: "200",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  </div>
                </div>
              );
            },
          },
          {
            prop: "updated_at",
            label: "更新时间",
            // sortable: "custom",
            width: "200",
            render: (j, data) => {
              return (
                <div>
                  {data.row.last_follow_day >= 0 &&
                    data.row.last_follow_day != "" ? (
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content" style="max-width:300px">
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                      <div class="follow-content">
                        <span style="margin-right: 5px;">
                          {this.$options.filters.getPastDay(
                            data.row.last_follow_day
                          )}
                        </span>
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                    </el-tooltip>
                  ) : (
                    ""
                  )}
                  <div style="text-align: left;">{data.row.updated_at}</div>
                  <div
                    class="followLabel"
                    onClick={() => {
                      this.fastFollowUp(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                </div>
              );
            },
          },
          {
            label: "归属地",
            minWidth: "200",
            prop: "mobile",
            render: (h, data) => {
              return (
                <div class="flex-box table-btns">
                  <span class="search-Belong">
                    {data.row.mobile_place == "" ||
                      data.row.mobile_place == undefined ? (
                      <el-button
                        type="primary"
                        plain
                        onClick={() => {
                          this.HomeAddress(data.row);
                        }}
                      >
                        归属地查询
                      </el-button>
                    ) : (
                      <span class="Address">{data.row.mobile_place}</span>
                    )}
                  </span>
                  {/* <div
                    class="fast-look-tel"
                    onClick={() => {
                      this.fastLookTel(data.row);
                    }}
                  >
                    <i class="el-icon-phone"></i>
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "操作",
            minWidth: "150",
            fixed: "right",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    详情
                  </el-link>
  
                </div>
              );
            },
          },
  
        ],
        table_header4: [
          // {
          //   prop: "id",
          //   label: "ID",
          //   width: "80px",
          // },
  
          {
            label: "客户名称",
            width: "200px",
            fixed: "left",
            // sortable: "custom",
            render: (h, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.cname ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div style="align-items: center;" class="flex-row">
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>
                          <div class="flex-row">
                            客户名称：{data.row.cname}
  
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.cname}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                    </div>
                    <div class="cus-box-foot flex-row">
  
                      {data.row && data.row.create_id == this.status_id ? (
                        <span class="cus-icon-type">私客</span>
                      ) : null}
  
  
                    </div>
                  </div>
                  {data.row.wxqy_id > 0 ? (
                    <img
                      class="cus-img"
                      src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                    />
                  ) : (
                    ""
                  )}
                  {/* <div
                    class="fast-Edit-cus"
                    onClick={() => {
                      this.fastEditData(data.row);
                    }}
                  >
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      alt=""
                    />
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "手机号",
            fixed: "left",
            width: "200",
            prop: "mobile",
            render: (h, data) => {
              // const mobileFilter = function (val) {
              //   let reg = /^(.{3}).*(.{3})$/;
              //   return val.replace(reg, "$1*****$2");
              // };
              if(!data.row.mobile){
                return('');
              }
              return (
                <div class="flex-box table-btns">
                  {data.row.last_call_follow
                    &&
                    data.row.last_call_follow.id
                    ?
                    (<div class="last_call_follow w180 div row">
                      <el-tooltip
                        class="item"
                        style="display:flex;flex-direction:row;justify-content:center"
                        effect="light"
                        placement="top"
                      >
                        <div slot="content" style="max-width:300px">
                          {data.row.last_call_follow.content}
                        </div>
                        <div class="cus-clue-text">
                          {data.row.last_call_follow
                            &&
                            data.row.last_call_follow.call_status == 1
                            ?
                            (<span class='cus-clue-text_c'>{data.row.mobile}</span>)
                            :
                            (<span class='cus-clue-text_u'>{data.row.mobile}</span>)}
                        </div>
                      </el-tooltip>
                    </div>)
                    :
                    (<span class='cus-clue-text_n'>{data.row.mobile}</span>)}
                  {/* <div
                    class="fast-look-tel"
                    onClick={() => {
                      this.fastLookTel(data.row);
                    }}
                  >
                    <i class="el-icon-phone"></i>
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "最新维护记录",
            prop: "last_follow_info",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.last_follow_info ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
  
                          <div class="flex-row">
                            {data.row.last_follow_info.content}
  
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.last_follow_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                </div>
              );
            },
          },
          {
            label: "跟客天数",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.follow_day !== '' && data.row.last_follow_info.follow_day >= 0 ? (data.row.last_follow_info.follow_day + '天') : '--'}
  
                </div>
              )
            }
          },
  
          {
            label: "客户等级",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : "空"}
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户意向",
            prop: "",
            width: "100px",
            render: (j, data) => {
              return (
                <el-tooltip class="item" effect="light" placement="top">
                  <div slot="content" >
                    {data.row.intention_community}
                  </div>
                  <div class="cus-clue-text cus-clue-text_community">
                    <span>{data.row.intention_community || '--'}</span>
                  </div>
                </el-tooltip>
              )
            }
          },
          {
            label: "客户状态",
            prop: "tracking",
            width: "130px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot" style="width:80px">
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="100"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title || '--'}
                              </div>
                            );
                          })
                          : "--"}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title || '--'}
                        </span>
                      ) : (
                        <span
                          slot="reference"
  
  
                        >
                          {"--"}
                        </span>
                      )}
                    </el-popover>
                  </div>
                </div>
              )
            }
          },
          {
            label:"最后通话状态",
            prop:"",
            width:"160px",
            render:(j,data)=>{
              return (
                <div>
                  <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                    ? 'success'
                    : data.row.last_call_status && data.row.last_call_status === 2
                      ? 'danger'
                      : 'info'}>
                    {data.row.last_call_status && data.row.last_call_status === 1
                      ? '已接通'
                      : data.row.last_call_status && data.row.last_call_status === 2
                        ? '未接通'
                        : '未联系'}
                  </el-tag>
                </div>
              )
            }
          },
          {
            label: "客户标签",
            prop: "label",
            width: "220px",
            render: (j, data) => {
              return (
  
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user cus-clue-label">
                      {data.row.label && data.row.label.length > 0 ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.label.join(",")}
  
                          </div>
                          <div class="cus-userName w100 flex-row" slot="reference">
                            {data.row.label.length > 0
                              ? data.row.label.slice(0, 2).map((item, index) => {
                                return (
                                  <div class="flex-row align-center">
                                    <span class="cus-icon-label" key={index}>
                                      {item}
                                    </span>
                                  </div>
                                );
                              })
                              : '--'}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
  
                    </div>
                  </div>
                  {data.row.label && data.row.label.length > 0 ? (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  ) : (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  )}
                </div>
              )
            }
          },
          {
            label: "录入人",
            prop: "create_user",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.create_user && data.row.create_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.create_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
  
          {
            label: "维护人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.follow_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "带看人",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.take_user && data.row.take_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.take_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
  
          {
            label: "成交人",
            prop: "deal_user",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.deal_user && data.row.deal_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.deal_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
          {
            label: "成交周期",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                </div>
              )
            }
          },
          {
            label: "跟进状态",
            prop: "public_status",
            minWidth: "120px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.public_status == 2 && data.row.public2_status ? (
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(
                        data.row.public2_status, data.row.go_sea_day
                      )}
                    </span>
                  ) : (
                    "正常"
                  )}
                </div>
              )
            }
          },
          {
            label: "客户线索",
            prop: "remark",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="flex-box">
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.remark}
                    </div>
                    <div class="cus-clue-text">
                      <span>{data.row.remark || '--'}</span>
                    </div>
                  </el-tooltip>
  
                </div>
              );
            },
          },
          {
            label: "客户类型",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.client_type ? (
                    <el-tag type="info">{data.row.client_type.title}</el-tag>
                  ) : "--"}
                </div>
              )
            }
          },
          {
            label: "创建时间",
            prop: "created_at",
            minWidth: "200",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  </div>
                </div>
              );
            },
          },
          {
            prop: "updated_at",
            label: "更新时间",
            // sortable: "custom",
            width: "200px",
            render: (j, data) => {
              return (
                <div>
                  {data.row.last_follow_day >= 0 &&
                    data.row.last_follow_day != "" ? (
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content" style="max-width:300px">
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                      <div class="follow-content">
                        <span style="margin-right: 5px;">
                          {this.$options.filters.getPastDay(
                            data.row.last_follow_day
                          )}
                        </span>
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                    </el-tooltip>
                  ) : (
                    ""
                  )}
                  <div style="text-align: left;">{data.row.updated_at}</div>
                  <div
                    class="followLabel"
                    onClick={() => {
                      this.fastFollowUp(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                </div>
              );
            },
          },
          {
            minWidth: "190",
            label: "来源",
            render: (h, data) => {
              return (
                <div>
                  {data.row && data.row.source ? (
                    <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                  ) : (
                    ""
                  )}
                </div>
              );
            },
          },
          {
            label: "归属地",
            minWidth: "200",
            prop: "mobile",
            render: (h, data) => {
              return (
                <div class="flex-box table-btns">
                  <span class="search-Belong">
                    {data.row.mobile_place == "" ||
                      data.row.mobile_place == undefined ? (
                      <el-button
                        type="primary"
                        plain
                        onClick={() => {
                          this.HomeAddress(data.row);
                        }}
                      >
                        归属地查询
                      </el-button>
                    ) : (
                      <span class="Address">{data.row.mobile_place}</span>
                    )}
                  </span>
                </div>
              );
            },
          },
  
          {
            label: "操作",
            minWidth: "150",
            fixed: "right",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    详情
                  </el-link>
  
                </div>
              );
            },
          },
  
        ],
        table_header5: [
          // {
          //   prop: "id",
          //   label: "ID",
          //   minWidth: "80px",
          // },
  
          {
            label: "客户名称",
            width: "200px",
            fixed: "left",
            // sortable: "custom",
            render: (h, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.cname ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div style="align-items: center;" class="flex-row">
                            客户编号：{data.row.id}
                            <el-button
                              size="mini"
                              type="success"
                              style="margin-left: 20px"
                              onClick={() => {
                                this.copyCusID(data.row.id);
                              }}
                            >
                              复制
                            </el-button>
                          </div>
                          <div class="flex-row">
                            客户名称：{data.row.cname}
  
                          </div>
                          <div class="cus-userName" slot="reference">
                            {data.row.cname}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                      <div class="cus-sex">
                        {data.row.sex && data.row.sex == 1 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                            alt=""
                          />
                        ) : null}
                        {data.row.sex && data.row.sex == 2 ? (
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                            alt=""
                          />
                        ) : null}
                      </div>
                    </div>
                    <div class="cus-box-foot flex-row">
  
                      {data.row && data.row.create_id == this.status_id ? (
                        <span class="cus-icon-type">私客</span>
                      ) : null}
  
  
                    </div>
                  </div>
                  {data.row.wxqy_id > 0 ? (
                    <img
                      class="cus-img"
                      src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                    />
                  ) : (
                    ""
                  )}
                  {/* <div
                    class="fast-Edit-cus"
                    onClick={() => {
                      this.fastEditData(data.row);
                    }}
                  >
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      alt=""
                    />
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "手机号",
            fixed: "left",
            width: "200px",
            prop: "mobile",
            render: (h, data) => {
              // const mobileFilter = function (val) {
              //   let reg = /^(.{3}).*(.{3})$/;
              //   return val.replace(reg, "$1*****$2");
              // };
              if(!data.row.mobile){
                return('');
              }
              return (
                <div class="flex-box table-btns">
                  {data.row.last_call_follow
                    &&
                    data.row.last_call_follow.id
                    ?
                    (<div class="last_call_follow div row">
                      <el-tooltip
                        class="item"
                        style="display:flex;flex-direction:row;justify-content:center"
                        effect="light"
                        placement="top"
                      >
                        <div slot="content" style="max-width:300px">
                          {data.row.last_call_follow.content}
                        </div>
                        <div class="cus-clue-text">
                          {data.row.last_call_follow
                            &&
                            data.row.last_call_follow.call_status == 1
                            ?
                            (<span class='cus-clue-text_c'>{data.row.mobile}</span>)
                            :
                            (<span class='cus-clue-text_u'>{data.row.mobile}</span>)}
                        </div>
                      </el-tooltip>
                    </div>)
                    :
                    (<span class='cus-clue-text_n'>{data.row.mobile}</span>)}
                  {/* <div
                    class="fast-look-tel"
                    onClick={() => {
                      this.fastLookTel(data.row);
                    }}
                  >
                    <i class="el-icon-phone"></i>
                  </div> */}
                </div>
              );
            },
          },
          {
            label: "最新回访记录",
            prop: "last_follow_info",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user">
                      {data.row.last_follow_info ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
  
                          <div class="flex-row">
                            {data.row.last_follow_info.content}
  
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.last_follow_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                </div>
              );
            },
          },
          {
            label: "跟客天数",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.follow_day !== '' && data.row.last_follow_info.follow_day >= 0 ? (data.row.last_follow_info.follow_day + '天') : '--'}
  
                </div>
              )
            }
          },
  
          {
            label: "客户等级",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : "空"}
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户意向",
            prop: "",
            width: "100px",
            render: (j, data) => {
              return (
                <el-tooltip class="item" effect="light" placement="top">
                  <div slot="content" >
                    {data.row.intention_community}
                  </div>
                  <div class="cus-clue-text cus-clue-text_community">
                    <span>{data.row.intention_community || '--'}</span>
                  </div>
                </el-tooltip>
              )
            }
          },
          {
            label: "客户状态",
            prop: "tracking",
            width: "130px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  <div class="cus-box-foot" style="width:80px">
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="100"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title || '--'}
                              </div>
                            );
                          })
                          : "--"}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title || '--'}
                        </span>
                      ) : (
                        <span
                          slot="reference"
  
  
                        >
                          {"--"}
                        </span>
                      )}
                    </el-popover>
                  </div>
                </div>
              )
            }
          },
          {
            label: "客户标签",
            prop: "label",
            width: "220px",
            render: (j, data) => {
              return (
  
                <div class="cus-box div row">
                  <div class="cus-box-header flex-box">
                    <div class="flex-row cus-header-user cus-clue-label">
                      {data.row.label && data.row.label.length > 0 ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.label.join(",")}
  
                          </div>
                          <div class="cus-userName w100 flex-row" slot="reference">
                            {data.row.label.length > 0
                              ? data.row.label.slice(0, 2).map((item, index) => {
                                return (
                                  <div class="flex-row align-center">
                                    <span class="cus-icon-label" key={index}>
                                      {item}
                                    </span>
                                  </div>
                                );
                              })
                              : '--'}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )}
  
                    </div>
                  </div>
                  {data.row.label && data.row.label.length > 0 ? (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  ) : (
                    <div
                      class="clueLabel"
                      onClick={() => {
                        this.fastEditLabel(data.row);
                      }}
                    >
                      <img
                        src={
                          "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                        }
                        alt=""
                      />
                    </div>
                  )}
                </div>
              )
            }
          },
          {
            label: "录入人",
            prop: "create_user",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.create_user && data.row.create_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.create_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
  
          {
            label: "维护人",
            prop: "repeat_call_name",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.follow_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
          {
            label: "带看人",
            minWidth: "110px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.take_user && data.row.take_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.take_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              );
            },
          },
  
          {
            label: "成交人",
            prop: "deal_user",
            minWidth: "150px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.deal_user && data.row.deal_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {data.row.deal_user.user_name}
                    </span>
                  ) : (
                    "--"
                  )}
                </div>
              )
            }
          },
          {
            label: "成交周期",
            prop: "",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div class="cus-box" style="text-align: left;">
                  {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                </div>
              )
            }
          },
          {
            label: "跟进状态",
            prop: "public_status",
            minWidth: "120px",
            render: (j, data) => {
              return (
                <div style="text-align:left">
                  {data.row.public_status == 2 && data.row.public2_status ? (
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(
                        data.row.public2_status, data.row.go_sea_day
                      )}
                    </span>
                  ) : (
                    "正常"
                  )}
                </div>
              )
            }
          },
          {
            label: "客户线索",
            prop: "remark",
            width: "200px",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div class="flex-box">
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.remark}
                    </div>
                    <div class="cus-clue-text">
                      <span>{data.row.remark || '--'}</span>
                    </div>
                  </el-tooltip>
  
                </div>
              );
            },
          },
          {
            label: "客户类型",
            prop: "client_type",
            minWidth: "100px",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  {data.row.client_type ? (
                    <el-tag type="info">{data.row.client_type.title}</el-tag>
                  ) : "--"}
                </div>
              )
            }
          },
          {
            label: "创建时间",
            prop: "created_at",
            minWidth: "200",
            // sortable: "custom",
            render: (j, data) => {
              return (
                <div style="text-align: left;">
                  <div>
                    <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  </div>
                </div>
              );
            },
          },
          {
            prop: "updated_at",
            label: "更新时间",
            // sortable: "custom",
            width: "200",
            render: (j, data) => {
              return (
                <div>
                  {data.row.last_follow_day >= 0 &&
                    data.row.last_follow_day != "" ? (
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content" style="max-width:300px">
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                      <div class="follow-content">
                        <span style="margin-right: 5px;">
                          {this.$options.filters.getPastDay(
                            data.row.last_follow_day
                          )}
                        </span>
                        {data.row.last_follow && data.row.last_follow.content
                          ? data.row.last_follow.content
                          : null}
                      </div>
                    </el-tooltip>
                  ) : (
                    ""
                  )}
                  <div style="text-align: left;">{data.row.updated_at}</div>
                  <div
                    class="followLabel"
                    onClick={() => {
                      this.fastFollowUp(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                </div>
              );
            },
          },
          {
            minWidth: "190",
            label: "来源",
            render: (h, data) => {
              return (
                <div>
                  {data.row && data.row.source ? (
                    <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                  ) : (
                    ""
                  )}
                </div>
              );
            },
          },
          {
            label: "归属地",
            minWidth: "200",
            prop: "mobile",
            render: (h, data) => {
              return (
                <div class="flex-box table-btns">
                  <span class="search-Belong">
                    {data.row.mobile_place == "" ||
                      data.row.mobile_place == undefined ? (
                      <el-button
                        type="primary"
                        plain
                        onClick={() => {
                          this.HomeAddress(data.row);
                        }}
                      >
                        归属地查询
                      </el-button>
                    ) : (
                      <span class="Address">{data.row.mobile_place}</span>
                    )}
                  </span>
                </div>
              );
            },
          },
  
          {
            label: "操作",
            minWidth: "150",
            fixed: "right",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    详情
                  </el-link>
  
                </div>
              );
            },
          },
  
        ],
        is_follow_dialog: false,
        is_follow_data: [],
        is_follow_loading: false,
        is_follow_params: {
          page: 1,
          total: 0,
          per_page: 10,
        },
        label_list: [],
        multipleSelectionname: [],
        is_collapse: true,
        tracking_params: {
          type: 4,
        },
        is_dialog_upload: false,
        uploadAdmin_id: "",
        uploadAdmin_id1: "",
        upload_form: {
          type: 1, // 是否覆盖数据 1：不覆盖 2：覆盖
          admin_id: "", // 管理员id
          create_id: "",
          file: "", // 导入的文件
          type_id: "", // 客户类型id
          source_id: "", // 客户来源
          label: "", // 标签:字符串格式，多个用逗号隔开
          remark: "", // 客户备注线索
        },
        admin_params: {
          page: 1,
          per_page: 10,
          total: 0,
          user_name: "",
        },
        admin_list: [],
        is_button_loading: false,
        pull_params: {
          next_user_cursor: 0,
          next_customer_cursor: "",
        },
        list_tabs: [
          { id: 0, title: "全部" },
          { id: 1, title: "已认领" },
          { id: 2, title: "已跟进" },
          { id: 3, title: "未跟进" },
        ],
        customerstatus:[
          {id:1,title:"未联系"},
          {id:2,title:"未接通"},
          {id:3,title:"已接通"}
        ],
        tips: [
          "模板中的表头不可更改，表头行不可删除",
          "单次导入的数据不超过3000条",
          "标红标题对应内容为必填项，如不填写则过滤出去不进行导入",
          "同一个表格重复的手机号会导入1个客户，系统将按重复的顺序将客户线索内容追加到线索记录 ",
          "选择覆盖，导入的数据和已有数据重复时，会覆盖之前的数据，选择不覆盖则过滤掉重复数据",
        ],
        no_follow_number: "",
        show_tel_search: false,
        buttonhidden: true,
        myButton: false,
        myButton1: false,
        is_show_upload: false, // 是否显示导入按钮
        customer_list_type: [
          { id: 0, title: "新增线索", is_select: true },
          { id: 2, title: "最新跟进", is_select: true },
          { id: 3, title: "多条线索", is_select: true },
          // { id: 4, title: "最近活跃", is_select: false },
          // { id: 1, title: "已认领", is_select: false },
          // { id: 3, title: "未跟进", is_select: false },
          // { id: 4, title: "私客" },
          // { id: 5, title: "掉公" },
        ],
        selectedItemId: "",
        // 1:最近活跃,2:多条线索
        screen_list_type: [
          { id: 1, title: "已认领", is_select: true },
          { id: 5, title: "未跟进", is_select: true },
        ],
        screen_list_type1: [
          { id: "1_5", title: "待分配", is_select: true },
          { id: "2_5", title: "待跟进", is_select: true },
        ],
        screen_list_type2: [
          { id: "4", title: "最近活跃", is_select: true },
        ],
        customer_type: 0,
        notNewClue: true,
        is_show: 0, // 控制客户标签样式
        label_default_list: [], // 获取原始默认标签列表
        customerLabelList: {}, // 所选择的标签
        changeParentLabel: "", // 之前所选的上一级的标签
        is_all: true, // 控制客户标签，“全部”标签的样式
        datalist: [], // 全部部门人员
        show_Customer_label: false, // 控制客户标签模态框显示/隐藏
        labels_list: [], // 客户标签列表
        // 确定编辑客户标签的接口传参
        confirm_batch_list: {
          ids: "", // 客户id 多个用逗号隔开
          label: "", // 客户标签id 多个用逗号隔开
          type: "1", // 客户类型 1公海 2私客
        },
        pop_depart: false, // 显示/隐藏部门搜索popover
        AllDepartment: [], // 全部部门列表
        is_transfer_customer: false, // 转交客户模态框
        c_id: "", // 转让客户id：多个用，隔开
        selectedMember: "", // 选择搜索部门
        Not_duplicate_datalist: [], // 没有重复部门成员的容器
        filtrMember: [], // 选中部门后过滤出的部门成员
        status_id: "", // 登录后的个人id
        status_list: [], // 快速编辑客户状态数据容器
        copy_status_list: [], // 深拷贝客户状态
        getStutas_params: {
          type: 2,
        },
        is_loading: false,
        show_Examine_dialog: false, // 控制客户审批模态框
        ponent_Examine_data: {}, // 提交审批客户信息
        ponent_Examine_stutas: {}, // 选择的要修改的状态
        website_id: "", // 当前站点id
        show_cus_Edit: false, // 显示快速编辑客户维护资料模态框
        ponent_maintain_data: {}, // 客户信息
        ponent_maintain_source: [], // 深拷贝客户来源列表
        ponent_maintain_level: [], // 深拷贝客户来源列表
        ponent_maintain_type: [], // 深拷贝客户类型列表
        ponent_maintain_label: [], // 深拷贝客户标签列表
        show_look_Tel: false, // 显示快速查看客户手机号模态框
        ponent_Tel_data: {}, // 客户信息
        is_fast: false, // 是否是快速编辑标签
        fastEdit_params: {
          client_id: "", // 客户id
          label: "", // 标签id多个，隔开
        },
        Examine_type: 19, // 默认审批类型
        is_pullDown: false, // 是否展开下拉框
        is_pullDown1: false,
        ponent_Follow_data: {}, // 客户信息
        show_Follow_dialog: false, // 显示快速跟进客户模态框
        tabIndex: -1,
        is_small_system: false,
        is_pullDown2: false,
        show_right: false,
        nowDialData: {},
        placeholder: "请输入客户电话",
        user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_FILE_2}`,
        scrollTop: 0,
        topHeight: 330,
        source: "",
        third_source_list: [],
        // screenWidth: 1450
        shownum:false,
        copydata:[],
        dialogss: {
          transToMember: false
        }
      };
    },
    watch: {
      admin_list: {
        handler(newVal) {
          // 根据数据变化判断是否显示年龄列和性别列
          this.shownum = newVal.some(item => item.number);
          console.log(newVal.some(item => item.number));
        },
        deep: true // 监听对象内部属性的变化
      }
    },
  
    computed: {
      myHeader() {
        return {
          // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
          Authorization: config.TOKEN,
        };
      },
      levelLabel() {
        return this.level_list.filter((item) => {
          return item.id > 0;
        });
      },
      sourceLabel() {
        return this.source_list.filter((item) => {
          return item.id > 0;
        });
      },
      typeLabel() {
        return this.type_list.filter((item) => {
          return item.id > 0;
        });
      },
      tableList_header() {
        if (this.params.type == 1) {
          return this.table_header;
        } else if (this.params.type == 2) {
          return this.table_header1
        } else {
          return this.table_header2
        }
      }
  
    },
    filters: {
      // 判断是否是掉公或转公客户
      publicStatusParse(row, row1) {
        // console.log(row,row1);
        if (row == 1) {
          return "已转公" + row1;
        } else if (row == 2) {
          return "已掉公" + row1;
        }
      },
      // 计算过去时间天数
      getPastDay(val) {
        if (val == 0) {
          return "今天";
        } else {
          return val + "天前";
        }
      },
    },
    created() {
      // 赋值website_id
      if (this.$route.query.website_id) {
        this.website_id = this.$route.query.website_id;
      }
      if (this.$route.query.status) {
        // this.params.c_type1 = this.$route.query.status;
        // if (this.$route.query.status == 0) {
        //   this.params.c_type1 = 1;
        // }
      } else {
        // this.params.c_type1 = 0;
      }
      let screenWidth = document.body.clientWidth
      if (screenWidth < 1355) {
        this.is_small_system = true
        this.myButton1 = true
      } else {
        this.is_small_system = false
        this.myButton1 = false
      }
      this.MembersNEW()
    },
    mounted() {
      let pagenum = localStorage.getItem( 'pagenum')
    	this.params.per_page = Number(pagenum)||10
      // this.screenWidth = document.body.clientWidth
  
      // window.onresize = () => {
      //   console.log(12323);
      //   return (() => {
      //     this.screenWidth = document.body.clientWidth
      //   })()
      // }
      if (this.$route.query.uid) {
        this.member_value = [2,Number(this.$route.query.uid)]
        console.log(this.member_value);
        this.params.admin_type = 2
        this.params.admin_id = this.$route.query.uid
      }
      this.getScreenWidth();
      this.getTypelist();
      this.getTrackingList();
      this.getLevelData();
      this.getSourceData();
      this.getLabelList();
      this.getAdmin();
      this.getCrmCustomerFollowNumber();
      this.getadminUser();
      this.getStatus();
      this.getDepartmentList(); // 获取部门
      this.getMemberList();//获取成员
      this.tfybinding()
  
      let page = document.querySelector('.main-content');
      window.addEventListener('resize', this.handleResize);
  
      const filter = document.getElementById("filter")
  
      this.topHeight = filter.offsetHeight + 120
      page.addEventListener('scroll', this.debounce(this.handleScroll, 20))
  
    },
    beforeDestroy() {
      // 移除事件监听
      window.removeEventListener('resize', this.handleResize);
      window.removeEventListener('scroll', this.handleScroll)
      window.addEventListener("beforeunload", function() {
       document.cookie = "value=; expires=Thu, 01 Jan 1970 00:00:00 GMT";
     });
    },
    // 当该页面进入时触发
    async activated() {
      // if (
      //     this.params.c_type1 != this.$route.query.status &&
      //     this.$route.query.status != undefined
      // ) {
      //     this.params.c_type1 = this.$route.query.status;
      //     this.getDataList();
      // }
      // 判断是否要刷新数据
      // console.log(this.$route.query.status);
      // console.log(this.$store.state.allowUpdat);
      // if (this.$store.state.allowUpdate) {
      //     this.$store.state.allowUpdate = false;
      this.getDataList(); // 刷新页面数据
  
      // }
    },
    methods: {
      
          //腾房云是否绑定
          tfybinding(){
        this.$http.tfyshow().then(res=>{
          console.log(res.data);
          document.cookie = `value=${res.data}`;
        })
      },
      changeSelectParams(e) {
        // console.log(e);
        if (e == 1) {
          this.placeholder = '请输入客户电话'
        } else if (e == 2) {
          this.placeholder = '请输入客户编号'
  
        } else if (e == 3) {
          if (this.recordstype == 'xiansuo') {
            this.placeholder = '请输入客户姓名'
          } else {
            this.placeholder = '请输入线索'
          }
        }else if ( e == 4){
          this.placeholder = '请输入客户姓名'
        }
  
      },
      clearSelectKeyword() {
        this.params.keywords = ''
        this.params.mobile = ''
        this.params.cname = ''
        this.params.number = ''
        this.handleSearch()
      },
      setParams(key) {
        let arr = ['keywords', 'mobile', 'cname', 'number']
        arr.map(item => {
          if (item !== key) {
            this.params[item] = ''
          }
        })
      },
      handleKeywordSearch() {
        if (this.select_params.type == 1) {
          this.setParams('mobile')
          this.placeholder = '请输入手机号码'
          this.params.mobile = this.select_params.keywords.replace(/\s+/g,"");
        } else if (this.select_params.type == 2) {
          this.setParams('number')
          this.placeholder = '请输入客户编号'
          this.params.number = this.select_params.keywords
        } else if (this.select_params.type == 3) {
          if(this.recordstype == 'xiansuo'){
            this.placeholder = '请输入客户姓名'
            this.setParams('keywords')
            this.params.keywords = this.select_params.keywords
          }else{
            this.placeholder = '请输入线索内容'
            this.setParams('keywords')
            this.params.keywords = this.select_params.keywords
          }
        } else if (this.select_params.type == 4) {
          this.setParams('cname')
          this.placeholder = '请输入客户姓名'
          this.params.cname = this.select_params.keywords
        }
        this.handleSearch()
      },
      // handleKeywordSearch() {
      //   if (this.select_params.type == 1) {
      //     this.params.keywords = ''
      //     this.placeholder = '请输入客户电话'
      //     this.params.mobile = this.select_params.keywords.replace(/\s+/g,"");
      //   } else if (this.select_params.type == 2) {
      //     this.placeholder = '请输入客户编号'
      //     this.params.keywords = ''
      //     this.params.mobile = this.select_params.keywords
      //   } else if (this.select_params.type == 3) {
      //     this.placeholder = '请输入线索'
      //     this.params.mobile = ''
      //     this.params.keywords = this.select_params.keywords
      //   }else if (this.select_params.type == 4) {
      //     this.placeholder = '请输入客户姓名'
      //     this.params.mobile = ''
      //     this.params.keywords = ''
      //     this.params.cname = this.select_params.keywords
      //   }
      //   this.handleSearch()
      // },
      debounce(fn, delay, immediate = false) {
        // 1.定义一个定时器, 保存上一次的定时器
        let timer = null
        let isInvoke = false
        // 2.真正执行的函数
        const _debounce = function (...ages) {
          // 取消上一次的定时器
          if (timer) clearTimeout(timer)
  
          // 判断是否需要立即执行
          if (immediate && !isInvoke) {
            fn.apply(this, ages)
            isInvoke = true
          } else {
            // 延迟执行
            timer = setTimeout(() => {
              // 外部传入的真正要执行的函数
              fn.apply(this, ages)
              isInvoke = false
            }, delay)
          }
        }
  
        return _debounce
      },
      handleScroll() {
        const page = document.getElementsByClassName("main-content")[0];
        this.scrollTop = page.scrollTop
  
  
  
        // let scrollTop = page.scrollTop
        // console.log(scrollTop, '滚动距离')
        // if (scrollTop > 100) {
        //   console.log(scrollTop);
        //   // 滚动到导航栏附近则克隆元素
        //   // this.cloneDom()
        // } else {
        //   // 否则删除克隆层 还原原始dom
        //   var origin = document.getElementById('stickyId')
        //   var originNewBox = document.getElementById('testCopyBox')
        //   if (originNewBox.hasChildNodes()) {
        //     origin.appendChild(this.originNode)
        //     originNewBox.removeChild(originNewBox.firstChild)
        //   }
        // }
      },
      getThirdSource() {
        this.$http.getThirdSourceList().then((res) => {
          if (res.status == 200) {
            this.third_source_list = res.data
          }
        })
      },
      changeThirdSource() {
        this.handleSearch()
      },
      Channel_number(){
        // console.log(this.params);
        this.getDataList()
      },
      Channel_Name(){
      //  console.log(this.params);
        this.getDataList()
      },
      // cloneDom() {
      //   var origin = document.getElementsByClassId('stickyId')
      //   this.originNode = origin
      //   var originNewBox = document.getElementById('testCopyBox')
      //   // 若原本有则清空
      //   if (originNewBox.hasChildNodes()) {
      //     originNewBox.removeChild(originNewBox.firstChild)
      //   }
      //   // 否则进行克隆
      //   originNewBox.appendChild(origin)
      // },
      handleResize() {
        // 获取屏幕宽度
        const filter = document.getElementById("filter")
  
        this.topHeight = filter.offsetHeight + 120
        this.getScreenWidth();
      },
      formatTelTime(val) {
        let wholeTime = 0
        let sencond = parseInt(val) || 0
        const hour = sencond / 3600;
        const minute = (sencond % 3600) / 60;
        const sec = ((sencond % 3600) % 60) % 60;
        wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
        return wholeTime
      },
      getScreenWidth() {
        // 获取屏幕宽度
        //  window.innerWidth;
        //  console.log(window.innerWidth);
        if (window.innerWidth <= 1350) {
          this.buttonhidden = false
          this.is_small_system = true
          this.myButton1 = true
        } else {
          this.is_small_system = false
          this.buttonhidden = true
          this.myButton1 = false
        }
      },
      //获取部门成员信息
      getMemberList() {
        this.$http.getDepartmentMemberList().then((res) => {
          if (res.status == 200) {
            this.memberList = res.data;
            this.memberList.push({
              id: 999,
              name: "未分配部门成员",
              order: 100000000,
              pid: 0,
              subs: this.memberList[0].user,
            });
            this.recursionData(this.memberList);
            this.Not_duplicate_datalist = JSON.parse(
              JSON.stringify(this.datalist)
            );
            this.filteredData();
            for (let i = 0; i < this.datalist.length; i++) {
              for (let j = i + 1; j < this.datalist.length; j++) {
                if (this.datalist[i].id == this.datalist[j].id) {
                  this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
                }
              }
            }
          }
        });
      },
      recursionData(data) {
        // console.log(data,"内容");
        // console.log(this.datalist,"全部人员");
        for (let key in data) {
          if (typeof data[key].subs == "object") {
            data[key].subs.map((item) => {
              if (item.user) {
                item.subs = item.user;
                item.subs.map((list) => {
                  list.Parent = item.id;
                });
              }
              if (item.user_name) {
                item.name = item.user_name;
                this.datalist.push(item);
              }
            });
            this.recursionData(data[key].subs);
          }
        }
      },
      //选中部门人员
      selecetedMember(e) {
        // console.log(e.checkedNodes, "成员列表");
        if (e.checkedNodes && e.checkedNodes.length) {
          this.uploadAdmin_id = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.upload_form.admin_id =
            e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
        } else {
          this.upload_form.admin_id = "";
        }
        this.show_member_list = false;
      },
      selecetedMember1(e) {
        // console.log(e.checkedNodes, "成员列表");
        if (e.checkedNodes && e.checkedNodes.length) {
          this.uploadAdmin_id1 = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.upload_form.create_id =
            e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
        } else {
          this.upload_form.create_id = "";
        }
        this.show_member_list = false;
      },
      //关闭弹窗之间的回调
      cancels() {
        this.is_dialog_upload = false;
        this.upload_form = {
          type: 1,
          admin_id: "",
          create_id: "",
          file: "",
        };
      },
      checkStatus(item) {
        if (item.push_type == 2) {
          // 手动认领  
          // 潜在用户 我司成交的不可认领
          if (item.tracking_identify == 1) {
            return true
          }
          // 掉工转公标记的可以领取 
          if (item.public2_status > 0) {
            return false
          }
          // 潜在客户可以领取
          if (this.type == "qianzai") {
            return false
          }
          // 其他情况 不可领取 
          return true
        }
        return false
      },
      // 获取信息展示
      getadminUser() {
        this.$http.getAdmin().then((res) => {
          if (res.status === 200) {
            this.status_id = res.data.id;
            this.selfID = res.data.id;
            if (res.data.roles[0].name === "站长") {
              this.is_show_upload = true;
              this.show_right = true
            } else {
              this.getSiteCrmSetting(res.data.id);
            }
          }
        }).catch(() => {
          this.show_right = true
        });
      },
  
      // filterTime(val) {
      //     var date = new Date(val * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
      //     var Y = date.getFullYear() + '-';
      //     var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
      //     var D = date.getDate() + ' ';
      //     var h = date.getHours() + ':';
      //     var m = date.getMinutes() + ':';
      //     var s = date.getSeconds();
      //     return Y+M+D+h+m+s;
      // },
      // 获取批量导入的crm站点设置
      getSiteCrmSetting(id) {
        this.$http.getAuthShow("batch_import_uid").then((res) => {
          if (res.status === 200) {
            let num = [];
            num.push(res.data);
            // console.log(num,"num",id)
            if (num.indexOf(id) != -1 || !res.data) {
              this.is_show_upload = true;
  
            }
          }
          this.show_right = true
        }).catch(() => {
          this.show_right = true
        });
      },
      // 快速编辑标签
      async fastEditLabel(row) {
        this.multipleSelection = []; // 清空
        this.multipleSelection.push(row.id); // 赋值客户id
        this.is_fast = true; // 设置为快速编辑标签
        // 去除已选中的标签
        this.labels_list.map((item) => {
          item.label.map((list) => {
            list.check = false;
          });
        });
        //console.log(row,"row");
        this.fastEdit_params.client_id = row.id;
        new Promise((resolve) => {
          // 获取客户标签列表
          if (!this.labels_list.length) {
            this.getLabelGroupNoPageNew();
          }
          if (!this.labels_list.length) {
            setTimeout(() => {
              resolve();
            }, 500);
          } else {
            resolve();
          }
        }).then(() => {
          this.show_Customer_label = true; // 显示模态框
          // console.log(this.labels_list,"this.labels_list");
          row.label.map((item) => {
            this.labels_list.map((list) => {
              list.label.map((arr) => {
                if (arr.name == item) {
                  arr.check = true;
                }
              });
            });
          });
          this.$forceUpdate();
        });
      },
      resetLoudongSearch() {
        this.params.mobile = "";
      },
      handleSearch() {
        this.params.page = 1;
        this.getDataList();
      },
      getCrmCustomerFollowNumber() {
        this.$http.getCrmCustomerFollowNumber().then((res) => {
          if (res.status === 200) {
            this.no_follow_number = res.data;
          }
        });
      },
      // changeTab(e, type) {
      //     if (type == 1) {
      //         this.params.status = e.id;
      //     } else {
      //         this.params.type = e.id;
      //     }
      //     this.params.page = 1;
      //     this.getDataList();
      // },
      getTypelist() {
        this.$http.getCrmCustomerTypeDataNopage().then((res) => {
          if (res.status === 200) {
            this.type_list = [{ id: 0, title: "全部" }, ...res.data];
            // this.push_form.type = res.data.filter((item) => {
            //     return item.is_default;
            // })[0].id;
            // let cus_type = parseInt(this.$route.query.cus_type);
            // res.data.map((item) => {
            //     if (cus_type == 1 && item.title == "求购") {
            //         // this.params.type = item.id;
            //     }
            //     if (cus_type == 2 && item.title == "求租") {
            //         // this.params.type = item.id;
            //     }
            // });
          }
          // this.ponent_maintain_type = JSON.parse(JSON.stringify(res.data));
          // let i = this.customer_list_type.findIndex(
          //     (item) => item.id == this.params.c_type1
          // );
          // if (i != -1) {
          //     this.getDataLists(this.customer_list_type[i]);
          // }
        });
      },
      getLabelList() {
        this.$http.getLabelGroupNoPage().then((res) => {
          if (res.status === 200) {
            // console.log(res.data);
            this.ponent_maintain_label = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户类型列表
            this.label_list = res.data;
            // console.log(this.label_list, "查看");
            this.label_default_list = res.data;
          }
        });
      },
      getDataLists(item, index) {
        this.tabIndex = index;
        // console.log(item);
        // console.log(this.params);
        // this.params.c_type1 = item.id;
  
        // if(this.params.c_type1 ==1){
        //   console.log(1111);
        //   // this.params.c_type5 = item.id;
        // }
        // this.params.type = 0;
        this.params.page = 1;
        this.getDataList();
      },
      //获取客户列表
      getDataList() {
        this.is_table_loading = true;
        let params = Object.assign({}, this.params);
        if(this.recordstype == 'liuzhuan'){
          this.getTransfertasklist();
          return;
        }
        if (params.type == 'xiansuo') {
          this.getXiansuo(params)
        } else {
          this.getList(params)
        }
  
      },
      getXiansuo(o_params) {
        let { end_date, start_date, mobile, keywords, per_page, page,refer_id,refer_name} = o_params
        let params = {
          end_date,
          start_date,
          mobile,
          keywords,
          per_page,
          page,
          refer_id,
          refer_name,
        }
        params.platform = this.source || 0
        if (params.start_date == "") {
          delete params.label
        }
        if (params.end_date == "") {
          delete params.end_date
        }
        // console.log(params);
        this.$http.getJingyingViewList(params).then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
          }
        }).catch(() => {
          this.is_table_loading = false;
        })
      },
      getList(params) {
        // date_type: 0,//时间类型(1:创建时间,2:跟进时间,3:线索时间,4:更新时间,5:掉公时间,6:转公时间,7:跟客天数,8:带看时间)
        if (params.date_type == '') {
          delete params.date_type
        }
        // follow_type: 0,//跟进类型(1:已跟进,2:已认领,3:未跟进,4:待分配,5:已转公,6:已掉公,7:语音跟进)
        if (params.follow_type == '') {
          delete params.follow_type
        }
        // client_type: 0,//客户类型(1:公海客户,2:潜在客户,3:成员私客)
        if (params.client_type == 0 || params.client_type == "") {
          delete params.client_type
        }
        // yaoyue_type: 0,//邀约类型(1:查看电话,2:外呼(已接通),3:外呼(未接通),4:已带看,5:未带看,6:有复看)
        if (params.yaoyue_type == '') {
          delete params.yaoyue_type
        }
        // tracking_type: 0,//	状态类型(1:有效客户,2:无效客户,3:暂缓客户,4:我司成交,:5他司成交,6:未成交)
        if (params.tracking_type == '') {
          delete params.tracking_type
        }
        // level_id: 0,//客户等级
        if (params.level_id == '') {
          delete params.level_id
        }
        // admin_type: 0,//管理员类型(1:录入人,2:维护人,3:带看人,4成交人）
        if (params.admin_type == '') {
          delete params.admin_type
        }
        // admin_id: 0,//管理员id
        if (params.admin_id == '') {
          delete params.admin_id
        }
        // department_id: 0,//部门id
        if (params.department_id == '') {
          delete params.department_id
        }
        // source_id: 0, // 客户来源
        if (params.source_id == '') {
          delete params.source_id
        }
        // type_id: 0,// 客户类型
        if (params.type_id == '') {
          delete params.type_id
        }
        if (params.call_status == '') {
          delete params.call_status
        }
        // label: 0,//标签
        if (params.label == '') {
          delete params.label
        }
        if (params.start_date == "") {
          delete params.label
        }
        if (params.end_date == "") {
          delete params.end_date
        }
        // console.log(params);
        this.$http.informationfollowlist({ params }).then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            // console.log(res.data.data);
            this.tableData = res.data.data;
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
          }
        }).catch(() => {
          this.is_table_loading = false;
        })
      },
      //客户级别列表
      getLevelData() {
        this.$http.getCrmCustomerLevelNopage().then((res) => {
          if (res.status === 200) {
            this.ponent_maintain_level = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户级别列表
            this.level_list = [...res.data, { title: "空", id: 4 }];
  
          }
        });
      },
      //获取客户状态
      getTrackingList() {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.tracking_params })
          .then((res) => {
            if (res.status === 200) {
              this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
              // console.log(this.tracking_list);
            }
          });
      },
      resetXiansuoSearch() {
        this.params.keywords = "";
      },
      onClickSearch() {
        this.params.page = 1;
        this.getDataList();
      },
      //根据时间搜索
      // onClickType(e, type) {
      //     switch (type) {
      //         case 1:
      //             this.params.source_id = e.id;
      //             break;
      //         case 2:
      //             this.params.tracking_id = e.id;
      //             break;
      //         case 3:
      //             this.params.is_bind = e.id;
      //             break;
      //         case 4:
      //             this.params.date_type = e.id;
      //             delete this.params.start_date; // 清空开始时间
      //             delete this.params.end_date; // 清空结束时间
      //             this.timeValue = ""; // 清空自定义时间绑定值
      //             break;
      //         case 5:
      //             this.params.level_id = e.id;
      //             break;
      //         case 6:
      //             this.params.type = e.id;
      //             break;
      //         default:
      //             break;
      //     }
      //     this.params.page = 1;
      //     this.getDataList();
      // },
      onChangeTime(e) {
        if(e){
          if (e[1].endsWith("00:00:00")) {
            e[1] = e[1].slice(0, -8) + "23:59:59";
          }
            this.params.start_date = e ? e[0] : ""; // 赋值开始时间
            this.params.end_date = e ? e[1] : ""; // 赋值结束时间
        }else{
          this.params.start_date = ""; // 赋值开始时间
          this.params.end_date = ""; // 赋值结束时间
        }
        this.params.page = 1; // 显示第一页
        console.log(this.params);
        this.getDataList(); // 获取最新数据
      },
      getSourceData() {
        this.$http.listcustomersourcenew().then((res) => {
          if (res.status === 200) {
            let arr = JSON.parse(JSON.stringify(res.data));
            arr.map((item)=>{
              if(item.children==0){
                delete item.children
              }
            })
            this.ponent_maintain_source = JSON.parse(JSON.stringify(arr)) // 深拷贝客户来源列表
            this.source_list = [{ title: "全部", id: 0 }, ...res.data];
            this.source_list.map((item)=>{
              if(item.children==0){
                delete item.children
              }
            })
            this.push_form.source_id = res.data.filter((item) => {
              return item.is_default == 1;
            })[0].id;
          }
        });
      },
      removeDomain(item) {
        var index = this.other_mobile.indexOf(item);
        if (index !== -1) {
          this.other_mobile.splice(index, 1);
        }
      },
      addDomain() {
        this.other_mobile.push({
          mobile: "",
        });
      },
      onClickLevel(item) {
        this.push_form.level_id = item.id;
      },
      onClickTypeClient(item) {
        this.push_form.type = item.id;
      },
      // onChangeKeywords() {
      //     this.params.page = 1;
      //     this.getDataList();
      // },
      selectionChange(e) {
        this.multipleSelectionname = e; // 赋值当前客户信息
        let arr = e.map((item) => {
          return item.id;
        });
        this.multipleSelection = arr; // 赋值当前客户的id
        // 只有在客户标签列表为空时请求数据
        if (!this.labels_list.length) {
          this.getLabelGroupNoPageNew();
        }
      },
      onClickDetailXiansuo(row) {
        this.$http.getClientIdByPhone({ mobile: row.mobile }).then(res => {
          if (res.status == 200) {
            this.onClickDetail({ id: res.data })
          }
        })
      },
      async onClickDetail(row) {
        // let res = await this.$http.getForceFollow()
        // if (res.status == 200 && res.data && res.data.id > 0) {
        //   this.$confirm("您有未跟进的客户确认去跟进吗？").then(() => {
        //     let url = `/crm_customer_detail?id=${res.data.client_id}&type=seas&tel_follow_id=${res.data.id}`;
        //     this.$goPath(url);
        //   })
        //   return
        // }
        // let url = `/crm_customer_detail?id=${row.id}&type=seas`;
        // this.$goPath(url);
        let url = `/crm_information_detail?id=${row.id}&type=my&information=1`;
        this.$goPath(url);
        this.$store.commit('setinformationdata', this.params);
      },
      onPageChange(current_page) {
        this.params.page = current_page;
        this.getDataList();
      },
      //每页几条
      handleSizeChange(e){
        this.params.per_page = e
        this.getDataList();
      },
      onPageChangeQwFollow(e) {
        this.is_follow_params.page = e;
        this.getFollow();
      },
      isShowFollow(row) {
        this.is_follow_dialog = true;
        this.is_follow_params.client_id = row.client_id;
        this.getFollow();
      },
      formatGenkeDay(row, type) {
        let value = '--'
        if (type == 'genke') {
          if (row.last_follow_time && row.get_time) {
            let new_d = +new Date(row.last_follow_time)
            let old_d = +new Date(row.get_time)
            value = parseInt((new_d - old_d) / 1000 / 60 / 60 / 24)
          }
        }
        if (type == 'chengjiao') {
          if (row.created_at && row.deal_time) {
            let new_d = +new Date(row.deal_time)
            let old_d = +new Date(row.created_at)
            // console.log(new_d);
            value = parseInt((new_d - old_d) / 1000 / 60 / 60 / 24)
          }
        }
        return value
      },
      // crm客户跟进
      getFollow() {
        this.is_follow_loading = true;
        this.$http
          .getCrmCustomerFollowData({ params: this.is_follow_params })
          .then((res) => {
            this.is_follow_loading = false;
            if (res.status === 200) {
              this.is_follow_data = res.data.data;
              this.is_follow_params.total = res.data.total;
            }
          });
      },
      // onClickForm(e) {
      //   this.$http.setCrmCustomerData(e).then((res) => {
      //     if (res.status === 200) {
      //       this.$message.success("操作成功");
      //       this.is_push_customer = false;
      //       this.form = {};
      //       this.form1 = {};
      //       this.params.page = 1;
      //       this.getDataList();
      //     }
      //   });
      // },
      onClickForm() {
        if (
          this.push_form.label &&
          this.push_form.label != undefined &&
          typeof this.push_form.label !== "string"
        ) {
          this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
        }
        if (this.other_mobile.length > 0) {
          let arr = this.other_mobile.map((item) => {
            return item.mobile;
          });
          let othertel = arr.filter((item, index) => {
            if (index) {
              return item;
            }
          });
          this.push_form.mobile = arr[0];
          this.push_form.subsidiary_mobile = othertel.join(",");
        }
        if (!this.push_form.mobile) {
          this.$message.error("请检查联系方式");
          return;
        }
        if (!this.push_form.cname) {
          this.$message.error("请检查客户姓名");
          return;
        }
        if (!this.push_form.sex) {
          this.$message.error("请检查客户性别");
          return;
        }
        if (!this.push_form.level_id) {
          this.$message.error("请检查客户等级");
          return;
        }
        if (!this.push_form.type) {
          this.$message.error("请检查客户类型");
          return;
        }
        if (!this.push_form.source_id) {
          this.$message.error("请检查客户来源");
          return;
        }
        this.is_button_loading = true;
        this.$http.setCrmCustomerDataV2(this.push_form).then((res) => {
          this.is_button_loading = false;
          this.is_push_customer = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
          } else if (res.status === 422) {
            const cus_id =
              res.data.data &&
                res.data.data.id != "" &&
                res.data.data.id != undefined
                ? res.data.data.id
                : 0; // 赋值客户id
            // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
            if (
              res.data.data &&
              res.data.data.follow_id &&
              res.data.data.follow_id != undefined &&
              res.data.data.follow_id != 0
            ) {
              this.$confirm("客户号码已有维护人，无法重复录入。", "提示", {
                confirmButtonText: "立即查看",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                  this.$goPath(url); // 跳转客户详情
                })
                .catch(() => {
                  return;
                });
            } else {
              // 该客户没有维护跟进人时触发
              this.$confirm("客户号码已存在，认领后可跟进。", "提示", {
                confirmButtonText: "立即认领",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  this.$http
                    .getCrmCustomerPublick({ ids: cus_id + "" })
                    .then((res) => {
                      if (res.status === 200) {
                        this.$message.success("认领成功");
                        let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                        this.$goPath(url); // 跳转客户详情
                      }
                    });
                })
                .catch(() => {
                  return;
                });
            }
          }
        });
      },
      // 折叠面板
      onChangeCollapse() {
        // this.is_collapse = !this.is_collapse;
      },
      onClickGet(row) {
        this.$confirm("是否认领该客户", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$http.getCrmCustomerPublick({ ids: row.id + "" }).then((res) => {
              if (res.status === 200) {
                this.$message.success("认领成功");
                let url = `/crm_customer_detail?id=${row.id}&type=my`;
                this.$goPath(url);
              }
            });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消");
          });
      },
      // handleChangeLabel() {
      //   this.params.page = 1;
      //   this.getDataList();
      // },
      douview(){
        this.$goPath(`/douyin_circulate_for_perusal`);
      },
      outboundview(){
        this.$goPath(`/outbound`)
      },
      //
      datastatistics(){
        this.$goPath("/wanderabout")
      },
      leftla() {
        this.buttonhidden = true
        this.myButton = true
        this.myButton1 = false
      },
      Rightdrag() {
        this.myButton1 = true
        this.buttonhidden = false
        this.myButton = false
      },
      getFile() {
        this.is_dialog_upload = true;
        this.getMemberList();
        // this.$refs.file.click();
      },
      handleSuccessAvatarTemporary(response) {
  
        this.handleFileUpload(response.url)
      },
      // 获取文件
      handleFileUpload(response) {
        // 阻止发生默认行为
        event.preventDefault();
        let url = response;
        let formData = new FormData();
        formData.append("url", url);
        formData.append("admin_id", this.upload_form.admin_id || 0);
        formData.append("type", this.upload_form.type);
        formData.append("type_id", this.upload_form.type_id);
        formData.append("source_id", this.upload_form.source_id);
        formData.append("create_id", this.upload_form.create_id || 0);
        if (Array.isArray(this.upload_form.label)) {
          formData.append("label", this.upload_form.label.join(","));
        } else {
          formData.append("label", this.upload_form.label);
        }
        formData.append("remark", this.upload_form.remark);
        // this.formData.get("file");
        this.onUpload(formData);
      },
  
  
      // 上传文件
      onUpload(formData) {
        this.loading = this.$loading({
          lock: true,
          text: "上传中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        // this.$message.success("正在上传...");
        //  修改为传url
        this.$http.informationexport(formData).then((res) => {
          // this.$http.uploadCrmCustomerData(formData).then((res) => {
          if (res.status === 200) {
            // console.log(res.data);
            // 如果只有新增
            let text =
              "导入成功，新增客户数量" +
              res.data.success_num +
              "条,系统重复客户数量" +
              res.data.crm_repeat_num +
              "条,表格重复客户数量" +
              res.data.excel_repeat_num +
              "条,失败客户数量" +
              res.data.error_num +
              "条";
            this.loading.close();
            this.$confirm(text, "提示", {
              confirmButtonText: "查看详情",
              cancelButtonText: "取消",
              type: "success",
            })
              .then(() => {
                // console.log(1111111)
                // console.log(res.data.task_id)
                this.params.page = 1;
                this.getDataList();
                this.is_dialog_upload = false;
                this.is_loading = false;
                let url = `/crm_customer_task_list?task_id=` + res.data.task_id;
                this.$goPath(url); // 跳转客户详情
              })
              .catch(() => {
                this.params.page = 1;
                this.getDataList();
                this.is_dialog_upload = false;
                this.is_loading = false;
              });
          } else {
            this.loading.close();
          }
        });
      },
      onUploadTem() {
        window.open(
          "https://img.tfcs.cn/backup/static/admin/xls/client_template_new.xls?f=" +
          +new Date()
        );
      },
      //表单重置
      reset() {
        this.push_form = {
          cname: "",
          source_id: "",
          level_id: 1,
          type: "",
          sex: 1,
          subsidiary_mobile: "",
          intention_community: "",
          label: "",
          // intention_street: "",
          remark: "",
        };
        this.other_mobile = [{ mobile: "" }];
      },
      //取消
      cancel() {
        this.reset();
        this.is_push_customer = false;
      },
  
      getSelectWidth(item, LabelList) {
        var text_temp = "",
          text_width = 0,
          text_el = null;
        var current_option = "";
        if (LabelList) {
          current_option =
            item.label && item.label.find((arr) => arr.id === LabelList[0]);
        } else {
          current_option = false;
        }
        if (current_option) {
          text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
          document.body.insertAdjacentHTML("afterbegin", text_temp);
        } else {
          text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${item.name}</span>`;
          document.body.insertAdjacentHTML("afterbegin", text_temp);
        }
        text_el = document.getElementById("text");
        text_width = text_el.offsetWidth;
        text_el.remove();
        return text_width + 57 + "px";
      },
      //回访记录
      Follow_up_records(type, other) {
        console.log(type, other);
        if(type == "liuzhuan"&&other=="-1"){
          this.recordstype = type
          this.params.page = 1
          this.getTransfertasklist()
        }else{
          this.params.date_type = 0
        if(type==1){
          this.params.date_type = 2
        }
        if (other >= 0) {
          this.douyinview = false
          this.outview = false
          this.recordstype = other
        } else {
          this.douyinview = false
          this.outview = false
          this.recordstype = type
        }
        // if (type == "xiansuo" && this.third_source_list.length == 0) {
        //   this.getThirdSource()
        // }
        this.params.type = type
        this.params.page = 1
        this.getDataList(); // 获取最新数据
        }
      },
      //带看记录
      Take_look_record() {
        this.recordstype = 2
        this.params.type = 2
        this.params.page = 1
        this.params.date_type = 8
        this.douyinview = false
        this.outview = false
        this.getDataList(); // 获取最新数据
      },
      //外呼记录
      Outbound_record() {
        this.params.date_type = 0
        this.recordstype = 3
        this.params.type = 3
        this.params.page = 1
        this.douyinview = false
        this.outview = true
        this.getDataList(); // 获取最新数据
      },
      // 获取流转任务列表
      getTransfertasklist(){
        const { page, per_page } = this.params
        this.is_table_loading = true;
        this.$http.getTransfertasklist({ page, per_page }).then(res=>{
          this.is_table_loading = false;
          if(res.status == 200){
            console.log(res.data.data,"流转任务的列表");
            this.tableData = res.data.data
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
          }
        }).catch(e => {
          this.is_table_loading = false;
        })
      },
      //时间类型
      time_type(e) {
        // console.log(e)
        this.params.date_type = e
        this.getDataList(); // 获取最新数据
      },
      //跟进类型
      follow_through(e) {
        // console.log(e);
        this.params.follow_type = e
        this.getDataList(); // 获取最新数据
      },
      //全部客户
      all_Customers_through(e){
        this.params.client_type = e
        this.getDataList(); // 获取最新数据
      },
      //邀约类型
      invite_type(e) {
        this.params.yaoyue_type = e
        this.getDataList(); // 获取最新数据
      },
      //状态类型
      customer_status(e) {
        this.params.tracking_type = e
        this.getDataList(); // 获取最新数据
      },
      //客户等级
      customer_grade(e) {
        if (e == 4) {
          this.params.level_type = e
          delete this.params.level_id
        } else {
          delete this.params.level_type
          this.params.level_id = e
        }
  
        this.getDataList(); // 获取最新数据
      },
      //部门，成员
      loadFirstLevelChildren(value) {
        console.log(this.member_value,"=================");
        if(value){
        this.params.admin_type = value[0]
      if(value[1]){
        this.params.admin_id = value[1]
      }else{
        this.params.admin_id = 0
      }
      }else{
        this.params.admin_type = 0
        this.params.admin_id = 0
      }
      this.params.department_id = 0
      this.getDataList(); // 获取最新数据
      },
      loadFirstLevelChildrenA(){
        // this.MembersNEW()
      },
      // 获取成员的接口（新）
      MembersNEW(){
        this.$http.getDepartmentMemberListNew().then((res)=>{
          if(res.status==200){
            this.member_listNEW.map(item => {
            item.subs = res.data
           })
          }
        })
      },
      //客户来源
      customer_source(e) {
        this.params.source_id = e[e.length - 1]
        this.getDataList(); // 获取最新数据
      },
      //客户类型
      client_type(e) {
        this.params.type_id = e
        this.getDataList(); // 获取最新数据
      },
      //通话状态
      customerstatus_type(e){
        this.params.call_status = e
        this.getDataList(); // 获取最新数据
      },
      //BI数据面板
      tomianban(){
        this.$goPath("Big_Data_Panel")
      },
      //清空
      empty() {
        this.value = ''
        this.params.date_type = 0
        this.timeValue = ''
        this.params.follow_type = ''
        this.params.client_type = ''
        this.follow_up_value = ''
        this.params.yaoyue_type = ''
        this.Invitation_value = ''
        this.params.tracking_type = ''
        this.customer_statusvalue = ''
        this.grade_value = ''
        this.member_value = ''
        this.source_value = ''
        this.typeLabel_value = ''
        this.customerstatus_value = ''
        this.params.level_id = ''
        this.params.admin_type = ''
        this.params.admin_id = ''
        this.params.department_id = ''
        this.params.source_id = ''
        this.params.type_id = ''
        this.params.label = ''
        this.params.start_date = ""
        this.params.end_date = ""
        this.select_params.type = 1
        this.select_params.keywords = ''
        this.params.mobile = ""
        this.params.keywords = ''
        this.source = ''
        this.params.refer_id =''
        this.params.refer_name = '' 
        this.getDataList(); // 获取最新数据
      },
      Refresh() {
        //   window.location.reload()
        // this.params.page = 1
        this.getDataList(); // 获取最新数据
      },
  
      //客户标签// 检索客户标签发生改变时触发
      changeCustomerLabel(val) {
        // console.log(val);
        this.is_all = false;
        // 父级标签id赋值
        let label_num = Object.keys(this.customerLabelList).length;
        // 如果选择的标签大于1就删除之前选择的标签
        if (label_num > 1) {
          delete this.customerLabelList[this.changeParentLabel];
        }
        this.changeParentLabel = val[2].id;
        // 更新数据
        this.params.label = val[0];
        this.params.page = 1;
        // console.log(this.params);
        this.getDataList();
      },
  
  
      getAllLabelList() {
        this.is_all = true;
        this.changeParentLabel = "";
        this.customerLabelList = {};
        delete this.params.label;
        this.params.page = 1;
        this.getDataList();
      },
      // 开始导入
      startImport() {
        this.is_loading = true;
        if (
          this.upload_form.type_id == "" ||
          this.upload_form.type_id == undefined
        ) {
          this.upload_form.type_id = 0;
        }
        if (
          this.upload_form.source_id == "" ||
          this.upload_form.source_id == undefined
        ) {
          this.upload_form.source_id = 0;
        }
        // 处理为正常部门成员id
        if (this.upload_form.admin_id.toString().length >= 6) {
          this.upload_form.admin_id = parseInt(
            this.upload_form.admin_id.toString().slice(0, 3)
          );
        }
        // console.log(this.upload_form);
        // this.$refs.file.click();
        //唤起上传文件组件
        // this.$refs.uploadBox.$children[0].$refs.input.click();
        this.$refs["upload"].$refs["upload-inner"].handleClick()
        this.is_loading = false;
      },
      // 清除当前选择成员
      delName() {
        this.uploadAdmin_id = "";
        this.upload_form.admin_id = 0;
      },
      // 点击设置客户标签按钮
      setCustomerLabel() {
        // 判断是否选中客户
        if (!this.multipleSelection.length) {
          return this.$message({
            message: "请选择客户",
            type: "warning",
          });
        }
        this.show_Customer_label = true; // 显示模态框
        // 去除已选中的标签
        this.labels_list.map((item) => {
          item.label.map((list) => {
            list.check = false;
          });
        });
      },
      // 选中标签
      checkChangeLabels(index0, index) {
        let that = this;
        that.labels_list[index0].label[index].check =
          !that.labels_list[index0].label[index].check;
        this.$forceUpdate();
      },
      // 确定更改客户标签
      confirmSelected() {
        this.is_loading = true;
        let num = []; // 已选客户标签容器
        // 遍历出勾选中的客户标签
        this.labels_list.map((item) => {
          item.label.map((list) => {
            if (list.check) {
              num.push(list.id);
            }
          });
        });
        // 赋值已选中的客户标签参数
        if (num && num.length) {
          this.confirm_batch_list.label = num.join(",");
        }
        // 赋值已选中的客户id参数
        if (this.multipleSelection && this.multipleSelection.length) {
          this.confirm_batch_list.ids = this.multipleSelection.join(",");
        }
        // 请求接口
        this.$http
          .batchgetinformationlable(this.confirm_batch_list)
          .then((res) => {
            if (res.status == 200) {
              this.$message({
                message: "操作成功",
                type: "success",
              });
              this.show_Customer_label = false; // 关闭模态框
              this.is_loading = false;
              this.getDataList(); // 获取最新数据
            } else {
              this.is_loading = false;
            }
          })
          .catch(() => {
            this.is_loading = false;
          });
      },
      // 导入成员当获取焦点时触发
      focusSelete() {
        this.$refs.focusMember.blur(); // 失去焦点
        this.show_member_list = true;
      },
      focusSelete1() {
        this.$refs.focusMember.blur(); // 失去焦点
        this.show_member_list1 = true;
      },
      // 获取客户标签列表
      getLabelGroupNoPageNew() {
        // 获取客户标签列表
        this.$http.getLabelGroupNoPageNew().then((res) => {
          if (res.status == 200) {
            if (res.data.qiwei_tag && res.data.qiwei_tag.length) {
              res.data.qiwei_tag.map((item) => {
                item.label = item.taggroup;
                delete item.taggroup;
              });
            }
            this.labels_list = res.data.qiwei_tag.concat(res.data.system_tag);
          }
          // 格式化数据-添加check属性
          if (this.labels_list && this.labels_list.length) {
            this.labels_list.map((item) => {
              if (item.label) {
                item.label.map((list) => {
                  list.check = false;
                });
              }
            });
          }
        });
      },
      // 查询归属地
      HomeAddress(row) {
        this.$http.lookphoneLocation(row.id).then((res) => {
          if (res.status == 200) {
            this.getDataList(); // 获取最新数据
          }
        });
      },
    // 点击全部部门
    Reqdepartment(){
      this.getDepartmentList(); // 获取部门
    },
      // 部门选取发生改变
      changePopDepar(val) {
        this.member_value = "";
      if (val != null) {
        this.filtrMember = [];
        this.filtrDepartMember(this.memberList, val);
        this.filtrMember = this.filtrMember.filter((item) => {
          return item.id.toString().length <= 4;
        });
        this.searchDepart() 
      } else {
        this.params.department_id = 0
        this.getDataList();
        this.filteredData();
      }
      },
      // 选中对应部门，遍历出部门成员
      filtrDepartMember(data, val) {
        for (let key in data) {
          // console.log(data[key].id,val,"执行",data[key].id == val)
          if (data[key].id == val) {
            if (data[key].subs) {
              data[key].subs.map((item) => {
                if (item.user) {
                  item.user.map((list) => {
                    this.filtrMember.push(list);
                  });
                } else if (item.user_name) {
                  this.filtrMember.push(item);
                }
              });
            }
          }
          this.filtrDepartMember(data[key].subs, val);
        }
      },
      // 部门成员去重
      filteredData() {
        const uniqueIds = {};
        const filtered = [];
        for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
          const item = this.Not_duplicate_datalist[i];
          if (!uniqueIds[item.id]) {
            uniqueIds[item.id] = true;
            filtered.push(item);
          }
        }
        // console.log(filtered);
        this.filtrMember = filtered;
        this.member_list.map(item => {
          item.subs = filtered
        })
      },
      // 获取全部的部门
      getCrmDepartmentList() {
        // this.params.admin_id = 0; // 清空搜索内容
        // this.params.department_id = 0; // 清空搜索内容
        // this.getDepartmentList(); // 获取部门
        // // 获取部门成员
        // if (!this.datalist.length) {
        //   this.getMemberList();
        // }
        // if (!this.AllDepartment.length) {
        //   this.$http.getCrmDepartmentList().then((res) => {
        //     if (res.status == 200) {
        //       this.AllDepartment = res.data;
        //     }
        //   })
        // }
      },
      //操作
      operateonClick(item){
        // 判断是否选中客户
        if (!this.multipleSelection.length) {
            return this.$message({
              message: "请选择客户",
              type: "warning",
            });
          }
        console.log(this.multipleSelection.length);
        if(this.multipleSelection.length&&this.multipleSelection.length>10){
            return this.$message({
              message: "每次最多选择10个客户",
              type: "warning",
            });
        }
        if(item.id==2){
          //删除操作
          let ids = this.multipleSelection.join(","); // 数组转换字符串
          this.delBatchDelete(ids)
        }else if(item.id==3){
          let ids = this.multipleSelection.join(",");
          //转交到同事
          this.dialogss.transToMember = true;
          this.$nextTick(()=>{
            this.$refs.transToMember.open({
              ids: ids
            }).onSuccess(()=>{
              this.getDataList();
            });
          })
        }else if(item.id==4){
          this.is_transfer_customer = true;
        }
      },
       //批量删除流转客
       delBatchDelete(ids){
        let params = {
          ids:''
        }
        this.$confirm("该操作不可恢复，是否删除", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 点击确认调用公众号授权
            params.ids = ids
            this.$http.informationbatch_del(params).then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.getDataList();
              }
            });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消操作");
          });
      },
      // 获取部门
      getDepartmentList() {
        if (!this.AllDepartment.length) {
          this.$http.getCrmDepartmentList().then((res) => {
            if (res.status == 200) {
              this.AllDepartment = res.data;
            }
          });
        }
      },
      // 按照部门搜索
      searchDepart() {
        if (this.selectedMember == "" || this.selectedMember == undefined) {
          this.params.admin_id = 0; // 赋值0
        }
        this.getDataList();
      },
      // 按照成员搜索
      searchMember() {
        // 处理id为前三位
        if (this.params.admin_id.toString().length >= 6) {
          this.params.admin_id = parseInt(
            this.params.admin_id.toString().slice(0, 3)
          );
        }
        this.getDataList();
      },
      // 搜索部门成员发生改变时触发
      changeSearchMember() {
        // 如果搜索的参数为空或undefined
        if (this.selectedMember == "" || this.selectedMember == undefined) {
          this.params.admin_id = 0; // 赋值0
        } else {
          this.params.admin_id = this.selectedMember; // 有值则赋值
        }
      },
      //导出客户
      leading_out() {
        if( !this.params.start_date&&! this.params.end_date){
          this.$message({
            type:"warning",
            message:"请选择时间范围！"
          })
          return
        }
        // if (this.params.start_date && this.params.end_date) {
        //   const startDate = new Date(this.params.start_date.replace(/-/g, "/")); // 获取开始时间，并将其转换为Date对象
        //   const endDate = new Date(this.params.end_date.replace(/-/g, "/")); // 获取结束时间，并将其转换为Date对象、
          
          // // 获取开始时间和结束时间的年份和月份
          // const startYear = startDate.getFullYear();
          // const startMonth = startDate.getMonth();
          // const endYear = endDate.getFullYear();
          // const endMonth = endDate.getMonth();
  
          // 如果开始时间和结束时间跨年或跨月，则返回null
          // if (startYear !== endYear || startMonth !== endMonth) {
          //   this.$message({
          //     type:"warning",
          //     message:" 范围不可跨月！ "
          //   })
          //   return
          // }
  
          // 计算两个日期对象之间的毫秒数差值
        //   const timeDiff = endDate.getTime() - startDate.getTime();
  
        //   // 将毫秒数差值转换为天数
        //   const days = Math.ceil(timeDiff / (1000 * 3600 * 24));
        //   console.log(days);
        //   if(days>189){
        //     this.$message({
        //       type:"warning",
        //       message:" 最多可选范围 189天 "
        //     })
        //     return
        //   }
        // }
        this.$confirm("确定要导出吗？", '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          Loading.service({
            text: "正在处理表格数据 请稍候。。。",
          });
          // console.log(this.params);
          if(this.params.keywords == ""){
            delete this.params.keywords
          }
          if(this.params.mobile == ""){
            delete this.params.mobile
          }
          if(this.params.refer_id==""){
            delete this.params.refer_id
          }
          if(this.params.refer_name ==""){
            delete this.params.refer_name 
          }
          this.$http.informationexport(this.params).then((res) => {
            Loading.service().close()
            if (res.status == 200) {
              this.$confirm('导出正在进行, 是否前往操作记录查看?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                   this.$goPath("/crm_customer_export_list?information=1")
                }).catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消查看'
                  });          
                });
              // window.location.href = res.data.url;
            }
          }).catch(() => {
            Loading.service().close()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消导出'
          });
        });
      },
      // 转交客户
      TransferCustomer() {
        // 判断是否选中客户
        if (!this.multipleSelection.length) {
          return this.$message({
            message: "请选择客户",
            type: "warning",
          });
        }
        this.is_transfer_customer = true;
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      },
      // 搜索转交人
      onAdminSearch() {
        this.admin_params.page = 1;
        this.getAdmin();
      },
      getAdmin() {
        this.$http
          .getUserList(
            this.admin_params
          )
          .then((res) => {
            if (res.status === 200) {
              this.admin_list = res.data.data;
              this.admin_params.total = res.data.total;
            }
          });
      },
      //选中接收人员
      copyselecchange(e){
        this.copydata = e
      },
      //复制客户
      oncopy(){
        if(!this.copydata.length){
         return this.$message.warning("请选择接收人员！")
        }
        let copyid = []
        this.copydata.map(item=>{
         copyid.push(item.id)
        })
        this.determinecopy(copyid)
      },
      //确认复制
      determinecopy(copyid){
        let params = {
          ids:this.multipleSelection.join(","),
          user_ids:copyid.join(",")
        }
        console.log(params);
        this.$http.Transferredcustomerscopy(params).then(res=>{
          if(res.status==200){
            this.$message.success("复制成功！")
            this.is_transfer_customer = false
            this.copydata = []
            this.$refs.capytable.clearSelection();
            if(this.drawer){
              this.$refs.childRef.getDetail();
            }
              this.getDataList(); // 获取最新数据
          }
        })
      },
      // 确定转让客户
      onZhuanrang(e) {
        this.$confirm("是否转交客户", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 将数组转换字符串
            this.c_id = this.multipleSelection.join(",");
            // 点击确认调用公众号授权
            this.$http
              .setCrmCustomerZhuanrang({
                be_transfer_id: e.id,
                ids: this.c_id,
              })
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success("操作成功");
                  this.is_transfer_customer = false;
                  this.getDataList(); // 获取最新数据
                }
              });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消");
          });
      },
      PersPageChange(e) {
        this.admin_params.page = e;
        this.getAdmin();
      },
      // 关闭客户审批模态框
      closeExamine() {
        this.show_Examine_dialog = false; // 关闭模态框
      },
      // 客户审批提交成功后的回调函数
      submitExamineAfter() {
        this.show_Examine_dialog = false;
        this.getDataList(); // 刷新客户列表
      },
      // 获取客户状态
      getStatus() {
        if (!this.status_list.length) {
          this.$http
            .getCrmCustomerFollowInfo({ params: this.getStutas_params })
            .then((res) => {
              if (res.status === 200) {
                this.status_list = res.data;
                this.status_list.map((item) => {
                  if (item.title == "有效客户") {
                    item.value_name = 1;
                  } else if (item.title == "无效客户") {
                    item.value_name = 2;
                  } else if (item.title == "暂缓客户") {
                    item.value_name = 3;
                  } else {
                    item.value_name = 1;
                  }
                  return item;
                });
                this.copy_status_list = JSON.parse(
                  JSON.stringify(this.status_list)
                ); // 深拷贝客户状态
              }
            });
        }
      },
      setStatus(row) {
        this.status_list = JSON.parse(JSON.stringify(this.copy_status_list)); // 重新赋值客户状态
        let delIndex = "";
        this.status_list.map((item, index) => {
          if (item.id == row.tracking.id) {
            delIndex = index;
          }
        });
        if (typeof delIndex == "number") {
          this.status_list.splice(delIndex, 1);
        }
      },
      // 点击更改客户状态
      onClickFollowStatus(row, item) {
        // 审批无需审核 is_state == 2
        if (row.is_state == 2) {
          let examine = false;
          if (row.state_list && row.state_list.length) {
            // 如果state_list中有当前要更改的状态
            row.state_list.map((list) => {
              if (list == item.id) {
                examine = true;
              }
            });
          }
          if (examine) {
            // 判断是不是客户管理员， 是就不审批直接更改
            if (row.admin_list && row.admin_list.length) {
              // 遍历客户管理员
              const isLargeNumber = (item) => item == this.selfID;
              let is_admins = row.admin_list.findIndex(isLargeNumber);
              // 如果是客户管理员无需审批
              if (is_admins >= 0) {
                this.setCrmCustomerStatus(row, item); // 无需审批
              } else {
                this.requiresExamineStatus(row, item); // 需要审批
              }
            }
          } else {
            // 不走审批
            this.setCrmCustomerStatus(row, item); // 无需审批
          }
        } else {
          this.$message.warning("当前客户状态不可以进行审批");
        }
        // console.log(row, "row", item, 'item')
      },
      // 弹框拨打电话详情
      play1(row) {
        clearTimeout(this.timer)
        let audios = document.getElementById("musicMp3");
        if (this.currI !== row && this.currI) {
          audios.pause();
          this.$set(this.currI, 'isPlaying', false);
          audios.src = row.last_call_info.record_url
          this.$forceUpdate();
        }
        if (row.isPlaying) {
          audios.pause();
          this.$set(row, 'isPlaying', false)
          this.$forceUpdate();
        } else {
          if (this.currI !== row) {
            audios.src = row.last_call_info.record_url
          }
          audios.play();
          this.currI = row;
          this.$set(row, 'isPlaying', true);
          this.$forceUpdate();
        }
        this.timer = setTimeout(() => {
          this.$set(row, 'isPlaying', false)
          this.$forceUpdate();
        }, row.last_call_info.duration * 1000);
      },
      // 无审批修改客户状态
      setCrmCustomerStatus(row, item) {
        this.$http
          .setCrmCustomerStatus({ id: row.id, tracking_id: item.id })
          .then((res) => {
            if (res.status == 200) {
              this.$message.success("操作成功");
              document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
              this.getDataList(); // 刷新数据
            }
          });
      },
      // 需审批修改客户状态
      requiresExamineStatus(row, item) {
        document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
        this.Examine_type = 19; // 切换类型为修改客户状态
        this.show_Examine_dialog = true; // 显示审批模态框
        this.ponent_Examine_data = row; // 赋值客户信息
        this.ponent_Examine_stutas = item; // 选择的要修改的状态
        // 获取部门
        if (!this.AllDepartment.length) {
          this.getDepartmentList();
        }
      },
      // // 快速编辑客户维护资料
      // fastEditData(row) {
      //   row.information =  1
      //   this.ponent_maintain_data = row;
      //   this.show_cus_Edit = true;
      // },
      // 关闭快速编辑客户维护资料
      fastCloseEdit() {
        this.show_cus_Edit = false;
      },
      // 刷新页面获取最新数据
      submitMaintain() {
        this.getDataList();
        this.show_cus_Edit = false;
      },
      // 快速查看客户手机号
      fastLookTel(row) {
        // 查看电话时跟进
        this.$http.informationlookphone(row.id).then((res) => {
          if (res.status === 200) {
            row.information =  1
            if (row.tracking && row.tracking.id) {
              row.tracking_id = row.tracking.id
            }
            this.ponent_Tel_data = row;
            this.nowDialData = res.data
            this.show_look_Tel = true; // 显示模态框
            this.getDataList(); // 刷新页面数据
          }
        });
      },
      // 关闭快速查看客户手机号回调函数
      fastCloseTel() {
        this.show_look_Tel = false;
      },
      // 提交查看手机号跟进成功回调函数
      fastSubmitTel() {
        this.getDataList();
        this.show_look_Tel = false;
      },
      // 阻止默认行为和事件传播
      showTypeStatus(e) {
        e.preventDefault();
        e.stopPropagation();
        this.is_pullDown1 = false;
        if (this.is_pullDown) {
          this.is_pullDown = false;
        } else {
          this.is_pullDown = true;
        }
      },
      showTypeStatus1(e) {
        e.preventDefault();
        e.stopPropagation();
        this.is_pullDown = false;
        if (this.is_pullDown1) {
          this.is_pullDown1 = false;
        } else {
          this.is_pullDown1 = true;
        }
      },
      showTypeStatus2(e) {
        e.preventDefault();
        e.stopPropagation();
        this.is_pullDown = false;
        this.is_pullDown1 = false;
        if (this.is_pullDown2) {
          this.is_pullDown2 = false;
        } else {
          this.is_pullDown2 = true;
        }
      },
      // 改变搜索类型
      changeScreenType(list, item) {
        // console.log(list, item, '1111111111');
        // list: 当前选择类型 , item: 选择之前的类型
        // console.log(list, "list", item, "item");
        this.is_pullDown = false; // 关闭popover
        let firstIndex = "";
        let secondIndex = "";
        this.customer_list_type.map((arr, index) => {
          if (arr.title == item.title) {
            firstIndex = index;
          }
        });
        this.customer_list_type.splice(firstIndex, 1, list);
        this.screen_list_type.map((arr, index) => {
          if (arr.title == list.title) {
            secondIndex = index;
          }
        });
        this.screen_list_type.splice(secondIndex, 1, item);
        this.getDataLists(list);
      },
      changeScreenType1(list, item) {
        this.is_pullDown1 = false; // 关闭popover
        let firstIndex1 = "";
        let secondIndex1 = "";
        this.customer_list_type.map((arr, index) => {
          // console.log(arr.title, '111');
          // console.log(item.title, '222');
          if (arr.title == item.title) {
            firstIndex1 = index;
          }
        });
        this.customer_list_type.splice(firstIndex1, 1, list);
        this.screen_list_type1.map((arr, index) => {
          if (arr.title == list.title) {
            secondIndex1 = index;
          }
        });
        this.screen_list_type1.splice(secondIndex1, 1, item);
        this.getDataLists(list);
      },
      changeScreenType2(list, item) {
        this.is_pullDown2 = false; // 关闭popover
        let firstIndex1 = "";
        let secondIndex1 = "";
        this.customer_list_type.map((arr, index) => {
          // console.log(arr.title, '111');
          // console.log(item.title, '222');
          if (arr.title == item.title) {
            firstIndex1 = index;
          }
        });
        this.customer_list_type.splice(firstIndex1, 1, list);
        this.screen_list_type2.map((arr, index) => {
          if (arr.title == list.title) {
            secondIndex1 = index;
          }
        });
        this.screen_list_type2.splice(secondIndex1, 1, item);
        this.getDataLists(list);
      },
      // 客户列表快速跟进客户内容
      fastFollowUp(row) {
        // console.log(row, "row");
        this.ponent_Follow_data = row; // 赋值客户信息
        this.show_Follow_dialog = true;
      },
      // 快速添加跟进成功执行
      addFollowSuccess() {
        this.$message.success("操作成功");
        this.getDataList(); // 刷新页面数据
      },
      // 关闭快速跟进客户内容模态框
      closeFollow() {
        this.show_Follow_dialog = false;
      },
      // 排序
      sortChangeData(column) {
        // console.log(column.column.label,column,"参数");
        // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
        if (column) {
          if (column.column.label === "客户名称") {
            this.params.c_type3 = 1;
          } else if (column.column.label === "更新时间") {
            this.params.c_type3 = 2;
          } else if (column.column.label === "客户线索") {
            this.params.c_type3 = 3;
          } else if (column.column.label === "创建时间") {
            this.params.c_type3 = 4;
          }
          // 判断升序
          if (column.order === "ascending") {
            this.params.c_type3_sort = 2;
          } else if (column.order === "descending") {
            // 判断倒序
            this.params.c_type3_sort = 1;
          } else {
            // 默认
            // this.params.c_type1 = 0;
            delete this.params.c_type3;
            delete this.params.c_type3_sort;
          }
          this.getDataList();
        }
      },
      sortChange(column) {
        // console.log(column.column.label,column,"参数");
        // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
        if (column) {
          // 判断升序
          if (column.order === "ascending") {
            this.admin_params.sort = 1;
          } else if (column.order === "descending") {
            // 判断倒序
            this.admin_params.sort = 2;
          } else {
            // // 默认
            // // this.params.c_type1 = 0;
            delete this.admin_params.sort;
            // delete this.params.c_type3_sort;
          }
          this.admin_params.page = 1
          this.getAdmin();
        }
      },
      // 赋值客户编号
      copyCusID(id) {
        this.$onCopyValue(id);
      },
    },
  };
  </script>
    
<style lang="scss" scoped>
.items-center {
  align-items: center;
}

.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 20px 24px 80px;
  // overflow: hidden;

  .box-crm-head {
    width: 100%;
    // background-color: #ffff;
    // margin: 10px 20px 10px 0px;
    display: flex;

    .crm-head-list {
      width: 100px;
      height: 30px;
      color: #8a929f;
      text-align: center;
      padding: 10px;
      //   margin-right: 20px;
      cursor: pointer;

      &.head_list {
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        background-color: #ffff;
      }
    }
  }

  .cus-userName {
    text-align: left;
    margin: 5px 5px 5px 0;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.w100 {
      max-width: 100%;
    }

    &.cus-userName1 {
      //   max-width: 100%;
      color: #606266;
      //   white-space: normal;
    }
  }

  .head-list {
    margin-right: 10px;
    margin-top: 10px;
  }

  .my-cascader::placeholder {
    color: red;
  }

  .crm-selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        // border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        padding: 15px 27px !important;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
  }

  //   .el-range-editor--small.el-input__inner {
  //     height: 26px;
  //   }

  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    margin-left: -24px;
    margin-right: -24px;

    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }

  .grid-content {
    background: #fff;
    height: 113px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
    }

    .left {
      font-size: 14px;
      color: #8a929f;
      margin-right: 64px;
    }

    .number {
      display: flex;
      align-items: center;

      .number1 {
        font-size: 24px;
        color: #2e3c4e;
        margin-right: 10px;
      }

      .number2 {
        font-size: 12px;
        color: #fe6c17;
      }
    }
  }
}

.isbottom {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  flex-wrap: wrap;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
    white-space: nowrap;
  }

  .selected-header {
    font-size: 14px;
    color: #8a929f;
    margin-right: 8px;
    padding: 3px 16px;
    cursor: pointer;
    margin-bottom: 5px;
    white-space: nowrap;
  }

  .label_actions {
    border-radius: 4px;
    background: #e8f1ff;
    color: #2d84fb;
  }

  .selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
        //
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
  }
}

.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;

  .text {
    font-size: 14px;
  }
}

.labelrow {
  margin-bottom: 20px;
}

.t-t-b-right {
  // width: 50%;
  // height: 30px;
  display: flex;
  justify-content: flex-end;
  position: relative;

  // background: #fff;
  // overflow: hidden;
  // display: none;
  // #myButton {
  //   display: none;
  //   /* 默认隐藏按钮 */
  // }

  // /* 当屏幕宽度小于600px时显示按钮 */
  // @media screen and (max-width: 1350px) {
  //   #myButton {
  //     display: block;
  //     /* 显示按钮 */
  //   }
  // }

  // #myButton1 {
  //   margin-left: 10px;
  //   display: none;
  //   /* 默认隐藏按钮 */
  // }

  // /* 当屏幕宽度小于600px时显示按钮 */
  // @media screen and (max-width: 1350px) {
  //   #myButton1 {
  //     display: block;
  //     /* 显示按钮 */
  //   }
  // }

  // &.show {
  // display: flex;
  // transition: 0.3s;
  // }
  &.abs {
    position: absolute;
    // width: 620px;
    right: 25px;
    // margin-bottom: 15px;
    overflow: hidden;

    // .el-button {
    //   // height: 30px;
    //   // margin-top: 2px;
    // }

    // &:hover {

    // }
  }
}

.crmuserbox {
  align-items: center;

  span {
    display: block;
  }

  img {
    border-radius: 50%;
  }

  .label {
    width: fit-content;
    padding: 0 6px;
    background: #2d84fb;
    border-radius: 4px;
    color: #fff;
  }
}

.cus-clue-text {
  width: 270px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;

  &.cus-clue-text_community {
    width: 100%;
  }
}

.table-top-box-abs {
  position: absolute;
  /* padding-top: 30px; */
  padding: 10px 24px;
  top: 0;
  left: 0;
  right: 0;
  height: 65px;

  // transition: 0.3s;
  &.fixed {
    position: fixed;
    top: 60px;
    left: 254px;
    right: 40px;
    padding: 10px 24px;
    background: #fff;
    z-index: 100;

    .abs {
      right: 25px;
    }
  }
}
</style>
<style lang="scss">
.cus-box {
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;
  padding-left: 5px;

  .cus-box-header {
    .cus-header-user {
      align-items: center;

      .cus-userName {
        color: #2d84fb;
        text-align: left;
        margin: 5px 5px 5px 0;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.w100 {
          max-width: 100%;
        }

        &.cus-userName1 {
          //   max-width: 100%;
          color: #606266;
          //   white-space: normal;
        }
      }

      .cus-sex {
        width: 16px;
        height: 16px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 12px;
      white-space: nowrap;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      color: #67c23a;
      border: 1px solid #67c23a;
    }

    .cus-icon-purchase {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }
  }

  .cus-img {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 14px;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      display: block;
      color: #67c23a;
      border: 1px solid #67c23a;
      cursor: pointer;
    }

    .cus-icon-purchase {
      display: block;
      color: #98a6c3;
      border: 1px solid #98a6c3;
      cursor: pointer;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }

    .cus-douyinIcon {
      width: 16px;
      height: 16px;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .fast-Edit-cus {
    display: none;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.b-tabs {
  cursor: pointer;

  .b-t-item {
    margin-right: 24px;
    color: #8a929f;
    position: relative;

    &.isactive {
      color: #00a3ff;

      &::after {
        position: absolute;
        content: "";
        left: 50%;
        transform: translateX(-50%);
        height: 3px;
        // background: #2d84fb;
        width: 100%;
        display: block;
        margin-top: 4px;
      }
    }
  }

  .config-customer {
    .el-button {
      padding: 7px 15px;
    }
  }
}

.abs {
  .search_loudong {
    height: 30px;
    margin-top: 2px;
    width: 65px;
  }
}

.search_loudong {
  background: #409eff;
  white-space: nowrap;
  padding: 0 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.clueRemark {
  display: flex;

  .el-textarea {
    width: 360px;

    .el-textarea__inner {
      min-height: 40px !important;
      height: 40px;
    }
  }
}

.table-btns {
  .search-Belong {
    .el-button {
      padding: 4px 7px;
      margin-top: 3px;
      border-radius: 2px;
    }
  }

  .fast-look-tel {
    display: none;
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;
    opacity: 0.8;
    line-height: 1;

    i {
      color: #409eff;
    }

    // img {
    //   width: 100%;
    //   height: 100%;
    // }
  }
}

// img {
//   width: 100%;
//   height: 100%;
// }

.audio_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
  background: #409eff;
}

.labelname {
  margin-bottom: 10px;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;
  }

  .checked {
    background: rgba(45, 132, 251, 0.15);
    border: 1px solid rgba(45, 132, 251, 1);
    color: #2d84fb;
  }
}

.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.inp_no_border {
  width: 155px;

  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
}

.dialog_customer_label {
  height: 560px;
  overflow-y: auto;
}

.search-member-box {
  .el-input {
    .el-input__inner {
      width: 155px;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }
}

.cus-clue-label {
  align-items: center;
  flex-wrap: wrap;
  line-height: 1;

  .cus-icon-label {
    color: #16a1bc;
    border: 1px solid #16a1bc;
    // color: #409eff;
    // border: 1px solid #b3d8ff;
    font-size: 14px;
    padding: 5px 11px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
  }
}

.clueLabel {
  width: 20px;
  height: 20px;
  display: none;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.last_call_follow {
  align-items: center;
  justify-content: center;

  &.w180 {
    width: 160px;
    overflow: hidden;
  }

  .cus-clue-text_c {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 3px;
      right: -5px;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #9edf2e;
    }
  }

  .cus-clue-text_u {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 3px;
      right: -5px;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #f56c6c;
    }
  }
}

.label_list {
  flex-wrap: wrap;

  .label_item {
    margin-bottom: 5px;
  }
}

.el-table__body {
  .el-table__row {
    .is-center:nth-child(4):hover .clueLabel {
      display: block;
    }

    .is-center:nth-child(2):hover .fast-Edit-cus {
      display: block;
    }

    .is-center:nth-child(3):hover .fast-look-tel {
      display: block;
    }

    .is-center:nth-child(6):hover .followLabel {
      display: block;
    }
  }
}

.screen-type {
  display: flex;
  flex-direction: column;
  margin: -12px;
  padding: 6px 0px;

  .screen-type-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .screen-type-content:hover {
    background-color: #f5f7fa;
  }
}

.public-status {
  // display: inline-block;
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  padding: 0 4px;
  border-radius: 4px;
}

.follow-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.followLabel {
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.el-date-editor {
  width: 280px !important;
}

#musicMp3 {
  position: absolute;
  left: 100000%;
  top: 10000%;
}

.sticky {
  position: sticky;
  top: 200px;
}

.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}

.content-box-crm {
  &.content-box-crm-pr {
    position: relative;
    padding: 24px 0;
    padding-top: 85px;
  }
  &.pt24{
    padding-top: 24px;
  }
}
.op-btn+.op-btn{
  margin-left: 10px;
}
</style>
    