<template>
  <div>
    <el-menu class="menu-bar">
      <template v-for="value in list">
        <el-submenu
          :index="value.id + ''"
          :key="value.id"
          v-if="value.subs && value.subs != null && value.subs.length"
        >
          <template slot="title">
            <div
              class="flex-row row_item align-center"
              @click="onClickItem(value)"
            >
              <span class="flex-1" slot="title">{{ value.name }}</span>
              <img
                class="img"
                v-if="value.checked"
                src="@/assets/select.png"
                alt=""
              />
            </div>
          </template>
          <memberList
            :list="value.subs"
            @onClickItem="onClickItem"
          ></memberList>
        </el-submenu>

        <el-menu-item
          :index="value.id + ''"
          v-else
          :key="value.id"
          :class="value.id"
        >
          <div
            class="flex-row row_item align-center"
            @click.prevent.stop="onClickItem(value)"
          >
            <span class="flex-1" slot="title">{{ value.name }}</span>
            <img
              class="img"
              v-if="value.checked"
              src="@/assets/select.png"
              alt=""
            />
          </div>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script>
export default {
  props: ['list'],
  name: 'memberList',
  created () {
  },
  components: {
  },
  methods: {
    onClickItem (value) {

      value.checked = !value.checked
      console.log(value);
      this.$forceUpdate()
      this.$emit('onClickItem', value)
    }



    // clickMenu (e, value) {
    //   console.log(e, 12323);
    //   this.$emit('onClickMenu', { detail: e.detail, item: value })
    // },
  }
}
</script>
<style scoped lang="scss">
.row_item {
  .img {
    width: 20px;
    height: 20px;
    object-fit: cover;
  }
}
.el-submenu .el-menu-item {
  padding: 0 20px 0 45px;
}
</style>