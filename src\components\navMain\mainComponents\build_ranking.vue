<template>
  <el-container>
    <el-header>
      <el-button size="mini" type="primary" @click="createData"
        >添加榜单</el-button
      >
      <el-button
        size="mini"
        type="success"
        @click="$router.push('/bind_ranking')"
        >绑定楼盘</el-button
      >
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_create_rank" label-width="100px">
        <el-form-item label="榜单名称：">
          <el-input
            v-model="form_create_rank.name"
            placeholder="榜单名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="首页显示：">
          <el-radio-group v-model="form_create_rank.home_display">
            <el-radio-button
              v-for="item in enable"
              :key="item.value"
              :label="item.value"
            >
              {{ item.desc }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注信息：">
          <el-input
            type="textarea"
            v-model="form_create_rank.remark"
            placeholder="备注信息"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onCreate">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "build_ranking",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      form_create_rank: {},
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      enable: [
        { value: 0, desc: "关闭" },
        { value: 1, desc: "开启" },
      ],
      dialogTitle: "",
      dialogCreate: false,
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "name", label: "名称" },
        { prop: "remark", label: "备注" },
        {
          label: "首页显示",
          render: (h, data) => {
            return <div>{data.row.home_display == 0 ? "不显示" : "显示"}</div>;
          },
        },
        {
          label: "列表显示",
          render: (h, data) => {
            return <div>{data.row.list_display == 1 ? "显示" : "不显示"}</div>;
          },
        },
        { prop: "sort", label: "排序" },
        { prop: "created_at", label: "添加时间" },
        { prop: "updated_at", label: "修改时间" },
        {
          label: "操作",
          width: "250",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="success"
                  onClick={() => {
                    this.changeData(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  size="mini"
                  type="primary"
                  onClick={() => {
                    this.$router.push(
                      `/bind_ranking?ranking_id=${data.row.id}`
                    );
                  }}
                >
                  绑定楼盘
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  onClick={() => {
                    this.deleteData(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.$http.getCustomizeRanking({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    createData() {
      this.form_create_rank = {
        home_display: 0,
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    changeData(row) {
      this.form_create_rank = row;
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    deleteData(row) {
      this.$confirm("此操作将删除该数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.deleteCustomizeRanking(row.id).then((res) => {
          if (res.status === 200) {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.getDataList();
          }
        });
      });
    },
    onCreate() {
      if (this.dialogTitle === "addData") {
        this.$http.createCustomizeRanking(this.form_create_rank).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.updateCustomizeRanking(this.form_create_rank).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
  },
};
</script>

<style></style>
