<template>
<el-dialog :title="title" :visible.sync="show" width="786px">
    <el-form label-width="120px" style="padding-right: 40px">
        <el-form-item label="上传凭证">
            <tUpload  accept=".jpeg,.jpg,.png,.pdf,.docx,.txt,.text,.xls,.xlsx,.doc,.ppt,.pptx" v-model="params.file"></tUpload>
        </el-form-item>

        <el-form-item label="凭证描述:">
            <el-input v-model="params.file_desc" placeholder="请输入凭证描述" type="textarea"></el-input>
        </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>

<script>
import tUpload from '@/components/tplus/tUpload/index.vue'
export default {
    components: {
        tUpload
    },
    data(){
        return {
            show: false,        //dialog是否显示
            successFn: null, 
            isAdd: true,
            submiting: false,
            params: {}
        }
    },
    computed: {
        title(){
            return this.isAdd? '新增附件凭证' : '编辑附件凭证';
        }
    },
    methods: {
        open(params){
            this.params = {
                id: 0,
                report_id: 0,
                file: '',
                file_desc: '',
            };
            for(const key in params){
                if(this.params[key] !== undefined){
                    this.params[key] = params[key];
                }
            }
            this.isAdd = params.id ? false : true;
            this.show = true;
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            if(!this.params.file){
                this.$message.error('请上传附件凭证');
                return;
            }

            const params = {...this.params};
            if(this.isAdd){
                delete params.id;
            }
            this.submiting = true;
            const res = await this.$http[this.isAdd?'addReportFilesAPI':'editReportFilesAPI'](params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }  
}
</script>
<style lang="scss" scoped>  
</style>