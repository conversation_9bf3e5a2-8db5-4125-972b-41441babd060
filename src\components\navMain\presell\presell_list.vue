<template>
  <el-container>
    <el-header>
      <el-button size="mini" type="primary" @click="createData"
        >添加数据</el-button
      >
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
    </el-main>
    <el-footer>
      <myPagination
        :total="params.total"
        :pagesize="params.per_page"
        :currentPage="params.page"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_presell" label-width="100px">
        <el-form-item label="预售标题：">
          <el-input
            v-model="form_presell.title"
            placeholder="请输入预售标题"
          ></el-input>
        </el-form-item>
        <el-form-item label="许可名称：">
          <el-input
            v-model="form_presell.licence_name"
            placeholder="请输入许可名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="总住宅：">
          <el-input
            v-model="form_presell.residence_total"
            placeholder="请输入总住宅套数："
            type="number"
            min="0"
            step="1"
          >
            <template slot="append">套</template>
          </el-input>
        </el-form-item>
        <el-form-item label="住宅面积：">
          <el-input
            v-model="form_presell.residence_total"
            placeholder="请输入住宅面积："
            type="number"
            min="0"
            step="1"
          >
            <template slot="append">㎡</template>
          </el-input>
        </el-form-item>
        <el-form-item label="非住宅：">
          <el-input
            v-model="form_presell.non_residence_total"
            placeholder="请输入非住宅套数："
            type="number"
            min="0"
            step="1"
          >
            <template slot="append">套</template>
          </el-input>
        </el-form-item>
        <el-form-item label="非住宅面积：">
          <el-input
            v-model="form_presell.non_residence_area_total"
            placeholder="请输入非住宅面积："
            type="number"
            min="0"
            step="1"
          >
            <template slot="append">㎡</template>
          </el-input>
        </el-form-item>
        <el-form-item label="许可内容：">
          <el-input
            type="textarea"
            v-model="form_presell.licence_content"
            placeholder="请输入许可内容："
          >
          </el-input>
        </el-form-item>
        <el-form-item label="预售日期：">
          <el-date-picker
            v-model="form_presell.presell_date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择预售日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onCreate">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "presell_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        build_id: this.$route.query.build_id,
        website_id: localStorage.getItem("website_id"),
      },
      form_presell: {},
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      dialogCreate: false,
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "area_total", label: "总面积/㎡" },
        { prop: "residence_total", label: "总住宅/套" },
        { prop: "non_residence_total", label: "非住宅/套" },
        { prop: "title", label: "预售标题" },
        { prop: "licence_name", label: "预售许可" },
        { prop: "presell_date", label: "预售日期" },
        { prop: "licence_content", label: "内容" },
        {
          label: "操作",
          fixed: "right",
          width: "250",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="success"
                  onClick={() => {
                    this.changeData(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  onClick={() => {
                    this.deleteData(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.$http.getPresellList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    createData() {
      this.form_presell = {
        build_id: this.params.build_id,
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    onCreate() {
      if (this.dialogTitle === "addData") {
        this.$http.createPresellData(this.form_presell).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.updatePresellData(this.form_presell).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    changeData(row) {
      this.form_presell = row;
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    deleteData(row) {
      this.$confirm("此操作将删除该数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.deletePresellData(row.id).then((res) => {
          if (res.status === 200) {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.getDataList();
          }
        });
      });
    },
  },
};
</script>

<style lang="scss">
.el-input {
  width: 300px;
}
</style>
