import Vue from "vue";
import utils from './utils';

var directives = {
  number_rang: {
    bind: function () {
      // const input = el.children[0]
      // const value = Number(input.value)
      // if (isNaN(value) || input.value === '') {
      //   this.pre_value = ''
      //   return
      // }
    },
    update: function (el, binding) {
      //这里可以根据当前传递的权限数组去匹配后端就扣提供的当前用户所有的权限，没找到则删除这个节点元素
      if (binding.value) {
        const trigger = (el, type) => {
          const e = document.createEvent("HTMLEvents");
          e.initEvent(type, true, true);
          el.dispatchEvent(e);
        };
        // eslint-disable-next-line
        const transFloat = function (value, digit = 0) {
          // 值允许输入一个小数点和数字
          value = value
            .replace(/[^\d.]/g, "")
            .replace(/^0\d+|^\./g, "")
            .replace(/\.{2,}/g, ".")
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".") //保证.只出现一次，而不能出现两次以上
            .replace(/^(\d+)\.(\d\d).*$/, "$1.$2")
          let num = 1
          if (digit) {
            num = Math.pow(10, digit)
          }
          if (value.indexOf('.') === value.length - 1 && digit) {
            return value
          } else {
            let arr = value.split(".")
            if (arr.length > 1 && arr[1].length == 2) {
              return Math.floor(value * num) / num
            } else {
              return value
            }

          }
        }
        setTimeout(() => {
          const input = el.children[0];
          const value = transFloat(input.value, binding.arg);
          const min = Number(binding.value[0]);
          const max = Number(binding.value[1]);
          if (isNaN(value) || input.value === "") {
            input.value = "";
            trigger(input, "input");
            return;
          }
          if (!isNaN(min) && value < min) {
            input.value = min;
            trigger(input, "input");
          } else if (!isNaN(max) && value > max) {
            input.value = max;
            trigger(input, "input");
          } else {
            input.value = value;
            trigger(input, "input");
          }
        }, 10);
      }
    },
  },

  fixedScroll: {
    update: utils.debounce(function (el, binding){
      let scrollElem = el.querySelector('.el-table__body-wrapper');
      if(!scrollElem){
        return;
      }
      scrollElem.style.overflowX = 'auto';
      let virtualNode = el.querySelector('.virtual-scrollable-x'),
        virtualInnerNode = null;
      if(!virtualNode){
        virtualNode = document.createElement('div');
        virtualInnerNode = document.createElement('div');

       
        let isMac = /macintosh|mac os x/i.test(navigator.userAgent.toLowerCase())

        virtualNode.className = "virtual-scrollable-x" + (isMac ? " mac" : "")
        virtualInnerNode.className = "virtual-scrollable-x-inner";
        virtualInnerNode.style.height = '1px';
        virtualNode.style.overflowX = 'auto';
        virtualNode.style.position = 'fixed';
        virtualNode.style.zIndex = 1000;
        virtualNode.appendChild(virtualInnerNode);
        el.appendChild(virtualNode);

        let bottom = 0;
        if(typeof binding.value === 'number'){
          bottom = binding.value;
        }else{
          let bootomElem = el.querySelector(binding.value);
          if(bootomElem){
            bottom = bootomElem.clientHeight;
          }
        }
        virtualNode.style.bottom = bottom + 'px';

        virtualNode.addEventListener('scroll', utils.throttle(function(e){
          scrollElem.scrollLeft = virtualNode.scrollLeft;
        }, 16.66))

        let leftTable = el.querySelector('.el-table__fixed'), 
          rightTable = el.querySelector('.el-table__fixed-right');

        if(leftTable){
          leftTable.style.paddingBottom = '30px';
        }
        if(rightTable){
          rightTable.style.paddingBottom = '30px';
        }

        new MutationObserver(utils.debounce(()=>{
          virtualNode.style.width = scrollElem.clientWidth + 'px';
          virtualInnerNode.style.width = scrollElem.scrollWidth + 'px';
          let rect = scrollElem.getBoundingClientRect();
          virtualNode.style.left = rect.left+'px';
        }, 300)).observe(scrollElem, { attributes: true, subtree: true }); 
      }else{
        virtualInnerNode = virtualNode.firstElementChild;
      }

   
      let rect = scrollElem.getBoundingClientRect();
      virtualNode.style.left = rect.left+'px';
      
      virtualNode.style.width = scrollElem.clientWidth + 'px';
      virtualInnerNode.style.width = scrollElem.scrollWidth + 'px';
      scrollElem.style.overflowX = 'hidden';
    }, 1000)
  },
};

export default {
  install: function () {
    Object.keys(directives).forEach((key) => {
      // 注册指令
      Vue.directive(key, directives[key]);
    });
  },
};
