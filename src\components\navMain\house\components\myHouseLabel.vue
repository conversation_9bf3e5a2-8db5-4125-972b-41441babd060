<template>
    <div>
        <el-dialog
            title="房源标签"
            :visible.sync="dialogVisible"
            width="660px"
            @close="closeHouseLabel"
        >
            <div class="lab_list">
                <div v-for="item in labels_list" :key="item.id">
                <div class="labelname">{{ item.name }}</div>
                <div class="lables-box div row">
                    <div
                        class="labels-item"
                        :class="{ checked: i1.check }"
                        v-for="(i1, i2) in item.sub"
                        :key="i2"
                        @click="checkChangeLabels(i1)"
                    >
                    {{ i1.name }}
                    </div>
                </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onClickLabels">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            dialogVisible: false, // 控制房源标签模态框
            labels_list: [], // 标签列表
            labels_selected: [], // 已选中的标签列表
        }
    },
    props: {
        // 控制房源标签模态框
        showHouseLabel: {
            type: Boolean,
            default: false
        },
        // 仅出售、租售显示的房源标签
        label_sale: {
            type: Array,
            default: () => {}
        }
    },
    created() {
        this.dialogVisible = this.showHouseLabel; // 赋值模态框参数
        this.getHouseLabel(); // 获取房源标签
    },
    watch: {
        // 展示已选中的标签
        labels_list: {
            handler(newVal) {
                if(newVal.length) {
                    this.label_sale.map((label) => {
                        this.labels_list.map((item) => {
                            item.sub.map((sub) => {
                                if(sub.values == label) {
                                    sub.check = true;
                                }
                            });
                        });
                    })
                    this.$forceUpdate();
                }
            }
        }
    },
    methods: {
        // 关闭模态框回调函数
        closeHouseLabel() {
            this.$emit("closeHouseLabel", {});
        },
        // 选中房源标签
        checkChangeLabels(item) {
            item.check = !item.check;
            this.$forceUpdate();
        },
        // 确定增加标签
        onClickLabels() {
            let num = [];
            this.labels_list.map((item) => {
                item.sub.map((sub) => {
                    if(sub.check == true) {
                        num.push(sub);
                    }
                });
            });
            this.labels_selected = num; // 赋值已选中的标签
            this.dialogVisible = false; // 关闭模态框
            this.$emit("addHouseLabel", this.labels_selected);
        },
        // 获取房源标签
        getHouseLabel() {
            this.$ajax.house.getHouseLabels().then((res) => {
                if(res.status == 200) {
                    this.labels_list = res.data;
                    // 处理数据，追加check控制样式显示
                    this.labels_list = this.labels_list.map((item) => {
                        item.sub.map((sub) => {
                            sub.check = false;
                            return sub;
                        });
                        return item;
                    });
                }
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.lab_list {
  max-height: 480px;
  overflow-y: auto;
  .labelname {
    margin-bottom: 10px;
  }
  .lables-box {
    flex-wrap: wrap;
    flex: 1;

    .labels-item {
      margin-bottom: 10px;
      cursor: pointer;
      background: #f1f4fa;
      border-radius: 4px;
      padding: 5px 22px;
      min-width: 80px;
      font-size: 16px;
      border: 1px solid #f1f4fa;
      text-align: center;
      margin-right: 24px;
      color: #8a929f;
      &.checked {
        background: rgba(45, 132, 251, 0.15);
        border: 1px solid rgba(45, 132, 251, 1);
        color: #2d84fb;
      }
    }
  }
}
</style>