<template>
    <div class="container">
        <div class="header-box">
            <div class="header-left">
                <!-- 头像 -->
                <div class="header-left-avatar flex-row">
                    <div class="avatar">
                        <img src="@/assets/icon-tx.png" alt="" />
                    </div>
                    <div class="name-box flex-box">
                        <div class="name">{{ recored_Detail.admin.user_name }}</div>
                        <div class="label">
                            <span v-if="recored_Detail.admin.department_name">
                                {{ recored_Detail.admin.department_name }}
                            </span>
                        </div>
                    </div>
                </div>
                <!-- 信息 -->
                <div class="header-left-info flex-row">
                    <!-- <div>案场名称：--</div> -->
                    <div class="flex-row align-center">岗位：
                        <span v-if="recored_Detail.admin.department_name">{{ recored_Detail.admin.department_name }}</span>
                        <span v-else>----</span>
                    </div>
                    <div class="interval flex-row align-center">时间：
                        <span v-if="recored_Detail.created_at">{{ recored_Detail.created_at }}</span>
                        <span v-else>----</span>
                    </div>
                    <div class="interval remark flex-row align-center">备注：
                        <span v-if="(detail.remark && detail.remark != '') && not_edit">{{ detail.remark }}</span>
                        <span v-else-if="(detail.remark && detail.remark != '') && !not_edit" class="remark-input">
                            <el-input ref="remark" v-model="detail.remark" placeholder="请输入" @blur="blurRemark"></el-input>
                        </span>
                        <span v-else>----</span>
                        <div v-if="not_edit" class="remark-icon" @click="editRemark">
                            <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="">
                        </div>
                    </div>
                </div>
                <!-- 搜索 -->
                <!-- <div class="header-left-search flex-row">
                    <div class="short-select">
                        <el-select v-model="search_params.type" placeholder="客户类型">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="short-select">
                        <el-select v-model="search_params.intention" placeholder="意向购买">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="short-select">
                        <el-select v-model="search_params.record" placeholder="录音类型">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="long-select">
                        <el-select v-model="search_params.record" placeholder="客户姓名+联系电话">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div> -->
            </div>
            <div class="header-right">
                <div class="title">客户信息</div>
                <div class="header-right-info flex-row">
                    <div>{{ detail.cname }}</div>
                    <!-- <div style="margin-left: 33px;">156*****086</div> -->
                    <div class="flex-box client-phone-detail">
                        <div v-if="detail.mobile" style="color: #8a929f">
                            {{ detail.mobile | mobileFilter }}
                        </div>
                        <div v-if="detail && detail.mobile_place" class="attribution">
                            {{ detail.mobile_place }}
                        </div>
                        <div class="name-box div row" style="margin: 0"
                            v-for="(item, index) in detail.subsidiary_mobile_list" :key="index">
                            <span v-if="item">{{ item | mobileFilter }}</span>
                        </div>
                    </div>

                    <div class="check-phone" @click="viewCustomerTel">查看电话</div>
                    <div class="level" @click="allStaffCallPhone"
                        v-if="detail.call_open_crm == 2 || detail.call_open_crm == 3">外呼</div>
                </div>
            </div>
        </div>
        <div class="main-box">
            <!-- 沟通记录 -->
            <div class="main-left">
                <recordCommune :record_id="record_id"></recordCommune>
            </div>
            <div class="main-right">
                <!-- 实体抽取 -->
                <communeInfo :record_id="record_id"></communeInfo>
            </div>
        </div>
        <el-dialog :visible.sync="showViewPhone" width="420px" :show-close="false" :close-on-click-modal="false">
            <div slot="title" class="warn-title">
                <div class="flex-row">
                    <div>
                        <img src="https://img.tfcs.cn/backup/static/admin/default_person.png" alt="" />
                    </div>
                    <div style="margin-left: 15px; cursor: pointer">
                        <el-popover placement="bottom-start" width="280" trigger="hover" :content="
                                                        (this.behavior_list.length && this.behavior_list[0].content) ||
                                                        this.detail.remark
                                                    ">
                            <span class="flex-1" slot="reference">{{
                                                            this.detail.cname
                                                            }}</span>
                        </el-popover>
                        <i class="el-icon-info"></i>
                    </div>
                </div>
            </div>
            <div class="flex-box">
                <div class="flex-row itemCenter">
                    <div>
                        <div class="flex-row all-phone" v-for="(item, index) in allChangeMobile" :key="index">
                            <div @click="copyAllPhone($event, item)" class="flex-1" style="font-weight: 700; color: red">
                                {{ item | cellPhoneDispose }}
                            </div>
                            <div class="callBtn">
                                <el-button size="mini" type="primary" style="margin-left: 213px"
                                    v-if="detail.call_open_crm == 2 || detail.call_open_crm == 3"
                                    @click="showCallPhone(item)">
                                    外呼
                                </el-button>
                            </div>
                        </div>
                    </div>
                    <div v-if="detail.call_open_crm == 0">
                        <el-select v-model="formInline.region" placeholder="接通状态" size="mini" clearable>
                            <el-option label="已接通" value="1"></el-option>
                            <el-option label="未接通" value="0"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="customer-status-box" v-if="is_deal">
                    <div style="color: #666; font-size: 16px; margin-bottom: 20px">
                        客户状态
                    </div>
                    <el-radio-group v-model="follow_params1.tracking_id" size="mini">
                        <el-radio v-for="item in tel_status_list" :key="item.id" :label="item.id" border>
                            {{ item.title }}
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="msg" style="margin: 20px 0">
                    <el-input :rows="3" v-model="follow_params1.content" type="textarea"
                        placeholder="请输入跟进内容（企业内公开）"></el-input>
                </div>
                <div class="footer flex-row align-center j-center">
                    <el-button type="primary" @click="onChangeStatus1" :loading="is_loading"
                        style="width: 375px">确定</el-button>
                </div>
            </div>
        </el-dialog>
        <el-dialog width="330px" title="智能手机" custom-class="dialog" :visible.sync="showPhone">
            <myCallPhone v-if="showPhone" :autoPhonenNumber="autoPhonenNumber" :concealPhoneNumber="concealPhoneNumber"
                :CallPhoneOwner="CallPhoneOwner" @phoneClose="closeCallPhone" @getCallId="getCallId"
                :phonePlace="detail.mobile_place"></myCallPhone>
        </el-dialog>
        <el-dialog width="330px" title="智能手机" custom-class="dialog" :visible.sync="showAllStaffPhone">
            <allStaffCallPhones v-if="showAllStaffPhone" :autoPhonenNumber="autoPhonenNumber"
                :concealPhoneNumber="concealPhoneNumber" :CallPhoneOwner="CallPhoneOwner" :clientID="u_id"
                @getCallId="getCallId" @allStaffphoneClose="allStaffphoneClose"
                :phonePlace="detail.mobile_place"></allStaffCallPhones>
        </el-dialog>
    </div>
</template>
<script>
import recordCommune from "@/components/navMain/crm/components/recordCommune";
import communeInfo from "@/components/navMain/crm/components/communeInfo";
import myCallPhone from "@/components/navMain/crm/components/myCallPhone";
import allStaffCallPhones from "@/components/navMain/crm/components/allStaffCallPhones";
export default {
    components: {
        recordCommune,
        communeInfo,
        myCallPhone,
        allStaffCallPhones,
    },
    data() {
        return {
            // 搜索接口参数
            search_params: {
                type: "", // 客户类型
                intention: "", // 意向购买
                record: "", // 录音类型
            },
            options: [],
            record_id: "", // 当前录音id
            // 客户详情请求参数
            getClient_params: {
                type: "", // 1: 公海 2: 私客
            },
            detail: {}, // 客户详情数据
            recored_Detail: {}, // 当前录音详情
            not_edit: true, // 判断客户备注是否编辑
            before_remark: "", // 修改前的客户备注
            showViewPhone: false, // 控制拨打电话模态框
            AdminData: {}, // 自己当前的信息
            selfID: '', // 自己的id
            has_roles: false, // 查看电话权限
            nowDialData: "", // 当前查看电话权限数据
            behavior_list: [], // 查看电话行为列表
            // 操作日志请求参数
            c_params: {
                client_id: "",
                page: 1,
                total: 0,
                per_page: 10,
            },
            is_c_loading: true, // 控制操作日志接口加载
            allChangeMobile: [], // 全部手机号
            showPhoneName: "", // 存储客户姓名
            autoPhonenNumber: "", // 查看电话-外呼自动填入的手机号
            concealPhoneNumber: "", // 查看电话-隐号手机号
            changeMobile: "", // 处理手机号前三后四
            formInline: {
                region: '1',
            },//电话接通状态
            is_loading: false, // loading加载
            call_id: [], // 存储外呼成功，跟进记录call_phone_id参数的值
            follow_params1: {
                type: 1,
                content: "",
                tracking_id: "", // 跟进状态
            },
            is_deal: true, // 客户状态是否成交
            // 查看电话跟进客户状态列表
            telFollowStatus: {
                type: 2,
            },
            tel_status_list: [], // 状态列表
            close_button: false,
            showPhone: false, // 控制手机拨打电话组件显示
            showAllStaffPhone: false, // 控制全员隐藏号码可拨打电话组件
            tel_follow_id: 0,
        }
    },
    created() {
        this.record_id = this.$route.query.r_id; // 录音id
        this.c_params.client_id = this.u_id = this.$route.query.u_id; // 赋值客户id
        if (this.$route.query.tel_follow_id) {
            this.tel_follow_id = this.$route.query.tel_follow_id;
        }
        this.type = this.$route.query.type; // 1公海 2私客
        this.getClient_params.type = this.type; // 赋值客户详情请求参数
        let re_detail = JSON.parse(sessionStorage.getItem('record_Detail'));
        if(re_detail && re_detail != null) {
            this.recored_Detail = re_detail;
        }
        console.log(this.recored_Detail,"录音详情")
        this.getDetail(); // 获取客户详情
    },
    beforeDestroy() {
        // sessionStorage.removeItem('record_Detail');
    },
    filters: {
        // 跟进权限判断更改为全号、隐号
        cellPhoneDispose(value) {
            value = value.substring(0, 3) + " " + value.substring(3, 7) + " " + value.substring(7);
            return value;
        },
        // 将手机号更改为隐号
        mobileFilter(val) {
            let reg = /^(.{3}).*(.{3})$/;
            return val.replace(reg, "$1*****$2");
        },
    },
    methods: {
        // 获取客户详情
        getDetail() {
            this.$http.getCrmCustomerDetailV2(this.u_id, this.getClient_params).then(async (res) => {
                if(res.status == 200) {
                    console.log(res.data,"客户详情");
                    this.detail = res.data;
                    this.showPhoneName = res.data.cname; // 存储客户姓名
                    this.allChangeMobile = []
                    let allChangeMobile = []
                    this.changeMobile = JSON.parse(JSON.stringify(this.detail.mobile));
                    if (this.detail.subsidiary_mobile != "" && this.detail.subsidiary_mobile != undefined) {
                        allChangeMobile = (this.changeMobile + "," + this.detail.subsidiary_mobile).split(",");
                    } else {
                        allChangeMobile = [this.changeMobile];
                    }
                    allChangeMobile.map(item => {
                        if (item) {
                            this.allChangeMobile.push(item)
                        }
                        return item
                    })
                    // 获取是否需要去跟进
                    let result = await this.$http.getForceFollow().catch(() => { });
                    if (result.status == 200) {
                        if (result.data && result.data.id > 0 && result.data.client_id == this.u_id) {
                            this.close_buttonshow()
                            this.tel_follow_id = result.data.id
                            this.showViewPhone = true; // 显示查看电话模态框
                            this.getbehaviorData()
                            this.getTelFollowStatus();
                        } else if (result.data && result.data.id > 0 && result.data.client_id != this.u_id) {
                            let url = `/crm_customer_detail?id=${result.data.client_id}&type=${this.website_types}&tel_follow_id=${result.data.id}`;
                            this.$goPath(url);
                        }
                    }
                    this.getOneselfList(); // 获取查看权限
                }
            })
        },
        // 编辑客户备注
        editRemark() {
            this.not_edit = !this.not_edit; // 控制客户备注显示的样式
            this.before_remark = this.detail.remark;
            this.$nextTick(() => {
                this.$refs.remark.focus();
            })
        },
        // 备注input失去焦点
        blurRemark() {
            this.not_edit = !this.not_edit;
            // 如果内容不相等就修改备注
            if(this.before_remark != this.detail.remark) {
                this.onChangeRemark(); // 修改客户备注
            }
        },
        // 修改客户备注
        onChangeRemark() {
            let form = {
                remark: this.detail.remark,
                id: this.detail.id,
            };
            if (!form.remark) {
                this.$message.error("请检查备注内容");
                return;
            }
            this.$http.setCrmCustomerRemarkData(form).then((res) => {
                if (res.status === 200) {
                    this.$message.success("操作成功");
                }
            });
        },
        //点击查看电话
        viewCustomerTel() {
            this.close_buttonshow(); // 获取权限显示范围
            if (!this.has_roles) {
                return this.$message.warning("暂无权限");
            }
            this.$http.setViewCrmCustomerTel(this.detail.id).then((res) => {
                if (res.status === 200) {
                    this.showViewPhone = true; // 显示查看电话模态框
                    this.nowDialData = res.data;
                    this.getbehaviorData(); // 获取操作日志
                    this.getTelFollowStatus(); // 获取查看电话跟进状态
                }
            });
        },
        // 获取权限显示范围
        close_buttonshow() {
            this.$http.getAuthShow("is_see_tel_follow").then((res) => {
                if (res.status == 200) {
                    if (res.data == 1) {
                        this.close_button = true
                    } else {
                        this.close_button = false
                    }
                }
            })
        },
        // 获取是否有权限查看客户操作（创始人、维护人、客户管理员可见）
        getOneselfList() {
            this.$http.getAdmin().then((res) => {
                if (res.status == 200) {
                    this.AdminData = res.data; // 存储自己的信息
                    this.selfID = res.data.id; // 赋值自己的id
                    let mangers = this.detail.admin_list
                    let keyuanManger = 0, whr = 0
                    if (mangers.includes(this.selfID + "")) {
                        keyuanManger = 1
                    }
                    if (this.selfID == this.detail.follow_id) {
                        whr = 1
                    }
                    if (keyuanManger == 1 || whr == 1 || (this.detail.follow_id > 0 && this.detail.follow_id == this.selfID)) {
                        this.has_roles = true
                    }
                }
            })
        },
        // 获取操作日志
        getbehaviorData() {
            if (this.c_params.page === 1) {
                this.behavior_list = [];
            }
            this.$http
            .getCrmCustomerLogList({ params: this.c_params })
            .then((res) => {
                if (res.status === 200) {
                    this.behavior_list = this.behavior_list.concat(res.data.data);
                    if (res.data.data.length < this.c_params.per_page) {
                        this.is_c_loading = true;
                    } else {
                        this.is_c_loading = false;
                    }
                }
            });
        },
        loadmoreLog() {
            if (this.is_c_loading) {
                return;
            }
            this.c_params.page++;
            this.getbehaviorData();
        },
        copyAllPhone(e, item) {
            e.stopPropagation();
            e.preventDefault();
            // 1: 全号 2: 隐号
            if (this.nowDialData.type == 2) {
                item = item.replace(/^(.{3}).*(.{3})$/, "$1*****$2");
            }
            this.$onCopyValue(item)
        },
        showCallPhone(item) {
            this.CallPhoneOwner = this.showPhoneName; // 赋值客户名称
            this.autoPhonenNumber = item; // 赋值客户真实号码
            if (this.nowDialData.type == 2) {
                this.concealPhoneNumber = this.changeMobile.replace(/^(.{3}).*(.{3})$/, "$1****$2"); // 赋值客户隐藏手机号
            }
            this.showPhone = true;
        },
        // 提交查看电话跟进内容
        onChangeStatus1() {
            this.is_loading = true; // 显示loading动画
            if (this.call_id != []) {
                this.$set(this.follow_params1, "client_id", this.u_id); // 客户ID
                this.$set(this.follow_params1, "contact_id", 0); // 客户ID
                this.$set(this.follow_params1, "call_phone_id", this.call_id[0]); // 电话记录ID
                this.$set(this.follow_params1, "call_name", this.call_id[1]); // 外呼拨打的客户姓名
                this.$set(this.follow_params1, "call_phone", this.call_id[2]); // 外呼拨打的客户号码
                this.$set(this.follow_params1, "call_show_phone", this.call_id[3]); // 外呼拨打的外显号码
            }
            if (this.follow_params1.content.length < 5) {
                this.$message.error("最少输入不能小于五个文字");
                this.is_loading = false;
                return;
            }
            let params = Object.assign({}, this.follow_params1)
            if (params.tracking_id == '') {
                params.tracking_id = 0
            }
            params.call_status = this.formInline.region
            if (params.call_status == '') {
                delete params.call_status
            }
            params.tel_log_id = this.tel_follow_id || 0
            // console.log(params);
            this.$http.setCrmCustomerTelFollowData(params).then(res => {
                if (res.status == 200) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    });
                    this.showViewPhone = false; // 关闭模态框
                    this.is_loading = false; // 关闭loading动画
                    this.call_id = [];
                    this.$set(this.follow_params1, "content", "");
                    this.$set(this.follow_params1, "call_phone_id", "");
                    this.$set(this.follow_params1, "call_name", "");
                    this.$set(this.follow_params1, "call_phone", "");
                    this.$set(this.follow_params1, "call_show_phone", "");
                    this.tel_follow_id = 0;
                    this.detail.tracking_id = this.follow_params1.tracking_id; // 重置赋值查看电话状态id
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 获取查看电话跟进状态
        getTelFollowStatus() {
            this.$http
            .getCrmCustomerFollowInfo({ params: this.telFollowStatus })
            .then((res) => {
            if (res.status === 200) {
                this.tel_status_list = res.data;
                let test = true;
                this.tel_status_list.map(item => {
                    if (this.detail.tracking_id == item.id) {
                        test = false;
                        this.follow_params1.tracking_id = item.id;
                    }
                    if (item.title == '有效客户') {
                        item.value_name = 1
                    } else if (item.title == '无效客户') {
                        item.value_name = 2
                    } else {
                        item.value_name = 1
                    }
                    return item
                })
                if (test && (this.detail.tracking != undefined && this.detail.tracking != null)) {
                    // 如果客户状态为他司成交
                    if (this.detail.tracking.title == "他司成交") {
                        this.follow_params1.tracking_id = this.effectiveID; // 默认选中有效客户id
                    } else if (this.detail.tracking.title == "我司成交") { // 如果客户状态为我司成交
                        this.follow_params1.tracking_id = this.effectiveID;
                        this.is_deal = false; // 控制客户状态选择
                    } else {
                        this.follow_params1.tracking_id = this.detail.tracking_id || this.detail.tracking.id; // 赋值当前的客户状态id
                    }
                }
                if (!this.follow_params1.tracking_id) {
                    this.follow_params1.tracking_id = 0;
                }
            }
            });
        },
        // 点击外呼按钮
        allStaffCallPhone() {
            this.autoPhonenNumber = JSON.parse(JSON.stringify(this.detail.mobile)); // 赋值客户真实号码
            this.CallPhoneOwner = this.showPhoneName; // 赋值客户名称
            // 拨打号码显示为隐号
            if (this.nowDialData.type == 2) {
                this.concealPhoneNumber = this.changeMobile.replace(/^(.{3}).*(.{3})$/, "$1****$2"); // 赋值客户隐藏手机号
            }
            this.showAllStaffPhone = true; // 显示智能手机模态框
        },
        closeCallPhone() {
            this.showPhone = false;
        },
        allStaffphoneClose() {
            this.showAllStaffPhone = false;
        },
        // 接收父组件传参
        getCallId(e) {
            console.log(e, "e");
            this.call_id = e;
            this.postData(this.call_id)
        },
        postData(call) {
            let params = {
                call_phone_id: call[0],
                call_name: call[1],
                call_phone: call[2],
                call_show_phone: call[3],
                type: 1,
                client_id: this.u_id
            }
            this.$http.sendPhoneTel(params).then(res => {
                console.log(res.data,"res")
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.container {
    background: #F1F4FA;
    margin: -15px;
    padding: 20px 24px;

    .header-box {
        width: 100%;
        // height: 217px;
        display: flex;
        flex-direction: row;
        background: #FFFFFF;
        padding: 25px 33px;
        box-sizing: border-box;

        .header-left {
            display: flex;
            flex-direction: column;
            width: 57%;
            height: 100%;
            border-right: 1px solid #E2E5EC;

            .header-left-avatar {
                margin-bottom: 24px;

                .avatar {
                    width: 57px;
                    height: 57px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .name-box {
                    margin-left: 15px;
                    justify-content: space-between;

                    .name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2E3C4E;
                    }

                    .label {
                        font-size: 12px;
                        padding: 2px 8px;
                        border-radius: 2px;
                        color: #8A8A8A;
                        background: #F1F4FA;
                    }
                }
            }

            .header-left-info {
                font-size: 14px;
                color: #8A8A8A;
                margin-bottom: 25px;
                position: relative;

                .interval {
                    margin-left: 24px;
                }

                .remark {
                    span {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        max-width: 450px;
                    }

                    .remark-icon {
                        width: 20px;
                        height: 20px;
                        margin-left: 10px;
                        cursor: pointer;

                        img {
                            width: 100%;
                        }
                    }

                    .remark-input {
                        position: absolute;
                        left: 365px;
                        width: 163px;
                    }
                }
            }

            .header-left-search {
                .short-select {
                    width: 163px;
                    height: 40px;
                    margin-right: 19px;
                }

                .long-select {
                    width: 298px;
                    height: 40px;

                    .el-select {
                        width: 100%;
                    }
                }
            }
        }

        .header-right {
            display: flex;
            flex-direction: column;
            width: 43%;
            height: 100%;
            padding: 30px 0 0 30px;
            box-sizing: border-box;

            .title {
                color: #414141;
                font-size: 16px;
                font-weight: bold;
            }

            .header-right-info {
                // align-items: center;
                margin-top: 30px;
                font-size: 14px;
                color: #8A929F;

                .check-phone {
                    width: 86px;
                    height: 36px;
                    background: #2D84FB;
                    color: #FFFFFF;
                    text-align: center;
                    line-height: 36px;
                    margin-left: 27px;
                    cursor: pointer;
                }

                .level {
                    height: 36px;
                    box-sizing: border-box;
                    font-size: 14px;
                    color: #fff;
                    background: linear-gradient(180deg, #f8a707, #f85d02 100%);
                    border: none;
                    padding: 8px 15px;
                    border-radius: 2px;
                    margin-left: 20px;
                    cursor: pointer;
                    font-size: 16px;
                    position: relative;
                }

                .client-phone-detail {
                    margin-left: 33px;
                    line-height: 20px;
                }
            }
        }
    }

    .main-box {
        display: flex;
        flex-direction: row;
        width: 100%;
        margin-top: 21px;

        .main-left {
            width: 57%;
        }

        .main-right {
            width: 43%;
            margin-left: 16px;
        }
    }
}

.warn-title {
    .el-tabs__nav-wrap::after {
        height: 0px;
    }

    .el-tabs__item {
        font-weight: 700;
        font-size: 18px;
    }

    .el-tabs__active-bar {
        height: 0px;
    }

    .el-icon-info {
        font-size: 1px;
        color: rgb(245, 108, 108);
        margin-left: 5px;
    }
}

.itemCenter {
    align-items: center;
    justify-content: space-between;

    // margin-top: -10px;
    .all-phone {
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.customer-status-box {
    margin-top: 20px;

    .el-radio-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .el-radio {
            margin: 0 0 10px 0;
        }

        .el-radio:last-child {
            margin: 0;
        }
    }
}

.callBtn {
    .el-button {
        padding: 4px 8px;
        // margin-left: 5px;
    }
}
</style>