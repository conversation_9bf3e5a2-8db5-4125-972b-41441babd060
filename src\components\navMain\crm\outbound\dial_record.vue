<template>
  <div class="outbount">
    <div class="outbount_search">
      <!-- tabs -->
      <dial-record-task></dial-record-task>
    </div>
  </div>
</template>

<script>
import DialRecordTask from "./components/dial_record_task.vue";
export default {
  components: { 
    DialRecordTask,
  },
  data() {
    return {
      
    };
  },
  methods: {

  },
};
</script>

<style scoped lang="scss">
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
.outbount_search {
  padding: 20px;
  background: #fff;
  .Call_record{
    font-size: 24px;
    color: #1c212a;
    margin-bottom: 20px;
  }
}
</style>
