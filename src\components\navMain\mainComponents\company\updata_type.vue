<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="() => $router.back()"
				>返回</el-button
			>
		</div> -->
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item label="户型分类" prop="category" label-width="100px">
        <el-select v-model="form.category" style="width:300px">
          <el-option
            v-for="(item, index) in type_classification_list"
            :key="index"
            :label="item.description"
            :value="+item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="户型" prop="room" label-width="100px">
        <el-input style="width:100px" v-model="form.room"
          ><template slot="append">室</template></el-input
        >
        <el-input style="width:100px" v-model="form.hall"
          ><template slot="append">厅</template></el-input
        >
        <el-input style="width:100px" v-model="form.guard"
          ><template slot="append">卫</template></el-input
        >
        <el-tooltip
          effect="dark"
          content=" 如果户型超过五室请在“户型特色”里进行说明"
          placement="right-start"
          style="font-size:18px"
          ><i class="el-icon-info"></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="面积" prop="area" label-width="100px">
        <el-input type="number" style="width:300px" v-model="form.area"
          ><template slot="append">㎡</template></el-input
        >
      </el-form-item>
      <el-form-item label="套内面积" prop="area" label-width="100px">
        <el-input type="number" style="width:300px" v-model="form.inside_area"
          ><template slot="append">㎡</template></el-input
        >
      </el-form-item>
      <el-form-item label="户型标识" prop="name" label-width="100px">
        <el-input style="width:300px" v-model="form.type_logo"></el-input>
        <el-tooltip
          effect="dark"
          style="font-size:18px"
          content=" 标识不同户型短标题如：A户型、B户型，字数限定在8个字以内"
          placement="right-start"
          ><i class="el-icon-info"></i>
        </el-tooltip>
      </el-form-item>

      <el-form-item
        label="售价"
        prop="sell_price"
        label-width="100px"
        v-if="website_info.website_mode_category !== 0"
      >
        <el-input
          placeholder="请输入价格"
          type="number"
          style="width:300px"
          v-model="form.sell_price"
          ><template slot="append">元/㎡</template></el-input
        >
      </el-form-item>
      <el-form-item
        label="销售状态"
        prop="status"
        label-width="100px"
        v-if="website_info.website_mode_category !== 0"
      >
        <el-select v-model="form.status" placeholder="请选择状态">
          <el-option
            v-for="item in status_list"
            :key="item.id"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="户型朝向"
        prop="orient_category"
        label-width="100px"
        v-if="website_info.website_mode_category !== 0"
      >
        <el-select v-model="form.orient_category" placeholder="请选择朝向">
          <el-option
            v-for="item in orient_category_list"
            :key="item.id"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="户型特色" prop="feature" label-width="100px">
        <el-input
          v-model="form.feature"
          type="textarea"
          style="width:300px"
          rows="4"
          placeholder="多个特色用，隔开"
        ></el-input>
      </el-form-item>
      <el-form-item label="户型图" label-width="100px">
        <!-- <div class="img-box">
					<img :src="form.img" alt="" />
				</div> -->
        <el-upload
          :headers="myHeader"
          :action="house_type_img"
          :on-success="handleSuccess"
          list-type="picture-card"
          :limit="1"
          :show-file-list="false"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
        >
          <img
            v-if="form.img"
            width="148px"
            height="148px"
            :src="form.img"
            alt=""
          />
          <i v-else class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item label-width="100px">
        <el-button type="primary" @click="onSubmit">立即修改</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
/* eslint-disable */
import config from "@/utils/config";
import { mapState } from "vuex";
export default {
  name: "updata_type",
  data() {
    const validateRoom = (rule, value, cb) => {
      if (
        this.form.total_room &&
        this.form.total_salloon &&
        this.form.total_washroom
      ) {
        cb();
      } else {
        cb(new Error("室厅卫不能为空"));
      }
    };
    return {
      form: {
        category: "",
        type_logo: "",
        region: "",
        room: "",
        hall: "",
        guard: "",
        area: "",
        mark_sale: "",
        feature: "",
        desc1: "",
        desc2: "",
        desc3: "",
        img: "",
        sell_price: "", // 售价
        status: "", //状态
        orient_category: "1",
        inside_area: "",
      },
      // 户型分类列表
      type_classification_list: [],
      // 上传的内容
      dialogImageUrl: "",
      dialogVisible: false,
      build_id: null,
      id: null,
      rules: {
        category: [{ required: true, trigger: "blur", message: "请选择" }],
        type_logo: [
          { required: true, trigger: "blur", message: "请输入户型标识" },
        ],
        room: [
          {
            required: true,
            trigger: "blur",
            validator: validateRoom,
            message: "请输入户型",
          },
        ],
        area: [{ required: true, trigger: "blur", message: "请输入面积" }],
        // features: [{ required: true, trigger: "blur", message: "请输入特色" }],
      },
      house_type_img: `/api/common/file/upload/enterprise?category=${config.HOUSER_TYPE_IMG}`,
      status_list: [],
      orient_category_list: [],
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("company_token"),
      };
    },
    ...mapState(["website_info"]),
  },
  mounted() {
    this.build_id = this.$route.query.build_id;
    this.id = this.$route.query.id;
    this.getlist();
  },
  methods: {
    onSubmit() {
      this.$http
        .updateCompanyHouse({
          id: this.id,
          build_id: this.build_id,
          category: this.form.category,
          name: this.form.type_logo,
          total_room: this.form.room,
          total_salloon: this.form.hall,
          total_washroom: this.form.guard,
          area: this.form.area,
          feature: this.form.feature,
          img: this.form.img,
          sell_price: this.form.sell_price,
          status: this.form.status,
          orient_category: this.form.orient_category,
          inside_area: this.form.inside_area,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.$goPath(`/company_setup_type?id=${this.build_id}`);
          }
        });
    },
    getlist() {
      this.$setDictionary((e) => {
        e.find((item) => {
          switch (item.name) {
            case "BUILD_HOUSE_TYPE_CATEGORY":
              this.type_classification_list = item.childs;
              break;
            case "BUILD_STATUS":
              this.status_list = item.childs;
              break;
            case "BUILD_HOUSE_TYPE_ORIENT_CATEGORY":
              this.orient_category_list = item.childs;
              break;
          }
        });
      });
      this.$http.queryCompanyHouse(this.id).then((res) => {
        this.form.category = +res.data.category;
        this.form.type_logo = res.data.name;
        this.form.room = res.data.total_room;
        this.form.hall = res.data.total_salloon;
        this.form.guard = res.data.total_washroom;
        this.form.area = res.data.area;
        this.form.feature = res.data.feature;
        this.form.img = res.data.img;
        this.form.sell_price = res.data.sell_price;
        this.form.status = res.data.status.toString();
        this.form.orient_category = res.data.orient_category + "";
        this.form.inside_area = res.data.inside_area;
        // this.form.img = res.data.img;
      });
    },
    // 上传文件
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response.url;
      this.dialogVisible = true;
    },
    handleSuccess(response, file, fileList) {
      this.form.img = response.url;
      console.log(this.form.img);
    },
    goBack() {
      this.$goPath(`/company_setup_type?id=${this.build_id}`);
    },
  },
};
</script>

<style lang="scss" scoped>
.title_dis {
  padding: 10px;
  font-size: 12px;
  background: #edfbf8;
  color: #999;
}
.lou-type {
  margin: 20px 0;
  .lou-type-s {
    margin-right: 12px;
    color: #606266;
    font-size: 14rpx;
  }
}
.img-box {
  width: 148px;
  height: 148px;
  margin: 10px 0;
  img {
    width: 100%;
    height: 100%;
  }
}
.el-input {
  width: 200px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-form {
// 	padding-top: 30px;
// }
</style>
