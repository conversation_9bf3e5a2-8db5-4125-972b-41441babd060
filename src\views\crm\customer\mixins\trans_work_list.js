export default {
    data(){
        return {
            table_headerliuzhuan: [{
                    prop: "id",
                    label: "ID",
                    width: "80",
                    align: "left",
                    fixed: "left"
                },{
                    prop: "work_name",
                    label: "计划名称",
                    minWidth: '200px',
                    align: "left",
                    fixed: "left"
                },
                {
                    prop: "customer_count",
                    label: "客户数量",
                    width: "110",
                    align: "center"
                },
                {
                    prop: "poin_count",
                    label: "节点数",
                    width: "100",
                    align: "center"
                },
                {
                    prop: "finish_poin_count",
                    label: "已完成节点数",
                    width: "110",
                    align: "center"
                },
                {
                    prop: "undo_poin_count",
                    label: "未完成节点数",
                    width: "110",
                    align: "center"
                },
                {
                    prop: "work_status",
                    label: "状态",
                    align: "center",
                    render: (h, data) => {
                        return (
                        <div>
                        {data.row.work_status==1 ? <el-tag size="mini" type="success">正常</el-tag> : ''}
                        {data.row.work_status==2 ? <el-tag size="mini" type="danger">禁用</el-tag> : ''}
                        </div>
                        );
                    },
                },
                {
                    prop: "operate_name",
                    label: "操作人",
                    align: "center"
                },
                {
                    prop: "point_names",
                    label: "节点名称",
                    align: "left",
                    minWidth: "150px",
                    tooltip: true
                },
                {
                    prop: "created_at",
                    label: "操作时间",
                    minWidth: "170px",
                    align: "center"
                },
                {
                    label: "操作",
                    minWidth: "150px",
                    fixed: "right",
                    render: (h, data) => {
                        let setStatusLabel = data.row.work_status == 1? "禁用" : "启用";
                        return (
                        <div>
                            <el-link type="primary" class="op-btn" onClick={()=>this.editTransWork(data.row)}>查看</el-link>
                            <el-popconfirm class="op-btn" title={"确定要"+setStatusLabel+"该计划吗？"} ononConfirm={()=>this.setTransWorkStatus(data.row)}>
                            <el-link type="warning" slot="reference">{setStatusLabel}</el-link>
                            </el-popconfirm>
                            <el-popconfirm class="op-btn" title="确定要删除该计划吗？" ononConfirm={()=>this.delTransWork(data.row)}>
                            <el-link type="danger" slot="reference">删除</el-link>
                            </el-popconfirm>
                        </div>
                        );
                    },
                },
              ],
        }
    },
    watch: {
        'dialogs.addTransTask'(val){
            if(val === false){
                if(this.$refs.addTransTask.isDataChanged){
                    this.getTransTaskList();
                }
            }
        }
    },
    mounted() {
        this.checkTransTaskAuth();
    },
    methods: {
        /**
         * 判断是否有权限
         */
        checkTransTaskAuth(){
            this.$http.checkAutoWorkAuth({autoMsg: false}).then(res => {
                if(res.status == 200){
                    this.auths.transTask = true;
                }
            });
        },
        /**
         * 获取流转计划列表
         */
        getTransTaskList(){
            this.getTransfertasklist()
        },
        /**
         * 编辑计划
         */
        async editTransWork(row){
            this.dialogs.addTransTask = true;
            await this.$nextTick();
            this.$refs.addTransTask.open(row);
        },
        /**
         * 修改状态
         * @param { Object } row 
         */
        async setTransWorkStatus(row){
            let work_status = row.work_status == 1 ? 2 : 1
            const res = await this.$http.setbatchcirculationstatus({ auto_work_id: row.id, work_status });
            if(res.status == 200){
                this.$message({
                    type: 'success',
                    message: work_status == 1 ? '启用成功' : '禁用成功'
                }); 
                this.getTransTaskList()
            }
        },
        /**
         * 删除流转计划
         * @param { Object } row 
         */
        delTransWork(row){
            this.$http.delebatchcirculation(row.id).then(res=>{
                if(res.status == 200){
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    }); 
                    this.getTransTaskList()
                }
            })
        },
    },
}