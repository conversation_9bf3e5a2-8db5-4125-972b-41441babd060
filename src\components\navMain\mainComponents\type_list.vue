<template>
  <el-main>
    <myTable :table-list="tableData" :header="table_header"></myTable>
  </el-main>
</template>

<script>
import myTable from "@/components/components/my_table";
export default {
  components: {
    myTable,
  },
  props: {
    build_id: String,
  },
  data() {
    return {
      tableData: [],
      table_header: [
        {
          label: "标题",
          render: (h, data) => {
            return (
              <div class="div row scope-box">
                <img style="width:80px" src={data.row.img} />
                <div class="left">
                  <div class="top">{data.row.name}</div>
                  <div class="bottom">
                    {data.row.total_room}室{data.row.total_sallon}厅
                    {data.row.total_washroom}卫{data.row.area}平
                  </div>
                </div>
              </div>
            );
          },
        },
        { prop: "created_at", label: "添加时间" },
        {
          label: "操作",
          width: "200",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  type="primary"
                  size="mini"
                  onClick={() => {
                    this.changeData(data.row);
                  }}
                >
                  修改
                </el-button>
                <el-button
                  type="danger"
                  size="mini"
                  onClick={() => {
                    this.deleteData(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.$nextTick(function() {
      this.getDataList();
    });
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.$http.getBuildList(this.build_id).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data;
        }
      });
    },
    // 修改操作
    changeData(row) {
      this.$goPath(`/updata_type?id=${row.id}&build_id=${row.build_id}`);
    },
    //删除操作
    deleteData(row) {
      // deleteHouse
      this.$confirm("此操作将删除该户型, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteHouse(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
