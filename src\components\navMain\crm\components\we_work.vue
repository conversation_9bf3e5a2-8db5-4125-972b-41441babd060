<template>
  <div style="margin-left: 10px">
    <div v-if="loading">
      <div class="item flex-row align-center">
        <span> 客户名称： </span>
        <span>
          {{ info.name }}
        </span>
        <span v-if="info.type == 1" style="color: #1aad19">@微信</span>
        <!-- <span v-if="info.type == 2" style="color: #ffbe3a"
        >@{{ row.corp_name }}</span
      > -->
      </div>
      <div class="item flex-row align-center">
        <span> 来源类型： </span>
        <span>
          {{ info.add_way_title }}
        </span>
      </div>
      <div class="item flex-row align-center">
        <span> 好友添加： </span>
        <span>
          {{ info && info.admin_user ? info.admin_user.user_name : "--" }}/{{
            info && info.admin_user ? info.admin_user.department_name : "--"
          }}
        </span>
      </div>
      <div class="item flex-row align-center">
        <span> 添加时间： </span>
        <span>
          {{ info.add_time }}
        </span>
      </div>
      <div class="item flex-row align-center">
        <span> 用户类型： </span>
        <span>
          {{ info.type == 1 ? "微信用户" : "企业微信用户" }}
        </span>
      </div>
      <div
        class="item flex-row"
        v-if="info.group_list && info.group_list.length"
      >
        <span style="min-width: 70px">加入的群：</span>
        <span class="flex-1">
          <el-table :data="info.group_list" border width="100%">
            <el-table-column label="群名称" v-slot="{ row }">
              {{ row.groupchat_list && row.groupchat_list.name }}
            </el-table-column>
            <el-table-column
              label="入群方式"
              prop="join_scene"
            ></el-table-column>
            <el-table-column
              label="加入时间"
              prop="join_time"
            ></el-table-column>
          </el-table>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {

  props: {
    info_id: {
      type: [String, Number],
      default: ''
    },
    status:{
      type: [String, Number],
      default:''
    },
  },
  data() {
    return {
      info: {},
      loading: false
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      let wxapi = "getQwInfo"
      if(this.status&&this.status==1){
        wxapi = 'informationgetQwInfo'
      }
      this.$http[wxapi](this.info_id).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.info = res.data
        }
        this.loading = true
      })
    }
  },
};
</script>

<style scoped lang="scss">
.item {
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}
</style>
