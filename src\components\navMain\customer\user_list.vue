<template>
  <div class="list">
    <el-container>
      <el-header class="div row">
        <myTopTips title="用户列表" :number="tableData.length">
          <div class="add-build" v-if="company_id">
            <el-button type="primary" @click="submitData">确认添加</el-button>
          </div>
        </myTopTips>

        <div class="div row">
          <el-select v-model="params.category" placeholder="请选择用户类型" style="width: 150px; margin-right: 20px" clearable
            @change="changeCategory">
            <el-option label="经纪人" value="1"></el-option>
            <el-option label="项目助理" value="3"></el-option>
          </el-select>
          <el-select v-model="is_auth_wx" placeholder="请选择微信/小程序" style="width: 170px; margin-right: 20px" clearable
            @change="changeauthwx">
            <el-option v-for="item,index in authwxdata" :key="index" :label="item.name " :value="item.value"></el-option>
          </el-select>
          <el-input style="width: 450px" v-model="inputVal" :placeholder="selectVal == 1
                      ? '请输入用户名'
                      : selectVal == 2
                        ? '请输入联系方式'
                        : '请输入销售公司'
                      " @input="onInput" @change="onChange">
            <el-select v-model="selectVal" slot="prepend" placeholder="请选择" style="width: 110px" @change="changeSelect">
              <el-option label="用户名" value="1"></el-option>
              <el-option label="联系方式" value="2"></el-option>
              <el-option label="销售公司" value="3"></el-option>
            </el-select>
            <el-button slot="append" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          </el-input>
          <el-button type="primary" style="border-radius: 4px;margin-left:10px;" @click="userimport">导出</el-button>
        </div>
      </el-header>
      <div class="browse-table">
        <div class="browse div row">
          <div class="browse-item" v-for="(item, index) in time_array" :key="index"
            :class="{ browse_active: item.value === list_params.date_str }" @click="onClickBrowse(index, item.id)">
            {{ item.desc }}
          </div>
        </div>
        <div class="block-time div row" v-if="isCustomize">
          <el-date-picker v-model="list_params.start" type="date" placeholder="请选择开始日期" value-format="yyyy-MM-dd">
            >
          </el-date-picker>
          <el-date-picker v-model="list_params.end" type="date" value-format="yyyy-MM-dd" placeholder="请选择结束日期">
            >
          </el-date-picker>
          <el-button type="primary" @click="clickTime">查询</el-button>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content bg-purple div row">
              <div class="left">
                <p>用户注册总数</p>
                <p class="desc">注册用户总数量（名）</p>
              </div>
              <div class="right">{{ user_info.total }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple div row">
              <div class="left">
                <p>新增总数</p>
                <p class="desc">日期时间内增加用户总数（名）</p>
              </div>
              <div class="right">{{ user_info.new_user_total }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple div row">
              <div class="left">
                <p>用户资金总数</p>
                <p class="desc">用户资金总金额（元）</p>
              </div>
              <div class="right">
                {{ user_info.brokerage_balance_amount }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <myTable v-loading="is_table_loading" :select="true" :table-list="tableData" :header="table_header"
        @selection-change="handleSelectionChange"></myTable>
      <el-footer>
        <!-- 分页 -->
        <div class="pagination-box">
          <myPagination :total="params.total" :currentPage="params.currentPage" :pagesize="params.per_page"
            @handleCurrentChange="handleCurrentChange" @handleSizeChange="handleSizeChange"></myPagination>
        </div>
      </el-footer>
    </el-container>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="60%">
      <el-form label-width="100px" :model="form" style="auto">
        <el-form-item label="用户状态">
          <el-radio-group v-model="form.user_status">
            <el-radio v-for="item in user_list" :key="item.id" :label="item.value">{{ item.description }}</el-radio>
          </el-radio-group>
          <el-button style="margin-left: 30px; border-radius: 4px" type="primary" @click="onUser">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="绑定项目" :visible.sync="show_bind_object" width="60%">
      <el-form label-width="100px" :model="user_form" style="auto">
        <el-form-item label="绑定方式">
          <el-radio-group v-model="user_form.type" @change="radioChange">
            <el-radio v-for="item in [
                          {
                            id: 1,
                            value: '自定义添加',
                          },
                          {
                            id: 2,
                            value: '导入其他助理项目',
                          },
                        ]" :key="item.id" :label="item.id" :border="true">{{ item.value }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择用户" v-if="user_form.type == 2">
          <el-select v-model="user_id" filterable remote placeholder="检索姓名/手机号" :remote-method="getUserList"
            @change="getUserProject">
            <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="绑定项目：" v-if="(user_form.type == 2 && user_id) || user_form.type == 1">
          <el-select v-model="user_form.ids" filterable multiple value-key="name" remote placeholder="请输入项目名称"
            :remote-method="getCommonProjectList">
            <el-option v-for="item in project_list" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="show_bind_object = false">取 消</el-button>
        <el-button type="primary" @click="submitBindObject">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="show_add_member" width="400px" title="选择绑定后台成员" append-to-body>
      <div class="member">
        <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
          :defaultExpandAll="false" ref="memberList">
        </multipleTree>
        <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false">取消</el-button>
          <el-button type="primary" @click="selectMemberOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import multipleTree from "@/components/navMain/crm/components/my_singleTree.vue";
import { mapState } from "vuex";
export default {
  name: "user_list",
  components: {
    myPagination,
    myTable,
    multipleTree
  },
  data() {
    return {
      input: "",
      params: {
        page: 1,
        per_page: 10,
        name: "",
        phone: "",
        category: "",
        company: "",
        date_str:"day",
      },
      form: {
        id: "",
        audit_status: "",
        user_status: "",
      },
      purchase: {
        id: '',
        opt: '',
      },
      audit_list: [],
      user_list: [],
      categroy_list: [],
      tableData: [],
      multipleSelection: [],
      dialogVisible: false,
      cur_user_idx: 0,
      company_id: 0,
      inputVal: "",
      selectVal: "2",
      time_array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
        { desc: "自定义", value: "customize", id: 8 },
      ],
      list_params: {
        date_str: "day",
        start: "",
        end: "",
      },
      is_auth_wx:"",
      authwxdata:[
        {name:"授权微信",value:"1_1"},
        {name:"未授权微信",value:"1_2"},
        {name:"授权小程序",value:"2_1"},
        {name:"未授权小程序",value:"2_2"}
      ],
      isCustomize: false,
      user_info: {},
      // time_info: {
      //   start: "",
      //   end: "",
      // },
      table_header: [
        { prop: "id", label: "ID", width: "80" },
        {
          prop: "category",
          width: "225",
          label: "用户",
          formatter: (row) => {
            for (var i in this.categroy_list) {
              if (parseInt(this.categroy_list[i].value) === row.category) {
                return this.categroy_list[i].description;
              }
            }
          },
          render: (h, data) => {
            return (
              <div style="position: relative;">
                <img style="height:40px;width:40px;float:left;border-radius:50%;margin-top:10px" src={data.row.avatar ? data.row.avatar : "https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80"} />
                <div>
                  <p>
                    {data.row.name ? `${data.row.name}` : "--"}
                    {data.row.nickname ? "（" + data.row.nickname + "）" : ""}
                  </p>
                  <div class="company-user">
                    <p>
                      {data.row.company_name ? (
                        <p style="margin-left:10px" domPropsInnerHTML={data.row.company_name}></p>
                      ) : (
                        <p style="color:#999">暂无公司</p>
                      )}
                    </p>
                    <p style="margin-left:10px">
                      {this.formatCategory(data.row)}
                    </p>
                  </div>
                  <p class="company-user">
                    {data.row.is_case === 1 ? (
                      <p>
                        <el-tag size="mini">
                          {this.website_info.website_mode_category == 0
                            ? "案场方"
                            : "置业顾问"}
                        </el-tag>
                      </p>
                    ) : (
                      <p></p>
                    )}
                    {data.row.is_searcher === 1 ? (
                      <p>
                        <el-tag size="mini">资料查询员</el-tag>
                      </p>
                    ) : (
                      <p></p>
                    )}
                    {data.row.is_channel_manager === 1 ? (
                      <p>
                        <el-tag size="mini">渠道经理</el-tag>
                      </p>
                    ) : (
                      <p></p>
                    )}
                  </p>
                </div>
              </div>
            );
          },
        },
        {
          label: "佣金余额/元",
          render: (h, data) => {
            return (
              <el-tag
                size="mini"
                type={
                  data.row.brokerage_balance_amount === "0.00"
                    ? "danger"
                    : "success"
                }
                domPropsInnerHTML={data.row.brokerage_balance_amount}
              ></el-tag>
            );
          },
        },
        {
          label: "小程序授权登录",
          render: (h, data) => {
            return data.row.wx_mp_open_id ? (
              <el-tag type="success">已授权</el-tag>
            ) : (
              <el-tag type="danger">未授权</el-tag>
            );
          },
        },
        {
          label: "是否绑定微信",
          render: (h, data) => {
            return data.row.wx_open_id ? (
              <el-tag type="success">已绑定</el-tag>
            ) : (
              <el-tag type="danger">未绑定</el-tag>
            );
          },
        },
        { prop: "created_at", label: "注册时间" },
        {
          label: "联系方式",
          render: (h, data) => {
            return (
              <div>
                {data.row.phone ? (
                  <p domPropsInnerHTML={data.row.phone}></p>
                ) : (
                  <p style="color:#999">暂未填写联系方式</p>
                )}
              </div>
            );
          },
        },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-dropdown style="margin-right:10px">
                  <el-button type="primary" size="mini" icon="el-icon-s-tools" style="width:97px;border-radius:5px">
                    角色设置
                  </el-button>
                  {
                    <el-dropdown-menu slot="dropdown">

                      <el-dropdown-item
                        icon="el-icon-edit"
                        nativeOnClick={() => {
                          this.editManager(data.row, 1);
                        }}
                      >
                        {this.website_info.website_mode_category == 0 ? "设为案场方"
                          : this.website_info.website_mode_category == 3 && data.row.is_build_consultant == 0 ? "设为楼盘置业顾问"
                            : this.website_info.website_mode_category == 3 && data.row.is_build_consultant == 1 ? "取消为楼盘置业顾问"
                              : ""}
                      </el-dropdown-item>
                      {this.website_info.website_mode_category !== 3 ? (
                        <el-dropdown-item
                          icon="el-icon-edit"
                          nativeOnClick={() => {
                            this.editManager(data.row, 2);
                          }}
                        >
                          设为项目助理
                        </el-dropdown-item>
                      ) : (
                        ""
                      )}
                    </el-dropdown-menu>
                  }
                </el-dropdown>
                {this.$hasShow("修改用户") ? (
                  <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="mini"
                    style="width:97px;border-radius:5px"
                    onClick={() => {
                      this.editUser(data.row);
                    }}
                  >
                    修改用户
                  </el-button>
                ) : (
                  ""
                )}
                {data.row.category === 3 ? (
                  <el-button
                    type="primary"
                    size="mini"
                    style="width:97px;border-radius:5px;margin-top:10px"
                    onClick={() => {
                      this.bindObject(data.row);
                    }}
                  >
                    绑定项目
                  </el-button>
                ) : ''}
                {data.row.user_status === 0 ? (
                  <el-button
                    type="danger"
                    size="mini"
                    style="width:97px;border-radius:5px;margin-top:10px"
                    onClick={() => {
                      this.onUser(data.row);
                    }}
                  >
                    拉黑
                  </el-button>
                ) : (
                  <el-button
                    type="primary"
                    size="mini"
                    onClick={() => {
                      this.onUser(data.row);
                    }}
                  >
                    恢复
                  </el-button>
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
      show_bind_object: false,
      user_id: '',
      userList: [],
      user_form: {
        ids: [],
        type: 1,
        user_id: ''
      },
      project_list: [],
      show_add_member: false,
      serverData: [],
      selectedIds: [],
      datalist: [],
      set_neibu_form: {
        admin_id: "",
        user_id: ""
      },
      // website_info: {
      //   website_mode_category : 0
      // }
    };
  },
  created() {
    this.company_id = this.$route.query.company_id;
    // <el-dropdown>
    //   <el-button
    //     icon="el-icon-delete-solid"
    //     size="mini"
    //     type="danger"
    //     style="margin:04px"
    //   >
    //     删除操作
    //   </el-button>
    //   <el-dropdown-menu slot="dropdown">
    //     <el-dropdown-item
    //       nativeOnClick={() => {
    //         this.deleteReportInfo(data.row);
    //       }}
    //     >
    //       删除报备信息
    //     </el-dropdown-item>
    //     <el-dropdown-item
    //       nativeOnClick={() => {
    //         this.deleteUserInfo(data.row);
    //       }}
    //     >
    //       仅删除账号
    //     </el-dropdown-item>
    //   </el-dropdown-menu>
    // </el-dropdown>
  },
  mounted() {
    console.log(this.website_info.website_mode_category)
    this.getDataList();
    this.getUserInfo();
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "SALE_ORDER_REFUND_AUDIT_STATUS":
            this.audit_list = item.childs;
            break;
          case "USER_STATUS":
            this.user_list = item.childs;
            break;
          case "USER_CATEGORY":
            this.categroy_list = item.childs;
            break;
        }
      });
    });
  },
  computed: {
    ...mapState(["website_info"]),
  },
  methods: {
    radioChange() {
      this.user_form.ids = []
      if (this.user_form.type == 1) {
        // 获取当前用户的项目列表
        this.getCurrentUserInfo(this.currentUser.id)
      }
    },
    async getMemberList() {
      await this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key+=父级
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = parseInt(this.datalist[i].id += this.datalist[i].Parent + '');
              }
            }
          }
          // console.log(this.datalist);
        }
      })
      this.getCommonSettingRoles();
    },
    getCommonSettingRoles() {
      this.$http.getCommonSettingRolesConf(1).then((res) => {
        this.is_show = false;
        if (res.status === 200) {
          // this.form_info.personnel_auth = res.data.personnel_auth.split(",").map(item => Number(item))
          this.form_list = res.data.map((item) => {
            if (
              item.value &&
              (item.type == 3 || item.key == "default_department")
            ) {
              item.value = item.value.split(",").map((i) => Number(i));
              // =====
              let i = 0;
              if (item.value != [] && item.value != undefined && item.key != "default_department") {
                item.value.map((arr) => {
                  this.$nextTick(() => {
                    this.datalist.map((list) => {
                      if (arr != list.id) {
                        i++;
                        if (i == this.datalist.length) {
                          item.value.splice(item.value.indexOf(arr), 1);
                          // if(item.key == 'finance_auth') {
                          //   console.log(item.value,"观察");
                          // }
                        }
                      }
                    })
                    i = 0;
                  })
                })
              }
              // =====
            }
            // if(item.value && item.key == 'personnel_auth') {
            //   this.selectedIds = item.value;
            // } else if(item.value && item.key == 'deal_auth') {
            //   this.selectedDeal = item.value;
            // }
            return item;
          });
        }
      });
    },

    recursionData(data) {
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    getUserList(e) {
      if (!e) {
        return
      }
      this.$http.getProjectMember({ keywords: e }).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.userList = res.data
        }
      })

    },
    selectMemberOk() {
      this.setneibuchegnyuan()
      // console.log(this.selectedIds,"已选中");
      // this.form_list.map((item) => {
      //   if (this.storageKey == item.key) {
      //     item.value = this.selectedIds;
      //   }
      // })
    },
    setneibuchegnyuan() {
      console.log(this.selectedIds);
      if (!this.selectedIds.length) {
        this.$message.warning("请选择管理员")
        return
      }
      this.set_neibu_form.admin_id = this.selectedIds.join(",")
      this.$http.setNeibuChengyuan(this.set_neibu_form).then(res => {
        if (res.status == 200) {
          this.show_add_member = false
          this.$message.success("操作成功")
          this.getDataList()
          this.getMemberList()
        }
      })
    },
    cancelNeibuChengyuan(row) {
      this.$confirm('确定此操作吗').then(() => {
        this.$http.cancelNeibuChengyuan(row.id).then(res => {
          if (res.status == 200) {
            this.$message.success("操作成功")
            this.getDataList()
            this.getMemberList()
          }
        })
      })

    },
    editNeibuManager(row) {
      this.set_neibu_form.user_id = row.id
      if (row.is_inner_user == 1) {
        this.cancelNeibuChengyuan(row)
      } else {
        this.selectedIds = []
        this.getMemberList()
        this.showMemberList()
      }
    },
    showMemberList() {
      setTimeout(() => {
        this.show_add_member = true
      }, 500);

    },
    getUserProject() {
      this.getCurrentUserInfo(this.user_id)
    },

    getCommonProjectList(build_name) {
      this.build_loading = true;
      this.$http
        .getCommonProjectList({ params: { name: build_name } })
        .then((res) => {
          if (res.status === 200) {
            this.project_list = res.data;
          }
        });
    },
    submitBindObject() {
      let user_form = Object.assign({}, this.user_form)
      user_form.ids = user_form.ids.join(",")
      user_form.user_id = this.currentUser.id
      this.$http.submitBindObject(user_form).then(res => {
        if (res.status == 200) {
          this.$message.success("操作成功")
          this.getDataList();
          this.show_bind_object = false
        }
      })
    },
    getCurrentUserInfo(id) {
      this.$http.queryUserInfo(id).then((res) => {
        if (res.status === 200) {
          // this.project_list = res.data.projects
          this.user_form.ids = res.data.projects.map((item) => {
            return item.id;
          });
        }
      });
    },
    selecetedMember(e) {
      this.selectedIds = e.checkedKeys;
    },

    // 获取列表数据
    getDataList() {
      this.is_table_loading = true;
      if (this.company_id) {
        this.$http
          .searchUserListByCompany(
            this.params.page,
            0,
            this.params.name,
            this.params.phone
          )
          .then((res) => {
            this.is_table_loading = false;
            if (res.status === 200) {
              this.tableData = res.data.data;
              // this.params.currentPage = res.data.current_page;
              this.params.total = res.data.total;
              // this.params.row = res.data.per_page;
            }
          });
        return;
      }
      if (!this.params.category) {
        delete this.params.category;
      }
      this.$http.showUserList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    async bindObject(row) {
      console.log(row);
      this.currentUser = row
      if (this.user_form.type == 1) {
        this.getCurrentUserInfo(this.currentUser.id)
      }
      this.getCommonProjectList()

      this.show_bind_object = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
      this.getDataList();
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.params.current_page = 1;
      this.getPageData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
    },
    // // 搜索用户
    search() {
      this.params.page = 1;
      this.getDataList();
      // this.$http.searchUserByPhone(this.params.phone).then((res) => {
      //   if (res.status === 200) {
      //     this.tableData = res.data.data;
      //     this.params.currentPage = res.data.current_page;
      //     this.params.total = res.data.total;
      //     this.params.row = res.data.per_page;
      //   }
      // });
    },
    //导出
    userimport(){
      this.$confirm('确定要导出吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.newhouseuserimport(this.params).then(res=>{
            if(res.status==200){
              window.open(res.data)
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });          
        });
    },
    onChange() {    
      switch (this.selectVal) { 
        case "1":
          this.params.name = this.inputVal;
          break;
        case "2":
          this.params.phone = this.inputVal;
          break;
        case "3":
          this.params.company = this.inputVal;
          break;
      }
    },
    onInput(e) {
      if (!e) {
        this.params = {
          page: 1,
          name: "",
          phone: "",
          company: "",
        };
        this.getDataList();
      }
    },
    changeSelect(e) {
      if (e) {
        this.inputVal = "";
        this.params = {
          name: "",
          phone: "",
          company: "",
        };
      }
    },
    changeCategory(e) {
      this.params.category = e;
      this.search();
    },
    changeauthwx(e){
      if (e) {
          // 使用正则表达式提取下划线后的数字
          const match = e.match(/_(\d+)$/);
          if (match) {
            const value = match[1]; // 提取下划线后的数字部分
            if (e.startsWith('1_')) {
              this.params.is_auth_wx = value;
              delete this.params.is_auth_wx_mp
            } else if (e.startsWith('2_')) {
              delete this.params.is_auth_wx
              this.params.is_auth_wx_mp = value;
            }
          }
      }else{
        this.is_auth_wx = ""//搜索框赋空
       delete this.params.is_auth_wx
       delete this.params.is_auth_wx_mp
      }
      this.getDataList()
    },
    // 搜索下拉
    handleCommand(command) {
      this.$message({ message: command });
    },
    formatAudit(row) {
      for (var i in this.audit_list) {
        if (parseInt(this.audit_list[i].value) === row.audit_status) {
          return this.audit_list[i].description;
        }
      }
    },
    formatUser(row) {
      for (var i in this.user_list) {
        if (parseInt(this.user_list[i].value) === row.user_status) {
          return this.user_list[i].description;
        }
      }
    },
    formatCategory(row) {
      for (var i in this.categroy_list) {
        if (parseInt(this.categroy_list[i].value) === row.category) {
          return this.categroy_list[i].description;
        }
      }
    },
    editData(row) {
      this.form.id = row.id;
      this.dialogVisible = true;
    },
    editManager(row, type) {
      console.log(row.is_build_consultant, '%%%%%%%%');
      this.$confirm(
        type == 1 ? (this.website_info.website_mode_category == 0 ? "是否设为设为案场方" : this.website_info.website_mode_category == 3 && row.is_build_consultant == 0 ? "是否设为楼盘置业顾问" : "是否取消楼盘置业顾问") : "是否设为项目助理？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          // if (row.is_case == 1 && type == 1) {
          //   this.$message.error("该用户已经是置业顾问了");
          //   return;
          // }
          let storeidlist = row.company_store_manager_user_id.split(",");
          storeidlist.map((item) => {
            if (item == row.id && type == 2) {
              this.$message.error("店长不可设置项目助理");
              return;
            }
          });
          if (type == 2) {
            let form = {
              id: row.id,
              category: 3,
            };
            this.setUserCategory(form);
          }
          if (type == 1 && row.is_build_consultant == 0) {
            console.log(this.purchase, "设置楼盘置业顾问")
            this.purchase.id = row.id
            this.purchase.opt = 'add'
            this.purchas()
            // let form = {
            //   id: row.id,
            //   is_case: 1,
            // };
            // this.$http.setIsCaseForUser(form).then((res) => {
            //   if (res.status === 200) {
            //     this.$message.success("操作成功");
            //   }
            // });

          } else if (type == 1 && row.is_build_consultant == 1) {
            this.purchase.id = row.id
            this.purchase.opt = 'cancel'
            this.purchas()
          }
        })
        .catch(() => { });
    },
    purchas() {
      this.$http.setestatepurchase(this.purchase).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList()
        }
      })
    },
    setUserCategory(form) {
      this.$http.setUserCategory(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
        }
      });
    },

    onUser(row) {
      // 拉黑经纪人操作 （是否可以报备客户）
      this.$http
        .editUserStatus({
          id: row.id,
          user_status: row.user_status === 0 ? 2 : 0,
        })
        .then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
          }
          this.getDataList();
        });
      // this.$http
      //   .editUserStatus({
      //     id: this.form.id,
      //     user_status: this.form.user_status,
      //   })
      //   .then((res) => {
      //     if (res.status === 200) {
      //       this.$message({
      //         message: "修改成功",
      //         type: "success",
      //       });
      //     }
      //     this.getDataList();
      //   });
    },
    editUser(row) {
      this.$goPath(`/edit_user?id=${row.id}`);
    },
    goBack() {
      this.$router.back();
    },
    submitData() {
      if (this.multipleSelection.length == 0) {
        this.$message.error("请选择数据以后进行添加");
        return;
      }
      let ids = this.multipleSelection.map((item) => {
        return item.id;
      });
      this.$http
        .addCompanyUsers({ ids: ids.join(","), company_id: this.company_id })
        .then((res) => {
          if (res.status === 200) {
            setTimeout(() => {
              this.cur_user_idx++;
            });
            this.$message({
              message: "添加成功",
              type: "success",
            });
            this.goBack();
          }
        });
    },
    // 点击选择日期数据
    onClickBrowse(index, id) {
      this.list_params.date_str = this.time_array[index].value;
      this.params.date_str  = this.time_array[index].value;
      if (id === 8) {
        this.isCustomize = true;
      } else {
        this.isCustomize = false;
        this.list_params.start = "";
        this.list_params.end = "";
        delete this.params.date_start
        delete this.params.date_end
        this.getUserInfo();
        this.getDataList()
      }
    },
    getUserInfo() {
      this.$http.getUserData(this.list_params).then((res) => {
        if (res.status === 200) {
          this.user_info = res.data;
        }
      });
    },
    clickTime() {
      if (!this.list_params.start) {
        this.$message({
          message: "请选择开始时间",
          type: "error",
        });
      } else if (!this.list_params.end) {
        this.$message({
          message: "请选择结束时间",
          type: "error",
        });
      } else {
        delete this.params.date_str
        this.params.date_start = this.list_params.start
        this.params.date_end = this.list_params.end
        this.getUserInfo();
        this.getDataList()
      }
    },
    deleteReportInfo(row) {
      this.$confirm(
        "确定删除其名下的所有报备？该操作不可逆，请谨慎操作！",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$http.deleteReportInfo(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          console.log("取消");
        });
    },
    deleteUserInfo(row) {
      this.$confirm(
        "此操作仅删除用户账号，如需删除其下的报备信息请选择“删除报备信息”，是否继续？",
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$http.deleteUserInfo(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          console.log("取消");
        });
    },
  },
  activated() {
    console.log(1231232);
    if (this.$store.state.reloadCon) {
      this.getDataList()
      this.$store.state.reloadCon = false
    }
  },
};
</script>
<style lang="scss">
.company-user {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style scoped lang="scss">
// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;

  .browse {
    width: 480px;

    .browse-item {
      margin: 0 5px;
      font-size: 14px;
      padding: 2px 10px;
      border-radius: 50px;
      color: #333;

      &.browse_active {
        color: #fff;
        background: #0068e6;
      }
    }
  }

  .block-time {
    justify-content: flex-start;
    margin: 10px;
  }

  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col {
    border-radius: 4px;
  }

  .bg-purple {
    border: 1px dashed #d3dce6;
  }

  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;

    .left {
      color: #999;
      font-size: 14px;
      text-align: start;

      p {
        color: #333;
      }

      .desc {
        color: #999;
      }
    }

    .right {
      color: #26bf8c;
    }
  }
}

.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}

.el-button {
  border: none;
  border-radius: 10px;
}

.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}

.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;

  .add-build {
    margin-left: 10px;

    .el-button {
      border-radius: 4px;
    }
  }
}

.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}

.scope-box {
  height: 40px;
  width: 40px;

  img {
    width: 100%;
  }
}

.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}

// .el-main {
// 	padding-top: 40px;
// }
.header-top {
  padding: 60px 20px 0;
}
</style>
