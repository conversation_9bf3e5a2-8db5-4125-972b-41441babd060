/* eslint-disable */
import Vue from "vue";
import Vuex from "vuex";
import http from "../utils/http";
import { toTree } from "../utils/tools";
Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    openedTab: ["index"],
    activeTab: "",
    token: "",
    userinfo: {
      username: "",
      password: "",
    },
    city_list: [],
    userInfo: localStorage.getItem("TOKEN") || "", //获取localstorage存储的登录信息。或者是空值
    website_id: 0,
    // 获取权限
    perms: [],
    slideNavs: [],
    roles: [],
    // 角色集合
    perms_list: [],
    options_tabs: [],
    activeIndex: "",
    website_info: {}, // 系统信息
    dictionary_list: [], // 字典数据
    is_qywx_expire: false, // 企业微信是否到期
    // 顶级菜单
    top_slide_menu: "",
    myRoleInfo: null,   //保存当前登录成员权限信息
    myInfo: null,       //保存当前登录成员信息
    informationdata: {},
    crmConfigProjectType: 0,   //CRM项目选项类型  1自建项目  2房源小区库
    customerDetailChangeParams: [],   //客户详情切换参数
    multipleSelection: [],//选中的客户id
    ismanager:"",//是否是管理员

    // 挂载分享
    // editLeafletList: [],//编辑数据列表
    // caRecordList: [],//获客记录数据列表
    // editLeafletData: [],//编辑数据
    // caRecordData: [],//获客记录数据
  },
  mutations: {
    // // 管理编辑数据
    // setEditLeafletList(state, data) {
    //   const isLeaflet = state.editLeafletList.some((item) => item.id == data.id)
    //   if (isLeaflet) return
    //   state.editLeafletList.push(data)
    // },
    // // 管理获客数据
    // setCaRecordList(state, data) {
    //   const isCaRecord = state.caRecordList.some((item) => item.id == data.id)
    //   if (isCaRecord) return
    //   state.caRecordList.push(data)
    // },
    // filterLeafletData(state, id) {
    //   const item = state.editLeafletList.find((item) => item.id == id)
    //   this.editLeafletData = item
    // },
    // filtercaRecordData(state, id) {
    //   const item = state.caRecordList.find((item) => item.id == id)
    //   this.caRecordData = item
    // },


    addTab(state, { componentName }) {
      console.log(state.openedTab, 123)
      state.openedTab.push(componentName);
    },
    changeTab(state, componentName) {
      state.activeTab = componentName;
    },
    titleTab(state, titleTab) {
      state.titleTab = titleTab;
    },
    deductTab(state, componentName) {
      let index = state.openedTab.indexOf(componentName);
      state.openedTab.splice(index, 1);
    },
    // 登录
    setToken(state, payload) {
      state.token = payload;
    },
    removeUserInfo(state, payload) {
      state.token = "";
      state.userinfo = {};
    },
    login(state, v) {
      localStorage.setItem("TOKEN", v); //将传递的数据先保存到localStorage中
      state.userInfo = v;
    },
    setSiteID(state, payload) {
      state.website_id = payload;
    },
    setUserBtn(state, payload) {
      state.perms = payload;
    },
    setSlideNavs(state, payload) {
      state.slideNavs = payload;
    },
    setRoles(state, payload) {
      state.roles = payload;
      // let user_roles = []
      // if (payload && payload.roles && payload.roles.length) {
      //   user_roles = payload.roles.map(item => item.name)
      //   localStorage.setItem("user_roles", JSON.stringify(user_roles))
      // }

    },
    setPermsList(state, payload) {
      state.perms_list = payload;
    },
    add_tabs(state, payload) {
      state.options_tabs.push(payload);
    },
    delete_tabs(state, route) {
      let index = 0;
      for (let option of state.options_tabs) {
        if (option.route === route) {
          break;
        }
        index++;
      }
      if (state.options_tabs[index]) {
        state.options_tabs[index].keepalive = false;
      }
      state.options_tabs.splice(index, 1);
    },
    // 设置当前激活的tab
    set_active_index(state, [name, replaceRoute]) {
      //替换tab
      if (replaceRoute) {
        let index = state.options_tabs.findIndex(e => e.route === name),
          oldIndex = state.options_tabs.findIndex(e => e.route === replaceRoute);
        if (oldIndex !== -1) {
          oldIndex > index && oldIndex--;
          state.options_tabs.splice(oldIndex, 1, state.options_tabs.splice(index, 1)[0]);
        }
      }

      state.activeIndex = name;

    },
    setWebsiteInfo(state, payload) {
      state.website_info = payload;
    },
    setDictionary_list(state, payload) {
      state.dictionary_list = payload;
    },
    // 设置系统是否到期
    setExprie(state, payload) {
      state.is_qywx_expire = payload;
    },
    setSlideTopMenu(state, payload) {
      state.top_slide_menu = payload;
    },
    //设置当前登录成员信息
    setMyInfo(state, payload) {
      state.myInfo = payload;
    },
    setinformationdata(state, payload) {
      state.informationdata = payload;
      localStorage.setItem('informationdata', JSON.stringify(payload)); // 将数据存储到localStorage中
    },
    setmultipleSelection(state, payload) {
      state.multipleSelection = payload;
      localStorage.setItem('multipleSelection', JSON.stringify(payload)); // 将数据存储到localStorage中
    },
    setismanager(state, payload) {
      state.ismanager = payload;
      localStorage.setItem('ismanager', JSON.stringify(payload)); // 将数据存储到localStorage中
    },
    //设置当前crm开启的项目类型
    setCrmConfigProjectType(state, payload) {
      state.crmConfigProjectType = payload;
    },
    setCustomerDetailChangeParams(state, [id, params]) {
      let changes = state.customerDetailChangeParams;
      const index = changes.findIndex(e => e.id == id);
      if (params) {
        if (index === -1) {
          changes.push({ id, params });
        } else {
          changes.splice(index, 1, { id, params })
        }
      } else {
        if (index !== -1) {
          changes.splice(index, 1);
        }
      }
    },
  },
  actions: {
    getSiteID(ctx, options) {
      // ctx.commit("setSiteID", this.$GetQueryString("website_id"));
    },
    getSlideNavs(ctx) {
      http.getPermissionList().then((res) => {
        if (res.status === 200) {
          ctx.commit(
            "setPermsList",
            res.data.filter((item) => {
              return item.menu === 1;
            })
          );
          ctx.commit(
            "setSlideNavs",
            toTree(
              res.data.map((item) => {
                item.extend = JSON.parse(item.extend);
                return item;
              })
            )
          );
          let menu0 = res.data.filter((item) => {
            return item.menu === 0;
          });
          ctx.commit(
            "setUserBtn",
            menu0.map((item) => {
              return item.extend.name;
            })
          );
        }
      });
    },
    getRoles(ctx) {
      http.getAdminRoles(localStorage.getItem("admin_id")).then((res) => {
        if (res.status === 200) {
          ctx.commit("setRoles", res.data);
        }
      });
    },
    getTopSlideTopMenu(ctx, callback) {
      http.getPermissionSlideList().then((res) => {
        if (res.status === 200) {
          ctx.commit("setSlideTopMenu", res.data);
          callback(res.data);
        }
      });
    },
    //获取当前成员操作权限
    async queryMyRoleInfo(ctx) {
      const res = await http.getMyRoleInfo();
      if (res.status == 200) {
        return res.data;
      }
    },
    //获取当前成员信息
    async queryMyInfo(ctx, cb) {
      if (!ctx.state.myInfo) {
        const res = await http.getAdmin();
        if (res.status == 200) {
          ctx.commit('setMyInfo', res.data);
        }
      }
      cb && cb(ctx.state.myInfo);
      return ctx.state.myInfo;
    },
    //获取当前开启的crm项目配置
    async queryCrmConfigProjectType(ctx) {
      if (!ctx.state.crmConfigProjectType) {
        const res = await http.getCrmConfigProjectType();
        if (res.status == 200) {
          ctx.commit('setCrmConfigProjectType', res.data);
        }
      }
    },

  },
});
