<template>
  <!-- 审批 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <myCheck
                :type="time_list"
                label="name"
                value="id"
                :is_tabs="is_tabs"
                @onClick="onClickTabs"
              ></myCheck>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
          >
            <el-table-column prop="id" width="80" label="ID"></el-table-column>
            <el-table-column label="申请人" prop="admin.user_name">
            </el-table-column>
            <el-table-column label="客户姓名" prop="name"> </el-table-column>
            <el-table-column label="客户联系方式" prop="mobile">
            </el-table-column>
            <el-table-column label="审批金额/元" prop="money">
            </el-table-column>
            <el-table-column label="事项" v-slot="{ row }">
              {{ row.remarks || "--" }} </el-table-column
            ><el-table-column label="审批类型" v-slot="{ row }">
              {{ row.type == 1 ? "客源审批" : "房源审批" }}
            </el-table-column>
            <!-- <el-table-column label="补充内容" prop="content"> </el-table-column> -->
            <el-table-column
              label="申请时间"
              prop="created_at"
            ></el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }">
              <!-- <el-link type="primary" @click="onClickDialogAgree(row)"
                >同意</el-link
              > -->
              <el-link
                v-if="row.status == 0"
                type="primary"
                @click="onClickDialogDetail(row)"
                >详情</el-link
              >
              <!-- <el-link
                style="margin-left:24px"
                type="primary"
                @click="onClickDialog(row)"
                >拒绝</el-link
              > -->
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog width="374px" :visible.sync="is_dialog_content" title="拒绝理由">
      <div class="title-lable">内容填写：</div>
      <el-input
        type="textarea"
        :rows="10"
        placeholder="请输入"
        v-model="refuse_form.refuse_reason"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_dialog_content = false">取 消</el-button>
        <el-button type="primary" @click="onCreateData">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="is_dialog_detail" title="详情">
      <div class="label">发起人：{{ is_dialog_content_title }}</div>
      <div class="label">审批内容：</div>
      <el-input
        type="textarea"
        placeholder="请输入"
        :rows="8"
        v-model="refuse_form.refuse_reason"
      ></el-input>
      <span slot="footer" class="dialog-footer-detail">
        <el-button
          type="primary"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          @click="onCreateDataAudit"
          >同 意</el-button
        >
        <el-button type="danger" @click="onCreateData">拒 绝</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import myCheck from "./components/my_check";
export default {
  name: "crm_customer_audit",
  components: {
    myCheck,
  },
  data() {
    return {
      params: {
        page: 1,
        total: 0,
        per_page: 10,
        type: 0,
      },
      is_tabs: 0,
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "最新" },
        { id: 2, name: "审批中" },
        { id: 3, name: "已同意" },
        { id: 4, name: "已拒绝" },
      ],
      time_value: "",
      tableData: [],
      is_table_loading: false,
      is_dialog_content: false,
      content: "",
      is_dialog_detail: false,
      audit_content: "",
      refuse_form: {
        id: "",
        refuse_reason: "",
      },
      is_dialog_content_title: "--",
      is_button_loading: false,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getCrmCustomerExamineData({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        });
    },
    onClickTime(e) {
      console.log(e);
    },
    onClickDialogAgree(e) {
      // this.$goPath(`/crm_customer_audit_detail?id=${e.id}`);
      this.$confirm("是否同意审批内容", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.onAgreeAuditData({ id: e.id }).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onClickDialogDetail(e) {
      this.is_dialog_detail = true;
      this.is_dialog_content_title = e.admin.user_name;
      this.refuse_form.id = e.id;
    },
    onCreateDataRefuse() {},
    onCreateDataAudit() {
      this.$confirm("是否同意审批内容", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.is_button_loading = true;
          this.$http
            .onAgreeAuditData({ id: this.refuse_form.id })
            .then((res) => {
              this.is_button_loading = false;
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.is_dialog_detail = false;
                this.getDataList();
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onClickDialog(e) {
      this.refuse_form.id = e.id;
      this.is_dialog_content = true;
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onClickTabs(e) {
      this.is_tabs = e.id;
      this.params.type = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onCreateData() {
      if (!this.refuse_form.refuse_reason) {
        this.$message.error("请输入内容");
        return;
      }
      this.$http.onRefuseAuditData(this.refuse_form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
          // this.is_dialog_content = false;
          this.is_dialog_detail = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .bottom-border {
    justify-content: flex-start;
    align-items: center;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
.dialog-footer-detail {
  width: 100%;
  display: inline-block;
  text-align: center;
}
.audit {
  font-size: 14px;
  color: #fff;
  padding: 3px 10px;
  border-radius: 4px;
  background: #3172f6;
  &.audit1 {
    background: #00b432;
  }
  &.audit2 {
    background: #fa5c5c;
  }
  &.audit3 {
    background: #ff8a00;
  }
}
.title-lable {
  font-size: 14px;
  color: #8a929f;
  margin-bottom: 8px;
}
.label {
  margin-bottom: 12px;
}
</style>
