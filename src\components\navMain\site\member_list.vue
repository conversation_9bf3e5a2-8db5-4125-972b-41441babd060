<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ tableData.length }}</span>
              <span class="text">条</span>
            </div>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="TableRowStyle"
          >
            <el-table-column prop="id" label="ID" width="100"></el-table-column>
            <el-table-column prop="name" label="姓名" v-slot="{ row }">
              <span>{{ row.name }}</span>
              <el-link
                style="margin-left:10px"
                @click="$onCopyValue(row.userid)"
                type="primary"
                >ID</el-link
              >
            </el-table-column>
            <el-table-column
              prop="department.departmentid"
              label="部门ID"
            ></el-table-column>
            <el-table-column
              prop="department.name"
              label="部门名称"
            ></el-table-column>
            <el-table-column label="标签">
              <template slot-scope="scope">
                <el-tag v-for="(v, i) in scope.row.tag_relation" :key="i">
                  {{ v.tag.name }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "member_list",
  components: {},
  data() {
    return {
      tableData: [],
      table_header: [
        // { prop: "id", label: "ID" },
        // { prop: "userid", label: "userid" },
        // { prop: "name", label: "姓名" },
        // { prop: "department.departmentid", label: "部门ID" },
        // { prop: "department.name", label: "部门名称" },
        // { prop: "tag_relation[0].tag.name", label: "标签" },
      ],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      is_table_loading: true,
      inputVal: "",
    };
  },
  computed: {},
  watch: {},
  methods: {
    getMemberList() {
      this.is_table_loading = true;
      this.$http.getMemberList({ params: this.params }).then((res) => {
        console.log(res, "人员列表");
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    TableRowStyle({ rowIndex }) {
      let rowBackground = {};
      if ((rowIndex + 1) % 2 === 0) {
        rowBackground.background = "#EFEFEF";
        return rowBackground;
      }
    },
  },
  created() {},
  mounted() {
    this.getMemberList();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
</style>
