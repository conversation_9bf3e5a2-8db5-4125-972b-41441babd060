<template>
<div class="tab-content-container" v-fixed-scroll="48">
  <div class="tab-content-body main-scroll">
    <div class="page">
    <div class="header">
      <div class="btnoperate">
        <div class="LibraryTitle" @click="returnsuperior"><i class="el-icon-arrow-left"></i>
          <div style="margin: 6px 10px;">{{Scriptiddata.package_name}}</div>
          <!-- <el-link type="primary"><i class="el-icon-edit"></i>编辑</el-link> -->
        </div>
        <!-- <div>
          <el-button size="small" type="primary" @click="addspeechcraft">添加话术</el-button>
        </div> -->
    </div>
    <div class="assorttitle">
      <!-- <div class="itemtitle"> -->
        <!-- <div v-for="(item,index) in tableData" :key="index" :class="{ isactive: c_type2 == index }"
        @click="sortchange(item,index)">
          {{item.cate_name}}
          <i style="margin-left: 7px;" v-if="c_type2 == index " class="el-icon-edit"
          @click="EditClassification(item)"></i>
        </div> -->
        <el-tabs v-model="c_type2" class="itemtitle" @tab-click="sortchange" >
          <el-tab-pane v-for="(item,index) in tableData" :key="item.id" :name="item.id.toString()">
              <span slot="label"> {{ item.cate_name }}<i style="margin-left: 7px;" v-if="c_type2 == item.id " class="el-icon-edit"
          @click="EditClassification(item)"></i></span>
          </el-tab-pane>
       
        </el-tabs>
      <!-- </div> -->
      <div class="setassort" @click="addassort">
        <div style="margin: 2px 7px;">
          <i class="el-icon-plus"></i>
        </div>
          添加
      </div>
      <div class="setassort" @click="openCatesSortForm" v-if="tableData.length">
        <div style="margin: 2px 7px;">
          <i class="el-icon-setting"></i>
        </div>
          设置
      </div>
    </div>
    </div> 
    
    <div class="scripttable" v-if="tableData.length">
        <template>
          <div v-for="(item,index) in speechcraft" :key="item.id">
            <el-collapse v-model="activeNames">
              <div class="insert">
                <!-- <el-button size="small" type="primary" @click="scriptdetails">详情</el-button> -->
                <el-link type="primary" :underline="false" @click="addspeechcraft(item,2)">+ 插入一条</el-link>
              </div>
            <el-collapse-item :title="item.title" :name="String(item.id)">
                <div class="speech-content" v-html="item.contentHtml"></div>
                <div class="operatebtn">
                  <el-link :underline="false" icon="el-icon-copy-document" @click="scriptcopy(item)">复制</el-link>
                  <el-link :underline="false" icon="el-icon-edit" @click="editingscript(item)">编辑</el-link>
                  <el-link :underline="false" icon="el-icon-delete" @click="delscript(item)">删除</el-link>
                </div>
            </el-collapse-item>
          </el-collapse>
          </div>
        </template>
    </div>
        <div class="scriptpage">
          <!-- <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="parma.page"
              :page-size="parma.per_page"
              layout="total, prev, pager, next, jumper"
              :total="parma.total">
            </el-pagination> -->
            <!-- <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="parma.page"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="parma.per_page"
              layout="total, sizes, prev, pager, next, jumper"
              :total="parma.total">
            </el-pagination> -->
        </div>
        <!-- 添加分类 -->
        <el-dialog
        title="分类操作"
        :visible.sync="EditingVisible"
        width="30%"
        :before-close="handleClose">
        <div class="text" style="margin: 0px 0px 10px 0px;">添加分类标题</div>
          <el-input v-model="rowdata.cate_name" placeholder="请输入内容"></el-input>
      
        <span slot="footer" class="dialog-footer">
          <el-button @click="EditingVisible = false">取 消</el-button>
          <el-button type="primary" @click="addassortname">确 定</el-button>
        </span>
      </el-dialog>
        <!-- 添加话术框 -->
        <div>
          <el-drawer
            :title="dratitle"
            :visible.sync="adddrawer"
            :direction="direction"
            :wrapperClosable="false"
            :before-close="handleClose">
            <div class="QuickEditA">
                <el-form ref="form" :model="formData" label-width="80px">
                    <el-form-item label="话术分类">
                      <el-select v-model="formData.cate_id" :disabled="!!formData.words_id">
                        <el-option v-for="item in tableData" :key="item.id" :label="item.cate_name" :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="话术标题">
                      <el-input v-model="formData.title"></el-input>
                    </el-form-item>
                    

                    <!-- <el-form-item label="关键词">
                      <el-input v-model="formData.keywords"></el-input>
                    </el-form-item> -->
                    <el-form-item label="话术内容">
                      <el-input :autosize="{ minRows: 30, maxRows: 20}" type="textarea" v-model="formData.content"
                      maxlength="500" show-word-limit></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div class="demo-drawer__footer">
              <el-button size="small" @click="handleClose">取 消</el-button>
              <el-button v-if="dratitle=='添加话术'" size="small" type="primary" @click="Submitscript" :loading="loading">确定</el-button>
              <el-button v-if="dratitle=='编辑话术'" size="small" type="primary" @click="Submiteditingscript" :loading="loading">确定</el-button>
            </div>
          </el-drawer>
        </div>
        <!-- 修改分类名 -->
        <el-dialog
          title="编辑分类"
          :visible.sync="itemizename"
          width="30%"
          :before-close="handleClose">

          <div class="text" style="margin: 0px 0px 10px 0px;">分类标题</div>
          <el-input v-model="itemizedata.cate_name" placeholder="请输入内容"></el-input>

          <span slot="footer" class="dialog-footer">
            <el-button @click="itemizename = false">取 消</el-button>
            <el-button type="primary" @click="submititemize">确 定</el-button>
          </span>
        </el-dialog>
        <scriptdetails ref="scriptdetails"></scriptdetails>
    </div>
  </div>
  <div class="tab-content-footer">
    <div>
      <template v-if="tableData.length">
        
        <el-button type="primary" size="small" @click="addspeechcraft(1)" icon="el-icon-plus">添加话术</el-button>
        <el-button type="primary" size="small" @click="openItemSortForm" icon="el-icon-sort" v-if="speechcraft.length">话术排序</el-button>
        <el-button type="primary" size="small" @click="dialogs.batchReplace = true">批量替换</el-button>
      </template>
    </div>
    <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="parma.total"
      :page-sizes="[10, 20, 30, 50,100]" :page-size="parma.per_page" :current-page="parma.page"
      @current-change="handleCurrentChange" @size-change="handleSizeChange">
    </el-pagination>
  </div>

  <setCatesSortForm :visible.sync="dialogs.setCatesSortForm" :package_id="Scriptiddata.id" v-model="tableData" @itemRemove="hanldeCateRemove"/>
  <setSpeechBatchSortForm :visible.sync="dialogs.setSpeechBatchSortForm" :package_id="Scriptiddata.id" :cate_id="c_type2" @change="getscriptlist"/>
  <batchReplace :visible.sync="dialogs.batchReplace" :package_id="Scriptiddata.id" @success="getscriptlist"/>
</div>
</template>
<script>
import scriptdetails from "./detailedinformation.vue"
import setCatesSortForm from '@/views/speech_library/components/setCatesSortForm.vue'
import setSpeechBatchSortForm from '@/views/speech_library/components/setSpeechBatchSortForm.vue'
import batchReplace from '@/views/speech_library/components/batchReplace.vue'
export default {
    props:{
        Scriptiddata:{
          type:Object,
          default:() => {}
      },
    },
    components:{
      scriptdetails,
      setCatesSortForm,
      setSpeechBatchSortForm,
      batchReplace
    },
    data() {
        return {
           parma:{
            cate_id:"",//分类id
            keywords:"",//关键词搜索
            page: 1,
            per_page: 10,
            total: 0,
           },
           direction: 'rtl',
           adddrawer:false,//控制侧边添加话术的模态框
           formData:{
            cate_id: ''
           },
           loading:false,
           tableData:[],//话术表格
           dratitle:"添加话术",//右侧边栏的标题
           is_table_loading:false,
           activeNames:[],
           EditingVisible:false,//添加分类弹框
           itemizename:false,//编辑分类弹框
           rowdata:{},//分类数据 
           c_type2:'0',//根据索引给颜色
           huashulist:{
            cate_id:"",//分类id
            keywords:"",//关键词
            package_id:"",//话术库id
           },//获取话术
           speechcraft:[],//话术折叠
           itemizedata:{},//编辑分类信息
           dialogs: {
            setCatesSortForm: false,
            setSpeechBatchSortForm: false,
            batchReplace: false,
           }
        }
    },
    mounted(){ 
        this.getlibrarylist()
    },
    methods:{
        //分类删除
        hanldeCateRemove(item){
          let index = this.tableData.findIndex(e=>e.id == item.id);
          if(index !== -1){
            if(this.c_type2 == item.id){
              let nextCate = this.tableData[index+1] || this.tableData[index-1] || null
              this.c_type2 = nextCate && String(nextCate.id) || '';
              this.huashulist.cate_id = this.c_type2
              this.getscriptlist()
            }
            this.tableData.splice(index,1);
          }
        },
        //分类排序
        openCatesSortForm(){
            this.dialogs.setCatesSortForm = true
        },
        //话术排序
        openItemSortForm(item){
            this.dialogs.setSpeechBatchSortForm = true
        },
        //返回到话术分类
        returnsuperior(){
            this.$emit('Listchange' );
        },
        //获取话术分类
        getlibrarylist(){

          // this.is_table_loading = true
            // this.parma.cate_id = this.Scriptiddata.scope
            this.$http.getscriptsortV2(this.Scriptiddata.id).then((res)=>{
                if(res.status==200){
                  // this.is_table_loading = false
                    console.log(res.data,"内容");
                    this.tableData = res.data.list
                    if(this.tableData.length && (this.c_type2 == 0 || !this.tableData.find(e => e.id == this.c_type2))){
                        this.c_type2 = this.tableData[0].id.toString()
                    }
                    this.getscriptlist()
                    // this.parma.page = res.data.page
                }
            })
        },
        //添加分类
        addassort(){
          this.rowdata = {}
          this.EditingVisible = true
        },
        //编辑分类
        EditClassification(row){
          this.itemizedata = JSON.parse(JSON.stringify(row));
          this.itemizename = true
        },
        //确定编辑分类
        submititemize(){
          console.log(this.itemizedata,"=======================");
          if(!this.itemizedata.cate_name){
            return this.$message.warning("分类标题不能为空！")
          }
          this.$http.editscriptlibrar(this.itemizedata).then((res)=>{
            if(res.status==200){
              this.$message.success("编辑成功！")
              this.itemizename = false
              this.getlibrarylist()
            }
          })
        },
        //分类切换赋值
        sortchange(row,index){
          // this.c_type2 = index
          this.huashulist.cate_id = this.c_type2
          // this.huashulist.cate_id = row.id
          this.parma.page = 1;
          this.getscriptlist()
        },
        //获取话术
        getscriptlist(){
          this.huashulist.package_id = this.Scriptiddata.id
          this.huashulist.cate_id = this.c_type2
          this.$http.getscriptV2({...this.parma, ...this.huashulist}).then((res)=>{
            if(res.status==200){
              this.speechcraft = (res.data.data || []).map(item => {
                item.contentHtml =item.content.replace(/\n/g, "<br/>");
                return item;
              })
              this.parma.total = res.data.total
              this.activeNames = this.speechcraft.map(e => String(e.id))
            }
          })
        },
        //添加话术
        addspeechcraft(row,start){
          this.formData.words_id = 0;
          if(row==1){
            this.formData.package_id = this.Scriptiddata.id 
            this.formData.cate_id = Number(this.c_type2)
          }else{
            if(start==2){
              this.formData.words_id = row.id
            }
            this.formData.cate_id = row.cate_id
            this.formData.package_id = row.package_id
          }
            this.dratitle = "添加话术"
            this.adddrawer = true
        },
        //关闭模态框
        handleClose(){ 
            this.formData = {
              cate_id: ''
            }  
            this.adddrawer = false
            this.EditingVisible = false
            this.itemizename = false
        },
        //删除分类
        onDelete(row) {
          this.$confirm("是否删除", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              // 点击确认调用公众号授权
              this.$http.delscriptlibrar(row.id).then((res) => {
                if (res.status === 200) {
                  this.$message.success("操作成功");
                  this.huashulist.cate_id = ""
                  this.getlibrarylist();
                }
              });
            })
            .catch(() => {
              // 点击取消控制台打印已取消
              console.log("已取消");
            });
        },
        //添加话术分类
        addassortname(){
          this.rowdata.package_id = this.Scriptiddata.id
          if(!this.rowdata.cate_name){
            return this.$message.warning("请填写分类名")
          }
          this.$http.addscriptassortV2(this.rowdata).then((res)=>{
            if (res.status==200) {
              this.getlibrarylist()
              /* this.huashulist.cate_id = ""
              this.c_type2 = '0' */
              this.$message.success("添加成功！")
              this.EditingVisible = false
            }
          })
        },
        //确定添加话术
        Submitscript(){
           if(!this.formData.title){
            return this.$message.warning("请输入话术标题")
           }
           if(!this.formData.content){
            return this.$message.warning("请输入话术内容")
           }
           this.$http.addcontributeV2(this.formData).then((res)=>{
            if(res.status == 200){
                this.$message({
                    type:"success",
                    message:"添加成功！"
                })
                this.getscriptlist()
                this.handleClose()
            }
           })
        },
        //话术详情
        scriptdetails(){
          this.$refs.scriptdetails.open()
        },
        //编辑话术
        editingscript(row){
          this.formData = JSON.parse(JSON.stringify(row)); // 拷贝
          this.dratitle = "编辑话术"
          this.adddrawer = true
          console.log(row,"================",this.formData);
        },
        //确定编辑话术
        Submiteditingscript(){
          if(!this.formData.title){
            return this.$message.warning("请输入话术标题")
           }
           if(!this.formData.content){
            return this.$message.warning("请输入话术内容")
           }
           const { cate_id, title, content,package_id, id } = this.formData
          let rowdata = {cate_id, title, content,package_id, id };
           this.$http.editscriptV2(rowdata).then((res)=>{
            if(res.status == 200){
                this.$message({               
                    type:"success",
                    message:"修改成功！"
                })
                this.getscriptlist()
                this.adddrawer = false
            }
           })
        },
        //复制话术
        scriptcopy(row){
          // console.log(row,"fuzhi");
          
          this.copyTxt(row.content);
        },
        copyTxt(txt){
          var oinput = document.createElement('textarea');
          oinput.value = txt;
          document.body.appendChild(oinput);
          oinput.select();				
          document.execCommand('copy');
          document.body.removeChild(oinput);  
          this.$message({               
              type:"success",
              message:"复制成功！"
          })
        },
        //删除话术
        delscript(row){
            this.$confirm('此操作将永久删除该话术, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$http.newdelscriptV2(row.id).then((res)=>{
                if(res.status==200){
                   this.$message({
                      type: 'success',
                      message: '删除成功!'
                    });
                    this.getlibrarylist()
                }
              })
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });          
            });
        },
        //
        handleSizeChange(e){
          this.parma.per_page = e;
          this.getscriptlist();
        },
        //分页
        handleCurrentChange(e){
          this.parma.page = e
          this.getscriptlist()
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
  width: 99%;
//   height: 200px;
  background-color: white;
  margin: 0 auto;
  border-radius:4px ;
  .header{
    padding: 3px 0;
    position: sticky;
    top: -15px;
    z-index: 1;
    background: #fff;
  }
  .btnoperate{
    margin:10px; 
    display:flex;
    justify-content: space-between;
    .LibraryTitle{
      cursor: pointer;
      color: #131315;
      font-weight: 600;
      display: flex;
      align-items: center;
    }
  }
  .assorttitle{
    margin: 15px 33px 0;
    // margin-left: 35px;
    // margin-top: 20px;
    color: #4E5969;
    font-size: 14px;
    display: flex;
    // justify-content: space-between;
    // width: 96%;
    height: 35px;
    border: 1px solid #ffff;
    // border-bottom-color: #d6dade;
    .itemtitle{
      flex: 1;
      width: calc( 100% - 132px );
    //  display: flex;
     div{
      margin-right: 25px;
      position: relative;
      cursor: pointer;
      &.isactive{
        color: #165DFF;
        &::after {
            position: absolute;
          left: 35%;
          transform: translateX(-38%);
          content: "";
          height: 3px;
          background: #2d84fb;
          width: 60px;
          display: block;
          margin-top: 14px;
        }
     }
     }

    }
    .setassort{
      width: 66px;
      display: flex;
      overflow: hidden;
      cursor: pointer;
      margin-top: 17px;
    }
  }
  .QuickEditA {
    width: 95%;
    height: calc(97vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-left: 8px;
  }
  .demo-drawer__footer{
    display: flex;
    justify-content: flex-end;
    margin: 10px;
  }
  .scripttable{
    padding: 20px 33px 0;
    .insert{
      display: flex;
      justify-content: flex-end;
    }
    .speechcraftcontent{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .speech-content{
      word-break: break-all;
    }
    .operatebtn{
       display: flex;
       justify-content: flex-end;
       margin-top: 8px;
       .el-link{
        color: #828385;
        margin-left: 15px;
        &:hover{
          color: #409EFF;
        }
       }
    }
    ::v-deep .el-collapse{
      border-top: 0px solid #EBEEF5;
      .el-collapse-item__header.is-active {
        color: #131315;
        font-weight: 600;
        font-size: 17px;
      }
      .el-collapse-item__content{
        padding-bottom: 15px;
      }
    }
  }
  .scriptpage{
    margin: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
.speech-footer{
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>