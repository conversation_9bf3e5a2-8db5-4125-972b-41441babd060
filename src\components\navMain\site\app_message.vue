<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-header class="div row" style="justify-content: space-between">
          <!-- 标题 -->
          <div class="div row">
            <div class="title">客户消息</div>
            <div class="title_number">
              <div>
                当前页面共（<i>{{ tableData.length }}</i
                >）条数据
              </div>
            </div>
          </div>
        </el-header>
        <div class="appMessage">
          <myTable
            v-loading="is_table_loading"
            :tableList="tableData"
            :header="table_header"
          ></myTable>
          <myPagination
            :total="params.total"
            :currentPage="params.page"
            :pagesize="params.per_page"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
          ></myPagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import myTable from "@/components/components/my_table";
import myPagination from "@/components/components/my_pagination";
export default {
  name: "app_message",
  components: {
    myTable,
    myPagination,
  },
  data() {
    return {
      tableData: [],
      table_header: [
        { prop: "id", label: "ID" },
        // { prop: "msgid", label: "消息ID" },
        {
          prop: "sender",
          label: "发送人",
          render: (h, data) => {
            return (
              <div>
                <span>{data.row.sender}</span>
                <el-link
                  style="margin-left:10px"
                  onClick={() => {
                    this.$onCopyValue(data.row.msgid);
                  }}
                  type="primary"
                >
                  ID
                </el-link>
              </div>
            );
          },
        },
        { prop: "receiver", label: "接收人" },
        {
          prop: "type",
          label: "消息类型",
          render: (h, data) => {
            if (data.row.type == 0) {
              return <el-tag type="success">客户</el-tag>;
            } else if (data.row.type == 1) {
              return <el-tag>客户群</el-tag>;
            }
          },
        },
        { prop: "content", label: "消息内容" },
        { prop: "created_at", label: "发送时间" },
      ],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      is_table_loading: true,
      inputVal: "",
    };
  },
  computed: {},
  watch: {},
  methods: {
    getChatMsgList() {
      this.is_table_loading = true;
      this.$http.getChatMsgList({ params: this.params }).then((res) => {
        console.log(res, "客户消息");
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
      this.getChatMsgList();
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
    },
  },
  created() {},
  mounted() {
    this.getChatMsgList();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.el-button {
  border: none;
  border-radius: 10px;
  margin: 5px;
}
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
</style>
