<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="基本信息" name="first">
      <div class="pages">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="grid-content div tagblue">
              <div class="number">
                <span class="number1">100</span>
                <span class="number2">人</span>
              </div>
              <span class="left">当前群人数</span>
            </div>
          </el-col>
          <el-col :span="8"
            ><div class="grid-content div taggreen">
              <div class="number">
                <span class="number1">100</span>
                <span class="number2">人</span>
              </div>
              <span class="left">今日进群人数</span>
            </div></el-col
          >
          <el-col :span="8"
            ><div class="grid-content div tagpurple">
              <div class="number">
                <span class="number1">100</span>
                <span class="number2">人</span>
              </div>
              <span class="left">今日退群人数</span>
            </div></el-col
          >
          <el-col :span="24">
            <div class="base-member">
              <span class="base-member-span">群成员</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="content-box-crm">
              <div class="table-top-box div row">
                <div class="t-t-b-left div row">
                  <span class="text">共</span>
                  <span class="blue">{{ params.total }}</span>
                  <span class="text">条</span>
                  <span class="text" style="margin: 0 12px">|</span>
                  <span class="text">已选</span>
                  <span class="blue">{{ multipleSelection.length }}</span>
                  <span class="text">个</span>
                </div>
              </div>
              <el-table
                v-loading="is_table_loading"
                :data="tableData"
                border
                :header-cell-style="{ background: '#EBF0F7' }"
                highlight-current-row
                :row-style="TableRowStyle"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="55"> </el-table-column>

                <el-table-column
                  prop="name"
                  label="群名"
                  align="center"
                ></el-table-column>
                <el-table-column prop="notice" label="群公告" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.notice" type="success">{{
                      scope.row.notice
                    }}</el-tag>
                    <el-tag v-else type="danger">暂无</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="admin_list"
                  label="管理员列表"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="owner.name"
                  label="创建人"
                  align="center"
                ></el-table-column>
                <el-table-column label="人数" align="center">
                  <template slot-scope="scope">
                    <el-tag v-for="(v, i) in scope.row.member" :key="i">{{
                      v.member_count
                    }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="跟进状态" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.status == 0" type="success"
                      >跟进人正常</el-tag
                    >
                    <el-tag v-if="scope.row.status == 1" type="danger"
                      >跟进人离职</el-tag
                    >
                    <el-tag v-if="scope.row.status == 2" type="warning"
                      >离职继承中</el-tag
                    >
                    <el-tag v-if="scope.row.status == 3">离职继承完成</el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                style="text-align: end; margin-top: 24px"
                background
                layout="prev, pager, next"
                :total="params.total"
                :page-size="params.per_page"
                :current-page="params.page"
                @current-change="onPageChange"
              >
              </el-pagination>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-tab-pane>
    <el-tab-pane label="操作日志" name="second">操作日志</el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: "customer_base",
  components: {},
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      is_table_loading: true,
      inputVal: "",
      activeName: "first",
      multipleSelection: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    getGroupchatList() {
      this.is_table_loading = true;
      this.$http.getGroupchatList({ params: this.params }).then((res) => {
        console.log(res, "客户群");
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
          this.is_table_loading = false;
        }
      });
    },
    // eslint-disable-next-line
    TableRowStyle({ row, rowIndex }) {
      let rowBackground = {};
      if ((rowIndex + 1) % 2 === 0) {
        rowBackground.background = "#EFEFEF";
        return rowBackground;
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onPageChange(e) {
      this.params.page = e;
      this.getGroupchatList();
    },
    handleClick() {},
  },
  created() {},
  mounted() {
    this.getGroupchatList();
  },
};
</script>
<style lang="scss" scoped>
.pages {
  height: 100%;
  margin: -15px;
  padding: 24px;
  .base-member {
    height: 50px;
    background: #fff;
    line-height: 50px;
    .base-member-span {
      font-weight: 700;
    }
  }
  .tagblue {
    background: #4b95fb;
  }
  .taggreen {
    background: #65cf98;
  }
  .tagpurple {
    background: #977dfb;
  }
  .grid-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 113px;
    border-radius: 10px;
    align-items: center;
    justify-content: center;
    img {
      width: 16px;
      height: 16px;
    }
    .left {
      font-size: 14px;
      color: #fff;
    }
    .number {
      .number1 {
        font-size: 24px;
        color: #fff;
        margin-right: 5px;
      }
      .number2 {
        font-size: 24px;
        color: #fff;
      }
    }
  }
  .ctn {
    margin-top: 12px;
    padding: 24px;
    background: #fff;
    .top-box {
      width: 100%;
    }
  }
}
.isbottom {
  margin-bottom: 12px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
