<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/no-dupe-keys -->
<template>
  <div>
    <div class="report_content">
      <div class="report_main">
        <div style="width: 794px;">
          <div class="report_title">报备客户</div>
          <el-form label-width="220px" ref="ruleform">
            <!-- 选择推荐项目 -->
            <el-form-item label="推荐项目 :">
              <div class="report_item">
                <el-select
                  :popper-append-to-body="false"
                  popper-class="project_select"
                  :filterable="true"
                  :multiple="true"
                  remote
                  placeholder="请选择"
                  v-model="build_arr"
                  :loading="broker_loading"
                  @change="choseProject"
                >
                  <el-option
                    v-for="(item, index) in project_list"
                    :key="index"
                    :label="item.build_name"
                    :value="item.project_id"
                  ></el-option>
                </el-select>
                
                <!-- div输入栏 -->
                <!-- <div
                  class="report_select"
                >
                  <div
                    class="report_select_box"
                    contenteditable="true"
                  >
                  </div>
                  <i class="el-icon-caret-right"></i>
                </div>
                <span class="report_prompt">请选择</span> -->
              </div>
            </el-form-item>
            <!-- 智能识别 -->
            <el-form-item label="智能识别 :">
              <div class="report_item">
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请输入内容"
                  v-model="textarea">
                </el-input>
              </div>
            </el-form-item>
            <!-- 填写客户名称 -->
            <el-form-item label="客户姓名 :">
              <div class="report_item">
                <el-input
                  placeholder="请填写客户姓名"
                  v-model="form_create.customer.name"
                ></el-input>
              </div>
            </el-form-item>
            <!-- 选择性别 0女 1男 -->
            <el-form-item label="客户性别 :">
              <div class="report_item">
                <el-radio-group v-model="form_create.customer.sex">
                  <el-radio
                    class="radio_style"
                    v-for="item in client_sex"
                    :key="item.value"
                    :label="item.value"
                    :value="item.value"
                  >
                    {{ item.description }}
                  </el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <!-- 选择是否陪同 0否 1是 -->
            <el-form-item label="是否陪同 :">
              <div class="report_item">
                <el-radio
                  class="radio_style"
                  v-for="item in is_check"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                  v-model="form_create.reported.visit_category"
                >
                  {{ item.description }}
                </el-radio>
              </div>
            </el-form-item>
            <!-- 选择手机号类型 0隐号 1全号-->
            <el-form-item label="号码类型 :" v-if="PhoneTypeChoice">
              <el-radio
                class="radio_style"
                v-for="item in full_number_list"
                :key="item.value"
                :label="item.value"
                :value="item.value"
                @change="chosePhoneType(item.value)"
                v-model="current_tel"
              >
                {{ item.description }}
              </el-radio>
            </el-form-item>
            <!-- 显示提示 -->
            <div class="PhoneTips" v-if="PhoneTypeChoice == false">您报备的项目包含隐号项目，系统将自动为您处理手机号码</div>
            <!-- 如果是全号 -->
            <el-form-item label="手机号码 :" v-if="current_tel == 1">
              <div class="report_item">
                <el-input
                  placeholder="请填写手机号"
                  v-model="form_create.customer.phone"
                  maxlength="11"
                ></el-input>
              </div>
              <!-- 新增报备客户按钮 -->
              <i 
                class="el-icon-circle-plus-outline addCustomer"
                @click="addLinkman"
              ></i>
            </el-form-item>
            <!-- 如果是隐号 -->
            <el-form-item v-if="current_tel == 0" label="手机号码 :">
              <div class="report_item">
                <el-input
                  class="hide_tel"
                  placeholder="前三位"
                  v-model="conceal_phone.start"
                  style="display: inline-block;width:155px"
                  maxlength="3"
                ></el-input>
                <span style="margin: 0 10px">
                  {{ is_num_mode ? "***" : "****" }}</span
                >
                <el-input
                  class="hide_tel"
                  v-model="conceal_phone.end"
                  :placeholder="is_num_mode ? '后五位' : '后四位'"
                  :maxlength="is_num_mode ? 5 : 4"
                  style="display: inline-block;width:155px"
                >
                </el-input>
              </div>
              <!-- 新增报备客户按钮 -->
              <i 
                class="el-icon-circle-plus-outline addCustomer"
                @click="addLinkman"
              ></i>
            </el-form-item>
            <!-- 新增联系人列表other_phone_list -->
            <div
              v-for="(item, index) in other_phone_list"
              :key="index"
            >
              <div class="new_Contact_Person">
                <div style="width: 220px; text-align: right;">
                  <el-input style="width:100px" v-model="item.name" placeholder="客户姓名"></el-input>
                </div>
                <div style="line-height: 40px; margin-left: 20px;">
                    <el-radio-group v-model="item.sex">
                      <el-radio
                        class="radio_style"
                        v-for="item in client_sex"
                        :key="item.value"
                        :label="item.value"
                        :value="item.value"
                      >
                        {{ item.description }}
                      </el-radio>
                    </el-radio-group>
                  </div>
                <div v-if="current_tel == 1" style="margin-left: 20px;">
                    <el-input
                      style="width: 215px;"
                      placeholder="请填写手机号码"
                      v-model="item.phone"
                      maxlength="11"
                    ></el-input>
                  </div>
                <div v-if="current_tel == 0" style="margin-left: 20px;">
                    <el-input
                      class="new_hide_Tel"
                      placeholder="前三位"
                      v-model="item.start_phone"
                      style="display: inline-block;width:80px"
                      maxlength="3"
                    ></el-input>
                    <span style="margin: 0 10px;">****</span>
                    <el-input
                      class="new_hide_Tel"
                      v-model="item.end_phone"
                      placeholder="后四位"
                      maxlength="4"
                      style="display: inline-block;width:85px"
                    >
                    </el-input>
                  </div>
                <!-- 新建联系人删除按钮 -->
                <el-button
                  type="danger"
                  size="small"
                  @click="delLinkman(item)"
                  icon="el-icon-delete"
                  circle
                  style="display: inline-block;"
                ></el-button>
              </div>
            </div>
            <!-- 预计到场时间 -->
            <el-form-item label="预计到场时间 :">
              <div class="report_item">
                <div class="block">
                  <el-date-picker
                    v-model="form_create.reported.visit_time"
                    type="datetime"
                    placeholder="选择日期时间"
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </div>
              </div>
            </el-form-item>
            <!-- 填写描述 -->
            <el-form-item label="描述 :">
              <div class="report_item">
                <el-input
                  type="textarea"
                  :rows="3"
                  placeholder="请填写其他要求说明"
                  v-model="form_create.reported.remark"
                >
                </el-input>
              </div>
            </el-form-item>
          </el-form>
          <!-- 确认提交 -->
          <div class="flex-row confirmSubmit">
            <el-button 
              type="primary" 
              class="confirmButton"
              @click="onSubmit"
            >
              提交
            </el-button>
          </div>
          <!-- 新增联系人 -->
          <!-- <el-button @click="addLinkman" style="float: right;margin-right: 15px;">
            新增联系人
          </el-button> -->
        </div>
        <div class="house_select" ref="selectBox">
          <!-- 顶部 -->
          <div class="house_selectTop"></div>
          <!-- 内容 -->
          <div class="house_selectMain" ref="selectMain">
            <div 
              class="select_box"
              v-for="(item, index) in project_list"
              :key="index"
            >
              <!-- 左 -->
              <div class="select_boxLeft">
                <span>{{item.build_name}}</span>
                <span>{{item.b_region_0_name + ' '}} {{item.b_region_1_name}}</span>
                <span>{{item.build_avg_price}}元/m²</span>
              </div>
              <!-- 右 -->
              <div class="select_boxRight">
                <!-- <div class="Checkbox"></div> -->
                <el-checkbox v-model="item.is_report" class="checkColor" @change="changeReport"></el-checkbox>
              </div>
            </div>
          </div>
          <!-- 底部 -->
          <div class="house_selectFooter">
            <span class="selected">已选择 {{this.selected_length}}/{{this.project_list.length}}</span>
            <el-button type="primary" @click="confirmSelected">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
export default {
  data() {
    return {
      textarea:"",
      PhoneTypeChoice: true, // 判断项目全/隐是否可显示选手机号类型
      //客户性别
      client_sex: [
        {
          description: "男",
          value: "1",
        },
        {
          description: "女",
          value: "0",
        },
      ],
      //项目列表参数
      params: {
        page: 1,
        per_page: 100,
      },
      //是否陪同
      is_check: [
        { value: "0", description: "否" },
        { value: "1", description: "是" },
      ],
      //项目列表
      project_list: [],
      broker_loading: false,
      //表带提交内容
      //存放选中的楼盘id
      build_arr: [],
      //楼盘选中的数据
      blild_type_checkon: [],
      //判断报备方式是否一致
      hide_num_mode: "",
      //手机号类型
      current_tel: "1",
      //隐号前三后四
      phone: {
        start: "",
        end: "",
      },
      //时间选择
      time_picker: "YMDhm",
      //客户姓名
      customer_name: "",
      //客户性别
      current_sex: "",
      //待处理的手机号码
      customer_phone: "",
      //是否陪同
      reported_go_with: "",
      //到场时间
      visit_time: "",
      // eslint-disable-next-line no-dupe-keys, vue/no-dupe-keys
      form_create: {
        //提交客户信息表单
        customer: {
          name: "",
          phone: "",
          sex: "",
        },
        reported: {
          project_id: "",
          remark: "",
          intention_build_category: "",
          // budget: "",
          visit_time: "",
          visit_people: "",
          visit_category: "1", //是否陪同
          customer_attached_phone: "",
          reported_go_with: "0",
          customer_id_no: "", // 身份证号后六位
        },
        // 报备隐/全
        project_data: {
          full_num_reported: "",
          build_name: "",
        },
      },
      //手机号类型选择:全号/隐号
      // eslint-disable-next-line no-dupe-keys, vue/no-dupe-keys
      full_number_list: [
        { value: "1", description: "全号" },
        { value: "0", description: "隐号" },
      ],
      //隐号手机号
      conceal_phone: {
        start: "",
        end: "",
      },
      //新增联系人
      other_phone_list: [],
      // 拷贝新增联系人
      other_clone_list: [],
      //存放选中的项目
      checkProdata: [],
      is_num_mode: false, // 前三后五显示
      build_type_checkon: [], //楼盘选中数据
      build_type_list: [], // 选择楼盘类型
      is_show_content: "",
      is_show_modal: false,
      Hide_project: "", // 是否是隐号项目
      selected_project_list: [], // 已经选中的项目
      selected_length: 0, // 已选中的项目的个数
    };
  },
  mounted() {
    this.getProject();
  },
  methods: {
    //获取项目列表数据
    getProject() {
      // 设置loading状态为true
      this.broker_loading = true;
      this.$http.getBaobeiProjectList().then((res) => {
        if (res.status === 200) {
          console.log(res)
          // 设置loading状态为false
          this.broker_loading = false;
          // 将推荐项目赋值给project_list
          this.project_list = res.data.data;
          // 每个推荐项目多选框绑定值，默认false不选中
          this.project_list.map((item) => {
            this.$set(item,'is_report',false)
          })
        }
      });
    },
    //选中项目
    // eslint-disable-next-line no-unused-vars
    // 当推荐项目发生改变时触发
    choseProject(item) {
      // 将选中项目的length赋值
      this.selected_length = this.build_arr.length;
      // 当通过select选中项目，右侧选择栏也跟着选中
      this.project_list.map((data) => {
        if(data.project_id == item) {
          data.is_report = true;
        } else { // 当通过select取消项目，右侧选择栏也跟着取消
          data.is_report = false;
        }
      })
      // 处理选中的项目数据
      this.dataProcessing(item);
    },
    // 选中项目后的数据处理
    dataProcessing(item) {
      // 将当前选中的id值给items
      let items = item;
      // 将项目列表数据赋值给blild_type_checkon
      this.blild_type_checkon = this.project_list.filter((x) =>
        new Set(items).has(x.project_id)
      );
      // 判断是否是隐号项目
      let mapHideProject = []
      this.blild_type_checkon.map((item)=>{
        mapHideProject.push(item.full_num_reported)
      })
      let result = mapHideProject.filter(index => index == 0);
      if(result == "") {
        this.chosePhoneType("1")
        return
      }
      if(result.length == mapHideProject.length) {
        // 都是隐号项目
        this.current_tel = 0
        // 显示选择手机号类型
        this.PhoneTypeChoice = true
        // 当全是隐号项目将手机号类型改为隐号
        this.chosePhoneType("0")
        console.log("都是隐号项目")
      }else {
        // 有隐号项目也有全号项目
        this.current_tel = 1
        // 隐藏选择手机号类型
        this.PhoneTypeChoice = false
        this.chosePhoneType("1")
        console.log("您报备的项目包含隐号项目，系统将自动为您处理手机号码")
      }
      //判断报备方式是否一致
      let checkId = items.slice(-1)[0];
      // 判断项目中选中的project_id是否一致
      let checkProject = this.project_list.filter((key) => {
        return key.project_id == checkId;
      });
      this.checkProdata = checkProject;
      // 如果选中0个楼盘
      if (this.build_arr.length == 0) {
        this.hide_num_mode = "";
      }
      // 如果hide_num_mode为空将1赋值它
      if (this.hide_num_mode == "" && checkProject != "") {
        this.hide_num_mode = checkProject[0].hide_num_mode;
      }
      if (checkProject != "" && checkProject[0].hide_num_mode != this.hide_num_mode) {
        this.build_arr.pop();
        this.blild_type_checkon.pop();
        this.$message({
          type: "warning",
          message: "报备方式不一致",
        });
      }
    },
    //立即提交
    async onSubmit() {
      // 如果没有选择项目
      if (this.build_arr.length === 0) {
        this.$message({
          message: "请选择项目",
          type: "warning",
        });
        return;
      }
      // 如果没有输入姓名
      if (this.form_create.customer.name == "") {
        this.$message({
          message: "请输入客户姓名",
          type: "warning",
        });
        return;
      }
      // 如果没有选择性别
      if (!this.form_create.customer.sex) {
        this.$message({
          message: "请选择性别",
          type: "warning",
        });
        return;
      }
      // phone: 前三后四  phone1：前三后五
      var phone =
        this.form_create.customer.phone || this.conceal_phone.start + "****" + this.conceal_phone.end;
      var phone1 =
        this.form_create.customer.phone || this.conceal_phone.start + "***" + this.conceal_phone.end;
      // 如果没有填写手机号
      if (!phone || phone == "****") {
        this.$message({
          message: "请输入手机号",
          type: "warning",
        });
        return;
      }
      this.other_clone_list = JSON.parse(JSON.stringify(this.other_phone_list))
      this.other_clone_list.unshift({
        name: this.form_create.customer.name,
        phone: this.is_num_mode ? phone1 : phone,
        sex: this.form_create.customer.sex,
      });
      // 将客户名称，联系方式存入对象数组
      let arr = [];

      for (let i = 0; i < this.other_clone_list.length; i++) {
        // 区分前三后五位
        this.other_clone_list[i].phone =
          this.other_clone_list[i].phone ||
          this.other_clone_list[i].start_phone +
          `${this.is_num_mode ? "***" : "****"}` +
          this.other_clone_list[i].end_phone;
        // 将客户信息列表赋值给obj
        let obj = JSON.parse(JSON.stringify(this.form_create));
        // 将新增联系人的name赋值给客户姓名name
        obj.customer.name = this.other_clone_list[i].name;
        // 将新增联系人的phone赋值给客户手机号phone
        obj.customer.phone = this.other_clone_list[i].phone;
        // 将新增联系人的sex赋值给客户性别sex
        obj.customer.sex = this.other_clone_list[i].sex;
        arr.push(obj);
      }
      let arr2 = this.onChangeArr(arr);
      let project_ids = [],
        customer_phones = [];
      let arr3 = [];
      for (let i = 0; i < arr2.length; i++) {
        let params = arr2[i];
        let formData = JSON.parse(JSON.stringify(params));
        if (!this.assignData(formData, i)) return;
        // 当手机号为全号
        if (this.current_tel == 1) {
          console.log(formData.customer.phone,"123")
          if (formData.project_data.full_num_reported == 1) {
            formData.customer.phone;
          } else if (this.current_tel == 1) {
            if (formData.project_data.full_num_reported == 1) {
              formData.customer.phone;
            } 
            // else {
            //   formData.customer.phone = formData.customer.phone.replace(
            //     /(\d{3})\d{4}(\d{4})/,
            //     "$1****$2"
            //   );
            // }
          }
        } else if (this.current_tel == 0) {
          formData.customer.phone = formData.customer.phone.replace(
            /(\d{3})\d{4}(\d{4})/,
            "$1****$2"
          );
        }
        if (formData.project_data.reported_visit == 0) {
          formData.reported.visit_people = 0;
        }
        //将需要验证的手机号存入数组
        project_ids.push(formData.reported.project_id);
        customer_phones.push(formData.customer.phone);
        let form_data = {};
        if (i + 1 === arr2.length) {
          form_data = {
            project_ids: project_ids.filter(
              (item, index, arr) => arr.indexOf(item, 0) === index // 过滤数组重复数据
            ),
            customer_phones: customer_phones.filter(
              (item, index, arr) => arr.indexOf(item, 0) === index // 过滤数组重复数据
            ),
          };
        }
        arr3.push(formData);
        if (JSON.stringify(form_data) != "{}") {
          await this.onValidataPhone(
            // 验证手机号码 
            form_data
          ).then((res) => {
            if (res.length === 0) {
              this.batchSub(arr3);
            } else if (res.type) {
              if (res1.confirm) {
                console.log("确认");
                this.other_phone_list.splice(i, 1);
                if (res.type === "confirm") {
                  this.batchSub(arr3);
                }
              }
              if (res1.cancel) {
                formData.customer.phone = "";
              }
            }
          });
        }
      }
    },
    onValidatePhone (arr) {
      if (arr.length === 0) {
        return;
      }
      return new Promise((resolve, reject) => {
        this.$http.submitBaobeiReal(arr).then((res) =>{
          if(res.status == 200) {
            resolve(res.data);
          }else {
            reject("error");
          }
        })
      });
    },
    //重置表单验证
    resetFormRules() {
      // eslint-disable-next-line no-undef
      this.$refs[ruleform].resetFields();
    },
    //选择手机号类型发生改变时
    chosePhoneType(e) {
      console.log("发生改变",e)
      this.current_tel = e;
      this.form_create.customer.phone = "";
      this.conceal_phone.start = "";
      this.conceal_phone.end = "";
    },
    //新增联系人
    addLinkman() {
      // 如果新增联系人的.length小于10就新增
      if (this.other_phone_list.length < 10) {
        this.other_phone_list.push({
          name: "",
          sex: "",
          phone: "",
          start_phone: "",
          end_phone: "",
        });
        // 增加联系人时加大右侧选择栏的高度
        this.$nextTick(() => {
          let height = this.$refs.selectBox.offsetHeight-127
          this.$refs.selectMain.style.height = height + 'px';
        })
      } else {
        this.$message({
          message: "最多添加10个联系人",
          type: "warning",
        });
      }
    },
    //删除联系人
    delLinkman(item) {
      let index = this.other_phone_list.indexOf(item);
      if (index !== -1) {
        this.other_phone_list.splice(index, 1);
        // 删除联系人时减小右侧选择栏的高度
        this.$nextTick(() => {
          let height = this.$refs.selectBox.offsetHeight - 127;
          if(height > 696) {
            this.$refs.selectMain.style.height = height - 62 + 'px';
          } else {
            this.$refs.selectMain.style.height = height - 23 + 'px';
          }
          
        })
      }
    },
    //验证手机号码
    onValidataPhone(arr) {
      if (arr.length === 0) {
        return;
      }
      return new Promise((resolve, reject) => {
        this.$http.validatePhone(arr).then((res) => {
          if (res.status == 200) {
            resolve(res.data);
          } else {
            reject("error");
            this.$message({
              message: res.data.message,
              type: "warning",
            });
          }
        });
      });
    },
    //数据处理
    assignData(params, index) {
      // 手机号码验证
      var phone_reg = /^1[3-9]\d{9}$/;
      // 判断前三后五/前三后四过滤
      var hide_phone_reg = this.is_num_mode
        ? /^1[3-9]\d{1}\*\*\*\d{5}$/
        : /^1[3-9]\d{1}\*\*\*\*\d{4}$/;
      if (!params.reported.visit_category) {
        delete params.reported.visit_category;
      }
      if (!params.reported.visit_people) {
        delete params.reported.visit_people;
      }
      if (!params.reported.intention_build_category) {
        delete params.reported.intention_build_category;
      }
      if (params.customer.phone.indexOf("*") != -1) {
        if (!hide_phone_reg.test(params.customer.phone)) {
          this.other_phone_list.splice(index, 1);
          this.$message({
            message: "请检查客户[" + params.customer.name + "]的联系方式",
          });
          return false;
        }
      } else {
        if (!phone_reg.test(params.customer.phone)) {
          this.other_phone_list.splice(index, 1);
          this.$message({
            message: "请检查客户[" + params.customer.name + "]的联系方式",
          });
          return false;
        }
      }
      return true;
    },
    //处理数组对象信息
    onChangeArr (arr) {
      // 联系人个数
      var customerLen = arr.length,
        // 推荐楼盘id个数
        projectLen = this.build_arr.length,
        key = 0,
        changeArr = [];
      // 循环中将项目id存入对象数组
      for (var i = 0; i < customerLen; i++) {
        for (var j = 0; j < projectLen; j++) {
          var temCustomer = this.cloneArr(arr[i]);
          temCustomer["reported"]["project_id"] = this.blild_type_checkon[j][
            "project_id"
          ];
          temCustomer["project_data"]["full_num_reported"] = this.blild_type_checkon[j][
            "full_num_reported"
          ];
          temCustomer["project_data"]["build_name"] = this.blild_type_checkon[j][
            "build_name"
          ];
          temCustomer["project_data"]["reported_visit"] = this.blild_type_checkon[j][
            "reported_visit"
          ];
          temCustomer["project_data"]["hide_num_mode"] = this.blild_type_checkon[j][
            "hide_num_mode"
          ];
          changeArr[key] = temCustomer;
          key++;
        }
      }
      return changeArr;
    },
    //处理数组对象信息
    cloneArr (Obj) {
      var buf;
      if (Obj instanceof Array) {
        buf = [];
        var i = Obj.length;
        while (i--) {
          buf[i] = this.cloneArr(Obj[i]);
        }
        return buf;
      } else if (Obj instanceof Object) {
        buf = {};
        for (var k in Obj) {
          buf[k] = this.cloneArr(Obj[k]);
        }
        // console.log(buf,"buf")
        return buf;
      } else {
        return Obj;
      }
    },
    //提交过程
    async batchSub(arr) {
      this.module_msg = ''
      for (let i in arr) {
        await this.onsubmitFormData(arr[i], i)
          .then((res) => {
            // 提交报备流程
            // this.isShowLoading(res);
            if(i == arr.length-1) {
              this.$confirm(res, '报备信息', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '我的客户',
                cancelButtonText: '取消',
                type: 'success'
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '跳转成功!'
                });
                this.$goPath(`/report_customer`);
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已关闭'
                });          
              });
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
      },
      isShowLoading (res) {
        this.is_show_content = res;
        this.is_show_modal = true;
      },
      //提交表单内容
      onsubmitFormData (formData, index) {
        this.other_phone_list.splice(index, 1);
        return new Promise((resolve, reject) => {
          this.$http.submitBaobeiCustomer(formData).then((res) => {
            let msg = res.status === 200 ? `成功：` : `失败：${res.data.message}`;
            // 拼接报备状态显示文字描述
            this.module_msg += `<div style='color:${res.status === 200 ? "green" : "red"
              }'>报备${msg}[${formData.customer.name}-${formData.customer.phone
              }-${formData.project_data.build_name}]；</div>\n`;
            resolve(this.module_msg);
            this.other_clone_list = [];
            this.other_phone_list = [];
            this.build_arr = [];
            this.form_create.customer.name = "";
            this.form_create.customer.phone = "";
            this.conceal_phone.start = "";
            this.conceal_phone.end = "";
            this.form_create.reported.visit_time = "";
            this.form_create.reported.remark = "";
            this.form_create.customer.sex = "";
            this.selected_length = 0;
            this.selected_project_list = [];
            this.project_list.map((item) => {
              item.is_report = false;
            })
          })
        });
      },
      // 提交表单内容测试
      // onsubmitFormData (formData, index) {
      //   this.other_phone_list.splice(index, 1);
      //   return new Promise((resolve, reject) => {
      //       let msg = "成功：";
      //       // 拼接报备状态显示文字描述
      //       this.module_msg += `<div style='color:green;'>报备${msg}[${formData.customer.name}-${formData.customer.phone
      //         }-${formData.project_data.build_name}]；</div>\n`;
      //       resolve(this.module_msg);
      //       // 提交成功，清空已经填写的数据
      //       this.other_clone_list = [];
      //       this.other_phone_list = [];
      //       this.build_arr = [];
      //       this.form_create.customer.name = "";
      //       this.form_create.customer.phone = "";
      //       this.conceal_phone.start = "";
      //       this.conceal_phone.end = "";
      //       this.form_create.reported.visit_time = "";
      //       this.form_create.reported.remark = "";
      //       this.form_create.customer.sex = "";
      //       this.selected_length = 0;
      //       this.selected_project_list = [];
      //       this.project_list.map((item) => {
      //         item.is_report = false;
      //       })
      //   });
      // },
      // 当报备项目选择发生变化时调用
      changeReport() {
        // 每次选择报备项目清空 已选择的报备项目
        this.selected_project_list = [];
        let selected = this.project_list.filter(data => {
          return data.is_report == true;
        })
        // 将选中的项目push
        selected.forEach(item => {
          this.selected_project_list.push(item);
        })
        // 将已选中项目的length赋值
        this.selected_length = this.selected_project_list.length;
      },
      // 确定选择需要报备的项目
      confirmSelected() {
        let arr_list = [];
        // 如果选中0个项目将赋值为[]
        if(this.selected_project_list.length == 0) {
          return this.build_arr = [];
        }
        // 遍历选择的项目
        this.selected_project_list.map(item => {
          arr_list.push(item.project_id);
        })
        this.build_arr = arr_list
        // 选中项目后的数据处理
        this.dataProcessing(arr_list);
      }
    },
};
</script>

<style lang="scss" scoped>
::v-deep .report_content {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
  .report_main {
    display: flex;
    width: 1145px;
    margin: 0 auto;
    background-color: #FFFFFF;
    .house_select {
      width: 351px;
      background: #F2F2F2;
      .house_selectTop {
        width: 100%;
        height: 57px;
        // height: 10%;
      }
      .house_selectMain {
        display: flex;
        flex-direction: column;
        height: 673px;
        // height: 80%;
        padding: 0 20px;
        box-sizing: border-box;
        overflow: auto;
        overflow-x: hidden;
        .select_box {
          display: flex;
          width: 310px;
          height: 92px;
          background-color: #FFFFFF;
          padding: 14px 20px;
          box-sizing: border-box;
          border-radius: 5px;
          margin-bottom: 10px;
          .select_boxLeft {
            display: flex;
            flex-direction: column;
            & span:first-child {
              color: #1D1F21;
              font-weight: 600;
              font-size: 16px;
              line-height: normal;
              letter-spacing: 0px;
              text-align: left;
            }
            & span:nth-child(2) {
              margin-top: 4px;
              color: #5D6B7B;
              font-size: 12px;
              line-height: normal;
              letter-spacing: 0px;
              text-align: left;
            }
            & span:nth-child(3) {
              margin-top: 4px;
              color: #FF4A4A;
              font-size: 12px;
              line-height: normal;
              letter-spacing: 0px;
              text-align: left;
            }
          }
          .select_boxRight {
            display: flex;
            flex-wrap: wrap;
            align-content: center;
            margin-left: auto;
            // .Checkbox {
            //   width: 20px;
            //   height: 20px;
            //   border-radius: 50%;
            //   border: 1px solid #B1B1B1;
            // }
            .checkColor .el-checkbox__input.is-checked .el-checkbox__inner, .myRedCheckBox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
              border-color: #3172F6;
              background-color:#3172F6;
            }

            .el-checkbox {
              .el-checkbox__input {
                .el-checkbox__inner {
                  width: 20px;
                  height: 20px;
                  border-radius: 50%;
                  // background-color: #3172F6;
                  // border-color: #3172F6;
                }
                .el-checkbox__inner::after {
                  width: 5px;
                  height: 10px;
                  left: 5px;
                  border: 2px solid #FFF;
                  border-left: 0;
                  border-top: 0;
                }
              }
            }
          }
        }
      }
      .house_selectFooter {
        display: flex;
        align-items: center;
        width: 100%;
        height: 70px;
        // height: 10%;
        padding: 20px;
        box-sizing: border-box;
        background: #F6F6F6;
        z-index: 9;
        .selected {
          margin-left: 20px;
          font-size: 14px;
          color: #5D6B7B;
        }
        .el-button {
          padding: 8px 17px;
          margin-left: auto;
        }
        .el-button--primary {
          border-color: #2D84FB;
          background-color: #2D84FB;
        }
        .el-button--primary:focus, .el-button--primary:hover {
          background: #66b1ff;
          border-color: #66b1ff;
          color: #FFF;
        }
        .el-button--primary:active {
          outline: 0;
        }
        .el-button--primary:active {
          background: #3a8ee6;
          border-color: #3a8ee6;
          color: #FFF;
        }
      }
    }
    .el-form {
      .el-form-item {
        margin-left: 30px;
        .el-form-item__label {
          color: #8A929F;
        }
        .el-form-item__content {
          .report_item {
            // .el-select {
            //   .project_select {
            //     visibility: hidden;
            //   }
            // }
            .block {
              .el-date-editor {
                width: 360px;
              }
            }
          }
          .report_item:nth-child(1) {
            position: relative;
            height: 41px;
            .el-input {
              .el-input__suffix {
                font-size: 20px;
              }
            }
            .report_prompt {
              color: #8A929F;
              position: absolute;
              top: 0px;
              right: 30px;
            }
          }
        }
      }
    }
    .report_title {
      text-align: center;
      padding: 28px 0;
      color: #2E3C4E;
      font-size: 20px;
      font-weight: 600;
    }
    .report_item {
      display: inline-block;
      .hide_tel {
        .el-input__inner {
          width: 155px !important;
        }
      }
      .new_hide_Tel {
        .el-input__inner {
          width: 75px !important;
        }
      }
      .el-textarea {
        width: 360px;
        .el-textarea__inner {
          min-height: 130px !important;
        }
      }
      .el-select {
        .el-input {
          width: 360px;
        }
      }
      .el-input {
        .el-input__inner {
          width: 360px;
        }
      }
    }
  }
  
}
.PhoneTips {
  font-size: 12px;
  color: #606266;
  margin-bottom: 22px;
  text-align: center;
}
.house_selectMain::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 5px;
}
/* 滚动条上的滚动滑块 */
.house_selectMain::-webkit-scrollbar-thumb {
    background: #D9D9D9;

}
.addCustomer {
  font-size: 18px;
  color: #409EFF;
  cursor: pointer;
  margin-left: 10px;
}
::v-deep .new_Contact_Person {
  display: flex;
  margin-bottom: 22px;
  padding: 0 20px;
  .el-form-item:first-child {
    .el-form-item__label {
      width: 110px !important;
    }
    .el-form-item__content {
      margin-left: 110px !important;
      .report_item {
        .el-input {
          .el-input__inner {
            width: 80px;
          }
        }
      }
    }
  }
  .el-form-item:nth-child(2) {
    .el-form-item__label {
      width: auto !important;
    }
    .el-form-item__content {
      width: 170px;
      margin-left: 0 !important;
    }
  }
  .el-form-item:nth-child(3) {
    .el-form-item__label {
      width: auto !important;
    }
    .el-form-item__content {
      margin-left: 76px !important;
      .report_item {
        .el-input {
          .el-input__inner {
            width: 180px;
          }
        }
      }
    }
  }
  .el-button {
    height: 32px;
    margin: 5px 0 0 10px;
  }
}
::v-deep .confirmSubmit {
  padding: 20px;
  justify-content: center;
  .el-button--primary {
    border-color: #2D84FB;
    background-color: #2D84FB;
  }
  .el-button--primary:focus, .el-button--primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    color: #FFF;
  }
  .confirmButton {
    margin-top: 15px;
    padding: 8px 17px;
  }
  .el-button--primary:active {
    outline: 0;
  }
  .el-button--primary:active {
    background: #3a8ee6;
    border-color: #3a8ee6;
    color: #FFF;
  }
}
::v-deep .radio_style {
  .el-radio__label {
    color: #8A929F;
  }
  .el-radio__input.is-checked+.el-radio__label {
    color: #409EFF;
  }
}
::v-deep .report_select {
  width: 360px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  display: inline-block;
  min-height: 40px;
  line-height: 40px;
  position: relative;
  & i {
    font-size: 20px;
    color: #8A929F;
    position: absolute;
    top: 9px;
    right: 10px;
  }
  .report_select_box {
    display: inline-block;
    width: 280px;
    box-sizing: border-box;
    padding: 0 15px;
    border: none;
    outline: 0;
  }
}
</style>
