<template>
    <div class="webVr-box">
        <div class="webVr_frame">
            <div class="header flex-row">
                <el-form label-width="80px" :inline="true" :model="webVrSeach" class="demo-form-inline">
                    <el-form-item label="搜索作品">
                        <el-input v-model="webVrSeach.name" placeholder="请输入关键词" prefix-icon="el-icon-search"
                            @blur="blurSearchName">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="作品分类">
                        <el-select style="width: 240px;" v-model="webVrSeach.pk_atlas_main" @change="changeSearchPhoto"
                            placeholder="请选择分类" clearable>
                            <el-option v-for="item in PhotoList" :key="item.pk_atlas_main" :label="item.name"
                                :value="item.pk_atlas_main">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间筛选">
                        <el-date-picker style="width: 240px;" v-model="SearchTime" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                            @change="changeSearchTime">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
                <div class="header-menu">
                    <div class="flex-row">
                        <el-popover v-model="showPopover" placement="bottom" width="200" trigger="click"
                            popper-class="popoverMore">
                            <div class="flex-box popo_box">
                                <div class="popo_editLabel">
                                    <span @click="webVrEditLabel">编辑标签</span>
                                </div>
                                <div class="popo_classify">
                                    <span @click="webVrEditPhoto">编辑分类</span>
                                </div>
                                <div class="popo_panoramaList">
                                    <span @click="webVrTaskList">VR全景任务列表</span>
                                </div>
                            </div>
                            <!-- 批量设置 -->
                            <div slot="reference" class="batch-setting">
                                <i class="el-icon-newfontxiangmuliebiao"></i>
                                <span>更多操作</span>
                            </div>
                        </el-popover>
                        <!-- 新建标签 -->
                        <div class="newly-label" @click="webVrAddLabel">
                            <i class="el-icon-newfontwenjianku"></i>
                            <span>新建标签</span>
                        </div>
                        <!-- 新建分类 -->
                        <div class="newly-category" @click="webVrAddPhoto">
                            <el-button icon="el-icon-newfontwenjianku" type="primary">新建分类</el-button>
                        </div>
                        <!-- 创建作品 -->
                        <div class="create-works">
                            <el-button icon="el-icon-plus" type="warning" @click="createVrWorks">
                                创建作品
                            </el-button>
                        </div>
                    </div>
                    <!-- <div class="flex-row" style="justify-content: end;"> -->
                    <!-- 添加标签 -->
                    <!-- <div class="add-label" @click="webVrAddLabel">
                            <span>添加标签</span>
                        </div> -->
                    <!-- <div class="edit-label" @click="webVrTaskList">
                            <span>任务列表</span>
                        </div> -->
                    <!-- 编辑标签 -->
                    <!-- <div class="edit-label" @click="webVrEditLabel">
                            <span>编辑标签</span>
                        </div> -->
                    <!-- 添加相册 -->
                    <!-- <div class="add-Photo" @click="webVrAddPhoto">
                            <span>添加相册</span>
                        </div> -->
                    <!-- 编辑相册 -->
                    <!-- <div class="edit-Photo" @click="webVrEditPhoto">
                            <span>编辑分类</span>
                        </div> -->
                    <!-- </div> -->
                </div>
            </div>
            <div class="links-menu"></div>
            <div class="webVr_list">
                <div class="webVr_main" v-for="(item, index) in formList" :key="index">
                    <div class="webVr_edge">
                        <!-- 多选框 -->
                        <div class="webVr-checkbox">
                            <el-checkbox></el-checkbox>
                        </div>
                        <!-- 展示图 -->
                        <div class="webVr-pictures">
                            <img :src="item.thumb_path" alt="">
                            <div class="heat">
                                <div>
                                    <i class="el-icon-newfontic_zan iconSize"></i>
                                    {{ item.praised_num }}
                                </div>
                                <div>
                                    <i class="el-icon-newfontyanjing iconSize"></i>
                                    {{ item.browsing_num }}
                                </div>
                            </div>
                        </div>
                        <!-- 标题栏 -->
                        <div class="webVr-title">
                            <div class="title">{{ item.name }}</div>
                            <span class="introduction">{{ item.profile }}</span>
                            <div class="label">
                                <span class="label-box" v-for="(tag, idx) in item.tags" :key="idx">
                                    {{ tag }}
                                </span>
                            </div>
                            <!-- <div class="switch" @click="onGglobalSwitch">
                                <span>全局开关</span>
                            </div> -->
                        </div>
                        <!-- 控制按钮 -->
                        <div class="webVr-Func flex-row">
                            <div class="webVr-edit">
                                <el-button type="warning" @click="vredit(item)">编辑</el-button>
                            </div>
                            <div class="webVr-delete">
                                <el-button plain>删除</el-button>
                            </div>
                            <div class="webVr-rename">
                                <el-button plain>重命名</el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <el-pagination class="myPagination" background layout="total,prev, pager, next" :total="data_count"
                    :page-size="webVrSeach.rows" :current-page="webVrSeach.page" @current-change="onPageChange">
                </el-pagination>
            </div>
        </div>
        <!-- 全局开关模态框 -->
        <!-- <el-dialog title="全局开关" :visible.sync="dialogGglobalSwitch" width="680px">
            <div style="height: 485px;">
                <div class="Gglobal-box">
                    <div class="Gglobal-main" v-for="(item, index) in GglobalDataList" :key="index">
                        <span class="Gglobal-name">{{ item.name }}</span>
                        <el-switch v-model="item.values" :width="32">
                        </el-switch>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer Gglobal-footer">
                <div style="border-bottom: 1px solid #D8D8D8; margin-bottom: 26px;"></div>
                <el-button @click="dialogGglobalSwitch = false">取 消</el-button>
                <el-button type="primary" @click="dialogGglobalSwitch = false">确 定</el-button>
            </span>
        </el-dialog> -->
        <!-- 编辑vr标签模态框 -->
        <el-dialog title="编辑标签" :visible.sync="dialogEditWorks" width="680px" :close-on-click-modal="false"
            @close="dialogEditClose">
            <div class="EditWorks-box">
                <el-form :model="form_editLabel" label-width="80px">
                    <el-form-item label="标签类型">
                        <el-select v-model="form_editLabel.type" placeholder="请选择" @change="changeEditLabel">
                            <el-option v-for="item in editLabelType" :key="item.values" :label="item.name"
                                :value="item.values">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="标签id">
                        <el-select v-model="form_editLabel.id" placeholder="请选择">
                            <el-option v-for="item in VrWorksLabel_list" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="标签名称">
                        <el-input v-model="form_editLabel.name" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="排序">
                        <el-input v-model="form_editLabel.sort" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer EditWorks-footer">
                <el-button @click="dialogEditWorks = false">取 消</el-button>
                <el-button type="primary" @click="confirmEditLabel">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 编辑vr相册模态框 -->
        <el-dialog title="编辑分类" :visible.sync="dialogEditPhoto" width="680px" :close-on-click-modal="false"
            @close="dialogEditPhonoClose">
            <div>
                <el-form :model="form_editPhoto" label-width="80px">
                    <el-form-item label="选择分类" class="PhotoID">
                        <el-select v-model="form_editPhoto.id" placeholder="请选择">
                            <el-option v-for="item in PhotoList" :key="item.pk_atlas_main" :label="item.name"
                                :value="item.pk_atlas_main">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="分类名称">
                        <el-input v-model="form_editPhoto.name" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer EditPhoto-footer">
                <el-button @click="dialogEditPhoto = false">取 消</el-button>
                <el-button type="primary" @click="confirmEditPhoto">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 添加vr标签模态框 -->
        <el-dialog title="新建标签" :visible.sync="dialogAddLabel" width="680px" :close-on-click-modal="false"
            @close="dialogAddLabelClose">
            <div>
                <el-form :model="form_AddLabel" label-width="80px">
                    <el-form-item label="标签名称">
                        <el-input v-model="form_AddLabel.name" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="标签类型" class="PhotoID">
                        <el-select v-model="form_AddLabel.type" placeholder="请选择">
                            <el-option v-for="item in editLabelType" :key="item.values" :label="item.name"
                                :value="item.values">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序">
                        <el-input v-model="form_AddLabel.sort" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer EditPhoto-footer">
                <el-button @click="dialogAddLabel = false">取 消</el-button>
                <el-button type="primary" @click="confirmAddLabel">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 添加vr相册模态框 -->
        <el-dialog title="新建分类" :visible.sync="dialogAddPhoto" width="680px" :close-on-click-modal="false"
            @close="dialogAddPhonoClose">
            <div>
                <el-form :model="form_AddPhoto" label-width="80px">
                    <el-form-item label="分类名称">
                        <el-input v-model="form_AddPhoto.name" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer EditPhoto-footer">
                <el-button @click="dialogAddPhoto = false">取 消</el-button>
                <el-button type="primary" @click="confirmAddPhoto">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 创建作品 -->
        <el-dialog title="创建作品" :visible.sync="dialogCreateWorks" width="680px" :close-on-click-modal="false"
            @close="dialogAddPhonoClose">
            <div>
                <el-form :model="form_create" label-width="80px">
                    <el-form-item label="作品名称">
                        <el-input v-model="form_create.pname" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="作品标签" class="PhotoID">
                        <el-select v-model="worksLabel" multiple placeholder="请选择">
                            <el-option v-for="item in VrWorksLabel_list" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="作品分类" class="PhotoID">
                        <el-select v-model="form_create.atlas_id" placeholder="请选择">
                            <el-option v-for="item in PhotoList" :key="item.pk_atlas_main" :label="item.name"
                                :value="item.pk_atlas_main">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="作品详情" class="Photo-details">
                        <!-- :before-upload="e=>beforeUpload(e,index)" @mousedown.native="resetIndex(index)" -->
                        <div class="photo-item" v-for="(item, index) in imgList" :key="index">
                            <el-upload :disabled="item.url != '' && item.url != undefined" class="uploader-create"
                                :action="upurl" list-type="picture-card" :headers="myHeader"
                                :on-success="e => handleUploadSuccess(e, index)" :show-file-list="false" :multiple="true">
                                <img v-if="item.imgsrc" :src="item.imgsrc" class="photo-item-img">
                                <i v-else class="el-icon-plus photo-item-uploader-icon"></i>
                                <span class="uploader-actions" v-if="item.url">
                                    <span class="uploader-actions-item" @click="handlePictureCardPreview($event, index)">
                                        <i class="el-icon-view"></i>
                                    </span>
                                    <span class="uploader-actions-item" @click="e => handleRemove(e, index)">
                                        <i class="el-icon-delete"></i>
                                    </span>
                                </span>
                            </el-upload>
                            <el-form label-width="40px" size="mini">
                                <el-form-item label="描述">
                                    <el-input style="width: 108px;" v-model="item.imgname"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer EditPhoto-footer">
                <el-button @click="dialogCreateWorks = false">取 消</el-button>
                <el-button type="primary" @click="confirmCreate">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog :visible.sync="dialogUploadPhoto" class="dialog-Photo">
            <img width="100%" :src="dialogPhoto" alt="">
        </el-dialog>
        <el-dialog title="VR全景任务列表" :visible.sync="dialogPanoramaList">
            <!-- ====== -->
            <el-form label-width="80px" :inline="true" :model="webVrTask_params">
                <el-form-item label="搜索作品">
                    <el-input style="width: 240px;" v-model="webVrTask_params.name" placeholder="请输入关键词"
                        prefix-icon="el-icon-search" @blur="blurTabsName">
                    </el-input>
                </el-form-item>
                <el-form-item label="作品分类">
                    <el-select style="width: 240px;" v-model="webVrTask_params.atlas_id" @change="changeTabsClassify"
                        placeholder="请选择分类" clearable>
                        <el-option v-for="item in PhotoList" :key="item.pk_atlas_main" :label="item.name"
                            :value="item.pk_atlas_main">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间筛选">
                    <el-date-picker style="width: 240px;" v-model="tabsSearchTime" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                        @change="changeTabsSearchTime">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <!-- ====== -->
            <el-table :data="tableData" border stripe style="width: 100%">
                <!-- <el-table-column
                    prop="id"
                    label="任务id"
                >
                </el-table-column> -->
                <el-table-column prop="pname" label="作品名称">
                </el-table-column>
                <el-table-column prop="albums" label="作品分类">
                </el-table-column>
                <el-table-column prop="ctime" label="创建时间">
                </el-table-column>
            </el-table>
            <el-pagination class="PanoramaPage" background @current-change="Panorama_pageChange"
                :current-page="webVrTask_params.page" :page-size="webVrTask_params.rows" layout="total, prev, pager, next"
                :total="panorama_total">
            </el-pagination>
        </el-dialog>
    </div>
</template>
<script>
import config from "@/utils/config.js";
export default {
    data() {
        return {
            formList: [], // vr作品列表
            ceshi: true,
            // dialogGglobalSwitch: false, // 全局开关模态框
            dialogEditWorks: false, // 编辑标签模态框
            dialogEditPhoto: false, // 编辑相册模态框
            dialogAddLabel: false, // 添加标签模态框
            dialogAddPhoto: false, // 添加相册模态框
            dialogCreateWorks: false, // 创建作品模态框
            dialogUploadPhoto: false, // 查看已上传的全景图片模态框
            // 全局开关属性
            GglobalDataList: [
                {
                    name: "创作者名称",
                    values: false
                },
                {
                    name: "浏览量",
                    values: true
                },
                {
                    name: "场景选择",
                    values: true
                },
                {
                    name: "场景名称",
                    values: true
                },
                {
                    name: "点赞",
                    values: true
                },
                {
                    name: "分享",
                    values: true
                },
                {
                    name: "全屏",
                    values: true
                },
                {
                    name: "清屏",
                    values: true
                },
                {
                    name: "VR眼睛",
                    values: true
                },
                {
                    name: "视角切换",
                    values: true
                },
                {
                    name: "作品来源",
                    values: false
                },
            ],
            // 作品列表搜索传参
            webVrSeach: {
                name: "", // 搜索作品名称
                pk_atlas_main: "", // 搜索相册id
                time_s: "", // 搜索开始时间
                time_e: "", // 搜索结束时间
                page: 1, // 当前页码
                rows: 20, // 每页多少条
            },
            // 编辑VR标签参数
            form_editLabel: {
                name: "", // 编辑标签名称
                type: "", // 编辑标签类型 1：图片，2：视频
                sort: "", // 排序
                id: "", // 标签id
            },
            // 添加VR标签参数
            form_AddLabel: {
                name: "", // 标签名称
                type: "", // 1:图片标签 2:视频标签
                sort: "", // 排序 
            },
            // 添加VR相册参数
            form_AddPhoto: {
                name: "", // 相册名称
            },
            // 存储接口获取相册列表
            PhotoList: [],
            // 编辑VR相册
            form_editPhoto: {
                name: "", // 相册名称
                id: "", // 相册id
            },
            // 创建作品接口传参
            form_create: {
                pname: "", // 作品名称
                pic_tags: "", // 标签,多个用英文逗号隔开
                atlas_id: "", // 相册id 
                images: "", // 作品详情,数字对象格式[{}](JSON字符串) imgsrc:全景图片路径 imgname:全景图片名称
            },
            // 存储筛选时间参数
            SearchTime: "",
            // 编辑VR标签类型参数
            editLabelType: [
                {
                    name: "图片", values: 1
                },
                {
                    name: "视频", values: 2
                }
            ],
            // 存储通过接口获取的vr标签参数
            VrWorksLabel_list: [],
            // 上传全景预览图
            dialogImageUrl: '',
            dialogVisible: false,
            disabled: false,
            upurl: "/api/common/file/upload/admin?category=401", // 作品详情 上传图片url路径
            worksLabel: "", // 作品标签
            // 存储全景图片
            imgList: [
                {
                    path: "",
                    desc: ""
                }
            ],
            dialogPhoto: "", // 查看已上传的全景图片
            // 全景VR任务列表接口参数
            webVrTask_params: {
                name: '',
                atlas_id: '',
                time_s: "",
                time_e: "",
                page: 1, // 当前页码
                rows: 10, // 每页多少条
            },
            data_count: 0, // 分页器总条数
            showPopover: false, // Popover 弹出框
            dialogPanoramaList: false, // 全景任务列表模态框
            tableData: [], // VR全景任务列表数据
            panorama_total: 0, // VR全景任务总条数
            tabsSearchTime: "", // VR全景任务检索时间
        }
    },
    mounted() {
        // 获取vr作品列表
        this.getDataList();
        // 获取vr相册列表
        this.getVrworksPhoto();
        // 获取vr标签
        this.getVrWorksLabel();
    },
    computed: {
        // 获取请求头
        myHeader() {
            return {
                // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
                Authorization: config.TOKEN,
            };
        },
    },
    methods: {
        // 获取vr作品列表
        getDataList() {
            this.$http.getVrWorksList(this.webVrSeach).then((res) => {
                if (res.status == 200) {
                    // console.log(res.data.data);
                    this.formList = res.data.data;
                    this.data_count = res.data.total;
                }
            })
        },
        // 全局开关模态框
        // onGglobalSwitch() {
        //     this.dialogGglobalSwitch = true;
        // },
        // 当搜索时间失去焦点 
        changeSearchTime(val) {
            if (val != '' && val != undefined) {
                this.webVrSeach.time_s = val[0];
                this.webVrSeach.time_e = val[1];
            } else {
                this.webVrSeach.time_s = "";
                this.webVrSeach.time_e = "";
            }
            this.webVrSeach.page == 1;
            this.getDataList();
        },
        // 当搜索作品失去焦点
        blurSearchName() {
            this.webVrSeach.page == 1;
            this.getDataList();
        },
        // 当分类筛选发生改变时
        changeSearchPhoto() {
            this.webVrSeach.page == 1;
            this.getDataList();
        },
        // VR全景任务列表
        webVrTaskList() {
            this.$http.getVrworksTask(this.webVrTask_params).then((res) => {
                if (res.status == 200) {
                    console.log(res.data);
                    this.tableData = res.data.data;
                    this.dialogPanoramaList = true;
                    this.showPopover = false;
                    this.panorama_total = res.data.total;
                }
            })
        },
        // 编辑vr作品
        webVrEditLabel() {
            // this.getVrWorksLabel(); // 获取vr标签
            this.dialogEditWorks = true;
            this.showPopover = false;
        },
        // 编辑vr作品相册
        webVrEditPhoto() {
            this.dialogEditPhoto = true;
            this.showPopover = false;
        },
        // 确定编辑vr标签
        confirmEditLabel() {
            this.$http.editVrWorksLabel(this.form_editLabel).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        message: '编辑成功',
                        type: 'success'
                    });
                    this.dialogEditWorks = false;
                    this.getDataList();
                }
            })
        },
        // 确定添加vr标签
        confirmAddLabel() {
            this.$http.addVrworksLabel(this.form_AddLabel).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        message: '添加成功',
                        type: 'success'
                    });
                    this.dialogAddLabel = false;
                    this.getDataList();
                }
            })
        },
        // 确定添加vr相册
        confirmAddPhoto() {
            this.$http.addVrworksPhoto(this.form_AddPhoto).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        message: '添加成功',
                        type: 'success'
                    });
                    this.dialogAddPhoto = false;
                    // 请求获取vr相册接口
                    this.getVrworksPhoto();
                }
            })
        },
        // 确定编辑vr相册
        confirmEditPhoto() {
            this.$http.editVrWorksPhoto(this.form_editPhoto).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        message: '编辑成功',
                        type: 'success'
                    });
                    this.dialogEditPhoto = false;
                    // 请求获取vr相册接口
                    this.getVrworksPhoto();
                    this.getDataList();
                }
            })
        },
        // 获取vr标签
        getVrWorksLabel() {
            this.$http.getVrWorksLabel({ type: this.form_editLabel.type }).then((res) => {
                if (res.status == 200) {
                    this.VrWorksLabel_list = res.data
                }
            })
        },
        // 获取vr相册
        getVrworksPhoto() {
            this.$http.getVrworksPhoto().then((res) => {
                if (res.status == 200) {
                    this.PhotoList = res.data;
                }
            })
        },
        // 当编辑标签中的标签类型发生变化时
        changeEditLabel() {
            this.form_editLabel.id = "";
            this.getVrWorksLabel()
        },
        // 编辑标签模态框关闭时
        dialogEditClose() {
            // 将模态框参数赋值为空
            this.form_editLabel.name = "";
            this.form_editLabel.type = "";
            this.form_editLabel.sort = "";
            this.form_editLabel.id = "";
        },
        // 编辑vr相册模态框关闭时
        dialogEditPhonoClose() {
            // 将模态框参数赋值为空
            this.form_editPhoto.name = "";
            this.form_editPhoto.id = "";
        },
        // 添加vr标签模态框关闭时
        dialogAddLabelClose() {
            // 将模态框参数赋值为空
            this.form_AddLabel.name = "";
            this.form_AddLabel.type = "";
            this.form_AddLabel.sort = "";
        },
        // 添加vr相册模态框关闭时
        dialogAddPhonoClose() {
            // 将模态框参数赋值为空
            this.form_AddPhoto.name = "";
        },
        // 添加vr作品标签模态框
        webVrAddLabel() {
            this.dialogAddLabel = true;
        },
        // 添加vr作品相册模态框
        webVrAddPhoto() {
            this.dialogAddPhoto = true;
        },
        // 创建作品
        createVrWorks() {
            this.dialogCreateWorks = true;
        },
        // 确定创建作品
        confirmCreate() {
            // 将数组格式作品标签转换为字符串
            this.form_create.pic_tags = this.worksLabel.toString();
            let data_List = [];
            Object.assign(data_List, this.imgList)
            data_List.pop();
            let sum = [];
            data_List.forEach((item) => {
                if (item.imgname == "" || item.imgname == undefined) {
                    return this.$message.error('描述不能为空');
                } else if (item.url == "" || item.url == undefined) {
                    return this.$message.error('请选择全景图片');
                }
                sum.push(
                    // JSON.stringify(
                    //     {
                    //         imgsrc: item.url,
                    //         imgname: item.imgname
                    //     }
                    // )
                    {
                        imgsrc: item.url,
                        imgname: item.imgname
                    }
                )
            })
            this.form_create.images = JSON.stringify(sum);
            this.$http.createVrworks(this.form_create).then((res) => {
                if (res.status == 200) {
                    this.$message({
                        message: '创建作品成功',
                        type: 'success'
                    });
                    this.dialogCreateWorks = false;
                }
            })
        },
        // 创建作品上传全景图片成功时执行
        handleUploadSuccess(e, index) {
            // console.log(e, index);
            e.imgsrc = "http://tfyvr-test.oss-cn-hangzhou.aliyuncs.com/" + e.url
            e.imgname = this.imgList[index].imgname
            this.imgList.unshift(e)
            // 清空 图片详情最开始的图片描述
            if (this.imgList[index].imgname) {
                let end = this.imgList.length - 1;
                this.imgList[end].imgname = "";
            }
        },
        // 查看已上传的全景图片
        handlePictureCardPreview(e, index) {
            this.dialogUploadPhoto = true;
            this.dialogPhoto = this.imgList[index].imgsrc;
        },
        // 删除已上传的全景图片
        handleRemove() {
            this.imgList.shift();
        },
        // 当页数发生变化时
        onPageChange(val) {
            this.webVrSeach.page = val;
            this.getDataList();
        },
        // 当页数发生变化触发
        Panorama_pageChange(val) {
            this.webVrTask_params.page = val;
            this.webVrTaskList();
        },
        // 全景任务列表搜索作品
        blurTabsName() {
            this.webVrTaskList();
        },
        // 全景任务列表作品分类
        changeTabsClassify() {
            this.webVrTaskList();
        },
        // 全景任务列表
        changeTabsSearchTime(val) {
            if (val != '' && val != undefined) {
                this.webVrTask_params.time_s = val[0];
                this.webVrTask_params.time_e = val[1];
            } else {
                this.webVrTask_params.time_s = "";
                this.webVrTask_params.time_e = "";
            }
            this.webVrTask_params.page == 1;
            this.webVrTaskList();
        },
        vredit(item) {
            console.log(item);
            // 存储数据到本地存储
            localStorage.setItem('data',JSON.stringify(item));
            this.$goPath(`/vr_edit`);
        }
    }
}
</script>
<style lang="scss" scoped>
.webVr-box {
    padding: 24px;
    margin: -15px;
    background: #F1F4FA;

    .Gglobal-box {
        display: flex;
        flex-wrap: wrap;

        ::v-deep .Gglobal-main {
            // width: 100px;
            padding-right: 24px;
            margin-bottom: 32px;

            .Gglobal-name {
                font-size: 12px;
                padding-right: 8px;
            }

            .el-switch {
                .el-switch__core {
                    height: 16px;
                    border-color: #F1F4FA;
                    background-color: #F1F4FA;
                }
            }

            .el-switch.is-checked .el-switch__core {
                border-color: #2D84FB;
                background-color: #2D84FB;
            }

            .el-switch__core:after {
                width: 14px;
                height: 14px;
                top: 0px;
                left: 16px;
                margin-left: -15px;
            }

            .el-switch.is-checked .el-switch__core::after {
                left: 100%;
            }
        }
    }

    .webVr_frame {
        background: #FFFFFF;
        border-radius: 4px;

        ::v-deep .header {
            display: flex;
            justify-content: space-between;
            position: relative;
            padding: 24px;
            box-sizing: border-box;
            align-items: initial;

            .el-form {
                .el-form-item:first-child {
                    .el-form-item__content {
                        .el-input {
                            .el-input__inner {
                                width: 240px;
                                height: 32px;
                                // margin: 24px 0 24px 24px;
                                padding-left: 36px;
                            }
                        }
                    }
                }

                .el-form-item:nth-child(2) {
                    .el-form-item__content {
                        .el-select {
                            .el-input {
                                .el-input__inner {
                                    height: 32px;
                                }
                            }
                        }
                    }
                }

                .el-form-item:last-child {
                    margin-bottom: 0;
                }
            }

            .el-input {
                // .el-input__inner {
                //     width: 240px;
                //     height: 32px;
                //     // margin: 24px 0 24px 24px;
                //     padding-left: 36px;
                // }

                .el-input__prefix {
                    left: 4px;
                    font-size: 16px;
                    // margin-top: -4px;
                }
            }

            .header-menu {
                display: flex;
                flex-direction: column;

                .batch-setting,
                .newly-label,
                .edit-label,
                .edit-Photo,
                .add-label,
                .add-Photo {
                    display: flex;
                    align-items: center;
                    width: 102px;
                    height: 32px;
                    line-height: 32px;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: 400;
                    cursor: pointer;
                }

                .batch-setting {
                    background: #FFFFFF;
                    border: 0.5px solid #DDE1E9;
                    color: #2E3C4E;

                    & i {
                        font-size: 14px;
                        padding: 0px 10px 0px 10px;
                    }

                    &:hover {
                        color: #409EFF;
                        border-color: #409EFF;
                    }
                }

                .edit-label,
                .edit-Photo,
                .add-label,
                .add-Photo {
                    // display: flex;
                    justify-content: center;
                    // align-items: center;
                    background: #FFFFFF;
                    border: 0.5px solid #DDE1E9;
                    color: #2E3C4E;
                    margin-top: 22px;
                }

                .edit-label:hover,
                .edit-Photo:hover,
                .add-label:hover,
                .add-Photo:hover {
                    background: #FFF;
                    border-color: #409EFF;
                    color: #409EFF;
                }

                .edit-Photo,
                .edit-label,
                .add-Photo {
                    margin-left: 24px;
                }

                .newly-label {
                    color: #8A929F;
                    background: #F1F4FA;
                    border-radius: 4px;
                    margin-left: 24px;

                    & i {
                        font-size: 12px;
                        padding: 0px 10px 0px 10px;
                    }

                    &:hover {
                        background-color: #e1e2e5;
                    }
                }

                .newly-category {
                    .el-button {
                        padding: 8px 13px;
                    }

                    .el-button--primary {
                        background-color: #2D84FB;
                        border-color: #2D84FB;
                    }

                    .el-button--primary:hover {
                        background: #66b1ff;
                        border-color: #66b1ff;
                        color: #FFF;
                    }

                    .el-button:active {
                        background-color: #3a8ee6;
                        border-color: #3a8ee6;
                        outline: 0;
                    }

                    & i {
                        font-size: 12px;
                    }
                }

                .newly-category,
                .create-works {
                    margin-left: 24px;
                }

                .create-works {
                    .el-button {
                        padding: 8px 13.5px;
                    }

                    .el-button--warning {
                        background-color: #FE6C17;
                        border-color: #FE6C17;
                    }

                    .el-button--warning:hover {
                        background: #eba67e;
                        border-color: #eba67e;
                        color: #FFF;
                    }

                    .el-button:active {
                        background-color: #d35f1c;
                        border-color: #d35f1c;
                        outline: 0;
                    }
                }
            }

            .el-form {
                .el-form-item:last-child {
                    display: block;
                }

                .el-form-item {
                    margin-right: 30px;

                    .el-form-item__label {
                        text-align: center;
                    }
                }
            }

            .demo-form-inline {
                .el-form-item:first-child {
                    .el-form-item__content {
                        .el-input {
                            .el-input__inner {
                                height: 40px;
                            }
                        }
                    }
                }

                .el-form-item:nth-child(2) {
                    .el-form-item__content {
                        .el-select {
                            .el-input {
                                .el-input__inner {
                                    height: 40px;
                                }
                            }
                        }
                    }
                }
            }
        }

        .links-menu {
            height: 24px;
            line-height: 24px;
            background-color: #F1F4FA;
        }

        .webVr_list {
            // padding: 0 24px;
            box-sizing: border-box;
            background-color: #F1F4FA;
            height: calc(100vh - 321px);
            overflow-y: auto;

            ::v-deep .webVr_main {
                padding: 24px 24px 0 24px;
                background-color: #FFFFFF;
                cursor: pointer;

                .webVr_edge {
                    display: flex;
                    padding-bottom: 24px;
                    border-bottom: 1px solid #F1F4FA;

                    .webVr-Func {
                        margin-left: auto;
                        height: 32px;

                        .webVr-edit {
                            margin-left: 12px;

                            .el-button {
                                padding: 8px 29px;
                            }

                            .el-button--warning {
                                background-color: #FE6C17;
                                border-color: #FE6C17;
                            }

                            .el-button--warning:hover {
                                background: #eba67e;
                                border-color: #eba67e;
                                color: #FFF;
                            }

                            .el-button:active {
                                background-color: #d35f1c;
                                border-color: #d35f1c;
                                outline: 0;
                            }
                        }

                        .webVr-delete,
                        .webVr-rename {
                            margin-left: 12px;

                            .el-button {
                                padding: 8px 29px;
                            }
                        }
                    }

                    .webVr-checkbox {
                        line-height: 160px;
                    }

                    .webVr-pictures {
                        width: 160px;
                        height: 160px;
                        background-color: #F8F8F8;
                        margin-left: 12px;
                        position: relative;

                        & img {
                            width: 100%;
                        }

                        .heat {
                            width: 100%;
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            position: absolute;
                            bottom: 0px;
                            padding: 13px;
                            box-sizing: border-box;
                            color: #FFFFFF;
                            font-size: 12px;

                            .iconSize {
                                font-size: 16px;
                            }
                        }
                    }

                    .webVr-title {
                        display: flex;
                        flex-direction: column;
                        margin-left: 24px;

                        .title {
                            font-size: 16px;
                            color: #2E3C4E;
                            font-weight: 600;
                        }

                        .introduction {
                            font-size: 14px;
                            color: #8A929F;
                            line-height: 20px;
                            font-weight: 400;
                            margin-top: 12px;
                        }

                        .label {
                            display: flex;
                            margin-top: 12px;

                            .label-box {
                                background: #F1F4FA;
                                border-radius: 4px;
                                padding: 4px 8px;
                                margin-right: 12px;
                                font-size: 12px;
                                color: #8A929F;
                                text-align: center;
                                line-height: 16px;
                                font-weight: 400;
                            }
                        }

                        .switch {
                            width: 88px;
                            box-sizing: border-box;
                            background: #FFFFFF;
                            font-size: 12px;
                            color: #2D84FB;
                            font-weight: 500;
                            margin-top: auto;
                            padding: 7px 19px;
                            border-radius: 4px;
                            border: 1px solid #DDE1E9;
                            cursor: pointer;

                            &:hover {
                                border-color: #2D84FB;
                            }
                        }
                    }
                }
            }

            .myPagination {
                text-align: end;
                padding-top: 24px;
                // background-color: #F1F4FA;
            }

            .webVr_main:hover {
                background-color: #f7faff;
                transition: .36s;
            }
        }
    }

    ::v-deep .Gglobal-footer {
        .el-button:nth-child(2) {
            border: none;
            font-size: 16px;
            padding: 13px 32px;
        }

        .el-button:nth-child(2):hover {
            background-color: #FFFFFF;
        }

        .el-button:last-child {
            font-size: 16px;
            padding: 12px 47px;
            background-color: #2D84FB;
            border-color: #2D84FB;
        }

        .el-button--primary:hover {
            background: #66b1ff;
            border-color: #66b1ff;
            color: #FFF;
        }

        .el-button--primary:active {
            background: #3a8ee6;
            border-color: #3a8ee6;
            color: #FFF;
        }
    }

    ::v-deep .EditWorks-box {
        .el-form {

            .el-form-item:nth-child(2),
            .el-form-item:nth-child(1) {
                .el-form-item__content {
                    .el-select {
                        width: 560px;
                    }
                }
            }
        }
    }

    ::v-deep .PhotoID {
        .el-form-item__content {
            .el-select {
                width: 560px;
            }
        }
    }

    ::v-deep .EditWorks-footer,
    .EditPhoto-footer {
        .el-button:first-child {
            border: none;
            font-size: 16px;
            padding: 13px 32px;
        }

        .el-button:first-child:hover {
            background-color: #FFFFFF;
        }

        .el-button:last-child {
            font-size: 16px;
            padding: 12px 47px;
            background-color: #2D84FB;
            border-color: #2D84FB;
        }

        .el-button:last-child:hover {
            background: #66b1ff;
            border-color: #66b1ff;
            color: #FFF;
        }

        .el-button:last-child:active {
            background: #3a8ee6;
            border-color: #3a8ee6;
            color: #FFF;
        }
    }

    ::v-deep .dialog-Photo {
        .el-dialog {
            .el-dialog__header {
                .el-dialog__title {
                    border-left: none;
                }
            }
        }
    }
}

::v-deep .Photo-details {
    .el-form-item__content {
        display: flex;
        flex-wrap: wrap;

        .photo-item {
            margin-right: 20px;

            .uploader-create {
                .el-upload {
                    position: relative;

                    .uploader-actions {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        cursor: default;
                        text-align: center;
                        color: #fff;
                        opacity: 0;
                        font-size: 20px;
                        background-color: rgba(0, 0, 0, .5);
                        transition: opacity .3s;

                        .uploader-actions-item {
                            margin: 0 10px;
                            font-size: 14px;
                            cursor: pointer;

                            & i {
                                color: #fff;
                            }
                        }
                    }

                    .uploader-actions:hover {
                        opacity: 1;
                    }
                }
            }

            .el-form {
                .el-form-item {
                    margin-top: 10px
                }
            }
        }
    }

    .photo-item-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.popoverMore {
    .popo_box {

        .popo_editLabel,
        .popo_classify,
        .popo_panoramaList {
            span {
                display: block;
                padding: 8px 10px;
                color: #2E3C4E;
                cursor: pointer;

                &:hover {
                    color: #FFFFFF;
                    background-color: #2D84FB;
                }
            }
        }
    }
}

.PanoramaPage {
    text-align: end;
    margin-top: 30px;
}
</style>