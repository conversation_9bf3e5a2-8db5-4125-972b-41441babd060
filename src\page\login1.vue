<template>
  <div v-if="boolAn" class="login-container" ref="targetElement" id="box_mask">
    <!-- 头部北京 -->
    <div class="login-top">
      <img class="imgWeb" :src="cusMapDetail.top_pic" alt="" mode="widthFix" />
    </div>
    <div class="nav">
      <div
        class="nav-as"
        v-for="(item, index) in cusMapDetail.maps"
        :key="item.id"
        @touchstart="touchStart(item.url, item.id)"
        @touchmove="touchMove"
      >
        <img class="imgWeb" :src="item.url" alt="" mode="widthFix" />
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="showScalePop" width="90%">
      <span
        >姓名：{{
          (shareInfo.admin_user && shareInfo.admin_user.user_name) || ""
        }}</span
      >
      <br />
      <span
        >电话：{{
          (shareInfo.admin_user && shareInfo.admin_user.phone) || ""
        }}</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="showScalePop = false">取 消</el-button>
        <el-button type="primary" @click="makePhoneCall">拨打电话</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import http1 from "@/utils/http1";
export default {
  name: "",
  data() {
    return {
      boolAn: true,
      token: "",
      wesite_id: "",
      id: "",
      ftime: "",
      shareId: "",
      cusMapDetail: {}, // 客户端获取地图详情
      interval: null, // 用于保存setInterval的ID
      browse_time: 0,
      nowResidue_time: "", // 剩余等待时间
      progressPercent: 0, // 进度条初始百分比
      is_download: false, // 控制浏览限制,图片是否可以下载
      is_close: false,
      allow_type3: true,
      showScalePop: false,
      nowmap_id: "", // 当前要下载地图图片的id
      nowplugin_id: 6, // 当前地图插件id
      sid: 0, // 分享者id
      lastWindowWidth: window.innerWidth,
      currentWindowWidth: window.innerWidth,
      shareInfo: {
        admin_user: {},
        msg: "",
      },
      allow_type: "", // 当前领取状态 -1：授权用户信息，还是填写表单 1：可以领取地图 2：已经领取过了
    };
  },
  created() {
    this.token = this.$route.query.token || "";
    this.wesite_id = this.$route.query.website_id || "";
    this.id = this.$route.query.id;
    this.ftime = this.$route.query.ftime || "";
    this.shareId = this.$route.query.shareId || "";
    if (this.$route.query.token) {
      localStorage.setItem("map_token", this.token);
      localStorage.setItem("web_id", this.wesite_id);
    }
    this.nowplugin_id = this.id;
    setTimeout(() => {
      this.getListMove();
    });
  },
  mounted() {
    this.onImageTouchStart(); // 开启长按菜单限制
    // 判断是否微信环境
    this.checkEnvironment();
    // this.$$nextTick(() => {
    window.addEventListener("resize", this.handleResize);
    // 获取要监听的目标元素
    const target = this.$refs.targetElement;

    // 创建一个 ResizeObserver 实例
    this.observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        // 在元素大小变化时触发的回调函数
        console.log("目标元素发生大小变化了", entry);

        // 在这里可以添加发送请求的逻辑
        this.sendResizeRequest();
      }
    });

    // 开始监听目标元素的大小变化
    this.observer.observe(target);
    // });
  },
  computed: {
    show_time() {
      return (
        this.browse_time < parseInt(this.cusMapDetail.browse_time) * 60 &&
        this.cusMapDetail.browse_auth == 1 &&
        !this.is_close
      );
    },
    scaleMax() {
      if (this.cusMapDetail.receive_type == 3 && this.allow_type3 == 1) {
        return 20;
      } else {
        return 1;
      }
    },
  },
  methods: {
    checkEnvironment() {
      // return
      if (this.isWeChatEnvironment()) {
        // 在微信环境下，执行登录逻辑
        // console.log('是微信');
        return;
      } else {
        // 不在微信环境下，提示只能在微信浏览器打开
        this.boolAn = false;
        alert("当前浏览器不支持，请使用微信小程序");
      }
    },
    isWeChatEnvironment() {
      // 判断是否在微信浏览器环境下
      return window.__wxjs_environment === "miniprogram";
    },
    // 打电话
    makePhoneCall() {
      let phoneNumber = this.shareInfo.admin_user.phone
        ? this.shareInfo.admin_user.phone
        : "";
      window.location.href = `tel:${phoneNumber}`;
    },
    async sendResizeRequest() {
      // 在这里添加发送请求的逻辑
      if (this.alist) return;
      this.alist = true;
      setTimeout(() => {
        this.alist = false;
      }, 1500);
      if (this.cusMapDetail.receive_type == 3) {
        const res = await http1.receiveList(this.nowplugin_id);
        if (res.status == 200) {
          this.allow_type = this.allow_type3 = res.data.allow_type;
        } else if (res.statusCode == 401) {
          console.log("去登录");
        }
        setTimeout(() => {
          this.scale = false;
        }, 2000);
      }
    },
    handleResize() {
      // 处理窗口大小变化的逻辑
      // 更新当前窗口宽度
      this.currentWindowWidth = window.innerWidth;

      // 判断是否发生了画面缩放
      if (this.currentWindowWidth !== this.lastWindowWidth) {
        console.log("用户进行了画面缩放");
      }
      // 更新上次窗口宽度
      this.lastWindowWidth = this.currentWindowWidth;

      if (this.scale) return;
      this.scale = true;
    },
    // 请求
    async getListMove() {
      let obj = {
        share_time: this.ftime,
        share_id: this.shareId,
      };
      console.log(obj, "objobjobj");
      const res = await http1.getList(this.id, obj);
      if (res.status == 200) {
        if (res.data.allow_type == 2) {
          if (
            res.data.admin_user &&
            Array.isArray(res.data.admin_user) &&
            res.data.admin_user.length == 0
          ) {
            this.$message.warning(res.data.msg);
          } else {
            this.$nextTick(() => {
              this.shareInfo = res.data;
              this.showScalePop = true;
            });
          }
          return;
        }
        this.cusMapDetail = res.data;
        console.log(this.cusMapDetail, "this.cusMapDetailthis.cusMapDetail");
        document.title = this.cusMapDetail.title;
        // 有问题点
        // let show_time =
        //   localStorage.getItem("colllarMap" + this.id) && !this.is_new_login;
        if (this.cusMapDetail.browse_auth == 1) {
          this.startCountdown(); // 控制浏览时间
        } else {
          this.is_close = true;
          this.is_download = true; // 关闭浏览限制
        }
        if (this.shareId > 0) {
          this.getShareInfo();
        }
      } else if (res.statusCode == 401) {
        return;
      }
    },
    async getShareInfo() {
      const res = await http1.redMoveList(this.shareId);
      console.log(res, "shareId出来了吗");
      if (res.status == 200) {
        if (Array.isArray(res.data.admin_user)) {
          res.data.admin_user = {};
        }
        this.shareInfo = res.data;
      } else if (res.statusCode == 401) {
        console.log("去登录");
      }
    },
    // 控制浏览时间
    startCountdown() {
      let waiting_time = parseInt(this.cusMapDetail.browse_time) * 60; // 需要浏览等待的时间
      this.interval = setInterval(() => {
        if (this.browse_time < waiting_time) {
          this.browse_time++; // 减少所需浏览时间
          this.nowResidue_time = waiting_time - parseInt(this.browse_time); // 剩余等待时间
          this.progressPercent = (this.browse_time / waiting_time) * 100; // 更新进度条百分比
        } else {
          this.stopCountdown(); // 浏览时间为0时停止计时
          this.is_download = true; // 关闭浏览限制
        }
      }, 1000);
    },
    // 停止浏览计时
    stopCountdown() {
      clearInterval(this.interval);
    },
    // 开始按下触碰
    touchStart(url, id) {
      let _this = this;
      _this.nowmap_id = id;
      clearTimeout(_this.timer); //再次清空定时器，防止重复注册定时器
      _this.timer = setTimeout(() => {
        _this.checkStatus(
          function () {
            if (
              _this.is_download &&
              _this.allow_type == 1 &&
              _this.cusMapDetail.receive_type == 1
            ) {
              // 赋值当前要下载图片的id;
              _this.saveImage(url);
              _this.saveMapClickRecode(); // 保存地图点击记录
            } else if (_this.cusMapDetail.receive_type == 3) {
              _this.saveMapClickRecode(); // 保存地图点击记录
            } else {
              _this.saveMapClickRecode(); // 保存地图点击记录
            }
          },
          function () {
            _this.saveMapClickRecode(); // 保存地图点击记录
          },
          function (res) {
            if (_this.cusMapDetail.receive_type == 3) {
              if (_this.sid) {
                _this.showScalePop = true;
              } else if (_this.shareInfo.msg) {
                _this.$message.warning(_this.shareInfo.msg);
              } else {
                _this.$message.warning(res.msg);
              }
              return;
            }
            _this.$message.warning(res.msg);
          }
        );
      }, 1000);
    },
    async checkStatus(callback, callback1, callback2) {
      console.log("触发长按事件");
      // is_download: 是否还在浏览限制
      if (this.is_download) {
        // 判断当前领取状态
        const res = await http1.receiveList(this.nowplugin_id);
        console.log(res, "点击");
        if (res.statusCode == 200) {
          this.allow_type = this.allow_type3 = res.data.allow_type;
          // -1 去授权 1 可以领取 2 领取过了
          if (res.data.allow_type == -1) {
            console.log("去登录");
          } else if (res.data.allow_type == 1) {
            callback && callback();
          } else if (res.data.allow_type == 2) {
            this.saveMapClickRecode(); // 保存地图点击记录
            callback2 && callback2(res.data);
          }
          //    return  this.allow_type = res.data.allow_type; // 赋值当前领取状态
        } else if (res.statusCode == 401) {
          console.log("去登录");
        }
      } else {
        callback1 && callback1();
      }
    },
    // 结束触碰
    touchEnd() {
      clearTimeout(this.timer); //手指离开
    },
    // 触碰滑动
    touchMove() {
      clearTimeout(this.timer);
    },
    // 阻止显示长按菜单默认行为
    prevent(event) {
      console.log("阻止默认行为");
      event.preventDefault(); // 阻止默认的上下文菜单
    },
    // 长按菜单限制
    onImageTouchStart() {
      document.addEventListener("contextmenu", this.prevent); // 开启长按菜单限制
    },
    // 保存地图点击记录
    async saveMapClickRecode() {
      let obj = {
        map_id: this.nowmap_id,
        plugin_id: this.id,
      };
      const res = await http1.saveSign(obj);
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer); // 清空定时器
    // 在组件销毁前移除监听器，以避免内存泄漏
    window.removeEventListener("resize", this.handleResize);
    document.removeEventListener("contextmenu", this.prevent); // 解除长按菜单限制
  },
};
</script>
<style scoped lang="scss">
.login-container {
  user-select: none;
}
@media screen and (max-width: 768px) {
  // 移动端适配rem
  .login-container {
    width: 100%; /* 适应屏幕宽度 */
    height: 100%;
    // padding: .625rem;
    box-sizing: border-box;
    overflow-y: scroll;
    .login-top {
      width: 100%;
      img {
        width: 100%;
        object-fit: cover;
      }
    }
    .nav {
      width: 100%;
      padding-bottom: 6.25rem;
      box-sizing: border-box;
      .nav-as {
        width: 100%;
        box-sizing: border-box;
      }
      // margin-top: .625rem;
      img {
        padding: 0.1%;
        box-sizing: border-box;
      }
    }
  }
  .imgWeb {
    width: 100%;
    display: block;
    pointer-events: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -moz-user-select: none;
    user-select: none;
  }
  /deep/.el-dialog {
    margin-top: 37vh !important;
  }
}
</style>