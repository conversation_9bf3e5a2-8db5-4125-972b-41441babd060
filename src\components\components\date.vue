<template>
  <div id="calendar">
    <!-- 年份 月份 -->
    <div class="month__date">
      <ul>
        <!--点击会触发pickpre函数，重新刷新当前日期 @click(vue v-on:click缩写) -->
        <li class="year-month">
          <span class="choose-year">{{ currentYear }}-</span>
          <span class="choose-month">{{ currentMonth }}</span>
        </li>
        <i
          class="el-icon-arrow-left arrow"
          @click="pickPre(currentYear, currentMonth)"
        ></i>
        <i
          class="el-icon-arrow-right arrow"
          @click="pickNext(currentYear, currentMonth)"
        ></i>
      </ul>
    </div>
    <!-- 星期 -->
    <ul class="weekdays">
      <li>SUN</li>
      <li>MON</li>
      <li>TUE</li>
      <li>WED</li>
      <li>THU</li>
      <li>FRI</li>
      <li>SAT</li>
    </ul>
    <!-- 日期 -->
    <ul class="days">
      <!-- 核心 v-for循环 每一次循环用<li>标签创建一天 -->
      <li v-for="(dayobject, i) in days" :key="i">
        <!--本月-->
        <!--如果不是本月  改变类名加灰色-->
        <span
          v-if="dayobject.day.getMonth() + 1 != currentMonth"
          class="other-month"
          @click="getDayTime(dayobject.day)"
          >{{ dayobject.day.getDate() }}</span
        >
        <!--如果是本月  还需要判断是不是这一天-->
        <span v-else>
          <!--今天  同年同月同日-->
          <span
            :class="{
              active:
                dayobject.day.getFullYear() == new Date().getFullYear() &&
                dayobject.day.getMonth() == new Date().getMonth() &&
                dayobject.day.getDate() == new Date().getDate(),
            }"
            @click="getDayTime(dayobject.day)"
            >{{ dayobject.day.getDate() }}</span
          >
          <!-- <span v-else @click="getDayTime(dayobject.day)">{{
            dayobject.day.getDate()
          }}</span> -->
        </span>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentDay: 1,
      currentMonth: 1,
      currentYear: 2021,
      currentWeek: 1,
      days: [],
    };
  },
  created: function() {
    // 在vue初始化时调用
    this.initData(null);
  },
  methods: {
    initData: function(cur) {
      // var leftcount = 0 // 存放剩余数量
      var date;
      if (cur) {
        date = new Date(cur);
      } else {
        var now = new Date();
        var d = new Date(this.formatDate(now.getFullYear(), now.getMonth(), 1));
        d.setDate(34);
        date = new Date(this.formatDate(d.getFullYear(), d.getMonth() + 1, 1));
      }
      this.currentDay = date.getDate();
      this.currentYear = date.getFullYear();
      this.currentMonth = date.getMonth() + 1;
      this.currentWeek = date.getDay(); // 1...6,0
      if (this.currentWeek === 0) {
        this.currentWeek = 7;
      }
      var str = this.formatDate(
        this.currentYear,
        this.currentMonth,
        this.currentDay
      );
      this.days.length = 0;
      // 今天是周日，放在第一行第7个位置，前面6个
      // 初始化本周
      for (var i = this.currentWeek; i >= 0; i--) {
        var d2 = new Date(str);
        d2.setDate(d2.getDate() - i);
        var dayobjectSelf = {}; // 用一个对象包装Date对象  以便为以后预定功能添加属性
        dayobjectSelf.day = d2;
        this.days.push(dayobjectSelf); // 将日期放入data 中的days数组 供页面渲染使用
      }
      // 其他周
      for (var j = 1; j <= 34 - this.currentWeek; j++) {
        var d3 = new Date(str);
        d3.setDate(d3.getDate() + j);
        var dayobjectOther = {};
        dayobjectOther.day = d3;
        this.days.push(dayobjectOther);
      }
    },
    getDayTime(el) {
      let date = new Date(el);
      let y = date.getFullYear();
      let m = date.getMonth() + 1;
      m = m < 10 ? "0" + m : m;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      const time = y + "-" + m + "-" + d;
      this.currentDay = d;
      this.$emit("onClickTime", time);
    },
    pickPre: function(year, month) {
      // setDate(0); 上月最后一天
      // setDate(-1); 上月倒数第二天
      // setDate(dx) 参数dx为 上月最后一天的前后dx天
      var d = new Date(this.formatDate(year, month, 1));
      d.setDate(0);
      this.initData(this.formatDate(d.getFullYear(), d.getMonth() + 1, 1));
    },
    pickNext: function(year, month) {
      var d = new Date(this.formatDate(year, month, 1));
      d.setDate(35);
      this.initData(this.formatDate(d.getFullYear(), d.getMonth() + 1, 1));
    },
    // 返回 类似 2016-01-02 格式的字符串
    formatDate: function(year, month, day) {
      var y = year;
      var m = month;
      if (m < 10) m = "0" + m;
      var d = day;
      if (d < 10) d = "0" + d;
      return y + "-" + m + "-" + d;
    },
  },
};
</script>
<style>
#calendar {
  font-size: 14px;
  width: 100%;
  margin: 0 auto;
  cursor: pointer;
  margin-top: 12px;
}
.month__date {
  position: absolute;
  top: 18px;
  right: 24px;
  color: #8a929f;
  width: 120px;
}
.month__date ul {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: space-between;
  height: 35px;
  align-items: center;
}
.year-month {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.choose-month {
  text-align: center;
  font-size: 14px;
}
.arrow {
  height: 24px;
  width: 24px;
  text-align: center;
  line-height: 24px;
  background: #fff;
  color: #8a929f;
  border-radius: 50%;
}

.month ul li {
  font-size: 14px;
  text-transform: uppercase;
}
.weekdays {
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  color: #8a929f;
  justify-content: space-around;
}
.weekdays li {
  display: inline-block;
  width: 13.6%;
  text-align: center;
}
.days {
  background: #ffffff;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}
.days li {
  list-style-type: none;
  display: inline-block;
  width: 14.2%;
  text-align: center;
  padding-top: 10px;
  font-size: 14px;
  color: #2e3c4e;
  line-height: 32px;
}
.days li .active {
  display: inline-block;
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 50%;
  background: #fef0e7;
  color: #fe6c17;
}
.days li .other-month {
  padding: 5px;
  color: #dde1e9;
}
.days li:hover > span > span {
  display: inline-block;
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 50%;
  background: #fe6c17;
  box-shadow: 0px 2px 6px 0px rgba(254, 108, 23, 0.4);
  color: #fff;
}
</style>
