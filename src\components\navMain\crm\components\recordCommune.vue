<template>
    <div class="communication">
        <div class="header-box flex-row">
            <div class="title flex-row">
                沟通记录
                <!-- <div class="edit-name">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="">
                </div> -->
            </div>
            <!-- <div class="search">
                <el-input 
                    v-model="search_params.key" 
                    placeholder="搜索"
                    suffix-icon="el-icon-search"
                ></el-input>
            </div> -->
        </div>
        <div class="main-box flex-box">
            <div class="content flex-box" v-infinite-scroll="loadRecordList">
                <div class="message-box" :style="item.is_admin == 1 ? 'justify-content: flex-end; margin-top: 30px;' : ''"
                    v-for="(item, index) in recording_list" :key="index">
                    <!-- 用户 -->
                    <div v-if="item.is_admin == 0" class="flex-box">
                        <div class="flex-row user-info">
                            <div style="margin-right: 20px;">{{ item.user_info.user_name }}</div>
                            <div>{{ item.begin_time | formatTime }}~</div>
                            <div>{{ item.end_time | formatTime }}</div>
                            <div class="play-button" @click="selectPlay(item)">
                                <img src="@/assets/<EMAIL>" alt="" />
                            </div>
                        </div>
                        <div class="flex-row user-content">
                            <div style="width: 55px; height: 55px;">
                                <img style="width: 100%; height: 100%;"
                                    :src="item.user_info && item.user_info.avatar ? item.user_info.avatar : '@/assets/<EMAIL>'"
                                    alt="" />
                            </div>
                            <div class="triangle"></div>
                            <span class="text">{{ item.text }}</span>
                        </div>
                    </div>
                    <!-- 系统成员 -->
                    <div v-else>
                        <div class="flex-row admin-info">
                            <div class="play-button" @click="selectPlay(item)">
                                <img src="@/assets/<EMAIL>" alt="" />
                            </div>
                            <div>{{ item.begin_time | formatTime }}~</div>
                            <div>{{ item.end_time | formatTime }}</div>
                            <div class="flex-row" style="margin-left: 10px;">
                                <div class="department-box" v-if="item.user_info && item.user_info.department.length">
                                    <span v-for="(list, idx) in item.user_info.department" :key="idx+'i'">
                                        <span>{{ list.name }}</span>
                                        <span v-show="idx != item.user_info.department.length - 1">/</span>
                                    </span>
                                </div>
                                <span style="margin-left: 20px;">{{ item.user_info.user_name }}</span>
                            </div>
                        </div>
                        <div class="flex-row user-content">
                            <div class="triangle-admin"></div>
                            <span class="text-admin">{{ item.text }}</span>
                            <div style="width: 55px; height: 55px;">
                                <img style="width: 100%; height: 100%;" :src="item.user_info.avatar" alt="" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="separation"></div>
        <div class="foot-box">
            <communicationPlayer ref="radio" v-if="recordSource.id && recordSource.record_url != ''"
                :activity="recordSource" :record_url="recordSource.record_url" :duration_time="recordSource.duration"
                :is_detail="true" :begin_time="begin_time" :end_time="end_time" :speed="true"
                style_audioRight="background: #fff; flex: 9;" style_container="margin-top: 0px;" style_line="500"
                @resetPlay="resetPlay"></communicationPlayer>
        </div>
    </div>
</template>
<script>
import communicationPlayer from "@/components/components/communicationPlayers.vue"
export default {
    components: {
        communicationPlayer,
    },
    props: {
        // 录音id
        record_id: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            options: [],
            search_params: {
                key: "",
            },
            // 识别列表参数
            recognition_params: {
                page: 1, // 页码
                per_page: 10, // 条数
                record_id: '', // 录音id
            },
            recording_list: [], // 录音识别列表
            is_loadMore: true, // 用于判断下拉加载
            begin_time: '', // 录音开始时间
            end_time: '', // 录音结束时间
            recordSource: {}, // 当前录音id、url等
        }
    },
    created() {
        this.getRecordRecognition(); // 获取识别列表
    },
    filters: {
        formatTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;

            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
        },
    },
    methods: {
        resetPlay() {
            this.recording_list.is_play = !this.recording_list.is_play
            console.log("执行重置", this.recording_list.is_play);
        },
        // 获取识别列表
        getRecordRecognition() {
            if(!this.recognition_params.record_id) {
                this.recognition_params.record_id = this.record_id; // 赋值录音id
            }
            this.$http.getRecordRecognition(this.recognition_params).then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"识别列表");
                    res.data.list.map((item) => {
                        item.is_play = false; // 用于控制播放按钮
                    })
                    this.recording_list = this.recording_list.concat(res.data.list);
                    this.recordSource = res.data.record;
                    if(res.data.list.length < (this.recognition_params.per_page || 10)) {
                        this.is_loadMore = true;
                    } else {
                        this.is_loadMore = false;
                    }
                }
            })
        },
        // 下拉加载
        loadRecordList() {
            if(this.is_loadMore) {
                return;    
            }
            this.recognition_params.page++;
            this.getRecordRecognition();
        },
        // 选择播放录音
        selectPlay(item) {
            // console.log(item.begin_time,"item");
            this.begin_time = Math.floor(item.begin_time / 1000); // 赋值录音开始时间
            this.end_time = Math.floor(item.end_time / 1000); // 赋值录音结束时间
            this.$nextTick(() => {
                this.$refs.radio.beginTime();
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.communication {
    width: 100%;
    height: 730px;
    padding-top: 27px;
    box-sizing: border-box;
    background: #FFFFFF;

    .header-box {
        justify-content: space-between;
        padding: 0 42px 0 34px;
        margin-bottom: 22px;

        .title {
            color: #2E3C4E;
            font-size: 20px;
            font-weight: bold;
            align-items: center;

            .edit-name {
                width: 25px;
                height: 25px;
                cursor: pointer;

                img {
                    width: 100%;
                }
            }
        }

        .search {
            width: 224px;
            height: 40px;

            ::v-deep .el-input__icon {
                color: #8A929F;
            }
        }
    }

    .main-box {
        // width: 100%;
        height: 554px;
        overflow-y: auto;
        // padding: 0 25px 0 18px;
        margin: 0 25px 0 18px;
        background: #FAFBFE;

        // box-sizing: border-box;
        .content {
            // height: 100%;
            background: #FAFBFE;
            padding: 24px 16px;

            .message-box {
                display: flex;
                color: #2E3C4E;
                font-size: 16px;
                padding-bottom: 50px;
                position: relative;

                .user-info,
                .admin-info {
                    margin-bottom: 10px;

                    .department-box {
                        max-width: 240px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .play-button {
                        width: 20px;
                        height: 20px;
                        cursor: pointer;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }

                .user-info {
                    margin-left: 80px;

                    .play-button {
                        margin-left: 12px;
                    }
                }

                .admin-info {
                    position: absolute;
                    top: -30px;
                    right: 0px;
                    margin-right: 80px;

                    .play-button {
                        margin-right: 12px;
                    }
                }

                .user-content {
                    position: relative;

                    .text,
                    .text-admin {
                        display: flex;
                        align-items: center;
                        max-width: 458px;
                        padding: 13px 20px;
                        border-radius: 10px;
                        background: #E8F1FF;
                    }

                    .text {
                        margin-left: 24px;
                    }

                    .text-admin {
                        margin-right: 24px;
                    }

                    .triangle,
                    .triangle-admin {
                        width: 0px;
                        height: 0px;
                        border: 10px solid transparent;
                        position: absolute;
                    }

                    .triangle {
                        border-right-color: #E8F1FF;
                        top: 20px;
                        left: 60px;
                    }

                    .triangle-admin {
                        border-left-color: #E8F1FF;
                        top: 20px;
                        right: 85px;
                    }
                }
            }
        }
    }

    .main-box::-webkit-scrollbar {
        width: 6px;
    }

    .main-box::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background: #DDE1E9;
    }

    .main-box::-webkit-scrollbar-button {
        display: none;
    }

    .separation {
        border-top: 1px solid #DDE1E9;
    }

    .foot-box {
        width: 100%;
        height: 86px;
        display: flex;
        flex-direction: row;
        align-items: center;
        box-sizing: border-box;
        padding: 0 28px 0 12px;
    }
}</style>