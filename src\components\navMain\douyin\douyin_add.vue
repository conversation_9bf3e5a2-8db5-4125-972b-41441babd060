<template>
  <div class="douyin_fabu flex-row">
    <div class="douyin_fabu_left flex-1  mr24">
      <div class="title_size mt24">抖音创作助手</div>
      <div class="douyin_tips mt12 flex-row">
        <div class="douyin_tips_icon mr12">
          <img :src="
                        $imageFilter(
                          'https://img.tfcs.cn/backup/static/admin/douyin/douyin_add_icon.png',
                          'w_80'
                        )
                      "></img>
        </div>
        <div class="douyin_tips_content">
          <div class="douyin_content_c">
            适用于抖音图文、短视频快速发布。可预设文案，无需拍摄后期制作、缩短内容制作时长，更加快速的内容创作和涨粉。
          </div>

        </div>
      </div>
      <!--传作助手提示语结束 -->
      <div class="title_size mt24">标题描述</div>
      <div class="descript  mt12">
        <p id='test-autocomplete-textarea' class="tribute-demo-input" ref="at_friend" placeholder="Enter some text here">
        </p>
        <!-- <textarea  id ='test-autocomplete-textarea' ref ="at_friend" rows="5" v-model ="params.text" ></textarea> -->
        <div class="descript_other flex-row mt12">
          <div class="descript_other_label mr12" @click="addAt('#')">
            #添加话题
          </div>
          <div class="descript_other_label mr12" @click="addAt('@')">
            @好友
          </div>
        </div>
        <div class="colleague-box" v-show="showDropdown">
          <div class="colleague_content" v-for="(item, index) in userList" :key="index" @click="addUser(item)">
            {{ item.name }}
          </div>
        </div>

      </div>
      <!-- 标题描述 结束 -->
      <div class="title_size space_between cur_piont flex-row mt24">
        <div class='cur_piont'>
          上传作品
        </div>
        <div class='cur_piont'>
          <el-button v-if='!(imgList.length&& !params.image_list.length) || params.media_id.length' @click="showSucai"
            size="small">素材库选择</el-button>
        </div>
      </div>
      <div class="upfile mt12">
        <div class="file_content flex-row">
          <div class="image_list" v-if='files.length==0'>
            <template v-if='imgList.length'>
              <div class="img mr24" v-for="(img,index) in imgList" :key="index">
                <img :src="img.url" alt="">
                <div class="hover">
                  <div class="delete flex-row">
                    <i class="el-icon-delete mr12" @click="removeImg(index)"></i>
                    <!-- <i class="el-icon-view"></i> -->
                  </div>

                </div>
              </div>
            </template>
            <el-upload class="upload-demo upload-img mr12" accept=".jpg,.png,.jpeg" v-if="showAdd" :action="uploadImgUrl"
              :headers="headers" multiple :show-file-list="false" :before-upload="handleBeforeUploadImg"
              :on-success="uploadSuccess">
              <!-- v-if ="showAdd ||(imgList.length==0 &&!showAdd)" -->
              <div class="img_c ">
                <div class="img_c_tet">上传图片</div>
                <div class="img_c_add">+</div>
              </div>
            </el-upload>
          </div>

          <template v-if="!imgList.length">
            <el-upload class="upload-demo upload-video" accept=".mp4" :auto-upload="false" action=""
              :show-file-list="false" :on-change="handleChange" :before-upload="handleBeforeUpload">
              <div class="img_c " v-if='files.length==0'>
                <div class="img_c_tet">上传视频</div>
                <div class="img_c_add">+</div>
              </div>
              <div class="video_pre" v-else>
                <video :src="files[0].url" class="video" preload="none">
                </video>
                <div class="hover" v-if='files.length>0'>
                  <div class="delete flex-row">
                    <i class="el-icon-delete mr12" @click.prevent.stop="removeVideo"></i>
                  </div>
                </div>
              </div>
            </el-upload>

          </template>
        </div>
      </div>
      <!-- 上传作品 结束 -->
      <!-- <div class="title_size mt24">
        添加位置
      </div>
      <div class="add_address mt12">
        <el-select >

        </el-select>
      </div> -->
      <!-- 添加位置 结束 -->
      <div class="title_size mt24" v-if='params.type==2'>
        设置封面
      </div>
      <div class="set_cover mt12" v-if='params.type==2'>
        <el-input style="width:220px" v-model='params.cover_tsp' placeholder="将输入的指定时间点对应帧设置为视频封面"></el-input>
      </div>
      <div class="title_size mt24">
        小程序设置
      </div>
      <div class="set_cover mt12">
        <el-form>
          <el-form-item label="小程序名称" label-width="100px">
            <el-input style="width:220px" v-model="params.micro_app_title" placeholder="请输入小程序名称"></el-input>
          </el-form-item>
          <el-form-item label="小程序APPID" label-width="100px">
            <el-input style="width:220px" v-model="params.micro_app_id" placeholder="请输入小程序APPID"></el-input>
          </el-form-item>
          <el-form-item label="小程序路径" label-width="100px">
            <el-input style="width:220px" v-model="params.micro_app_url" placeholder="请输入小程序路径"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- <div class="title_size mt24">
        选择封面
      </div>
      <div class="upfile mt12">
        <div class="file_content flex-row">
          
          <el-upload class="upload-demo upload-img mr12"
            accept=".jpg,.png"
            :before-upload="handleBeforeUpload"
            :on-success="handleUpload"
            :on-progress="handleProgress">
              <div class="img_c ">
                <div class="img_c_tet">上传图片</div>
                <div class="img_c_add">+</div>
              </div>
          </el-upload>
          
        </div>
      </div> -->

      <div class="oper flex-row">
        <!-- <div class="oper_l">
          存草稿
        </div> -->
        <div class="oper_r flex-1 flex-row">
          <div class="cancel">
            取消
          </div>
          <div class="submit" @click='submit'>
            确定
          </div>
        </div>
      </div>
    </div>
    <div class="douyin_fabu_right">
    </div>
    <el-dialog :visible.sync="show_sucai" width="970px" title="素材库选择">
      <template v-if='show_sucai'>


        <div class="filter mt24 flex-row">
          <div class="filter_list flex-1 flex-row items-center">
            <div class="filter_item" :class="{ active: sucai_params.type == 1 }" @click="
                          sucai_params.page = 1;
                          sucai_params.type = 1;
                          getSucaiList();
                        ">
              图片
            </div>
            <div class="line"></div>
            <div class="filter_item" :class="{ active: sucai_params.type == 2 }" @click="
                          sucai_params.page = 1;
                          sucai_params.type = 2;
                          getSucaiList();
                        ">
              视频
            </div>
          </div>
        </div>
        <div class="list flex-row" v-infinite-scroll="loadMore">
          <div class="card" v-for="(item,index) in sucaiList" :key="item.id">
            <div class="card_img">
              <img :src="item.url" alt="" />
              <div class="check_info" @click="selectSucai(item,index)" :class="{ checked: item.check == true }">
                <i v-if="item.check" class="el-icon-check" style="color: #2d84fb"></i>
                <!-- <img src="" alt=""> -->
              </div>
            </div>
            <div class="card_title flex-row">
              <div class="title_name">
                {{ item.title }}
              </div>
              <div class="title_r">已有{{ item.use_count }}人使用</div>
            </div>
            <!-- <div class="card_xiangmu">项目名称：{{ item.build }}</div> -->
            <div class="card_user flex-row">
              <div class="name">上传人：{{ item.admin_name }}</div>
              <div class="time">
                {{ item.created_at }}
              </div>
            </div>
          </div>
        </div>

        <div class="oper flex-row">
          <div class="oper_r flex-1 flex-row">
            <div class="cancel" @click="show_sucai =false">
              取消
            </div>
            <div class="submit" @click='selectOk'>
              确定
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import axios from "axios"
import Tribute from "tributejs";

export default {
  data() {
    return {
      headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") },
      params: {
        type: 1, //图片还是视频
        image_list: [],//抖音图片id 多个用,隔开
        text: "",  //标题 
        at_users: [],  //@nickname 对应的open_id
        micro_app_title: "", //小程序标题
        micro_app_url: "",//小程序链接
        micro_app_id: "",//小程序appid
        // poi_id: '',   //地理位置id
        cover_tsp: '', //视频时 将指定的时间点对应帧设置为视频封面
        // pio_commerce: 0, //视频时如果用户拥有门店推广能力则 用户发布视频所加的位置默认开启门店推广
        video_id: ""
      },
      time: true,
      imgList: [],
      files: [],  // 选中的文件列表
      uploading: false,  // 是否正在上传
      percent: 0,  // 上传进度
      uploadImgUrl: '/api/common/file/upload/admin?category=101',
      userList: [],
      showDropdown: false,
      show_sucai: false,
      sucaiList: [],
      sucai_params: {
        page: 1,
        page_size: 10,
        type: 1
      },
      showAdd: true
    }
  },
  mounted() {
    let _this = this
    this.atFriendsList = []
    this.tributeAutocompleteTestArea = new Tribute({
      values: (text, cb) => {
        this.remoteSearch(text, users => {
          cb(users)
        });
      },
      selectTemplate: function (item) {
        if (typeof item === "undefined") return null;
        if (this.range.isContentEditable(this.current.element)) {
          return (
            `<a  contenteditable="false" href="javascript:void(0)" style="color: #0088ff; text-decoration: none;" data-value='${item.original.value}'>@ ${item.original.key} &nbsp </a>`
          );
        }


        return "@" + item.original.key;
      },
      noMatchTemplate: function () {
        return "<li>暂无数据</li>"
      },
      requireLeadingSpace: false
    });
    this.tributeAutocompleteTestArea.attach(document.getElementById("test-autocomplete-textarea"));
    document
      .getElementById("test-autocomplete-textarea")
      .addEventListener("tribute-replaced", function (e) {
        _this.atFriendsList.push(e.detail.item.original)
        console.log("Original Event:", e.detail.event);
        console.log("Matched item:", e.detail.item);
      });

    var noMatchRunOnce = false;
    document
      .getElementById("test-autocomplete-textarea")
      .addEventListener("tribute-no-match", function (e) {
        if (noMatchRunOnce) return;
        // var values = [
        //   {
        //     key: "Cheese Tacos",
        //     value: "Cheese Tacos",
        //     email: "<EMAIL>"
        //   }
        // ];
        // this.tributeAutocompleteTestArea.appendCurrent(values);
        noMatchRunOnce = true;
      });
  },
  methods: {
    // 分片上传
    async upload(file, upload_id) {
      let headers = { Authorization: "Bearer " + localStorage.getItem("TOKEN") }
      const fileSize = file.size;  // 文件大小
      let chunkSize = 1024 * 1024 * 5; // 每个块的大小为 5MB
      let chunks = Math.ceil(fileSize / chunkSize);
      if (fileSize % chunkSize < 1024 * 1024 * 5 && fileSize % chunkSize !== 0) {
        // 总片数 chunks
        chunks = chunks - 1
        this.lastLowerThanFive = true
      }
      // 总块数
      const tasks = [];  // 上传任务数组
      let uploaded = 0;  // 已上传块数


      // 文件切割
      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize;
        let end = Math.min(start + chunkSize, fileSize);
        // 最后一个少于5M 最后一片 的最大值就是file的大小
        if (i == chunks - 1 && this.lastLowerThanFive) {
          end = fileSize
        }
        tasks.push(
          new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file.raw.slice(start, end, file.raw.type), file.name);  // 块数据
            axios
              .post(this.uploadUrl, formData, { headers, timeout: 600000 })  // 上传块数据
              .then(async (res) => {
                if (res.status == 200) {
                  let fenpianUpRes = await axios
                    .post('/api/admin/byte_dance/upload_media_part', { url: res.data.url, part_number: i + 1, upload_id }, { headers })  // 上传块数据
                  if (fenpianUpRes.status == 200) {
                    uploaded++;
                    this.percent = Math.floor((uploaded / chunks) * 100);
                    this.loading.setText(this.percent + '%');
                    resolve(res.data.url);
                  } else {
                    reject(res)
                  }

                } else {
                  reject(res);
                }
              })
              .catch(err => {
                reject(err);
              });

          })
        );
      }

      // 待所有块上传完成后，发送合并请求
      await Promise.all(tasks);
      // 上传完成
      const finishAll = await axios.post('/api/admin/byte_dance/complete_upload_media_part', { upload_id }, { headers });
      console.log(finishAll);
      if (finishAll.status === 200) {
        this.params.video_id = finishAll.data.video.video_id
      } else {
        this.params.video_id = ''
      }
    },
    handleChange(files, fileList) {
      console.log(files, fileList);
      this.params.type = 2
      this.files = fileList;

    },
    addUser(item) {
      let tpl = `<a
          href="javascript:void(0)"
          style='color: #0088ff; text-decoration: none;'
          contenteditable='false'
          data-value ='${item.value}'
        >
          @${item.key}
        </a>&nbsp;`
      let sel = this.$refs.at_friend
      sel.innerHTML += tpl;
      this.atFriendsList.push(item)
      let range = window.getSelection()
      range.selectAllChildren(sel);
      range.collapseToEnd();
      this.showDropdown = false
    },
    async addAt(type) {
      let obj = this.$refs.at_friend
      if (type == "@") {
        await this.remoteSearch("", () => { })
        this.showDropdown = true
        return
      }

      this.$refs.at_friend.innerText += type
      this.$nextTick(() => {
        if (window.getSelection) { //ie11 10 9 ff safari
          obj.focus(); //解决ff不获取焦点无法定位问题
          let range = window.getSelection(); //创建range
          range.selectAllChildren(obj); //range 选择obj下所有子内容
          range.collapseToEnd(); //光标移至最后
        } else if (document.selection) { //ie10 9 8 7 6 5
          let range = document.selection.createRange(); //创建选择对象
          //var range = document.body.createTextRange();
          range.moveToElementText(obj); //range定位到obj
          range.collapse(false); //光标移至最后
          range.select();
        }


      })

    },
    // search(key) {
    //   this.$http.atUserList({ keywords: key }).then(res => {
    //     if (res.status == 200) {
    //       res.data.data.map(item => {
    //         item.value = item.open_id
    //         item.key = item.name
    //         return item
    //       })
    //       return res.data.data
    //     }
    //   })
    // },
    remoteSearch(key, cb) {
      // this.text = text;
      console.log(key);
      // if (!key) {
      //   return;
      // }
      if (this.time) {//节流防抖
        this.time = false
        setTimeout(() => {
          console.log(123);
          this.$http.atUserList({ keywords: key }).then(res => {
            if (res.status == 200) {
              res.data.data.map(item => {
                item.value = item.open_id
                item.key = item.name
                return item
              })
              this.userList = res.data.data
              cb(res.data.data)
            }
          })
          this.time = true;
        }, 500)
      }

      // console.log(key, cb);
    },
    // 小于20M直传
    async uploads(file) {
      const formData = new FormData()
      formData.append('file', file.raw)
      const res = await axios
        .post('/api/admin/byte_dance/upload_file', formData, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") } })  // 上传块数据
      if (res.status == 200) {
        await this.uploadToMedia(res.data.url)
      }
    },
    // 上传视频到素材库 小于20M时使用
    async uploadToMedia(url) {
      let mediaRes = await axios
        .post('/api/admin/byte_dance/upload_byte_media', { url, type: 2 }, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN"), timeout: 60000 } })
      if (mediaRes.status == 200) {
        this.params.video_id = mediaRes.data.video_id
      } else {
        this.params.video_id = ''
      }
    },
    // 开始上传视频 
    async handleUpload() {
      try {

        this.uploading = true;
        for (let i = 0; i < this.files.length; i++) {
          const file = this.files[i];
          let size = file.size
          // 小于20M 不分片
          this.handleProgress()
          if (size < 1024 * 1024 * 20) {
            await this.uploads(file)
          } else {
            // 大于20M 分片上传
            this.percent = 0

            let uploadIdRes = await axios.get('/api/admin/byte_dance/upload_media_part_id', { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") } })
            if (uploadIdRes.status !== 200) {
              this.$message.error("分片上传初始化失败")
              return
            }
            await this.upload(file, uploadIdRes.data.upload_id);
          }

        }
      } catch (err) {
        this.$message.error(`文件上传失败！${err.message}`);
      } finally {
        this.uploading = false;
      }
    },
    handleBeforeUploadImg() {
      this.params.type = 1
      this.params.image_list = []
    },
    showSucai() {
      this.getSucaiList()
      this.show_sucai = true
    },
    getSucaiList() {
      if (this.sucai_params.page == 1) {
        this.sucaiList = []
      }
      this.load_status = "loading"
      this.$http.getDouyinSucaiList(this.sucai_params).then(res => {
        if (res.status == 200) {
          if (this.sucai_params.type == 1) {

            res.data.data.map(item => {
              item.check = false
              this.imgList.map(img => {
                if (img.id == item.id) {
                  item.check = true
                }
              })

            })
          } else {
            res.data.data.map(item => {
              item.check = false
              this.files.map(img => {
                if (img.id == item.id) {
                  item.check = true
                }
              })

            })
          }

          this.sucaiList = this.sucaiList.concat(res.data.data)
          if (res.data.data.length < this.sucai_params.page_size) {
            this.load_status = false
          } else {
            this.load_status = true
          }
        }
      })
    },
    loadMore() {
      if (!this.load_status || this.load_status == "loading") {
        return;
      }
      console.log(this.load_status);
      this.sucai_params.page++;
      this.getSucaiList();
    },
    // 图片上传
    uploadSuccess(file, fileList) {
      console.log(file, fileList);
      this.imgList.push(file)
    },
    // 图片删除
    removeImg(index) {
      this.imgList.splice(index, 1)
      this.params.media_id = []
      if (this.imgList.length == 0) {
        this.showAdd = true
      }
    },
    removeVideo() {
      this.files = []
      this.showAdd = true
    },

    handleBeforeUpload() {
      // TODO: 检查文件大小、类型等
    },
    handleProgress() {
      // 显示上传进度
      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    async uploadImgToMedia() {
      let tasks = []
      if (this.imgList.length) {
        for (let index = 0; index < this.imgList.length; index++) {
          const url = this.imgList[index].url;
          tasks.push(
            new Promise((resolve, reject) => {
              axios
                .post('/api/admin/byte_dance/upload_byte_media', { url, type: 1 }, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN"), timeout: 60000 } })
                .then((res) => {
                  if (res.status == 200) {
                    let percent = Math.floor(((index + 1) / this.imgList.length) * 100)
                    this.loading.setText(percent + "%")
                    resolve(res.data)
                  } else {
                    reject(res)
                  }
                }).catch((err) => {
                  console.log(err);
                  reject(err)
                })
            })
          )
        }
        await Promise.all(tasks).then(res => {
          this.params.image_list = []
          res.map(item => {
            this.params.image_list.push(item.image_id)
          })

        })
      }
    },
    selectSucai(item, index) {
      if (this.sucai_params.type == 1) {
        item.check = !item.check
      } else {
        this.sucaiList.map(i => i.check = false)
        this.$set(this.sucaiList[index], "check", true)
        // let i = this.sucaiList.findIndex(i => { i.id == item.id })

      }
    },
    selectOk() {
      this.showAdd = false
      if (this.sucai_params.type == 1) {
        this.imgList = this.sucaiList.filter(item => item.check)
        this.params.type = 1
        let media_ids = []
        this.imgList.map(item => {
          media_ids.push(item.media_id)
        })
        this.params.media_id = media_ids
      } else {
        this.files = this.sucaiList.filter(item => item.check)
        this.params.type = 2
        this.params.video_id = this.files[0].media_id
      }
      this.show_sucai = false
    },
    // 发布作品
    async submit() {
      if (this.clciking) return
      this.clciking = true
      // [0].getAttribute("data-value")
      let valueArr = this.$refs.at_friend.getElementsByTagName("a"), atUserIds = []
      if (valueArr && valueArr.length) {
        for (let index = 0; index < valueArr.length; index++) {
          const item = valueArr[index];
          atUserIds.push(item.getAttribute("data-value"))
        }
        // valueArr.map(item => {
        //   atUserIds.push(item.getAttribute("data-value"))
        // })
      }
      this.params.text = this.$refs.at_friend.innerText
      // console.log(this.params.text);
      // // const str = '@张三 123 @李四 adv'
      // // const reg = /@(\S+)/g
      // const reg = /data-value="(\s)">/g
      // let match
      // let names = [], atUserIds = []
      // while ((match = reg.exec(this.params.text)) !== null) {
      //   const text = match[1]
      //   names.push(text)
      //   console.log(text)
      // }
      // if (names.length) {
      //   this.atFriendsList.map(item => {
      //     if (names.includes(item.name)) {
      //       atUserIds.push(item.value)
      //     }
      //     // names.map(i => {
      //     //   if (i == item.name) {
      //     //     atUserIds.push(item.value)
      //     //   }
      //     // })
      //   })
      // }
      // console.log(names, this.atFriendsList, atUserIds);

      if (this.params.type == 2) {
        if (!this.params.video_id) {
          await this.handleUpload()
          if (!this.params.video_id) {
            this.clciking = false
            this.$message.error("获取视频id失败")
            return
          }
          this.loading.setText("上传完成创建作品中");
        } else {
          this.loading = this.$loading({
            lock: true,
            text: "发布中",
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
        }


      } else {
        if (!this.params.image_list.length) {
          this.handleProgress()
          await this.uploadImgToMedia()
          this.loading.setText("图片上传完成创建作品中");
        } else {
          this.loading = this.$loading({
            lock: true,
            text: "发布中",
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
        }


      }


      let params = Object.assign({}, this.params)
      if (atUserIds.length) {
        params.at_users = [... new Set(atUserIds)].join(",")
      } else {
        params.at_users = ''
      }

      if (params.type == 2) {
        delete params.image_list
      } else {
        if (params.image_list && params.image_list.length) {
          params.image_list = params.image_list.join(",")
        }
        delete params.video_id
      }
      // params.at_users = params.at_users.length ? params.at_users.join(",") : ''

      this.$http.fabudouyin(params).then((res) => {
        if (res.status == 200) {
          this.loading && this.loading.close()
          this.$message.success("创建成功")
          this.clciking = false
          // 成功跳转到作品列表
          setTimeout(() => {
            this.$goPath('/douyin_videos')
          }, 500);
        } else {
          this.loading && this.loading.close()
          this.clciking = false
        }
      }).catch(() => {
        this.loading && this.loading.close()
        this.clciking = false
      })
    },
  }
}
</script>

<style lang ="scss" scoped>
.mt12 {
  margin-top: 12px;
}

.mt24 {
  margin-top: 24px;
}

.mr12 {
  margin-right: 12px;
}

.space_between {
  justify-content: space-between;
}

.cur_piont {
  cursor: pointer;
}

.flex-1 {
  flex: 1;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.title_size {
  font-weight: 500;
  font-size: 16px;
  color: #2e3c4e;
}

.douyin_fabu {
  padding: 24px;
  margin: -15px;
  background: #f1f4fa;

  .douyin_fabu_left {
    padding: 0 24px;
    background: #fff;

    .douyin_tips {
      .douyin_tips_icon {
        background: #f1f4fa;
        border-radius: 63px;
        width: 64px;
        height: 64px;
        min-width: 64px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 40px;
          /* height: 30px; */
          object-fit: cover;
        }
      }

      .douyin_tips_content {
        display: flex;
        flex-direction: row;
        /* justify-content: center; */
        align-items: center;
        flex: 1;
        padding: 12px 15px;
        background: #f1f4fa;
        font-size: 12px;
        color: #8a929f;
      }
    }

    .descript {
      width: 100%;
      position: relative;

      textarea {
        outline: none;
        background: #ffffff;
        border: 1px solid #dde1e9;
        border-radius: 4px;
        width: 100%;
      }

      .descript_other {
        .descript_other_label {
          padding: 10px 12px;
          background: #f1f4fa;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
        }
      }
    }

    .upfile {
      .file_content {
        .image_list {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;

          .img {
            width: 88px;
            height: 118px;
            margin-right: 24px;
            overflow: hidden;
            position: relative;

            &:hover {
              .hover {
                display: flex;
              }
            }

            img {
              width: 100%;
              object-fit: cover;
            }
          }
        }

        .video_pre {
          width: 88px;
          height: 118px;
          overflow: hidden;
          position: relative;

          &:hover {
            .hover {
              display: flex;
            }
          }
        }

        .video {
          width: 88px;
          height: 118px;
        }

        .hover {
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          margin: auto;
          background: rgba(0, 0, 0, 0.3);
          display: none;
          justify-content: center;
          align-items: center;
          flex-direction: row;

          .el-icon-delete {
            color: #f00;
            cursor: pointer;
          }
        }

        .upload-demo {
          width: 88px;
          height: 118px;
          background: #ffffff;
          border: 1px solid #dde1e9;
          border-radius: 4px;

          &.upload-video {
            position: relative;

            &:hover .hover {
              display: flex;
              z-index: 1000;
            }
          }

          .img_c {
            width: 88px;
            height: 118px;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .img_c_tet {
              font-size: 14px;
              color: #8a929f;
            }

            .img_c_add {
              border: 1.5px solid #dde1e9;
              width: 18px;
              height: 18px;
              margin: 10px auto 0;
              height: 18px;
              color: #8a929f;
              line-height: 14px;
              font-size: 17px;
            }
          }
        }
      }
    }
  }
}

.oper {
  margin-top: 58px;
  margin-left: -24px;
  margin-right: -24px;
  padding: 12px 24px;
  background: #ffffff;
  border-top: 1px solid #dde1e9;

  .oper_l {
    padding: 6px 12px;
    font-size: 14px;
    color: #2d84fb;
    cursor: pointer;
  }

  .oper_r {
    justify-content: flex-end;

    .cancel {
      cursor: pointer;
      padding: 6px 12px;
      font-size: 14px;
      color: #8a929f;
    }

    .submit {
      padding: 6px 12px;
      font-size: 14px;
      color: #fff;
      background: #2d84fb;
      border: 1px solid #2d84fb;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}

.tribute-demo-input {
  outline: none;
  border: 1px solid #eee;
  padding: 3px 5px;
  border-radius: 2px;
  font-size: 15px;
  min-height: 64px;
  line-height: 1.5;
  cursor: text;
  text-wrap: wrap;
}

.colleague-box {
  display: flex;
  width: 240px;
  height: 300px;
  overflow-y: auto;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid #e6e7e8;
  box-sizing: content-box;
  padding: 12px;
  position: absolute;
  z-index: 5;
  border-radius: 7px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .colleague_content {
    display: flex;
    padding: 8px 10px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;

    &:hover {
      background: #f2f2f3;
    }
  }
}

.filter {
  .filter_list {
    align-items: center;

    .filter_item {
      font-weight: 500;
      font-size: 16px;
      color: #8a929f;
      margin: 0 12px;
      cursor: pointer;

      &:first {
        margin-left: 0;
      }

      &.active {
        color: #2d84fb;
      }
    }

    .line {
      background: #8a929f;
      width: 2px;
      height: 18px;
    }
  }
}

.list {
  flex-wrap: wrap;
  height: 600px;
  overflow-y: auto;

  .card {
    padding: 24px;
    margin-top: 20px;
    background: #ffffff;
    border-radius: 8px;
    margin-right: 24px;

    .card_img {
      width: 229px;
      height: 150px;
      overflow: hidden;
      position: relative;
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));

      .check_info {
        position: absolute;
        top: 5px;
        left: 5px;
        width: 18px;
        height: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #8a929f;

        &.checked {
          border-color: #2d84fb;
        }
      }

      .img {
        width: 100%;
        object-fit: cover;
      }
    }

    .card_title {
      align-items: center;
      margin-top: 12px;

      .title_name {
        font-weight: 500;
        font-size: 16px;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #2e3c4e;
      }

      .title_r {
        min-width: 80px;
        font-size: 12px;
        color: #2d84fb;
      }
    }

    .card_user {
      align-items: center;
      margin-top: 12px;

      .name {
        font-size: 12px;
        color: #8a929f;
        flex: 1;
      }

      .time {
        font-size: 12px;
        color: #8a929f;
        justify-content: flex-end;
      }
    }
  }
}</style>