<template>
  <div>
    <div class="page i-form-list">
      <el-form inline :model="push_form" label-width="90px" label-position="left">
        <el-form-item label="客户姓名：" class="linefeed">
          <div class="row input-box div">
            <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="客户电话：" v-if="!data_list.information" class="linefeed">
          <!-- <div class="row input-box div isbottom">
            <div>
            <el-input placeholder="请输入电话号码" v-model="push_form.mobile" disabled maxlength="11"
              @blur="Validationphone(push_form.mobile)"></el-input>
            </div>
            <div>
            <img class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png" alt=""
              @click.prevent="addDomain" />
            </div>
            <div>
              <img class="convert" src="https://img.tfcs.cn/backup/fabu/fabu/Vector.png" alt=""
              @click.prevent="addDomain" />
            </div>
          </div> -->
          <div>
          <div class="row input-box div isbottom " v-for="(domain, index) in other_mobile" :key="index">
            <div class="row div" v-if="other_mobile&&domain.is_del!==1">
              <div class="phonerow flex-row">
                <el-input v-if="!domain.is_main" style="margin-bottom:10px;" placeholder="请输入客户姓名" v-model="domain.name"
                  maxlength="11"></el-input>
                <el-input :disabled="domain.is_main==1" placeholder="请输入电话号码" v-model="domain.tel" maxlength="11"
                  @blur="Validationphone(domain.tel)"></el-input>
              </div>
              <div v-if="!domain.is_main">
                <img class="add" @click.prevent="removeDomain(domain)"
                  src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
              </div>     
              <div v-if="domain.is_main">
                  <img class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png" alt=""
                    @click.prevent="addDomain" />
              </div>
              <div v-if="index !== other_mobile.length - 1">
                <img class="convert" src="https://img.tfcs.cn/backup/fabu/fabu/Vector.png" alt=""
                 @click.prevent="commutator(domain, index)"/>
              </div>
         
            </div>
          </div>
        </div>
        </el-form-item>
        <!-- 流转客客户电话 -->
        <el-form-item label="客户电话：" v-if="data_list.information==1">
           <!-- 客户已拥有手机号 -->
          <div class="row input-box div isbottom">
            <el-input :disabled="isMobileDisabled" placeholder="请输入电话号码" v-model.lazy="data_list.mobile" maxlength="11"
            @blur="Validationphone"></el-input>
            <img class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png" alt=""
              @click.prevent="addDomain" />

          </div>
          <div class="row input-box div isbottom " v-for="(domain, index) in now_haveMobile_list" :key="index">
            <div class="row div" v-if="now_haveMobile_list&&domain.is_del!==1">
              <div>
                <el-input style="margin-bottom:10px;" placeholder="请输入客户姓名" v-model="domain.name"
                  maxlength="11"></el-input>
                <el-input placeholder="请输入电话号码" v-model="domain.tel" maxlength="11"
                  @blur="Validationphone(domain.tel)"></el-input>
              </div>
              <div>
                <img class="add" @click.prevent="removeDomain(domain)"
                  src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="微信号：" v-if="this.data_list.information==1" class="linefeed">
          <div class="row input-box div">
            <el-input placeholder="请输入微信号" v-model="push_form.wx_account"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="抖音号：" v-if="this.data_list.information==1" class="linefeed">
          <div class="row input-box div">
            <el-input placeholder="请输入抖音号" v-model="push_form.dy_account"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="客户来源：" class="linefeed">
          <div class="input-box">
            <el-cascader :style="{minWidth: '20px', width: '100%',}" :value="push_form.source2_id==0?push_form.source_id:push_form.source2_id" placeholder="请选择"
              :options="source_list" @change="sourceLabel_status"
               :props="{
                  label: 'title',
                  value: 'id',
                  children: 'children',
                  checkStrictly: true}">
            </el-cascader>
          </div>
        </el-form-item>
        <el-form-item label="客户性别：" class="linefeed">
              <div class="sex-box">
                <div class="isactive" clas v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id == push_form.sex }"
                @click="() => {push_form.sex = item.id;}" >
                  <img
                    :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt=""
                    />
                    <div class="isactivesex">{{item.id==2?"女":"男"}}</div>
                </div>
              </div>
          </el-form-item>
        <el-form-item label="客户级别：" class="linefeed">
          <el-select v-model="push_form.level_id" placeholder="请选择客户等级" style="width: 240px;">
            <el-option v-for="item,index in level_list" :key="index" :label="item.title+'级'" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户类型：" class="linefeed">
          <el-select v-model="push_form.type" placeholder="请选择客户类型" style="width: 240px;">
            <el-option v-for="item,index in type_list" :key="index" :label="item.title" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户标签：" class="linefeed">
          <div class="input-box">
            <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签" :options="label_list"
              :props="{
                  value: 'id',
                  label: 'name',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
              }">
            </el-cascader>
          </div>
        </el-form-item>
        <el-form-item label="所在城市:" v-if="cusDataLsit.is_show_city==1" class="linefeed">
          <div class="block" style="width: 240px;">
            <el-popover placement="top" width="540" trigger="click">
              <el-input placeholder="请输入" slot="reference" v-model="provincesvalueA" clearable
                @change="provinclear"></el-input>
              <div v-if="cusDataLsit.is_show_city==1 && recordsdata.length" class="recordcss">
                <div v-for="item,index in recordsdata" :key="index">
                  <div class="invitecss" @click="recordid(item)"
                    :class="{isPositionscss:provincesvalueA== item.province_name +'/'+item.city_name+'/'+item.area_name}">
                    {{ item.province_name +'/'+item.city_name+'/'+item.area_name}}
                  </div>
                </div>
              </div>
              <el-cascader-panel ref="grouplist" v-model="provincesvalue" clearable
               :props="{
                  value: 'id',
                  label: 'name',
                  children: 'children',
                  emitPath: true,
                  multiple: false,
                }" :options="provincesoptions" @change="provincesChange">
              </el-cascader-panel>
            </el-popover>
          </div>
        </el-form-item>
        <el-form-item label="客户意向：" class="linefeed">
          <div class="row input-box div">
            <t-crm-project-select multiple allow-create default-first-option value-key="name" placeholder="请选择或输入"
              v-model="push_form.intention_community"  width="240px" />
          </div>
        </el-form-item>
        <el-form-item label="备注：" class="linefeed" v-if="showremark==1">
          <div class="row input-box div">
            <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import TCrmProjectSelect from '@/components/tplus/tSelect/tCrmProjectSelect.vue';
export default {
    components: { TCrmProjectSelect },
    props: {
        // 控制显示隐藏模态框
        show_cus_Edit: {
            type: Boolean,
            default: false
        },
        // 客户来源列表
        source_list: {
            type: Array,
            default: (() => {})
        },
        // 客户等级列表
        level_list: {
            type: Array,
            default: (() => {})
        },
        // 客户类型列表
        type_list: {
            type: Array,
            default: (() => {})
        },
        // 客户标签列表
        label_list: {
            type: Array,
            default: (() => {})
        },
        // 客户信息
        data_list: {
            type: Object,
            default: (() => {})
        },
    },
    data() {
        return {
            is_push_customer: false, // 模态框显示/隐藏
            // 提交维护参数
            push_form: {
                cname: "", // 客户名
                mobile:"",// 客户电话
                source_id: "", // 客户来源
                source2_id:'',
                level_id: "", // 客户等级
                type: 1, // 客户类型
                sex: 1, // 客户性别
                subsidiary_mobile: [], // 更多手机号码
                intention_community: [], // 客户意向
                remark: "", // 备注
                label: [], // 客户标签
                // add_type:"1",//1是私客，2是公海
            },
            other_mobile: [], // 客户电话
            // 客户性别
            sex_list: [
                { id: 1, name: "nan" },
                { id: 2, name: "nv3" },
            ],
            is_loading: false, // loading动画
            cusDataLsit: [], // 客户详情
            now_haveMobile_list: [], // 已拥有的手机号列表
            inten_loading: false, // 客户意向loading
            IntenOptions: [], // 客户意向列表
            provincesoptions:[],//城市选择联动
            provincesvalue:[],//城市id
            provincesvalueA:"",
            recordsdata:[],//省市区最近记录
            website_id:"",
            someProperty: '' ,
            isMobileDisabled:false,
            showremark:0, //备注功能是否显示
        }
    },
    watch: {  
        data_list: {  
          handler(newVal) {  
            // console.log(newVal);  
            if(newVal) {  
                this.$set(this,'data_list', newVal); 
                if(this.data_list&&this.data_list.information==1){
                    this.customerinformation()
                }else{
                    this.initializeData();
                } 
            }  
        // 根据数据变化判断是否显示数量  
      },  
      deep: true // 深度监听对象的变化  
    },
    },
    mounted() {
        this.website_id = this.$route.query.website_id
        // console.log(this.$route.query.website_id);
        this.getshowremark()
        if(this.data_list&&this.data_list.information==1){
            this.customerinformation()
        }else{
            this.initializeData();
        }
        this.Cities_provinces()
        this.Regional_records()
    },
    // computed: {  
    //     setMobileFormat() {
    //         return this.push_form.mobile.replace(/^(.{3}).*(.{3})$/, "$1****$2");
    //     }
    // },  
    filters: {
        setMobileFormat(item) {
            return item.replace(/^(.{3}).*(.{3})$/, "$1****$2");
        }
    },
    methods: {
      //获取配置项中备注功能是否显示
      getshowremark() {
        this.$http.getAuthShow('is_open_remark').then(res => {
          if (res.status == 200) {
            this.showremark = res.data
          }
        })
      },
        // 添加手机号
        addDomain() {
          if(this.data_list.information==1){
            this.now_haveMobile_list.push({
              name:"",
                tel: "",
            })
          }else{
            this.other_mobile.push({
                name:"",
                tel: "",
            });
          }
        },
        // 删除添加的手机号
        removeDomain(item) {
          // console.log(item);
          item.is_del = 1
          // 强制更新数组，以便让 Vue 监听到数据变化并重新渲染页面
          if(this.data_list.information==1){
            this.$set(this.now_haveMobile_list, this.now_haveMobile_list.indexOf(item), item);
          }else{
            this.$set(this.other_mobile, this.other_mobile.indexOf(item), item);
          }
          
        },
        //转换手机号位置
        commutator(domain, index){
          if (index < this.other_mobile.length - 1) {
            // 交换当前数据与下一条数据的位置
            [this.other_mobile[index], this.other_mobile[index + 1]] = [this.other_mobile[index + 1], this.other_mobile[index]];
            // 强制刷新组件
            // console.log(this.other_mobile);
            this.$forceUpdate();
          }
        },
         //删除附属手机号
        removephone(idx){
      // console.log(this.now_haveMobile_list,"旧");
      this.now_haveMobile_list.splice(idx, 1);
      // console.log(this.now_haveMobile_list,"新");
        },
        // 点击客户级别
        onClickLevelChange(item) {
            this.push_form.level_id = item.id;
        },
        // 点击客户类型
        onClickTypeClient(item) {
            this.push_form.type = item.id;
        },
        //客户来源
        sourceLabel_status(e){
            if(e.length>1){
              this.push_form.source2_id = e[1]
              this.push_form.source_id = e[0]
            }else{
              this.push_form.source_id = e[0]
              this.push_form.source2_id = 0
            }
        },
        //验证手机号
        Validationphone(phoneNumber){
        // console.log(phoneNumber);
        if(phoneNumber){
          const regex = /^(?:1[3-9]\d{9}|(?:\+?852|00852)?[5-9]\d{7}|(?:\+?853|00853)?[6-9]\d{7})$/;
          if (regex.test(phoneNumber)) {
            // this.$message.success('手机号格式正确')
          } else {
            this.$message.warning('请输入正确的手机号')
          }
        }
  
        },
        // 确定更改维护资料
        onClickForm() {
         let push_formcopy = JSON.parse(JSON.stringify(this.push_form)); // 拷贝
         let other_mobilecopy = JSON.parse(JSON.stringify(this.other_mobile)); // 拷贝
        //  console.log(other_mobilecopy);
        //  console.log(this.data_list);
        //  console.log(this.data_list.information);
          if(!this.data_list.information&&this.data_list.information!==1){
            console.log(1112121212121);
            if (push_formcopy.cname=="") {
                this.$message.error("请检查客户姓名");
                return;
            }
            if (push_formcopy.source_id==""||push_formcopy.source_id==0) {
                this.$message.error("请检查客户来源");
                return;
            }
            if (push_formcopy.type=="") {
                this.$message.error("请检查客户类型");
                return;
            }
          }else{
            if (push_formcopy.type=="") {
              push_formcopy.type = 0
            }
          }
            if (!push_formcopy.sex) {
              push_formcopy.sex = 0
            }
            if (!push_formcopy.level_id) {
              push_formcopy.level_id = 0
            }
            // 将多个id转换为字符串用,隔开
            if (push_formcopy.label && Array.isArray(push_formcopy.label)) {
              push_formcopy.label = push_formcopy.label.join(",");
            }
            // 处理客户手机号码数据
            push_formcopy.mobile = this.data_list.mobile;
            if(this.data_list.information&&this.data_list.information==1) {
              console.log("执行一流转客");
              // console.log(this.now_haveMobile_list);
                if (this.now_haveMobile_list.length > 0) {
                        this.push_form.subsidiary_mobile = this.now_haveMobile_list
                }
            } else {
                console.log("执行2crm")
                if (other_mobilecopy.length > 0) {
                  this.cusDataLsit.subsidiary_tel_list.map(itm=>{
                    other_mobilecopy.map(item => {
                      if(item.tel==itm.tel.replace(/^(.{3}).*(.{3})$/, "$1****$2")){
                        item.tel = itm.tel
                        // console.log(item.tel,"手机号====");
                        // console.log(itm.tel);
                       
                      }
                    });
                  })
                   const mainMobile = other_mobilecopy.find(item => item.tel === this.data_list.mobile);
                    const index = other_mobilecopy.indexOf(mainMobile);
                    other_mobilecopy.forEach((item, i) => {
                      if (i > index) {
                        item.order = 0;
                      } else {
                        item.order = mainMobile.order + (index - i);
                      }
                    });
                    other_mobilecopy = other_mobilecopy.filter(item => item.is_main !== 1);
                    if (!push_formcopy && other_mobilecopy.length > 0) {
                      push_formcopy = other_mobilecopy[0];
                        if (other_mobilecopy.length > 1) {
                          push_formcopy.subsidiary_mobile = other_mobilecopy
                        }
                    } else {
                      push_formcopy.subsidiary_mobile = other_mobilecopy
                    }
                }
            }
            const params = {...push_formcopy};
            params.intention_community = params.intention_community.length ? params.intention_community.join(",") : '';          
            // console.log(params);
            this.is_loading = true;
            if(this.data_list.information&&this.data_list.information==1){
              console.log(params,"流转客");
                // 流转客修改资料
                this.$http.renewcustomerinfor(params).then((res)=>{
                    this.is_push_customer = false;
                if (res.status === 200) {
                    this.$message.success("操作成功");
                    this.is_loading = false;
                    this.$emit("submitMaintain", ""); // 刷新页面获取最新数据
                    }
                }).catch(() => {
                    this.is_loading = false;
                })
            }else{
              console.log(params,"CRM");
                this.$http.neweditcrminfromation(params).then((res) => {
                this.is_push_customer = false;
                if (res.status === 200) {
                    this.$message.success("操作成功");
                    this.is_loading = false;
                    this.$emit("submitMaintain", ""); // 刷新页面获取最新数据
                    }
                }).catch(() => {
                    this.is_loading = false;
                })
                }
           
        },
        // 关闭模态框触发
        handlerClose() {
            // 清空数据
            this.push_form = {
                cname: "", // 客户名
                source_id: "", // 客户来源
                level_id: "", // 客户等级
                type: 1, // 客户类型
                sex: 1, // 客户性别
                subsidiary_mobile: [], // 更多手机号码
                intention_community: [], // 客户意向
                remark: "", // 备注
                label: [], // 客户标签
            },
            this.$emit("fastCloseEdit","");
        },
        //如果是流转客
        customerinformation(){
            this.is_push_customer = this.show_cus_Edit; // 赋值模态框显示/隐藏
            this.$http.getcustomerinfor(this.data_list.id).then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"客户详情")
                    this.cusDataLsit = res.data;
                    // console.log(this.cusDataLsit,"this.cusDataLsit")
                    if(this.cusDataLsit.mobile){
                      this.isMobileDisabled = true
                    }else{
                      this.isMobileDisabled = false
                    }
                    this.push_form.id = this.data_list.id; // 赋值客户id
                    this.push_form.cname = this.cusDataLsit.cname; // 赋值客户姓名
                    this.push_form.source_id = this.cusDataLsit.source_id; // 赋值客户来源
                    this.push_form.source2_id = this.cusDataLsit.source2_id; // 赋值客户来源
                    this.push_form.sex = this.cusDataLsit.sex; // 赋值客户性别
                    this.push_form.level_id = this.cusDataLsit.level_id; // 赋值客户等级
                    this.push_form.intention_community = this.cusDataLsit.intention_community ? this.cusDataLsit.intention_community.split(',') : []; // 客户意向
                    this.push_form.type = this.cusDataLsit.type; // 赋值客户类型
                    this.push_form.province_id=this.cusDataLsit.province_id,
                    this.push_form.city_id=this.cusDataLsit.city_id,
                    this.push_form.area_id=this.cusDataLsit.area_id,
                    this.$set(this.push_form, "dy_account", this.cusDataLsit.dy_account);
                    this.$set(this.push_form, "wx_account", this.cusDataLsit.wx_account);
                    // 赋值客户标签
                    this.push_form.label = this.cusDataLsit.label && this.cusDataLsit.label.length ? 
                    this.cusDataLsit.label.split(",") : this.cusDataLsit.label;
                    // 赋值客户备注
                    this.push_form.remark = this.cusDataLsit.remark && this.cusDataLsit.remark != undefined ?
                    this.cusDataLsit.remark : '';
                    if(this.push_form.level_id==0){
                        this.push_form.level_id = ""
                    }
                    if(this.push_form.type==0){
                        this.push_form.type = ""
                    }       
                    if(this.cusDataLsit.province_id){
                        this.provincesvalue[0]=this.cusDataLsit.province_id
                      }
                      if(this.cusDataLsit.city_id){
                        this.provincesvalue[1]=this.cusDataLsit.city_id
                      }
                      if(this.cusDataLsit.area_id){
                        this.provincesvalue[2]=this.cusDataLsit.area_id
                      }
                      if(this.cusDataLsit.province&&this.cusDataLsit.city&&this.cusDataLsit.area){
                        this.provincesvalueA = this.cusDataLsit.province.name+"/"+this.cusDataLsit.city.name+"/"+this.cusDataLsit.area.name
                      }
                    // 处理更多手机号，过滤为空的数据
                    let phoneNum = [];
                    if(this.cusDataLsit.subsidiary_mobile_list.length) {
                        this.cusDataLsit.subsidiary_mobile_list.map((item) => {
                            if(item != "") {
                                phoneNum.push(item);
                            }
                        })
                    }
                    this.cusDataLsit.subsidiary_mobile = phoneNum.join(",");
                    // 如果有手机号
                    if(this.cusDataLsit.mobile) {
                        if(this.cusDataLsit.subsidiary_tel_list) {
                            this.now_haveMobile_list = this.cusDataLsit.subsidiary_tel_list
                        }
                    } else {
                        // 如果没有手机号，增加一条输入框用于填写
                        // this.addDomain();
                    }
                }
        })
        },
        // CRM客户数据赋值
        initializeData() {
            this.is_push_customer = this.show_cus_Edit; // 赋值模态框显示/隐藏
            this.$http.getCrmCustomerDetailV2(this.data_list.id).then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"客户详情")
                    this.cusDataLsit = res.data;
                    // console.log(this.cusDataLsit,"this.cusDataLsit")
                    this.push_form.id = this.data_list.id; // 赋值客户id
                    this.push_form.cname = this.cusDataLsit.cname; // 赋值客户姓名
                    this.push_form.source_id = this.cusDataLsit.source_id; // 赋值客户来源
                    this.push_form.source2_id = this.cusDataLsit.source2_id; // 赋值客户来源
                    this.push_form.sex = this.cusDataLsit.sex; // 赋值客户性别
                    this.push_form.level_id = this.cusDataLsit.level_id; // 赋值客户等级
                    this.push_form.intention_community = this.cusDataLsit.intention_community ? this.cusDataLsit.intention_community.split(',') : []; // 客户意向
                    this.push_form.type = this.cusDataLsit.type; // 赋值客户类型
                    this.push_form.province_id=this.cusDataLsit.province_id,
                    this.push_form.city_id=this.cusDataLsit.city_id,
                    this.push_form.area_id=this.cusDataLsit.area_id,
                    this.push_form.mobile = this.cusDataLsit.mobile.replace(/^(.{3}).*(.{3})$/, "$1****$2")
                    this.other_mobile = this.cusDataLsit.subsidiary_tel_list.map(item => {
                      const hiddenTel = item.tel.replace(/^(.{3})(.{7})(.{3})$/, "$1*******$3");
                      console.log(hiddenTel);
                      return {
                        ...item,
                        tel: hiddenTel
                      };
                    });
                    // this.other_mobile  = this.cusDataLsit.subsidiary_tel_list
                    // console.log(this.other_mobile);
                    // 赋值客户标签
                    this.push_form.label = this.cusDataLsit.label && this.cusDataLsit.label.length ? 
                    this.cusDataLsit.label.split(",") : this.cusDataLsit.label;
                    // 赋值客户备注
                    this.push_form.remark = this.cusDataLsit.remark && this.cusDataLsit.remark != undefined ?
                    this.cusDataLsit.remark : '';
                    if(this.push_form.level_id==0){
                        this.push_form.level_id = ""
                    }
                    if(this.push_form.type==0){
                        this.push_form.type = ""
                    }       
                    if(this.cusDataLsit.province_id){
                        this.provincesvalue[0]=this.cusDataLsit.province_id
                      }
                      if(this.cusDataLsit.city_id){
                        this.provincesvalue[1]=this.cusDataLsit.city_id
                      }
                      if(this.cusDataLsit.area_id){
                        this.provincesvalue[2]=this.cusDataLsit.area_id
                      }
                      if(this.cusDataLsit.province&&this.cusDataLsit.city&&this.cusDataLsit.area){
                        this.provincesvalueA = this.cusDataLsit.province.name+"/"+this.cusDataLsit.city.name+"/"+this.cusDataLsit.area.name
                      }
                    // 处理更多手机号，过滤为空的数据
                    let phoneNum = [];
                    if(this.cusDataLsit.subsidiary_mobile_list.length) {
                        this.cusDataLsit.subsidiary_mobile_list.map((item) => {
                            if(item != "") {
                                phoneNum.push(item);
                            }
                        })
                    }
                    this.cusDataLsit.subsidiary_mobile = phoneNum.join(",");
                    // 如果有手机号
                    if(this.cusDataLsit.mobile) {
                        // if(this.cusDataLsit.subsidiary_mobile) {
                        //     this.now_haveMobile_list = (this.cusDataLsit.subsidiary_mobile ).split(",")
                        // }
                        // 如果存在更多手机号
                        // if(this.cusDataLsit.subsidiary_mobile) {
                        //     this.now_haveMobile_list = (this.cusDataLsit.mobile+","+this.cusDataLsit.subsidiary_mobile ).split(",")
                        // } else {
                        //     this.now_haveMobile_list = [this.cusDataLsit.mobile];
                        // }
                    } else {
                        // 如果没有手机号，增加一条输入框用于填写
                        this.addDomain();
                    }
                }
        })
        },
        //省市区如果是空
        provincesChange(e){
          let nodeinfo = this.$refs["grouplist"].getCheckedNodes()
          this.provincesvalueA = nodeinfo[0].pathLabels[0] + "/"+ nodeinfo[0].pathLabels[1]+"/"+nodeinfo[0].pathLabels[2]
          if(e.length){
            this.push_form.province_id = e[0]
            this.push_form.city_id = e[1]
            this.push_form.area_id = e[2]
          }else{
             this.push_form.province_id =0
             this.push_form.city_id =0
             this.push_form.area_id =0
          }

        },
        //获取最近省市区的记录
        Regional_records(){
          this.$http.regionalrecords(this.data_list.id).then((res)=>{
            if(res.status==200){
                // console.log(res.data,"省市区A");
                this.recordsdata = res.data
                // this.provincesoptions[0].children = res.data
            }
          })
        },
        Cities_provinces(){
          this.$http.cities_and_provinces().then((res)=>{
            if(res.status==200){
            //   console.log(res.data,"省市区");
              this.provincesoptions = res.data
            }
          })
        },
        recordid(item){
            let arr = []
            arr[0] = item.province_id
            arr[1] = item.city_id
            arr[2] = item.area_id
            this.provincesvalue = arr
            this.push_form.province_id = this.provincesvalue[0]
            this.push_form.city_id = this.provincesvalue[1]
            this.push_form.area_id = this.provincesvalue[2]
            this.provincesvalueA = item.province_name+"/"+ item.city_name+"/"+item.area_name
        },
        provinclear(e){
           if(!e){
            this.push_form.province_id = 0
            this.push_form.city_id = 0
            this.push_form.area_id = 0
           }
        },
        // 获取客户意向列表
        getIntentionList(value) {
            if(value != " " && value != "") {
                this.inten_loading = true; // 开启loading
                this.$http.getProjectComfirm({keyword: value}).then((res) => {
                if(res.status == 200) {
                    // console.log(res.data,"获取意向列表")
                    this.inten_loading = false; // 关闭loading
                    this.IntenOptions = res.data;
                } else {
                    this.inten_loading = false;
                }
                }).catch(() => {
                    this.inten_loading = false;
                })
            }
        },
        changeIntention() {
            this.$forceUpdate();
        },
    }
}
</script>
<style lang="scss" scoped>
.page {
  width: 90%;
  margin: 0 auto;
  max-width: 400px;
}

/deep/ .i-form-list {
    .el-form-item__label {
      color: #8a929f;
      font-size: 15px !important;
    }
.linefeed{
  white-space: nowrap;
}
  .input-box {
    width: 238px;
    justify-content: space-between;
    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }
  .sex-box {
         width: 235px;
         height: 34px;
         display: flex;
        //  justify-content: space-between;
        img {
          width: 30px;
          height: 30px;
          border-radius: 4px;
          border: 1px solid #fff;
          margin-right: 14px;
        }
        .isactive{
          width: 120px;
          border: 1px solid  #dfe5ef;
          border-radius: 4px;
          margin-left: 3px; 
          display: flex;
          align-items: center;
          .isactivesex{
            font-size: 15px;
            margin-left: 5px;
            color: #8a929f;
          }
        }
        .is_active {
            border: 1px solid rgba(45, 132, 251, 1);
          }
      }
  .isbottom {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
    .phonerow{
      flex-direction: column;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 237px;
    }

    .add {
      width: 16px;
      height: 16px;
      margin-left: 10px;
    }
    .convert{
      width: 16px;
      height: 16px;
      // border:1px solid #8A929F;
      border-radius: 50%;
      margin-left: 10px;
    }
  }
}

.recordcss {
  // width: 220px;
  display: flex;
  flex-wrap: wrap;

  .invitecss {
    padding: 7px 18px;
    margin-left: 10px;
    margin-bottom: 10px;
    color: #8a929f;
    background-color: #f4f4f5;
    border: none;
    font-size: 14px;
    cursor: pointer;
  }

  .isPositionscss {
    background-color: #e8f1fe;
    color: #409eff;
  }
}
</style>