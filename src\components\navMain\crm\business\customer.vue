<template>
  <div>
    <div class="table-top-box div row">
      <div class="t-t-b-left div row flex-1">
        <el-cascader placeholder="请选择部门" style="width: 210px; margin-right: 16px" v-model="params.department_id" clearable
          :options="department_list" :props="{
            value: 'id',
            label: 'name',
            children: 'subs',
            emitPath: false,
          }" @change="onPageChange1(1)"></el-cascader>
        <el-input style="width: 200px" placeholder="请输入姓名" v-model="params.user_name"></el-input>
        <el-cascader
              class="my-cascader"
              v-model="member_value"
              :options="member_listNEW"
              clearable
              filterable
              placeholder="角色类型"
              :style="{
                minWidth: '20px',
                width: '110px',
              }"
              :props="{
                label: 'user_name',
                value: 'id',
                children: 'subs',
                checkStrictly: true,
              }"
              @change="loadFirstLevelChildren"
            ></el-cascader>
            <el-select v-model="params.sort" placeholder="请选择排序" style="margin-left:10px;"
            clearable @change="onPageChange1(1)">
              <el-option
                v-for="item in optionalsorting"
                :key="item.value"
                :label="item.name"
                :value="item.value">
              </el-option>
            </el-select>
        <el-button style="margin: 0 16px" type="primary" class="el-icon-search" @click="onPageChange1(1)">搜索</el-button>
        <!-- <el-date-picker
          style="width: 210px; margin-right: 16px"
          v-model="p_time"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="changeTimeRange"
        >
        </el-date-picker> -->
        <!-- <el-button type="primary" v-if="false"> 数据导出</el-button> -->
      </div>
      <!-- <div class="t-t-b-right div row f-c">
        <span class="f f1"></span>第一 <span class="f f2"></span>第二
        <span class="f f3"></span>第三
      </div> -->
    </div>
    <div v-fixed-scroll="62">
    <el-table style="margin-bottom:30px;" v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :cell-style="cellStyle" @sort-change="onSortChange"
      show-summary
      :summary-method="handleSummary">
      <template v-for="(item,index) in headerList">
        <el-table-column :key="index" :prop="item.field" :label="item.name" v-slot="{ row }"  :fixed="item.field !== 'user_name' && item.field !== 'khzl' ? left : ''"
        :sortable="item.level==2?true:false" :align="item.field !== 'user_name' ? center : ''" :render-header="(h, obj) => renderHeader(h, obj,item)"
        :show-overflow-tooltip='true'>
          <template v-if="item.field == 'user_name'">
            <div class="name-box-cy div row">
              <img class="left" v-if="row.wx_work_avatar" :src="row.wx_work_avatar" alt="" />
              <div class="left" v-else>{{ row.user_name[0] }}</div>
              <div class="right">
                <div class="c">{{ row.user_name }}</div>
                <div class="b">
                  {{ row.department ? row.department[0].name : "" }}
                </div>
              </div>
            </div>
          </template>
          <template v-if="item.field == 'khzl'">
            <template v-if="params.admin_type > 0">
              <template v-if="admin_list.id == params.admin_id">
                <el-link type="primary"
                  v-if="row[item.field]>0"
                @click="handleCellClick(row,item.field )">{{row[item.field]}}</el-link>
                <span v-else>{{row[item.field]}}</span>
              </template>
              <template v-if="admin_list.id !== params.admin_id">
                <span @click="sevendata(row,item.field)" style="cursor: pointer;">{{row[item.field]}}</span>
              </template>
            </template>
            <template v-if="!params.admin_type">
              <span @click="sevendata(row,item.field)" style="cursor: pointer;">
                {{row[item.field]&&row[item.field] !== undefined && row[item.field] !== null ? row[item.field] : row[item.field] }}
              </span>
            </template>
          </template>
          <template v-if="item.field == 'fk'">
            <template v-if="params.admin_type > 0">
              <template v-if="admin_list.id == params.admin_id">
                <el-tooltip class="item" effect="dark" placement="top-start">
                  <div slot="content">
                    <div style="margin-bottom:10px">分配量包含自动分配+导入分配+手动转交分配客户的次数</div>
                    <div>如果存在多次分配或转交则跳转列表筛选数据会不等于分配量</div></div>
                  <el-link type="primary"
                  v-if="row[item.field]>0"
                @click="handleCellClick(row,item.field )">{{row[item.field]}}</el-link>
                <span v-else>{{row[item.field]}}</span>
                </el-tooltip>
              </template>
              <template v-if="admin_list.id !== params.admin_id">
                {{row[item.field]}}
              </template>
            </template>
            <template v-if="!params.admin_type">
              {{row[item.field]&&row[item.field] !== undefined && row[item.field] !== null ? row[item.field] : 0 }}
            </template>
          </template>
          <template v-if="item.field !== 'user_name'&&item.field !== 'khzl'&&item.field !== 'fk'">
            {{row[item.field]&&row[item.field] !== undefined && row[item.field] !== null ? row[item.field] : 0 }}
          </template>
        </el-table-column>
      </template>
      <el-table-column label="私客详情" width="80" fixed="right" class="skxq"
      v-if="show_shitu" v-slot="{row}">
      <!-- {{row.id}} -->
        <el-link type="primary" :underline="false" target="_blank" @click="Jumplink(row)">查看</el-link>
      </el-table-column>
    </el-table>
    </div>
    <!-- <el-pagination style="text-align: end; margin-top: 24px" background layout="total,prev, pager, next"
      :total="params.total" :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
    </el-pagination> -->
    <div class="page_footer flex-row items-center">
          <div class="page_footer_l flex-row flex-1 items-center">
            <div class="head-list">
              <!-- <el-button type="primary" size="small" @click="empty">清空</el-button> -->
            </div>
            <div class="head-list">
              <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
            </div>
          </div>
          <div>
            <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
              :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
              @current-change="onPageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
        <sevendata ref="sevendata"></sevendata>
  </div>
</template>

<script>
import sevendata from "./sevenpop_upnotification.vue"
export default {
  components:{
    sevendata
  },
  props: {
    house_params: {
      type: [Object],
      default: () => { return { page: 1 } },
    },
    admin_list:{
      type:Object,
      default:() => {}
    },
    show_shitu:{
      type:Boolean,
      default:false
    },
    optionalsorting:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      center:"center",
      left:false,
      params: {
        page: 1,
        per_page: 10,
        start_date: "",
        end_date: "",
        department_id: 0,
        user_name: "",
        date_type:3
        // sort_type: "",
        // sort_style:"",
      },
      p_time: "",
      tableData: [],
      is_table_loading: false,
      department_list: [],
      headerList: [],
      website_id:"",
       //成员
       member_value: [],
       member_listNEW: [
        {
          id: 1, user_name: "录入人",
          subs: []
        },
        // {
        //   id: 2, user_name: "维护人",
        //   subs: []
        // },
        // {
        //   id: 3, user_name: "带看人",
        //   subs: []
        // },
        // {
        //   id: 4, user_name: "成交人",
        //   subs: []
        // },
      ],
      sortoptions:[],
    };
  },
  watch: {
//     "house_params.page": {
//       handler(val) {
//         console.log(val, "111");
//         if (val) {
//           this.params.page = val
// log
//         } else {
//           this.params.page = 1
//         }
//         this.$forceUpdate()


//       },
//       immediate: true
//     },
    // ipAddress:{
    //   handler(val){
    //     this.ipAdd = val
    //     console.log(val);
    //   }
    // }
  },
  mounted() {
    this.getCrmDepartmentList(); 
    this.MembersNEW()
  },
  created() {
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
    // this.getIPAddress()
    if (this.$route.query.website_id) {
        this.website_id = this.$route.query.website_id;
      }
      this.getCrmIntelistFn()
      this.getCrmIntelligentRateList();

  },
  methods: {
    // getIPAddress() {
    //   // 使用异步请求获取IP地址
    //   const url = 'https://api.ipify.org?format=json';
    //   fetch(url)
    //     .then(response => response.json())
    //     .then(data => {
    //       this.ipAddress = data.ip;
    //       // console.log("本机ip地址",this.ipAddress);

    //     })
    //     .catch(error => {
    //       console.log('获取IP地址失败:', error);
    //     });
    // },
    handleSummary({ columns, data }) {
      // console.log(columns, data,"--------------------");
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          if (index === 2) {
            sums[index] = '--';
            return;
          }
          if (index === 17) {
            sums[index] = '--';
            return;
          }
          if (['khzl', 'rl', 'fk', 'auto_rl', 'import_rl', 'hand_rl', 'lr', 'wh', 'hf', 'dk', 'cj'].includes(column.property)) {
              const values = data.map(item => parseFloat(item[column.property]));
              if (!isNaN(values[0])) {
                  sums[index] = values.reduce((prev, curr) => {
                      const value = parseFloat(curr);
                      return isNaN(value) ? prev : prev + value;
                  }, 0);
                  sums[index] = Math.round(sums[index] * 100) / 100;
              } else {
                  sums[index] = '';
              }
          } else {
              const whSum = sums.find((_, i) => columns[i].property === 'wh');
              const dkSum = sums.find((_, i) => columns[i].property === 'dk');
              const hfSum = sums.find((_, i) => columns[i].property === 'hf');
              const fkSum = sums.find((_, i) => columns[i].property === 'fk');
              const cjSum = sums.find((_, i) => columns[i].property === 'cj');

              switch (column.property) {
                  case 'take_ratio':
                      if (dkSum !== 0) {
                        let ratio = Math.floor(whSum / dkSum);
                        sums[index] = `${ratio} : 1`;
                      } else {
                        sums[index] = '--';
                      }
                      break;
                  case 'follow_up_rate':
                      if (whSum !== 0) {
                          sums[index] = ((hfSum / whSum) * 100).toFixed() + '%';
                      } else {
                          sums[index] = '--';
                      }
                      break;
                  case 'clue_take_rate':
                      if (fkSum !== 0) {
                          sums[index] = ((dkSum / fkSum) * 100).toFixed() + '%';
                      } else {
                          sums[index] = '--';
                      }
                      break;
                  case 'take_deal_rate':
                      if (dkSum !== 0) {
                          sums[index] = ((cjSum / dkSum) * 100).toFixed() + '%';
                      } else {
                          sums[index] = '--';
                      }
                      break;
                  default:
                      sums[index] = '--';
                      break;
              }

              // Ensure sums[index] is not null
              if (sums[index] === null) {
                  sums[index] = '--';
              }
          }
        });
        return sums;
      },
    /* eslint-disable */
    //单元格的点击事件
    handleCellClick(item,field ) {
      let paramsdata = Object.assign({}, this.params, this.house_params)
      console.log(paramsdata);
      console.log(item,field,"item========");
      let formattedDateArr = {start:"",end:""};
      let text_time = {start:"",end:""}
      let end ="" 
      let start = ""
      if(paramsdata.date_type>1){
        if(paramsdata.date_type==2){
         end = new Date();
         start = new Date(end);
        start.setHours(0, 0, 0, 0);
      }else if(paramsdata.date_type==3){
        end = new Date();
        end.setHours(0, 0, 0, 0);
        start = new Date(end);
        start.setDate(start.getDate() - 1);
      }else if(paramsdata.date_type==4){
        end = new Date();
        start = new Date(end);
        start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
        start.setHours(0, 0, 0, 0);
      }else if(paramsdata.date_type==5){
        end = new Date();
        end.setDate(end.getDate() - end.getDay()); // 获取当前周的最后一天
        end.setHours(23, 59, 59, 0);
        start = new Date(end);
        start.setDate(start.getDate() - 6); // 获取上一周的第一天
        start.setHours(0, 0, 0, 0);
      }else if(paramsdata.date_type==6){
        end = new Date();
        start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
        start.setHours(0, 0, 0, 0);
      }else if(paramsdata.date_type==7){
        end = new Date();
        end.setDate(0); // 获取上个月的最后一天
        end.setHours(23, 59, 59, 0);
        start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
        start.setHours(0, 0, 0, 0);
      }
      const formatDate = (date) => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        }
        const formattedEnd = formatDate(end);
        const formattedStart = formatDate(start);
        text_time.start = formattedStart
        text_time.end = formattedEnd
      
      }
      if(paramsdata.start_date){
          const startDate =paramsdata.start_date.split(' ')[0];
          const endDate = paramsdata.end_date.split(' ')[0];
          formattedDateArr.start = startDate;
          formattedDateArr.end = endDate;
      }
      const params = paramsdata.start_date? formattedDateArr : text_time;
      params.ids = item.id
      console.log(params);
      if(field=="fk"){
         const routeData = this.$router.resolve({
              name: 'crm_customer_my_list',
              query: { type: 2, status: 13, website_id: this.website_id, ...params},
            });
            window.open(routeData.href, '_blank');
      }else{
        const routeData = this.$router.resolve({
              name: 'crm_customer_my_list',
              query: { type: 2, status: 13, website_id: this.website_id, ids: params.ids},
            }); 
            window.open(routeData.href, '_blank');
      }
         
    },
    //点击客户总量弹出近7日客户量变动记录
    sevendata(item,field){
      let params = {
        page:this.params.page,
        per_page:this.params.per_page,
        admin_id:item.id,
      }
      // console.log(item);
      this.$refs.sevendata.open(params,item.user_name)
    },    // 自定义排序
    onSortChange({ column, prop, order }) {
      let sort = '',
        sortArr = ''
      if (column && column.property) {
        sortArr = column.property
        if (sortArr) {
            sort = sortArr
        }
      }
      // this.params.type = column.index;
      if (order === "descending") {
        this.params.sort_style = 2
        this.params.sort_type = sort
        // 大->小
      } else if (order === "ascending") {
        this.params.sort_style = 1
        this.params.sort_type = sort
      } else {
        delete this.params.sort_style 
        delete this.params.sort_type 
      }
      // this.$emit("sortData")
      this.params.page = 1;
      this.getCrmIntelistFn();
    },
    //表头提示文字
    renderHeader(h, data, item){
      if (item.tips && item.tips !== "") {
          return h("span", [
            h(
              "el-tooltip",
              {
                class: "item",
                props: {
                  effect: "dark",
                  content: item.tips,
                  placement: "top",
                },
              },
              [h("span", data.column.label)]
            ),
          ]);
        } else {
          return h("span", data.column.label);
        }
    },
    // 获取成员的接口（新）
    MembersNEW(){
      this.$http.getDepartmentMemberListNew().then((res)=>{
        if(res.status==200){
          this.member_listNEW.map(item => {
          item.subs = res.data
         })
        }
      })
    },
    // 获取部门列表
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      try {
        if (column && column.property) {
        let idxArr = [], idx = ''
        //  idxArr.push(column.property)
        //  console.log(column);
        //  console.log(idxArr);
        // if (idxArr.length == 1) {
          idx = column.property
          return this.setStyle(row[idx].rank);
        // }
      }
      } catch (error) {
        
      }
      
    },
    setStyle(rank) {
      if (rank === 1) {
        return `background-color:#ffb88d;`;
      }
      if (rank === 2) {
        return `background-color:#bedcff;`;
      }
      if (rank === 3) {
        return `background-color:#ffeba5;`;
      }
    },
    // changeTimeRange(e) {
    //   this.params.start_date = e ? e[0] : "";
    //   this.params.end_date = e ? e[1] : "";
    //   this.params.page = 1;
    //   this.getCrmIntelligentRateList();
    // },
// 智慧经营客户数据卡片(视图)
    getCrmIntelligentRateList() {
      // this.is_table_loading = true;
      let params = Object.assign({}, this.params, this.house_params)
      if(params.start_date&&params.end_date){
       delete params.date_type
      }
      // if((this.website_id==109||this.website_id==176 )||(this.website_id==626&&ipAddress=="**************")){
        this.$http
        .newgetCrmIntelligentRateListcst({ params })
        .then((res) => {
          // this.is_table_loading = false;
          if (res.status === 200) {
            this.params.total = res.data.total;
            if (params.page == 1) {
              this.$emit("getDataOk", res)
            }
          }
        });
      // }else{
      //   this.$http
      //   .getCrmIntelligentRateListcst({ params })
      //   .then((res) => {
      //     // this.is_table_loading = false;
      //     if (res.status === 200) {
      //       this.params.total = res.data.total;
      //       if (params.page == 1) {
      //         this.$emit("getDataOk", res)
      //       }
      //     }
      //   });
      // }
    },
    // 获智慧经营客户数据列表(表格)
    getCrmIntelistFn() {
      this.is_table_loading = true;
      let params = Object.assign({}, this.params,this.house_params)
      if(params.start_date&&params.end_date){
        delete params.date_type
      }
      // if((this.website_id==109||this.website_id==176 )||(this.website_id==626&&ipAddress=="**************")){
        this.$http
        .newgetCrmIntelist({ params })
        .then((res) => {
          if (res.status === 200) {
            this.is_table_loading = false;
            this.headerList = res.data.header
            this.$nextTick(()=>{
              this.tableData = res.data.data;
              if( this.params.sort_style == 2){
              this.tableData = this.tableData.reverse()
              // console.log(this.tableData,"哈哈哈哈哈");
            }
            })
            this.params.total = res.data.total;
            this.params.page = res.data.current_page;
            // if (params.page == 1) {
              this.$emit("getheaderData", res.data.header)
            // }
          }
        });
    
    },
    onPageChange1(){
      // console.log(this.params,111111);
      // console.log(this.params);
      this.getCrmIntelistFn() 
    },
     //录入成员
     loadFirstLevelChildren(value) {
      if(value){
        this.params.admin_type = value[0]
      if(value[1]){
        this.params.admin_id = value[1]
      }else{
        this.params.admin_id = 0
      }
      }else{
        this.params.admin_type = 0
        this.params.admin_id = 0
      }
      this.$emit('custom-event', this.params.admin_type);
      this.getCrmIntelistFn() ; // 获取最新数据
      this.getCrmIntelligentRateList()
    },
    //查看，跳转到经营视图
    Jumplink(row){
      // const routeData = this.$router.resolve({
      //     name: 'crm_Follow_up_list',
      //     query: { alltype: 3, uid: row.id},
      // });
      // console.log("crm_customer_business_setting?type=crm");
      this.$goPath(`crm_Follow_up_list?alltype=3&uid=${row.id}`)
      // window.open(routeData.href);
    },
    onPageChange(e) {
      console.log(e);
      // this.params.page = e
      this.house_params.page = e
      this.getCrmIntelistFn() 
      // this.$emit("getData", e)
      // this.getCrmIntelligentRateList();
    },
    //每页几条
    handleSizeChange(e){
      this.params.per_page = e
      this.getCrmIntelistFn() 
    },
    //刷新
    Refresh() {
      this.getCrmIntelistFn() ; // 获取最新数据
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-table .cell {
  line-height: 30px;
}
::v-deep .el-table .el-table__fixed{
    padding-bottom: 0px !important;
}
::v-deep .el-table .el-table__fixed-right{
  padding-bottom: 0px !important;
}
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
.f-c {
  color: #2e3c4e;
  font-size: 14px;
  display: flex;
  align-items: center;

  .f {
    width: 20px;
    height: 8px;
    margin: 0 10px;

    &.f1 {
      background: #ffb88d;
    }

    &.f2 {
      background: #bedcff;
    }

    &.f3 {
      background: #ffeba5;
    }
  }
}

.name-box-cy {
  align-items: center;
  width: 180px;

  .left {
    margin-right: 7px;
    background: #2d84fb;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: #fff;
    line-height: 24px;
    text-align: center;
    flex-shrink: 0;
    font-size: 14px;
  }

  .right {
    font-size: 12px;

    .c {
      color: #2e3c4e;
    }

    .b {
      color: #8a929f;
    }
  }
}

.item_c {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.w200 {
    width: 200px;
  }

  &.w90 {
    width: 90px;
  }
}
.my-cascader{
  margin-left: 10px;
}
</style>
