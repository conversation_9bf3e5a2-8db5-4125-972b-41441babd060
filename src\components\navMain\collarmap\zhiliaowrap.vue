<template>
    <div>
        <!-- 操作按钮 -->
        <div class="container-header">
          <el-button type="primary" @click="adddatapackage">添加资料包</el-button>
        </div> 
        <!-- 资料包表格列表 -->
        <div class="tabstyle">
            <template>
                <el-table
                  :data="tableData"
                  style="width: 100%"
                  :header-cell-style="{ background: '#EBF0F7' }"
                  border
                  v-loading="is_table_loading">
                  <el-table-column
                    prop="title"
                    label="标题">
                  </el-table-column>
                  <!-- <el-table-column
                    prop="admin_name"
                    label="绑定成员">
                  </el-table-column> -->
                  <el-table-column
                    prop="created_at"
                    label="创建时间">
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    v-slot="{ row }">
                    <!-- <el-link type="primary">详情</el-link> -->
                    <el-link type="primary" @click="datashortlink(row.id)">资料包短链接</el-link>
                    <el-link type="success" style="margin: 0px 10px" @click="customerrecords(row.id)">获客记录</el-link>
                    <el-link type="primary" @click="dataminiprogram(row.id)">资料包小程序码</el-link>
                    
                    <el-link style="margin: 0px 10px" type="warning" @click="editorialmaterial(row.id)">编辑</el-link>
                    <el-link type="danger" @click="deletedata(row.id)">删除</el-link>
                  </el-table-column>
                </el-table>
                <div class="page_footer flex-row items-center">
                  <div class="page_footer_l flex-row flex-1 items-center">
                    <div class="head-list">
                    </div>
                    <div class="head-list">
                      <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                    </div>
                  </div>
                  <div style="margin-right:10px;">
                      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="params.total"
                      :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                      @current-change="handleCurrentChange" @size-change="handleSizeChange">
                    </el-pagination>
                  </div>
                </div>
            </template>
        </div>
        <el-dialog class="inlay" width="300px" :visible.sync="codeVisible">
          <div class="QR-box">
            <img :src="QRcode" alt="" />
          </div>
        </el-dialog>
        <addzlwrap ref="addzlwrap" v-if="dialogs.addzlwrap" @getzhiliaolist="getzhiliaolist" />
        <editzlwarp ref="editzlwarp" v-if="dialogs.editzlwarp" @getzhiliaolist="getzhiliaolist" />
        <customerrecords ref="customerrecords" v-if="dialogs.customerrecords"/>
    </div>
</template>
<script>
import addzlwrap from './add_zl_wrap.vue'
import customerrecords from './customer_records.vue'
import editzlwarp from './edit_zl_wrap.vue'
export default {
  components:{
    addzlwrap,
    editzlwarp,
    customerrecords,
  },
    data() {
        return {
            tableData:[],//资料包列表数据-
            website_id:"",//站点id
            dialogs:{
              addzlwrap:false,//添加资料包
              editzlwarp:false,//编辑资料包
              customerrecords:false,//获客记录
            },
            is_table_loading:false,
            params:{
              page: 1,
              per_page: 10,
            },
            codeVisible: false,
            QRcode: '', // 资料包小程序二维码
        }
    },
    created(){
        this.getzhiliaolist()
    },
    mounted(){
        this.website_id = this.$route.query.website_id
    },
    methods:{
        // 获取资料包列表
        getzhiliaolist(){
          this.is_table_loading = true
            this.$http.obtainlistdatapackages(this.params).then(res=>{ 
                if(res.status==200){
                  this.is_table_loading = false
                    this.tableData = res.data.data
                    this.params.total = res.data.total
                    // console.log(res,"这是资料包列表");
                }
            })
        },
        //刷新
        Refresh(){
          this.getzhiliaolist()
        },
        //添加资料包
        async adddatapackage(){
          this.dialogs.addzlwrap = true
          await this.$nextTick();  // 等待 DOM 更新完成
          this.$refs.addzlwrap.open();
        },
        //资料包小程序码
        dataminiprogram(id){
          this.$http.datapackageminiprogram(id).then(res=>{
            if(res.status==200){
              this.codeVisible = true; 
              this.QRcode = res.data; // 赋值二维码链接
            }
          })
        },
        //资料包短链接
        datashortlink(id){
          this.$http.datapackageshortlink(id).then(res=>{
            if(res.status == 200){
              // console.log(res,"duanlainjei ");
              this.$onCopyValue(res.data)
            }
          })
        }, 
        //获客记录
        async customerrecords(id){
          this.dialogs.customerrecords = true
          await this.$nextTick();  // 等待 DOM 更新完成
          this.$refs.customerrecords.open(id);
        },
        //编辑资料包
        async editorialmaterial(id){
          this.dialogs.editzlwarp = true
          await this.$nextTick();  // 等待 DOM 更新完成
          this.$refs.editzlwarp.open(id);
        },
        //删除资料包
        deletedata(id){
          this.$confirm('此操作将永久删除该资料包, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$http.deletedatapackage(id).then(res=>{
              if(res.status==200){
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                });
                this.getzhiliaolist()//刷新页面
              }
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作'
            });          
          });
        },
        //分页
        handleSizeChange(val){
             this.params.per_page = val
             this.getzhiliaolist()       
        },
        handleCurrentChange(val){
             this.params.page = val
             this.getzhiliaolist()
        },
    },
}
</script>
<style lang="scss" scoped>
.tabstyle{
  margin: 20px 0px;
  // margin-bottom: 20px;
  .page_footer {
    position: fixed;
    left: 230px;
    right: 0;
    bottom: 0;
    background: #fff;
    padding: 10px;
    z-index: 1000;
    .head-list{
      margin-left: 13px;
    }
  }
}
.inlay {
  ::v-deep .el-dialog {
    .el-dialog__header {
      .el-dialog__title {
        border-left: 0;
      }
    }
  }
}

.QR-box {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>