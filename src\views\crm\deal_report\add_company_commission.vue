<template>
<el-dialog :title="title" :visible.sync="show" width="786px">
    <el-form label-width="120px" style="padding-right: 40px">
        <el-alert title="默认公式：扣除款项 = 应收佣金总和 x 佣金点位。（如不满足业务需求请选择自定义输入）" type="warning"  :closable="false"/>
        <br>
        <el-form-item label="款项类型:">
            <el-radio-group v-model="params.cate_id">
                <el-radio :label="1">公司佣金</el-radio>
                <el-radio :label="2">返现</el-radio>
                <el-radio :label="3">补助</el-radio>
                <el-radio :label="4">其它</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="佣金金额:">
            {{params.amount}}
            <span> * </span>
            <el-input v-model="params.proportion" placeholder="扣除占比" style="width: 120px;" @input="handleProportionInput"><i slot="suffix">%</i></el-input>
            <span> = </span>
            <el-input v-model="params.commission" placeholder="扣除金额" style="width: 120px;" @input="handleCommissionInput"><i slot="suffix">元</i></el-input>
        </el-form-item>
        <el-form-item label="描述:">
            <el-input v-model="params.descp" placeholder="请输入描述" type="textarea"></el-input>
        </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submiting">保存</el-button>
    </span>
</el-dialog>
</template>  

<script>
export default {
    name: 'addCrmDealCompanyCommission',
    data(){
        return {
            show: false,        //dialog是否显示
            successFn: null, 
            isAdd: true,
            submiting: false,
            params: {}
        }
    },
    computed: {
        title(){
            return this.isAdd?'新增扣除款项':'编辑扣除款项';
        },
    },
    methods: {
        handleCommissionInput(val){
            let proportion = val / this.params.amount * 100;
            this.params.proportion = isNaN(proportion) ? '' : proportion.toFixed(2)*1;
        },
        handleProportionInput(val){
            let commission = this.params.amount * val  / 100;
            this.params.commission = isNaN(commission) ? '' : commission.toFixed(2)*1;
        },
        open(params){
            this.params = {
                id: 0,
                report_id: 0,
                cate_id: 1,
                amount: 0,
                proportion: '',
                commission: '',
                descp: ''
            };
            for(const key in params){
                if(this.params[key] !== undefined){
                    this.params[key] = params[key];
                }
            }
         
            this.isAdd = params.id ? false : true;
            this.show = true;
            console.log(params, this.params);
            return this
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancel(){
            this.show = false;
        },
        async submit(){
            const params = {
                report_id: this.params.report_id,
                cate_id: this.params.cate_id,
                commission: this.params.commission,
                descp: this.params.descp
            };

            if(!this.isAdd){
                params.id = this.params.id;
            }
            this.submiting = true;
            const res = await this.$http[this.isAdd?'addCompanyCommissionAPI':'editCompanyCommissionAPI'](params);
            this.submiting = false;
            if(res.status == 200){
                this.show = false;
                this.$message.success(res.data?.msg || '保存成功');
                this.successFn && this.successFn(res.data);
            }
        }
    }
}
</script>
<style lang="scss" scoped>  
</style>