<template>
  <div>
    <el-form label-width="100px">
      <el-form-item label="姓名">
        <div class="form-item-block">
          <el-input
            v-model="form_params.user_name"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入姓名"
          >
          </el-input>

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>

      <el-form-item label="手机号">
        <div class="form-item-block">
          <el-input
            v-model="form_params.phone"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入手机号"
          ></el-input>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="职位">
        <div class="form-item-block">
          <el-input
            v-model="form_params.post"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入职位"
          >
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="设置密码">
        <div class="form-item-block">
          <el-input
            v-model="form_params.password"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入密码"
            type="password"
          ></el-input>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="确认密码">
        <div class="form-item-block">
          <el-input
            v-model="form_params.password_confirmation"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入确认密码"
            type="password"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="所属部门">
        <div class="form-item-block form_customer flex-row f-wrap">
          <el-button
            size="big"
            class="member_button"
            v-for="(item,index) in form_selectedMember"
            :key="item.id" @click="removeSelectedMember(index)"
            >{{ item.name }}
              <span class="el-icon-close icon-close"></span>
            </el-button>

          <el-button size="big" class="el-icon-plus" @click="showAddDepartment"
            >选择部门</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="状态"
        v-if="
          !(
            form_params.roles &&
            form_params.roles.length &&
            form_params.roles[0].name == '站长'
          )
        "
      >
        <el-radio-group v-model="form_params.status" size="mini">
          <el-radio :label="1" border>启用</el-radio>
          <el-radio :label="0" border>禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="是否同步微信"
        v-if="website_info.self_auth_create_all"
      >
        <el-radio-group v-model="form_params.syn_wx" size="mini">
          <el-radio :label="1" border>同步</el-radio>
          <el-radio :label="0" border>不同步</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>

    <el-dialog
      :visible.sync="show_add_department"
      width="600px"
      title="选择部门"
      append-to-body
    >
      <div class="member" v-if="show_add_department">
        <div class="member_box flex-row">
          <div class="left member_con flex-1">
            <memberList
              :list="navList"
              :defaultValue="selectedIds"
              @onClickItem="selecetedDepartment"
              ref="memberList"
              :checkStrictly="true"
              :defaultProps="defaultProps"
              from="personnel"
            >
            </memberList>
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择部门</div>
            <div class="selected_list">
              <div
                class="selected_item flex-row align-center"
                v-for="item in selectedList"
                :key="item.id"
              >
                <div class="name flex-1">{{ item.name }}</div>
                <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="show_add_department = false"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="selectDepartmentOk"
            :loading="isSubmiting"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import memberList from "./memberList.vue";
export default {
  props: ["form", "navList", "website_info"],
  components: {
    memberList,
  },
  data() {
    return {
      form_params: {},
      show_add_department: false,
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      defaultProps: {
        children: "subs",
        label: "name",
      },
      selectedPids: [],
      isSubmiting: false,
    };
  },
  created() {
    //this.form_params = Object.assign({}, this.form);
    this.form_params = JSON.parse(JSON.stringify(this.form));
    this.selectedIds = this.form_params.wx_work_department_id;
    this.selectedList = this.form_params.department;
    this.form_selectedMember = [...this.form_params.department];
    if (
      this.form_params.all_department_id &&
      typeof this.form_params.all_department_id == "string"
    ) {
      this.form_params.all_department_id = this.form_params.all_department_id.split(
        ","
      );
    }
    if (
      typeof this.form_params.all_department_id == "object" &&
      Object.prototype.toString.call(this.form_params.all_department_id) ==
      "[object Object]"
    ) {
      let arr = [];
      for (const key in this.form_params.all_department_id) {
        arr.push(this.form_params.all_department_id[key]);
      }
      this.form_params.all_department_id = arr;
    }
    this.selectedPids = this.form_params.all_department_id || [];
  },
  methods: {
    subform() {
      if (!this.website_info.self_auth_create_all) {
        this.form_params.syn_wx = 0;
      }
      let params = Object.assign({}, this.form_params);
      if (params.wx_work_department_id && params.wx_work_department_id.length) {
        params.wx_work_department_id = params.wx_work_department_id.join(",");
        params.all_department_id = params.all_department_id.join(",");
      } else {
        params.wx_work_department_id = ''
        params.all_department_id = ''
      }

      delete params.department;
      params.website_id = localStorage.getItem("website_id");
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .editCrmMember(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("编辑成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("编辑失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
    showAddDepartment() {
      this.show_add_department = true;
    },
    selectDepartmentOk() {
      this.form_selectedMember = this.selectedList;
      this.form_params.wx_work_department_id = this.selectedIds;
      this.form_params.all_department_id = this.selectedPids;
      this.show_add_department = false;
    },
    selecetedDepartment(e) {
      // 选中会员
      console.log(1232333);
      this.selectedPids = [];
      this.selectedIds = e.checkedKeys;
      this.selectedList = e.checkedNodes;
      e.checkedNodes.map((item) => {
        this.selectedPids.push(item.pArr + "," + item.id);
      });
    },
    deleteSelected(e) {
      let idx = this.selectedList.findIndex((item) => item.id == e.id);
      this.selectedList.splice(idx, 1);
      this.selectedIds.splice(idx, 1);
      this.selectedPids.splice(idx, 1);
      setTimeout(() => {
        this.$refs.memberList.changeSelected(this.selectedIds, true);
      }, 100);
    },
    removeSelectedMember(index){
      const item = this.form_selectedMember.splice(index, 1)[0];
      this.selectedList = this.selectedList.filter(e => e.id != item.id);
      this.selectedIds = this.selectedIds.filter(id => id != item.id);
      this.selectedPids = this.selectedPids.filter(id => id != item.id);

      this.form_params.wx_work_department_id = this.selectedIds;
      this.form_params.all_department_id = this.selectedPids;

    }
  },
};
</script>

<style lang="scss" scoped>
.member_button{
  position: relative;
  padding-right: 26px;
  .icon-close{
    font-size: 16px;
    position: absolute;
    z-index: 1;
    right: -2px;
    top: -2px;
    color: #aaa;
    padding: 3px;
    transition: color .2s;
    &:hover{
      color: #3c3c3c;
    }
  }
}
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}

.form-item-block {
  .el-button {
    margin-right: 5px;
  }
  &.form_customer {
    .el-button {
      margin-bottom: 5px;
    }
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
</style>
