<template>
    <div class="housingMaintain">
        <!-- 标题 -->
        <div class="Maintain_top">
            房源维护完成度
            <p>{{MaintainData.percent}}</p>
        </div>
        <!-- 进度条 -->
        <div class="Maintain_progress">
            <el-progress 
              :percentage="MaintainProgress" 
              color="#FE6E46"
            >
            </el-progress>
        </div>
        <!-- 提示信息 -->
        <div class="Maintain_message">
            房源维护完成度越高越及时，成交可能性越大！
        </div>
          <!-- 房源分 -->
          <div>房源分: {{detailScore}}</div>
          <!-- 折叠信息 -->
          <div class="Maintain_information">
              <div v-for="(item,index) in score_list" :key="index">
                  <!-- 标题 -->
                  <div class="fold_child" @click="scoreControl_click(item)">
                      {{item.name}}
                      <span 
                        v-if="item.status == 0"
                        @click="foldTitleClick(index)"
                      >
                        {{item.tip_no}}
                      </span>
                      <span 
                        v-if="item.status == 1"
                        @click="foldTitleClick(index)"
                      >
                        {{item.tip_yes}}
                      </span>
                      <i 
                        v-show="item.sub.length != 0" 
                        class="el-icon-arrow-down my_active"
                        :class="item.isOpen ? 'my_active' : 'my_active_on'"
                      >
                      </i>
                      <!-- 标题右侧百分比 -->
                      <div class="fold_percentage" v-if="item.score">
                        <span 
                          v-if="item.status == 1"
                          :style="item.status == 1 ? 'color: #262C3D;' : 'color: #F44554;'"
                        >
                          {{item.score}}分/
                        </span>
                        <span
                          v-if="item.status == 0"
                          :style="item.status == 0 ? 'color: #F44554;' : ''"
                        >
                          0分/
                        </span> 
                        <span>{{item.score}}分</span>
                      </div>
                      <div class="fold_percentage" v-if="item.sub.length > 0">
                        <span 
                          :style="item.sub_score == item.sub_total ? 'color: #262C3D;' : 'color: #F44554;'"
                        >
                          {{item.sub_score}}分/
                        </span>
                        <span>{{item.sub_total}}分</span>
                      </div>
                  </div>
                  <!-- 内容 -->
                  <div 
                    class="fold_content" 
                    v-show="item.sub.length != 0 && item.isOpen"
                  >
                    <div 
                      class="fold_content_arr" 
                      v-for="(arr,idx) in item.sub" :key="idx"
                    >
                      {{arr.name}}
                      <span 
                        v-if="arr.status == 0" 
                        @click="foldContentClick(index)"
                      >
                        {{arr.tip_no}}
                      </span>
                      <span 
                        v-if="arr.status == 1" 
                        @click="foldContentClick(index)"
                      >
                      {{arr.tip_yes}}</span>
                      <!-- 内容右侧百分比 -->
                      <div class="fold_content_percentage">
                        <span 
                          v-if="arr.status == 1" 
                          class="percentage_active_on"
                        >
                          {{arr.score}}分/
                        </span>
                        <span
                          v-if="arr.status == 0" 
                          class="percentage_active"
                        >
                          0分/
                        </span> 
                        <span>{{arr.score}}分</span>
                      </div>
                    </div>
                  </div>
              </div>
          </div>
    </div>
</template>
<script>
export default {
    props: {
        MaintainData:{
          type:[Object],
          default:() => {}
        },
        detailScore: {
          type: Number,
          default:() => {}
        },
    },
    data() {
        return {
            scoreControl: false, // 子折叠控制按钮
            scrollTop: ''
        }
    },
    computed: {
        // 进度条百分比
        MaintainProgress() {
            const num = this.MaintainData ? this.MaintainData.percent : ''
            let num_1 = null
            if(num) {
                num_1 = num.split('%')[0]
            }
            return Number(num_1)
        },
        // 修改父组件传递过来的数据
        score_list() {
            const data = this.MaintainData ? this.MaintainData.score_list : ''
            let data_1 = null
            if(data) {
                data_1 = data
            }
            console.log(data_1,"this.score_list")
            return data_1
        }
    },
    created() {
        this.id = this.$route.query.id;
    },
    methods: {
        // 子折叠控制按钮
        scoreControl_click(item) {
            if(item.sub != "") {
              item.isOpen = !item.isOpen
            }
        },
        // 房源维护折叠信息内容点击事件
        foldContentClick(index) {
          console.log(index,"内容点击")
          if(index == 0) {
            this.$goPath("/house_edit?id=" + this.id);
          } else if(index == 1) {
            this.$goPath("/house_edit?id=" + this.id +"&step=2");
          }else if(index == 5) {
            const num = {id:3, name:"钥匙委托"}
            this.$emit('foldTitlePhoto',num)
          }else if(index == 6) {
            const num = {id:3, name:"钥匙委托"}
            this.$emit('scollView','#cont',num)
          }
        },
        // 房源维护折叠信息标题点击事件
        foldTitleClick(index) {
          console.log(index,"点击标题")
          if(index == 2) {
            const num = {id:2, name:"房源相册"}
            this.$emit('foldTitlePhoto',num)
          }else if(index == 3) {
            const num = {id:2, name:"房源相册"}
            this.$emit('foldTitlePhoto',num,2)
          }else if(index == 4) {
            const num = {id:2, name:"房源相册"}
            this.$emit('foldTitlePhoto',num,3)
          }else if(index == 5) {
            const num = {id:3, name:"钥匙委托"}
            this.$emit('foldTitlePhoto',num)
          }else if(index == 8 || index == 9) {
            this.$goPath("/house_edit?id=" + this.id);
          }
        },
    }
}
</script>
<style scoped lang="scss">
.housingMaintain {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  .Maintain_top {
    display: flex;
    font-size: 18px;
    font-weight: 600;
    & p {
      margin-left: auto;
    }
  }
  ::v-deep .Maintain_progress {
    padding: 5px 0;
    .el-progress-bar .el-progress-bar__inner {
      border-radius: inherit;
    }
    .el-progress-bar .el-progress-bar__outer {
      border-radius: inherit;
    }
    .el-progress-bar {
      padding-right: 0;
    }
    .el-progress__text {
      display: none;
    }
  }
  .Maintain_message {
    font-size: 14px;
    & span {
      color: #3A55CD;
    }
  }
  .Maintain_control {
    cursor: pointer;
    color: #4398F1;
    font-size: 18px;
    & i {
      font-size: 16px;
      font-weight: 600;
      position: relative;
      right: 3px;
    }
  }
  .Maintain_information {
    display: flex;
    flex-direction: column;
    .fold_child {
        display: flex;
        cursor: pointer;
        padding: 5px 0;
        margin-right: 10px;
        & span {
          color: #3A55CD;
          // margin-left: 5px;
          font-size: 14px;
          cursor: pointer;
        }
        & i {
          color: #CFCFD4;
          font-size: 16px;
          height: 16px;
          margin-top: 3px;
        }
        .my_active {
            transform-origin:center center;
            transition: transform 0.2s;
            transform: rotate(-180deg);
            }
        .my_active_on {
            transform-origin:center center;
            transition: transform 0.2s;
            transform: rotate(0deg);
        }
        .fold_percentage {
          display: flex;
          margin-left: auto;
          & span:last-child {
            // width: 30px;
            color: #AEA4B6;
            text-align: right;
          }
        }
    }
    .fold_content {
      display: flex;
      flex-direction: column;
      background-color: #F2F3F4;
      .fold_content_arr {
        display: flex;
        padding: 5px 10px;
        font-size: 14px;
        & span {
          color: #3A55CD;
          // margin-left: 5px;
          cursor: pointer;
        }
        .fold_content_percentage {
          display: flex;
          margin-left: auto;
          & span {
            color: #AEA4B6;
            cursor: initial;
          }
          & span:last-child {
            // width: 30px;
            text-align: right;
          }
          .percentage_active {
            color: #F44554;
          }
          .percentage_active_on {
            color: #262C3D;
          }
        }
      }
    }
  }
}
</style>