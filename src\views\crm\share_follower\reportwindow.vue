<template>
    <el-dialog :visible.sync="show" title="报备客户" width="30%" append-to-body>
        <div style="width: 90%;">
            <el-form ref="form" :model="formdata" label-width="80px">
                <el-form-item label="客户姓名">
                  <el-input v-model="formdata.cname"></el-input>
                </el-form-item>
                <el-form-item label="手机号" v-for="(item,index) in formdata.mobile" :key="index">
                  <div class="mobile-wrapper">
                    <el-input v-model="formdata.mobile[index]" class="mobile-input"></el-input>
                    <div class="icon-btn">
                      <i class="el-icon-circle-plus-outline" v-if="index==0" @click="addMobile"></i>
                      <i class="el-icon-remove-outline" v-else @click="removeMobile(index)"></i>
                    </div>
                  </div>
                  
                </el-form-item>

                <el-form-item label="报备项目">
                <!-- <tCrmProjectSelect multiple allow-create default-first-option v-model="formdata.report_project"
                value-key="name" @change="onProjectSelected"
                  width="100%" /> -->
                  <t-crm-project-select default-first-option v-model="formdata.project_id" value-key="id"
                  placeholder="请搜索选择项目" ref="auditProjectSelect" width="100%" multiple/>
              </el-form-item>
              <el-form-item label="报备日期">
                <!-- <el-date-picker style="width: 100%" type="date" v-model="formdata.visit_time" placeholder="选择日期"
                  value-format="yyyy-MM-dd">
                </el-date-picker> -->
              <el-date-picker
                style="width: 100%"
                v-model="formdata.visit_time"
                type="datetime"
                placeholder="选择日期时间"
                default-time="12:00:00"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              </el-form-item>
              <el-form-item label="带访时间" class="appointment-time">
                <div class="time-box">
                  <div class="time-boxList" :class="{ 'time-active': item.id == formdata.visit_type }"
                    v-for="(item, index) in timeValue" :key="index" @click="appointmentTime(item)">
                    <span>{{ item.value }}</span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="渠道专员">
                <el-select style="width: 100%" v-model="formdata.channel_uid" @change="changeAudit" multiple>
                  <el-option v-for="item in matchedItems" :key="item.values" :value="item.values" :label="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="cancle">取 消</el-button>
            <el-button type="primary" :loading="submitiing" @click="confirm">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import tCrmProjectSelect from "@/components/tplus/tSelect/tCrmProjectSelect.vue";
export default {
    name: 'reportwindow',
    components:{
        tCrmProjectSelect
    },
    data(){
        return {
            show: false,        //dialog是否显示
            submitiing: false,
            params: { } ,         //表单参数
            formdata:{
              cname:"",
              mobile:[''],
              visit_type:"1",
              visit_time:"",
              project_id: [],
              report_project: '',
              channel_uid: [],

            },//报备参数
            // 带看时间
            timeValue: [
              {
                value: '上午',
                id: 1
              },
              {
                value: '中午',
                id: 2
              },
              {
                value: '晚上',
                id: 3
              }
            ],
            Auditingdata:[],//审核人员id
            matchedItems: [],//审核人员数据
        }
    },
    methods: {
        //打开弹层
        open(params){
            // 获取当前时间
            let currentDate = new Date();
            let year = currentDate.getFullYear();
            let month = String(currentDate.getMonth() + 1).padStart(2, '0'); // 月份补零
            let day = String(currentDate.getDate()).padStart(2, '0'); // 日补零
            let hours = String(currentDate.getHours()).padStart(2, '0'); // 小时补零
            let minutes = String(currentDate.getMinutes()).padStart(2, '0'); // 分钟补零
            let seconds = String(currentDate.getSeconds()).padStart(2, '0'); // 秒补零
            let formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            this.formdata.visit_time = formattedDateTime;

            this.params = params;
            this.formdata.client_id = this.params.id
            this.formdata.cname = this.params.cname
            this.formdata.mobile = params.mobiles.length ? params.mobiles : [''];
            this.formdata.follow_id = params.lookfollow_id
            this.formdata.project_id = [];
            this.formdata.channel_uid =  [];
            this.formdata.report_project = '';
            this.matchedItems=[]
            this.reportReview()
            this.show = true;
            return this;
        },
        addMobile(){
          this.formdata.mobile.push('')
        },
        removeMobile(index){
          this.formdata.mobile.splice(index, 1)
        },
        // 当点击带看时间触发
        appointmentTime(item) {
          this.formdata.visit_type = item.id;
          this.$forceUpdate();
        },
        //获取报备审核员
        reportReview(){
            this.$http.getAuthShow("report_uid").then((res)=>{
                if(res.status==200){
                    this.Auditingdata = res.data
                    this.addColleague()
                }
            })
        },
        //全部成员
        addColleague() {
            this.$http.getColleagueDetailsList().then((res) => {
              if (res.status == 200) {
                let uids = this.Auditingdata ? String(this.Auditingdata).split(',').map(e=>e*1) : [];
                this.matchedItems = (res.data || []).filter( item => uids.includes(item.values))
              }
            })
        },
        onProjectSelected(selectedProjects){
            console.log(selectedProjects);
        },
        //报备审核
        changeAudit(){

        },
        //取消
        cancle(){
            this.show = false;
        },
        //确定
        confirm(){
          const formdata = {...this.formdata};
          if(!this.formdata.cname){
                return this.$message.warning("客户姓名不能为空！")
            }
            formdata.mobile = formdata.mobile.filter(e => e.trim() !== '');
            if(!formdata.mobile.length){
                return this.$message.warning("手机号不能为空！")
            }
            if(!formdata.project_id.length){
                return this.$message.warning("请选择报备项目！")
            }
            if(!this.formdata.visit_time){
                return this.$message.warning("报备日期不能为空！")
            }
            if(!this.formdata.visit_type){
                return this.$message.warning("报备时间不能为空！")
            }
            if(!this.formdata.channel_uid.length){
                return this.$message.warning("审核人员不能为空！")
            }
            const auditProjectSelect = this.$refs?.auditProjectSelect;
            const options = auditProjectSelect ? auditProjectSelect.getSelectOption() : [];
            formdata.project_id = formdata.project_id.join(',');
            formdata.report_project = options.map(e=>e.name).join(',');
            formdata.channel_uid  = formdata.channel_uid.join(',');
            formdata.mobile  = formdata.mobile.join(',');

            this.submitiing = true;
            this.$http.crmaddmitreport(formdata).then((res)=>{
              this.submitiing = false;
                if(res.status==200){
                    this.$message.success("报备成功！")
                    this.show = false;
                    this.matchedItems=[]
                    this.$emit("success")
                }
            }).catch(e=>{
              this.submitiing = false;
            })
            
        }
    }
}
</script>
<style lang="scss" scoped>
      .el-form {
    .appointment-time {
      .el-form-item__content {
        .time-box {
          display: flex;
          // align-items: center;
          height: 30px;
          background: #f0eff4;
          border: 1px solid #f0eff4;
          border-radius: 6px;
          cursor: pointer;

          .time-boxList {
            display: flex;
            align-items: center;
            background: #f0eff4;
            padding: 4px 43px;
            border: 1px solid #f0eff4;

            & span {
              font-size: 12px;
              white-space: nowrap;
            }
          }

          .time-active {
            background: #ffffff;
            border: 1px solid #f0eff4;
            border-radius: 6px;
          }
        }
      }
    }
  }

.mobile-wrapper{
  display: flex;
  flex-direction: row;
  .icon-btn{
    cursor: pointer;
    color: #409eff;
    margin-left: 12px;
    font-size: 24px;
  }
}
</style>