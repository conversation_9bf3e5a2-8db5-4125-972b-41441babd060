<template>
  <div class="pages">
    <div class="pages_content">
      <div class="check-box div row" id="pages_content">
      <div
        v-for="item in tabs_list"
        @click="onClickTabs(item)"
        :class="{ isactive: item.title === is_tabs_page }"
        :key="item.id"
        class="check-item"
      >
        {{ item.name }}
      </div>
    </div>
    <div
      style="background: #fff; padding: 24px; min-height: 110vh"
      :is="is_tabs_page"
      keep-alive
    ></div>
    </div>
  </div>
</template>

<script>
import setting from "./default/setting";
import imenu from "./default/menu";
import message from "./default/message";
import company from "./default/company";
export default {
  components: {
    setting,
    imenu,
    message,
    company,
  },
  data() {
    return {
      tabs_list: [
        // { id: 1, name: "通用配置", title: "general" },
        { id: 1, name: "默认配置", title: "setting" },
        { id: 2, name: "系统菜单", title: "imenu" },
        { id: 5, name: "消息开关", title: "message" },
        { id: 3, name: "角色管理", title: "role" },
        { id: 4, name: "成员菜单", title: "member" },
        { id: 6, name: "公司信息", title: "company" }
      ],
      is_tabs_page: "setting",
    };
  },
  mounted() {},
  methods: {
    /* eslint-disable */
    onClickTabs(e) {
      if (e.title === "role") {
        this.$router.push("permission_list");
        return;
      }
      if (e.title === "member") {
        this.$router.push("crm_customer_personnel");
        return;
      }
      this.is_tabs_page = e.title;
    },
  },
};
</script>

<style lang="scss" scoped>
.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  margin-left: 20px;
  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;
    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}
.pages_content{
  max-height: calc(100vh - 209px); /* 限制最大高度为视口高度 */
  overflow-y: auto;  /* 添加垂直滚动条，按需显示 */
  margin-left: 44px;
}
</style>
