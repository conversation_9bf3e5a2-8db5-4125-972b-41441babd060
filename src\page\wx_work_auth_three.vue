<template>
  <div></div>
</template>

<script>
// import getWxWorkjssdk from "@/utils/getWxWorkjssdk.js";
export default {
  data() {
    return {
      user_id: "",
      params: {
        website_id: "",
        redirect_uri: "",
      },
    };
  },
  mounted() { },
  created() {
    // this.getWxQyWxConfig(
    //   ["agentConfig", "getCurExternalContact"],
    //   (wx) => {
    //     this.wx = wx;
    //     this.getWxworkUserid();
    //   },
    //   this.$route.query.site_id
    // );
    let website_id = this.getUrlKey("site_id");
    localStorage.setItem("website_id", website_id);
    this.params = {
      redirect_uri: window.location.href,
      website_id: website_id,
    };
    let code = this.getUrlKey("code");
    if (!code) {
      this.setWxWorkGetauthorize();
    } else {
      let website_id = localStorage.getItem("website_id");
      // 区分登录系统
      if (this.isMobile()) {
        let url =
          window.location.origin +
          `/fenxiao/admin/wx_work_auth?website_id=${website_id}&type=2&code=${code}`;
        window.location.href = url;
      } else {
        this.getWxWorkSiteToken(code, website_id);
      }
    }
  },
  // mixins: [getWxWorkjssdk],
  methods: {
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
    // 获取路径中的参数（code等）
    getUrlKey(name) {
      return (
        decodeURIComponent(
          (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
            location.href
          ) || ["", ""])[1].replace(/\+/g, "%20")
        ) || null
      );
    },
    setWxWorkGetauthorize() {
      this.$http
        .setWxWorkGetauthorize({
          params: this.params,
        })
        .then((res) => {
          if (res.status === 200) {
            window.location.href = res.data;
          }
        });
    },
    getWxWorkSiteToken(code, id) {
      // 自建应用代开发
      this.$http
        .getWxWorkSiteToken({ params: { code: code, website_id: id } })
        .then(async (res) => {
          // let result = await this.$http.getInfoConfig()
          // if (result.status == 200) {
          //   console.log();
          // } else {
          //   this.$store.state.disableClick = result.data.message
          // }
          if (res.status === 200) {
            localStorage.setItem("website_id", id);
            localStorage.setItem("auth_way", res.data.auth_way || 0);
            localStorage.setItem("TOKEN", res.data.token);
            localStorage.setItem("admin_token_" + id, res.data.token);
            let url = `https://yun.tfcs.cn/admin/#/index?website_id=${id}`;
            window.location.href = url;
          }
        });
    },
    getWxworkUserid() {
      var _this = this;
      _this.wx.invoke("getCurExternalContact", {}, function (res) {
        if (res.err_msg == "getCurExternalContact:ok") {
          _this.user_id = res.userId;
          console.log(_this.user_id);
        } else {
          //错误处理
        }
      });
    },
  },
};
</script>

<style></style>
