<template>
	<el-form inline size="medium">
		<!-- 日期范围 -->
		<el-form-item label="分析时间">
			<el-date-picker
				v-model="dateRange"
				type="daterange"
				start-placeholder="开始日期"
				end-placeholder="结束日期"
				value-format="yyyy-MM-dd"
				@change="handleDateChange"
				:picker-options="pickerOptions">
			</el-date-picker>
		</el-form-item>

		<!-- 关键词 -->
		<el-form-item label="关键词">
			<el-input
				v-model="localParams.keywords"
				placeholder="请输入关键词"
				clearable
				maxlength="4"
				show-word-limit
				@change="handleSearch">
			</el-input>
		</el-form-item>

		<!-- 兴趣偏好 -->
		<el-form-item label="兴趣偏好">
			<el-input
				v-model="localParams.intention_community"
				placeholder="请输入兴趣偏好"
				clearable
				maxlength="4"
				show-word-limit
				@change="handleSearch">
			</el-input>
		</el-form-item>
		
		<!-- 健康状态 -->
		<el-form-item label="健康状态">
			<el-select v-model="localParams.is_health" clearable placeholder="选择健康状态" @change="handleSearch">
				<el-option label="健康" :value="1"></el-option>
				<el-option label="违规" :value="2"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 成交意向等级 -->
		<el-form-item label="成交意向">
			<el-select v-model="localParams.deal_level" clearable placeholder="选择成交意向等级" @change="handleSearch">
				<el-option label="A级" :value="1"></el-option>
				<el-option label="B级" :value="2"></el-option>
				<el-option label="C级" :value="3"></el-option>
				<el-option label="D级" :value="4"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 流失风险等级 -->
		<el-form-item label="流失风险">
			<el-select v-model="localParams.loss_level" clearable placeholder="选择流失风险等级" @change="handleSearch">
				<el-option label="低风险" :value="1"></el-option>
				<el-option label="中风险" :value="2"></el-option>
				<el-option label="高风险" :value="3"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 客户需求等级 -->
		<el-form-item label="客户需求">
			<el-select v-model="localParams.demand_level" clearable placeholder="选择客户需求等级" @change="handleSearch">
				<el-option label="有效" :value="1"></el-option>
				<el-option label="无效" :value="2"></el-option>
				<el-option label="暂缓" :value="3"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 资料维护 -->
		<el-form-item label="资料维护">
			<el-select v-model="localParams.is_cover" clearable placeholder="选择资料维护" @change="handleSearch">
				<el-option label="已采纳" :value="1"></el-option>
				<el-option label="未采纳" :value="0"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 是否分配 -->
		<el-form-item label="AI分配">
			<el-select v-model="localParams.is_assign" clearable placeholder="选择AI分配" @change="handleSearch">
				<el-option label="已分配" :value="1"></el-option>
				<el-option label="未分配" :value="0"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 留资状态 -->
		<el-form-item label="留资状态">
			<el-select v-model="localParams.is_follow" clearable placeholder="选择留资状态" @change="handleSearch">
				<el-option label="已有维护人" :value="1"></el-option>
				<el-option label="暂无维护人" :value="0"></el-option>
			</el-select>
		</el-form-item>
		
		<!-- 部门筛选 -->
		<el-form-item label="部门">
			<el-cascader
				v-model="localParams.department_id"
				:options="departmentList"
				:props="{
					value: 'id',
					label: 'name',
					children: 'subs',
					emitPath: false,
					checkStrictly: true
				}"
				placeholder="请选择部门"
				clearable
				filterable
				@change="handleDepartmentChange">
			</el-cascader>
		</el-form-item>
		
		<!-- 人员筛选 -->
		<el-form-item label="成员">
			<el-select 
				v-model="localParams.admin_id" 
				filterable 
				placeholder="请输入成员姓名"
				@change="handleSearch"
				clearable>
				<el-option
					v-for="item in userList"
					:key="item.values"
					:label="item.name"
					:value="item.values">
				</el-option>
			</el-select>
		</el-form-item>
		
		<!-- 操作按钮 -->
		<el-form-item>
			<el-button type="primary" @click="handleSearch">查询</el-button>
			<el-button @click="handleReset">重置</el-button>
		</el-form-item>
	</el-form>
</template>

<script>
export default {
	name: 'FilterBar',
	props: {
		params: { type: Object, default: () => ({}) }
	},
	data() {
		return {
			localParams: {},
			departmentList: [],
			userList: [],
			dateRange: [], // 日期范围 [start_date, end_date]
			pickerOptions: {
				shortcuts: [
					{
						text: '今天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '昨天',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24);
							end.setTime(end.getTime() - 3600 * 1000 * 24);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '本周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							const dayOfWeek = start.getDay();
							start.setTime(start.getTime() - (dayOfWeek - 1) * 24 * 3600 * 1000);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '上周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							const dayOfWeek = start.getDay();
							start.setTime(start.getTime() - (dayOfWeek + 6) * 24 * 3600 * 1000);
							end.setTime(start.getTime() + 6 * 24 * 3600 * 1000);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '本月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setDate(1);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '上月',
						onClick(picker) {
							const start = new Date();
							start.setMonth(start.getMonth() - 1);
							start.setDate(1);
							const end = new Date();
							end.setDate(0);
							picker.$emit('pick', [start, end]);
						}
					}
				],
				disabledDate(time) {
					return time.getTime() > Date.now();
				}
			}
		}
	},
	watch: {
		params: {
			handler(newParams) {
				this.localParams = { ...newParams };
			},
			immediate: true,
			deep: true
		}
	},
	created() {
		this.getDepartmentList();
		this.getUserList();
	},
	methods: {
		// 获取部门列表
		async getDepartmentList() {
			try {
				const res = await this.$http.getDepartmentList();
				if (res.status === 200) {
					this.departmentList = res.data || [];
				} else {
					console.error('获取部门列表失败:', res.data?.message);
				}
			} catch (error) {
				console.error('获取部门列表失败:', error);
			}
		},

		// 处理部门选择变更
		handleDepartmentChange(value) {
			this.localParams.department_id = value;
			this.handleSearch();
		},
		
		// 获取用户列表
		async getUserList() {
			try {
				const res = await this.$http.getUserListDepartment({});
				if (res.status === 200) {
					this.userList = res.data || [];
				} else {
					console.error('获取用户列表失败:', res.data?.message);
					this.userList = [];
				}
			} catch (error) {
				console.error('获取用户列表失败:', error);
				this.userList = [];
			}
		},
		
		// 处理搜索
		handleSearch() {
			this.$emit('search', this.localParams);
		},
		
		// 处理日期变更
		handleDateChange(dateRange) {
			if (dateRange && dateRange.length === 2) {
				this.localParams.start_date = dateRange[0] + ' 00:00:00';
				this.localParams.end_date = dateRange[1] + ' 23:59:59';
			} else {
				this.localParams.start_date = '';
				this.localParams.end_date = '';
			}
			this.handleSearch();
		},
		
		// 重置筛选
		handleReset() {
			this.localParams = {
				page: 1,
				per_page: this.localParams.per_page || 10,
				deal_level: '',
				loss_level: '',
				demand_level: '',
				is_cover: '',
				is_assign: '',
				is_follow: '',
				is_health: '',
				admin_id: '',
				department_id: '',
				start_date: '',
				end_date: ''
			};
			this.dateRange = [];
			this.userList = [];
			this.$emit('reset');
		}
	}
}
</script>

<style scoped>
.el-form {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-start;
}

.el-form-item {
	margin-bottom: 15px;
	margin-right: 0px;
	display: flex;
	align-items: center;
	min-height: 36px;
}

.el-form-item:last-child {
	margin-right: 0;
}

/* 设置标签固定宽度，4个汉字宽度约80px */
.el-form-item ::v-deep .el-form-item__label {
	width: 70px;
	text-align: right;
	padding-right: 8px;
	line-height: 1.2;
	margin-bottom: 0;
	flex-shrink: 0;
}

/* 统一输入框宽度，调整日期选择器宽度 */
.el-form-item ::v-deep .el-form-item__content {
	width: 240px;
	flex: 0 0 240px;
}

.el-date-editor--daterange.el-input__inner{
	width: 240px !important;
}

/* 确保下拉框和输入框宽度一致 */
.el-form-item ::v-deep .el-select,
.el-form-item ::v-deep .el-input,
.el-form-item ::v-deep .el-cascader {
	width: 100%;
}

/* 处理换行时的对齐 */
.el-form-item ::v-deep .el-form-item__label {
	align-self: flex-start;
	margin-top: 9px; /* 垂直居中调整 */
}

/* 查询按钮左侧间距 */
.el-form-item:last-child .el-button:first-child {
  margin-left: 6px;
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
	.el-form {
		flex-direction: column;
		align-items: stretch;
	}
	
	.el-form-item {
		margin-right: 0;
		margin-bottom: 10px;
	}
	
	.el-form-item ::v-deep .el-form-item__label {
		width: 100px;
	}
	
	.el-form-item ::v-deep .el-form-item__content {
		width: 100%;
		max-width: 220px;
	}
}
</style>
