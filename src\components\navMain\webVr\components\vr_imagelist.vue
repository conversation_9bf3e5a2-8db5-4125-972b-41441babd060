<template>
    <div>
        <div class="material_list">
            <div class="material_main" v-for="(item, index) in forData_list" :key="index">
                <div class="material_edge">
                    <!-- 多选框 -->
                    <div class="material-checkbox">
                        <el-checkbox @change="check($event, item)"></el-checkbox>
                    </div>
                    <!-- 展示图 -->
                    <div class="material-pictures">
                        <img :src="item.thumb_path" alt="">
                    </div>
                    <!-- 标题栏 -->
                    <div class="material-title">
                        <div class="title">{{ item.filename }}</div>
                        <div class="modify">修改： {{ item.create_time }}</div>
                        <!-- <div class="title-size">大小： {{ (item.media_size/ 1024).toFixed(1) }} MB</div> -->
                        <div class="title-size">VR作品： {{ item.is_bind_project == 1 ? '已绑定' : '未绑定' }}</div>
                        <div class="switch" @click="onPreview(item)">
                            <span>预览</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mask1" v-if="dialogUploadPicture" @click="dialogUploadPicture = false">
            <div class="preview_img" @click.prevent.stop="() => { }">
                <img id="preImg" :src="dialogPhoto" alt="" />
            </div>
        </div>
        <!-- ====== -->
    </div>
</template>
<script>
export default {
    props: {
        forData_list: {
            type: Array,
            default: () => { }
        },
    },
    data() {
        return {
            // checked:"",
            dialogPhoto: "",
            dialogUploadPicture: false, // 预览图片模态框
            Vr_photoid:[]
        }
    },
    methods: {
        // aaa(item){
        //     console.log();
        //      this.$emit("Vr_photoid",item.pk_img_main )
        // },
        check(e, item) {
            if (e == true) {
               this.Vr_photoid.push(item.pk_img_main)
                console.log(this.Vr_photoid);
                this.$emit("Vr_photoid", this.Vr_photoid)
            }
        },
        // 素材预览
        onPreview(item) {
            this.dialogUploadPicture = true;
            this.dialogPhoto = item.thumb_path
        },
        // 关闭预览模态框
        closePicture() {
            this.dialogPhoto = "";
        },
    },
    mounted() {
        // console.log(this.checked);
    }

}
</script>
<style lang="scss" scoped>
.material_list {

    // box-sizing: border-box;
    // background-color: #F1F4FA;
    // max-height: calc(100vh - 335px);
    // overflow-y: auto;
    ::v-deep .material_main {
        padding: 24px 24px 0 24px;
        background-color: #FFFFFF;
        cursor: pointer;

        .material_edge {
            display: flex;
            padding-bottom: 24px;
            border-bottom: 1px solid #F1F4FA;
        }

        .material-checkbox {
            line-height: 160px;
        }

        .material-pictures {
            width: 160px;
            height: 160px;
            background-color: #F8F8F8;
            margin-left: 12px;
            position: relative;

            & img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .material-title {
            display: flex;
            flex-direction: column;
            margin-left: 24px;

            .title {
                font-size: 16px;
                color: #2E3C4E;
                font-weight: 600;
            }

            .title-size,
            .modify {
                font-size: 14px;
                color: #8A929F;
                line-height: 20px;
                font-weight: 400;
            }

            .modify {
                margin: 12px 0;
            }

            .switch {
                width: 88px;
                text-align: center;
                box-sizing: border-box;
                background: #FFFFFF;
                font-size: 14px;
                color: #2D84FB;
                font-weight: 500;
                margin-top: auto;
                padding: 7px 19px;
                border-radius: 4px;
                border: 1px solid #DDE1E9;
                cursor: pointer;
            }

            .switch:hover {
                border-color: #2D84FB;
            }
        }
    }

    .material_main:hover {
        background-color: #f7faff;
        transition: .36s;
    }
}

// ::v-deep .dialog-Photo {
//     .el-dialog {
//         .el-dialog__header {
//             .el-dialog__title {
//                 border-left: none;
//             }
//         }
//     }
// }

.mask1 {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    top: 60px;
    bottom: 0;
    right: 0;
    left: 230px;
    padding: 10px;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;

    .preview_img {
        position: relative;

        img {
            max-width: 1000px;
            object-fit: cover;
        }

        .img {
            max-width: 800px;
            height: 600px;
            overflow-y: auto;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}
</style>