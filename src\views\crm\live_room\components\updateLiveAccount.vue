<template>
<el-dialog title="关联人事账号" :visible.sync="show" width="600px">
    <div class="container" v-loading="loading">
		<el-form label-width="120px">
			<el-form-item label="选择人事账号">
				<admSelect v-model="params.admin_id" filterable :data="adminList" props="{value: id}" placeholder="请选择">
					<template #default="item">
						<span class="option-user_name">
							{{item.user_name}}
						</span>
						<span class="option-admin-phone">{{item.phone}}</span>
						<span class="option-dy_account_id" v-if="item.dy_account_id">
							已关联：{{item.name}} <small>{{item.ies_uniq_id}}</small>
						</span>
					</template>
				</admSelect>
			</el-form-item>
		</el-form>
	</div>
	<span slot="footer" class="dialog-footer">
		<el-button @click="cancle">取 消</el-button>
		<el-button type="primary" :loading="submiting" @click="confirm">确 定</el-button>
	</span>
</el-dialog>
</template>

<script>
export default {
	data() {
		return {
			show: false,        //dialog是否显示
            cancleFn: null,     //取消回调
            successFn: null,    //成功回调
			loading: false,
			submiting: false,
			params: {
				account_id: '',
				admin_id: '',
			},
			adminList: []
		}
	},
	mounted(){
		
	},
	methods: {
		//打开弹层
		open(account){
			this.getAdminList();
            this.params.account_id = account.id;
			this.params.admin_id = account.admin_id || '';
            this.show = true;
            return this;
        },
		async getAdminList(){
			this.loading = true;
			const res = await this.$http.getDouyinLiveAccountAdminList().catch(()=>{});
			this.loading = false;
			if(res.status == 200){
				this.adminList = (res.data || []).map(e => {
					return {...e, label: e.user_name+e.phone}
				});
			}
		},
        //注册取消方法
        onCancle(fn){
            fn && (this.cancleFn = fn);
            return this;
        },
        //注册成功方法
        onSuccess(fn){
            fn && (this.successFn = fn);
            return this;
        },

        //取消
        cancle(){
            this.show = false;
            this.cancleFn && this.cancleFn();
        },
        //确定
        async confirm(){
            this.submiting = true;
            const res = await this.$http.updateDouyinLiveAccount(this.params).catch(()=>{});
			this.submiting = false;
			if(res.status == 200){
				this.$message.success(res.data?.msg || '关联成功');
				this.show = false;
            	this.successFn && this.successFn();
			}
        }

	}
}
</script>
<style  scoped lang="scss">
.container{
	padding: 16px 16px;
}
.option-user_name{
	display: inline-block;
	min-width: 100px;
}
.option-admin-phone,.option-dy_account_id{
	color: #a9a9a9;
	margin-left: 12px;
	font-size: 12px;
}

.option-admin-phone{
	display: inline-block;
	width: 90px;
}
.option-dy_account_id{
	color: #f60;
}

</style>