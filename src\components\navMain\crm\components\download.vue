<template>
  <div class="download div row">
    <div class="down-left">
      <p class="down-title">{{ content.title }}</p>
      <p class="down-tips">{{ content.tips }}</p>
    </div>
    <div class="down-right" @click="onClick">
      <img :src="
                content.img ||
                  'https://img.tfcs.cn/backup/static/admin/customer/download.png'
              " alt="" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: Object,
      default() {
        return {
          title: "下载组件标题",
          img: "",
          tips: "下载组件底部对于下载的描述",
        };
      },
    },
  },
  methods: {
    onClick() {
      console.log("点击下载");
      this.$emit("onClick");
    },
  },
};
</script>

<style scoped lang="scss">
.download {
  border-radius: 4px;
  border: 1px solid #dde1e9;
  padding: 24px;
  max-width: 500px;
  background: #fff;
  align-items: center;
  justify-content: space-between;

  .down-left {
    .down-title {
      font-size: 16px;
      color: #2e3c4e;
    }

    .down-tips {
      margin-top: 9px;
      font-size: 14px;
      color: #8a929f;
    }
  }

  .down-right {}
}
</style>
