<template>
  <div>
    <el-button
      style="margin-bottom:20px"
      type="primary"
      size="mini"
      class="el-icon-plus"
      @click="createLabel"
      >添加标签组</el-button
    >
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      highlight-current-row
    >
      <!-- <el-table-column
        prop="label_group_id"
        label="ID"
        width="80"
      ></el-table-column> -->
      <el-table-column prop="name" label="标签组"></el-table-column>
      <el-table-column label="标签">
        <template slot-scope="scope">
          <el-input
            class="input-new-tag"
            v-if="scope.row.inputVisible"
            v-model="scope.row.inputValue"
            :ref="'saveTagInput' + scope.$index"
            size="mini"
            @keyup.enter.native="handleInputConfirm(scope.row, scope.$index)"
            @blur="handleInputConfirm(scope.row, scope.$index)"
          >
          </el-input>
          <el-button
            v-else
            style="font-size: 14px;"
            type="primary"
            plain
            size="mini"
            @click="showInput(scope.$index)"
            >+ 添 加
          </el-button>
          <el-tag
            type="primary"
            v-for="item in scope.row.label"
            :key="item.id"
            size="medium"
            style="margin-left: 4px;margin-top:4px"
            closable
            @close="onDeleteLabel(item)"
            >{{ item.name }}</el-tag
          >
        </template>
      </el-table-column>
      <!-- <el-table-column v-if="website_id==109||website_id==176" prop="is_force" label="是否强制使用"
      v-slot="{ row }">
          <el-tag :type="row.is_force==0?'success':'danger'"> {{row.is_force==0?"不强制":"强制"}}</el-tag>
      </el-table-column> -->
      <el-table-column width="100" prop="order" label="排序"></el-table-column>
      <!-- <el-table-column prop="created_at" label="添加时间"></el-table-column> -->
      <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <el-link type="primary" @click="onChangeEdit(scope.row)">编辑</el-link
          ><el-link
            type="danger"
            style="margin-left:20px"
            @click="onDelete(scope.row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align:end;margin-top:24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form :model="form_labels" label-position="left" label-width="100px">
        <el-form-item label="标签组名称：">
          <el-input
            style="width:300px"
            v-model="form_labels.name"
            placeholder="请输入"
          ></el-input>
          <span style="margin: 20px;">
            标记后成员不可修改和删除
              <el-radio v-model="form_labels.is_force" label="1">是</el-radio>
              <el-radio v-model="form_labels.is_force" label="0">否</el-radio>
            </span>
        </el-form-item>
        <el-form-item label="排序：">
          <el-input
            style="width:300px"
            v-model="form_labels.order"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <div class="ove">
          <el-form-item
            v-for="(domain, index) in form_labels.tags"
            label="标签"
            :key="domain.key"
            :prop="'tags.' + index + '.name'"
            :rules="[
              { required: true, message: '标签名称不能为空', trigger: 'blur' },
              { max: 8, message: '标签名称不能超过8个字符', trigger: 'blur' }
            ]"
          >
            <el-input
              style="width:300px"
              placeholder="请输入"
              v-model="domain.name"
            ></el-input>
            <span style="margin: 20px;">
              标记后成员不可修改和删除
              <el-radio v-model="domain.is_force" label="1">是</el-radio>
              <el-radio v-model="domain.is_force" label="0">否</el-radio>
            </span>
            <span
              v-if="index"
              class="el-icon-close"
              style="font-size: 24px; color: #e3e3e3; margin-left: 10px"
              @click.prevent="removeDomain(domain)"
            ></span>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button
            @click="addDomain"
            class="el-icon-plus"
            type="primary"
            size="small"
            >添加新标签</el-button
          >
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCreate = false">取 消</el-button>
        <el-button
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          type="primary"
          @click="submitData"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_labels: {},
      is_button_loading: false,
      website_id:"",//站点信息
    };
  },
  mounted() {
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id;
    }
    this.getDataList();
  },
  methods: {
    /* eslint-disable */
    getDataList() {
      this.is_table_loading = true;
      this.$http.getLabelsGroupData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data.map((item) => {
            item.inputVisible = false;
            item.inputValue = "";
            return item;
          });
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    createLabel() {
      this.form_labels = {
        order: 1,
        name: "",
        is_force:"0",
        tags: [
          {
            name: "",
            key: Date.now(),
            is_force:"0",
          },
        ],
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    onChangeEdit(row) {
      row.is_force = String(row.is_force);
      this.form_labels = {
        id: row.label_group_id,
        is_force:row.is_force,
        name: row.name,
        order: row.order,
        tags: row.label,
      };
      this.form_labels.tags.forEach(tag => {
        // 将is_force字段的值转换为字符串
        tag.is_force = String(tag.is_force);
      });
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
    },
    addDomain() {
      this.form_labels.tags.push({
        name: "",
        key: Date.now(),
        is_force:"0",
      });
    },
    removeDomain(item) {
      var index = this.form_labels.tags.indexOf(item);
      if (index !== -1) {
        this.form_labels.tags.splice(index, 1);
      }
    },
    submitData() {
      console.log(this.form_labels.name.length);
      if (!this.form_labels.name || this.form_labels.name.length > 8) {
        this.$message.error("请输入不超过8个字符的标题");
        return;
      }
      if (typeof this.form_labels.is_force === 'string') {
          // 将字符串转换为数字，并重新赋值给is_force字段
         this.form_labels.is_force = parseInt(this.form_labels.is_force);
        }
      this.form_labels.tags.forEach(tag => {
        // 检查is_force字段是否是字符串类型
        if (typeof tag.is_force === 'string') {
          // 将字符串转换为数字，并重新赋值给is_force字段
          tag.is_force = parseInt(tag.is_force);
        }
      });
      this.is_button_loading = true;
      if (this.dialogTitle === "addData") {
        console.log(this.form_labels);
        this.$http.setLabelsGroupData(this.form_labels).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else {
        this.$http.editLabelsGroupData(this.form_labels).then((res) => {
          this.is_button_loading = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    onDelete(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http
            .deleteLabelsGroupData({ ids: row.label_group_id + "" })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.getDataList();
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onDeleteLabel(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http.deleteLabels({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    //添加tag
    showInput(index) {
      this.tableData[index].inputVisible = true;
      this.$nextTick(() => {
        this.$nextTick((_) => {
          this.$refs[`saveTagInput${index}`].$refs.input.focus();
        });
      });
    },
    //列表新增
    handleInputConfirm(item, index) {
      let inputValue = this.tableData[index].inputValue;
      let tag_names = inputValue;
      if (inputValue) {
        this.addCorpTag(item.id, tag_names);
      }
      this.tableData[index].inputVisible = false;
      this.tableData[index].inputValue = "";
    },
    addCorpTag(group_id, tag_names) {
      let para = {
        parentid: group_id + "",
        name: tag_names,
        order: 1,
      };
      this.$http.setLabelsList(para).then((res) => {
        if (res.status === 200) {
          this.getDataList();
        }
      });
    },
  },
};
</script>

<style></style>
