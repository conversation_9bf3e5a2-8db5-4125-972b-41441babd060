<template>
  <el-container>
    <el-header class="div row" style="justify-content:space-between">
      <div class="div row">
        <div class="title">账单列表</div>
        <div class="title_number">
          <div>
            当前页面共（<i>{{ tableData.length }}</i
            >）条数据
          </div>
        </div>
      </div>
      <div class="div row">
        <el-input
          clearable
          style="width:200px"
          placeholder="请输入账单编号"
          v-model="params.bill_sn"
          @change="onChangeBillSn"
        ></el-input>
        <el-input
          clearable
          style="width:200px"
          placeholder="请输入批次编号"
          v-model="params.batch_sn"
          @change="onChangeBatchSn"
        ></el-input>
        <el-cascader
          @change="changeTableCascader"
          :options="bill_search_options"
          clearable
        ></el-cascader>
        <el-select
          v-if="params.category"
          v-model="search_value"
          filterable
          remote
          clearable
          reserve-keyword
          :placeholder="placeholderVal"
          :remote-method="remoteMethod"
          :loading="loading_search"
          @change="changeSelect"
        >
          <el-option
            v-for="(item, index) in table_search_value_list"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </el-header>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    ></myTable>
    <div class="pagination-box">
      <myPagination
        :total="params.total"
        :currentPage="params.page"
        :pagesize="params.per_page"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></myPagination>
    </div>
    <el-dialog
      @close="form_create = {}"
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
    >
      <el-form :model="form_create" label-width="100px">
        <el-form-item label="金额">
          <el-input
            type="number"
            min="0"
            step="1"
            v-model="form_create.paid_amount"
            placeholder="请输入具体金额"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="form_create.paid_date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="onClickForm"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      @close="form_create_invoice = {}"
      title="开票记录"
      :visible.sync="dialogCreateInvoice"
    >
      <el-form :model="form_create_invoice" label-width="100px">
        <el-form-item label="金额">
          <el-input
            type="number"
            min="0"
            step="1"
            v-model="form_create_invoice.amount"
            placeholder="请输入具体金额"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="form_create_invoice.operation_date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="onClickInvoiceForm"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "bill_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      form_create: {},
      dialogCreate: false,
      titleMap: {
        earning: "应收金额",
        disburse: "应付金额",
      },
      dialogTitle: "",
      form_create_invoice: {},
      dialogCreateInvoice: false,
      //  0 应收  1 应付
      bill_search_options: [
        {
          value: 0,
          label: "应收",
          children: [
            { value: 1, label: "客户" },
            { value: 2, label: "项目公司" },
          ],
        },
        {
          value: 1,
          label: "应付",
          children: [
            { value: 3, label: "经纪人" },
            { value: 4, label: "销售公司" },
          ],
        },
      ],
      search_value: "",
      loading_search: false,
      placeholderVal: "请输入内容",
      table_cascader_value: "",
      table_search_value_list: [],
      object_category_list: [],
      table_header: [
        { prop: "bill_sn", label: "账单编号" },
        {
          label: "批次编号",
          render: (h, data) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goBatchList(data.row.batch_sn);
                }}
              >
                {data.row.batch_sn}
              </el-link>
            );
          },
        },
        {
          label: "账单类型",
          render: (h, data) => {
            return (
              <div>
                {data.row.category === 0 ? "应收" : "应付"} /{" "}
                {this.$computedValueType(
                  this.object_category_list,
                  data.row.object_category
                )}
              </div>
            );
          },
        },
        {
          label: "客户",
          render: (h, data) => {
            return (
              <div>
                {data.row.so_c_name || data.row.project_company_name}
                {data.row.so_c_phone ? (
                  <el-tag type="success" size="mini">
                    {data.row.so_c_phone}
                  </el-tag>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
        {
          label: "已开票金额/元",
          render: (h, data) => {
            return (
              <el-tag
                onClick={() => {
                  this.jumpInvoice(data.row);
                }}
                type={
                  data.row.make_invoice_amount === "0.00" ? "danger" : "success"
                }
              >
                {data.row.make_invoice_amount}
              </el-tag>
            );
          },
        },
        {
          label: "已付金额/元",
          render: (h, data) => {
            return (
              <el-tag
                onClick={() => {
                  this.jumpPayment(data.row);
                }}
                type={data.row.paid_amount === "0.00" ? "danger" : "success"}
              >
                {data.row.paid_amount}
              </el-tag>
            );
          },
        },
        {
          label: "应付金额/元",
          render: (h, data) => {
            return (
              <el-tag type={data.row.amount === "0.00" ? "danger" : "primary"}>
                {data.row.amount}
              </el-tag>
            );
          },
        },
        { prop: "payment_date", label: "账单日期" },
        { prop: "payment_days", label: "账单天数/天" },
        { prop: "project_company_name", label: "项目公司" },
        { prop: "sale_company_name", label: "销售公司" },
        {
          label: "经纪人",
          render: (h, data) => {
            return (
              <div>
                {data.row.bu_name ||
                  data.row.bu_nickname ||
                  data.row.bu_user_name}
                {
                  <el-tag size="mini" type="success">
                    {data.row.bu_phone}
                  </el-tag>
                }
              </div>
            );
          },
        },
        {
          label: "操作",
          width: "120",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {!data.row.batch_id ? (
                  <div>
                    {data.row.payment_status !== 1 ? (
                      <el-button
                        onClick={() => {
                          this.onClickPay(data.row);
                        }}
                        type={data.row.category === 1 ? "success" : "primary"}
                        size="mini"
                      >
                        {data.row.category === 1 ? "付款" : "收款"}
                      </el-button>
                    ) : (
                      ""
                    )}
                    <el-button
                      onClick={() => {
                        this.onClickInvoice(data.row);
                      }}
                      type="danger"
                      size="mini"
                    >
                      开票
                    </el-button>
                  </div>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    if (this.$route.query.batch_sn) {
      this.params.batch_sn = this.$route.query.batch_sn;
    }
    this.object_category_list = this.$getDictionary(
      "SALE_ORDER_BILL_OBJECT_CATEGORY"
    );
    this.getDataList();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当page变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      if (!this.params.category) {
        delete this.params.category;
      }
      if (!this.params.customer_phone) {
        delete this.params.customer_phone;
      }
      if (!this.params.project_company_id) {
        delete this.params.project_company_id;
      }
      if (!this.params.broker_user_id) {
        delete this.params.broker_user_id;
      }
      if (!this.params.sale_company_id) {
        delete this.params.sale_company_id;
      }
      if (!this.params.bill_sn) {
        delete this.params.bill_sn;
      }
      if (!this.params.batch_sn) {
        delete this.params.batch_sn;
      }
      if (!this.params.object_category) {
        delete this.params.object_category;
      }
      this.$http.getBillList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    onClickPay(row) {
      this.form_create.id = row.id;
      this.dialogCreate = true;
      // 根据列表数据  判断应收|| 应付
      row.category == 1
        ? (this.dialogTitle = "disburse")
        : (this.dialogTitle = "earning");
    },
    onClickForm() {
      if (this.dialogTitle === "disburse") {
        this.$http.createDisburseData(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getDataList();
            this.form_create = {};
            this.dialogCreate = false;
          }
        });
      } else if (this.dialogTitle === "earning") {
        this.$http.createEarningData(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getDataList();
            this.form_create = {};
            this.dialogCreate = false;
          }
        });
      }
    },
    jumpPayment(row) {
      this.$goPath(`/payment_records_list?bill_id=${row.id}`);
    },
    jumpInvoice(row) {
      this.$goPath(`/invoice_list?bill_id=${row.id}`);
    },
    onClickInvoice(row) {
      this.form_create_invoice.bill_id = row.id;
      this.dialogCreateInvoice = true;
    },
    onClickInvoiceForm() {
      this.$http.createInvoiceData(this.form_create_invoice).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.form_create_invoice = {};
          this.getDataList();
          this.dialogCreateInvoice = false;
        }
      });
    },
    changeTableCascader(e) {
      this.table_cascader_value = e.toString();

      this.table_search_value_list = [];
      this.params = {
        page: 1,
        category: e[0] + "",
        customer_phone: "",
        project_company_id: "",
        broker_user_id: "",
        sale_company_id: "",
        object_category: e[1] + "",
      };
      if (!this.table_cascader_value) {
        this.params.category = "";
        this.params.object_category = "";
        this.getDataList();
      }
      switch (this.table_cascader_value) {
        case "0,1":
          this.placeholderVal = "请输入客户联系方式";
          break;
        case "0,2":
          this.placeholderVal = "请输入项目公司名称";
          break;
        case "1,3":
          this.placeholderVal = "请输入经纪人联系方式";
          break;
        case "1,4":
          this.placeholderVal = "请输入销售公司名称";
          break;
      }
    },
    remoteMethod(e) {
      this.table_search_value_list = [];
      this.loading_search = true;
      switch (this.table_cascader_value) {
        case "0,1":
          var params = {
            customer_phone: e,
          };
          this.$http.getReportList({ params: params }).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.customer_name,
                  value: item.customer_phone,
                };
              });
            }
          });
          break;
        case "0,2":
          this.$http.searchCompanyCategory(e, 1).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
          });
          break;
        case "1,3":
          var params1 = {
            phone: e,
          };
          this.$http.getUserBroker({ params: params1 }).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.name || item.nickname || item.user_name,
                  value: item.id,
                };
              });
            }
          });
          break;
        case "1,4":
          this.$http.searchCompanyCategory(e, 2).then((res) => {
            if (res.status === 200) {
              this.loading_search = false;
              this.table_search_value_list = res.data.data.map((item) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
          });
          break;
      }
    },
    changeSelect(e) {
      switch (this.table_cascader_value) {
        case "0,1":
          this.params.customer_phone = e;
          break;
        case "0,2":
          this.params.project_company_id = e;
          break;
        case "1,3":
          this.params.broker_user_id = e;
          break;
        case "1,4":
          this.params.sale_company_id = e;
          break;
      }
      this.getDataList();
    },
    onChangeBillSn() {
      this.getDataList();
    },
    onChangeBatchSn() {
      this.getDataList();
    },
    goBatchList(link) {
      this.$goPath(`/batch_list?batch_sn=${link}`);
    },
  },
};
</script>

<style lang="scss">
.row {
  align-items: center;
  text-align: center;
  color: #999;
  .title {
    color: #333;
    font-weight: bold;
  }
  .title_number {
    margin-left: 10px;
  }
  i {
    text-align: center;
  }
}
.el-button {
  margin: 10px;
}
</style>
