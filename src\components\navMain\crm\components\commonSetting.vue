<template>
  <div>
    <el-form  v-if="is_show_crm" :model="form_info" label-width="240px">
      <!-- <el-form-item label="是否开启掉公策略：">
        <el-radio v-model="form_info.is_auto_recycle" :label="1">开启</el-radio>
        <el-radio v-model="form_info.is_auto_recycle" :label="0">关闭</el-radio>
        <el-tooltip
          class="item"
          effect="light"
          placement="right"
          style="margin-left: 145px"
        >
          <div slot="content" style="max-width: 300px">
            开启后系统将根据设定规则将线索掉入公海 
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <!-- <el-form-item label="无跟进逾期天数：" v-if="form_info.is_auto_recycle">
        <el-input
          placeholder="请输入天数"
          style="width: 300px"
          v-model="form_info.max_overdue_day"
          min="0"
          step="1"
          type="number"
        >
          <template slot="append">天</template>
        </el-input>
        <el-tooltip
          class="item"
          effect="light"
          placement="right"
          style="margin-left: 10px"
        >
          <div slot="content" style="max-width: 300px">
            超出逾期天数将自动掉公
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <!-- <el-form-item label="未拨打电话掉公时长：" v-if="form_info.is_auto_recycle">
        <el-input
          placeholder="请输入天数"
          style="width: 300px"
          v-model="form_info.max_no_tel_time"
          min="0"
          step="1"
          type="number"
        >
          <template slot="append">小时</template>
        </el-input>
        <el-tooltip
          class="item"
          effect="light"
          placement="right"
          style="display: inline-block; margin-left: 10px"
        >
          <div slot="content" style="max-width: 300px">
            超出未拨打电话时长的客户，执行自动掉公策略。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <!-- <el-form-item label="掉公执行范围：" v-if="form_info.is_auto_recycle">
        <el-select v-model="form_info.overdue_tracking_id" multiple placeholder="请选择" style="width: 300px;">
          <el-option
            v-for="item in overdue_tracking_name"
            :key="item.id"
            :label="item.title"
            :value="item.id">
          </el-option>
        </el-select>
        <el-tooltip
          class="item"
          effect="light"
          placement="right"
          style="margin-left: 10px"
        >
          <div slot="content" style="max-width: 300px">
            可多选，默认“有效客户”，不在此状态范围内的，不再执行自动掉公策略。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item label="手动认领客户成员范围：">
        <el-select ref="get_black_list" style="width: 300px" v-model="form_info.get_black_list" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('get_black_list')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            指定人员可认领公海客户
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="手动认领客户部门范围：">
        <el-cascader style="width: 300px" :options="AllDepartment" :props="{
          value: 'id',
          label: 'name',
          children: 'subs',
          multiple: true,
          checkStrictly: true,
          emitPath: false
        }" collapse-tags v-model="form_info.get_black_department"></el-cascader>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            指定部门范围成员可认领公海客户
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="成员每日手动认领数量: ">
        <template>
        <el-radio v-model="Maximumclaim" label="0">不限制</el-radio>
        <el-radio v-model="Maximumclaim" label="1">限制领取</el-radio>
        <el-input placeholder="请输入" v-if="Maximumclaim>0" style="width: 94px;margin-left:-24px" v-model="form_info.max_get_num" min="0" step="1"
          type="number"></el-input>
          <span v-if="Maximumclaim>0" class="tail">次</span> 
        </template>
        <!-- <template v-else>
          <el-input placeholder="输入0则不限制领取次数" style="width: 300px" v-model="form_info.max_get_num" min="0" step="1"
          type="number"></el-input>
        </template> -->
        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">0为不限制</div>
          <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "></i>
        </el-tooltip>
        <el-link type="primary" style="margin-left:20px;" @click="numberphone('claim')"> 按成员设置</el-link>
      </el-form-item>
      <el-form-item label=" 成员客户私客总量：">
        <template> 
          <el-radio v-model="Maximumall" label="0">不限制</el-radio>
          <el-radio v-model="Maximumall" label="1">限制</el-radio>
          <el-input  v-if="Maximumall>0" placeholder="请输入" style="width: 94px;margin-left:4px" v-model="form_info.max_get_total" min="0" step="1"
          type="number"></el-input>
          <span v-if="Maximumall>0" class="tail">个</span> 
        </template>
        <!-- <template v-else>
          <el-input placeholder="输入0则不限制领取次数,超出上限系统将停止自动分配，不能从公海认领客户。" style="width: 300px" v-model="form_info.max_get_total" min="0" step="1"
          type="number"></el-input>
        </template> -->

        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">0为不限制,超出上限系统将停止自动分配，不能从公海认领客户，成交客户不计入客户总量</div>
          <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "></i>
        </el-tooltip>
        <el-link type="primary" style="margin-left:20px;" @click="numberphone('total')"> 按成员设置</el-link>
      </el-form-item>
      <el-form-item label="成员每日查看电话客户数量：">
        <template>
          <el-radio v-model="lookphone" label="0">不限制</el-radio>
          <el-radio v-model="lookphone" label="1">限制查看</el-radio>
          <el-input v-if="lookphone>0" placeholder="请输入" style="width:94px;margin-left:-24px" v-model="form_info.max_look_num" min="0" step="1"
          type="number"></el-input>
          <span v-if="lookphone>0" class="tail">个</span> 
        </template>   
        <!-- <template v-else>
          <el-input placeholder="输入0则不限制领取次数" style="width: 300px" v-model="form_info.max_look_num" min="0" step="1"
          type="number"></el-input>
        </template> -->
          <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">0为不限制</div>
          <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "></i>
        </el-tooltip>
        <el-link type="primary" style="margin-left:20px;" @click="numberphone('look')"> 按成员设置</el-link>
      </el-form-item>
      <el-form-item label="客源审批人：">
        <el-select ref="deal_approver_uid" style="width: 300px" v-model="form_info.deal_approver_uid" multiple
          placeholder="请选择" @focus="showPersonnelAuthority('deal_approver_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">审批人</div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="客户管理员：">
        <el-select ref="admin_list" style="width: 300px" v-model="form_info.admin_list" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('admin_list')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            范围内成员可以对客户进行操作
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="导出客户权限范围：" v-if="website_id == 176">
        <el-select ref="export_uid" style="width: 300px" v-model="form_info.export_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('export_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            范围内成员可以导出客户
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="经营视图导出豁免验证范围：">
        <el-select ref="export_not_sms_uid" style="width: 300px" v-model="form_info.export_not_sms_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('export_not_sms_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            选择成员，范围内的成员经营视图导出，无需弹起验证码验证，直接可导出
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <!-- <el-form-item label="成交分佣用户范围：">
        <mySelect
          :optionSource="user_list"
          v-model="form_info.deal_commission_uid"
          labelKey="user_name"
          valueKey="id"
          multiple
          width="300px"
          @page-change="onPageChange"
          :paginationOption="{
            pageSize: params.per_page, //每页显示条数
            currentPage: params.page, //当前页
            pagerCount: 5, //按钮数，超过时会折叠
            total: params.total, //总条数
          }"
        ></mySelect>
      </el-form-item> -->
      <!-- <el-form-item label="标无效审批权限范围：">
        <mySelect
          :optionSource="user_list"
          v-model="form_info.mark_inherit_approver_uid"
          labelKey="user_name"
          valueKey="id"
          multiple
          width="300px"
          style="display: inline-block;"
          @page-change="onPageChange"
          :paginationOption="{
            pageSize: params.per_page, //每页显示条数
            currentPage: params.page, //当前页
            pagerCount: 5, //按钮数，超过时会折叠
            total: params.total, //总条数
          }"
        ></mySelect>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block;margin-left:10px">
            <div slot="content" style="max-width: 300px">
              审批人
            </div>
            <i class="el-icon-info" style="color:#F56C6C;font-size: 20px;"> </i>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item label="客户状态审批范围：">
        <el-select v-model="form_info.up_client_state_list" multiple placeholder="请选择" style="width: 300px">
          <el-option v-for="item in overdue_tracking_name" :key="item.id" :label="item.title" :value="item.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            默认我司成交、他司成交,不在范围内的类别,直接生效。在范围内的类别,需要提交审批给审批人。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="批量导入客户权限范围：">
        <el-select ref="batch_import_uid" style="width: 300px" v-model="form_info.batch_import_uid" multiple
          placeholder="不填写则全员可以导入" @focus="showPersonnelAuthority('batch_import_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            默认全员可见，建议指定范围
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <!-- <el-form-item label="CRM设置权限范围：">
        <mySelect
          :optionSource="user_list"
          style="display: inline-block"
          v-model="form_info.config_approver_uid"
          labelKey="user_name"
          valueKey="id"
          multiple
          width="300px"
          @page-change="onPageChange"
          :paginationOption="{
            pageSize: params.per_page, //每页显示条数
            currentPage: params.page, //当前页
            pagerCount: 5, //按钮数，超过时会折叠
            total: params.total, //总条数
          }"
        ></mySelect>
        <el-tooltip
          class="item"
          effect="light"
          placement="right"
          style="display: inline-block; margin-left: 10px"
        >
          <div slot="content" style="max-width: 300px">
            范围内平台配置入口可见
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item label="公海新增客户通知范围：">
        <el-select ref="add_notice_uid" style="width: 300px" v-model="form_info.add_notice_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('add_notice_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            该范围内设置成员可以收到公海新增客户线索提醒
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <!-- <el-form-item label="报备渠道审核专员：" v-if="website_id==626">
        <el-select ref="report_uid" style="width: 300px" v-model="form_info.report_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('report_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            进入报备记录列表，可上传报备回执
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="报备记录是否开启：" v-if="website_id==626">
        <el-radio v-model="form_info.is_open_report" :label="1">开启</el-radio>
        <el-radio v-model="form_info.is_open_report" :label="0">关闭</el-radio>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 147px">
          <div slot="content" style="max-width: 300px">
            开启后将显示报备客户入口
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item label="客户详情备注功能是否开启：">
        <el-radio v-model="form_info.is_open_remark" :label="1">开启</el-radio>
        <el-radio v-model="form_info.is_open_remark" :label="0">关闭 </el-radio>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 147px">
          <div slot="content" style="max-width: 300px">
            开启后将显示客户详情备注功能
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="公海新增客户通知部门范围：">
        <el-cascader style="width: 300px" :options="AllDepartment" :props="{
          value: 'id',
          label: 'name',
          children: 'subs',
          multiple: true,
          checkStrictly: true,
          emitPath: false
        }" collapse-tags v-model="form_info.add_notice_department"></el-cascader>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            该设置范围内的部门成员能够收到公海新增客户线索提醒
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="公海仅显示本部门客户的部门范围：">
        <el-cascader
            style="width: 300px;"
            v-model="form_info.department_show"
            clearable
            :show-all-levels="false"
            placeholder="请选择部门"
            :options="AllDepartmentA"
            :props="{
                value: 'id',
                label: 'name',
                children: 'subs',
                emitPath: false,
                multiple: true,
                checkStrictly: true,
            }"
            >
          </el-cascader>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            设置范围内的部门成员在公海列表只能看到本部门的客户。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="客户手机号双向核验：" v-if="website_id==109||website_id==176">
        <el-radio v-model="form_info.only_auth" :label="1">开启</el-radio>
        <el-radio v-model="form_info.only_auth" :label="0">关闭</el-radio>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 147px">
          <div slot="content" style="max-width: 300px">
            是否开启客源（CRM）手机号与报备系统手机号唯一性核验，开启后如果CRM中手机号已存在，经纪人将不能重复报备。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="公海客户列表私客是否可见：">
        <el-select style="width: 300px" v-model="form_info.is_get_show" placeholder="请选择">
          <el-option v-for="item in cusShowType" :key="item.values" :label="item.label" :value="item.values">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">默认全员可见。</div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="公海列表掉公/转公客户是否可见：">
        <el-select style="width: 300px" v-model="form_info.is_potential_show" placeholder="请选择">
          <el-option v-for="item in cusShowType" :key="item.values" :label="item.label" :value="item.values">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            默认仅创始人 / 客户管理员可见。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="私客资料页浏览权限：">
        <el-select style="width: 300px" v-model="form_info.see_view" placeholder="请选择">
          <el-option v-for="item in Claimeddata" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            全员可见：所有成员都可浏览；<br>
            仅创始人/维护人可见： 创始人、维护人可浏览。<br>
            角色成员范围：包含录入人、维护人、共享维护人、带看人、成交人可浏览或指定成员、部门范围。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="私客资料页浏览成员范围：" v-if="form_info.see_view==4">
        <el-select ref="private_uid" style="width: 300px" v-model="form_info.private_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('private_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            <!-- 指定人员可认领公海客户 -->
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="私客资料页浏览部门范围：" v-if="form_info.see_view==4">
        <el-cascader style="width: 300px" :options="AllDepartment" :props="{
          value: 'id',
          label: 'name',
          children: 'subs',
          multiple: true,
          checkStrictly: true,
          emitPath: false
        }" collapse-tags v-model="form_info.private_department"></el-cascader>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            <!-- 指定部门范围成员可认领公海客户 -->
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="私客是否隐藏历史跟进内容：">
        <el-radio v-model="form_info.is_genjin_status" :label="1">开启</el-radio>
        <el-radio v-model="form_info.is_genjin_status" :label="0">关闭</el-radio>
        <!-- <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 147px">
          <div slot="content" style="max-width: 300px">
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>  
        </el-tooltip> -->
      </el-form-item>
      <el-form-item label="线索记录显示范围：">
        <el-select style="width: 300px" v-model="form_info.behavior_show_range" placeholder="请选择">
          <el-option v-for="item in cluerange" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            <!-- 客户详情页的浏览权限。 -->
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="是否开启全员跟进：">
        <el-radio-group v-model="form_info.all_follow_status">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 175px">
          <div slot="content" style="max-width: 300px">
            开启后全员可跟进客户信息。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="是否开启删除客户：">
        <el-radio-group v-model="form_info.is_del">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 175px">
          <div slot="content" style="max-width: 300px">
            开启后可将客户信息从系统中删除。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="删除客户无需审核权限范围：" v-if="form_info.is_del == 1">
        <el-select ref="del_client_list" style="width: 300px" v-model="form_info.del_client_list" multiple
          placeholder="请选择" @focus="showPersonnelAuthority('del_client_list')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            范围内成员删除客户无需审核。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <!-- <el-form-item label="是否开启查看电话强制跟进:">
        <el-radio-group v-model="form_info.is_see_tel_follow">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 175px">
          <div slot="content" style="max-width: 300px">
            查看电话以后未添加跟进将提醒跟进（创始人和客户管理员除外）
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item label="是否显示客户所在城市:">
        <el-radio-group v-model="form_info.is_show_city">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 172px">
          <div slot="content" style="max-width: 300px">
            开启后录入客户/维护客户/客户详情  页面将显示所在城市可选项。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="成员自主控制分客接单开关:">
        <el-radio-group v-model="form_info.is_auto_allocation">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 172px">
          <div slot="content" style="max-width: 300px">
            开启后成员可以自主在开关设置是否接收分客。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="转交给同事是否开启:">
        <el-radio-group v-model="form_info.is_zr">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 172px">
          <div slot="content" style="max-width: 300px">
                 是否允许成员将私客转交给同事 <br>
                 创始人及客户管理员权限不受限制。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="转交权限成员范围:" v-if="form_info.is_zr==1">
        <el-radio-group v-model="form_info.zr_type">
          <el-radio :label="1">全员</el-radio>
          <el-radio :label="2">按需选择</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 142px">
          <div slot="content" style="max-width: 300px">
            范围内成员，可操作转交给同事【私客】
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="转交成员范围:" v-if="form_info.zr_type==2&&form_info.is_zr==1">
        <el-select ref="zr_user_range" style="width: 300px" v-model="form_info.zr_user_range" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('zr_user_range')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            <!-- 设置范围内的成员可以接收新增转公/掉公潜在客户的消息提醒。 -->
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="转交部门范围：" v-if="form_info.zr_type==2&&form_info.is_zr==1">
        <el-cascader
            style="width: 300px;"
            v-model="form_info.zr_department_range"
            clearable
            :show-all-levels="false"
            placeholder="请选择部门"
            :options="AllDepartmentA"
            :props="{
                value: 'id',
                label: 'name',
                children: 'subs',
                emitPath: false,
                multiple: true,
                checkStrictly: true,
            }"
            >
          </el-cascader>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 10px">
          <div slot="content" style="max-width: 300px">
            <!-- 设置范围内的部门成员在公海列表只能看到本部门的客户。 -->
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="潜在客户成员权限范围:">
        <el-select ref="add_potential_notice_uid" style="width: 300px" v-model="form_info.add_potential_notice_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('add_potential_notice_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            设置范围内的成员可以接收新增转公/掉公潜在客户的消息提醒。<br>
            成员可进入潜在客户，查看号码并跟进
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="潜在客户跟进权限范围:">
        <el-select ref="potential_client_operation_uid" style="width: 300px" v-model="form_info.potential_client_operation_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('potential_client_operation_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            设置范围内的成员，潜在客户不需要认领可以直接查看拨打号码、跟进、修改资料。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="潜在客户仅显示本部门范围:">
        <el-radio-group v-model="form_info.potential_is_show">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 172px">
          <div slot="content" style="max-width: 300px">
            开启后潜在客户列表仅显示同部门成员掉公/转公的客户。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="查看电话权限角色范围:">
        <el-select style="width: 300px" v-model="see_tel_data" multiple  placeholder="请选择" @change="removeTag">
          <el-option v-for="item in lookRoledata" :key="item.id" :label="item.name" :value="item.id" :disabled="item.disabled">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            系统默认维护人/创始人/客户管理 有权限查看客户电话，为便于团队协作跟进客户，如增加对应角色，单个客户关联的角色成员均有权限查看电话。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>

      </el-form-item>
      <el-form-item label="智慧经营数据可见范围:">
        <el-select ref="management_see_list" style="width: 300px" v-model="form_info.management_see_list" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('management_see_list')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            范围内的成员可查看全员数据；不在范围内的和原来一样只显示自己的数据。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="列表分页数自定义:">
        <el-select style="width: 300px" v-model="form_info.list_limit" placeholder="请选择">
          <el-option v-for="item in Pagecustomization" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <!-- <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            范围内的成员可查看全员数据；不在范围内的和原来一样只显示自己的数据。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip> -->
      </el-form-item>
      <el-form-item label="工作台数据可见范围:">
        <el-select style="width: 300px" v-model="form_info.work_show" placeholder="请选择">
          <el-option v-for="item in stagingdata" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批量加入流转权限范围:">
        <el-select ref="auto_work_assign" style="width: 300px" v-model="form_info.auto_work_assign" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('auto_work_assign')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="直播获客可进入成员范围:">
        <el-select ref="dy_living_room_uid" style="width: 300px" v-model="form_info.dy_living_room_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('dy_living_room_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批量自动分配权限成员范围 :">
        <el-select ref="auto_allocation_uid" style="width: 300px" v-model="form_info.auto_allocation_uid" multiple placeholder="请选择"
          @focus="showPersonnelAuthority('auto_allocation_uid')" @change="PersonnelChange">
          <el-option :disabled="true" v-for="list in datalist" :key="list.id" :label="list.user_name" :value="list.id">
          </el-option>
        </el-select>
        <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 8px">
          <div slot="content" style="max-width: 300px">
            设置成员将有权限操作批量自动分配。
          </div>
          <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="允许成员删除流转客权限:">
        <el-radio-group v-model="form_info.is_lz_del">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="带看跟进上传图片是否必填:">
        <el-radio-group v-model="form_info.is_take_images">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="手动录入客户规则:">
        <el-radio-group v-model="form_info.add_client_repeat">
          <div style="margin-top: 13px"><el-radio :label="0">去重，公海私客号码不能重复录入</el-radio></div>
          <div style="margin-top: 10px"><el-radio :label="1">不去重，重复号码录入至成员流转客</el-radio></div>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="移动端客户列表布局:">
        <el-radio-group v-model="form_info.wap_console_layout">
          <el-radio :label="1">卡片版</el-radio>
          <el-radio :label="2">简洁版</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="移动端客户列表头像风格:">
        <el-radio-group v-model="form_info.list_headimage_style">
          <el-radio :label="1">简洁首字</el-radio>
          <el-radio :label="2">图片头像</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- <el-form-item label="强制跟进有效期：" v-if="form_info.is_see_tel_follow==1">
        <el-radio-group v-model="Mandatory">
          <el-radio :label="0">永不失效</el-radio>
          <el-radio :label="1">按时间</el-radio>
        </el-radio-group>
      </el-form-item> -->
<!-- 
      <el-form-item label="强制跟进有效期：" v-if="Mandatory==1&&form_info.is_see_tel_follow==1">
        <el-input placeholder="输入强制跟进有效期" style="width: 300px" v-model="form_info.follow_validity_time" min="1" step="1"
          type="number">
          <template slot="append"> 分钟 </template>
        </el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="onClickForm">确认</el-button>
      </el-form-item>
    </el-form>
    <myEmpty v-else desc="当前用户不可查看"></myEmpty>
    <el-dialog :visible.sync="show_add_member" width="400px" :title="department_title" append-to-body>
      <div class="member" ref="memberList">
        <div v-if="identification == 'potential_client_operation_uid'"
        style="margin-bottom:20px"> <el-button  type="primary" size="small" @click="Select_All">全选</el-button></div>
        <multipleTree v-if="show_add_member" :list="serverData" :defaultValue="selectedIds" @onClickItem="selecetedMember"
          :defaultExpandAll="false">
        </multipleTree>
        <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
          <el-button type="text" @click="show_add_member = false">取消</el-button>
          <el-button type="primary" @click="selectMemberOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import mySelect from "./my_select";
import myEmpty from "@/components/components/my_empty.vue";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
export default {
  components: {
    // mySelect,
    myEmpty,
    multipleTree,
  },
  data() {
    return {
      Mandatory:"",
      form_info: {
        is_zr: 1,//转交给同事是否开启
        is_auto_recycle: 0, //是否逾期自动回收(1:开启,0:关闭)
        max_overdue_day: 10, //最大逾期天数
        overdue_tracking_id: "", //支持掉公的客户状态
        max_get_num: 2, //成员每天最大认领条数(0:不限制)
        max_look_num: 10, //成员每天最大查看电话数量(0:不限制)
        deal_approver_uid: "", //成交审批用户范围
        admin_list: "", // 客户管理员
        deal_commission_uid: "", //成交分佣用户范围(默认管理员)
        mark_inherit_approver_uid: "", //标记无效审批用户id
        batch_import_uid: "", //批量导入客户权限范围(为空全员可导入操作)
        // config_approver_uid: "", //CRM设置权限范围(默认管理员)
        max_get_total: 30, //成员最大认领条数(0不限制)
        get_black_list: "", //领取客户黑名单
        private_uid:"",//
        add_notice_uid: "", //新增客户通知范围
        report_uid:"",//报备渠道审核专员
        only_auth: 0, // 客户手机号双向核验
        is_open_report:0,//报备记录是否开启
        is_open_remark:0,//客户详情备注功能是否开启
        is_get_show: 0, // 公海已领取客户是否可见(1:全员可见2: 仅创始人可见 3:全员不可见)
        is_potential_show: 2, // 公海掉公转公是否可见(1:全员可见2: 仅创始人可见 3:全员不可见)
        see_view: 2, // 已认领客户资料(1:全员可见2:仅创始人/管理员可见 3:仅维护人可见)
        // behavior_show_range:0,//线索记录显示范围(0显示全局线索记录,1仅显示录入人线索记录)
        is_del: 0, // 是否可以删除(0:关闭,1:开启)
        all_follow_status: 0, // 客户管理员
        del_client_list: "", // 删除客户无需审核权限范围
        up_client_state_list: [35, 36], // 更改客户状态无需审核范围
        max_no_tel_time: "", // 未拨打电话掉公时长
        is_see_tel_follow: 0,   //是否开启查看电话强制跟进
        follow_validity_time: 10,  //强制跟进有效期：
        get_black_department: "",//  认领客户部门范围
        private_department:"",
        add_notice_department: "", //新增客户通知部门范围
        department_show:"",//公海客户显示范围
        zr_department_range:"",//转交部门范围
        work_show:"",
        see_tel_role:"",
      },
      see_tel_data:[4],
      params: {
        page: 1,
        per_page: 100,
        type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
      },
      user_list: [],
      is_show_crm: false,
      show_add_member: false, // 部门成员模态框
      selectedIds: [], // 人事权限范围 默认勾选和展开的节点的 key 的数组
      identification: "",
      department_title: "",
      datalist: [], // 全部部门成员列表
      serverData: [], // 部门成员数据
      overdue_tracking_name: [], // 掉公范围状态
      website_id: '',
      // 公海已认领客户可见状态
      cusShowType: [
        {
          label: "全员可见",
          values: 1
        },
        {
          label: "仅创始人 / 客户管理员可见",
          values: 2
        },
        {
          label: "全员不可见",
          values: 3
        },
      ],
      Claimeddata:[
        { id:1,name:"全员可见" },
        { id:2,name:"仅创始人/维护人可见" },
        // { id:3,name:"仅维护人可见" },
        { id:4,name:"角色成员可见/成员范围可见" },
       
      ],
      cluerange:[
        { id:0,name:"显示全局线索记录" },
        { id:1,name:"仅显示录入人线索记录" },
      ],
      Pagecustomization:[
        {id:10,name:"10条/页"},
        {id:20,name:"20条/页"},
        {id:30,name:"30条/页"},
        // {id:50,name:"50条/页"},
        // {id:100,name:"100条/页"}
      ],
      stagingdata:[
        {id:1,name:"全员可见"},
        {id:2,name:"仅创始人 / 客户管理员可见"}
      ],
      lookRoledata:[
        {id:1,name:"录入人"},
        {id:2,name:"带看人"},
        {id:3,name:"成交人"},
        {id:4,name:"维护人", disabled: true}
      ],
      checkList: ['维护人'],
      AllDepartment: [],
      AllDepartmentA: [],
      valuelist: "",
      Maximumclaim:"0",//最大领取次数
      Maximumall:"0",//成员客户总量上限
      lookphone:"0",//成员每天最大查看电话数量
    };
  },
  mounted() {
    this.website_id = this.$route.query.website_id
    // if(this.website_id==109||this.website_id ==176){
    //   this.Claimeddata.splice(2, 1,  { id:4,name:"角色范围" },);
    // }
    // this.getadminUser();
    this.getSiteCrmSetting();
    this.getMemberList();
    this.getCustomerStatus();
    this.getDepartmentList()
  },
  methods: {
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          if (res.data.roles && res.data.roles[0].name === "站长") {
            this.is_show_crm = true;
            this.getSetting();
            this.getManagerAuthList();
          } else {
            this.getSiteCrmSetting(res.data.id);
          }
        }
      });
    },
    // 获取批量导入的crm站点设置
    getSiteCrmSetting() {
      this.is_show_crm = true;
      // this.getSetting();
      this.getManagerAuthList();
      // this.$http.getAuthShow("config_approver_uid").then((res) => {
      //   if (res.status === 200) {
      //     if (res.data.indexOf(id) != -1) {
      //       this.is_show_crm = true;

      //     }
      //   }
      // });
    },
    onPageChange(e) {
      this.params.page = e;
      this.getManagerAuthList();
    },
    getManagerAuthList() {
      this.$http.getManagerAuthList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.user_list = res.data.data;
        }
      });
    },
    getSetting() {
      this.$http.getSiteCrmSetting().then((res) => {
        if (res.status === 200) {
          this.form_info = res.data;
          if(this.form_info.max_get_num>0){
            this.Maximumclaim = "1"
          }else if (this.form_info.max_get_num==0){
            this.Maximumclaim = "0"
          }
          if(this.form_info.max_get_total>0){
            this.Maximumall = "1"
          }else if (this.form_info.max_get_total==0){
            this.Maximumall = "0"
          }
          if(this.form_info.max_look_num>0){
            this.lookphone = "1"
          }else if (this.form_info.max_look_num==0){
            this.lookphone = "0"
          }
          if(this.form_info.see_tel_role){
            this.see_tel_data = this.form_info.see_tel_role.split(",").map((item) => {
            return parseFloat(item);
          });
          }
          this.form_info.deal_approver_uid = res.data.deal_approver_uid
            ? this.setArr(res.data.deal_approver_uid)
            : "";
          this.form_info.admin_list = res.data.admin_list
            ? this.setArr(res.data.admin_list)
            : "";
          this.form_info.get_black_department = res.data.get_black_department.split(",")
          this.form_info.private_department = res.data.private_department.split(",")
          this.form_info.add_notice_department = res.data.add_notice_department.split(",")
          this.form_info.department_show = res.data.department_show.split(",")
          this.form_info.zr_department_range = res.data.zr_department_range.split(",")
          this.form_info.deal_commission_uid = res.data.deal_commission_uid
            ? this.setArr(res.data.deal_commission_uid)
            : "";
          this.form_info.mark_inherit_approver_uid = res.data
            .mark_inherit_approver_uid
            ? this.setArr(res.data.mark_inherit_approver_uid)
            : "";
          this.form_info.batch_import_uid = res.data.batch_import_uid
            ? this.setArr(res.data.batch_import_uid)
            : "";
          this.form_info.export_uid = res.data.export_uid
            ? this.setArr(res.data.export_uid)
            : "";
            this.form_info.export_not_sms_uid = res.data.export_not_sms_uid
            ? this.setArr(res.data.export_not_sms_uid)
            : "";
          this.form_info.add_potential_notice_uid = res.data.add_potential_notice_uid
            ? this.setArr(res.data.add_potential_notice_uid)
            : "";
          this.form_info.zr_user_range = res.data.zr_user_range
            ? this.setArr(res.data.zr_user_range)
            : "";
          this.form_info.potential_client_operation_uid = res.data.potential_client_operation_uid
            ? this.setArr(res.data.potential_client_operation_uid)
            : "";
          this.form_info.management_see_list = res.data.management_see_list
            ? this.setArr(res.data.management_see_list)
            : "";
          this.form_info.auto_work_assign = res.data.auto_work_assign
            ? this.setArr(res.data.auto_work_assign)
            : ""; 
          this.form_info.dy_living_room_uid = res.data.dy_living_room_uid
            ? this.setArr(res.data.dy_living_room_uid)
            : "";
          this.form_info.auto_allocation_uid = res.data.auto_allocation_uid
            ? this.setArr(res.data.auto_allocation_uid)
            : "";
          this.form_info.config_approver_uid = res.data.config_approver_uid
            ? this.setArr(res.data.config_approver_uid)
            : "";
          this.form_info.get_black_list = res.data.get_black_list
            ? this.setArr(res.data.get_black_list)
            : "";
          this.form_info.private_uid = res.data.private_uid
            ? this.setArr(res.data.private_uid)
            : "";
          this.form_info.add_notice_uid = res.data.add_notice_uid
            ? this.setArr(res.data.add_notice_uid)
            : "";
          this.form_info.report_uid = res.data.report_uid
            ? this.setArr(res.data.report_uid)
            : "";
          this.form_info.del_client_list = res.data.del_client_list
            ? this.setArr(res.data.del_client_list)
            : "";
          this.form_info.overdue_tracking_id = res.data.overdue_tracking_id
            ? this.setArrMini(res.data.overdue_tracking_id)
            : "";
          this.form_info.up_client_state_list = res.data.up_client_state_list
            ? this.setArrMini(res.data.up_client_state_list)
            : "";
          if(this.form_info.follow_validity_time==0){
            this.Mandatory = 0
          }else{
            this.Mandatory = 1
          }
        }
      });
    },
    // 处理简单数据
    setArrMini(arr) {
      let n_arr = arr.split(",");
      let n_arr_2 = n_arr.map((item) => {
        return parseInt(item);
      });
      return n_arr_2;
    },
    // 处理部门成员数据
    setArr(arr) {
      let n_arr = arr.split(",");
      let n_arr_2 = n_arr.map((item) => {
        return parseInt(item);
      });
      // ====
      let i = 0;
      if (n_arr_2 != [] && n_arr_2 != undefined) {
        n_arr_2.map((item) => {
          this.$nextTick(() => {
            this.datalist.map((list) => {
              if (item != list.id) {
                i++;
                if (i == this.datalist.length) {
                  n_arr_2.splice(n_arr_2.indexOf(item), 1);
                  // console.log(n_arr_2,"观察");
                }
              }
            })
            i = 0;
          })
        })
      }
      // ====
      return n_arr_2;
    },
    removeTag(val) {
      if (val.indexOf(4)==-1) {
        this.see_tel_data = val
        this.see_tel_data.splice(0, 0, 4);
        this.$message.warning("维护人不可被删除！")
      }
    },
    onClickForm() {
      localStorage.setItem('pagenum', JSON.stringify(this.form_info.list_limit)); // 更新一下每页条数
      let result = [];
      if (this.form_info.deal_approver_uid != [] && this.form_info.deal_approver_uid != '') {
        this.form_info.deal_approver_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.deal_approver_uid = Array.from(new Set(result));
      this.form_info.deal_approver_uid = this.form_info.deal_approver_uid
        ? this.form_info.deal_approver_uid.join(",")
        : "";

      result = [];
      if (this.form_info.export_uid != [] && this.form_info.export_uid != '') {
        this.form_info.export_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.export_uid = Array.from(new Set(result));
      this.form_info.export_uid = this.form_info.export_uid
        ? this.form_info.export_uid.join(",")
        : "";

        result = [];
      if (this.form_info.export_not_sms_uid != [] && this.form_info.export_not_sms_uid != '') {
        this.form_info.export_not_sms_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.export_not_sms_uid = Array.from(new Set(result));
      this.form_info.export_not_sms_uid = this.form_info.export_not_sms_uid
        ? this.form_info.export_not_sms_uid.join(",")
        : "";

      result = [];
      if (this.form_info.add_potential_notice_uid != [] && this.form_info.add_potential_notice_uid != '') {
        this.form_info.add_potential_notice_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.add_potential_notice_uid = Array.from(new Set(result));
      this.form_info.add_potential_notice_uid = this.form_info.add_potential_notice_uid
        ? this.form_info.add_potential_notice_uid.join(",")
        : "";

        result = [];
      if (this.form_info.zr_user_range != [] && this.form_info.zr_user_range != '') {
        this.form_info.zr_user_range.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.zr_user_range = Array.from(new Set(result));
      this.form_info.zr_user_range = this.form_info.zr_user_range
        ? this.form_info.zr_user_range.join(",")
        : "";

      result = [];
      if (this.form_info.potential_client_operation_uid != [] && this.form_info.potential_client_operation_uid != '') {
        this.form_info.potential_client_operation_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.potential_client_operation_uid = Array.from(new Set(result));
      this.form_info.potential_client_operation_uid = this.form_info.potential_client_operation_uid
        ? this.form_info.potential_client_operation_uid.join(",")
        : "";

        result = [];
      if (this.form_info.management_see_list != [] && this.form_info.management_see_list != '') {
        this.form_info.management_see_list.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.management_see_list = Array.from(new Set(result));
      this.form_info.management_see_list = this.form_info.management_see_list
        ? this.form_info.management_see_list.join(",")
        : "";

      result = [];
      if (this.form_info.auto_work_assign != [] && this.form_info.auto_work_assign != '') {
        this.form_info.auto_work_assign.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.auto_work_assign = Array.from(new Set(result));
      this.form_info.auto_work_assign = this.form_info.auto_work_assign
        ? this.form_info.auto_work_assign.join(",")
        : "";  

        result = [];
      if (this.form_info.dy_living_room_uid != [] && this.form_info.dy_living_room_uid != '') {
        this.form_info.dy_living_room_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.dy_living_room_uid = Array.from(new Set(result));
      this.form_info.dy_living_room_uid = this.form_info.dy_living_room_uid
        ? this.form_info.dy_living_room_uid.join(",")
        : "";  

        result = [];
      if (this.form_info.auto_allocation_uid != [] && this.form_info.auto_allocation_uid != '') {
        this.form_info.auto_allocation_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.auto_allocation_uid = Array.from(new Set(result));
      this.form_info.auto_allocation_uid = this.form_info.auto_allocation_uid
        ? this.form_info.auto_allocation_uid.join(",")
        : "";  


      result = [];
      if (this.form_info.admin_list != [] && this.form_info.admin_list != '') {
        this.form_info.admin_list.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.admin_list = Array.from(new Set(result));
      this.form_info.admin_list = this.form_info.admin_list
        ? this.form_info.admin_list.join(",")
        : "";
      this.form_info.overdue_tracking_id = this.form_info.overdue_tracking_id
        ? this.form_info.overdue_tracking_id.join(",")
        : "";
      this.form_info.up_client_state_list = this.form_info.up_client_state_list
        ? this.form_info.up_client_state_list.join(",")
        : "";
      this.form_info.deal_commission_uid = this.form_info.deal_commission_uid
        ? this.form_info.deal_commission_uid.join(",")
        : "";
      this.form_info.mark_inherit_approver_uid = this.form_info
        .mark_inherit_approver_uid
        ? this.form_info.mark_inherit_approver_uid.join(",")
        : "";
      result = [];
      if (this.form_info.batch_import_uid != [] && this.form_info.batch_import_uid != '') {
        this.form_info.batch_import_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.batch_import_uid = Array.from(new Set(result));
      this.form_info.batch_import_uid = this.form_info.batch_import_uid
        ? this.form_info.batch_import_uid.join(",")
        : "";
      // this.form_info.config_approver_uid = this.form_info.config_approver_uid
      //   ? this.form_info.config_approver_uid.join(",")
      //   : "";
      result = [];
      if (this.form_info.get_black_list != [] && this.form_info.get_black_list != '') {
        this.form_info.get_black_list.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.get_black_list = Array.from(new Set(result));
      this.form_info.get_black_list = this.form_info.get_black_list
        ? this.form_info.get_black_list.join(",")
        : "";

      result = [];
      if (this.form_info.private_uid != [] && this.form_info.private_uid != '') {
        this.form_info.private_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.private_uid = Array.from(new Set(result));
      this.form_info.private_uid = this.form_info.private_uid
        ? this.form_info.private_uid.join(",")
        : "";
      result = [];
      if (this.form_info.add_notice_uid != [] && this.form_info.add_notice_uid != '') {
        this.form_info.add_notice_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.add_notice_uid = Array.from(new Set(result));
      this.form_info.add_notice_uid = this.form_info.add_notice_uid
        ? this.form_info.add_notice_uid.join(",")
        : "";
      result = [];
      if (this.form_info.report_uid != [] && this.form_info.report_uid != '') {
        this.form_info.report_uid.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.report_uid = Array.from(new Set(result));
      this.form_info.report_uid = this.form_info.report_uid
        ? this.form_info.report_uid.join(",")
        : "";
      result = [];
      if (this.form_info.del_client_list != [] && this.form_info.del_client_list != '') {
        this.form_info.del_client_list.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.del_client_list = Array.from(new Set(result));
      this.form_info.del_client_list = this.form_info.del_client_list
        ? this.form_info.del_client_list.join(",")
        : "";
        result = [];
      if (this.form_info.get_black_department != [] && this.form_info.get_black_department != '') {
        this.form_info.get_black_department.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.get_black_department = Array.from(new Set(result));
      this.form_info.get_black_department = this.form_info.get_black_department
        ? this.form_info.get_black_department.join(",")
        : "";

      result = [];
      if (this.form_info.private_department != [] && this.form_info.private_department != '') {
        this.form_info.private_department.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.private_department = Array.from(new Set(result));
      this.form_info.private_department = this.form_info.private_department
        ? this.form_info.private_department.join(",")
        : "";

        result = [];
      if (this.form_info.add_notice_department != [] && this.form_info.add_notice_department != '') {
        this.form_info.add_notice_department.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.add_notice_department = Array.from(new Set(result));
      this.form_info.add_notice_department = this.form_info.add_notice_department
        ? this.form_info.add_notice_department.join(",")
        : "";
        
      result = [];
      if (this.form_info.department_show != [] && this.form_info.department_show != '') {
        this.form_info.department_show.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.department_show = Array.from(new Set(result));
      this.form_info.department_show = this.form_info.department_show
        ? this.form_info.department_show.join(",")
        : "";
        result = [];
      if (this.form_info.zr_department_range != [] && this.form_info.zr_department_range != '') {
        this.form_info.zr_department_range.map((item) => {
          if (item.toString().length >= 6) {
            result.push(parseInt(item.toString().slice(0, 3)));
          } else {
            result.push(item);
          }
        })
      }
      this.form_info.zr_department_range = Array.from(new Set(result));
      this.form_info.zr_department_range = this.form_info.zr_department_range
        ? this.form_info.zr_department_range.join(",")
        : "";
      console.log(this.form_info);
      if (this.form_info && Object.keys(this.form_info).indexOf('look_tel_list')) {
        delete this.form_info.look_tel_list;
      }
      console.log(this.see_tel_data);
      let uniqueArr = this.see_tel_data.filter((item, index) => {
        return this.see_tel_data.indexOf(item) === index;
      });
      this.form_info.see_tel_role = uniqueArr.join(",") 
      console.log(this.form_info);
      if(this.Maximumclaim == "0"){
        this.form_info.max_get_num = 0
      }
      if(this.Maximumall == "0"){
        this.form_info.max_get_total = 0
      }
      if(this.lookphone == "0"){
        this.form_info.max_look_num = 0
      }
      this.$http.setSiteCrmSetting(this.form_info).then((res) => {
        if (res.status === 200) {
          this.form_info.deal_approver_uid = this.form_info.deal_approver_uid
            ? this.setArr(this.form_info.deal_approver_uid)
            : "";
          this.form_info.admin_list = this.form_info.admin_list
            ? this.setArr(this.form_info.admin_list)
            : "";
            this.form_info.get_black_department = res.data.get_black_department
            ? this.setArr(res.data.get_black_department)
            : "";
          this.form_info.private_department = res.data.private_department
            ? this.setArr(res.data.private_department)
            : "";
          console.log(this.form_info.get_black_department);
          this.form_info.add_notice_department = res.data.add_notice_department
            ? this.setArr(res.data.add_notice_department)
            : "";
          this.form_info.department_show = res.data.department_show
            ? this.setArr(res.data.department_show)
            : "";
          this.form_info.zr_department_range = res.data.zr_department_range
            ? this.setArr(res.data.zr_department_range)
            : "";
          this.form_info.deal_commission_uid = this.form_info
            .deal_commission_uid
            ? this.setArr(this.form_info.deal_commission_uid)
            : "";
          this.form_info.mark_inherit_approver_uid = this.form_info
            .mark_inherit_approver_uid
            ? this.setArr(this.form_info.mark_inherit_approver_uid)
            : "";
          this.form_info.batch_import_uid = this.form_info.batch_import_uid
            ? this.setArr(this.form_info.batch_import_uid)
            : "";
          // this.form_info.config_approver_uid = this.form_info
          //   .config_approver_uid
          //   ? this.setArr(this.form_info.config_approver_uid)
          //   : "";
          this.form_info.get_black_list = this.form_info.get_black_list
            ? this.setArr(this.form_info.get_black_list)
            : "";
          this.form_info.private_uid = this.form_info.private_uid
            ? this.setArr(this.form_info.private_uid)
            : "";

          this.form_info.add_notice_uid = this.form_info.add_notice_uid
            ? this.setArr(this.form_info.add_notice_uid)
            : "";
          this.form_info.report_uid = this.form_info.report_uid
            ? this.setArr(this.form_info.report_uid)
            : "";
          this.form_info.del_client_list = this.form_info.del_client_list
            ? this.setArr(this.form_info.del_client_list)
            : "";
          this.$message.success("操作成功");
          // this.$router.push("/crm_customer_business");
          this.getSetting();
        } else {
          this.form_info.deal_approver_uid = this.form_info.deal_approver_uid
            ? this.setArr(this.form_info.deal_approver_uid)
            : "";
          this.form_info.admin_list = this.form_info.admin_list
            ? this.setArr(this.form_info.admin_list)
            : "";
            this.form_info.get_black_department = res.data.get_black_department
            ? this.setArr(res.data.get_black_department)
            : "";
          this.form_info.private_department = res.data.private_department
            ? this.setArr(res.data.private_department)
            : "";
          console.log(this.form_info.get_black_department);
          this.form_info.add_notice_department = res.data.add_notice_department
            ? this.setArr(res.data.add_notice_department)
            : "";
          this.form_info.department_show = res.data.department_show
            ? this.setArr(res.data.department_show)
            : "";
          this.form_info.zr_department_range = res.data.zr_department_range
            ? this.setArr(res.data.zr_department_range)
            : "";
          this.form_info.deal_commission_uid = this.form_info
            .deal_commission_uid
            ? this.setArr(this.form_info.deal_commission_uid)
            : "";
          this.form_info.mark_inherit_approver_uid = this.form_info
            .mark_inherit_approver_uid
            ? this.setArr(this.form_info.mark_inherit_approver_uid)
            : "";
          this.form_info.batch_import_uid = this.form_info.batch_import_uid
            ? this.setArr(this.form_info.batch_import_uid)
            : "";
          // this.form_info.config_approver_uid = this.form_info
          //   .config_approver_uid
          //   ? this.setArr(this.form_info.config_approver_uid)
          //   : "";
          this.form_info.get_black_list = this.form_info.get_black_list
            ? this.setArr(this.form_info.get_black_list)
            : "";
          this.form_info.private_uid = this.form_info.private_uid
            ? this.setArr(this.form_info.private_uid)
            : "";
          this.form_info.add_notice_uid = this.form_info.add_notice_uid
            ? this.setArr(this.form_info.add_notice_uid)
            : "";
          this.form_info.report_uid = this.form_info.report_uid
            ? this.setArr(this.form_info.report_uid)
            : "";
          this.form_info.del_client_list = this.form_info.del_client_list
            ? this.setArr(this.form_info.del_client_list)
            : "";
        }
      });
    },
    // 获取部门
    getDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status == 200) {
          this.AllDepartment = res.data;
          this.AllDepartmentA = res.data;
          this.AllDepartmentA.map((item) => {
            if(item.subs && item.subs.length) {
                item.disabled = true;
            } else {
                item.disabled = false;
            }
            return item;
          })
        }
      });
    },
    // 获取部门成员列表
    async getMemberList() {
      await this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.serverData = JSON.parse(JSON.stringify(res.data))
          this.serverData.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.serverData[0].user
          })
          this.recursionData(this.serverData);
          // 当键值key重复就更新key
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      })
      this.getSetting();
    },
    // 获取客户状态
    getCustomerStatus() {
      this.$http.getCrmCustomerFollowInfo({ params: { type: 4 } }).then((res) => {
        if (res.status == 200) {
          this.overdue_tracking_name = res.data
        }
      })
    },
    // 递归数据处理
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    // 全选
    Select_All(){
      console.log(this.datalist);
      let selectall_id = this.datalist
      console.log(selectall_id);
      let all_id = selectall_id.map(item=>{
        return item.id
      })
      this.selectedIds = all_id
    },
    // 选中变化时触发
    selecetedMember(e) {
      this.selectedIds = e.checkedKeys;
      // this.selectedList = e.checkedNodes;
    },
    selectMemberOk() {
      this.show_add_member = false;
      if (this.identification == 'get_black_list') {
        this.form_info.get_black_list = this.selectedIds;
      } else if (this.identification == 'deal_approver_uid') {
        this.form_info.deal_approver_uid = this.selectedIds;
      }else if (this.identification == 'private_uid') {
        this.form_info.private_uid = this.selectedIds;
      } else if (this.identification == 'admin_list') {
        this.form_info.admin_list = this.selectedIds;
      } else if (this.identification == 'batch_import_uid') {
        this.form_info.batch_import_uid = this.selectedIds;
      } else if (this.identification == 'add_notice_uid') {
        this.form_info.add_notice_uid = this.selectedIds;
      } else if (this.identification == 'report_uid') {
        this.form_info.report_uid = this.selectedIds;
      } else if (this.identification == 'del_client_list') {
        this.form_info.del_client_list = this.selectedIds;
      } else if (this.identification == 'export_uid') {
        this.form_info.export_uid = this.selectedIds;
      } else if (this.identification == 'export_not_sms_uid') {
        this.form_info.export_not_sms_uid = this.selectedIds;
      }else if (this.identification == 'add_potential_notice_uid'){
        this.form_info.add_potential_notice_uid = this.selectedIds;
      }else if (this.identification == 'zr_user_range'){
        this.form_info.zr_user_range = this.selectedIds;
      }else if (this.identification == 'potential_client_operation_uid'){
        this.form_info.potential_client_operation_uid = this.selectedIds;
      } else if (this.identification == 'management_see_list'){
        this.form_info.management_see_list = this.selectedIds;
      }else if (this.identification == 'auto_work_assign'){
        this.form_info.auto_work_assign = this.selectedIds;
      }else if (this.identification == 'dy_living_room_uid'){
        this.form_info.dy_living_room_uid = this.selectedIds;
      }else if (this.identification == 'auto_allocation_uid'){
        this.form_info.auto_allocation_uid = this.selectedIds;
      }
    },
    showPersonnelAuthority(val) {
      this.identification = val
      if (this.identification == 'get_black_list' && this.form_info.get_black_list != '') {
        this.selectedIds = this.form_info.get_black_list;
        this.department_title = '手动认领客户成员范围';
      } else if (this.identification == 'deal_approver_uid' && this.form_info.deal_approver_uid != '') {
        this.selectedIds = this.form_info.deal_approver_uid;
        this.department_title = '客源审批人';
      } else if (this.identification == 'admin_list' && this.form_info.admin_list != '') {
        this.selectedIds = this.form_info.admin_list;
        this.department_title = '客户管理员';
      } else if (this.identification == 'batch_import_uid' && this.form_info.batch_import_uid != '') {
        this.selectedIds = this.form_info.batch_import_uid;
        this.department_title = '批量导入客户权限范围';
      } else if (this.identification == 'add_notice_uid' && this.form_info.add_notice_uid != '') {
        this.selectedIds = this.form_info.add_notice_uid;
        this.department_title = '新增客户通知范围';
      } else if (this.identification == 'report_uid' && this.form_info.report_uid != '') {
        this.selectedIds = this.form_info.report_uid;
        this.department_title = '报备渠道审核专员';
      } else if (this.identification == 'del_client_list' && this.form_info.del_client_list != '') {
        this.selectedIds = this.form_info.del_client_list;
        this.department_title = '删除客户无需审核权限范围';
      } else if (this.identification == 'export_uid' && this.form_info.export_uid != '') {
        this.selectedIds = this.form_info.export_uid;
        this.department_title = '导出客户权限范围';
      } else if (this.identification == 'private_uid' && this.form_info.private_uid != '') {
        this.selectedIds = this.form_info.private_uid;
        this.department_title = '私客资料页浏览成员范围';
      }else if (this.identification == 'export_not_sms_uid' && this.form_info.export_not_sms_uid != '') {
        this.selectedIds = this.form_info.export_not_sms_uid;
        this.department_title = '导出客户权限范围';
      } else if(this.identification == 'add_potential_notice_uid' && this.form_info.add_potential_notice_uid != ''){
        this.selectedIds = this.form_info.add_potential_notice_uid;
        this.department_title = '潜在客户成员权限范围';
      } else if(this.identification == 'zr_user_range' && this.form_info.zr_user_range != ''){
        this.selectedIds = this.form_info.zr_user_range;
        this.department_title = '转交成员范围';
      }else if(this.identification == 'potential_client_operation_uid' && this.form_info.potential_client_operation_uid != ''){
        this.selectedIds = this.form_info.potential_client_operation_uid;
        this.department_title = '潜在客户跟进权限范围';
      }else if(this.identification == 'management_see_list' && this.form_info.management_see_list != ''){
        this.selectedIds = this.form_info.management_see_list;
        this.department_title = '智慧经营数据可见范围';
      }else if(this.identification == 'auto_work_assign'){
        this.selectedIds = this.form_info.auto_work_assign || [];
        this.department_title = '批量加入流转权限范围';
      }else if(this.identification == 'dy_living_room_uid'){
        this.selectedIds = this.form_info.dy_living_room_uid || [];
        this.department_title = '直播获客可进入成员范围';
      }else if(this.identification == 'auto_allocation_uid'){
        this.selectedIds = this.form_info.auto_allocation_uid || [];
        this.department_title = '批量自动分配权限成员范围';
      }  else {
        this.selectedIds = [];
      }
 
     
      // this.$nextTick(() => {
      //   console.log(1111111);
      //   this.$refs.get_black_list.blur();
      //   this.$refs.deal_approver_uid.blur();
      //   this.$refs.admin_list.blur();
      //   this.$refs.batch_import_uid.blur();
      //   this.$refs.add_notice_uid.blur();
      //   this.$refs.del_client_list.blur();
      //   this.$refs.export_uid.blur();
      //   this.$refs.add_potential_notice_uid.blur();
      // })
      this.$nextTick(() => {
        console.log(1111111);
        this.$nextTick(() => {
          if (this.$refs.get_black_list) {
            this.$refs.get_black_list.blur();
          }
          if(this.$refs.deal_approver_uid){
            this.$refs.deal_approver_uid.blur();
          }
          if(this.$refs.admin_list){
            this.$refs.admin_list.blur();
          }
          if(this.$refs.batch_import_uid){
            this.$refs.batch_import_uid.blur();
          }
          if(this.$refs.add_notice_uid){
            this.$refs.add_notice_uid.blur();
          }
          if(this.$refs.report_uid){
            this.$refs.report_uid.blur();
          }
          if (this.$refs.private_uid) {
            this.$refs.private_uid.blur();
          }
          if(this.$refs.del_client_list){
            this.$refs.del_client_list.blur();
          }
          if(this.$refs.export_uid){
            this.$refs.export_uid.blur();
          }
          if(this.$refs.export_not_sms_uid){
            this.$refs.export_not_sms_uid.blur();
          }
          if(this.$refs.add_potential_notice_uid){
            this.$refs.add_potential_notice_uid.blur();
          }
          if(this.$refs.zr_user_range){
            this.$refs.zr_user_range.blur();
          }
          if(this.$refs.potential_client_operation_uid){
            this.$refs.potential_client_operation_uid.blur();
          }
          if(this.$refs.management_see_list){
            this.$refs.management_see_list.blur();
          }
          if(this.$refs.auto_work_assign){
            this.$refs.auto_work_assign.blur();
          }
          if(this.$refs.dy_living_room_uid){
            this.$refs.dy_living_room_uid.blur();
          }
          if(this.$refs.auto_allocation_uid){
            this.$refs.auto_allocation_uid.blur();
          }
        });
      });
      this.show_add_member = true;
    },
    PersonnelChange(val) {
      this.selectedIds = val;
    },
    //单独设置成员每日查看电话客户数量
    numberphone(describe){
      this.$goPath(`/settings_page?keyword=${describe}`);

      
    },
  },
};
</script>

<style  lang="scss" scoped>
.tail{
  color: #2e3c4e;
  margin-left: 10px;
}
</style>
