<template>
    <div>
        <el-form
            style="height: 140vh"
            :model="form_info"
            label-width="240px">
            <el-form-item label="线索同步: " label-width="228px">
                <div style="margin-left:10px;">
                    <el-radio v-model="form_info.t_clue_push" :label="1">不同步，仅更新用户信息</el-radio>
                    <el-radio v-model="form_info.t_clue_push" :label="2">仅同步系统不存在的线索ID</el-radio>
                    <el-tooltip
                        class="item"
                        effect="light"
                        placement="right"
                        >
                        <div slot="content" style="max-width: 300px">
                            <!-- 开启后系统将根据设定规则将线索掉入公海  -->
                        </div>
                        <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                    </el-tooltip>
                </div>
            
        </el-form-item>
        <el-form-item label="同步时长：">
            <template>
                <el-input
                style="width: 300px;margin-left:0px"
                placeholder="请输入天数"
                class="overdue"
                v-model="form_info.t_clue_dy_official_later"
                min="0"
                step="1"
                type="number"
                >
                <template slot="append">
                    <el-select v-model="max_overdue_unit">
                      <el-option label="分钟" value="1"></el-option>
                    </el-select>
                </template>
            </el-input>
            </template>
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="margin-left: 10px"
                >
                <div slot="content" style="max-width: 300px">
                    开启抖音官方同步时线索不同步时长 单位分钟 默认60 <br>
                    0为不限制 60表示线索创建时间小于60分钟不同步
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="同步时间：">
                <el-date-picker class="color" v-model="value1" type="datetimerange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                @change="Select_Time">
              </el-date-picker>
            <el-tooltip
                class="item"
                effect="light"
                placement="right"
                style="margin-left: 15px"
                >
                <div slot="content" style="max-width: 300px">
                   <!-- 1.同时满足: 指上边两条规则同时满足才会触发掉公。<br/>
                   2.单一满足: 指满足其中一个条件就会触发掉公规则。 -->
                </div>
                <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
            </el-tooltip>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="onClickForm">确认</el-button>
        </el-form-item>
        </el-form>
    </div>
</template>
<script>
export default {
    components: {
  },
    data() {
        return {
            value1:[],
            form_info: {
                t_clue_push: 2, //线索推送 1.不推送 仅更新用户信息 2.仅推送系统不存在的线索ID
                t_clue_dy_official_later: "", //开启抖音官方推送时线索不入库时长 单位分钟 默认60 0不限制 60表示线索创建时间小于60分钟不推送
                t_clue_push_start_time:"",//推送开始时间
                t_clue_push_end_time:"",//推送结束时间
               
            },
            overdue_tracking_name: [], // 掉公范围状态
            max_overdue_unit:"1",//单位分钟 
        }
    },
    created() {
        this.website_id = this.$route.query.website_id
        this.getCustomerStatus();
    },
    methods: {
        // 获取客户状态
        getCustomerStatus() {
            this.$http.getTassistant().then((res) => {
                if(res.status == 200) {
                this.form_info = res.data
                if(res.data.t_clue_push_start_time!=0){
                    this.value1.push(res.data.t_clue_push_start_time)
                    this.value1.push(res.data.t_clue_push_end_time)
                }
               
                console.log( res.data);
                }
            })
        },
        //选中时间
        Select_Time(e){
        this.form_info.t_clue_push_start_time = e ? e[0] : ""; // 赋值开始时间
        this.form_info.t_clue_push_end_time = e? e[1] : ""; // 赋值结束时间
        },
        // 确认提交参数
        onClickForm(){
            let params = JSON.parse(JSON.stringify(this.form_info)); // 深拷贝客户状态
            if(!params.t_clue_push_start_time){
                params.t_clue_push_start_time = 0
                params.t_clue_push_end_time = 0              
            }
            this.$http.setTassistant(params).then((res) => {
                if(res.status == 200) {
                    this.$message.success("操作成功！")
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep .overdue{
    width: 300px;
    .el-input-group__append,
    .el-input-group__prepend {
     width: 60px; /* 将宽度设置为您所需的值 */
    }
}
/deep/.el-date-editor .el-range-input{
    background-color: white !important;
}
</style>