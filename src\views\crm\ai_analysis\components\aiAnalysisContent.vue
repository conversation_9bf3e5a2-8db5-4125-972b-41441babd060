<template>
<div v-fixed-scroll="48">
	<!-- 筛选条件区域 -->
	<div class="filter-wrapper">
		<filterBar
			:params="params"
			@search="handleSearch"
			@reset="handleReset"
		/>
	</div>

	<!-- 数据表格区域 -->
	<aiAnalysisTable
		:params="params"
		:list="list"
		:loading="loading"
		@goCustomerDetail="handleGoCustomerDetail"
		@refreshData="handleRefreshData"
	/>

	<!-- 分页区域 -->
	<div class="tab-content-footer">
		<div>
			<el-button type="primary" size="small" @click="handleReset">清空</el-button>
			<el-button type="primary" size="small" @click="handleRefreshData">刷新</el-button>
		</div>
		<el-pagination
			background
			layout="total,sizes,prev, pager, next, jumper"
			:total="count"
			:page-sizes="[10, 20, 30, 50, 100]"
			:page-size="params.per_page"
			:current-page="params.page"
			@current-change="handlePageChange"
			@size-change="handleSizeChange">
		</el-pagination>
	</div>
</div>
</template>

<script>
import aiAnalysisTable from "./aiAnalysisTable";
import filterBar from "./filterBar";

export default {
	name: 'AiAnalysisContent',
	components: {
		aiAnalysisTable,
		filterBar
	},
	props: {
		params: { type: Object, default: () => ({}) },
		list: { type: Array, default: () => [] },
		loading: { type: Boolean, default: false },
		count: { type: Number, default: 0 }
	},
	methods: {
		// 处理筛选条件变更
		handleSearch(newParams) {
			this.$emit('search', newParams);
		},
		
		// 重置搜索
		handleReset() {
			this.$emit('reset');
		},
		
		// 页码变更
		handlePageChange(page) {
			this.$emit('pageChange', page);
		},
		
		// 每页数量变更
		handleSizeChange(size) {
			this.$emit('sizeChange', size);
		},
		
		// 跳转客户详情
		handleGoCustomerDetail(row) {
			this.$emit('goCustomerDetail', row);
		},

		// 刷新数据
		handleRefreshData() {
			this.$emit('refreshData');
		}
	}
}
</script>

<style scoped>
.filter-wrapper {
	padding: 16px 16px 12px;
	background: #fff;
	margin-bottom: 0;
}

.tab-content-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 28px;
	height: 48px;
	background: #fff;
	border-top: 1px solid #ebeef5;
}
</style>
