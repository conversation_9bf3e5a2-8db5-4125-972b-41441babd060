<template>
<div class="add-base-info">
    <div class="div row collapse-control" @click="isContentCollapse = !isContentCollapse">
        <span class="text"> 基本信息 </span>
        <span :class="isContentCollapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
    </div>
    <myCollapse :isActive="!isContentCollapse" class="collapse-pane">
        <template v-slot:content>
            <el-form ref="form" :rules="rules" :model="params" label-width="120px" class="form-flex">
                <el-form-item label="客户名称" prop="name">
                    <el-input v-model="params.name"></el-input>
                </el-form-item>
                <el-form-item label="客户手机号" prop="phone">
                    <el-input v-model="params.phone" @input="onPhoneInput"></el-input>
                </el-form-item>
                <el-form-item label="成交人" prop="admin_id">
                    <tMemberSelect v-model="params.admin_id" placeholder="选择成交人" ref="tMemberSelect"/>
                </el-form-item>
                <el-form-item label="成交时间" prop="cjrq">
                    <el-date-picker v-model="params.cjrq" type="datetime" placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="项目名称" prop="project">
                    <t-crm-project-select allow-create default-first-option v-model="params.project" value-key="name" placeholder="请搜索选择项目" ref="tCrmProjectSelect" width="100%"/>
                </el-form-item>
                <el-form-item label="房号" prop="room_number">
                    <el-input v-model="params.room_number"></el-input>
                </el-form-item>
                <el-form-item label="购买方式">
                    <el-select v-model="params.buy_type" clearable>
                        <el-option :key="v.value" :label="v.label" :value="v.value" v-for="v in buyTypes"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="客户住址">
                    <el-input v-model="params.address"></el-input>
                </el-form-item>
                <el-form-item label="面积" prop="mianji">
                    <el-input v-model="params.mianji">
                        <i slot="suffix">m²</i></el-input>
                </el-form-item>
                <el-form-item label="单价" prop="danjia">
                    <el-input v-model="params.danjia"><i slot="suffix">元</i></el-input>
                </el-form-item>
                <el-form-item label="成交总额" prop="amount">
                    <el-input v-model="params.amount"><i slot="suffix">元</i></el-input>
                </el-form-item>
                <el-form-item label="佣金点位" prop="proportion">
                    <el-input v-model="params.proportion"><i slot="suffix">%</i></el-input>
                </el-form-item>
                
                <el-form-item label="渠道名称">
                    <el-input v-model="params.channel_name"></el-input>
                </el-form-item>
                <el-form-item label="渠道分成">
                    <el-input v-model="params.channel_proportion" placeholder="渠道费/跳点" class="input-with-select">
                        <template #prepend>
                            <el-select v-model="params.channel_proportion_type" class="in-input">
                                <el-option label="减" :value="2"></el-option>
                                <el-option label="加" :value="1"></el-option>    
                            </el-select>
                        </template>
                        <i slot="suffix">%</i>
                    </el-input>
                </el-form-item>
                <el-form-item label="渠道税点" class="form-item-amount">
                    <el-input v-model="params.channel_rate" placeholder="代扣税点( 选填 )" class="input-with-select">
                        <i slot="suffix">%</i>
                    </el-input>
                    <div class="calc-commission-desc" v-if="isAdd">
                        <div class="commission-text">
                            当前佣金计算：<span class="commission-text-red">{{calcCommission}}</span> 元
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </template>
    </myCollapse>

    <div class="recive-commission-desc" v-if="!isAdd">
        <div class="commission-text">
            应收佣金：<span class="commission-text-red">{{params.amount || 0}}</span> 
            * <span class="commission-text-red">{{params.proportion || 0}}%</span>
            <template v-if="channelCommission != 0">
               {{ channelCommission > 0 ? '+' : '-'}} 渠道分佣 <span class="commission-text-red">{{Math.abs(channelCommission).toFixed(2)}}</span>
            </template>
            <template v-if="withholdTaxAmount != 0">
                - 代扣税 <span class="commission-text-red">{{ withholdTaxAmount.toFixed(2) }}</span>
            </template>
            = <span class="commission-text-red">{{calcCommission.toFixed(2)}}</span> 元
        </div>
    </div>

</div>  
</template>
  
<script>
import myCollapse from "@/components/navMain/crm/components/collapse";
import tMemberSelect from "@/components/tplus/tSelect/tMemberSelect.vue";
import Utils from '@/utils/utils';
import TCrmProjectSelect from '@/components/tplus/tSelect/tCrmProjectSelect.vue';
export default {
    components: {
        myCollapse, tMemberSelect,
        TCrmProjectSelect
    },
    props: {
        value: { type: Object, default: () => {return {}} },
        reportData: { type: Object, default: () => {return {}} }
    },
    data() {
        return {
            isContentCollapse: false,   //是否折叠
            submiting: false,
            params: {},
            buyTypes: [
                { label: '按揭', value: 1 },
                { label: '抵押', value: 2 },
                { label: '全款', value: 3 },
            ],
            rules: {
                name: [
                    { required: true, message: '请输入客户名称', trigger: 'blur' }
                ],
                phone: [
                    { required: true, message: "请输入手机号", trigger: "blur" },
                    { validator(rule, value, callback) {
                        const reg = /^1[3-9][0-9]\d{8}$/;
                        if(!reg.test(value)){
                            callback('手机号格式不正确')
                        }else{
                            callback();
                        }
                    }, trigger: "blur" }
                ],
                amount: [
                    { required: true, message: '请输入成交金额', trigger: 'change' },
                ],
                admin_id: [
                    { required: true, message: '请选择成交人', trigger: 'change' },
                ],
                cjrq: [
                    { required: true, message: '请选择成交日期', trigger: 'blur' },
                ],
                proportion: [
                    { required: true, message: '请填写佣金点位', trigger: 'blur' },
                ],
                project: [
                    { required: true, message: '请填写项目名称', trigger: 'change' },
                ],
                mianji: [
                    { required: true, message: '请填写面积', trigger: 'blur' },
                ],
                danjia: [
                    { required: true, message: '请填写单价', trigger: 'blur' },
                ],
            }
        };
    },
    
    computed: {
        //是否新增
        isAdd(){
            return !this.params.id;
        },
        //计算总额
        calcAmount(){
            let amount = this.params.mianji * this.params.danjia;
            return !amount || !isFinite(amount) ? '' : amount.toFixed(2)
        },
        //正常佣金
        normalCommission(){
            let amount = this.params.amount * this.params.proportion / 100;
            amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
            this.$emit('update:normalCommission', amount);
            return amount; 
        },
        //渠道分佣
        channelCommission(){
            let amount =  (this.params.channel_proportion_type == 2 ? -1: 1) * this.normalCommission * this.params.channel_proportion / 100;
            amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
            this.$emit('update:channelCommission', amount);
            return amount; 
        },
        //代扣税金额
        withholdTaxAmount(){
            let amount = (this.normalCommission + this.channelCommission) * this.params.channel_rate / 100;
            amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
            this.$emit('update:withholdTaxAmount', amount);
            return amount; 
        },
        //当前计算佣金
        calcCommission(){
            let amount = this.normalCommission + this.channelCommission - this.withholdTaxAmount;
            amount = !amount || !isFinite(amount) ? 0 : amount.toFixed(2) * 1;
            this.$emit('update:calcCommission', amount);
            return amount
        },
    },
    watch: {
        reportData: {
            handler(data) {
                this.params = { 
                    id: data.report_id || 0,
                    admin_id : data.admin?.admin_id ?? '', 
                    name: data.customer?.name ?? '', 
                    phone: data.customer?.phone ?? '', 
                    amount: data.deal?.amount ?? '', 
                    cjrq: data.deal?.cjrq ?? '', 
                    proportion: data.deal?.proportion ?? '', 
                    project: data.deal?.project ?? '', 
                    project_id: data.deal?.project_id ?? '', 
                    mianji: data.deal?.mianji ?? '', 
                    danjia: data.deal?.danjia ?? '',
                    room_number: data.deal?.room_number ?? '',
                    operate_from: data.deal?.operate_from ?? 1,    //操作来源
                    buy_type: data.deal?.buy_type || '',
                    address: data.deal?.address ?? '',
                    channel_name: data.deal?.channel_name ?? '',
                    channel_proportion_type: data.deal?.channel_proportion_type || 2, //2减，1加
                    channel_proportion: data.deal?.channel_proportion || '',
                    channel_rate: data.deal?.channel_rate || '',
                }
                this.$nextTick(() => {
                    this.params.amount = data.deal?.amount ?? '';
                    if(this.params.project_id && this.params.project){
                        this.$refs.tCrmProjectSelect.setOptionData({
                            id: this.params.project_id,
                            name: this.params.project
                        })
                    }
                });
            },
            immediate: true
        },
        calcAmount(val){
            this.params.amount = val;
        },
        params: {
            handler(val){
                this.$emit('update:params', val);
            },
            deep: true
        },
    },
    methods: {
        submitForm() {
            this.submit((data)=>{
                this.$emit('submit', data);
            });
        },
        async submit(cb, conf = {}) {
            const vali = await this.$refs.form.validate().catch(()=>{});
            if(vali){
                if(this.submiting){
                    return
                }

                const params = {...this.params};
                const option = this.$refs.tCrmProjectSelect.getSelectOption();
                params.project_id = option ? option.id : '';

                this.isAdd && delete params.id;

                this.submiting = true;
                const res = await this.$http[this.isAdd ?'addReportAPI':'editReportAPI'](params);
                this.submiting = false;
                if(res.status == 200){
                    conf.autoSuccessMsg !== false && this.$message.success(res.data?.msg || '保存成功');
                    this.isAdd && (this.params.id = res.data?.id);
                    cb && cb(res.data);
                    return true;
                }
            }
        },
        //通过所填手机号查询crm客户成交人-设置成交人
        onPhoneInput: Utils.debounce(async function(){
            const phone = this.params.phone;
            if(phone.length ==  11){
                const res = await this.$http.crmDealSearchDealUser(phone);
                if(res.status == 200){
                    const admin_id = res.data?.id || 0;
                    if(admin_id){
                        const options = this.$refs.tMemberSelect.getOptionData()                 
                        if(options.find(item => item.value == admin_id)){
                            this.params.admin_id = admin_id;
                        }
                    }
                }
            }
        })
        
    },
};
</script>
  
<style lang="scss" scoped>
::v-deep .el-form-item__content{
    width: 220px;
    .input-with-select .el-input-group__prepend {
        background-color: #fff;
        .el-select{
            color: #606266;
            width: 65px;
        }
    }
    .el-input__suffix{
        line-height: 40px;
    }

}
.add-base-info{
    .collapse-control {
        border: none;
        width: 100%;
        text-align: end;
        line-height: 1;
        padding: 12px 0 20px 0;
        color: #a1a1a1;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        .text {
            font-size: 14px;
        }
    }
    .collapse-pane{
        border-bottom: 1px solid #f4f4f4;position: relative;
    }
    .form-flex{
        display: flex;flex-wrap: wrap;
        .form-item-amount{
            ::v-deep .el-form-item__content{
                width: auto;
                display: flex;
                .el-input{
                    flex: 1;
                    width: 220px;
                }
            }
            .calc-commission-desc{
                height: 40px;
                line-height: 40px;
                padding: 0 20px;
            }
        }
    }
    
    .recive-commission-desc{
        display: flex;
        height: 50px;
        align-items: center;
        padding: 0 35px;
    }
    .commission-text{
        font-size: 16px;
        color: #6c6c6c;
        .commission-text-red{
            color: #f40;
            font-weight: 600;
        }
    }
}

</style>