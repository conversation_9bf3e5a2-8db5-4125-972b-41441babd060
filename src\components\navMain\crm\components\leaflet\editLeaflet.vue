<template>
  <div class="add-leaflet">
    <div class="add-leaflet-l">
      <el-form ref="form" :model="form" label-width="100px">
        <el-form-item label="活动标题" prop="title">
          <el-input v-model="form.title"   type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"></el-input>
        </el-form-item>
        <el-form-item label="副标题" prop="title_sub">
          <el-input v-model="form.title_sub"  type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"></el-input>
        </el-form-item>
        <el-form-item label="富文本" style="line-height: 0;">
          <UE :value="ueditor.value" :config="ueditor.config" ref="ue" @input="inputUe"></UE>
        </el-form-item>
        <el-form-item label="按钮名称" prop="btn_name">
          <el-input v-model="form.btn_name"></el-input>
        </el-form-item>
        <el-form-item label="宣传图">
          <el-upload :limit="1" :headers="myHeader" :action="LeafletPicUpload" :on-success="LeafletTopSuccess"
            list-type="picture-card">
            <img v-if="form.top_pic" :src="form.top_pic" class="avatar">
            <i v-else class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="分享标题" prop="share_title">
          <el-input v-model="form.share_title"  type="textarea"
          :autosize="{ minRows: 2, maxRows: 4}"></el-input>
        </el-form-item>
        <el-form-item label="分享图片">
          <el-upload :limit="1" :headers="myHeader" :action="LeafletPicUpload" :on-success="LeafletShareSuccess"
            list-type="picture-card">
            <img v-if="form.share_pic" :src="form.share_pic" class="avatar">
            <i v-else class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="分享描述" prop="share_desc">
          <el-input type="textarea" v-model="form.share_desc"
          :autosize="{ minRows: 2, maxRows: 4}"></el-input>
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch style="margin-top: 10px;" v-model="switchStatus" @change="changeSwitch"></el-switch>
        </el-form-item>
        <el-form-item label="城市">
          <tRegionCascader v-model="regionIds" api="getRegionCityList" clearable/>
        </el-form-item>
        <el-form-item label="滚动报名信息">
          <el-radio-group v-model="form.show_enroll"  style="line-height: 50px;">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报名信息标题">
          <el-input v-model="form.enroll_name"></el-input>
        </el-form-item>

        <div style="float: right;margin-top: 20px;">
          <el-button type="primary" @click="submit('form')">确认提交</el-button>
          <el-button type="warning" @click="regain">重新编辑</el-button>
        </div>
      </el-form>
    </div>
    <div class="add-leaflet-r">
      <myPreview :title="form.title" :title_sub="form.title_sub" :content="form.content" :btn_name="form.btn_name"
        :top_pic="form.top_pic" :share_title="form.share_title" :share_pic="form.share_pic" :share_desc="form.share_desc">
      </myPreview>
    </div>
  </div>
</template>

<script>
import UE from "@/components/ueditor";
import config from "@/utils/config";
import { mapState } from "vuex";
import myPreview from './components/my-preview/index.vue'
import tRegionCascader from '@/components/tplus/tCascader/tRegionCascader.vue';
export default {
  props: {
    id: {
      type: Number,
      default: 0
    },
    // 用于监听tab切换 并 筛选对应数据
    sole: {
      type: String,
      default: ''
    },
    leafletList: {
      type: Array,
      default: () => []
    }
  },
  components: { UE, myPreview, tRegionCascader },
  data() {
    return {
      switchStatus: false,
      ueditor: {
        value: "",
        config: {
          initialFrameWidth: "100%",
        },
      },
      form: {
        id: 0,
        title: '',//标题
        title_sub: '',//副标题
        content: '',//富文本
        btn_name: '',//按钮名称
        top_pic: '',//顶部图片
        share_title: '',//分享标题
        share_pic: '',//分享图片
        share_desc: '',//分享描述
        is_show: 1,//是否显示
        province_id: 0,
        city_id: 0,
        show_enroll: 0,
        enroll_name: ''
      },
      LeafletPicUpload: '/api/admin/page_plugin/upload_pic',
      top_pic: [],
      share_pic: [],
      newsImageUrl: "",
      editLeafletData: {},
      // rules: {
      //   title: [
      //     { required: true, message: '请输入活动标题', trigger: 'blur' },
      //     { min: 2, max: 8, message: '长度在 2 到 8 个字符', trigger: 'blur' }
      //   ],
      //   title_sub: [
      //     { required: false, message: '请输入活动副标题', trigger: 'blur' },
      //     { min: 2, max: 8, message: '长度在 2 到 8 个字符', trigger: 'blur' }
      //   ],
      //   btn_name: [
      //     { required: true, message: '请输入按钮文字', trigger: 'blur' },
      //     { min: 2, max: 8, message: '长度在 2 到 8 个字符', trigger: 'blur' }
      //   ],
      //   share_title: [
      //     { required: false, message: '请输入分享标题', trigger: 'blur' },
      //     { min: 2, max: 8, message: '长度在 2 到 8 个字符', trigger: 'blur' }
      //   ],
      //   share_desc: [
      //     { required: false, message: '请输入分享描述', trigger: 'blur' },
      //     { min: 2, max: 8, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      //   ],
      // }
    }
  },
  watch: {
    "id": {
      immediate: true,
      handler() {
        let leafletData = this.leafletList.find((item) => item.id == this.id)
        this.form = leafletData
        this.form.is_show == 1 ? this.switchStatus = true : this.switchStatus = false
        this.ueditor.value = leafletData.content
        let content = leafletData.content
        this.form.content = content.replace(/\<img/gi, '<img style="width:100%;height:auto;margin:0;padding:0;" ').replace(/style="text-wrap: wrap;"/gi, '').replace(/\<p/gi, '<p style="padding:0;margin:0;font-size:0" ')
      }
    }
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
    ...mapState(["website_info"]),
    regionIds: {
      set([ province_id, city_id ]){
        this.form.province_id = province_id || 0; 
        this.form.city_id = city_id || 0;
      },
      get(){
        return [ this.form.province_id, this.form.city_id ]
      }
    }
  },
  mounted() {
    const leafletData = this.leafletList.find((item) => item.id == this.id)
    this.form = leafletData
    this.form.is_show == 1 ? this.switchStatus = true : this.switchStatus = false
  },
  methods: {
    // async geteditLeafletData() {
    //   const res = await this.$http.leafletDetail(this.id)
    //   if (res.status == 200) {
    //     this.editLeafletData = res.data
    //     const { id, title, title_sub, content, btn_name, top_pic, share_title, share_desc, is_show, share_pic } = res.data
    //     this.form = { id, title, title_sub, content, btn_name, top_pic, share_title, share_pic, share_desc, is_show, }
    //     this.ueditor.value = content
    //     this.form.is_show == 1 ? this.switchStatus = true : this.switchStatus = false
    //   }
    // },
    LeafletTopSuccess(response) {
      this.form.top_pic = response.url
    },
    LeafletShareSuccess(response) {
      this.form.share_pic = response.url
    },
    inputUe(obj) {
      let content = obj.content
      this.form.content = content.replace(/\<img/gi, '<img style="width:100%;height:auto;margin:0;padding:0;" ').replace(/style="text-wrap: wrap;"/gi, '').replace(/\<p/gi, '<p style="padding:0;margin:0;font-size:0" ')
    },
    changeSwitch(e) {
      e ? this.form.is_show = 1 : this.form.is_show = 0
    },
    submit(form) {
      this.$refs[form].validate(async (valid) => {
        if (valid) {
          if (!this.form.top_pic) {
            this.$message({
              message: '请上传活动宣传图',
              type: 'warning'
            });
            return
          }
          const res = await this.$http.editLeaflet(this.form)
          if (res.status !== 200) {
            this.$message({
              message: res.data.message,
              type: 'warning'
            });
            return
          }
          console.log(res);
          this.$emit('changeLeafletStatus')

        } else {
          this.$message({
            message: '请填写相关信息',
            type: 'warning'
          });
          return false;
        }
      });
    },
    regain() {
      const { id, title, title_sub, content, btn_name, top_pic, share_title, share_desc, is_show, share_pic } = this.editLeafletData
      this.form = { id, title, title_sub, content, btn_name, top_pic, share_title, share_pic, share_desc, is_show, }
      this.ueditor.value = content
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-form-item__content {
  line-height: 0 !important;
}

.avatar {
  display: block;
  width: 150px;
  height: 150px;
}

.add-leaflet {
  position: relative;
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  width: 100%;

  .add-leaflet-l {
    width: 60%;
  }

  .add-leaflet-r {
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 99;
  }
}
</style>