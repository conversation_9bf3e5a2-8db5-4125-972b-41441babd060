<template>
  <div class="outbount">
    <!-- 顶部模块 -->

    <div class="bg_fff div row" style="padding: 20px">
      <div class="div bottom-border row flex-1">
        <div class="outbount_top_item align-center flex-row mr20 mb12">
          <!-- <div class="task_name mr10">成员姓名</div> -->
          <!-- admin_id -->
          <div class="task_sel select_name align-center flex-row">
            <div>
              <el-select v-model="name_depart" placeholder="请选择成员/部门" size="small" style="width: 150px;"
              @change="changemorb" @focus="focusdepart">
              <el-option
                v-for="item in name_departlist"
                :key="item.value"
                :label="item.name"
                :value="item.value">
              </el-option>
              </el-select>
              <!-- <el-input placeholder="请选择成员" v-model="user_name" size="small" style="width: 150px;"
                @focus="showMemberList">
                <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close"></i></el-input> -->
            </div>
            <el-button size="small" class="export_excel" type="primary" @click="exportExcel" v-if="export_demand">
              导出
            </el-button>
            <el-button v-if="listdata" class="listbnt" size="mini" @click="outlist">
              <div class="flex-row items-center">
                <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinlist.png" alt="">
                <span>数据列表</span>
              </div>

            </el-button>
          </div>
        </div>
      </div>
      <div class="bottom-border div row">
        <!-- <span class="text">创建时间：</span> -->

        <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
        <!-- <span class="text">自定义：</span> -->
        <el-date-picker style="width: 250px" size="small" v-model="p_time" type="datetimerange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" @change="changeTimeRange">
        </el-date-picker>
      </div>
    </div>
    <div class="bg_fff box_top marbot28">
      <el-row class="top_row" :gutter="20" :class="{ small: is_small_sys }">
        <el-col class="el-col-4-8">
          <div class="top_item" :style="zuoxiStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
              </div>
              <div class="top_item_left_name">坐席数量</div>
            </div>
            <div class="top_item_right flex-row">
              <div class="top_item_right_num">{{ topInfo.seats_count }}</div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="top_item" :style="bodaStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
              </div>
              <div class="top_item_left_name">拨打总量</div>
            </div>
            <div class="top_item_right flex-row">
              <div class="top_item_right_num">{{ topInfo.call_count }}</div>
            </div>
          </div>
        </el-col>

        <el-col class="el-col-4-8">
          <div class="top_item" :style="botongStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
              </div>
              <div class="top_item_left_name">拨通总量</div>
            </div>
            <div class="top_item_right flex-row">
              <div class="top_item_right_num">{{ topInfo.on_call_count }}</div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="top_item" :style="bodaStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
              </div>
              <div class="top_item_left_name">接通率</div>
            </div>
            <div class="top_item_right flex-row">
              <div class="top_item_right_num">
                {{ (+topInfo.on_call_percentage * 100).toFixed(0) }}%
              </div>
            </div>
          </div>
        </el-col>
        <el-col class="el-col-4-8">
          <div class="top_item" :style="weiboStyle">
            <div class="top_item_left flex-row flex-1">
              <div class="top_item_left_img">
                <img :src="`${this.$imageDomain}/static/admin/waihu/<EMAIL>`" alt="" />
              </div>
              <div class="top_item_left_name">平均时长</div>
            </div>
            <div class="top_item_right flex-row">
              <div class="top_item_right_num">
                {{ topInfo.avg_duration | secondFormat }}
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 顶部模块结束 -->
    <!-- 图表 模块 -->
    <div class="marbot28 bg_fff charts">
      <!-- 图表模块 -->
      <div class="echart flex-row align-center">
        <!-- <el-row class ="" :gutter="20"> -->
        <div class="flex-1">
          <el-col :span="24">
            <el-col :span="12">
              <div class="echart_left">
                <div class="echart_l_top">概览</div>
                <div class="echart_l_con">
                  <div class="chart-box sale-chart" id="chart-box" style="width: 100%; height: 390px"></div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="echart_left">
                <div class="echart_l_top">时长统计</div>
                <div class="echart_l_con">
                  <div class="chart-box pie-chart" style="width: 100%; height: 390px"></div>
                </div>
              </div>
            </el-col>
          </el-col>
        </div>
        <div class="echart-right">
          <div class="echart_l_top">通话统计</div>
          <div class="echart_l_con flex-row flex-wrap">
            <div class="echart_l_con_item" v-for="(item, index) in left1Data" :key="index">
              <div class="echart_l_con_item_top">{{ item.count }}</div>
              <div class="echart_l_con_item_bottom">{{ item.name }}</div>
            </div>
            <!-- <div class="echart_l_con_item">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div>
            <div class="echart_l_con_item">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div>
            <div class="echart_l_con_item">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div>
            <div class="echart_l_con_item echart_l_con_item_one">
              <div class="echart_l_con_item_top">0</div>
              <div class="echart_l_con_item_bottom">通话统计</div>
            </div> -->
          </div>
        </div>
        <!-- </el-row> -->
      </div>

      <!-- 表格模块 -->
      <div class="table_con">
        <div class="table_search flex-row align-center">
          <div class="search_left flex-1 flex-row align-center">
            <div class="table_title">坐席情况统计</div>
            <!-- <div class="table_search_inp flex-row align-center">
              <el-input
                class="small_inp"
                size="small"
                v-model="zuoxi_params.start_time"
              >
                <template slot="append">秒</template>
              </el-input>
              <span>-</span>
              <el-input
                class="small_inp"
                size="small"
                v-model="zuoxi_params.end_time"
              >
                <template slot="append">秒</template>
              </el-input>
            </div> -->
          </div>
          <div class="table_search_btns flex-row align-center">
            <!-- <el-button
              size="small"
              type="primary"
              class="mr10"
              @click="searchTable"
            >
              搜索
            </el-button> -->
            <!-- <el-button size="small" type="primary" @click="exportExcel">
              导出
            </el-button> -->
          </div>
        </div>
        <div class="table">
          <el-table v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row :row-style="$TableRowStyle" @sort-change="sortChange">
            <!-- @selection-change="handleSelectionChange" -->
            <!-- <el-table-column type="selection" width="55"> </el-table-column> -->

            <el-table-column label="成员名称" width="200px" prop="admin_name" fixed="left">
              <template slot-scope="scope">
                <div class="div row mem_info">
                  <div class="avatar">
                    <img v-if="scope.row.avatar" :src="scope.row.avatar" alt="" />
                    <span v-else>
                      {{ scope.row.admin_name && scope.row.admin_name[0] }}
                    </span>
                  </div>
                  <el-popover
                      placement="top-start"
                      width="200"
                      trigger="hover">
                      <div class="info">
                        <div class="info_name">
                          {{ scope.row.admin_name }}
                        </div>
                        <div class="info_dep">
                          <span class="dep_item" v-for="item in scope.row.department" :key="item.id">
                             {{ item.name }}
                          </span>
                        </div>
                      </div>
                      <span slot="reference"> 
                        <!-- 在这里放置触发 popover 的元素 -->
                        <div class="info-trigger">
                          {{ scope.row.admin_name }}
                        </div>
                        <div class="info_dep">
                          <span class="dep_item" v-for="item in scope.row.department" :key="item.id">
                            {{ item.name }}
                          </span>
                        </div>
                      </span>
                    </el-popover>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="成员手机号" prop="admin_phone">
            </el-table-column>
            <el-table-column label="外显手机号" prop="show_phone" width="130px" v-slot="{ row }">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover">
                <div>{{ row.show_phone}}</div>
                <div class="Explicitphone"  slot="reference">{{ row.show_phone}}</div>
              </el-popover>
               
            </el-table-column>
            <el-table-column label="接通数量" prop="on_call_count" sortable="custom">
            </el-table-column>
            <el-table-column label="拨打数量" prop="call_count" sortable="custom">
            </el-table-column>
            <el-table-column label="通话时长" prop="duration" sortable="custom" v-slot="{ row }">
              <template>
                <div>{{ row.duration | secondFormat }}</div>
              </template>
            </el-table-column>
            <el-table-column label="平均时长" prop="avg_duration" sortable="custom" v-slot="{ row }">
              <template>
                <div>{{ row.avg_duration | secondFormat1 }}</div>
              </template>
            </el-table-column>
            <!-- <el-table-column label="首电时长" prop="avg_duration" sortable="custom" v-slot="{ row }">
              <template>
                <div>{{ row.first_call | secondFormat1 }}</div>
              </template>
            </el-table-column>
            <el-table-column label="复电时长" prop="avg_duration" sortable="custom" v-slot="{ row }">
              <template>
                <div>{{ row.second_call | secondFormat1 }}</div>
              </template>
            </el-table-column> -->
            <el-table-column label="接通率" v-slot="{ row }" prop="on_call_percentage" sortable="custom">
              <template>
                <div>{{ (+row.on_call_percentage * 100).toFixed(0) }}%</div>
              </template>
            </el-table-column>
            <el-table-column label="添加时间" prop="created_at">
            </el-table-column>
          </el-table>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev,pager,next" :total="total"
            :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange"></el-pagination>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="show_select_dia" width="660px" title="选择成员">
      <memberListSingle v-if="show_select_dia" :list="memberList" :isOutbound="true" @onClickItem="selecetedMember">
      </memberListSingle>
    </el-dialog>
    <el-dialog :visible.sync="show_department" width="660px" title="选择部门">
      <obtaindepartment v-if="show_department" :list="memberList" @onClickItem="seleceteddepartment">
      </obtaindepartment>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import myLabel from '@/components/navMain/crm/components/my_label'
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
import obtaindepartment from "./obtaindepartment.vue"
export default {
  components: { myLabel, memberListSingle,obtaindepartment },

  data() {
    return {
      xiansuoStyle: {},
      bodaStyle: {},
      zuoxiStyle: {},
      botongStyle: {},
      weiboStyle: {},
      listdata:false,//控制列表数据按钮切换
      time_list: [
        { id: 1, name: "全部", value: "" },
        { id: 1, name: "今天", value: "today" },
        { id: 2, name: "昨天", value: "yesterday" },
        { id: 3, name: "本周", value: "this_week" },
        { id: 4, name: "上周", value: "last_week" },
        { id: 5, name: "本月", value: "this_month" },
        { id: 6, name: "上月", value: "last_month" },
      ],
      p_time: '',
      zuoxi_params: {
        times: "",
        admin_id: ''
        // start_time: 0,
        // end_time: 0
      },
      is_table_loading: false,
      tableData: [],
      total: 0,
      params: {
        page: 1,
        per_page: 10,
        sort: ''
      },
      topInfo: {
        "clueCount": 0,//线索总量
        "seats_count": 0,//坐席数量
        "call_count": 0,//拨打总量
        "unCallCount": 0,//未拨打数量
        "on_call_count": 0,//拨通总量
        on_call_percentage: 0, //接通率
        avg_duration: 0, //平均时长
      },
      leftDataY: ['坐席数量', '拨打总量', '拨通总量', '接通率', '平均时长'],
      leftDataX: [],
      middleInfo: {},
      left1DataY: ['小于30秒', '30s-1分钟', '1-2分钟', '2-5分钟', '5-10分钟', '10分钟以上'],
      left1Data: [],
      memberList: [],
      user_name: "",
      show_select_dia: false,
      is_small_sys: false,
      export_demand: false,
      admin_list:'',
      name_departlist:[
        {value:1,name :"成员"},
        {value:2,name :"部门"}
      ],
      show_department:false,
      name_depart:"",
    };
  },
  filters: {
    secondFormat(val) {
      let wholeTime = 0
      let sencond = parseInt(val) || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((sencond % 3600) % 60) % 60;
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
      return wholeTime
    },
    secondFormat1(val) {
      let arr = (val + '').split(".")
      let min_ = ''
      if (arr.length) {
        min_ = arr[arr.length - 1].substring(0, 2)
      }
      let wholeTime = 0
      let sencond = +val || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((((sencond % 3600) % 60) % 60)).toFixed(0);
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + (min_ > 0 ? ("." + min_) : '') + '"')
      return wholeTime
    }
  },
  created() {
    this.getTopInfo()
    this.getMiddleInfo()
    this.getBottomData()
    this.getExportDemand()
    this.xiansuoStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_red.png)`
    }
    this.zuoxiStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_orange.png)`
    }
    this.bodaStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_blue.png)`
    }
    this.botongStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_green.png)`
    }
    this.weiboStyle = {
      backgroundImage: `url(${this.$imageDomain}/static/admin/waihu/bg_red.png)`
    }
    let clientWidth = document.body.clientWidth
    if (clientWidth < 1366) {
      this.is_small_sys = true
    } else {
      this.is_small_sys = false
    }
    this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            console.log("true");
            if ((res.data + '').indexOf(this.admin_list.id) !== -1) {
               this.listdata = true
            } 
          }
        })
  },
  mounted() {
    window.addEventListener('resize', () => {
      console.log(123213);
      let clientWidth = document.body.clientWidth
      console.log(clientWidth);
      if (clientWidth < 1366) {
        this.is_small_sys = true
      } else {
        this.is_small_sys = false
      }
    });

  },
  methods: {
    initChartOne() {
      var myChart1 = echarts.init(document.querySelector(".sale-chart"));
      var textColorList = ['#ffac4c', '#4a78ff', '#1ac219', '#4a78ff', '#fe5958'];

      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',

          },
          formatter: (data) => {
            let index = data[0].dataIndex
            let unit = ''
            if (index == 3) {
              unit = "%"
            }
            if (index == 4) {
              unit = '"'
            }
            return data[0].name + ":" + data[0].value + unit
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          splitLine: {
            show: false,
          }
        },
        yAxis: {
          type: 'category',

          data: this.leftDataY
        },
        color: textColorList,
        series: [
          {
            type: 'bar',
            itemStyle: {
              normal: {
                color: function (params) {
                  // 给出颜色
                  return textColorList[params.dataIndex]
                },
              }
            },
            data: this.leftDataX
          },
        ],
      };
      myChart1.setOption(option);
      window.addEventListener("resize", function () {
        myChart1.resize();
      });
    },
    initChartTwo() {
      var myChart = echarts.init(document.querySelector(".pie-chart"));
      var textColorList = ["#6378FF", "#6C9BFF", "#54D89D", "#FFA338", '#FF8F86', '#FE5858'];

      var option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        color: textColorList,
        padding: [10, 100, 0, 0],
        series: [
          {
            name: '',
            type: 'pie',
            left: "0",
            top: 14,
            bottom: 30,

            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {   //视觉引导线
              show: true
            },
            data: this.left1Data,
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    getTopInfo() {

      this.$http.getOutboundIndexTopInfo(this.zuoxi_params).then(res => {
        if (res.status == 200) {
          this.topInfo = res.data
          let data = []
          // data[0] = this.topInfo.clueCount
          data[0] = this.topInfo.seats_count
          data[1] = this.topInfo.call_count
          data[2] = this.topInfo.on_call_count
          data[3] = +this.topInfo.on_call_percentage * 100
          data[4] = this.topInfo.avg_duration
          this.leftDataX = [...data]
          this.initChartOne()

        }
      })
    },
    getMiddleInfo() {
      this.$http.getOutboundIndexMiddleInfo(this.zuoxi_params).then(res => {

        if (res.status == 200) {

          let dataObj = { data1: { count: 0, duration: 0 }, data2: { count: 0, duration: 0 }, data3: { count: 0, duration: 0 }, data4: { count: 0, duration: 0 }, data5: { count: 0, duration: 0 }, data6: { count: 0, duration: 0 } }

          if (res.data) {
            this.middleInfo = res.data
            // data = Array.from(Object.values(res.data), x => x)
          }
          else {
            this.middleInfo = dataObj
          }

          let data = Array.from(Object.values(this.middleInfo), x => x)
          for (let index = 0; index < this.left1DataY.length; index++) {
            const ele = this.left1DataY[index];
            data[index].name = ele
            data[index].value = data[index].duration || 0
            // const ele = this.left1DataY[index];
            // ele.name =
          }
          this.left1Data = data
          this.initChartTwo()

        }
      })
    },
    exportExcel() {
      let params = Object.assign({}, this.params, this.zuoxi_params)
      this.$confirm('确认导出坐席列表吗？', '提示').then(() => {
        this.$http.exportSeatsStatic(params).then((res) => {
          if (res.status == 200) {
            window.open(res.data)
          } else {
            this.$message.error(res.message || '导出失败')
          }

        })
      })
    },
    outlist(){
      this.$goPath(`/crm_Follow_up_list`);
    },
    getBottomData() {
      let params = Object.assign({}, this.params, this.zuoxi_params)
      this.$http.getOutboundIndexBottomInfo(params).then(res => {
        if (res.status == 200) {
          this.tableData = res.data.data
          this.total = res.data.total
        }
      })
    },
    sortChange(column) {
      console.log(column);
      if (column.order == "descending") {
        this.params.sort = column.prop + '_desc'
      } else if (column.order == "ascending") {
        this.params.sort = column.prop + '_asc'
      } else {
        this.params.sort = ''
      }
      this.params.page = 1
      this.getBottomData()
    },
    onClickTime(e) {
      this.zuoxi_params.times = e.value
      this.getTopInfo()
      this.getMiddleInfo();
      this.getBottomData()
      // this.getMiddleInfo()
    },
    changeTimeRange(e) {
      if(e){
        if (e[1].endsWith("00:00:00")) {
        e[1] = e[1].slice(0, -8) + "23:59:59";
        }
      }
      if (e && e.length) {
        this.zuoxi_params.times = e.join(",")
      } else {
        this.zuoxi_params.times = ''
      }
      this.getTopInfo()
      this.getMiddleInfo();
      this.getBottomData()
      // this.getMiddleInfo()

    },
    onPageChange(e) {
      this.params.page = e;
      this.getBottomData();
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getOutboundDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    //
    focusdepart(){
      if(this.name_depart){
        this.name_depart = ''
        this.name_departlist=[
          {value:1,name :"成员"},
          {value:2,name :"部门"}
        ]
        this.zuoxi_params.department_id = "";
        this.zuoxi_params.admin_id = "";
        this.getTopInfo()
        this.getMiddleInfo();
        this.getBottomData()
      }
    },
    //切换检索成员或部门
    changemorb(){
      this.getDepartment()
      console.log(this.name_depart);
      if(this.name_depart==1){
        // this.showMemberList()
        this.show_select_dia = true;
      }else{
        // this.showdepartment()
        this.show_department = true
      }
    },
    // //成员检索
    // showMemberList() {
    //   this.show_select_dia = true;
    // },
    // //部门检索
    // showdepartment(){
    //   this.show_department = true
    // },
    delName() {
      this.zuoxi_params.admin_id = ''
      // this.params.wx_work_user_id = ''
      this.user_name = ''
      // this.zuoxi_params.page = 1;
      this.getTopInfo()
      this.getMiddleInfo();
      this.getBottomData()
    },
    selecetedMember(e) {
      if(this.name_depart==1){
        this.name_departlist[0].name = `成员(${e.checkedNodes[e.checkedNodes.length - 1].name})`
      }
      if (e.checkedNodes && e.checkedNodes.length) {
        // this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.zuoxi_params.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        this.zuoxi_params.department_id = "";
      } else {
        // this.user_name = "";
        this.zuoxi_params.admin_id = "";
        this.zuoxi_params.department_id = "";
      }
      this.getTopInfo()
      this.getMiddleInfo();
      this.getBottomData()
      this.show_select_dia = false;
    },
    //部门
    seleceteddepartment(e){
      if(this.name_depart==2){
        this.name_departlist[1].name = `部门(${e.checkedNodes[e.checkedNodes.length - 1].name})`
      }
      if (e.checkedNodes && e.checkedNodes.length) {
        // this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.zuoxi_params.department_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        this.zuoxi_params.admin_id = "";
      } else {
        // this.user_name = "";
        this.zuoxi_params.department_id = "";
        this.zuoxi_params.admin_id = "";
      }
      this.getTopInfo()
      this.getMiddleInfo();
      this.getBottomData()
      this.show_department = false;
    },
    async getExportDemand() {
      // console.log(123213);
      let has_roles = false
      let zhanzhangRes = await this.$http.getAdmin().catch(() => { })
      this.admin_list = zhanzhangRes.data
      if (zhanzhangRes.status == 200 && zhanzhangRes.data.roles && Array.isArray(zhanzhangRes.data.roles) && zhanzhangRes.data.roles.length && zhanzhangRes.data.roles[0].name == '站长') {
        has_roles = true
      this.listdata = true
      } else {
        let guanliyuanAdmin = await this.$http.getAuthShow('export_uid').catch(() => { })
        if (guanliyuanAdmin.status == 200) {
          console.log("false");
          if (guanliyuanAdmin.data) {
            // console.log(guanliyuanAdmin.data);
            // console.log(zhanzhangRes.data.id, guanliyuanAdmin.data.split(","), guanliyuanAdmin.data.split(",").includes(zhanzhangRes.data.id + ''));
            if (Array.from(String(zhanzhangRes.data)).join(',').includes(zhanzhangRes.data.id + '')) {
              has_roles = true
            }
          }
        }
      }
      this.export_demand = has_roles
    },
    searchTable() {
      this.params.page = 1
      this.getBottomData()
    }

  },
};
</script>
<style lang ="scss" scoped>
.items-center {
  align-items: center;
}

.bottom-border {
  ::v-deep .label-box .label-item {
    margin-right: 0;
    padding: 3px 12px;
  }
}

.listbnt {
  margin-left: 10px;
}
.info_dep{
  width: 145px;
  white-space: nowrap;      /* 强制不换行 */
  overflow: hidden;         /* 隐藏超出内容 */
  text-overflow: ellipsis;
  cursor: pointer;
  font-size: 13px;
  color: #8e9093;
}
.Explicitphone{
  width: 115px;
  white-space: nowrap;      /* 强制不换行 */
  overflow: hidden;         /* 隐藏超出内容 */
  text-overflow: ellipsis;
}
</style>
<style lang="scss">
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
  // min-width: 1390px;
  // overflow: auto;
}

.marbot28 {
  margin-bottom: 28px;
}

.bg_fff {
  background: #fff;
}

.box_top {
  padding: 20px;
}

.top_item {
  // background: #3333;
  padding: 30px;
  border-radius: 10px;
  height: 130px;
  background-repeat: no-repeat;
  box-sizing: border-box;
  background-size: 100% 100%;

  .top_item_left {
    .top_item_left_img {
      width: 28px;
      height: 28px;
      overflow: hidden;
      border-radius: 50%;
      align-self: flex-start;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .top_item_left_name {
      margin-left: 10px;
      font-size: 18px;
      color: #fff;
    }
  }

  .top_item_right {
    align-self: center;
    justify-content: flex-end;
    color: #fff;
    font-size: 48px;
    font-weight: 800;
  }
}

.echart {
  padding: 20px 10px;
}

.echart-right {
  min-width: 340px;
  width: 340px;

  .echart_l_con {
    padding: 10px;
    flex-wrap: wrap;
    box-sizing: border-box;

    .echart_l_con_item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 155px;
      padding: 14px 0;
      margin-right: 10px;
      border-radius: 10px;
      background: #f1f4fa;
      height: 90px;
      margin-bottom: 10px;

      &.echart_l_con_item_one {
        width: 320px;
        margin-right: 0;
        margin-bottom: 0;
      }

      &:nth-child(2n) {
        margin-right: 0;
      }

      .echart_l_con_item_top {
        margin-bottom: 10px;
        font-weight: 600;
        color: #2d84fb;
        font-size: 20px;
      }

      .echart_l_con_item_bottom {
        color: #2e3c4e;
        font-size: 16px;
      }
    }
  }
}

.mr10 {
  margin-right: 10px;
}

.table_title {
  font-weight: 600;
}

.table_search_inp {
  margin-left: 20px;
}

.small_inp {
  width: 100px;
}

.table_con {
  .table_search {
    margin-bottom: 10px;
  }
}

.el-col-4-8 {
  width: 20%;
}

.small {
  .el-col-4-8 {
    width: 25%;
  }

  .top_item {
    margin-bottom: 20px;
  }
}

.outbount_top_item {
  .task_name {
    color: #2e3c4e;
    font-size: 14px;
  }

  .select_name {
    width: 200px;
  }
}

.mem_info {
  .avatar {
    width: 40px;
    min-width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: #2d84fb;
    text-align: center;
    margin-right: 10px;

    span {
      text-align: center;
      line-height: 40px;
      color: #fff;
    }

    .img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      overflow: hidden;
      background: #2d84fb;

      img {
        width: 100%;
        object-fit: cover;
      }
    }
  }
}

.export_excel {
  margin-left: 10px;
}
</style>
