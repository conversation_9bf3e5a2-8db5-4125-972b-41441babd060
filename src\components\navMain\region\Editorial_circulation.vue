<template>
    <div>
        <div class="addbackground">
            <div class="Add_Content">
                <el-form ref="form" :model="circularize_data" label-width="80px">
                    <el-form-item label="标题">
                        <el-input v-model="circularize_data.title"></el-input>
                    </el-form-item>
                </el-form>

            </div>
            <div class="Add_Content_right">
                <el-form ref="form" :model="circularize_data" label-width="80px">
                    <el-form-item label="联系电话">
                        <el-input v-model="circularize_data.phone"></el-input>
                    </el-form-item>
                </el-form>
                <el-form ref="form" :model="circularize_data" label-width="80px">
                    <el-form-item label="开始时间">
                        <el-input v-model="circularize_data.start_time_show"></el-input>
                    </el-form-item>
                </el-form>
                <el-form ref="form" :model="circularize_data" label-width="80px">
                    <el-form-item label="结束时间">
                        <el-input v-model="circularize_data.end_time_show"></el-input>
                    </el-form-item>
                </el-form>
                <div class="disjunctor">
                    <div class="position">
                        <div class="tagging">红包开启</div>
                        <div>
                            <el-switch v-model="value1"></el-switch>
                        </div>
                    </div>
                    <div class="position">
                        <div class="tagging">是否禁用</div>
                        <div>
                            <el-switch v-model="value2"></el-switch>
                        </div>
                    </div>
                </div>
                <div class="editor">
                    <div style="border: 1px solid #ccc">
                        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                            :mode="mode" />
                        <Editor style="height: 380px; overflow-y: hidden" v-model="html" :defaultConfig="editorConfig"
                            :mode="mode" @onCreated="onCreated" />
                    </div>
                </div>
                <div class="background_music">
                    <span>背景音乐</span>
                    <div>
                        <el-upload class="upload-demo" action="/api/common/file/upload/admin?category=103"
                            :on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove" multiple
                            :limit="3" :on-exceed="handleExceed" accept=".mp4">
                            <div class="music_right">
                                <span>上传</span>
                            </div>
                        </el-upload>
                    </div>
                </div>
                <div class="add_Circulation">
                    <el-button type="primary" @click="modify">修改传阅</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
export default {
    components: { Editor, Toolbar },
    data() {
        return {
            html: '',
            value1: false,
            value2: false,
            editor: null,
            toolbarConfig: {},
            editorConfig: { placeholder: '请输入内容测试...' },
            mode: 'default', // or 'simple'
            packet_status: '',
            status: '',
            circularize_data: {}


        }
    },
    methods: {
        onCreated(editor) {
            this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
        },
        handleRemove(file, fileList) {
            console.log(file, fileList);
        },
        handlePreview(file) {
            console.log(file);
        },
        beforeRemove(file) {
            return this.$confirm(`确定移除 ${file.name}？`);
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        modify() {
            if (this.value1 == false) {
                this.circularize_data.red_packet_status = 0
            } else {
                this.circularize_data.red_packet_status = 1
            }
            if (this.value2 == false) {
                this.circularize_data.status = 0
            } else {
                this.circularize_data.status = 1
            }
            this.circularize_data.start_time = this.circularize_data.start_time_show
            this.circularize_data.end_time = this.circularize_data.end_time_show
            var re1 = new RegExp("<.+?>", "g")
            //执行替换成空字符
            var msg = this.html.replace(re1, '')
            this.html = msg
            this.circularize_data.content = this.html
            // console.log(this.circularize_data);
            // console.log(this.circularize_data.content);
            this.$http.Modify_circulation(this.circularize_data).then(res => {
                if (res.status == 200) {
                    this.$message({
                        type: 'success',
                        message: '修改成功!'
                    });
                    this.$goPath("perusal");
                }
            })
        },
        //获取信息
        details_circulation() {
            this.$http.details_circulation(this.$route.query.id).then(res => {
                console.log(res);
                if (res.status == 200) {
                    if (res.data.red_packet_status == 0) {
                        this.value1 = false
                    } else {
                        this.value1 = true
                    }
                    if (res.data.status == 0) {
                        this.value2 = false
                    } else {
                        this.value2 = true
                    }
                    this.html = res.data.content
                    this.circularize_data = res.data
                }
            })
        }
    },
    mounted() {
        this.details_circulation()
        //    console.log(this.$route.query.id); 
    }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style scoped lang="scss">
.addbackground {
    width: 95%;
    height: 1200px;
    // background-color: pink;
    margin: 30px auto;
    // display: flex;
    // justify-content: space-between;

    .Add_Content {
        width: 900px;
        height: 50px;
        // background-color: palegoldenrod;

        /deep/.el-input__inner {
            width: 300px;
            margin-left: 28px;
        }

        /deep/.el-form-item__label {
            font-size: 17px;
            color: #000;
        }

        .editor {
            width: 800px;
            height: 380px;
            // background-color: cornflowerblue;
            margin: 40px auto;
            margin-left: 30px;
        }
    }

    .Add_Content_right {
        width: 600px;
        height: 1200px;
        margin-left: 30px;
        margin-top: 20px;

        // background-color: palevioletred;
        .disjunctor {
            width: 500px;
            height: 70px;
            // background-color: palegreen;
            display: flex;
            justify-content: space-between;
        }

        .position {
            width: 150px;
            height: 50px;
            // background-color: plum;
            // margin-left: 32px;
            display: flex;
            justify-content: space-between;
        }

        /deep/.el-input__inner {
            width: 300px;
            // margin-left: 30px;
        }

        /deep/.el-form-item__label {
            font-size: 16px;
            color: #000;
        }

        .background_music {
            width: 900px;
            height: 130px;
            // border: 1px solid #fff;
            // border-bottom-color: #dde1e9;
            //   background-color: #fe6c17;
            margin: 0 auto;
            margin-top: 100px;

            div {
                display: flex;

            }

            .music_right {
                width: 90px;
                height: 80px;
                background-color: #F1F4FA;
                margin-top: 10px;
                margin-left: 10px;

                span {
                    margin: 25px auto;
                }
            }
        }

        .add_Circulation {
            width: 600px;
            height: 40px;
            // background-color: paleturquoise;
            display: flex;
            justify-content: flex-end;
        }
    }



}
</style>