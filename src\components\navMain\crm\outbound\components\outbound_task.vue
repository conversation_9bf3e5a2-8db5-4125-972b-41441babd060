<template>
  <div class="table">
    <div class="table_oper flex-row align-center">
      <div class="table_oper_item flex-row align-center flex-1">
        <div class="mr10">
          <el-radio v-model="all" :label="1" border @change="changeRadioAll">全部</el-radio>
          <el-checkbox border v-model="task_params.is_call" :false-label="0" :true-label="1" @change="changeCheckbox">{{
                      task_params.is_call === ""
                      ? "拨打状态"
                      : task_params.is_call
                      ? "已拨打"
                      : "未拨打"
                      }}</el-checkbox>
          <el-checkbox border v-model="task_params.is_on" :false-label="0" :true-label="1" @change="changeCheckbox">{{
                      task_params.is_on === ""
                      ? "接通状态"
                      : task_params.is_on
                      ? "已接通"
                      : "未接通"
                      }}</el-checkbox>
        </div>
      </div>
      <div class="table_oper_item flex-row align-center">
        <!-- <el-button size="small" type="primary">重新分配</el-button>
        <el-button size="small" type="primary">转到客户</el-button> -->
        <el-popconfirm title="确定导出吗？" @onConfirm="exportExcel">
          <el-button slot="reference" size="small" type="primary">导出</el-button>
        </el-popconfirm>
      </div>
    </div>
    <el-table v-loading="task_table_loading" :data="task_tableData" border :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row :row-style="$TableRowStyle">
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column label="ID" width="70" prop="id"></el-table-column>
      <el-table-column label="线索名称" prop="name"> </el-table-column>
      <el-table-column label="号码" prop="phone"> </el-table-column>
      <el-table-column label="通话时长" prop="duration"> </el-table-column>

      <el-table-column label="拨打状态" v-slot="{ row }">
        <el-tag :type="row.is_call == 1 ? 'success' : 'warning'">{{
                  row.is_call == 1 ? "已拨打" : "未拨打"
                  }}</el-tag>
      </el-table-column>
      <el-table-column label="接通状态" v-slot="{ row }">
        <el-tag :type="row.is_on == 1 ? 'success' : 'warning'">{{
                  row.is_on == 1 ? "已接通" : "未接通"
                  }}</el-tag>
      </el-table-column>
      <el-table-column label="拨打时间" prop="call_time"> </el-table-column>

      <el-table-column label="操作" v-slot="{ row }">
        <!-- v-slot="{ row }" -->
        <el-link slot="reference" type="success" class="mr10" @click="makeTel(row)">拨打电话</el-link>
        <el-link type="primary" @click="telListInfo(row)" style="margin-right: 10px;">详情</el-link>
        <el-link type="primary" @click="clickCustomer(row)">跟进</el-link>
      </el-table-column>
    </el-table>
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev,pager,next" :total="taskTotal"
      :page-size="task_params.per_page" :current-page="task_params.page"
      @current-change="onTaskPageChange"></el-pagination>

    <el-dialog width="420px" title="智能手机" custom-class="dialog" :visible.sync="make_phone" @close="PhoneClose"
      style="margin-top: 15vh;">
      <div class="maKe_phone_call">
        <template v-if="step == 1">
          <div class="maKe_phone_title">选择外显号码</div>
          <el-select v-model="selected_phone" clearable>
            <el-option v-for="item in outShowPhoneList" :key="item.show_id" :label="item.phone" :value="item.show_id">
            </el-option>
          </el-select>
          <div class="flex-row align-center submit_make_phone">
            <el-button class="flex-1" type="primary" @click="ConfirmTel()">
              确认拨打</el-button>
          </div>
        </template>
        <template v-if="step == 2 || step == 3">
          <div v-if="call_route != 2 && call_route != 4 && call_route != 8">
            <div class="avatar">
              <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
            </div>
            <div class="telphone">{{ current_phone_num }}</div>
            <div class="area">归属地</div>
            <div class="telphone waiting blue strong">
              {{ step == 2 ? "运营商转接等待中..." : "运营商转接成功" }}
            </div>
            <div class="link_step">
              <div class="link_step_title strong">通话步骤</div>
              <div class="link_step_con flex-row j-between">
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img
                      src="https://img.tfcs.cn/backup/static/admin/waihu/zhuanjiechenggong.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">线路转接成功</div>
                </div>
                <div class="join_line">>>></div>
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img src="https://img.tfcs.cn/backup/static/admin/waihu/laidian.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">手机来电</div>
                </div>
                <div class="join_line">>>></div>
                <div class="link_step_left">
                  <div class="link_step_left_top">
                    <img src="https://img.tfcs.cn/backup/static/admin/waihu/jieting.png?x-oss-process=style/w_80"
                      alt="" />
                  </div>
                  <div class="link_step_left_bottom">客户接听通话</div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="call_route == 2 || call_route == 4">
            <div v-if="callRouteShow">
              <!-- 文本 -->
              <!-- <div class="success_left">
                一个被叫号每天只能拨打五次如果无法拨通尝试更换主叫
              </div> -->
              <div class="zhujihaoall">
                <!-- 主机号 -->
                <div class="zhujibohao">
                  请使用
                  <span class="zhujibohao_number">{{ callers }}</span>
                  拨打
                </div>
                <!-- 隐私号 -->
                <div class="yinshi">
                  <div style="color: #FF1607; font-size: 24px;">
                    {{ phoneNumber }}
                  </div>
                  <div class="yinshi_number">
                    隐私号码
                  </div>
                </div>
                <!-- 倒计时 -->
                <div class="tiem">
                  <div class="tiem_image">
                    <img src="../../../../../assets/tiem (1).png" alt="" class="tiem_image_img">
                  </div>
                  <div style="color: rgba(46, 60, 78, 0.70); font-size: 12px;margin-left: 4px;">隐私号码
                    <span style="color: #FF3D3D; font-size: 12px;">{{ convertedTime }}s</span>
                    后失效
                  </div>
                </div>

              </div>
              <!-- 扫码 -->
              <!-- <div class="success_right"> -->
              <div class="tiem_images">
                <img src="../../../../../assets/tiem (2).png" alt="" class="tiem_image_imgs">
              </div>

              <!-- <el-popover
    placement="top-start"
    title="标题"
    width="200"
    trigger="hover"
    content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
    <div style="color: #FFF; font-size: 14px;margin-left: 12px;margin-bottom: 1px;">
        扫码拨打
       </div> -->
              <el-popover placement="top-start" width="212" trigger="hover">
                <div style="margin-top:10px;">
                  <el-alert title="请使用手机自带的扫一扫,不能使用微信,支付宝!!!" type="warning" show-icon :closable="false">
                  </el-alert>
                </div>
                <div id="qrcode" class="qrcode" ref="qrCodeUrl"></div>
                <el-button slot="reference" class='button'>扫码拨打</el-button>
              </el-popover>
            </div>
          </div>
          <!-- <div class="to_back" @click="goBack">
            <div class="to_back_img">
              <img
                src="https://img.tfcs.cn/backup/static/admin/waihu/back_red.png?x-oss-process=style/w_80"
                alt=""
              />
            </div>
            <div class="strong to_back_name">返回</div>
          </div> -->
        </template>
      </div>
    </el-dialog>
    <el-dialog title="拨打详情" width="800px" :visible.sync="showTelInfo">
      <telListInfo :id="currentTask.id"> </telListInfo>
    </el-dialog>
    <inputCustomer v-if="is_push_customer" @handleShow="handleShow" :cluePhone="cluePhone" :clueName="clueName">
    </inputCustomer>
  </div>
</template>

<script>
import telListInfo from "./telListInfo"
import QRCode from "qrcodejs2";
import inputCustomer from "./InputCustomer.vue"
export default {
  components: {
    telListInfo,
    inputCustomer
  },
  props: {
    current: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      callRouteShow:false,
      is_push_customer: false,
      sub_loading: false,
      task_params: {
        page: 1,
        per_page: 10,
        is_on: '',
        member_task_id: '',
        is_call: ""
      },
      taskTotal: 0,
      task_tableData: [],
      curr: {},
      task_table_loading: false,
      make_phone: false,
      step: 1,
      selected_phone: '', //选中的外显号码id
      MakeCallID: 0, // 存储线索拨打id
      outShowPhoneList: [], //外显号码列表
      all: 1,
      showTelInfo: false,
      currentTask: {},
      current_phone_num: '',
      cluePhone: "", // 线索包号码
      clueName: "", // 线索包名称
      call_route: '',
      phoneNumber: '',// 小号
      callers: '',// 主号
      tiemexpires: 0,//倒计时
      convertedTime: 0,//处理后的时间
      intervalId: null,// 计时器
    }
  },
  watch: {
    current: {
      handler(val) {
        if (val) {
          this.curr = val
          if (val.id) {
            this.getTask()
          } else {
            this.task_tableData = []
          }

        } else {
          this.task_tableData = []
        }
      }
    }
  },
  methods: {
    // 子组件传递的参数
    handleShow(val) {
      this.is_push_customer = val
    },
    // 点击跟进
    clickCustomer(row) {
      if (row.crm_id != '') {
        let url = `/crm_customer_detail?id=${row.crm_id}&type=my`;
        this.$goPath(url);
      }
      else {
        this.is_push_customer = true
        this.cluePhone = row.phone
        this.clueName = row.name
      }
    },
    // 获取坐席列表
    getTask() {
      this.task_table_loading = true
      this.task_params.member_task_id = this.curr.id
      this.$http.getMyOutboundTaskList(this.task_params).then(res => {
        if (res.status == 200) {
          this.task_tableData = res.data.data
          this.taskTotal = res.data.total
        }
        this.task_table_loading = false
      }).catch(() => {
        this.task_table_loading = false
      })
    },
    // 坐席列表页面更新
    onTaskPageChange(val) {
      this.task_params.page = val
      this.getTask()
    },
    // makePhone() {
    //   this.step = 2
    //   this.getShowTelNumber()
    //   this.make_phone = true
    // },
    // getShowTelNumber() {
    //   this.$http.getShowTelNumber({
    //     status: 1,
    //     is_disable: 0,
    //     per_page: 1000
    //   }).then(res => {
    //     if (res.status == 200) {
    //       this.outShowPhoneList = res.data.data
    //     }
    //     // this.telNumberLoading = false
    //   }).catch(() => {
    //     // this.telNumberLoading = false
    //   })
    // },
    goBack() {
      // if (this.step > 1) {
      //   this.step--
      // } else {
      this.make_phone = false
      // }
    },
    makeTel(row) {
      console.log(row)
      this.MakeCallID = row.id
      this.step = 1
      this.current_phone_num = row.phone
      this.make_phone = true
      this.$http.getExplicitNumber().then(res => {
        if (res.status == 200) {
          console.log(res.data, '9999999000000');
          this.outShowPhoneList = res.data
        }
      })
    },
    ConfirmTel() {
      this.makePhone(this.MakeCallID)
    },
    makePhone(id) {
      this.$http.callOutboundNum(id, { show_id: this.selected_phone }).then(res => {
        if (res.status == 200) {
          this.make_phone = true
          this.callRouteShow = true
          console.log(res, '返回字段');
          this.step = 2
          this.callers = res.data.caller
          this.call_route = res.data.call_route
          this.tiemexpires = res.data.expires
          this. convertedTime =  this.tiemexpires / 2
          // 倒计时处理
      this.intervalId = setInterval(() => {
        this.step = 3;
        if (this. convertedTime > 1) {
          this. convertedTime--;
        } else {
          clearInterval(this.intervalId);
          this.make_phone = false

        }
      }, 1000);
// 显示二维码
          if (res.data.call_route == 2 || res.data.call_route == 4|| res.data.call_route == 8) {
            var that = this
            that.phoneNumber = res.data.telX
            console.log(that.$refs.qrCodeUrl,'this.$refs.qrCodeUrl');
        setTimeout(() => {
                  new QRCode(that.$refs.qrCodeUrl, {
                  text: 'tel:'+ that. phoneNumber, // 需要转换为二维码的内容
                  width: 170,
                  height: 170,
                  colorDark: '#000000',
                  colorLight: '#ffffff' ,
                  correctLevel: QRCode.CorrectLevel.H
              })
               }, 0)

      
          }else{
              setTimeout(() => {
              this.step = 3;
              setTimeout(() => {
                that.make_phone = false
              }, 1000);
            }, 30000);
          }
        }
      })
    },
    changeCheckbox() {
      this.task_params.page = 1
      this.all = ''
      this.getTask()
    },
    changeRadioAll() {
      this.task_params.page = 1
      this.task_params.is_on = ''
      this.task_params.is_call = ''
      this.getTask()
    },
    // 弹框拨打电话详情
    telListInfo(row) {
 
      this.currentTask = row
      this.showTelInfo = true
    },
    exportExcel() {
      this.task_params.member_task_id = this.curr.id
      this.$http.exportTelTask(this.task_params).then(res => {
        console.log(res);
        if (res.status == 200) {
          window.open(res.data)
        }
      })
    },
    PhoneClose() {
      this.selected_phone = "";
      this.outShowPhoneList = [];
      clearInterval(this.intervalId);
          this.make_phone = false
          this.callRouteShow = false
    }

  }
}
</script>

<style lang ="scss" scoped>
::v-deep .button {
  color: #ffff;
  border-color: #12D367;
  background-color: #12D367;
  width: 100%;
}

::v-deep .el-button:active .button {
  background-color: #12D367;
  border-color: #12D367;
  color: #ffff;
}

::v-deep .el-button .button {
  position: relative;
  background: #12D367;
  border: 1px solid #12D367;
  width: 100%;
  color: #fff;
}

.zhujihaoall {
  width: 100%;
  text-align: center;
}

.success_right {
  // width: 92px;
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  background: #12D367;
}

.success_left {
  padding: 8px 10px;
  // width: 70%;
  color: #12D367;
  font-size: 12px;
  border-radius: 4px;
  // text-align: left;s
  border: 1px solid #12D367;
  background: rgba(18, 211, 103, 0.10);
  // margin-right: 12px;
}

.yinshi_number {
  color: rgba(46, 60, 78, 0.40);
  font-size: 12px;
  padding: 4px;
  border-radius: 4px;
  line-height: 22px;
  background: #F1F4FA;
  margin-left: 8px;

}

.tiem_image_img {
  width: 100%;
  height: 100%;
}

.tiem_image_imgs {
  width: 100%;
  height: 100%;
  margin: 0 4px;
}

.tiem_images {
  position: absolute;
  left: 10px;
  z-index: 999;
  top: 212px;
  width: 16px;
  height: 16px;
  margin-left: 145px;
}

.tiem_image {
  width: 16px;
  height: 16px;
}

.tiem {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  margin-bottom: 24px;
  justify-content: center;
}

.yinshi {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: 8px;
  justify-content: center;
}

.zhujibohao_number {
  color: #2D84FB;
  font-size: 14px;
}

.zhujibohao {
  color: rgba(46, 60, 78, 0.70);
  font-size: 14px;
  // text-align: left;
  margin-bottom: 24px;
  margin-top: 24px;
}

.qrcode {
  width: 200px;
  height: 200px;
  text-align: center;
  margin-left: 20px;
  margin-top: 40px;
}

::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;

  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }

  .el-dialog__body {
    padding: 0 12px;
  }
}

.mr10 {
  margin-right: 10px;
}

.strong {
  font-weight: 600;
}

.blue {
  color: #2d84fb;
}

.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}

.maKe_phone_call {
  // height: 500px;
  text-align: center;
  background: #fff;
  padding: 20px 20px;
  margin-bottom: 10px;
  box-sizing: border-box;

  .maKe_phone_title {
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
  }

  .submit_make_phone {
    margin-top: 65px;
  }

  .avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 50%;

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .telphone {
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
    margin-top: 16px;
  }

  .area {
    color: #8a929f;
    font-size: 14px;
    margin-top: 16px;
  }

  .waiting {
    margin: 15px 0 20px;
  }

  .link_step {
    border-radius: 13px;
    background: #f1f4fa;
    padding: 10px 16px;

    .link_step_title {
      color: #2e3c4e;
      font-size: 14px;
      margin-bottom: 15px;
      white-space: nowrap;
    }

    .link_step_con {
      .link_step_left {
        width: 48px;

        .link_step_left_bottom {
          color: #2e3c4e;
          font-size: 12px;
          white-space: nowrap;
          margin-left: -10px;
        }
      }

      .join_line {
        margin-top: 5px;
        color: #2d84fb;
      }
    }
  }

  .to_back {
    margin-top: 65px;

    .to_back_img {
      width: 50px;
      height: 50px;
      margin: 0 auto 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .to_back_name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }

  .el-select {
    width: 100%;
  }
}
</style>