<template>
  <div style="max-height: 70vh; overflow-y: auto">
    <el-alert
      style="margin: 0 0 10px"
      title="接待人员需要绑定互通账号"
      type="warning"
    >
    </el-alert>
    <el-form label-width="120px">
      <div class="title">基本信息</div>
      <el-form-item label="客服名称">
        <div class="form-item-block">
          <el-input v-model="form_params.name" placeholder="请输入客服名称">
          </el-input>

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="头像上传类型">
        <div class="form-item-block">
          <el-select v-model="prelogo_type">
            <el-option label="图库选择" value="1"> </el-option>
            <el-option label="本地上传" value="2"> </el-option>
          </el-select>

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="客服头像" v-if="prelogo_type == 2">
        <div class="form-item-block">
          <el-upload
            :headers="myHeader"
            :action="website_img"
            :on-success="handleSuccess"
            list-type="picture-card"
            :show-file-list="false"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
          >
            <img
              v-if="form_params.avatar_url"
              :src="form_params.avatar_url"
              class="avatar"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>

          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="客服头像" v-if="prelogo_type == 1">
        <div class="form-item-block">
          <img
            v-if="form_params.avatar_url"
            :src="form_params.avatar_url"
            class="avatar"
            @click="toSelect"
          />
          <div class="to_select" v-else @click="toSelect">选择头像</div>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <!-- <el-form-item label="接待方式">
        <el-radio-group v-model="form_params.syn_wx" size="mini">
          <el-radio :label="1" border>人工接待</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <div class="line"></div>
      <div class="title padt20">欢迎语设置</div>
      <el-form-item label="欢迎语">
        <div class="form-item-block line_height1">
          <el-switch v-model="form_params.open_welcome" active-color="#2D84FB">
          </el-switch>
          <p class="tip">开启后，客户打开会话后将自动发送欢迎语引导客户咨询</p>
        </div>
      </el-form-item>
      <el-form-item label="消息内容">
        <div class="form-item-block">
          <el-input
            v-model="form_params.greeting"
            placeholder="请输入消息内容"
            type="textarea"
            :rows="6"
            maxlength="500"
            show-word-limit
          ></el-input>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <div class="line"></div>
      <div class="title padt20">接待人员设置</div>
      <el-form-item label="设置接待人员">
        <div class="form-item-block form_kefu flex-row f-wrap">
          <el-button
            size="big"
            class="member_button"
            v-for="item in form_selectedMember"
            :key="item.id"
            >{{ item.name }}</el-button
          >
          <el-button size="big" class="el-icon-plus" @click="showAddMember"
            >添加成员</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="是否作席">
        <div class="form-item-block line_height1">
          <el-switch v-model="form_params.is_online" active-color="#2D84FB">
          </el-switch>
        </div>
      </el-form-item>
      <div class="title padt20">留资设置</div>
      <el-form-item label="线索推送状态">
        <el-radio-group v-model="form_params.is_get_mobile" size="mini">
          <el-radio :label="1" border>开启</el-radio>
          <el-radio :label="0" border>关闭</el-radio>
        </el-radio-group>
        <p class="tip">
          开启后通过客服会话回复的11位手机号码将自动流入CRM客户公海客户线索。
        </p>
      </el-form-item>
      <!-- <el-form-item label="超时回复提醒：">
        <div class="form-item-block line_height1">
          <el-switch
            v-model="form_params.openchaoshi"
            :active-text="inactiveText"
            active-color="#2D84FB"
            @change="changChaoshi"
          >
          </el-switch>
          <p class="tip">开启后，客户打开会话后将自动发送欢迎语引导客户咨询</p>
          <p class="tips flex-row">
            <span>超时回复提醒：</span
            ><el-input
              v-model="form_params.chaoshi"
              style="width: 50px"
              size="mini"
            ></el-input>
            <span>分钟，未回复时，将发送消息提醒跟进</span>
          </p>
        </div>
      </el-form-item> -->
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <el-dialog
      :visible.sync="show_select_dia"
      width="600px"
      title="选择头像"
      append-to-body
    >
      <div class="imgLists">
        <div class="img_list flex-row" @scroll="handleScroll">
          <div
            class="img"
            v-for="(item, index) in imgList"
            :key="index"
            :class="{ active: currentImg == item.url }"
            @click="selectAvatar(item)"
          >
            <img :src="item.url" alt="" />
            <div class="checked">
              <img src="@/assets/select.png" alt="" />
            </div>
          </div>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_select_dia = false"
            >取消</el-button
          >
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="show_add_member"
      width="600px"
      title="选择成员"
      append-to-body
      :before-close="closeSelectMember"
    >
      <div class="member">
        <div class="member_box flex-row">
          <div class="left member_con flex-1">
            <memberList
              v-if="show_add_member"
              :list="memberList"
              source="renshi"
              :defaultValue="selectedIds"
              @onClickItem="selecetedMember"
              ref="memberList"
            >
            </memberList>
            <!-- @setchecked="setChecked"-->
          </div>
          <div class="right member_con flex-1">
            <div class="select_title">已选择成员</div>
            <div class="selected_list">
              <div
                class="selected_item flex-row align-center"
                v-for="item in selectedList"
                :key="item.id"
              >
                <!-- <div class="prelogo">
                    <img :src="item.prelogo" alt="">
                  </div> -->
                <div class="name flex-1">{{ item.name }}</div>
                <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="footer flex-row align-center">
          <el-button type="text" @click="closeSelectMember">取消</el-button>
          <el-button
            type="primary"
            @click="selectMemberOk"
            :loading="isSubmiting"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config";
import memberList from "../memberList";
export default {
  props: [],
  components: {
    memberList,
  },
  data() {
    return {
      form_params: {
        avatar: "",
        avatar_url: "",
        greeting: "",
        open_welcome: true,
        admin_user_id: "",
        is_online: true,
        name: "",
        is_get_mobile: "0"
      },
      website_img: `/api/common/file/upload/admin?category=${config.WEBSITE_IMG}`,
      dialogVisible: false,
      prelogo_type: "1",
      show_select_dia: false,
      dialogImageUrl: "",
      inactiveText: "未开启",
      show_add_member: false,
      memberList: [],
      selectedList: [],
      selectedIds: [],
      imgList: [],
      currentImg: "",
      form_selectedMember: [],
      isSubmiting: false,
      img_params: {
        page: 1,
        per_page: 20,
        type: 1,
      },
      form_selectedMemberBack: [],
    };
  },
  computed: {
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
  },
  created() {
    this.form_params = {
      avatar: "",
      avatar_url: "",
      greeting: "",
      open_welcome: true,
      admin_user_id: "",
      is_online: true,
      name: "",
      is_get_mobile: 0
    };
    this.getDepartment();
    this.getImgList();
  },

  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      if (params.is_online) {
        params.is_online = 1;
      } else {
        params.is_online = 0;
      }
      if (params.open_welcome) {
        params.open_welcome = 1;
      } else {
        params.open_welcome = 0;
      }
      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .addCrmService(params)
        .then((res) => {
          if (res.status == 200) {
            this.$message.success("添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error("添加失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    cancel() {
      this.$emit("cancel");
    },
    handleRemove() {
      this.form_params.img = "";
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      this.$http.getCrmServiceAvatarList(this.img_params).then((res) => {
        if (res.status == 200) {
          this.imgList = this.imgList.concat(res.data.data);
          if (
            res.data.data &&
            res.data.data.length == this.img_params.per_page
          ) {
            this.loadMore = true;
          } else {
            this.loadMore = false;
          }
        }
      });
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response.url;
      this.dialogVisible = true;
    },
    // 图片上传 1 => 'image', 2 => 'voice', 3 => 'video', 4 => 'file'"
    handleSuccess(response) {
      this.form_params.avatar_url = response.url;
      let params = { url: response.url, type: 1 };
      this.$http.getAvatarId(params).then((res) => {
        if (res.status == 200) {
          this.form_params.avatar = res.data.media_id;
        }
      });
    },
    // 选择头像
    toSelect() {
      this.show_select_dia = true;
    },
    // 选中头像
    selectAvatar(e) {
      this.currentImg = e.url;
      // this.imgList.map(item => item.checked = false)
      // e.checked = true
    },
    // 选中头像确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      this.form_params.avatar = current.media_id;
      this.form_params.avatar_url = current.url;
      this.show_select_dia = false;
    },
    newsetArr(arr, key = "id") {
      var result = [];
      var obj = {};
      for (var i = 0; i < arr.length; i++) {
        if (!obj[arr[i][key]]) {
          result.push(arr[i]);
          obj[arr[i][key]] = true;
        }
      }
      console.log(result);
      return result;
    },
    // 移除选中的人员
    deleteSelected(e) {
      let idx = this.selectedList.findIndex((item) => item.id == e.id);
      this.selectedList.splice(idx, 1);
      this.selectedIds.splice(idx, 1);

      setTimeout(() => {
        // this.$refs.memberList.changeSelected([e.id], false);
        this.$refs.memberList.changeSelected(this.selectedIds, true);
      }, 100);
    },
    // 超时按钮切换
    changChaoshi(e) {
      if (!e) {
        this.inactiveText = "未开启";
      } else {
        this.inactiveText = "已开启";
      }
    },
    // 显示选择会员弹框
    showAddMember() {
      this.show_add_member = true;
    },
    setChecked() {
      this.$nextTick(() => {
        this.$refs.memberList.changeSelected(this.selectedIds, true);
      });
    },
    // 选中会员
    selecetedMember(e) {
      // this.selectedIds = [...new Set(e.checkedKeys)]
      // if (e.checkedNodes.length) {
      //   let nodesIds = [];
      //   e.checkedNodes.map((item) => {
      //     nodesIds.push(item.id);
      //   });
      //   this.form_selectedMemberBack.map((item) => {
      //     if (
      //       this.selectedIds.includes(item.id) &&
      //       !nodesIds.includes(item.id)
      //     ) {
      //       e.checkedNodes.push(item);
      //     }
      //   });
      // } else {
      //   this.form_selectedMemberBack.map((item) => {
      //     if (this.selectedIds.includes(item.id)) {
      //       e.checkedNodes.push(item);
      //     }
      //   });
      // }

      // this.selectedList = this.newsetArr(e.checkedNodes);
      // this.selectedIds = [];
      // this.selectedList.map((item) => {
      //   this.selectedIds.push(item.id);
      // });
      // this.$forceUpdate();

      this.selectedIds = e.checkedKeys
      this.selectedList = e.checkedNodes
    },
    closeSelectMember() {
      this.selectedList = this.form_selectedMember;
      this.selectedIds = [];
      this.selectedList.map((item) => {
        this.selectedIds.push(item.id);
      });
      this.show_add_member = false;
    },

    selectMemberOk() {

      this.form_params.admin_user_id = [...new Set(this.selectedIds)]
      this.form_selectedMember = this.newsetArr(this.selectedList);
      if (this.form_params.admin_user_id.length > 0) {
        this.form_params.admin_user_id = this.form_params.admin_user_id.join(",");
      } else {
        this.form_params.admin_user_id = ''
      }
      // this.form_params.admin_user_id = this.selectedIds.join(",");
      // this.form_selectedMember = this.selectedList;
      this.form_selectedMemberBack = this.selectedList;
      this.show_add_member = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}
.el-input,
.el-select,
.el-textarea {
  width: 260px;
}
.title {
  padding: 0 28px 28px 0;
  font-size: 16px;
  color: #2e3c4e;
  font-weight: 600;
  &.padt20 {
    padding-top: 20px;
  }
}
.line {
  height: 1px;
  width: 100%;
  background: #d6d6d6;
}
.form-item-block {
  &.form_kefu {
    .el-button {
      margin-bottom: 5px;
    }
  }
  .avatar {
    width: 150px;
    height: 150px;
    object-fit: cover;
  }
  .to_select {
    display: inline-block;
    width: 60px;
    text-align: center;
    cursor: pointer;
    line-height: 1;
    padding: 7px 5px;
    border: 1px solid #409eff;
    color: #409eff;
    border-radius: 5px;
  }
  .tip {
    color: #8a929f;
    font-size: 12px;
    line-height: 1;
  }
  .tips {
    margin-top: 10px;
  }
}

.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;
  &::-webkit-scrollbar {
    width: 0;
  }
  .img {
    width: 112px;
    height: 112px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 5px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.member {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
