<template>
    <div class="container">
        <div class="container-box">
            <div class="container-title">话术设置</div>
            <div class="search-cloumn flex-row">
                <div class="search-box">
                    <el-input v-model="search_text" placeholder="搜索话术"></el-input>
                </div>
                <div class="button-box1">
                    <el-button type="primary" @click="comfirmSearch">查询</el-button>
                </div>
                <div class="button-box2">
                    <el-button type="info" @click="comfirmReset">重置</el-button>
                </div>
                <div class="button-box1">
                    <el-button 
                        type="primary"
                        @click="addVerbalDialog"
                    >添加话术</el-button>
                </div>
                <div class="button-box1">
                    <el-button 
                        type="primary"
                        @click="classifyDialog"
                    >话术分类</el-button>
                </div>
            </div>
            <div class="table-box">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    :header-cell-style="{background:'#EBF0F7', borderColor: '#DDE1E9', color: '#2E3C4E'}"
                    :cell-style="{borderColor:'#DDE1E9', fontSize: '14px', color: '#2E3C4E'}"
                    row-key="id"
                    :expand-row-keys="expands"
                >
                    <el-table-column
                        prop="id"
                        label="ID"
                        align="center"
                        width="100px"
                    >
                        <template v-slot="{row}">
                            <span style="color: #2D84FB;" v-if="row.id < 10">{{ '0' + row.id }}</span>
                            <span style="color: #2D84FB;" v-else>{{ row.id }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="text"
                        label="话术内容"
                        align="center"
                        width="270px"
                        show-overflow-tooltip
                    >
                        <template v-slot="{row}">
                            <span class="often_style">{{ row.text }}</span>
                            <!-- <el-tooltip class="item" effect="dark" :content="row.text" placement="top">
                                <span class="often_style">{{ row.text }}</span>
                            </el-tooltip> -->
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="cate_name"
                        label="分类名称"
                        align="center"
                        width="150px"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="created_at"
                        label="添加时间"
                        align="center"
                        width="260px"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="like_text"
                        label="关键字"
                        align="center"
                    >
                        <template v-slot="{row}">
                            <div class="flex-row" style="flex-wrap: wrap;">
                                <span 
                                    v-for="(item, index) in row.keywords"
                                    :key="index"
                                    class="resemble_style"
                                >
                                    {{ item }}
                                </span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="footer">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="search_params.page"
                    layout="total, prev, pager, next"
                    :total="total"
                    background>
                </el-pagination>
            </div>
        </div>
        <el-dialog
            title="添加话术"
            width="500px"
            :visible.sync="addVerbal_dialog"
            :close-on-click-modal="false"
        >
            <div>
                <el-form ref="verbal" :model="addVerbal_params" label-width="80px">
                    <el-form-item label="分类名称" prop="cate_id">
                        <el-select 
                            ref="classify"
                            style="width: 290px;" 
                            v-model="addVerbal_params.cate_id" 
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in verbal_type_list"
                                :key="item.id"
                                :label="item.cate_name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="话术内容" prop="text">
                        <el-input style="width: 290px;" v-model="addVerbal_params.text" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="关键字" prop="keywords">
                        <el-tag
                            :key="tag"
                            v-for="tag in addVerbal_params.keywords"
                            closable
                            :disable-transitions="false"
                            @close="handleTagClose(tag)">
                            {{tag}}
                        </el-tag>
                        <el-input
                            class="input-new-tag"
                            :style="addVerbal_params.keywords.length ? '': 'margin-left: 0px;'"
                            v-if="inputVisible"
                            v-model="inputValue"
                            ref="saveTagInput"
                            size="small"
                            @keyup.enter.native="handleInputConfirm"
                            @blur="handleInputConfirm"
                        >
                        </el-input>
                        <el-button 
                            v-else
                            class="button-new-tag"
                            :style="addVerbal_params.keywords.length ? '': 'margin-left: 0px;'"
                            size="small"
                            @click="showInput"
                        >
                            + 添加
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addVerbal_dialog = false">取 消</el-button>
                <el-button :loading="add_loading" type="primary" @click="addVerbal">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            title="话术分类"
            :visible.sync="classify_Dialog"
            width="560px"
        >
            <div style="margin-bottom: 12px;" class="flex-row">
                <el-input 
                    style="width: 260px; margin-right: 10px;"
                    v-model="addClassify_params.cate_name" 
                    placeholder="分类名称"
                ></el-input>
                <el-button type="primary" @click="addVerbalClassify" :loading="is_loading">添加</el-button>
            </div>
            <div style="max-height: 568px; overflow-y: auto;">
                <el-table
                    :data="verbal_type_list"
                    border
                    style="width: 100%"
                    :header-cell-style="{background:'#EBF0F7', borderColor: '#DDE1E9', color: '#2E3C4E'}"
                    :cell-style="{borderColor:'#DDE1E9', fontSize: '14px', color: '#2E3C4E'}"
                >
                    <el-table-column
                        align="center"
                        prop="cate_name"
                        label="分类名称"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="操作"
                        width="180"
                    >
                        <template v-slot="{ row }">
                            <div>
                                <el-link 
                                    type="warning" 
                                    style="margin-right: 10px;"
                                    @click="editClassify(row)"
                                >
                                    编辑
                                </el-link>
                                <el-link type="danger" @click="deleteClassify(row)">删除</el-link>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-dialog
                width="560px"
                title="编辑话术分类"
                :visible.sync="editClassify_dialog"
                append-to-body>
                <div>
                    <el-form :model="editClassify_params" label-width="80px">
                        <el-form-item label="分类名称">
                            <el-input v-model="editClassify_params.cate_name" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="editClassify_dialog = false">取 消</el-button>
                    <el-button type="primary" @click="editVerbalClassify" :loading="is_loading">确定</el-button>
                </div>
            </el-dialog>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            // 话术列表传参
            search_params: {
                page: 1, // 页码
                keyword: '', // 话术内容
                cate_id: '', // 话术id
            },
            search_text: '',
            options: [],
            tableData: [],
            expands: [],
            total: 20,
            // 添加话术传参
            addVerbal_params: {
                cate_id: '', // 分类id
                text: '', // 话术内容
                keywords: [], // 关键字，多个用,分开
            },
            addVerbal_dialog: false, // 添加话术模态框
            verbal_type_list: [], // 话术分类
            inputVisible: false,
            inputValue: '',
            add_loading: false, // loading加载
            classify_Dialog: false, // 话术分类模态框
            // 添加话术分类参数
            addClassify_params: {
                cate_name: '', // 分类名称
            },
            // 编辑话术分类参数
            editClassify_params: {
                cate_name: '',
                id: '',
            },
            editClassify_dialog: false, // 编辑话术分类模态框
            is_loading: false, // loading加载
        }
    },
    created() {
        this.getVerbalList();
    },
    methods: {
        // 更改当前页码
        handleCurrentChange(value) {
            this.search_params.page = value;
            this.getVerbalList();
        },
        // 获取话术列表
        getVerbalList() {
            this.$http.getVerbalList(this.search_params).then((res) => {
                if(res.status == 200) {
                    let num = [];
                    this.tableData = res.data.data;
                    this.total = res.data.total;
                    this.tableData.map((item) => {
                        item.keywords.map((list) => {
                            num = list.split(",");
                        })
                        item.keywords = num;
                    })
                    console.log(this.tableData,"数据")
                }
            })
        },
        // 添加话术
        addVerbalDialog() {
            this.addVerbal_dialog = true; // 显示模态框
            if(!this.verbal_type_list.length) {
                this.getVerbalType(); // 获取话术分类
            }
        },
        // 获取话术分类
        getVerbalType() {
            this.$http.getVerbalType().then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"话术分类");
                    this.verbal_type_list = res.data.cates;
                }
            })
        },
        // 添加话术
        addVerbal() {
            if(this.addVerbal_params.cate_id == "" || this.addVerbal_params.cate_id == undefined) {
                return this.$message.warning("请选择分类");
            }
            if(this.addVerbal_params.text == "" || this.addVerbal_params.text == undefined) {
                return this.$message.warning("请输入话术内容");
            }
            if(!this.addVerbal_params.keywords.length) {
                return this.$message.warning("请添加关键字");
            }
            let params = Object.assign({}, this.addVerbal_params);
            params.keywords = params.keywords.join(",")
            this.add_loading = true; // 开启loading
            this.$http.addVerbal(params).then((res) => {
                if(res.status == 200) {
                    this.$message.success("添加成功");
                    this.addVerbal_dialog = false; // 关闭模态框
                    this.$refs.verbal.resetFields(); // 重置数据
                    this.add_loading = false; // 关闭loading
                    this.getVerbalList(); // 刷新话术列表
                } else {
                    this.add_loading = false;
                }
            }).catch(() => {
                this.add_loading = false;
            })
        },
        // 添加话术关闭tag触发
        handleTagClose(tag) {
            this.addVerbal_params.keywords.splice(this.addVerbal_params.keywords.indexOf(tag), 1);
        },
        handleInputConfirm() {
            let value = this.inputValue;
            if (value) {
                this.addVerbal_params.keywords.push(value);
            }
            this.inputVisible = false;
            this.inputValue = '';
        },
        showInput() {
            this.inputVisible = true;
            this.$nextTick(() => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },
        // 点击搜索按钮
        comfirmSearch() {
            const numericValue = parseInt(this.search_text);
            if(isNaN(numericValue)) {
                this.search_params.keyword = this.search_text;
                this.search_params.cate_id = '';
            } else {
                this.search_params.cate_id = this.search_text;
                this.search_params.keyword = '';
            }

            this.search_params.page = 1;
            this.getVerbalList();
        },
        // 点击重置按钮
        comfirmReset() {
            this.search_text = '';
            this.search_params.keyword = '';
            this.search_params.cate_id = '';
            this.search_params.page = 1;
            this.getVerbalList();
        },
        // 添加话术分类
        addVerbalClassify() {
            if(!this.addClassify_params.cate_name) {
                return this.$message.warning('请填写分类名称');
            }

            this.is_loading = true; // 显示loading
            this.$http.addVerbalClassify(this.addClassify_params).then((res) => {
                if(res.status  == 200) {
                    this.$message.success('添加成功');
                    this.is_loading = false; // 关闭loading
                    this.addClassify_params.cate_name = ''; // 重置参数
                    this.getVerbalType(); // 获取话术分类
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 话术分类模态框
        classifyDialog() {
            if(!this.verbal_type_list.length) {
                this.getVerbalType(); // 获取话术分类
            }
            this.classify_Dialog = true;
        },
        // 编辑话术分类
        editClassify(row) {
            this.editClassify_params.id = row.id; // 赋值编辑的话术分类id
            this.editClassify_params.cate_name = row.cate_name; // 赋值编辑的话术名称
            this.editClassify_dialog = true; // 显示编辑话术分类模态框
        },
        // 确定编辑话术分类
        editVerbalClassify() {
            if(!this.editClassify_params.cate_name) {
                return this.$message.warning('请填写分类名称');
            }

            this.is_loading = true; // 开启loading
            this.$http.editVerbalClassify(this.editClassify_params).then((res) => {
                if(res.status == 200) {
                    this.$message.success("编辑成功");
                    this.is_loading = false; // 关闭loading
                    this.editClassify_dialog = false; // 关闭模态框
                    this.getVerbalType(); // 获取话术分类
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 删除话术分类
        deleteClassify(row) {
            this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.deleteVerbalClassify(row.id).then((res) => {
                    if(res.status == 200) {
                        this.$message.success("删除成功");
                        this.getVerbalType(); // 获取话术分类
                    }
                })
            }).catch(() => {
                return;
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.container {
    margin: -15px;
    padding: 20px 24px;
    background: #F1F4FA;
    .container-box {
        padding: 26px 35px;
        background: #FFFFFF;
        .container-title {
            color: #2E3C4E;
            font-size: 24px;
            margin-bottom: 26px;
        }
        .search-cloumn {
            .search-box {
                margin-right: 24px;
                ::v-deep .el-select {
                    .el-input {
                        .el-input__inner {
                            width: 212px;
                        }
                    }
                }
            }
            ::v-deep .button-box1 {
                margin-left: 15px;
                .el-button {
                    font-size: 16px;
                    background: #2D84FB;
                    border-color: #2D84FB;
                    padding: 11px 24px;
                    &:hover {
                        background: #639ff2;
                        border-color: #639ff2;
                    }
                    &:active {
                        background: #246ac9;
                        border-color: #246ac9;
                    }
                }
            }
            .button-box2 {
                margin-left: 20px;
                .el-button {
                    font-size: 16px;
                    background: #787B88;
                    border-color: #787B88;
                    padding: 11px 24px;
                    &:hover {
                        background: #989898;
                        border-color: #989898;
                    }
                    &:active {
                        background: #5d5e61;
                        border-color: #5d5e61;
                    }
                }
            }
        }
        .table-box {
            margin-top: 21px;
            .often_style {
                // display: inline-block;
                // max-width: 180px;
                // white-space: nowrap;
                // overflow: hidden;
                // text-overflow: ellipsis;
                padding: 4px 20px;
                border-radius: 5px;
                background: #F6F8FC;
            }
            .resemble_style {
                padding: 4px 17px;
                border-radius: 5px;
                background: #F6F8FC;
                margin-right: 33px;
                margin-bottom: 10px;
                margin: 10px 33px 10px 0px;
            }
            .resemble_style:last-child {
                margin-right: 0px;
            }
            ::v-deep .el-table__expand-icon {
                visibility: hidden;
            }
        }
        .footer {
            margin-top: 30px;
            text-align: end;
            ::v-deep .el-pagination.is-background .btn-prev {
                border-radius: 4px;
                background-color: #FFFFFF;
                border: 1px solid #DDE1E9;
            }
            ::v-deep .el-pagination.is-background .btn-next {
                border-radius: 4px;
                background-color: #FFFFFF;
                border: 1px solid #DDE1E9;
            }
            ::v-deep .el-pagination.is-background .el-pager li:not(.disabled){
                border-radius: 4px;
                background-color: #FFFFFF;
                border: 1px solid #DDE1E9;
            }
            ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
                background-color: #409eff;
                border-color: #409eff;
                color: #fff;
            }
        }
    }
    .el-tag + .el-tag {
        margin-left: 10px;
    }
    .button-new-tag {
        margin-left: 10px;
        height: 32px;
        line-height: 30px;
        padding-top: 0;
        padding-bottom: 0;
    }
    .input-new-tag {
        width: 90px;
        margin-left: 10px;
        vertical-align: bottom;
    }
}
</style>