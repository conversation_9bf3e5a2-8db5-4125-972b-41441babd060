<template>
  <!--  标签库 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row" style="border-bottom: 1px solid #e2e2e2">
            <span class="text">分配员工：</span>
            <div>
              <el-input placeholder="请选择使用员工" v-model="username" @focus="showMemberList">
                <i @click="delName" slot="suffix" class="el-input__icon el-icon-circle-close"></i></el-input>
            </div>
          </div>
          <div class="bottom-border div row" style="margin-top: 24px;padding-bottom:0">
            <span class="text">添加状态：</span>
            <myLabel :arr="add_list" @onClick="onClickAdd"></myLabel>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row flex-1">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
            </div>
            <div class="t-t-b-right div row">
              <el-button type="primary" size="mini" @click="importFile">导入</el-button>
            </div>
          </div>
          <el-table :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
            :row-style="$TableRowStyle" v-loading="is_table_loading">
            <el-table-column label="姓名" prop="name"></el-table-column>
            <el-table-column label="联系方式" prop="mobile"></el-table-column>
            <el-table-column label="导入时间" prop="created_at"></el-table-column>
            <el-table-column label="是否隐藏手机号" v-slot="{ row }">
              {{ row.hide == 1 ? "隐藏" : "未隐藏" }}
            </el-table-column>
            <el-table-column label="是否添加" v-slot="{ row }">
              {{ row.type == 1 ? "已添加" : "未添加" }}
            </el-table-column>
            <!-- <el-table-column label="企业标签" v-slot="{ row }">
              <el-tag
                size="mini"
                style="margin-right:5px"
                v-for="(item, index) in row.labels"
                :key="index"
                >{{ item }}</el-tag
              >
            </el-table-column> -->
            <el-table-column label="分配员工" prop="user_name"></el-table-column>
          </el-table>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
            :total="params.total" :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择员工">
      <memberListSingle v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" ref="memberList">
      </memberListSingle>
    </el-dialog>
    <el-dialog :visible.sync="is_dialog_file" width="660px" title="批量导入">
      <div class="labelrow">
        <el-link type="primary" @click="onClicktemplate">1：点击下载导入数据模块</el-link>
      </div>
      <div class="l-d-box" style="margin-left:2rem">
        <div class="desc">1)目前仅支持导入手机号码和客户称呼；</div>
        <div class="desc">
          2)最多可支持导入1000个手机号，超过数量将不再导入，
        </div>
      </div>
      <div class="labelrow">
        2：是否隐藏手机号码：<el-switch v-model="is_hide" active-color="#13ce66" inactive-color="#ff4949" active-value="1"
          inactive-value="0">
        </el-switch>
      </div>
      <div class="l-d-box" style="margin-left:2rem">
        <div class="desc">隐藏后员工只能看到一个随机串</div>
      </div>
      <div class="labelrow">
        3：选择添加好友的员工和企微标签
        <div style="margin-left:1rem">
          <div class="xuanz">
            1)选择员工：
            <el-select v-model="user_id" size="small" placeholder="请选择员工" clearable>
              <el-option v-for="item in group_manager_list" :label="item.user_name" :key="item.wx_agent_userid"
                :value="item.wx_agent_userid"></el-option>
            </el-select>
            <div class="l-d-box">
              <div class="desc">
                表格内客户将平均分配给选择的员工，分配完成后员工收到添加好友提醒，需要员工手动添加
              </div>
            </div>
          </div>
          <div class="xuanz">
            2)企微标签：
            <el-cascader clearable size="small" v-model="tag_id" :options="tag_list" :props="{
                            value: 'id',
                            label: 'name',
                            children: 'taggroup',
                            emitPath: false,
                            multiple: true,
                          }"></el-cascader>
            <div class="l-d-box">
              <div class="desc">
                表格内的客户添加成功后，将会自动标记上标签
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <input type="file" ref="file_p" accept=".xls,.doc,.txt,.xlsx" style="display: none;"
          v-on:change="handleFileUpload($event)" />
        <el-button @click="is_dialog_file = false">取 消</el-button>
        <el-button type="primary" @click="$refs.file_p.click()">开始导入</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import memberListSingle from "../site/components/memberList_single.vue";
import myLabel from "./components/my_label";
export default {
  name: "crm_batch_add",
  components: {
    memberListSingle,
    myLabel,
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_id: "",
        type: 0,
      },
      tableData: [],
      is_table_loading: false,
      username: "",
      show_member_list: false,
      selectedIds: [],
      memberList: [],
      is_dialog_file: false,
      add_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "未添加" },
        { id: 2, name: "已添加" },
      ],
      is_hide: "",
      group_manager_list: [],
      user_id: "",
      tag_list: [],
      tag_id: [],
    };
  },
  mounted() {
    this.getDataList();
    this.getDepartment();
    this.getCustomrListSearch();
  },
  methods: {
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    getCustomrListSearch() {
      this.$http.getCustomrListSearch().then((res) => {
        if (res.status === 200) {
          this.group_manager_list = res.data.adminUsers;
          this.tag_list = res.data.agentTags;
        }
      });
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    handleFileUpload(event) {
      // 阻止默认行为
      event.preventDefault();
      let file = this.$refs.file_p.files[0];
      let formData = new FormData();
      formData.append("excel", file);
      formData.append("hide", this.is_hide);
      formData.append("wx_work_userid", this.user_id);
      let tag_id = this.tag_id.join(",");
      formData.append("tag_id", tag_id);
      this.onUpload(formData);
    },
    onUpload(formData) {
      this.$message.success("正在上传...");
      this.$http.uploadCrmCustomerFriendData(formData).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.params.page = 1;
          this.is_dialog_file = false;
          this.getDataList();
        }
      });
    },
    onClickAdd(e) {
      this.params.type = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.username = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.params.user_id =
          e.checkedNodes[e.checkedNodes.length - 1].wx_work_user_id;
      } else {
        this.username = "";
        this.params.user_id = "";
      }
      this.show_member_list = false;
      this.params.page = 1;
      this.getDataList();
    },
    showMemberList() {
      this.show_member_list = true;
    },
    delName() {
      this.params.user_id = "";
      this.username = "";
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getFriendMemberData({ params: this.params })
        .then((res) => {
          if (res.status === 200) {
            this.is_table_loading = false;
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        })
        .catch(() => {
          this.is_table_loading = false;
        });
    },
    onClicktemplate() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/friends_template.xlsx"
      );
    },
    importFile() {
      this.is_dialog_file = true;
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .top {
    align-items: center;
  }

  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }

  .bottom-border {
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 20px;

    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}

.labelrow {
  margin-bottom: 20px;

  .xuanz {
    margin-left: 1rem;
    margin-top: 20px;
  }
}

.l-d-box {
  margin-top: 10px;

  .desc {
    color: #999;
    margin-bottom: 10px;
  }
}

.m-user {
  align-items: center;

  .a-avatar {
    text-align: center;
    width: 30px;
    height: 30px;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 10px;
    line-height: 30px;
    color: #fff;
    background: #2d84fb;
  }
}
</style>
