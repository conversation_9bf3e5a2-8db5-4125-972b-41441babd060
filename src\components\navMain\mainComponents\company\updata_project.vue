<template>
  <el-container
    ><el-header>
      <tipsList :tips_content="tips_list"></tipsList>
    </el-header>
    <el-main>
      <el-form
        :rules="rules"
        label-width="200px"
        :model="form"
        style="width: 100%"
      >
        <el-tabs type="card" v-model="activeName">
          <el-tab-pane label="项目配置" name="first">
            <el-form-item label="请选择楼盘" prop="build_id">
              <el-select
                v-model="form.build_name"
                filterable
                remote
                placeholder="请输入楼盘名称"
                :remote-method="getbuildData"
                :loading="build_loading"
                @change="onBuild"
              >
                <el-option
                  v-for="item in build_options"
                  :key="item.build_id"
                  :label="item.name"
                  :value="item.build_id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
            <!-- <el-form-item label="项目名称拼音">
          <el-input v-model="form.pinyin_name"></el-input>
          <p>请输入项目名称首字母</p>
        </el-form-item> -->
            <el-form-item label="项目是否显示">
              <el-radio-group v-model="form.display">
                <el-radio
                  v-for="item in display_list"
                  :key="item.boolean"
                  :label="item.boolean"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="类型">
              <el-input
                maxlength="2"
                v-model="form.label"
                placeholder="最多输入两字;例：精选"
              ></el-input>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="报备配置" name="second">
            <el-form-item label="自动报备有效：">
              <el-switch
                v-model="form.auto_reported_valid"
                active-color="#13ce66"
                inactive-color="#748a8f"
              >
              </el-switch>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  指的是经纪人在系统报备客户后自动成为有效客户状态，无需后台或项目助理审核
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item
              label="多人报备客户保护："
              v-if="form.auto_reported_valid == 0"
            >
              <el-switch
                v-model="form.allow_multi_broker_reported_protected"
                active-color="#748a8f"
                inactive-color="#13ce66"
              >
              </el-switch>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  用来控制是否允许多人报备同一个客户，如果开启则不允许多人报备同一客户
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="意向楼盘类型选择：">
              <el-switch
                v-model="form.reported_intention"
                active-color="#13ce66"
                inactive-color="#748a8f"
              ></el-switch>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  前台经纪人报备页面中是否显示客户的意向楼盘类型，例如该客户是想要住宅还是商铺，多层还是高层等。注意：如果只有一个类型则自动过滤隐藏
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="全号报备：">
              <el-radio-group v-model="form.full_num_reported">
                <el-radio
                  v-for="item in full_num_reported_list"
                  :key="item.id"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  指该项目报备支持全号报备还是隐号报备。如果开启全号报备则只允许报备全号；如果开启隐号报备则只允许隐号报备，多项目报备时如果其中一个项目需要全号报备则填写全号，其他隐号报备的项目系统会自动处理为隐号
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="隐号方式：" v-if="form.full_num_reported == 0">
              <el-radio-group v-model="form.hide_num_mode">
                <el-radio :label="1">前三后四</el-radio>
                <el-radio :label="2">前三后五</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="报备是否陪同：">
              <el-radio-group v-model="form.reported_go_with">
                <el-radio
                  v-for="item in reported_go_with_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  报备时是否填写客户是否陪同
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="身份证号后六位：">
              <el-radio-group v-model="form.reported_id_no_category">
                <el-radio
                  v-for="item in reported_id_no_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  报备时是否填写身份证号码后6位，一般隐号报备客户手机号码重复率较高，同时开启填写身份证号后6位更容易区分判客
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="手机号补全：">
              <el-radio-group v-model="form.fill_customer_phone_category">
                <el-radio
                  v-for="item in fill_customer_phone_category_list"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  允许哪些人补全手机号码
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="可报备用户：">
              <el-radio-group v-model="form.customer_reported_user_category">
                <el-radio
                  v-for="item in customer_reported_user_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  允许哪些用户报备客户
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="到访日期格式：">
              <el-radio-group v-model="form.visit_time_format_category">
                <el-radio
                  v-for="item in visit_time_format_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  前台通过哪种格式显示时间，一般保持默认即可，也可自定义配置显示
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备日期格式：">
              <el-radio-group
                v-model="form.reported_created_at_format_category"
              >
                <el-radio
                  v-for="(
                    item, index
                  ) in reported_created_at_format_category_list"
                  :key="index"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  前台通过哪种格式显示时间，一般保持默认即可，也可自定义配置显示
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <!-- 报备规则设置 -->
            <el-form-item label="规则设置：">
              <el-radio-group v-model="form.reported_rule_category">
                <el-radio
                  v-for="item in reported_rule_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item
              label="报备规则配置："
              v-if="form.reported_rule_category == 2"
            >
              <el-input
                placeholder="请输入报备内容"
                v-model="form_report_content.reported"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-input
                placeholder="请输入带看内容"
                v-model="form_report_content.visit"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-input
                placeholder="请输入成交内容"
                v-model="form_report_content.deal"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-input
                placeholder="请输入结佣内容"
                v-model="form_report_content.commission"
                type="textarea"
                rows="8"
              >
              </el-input>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  填写项目报备规则，根据提示输入对应输入框即可，也可以配置为不显示
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备未审核无效类型：">
              <el-radio-group
                v-model="form.reported_not_audit_invalid_category"
              >
                <el-radio
                  v-for="item in reported_not_audit_invalid_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  如果项目配置未开启自动报备有效，前台报备的客户的状态会显示为待审核。如果配置为手动，则需要后台或项目助理手动设为无效，否则一直是待审核状态；如果设为自动，则该客户如果超出报备保护期自动设为无效
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备延迟到访：">
              <el-input v-model="form.delay_visit_minute">
                <template slot="append">分钟</template>
              </el-input>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  报备成功后延迟指定时间才可以到访，少于规定时间无法确认到访，例如下图配置为30分钟，就需要30分钟后才可以确认到访
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="延迟到访/报备保护规则：">
              <el-radio-group v-model="form.from_follow_up">
                <el-radio :label="1">领取后到访延迟和报备保护开始计算</el-radio>
                <el-radio :label="0"
                  >设为有效后，到访延迟和报备保护开始计算</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  指的是从哪个时间点开始计算报备保护的时间以及延迟到访的时间
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="报备保护时间：">
              <el-input
                style="width: 150px"
                v-model="form.reported_protected_day"
                ><template slot="append">天</template></el-input
              >
              <el-input
                style="width: 150px"
                v-model="form.reported_protected_hour"
                ><template slot="append">小时</template></el-input
              >
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  设置报备保护期时间，保护期内其他人无法进行报备
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="到访保护时间：">
              <el-input style="width: 150px" v-model="form.visit_protected_day"
                ><template slot="append">天</template></el-input
              >
              <el-input style="width: 150px" v-model="form.visit_protected_hour"
                ><template slot="append">小时</template></el-input
              >
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  客户确认到访后该客户的保护期时间，保护期内其他人无法进行报备
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <!-- <el-form-item label="线上客户保护天数">
          <el-input
            
            v-model="form.online_customer_protected_day"
          ></el-input>
        </el-form-item> -->
            <el-form-item label="重复报备保护时间：">
              <el-input
                style="width: 150px"
                v-model="form.repeat_reported_protected_day"
                ><template slot="append">天</template></el-input
              >
              <el-input
                style="width: 150px"
                v-model="form.repeat_reported_protected_hour"
                ><template slot="append">小时</template></el-input
              >
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  是指之前报备过一个客户，然后想再次报备需要等重复报备保护时间过了以后才可以报备
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>
            <el-form-item label="来访方式是否显示：">
              <el-radio-group v-model="form.reported_visit">
                <el-radio
                  v-for="item in display_list"
                  :key="item.boolean"
                  :label="item.boolean"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content" style="max-width: 300px">
                  指前台报备时是否需要填写来访方式
                </div>
                <el-button
                  style="margin-left: 10px"
                  type="danger"
                  size="mini"
                  class="el-icon-info"
                  >说明</el-button
                >
              </el-tooltip>
            </el-form-item>

            <!-- <el-form-item label="预算是否显示">
              <el-radio-group v-model="form.reported_budget">
                <el-radio
                  v-for="item in display_list"
                  :key="item.boolean"
                  :label="item.boolean"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item> -->
          </el-tab-pane>
          <el-tab-pane label="其它配置" name="fourth">
            <el-form-item label="合作时间：">
              <div class="block">
                <el-date-picker
                  v-model="form.cooperation_start"
                  type="date"
                  placeholder="选择开始日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <i style="font-style: normal; color: #748a8f; margin: 0 10px"
                  >至</i
                >
                <el-date-picker
                  v-model="form.cooperation_end"
                  type="date"
                  placeholder="选择结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </el-form-item>
            <el-form-item label="项目归属：">
              <el-radio-group v-model="form.project_ownership">
                <el-radio
                  v-for="item in project_ownership_list"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="人脸识别：">
              <el-radio-group v-model="form.face_recognition">
                <el-radio
                  v-for="item in full_num_reported_list"
                  :key="item.id"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="回访联系：">
              <el-radio-group v-model="form.return_visit_phone">
                <el-radio
                  v-for="item in full_num_reported_list"
                  :key="item.id"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item label="结佣方式：">
              <el-radio-group v-model="form.clearing_brokerage_category">
                <el-radio
                  v-for="item in clearing_brokerage_category_list"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item label="合作方式：">
              <el-radio-group v-model="form.cooperation_category">
                <el-radio
                  v-for="item in cooperation_category_list"
                  :key="item.id"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item></el-tab-pane
          >
          <el-tab-pane label="佣金规则" name="fifth">
            <el-form-item label="项目佣金规则">
              <el-radio-group v-model="form.brokerage_rule_category">
                <el-radio
                  v-for="item in brokerage_rule_category_list"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.description }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <div class="personal">
              <div class="tips-title">
                <span>此处设置内容仅在独立经纪人或游客登录时展示</span>
              </div>
              <!-- <el-form-item
						label="此处设置内容仅在独立经纪人或游客登录时展示"
						label-width="400px"
						align="left"
					></el-form-item> -->
              <el-form-item label="佣金分成">
                <el-input
                  placeholder="不设置佣金填0即可"
                  v-model="form.brokerage_rule"
                ></el-input>
                <!-- <i style="color:#f78989">元/套</i> -->
              </el-form-item>
              <el-form-item label="分佣规则">
                <el-input
                  type="textarea"
                  v-model="form.brokerage_description"
                ></el-input>
              </el-form-item>
              <el-form-item label="带看奖励">
                <el-input
                  style="200px"
                  v-model="form.shopping_guide_reward"
                ></el-input>
              </el-form-item>
            </div>
            <div class="store">
              <!-- <el-form-item
						label="此处设置内容仅在门店经纪人登录时展示，留空时将于独立经纪人展示一样的内容"
						label-width="600px"
					></el-form-item> -->
              <div class="store-tips">
                <span
                  >此处设置内容仅在门店经纪人登录时展示，留空时将于独立经纪人展示一样的内容</span
                >
              </div>
              <el-form-item label="佣金分成">
                <el-input
                  placeholder="不设置佣金填0即可"
                  v-model="form.store_brokerage_rule"
                ></el-input>
                <!-- <i style="color:#f78989">元/套</i> -->
              </el-form-item>
              <el-form-item label="分佣规则">
                <el-input
                  type="textarea"
                  v-model="form.store_brokerage_description"
                ></el-input>
              </el-form-item>
              <el-form-item label="带看奖励">
                <el-input
                  style="200px"
                  v-model="form.store_shopping_guide_reward"
                ></el-input>
              </el-form-item>
            </div>
          </el-tab-pane>
          <el-tab-pane label="案场" name="sixth">
            <el-form-item label="案场到访保护天数：">
              <el-input v-model="form.case_reported_protected_day"
                ><template slot="append">天</template></el-input
              >
            </el-form-item>
            <el-form-item label="案场到访保护小时：">
              <el-input v-model="form.case_reported_protected_hour"
                ><template slot="append">小时</template></el-input
              >
            </el-form-item>
            <!-- <el-form-item label="案场到访保护时间：">
              <el-input v-model="form.case_delay_visit_minute">
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item> -->
          </el-tab-pane>
          <!-- 
          <el-tab-pane label="二级分销配置" name="third">
            <el-form-item label="分销设置：">
              <el-radio-group v-model="form.distribution_brokerage_type">
                <el-radio
                  @change="changeDistribution"
                  v-for="item in distribution_type"
                  :key="item.value"
                  :label="item.value"
                  >{{ item.desc }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="分销内容："
              v-if="form.distribution_brokerage_type === 1"
            >
              <el-input-number
                v-model="form.distribution_brokerage_value"
                :precision="2"
                :step="step"
                :max="max_num"
                :min="0"
              ></el-input-number>
            </el-form-item>
            <el-form-item
              label="分销内容："
              v-if="form.distribution_brokerage_type === 2"
            >
              <el-input v-model="form.distribution_brokerage_value"></el-input>
            </el-form-item>
          </el-tab-pane> -->
        </el-tabs>

        <div class="btn-box">
          <el-form-item size="large">
            <el-button type="primary" @click="onSubmit">保存项目信息</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import tipsList from "@/components/components/tips_list.vue";
export default {
  name: "updata_project",
  components: { tipsList },
  data() {
    return {
      build_options: [],
      company_options: [],
      build_list: [],
      broker_list: [],
      company_list: [],
      build_loading: false,
      broker_loading: false,
      company_loading: false,
      project_id: "",
      form: {
        build_id: "",
        company_id: "",
        name: "",
        label: "",
        brokerage_rule: "",
        brokerage_description: "",
        shopping_guide_reward: "",
        store_brokerage_rule: "",
        store_brokerage_description: "",
        store_shopping_guide_reward: "",
        project_ownership: "1",
        clearing_brokerage_category: "1",
        auto_reported_valid: 1,
        reported_protected_day: 1,
        visit_protected_day: 1,
        // online_customer_protected_day: 1,
        repeat_reported_protected_day: 1,
        display: true,
        cooperation_category: "1",
        cooperation_start: "",
        cooperation_end: "",
        full_num_reported: 0,
        // reported_budget: true,
        reported_visit: false,
        distribution_brokerage_type: 0,
        distribution_brokerage_value: "0.00",
        //人脸识别
        face_recognition: 0,
        //回访电话
        return_visit_phone: 0,
        fill_customer_phone_category: "0",
        // 保护小时数
        /**
          项目新增字段：
            @reported_protected_hour 报备保护小时数 
            说明: 与报备保护天数可以同时设置，验证一下，保护天数和保护小时数，必须有一个大于0，
            都只能填整数
            @allow_multi_broker_reported_protected 
            是否允许多个经纪人报备同一个客户进入保护期， 非自动报备有效时有此项，默认为0，不允许
            @repeat_reported_protected_hour 重复报备保护小时数，参考报备保护小时数
            @visit_protected_hour 到访保护小时数， 参考报备保护小时数
        */
        reported_protected_hour: 0,
        allow_multi_broker_reported_protected: 0,
        repeat_reported_protected_hour: 0,
        visit_protected_hour: 0,
        customer_reported_user_category: "0",
        // 意向楼盘选择
        reported_intention: "0",
        // 延迟到访时间  /分钟
        delay_visit_minute: 0,
        reported_rule: "",
        reported_rule_category: "1",
        // 楼盘佣金规则显示
        brokerage_rule_category: "1",
        visit_time_format_category: "0",
        // 设置审核类型
        reported_not_audit_invalid_category: "1",
        reported_created_at_format_category: "0", // 报备时间
        reported_go_with: "0",
        reported_id_no_category: "1", // 是否开启填写身份证后6位
        from_follow_up: 0,
        case_reported_protected_day: 0, // 案场报备保护天数
        case_reported_protected_hour: 0, // 案场报备保护小时数
        case_delay_visit_minute: 0,
        hide_num_mode: 1, // 隐号方式
      },
      project_ownership: "",
      clearing_brokerage_category: "",
      display: "",
      cooperation_category: "",
      project_ownership_list: [],
      clearing_brokerage_category_list: [],
      display_list: [
        { boolean: true, name: "是" },
        { boolean: false, name: "否" },
      ],
      distribution_type: [
        { value: 0, desc: "系统配置" },
        { value: 1, desc: "按比例分销" },
        { value: 2, desc: "按固定分销" },
      ],
      full_num_reported_list: [
        { id: 0, name: "否" },
        { id: 1, name: "是" },
      ],
      reported_id_no_category_list: [
        { value: "1", description: "关闭" },
        { value: "2", description: "开启" },
      ],
      cooperation_category_list: [],
      isOpen: null,
      isOff: null,
      formCompany: {
        project_id: "",
        company_id: "",
      },
      fromBroker: {
        project_id: "",
        user_id: "",
      },
      fromManager: {
        project_id: "",
        user_id: "",
      },
      user_list: [],
      dialogBroker: false,
      dialogManager: false,
      dialogCompany: false,
      rules: {
        build_id: [{ required: true, trigger: "blur", message: "请选择楼盘" }],
        name: [{ required: true, trigger: "blur", message: "请输入项目名称" }],
      },
      step: 0,
      max_num: 1,
      activeName: "first",
      tips_list: [
        "请正确填写输入框信息",
        "分销配置：选择系统配置：项目分销规则将默认为系统设置规则，选择分销比例内容大于等于0小于等于1，固定分销内容大于等于0，小于999999.99的值",
        "多人报备客户保护：是否允许多个经纪人报备同一个客户进入保护期，非自动报备有效时设置",
      ],
      fill_customer_phone_category_list: [],
      customer_reported_user_category_list: [],
      reported_rule_category_list: [],
      // 报备规则内容
      form_report_content: {
        reported: "",
        visit: "",
        deal: "",
        commission: "",
      },
      brokerage_rule_category_list: [],
      visit_time_format_category_list: [],
      reported_not_audit_invalid_category_list: [],
      reported_created_at_format_category_list: [],
      reported_go_with_list: [],
    };
  },
  mounted() {
    if (this.$route.query.id) {
      this.project_id = this.$route.query.id;
      this.formCompany.project_id = this.project_id;
      this.fromBroker.project_id = this.project_id;
      this.fromManager.project_id = this.project_id;
      this.getQuery();
      // this.getbrokerData();
    }
    this.getbuildData(this.$route.query.name, true);
    this.getCompanyInfo();
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "PROJECT_OWNERSHIP":
            this.project_ownership_list = item.childs;
            break;
          case "CLEARING_BROKERAGE_CATEGORY":
            this.clearing_brokerage_category_list = item.childs;
            break;
          case "COOPERATION_CATEGORY":
            this.cooperation_category_list = item.childs;
            break;
          case "FILL_CUSTOMER_PHONE_CATEGORY":
            this.fill_customer_phone_category_list = item.childs;
            this.fill_customer_phone_category_list.push({
              value: "0",
              description: "系统配置",
            });
            break;
          case "CUSTOMER_REPORTED_USER_CATEGORY":
            this.customer_reported_user_category_list = item.childs;
            this.customer_reported_user_category_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
          case "REPORTED_RULE_CATEGORY":
            this.reported_rule_category_list = item.childs;
            this.reported_rule_category_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
          case "BROKERAGE_RULE_CATEGORY":
            this.brokerage_rule_category_list = item.childs;
            this.brokerage_rule_category_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
          case "VISIT_TIME_FORMAT_CATEGORY":
            this.visit_time_format_category_list = item.childs;
            this.visit_time_format_category_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
          case "REPORTED_NOT_AUDIT_INVALID_CATEGORY":
            this.reported_not_audit_invalid_category_list = item.childs;
            this.reported_not_audit_invalid_category_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
          case "REPORTED_CREATED_AT_FORMAT_CATEGORY":
            this.reported_created_at_format_category_list = item.childs;
            this.reported_created_at_format_category_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
          case "REPORTED_GO_WITH_CATEGORY":
            this.reported_go_with_list = item.childs;
            this.reported_go_with_list.push({
              description: "系统配置",
              value: "0",
            });
            break;
        }
      });
    });
  },
  methods: {
    getCompanyInfo() {
      this.$http.companyGetInfo().then((res) => {
        if (res.status === 200) {
          this.form.company_id = res.data.id;
        }
      });
    },
    getQuery() {
      this.$http.companyQueryProject(this.project_id).then((res) => {
        if (res.status === 200) {
          this.form = res.data;
          this.form.project_ownership = this.form.project_ownership.toString();
          this.form.clearing_brokerage_category = this.form.clearing_brokerage_category.toString();
          this.form.cooperation_category = this.form.cooperation_category.toString();
          this.form.fill_customer_phone_category = this.form.fill_customer_phone_category.toString();
          this.form.customer_reported_user_category = this.form.customer_reported_user_category.toString();
          this.form.reported_rule_category = this.form.reported_rule_category.toString();
          this.form.brokerage_rule_category = this.form.brokerage_rule_category.toString();
          this.form.visit_time_format_category = this.form.visit_time_format_category.toString();
          this.form.reported_not_audit_invalid_category = this.form.reported_not_audit_invalid_category.toString();
          this.form.reported_go_with = this.form.reported_go_with.toString();
          this.form.from_follow_up = res.data.from_follow_up;
          this.form.case_delay_visit_minute = res.data.case_delay_visit_minute;
          this.form.hide_num_mode = res.data.hide_num_mode || 1;
          this.form.case_reported_protected_day =
            res.data.case_reported_protected_day;
          this.form.case_reported_protected_hour =
            res.data.case_reported_protected_hour;
          this.form.reported_id_no_category =
            this.form.reported_id_no_category + "";
          switch (this.form.visit_time_format_category) {
            case "1":
              this.form.visit_time_format_category = "Y-m-d H:i";
              break;
            case "2":
              this.form.visit_time_format_category = "Y-m-d";
              break;
            default:
              this.form.visit_time_format_category;
              break;
          }
          let obj = res.data.reported_rule;

          if (obj) {
            this.form_report_content = JSON.parse(obj);
          }
          switch (this.form.display) {
            case 0:
              this.form.display = false;
              break;
            case 1:
              this.form.display = true;
              break;
            default:
              break;
          }
          // switch (this.form.reported_budget) {
          //   case 0:
          //     this.form.reported_budget = false;
          //     break;
          //   case 1:
          //     this.form.reported_budget = true;
          //     break;
          //   default:
          //     break;
          // }
          switch (this.form.reported_visit) {
            case 0:
              this.form.reported_visit = false;
              break;
            case 1:
              this.form.reported_visit = true;
              break;
            default:
              break;
          }
          switch (this.form.auto_reported_valid) {
            case 0:
              this.form.auto_reported_valid = false;
              break;
            case 1:
              this.form.auto_reported_valid = true;
              break;
            default:
              break;
          }
          switch (this.form.reported_intention) {
            case 0:
              this.form.reported_intention = false;
              break;
            case 1:
              this.form.reported_intention = true;
              break;
            default:
              break;
          }
          switch (this.form.allow_multi_broker_reported_protected) {
            case 0:
              this.form.allow_multi_broker_reported_protected = false;
              break;
            case 1:
              this.form.allow_multi_broker_reported_protected = true;
              break;
            default:
              break;
          }
        }
      });
    },
    getbuildData(query, status) {
      this.$http.getcomapnyBuildListByname(query).then((res) => {
        this.build_list = res.data.data.map((item) => {
          return { name: item.name, build_id: item.id };
        });
        if (query) {
          this.build_loading = true;
          setTimeout(() => {
            this.build_loading = false;
            this.build_options = this.build_list.filter((item) => {
              return item.name.indexOf(query) > -1;
            });
          }, 200);
          if (status) {
            this.build_options.map((item) => {
              if (query == item.name) {
                this.form.build_id = item.build_id;
              }
            });
          }
        } else {
          this.build_options = [];
        }
      });
    },
    goBack() {
      this.$router.back();
    },
    onBuild(a) {
      this.form.build_id = a;
    },
    changeDistribution() {
      if (this.form.distribution_brokerage_type === 1) {
        this.step = 0.01;
        this.max_num = 1;
        this.form.distribution_brokerage_value = "";
      } else {
        this.form.distribution_brokerage_value = "";
      }
    },
    onSubmit() {
      if (!this.form.build_id) {
        this.$message.error("请选择楼盘");
        return;
      }
      if (!this.form.name) {
        this.$message.error("请选择项目名称");
        return;
      }
      if (!this.form.visit_protected_day) {
        this.form.visit_protected_day = 0;
      }
      if (!this.form.reported_protected_day) {
        this.form.reported_protected_day = 0;
      }
      if (!this.form.repeat_reported_protected_day) {
        this.form.repeat_reported_protected_day = 0;
      }
      // if (
      //   this.form.distribution_brokerage_type !== 0 &&
      //   !this.form.distribution_brokerage_value
      // ) {
      //   this.$message({
      //     message: "请输入分销内容",
      //     type: "error",
      //   });
      //   return;
      // }
      for (var prop in this.form) {
        if (this.form[prop] === "") {
          delete this.form[prop];
        }
      }
      //   if (!this.form.online_customer_protected_day) {
      //     this.form.online_customer_protected_day = 0;
      //   }
      // for (var prop in this.form) {
      // 	if (this.form[prop] === "") {
      // 		delete this.form[prop];
      // 	}
      // }
      this.form.reported_rule = JSON.stringify(this.form_report_content);
      if (this.$route.query.id) {
        this.$http.companyUpdataProject(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.$store.state.reloadCon = true
            // this.$router.back();
            console.log(11111);
            this.$goPath("/company_project_list");
          }
        });
      } else {
        this.$http.companyCreateProject(this.form).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.$store.state.reloadCon = true
            this.$router.back();
            // this.$goPath("/company_project_list");
          }
        });
      }
    },
    // goBack() {
    //   this.$router.push("/project_list");
    // },
  },
};
</script>

<style lang="scss" scoped>
.el-form-item {
  padding: 10px;
  background: #fff;
}
.title {
  color: #333;
  font-weight: bold;
}
.title-ctn {
  padding: 10px;
  color: #fff;
  background: #edfbf8;

  i {
    color: red;
  }
}
.el-input,
.el-textarea,
.el-select {
  width: 300px;
}
.el-date-editor {
  width: 133px;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}
// .el-header {
// 	height: 80px;
// }
.el-main {
  margin-top: 100px;
  .title {
    margin: 40px 0 10px;
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
}
p {
  color: #748a8f;
  margin: 4px 0;
}
.personal /deep/ .el-form-item__label {
  color: #2589ff;
  background: #f4f9ff;
}
.tips-title {
  color: #2589ff;
  background: #f4f9ff;
  margin-bottom: 20px;
  padding: 10px 0 10px 200px;
}
.store /deep/ .el-form-item__label {
  color: #a125ff;
  background: #f9f2ff;
}
.store-tips {
  color: #a125ff;
  background: #f9f2ff;
  margin-bottom: 20px;
  padding: 10px 0 10px 200px;
}
</style>
