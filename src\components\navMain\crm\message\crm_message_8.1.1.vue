<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="div row">
            <div class="title flex-1">消息存档</div>
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="message-tip">
            你已成功开通【私域流量管理】产品，还需要配置以下企业信息才能正常使用
          </div>
        </div>

        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="gaikuang div row align-center">
            <span class="text">总览：</span>
            <span
              >请按要求正确填写以下信息，否则将会有部分功能无法使用，如果您无法进入企业微信管理后台，请联系企业管理员</span
            >
          </div>
          <div class="progress flex-row">
            <div
              class="flex-row flex-1"
              v-for="(item, index) in data"
              :key="item.id"
            >
              <div
                class="progress_item flex-row j-center flex-1"
                :class="{ active: currentId == item.id }"
              >
                <div class="flex-box j-center align-center">
                  <div class="num flex-row j-center align-center">
                    {{ item.value }}
                  </div>
                  <div class="name">
                    {{ item.name }}
                  </div>
                </div>
              </div>
              <div class="line flex-4" v-if="index < data.length - 1"></div>
            </div>
          </div>
          <div class="form_tip">
            <p class="form_tip_top">
              1.登录企业微信后台没打开【应用管理】，点击【会话内容存档】，点击【查看购买信息】，根据您的购买信息选择过期时间。如未开启，
              <span class="primary"> 请联系客服 </span>
            </p>
            <p class="form_tip_bottom">
              *请正确填写到期时间，以方便在到期前及时提醒您，避免丢失重要信息
            </p>
          </div>
          <div class="form">
            <el-form label-width="150px">
              <el-form-item label="服务版过期时间：">
                <div class="form-item-block">
                  <el-input
                    placeholder="请输入服务版过期时间："
                    v-model="params.qrcode_name"
                    style="width: 240px; margin-right: 12px"
                  >
                  </el-input>
                </div>
              </el-form-item>
              <el-form-item label="企业版过期时间：">
                <div class="form-item-block">
                  <el-input
                    placeholder="请输入企业版过期时间："
                    v-model="params.qrcode_name"
                    style="width: 240px; margin-right: 12px"
                  >
                  </el-input>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "crm_message_8",
  components: {},
  data() {
    return {
      params: {},
      data: [
        {
          id: 1,
          name: "配置到期时间",
          value: 1,
        },
        {
          id: 2,
          name: "配置开启范围",
          value: 2,
        },
        {
          id: 3,
          name: "配置可信IP地址",
          value: 3,
        },
        {
          id: 4,
          name: "消息存档权限授权",
          value: 4,
        },
      ],
      currentId: 1,
    };
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.content-box-crm {
  .message-tip {
    color: #8a929f;
    font-size: 14px;
    background: #e7f3fd;
    padding: 20px 24px;
  }
  .gaikuang {
    padding: 0 20px 20px;
    // border-bottom: 1px solid #eee;
    span {
      color: #8a929f;
      font-family: PingFang SC;
      font-weight: regular;
      font-weight: normal;
      font-size: 14px;
      &.text {
        color: #333;
        font-weight: 600;
        font-size: 18px;
      }
      &.primary {
        color: #2d84fb;
        cursor: pointer;
      }
    }
  }
  .progress {
    padding: 0 100px;
    .progress_item {
      min-width: 110px;
      &.active {
        .num {
          background: #2d84fb;
        }
        .name {
          color: #2d84fb;
        }
      }
      .num {
        background: #d9d9d9;
        width: 50px;
        height: 50px;
        margin: 0 auto;
        border-radius: 50%;
        color: #fff;
      }
      .name {
        margin-top: 16px;
        color: #8a929f;
        font-size: 14px;
      }
    }
    .line {
      // width: 215px;
      margin-top: 25px;
      height: 1px;
      background: #e2e2e2;
    }
  }
  .form_tip {
    margin: 40px 0 0 132px;
    .form_tip_top {
      color: #2e3c4e;
      font-size: 14px;
      .primary {
        color: #2d84fb;
        cursor: pointer;
      }
    }
    .form_tip_bottom {
      color: #8a929f;
      font-size: 14px;
    }
  }
  .form {
    padding: 0 132px;
    margin-top: 40px;
  }
}
</style>
