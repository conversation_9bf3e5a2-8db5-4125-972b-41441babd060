<template>
  <div
    style="margin-left: 10px; overflow: auto;margin-bottom: 84px;"
    :style="{ height: followList_height + 'px' }"
    v-infinite-scroll="loadMoreFollow"
    :infinite-scroll-disabled="busy_new"
  >
    <el-timeline v-if="timeline.length > 0">
      <el-timeline-item
        v-for="(activity, index) in timeline"
        :key="index"
        :size="activity.size"
        placement="top"
        color="#2D84FB"
      >
        <div class="agent_info flex-row align-center">
          <div class="time">
            {{ activity.created_at }}
          </div>
          <div class="agent_name" v-if="activity.admin_user && activity.admin_user.user_name&&activity.admin_user.user_name">
            {{ activity.admin_user && activity.admin_user.user_name }}/{{
              activity.admin_user && activity.admin_user.department_name
            }}
          </div>
          <div class="agent_name" v-else>
            {{activity.admin_user?activity.admin_user.user_name?activity.admin_user.user_name:activity.admin_user.user_name:""}}
          </div>
        </div>
        <div class="spancolor">
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content" style="max-width: 500px">
                {{activity.content}}
              </div>
            <div v-if="activity.client_clue==null" class="clue-content">{{ activity.content }}</div>
          </el-tooltip>
          <el-collapse v-model="activeNames" @change="handleChange" v-if="activity.client_clue&&activity.client_clue.length!=0">
            <el-collapse-item :title="activity.content" name="1">
              <template v-slot:title>
                  <el-tooltip class="item" effect="dark" placement="top-start">
                    <div slot="content" style="max-width: 500px">
                      {{activity.content}}
                    </div>
                    <div class="clue-content">{{ activity.content }}</div>
                  </el-tooltip>
              </template>
              <div class="content">
              <div style="margin-top: 10px;" v-if="activity.client_clue.cname!==''">姓名：{{ activity.client_clue.cname}}</div>
              <div v-if="activity.client_clue.age!==0">年龄：{{ activity.client_clue.age }}</div>
              <div v-if="activity.client_clue.sex!==0">性别：{{ activity.client_clue.sex == 1 ? "男" : "女"}}</div>
              <div v-if="activity.client_clue.intention_community!==''">意向：{{ activity.client_clue.intention_community}}</div>
              <div v-if="activity.client_clue.remark!==''">备注：{{ activity.client_clue.remark }}</div>
              <div v-if="activity.client_clue.third_remark!==''">线索备注：{{ activity.client_clue.third_remark}}</div>
              <div v-if="activity.client_clue.province_name!==''">省份：{{ activity.client_clue.province_name==""?"--":activity.client_clue.province_name }}</div>
              <div v-if="activity.client_clue.city_name!==''">城市：{{ activity.client_clue.city_name }}</div>
              <div v-if="activity.client_clue.country_name!==''">地区：{{ activity.client_clue.country_name }}</div>
              <div v-if="activity.client_clue.address!==''">详细地址：{{ activity.client_clue.address }}</div>
              <div v-if="activity.client_clue.location!==''">定位城市：{{ activity.client_clue.location }}</div>
              <div v-if="activity.client_clue.clue_id!==''">线索原始id: {{ activity.client_clue.clue_id }}</div> 
              <div v-if="activity.client_clue.refer_dy_id!==''">{{activity.from_type.title}}: {{ activity.client_clue.refer_dy_id }}</div> 
              <!-- <div v-if="activity.client_clue.refer_dy_id!==''">{{activity.from_type.id==1?"腾房云：":activity.from_type.id==2?"百度基木鱼：":activity.from_type.id==3?
              "分销报备：":activity.from_type.id==4?"抖音号：": activity.from_type.id==5?"T+：":activity.from_type.id==6?"快手号：":activity.from_type.id==7?"海豚知道：":
              activity.from_type.id==8?"企业微信：":activity.from_type.id==9?"小程序：":activity.from_type.id==10?"外呼智能机器人：":activity.from_type.id==11?
              "微信视频号：":activity.from_type.id==12?"飞鱼：":activity.from_type.id==13?"领资料：":"小红书："}}{{ activity.client_clue.refer_dy_id }}</div>  -->
              <div v-if="activity.client_clue.refer_dy_name !==''">{{ getName(activity) }}</div> 
              <div v-if="activity.client_clue.config_name!==''">配置项名称：{{ activity.client_clue.config_name }}</div> 
              <div v-if="activity.client_clue.create_name!==''">录入人：{{ activity.client_clue.create_name }}</div>
              <div v-if="activity.client_clue.follow_name!==''">维护人：{{ activity.client_clue.follow_name }}</div> 
              <div v-if="activity.client_clue.label_type">意向等级：{{ activity.client_clue.label_type }}级</div> 
              <div v-if="activity.client_clue.duration"> 通话时长：{{ activity.client_clue.duration }}s</div> 
              <div v-if="activity.client_clue.focus"> 关注点：{{ activity.client_clue.focus}}</div>
              <div v-if="activity.client_clue.comment"> 互动内容：{{ activity.client_clue.comment}}</div>
              <div v-if="activity.client_clue&&activity.client_clue.action_record"> 
                <div v-if="activity.client_clue.action_record.action_desc"> 互动记录：{{ activity.client_clue.action_record.action_desc}}</div>
                <!-- <div v-if="activity.client_clue.action_record.user_page"> 用户主页：
                  <el-link type="primary" @click="OpenVideo(activity.client_clue.action_record.user_page)">{{activity.client_clue.action_record.user_page}}</el-link></div> -->
                <div v-if="activity.client_clue.action_record.action_type_name"> 互动类型: {{ activity.client_clue.action_record.action_type_name}}</div>
                <!-- <div class="follow-picture" v-if="activity.client_clue.action_record.cover"> 封面图：
                  <img class="img" :src="$imageFilter(activity.client_clue.action_record.cover, 'w_240')" alt="">
                  <span class="uploader-actions" v-if="activity.client_clue.action_record.cover">
                                  <span class="uploader-actions-item" @click="handlePictureCardPreview(activity.client_clue.action_record.cover)">
                                    <i class="el-icon-view"></i>
                                  </span>
                                </span></div> -->
                  <!-- {{ activity.client_clue.action_record.cover}} -->
                <div v-if="activity.client_clue.action_record.create_time"> 互动时间：{{ activity.client_clue.action_record.create_time}}</div>
                <!-- <div v-if="activity.client_clue.action_record.video_name"> 短视频/名称：{{ activity.client_clue.action_record.video_name}}</div> -->
                <!-- <div v-if="activity.client_clue.action_record.video_url"> 短视频播放URL地址:
                  <el-link type="primary" @click="OpenVideo(activity.client_clue.action_record.video_url)">{{ activity.client_clue.action_record.video_url}}</el-link></div> -->
                </div>
            </div>
            </el-collapse-item>
          </el-collapse>
          <!-- <template v-if="activity.client_clue&&activity.client_clue.action_record&&activity.client_clue.action_record.cover&&activeNames==1">
            <div v-if="activity.client_clue.action_record&&activeNames==1" class="SourceCard">
               <div v-if="activity.client_clue.action_record.cover" class="CoverImage">
                <img class="img" :src="$imageFilter(activity.client_clue.action_record.cover, 'w_240')" alt="">
                  <span class="uploader-actions" v-if="activity.client_clue.action_record.cover">
                    <span class="uploader-actions-item" @click="handlePictureCardPreview(activity.client_clue.action_record.cover)">
                      <i class="el-icon-view"></i>
                    </span>
                  </span>
              </div>
              <div v-if="activity.client_clue.action_record.video_name" class="Livetitle">
                <el-link type="info" @click="copyaddress(activity)">{{activity.client_clue.action_record.video_name}}</el-link>
              </div>
            </div>
          </template> -->
         
        </div>
      </el-timeline-item>
    </el-timeline>
    <!-- <myEmpty v-if="timeline.length == 0 && !busy_new"></myEmpty> -->
    <myEmpty v-else :loading="loading"></myEmpty>
    <!-- 查看已上传图片模态框 -->
    <div class="mask1" v-if="show_dialog_pictures" @click="show_dialog_pictures = false">
      <div class="preview_img" @click.prevent.stop="() => { }">
        <img id="preImg" :src="dialog_pictures_src" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import myEmpty from "@/components/components/my_empty.vue";
export default {
  components: {
    myEmpty,
  },
  props: {
    timeline: {
      type: Array,
      default: () => [],
    },
    page: {
      type: Number,
      default: 1,
    },
    busy: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    followList_height: {
      type: Number,
      default: 400,
    }
  },
  watch: {
    busy(val) {
      this.busy_new = val
    }
  },
  data() {
    return {
      busy_new: true,
      activeNames: "1",
      show_dialog_pictures: false, // 查看已上传的图片
    }
  },
  created() {
    console.log(this.busy);
  },
  computed: {
  getName: function() {
    return function(activity) {
      if (activity.client_clue.refer_dy_name !== '') {
        let setname = ""
        if(activity.client_clue.platform==1){
          setname = "腾房云"
        }else if (activity.client_clue.platform==2){
          setname = "百度基木鱼"
        }else if (activity.client_clue.platform==3){
          setname = "分销报备"
        }else if (activity.client_clue.platform==4){
          setname = "抖音"
        }else if (activity.client_clue.platform==5){
          setname = "T+"
        }else if (activity.client_clue.platform==6){
          setname = "快手"
        }else if (activity.client_clue.platform==7){
          setname = "海知道"
        }else if (activity.client_clue.platform==8){
          setname = "企业微信"
        }else if (activity.client_clue.platform==9){
          setname = "小程序"
        }else if (activity.client_clue.platform==10){
          setname = "外呼智机器人"
        }else if (activity.client_clue.platform==11){
          setname = "微信视频号"
        }
        return setname + '名称：' + activity.client_clue.refer_dy_name;
      } else {
        // 其他逻辑
        return '';
      }
    }
  }
},
  methods: {
    loadMoreFollow() {
      this.$emit("loadmore", this.page);
    },
    handleChange(val) {
        console.log(val);
        console.log(this.activeNames);
      },
    OpenVideo(url){
      window.open(url);
    },
    // 查看已上传的图片
    handlePictureCardPreview(item) {
        // console.log(item,'item');
        this.show_dialog_pictures = true;
        if (item.url) {
          this.dialog_pictures_src = item.url;
        } else {
          this.dialog_pictures_src = item;
        }
    },
    copyaddress(activity){
      this.$onCopyValue(activity.client_clue.action_record.video_url);
    },
  },
};
</script>

<style scoped lang="scss">
.spancolor {
  color: #8a929f;
  white-space: pre-line;
  ::v-deep .el-collapse{
    border: 0px solid #ffffff;
  }
  ::v-deep .el-collapse-item__header {
              border: 0px solid #ffffff;
          }
  ::v-deep .el-collapse-item__arrow {
    margin: 5px;
  }
  ::v-deep.el-collapse-item__content{
    padding-bottom:0px;
  }
  ::v-deep.el-collapse-item__wrap{
    border-bottom: 0px solid #EBEEF5;
  }
    .content{
      width: 96%;
      padding: 13px;
      border-radius:8px;
      background-color: #f6f6f6;
      .follow-picture{
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        position: relative;
      }
      .img{
        width: 30px;
        height: 30px;
      }
      .uploader-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      position: absolute;
      width:30px;
      height: 30px;
      left: 0;
      top: 0;
      // border-radius: 4px;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      transition: opacity 0.3s;
      margin-left: 52px;

      .uploader-actions-item {
        font-size: 20px;
        cursor: pointer;

        & i {
          color: #fff;
        }
      }
    }

    .uploader-actions:hover {
      opacity: 1;
    }
    }
    .SourceCard{
      width: 50%;
      height: 95px;
      background-color: #f6f6f6;
      border-radius: 4px;
      margin-top: 10px;
      display: flex;
      overflow: hidden;
      .CoverImage{
        width: 70px;
        height: 70px;
        border: 1px solid #f6f6f6;
        margin: 11px 10px 10px 10px;
        border-radius: 4px;
        position: relative;
        img{
          width: 70px;
          height: 70px;
        }
        .uploader-actions {
         display: flex;
         flex-direction: row;
         justify-content: center;
         align-items: center;
         position: absolute;
         width:70px;
         height: 70px;
         left: 0;
         top: 0;
         // border-radius: 4px;
         cursor: default;
         text-align: center;
         color: #fff;
         opacity: 0;
         font-size: 20px;
         background-color: rgba(0, 0, 0, 0.5);
         transition: opacity 0.3s;
         .uploader-actions-item {
           font-size: 20px;
           cursor: pointer;

           & i {
             color: #fff;
           }
         }
    }
    .uploader-actions:hover {
      opacity: 1;
    }
      }
      .Livetitle{
        margin: 10px 10px 10px 10px;
        cursor: pointer;
      }
    }
}
.clue-content {
  max-width: 830px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.agent_info {
  margin: -10px 0 24px 0;
  .time {
    margin-right: 10px;
    color: #8a929f;
  }
  .img {
    margin-right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .agent_name {
    font-size: 12px;
    color: #8a929f;
  }
}
.f_content {
  &.red {
    color: #fc0606;
  }
}
.mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 0px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
      object-fit: cover;
    }

    .img {
      max-width: 800px;
      height: 600px;
      overflow-y: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
</style>
