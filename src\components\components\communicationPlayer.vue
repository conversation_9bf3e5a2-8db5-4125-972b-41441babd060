<template>
    <div>
        <audio :src="record_url" @timeupdate="updateProgress" controls ref="audioRef" style="display: none"
            class="audios"></audio>
        <div class="container flex-row" :style="style_container">
            <div class="audio-right" :style="style_audioRight">
                <!-- <i 
                    @click="playAudio" class="dialogAudioPlay el-icon-video-play"
                >
                </i> -->
                <div @click="playAudio($event)" class="dialogAudioPlay">
                    <img v-if="!activity.is_play" :data-index="indexs" src="@/assets/<EMAIL>" alt="" />
                    <img v-else :data-index="indexs" src="@/assets/<EMAIL>" alt="" />
                </div>
                <div class="progress-bar-bg_c">
                    <div class="progress-bar-bg" id="progressBarBg" v-dragto="setAudioIcon"></div>
                    <div class="progress-bar" id="progressBar" @click="setProgress"></div>
                    <div class="progress-btn" id="progressBtn" @mousedown="handleProgress"></div>
                </div>
                <div class="audio-time" style="min-height: 10px">
                    <span id="audioCurTime">
                        {{ audioStart }}
                    </span>
                    /
                    <span>{{ duration_time | formatTime }}</span>
                </div>
                <div class="volume">
                    <div @click.stop="() => { return false }" class="volume-progress" v-show="audioHuds">
                        <div class="volume-bar-bg" id="volumeBarBg" v-adjuster="handleShowMuteIcon">
                            <div class="volume-bar" id="volumeBar"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="download" @click="getDeatil()" v-if="show_analysis">
                <!-- @click="DownloadVoice" -->
                <!-- <img src="@/assets/<EMAIL>" alt="">
                下载录音 -->
                录音分析
            </div>
            <div v-if="speed" class="speed-box flex-row">
                <div class="column" :class="current_speed == item.value ? 'actives' : ''" v-for="item in speed_type"
                    :key="item.value" @click="changeSpeed(item.value)">
                    {{ item.label }}
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        // 当前点击的数据
        activity: {
            type: Object,
            default: () => { }
        },
        // 当前点击的Index
        indexs: {
            type: [String, Number],
            default: 0
        },
        // 控制拖动范围，如果超出范围停止拖动
        style_line: {
            type: [Number, String],
            default: 540
        },
        // 类名audio-right的宽度和背景色
        style_audioRight: {
            type: String,
            default: 'width: 732px; background: #F6F6F6;'
        },
        // 类名container的margin值
        style_container: {
            type: String,
            default: 'margin-top: 13px;'
        },
        // 录音开始时间
        begin_time: {
            type: [String, Number],
            default: ''
        },
        // 录音结束时间
        end_time: {
            type: [String, Number],
            default: ''
        },
        // 显示/隐藏播放速度
        speed: {
            type: Boolean,
            default: false
        },
        // 播放录音的src
        record_url: {
            type: String,
            default: ''
        },
        // 播放录音的持续时间
        duration_time: {
            type: Number,
            default: 0
        },
        // 当前客户id
        info_id: {
            type: [String, Number],
            default: ''
        },
        // 当前客户是1公海/2私客
        type: {
            type: [String, Number],
            default: ''
        },
        // 控制录音分析按钮显示/隐藏
        show_analysis: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            audioStatus: 'play',
            audioStart: '00:00',
            duration: '00:00',
            audioVolume: 0.5,
            audioHuds: false,
            componentIndex: 0,
            // 播放速度类型
            speed_type: [
                { label: 'X1.0', value: 1 },
                { label: 'X1.5', value: 2 },
                { label: 'X2.0', value: 3 },
            ],
            current_speed: 1, // 当前播放速度类型
        }
    },
    directives: {
        dragto: {
            inserted: function (el, binding, vnode) {
                el.addEventListener('click', (e) => {
                    vnode.context.componentIndex = e.target.index
                    let wdiv = document.querySelectorAll(".progress-bar-bg")[vnode.context.componentIndex].clientWidth;
                    let audio = vnode.context.$refs.audioRef;
                    // 只有录音开始播放后才可以调节，已经播放过但暂停了的也可以
                    let ratemin = e.offsetX / wdiv;
                    let rate = ratemin * 100;
                    document.querySelectorAll('.progress-bar')[vnode.context.componentIndex].style.width = rate + '%';
                    audio.currentTime = audio.duration * ratemin;
                    audio.pause();
                    // document.querySelectorAll(".dialogAudioPlay")[vnode.context.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                    vnode.context.activity.is_play = false;
                    vnode.context.$forceUpdate();
                    binding.value();
                }, false)
            }
        },
        adjuster: {
            inserted: function (el, binding, vnode) {
                el.addEventListener('click', (e) => {
                    let hdiv = document.getElementById('volumeBaeBg').clientHeight;
                    let audio = vnode.context.$refs.audioRef;
                    // 只有音乐开始播放后才可以调节，已经播放过但暂停了的也可以
                    let ratemin = e.offsetY / hdiv;
                    let rate = ratemin * 100;
                    document.getElementById('volumeBar').style.height = rate + '%';
                    audio.volume = ratemin;
                    binding.value(rate / 100);
                }, false)
            }
        }
    },
    mounted() {
        this.fetch();
        let detail = document.getElementsByClassName("dialogAudioPlay");
        let barBg = document.querySelectorAll(".progress-bar-bg");
        let Bar = document.querySelectorAll(".progress-bar");
        let Btn = document.querySelectorAll(".progress-btn");
        for (let i = 0; i < detail.length; i++) {
            detail[i].index = i;
            barBg[i].index = i;
            Bar[i].index = i;
            Btn[i].index = i;
        }
    },
    watch: {
        // activitys: {
        //     handler(newval,lodval) {
        //         console.log(this.activity )
        //         this.activity = newval
        //     },
        //     immediate:true
        // }
    },
    filters: {
        formatTime(value) {
            // 将数字转换为分钟和秒钟
            var minutes = Math.floor(value / 60);
            var seconds = value % 60;

            // 将分钟和秒钟转换为字符串，并在需要时添加前导零
            var minutesString = minutes.toString().padStart(2, '0');
            var secondsString = seconds.toString().padStart(2, '0');

            // 将时间字符串格式化为00:00格式
            return minutesString + ':' + secondsString;
        },
    },
    methods: {
        // 跳转录音详情
        getDeatil() {
            console.log(this.activity.record_id,'33333');
            this.$goPath(`recording_Details?r_id=${this.activity.record_id}&u_id=${this.info_id}&type=${this.type}`);
            sessionStorage.setItem('record_Detail', JSON.stringify(this.activity)); // 存储当前录音信息
        },
        fetch() {
            let that = this;
            var myVid = this.$refs.audioRef;
            myVid.loop = false;
            // 监听音频播放完毕
            myVid.addEventListener('ended', function () {
                // 显示播放icon
                // document.querySelectorAll(".dialogAudioPlay")[that.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                that.activity.is_play = false;
                that.$forceUpdate();
                document.querySelectorAll('.progress-bar')[that.componentIndex].style.transition = 'none';
                document.querySelectorAll('.progress-btn')[that.componentIndex].style.transition = 'none';
                document.querySelectorAll('.progress-bar')[that.componentIndex].style.width = '0%'; // 初始化进度条
                document.querySelectorAll('.progress-btn')[that.componentIndex].style.left = '0%';
                that.audioStart = '00:00';
            }, false)
            if (myVid != null) {
                myVid.oncanplay = function () {
                    that.duration = that.transTime(myVid.duration) // 计算音频时长
                }
            }
        },
        updateProgress(e) {
            var value = e.target.currentTime / e.target.duration;
            if (document.querySelectorAll('.progress-bar')[this.componentIndex]) {
                document.querySelectorAll('.progress-bar')[this.componentIndex].style.width = value * 100 + '%';
                document.querySelectorAll('.progress-btn')[this.componentIndex].style.left = value * 100 + '%'
                if (e.target.currentTime === e.target.duration) {
                    // document.querySelectorAll(".dialogAudioPlay")[this.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                    this.activity.is_play = false;
                    this.$forceUpdate();
                }
            } else {
                // document.querySelectorAll(".dialogAudioPlay")[this.componentIndex].className = "dialogAudioPlay el-icon-video-play";
                this.activity.is_play = false;
                this.$forceUpdate();
            }
            this.audioStart = this.transTime(this.$refs.audioRef.currentTime)
        },
        playAudio(e) {
            this.componentIndex = e.target.dataset.index;
            let recordAudio = this.$refs.audioRef; // 获取audio元素
            if (recordAudio.paused) {
                // 让所有播放按钮暂停
                let audios = document.querySelectorAll(".audios");
                audios.forEach(item => {
                    item.pause();
                })
                this.$emit("resetPlay", "");
                recordAudio.play();
                this.activity.is_play = true;
                this.$forceUpdate();
                document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'width 0.3s linear';
                document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'left 0.3s linear';
            } else {
                recordAudio.pause();
                this.activity.is_play = false;
                this.$forceUpdate();
                document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
                document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
            }
            this.audioStart = this.transTime(this.$refs.audioRef.currentTime);
        },
        /*
            音频播放时间换算
            @param {number} value - 音频当前播放时间，单位秒
        */
        transTime(time) {
            var duration = parseInt(time);
            var minute = parseInt(duration / 60);
            var sec = (duration % 60) + '';
            var isM0 = ':';
            if (minute === 0) {
                minute = '00';
            } else if (minute < 10) {
                minute = '0' + minute;
            }
            if (sec.length === 1) {
                sec = '0' + sec;
            }
            return minute + isM0 + sec
        },
        setAudioIcon() {
            // document.querySelectorAll(".dialogAudioPlay")[this.componentIndex].className = "dialogAudioPlay el-icon-video-play";
            this.activity.is_play = false;
            this.$forceUpdate();
            document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
            document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
            setTimeout(() => {
                document.querySelectorAll(".progress-bar")[this.componentIndex].style.transition = 'none';
                document.querySelectorAll(".progress-btn")[this.componentIndex].style.transition = 'none';
            }, 100)
        },
        handleShowMuteIcon(val) {
            console.log(val);
            this.audioVolume = val;
        },
        handleProgress(e) {
            let that = this;
            this.componentIndex = e.target.index;
            e.stopPropagation();
            e.preventDefault();
            // 进度条
            let audio = this.$refs.audioRef;
            let progressBar = document.querySelectorAll(".progress-bar")[e.target.index];
            let progressBtn = document.querySelectorAll(".progress-btn")[e.target.index];
            audio.pause();
            // document.querySelectorAll(".dialogAudioPlay")[e.target.index].className = "dialogAudioPlay el-icon-video-play";
            this.activity.is_play = false;
            this.$forceUpdate();
            document.querySelectorAll(".progress-bar")[e.target.index].style.transition = 'none';
            document.querySelectorAll(".progress-btn")[e.target.index].style.transition = 'none';
            // 获取圆点偏移量
            let progressLeft = e.clientX - progressBtn.offsetLeft;
            // 圆圈绑定拖拽
            let progressX
            document.onmousemove = function (event) {
                event.stopPropagation();
                event = event || window.event;
                // 获取鼠标坐标
                progressX = event.clientX - progressLeft;
                if (progressX <= 0) {
                    // 如果超出范围停止拖动
                    progressX = 0;
                } else if (progressX >= that.style_line) {
                    progressX = that.style_line;
                }
                let BarBgWidth = document.querySelectorAll('.progress-bar-bg')[e.target.index].offsetWidth;
                let ratemin = progressX / BarBgWidth
                if (!isNaN(ratemin)) {
                    audio.currentTime = audio.duration * ratemin;
                }
                progressBtn.style.left = progressX + 'px';
                progressBar.style.width = progressX + 'px';
            }
            document.onmouseup = function (event) {
                event.stopPropagation();
                document.querySelectorAll('.progress-bar')[e.target.index].style.width = progressX + 'px';
                document.querySelectorAll('.progress-btn')[e.target.index].style.left = progressX + 'px';
                // 获取元素的灰色进度条宽度
                let BarBgWidth = document.querySelectorAll('.progress-bar-bg')[e.target.index].offsetWidth;
                let ratemin = progressX / BarBgWidth
                if (!isNaN(ratemin)) {
                    audio.currentTime = audio.duration * ratemin;
                }
                document.querySelectorAll('.progress-bar')[e.target.index].style.transition = 'width 0.3s linear';
                document.querySelectorAll('.progress-btn')[e.target.index].style.transition = 'left 0.3s linear';
                // 取消鼠标移动事件
                document.onmousemove = null;
                // 取消鼠标抬起事件
                document.onmouseup = null;
            }
        },
        setProgress(e) {
            document.querySelectorAll('.progress-bar')[e.target.index].style.transition = 'none';
            document.querySelectorAll('.progress-btn')[e.target.index].style.transition = 'none';
            let wdiv = document.querySelectorAll('.progress-bar-bg')[e.target.index].clientWidth;
            let audio = this.$refs.audioRef;
            let ratemin = e.offsetX / wdiv
            let rate = ratemin * 100;
            document.querySelectorAll('.progress-bar')[e.target.index].style.width = rate + '%';
            audio.currentTime = audio.duration * ratemin;
            audio.pause();
            // document.querySelectorAll(".dialogAudioPlay")[e.target.index].className = "dialogAudioPlay el-icon-video-play";
            this.activity.is_play = false;
            this.$forceUpdate();
            setTimeout(() => {
                document.querySelectorAll('.progress-bar')[e.target.index].style.transition = 'width 0.3s linear'
                document.querySelectorAll('.progress-btn')[e.target.index].style.transition = 'left 0.3s linear'
            }, 100)
        },
        // 下载录音
        DownloadVoice() {
            // console.log("下载录音", this.activity.record_url);
            window.open(this.activity.record_url);
        },
        // 处理开始播放时间(父组件调用)
        beginTime() {
            // console.log(this.begin_time,"开始时间")
            let audio = this.$refs.audioRef;
            audio.currentTime = this.begin_time;
            audio.play();
            this.activity.is_play = true;
        },
        // 改变播放速度类型
        changeSpeed(value) {
            this.current_speed = value;
            let audio = this.$refs.audioRef;
            audio.playbackRate = value;
        },
        // 父组件更新子组件显示
        upPlay() {
            this.$forceUpdate();
        }
    }
}
</script>
<style scoped lang="scss">
.container {
    display: flex;
    flex-direction: row;
    align-items: center;

    // margin-top: 13px;
    // justify-content: space-between;
    .download {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 30px;
        box-sizing: border-box;
        border-radius: 5px;
        color: #8A929F;
        background: #FFFFFF;
        border: 1px solid #DDE1E9;
        margin-left: 12px;
        cursor: pointer;

        img {
            width: 14px;
            height: 14px;
            margin-right: 5px;
        }

        &:hover {
            background: #fff;
            border-color: #409eff;
            color: #409eff;
        }
    }

    .speed-box {
        border-radius: 5px;
        color: #8A929F;
        font-size: 12px;
        margin-left: 24px;
        cursor: pointer;

        .column {
            padding: 6px 12px;
            border-top: 1px solid #DDE1E9;
            border-bottom: 1px solid #DDE1E9;
        }

        .column:first-child {
            border: 1px solid #DDE1E9;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
        }

        .column:last-child {
            border: 1px solid #DDE1E9;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
        }

        .actives {
            background-color: #2D84FB;
            border-color: #2D84FB;
            color: #fff;
        }
    }
}

.volume {
    position: relative;

    .volume-progress {
        position: absolute;
        top: -150px;
        width: 32px;
        height: 140px;
        background: #f6f6f6;
        border-radius: 4px;
        padding-top: 10px;

        .volume-bar-bg {
            margin: 0 auto;
            width: 6px;
            height: 120px;
            background: #dcdcdc;
            border-radius: 100px;
            flex: 1;
            position: relative;
            transform: rotate(180deg);
            cursor: pointer;

            .volume-bar {
                width: 6px;
                height: 50%;
                background: #56bf8b;
                border-radius: 100px;
            }
        }
    }
}

.audio-right {
    // width: 732px;
    height: 48px;
    line-height: 49px;
    border-radius: 24px;
    display: flex;
    box-sizing: border-box;
    padding: 0 27px 0 16px;

    // background: #F6F6F6;
    .dialogAudioPlay {
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;
        color: #5c5e66;

        img {
            width: 30px;
            height: 30px;
        }
    }

    .audio-time {
        overflow: hidden;
        font-size: 14px;
        color: #2E3C4E;
    }

    .progress-bar-bg_c {
        flex: 1;
        position: relative;
        margin: 0 16px 0 12px;

        .progress-bar-bg {
            height: 6px;
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            border-radius: 3px;
            background: #C9DAEB;
            transform: translateY(-50%);
            margin-top: -1px;
            cursor: pointer;
        }

        .progress-bar {
            width: 0%;
            height: 6px;
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            background-color: #2D84FB;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
            cursor: pointer;
        }

        .progress-btn {
            width: 14px;
            height: 14px;
            background: #FFFFFF;
            box-shadow: 0px 0px 8px 0px #0000003F;
            border-radius: 50%;
            position: absolute;
            top: 34%;
            box-sizing: border-box;
            cursor: pointer;
        }
    }
}
</style>