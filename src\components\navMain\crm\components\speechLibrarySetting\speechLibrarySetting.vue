<template>
    <div class="content">
        <el-form ref="form" :model="params" label-width="200px" v-loading="loading">
            <el-form-item label="话术库开启状态">
                <el-radio v-model="params.show_words_package_by_phone" :label="0">关闭</el-radio>
                <el-radio v-model="params.show_words_package_by_phone" :label="1">开启</el-radio>
            </el-form-item>
            <el-form-item label="CRM话术库打开方式">
                <el-radio-group v-model="params.show_words_package_type" class="vertical-radio-group">
                    <el-radio :label="1">查看电话手动打开话术库（点按钮显示右边栏）</el-radio><br/>
                    <el-radio :label="2">查看电话自动弹出话术库（自动弹出右边栏）</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="创建团队话术库权限成员范围">
                <tMemberSelect v-model="params.create_words_package_auth" multiple placeholder="请选择成员" style="width: 360px;"/>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSubmit" :loading="submiting">确定</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script> 
import tMemberSelect from '@/components/tplus/tSelect/tMemberSelect'
export default {
    components: {
        tMemberSelect
    },
    data() {
        return {
            loading: false,
            submiting: false,
            params: {
                show_words_package_by_phone: 0,              //话术库开启状态
                show_words_package_type: 1,                  //CRM话术库打开方式
                create_words_package_auth: [],               //创建团队话术库权限成员范围
            },
            numdata:"",//团队话术库数量
        }
    },
    created() {
        this.getSetting();
        this.getnumber()
    },
    methods: {
        getnumber(){
            this.$http.getSpeechnumber().then((res)=>{
                if(res.status==200){
                    this.numdata = res.count
                }
            })
        },
        async getSetting(){
            this.loading = true;
            const res = await this.$http.getSpeechLibraryConfig().catch(e=>{})
            this.loading = false;
            if(res && res.status === 200){
                const conf = res.data;
                conf.create_words_package_auth = (conf.create_words_package_auth || []).map(e=>e.id);
                this.params = conf;
            }
        },
        async onSubmit(){
            if(this.numdata==0){
                this.$message.warning("请先添加话术库！")
                this.$goPath("/language_copy");
            }else{
                const params = {...this.params}
                params.create_words_package_auth = params.create_words_package_auth.join(',')
                this.submiting = true;
                const res = await this.$http.saveSpeechLibraryConfig(params).catch(e=>{})
                this.submiting = false;
                if(res && res.status === 200){
                    this.$message.success('保存成功')
                }
            }
           
        }
    }
}
</script>
<style scoped lang="scss">
.content {
    padding: 0 20px;
    .vertical-radio-group{
        .el-radio{
            margin-top: 13px;
        }
    }
}
</style>