<template>
<div>
    <editReport @editReportDone="onEditReportDone" v-if="isEditReport"/>
   
    <div class="tab-content-container" v-else>
        <div class="tab-content-body topped">
            <div class="tab-content-body-inner">
                <tWaterMaker ref="tWaterMaker">
                    <viewReport ref="viewReport"/>
                </tWaterMaker>
            </div>
        </div>

        <div class="tab-content-footer report-footer" v-if="hasReportGeneralPermission">
            <template v-if="!isReportCanceled">
                <el-button @click="printReport">打印本页</el-button>
                <template v-if="isReportManager">
                    <el-button type="primary" @click="editReport">修改报告</el-button>
                    <el-button type="danger" @click="dialogs.cancelReport=true">作废</el-button>
                </template>    
            </template>
            <el-button  disabled v-else>已作废</el-button>
        </div>
    </div>

    <el-dialog title="提示" :visible.sync="dialogs.cancelReport" width="500px">
        <p>确定要作废该报告吗？</p>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogs.cancelReport = false" size="medium">取消</el-button>
            <el-button type="primary" @click="cancelReport(1)" size="medium">作废并撤销成交状态</el-button>
            <el-button type="primary" @click="cancelReport(0)" size="medium">直接作废</el-button>
        </div>
    </el-dialog>

</div>
</template>
  
<script>
import editReport from "./components/add_report.vue";
import viewReport from "./components/view.vue";
import tWaterMaker from "@/components/tplus/tWaterMaker/index.vue";
export default {
    name: "crm_deal_report_detail",
    components: {
        editReport, viewReport, tWaterMaker
    },
    data() {
        return {
            loading: false,
            reportId: 0,                     //报告id
            reportData: {},                  //报告数据
            isEditReport: false,
            isReportTrader: false,           //是否报告成交人
            isReportManager: false,          //是否报告管理员
            dialogs: {
                cancelReport: false
            }
        };
    },
    computed: {
        //报告普通权限：打印
        hasReportGeneralPermission(){
            return this.isReportTrader || this.isReportManager;
        },
        isReportCanceled(){
            return this.reportData?.deal?.status == '作废';
        }
    },
    created(){
        
    },
    mounted() {
        //当前登录成员名作为水印
        this.$store.dispatch('queryMyInfo', (myInfo)=>{
            const today = this.$Utils.formatDate(null, 'YYYY-MM-DD');
            this.$refs.tWaterMaker.create(myInfo.user_name + ' '+today);
        });
        this.reportId = this.$route.query.id || 0;
        this.init();
    },
    methods: {
        async init(){
            await this.getReportDetail();
            this.getReportOpPermission();
        },
        async getReportDetail(){
            const data = await this.$refs.viewReport.getReportDetail(this.reportId);
            if(data){
                this.reportData = data;
            }
        },
        //获取报告操作权限
        async getReportOpPermission(){
            //判断是否报告成交人
            this.isReportTrader = localStorage.getItem("admin_id") == this.reportData.admin?.admin_id;
            //判断是否报告管理员
            const roleInfo = await this.$store.dispatch("queryMyRoleInfo");
            if(roleInfo){
                this.isReportManager = roleInfo.is_deal_auth_or_super ? true : false;
            }
        },
        //打印报告
        printReport(){
            this.$refs.viewReport.printReport();
        },
        //修改报告
        editReport(){
            this.isEditReport = true;
        },
        onEditReportDone(){
            this.isEditReport = false;
            this.$nextTick(() => {
                this.getReportDetail();
            });
        },
        //作废报告 is_cancel=1 作废并撤销成交转状态
        async cancelReport(is_cancel = 0){
            const res = await this.$http.cancelCrmDealReport({ids: this.reportId, is_cancel});
            if(res.status == 200){
                this.dialogs.cancelReport = false;
                this.$message.success(res.data?.msg || '作废成功');
                this.goReportIndexPage();
                this.$emitPageRefresh('crm_deal_report_index');
            }
            return;
        },
        //返回成交报告列表
        goReportIndexPage(){
            let name = window.location.href.split("#")[1];
            this.$store.state.closeTab = true;
            eventBus.$emit("closeTab", name);
            this.$goPath('/crm_deal_report_index')
        }
    },
};
</script>
  
<style lang="scss" scoped>
.report-footer{
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: flex-end;
}
</style>