<template>
  <!--  标签库 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div
            class="bottom-border div row"
            style="margin-top: 20px; border-bottom: 1px solid #e2e2e2"
          >
            <span class="text">客户昵称：</span>
            <div>
              <el-input
                clearable
                placeholder="请输入"
                v-model="params.user_name"
                @change="onChangeUsername"
              >
              </el-input>
            </div>
          </div>
          <div
            class="bottom-border div row"
            style="margin-top: 20px; padding-bottom: 0"
          >
            <span class="text">流失时间：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="value1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeTime"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="content-box-crm">
          <el-table
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            v-loading="is_table_loading"
          >
            <el-table-column label="全部客户" v-slot="{ row }">
              <div class="div row m-user">
                <img
                  class="a-avatar"
                  v-if="row.avatar"
                  :src="row.avatar"
                  alt=""
                />
                <div class="a-avatar" v-else>{{ row.client_name[0] }}</div>
                <span>{{ row.client_name }}</span>
              </div></el-table-column
            >
            <!-- <el-table-column
              label="所属员工"
              prop="user_name"
            ></el-table-column> -->
            <el-table-column label="客户状态">
              <span style="color: red">已流失</span>
            </el-table-column>
            <el-table-column
              label="流失时间"
              width="200"
              prop="deleted_at"
            ></el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "crm_my_customer_loss",

  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
        times: "",
      },
      tableData: [],
      is_table_loading: false,
      value1: "",
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    onChangeTime(e) {
      this.params.times = e ? e.join(",") : "";
      this.params.page = 1;
      this.getDataList();
    },

    onChangeUsername() {
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },

    getDataList() {
      this.is_table_loading = true;
      this.$http.getCrmMyCustomerLossData({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .top {
    align-items: center;
  }
  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }
  .bottom-border {
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 20px;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
.m-user {
  align-items: center;
  .a-avatar {
    text-align: center;
    width: 30px;
    height: 30px;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 10px;
    line-height: 30px;
    color: #fff;
    background: #2d84fb;
  }
}
</style>
