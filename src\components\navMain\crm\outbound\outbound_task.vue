<template>
  <div class="outbount">
    <div class="outbount_search">
      <div class="outbount_top align-center flex-row">
        <div
          class="outbount_top_item align-center flex-row mr60 mb12"
          v-for="(item, index) in search_list"
          :key="index"
        >
          <div class="task_name mr10">{{ item.name }}</div>
          <template v-if="item.type == 'select'">
            <el-select
              class="select_name"
              clearable
              :remote="item.remote"
              :filterable="item.remote"
              :remote-method="remoteMethod(item['remoteFn'])"
              v-model="params[item.value]"
            >
              <!-- v-bind="{ remoteMethod: item.remoteFn }" -->
              <el-option
                v-for="sel_item in item.options"
                :key="sel_item.id"
                :label="sel_item.name"
                :value="sel_item.id"
              >
              </el-option>
            </el-select>
          </template>
          <template v-if="item.type == 'date'">
            <el-date-picker
              class="select_name"
              v-model="params[item.value]"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </template>
        </div>
      </div>
      <div class="search_btns align-center flex-row">
        <el-button plain type="info" @click="reset">重置</el-button>
        <el-button type="primary" @click="getTaskPackageList">查询</el-button>
      </div>
    </div>
    <div class="outbount_content flex-row">
      <div class="outbount_content_left">
        <div class="outbount_content_left_date">
          <el-date-picker
            class="select_name"
            v-model="params['task_date']"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </div>
        <div class="outbount_content_left_btn flex-row">
          <el-button
            plain
            type="primary"
            class="flex-1"
            @click="getTaskPackageList"
            >刷新记录状态</el-button
          >
        </div>
        <!-- 任务包列表 -->
        <div class="task_list">
          <div
            class="task_item"
            v-for="item in taskPackageList"
            :key="item.id"
            :class="{ active: current.id == item.id }"
            @click="changeTaskItem(item)"
          >
            <div class="task_title flex-row align-center">
              <div class="task_title_name flex-1">
                {{ item.name }}
              </div>
              <div class="task_title_status">
                {{ item.status | filterStatus }}
              </div>
            </div>
            <div class="task_preview flex-row align-center">
              <div class="task_preview_item flex-1">
                <div class="task_preview_item_top">{{ item.count }}</div>
                <div class="task_preview_item_bottom">总量</div>
              </div>
              <div class="task_preview_item_line"></div>
              <div class="task_preview_item flex-1">
                <div class="task_preview_item_top">{{ item.callNumber }}</div>
                <div class="task_preview_item_bottom">已拨</div>
              </div>
              <div class="task_preview_item_line"></div>
              <div class="task_preview_item flex-1">
                <div class="task_preview_item_top">{{ item.unCallNumber }}</div>
                <div class="task_preview_item_bottom">未拨</div>
              </div>
            </div>
            <div class="task_info">
              <div class="task_info_item flex-row align-center">
                <div class="task_info_item_value">分配来源:</div>
                <div class="task_info_item_name">{{ item.channel_name }}</div>
              </div>
              <div class="task_info_item flex-row align-center matb10">
                <div class="task_info_item_value">分配人:</div>
                <div class="task_info_item_name">{{ item.user_name }}</div>
              </div>
              <div class="task_info_item flex-row align-center">
                <div class="task_info_item_value">分配时间:</div>
                <div class="task_info_item_name">{{ item.created_at }}</div>
              </div>
            </div>
            <div class="sanjiao"></div>
          </div>
        </div>
      </div>
      <div class="outbount_content_right flex-1">
        <div class="outbount_content_right_title flex-row align-center">
          <div class="outbount_content_right_title_con flex-1">
            {{ current.name || "--" }}
          </div>
          <div class="outbount_content_right_title_info flex-row align-center">
            <span>创建时间：</span>
            <span class="mr10">{{ current.import_time || "--" }}</span>
            <span>导入人：</span>
            <span class="mr10">{{ current.import_name || "--" }}</span>
          </div>
        </div>
        <div class="outbount_content_right_nav flex-row align-center">
          <div class="outbount_content_right_nav_item">
            <div class="outbount_content_right_nav_item_t">未拨/已拨/总量</div>
            <div class="outbount_content_right_nav_item_b">
              {{ current.unCallNumber || 0 }}/{{ current.callNumber || 0 }}/{{
                current.count || 0
              }}
            </div>
          </div>
          <div class="outbount_content_right_nav_item">
            <div class="outbount_content_right_nav_item_t">通话总时长</div>
            <div class="outbount_content_right_nav_item_b">
              {{ staticInfo.totalDuration | fillterSecond }}
            </div>
          </div>
          <div class="outbount_content_right_nav_item">
            <div class="outbount_content_right_nav_item_t">
              平均通话时长(秒)
            </div>
            <div class="outbount_content_right_nav_item_b">
              {{ staticInfo.avgDuration || 0 }}
            </div>
          </div>
        </div>
        <outboundTask ref="table" :current="current"></outboundTask>
      </div>
    </div>
  </div>
</template>

<script>
import outboundTask from "./components/outbound_task.vue";
export default {
  components: { outboundTask },
  data() {
    return {
      search_list: [
        {
          name: '任务包名',
          type: "select",
          value: 'clue_id',
          options: [
            {
              label: '任务包1',
              value: 1,
              id: 1
            },
            {
              label: '任务包2',
              value: 2,
              id: 2
            }
          ]
        },
        {
          name: '任务状态',
          type: "select",
          value: 'status',
          options: [
            {
              name: '全部状态',
              id: ''
            },
            {
              name: '未进行',
              id: 0
            },
            {
              name: '进行中',
              id: 1
            },
            {
              name: '已完成',
              id: 2
            }
          ]
        }
        , {
          name: '线索来源',
          type: "select",
          value: 'channel_id',
          options: [

          ]
        },
        {
          name: '分配人',
          type: "select",
          value: 'operate_id',
          remote: true,
          remoteFn: null,
          options: [

          ]
        },
        {
          name: '分配时间',
          type: "date",
          value: 'task_date',
          options: []
        }
      ],
      params: {
        clue_id: '',
        status: '',
        channel_id: ""
        // task_date: []

      },
      taskPackageList: [],
      current: {},
      staticInfo: {
        totalDuration: 0,
        avgDuration: 0,
      }

    }
  },
  filters: {
    filterStatus(val) {

      if (val === undefined) return ''
      let name = ''
      switch (Number(val)) {
        case 0:
          name = "未进行"
          break;
        case 1:
          name = "进行中"
          break;
        case 2:
          name = "已完成"
          break;
        default:
          name = ''
          break;
      }
      return name
    },
    fillterSecond(val) {
      let time = '', h = 0, m = 0, s = 0, y = 0
      if (val && val >= 0) {
        if (val >= 3600) {
          h = Math.floor(val / 3600)
          y = val % 3600
          m = Math.floor(y / 60)
          s = y % 60
        }
        else if (val >= 60) {
          h = 0
          m = Math.floor(val / 60)

          s = val % 60


        } else {
          h = 0
          m = 0
          s = val
        }

      }
      time = `${h}:${m}:${s}`
      return time
    },
  },
  created() {

    //获取线索来源
    this.getChannelList()
    // 获取分配人列表
    this.getUserList()
    this.getTaskPackageList()
  },

  methods: {
    getPackageList() {
      this.$http.getCallClues({}).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.search_list[0].options = res.data.data
        }
      })
    },
    getChannelList() {
      this.$http.getChannel({}).then(res => {
        if (res.status == 200) {
          this.search_list[2].options = res.data.data
        }
      })
    },
    getUserList(e) {
      let user_name = ''
      if (e) {
        user_name = e
      }
      this.search_list[3].remoteFn = 'getUserList'
      this.$http.getManagerAuthList({ type: 0, per_page: 1000, page: 1, user_name }).then(res => {
        console.log(res, 1111111);
        if (res.status == 200) {
          res.data.data.map(item => {
            item.name = item.user_name
            return item
          })
          this.search_list[3].options = res.data.data
        }
      })
    },
    getCallInfo() {
      this.$http.getCallTaskStatistics({ member_task_id: this.current.id }).then(res => {
        console.log(res, 11232);
        if (res.status == 200) {
          this.staticInfo = res.data
        }
      })
    },

    getTaskPackageList() {
      let params = Object.assign({}, this.params)
      if (params.task_date && params.task_date.length) {
        params.stime = params.task_date[0]
        params.etime = params.task_date[1]
      }
      delete params.task_date
      this.$http.getTaskPackageList(params).then(res => {
        if (res.status == 200) {
          this.taskPackageList = res.data.data

          if (res.data.data.length) {
            this.search_list[0].options = res.data.data
            this.current = res.data.data[0]
            //获取通话时长
            this.getCallInfo()
          } else {
            this.current = {
              id: ''
            }
            this.staticInfo = {
              totalDuration: 0,
              avgDuration: 0
            }
          }
          // this.current = res.data.data[0]
        }
      })
    },
    changeTaskItem(item) {
      this.current = item
      this.getCallInfo()
    },
    remoteMethod(e, fn) {
      console.log(fn);
      if (e) {
        fn && this[fn]()
      }
      // 
    },
    reset() {
      this.params.name = ''
      this.params.task_date = []
      this.params.status = ''
      this.params.clue_id = ''
    }
  }


}
</script>

<style scoped lang="scss">
.mr10 {
  margin-right: 10px;
}
.mr60 {
  margin-right: 60px;
}
.mb12 {
  margin-bottom: 12px;
}
.matb10 {
  margin: 10px 0;
}
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
.outbount_search {
  padding: 20px;
  background: #fff;
}
.outbount_top {
  width: 1200px;
  flex-wrap: wrap;
  .outbount_top_item {
    .task_name {
      color: #2e3c4e;
      font-size: 14px;
    }
    .select_name {
      width: 260px;
    }
  }
}
.outbount_content {
  background: #fff;
  margin-top: 5px;
  padding-top: 10px;
  .outbount_content_left {
    .outbount_content_left_date {
    }
    .outbount_content_left_btn {
      margin: 40px 0;
    }
  }
}
.outbount_content_left {
  padding: 5px 20px;
}
.task_list {
  .task_item {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border: 1px solid #e1e1e1;
    margin-bottom: 10px;
    position: relative;
    .sanjiao {
      position: absolute;
      top: 50%;
      right: -20px;
      transform: translate(0, -50%);
      width: 0;
      height: 0;
      border: 10px solid transparent;
    }
    &.active {
      .task_title {
        background: #2d84fb;
        color: #fff;
      }
      border: 1px solid #2d84fb;
      .sanjiao {
        border-left: 10px solid #2d84fb;
      }
    }
    .task_title {
      background: #e1e1e1;
      padding: 10px 14px;
      color: #2e3c4e;
      // .task_title_name {
      // }
      // .task_title_status {
      // }
    }
    .task_preview {
      margin-top: 20px;
      border-bottom: 1px solid #e1e1e1;
      .task_preview_item {
        text-align: center;
        padding: 16px 0;
        .task_preview_item_top {
          color: #2e3c4e;
          font-weight: 600;
          font-size: 14px;
          text-align: center;
        }
        .task_preview_item_bottom {
          margin-top: 4px;
          color: #8a929f;
          font-size: 14px;
        }
      }
      .task_preview_item_line {
        height: 30px;
        width: 1px;
        min-width: 1px;
        margin-bottom: 20px;
        align-self: flex-end;
        background: #e1e1e1;
      }
    }
    .task_info {
      padding: 16px;
      .task_info_item {
        color: #8a929f;
        font-size: 14px;
        .task_info_item_value {
          margin-right: 5px;
        }
        // .task_info_item_name {
        // }
      }
    }
  }
}
.outbount_content_right {
  padding: 10px 25px;
  .outbount_content_right_title {
    .outbount_content_right_title_con {
      color: #2e3c4e;
      font-size: 20px;
      font-weight: 500;
    }
    .outbount_content_right_title_info {
      color: #8a929f;
      font-size: 14px;
    }
  }
  .outbount_content_right_nav {
    padding: 20px;
    &_item {
      width: 33%;
      max-width: 333px;
      &_t {
        color: #8a929f;
        font-size: 14px;
      }
      &_b {
        margin-top: 10px;
        font-weight: 600;
        color: #2e3c4e;
        font-size: 24px;
      }
    }
  }
}
</style>