<template>
  <div class="pages" v-fixed-scroll="62">
    <template v-if="is_tabs !== 'wxwork'">
      <!--:is 的作用：会将div标签转换成 currentView 变量绑定的这个组件-->
      <!-- <div :is="is_tabs" keep-alive></div> -->
      <div id="filter">
        <div class="div b-tabs row">
          <customTabs v-model="params.c_type4" @change="handleCustomTabChange" @changeOut="handleCustomTabChangeOut"
            :type="3" :content-loaded="!is_table_loading">
            <div v-for="item in customer_list_type" :key="item.id" @click="getDataLists(item)" class="b-t-item"
              :class="{ isactive: params.c_type4 == item.id }">
              <span v-if="item.is_select != true">{{ item.title }}</span>
              <span v-else>
                {{ item.title }}
              </span>
            </div>
          </customTabs>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <div class="head-list" style="margin-left: 0px;" :class="{'t-select-group':datetime_type}">
              <el-select class="crm-selected-label" clearable v-model="datetime_type" placeholder="时间类型" :style="{
                              minWidth: '30px',
                              width: getSelectWidth(customerLabelList),
                            }" @change="onClickType($event, 4)">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-select v-model="params.date_sort" class="crm-selected-label short" style="width: 78px;"
                @change="handleSearch" v-show="datetime_type">
                <el-option label="降序" :value="1"></el-option>
                <el-option label="升序" :value="2"></el-option>
              </el-select>
            </div>
            <!-- 时间检索 -->
            <div class="block head-list">
              <!-- <span class="demonstration">带快捷选项</span> -->
              <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                @change="onChangeTime">
              </el-date-picker>
            </div>
            <!-- <div class="head-list" v-if="website_id!=1">
              <el-select
              class="crm-selected-label"
              clearable
              v-model="Source_statusvalue"
              placeholder="客户来源"
              :style="{
                minWidth: '20px',
                width: '130px',
              }"
              @change="onClickType($event, 1)"
            >
              <el-option
                v-for="item in source_list"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              >
              </el-option>
            </el-select>
            </div> -->
            <!-- 客户来源 -->
            <div class="head-list" style="margin-top: 10px;">
              <!-- <el-cascader class="crm-selected-label" :style="{
                                  minWidth: '20px',
                                  width: '130px',
                                }" v-model="Source_statusvalue" placeholder="客户来源" :options="source_list"
                @change="onClickType($event, 1)" :props="{
                                      label: 'title',
                                      value: 'id',
                                      children: 'children',
                                      checkStrictly: true,
                                      emitPath: false,
                                    }" clearable>
              </el-cascader> -->
              <el-cascader class="crm-selected-label"
              size="small " 
               :style="{
                 minWidth: '20px',
                 width: '165px',
               }" v-model="Source_statusvalue" placeholder="客户来源" :options="source_list" @change="onClickType($event, 1)"
                :props="{
                  label: 'title',
                  value: 'id',
                  children: 'children',
                  checkStrictly: false,
                  emitPath: false,
                  multiple:true,
                }" clearable
                collapse-tags>
              </el-cascader>
            </div>
            <!-- 客户状态 -->
            <div class="head-list">
              <el-cascader
              size="small " class="crm-selected-label" v-model="customer_statusvalue" placeholder="客户状态"
                :options="tracking_list" :style="{
                  minWidth: '20px',
                  width: '165px',
                }" :props="{
                  value: 'id',
                  label: 'title',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }" collapse-tags clearable  @change="onClickType($event, 2)"></el-cascader>
            </div>
            <!-- <div v-else class="head-list">
              <el-select class="crm-selected-label" clearable v-model="customer_statusvalue" placeholder="客户状态" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 2)">
                <el-option v-for="item in tracking_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div> -->
            <!-- 客户类型 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="typeLabel_value" placeholder="客户类型" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 6)">
                <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 客户等级 -->
            <div class="head-list">
              <el-cascader
              size="small " class="crm-selected-label" v-model="grade_value" placeholder="客户等级"
                :options="level_list" :style="{
                  minWidth: '20px',
                  width: '130px',
                }" :props="{
                  value: 'id',
                  label: 'title',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }" collapse-tags clearable @change="onClickType($event, 5)"></el-cascader>
              <!-- <el-select class="crm-selected-label" clearable v-model="grade_value" placeholder="客户等级" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 5)">
                <el-option v-for="item in level_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select> -->
            </div>
            <!-- 绑定企微 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="typeqiwei_value" placeholder="绑定企微" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 3)">
                <el-option v-for="item in bind_list" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 通话状态 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="customerstatus_value" placeholder="通话状态" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 7)">
                <el-option v-for="item in statusdata_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 带看状态 -->
            <div class="head-list">
              <el-select class="crm-selected-label" clearable v-model="showstatus_value" placeholder="带看状态" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="onClickType($event, 9)">
                <el-option v-for="item in showstatus_list" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- 部门 -->
            <div class="head-list" style="margin-top:6px">
              <el-cascader class="crm-selected-label" v-model="params.department_id" placeholder="请选择部门" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" :options="AllDepartment" @change="changePopDepar" @focus="Reqdepartment" :clearable="true"
                :show-all-levels="false" :props="{
                                  label: 'name',
                                  value: 'id',
                                  children: 'subs',
                                  checkStrictly: true,
                                  emitPath: false,
                                }">
              </el-cascader>
            </div>
            <!-- 成员 -->
            <div class="head-list">
              <el-select class="crm-selected-label" v-model="selectedMember" placeholder="请选择成员" :style="{
                              minWidth: '20px',
                              width: '130px',
                            }" @change="changeSearchMember" filterable clearable @focus="RequestMembers">
                <el-option v-for="item in filtrMember" :key="item.id" :label="item.user_name" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <!-- <span class="text">客户来源：</span>
            <myLabel labelKey="title" :arr="source_list" :activeIndex="source_index" @onClick="onClickType($event, 1)">
            </myLabel> -->
          </div>
          <!-- <div class="bottom-border div row" style="padding-top: 24px">
            <span class="text">客户状态：</span> -->
          <!-- <div class="label-item" v-for="(item, index) in tracking_list" :key='item.id'
            :class="{ isactive: index == activeIndex }" @click="onClick(item, index)">
            {{ item.title }}
          </div> -->
          <!-- <myLabel labelKey="title" :arr="tracking_list" :activeIndex="tracking_index"
              @onClick="onClickType($event, 2)"></myLabel>
          </div> -->
          <div class="bottom-border" style="padding-top: 24px;border-bottom: 0px dashed #e2e2e2;">
            <!-- <span class="text">客户标签：</span> -->
            <div class="label_list div row">
              <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="clearlabel">
                客户标签
              </span>
              <div v-for="item in label_list" :key="item.id" class="label_item">
                    <el-dropdown trigger="click" @command="handleCommand($event,item)">
                      <div class="namedata">
                        <span class="selectedname">{{item.name}}<i class="el-icon-arrow-down el-icon--right"></i></span>
                      </div>
                      <el-dropdown-menu slot="dropdown" style="max-height: 300px !important;overflow-y: auto !important;">
                        <el-dropdown-item v-for="(label, index) in item.label" :key="index"
                        :command="label">{{ label.name }}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
              </div>
            </div>
            <div class="alllabeldatastyle">
              <div class="div row">
                <div class="labeldatastyle" v-for="(arr,index) in labeldata" :key="index">
                <el-tag type="info">{{ arr.title+"/"+arr.name }}
                  <i class="el-icon-error" style="font-size:15px;" @click="dellabeld(index,arr.id)"></i></el-tag>
              </div>
              </div>
              <div class="labelerr">
                <el-button v-if="labeldata.length" type="primary" size="mini" icon="el-icon-delete"
                @click="clearlabel"></el-button>
              </div>
            </div>
          </div>
          <!-- <div v-else class="bottom-border div row" style="padding-top: 24px;border-bottom: 0px dashed #e2e2e2;"> -->
            <!-- <span class="text">客户标签：</span> -->
            <!-- <div class="label_list div row">
              <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="getAllLabelList">
                客户标签
              </span>
              <span v-for="(item, index) in label_list" :key="index" class="label_item">
                <el-select class="selected-label" v-model="customerLabelList[item.id]" @change="changeCustomerLabel"
                  :placeholder="item.name" :style="{
                                      minWidth: '40px',
                                      width: getSelectWidth(item, customerLabelList[item.id]),
                                      background: changeParentLabel == item.id ? '#E8F1FF' : '',
                                    }">
                  <el-option v-for="arr in item.label" :key="arr.id" :label="arr.name" :value="[arr.id, arr, item]">
                  </el-option>
                </el-select>
              </span>
            </div>
          </div> -->
          <!-- 折叠面板 -->
          <!-- <myCollapse :isActive="is_collapse">
            <template v-slot:content>
              <div class="bottom-border div row" style="padding-top: 24px">
                <span class="text">客户类型：</span>
                <myLabel labelKey="title" :arr="type_list" :activeIndex="type_index" :isdesc="true"
                  @onClick="onClickType($event, 6)"></myLabel>
              </div>
              <div class="bottom-border div row" style="padding-top: 24px">
                <span class="text">绑定企微：</span>
                <myLabel labelKey="name" :arr="bind_list" :activeIndex="bind_index" @onClick="onClickType($event, 3)">
                </myLabel>
              </div>
              <div class="bottom-border div row" style="padding-top: 24px">
                <span class="text">客户等级：</span>
                <myLabel labelKey="title" :arr="level_list" :activeIndex="level_index" :isdesc="true"
                  @onClick="onClickType($event, 5)"></myLabel>
              </div>
              <div class="bottom-border div row" style="padding-top: 24px">
                <span class="text">通话状态：</span>
                <myLabel labelKey="title" :arr="statusdata_list" :activeIndex="statusdata_index"
                  @onClick="onClickType($event, 7)"></myLabel>
              </div>
              <div class="bottom-border div row" style="padding-top: 24px">
                <span class="text">筛选时间：</span>
                <myLabel ref="childRef" :arr="time_list" :activeIndex="time_index" @onClick="onClickType($event, 4)">
                </myLabel>
                <span class="text">自定义：</span>
                <el-date-picker style="width: 250px" size="small" v-model="timeValue" type="daterange" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="onChangeTime">
                </el-date-picker>
              </div>
            </template>
          </myCollapse>
          <div class="div row loadmore" @click="onChangeCollapse">
            <span class="text"> 更多 </span>
            <span :class="is_collapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
          </div> -->
        </div>
      </div>
      <div class="content-box-crm" style="margin-bottom: 60px">
        <div class="table-top-box table-top-box-abs div row" :class="{ fixed: scrollTop > topHeight }" id="stickyId">
          <div class="t-t-b-left div b-tabs row" style="width: 100%; height: 30px">
            <el-input :placeholder="placeholder" v-model="select_params.keywords" class="input-with-select"
              style="width: 300px" clearable size="small" @keyup.enter.native="handleKeywordSearch"
              @clear="clearSelectKeyword">
              <el-select v-model="select_params.type" @change="changeSelectParams" slot="prepend" placeholder="请选择"
                size="small" style="width: 100px">
                <el-option label="手机号码" :value="1"></el-option>
                <template v-if="recordstype != 'xiansuo'">
                  <el-option label="客户编号" :value="2"></el-option>
                  <el-option label="线索内容" :value="3"></el-option>
                  <el-option label="客户姓名" :value="4"></el-option>
                  <el-option label="跟进内容" :value="6"></el-option>
                  <el-option label="归属地" :value="5"></el-option>
                </template>
              </el-select>
              <!-- <el-button slot="append" icon="el-icon-search"></el-button> -->
            </el-input>
          </div>
          <!-- </div> -->
          <div class="t-t-b-right" :class="{ abs: is_small_system, show: show_right }">
            <el-button type="text" size="mini" class="el-icon-d-arrow-left" id="myButton" v-show="myButton1"
              @click="leftla"></el-button>
            <el-button style="font-size: 14px" @click="push_customerA" type="primary" size="mini"
              class="btn el-icon-plus">录入客户</el-button>
            <el-button style="font-size: 14px" v-if="is_show_upload" @click="getFile" type="primary"
              size="mini">导入</el-button>
              <el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="TransferCustomer(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" style="margin-right:10px;" @click="getCrmDepartmentList" class="search_loudong div row align-center">
                <div class="seach_value">操作</div>
                <div class="sanjiao" :class="{ transt: transfer_type }"></div>
              </div>
            </el-popover>
            <!-- <el-button v-else style="font-size: 14px" type="primary" size="mini" @click="TransferCustomer">
              转交
            </el-button> -->
            <el-button v-show="buttonhidden" style="font-size: 14px;" type="primary" @click="setCustomerLabel" size="mini">
              设置标签
            </el-button>
            <!-- <el-popover v-show="buttonhidden" v-model="pop_depart" placement="bottom" width="500px" trigger="click">
              <div class="flex-box">
                <div>搜索</div>
                <div style="margin: 10px 0" class="flex-row">
                  <el-cascader class="inp_no_border" v-model="department" placeholder="请选择"
                    :options="Memberspartment" @change="changePopDepar" :clearable="true" :show-all-levels="false" :props="{
                      label: 'name',
                      value: 'id',
                      children: 'subs',
                      checkStrictly: true,
                      emitPath: false,
                    }">
                  </el-cascader>
                  <el-select v-model="department" placeholder="请选择" :disabled="disabledment">
                    <el-option v-for="item in Memberspartment" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                  <el-button slot="append" icon="el-icon-search" style="
                      background: #f5f7fa;
                      border-left: none;
                      border-top-left-radius: 0;
                      border-bottom-left-radius: 0;
                    " @click="searchDepart"></el-button>
                </div>
                <div class="flex-row">
                  <el-select class="search-member-box" v-model="selectedMember" placeholder="请选择成员"
                    @change="changeSearchMember" @blur="closeMember" filterable clearable>
                    <el-option v-for="item in filtrMember" :key="item.id" :label="item.user_name" :value="item.id">
                    </el-option>
                  </el-select>
                  <el-button slot="append" icon="el-icon-search" style="
                      background: #f5f7fa;
                      border-left: none;
                      border-top-left-radius: 0;
                      border-bottom-left-radius: 0;
                    " @click="searchMember">
                  </el-button>
                </div>
              </div>
              <div slot="reference" @click="getCrmDepartmentList" class="search_loudong div row align-center">
                <div class="seach_value">成员</div>
                <div class="sanjiao" :class="{ transt: pop_depart }"></div>
              </div>
            </el-popover> -->
            <el-button type="danger" size="mini" style="margin-left: 10px" v-if="show_shitu"
              @click="toShitu">经营视图</el-button>
            <el-button type="text" size="mini" class="el-icon-d-arrow-right" id="myButton1" v-show="myButton"
              @click="Rightdrag" style="margin-left: 10px"></el-button>
          </div>
        </div>
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="table_header" select
          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row tooltipEffect="light"
          :row-style="$TableRowStyle" @selection-change="selectionChange" :sort_change="sortChangeData">
        </myTable>
        <div class="page_footer flex-row items-center">
          <div class="page_footer_l flex-row flex-1 items-center">
            <div class="head-list">
              <el-button type="primary" size="small" @click="empty">清空</el-button>
            </div>
            <div class="head-list">
              <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
            </div>
            <!-- <div class="head-list">
              <el-button type="primary" size="small" @click="Topping">置顶</el-button>
            </div> -->
          </div>
          <div>
            <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
              :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
              @current-change="onPageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
    </template>

    <!-- <wxwork v-if="is_tabs === 'wxwork'"></wxwork> -->
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel"
      :close-on-click-modal="false">
      <!-- <myForm
          @clsoe="is_push_customer = false"
          :data1="n_client_field"
          :data2="n_company_field"
          :form="form"
          :form1="form1"
          @onClick="onClickForm"
        ></myForm> -->
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"
                @blur="Validationphone(domain.mobile)"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <!-- <el-select style="width: 100%" v-if="website_id!==1" v-model="push_form.source_id" placeholder="请选择">
                <el-option v-for="item in sourceLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select> -->
              <el-cascader :style="{
                                  width: '100%',
                                }" v-model="source_idvalue" placeholder="请选择" :options="sourceLabel" @change="sourceLabel_status"
                :props="{
                                      label: 'title',
                                      value: 'id',
                                      children: 'children',
                                      checkStrictly: true 
                                    }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <!-- <el-select
                  style="width:100%"
                  v-model="push_form.sex"
                  placeholder="请选择"
                >
                  <el-option label="男" :value="1"></el-option>
                  <el-option label="女" :value="2"></el-option>
                </el-select> -->
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                  :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt="" @click="() => {
                                      push_form.sex = item.id;
                                    }
                                      " />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <el-select v-model="push_form.level_id" placeholder="请选择客户等级" style="width: 235px;">
              <el-option v-for="item,index in level_list" :key="index" :label="item.title+'级'"
                :value="item.id"></el-option>
            </el-select>
            <!-- <div class="row input-box div">
              <div @click="onClickLevel(item)" class="l-item row" v-for="item in levelLabel" :key="item.id" :class="{
                lisactive: item.id === push_form.level_id,
              }">
                <div class="l-name">{{ item.title }}</div>
                <div class="l-name-1">{{ item.desc }}</div>
              </div>
            </div> -->
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户标签：">
            <div class="input-box">
              <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签"
                :options="label_default_list" :props="{
                                  value: 'id',
                                  label: 'name',
                                  children: 'label',
                                  emitPath: false,
                                  multiple: true,
                                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="所在城市：" v-if="is_show_city&&is_show_city==1">
            <div class="block">
              <el-cascader style="width: 240px;" v-model="provincesvalue" clearable :props="{
                                      value: 'id',
                                      label: 'name',
                                      children: 'children',
                                      emitPath: true,
                                      multiple: false,
                                    }" :options="provincesoptions" @change="provincesChange"></el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <t-crm-project-select multiple allow-create default-first-option value-key="name" placeholder="请选择或输入"
                v-model="push_form.intention_community" width="100%" />
            </div>
          </el-form-item>
          <!-- <el-form-item label="意向区域：">
              <div class="row input-box div">
                <el-input
                  placeholder="请输入"
                  v-model="push_form.intention_street"
                ></el-input>
              </div>
            </el-form-item> -->
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer flex-row align-center">
        <el-switch v-model="push_form.add_type" active-color="#13ce66" inactive-color="#2d84fb" inactive-value="1"
          active-value="2" active-text="公海" inactive-text="私客">
        </el-switch>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
            定</el-button>
        </div>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_follow_dialog" title="跟进记录">
      <el-table v-loading="is_follow_loading" :data="is_follow_data" border>
        <el-table-column label="ID" prop="client_follow_id"></el-table-column>
        <el-table-column label="跟进类型" prop="type_title"></el-table-column>
        <el-table-column label="跟进内容" prop="content"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="is_follow_params.total" :page-size="is_follow_params.per_page" :current-page="is_follow_params.page"
        @current-change="onPageChangeQwFollow">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_dialog_upload" title="导入" :before-close="cancels">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow red">
        [重要提示]每次导入前请下载最新模板表格整理客户信息后再执行导入。
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <!-- <el-select
          style="width: 200px; margin-bottom: 10px"
          v-model="upload_form.type"
          placeholder="请选择"
        >
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select> -->
      <div class="flex-row">
        <!-- 是否覆盖 -->
        <el-select style="width: 200px; margin-bottom: 10px" v-model="upload_form.type" placeholder="请选择是否覆盖数据">
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select>
        <!-- 选择分类 -->
        <el-select style="width: 150px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.type_id"
          placeholder="请选择分类" clearable>
          <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
        <!-- 选择客户来源 -->
        <!-- <el-select style="width: 200px; margin-bottom: 10px; margin-left: 12px" v-if="website_id!=1" v-model="upload_form.source_id"
          placeholder="请选择客户来源" clearable> -->
        <!-- source_import -->
        <!-- <el-option v-for="item in source_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select> -->
        <el-cascader :style="{
                      width: '200px',
                    }" style=" margin-bottom: 10px; margin-left: 12px" v-model="source_idvalue" placeholder="请选择客户来源"
          :options="sourceLabel" @change="sourceLabelimport" :props="{
                          label: 'title',
                          value: 'id',
                          children: 'children',
                          checkStrictly: true 
                        }" clearable>
        </el-cascader>
      </div>
      <div class="flex-row">
        <!-- 选择成员 -->
        <el-input ref="focusMember" placeholder="请选择维护人" v-model="uploadAdmin_id" style="width: 200px; display: block"
          @focus="focusSelete">
          <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <!-- 选择标签 -->
        <!-- <el-select
            style="width: 362px; margin-bottom: 10px; margin-left: 12px;"
            v-model="upload_form.label"
            multiple
            placeholder="请选择标签"
          >
            <el-option label="标签1" :value="1"></el-option>
            <el-option label="标签2" :value="2"></el-option>
          </el-select> -->
        <el-cascader style="width: 362px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.label" clearable
          placeholder="请选择标签" :options="label_list" :props="{
                      value: 'id',
                      label: 'name',
                      children: 'label',
                      emitPath: false,
                      multiple: true,
                    }">
        </el-cascader>
      </div>
      <!-- 客户备注线索 -->
      <div class="clueRemark">
        <el-input ref="focusMember" placeholder="请选择录入人" v-model="uploadAdmin_id1" style="width: 200px; display: block"
          @focus="focusSelete1">
          <i v-show="uploadAdmin_id1 != '' && uploadAdmin_id1 != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <el-input type="textarea" :rows="2" placeholder="请输入客户备注线索" v-model="upload_form.remark"
          style="margin-left: 11px">
        </el-input>
      </div>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
          >添加文件</el-button
        > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancels">取 消</el-button>
        <el-button type="primary" @click="startImport" :loading="is_loading">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list1" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list1" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember1" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog title="客户标签" :visible.sync="show_Customer_label" width="660px">
      <div class="dialog_customer_label">
        <div v-for="(item, index) in labels_list" :key="item.id">
          <div class="labelname">{{ item.name }}</div>
          <div class="lables-box div row">
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.taggroup" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.label" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="allbtn">
          <div>
            <template v-if="!is_fast">
              <el-radio v-model="confirm_batch_list.type" label="1">覆盖</el-radio>
              <el-radio v-model="confirm_batch_list.type" label="2">追加 </el-radio>
            </template>
          </div>
          <div>
            <el-button @click="show_Customer_label = false">取 消</el-button>
            <el-button v-if="is_fast" type="primary" @click="confirmFastSelected" :loading="is_loading">确 定</el-button>
            <el-button v-else type="primary" :loading="is_loading" @click="confirmSelected">确 定</el-button>
          </div>
      </div>
      </span>
    </el-dialog>
      <!-- 列表 -->
    <el-dialog :visible.sync="is_transfer_customer" :title="changetitle">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border @sort-change="sortChange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum&&changetitle=='转交客户'"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row)">转交客户</el-link>
          </template>
        </el-table-column>


        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>
    <!-- 右侧 -->
    <el-dialog :visible.sync="right_transfer_customer" :title="changetitle">
      <div class="tips" v-if="changetitle=='转交客户'">
        <div>提示语：转交后维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border @sort-change="sortChange">
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="数量" prop="number" sortable="custom" v-if="shownum&&changetitle=='转交客户'"></el-table-column>
        <el-table-column label="操作" fixed="right" v-if="changetitle=='转交客户'">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row,1)">转交客户</el-link>
          </template>
        </el-table-column>


        <el-table-column label="操作" fixed="right" v-if="changetitle =='复制客户'">
          <template slot-scope="scope" v-if="changetitle =='复制客户'">
            <el-link type="primary" @click="oncopycrm(scope.row,1)">复制客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>
    <!-- 快速编辑客户维护资料模态框 -->
    <my-Maintenance v-if="show_cus_Edit" :show_cus_Edit="show_cus_Edit" :source_list="ponent_maintain_source"
      :level_list="ponent_maintain_level" :type_list="ponent_maintain_type" :label_list="ponent_maintain_label"
      :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit" @submitMaintain="submitMaintain">
    </my-Maintenance>
    <!-- 快速查看客户手机号模态框 -->
    <my-LookTel v-if="show_look_Tel" :show_look_Tel="show_look_Tel" :ponent_Tel_data="ponent_Tel_data"
      :nowDialData="nowDialData" @fastCloseTel="fastCloseTel" @fastSubmitTel="fastSubmitTel" :selfID="selfID">
    </my-LookTel>

    <!-- 快速提交客户审批 -->
    <my-Examine v-if="show_Examine_dialog" :show_Examine_dialog="show_Examine_dialog"
      :ponent_Examine_data="ponent_Examine_data" :ponent_Examine_stutas="ponent_Examine_stutas"
      :ponent_Examine_type="Examine_type" :AllDepartment="AllDepartment" @closeExamine="closeExamine"
      @submitExamineAfter="submitExamineAfter"></my-Examine>
    <!-- 快速跟进客户内容模态框 -->
    <myFollowUp v-if="show_Follow_dialog" :show_Follow_dialog="show_Follow_dialog"
      :ponent_Follow_data="ponent_Follow_data" @addFollowSuccess="addFollowSuccess" @closeFollow="closeFollow">
    </myFollowUp>
    <!-- <input
      v-if="is_dialog_upload"
      type="file"
      ref="file"
      style="display: none"
      v-on:change="handleFileUpload($event)"
    /> -->
    <el-upload :limit="1" class="upload-demo" :headers="myHeader" :action="user_avatar"
      :on-success="handleSuccessAvatarTemporary" ref="upload" style="display: none" v-if="is_dialog_upload">
      <el-button class="el-icon-download" size="small">本地上传</el-button>
    </el-upload>
    <div>
      <el-drawer :visible.sync="drawer" title="客户信息" :direction="direction" :modal="false" :before-close="handleClose" size="56%">
        <div class="QuickEdit" custom-class="kehu">
          <Drawerdetails v-infinite-scroll="load" ref="childReff" :ids="ids" :type="seas" :types="seastype"
            @getDataListA="getDataList" :recordName.sync="recordName">
          </Drawerdetails>
        </div>
        <div class="demo-drawer__footer">
          <div class="drawerfoot flex-row">
            <div class="flex-row" style="margin-left: 15px;">
              <div class="crmstatus" style="margin-right: 15px;">客户状态: {{ Claim }}</div>
              <el-button type="primary" size="small" style="margin-right:20px" v-if="ClaimCustomers"
                @click="Getright(c_detail.id)">立即认领</el-button>
              <div>
                <!-- <el-button type="primary" size="small" style="margin-left:20px" plain @click="TopA(ids)">{{ticky_post }}</el-button> -->
              </div>
              <el-input size="small" placeholder="关键词" v-model="followsearch" class="input-with-select short" v-if="recordName =='Follow'"
              clearable @clear="getfollowsearch">
                <el-button slot="append" icon="el-icon-search" @click="getfollowsearch"></el-button>
              </el-input>
            </div>
            <div>
              <el-popover v-if="transmitstatus" v-model="Transfer_right" placement="bottom" width="500px" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="onClickrightcopy(item)">
                  {{ item.name }}
                </div>
              </div>
              <el-button style="margin-right: 10px;" type="primary" size="small" slot="reference">
                  操作
                </el-button>
            </el-popover>
              <!-- <el-button v-else style="margin-right: 10px;" type="primary" size="small"
                @click="onClickright">
                转交
              </el-button> -->
              <el-button type="warning" size="small" style="width: 100px;" @click="sureright">确定</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <div class="importdrawer">
      <el-drawer title="数据导入" :visible.sync="importdrawer" :direction="direction" :with-header="false">
        <div>
          <importinformation ref="importinformation" class="QuickEditA" :type_list="type_list" :sourceLabel="sourceLabel"
            :label_list="label_list" :memberList="memberList" @child-event="closesidebar" :typesof=3></importinformation>
        </div>
        <div class="isfooter">
          <div class="drawerfoot flex-row isfoot">
            <!-- <div> -->
            <el-button style="margin-right: 10px;" type="info" plain size="small" @click="importdrawer = false">
              取消
            </el-button>
            <el-button type="primary" size="small" style="width: 100px;" @click="Starting_import">开始导入</el-button>
            <!-- </div> -->
          </div>
        </div>
      </el-drawer>
    </div>
      <!-- 录入客户的插件 -->
      <div class="enterdrawer">
      <el-drawer title="录入客户" :visible.sync="enterdrawer" :direction="direction">
        <div>
          <entercustomer ref="enterinformation" class="QuickEditA" :type_list="type_list" :sourceLabel="sourceLabel"
            :label_list="label_list" :memberList="memberList" @child-event="enterdrawerfalse" :typesof=2
            :level_list="level_list" :typeLabel="typeLabel" :label_default_list="label_default_list"
            :radio1="radio1"></entercustomer>
        </div>
        <div class="isfooter">
          <div class="drawerfoot flex-row enterfoot">
            <div style="margin-left:20px;">
              <el-radio-group v-model="radio1" size="small">
                <el-radio-button v-for="item in radio1data" :key="item.id" :label="item.id">{{ item.name}}</el-radio-button>
              </el-radio-group>
            </div>
            <div>
              <el-button style="margin-right: 10px;" type="info" plain size="small" @click="enterdrawer=false">
                取消
              </el-button>
              <el-button type="primary" size="small" style="width: 100px;" v-loading="is_button_loading"
                @click="onClickForm">确定</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <!-- 修改客户资料的插件 -->
    <div class="enterdrawer">
      <el-drawer title="修改资料" :visible.sync="show_cus_EditA" :direction="direction">
        <div>
          <!-- v-if="show_cus_Edit" -->
          <myMaintenancecopy ref="myMaintenancecopy" class="QuickEditA" :show_cus_Edit="show_cus_EditA"
            :source_list="ponent_maintain_source" :level_list="ponent_maintain_level" :type_list="ponent_maintain_type"
            :label_list="ponent_maintain_label" :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit"
            @submitMaintain="submitMaintain">
          </myMaintenancecopy>
        </div>
        <div class="isfooter">
          <div class="drawerfoot flex-row enterfoot">
            <div style="margin-left:20px;">
              <!-- <el-radio-group v-model="radio1" size="small">
                <el-radio-button v-for="item in radio1data" :key="item.id" :label="item.id">{{ item.name
                                  }}</el-radio-button>
              </el-radio-group> -->
            </div>
            <div>
              <el-button style="margin-right: 10px;" type="info" plain size="small" @click="show_cus_EditA=false">
                取消
              </el-button>
              <el-button type="primary" size="small" style="width: 100px;" v-loading="is_button_loading"
                @click="onClickForm(1)">确定</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <scriptdetails ref="scriptdetails"></scriptdetails>
    <automatic ref="automatic"  @getDataList='getDataList'></automatic>
  </div>
</template>
  
<script>
// import my from "./components/my";
// import seas from "./components/seas";
// import wxwork from "./components/wxwork";
// import myForm from "./components/customer_form";
import myTable from "@/components/components/my_table";
// import myLabel from "./components/my_label.vue";
// import myCollapse from "./components/collapse";
import myExamine from "@/components/components/my_Examine.vue";
import myMaintenance from "@/components/components/my_maintenance.vue";
import myLookTel from "@/components/components/my_lookTel.vue";
// import mySelect from "./components/my_select";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import myFollowUp from "@/components/components/my_followUp.vue";
import config from "@/utils/config";
import Drawerdetails from "./components/Drawer_details.vue"
import TCrmProjectSelect from '@/components/tplus/tSelect/tCrmProjectSelect.vue';
import importinformation from '@/components/tplus/tSelect/importinformation.vue';
import customTabs from '@/views/crm/custom_tab/components/tabs.vue';
import customTabsMixin from '@/views/crm/custom_tab/mixins/custom_tab.js';
import myMaintenancecopy from "@/components/components/my_maintenance_copy.vue";
import entercustomer from '@/components/tplus/tSelect/entercustomer.vue';
import scriptdetails from '@/views/crm/share_follower/detailedinformation.vue'
import automatic  from '@/views/crm/share_follower/automatic_allocation.vue'
export default {
  name: "crm_customer_seas_list",
  components: {
    // my,
    // seas,
    // myForm,
    // wxwork,
    myTable,
    // mySelect,
    // myLabel,
    // myCollapse,
    multipleTree,
    myMaintenance,
    myLookTel,
    myExamine,
    myFollowUp,
    Drawerdetails,
    importinformation,
    TCrmProjectSelect, customTabs,
    myMaintenancecopy,
    entercustomer,
    scriptdetails,
    automatic,
  },
  mixins: [customTabsMixin],
  data() {
    return {
      calculatecreate:"录入人",
      chnegjiaouser:"成交人",
      weihuuser:"维护人",
      diakanuser:"带看人",
      recordName: '',
      followsearch: '',
      transmitstatus:true,
      ClaimCustomers:false,
      Claim:"",
      count: 0,
      seas:"seas",
      seastype:"",
      drawer: false,
      direction: 'rtl',
      //选择成员弹框控制
      show_member_list: false,
      show_member_list1: false,
      is_tabs: "all",
      selectedIds: [],
      tabs: [
        {
          id: 1,
          name: "所有客户",
          desc: "all",
        },
        {
          id: 2,
          name: "我的客户",
          desc: "my",
        },
        {
          id: 3,
          name: "公海客户",
          desc: "seas",
        },
        // {
        //   id: 4,
        //   name: "企微客户",
        //   desc: "wxwork",
        // },
      ],
      is_push_customer: false,
      disabledment:false,
      push_form: {
        cname: "",
        source_id: "",
        source2_id:"",
        level_id: "",
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: [],
        label: "", // 客户标签
        // intention_street: "",
        remark: "",
        add_type: "1",
      },
      sex_list: [
        { id: 1, name: "nan" },
        { id: 2, name: "nv3" },
      ],
      other_mobile: [{ mobile: "" }],
      type_list: [],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      timeValue: "",
      level_list: [],
      statusdata_list: [
        { id: 0, title: "全部" },
        { id: 1, title: "未联系" },
        { id: 2, title: "未接通" },
        { id: 3, title: "已接通" },
      ],
      source_list: [],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - 1);
            end.setDate(start.getDate());
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本周',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end);
            start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上周',
          onClick(picker) {
              const today = new Date(); // 获取当前日期
              const end = new Date(today); // 结束日期为当前日期
              const start = new Date(today); // 开始日期为当前日期
              const day = today.getDay(); // 获取当前是星期几
              const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
              const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
              end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
              // end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
              start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
              // start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
              // 将计算得到的时间范围传递给日期选择器
              picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '上月',
          onClick(picker) {
            const end = new Date();
            end.setDate(0); // 获取上个月的最后一天
            end.setHours(23, 59, 59, 0);
            const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },]
      },
        //时间类型
        options: [
        { value: 0, label: '全部' },
        { value: 1, label: '创建时间' },
        { value: 2, label: '跟进时间' },
        { value: 3, label: '线索时间' },
        { value: 4, label: '更新时间' },
        { value: 5, label: '掉公时间' },
        { value: 6, label: '转公时间' },
        { value: 8, label: '带看时间' },
        { value: 10, label: '转交时间' },
      ],
      datetime_type:1,//时间类型
      Source_statusvalue:"",//客户来源
      customer_statusvalue:"",//客户状态
      typeLabel_value:"",//客户类型
      grade_value:"",//客户等级
      typeqiwei_value:"",//绑定企微
      customerstatus_value:"",//通话状态
      showstatus_value:"",//带看状态
      showstatus_list: [
        { id: 0, title: "全部" },
        { id: 1, title: "未带看" },
        { id: 2, title: "已带看" },
        { id: 3, title: "有复看" },
      ],//1:未带看,2:已带看,3:有复看
      selectedMember_value:"",//全部成员
      department_id_value:"",//全部部门
      source_index: 0,
      tracking_index: 0,
      type_index: 0,
      bind_index: 0,
      level_index: 0,
      statusdata_index: 0,
      time_index: 0,
      client_field: {
        // 获取客户字段
        type: 2,
      },
      n_client_field: {},
      n_company_field: {},
      form: {},
      form1: {},
      type: 1,
      tracking_list: [
        // { title: "全部", id: 0 },
        // { title: "转公客户", id: 1 },
        // { title: "掉公客户", id: 2 },
        // { title: "锁定客户", id: 3 }
      ],
      activeIndex: 0,
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      //成员部门信息
      memberList: [],
      multipleSelection: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 3,
        // status: 0,
        mobile: "",
        date_style:1,
        // sort_type: 0,
        c_type1: 4, // 公海客户状态 1:新增线索 2：最新跟进 3：多条线索 4：潜在客户
        c_type4: 0, // 潜在客户类型 0：全部 1：转公 2：掉公 3：锁定
        department_id: 0,
        admin_id: 0,
        c_type6: 0,
        date_sort: 1,   //1=倒序,2=升序
      },
      department:1,
      show_xiansuo_search: false,
      is_table_loading: false,
      enterdrawer:false,//录入客户的侧边栏
      radio1: 2,//公海or私客
      radio1data:[ 
        {id: 1 ,name: "私客"},
        {id: 2 ,name: "公海"},
        // {id: 3 ,name: "流转客"}
      ],
      show_cus_EditA: false, // 显示快速编辑客户维护资料模态框
      tableData: [],
      table_header: [],
      transfer_type: false, // 显示/隐藏转交类型
      // 转交类型
      cus_list: [
        { id: 1, name: "转交到同事" },
      ],
      Transfer_right:false,
      changetitle:"转交客户",
      is_follow_dialog: false,
      is_follow_data: [],
      is_follow_loading: false,
      is_follow_params: {
        page: 1,
        total: 0,
        per_page: 10,
      },
      label_list: [],
      multipleSelectionname: [],
      is_collapse: false,
      tracking_params: {
        type: 4,
      },
      is_dialog_upload: false,
      uploadAdmin_id: "",
      uploadAdmin_id1: "",
      upload_form: {
        type: 1, // 是否覆盖数据 1：不覆盖 2：覆盖
        admin_id: "", // 管理员id
        file: "", // 导入的文件
        type_id: "", // 客户类型id
        source_id: "", // 客户来源
        source2_id:'',
        label: "", // 标签:字符串格式，多个用逗号隔开
        remark: "", // 客户备注线索
      },
      admin_params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
        type: 0
      },
      admin_list: [],
      is_button_loading: false,
      pull_params: {
        next_user_cursor: 0,
        next_customer_cursor: "",
      },
      list_tabs: [
        { id: 0, title: "全部" },
        { id: 1, title: "已认领" },
        { id: 2, title: "已跟进" },
        { id: 3, title: "未跟进" },
      ],
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过3000条",
        "标红标题对应内容为必填项，如不填写则过滤出去不进行导入",
        "同一个表格重复的手机号会导入1个客户，系统将按重复的顺序将客户线索内容追加到线索记录 ",
        "选择覆盖，导入的数据和已有数据重复时，会覆盖之前的数据，选择不覆盖则过滤掉重复数据",
      ],
      no_follow_number: "",
      show_tel_search: false,
      buttonhidden: true,
      show_shitu: false,
      myButton: false,
      myButton1: false,
      is_small_system: false,
      show_right: false,
      is_show_upload: false, // 是否显示导入按钮
      customer_list_type: [
        { id: 0, title: "全部", is_select: 0 },
        { id: 1, title: "转公", is_select: 1 },
        { id: 2, title: "掉公", is_select: 2 },
        { id: 4, title: "AI机器人", is_select: 4 },
        { id: 5, title: "导入客户", is_select: 5 },
        {id: 6, title: "废客", is_select: 6}
        // { id: 1, title: "已认领", is_select: false },
        // { id: 3, title: "未跟进", is_select: false },
        // { id: 4, title: "私客" },
        // { id: 5, title: "掉公" },
      ],
      // 1:最近活跃,2:多条线索
      // screen_list_type: [
      //   { id: 1, title: "已认领", is_select: true },
      //   { id: 5, title: "未跟进", is_select: true },
      // ],
      customer_type: 0,
      notNewClue: true,
      is_show: 0, // 控制客户标签样式
      label_default_list: [], // 获取原始默认标签列表
      customerLabelList: {}, // 所选择的标签
      changeParentLabel: "", // 之前所选的上一级的标签
      is_all: true, // 控制客户标签，“全部”标签的样式
      datalist: [], // 全部部门人员
      show_Customer_label: false, // 控制客户标签模态框显示/隐藏
      labels_list: [], // 客户标签列表
      // 确定编辑客户标签的接口传参
      confirm_batch_list: {
        ids: "", // 客户id 多个用逗号隔开
        label: "", // 客户标签id 多个用逗号隔开
        type: "2", //添加标签时选择 2:追加 或 1:覆盖
      },
      pop_depart: false, // 显示/隐藏部门搜索popover
      AllDepartment: [], // 全部部门列表
      Memberspartment:[
        {id:1,name:"转公"},
        {id:2,name:"掉公"}
      ],
      is_transfer_customer: false, // 转交客户模态框
      right_transfer_customer:false,
      c_id: "", // 转让客户id：多个用，隔开
      selectedMember: "", // 选择搜索部门
      Not_duplicate_datalist: [], // 没有重复部门成员的容器
      filtrMember: [], // 选中部门后过滤出的部门成员
      status_id: "", // 登录后的个人id
      status_list: [], // 快速编辑客户状态数据容器
      copy_status_list: [], // 深拷贝客户状态
      getStutas_params: {
        type: 2,
      },
      is_loading: false,
      show_Examine_dialog: false, // 控制客户审批模态框
      ponent_Examine_data: {}, // 提交审批客户信息
      ponent_Examine_stutas: {}, // 选择的要修改的状态
      website_id: "", // 当前站点id
      show_cus_Edit: false, // 显示快速编辑客户维护资料模态框
      ponent_maintain_data: {}, // 客户信息
      ponent_maintain_source: [], // 深拷贝客户来源列表
      ponent_maintain_level: [], // 深拷贝客户来源列表
      ponent_maintain_type: [], // 深拷贝客户类型列表
      ponent_maintain_label: [], // 深拷贝客户标签列表
      show_look_Tel: false, // 显示快速查看客户手机号模态框
      ponent_Tel_data: {}, // 客户信息
      is_fast: false, // 是否是快速编辑标签
      fastEdit_params: {
        client_id: "", // 客户id
        label: "", // 标签id多个，隔开
      },
      Examine_type: 19, // 默认审批类型
      is_pullDown: false, // 是否展开下拉框
      show_Follow_dialog: false, // 显示快速跟进客户模态框
      user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_FILE_2}`,
      nowDialData: {},
      selfID: "",
      scrollTop: 0,
      topHeight: 330,
      //记录切换
      recordstype: 0,
      placeholder: "请输入手机号码",
      select_params: {
        type: 1,
        keywords: ""
      },
      shownum: false,
      ids: "",
      multipleright:"",
      provincesoptions:[],//城市选择联动
      provincesvalue:[],//城市id
      is_show_city:"",
      source_idvalue:"",//录入客户来源
      importdrawer:false,//导入侧边栏
      labeldata:[],
      customerLabelListcopy: [], // 所选择的标签
      customerDetailChangedIds: [],
      guanshow:"",
    };
  },
  watch: {
    admin_list: {
      handler(newVal) {
        console.log(newVal);
        // 根据数据变化判断是否显示数量列
        this.shownum = newVal.some(item => {
          if (item.number || item.number == 0) {
            return true // 监听对象内部属性的变化
          } else {
            return false
          }
        });
      },

    }
  },

  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    levelLabel() {
      return this.level_list.filter((item) => {
        return item.id > 0;
      });
    },
    sourceLabel() {
      return this.source_list.filter((item) => {
        return item.id > 0;
      });
    },
    typeLabel() {
      return this.type_list.filter((item) => {
        return item.id > 0;
      });
    },
  },
  filters: {
    // 计算过去时间天数
    getPastDay(val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
    publicStatusParse(row, row1) {
      if (row == 1) {
        return row1 + "已转公";
      } else if (row == 2) {
        return row1 + "已掉公";
      }else if (row == 5){
        return "导入客户";
      }
    },
  },
  created() {
    // 赋值website_id
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id;
    }
    if (this.$route.query.status) {
      this.params.c_type1 = this.$route.query.status;
    }
    if (this.$route.query.c_type4) {
      this.params.c_type4 = this.$route.query.c_type4;
    }
    let screenWidth = document.body.clientWidth
    if (screenWidth < 1355) {
      this.is_small_system = true
      this.myButton1 = true
    } else {
      this.is_small_system = false
      this.myButton1 = false
    }
    this.getPerssion()
    this.getjuesename()
  },
  mounted() {
    if(this.$store.state.ismanager){
      if(this.$store.state.ismanager==1){
        this.cus_list.push( { id: 2, name: "复制到同事的流转客" },)
      }
    }else{
      this.btnexport()
    }
    window.addEventListener('resize', this.handleResize);
    this.getScreenWidth();
    this.getTrackingList();
    this.getLevelData();
    this.getSourceData();
    this.getLabelList();
    this.getAdmin();
    this.getCrmCustomerFollowNumber();
    this.getadminUser();
    this.getStatus();
    this.tfybinding()
    this.getTypelist()
    let page = document.querySelector('.main-content');
    window.addEventListener('resize', this.handleResize);

    const filter = document.getElementById("filter")

    this.topHeight = filter.offsetHeight + 120
    page.addEventListener('scroll', this.debounce(this.handleScroll, 20))

    eventBus.$on('customerDetailChange', (id) => {
      !this.customerDetailChangedIds.includes(id) && this.customerDetailChangedIds.push(id);
    })
    let pagenum = localStorage.getItem( 'pagenum')
    this.params.per_page = Number(pagenum)||10
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('scroll', this.handleScroll)
    window.addEventListener("beforeunload", function () {
      document.cookie = "value=; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    });
    eventBus.$off('customerDetailChange')
  },
  methods: {
    //获取角色名称
    getjuesename(){
      this.$http.getsetuprolename().then(res=>{
        if(res.status == 200){
          if(res.data.diy_create_name){
            //录入人
            this.calculatecreate = res.data.diy_create_name
          }
          if(res.data.diy_follow_name){
            //维护人
            this.weihuuser = res.data.diy_follow_name
          }
          if(res.data.diy_take_name){
            //带看人
            this.diakanuser = res.data.diy_take_name
          }
          if(res.data.diy_deal_name){
            //成交人
            this.chnegjiaouser = res.data.diy_deal_name
          }
          this.storingtables()
        }
      })
    },
    //判断是不是客户管理员或创始人
    btnexport(){
        this.$http.determinecustomeradmin().then((res)=>{
          if(res.status==200){
            // console.log(res.data," //判断是不是客户管理员");
            if(res.data.is_manager==1){
              this.cus_list.push( { id: 2, name: "复制到同事的流转客" },)
            }
          }
        })
      },
    storingtables(){
      this.table_header = [
    // {
        //   prop: "id",
        //   label: "ID",
        //   width: "80px",
        // },

        {
          label: "客户名称",
          width: "240px",
          minWidth: "240px",
          fixed: "left",
          sortable: "custom",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {data.row.cname ? (
                      <el-popover
                        placement="top-start"
                        max-width="240px"
                        trigger="hover"
                      >
                        <div style="align-items: center;white-space: nowrap;" class="flex-row">
                          客户编号：{data.row.id}
                          <el-button
                            size="mini"
                            type="success"
                            style="margin-left: 20px"
                            onClick={() => {
                              this.copyCusID(data.row.id);
                            }}
                          >
                            复制
                          </el-button>
                        </div>
                        <div class="cus-userName cus-userName_nowrap" slot="reference"
                        onClick={() => {
                              this.Quick_Edit(data.row);
                            }}
                        >
                          {data.row.cname}
                        </div>
                      </el-popover>
                    ) : (
                      ""
                    )}
                    <div class="cus-sex">
                      {data.row.sex && data.row.sex == 1 ? (
                        <img
                          src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                          alt=""
                        />
                      ) : null}
                      {data.row.sex && data.row.sex == 2 ? (
                        <img
                          src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                          alt=""
                        />
                      ) : null}
                    </div>
                  </div>
                  <div class="cus-box-foot flex-row">
                    {data.row.level ? (
                      <span class="cus-icon-level">
                        {data.row.level.title}级
                      </span>
                    ) : null}
                    {data.row && data.row.create_id == this.status_id ? (
                      <span class="cus-icon-type">私客</span>
                    ) : null}
                    {data.row.client_type ? (
                      <span class="cus-icon-purchase">
                        {data.row.client_type.title}
                      </span>
                    ) : null}
                    {/* {data.row.tracking && data.row.tracking.title == '有效客户' ? (<span class='cus-icon-customer'>有效客户</span>) : null}
                      {data.row.tracking && data.row.tracking.title != '有效客户' ? (<span class='cus-icon-purchase'>{data.row.tracking.title}</span>) : null} */}
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="150"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length
                          ? this.status_list.map((item) => {
                            return (
                              <div
                                class="f-item"
                                onClick={() => {
                                  this.onClickFollowStatus(data.row, item);
                                }}
                              >
                                {item.title}
                              </div>
                            );
                          })
                          : null}
                      </div>
                      {data.row.tracking &&
                        Object.keys(data.row.tracking).length &&
                        data.row.tracking.title == "有效客户" ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-customer"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          有效客户
                        </span>
                      ) : data.row.tracking &&
                        Object.keys(data.row.tracking).length ? (
                        <span
                          slot="reference"
                          id={"popClick" + data.row.id}
                          class="cus-icon-purchase"
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >
                          {data.row.tracking.title}
                        </span>
                      ) : (
                        ""
                      )}
                    </el-popover>
                  </div>
                </div>
                {data.row.wxqy_id > 0 ? (
                  <img
                    class="cus-img"
                    src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                  />
                ) : (
                  ""
                )}
                <div
                  class="fast-Edit-cus"
                  onClick={() => {
                    this.fastEditData(data.row);
                  }}
                >
                  <img
                    src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                    alt=""
                  />
                </div>
              </div>
            );
          },
        },
        {
          label: "手机号",
          prop: "mobile",
          width: "200px",
          minWidth: "200px",
          fixed: "left",
          render: (h, data) => {
            const mobileFilter = function (val) {
              let reg = /^(.{3}).*(.{3})$/;
              return val.replace(reg, "$1*****$2");
            };
            return (
              <div class="flex-box table-btns">
                {data.row.last_call_follow
                  &&
                  data.row.last_call_follow.id
                  && data.row.call_open_crm > 0
                  ?
                  (<div class="last_call_follow w180 div row">
                    <el-tooltip
                      class="item"
                      style="display:flex;flex-direction:row;justify-content:center"
                      effect="light"
                      placement="top"
                    >
                      <div slot="content" style="max-width:300px">
                        {data.row.last_call_follow.content}
                      </div>
                      <div class="cus-clue-text">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.call_status == 1
                          ?
                          (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                          :
                          (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                      </div>
                    </el-tooltip>
                  </div>)
                  :
                  (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                <span class="search-Belong">
                  {data.row.mobile_place == "" ||
                    data.row.mobile_place == undefined ? (
                    <el-button
                      type="primary"
                      plain
                      onClick={() => {
                        this.HomeAddress(data.row);
                      }}
                    >
                      归属地查询
                    </el-button>
                  ) : (
                    <span class="Address">{data.row.mobile_place}</span>
                  )}
                </span>
                <div
                  class="fast-look-tel"
                  onClick={() => {
                    this.fastLookTel(data.row);
                  }}
                >
                  <i class="el-icon-phone"></i>
                </div>
              </div>
            );
          },
        },

        {
          label: "客户线索",
          prop: "remark",
          minWidth: "270px",
          sortable: "custom",
          // tooltip: true,
          render: (j, data) => {
            return (
              <div class="flex-box" >
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.last_behavior_info && data.row.last_behavior_info.content || data.row.remark}
                    </div>
                    <div class="follow-content" style="width:235px;text-align: left">
                      {data.row.last_behavior_info && data.row.last_behavior_info.content || data.row.remark}
                    </div>
                  </el-tooltip>
                <div class="cus-clue-label flex-row">
                  {data.row.label.length
                    ? data.row.label.slice(0, 4).map((item, index) => {
                      return (
                        <div class="flex-row align-center">
                          <span class="cus-icon-label" key={index}>
                            {item}
                          </span>
                        </div>
                      );
                    })
                    : null}
                </div>
                {data.row.label && data.row.label.length ? (
                  <div
                    class="clueLabel"
                    onClick={() => {
                      this.fastEditLabel(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                ) : (
                  <div
                    class="clueLabel"
                    onClick={() => {
                      this.fastEditLabel(data.row);
                    }}
                  >
                    <img
                      src={
                        "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                      }
                      alt=""
                    />
                  </div>
                )}
              </div>
            );
          },
        },
        {
          label: "创建时间",
          prop: "created_at",
          sortable: "custom",
          minWidth: "240px",
          render: (j, data) => {

            return (
              <div style="text-align: left;width:220px">
                <div>
                  <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                  {data.row.follow_user && data.row.follow_user.user_name ? (
                    <span style="margin-right: 5px;">
                      {this.weihuuser}：{data.row.follow_user.user_name}
                    </span>
                  ) : (
                    ""
                  )}
                  {data.row.public_status == 2 && data.row.public2_status ? (
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(
                        data.row.public2_status, data.row.go_sea_day
                      )}
                    </span>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            );
          },
        },

        {
          label: "更新时间",
          prop: "updated_at",
          minWidth: "200",
          sortable: "custom",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_follow &&
                  data.row.last_follow != "" ?  (<el-popover
						    	placement="top-start"
						    	width="300"
						    	trigger="hover">
						    	<div class="comment-popover" domPropsInnerHTML={data.row.last_follow.content.replace(/\n/g, '<br>')}></div>
						    	<div slot="reference"><div class="comment">
                    <span style="margin-right: 5px;">
                      {data.row.last_follow_days}
                      </span>{data.row.last_follow && data.row.last_follow.content
                            ? data.row.last_follow.content
                            : null}</div></div>
						    </el-popover>) : (
                  ""
                )}
                <div style="text-align: left;">{data.row.updated_at}</div>
                <div
                  class="followLabel"
                  onClick={() => {
                    this.fastFollowUp(data.row);
                  }}
                >
                  <img
                    src={
                      "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                    }
                    alt=""
                  />
                </div>
              </div>
            );
          },
        },
        {
          label: "最后通话状态",
          prop: "",
          width: "160px",
          render: (j, data) => {
            return (
              <div>
                <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                  ? 'success'
                  : data.row.last_call_status && data.row.last_call_status === 2
                    ? 'danger'
                    : 'info'}>
                  {data.row.last_call_status && data.row.last_call_status === 1
                    ? '已接通'
                    : data.row.last_call_status && data.row.last_call_status === 2
                      ? '未接通'
                      : '未联系'}
                </el-tag>
              </div>
            )
          }
        },
        {
          label: "最新带看记录",
          prop: "last_take_info",
          width: "200px",
          // sortable: "custom",
          render: (j, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {
                      data.row.last_take_info && data.row.last_take_info.content ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.last_take_info.content}
                          </div>
                          <div class="cus-userName1" slot="reference">
                            {data.row.last_take_info.content}
                          </div>
                        </el-popover>
                      ) : (
                        ""
                      )
                    }
                  </div>
                </div>
              </div>
            );
          }
        },
        {
          label: "带看次数",
          prop: "client_type",
          minWidth: "100px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_take_info && data.row.last_take_info.take_num || 0}
              </div>
            )
          }
        },

        {
          label: "最近带看时间",
          prop: "client_type",
          minWidth: "135px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_take_info ? (
                  <span style="margin-right: 5px;">
                    {data.row.last_take_info.take_date}{data.row.last_take_info.take_time == 1 ? '上午' : (data.row.last_take_info.take_time == 2 ? '下午' : '晚上')}
                  </span>
                ) : '--'}
              </div>
            )
          }
        },
        {
          label: this.diakanuser,
          prop: "client_type",
          minWidth: "100px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_take_info ? (
                  <span style="margin-right: 5px;">
                    {data.row.last_take_info.admin ? data.row.last_take_info.admin.user_name : '--'}
                  </span>
                ) : '--'}
              </div>
            )
          }
        },
        {
          label: "陪看人",
          prop: "client_type",
          minWidth: "130px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.last_take_info ? (
                  <span style="margin-right: 5px;">
                    {data.row.last_take_info.accompany}
                  </span>
                ) : '--'}
              </div>
            )
          }
        },
        {
          label: this.calculatecreate,
          prop: "create_user",
          minWidth: "150px",
          render: (j, data) => {
            return (
              <div style="text-align:left">
                {data.row.create_user && data.row.create_user.user_name ? (
                  <span style="margin-right: 5px;">
                    {data.row.create_user.user_name}
                  </span>
                ) : (
                  "--"
                )}
              </div>
            )
          }
        },

        {
          label: this.weihuuser,
          prop: "repeat_call_name",
          minWidth: "110px",
          // sortable: "custom",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.follow_user && data.row.follow_user.user_name ? (
                  <span style="margin-right: 5px;">
                    {data.row.follow_user.user_name}
                  </span>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },
        {
          label: this.chnegjiaouser,
          prop: "deal_user",
          minWidth: "150px",
          render: (j, data) => {
            return (
              <div style="text-align:left">
                {data.row.deal_user && data.row.deal_user.user_name ? (
                  <span style="margin-right: 5px;">
                    {data.row.deal_user.user_name}
                  </span>
                ) : (
                  "--"
                )}
              </div>
            )
          }
        },
        {
          minWidth: "190",
          label: "来源",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.source ? (
                  <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
        /* {
          label: "状态",
          width: "100px",
          render: (h, data) => {
            return (
              <div>
                {data.row.tracking ? (
                  <span>{data.row.tracking.title}</span>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        }, */

        {
          label: "操作",
          minWidth: "150",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.onClickDetail(data.row);
                  }}
                >
                  详情
                </el-link>
                {this.params.status != 1 && !data.row.follow_id && !this.checkStatus(data.row) ? (
                  <el-link
                    style="margin-left:20px"
                    type="primary"
                    onClick={() => {
                      this.onClickGet(data.row);
                    }}
                  >
                    认领
                  </el-link>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ]
    },
    getfollowsearch(){
      this.$refs.childReff.getfollowsearch(this.followsearch);
    },
    async getPerssion() {
      let admin_roles = await this.$http.getAdmin().catch(() => {
        console.log();
      })
      this.shitu_list = admin_roles.data;
      if (this.shitu_list.roles && this.shitu_list.roles.length && this.shitu_list.roles[0].name == '站长') {
        this.show_shitu = true
      } else {
        this.$http.getAuthCrmShow('config_auth_uid').then(res => {
          if (res.status == 200) {
            if ((res.data + '').indexOf(this.shitu_list.id) > -1) {
              this.show_shitu = true
            }
          }
        })
      }
      this.getpl()
    },
      load () {
        this.count += 2
      },
    //腾房云是否绑定
    tfybinding() {
      this.$http.tfyshow().then(res => {
        document.cookie = `value=${res.data}`;
      })
    },
    changeSelectParams(e) {
      if (e == 1) {
        this.placeholder = '请输入手机号码'
      } else if (e == 2) {
        this.placeholder = '请输入客户编号'

      } else if (e == 3) {
        this.placeholder = '请输入线索内容'
      }
      else if (e == 4) {
        this.placeholder = '请输入客户姓名'
      }
      else if (e == 5) {
        this.placeholder = '请输入归属地'
      }else if ( e == 6){
        this.placeholder = '请输入跟进内容'
      }

    },
    clearSelectKeyword() {
      this.params.keywords = ''
      this.params.mobile = ''
      this.params.cname = ''
      this.params.number = ''
      this.params.mobile_place = ''
      this.params.follow_keywords = ''
      this.handleSearch()
    },
    setParams(key) {
      let arr = ['keywords', 'mobile', 'cname', 'number','mobile_place','follow_keywords']
      arr.map(item => {
        if (item !== key) {
          this.params[item] = ''
        }
      })
    },
    handleKeywordSearch() {
      if (this.select_params.type == 1) {
        this.setParams('mobile')
        this.placeholder = '请输入手机号码'
        this.params.mobile = this.select_params.keywords.replace(/\s+/g,"");
      } else if (this.select_params.type == 2) {
        this.setParams('number')
        this.placeholder = '请输入客户编号'
        this.params.number = this.select_params.keywords
      } else if (this.select_params.type == 3) {
        this.placeholder = '请输入线索内容'
        this.setParams('keywords')
        this.params.keywords = this.select_params.keywords
      } else if (this.select_params.type == 4) {
        this.setParams('cname')
        this.placeholder = '请输入客户姓名'
        this.params.cname = this.select_params.keywords
      } else if (this.select_params.type == 5) {
        this.setParams('mobile_place')
        this.placeholder = '请输入归属地'
        this.params.mobile_place = this.select_params.keywords
      } else if (this.select_params.type == 6) {
        this.setParams('follow_keywords')
        this.placeholder = '请输入跟进内容'
        this.params.follow_keywords = this.select_params.keywords
      }
      this.handleSearch()
    },
    debounce(fn, delay, immediate = false) {
      // 1.定义一个定时器, 保存上一次的定时器
      let timer = null
      let isInvoke = false
      // 2.真正执行的函数
      const _debounce = function (...ages) {
        // 取消上一次的定时器
        if (timer) clearTimeout(timer)

        // 判断是否需要立即执行
        if (immediate && !isInvoke) {
          fn.apply(this, ages)
          isInvoke = true
        } else {
          // 延迟执行
          timer = setTimeout(() => {
            // 外部传入的真正要执行的函数
            fn.apply(this, ages)
            isInvoke = false
          }, delay)
        }
      }

      return _debounce
    },
    handleResize() {
      // 获取屏幕宽度
      const filter = document.getElementById("filter")

      this.topHeight = filter.offsetHeight + 120
      // 获取屏幕宽度
      this.getScreenWidth();
    },
    handleScroll() {
      const page = document.getElementsByClassName("main-content")[0];
      this.scrollTop = page.scrollTop
    },
    getScreenWidth() {
      if (window.innerWidth <= 1350) {
        this.buttonhidden = false
        this.is_small_system = true
        this.myButton1 = true
      } else {
        this.is_small_system = false
        this.buttonhidden = true
        this.myButton1 = false
      }
    },
    //获取部门成员信息
    getMemberList() {
      this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data;
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user,
          });
          this.recursionData(this.memberList);
          this.Not_duplicate_datalist = JSON.parse(
            JSON.stringify(this.datalist)
          );
          this.filteredData();
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
              }
            }
          }
        }
      });
    },
    recursionData(data) {
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              });
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item);
            }
          });
          this.recursionData(data[key].subs);
        }
      }
    },
    //选中部门人员
    selecetedMember(e) {
      // console.log(e.checkedNodes, "成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.admin_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.admin_id = "";
      }
      this.show_member_list = false;
    },
    selecetedMember1(e) {
      // console.log(e.checkedNodes, "成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id1 = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.create_id =
          e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.create_id = "";
      }
      this.show_member_list = false;
    },
    //关闭弹窗之间的回调
    cancels() {
      this.is_dialog_upload = false;
      this.upload_form = {
        type: 1,
        admin_id: "",
        create_id: "",
        file: "",
      };
    },
    checkStatus(item) {
      if (item.push_type == 2) {
        // 手动认领  
        // 我司成交的不可认领
        if (item.tracking_identify == 1) {
          return true
        }
        //潜在用户不受影响，都可以领取
        return false
      }
      return false
    },
    // 获取信息展示
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.status_id = res.data.id;
          this.selfID = res.data.id;
          if (res.data.roles[0].name === "站长") {
            this.is_show_upload = true;
          } else {
            this.getSiteCrmSetting(res.data.id);
          }
        }
      });
    },
    // filterTime(val) {
    //     var date = new Date(val * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    //     var Y = date.getFullYear() + '-';
    //     var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
    //     var D = date.getDate() + ' ';
    //     var h = date.getHours() + ':';
    //     var m = date.getMinutes() + ':';
    //     var s = date.getSeconds();
    //     return Y+M+D+h+m+s;
    // },
    // 获取批量导入的crm站点设置
    getSiteCrmSetting(id) {
      this.$http.getAuthShow("batch_import_uid").then((res) => {
        if (res.status === 200) {
          let num = [];
          num.push(res.data);
          // console.log(num,"num",id)
          if (num.indexOf(id) != -1 || !res.data) {
            this.is_show_upload = true;
          }
        }
      });
    },
    // 快速编辑标签
    async fastEditLabel(row) {
      this.multipleSelection = []; // 清空
      this.multipleSelection.push(row.id); // 赋值客户id
      this.is_fast = true; // 设置为快速编辑标签
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        });
      });
      //console.log(row,"row");
      this.fastEdit_params.client_id = row.id;
      new Promise((resolve) => {
        // 获取客户标签列表
        if (!this.labels_list.length) {
          this.getLabelGroupNoPageNew();
        }
        if (!this.labels_list.length) {
          setTimeout(() => {
            resolve();
          }, 500);
        } else {
          resolve();
        }
      }).then(() => {
        this.show_Customer_label = true; // 显示模态框
        // console.log(this.labels_list,"this.labels_list");
        row.label.map((item) => {
          this.labels_list.map((list) => {
            list.label.map((arr) => {
              if (arr.name == item) {
                arr.check = true;
              }
            });
          });
        });
        this.$forceUpdate();
      });
    },
    // 获取是否开通批量自动分配的权限
    getpl(){
      this.$http.getAuthShow("auto_allocation_uid").then(res=>{
        if(res.status==200){
          // this.getpldata = res.data
          const dataArray = res.data.split(","); // 假设用逗号分隔
          const match = dataArray.includes(String(this.shitu_list.id));
          if (match) {
            this.cus_list.push({id:3, name: "批量自动分配"})
          } else {
            if(this.show_shitu){
              this.cus_list.push({id:3, name: "批量自动分配"})
            }
          }
        }
      })
    },
    resetLoudongSearch() {
      this.params.mobile = "";
      this.handleSearch()
      this.show_tel_search = false
    },
    handleSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    //清空
    empty() {
      // this.source_list
      // console.log(123);
      this.source_index = 0
      this.tracking_index = 0
      this.type_index = 0
      this.bind_index = 0
      this.level_index = 0
      this.statusdata_index = 0
      this.time_index = 0
      this.select_params.keywords = ''
      this.timeValue = ""; // 清空自定义时间绑定值
      this.is_all = true;
      this.changeParentLabel = "";
      this.selectedMember = '';
      // this.department = 1
      this.customerLabelList = {};
      this.datetime_type = 1,//时间类型
      this.customer_statusvalue = "",// 客户状态
      this.Source_statusvalue = "",//客户来源
      this.typeLabel_value = "",//客户类型
      this.typeqiwei_value = "",//绑定企微
      this.grade_value = "",//客户等级
      this.customerstatus_value = "",//通话状态
      this.showstatus_value = "",//带看状态
      this.customerLabelListcopy = [], // 所选择的标签
      this.labeldata = [],
      // delete this.params.label;
      this.params = {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 3,
        // status: 0,
        mobile: "",
        // sort_type: 0,
        c_type1: 4, // 公海客户状态 1:新增线索 2：最新跟进 3：多条线索 4：潜在客户
        c_type4: 0, // 潜在客户类型 0：全部 1：转公 2：掉公 3：锁定
        department_id: 0,
        admin_id: 0,
        c_type6: 0,
        date_sort: 1
      },
        this.$forceUpdate()
      this.getDataList(); // 获取最新数据
    },
    //刷新
    Refresh() {
      this.getDataList(); // 获取最新数据
    },
    Topping() {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      this.multipleSelectionname.map(item => {
        // console.log(item.id);
        this.$http.setMyClientTop(item.id).then(res => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            this.getDataList(); // 刷新客户详情数据
          }
        })
      })

    },
    getCrmCustomerFollowNumber() {
      this.$http.getCrmCustomerFollowNumber().then((res) => {
        if (res.status === 200) {
          this.no_follow_number = res.data;
        }
      });
    },
    changeTab(e, type) {
      if (type == 1) {
        this.params.status = e.id;
      } else {
        this.params.type = e.id;
      }
      this.params.page = 1;
      this.$nextTick(()=>{
        this.getDataList();
      })
    },
    getTypelist() {
      this.$http.getCrmCustomerTypeDataNopage().then((res) => {
        // console.log(res.data);
        if (res.status === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
          this.push_form.type = res.data.filter((item) => {
            return item.is_default;
          })[0].id;
          let cus_type = parseInt(this.$route.query.cus_type);
          res.data.map((item) => {
            if (cus_type == 1 && item.title == "求购") {
              this.params.type = item.id;
            }
            if (cus_type == 2 && item.title == "求租") {
              this.params.type = item.id;
            }
          });
        }
        this.ponent_maintain_type = JSON.parse(JSON.stringify(res.data));
        let i = this.customer_list_type.findIndex(
          (item) => item.id == this.params.c_type4
        );
        if (i != -1) {
          this.getDataLists(this.customer_list_type[i]);
        }
      });
    },
    getLabelList() {
      this.$http.getLabelGroupNoPage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_label = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户类型列表
          this.label_list = res.data;
          // console.log(this.label_list,"查看");
          this.label_default_list = res.data;
        }
      });
    },
    getDataLists(item) {
      // if (item.title == '新增线索') {
      //   this.notNewClue = true;
      //   let isNewClue_update = this.table_header.filter((remo) => {
      //     return remo.label == "更新时间";
      //   })
      //   if (isNewClue_update.length != 0) {
      //     if (this.notNewClue) {
      //       this.table_header.splice(4, 1, { prop: 'source.title', label: '来源' });
      //     }
      //   }
      //   let isNewClue_create = this.table_header.filter((remo) => {
      //     return remo.label == "创建时间";
      //   })
      //   if (isNewClue_create.length == 0) {
      //     this.table_header.splice(4, 0, { prop: "created_at", label: "创建时间" });
      //   }
      // }
      // // 如果不是“新增线索”就删除创建时间，添加更新时间
      // else if (item.title != '新增线索') {
      //   this.notNewClue = false;
      //   let notNewClue_create = this.table_header.filter((remo) => {
      //     return remo.label == "创建时间";
      //   })
      //   if (notNewClue_create.length != 0) {
      //     this.table_header.splice(4, 1);
      //   }
      //   let notNewClue_update = this.table_header.filter((remo) => {
      //     return remo.label == "更新时间";
      //   })
      //   if (notNewClue_update.length == 0) {
      //     this.table_header.splice(4, 0, { prop: "updated_at", label: "更新时间" });
      //   }
      // }
      console.log(item);
      // if(item.id==1||item.id==2){
      //   if(item.id==1){
      //     this.department = 1
      //     this.disabledment = true
      //     this.params.admin_id = 0; // 赋值0
      //     // this.params.last_zg_uid = 0
      //   }else{
      //     this.department = 2 
      //     this.disabledment = true
      //     this.params.admin_id = 0; // 赋值0
      //     // this.params.last_zg_uid = 0
      //   } 
      // }else{
        // this.selectedMember = "";
        // this.disabledment = false
        // this.params.admin_id = 0; // 赋值0
        //   this.params.last_zg_uid = 0
      // }
      this.params.c_type4 = item.id;
      // console.log(this.params);
      this.params.type = 0;
      this.params.page = 1;
      this.$nextTick(() => {
        this.getDataList();
      });
    },
    // getSecondDataLists(item) {
    //   console.log(item);
    //   if (item.title != '新增线索') {
    //     this.notNewClue = false;
    //     let notNewClue_create = this.table_header.filter((remo) => {
    //       return remo.label == "创建时间";
    //     })
    //     if (notNewClue_create.length != 0) {
    //       this.table_header.splice(4, 1);
    //     }
    //     let notNewClue_update = this.table_header.filter((remo) => {
    //       return remo.label == "更新时间";
    //     })
    //     if (notNewClue_update.length == 0) {
    //       this.table_header.splice(4, 0, { prop: "last_follow_time", label: "更新时间" });
    //     }
    //   }
    //   if (item) {
    //     // this.customer_type = item.id;
    //     // this.params.sort_type = item.id;
    //     delete this.params.status;
    //     this.params.sort_type = item.id;
    //   }
    //   this.params.page = 1
    //   this.getDataList()
    // },
    getDataList() {
      let params = Object.assign({}, this.params);
      this.is_table_loading = true;
      if (!this.params.source_id) {
        delete params.source_id;
      }
      if (!this.params.tracking_id) {
        delete params.tracking_id;
      }
      if (!this.params.is_bind) {
        delete params.is_bind;
      }
      if (this.params.call_status == 0) {
        delete params.call_status;
      }
      if (!this.params.type) {
            params.type = 0
      }
      if (!this.params.level_id) {
            params.level_id = 0
      }
      if( this.params.date_style==0){
        delete params.date_style
      }
      // params.public_status = 2
      // console.log(params);
      Object.assign(params, this.customTabParams);
      this.$http.getCrmCustomerClientList({ params }).then((res) => {
        // console.log(res.data, "数据red.data");
        this.is_table_loading = false;
        if (res.status === 200) {
          // res.data.data.forEach(function (item) {
          //   console.log(item.updated_at);
          //   const now = new Date();
          //   const date = new Date(item.updated_at);
          //   console.log(date);
          //   const timeDiff = Math.floor((now - date) / (1000 * 60 * 60 * 24));
          //   console.log(timeDiff);
          //   let timeAgo;
          //   if (timeDiff === 0) {
          //     timeAgo = "今天";
          //   } else if (timeDiff === 1) {
          //     timeAgo = "一天前";
          //   } else {
          //     timeAgo = timeDiff + "天前";
          //   }
          //   console.log(timeAgo);
          //  item.updated_at =timeAgo
          //   this.getFollowDay(item.updated_at)
          // });
          this.tableData = res.data.data;
          // console.log(this.tableData);
          if(this.tableData.length){
              this.is_show_city = this.tableData[0].is_show_city
            }
          console.log(this.is_show_city);
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    getLevelData() {
      this.$http.getCrmCustomerLevelNopage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_level = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户级别列表
          this.level_list = [...res.data];
        }
      });
    },
    getTrackingList() {
      // console.log(this.tracking_params);
      this.$http
        .getCrmCustomerFollowInfo({ params: this.tracking_params })
        .then((res) => {
          // console.log(res.data);
          if (res.status === 200) {
            // if(this.website_id==109||this.website_id==176){
              this.tracking_list = [ ...res.data];
            // }else{
            //   this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
            // }
          }
        });
    },
    toShitu() {
      this.$goPath("/crm_Follow_up_list")
    },
    resetXiansuoSearch() {
      this.params.keywords = "";
      this.handleSearch()
      this.show_xiansuo_search = false
    },
    onClickSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    onClickType(e, type) {
      switch (type) {
        case 1:
          this.params.source_id =  e.join(",")||"";
          break;
        case 2:
        // if(this.website_id==109||this.website_id==176){
            this.params.tracking_id = e.join(",")||""
        // }else{
        //     this.params.tracking_id = e;
        // }
          break;
        case 3:
          this.params.is_bind = e;
          break;
        case 4:
          this.params.date_style = e
          this.params.date_sort = 1;  //重置排序
          // this.params.date_type = e.id;
          // delete this.params.start_date; // 自定义时间发生改变，清空开始时间
          // delete this.params.end_date; // 清空结束时间
          // this.timeValue = ""; // 清空自定义时间绑定值
          break;
        case 5:
          this.params.level_id =  e.join(",")||"";
          break;
        case 6:
          this.params.type = e;
          break;
        case 7:
          this.params.call_status = e;
          break;
        case 9:
          //带看状态
          if(e==0){
           delete this.params.take_status
          }else{
            this.params.take_status = e;
          }
          break;
        default:
          break;
      }
      this.params.page = 1;
      this.$nextTick(()=>{
        this.getDataList();
      })
    },
    onClick(e, index) {
      // console.log(e);
      if (this.activeIndex == index) {
        return;
      }
      this.activeIndex = index;
    },
    onChangeTime(e) {
      this.params.start_date = e ? e[0] : ""; // 赋值开始时间
      this.params.end_date = e? e[1] : ""; // 赋值结束时间
      // this.params.date_type = 0; // 清空筛选时间
      // this.$refs.childRef.clearScreening(); // 重置筛选时间为全部
      this.params.page = 1; // 显示第一页
      this.getDataList(); // 获取最新数据
    },
    getSourceData() {
      this.$http.listcustomersourcenew().then((res) => {
        if (res.status === 200) {
          let arr = JSON.parse(JSON.stringify(res.data));
          arr.map((item)=>{
            if(item.children==0){
              delete item.children
            }
          })
          this.ponent_maintain_source = JSON.parse(JSON.stringify(arr)) // 深拷贝客户来源列表
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
          this.source_list.map((item)=>{
            if(item.children==0){
              delete item.children
            }
          })
          this.push_form.source_id = res.data.filter((item) => {
            return item.is_default == 1;
          })[0].id;
        }
      });
    },
    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    //验证手机号
    Validationphone(phoneNumber){
      console.log(phoneNumber);
      if(phoneNumber){
        const regex = /^(?:1[3-9]\d{9}|(?:\+?852|00852)?[5-9]\d{7}|(?:\+?853|00853)?[6-9]\d{7})$/;
        if (regex.test(phoneNumber)) {
          // this.$message.success('手机号格式正确')
        } else {
          this.$message.warning('请输入正确的手机号')
        }
      }

    },
    addDomain() {
      this.other_mobile.push({
        mobile: "",
      });
    },
    onClickLevel(item) {
      this.push_form.level_id = item.id;
    },
    onClickTypeClient(item) {
      this.push_form.type = item.id;
    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    selectionChange(e) {
      this.multipleSelectionname = e; // 赋值当前客户信息
      let arr = e.map((item) => {
        return item.id;
      });
      this.multipleSelection = arr; // 赋值当前客户的id
      // 只有在客户标签列表为空时请求数据
      if (!this.labels_list.length) {
        this.getLabelGroupNoPageNew();
      }
    },
    async onClickDetail(row) {

      let res = await this.$http.getForceFollow()
      if (res.status == 200 && res.data && res.data.id > 0) {
        this.$confirm("您有未跟进的客户确认去跟进吗？", "提示").then(() => {
          let url = `/crm_customer_detail?id=${res.data.client_id}&type=seas&tel_follow_id=${res.data.id}`;
          this.$goPath(url);
        })
        return
      }
      let url = `/crm_customer_detail?id=${row.id}&type=seas`;
      this.$goPath(url);
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    //每页几条
    handleSizeChange(e){
      this.params.per_page = e
      this.getDataList();
    },
    onPageChangeQwFollow(e) {
      this.is_follow_params.page = e;
      this.getFollow();
    },
    isShowFollow(row) {
      this.is_follow_dialog = true;
      this.is_follow_params.client_id = row.client_id;
      this.getFollow();
    },
    getFollow() {
      this.is_follow_loading = true;
      this.$http
        .getCrmCustomerFollowData({ params: this.is_follow_params })
        .then((res) => {
          this.is_follow_loading = false;
          if (res.status === 200) {
            this.is_follow_data = res.data.data;
            this.is_follow_params.total = res.data.total;
          }
        });
    },
    // onClickForm(e) {
    //   this.$http.setCrmCustomerData(e).then((res) => {
    //     if (res.status === 200) {
    //       this.$message.success("操作成功");
    //       this.is_push_customer = false;
    //       this.form = {};
    //       this.form1 = {};
    //       this.params.page = 1;
    //       this.getDataList();
    //     }
    //   });
    // },
    provincesChange(e){
          console.log(e,"e");
          this.push_form.province_id = e[0]
          this.push_form.city_id = e[1]
          this.push_form.area_id = e[2]
        },
    Cities_provinces(){
          this.$http.cities_and_provinces().then((res)=>{
            if(res.status==200){
              // console.log(res.data,"省市区");
              this.provincesoptions = res.data
            }
          })
        },
    //录入客户，客户来源
    sourceLabel_status(e){
      console.log(e);
      if(e.length>1){
        this.push_form.source2_id = e[1]
        this.push_form.source_id = e[0]
      }else{
        this.push_form.source_id = e[0]
        this.push_form.source2_id = 0
      }

    },
    //导入客户来源
    sourceLabelimport(e){
      console.log(e,"wee");
      if (e.length) {
        if(e.length>1){
        this.upload_form.source2_id = e[1]
        this.upload_form.source_id = e[0]
      }else{
        this.upload_form.source_id = e[0]
        this.upload_form.source2_id  = 0
      }
      }else{
        this.upload_form.source2_id = 0
        this.upload_form.source_id = 0
      }
    },
    onClickForm(status) {
      // if(this.website_id==109||this.website_id==176){
        if(status==1){
          this.$refs.myMaintenancecopy.onClickForm();
        }else{
          this.$refs.enterinformation.onClickForm();
        }
      // }else{
      //   if (
      //   this.push_form.label &&
      //   this.push_form.label != undefined &&
      //   typeof this.push_form.label !== "string"
      // ) {
      //   this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
      // }
      // if (this.other_mobile.length > 0) {
      //   let arr = this.other_mobile.map((item) => {
      //     return item.mobile;
      //   });
      //   let othertel = arr.filter((item, index) => {
      //     if (index) {
      //       return item;
      //     }
      //   });
      //   this.push_form.mobile = arr[0];
      //   this.push_form.subsidiary_mobile = othertel.join(",");
      // }
      // if (!this.push_form.mobile) {
      //   this.$message.error("请检查联系方式");
      //   return;
      // }
      // if (!this.push_form.cname) {
      //   this.$message.error("请检查客户姓名");
      //   return;
      // }
      // if (!this.push_form.sex) {
      //   this.$message.error("请检查客户性别");
      //   return;
      // }
      // if (!this.push_form.level_id) {
      //   this.push_form.level_id = 0
      //   // this.$message.error("请检查客户等级");
      //   // return;
      // }
      // if (!this.push_form.type) {
      //   this.$message.error("请检查客户类型");
      //   return;
      // }
      // if (!this.push_form.source_id) {
      //   this.$message.error("请检查客户来源");
      //   return;
      // }
      // if (!this.push_form.source2_id) {
      //   this.push_form.source2_id = 0
      // }

      // const params = {...this.push_form};
      // params.intention_community = params.intention_community.length ? params.intention_community.join(",") : '';

      // this.is_button_loading = true;
      // this.$http.setCrmCustomerDataV2(params).then((res) => {
      //   this.is_button_loading = false;

      //   if (res.status === 200) {
      //     this.$message.success("操作成功");
      //     this.getDataList();
      //     this.reset()
      //     this.provincesvalue = []
      //     this.source_idvalue =""
      //     this.is_push_customer = false;
      //   } else if (res.status === 422) {
      //     const cus_id =
      //       res.data.data &&
      //         res.data.data.id != "" &&
      //         res.data.data.id != undefined
      //         ? res.data.data.id
      //         : 0; // 赋值客户id
      //     // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
      //     if (
      //       res.data.data &&
      //       res.data.data.follow_id &&
      //       res.data.data.follow_id != undefined &&
      //       res.data.data.follow_id != 0
      //     ) {
      //       this.$confirm("客户号码已有维护人，无法重复录入。", "提示", {
      //         confirmButtonText: "立即查看",
      //         cancelButtonText: "取消",
      //         type: "warning",
      //       })
      //         .then(() => {
      //           let url = `/crm_customer_detail?id=${cus_id}&type=my`;
      //           this.$goPath(url); // 跳转客户详情
      //         })
      //         .catch(() => {
      //           return;
      //         });
      //       this.is_push_customer = false;
      //     } else {
      //       // 该客户没有维护跟进人时触发
      //       if (cus_id > 0) {
      //         this.$confirm("客户号码已存在，认领后可跟进。", "提示", {
      //           confirmButtonText: "立即认领",
      //           cancelButtonText: "取消",
      //           type: "warning",
      //         })
      //           .then(() => {
      //             this.$http
      //               .getCrmCustomerPublick({ ids: cus_id + "" })
      //               .then((res) => {
      //                 if (res.status === 200) {
      //                   this.$message.success("认领成功");
      //                   let url = `/crm_customer_detail?id=${cus_id}&type=my`;
      //                   this.$goPath(url); // 跳转客户详情
      //                 }
      //               });
      //           })
      //           .catch(() => {
      //             return;
      //           });
      //         this.is_push_customer = false;
      //       }
      //     }
      //   }
      // });
      // }
    },
    // 折叠面板
    onChangeCollapse() {
      this.is_collapse = !this.is_collapse;
    },
    onClickGet(row) {
      this.$confirm("是否认领该客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // console.log(row.id);
          this.$http.getCrmCustomerPublick({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("认领成功");
              let url = `/crm_customer_detail?id=${row.id}&type=my`;
              this.$goPath(url);
              this.Refresh();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    leftla() {
      this.buttonhidden = true
      this.myButton = true
      this.myButton1 = false
    },
    Rightdrag() {
      this.myButton1 = true
      this.buttonhidden = false
      this.myButton = false
    },
    // handleChangeLabel() {
    //   this.params.page = 1;
    //   this.getDataList();
    // },
    //录入客户
    push_customerA(){
      // this.getTypelist();
      this.Cities_provinces()
      this.push_form.type = this.type_list.filter((item) => {
            return item.is_default;
          })[0].id;
          // if(this.website_id==109||this.website_id==176){
            this.enterdrawer = true
          // }else{
          //    this.is_push_customer = true
          // }
    },
    //录入客户取消事件
    enterdrawerfalse(){
      this.enterdrawer = false
      this.params.page = 1;
      this.getDataList();
    },
    //导入
    getFile() {
      // this.getTypelist();
      this.getMemberList();
      // if(this.website_id==109){
      this.importdrawer = true
      // }else{
        // this.is_dialog_upload = true;
      // }
      // this.$refs.file.click();
    },
    //开始导入
    Starting_import(){
      this.$refs.importinformation.Starting_import();
    },
    // 侧边栏合上
    closesidebar(){
      this.importdrawer = false
      this.params.page = 1;
      this.getDataList();
    },
    handleSuccessAvatarTemporary(response) {

      this.handleFileUpload(response.url)
    },
    // 获取文件
    handleFileUpload(response) {
      // console.log("执行");
      // 阻止发生默认行为
      // event.preventDefault();
      let url = response;
      let formData = new FormData();
      formData.append("url", url);
      formData.append("admin_id", this.upload_form.admin_id || 0);
      formData.append("type", this.upload_form.type);
      formData.append("type_id", this.upload_form.type_id);
      formData.append("source_id", this.upload_form.source_id);
      formData.append("source2_id", this.upload_form.source2_id);
      formData.append("create_id", this.upload_form.create_id || 0);
      if (Array.isArray(this.upload_form.label)) {
        formData.append("label", this.upload_form.label.join(","));
      } else {
        formData.append("label", this.upload_form.label);
      }
      formData.append("remark", this.upload_form.remark);
      // this.formData.get("file");
      this.onUpload(formData);
    },
    // 上传文件
    onUpload(formData) {
      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      // this.$message.success("正在上传...");
      this.$http.uploadCrmCustomerDataURL(formData).then((res) => {
        // this.$http.uploadCrmCustomerData(formData).then((res) => {
        if (res.status === 200) {
          // 如果只有新增
          let text =
            "导入成功，新增客户数量" +
            res.data.success_num +
            "条,系统重复客户数量" +
            res.data.crm_repeat_num +
            "条,表格重复客户数量" +
            res.data.excel_repeat_num +
            "条,失败客户数量" +
            res.data.error_num +
            "条";
          // if (res.data.add_num > 0 && res.data.edit_num == 0) {
          //   this.$message.success("导入成功，新增" + res.data.add_num + "条");
          // } else if (res.data.add_num == 0 && res.data.edit_num > 0) { // 如果只有编辑覆盖
          //   this.$message.success("导入成功，编辑覆盖" + res.data.edit_num + "条");
          // } else if (res.data.add_num > 0 && res.data.edit_num > 0) { // 如果新增和覆盖都有
          //   this.$message.success("导入成功，新增" + res.data.add_num + "条，编辑覆盖" + res.data.edit_num + "条");
          // } else if (res.data.add_num == 0 && res.data.edit_num == 0) { // 如果新增和覆盖都没有
          //   this.$message.error("导入失败，新增" + res.data.add_num + "条，编辑覆盖" + res.data.edit_num + "条");
          // }
          this.loading.close();
          this.$confirm(text, "提示", {
            confirmButtonText: "查看详情",
            cancelButtonText: "取消",
            type: "success",
          })
            .then(() => {
              this.params.page = 1;
              this.getDataList();
              this.is_dialog_upload = false;
              this.is_loading = false;
              let url = `/crm_customer_task_list?task_id=` + res.data.task_id;
              this.$goPath(url); // 跳转客户详情
            })
            .catch(() => {
              this.params.page = 1;
              this.getDataList();
              this.is_dialog_upload = false;
              this.is_loading = false;
            });
        } else {
          this.loading.close();
        }
      });
    },
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/client_template_new.xls?f=" +
        +new Date()
      );
    },
    //表单重置
    reset() {
      this.push_form = {
        cname: "",
        source_id: "",
        source2_id: "",
        level_id: "",
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: [],
        label: "",
        // intention_street: "",
        remark: "",
      };
      this.other_mobile = [{ mobile: "" }];
    },
    //取消
    cancel() {
      this.reset();
      this.is_push_customer = false;
    },
    //一个个删除标签
    dellabeld(index,id){
        if (index > -1 && index <  this.labeldata.length) {
          // 如果索引值在数组长度范围内，则可以删除元素
          this.labeldata.splice(index, 1);
        }
       let labelid = this.customerLabelListcopy.indexOf(id);
        if (labelid > -1) {
          this.customerLabelListcopy.splice(labelid, 1);
        }
      this.params.label = this.customerLabelListcopy.join(",")
      this.params.page = 1;
      this.getDataList();
    },
    //清空标签
    clearlabel(){
      this.labeldata = []
      this.customerLabelListcopy = []
      this.params.label = ""
      this.params.page = 1;
      this.getDataList();
    },
    //选中客户标签
    handleCommand(item, label) {
      // 检查是否已存在相同的 item 和 label 组合
      const isDuplicate = this.labeldata.some(data => data.id === item.id);
      if (!isDuplicate) {
        this.labeldata.push({ name: item.name, title: label.name,id:item.id });
        this.customerLabelListcopy.push(item.id )
      }
        this.params.label = this.customerLabelListcopy.join(",")
        this.params.page = 1;
        this.getDataList();
    },
    // 检索客户标签发生改变时触发
    changeCustomerLabel(val) {
      this.is_all = false;
      // 父级标签id赋值
      let label_num = Object.keys(this.customerLabelList).length;
      // 如果选择的标签大于1就删除之前选择的标签
      if (label_num > 1) {
        delete this.customerLabelList[this.changeParentLabel];
      }
      this.changeParentLabel = val[2].id;
      // 更新数据
      this.params.label = val[0];
      this.params.page = 1;
      this.getDataList();
    },
    getSelectWidth(item, LabelList) {
      var text_temp = "",
        text_width = 0,
        text_el = null;
      var current_option = "";
      if (LabelList) {
        current_option =
          item.label && item.label.find((arr) => arr.id === LabelList[0]);
      } else {
        current_option = false;
      }
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${item.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text");
      text_width = text_el.offsetWidth;
      text_el.remove();
      return text_width + 57 + "px";
    },
    getAllLabelList() {
      this.is_all = true;
      this.changeParentLabel = "";
      this.customerLabelList = {};
      delete this.params.label;
      this.params.page = 1;
      this.getDataList();
    },
    // 开始导入
    startImport() {
      this.is_loading = true;
      if (
        this.upload_form.type_id == "" ||
        this.upload_form.type_id == undefined
      ) {
        this.upload_form.type_id = 0;
      }
      if (
        this.upload_form.source_id == "" ||
        this.upload_form.source_id == undefined
      ) {
        this.upload_form.source_id = 0;
      }
      if (
        this.upload_form.source2_id == "" ||
        this.upload_form.source2_id == undefined
      ) {
        this.upload_form.source2_id = 0;
      }
      // 处理为正常部门成员id
      if (this.upload_form.admin_id.toString().length >= 6) {
        this.upload_form.admin_id = parseInt(
          this.upload_form.admin_id.toString().slice(0, 3)
        );
      }
      // this.$refs.file.click();
      this.$refs["upload"].$refs["upload-inner"].handleClick()
      this.is_loading = false;
    },
    // 清除当前选择成员
    delName() {
      this.uploadAdmin_id = "";
      this.upload_form.admin_id = 0;
    },
    // 点击设置客户标签按钮
    setCustomerLabel() {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      this.is_fast = false;
      this.show_Customer_label = true; // 显示模态框
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        });
      });
    },
    // 选中标签
    checkChangeLabels(index0, index) {
      let that = this;
      that.labels_list[index0].label[index].check =
        !that.labels_list[index0].label[index].check;
      this.$forceUpdate();
    },
    // 确定更改客户标签
    confirmFastSelected() {
      this.is_loading = true;
      let num = []; // 已选客户标签容器
      // 遍历出勾选中的客户标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          if (list.check) {
            num.push(list.id);
          }
        });
      });
      // 赋值已选中的客户标签参数
      if (num && num.length) {
        this.confirm_batch_list.label = num.join(",");
      }
      // 赋值已选中的客户id参数
      if (this.multipleSelection && this.multipleSelection.length) {
        this.confirm_batch_list.client_id = this.multipleSelection.join(",");
      }
      // 请求接口
      this.$http
        .setCrmCustomerLabelsData(this.confirm_batch_list)
        .then((res) => {
          if (res.status == 200) {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.show_Customer_label = false; // 关闭模态框
            this.is_loading = false;
            this.getDataList(); // 获取最新数据
          } else {
            this.is_loading = false;
          }
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
      // 确定更改客户标签
      confirmSelected() {
      this.is_loading = true;
      let num = []; // 已选客户标签容器
      // 遍历出勾选中的客户标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          if (list.check) {
            num.push(list.id);
          }
        });
      });
      // 赋值已选中的客户标签参数
      if (num && num.length) {
        this.confirm_batch_list.label = num.join(",");
      }
      // 赋值已选中的客户id参数
      if (this.multipleSelection && this.multipleSelection.length) {
        this.confirm_batch_list.ids = this.multipleSelection.join(",");
      }
      // 请求接口
      this.$http
        .newbatchSetLabelGroup(this.confirm_batch_list)
        .then((res) => {
          if (res.status == 200) {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.show_Customer_label = false; // 关闭模态框
            this.is_loading = false;
            this.getDataList(); // 获取最新数据
          }
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
    // 导入成员当获取焦点时触发
    focusSelete() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_member_list = true;
    },
    focusSelete1() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_member_list1 = true;
    },
    // 获取客户标签列表
    getLabelGroupNoPageNew() {
      // 获取客户标签列表
      this.$http.getLabelGroupNoPageNew().then((res) => {
        if (res.status == 200) {
          if (res.data.qiwei_tag && res.data.qiwei_tag.length) {
            res.data.qiwei_tag.map((item) => {
              item.label = item.taggroup;
              delete item.taggroup;
            });
          }
          this.labels_list = res.data.qiwei_tag.concat(res.data.system_tag);
        }
        // 格式化数据-添加check属性
        if (this.labels_list && this.labels_list.length) {
          this.labels_list.map((item) => {
            if (item.label) {
              item.label.map((list) => {
                list.check = false;
              });
            }
          });
        }
      });
    },
    // 查询归属地
    HomeAddress(row) {
      this.$http.inquireHomeAddress(row.id).then((res) => {
        if (res.status == 200) {
          row.mobile_place = res.data;
          //this.getDataList(); // 获取最新数据
        }
      });
    },
    // 点击全部部门
    Reqdepartment(){
      this.getDepartmentList(); // 获取部门
    },
    //点击全部成员
    RequestMembers(){
      if (!this.datalist.length) {
        this.getMemberList();
      }
    },
    // 部门选取发生改变
    changePopDepar(val) {
      this.selectedMember = "";
      if (val != null) {
        this.filtrMember = [];
        this.filtrDepartMember(this.memberList, val);
        this.filtrMember = this.filtrMember.filter((item) => {
          return item.id.toString().length <= 4;
        });
      } else {
        this.filteredData();
      }
      this.handleSearch()
    },
    // 选中对应部门，遍历出部门成员
    filtrDepartMember(data, val) {
      for (let key in data) {
        // console.log(data[key].id,val,"执行",data[key].id == val)
        if (data[key].id == val) {
          if (data[key].subs) {
            data[key].subs.map((item) => {
              if (item.user) {
                item.user.map((list) => {
                  this.filtrMember.push(list);
                });
              } else if (item.user_name) {
                this.filtrMember.push(item);
              }
            });
          }
        }
        this.filtrDepartMember(data[key].subs, val);
      }
    },
    // 部门成员去重
    filteredData() {
      const uniqueIds = {};
      const filtered = [];
      for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
        const item = this.Not_duplicate_datalist[i];
        if (!uniqueIds[item.id]) {
          uniqueIds[item.id] = true;
          filtered.push(item);
        }
      }
      this.filtrMember = filtered;
    },
    // 获取全部的部门
    getCrmDepartmentList() {
      // this.department = 1
      // this.selectedMember =''
      // this.params.admin_id = 0; // 清空搜索内容
      // this.params.department_id = 0; // 清空搜索内容
      // this.getDepartmentList(); // 获取部门
      // 获取部门成员
      // if (!this.datalist.length) {
      //   this.getMemberList();
      // }
      // if (!this.AllDepartment.length) {
      //   this.$http.getCrmDepartmentList().then((res) => {
      //     if (res.status == 200) {
      //       this.AllDepartment = res.data;
      //     }
      //   })
      // }
    },
    // 获取部门
    getDepartmentList() {
      if (!this.AllDepartment.length) {
        this.$http.getCrmDepartmentList().then((res) => {
          if (res.status == 200) {
            this.AllDepartment = res.data;
          }
        });
      }
    },
    // 按照部门搜索
    searchDepart() {
      if (this.selectedMember == "" || this.selectedMember == undefined) {
        this.params.admin_id = 0; // 赋值0
      }
      this.handleSearch();
    },
    // 按照成员搜索
    // searchMember() {
    //   // 处理id为前三位
    //   if (this.params.admin_id.toString().length >= 6) {
    //     this.params.admin_id = parseInt(
    //       this.params.admin_id.toString().slice(0, 3)
    //     );
    //   }
    //   this.handleSearch()
    // },
    // 搜索部门成员发生改变时触发
    changeSearchMember() {
      // this.selectedMember = ''
      // 如果搜索的参数为空或undefined
      if(this.department == 2){
        if (this.selectedMember == "" || this.selectedMember == undefined) {
          this.params.admin_id = 0; // 赋值0
          // this.params.admin_id = 0
        } else {
          this.params.admin_id = this.selectedMember; // 有值则赋值
          // this.params.last_zg_uid = 0
        }
      }else if(this.department == 1){
        if (this.selectedMember == "" || this.selectedMember == undefined) {
          this.params.admin_id = 0; // 赋值0
          this.params.admin_id = 0
        } else {
          this.params.admin_id = this.selectedMember; // 有值则赋值
          // this.params.last_dg_uid = 0
        }
      }
      this.handleSearch()
    },
    closeMember(){
      this.selectedMember = ''
    },
    // 转交客户
    TransferCustomer(item) {
        // 判断是否选中客户
        if (!this.multipleSelection.length) {
        return this.$message({
          message: "请选择客户",
          type: "warning",
        });
      }
      if(this.multipleSelection.length&&this.multipleSelection.length>50){
        return this.$message({
          message: "每次最多选择50个客户",
          type: "warning",
        });
      }
      
      this.transfer_type = false; // 隐藏pop框
      if (item.id == 1) {
        // 如果是转交指定维护人
        this.is_transfer_customer = true;
        this.changetitle = "转交客户",
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      } else if (item.id == 2) {
        this.changetitle = "复制客户",
        this.is_transfer_customer = true;
      } else if (item.id == 3){
        this.$refs.automatic.open(this.multipleSelection)
      }
      if(!item.id){
        this.is_transfer_customer = true;
        this.changetitle = "转交客户",
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      }
    },
     //复制客户
     oncopycrm(e,id){
      this.$confirm(`是否将所选客户复制到【${e.user_name}】的流转客？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 将数组转换字符串
          if(id==1){
            this.c_id = this.multipleright.toString(); // 数组转换字符串
          }else{
            this.c_id = this.multipleSelection.join(",");
          }
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomercopy({
              user_ids: e.id.toString(),
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.right_transfer_customer = false;
                this.is_transfer_customer = false
                if (id==1) {
                  this.$refs.childRef.getDetail()
                }else{
                 this.getDataList(); // 获取最新数据 
                }
              }
            });
        })
        .catch((err) => {
          console.log(err);
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    //右侧速编转交
    onClickrightcopy(item){
      this.multipleright = this.ids
      this.Transfer_right = false; // 隐藏pop框
       if (item.id == 1) {
        // 如果是转交指定维护人
        this.changetitle = "转交客户",
        this.getAdmin()
        this.right_transfer_customer = true;
        this.admin_params.user_name = "";
        this.admin_params.page = 1;
      } else if (item.id == 2) {
        this.changetitle = "复制客户",
        this.right_transfer_customer = true;
      }
    },
    // 搜索转交人
    onAdminSearch() {
      this.admin_params.page = 1;
      this.getAdmin();
    },
    getAdmin() {
      this.$http
        .getUserList(
          this.admin_params
        )
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    // 确定转让客户
    onZhuanrang(e,id) {
      this.$confirm(`是否将所选客户转交给【${e.user_name}】？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if(id==1){
            this.c_id = this.multipleright.toString(); // 数组转换字符串
          }else{
            this.c_id = this.multipleSelection.join(","); // 数组转换字符串
          }
          // 将数组转换字符串
          // this.c_id = this.multipleSelection.join(",");
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomerZhuanrang({
              be_transfer_id: e.id,
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.is_transfer_customer = false;
                this.getDataList(); // 获取最新数据
                // this.$refs.childRef.getDetail()
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    PersPageChange(e) {
      this.admin_params.page = e;
      this.getAdmin();
    },
    // 关闭客户审批模态框
    closeExamine() {
      this.show_Examine_dialog = false; // 关闭模态框
    },
    // 客户审批提交成功后的回调函数
    submitExamineAfter() {
      this.show_Examine_dialog = false;
      this.getDataList(); // 刷新客户列表
    },
    // 获取客户状态
    getStatus() {
      if (!this.status_list.length) {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.getStutas_params })
          .then((res) => {
            if (res.status === 200) {
              this.status_list = res.data;
              this.status_list.map((item) => {
                if (item.title == "有效客户") {
                  item.value_name = 1;
                } else if (item.title == "无效客户") {
                  item.value_name = 2;
                } else if (item.title == "暂缓客户") {
                  item.value_name = 3;
                } else {
                  item.value_name = 1;
                }
                return item;
              });
              this.copy_status_list = JSON.parse(
                JSON.stringify(this.status_list)
              ); // 深拷贝客户状态
            }
          });
      }
    },
    setStatus(row) {
      this.status_list = JSON.parse(JSON.stringify(this.copy_status_list)); // 重新赋值客户状态
      let delIndex = "";
      this.status_list.map((item, index) => {
        if (item.id == row.tracking.id) {
          delIndex = index;
        }
      });
      if (typeof delIndex == "number") {
        this.status_list.splice(delIndex, 1);
      }
    },
    // 点击更改客户状态
    onClickFollowStatus(row, item) {
      // 审批无需审核 is_state == 2
      if (row.is_state == 2) {
        let examine = false;
        if (row.state_list && row.state_list.length) {
          // 如果state_list中有当前要更改的状态
          row.state_list.map((list) => {
            if (list == item.id) {
              examine = true;
            }
          });
        }
        // 走审批
        if (examine) {
          document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
          (this.Examine_type = 19), // 切换类型为修改客户状态
            (this.show_Examine_dialog = true); // 显示审批模态框
          this.ponent_Examine_data = row; // 赋值客户信息
          this.ponent_Examine_stutas = item; // 选择的要修改的状态
          // 获取部门
          if (!this.AllDepartment.length) {
            this.getDepartmentList();
          }
        } else {
          // 不走审批
          this.$http
            .setCrmCustomerStatus({ id: row.id, tracking_id: item.id })
            .then((res) => {
              if (res.status == 200) {
                this.$message.success("操作成功");
                this.getDataList(); // 刷新数据
              }
            });
        }
      } else {
        this.$message.warning("当前客户状态不可以进行审批");
      }
      // console.log(row, "row", item, 'item')
    },
    // 快速编辑客户维护资料
    fastEditData(row) {
      this.ponent_maintain_data = row;
      // if(this.website_id==109||this.website_id==176){
        this.show_cus_EditA = true;
      // }else{
      //   this.show_cus_Edit = true;
      // }
    },
    // 关闭快速编辑客户维护资料
    fastCloseEdit() {
      this.show_cus_Edit = false;
    },
    // 刷新页面获取最新数据
    submitMaintain() {
      this.getDataList();
      // if(this.website_id==109||this.website_id==176){
        this.show_cus_EditA = false;
      // }else{
      //   this.show_cus_Edit = false;
      // }
    },
    // 快速查看客户手机号
    fastLookTel(row) {
      // const exists = row.admin_list.map(item => parseInt(item, 10)).includes(this.selfID);
      // if(exists==false){
      //   if(row.tracking_identify_name=="我司成交"){
      //   this.$message.warning(" 暂无权限，客户状态为我司成交。")
      //   return
      // }
      // if(row.tracking_identify_name=="他司成交"){
      //   this.$message.warning(" 暂无权限，客户状态为他司成交，如需操作可发起审批更改客户状态。")
      //   return
      // }
      // }
      // 查看电话时跟进
      this.$http.setViewCrmCustomerTel(row.id).then((res) => {
        if (res.status === 200) {
          this.ponent_Tel_data = row;
          this.nowDialData = res.data
          // console.log(this.nowDialData);
          this.show_look_Tel = true; // 显示模态框
          this.getDataList(); // 刷新页面数据
        }
      });
    },
    // 关闭快速查看客户手机号回调函数
    fastCloseTel() {
      this.show_look_Tel = false;
    },
    // 提交查看手机号跟进成功回调函数
    fastSubmitTel() {
      this.getDataList();
      this.show_look_Tel = false;
    },
    // 阻止默认行为和事件传播
    showTypeStatus(e) {
      e.preventDefault();
      e.stopPropagation();
      if (this.is_pullDown) {
        this.is_pullDown = false;
      } else {
        this.is_pullDown = true;
      }
    },
    // 改变搜索类型
    changeScreenType(list, item) {
      // list: 当前选择类型 , item: 选择之前的类型
      // console.log(list, "list", item, "item");
      this.is_pullDown = false; // 关闭popover
      let firstIndex = "";
      let secondIndex = "";
      this.customer_list_type.map((arr, index) => {
        if (arr.title == item.title) {
          firstIndex = index;
        }
      });
      this.customer_list_type.splice(firstIndex, 1, list);
      this.screen_list_type.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex = index;
        }
      });
      this.screen_list_type.splice(secondIndex, 1, item);
      this.getDataLists(list);
    },
    // 客户列表快速跟进客户内容
    fastFollowUp(row) {
      // console.log(row, "row");
      this.ponent_Follow_data = row; // 赋值客户信息
      this.show_Follow_dialog = true;
    },
    // 关闭快速跟进客户内容模态框
    closeFollow() {
      this.show_Follow_dialog = false;
    },
    // 快速添加跟进成功执行
    addFollowSuccess() {
      this.$message.success("操作成功");
      this.getDataList(); // 刷新页面数据
    },
    // 排序
    sortChangeData(column) {
      // console.log(column.column.label,column,"参数");
      if (column) {
        if (column.column.label === "客户名称") {
          this.params.c_type3 = 1; // (0:新增线索,1:最近活跃,2:多条线索,3:最新跟进,4:客户等级,5:跟进记录)
        } else if (column.column.label === "更新时间") {
          this.params.c_type3 = 2;
        } else if (column.column.label === "客户线索") {
          this.params.c_type3 = 3;
        } else if (column.column.label === "创建时间") {
          this.params.c_type3 = 4;
        }
        // 判断升序
        if (column.order === "ascending") {
          this.params.c_type3_sort = 2;
        } else if (column.order === "descending") {
          // 判断倒序
          this.params.c_type3_sort = 1;
        } else {
          // 默认
          // this.params.sort_type = 0;
          delete this.params.c_type3;
          delete this.params.c_type3_sort;
        }
        this.getDataList();
      }
    },
    sortChange(column) {
      // console.log(column.column.label,column,"参数");
      // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
      if (column) {
        // 判断升序
        if (column.order === "ascending") {
          this.admin_params.sort = 1;
        } else if (column.order === "descending") {
          // 判断倒序
          this.admin_params.sort = 2;
        } else {
          // // 默认
          // // this.params.c_type1 = 0;
          delete this.admin_params.sort;
          // delete this.params.c_type3_sort;
        }
        this.admin_params.page = 1
        this.getAdmin();
      }
    },
    // 赋值客户编号
    copyCusID(id) {
      this.$onCopyValue(id);
    },   
    //右侧快速编辑的立即领取
    Getright(){
      this.$confirm("是否认领该客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.getCrmCustomerPublick({ ids:this.ids+ "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("认领成功");
              let url = `/crm_customer_detail?id=${this.ids}&type=my`;
              this.$goPath(url);
              this.drawer = false
              this.getDataList(); // 刷新页面数据
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    //右侧速编转交
    onClickright(){
    this.multipleright = this.ids
      this.right_transfer_customer = true;
      this.admin_params.user_name = "";
      this.admin_params.page = 1;
    },
    //右侧速编确定提交
    sureright(){
      this.$refs.childReff.onClickForm();
      this.getDataList(); // 刷新页面数据
    },
    //右侧认领的判断
    checkStatusright() {
      // types!=='my'|| !c_detail.follow_user.length && !this.checkStatus(c_detail)
      // ClaimCustomers
      if(this.type!=='my' && this.c_detail.follow_user){
          if (this.seastype == 2) {
          // 手动认领  
          // 潜在用户 我司成交的不可认领
          if (this.c_detail.tracking_identify == 1) {
            this.Claim = "已认领"
            return this.ClaimCustomers =  false
            
          }
          // 掉工转公标记的可以领取 
          if (this.c_detail.public2_status > 0) {
            this.Claim = "未认领"
            return this.ClaimCustomers = true
          }
          // 潜在客户可以领取
          // if (this.type == "qianzai") {
          //   return false
          // }
          // 其他情况 不可领取 
          this.Claim = "已认领"
          return this.ClaimCustomers =  false
        }
        this.Claim = "已认领"
        return this.ClaimCustomers =  false
        }
        this.Claim = "未认领"
        return this.ClaimCustomers = true
    },
    //控制抽屉的出现
    handleClose() {
      this.c_detail = ""
      this.drawer = false
    },
    Quick_Edit(row){
      if(row.deal_user){
          this.transmitstatus = false
        }else{
          this.transmitstatus = true
        }
      this.c_detail = row
      // if(this.website_id==176){
        this.ids = row.id
        this.seastype = row.push_type
        this.drawer = true//显示抽屉框
        this.checkStatusright() 
      // }
    }
  },
  // 当该页面进入时触发
  async activated() {
    // 判断是否要刷新数据
    if (this.$store.state.allowUpdate) {
      this.$store.state.allowUpdate = false;
      //this.getDataList(); // 刷新页面数据
    }

    if(this.customerDetailChangedIds.length && this.tableData.find(e => this.customerDetailChangedIds.includes(e.id))){
      this.customerDetailChangedIds = [];
      this.getDataList();
    }
  },
};
</script>
  
<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 0 24px 24px;
  .input-with-select.short{
    width: 96px;
    button{
      padding: 0 0 0 3px;
      width: 36px
    }
  }
.input-with-select{
  /deep/ .el-input__inner{
    padding: 1px 9px !important;
  }
  /deep/.el-input-group__append, .el-input-group__prepend{
    padding: 0px 13px !important;
  }
}

  .b-tabs {
    // cursor: pointer;

    .b-t-item {
      width: 120px;
      height: 50px;
      text-align: center;
      line-height: 40px;
      color: #8a929f;
      position: relative;
      margin-top: 20px;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;

      &.isactive {
        color: #00a3ff;
        background-color: #fff;

        // &::after {
        //   position: absolute;
        //   content: "";
        //   left: 50%;
        //   transform: translateX(-50%);
        //   height: 3px;
        //   // background: #2d84fb;
        //   width: 100%;
        //   display: block;
        //   margin-top: 4px;
        // }
      }
    }

    .config-customer {
      .el-button {
        padding: 7px 15px;
      }
    }
  }

  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    margin-left: -24px;
    margin-right: -24px;

    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }

  .grid-content {
    background: #fff;
    height: 113px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
    }

    .left {
      font-size: 14px;
      color: #8a929f;
      margin-right: 64px;
    }

    .number {
      display: flex;
      align-items: center;

      .number1 {
        font-size: 24px;
        color: #2e3c4e;
        margin-right: 10px;
      }

      .number2 {
        font-size: 12px;
        color: #fe6c17;
      }
    }
  }

  ::v-deep .el-drawer.kehu {
    width: 54% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }
  }

  .QuickEdit {
    width: 100%;
    height: calc(100vh - 90px);
    margin-top: -10px;
    overflow-y: auto;
    overflow-x: hidden;

    .kehu_footer {
      width: 93%;
      margin: 0 auto;
      justify-content: space-between;
    }
  }

  .demo-drawer__footer {
    width: 48%;
    height: 42px;
    margin-top: 10px;
    background-color: #ffff;
    position: fixed;
    bottom: 0px;
    z-index: 2;

    .drawerfoot {
      // margin-top: 35px;
      justify-content: space-between;

      .crmstatus {
        width: 135px;
        height: 30px;
        border: 1px solid rgb(222, 225, 231);
        text-align: center;
        border-radius: 3px;
        line-height: 29px;
        font-size: 15px;
      }
    }
  }
}

.QuickEditA {
  width: 100%;
  height: calc(100vh - 90px);
  overflow-y: auto;
  overflow-x: hidden;
}

.importdrawer {
  ::v-deep .el-drawer {
    width: 36% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }

    .isfooter {
      width: 35%;
      height: 42px;
      // margin-top: 10px;
      background-color: #ffff;
      position: fixed;
      bottom: 0px;
      z-index: 2;
    }

    .isfoot {
      justify-content: flex-end;
    }
  }

}
.enterdrawer {
  ::v-deep .el-drawer {
    width: 24% !important;

    .el-drawer__header {
      color: #2E3C4E !important;
      margin-bottom: 10px;
    }

    .isfooter {
      width: 23%;
      height: 42px;
      // margin-top: 10px;
      background-color: #ffff;
      position: fixed;
      bottom: 0px;
      z-index: 2;
    }

    .enterfoot {
      justify-content: space-between;
    }
  }
}
.isbottom {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;
  flex-wrap: wrap;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
    white-space: nowrap;
  }

  .label-item {
    font-size: 14px;
    color: #8a929f;
    // width: 70px;
    margin-right: 8px;
    padding: 3px 16px;
    background: #fff;
    cursor: pointer;

    &.isactive {
      border-radius: 4px;
      background: #e8f1ff;
      color: #2d84fb !important;
    }
  }

  .selected-header {
    font-size: 14px;
    color: #8a929f;
    margin-right: 8px;
    padding: 3px 16px;
    cursor: pointer;
    margin-bottom: 5px;
    white-space: nowrap;
  }

  .label_actions {
    border-radius: 4px;
    background: #e8f1ff;
    color: #2d84fb;
  }
  .namedata{
    margin-left: 16px;
    .selectedname{
      margin-right: 19px;
      color: #8a929f;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .alllabeldatastyle{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 4px;
    .labeldatastyle{
      margin-top: 8px;
      margin-right: 10px;
    }
    .labelerr{
      margin-top: 12px;
    }
  } 
  .selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
        //
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
  }
  .custom-cascader{
   ::v-deep .el-input {
    .el-input__inner {
          border: 0px solid #DCDFE6;
          .el-input__suffix {
            color: #ffff !important;
          }
        }
    }
  }
  .custom-cascader{
   ::v-deep .el-input {
      .el-input__suffix {
        display: none !important;
      }
    }
  }
  .head-list {
    margin-right: 10px;
    margin-top: 10px;
  }

  .crm-selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        // border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        padding: 15px 27px !important;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }

    &.short ::v-deep .el-input .el-input__inner {
      padding: 15px !important;
    }
  }
}
.allbtn{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;

  .text {
    font-size: 14px;
  }
}

.labelrow {
  margin-bottom: 20px;

  &.red {
    color: #f85d02;
  }
}

.t-t-b-right {
  // width: 50%;
  display: flex;
  justify-content: flex-end;
  position: relative;

  &.abs {
    position: absolute;
    width: 689px;
    right: 40px;
    overflow: hidden;
  }

  .abs {
    .search_loudong {
      height: 30px;
      margin-top: 2px;
      width: 65px;
    }
  }
}

.crmuserbox {
  align-items: center;

  span {
    display: block;
  }

  img {
    border-radius: 50%;
  }

  .label {
    width: fit-content;
    padding: 0 6px;
    background: #2d84fb;
    border-radius: 4px;
    color: #fff;
  }
}
</style>
<style lang="scss">
.cus-box {
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;

  .cus-box-header {
    .cus-header-user {
      align-items: center;

      .cus-userName {
        color: #2d84fb;
        text-align: left;
        margin: 5px 5px 5px 0;

        &.cus-userName_nowrap {
          max-width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .cus-userName1 {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
      }

      .cus-sex {
        width: 16px;
        height: 16px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 12px;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      color: #67c23a;
      border: 1px solid #67c23a;
    }

    .cus-icon-purchase {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }
  }

  .cus-img {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 14px;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      display: block;
      color: #67c23a;
      border: 1px solid #67c23a;
      cursor: pointer;
    }

    .cus-icon-purchase {
      display: block;
      color: #98a6c3;
      border: 1px solid #98a6c3;
      cursor: pointer;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }

    .cus-douyinIcon {
      width: 16px;
      height: 16px;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .fast-Edit-cus {
    display: none;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.search_loudong {
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.clueRemark {
  display: flex;

  .el-textarea {
    width: 360px;

    .el-textarea__inner {
      min-height: 40px !important;
      height: 40px;
    }
  }
}

.table-btns {
  .search-Belong {
    .el-button {
      padding: 4px 7px;
      margin-top: 3px;
      border-radius: 2px;
    }
  }

  .last_call_follow {
    align-items: center;
    justify-content: center;

    &.w180 {
      width: 180px;
    }

    .cus-clue-text_c {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 3px;
        right: -5px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #9edf2e;
      }
    }

    .cus-clue-text_u {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 3px;
        right: -5px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #f56c6c;
      }
    }
  }

  .fast-look-tel {
    display: none;
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;
    opacity: 0.8;
    line-height: 1;

    i {
      color: #409eff;
    }

    // img {
    //   width: 100%;
    //   height: 100%;
    // }
  }
}

.labelname {
  margin-bottom: 10px;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;
  }

  .checked {
    background: rgba(45, 132, 251, 0.15);
    border: 1px solid rgba(45, 132, 251, 1);
    color: #2d84fb;
  }
}

.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.inp_no_border {
  width: 155px;

  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
  // color: #EBF0F7;
}

.dialog_customer_label {
  height: 560px;
  overflow-y: auto;
}

.search-member-box {
  .el-input {
    .el-input__inner {
      // width: 155px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
}

.cus-clue-label {
  align-items: center;
  flex-wrap: wrap;
  line-height: 1;

  .cus-icon-label {
    color: #16a1bc;
    border: 1px solid #16a1bc;
    // color: #409eff;
    // border: 1px solid #b3d8ff;
    font-size: 14px;
    padding: 5px 11px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
  }
}

.clueLabel {
  width: 20px;
  height: 20px;
  display: none;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.label_list {
  flex-wrap: wrap;

  .label_item {
    margin-bottom: 5px;
  }
}

.el-table__body {
  .el-table__row {
    .is-center:nth-child(4):hover .clueLabel {
      display: block;
    }

    .is-center:nth-child(2):hover .fast-Edit-cus {
      display: block;
    }

    .is-center:nth-child(3):hover .fast-look-tel {
      display: block;
    }

    .is-center:nth-child(6):hover .followLabel {
      display: block;
    }
  }
}

.screen-type {
  display: flex;
  flex-direction: column;
  margin: -12px;
  padding: 6px 0px;

  .screen-type-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .screen-type-content:hover {
    background-color: #f5f7fa;
  }
}

.followLabel {
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.public-status {
  // display: inline-block;
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  padding: 0 4px;
  border-radius: 4px;
}
.comment{
		text-align: left;
		display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical; 
	}
  .comment-popover{
	line-height: 1.5;
}
.follow-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &.w180 {
    width: 180px;
  }
}

.over_text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.cus-clue-text {
  width: 100%;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;

  &.text_left {
    text-align: left;
  }

  &.w180 {
    width: 180px;
  }
}

.over_text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.text_left {
  text-align: left;
}

.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}

.table-top-box-abs {
  // position: absolute;
  /* padding-top: 30px; */
  padding: 10px 0px;
  top: 0;
  left: 0;
  right: 0;
  height: 65px;

  // transition: 0.3s;
  &.fixed {
    position: fixed;
    top: 60px;
    left: 254px;
    right: 40px;
    padding: 10px 24px;
    background: #fff;
    z-index: 100;

    .abs {
      right: 25px;
    }
  }
}

.content-box-crm {
  &.content-box-crm-pr {
    position: relative;
    padding: 24px 0;
    padding-top: 50px;
  }
}
</style>
  