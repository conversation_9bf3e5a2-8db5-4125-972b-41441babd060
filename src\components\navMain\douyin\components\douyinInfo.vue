<template>
  <div class="detail">
    <el-table
      v-loading="is_table_loading"
      :data="tableData"
      border
      ref="detrail"
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <el-table-column label="日期" prop="date"> </el-table-column>
      <el-table-column label="播放新增" prop="new_play"> </el-table-column>
      <el-table-column label="点赞新增" prop="new_like"> </el-table-column>
      <el-table-column label="评论新增" prop="new_comment"> </el-table-column>
      <el-table-column label="分享新增" prop="new_share"> </el-table-column>
      <el-table-column label="访问新增" prop="profile_uv"> </el-table-column>
      <el-table-column label="作品新增" prop="new_issue"> </el-table-column>
      <el-table-column label="粉丝新增" prop="new_fans"> </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev, pager, next"
      :total="params.total"
      :page-size="params.per_page"
      :current-page="params.page"
      @current-change="onPageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {

  props: {
    id: {
      type: [String, Number],
      default: ""
    }
  },
  watch: {
    "id": {
      handler(val) {
        console.log(val, 111);
        if (val) {
          this.params.open_id = val
          this.getDetail()
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        open_id: '',
      },

    }
  },
  methods: {
    getDetail() {
      this.is_table_loading = true
      this.$http.getDouyinKanbanDetail(this.params).then(res => {
        console.log(res);
        this.is_table_loading = false
        if (res.status == 200) {
          this.tableData = res.data.data
          // this.$set(this.tableData, res.data.data)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
  }
}
</script>

<style lang ="scss" scoped>
.detail {
  max-height: 70vh;
  overflow-y: auto;
}
</style>