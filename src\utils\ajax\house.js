// import { UserCenter } from "../http";
// const ajax = new UserCenter
// const http = ajax.http
// export var user = new User(service)

export default class House {
  constructor(options) {
    this.http = options.$http
  }
  // 添加
  add(params) {
    return this.http.post(`/admin/house/savePrivateHouse`, params)
  }
  // 获取房源详情   展示 privateHousesDetail/{id}
  getDetail(id) {
    return this.http.get(`/admin/house/privateHousesDetail/${id}`)
  }
  // 获取房源详情   编辑用 privateHousesDetailByEdit/{id}
  getEditDetail(id) {
    return this.http.get(`/admin/house/privateHousesDetailByEdit/${id}`)
  }
  // 编辑
  edit(params) {
    return this.http.post(`/admin/house/editPrivateHouse`, params)
  }
  // 获取房源图片
  getHousePicture(id, params) {
    return this.http.get(`/admin/house/housePhotoNew/${id}`, { params })
  }
  // new 获取房源图片
  newGetHousePicture(id, params) {
    return this.http.get(`/admin/house/housePhotoSk/${id}`, { params });
  }
  // 编辑图片
  editPic(id, params) {
    return this.http.post(`/admin/house/privateHousesUpPic/${id}`, params)
  }
  // 列表筛选条件
  getFilterList() {
    return this.http.get(`/admin/house/houseConditionByLm`)
  }
  // 列表
  getList(params) {
    return this.http.get(`/admin/house/privateHouses`, { params })
  }
  //添加编辑房源获取需要的信息
  options(params) {
    return this.http.get(`/admin/house/houseSelectFieldsByLm`, { params })
  }

  // 搜索小区
  searchCommunity(keyword) {
    return this.http.get(`/admin/house/searchCommunity?title=${keyword}`)
  }
  // 楼栋
  getLoudong(params) {
    return this.http.get(`/admin/house/searchBuilding`, { params })
  }
  // 单元
  getUnit(params) {
    return this.http.get(`/admin/house/searchUnit`, { params })
  }
  // 房号
  getFanghao(params) {
    return this.http.get(`/admin/house/searchRoom`, { params })
  }
  // 查重
  checkRepeat(params) {
    return this.http.get(`/admin/house/houseRepeat`, { params })
  }
  // 获取房源详情 跟进类型   
  getStatusList() {
    return this.http.get(`/admin/house/getFollowType`)
  }
  // 添加跟进
  addFollow(params) {
    return this.http.post(`/admin/house/addPrivateHouseFollow`, params)
  }
  // 获取跟进列表
  getFollowList(id, params) {
    return this.http.get(`/admin/house/privateHouseFollows/${id}`, params)
  }
  // 房源-跟进记录-点击置顶
  changeTopButton(id, opt) {
    return this.http.get(`/admin/house/privateHouseFollows/${id}/${opt}`)
  }
  // 跟进记录-记录点赞
  addFollowPraise(data) {
    return this.http.post("/admin/house/follow/addStar", data)
  }
  // 跟进记录-删除单张跟进图片
  deleteFollowPraise(data) {
    return this.http.post("/admin/house/privateHouseFollows/delPic", data)
  }
  // 添加钥匙委托vip等
  setWeituo(params) {
    return this.http.post(`/admin/house/privateHousesExtends`, params)
  }
  // 查看业主电话  houseTelLog
  seeOwnerTel(params) {
    return this.http.post(`/admin/house/houseTelLog`, params)
  }
  // 查看业主电话  houseTelLog
  seeOwnerTelNew(params) {
    return this.http.post(`/admin/house/houseTelLog2`, params)
  }
  // 查看房号  checkRoomNumber/{id}
  seeRoomNumber(id) {
    return this.http.get(`/admin/house/checkRoomNumber/${id}`)
  }
  // 房源日志
  getLog(params) {
    return this.http.get(`/admin/house/houseLogList`, { params })
  }
  getShowRegisterList(id, params) {
    return this.http.get(`/admin/house/take/${id}`, { params })
  }
  // 房源日志检索条件
  getLogFilterList() {
    return this.http.get(`/admin/house/houseLogSelectFields`)
  }

  // 添加房源智能匹配
  houseAddMatching(keywords) {
    return this.http.get(`/admin/house/intelligentMatching?keywords=${keywords}`)
  }

  // 获取房源标签
  getLabels(id) {
    return this.http.get(`/admin/house/houseLabelById/${id}`)
  }
  // 录入房源获取房源标签
  getHouseLabels() {
    return this.http.get("/admin/house/houseLabel");
  }
  // 获取房源提醒时间
  getRemindDate(id) {
    return this.http.get(`/admin/house/privateHouseMsgTimeAndSms/${id}`)
  }
  // 获取钥匙添加记录
  getKeyList(id) {
    return this.http.get(`/admin/house/keyList/${id}`)
  }
  // 获取钥匙添加记录
  getVIPWeituoList(id) {
    return this.http.get(`/admin/house/vipList/${id}`)
  }
  setRemind(params) {
    return this.http.post(`/admin/house/addPrivateHouseMsg`, params)
  }
  // 获取业主电话所属类型
  getTelTypeList() {
    return this.http.get(`/admin/house/telType`)
  }
  getUpToken(params) {
    return this.http.post(`/admin/house/uploadToken`, params)
  }
  getUploadImg(token) {
    return this.http.post(`/common/house/fileList`, { up_token: token })
  }
  clearImg(token) {
    return this.http.post(`/common/house/delFileLog`, { up_token: token })
  }
  // 获取是否可以复制文案进行分享 【权限配置 单日分享复制上限 默认20次 ，0不限制】
  shareHouse(id) {
    return this.http.get(`/admin/house/shareHouse/${id}`)
  }
  getLouceng(params) {
    return this.http.get(`/admin/house/searchFloor`, { params })
  }
  getFanghaoList(params) {
    return this.http.get(`/admin/house/searchRoomByFloor`, { params })
  }
  sendPhoneMsg(params) {
    return this.http.get(`/admin/house/captcha`, { params })
  }
  getSiteList(params) {
    return this.http.get(`/admin/house/getPushSite`, { params })
  }
  bindSiteByPhone(id, params) {
    return this.http.post(`/admin/house/agentBindingSite/${id}`, params)
  }
  houseSendGroup(id, params) {
    return this.http.post(`/admin/house/releaseToMassSite/${id}`, params)
  }
  delKeyLog(id) {
    return this.http.get(`/admin/house/delKey/${id}`)
  }

  // 获取是否开启单边代理
  getOPenShowSingle() {
    return this.http.get(`/admin/house/unilateralAgent`)
  }
  // 获取人事列表
  getManagerAuthList(params) {
    return this.http.get(`/admin/house/userList`, params)
  }
  //获取房源委托人 等信息

  getDetailManagers(id) {
    return this.http.get(`/admin/house/houseRoleList/${id}`)
  }
  // 设置房源委托人等
  setHouseRoles(params) {
    return this.http.post(`/admin/house/houseRoleEdit`, params)
  }

  // 获取房源有效无效状态/houseTradeStatus/{id}
  getHouseStatus(id) {
    return this.http.get(`/admin/house/houseTradeStatus/${id}`)
  }
  // 获取房源状态选项/houseTradeStatusList
  getHouseStatusList() {
    return this.http.get(`/admin/house/houseTradeStatusList`)
  }

  // 设置房源状态
  setHouseStatus(params) {
    return this.http.post(`/admin/house/houseTradeStatusEdit`, params)
  }
  getCommonSettingRolesForPersoneral() {
    return this.http.get(`/admin/website/get_personnel_auth`)
  }
  getCommonSettingRolesForPersoneralNew(params) {
    return this.http.get(`/admin/website/get_fixed_config`, { params })
  }

  getHouseTel(id) {
    return this.http.get(`/admin/house/houseTel/${id}`)
  }
  editTel(params) {
    return this.http.post(`/admin/house/houseTelEdit`, params)
  }




  // 获取审批类型  id=2是房源  1 是客户
  getAuditTypeList(id) {
    return this.http.get(`/admin/house/approveType/${id}`)
  }


  //根据审批类型获取模板列表
  getModalList(id, bid) {
    return this.http.get(`/admin/house/approveFields/${id}/${bid}`)
  }

  // 获取审批人列表
  getAuditPerson(id) {
    return this.http.get(`/admin/house/approvePerson/${id}`)
  }

  addHouseAudit(params) {
    return this.http.post("/admin/house/addApprove", params)
  }

  //  获取房源审核状态列表
  getAuditStatusList() {
    return this.http.get(`/admin/house/approveStatus`)
  }


  // 审批管理员 获取房源审批列表

  getHouseAuditList(id, params) {
    return this.http.get(`/admin/house/approveList/${id}`, params)
  }


  // 获取审批详情

  getHouseAuditDetail(id) {
    return this.http.get(`/admin/house/approveDetail/${id}`)
  }

  // 更新房源审批状态

  changeAuditStatus(params) {
    return this.http.post(`/admin/house/dealApprove`, params)
  }

  // 获取本部门人员列 添加抄送人员使用
  getRecipientsList() {
    return this.http.get(`/admin/house/userListDepartment`)
  }


  // 发送留言
  sendAuditMessage(params) {
    return this.http.post(`/admin/house/approveMsg`, params)
  }
  // 留言列表
  getAuditMessage(id, params) {
    return this.http.get(`/admin/house/approveMsgList/${id}`, params)
  }
  // /api/admin/house/cancelApprove/{id}  撤销
  chexiaoAudit(id) {
    return this.http.get(`/admin/house/cancelApprove/${id}`)
  }

  // /api/admin/house/housePrivacyNum  获取隐私条数
  getPrivaceNum() {
    return this.http.get(`/admin/house/housePrivacyNum`)
  }

  // 根据房源id 获取相机列表
  getVrPhotoList(id) {
    return this.http.get(`/admin/house/vr/cameraListByDep/${id}`)
  }
  setVrOrder(params) {
    return this.http.post(`/admin/house/vr/addOrder`, params)
  }
  // 获取站点vr 参数配置
  getVrSetting(params = {}) {
    return this.http.get(`/admin/house/vr/camera/getSetting`, { params })
  }
  // 提交站点vr 参数配置
  setVrSetting(params = {}) {
    return this.http.post(`/admin/house/vr/camera/setting`, params)
  }


  // 撤销委托
  deleteWeituo(params) {
    return this.http.post(`/admin/house/privateHousesExtends/cancel`, params)
  }

  //  获取抖音需要的素材

  getSucai(id) {
    return this.http.get(`/admin/house/houseVideoAndPhoto/${id}`)
  }



  // 获取申请小区的列表
  getAuditCommunityList(params) {
    return this.http.get(`/admin/house/apply/community/list`, { params })
  }
  // 添加申请小区
  addAuditCommunity(params) {
    return this.http.post(`/admin/house/apply/community/add`, params)
  }
  // 编辑申请小区
  editAuditCommunity(params) {
    return this.http.post(`/admin/house/apply/community/update`, params)
  }
  // 获取申请小区详情
  getAuditCommunityDetail(id) {
    return this.http.get(`/admin/house/apply/community/detail/${id}`)
  }
  // 删除申请小区
  deleteAuditCommunity(id) {
    return this.http.get(`/admin/house/apply/community/delete/${id}`)
  }

  //根据会员所在区域获取商圈列表 /api/admin/house/getAreaRegion
  getAreaRegion() {
    return this.http.get(`/admin/house/getAreaRegion`)
  }
  // 维护度详情
  getMaintainDetails(id) {
    return this.http.get(`/admin/house/score/detail/${id}`)
  }
  // 获取跟进记录-@同事信息
  getFollowColleagueDetails(id) {
    return this.http.get(`/admin/house/follow/userInfo/${id}`)
  }
  // 获取跟进记录录音
  getFollowRecording(id) {
    return this.http.get(`/admin/house/follow/callRecord/${id}`)
  }
  // 房源信息-获取电话记录
  // getPhoneRecord(id, params) {
  //   return this.http.get(`/admin/house/take/tel/${id}`, {params})
  // }
  // 新房源信息-获取电话记录
  getNewPhoneRecord(id, params) {
    return this.http.get(`/admin/house/take/tel_new/${id}`, { params })
  }
  // 房源信息-获取带看记录
  getTakeLookRecord(id, params) {
    return this.http.get(`/admin/house/take/follow/${id}`, { params })
  }
  // 获取房源筛选Label
  getHouseSeachLabel() {
    return this.http.get("/admin/house/labelConditionByLm")
  }
  exportMakeData(id) {
    return this.http.get(`/admin/house/take/export/${id}`)
  }
  // 获取上传的客户表格任务详情(导入)
  getTaskList(params) {
  return this.http.get(`/admin/crm/export_log/search/`, { params })
  }
  // 获取上传的客户表格任务详情(导入)
  newgetTaskList(id,params) {
    return this.http.get(`/admin/crm/excel/import/info_search/${id}`, { params })
  }
  // 获取上传的客户表格任务详情(导入)
  newgetAllTaskList(params) {
    return this.http.get(`/admin/crm/excel/import/task_search`, { params })
  } 
  // 获取全部导入任务列表
  getAllTaskList(params) {
    return this.http.get(`/admin/crm/export_task/search`, { params })
  }
  // 获取全部导出任务列表
  getallexportdata(params){
    return this.http.get(`/admin/crm/excel/export/task_search`, { params })
  }
  // 导出的任务列表详情
  getxeportlistdetails(id,params){
    return this.http.get(`/admin/crm/excel/export/info_search/${id}`,{params})
  }
}

