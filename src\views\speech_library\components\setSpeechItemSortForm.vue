<template>
<el-dialog :visible.sync="show" title="设置排序" width="500px">   

    <el-form ref="form" :model="params" label-width="120px">
      
        <el-form-item label="排序值">
            <el-input type="number" v-model="params.sort" class="sort-input"></el-input>
            <div class="tip">排序值越大越靠前</div>
        </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
        <el-button @click="cancle">取 消</el-button>
        <el-button type="primary" @click="confirm" :loading="submiting">确 定</el-button>
    </span>
</el-dialog>
</template>

<script>
export default {
    data(){
        return {
            show: false,
            submiting: false,
            params: {
                id: '',
                sort: '',
            },
            successFn: null,
        }
    },
    methods: {
        open(params){
            this.params.id = params.id;
            this.params.sort = params.sort || '';
            this.show = true;
            return this;
        },
        onSuccess(fn){
            this.successFn = fn;
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(this.params.sort === ''){
                this.$message.warning('请输入排序值');
                return;
            }
            this.submiting = true;
            try{
                const res = await this.$http.setSpeechItemSort(this.params);    
                if(res.status == 200){
                    this.$message.success(res?.data?.msg || '设置成功');
                    this.show = false;
                    this.successFn && this.successFn();
                }
            }catch(e){
                console.error(e);
            }
            this.submiting = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.sort-input{
    width: 280px
}
.tip{
    color: #f40;
    line-height: 1;
    padding-top: 10px;
}
::v-deep{
    input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
}
</style>