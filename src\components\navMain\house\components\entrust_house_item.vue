<template>
  <div class="entrust-house-item">
    <div class="entrust-house-img">
      <el-carousel height="150px" indicator-position="outside">
        <el-carousel-item v-for="(uItem, uIndex ) in data.pic_list" :key="uIndex">
          <img :src="uItem.url" />
        </el-carousel-item>
      </el-carousel>
    </div>
    <div class="entrust-house-info">
      <div class="info-l">
        <div class="title">{{ data.title_promotion ? data.title_promotion : ' -- ' }}</div>
        <div class="title-2">{{ data.description ? data.description : ' -- ' }}</div>
        <div class="info-basic">
          <div class="type">类型：{{ type }}</div>
          <div class="wuye" v-if='wuye'>物业类型： {{ wuye }}</div>
          <div class="wuye" v-if="data.usage_type_id != 6 && data.usage_type_id != 8">小区： {{
            data.community_name ? data.community_name : '--' }}</div>
          <div class="wuye" v-else>区域： {{ data.area_name ? data.area_name : ' -- ' }}</div>
        </div>
        <div class="info-basic">
          <div class="wuye">面积： {{ data.mianji ? data.mianji : ' -- ' }}m²</div>
          <div class="wuye" v-if="chaoxiang">朝向： {{ chaoxiang }}</div>
          <div class="wuye" v-if="zhuangxiu">装修： {{ zhuangxiu }}</div>
          <div class="wuye" v-if="data.usage_type_id != 6 && data.usage_type_id != 8">户型： {{ data.shi ? data.shi : ' -- '
          }}室{{
  data.ting ? data.ting : ' -- '
}}厅{{ data.wei ? data.wei : ' -- ' }}卫
          </div>
          <div class="wuye">楼层： {{ data.floor ? data.floor : ' -- ' }} ｜ 所在楼层：{{ data.sz_floor ? data.sz_floor : ' -- ' }}
          </div>
        </div>
        <div class="info-basic">
          <div class="type">联系人：{{ data.owner ? data.owner : ' -- ' }}</div>
          <div class="wuye">联系电话： {{ data.owner_tel }}</div>
        </div>
        <div class="info-basic">
          <div class="wuye">委托时间： {{ data.add_time }}</div>
        </div>
        <div class="info-basic">
          <div class="btn" v-if="data.status == 0">
            <el-popover placement="top" width="160" v-model="visible">
              <p>确定同意委托并录入该房源吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="visible = false">取消</el-button>
                <el-button type="primary" size="mini" @click="consent">确定</el-button>
              </div>
              <el-button slot="reference" type="success">通过委托</el-button>
            </el-popover>
          </div>
          <div class="btn" v-else>
            <el-tag :type="data.status == 1 ? 'success' : 'danger'">{{ data.status == 1 ? '已通过委托' : '已驳回委托' }}</el-tag>
          </div>
        </div>
      </div>
      <div class="info-r">
        <div class="total-price" v-if="data.trade_type == 1">
          <div>{{ data.sale_price * data.mianji }}</div>
          <i>万</i>
        </div>
        <div class="price">
          <div>{{ data.trade_type == 1 ? data.sale_price : data.rent_price }}</div>
          <i>/m²</i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      visible: false,
    }
  },
  computed: {
    wuye() {
      let arr = [
        { name: "住宅", value: 1 },
        { name: "别墅", value: 2 },
        { name: "商住", value: 3 },
        { name: "商铺", value: 4 },
        { name: "写字楼", value: 5 },
        { name: "厂房", value: 6 },
        { name: "车库", value: 7 },
        { name: "土地", value: 8 },
        { name: "其它", value: 99 },
      ]
      let item = arr.find((val) => val.value == this.data.usage_type_id)
      return item.name
    },
    type() {
      let arr = [
        { name: '出租', value: 2 },
        { name: '出售', value: 1 }
      ]
      let item = arr.find((val) => val.value == this.data.trade_type)
      return item.name
    },
    chaoxiang() {
      let arr = [
        { name: "东", value: 1 },
        { name: "南", value: 2 },
        { name: "西", value: 3 },
        { name: "北", value: 4 },
        { name: "东南", value: 5 },
        { name: "东北", value: 6 },
        { name: "西南", value: 7 },
        { name: "西北", value: 8 },
        { name: "南北", value: 9 },
        { name: "东西", value: 10 }
      ]
      let item = arr.find((val) => val.value == this.data.chaoxiang)
      return item.name
    },
    zhuangxiu() {
      let arr = [
        { name: "毛坯", value: 1 },
        { name: "普通装修", value: 2 },
        { name: "精装修", value: 4 },
        { name: "豪华装修", value: 6 }
      ]
      let item = arr.find((val) => val.value == this.data.zhuangxiu)
      return item.name
    }

  },
  methods: {
    async consent() {
      const res = await this.$http.confirmEntrustAPI(this.data.id)
      if (res.status == 200) {
        // this.$parent.entrustList = []
        this.$parent.getList()
        this.$message({
          message: '已通过委托申请',
          type: 'success'
        });
      }
      this.visible = false
    },
    async reject() {
      const res = await this.$http.cancelEntrustAPI()
      if (res.status == 200) {
        this.$parent.getList()
        this.$message({
          message: '已驳回委托申请',
          type: 'warning'
        });
      }
    },

  },
}
</script>
<style lang="scss">
.entrust-house-item {
  display: flex;
  flex-direction: row;
  padding: 30px 20px;
  border-bottom: 1px solid #f3f3f3;
  background-color: #fff;

  .entrust-house-img {
    display: inline-block;
    width: 150px;
    height: 150px;
    border-radius: 5px;
    overflow: hidden;
    z-index: 0;


    img {
      width: 100%;
      height: 100%;
    }
  }

  .entrust-house-info {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-left: 20px;

    .info-l {
      .title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: 5px;
      }

      .title-2 {
        font-size: 12px;
        color: #a1a1a1;
        line-height: 1.3;
        margin-bottom: 10px;
      }

      .info-basic {
        display: flex;
        color: #88939e;
        font-size: 14px;

        div {
          margin-right: 20px;
        }

        .btn {
          margin-top: 20px;

          .el-button {
            padding: 5px 10px;
            font-size: 14px;
          }
        }
      }
    }

    .info-r {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-right: 20px;
      color: #dc352f;
      font-size: 20px;
      font-weight: 500;

      div {
        display: flex;
        align-items: center;

        i {
          margin-left: 5px;
          font-size: 12px;
        }
      }

      &>div:nth-child(2) {
        color: #88939e;
        font-size: 14px;
      }
    }
  }
}
</style>