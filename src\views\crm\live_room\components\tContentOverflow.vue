<template>
    <div class="container" ref="container">
        <div :class="['content', { expanded: isExpand }]" ref="content">
        <p ref="text">
          <slot></slot>
        </p>
      </div>
        <el-link :underline="false" type="primary" @click="toggleExpand" v-if="isOvered">
          <span v-if="isExpand">
            收起 <i class="el-icon-arrow-up"></i>
          </span>
          <span v-else>
            展开<i class="el-icon-arrow-down"></i>
          </span>
        </el-link>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      rows: { type: Number, default: 5 },
      status:{
        type:Number,
        default:0
      }
    },
    data() {
      return {
        isOvered: false,
        isExpand: false
      }
    },
    mounted() {
      this.checkContentOverflow();
    },
    methods: {
      toggleExpand() {
        this.isExpand = !this.isExpand;
      },
      checkContentOverflow() {
        // console.log(this.status);
        // 42抖音私信 45视频号私信 48 小店客服私信 
        if (this.status==42 || this.status==45 || this.status==48) {
          return
        }else{
          this.$nextTick(() => {
          const container = this.$refs.container;
          const text = this.$refs.text;
          if (text.scrollHeight > container.clientHeight) {
            this.isOvered = true;
          }
        });
        }
    
      }
    }
  }
  </script>
  
  <style scoped>
  .container {
    /* max-height: var(--max-height, 100px); Adjust the max-height as needed */
    overflow: hidden;
  }
  
  .content {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: var(--rows, 5);
  }
  
  .content.expanded {
    overflow: visible;
    display: block;
  }
  
  /* .toggle-button {
    color: #488AF6;
    font-size: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
  } */
  
  .icon {
    margin-left: 8px;
  }
  </style>