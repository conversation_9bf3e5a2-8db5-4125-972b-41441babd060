<template>
    <div>
        <el-link type="primary" :underline="false"  @click="openReportCustomerForm">报备客户</el-link>
        <reportwindow ref="reportwindow" @success="handlerSuccess"/>
    </div>
</template>

<script>
import reportwindow from '@/views/crm/share_follower/reportwindow.vue'
export default {
    props:{
        customer: {
            type: Object,
            default: () => ({})
        } ,
        mobiles: {
            type: Array,
            default: () => []
        } ,
        lookfollow_id:{
            type:Number,
            default:0
        }
    },
    components:{
        reportwindow
    },
    data(){
        return {
        
        }
    },
    methods:{
        openReportCustomerForm(){
            this.$refs.reportwindow.open({
                id: this.customer.id,
                cname: this.customer.cname,
                mobiles: [...this.mobiles],
                lookfollow_id:this.lookfollow_id
            })
        },
        handlerSuccess(){
            this.$emit('success')
        }
    },
};
</script>
<style lang="scss" scoped>
</style>