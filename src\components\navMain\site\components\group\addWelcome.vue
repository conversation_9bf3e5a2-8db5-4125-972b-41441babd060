<template>
  <div>
    <!-- <div class="tips">
      <div>
        1.在二维码处设置的欢迎语会被优先推送，如果成员在二维码处设置了欢迎语，在此设置的欢迎语
        不会生效
      </div>

      <div>
        2.欢迎语将在客户加为好友后20秒内发下，因网络延可能造成发送不成功
      </div>
    </div> -->
    <el-form label-width="100px">
      <el-form-item label="群欢迎语">
        <div class="form-item-block">
          <el-input v-model="welcome_mes.text.desc" style="margin-right: 12px" placeholder="请输入群欢迎语"
            type="textarea"></el-input>
          <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
        </div>
      </el-form-item>
      <el-form-item label="是否通知">
        <div class="form-item-block align-center flex-wrap">
          <el-switch v-model="notify" active-color="#2D84FB"> </el-switch>
        </div>
      </el-form-item>

      <el-form-item label="附件">
        <el-radio-group v-model="params_type_arr" size="mini" @change="radioChange">
          <el-radio :label="2" border>图片</el-radio>
          <el-radio :label="5" border :class="{ active: params_type == 5 }">视频</el-radio>
          <el-radio :label="6" border :class="{ active: params_type == 6 }">文件</el-radio>
          <el-radio :label="3" border :class="{ active: params_type == 3 }">链接</el-radio>
          <el-radio :label="4" :class="{ active: params_type == 4 }" border>小程序</el-radio>
        </el-radio-group>
        <div class="type_tips tips">
          <template v-if="params_type == 2 || params_type == 3 || params_type == 4">
            图片仅支持png,jpg格式 最大10M</template>
          <template v-if="params_type == 5">
            视频仅支持mp4格式 最大10M</template>
          <template v-if="params_type == 6"> 文件最大20M</template>
        </div>
      </el-form-item>
      <template v-if="params_type == 2">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" list-type="picture-card"
              :show-file-list="false" accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf">
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia">素材库上传</el-button>
            <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
          </div>
        </el-form-item>
        <el-form-item label="图片" v-if="welcome_mes.image.pic_url">
          <div class="form-item-block">
            <img :src="welcome_mes.image.pic_url" class="avatar" />
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 3">
        <el-form-item label="类型">
          <el-radio-group v-model="link_type" size="mini" @change="changLinkType">
            <el-radio :label="1" border :class="{ active: link_type == 1 }">默认</el-radio>
            <el-radio :label="2" border :class="{ active: link_type == 2 }">自定义</el-radio>
          </el-radio-group>
          <template v-if="link_type == 1">
            <div class="img_list flex-row" @scroll="handleScroll">
              <el-table :data="sucaiList" border>
                <el-table-column label="ID" width="100" v-slot="{ row }">
                  <el-radio v-model="welcome_mes.link.extra_id" :label="row.id" border @input="selectSucai"></el-radio>
                </el-table-column>

                <el-table-column label="链接标题" prop="title"></el-table-column>
                <el-table-column label="链接路径" prop="url"></el-table-column>
                <el-table-column label="链接描述" prop="desc"></el-table-column>
                <el-table-column label="封面图" v-slot="{ row }">
                  <div style="width: 150px; height: 150px">
                    <img style="width: 100%; height: 100%; object-fit: cover" :src="row.pic_url" alt="" />
                  </div>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-form-item>

        <template v-if="link_type == 2">
          <el-form-item label="跳转路径">
            <div class="form-item-block">
              <el-input v-model="welcome_mes.link.url" style="width: 240px; margin-right: 12px"
                placeholder="请输入跳转路径"></el-input>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="链接标题">
            <div class="form-item-block">
              <el-input v-model="welcome_mes.link.title" style="width: 240px; margin-right: 12px"
                placeholder="请输入链接标题"></el-input>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="链接描述">
            <div class="form-item-block">
              <el-input v-model="welcome_mes.link.desc" style="width: 240px; margin-right: 12px"
                placeholder="请输入链接地址"></el-input>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="">
            <div class="form-item-block img_url flex-row">
              <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" list-type="picture-card"
                :show-file-list="false" accept=".bmp,.jpg,.jpeg,.png,.gif,.pdf">
                <el-button plain type="primary">本地上传</el-button>
              </el-upload>

              <el-button plain type="primary" @click="showSelectDia">素材库上传</el-button>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="封面">
            <div class="form-item-block flex-row">
              <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" list-type="picture-card"
                :show-file-list="false">
                <img v-if="welcome_mes.link.pic_url" :src="welcome_mes.link.pic_url" class="avatar" />
                <i v-else class="el-icon-plus"></i>
              </el-upload>
            </div>
          </el-form-item>
        </template>
      </template>
      <template v-if="params_type == 4">
        <el-form-item label="类型">
          <el-radio-group v-model="mini_type" size="mini" @change="changMiniType">
            <el-radio :label="1" border :class="{ active: mini_type == 1 }">默认</el-radio>
            <el-radio :label="2" border :class="{ active: mini_type == 2 }">自定义</el-radio>
          </el-radio-group>
          <template v-if="mini_type == 1">
            <div class="img_list flex-row" @scroll="handleScroll">
              <el-table :data="miniList" border>
                <el-table-column label="ID" width="100" v-slot="{ row }">
                  <el-radio v-model="welcome_mes.miniprogram.extra_id" :label="row.id" border
                    @input="selectSucai"></el-radio>
                </el-table-column>

                <el-table-column label="小程序标题" prop="title"></el-table-column>
                <el-table-column label="小程序路径" prop="url"></el-table-column>
                <el-table-column label="小程序APPID" prop="appid"></el-table-column>
                <el-table-column label="封面图" v-slot="{ row }">
                  <div style="width: 150px; height: 150px">
                    <img style="width: 100%; height: 100%; object-fit: cover" :src="row.pic_url" alt="" />
                  </div>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-form-item>
        <template v-if="mini_type == 2">
          <el-form-item label="小程序标题">
            <div class="form-item-block">
              <el-input v-model="welcome_mes.miniprogram.title" style="width: 240px; margin-right: 12px"
                placeholder="请输入小程序标题"></el-input>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="小程序appid">
            <div class="form-item-block">
              <el-input v-model="welcome_mes.miniprogram.appid" style="width: 240px; margin-right: 12px"
                placeholder="请输入小程序id"></el-input>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="跳转路径">
            <div class="form-item-block">
              <el-input v-model="welcome_mes.miniprogram.url" style="width: 240px; margin-right: 12px"
                placeholder="请输入跳转路径"></el-input>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="封面">
            <div class="form-item-block img_url flex-row">
              <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" list-type="picture-card"
                :show-file-list="false" accept=".jpg,.png">
                <el-button plain type="primary">本地上传</el-button>
              </el-upload>

              <el-button plain type="primary" @click="showSelectDia">素材库上传</el-button>
              <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
            </div>
          </el-form-item>
          <el-form-item label="封面" v-if="miniCover">
            <div class="form-item-block">
              <img :src="miniCover" class="avatar" />
            </div>
          </el-form-item>
        </template>
      </template>
      <template v-if="params_type == 5">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload :headers="myHeader" :action="website_img" :on-success="handleSuccess" list-type="picture-card"
              :show-file-list="false" accept=".mp4,.AVI,.MPEG,.MOV,.WMV">
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>

            <el-button plain type="primary" @click="showSelectDia">素材库上传</el-button>
            <!-- <p class="tip">例如: 1, A, 甲, 一 等</p> -->
          </div>
        </el-form-item>
        <el-form-item label="视频" v-if="videoname">
          <div class="form-item-block">
            <video :src="videoname" class="avatar" />
          </div>
        </el-form-item>
      </template>
      <template v-if="params_type == 6">
        <el-form-item label="">
          <div class="form-item-block img_url flex-row">
            <el-upload class="upload-demo" :headers="myHeader" :action="website_img" :on-success="handleSuccess"
              :show-file-list="false">
              <el-button plain type="primary">本地上传</el-button>
            </el-upload>
            <el-button plain type="primary" @click="showSelectDia">素材库上传</el-button>
          </div>
          <p class="tip">{{ filename }}</p>
        </el-form-item>
        <!-- <el-form-item label="文件" v-if="filename">
          <div class="form-item-block">
            <img :src="filename" class="avatar" />
          </div>
        </el-form-item> -->
      </template>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
    <el-dialog :visible.sync="show_select_dia" width="600px" title="选择附件" append-to-body>
      <div class="imgLists">
        <div class="img_list flex-row">
          <template v-if="params_type !== 6">
            <template v-if="params_type == 5">
              <div class="img" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
                @click="selectAvatar(item)">
                <video :src="item.url" alt="" />
              </div>
            </template>
            <template v-else>
              <div class="img" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
                @click="selectAvatar(item)">
                <img :src="item.url" alt="" />
              </div>
            </template>
          </template>
          <template v-else>
            <div class="files" v-for="(item, index) in imgList" :key="index" :class="{ active: currentImg == item.url }"
              @click="selectAvatar(item)">
              <div class="file_img">
                <img src="https://img.tfcs.cn/backup/static/admin/crm/static/file_default.png" alt="" />
              </div>
              <div class="file_name">
                {{ item.user_name }}
              </div>
            </div>
          </template>
        </div>
        <div class="footer row align-center">
          <el-button type="text" @click="show_select_dia = false">取消</el-button>
          <el-button type="primary" @click="selectImgOk">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import config from "@/utils/config";
export default {
  components: {

  },
  props: ["navList"],
  data() {
    return {
      form_params: {

      },
      link_params: {
        page: 1,
        per_page: 10,
        type: 3
      },
      notify: true,
      website_img: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`,
      selectedIds: [],
      form_selectedMember: [],
      selectedList: [],
      show_add_member: false,
      isSubmiting: false,
      cover: "",
      imgList: [],
      show_select_dia: false,
      currentImg: "",
      params_type: 2,
      img_params: {
        page: 1,
        per_page: 20,
        type: 1,
      },
      welcome_mes: {

        text: {
          type: 1,
          desc: "",
        },
        image: {
          type: 2,
          media_id: "",
          pic_url: "",
        },
        link: {
          type: 3,
          title: "",
          pic_url: "",
          desc: "",
          url: "",
          extra_id: ''
        },
        miniprogram: {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: '',
          extra_id: ''
        },
        video: {
          type: 5,
          media_id: "",
        },
        file: {
          type: 6,
          media_id: "",
        },
      },
      params_type_arr: [],
      filename: "",
      videoname: "",
      imgname: "",
      miniCover: "",
      link_type: 1,
      mini_type: 1,
      sucaiList: [],
      miniList: [],
      currentLink: {}
    };
  },
  computed: {
    myHeader() {
      return {
        Authorization: "Bearer " + localStorage.getItem("TOKEN"),
      };
    },
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params);
      let _welcome_mes = {}
      _welcome_mes.text = JSON.parse(JSON.stringify(this.welcome_mes.text))
      // let _welcome_mes = Object.assign({}, this.welcome_mes);
      switch (this.params_type) {
        case 2:
          _welcome_mes.image = JSON.parse(JSON.stringify(this.welcome_mes.image))
          break;
        case 3:
          _welcome_mes.link = JSON.parse(JSON.stringify(this.welcome_mes.link))
          break;
        case 4:
          _welcome_mes.miniprogram = JSON.parse(JSON.stringify(this.welcome_mes.miniprogram))
          break;
        case 5:
          _welcome_mes.video = JSON.parse(JSON.stringify(this.welcome_mes.video))
          break;
        case 6:
          _welcome_mes.file = JSON.parse(JSON.stringify(this.welcome_mes.file))
          break;

        default:
          break;
      }
      if (!_welcome_mes.text.desc) {
        this.$message.warning("欢迎语不能为空");
        return;
      }
      if (this.params_type == 2 && (!_welcome_mes.image.media_id)) {
        this.$message.warning('图片不能为空');
        return
      }
      if (this.params_type == 3) {
        let linkArr = {
          title: "链接标题不能为空",
          pic_url: "链接封面不能为空",
          desc: "链接描述不能为空",
          url: "跳转链接不能为空",
        };
        let emptyLink = [];
        for (const key in _welcome_mes.link) {
          if (!_welcome_mes.link[key]) {
            emptyLink.push(key);
          }
        }
        if (emptyLink.length) {
          console.log(12323);
          this.$message.warning(linkArr[emptyLink[0]]);
          return;
        }
      }
      if (this.params_type == 4) {
        let miniArr = {
          title: "小程序标题不能为空",
          media_id: "小程序封面不能为空",
          appid: "小程序appid不能为空",
          page: "小程序链接不能为空",
        };
        let emptyMini = [];
        for (const key in _welcome_mes.miniprogram) {
          if (!_welcome_mes.miniprogram[key]) {
            emptyMini.push(key);
          }
        }
        if (emptyMini.length) {
          this.$message.warning(miniArr[emptyMini[0]]);
          return;
        }
      }
      if (this.params_type == 5) {
        if (!_welcome_mes.video.media_id) {
          this.$message.warning('请上传视频');
          return
        }
      }
      if (this.params_type == 6) {
        if (!_welcome_mes.file.media_id) {
          this.$message.warning('请上传文件');
          return
        }
      }
      if (this.notify) {
        params.notify = 1
      } else {
        params.notify = 0
      }
      params.welcome_msg = JSON.stringify(_welcome_mes);

      if (this.isSubmiting) return;
      this.isSubmiting = true;
      this.$http
        .addCrmGroupWelcomeWord(params)
        .then((res) => {
          console.log(res);
          if (res.status == 200) {
            this.$message.success(res.data.msg || "添加成功");
            setTimeout(() => {
              this.isSubmiting = false;
            }, 200);
            this.$emit("success");
          } else {
            this.isSubmiting = false;
            this.$message.error(res.data.msg || "添加失败");
          }
        })
        .catch(() => {
          this.isSubmiting = false;
        });
    },
    showSelectDia() {
      this.getImgList();
      this.show_select_dia = true;
    },
    // 选中图片
    selectAvatar(e) {
      this.currentImg = e.url;
    },
    // 获取头像列表
    getImgList() {
      if (this.img_params.page == 1) {
        this.imgList = [];
      }
      this.loadMore = "";
      switch (this.params_type) {
        case 2:
        case 3:
        case 4:
          this.img_params.type = 1;
          break;
        case 5:
          this.img_params.type = 3;
          break;
        case 6:
          this.img_params.type = 4;
          break;

        default:
          break;
      }
      this.$http.getCrmServiceAvatarList(this.img_params).then((res) => {
        if (res.status == 200) {
          if (this.img_params.type == 4) {
            res.data.data.map((item) => {
              item.user_name = item.url.substring(
                item.url.lastIndexOf("/") + 1
              );
              return item;
            });
          }
          this.imgList = this.imgList.concat(res.data.data);
          if (res.data.data.length == this.img_params.per_page) {
            this.loadMore = true;
          } else {
            this.loadMore = false;
          }
        }
      }).catch(() => {
        this.loadMore = false
      });
    },
    cancel() {
      this.$emit("cancel");
    },
    async handleSuccess(response) {
      if (response && !response.url) {
        this.$message.error("图片上传失败");
        return;
      }
      let url = response.url;

      let params = { url: response.url };
      let res = { data: {} };
      switch (this.params_type) {
        case 2:
          // 图片
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          this.welcome_mes.image.pic_url = url;
          this.welcome_mes.image.media_id = res.data.media_id;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = url;

          break;
        case 4:
          params.type = 1;
          res = await this.$http
            .getAvatarId(params)
            .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
          // 小程序 miniprogram   media_id
          this.miniCover = url;
          this.welcome_mes.miniprogram.media_id = res.data.media_id;
          break;
        case 5:
          // 视频
          params.type = 3;
          console.log(url);
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败 请检查素材大小");
            return;
          });

          this.videoname = url;
          this.welcome_mes.video.media_id = res.data.media_id;
          break;
        case 6:
          // 文件
          params.type = 4;
          res = await this.$http.getAvatarId(params).catch((err) => {
            console.log(err);
            this.$message.error("获取素材id失败");
          });
          this.filename = url;
          this.welcome_mes.file.media_id = res.data.media_id;
          break;

        default:
          break;
      }
    },
    // 选中图片确认
    selectImgOk() {
      let current = this.imgList.find((item) => item.url == this.currentImg);
      switch (this.params_type) {
        case 2:
          // 图片
          this.welcome_mes.image.media_id = current.media_id;
          this.welcome_mes.image.pic_url = current.url;
          break;
        case 3:
          // 链接
          this.welcome_mes.link.pic_url = current.url;

          break;
        case 4:
          // 小程序 miniprogram   media_id
          this.miniCover = current.url;
          // this.welcome_mes.miniprogram.pic_url = url;
          this.welcome_mes.miniprogram.media_id = current.media_id;
          break;
        case 5:
          // 视频
          this.videoname = current.url;
          this.welcome_mes.video.media_id = current.media_id;
          break;
        case 6:
          // 文件
          this.filename = current.url;
          this.welcome_mes.file.media_id = current.media_id;
          break;

        default:
          break;
      }
      this.show_select_dia = false;
    },
    handleScroll(e) {
      if (
        e.target.offsetHeight + e.target.scrollTop >=
        e.target.scrollHeight - 15 &&
        this.loadMore
      ) {
        this.img_params.page++;
        this.getImgList();
      }
    },
    radioChange(e) {
      console.log(e
      );
      if (e) {
        this.params_type = e;
        var category = config.CATEGORY_IM_IMAGE;
        switch (e) {
          case 2:
          case 3:
          case 4:
            category = config.CATEGORY_IM_IMAGE;
            break;
          case 5:
            category = config.CATEGORY_IM_VIDEO;
            break;
          case 6:
            category = config.CATEGORY_IM_FILE;
            break;
          default:
            break;
        }
        this.website_img = `/api/common/file/upload/admin?category=${category}`;
        if (e == 3) {
          this.link_params.page = 1
          this.link_params.type = 3
          this.getLongLink()
        }
        if (e == 4) {
          this.link_params.page = 1
          this.link_params.type = 4
          this.getLongLink()
        }
      }
    },
    selectSucai(item) {
      console.log(item);

      if (this.params_type == '3') {
        this.sucaiList.map(i => {
          if (i.id == item) {
            this.currentLink = i
          }
          return i
        })
        this.welcome_mes.link = {
          type: 3,
          title: this.currentLink.title,
          pic_url: this.currentLink.pic_url,
          desc: this.currentLink.desc,
          url: this.currentLink.url,
          extra_id: this.currentLink.id
        }
      }
      if (this.params_type == '4') {
        this.miniList.map(i => {
          if (i.id == item) {
            this.currentMini = i
          }
          return i
        })
        this.welcome_mes.miniprogram = {
          type: 4,
          title: this.currentMini.title,
          media_id: this.currentMini.media_id,
          appid: this.currentMini.appid,
          url: this.currentMini.url,
          extra_id: this.currentMini.id
        }
      }
    },
    getLongLink() {

      if (this.params_type == '3') {
        if (this.link_params.page == 1) {
          this.sucaiList = []
        }
        this.link_params.type = 3
      } else {
        if (this.link_params.page == 1) {
          this.miniList = []
        }
        this.link_params.type = 4
      }
      this.$http.foreverMedias(this.link_params).then(res => {
        if (res.status == 200) {
          if (this.params_type == '3') {
            this.sucaiList = this.sucaiList.concat(res.data.data)
            if (res.data.data.length < this.link_params.per_page) {
              this.loadMore = false
            } else {
              this.loadMore = true
            }
          } else if (this.params_type == '4') {
            this.miniList = this.miniList.concat(res.data.data)
            if (res.data.data.length < this.link_params.per_page) {
              this.loadMore = false
            } else {
              this.loadMore = true
            }
          }

        }
      })
    },
    changLinkType(e) {
      if (e == 2) {
        this.img_params.page = 1
        this.getImgList()
        this.welcome_mes.link = {
          type: 3,
          title: '',
          pic_url: '',
          desc: '',
          url: '',
        }
      }
    },
    changMiniType(e) {
      if (e == 2) {
        this.img_params.page = 1
        this.getImgList()
        this.welcome_mes.miniprogram = {
          type: 4,
          title: "",
          media_id: "",
          appid: "",
          url: "",
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 20px 40px;

  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 30px;

  &.type_tips {
    margin-bottom: 0;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}

.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}

.selected_list {
  padding: 10px 0;

  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;

    .delete {
      font-size: 22px;
      cursor: pointer;
    }

    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}

.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  scrollbar-width: 0;

  &::-webkit-scrollbar {
    width: 0;
  }

  .img {
    width: 115px;
    height: 115px;
    margin-right: 20px;
    border: 5px solid #fff;
    position: relative;
    overflow: hidden;

    &.active {
      border: 5px solid #409eff;

      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    &:nth-child(4n) {
      margin-right: 0;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .files {
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 5px solid #fff;

    &.active {
      border: 5px solid #409eff;
    }

    &:nth-child(3n) {
      margin-right: 0;
    }

    .file_img {
      width: 120px;
      height: 120px;
      text-align: center;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .file_name {
      font-size: 12px;
      text-align: center;
      max-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      margin-top: 5px;
    }
  }
}

.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}

.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }

  video {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}

.el-form-item ::v-deep .el-radio {
  margin-right: 0;

  &.active {
    border: 1px solid #a6e22e;
  }
}

.upload-demo {
  margin-right: 5px;
}
</style>
