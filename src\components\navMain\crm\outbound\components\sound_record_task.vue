<template>
  <div>
    <div class="outbount_search">
      <div class="outbount_top align-center flex-row">
        <div class="outbount_top_item align-center flex-row mr60 mb12">
          <div class="task_name mr10">通话时长</div>
          <div class="task_sel w220 align-center flex-row">
            <el-select
              class="select_name flex-1"
              size="small"
              v-model="time"
              @change="changeTimer"
            >
              <el-option
                v-for="sel_item in timeList"
                :key="sel_item.id"
                :label="sel_item.name"
                :value="sel_item.id"
              >
              </el-option>
            </el-select>
            <!-- 自定义时间线 -->
            <template v-if="time == 'default'">
              <el-input
                size="small"
                class="w20 ml10"
                v-model="task_params.custom_s_duration"
              >
                <template slot="append">秒</template>
              </el-input>
              -
              <el-input
                class="w20"
                size="small"
                v-model="task_params.custom_e_duration"
              >
                <template slot="append">秒</template>
              </el-input>
            </template>
          </div>
        </div>
        <div class="outbount_top_item align-center flex-row mr60 mb12">
          <div class="task_name mr10">成员姓名</div>
          <div class="task_sel w300 align-center flex-row">
            <div>
              <el-input
                placeholder="请选择成员"
                v-model="user_name"
                @focus="showMemberList"
              >
                <i
                  class="el-input__icon el-icon-circle-close"
                  @click="delName"
                  slot="suffix"
                ></i
              ></el-input>
            </div>
          </div>
        </div>
        <div class="outbount_top_item align-center flex-row mr60 mb12">
          <div class="task_name mr10">被叫号码</div>
          <div class="task_sel w220 align-center flex-row">
            <el-input size="small" v-model="task_params.callee"> </el-input>
          </div>
        </div>
        <div class="outbount_top_item align-center flex-row mr60 mb12">
          <div class="task_name mr10">拨打时间</div>
          <div class="task_sel w300 align-center flex-row">
            <el-date-picker
              class="select_name"
              v-model="date"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeDate"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </div>
        </div>
      </div>
      <div class="search_btns align-center flex-row">
        <el-button plain type="info" @click="reset">重置</el-button>
        <el-button type="primary" @click="getData">查询</el-button>
      </div>
    </div>
    <!-- 直拨录音表单 -->
    <el-table
      :data="task_tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <!-- <el-table-column label="ID" width="70" prop="id"></el-table-column> -->
      <!-- <el-table-column label="成员id" prop="admin_id"> </el-table-column> -->
      <el-table-column label="成员名称" prop="admin_name"> </el-table-column>
      <el-table-column label="主叫" prop="caller"> </el-table-column>
      <el-table-column label="被叫" prop="callee"> </el-table-column>
      <el-table-column label="来源" prop="source"> </el-table-column>
      <el-table-column label="拨打时间" prop="call_time"> </el-table-column>
      <el-table-column label="通话总时长(秒)" prop="total_duration">
      </el-table-column>
      <el-table-column label="被叫时长(秒)" prop="callee_duration">
      </el-table-column>
      <el-table-column label="主叫时长(秒)" prop="caller_duration">
      </el-table-column>
      <el-table-column label="操作" v-slot="{ row }">
        <el-popconfirm
          title="确定下载录音吗？"
          v-if="row.duration > 0"
          @onConfirm="download(row)"
        >
          <el-link slot="reference" type="success" class="mr10"
            >下载录音</el-link
          >
        </el-popconfirm>
        <el-link
          style="margin-left: 5px"
          :underline="false"
          @click="play(row)"
          v-if="row.record_url"
        >
          <div class="audio_img">
            <img
              style="width: 20px; object-fit: cover"
              v-if="row.isPlaying"
              :src="$imageDomain + '/static/admin/outbound/play_voice.gif'"
              alt=""
            />
            <img
              style="width: 20px; object-fit: cover"
              v-else
              :src="$imageDomain + '/static/admin/outbound/voice_icon.png'"
              alt=""
            />
          </div>
        </el-link>
      </el-table-column>
    </el-table>
    <audio
      ref="musicMp3"
      id="musicMp3"
      :autoplay="false"
      controls="false"
      style="z-index: -1"
    ></audio>
    <!-- 分页 -->
    <div class="block">
      <el-pagination
        style="text-align: end; margin-top: 24px"
        background
        layout="prev,pager,next"
        :total="telinfoTotal"
        @current-change="handleCurrentChange"
        :current-page="telinfo_params.page"
        :page-size="telinfo_params.per_page"
      >
      </el-pagination>
    </div>
    <el-dialog :visible.sync="show_select_dia" width="660px" title="选择成员">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :isOutbound="true"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog>
  </div>
</template>

<script>
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
export default {
  props: {
    activeName: {
      type: String,
      default: "first",
    },
  },
  components: {
    memberListSingle
  },
  data() {
    return {
      task_tableData: [],
      telinfo_params: {
        // 当前页
        page: 1,
        // 每页多少条
        per_page: 10,
      },
      // 总条数
      telinfoTotal: 0,
      // 搜索栏
      task_params: {
        page: 1,
        per_page: 10,
        admin_id: "",
        s_duration: "",
        e_duration: "",
        custom_s_duration: "",
        custom_e_duration: "",
        callee: "",
        stime: "",
        etime: "",
      },
      time: "",
      task_table_loading: false,
      timeList: [
        {
          id: "",
          name: "全部时长",
        },
        {
          id: "0_30",
          name: "0-30秒",
        },
        {
          id: "30_60",
          name: "30-60秒",
        },
        {
          id: "60_120",
          name: "60-120秒",
        },
        {
          id: "120_300",
          name: "120-300秒",
        },
        {
          id: "300_600",
          name: "300-600秒",
        },
        {
          id: "600_1000000",
          name: "大于600秒",
        },
        {
          id: "default",
          name: "自定义",
        },
      ],
      memberList: [],
      user_name: "",
      date: [],
      show_select_dia: false,
    };
  },
  created() {
    this.getData();
    this.getDepartment();
  },
  methods: {
    // 拨打记录-直拨记录
    getData() {
      this.task_table_loading = true;
      let timeArr = this.time.split("_");
      if (this.time == "default") {
        this.task_params.s_duration = "";
        this.task_params.e_duration = "";
      } else if (timeArr.length) {
        this.task_params.s_duration = timeArr[0];
        this.task_params.e_duration = timeArr[1];
        this.task_params.custom_s_duration = "";
        this.task_params.custom_e_duration = "";
      }
      if (this.date.length) {
        this.task_params.stime = this.date[0];
        this.task_params.etime = this.date[1];
      }
      this.$http.DirectRecording(this.task_params).then((res) => {
        if (res.status == 200) {
          this.task_tableData = res.data.data;
          // this.telinfo_params.page = res.data.current_page;
          // this.telinfo_params.per_page = res.data.per_page;
          this.telinfoTotal = res.data.total;
        }
      });
    },
    // 下载录音
    download(row) {
      window.open(row.record_url);
    },
    // 分页器-当前页
    handleCurrentChange(val) {
      this.task_params.page = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    changeTimer(e) {
      if (e == "default") {
        this.date = [];
      } else {
        this.custom_s_duration = "";
        this.custom_e_duration = "";
      }
    },
    changeDate(e) {
      console.log(e);
      if (e) {
        this.time = "";
        this.custom_s_duration = "";
        this.custom_e_duration = "";
        this.$forceUpdate();
      }
    },
    // 重置
    reset() {
      this.time = "";
      this.date = [];
      this.task_params.admin_id = "";
      this.task_params.s_duration = "";
      this.task_params.e_duration = "";
      this.task_params.custom_s_duration = "";
      this.task_params.custom_e_duration = "";
      this.task_params.callee = "";
      this.task_params.stime = "";
      this.task_params.etime = "";
    },
    delName() {
      this.task_params.admin_id = "";
      this.user_name = "";
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.task_params.admin_id =
          e.checkedNodes[e.checkedNodes.length - 1].id;
      } else {
        this.user_name = "";
        this.task_params.admin_id = "";
      }
      this.show_select_dia = false;
    },
    showMemberList() {
      this.show_select_dia = true;
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getOutboundDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    // 播放录音按钮
    play(row) {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      if (this.currI !== row.id && this.currI) {
        audios.pause();
        this.$set(row, 'isPlaying', false);
        audios.src = row.record_url
        this.$forceUpdate();
      }
      if (row.isPlaying) {
        audios.pause();
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      } else {
        if (this.currI !== row.id) {
          audios.src = row.record_url
        }
        audios.play();
        this.currI = row.id;
        this.$set(row, 'isPlaying', true);
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      }, row.duration * 1000);
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;
  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }
  .el-dialog__body {
    padding: 0 12px;
  }
}
.mr10 {
  margin-right: 10px;
}
.ml10 {
  margin-left: 10px;
}
.mr60 {
  margin-right: 60px;
}
.mb12 {
  margin-bottom: 12px;
}
.w220 {
  width: 260px;
}
.w20 {
  width: 60px;
}
::v-deep .w20.el-input .el-input-group__append {
  padding: 0 5px;
}
::v-deep .w20.el-input .el-input__inner {
  padding: 0 5px;
}
.outbount_search {
  padding: 20px;
  background: #fff;
}
.outbount_top {
  width: 1200px;
  flex-wrap: wrap;
  .outbount_top_item {
    .task_name {
      color: #2e3c4e;
      font-size: 14px;
    }
    .select_name {
      width: 260px;
    }
  }
}
.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}
.audio_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
  background: #409eff;
}
#musicMp3 {
  position: absolute;
  left: -200%;
  top: -200%;
  width: 0;
  height: 0;
}
</style>
