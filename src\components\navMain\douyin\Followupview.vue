<template>
    <div class="page">
      <div>
        <div class="Header">回访数据</div>
      </div>
        <div class="pagecontent">
          <div class="SearchCondition">
            <div class="block">
              <el-date-picker style="width: 300px" v-model="timeValue" type="daterange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                @change="onChangeTime">
              </el-date-picker>
              <el-cascader style="margin:0px 10px;" size="small" v-model="member_value"
                :options="member_listNEW" clearable filterable placeholder="成员" :style="{
                    minWidth: '20px',
                     width: '110px',
                    }" :props="{
                        label: 'user_name',
                        value: 'id',
                        children: 'subs',
                        checkStrictly: true,
                    }" @change="loadFirstLevelChildren">
                </el-cascader>
                <el-select v-model="paramsdata.sort" placeholder="排序"
                size="small" style="width: 145px;" @change="Sortevents"
                clearable>
                  <el-option
                    v-for="item in sortoptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.value">
                  </el-option>
                </el-select>
            </div>
            <div class="head-list">
              <el-button v-show="show>0" size="small" type="primary" @click="exportfollow">导出</el-button>
                <!-- <el-button class="listbnt" size="mini" @click="doulist">
                  <div class="flex-row items-center">
                    <img src="https://img.tfcs.cn/backup/static/admin/douyin/statistics/douyinlist.png" alt="">
                    <span>数据列表</span>
                  </div>
                </el-button> -->
            </div>
          </div>
          <div class="taketable">
            <el-table
                v-loading="is_table_loading"
                :data="classifydata"
                style="width: 100%;margin-bottom: 20px;"
                :header-cell-style="{ background: '#EBF0F7' }"
                row-key="id"
                border>
                <el-table-column
                  prop="user_name"
                  align="center"
                  label="姓名"
                  >
                </el-table-column>
                <el-table-column
                  prop="follow_up_num"
                  align="center"
                  label="回访量"
                  >
                </el-table-column>
                <el-table-column
                  prop="on_call_num"
                  align="center"
                  label="外呼接通量"
                  >
                </el-table-column>
                <el-table-column
                  prop="un_call_num"
                  align="center"
                  label="外呼未接通量"
                  >
                </el-table-column>
                <el-table-column
                  prop="last_hf_content"
                  align="center"
                  label="最近回访内容"
                  >
                </el-table-column>
                <el-table-column
                  prop="last_hf_date"
                  align="center"
                  label="最近回访时间"
                  >
                </el-table-column>
                <el-table-column
                  prop="last_follow_date"
                  align="center"
                  label="最后一次跟进时间"
                  >
                </el-table-column>
            </el-table>
              <div class="page_footer flex-row items-center">
               <div class="page_footer_l flex-row flex-1 items-center">
                 <div class="head-list">
                   <el-button type="primary" size="small" @click="empty">清空</el-button>
                 </div>
                 <div class="head-list">
                   <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                 </div>
               </div>
               <div style="margin-right:10px;">
                 <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
                   :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
                   @current-change="onPageChange" @size-change="handleSizeChange">
                 </el-pagination>
               </div>
              </div>
          </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            timeValue:"",//时间检索
            pickerOptions: {
                shortcuts: [{
                  text: '今天',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                    text: '昨天',
                    onClick(picker) {
                      const end = new Date();
                      const start = new Date(end);
                      start.setDate(start.getDate() - 1);
                      end.setDate(start.getDate());
                      picker.$emit('pick', [start, end]);
                    }
                  },{
                  text: '本周',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end);
                    start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上周',
                  onClick(picker) {
                    const end = new Date(); // 获取当前日期
                      end.setDate(end.getDate() - end.getDay() - 7); // 获取上周的最后一天
                      end.setHours(23, 59, 59, 0);
                      const start = new Date(end);
                      start.setDate(start.getDate() - 6); // 获取上一周的第一天
                      start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '本月',
                  onClick(picker) {
                    const end = new Date();
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                }, {
                  text: '上月',
                  onClick(picker) {
                    const end = new Date();
                    end.setDate(0); // 获取上个月的最后一天
                    end.setHours(23, 59, 59, 0);
                    const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                    start.setHours(0, 0, 0, 0);
                    picker.$emit('pick', [start, end]);
                  }
                },]
            },
            member_value:"",
            //成员
            member_listNEW: [],
            classifydata:[],//表格数据
            params:{
                total:0,
                per_page:10,
                page:1
            },
            paramsdata:{
                per_page:10,
                page:1,
                start_date:"",
                end_date:"",
                admin_id:"",
                sort:"",
            },
            sortoptions:[
              {id:1,value:"follow_up_num",name:"回访量"},
              {id:2,value:"on_call_num",name:"外呼接通降序"},
              {id:3,value:"un_call_num",name:"外呼未接通降序"},
            ],
            show:0,
            is_table_loading:false,
        }
    },
    mounted(){
      let pagenum = localStorage.getItem( 'pagenum')
        this.params.per_page = Number(pagenum)||10
        this.paramsdata.per_page = Number(pagenum)||10
      if(this.$store.state.ismanager){
        this.show =  this.$store.state.ismanager
      }else{
        this.btnexport()
      }
        
        this.getlookdata()
        this.MembersNEW()
    },
    methods:{
        //获取是否是可以显示导出按钮
        btnexport(){
          this.$http.determinecustomeradmin().then((res)=>{
            if(res.status==200){
              console.log(res.data," //判断是不是客户管理员");
              this.show = res.data.is_manager
            }
          })
        },
        //获取表格数据
        getlookdata(){
          this.is_table_loading = true
            this.$http.getFollowuplist(this.paramsdata).then((res)=>{
                if(res.status==200){
                  this.is_table_loading = false
                    this.classifydata = res.data.data
                    this.params.total = res.data.total
                    console.log(this.classifydata,"=================");
                }
            })
        },
        // 自定义筛选时间发生改变时触发
        onChangeTime(e) {
          this.paramsdata.start_date = e ? e[0] : ""; // 赋值开始时间
          this.paramsdata.end_date = e? e[1] : ""; // 赋值结束时间
          this.paramsdata.page = 1; // 显示第一页
          this.params.page = 1; // 显示第一页
          this.getlookdata(); // 获取最新数据
        },
        // 获取成员的接口（新）
        MembersNEW(){
          this.$http.getDepartmentMemberListNew().then((res)=>{
            if(res.status==200){
                console.log(res.data);
              this.member_listNEW = res.data
            }
          })
        },
        //成员
        loadFirstLevelChildren(value) {
          this.paramsdata.admin_id = value[0]
          this.getlookdata(); // 获取最新数据
        },
        Sortevents(){
          this.getlookdata(); // 获取最新数据
        },
        //跳转到数据列表页
        doulist() {
          this.$goPath(`/crm_Follow_up_list`);
        },
        exportfollow(){
          this.$confirm('确定要导出数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
           this.$http.exportfollowupdata(this.paramsdata).then((res)=>{
            if(res.status == 200){
              window.open(res.data);
            }
           })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消导出'
            });          
          });
        },
        //分页
        onPageChange(current_page) {
          this.params.page = current_page;
          this.paramsdata.page = current_page;
          this.getlookdata();
        },
                //每页几条
                handleSizeChange(e){
          this.params.per_page = e
          this.paramsdata.per_page = e;
          this.getlookdata();
        },
        //清空
        empty(){
          this.timeValue =  ""
          this.member_value = ""
          this.paramsdata={
            per_page:this.params.per_page,
            page:1,
            start_date:"",
            end_date:"",
            admin_id:"",
            sort:"",
          }
          this.getlookdata();
        },
        //刷新
        Refresh(){
          this.getlookdata();
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 20px 24px 80px;
    .Header{
      width: 110px;
      height: 40px;
      background-color: #ffffff;
      text-align: center;
      line-height: 42px;
      color: #8a929f;
    }
    .pagecontent{
        width: 100%;
        // height: 700px;
        background-color: #ffffff;
        border-radius: 4px;
        overflow: hidden;
        .SearchCondition{
            margin: 20px;
            display: flex;
            justify-content: space-between;
            .head-list{
              display: flex;
            }
        }
        .taketable{
            width: 97%;
            margin: 0 auto;
            .page_footer {
              position: fixed;
              left: 230px;
              right: 0;
              bottom: 0;
              background: #fff;
              padding: 10px;
              z-index: 1000;
              .head-list{
                margin-left: 13px;
              }
            }
        }
    }
}
</style>