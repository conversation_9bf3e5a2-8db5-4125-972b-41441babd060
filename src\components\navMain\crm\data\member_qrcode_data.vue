<template>
  <!-- 财务报告 -->
  <div class="pages">
    <div class="data-box div row">
      <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/dqq.png?x-oss-process=style/w_1200');
        ">
        <div class="number">{{ statistical_list.totalCount || 0 }}</div>
        <div class="number2">扫码添加好友人数</div>
      </div>
      <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrjq.png?x-oss-process=style/w_1200');
        ">
        <div class="number">{{ statistical_list.count || 0 }}</div>
        <div class="number2">净增好友人数</div>
      </div>
      <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrtq.png?x-oss-process=style/w_1200');
        ">
        <div class="number">{{ statistical_list.reduceCount || 0 }}</div>
        <div class="number2">总流失好友数</div>
      </div>
      <div class="data-item" style="
          background-image: url('https://img.tfcs.cn/backup/static/admin/customer/jrtq.png?x-oss-process=style/w_1200');
        ">
        <div class="number">{{ statistical_list.dayCount || 0 }}</div>
        <div class="number2">今日新增人数</div>
      </div>
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm">
          <el-table v-loading="is_table_loading" :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row :row-style="$TableRowStyle">
            <el-table-column label="企业员工" v-slot="{ row }">
              <div class="div row m-user">
                <img class="a-avatar" v-if="row.wx_work_avatar" src="" alt="" />
                <div class="a-avatar" v-else>{{ row.user_name[0] }}</div>
                <span>{{ row.user_name }}</span>
              </div>
            </el-table-column>
            <el-table-column align="center" label="扫码添加好友人数" prop="totalCount"></el-table-column>
            <el-table-column align="center" label="净增好友人数" prop="count"></el-table-column>
            <el-table-column align="center" label="总流失好友数" prop="reduceCount"></el-table-column>
            <el-table-column align="center" label="今日新增人数" prop="dayCount"></el-table-column>
            <el-table-column align="center" label="今日流失人数" prop="reduceDayCount"></el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "member_qrcode_data",
  data() {
    return {
      statistical_list: "",
      tableData: [],
      is_table_loading: false,
    };
  },
  mounted() {
    let id = this.$route.query.id;
    this.getMemberCodeData(id);
  },
  methods: {
    getMemberCodeData(id) {
      this.is_table_loading = true;
      this.$http.getMemberCodeData(id).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.statistical_list = res.data.statistics;
          this.tableData = res.data.users;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}

.data-box {
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  .data-item {
    display: flex;
    flex-direction: column;
    width: 20%;
    justify-content: center;
    padding-left: 60px;
    height: 140px;
    border-radius: 10px;
    background-size: 100% 100%;
    color: #fff;
    background-repeat: no-repeat;

    .number {
      font-size: 36px;
    }

    .number2 {
      font-size: 18px;
      margin-top: 6px;
    }
  }
}

.m-user {
  align-items: center;

  .a-avatar {
    text-align: center;
    width: 30px;
    height: 30px;
    overflow: hidden;
    border-radius: 50%;
    margin-right: 10px;
    line-height: 30px;
    color: #fff;
    background: #2d84fb;
  }
}
</style>
