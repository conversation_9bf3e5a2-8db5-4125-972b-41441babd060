<template>
  <div class="outbound">
    <div class="tabs" v-if="this.setnull">
      <div class="check-box div row" id="pages_content">
        <div v-for="item in tabs_list" @click="onClickTabs(item)" :class="{ isactive: item.title === is_tabs_page }"
          :key="item.id" class="check-item">
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="tabs" v-if="this.getnull">
      <div class="check-box div row" id="pages_content">
        <div v-for="item in tabs_listtwo" @click="onClickTabs(item)" :class="{ isactive: item.title === is_tabs_page }"
          :key="item.id" class="check-item">
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- 外呼数据统计 -->
    <div class="balance" v-if="is_tabs_page !== 'intelligent' && is_tabs_page !== 'speechLibrarySetting'
    && is_tabs_page !== 'AIconfigurationpage'">
      <div class="sms-border div row">
        <div class="sms-item flex-1">
          <div class="t">{{ OutboundData.amount }}</div>
          <div class="b">充值金额(元)</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">{{ OutboundData.balance || 0 }}</div>
          <div class="b">余额(元)</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">
            {{ OutboundData.total_duration | filterSen2Hour }}
          </div>
          <div class="b">通话总时长</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">
            {{ OutboundData.total_callee_duration | filterSen2Hour }}
          </div>
          <div class="b">被叫总时长</div>
        </div>

        <div class="sms-item flex-1">
          <div class="t">
            {{ OutboundData.total_caller_duration | filterSen2Hour }}
          </div>
          <div class="b">主叫总时长</div>
        </div>
      </div>
    </div>
    <!--智能AI机器人-->
    <div class="balance" v-if="is_tabs_page == 'intelligent'" v-loading="AIborot_loading">
      <div class="sms-border div row">
        <div class="sms-item flex-1">
          <div class="t">{{ robotData.phone_count }}</div>
          <div class="b">号码总量</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">{{ robotData.call_count }}</div>
          <div class="b">接通总量</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">
            {{ robotData.total_duration | filterSen2Hour }}
          </div>
          <div class="b">接通总时长</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">
            {{ robotData.avg_duration | filterSen2Hour }}
          </div>
          <div class="b">平均时长</div>
        </div>

        <!-- <div class="sms-item flex-1">
          <div class="t">
            --
          </div>
          <div class="b">AI数据(五)</div>
        </div> -->
      </div>
    </div>
    <div class="tab_content">
      <template v-if="is_tabs_page == 'setting'">
        <el-form label-width="120px">
          <el-form-item label="机构名称">
            <el-input class="w_300" :disabled="jigou_params.id ? true : false" v-model="jigou_params.organization_name">
            </el-input>
          </el-form-item>
          <el-form-item label="用户名">
            <el-input class="w_300" :disabled="jigou_params.id ? true : false" v-model="jigou_params.user_name">
            </el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input class="w_300" :disabled="jigou_params.id ? true : false" v-model="jigou_params.phone">
            </el-input>
          </el-form-item>
        </el-form>
        <div class="btns">
          <el-button type="primary" :loading="sub_loading" :disabled="jigou_params.id ? true : false" @click="saveJigou">
            提交</el-button>
        </div>
      </template>
      <template v-if="is_tabs_page == 'seat'">
        <div class="outbount_search" v-if="search_list.length">
          <div class="outbount_top align-center flex-row">
            <div class="outbount_top_item align-center flex-row mr60 mb12" v-for="(item, index) in search_list"
              :key="index">
              <div class="task_name mr10">{{ item.name }}</div>
              <template v-if="item.type == 'select'">
                <el-select class="select_name" v-model="zuoxi_params[item.value]">
                  <el-option v-for="sel_item in item.options" :key="sel_item.id" :label="sel_item.label"
                    :value="sel_item.value">
                  </el-option>
                </el-select>
              </template>
              <template v-if="item.type == 'date'">
                <el-date-picker class="select_name" v-model="zuoxi_params[item.value]" type="daterange"
                  start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
              </template>
            </div>
          </div>
          <div class="search_btns align-center flex-row">
            <el-button plain type="info">重置</el-button>
            <el-button type="primary">查询</el-button>
          </div>
        </div>
        <div class="table">
          <div class="table_oper flex-row align-center">
            <div class="flex-1"></div>
            <div class="table_oper_item flex-row align-center">
              <!-- <el-button size="small" type="primary" @click="mangerJigou"
                >外呼机构</el-button
              > -->
              <el-input placeholder="请输入坐席" size="small" v-model="zuoxi_params.admin_name" class="input-with-select">
                <el-button slot="append" icon="el-icon-search" @click="getZuoxi"></el-button>
              </el-input>
              <el-button style="margin-left: 10px;" size="small" type="warning" @click="showOutTel">外显号码</el-button>
              <el-button size="small" type="primary" @click="addZuoxi">添加坐席</el-button>
              <!-- <el-button size="small" type="primary">添加坐席</el-button> -->
            </div>
          </div>
          <el-table v-loading="zuoxi_table_loading" :data="zuoxi_tableData" border
            :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
            <!-- @selection-change="handleSelectionChange" -->
            <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
            <el-table-column label="ID" width="75" prop="id"></el-table-column>

            <el-table-column label="成员名称" prop="admin_name">
            </el-table-column>
            <el-table-column label="外显号码" prop="show_phone">
            </el-table-column>
            <el-table-column label="状态" prop="status" v-slot="{ row }">
              <el-tag :type="row.status == 1 ? 'success' : 'warning'">{{
                row.status == 1 ? "已启用" : "已停用"
              }}</el-tag>
            </el-table-column>
            <el-table-column label="添加时间" prop="created_at">
            </el-table-column>

            <el-table-column label="操作" v-slot="{ row }" width="220">
              <el-button type="primary" class="mr10" @click="editZuoxi(row)">编辑</el-button>
              <!-- <el-popconfirm title="确定删除此坐席吗？" class="mr10" @onConfirm="delZuoxi(row)"> -->
              <el-button slot="reference" type="warning" @click="delZuoxi(row)">删除</el-button>
              <!-- </el-popconfirm> -->
            </el-table-column>
          </el-table>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev,pager,next" :total="zuoxiTotal"
            :page-size="zuoxi_params.per_page" :current-page="zuoxi_params.page"
            @current-change="onZuoxiPageChange"></el-pagination>

          <el-dialog width="500px" :title="zuoxiTitle" :visible.sync="zuoxi_edit" :close-on-click-modal="false">
            <el-form label-width="120px">
              <el-form-item label="选择成员">
                <el-input class="w-300" placeholder="请选择成员" v-model="zuoxi_user.name" @focus="showMemberList">
                </el-input>
              </el-form-item>
              <!-- filterable
                  remote
                  :remote-method="getShowTelNumber" -->
              <el-form-item label="选择外显号码">
                <el-select v-model="zuoxi_form.show_id" class="w-300" multiple @change="handlechange" filterable remote
                  placeholder="请输入联系方式">
                  <el-option v-for="item in show_number_list" :loading="telNumberLoading" :key="item.id"
                    :label="`${item.phone}${item.name ? ' -- ' + item.name : ''}`" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 添加的可用坐席套餐 -->
              <el-form-item label="可用坐席套餐" v-if="zuoxiTitle == '添加坐席' && showSeats != 0">
                <el-select v-model="zuoxi_form.buy_id" class="w-300">
                  <el-option v-for="item in Seats_list" :loading="telNumberLoading" :key="item.id" :label="'总坐席' +
                    item.seat_total +
                    ' ' +
                    '可用坐席' +
                    (item.seat_total - item.use_seat_total) +
                    ' ' +
                    '剩余天数' +
                    item.remain_time
                    " :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 编辑的可用坐席套餐 -->
              <el-form-item label="可用坐席套餐" v-if="zuoxiTitle == '编辑坐席' && show_package == 1">
                <el-select v-model="zuoxi_form.buy_id" class="w-300">
                  <el-option v-for="item in Seats_list" :loading="telNumberLoading" :key="item.id" :label="'总坐席' +
                    item.seat_total +
                    ' ' +
                    '可用坐席' +
                    (item.seat_total - item.use_seat_total) +
                    ' ' +
                    '剩余天数' +
                    item.remain_time
                    " :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <div v-if="zuoxiTitle == '编辑坐席' &&
                showSeats != 0 &&
                show_package == 0
                " style="margin-left: 25px">
                {{ this.description }}
              </div>
              <el-form-item label="状态">
                <!-- <el-select v-model="zuoxi_form.status">                   active-color="#13ce66"
                  inactive-color="#ff4949" -->
                <el-switch v-model="zuoxi_form.status" :active-value="1" :inactive-value="2">
                </el-switch>
                <div style="color: red;" v-if="zuoxiTitle == '添加坐席'">每添加一个坐席将扣除20元月租</div>
              </el-form-item>
              <div class="Explicit-table" v-if="show && gridData.length">
                <el-table :data="gridData" border>
                  <el-table-column prop="show_phone" label="号码"></el-table-column>
                  <el-table-column prop="is_default" label="是否默认">
                    <template slot-scope="scope">
                      <el-switch v-model="scope.row.is_default" :active-value="1" :inactive-value="0"
                        @change="switchChange(scope.row)">
                      </el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="排序">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.sort" placeholder="请输入序号"></el-input>
                    </template>
                  </el-table-column>
                </el-table>

              </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button type="primary" @click="cancelAdd">取消</el-button>
              <el-button type="primary" :loading="is_add_loading" @click="submitZuoxi">提交</el-button>
            </span>
          </el-dialog>
          <el-dialog :visible.sync="show_select_dia" width="660px" title="选择人员">
            <memberListSingle v-if="show_select_dia" :list="memberList" source="renshi" :defaultValue="selectedIds"
              @onClickItem="selecetedMember"></memberListSingle>
          </el-dialog>
          <el-drawer class="drawers-box" title="外显号码与批量导入" :visible.sync="show_out_tel" direction="rtl" size="50%">
            <outTelList></outTelList>
          </el-drawer>
          <!-- <el-dialog
            :visible.sync="show_out_tel"
            width="800px"
            title="外显号码"
          >
            <outTelList></outTelList>
          </el-dialog> -->
          <el-drawer title="外呼机构" :visible.sync="show_out_jigou" direction="rtl" size="50%">
            <el-form label-width="120px">
              <el-form-item label="机构名称">
                <el-input class="w_300" :disabled="jigou_params.id ? true : false"
                  v-model="jigou_params.organization_name">
                </el-input>
              </el-form-item>
              <el-form-item label="用户名">
                <el-input class="w_300" :disabled="jigou_params.id ? true : false" v-model="jigou_params.user_name">
                </el-input>
              </el-form-item>
              <el-form-item label="手机号">
                <el-input class="w_300" :disabled="jigou_params.id ? true : false" v-model="jigou_params.phone">
                </el-input>
              </el-form-item>
              <el-form-item label="状态">
                <div>
                  <el-tag v-if="jigou_params.status == 0" type="warning">待审</el-tag>
                  <el-tag v-if="jigou_params.status == 1" type="success">通过</el-tag>
                  <!-- {{ jigou_params.status == 0 ? "待审" : "通过" }} -->
                </div>
              </el-form-item>
            </el-form>
            <div class="btns">
              <el-button type="primary" :loading="sub_loading" :disabled="jigou_params.id ? true : false"
                @click="saveJigou">
                提交</el-button>
            </div>
          </el-drawer>
        </div>
      </template>
      <template v-if="is_tabs_page == 'channel'">
        <channel></channel>
      </template>
      <!-- 套餐管理 -->
      <template v-if="is_tabs_page == 'package'">
        <setMenu></setMenu>
      </template>
      <!-- 外呼配置 -->
      <template v-if="is_tabs_page == 'OutboundConfig'">
        <OutboundConfig></OutboundConfig>
      </template>
      <!--AI机器人配置-->
      <!-- <el-button class="tolead-box" size="small" type="primary" @click="tolead">导入</el-button> -->
      <template v-if="is_tabs_page == 'intelligent'">
        <intelligent></intelligent>
      </template>
      <!-- 话术库 -->
      <template v-if="is_tabs_page == 'speechLibrarySetting'">
        <speechLibrarySetting></speechLibrarySetting>
      </template>
      <!-- AI配置 -->
      <template v-if="is_tabs_page == 'AIconfigurationpage'">
        <AIconfigurationpage></AIconfigurationpage>
      </template>
    </div>
  </div>
</template>

<script>
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
import outTelList from "@/components/navMain/crm/components/outTelList/outTelList.vue";
import channel from "@/components/navMain/crm/components/channel/channel.vue";
import setMenu from "@/components/navMain/crm/components/setMenu/setMenu.vue";
import OutboundConfig from "@/components/navMain/crm/components/OutboundConfig/OutboundConfig.vue";
import intelligent from "@/components/navMain/crm/components/intelligent/intelligent.vue";
import speechLibrarySetting from "@/components/navMain/crm/components/speechLibrarySetting/speechLibrarySetting.vue";
import AIconfigurationpage from "@/components/components/AI/AIconfiguration_page.vue";
export default {
  components: {
    memberListSingle,
    outTelList,
    channel,
    setMenu,
    OutboundConfig,
    intelligent,
    speechLibrarySetting,
    AIconfigurationpage
  },
  props: {
    query_type: {
      type: String,
      default: ''
    }
  },
  filters: {
    filterSen2Hour(val) {
      let wholeTime = 0
      let sencond = parseInt(val) || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((sencond % 3600) % 60) % 60;
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
      return wholeTime
    }
  },
  data() {
    return {
      tabs_list: [
        // {
        //   id: 1,
        //   title: "setting",
        //   name: '外呼机构'
        // },
        {
          id: 2,
          title: "seat",
          name: '坐席管理'
        },
        {
          id: 3,
          title: "channel",
          name: '渠道管理'
        },
        {
          id: 4,
          title: "package",
          name: '消费记录'
        },
        {
          id: 5,
          title: "OutboundConfig",
          name: '外呼配置'
        },
        // {
        //   id: 6,
        //   title: "intelligent",
        //   name: '智能AI机器人'
        // }
        {
          id: 7,
          title: "speechLibrarySetting",
          name: '话术库'
        },
        {
          id: 8,
          title: "AIconfigurationpage",
          name: 'AI配置'
        },
      ],
      tabs_listtwo: [
        // {
        //   id: 1,
        //   title: "setting",
        //   name: '外呼机构'
        // },
        {
          id: 2,
          title: "seat",
          name: '坐席管理'
        },
        {
          id: 3,
          title: "channel",
          name: '渠道管理'
        },
        {
          id: 4,
          title: "package",
          name: '消费记录'
        },
        {
          id: 5,
          title: "OutboundConfig",
          name: '外呼配置'
        },
        {
          id: 6,
          title: "intelligent",
          name: '智能AI机器人'
        },
        {
          id: 7, 
          title: "speechLibrarySetting",
          name: '话术库'
        },
        {
          id: 8,
          title: "AIconfigurationpage",
          name: 'AI配置'
        },
      ],
      AIborot_loading: false,
      is_tabs_page: "seat",
      jigou_params: {
        phone: '',
        organization_name: '',
        user_name: ''
      },
      sub_loading: false,
      zuoxi_params: {
        page: 1,
        per_page: 10,
        admin_name:''
      },
      zuoxiTotal: 0,
      search_list: [],
      zuoxi_tableData: [],
      zuoxi_table_loading: false,
      // 坐席添加编辑相关
      selectedIds: [], //默认选中
      memberList: [],  //部门列表
      show_select_dia: false, //选择人员弹框 
      show_number_list: [],  //外显号码列表
      show_Seats_list: [], // 坐席套餐列表
      show: false,//添加坐席号码详情
      zuoxi_form: {
        status: 1, //状态
        show_id: [], //外显号码id
        admin_id: '', //会员id
        buy_id: "", // 坐席套餐id
        show_phones: []
      },
      zuoxi_user: {  //选择人员信息
        id: '',
        name: ''
      },
      getnull:false,
      setnull: false,
      zuoxi_edit: false, //显示添加编辑坐席弹框
      zuoxiTitle: '', //添加编辑坐席的弹框标题
      gridDatacopy: [],
      gridData: [],
      telNumberLoading: false,
      is_add_loading: false,
      show_out_tel: false,
      show_out_jigou: false,
      showSeats: 0, // 控制可用坐席套餐是否开启
      Seats_list: [], // 坐席套餐列表
      description: "",
      show_package: 0, // 编辑坐席是否显示可用坐席套餐0隐1显
      OutboundData: "", // 外呼数据
      robotData: '', //机器人外呼数据
      website_id: '',//当前站点id
    }
  },
  created() {
    this.getrobot()
    if (this.query_type) {
      this.is_tabs_page = this.query_type

    }
    // 赋值website_id
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id
    }
    // if(this.website_id == 176 || this.website_id == 109 ){
    //   this.tabs_listtwo.push({
    //     id: 8,
    //     title: "AIconfigurationpage",
    //     name: 'AI配置'
    //   })
    //   this.tabs_list.push({
    //     id: 8,
    //     title: "AIconfigurationpage",
    //     name: 'AI配置'
    //   })
    // }

    //小钢炮定制机器人模块判定
    if (this.website_id != 176 || this.website_id != 626 || this.website_id != 164) {
      this.getnull = false
      this.setnull = true
    } else if (this.website_id == 176 || this.website_id == 626 || this.website_id == 164 || this.website_id == 639) {
      this.getnull = true
      this.setnull = false
    } 
     if (this.website_id == 176 || this.website_id == 626 || this.website_id == 164 || this.website_id == 639) {
      this.setnull = false
      this.getnull = true
    } else if (this.website_id != 176 || this.website_id != 626 || this.website_id != 164) {
      this.setnull = true
      this.getnull = false
    }
    console.log(this.website_id, "11111111")

    // if (this.query_type == "menu") {
    //   this.is_tabs_page = "zuoxi"
    // }
    // 2-seat 坐席管理
    // 2-channel 渠道管理
    // 2-package 套餐管理
    // this.getJigou()
    this.getShowTelNumber()
    if (this.is_tabs_page == "seat") {
      this.getZuoxi()
      this.getShowSeats()
    }
    // 获取外呼数据统计
    this.getOutboundStatistics()
  },
  computed: {
    callTotalDuration() {
      // 秒 
      const sencond = parseInt(this.OutboundData.duration) || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((sencond % 3600) % 60) % 60;
      let wholeTime = ''
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
      return wholeTime
      // const minute = sencond / 60;
      // const sec = sencond % 60;
      // let wholeTime = null
      // if (sec != 0) {
      //   wholeTime = parseInt(minute) + "'" + sec + '"'
      // } else {
      //   wholeTime = parseInt(minute) + "'"
      // }
      // return wholeTime
    },
    callRestDuration() {
      // 秒 
      const sencond = parseInt(this.OutboundData.call_time_send) || 0
      const hour = sencond / 3600;
      const minute = (sencond % 3600) / 60;
      const sec = ((sencond % 3600) % 60) % 60;
      let wholeTime = null
      wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
      return wholeTime
    }
  },
  methods: {
    onClickTabs(item) {
      this.is_tabs_page = item.title
      switch (this.is_tabs_page) {
        case 'setting':
          this.getJigou()
          break;
        case 'seat':
          this.getZuoxi()
          break;

        default:
          break;
      }
    },
    mangerJigou() {
      this.getJigou()
      this.show_out_jigou = true
    },
    // 添加机构 获取机构
    getJigou() {
      this.$http.getJigou().then(res => {
        if (res.status == 200) {
          this.jigou_params = res.data
        }
      })
    },
    getrobot() {
      console.log("调用了机器人数据")
      this.$http.getrobotstatistics().then(res => {
        this.robotData = res.data;
        console.log(this.robotData, "获取到的机器人数据")
      })
    },
    saveJigou() {
      this.sub_loading = true
      this.$http.saveJigou(this.jigou_params).then(res => {
        if (res.status == 200) {
          this.$message.success(res.data?.message || res.message || '保存成功')
        }
        this.sub_loading = false
      })
        .catch(() => {
          this.sub_loading = false
        })
    },
    // 获取坐席列表
    getZuoxi() {
      this.zuoxi_table_loading = true
      this.$http.getZuoxi(this.zuoxi_params).then(res => {
        if (res.status == 200) {
          this.zuoxi_tableData = res.data.data
          this.zuoxiTotal = res.data.total
        }
        this.zuoxi_table_loading = false
      }).catch(() => {
        this.zuoxi_table_loading = false
      })
    },
    // 坐席列表页面更新
    onZuoxiPageChange(val) {
      this.zuoxi_params.page = val
      this.getZuoxi()
    },
    // 添加,编辑,删除,坐席
    addZuoxi() {
      this.gridData = []
      this.show = false
      this.zuoxi_form.status = 1
      this.zuoxi_form.admin_id = ''
      this.zuoxi_form.show_id = ''
      this.zuoxi_user = {
        id: '',
        name: ''
      }
      this.isAdd = true
      this.getDepartment()
      this.getShowTelNumber()
      this.zuoxiTitle = "添加坐席"
      this.zuoxi_edit = true


    },
    editZuoxi(row) {
      this.gridData = []
      //获取编辑信息，显示
      this.$http.DetailsZuoxzi(row.id).then(res => {
        if (res.status == 200) {
          this.zuoxi_form.admin_id = res.data.admin_id
          // this.zuoxi_form.buy_id = row.seat_package_id
          // this.zuoxi_form.status = res.data.status
          this.zuoxi_form.id = res.data.id
          let gridData = res.data.call_seats_phones
          this.gridData = JSON.parse(JSON.stringify(gridData))
          this.gridDatacopy = JSON.parse(JSON.stringify(gridData))
          this.zuoxi_user = {
            id: res.data.admin_id,
            name: res.data.admin_name
          }
          this.zuoxi_form.show_id = []
          this.gridData.map(item => {
            this.zuoxi_form.show_id.push(item.show_id)
          })
          // this.zuoxi_form.show_id = this.gridData.show_phone
          this.show = true
          this.getDepartment()
          this.getShowTelNumber()
          this.idObtainSeatsState(row)
          this.zuoxiTitle = "编辑坐席"
          this.zuoxi_edit = true
        }
      })
      // this.zuoxi_form.show_id = [];
      // this.zuoxi_form.show_phones = []
      this.zuoxi_form.buy_id = row.seat_package_id
      this.isAdd = false
      this.zuoxi_form.status = row.status
      // this.zuoxi_form.admin_id = row.admin_id
      this.zuoxi_form.show_id = row.show_phone
      // this.zuoxi_form.id = row.id
      // row.show_phones.map((item) => {
      //   this.zuoxi_form.show_id.push(item.id);
      // })
      // this.zuoxi_user = {
      //   id: row.admin_id,
      //   name: row.admin_name
      // }

    },
    delZuoxi(row) {
      this.$confirm('此操作将删除该坐席, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delZuoxzi(row.id).then(res => {
          if (res.status == 200) {
            this.$message.success(res.message || res.data?.message || '删除成功')
            this.getZuoxi()
          }
        }).catch(() => {

        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    submitZuoxi() {
      if (this.isAdd) {
        this.addSubmit()
      } else {
        this.editSubmit()
      }
    },
    addSubmit() {
      if (!this.zuoxi_user.id) {
        this.$message.warning("请选择坐席人员")
        return
      }
      // if (this.zuoxi_form.buy_id == '') {
      //   this.$message.warning("请选择可用坐席套餐")
      //   return
      // }
      for (const element of this.gridData) {
        if (element.sort == undefined) {
          this.$message.warning("请输入序号")
          return
        }
      }
      this.zuoxi_form.admin_id = this.zuoxi_user.id
      this.zuoxi_form.show_id = this.zuoxi_form.show_id.toString()
      this.is_add_loading = true
      let show_phones1 = {
        show_phones: []
      }
      this.gridData.forEach(item => {
        // show_phones.show_phones.push({ show_id: item.show_id, is_default: item.is_default, sort: item.sort })
        show_phones1.show_phones.push(item)
      })
      if (this.zuoxi_form.show_id) {
        delete this.zuoxi_form.show_id
        show_phones1.admin_id = this.zuoxi_form.admin_id
        show_phones1.status = this.zuoxi_form.status
      }
      show_phones1.show_phones = JSON.stringify(show_phones1.show_phones)
      this.$http.addZuoxzi(show_phones1).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '添加成功')
          this.getZuoxi()
          this.getShowSeats()
          this.zuoxi_edit = false
        } else {
          this.gridData = []
        }
        this.is_add_loading = false
        this.zuoxi_form.show_id = "";
      }).catch(() => {
        this.is_add_loading = false
      })
    },
    editSubmit() {
      if (!this.zuoxi_user.id) {
        this.$message.warning("请选择坐席人员")
        return
      }
      for (const element of this.gridData) {
        if (element.sort == undefined) {
          this.$message.warning("请输入序号")
          return
        }
      }
      this.zuoxi_form.admin_id = this.zuoxi_user.id
      this.is_add_loading = true
      this.zuoxi_form.show_id = this.zuoxi_form.show_id.toString()
      this.zuoxi_form.show_phones = []
      this.gridData.forEach(item1 => {
        if (item1) {
          if (item1.id) {
            this.zuoxi_form.show_phones.push({ show_id: item1.id, is_default: item1.is_default, sort: item1.sort })
          } else {
            this.zuoxi_form.show_phones.push({ show_id: item1.show_id, is_default: item1.is_default, sort: item1.sort })
          }
        }

      })

      this.zuoxi_form.show_phones = JSON.stringify(this.zuoxi_form.show_phones)
      if (this.zuoxi_form) {
        delete this.zuoxi_form.show_id
      }
      this.$http.editZuoxzi(this.zuoxi_form).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || res.data?.message || '编辑成功')
          this.getZuoxi()
          this.getShowSeats()
          this.zuoxi_edit = false
        } else {
          this.gridData = this.gridDatacopy
          // this.gridData.map(item=>{
          //   console.log(item);
          //   console.log( this.zuoxi_form);
          //   this.zuoxi_form.show_id = item.show_phone
          //   console.log(this.zuoxi_form);
          // })
        }
        this.is_add_loading = false
        this.zuoxi_form.show_id = "";
      }).catch(() => {
        this.is_add_loading = false
      })
    },
    // 根据id获取坐席套餐状态
    idObtainSeatsState(row) {
      this.$http.getSeatsState(row.seat_package_id).then(res => {
        if (res.status == 200) {
          this.show_package = res.data.show_package
          this.description = res.data.description
        }
      })
    },
    //设置默认只能有一个打开
    switchChange(row) {
      let switchid = []
      this.gridData.map(item => {
        if (item.is_default == 1) {
          switchid.push(item.is_default)
          if (switchid.length > 1) {
            this.$message({
              type: "warning",
              message: "只能有一个默认号码"
            })
            row.is_default = 0
            return
          }
        }
      })
    },
    cancelAdd() {
      this.zuoxi_edit = false
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.zuoxi_user = {
          id: e.checkedNodes[e.checkedNodes.length - 1].id,
          name: e.checkedNodes[e.checkedNodes.length - 1].name
        }

      } else {
        this.zuoxi_user = {
          id: '',
          name: ''
        }
      }
      this.show_select_dia = false;
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getCrmDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    showMemberList() {
      this.show_select_dia = true;
    },
    // 获取外显号码列表
    getShowTelNumber(phone = '') {
      if (phone != '' && phone.length !== 11) return
      // this.telNumberLoading = true
      this.$http.getShowTelNumber({
        phone,
        status: 1,
        is_disable: 0,
        per_page: 1000
      }).then(res => {
        if (res.status == 200) {
          this.show_number_list = res.data.data
        }
        // this.telNumberLoading = false
      }).catch(() => {
        // this.telNumberLoading = false
      })
    },
    // 弹框外显号码
    showOutTel() {
      this.show_out_tel = true
    },
    // 获取坐席套餐列表
    getShowSeats() {
      this.$http.getAvailableList().then(res => {
        if (res.status == 200) {
          this.showSeats = res.data.status
          this.Seats_list = res.data.list
        }
      })
      // show_Seats_list
    },
    handlechange(event) {
      if (this.zuoxiTitle == "添加坐席") {
        if (Array.isArray(event) && event.length === 0) {
          this.show = false;
          this.gridData = []
        } else {
          this.show = true;
          // let event = event.join(",")
          const currentPhoneNumbers = event.map(phone => (phone + '').trim());
          let ids = []
          currentPhoneNumbers.forEach(phoneNumber => {
            this.show_number_list.forEach(item => {
              if (item.id == phoneNumber) {
                ids.push({ show_phone: item.phone, show_id: item.id, is_default: item.is_default, sort: 0 })
              }
            });
          });
          this.gridData = ids;
        }
      }
      if (this.zuoxiTitle == "编辑坐席") {

        // this.gridData = event
        if (Array.isArray(event) && event.length === 0) {
          this.show = false;
          this.gridData = []
        } else {
          this.show = true;
          const currentPhone = event.map(phone => (phone + '').trim());
          let ids = []
          currentPhone.map(phoneNumber => {
            this.show_number_list.map(item => {
              if (item.id == phoneNumber) {
                this.$set(item, 'sort', item.sort || 0)
                ids.push(item)
              }
            });
          });
          this.gridDatacopy.map(it => {
            ids.map(item => {
              item.show_phone = item.phone
              if (it.show_id == item.id) {
                this.$set(item, 'is_default', it.is_default)
                this.$set(item, 'sort', it.sort)
              }
              return item
            })
            return it
          })
          this.gridData = ids;
        }
      }
      this.$forceUpdate()
    },
    // 获取外呼数据统计
    getOutboundStatistics() {
      this.AIborot_loading = true
      this.$http.getOutboundStatistics().then(res => {
        if (res.status == 200) {
          this.OutboundData = res.data.data
          this.AIborot_loading = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}

.mr10 {
  margin-right: 10px;
}

.w-300 {
  width: 300px;
}

.check-box {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  width: fit-content;
  cursor: pointer;
  margin-left: 20px;

  .check-item {
    padding: 8px 18px;
    color: #607080;
    font-size: 16px;

    &.isactive {
      color: #fff;
      background: #0083ff;
    }
  }
}

.tabs {
  margin-left: 44px;
}

.w_300 {
  width: 300px;
}

.tab_content {
  padding: 40px 44px;
}

.btns {
  padding-left: 44px;
}

.table_oper {
  padding: 16px 0;
}

.balance {
  padding: 0px 44px;

  .sms-border {
    margin-top: 10px;
    padding: 24px 0;
    border-bottom: 1px solid #eee;
    margin-left: -24px;
    margin-right: -24px;

    .sms-item {
      padding: 12px 0;
      flex: 1;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-right: 1px solid #eee;
      cursor: pointer;

      &:last-child {
        border-right: none;
      }

      .max-w-160 {
        max-width: 160px;
      }

      .w-160 {
        width: 160px;
      }

      .t {
        color: #0083ff;
        font-size: 32px;
      }

      .b {
        font-size: 14px;
        color: #768196;
      }

      .btn {
        padding: 9px 0;
        color: #fff;
        text-align: center;
        border-radius: 5px;
      }

      .b1 {
        margin-bottom: 14px;
        background: #17b63a;
      }

      .b2 {
        background: #fd7979;
      }
    }
  }
}



::v-deep.drawers-box {
  .el-drawer__container {
    .el-drawer {
      overflow: auto !important;
    }
  }
}
</style>