<template>
  <!-- 审批 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm">
          <div class="table-top-box div row align-center">
            <div class="t-t-b-left div row flex-1">
              <myCheck
                :type="time_list"
                label="name"
                value="id"
                :is_tabs="is_tabs"
                @onClick="onClickTabs"
              ></myCheck>
            </div>
            <div class="t-t-b-right div row">
              <el-input
                style="width: 190px; margin-right: 10px"
                v-model="params.number"
                placeholder="编号检索"
                @keyup.enter.native="handleSearch"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleSearch"
                ></el-button>
              </el-input>
              <el-popover
                placement="bottom"
                width="500px"
                v-model="show_comm_search"
              >
                <div>
                  <div>搜索</div>
                  <div
                    class="inps flex-row align-center"
                    style="margin: 10px 0"
                  >
                    <el-input
                      placeholder="请输入小区名称"
                      v-model="params.community_name"
                      style="margin-right: 10px; width: 180px"
                    ></el-input>
                  </div>
                  <div class="btns" style="text-align: right">
                    <el-button @click="resetSearch('community_name')"
                      >重置</el-button
                    >
                    <el-button type="primary" @click="handleSearch"
                      >确定</el-button
                    >
                  </div>
                </div>

                <div
                  class="search_loudong flex-row align-center"
                  slot="reference"
                >
                  <div class="seach_value">小区</div>
                  <div
                    class="sanjiao"
                    :class="{ transt: show_comm_search }"
                  ></div>
                </div>
              </el-popover>

              <el-popover
                placement="bottom"
                width="200px"
                v-model="show_department_search"
              >
                <div>
                  <el-cascader
                    placeholder="请选择部门"
                    style="width: 210px; margin-right: 16px"
                    v-model="params.department_id"
                    clearable
                    :show-all-levels="false"
                    :options="department_list"
                    :props="{
                      value: 'id',
                      label: 'name',
                      children: 'subs',
                      emitPath: false,
                    }"
                    @change="handleSearch"
                  ></el-cascader>
                </div>
                <div style="margin-top: 10px">
                  <el-input
                    style="width: 210px"
                    v-model="params.user_name"
                    placeholder="请输入人员"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="handleSearch"
                    ></el-button>
                  </el-input>
                </div>

                <div
                  class="search_loudong flex-row align-center"
                  slot="reference"
                  style="padding: 0 18px; margin-left: 10px; margin-right: 5px"
                >
                  <div class="seach_value flex-row align-center">
                    {{ department_name }}
                    <div
                      class="sanjiao"
                      :class="{ transt: show_department_search }"
                    ></div>
                  </div>
                </div>
              </el-popover>
            </div>
          </div>
          <div class="labels" style="padding: 15px 0">
            <myLabel :arr="statusList" @onClick="onClickTime"></myLabel>
          </div>
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            class="house_table"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="ID" prop="id" width="55"> </el-table-column>
            <el-table-column label="审批编号" v-slot="{ row }">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="row.number"
              >
                <el-button
                  size="mini"
                  slot="reference"
                  @click="copy(row.number)"
                  >复制编号</el-button
                >
              </el-popover>
            </el-table-column>
            <el-table-column label="发起人" v-slot="{ row }">
              <span>{{ row.applicant.user_name }}</span>
            </el-table-column>
            <el-table-column label="审批类型" v-slot="{ row }">
              <span>{{ row.cat_name.title }}</span>
            </el-table-column>
            <el-table-column label="房源" v-slot="{ row }">
              <span class="house_name" @click="goHouse(row)">{{
                row.house
              }}</span>
            </el-table-column>
            <el-table-column label="审批状态" v-slot="{ row }">
              <el-tag :type="typeName(row.status)">{{
                statusName(row.status)
              }}</el-tag>
              <!-- <el-tag type="warning" v-if="row.status==0">未审核</el-tag>
              <el-tag type="success" v-if="row.status==1">通过审核</el-tag>
              <el-tag type="danger" v-if="row.status==2">审核已拒绝</el-tag> -->
            </el-table-column>

            <el-table-column label="发起时间" prop="ctime"> </el-table-column>
            <!-- ctime -->
            <el-table-column label="操作" fixed="right" v-slot="{ row }">
              <el-link type="primary" @click="onClickDialogDetail(row)"
                >详情</el-link
              >
              <el-link
                style="margin-left: 10px"
                type="warning"
                @click="toMessage(row)"
                >留言</el-link
              >
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog width="374px" :visible.sync="is_dialog_content" title="拒绝理由">
      <div class="title-lable">内容填写：</div>
      <el-input
        type="textarea"
        :rows="10"
        placeholder="请输入"
        v-model="refuse_form.refuse_reason"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_dialog_content = false">取 消</el-button>
        <el-button type="primary" @click="onCreateData">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="pre_img" width="1000px" title="预览">
      <div class="pre_img">
        <img :src="imgUrl" alt="" />
      </div>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="is_dialog_detail" title="详情">
      <div class="audit_detail">
        <div class="label flex-row align-center">
          <span class="name"> 发起人:</span>
          <span class="value">
            {{ formData.applicant.user_name }} (
            {{ formData.applicant.department }})</span
          >
        </div>
        <div class="label flex-row align-center">
          <div class="flex-row align-center flex-1">
            <span class="name"> 房源名称:</span>
            <span class="value"> {{ formData.house }}</span>
          </div>
          <el-button
            type="primary"
            v-if="formData.house"
            @click="goHouse({ sys_hid: formData.sys_hid })"
          >
            查看
          </el-button>
        </div>
        <div
          class="label flex-row align-center"
          v-for="(item, index) in formData.template"
          :key="index"
        >
          <span class="name"> {{ item.label }}: </span>
          <span class="value">{{ item.value }}</span>
        </div>

        <div class="label flex-row align-center">
          <span class="name"> 申请时间:</span>
          <span class="value">{{ formData.ctime }}</span>
          <!-- {{formData.approver_uid.user_name}} -->
        </div>
        <div
          class="label flex-row align-center"
          v-if="formData.status == 0 && formData.identify == 4"
        >
          <span class="name"> 审批备注:</span>
          <el-input v-model="refuse_form.remarks"></el-input>
          <!-- <span class="value">{{formData.remarks}}</span> -->
          <!-- {{formData.approver_uid.user_name}} -->
        </div>
        <div class="label flex-row align-center" v-else>
          <span class="name"> 审批备注:</span>
          <span class="value">{{ formData.remarks }}</span>
          <!-- {{formData.approver_uid.user_name}} -->
        </div>
        <div class="label">
          <div class="label_title">审批凭证</div>
          <diV
            class="attachment"
            v-if="formData.status == 0 && formData.identify == 4"
          >
            <el-upload
              multiple
              action="/api/common/file/upload/admin?category=6"
              :headers="upload_headers"
              :limit="10"
              :file-list="attenchmentList"
              :show-file-list="true"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif"
              list-type="picture-card"
              :on-success="
                (response, file, fileList) => {
                  onUploadAttechmentSuccess({
                    response,
                    file,
                    fileList,
                  });
                }
              "
            >
              <i class="el-icon-picture-outline"></i>
            </el-upload>
          </diV>
          <div class="audit_imgs flex-row" v-else>
            <div
              class="audit_img"
              v-for="(item, index) in formData.attachment"
              @click="preview_img(item)"
              :key="index"
            >
              <img :src="item" alt="" />
            </div>
          </div>

          <!-- {{formData.approver_uid.user_name}} -->
        </div>

        <div class="label flex-row align-center">
          <span class="name"> 审批状态 </span>
          <span class="value success">{{ statusName(formData.status) }}</span>
        </div>
        <template v-if="formData.status > 0">
          <div class="label flex-row align-center">
            <span class="name"> 审批人:</span>
            <span class="value">{{
              formData.real_approver_uid.user_name
            }}</span>
          </div>
          <div class="label flex-row">
            <span class="name"> 审批意见：</span>
            <span class="value"> {{ formData.sys_memo }}</span>
          </div>
          <div class="label flex-row align-center">
            <span class="name"> 审批时间：</span>
            <span class="value"> {{ formData.real_approver_time }}</span>
          </div>
        </template>
        <div
          class="label multers flex-row align-center"
          v-if="formData.status == 0"
        >
          <span class="name"> 审批人:</span>
          <span
            class="value"
            v-for="audit in formData.approver_uid"
            :key="audit.id"
          >
            {{ audit.user_name }}
          </span>
          <!-- {{formData.approver_uid.user_name}} -->
        </div>
        <div class="label multers flex-row align-center" v-if="hasReci">
          <span class="name"> 抄送人:</span>
          <span
            class="value"
            v-for="audit in formData.recipients"
            :key="audit.id"
          >
            {{ audit.user_name }}
          </span>
        </div>

        <template v-if="formData.status == 0">
          <div
            class="label flex-row align-center"
            v-if="!hasReci && formData.identify < 3"
          >
            <span class="name"> 抄送:</span>
            <el-select v-model="refuse_form.recipients" multiple>
              <el-option
                v-for="item in recipientsList"
                :key="item.values"
                :value="item.values"
                :label="item.name"
              >
              </el-option>
            </el-select>
          </div>

          <div class="label">
            {{
              formData.status == 0 && formData.identify == 4
                ? "补充信息"
                : "审批意见(留言内容)："
            }}
          </div>
          <el-input
            type="textarea"
            placeholder="请输入"
            :rows="8"
            v-model="refuse_form.sys_memo"
          ></el-input>
        </template>
        <span slot="footer" class="dialog-footer-detail">
          <template v-if="formData.status == 0">
            <el-button type="warning" @click="onCreateMessageAudit(3)">
              {{
                formData.status == 0 && formData.identify == 4
                  ? "补充信息"
                  : "留 言"
              }}</el-button
            >
            <el-button
              type="primary"
              v-if="formData.identify < 3"
              v-loading="is_button_loading"
              :disabled="is_button_loading"
              @click="onCreateDataAudit(1)"
              >同 意</el-button
            >
            <el-button
              v-if="formData.identify < 3"
              v-loading="is_button_loading1"
              type="danger"
              @click="onCreateDataAudit(2)"
              >拒 绝</el-button
            >
          </template>
          <el-button
            type="warning"
            @click="chexiao()"
            v-if="formData.identify == 4"
            >撤 销</el-button
          >
        </span>
      </div>
    </el-dialog>

    <el-dialog
      width="800px"
      :visible.sync="message_table_show"
      :title="messageTitle"
    >
      <div>
        <el-table
          :data="messageTableData"
          class="house_table"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <el-table-column label="留言者" v-slot="{ row }">
            <span>{{ row.crm_user && row.crm_user.user_name }}</span>
          </el-table-column>
          <el-table-column label="身份" prop="type"> </el-table-column>
          <el-table-column label="留言内容" prop="content"></el-table-column>
          <el-table-column label="留言时间" prop="ctime"> </el-table-column>
        </el-table>
        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="messageTotal"
          :page-size="message_params.per_page"
          :current-page="message_params.page"
          @current-change="onMessagePageChange"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import myCheck from "../crm/components/my_check";
import myLabel from "../crm/components/my_label";
import config from "@/utils/config.js";
export default {
  name: "house_audit",
  components: {
    myCheck,
    myLabel
  },
  data() {
    return {
      recipientsList: [],
      params: {
        page: 1,
        total: 0,
        per_page: 10,
        cat_id: '',
      },
      is_tabs: '',
      time_list: [

      ],
      time_value: "",
      tableData: [],
      is_table_loading: false,
      is_dialog_content: false,
      content: "",
      is_dialog_detail: false,
      audit_content: "",
      refuse_form: {
        id: "",
        sys_memo: "",
        recipients: []
      },
      tableLabel: [
      ],
      attenchmentList: [],
      commonLabel: [
        {
          label: "房源名称",

          prop: 'house',
        },
        {
          label: "审批状态",

          prop: 'status',
        },
        {
          label: "审批人",

          prop: 'approver',
        },
        {
          label: '备注',
          prop: "remarks"
        },
        {
          label: '操作',
          prop: "oper"
        }
      ],
      is_dialog_content_title: "--",
      is_button_loading: false,
      is_button_loading1: false,
      statusList: [


      ],
      upload_headers: {
        Authorization: config.TOKEN,
      },
      pre_img: false,
      imgUrl: '',
      formData: {
        template: {},
        real_approver_uid: {

        },
        applicant: {},

      },
      hasReci: false,
      show_department_search: false,
      show_comm_search: false,
      department_name: "部门/人员",
      department_list: [],
      message_table_show: false,
      currentAudit: {},
      messageTitle: "",
      message_params: {
        page: 1,
        per_page: 10
      },
      messageTableData: [],
      messageTotal: 0
    };
  },
  mounted() {
    this.getAuditTypeList()
    this.getCrmDepartmentList()
    this.getStatusList()

  },
  filters: {
    filterStatus(val) {
      let name = ''
      switch (Number(val)) {
        case 0:
          name = '审核中'
          break;
        case 1:
          name = '审核通过'
          break;
        case 2:
          name = '审核拒绝'
          break;
        default:
          break;
      }
      return name
    }
  },
  computed: {
    statusLists() {

      return this.statusList.filter(item => !item.id)
    },
  },
  methods: {
    toMessage(row) {
      this.currentAudit = row
      this.getMessageList(row.id)
    },
    getMessageList(id) {
      this.$ajax.house.getAuditMessage(id, this.message_params).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.messageTitle = `${this.currentAudit.applicant.user_name}发起的 ${this.currentAudit.house} ${this.currentAudit.cat_name.title} 审批留言列表`
          this.messageTableData = res.data.data
          this.messageTotal = res.data.total
          this.message_table_show = true
        }

      })
    },
    onMessagePageChange(e) {
      this.message_params.page = e
      this.getMessageList()
    },
    chexiao() {
      this.$confirm("此操作将撤销本次提交, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$ajax.house.chexiaoAudit(this.refuse_form.id).then(res => {
          if (res.status == 200) {
            this.$message.success("操作成功")
            this.getDataList()
            this.is_dialog_detail = false
          }
        })
      }).catch(() => {
        this.$message.info("已取消")
      })

    },
    getStatusList() {
      this.getDataList()
      this.$ajax.house.getAuditStatusList().then(res => {
        if (res.status == 200) {
          this.statusList = res.data
          this.statusList.unshift({ name: '全部', values: "", id: -1 })
          // this.statusList.push({name:'抄送我',values:1111,id:1111})

          this.statusList.push({ name: '抄送我', values: 'is_owner-1', id: 1111 }, { name: '我的申请', values: "is_my-1", id: 2222 })
        }
      })
    },
    typeName(id) {
      console.log(id);
      return "success"

    },
    copy(con) {
      console.log(con);
      this.$onCopyValue(con);
    },
    statusName(id) {

      let curr = this.statusLists.find(item => item.values == id)
      if (curr) {
        return curr.name
      }
      return ''
    },
    onCreateMessageAudit() {

      let params = {
        id: this.refuse_form.id,
        sys_memo: this.refuse_form.sys_memo,
        recipients: this.refuse_form.recipients && this.refuse_form.recipients.length ? this.refuse_form.recipients.join(",") : ""
      }

      let imgArr = []
      if (this.formData.status == 0 && this.formData.identify == 4) {
        this.attenchmentList.map(item => {
          imgArr.push(item.url)
        })
        params.attachment = imgArr.join(",")
        params.remarks = this.refuse_form.remarks
      }



      this.$ajax.house.sendAuditMessage(params).then(res => {
        console.log(res);
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
          this.is_dialog_detail = false;
        }
      })
    },
    getAuditTypeList() {
      this.$ajax.house.getAuditTypeList(2).then(res => {
        if (res.status == 200) {
          this.time_list = res.data.map(item => {
            item.id = item.values
            return item
          })
          this.time_list.unshift({
            id: "",
            name: '全部'
          })

        }
      })
    },
    getCrmDepartmentList() {
      this.$http.getCrmDepartmentList().then((res) => {
        if (res.status === 200) {
          this.department_list = res.data;
        }
      });
    },
    handleSearch() {
      this.params.page = 1
      this.getDataList()
    },
    resetSearch(type) {
      this.params[type] = ''
      this.show_comm_search = false
      this.show_department_search = false
    },
    onUploadAttechmentSuccess(options = {}) {
      let { response } = options;
      this.attenchmentList.push(response)
    },
    getModelList(id) {
      this.$ajax.house.getModalList(id).then(res => {
        console.log(res);
        if (res.data.length) {
          this.tableLabel = res.data.map(item => {
            item.label = item.title
            item.prop = item.name
            return item
          }).concat(this.commonLabel)
        } else {
          this.tableLabel = this.commonLabel
        }
        this.getDataList();

      })


    },
    goHouse(row) {
      this.is_dialog_detail = false;
      this.$goPath("/house_detail?id=" + row.sys_hid)
    },
    getDataList() {
      this.is_table_loading = true;
      let params = Object.assign({}, this.params)

      // for (const key in params) {
      //  if (key.split("-").length>1){
      //   let name = key.split("_")[0]
      //   let value = key.split("-")[1]
      //   params[name] =value
      //   delete params[key]
      //  }
      // }
      // if (params.status ==1111){
      //   params.is_owner =1
      //   params.is_my =0
      //   params.status =''
      // }else if (params.status ==2222){
      //   params.is_my =1
      //   params.is_owner =0
      //   params.status =''
      // }else{
      //   params.is_owner =''

      // }
      this.$ajax.house.getHouseAuditList(2, { params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        });
    },
    onClickTime(e) {
      if (e.id == 1111) {
        this.params.is_my = 0
        this.params.status = ''
      }
      if (e.id == 2222) {
        this.params.is_owner = 0
        this.params.status = ''
      }
      if (e.values === '') {
        this.params.is_my = 0
        this.params.is_owner = 0
      }
      if ((e.values + '').split("-").length > 1) {
        let value = e.values.split("-")[0]
        let name = e.values.split("-")[1]
        this.params[value] = name
      } else {

        this.params.status = e.values
      }
      this.params.page = 1
      this.getDataList()

    },
    onClickDialogAgree(e) {
      // this.$goPath(`/crm_customer_audit_detail?id=${e.id}`);
      this.$confirm("是否同意审批内容", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.onAgreeAuditData({ id: e.id }).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onClickDialogDetail(e) {
      this.refuse_form = {
        id: "",
        sys_memo: "",
        recipients: [],
        remarks: "",
        refuse_reason: "",
        status: ""
      };
      this.attenchmentList = [];
      this.audit_content = "";

      this.$ajax.house.getHouseAuditDetail(e.id).then(res => {
        if (res.status == 200) {
          this.formData = res.data
          if (this.formData.status == 0 && this.formData.identify == 4) {
            res.data.attachment.map(item => {
              let obj = {
                name: "",
                url: item
              }
              this.attenchmentList.push(obj)
            })
            this.refuse_form.remarks = this.formData.remarks
          }

          // this.attenchmentList = res.data.attachment
          if (this.formData.recipients && this.formData.recipients.length) {
            this.hasReci = true
          } else {
            if (this.formData.status == 0) {
              this.getRecipientsList()
            }
            this.hasReci = false
          }


          this.is_dialog_detail = true;
          this.refuse_form.id = e.id;
        }

      })
    },
    getRecipientsList() {
      this.$ajax.house.getRecipientsList().then(res => {
        console.log(res);
        this.recipientsList = res.data
      })
    },
    onCreateDataRefuse() { },

    onCreateDataAudit(type) {
      let text = '是否同意审批内容'
      if (type == 2) {
        text = '是否拒绝审批'
      }
      this.refuse_form.status = type
      let params = Object.assign({}, this.refuse_form)
      if (params.recipients.length) {
        params.recipients = params.recipients.join(',')
      } else {
        params.recipients = ''
      }
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (type == 1) {
            this.is_button_loading = true;
          } else {
            this.is_button_loading1 = true;
          }

          this.$ajax.house.changeAuditStatus(params)
            .then((res) => {
              if (type == 1) {
                this.is_button_loading = false;
              } else {
                this.is_button_loading1 = false;
              }

              if (res.status === 200) {
                this.$message.success("操作成功");
                this.is_dialog_detail = false;
                this.getDataList();
              }
            }).catch(() => {
              if (type == 1) {
                this.is_button_loading = false;
              } else {
                this.is_button_loading1 = false;
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onClickDialog(e) {
      this.refuse_form.id = e.id;
      this.is_dialog_content = true;
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onClickTabs(e) {
      this.is_tabs = e.id;
      this.params.cat_id = e.id;
      this.params.page = 1;
      this.getDataList()
    },
    preview_img(img) {
      this.pre_img = true
      this.imgUrl = img
    },
    onCreateData() {
      if (!this.refuse_form.refuse_reason) {
        this.$message.error("请输入内容");
        return;
      }
      this.$http.onRefuseAuditData(this.refuse_form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
          // this.is_dialog_content = false;
          this.is_dialog_detail = false;
        }
      });
    },
  },
};
  </script>
  
  <style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .bottom-border {
    justify-content: flex-start;
    align-items: center;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
.dialog-footer-detail {
  width: 100%;
  margin-top: 10px;
  display: inline-block;
  text-align: center;
}
.audit {
  font-size: 14px;
  color: #fff;
  padding: 3px 10px;
  border-radius: 4px;
  background: #3172f6;
  &.audit1 {
    background: #00b432;
  }
  &.audit2 {
    background: #fa5c5c;
  }
  &.audit3 {
    background: #ff8a00;
  }
}
.title-lable {
  font-size: 14px;
  color: #8a929f;
  margin-bottom: 8px;
}
.pre_img {
  width: 100%;
  height: 100%;
  text-align: center;
  overflow: auto;
}
.audit_detail {
  max-height: 70vh;
  overflow-y: auto;
}
.label {
  margin-bottom: 12px;
  .label_title {
    margin-bottom: 10px;
  }
  &.multers {
    .value {
      margin-right: 10px;
    }
  }
  .name {
    display: inline-block;
    min-width: 70px;
  }
  .value {
    &.success {
      padding: 2px 5px;
      border-radius: 3px;
      background: #0dbc79;
      color: #fff;
    }
    &.danger {
      padding: 2px 5px;
      background: #fa5c5c;
      border-radius: 3px;
      color: #fff;
    }
    &.warning {
      padding: 2px 5px;
      border-radius: 3px;
      background: #ff8a00;
      color: #fff;
    }
  }
  .audit_imgs {
    flex-wrap: wrap;
    .audit_img {
      width: 105px;
      height: 105px;
      margin-right: 10px;
      margin-bottom: 10px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
.house_table {
  .house_name {
    color: #3172f6;
    cursor: pointer;
  }
}

.search_loudong {
  background: #f8f8f8;
  height: 41px;
  padding: 0 11px;
  margin-left: 5px;
  font-size: 13px;
  color: #999;
  cursor: pointer;
  .seach_value {
    font-size: 14px;
    color: #999;
  }
  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #999;
    margin-left: 5px;
    margin-top: 5px;
    &.transt {
      border-bottom-color: #999;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
</style>
  