// /**
//  * 登录
//  */

/* eslint-disable */
import axios from "axios";
// import router from "../router";
import { Message } from "element-ui";
import router from "../router";
import store from "../store/index";
import { showLoading, hideLoading } from "./my_loading";
import {
  goPath,
} from "@/utils/tools";
class UserCenter {
  constructor() {
    this.$http1 = axios.create({
      baseURL: "/api",
      timeout: 10000,
    });

    // 请求拦截
    this.$http1.interceptors.request.use(
      function (config) {
        // showLoading();
        let auth_way = localStorage.getItem("auth_way"); // 测试来源
        config.headers["authway"] = auth_way ? auth_way : 0;
        // 报备：1 、自建：2、t+：3
        if (localStorage.getItem("map_token")) {
          config.headers["Authorization"] =
            "Bearer " + localStorage.getItem("map_token");
        }
        // config.headers["Content-Type"] = "application/json";
        //请求头内容
        config.headers["Content-Type"] = "application/json";
        // 导出添加一分钟超时限制

        // 请求方式 post/get
        if (config.method == "post") {
          config.data = {
            ...config.data,
            website_id: localStorage.getItem("web_id"),
          };
        } else if (config.method == "get") {
          config.params = {
            website_id: localStorage.getItem("web_id"),
            ...config.params,
          };
        }
        return config;
      },
      function (error) {
        return Promise.reject(error);
      }
    );

    // 响应拦截
    this.$http1.interceptors.response.use(
      (response) => {
        // hideLoading();
        return response;
      },
      (error) => {
        // console.log(error, "12345");
        let exUrl = ['/admin/customer/create']  // 弹出提示的接口
        // hideLoading();
        // console.log(error.response.data);
        if (error.response.status === undefined) {
          Message.error("请检查网络连接");
        }
        if (error.response.status === 422) {
          // 判断微信小程序授权
          var str = error.response.config.url;

          if (error.response.data.message) {
            if (error.response.data.errors) {
              let promise = Promise.resolve();
              for (var key in error.response.data.errors) {
                error.response.data.errors[key].forEach((item) => {
                  promise = promise.then(() => {
                    return new Promise((resolve) => {
                      resolve(item);
                    });
                  });
                  promise.then((err) => {

                    Message.error(err);
                  });
                });
              }
            } else {
              if (!exUrl.includes(error.config.url)) {
                Message.error(error.response.data.message);
              }

            }
          } else {
            Message.error("请求错误");
          }
          // return Promise.reject(error);
          return error.response;
        }
        if (error.response.status === 401) {
          if (!store.state.hasMessage) {
            store.state.hasMessage = true
            Message.error(error.response.data.message || 'token失效');
            setTimeout(() => {
              store.state.hasMessage = false
            }, 800);
          }

        }
        if (error.response.status === 500) {
          Message.error(
            "网络连接错误，请检查网络连接： " + error.response.data.message
          );
        }
        if (error.response.status === 404) {
          Message.error("请求出错404！");
        }
        // if (JSON.stringify(error).indexOf("timeout") != -1) {
        //   Message.error("请求超时请刷新重试");
        // var newHttp = new Promise(function(resolve) {
        //   resolve();
        // })`newHttp实例执行完成后会再次执行`;
        // // 返回一个promise实例，同时重新发起请求，config请求配置，包扩请求头和请求参数
        // return newHttp.then(function() {
        //   return axios(config);
        // });
        // }


        return Promise.reject(error);
      }
    );
  }

  /**
   * 查询地图
   */
  getList(id, params) {
    return this.$http1.get(`/client/map_plugin/get_maps/${id}`, { params });
  }
  saveSign(params) {
    return this.$http1.post(`/client/map_plugin/map_click`, params)
  }
  receiveList(id) {
    return this.$http1.get(`/client/map_plugin/is_allow_receive/${id}`,)
  }
  redMoveList(id) {
    return this.$http1.get(`/client/map_plugin/get_share_info/${id}`,)
  }

  //获取地图插件详情
  getMapPluginDetail(id){
    return this.$http1.get(`/client/map_plugin/get_maps_v2/${id}`)
  }
  //检查地图链接是否失效
  checkMapUrlexpire(code){
    return this.$http1.get(`/client/map_plugin/check_expire/${code}`)
  }
  //获取分享者信息
  getShareUserInfo(uid){
    return this.$http1.get(`/client/map_plugin/get_share_user/${uid}`)
  }
  //获取微信js_sdk配置
  getWxConfig(id){
      return this.$http1.get(`/common/config/query/wx_public_web_login/client/${id}`);
  }

}

export default new UserCenter();
