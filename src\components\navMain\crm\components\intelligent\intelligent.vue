
<template>
  <div class="content">
    <el-button class="tolead-box tolead-box-add" size="small" type="primary" @click="newrules">分配规则</el-button>
    <el-tabs v-model="activeName1" @tab-click="handleClickLog" v-if="isShow">
      <el-tab-pane v-for="(item, index) in tabs_list_new" :key="index" :label="item.label" :name="item.name">
      </el-tab-pane>
    </el-tabs>
    <div class="seach_all">
      <div>
        <myLabeldial class="label-all-box" style="margin-bottom: 15px;" :arr="time_listone" :activeIndex="activeIndexdial"
          @onClick="onClickType($event, 4)" v-if="log_params.type == 2">
        </myLabeldial>
        <myLabeladd class="label-all-box" style="margin-bottom: 15px;" :arr="time_list" :activeIndex="activeIndexadd"
          @onClick="onClickType($event, 4)" v-if="log_params.type == 3">
        </myLabeladd>
        <myLabeladdtwo class="label-all-box" style="margin-bottom: 15px;" :arr="time_list"
          :activeIndex="activeIndexaddtwo" @onClick="onClickType($event, 4)" v-if="log_params.type == 1">
        </myLabeladdtwo>
        <div v-if="this.getlog_input" style="margin: 15px 0px 0px 386px; display: flex;">
          <el-date-picker style="width: 220px" v-model="p_time" type="datetimerange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" size="mini"
            @change="changeTimeRange">
          </el-date-picker>
        </div>
        <myLabelcall class="label-all-box" :arr="second_list" :activeIndex="activeIndexcall"
          @onClick="secondType($event, 4)" v-if="log_params.type == 2">
        </myLabelcall>
        <div v-if="this.times_input" style="margin: 15px 0px 0px 399px; display: flex;">
          <el-input v-model="timesone" type='number' size="mini" style="width: 90px;height: 20px;">
          </el-input>
          <div style="margin:2px 5px 0px 5px">至</div>
          <el-input size="mini" type='number' v-model="timestwo" style="width: 140px;">
            <el-button slot="append" icon="el-icon-search" @click="inquire"></el-button>
          </el-input>
        </div>
        <myLabellabel class="label-all-box" style="margin-top: 15px;" :arr="options_list" :activeIndex="activeIndexlabel"
          @onClick="optionsClick($event, 4)" v-if="log_params.type == 2">
        </myLabellabel>
        <myLabelIndex class="label-all-box" style="margin-bottom: 15px;" :arr="notimport_list" :activeIndex="activeIndex"
          @onClick="notimport($event, 4)" v-if="log_params.type == 3">
        </myLabelIndex>
        <myLabelthred :arr="call_status_list" :activeIndex="activethred" @onClick="callstatus($event, 4)"
          v-if="log_params.type == 3">
        </myLabelthred>
      </div>
      <div class="seach_all_bottom" v-if="log_params.type == 2">
        <!-- <div class="t-t-b-left div b-tabs row" style="width: 100%; height: 30px">
          <el-input :placeholder="placeholder" v-model="select_params.keywords" class="input-with-select"
            style="width: 300px" clearable @keyup.enter.native="handleKeywordSearch">
            <el-select v-model="select_params.type" @change="changeSelectParams" slot="prepend" placeholder="请选择"
              style="width: 100px">
              <el-option label="手机号码" :value="1"></el-option>
              <el-option label="客户姓名" :value="2"></el-option>
            </el-select>
          </el-input>
        </div> -->
        <el-input v-model="log_paramstwo.name" placeholder="请输入客户名称" style="width: 180px;">
        </el-input>
        <el-input v-model="log_paramstwo.phone" placeholder="请输入手机号码" style="width: 180px;margin:0px 10px;">
        </el-input>
        <el-button class="seach_button" size="medium " type="primary" @click="handleKeywordSearch">搜索</el-button>
        <!-- <el-button class="tolead-box" size="small" type="primary" @click="tolead">导入</el-button> -->
        <el-button type="primary" @click="is_upload_dialog = true" style="height: 40px;">导入</el-button>
        <input type="file" ref="file_p" accept=".xls,.doc,.txt,.xlsx" style="display: none"
          v-on:change="handleFileUpload($event)" />
      </div>
      <div class="seach_all_bottom" v-if="log_params.type == 3">
        <!-- <div class="t-t-b-left div b-tabs row" style="width: 100%; height: 30px">
          <el-input :placeholder="placeholder" v-model="select_params.keywords" class="input-with-select"
            style="width: 300px" clearable @keyup.enter.native="handleKeywordSearch">
            <el-select v-model="select_params.type" @change="changeSelectParams" slot="prepend" placeholder="请选择"
              style="width: 100px">
              <el-option label="手机号码" :value="1"></el-option>
              <el-option label="客户姓名" :value="2"></el-option>
            </el-select>
          </el-input>
        </div> -->
        <el-input v-model="log_paramsthree.name" placeholder="请输入客户名称" style="width: 180px;">
        </el-input>
        <el-input v-model="log_paramsthree.phone" placeholder="请输入手机号码" style="width: 180px;margin:0px 10px;">
        </el-input>
        <el-button class="seach_button" size="medium " type="primary" @click="handleKeywordSearch">搜索</el-button>
        <el-button type="primary" @click="is_upload_dialog = true" style="height: 40px;">导入</el-button>
        <input type="file" ref="file_p" accept=".xls,.doc,.txt,.xlsx" style="display: none"
          v-on:change="handleFileUpload($event)" />
      </div>
      <div class="seach_all_bottom" v-if="log_params.type == 1">
        <el-button type="primary" @click="is_upload_dialog = true" style="height: 40px;">导入</el-button>
        <input type="file" ref="file_p" accept=".xls,.doc,.txt,.xlsx" style="display: none"
          v-on:change="handleFileUpload($event)" />
      </div>
    </div>
    <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="robot_list" highlight-current-row style="width: 100%"
      :show-overflow-tooltip="true" v-if="log_params.type == 2">
      <!-- 列表下拉 -->
      <el-table-column prop="time_total" label="客户名称" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <template>
        <el-table-column label="手机号" align="center" width="160">
          <template slot-scope="scope">
            {{ scope.row.phone }}
          </template>
        </el-table-column>
      </template>
      <el-table-column prop="time_total" label="通话时长" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.duration }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="拨打时间" align="center" width="180">
        <template slot-scope="scope">
          {{ scope.row.started_at }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="客户意向" align="center" width="100">
        <template slot-scope="scope">
          <!-- {{ scope.row.label_type == 'D' ? '拒绝' : scope.row.label_type == 'C' ? '再联系' : scope.row.label_type == 'B' ?
            '一般意向' : scope.row.label_type == 'A' ? '高意向' : '--' }} -->
          {{ scope.row.label_type }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="客户关注点" align="center">
        <template slot-scope="scope">
          <!-- <div v-for="(index, item) in scope.row.focus" :key="item">
            <span>
              {{ index }}
            </span>
          </div> -->
          <div :title="scope.row.focus" class="scope-focus-box">{{ scope.row.focus }}</div>

        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="录音路径" align="center">
        <template slot-scope="scope">
          {{ scope.row.record_url }}
        </template>
        <template slot-scope="scope">
          <myaudio :theUrl="scope.row.record_url" :client_id="scope.row.client_id" ></myaudio>
        </template>
      </el-table-column>
    </el-table>

    <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="log_list" highlight-current-row style="width: 100%" v-if="log_params.type == 1">
      <!-- 列表下拉 -->
      <el-table-column prop="time_total" label="操作成员" align="center">
        <template slot-scope="scope">
          {{ scope.row.admin_name }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="添加时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="失败个数" align="center">
        <template slot-scope="scope">
          {{ scope.row.import_failed }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="成功个数" align="center">
        <template slot-scope="scope">
          {{ scope.row.import_success }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="号码总量" align="center">
        <template slot-scope="scope">
          {{ scope.row.phone_count }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="文件大小" align="center">
        <template slot-scope="scope">
          {{ scope.row.file_size }}KB
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="文件名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.file_name }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="操作" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="onEditData(scope.row)">详情</el-link>
          <el-link style="margin-left:8px" type="primary" @click="mydownload(scope.row)">下载</el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-table v-loading="tel_table_loading" border :header-cell-style="{ background: '#EBF0F7' }"
      :row-style="$TableRowStyle" :data="log_list" highlight-current-row style="width: 100%" v-if="log_params.type == 3">
      <!-- 列表下拉 -->
      <el-table-column prop="time_total" label="客户名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="客户手机号" align="center">
        <template slot-scope="scope">
          {{ scope.row.phone }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="导入机器人状态" align="center">
        <!-- <template slot-scope="scope">
          <div v-if="scope.row.status == 0 || scope.row.status == 1">
            {{ scope.row.status == 0 ? '未导入' : scope.row.status == 1 ? '导入成功' : '' }}
          </div>
          <div v-if="scope.row.status == 2">
            <el-tooltip class="item" effect="dark" :content="scope.row.error_msg" placement="top-start">
              导入失败
            </el-tooltip>
          </div>
        </template> -->
        <template slot-scope="scope">
          <el-popover>
            <div slot="reference" class="name-wrapper">
              <el-tag type="success" size="medium" v-if="scope.row.status !== 2">{{ scope.row.status == 0 ? '未导入' :
                scope.row.status == 1 ? '导入成功' : '' }}</el-tag>
            </div>
          </el-popover>
          <el-popover trigger="hover" placement="top">
            <p>{{ scope.row.error_msg }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag type="danger" size="medium" v-if="scope.row.status == 2">导入失败</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="time_total" label="导入失败原因" align="center">
        <template slot-scope="scope">
          {{ scope.row.error_msg }}
        </template>
      </el-table-column> -->
      <el-table-column prop="time_total" label="拨打状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.is_call == 0 ? '未拨打' : scope.row.is_call == 1 ? '拨打' : '' }}
        </template>
      </el-table-column>
      <el-table-column prop="time_total" label="添加时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at }}
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="log_params.total" :page-size="log_params.per_page" :current-page="log_params.page"
      @current-change="logPageChange" v-if="this.log_params.type == 1">
    </el-pagination>
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="log_paramstwo.total" :page-size="log_paramstwo.per_page" :current-page="log_paramstwo.page"
      @current-change="logPageChange" v-if="this.log_params.type == 2">
    </el-pagination>
    <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
      :total="log_paramsthree.total" :page-size="log_paramsthree.per_page" :current-page="log_paramsthree.page"
      @current-change="logPageChange" v-if="this.log_params.type == 3">
    </el-pagination>

    <el-dialog title="通话详情" :visible.sync="show_detail" width="400px" @close="show_detail = false">
      <!-- <span class="PhoneTitle">拨打信息</span> -->
      <div class="flex-box detail_box">
        <el-form label-width="150px" label_position="left">
          <el-form-item label="主叫号码："> {{ detail.caller }} </el-form-item>
          <el-form-item label="被叫号码："> {{ detail.callee }} </el-form-item>
          <el-form-item label="接通状态：">
            {{ detail.call_status == 1 ? "已接通" : "未接通" }}
          </el-form-item>

          <el-form-item label="通话录音：" v-if="detail.record_url">
            <div class="audio_img" @click="play">
              <img style="width: 20px; object-fit: cover" v-if="isPlaying"
                :src="$imageDomain + '/static/admin/outbound/play_voice.gif'" alt="" />
              <img style="width: 20px; object-fit: cover" v-else
                :src="$imageDomain + '/static/admin/outbound/voice_icon.png'" alt="" />
              <span class="c_white"> {{ detail.duration }}"</span>
            </div>
            <!-- {{ detail.record_url }} -->
          </el-form-item>

          <el-form-item label="被叫通话时长：">
            {{ detail.callee_duration }}"
          </el-form-item>

          <el-form-item label="主叫通话时长：">
            {{ detail.caller_duration }}"
          </el-form-item>
          <el-form-item label="总通话时长：">
            {{ detail.total_duration }}"
          </el-form-item>
          <el-form-item label="计费："> {{ detail.cost }}元 </el-form-item>
          <el-form-item label="呼出时间：">
            {{ detail.call_time }}
          </el-form-item>
          <el-form-item label="结束时间：">
            {{ detail.end_time }}
          </el-form-item>
          <el-form-item label="被叫接通时间：">
            {{ detail.callee_answered_time || "未接听" }}
          </el-form-item>
          <el-form-item label="主叫接通时间：">
            {{ detail.caller_answered_time || "未接听" }}
          </el-form-item>
        </el-form>

        <audio ref="musicMp3" id="musicMp3" :autoplay="false" controls="false" style="z-index: -1"></audio>
      </div>
    </el-dialog>

    <el-dialog width="660px" :visible.sync="is_upload_dialog" title="导入">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text" style="color: red;">{{ index + 1 }}.{{ item }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_upload_dialog = false">取 消</el-button>
        <el-button type="primary" @click="$refs.file_p.click()">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="newrules_loading" title="分配规则" :before-close="newrulescancels">
      <div style="font-size: 12px; color: red;margin-bottom: 15px;">如果拨打的手机号在潜在客户中，满足其中以下一个规则，
        会自动再次分配</div>
      <el-form ref="form" :model="formInline" label-width="128px">
        <el-form-item label="客户是否有关注点:">
          <el-switch v-model="formInline.is_focus"></el-switch>
        </el-form-item>
        <el-form-item label="客户标签:">
          <el-checkbox-group v-model="formInlinetype">
            <el-checkbox label="A">A(高意向)</el-checkbox>
            <el-checkbox label="B">B(一般意向)</el-checkbox>
            <el-checkbox label="C">C(再联系)</el-checkbox>
            <el-checkbox label="D">D(拒绝)</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="通话时长:">
          <el-input type='number' v-model="formInline.duration" style="width: 120px;"></el-input>
          <div style="font-size: 12px; color: red">默认为0，如果客户通话时长超过10秒都可默认为有意向的客户</div>
        </el-form-item>
        <el-form-item>
          <el-button @click="newrulescancels">取消</el-button>
          <el-button type="primary" @click="addrules">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import myLabeldial from "../../components/my_label.vue"; //通话列表第一个筛选
import myLabeladd from "../../components/my_label.vue";  //号码库及
import myLabeladdtwo from "../../components/my_label.vue";  //导入记录列表的筛选
import myLabelcall from "../../components/my_label_two.vue";  //通话时长
import myLabellabel from "../../components/my_label.vue";  //标签
import myLabelIndex from "../../components/my_label_two.vue";   //导入状态
import myLabelthred from "../../components/my_label.vue";   //拨打状态

// import myLabeltwo from "../../components/my_label_two.vue";  
// import myLabelthree from "../../components/my_label_three.vue";
import config from "@/utils/config";
import myaudio from "./audio.vue";
// import messageVue from '../../default/message.vue';

export default {
  components: {
    myLabeldial,
    myLabeladd,
    myLabelcall,
    myLabellabel,
    myLabelIndex,
    myLabelthred,
    myaudio,
    myLabeladdtwo
    // myLabelthree
  },
  data() {
    return {
      p_time: "",
      formInline: {   //新增分则提交的表单
        label_type: '', //意向
        is_focus: '',  //客户是否有关注点
        duration: '',  //通话时长
      },
      formInlinetype: [],
      newrules_loading: false,
      isShow: true,
      times_input: false,
      getlog_input: false,
      controlList: 'onlyOnePlaying',
      tableData: [], // 表格绑定值
      activeName1: "2",
      tel_table_loading: false,
      is_upload_dialog: false,//导入弹框
      // tabs绑定值
      // user_avatar: `/api/admin/call_clue/robot/import_file?category=${config.CATEGORY_IM_FILE_2}`,
      tabs_list_new: [
        {
          id: 2,
          name: '2',
          label: '通话记录'
        },
        {
          id: 3,
          name: '3',
          label: '号码库列表'
        },
        {
          id: 1,
          name: '1',
          label: '导入记录',
        },
      ],
      notimport_list: [
        { id: 1, name: "导入状态", status: '-1' },
        { id: 2, name: "未导入", status: '0' },
        { id: 3, name: "导入成功", status: '1' },
        { id: 4, name: "导入失败", status: '2' },
      ],
      call_status_list: [
        { id: 1, name: "拨打状态", is_call: '-1' },
        { id: 2, name: "未拨打", is_call: '0' },
        { id: 3, name: "已拨打", is_call: '1' },
      ],
      time_list: [
        { id: 1, name: "添加时间", times: '' },
        { id: 2, name: "今天", times: 'today' },
        { id: 3, name: "昨天", times: 'yesterday' },
        { id: 4, name: "本周", times: 'this_week' },
        { id: 5, name: "上周", times: 'last_week' },
        { id: 6, name: "本月", times: 'this_month' },
        { id: 7, name: "上月", times: 'last_month' },
        { id: 8, name: "自定义时间", },
        // date_type  1全部 2今天 3昨天 4本周 5上周 6本月 7上月
      ],
      time_listone: [
        { id: 1, name: "拨打时间", times: '' },
        { id: 2, name: "今天", times: 'today' },
        { id: 3, name: "昨天", times: 'yesterday' },
        { id: 4, name: "本周", times: 'this_week' },
        { id: 5, name: "上周", times: 'last_week' },
        { id: 6, name: "本月", times: 'this_month' },
        { id: 7, name: "上月", times: 'last_month' },
        { id: 8, name: "自定义时间", },

        // date_type  1全部 2今天 3昨天 4本周 5上周 6本月 7上月
      ],
      second_list: [
        { id: 1, name: "通话时长", duration: '' },
        { id: 2, name: "<15秒", duration: '0,15' },
        { id: 3, name: "15-30秒", duration: '15,30' },
        { id: 4, name: "31-60秒", duration: '31,60' },
        { id: 5, name: "31-120秒", duration: '31,120' },
        { id: 6, name: ">120秒", duration: '120,3600' },
        { id: 7, name: "自定义时间", },
      ],
      //意见
      options_list: [
        {
          id: 1, name: "标签", label_type: ""
        },
        {
          id: 2, name: "A (高意向)", label_type: "A"
        },
        {
          id: 3, name: "B (一般意向)", label_type: "B"
        },
        {
          id: 4, name: "C (再联系)", label_type: "C"
        },
        {
          id: 5, name: "D (拒绝)", label_type: "D"
        },
      ],
      activeIndexdial: 0,
      activeIndexadd: 0,
      activeIndexcall: 0,
      activeIndexlabel: 0,
      activeIndex: 0,
      activethred: 0,
      activeall: 0,
      activeIndexaddtwo: 0,
      robot_list: [],
      log_list: [],
      //导入记录
      log_params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        type: 2,
        times: '',
      },
      //通话记录
      log_paramstwo: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        times: '',
        duration: '',
        phone: '',
        name: '',
        label_type: ''
      },
      //号码库列表
      log_paramsthree: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        times: '',
        phone: '',
        name: '',
        status: '', //导入状态
        is_call: '',//拨打状态
      },
      recharge_list: [], // 充值记录列表
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过1000条",
        "表格中不可有重复的手机号",
        "导入号码成功后，请联系平台客服确认",
        "导入号码库，号码未拨打的情况下，机器人号码库不支持重复导入",
        "分配规则为空的情况下，系统会自动根据AB等级及高意向一般意向自动分配潜在客户",
      ],
      detail: {},
      show_detail: false,
      isPlaying: false,
      // placeholder: "请输入手机号码",
      select_params: {
        type: 1,
        keywords: ""
      },
      timesone: '',
      timestwo: '',
      upload_form: {
        type: 1, // 是否覆盖数据 1：不覆盖 2：覆盖
        admin_id: "", // 管理员id
        file: "", // 导入的文件
        type_id: "", // 客户类型id
        source_id: "", // 客户来源
        label: "", // 标签:字符串格式，多个用逗号隔开
        remark: "", // 客户备注线索
      },
    }
  },
  created() {
    // this.getSeatsFees()
    // this.RechargeRecord()
    this.$http.getoptionsList().then((res) => {
      // this.options_list = res.data
      console.log(res.data, "意见列表")
    })
    if (this.log_params.type == 2) {
      this.getLog()
    } else if (this.log_params.type == 1) {
      this.importList()
    } else if (this.log_params.type == 3) {
      this.robotimport()
    }
    // this.log_params.log_id = this.timesone + "," + this.timestwo
    // console.log(this.log_params.log_id,"拼接的时间")
  },
  methods: {
    //局部刷新
    reload() {//局部刷新
      this.isShow = false;
      this.$nextTick(() => {
        this.isShow = true
      })
    },
    handleFileUpload(event) {
      // 阻止默认行为
      event.preventDefault();
      let file = this.$refs.file_p.files[0];
      let formData = new FormData();
      formData.append("file", file);
      this.onUpload(formData);
    },
    onUpload(formData) {
      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      
      //  self_auth_create_all区分是否是企业微信自建应用的导入程序
      this.$http.uploadCrmCustomerAI(formData).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.is_upload_dialog = false;
          this.loading.close();

          this.$parent.getOutboundStatistics();  //调用父组件的事件
          this.importList();
        }
        if (res.status !== 200) {
          this.loading.close();
        }
      })
    },
    onUploadTem() {
      window.open(
        "https://tfyfxbb.oss-cn-hangzhou.aliyuncs.com/uploads/robot/robot_template.xlsx?f=" +
        +new Date()
      );
    },
    // setParams(key) {
    //   let arr = ['phone', 'name']
    //   arr.map(item => {
    //     if (item !== key) {
    //       this.log_params[item] = ''
    //     }
    //   })
    //   if (this.log_params.type == 2) {
    //     this.getLog()
    //   } else if (this.log_params.type == 3) {
    //     this.robotimport()
    //   }
    // },
    // changeSelectParams(e) {
    //   // console.log(e);
    //   if (e == 1) {
    //     this.placeholder = '请输入手机号码'
    //   } else if (e == 2) {
    //     this.placeholder = '请输入客户名字'
    //   }

    // },
    //提交分则
    addrules() {
      this.formInline.label_type = this.formInlinetype.toString()
      console.log(this.formInline.label_type, "11111111111")
      this.$http.setrulesfrom(this.formInline).then(() => {
        this.$message({
          message: '提交成功',
          type: 'success'
        })
        this.newrules_loading = false
      })
    },
    //新增分则弹窗
    newrules() {
      this.$http.getrulesfrom().then((res) => {
        if (res.data.config.label_type == '') {
          this.formInlinetype = ['A', 'B']
          console.log("11111111111111111")
        } else {
          this.formInlinetype = res.data.config.label_type?.split(',') || []
        }
        this.formInline.is_focus = res.data.config.is_focus
        this.formInline.duration = res.data.config.duration
        console.log(res.data.config, "获取自动分配归责")
        this.newrules_loading = true
      })

      // const a = ''
      // const b = a?.split(',') || []
      // console.log(b)
    },
    //关闭新增分则的弹窗
    newrulescancels() {
      this.newrules_loading = false
    },
    handleKeywordSearch() {
      // if (this.select_params.type == 1) {
      //   this.setParams('phone')
      //   this.placeholder = '请输入手机号码'
      //   this.log_params.phone = this.select_params.keywords.replace(/\s+/g, "");
      // } else if (this.select_params.type == 2) {
      //   this.setParams('name')
      //   this.placeholder = '请输入客户编号'
      //   this.log_params.name = this.select_params.keywords
      // }
      if (this.log_params.type == 2) {
        this.log_paramstwo.page = 1
        this.getLog()
      } else if (this.log_params.type == 3) {
        this.log_paramsthree.page = 1
        this.robotimport()
      }
      this.tel_table_loading = true
    },

    //切换拨打状态
    callstatus(e) {
      this.log_paramsthree.is_call = e.is_call
      this.tel_table_loading = true
      this.log_paramsthree.page = 1
      this.robotimport()
      console.log(e, "拨打的状态")
    },
    //切换导入状态
    notimport(e) {
      this.log_paramsthree.status = e.status
      this.log_paramsthree.page = 1
      this.tel_table_loading = true
      this.robotimport()
    },
    //切换时间
    secondType(e) {
      if (e.id != 7) {
        this.log_paramstwo.duration = e.duration
        this.tel_table_loading = true
        this.log_paramstwo.page = 1
        this.times_input = false
        this.timesone = '',   //清空两个输入框的值
          this.timestwo = ''
        this.getLog()
      } else if (e.id == 7) {
        this.times_input = true
        this.log_paramstwo.duration = ""
        this.log_params.page = 1
        this.tel_table_loading = true
        this.getLog()
        console.log("自定义时间")
      }
    },
    //自定义时间
    inquire() {
      this.log_paramstwo.duration = this.timesone + "," + this.timestwo
      this.log_params.page = 1
      this.tel_table_loading = true
      this.getLog()
    },
    //切换意见 
    optionsClick(e) {
      this.log_paramstwo.label_type = e.label_type
      this.tel_table_loading = true
      this.getLog()
      console.log(e, "切换意见了")
    },
    //切换日期
    onClickType(e) {
      if (e.id != 8) {
        this.getlog_input = false
        this.tel_table_loading = true
        if (this.log_params.type == 2) {
          this.log_paramstwo.times = e.times
          this.log_paramstwo.page = 1
          this.getLog()
        } else if (this.log_params.type == 1) {
          this.log_params.times = e.times
          this.log_params.page = 1
          this.importList()
        } else if (this.log_params.type == 3) {
          this.log_paramsthree.times = e.times
          this.log_paramsthree.page = 1
          this.robotimport()
        }
      } else if (e.id == 8) {
        this.getlog_input = true
        this.tel_table_loading = true
        if (this.log_params.type == 2) {
          this.log_paramstwo.times = e.times
          this.log_paramstwo.page = 1
          this.getLog()
        } else if (this.log_params.type == 1) {
          this.log_params.times = e.times
          this.log_params.page = 1
          this.importList()
        } else if (this.log_params.type == 3) {
          this.log_paramsthree.times = e.times
          this.log_paramsthree.page = 1
          this.robotimport()
        }
      }
    },
    //自定义日期
    changeTimeRange(e) {
      let a = e ? e[0] : "";
      let b = e ? e[1] : "";
      this.tel_table_loading = true
      if (this.log_params.type == 2) {
        this.log_paramstwo.times = a + "," + b;
        this.log_paramstwo.page = 1
        this.getLog()
      } else if (this.log_params.type == 1) {
        this.log_params.times = a + "," + b;
        this.log_params.page = 1
        this.importList()
      } else if (this.log_params.type == 3) {
        this.log_paramsthree.times = a + "," + b;
        this.log_paramsthree.page = 1
        this.robotimport()
      }
      console.log(this.log_paramstwo.times, "&&&&&")
      if (this.log_paramstwo.times == ',') {
        this.log_params.page = 1
        this.log_paramstwo.times = ""
        this.getLog()
        console.log(this.log_paramstwo.times, "aaaaaaaa")
      } else if (this.log_params.times == ',') {
        this.log_paramstwo.times = ""
        this.log_paramstwo.page = 1
        this.importList()
      } else if (this.log_paramsthree.times == ',') {
        this.log_paramsthree.times = ""
        this.log_paramsthree.page = 1
        this.robotimport()
      }
    },
    //列表详情按钮
    onEditData(row) {
      this.activeName1 = "3" //改变tab栏的状态
      this.log_params.type = 3
      this.tel_table_loading = true
      this.log_paramsthree.page = 1
      this.log_paramsthree.log_id = row.id
      this.log_paramsthree.total = 0
      this.robotimport()
      this.reload()
      console.log(this.activeName1, "详情传过去的数据")
    },
    //下载excel文件
    mydownload(row) {
      console.log(row.file_path, "可以下载的数据")
      window.open(
        row.file_path
      );
    },
    //点击tabs
    handleClickLog(e) {
      this.getlog_input = false
      this.tel_table_loading = true
      this.times_input = false
      this.activeName1 = e._props.name
      this.log_params.type = e._props.name
      this.timesone = ''
      this.timestwo = ''
      this.log_paramstwo.name = ''
      this.log_paramstwo.phone = ''
      this.log_paramsthree.name = ''
      this.log_paramsthree.phone = ''
      // this.log_list = []
      this.p_time = ''
      this.callingall()
    },
    callingall() {
      if (this.log_params.type == 2) {
        this.log_paramstwo.page = 1
        this.activeIndexdial = 0
        this.activeIndexcall = 0
        this.activeIndexlabel = 0
        this.getLog()
      } else if (this.log_params.type == 1) {
        this.activeIndexadd = 0
        this.log_params.page = 1
        this.importList()
      } else if (this.log_params.type == 3) {
        this.activeIndexadd = 0
        this.activeIndex = 0
        this.activethred = 0
        this.log_paramsthree.page = 1
        this.robotimport()
      }
    },
    //号码库列表
    robotimport() {
      this.$http.getimportDetail(this.log_paramsthree).then(res => {
        if (res.status == 200) {
          this.log_list = res.data.data
          this.log_paramsthree.total = res.data.total
          this.tel_table_loading = false
        }
      })
    },
    //通话记录列表
    getLog() {
      this.$http.getcallrobot(this.log_paramstwo).then(res => {
        if (res.status == 200) {
          console.log(this.robot_list, "通话记录里的数据")
          this.robot_list = res.data.data
          this.log_paramstwo.total = res.data.total
          this.tel_table_loading = false
        }
      })
    },
    //导入记录列表
    importList() {
      this.$http.getrobotimport(this.log_params).then(res => {
        this.tel_table_loading = false
        this.log_params.total = res.data.total
        this.log_list = res.data.data
        console.log(this.log_list, "导入记录列表的数据")
      })
    },

    play() {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      audios.src = this.detail.record_url
      if (this.isPlaying) {
        audios.pause();
        this.isPlaying = false
      } else {
        audios.play();
        this.isPlaying = true
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.isPlaying = false
        this.$forceUpdate();
        console.log("结束执行" + this.detail.duration);
      }, this.detail.duration * 1000);
    },
    // 分页器当前页发生改变
    logPageChange(val) {
      this.tel_table_loading = true
      if (this.log_params.type == 2) {
        this.log_paramstwo.page = val
        this.getLog()
      } else if (this.log_params.type == 1) {
        this.log_params.page = val
        this.importList()
      } else if (this.log_params.type == 3) {
        this.log_paramsthree.page = val
        this.robotimport()
      }
    },
  },
  computed: {
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
  }
}
</script>
<style scoped lang="scss">
.content {
  padding: 20px;
}

.seach_all {
  width: 100%;
  // min-height: 20px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;

  .seach_all_bottom {
    display: flex;
    justify-content: space-between;
    // margin-left: 520px;

    .seach_button {
      height: 40px;
      width: 80px;
      // margin-left: 00px;
    }
  }
}

.phoneMenu {
  .el-button:first-child {
    background-color: #17b63a;
  }

  .el-button:first-child,
  .el-button:nth-child(2) {
    width: 160px;
    height: 40px;
    box-sizing: border-box;
    padding: 9px 0px !important;
    font-size: 16px;
  }
}

.b-tabs {
  cursor: pointer;

  .b-t-item {
    width: 120px;
    height: 50px;
    text-align: center;
    line-height: 40px;
    color: #8a929f;
    position: relative;
    margin-top: 20px;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;

    &.isactive {
      color: #00a3ff;
      background-color: #fff;

      // &::after {
      //   position: absolute;
      //   content: "";
      //   left: 50%;
      //   transform: translateX(-50%);
      //   height: 3px;
      //   // background: #2d84fb;
      //   width: 100%;
      //   display: block;
      //   margin-top: 4px;
      // }
    }
  }

  .config-customer {
    .el-button {
      padding: 7px 15px;
    }
  }
}

.table-top-box-abs {
  // position: absolute;
  /* padding-top: 30px; */
  padding: 10px 0px;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;

  // transition: 0.3s;
  &.fixed {
    position: fixed;
    top: 60px;
    left: 254px;
    right: 40px;
    padding: 10px 24px;
    background: #fff;
    z-index: 100;

    .abs {
      right: 25px;
    }
  }
}

.scope-focus-box {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

.code-box {
  text-align: center;

  p {
    text-align: center;
    color: #6bcc03;
    font-size: 28px;
  }
}

.ml5 {
  margin-left: 5px;
}

.search_box {
  margin-bottom: 12px;
}

.detail_box {
  height: 70vh;
  overflow-y: auto;

  .el-form-item {
    margin-bottom: 10px;
  }

  ::v-deep .el-form-item__label {
    text-align: left;
    // justify-content: flex-start;
  }
}

.audio_img {
  width: 100px;
  height: 20px;
  display: flex;
  background: #409eff;
  margin-top: 10px;

  // justify-content: center;
  align-items: center;

  .c_white {
    margin-left: 5px;
    color: #fff;
  }
}

.tolead-box {
  height: 40px;
  width: 80px;
}

.tolead-box-add {
  position: relative;
  left: 94%;
  top: 36px;
  z-index: 1;
}

.label-all-box {
  width: 700px;
}
</style>