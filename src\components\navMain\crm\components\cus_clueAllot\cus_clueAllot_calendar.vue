<template>
    <div class="calendar-wrapper">
        <div class="current-date">今天是{{currentDate}}</div>
        <el-table :data="currentDatas" border stripe class="calendar-table">
            <el-table-column v-for="(week,index) in weeks" :label="week" :key="index" align="center">
                <template v-slot="{ row }">
                    <template v-if="row[index].type === 'data'">
                        <span v-if="row[index].value===''">
                            <el-link :underline="false" type="primary" @click="setDayData(row[index])" :class="{'cell-disabled': row[index].disabled, cur:row[index].cur}">设置</el-link>
                        </span>
                        <span v-else>
                            <el-link :underline="false" @click="setDayData(row[index])" :class="{'cell-disabled': row[index].disabled, cur:row[index].cur}">{{row[index].value}}</el-link>
                        </span>
                    </template>
                    <template v-else>
                        <span :class="{'cell-disabled': row[index].disabled, cur:row[index].cur}">{{row[index].value}}</span>
                    </template>
                </template>
            </el-table-column>
        </el-table>
      
        <cus_clueAllot_calendar_set ref="setDayData" v-if="dialogs.setDayData"/>
    </div>
</template>

<script>
import cus_clueAllot_calendar_set from './cus_clueAllot_calendar_set.vue';
export default {
    props: {
        datas: []
    },
    components: {
        cus_clueAllot_calendar_set
    },
    data() {
        return {
            year: 0,
            month: 0,
            day: 0,
            weeks: ['一', '二', '三', '四', '五', '六', '日'],
            calendarDatas: [],
            dialogs: {
                setDayData: false,
            }
        }
    },
    watch: {
        datas: {
            handler(d){
                const datas = Array.isArray(d) ? d: [];
                this.calendarDatas = datas.map(item => {
                    let type = 0;      //1会员，2分组
                    if(item.admin_id){
                        type = 1;
                    }else if(item.group_id){
                        type = 2;
                    }
                    return {
                        day: item.day,
                        admin_id: item?.admin_id || '',
                        group_id: item?.group_id || '',
                        type,
                        admin_name: item?.admin?.user_name || '',
                        group_name: item?.push_group?.title || ''
                    }
                });
            },
            immediate: true
        }
    },
    computed: {
        //当前日期
        currentDate(){
            return this.year+'年'+this.month+'月'+this.day+'日';
        },
        //结构化日历天数数据
        currentDays() {
            const days = [];
            const dayTotal = this.getMonthDays();
            const week = this.getMonthFirstDay();

            for (let i = 1; i < week; i++) {
                days.push({ value: '', type: 'empty' });
            }

            for (let i = 1; i < 32; i++) {
                days.push({ value: i, type: 'day', disabled: i > dayTotal, cur: this.day === i });
            }
            
            for (let i = 7,length = days.length%7 || 7; i > length; i--) {
                days.push({ value: '', type: 'empty'});
            }

            let currentDays = [],
                index = -1;
            for (let i = 0, length = days.length; i < length; i++) {
                if(i % 7 === 0){
                    index++;
                    currentDays.push([]);
                }
                currentDays[index].push(days[i]);
            }
            return currentDays;
        },
        //日历数据
        currentDatas(){
            const list = [], datas = this.calendarDatas;
            this.currentDays.forEach((days, index) => {
                list.push(days);
                const row = [];
                for(let i = 0; i < 7; i++){
                    if(days[i].type === 'empty'){
                        row.push(days[i]);
                    }else{
                        let dayValue = days[i].value,
                            dayData = datas.find(item => item.day === dayValue),
                            value = '';  
                        if(dayData){
                            if(dayData.type === 1){
                                value = dayData.admin_name || dayData.admin_id;
                            }else if(dayData.type === 2){
                                value = dayData.group_name || dayData.group_id;
                            }
                        }
                        row.push({ value, day: dayValue, disabled: days[i].disabled, cur: days[i].cur, type: 'data'});
                    }
                }
                list.push(row)
            })
            return list;
        },
    },
    created() {
        //年月日
        const date = new Date();
        this.year = date.getFullYear();
        this.month = date.getMonth() + 1;
        this.day = date.getDate();
    },
    methods: {
        //获取当前月天数
        getMonthDays() {
            const date = new Date(this.year, this.month, 0);
            return date.getDate();
        },
        //获取当前月第一天是星期几
        getMonthFirstDay() {
            const date = new Date(this.year, this.month - 1, 1);
            return date.getDay() || 7;
        },
        //设置day数据
        async setDayData(row){
            this.dialogs.setDayData = true;
            await this.$nextTick();
            const index = this.calendarDatas.findIndex(item => item.day === row.day);
            const params = index !== -1 ? this.calendarDatas[index] : {
                day: row.day,
                admin_id: '',
                group_id: '',
                type: 0,
                admin_name: '',
                group_name: ''
            };

            this.$refs.setDayData.open(params).onCancel(()=>{
                this.dialogs.setDayData = false;
            }).onSuccess(data=>{
                this.dialogs.setDayData = false;
               if(index === -1){
                   this.calendarDatas.push(data);
               }else{
                    this.$set(this.calendarDatas, index, data);
               }
            });
        },
        //获取日历数据
        getCalendarData(){
            return this.calendarDatas;
        }
    }
}
</script>

<style lang="scss" scoped>
.calendar-wrapper{
    width: 700px;
    .current-date{
        text-align: right;
        color: #606266;
        padding: 10px 0;
    }
    .calendar-table{
        .cell-disabled{
            color: #c0c4cc;
        }
        .cur{
            color:#ff5b6a
        }
    }
}

::v-deep .el-table {
    th > .cell{
        padding-right: 10px!important;
    }
    .el-table__body tr:nth-child(2n+1){
        td{
            padding: 6px 0;
        }
        &:hover{
            td{
                background: #fff;
            }
            
        }
    }
}
</style>