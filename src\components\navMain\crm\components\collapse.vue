<template>
  <div class="container">
    <collapse>
      <div v-show="isActive">
        <div>
          <slot name="content"></slot>
        </div>
      </div>
    </collapse>
  </div>
</template>
<script>
import collapse from "./js/collapse.js";
export default {
  data() {
    return {};
  },
  props: {
    isActive: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    collapse,
  },
};
</script>
