<template>
  <div>
    <div class="content-box-crm">
      <div class="table-top-box div row">
        <div class="t-t-b-left div row"></div>
        <div class="t-t-b-right div row">
          <!-- <el-button type="warning" > 下载示例 </el-button> -->
          <!-- <el-button type="primary" @click="add"> 套餐列表</el-button> -->
        </div>
      </div>
      <div>
        <el-table
          v-loading="is_table_loading"
          :data="orderData"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="创建时间：">
                  <span>{{ props.row.created_at }}</span>
                </el-form-item>
                <el-form-item label="支付时间：" v-if="props.row.status == 1">
                  <span>{{ props.row.payment_at }}</span>
                </el-form-item>
                <el-form-item label="支付方式：" v-if="props.row.status == 1">
                  <span>{{
                    props.row.payment_category_id == 0
                      ? "暂未支付"
                      : props.row.payment_category_id == 1
                      ? "微信小程序支付"
                      : props.row.payment_category_id == 2
                      ? "微信扫码支付"
                      : props.row.payment_category_id == 2
                      ? "微信APP支付"
                      : "微信H5支付"
                  }}</span>
                </el-form-item>

                <el-form-item label="订单支付状态：">
                  <span>{{
                    props.row.payment_status === 0 ? "未付款" : "已付款"
                  }}</span>
                </el-form-item>
                <el-form-item label="成交单号" v-if="props.row.status == 1">
                  <span>{{ props.row.payment_trade_sn }}</span>
                </el-form-item>
                <el-form-item label="备注信息：">
                  <span>{{ props.row.remark }}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column
            prop="id"
            label="订单ID"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="order_sn"
            label="订单号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="payment_amount"
            label="订单金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="sms_total"
            label="短信条数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="payment_status"
            label="支付状态"
            align="center"
            v-slot="{ row }"
          >
            <el-tag type="success" v-if="row.payment_status === 1">
              已付款
            </el-tag>
            <el-tag
              type="warning"
              v-if="row.payment_status === 0 && row.status !== 2"
            >
              未付款
            </el-tag>
            <el-tag class="ml5" type="danger" v-if="row.status === 2">
              已取消
            </el-tag>
          </el-table-column>
          <!-- <el-table-column
            prop="created_at"
            label="创建时间"
            v-slot="{ row }"
            align="center"
          >
            {{ row.created_at || "--" }}
          </el-table-column>
          <el-table-column
            prop="payment_at"
            label="支付时间"
            v-slot="{ row }"
            align="center"
          >
            {{ row.payment_at || "--" }}
          </el-table-column> -->
          <el-table-column
            label="操作"
            width="240"
            align="center"
            v-slot="{ row }"
          >
            <el-popconfirm
              title="确定取消订单吗？"
              v-if="row.payment_status === 0 && row.status == 0"
              @onConfirm="cancelOrder(row)"
            >
              <el-button slot="reference" type="primary" size="small"
                >取消订单</el-button
              >
            </el-popconfirm>
            <el-button
              v-if="row.payment_status === 0 && row.status !== 2"
              type="success"
              size="small"
              style="margin: 0 10px"
              @click="pay(row)"
              >微信支付</el-button
            >
          </el-table-column>
        </el-table>

        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="params.per_page"
          :current-page="params.page"
          @current-change="onPageChange"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      width="700px"
      :visible.sync="is_showDia"
      append-to-body
      title="套餐列表"
    >
      <div v-if="is_showDia">
        <el-table
          v-loading="is_table_loading1"
          :data="packageList"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <!-- <el-table-column prop="id" label="订单ID"></el-table-column> -->
          <el-table-column prop="id" label="套餐id"></el-table-column>
          <el-table-column prop="price" label="套餐价格"></el-table-column>
          <el-table-column prop="sms_total" label="短信条数"></el-table-column>
          <!-- sms_total -->
          <el-table-column
            prop="description"
            label="套餐描述"
          ></el-table-column>
          <el-table-column prop="status" label="套餐状态" v-slot="{ row }">
            <el-tag v-if="row.status == 1" type="success"> 正常 </el-tag>
            <el-tag v-else type="warning"> 已下架 </el-tag>
          </el-table-column>

          <el-table-column label="操作" v-slot="{ row }">
            <el-popconfirm
              title="确定创建订单吗？"
              style="margin: 0 10px"
              @onConfirm="confirmAdd(row)"
            >
              <el-link slot="reference" type="success">创建订单</el-link>
            </el-popconfirm>
          </el-table-column>
        </el-table>

        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="packageTotal"
          :page-size="package_params.per_page"
          :current-page="package_params.page"
          @current-change="onPackagePageChange"
        >
        </el-pagination>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button type="primary" v-loading="add_loading" @click="confirmAdd"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      width="500px"
      append-to-body
      :visible.sync="showPayQrcode"
      title="支付"
    >
      <div class="flex-row items-center j-center" v-if="showPayQrcode">
        <div>
          <div class="pay_img">
            <img :src="payQrcode" alt="" />
          </div>
          <div class="pay_tips">请打开微信扫一扫支付</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderData: [],
      is_table_loading: false,
      is_table_loading1: false,
      params: {
        page: 1,
        per_page: 10,
      },
      total: 0,
      is_showDia: false,
      order_form: {
        package_id: ""
      },
      add_loading: false,
      is_task_loading: false,
      packageList: [],
      payQrcode: "",
      showPayQrcode: false,
      package_params: {
        page: 1,
        per_page: 10
      },
      packageTotal: 0
    }
  },
  computed: {
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$http.getMaketingOrderList(this.params).then(res => {
        if (res.status == 200) {
          this.orderData = res.data.data
          this.total = res.data.total
        }

      }).catch(() => {

      })
    },
    add() {
      this.getPackageList()
      this.is_showDia = true
    },
    getPackageList() {
      this.is_table_loading1 = true
      this.$http.getPackageList({ status: 1 }).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.packageList = res.data.data
        }
        this.packageTotal = res.data?.total || 0
        this.is_table_loading1 = false
      }).catch(() => {
        this.is_table_loading1 = false
      })
    },
    confirmAdd(row) {
      this.add_loading = true
      this.$http.addMarketOrder({ package_id: row.id }).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.$message.success("订单创建成功")
          this.is_showDia = false
          this.add_loading = false
          this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })

    },
    pay(row) {
      this.$http.getMarkeOrderQrcode(row.id).then(res => {
        console.log(res.data, 1111);
        if (res.status == 200) {
          this.showPayQrcode = true
          this.payQrcode = "data:image/png;base64," +
            btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            );
        }
      })
    },
    cancelOrder(row) {
      this.$http.cancelMarketOrder(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "订单取消成功")
          this.getList()
        }
      })
    },


    onPageChange(current_page) {
      this.params.page = current_page;
      this.getList()
    },
    onPackagePageChange(current_page) {
      this.package_params.page = current_page
      this.getPackageList()
    }



  }
}
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px 12px;
}
.content-box-crm {
  padding: 0;
}
.ml30 {
  margin-left: 30px;
}
.ml5 {
  margin-left: 5px;
}
.padd10 {
  padding: 10px 0 40px;
}
.title {
  padding-top: 20px;
  padding-left: 75px;
}
.pay_img {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.pay_tips {
  margin-top: 10px;
  text-align: center;
  color: #6bcc03;
  font-size: 28px;
}
</style>