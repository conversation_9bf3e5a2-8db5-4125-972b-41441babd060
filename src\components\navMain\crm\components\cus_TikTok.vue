<template>
    <div>
         <!-- 表格 -->
         <div class="liuzi">
            <div class="jiansuo">
                <div style="margin-right:10px;">
                    <el-input style="width: 185px;" size="small" placeholder="请输入线索名称" v-model="input3" class="input-with-select">
                      <el-button slot="append" icon="el-icon-search" @click="search"></el-button>
                    </el-input>
                    <el-select clearable size="small" style="width: 150px;margin:0px 10px;" v-model="sourcevalue" placeholder="请选择来源"
                       @change="search">
                        <el-option
                        v-for="item in sourceoptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                    <el-select clearable size="small" style="width: 150px;" v-model="statusvalue" placeholder="请选择状态"
                       @change="search">
                        <el-option key="1" label="开启" value="1"></el-option>
                        <el-option key="0" label="关闭" value="0"></el-option>
                    </el-select>
                </div>
                <el-button type="primary" class="add_table" @click="addTikToKClue">+新增</el-button>
            </div>
             <div>
                <el-tooltip class="item" effect="light" placement="right" style="display: inline-block; margin-left: 175px">
                  <div slot="content" style="max-width: 320px;line-height:25px;">
                    去重：手机号码唯一，留资号码进入公海或指定成员-私客 <br>
                    不去重：重复留资号码，将分配至指定成员-流转客。 设置后，所有线索来源推送规则全局生效。<br>
                    可设置多次留资间隔分配频率限制，默认60分钟内同一手机号码多次推送仅分配1次。
                  </div>
                  <i class="el-icon-info" style="color: #f56c6c; font-size: 20px"> </i>
                </el-tooltip>
                <el-radio-group v-model="radio1" size="small" @change="submitliuzi">
                  <el-radio-button label="0">留资去重</el-radio-button>
                  <el-radio-button label="1">不去重</el-radio-button>
                </el-radio-group>
              </div>
         </div>
         <el-table
             v-loading="is_table_loading"
             :data="TikTokDataList.slice((currentPage-1)*pageSize,currentPage*pageSize)"
             :header-cell-style="{ background: '#EBF0F7' }"
             border
             highlight-current-row
             @select="handleSelectionChange"
             @select-all="allhandleSelectionChange"
         >
         <el-table-column
            type="selection"
            width="55">
         </el-table-column>
         <el-table-column prop="name" label="线索名称"></el-table-column>
         <!-- <el-table-column label="线索归属" v-slot="{ row }">
             <span>{{ row | AttributionClue }}</span>
         </el-table-column> -->
         <el-table-column label="来源" v-slot="{ row }">
             <span>{{row.platform === 1 ? "抖音" : row.platform === 2 ? "快手" :row.platform === 3 ?  "海豚知道":
             row.platform === 4 ?'视频号橱窗':row.platform === 5?'飞鱼':row.platform === 6?"小红书私信"
             :row.platform === 7?"小红书私信":row.platform === 8?"找房实验室":row.platform === 9?"开放平台":
             row.platform === 10?"微信小店":row.platform === 11?'腾讯广告':row.platform === 12?"视频号":"微信小店客服"}}</span>
         </el-table-column>
         <!-- <el-table-column label="关联成员" v-slot="{ row }">
             <span>{{ row | AssociatedMember }}</span>
         </el-table-column> -->
         <el-table-column label="状态" v-slot="{ row }">
             <el-tag :type="row.status === 1 ? 'primary' : 'danger'">{{
             row.status === 1 ? "开启" : "关闭"
             }}</el-tag>
         </el-table-column> 
         <el-table-column label="操作" fixed="right">
             <template slot-scope="scope">
                 <el-link 
                   type="primary" 
                   @click="copy(scope.row)"
                   style="margin-right: 10px; font-sizi: 12px;"
                   v-if="scope.row.url"
                 >
                     复制API
                 </el-link>
                 <el-link 
                   type="primary" 
                   @click="onChangeEdit(scope.row, scope.row.id-1)"
                   style="margin-right: 10px; font-sizi: 12px;"
                 >
                     编辑
                 </el-link>
                 <el-link
                   type="primary" 
                   @click="openOpPage(scope.row, scope.$index)"
                   style="margin-right: 10px; font-sizi: 12px;"
                 >
                     操作
                 </el-link>
                <el-popover
                  placement="top"
                  width="185"
                  trigger="click">
                  <div>
                    <div v-for="item,index in scope.row.list" :key="index" style="margin:10px;">
                        {{item.dy_id}}  <el-button style="margin-left:10px;" size="mini" type="primary"
                        @click="viewclues(item.dy_id)">查看</el-button>
                    </div>
                  </div>
                  <el-link
                  v-if="scope.row.list"
                   type="primary" 
                   style="margin-right: 10px; font-sizi: 12px;"
                   slot="reference"
                 >
                    查看
                 </el-link>
                </el-popover>
                <el-link
                  v-if="scope.row.key&&!scope.row.list"
                   type="primary" 
                   style="margin-right: 10px; font-sizi: 12px;"
                   slot="reference"
                   @click="viewcluescopy(scope.row)"
                 >
                    查看
                 </el-link>
               
                <el-link
                   v-if="scope.row.platform == 10"
                   type="primary"
                   @click="initialization(scope.row)"
                   :disabled="scope.row.is_init==1"
                   style="margin-right: 10px; font-sizi: 12px;">
                 {{scope.row.is_init==1?"拉取中":"拉取"}}
                 </el-link>

                 <el-link 
                    v-if="scope.row.status == 0"
                   type="danger" 
                   style="font-sizi: 12px;"
                   @click="onDelete(scope.row, scope.$index)"
                 >   
                     删除
                 </el-link>
             </template>
         </el-table-column>
         <el-table-column label="多次留资间隔分配频率限制" v-if="radio1==1" fixed="right"
         v-slot="{row}">
            <!-- <el-radio v-model="row.duration" label="1">开启</el-radio>
            <el-radio v-model="row.duration" label="0">关闭</el-radio> -->
            <div class="flex-row">
                <div>
                    <el-input
                        size="mini"
                        style="width: 150px;"
                        placeholder="请输入时间"
                        class="overdue"
                        v-model="row.limiter"
                        min="0"
                        step="1"
                        type="number"
                        @focus="preservetime(row)"
                        >
                        <template slot="append">
                            分钟
                        </template>
                    </el-input>
                </div>
            <div style="margin:6px;">
                <el-button type="primary" size="mini" @click="settime(row)">保存</el-button>
            </div>
            </div>
         </el-table-column>
         </el-table>
         <div class="page_footer flex-row items-center">
            <div class="page_footer_l flex-row flex-1 items-center">
              <div class="head-list">
                  <el-radio-group v-model="batchopen" size="small" @change="submitbatchopen">
                  <el-radio-button label="1">批量开启</el-radio-button>
                  <el-radio-button label="0">关闭</el-radio-button>
                </el-radio-group>
              </div>
              <div class="head-list">
                <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
              </div>
            </div>
            <div style="margin-right:10px;">
              <el-pagination align='center' 
              background
                 @size-change="handleSizeChange" 
                 @current-change="handleCurrentChange"
                 :current-page="currentPage" 
                 :page-sizes="[10,20,30,50]" 
                 :page-size="pageSize" 
                 layout="total, sizes, prev, pager, next, jumper" 
                 :total="TikTokDataList.length">
              </el-pagination>
            </div>
          </div>
 
 <!-- 编辑线索模态框 -->
         <el-dialog
           title="修改线索"
           :visible.sync="EditVisible"
           :close-on-click-modal="false"
           width="666px"
           @close="EditShutDown"
         >
         <el-form :model="form_labels" label-position="left" label-width="134px">
             <el-form-item label="线索来源：">
                 <el-select v-model="form_labels.platform" disabled  @change="changeAddPlat" 
                     style="width: 81%;" placeholder="请选择">
                        <el-option
                          v-for="item in platformoption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
             </el-form-item>
             <el-form-item label="线索名称：">
                 <el-input
                     style="width:400px"
                     v-model="form_labels.name"
                     :placeholder="form_labels.platform == 1 ? '请输入抖音号名称' : form_labels.platform == 2 ? '请输入快手号名称' : 
                        form_labels.platform == 3 ?'请输入线索名称': form_labels.platform == 4 ?'请输入视频号橱窗名称':form_labels.platform == 5 ?'请输入飞鱼名称':
                        form_created.platform == 6 ?'请输入小红书线索名称':form_created.platform == 7 ?'请输入小红书线索名称':form_created.platform == 8?
                        '请输入找房实验室名称':form_created.platform == 9?'开放平台':
                        form_created.platform == 10?'微信小店':form_created.platform == 11 ?'腾讯广告':form_created.platform == 11 
                        ?'视频号':'微信小店客服'"
                 ></el-input>
             </el-form-item>
             <el-form-item label="小店id：" v-if="form_labels.platform == 10">
                 <el-input
                     style="width:400px"
                     v-model="form_labels.app_id"
                     placeholder="请输入小店id(必填)"
                 ></el-input>
             </el-form-item>
             <el-form-item label="小店密钥：" v-if="form_labels.platform == 10">
                 <el-input
                     style="width:400px"
                     v-model="form_labels.app_secret"
                     placeholder="请输入小店密钥(必填)"
                 ></el-input>
             </el-form-item>
             <el-form-item label="达人ID：" v-if="form_labels.platform == 3">
                 <el-input
                     style="width:400px"
                     v-model="form_labels.key"
                     placeholder="请输入达人ID"
                 ></el-input>
             </el-form-item>
             <el-form-item label="SALT密钥：" v-if="form_labels.platform == 3">
                 <el-input
                     style="width:400px"
                     v-model="form_labels.secret"
                     placeholder="请输入SALT密钥"
                 ></el-input>
             </el-form-item>
             <el-form-item label="视频号橱窗ID: " v-if="form_labels.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_labels.key" 
                         placeholder="请输入视频号橱窗ID (必填)">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="账号秘钥" v-if="form_labels.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_labels.secret" 
                         placeholder="请输入账号秘钥(必填)">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息推送token令牌" v-if="form_labels.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_labels.token" 
                         placeholder="请输入消息推送token令牌(必填)">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息推送token令牌" v-if="form_labels.platform == 10">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_labels.token" 
                         placeholder="请输入消息推送token令牌">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息秘钥" v-if="form_labels.platform == 10">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_labels.message_secret" 
                         placeholder="请输入消息秘钥">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息推送秘钥" v-if="form_labels.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_labels.encoding_aes_key" 
                         placeholder="请输入消息推送秘钥(必填)">
                         </el-input>
                 </el-form-item>
             <el-form-item label="线索标签：" v-if="form_labels.platform !== 1">
                 <el-cascader
                     style="width: 400px;"
                     v-model="form_labels.label"
                     clearable
                     :show-all-levels="false"
                     placeholder="请选择标签"
                     @change="formLabelChange"
                     :options="label_list"
                     :props="{
                         value: 'id',
                         label: 'name',
                         children: 'label',
                         emitPath: false,
                         multiple: true,
                         checkStrictly: true,
                     }"
                 >
                 </el-cascader>
             </el-form-item>
            <!-- 抖音 -->
             <template v-if="form_labels.platform == 1">
             <div class="memberList">
             <div 
                 v-for="(item, index) in clueBelong_list"
                 :key="index"
             >
                 <div :style="item.type == 1 ? 'margin-bottom: 22px;' : ''" class="new_Contact_Person" style="margin-bottom:20px">
                     <!-- <div class="dyhao">
                         线索分配：
                     </div> -->
                             <div>
                                 <div>
                                     线索分配：
                                     <el-input 
                                         style="width: 172px;margin-left: 60px;"
                                         class="douyinID"
                                         v-model="item.dy_id" 
                                         placeholder="抖音号ID (必填)"
                                     >
                                     </el-input>
                                     <el-select 
                                         style="margin-left:5px;width: 77px;"
                                         v-model="item.status" 
                                         placeholder="状态"
                                         >
                                         <el-option
                                             v-for="list in user_status"
                                             :key="list.id"
                                             :label="list.name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                     <el-select 
                                         style="margin-left:5px;width: 140px;"
                                         v-model="item.create_id" 
                                         placeholder="录入人(可选填)"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in user_list"
                                             :key="list.values"
                                             :label="list.name"
                                             :value="list.values">
                                         </el-option>
                                     </el-select>
                                 </div>
                                 <div style="margin-left: 134px;" class="allocation">
                                     <el-select 
                                     style="width: 116px;"
                                     v-model="item.allocationway" 
                                     placeholder="分配方式"
                                     @change="switchtype(item)"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                    <el-select 
                                        style="margin-left: 10px;width: 106px;"
                                         :placeholder="item.allocationway=='1_1'?'维护人':'分组'"
                                         :value="item.follow_id ? item.follow_id : item.follow_group_id"
                                         @input="item.follow_id = $event; item.follow_group_id = $event"
                                         @change="douyinwei($event,item, index)"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in (item.allocationway=='1_1'?user_list:respectuser_list)"
                                             :key="list.id||list.values"
                                             :label="list.user_name||list.name"
                                             :value="list.id||list.values">
                                         </el-option>
                                     </el-select>
                                     <el-cascader
                                         style="width: 156px;margin-left:10px"
                                         v-model="item.label"
                                         clearable
                                         :show-all-levels="false"
                                         placeholder="请选择标签"
                                         @change="handleLabelChange(item)"
                                         :options="label_list"
                                         :props="{
                                             value: 'id',
                                             label: 'name',
                                             children: 'label',
                                             emitPath: false,
                                             multiple: true,
                                             checkStrictly: true,
                                         }">
                                     </el-cascader>
                                 </div>
                             </div>
                     <i v-if="index == 0 && form_labels.platform == 1" @click="addClueBelong" class="el-icon-circle-plus-outline addBelong"></i>
                     <i v-else-if="index > 0 && form_labels.platform == 1" @click="delClueBelong(item,index)" class="el-icon-remove-outline delBelong"></i>
                 </div>
             </div>
             </div>
             </template>
            <!-- 快手 -->
             <template v-if="form_labels.platform == 2||form_labels.platform == 12">
                 <div :style="form_labels.type == 1 ? 'margin-bottom: 22px;' : ''" class="new_Contact_Person">
                     <div class="dyhao">
                         线索分配：
                     </div>
                     <div>
                     <div class="allocation flex-row">
                            <el-select 
                                 style="width: 150px;"
                                 v-model="form_labels.create_id" 
                                
                                 placeholder="录入人"
                                 clearable
                                 filterable>
                                  <el-option
                                     v-for="list in user_list"
                                     :key="list.values"
                                     :label="list.name"
                                     :value="list.values">
                                 </el-option>
                            </el-select>
                         <el-input 
                            v-if="form_labels.platform == 2"
                             style="width: 240px;"
                             class="douyinID"
                             v-model="form_labels.key" 
                             placeholder='快手号ID (可选填)'>
                         </el-input>
                         <el-input 
                            v-if="form_labels.platform == 12"
                             style="width: 240px;"
                             class="douyinID"
                             v-model="form_labels.account_id" 
                             placeholder='视频号ID (可选填)'>
                         </el-input>
                     </div>
                     <div class="allocation">
                     <el-select 
                         style="width: 153px;"
                         v-model="allocationway" 
                         placeholder="分配方式"
                         @change="ksswitchtype"
                         >
                             <el-option
                                 v-for="list in way_list"
                                 :key="list.id"
                                 :label="list.user_name"
                                 :value="list.id">
                             </el-option>
                     </el-select>
                      <el-select 
                         style="margin-left: 10px;width: 130px;" 
                         :placeholder="allocationway=='1_1'?'维护人':'分组'"
                         :value="form_labels.follow_id ? form_labels.follow_id : form_labels.follow_group_id"
                         @input="form_labels.follow_id = $event; form_labels.follow_group_id = $event"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in (allocationway=='1_1'?user_list:respectuser_list)"
                             :key="list.id||list.values"
                             :label="list.user_name||list.name"
                             :value="list.id||list.values">
                         </el-option>
                      </el-select>
                     </div>
                 </div>
                 </div>
             </template>
            <!-- 海豚知道 -->
             <template v-if="form_labels.platform == 3">
                 <el-select 
                         style="margin-left: 134px;width:100px" 
                         v-model="form_labels.follow_id" 
                         @change="changAddType"
                         placeholder="维护人"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in user_list"
                             :key="list.values"
                             :label="list.name"
                             :value="list.values">
                         </el-option>
                     </el-select>
                     <el-select 
                         style="margin-left: 10px;" 
                         v-model="form_labels.create_id" 
                         @change="changAddType"
                         placeholder="录入人"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in user_list"
                             :key="list.values"
                             :label="list.name"
                             :value="list.values">
                         </el-option>
                     </el-select>
             </template>
            <!-- 视频号橱窗 -->
             <template v-if="form_labels.platform == 4">
                 <div style="margin-bottom: 22px;" class="new_Contact_Person">
                     <div>
                         线索分配：
                     </div>
                     <div>
                     <div class="allocation flex-row">
                            <el-select 
                                 style="width: 117px;margin-right:10px;"
                                 v-model="form_labels.create_id" 
                                
                                 placeholder="录入人"
                                 clearable
                                 filterable>
                                  <el-option
                                     v-for="list in user_list"
                                     :key="list.values"
                                     :label="list.name"
                                     :value="list.values">
                                 </el-option>
                            </el-select>
                     <!-- </div>
                     <div class="allocation"> -->
                     <el-select 
                         style="width: 130px;"
                         v-model="allocationway" 
                         placeholder="分配方式"
                         @change="ksswitchtype"
                         >
                             <el-option
                                 v-for="list in way_list"
                                 :key="list.id"
                                 :label="list.user_name"
                                 :value="list.id">
                             </el-option>
                     </el-select>
                      <el-select 
                            style="margin-left: 10px;width: 130px;" 
                            :placeholder="allocationway=='1_1'?'维护人':'分组'"
                            :value="form_labels.follow_id ? form_labels.follow_id : form_labels.follow_group_id"
                         @input="form_labels.follow_id = $event; form_labels.follow_group_id = $event"
                           clearable
                           filterable>
                           <el-option
                               v-for="list in (allocationway=='1_1'?user_list:respectuser_list)"
                               :key="list.id||list.values"
                               :label="list.user_name||list.name"
                               :value="list.id||list.values">
                           </el-option>
                       </el-select>
                     </div>
                 </div>
                 </div>
             </template>
            <!-- 飞鱼 -->
             <template v-if="form_labels.platform == 5">
                 <div :style="form_labels.type == 1 ? 'margin-bottom: 22px;' : ''" class="new_Contact_Person">
                     <div class="dyhao">
                         线索分配：
                     </div>
                     <div>
                     <div class="allocation flex-row">
                            <el-select 
                                 style="width: 111px;margin-right:10px;"
                                 v-model="form_labels.create_id" 
                                
                                 placeholder="录入人"
                                 clearable
                                 filterable>
                                  <el-option
                                     v-for="list in user_list"
                                     :key="list.values"
                                     :label="list.name"
                                     :value="list.values">
                                 </el-option>
                            </el-select>
                     <el-select 
                         style="width: 137px;"
                         v-model="allocationway" 
                         placeholder="分配方式"
                         @change="ksswitchtype"
                         >
                             <el-option
                                 v-for="list in way_list"
                                 :key="list.id"
                                 :label="list.user_name"
                                 :value="list.id">
                             </el-option>
                     </el-select>
                       <el-select 
                            style="margin-left: 10px;width: 130px;" 
                            :placeholder="allocationway=='1_1'?'维护人':'分组'"
                            :value="form_labels.follow_id ? form_labels.follow_id : form_labels.follow_group_id"
                            @input="form_labels.follow_id = $event; form_labels.follow_group_id = $event"
                           clearable
                           filterable>
                           <el-option
                               v-for="list in (allocationway=='1_1'?user_list:respectuser_list)"
                               :key="list.id||list.values"
                               :label="list.user_name||list.name"
                               :value="list.id||list.values">
                           </el-option>
                       </el-select>
                     </div>
                 </div>
                 </div>
             </template>
            <!-- 小红书&&微信小店&&腾讯广告-->
            <template v-if="form_labels.platform == 6||form_labels.platform == 7||form_labels.platform == 10
            ||form_labels.platform == 11">
                 <div :style="form_labels.type == 1 ? 'margin-bottom: 22px;' : ''" class="new_Contact_Person">
                     <div class="dyhao">
                         线索分配：
                     </div>
                     <div>
                     <div class="allocation flex-row">
                            <el-select 
                                 style="width: 120px;margin-right:10px;"
                                 v-model="form_labels.create_id" 
                                
                                 placeholder="录入人"
                                 clearable
                                 filterable>
                                  <el-option
                                     v-for="list in user_list"
                                     :key="list.values"
                                     :label="list.name"
                                     :value="list.values">
                                 </el-option>
                            </el-select>
                     <el-select 
                         style="width: 130px;"
                         v-model="allocationway" 
                         placeholder="分配方式"
                         @change="ksswitchtype"
                         >
                             <el-option
                                 v-for="list in way_list"
                                 :key="list.id"
                                 :label="list.user_name"
                                 :value="list.id">
                             </el-option>
                     </el-select>
                      <el-select 
                         style="margin-left: 10px;width: 130px;" 
                         :placeholder="allocationway=='1_1'?'维护人':'分组'"
                         :value="form_labels.follow_id ? form_labels.follow_id : form_labels.follow_group_id"
                         @input="form_labels.follow_id = $event; form_labels.follow_group_id = $event"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in (allocationway=='1_1'?user_list:respectuser_list)"
                             :key="list.id||list.values"
                             :label="list.user_name||list.name"
                             :value="list.id||list.values">
                         </el-option>
                      </el-select>
                     </div>
                 </div>
                 </div>
             </template>
             <!-- 找房实验室 (开放平台)-->
             <template v-if="form_labels.platform == 8||form_labels.platform == 9">
                 <div :style="form_labels.type == 1 ? 'margin-bottom: 22px;' : ''" class="new_Contact_Person">
                     <div class="dyhao">
                         线索分配：
                     </div>
                     <div>
                     <div class="allocation flex-row">
                            <el-select 
                                 style="width: 120px;margin-right:10px;"
                                 v-model="form_labels.create_id" 
                                
                                 placeholder="录入人"
                                 clearable
                                 filterable>
                                  <el-option
                                     v-for="list in user_list"
                                     :key="list.values"
                                     :label="list.name"
                                     :value="list.values">
                                 </el-option>
                            </el-select>
                     <el-select 
                         style="width: 130px;"
                         v-model="allocationway" 
                         placeholder="分配方式"
                         @change="ksswitchtype"
                         >
                             <el-option
                                 v-for="list in way_list"
                                 :key="list.id"
                                 :label="list.user_name"
                                 :value="list.id">
                             </el-option>
                     </el-select>
                      <el-select 
                         style="margin-left: 10px;width: 130px;" 
                         :placeholder="allocationway=='1_1'?'维护人':'分组'"
                         :value="form_labels.follow_id ? form_labels.follow_id : form_labels.follow_group_id"
                         @input="form_labels.follow_id = $event; form_labels.follow_group_id = $event"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in (allocationway=='1_1'?user_list:respectuser_list)"
                             :key="list.id||list.values"
                             :label="list.user_name||list.name"
                             :value="list.id||list.values">
                         </el-option>
                      </el-select>
                     </div>
                 </div>
                 </div>
             </template>
            <!-- 微信小店客服 -->
            <template v-if="form_labels.platform == 13">
             <div class="memberList">
             <div 
                 v-for="(item, index) in clueBelong_list"
                 :key="index"
             >
                 <div :style="item.type == 1 ? 'margin-bottom: 22px;' : ''" class="new_Contact_Person" style="margin-bottom:20px">
                             <div>
                                 <div>
                                     线索分配：
                                     <el-input 
                                         style="width: 172px;margin-left: 60px;"
                                         class="douyinID"
                                         v-model="item.unique_id" 
                                         placeholder="微信小店客服ID "
                                     >
                                     </el-input>
                                     <el-select 
                                         style="margin-left:5px;width: 77px;"
                                         v-model="item.status" 
                                         placeholder="状态"
                                         >
                                         <el-option
                                             v-for="list in user_status"
                                             :key="list.id"
                                             :label="list.name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                     <el-select 
                                         style="margin-left:5px;width: 140px;"
                                         v-model="item.create_id" 
                                         placeholder="录入人"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in user_list"
                                             :key="list.values"
                                             :label="list.name"
                                             :value="list.values">
                                         </el-option>
                                     </el-select>
                                 </div>
                                 <div style="margin-left: 134px;" class="allocation">
                                     <el-select 
                                     style="width: 116px;"
                                     v-model="item.allocationway" 
                                     placeholder="分配方式"
                                     @change="switchtype(item)"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                    <el-select 
                                        style="margin-left: 10px;width: 106px;"
                                         :placeholder="item.allocationway=='1_1'?'维护人':'分组'"
                                         :value="item.follow_id ? item.follow_id : item.follow_group_id"
                                         @input="item.follow_id = $event; item.follow_group_id = $event"
                                         @change="douyinwei($event,item, index)"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in (item.allocationway=='1_1'?user_list:respectuser_list)"
                                             :key="list.id||list.values"
                                             :label="list.user_name||list.name"
                                             :value="list.id||list.values">
                                         </el-option>
                                     </el-select>
                                     <el-cascader
                                         style="width: 156px;margin-left:10px"
                                         v-model="item.label"
                                         clearable
                                         :show-all-levels="false"
                                         placeholder="请选择标签"
                                         @change="handleLabelChange(item)"
                                         :options="label_list"
                                         :props="{
                                             value: 'id',
                                             label: 'name',
                                             children: 'label',
                                             emitPath: false,
                                             multiple: true,
                                             checkStrictly: true,
                                         }">
                                     </el-cascader>
                                 </div>
                             </div>
                     <i v-if="index == 0 && form_labels.platform == 13" @click="addClueBelong" class="el-icon-circle-plus-outline addBelong"></i>
                     <i v-else-if="index > 0 && form_labels.platform == 13" @click="delClueBelong(item,index)" class="el-icon-remove-outline delBelong"></i>
                 </div>
             </div>
             </div>
             </template>
             <el-form-item label="启用状态：">
                 <el-radio-group v-model="form_labels.status">
                     <el-radio :label="1">开启</el-radio>
                     <el-radio :label="0">关闭</el-radio>
                 </el-radio-group>
             </el-form-item>
             <el-form-item label="注意事项:" v-if="form_labels.platform == 1">
                 <p 
                 class="reminder">
                      1.如果上方规则指定维护人或分组将根据客户来源抖音号ID分配给指定成员或分组轮流分配（优先级大于线索分配里设置的分配规则）,未匹配到抖音ID的客户则会进入公海按照线索分配设置规则进行分配。 <br>
                      2.抖音ID状态设为关闭后则该抖音来源的线索不推送至系统，适合企
                     业号包含多个员工号的情景使用。
                 </p>
             </el-form-item>
             <el-form-item label="注意事项:" v-if="form_labels.platform == 10">
                    <p 
                    class="reminder">
                         1.首次配置，系统将自动同步近一周订单信息；后续自动同步频率，系统每隔30分钟拉取新增订单信息；
                    </p>
             </el-form-item>
         </el-form>
         <span slot="footer" class="dialog-footer">
             <el-button @click="EditVisible = false">取 消</el-button>
             <el-button type="primary" @click="ConfirmEdit" :loading="confirmLoading">确 定</el-button>
         </span>
         </el-dialog>
 <!-- 添加线索模态框 -->
         <el-dialog
           title="添加线索"
           :visible.sync="addVisible"
           :close-on-click-modal="false"
           width="666px"
         >
             <el-form :model="form_created" label-position="left" label-width="134px">
                 <el-form-item label="线索来源：">
                     <el-select v-model="form_created.platform" @change="changeAddPlat" 
                     style="width: 81%;" placeholder="请选择">
                        <el-option
                          v-for="item in platformoption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                 </el-form-item>
                 <el-form-item label="线索名称：">
                     <el-input
                         v-model="form_created.name"
                         style="width:400px"
                         :placeholder="form_created.platform == 1 ? '请输入抖音号名称' : form_created.platform == 2 ? '请输入快手号名称' : 
                          form_created.platform == 3 ?'请输入线索名称': form_created.platform == 4 ?'请输入视频号橱窗名称':form_created.platform == 5 ?'请输入飞鱼名称':
                          form_created.platform == 6 ?'请输入小红书线索名称':form_created.platform == 7 ?'请输入小红书线索名称':
                            form_created.platform == 8 ?'请输入找房实验室名称':form_created.platform == 9 ?'开放平台':
                            form_created.platform == 10 ?'微信小店': form_created.platform == 11?'腾讯广告':form_created.platform == 11
                            ?'视频号':'微信小店客服'"
                     ></el-input>
                 </el-form-item>
                 <!-- <el-form-item v-if="form_created.platform == 2" :label="form_created.platform == 1 ? '抖音号ID：' : '快手号ID：'">
                     <el-input
                         v-model="form_created.key"
                         style="width:400px"
                         :placeholder="form_created.type == 1 ? '请输入快手号ID (可选填)' : '请输入快手号ID (必填)'"
                     ></el-input>
                 </el-form-item> -->
                 <el-form-item label="小店id：" v-if="form_created.platform == 10">
                      <el-input
                          style="width:400px"
                          v-model="form_created.app_id"
                          placeholder="请输入小店id(必填)"
                      ></el-input>
                  </el-form-item>
                  <el-form-item label="小店密钥：" v-if="form_created.platform == 10">
                      <el-input
                          style="width:400px"
                          v-model="form_created.app_secret"
                          placeholder="请输入小店密钥(必填)"
                      ></el-input>
                  </el-form-item>
                 <el-form-item label="达人ID" v-if="form_created.platform == 3">
                     <el-input
                         v-model="form_created.key"
                         style="width:400px"
                         placeholder="请输入达人ID"
                     ></el-input>
                 </el-form-item>
                 <el-form-item label="SALT密钥" v-if="form_created.platform == 3">
                     <el-input
                         type="textarea"
                         :rows="2"
                         v-model="form_created.secret"
                         style="width:400px"
                         placeholder="请输入SALT密钥"
                     ></el-input>
                 </el-form-item>
                 <el-form-item label="视频号橱窗ID" v-if="form_created.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_created.key" 
                         placeholder="请输入视频号橱窗ID (必填)">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="账号秘钥" v-if="form_created.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_created.secret" 
                         placeholder="请输入账号秘钥">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息推送token令牌" v-if="form_created.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_created.token" 
                         placeholder="请输入消息推送token令牌(必填)">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息推送token令牌" v-if="form_created.platform == 10">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_created.token" 
                         placeholder="请输入消息推送token令牌">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息秘钥" v-if="form_created.platform == 10">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_created.message_secret" 
                         placeholder="请输入消息秘钥">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="消息推送秘钥" v-if="form_created.platform == 4">
                     <el-input 
                         style="width: 400px;"
                         class="douyinID"
                         v-model="form_created.encoding_aes_key" 
                         placeholder="请输入消息推送秘钥(必填)">
                         </el-input>
                 </el-form-item>
                 <el-form-item label="线索标签："
                 v-if="form_created.platform !== 1">
                     <el-cascader
                         style="width: 400px;"
                         v-model="clueLabel"
                         clearable
                         :show-all-levels="false"
                         placeholder="请选择标签"
                         :options="label_list"
                         :props="{
                             value: 'id',
                             label: 'name',
                             children: 'label',
                             emitPath: false,
                             multiple: true,
                             checkStrictly: true,
                         }"
                     >
                     </el-cascader>
                 </el-form-item>
                 <!-- 抖音 -->
                 <template v-if="form_created.platform == 1">
                     <div class="memberList">
                         <div 
                             v-for="(item, index) in clueBelong_list"
                             :key="index"
                         >
                             <div style="margin-bottom: 20px;" class="new_Contact_Person">
                                 <div class="dyhao">
                                     线索分配：
                                 </div>
                                 <div>
                                 <div> 
                                     <el-input 
                                         style="width: 172px;margin-left: 64px;"
                                         class="douyinID"
                                         v-model="item.dy_id" 
                                         placeholder="抖音号ID (必填)"
                                     >
                                     </el-input>
                                     <el-select 
                                         style="margin-left:5px;width: 77px;"
                                         v-model="item.status" 
                                         placeholder="状态"
                                         >
                                         <el-option
                                             v-for="list in user_status"
                                             :key="list.id"
                                             :label="list.name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                     <el-select 
                                         style="margin-left:5px;width: 140px;"
                                         v-model="item.create_id" 
                                         placeholder="录入人(可选填)"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in user_list"
                                             :key="list.values"
                                             :label="list.name"
                                             :value="list.values">
                                         </el-option>
                                     </el-select>
                                 </div>
                                 <div class="allocation">
                                     <el-select 
                                     style="width: 116px;"
                                     v-model="item.allocationway" 
                                     placeholder="分配方式"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                      <el-select 
                                        style="margin-left: 10px;width: 106px;"
                                         :placeholder="item.allocationway=='1_1'?'维护人':'分组'"
                                         v-model="item.Maintainer"
                                         @change="douyinwei($event,item, index)"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in (item.allocationway=='1_1'?user_list:respectuser_list)"
                                             :key="list.id||list.values"
                                             :label="list.user_name||list.name"
                                             :value="list.id||list.values">
                                         </el-option>
                                     </el-select>
                                     <el-cascader
                                         style="width: 156px;margin-left:10px"
                                         v-model="item.label"
                                         clearable
                                         :show-all-levels="false"
                                         placeholder="请选择标签"
                                         @change="handleLabelChange(item)"
                                         :options="label_list"
                                         :props="{
                                             value: 'id',
                                             label: 'name',
                                             children: 'label',
                                             emitPath: false,
                                             multiple: true,
                                             checkStrictly: true,
                                         }">
                                     </el-cascader>
                                 </div>
                             </div>
                                 <i v-if="index == 0 && form_created.platform == 1" @click="addClueBelong(item)" class="el-icon-circle-plus-outline addBelong"></i>
                                 <i v-else-if="index > 0 && form_created.platform == 1" @click="delClueBelong(item,index)" class="el-icon-remove-outline delBelong"></i>
                             </div>
                         </div>
                     </div>
                 </template>
                 <!-- 快手 -->
                 <template v-if="form_created.platform == 2||form_created.platform == 12">
                     <div style="margin-bottom: 22px;" class="new_Contact_Person">
 
                         <div class="dyhao">
                             线索分配：
                         </div>
                         <div>
                             <div>
                                 <el-select 
                                     style="margin-left: 64px;" 
                                     v-model="form_created.create_id" 
                                     @change="changAddType"
                                     placeholder="录入人"
                                     clearable
                                     filterable
                                 >
                                     <el-option
                                         v-for="list in user_list"
                                         :key="list.values"
                                         :label="list.name"
                                         :value="list.values">
                                     </el-option>
                                 </el-select>
                                 <el-input 
                                    v-if="form_created.platform == 2"
                                     style="width: 300px;"
                                     class="douyinID"
                                     v-model="form_created.key" 
                                     :placeholder="form_created.type == 1 ? '快手号ID (可选填)' : '快手号ID (必填)'"
                                 >
                                 </el-input>
                                 <el-input 
                                    v-if="form_created.platform == 12"
                                     style="width: 300px;"
                                     class="douyinID"
                                     v-model="form_created.account_id" 
                                     placeholder="视频号 (必填)"
                                 >
                                 </el-input>
                             </div>
                             <div class="allocation" style="margin-left:65px;">
                                 <el-select 
                                     style="width: 116px;"
                                     v-model="form_created.allocationway" 
                                     placeholder="分配方式"
                                     @change="ksswitchtype(1)"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                 <el-select 
                                      style="margin-left: 10px;width: 160px;"  
                                      :placeholder="form_created.allocationway=='1_1'?'维护人':'分组'"
                                      :value="form_created.follow_id ? form_created.follow_id : form_created.follow_group_id"
                                     @input="form_created.follow_id = $event; form_created.follow_group_id = $event"
                                     clearable
                                     filterable>
                                     <el-option
                                         v-for="list in (form_created.allocationway=='1_1'?user_list:respectuser_list)"
                                         :key="list.id||list.values"
                                         :label="list.user_name||list.name"
                                         :value="list.id||list.values">
                                     </el-option>
                                 </el-select>
                                 
                             </div>
                         </div>
                     </div>
                 </template>
                 <!-- 海豚知道 -->   
                 <template v-if="form_created.platform == 3">
                     <el-select 
                         style="margin-left: 135px;width:100px" 
                         v-model="form_created.follow_id" 
                         @change="changAddType"
                         placeholder="维护人"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in user_list"
                             :key="list.values"
                             :label="list.name"
                             :value="list.values">
                         </el-option>
                     </el-select>
                     <el-select 
                         style="margin-left: 10px;" 
                         v-model="form_created.create_id" 
                         @change="changAddType"
                         placeholder="录入人"
                         clearable
                         filterable>
                         <el-option
                             v-for="list in user_list"
                             :key="list.values"
                             :label="list.name"
                             :value="list.values">
                         </el-option>
                     </el-select>
                 </template>
                  <!-- 视频号橱窗 -->
                  <template v-if="form_created.platform == 4">
                     <div style="margin-bottom: 22px;margin-top:20px; ;" class="new_Contact_Person">
                         <div>
                             线索分配：
                         </div>
                         <div>
                             <div>
                                 <el-select 
                                     style="margin-left: 64px;" 
                                     v-model="form_created.create_id" 
                                     @change="changAddType"
                                     placeholder="录入人"
                                     clearable
                                     filterable
                                 >
                                     <el-option
                                         v-for="list in user_list"
                                         :key="list.values"
                                         :label="list.name"
                                         :value="list.values">
                                     </el-option>
                                 </el-select>
                             <!-- </div>
                             <div class="allocation" style="margin-left:43px:"> -->
                                 <el-select 
                                     style="width: 130px; margin-left:10px;"
                                     v-model="form_created.allocationway" 
                                     placeholder="分配方式"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                 <el-select 
                                      style="margin-left: 10px;width: 160px;"  
                                      :placeholder="form_created.allocationway=='1_1'?'维护人':'分组'"
                                      :value="form_created.follow_id ? form_created.follow_id : form_created.follow_group_id"
                                     @input="form_created.follow_id = $event; form_created.follow_group_id = $event"
                                     clearable
                                     filterable>
                                     <el-option
                                         v-for="list in (form_created.allocationway=='1_1'?user_list:respectuser_list)"
                                         :key="list.id||list.values"
                                         :label="list.user_name||list.name"
                                         :value="list.id||list.values">
                                     </el-option>
                                 </el-select>
                                 
                             </div>
                         </div>
                     </div>
                 </template>
                 <!-- 飞鱼 -->
                 <template v-if="form_created.platform == 5">
                     <div style="margin-bottom: 22px;" class="new_Contact_Person">
 
                         <div>
                             线索分配：
                         </div>
                         <div>
                             <div>
                                 <el-select 
                                     style="margin-left: 64px;" 
                                     v-model="form_created.create_id" 
                                     @change="changAddType"
                                     placeholder="录入人"
                                     clearable
                                     filterable
                                 >
                                     <el-option
                                         v-for="list in user_list"
                                         :key="list.values"
                                         :label="list.name"
                                         :value="list.values">
                                     </el-option>
                                 </el-select>
                                 <el-select 
                                     style="width: 129px; margin-left:10px;"
                                     v-model="form_created.allocationway" 
                                     placeholder="分配方式"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                 <el-select 
                                      style="margin-left: 10px;width: 160px;"  
                                      :placeholder="form_created.allocationway=='1_1'?'维护人':'分组'"
                                      :value="form_created.follow_id ? form_created.follow_id : form_created.follow_group_id"
                                     @input="form_created.follow_id = $event; form_created.follow_group_id = $event"
                                     clearable
                                     filterable>
                                     <el-option
                                         v-for="list in (form_created.allocationway=='1_1'?user_list:respectuser_list)"
                                         :key="list.id||list.values"
                                         :label="list.user_name||list.name"
                                         :value="list.id||list.values">
                                     </el-option>
                                 </el-select>
                                 
                             </div>
                         </div>
                     </div>
                 </template>
                <!-- 小红书&&微信小店&&腾讯广告 -->
                <template v-if="form_created.platform == 6||form_created.platform == 7||form_created.platform == 10
                ||form_created.platform == 11">
                     <div style="margin-bottom: 22px;" class="new_Contact_Person">
                         <div class="dyhao">
                             线索分配：
                         </div>
                         <div>
                             <div>
                                 <el-select 
                                     style="margin-left: 64px;width: 105px;" 
                                     v-model="form_created.create_id" 
                                     @change="changAddType"
                                     placeholder="录入人"
                                     clearable
                                     filterable
                                 >
                                     <el-option
                                         v-for="list in user_list"
                                         :key="list.values"
                                         :label="list.name"
                                         :value="list.values">
                                     </el-option>
                                 </el-select>
                             <!-- </div> -->
                             <!-- <div class="allocation" style="margin-left:65px;"> -->
                                 <el-select 
                                     style="width: 116px;margin-left:10px;"
                                     v-model="form_created.allocationway" 
                                     placeholder="分配方式"
                                     @change="ksswitchtype(1)"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                 <el-select 
                                      style="margin-left: 10px;width: 160px;"  
                                      :placeholder="form_created.allocationway=='1_1'?'维护人':'分组'"
                                      :value="form_created.follow_id ? form_created.follow_id : form_created.follow_group_id"
                                     @input="form_created.follow_id = $event; form_created.follow_group_id = $event"
                                     clearable
                                     filterable>
                                     <el-option
                                         v-for="list in (form_created.allocationway=='1_1'?user_list:respectuser_list)"
                                         :key="list.id||list.values"
                                         :label="list.user_name||list.name"
                                         :value="list.id||list.values">
                                     </el-option>
                                 </el-select>
                                 
                             </div>
                         </div>
                     </div>
                 </template>
                 <!-- 找房实验室(开放平台) -->
                 <template v-if="form_created.platform == 8||form_created.platform == 9">
                     <div style="margin-bottom: 22px;" class="new_Contact_Person">
                         <div class="dyhao">
                             线索分配：
                         </div>
                         <div>
                             <div>
                                 <el-select 
                                     style="margin-left: 64px;width: 105px;" 
                                     v-model="form_created.create_id" 
                                     @change="changAddType"
                                     placeholder="录入人"
                                     clearable
                                     filterable
                                 >
                                     <el-option
                                         v-for="list in user_list"
                                         :key="list.values"
                                         :label="list.name"
                                         :value="list.values">
                                     </el-option>
                                 </el-select>
                             <!-- </div> -->
                             <!-- <div class="allocation" style="margin-left:65px;"> -->
                                 <el-select 
                                     style="width: 116px;margin-left:10px;"
                                     v-model="form_created.allocationway" 
                                     placeholder="分配方式"
                                     @change="ksswitchtype(1)"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                 <el-select 
                                      style="margin-left: 10px;width: 160px;"  
                                      :placeholder="form_created.allocationway=='1_1'?'维护人':'分组'"
                                      :value="form_created.follow_id ? form_created.follow_id : form_created.follow_group_id"
                                     @input="form_created.follow_id = $event; form_created.follow_group_id = $event"
                                     clearable
                                     filterable>
                                     <el-option
                                         v-for="list in (form_created.allocationway=='1_1'?user_list:respectuser_list)"
                                         :key="list.id||list.values"
                                         :label="list.user_name||list.name"
                                         :value="list.id||list.values">
                                     </el-option>
                                 </el-select>
                                 
                             </div>
                         </div>
                     </div>
                 </template>
                <!-- 微信小店客服 -->
                <template v-if="form_created.platform == 13">
                     <div class="memberList">
                         <div 
                             v-for="(item, index) in clueBelong_list"
                             :key="index"
                         >
                             <div style="margin-bottom: 20px;" class="new_Contact_Person">
                                 <div class="dyhao">
                                     线索分配：
                                 </div>
                                 <div>
                                 <div> 
                                     <el-input 
                                         style="width: 172px;margin-left: 64px;"
                                         class="douyinID"
                                         v-model="item.unique_id" 
                                         placeholder="微信小店客服ID"
                                     >
                                     </el-input>
                                     <el-select 
                                         style="margin-left:5px;width: 77px;"
                                         v-model="item.status" 
                                         placeholder="状态"
                                         >
                                         <el-option
                                             v-for="list in user_status"
                                             :key="list.id"
                                             :label="list.name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                     <el-select 
                                         style="margin-left:5px;width: 140px;"
                                         v-model="item.create_id" 
                                         placeholder="录入人"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in user_list"
                                             :key="list.values"
                                             :label="list.name"
                                             :value="list.values">
                                         </el-option>
                                     </el-select>
                                 </div>
                                 <div class="allocation">
                                     <el-select 
                                     style="width: 116px;"
                                     v-model="item.allocationway" 
                                     placeholder="分配方式"
                                     >
                                         <el-option
                                             v-for="list in way_list"
                                             :key="list.id"
                                             :label="list.user_name"
                                             :value="list.id">
                                         </el-option>
                                     </el-select>
                                      <el-select 
                                        style="margin-left: 10px;width: 106px;"
                                         :placeholder="item.allocationway=='1_1'?'维护人':'分组'"
                                         v-model="item.Maintainer"
                                         @change="douyinwei($event,item, index)"
                                         clearable
                                         filterable>
                                         <el-option
                                             v-for="list in (item.allocationway=='1_1'?user_list:respectuser_list)"
                                             :key="list.id||list.values"
                                             :label="list.user_name||list.name"
                                             :value="list.id||list.values">
                                         </el-option>
                                     </el-select>
                                     <el-cascader
                                         style="width: 156px;margin-left:10px"
                                         v-model="item.label"
                                         clearable
                                         :show-all-levels="false"
                                         placeholder="请选择标签"
                                         @change="handleLabelChange(item)"
                                         :options="label_list"
                                         :props="{
                                             value: 'id',
                                             label: 'name',
                                             children: 'label',
                                             emitPath: false,
                                             multiple: true,
                                             checkStrictly: true,
                                         }">
                                     </el-cascader>
                                 </div>
                             </div>
                                 <i v-if="index == 0 && form_created.platform == 13" @click="addClueBelong(item)" class="el-icon-circle-plus-outline addBelong"></i>
                                 <i v-else-if="index > 0 && form_created.platform == 13" @click="delClueBelong(item,index)" class="el-icon-remove-outline delBelong"></i>
                             </div>
                         </div>
                     </div>
                 </template>
                 <!-- <el-form-item 
                     label="关联成员：" 
                     v-show="form_created.platform == 2 || form_created.platform == 3">
                     <el-select 
                         v-model="form_created.uid" 
                         @change="changeCorrelation"
                         placeholder="请选择关联成员" 
                         style="width:300px"
                     >
                         <el-option
                             v-for="item in user_list"
                             :key="item.id"
                             :label="item.user_name"
                             :value="item.id">- 
                         </el-option>
                     </el-select>
                 </el-form-item> -->
                 <el-form-item label="启用状态：">
                     <el-radio v-model="form_created.status" :label="1">开启</el-radio>
                     <el-radio v-model="form_created.status" :label="0">关闭</el-radio>
                 </el-form-item>
                 <el-form-item label="注意事项:" v-if="form_created.platform == 1">
                    <p 
                    class="reminder">
                         1.如果上方规则指定维护人或分组将根据客户来源抖音号ID分配给指定成员或分组轮流分配（优先级大于线索分配里设置的分配规则）,未匹配到抖音ID的客户则会进入公海按照线索分配设置规则进行分配。 <br>
                         2.抖音ID状态设为关闭后则该抖音来源的线索不推送至系统，适合企
                        业号包含多个员工号的情景使用。
                    </p>
                 </el-form-item>
                 <el-form-item label="注意事项:" v-if="form_created.platform == 10">
                    <p 
                    class="reminder">
                         1.首次配置，系统将自动同步近一周订单信息；后续自动同步频率，系统每隔30分钟拉取新增订单信息；
                    </p>
                 </el-form-item>
             </el-form>
             <span slot="footer" class="dialog-footer">
                 <el-button @click="addVisible = false">取 消</el-button>
                 <el-button type="primary" @click="ConfirmAdd" :loading="confirmLoading">确 定</el-button>
             </span>
         </el-dialog>

         <updateEntryPerson ref="updateEntryPerson" v-if="dialogs.updateEntryPerson"/>
    </div>
 </template>
 <script>
 import updateEntryPerson from "./cus_TikTok/updateEntryPerson";
 export default {
    components: {
         updateEntryPerson,
    },
     data() {
         return {
             TikTokDataList: [],
             is_table_loading: false,
             EditVisible: false, // 编辑模态框显示隐藏
             addVisible: false, // 添加模态框显示隐藏
             form_labels: {},
             // 添加线索
             form_created: {
                //  key: "", // 抖音号
                 name: "", // 线索名称
                 follow_id: "", // 维护人
                 create_id:"",//录入人
                 status: 1,
                 label: "",
                 platform: 1, // 线索来源
                 follow_group_id:"",
                 allocationway:"1_1"
             },
             allocationway:"",
             // douyinallocationway:"",
             clueLabel: "", // 线索标签
             EditIndex: "", // 保存当前点击编辑的下标
             way_list: [
                 {id:"1_1",user_name:"指定维护人"},
                 {id:"2_1",user_name:"按分组分配"}
             ], 
             user_status:[
                 {id:0,name:"关闭"},
                 {id:1,name:"开启"}
             ],//状态
             user_list:[],// 关联成员列表
             respectuser_list:[],//维护人成员列表
             params: {
                 page: 1,
                 per_page: 100,
                 type: 0, // 1:审批权限列表,2:成交分佣权限列表3:标记无效权限列表
             },
             label_list: [], // 存储接口调取的客户标签
             // 线索归属类型
             douyin_number_type: [
                 {
                     name: '公海',
                     values: 1
                 },
                 {
                     name: '私客',
                     values: 2
                 }
             ],
             //线索来源
             platformoption:[
                {value:1,label:"抖音"},
                {value:2,label:"快手"},
                {value:4,label:"视频号橱窗"},
                {value:5,label:"飞鱼"},
                {value:3,label:"海豚知道"},
                {value:6,label:"小红书私信"},
                {value:7,label:"小红书表单"},
                {value:8,label:"找房实验室"},
                {value:9,label:"开放平台"},
                {value:10,label:"微信小店"},
                {value:11,label:"腾讯广告"},
                {value:12,label:"视频号"},
                {value:13,label:"微信小店客服"}
             ],
             // 线索归属列表
             clueBelong_list: [
                 {
                     // type: 1, // 归属 1公海 2私客
                     follow_id: '', // 维护人
                     dy_id: '', // 抖音id
                     user_name: '', // 成员名称
                     create_id: '', // 录入人
                     follow_group_id:"",//分组
                     allocationway:"1_1",//分配方式
                     clueLabel:"",//线索
                     Maintainer:"",
                     label:"",
                     status:1,//状态
                 }
             ],
             clueTitle: '添加线索', // 模态框title
             row_params: {}, // 点击编辑获得当前表格的参数
             deep_row_params: {}, // 深度拷贝当前表格的参数
             confirmLoading: false, // 加载动画
             Maintainer:"",//存储抖音录入人 
             website_id:"",//站点ID
             respectuser:[],
             radio1:"",//留资去重
             dialogs: {
                updateEntryPerson: false
             },
             currentPage: 1, // 当前页码
             total: 0, // 总条数
             pageSize: 10, // 每页的数据条数
             input3:"",
             sourceoptions: [{
                  value: 1,
                  label: '抖音'
                }, {
                  value: 2,
                  label: '快手'
                }, {
                  value: 3,
                  label: '海豚知道'
                }, {
                  value: 4,
                  label: '视频号橱窗'
                }, {
                  value: 5,
                  label: '飞鱼'
                }, 
                {
                    value: 6,
                    label: '小红书私信'
                }, {
                    value: 7,
                    label: '小红书表单'
                }, {
                    value:8,
                    label:"找房实验室"
                }, 
                {
                    value:9,
                    label:"开放平台"
                },
                {
                    value:10,
                    label:"微信小店"
                }, 
                {
                    value:11,
                    label:"腾讯广告"
                },
                {
                    value:12,
                    label:"视频号"
                },
                {   value:13,
                    label:"微信小店客服"
                },
            ],
             sourcevalue:"",
             statusvalue:"",
             backupsdata:[],//备份数据
             batchopen:"",
             multipleSelection: [],
             backupscurrentPage:"",//备份页码
             duration:"0",
             max_no_tel_unit:"3",
             limiter:"",
         }
     },
     mounted() {
        let pagenum = localStorage.getItem( 'pagenum')
        this.pageSize= Number(pagenum)||10
        // 赋值website_id
        if (this.$route.query.website_id) {
          this.website_id = this.$route.query.website_id;
        }
         this.getTikTokConfig();
         this.getManagerAuthList();
         this.getLabelList();
         this.getassociationsatff()
     },
     filters: {
         // 处理关联成员数据
         AssociatedMember(val) {
             // 如果是新版抖音
             if(val.list) {
                 let num = [];
                 const nonEmptyValues = val.list.filter(item => item.user_name !== ''); // 过滤user_name为空的数据
                 // console.log(nonEmptyValues,"nonEmptyValues")
                 nonEmptyValues.map((item) => {
                     if(item && item.user_name != '') {
                         num.push(item.user_name);
                     }
                 })
                 if (nonEmptyValues.length === 0) {
                     return '公海';
                 } else {
                     return num.join(",");
                 }
             } else {
                 return val.user_name ? val.user_name : '公海';
             }
         },
         // 处理线索归属数据
         AttributionClue(val) {
             // 如果是新版抖音
             if(val.list) {
                 const nonEmptyValues = val.list.filter(item => item.type !== 1); // 过滤user_name为空的数据
                 if (nonEmptyValues.length === 0) {
                     return '公海';
                 } else {
                     return '私客';
                 }
             } else {
                 return val.type == 1 ? '公海' : '私客';
             }
         }
     },
     watch: {
         // 监听修改线索中线索标签发生改变
         // 'form_labels.label': {
         //     handler(newVal, oldVal) {
         //         console.log(newVal);
         //         if(newVal.length&&newVal.length > 4) {
         //             this.$message.warning("最多只支持选择4项");
         //             this.$nextTick(() => {
         //                 this.form_labels.label = oldVal;
         //             })
         //         }
         //     }
         // },
         clueLabel:{
             handler(newVal, oldVal) {
                 if(newVal.length > 4) {
                     this.$message.warning("最多只支持选择4项");
                     this.$nextTick(() => {
                         this.clueLabel = oldVal;
                     })
                 }
             }
         },
         // 监听添加线索中线索标签发生变化
         // clueBelong_list: {
         //     handler(newVal, oldVal) {
         //         console.log(newVal, oldVal);
         //         // if(newVal.length > 4) {
         //         //     this.$message.warning("最多只支持选择4项");
         //         //     this.$nextTick(() => {
         //         //         this.clueLabel = oldVal;
         //         //     })
         //         // }
         //     }
         // }
     },
     methods: {
        //获取留资是否去重
        getClueAllocation() {
        this.$http.getClueAllocation().then((res) => {
          if (res.status == 200) {
            this.radio1 = res.data.push_repeat

          }
        })
      },
         // 获取抖音线索配置
         getTikTokConfig() {
            this.getClueAllocation()
             this.is_table_loading = true;
             this.$http.newgetTikTokConfig().then(res => {
                 this.is_table_loading = false;
                 if(res.status == 200) {
                     this.backupsdata = res.data
                     this.TikTokDataList = res.data;
                 }
             })
         },
         // 复制文本
         copy(row) {
             this.$onCopyValue(row.url);
         },
         onClickForm() {
             this.$http.updataTikTokConfig(this.form_info).then(res => {
                 if(res.status == 200) {
                     this.$message({
                         message: '操作成功',
                         type: 'success'
                     });
                     this.getTikTokConfig();
                 }
             }).catch(() => {
                 this.$message.error('操作失败');
             })
         },
         //抖音切换分组或者成员
         switchtype(item){
           if(item.allocationway=="2_1"){
            return item.follow_id = ""
           }else{
            return item.follow_group_id = ""
           }
         },
         //快手,视频号橱窗，飞鱼切换分组或者成员
         ksswitchtype(status){
            if(status&&status==1){
                if(this.allocationway=="2_1"){
                  this.form_created.follow_id = ""
                  this.form_created.follow_group_id = ""
                }else{
                  this.form_created.follow_group_id = ""
                  this.form_created.follow_id = ""
                }
            }else{
                if(this.allocationway=="2_1"){
                  this.form_labels.follow_id = ""
                  this.form_labels.follow_group_id = ""
                }else{
                  this.form_labels.follow_group_id = ""
                  this.form_labels.follow_id = ""
                }
            }   
         },
         // 点击编辑
         onChangeEdit(row,index) {
            //维护组的id储存
            this.respectuser = this.respectuser_list.map(itm=>{
                // console.log(typeof(itm.id));
                return itm.id
              })
             // 优化:如果参数为空则添加参数
            //  console.log(row,index,"编辑数据");
             if(row.list && row.list.length == 0) {
                 row.list.push({
                     // type: 1, // 归属 1公海 2私客
                     create_id: "",			// 录入人
                     follow_id: "",			// 维护人
                     follow_group_id:"",	// 维护组
                     label: "",
                     allocationway:"1_1",//分配方式
                     status:1,//状态
                 })
             }
            //  console.log(row,"row")
             this.deep_row_params = JSON.parse(JSON.stringify(row));
             this.row_params = row;
             this.EditIndex = index; // 当table的index
             this.EditVisible = true; // 显示模态框
             // 如果存在list抖音参数执行
             if(row.list && row.list.length) {
                 // 将线索标签字符串转数组
                 // if(row.list.label != "" && row.list.label != undefined && typeof(row.label) == 'string') {
 
                     this.form_labels = {
                         key: row.key,
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         label:row.label
                     }
                     
                     this.clueBelong_list = row.list;
                     // // this.clueBelong_list.label = row.label.split(",")
                     // // 将接口参数字符串成员转换数字
                    //  console.log(this.clueBelong_list);
                     this.clueBelong_list.map((item) => {
                         if(item.label&&typeof(item.label) == 'string'){
                             item.label=item.label.split(",")
                         }
                         if(item.create_id == "0" ||item.create_id==''||item.create_id==undefined||item.create_id==null){
                             this.$set(item,"create_id", "") 
                         }
                         if(item.follow_id != '' && item.follow_id != undefined) {
                             item.follow_id = parseInt(item.follow_id);
                            this.$set(item,"allocationway", "1_1") 
                         }
                         if (this.respectuser.includes(Number(item.follow_group_id))) {
                        //   console.log(`包含`);
                        } else {
                            item.follow_group_id = ""
                        }
                         if(item.follow_id==0&&item.follow_group_id==0||item.follow_group_id==""&&item.follow_id ==""){
                             this.$set(item,"allocationway", "1_1") 
                         }
                         if(item.follow_group_id!=""&&item.follow_group_id!=undefined){
                             item.follow_group_id = parseInt(item.follow_group_id);
                             this.$set(item,"allocationway", "2_1") 
                         }
                     })
                     if(row.platform == 13){
                        if(row.label != "" && row.label != undefined && typeof(row.label) == 'string') {
                            this.form_labels.label = row.label.split(",")
                        }
                     }
                    //  console.log(this.clueBelong_list);
             } else if(row.platform == 2||row.platform == 12) { 
                 // 如果是快手执行
                 // 将线索标签字符串转数组 
                
                 if(row.label != "" && row.label != undefined && typeof(row.label) == 'string') {
                    //  console.log("快手执行");
                    if(row.platform == 2){
                        this.form_labels = {
                            key: row.key,
                            label: row.label.split(","),
                            name: row.name,
                            status: row.status,
                            platform: row.platform,
                            create_id: row.create_id,
                            follow_id: row.follow_id,
                            follow_group_id:row.follow_group_id
                        }
                    }
                    if(row.platform == 12){
                        this.form_labels = {
                            account_id: row.account_id,
                            label: row.label.split(","),
                            name: row.name,
                            status: row.status,
                            platform: row.platform,
                            create_id: row.create_id,
                            follow_id: row.follow_id,
                            follow_group_id:row.follow_group_id
                        }
                    }
                    // console.log(this.respectuser);
                    // console.log(this.form_labels.follow_group_id);
                    if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(this.form_labels.follow_id&&this.form_labels.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     if(this.form_labels.create_id==0){
                         this.form_labels.create_id = ''
                     }
                     if(this.form_labels.follow_id==0){
                         this.form_labels.follow_id = ''
                     }
                    //  console.log(this.form_labels);
                 }else{
                    //  console.log(row,"*************");
                     if(row){
                        if(row.platform == 2){
                            this.form_labels = {
                                key: row.key,
                                label: row.label.split(","),
                                name: row.name,
                                status: row.status,
                                platform: row.platform,
                                create_id: row.create_id,
                                follow_id: row.follow_id,
                                follow_group_id:row.follow_group_id
                            }
                        }
                        if(row.platform == 12){
                            this.form_labels = {
                                account_id: row.account_id,
                                label: row.label.split(","),
                                name: row.name,
                                status: row.status,
                                platform: row.platform,
                                create_id: row.create_id,
                                follow_id: row.follow_id,
                                follow_group_id:row.follow_group_id
                            }
                        }
                        //  console.log(row,"*************");
                     if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(row.follow_id&&row.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     // if(this.form_labels.create_id==0){
                     //     this.form_labels = ''
                     // }
                     // if(this.form_labels.follow_id==0){
                     //     this.follow_id = ''
                     // }
                     }
 
                 }
 
             } else if(row.platform == 3) { 
                 // 如果是海豚知道
                 // 将线索标签字符串转数组
                 if(row.label != "" && row.label != undefined && typeof(row.label) == 'string') {
                     this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         secret: row.secret,
                     }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(this.form_labels.follow_id == "0" ||this.form_labels.follow_id==''||this.form_labels.follow_id==undefined||this.form_labels.follow_id==null){
                         this.$set(this.form_labels,"follow_id", "") 
                     }
                 } else {
                     this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         secret: row.secret,
                     }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(this.form_labels.follow_id == "0" ||this.form_labels.follow_id==''||this.form_labels.follow_id==undefined||this.form_labels.follow_id==null){
                         this.$set(this.form_labels,"follow_id", "") 
                     }
                 }
             } else if(row.platform == 4) {
                 if(row.label != "" && row.label != undefined && typeof(row.label) == 'string') {
                    //  console.log("视频号执行");
                     this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         follow_group_id:row.follow_group_id,
                         secret:row.secret,
                         token:row.token,
                         encoding_aes_key:row.encoding_aes_key
                     }
                     if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                    //  console.log(this.form_labels);
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(this.form_labels.follow_id&&this.form_labels.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     if(this.form_labels.create_id==0){
                         this.form_labels.create_id = ''
                     }
                     if(this.form_labels.follow_id==0){
                         this.form_labels.follow_id = ''
                     }
                    //  console.log(this.form_labels);
                 }else{
                    //  console.log(row,"*************");
                     if(row){
                        //  console.log(row,"*************");
                         this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         follow_group_id:row.follow_group_id,
                         secret:row.secret,
                         token:row.token,
                         encoding_aes_key:row.encoding_aes_key
                     }
                     if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(row.follow_id&&row.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     }
 
                 }
             } else if(row.platform == 5) {
                if(row.label != "" && row.label != undefined && typeof(row.label) == 'string') {
                    //  console.log("飞鱼号执行");
                     this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         follow_group_id:row.follow_group_id
                     }
                     if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                    //  console.log(this.form_labels);
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(this.form_labels.follow_id&&this.form_labels.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     if(this.form_labels.create_id==0){
                         this.form_labels.create_id = ''
                     }
                     if(this.form_labels.follow_id==0){
                         this.form_labels.follow_id = ''
                     }
                    //  console.log(this.form_labels);
                 }else{
                    //  console.log(row,"*************");
                     if(row){
                        //  console.log(row,"*************");
                         this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         follow_group_id:row.follow_group_id
                     }
                     if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(row.follow_id&&row.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     }
 
                 }
             }else if(row.platform == 6||row.platform == 7||row.platform == 8||row.platform == 9||row.platform == 10
             ||row.platform == 11) { 
                 // 如果是小红书执行
                 // 将线索标签字符串转数组 
                
                 if(row.label != "" && row.label != undefined && typeof(row.label) == 'string') {
                     this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         follow_group_id:row.follow_group_id,
                         app_id:row.app_id,
                         app_secret: row.app_secret,
                         token: row.token,
                         message_secret: row.message_secret
                     }
                    //  if(row.app_id){
                    //     this.form_labels.app_id = row.app_id
                    //     this.form_labels.app_secret = row.app_secret
                    //     this.form_labels.token = row.token
                    //     this.form_labels.message_secret = row.message_secret
                    //  }

                    // console.log(this.respectuser);
                    // console.log(this.form_labels.follow_group_id);
                    if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(this.form_labels.follow_id&&this.form_labels.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     if(this.form_labels.create_id==0){
                         this.form_labels.create_id = ''
                     }
                     if(this.form_labels.follow_id==0){
                         this.form_labels.follow_id = ''
                     }
                    //  console.log(this.form_labels);
                 }else{
                    //  console.log(row,"*************");
                     if(row){
                        //  console.log(row,"*************");
                         this.form_labels = {
                         key: row.key,
                         label: row.label.split(","),
                         name: row.name,
                         status: row.status,
                         platform: row.platform,
                         create_id: row.create_id,
                         follow_id: row.follow_id,
                         follow_group_id:row.follow_group_id,
                         app_id:row.app_id,
                         app_secret: row.app_secret,
                         token: row.token,
                         message_secret: row.message_secret
                     }
                    //  if(row.app_id){
                    //     this.form_labels.app_id = row.app_id
                    //     this.form_labels.app_secret = row.app_secret
                    //     this.form_labels.token = row.token
                    //     this.form_labels.message_secret = row.message_secret
                    //  }
                     if (this.respectuser.includes(Number(this.form_labels.follow_group_id))) {
                    //   console.log(`包含`);
                    } else {
                        this.form_labels.follow_group_id = ""
                    }
                     if(this.form_labels.create_id == "0" ||this.form_labels.create_id==''||this.form_labels.create_id==undefined||this.form_labels.create_id==null){
                             this.$set(this.form_labels,"create_id", "") 
                         }
                     if(row.follow_id&&row.follow_id!=""){
                         this.allocationway = "1_1"
                     }else{
                         this.allocationway = "2_1"
                     }
                     if(this.form_labels.follow_group_id){
                         this.form_labels.follow_group_id = Number(this.form_labels.follow_group_id)
                     }
                     if(this.form_labels.follow_id){
                         this.form_labels.follow_id = Number(this.form_labels.follow_id)
                     }
                     }
                 }
             }
         },
         //保存时间
         preservetime(row){
            // console.log(row);
            this.limiter = row.limiter
         },
         settime(row){
         if(row.limiter<1){
             this.$message.warning("时间不能为空，必须大于1")
             return row.limiter = this.limiter
         }
             let DataListPush = {auto_push: this.TikTokDataList}
             this.$http.newupdataTikTokConfig(
                 DataListPush
             ).then((res) => {
                 if(res.status == 200) {
                     this.$message({
                         message: '操作成功',
                         type: 'success'
                     });
                     this.getTikTokConfig();
                 }
             }).catch(() => {
                 this.getTikTokConfig();
             })
         },
         // 确定编辑
         ConfirmEdit() {
            //  console.log(this.form_labels,"this.form_labels数据")
             this.confirmLoading = true; // loading
             if(this.form_labels.name == "") {
                 this.confirmLoading = false;
                 return this.$message({
                     message: '线索名称不能为空',
                     type: 'warning'
                 });
             }
             if( this.form_labels.platform == 2||this.form_labels.platform == 3){
                //  console.log(this.form_labels.label);
             if(this.form_labels.label.length > 4) {
                 this.confirmLoading = false;
                 return this.$message({
                     message: '线索标签最多只支持选择4项',
                     type: 'warning'
                 });
             }
             }
             let control = false;
             // 如果是新版抖音
             // console.log(this.form_labels);
             if(this.form_labels.platform == 1||this.form_labels.platform == 13) {
                //  console.log("执行新版抖音")
                 // 如果是公海并且没有填写抖音号ID数据
                 // const filteredData = this.row_params.list.filter(list => list.dy_id == '' && list.type == 1);
                 // if(filteredData.length == this.row_params.list.length) {
                 //     this.row_params.list = [];
                 // }
             if (this.clueBelong_list.length&&this.form_labels.platform == 1) {
                 // 使用 some 方法检查是否有元素的 dy_id 为空
                 const isEmptyDyId = this.clueBelong_list.some(item => !item.dy_id);
                 if (isEmptyDyId) {
                     this.confirmLoading = false;
                     return this.$message.warning("请填写抖音号！");
                 }
             }
             
             if (this.clueBelong_list.length&&this.form_labels.platform == 13) {
                 // 使用 some 方法检查是否有元素的 dy_id 为空
                 const isEmptyDyId = this.clueBelong_list.some(item => !item.unique_id);
                 if (isEmptyDyId) {
                     this.confirmLoading = false;
                     return this.$message.warning("请填写微信小店客服ID号！");
                 }
             }
                 let effectives = [];
                 // 如果填写了抖音号ID数据
                 if(this.clueBelong_list.length) {
                     this.clueBelong_list.map(item => {
                         if(control) {
                             return this.confirmLoading = false;
                         }
                         if(this.form_labels.platform == 1){
                            if(item.label){
                                effectives.push({
                                follow_id: item.follow_id,
                                dy_id: item.dy_id,
                                label: item.label.join(","),
                                create_id: item.create_id,
                                follow_group_id:item.follow_group_id,
                                status:item.status,
                            });
                            }else{
                                effectives.push({
                                follow_id: item.follow_id,
                                dy_id: item.dy_id,
                                label: '',
                                create_id: item.create_id,
                                follow_group_id:item.follow_group_id,
                                status:item.status,
                            });
                            }
                         }else{
                            console.log(item,"weixin 小店");
                            if(item.label.length){
                                effectives.push({
                                follow_id: item.follow_id,
                                unique_id: item.unique_id,
                                label: item.label.join(","),
                                create_id: item.create_id,
                                follow_group_id:item.follow_group_id,
                                status:item.status,
                             });
                             }else{
                                 effectives.push({
                                 follow_id: item.follow_id,
                                 unique_id: item.unique_id,
                                 label: '',
                                 create_id: item.create_id,
                                 follow_group_id:item.follow_group_id,
                                 status:item.status,
                             });
                             }
                         }
                       
                     })
                 }
                 // 删除多余参数 secret: 海豚知道密钥
                //  if(this.form_labels.secret) {
                //      delete this.form_labels.secret
                //  }
                 if(this.form_labels.key) {
                     this.TikTokDataList[this.EditIndex].key = this.form_labels.key;
                 }
                 this.TikTokDataList[this.EditIndex].name = this.form_labels.name;
                 this.TikTokDataList[this.EditIndex].status = this.form_labels.status;
                 this.TikTokDataList[this.EditIndex].platform = this.form_labels.platform;
                    if(this.form_labels.platform == 13){
                       if(this.form_labels.label && Array.isArray(this.form_labels.label)) {
                         this.TikTokDataList[this.EditIndex].label = this.form_labels.label.join(",");
                    } 
                 }
              
                 this.TikTokDataList[this.EditIndex].list = effectives;
                 // this.form_labels.list.map((list) => {
                 //     if(list.type == 1) {
                 //         list.uid = '';
                 //     }
                 // })
                //  console.log(this.form_labels);
                //  console.log(this.TikTokDataList);
             } else {
                 if(this.form_labels.type == 2 && (this.form_labels.uid == '' || this.form_labels.uid == undefined)) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请选择维护人',
                         type: 'warning'
                     });
                 }
                 // 判断抖音快手号不能为空
                 if(this.form_labels.key == "" && this.form_labels.platform == 2||this.form_labels.key == "" && this.form_labels.platform == 1) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: `${this.form_labels.platform == 1 ? '抖音号' : '快手号'}不能为空`,
                         type: 'warning'
                     });
                 }
                 // 判断海豚知道号不能为空
                 if(this.form_labels.key == "" && this.form_labels.platform == 3) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: "请输入达人ID",
                         type: 'warning'
                     });
                 }
                // 判断视频号橱窗ID不能为空
                if(this.form_labels.key == "" && this.form_labels.platform == 4) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: "请输入视频号橱窗ID",
                         type: 'warning'
                     });
                 }
                 if(this.form_labels.app_id == "" && this.form_labels.platform == 10) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: "请输入小店id ",
                         type: 'warning'
                     });
                 }
                 if(this.form_labels.app_secret == "" && this.form_labels.platform == 10) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: "请输入小店秘钥 ",
                         type: 'warning'
                     });
                 }
                 if(this.form_labels.secret == ''  && this.form_labels.platform == 4) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入账号秘钥',
                         type: 'warning'
                     });
                 }
                 if(this.form_labels.token == ''  && this.form_labels.platform == 4) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入消息推送token令牌',
                         type: 'warning'
                     });
                 }
                 if(this.form_labels.encoding_aes_key == ''  && this.form_labels.platform == 4) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入消息推送秘钥',
                         type: 'warning'
                     });
                 }
                 if(this.form_labels.account_id == ''  && this.form_labels.platform == 12) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入视频号ID',
                         type: 'warning'
                     });
                 }
                 //判断海豚知道SALT密钥不能为空
                 if((this.form_labels.secret == "" || this.form_labels.secret == undefined) && this.form_labels.platform == 3) {
                     control = true;
                     this.confirmLoading = false;
                     return this.$message({
                         message: "请输入SALT密钥",
                         type: 'warning'
                     });
                 }
                 // 如果不是快手线索就赋值key
                 if(this.form_labels.platform != 2) {
                     this.TikTokDataList[this.EditIndex].key = this.form_labels.key;
                 } else if(this.form_labels.platform == 2) {
                     
                     // 删除多余参数: secret
                    //  if(this.form_labels.secret) {
                    //      delete this.form_labels.secret
                    //  }
                 }
                //  console.log(this.form_labels,"============");
                 this.TikTokDataList[this.EditIndex].secret = this.form_labels.secret;
                 this.TikTokDataList[this.EditIndex].encoding_aes_key = this.form_labels.encoding_aes_key;
                 this.TikTokDataList[this.EditIndex].name = this.form_labels.name;
                 this.TikTokDataList[this.EditIndex].token = this.form_labels.token;
                 if(this.allocationway=="1_1"){
                     if(Array.isArray(this.form_labels.follow_id)&&this.form_labels.follow_id){
                         this.TikTokDataList[this.EditIndex].follow_id =  this.form_labels.follow_id.join();
                     }else{
                        if (typeof this.form_labels.follow_id === 'number') {
                          // 如果是数字类型，则转换为字符串类型
                          this.TikTokDataList[this.EditIndex].follow_id = String(this.form_labels.follow_id);
                        } else { 
                          // 如果不是数字类型，则直接赋值
                          this.TikTokDataList[this.EditIndex].follow_id = this.form_labels.follow_id;
                        }
                     }
                     this.TikTokDataList[this.EditIndex].follow_group_id = ''
                 }else{
                     if(Array.isArray(this.form_labels.follow_group_id)&&this.form_labels.follow_group_id){
                         this.TikTokDataList[this.EditIndex].follow_group_id = this.form_labels.follow_group_id.join();
                     }else{
                        if (typeof this.form_labels.follow_group_id === 'number') {
                          // 如果是数字类型，则转换为字符串类型
                          this.TikTokDataList[this.EditIndex].follow_group_id = String(this.form_labels.follow_group_id);
                        } else {
                          // 如果不是数字类型，则直接赋值
                          this.TikTokDataList[this.EditIndex].follow_group_id = this.form_labels.follow_group_id;
                        }
                     }
                     this.TikTokDataList[this.EditIndex].follow_id  = ''
                    //  console.log(this.TikTokDataList[this.EditIndex]);
                 }
                 this.TikTokDataList[this.EditIndex].create_id = this.form_labels.create_id;
                 this.TikTokDataList[this.EditIndex].status = this.form_labels.status;
                 // this.TikTokDataList[this.EditIndex].type = this.form_labels.type;
                 if(this.form_labels.label && Array.isArray(this.form_labels.label)) {
                     this.TikTokDataList[this.EditIndex].label = this.form_labels.label.join(",");
                 } else {
                     this.TikTokDataList[this.EditIndex].label = this.form_labels.label;
                 }
                 this.TikTokDataList[this.EditIndex].platform = this.form_labels.platform;
                 // 如果是海豚知道
                 if(this.form_labels.platform == 3) {
                     this.TikTokDataList[this.EditIndex].secret = this.form_labels.secret;
                     delete this.TikTokDataList[this.EditIndex].list; // 过滤参数list
                     delete this.TikTokDataList[this.EditIndex].uid
                    //  console.log(this.form_labels);
                     if(this.form_labels.follow_id){
                         this.TikTokDataList[this.EditIndex].follow_id = this.form_labels.follow_id
                     }else{
                         this.TikTokDataList[this.EditIndex].follow_id =""
                     }
                     if(this.form_labels.create_id){
                         this.TikTokDataList[this.EditIndex].create_id = this.form_labels.create_id
                     }else{
                         this.TikTokDataList[this.EditIndex].create_id =""
                     }
                     delete this.TikTokDataList[this.EditIndex].follow_group_id
 
                 } else if(this.form_labels.platform == 2||this.form_labels.platform == 6||
                 this.form_labels.platform == 7||this.form_labels.platform == 12) {
                    //  console.log("是快手平台");
                     // 如果是快手平台
                     delete this.TikTokDataList[this.EditIndex].list; // 过滤参数list
                     delete this.TikTokDataList[this.EditIndex].uid
                     delete this.TikTokDataList[this.EditIndex].create_uid
                     delete this.TikTokDataList[this.EditIndex].type
                     delete this.TikTokDataList[this.EditIndex].user_name
                     delete this.TikTokDataList[this.EditIndex].app_secret;
                     delete this.TikTokDataList[this.EditIndex].message_secret
                     delete this.TikTokDataList[this.EditIndex].app_id
                     if(this.form_labels.platform == 12 ){
                        delete this.TikTokDataList[this.EditIndex].key;    
                        delete this.TikTokDataList[this.EditIndex].token;
                        delete this.TikTokDataList[this.EditIndex].encoding_aes_key;
                        delete this.TikTokDataList[this.EditIndex].secret;
                        delete this.TikTokDataList[this.EditIndex].url;
                     }
                     if(this.form_labels.platform == 6||this.form_labels.platform == 7||
                     this.form_labels.platform == 8||this.form_labels.platform == 9||
                     this.form_labels.platform == 11){
                        delete this.TikTokDataList[this.EditIndex].secret;
                        delete this.TikTokDataList[this.EditIndex].list;
                        delete this.TikTokDataList[this.EditIndex].encoding_aes_key
                        delete this.TikTokDataList[this.EditIndex].token
                        delete this.TikTokDataList[this.EditIndex].app_secret;
                        delete this.TikTokDataList[this.EditIndex].message_secret
                        delete this.TikTokDataList[this.EditIndex].app_id
                     }
                 }
                if( this.form_labels.platform == 10){
                    this.TikTokDataList[this.EditIndex].message_secret = this.form_labels.message_secret
                    delete this.TikTokDataList[this.EditIndex].secret;
                }
                if( this.form_labels.platform == 13){
                    delete this.TikTokDataList[this.EditIndex].create_id
                    delete this.TikTokDataList[this.EditIndex].encoding_aes_key
                    delete this.TikTokDataList[this.EditIndex].follow_id
                    delete this.TikTokDataList[this.EditIndex].follow_group_id
                    delete this.TikTokDataList[this.EditIndex].secret
                    delete this.TikTokDataList[this.EditIndex].token
                }
             }
             if(control) {
                 return this.confirmLoading = false;
             }
             this.TikTokDataList.forEach(item => {
                 delete item.id; // id用于前端判断，不提交id数据
             });
             let DataListPush = {auto_push: this.TikTokDataList}
            //  console.log(DataListPush,"提交接口参数")
             this.$http.newupdataTikTokConfig(
                 DataListPush
             ).then((res) => {
                 if(res.status == 200) {
                     this.$message({
                         message: '操作成功',
                         type: 'success'
                     });
                     this.confirmLoading = false;
                     this.EditVisible = false;
                     this.getTikTokConfig();
                 }
             }).catch(() => {
                 this.confirmLoading = false;
                 this.getTikTokConfig();
             })
         },
         // 确定添加
         ConfirmAdd() {
             this.confirmLoading = true; // loading
             if(this.form_created.name == "" && this.form_created.platform !== 3) {
                 this.confirmLoading = false;
                 return this.$message({
                     message: '线索名称不能为空',
                     type: 'warning'
                 });
             }
             // 如果线索标签数量不能大于4个
             if(this.clueLabel.length > 4) {
                 this.confirmLoading = false;
                 return this.$message.warning("线索标签最多只支持选择4项");
             }

            //  if (this.clueBelong_list.length) {
            //     // 使用 some 方法检查是否有元素的 dy_id 为空
            //     const isEmptyDyId = this.clueBelong_list.some(item => !item.dy_id);
            //     if (isEmptyDyId) {
            //         this.confirmLoading = false;
            //         return this.$message.warning("请填写抖音号！");
            //     }
            //  }
             let control = false;
         // 当添加快手平台线索时
             if(this.form_created.platform == 2 || this.form_created.platform == 12) {
                //  console.log("kuaishou,1111111");
                 if((this.form_created.key == '' || this.form_created.key == undefined) && this.form_created.type != 1) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入快手号ID',
                         type: 'warning'
                     });
                 }
                 if((this.form_created.account_id == '' || this.form_created.account_id == undefined)&& this.form_created.platform == 12) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入视频号ID',
                         type: 'warning'
                     });
                 }
                 if(this.form_created.type == 2 && (this.form_created.uid == '' || this.form_created.uid == undefined)) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请选择维护人',
                         type: 'warning'
                     });
                 }
                 // 字符串转数组
                 if(Array.isArray(this.clueLabel)) {
                     this.form_created.label = this.clueLabel.join(",");
                 } else {
                     this.form_created.label = this.clueLabel;
                 }
                 // 赋值成员user_name
                 this.user_list.map((list) => {
                     if(this.form_created.uid == list.values) {
                         this.form_created.user_name = list.name;
                     }
                 })
                 // 删除多余用不到的 其他平台参数
                 if(this.form_created.list) { // list: 只有在抖音线索才有的数据
                     delete this.form_created.list;
                 }
                 if(this.form_created.secret) { // secret: 海豚知道的SALT密钥
                     delete this.form_created.secret;
                 }
                 if(this.form_created.type){
                     delete this.form_created.type
                 }
                //  console.log(this.form_created.allocationway,"是否出错");
                 if(this.form_created.allocationway=="1_1"){
                    this.form_created.follow_group_id = ""
                 }else{
                    this.form_created.follow_id = ""
                 }
                 if(this.form_created.platform == 12){
                    delete this.form_created.key
                 }
                 delete this.form_created.allocationway
                //  console.log(this.TikTokDataList);
                 this.TikTokDataList.push(this.form_created);
             
             } else if(this.form_created.platform == 1||this.form_created.platform == 13) {
         // 添加抖音平台线索时
                 // 如果是公海并且没有填写抖音号ID数据
                 // const filteredData = this.clueBelong_list.filter(list => list.dy_id == '' && list.type == 1);
                 // if(filteredData.length == this.clueBelong_list.length) {
                 //     this.clueBelong_list = [];
                 // }
                //  console.log("douyin 1111111");
                 let effectives = [];
                 // 如果填写了抖音号ID数据
                 if(this.clueBelong_list.length) {
                    //  console.log(this.clueBelong_list);
 
                     this.clueBelong_list.map((item) => {
                         if(control) {
                             return this.confirmLoading = false;
                         }
                         if(item.uid && (item.unique_id == '' || item.unique_id == undefined)&& this.form_created.platform == 13){
                                 this.$message({
                                 message: "微信小店客服ID不能为空",
                                 type: 'warning'
                             })
                                        
                            this.confirmLoading = false;
                            control = true;
                         }
                             if(item.uid && (item.dy_id == '' || item.dy_id == undefined)&& this.form_created.platform == 1){
                                 this.$message({
                                 message: "抖音号ID不能为空",
                                 type: 'warning'
                             });
                             this.confirmLoading = false;
                              control = true;
                             }
                         delete item.type
                         delete item.user_name
                        //  console.log(item.label);
                         if(!item.create_id||item.create_id==undefined){
                             item.create_id = ''
                         }
                         if(!item.follow_id||item.follow_id==undefined){
                             item.follow_id = ''
                         }
                         if(this.form_created.platform == 1){
                            if(item.label){
                             effectives.push({
                             follow_id: item.follow_id,
                             dy_id: item.dy_id,
                             label: item.label.join(","),
                             create_id: item.create_id,
                             follow_group_id:item.follow_group_id,
                             status:item.status
                            });
                            }else{
                                effectives.push({
                                follow_id: item.follow_id,
                                dy_id: item.dy_id,
                                label: "",
                                create_id: item.create_id,
                                follow_group_id:item.follow_group_id,
                                status:item.status
                            });
                            }
                         }else{
                            if(item.label.length){
                             effectives.push({
                             follow_id: item.follow_id,
                             unique_id: item.unique_id,
                             label: item.label.join(","),
                             create_id: item.create_id,
                             follow_group_id:item.follow_group_id,
                             status:item.status
                            });
                            }else{
                                effectives.push({
                                follow_id: item.follow_id,
                                unique_id: item.unique_id,
                                label: "",
                                create_id: item.create_id,
                                follow_group_id:item.follow_group_id,
                                status:item.status
                            });
                            }
                         }
 
                     })
                 }
                //  console.log(effectives);
 
                 if(control) {
                     return this.confirmLoading = false;
                 }
                 this.$set(this.form_created, 'list', effectives);
                 // 赋值成员user_name
                //  console.log(this.form_created);
                 delete this.form_created.allocationway
                 delete this.form_created.create_id
                 delete this.form_created.follow_id
                 if(this.form_created.platform == 1){
                    delete this.form_created.label
                 }else{
                    if(Array.isArray(this.clueLabel)) {
                     this.form_created.label = this.clueLabel.join(",");
                    } else {
                        this.form_created.label = this.clueLabel;
                    }
                    delete this.form_created.type
                 }
               
                 delete this.form_created.follow_group_id
                 this.TikTokDataList.push(this.form_created);
        
             } else if (this.form_created.platform == 3) {
         // 添加海豚知道平台线索时
                 if(Object.keys(this.form_created).indexOf("secret") == -1) {
                     this.form_created.secret = "";
                 }
                 if(this.form_created.type == 2 && (this.form_created.uid == '' || this.form_created.uid == undefined)) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请选择维护人',
                         type: 'warning'
                     });
                 }
                 // 字符串转数组
                 if(Array.isArray(this.clueLabel)) {
                     this.form_created.label = this.clueLabel.join(",");
                 } else {
                     this.form_created.label = this.clueLabel;
                 }
                 // 赋值成员user_name
                 this.user_list.map((list) => {
                     if(this.form_created.uid == list.values) {
                         this.form_created.user_name = list.name;
                     }
                 })
                 // 删除多余用不到的 其他平台参数
                 if(this.form_created.list) { // list: 只有在抖音线索才有的数据
                     delete this.form_created.list;
                 }
                     delete this.form_created.type;
                     delete this.form_created.follow_group_id;
                     delete this.form_created.allocationway;
                 this.TikTokDataList.push(this.form_created);
             } else if (this.form_created.platform == 4) {
                 if((this.form_created.key == '' || this.form_created.key == undefined) && this.form_created.type != 1) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入视频号橱窗ID',
                         type: 'warning'
                     });
                 }
                 if((this.form_created.secret == '' || this.form_created.secret == undefined) && this.form_created.type != 1) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入账号秘钥',
                         type: 'warning'
                     });
                 }
                 if((this.form_created.token == '' || this.form_created.token == undefined) && this.form_created.type != 1) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入消息推送token令牌',
                         type: 'warning'
                     });
                 }
                 if((this.form_created.encoding_aes_key == '' || this.form_created.encoding_aes_key == undefined) && this.form_created.type != 1) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请输入消息推送秘钥',
                         type: 'warning'
                     });
                 }
                 if(this.form_created.type == 2 && (this.form_created.uid == '' || this.form_created.uid == undefined)) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请选择维护人',
                         type: 'warning'
                     });
                 }
                 // 字符串转数组
                 if(Array.isArray(this.clueLabel)) {
                     this.form_created.label = this.clueLabel.join(",");
                 } else {
                     this.form_created.label = this.clueLabel;
                 }
                 // 赋值成员user_name
                 this.user_list.map((list) => {
                     if(this.form_created.uid == list.values) {
                         this.form_created.user_name = list.name;
                     }
                 })
                 // 删除多余用不到的 其他平台参数
                 if(this.form_created.list) { // list: 只有在抖音线索才有的数据
                     delete this.form_created.list;
                 }
                //  if(this.form_created.secret) { // secret: 海豚知道的SALT密钥
                //      delete this.form_created.secret;
                //  }
                 if(this.form_created.type){
                     delete this.form_created.type
                 }
                 delete this.form_created.allocationway
                //  console.log(this.form_created);
                 this.TikTokDataList.push(this.form_created);
             } else if (this.form_created.platform == 5) {
                 if(this.form_created.type == 2 && (this.form_created.uid == '' || this.form_created.uid == undefined)) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请选择维护人',
                         type: 'warning'
                     });
                 }
                 // 字符串转数组
                 if(Array.isArray(this.clueLabel)) {
                     this.form_created.label = this.clueLabel.join(",");
                 } else {
                     this.form_created.label = this.clueLabel;
                 }
                 // 赋值成员user_name
                 this.user_list.map((list) => {
                     if(this.form_created.uid == list.values) {
                         this.form_created.user_name = list.name;
                     }
                 })
                 delete this.form_created.key;//飞鱼没有key
                 // 删除多余用不到的 其他平台参数
                 if(this.form_created.list) { // list: 只有在抖音线索才有的数据
                     delete this.form_created.list;
                 } 
                 if(this.form_created.secret) { // secret: 海豚知道的SALT密钥
                     delete this.form_created.secret;
                 }
                 if(this.form_created.type){
                     delete this.form_created.type
                 }
                 delete this.form_created.allocationway
                 console.log(this.form_created);
                 this.TikTokDataList.push(this.form_created);
             }else if(this.form_created.platform == 6||this.form_created.platform == 7
             ||this.form_created.platform == 8||this.form_created.platform == 9||this.form_created.platform == 10
             ||this.form_created.platform == 11) {
                //  console.log("小红书,1111111");
                 if((this.form_created.key == '' || this.form_created.key == undefined) && this.form_created.type != 1) {
                     this.confirmLoading = false;
                     return this.$message({ 
                         message: '请输入小红书线索名称',
                         type: 'warning'
                     }); 
                 }
                 console.log(this.form_created.app_id);
                 if((this.form_created.app_id == '' || this.form_created.app_id == undefined)&&this.form_created.platform == 10) {
                     this.confirmLoading = false;
                     return this.$message({ 
                         message: '请输入小店id',
                         type: 'warning'
                     }); 
                 }
                 if((this.form_created.app_secret == '' || this.form_created.app_secret == undefined)&&this.form_created.platform == 10) {
                     this.confirmLoading = false;
                     return this.$message({ 
                         message: '请输入小店秘钥',
                         type: 'warning'
                     }); 
                 }
                 if(this.form_created.type == 2 && (this.form_created.uid == '' || this.form_created.uid == undefined)) {
                     this.confirmLoading = false;
                     return this.$message({
                         message: '请选择维护人',
                         type: 'warning'
                     });
                 }
                 // 字符串转数组
                 if(Array.isArray(this.clueLabel)) {
                     this.form_created.label = this.clueLabel.join(",");
                 } else {
                     this.form_created.label = this.clueLabel;
                 }
                 // 赋值成员user_name
                 this.user_list.map((list) => {
                     if(this.form_created.uid == list.values) {
                         this.form_created.user_name = list.name;
                     }
                 })
                 // 删除多余用不到的 其他平台参数
                 if(this.form_created.list) { // list: 只有在抖音线索才有的数据
                     delete this.form_created.list;
                 }
                 if(this.form_created.secret) { // secret: 海豚知道的SALT密钥
                     delete this.form_created.secret;
                 }
                 if(this.form_created.type){
                     delete this.form_created.type
                 }
                 if(this.form_created.allocationway=="1_1"){
                    this.form_created.follow_group_id  = ""
                 }else{
                    this.form_created.follow_id = ""
                 }
                 delete this.form_created.allocationway
                //  console.log(this.TikTokDataList);

                 this.TikTokDataList.push(this.form_created);
             
             }
             if(control) {
                 return this.confirmLoading = false;
             }
             let DataListPush = {auto_push: this.TikTokDataList};
            //  console.log(DataListPush,"=============");
             this.$http.newupdataTikTokConfig(
                 DataListPush
             ).then((res) => {
                 if(res.status == 200) {
                     // 清空已填入的信息
                     this.form_created.name = "";
                     this.form_created.status = 1;
                     this.form_created.follow_id = "";
                     this.form_created.create_id = "";
                     this.form_created.label = "";
                     this.clueBelong = 1;
                     this.form_created.platform = 1;
                     this.form_created.key = '';
                     this.form_created.account_id = "";
                     this.Maintainer = ""
                     this.clueBelong_list = [
                         {
                             //type: 1, // 归属 1公海 2私客
                             follow_id: '', // 成员
                             dy_id: '', // 抖音id
                             user_name: '', // 成员名称
                             create_id: '', // 录入人
                             follow_group_id:'',
                             unique_id:'',
                         }
                     ];
                     if(Object.keys(this.form_created).indexOf("secret")) {
                         delete this.form_created.secret;
                     }
                     this.clueLabel = '';
                     this.$message({
                         message: '操作成功',
                         type: 'success'
                     });
                     this.confirmLoading = false;
                     this.addVisible = false;
                     this.getTikTokConfig();
                 } else {
                     this.confirmLoading = false;
                     this.getTikTokConfig();
                 }
             }).catch(() => {
                 this.confirmLoading = false;
             })
         },
         // 删除按钮
         onDelete(row, index) {
             this.$confirm("是否删除", "提示", {
                 confirmButtonText: "确定",
                 cancelButtonText: "取消",
                 type: "warning",
             })
             .then(() => {
                const index = this.TikTokDataList.findIndex(item => item.id === row.id);
                if (index !== -1) {
                    // 删除找到的那条数据
                    this.TikTokDataList.splice(index, 1);
                }
                // 准备更新的数据对象
                let DataListPush = { auto_push: this.TikTokDataList };
                // console.log(DataListPush);
                 this.$http.newupdataTikTokConfig(
                     DataListPush
                 ).then((res) => {
                     if(res.status == 200) {
                         this.$message({
                             message: '操作成功',
                             type: 'success'
                         });
                         this.getTikTokConfig();
                     } else {
                         this.getTikTokConfig();
                     }
                 })
             })
             .catch(() => {
                 // 点击取消控制台打印已取消
                 console.log("已取消");
             });
         },
         //添加抖音线索标签不能大于4个
         handleLabelChange(item) {
           if (item.label && item.label.length > 4) {
             this.$message.warning("最多只支持选择4项")
             item.label = item.label.slice(0, 4);
           }
         },
         formLabelChange(e){
            //  console.log(e);
             if (e && e.length > 4) {
             this.$message.warning("最多只支持选择4项")
             this.form_labels.label = e.slice(0, 4);
           }
         },
         search() {
            if(this.backupscurrentPage){
                if(this.currentPage != this.backupscurrentPage){
                    this.currentPage = Number(this.backupscurrentPage);
                }
            }
            // 不再仅筛选当前页数据，而是筛选所有数据
            let filteredData = this.backupsdata.filter(item => {
                // 检查姓名是否包含搜索关键字（如果输入了姓名搜索条件）
                const nameMatch = !this.input3 || item.name.toLowerCase().includes(this.input3.toLowerCase());
                // 检查来源是否与指定的值匹配（如果输入了来源搜索条件）
                const sourceMatch = !this.sourcevalue || String(item.platform) === this.sourcevalue.toString();
                // 检查状态是否与指定的值匹配（如果输入了状态搜索条件）
                const statusMatch = !this.statusvalue || String(item.status) === this.statusvalue.toString();
                // 返回三个条件都满足的结果
                return nameMatch && sourceMatch && statusMatch;
            });
            // 更新 TikTokDataList 为筛选后的所有数据
            this.TikTokDataList = filteredData;
            // 如果没有搜索条件，则恢复第一页数据
            if (!this.input3 && !this.sourcevalue && !this.statusvalue) {
                if(this.backupscurrentPage){
                    // this.handleCurrentChange(this.backupscurrentPage)//数据虽保留在当前页，但是页码对不上
                    this.handleCurrentChange(1);  // 强制回到第一页
                }
                return this.getTikTokConfig();
            }
            // 由于筛选是对所有数据进行的，现在不需要根据当前页数来筛选数据
            // 如果希望保留分页效果，可以根据筛选后的数据长度来重新计算当前页面应显示的数据。
            this.TikTokDataList = this.TikTokDataList.slice((this.currentPage-1)*this.pageSize, this.currentPage*this.pageSize);
         },
         //表格多选
         handleSelectionChange(val) {
           this.multipleSelection = val;
         },
         allhandleSelectionChange(val){
            this.multipleSelection = val;

         },
         //批量设置开启关闭
         submitbatchopen(){
            if(!this.multipleSelection.length){
                this.batchopen = ""
                return this.$message.warning("请选择线索")
            }
            let params = {
                name:"",
                status:""
            }
            params.status = this.batchopen
            // 使用 map 方法遍历数组并返回新的值
            params.name = this.multipleSelection.map(item => {
              // 将 item.name 转换为字符串，并赋给新字段
              return String(item.name);
            });
            params.name = params.name.join(",")
            // console.log(params);
            this.$http.cluebatchopen(params).then((res)=>{
                if(res.status==200){
                    this.$message.success("批量修改成功！")
                    this.batchopen = ""
                    this.getTikTokConfig()
                }
            })

         },
         // 添加抖音线索
         addTikToKClue() {
             this.addVisible = true; // 显示模态框
             this.clueBelong_list = [
                 {
                     // type: 1, // 归属 1公海 2私客
                     uid: '', // 成员id
                     dy_id: '', // 抖音id
                     user_name: '', // 成员名称
                     create_uid: '', // 录入人
                     follow_group_id:"",//分组
                     allocationway:"1_1",//分配方式
                     clueLabel:"",//线索
                     Maintainer:"",
                     status:1,//状态
                 }
             ]
         },
         //设置留资是否去重
         submitliuzi(){
            let params={}
            params.push_repeat = this.radio1
            this.$http.setliuziDeduplication(params).then((res)=>{
                if(res.status==200){
                    this.$message.success("操作成功");
                }
            })
         },
         // 获取关联成员
         getManagerAuthList() {
             this.$http.getMemberListpage().then((res) => {
                 if (res.status === 200) {
                    // console.log(res);
                     this.user_list = res.data;
                     this.$emit('sendValue', this.user_list);
                 }
             });
         },
         //获取关联分组
         getassociationsatff(){
             this.$http.getcluegrouping().then((res)=>{
                 if (res.status==200){
                     // 拷贝res.data，将 id 和 title 属性复制到this.user_list中
                     // 将 title 属性重命名为 username
                     this.serverData = JSON.parse(JSON.stringify(res.data))
                     this.respectuser_list = this.serverData.map(obj => {
                       return { id: obj.id, user_name: obj.title }
                     })
                     // this.respectuser_list.push({
                     //     id: "1_1",
                     //     user_name:"创建分组"
                     // })
                     // console.log(this.user_list)
                 }
             })
         },
         // 获取客户标签
         getLabelList() {
         this.$http.getLabelGroupNoPage().then((res) => {
             if (res.status === 200) {
                 res.data.map((item) => {
                     if(item.label && item.label.length) {
                         item.disabled = true;
                     } else {
                         item.disabled = false;
                     }
                     return item;
                 })
                 this.label_list = res.data;
                 }
             });
         },
         // 点击增加线索归属
         addClueBelong(item) {
            //  console.log(item);
            if(this.form_labels.platform == 1||this.form_created.platform==1){
                this.clueBelong_list.push(
                 {
                     uid: '', // 成员id
                     dy_id: '', // 抖音id
                     user_name: '', // 成员名称
                     create_uid: '', // 录入人
                     follow_group_id:"",//分组
                     allocationway:'1_1',
                     Maintainer:""
                 }
             )
            }else if(this.form_labels.platform == 13||this.form_created.platform==13){
                this.clueBelong_list.push(
                 {
                     uid: '', // 成员id
                     unique_id: '', //微信小店客服id
                     user_name: '', // 成员名称
                     create_uid: '', // 录入人
                     follow_group_id:"",//分组
                     allocationway:'1_1',
                     Maintainer:""
                 }
             )
            }
            
         },
         // 点击删除线索归属
         delClueBelong(item,index) {
            //  console.log(item,index);
             this.clueBelong_list.splice(index, 1);
         },
         //添加,抖音的录入人
         douyinwei(e,item,index){ 
             if(e){
                // console.log(e);
                 if(item.allocationway=="1_1"){
                 this.clueBelong_list[index].follow_id =  e
                 this.clueBelong_list[index].follow_group_id = ""
             }else{
                 this.clueBelong_list[index].follow_group_id = e
                 this.clueBelong_list[index].follow_id = "" 
             }
             }
            //  console.log(this.clueBelong_list);
         },
          //添加,快手的录入人
          kuaishouwei(e){ 
             if(e){
                 if(this.form_created.allocationway=="1_1"){
                 this.form_created.follow_id =  e.join(",")
                 this.form_created.follow_group_id = ""
             }else{
                 this.form_created.follow_group_id = e.join(",")
                 this.form_created.follow_id = "" 
             }
             }
         },
         // 当修改数据线索来源发生改变
         changeEditPlat(val) {
             // 重新赋值当前数据
             if(val == this.deep_row_params.platform) {
                 this.row_params = JSON.parse(JSON.stringify(this.deep_row_params));
                 this.form_labels = JSON.parse(JSON.stringify(this.deep_row_params));
                 if(this.form_labels.label.indexOf(",") >= 0) {
                     this.form_labels.label = this.form_labels.label.split(",");
                 } else {
                     this.form_labels.label = [this.form_labels.label];
                 }
                //  console.log("执行自身平台", this.deep_row_params)
             } else if(val == 1 && val != this.deep_row_params.platform) {
                //  console.log("执行抖音平台", this.form_labels)
                 this.form_labels = {
                     label: [],
                     list: [{uid: "", type: 1, dy_id: "", create_uid: "", user_name: ""}],
                     name: "",
                     platform: 1,
                     status: 1
                 }
                //  console.log(this.form_labels,"切换平台后数据抖音")
                 // 重置线索归属列表
                 this.clueBelong_list = [
                     {
                         type: 1, // 归属 1公海 2私客
                         uid: '', // 成员id
                         dy_id: '', // 抖音id
                         user_name: '', // 成员名称
                         create_uid: '', // 录入人,
                         allocationway:"1_1"
                     }
                 ];
             } else if(val == 2 && val != this.deep_row_params.platform) {
                //  console.log("执行快手平台",this.form_labels)
                 this.form_labels = {
                     create_uid: "",
                     label: [],
                     name: "",
                     platform: 2,
                     status: 1,
                     type: 1,
                     uid: "",
                     user_name: "",
                 }
                 this.allocationway = "1_1"
             } else if(val == 3 && val != this.deep_row_params.platform) {
                //  console.log("执行海豚知道平台")
                 this.form_labels = {
                     key: "",
                     label: [],
                     name: "",
                     platform: 3,
                     secret: "",
                     status: 1,
                     type: 1,
                     uid: "",
                 }
             }else if(val == 4 && val != this.deep_row_params.platform) {
                //  console.log("执行视频号平台",this.form_labels)
                 this.form_labels = {
                     create_uid: "",
                     label: [],
                     name: "",
                     platform: 4,
                     status: 1,
                     uid: "",
                     user_name: "",
                 }
                 this.allocationway = "1_1"
             }else if(val == 5 && val != this.deep_row_params.platform) {
                //  console.log("执行飞鱼平台",this.form_labels)
                 this.form_labels = {
                     create_uid: "",
                     label: [],
                     name: "",
                     platform: 5,
                     status: 1,
                     uid: "",
                     user_name: "",
                 }
                 this.allocationway = "1_1"
             }
             // if(val == 2) {
             //     delete this.row_params.list; // 如果是快手线索就删除list
             // } else {
             //     this.row_params.list = this.deep_row_params.list; // 如果从快手线索改变为抖音线索就重新赋值
             // }
             // 如果默认平台不是海豚知道,切换时清除 达人IDform_labels.key
             // if(this.deep_row_params.platform != 3) {
             //     this.form_labels.key = "";
             // }
             // this.form_labels.type = 1
             this.$forceUpdate();
         },
         // 当修改数据线索来源发生改变
         changeAddPlat() {
             this.form_created.type = 1
             this.$forceUpdate();
         },
         // 处理点击后页面不显示问题
         changEditType() {
             this.$forceUpdate();
         },
         // 处理点击后页面不显示问题
         changAddType() {
             this.$forceUpdate();
         },
         // 关闭编辑模态框时触发
         EditShutDown() {
             this.row_params.list = this.deep_row_params.list; // 直接修改模态框数据会留下，所以当模态框关闭时要重新赋值
         },
         // 关联成员发生改变
         changeCorrelation() {
             this.$forceUpdate();
         },
         //操作页面
         openOpPage(row){
            this.dialogs.updateEntryPerson = true;
            this.$nextTick(()=>{
                this.$refs.updateEntryPerson.open(row).onSuccess(()=>{
                    //this.getTikTokConfig();
                });
            })
         },
         //跳转到经营视图线索tab查看
         viewclues(id){
            // console.log(id);
            this.$goPath(`crm_Follow_up_list?clue=${id}`);
         },
         viewcluescopy(row){
            if(row.platform==8){
                this.$goPath(`crm_Follow_up_list?platform=15`);
            }else if(row.platform==10){
                this.$goPath(`crm_Follow_up_list?platform=16`);
            }else if(row.platform==11){
                this.$goPath(`crm_Follow_up_list?platform=18`);
            }else{
                this.$goPath(`crm_Follow_up_list?clue=${row.key}`);
            }
         },
         //微信小店拉取
        initialization(row){
            this.$confirm('确定操作拉取 , 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
                this.$http.TikTokinitialization(row.key).then(res=>{
                    if(res.status==200){
                        this.$message({
                          type: 'success',
                          message: '操作成功!'
                        });
                        this.getTikTokConfig()
                    }
                })
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作'
              });          
            });
        },
         handleSizeChange(val) {
          console.log(`每页 ${val} 条`);
          this.currentPage = 1;
          this.pageSize = val;
        },
        //当前页改变时触发 跳转其他页
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.currentPage = val;
            this.backupscurrentPage = val
        },
        //刷新
        Refresh(){
            this.getTikTokConfig()
        },
     }
 }
 </script>
 <style scoped lang="scss">
 .liuzi{
    display:flex;
    justify-content: space-between;
    .jiansuo{
        display: flex;
    }
 }
 .page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;
  .head-list{
    margin-left: 13px;
  }
}
.overdue{
    margin-top: 6px;
}
 .add_table {
     padding: 7px 15px;
     margin-bottom: 20px;
 }
 .new_Contact_Person {
     display: flex;
     align-items: center;
     
     ::v-deep .el-select {
         width: 90px;
         .el-input {
             .el-input__inner {
                 text-align: center;
             }
         }
     }
     .douyinID {
         width: 200px;
         margin-left: 10px;
     }
     .addBelong {
         font-size: 18px;
         color: #409EFF;
         cursor: pointer;
         margin-left: 10px;
     }
     .delBelong {
         font-size: 18px;
         color: #F56C6C;
         cursor: pointer;
         margin-left: 10px;
     }
     .deftHand_private {
         width: 195px;
         ::v-deep .el-input {
             .el-input__inner {
                 text-align: left;
             }
         }
     }
     .deftHand_public {
         width: 400px;
         ::v-deep .el-input {
             .el-input__inner {
                 text-align: left;
             }
         }
     }
     .dyhao{
         margin-top: -50px;
     }
     .allocation{
         margin-top: 10px;
         margin-left: 64px;
     }
     // .Divider{
     //     width: 79%;
     //     height: 1px;
     //     // margin: 0 auto;
     //     margin-top: 10px;
         
     //     margin-bottom: 10px;
     //     border-top: 1px solid #8e9aac;
     // }
 }
 .reminder {
     width: 410px;
     box-sizing: border-box;
     background-color: #F7F8FA;
     color: #4E5969;
     padding: 7px 12px;
     margin: 10px 60px 10px 0px;
     font-size: 12px;
     line-height: 20px;
 }
 .memberList {
     max-height: 370px;
     overflow-y: auto;
 }
 </style>