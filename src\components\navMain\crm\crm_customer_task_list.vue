<template>
    <div class="task_list">

        <div class="header-title div row">
      <div class="ht-title">当前任务详情</div>
      <div style="margin-right: 24px">
        <el-link type="primary" @click="toAllTaskList()">全部任务列表</el-link>

      </div>
    </div>
    <div class="div row">
        <div style="margin-bottom: 15px;margin-right:10px;">
        <el-input
            :placeholder="placeholder"
            v-model="select_params.keywords"
            class="input-with-select"
            style="width: 320px"
            clearable
            @keyup.enter.native="handleKeywordSearch"
            @clear="clearSelectKeyword"
          >
            <el-select
              v-model="select_params.type"
              @change="changeSelectParams"
              slot="prepend"
              placeholder="请选择"
              size="small"
              style="width: 105px"
            >
              <el-option label="手机号码" :value="1"></el-option>
              <el-option label="客户姓名" :value="2"></el-option>
            </el-select>
          </el-input>
        </div>
        <div
         style="margin-bottom: 15px;">
            <el-select v-model="statusA" slot="prepend" placeholder="可根据状态搜索"
            @change="Search_Status(1)">
              <el-option label="全部" value="4"></el-option>  
              <el-option label="未执行" value="0"></el-option>
              <el-option label="成功" value="1"></el-option>
              <el-option label="重复" value="2"></el-option>
              <el-option label="失败" value="3"></el-option>
            </el-select>
            <!-- <el-button slot="append" icon="el-icon-search" @click="Search_Status(1)"></el-button> -->
        </div>
    </div>
    
        <!-- 任务详情 -->
        <el-row :gutter="20">
            <el-col :span="24">

                <div class="content-box-crm">
                    <div class="table-top-box div row">

                    </div>
                    <el-table :data="tableData" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row
                        :row-style="$TableRowStyle" v-loading="loading">
                        <!-- <el-table-column label="详情id" width="80" prop="id" align="left">

                        </el-table-column>
                        <el-table-column label="任务id" width="80" prop="task_id" align="left">

                        </el-table-column> -->
                        <el-table-column label="表格第几行" prop="line" align="left">
                        </el-table-column>
                        <el-table-column label="客户姓名" prop="cname" align="left">
                            <template slot-scope="scope" >
                                <div @click="informationdetail(scope.row)" :class="{styleHand : information==1}">
                                    {{scope.row.cname?scope.row.cname:"--"}}
                                </div>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="录入人姓名" prop="create_name" align="left" v-if="website_id!=109">
                            <template slot-scope="scope">
                                {{scope.row.create_name?scope.row.create_name:"--"}}
                            </template>
                        </el-table-column> -->
                        <el-table-column v-if="information!=1" label="录入人姓名" prop="create_user" align="left">
                            <template slot-scope="scope">
                                {{scope.row.create_user?scope.row.create_user.user_name:"--"}}
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="维护人姓名" prop="follow_name" align="left" v-if="website_id!=109">
                            <template slot-scope="scope">
                                {{scope.row.follow_name?scope.row.follow_name:"--"}}
                            </template>
                        </el-table-column> -->
                        <el-table-column v-if="information!=1" label="维护人姓名" prop="follow_user" align="left">
                            <template slot-scope="scope">
                                {{scope.row.follow_user?scope.row.follow_user.user_name:"--"}}
                            </template>
                        </el-table-column>
                        <el-table-column v-if="information==1" label="副号" prop="subsidiary_mobile" align="left">
                            <template slot-scope="scope">
                                <div>
                                    {{scope.row.subsidiary_mobile ? scope.row.subsidiary_mobile : '--' }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="手机号" prop="mobile" align="left">
                            <template slot-scope="scope">
                                <div  @click="informationdetail(scope.row)" :class="{styleHand : information==1}">
                                    {{scope.row.mobile}}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="information==1" label="操作人姓名" prop="admin" align="left">
                            <template slot-scope="scope">
                                {{scope.row.admin?scope.row.admin.user_name:"--"}}
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="状态" prop="status">
                        </el-table-column> -->
                        <el-table-column prop="status" label="状态"  align="left">
                                    <template slot-scope="scope">
                                        <el-tag type="success" size="default" v-if="scope.row.status == 1">成功</el-tag>
                                        <el-tag type="warning" size="default" v-if="scope.row.status == 2">重复</el-tag>
                                        <el-tag type="warning" size="default" v-if="scope.row.status== 3">失败</el-tag>
                                        <el-tag type="danger" size="default" v-if="scope.row.status== 0">未执行</el-tag>
                                     
                                    </template>
                                </el-table-column>

                        <el-table-column label="内容" prop="content" align="left">

                        </el-table-column>
                        <!-- <el-table-column label="客户id" width="80" prop="client_id" align="left">

                        </el-table-column> -->
                        <el-table-column label="创建时间" prop="created_at" align="left">

                        </el-table-column>

                    </el-table>
      <!-- 分页 -->
      <div class="block">
        <div> 
            <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
        </div>
        <div>
            <el-pagination
                background
                layout="prev,pager,next"
                :total="telinfoTotal"
                @current-change="handleCurrentChange"
                :current-page="telinfo_params.page"
                :page-size="telinfo_params.per_page"
            >
            </el-pagination>
        </div>
    </div>
                </div>
            </el-col>
            <!-- <el-col v-if="!auth_transaction &&!showAdd &loadEnd" :span="24">
          <myEmpty desc="当前用户不可查看"></myEmpty>
        </el-col> -->
        </el-row>

    </div>
</template>
  
<script>

export default {
    name: "crm_customer_task_list",
    components: {
        // myLabel,

    },
    data() {
        return {
            telinfo_params: {
                page: 1,
                status:0,
                per_page: 10,
            },
            status:"",
            statusA:"4",
            tableData: [],
            task_id: 0,
            website_id: 0,
            telinfoTotal:0,
            loading:false,
            information:"",
            placeholder: "请输入手机号码(回车搜索)",
            select_params: {
              type: 1,
              mobile: ""
            },//模糊检索
            inforparams:{
                name:"",
                mobile:"",
            }

        };
    },
    mounted() {
        if (this.$route.query.website_id) {
            this.website_id = this.$route.query.website_id
        }
        if (this.$route.query.information) {
            this.information = this.$route.query.information
        }
        if (this.$route.query.task_id) {
            this.task_id = this.$route.query.task_id*1

            if(this.$route.query.information&&this.$route.query.information==1){
                this.informationrecord()
            }else{
                this.getData();
            }

        }
        console.log(this.$route.query)

    },
    created() {
   
  },
    computed: {
        
    },
    methods: {
        // 拨打记录-直拨记录
        getData() {
                let data = {
                    page: this.telinfo_params.page,
                    per_page: this.telinfo_params.per_page,
                    status:this.statusA,
                    mobile:this.telinfo_params.mobile,
                    name:this.telinfo_params.name 
                }
                if(data.status==4){
                    delete data.status
                }
                let id = this.task_id
                this.loading = true
                this.$ajax.house.newgetTaskList(id,data).then((res) => {
                if (res.status == 200) {
                    this.loading = false
                    this.tableData = res.data.data;
                    this.telinfoTotal = res.data.total
                }
            });
        },
        //流转客的记录
        informationrecord(){
            let item = {
                page: this.telinfo_params.page,
                per_page: this.telinfo_params.per_page,
                // task_id: this.task_id,
                status:this.statusA,
                mobile:this.inforparams.mobile,
                name:this.inforparams.name 
            }
            if(item.status==4){
                delete item.status
            }
            let id = this.task_id
            this.loading = true
            this.$http.informationimportlist(id,item).then((res) => {
                if (res.status == 200) {
                    this.loading = false
                    this.tableData = res.data.data;
                    // this.telinfo_params.page = res.data.current_page
                    // this.telinfo_params.per_page = res.data.per_page
                    this.telinfoTotal = res.data.total
                }
            });
        },
        //资料课跳转客户详情
        informationdetail(row){
            if(this.information==1&&row.status==1){
               console.log(row); 
               let url = `/crm_information_detail?id=${row.client_id}&type=my&information=1`;
                this.$goPath(url);
            }
        },
        //姓名手机号的模糊检索
        changeSelectParams(e) {
        // console.log(e);
        if (e == 1) {
          this.placeholder = '请输入手机号码(回车搜索)'
        } else if (e == 2) {
          this.placeholder = '请输入客户姓名(回车搜索)'
        }
      },
      //清空检索
      clearSelectKeyword() {
        this.inforparams.mobile = ''
        this.inforparams.name = ''
        this.informationrecord()
      },
      //回车进行检索
      handleKeywordSearch() {
        if (this.select_params.type == 1) {
          this.placeholder = '请输入手机号码'
          this.inforparams.name = ""
          this.inforparams.mobile=this.select_params.keywords.replace(/\s+/g,"");
          this.telinfo_params.name = ""
          this.telinfo_params.mobile=this.select_params.keywords.replace(/\s+/g,"");
          
        } else if (this.select_params.type == 2) {
          this.placeholder = '请输入客户姓名'
          this.inforparams.mobile = ''
          this.inforparams.name = this.select_params.keywords
          this.telinfo_params.mobile = ''
          this.telinfo_params.name = this.select_params.keywords
        }
        if(this.information&&this.information==1){
            this.informationrecord()
        }else{
            this.getData()
        }
       
      },
        // 分页器-当前页
        handleCurrentChange(val) {
            this.telinfo_params.page = val
            if(this.information==1){
               this.informationrecord() 
            }else{
                this.getData()
            }
            console.log(`当前页: ${val}`);
        },
        //刷新按钮，刷新表格
        Refresh(){
            if(this.information==1){
               this.informationrecord() 
            }else{
                this.getData()
            }
        },
       toAllTaskList(){
        let url = `/crm_customer_all_task_list`;
          this.$goPath(url); // 跳转客户详情
       },
       Search_Status(status){
        if(status==1){
            if(this.information==1){
               this.informationrecord() 
            }else{
                this.getData()
            }
        }else{
            if(this.status==""||this.status==undefined){
                this.$message.warning("请选择状态")
                return
            }else{
                this.telinfo_params.status = this.status
                this.getData()
            }
        } 
       }
    },
};
</script>
  
<style scoped lang="scss">
.task_list {
    height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 24px;

    .bottom-border {
        align-items: center;
        padding-bottom: 24px;
        justify-content: flex-start;
        border-bottom: 1px dashed #e2e2e2;

        .text {
            font-size: 14px;
            color: #8a929f;

            .label {
                width: 70px;
                display: inline-block;
                text-align: right;
            }
        }
    }
    .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    margin: -24px -24px 24px;
    justify-content: space-between;
    align-items: center;
    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }
  .block{
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
}
.styleHand{
    cursor: pointer;
}
}


</style>
  