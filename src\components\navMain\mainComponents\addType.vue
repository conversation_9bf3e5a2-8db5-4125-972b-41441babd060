<template>
  <el-main>
    <el-form ref="form" :model="form" :rules="rules">
      <div class="auto-width">
        <el-form-item label="户型分类" prop="category" label-width="100px">
          <el-select v-model="form.category" style="width:300px">
            <el-option
              v-for="(item, index) in type_classification_list"
              :key="index"
              :label="item.description"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <el-form-item label="户型" prop="room" label-width="100px">
        <el-input
          style="width:100px"
          v-model="form.total_room"
          placeholder="室"
        >
          <template slot="append">室</template>
        </el-input>
        <el-input
          style="width:100px"
          v-model="form.total_salloon"
          placeholder="厅"
        >
          <template slot="append">厅</template></el-input
        >
        <el-input
          style="width:100px"
          v-model="form.total_washroom"
          placeholder="卫"
        >
          <template slot="append">卫</template></el-input
        >
        <el-tooltip
          effect="dark"
          content=" 如果户型超过五室请在“户型特色”里进行说明"
          placement="right-start"
          style="font-size:18px"
          ><i class="el-icon-info"></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="面积" prop="area" label-width="100px">
        <el-input
          placeholder="请输入面积"
          type="number"
          style="width:300px"
          v-model="form.area"
          ><template slot="append">㎡</template></el-input
        >
      </el-form-item>
      <el-form-item label="套内面积" prop="inside_area" label-width="100px">
        <el-input
          placeholder="请输入面积"
          type="number"
          style="width:300px"
          v-model="form.inside_area"
          ><template slot="append">㎡</template></el-input
        >
      </el-form-item>
      <div class="auto-width">
        <el-form-item label="户型标识" prop="name" label-width="100px">
          <el-input
            v-model="form.name"
            placeholder="请输入户型标识"
            style="width:300px"
          ></el-input>
          <el-tooltip
            effect="dark"
            style="font-size:18px"
            content=" 标识不同户型短标题如：A户型、B户型，字数限定在8个字以内"
            placement="right-start"
            ><i class="el-icon-info"></i>
          </el-tooltip>
        </el-form-item>
      </div>

      <el-form-item
        label="售价"
        prop="sell_price"
        label-width="100px"
      >
        <el-input
          placeholder="请输入价格"
          type="number"
          style="width:300px"
          v-model="form.sell_price"
          ><template slot="append">元/㎡</template></el-input
        >
      </el-form-item>
      <el-form-item
        label="销售状态"
        prop="status"
        label-width="100px"
      >
        <el-select v-model="form.status" placeholder="请选择状态">
          <el-option
            v-for="(item,index) in status_listnew"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="户型朝向"
        prop="orient_category"
        label-width="100px"
        v-if="website_info.website_mode_category !== 0"
      >
        <el-select v-model="form.orient_category" placeholder="请选择朝向">
          <el-option
            v-for="item in orient_category_list"
            :key="item.id"
            :label="item.description"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="户型特色" prop="feature" label-width="100px">
        <el-input
          v-model="form.feature"
          type="textarea"
          placeholder="请输入户型特色；多个特色用，隔开"
          style="width:300px"
          rows="4"
        ></el-input>
      </el-form-item>
      <el-form-item label="户型图" label-width="100px">
        <el-upload
          :headers="myHeader"
          :action="house_type_img"
          :on-success="handleSuccess"
          list-type="picture-card"
          :limit="1"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item label-width="100px">
        <el-button type="primary" @click="onSubmit">立即创建</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </el-main>
</template>

<script>
import config from "@/utils/config";
import { mapState } from "vuex";
export default {
  data() {
    const validateRoom = (rule, value, cb) => {
      if (
        this.form.total_room &&
        this.form.total_salloon &&
        this.form.total_washroom
      ) {
        cb();
      } else {
        cb(new Error("室厅卫不能为空"));
      }
    };
    return {
      form: {
        // region: "1",
        total_room: "",
        total_salloon: "",
        total_washroom: "",
        area: "",
        room: "",
        name: "",
        feature: "",
        build_id: "",
        category: "1",
        orient_category: "1",
        img: "",
        inside_area: "",
        sell_price: "", // 售价
        status: "", //状态
      },
      // 户型分类列表
      type_classification_list: [],
      // 上传的内容
      dialogImageUrl: "",
      dialogVisible: false,
      build_id: null,
      rules: {
        category: [{ required: true, trigger: "blur", message: "请选择" }],
        type_logo: [
          { required: true, trigger: "blur", message: "请输入户型标识" },
        ],
        room: [
          {
            required: true,
            trigger: "blur",
            validator: validateRoom,
            message: "请输入户型",
          },
        ],
        area: [{ required: true, trigger: "blur", message: "请输入面积" }],
        // features: [{ required: true, trigger: "blur", message: "请输入特色" }],
      },
      house_type_img: `/api/common/file/upload/admin?category=${config.HOUSER_TYPE_IMG}`,
      status_list: [],
      orient_category_list: [],
      status_listnew: [
        {
          value: '1',
          label: '在售'
        },
        {
          value: '2',
          label: '预售'
        },
        {
          value: '3',
          label: '售罄'
        },
      ]
    };
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: "Bearer " + window.localStorage.getItem("TOKEN"),
      };
    },
    ...mapState(["website_info"]),
  },
  mounted() {
    this.form.build_id = this.$router.app._route.query.id;
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "BUILD_HOUSE_TYPE_CATEGORY":
            this.type_classification_list = item.childs;
            break;
          case "BUILD_STATUS":
            this.status_list = item.childs;
            break;
          case "BUILD_HOUSE_TYPE_ORIENT_CATEGORY":
            this.orient_category_list = item.childs;
            break;
        }
      });
    });
  },
  methods: {
    onSubmit() {
      for (var prop in this.form) {
        if (this.form[prop] === "") {
          delete this.form[prop];
        }
      }
      if (this.form.img === "") {
        this.$message({
          message: "上传失败",
          type: "error",
        });
      } else {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.$http.createHouse(this.form).then((res) => {
              if (res.status === 200) {
                this.$message({
                  message: "提交成功",
                  type: "success",
                });
                this.$goPath(`/setup_type?id=${this.form.build_id}`);
              }
            });
          }
        });
      }
    },
    // 上传文件
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.response.url;
      this.dialogVisible = true;
    },
    handleSuccess(response) {
      this.form.img = response.url;
    },
    goBack() {
      this.$router.back();
    },
  },
};
</script>

<style lang="scss" scoped>
.el-main {
  margin-top: 20px;
}
.title_dis {
  padding: 10px;
  font-size: 12px;
  background: #edfbf8;
  color: #999;
}
.lou-type {
  margin: 20px 0;
  .lou-type-s {
    margin-right: 12px;
    color: #606266;
    font-size: 14rpx;
  }
}
</style>
