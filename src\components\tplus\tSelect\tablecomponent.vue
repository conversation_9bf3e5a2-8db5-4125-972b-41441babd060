<template>
    <div v-fixed-scroll="62">
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="this['table_header' + recordstype]" select
          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
          :sort_change="sortChangeData" @selection-change="selectionChange"></myTable>
    </div>
</template>
<script>
import myTable from "@/components/components/my_table";
export default {
    components: {
    myTable,
  },
    props:{
        is_table_loading:{
            type:Boolean,
            default:false
        },
        tableData:{
            type:Array,
            default:()=>[]
        },
        recordstype:{
            type:[Number, String],
            required:true
        },
        status_list:{
            type:Array,
            default:()=>[]
        },
        labels_list:{
            type:Array,
            default:()=>[]
        }
    },
    data() {
        return {
            calculatecreate:"录入人",
            chnegjiaouser:"成交人",
            weihuuser:"维护人",
            diakanuser:"带看人",
            table_header0: [],//全部
            table_header1: [],//回访记录
            table_header2: [],//带看记录 
            table_header3: [],//外呼记录
            table_header4: [],//维护记录
            table_header5: [],//成交记录(暂未放开)
            table_headerxiansuo: [
        {
          label: "客户名称",
          width: "200px",
          fixed: "left",
          // sortable: "custom",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {/* onClick={() => {
                      this.$emit("onClickDetailXiansuo",{data:data.row,status:1})
                    }} */}
                    {data.row.cname ? (<div class="cus-userName">{data.row.cname}</div>
                    ) : (
                      ""
                    )}
                    <div class="cus-sex">
                      {data.row.sex && data.row.sex == 1 ? (
                        <img
                          src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                          alt=""
                        />
                      ) : null}
                      {data.row.sex && data.row.sex == 2 ? (
                        <img
                          src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                          alt=""
                        />
                      ) : null}
                    </div>
                  </div>
                  <div class="cus-box-foot flex-row">

                    {data.row && data.row.create_id == this.status_id ? (
                      <span class="cus-icon-type">私客</span>
                    ) : null}


                  </div>
                </div>
                {data.row.wxqy_id > 0 ? (
                  <img
                    class="cus-img"
                    src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                  />
                ) : (
                  ""
                )}

              </div>
            );
          },
        },
        {
          label: "手机号",
          fixed: "left",
          width: "200px",
          prop: "mobile",
          render: (h, data) => {
            const mobileFilter = function (val) {
              let reg = /^(.{3}).*(.{3})$/;
              return val.replace(reg, "$1*****$2");
            };
            return (
              <div class="flex-box table-btns">
                {data.row.last_call_follow
                  &&
                  data.row.last_call_follow.id
                  ?
                  (<div class="last_call_follow div row">

                    <div class="cus-clue-text">
                      {data.row.last_call_follow
                        &&
                        data.row.last_call_follow.call_status == 1
                        ?
                        (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                        :
                        (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                    </div>
                  </div>)
                  :
                  (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
              </div>
            );
          },
        },

        {
          label: "备注",
          prop: "",
          width: "200px",
          render: (j, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {
                      data.row.remark ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.remark}
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.remark}
                          </div>
                        </el-popover>
                      ) : (
                        "--"
                      )
                    }
                  </div>
                </div>
              </div>
            );
          }
        },

        {
          label: "类型",
          prop: "client_type",
          minWidth: "100px",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                {data.row.platform_info ? (
                  <el-tag type="info">{data.row.platform_info.title}</el-tag>
                ) : "--"}
              </div>
            )
          }
        },
        {
          label: "推送时间",
          prop: "created_at",
          minWidth: "200",
          // sortable: "custom",
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                <div>
                  <div style="margin-bottom: 2px;">{data.row.create_time || '--'}</div>
                </div>
              </div>
            );
          },
        },
        {
          minWidth: "190",
          label: "分配模式",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.push_type ? (
                  <div>{data.row.push_type == 0 ? "手动认领" : data.row.push_type == 1 ? "自动分配" : "手动分配"}</div>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },
        {
          minWidth: "190",
          label: "分配规则",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.push_style ? (
                  <div>{data.row.push_style == 1 ? "按人员分配" : data.row.push_type == 1 ? "按分组分配" : "排期分配"} {data.row.push_type == 1 && data.row.follow_group_id > 0 ? "(线索推送)" : "" }</div>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },
        {
          minWidth: "190",
          label: "分配状态",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    {
                      data.row.push_remark ? (
                        <el-popover
                          placement="top-start"
                          width="200"
                          trigger="hover"
                        >
                          <div class="flex-row">
                            {data.row.push_remark}
                          </div>
                          <div class="cus-userName cus-userName1" slot="reference">
                            {data.row.push_remark}
                          </div>
                        </el-popover>
                      ) : (
                        "正常"
                      )
                    }
                  </div>
                </div>
              </div>
            );
          },
        },
        {
          minWidth: "190",
          label: "首次分配",
          render: (h, data) => {
            return (
              <div>
                {data.row.group_user&& data.row.follow_user? (
                  <div>{data.row.follow_user.user_name } ({data.row.group_user.title})</div>
                ) : (
                  <div>
                    {data.row.follow_user ? data.row.follow_user.user_name :  "--"}
                  </div>
                )}
              </div>
            );
          },
        },
        {
          minWidth: "190",
          label: "城市",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.location ? (
                  <div>{data.row.location}</div>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },
        {
          minWidth: "190",
          label: "渠道号码",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.refer_dy_id ? (
                  <div>{data.row.refer_dy_id}</div>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },

        {
          minWidth: "190",
          label: "渠道名称",
          render: (h, data) => {
            return (
              <div>
                {data.row && data.row.refer_dy_name ? (
                  <div>{data.row.refer_dy_name}</div>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        },


        {
          label: "操作",
          minWidth: "150",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.$emit("onClickDetailXiansuo",data.row)
                  }}
                >
                  详情
                </el-link>

              </div>
            );
          },
        },

            ],//线索记录
        }

    },
    filters: {
    // 判断是否是掉公或转公客户
    publicStatusParse(row, row1) {
      // console.log(row,row1);
      if (row == 1) {
        return "已转公" + row1;
      } else if (row == 2) {
        return "已掉公" + row1;
      }
    },
    // 计算过去时间天数
    getPastDay(val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
  },
    created(){
      let userData = localStorage.getItem( 'juesename');
       userData = JSON.parse(userData)
      if(userData.diy_create_name){
        
         //录入人
         this.calculatecreate = userData.diy_create_name
       }
       if(userData.diy_follow_name){
         //维护人
         this.weihuuser = userData.diy_follow_name
       }
       if(userData.diy_take_name){
         //带看人
         this.diakanuser = userData.diy_take_name
       }
       if(userData.diy_deal_name){
         //成交人
         this.chnegjiaouser = userData.diy_deal_name
       }
      this.RenderingA()//全部
        this.RenderingB()//回访
        this.RenderingC()//带看
        this.RenderingD()//外呼
        this.RenderingF()//维护
    },
    mounted(){
  
    },
    methods:{
            //渲染"全部"表头
            RenderingA(){
              this.table_header0 = [
                  // {
                //   prop: "id",
                //   label: "ID",
                //   width: "80px",
                // },
                
                {
                  label: "客户名称",
                  width: "200px",
                  fixed: "left",
                  // sortable: "custom",
                  render: (h, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.cname ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div style="align-items: center;white-space: nowrap;" class="flex-row">
                                  客户编号：{data.row.id}
                                  <el-button
                                    size="mini"
                                    type="success"
                                    style="margin-left: 20px"
                                    onClick={() => {
                                      this.$emit("copyCusID",data.row.id)
                                    }}
                                  >
                                    复制
                                  </el-button>
                                </div>
                                <div class="flex-row">
                                  客户名称：{data.row.cname}
                                </div>
                                <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                                  this.$emit("Quick_Edit",data.row);
                                    }}>
                                  {data.row.cname}
                                </div> 
                                 {/* <div class="cus-userName" slot="reference">
                                  {data.row.cname}
                                </div>  */}
                              </el-popover>
                            ) : (
                              ""
                            )}
                            <div class="cus-sex">
                              {data.row.sex && data.row.sex == 1 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                                  alt=""
                                />
                              ) : null}
                              {data.row.sex && data.row.sex == 2 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                                  alt=""
                                />
                              ) : null}
                            </div>
                          </div>
                          <div class="cus-box-foot flex-row">
                        
                            {data.row && data.row.create_id == this.status_id ? (
                              <span class="cus-icon-type">私客</span>
                            ) : null}
                            
                            
                          </div>
                        </div>
                        {data.row.wxqy_id > 0 ? (
                          <img
                            class="cus-img"
                            src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                          />
                        ) : (
                          ""
                        )}
                        <div
                          class="fast-Edit-cus"
                          onClick={() => {
                            this.$emit("fastEditData",data.row)
                          }}
                        >
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "手机号",
                  fixed: "left",
                  width: "200px",
                  prop: "mobile",
                  render: (h, data) => {
                    const mobileFilter = function (val) {
                      let reg = /^(.{3}).*(.{3})$/;
                      return val.replace(reg, "$1*****$2");
                    };
                    return (
                      <div class="flex-box table-btns">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.id
                          ?
                          (<div class="last_call_follow div row">
                            <el-tooltip
                              class="item"
                              style="display:flex;flex-direction:row;justify-content:center"
                              effect="light"
                              placement="top"
                            >
                              <div slot="content" style="max-width:300px">
                                {data.row.last_call_follow.content}
                              </div>
                              <div class="cus-clue-text">
                                {data.row.last_call_follow
                                  &&
                                  data.row.last_call_follow.call_status == 1
                                  ?
                                  (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                                  :
                                  (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                              </div>
                            </el-tooltip>
                          </div>)
                          :
                          (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最新跟进记录",
                  prop: "last_follow_info",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.last_follow_info ? (
                              <el-popover
                                placement="top-start"
                                width="250"
                                trigger="hover"
                              >
                            
                                <div class="flex-row" style="white-space: pre-wrap;">
                                  {data.row.last_follow_info.content}
                                
                                </div>
                                <div class="cus-userName cus-userName1" slot="reference">
                                  {data.row.last_follow_info.content}
                                </div>
                              </el-popover>
                            ) : (
                              "--"
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: this.chnegjiaouser,
                  prop: "deal_user",
                  width: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.deal_user && data.row.deal_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.deal_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "成交周期",
                  prop: "",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                      </div>
                    )
                  }
                },
            
                {
                  label: "跟客天数",
                  prop: "",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row && data.row.genjin_day !== '' && data.row.genjin_day >= 0 ? (data.row.genjin_day + '天') : '--'}
                    
                      </div>
                    )
                  }
                },
            
                {
                  label: "客户等级",
                  prop: "",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot">
                          {data.row.level ? (
                            <span class="cus-icon-level">
                              {data.row.level.title}级
                            </span>
                          ) : "空"}
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户意向",
                  prop: "intention_community",
                  width: "100px",
                  render: (j, data) => {
                    return (
                           <div class="flex-row cus-header-user">
                            {data.row.intention_community ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div class="flex-row">
                                  {data.row.intention_community}
                                </div>
                                <div class="cus-userName" slot="reference">
                                  {data.row.intention_community}
                                </div>
                              </el-popover>
                            ) : (
                              "--"
                            )}
                          </div>
                    )
                  }
                },
                {
                  label: "客户状态",
                  prop: "tracking",
                  width: "130px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot" style="width:80px">
                          <el-popover
                            popper-class="cus-poper-Label"
                            placement="bottom"
                            width="100"
                            trigger="click"
                          >
                            <div class="f-list">
                              {this.status_list.length
                                ? this.status_list.map((item) => {
                                  return (
                                    <div
                                      class="f-item"
                                      onClick={() => {
                                        this.$emit("onClickFollowStatus",data.row,item)
                                      }}
                                    >
                                      {item.title || '--'}
                                    </div>
                                  );
                                })
                                : "--"}
                            </div>
                            {data.row.tracking &&
                              Object.keys(data.row.tracking).length &&
                              data.row.tracking.title == "有效客户" ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-customer"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                有效客户
                              </span>
                            ) : data.row.tracking &&
                              Object.keys(data.row.tracking).length ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-purchase"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                {data.row.tracking.title || '--'}
                              </span>
                            ) : (
                              <span
                                slot="reference"
                          
                          
                              >
                                {"--"}
                              </span>
                            )}
                          </el-popover>
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label:"最后通话状态",
                  prop:"",
                  width:"160px",
                  render:(j,data)=>{
                    return (
                      <div>
                        <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                          ? 'success'
                          : data.row.last_call_status && data.row.last_call_status === 2
                            ? 'danger'
                            : 'info'}>
                          {data.row.last_call_status && data.row.last_call_status === 1
                            ? '已接通'
                            : data.row.last_call_status && data.row.last_call_status === 2
                              ? '未接通'
                              : '未联系'}
                        </el-tag>
                      </div>
                    )
                  }
                },
                {
                  label: "客户标签",
                  prop: "label",
                  width: "220px",
                  render: (j, data) => {
                    return (
                
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user cus-clue-label">
                            {data.row.label && data.row.label.length > 0 ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div class="flex-row">
                                  {data.row.label.join(",")}
                                
                                </div>
                                <div class="cus-userName w100 flex-row" slot="reference">
                                  {data.row.label.length > 0
                                    ? data.row.label.slice(0, 2).map((item, index) => {
                                      return (
                                        <div class="flex-row align-center">
                                          <span class="cus-icon-label" key={index}>
                                            {item}
                                          </span>
                                        </div>
                                      );
                                    })
                                    : '--'}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}
                          
                          </div>
                        </div>
                        {data.row.label && data.row.label.length > 0 ? (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        ) : (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        )}
                      </div>
                    )
                  }
                },
                {
                  label:this.calculatecreate,
                  prop: "create_user",
                  width: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.create_user && data.row.create_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.create_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },
            
                {
                  label: this.weihuuser,
                  prop: "repeat_call_name",
                  width: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.follow_user && data.row.follow_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.follow_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.diakanuser,
                  width: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.take_user && data.row.take_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.take_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "跟进状态",
                  prop: "public_status",
                  width: "120px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.public_status == 2 && data.row.public2_status ? (
                          <span class="public-status">
                            {this.$options.filters.publicStatusParse(
                              data.row.public2_status, data.row.go_sea_day
                            
                            )}
                          </span>
                        ) : (
                          "正常"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "客户线索",
                  prop: "remark",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="flex-box">
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content" style="max-width:300px">
                            {data.row.remark}
                          </div>
                          <div class="cus-clue-text follow-content">
                            <span>{data.row.remark || '--'}</span>
                          </div>
                        </el-tooltip>
                    
                      </div>
                    );
                  },
                },
                {
                  label: "客户类型",
                  prop: "client_type",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.client_type ? (
                          <el-tag type="info">{data.row.client_type.title}</el-tag>
                        ) : "--"}
                      </div>
                    )
                  }
                },
                {
                  label: "创建时间",
                  prop: "created_at",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  prop: "updated_at",
                  label: "更新时间",
                  // sortable: "custom",
                  width: "200px",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_follow_day >= 0 &&
                          data.row.last_follow_day != "" ? (
                          <el-tooltip class="item" effect="light" placement="top">
                            <div slot="content" style="max-width:300px">
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                            <div class="follow-content">
                              <span style="margin-right: 5px;">
                                {this.$options.filters.getPastDay(
                                  data.row.last_follow_day
                                )}
                              </span>
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                          </el-tooltip>
                        ) : (
                          ""
                        )}
                        <div style="text-align: left;">{data.row.updated_at}</div>
                        <div
                          class="followLabel"
                          onClick={() => {
                            this.$emit("fastFollowUp",data.row)
                          }}
                        >
                          <img
                            src={
                              "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            }
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  width: "190px",
                  label: "来源",
                  render: (h, data) => {
                    return (
                      <div>
                        {data.row && data.row.source ? (
                          <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                        ) : (
                          ""
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "归属地",
                  width: "200px",
                  prop: "mobile",
                  render: (h, data) => {
                    return (
                      <div class="flex-box table-btns">
                        <span class="search-Belong">
                          {data.row.mobile_place == "" ||
                            data.row.mobile_place == undefined ? (
                            <el-button
                              type="primary"
                              plain
                              onClick={() => {
                                this.$emit("HomeAddress",data.row)
                              }}
                            >
                              归属地查询
                            </el-button>
                          ) : (
                            <span class="Address">{data.row.mobile_place}</span>
                          )}
                        </span>
                      </div>
                    );
                  },
                },
            
                {
                  label: "操作",
                  width: "150",
                  fixed: "right",
                  render: (h, data) => {
                    return (
                      <div>
                        <el-link
                          type="primary"
                          onClick={() => {
                            this.$emit("onClickDetail",data.row)
                          }}
                        >
                          详情
                        </el-link>
                    
                      </div>
                    );
                  },
                },
              ]
            },
            //渲染回访表头
            RenderingB(){
              this.table_header1 = [
                 // {
                //   prop: "id",
                //   label: "ID",
                //   width: "80px",
                // },
                
                {
                  label: "客户名称",
                  width: "200px",
                  fixed: "left",
                  // sortable: "custom",
                  render: (h, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.cname ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div style="align-items: center;white-space: nowrap;" class="flex-row">
                                  客户编号：{data.row.id}
                                  <el-button
                                    size="mini"
                                    type="success"
                                    style="margin-left: 20px"
                                    onClick={() => {
                                      this.$emit("copyCusID",data.row.id)
                                    }}
                                  >
                                    复制
                                  </el-button>
                                </div>
                                <div class="flex-row">
                                  客户名称：{data.row.cname}
                                
                                </div>
                                <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                                  this.$emit("Quick_Edit",data.row);
                                    }}>
                                  {data.row.cname}
                                </div> 
                                {/* <div class="cus-userName" slot="reference">
                                  {data.row.cname}
                                </div> */}
                              </el-popover>
                            ) : (
                              ""
                            )}
                            <div class="cus-sex">
                              {data.row.sex && data.row.sex == 1 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                                  alt=""
                                />
                              ) : null}
                              {data.row.sex && data.row.sex == 2 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                                  alt=""
                                />
                              ) : null}
                            </div>
                          </div>
                          <div class="cus-box-foot flex-row">
                        
                            {data.row && data.row.create_id == this.status_id ? (
                              <span class="cus-icon-type">私客</span>
                            ) : null}
                            
                            
                          </div>
                        </div>
                        {data.row.wxqy_id > 0 ? (
                          <img
                            class="cus-img"
                            src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                          />
                        ) : (
                          ""
                        )}
                        <div
                          class="fast-Edit-cus"
                          onClick={() => {
                            this.$emit("fastEditData",data.row)
                          }}
                        >
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "手机号",
                  fixed: "left",
                  width: "200px",
                  prop: "mobile",
                  render: (h, data) => {
                    const mobileFilter = function (val) {
                      let reg = /^(.{3}).*(.{3})$/;
                      return val.replace(reg, "$1*****$2");
                    };
                    return (
                      <div class="flex-box table-btns">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.id
                          ?
                          (<div class="last_call_follow div row">
                            <el-tooltip
                              class="item"
                              style="display:flex;flex-direction:row;justify-content:center"
                              effect="light"
                              placement="top"
                            >
                              <div slot="content" style="max-width:300px">
                                {data.row.last_call_follow.content}
                              </div>
                              <div class="cus-clue-text">
                                {data.row.last_call_follow
                                  &&
                                  data.row.last_call_follow.call_status == 1
                                  ?
                                  (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                                  :
                                  (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                              </div>
                            </el-tooltip>
                          </div>)
                          :
                          (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最新回访记录",
                  prop: "last_follow_info",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.last_follow_info ? (
                              <el-popover
                                placement="top-start"
                                width="250"
                                trigger="hover"
                              >
                            
                                <div class="flex-row" style="white-space: pre-wrap;">
                                  {data.row.last_follow_info.content}
                                
                                </div>
                                <div class="cus-userName cus-userName1" slot="reference">
                                  {data.row.last_follow_info.content}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "跟客天数",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row && data.row.genjin_day !== '' && data.row.genjin_day >= 0 ? (data.row.genjin_day + '天') : '--'}
                    
                      </div>
                    )
                  }
                },
            
                {
                  label: "客户等级",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot">
                          {data.row.level ? (
                            <span class="cus-icon-level">
                              {data.row.level.title}级
                            </span>
                          ) : "空"}
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户意向",
                  prop: "",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <el-tooltip class="item" effect="light" placement="top">
                        <div slot="content" >
                          {data.row.intention_community}
                        </div>
                        <div class="cus-clue-text cus-clue-text_community">
                          <span>{data.row.intention_community || '--'}</span>
                        </div>
                      </el-tooltip>
                    )
                  }
                },
                {
                  label: "客户状态",
                  prop: "tracking",
                  width: "130px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot" style="width:80px">
                          <el-popover
                            popper-class="cus-poper-Label"
                            placement="bottom"
                            width="100"
                            trigger="click"
                          >
                            <div class="f-list">
                              {this.status_list.length
                                ? this.status_list.map((item) => {
                                  return (
                                    <div
                                      class="f-item"
                                      onClick={() => {
                                        this.$emit("onClickFollowStatus",data.row,item)
                                      }}
                                    >
                                      {item.title || '--'}
                                    </div>
                                  );
                                })
                                : "--"}
                            </div>
                            {data.row.tracking &&
                              Object.keys(data.row.tracking).length &&
                              data.row.tracking.title == "有效客户" ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-customer"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                有效客户
                              </span>
                            ) : data.row.tracking &&
                              Object.keys(data.row.tracking).length ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-purchase"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                {data.row.tracking.title || '--'}
                              </span>
                            ) : (
                              <span
                                slot="reference"
                          
                          
                              >
                                {"--"}
                              </span>
                            )}
                          </el-popover>
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label:"最后通话状态",
                  prop:"",
                  width:"160px",
                  render:(j,data)=>{
                    return (
                      <div>
                        <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                          ? 'success'
                          : data.row.last_call_status && data.row.last_call_status === 2
                            ? 'danger'
                            : 'info'}>
                          {data.row.last_call_status && data.row.last_call_status === 1
                            ? '已接通'
                            : data.row.last_call_status && data.row.last_call_status === 2
                              ? '未接通'
                              : '未联系'}
                        </el-tag>
                      </div>
                    )
                  }
                },
                {
                  label: "客户标签",
                  prop: "label",
                  width: "220px",
                  render: (j, data) => {
                    return (
                
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user cus-clue-label">
                            {data.row.label && data.row.label.length > 0 ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div class="flex-row">
                                  {data.row.label.join(",")}
                                
                                </div>
                                <div class="cus-userName w100 flex-row" slot="reference">
                                  {data.row.label.length > 0
                                    ? data.row.label.slice(0, 2).map((item, index) => {
                                      return (
                                        <div class="flex-row align-center">
                                          <span class="cus-icon-label" key={index}>
                                            {item}
                                          </span>
                                        </div>
                                      );
                                    })
                                    : '--'}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}
                          
                          </div>
                        </div>
                        {data.row.label && data.row.label.length > 0 ? (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        ) : (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: this.calculatecreate,
                  prop: "create_user",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.create_user && data.row.create_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.create_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },
            
                {
                  label: this.weihuuser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.follow_user && data.row.follow_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.follow_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.diakanuser,
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.take_user && data.row.take_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.take_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
            
                {
                  label: this.chnegjiaouser,
                  prop: "deal_user",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.deal_user && data.row.deal_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.deal_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "成交周期",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: "跟进状态",
                  prop: "public_status",
                  minWidth: "120px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.public_status == 2 && data.row.public2_status ? (
                          <span class="public-status">
                            {this.$options.filters.publicStatusParse(
                              data.row.public2_status, data.row.go_sea_day
                            )}
                          </span>
                        ) : (
                          "正常"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "客户线索",
                  prop: "remark",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="flex-box">
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content" style="max-width:300px">
                            {data.row.remark}
                          </div>
                          <div class="cus-clue-text follow-content">
                            <span>{data.row.remark || '--'}</span>
                          </div>
                        </el-tooltip>
                    
                      </div>
                    );
                  },
                },
                {
                  label: "客户类型",
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.client_type ? (
                          <el-tag type="info">{data.row.client_type.title}</el-tag>
                        ) : "--"}
                      </div>
                    )
                  }
                },
                {
                  label: "创建时间",
                  prop: "created_at",
                  minWidth: "200",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  prop: "updated_at",
                  label: "更新时间",
                  // sortable: "custom",
                  width: "200px",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_follow_day >= 0 &&
                          data.row.last_follow_day != "" ? (
                          <el-tooltip class="item" effect="light" placement="top">
                            <div slot="content" style="max-width:300px">
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                            <div class="follow-content">
                              <span style="margin-right: 5px;">
                                {this.$options.filters.getPastDay(
                                  data.row.last_follow_day
                                )}
                              </span>
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                          </el-tooltip>
                        ) : (
                          ""
                        )}
                        <div style="text-align: left;">{data.row.updated_at}</div>
                        <div
                          class="followLabel"
                          onClick={() => {
                            this.$emit("fastFollowUp",data.row)
                          }}
                        >
                          <img
                            src={
                              "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            }
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                
                  minWidth: "190",
                  label: "来源",
                  render: (h, data) => {
                    return (
                      <div>
                        {data.row && data.row.source ? (
                          <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                        ) : (
                          ""
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "归属地",
                  minWidth: "200",
                  prop: "mobile",
                  render: (h, data) => {
                    return (
                      <div class="flex-box table-btns">
                        <span class="search-Belong">
                          {data.row.mobile_place == "" ||
                            data.row.mobile_place == undefined ? (
                            <el-button
                              type="primary"
                              plain
                              onClick={() => {
                                this.$emit("HomeAddress",data.row)
                              }}
                            >
                              归属地查询
                            </el-button>
                          ) : (
                            <span class="Address">{data.row.mobile_place}</span>
                          )}
                        </span>
                      </div>
                    );
                  },
                },
            
                {
                  label: "操作",
                  minWidth: "150",
                  fixed: "right",
                  render: (h, data) => {
                    return (
                      <div>
                        <el-link
                          type="primary"
                          onClick={() => {
                            this.$emit("onClickDetail",data.row)
                          }}
                        >
                          详情
                        </el-link>
                    
                      </div>
                    );
                  },
                },
            
              ]
            },
            //渲染带看表头
            RenderingC(){
              this.table_header2 = [
                 // {
                //   prop: "id",
                //   label: "ID",
                //   width: "80px",
                // },
                
                {
                  label: "客户名称",
                  width: "200px",
                  fixed: "left",
                  // sortable: "custom",
                  render: (h, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.cname ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div style="align-items: center;white-space: nowrap;" class="flex-row">
                                  客户编号：{data.row.id}
                                  <el-button
                                    size="mini"
                                    type="success"
                                    style="margin-left: 20px"
                                    onClick={() => {
                                      this.$emit("copyCusID",data.row.id)
                                    }}
                                  >
                                    复制
                                  </el-button>
                                </div>
                                <div class="flex-row">
                                  客户名称：{data.row.cname}
                                
                                </div>
                                <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                                  this.$emit("Quick_Edit",data.row);
                                    }}>
                                  {data.row.cname}
                                </div> 
                                {/* <div class="cus-userName" slot="reference">
                                  {data.row.cname}
                                </div> */}
                              </el-popover>
                            ) : (
                              ""
                            )}
                            <div class="cus-sex">
                              {data.row.sex && data.row.sex == 1 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                                  alt=""
                                />
                              ) : null}
                              {data.row.sex && data.row.sex == 2 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                                  alt=""
                                />
                              ) : null}
                            </div>
                          </div>
                          <div class="cus-box-foot flex-row">
                        
                            {data.row && data.row.create_id == this.status_id ? (
                              <span class="cus-icon-type">私客</span>
                            ) : null}
                            
                            
                          </div>
                        </div>
                        {data.row.wxqy_id > 0 ? (
                          <img
                            class="cus-img"
                            src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                          />
                        ) : (
                          ""
                        )}
                        <div
                          class="fast-Edit-cus"
                          onClick={() => {
                            this.$emit("fastEditData",data.row)
                          }}
                        >
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "手机号",
                  fixed: "left",
                  width: "200",
                  prop: "mobile",
                  render: (h, data) => {
                    const mobileFilter = function (val) {
                      let reg = /^(.{3}).*(.{3})$/;
                      return val.replace(reg, "$1*****$2");
                    };
                    return (
                      <div class="flex-box table-btns">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.id
                          ?
                          (<div class="last_call_follow w180 div row">
                            <el-tooltip
                              class="item"
                              style="display:flex;flex-direction:row;justify-content:center"
                              effect="light"
                              placement="top"
                            >
                              <div slot="content" style="width:300px">
                                {data.row.last_call_follow.content}
                              </div>
                              <div class="cus-clue-text">
                                {data.row.last_call_follow
                                  &&
                                  data.row.last_call_follow.call_status == 1
                                  ?
                                  (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                                  :
                                  (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                              </div>
                            </el-tooltip>
                          </div>)
                          :
                          (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最新带看记录",
                  prop: "last_take_info",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {
                              data.row.last_take_info && data.row.last_take_info.content ? (
                                <el-popover
                                  placement="top-start"
                                  width="250"
                                  trigger="hover"
                                >
                                  <div class="flex-row" style="white-space: pre-wrap;">
                                    {data.row.last_take_info.content}
                                  </div>
                                  <div class="cus-userName cus-userName1" slot="reference">
                                    {data.row.last_take_info.content}
                                  </div>
                                </el-popover>
                              ) : (
                                ""
                              )
                            }
                          </div>
                        </div>
                      </div>
                    );
                  }
                },
                {
                  label: "带看次数",
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.last_take_info && data.row.last_take_info.take_num || 0}
                      </div>
                    )
                  }
                },
            
                {
                  label: "最近带看时间",
                  prop: "client_type",
                  minWidth: "135px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.last_take_info ? (
                          <span style="margin-right: 5px;">
                            {data.row.last_take_info.take_date}{data.row.last_take_info.take_time == 1 ? '上午' : (data.row.last_take_info.take_time == 2 ? '下午' : '晚上')}
                          </span>
                        ) : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: "项目名称",
                  prop: "last_take_info",
                  minWidth: "135px",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_take_info&&data.row.last_take_info.project_list.length
                          ? data.row.last_take_info.project_list.map((item, index) => {
                            return (
                              <div style="text-align: center;">
                                <span style="cursor: pointer;" key={index}
                                        onClick={() => {
                                    this.$emit("openproject",item)
                                  }}>
                                  {item.name}
                                </span>
                              </div>
                            );
                          })
                          : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: this.diakanuser,
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.last_take_info ? (
                          <span style="margin-right: 5px;">
                            {data.row.last_take_info.admin ? data.row.last_take_info.admin.user_name : '--'}
                          </span>
                        ) : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: "陪看人",
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.last_take_info&&data.row.last_take_info.accompany ? (
                          <span style="margin-right: 5px;">
                            {data.row.last_take_info.accompany}
                          </span>
                        ) : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: "分边比例",
                  prop: "last_take_info",
                  minWidth: "130px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.last_take_info&&data.row.last_take_info.proportion_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.last_take_info.proportion_name}
                          </span>
                        ) : '--'}
                      </div>
                    )
                  }
                },
            
                {
                  label: "客户等级",
                  prop: "level",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: center;">
                        <div class="cus-box-foot">
                          {data.row.level ? (
                            <span class="cus-icon-level">
                              {data.row.level.title}级
                            </span>
                          ) : '空'}
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户状态",
                  prop: "tracking",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot" style="width:80px">
                          <el-popover
                            popper-class="cus-poper-Label"
                            placement="bottom"
                            width="100"
                            trigger="click"
                          >
                            <div class="f-list">
                              {this.status_list.length
                                ? this.status_list.map((item) => {
                                  return (
                                    <div
                                      class="f-item"
                                      onClick={() => {
                                        this.$emit("onClickFollowStatus",data.row,item)
                                      }}
                                    >
                                      {item.title}
                                    </div>
                                  );
                                })
                                : null}
                            </div>
                            {data.row.tracking &&
                              Object.keys(data.row.tracking).length &&
                              data.row.tracking.title == "有效客户" ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-customer"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                有效客户
                              </span>
                            ) : data.row.tracking &&
                              Object.keys(data.row.tracking).length ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-purchase"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                {data.row.tracking.title}
                              </span>
                            ) : (
                              ""
                            )}
                          </el-popover>
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label:"最后通话状态",
                  prop:"",
                  width:"160px",
                  render:(j,data)=>{
                    return (
                      <div>
                        <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                          ? 'success'
                          : data.row.last_call_status && data.row.last_call_status === 2
                            ? 'danger'
                            : 'info'}>
                          {data.row.last_call_status && data.row.last_call_status === 1
                            ? '已接通'
                            : data.row.last_call_status && data.row.last_call_status === 2
                              ? '未接通'
                              : '未联系'}
                        </el-tag>
                      </div>
                    )
                  }
                },
                {
                  label: "客户标签",
                  prop: "label",
                  width: "200px",
                  render: (j, data) => {
                    return (
                
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user cus-clue-label">
                            {data.row.label && data.row.label.length ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div class="flex-row">
                                  {data.row.label.join(",")}
                                
                                </div>
                                <div class="cus-userName w100 flex-row" slot="reference">
                                  {data.row.label.length
                                    ? data.row.label.slice(0, 2).map((item, index) => {
                                      return (
                                        <div class="flex-row align-center">
                                          <span class="cus-icon-label" key={index}>
                                            {item}
                                          </span>
                                        </div>
                                      );
                                    })
                                    : '--'}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}
                          
                          </div>
                        </div>
                        {data.row.label && data.row.label.length ? (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        ) : (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: this.calculatecreate,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.create_user && data.row.create_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.create_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.weihuuser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.follow_user && data.row.follow_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.follow_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.chnegjiaouser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.deal_user && data.row.deal_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.deal_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "跟进状态",
                  prop: "public_status",
                  minWidth: "120px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:center">
                        {data.row.public_status == 2 && data.row.public2_status ? (
                          <span class="public-status">
                            {this.$options.filters.publicStatusParse(
                              data.row.public2_status, data.row.go_sea_day
                            )}
                          </span>
                        ) : (
                          <span
                            class="cus-icon-customer"
                          >
                            正常
                          </span>
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "客户线索",
                  prop: "remark",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="flex-box">
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content" style="max-width:300px">
                            {data.row.remark}
                          </div>
                          <div class="cus-clue-text follow-content">
                            <span>{data.row.remark}</span>
                          </div>
                        </el-tooltip>
                      </div>
                    );
                  },
                },
                {
                  label: "客户类型",
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: center;">
                        {data.row.client_type ? (
                          <el-tag type="info">{data.row.client_type.title}</el-tag>
                        ) : null}
                      </div>
                    )
                  }
                },
                {
                  label: "创建时间",
                  prop: "created_at",
                  minWidth: "200",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  prop: "updated_at",
                  label: "更新时间",
                  // sortable: "custom",
                  width: "200px",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_follow_day >= 0 &&
                          data.row.last_follow_day != "" ? (
                          <el-tooltip class="item" effect="light" placement="top">
                            <div slot="content" style="max-width:300px">
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                            <div class="follow-content">
                              <span style="margin-right: 5px;">
                                {this.$options.filters.getPastDay(
                                  data.row.last_follow_day
                                )}
                              </span>
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                          </el-tooltip>
                        ) : (
                          ""
                        )}
                        <div style="text-align: left;">{data.row.updated_at}</div>
                        <div
                          class="followLabel"
                          onClick={() => {
                            this.$emit("fastFollowUp",data.row)
                          }}
                        >
                          <img
                            src={
                              "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            }
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  minWidth: "190",
                  label: "来源",
                  render: (h, data) => {
                    return (
                      <div>
                        {data.row && data.row.source ? (
                          <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                        ) : (
                          ""
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "归属地",
                  minWidth: "200",
                  prop: "mobile",
                  render: (h, data) => {
                
                    return (
                      <div class="flex-box table-btns">
                        <span class="search-Belong">
                          {data.row.mobile_place == "" ||
                            data.row.mobile_place == undefined ? (
                            <el-button
                              type="primary"
                              plain
                              onClick={() => {
                                this.$emit("HomeAddress",data.row)
                              }}
                            >
                              归属地查询
                            </el-button>
                          ) : (
                            <span class="Address">{data.row.mobile_place}</span>
                          )}
                        </span>
                      </div>
                    );
                  },
                },
            
                {
                  label: "操作",
                  minWidth: "150",
                  fixed: "right",
                  render: (h, data) => {
                    return (
                      <div>
                        <el-link
                          type="primary"
                          onClick={() => {
                            this.$emit("onClickDetail",data.row)
                          }}
                        >
                          详情
                        </el-link>
                    
                      </div>
                    );
                  },
                },
              ]
            },
            //渲染外呼表头
            RenderingD(){
              this.table_header3 = [
              {
                  label: "客户名称",
                  width: "200px",
                  fixed: "left",
                  // sortable: "custom",
                  render: (h, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.cname ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div style="align-items: center;white-space: nowrap;" class="flex-row">
                                  客户编号：{data.row.id}
                                  <el-button
                                    size="mini"
                                    type="success"
                                    style="margin-left: 20px"
                                    onClick={() => {
                                      this.$emit("copyCusID",data.row.id)
                                    }}
                                  >
                                    复制
                                  </el-button>
                                </div>
                                <div class="flex-row">
                                  客户名称：{data.row.cname}
                                
                                </div>
                                <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                                  this.$emit("Quick_Edit",data.row);
                                    }}>
                                  {data.row.cname}
                                </div> 
                                {/* <div class="cus-userName" slot="reference">
                                  {data.row.cname}
                                </div> */}
                              </el-popover>
                            ) : (
                              ""
                            )}
                            <div class="cus-sex">
                              {data.row.sex && data.row.sex == 1 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                                  alt=""
                                />
                              ) : null}
                              {data.row.sex && data.row.sex == 2 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                                  alt=""
                                />
                              ) : null}
                            </div>
                          </div>
                          <div class="cus-box-foot flex-row">
                        
                            {data.row && data.row.create_id == this.status_id ? (
                              <span class="cus-icon-type">私客</span>
                            ) : null}
                            
                            
                          </div>
                        </div>
                        {data.row.wxqy_id > 0 ? (
                          <img
                            class="cus-img"
                            src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                          />
                        ) : (
                          ""
                        )}
                        <div
                          class="fast-Edit-cus"
                          onClick={() => {
                            this.$emit("fastEditData",data.row)
                          }}
                        >
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "手机号",
                  fixed: "left",
                  width: "200px",
                  prop: "mobile",
                  render: (h, data) => {
                    const mobileFilter = function (val) {
                      let reg = /^(.{3}).*(.{3})$/;
                      return val.replace(reg, "$1*****$2");
                    };
                    return (
                      <div class="flex-box table-btns">
                        {data.row.last_call_info
                          &&
                          data.row.last_call_info.id
                          ?
                          (<div class="last_call_follow w180 div row">
                            <el-tooltip
                              class="item"
                              style="display:flex;flex-direction:row;justify-content:center"
                              effect="light"
                              placement="top"
                            >
                              <div slot="content" style="max-width:300px">
                                {data.row.last_call_info.content}
                              </div>
                              <div class="cus-clue-text">
                                {data.row.last_call_info
                                  &&
                                  data.row.last_call_info.call_status == 1
                                  ?
                                  (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                                  :
                                  (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                              </div>
                            </el-tooltip>
                          </div>)
                          :
                          (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                      
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最近拨打记录",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.last_call_info ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                            
                                <div class="flex-row">
                                  {data.row.last_call_info.content}
                                
                                </div>
                                <div class="cus-userName cus-userName1" slot="reference">
                                  {data.row.last_call_info.content}
                                </div>
                              </el-popover>
                            ) : (
                              <span></span>
                            )}
                          
                          </div>
                        </div>
                      </div>
                    );
                  },
                },
            
                {
                  label: "拨打次数",
                  prop: "call_num",
                  minWidth: "100px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info && data.row.call_info.call_num || "--"}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "接通次数",
                  prop: "call_open_num",
                  minWidth: "100px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info && data.row.call_info.call_open_num || '--'}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最近状态",
                  prop: "last_call_status",
                  minWidth: "100px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <el-tag type={data.row.call_info && data.row.call_info.last_call_status == 1 ? 'success ' : 'danger'}>{data.row.call_info && data.row.call_info.last_call_status == 1 ? "接通" : "未接通"}</el-tag>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "通话录音",
                  prop: "",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.last_call_info && data.row.last_call_info.record_url ? (<el-link
                          style="margin-left: 5px"
                          onClick={() => {
                            this.$emit("play1",data.row)
                            }}>
                          <div class="audio_img">
                        
                            <img
                              style="width: 20px; object-fit: cover"
                              src={data.row.isPlaying ? `${this.$imageDomain}/static/admin/outbound/play_voice.gif` : `${this.$imageDomain}/static/admin/outbound/voice_icon.png`}
                              alt=""
                            />
                        
                          </div>
                        </el-link>) : '--'}
                    
                      </div >
                    );
                  },
                },
            
                {
                  label: "最近通话时长",
                  prop: "last_call_duration",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {this.formatTelTime(data.row.call_info && data.row.call_info.last_call_duration || 0)}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "首电响应时长",
                  prop: "first_call_day",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info ? (data.row.call_info.first_call_day == "" ? "" : data.row.call_info.first_call_day) : ''}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "复电响应时长",
                  prop: "repeat_call_day",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info ? (data.row.call_info.repeat_call_day == "" ? "" : data.row.call_info.repeat_call_day) : ''}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最近拨打成员",
                  prop: "last_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info && data.row.call_info.last_call_name || '--'}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "首次拨打成员",
                  prop: "first_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info && data.row.call_info.first_call_name || '--'}
                        </div>
                      </div>
                    );
                  },
                }
                ,
                {
                  label: "二次复电成员",
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          {data.row.call_info && data.row.call_info.repeat_call_name || '--'}
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: this.calculatecreate,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.create_user && data.row.create_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.create_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.weihuuser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.follow_user && data.row.follow_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.follow_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label:this.diakanuser,
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.take_user && data.row.take_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.take_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.chnegjiaouser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.deal_user && data.row.deal_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.deal_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "客户类型",
                  prop: "client_type",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.client_type ? (
                          <el-tag type="info">{data.row.client_type.title}</el-tag>
                        ) : null}
                      </div>
                    )
                  }
                },
                {
                  label: "客户等级",
                  prop: "level",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot">
                          {data.row.level ? (
                            <span class="cus-icon-level">
                              {data.row.level.title}级
                            </span>
                          ) : '空'}
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户状态",
                  prop: "tracking",
                  width: "200px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot" style="width:80px">
                          <el-popover
                            popper-class="cus-poper-Label"
                            placement="bottom"
                            width="100"
                            trigger="click"
                          >
                            <div class="f-list">
                              {this.status_list.length
                                ? this.status_list.map((item) => {
                                  return (
                                    <div
                                      class="f-item"
                                      onClick={() => {
                                        this.$emit("onClickFollowStatus",data.row,item)
                                      }}
                                    >
                                      {item.title}
                                    </div>
                                  );
                                })
                                : null}
                            </div>
                            {data.row.tracking &&
                              Object.keys(data.row.tracking).length &&
                              data.row.tracking.title == "有效客户" ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-customer"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                有效客户
                              </span>
                            ) : data.row.tracking &&
                              Object.keys(data.row.tracking).length ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-purchase"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                {data.row.tracking.title}
                              </span>
                            ) : (
                              ""
                            )}
                          </el-popover>
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "创建时间",
                  prop: "created_at",
                  minWidth: "200",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  prop: "updated_at",
                  label: "更新时间",
                  // sortable: "custom",
                  width: "200",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_follow_day >= 0 &&
                          data.row.last_follow_day != "" ? (
                          <el-tooltip class="item" effect="light" placement="top">
                            <div slot="content" style="max-width:300px">
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                            <div class="follow-content">
                              <span style="margin-right: 5px;">
                                {this.$options.filters.getPastDay(
                                  data.row.last_follow_day
                                )}
                              </span>
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                          </el-tooltip>
                        ) : (
                          ""
                        )}
                        <div style="text-align: left;">{data.row.updated_at}</div>
                        <div
                          class="followLabel"
                          onClick={() => {
                            this.$emit("fastFollowUp",data.row)
                          }}
                        >
                          <img
                            src={
                              "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            }
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "归属地",
                  minWidth: "200",
                  prop: "mobile",
                  render: (h, data) => {
                    return (
                      <div class="flex-box table-btns">
                        <span class="search-Belong">
                          {data.row.mobile_place == "" ||
                            data.row.mobile_place == undefined ? (
                            <el-button
                              type="primary"
                              plain
                              onClick={() => {
                                this.$emit("HomeAddress",data.row)
                              }}
                            >
                              归属地查询
                            </el-button>
                          ) : (
                            <span class="Address">{data.row.mobile_place}</span>
                          )}
                        </span>
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "操作",
                  minWidth: "150",
                  fixed: "right",
                  render: (h, data) => {
                    return (
                      <div>
                        <el-link
                          type="primary"
                          onClick={() => {
                            this.$emit("onClickDetail",data.row)
                          }}
                        >
                          详情
                        </el-link>
                    
                      </div>
                    );
                  },
                },
            
              ]
            },
            //渲染维护表头
            RenderingF(){
              this.table_header4 = [
                 // {
                //   prop: "id",
                //   label: "ID",
                //   width: "80px",
                // },
                
                {
                  label: "客户名称",
                  width: "200px",
                  fixed: "left",
                  // sortable: "custom",
                  render: (h, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.cname ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div style="align-items: center;white-space: nowrap;" class="flex-row">
                                  客户编号：{data.row.id}
                                  <el-button
                                    size="mini"
                                    type="success"
                                    style="margin-left: 20px"
                                    onClick={() => {
                                      this.$emit("copyCusID",data.row.id)
                                    }}
                                  >
                                    复制
                                  </el-button>
                                </div>
                                <div class="flex-row">
                                  客户名称：{data.row.cname}
                                
                                </div>
                                <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                                  this.$emit("Quick_Edit",data.row);
                                    }}>
                                  {data.row.cname}
                                </div> 
                                {/* <div class="cus-userName" slot="reference">
                                  {data.row.cname}
                                </div> */}
                              </el-popover>
                            ) : (
                              ""
                            )}
                            <div class="cus-sex">
                              {data.row.sex && data.row.sex == 1 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                                  alt=""
                                />
                              ) : null}
                              {data.row.sex && data.row.sex == 2 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                                  alt=""
                                />
                              ) : null}
                            </div>
                          </div>
                          <div class="cus-box-foot flex-row">
                        
                            {data.row && data.row.create_id == this.status_id ? (
                              <span class="cus-icon-type">私客</span>
                            ) : null}
                            
                            
                          </div>
                        </div>
                        {data.row.wxqy_id > 0 ? (
                          <img
                            class="cus-img"
                            src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                          />
                        ) : (
                          ""
                        )}
                        <div
                          class="fast-Edit-cus"
                          onClick={() => {
                            this.$emit("fastEditData",data.row)
                          }}
                        >
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "手机号",
                  fixed: "left",
                  width: "200",
                  prop: "mobile",
                  render: (h, data) => {
                    const mobileFilter = function (val) {
                      let reg = /^(.{3}).*(.{3})$/;
                      return val.replace(reg, "$1*****$2");
                    };
                    return (
                      <div class="flex-box table-btns">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.id
                          ?
                          (<div class="last_call_follow w180 div row">
                            <el-tooltip
                              class="item"
                              style="display:flex;flex-direction:row;justify-content:center"
                              effect="light"
                              placement="top"
                            >
                              <div slot="content" style="max-width:300px">
                                {data.row.last_call_follow.content}
                              </div>
                              <div class="cus-clue-text">
                                {data.row.last_call_follow
                                  &&
                                  data.row.last_call_follow.call_status == 1
                                  ?
                                  (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                                  :
                                  (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                              </div>
                            </el-tooltip>
                          </div>)
                          :
                          (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最新维护记录",
                  prop: "last_follow_info",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.last_follow_info ? (
                              <el-popover
                                placement="top-start"
                                width="250"
                                trigger="hover"
                              >
                            
                                <div class="flex-row" style="white-space: pre-wrap;">
                                  {data.row.last_follow_info.content}
                                
                                </div>
                                <div class="cus-userName cus-userName1" slot="reference">
                                  {data.row.last_follow_info.content}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "跟客天数",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row && data.row.genjin_day !== '' && data.row.genjin_day >= 0 ? (data.row.genjin_day + '天') : '--'}
                    
                      </div>
                    )
                  }
                },
            
                {
                  label: "客户等级",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot">
                          {data.row.level ? (
                            <span class="cus-icon-level">
                              {data.row.level.title}级
                            </span>
                          ) : "空"}
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户意向",
                  prop: "",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <el-tooltip class="item" effect="light" placement="top">
                        <div slot="content" >
                          {data.row.intention_community}
                        </div>
                        <div class="cus-clue-text cus-clue-text_community">
                          <span>{data.row.intention_community || '--'}</span>
                        </div>
                      </el-tooltip>
                    )
                  }
                },
                {
                  label: "客户状态",
                  prop: "tracking",
                  width: "130px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot" style="width:80px">
                          <el-popover
                            popper-class="cus-poper-Label"
                            placement="bottom"
                            width="100"
                            trigger="click"
                          >
                            <div class="f-list">
                              {this.status_list.length
                                ? this.status_list.map((item) => {
                                  return (
                                    <div
                                      class="f-item"
                                      onClick={() => {
                                        this.$emit("onClickFollowStatus",data.row,item)
                                      }}
                                    >
                                      {item.title || '--'}
                                    </div>
                                  );
                                })
                                : "--"}
                            </div>
                            {data.row.tracking &&
                              Object.keys(data.row.tracking).length &&
                              data.row.tracking.title == "有效客户" ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-customer"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                有效客户
                              </span>
                            ) : data.row.tracking &&
                              Object.keys(data.row.tracking).length ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-purchase"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                {data.row.tracking.title || '--'}
                              </span>
                            ) : (
                              <span
                                slot="reference"


                              >
                                {"--"}
                              </span>
                            )}
                          </el-popover>
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label:"最后通话状态",
                  prop:"",
                  width:"160px",
                  render:(j,data)=>{
                    return (
                      <div>
                        <el-tag type={data.row.last_call_status && data.row.last_call_status === 1
                          ? 'success'
                          : data.row.last_call_status && data.row.last_call_status === 2
                            ? 'danger'
                            : 'info'}>
                          {data.row.last_call_status && data.row.last_call_status === 1
                            ? '已接通'
                            : data.row.last_call_status && data.row.last_call_status === 2
                              ? '未接通'
                              : '未联系'}
                        </el-tag>
                      </div>
                    )
                  }
                },
                {
                  label: "客户标签",
                  prop: "label",
                  width: "220px",
                  render: (j, data) => {
                    return (

                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user cus-clue-label">
                            {data.row.label && data.row.label.length > 0 ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div class="flex-row">
                                  {data.row.label.join(",")}

                                </div>
                                <div class="cus-userName w100 flex-row" slot="reference">
                                  {data.row.label.length > 0
                                    ? data.row.label.slice(0, 2).map((item, index) => {
                                      return (
                                        <div class="flex-row align-center">
                                          <span class="cus-icon-label" key={index}>
                                            {item}
                                          </span>
                                        </div>
                                      );
                                    })
                                    : '--'}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}

                          </div>
                        </div>
                        {data.row.label && data.row.label.length > 0 ? (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        ) : (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: this.calculatecreate,
                  prop: "create_user",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.create_user && data.row.create_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.create_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },

                {
                  label: this.weihuuser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.follow_user && data.row.follow_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.follow_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: this.diakanuser,
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.take_user && data.row.take_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.take_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },

                {
                  label: this.chnegjiaouser,
                  prop: "deal_user",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.deal_user && data.row.deal_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.deal_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "成交周期",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: "跟进状态",
                  prop: "public_status",
                  minWidth: "120px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.public_status == 2 && data.row.public2_status ? (
                          <span class="public-status">
                            {this.$options.filters.publicStatusParse(
                              data.row.public2_status, data.row.go_sea_day
                            )}
                          </span>
                        ) : (
                          "正常"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "客户线索",
                  prop: "remark",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="flex-box">
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content" style="max-width:300px">
                            {data.row.remark}
                          </div>
                          <div class="cus-clue-text follow-content">
                            <span>{data.row.remark || '--'}</span>
                          </div>
                        </el-tooltip>

                      </div>
                    );
                  },
                },
                {
                  label: "客户类型",
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.client_type ? (
                          <el-tag type="info">{data.row.client_type.title}</el-tag>
                        ) : "--"}
                      </div>
                    )
                  }
                },
                {
                  label: "创建时间",
                  prop: "created_at",
                  minWidth: "200",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  prop: "updated_at",
                  label: "更新时间",
                  // sortable: "custom",
                  width: "200px",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_follow_day >= 0 &&
                          data.row.last_follow_day != "" ? (
                          <el-tooltip class="item" effect="light" placement="top">
                            <div slot="content" style="max-width:300px">
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                            <div class="follow-content">
                              <span style="margin-right: 5px;">
                                {this.$options.filters.getPastDay(
                                  data.row.last_follow_day
                                )}
                              </span>
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                          </el-tooltip>
                        ) : (
                          ""
                        )}
                        <div style="text-align: left;">{data.row.updated_at}</div>
                        <div
                          class="followLabel"
                          onClick={() => {
                            this.$emit("fastFollowUp",data.row)
                          }}
                        >
                          <img
                            src={
                              "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            }
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  minWidth: "190",
                  label: "来源",
                  render: (h, data) => {
                    return (
                      <div>
                        {data.row && data.row.source ? (
                          <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                        ) : (
                          ""
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "归属地",
                  minWidth: "200",
                  prop: "mobile",
                  render: (h, data) => {
                    return (
                      <div class="flex-box table-btns">
                        <span class="search-Belong">
                          {data.row.mobile_place == "" ||
                            data.row.mobile_place == undefined ? (
                            <el-button
                              type="primary"
                              plain
                              onClick={() => {
                                this.$emit("HomeAddress",data.row)
                              }}
                            >
                              归属地查询
                            </el-button>
                          ) : (
                            <span class="Address">{data.row.mobile_place}</span>
                          )}
                        </span>
                      </div>
                    );
                  },
                },

                {
                  label: "操作",
                  minWidth: "150",
                  fixed: "right",
                  render: (h, data) => {
                    return (
                      <div>
                        <el-link
                          type="primary"
                          onClick={() => {
                            this.$emit("onClickDetail",data.row)
                          }}
                        >
                          详情
                        </el-link>

                      </div>
                    );
                  },
                },
              ]
            },
            //渲染成交记录(暂未放开)
            RenderingG(){
              this.table_header5 = [
                  // {
                //   prop: "id",
                //   label: "ID",
                //   minWidth: "80px",
                // },

                {
                  label: "客户名称",
                  width: "200px",
                  fixed: "left",
                  // sortable: "custom",
                  render: (h, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.cname ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div style="align-items: center;white-space: nowrap;" class="flex-row">
                                  客户编号：{data.row.id}
                                  <el-button
                                    size="mini"
                                    type="success"
                                    style="margin-left: 20px"
                                    onClick={() => {
                                      this.$emit("copyCusID",data.row.id)
                                    }}
                                  >
                                    复制
                                  </el-button>
                                </div>
                                <div class="flex-row">
                                  客户名称：{data.row.cname}

                                </div>
                                <div class="cus-userName cus-userName_nowrap" slot="reference" onClick={() => {
                                  this.$emit("Quick_Edit",data.row);
                                    }}>
                                  {data.row.cname}
                                </div> 
                                {/* <div class="cus-userName" slot="reference">
                                  {data.row.cname}
                                </div> */}
                              </el-popover>
                            ) : (
                              ""
                            )}
                            <div class="cus-sex">
                              {data.row.sex && data.row.sex == 1 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nan.png"
                                  alt=""
                                />
                              ) : null}
                              {data.row.sex && data.row.sex == 2 ? (
                                <img
                                  src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png"
                                  alt=""
                                />
                              ) : null}
                            </div>
                          </div>
                          <div class="cus-box-foot flex-row">

                            {data.row && data.row.create_id == this.status_id ? (
                              <span class="cus-icon-type">私客</span>
                            ) : null}


                          </div>
                        </div>
                        {data.row.wxqy_id > 0 ? (
                          <img
                            class="cus-img"
                            src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                          />
                        ) : (
                          ""
                        )}
                        <div
                          class="fast-Edit-cus"
                          onClick={() => {
                            this.$emit("fastEditData",data.row)
                          }}
                        >
                          <img
                            src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "手机号",
                  fixed: "left",
                  width: "200px",
                  prop: "mobile",
                  render: (h, data) => {
                    const mobileFilter = function (val) {
                      let reg = /^(.{3}).*(.{3})$/;
                      return val.replace(reg, "$1*****$2");
                    };
                    return (
                      <div class="flex-box table-btns">
                        {data.row.last_call_follow
                          &&
                          data.row.last_call_follow.id
                          ?
                          (<div class="last_call_follow div row">
                            <el-tooltip
                              class="item"
                              style="display:flex;flex-direction:row;justify-content:center"
                              effect="light"
                              placement="top"
                            >
                              <div slot="content" style="max-width:300px">
                                {data.row.last_call_follow.content}
                              </div>
                              <div class="cus-clue-text">
                                {data.row.last_call_follow
                                  &&
                                  data.row.last_call_follow.call_status == 1
                                  ?
                                  (<span class='cus-clue-text_c'>{mobileFilter(data.row.mobile)}</span>)
                                  :
                                  (<span class='cus-clue-text_u'>{mobileFilter(data.row.mobile)}</span>)}
                              </div>
                            </el-tooltip>
                          </div>)
                          :
                          (<span class='cus-clue-text_n'>{mobileFilter(data.row.mobile)}</span>)}
                        <div
                          class="fast-look-tel"
                          onClick={() => {
                            this.$emit("fastLookTel",data.row)
                          }}
                        >
                          <i class="el-icon-phone"></i>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "最新回访记录",
                  prop: "last_follow_info",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user">
                            {data.row.last_follow_info ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >

                                <div class="flex-row">
                                  {data.row.last_follow_info.content}

                                </div>
                                <div class="cus-userName cus-userName1" slot="reference">
                                  {data.row.last_follow_info.content}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  label: "跟客天数",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row && data.row.genjin_day !== '' && data.row.genjin_day >= 0 ? (data.row.genjin_day + '天') : '--'}

                      </div>
                    )
                  }
                },

                {
                  label: "客户等级",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot">
                          {data.row.level ? (
                            <span class="cus-icon-level">
                              {data.row.level.title}级
                            </span>
                          ) : "空"}
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户意向",
                  prop: "",
                  width: "100px",
                  render: (j, data) => {
                    return (
                      <el-tooltip class="item" effect="light" placement="top">
                        <div slot="content" >
                          {data.row.intention_community}
                        </div>
                        <div class="cus-clue-text cus-clue-text_community">
                          <span>{data.row.intention_community || '--'}</span>
                        </div>
                      </el-tooltip>
                    )
                  }
                },
                {
                  label: "客户状态",
                  prop: "tracking",
                  width: "130px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        <div class="cus-box-foot" style="width:80px">
                          <el-popover
                            popper-class="cus-poper-Label"
                            placement="bottom"
                            width="100"
                            trigger="click"
                          >
                            <div class="f-list">
                              {this.status_list.length
                                ? this.status_list.map((item) => {
                                  return (
                                    <div
                                      class="f-item"
                                      onClick={() => {
                                        this.$emit("onClickFollowStatus",data.row,item)
                                      }}
                                    >
                                      {item.title || '--'}
                                    </div>
                                  );
                                })
                                : "--"}
                            </div>
                            {data.row.tracking &&
                              Object.keys(data.row.tracking).length &&
                              data.row.tracking.title == "有效客户" ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-customer"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                有效客户
                              </span>
                            ) : data.row.tracking &&
                              Object.keys(data.row.tracking).length ? (
                              <span
                                slot="reference"
                                id={"popClick" + data.row.id}
                                class="cus-icon-purchase"
                                onClick={() => {
                                  this.$emit("setStatus",data.row)
                                }}
                              >
                                {data.row.tracking.title || '--'}
                              </span>
                            ) : (
                              <span
                                slot="reference"


                              >
                                {"--"}
                              </span>
                            )}
                          </el-popover>
                        </div>
                      </div>
                    )
                  }
                },
                {
                  label: "客户标签",
                  prop: "label",
                  width: "220px",
                  render: (j, data) => {
                    return (

                      <div class="cus-box div row">
                        <div class="cus-box-header flex-box">
                          <div class="flex-row cus-header-user cus-clue-label">
                            {data.row.label && data.row.label.length > 0 ? (
                              <el-popover
                                placement="top-start"
                                width="200"
                                trigger="hover"
                              >
                                <div class="flex-row">
                                  {data.row.label.join(",")}

                                </div>
                                <div class="cus-userName w100 flex-row" slot="reference">
                                  {data.row.label.length > 0
                                    ? data.row.label.slice(0, 2).map((item, index) => {
                                      return (
                                        <div class="flex-row align-center">
                                          <span class="cus-icon-label" key={index}>
                                            {item}
                                          </span>
                                        </div>
                                      );
                                    })
                                    : '--'}
                                </div>
                              </el-popover>
                            ) : (
                              ""
                            )}

                          </div>
                        </div>
                        {data.row.label && data.row.label.length > 0 ? (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        ) : (
                          <div
                            class="clueLabel"
                            onClick={() => {
                              this.$emit("fastEditLabel",data.row)
                            }}
                          >
                            <img
                              src={
                                "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                              }
                              alt=""
                            />
                          </div>
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: this.calculatecreate,
                  prop: "create_user",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.create_user && data.row.create_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.create_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },

                {
                  label: this.weihuuser,
                  prop: "repeat_call_name",
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.follow_user && data.row.follow_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.follow_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },
                {
                  label:this.diakanuser,
                  minWidth: "110px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.take_user && data.row.take_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.take_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    );
                  },
                },

                {
                  label: this.chnegjiaouser,
                  prop: "deal_user",
                  minWidth: "150px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.deal_user && data.row.deal_user.user_name ? (
                          <span style="margin-right: 5px;">
                            {data.row.deal_user.user_name}
                          </span>
                        ) : (
                          "--"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "成交周期",
                  prop: "",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div class="cus-box" style="text-align: left;">
                        {data.row.last_follow_info && data.row.last_follow_info.deal_time_day && data.row.last_follow_info.deal_time_day >= 0 ? (data.row.last_follow_info.deal_time_day + '天') : '--'}
                      </div>
                    )
                  }
                },
                {
                  label: "跟进状态",
                  prop: "public_status",
                  minWidth: "120px",
                  render: (j, data) => {
                    return (
                      <div style="text-align:left">
                        {data.row.public_status == 2 && data.row.public2_status ? (
                          <span class="public-status">
                            {this.$options.filters.publicStatusParse(
                              data.row.public2_status, data.row.go_sea_day
                            )}
                          </span>
                        ) : (
                          "正常"
                        )}
                      </div>
                    )
                  }
                },
                {
                  label: "客户线索",
                  prop: "remark",
                  width: "200px",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div class="flex-box">
                        <el-tooltip class="item" effect="light" placement="top">
                          <div slot="content" style="max-width:300px">
                            {data.row.remark}
                          </div>
                          <div class="cus-clue-text follow-content">
                            <span>{data.row.remark || '--'}</span>
                          </div>
                        </el-tooltip>

                      </div>
                    );
                  },
                },
                {
                  label: "客户类型",
                  prop: "client_type",
                  minWidth: "100px",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        {data.row.client_type ? (
                          <el-tag type="info">{data.row.client_type.title}</el-tag>
                        ) : "--"}
                      </div>
                    )
                  }
                },
                {
                  label: "创建时间",
                  prop: "created_at",
                  minWidth: "200",
                  // sortable: "custom",
                  render: (j, data) => {
                    return (
                      <div style="text-align: left;">
                        <div>
                          <div style="margin-bottom: 2px;">{data.row.created_at}</div>
                        </div>
                      </div>
                    );
                  },
                },
                {
                  prop: "updated_at",
                  label: "更新时间",
                  // sortable: "custom",
                  width: "200",
                  render: (j, data) => {
                    return (
                      <div>
                        {data.row.last_follow_day >= 0 &&
                          data.row.last_follow_day != "" ? (
                          <el-tooltip class="item" effect="light" placement="top">
                            <div slot="content" style="max-width:300px">
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                            <div class="follow-content">
                              <span style="margin-right: 5px;">
                                {this.$options.filters.getPastDay(
                                  data.row.last_follow_day
                                )}
                              </span>
                              {data.row.last_follow && data.row.last_follow.content
                                ? data.row.last_follow.content
                                : null}
                            </div>
                          </el-tooltip>
                        ) : (
                          ""
                        )}
                        <div style="text-align: left;">{data.row.updated_at}</div>
                        <div
                          class="followLabel"
                          onClick={() => {
                            this.$emit("fastFollowUp",data.row)
                          }}
                        >
                          <img
                            src={
                              "https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                            }
                            alt=""
                          />
                        </div>
                      </div>
                    );
                  },
                },
                {
                  minWidth: "190",
                  label: "来源",
                  render: (h, data) => {
                    return (
                      <div>
                        {data.row && data.row.source ? (
                          <div >{data.row.source2 ? data.row.source.title+"-"+data.row.source2.title : data.row.source.title}</div>
                        ) : (
                          ""
                        )}
                      </div>
                    );
                  },
                },
                {
                  label: "归属地",
                  minWidth: "200",
                  prop: "mobile",
                  render: (h, data) => {
                    return (
                      <div class="flex-box table-btns">
                        <span class="search-Belong">
                          {data.row.mobile_place == "" ||
                            data.row.mobile_place == undefined ? (
                            <el-button
                              type="primary"
                              plain
                              onClick={() => {
                                this.$emit("HomeAddress",data.row)
                              }}
                            >
                              归属地查询
                            </el-button>
                          ) : (
                            <span class="Address">{data.row.mobile_place}</span>
                          )}
                        </span>
                      </div>
                    );
                  },
                },

                {
                  label: "操作",
                  minWidth: "150",
                  fixed: "right",
                  render: (h, data) => {
                    return (
                      <div>
                        <el-link
                          type="primary"
                          onClick={() => {
                            this.$emit("onClickDetail",data.row)
                          }}
                        >
                          详情
                        </el-link>

                      </div>
                    );
                  },
                },
              ]
            },
        // 排序
        sortChangeData(column) {
          // console.log(column.column.label,column,"参数");
          // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
          if (column) {
            if (column.column.label === "客户名称") {
              this.params.c_type3 = 1;
            } else if (column.column.label === "更新时间") {
              this.params.c_type3 = 2;
            } else if (column.column.label === "客户线索") {
              this.params.c_type3 = 3;
            } else if (column.column.label === "创建时间") {
              this.params.c_type3 = 4;
            }
            // 判断升序
            if (column.order === "ascending") {
              this.params.c_type3_sort = 2;
            } else if (column.order === "descending") {
              // 判断倒序
              this.params.c_type3_sort = 1;
            } else {
              // 默认
              // this.params.c_type1 = 0;
              delete this.params.c_type3;
              delete this.params.c_type3_sort;
            }
            // this.getDataList();
          }
        },
        selectionChange(e) {
          this.multipleSelectionname = e; // 赋值当前客户信息
          let arr = e.map((item) => {
            return item.id;
          });
          this.multipleSelection = arr; // 赋值当前客户的id
          this.$emit("selectionChange",e)
          // 只有在客户标签列表为空时请求数据
          if (!this.labels_list.length) {
            this.$emit("getLabelGroupNoPageNew",)
          }
        },
        formatTelTime(val) {
          let wholeTime = 0
          let sencond = parseInt(val) || 0
          const hour = sencond / 3600;
          const minute = (sencond % 3600) / 60;
          const sec = ((sencond % 3600) % 60) % 60;
          wholeTime = (hour >= 1 ? (parseInt(hour) + "h") : '') + (hour < 1 && minute < 1 ? '' : (parseInt(minute) + "'")) + (sec + '"')
          return wholeTime
        },
    }
}
</script>
<style scoped lang="scss" >
.follow-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
</style>