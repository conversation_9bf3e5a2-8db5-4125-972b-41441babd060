<template>
  <div>
    <el-button style="margin-bottom:20px" type="primary" size="mini" class="el-icon-plus"
      @click="createCus">添加客户字段</el-button>
    <el-table v-loading="is_table_loading" :data="tableData" border highlight-current-row>
      <el-table-column prop="client_column_id" label="ID"></el-table-column>
      <el-table-column prop="colum_name" label="字段名称">
        <template slot-scope="scope">
          <el-input :disabled="scope.row.is_system == 1 ? true : false" v-model="scope.row.colum_name"
            placeholder="请输入"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="colum_type" label="字段类型">
        <template slot-scope="scope">
          <el-select :disabled="scope.row.is_system == 1" v-model="scope.row.colum_type" placeholder="请选择">
            <el-option v-for="item in field_list_new" :key="item.id" :label="item.name" :value="item.type">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="is_show" label="是否显示">
        <template slot-scope="scope">
          <el-switch :disabled="
                        scope.row.is_system == 1 && scope.row.is_edit == 0 ? true : false
                      " v-model="scope.row.is_show" active-color="#13ce66" inactive-color="#ff4949" :active-value="1"
            :inactive-value="0">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="is_require" label="是否必填">
        <template slot-scope="scope">
          <el-switch :disabled="
                        scope.row.is_system == 1 && scope.row.is_edit == 0 ? true : false
                      " v-model="scope.row.is_require" active-color="#13ce66" inactive-color="#ff4949" :active-value="1"
            :inactive-value="0">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序">
        <template slot-scope="scope">
          <el-input :disabled="
                        scope.row.is_system == 1 && scope.row.is_edit == 0 ? true : false
                      " v-model="scope.row.sort" placeholder="请输入"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="tips" label="提示内容">
        <template slot-scope="scope">
          <el-input :disabled="scope.row.is_system == 1" v-model="scope.row.tips" placeholder="请输入"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间"></el-table-column>
      <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <el-link @click="onClickChange(scope.row)" type="primary">编辑</el-link>
          <el-link v-if="scope.row.is_system === 2" style="margin-left:10px" @click="onClickDelete(scope.row)"
            type="danger">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="text-align:end;margin-top:24px" background layout="prev, pager, next" :total="params.total"
      :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
    </el-pagination>
    <el-dialog :visible.sync="is_colum_dialog" title="编辑">
      <div class="row input-box div " v-for="(item, index) in colum_type_form" :key="index">
        <el-input placeholder="请输入名称" v-model="item.name"></el-input>
        <el-input placeholder="请输入名称值" v-model="item.value"></el-input>
        <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png" alt=""
          @click.prevent="addDomain" />
        <img v-if="index !== 0" @click.prevent="removeDomain(item)"
          src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onClickColum">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
      },
      is_table_loading: true,
      field_list: {},
      field_list_new: [
        {
          id: 1,
          type: "text",
          name: "单行文本",
        },
        {
          id: 2,
          type: "radio",
          name: "单选按钮",
        },
        {
          id: 3,
          type: "checkbox",
          name: "多选按钮",
        },
        {
          id: 4,
          type: "date",
          name: "日期",
        },
        {
          id: 5,
          type: "datetime",
          name: "日期时间",
        },
        {
          id: 6,
          type: "mobile",
          name: "手机号码",
        },
        {
          id: 7,
          type: "select",
          name: "下拉列表",
        },
      ],
      is_colum_dialog: false,
      colum_type_form: [],
      colum_value: "",
      new_form: {},
    };
  },
  created() {
    this.getFieldtype();
    this.getDataList();
  },
  methods: {
    getFieldtype() {
      this.$http.getFieldTypeData().then((res) => {
        if (res.status === 200) {
          this.field_list = res.data;
        }
      });
    },
    fieldType(e) {
      for (let i in this.field_list) {
        if (e === i) {
          return i;
        }
      }
    },
    createCus() {
      this.tableData.push({
        colum_name: "",
        colum_type: "text",
        is_show: 1,
        is_require: 1,
        sort: 0,
        tips: "请输入",
        colum_value: [{ value: "", name: "" }],
      });
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getCrmCustomerSettingData({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
          }
        });
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onClickChange(e) {
      this.new_form = e;
      if (
        e.colum_type === "radio" ||
        e.colum_type === "checkbox" ||
        e.colum_type === "select"
      ) {
        if (e.colum_value.length > 0) {
          // 有参数赋值
          this.colum_type_form = e.colum_value;
        } else {
          this.colum_type_form = [{ value: "", name: "" }];
        }
        this.is_colum_dialog = true;
        return;
      }
      if (e.client_column_id) {
        // 编辑
        this.editData();
      } else {
        // 添加
        this.setData();
      }
    },
    setData() {
      this.new_form.colum_value = this.colum_value || "";
      if (!this.new_form.colum_name) {
        this.$message.error("请输入字段名称");
        return;
      }
      this.$http.setFieldTypeData(this.new_form).then((res) => {
        if (res.status === 200) {
          this.is_colum_dialog = false;
          this.$message.success("设置成功");
          this.getDataList();
        }
      });
    },
    editData() {
      this.new_form.colum_value = this.colum_value || "";
      this.new_form.id = this.new_form.client_column_id;
      if (!this.new_form.colum_name) {
        this.$message.error("请输入字段名称");
        return;
      }
      this.$http.editFieldTypeData(this.new_form).then((res) => {
        if (res.status === 200) {
          this.is_colum_dialog = false;
          this.$message.success("设置成功");
          this.getDataList();
        }
      });
    },
    addDomain() {
      this.colum_type_form.push({
        name: "",
        value: "",
      });
    },
    removeDomain(item) {
      var index = this.colum_type_form.indexOf(item);
      if (index !== -1) {
        this.colum_type_form.splice(index, 1);
      }
    },
    onClickColum() {
      let str = this.colum_type_form.map((item) => {
        return `${item.name}=${item.value}\n`;
      });
      this.colum_value = str.toString().replace(/,/g, "");
      this.new_form.colum_value = this.colum_value;
      if (this.new_form.client_column_id) {
        this.editData();
      } else {
        this.setData();
      }
    },
    onClickDelete(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http.deleteFieldTypeData(row.client_column_id).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
  },
};
</script>

<style scoped lang="scss">
.input-box {
  width: 500px;
  align-items: center;
  margin-bottom: 10px;
}

img {
  width: 32px;
  height: 32px;
  margin-left: 12px;
}
</style>
