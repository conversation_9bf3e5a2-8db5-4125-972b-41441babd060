<template>
<div>
  <el-drawer
      :visible.sync="AIdrawer"
      :direction="direction"
      :before-close="handleClose"
      :modal="modalai">
      <template #title>
        <div class="titleimgaI">
          <img class="imgaI" src="https://img.tfcs.cn/backup/static/admin/customer/AI.png" alt="image"/>智能分析
        </div>
      </template>
      <div class="page">
        <!-- 录音质检 -->
        <div class="audio_player" v-if="Phone.id && Phone.record_url != ''">
          <AudioPlayer
            :activity="Phone"
            :healthdata="AIdata"
            select="CustomerFollow"
          ></AudioPlayer>
        </div>
        <!-- 等级 -->
        <div class="deal_level" 
        :class="{
          'Adeal_level': extractedLetter === 'A',
          'Bdeal_level': extractedLetter === 'B',
          'Cdeal_level': extractedLetter === 'C',
          'Ddeal_level': extractedLetter === 'D',
        }"> 
          <div class="value"
          :class="{
             'Avalue': extractedLetter === 'A',
             'Bvalue': extractedLetter === 'B',
             'Cvalue': extractedLetter === 'C',
             'Dvalue': extractedLetter === 'D',
          }">
            {{ extractedLetter }}
          </div>
          <div class="info info_with">
            <div class="font_color">{{ AIdata.deal_level && AIdata.deal_level.name }} ：</div>
            <div class="infodesc">{{ AIdata.deal_level && AIdata.deal_level.desc }}</div>
          </div>
        </div>
        <!-- 风险分析 -->
        <div class="loss_level"
        :class="{
          'Adeal_level': (AIdata.loss_level && AIdata.loss_level.value) === '高',
          'Bdeal_level': (AIdata.loss_level && AIdata.loss_level.value) === '中',
          'Cdeal_level': (AIdata.loss_level && AIdata.loss_level.value) === '低',
        }">
          <div class="valuelevel"
          :class="{
          'Avalue': (AIdata.loss_level && AIdata.loss_level.value) === '高',
          'Bvalue': (AIdata.loss_level && AIdata.loss_level.value) === '中',
          'Cvalue': (AIdata.loss_level && AIdata.loss_level.value) === '低',
        }"
          >
            {{ AIdata.loss_level && AIdata.loss_level.value }}
          </div>
          <div class="info info_with">
            <div class="font_color">{{ AIdata.loss_level && AIdata.loss_level.name }} ：</div>
            <div class="infodesc">{{ AIdata.loss_level && AIdata.loss_level.desc }}</div>
          </div>
        </div>
        <!-- 通话摘要 -->
        <div>
          <div class="call_summary">
            <div class="circle"></div>
            <div class="font_color" style="margin-left: 10px;">
              {{ AIdata.call && AIdata.call.name }}
            </div>
          </div>
          <div class="infodesc">
            {{ AIdata.call && AIdata.call.value }}
          </div>
        </div>
        <!-- 客户需求等级分析  -->
        <div class="demand_level" v-if="AIdata.demand_level && AIdata.demand_level.desc">
          <div class="font_color flex-row">{{ AIdata.demand_level && AIdata.demand_level.name }} &nbsp;  
            <el-tag :type="AIdata.demand_level.value=='有效'?'success':AIdata.demand_level.value=='无效'?'info':'warning'" size="mini">{{ AIdata.demand_level && AIdata.demand_level.value }}</el-tag></div>
          <div class="infodesc">{{ AIdata.demand_level && AIdata.demand_level.desc }}</div>
        </div>
        <!-- 客户标签 -->
        <div>
          <div class="call_summary">
            <div class="circle"></div>
            <div class="font_color" style="margin-left: 10px;">
              {{ AIdata.label && AIdata.label.name }}
            </div>
          </div>
          <div class="infodesc">
            <el-tag v-for="(label, index) in labelValues" :key="index" size="warning" style="margin-right: 5px;">
              {{ label }}
            </el-tag>
          </div>
        </div>
        <!-- 沟通关键词 -->
        <div>
          <div class="call_summary">
            <div class="circle"></div>
            <div class="font_color" style="margin-left: 10px;">
              {{ AIdata.keywords && AIdata.keywords.name }}
            </div>
          </div>
          <div class="infodesc">
            <el-tag v-for="(keywords, index) in keywordsValues" :key="index" style="margin-right: 5px;">
              {{ keywords }}
            </el-tag>
          </div>
        </div>
        <!-- 用户画像 -->
        <div>
          <div class="call_summary">
            <div class="circle"></div>
            <div class="font_color" style="margin-left: 10px;">
              {{ AIdata.portrait && AIdata.portrait.name }}
            </div>
          </div>
          <div class="infodesc">
            <div v-for="(value, key) in AIdata.portrait && AIdata.portrait.value" :key="key">
              <div class="portrait_value" style="font-size:15px;"><span>{{ key }}:</span> {{ value }}</div>
            </div>
          </div>
        </div>
        <div class="AIword">
          以上内容由AI大模型智能生成
         </div>
      </div>
      <div class="demo-drawer__footer">
        <div class="drawerfooter">
          <span class="AIword"><i class="el-icon-warning"></i>一键采纳更新到客户资料</span>
          <el-button style="margin-left: 10px;"  size="medium"
          type="primary" @click="CoverageInformation" :loading="loading">一键采纳</el-button>
        </div>
      </div>
  </el-drawer>
</div>
</template>

<script>
import AudioPlayer from "@/components/components/audioPlayer.vue"
export default {
  components: {
        AudioPlayer,
    },
    props: {
      modalai: {
        type: Boolean,
        default: () => true,
      },
      waihu:{
        type:Boolean,
        default: () => false,
      }
    },
  data() {
    return {
      AIdrawer: false,
      direction: 'rtl',
      AIdata: {}, // AI分析数据内容
      Phone:{},//通话录音
      loading:false,
    };
  },
  computed: {
    extractedLetter() {
      // 提取 AIdata.deal_level.value 中的字母部分
      return this.AIdata.deal_level && this.AIdata.deal_level.value ? this.AIdata.deal_level.value.match(/[A-Za-z]/)[0] : '';
    },
    labelValues() {
      // 根据逗号分割 AIdata.label.value
      return this.AIdata.label && this.AIdata.label.value ? this.AIdata.label.value.split(',') : [];
    },
    keywordsValues() {
      return this.AIdata.keywords && this.AIdata.keywords.value ? this.AIdata.keywords.value.split('、') : [];
    },
  },
  methods: {
    // 打开侧边栏
    open(data) {
      this.Phone = data
      this.call_record_id = data.call_record_id
      this.getData();
      return this;
    },
    // 关闭侧边栏
    handleClose() {
      if(!this.Phone.is_analysis){
            if(this.waihu){
              this.$emit("getDataListA", {})
            }else{
              this.$emit("getFollowData", {})
              this.$emit("getClientTelephoneRecord", {})
            }
      }
      
      this.AIdrawer = false;
    },
    getData() {
      this.$http.aianalysiscallquality(this.call_record_id).then(res => {
        if (res.status === 200) {
          this.AIdata = res.data;
          // console.log(this.AIdata, "AI分析数据");
          if(this.AIdata.state==1){
            this.AIdrawer = true; 
          }else{
            this.$message.warning(" AI正在分析中，请稍候查看结果！");
          }
        }else {
          this.AIdrawer = false; 
        }
      });
    },
    //一键采纳
    CoverageInformation(){
      this.$confirm('此操作将根据AI助理分析的客户等级、意向标签、客户画像，智能覆盖原有的客户资料, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.coverPhone(this.call_record_id).then(res=>{
            if(res.status==200){
              this.$message.success("采纳成功！")
            }
            if(this.waihu){
              this.$emit("getDataListA", {})
            }else{
              this.$emit("getFollowData", {})
              this.$emit("getClientTelephoneRecord", {})
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });          
        });

    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer {
  .el-drawer__header {
    color: #2E3C4E !important;
    margin-bottom:15px !important;
  }
}
.titleimgaI{
display: flex;
}
.imgaI{
  width: 30px !important;
  margin-top: -3px;
  margin-right: 10px;

}
.page {
  width: 95%;
  margin: 0 auto;
  height: calc(100vh -  140px);
  overflow-y: auto;
  overflow-x: hidden;
}
// 录音质检
.audio_player{
  width: 100%;
margin-bottom: 20px;
}
/* 等级样式 */                                
.deal_level {
  width: 95%;
  display: flex; /* 使用flex布局 */
  border-radius: 8px; /* 圆角 */      
  padding: 10px; /* 添加内边距 */
  .value {
    width: 15%;
    text-align: center;
    margin: auto;
    border-radius: 8px; /* 圆角 */
    margin-right: 20px; /* 值和信息之间的间距 */
    font-size: 50px; // 修改字体大小
    color: white;
    font-weight: bold;
    position: relative;
  }
}
.infodesc {
    margin-top: 10px;
    font-size: 16px;
    color: #8a929f;
    line-height: 30px;
  .portrait_value {
    margin-top: 10px;
    color: rgb(94, 109, 130);
  }
}
//公用样式
.Adeal_level {
  background: linear-gradient(to right, rgba(254, 228, 229, 1), white);
  // border: 1px solid red; /* 边框颜色为红色 A级*/
}
.Bdeal_level {
  background: linear-gradient(to right, #FFE7CF, white);
  // border: 1px solid #FF7D00; /* 边框颜色为橙色 B级 */
}
.Cdeal_level {
  background: linear-gradient(to right, #5cda464d, white);
  // border: 1px solid #0080005c;
}
.Ddeal_level {
  background: linear-gradient(to right, #F2F2F2, #FFFFFF);
  // border: 1px solid #C9CDD4; /* 边框颜色为橙色 B级 */
}
.Avalue{
  background: linear-gradient(to left, #f13131, #f131313d);
}
.Bvalue{
  background: linear-gradient(to left, #FF7D00, #FFB166);
}
.Cvalue{
  background: linear-gradient(to left, #36bc1ef7, #2fed425c);
}
.Dvalue{
  background: #C9CDD4;
}//等级、风险颜色判断
.info_with {
  width: 80%;
}
.font_color {
  color: #303133;
}
.circle {  
  width: 13px;
  height: 13px;
  background-color: #409EFF;
  border-radius: 50%; /* 圆角 */
}
// 风险样式
.loss_level {
  width: 95%;
  display: flex; /* 使用flex布局 */
  // background: linear-gradient(to right, #5cda464d, white);
  // border: 1px solid #0080005c;
  border-radius: 8px; /* 圆角 */
  padding: 10px; /* 添加内边距 */
  margin-top: 10px;
  .valuelevel {
    width: 15%;
    text-align: center;
    margin: auto;
    // background: linear-gradient(to left, #36bc1ef7, #2fed425c);
    border-radius: 8px; /* 圆角 */
    margin-right: 20px; /* 值和信息之间的间距 */
    font-size: 45px; // 修改字体大小
    color: white;
    font-weight: bold;
    position: relative;
  }
}
//通话摘要样式
.call_summary {
  display: flex;
  margin-top: 20px;
  align-items: center;
}
// <!-- 客户需求状态分析  -->
.demand_level {
  background: #f7f8fa;
  // border: 1px solid #4e5969;
  border-radius: 8px; /* 圆角 */
  padding: 10px; /* 添加内边距 */
  margin-top: 20px;
}
.AIword{
    color: #8a929f;
    font-size: 13px;
    margin-top: 20px;
    margin-bottom: 60px;
  }
  .drawerfooter{
    margin-top: 10px;
    text-align:right;
    margin-right: 10px;
  }

</style>