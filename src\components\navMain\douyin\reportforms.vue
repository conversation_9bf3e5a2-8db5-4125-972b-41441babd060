<template>
  <div class="page">
    <div class="flex-row">
      <div class="Header" v-for="item in titledata" :key="item.id" :class="{isheader:titleid==item.id}"
      @click="titleswitch(item)"> {{item.name}}</div>
    </div>
      <div class="pagecontent">
        <div class="taketable">
          <div v-if="titleid==1">
            <dailyreport :AllDepartment="AllDepartment" :show="show"></dailyreport>
          </div>
          <div v-if="titleid==2">
            <monthlyreport :AllDepartment="AllDepartment" :show="show"></monthlyreport>
          </div>
         <div v-if="titleid==3">
          <anchoroperation :member_listNEW="member_listNEW"  :show="show"></anchoroperation>
         </div>
        </div>
      </div>
  </div>
</template>
<script>
import dailyreport from "./reportforms/dailyreport.vue"
import monthlyreport from "./reportforms/monthlyreport.vue"
import anchoroperation from "./reportforms/anchoroperationreport.vue"
export default {
  components:{
      dailyreport,
      monthlyreport,
      anchoroperation
  },
  data() {
      return {
          titledata:[
            {id:1,name:"日报"},
            {id:2,name:"月报"},
            {id:3,name:"主播"}
          ],//标题
          titleid:"1",//标题id
          member_listNEW: [],//成员
          show:0,//导出是否显示
          website_id:"",
          AllDepartment: [], // 全部部门列表
      }
  },
  created(){
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id;
    }
    if(this.$store.state.ismanager){
      this.show =  this.$store.state.ismanager
    }else{
      this.btnexport()
    }
      this.MembersNEW()//获取成员列表
      this.getDepartmentList()//获取部门列表
  },
  mounted(){
  },
  methods:{
      //获取是否是可以显示导出按钮
      btnexport(){
        this.$http.determinecustomeradmin().then((res)=>{
          if(res.status==200){
            // console.log(res.data," //判断是不是客户管理员");
            this.show = res.data.is_manager
            console.log(res.data.is_manager);
          }
        })
      },
      // 获取部门
      getDepartmentList() {
          this.$http.getCrmDepartmentList().then((res) => {
            if (res.status == 200) {
              this.AllDepartment = res.data;
            }
          });
      },
      //标题切换
      titleswitch(item){
        this.titleid = item.id
      },
      // 获取成员的接口（新）
      MembersNEW(){
        this.$http.getDepartmentMemberListNew().then((res)=>{
          if(res.status==200){
              // console.log(res.data);
            this.member_listNEW = res.data
          }
        })
      },
  }
}
</script>
<style lang="scss" scoped>
.page{
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 20px 24px 80px;
  .Header{
    width: 110px;
    height: 40px;
    text-align: center;
    line-height: 42px;
    color: #8a929f;
    cursor: pointer;
  }
  .isheader{
    background-color: #ffffff;
  }
  .pagecontent{
      width: 100%;
      // height: 700px;
      background-color: #ffffff;
      border-radius: 4px;
      overflow: hidden;
      .SearchCondition{
          margin: 20px;
          display: flex;
          justify-content: space-between;
          .block{
            display: flex;
          }
      }
      .head-list{
        display: flex;
      }
      .taketable{
          width: 97%;
          margin: 0 auto;
          margin-bottom: 20px;
          .page_footer {
            position: fixed;
            left: 230px;
            right: 0;
            bottom: 0;
            background: #fff;
            padding: 10px;
            z-index: 1000;
            .head-list{
              margin-left: 13px;
            }
          }
          ::v-deep .el-table thead.is-group th{
            text-align: center;
          }
          .card-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 15px;
            padding: 0px 0px 20px 0px;
          }

          .data-card {
            background-color: #e6f7ff; // 浅蓝色背景
            border-left: 5px solid #409EFF; // 左侧边栏颜色
            padding: 15px;
            border-radius: 5px;
            width: calc(16% - 5px); // 每行显示5个卡片，
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            // transition: transform 0.2s;
          
            // &:hover {
            //   transform: scale(1.02);
            // }
          
            p {
              margin: 5px 0;
            }
          }
      }
  }
::v-deep .el-table .el-table__fixed{
          padding-bottom: 0px !important;
}
::v-deep .el-table__fixed-right{
  padding-bottom: 0px !important;
}
}
</style>