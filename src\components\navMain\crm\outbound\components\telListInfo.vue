<template>
  <div class="table">
    <el-table
      v-loading="telinfo_table_loading"
      :data="telinfo_tableData"
      border
      :header-cell-style="{ background: '#EBF0F7' }"
      highlight-current-row
      :row-style="$TableRowStyle"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column label="ID" width="70" prop="id"></el-table-column>
      <el-table-column label="主叫" prop="caller"> </el-table-column>
      <el-table-column label="被叫" prop="callee"> </el-table-column>
      <el-table-column label="通话时长(秒)" prop="duration"> </el-table-column>

      <el-table-column label="拨打时间" prop="call_time"> </el-table-column>
      <el-table-column label="接通状态" v-slot="{ row }">
        <el-tag :type="row.duration > 0 ? 'success' : 'warning'">{{
          row.duration > 0 ? "已接通" : "未接听"
        }}</el-tag>
      </el-table-column>
      <el-table-column label="操作" v-slot="{ row }">
        <!-- v-slot="{ row }" -->
        <el-popconfirm
          title="确定下载录音吗？"
          v-if="row.duration > 0"
          @onConfirm="download(row)"
        >
          <el-link slot="reference" type="success" class="mr10"
            >下载录音</el-link
          >
        </el-popconfirm>
        <!-- <el-link @click="telListInfo(row)">详情</el-link> -->
      </el-table-column>
    </el-table>
    <el-pagination
      style="text-align: end; margin-top: 24px"
      background
      layout="prev,pager,next"
      :total="telinfoTotal"
      :page-size="telinfo_params.per_page"
      :current-page="telinfo_params.page"
      @current-change="onTelinfoPageChange"
    ></el-pagination>
  </div>
</template>

<script>

export default {
  props: {
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      telinfo_params: {
        page: 1,
        per_page: 10,
      },
      telinfoTotal: 0,
      telinfo_tableData: [],
      telinfo_table_loading: false,
      c_id: ''
    }
  },
  watch: {
    id: {
      handler(val) {
        console.log(val, 11111);
        if (val) {
          this.c_id = val
          this.getList()
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {

    // 获取坐席列表
    getList() {
      this.telinfo_table_loading = true
      this.$http.getCallPhoneList(this.c_id, this.telinfo_params).then(res => {
        if (res.status == 200) {
          this.telinfo_tableData = res.data
          this.telinfoTotal = res.total || 0
        }
        this.telinfo_table_loading = false
      }).catch(() => {
        this.telinfo_table_loading = false
      })
    },
    // 列表页面更新
    onTelinfoPageChange(val) {
      this.telinfo_params.page = val
      this.getList(this.c_id)
    },
    download(row) {
      window.open(row.record_url)
    }

  }
}
</script>

<style lang ="scss" scoped>
::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;
  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }
  .el-dialog__body {
    padding: 0 12px;
  }
}
.mr10 {
  margin-right: 10px;
}
.strong {
  font-weight: 600;
}
.blue {
  color: #2d84fb;
}
.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}

.maKe_phone_call {
  height: 500px;
  text-align: center;
  background: #fff;
  padding: 10px 20px;
  margin-bottom: 10px;
  box-sizing: border-box;
  .maKe_phone_title {
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
  }
  .submit_make_phone {
    margin-top: 65px;
  }
  .avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 50%;
    img {
      width: 100%;
      object-fit: cover;
    }
  }
  .telphone {
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
    margin-top: 16px;
  }
  .area {
    color: #8a929f;
    font-size: 14px;
    margin-top: 16px;
  }
  .waiting {
    margin: 15px 0 20px;
  }
  .link_step {
    border-radius: 13px;
    background: #f1f4fa;
    padding: 10px 16px;
    .link_step_title {
      color: #2e3c4e;
      font-size: 14px;
      margin-bottom: 15px;
    }
    .link_step_con {
      .link_step_left {
        width: 48px;
        .link_step_left_bottom {
          color: #2e3c4e;
          font-size: 12px;
        }
      }
      .join_line {
        margin-top: 5px;
        color: #2d84fb;
      }
    }
  }
  .to_back {
    margin-top: 65px;
    .to_back_img {
      width: 50px;
      height: 50px;
      margin: 0 auto 10px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .to_back_name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
</style>