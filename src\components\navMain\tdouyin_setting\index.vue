<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="授权设置" name="setting">
        <el-form label-width="120px">
          <el-form-item label="授权小程序">
            <div class="flex-row items-center">
              <el-button type="primary" @click="getLink"
                >获取授权链接</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="抖音小程序配置" name="tt_setting">
        <el-tabs v-model="active_name">
          <el-tab-pane
            :label="item.title"
            :name="value"
            v-for="(item, value) in setting"
            :key="value"
          >
            <!-- v-for="(item, value) in setting"
                :key="value" -->
            <div class="info">
              <!-- <el-card shadow="never"> -->
              <!-- <div slot="header" class="clearfix">
                  <span>{{ item.title }}</span>
                </div> -->
              <el-form label-width="120px" size="medium">
                <el-form-item
                  :label="sub.title"
                  v-for="(sub, key) in item.sub"
                  :key="key"
                >
                  <template v-if="sub.type != 'json'">
                    <template v-if="sub.type != 'image'">
                      <span slot="label">
                        {{ sub.title }}
                        <el-tooltip
                          v-if="sub.tips"
                          :content="sub.tips"
                          placement="top-start"
                        >
                          <el-link type="info" :underline="false"
                            ><i class="el-icon-info"></i
                          ></el-link>
                        </el-tooltip>
                      </span>
                      <el-input
                        v-model="douyinIndexParam[sub.name]"
                        style="width: 50%"
                        v-if="sub.type == 'text'"
                        :maxlength="
                          sub.name == 'app_name'
                            ? 10
                            : sub.name == 'share_title'
                            ? 15
                            : 255
                        "
                        show-word-limit
                      ></el-input>
                      <el-input
                        v-model="douyinIndexParam[sub.name]"
                        style="width: 50%"
                        type="textarea"
                        rows="3"
                        v-if="sub.type == 'textarea'"
                        :maxlength="
                          sub.name == 'app_name'
                            ? 10
                            : sub.name == 'share_title'
                            ? 15
                            : 255
                        "
                        show-word-limit
                      ></el-input>
                      <el-radio-group
                        v-model="douyinIndexParam[sub.name]"
                        v-if="sub.type == 'radio'"
                      >
                        <el-radio
                          v-for="rItem in sub.option"
                          :key="rItem.value"
                          :label="rItem.value"
                          >{{ rItem.name }}</el-radio
                        >
                      </el-radio-group>
                      <template v-if="sub.type == 'rich_text'">
                        <UE
                          :value="douyinIndexParam[sub.name]"
                          :config="ueditor.config"
                          @input="inputUe($event, sub.name)"
                          :ids="'ueditor' + sub.name"
                          :ref="'ue' + sub.name"
                          style="width: 1000px"
                        ></UE>
                      </template>
                    </template>
                    <template v-if="sub.type == 'image'">
                      <el-row>
                        <el-col style="width: 200px">
                          <el-upload
                            action="/api/common/file/upload/admin?category=3"
                            list-type="picture-card"
                            :headers="myHeader"
                            :show-file-list="false"
                            :on-preview="handlePictureCardPreview"
                            :on-success="
                              (e) => handleUploadSuccess(e, sub.name)
                            "
                          >
                            <img
                              v-if="douyinIndexParam[sub.name]"
                              :src="
                                douyinIndexParam[sub.name] +
                                '?x-oss-process=style/w_240'
                              "
                              class="up_image"
                            />
                            <i v-else class="el-icon-plus"></i>
                          </el-upload>
                        </el-col>
                        <el-col :span="10" v-if="sub.tips">
                          <div
                            style="
                              background-color: #faecd8;
                              color: #f40;
                              padding: 10px;
                              line-height: 2;
                            "
                          >
                            {{ sub.tips }}
                          </div>
                        </el-col>
                      </el-row>
                    </template>
                  </template>
                  <template v-if="sub.type == 'json'">
                    <template v-if="sub.name == 'nav_setting'">
                      <!-- 菜单设置 -->
                      <el-form label-width="80px" size="mini">
                        <el-form-item label-width="0" label="">
                          <el-radio-group
                            v-model="setting_nav.status"
                            @change="radioChange"
                          >
                            <el-radio
                              v-for="nav_setting in setting_nav.value"
                              :key="nav_setting.value"
                              :label="nav_setting.value"
                              >{{ nav_setting.title }}</el-radio
                            >
                          </el-radio-group>
                        </el-form-item>

                        <el-form-item label-width="0" v-if="setting_nav.status">
                          <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                          <el-form size="mini">
                            <ul>
                              <li
                                style="
                                  display: inline-block;
                                  margin-right: 15px;
                                  margin-bottom: 10px;
                                "
                                v-for="(row, rowi) in setting_nav.value[
                                  setting_nav.status - 1
                                ].children"
                                :key="rowi"
                              >
                                <el-upload
                                  action="/api/common/file/upload/admin?category=3"
                                  list-type="picture-card"
                                  style="margin-bottom: 10px"
                                  :headers="myHeader"
                                  :show-file-list="false"
                                  disabled
                                  :on-preview="handlePictureCardPreview"
                                  :on-success="
                                    (e) => handleUploadSuccess(e, row, 1)
                                  "
                                >
                                  <img
                                    v-if="row.bg_pic"
                                    :src="row.bg_pic"
                                    class="up_image"
                                  />
                                  <i v-else class="el-icon-plus"></i>
                                </el-upload>
                                <el-form-item
                                  label="标题"
                                  label-width="50px"
                                  class="img-input"
                                  :rules="{
                                    required: true,
                                    message: '请填写标题',
                                    trigger: 'blur',
                                  }"
                                >
                                  <el-input
                                    v-model="row.title"
                                    size="mini"
                                    class="w100"
                                  ></el-input>
                                </el-form-item>
                                <el-form-item
                                  label="链接"
                                  label-width="50px"
                                  class="img-input"
                                  :rules="{
                                    required: true,
                                    message: '请填写链接',
                                    trigger: 'blur',
                                  }"
                                >
                                  <el-input
                                    v-model="row.link"
                                    size="mini"
                                    disabled
                                    class="w100"
                                  ></el-input>
                                </el-form-item>
                                <el-form-item
                                  label="数量"
                                  v-if="setting_nav.status == 1"
                                  label-width="50px"
                                  class="img-input"
                                >
                                  <el-radio-group
                                    v-model="row.number_type"
                                    size="mini"
                                    class="radio_small"
                                  >
                                    <!-- <el-radio label="2" style="margin-right: 10px"
                                    >载入数据</el-radio
                                  > -->
                                    <el-radio
                                      style="font-size: 12px"
                                      v-for="r1 in row.number_type_option"
                                      :key="r1.value"
                                      :label="+r1.value"
                                      >{{ r1.name }}</el-radio
                                    >
                                  </el-radio-group>
                                  <div>
                                    <el-input
                                      v-model="row.number"
                                      size="mini"
                                      placeholder="请输入数量"
                                      v-if="row.number_type == 2"
                                      class="w100"
                                    ></el-input>
                                    <el-select
                                      size="mini"
                                      v-else
                                      class="w100"
                                      v-model="row.number_select"
                                    >
                                      <el-option
                                        v-for="r2 in row.number_select_option"
                                        :key="r2.value"
                                        :label="r2.name"
                                        :value="+r2.value"
                                      >
                                      </el-option>
                                    </el-select>
                                    <!-- <el-cascader
                                    v-else-if="row.parameter.type == 2"
                                    class="w100"
                                    v-model="row.parameter.name"
                                    :options="douyinSubnavCountOptions"
                                  ></el-cascader> -->
                                  </div>
                                </el-form-item>
                                <el-form-item
                                  label="状态"
                                  label-width="50px"
                                  class="img-input"
                                >
                                  <el-radio-group v-model="row.status">
                                    <el-radio
                                      :label="1"
                                      style="margin-right: 10px"
                                      >显示</el-radio
                                    >
                                    <el-radio :label="0">隐藏</el-radio>
                                  </el-radio-group>
                                </el-form-item>
                              </li>
                            </ul>
                          </el-form>
                        </el-form-item>
                        <!-- <el-button
                        type="primary"
                        size="mini"
                        v-if="
                          douyinIndexParam.nav.is_show &&
                          douyinIndexParam.nav.setting == 0
                        "
                        style="margin-left: 80px"
                        @click="openDouyinIndexMenu"
                        >去设置</el-button
                      > -->
                      </el-form>
                    </template>
                    <template v-if="sub.name == 'xinxi_setting'">
                      <el-form label-width="80px" size="mini">
                        <el-form-item
                          :label="child.title"
                          v-for="(child, childi) in setting_xinxi.value"
                          :key="childi"
                        >
                          <el-radio-group
                            v-model="child.status"
                            style="margin-right: 15px"
                          >
                            <el-radio
                              v-for="child_xin in child.status_option"
                              :key="child_xin.value"
                              :label="+child_xin.value"
                              >{{ child_xin.name }}</el-radio
                            >
                            <!-- <el-radio :label="0">关闭</el-radio> -->
                          </el-radio-group>
                          <span class="order" style="margin-right: 8px"
                            >排序</span
                          >
                          <span class="order-inp" style="margin-right: 15px">
                            <el-input
                              v-model="child.sort"
                              type="number"
                              min="0"
                              style="width: 60px"
                            >
                            </el-input>
                          </span>

                          <span class="order" style="margin-right: 8px"
                            >自定义</span
                          >
                          <span class="order-inp" style="margin-right: 15px">
                            <el-input
                              v-model="child.title_set"
                              placeholder="自定义名称"
                              style="width: 120px"
                            >
                            </el-input>
                          </span>
                        </el-form-item>
                      </el-form>
                    </template>
                    <template v-if="sub.name == 'member_menu_setting'">
                      <el-form-item
                        label-width="0"
                        v-if="setting_member_menu.status"
                      >
                        <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                        <el-form size="mini">
                          <ul>
                            <li
                              style="
                                display: inline-block;
                                margin-right: 15px;
                                margin-bottom: 10px;
                              "
                              v-for="(row, rowi) in setting_member_menu.value"
                              :key="rowi"
                            >
                              <el-upload
                                action="/api/common/file/upload/admin?category=3"
                                list-type="picture-card"
                                :headers="myHeader"
                                disabled
                                :show-file-list="false"
                                :on-preview="handlePictureCardPreview"
                                :on-success="
                                  (e) => handleUploadSuccess(e, row, 1)
                                "
                              >
                                <img
                                  v-if="row.bg_pic"
                                  :src="row.bg_pic"
                                  class="up_image"
                                />
                                <i v-else class="el-icon-plus"></i>
                              </el-upload>
                              <el-form-item
                                label=""
                                label-width="50px"
                                class="img-input"
                              >
                                {{ row.title }}
                              </el-form-item>
                              <el-form-item
                                label="状态"
                                label-width="50px"
                                class="img-input"
                              >
                                <el-radio-group v-model="row.status">
                                  <el-radio
                                    v-for="r in row.status_option"
                                    :key="r.value"
                                    :label="+r.value"
                                    style="margin-right: 10px"
                                    >{{ r.name }}</el-radio
                                  >
                                  <!-- <el-radio :label="0">隐藏</el-radio> -->
                                </el-radio-group>
                              </el-form-item>
                            </li>
                          </ul>
                        </el-form>
                      </el-form-item>
                    </template>
                    <template v-if="sub.name == 'new_house_menu_setting'">
                      <el-form-item
                        label-width="0"
                        v-if="new_house_menu_setting.status"
                      >
                        <!-- :model="
                            sub.value[douyinIndexParam.nav_setting.status]
                          " -->
                        <el-form size="mini">
                          <ul>
                            <li
                              style="
                                display: inline-block;
                                margin-right: 15px;
                                margin-bottom: 10px;
                              "
                            >
                              <el-form-item
                                :label="row.title"
                                label-width="120px"
                                class="img-input"
                                v-for="(
                                  row, rowi
                                ) in new_house_menu_setting.value"
                                :key="rowi"
                              >
                                <el-radio-group
                                  v-model="row.status"
                                  @change="
                                    ($event) => {
                                      new_house_radio_change($event, row, rowi);
                                    }
                                  "
                                >
                                  <el-radio
                                    v-for="r in row.status_option"
                                    :key="r.value"
                                    :label="+r.value"
                                    style="margin-right: 10px"
                                    >{{ r.name }}</el-radio
                                  >
                                </el-radio-group>
                              </el-form-item>
                            </li>
                          </ul>
                        </el-form>
                      </el-form-item>
                    </template>
                  </template>
                </el-form-item>
              </el-form>
              <!-- </el-card> -->

              <!-- <div v-if="douyinIndexParam.search">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>搜索框</span>
              </div>
              <el-form label-width="120px" size="medium">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.search_status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>

                <template v-if="douyinVersion == 1">
                  <el-form-item
                    label="风格"
                    v-if="douyinIndexParam.search.is_show"
                  >
                    <el-radio-group v-model="douyinIndexParam.search_style">
                      <el-radio label="1">圆角</el-radio>
                      <el-radio label="2">矩形</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </template>
                
              </el-form>
            </el-card>
          </div>

          <div>
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>顶部图片设置</span>
              </div>
              <el-form label-width="80px" size="medium">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.lunbo_status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <template v-if="douyinIndexParam.lunbo_status">
                  <template>
                    <el-form-item label="">
                      <el-row>
                        <el-col style="width: 200px">
                          <el-upload
                            action="/api/common/file/upload/admin?category=3"
                            list-type="picture-card"
                            :show-file-list="false"
                            :on-preview="handlePictureCardPreview"
                            :before-upload="beforeUpload"
                            :on-success="
                              (e) =>
                                handleUploadSuccess(
                                  e,
                                  douyinIndexParam.lunbo_pic
                                )
                            "
                          >
                            <img
                              v-if="douyinIndexParam.lunbo_pic"
                              :src="
                                douyinIndexParam.lunbo_pic +
                                '?x-oss-process=style/w_240'
                              "
                              class="up_image"
                            />
                            <i v-else class="el-icon-plus"></i>
                          </el-upload>
                        </el-col>
                        <el-col :span="10">
                          <div
                            style="
                              background-color: #faecd8;
                              color: #f40;
                              padding: 10px;
                              line-height: 2;
                            "
                          >
                            禁止出现微信及二维码、禁止出现电话号码、禁止出现广告内容，只能为自有品牌形象，不需要可关闭。推荐尺寸860*650像素。
                          </div>
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </template>
                </template>
              </el-form>
            </el-card>
          </div>

          <div v-if="douyinIndexParam.nav">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>菜单设置</span>
              </div>
              <el-form label-width="80px" size="mini">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.nav_status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item v-if="douyinIndexParam.nav_status == 1">
                  <el-form :model="douyinIndexParam.nav_setting" size="mini">
                    <ul>
                      <li
                        style="display: inline-block; margin-right: 15px"
                        v-for="(row, i) in douyinIndexParam.nav_setting"
                        :key="i"
                      >
                        <el-upload
                          action="{:url('Upload/uploadBuild')}"
                          list-type="picture-card"
                          :show-file-list="false"
                          :on-preview="handlePictureCardPreview"
                          :before-upload="beforeUpload"
                          :on-success="
                            (e) => handleUploadSuccess(e, row, 'icon')
                          "
                        >
                          <img
                            v-if="row.bg_pic"
                            :src="row.bg_pic"
                            class="up_image"
                          />
                          <i v-else class="el-icon-plus"></i>
                        </el-upload>
                        <el-form-item
                          label="标题"
                          label-width="50px"
                          class="img-input"
                          :prop="'[' + i + '].title'"
                          :rules="{
                            required: true,
                            message: '请填写标题',
                            trigger: 'blur',
                          }"
                        >
                          <el-input
                            v-model="row.title"
                            size="mini"
                            class="w100"
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="链接"
                          label-width="50px"
                          class="img-input"
                          :prop="'[' + i + '].path'"
                          :rules="{
                            required: true,
                            message: '请填写链接',
                            trigger: 'blur',
                          }"
                        >
                          <el-input
                            v-model="row.link"
                            size="mini"
                            class="w100"
                          ></el-input>
                        </el-form-item>
                        <el-form-item
                          label="数量"
                          label-width="50px"
                          class="img-input"
                        >
                          <el-radio-group v-model="row.parameter.type">
                            <el-radio label="2" style="margin-right: 10px"
                              >载入数据</el-radio
                            >
                            <el-radio label="1">自定义</el-radio>
                          </el-radio-group>
                          <div>
                            <el-input
                              v-model="row.number_select"
                              v-if="row.parameter.type == 1"
                              size="mini"
                              class="w100"
                            ></el-input>
                            <el-cascader
                              v-else-if="row.parameter.type == 2"
                              class="w100"
                              v-model="row.parameter.name"
                              :options="douyinSubnavCountOptions"
                            ></el-cascader>
                          </div>
                        </el-form-item>
                        <el-form-item
                          label="状态"
                          label-width="50px"
                          class="img-input"
                        >
                          <el-radio-group v-model="row.is_show">
                            <el-radio :label="1" style="margin-right: 10px"
                              >显示</el-radio
                            >
                            <el-radio :label="0">隐藏</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </li>
                    </ul>
                  </el-form>
                </el-form-item>
                <el-button
                  type="primary"
                  size="mini"
                  v-if="
                    douyinIndexParam.nav.is_show &&
                    douyinIndexParam.nav.setting == 0
                  "
                  style="margin-left: 80px"
                  @click="openDouyinIndexMenu"
                  >去设置</el-button
                >
              </el-form>
            </el-card>
          </div>

          <div v-if="douyinIndexParam.gundongzixun && douyinVersion == 2">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>资讯设置</span>
              </div>
              <el-form label-width="120" size="medium">
                <el-form-item label="是否开启">
                  <el-radio-group
                    v-model="douyinIndexParam.gundongzixun.is_show"
                  >
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
          <div v-if="douyinIndexParam.xinxilan">
            <el-card shadow="never">
              <div slot="header" class="clearfix">
                <span>信息设置</span>
              </div>
              <el-form label-width="80px" size="mini">
                <el-form-item label="是否开启">
                  <el-radio-group v-model="douyinIndexParam.xinxilan.is_show">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  :label="child.title"
                  v-for="(child, i) in douyinIndexParam.xinxilan.children"
                  :key="i"
                >
                  <el-radio-group v-model="child.is_show">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                  <span class="order">排序</span>
                  <span class="order-inp">
                    <el-input v-model="child.sort" type="number"> </el-input>
                  </span>

                  <span class="order">自定义</span>
                  <span class="order-inp">
                    <el-input v-model="child.value" placeholder="自定义名称">
                    </el-input>
                  </span>
                </el-form-item>
              </el-form>
            </el-card>

            <el-card shadow="never" v-if="douyinIndexParam.member_menu">
              <div slot="header" class="clearfix">
                <span>会员菜单</span>
              </div>
              <el-form
                :model="douyinIndexParam.member_menu.children"
                size="mini"
              >
                <ul>
                  <li
                    style="
                      display: inline-block;
                      margin-right: 15px;
                      text-align: center;
                    "
                    v-for="(row, i) in douyinIndexParam.member_menu.children"
                    :key="i"
                  >
                    <el-form-item label="" label-width="50px" class="img-input">
                      <img :src="row.icon" width="65" class="up_image" />
                    </el-form-item>
                    <el-form-item label="" label-width="50px" class="img-input">
                      {{ row.title }}
                    </el-form-item>
                    <el-form-item label="" label-width="50px" class="img-input">
                      <el-radio-group v-model="row.is_show">
                        <el-radio
                          :label="1"
                          style="margin-right: 10px"
                          @change="onMemberMenuChange(row)"
                          >显示</el-radio
                        >
                        <el-radio :label="0">隐藏</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </li>
                </ul>
              </el-form>
            </el-card>
          </div> -->
            </div>
            <div class="btns">
              <!-- <el-button type="primary" @click="submitDouyinIndexData"
            >提交修改
          </el-button> -->
              <div class="submit" @click="submitDouyinIndexData">提交修改</div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      :visible.sync="show_dialog_list"
      width="300px"
      title="授权小程序"
    >
      <iframe
        style="border: none"
        width="300px"
        height="300px"
        v-bind:src="inframe"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
import config from "@/utils/config"
import UE from "@/components/ueditor"
export default {
  components: { UE },
  data() {
    return {
      link: "",
      show_dialog_list: false,
      inframe: "",
      website_id: "",
      activeName: "setting",
      douyinIndexParam: {
        app_name: "",
        nav_setting: {
          selected: 0
        }
      },
      setting: {

      },
      active_name: "global",
      setting_member_menu: {
        value: [],
        status: 1,
      },
      setting_xinxi: {
        value: [],
        status: 1,
      },
      setting_nav: {
        value: [],
        status: 1,
      },
      new_house_menu_setting: {},
      ueditor: {
        // agreement: {
        //   initialFrameWidth: "100%",
        //   autoHeightEnabled: false,
        //   initialFrameHeight: 500, // 高度
        // },
        // aprivacy: {
        //   initialFrameWidth: "100%",
        //   autoHeightEnabled: false,
        //   initialFrameHeight: 500, // 高度
        // }
        // ,
        config: {
          initialFrameWidth: "100%",
          autoHeightEnabled: false,
          initialFrameHeight: 500, // 高度
        }
      }
    }
  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
    configagreement() {
      return {

      }
    }
    ,
    configaprivacy() {
      return {
        initialFrameWidth: "100%",
        autoHeightEnabled: false,
        initialFrameHeight: 500, // 高度
      }
    }
    ,
    configperson_protection() {
      return {
        initialFrameWidth: "100%",
        autoHeightEnabled: false,
        initialFrameHeight: 500, // 高度
      }
    }
  },
  mounted() {
    let from = this.$route.query.from
    let website_id = this.$route.query.website_id || localStorage.getItem("website_id")
    console.log(from);
    this.website_id = website_id
    this.getSetting()
  },
  methods: {
    getSetting() {
      this.$http.getTtSetting().then(res => {
        console.log(res);
        if (res.status == 200) {
          this.setting_nav = res.data.nav.sub.nav_setting
          this.setting_nav.value.map((item) => {
            if (item.selected == 1) {
              this.setting_nav.status = item.value
            }
          })
          this.setting_xinxi = res.data.xinxi?.sub.xinxi_setting || {}
          this.setting_member_menu = res.data.member_menu?.sub.member_menu_setting || {}
          // this.new_house_menu_setting = res.data.new_house_menu.sub.new_house_menu_setting
          this.setting = res.data
          this.setParams(this.setting)
        }
      })
    },
    setParams(setting) {
      for (const key in setting) {
        if (setting[key].sub) {
          // console.log(this.setting(key).sub);
          for (const j in setting[key].sub) {
            if (typeof (setting[key].sub[j]["value"]) == "number") {
              setting[key].sub[j]["value"] = setting[key].sub[j]["value"] + ''
            }
            this.$set(this.douyinIndexParam, setting[key].sub[j]["name"], setting[key].sub[j]["value"])
          }
        }
      }
    },
    getLink() {
      let href = window.location.href
      href += '****from****tDouyinShouquan'
      href = href.replace("#", "@@@@")
      this.$http.getTDouyinLink(encodeURIComponent(href)).then((res) => {
        const url = res.data
        window.open(url)
        // this.show_dialog_list = true
        // this.inframe = url
      })
    },
    handleClick() {

    },
    submitDouyinIndexData() {
      let params = Object.assign({}, this.douyinIndexParam)
      params.nav_setting = Object.assign([], this.setting_nav.value)
      params.xinxi_setting = Object.assign([], this.setting_xinxi.value)
      params.member_menu_setting = Object.assign([], this.setting_member_menu.value)
      // params.new_house_menu_setting = Object.assign([], this.new_house_menu_setting.value)
      if (this.setting_nav.status == 1) {
        params.nav_setting[this.setting_nav.status - 1].selected = 1
        params.nav_setting[this.setting_nav.status].selected = 0
      }
      if (this.setting_nav.status == 2) {
        params.nav_setting[this.setting_nav.status - 1].selected = 1
        params.nav_setting[0].selected = 0
      }
      this.$http.saveTtSetting(params).then(res => {
        if (res.status == 200) {
          this.$message.success('保存成功')
        }

      })

    },
    handlePictureCardPreview(e) {
      console.log(e);
    },
    beforeUpload() {

    },
    handleUploadSuccess(e, type, cate) {
      console.log(e, type, cate);
      if (cate) {
        console.log(cate);
        this.$set(type, "bg_pic", e.url)
      } else {
        this.douyinIndexParam[type] = e.url
      }


    },
    inputUe(e, type) {
      this.douyinIndexParam[type] = e.content;
    },
    radioChange() {
      this.$forceUpdate()
    },
    new_house_radio_change(e, type) {
      console.log(e, type);
      let other = {}
      if (type.name == 'zxzx') {
        other = this.new_house_menu_setting.value.find(item => item.name == 'yykf')
      }
      if (type.name == 'yykf') {
        other = this.new_house_menu_setting.value.find(item => item.name == 'zxzx')
      }
      if (type.name == "zxzx" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个")
        type.status = 0
        this.$forceUpdate()
      }
      if (type.name == "yykf" && e == 1 && other && other.status == 1) {
        this.$message.warning("预约看房和在线咨询只能选择一个")
        type.status = 0
        this.$forceUpdate()
      }
    }

  }
}
</script>

<style lang ="scss" scoped>
.el-upload {
  overflow: hidden;
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.img-input {
  white-space: nowrap;
}
.w100 {
  width: 100px;
}
.radio_small {
  ::v-deep .el-radio__label {
    font-size: 12px;
  }
}
.btns {
  text-align: center;
  position: fixed;
  top: 500px;
  right: 80px;
  z-index: 1000;
  .submit {
    padding: 10px 40px;
    background: #ff5b6a;
    color: #fff;
    cursor: pointer;
    border-radius: 40px;
  }
}
</style>