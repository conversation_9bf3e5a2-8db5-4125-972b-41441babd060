<template>
    <div>
      <!-- 流转表格 -->
      <el-dialog :visible.sync="wanderabout" width="80%" title="任务列表" append-to-body top="10vh">
          <div class="configuration" >
              <wanderabouttable ref="callwanderabout" v-if="wanderabout"></wanderabouttable>
          </div>
          <!-- <template #footer>
              <el-button type="text" @click="wanderabout = false">取消</el-button>
              <el-button type="primary" @click="wanderabout = false">确定</el-button>
          </template> -->
      </el-dialog>
      <!-- 批量加入流转任务 -->
        <div class="page">
          <add ref="add" v-bind="$attrs" v-on="$listeners" v-if="dialogs.add" @closed="handleClose">
            <!-- <template #footer>
              <div class="pagetable">
                <el-button type="primary" size="medium" @click="Transfertask">流转任务列表</el-button>
              </div>
            </template> -->
          </add>
        </div>
    </div>
</template>
<script>
import wanderabouttable from "@/components/navMain/crm/components/wanderabouttable.vue"
import add from '@/views/crm/customer/components/add_trans_task.vue';
import { Alert } from 'element-ui';
export default {
  components: {
    wanderabouttable,
    add
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
  },
    data() {
        return {
            wanderabout:false,//流转列表
            dialogs: {
              add: false
            }
        }
    },
    computed: {
    
    },
    created(){
    
    },
    watch:{
      show: {
        async handler(val) {
          this.dialogs.add = val;
          if(val){
            await this.$nextTick();
            this.$refs.add.open();
          }
        }
      }
    },
    methods:{
      // 打开流转任务列表
      Transfertask(){
        this.wanderabout = true
      },
      handleClose(){
        this.$emit('update:show', false)
      }
    }
}
</script>
<style lang="scss" scoped>
.pagetable{
  display: flex;
  justify-content:space-between;
  padding: 10px 20px;
  border-top: 1px solid #e9e9e9;
}
</style>