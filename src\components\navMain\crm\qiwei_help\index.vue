<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div
          class="content-box-crm"
          style="padding-bottom: 0; padding-top: 15px"
        >
          <div class="div row align-center">
            <div class="title flex-1">企微助手</div>
          </div>
        </div>
        <div class="tabs flex-row align-center">
          <div
            class="tab"
            :class="{ active: activeTab == 1 }"
            @click="clickTab(1)"
          >
            企业信息
          </div>
          <div
            class="tab"
            :class="{ active: activeTab == 2 }"
            @click="clickTab(2)"
          >
            消息存档
          </div>
          <div
            class="tab"
            :class="{ active: activeTab == 3 }"
            @click="clickTab(3)"
          >
            客户意向标签
          </div>
          <div
            class="tab"
            :class="{ active: activeTab == 4 }"
            @click="clickTab(4)"
          >
            授权管理
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="tips">
            <p>
              1.违规提醒、超时未回复提醒是仅针对开启了消息存档的员工与企微客户/外部群聊操作设置
            </p>
            <p>2.修改设置后立马生效，历史数据不变</p>
            <p>
              3.开启消息回复监控立马生效，历史数据不变；关闭消息回复监控立马生效，历史数据不再监控
            </p>
          </div>
        </div>
        <div
          class="content-box-crm"
          style="margin-bottom: 24px; position: relative"
        >
          <div class="bianji">
            <el-button size="mini" type="primary">编辑</el-button>
          </div>
          <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="违规设置提醒" name="first"
              >违规设置提醒</el-tab-pane
            >
            <el-tab-pane label="敏感词提醒" name="second"
              >敏感词提醒</el-tab-pane
            >
            <el-tab-pane label="超时未回复提醒设置" name="third">
              <div class="jiankong">
                <el-switch
                  v-model="params.jiankong"
                  active-text="消息回复监控开启"
                >
                </el-switch>
              </div>
              <div class="tixing_title">消息恢复监控范围及时间</div>
              <div class="form_item border flex-row align-center">
                <div class="label">选择员工：</div>
                <div class="value">张三，李四</div>
              </div>
              <div class="form_item border flex-row align-center">
                <div class="label">选择时间：</div>
                <div class="value">张三，李四</div>
                <div class="btn">添加</div>
              </div>
              <div class="form_item border flex-row align-center">
                <div class="label"></div>
                <div class="value">张三，李四</div>
                <div class="btn">添加</div>
              </div>
              <div class="tixing_tip">
                设置后，将监控这些时间内，员工恢复企微客户的时长
              </div>

              <div class="tixing_title">超时规划</div>
              <div class="form_item border flex-row align-center">
                <div class="label">回复超时规定：</div>
                <div class="value flex-row align-center">
                  <div>超过</div>
                  <el-input style="width: 50px" v-model="hour"></el-input>
                  <div>小时</div>
                  <el-input style="width: 50px" v-model="minute"></el-input>
                  <div>分 认定超时回复，最少为1分钟</div>
                </div>
              </div>
              <div class="form_item border flex-row align-center">
                <div class="label">回复超时提醒：</div>
                <div class="value flex-row align-center">
                  <el-radio
                    v-model="remind"
                    v-for="item in remind_list"
                    :key="item.id"
                    :label="item.id"
                    >{{ item.name }}</el-radio
                  >
                </div>
              </div>
              <div class="form_item flex-row align-center" v-if="remind == 3">
                <div class="label">选择指定员工：</div>
                <div class="value flex-row align-center">张三</div>
              </div>
              <div class="form_item form_tip flex-row align-center">
                <div class="label">回复超时规定：</div>
                <div class="value flex-row align-center">
                  当员工回复客户消息超过规定时间（超过时间最少为1分钟）后，认定该条消息回复及时，并生成一条消息回复超时记录。
                </div>
              </div>
              <div class="form_item form_tip flex-row align-center">
                <div class="label">回复超时提醒：</div>
                <div class="value flex-row align-center">
                  1.提醒当前员工，当消息回复超时，推送提醒给当前超时的员工/群聊的群主及其上级；
                </div>
              </div>
              <div class="form_item form_tip flex-row align-center">
                <div class="label"></div>
                <div class="value flex-row align-center">
                  2.提醒当前员工及其主管，当消息回复超时，推送提醒给当前超时的员工/群聊的群主及其上级；
                </div>
              </div>
              <div class="form_item form_tip flex-row align-center">
                <div class="label"></div>
                <div class="value flex-row align-center">
                  3.提醒当前/群内开启消息存档员工，当消息回复超时，推送提醒给当前超时的员工/群聊内开启了消息存档的员工
                </div>
              </div>
              <div class="form_item form_tip flex-row align-center">
                <div class="label"></div>
                <div class="value flex-row align-center">
                  4.提醒指定员工，当员工发生消息回复超时，推送提醒给当前超时的员工（有查看消息回复权限的员工）
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="正常结束语设置" name="fourth"
              >正常结束语设置</el-tab-pane
            >
            <!-- <el-tab-pane> -->

            <!-- </el-tab-pane> -->
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "crm_qiwei_help",
  data() {
    return {
      activeTab: 1,
      activeName: "third",
      params: {
        jiankong: true,
      },
      hour: "",
      minute: "",
      remind: 1,
      remind_list: [
        {
          id: 1,
          name: "提醒当前员工",
        },
        {
          id: 2,
          name: "提醒当前员工及主管",
        },
        {
          id: 3,
          name: "自定义员工",
        },
      ],
    };
  },
  methods: {
    clickTab(type) {
      this.activeTab = type;
    },
    handleClick() {},
  },
};
</script>

<style lang="scss" scoped>
.padd0 {
  padding: 0;
}
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.tabs {
  padding: 24px 0;
  background: #f1f4fa 100%;
  .tab {
    padding: 5px 15px;
    color: #8a929f;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 18px;
    cursor: pointer;
    font-weight: 600;
    &.active {
      color: #2d84fb;
      position: relative;
      &::after {
        content: "";
        position: absolute;
        left: 20%;
        bottom: 0;
        height: 3px;
        width: 60%;
        margin: 0 auto;
        background: #2d84fb;
      }
    }
  }
}
.content-box-crm {
  .tips {
    padding: 20px 24px;
    background: #e7f3fd;
    color: #8a929f;
    font-size: 14px;
    line-height: 20px;
  }
  .bianji {
    position: absolute;
    right: 25px;
    top: 30px;
  }
  .title {
    padding-bottom: 15px;
  }
  .jiankong {
    padding: 18px 0;
  }
  .tixing_title {
    padding: 15px 24px;
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: regular;
    font-size: 14px;
    background: #efefef;
  }
  .form_item {
    padding: 10px 24px;
    &.border {
      border-bottom: 1px solid #dde1e9;
      border-left: 1px solid #dde1e9;
      border-right: 1px solid #dde1e9;
    }
    &.form_tip {
      .label {
        color: #8a929f;
        font-size: 14px;
        min-width: 100px;
        margin-right: 0;
      }
      .value {
        color: #8a929f;
        font-size: 14px;
      }
    }
    .label {
      min-width: 80px;
      margin-right: 10px;
    }
    .value {
    }
    .btn {
      color: #2d84fb;
      font-size: 14px;
      margin-left: 14px;
    }
  }
  .tixing_tip {
    color: #8a929f;
    font-size: 14px;
    padding: 10px 20px 20px;
  }
}
</style>
