<template>
  <div></div>
</template>

<script>
export default {
  data() {
    return {
      params: {
        redirect_uri: "",
      },
    };
  },
  mounted() {
    // type=1工作台登录 ，type=2侧边栏登录
    var code = this.getUrlKey("code");
    let website_id = localStorage.getItem("website_id");
    if (code) {
      if (this.isMobile()) {
        let url =
          window.location.origin +
          `/fenxiao/admin/wx_work_auth?website_id=${website_id}&type=1&code=` +
          code;
        window.location.href = url;
      } else {
        this.setWxworkBycodeV2(code);
      }
    } else {
      this.getWxworkByCodeV2();
    }
  },
  methods: {
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
    // 获取路径中的参数（code等）
    getUrlKey(name) {
      return (
        decodeURIComponent(
          (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
            location.href
          ) || ["", ""])[1].replace(/\+/g, "%20")
        ) || null
      );
    },
    getWxworkByCodeV2() {
      this.params.redirect_uri = window.location.href;
      this.$http.getWxworkByCodeV2({ params: this.params }).then((res) => {
        if (res.status === 200) {
          window.location.href = res.data;
        }
      });
    },
    setWxworkBycodeV2(code) {
      // 第三方应用
      this.$http.setWxworkBycodeV2(code).then((res) => {
        if (res.status === 200) {
          localStorage.setItem("website_id", res.data.website_id);
          localStorage.setItem("auth_way", res.data.auth_way || 0);
          localStorage.setItem("TOKEN", res.data.token);
          localStorage.setItem(
            "admin_token_" + res.data.website_id,
            res.data.token
          );
          let url = `https://yun.tfcs.cn/admin/#/index?website_id=${res.data.website_id}`;
          window.location.href = url;
        }
      });
    },
  },
};
</script>

<style></style>
