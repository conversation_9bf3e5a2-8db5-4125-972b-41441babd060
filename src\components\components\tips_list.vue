<template>
  <div class="tips">
    <div
      class="title"
      :style="{
        color: titleColor,
        background: titleBackground,
      }"
    >
      {{ tipsTitle }}
    </div>
    <div
      class="title-ctn"
      :style="{
        background: ctnBackground,
      }"
    >
      <p
        :style="{
          color: ctnColor,
        }"
        v-for="(item, index) in tips_content"
        :key="index"
      >
        <i>*</i>{{ item }}
      </p>
      <slot name="tips"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tipsTitle: {
      type: String,
      default: "提示",
    },
    titleColor: {
      type: String,
      default: "#2589ff",
    },
    titleBackground: {
      type: String,
      default: "#2589ff14",
    },
    tips_content: [Array],
    ctnBackground: {
      type: String,
      default: "#edfbf8",
    },
    ctnColor: {
      type: String,
      default: "#748a8f",
    },
  },
};
</script>

<style scoped lang="scss">
.tips {
  width: 100%;
  .title {
    padding-left: 5px;
    text-align: left;
    color: #2589ff;
    background: #2589ff14;
  }
  .title-ctn {
    padding: 10px;
    color: #fff;
    background: #edfbf8;
    p {
      color: #748a8f;
      margin: 4px 0;
    }
    i {
      color: red;
    }
  }
}
</style>
