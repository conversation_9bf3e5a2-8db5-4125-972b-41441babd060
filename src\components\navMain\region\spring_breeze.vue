<template>
  <div class="header_catalogue">
    <div class="newly_increased">
      <div class="search_for">
        <div class="search_input">
          <div style="width: 240px">
            <el-input
              prefix-icon="el-icon-search"
              placeholder="请在这里输入"
              v-model="input3"
              class="input-with-select"
            >
            </el-input>
          </div>
          <div class="search_btn">
            <el-button type="primary">搜索</el-button>
          </div>
        </div>
        <div>
          <el-button type="warning">新增拓客地图</el-button>
        </div>
      </div>
      <div class="List_Mode">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          :header-cell-style="{
            background: '#DDE1E9',
            color: '#2E3C4E',
          }"
        >
          <el-table-column prop="id" label="ID" align="center">
          </el-table-column>
          <el-table-column prop="Area_Name" label="活动名称" align="center">
          </el-table-column>
          <el-table-column prop="date" label="参与人数" align="center">
          </el-table-column>
          <el-table-column prop="name" label="红包数" align="center">
          </el-table-column>
          <el-table-column prop="num" label="报备数" align="center">
          </el-table-column>
          <el-table-column prop="address" label="操作" align="center">
            <el-button plain>编辑</el-button>
            <el-button plain>详情</el-button>
          </el-table-column>
        </el-table>
        <div class="paging">
          <el-pagination background layout="pager" :total="50"> </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      input3: "",
      tableData: [{
        id: "01",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }, {
        id: "02",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }, {
        id: "03",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }, {
        id: "04",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }, {
        id: "05",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }, {
        id: "06",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }, {
        id: "07",
        Area_Name: "春风里区域拓客",
        name: "3564人",
        date: '3564个',
        num: '3487人',
      }],
    }
  },
}
</script>

<style scoped lang="scss" >
.header_catalogue {
  background: #f1f4fa;
  margin: -14px;
  padding: 24px;

  .newly_increased {
    width: 96%;
    height: 750px;
    background-color: #fff;
    margin: 10px auto;
    overflow: hidden;

    .search_for {
      width: 96%;
      height: 35px;
      // background-color: coral;
      margin: 20px auto;
      display: flex;
      justify-content: space-between;

      .search_input {
        display: flex;
      }

      /deep/.el-input__inner {
        height: 32px;
      }

      /deep/.el-input__icon {
        line-height: 33px;
      }

      .search_btn {
        .el-button {
          width: 76px;
          height: 36px;
          margin-left: 10px;
          line-height: 0;
        }
      }
    }

    .List_Mode {
      width: 96%;
      height: 500px;
      margin: 30px auto;

      .el-button {
        border-color: #2d84fb;
        color: #2d84fb;
      }

      .paging {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }
    }
  }
}
</style>