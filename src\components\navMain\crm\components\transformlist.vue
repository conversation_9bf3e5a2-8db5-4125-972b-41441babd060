<template>
    <div>
        <div v-if="is_house_show">
            <div class="table-top-box div row">
                <div class="t-t-b-left div row flex-1">
                    <el-cascader placeholder="请选择部门" style="width: 210px; margin-right: 16px" v-model="params.department_id"
                        clearable :options="department_list" :props="{
                            value: 'id',
                            label: 'name',
                            children: 'subs',
                            emitPath: false,
                        }" @change="onPageChange1(params.department_id)"></el-cascader>
                    <el-input style="width: 200px" placeholder="请输入姓名" v-model="params.user_name"></el-input>
                    <el-button style="margin: 0 16px" type="primary" class="el-icon-search"
                        @click="onPageChange2">搜索</el-button>
                    <!-- <el-date-picker v-if="website_id != 176" style="width: 210px; margin-right: 16px" v-model="p_time"
                        type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss" @change="changeTimeRange">
                    </el-date-picker> -->
                    <el-button type="primary" v-if="false"> 数据导出</el-button>
                </div>
                <div class="t-t-b-right div row f-c">
                    <span class="f f1"></span>第一 <span class="f f2"></span>第二
                    <span class="f f3"></span>第三
                </div>
            </div>
            <!-- 
          自定义排序
         el-table  @sort-change="onSortChange"
         el-table-column sortable='custom'
         -->
            <div style="margin-top: 20px;">
                <el-table v-loading="is_table_loading" @sort-change="onSortChange" :data="house_list.data" border
                    :header-cell-style="{ background: '#EBF0F7' }" :cell-style="cellStyle" :row-style="$TableRowStyle">
                    <el-table-column label="姓名" v-slot="{ row }" width="200px" fixed="left">
                        <div class="name-box-cy div row">
                            <div class="left">{{ row.user_name[0] }}</div>
                            <div class="right">
                                <div class="c">{{ row.user_name }}</div>
                                <div class="b">{{ row.department }}</div>
                            </div>
                        </div>
                    </el-table-column>
                    <el-table-column align="center" sortable="custom" label="客户数量" prop="khzl_num">
                    </el-table-column>
                    <el-table-column align="center" sortable="custom" label="首跟平均响应时长" prop="sgsc_num2"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="首看次数" prop="skcs_num"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="复看次数" prop="fkcs_num"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="带看总量" prop="dkzl_num"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="带看率" prop="dklv_num"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="复看率" prop="fklv_num"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="跟进率" prop="gjlv_num"></el-table-column>
                    <el-table-column align="center" sortable="custom" label="成交率" prop="cjlv_num"></el-table-column>
                </el-table>
            </div>
            <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
                :total="house_list.total" :page-size="house_list. per_page" :current-page="house_list.page"
                @current-change="onPageChange">
            </el-pagination>
        </div>
        <myEmpty v-else desc="当前用户不可查看"></myEmpty>
    </div>
</template>
  
<script>
import myEmpty from "@/components/components/my_empty.vue";
export default {
    components: {
        myEmpty,
    },
    props: {
        is_house_show: {
            type: [Boolean, Number],
            default: 1,
        },
        house_params: {
            type: [Object],
            default: () => { },
        },
        house_list: {
            type: [Object],
            default: () => { },
        }
    },
    watch: {
        // house_params(newVal) {
        //     console.log(newVal, "1111");
        //     if (!newVal.length) {
        //         this.HouseParams = []; // 如果 house_params 为空则设置为一个空数组
        //     } else {
        //         this.HouseParams = newVal; // 否则使用接收到的值
        //     }
        // }
    },
    data() {
        return {
            HouseParams: {},// 初始化为一个空数组
            params: {
                user_name: "",
                department_id: "",
                order: "",
            },
            p_time: "",
            tableData: [],
            is_table_loading: false,
            department_list: [],
            website_id: ''
        };
    },
    created() {
        
        this.website_id = this.$route.query.website_id

    },
    mounted() {
            // console.log(this.house_list);
        if (this.house_list) {
            console.log(this.house_list);
            // this.getCrmIntelligentRateList();
            this.getCrmDepartmentList();
        }
    },
    methods: {
        /* eslint-disable */
        // 获取部门列表
        getCrmDepartmentList() {
            this.$http.getCrmDepartmentList().then((res) => {
                if (res.status === 200) {
                    this.department_list = res.data;
                }
            });
        },
        /**
         *  row: 行数据
         *  column :列数据
         *  rowIndex: 行下标
         *  columnIndex: 列下标
         * */

        cellStyle({ row, column, rowIndex, columnIndex }) {
            // return `background-color:#BEDCFF;`;
            // return `background-color:#FFEBA5;`;
            // return `background-color:#FFB88D;`;
            if (column.label === "客户数量") {
                return this.setStyle(row.khzl.rank);
            }
            if (column.label === "首跟平均响应时长") {
                return this.setStyle(row.sgsc.rank);
            }
            if (column.label === "首看次数") {
                return this.setStyle(row.skcs.rank);
            }
            if (column.label === "复看次数") {
                return this.setStyle(row.fkcs.rank);
            }
            if (column.label === "带看总量") {
                return this.setStyle(row.dkzl.rank);
            }
            if (column.label === "带看率") {
                return this.setStyle(row.dklv.rank);
            }
            if (column.label === "复看率") {
                return this.setStyle(row.fklv.rank);
            }
            if (column.label === "跟进率") {
                return this.setStyle(row.gjlv.rank);
            }
            if (column.label === "成交率") {
                return this.setStyle(row.cjlv.rank);
            }
        },
        setStyle(rank) {
            if (rank === 1) {
                return `background-color:#ffb88d;`;
            }
            if (rank === 2) {
                return `background-color:#bedcff;`;
            }
            if (rank === 3) {
                return `background-color:#ffeba5;`;
            }
        },
        // changeTimeRange(e) {
        //     this.params.b_date = e ? e[0] : "";
        //     this.params.e_date = e ? e[1] : "";
        //     this.params.page = 1;
        // },

        onPageChange1(e) {
            console.log(this.params);
            this.$emit("getDataOk", this.params)
        },
        onPageChange2() {
            console.log(this.params);
            this.$emit("getDataOk", this.params)

        },
        onPageChange(e){
            console.log(e)
            this.$emit("getData", e)
        },
        // 自定义排序
        onSortChange({ column, prop, order }) {
            var prop_1 = prop.split("_")[0];
            if (order === "descending") {
                // 大->小
                this.params.order = prop_1 + "_down";
            } else if (order === "ascending") {
                this.params.order = prop_1 + "_up";
            } else {
                this.params.order = "";
            }
            console.log(this.params);
            this.$emit("sortData",this.params)
        },
    },
};
</script>
  
<style scoped lang="scss">
.f-c {
    color: #2e3c4e;
    font-size: 14px;
    display: flex;
    align-items: center;

    .f {
        width: 20px;
        height: 8px;
        margin: 0 10px;

        &.f1 {
            background: #ffb88d;
        }

        &.f2 {
            background: #bedcff;
        }

        &.f3 {
            background: #ffeba5;
        }
    }
}

.name-box-cy {
    align-items: center;

    .left {
        margin-right: 7px;
        background: #2d84fb;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        color: #fff;
        line-height: 24px;
        text-align: center;
        font-size: 14px;
        flex-shrink: 0;
    }

    .right {
        font-size: 12px;

        .c {
            color: #2e3c4e;
        }

        .b {
            color: #8a929f;
        }
    }
}
</style>
  