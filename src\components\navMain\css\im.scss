.im-box {
  min-width: 1170px;
  box-sizing: border-box;
  width: 35%;
  margin: auto;
  height: 700px;
  // background-color: #fff;
  // border: 1px solid #f3f3f3;
  // border-top-left-radius: 20px;
  // border-top-right-radius: 20px;
  overflow: hidden;
  // box-sizing: border-box;
  position: relative;
  &.has_info {
    width: 1626px;
  }
}
.chat_bar {
  width: 100%;
  height: 100%;
  // position: absolute;
  left: 0;
  padding: 12px 24px;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
  .my_info {
    padding: 15px;
    border: 1px solid #f3f3f3;
    border-radius: 12px;
    width: 366px;
    box-sizing: border-box;

    .header_box {
      width: 50px;
      height: 50px;
      overflow: hidden;
      float: left;
      margin-right: 10px;

      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .info_box {
      .name {
        font-size: 16px;
        margin-right: 5px;
        font-weight: bold;
      }

      .build_name {
        font-size: 12px;
        color: #888;
      }

      .level {
        margin-left: 5px;
        padding: 3px 5px;
        color: #ffffff;
        background-image: linear-gradient(180deg, #419dff, #1381f5);
        // background-color: #ff706f;
        border-radius: 5px;
        font-size: 10px;
      }

      .data_box {
        margin-top: 8px;
        font-size: 12px;
        color: #888;
        min-height: 16px;

        span {
          margin-right: 10px;
          display: inline-block;
          max-width: 180px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .menu_box {
        line-height: 40px;

        .menu_item {
          font-size: 13px;
          display: inline-block;
          color: #666;

          ~ .menu_item {
            margin-left: 15px;
          }

          .el-icon-tfy-dianhua1 {
            position: relative;
            top: 3px;
            margin-right: 3px;
            font-size: 24px;
            color: #1296db;
          }

          .el-icon-tfy-weixin2 {
            position: relative;
            top: 3px;
            margin-right: 3px;
            font-size: 24px;
            color: #09de74;
          }

          .el-icon-tfy-gengduo {
            position: relative;
            top: 3px;
            margin-right: 3px;
            font-size: 24px;
            color: #ff5e66;
          }
        }
      }
    }
  }

  .friend_list {
    margin-top: 10px;
    height: 565px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    // &::-webkit-scrollbar {
    //   width: 6px;
    //   height: auto;
    //   background-color: #ebebeb;
    // }
    // &::-webkit-scrollbar-thumb {
    //   border-radius: 10px;
    //   background-color: #aaa;
    // }
    // &::-webkit-scrollbar-track {
    //   border-radius: 10px;
    //   background-color: #dedede;
    // }
    &.has_my_info {
      height: 445px;
    }

    .friend {
      padding: 20px 0;
      height: 50px;
      font-size: 0;
      position: relative;
      cursor: pointer;
      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 62px;
        right: 0;
        height: 1px;
        background-color: #dde1e9;
      }
      &:hover {
        background-color: #f7faff;
      }

      &.active {
        background-color: #f7faff;
      }

      .img_box {
        width: 56px;
        height: 56px;
        float: left;
        margin-right: 12px;
        position: relative;

        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
        .unread {
          margin-top: 5px;
          min-width: 20px;
          box-sizing: border-box;
          text-align: center;
          height: 20px;
          padding: 0 5px;
          font-size: 12px;
          color: #fff;
          background-color: #ff6d29;
          // display: inline-block;
          line-height: 20px;
          border-radius: 10px;
          position: absolute;
          top: -6px;
          right: -6px;
        }
      }

      .friend_info {
        display: inline-block;
        width: 215px;

        .nickname {
          height: 25px;
          margin-bottom: 5px;
          color: #333;
          .name {
            display: inline-block;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 18px;
            float: left;
          }
        }
        .levelname {
          margin-left: 5px;
          padding: 1px 4px 2px 4px;
          display: inline-block;
          border-radius: 3px;
          font-size: 12px;
          color: #fff;
          background-image: linear-gradient(180deg, #69d4bb, #00caa7);

          &.official {
            background-image: linear-gradient(180deg, #8cd3fc, #4cc7f6);
            // color: #1296db;
          }

          &.agent {
            background-image: linear-gradient(180deg, #ff9767, #fd7737);
          }

          &.adviser {
            background-image: linear-gradient(180deg, #fcd88c, #f6ce4c);
          }
        }

        .chat {
          height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          color: #888;
        }
      }

      .right_info {
        float: right;
        width: 70px;
        overflow: hidden;

        .time {
          font-size: 13px;
          color: #999;
        }

        .btn {
          font-size: 13px;
          padding: 3px 5px;
          color: #1296db;
          line-height: 25px;
          border-radius: 3px;
          text-align: center;
        }
      }
    }

    .nodata {
      padding: 15px;
      text-align: center;
      font-size: 14px;
      color: #999;
    }
  }
}

.search_box {
  padding: 10px 0;
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .el-input {
    flex: 1;
  }
  .clear_btn {
    display: inline-block;
    padding: 5px 10px;
    margin-left: 66px;
    font-size: 14px;
    color: #8a929f;
    cursor: pointer;
  }
}

.tabs_box {
  font-size: 0;
  // border-bottom: 1px solid #f3f3f3;
  width: 100%;
  ::v-deep .el-tabs__header {
    margin-bottom: 0;
  }
  .tab_item {
    width: 33.333%;
    padding: 10px;
    box-sizing: border-box;
    font-size: 14px;
    display: inline-block;
    text-align: center;
    cursor: pointer;
    &.col4 {
      width: 25%;
    }

    &.active {
      border-bottom: 2px solid #ff5e66;
    }
  }
}

.chat_content {
  // background-color: #fff1f1;
  height: 100%;
  position: relative;
  overflow: hidden;

  .friend_top {
    padding: 15px;
    background-color: #fff;
    border-bottom: 1px solid #dde1e9;

    .header_box {
      width: 50px;
      height: 50px;
      overflow: hidden;
      float: left;
      margin-right: 10px;

      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .info_box {
      .name {
        font-size: 16px;
        margin-right: 5px;
        font-weight: bold;
      }

      .status {
        font-size: 12px;
        color: #48dab4;
      }

      .level {
        margin-left: 10px;
        padding: 3px 5px;
        color: #ffdc42;
        background-image: linear-gradient(180deg, #ff7e7e, #fd5f5f);
        border-radius: 5px;
        font-size: 10px;
      }

      .data_box {
        margin-top: 8px;
        font-size: 12px;
        color: #888;
        min-height: 16px;

        span {
          margin-right: 10px;
          display: inline-block;
          max-width: 180px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .menu_box {
      position: absolute;
      right: 0;
      top: 10px;
      line-height: 60px;

      .menu_item {
        display: inline-block;
        margin-right: 15px;
        cursor: pointer;
        > i {
          font-size: 22px;
          color: #8a929f;
          &:focus {
            outline-width: none;
          }
          &.active {
            color: #2d84fb;
          }
        }
      }
    }
  }

  .chat_body {
    position: relative;
    .mask {
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background-color: rgba($color: #000000, $alpha: 0);
      transition: 0.36s;
      &.show {
        z-index: 1;
        background-color: rgba($color: #000000, $alpha: 0.15);
      }
    }
    .house_list {
      width: 420px;
      padding: 24px;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 2;
      transform: translateX(100%);
      height: 100%;
      background-color: #fff;
      transition: 0.36s;
      &.show {
        transform: translateX(0);
      }
      .list_container {
        height: 495px;
        overflow-x: hidden;
        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
    .chat_info {
      width: 280px;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 2;
      transform: translateX(100%);
      height: 100%;
      background-color: #fff;
      transition: 0.36s;
      &.show {
        transform: translateX(0);
      }

      .card {
        padding: 10px;

        .title {
          padding: 10px;
        }

        .info_box {
          padding: 10px 20px;
          text-align: center;
          .avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
          }
          .name {
            padding: 5px;
            font-size: 16px;
            margin-bottom: 10px;
          }

          .btn {
            text-align: center;
            margin-top: 20px;
            font-size: 13px;
            color: #1296db;
            cursor: pointer;
          }
        }

        .time_line {
          padding: 10px 20px;
          height: 370px;
          overflow-x: hidden;
          &::-webkit-scrollbar {
            display: none;
          }
        }

        .line_item {
          padding-left: 30px;
          padding-bottom: 20px;
          border-left: 1px solid #dedede;
          position: relative;
          box-sizing: border-box;

          &::before {
            content: "";
            height: 5px;
            width: 5px;
            border-radius: 50%;
            background-color: #2d84fb;
            position: absolute;
            left: -3px;
            top: 0;
          }

          .time {
            margin-bottom: 12px;
            font-size: 12px;
            color: #8a929f;
          }
          .desc {
            font-size: 14px;
          }
        }
      }
    }
  }

  .chat_list_box {
    height: 470px;
    overflow-x: hidden;
    padding: 16px;
    box-sizing: border-box;
    &::-webkit-scrollbar {
      display: none;
    }

    .get_more {
      padding: 10px;
      font-size: 13px;
      color: #888;
      text-align: center;
      cursor: pointer;
    }
    .no_more {
      padding: 10px;
      font-size: 13px;
      color: #888;
      text-align: center;
    }

    .chat_item {
      .time {
        text-align: center;
        margin-top: 20px;
        span {
          padding: 4px 15px 2px 15px;
          border-radius: 3px;
          background-color: #e7e7e7;
          font-size: 13px;
          color: #888;
        }
      }
    }

    .my_chat {
      margin-top: 10px;
      min-height: 50px;

      .header_box {
        width: 50px;
        height: 50px;
        overflow: hidden;
        float: right;

        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .info {
        float: right;
        max-width: 60%;
        min-height: 25px;
        margin-right: 25px;
        padding: 6px 15px;
        margin-top: 5px;
        line-height: 1.6;
        min-height: 24px;
        border-radius: 10px;
        background-color: #2d84fb;
        color: #fff;
        position: relative;
        white-space: pre-line;

        .point {
          border: 10px solid;
          border-color: #2d84fb transparent transparent #2d84fb;
          position: absolute;
          top: 8px;
          right: -8px;
        }
      }

      .img {
        float: right;
        max-width: 300px;
        margin-right: 25px;
        padding: 12px;
        margin-top: 5px;
        line-height: 1.6;
        border-radius: 10px;
        background-color: #fff;
        color: #fff;
        position: relative;
        font-size: 0;

        img {
          max-width: 100%;
          border-radius: 10px;
        }

        .point {
          border: 10px solid;
          border-color: #fff transparent transparent #fff;
          position: absolute;
          top: 8px;
          right: -8px;
        }
      }
      .voice {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .play_vioce_icon {
        margin-right: 5px;
        width: 20px;
        height: 20px;
      }
      .build-card {
        width: 50%;
        padding: 10px;
        margin-right: 20px;
        background-color: #f7f7f7;
        border-radius: 6px;
        float: right;
        cursor: pointer;

        .build-img-box {
          width: 100%;
          height: 182px;
          border-radius: 5px;
          overflow: hidden;

          .build-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .build-title {
          margin-top: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .friend_chat {
      margin-top: 10px;
      padding: 10px;

      .header_box {
        width: 50px;
        height: 50px;
        overflow: hidden;
        float: left;

        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .info {
        float: left;
        max-width: 60%;
        min-height: 25px;
        margin-left: 25px;
        padding: 6px 15px;
        margin-top: 5px;
        line-height: 1.6;
        border-radius: 10px;
        background-color: #f1f4fa;
        color: #2e3c4e;
        position: relative;
        white-space: pre-line;

        .point {
          border: 10px solid;
          border-color: #f1f4fa #f1f4fa transparent transparent;
          position: absolute;
          top: 8px;
          left: -8px;
        }
        .voice {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .play_vioce_icon {
          margin-right: 5px;
          width: 20px;
          height: 20px;
        }
      }

      .img {
        float: left;
        max-width: 300px;
        margin-left: 25px;
        padding: 12px;
        margin-top: 5px;
        line-height: 1.6;
        border-radius: 10px;
        background-color: #ffffff;
        color: #ff706f;
        position: relative;
        font-size: 0;

        img {
          max-width: 100%;
          border-radius: 10px;
        }

        .point {
          border: 10px solid;
          border-color: #fff #fff transparent transparent;
          position: absolute;
          top: 8px;
          left: -8px;
        }
      }

      .build-card {
        width: 50%;
        padding: 10px;
        margin-left: 20px;
        background-color: #fff;
        border-radius: 6px;
        float: left;
        cursor: pointer;

        .build-img-box {
          width: 100%;
          height: 182px;
          border-radius: 5px;
          overflow: hidden;

          .build-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .build-title {
          margin-top: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .system-info {
      padding: 5px;
      width: 72%;
      margin: 0 auto;
      margin-top: 20px;
    }
    .apply-tel {
      position: relative;
      top: 20px;
      text-align: center;
      color: #666;
      font-size: 13px;

      .cont {
        padding: 3px 10px;
        border-radius: 3px;
        background-color: #e9e8e8;
      }

      .agree {
        color: #1296db;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .apply-wechat {
      position: relative;
      top: 20px;
      text-align: center;
      color: #666;
      font-size: 13px;

      .cont {
        padding: 3px 10px;
        border-radius: 3px;
        background-color: #e9e8e8;
      }

      .agree {
        color: #1296db;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .wechat-card {
      width: 320px;
      margin: 0 auto;
      border-radius: 10px;
      background-color: #fff;
      border: 1px solid #f3f3f3;

      .wechat-info-box {
        align-items: center;
        padding: 12px;
        height: 50px;
      }

      .wechat-icon {
        width: 50px;
        min-width: 50px;
        height: 50px;
        text-align: center;
        line-height: 52px;
        border-radius: 6px;
        background-color: #09de74;
        margin-right: 10px;
        float: left;
        font-size: 30px;
        color: #fff;
      }

      .wechat-info {
        .name {
          margin-bottom: 5px;
          color: #666;
        }

        .wechat {
          font-weight: bold;
        }
      }

      .options {
        border-top: 1px solid #f3f3f3;
        font-size: 0;

        div {
          display: inline-block;
          width: 100%;
          box-sizing: border-box;
          text-align: center;
          padding: 15px 10px;
          font-size: 15px;
          cursor: pointer;
        }

        .right-btn {
          border-left: 1px solid #f3f3f3;
          color: #ff706f;
        }
      }
    }
  }

  .send_box {
    height: 150px;
    padding: 10px 15px;
    box-sizing: border-box;
    background-color: #fff;
    position: relative;
    border-top: 1px solid #dde1e9;

    .face_list {
      padding: 10px;
      width: 360px;
      position: absolute;
      z-index: 2;
      bottom: 150px;
      background-color: #fff;
      box-shadow: 0 0 5px #ccc;

      .face_item {
        width: 10%;
        padding: 5px;
        box-sizing: border-box;
        display: inline-block;
        cursor: pointer;

        img {
          max-width: 100%;
        }
      }
    }

    .fast_reply_box {
      padding: 0 10px;
      width: 450px;
      box-sizing: border-box;
      position: absolute;
      z-index: 2;
      bottom: 150px;
      right: 0;
      background-color: #fff;
      box-shadow: 0 0 5px #ccc;
    }

    .fast_reply_chil_box {
      position: relative;

      // padding-bottom: 45px;
      .edit_fast_reply {
        height: 45px;
        width: 100%;
        line-height: 45px;
        text-align: center;
        color: #888;
        position: absolute;
        bottom: 0;
        cursor: pointer;
      }
    }

    .fast_reply_list {
      max-height: 190px;
      overflow-x: hidden;

      .fast_reply_item {
        padding: 10px;
        padding-right: 40px;
        border-bottom: 1px solid #f7f7f7;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #333;
        cursor: pointer;
        position: relative;

        .edit {
          width: 40px;
          padding: 10px 0;
          position: absolute;
          right: 0;
          bottom: 0;
          text-align: center;
          font-size: 20px;
        }
      }
    }

    .send_bar {
      height: 40px;
      line-height: 43px;

      .icon {
        padding: 5px;
        margin-right: 12px;
        font-size: 28px;
        color: #888;
        cursor: pointer;
      }

      .file {
        height: 0;
        width: 0;
        overflow: hidden;
      }

      .show_house_btn {
        display: inline-block;
        cursor: pointer;
        > i {
          display: inline-block;
          font-size: 30px;
          color: #888;
        }
        > span {
          margin-left: 6px;
          position: relative;
          bottom: 3px;
          display: inline-block;
          height: 28px;
          line-height: 28px;
          color: #888;
        }
      }

      .fast_reply {
        float: right;
        cursor: pointer;

        .text {
          position: relative;
          top: -5px;
          color: #888;
        }
      }
    }

    .send_input {
      padding: 10px;
      height: 40px;
      overflow: hidden;

      &:focus {
        outline: -webkit-focus-ring-color auto 0;
      }
    }

    .send_btn {
      position: absolute;
      bottom: 15px;
      right: 15px;
      display: inline-block;
      cursor: pointer;
      user-select: none;
    }
  }

  .tip {
    text-align: center;
    font-size: 36px;
    color: #dedede;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    height: 40px;
    margin: auto;
  }
}

.clear_btn {
  color: #666;
  transition: 0.3s;
}
.clear_btn:hover {
  color: #f44;
}
.load_more {
  padding: 10px;
  text-align: center;
  color: #888;
  cursor: pointer;
}

.el-icon-star-off {
  font-size: 27px;
}
.el-icon-star-on {
  font-size: 28px;
}
.el-icon-star-on.hover {
  font-size: 28px;
}

.layer_tel-box {
  padding: 20px 30px;
  margin-left: 5px;
}

.layer_tel-box .tel {
  padding: 15px 15px 15px 60px;
  line-height: 1.8;
  background: url(/static/skin/tfy/img/tel_icon.png) 0 32px no-repeat;
}

.layer_tel-box .tel p {
  font-size: 13px;
}

.layer_tel-box .tel p .color-red {
  font-size: 32px;
  font-weight: bold;
  color: #ff3b4b;
}

.layer_tel-box .build_name {
  padding-left: 60px;
  font-size: 17px;
}

.layer_tel-box .tel_time {
  margin-top: 5px;
  margin-bottom: 15px;
  padding: 3px 0;
  font-size: 13px;
  color: #ff3b4b;
  margin-left: 60px;
  width: 200px;
  text-align: center;
  border: 1px solid #ff3b4b;
  border-radius: 3px;
  cursor: pointer;
}

.layer_tel-box .tel_tip {
  margin-top: 30px;
  padding-left: 60px;
}

.layer_tel-box .tel_tip p {
  margin-bottom: 5px;
}

.layer_tel-box .tel_tip span {
  color: #ff3b4b;
}

.info_search_row {
  margin-bottom: 12px;
}
