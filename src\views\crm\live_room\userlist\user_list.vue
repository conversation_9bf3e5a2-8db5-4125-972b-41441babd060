<template>
    <div v-fixed-scroll="47">
      <div class="topregion">
        <div style="margin-left:10px;">
          <el-form :inline="true" :model="params" class="demo-form-inline">
              <el-form-item>
                <!-- 时间检索(创建时间) -->
                <el-date-picker class="margin" style="width: 350px" v-model="timeValue" type="datetimerange" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
                  value-format="yyyy-MM-dd HH:mm:ss" @change="onChangeTime">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="昵称">
                <el-input v-model="params.nickname" placeholder="昵称"></el-input>
              </el-form-item>
              <el-form-item label="手机号">
                <el-input v-model="params.phone" placeholder="手机号"></el-input>
              </el-form-item>
              <el-form-item label="ip地址">
                <el-input v-model="params.ip" placeholder="ip地址"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onSubmit">查询</el-button>
              </el-form-item>
          </el-form>
        </div>
      </div>
        <el-table
            v-loading="is_table_loading"
              :data="tableData"
              border
              style="width: 100%"
              :header-cell-style="{ background: '#EBF0F7' }">
              <el-table-column
                prop="nickname"
                label="昵称"
                width="180"
                fixed
                v-slot="{ row }">
                <div class="avatar">
                    <div>
                        <img v-if="row.avatar" :src="row.avatar" class="avatar-img"/> 
                        <span v-else class="avatar-word">{{getFirstWord(row.nickname)}}</span>
                    </div>
                    <div style="margin-left:5px;">{{row.nickname?row.nickname:"--"}}</div>
                </div>
              </el-table-column>
              <el-table-column
                prop="id"
                label="ID"
                width="120">
              </el-table-column>
              <el-table-column
                prop="phone"
                label="手机号"
                width="150"
                v-slot="{ row }">
                  <span
                    style="cursor: pointer"
                    @click="togglePhoneNumberDisplay(row)">
                    {{ row.displayedMobile? row.displayedMobile:"--"}}
                  </span>
              </el-table-column>
              <el-table-column
                prop="is_fill "
                label="留资状态"
                v-slot="{ row }"> 
                {{row.is_fill ==1?"已留资":row.is_fill==0?"未留资":"--"}}
              </el-table-column>
              <el-table-column
                prop="created_at"
                label="注册时间"
                width="180"
                v-slot="{ row }">
                {{row.created_at?row.created_at:"--"}}
              </el-table-column>
              <el-table-column
                prop="visit_times"
                label="访问次数"
                v-slot="{ row }">
                {{row.visit_times?row.visit_times:"--"}}
              </el-table-column>
              <el-table-column
                prop="login_times"
                label="登录次数"
                v-slot="{ row }">
                {{row.login_times?row.login_times:"--"}}
              </el-table-column>
              <el-table-column
                prop="login_last_time"
                label="登录时间"
                v-slot="{ row }"
                width="180">
                {{row.login_last_time?row.login_last_time:"--"}}
              </el-table-column>
              <el-table-column
                prop="last_visit_page"
                label="最近浏览"
                v-slot="{ row }"
                width="200">
                <el-tooltip class="item" placement="top" effect="light">
                  <div slot="content" class="huanhang">{{row.last_visit_page?row.last_visit_page:'--'}}</div>
                  <span class="clamp-text">
                  {{row.last_visit_page?row.last_visit_page:"--"}}
                </span>
                </el-tooltip>
                
              </el-table-column>
              <el-table-column
                prop="page_name"
                label="来源页面"
                width="200">
              </el-table-column>
              <el-table-column 
              prop="share_user_name"
              label="分享人"
              width="120"
              v-slot="{ row }">
              {{row.share_user_name?row.share_user_name:"--"}}
              </el-table-column>
              <el-table-column
                label="IP地址"
                prop="login_last_ip"
                width="120"
                v-slot="{ row }">
                    {{row.login_last_ip?row.login_last_ip:"--"}}
              </el-table-column>
            </el-table> 
            <div class="tab-content-footer">
              <div>
                  <!-- <el-button type="primary" size="small" @click="empty">清空</el-button> -->
                  <el-radio-group v-model="params.is_fill" size="small" @change="onSubmit">
                    <el-radio-button label="2">全部</el-radio-button>
                    <el-radio-button label="0">未留资</el-radio-button>
                    <el-radio-button label="1">已留资</el-radio-button>
                  </el-radio-group>
              </div>
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="paramsdata.per_page"
                    :current-page="paramsdata.page"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="paramsdata.total">
                  </el-pagination>
            </div>
    </div>
</template>

<script>
export default {
    props: {
      platform_cat: {
        type: String,
        default: ''
      },
    },
    data() {
        return {
            params:{
                page: 1,
                per_page: 10,
                platform_cat:3,
                is_fill:2,//自定义(全部)
            },
            is_table_loading:false,
            tableData:[],
            paramsdata:{
                page:1,
                per_page: 10,
            },//分页
            timeValue:"",//时间
            pickerOptions: {
              shortcuts: [{
                text: '今天',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '昨天',
                onClick(picker) {
                  const end = new Date();
                  end.setHours(0, 0, 0, 0);
                  const start = new Date(end);
                  start.setDate(start.getDate() - 1);
                  end.setDate(end.getDate() - 1);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本周',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end);
                  start.setDate(start.getDate() - (start.getDay() + 6) % 7); // 获取当前周的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上周',
                onClick(picker) {
                    const today = new Date(); // 获取当前日期
                    const end = new Date(today); // 结束日期为当前日期
                    const start = new Date(today); // 开始日期为当前日期
                    const day = today.getDay(); // 获取当前是星期几
                    const diffToLastSunday = day === 0 ? 7 : day; // 计算到上周日的天数差
                    const diffToMonday = 1 + diffToLastSunday; // 计算到上周一的天数差
                    end.setDate(today.getDate() - diffToLastSunday); // 设置结束日期为上周日
                    end.setHours(23, 59, 59, 999); // 设置时间为当天的23:59:59.999
                    start.setDate(today.getDate() - diffToMonday - 5); // 设置开始日期为上周一
                    start.setHours(0, 0, 0, 0); // 设置时间为当天的00:00:00
                    // 将计算得到的时间范围传递给日期选择器
                    picker.$emit('pick', [start, end]);
                }
              }, {
                text: '本月',
                onClick(picker) {
                  const end = new Date();
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取当前月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              }, {
                text: '上月',
                onClick(picker) {
                  const end = new Date();
                  end.setDate(0); // 获取上个月的最后一天
                  end.setHours(23, 59, 59, 0);
                  const start = new Date(end.getFullYear(), end.getMonth(), 1); // 获取上个月的第一天
                  start.setHours(0, 0, 0, 0);
                  picker.$emit('pick', [start, end]);
                }
              },]
            },
            radio1:"",//留资检索
        }
    },
    mounted(){
      if(this.platform_cat){
        this.params.platform_cat = this.platform_cat
      }
        this.getuser()
    },
    methods:{
      togglePhoneNumberDisplay(row) {
          row.displayedMobile = row.phone; // 更新displayedMobile属性
          this.tableData = [...this.tableData];//对整个数据进行重新赋值
      },
        getFirstWord(str){
          return str.trim().replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '').substring(0, 1);
        },
        getuser(){
          let paramscopy = JSON.parse(JSON.stringify(this.params)); // 拷贝
            if(this.params.is_fill==2){
              paramscopy.is_fill = ""
            }
            this.is_table_loading = true
            this.$http.getuserlist(paramscopy).then((res)=>{
                if(res.status==200){
                    this.is_table_loading = false
                    this.tableData = res.data.data
                    this.tableData.forEach(item => {
                      if(item.phone){
                        item.displayedMobile = item.phone.substr(0, 3) + '*****' + item.phone.substr(-3);
                      }
                    });
                    console.log(res.data);
                    this.paramsdata.page = res.data.current_page;
                    this.paramsdata.total = res.data.total;
                }
            })
        },
        //按时间筛选
        onChangeTime(e){
          if (e && e.length >= 2) {
            if (e[1].endsWith("00:00:00")) {
              e[1] = e[1].slice(0, -8) + "23:59:59";
            }
            this.params.start_date = e ? e[0] : ""; // 赋值开始时间
            this.params.end_date = e ? e[1] : ""; // 赋值结束时间
          } else {
            this.params.start_date = ""; // 赋值开始时间
            this.params.end_date = ""; // 赋值结束时间
          }
          this.params.page = 1; // 显示第一页
          this.getuser(); // 获取最新数据
        },
        //查询
        onSubmit(){
          this.params.page = 1; // 显示第一页
          this.getuser()
        },
        //每页 条
        handleSizeChange(val){
            this.params.per_page = val
            this.getuser()
        },
        //当前页
        handleCurrentChange(val) {
            this.params.page = val
            this.getuser()
        },

    },
}
</script>
<style scoped lang="scss" >
.topregion{
  margin-bottom: 15px;
  display: flex;
}
.avatar{
    // height: 32px;
    white-space: nowrap;
    display: flex;
    .avatar-img{
        height: 32px;
        width: 32px;
        border-radius: 50%;
    }
    .avatar-word{
        display: inline-block;
        height: 32px;
        width: 32px;
        border-radius: 50%;
        background-color: #f1f2f3;
        line-height: 32px;
        text-align: center;
        color: #c6c6c6;
        font-weight: 300;
    }
}
.huanhang{
  width: 200px;
  white-space: pre-wrap;
}
.clamp-text {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 限制显示两行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 15px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}
</style>