<template>
  <div>
    <el-table :data="leafletList" style="width: 100%" height="400" @select="select" @select-all="selectAll">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column fixed prop="created_at" label="日期" width="200">
      </el-table-column>
      <el-table-column prop="title" label="传阅页标题" width="200">
      </el-table-column>
      <el-table-column prop="btn_name" label="按钮描述" width="150">
      </el-table-column>
      <el-table-column label="状态" width="150">
        <template slot-scope="scope">
          <el-tag :type="scope.row.is_show ? 'success' : 'danger'">{{ scope.row.is_show ? '已开启' : '已关闭' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="宣传图" width="150">
        <template slot-scope="scope">
          <el-image :src="scope.row.top_pic" style="width: 50px; height: 60px" />
        </template>
      </el-table-column>
      <el-table-column prop="share_title" label="分享标题" width="150">
      </el-table-column>
      <el-table-column prop="share_desc" label="分享描述" width="200">
      </el-table-column>
      <el-table-column label="分享图片" width="120">
        <template slot-scope="scope">
          <el-image :src="scope.row.share_pic" style="width: 50px; height: 50px" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <!-- 1 编辑 2获客记录 -->
          <el-dropdown @command="handleCommand($event, scope.row)">
            <el-link type="Info">查看
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-link>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1">抖音小程序</el-dropdown-item>
              <el-dropdown-item command="2" disabled>微信小程序</el-dropdown-item>
              <el-dropdown-item command="copyPath">复制地址</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-link type="primary" style="margin: 0 20px;" @click="handleClick(scope.row, 'caRecord')">获客记录</el-link>
          <el-link type="warning" @click="handleClick(scope.row, 'editLeaflet')">编辑</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-dropdown @command="command">
        <el-button type="primary">
          批量开启 / 关闭<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">打开</el-dropdown-item>
          <el-dropdown-item command="0">关闭</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-popconfirm title="确认删除吗？" style="margin-left: 20px;" @onConfirm="allDel">
        <el-button slot="reference" type="danger">批量删除</el-button>
      </el-popconfirm>
      <el-dialog title="获客记录" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
        <div style="display: flex;flex-direction: column;align-items: center;">
          <el-table :data="caRecordList" style="width: 100%" height="500px">
            <el-table-column fixed prop="created_at" label="日期" width="200">
            </el-table-column>
            <el-table-column prop="mobile" label="客户手机号" width="150">
            </el-table-column>
            <el-table-column label="分享链路" width="150">
              <template slot-scope="scope">
                <el-tag :type="scope.row.admin_info ? 'success' : 'danger'">{{ scope.row.admin_info ? '内部分享' : '外部分享'
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="数据来源" width="150">
              <template slot-scope="scope">
                <el-tag :type="scope.row.type == 1 ? 'success' : ''">{{ scope.row.type == 1 ? '表单填写' : '授权登录'
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="share_user_name" label="分享人" width="150">
            </el-table-column>
          </el-table>
          <el-pagination layout="prev, pager, next" :hide-on-single-page="total > 10" :total="total"
            @current-change="handleCurrentChange">
          </el-pagination>
        </div>

      </el-dialog>
      <el-dialog customClass="myQrCode" title="查看挂载页" :visible.sync="dialogQrCode" width="600">
        <div>
          <img style="display: block; width: 200px;height: 200px;margin: 0 auto;" :src="qrCode" />
          <span
            style="display: block; width: 100%;text-align: center;margin-top: 10px;color: #a1a1a1;">请使用抖音APP扫描二维码</span>
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogQrCode: false,
      dialogType: 1,
      qrCode: '',
      caRecordList: [],
      dialogVisible: false,
      leafletList: [],
      ids: [],
      params: {
        page: 1,
        per_page: 10,
      },
      total: 0
    }
  },
  mounted() {
    this.getLeafletList()
  },
  methods: {
    async getLeafletList() {
      const res = await this.$http.getLeafletListAPI()
      if (res.status == 200) {
        this.leafletList = res.data.data
      }
    },
    async getQrCode(item) {
      const res = await this.$http.getQrCodeDyAPI({ id: item.id, env_version: 'current' })
      if (res.status == 200) {
        this.qrCode = `data: image/jpeg;base64,${res.data}`
        this.dialogQrCode = true
      }
    },
    handleCommand(type, item) {
      if (type == '1') {
        this.getQrCode(item)
      }
      switch(type){
        case 'copyPath':
          this.copyMiniProgramPath(item);
          break;
      }
    },
    // 复制小程序地址
    async copyMiniProgramPath(item){
      const res = await this.$http.getLeafletSharePath({ id: item.id }).catch(e=>{})
      if (res && res.status == 200) {
        this.$onCopyValue(res.data.url)
      }
    },
    // 编辑
    handleClick(item, type) {
      if (type == 'caRecord') {
        this.dialogVisible = true
        this.id = item.id
        this.getCaRecordList(this.id, this.params)
      } else {
        this.$emit('addTab', item, type)
      }

    },
    handleClose() {
      this.dialogVisible = false
    },
    handleCurrentChange(e) {
      this.params.page = e
      this.getCaRecordList(this.id, this.params)
      console.log(e)
    },
    select(e) {
      this.getIds(e)
    },
    selectAll(e) {
      this.getIds(e)
    },
    getIds(e) {
      if (!e.length) return
      let ids = []
      e.forEach(item => ids.push(item.id))
      this.ids = ids
    },
    //批量开启 / 关闭
    command(e) {
      this.batchOperation('is_show', e)
    },
    // 批量删除
    allDel() {
      this.batchOperation('del')
    },
    async batchOperation(type, isShow) {
      if (!this.ids.length) return
      let data = {
        ids: this.ids,
        field: type,
        value: isShow
      }
      if (type == 'del') {
        delete data.value
      }
      const res = await this.$http.changeLeaflet(data)
      if (res.status == 200) {
        this.getLeafletList()
      }
    },
    async getCaRecordList(id, params) {
      const res = await this.$http.getCaRecord(id, { params })
      if (res.status == 200) {
        this.caRecordList = res.data.data
        this.total = res.total
      }
    }
  },
}
</script>
<style lang="scss">
.myQrCode {
  width: 300px !important;
  border-radius: 10px;
}
</style>