<template>
  <el-main>
    <!-- <div class="back">
			<el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
    </div>-->
    <el-form label-width="100px" :model="form" :rules="rules" ref="formRules">
      <el-form-item label="标题：" prop="title">
        <el-input v-model="form.title" placeholder="请输入资讯标题"></el-input>
      </el-form-item>
      <el-form-item label="分类：">
        <el-select v-model="form.news_category_id" placeholder="请选择">
          <el-option
            v-for="item in news_category_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="form.content_category" placeholder="请选择">
          <el-option
            v-for="item in content_category_list"
            :key="item.id"
            :label="item.description"
            :value="+item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="缩略图模式："
        v-if="website_info.website_mode_category === 2"
      >
        <el-select v-model="form.list_category" placeholder="请选择">
          <el-option
            v-for="item in list_category_list"
            :key="item.id"
            :label="item.description"
            :value="+item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="tolink" v-if="Number(form.content_category) === 2">
        <el-form-item label="网址：" prop="out_link_address">
          <el-input
            placeholder="请以https://开头"
            style="width:500px"
            v-model="form.out_link_address"
          ></el-input>
        </el-form-item>
        <el-form-item label="简述：">
          <el-input style="width:500px" v-model="wai_content"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="内容描述：" prop="desc">
        <el-input type="textarea" v-model="form.description" rows="4"></el-input>
      </el-form-item>
      <el-form-item label="初始阅读量：" prop="read_total">
        <el-input type="number" v-model="form.read_total"></el-input>
      </el-form-item>
      <el-form-item label="排序：" prop="sort">
        <el-input type="number" v-model="form.sort"></el-input>
      </el-form-item>
      <el-form-item label="作者：">
        <el-input v-model="form.author"></el-input>
      </el-form-item>

      <el-form-item label="缩略图：">
        <el-upload
          :headers="myHeader"
          :action="information_upload_type"
          :on-success="handleSuccessnews"
          list-type="picture-card"
          :on-preview="handlePictureCardPreviewnews"
          :on-remove="handleRemovenews"
          :file-list="img_list"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="newsVisible">
          <img width="100%" :src="newsImageUrl" alt />
        </el-dialog>
      </el-form-item>
      <el-form-item
        label="内容："
        class="ueditor"
        prop="cont"
        v-if="Number(form.content_category) === 1"
      >
        <UE
          :value="ueditor.value"
          :config="ueditor.config"
          ref="ue"
          @input="inputUe"
        ></UE>
      </el-form-item>
      <div class="btn-box">
        <el-form-item size="large">
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button @click="goBack">返回管理列表</el-button>
        </el-form-item>
      </div>
    </el-form>
  </el-main>
</template>

<script>
/* eslint-disable */
import UE from "@/components/ueditor";
import config from "@/utils/config";
import { mapState } from "vuex";
export default {
  components: { UE },
  name: "add_news",
  data() {
    const validator = (rule, value, callback) => {
      if (this.form.content) {
        callback();
      } else {
        callback(new Error("请填写内容"));
      }
    };
    return {
      is_link: false,
      form: {
        content: "",
        title: "",
        content_category: 1,
        img: "",
        img_2: "",
        img_3: "",
        news_category_id: "",
        list_category: 1,
        author: "",
        read_total: 0,
        sort: 0,
        out_link_address: "",
        description: "",
        project_id: 0,
        cont: "",
      },
      news_category_list: [],
      content_category_list: [],
      ueditor: {
        value: "",
        config: {
          initialFrameWidth: "100%",
        },
      },
      ue: "ue",
      newsVisible: false,
      newsImageUrl: "",
      rules: {
        title: [{ required: true, trigger: "blur", message: "请输入标题" }],
        cont: [
          {
            required: true,
            validator: validator,
            trigger: "blur",
            message: "请输入内容",
          },
        ],

        out_link_address: [
          { required: true, trigger: "blur", message: "请输入外部链接" },
        ],
        sort: [{ required: true, trigger: "blur", message: "请输入排序" }],
        read_total: [
          { required: true, trigger: "blur", message: "请输入初始阅读量" },
        ],
      },
      wai_content: "",
      information_upload_type: `/api/common/file/upload/admin?category=${config.INFORMATION_IMG}`,
      list_category_list: [],
      img_list: [],
    };
  },
  mounted() {
    this.content_category_list = this.$getDictionary("CONTENT_CATEGORY");
    this.list_category_list = this.$getDictionary("NEWS_LIST_CATEGORY");
    this.getNewsCategoryList();
  },
  //关闭之后重置表单
  //  deactivated(){
  //     this.reset()
  //  },
  computed: {
    // 获取请求头
    myHeader() {
      return {
        Authorization: config.TOKEN,
      };
    },
    ...mapState(["website_info"]),
  },
  methods: {
    // this.content = this.$refs.ue.getUEContent();
    // 获取楼盘资讯分类
    getNewsCategoryList() {
      if (this.website_info.website_mode_category == 2) {
        this.$http.getNewsCategoryList().then((res) => {
          if (res.status === 200) {
            this.news_category_list = res.data;
          }
        });
      } else {
        this.news_category_list.push({ id: 1, name: "楼盘动态" });
      }
    },
    // 缩略图
    handleRemovenews(file, fileList) {
      this.img_list = fileList;
    },
    handlePictureCardPreviewnews(file) {
      this.newsImageUrl = file.response.url;
      this.newsVisible = true;
    },
    handleSuccessnews(response, file, fileList) {
      this.img_list = fileList;
    },
    goBack() {
      this.$router.back();
    },
    onSubmit() {
      // if (Number(this.form.content_category) === 1) {
      // 	this.form.content = this.$refs.ue.getUEContent();
      // } else {
      // 	this.form.content = "";
      // }
      // if (this.form.content_category == 1 && this.form.content == "") {
      // 	this.$message.error("请输入内容");
      // 	return;
      // }
      if (this.form.content === "" && this.form.content_category == 1) {
        this.$message.error("请输入内容");
        return;
      }
      if (this.img_list.length === 1) {
        this.form.img = this.img_list[0].response.url;
      }
      if (this.img_list.length === 2) {
        this.form.img_2 = this.img_list[1].response.url;
      }
      if (this.img_list.length === 3) {
        this.form.img_3 = this.img_list[2].response.url;
      }

      if (this.form.content === "" && this.form.content_category == 2) {
        delete this.form.content;
      }
      this.$refs.formRules.validate((valid) => {
        if (valid) {
          this.$http.uploadInfo(this.form).then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "上传成功",
                type: "success",
              });
              this.$goPath("/management_page");
            }
          });
        }
      });
    },
    inputUe(obj) {
      if (Number(this.form.content_category) === 1) {
        this.form.content = obj.content;
      } else {
        this.form.content = this.wai_content;
      }
    },
    //重置表单
    reset() {
      this.form = {
        content: "",
        title: "",
        content_category: 1,
        img: "",
        img_2: "",
        img_3: "",
        news_category_id: "",
        list_category: 1,
        author: "",
        read_total: 0,
        sort: 0,
        out_link_address: "",
        description: "",
        project_id: 0,
        cont: "",
      };
      this.$refs.formRules.resetFields();
      this.img_list = [];
    },
  },
};
</script>

<style scoped lang="scss">
.ueditor {
  margin: 20px 0;
}
.back {
  position: absolute;
  top: 78px;
  left: 240px;
  z-index: 10;
}

// .el-form {
// 	padding-top: 30px;
// }
.el-input,
.el-select,
.el-textarea {
  width: 300px;
}
</style>
