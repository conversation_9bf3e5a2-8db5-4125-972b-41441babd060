<template>
     <div class="allpage">
        <div class="tabstyle">  
          <el-form :inline="true" :model="parama" class="demo-form-inline">
            <el-form-item label="时间类型">
              <el-select v-model="parama.time_type" size="small"
                clearable placeholder="请选择时间类型">
                <el-option 
                  v-for="(label, key) in conditiondata.time_type" 
                    :key="key" 
                    :label="label" 
                    :value="key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="行为">
              <el-select v-model="parama.action" size="small"
                clearable placeholder="请选择行为">
                <el-option 
                  v-for="(label, key) in conditiondata.action" 
                    :key="key" 
                    :label="label" 
                    :value="key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签">
              <el-cascader v-model="parama.label" size="small"
                multiple
                clearable placeholder="请选择标签"
                :options="conditiondata.label" 
                :props="{
                  value: 'id',
                  label: 'name',
                  children: 'label',
                  emitPath: false,
                  multiple: true,
                }">
              </el-cascader>
            </el-form-item>
            <el-form-item label="等级">
              <el-select size="small" v-model="parama.level"
              multiple
              clearable placeholder="请选择等级">
                <el-option v-for="item,index in conditiondata.level" :key="index" :label="item.title+'级'"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="来源">
                <el-cascader size="small" 
                 v-model="source_idvalue"
                clearable placeholder="请选择来源" :options="conditiondata.source" @change="sourceLabel_status"
                  :props="{
                    label: 'title',
                    value: 'id',
                    children: 'children',
                    checkStrictly: true ,
                    multiple:true,
                  }">
                </el-cascader>
            </el-form-item>
            <el-form-item label="状态">
                <el-select size="small" v-model="parama.tracking"
                multiple
                clearable placeholder="请选择状态">
                  <el-option v-for="item in conditiondata.tracking" :key="item.id" :label="item.title" :value="item.id">
                  </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="类型">
                <el-select size="small" v-model="parama.type"
                multiple
                clearable placeholder="请选择类型">
                  <el-option v-for="item in conditiondata.type" :key="item.id" :label="item.title" :value="item.id">
                  </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="带看">
                <el-select size="small" v-model="parama.is_take"
                clearable placeholder="请选择类型">
                  <el-option v-for="item in conditiondata.is_task" :key="item.id" :label="item.title" :value="item.id">
                  </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="部门">
              <el-cascader size="small" v-model="parama.execute_department"
                multiple placeholder="请选择部门"
               :options="AllDepartment" :clearable="true"
                :show-all-levels="false" :props="{
                  label: 'name',
                  value: 'id',
                  children: 'subs',
                  checkStrictly: true,
                  emitPath: false,
                  multiple: true
                }">
              </el-cascader>
            </el-form-item>
            <el-form-item label="成员">
                <el-select size="small" v-model="parama.execute_uid"
                  multiple filterable 
                  clearable placeholder="请选择成员"> 
                    <el-option v-for="item in member_listNEW" :key="item.id" :label="item.user_name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="query">查询</el-button>
            </el-form-item>
          </el-form>
          <div class="bulk_operation">
            <el-popover v-model="transfer_type" placement="bottom" width="500px" trigger="click">
              <div class="f-list">
                <div class="f-item" v-for="item in cus_list" :key="item.id" @click="TransferCustomer(item)">
                  {{ item.name }}
                </div>
              </div>
              <div slot="reference" style="margin-right:10px;" class="search_loudong div row align-center">
                <div class="seach_value">批量操作</div>
                <div class="sanjiao" :class="{ transt: transfer_type }"></div>
              </div>
            </el-popover>
          </div>

       
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#EBF0F7' }"
              v-loading="is_table_loading"
              border
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>
              <el-table-column
                prop="status"
                label="流程状态"
                v-slot="{row}">
                <el-tag :type="row.status==0?'info':'success'">{{row.status==0?"禁用":"启用"}}</el-tag>
              </el-table-column>
              <el-table-column
                prop="types"
                label="类型"
                v-slot="{row}">
                {{row.types==1?"私客掉公":row.types==2?"自动分配":row.types==3?"自动转交":"自动提醒"}}
              </el-table-column>
              <el-table-column
                prop="time_type"
                label="条件判断"
                v-slot="{row}"
                min-width="160">
                <div>时间类型：{{ getTimeType(row.time_type) }}{{row.time_status==1?"大于":"小于"}}{{  row.diy_time / 24 +"天"}}</div>
                <div>行为：{{ getTimeaction(row.action) }}大于{{  row.diy_time / 24 +"天"}}</div>
                <!-- <div>大于： {{  row.diy_time / 24 +"天"}}</div> -->
                
              </el-table-column>
              <el-table-column
                prop="diy_time"
                label="同时满足"
                v-slot="{row}"
                min-width="160">
                <div class="satisfy">标签：{{ getTimelabel(row.label) }}</div>
                <div class="satisfy">等级：{{ getTimelevel(row.level) }}</div>
                <div class="satisfy">来源：{{ getTimesource(row.source,row.source2) }}</div>
                <div class="satisfy">状态：{{ getTimetracking(row.tracking) }}</div>
                <div class="satisfy">类型：{{ getTimetype(row.type) }}</div>
                <div class="satisfy">带看：{{ getTimeis_take(row.is_take) }}</div>
              </el-table-column>
              <el-table-column
                prop="execute_type"
                label="适用范围"
                v-slot="{row}">
                <div style="white-space: pre-line;">
                  {{ 
                    (row.execute_uid && row.execute_department) ? `成员/部门` : 
                    row.execute_uid ? `成员：${getexecute_uid(row.execute_uid)} `: 
                    row.execute_department ? `部门：${getexecute_department(row.execute_department)}` : 
                    "全部成员" 
                  }}
                </div>

              </el-table-column>
              <el-table-column
                prop="execute_hour"
                label="自动执行"
                v-slot="{row}">
                <div class="satisfy">每周：{{executeWeek(row.execute_week)}}</div>
                <div class="satisfy">每月：{{row.execute_type==3?row.execute_month.split(',').map(item => item + '号').join(', '):"--"}}</div>
                <div class="satisfy">每天：{{timePickerOptions(row.execute_hour)+"点"}}</div>
              </el-table-column>
              <!-- <el-table-column
                prop="created_at"
                label="创建时间">
              </el-table-column> -->
              <el-table-column
                label="操作">
                <template slot-scope="scope">
                  <el-link type="primary" @click="editeprocess(scope.row)">编辑</el-link>
                  <el-link type="danger" style="margin:10px;" @click="deleteprocess(scope.row.id)">删除</el-link>
                  <el-link  type="info" @click="toprocesslog(scope.row)">日志</el-link>
                </template>
              </el-table-column>
            </el-table>
        </div>
        <div class="page_footer">
          <div class="tab-content-footer">
            <div class="page_footer_l flex-row flex-1 items-center">
            <div class="head-list">
              <el-button type="primary" size="small" @click="empty">清空</el-button>
            </div>
            <div class="head-list">
              <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
            </div>
          </div>
            <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="parama.total"
              :page-sizes="[10, 20, 30, 50,100]" :page-size="parama.per_page" :current-page="parama.page"
              @current-change="onPageChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
        <!-- 编辑流程 -->
        <el-dialog
          title="编辑流程"
          :visible.sync="dialogVisible"
          width="85%"
          :before-close="handleClose">
          <editrules ref="editrules"  :conditiondata="conditiondata" :editdata="editdata"
          @handleClose="handleClose" @gettabdata="gettabdata"></editrules>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submit">确 定</el-button>
          </span>
        </el-dialog>
    </div>
</template>
<script>
import editrules from "./editpublic_rules.vue"
export default {
    components:{
		editrules,
	},
    props:{
      conditiondata: {
            type: Object,
            required: true,
            default: () => ({ time_type: {} })  // 确保有一个默认值
      },
      componentId:{
        type: String,
        default: () => "" // 确保有一个默认值
      } 
    },
    data() {
        return {
            parama:{
                page:1,
                per_page:10,
                time_type:"",//时间类型
                action:"",//行为
            },
            tableData:[],
            is_table_loading:false,
            dialogVisible:false,//编辑流程框
            editdata:{},
            form_data:{
              ids:"",
              status:"",
            },
            multipleSelection:[],//选中的流程
            source_idvalue:"",
            AllDepartment:[],//部门
            member_listNEW:[],
            // 操作类型
            cus_list: [
              { id: 1, name: "批量启用" },
              { id: 0, name: "批量禁用" },
            ],
            transfer_type: false, // 显示/隐藏转交类型
        }
    },
    computed: {
      //时间类型
      timeTypeData() {
            return this.conditiondata?.time_type || {}; // 防止未定义错误
      },
      //行为
      timeactionData(){
        return this.conditiondata?.action || {}; // 防止未定义错误
      },
      //标签
      timelabelData(){
        return this.conditiondata?.label || []; // 防止未定义错误
      },
      //等级
      timelevelData(){
        return this.conditiondata?.level || []; // 防止未定义错误
      },
      //来源
      timesourceData(){
        return this.conditiondata?.source || []; // 防止未定义错误
      },
      //状态
      timetrackingData(){
        return this.conditiondata?.tracking || []; // 防止未定义错误
      },
      //类型
      timewaytypeData(){
        return this.conditiondata?.type || []; // 防止未定义错误
      },
      //带看状态
      timewayis_takeData(){
        return this.conditiondata?.is_task || []; // 防止未定义错误
      },
    },
    watch:{
      componentId:{
        handler(newVal){
          // console.log(newVal);
          // if(newVal=="ruleslist"){
          //   this.gettabdata()
          // }
        }
      }
    },
    created(){
      this.gettabdata()
      this.MembersNEW()
      this.getDepartmentList()
    },
    methods:{
       // 转交客户
    TransferCustomer(item) {
      if(!this.multipleSelection.length){
          this.form_data.status = ""
          return this.$message.warning("请选择需要操作的流程！")
        }
          this.form_data.status = item.id
          this.changestatus()
    },
      //批量更改状态
      changestatus(){
        let params = JSON.parse(JSON.stringify(this.form_data)) // 深拷贝客户来源列表
        params.ids = this.multipleSelection.map(item => item.id).join(',');
        this.$http.bulkoperationpublic(params).then(res=>{
          if(res.status==200){
            this.$message.success("批量修改成功！")
            this.form_data.status = ""
            this.gettabdata()
          }
        })
      },
      //选择来源分别赋值
      sourceLabel_status(e){
          // 初始化 this.parama.source 和 this.parama.source2 为数组
          this.parama.source = [];
          this.parama.source2 = [];
          // 遍历 e 数组
          e.forEach(item => {
              // 将 item[0] 添加到 this.parama.source 数组中
              if (item.length > 0) {
                  this.parama.source.push(item[0]);
              }
              // 如果 item 有第二个元素，将 item[1] 添加到 this.parama.source2 数组中
              if (item.length > 1) {
                  this.parama.source2.push(item[1]);
              }
          });
      },
      //检索查询
      query(){
        
        let params = JSON.parse(JSON.stringify(this.parama)) // 深拷贝客户来源列表
        console.log(this.parama);
        if(params.label&&params.label.length){
          params.label = params.label.join(",")
        }
        if(params.level&&params.level.length){
          params.level = params.level.join(",")
        }
        if(params.execute_uid&&params.execute_uid.length){
          params.execute_uid = params.execute_uid.join(",")
        }
        if(params.execute_department&&params.execute_department.length){
          params.execute_department = params.execute_department.join(",")
        }
        if(params.type&&params.type.length){
          params.type = params.type.join(",")
        }
        if(params.tracking&&params.tracking.length){
          params.tracking = params.tracking.join(",")
        }
        if (params.source && params.source.length) {
            // 使用 Set 去重并转换为数组
            params.source = [...new Set(params.source)].join(",");
        }
        if (params.source2 && params.source2.length) {
            // 使用 Set 去重并转换为数组
            params.source2 = [...new Set(params.source2)].join(",");
        }
        console.log(params,"====");
        this.gettabdata(params)
      },
      //选中的流程
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      //获取列表
        gettabdata(params){
            this.is_table_loading = true
            this.$http.newpublictable(params&&params.page?params:this.parama).then(res=>{
                if(res.status==200){
                    // console.log(res);
                    this.tableData = res.data.data
                    this.parama.total = res.data.total
                    this.is_table_loading = false
                    if(params&&params.page){
                      // console.log(121212121);
                    }else{
                      this.$emit("acceptlength",this.tableData)
                    }
                }
            })
        },
        //返回时间
        timePickerOptions(hour) {
          const startHour = String(hour).padStart(2, '0'); // 格式化为两位数
          const endHour = String((hour + 1) % 24).padStart(2, '0'); // 下一个小时
          return `${startHour}:00--${endHour}:00`; // 返回格式化的时间段
        },
        //返回周几
        executeWeek(Week){
          let result = Week.split(',').map(item => {
            switch (item) {
              case '0':
                return '日';
              case '1':
                return '周一';
              case '2':
                return '周二';
              case '3':
                return '周三';
              case '4':
                return '周四';
              case '5':
                return '周五';
              case '6':
                return '周六';
              default:
                return item + '--'; // 处理未定义的情况
            }
          }).join(', ');
          return result
        },
        //返回时间类型
        getTimeType(timeType) {
            return this.timeTypeData[timeType] || '--'; // 返回默认值
        },
        //返回行为
        getTimeaction(action){
          return this.timeactionData[action] || '--'; // 返回默认值
        },
        //返回时间标签
        getTimelabel(label) {
          // 1. 解析传入的 label 字符串为数组
          const idArray = label.split(',').map(id => Number(id.trim())); // 转换为数字
          // 2. 定义一个数组用于存储找到的 names
          let foundNames = [];
          // 3. 递归函数，用于查找树结构中的节点
          const findNamesById = (data) => {
              data.forEach(node => {
                // console.log(node);
                  // 如果当前节点的 ID 在 idArray 中，添加 name 到 foundNames
                  // console.log(idArray);
                  if (idArray.includes(node.id)) {
                      foundNames.push(node.name);
                  }
                  // console.log(foundNames);
                  // 如果有子节点，继续递归查找
                  if (node.label && node.label.length > 0) {
                      findNamesById(node.label);
                  }
              });
          };
          // 4. 开始查找
          findNamesById(this.timelabelData);
          // 5. 返回找到的 names
          return foundNames.join()||"--";
        },
        //返回等级
        getTimelevel(level){
          // 将 level 字符串分割成数组并去除空格
          const levelArray = level.split(',').map(id => id.trim());
          // 查找所有 id 在 levelArray 中的项并返回其 title 属性
          const matchingItems = this.timelevelData.filter(item => levelArray.includes(String(item.id)));
          // 提取所有的title 属性
          return matchingItems.map(item => item.title+"级").join(', ')||"--"; // 使用逗号连接描述
        },
        //返回来源
        getTimesource(source,source2){
          // 1. 解析传入的 source，source 字符串为数组
          const idArray = source.split(',').map(id => Number(id.trim())); // 转换为数字
          const idArray2 = source2.split(',').map(id => Number(id.trim())); // 转换为数字
          // 2. 定义一个数组用于存储找到的 names
          let foundNames = [];
          // 3. 递归函数，用于查找树结构中的节点
          const findNamesById = (data) => {
              data.forEach(node => {
                // console.log(node);
                  // 如果当前节点的 ID 在 idArray 中，添加 name 到 foundNames
                  // console.log(idArray);
                  if (idArray.includes(node.id)) {
                      foundNames.push(node.title);
                  }
                  if(idArray2.includes(node.id)){
                    foundNames.push(node.title);
                  }
                  // console.log(foundNames);
                  // 如果有子节点，继续递归查找
                  if (node.children && node.children.length > 0) {
                      findNamesById(node.children);
                  }
              });
          };
          // 4. 开始查找
          findNamesById(this.timesourceData);
          // 5. 返回找到的 names
          return foundNames.join()||"--";
        },
        //返回状态
        getTimetracking(tracking){
          // 将 level 字符串分割成数组并去除空格
          const levelArray = tracking.split(',').map(id => id.trim());
          // 查找所有 id 在 levelArray 中的项并返回其 title 属性
          const matchingItems = this.timetrackingData.filter(item => levelArray.includes(String(item.id)));
          // 提取所有的title 属性
          return matchingItems.map(item => item.title).join(', ')||"--"; // 使用逗号连接描述
        },
        //返回类型
        getTimetype(type){
          // 将 level 字符串分割成数组并去除空格
          const levelArray = type.split(',').map(id => id.trim());
          // 查找所有 id 在 levelArray 中的项并返回其 title 属性
          const matchingItems = this.timewaytypeData.filter(item => levelArray.includes(String(item.id)));
          // 提取所有的title 属性
          return matchingItems.map(item => item.title).join(', ')||"--"; // 使用逗号连接描述
        },
        //返回带看状态
        getTimeis_take(is_take){
          // 将 is_take 字符串分割成数组并去除空格
          const levelArray = String(is_take).split(',').map(id => id.trim());
          // 查找所有 id 在 levelArray 中的项并返回其 title 属性
          const matchingItems = this.timewayis_takeData.filter(item => levelArray.includes(String(item.id)));
          // 提取所有的title 属性
          return matchingItems.map(item => item.title).join(', ')||"--"; // 使用逗号连接描述
        },
        //返回成员
        getexecute_uid(uid) {
          const levelArray = uid.split(',').map(id => id.trim());
          const matchingItems = this.member_listNEW.filter(item => levelArray.includes(String(item.id)));
          // 获取前两个成员名称，并连接成字符串
          const memberNames = matchingItems.slice(0, 2)
            .map(item => item.user_name)   // 提取 user_name
            .join(', ');                   // 使用逗号连接
          // 获取匹配成员总数
          const totalMembers = matchingItems.length;
          // 返回最终的字符串，包含成员名称和成员数量
          return `${memberNames}${totalMembers > 2 ? ', ...' : ''}\n(共 ${totalMembers} 个成员)` || '--';
        },
        //返回部门
        getexecute_department(department) {
          // 将 departmentIds 字符串按逗号分割成数组
          const idsArray = department.split(',').map(id => id.trim());
          // 递归查找部门名称的函数
          const findDepartmentName = (departments, id) => {
              for (let department of departments) {
                  if (department.id === id) {
                      return department.name;
                  }
                  // 如果有子部门，递归查找
                  if (department.subs && department.subs.length > 0) {
                      const foundName = findDepartmentName(department.subs, id);
                      if (foundName) return foundName;
                  }
              }
              return null; // 如果没有找到，返回 null
          };
          // 遍历 idsArray，查找每个 ID 的部门名称
          const result = idsArray.map(id => {
              const departmentName = findDepartmentName(this.AllDepartment, parseInt(id));
              return departmentName || '--'; // 找到则返回部门名称，否则返回 '--'
          });
          // 限制返回前两个部门名称，超过的部分用 '...' 替代
          const finalResult = result.slice(0, 2); // 取前两个
          if (result.length > 2) {
              finalResult.push('...'); // 超过两个的部分用 '...' 替代
          }
          // 计算总部门数
          const totalCount = result.length;
          // 拼接最终字符串，包含部门名称和部门数量
          const departmentNames = finalResult.join(', '); // 部门名称用逗号和空格分隔
          const resultString = `${departmentNames} \n(共 ${totalCount} 个部门)`;
          // 返回拼接后的字符串
          return resultString;
        },
        //删除流程
        deleteprocess(id){
            this.$confirm('此操作将永久删除该流程, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
                this.$http.delnewpublic(id).then(res=>{
                    if(res.status==200){
                        this.$message({
                          type: 'success',
                          message: '删除成功!'
                        });
                        this.gettabdata()
                    }
                })
           
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作'
              });          
            });
        },
        //流程日志
        toprocesslog(row){
          this.$emit("room-change",{row:row,text:"processlog",uiddata:this.member_listNEW})
        },
        submit(){
            this.$refs.editrules.submitdata()
        },
        //编辑
        editeprocess(row){
            this.editdata = row
            this.dialogVisible = true
        },
        //关闭编辑框
        handleClose(){
            this.dialogVisible = false
        },
        //刷新
        Refresh(){
            this.gettabdata()
        },
        //清空
        empty(){
          this.parama = {
            page:1,
            per_page:10,
            time_type:"",//时间类型
            action:"",//行为
            label:"",
            level:"",
            source:"",
            source2:"",
            tracking:"",
            type:"",
            execute_department:"",
            execute_uid:"",
          }
          this.source_idvalue = ""
          this.gettabdata()
        },
        onPageChange(current_page) {
          this.parama.page = current_page;
          this.gettabdata();
        },
        //每页几条
        handleSizeChange(e){
          this.parama.per_page = e
          this.gettabdata(); 
        },
        // 获取成员的接口（新）
        MembersNEW(){
          this.$http.getDepartmentMemberListNew().then((res)=>{
            if(res.status==200){
              this.member_listNEW = res.data;
            }
          })
        },
        // 获取部门
        getDepartmentList() {
          this.$http.getCrmDepartmentList().then((res) => {
            if (res.status == 200) {
              this.AllDepartment = res.data;
            }
          });
        },

    },
   
}
</script>
<style lang="scss" scoped>
.allpage {
//   height: 100vh; /* 让页面充满整个视口高度 */
  overflow-y: auto;   /* 超出部分可滚动 */
  // margin-bottom: 30px ;
  .tabstyle{
    margin-top: 10px;
    margin-bottom: 60px;
    .bulk_operation{
      margin-bottom: 10px;
      display: flex;
      justify-content: flex-end;
    }
  }
  .satisfy{
    overflow: hidden; 
    min-width: 135px; 
    white-space: nowrap; 
    text-overflow: ellipsis;
  }
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  // padding: 10px;
  z-index: 1000;

  .head-list {
    margin-left: 10px;
    margin-top: 10px;
  }
}

.search_loudong {
  // width: 65px;
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 33px;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }
}

.seach_value {
    white-space: nowrap;
    font-size: 14px;
    color: #fff;
  }
  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}
  .f-list {
      cursor: pointer;

      .f-item {
        padding: 8px 10px;

        &:hover {
          background: #2d84fb;
          color: #fff;
        }
      }
}
</style>