<template>
  <!-- 审批详情 -->
  <div class="pages">
    <div class="content-box-crm" style="padding-bottom: 800px">
      <div class="title-label">
        <span> 客源审批 </span>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">内部编码：</span></span>
        <el-input v-model="addForm.number" disabled style="width: 240px"></el-input>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">客户姓名：</span></span>
        <el-input style="width: 240px" v-model="addForm.name" disabled></el-input>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">联系方式：</span></span>
        <el-input style="width: 240px" v-model="addForm.mobile" disabled></el-input>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">审批人：</span></span>
        <mySelect :optionSource="admin_list" v-model="addForm.approver_uid" @change="onChangeSelect" labelKey="user_name"
          valueKey="id" multiple width="240px" @page-change="onPageChange" :paginationOption="{
                      pageSize: admin_params.per_page, //每页显示条数
                      currentPage: admin_params.page, //当前页
                      pagerCount: 5, //按钮数，超过时会折叠
                      total: admin_params.total, //总条数
                    }"></mySelect>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">收入费用：</span></span>
        <el-input style="width: 240px" v-model="addForm.money" placeholder="请填写"></el-input>
        <span style="margin-left: 20px" class="text">元</span>
      </div>
      <div class="bottom-border div row">
        <span class="text"><span class="label">补充备注：</span></span>
        <el-input style="width: 240px" v-model="addForm.remarks" type="textarea" :rows="5" placeholder="请填写"></el-input>
      </div>

      <div style="margin-top:20px">
        <el-button @click="onClickDialog" size="small" type="primary">提 交</el-button>
        <el-button size="small" @click="goBack">取 消</el-button>
      </div>
    </div>
    <el-dialog width="374px" :visible.sync="is_dialog_audit" title="审批">
      <div class="formbox">
        <div class="formitem">
          <span class="text">成交类型：</span>
          {{ tracking_name }}
        </div>
        <div class="formitem" v-if="user_detail.create_user">
          <span class="text">发起人：</span>
          {{ user_detail.create_user.user_name }}
        </div>
        <div class="formitem">
          <span class="text">客户名称：</span>
          {{ addForm.name }}
        </div>
        <div class="formitem">
          <span class="text">补充备注：</span>
          {{ addForm.remarks || "--" }}
        </div>
        <div class="formitem">
          <span class="text">收入费用：</span>
          {{ addForm.money || "--" }}元
          <span v-if="false" class="blue" @click="$router.push('/crm_customer_financial')">详情</span>
        </div>
      </div>
      <div class="desc-title">
        <span>审批流程</span>
        <!-- <span class="text">（已管理员预设不可修改审批人和删除抄送人）</span> -->
        <span class="text">（已管理员预设不可修改审批人）</span>
      </div>
      <div class="shenpi">
        <div class="sp-title div row">
          <img src="https://img.tfcs.cn/backup/static/admin/customer/shenqing.png" alt="" />
          <span class="l">审批人<span class="mark">*</span></span>
        </div>
        <div class="sp-content border-left div row">
          <div class="box" v-for="(item, index) in admin_audit_name" :key="index">
            <!-- <img
              width="80px"
              src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80"
              alt=""
            /> -->
            <div class="avatar">{{ item[0] }}</div>
            <span>{{ item }}</span>
          </div>
        </div>
      </div>
      <!-- <div class="shenpi">
        <div class="sp-title div row">
          <img
            src="https://img.tfcs.cn/backup/static/admin/customer/shenpi.png"
            alt=""
          />
          <span class="l">申请人<span class="mark">*</span></span>
        </div>
        <div class="sp-content" style="margin-bottom: 0">
          <div class="el-icon-plus box"></div>
        </div>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="is_dialog_audit = false">取 消</el-button>
        <el-button type="primary" @click="onCreateData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mySelect from "./components/my_select";
import getWxWorkjssdk from "@/utils/getWxWorkjssdk.js";
export default {
  name: "crm_customer_audit_detail",
  components: {
    mySelect,
  },
  data() {
    return {
      addForm: {
        client_id: "",
        name: "",
        mobile: "",
        tracking_id: "",
        number: "",
        money: 0,
        approver_uid: "",
        remarks: "",
      },
      is_dialog_audit: false,
      admin_params: {
        page: 1,
        per_page: 10,
        user_name: "",
        total: 0,
        type: 1,
      },
      admin_list: [],
      user_detail: {},
      status_audit_list: [],
      tracking_name: "",
      admin_audit_name: "",
    };
  },
  mixins: [getWxWorkjssdk],
  mounted() {
    this.addForm.client_id = this.$route.query.id;
    this.addForm.tracking_id = this.$route.query.tracking_id;
    this.getInsideCode();
    this.getUserDetail();
    this.getAdmin();
    this.getStatusAudit();
  },
  methods: {
    getInsideCode() {
      let date = Date.now();
      let dandom = this.randomFloat();
      this.addForm.number = date + "" + dandom;
    },
    randomFloat() {
      // 生成32 位随机值
      const fooArray = new Uint32Array(1);
      // 用最大可能的值来除
      return crypto.getRandomValues(fooArray)[0];
    },
    getStatusAudit() {
      this.$http
        .getCrmCustomerFollowInfo({ params: { type: 3 } })
        .then((res) => {
          if (res.status === 200) {
            this.status_audit_list = res.data;
            this.forMatStatus();
          }
        });
    },
    forMatStatus() {
      this.status_audit_list.map((item) => {
        if (item.id == this.addForm.tracking_id) {
          this.tracking_name = item.title;
        }
      });
    },
    getAdmin() {
      this.$http
        .getManagerAuthList({
          params: this.admin_params,
        })
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    onPageChange(e) {
      this.admin_params.page = e;
      this.getAdmin();
    },
    getUserDetail() {
      this.$http.getCrmCustomerDetailV2(this.addForm.client_id).then((res) => {
        if (res.status === 200) {
          this.addForm.name = res.data.cname;
          this.addForm.mobile = res.data.mobile;
          this.user_detail = res.data;
        }
      });
    },
    goBack() {
      this.$router.back();
    },
    onClickDialog() {
      if (!this.addForm.approver_uid) {
        this.$message.error("请选择审批人");
        return;
      }
      this.is_dialog_audit = true;
    },
    onChangeSelect(e) {
      //选中的数据和options进行匹配
      var arr = [];
      this.admin_list.map(function(i) {
        e.map((item) => {
          if (i.id == item) {
            arr.push(i);
          }
        });
      }); //在change中获取到整条对象数据
      this.admin_audit_name = arr.map((item) => {
        return item.user_name + "";
      });
    },
    onCreateData() {
      this.addForm.approver_uid = this.addForm.approver_uid.join(",");
      let params = Object.assign({}, this.addForm);
      this.$http.setCrmCustomerFollowStatus(params).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.is_dialog_audit = false;
          this.getCrmAuditTemplate(res.data.id);
          // this.$goPath(
          //   `/crm_customer_detail?id=${this.user_detail.id}&type=my`
          // );
        } else {
          this.getInsideCode();
        }
        // 转换回显审批人列表
        this.addForm.approver_uid = params.approver_uid
          .split(",")
          .map((item) => {
            return parseInt(item);
          });
      });
    },
    getCrmAuditTemplate(id) {
      this.getWxQyWxConfig(["thirdPartyOpenPage"], (wx, auth_way) => {
        var _this = this;
        _this.wx = wx;
        if (auth_way != 2) {
          this.$http.getCrmAuditTemplate(id).then((res) => {
            if (res.status === 200) {
              var form = {
                oaType: "10001", // 操作类型，目前支持：10001-发起审批；10002-查看审批详情。
                templateId: res.data.templateId,
                thirdNo: res.data.thirdNo,
                extData: res.data.extData,
              };
              _this.wx.invoke("thirdPartyOpenPage", form, function(res) {
                // 输出接口的回调信息
                console.log(res);
              });
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;

  .bottom-border {
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 24px;
    margin-top: 24px;
    border-bottom: 1px dashed #e2e2e2;

    .text {
      font-size: 14px;
      color: #8a929f;

      .label {
        width: 70px;
        display: inline-block;
        text-align: right;
      }
    }

    .el-radio::v-deep {
      color: #8a929f;
    }
  }

  .title-label {
    margin: 0 -24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;

    span {
      margin-left: 24px;
      color: #2e3c4e;
    }
  }
}

.formbox {
  .formitem {
    margin-bottom: 20px;
    color: #8a929f;

    .text {
      width: 70px;
      text-align: right;
      display: inline-block;
    }

    .blue {
      color: #4770ff;
      margin-left: 24px;
    }
  }
}

.desc-title {
  color: #2e3c4e;
  margin-bottom: 24px;

  .text {
    font-size: 11px;
    color: #8a929f;
  }
}

.shenpi {
  .sp-title {
    align-items: center;

    .l {
      margin-left: 20px;
      color: #8a929f;

      .mark {
        color: red;
      }
    }
  }

  .sp-content {
    margin-left: 10px;
    margin-top: 14px;
    // width: 100px;
    margin-bottom: 20px;
    flex-wrap: wrap;

    .avatar {
      line-height: 30px;
      width: 30px;
      height: 30px;
      font-weight: 600;
      font-size: 16px;
      color: #fff;
      background: #2d84fb;
      border-radius: 50%;
      text-align: center;
    }

    .box {
      // padding: 17px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-left: 30px;
      margin-bottom: 10px;

      span {
        margin-top: 10px;
      }

      // background: #f1f4fa;
      // border-radius: 4px;
    }
  }

  .border-left {
    border-left: 2px solid #eee;
  }
}

.tuandui {
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid #e2e2e2;
}
</style>
