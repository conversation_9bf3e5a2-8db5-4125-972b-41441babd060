<template>
  <div class="content">
    <el-form ref="form" :model="WeChat_form" label-width="200px">
      <el-form-item label="一键加微信（企微提醒）">
        <el-radio v-model="WeChat_form.call_friends" :label="0">关闭</el-radio>
        <el-radio v-model="WeChat_form.call_friends" :label="1"
          >全部开启</el-radio
        >
        <el-radio v-model="WeChat_form.call_friends" :label="2"
          >仅接通发送</el-radio
        >
      </el-form-item>
      <el-form-item label="房源ERP外呼">
        <div class="prompt">
          <el-radio v-model="WeChat_form.call_open_erp" :label="0"
            >关闭</el-radio
          >
          <el-radio v-model="WeChat_form.call_open_erp" :label="1"
            >开启</el-radio
          >
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content" style="max-width: 300px">
              开启后房源详情中的查看电话，手机号后显示外呼按钮。
            </div>
            <i
              class="el-icon-info"
              style="
                color: #f56c6c;
                font-size: 20px;
                margin-top: 10px;
                margin-left: 136px;
              "
            ></i>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item label="外呼方式">
        <el-radio-group v-model="WeChat_form.call_open_crm">
          <el-radio :label="0" style="margin-top: 15px;">关闭</el-radio><br>
          <el-radio :label="1" style="margin-top: 15px;">【私】直接拨打或外呼（私客可查看客户号码）</el-radio><br>
          <el-radio :label="2" style="margin-top: 15px;">【私】隐号外呼（私客不能查看客户号码）</el-radio><br>
          <el-radio :label="3" style="margin-top: 15px;">【公/私】未认领客户 全员可拨打外呼（私客可查看客户号码）</el-radio>
        </el-radio-group>
        <el-tooltip class="item" effect="light" placement="right">
          <div slot="content" style="max-width: 300px">
            <div style="margin-bottom: 5px">
              1.【只能拨打私客】直接拨打或外呼拨打（可查看私客的客户号码）
            </div>
            <div style="margin-bottom: 5px">
              2.【只能拨打私客】隐号外呼（不能查看私客的客户号码）
            </div>
            <div style="margin-bottom: 5px">
              3.【可拨打未认领的公客或自己的私客】未认领客户全员可拨打外呼（可查看私客的客户号码）
            </div>
          </div>
          <i
            class="el-icon-info"
            style="
              color: #f56c6c;
              font-size: 20px;
              margin-top: 10px;
              margin-left: 10px;
            "
          ></i>
        </el-tooltip>
      </el-form-item>
    <!-- <el-form-item v-if="WeChat_form.call_open_crm==3">
      <div class="timeout" >
        通话时长,超过
          <el-input class="outinput" v-model="WeChat_form.call_get_client_time" placeholder="请输入内容"></el-input>  
          秒可认领。
      </div>
    </el-form-item> -->
      <!-- <el-form-item label="首电响应时长规则">
        <div class="prompt">
          <el-radio v-model="WeChat_form.call_first_show" :label="0">关闭</el-radio>
          <el-radio v-model="WeChat_form.call_first_show" :label="1">开启</el-radio>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content" style="max-width: 300px">
              默认关闭，开启后将触发掉公规则
            </div>
            <i
              class="el-icon-info"
              style="
                color: #f56c6c;
                font-size: 20px;
                margin-top: 10px;
                margin-left: 136px;
              "
            ></i>
          </el-tooltip>
        </div>
      </el-form-item> -->
      <!-- <el-form-item v-if="WeChat_form.call_first_show==1">
      <div class="timeout" style="width: 552px;">
        客户分配或转交给维护人,超过
          <el-input class="outinput" v-model="WeChat_form.call_first_time" placeholder="请输入内容"></el-input>  
          小时未使用外呼联系自动掉公
      </div>
    </el-form-item> -->
      <el-form-item label="自动录入CRM">
        <div class="prompt">
          <el-radio v-model="callInfo" :label="0">关闭</el-radio>
          <el-radio v-model="callInfo" :label="1">开启</el-radio>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content" style="max-width: 300px">
              开启状态在通话结束后会自动录入CRM
            </div>
            <i
              class="el-icon-info"
              style="
                color: #f56c6c;
                font-size: 20px;
                margin-top: 10px;
                margin-left: 136px;
              "
            ></i>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onClickForm" v-loading="loading"
          >确定</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      WeChat_form: {
        call_friends: "",
        call_open_erp: "",
        call_open_crm: "",
        // call_get_client_time:"",
        // call_first_show:"",
        // call_first_time:"",
      },
      callInfo: 0, // 控制自动CRM录入
      callInfo_Status: 0, // 存储控制自动CRM录入状态
      loading: false,
      website_id: '',
    }
  },
  created() {
    this.website_id = this.$route.query.website_id
    this.getData();
    this.WhetherOpeningEntry();
  },
  methods: {
    // 获取一键加微信状态
    getData() {
      this.$http.getOutboundWeChat().then(res => {
        if (res.status == 200) {
          this.WeChat_form = {
            call_friends: res.data.call_friends,
            call_open_erp: res.data.call_open_erp,
            call_open_crm: res.data.call_open_crm,
            // call_get_client_time: res.data.call_get_client_time,
            // call_first_show: res.data.call_first_show,
            // call_first_time:res.data.call_first_time
          }
          // this.WeChat_form.call_friends = res.data.call_friends;
          // this.WeChat_form.call_open_erp = res.data.call_open_erp;
          // this.WeChat_form.call_open_crm = res.data.call_open_crm;
        }
      })
    },
    // 点击确定按钮
    onClickForm() {
      this.loading = true
      if (this.callInfo != this.callInfo_Status) {
        this.OpeningEntry();
      }
      console.log(this.WeChat_form);
      this.$http.setOutboundWeChat(this.WeChat_form).then(res => {
        if (res.status == 200) {
          this.$message.success("设置成功")
          this.getData()
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 获取开通外呼录入状态
    WhetherOpeningEntry() {
      this.$http.getWhetherOpening().then(res => {
        if (res.status == 200) {
          this.callInfo = res.data
          this.callInfo_Status = res.data
        }
      })
    },
    // 开通录入CRM
    OpeningEntry() {
      this.$http.changeOpeningStatus({ call_phone_crm: this.callInfo }).then(res => {
        if (res.status == 200) {
          this.WhetherOpeningEntry()
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.content {
  padding: 20px;
  .prompt {
    width: 320px;
  }
  .timeout{
    width: 320px;
    color: #606266;
    .outinput{
      width: 107px;
      height: 10px;
      /deep/.el-input__inner{
        height: 30px;
      }
    }
  }
}
</style>