<template>
  <div class="table outbount">
    <!-- tabs切换 -->
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="直拨" name="second">
        <sound-record-task v-if="activeName == 'second'"></sound-record-task>
      </el-tab-pane>
      <el-tab-pane label="线索包" name="first">
        <div v-if="activeName == 'first'">
          <div class="outbount_search">
            <div class="outbount_top align-center flex-row">
              <div class="outbount_top_item align-center flex-row mr60 mb12">
                <div class="task_name mr10">通话时长</div>
                <div class="task_sel w220 align-center flex-row">
                  <el-select
                    class="select_name flex-1"
                    size="small"
                    v-model="time"
                    @change="changeTimer"
                  >
                    <el-option
                      v-for="sel_item in timeList"
                      :key="sel_item.id"
                      :label="sel_item.name"
                      :value="sel_item.id"
                    >
                    </el-option>
                  </el-select>
                  <template v-if="time == 'default'">
                    <el-input
                      size="small"
                      class="w20 ml10"
                      v-model="task_params.custom_s_duration"
                    >
                      <template slot="append">秒</template>
                    </el-input>
                    -
                    <el-input
                      class="w20"
                      size="small"
                      v-model="task_params.custom_e_duration"
                    >
                      <template slot="append">秒</template>
                    </el-input>
                  </template>
                </div>
              </div>
              <div class="outbount_top_item align-center flex-row mr60 mb12">
                <div class="task_name mr10">任务包名</div>
                <div class="task_sel w300 align-center flex-row">
                  <el-select
                    class="select_name flex-1 mr10"
                    size="small"
                    v-model="task_params.clue_id"
                  >
                    <el-option
                      v-for="sel_item in clueList"
                      :key="sel_item.id"
                      :label="sel_item.name"
                      :value="sel_item.id"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div class="outbount_top_item align-center flex-row mr60 mb12">
                <div class="task_name mr10">成员姓名</div>
                <!-- admin_id -->
                <div class="task_sel w300 align-center flex-row">
                  <div>
                    <el-input
                      placeholder="请选择成员"
                      v-model="user_name"
                      @focus="showMemberList"
                    >
                      <i
                        @click="delName"
                        slot="suffix"
                        class="el-input__icon el-icon-circle-close"
                      ></i
                    ></el-input>
                  </div>
                </div>
              </div>
              <div class="outbount_top_item align-center flex-row mr60 mb12">
                <div class="task_name mr10">被叫号码</div>
                <div class="task_sel w220 align-center flex-row">
                  <el-input size="small" v-model="task_params.callee">
                  </el-input>
                </div>
              </div>
              <div class="outbount_top_item align-center flex-row mr60 mb12">
                <div class="task_name mr10">拨打时间</div>
                <div class="task_sel w300 align-center flex-row">
                  <el-date-picker
                    class="select_name"
                    v-model="date"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="changeDate"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  >
                  </el-date-picker>
                </div>
              </div>
            </div>
            <div class="search_btns align-center flex-row">
              <el-button plain type="info" @click="reset">重置</el-button>
              <el-button type="primary" @click="getTask">查询</el-button>
            </div>
          </div>
          <el-table
            v-loading="task_table_loading"
            :data="task_tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
          >
            <!-- @selection-change="handleSelectionChange" -->
            <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
            <!-- <el-table-column label="ID" width="70" prop="id"></el-table-column> -->
            <el-table-column label="线索名称" prop="clue_name">
            </el-table-column>
            <el-table-column label="被叫名称" prop="name"> </el-table-column>
            <el-table-column label="被叫号码" prop="callee"> </el-table-column>
            <el-table-column label="外显号码" prop="caller"> </el-table-column>
            <!-- <el-table-column label="时长(秒)" prop="duration">
            </el-table-column> -->
            <el-table-column label="通话总时长(秒)" prop="total_duration">
            </el-table-column>
            <el-table-column label="被叫时长(秒)" prop="callee_duration">
            </el-table-column>
            <el-table-column label="主叫时长(秒)" prop="caller_duration">
            </el-table-column>
            <el-table-column label="接通状态" prop="" v-slot="{ row }">
              <el-tag v-if="row.duration == 0" type="warning"> 未接听 </el-tag>
              <el-tag v-else type="success"> 已接听 </el-tag>
            </el-table-column>
            <!-- duration -->
            <el-table-column label="拨打时间" prop="call_time">
            </el-table-column>

            <el-table-column label="操作" v-slot="{ row }">
              <!-- v-slot="{ row }" -->
              <el-popconfirm
                v-if="row.duration > 0"
                title="确定下载录音文件吗？"
                @onConfirm="downLoad(row)"
              >
                <el-link slot="reference" type="success" class="mr10"
                  >下载录音</el-link
                >
              </el-popconfirm>
              <el-link
                style="margin-left: 5px"
                :underline="false"
                @click="play(row)"
                v-if="row.record_url"
              >
                <!-- style="
                    width: 20px;
                    height: 10px;
                    overflow: hidden;
                    background: #f9f9f9;
                  " -->
                <div class="audio_img">
                  <img
                    style="width: 20px; object-fit: cover"
                    v-if="row.isPlaying"
                    :src="
                      $imageDomain + '/static/admin/outbound/play_voice.gif'
                    "
                    alt=""
                  />
                  <img
                    style="width: 20px; object-fit: cover"
                    v-else
                    :src="
                      $imageDomain + '/static/admin/outbound/voice_icon.png'
                    "
                    alt=""
                  />
                </div>
              </el-link>
            </el-table-column>
          </el-table>
          <audio
            ref="musicMp3"
            id="musicMp3"
            :autoplay="false"
            controls="false"
            style="z-index: -1"
          ></audio>

          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev,pager,next"
            :total="taskTotal"
            :page-size="task_params.per_page"
            :current-page="task_params.page"
            @current-change="onTaskPageChange"
          ></el-pagination>
          <el-dialog
            :visible.sync="show_select_dia"
            width="660px"
            title="选择成员"
          >
            <memberListSingle
              v-if="show_select_dia"
              :list="memberList"
              :isOutbound="true"
              @onClickItem="selecetedMember"
            ></memberListSingle>
          </el-dialog>
        </div>
      </el-tab-pane>
      <!-- AI质检TAB -->
      <el-tab-pane v-if="showAiQualityTab" label="AI质检" name="third">
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import memberListSingle from "@/components/navMain/site/components/memberList_single.vue";
import SoundRecordTask from "@/components/navMain/crm/outbound/components/sound_record_task.vue";
export default {
  components: {
    memberListSingle,
    SoundRecordTask,
  },
  data() {
    return {
      sub_loading: false,
      task_params: {
        page: 1,
        per_page: 10,
        admin_id: "",
        clue_id: "",
        s_duration: "",
        e_duration: "",
        custom_s_duration: "",
        custom_e_duration: "",
        callee: "",
        stime: "",
        etime: "",
      },
      taskTotal: 0,
      task_tableData: [],
      task_table_loading: false,
      time: "",
      timeList: [
        {
          id: "",
          name: "全部时长",
        },
        {
          id: "0_30",
          name: "0-30秒",
        },
        {
          id: "30_60",
          name: "30-60秒",
        },
        {
          id: "60_120",
          name: "60-120秒",
        },
        {
          id: "120_300",
          name: "120-300秒",
        },
        {
          id: "300_600",
          name: "300-600秒",
        },
        {
          id: "600_1000000",
          name: "大于600秒",
        },
        {
          id: "default",
          name: "自定义",
        },
      ],
      clueList: [],
      date: [],
      memberList: [],
      user_name: "",
      show_select_dia: false,
      activeName: "second",
      // AI质检TAB显示控制
      showAiQualityTab: false,
    };
  },

  created() {
    this.getTask();
    this.getPackageList();
    this.getDepartment();
    this.checkAiQualityTabPermission();
  },
  methods: {
    // 获取列表
    getTask() {
      this.task_table_loading = true;
      let timeArr = this.time.split("_");
      if (this.time == "default") {
        this.task_params.s_duration = "";
        this.task_params.e_duration = "";
      } else if (timeArr.length) {
        this.task_params.s_duration = timeArr[0];
        this.task_params.e_duration = timeArr[1];
        this.task_params.custom_s_duration = "";
        this.task_params.custom_e_duration = "";
      }
      if (this.date.length) {
        this.task_params.stime = this.date[0];
        this.task_params.etime = this.date[1];
      }
      this.$http
        .getCallPhoneRecord(this.task_params)
        .then((res) => {
          if (res.status == 200) {
            this.task_tableData = res.data.data;
            this.taskTotal = res.data.total;
          }
          this.task_table_loading = false;
        })
        .catch(() => {
          this.task_table_loading = false;
        });
    },
    // 坐席列表页面更新
    onTaskPageChange(val) {
      this.task_params.page = val;
      this.getTask();
    },
    getPackageList() {
      this.$http.getCallClues({}).then((res) => {
        if (res.status == 200) {
          this.clueList = res.data.data;
        }
      });
    },
    // 获取部门列表
    async getDepartment() {
      let res = await this.$http.getOutboundDepartmentList().catch((err) => {
        console.log(err);
      });
      if (res.status == 200) {
        this.memberList = res.data;
      }
    },
    showMemberList() {
      this.show_select_dia = true;
    },
    delName() {
      this.task_params.admin_id = "";
      // this.params.wx_work_user_id = ''
      this.user_name = "";
      // this.task_params.page = 1;
      // this.getDataList();
    },
    selecetedMember(e) {
      if (e.checkedNodes && e.checkedNodes.length) {
        this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.task_params.admin_id =
          e.checkedNodes[e.checkedNodes.length - 1].id;
      } else {
        this.user_name = "";
        this.task_params.admin_id = "";
      }
      this.show_select_dia = false;
    },
    downLoad(row) {
      window.open(row.record_url);
    },
    changeDate(e) {
      console.log(e);
      if (e) {
        this.time = "";
        this.custom_s_duration = "";
        this.custom_e_duration = "";
        this.$forceUpdate();
      }
    },
    changeTimer(e) {
      if (e == "default") {
        this.date = [];
      } else {
        this.custom_s_duration = "";
        this.custom_e_duration = "";
      }
    },

    reset() {
      this.time = "";
      this.date = [];
      this.task_params.admin_id = "";
      this.task_params.clue_id = "";
      this.task_params.s_duration = "";
      this.task_params.e_duration = "";
      this.task_params.custom_s_duration = "";
      this.task_params.custom_e_duration = "";
      this.task_params.callee = "";
      this.task_params.stime = "";
      this.task_params.etime = "";
    },
    // tabs切换
    handleClick(tab) {
      console.log('TAB切换:', tab.name);

      // 如果点击AI质检TAB，跳转到指定路由
      if (tab.name === 'third') {
        this.$goPath("ai_analysis_list");
        return;
      }

      // 其他TAB正常切换
      this.activeName = tab.name;
    },
    play(row) {
      clearTimeout(this.timer)
      let audios = document.getElementById("musicMp3");
      if (this.currI !== row && this.currI) {
        audios.pause();
        this.$set(this.currI, 'isPlaying', false);
        audios.src = row.record_url
        this.$forceUpdate();
      }
      if (row.isPlaying) {
        audios.pause();
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
      } else {
        if (this.currI !== row) {
          audios.src = row.record_url
        }
        audios.play();
        this.currI = row;
        this.$set(row, 'isPlaying', true);
        this.$forceUpdate();
      }
      this.timer = setTimeout(() => {
        this.$set(row, 'isPlaying', false)
        this.$forceUpdate();
        console.log("结束执行");
      }, row.duration * 1000);
    },
    // 检查AI质检TAB显示权限
    async checkAiQualityTabPermission() {
      try {
        const [outboundRes, managerRes] = await Promise.all([
          this.$http.informationoutbound(),
          this.$http.determinecustomeradmin()
        ]);
        if (outboundRes.status === 200 && managerRes.status === 200) {
          const hasOutboundPermission = outboundRes.data === 1;
          const hasManagerPermission = managerRes.data === 1 ||
                                      (managerRes.data && managerRes.data.is_manager === 1);
          this.showAiQualityTab = hasOutboundPermission && hasManagerPermission;
          console.log('AI质检TAB显示状态:', this.showAiQualityTab);
        } else {
          this.showAiQualityTab = false;
        }
      } catch (error) {
        this.showAiQualityTab = false;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.outbount {
  background: #f8faff;
  padding: 28px;
  margin: -15px;
}
::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;
  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }
  .el-dialog__body {
    padding: 0 12px;
  }
}
.mr10 {
  margin-right: 10px;
}
.ml10 {
  margin-left: 10px;
}
.mr60 {
  margin-right: 60px;
}
.mb12 {
  margin-bottom: 12px;
}
.w220 {
  width: 260px;
}
.w20 {
  width: 60px;
}
::v-deep .w20.el-input .el-input-group__append {
  padding: 0 5px;
}
::v-deep .w20.el-input .el-input__inner {
  padding: 0 5px;
}
.outbount_search {
  padding: 20px;
  background: #fff;
}
.outbount_top {
  width: 1200px;
  flex-wrap: wrap;
  .outbount_top_item {
    .task_name {
      color: #2e3c4e;
      font-size: 14px;
    }
    .select_name {
      width: 260px;
    }
  }
}
.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}
#musicMp3 {
  position: absolute;
  left: -200%;
  top: -200%;
  width: 0;
  height: 0;
}
.audio_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
  background: #409eff;
}
</style>
