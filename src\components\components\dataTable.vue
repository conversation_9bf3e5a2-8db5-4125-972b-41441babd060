<template>
  <el-table ref="table" border v-bind="$props" v-on="$listeners">
    <el-table-column
      v-if="showSelection"
      type="selection"
      width="55"
      align="center"
    ></el-table-column>
    <el-table-column
      v-if="showIndex"
      label="序号"
      type="index"
      width="55"
      align="center"
      :index="indexMethod"
    ></el-table-column>
    <el-table-column
      v-for="column of computedColumns"
      :key="column.prop + column.label"
      :label="column.label"
      :prop="column.prop"
    >
      <template slot-scope="scope">
        <slot
          :name="getSlotName(column)"
          v-bind="{
            data: data,
            row: scope.row,
            $index: scope.$index,
            column,
          }"
        >
          <render-content
            :option="{
              render: column.render,
              scope: scope,
              column: column,
            }"
          >
          </render-content>
        </slot>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
const RenderContent = {
  props: {
    option: Object,
  },
  render(h) {
    if (this.option.render) {
      return this.option.render(h, this.option.scope);
    }
    const { row, $index } = this.option.scope;
    const { column } = this.option;
    const property = column.prop;
    let value = row[property];
    if (column && column.formatter) {
      return <span>{column.formatter(row, column, value, $index)}</span>;
    }
    if (column.type === "index") {
      value = $index + 1;
    }
    if (!value && value !== 0) {
      value = "-";
    }
    return <span>{value}</span>;
  },
};
export default {
  components: {
    RenderContent,
  },
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
    showSelection: {
      type: Boolean,
      default: false,
    },
    showIndex: {
      type: Boolean,
      default: false,
    },
    indexMethod: {
      type: Function,
    },
  },
  data() {
    return {};
  },
  computed: {
    computedColumns() {
      return this.columns.filter((column) =>
        column.showIf ? column.showIf(this.data) : true
      );
    },
  },
  methods: {
    getSlotName(column) {
      if (typeof column.render === "string") {
        return column.render;
      }
      return `${column.prop}-column`;
    },
  },
};
</script>
