<template>
  <!-- 客户详情 -->
  <div class="pages">
    <div style="height: 100%;background: #fff;">
      <div style="padding: 0px 24px;" v-loading="is_detail_loading">
        <el-row style="margin-top: 24px">
          <el-col :span="16" style="border-right: 2px solid #f1f4fa; padding-right: 30px">
            <div class="left-content div row">
              <div class="avatar">
                <img :src="`https://img.tfcs.cn/backup/static/admin/customer/${c_detail.sex == 2 ? 'nv2' : 'nan2'
                                  }.png`" alt="" />
              </div>
              <div class="contentdetail div row">
                <div class="detail-box div">
                  <div class="name div row">
                    <span class="n">{{ c_detail.cname || "---" }}</span>
                  </div>
                  <div class="namebox flex-row" style="margin-top: 10px;">
                    <div class="flex-row client-phone-detail">
                      <div v-if="c_detail.mobile" style="color: #8a929f">
                        {{ c_detail.mobile}}
                        <div v-if="c_detail && c_detail.mobile_place" class="attribution">
                          {{ c_detail.mobile_place }}
                        </div>
                        <div class="name-box div row" style="margin: 0"
                          v-for="(item, index) in c_detail.subsidiary_mobile_list" :key="index">
                          <span v-if="item">{{ item}}</span>
                        </div>
                      </div>
                      <div>
                        <el-button class="b-tel" type="primary" size="small" @click="viewCustomerTel"
                          plain>查看电话</el-button>
                      </div>
                    </div>
                  </div>
                  <div class="desc-box div row">
                    <el-tooltip popper-class="tooltClass" class="item" effect="light" placement="bottom-start">
                      <div class="intention" slot="content">
                        {{
                                                c_detail.intention_community
                                                ? c_detail.intention_community
                                                : "--"
                                                }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div style="margin-top: 10px;">
                  <el-button class="levelbtn" type="success" size="medium" icon="el-icon-phone" @click="allStaffCallPhone"
                    v-if="call_crmshow">外呼</el-button>
                </div>
              </div>
            </div>
            <div class="right-content">
              <div class="CustomersThe_card">
                <rightportraits :l_list="c_detail" @getDataAgain="getDataAgain">
                </rightportraits>
              </div>
              <!-- <div class="title mt10 div blable row" style="margin-top: 20px">
                    <div class="title_con">客户标签</div>
                  </div> -->
              <div class="label-box div row" style="margin-top: 10px;flex-wrap: wrap;">
                <div style="margin-right: 15px;margin-bottom: 10px;">
                  <el-button type="primary" size="small" plain :is_type_detail="is_type_detail != 3"
                    @click="updateLabels">添加标签</el-button>
                </div>
                <div class="" v-for="(item, index) in label" :key="index">
                  <!-- <div class="labelname">{{ item.name }}</div> -->
                  <div class="lables-box div row">
                    <span class="labels-tag checked" :class="{ checked: i1.check }" v-for="(i1, i2) in item.son"
                      :key="i2">
                      {{ i1.name }}
                    </span>
                  </div>
                </div>
                <!-- <div v-if="label.length === 0">
                      <myEmpty></myEmpty>
                    </div> -->
              </div>

              <div class="title" style="margin-top: 10px">客户跟进</div>
              <div class="follow-box div row">
                <el-popover placement="top-start" width="150" trigger="click">
                  <div class="f-list">
                    <div class="f-item" v-for="item in level_list" :key="item.id" @click="onClickLevel(item)">
                      {{ item.title }}级
                    </div>
                  </div>
                  <div v-if="now_customer_level.title" slot="reference" class="follow-item" :class="{
                                      level_B: now_customer_level.title == 'B',
                                      level_C: now_customer_level.title == 'C',
                                    }">
                    {{ now_customer_level.title }}级
                  </div>
                  <div slot="reference" class="follow-await" v-else>未评级</div>
                </el-popover>
                <div class="follow-arrow">
                  <img v-show="clientFollowStatus('see_tel_num')"
                    src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
                <div class="invite" :class="{ isPositions: clientFollowStatus('see_tel_num') }">
                  <el-button type="primary" plain @click="initiateInvite">
                    邀约
                  </el-button>
                </div>
                <div class="follow-arrow">
                  <img v-show="clientFollowStatus('take_num') || c_detail.is_first_look
                                      " src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
                <div class="appointment" :class="{
                                  isPositions:
                                    c_detail.is_first_look || clientFollowStatus('take_num'),
                                }">
                  <el-button type="primary" plain @click="appointmentLook">
                    带看
                  </el-button>
                </div>
                <div class="follow-arrow">
                  <img v-show="c_detail.is_repeat_look ||
                                      clientFollowStatus('is_repeat_look')
                                      " src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
                <div class="appointment" :class="{
                                  isPositions:
                                    c_detail.is_repeat_look ||
                                    clientFollowStatus('is_repeat_look'),
                                }">
                  <el-button type="primary" plain @click="appointmentRepeatLook">
                    复看
                  </el-button>
                </div>
                <div class="follow-arrow">
                  <img v-show="c_detail.deal_user" src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"
                    alt="" />
                </div>
                <div class="appointment">
                  <el-button v-if="!c_detail.deal_user" type="primary" plain @click="onClickFollowStatus">
                    成交
                  </el-button>
                  <el-button v-else class="follow-deal-status" type="primary" plain
                    @click="onClickFollowStatus({}, 19, {},1)">
                    成交
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                  </el-button>
                </div>
              </div>
              <div class="follow-input div row" :class="{ isborder: is_view_tel_desc }">
                <div class="popover" v-show="is_view_tel_desc">
                  {{ is_view_tel_desc }}
                  <div class="triangle"></div>
                </div>
                <div class="f-l row div">
                  <el-popover placement="top" width="150" trigger="click" v-model="visiblepop" :disabled="disabledstatus">
                    <div class="f-list">
                      <div class="f-item" v-for="item in followStatusList" :key="item.id" @click="onClickStatus(item)">
                        {{ item.title }}
                      </div>
                    </div>
                    <div slot="reference" class="row align-center div">
                      <span>{{visiblepop_title }}</span>
                      <img v-if="!c_detail.deal_user" src="https://img.tfcs.cn/backup/static/admin/customer/zhankai.png"
                        alt="" />
                    </div>
                  </el-popover>
                </div>
                <div class="f-r div row">
                  <div id="MyContent" class="paperview-input-text" contenteditable="true" placeholder="请输入跟进内容（企业内公开）"
                    ref="gain"></div>
                  <el-button @click="onChangeStatus" style="margin-left: auto" :loading="is_loading" type="primary"
                    size="mini">提交</el-button>
                </div>
              </div>
              <!-- 同事列表下拉 -->
              <div class="colleague-box" v-show="showDropdown">
                <div class="colleague_content" v-for="(item, index) in departmentMember" :key="index"
                  @click="MemberClick(item)">
                  {{ item.name }}
                </div>
              </div>
              <!-- 同事按钮 -->
              <div class="Colleagues flex-row">
                <div>
                  <div class="FriendsPiece" @click.stop="addColleague">@同事</div>
                </div>
                <div>
                  <div class="addPicture">
                    <el-upload class="uploader-create" :disabled="disabled_picture" :headers="myHeader"
                      :action="picture_upurl" :on-success="(e) => UploadParamsSuccess(e)" accept=".jpg,.png"
                      :show-file-list="false" :multiple="true">
                      <span @click="addPictures($event)"><i class="el-icon-plus"></i>图片</span>
                    </el-upload>
                  </div>
                </div>
                <div class="picture_list_box" v-for="(item, index) in imgList" :key="index">
                  <img v-if="item.url" :src="item.url" class="photo-item-img" />
                  <div class="delete-picture" @click="deletePicture(index)">
                    <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                  </div>
                  <span class="uploader-actions" v-if="item.url">
                    <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                      <i class="el-icon-view"></i>
                    </span>
                  </span>
                </div>
              </div>

              <!-- <div class="title">跟进记录</div> -->
              <el-tabs v-model="recordName" class="record_content" @tab-click="recordClick">
                <el-tab-pane label="跟进" name="Follow"></el-tab-pane>
                <el-tab-pane label="带看" name="TakeLook"></el-tab-pane>
                <el-tab-pane label="维护" name="Maintain"></el-tab-pane>
                <el-tab-pane label="外呼" name="TeleRecord"></el-tab-pane>
                <el-tab-pane label="线索" name="clues"></el-tab-pane>
                <!-- <el-tab-pane label="轨迹" name="guiji"></el-tab-pane> -->
                <!-- <el-tab-pane label="电话记录" name="Phone"></el-tab-pane> -->
              </el-tabs>
              <!-- 跟进记录 -->
              <div v-show="recordName == 'Follow'" class="follow-record" v-infinite-scroll="loadMoreFollow"
                :infinite-scroll-disabled="follow_load"
                style="overflow: auto;height: 400px; max-height: 400px;margin-bottom: 116px;" @click="hideColleague">
                <el-timeline style="margin-left: 10px" v-if="follow_list.length">
                  <el-timeline-item v-for="(activity, index) in follow_list" :key="index" placement="top" color="#2D84FB">
                    <div class="agent_info flex-row align-center">
                      <div class="time">
                        {{ activity.created_at }}
                      </div>
                      <!-- <div class="img">
                          <img :src="follow.agent.head_image" alt="" />
                        </div> -->
                      <div class="agent_name"
                        v-if="activity.admin&&activity.admin.user_name&&activity.admin.department_name">
                        {{ activity.admin && activity.admin.user_name }}/{{
                                                activity.admin && activity.admin.department_name
                                                }}
                      </div>
                      <div class="agent_name" v-else>
                        {{activity.admin?activity.admin.user_name?activity.admin.user_name:activity.admin.department_name:""
                                                }}
                      </div>
                      <div class="show_is_top" v-if="activity.order == 1">
                        已置顶
                      </div>
                      <div class="follow_info_box">
                        <div class="follow_info_praise" @click="setFollowPraise(activity)">
                          <i class="el-icon-newfontic_zan"></i>
                        </div>
                        <div class="follow_info_copy" @click="onCopyValues(activity)">
                          <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                        </div>
                        <div class="follow_add_top" @click="setFollowTop(activity)">
                          <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                          <span v-if="activity.order == 0">设为置顶</span>
                          <span v-if="activity.order == 1">取消置顶</span>
                        </div>
                      </div>
                    </div>
                    <div class="f_content f_line">
                      <!-- <span 
                        v-if="activity.tracking && activity.tracking.id"
                        style="margin-right: 5px"
                      >
                        查看电话跟进：
                      </span> -->
                      <span v-html="activity.content"></span>
                      <!-- <span 
                        v-if="activity.tracking && activity.tracking.id"
                      >
                        ，标记为【{{ activity.tracking.title }}】
                      </span> -->
                    </div>
                    <!-- 跟进语音 -->
                    <voiceAudioPlayer v-if="activity.url && activity.voice_duration" :activity="activity">
                    </voiceAudioPlayer>
                    <!-- 跟进图片 -->
                    <div v-if="activity.file_path_path && activity.file_path_path.length
                                          " class="follow-picture">
                      <div class="follow-picture-box" v-for="(item, index) in activity.file_path_path" :key="index">
                        <img :src="$imageFilter(item, 'w_240')" alt="" />
                        <span class="uploader-actions" v-if="item">
                          <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                            <i class="el-icon-view"></i>
                          </span>
                        </span>
                      </div>
                    </div>
                    <AudioPlayer v-if="activity.record_url" :activity="activity" :info_id="c_detail.id"
                      :type="getClient_params.type" select="CustomerFollow"></AudioPlayer>
                    <div class="follow-praise" v-if="activity.top_list&&activity.top_list.length > 0">
                      <div class="follow-praise-box">
                        <span class="follow-praise-img">
                          <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                        </span>
                        <span class="follow-praise-separate"></span>
                        <span class="follow-praise-text">
                          <span>{{ activity.top_list.join("，") }}</span>
                        </span>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <myEmpty v-else></myEmpty>
              </div>
              <!-- 同事信息框 -->
              <div class="infoFrame" @click.stop.prevent="">
                <div class="infoFrame-box">
                  <!-- 头像框 -->
                  <div class="infoFrame_icon">
                    {{ this.colleagueDetails.headPortrait }}
                  </div>
                  <!-- 同事信息 -->
                  <div class="infoFrame-info">
                    <span>{{ this.colleagueDetails.user_name }}</span>
                    <span>{{ this.colleagueDetails.department }}</span>
                    <div class="infoFrame-phone">
                      <span>{{ this.colleagueDetails.phone }}</span>
                      <div class="numberTop flex-row align-center" @click="copyPhone">
                        <i class="el-icon-phone" ref="tel" style="font-size: 14px; color: #48ea0a" />
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 小三角 -->
                <div class="infoAngle">
                  <i class="el-icon-caret-bottom"></i>
                </div>
              </div>
              <!-- 带看记录 -->
              <div class="follow-record" v-show="recordName == 'TakeLook'" v-infinite-scroll="loadMoreTakeLook"
                style="overflow: auto;max-height: 400px;margin-bottom: 156px;height: 400px; ">
                <el-timeline v-if="TakeLookRecord_list.length" style="margin-left: 10px">
                  <el-timeline-item v-for="(TakeLook, index) in TakeLookRecord_list" :key="index" placement="top"
                    color="#2D84FB">
                    <div class="agent_info flex-row align-center">
                      <div class="time">
                        {{ TakeLook.created_at }}
                      </div>
                      <div class="agent_name"
                        v-if="TakeLook.admin&&TakeLook.admin.user_name&&TakeLook.admin.department_name">
                        {{ TakeLook.admin.user_name }}/{{
                                                TakeLook.admin.department_name
                                                }}
                      </div>
                      <div class="agent_name" v-else>
                        {{
                                                TakeLook.admin?TakeLook.admin.user_name?TakeLook.admin.user_name:TakeLook.admin.department_name:""
                                                }}
                      </div>
                    </div>
                    <div class="FollowRecord">
                      <div style="width: 100%">
                        <div class="FollowText">
                          <div class="f_content" :class="{ red: TakeLook.effect_type == 8 }">
                            <div slot="reference">
                              <div v-if="TakeLook.project_name">
                                带看项目：<span class="f-item-cont">{{ TakeLook.project_name }}</span><br><br>
                              </div>
                              <div>
                                <span>{{ TakeLook.take_date }}</span>
                                <span v-if="TakeLook.take_time == 1" style="margin-left: 5px">上午</span>
                                <span v-if="TakeLook.take_time == 2" style="margin-left: 5px">下午</span>
                                <span v-if="TakeLook.take_time == 3" style="margin-left: 5px">晚上</span>
                              </div>
                              <div>
                                <span v-if="TakeLook.community_name != '' &&
                                                                  TakeLook.community_name != undefined
                                                                  ">
                                  带看房源: {{ TakeLook.community_name }} ,
                                </span>
                                <!-- <span v-else>带看房源: -- , </span> -->
                                <span v-if="TakeLook.house_id != '' &&
                                                                  TakeLook.house_id != undefined
                                                                  ">
                                  房源编号: {{ TakeLook.house_id }}
                                </span>
                                <!-- <span v-else>房源编号: --</span> -->
                              </div>
                              <div>
                                <span v-if="TakeLook.accompany !== '' ||
                                                                  TakeLook.accompany !== undefined
                                                                  ">陪看人员: {{ TakeLook.accompany }}</span>
                                <!-- <span v-else>{{ TakeLook.accompany }}</span> -->
                              </div>
                              <div v-if="TakeLook.content != '' &&
                                                              TakeLook.content != undefined
                                                              ">
                                跟进内容: {{ TakeLook.content }}
                                <!-- <span v-if="TakeLook.content == ''">--</span> -->
                              </div>
                              <div v-if="TakeLook.file_path && TakeLook.file_path.length
                                                              " class="follow-picture">
                                <div class="follow-picture-box" v-for="(item, index) in TakeLook.file_path" :key="index">
                                  <img :src="$imageFilter(item, 'w_240')" alt="" />
                                  <span class="uploader-actions" v-if="item">
                                    <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                                      <i class="el-icon-view"></i>
                                    </span>
                                  </span>
                                </div>
                              </div>
                              <!-- <div v-if="TakeLook.take_no != '' && TakeLook.take_no != undefined">
                                  房源编码: {{ TakeLook.take_no }}
                                </div> -->
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <myEmpty v-else></myEmpty>
              </div>
              <!-- 外呼记录 -->
              <div v-show="recordName == 'TeleRecord'">
                <myTeleRecord :PhoneRecord_list="TelePhoneRecord_list" @morePhoneRecord="morePhoneRecord"></myTeleRecord>
              </div>
              <!-- 维护记录 -->
              <div v-show="recordName == 'Maintain'" @click="hideColleague">
                <myMaintain :MaintainRecord_list="MaintainRecord_list" @loadMoreMaintain="loadMoreMaintain"></myMaintain>
              </div>
              <!-- 线索记录 -->
              <div v-show="recordName == 'clues'">
                <clues :busy="is_c_loading" :timeline="behavior_list" :page="c_params.page"></clues>
              </div>
              <div v-show="recordName == 'guiji'">
                <guiji :guiji_busy="guiji_busy" :guijiInfo="guijiInfo" :ids="ids"></guiji>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="width:85%;margin: 0 auto;">
              <el-form ref="form" :model="push_form" label-width="80px" label-position="left" style="width: 300px;">
                <el-form-item label="客户姓名">
                  <div class="row input-box div">
                    <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
                  </div>
                </el-form-item>
                <el-form-item label="客户性别">
                  <el-select v-model="push_form.sex" placeholder="请选择客户性别" style="width: 220px;">
                    <el-option label="未知" value="0"></el-option>
                    <el-option label="男" value="1"></el-option>
                    <el-option label="女" value="2"></el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="客户性别">
                      <div class="row input-box div">
                        <div class="sex-box div row">
                          <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                            :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt=""
                            @click="() => {
                                push_form.sex = item.id;
                              }
                              " />
                        </div>
                      </div>
                    </el-form-item> -->
                <el-form-item label="客户类型">
                  <el-select v-model="push_form.type" placeholder="请选择客户类型" style="width: 220px;">
                    <el-option v-for="item,index in type_list" :key="index" :label="item.title"
                      :value="item.id"></el-option>

                  </el-select>
                </el-form-item>
                <el-form-item label="客户等级">
                  <el-select v-model="push_form.level_id" placeholder="请选择客户等级" style="width: 220px;">
                    <el-option v-for="item,index in level_list" :key="index" :label="item.title+'级'"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="客户来源">
                  <!-- <el-select v-model="push_form.source_id" v-if="website_id!=1" placeholder="请选择客户等级" style="width: 220px;">
                      <el-option 
                        v-for="item in source_list"
                        :key="item.id" 
                        :label="item.title" 
                        :value="item.id"> </el-option>
                    </el-select> -->
                  <el-cascader :style="{
                                        minWidth: '20px',
                                        width: '100%',
                                      }" :value="push_form.source2_id==0?push_form.source_id:push_form.source2_id" placeholder="请选择"
                    :options="source_list" @change="sourceLabel_status" :props="{
                                            label: 'title',
                                            value: 'id',
                                            children: 'children',
                                            checkStrictly: true 
                                          }">
                  </el-cascader>
                </el-form-item>
                <!-- <el-form-item label="最近使用" v-if="c_detail.is_show_city==1 && recordsdata.length">
                    <div v-for="item,index in recordsdata" :key="index">
                      <div class="recordcss">
                        <div style="color:#606266">
                          {{ item.province_name +'/'+item.city_name+'/'+item.area_name}}
                        </div>
                        <div>
                          <el-button type="primary" size="mini" @click="recordid(item.province_id,item.city_id,item.area_id)">选择</el-button>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="所在城市" v-if="c_detail.is_show_city==1">
                    <div class="block">
                      <el-cascader
                        style="width: 220px;"
                        v-model="provincesvalue"
                        clearable
                        :props="{
                          value: 'id',
                          label: 'name',
                          children: 'children',
                          emitPath: true,
                          multiple: false,
                        }"
                        :options="provincesoptions"
                        @change="provincesChange"></el-cascader>
                    </div>
                  </el-form-item> -->
                <el-form-item label="所在城市:" v-if="c_detail.is_show_city&&c_detail.is_show_city==1">
                  <div class="block" style="width: 220px;">
                    <el-popover placement="top" width="540" trigger="click">
                      <el-input placeholder="请输入" slot="reference" v-model="provincesvalueA" clearable
                        @change="provinclear"></el-input>
                      <div v-if="c_detail.is_show_city==1 && recordsdata.length" class="recordcss">
                        <!-- <div style="color:black;font-size:16px;">最近记录 ：</div> -->
                        <div v-for="item,index in recordsdata" :key="index">
                          <!-- <div class="recordcss"> -->
                          <div class="invitecss" @click="recordid(item)"
                            :class="{isPositionscss:provincesvalueA== item.province_name +'/'+item.city_name+'/'+item.area_name}">
                            {{ item.province_name +'/'+item.city_name+'/'+item.area_name}}
                          </div>
                          <!-- <div>
                                   <el-button type="primary" size="mini" @click="recordid(item)">选择</el-button>
                                 </div> -->
                          <!-- </div> -->
                        </div>
                      </div>
                      <!-- <div style="color:black;font-size:16px;">选择省/市/区 ：</div> -->
                      <el-cascader-panel ref="grouplist" v-model="provincesvalue" clearable :props="{
                                                    value: 'id',
                                                    label: 'name',
                                                    children: 'children',
                                                    emitPath: true,
                                                    multiple: false,
                                                  }" :options="provincesoptions" @change="provincesChange">
                      </el-cascader-panel>
                    </el-popover>
                  </div>
                </el-form-item>
                <el-form-item label="客户意向">
                  <div class="row input-box div">
                    <t-crm-project-select multiple allow-create default-first-option value-key="name" placeholder="请选择或输入"
                      v-model="push_form.intention_community" width="100%" />
                  </div>
                </el-form-item>
                <el-form-item label="备注" class="Customer_interest">
                  <el-input v-model="push_form.remark" type="textarea" :autosize="{ minRows: 4, maxRows: 6}"></el-input>
                </el-form-item>
                <!-- </div> -->
              </el-form>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-dialog :visible.sync="is_labels_customer" :modal="false" title="客户标签" width="660px">
        <div class="dialog_customer_label">
          <div v-for="(item, index) in labels_list" :key="item.id">
            <div class="labelname">{{ item.name }}</div>
            <div class="lables-box div row">
              <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.label" :key="i2"
                @click="checkChangeLabels(index, i2, i1.id)">
                {{ i1.name }}
              </div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="is_labels_customer = false">取 消</el-button>
          <el-button type="primary" @click="onClickLabels">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 点击成交 -->
      <el-dialog width="500px" :visible.sync="show_audit" :modal="false" title="提交审批">
        <div class="audit_form" v-if="show_audit">
          <el-form label-width="80px">
            <el-form-item label="审批类型">
              <el-select style="width: 300px" v-model="audit_form.cat_id" @change="changeAudit">
                <el-option v-for="item in auditList" :key="item.values" :value="item.values" :label="item.name"
                  :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-for="(item, index) in keyAuditList" :label="item.title" :key="index">
              <template v-if="item.show_type == '1' && item.type != 'date'">
                <template v-if="item.name=='f_project'">
                  <t-crm-project-select allow-create default-first-option v-model="audit_form[item.name]" value-key="name"
                    placeholder="请搜索选择项目" ref="auditProjectSelect" width="300px" />
                </template>

                <template v-else>
                  <el-input style="width: 300px" v-model="audit_form[item.name]" @input="inputCusName">
                    <template v-if="item.units" slot="append">{{
                                          item.units
                                          }}</template>
                  </el-input>
                </template>
              </template>
              <template v-if="item.show_type == '1' && item.type == 'date'">
                <el-date-picker v-model="audit_form[item.name]" type="date" style="width: 300px"
                  :valueFormat="item.date_format" :format="item.date_format" placeholder="选择日期">
                </el-date-picker>
              </template>
              <template v-if="item.show_type == '4'">
                <el-input style="width: 300px" type="textarea" v-model="audit_form[item.name]"></el-input>
              </template>
              <!-- 下拉选择框 -->
              <template v-if="item.show_type == '2'">
                <el-select style="width: 300px" v-model="audit_form[item.name]" @change="changeCusStatus">
                  <el-option v-for="op in item.options" :key="op.values" :label="op.name" :value="op.values">
                  </el-option>
                </el-select>
              </template>
              <template v-if="item.show_type == '3'">
                <el-select style="width: 300px" :multiple="true" clearable v-model="audit_form[item.name]">
                  <el-option v-for="op in item.options" :key="op.values" :label="op.name" :value="op.values">
                  </el-option>
                </el-select>
              </template>
            </el-form-item>
            <el-form-item label="成交跟进">
              <el-input style="width: 300px" type="textarea" v-model="audit_form.remarks"></el-input>
            </el-form-item>
            <el-form-item label="审批人" v-if="auditPersonList.length &&
                          !(audit_form.cat_id == 21 && c_detail.is_del == 2)
                          ">
              <div class="audit_person flex-row items-center">
                <template v-if="auditPersonList.length">
                  <div class="audit_person_name" v-for="item in auditPersonList" :key="item.values">
                    {{ item.user_name }}
                  </div>
                </template>
                <template v-else>
                  <template v-if="auditPersonSelect.length">
                    <div class="audit_person_name" v-for="item in auditPersonSelect" :key="item.values">
                      {{ item.name }}
                    </div>
                  </template>
                  <div class="audit_person_name audit_person_add" @click="showMemberList('spr')">
                    添加审批人
                  </div>
                </template>
              </div>
            </el-form-item>
            <el-form-item label="凭证">
              <div class="attachment">
                <el-upload multiple action="/api/common/file/upload/admin?category=6" :headers="upload_headers"
                  :limit="10" :files="attenchmentList" accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif"
                  list-type="picture-card" :on-success="(response, file, fileList) => {
                                        onUploadAttechmentSuccess({
                                          response,
                                          file,
                                          fileList,
                                        });
                                      }
                                      ">
                  <i class="el-icon-picture-outline"></i>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer-detail">
          <el-button @click="show_audit = false">取 消</el-button>
          <el-button type="primary" @click="submitAudit" :loading="is_loading">
            确 定
          </el-button>
        </span>
      </el-dialog>
      <!-- 点击查看电话模态框 -->
      <el-dialog :visible.sync="showViewPhone" width="530px" :modal="false" :show-close="close_button"
        :close-on-click-modal="false">
        <!-- 
        @close="ShowPhoneClose" -->
        <div slot="title" class="warn-title">
          <div class="flex-row">
            <div>
              <img src="https://img.tfcs.cn/backup/static/admin/default_person.png" alt="" />
            </div>
            <div style="margin-left: 15px; cursor: pointer">
              <el-popover placement="bottom-start" width="280" trigger="hover" :content="(this.behavior_list.length && this.behavior_list[0].content) ||
                              this.c_detail.remark
                              ">
                <span class="flex-1" slot="reference">{{
                                  this.c_detail.cname
                                  }}</span>
              </el-popover>
              <i class="el-icon-info"></i>
            </div>
          </div>
        </div>
        <div class="flex-box">
          <div class="flex-row itemCenter">
            <div class="flex-row">
              <div>
                <div class="flex-row all-phone" v-for="(item, index) in allChangeMobile" :key="index">
                  <div @click="copyAllPhone($event, item)" style="font-weight: 700; color: red;margin-top: 3px;">
                    {{ nowDialData.type == 2?item.replace(/^(.{3}).*(.{3})$/, "$1*****$2"):item.substring(0, 3) + " " +
                                        item.substring(3, 7) + " " + item.substring(7) }}
                  </div>
                  <div class="Belonging" v-if="c_detail.mobile_place&&item==c_detail.mobile">
                    {{ c_detail.mobile_place }}
                  </div>
                  <div class="Belonging" v-if="!c_detail.mobile_place&&showViewPhone&&item==c_detail.mobile">
                    {{ Viewshowphone }}
                  </div>
                  <div class="callBtn" v-if="c_detail.mobile&&item==c_detail.mobile">
                    <el-button size="mini" type="warning" v-if="call_crmshow" @click="showCallPhone(item)"
                      style="margin-left:  11px;">
                      外呼
                    </el-button>
                    <el-select v-model="formInline.region" placeholder="接通状态" size="mini" clearable
                      style="margin-left:  25px;">
                      <el-option label="已接通" value="1"></el-option>
                      <el-option label="未接通" value="0"></el-option>
                    </el-select>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div class="customer-status-box" v-if="is_deal">
            <div style="color: #666; font-size: 16px; margin-bottom: 20px">
              客户状态
            </div>
            <el-radio-group v-model="follow_params1.tracking_id" size="mini">
              <el-radio v-for="item in tel_status_list" :key="item.id" :label="item.id" border>
                {{ item.title }}
              </el-radio>
            </el-radio-group>
          </div>
          <div class="msg" style="margin: 20px 0">
            <el-input :rows="3" v-model="follow_params1.content" type="textarea" placeholder="请输入跟进内容（企业内公开）"></el-input>
          </div>
          <div class="footer flex-row align-center j-center">
            <el-button type="primary" @click="onChangeStatus1" :loading="is_loading" style="width: 479px">确定</el-button>
          </div>
        </div>
      </el-dialog>
      <el-dialog width="330px" title="智能手机" custom-class="dialog" :modal="false" :visible.sync="showPhone">
        <myCallPhone v-if="showPhone" :autoPhonenNumber="autoPhonenNumber" :concealPhoneNumber="concealPhoneNumber"
          :CallPhoneOwner="CallPhoneOwner" @phoneClose="closeCallPhone" @getCallId="getCallId"
          :phonePlace="c_detail.mobile_place? c_detail.mobile_place : Viewshowphone"
          :sitelinedata="sitelinedata"></myCallPhone>
      </el-dialog>
      <el-dialog width="330px" title="智能手机" custom-class="dialog" :modal="false" :visible.sync="showAllStaffPhone">
        <allStaffCallPhones v-if="showAllStaffPhone" :autoPhonenNumber="autoPhonenNumber"
          :concealPhoneNumber="concealPhoneNumber" :CallPhoneOwner="CallPhoneOwner" :clientID="c_id"
          @getCallId="getCallId" @allStaffphoneClose="allStaffphoneClose"
          :phonePlace="c_detail.mobile_place? c_detail.mobile_place : Viewshowphone"
          :sitelinedata="sitelinedata"></allStaffCallPhones>
      </el-dialog>
      <el-dialog class="dialogz_in" :title="is_show_meke ? '预约带看' : '预约复看'" :modal="false"
        :visible.sync="appointmentVisible" width="500px" :close-on-click-modal="false">
        <div class="TakeLook-box flex-box">
          <div class="TakeLook-follow flex-box">
            <span class="TakeLook-title">{{
                          is_show_meke ? "带看跟进" : "复看跟进"
                          }}</span>
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" v-model="form_appoint.content"
              placeholder="请输入跟进内容(企业内公开)">
            </el-input>
          </div>
          <div class="flex-row">
            <div>
              <div class="addPicture">
                <el-upload class="uploader-create" :disabled="disabled_picture" :headers="myHeader"
                  :action="picture_upurl" :on-success="(e) => UploadSuccess(e)" accept=".jpg,.png" :show-file-list="false"
                  :multiple="true">
                  <span @click="addTake_lookPictures($event)"><i class="el-icon-plus"></i>图片</span>
                </el-upload>
              </div>
            </div>
            <div class="picture_list_box flex-row" v-for="(item, index) in takeimgList" :key="index">
              <img v-if="item.url" :src="item.url" class="photo-item-img" />
              <div class="delete-picture" @click="deletetakePicture(index)">
                <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
              </div>
              <span class="uploader-actions" v-if="item.url">
                <span class="uploader-actions-item" @click="handletakePicture(item, index)">
                  <i class="el-icon-view"></i>
                </span>
              </span>
            </div>
          </div>
          <el-form label-width="80px" :model="form_appoint" ref="form_appoint" style="margin-top: 20px;">
            <el-form-item label="预约类型">
              <el-radio-group v-model="form_appoint.type" @change="changeShowType">
                <el-radio :label="1">带看</el-radio>
                <el-radio :label="2">复看</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="带看项目">
              <tCrmProjectSelect multiple allow-create default-first-option v-model="form_appoint.project_id"
                width="100%" />
            </el-form-item>
            <el-form-item label="陪看人员">
              <!-- <el-select style="width: 340px" v-model="appointAccompany" multiple placeholder="请输入陪看人员"
                @focus.once="showAccompany">
                <el-option v-for="item in departmentMember" :key="item.values" :label="item.name" :value="item.name">
                </el-option>
              </el-select> -->
              <el-select ref="Personnel" style="width: 340px" v-model="appointAccompany" multiple placeholder="请输入陪看人员"
                @focus="showAA" @change="PersonnelChange">
                <el-option :disabled="true" v-for="list,index in datalist" :key="index" :label="list.user_name"
                  :value="list.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="is_show_meke ? '带看日期' : '复看日期'">
              <el-date-picker style="width: 340px" type="date" v-model="form_appoint.take_date" placeholder="选择日期"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="is_show_meke ? '带看时间' : '复看时间'" class="appointment-time">
              <div class="time-box">
                <div class="time-boxList" :class="{ 'time-active': item.id == form_appoint.take_time }"
                  v-for="(item, index) in timeValue" :key="index" @click="appointmentTime(item)">
                  <span>{{ item.value }}</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item :label="is_show_meke ? '带看单号' : '复看单号'">
              <el-input v-model="form_appoint.take_no" :placeholder="is_show_meke ? '请输入带看单号' : '请输入复看单号'"></el-input>
            </el-form-item>
            <!-- 未开通ERP模块的不显示 -->
            <el-form-item :label="is_show_meke ? '带看房源' : '复看房源'"
              v-if="webInfo.flm_company_id > 0 && webInfo.open_house == 1">
              <div class="flex-row TakeLook-house">
                <el-input @change="takeLookHouse" v-model="form_appoint.community_name" :placeholder="is_show_meke ? '请选择带看房源' : '请选择复看房源'
                                  "></el-input>
                <div class="TakeLook-select" @click="dialogSelectHouse">选择</div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <div class="TakeLook-footer">
            <el-button @click="appointmentVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmSubmitAppoint">提 交</el-button>
          </div>
        </span>
      </el-dialog>
      <el-dialog title="选择房源" :visible.sync="showSelectHouse" :modal="false" width="950px">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="bottom-border div row">
            <span class="text">房源类型：</span>
            <myLabel labelKey="title" :arr="houseType_list" @onClick="onClickType($event, 1)" :is_show="superlists">
            </myLabel>
          </div>
          <div class="bottom-border div row" style="padding-top: 24px">
            <span class="text">搜索小区：</span>
            <el-input width="200" v-model="form_houseType.keyword" placeholder="请输入小区名称" @blur="seachHouseName">
              <el-button slot="append" icon="el-icon-search"></el-button>
            </el-input>
          </div>
          <div class="bottom-border div row" style="padding-top: 24px">
            <span class="text">房源编号：</span>
            <el-input width="200" v-model="form_houseType.hid" placeholder="请输入房源编号" @blur="seachHouseName">
            </el-input>
          </div>
          <myTable :table-list="appointTabel_list" :header="table_header" :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row :row-style="$TableRowStyle"></myTable>
          <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
            :total="appointTabel_total" :page-size="form_houseType.per_page" :current-page="form_houseType.page"
            @current-change="customerPageChange">
          </el-pagination>
        </div>
        <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="showSelectHouse = false">取 消</el-button>
        <el-button type="primary" @click="showSelectHouse = false">确 定</el-button>
      </span> -->
      </el-dialog>
      <el-dialog :visible.sync="show_add_member" width="400px" title="请选择陪看人员" append-to-body>
        <div style="color: #e6a23c;"> <i class="el-icon-search"></i>可直接搜索成员或手机号</div>
        <el-input placeholder="请输入成员名字或手机号" style="width: 250px;" v-model="input2">
          <el-button slot="append" icon="el-icon-search" @click="userSearch"></el-button>
        </el-input>
        <div style="margin-top: 20px;">
          <multipleTree v-if="show_add_memberA" :list="serverData" :defaultValue="selectedIds"
            @onClickItem="selecetedMember" :defaultExpandAll="false" ref="memberList">
          </multipleTree>
          <!-- <el-tree :data="departmentMemberA" :props="defaultProps" @node-click="handleNodeClick"></el-tree> -->
          <div style="margin-top: 20px; justify-content: space-around" class="footer flex-row align-center">
            <el-button type="text" @click="show_add_member = false">取消</el-button>
            <el-button type="primary" @click="selectMemberOk">确定</el-button>
          </div>
        </div>
      </el-dialog>
      <!-- 查看已上传图片模态框 -->
      <div class="mask1" v-if="show_dialog_pictures" @click="show_dialog_pictures = false">
        <div class="preview_img" @click.prevent.stop="() => { }">
          <img id="preImg" :src="dialog_pictures_src" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
  import rightportraits from "./rightportraits.vue";
  import clues from "./clues";
  // import we_work from './we_work'
  import guiji from './guiji'
  // import goutong from './goutong'
  // import myForm from "./components/customer_form";
  // import loadMore from "@/components/components/loadMore.vue";
  // import mySelect from "./my_select";
  import myEmpty from "@/components/components/my_empty.vue";
  // import memberList from './memberList.vue'
  import config from "@/utils/config.js";
  import { getTime } from "@/utils/tools.js";
  import { Loading } from "element-ui";
  import tCrmProjectSelect from "@/components/tplus/tSelect/tCrmProjectSelect.vue";
  import myCallPhone from "@/components/navMain/crm/components/myCallPhone";
  import allStaffCallPhones from "@/components/navMain/crm/components/allStaffCallPhones";
  import AudioPlayer from "@/components/components/audioPlayer.vue";
  import voiceAudioPlayer from "@/components/components/voiceAudioPlayer.vue"
  import myLabel from "@/components/navMain/crm/components/my_label";
  import myTable from "@/components/components/my_table";
  import myTeleRecord from "@/components/navMain/crm/components/my_teleRecordDrawer";
  import myMaintain from "@/components/navMain/crm/components/myDrawer_details";
  // import smsReminder from "@/components/navMain/crm/components/smsReminder";
  import multipleTree from "../my-detailTree.vue"
  export default {
    name: "crm_customer_detail",
    components: {
      rightportraits,
      clues,
      // myForm,
      // loadMore,
      // mySelect,
      myEmpty,
      // memberList,
      // we_work,
      guiji,
      // goutong,
      myCallPhone,
      AudioPlayer,
      myLabel,
      myTable,
      allStaffCallPhones,
      myTeleRecord,
      voiceAudioPlayer,
      myMaintain,
      // smsReminder,
      multipleTree,
      tCrmProjectSelect
    },
    props: {
      ids: {
        type: Number,
        default: () =>"",
      },
      type:{
        type:String,
        default:()=>"",
      },
      // types:{
      //   type:Number,
      //   default:()=>"",
      // }
    },
    data() {
      return {
        show_add_member:false,//选择带看人模态框
        show_add_memberA:false,
        selectedIds: [], //默认勾选和展开的节点的 key 的数组
        defaultProps: {
          children: "subs",
          label: "name",
        },
        datalist: [], // 全部部门人员
        alllist:[],
        input2:"",
        userparams:{
          user_name:""
        },
        push_type:"",
        ClaimCustomers:false,
        Claim:"",
        label:[],
        superlists: 1,
        c_id: "", // 客户id
        c_detail: {}, //客户详情
        close_button: false,
        showPhoneName: "", // 存储客户姓名
        tabs: [
          { id: 1, label: "用户画像", name: "portrait", title: "用户画像" },
          { id: 2, label: "客户线索", name: "clues", title: "客户线索" },
          { id: 3, label: "企业微信", name: "we_work", title: "企业微信" },
          { id: 4, label: "客户轨迹", name: "guiji", title: "客户轨迹" },
  
        ],
        type_list: [],
        is_tabs: "portrait",
        level_list: [],
        status_list: [],
        followStatusList: [], // 跟进类型列表
        f_params: {
          client_id: "",
          page: 1,
          total: 0,
          per_page: 10,
        },
        c_params: {
          client_id: "",
          page: 1,
          total: 0,
          per_page: 10,
        },
        guiji_params: {
          page: 1,
          id: "",
          total: 0,
          per_page: 10,
        },
        follow_list: [],
        follow_load: true,
        is_push_customer: false,
        push_form: {
          cname: "",
          source_id: '',
          source2_id:'',
          level_id: 1,
          type: 1,
          sex: 1,
          subsidiary_mobile: "",
          intention_community: "",
          // intention_street: "",
          remark: "",
        },
        other_mobile: [],
        client_field: {
          // 获取客户字段
          type: 2,
        },
        n_client_field: {},
        n_company_field: {},
        behavior_list: [],
        is_transfer_customer: false,
        admin_params: {
          page: 1,
          per_page: 10,
          user_name: "",
          total: 0,
        },
        admin_list: [],
        is_remind_customer: false,
        remind_form: {
          id: "",
          // content: "",
          remind_time: "",
          type: 1,
        },
        date_range: [],
        currentDate: "",
        currentTimeIndex: 0,
        currentTime: "",
        timeArr: [],
        current_index: 1,
        form: {},
        form1: {},
        follow_status_list: [],
        is_labels_customer: false, // 标签弹窗
        labels_list: [], // 标签列表
        labels_choose: [],
        activeId: 0,
        is_detail_loading: false,
        follow_content: "",
        is_c_loading: true,
        // 1：可编辑客户资料
        // 2: 可查看不可编辑
        // 3:不可查看
        is_type_detail: 1,
        isDisable: false,
        // is_follow_desc: "",
        form_follow: {},
        source_list: [],
        cus_list: [
          { id: 1, name: "转交到同事" },
          { id: 2, name: "合并客户" },
          { id: 3, name: "转交到公海" },
          { id: 4, name: '绑定企业微信' },
          { id: 5, name: "一键转报备" },
        ],
        giveup_form: {
          content: "",
        },
        is_dialog_giveup: false,
        merge_form: {
          one_client_id: "",
          two_client_id: "",
          type: 1,
        },
        is_dialog_merge: false,
        client_list: [],
        client_params: {
          page: 1,
          per_page: 10,
          form: 2,
          total: 0,
        },
        sex_list: [
          { id: 1, name: "nan" },
          { id: 2, name: "nv3" },
        ],
        tracking_params: {
          type: "",
        },
        // 客户跟进状态列表
        followStatus_params: {
          type: 2,
        },
        visiblepop: false,
        visiblepop_title: "",
        follow_id: "",
        // status_audit_list: [],
        is_invalid_dialog: false,
        invalid_content: "",
        is_view_tel_desc: "",
        show_audit: false,
        auditList: [
          // {values:18,name:"成交审批"}
        ],
        audit_form: {
          cat_id: 22,
        },
        attenchmentList: [],
        show_audit_member: false,
        auditPersonList: [],
        auditPersonSelect: [],
        selectedAuditIds: [],
        // defaultProps: {
        //   children: "subs",
        //   label: "name",
        // },
        memberList: [],
        keyAuditList: [
          {values:1,name:"我司成交",}
        ],
        website_id: '',
        website_types: "", // 当前客户详情type值
        upload_headers: {
          Authorization: config.TOKEN,
        },
        guijiInfo: [],
        guiji_busy: true,
        is_qw_dialog: false,
        bind_qw: {
          client_id: "",
          openid: "",
        },
        qw_tableData: [],
        is_qw_loading: false,
        qw_params: {
          page: 1,
          per_page: 10,
          keywords: "",
          total: 0,
          type: 2,
        },
        show_baobei_dia: false,
        projectId: [],
        project_list: [],
        webInfo: {},
        visit_time: '',//一键报备到方时间
        remark: '',  //一键报备备注 
        module_msg: "",// 报备弹框信息
        followList_height: "400",
        showViewPhone: false,
        changeMobile: "", // 处理手机号前三后四
        allChangeMobile: [], // 全部手机号
        formInline: {
          region: '1',
        },//电话接通状态
        // Statusdisplay:1,//切换显示电话接通状态
        showPhone: false, // 控制手机拨打电话组件显示
        showAllStaffPhone: false, // 控制全员隐藏号码可拨打电话组件
        autoPhonenNumber: "", // 查看电话-外呼自动填入的手机号
        concealPhoneNumber: "", // 查看电话-隐号手机号
        follow_params1: {
          type: 1,
          content: "",
          tracking_id: "", // 跟进状态
        },
        call_id: [], // 存储外呼成功，跟进记录call_phone_id参数的值
        showDropdown: false,
        controlVisible: true, // 控制同事列表接口请求
        departmentMember: [],
        departmentMemberA: [],
        colleagueID: [], // 存储同事id
        follow_params: {
          content: "",
        },
        // 同事信息
        colleagueDetails: {
          headPortrait: "", // 头像
          user_name: "", // 姓名
          department: "", // 部门
          phone: "", // 手机号
        },
        // 同事信息-Plus
        colleagueInformation: {},
        appointmentVisible: false, // 预约带看模态框
        // 预约带看陪看人员
        appointAccompany: [],
        // 预约带看绑定参数
        form_appoint: {
          content: "", // 跟进内容 允许为空
          accompany: [], // 陪看人员 多个,分隔
          take_date: "", // 带看时间
          take_time: 1, // 1上午 2中午 3晚上
          take_no: "", // 带看单号 允许为空
          client_id: "", // 客户id
          house_id: "", // 房源id 允许为空
          community_name: "", // 小区名称 允许为空
        },
        // 带看时间
        timeValue: [
          {
            value: '上午',
            id: 1
          },
          {
            value: '下午',
            id: 2
          },
          {
            value: '晚上',
            id: 3
          }
        ],
        label_list: [], // 客户标签列表
        provincesoptions:[],//城市选择联动
        provincesvalue:[],//城市id
        showSelectHouse: false, // 选择房源模态框
        // 房源类型
        houseType_list: [
          {
            title: "全部",
            id: 0
          },
          {
            title: "出售",
            id: 1
          },
          {
            title: "出租",
            id: 2
          },
          {
            title: "租售",
            id: 3
          },
        ],
        form_houseType: {
          client_id: "", // 客户id
          trade_type: "", // 房源类型
          keyword: "", // 搜索小区名称
          hid: "", // 房源编号
          per_page: 10, // 一页多少条
          page: 1, // 当前页码
        },
        appointTabel_list: [], // 预约带看房源列表
        appointTabel_total: 0, // 预约带看房源总条数
        table_header: [
          {
            label: "房源编号",
            prop: "id",
            width: "80px"
          },
          {
            label: "房源名称",
            prop: "community_name",
          },
          {
            label: "房源信息",
            prop: "title",
            width: "350px"
          },
          {
            label: "房源类型",
            prop: "trade_type",
          },
          {
            label: "操作",
            fixed: "right",
            width: "80px",
            render: (h, data) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.onClickDetail(data.row);
                    }}
                  >
                    选择
                  </el-link>
                </div>
              );
            },
          },
        ],
        recordName: "Follow",
        // 预约带看列表请求参数
        TakeLook_list_params: {
          page: 1,
          per_page: 10,
          client_id: "",
        },
        TakeLookRecord_list: [], // 预约带看列表内容
        // 上传图片表单参数
        uploadPicture: {},
        // 上传图片列表
        imgList: [],
        takeimgList:[],
        show_take_pictures:false,//查看已上传的图片
        dialog_take_src:"",//查看已上传的图片的src
        picture_upurl: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
        disabled_picture: false, // 是否禁用上传图片
        show_dialog_pictures: false, // 查看已上传的图片
        dialog_pictures_src: '', // 查看已上传图片的src
        is_loading: false, // loading加载动画
        is_effective: 1, // 查看电话-有效客户标记
        // 查看电话跟进客户状态列表
        telFollowStatus: {
          type: 2,
        },
        tel_status_list: [], // 状态列表
        is_show_Controls: false, // 控制是否显示客户操作
        selfID: "", // 自己当前的用户id
        AdminData: {}, // 自己当前的信息
        now_haveMobile_list: [], // 已拥有的手机号
        TelePhoneRecord_list: [], // 电话记录列表
        // 电话记录接口请求参数
        TelePhone_list_params: {
          page: 1, // 当前页
          per_page: 10, // 每页多少条
          client_id: "", // 客户id
          status: 19, // status固定为19，外呼拨打记录
        },
        TelePhone_load: true, // 控制电话记录更多加载
        now_customer_level: {}, // 当前客户等级信息
        is_show_meke: true, // 控制预约带看文字样式
        inVainID: "", // 无效id容器
        effectiveID: "", // 有效客户id容器
        PutOffID: "", // 暂缓客户id容器
        // 客户详情请求参数
        getClient_params: {
          type: "", // 1: 公海 2: 私客
        },
        nowDialData: "", // 当前查看电话权限数据
        // 客户维护记录接口请求参数
        Maintain_list_params: {
          client_id: "", // 客户id
          page: 1, //页码
          per_page: 10, // 每页多少条
        },
        MaintainRecord_list: [], // 维护记录列表
        Maintain_load: false, // 控制维护记录加载
        titleName: 'first', // 当前添加提醒绑定tabs
        buttonDisabled: false, // 短信提醒客户按钮状态
        tel_follow_id: 0,
        IntenOptions: [], // 客户意向检索容器
        inten_loading: false, // 客户意向loading加载
        is_deal: true,
        shownum: false,
        has_dial: false, // 用于判断外呼权限
        call_crmshow:false,//判断外呼按钮是否显示
        recordsdata:[],//省市区最近记录
        source_idvalue:"",
        provincesvalueA:"",
        project_id: [],  // 项目id
        sitelinedata:[],//站点绑定线路 
      };
    },
    created() {
      let website_ids = this.$route.query.website_id;
      // console.log(website_ids, "website_ids")
      if (website_ids == 626 || website_ids == 176) {
        // this.tabs.pop(); // 隐藏沟通记录模块
        this.tabs.push({ id: 5, label: "沟通记录", name: "goutong", title: "沟通记录" },)
      }
    },
    watch: {
      admin_list: {
        handler(newVal) {
          this.shownum = newVal.some(item => item.number);
          // console.log(newVal.some(item => item.number));
        },
        deep: true // 监听对象内部属性的变化
      },
      // 监听showDropdown,来触发下拉列表
      showDropdown(value) {
        if (value) {
          document.body.addEventListener('click', () => {
            this.showDropdown = false
          })
        } else {
          document.body.addEventListener('click', () => { })
        }
      },
      ids: {
        handler(newValue, oldValue) {
        if(newValue!=oldValue){
          this.c_id = newValue;
          this.f_params.page = 1
          this.Maintain_list_params.page = 1
          this.TakeLook_list_params.page = 1
          this.TelePhone_list_params.page = 1
          this.c_params.page = 1
          this.behavior_list = [];
          this.recordName = "Follow"
          const myContent = document.getElementById("MyContent");
          myContent.innerHTML = "";
          this.TakeLookRecord_list = []
          this.getDetail() 
        }
        },
      },
      // types: {
      //   handler(newValue, oldValue) {
      //   if(newValue!=oldValue){
      //     this.push_type = newValue;
      //     this.getDetail() 
      //   }
      //   },
      // },
    },
    mounted() {
      document.getElementById("MyContent").addEventListener('input', this.ChangeMyContent);
      if (this.$route.query.website_id) {
        this.website_id = this.$route.query.website_id
      }
      if (this.type) {
        this.website_types = this.type;
      }
      if (this.$route.query.tel_follow_id) {
        this.tel_follow_id = this.$route.query.tel_follow_id;
      }
      switch (this.type) {
        case "my":
          this.is_type_detail = 1;
          this.tracking_params.type = 2;
          this.getClient_params.type = 2; // 2: 私客
          break;
        case "all":
          this.isDisable = true;
          break;
        case "seas":
          this.is_type_detail = 3;
          this.tracking_params.type = 1;
          this.getClient_params.type = 1; // 1: 公海
          break;
  
        default:
          break;
      }
      this.c_id = this.ids;
      // this.push_type = this.types
      this.f_params.client_id = this.c_id;
      this.c_params.client_id = this.c_id;
      // this.getClientField();
    
      this.getDetail();
      this.getSitelinedata()
      this.getFollowStatusList();
      this.getDateArray();
      // this.getStatus();
      // this.getFollowData(); // 维护记录
      // this.getAdmin();
      this.timeSlot(60);
      // this.getFollow();
      this.getTypelist();
      this.getWebInfo();
      this.getLabelList();
      this.call_crmshowA()
      // this.onDialogUpdate() 
  
    },
    computed: {
      // 获取请求头
      myHeader() {
        return {
          // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
          Authorization: config.TOKEN,
        };
      },
      shouldRender() {
        return this.showExamineStatus();
      },
      disabledstatus(){
        return !!this.c_detail?.deal_user
      }
    },
    beforeDestroy() {
      document.removeEventListener('input', this.ChangeMyContent);
      this.$store.state.allowUpdate = true; // 控制切换路由页面是否刷新参数
    },
    filters: {
      // 将手机号更改为隐号
      mobileFilter(val) {
        let reg = /^(.{3}).*(.{3})$/;
        return val.replace(reg, "$1*****$2");
      },
      // 跟进权限判断更改为全号、隐号
      // cellPhoneDispose(value) {
        // value = value.substring(0, 3) + " " + value.substring(3, 7) + " " + value.substring(7);
        // return value;
        // 1: 全号 2: 隐号
        // if(this.nowDialData.type == 1) {
        //   value = value.substring(0, 3) + " " + value.substring(3, 7) + " " + value.substring(7);
        //   return value;
        // } else if(this.nowDialData.type == 2) {
        //   return value.replace(/^(.{3}).*(.{3})$/, "$1*****$2");
        // }
      // }
    },
    methods: {
      getDataAgain() {
        this.getDetail();
        this.Maintain_list_params.page = 1
        this.$emit("getDataListA",{})
        this.getMaintainRecord(); // 维护记录
      },
      // resetTabActivePosition($el) {
      //   setTimeout(() => {
      //     const activeEl = $el.querySelector('.el-tabs__item.is-active');
      //     const lineEl = $el.querySelector('.el-tabs__active-bar');
      //     const style = getComputedStyle(activeEl);
      //     const pl = style.paddingLeft.match(/\d+/)[0] * 1;
      //     const pr = style.paddingRight.match(/\d+/)[0] * 1;
      //     const w = style.width.match(/\d+/)[0] * 1;
      //     lineEl.style.transform = 'translateX(' + (activeEl.offsetLeft + pl) + 'px)';
      //     lineEl.style.width = w - pl - pr + 'px';
      //     // console.log(lineEl.style.width, '  lineEl.style.width ')
      //   }, 500);
      // },
      // 获取部门列表
      async getDepartment() {
        let res = await this.$http.getCrmDepartmentList().catch((err) => {
          console.log(err);
        });
        if (res.status == 200) {
          this.memberList = this.reFormData(res.data);
        }
      },
      //获取站点配置线路
      getSitelinedata() {
        //获取站点绑定的线路
        this.$http.getsiteline().then((res) => {
           if (res.status == 200) {
             this.sitelinedata = res.data.list
             // console.log(res.data.list);
           } 
         });
      },
      // changeTabs(e) {
      //   // if (e.name == "guiji") {
      //   //   this.getGuijinInfo()
      //   // }
      //   e.name == 'guiji' && this.getGuijiInfo()
      //   e.name == "clues" && this.getbehaviorData()
      //   // console.log(e);
      // },
      // 获取轨迹
      getGuijiInfo() {
        if (this.guiji_params.page == 1) {
          this.guijiInfo = []
        }
        this.$http.getGuijinInfo(this.guiji_params).then((res) => {
          if (res.status === 200) {
            this.guijiInfo = this.guijiInfo.concat(res.data.list);
            if (res.data.list.length < this.guiji_params.per_page) {
              this.guiji_busy = true
            } else {
              this.guiji_busy = false
            }
          }
        });
      },
      loadmoreGuiji() {
        if (this.guiji_busy) return
        this.guiji_params.page++
        this.getGuijiInfo()
      },
      // 获取线索
      getbehaviorData() {
        if (this.c_params.page === 1) {
          this.behavior_list = [];
        }
        this.$http
          .informationcustomerleads({ params: this.c_params })
          .then((res) => {
            if (res.status === 200) {      
              this.behavior_list = this.behavior_list.concat(res.data.data);
              console.log(this.behavior_list ,"this.behavior_list ");
              if (res.data.data.length < this.c_params.per_page) {
                this.is_c_loading = true;
              } else {
                this.is_c_loading = false;
              }
            }
          });
      },
      loadmoreLog() {
        if (this.is_c_loading) {
          return;
        }
        this.c_params.page++;
        this.getbehaviorData();
      },
      reFormData(data, line = "") {
        data.map((item) => {
          item.pArr = `${line ? line + "," : ""}${item.pid}`;
          if (item.subs && item.subs instanceof Array && item.subs.length) {
            let nameLine = `${line ? line + "," : ""}${item.pid}`;
            this.reFormData(item.subs, nameLine);
          }
        });
        return data;
        // for (let index = 0; index < data.length; index++) {
        //   this.pidArr.push()
  
        // }
      },
  
      showMemberList() {
        this.show_audit_member = true
      },
      sortChangeData(column) {
        // console.log(column);
        // console.log(column.column.label,column,"参数");
        // (1:客户等级排序,2:跟进时长排序,3:线索时间,4:创建时间,5:更新时间)
        if (column) {
          // 判断升序
          if (column.order === "ascending") {
            this.admin_params.sort = 1;
          } else if (column.order === "descending") {
            // 判断倒序
            this.admin_params.sort = 2;
          } else {
            // // 默认
            // // this.params.c_type1 = 0;
            delete this.admin_params.sort;
            // delete this.params.c_type3_sort;
          }
          this.admin_params.page = 1
          // this.getAdmin();
        }
      },
         //判断外呼按钮是否显示
         call_crmshowA(){
        // console.log(this.c_id,"id");
        this.$http.informationoutbound().then((res)=>{
          // console.log(res.data);
          if(res.status==200){
            if(res.data==0){
              this.call_crmshow = false
            }else{
              this.call_crmshow = true
            }
  
          }
        })
      },
  
  
      getTypelist() {
        this.$http.getCrmCustomerTypeDataNopage().then((res) => {
          if (res.status === 200) {
            this.type_list = res.data;
          }
        });
      },
      getClientData() {
        this.$http.getDetailClient({ params: this.client_params }).then((res) => {
          if (res.status === 200) {
            this.client_list = res.data.data;
            this.client_params.total = res.data.total;
          }
        });
      },
      onPageChangeQw(e) {
        this.client_params.page = e;
        this.getClientData();
      },
      onPageChangeWx(e) {
        this.qw_params.page = e;
        this.getWxworkData();
      },
      checkChangeLabels(index0, index) {
        let that = this;
        that.labels_list[index0].label[index].check = !that.labels_list[index0]
          .label[index].check;
      },
      updateLabels() {
  
        this.is_labels_customer = true;
        this.getLabels();
      },
      //客户来源
      sourceLabel_status(e){
        if(e.length>1){
          this.push_form.source2_id = e[1]
          this.push_form.source_id = e[0]
        }else{
          this.push_form.source_id = e[0]
          this.push_form.source2_id = 0
        }
      },
      onClickLabels() {
        let labels_choose = []
        this.labels_list.map(item => {
          if (item.label && item.label.length) {
            item.label.map(i => {
              if (i.check) {
                labels_choose.push(i.id)
              }
            })
          }
        })
        let form = {
          client_id: this.c_id,
          label: labels_choose.join(","),
        };
        this.$http.informationlable(form).then((res) => {
          if (res.status === 200) {
            this.is_labels_customer = false;
            this.getDetail();
            this.$emit("getDataListA",{})
            this.$nextTick(() => {
              // this.followList_height = +this.$refs.portrait.$el.offsetHeight - 30
            })
            setTimeout(() => {
              this.f_params.page = 1;
              this.getFollowData();
            }, 200);
  
  
          }
        });
      },
      getSourceData() {
        this.$http.listcustomersourcenew().then((res) => {
          if (res.status === 200) {
            this.source_list = res.data;
            this.source_list.map((item)=>{
              if(item.children==0){
                delete item.children
              }
            })
          }
        });
      },
      getLabels() {
        this.labels_list = [];
        this.$http.getLabelGroupNoPageNew().then((res) => {
          if (res.status === 200) {
            if (res.data.qiwei_tag && res.data.qiwei_tag.length) {
              res.data.qiwei_tag.map(item => {
                item.label = item.taggroup
                delete item.taggroup
              })
            }
            let tags = [...res.data.qiwei_tag, ...res.data.system_tag]
            let checkIds = []
            if (this.c_detail.label) {
              checkIds = (this.c_detail.label + '').split(',')
            }
            tags.map((item) => {
              if (item.label.length > 0) {
                item.label.map((i1) => {
                  i1.check = false;
                  if (checkIds.includes(i1.id + '')) {
                    i1.check = true
                  }
                });
              }
            });
            this.labels_list = tags;
          }
        });
      },
      getFollow() {
        this.$http.getCrmCustomerFollowStatus().then((res) => {
          if (res.status === 200) {
            this.follow_status_list = res.data;
            res.data.map((item) => {
              if (item.is_default) {
                this.follow_id = item.id;
                this.visiblepop_title = item.title;
              }
            });
          }
        });
      },
      onAdminSearch() {
        this.admin_params.page = 1;
        // this.getAdmin();
      },
      // onPageChange(e) {
      //   this.admin_params.page = e;
      //   this.getAdmin();
      // },
      onZhuanrang(e) {
        this.$confirm(`是否将所选客户转交给【${e.user_name}】？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 点击确认调用公众号授权
            this.$http
              .setCrmCustomerZhuanrang({
                be_transfer_id: e.id,
                ids: this.c_id + "",
              })
              .then((res) => {
                if (res.status === 200) {
                  this.$message.success("操作成功");
                  this.f_params.page = 1;
                  this.is_transfer_customer = false;
                  // this.getFollowData();
                }
              });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消");
          });
      },
      getAdmin() {
        this.$http
          .getUserListNew(
            this.admin_params
          )
          .then((res) => {
            if (res.status === 200) {
              this.admin_list = res.data.data;
              this.admin_params.total = res.data.total;
            }
          });
      },
      getClientField() {
        this.$http
          .getCrmGetClientField({ params: this.client_field })
          .then((res) => {
            if (res.status === 200) {
              this.n_client_field = res.data.client;
              this.n_company_field = res.data.company;
            }
          });
      },
      getDetail() {
        this.c_detail = {}
        this.is_detail_loading = true;
        this.$http.getcustomerinfor(this.c_id).then(async (res) => {
          this.is_detail_loading = false;
          if (res.status === 200) {
            this.c_detail = res.data;
            // this.checkStatus() 
            console.log(this.c_detail,"=========================");
            this.label = this.c_detail.label_name
            if(this.c_detail.province_id){
              this.provincesvalue[0]=this.c_detail.province_id
            }
            if(this.c_detail.city_id){
              this.provincesvalue[1]=this.c_detail.city_id
            }
            if(this.c_detail.area_id){
              this.provincesvalue[2]=this.c_detail.area_id
            }
            if(this.c_detail.province&&this.c_detail.city&&this.c_detail.area){
                this.provincesvalueA = this.c_detail.province.name+"/"+this.c_detail.city.name+"/"+this.c_detail.area.name
            }
            if(this.c_detail.tracking&&this.c_detail.tracking.types==1){
              this.visiblepop_title = "有效客户"
            }else if(this.c_detail.tracking&&this.c_detail.tracking.types==2){
              this.visiblepop_title = "无效客户"
            }else if(this.c_detail.tracking&&this.c_detail.tracking.types==3){
              this.visiblepop_title = "暂缓客户"
            }else if(this.c_detail.tracking&&this.c_detail.tracking.types==4){
              this.visiblepop_title = "我司成交"
            }else {
              this.visiblepop_title = "他司成交"
            }
            this.push_form = {
              id: this.c_detail.id,
              cname: this.c_detail.cname,
              source_id: this.c_detail.source_id,
              source2_id: this.c_detail.source2_id,
              sex: this.c_detail.sex,
              level_id: this.c_detail.level_id,
              type: this.c_detail.type,
              remark: this.c_detail.remark,
              intention_community: this.c_detail.intention_community ? this.c_detail.intention_community.split(',') : [], // 客户意向
              mobile: this.c_detail.mobile,
              label: this.c_detail.label.split(","),
              province_id:this.c_detail.province_id,
              city_id:this.c_detail.city_id,
              area_id:this.c_detail.area_id,
            };
            if(this.push_form.sex==1){
              this.push_form.sex = "1"
            }else if(this.push_form.sex==2){
              this.push_form.sex = "2"
            }else{
              this.push_form.sex = "0"
            }
            if (this.push_form.type == 0) {
              this.push_form.type = ""
            }
            if(this.push_form.level_id==0){
              this.push_form.level_id = "未评级"
            }
            if(this.c_detail.is_show_city==1){
              this.Cities_provinces()
              this.Regional_records()
            }
            this.getFollowData()
            this.getMaintainRecord(); // 维护记录
            this.getbehaviorData();
            // this.getGuijiInfo();
            this.getSourceData();
            this.showPhoneName = res.data.cname; // 存储客户姓名
            // if (!this.c_detail.access_id) {
            //   this.tabs = this.tabs.filter(item => item.id != 4)
            // }
            if (this.c_detail.wxqy_id) {
              this.cus_list = this.cus_list.filter(item => item.id != 4)
            } else {
              this.tabs = this.tabs.filter(item => item.id != 3)
            }
            this.tabs = this.tabs.map((item) => {
              if (item.id == 2) {
                item.label = `${item.title}（${res.data.operation_log || 0}）`;
              }
              return item;
            });
            this.$nextTick(() => {
              // this.followList_height = +this.$refs.portrait.$el.offsetHeight - 30
            })
            this.allChangeMobile = []
            let allChangeMobile = []
            this.changeMobile = JSON.parse(JSON.stringify(this.c_detail.mobile));
            if (this.c_detail.subsidiary_mobile != "" && this.c_detail.subsidiary_mobile != undefined) {
              allChangeMobile = (this.changeMobile + "," + this.c_detail.subsidiary_mobile).split(",");
            } else {
              allChangeMobile = [this.changeMobile];
            }
            allChangeMobile.map(item => {
              if (item) {
                this.allChangeMobile.push(item)
              }
              return item
            })
            if (this.followStatusList && this.followStatusList.length) {
              if (!this.c_detail.tracking) {
                this.follow_id = this.followStatusList[0].id; // 获取跟进类型id
                this.visiblepop_title = this.followStatusList[0].title;
              } else {
                this.followStatusList.map((item) => {
                  if (item.title == this.c_detail.tracking.title) {
                    this.visiblepop_title = item.title;
                    this.follow_id = item.id;
                  }
                })
              }
            }
            // 如果存在手机号
            if (this.c_detail.mobile) {
              // 如果存在更多手机号
              if (this.c_detail.subsidiary_mobile) {
                this.now_haveMobile_list = (this.c_detail.mobile + "," + this.c_detail.subsidiary_mobile).split(",")
              } else {
                this.now_haveMobile_list = [this.c_detail.mobile];
              }
            } else {
              this.addDomain();
            }
            // if (this.tel_follow_id > 0) {
            //   this.showViewPhone = true; // 显示查看电话模态框
            //   this.getTelFollowStatus();
            // } else {
            let result = await this.$http.getForceFollow().catch(() => { })
            if (result.status == 200) {
              if (result.data && result.data.id > 0 && result.data.client_id == this.c_id) {
                this.close_buttonshow()
                this.tel_follow_id = result.data.id
                this.showViewPhone = true; // 显示查看电话模态框
                this.getbehaviorData()
                this.getTelFollowStatus();
              } else if (result.data && result.data.id > 0 && result.data.client_id != this.c_id) {
                this.$confirm("您有未跟进的客户确认去跟进吗？", "提示").then(() => { 
                  let url = `/crm_customer_detail?id=${result.data.client_id}&type=${this.website_types}&tel_follow_id=${result.data.id}`;
                this.$goPath(url);
                })
              }
            // }
            // if (result.status == 200 && result.data && result.data.id > 0) {
            //   this.$confirm("您有未跟进的客户确认去跟进吗？", "提示").then(() => {
            //     let url = `/crm_customer_detail?id=${result.data.client_id}&type=${this.website_types}&tel_follow_id=${result.data.id}`;
            //     this.$goPath(url);
            //   })
              // }
            }
            // this.getStatus(); // 获取客户状态列表
            this.getOneselfList(); // 获取是否有权限查看客户操作
            if (!this.level_list.length) {
              this.getLevel(); // 获取客户等级列表
            } else {
              this.getNowLevel(); // 从客户等级列表获取当前客户的等级
            }
            // console.log(this.c_detail);
            // let form = res.data;
            // this.n_company_field.map((item) => {
            //   form.company_column_field.map((item1) => {
            //     if (item.id === item1.template_id) {
            //       form[item.colum_key] = item1.value;
            //     }
            //   });
            // });
            // this.n_client_field.map((item) => {
            //   form.client_column_field.map((item1) => {
            //     if (item.id === item1.template_id) {
            //       form[item.colum_key] = item1.value;
            //     }
            //   });
            // });
            // this.form = Object.assign({}, form);
            // this.form1 = Object.assign({}, form);
          }
        });
      },
      getLevel() {
        this.$http.getCrmCustomerLevelNopage().then((res) => {
          if (res.status === 200) {
            this.level_list = res.data;
            this.getNowLevel();
          }
        });
      },
      // 获取当前客户等级
      getNowLevel() {
        this.level_list.map((item) => {
          if (item.id == this.c_detail.level_id) {
            this.now_customer_level = item; // 赋值当前客户等级
          }
        })
      },
      getStatus() {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.tracking_params })
          .then((res) => {
            if (res.status === 200) {
              this.status_list = res.data;
              let delIndex = "";
              this.status_list.map((item, index) => {
                if ((this.c_detail.tracking && Object.keys(this.c_detail.tracking)) && item.id == this.c_detail.tracking.id) {
                  delIndex = index;
                }
                if (item.title == '有效客户') {
                  item.value_name = 1
                } else if (item.title == '无效客户') {
                  item.value_name = 2
                } else if (item.title == '暂缓客户') {
                  item.value_name = 3
                } else {
                  item.value_name = 1
                }
                return item
              })
              this.status_list.splice(delIndex, 1);
            }
          });
      },
      getFollowStatusList() {
        this.$http.getCrmCustomerFollowInfo({ params: this.followStatus_params }).then((res) => {
          if (res.status === 200) {
            this.followStatusList = res.data;
            // 获取无效客户状态的id
            this.followStatusList.map((item) => {
              if (item.title == "无效客户") {
                this.inVainID = item.id;
              } else if (item.title == "有效客户") {
                this.effectiveID = item.id;
              } else if (item.title == "暂缓客户") {
                this.PutOffID = item.id;
              }
            })
          }
        })
      },
      // getStatusAudit() {
      //   this.$http
      //     .getCrmCustomerFollowInfo({ params: { type: 3 } })
      //     .then((res) => {
      //       if (res.status === 200) {
      //         this.status_audit_list = res.data;
      //       }
      //     });
      // },
      getFollowData() {
        this.f_params.client_id = this.c_id; // 赋值客户id
        this.follow_load = true;
        if (this.f_params.page === 1) {
          this.follow_list = [];
        }
        // 跟进详情
        this.$http
          .informationfollowrecord({ params: this.f_params })
          .then((res) => {
            if (res.status === 200) {
              console.log(res.data.data);
              this.follow_list = this.follow_list.concat(res.data.data);
              console.log(this.follow_list);
              this.follow_list.map(item => {
                item.isPlaying = false;
              })
              this.f_params.total = res.data.total;
              if (res.data.data.length < (this.f_params.per_page || 10)) {
                this.follow_load = true;
              } else {
                this.follow_load = false;
              }
              this.$nextTick(() => {
                let user_follow = document.getElementsByClassName("user_follow");
                for (let i = 0; i < user_follow.length; i++) {
                  user_follow[i].style.cursor = "pointer";
                  user_follow[i].onclick = (e) => {
                    e.stopPropagation()
                    e.preventDefault()
                    this.colleagueOnMouseDown(e)
                  }
                }
              })
            }
          });
      },
      loadMoreFollow() {
        if (this.follow_load) {
          return;
        }
        this.f_params.page++;
        this.getFollowData();
      },
      onClickLevel(e) {
        // if (this.is_type_detail === 3) {
        //   return;
        // }
        if (e.id == this.c_detail.level_id) {
          return;
        }
        this.$confirm("是否更改客户等级", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.c_detail.level_id = e.id;
            let form = {
              id: this.c_detail.id,
              level_id: e.id,
            };
            // 点击确认调用公众号授权
            this.$http.setinformationlevel(form).then((res) => {
              if (res.status === 200) {
                this.$message.success("修改成功");
                this.f_params.page = 1;
                this.getFollowData();
                this.getDetail();
                this.$emit("getDataListA",{})
              }
            });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消");
          });
      },
      onClickStatus(e) {
        this.follow_id = e.id;
        this.visiblepop = false;
        this.visiblepop_title = e.title;
      },
      onChangeStatus() {
        this.follow_params.content = this.$refs.gain.innerText // 赋值跟进内容
        if(!this.follow_id&&this.c_detail.tracking&&this.c_detail.tracking.id){
          this.follow_id = this.c_detail.tracking.id
        }
        if (!this.follow_id) {
          this.$message.error("请选择客源状态");
          return;
        }
        if (this.follow_params.content.length < 5) {
          this.$message.error("最少输入不能小于五个文字");
          return;
        }
        let nums = []; // 跟进图片接口参数容器
        // 遍历已上传跟进图片数量
        if (this.imgList.length != 0 && this.imgList != undefined) {
          this.imgList.map((item) => {
            nums.push(item.url);
          })
          nums = nums.join(','); // 将数组转换字符串
        } else {
          nums = '';
        }
        // 每点击一次清空同事id
        this.follow_params.remind = [];
        // 将用户id传入接口参数follow_params
        this.$set(this.follow_params, 'remind', this.colleagueID.toString());
        this.is_loading = true; // 开启loading
        // 点击确认调用公众号授权
        let params = {
          type: this.follow_id,
            tracking_id: this.follow_id,
            client_id: this.c_id,
            contact_id: 0,
            content: this.follow_params.content,
            file_path: nums,
            remind: this.follow_params.remind
        }
        if(this.c_detail.deal_user||params.tracking_id==""||params.tracking_id==undefined){
          delete params.tracking_id
        }
        this.$http
          .informationordinaryfollow(params)
          .then((res) => {
            if (res.status === 200) {
              this.is_loading = false; // 关闭loading
              this.imgList = []; // 清空上传跟进图片列表
              this.colleagueID = []; // 清空同事id
              this.$message.success("操作成功");
              this.$emit("getDataListA",{})
              this.f_params.page = 1;
              this.getDetail(); // 刷新客户详情数据
              this.getFollowData();
              this.$refs.gain.innerText = "";
              // 如果查看过维护记录
              if (this.MaintainRecord_list.length) {
                this.Maintain_list_params.page = 1;
                this.getMaintainRecord(); // 获取跟进记录
              }
            } else {
              this.is_loading = false;
            }
          }).catch(() => {
            this.is_loading = false;
          })
      },
      // 组合最近7天的日期
      getDateArray() {
        this.date_range = [];
        let arr = [0, 1, 2, 3, 4, 5, 6];
        arr.map((item) => {
          this.date_range.push(this.getDay(item));
        });
        if (this.current_index < 0) {
          this.current_index = 1;
        }
        this.currentDate = this.date_range[this.current_index].fullDate;
      },
      // 获取几天前 几天后 的日期 传递正数时是几天后的 参数为整数 返回对象  年月日   月日 和周几 因为传递给后端需要年所以加了个fullDate 带年的日期
      getDay(day) {
        var today = new Date();
        var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
        today.setTime(targetday_milliseconds); //注意，这行是关键代码
  
        var tYear = today.getFullYear();
  
        var tMonth = today.getMonth();
        var tDate = today.getDate();
        let weekDay = this.doHandleWeek(today.getDay());
        tMonth = this.doHandleMonth(tMonth + 1);
        tDate = this.doHandleMonth(tDate);
  
        return {
          fullDate: tYear + "-" + tMonth + "-" + tDate,
          shortDate: tMonth + "月" + tDate + "日",
          weekDate: weekDay,
        };
      },
      // 不足十 补0
      doHandleMonth(month) {
        var m = month;
  
        if (month.toString().length == 1) {
          m = "0" + month;
        }
        return m;
      },
      // 设置星期
      doHandleWeek(num) {
        let week = "";
        switch (num) {
          case 0:
            week = "周日";
  
            break;
          case 1:
            week = "周一";
  
            break;
          case 2:
            week = "周二";
  
            break;
          case 3:
            week = "周三";
  
            break;
          case 4:
            week = "周四";
  
            break;
          case 5:
            week = "周五";
  
            break;
          case 6:
            week = "周六";
  
            break;
          default:
            week = "";
            break;
        }
        return week;
      },
      changeCurrentTime(index) {
        this.currentTimeIndex = index;
        this.currentTime = this.timeArr[index];
      },
      timeSlot(step) {
        var date = new Date();
        // date.setHours(0); // 时分秒设置从零点开始
        date.setHours(date.getHours());
        date.setSeconds(0);
        date.setUTCMinutes(0);
  
        var slotNum = (24 * 60) / step; // 算出多少个间隔
        for (var f = 0; f < slotNum; f++) {
          //  stepM * f = 24H*60M
          // this.arr.push(new Date(Number(date.getTime()) + Number(step*60*1000*f)))   //  标准时间数组
          var time = new Date(
            Number(date.getTime()) + Number(step * 60 * 1000 * f)
          ); // 获取：零点的时间 + 每次递增的时间
          var hour = "",
            sec = "";
          time.getHours() < 10
            ? (hour = "0" + time.getHours())
            : (hour = time.getHours()); // 获取小时
          time.getMinutes() < 10
            ? (sec = "0" + time.getMinutes())
            : (sec = time.getMinutes()); // 获取分钟
          this.timeArr.push(hour + ":" + sec);
          this.currentTime = this.timeArr[0];
        }
      },
      onChangeDate(e) {
        let index = parseInt(e.index);
        this.current_index = index;
        this.currentDate = this.date_range[index].fullDate;
      },
      changeTab(index, item) {
        // 如果选择的和当前激活的不同
        this.current_index = index;
        this.currentDate = item.fullDate;
      },
      // onClickRemind() {
      //   this.remind_form.id = this.c_id;
      //   this.remind_form.remind_time =
      //     this.currentDate + " " + this.currentTime + ":00";
      //   this.$http.setCrmCustomerRemind(this.remind_form).then((res) => {
      //     if (res.status === 200) {
      //       this.$message.success("操作成功");
      //       this.f_params.page = 1;
      //       this.getFollowData();
      //       this.is_remind_customer = false;
      //     }
      //   });
      // },
      // 添加短信提醒客户
      addSmsReminder() {
        let params = this.$refs.smsChild.smsData_params; // 赋值短信提醒客户数据
        if (params.name == "" || params.name == undefined) {
          return this.$message.warning("请输入姓名");
        }
        if (params.mobile == "" || params.mobile == undefined) {
          return this.$message.warning("请输入电话");
        }
        this.$http.addWarnFollow(params).then((res) => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            this.buttonDisabled = true; // 控制按钮样式
            const times = new Date().getTime(); // 获取当前日期的时间戳
            let buttonDisabled = JSON.parse(localStorage.getItem('buttonDisabled')); // 获取本地存储
            if (buttonDisabled != "null" && (Array.isArray(buttonDisabled) && buttonDisabled.length)) {
              buttonDisabled.push({ id: this.c_id, time: times });
            } else {
              buttonDisabled = [{ id: this.c_id, time: times }];
            }
            localStorage.setItem('buttonDisabled', JSON.stringify(buttonDisabled)); // 将时间戳保存到本地存储中
            this.is_remind_customer = false;
          }
        })
      },
      onDialogUpdate() {
        this.push_form = {
          id: this.c_detail.id,
          cname: this.c_detail.cname,
          source_id: this.c_detail.source_id,
          sex: this.c_detail.sex,
          level_id: this.c_detail.level_id,
          type: this.c_detail.type,
          remark: this.c_detail.remark,
          intention_community: this.c_detail.intention_community,
          mobile: this.c_detail.mobile,
          label: this.c_detail.label.split(","),
        };
        if(this.c_detail.intention_community && this.c_detail.intention_community.indexOf(",") >= 0) {
          this.push_form.intention_community = this.c_detail.intention_community.split(",");
        } else if(this.c_detail.intention_community != "" && this.c_detail.intention_community != undefined) {
          this.push_form.intention_community = [this.c_detail.intention_community];
        } else {
          this.push_form.intention_community = [];
        }
        this.is_push_customer = true;
      },
      //获取最近省市区的记录
      Regional_records(){
        this.$http.regionalrecords(this.c_detail.id).then((res)=>{
          if(res.status==200){
            this.recordsdata = res.data
          }
        })
      },
      Cities_provinces(){
        this.$http.cities_and_provinces().then((res)=>{
          if(res.status==200){
            // console.log(res.data,"省市区");
            this.provincesoptions = res.data
          }
        })
      },
      //省市区如果是空
      provinclear(e){
             if(!e){
              this.push_form.province_id = 0
              this.push_form.city_id = 0
              this.push_form.area_id = 0
             }
      },
      recordid(item){
        let arr = []
        arr[0] = item.province_id
        arr[1] = item.city_id
        arr[2] = item.area_id
        this.provincesvalue = arr
        this.push_form.province_id = this.provincesvalue[0]
        this.push_form.city_id = this.provincesvalue[1]
        this.push_form.area_id = this.provincesvalue[2]
        this.provincesvalueA = item.province_name+"/"+ item.city_name+"/"+item.area_name
      },
      provincesChange(e){
        let nodeinfo = this.$refs["grouplist"].getCheckedNodes()
        this.provincesvalueA = nodeinfo[0].pathLabels[0] + "/"+ nodeinfo[0].pathLabels[1]+"/"+nodeinfo[0].pathLabels[2]
        if(e.length){
          this.push_form.province_id = e[0]
          this.push_form.city_id = e[1]
          this.push_form.area_id = e[2]
        }else{
          this.push_form.province_id = 0
          this.push_form.city_id = 0
          this.push_form.area_id = 0
        }
      },
      onClickForm() {
        if (!this.push_form.cname) {
          this.$message.error("请检查客户姓名");
          return;
        }
        if (!this.push_form.sex) {
          this.$message.error("请检查客户性别");
          return;
        }
        if (!this.push_form.level_id) {
          this.$message.error("请检查客户等级");
          return;
        }
        if (!this.push_form.type) {
          this.$message.error("请检查客户类型");
          return;
        }
        if (!this.push_form.source_id) {
          this.$message.error("请检查客户来源");
          return;
        }
        if (this.push_form.label && Array.isArray(this.push_form.label)) {
          this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
        }
        if(this.push_form.level_id=="未评级"){
          this.push_form.level_id = 0
        }
        const params = {...this.push_form};
            params.intention_community = params.intention_community.length ? params.intention_community.join(",") : '';
        this.$http.renewcustomerinfor(params).then((res) => {
          this.is_push_customer = false;
          if (res.status === 200) {
            this.$message.success("操作成功");
            this.c_params.page = 1;
            this.getbehaviorData();
            this.f_params.page = 1;
            this.getFollowData();
            this.getDetail();
          }
        });
      },
      call_off(){
        // this.$emit('child-event');
      },
      onClickFollowStatus(e, id = 22, item,status) {
        if(status==1){
          if(this.c_detail.deal_user){
            return this.$message.warning("客户已是成交状态，如需更改请提交审批")
          }
        }
        // if (this.website_id == 176) {
        this.audit_form = { cat_id: id }
        if (id == 22) {
          this.audit_form.f_cj_type = 1
        }
        if (id == 19) {
          this.audit_form.f_trade_status = item.value_name
        }
        this.audit_form.f_name = this.c_detail.cname
        this.attenchmentList = []
        this.auditPersonSelect = []
        this.selectedAuditIds = []
        this.getAuditTypeList(id)
        if (!this.memberList.length) {
          this.getDepartment()
        }
        this.show_audit = true
        return
        // }
        // this.form_follow.tracking_id = item.id;
        // this.form_follow.id = this.c_id;
        // // this.form_follow.type = item.examine_type
        // if (item.examine_auth > 0) {
        //   // this.form_follow.name = this.c_detail.cname || "";
        //   // this.form_follow.mobile = this.c_detail.mobile || "";
        //   this.$goPath(
        //     `/crm_customer_audit_detail?id=${this.c_detail.id}&tracking_id=${item.id}`
        //   );
        // } else {
        //   this.$confirm("是否修改客户状态", "提示", {
        //     confirmButtonText: "确定",
        //     cancelButtonText: "取消",
        //     type: "warning",
        //   })
        //     .then(() => {
        //       this.$http
        //         .setCrmCustomerFollowStatusv1(this.form_follow)
        //         .then((res) => {
        //           if (res.status === 200) {
        //             this.$message.success("操作成功");
        //             this.getDetail();
        //           }
        //         });
        //     })
        //     .catch(() => {
        //       // 点击取消控制台打印已取消
        //       console.log("已取消");
        //     });
        // }
      },
      fastSetCustomerStatus(item) {
        // 审批无需审核
        if (this.c_detail.is_state == 2) {
          let examine = false;
          // 遍历客户状态审批范围
          this.c_detail.state_list.map((list) => {
            // 如果当前要改变的状态需要审批
            if (list == item.id) {
              examine = true;
            }
          })
          if (examine) {
            // 如果在审批范围，判断是不是客户管理员，是就无需审批，不是就需要审批
            if (this.c_detail.admin_list && this.c_detail.admin_list.length) {
              // 遍历客户管理员
              const isLargeNumber = (item) => item == this.selfID;
              let is_admins = this.c_detail.admin_list.findIndex(isLargeNumber);
              // 如果是客户管理员就无需审批，否则走审批
              if (is_admins >= 0) {
                this.$confirm("此操作将更改客户状态, 是否继续?", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                }).then(() => {
                  this.setCrmCustomerStatus(this.c_id, item.id); // 无需审批
                })
  
              } else {
                this.onClickFollowStatus({}, 19, item); // 审批
              }
            } else {
              this.onClickFollowStatus({}, 19, item); // 审批
            }
          } else {
            this.$confirm("此操作将更改客户状态, 是否继续?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              this.setCrmCustomerStatus(this.c_id, item.id); // 无需审批
            })
          }
        } else {
          this.$message.warning("当前客户状态不可以进行审批")
        }
      },
      // 更改客户状态状态审批无需审核
      setCrmCustomerStatus(id, tracking) {
        this.$http.setCrmCustomerStatus({ id: id, tracking_id: tracking }).then((res) => {
          if (res.status == 200) {
            this.$message.success("客户状态修改成功");
            this.getDetail(); // 刷新数据
          }
        })
      },
      onUploadAttechmentSuccess(options = {}) {
        let { response } = options;
        this.attenchmentList = []
        this.attenchmentList.push(response.url)
      },
  
      // 审批类型  
      getAuditTypeList(id) {
        //获取审批类型
        this.$ajax.house.getAuditTypeList(5).then(res => {
          if (res.status == 200) {
            this.auditList = res.data
            if (this.auditList.length) {
              this.getAuditPerson(this.auditList[0].values)
            }
            if (this.auditList.length && this.auditList[0].is_model == 1) {
              if (!id) {
                id = this.auditList[0].values
              }
              this.getModelList(id)
  
            }
          }
        })
      },
      getModelList(id) {
        this.$ajax.house.getModalList(id, this.c_detail.id).then(res => {
          res.data.map(item => {
            if (item.type == 'date') {
              this.audit_form[item.name] = new Date()
            }
  
          })
          this.keyAuditList = res.data;
          // 如果是变更状态审批
          if (this.keyAuditList[0].name == "f_trade_status") {
            this.followStatusList.map((item) => {
              this.keyAuditList[0].options.map((list) => {
                if (item.title.split("客户")[0] == list.name) {
                  list.id = item.id;
                }
              })
            })
          }
          // console.log(this.keyAuditList, "keyAuditList111222")
        })
      },
      changeAudit(e) {
        if (e == 18) {
          this.audit_form.f_name = this.c_detail.cname
          this.audit_form.f_cj_type = 1
          this.audit_form.f_name = this.c_detail.cname
        }
        let curr = this.auditList.find(item => item.values == e)
        if (curr && curr.is_model) {
          this.getModelList(e)
        } else {
          this.keyAuditList = []
        }
  
      },
      selecetedAuditMember(e) {
        this.selectedAuditIds = e.checkedKeys
        this.auditPersonSelect = e.checkedNodes
      },
      selectAuditOk() {
        this.audit_form.approver_uid = this.selectedAuditIds.join(",")
        // this.selecetedAuditMember = this.selectedList
        this.show_audit_member = false
      },
  
      getAuditPerson(id) {
        this.$ajax.house.getAuditPerson(id).then(res => {
          this.auditPersonList = res.data
  
        })
      },
      submitAudit() {
        this.is_loading = true;
        let params = Object.assign({}, this.audit_form)
        params.sys_hid = this.c_detail.id
        if (this.auditPersonList.length) {
          params.approver_uid = []
          this.auditPersonList.map(item => {
            params.approver_uid.push(item.id)
          })
          // params.approver_uid = approver_uid.join(",")
        } else {
          params.approver_uid = this.selectedAuditIds
        }
  
        params.attachment = this.attenchmentList
  
        for (let key in params) {
          if (Object.prototype.toString.call(params[key]) === "[object Array]") {
            if (params[key].length > 0 && typeof params[key][0] === "object") {
              params[key] = JSON.stringify(params[key]);
            } else {
              params[key] = params[key].join(",");
            }
          }
        }
              //项目名称f_project
      if(params?.f_project){
        const auditProjectSelect = this.$refs?.auditProjectSelect[0];
        const option = auditProjectSelect ? auditProjectSelect.getSelectOption() : null;
        params.f_project_id = option ? option.id : ''; 
      }

        // 如果是删除客户审批 is_del:是否可以删除(1:不可删除,2:删除(无需审批),3:删除(走审批流程))
        // 如果是变更客户状态 is_state: 1:不可修改状态, 2: 根据state_list中判断是否存在该状态类型并且不是客户管理员就不走审批否则走审批
        if (params.cat_id == 21 && this.c_detail.is_del == 2) {
          this.$http.deleteFollowClient(params.sys_hid).then(res => { // 删除无需审批
            if (res.status == 200) {
              this.is_loading = false;
              this.$message.success("删除成功");
              setTimeout(() => {
                // this.goBacks(); // 后退至上一页
                this.$goPath("/crm_customer_my_list");
              }, 1000)
            } else {
              this.is_loading = false; // 关闭loading加载
            }
          }).catch(() => {
            this.is_loading = false;
          })
        } else if (params.cat_id == 21 && this.c_detail.is_del == 1) { // 不可删除
          this.is_loading = false;
          return this.$message.warning("不可删除当前客户");
        } else if (params.cat_id == 19 && this.c_detail.is_state == 2) { // 如果审批类型为变更状态
          let selectStatus = null; // 客户审批状态id容器
          let is_examine = false; // 控制审批
          // f_trade_status : 1：有效 2： 无效 3： 暂缓
          if (params.f_trade_status == 1) {
            selectStatus = this.effectiveID; // 有效客户id
          } else if (params.f_trade_status == 2) {
            selectStatus = this.inVainID; // 无效客户id
          } else if (params.f_trade_status == 3) {
            selectStatus = this.PutOffID; // 暂缓客户id
          }
          // 判断是否需要审批
          this.c_detail.state_list.map((item) => {
            if (item == selectStatus) {
              is_examine = true;
            }
          })
          if (!is_examine) {
            // console.log("不走审核")
            this.setCrmCustomerStatus(this.c_id, selectStatus); // 无需审批变更客户状态
            this.show_audit = false; // 关闭模态框
            this.is_loading = false; // 关闭loading加载
          } else { // 判断是否是客户管理员
            let is_admins = false;
            // this.c_detail.admin_list.map((arr) => {
            //   if (arr == this.selfID) {
            //     is_admins = true;
            //   }
            // })
            // 如果是管理员，无需审批
            if (is_admins) {
              // console.log("管理员无需审批");
              this.setCrmCustomerStatus(this.c_id, selectStatus); // 无需审批变更客户状态
              this.show_audit = false; // 关闭模态框
              this.is_loading = false; // 关闭loading加载
            } else {
              // console.log("走审核")
              this.$ajax.house.addHouseAudit(params).then(res => {
                if (res.status == 200) {
                  this.$message.success(res.mesage || '提交成功')
                  this.show_audit = false;
                  this.getDetail(); // 刷新数据
                }
                this.is_loading = false; // 关闭loading加载
              })
            }
          }
        } else if (params.cat_id == 19 && this.c_detail.is_state == 1) { // 不可修改状态
          this.show_audit = false;
          return this.$message.warning("当前客户不可进行审批");
        } else {
          this.$ajax.house.addHouseAudit(params).then(res => {
            if (res.status == 200) {
              this.$message.success(res.mesage || '提交成功')
              this.show_audit = false;
              if (this.auditPersonList) {
                console.log(11111);
                this.getDetail();
              } else {
                console.log(22222);
                // this.$goPath("/crm_customer_information");
                this.$emit("handleClose",{})
              }
            }
            this.is_loading = false; // 关闭loading加载
          })
          this.is_loading = false;
        }
      },
      // 后退至上一页
      goBacks() {
        this.$router.go(-1)
      },
      // pageChange(e) {
      //   this.admin_params.page = e;
      //   this.getAdmin();
      // },
      // onChangeRemark(e) {
      //   let form = {
      //     remark: e,
      //     id: this.c_detail.id,
      //   };
      //   if (!form.remark) {
      //     this.$message.error("请检查备注内容");
      //     return;
      //   }
      //   this.$http.setCrmCustomerRemarkData(form).then((res) => {
      //     if (res.status === 200) {
      //       this.$message.success("操作成功");
      //       this.c_params.page = 1;
      //       this.getbehaviorData();
      //       this.f_params.page = 1;
      //       this.getFollowData();
      //       // 如果查看过维护记录
      //       if (this.MaintainRecord_list.length) {
      //         this.Maintain_list_params.page = 1;
      //         this.getMaintainRecord(); // 获取维护记录
      //       }
      //     }
      //   });
      // },
      removeDomain(item) {
        var index = this.other_mobile.indexOf(item);
        if (index !== -1) {
          this.other_mobile.splice(index, 1);
        }
      },
      addDomain() {
        this.other_mobile.push({
          mobile: "",
        });
      },
      onClickLevelChange(item) {
        this.push_form.level_id = item.id;
      },
      onClickTypeClient(item) {
        this.push_form.type = item.id;
      },
      //点击查看电话
      viewCustomerTel() {
        this.close_buttonshow()
        // if (!this.has_roles) {
        //   this.$message.warning("暂无权限")
        //   return
        // }
        if(!this.c_detail.mobile_place){
        this.$http.lookphoneLocation(this.c_detail.id).then((res) => {
          if (res.status == 200) {
            this.Viewshowphone = res.data
          }
        });
      }
        this.$http.informationlookphone(this.c_detail.id).then((res) => {
          if (res.status === 200) {
            this.tel_follow_id = res.data.tel_log_id
            this.showViewPhone = true; // 显示查看电话模态框
            this.nowDialData = res.data;
            this.getbehaviorData()
            this.getTelFollowStatus(); // 获取查看电话跟进状态
            // this.getJudgmentPhone(); // 获取查看电话返回全号/隐号类型
          }
        });
      },
      close_buttonshow() {
        this.$http.getAuthShow("is_see_tel_follow").then((res) => {
          if (res.status == 200) {
            if (res.data == 1) {
              this.close_button = false
            } else {
              this.close_button = true
            }
          }
        })
      },
      getJudgmentPhone() {
        this.$http.getJudgmentPhone(this.c_id).then((res) => {
          if (res.status == 200) {
            this.nowDialData = res.data;
          }
        })
      },
      onClickCus(e) {
        switch (e.id) {
          case 1:
            if (this.admin_list && this.admin_list.length == 0) {
              this.type = this.$route.query.type
              if(this.type=="seas"){
                this.admin_params.type = 0            
              }else{
                this.admin_params.type = 1  
              }
              // this.getAdmin()
            }
  
            this.is_transfer_customer = true;
            break;
          case 2:
            this.client_params.client_id = this.c_id;
            this.getClientData();
            this.is_dialog_merge = true;
            break;
          case 3:
            this.giveup_form.ids = this.c_id;
            this.is_dialog_giveup = true;
            break;
          case 4:
            this.is_qw_dialog = true;
            this.bind_qw.client_id = this.c_id;
            this.qw_params.page = 1;
            this.getWxworkData();
            break;
          case 5:
            if (this.c_detail.follow_id) {
              this.$message.warning("当前客户存在跟进人暂不可以一键报备")
              return
            }
            this.checkBaobeiInfo()
            this.getWebInfo()
            break;
        }
      },
      checkBaobeiInfo() {
        this.$http.checkBaobeiInfo({ mobile: this.c_detail.mobile }).then(res => {
          if (res.status == 200) {
            this.show_baobei_dia = true
            this.getProjectList()
          }
        })
      },
      getWebInfo() {
        this.$http.getWebInfo(this.website_id).then(res => {
          if (res.status == 200) {
            this.webInfo = res.data;
          }
        })
      },
      getProjectList() {
        this.$http.getBaobeiProjectList().then(res => {
          if (res.status == 200) {
            this.project_list = res.data.data
          }
        })
      },
      subMitBaobei() {
        // 验证手机号  customer_phones  project_ids
        this.$http.checkPhoneAndProject({ project_ids: this.projectId, customer_phones: [this.c_detail.mobile] }).then(res => {
          if (res.status == 200) {
            Loading.service({
              text: "正在提交 请稍候。。。",
            });
            this.subMitBaobeiInfo(0)
          }
        })
      },
  
      subMitBaobeiInfo(index) {
        return new Promise((resolve) => {
          this.proIndex = index
          if (index > this.projectArr.length - 1) return
          let curr = this.projectArr[index]
          let customer = {
            name: this.c_detail.cname,
            sex: this.c_detail.sex,
            phone: this.c_detail.mobile
          },
            project_data = {
              build_name: curr.build_name,
              full_num_reported: curr.full_num_reported,
              hide_num_mode: curr.hide_num_mode,
              reported_visit: curr.reported_visit,
            },
            reported = {
              customer_attached_phone: '',
              customer_id_no: '',
              project_id: curr.project_id,
              remark: this.remark,
              reported_go_with: curr.reported_go_with,
              visit_category: 1,
              visit_time: this.visit_time,
            }
  
          // 处理手机号隐号
          if (curr.full_num_reported === 1) {
            console.log();
          } else if (
            // 当前项目隐号且前三后五
            curr.full_num_reported === 0 &&
            curr.hide_num_mode == 2
          ) {
            customer.phone = customer.phone.replace(
              /(\d{3})\d{3}(\d{5})/,
              "$1***$2"
            );
          } else {
            // 当前项目前三后四
            customer.phone = customer.phone.replace(
              /(\d{3})\d{4}(\d{4})/,
              "$1****$2"
            );
          }
          let time_picker = ''
          switch (curr.visit_time_format_category) {
            case "1":
            case "Y-m-d H:i:s":
            case "Y/m/d H:i":
            case "Y年m月d日 H时i分":
              time_picker = "YMDhm";
              break;
            case "2":
            case "Y年m月d日":
            case "Y/m/d":
            case "Y-m-d":
              time_picker = "YMD";
              break;
            default:
              time_picker = "YMDhm";
              break;
          }
          reported.visit_time = getTime(time_picker);
          this.$http.submitBaobeiReal({
            customer: customer,
            project_data: project_data,
            reported: reported
          }).then(res => {
            setTimeout(() => {
              if (res.status == 200) {
                this.submitBaobeiCallback({
                  client_id: reported.project_id,
                  project_name: project_data.build_name
                })
              }
              let msg = res.status === 200 ? `成功：` : `失败：`
              msg += `${res.message || res.data?.message}`
              this.module_msg += `<div style='line-height:2;color:${res.status === 200 ? "green" : "red"
                }'>报备${msg}[${customer.name}-${customer.phone
                }-${project_data.build_name}]；</div>\n`;
              if (this.proIndex == this.projectArr.length - 1) {
                Loading.service().close();
                this.$alert(this.module_msg, '提示', {
                  dangerouslyUseHTMLString: true
                }).then(res => {
                  console.log(res);
                  this.show_baobei_dia = false
                }).catch(() => {
                  this.show_baobei_dia = false
                });
                return
              }
              resolve(this.module_msg);
              this.proIndex++
              this.subMitBaobeiInfo(this.proIndex)
            }, 1000);
          })
        })
      },
      onClickGetPull() {
        // 拉取企微客户
        this.$message.success("正在拉取客户...");
        this.$http
          .getCrmWxworkPullData({ params: this.pull_params })
          .then((res) => {
            if (res.status === 200) {
              // 如果返回参数接着拉取
              if (res.data) {
                this.pull_params = res.data;
                this.onClickGetPull();
              } else {
                this.$message.success("拉取完成");
                this.qw_params.page = 1;
                this.getWxworkData();
              }
            } else {
              this.$message.error("拉取失败！");
            }
          });
      },
      submitBaobeiCallback(params) {
        this.$http.submitBaobeiCallback(params).then(() => {
  
        })
      },
      onQwSearch() {
        this.qw_params.page = 1;
        this.getWxworkData();
      },
      getWxworkData() {
        this.is_qw_loading = true;
        this.$http.getWxWorkUserData({ params: this.qw_params }).then((res) => {
          this.is_qw_loading = false;
          if (res.status === 200) {
            this.qw_tableData = res.data.data;
            this.qw_params.total = res.data.total;
          }
        });
      },
      onBindUser(row) {
        this.bind_qw.openid = row.openid;
        this.$confirm("是否绑定该企业用户", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 点击确认调用公众号授权
            this.$http.setCrmCustomerBindWxUser(this.bind_qw).then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.is_qw_dialog = false;
                this.cus_list.push({
                  name: '绑定企业微信', id: 4
                })
              }
            });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消");
          });
      },
      // onCreateGiveup() {
      //   this.$http.giveupCrmCustomerData(this.giveup_form).then((res) => {
      //     if (res.status === 200) {
      //       this.is_dialog_giveup = false;
      //       this.$message.success("操作成功");
      //       this.f_params.page = 1;
      //       this.getFollowData();
      //       // 如果查看过维护记录
      //       if (this.MaintainRecord_list.length) {
      //         this.Maintain_list_params.page = 1;
      //         this.getMaintainRecord(); // 获取维护记录
      //       }
      //     }
      //   });
      // },
      // onClickmerge(row) {
      //   this.merge_form.one_client_id =
      //     this.merge_form.type === 1 ? this.c_id : row.id;
      //   this.merge_form.two_client_id =
      //     this.merge_form.type === 1 ? row.id : this.c_id;
      //   this.$confirm("是否合并客户", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   })
      //     .then(() => {
      //       this.$http.setMergeDetailClient(this.merge_form).then((res) => {
      //         if (res.status === 200) {
      //           this.$message.success("操作成功");
      //           this.is_dialog_merge = false;
      //           this.f_params.page = 1;
      //           this.getFollowData();
      //         }
      //       });
      //     })
      //     .catch(() => {
      //       // 点击取消控制台打印已取消
      //       console.log("已取消");
      //     });
      // },
      changeProject(e) {
        // 判断后台设置的报备项目数量
        this.projectArr = []
        if (e.length > this.webInfo.max_select_project_total) {
          this.$message.warning(`最多可以选择${this.webInfo.max_select_project_total}个项目`)
          this.projectId.splice(e.length - 1)
        }
        //获取报备项目数组信息 提交报备的时候需要判断手机号是隐号报备还是全号报备
        this.projectArr = this.project_list.filter(item => (this.projectId.includes(item.build_id) || this.projectId.includes(item.build_id + '')))
      },
  
      // onCreateDataInvalid() {
      //   let params = Object.assign({}, this.$refs.audit_form);
      //   params.sys_hid = this.detail.id;
      //   // let form = {
      //   //   client_id: this.c_id,
      //   //   content: this.invalid_content,
      //   // };
      //   // this.$http.setCrmCustomerInvalid(form).then((res) => {
      //   //   if (res.status === 200) {
      //   //     this.$message.success("操作成功");
      //   //     this.is_invalid_dialog = false;
      //   //     this.f_params.page = 1;
      //   //     this.getFollowData();
      //   //   }
      //   // });
      // },
      async setWuxiao() {
        // is_state: 1 不可操作 2 判断是否需要审批
        if (this.c_detail.is_state == 2) {
          let examine = false;
          // 遍历客户状态审批范围
          this.c_detail.state_list.map((list) => {
            // 如果客户状态审批包含无效客户
            if (list == this.inVainID) {
              examine = true;
            }
          })
          if (examine) {
            // 判断自己是否是客户管理员, 如果是不走审批，如果不是就走审批
            if (this.c_detail.admin_list && this.c_detail.admin_list.length) {
              // 遍历客户管理员
              const isLargeNumber = (item) => item == this.selfID;
              let is_admin = this.c_detail.admin_list.findIndex(isLargeNumber);
              if (is_admin >= 0) {
                this.setCrmCustomerStatus(this.c_id, this.inVainID); // 无需审批
              } else {
                await this.onClickFollowStatus({}, 19, { value_name: 2 });
                await this.getModelList(19);
              }
            } else {
              await this.onClickFollowStatus({}, 19, { value_name: 2 });
              await this.getModelList(19);
            }
          } else {
            this.setCrmCustomerStatus(this.c_id, this.inVainID); // 无需审批
          }
        } else {
          this.$message.warning("当前客户状态不可以进行审批");
        }
  
        // this.audit_form.f_trade_status = 2
      },
      setCusTop() {
        this.$http.setMyClientTop(this.c_id).then((res) => {
          if (res.status == 200) {
            this.$message.success("操作成功");
            this.getDetail(); // 刷新客户详情数据
          }
        })
      },
      showCallPhone(item) {
        this.CallPhoneOwner = this.showPhoneName; // 赋值客户名称
        this.autoPhonenNumber = item; // 赋值客户真实号码
        // nowDialData.type == 2、3 拨打号码显示为隐号
        if (this.nowDialData.type == 2) {
          this.concealPhoneNumber = this.changeMobile.replace(/^(.{3}).*(.{3})$/, "$1****$2"); // 赋值客户隐藏手机号
        }
        this.showPhone = true;
      },
      allStaffCallPhone() {
        // if (!this.has_dial) {
        //   this.$message.warning("暂无权限")
        //   return
        // }
        this.autoPhonenNumber = JSON.parse(JSON.stringify(this.c_detail.mobile)); // 赋值客户真实号码
        this.CallPhoneOwner = this.showPhoneName; // 赋值客户名称
        // 拨打号码显示为隐号
        if (this.nowDialData.type == 2) {
          this.concealPhoneNumber = this.changeMobile.replace(/^(.{3}).*(.{3})$/, "$1****$2"); // 赋值客户隐藏手机号
        }
        this.showAllStaffPhone = true; // 显示智能手机模态框
      },
      closeCallPhone() {
        this.showPhone = false
      },
      allStaffphoneClose() {
        this.showAllStaffPhone = false;
      },
      getCallId(e) {
        this.call_id = e;
        this.postData(this.call_id)
      },
      postData(call) {
        let params = {
          call_phone_id: call[0],
          call_name: call[1],
          call_phone: call[2],
          call_show_phone: call[3],
          type: 1,
          client_id: this.c_id
        }
        this.$http.sendPhoneTel(params).then(res => {
          console.log(res);
          this.f_params.page = 1; // 重置跟进列表页码(清空数据)
          this.getFollowData(); // 获取跟进记录
          this.TelePhone_list_params.page = 1; // 重置电话记录页码(清空数据)
          // 如果有电话记录
          if (this.TelePhoneRecord_list.length) {
            this.getClientTelephoneRecord(); // 获取电话记录
          }
          // 如果查看过维护记录
          if (this.MaintainRecord_list.length) {
            this.Maintain_list_params.page = 1;
            this.getMaintainRecord(); // 获取维护记录
          }
        })
      },
      onChangeStatus1() {
        this.is_loading = true; // 显示loading动画
        if (this.call_id != []) {
          this.$set(this.follow_params1, "client_id", this.c_id); // 客户ID
          this.$set(this.follow_params1, "contact_id", 0); // 客户ID
          this.$set(this.follow_params1, "call_phone_id", this.call_id[0]); // 电话记录ID
          this.$set(this.follow_params1, "call_name", this.call_id[1]); // 外呼拨打的客户姓名
          this.$set(this.follow_params1, "call_phone", this.call_id[2]); // 外呼拨打的客户号码
          this.$set(this.follow_params1, "call_show_phone", this.call_id[3]); // 外呼拨打的外显号码
        }
        if (this.follow_params1.content.length < 5) {
          this.$message.error("最少输入不能小于五个文字");
          this.is_loading = false;
          return;
        }
        let params = Object.assign({}, this.follow_params1)
        if(this.c_detail.deal_user){
          console.log(this.c_detail.tracking.id);
          params.tracking_id = this.c_detail.tracking.id
        }else{
          if (params.tracking_id == '') {
          params.tracking_id = 0
        }
        }
        params.call_status = this.formInline.region
        if (params.call_status == '') {
          delete params.call_status
        }
        params.tel_log_id = this.tel_follow_id
        // console.log(params);
        this.$http.lookphoneFollow(params).then(res => {
          if (res.status == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.showViewPhone = false;
            this.is_loading = false; // 关闭loading动画
            this.getDetail(); // 刷新客户详情数据
            this.f_params.page = 1; // 重置跟进列表页码
            this.getFollowData(); // 获取跟进记录
            this.call_id = [];
            this.$set(this.follow_params1, "content", "");
            this.$set(this.follow_params1, "call_phone_id", "");
            this.$set(this.follow_params1, "call_name", "");
            this.$set(this.follow_params1, "call_phone", "");
            this.$set(this.follow_params1, "call_show_phone", "");
            this.tel_follow_id = 0
            this.$emit("getDataListA",{})
          } else {
            this.is_loading = false;
          }
        }).catch(() => {
          this.is_loading = false;
        })
      },
      // 关闭查看电话回调
      ShowPhoneClose() {
        this.call_id = [];
        this.$set(this.follow_params1, "content", "");
        this.$set(this.follow_params1, "call_phone_id", "");
        this.$set(this.follow_params1, "call_name", "");
        this.$set(this.follow_params1, "call_phone", "");
        this.$set(this.follow_params1, "call_show_phone", "");
      },
      // 点击@同事
      addColleague() {
        this.showDropdown = true
        if (this.controlVisible) {
          this.$http.getColleagueDetailsList().then((res) => {
            if (res.status == 200) {
              // console.log(res)
              this.controlVisible = false;
              this.departmentMember = res.data;
            }
          })
        }
      },
      // 点击上传图片
      addPictures() {
        // console.log(this.imgList.length,"length")
        // 判断图片列表的个数
        if (this.imgList.length == 5) {
          this.disabled_picture = true; // 禁用上传
          this.$message({
            message: '最多只能上传5张图片',
            type: 'warning'
          });
        } else {
          this.disabled_picture = false; // 启用上传
        }
      },
       //(带看)上传图片成功
       UploadSuccess(e) {
        // console.log(e, "上传成功");
        this.takeimgList.unshift(e);
        // console.log(this.imgList,"this.imgList");
      },
      // (带看)删除已上传的图片
      deletetakePicture(index) {
        this.takeimgList.splice(index, 1);
      },
       // (带看)查看已上传的图片
       handletakePicture(item) {
        // console.log(item,'item');
        this.show_take_pictures = true;
        this.appointmentVisible = false
        if (item.url) {
          this.dialog_take_src = item.url;
        } else {
          this.dialog_take_src = item;
        }
      },
       //(带看)点击上传图片
       addTake_lookPictures() {
        // console.log(this.imgList.length,"length")
        // 判断图片列表的个数
        if (this.takeimgList.length == 5) {
          this.disabled_picture = true; // 禁用上传
          this.$message({
            message: '最多只能上传5张图片',
            type: 'warning'
          });
        } else {
          this.disabled_picture = false; // 启用上传
        }
      },
      //(带看)关闭查看图片的模态框
      show_takepictures(){
        this.show_take_pictures = false;
        this.appointmentVisible = true
      },
  
      // 上传图片成功
      UploadParamsSuccess(e) {
        // console.log(e, "上传成功");
        this.imgList.unshift(e);
        // console.log(this.imgList,"this.imgList");
      },
      // 查看已上传的图片
      handlePictureCardPreview(item) {
        // console.log(item,'item');
        this.show_dialog_pictures = true;
        if (item.url) {
          this.dialog_pictures_src = item.url;
        } else {
          this.dialog_pictures_src = item;
        }
      },
      // 删除已上传的图片
      deletePicture(index) {
        this.imgList.splice(index, 1);
      },
      // 预约带看点击陪看人员
      showAccompany() {
        this.$http.getColleagueDetailsList().then((res) => {
          if (res.status == 200) {
            this.departmentMember = res.data;
          }
        })
      },
      userSearch(){
        console.log(this.input2)
          this.userparams.user_name = this.input2
          this.show_add_memberA = false
          this.showAccompanyA()
  
      },
      showAA(){
         this.$refs.Personnel.blur();
         this.show_add_member = true
         this.showAccompanyA()
      },
      showAccompanyA(){
        this.appointAccompany = []
        this.$http.getDepartmentMemberList(this.userparams).then((res) => {
          if (res.status == 200) {
            this.serverData = JSON.parse(JSON.stringify(res.data))
            console.log(this.serverData);
            this.show_add_memberA = true
            this.serverData.push({
              id: 999,
              name: "未分配部门成员",
              order: *********,
              pid: 0,
              subs: this.serverData[0].user
            })
            this.recursionData(this.serverData);
            // 当键值key重复就更新key+=父级
            for (let i = 0; i < this.datalist.length; i++) {
              for (let j = i + 1; j < this.datalist.length; j++) {
                if (this.datalist[i].id == this.datalist[j].id) {
                  this.datalist[i].id = this.datalist[i].id +"_"+ this.datalist[i].Parent + ''
                }
              }
            }
            // this.show_add_member = true
          }
        })
      },
      // 递归数据处理
      recursionData(data) {
        for (let key in data) {
          if (typeof data[key].subs == "object") {
            data[key].subs.map((item) => {
              if (item.user) {
                item.subs = item.user;
                item.subs.map((list) => {
                  list.Parent = item.id;
                })
              }
              if (item.user_name) {
                item.name = item.user_name;
                this.datalist.push(item)
              }
            })
            this.recursionData(data[key].subs);
          }
        }
      },
      // 选中变化时触发
      selecetedMember(e) {
        this.selectedIds = e.checkedKeys;
      },
      PersonnelChange(val) {
        this.selectedIds = val;
      },
      //树形结构
      handleNodeClick(data) {
          console.log(data);
      },
      //树形结构确定
      selectMemberOk(){
        // console.log(this.selectedIds);
        this.appointAccompany = this.selectedIds
         this.show_add_member = false;
      },
      // 当点击带看时间触发
      appointmentTime(item) {
        this.form_appoint.take_time = item.id;
        this.$forceUpdate();
      },
      MemberClick(item) {
        if (this.colleagueID.includes(item.values)) {
          this.$message({
            message: '请勿重复@同事',
            type: 'warning'
          });
          return
        }
        this.colleagueID.push(item.values);
        // 获取输入框的dom
        let sel = this.$refs.gain;
        // 获取点击同事名
        let tpl = `<a 
                      href="javascript:void(0)"
                      class="myTS"
                      name='${item.values}'
                      style='color: #0088ff; text-decoration: none;'
                      contenteditable= 'false'
                    >
                      @${item.name}
                    </a> &nbsp;`
        sel.innerHTML += tpl;
        let range = window.getSelection()
        range.selectAllChildren(sel);
        range.collapseToEnd();
      },
      recordClick(e) {
        if (e.label == '带看' && this.TakeLookRecord_list == '') {
          this.TakeLook_list_params.client_id = this.c_id;
          // this.showAccompany(); // 获取成员列表
          this.getTakeLookRecord();
        }
        if (e.label == '外呼' && !this.TelePhoneRecord_list.length) {
          this.getClientTelephoneRecord();
        }
        if (e.label == '跟进' && !this.follow_list.length) {
          this.getFollowData();
        }
        if (e.label == '维护' && !this.MaintainRecord_list.length) {
          this.getMaintainRecord();
        }
        if (e.label == '线索') {
          this.c_params.client_id = this.c_id;
          this.getbehaviorData();
        }
        if (e.label == '轨迹') {
          this.guiji_params.id = this.c_id;
          this.getGuijiInfo();
        }
      },
      getClientTelephoneRecord() {
        this.TelePhone_list_params.client_id = this.c_id; // 赋值客户id
        if (this.TelePhone_list_params.page === 1) {
          this.TelePhoneRecord_list = [];
        }
        this.$http.getinformationTelephoneRecord(this.TelePhone_list_params).then((res) => {
          if (res.status == 200) {
            this.TelePhoneRecord_list = this.TelePhoneRecord_list.concat(res.data.data);
            if (res.data.data.length == this.TelePhone_list_params.per_page) {
              this.TelePhone_load = true;
            } else {
              this.TelePhone_load = false;
            }
          } else {
            this.TelePhone_load = false;
          }
        }).catch(() => {
          this.TelePhone_load = false;
        })
      },
      getTakeLookRecord() {
        this.TakeLook_load = false;
        this.$http.getinformationlookList(this.TakeLook_list_params).then(res => {
          if (res.status == 200) {
            this.TakeLookRecord_list = this.TakeLookRecord_list.concat(res.data.data);
            if (res.data.data.length == this.TakeLook_list_params.per_page) {
              this.TakeLook_load = true;
            } else {
              this.TakeLook_load = false;
            }
          } else {
            this.TakeLook_load = false;
          }
        }).catch(() => {
          this.TakeLook_load = false;
        })
      },
      ChangeMyContent() {
        let user = document.querySelectorAll(".myTS");
        let colleagueID = [];
        user.forEach(item => {
          colleagueID.push(Number(item.name));
        })
        this.colleagueID = colleagueID
      },
      colleagueOnMouseDown(e) {
        let my_clientX = e.clientX;
        let my_clientY = e.clientY;
        let popover = document.querySelector(".infoFrame");
        popover.style.display = "block";
        popover.style.top = my_clientY - 108 + "px";
        popover.style.left = my_clientX - 110 + "px";
        // 通过id获取同事信息
        this.$http.getColleagueDetailsData(e.target.dataset.id).then((res) => {
          if (res.status == 200) {
            this.colleagueDetails.department = res.data.department;
            this.colleagueDetails.user_name = res.data.user_name;
            this.colleagueDetails.phone = res.data.phone;
            this.colleagueDetails.headPortrait = this.colleagueDetails.user_name.substring(0, 1);
          }
        })
      },
      //复制同事手机号
      copyPhone(e) {
        e.stopPropagation();
        e.preventDefault();
        this.$onCopyValue(this.colleagueDetails.phone)
      },
      copyAllPhone(e, item) {
        e.stopPropagation();
        e.preventDefault();
        // 1: 全号 2: 隐号
        if (this.nowDialData.type == 2) {
          item = item.replace(/^(.{3}).*(.{3})$/, "$1*****$2");
        }
        this.$onCopyValue(item)
      },
      // 复制跟进记录内容
      onCopyValues(val) {
        // 将所有 <span> 标签替换为指定格式
        const convertedString = val.content.replace(/<span[^>]*>(@.*?)<\/span>/g, '$1');
        this.$onCopyValue(convertedString);
      },
      // 隐藏跟进记录同事信息框
      hideColleague() {
        let popover = document.querySelector(".infoFrame");
        popover.style.display = "none";
      },
      // 点击预约带看触发模态框
      appointmentLook() {
        this.appointmentVisible = true; // 显示模态框
        this.form_appoint.type = 1; // 参数类型为'带看'
        this.form_appoint.client_id = this.c_id; // 将客户id赋值
        this.is_show_meke = true; // 控制预约带看文字样式
      },
      // 点击复看
      appointmentRepeatLook() {
        this.appointmentVisible = true; // 显示模态框
        this.form_appoint.type = 2; // 参数类型为'复看'
        this.form_appoint.client_id = this.c_id; // 将客户id赋值
        this.is_show_meke = false; // 控制预约带看文字样式
      },
      // 点击选择房源触发模态框
      dialogSelectHouse() {
        this.showSelectHouse = true;
        this.form_houseType.page = 1;
        this.seachHouseName();
      },
      getLabelList() {
        this.$http.getLabelGroupNoPage().then((res) => {
          if (res.status === 200) {
            this.label_list = res.data;
          }
        });
      },
      onClickType(e, type) {
        switch (type) {
          case 1:
            this.form_houseType.trade_type = e.id;
            break;
          default:
            break;
        }
        this.form_houseType.page = 1;
        this.seachHouseName();
      },
      // 预约带看搜索小区名称
      seachHouseName() {
        this.$http.getAppointHouseList(this.form_houseType).then((res) => {
          if (res.status == 200) {
            // console.log(res,"成功");
            this.appointTabel_list = res.data.data;
            this.appointTabel_total = res.data.total;
          }
        })
      },
      // 预约带看列表点击选择触发
      onClickDetail(row) {
        this.form_appoint.community_name = row.community_name; // 小区名称赋值
        this.form_appoint.house_id = row.id; // 房源编号赋值
        this.showSelectHouse = false; // 关闭模态框
      },
      // 预约带看列表分页发生改变触发
      customerPageChange(e) {
        this.form_houseType.page = e;
        this.seachHouseName();
      },
      // 确定提交预约带看
      confirmSubmitAppoint() {
        if (this.form_appoint.take_date == '' || this.form_appoint.take_date == undefined) {
          return this.$message.error("带看日期不能空");
        }
        if (this.form_appoint.house_id == '' || this.form_appoint.house_id == undefined) {
          // return this.$message.error("请选择带看房源");
          delete this.form_appoint.house_id;
        }
        if (this.form_appoint.content == '' || this.form_appoint.content == undefined) {
          return this.$message.error("请填写跟进内容");
        }
        if (this.appointAccompany != '' && this.appointAccompany != undefined) {
          this.form_appoint.accompany = this.appointAccompany.join(",");
        } else {
          delete this.form_appoint.accompany
        }
        let nums = []; // 跟进图片接口参数容器
        if (this.takeimgList.length != 0 && this.takeimgList != undefined) {
          this.takeimgList.map((item) => {
            nums.push(item.url);
          })
          nums = nums.join(','); // 将数组转换字符串
        } else {
          nums = '';
        }
        this.form_appoint.file_path = nums
        this.$http.informationTakelook(this.form_appoint).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.takeimgList = []
            // 如果是复看,将当前客户状态转换为复看
            if (this.form_appoint.type == 2 && this.c_detail.is_repeat_look == 0) {
              this.c_detail.is_repeat_look = 1;
            }
            this.clearAppointParams(); // 清除参数
            this.superlists++; // 将房源类型设置为全部
            this.appointmentVisible = false; // 关闭模态框
            this.getFollowData();
            this.getDetail(); // 刷新客户详情，获取最新的客户跟进状态
            // 如果查看过维护记录
            if (this.MaintainRecord_list.length) {
              this.Maintain_list_params.page = 1;
              this.getMaintainRecord(); // 获取维护记录
            }
            // 如果带看记录列表不为空就获取最新带看记录列表
            if (this.TakeLookRecord_list != '') {
              this.TakeLookRecord_list = []
              this.getTakeLookRecord();
            }
          }
        })
      },
      // 提交成功，清除已选择的参数
      clearAppointParams() {
        // 预约带看参数
        this.form_appoint = {
          content: "", // 跟进内容
          accompany: [], // 陪看人员
          take_date: "", // 带看日期
          take_time: 1, // 带看时间
          take_no: "", // 带看单号
          client_id: "", // 客户id
          house_id: "", // 房源id
          community_name: "", // 小区名称
          project_id: [], //带看项目
        };
        this.appointAccompany = [], // 陪看人员
          // 预约带看房源列表参数
          this.form_houseType.trade_type = ""; // 房源类型
        this.form_houseType.keyword = ""; // 小区名称
        this.form_houseType.hid = ""; // 房源编号
      },
      // 当选择房源后对房源名进行改动就需要清空已选择的房源id
      takeLookHouse() {
        if (this.form_appoint.community_name && this.form_appoint.community_name != undefined) {
          this.form_appoint.house_id = ""
        }
      },
      loadMoreTakeLook() {
        if (!this.TakeLook_load) {
          return;
        }
        this.TakeLook_list_params.page++;
        this.getTakeLookRecord();
      },
      // 跟进记录点击设为置顶
      setFollowTop(val) {
        this.$http.setFollowTextTop(val.id).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.f_params.page = 1;
            this.getFollowData(); // 刷新
          }
        })
      },
      // 跟进记录点赞
      setFollowPraise(val) {
        // is_top: 0未点赞 1点赞
        if (val.is_top == 1) {
          this.$http.addFollowPraise(val.id).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '已取消点赞',
                type: 'success'
              });
              this.f_params.page = 1;
              this.getFollowData(); // 刷新
            }
          })
        } else {
          this.$http.addFollowPraise(val.id).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '点赞成功',
                type: 'success'
              });
              this.getFollowData(); // 刷新
            }
          })
        }
      },
      // 客户状态发生改变触发
      changeCusStatus() {
        this.$forceUpdate();
      },
      // 客户名称发生改变触发
      inputCusName() {
        this.$forceUpdate();
      },
      // 获取查看电话跟进状态
      getTelFollowStatus() {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.telFollowStatus })
          .then((res) => {
            if (res.status === 200) {
              this.tel_status_list = res.data;
              let test = true;
              this.tel_status_list.map(item => {
                if (this.c_detail.tracking_id == item.id) {
                  test = false;
                  this.follow_params1.tracking_id = item.id;
                }
                if (item.title == '有效客户') {
                  this.is_deal = true;
                  item.value_name = 1
                } else if (item.title == '无效客户') {
                  this.is_deal = true;
                  item.value_name = 2
                } else {
                  this.is_deal = true;
                  item.value_name = 1
                }
                return item
              })
              if (test && (this.c_detail.tracking != undefined && this.c_detail.tracking != null)) {
                // 如果客户状态为他司成交
                if (this.c_detail.tracking.title == "他司成交") {
                  this.is_deal = false; // 控制客户状态选择
                  this.follow_params1.tracking_id = this.effectiveID; // 默认选中有效客户id
                } else if (this.c_detail.tracking.title == "我司成交") { // 如果客户状态为我司成交
  
                  this.follow_params1.tracking_id = this.effectiveID;
                  this.is_deal = false; // 控制客户状态选择
                } else {
                  this.follow_params1.tracking_id = this.c_detail.tracking_id || this.c_detail.tracking.id; // 赋值当前的客户状态id
                }
              }
              if (!this.follow_params1.tracking_id) {
                this.follow_params1.tracking_id = 0;
              }
            }
          });
      },
      checkStatus() {
        // types!=='my'|| !c_detail.follow_user.length && !this.checkStatus(c_detail)
        // ClaimCustomers
        if(this.type!=='my' || !this.c_detail.follow_user.length){
            if (this.push_type == 2) {
            // 手动认领  
            // 潜在用户 我司成交的不可认领
            if (this.c_detail.tracking_identify == 1) {
              // return true
              this.Claim = "已认领"
              return this.ClaimCustomers =  false
              
            }
            // 掉工转公标记的可以领取 
            if (this.c_detail.public2_status > 0) {
              // return false
              this.Claim = "未认领"
              return this.ClaimCustomers = true
            }
            // 潜在客户可以领取
            // if (this.type == "qianzai") {
            //   return false
            // }
            // 其他情况 不可领取 
            // return true
            this.Claim = "已认领"
            return this.ClaimCustomers =  false
          }
          // return false
          this.Claim = "已认领"
          return this.ClaimCustomers =  false
          }
          this.Claim = "未认领"
          return this.ClaimCustomers = true
      },
      Topping(order,id) {
        if (order !== 1) {
          this.$http.setMyClientTop(id).then(res => {
            if (res.status == 200) {
              this.$message.success("操作成功");
              this.getDetail(); // 刷新客户详情数据
            }
          })
        }
        if (order == 1) {
              this.$http.setMyClientTop(id).then(res => {
                if (res.status == 200) {
                  this.$message.success("操作成功");
                  this.getDetail(); // 刷新客户详情数据
                }
              })
        }
      },
      onClickGet(row) {
        this.$confirm("是否认领该客户", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$http.getCrmCustomerPublick({ ids: row + "" }).then((res) => {
              if (res.status === 200) {
                this.$message.success("认领成功");
                let url = `/crm_customer_detail?id=${row}&type=my`;
                this.$goPath(url);
              }
            });
          })
          .catch(() => {
            // 点击取消控制台打印已取消
            console.log("已取消");
          });
      },
      // 获取是否有权限查看客户操作（创始人、维护人、客户管理员可见）
      getOneselfList() {
        this.$http.getAdmin().then((res) => {
          if (res.status == 200) {
            this.AdminData = res.data; // 存储自己的信息
            this.selfID = res.data.id; // 赋值自己的id
            // 如果是站长就显示
  
            let mangers = this.c_detail.admin_list
            let keyuanManger = 0, whr = 0
            if (mangers.includes(this.selfID + "")) {
              keyuanManger = 1
            }
            if (this.selfID == this.c_detail.follow_id) {
              whr = 1
            }
            if (keyuanManger == 1 || whr == 1 || (this.c_detail.follow_id > 0 && this.c_detail.follow_id == this.selfID)) {
              this.has_roles = true
            }
            if(whr == 1 || (this.c_detail.follow_id > 0 && this.c_detail.follow_id == this.selfID) || !this.c_detail.follow_user) {
              this.has_dial = true; // 用于判断外呼权限
            }
            if (res.data.roles[0].name == "站长") {
              this.is_show_Controls = true;
              this.has_dial = true; // 用于判断外呼权限
            } else if (this.c_detail.follow_user && this.c_detail.follow_user.id ? this.c_detail.follow_user.id == res.data.id : null) { // 如果客户维护人是自己
              this.is_show_Controls = true;
            } else {
              // 如果不是站长也不是维护人,获取客户管理员列表判断是否可查看
              this.getAuthShow(res.data.id);
            }
          }
        })
      },
      // 获取客户管理员列表
      getAuthShow(id) {
        this.$http.getAuthShow("admin_list").then((res) => {
          if (res.status == 200) {
            let adminNums = "";
            let adminList = "";
            // 判断返回值是否是字符串,如果不是就转换为字符串
            if (res.data.typeof == "string") {
              adminList = res.data;
            } else {
              adminList = res.data.toString();
            }
            if (adminList && adminList.indexOf(",") >= 0) {
              adminNums = adminList.split(",");
            } else {
              adminNums = adminList;
            }
            if (adminNums && Array.isArray(adminNums)) {
              adminNums.map((item) => {
                if (item == id) {
                  this.is_show_Controls = true;
                }
              })
            } else {
              if (adminNums == id) {
                this.is_show_Controls = true;
              }
            }
          }
        })
      },
      showExamineStatus() {
        if (this.audit_form.cat_id == 19 && this.c_detail.is_state == 2) {
          // console.log("执行")
          return true
        } else {
          // console.log("不执行")
        }
        // if(this.audit_form.cat_id == 19) {
        //   console.log(this.audit_form.f_trade_status,"wwwxx")
        //   // return false;
        // } else {
        //   return true;
        // }
        // this.audit_form
        // if(this.audit_form.cat_id == 19 && this.c_detail.is_state == 2) {
  
        // }
        // let selectStatus = null; // 客户审批状态id容器
        // let is_examine = false; // 控制审批
        // // f_trade_status : 1：有效 2： 无效 3： 暂缓
        // if(params.f_trade_status == 1) {
        //   selectStatus = this.effectiveID; // 有效客户id
        // } else if(params.f_trade_status == 2) {
        //   selectStatus = this.inVainID; // 无效客户id
        // } else if(params.f_trade_status == 3) {
        //   selectStatus = this.PutOffID; // 暂缓客户id
        // }
        // // 判断是否需要审批
        // this.c_detail.state_list.map((item) => {
        //   if(item == selectStatus) {
        //     is_examine = true;
        //   }
        // })
        // if(!is_examine) {
        //   // console.log("不走审核")
        //   this.setCrmCustomerStatus(this.c_id, selectStatus); // 无需审批变更客户状态
        //   this.show_audit = false; // 关闭模态框
        //   this.is_loading = false; // 关闭loading加载
        // } else { // 判断是否是客户管理员
        //   let is_admins = false;
        //   this.c_detail.admin_list.map((arr) => {
        //     if(arr == this.selfID) {
        //       is_admins = true;
        //     }
        //   })
        //   // 如果是管理员，无需审批
        //   if(is_admins) {
        //     // console.log("管理员无需审批");
        //     this.setCrmCustomerStatus(this.c_id, selectStatus); // 无需审批变更客户状态
        //     this.show_audit = false; // 关闭模态框
        //     this.is_loading = false; // 关闭loading加载
        //   } else {
        //     // console.log("走审核")
        //     this.$ajax.house.addHouseAudit(params).then(res => {
        //       if (res.status == 200) {
        //         this.$message.success(res.mesage || '提交成功')
        //         this.show_audit = false;
        //         this.getDetail(); // 刷新数据
        //       }
        //       this.is_loading = false; // 关闭loading加载
        //     })
        //   }
        // }
      },
      // 发起邀约
      initiateInvite() {
        this.viewCustomerTel(); // 调用查看电话
        // 如果该客户没有一次查看电话次数，就请求客户详情(刷新客户跟进样式状态)
        if (this.c_detail.see_tel_num == 0 || this.c_detail.see_tel_num == null || this.c_detail.see_tel_num == undefined) {
          this.getDetail();
        }
      },
      clientFollowStatus(val) {
        if (this.c_detail.deal_user) {
          return true;
        }
        switch (val) {
          case 'see_tel_num':
            if (this.c_detail.see_tel_num) {
              return true;
            }
            break;
          case 'take_num':
            if (this.c_detail.see_tel_num && this.c_detail.take_num) {
              return true;
            }
            break;
          case 'is_repeat_look':
            if (this.c_detail.is_repeat_look) {
              // 如果有复看，没有带看，就给带看赋值1,显示已带看样式
              // if (this.c_detail.take_num == 0 || this.c_detail.take_num == null || this.c_detail.take_num == undefined) {
              //   this.c_detail.take_num = 1
              // }
              return true;
            }
            break;
          default:
            return false;
        }
      },
      // 预约带看、复看，更改预约类型
      changeShowType() {
        // 根据预约类型，改变文字样式
        if (this.form_appoint.type == 1) {
          this.is_show_meke = true;
        } else {
          this.is_show_meke = false;
        }
      },
      // 获取客户是否有权限查看外呼
      // getClientStatus() {
      //   // 判断外呼方式
      //   if (this.nowDialData.type == 1) {
      //     return true
      //   } else if (this.nowDialData.type == 2) {
      //     return true
      //   } else {
      //     return false;
      //   }
      // },
      // 获取更多电话记录数据
      morePhoneRecord() {
        if (this.TelePhone_load) {
          // console.log("触发下拉加载");
          this.TelePhone_list_params.page++; // 页码增加
          this.getClientTelephoneRecord();
        }
      },
      // 点击上一条客户数据
      changeDetailPrevious() {
        // console.log("上一条", this.$store.state.options_tabs);
        // 如果存在上一条客户id
        if (this.c_detail.page_last_id != '' && this.c_detail.page_last_id != undefined) {
          // 点击上一条，删除之前的客户数据tabs
          if (this.$store.state.options_tabs.length > 1) {
            this.$store.state.options_tabs.splice(-1, 1);
          }
          let url = `/crm_customer_detail?id=${this.c_detail.page_last_id}&type=${this.website_types}`;
          this.$goPath(url);
        } else {
          this.$message.warning("没有更多了")
        }
      },
      // 点击下一条客户数据
      changeDetailNext() {
        // console.log("下一条", this.$store.state.options_tabs);
        // 如果存在上一条客户id
        if (this.c_detail.page_next_id != '' && this.c_detail.page_next_id != undefined) {
          // 点击下一条，删除之前的客户数据tabs
          if (this.$store.state.options_tabs.length > 1) {
            this.$store.state.options_tabs.splice(-1, 1);
          }
          let url = `/crm_customer_detail?id=${this.c_detail.page_next_id}&type=${this.website_types}`;
          this.$goPath(url);
        } else {
          this.$message.warning("没有更多了")
        }
      },
      // 获取客户维护记录
      getMaintainRecord() {
        this.Maintain_list_params.client_id = this.c_id; // 赋值客户id
        if (this.Maintain_list_params.page === 1) {
          this.MaintainRecord_list = [];
        }
        this.$http.getinformationTelephoneRecord(this.Maintain_list_params).then((res) => {
          if (res.status == 200) {
            this.MaintainRecord_list = this.MaintainRecord_list.concat(res.data.data);
            if (res.data.data.length == this.Maintain_list_params.per_page) {
              this.Maintain_load = true;
            } else {
              this.Maintain_load = false;
            }
            this.$nextTick(() => {
              let user_follow = document.getElementsByClassName("user_follow");
              for (let i = 0; i < user_follow.length; i++) {
                user_follow[i].style.cursor = "pointer";
                user_follow[i].onclick = (e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  this.colleagueOnMouseDown(e)
                }
              }
            })
          } else {
            this.Maintain_load = false;
          }
        }).catch(() => {
          this.Maintain_load = false;
        })
      },
      // 加载更多维护记录
      loadMoreMaintain() {
        if (this.Maintain_load) {
          // console.log("触发下拉加载");
          this.Maintain_list_params.page++; // 页码增加
          this.getMaintainRecord(); // 获取客户维护记录
        }
      },
      showWarn() {
        // 检查本地存储中短信提醒的标记
        let timestamp = JSON.parse(localStorage.getItem('buttonDisabled'));
        // console.log(timestamp, "timestamp")
        if (timestamp != 'null' && (Array.isArray(timestamp) && timestamp.length)) {
          // 查找当前客户id
          let currentData = timestamp.find(item => item.id == this.c_id);
          let currentIndex = timestamp.findIndex(item => item.id == this.c_id);
          // 当存在客户id才执行
          if (currentData) {
            // 将时间转换为日期对象
            const disabledDate = new Date(parseInt(currentData.time, 10));
            // 获取当前日期
            const currentDate = new Date();
            // 检查日期是否在一天内
            if (currentDate.getDate() === disabledDate.getDate()
              &&
              currentDate.getMonth() === disabledDate.getMonth()
              &&
              currentDate.getFullYear() === disabledDate.getFullYear()) {
              this.buttonDisabled = true;
            } else {
              timestamp.splice(currentIndex, 1);
              localStorage.setItem('buttonDisabled', JSON.stringify(timestamp));
            }
          }
        }
        this.is_remind_customer = true; // 显示模态框
        this.titleName = "first";
        // this.$refs.tabs.$el.querySelector(".el-tabs__active-bar").style.width = "56px";
      },
      // 获取客户意向列表
      getIntentionList(value) {
        if (value != " " && value != "") {
          this.inten_loading = true; // 开启loading
          this.$http.getProjectComfirm({ keyword: value }).then((res) => {
            if (res.status == 200) {
              // console.log(res.data, "获取意向列表")
              this.inten_loading = false; // 关闭loading
              this.IntenOptions = res.data;
            } else {
              this.inten_loading = false;
            }
          }).catch(() => {
            this.inten_loading = false;
          })
        }
      },
      changeIntention() {
        this.$forceUpdate();
      },
    },
  };
  </script>
  
<style scoped lang="scss">
.pages {
  height: 97% !important;
  background: #f1f4fa 100%;
  margin: 0px !important;
  padding: 0 !important;

  .label {
    font-size: 18px;
    color: #2e3c4e;
    font-weight: 500;
    padding-left: 8px;
    border-left: 4px solid #2d84fb;
  }

  .left-content {
    .contentdetail {
      width: 89%;
      justify-content: space-between;
    }

    .avatar {
      width: 65px;
      height: 65px;
      border-radius: 50%;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .detail-box {
      margin-left: 24px;

      .name {
        .n {
          font-size: 16px;
          color: #2e3c4e;
        }
      }

      .wh {
        margin-left: 24px;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        color: #8a929f;
        white-space: nowrap;

        img {
          width: 16px;
          height: 14px;
          margin-right: 10px;
        }
      }

      .bind-box {
        margin-top: 13px;
        font-size: 14px;
        color: #8a929f;

        .b-n {
          margin-right: 24px;
          display: flex;
          align-items: center;

          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }
      }

      .label-box {
        margin-top: 25px;

        .lab {
          padding: 6px 12px;
          color: #fff;
          border-radius: 2px;
          font-size: 14px;
          margin-right: 24px;

          // &.left {
          //   background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
          // }
          &.right {
            background: #2d84fb;
            cursor: pointer;
          }

          &.warning {
            background: #ffcd4e;
          }
        }
      }

      .desc-box {
        font-size: 14px;
        color: #8a929f;
        margin-top: 2px;
        align-items: center;

        span {
          margin-right: 24px;
        }

        .ovel-1 {
          max-width: 270px;
          overflow: hidden; // 溢出部分隐藏
          white-space: nowrap; // 文字不换行
          text-overflow: ellipsis; // 显示省略号
        }
      }
    }

    .levelbtn {
      margin-top: 20px;
    }

    .namebox {
      margin-bottom: 0;

      .client-phone-detail {
        margin-right: 30px;
        line-height: 24px;

        // .attribution {
        //   font-size: 14px;
        //   color: #8a929f;
        // }
      }

      // .btn {
      //   border: 1px solid rgba(138, 146, 159, 1);
      //   font-size: 14px;
      //   color: #8a929f;
      //   padding: 8px 13px;
      //   border-radius: 2px;
      //   margin-right: 31px;
      //   cursor: pointer;
      //   align-items: center;
      //   white-space: nowrap;

      .b-tel {
        margin-left: 15px;
        // background: #cdd2da;
        // border: 6px solid #265da9;
        // border: none;
        // color: #c4fb2d;
        // white-space: nowrap;
      }

      .level {
        // margin-left: 60px;
        // justify-content: space-between;
        // color: #fff;
        // background: linear-gradient(180deg, #f8a707, #f85d02 100%);
        // border: none;
        // white-space: nowrap;
      }

      // }
    }

    .name-box {
      margin-top: 15px;
      align-items: center;
      margin-bottom: 40px;

      .name-box-img {
        width: 100px;
        height: 40px;
        margin-right: 24px;
      }

      span {
        display: inline-block;
        font-size: 16px;
        color: #8a929f;
        margin-right: 31px;
      }
    }
  }

  .right-content {
    .CustomersThe_card {
      width: 100%;
      height: 150px;
      // background-color: pink;
      // overflow-x:scroll;
      // max-width:100%;
      // overflow-y:hidden;
    }

    .title {
      font-size: 18px;
      color: #2e3c4e;
      font-weight: 500;
    }



    .name-box-title {
      justify-content: space-between;
      align-items: center;
      margin-top: 0;

      .row-two-label {
        display: inline-block;
        font-size: 14px;
        color: #8a929f;
        padding: 0 10px;
        margin: 0;
      }

      .previous,
      .prenext {
        font-size: 14px;
        color: #8a929f;
        cursor: pointer;

        i {
          font-size: 12px;
        }
      }

      .name-box-tem {
        align-items: center;
        cursor: pointer;

        .btn-row {
          font-size: 14px;
          color: #8a929f;
        }

        .row-label {
          color: #8a929f;
          margin-right: 0;
          padding: 0 10px;
        }
      }

      .title {
        font-size: 18px;
        color: #2e3c4e;
        font-weight: 500;
      }
    }

    .follow-box {
      margin: 24px 0;
      align-items: center;

      // span {
      //   margin-right: 24px;
      //   color: #8a929f;
      //   font-size: 16px;
      // }

      .follow-item {
        width: 64px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 2px;
        // margin-right: 24px;
        font-size: 14px;
        color: #fff;
        background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
        cursor: pointer;
      }

      .level_B {
        color: #fff;
        background: linear-gradient(180deg, #f8a707, #f85d02 100%);
      }

      .level_C {
        color: #fff;
        background: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }

      .follow-await {
        width: 64px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 2px;
        font-size: 14px;
        color: #8a929f;
        background-color: #f4f4f5;
        cursor: pointer;
      }
    }

    .appointment {
      .el-button {
        padding: 7px 18px;
        color: #8a929f;
        background-color: #f4f4f5;
        border: none;
        font-size: 14px;

        &:active {
          color: #fff;
          background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
        }
      }

      .follow-deal-status {
        color: #ff4646;
        background: #ffe7e7;
        position: relative;

        img {
          position: absolute;
          left: 3px;
          top: 3px;
        }
      }
    }

    .follow-deal-status {
      color: #ff4646;
      background: #ffe7e7;
      position: relative;

      img {
        position: absolute;
        left: 3px;
        top: 3px;
      }
    }

    .invite {
      .el-button {
        padding: 7px 18px;
        color: #8a929f;
        background-color: #f4f4f5;
        border: none;
        font-size: 14px;
      }
    }

    .isPositions {
      .el-button {
        background-color: #e8f1fe;
        color: #409eff;
      }
    }

    .follow-arrow {
      width: 6px;
      height: 12px;
      line-height: 12px;
      margin: 0 9px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .follow-input {
      width: 100%;
      padding: 10px 23px;
      border: 1px solid rgba(221, 225, 233, 1);
      border-radius: 4px;
      margin-bottom: 10px;
      position: relative;

      .popover {
        position: absolute;
        padding: 8px 10px;
        background: rgba(34, 34, 34, 0.8);
        border-radius: 4px;
        pointer-events: none; //防止穿透
        z-index: 220;
        color: #fff;
        top: -50px;
        left: 50%;
        transform: translateX(-50%);

        .triangle {
          position: absolute;
          display: block;
          left: 50%;
          transform: translateX(-40%);
          bottom: -10px;
          width: 0px;
          height: 0px;
          content: "";
          border-color: transparent;
          border-style: solid;
          border-width: 6px;
          border-top: 6px solid rgba(34, 34, 34, 0.8);
          z-index: 220;
        }
      }

      .f-l {
        font-size: 16px;
        width: 130px;
        align-items: center;
        color: #8a929f;
        // padding-right: 23px;
        border-right: 1px solid #dde1e9;
        cursor: pointer;

        img {
          width: 16px;
          height: 16px;
          margin-left: 12px;
        }
      }

      .f-r {
        margin-left: 12px;
        width: 100%;
        align-items: flex-start;

        .paperview-input-text {
          width: 100%;
          height: 100%;
          outline: none;
          margin-top: 5px;
          cursor: text;
          max-width: 390px;
        }

        .paperview-input-text:empty::before {
          color: lightgrey;
          content: attr(placeholder);
        }
      }
    }

    .record_content {
      ::v-deep .el-tabs__header {
        .el-tabs__nav-wrap {
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              .el-tabs__item {
                font-size: 18px;
              }
            }
          }
        }

        .el-tabs__nav-wrap::after {
          height: 0px;
        }
      }
    }
  }

  .isborder {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #2d84fb;
      box-shadow: 0 0 5px #2d84fb, inset 0 0 5px rgba(221, 225, 233, 1),
        0 1px 0 #2d84fb;
    }

    100% {
      border-color: rgba(221, 225, 233, 1);
      box-shadow: 0 0 20px #2d84fb, inset 0 0 10px rgba(221, 225, 233, 1),
        0 1px 0 #2d84fb;
    }
  }

  .kehustatus {
    width: 97%;
    height: 56px;
    border-top: 1px solid rgba(221, 225, 233, 1);
    // margin-top: 10px;

    .kehu_footer {
      width: 93%;
      margin: 0 auto;
      justify-content: space-between;
    }
  }

  .Customer_interest {
    ::v-deep .el-input__inner {
      height: 80px !important;
    }
  }
}

.recordcss {
  // width: 220px;
  display: flex;
  flex-wrap: wrap;

  .invitecss {
    padding: 7px 18px;
    margin-left: 10px;
    margin-bottom: 10px;
    color: #8a929f;
    background-color: #f4f4f5;
    border: none;
    font-size: 14px;
    cursor: pointer;
  }

  .isPositionscss {
    background-color: #e8f1fe;
    color: #409eff;
  }
}

.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

// .follow-record {
//   margin-top: 32px;
// }
.theme-list {
  margin-top: 12px;
  cursor: pointer;
}

.fixed-nav {
  overflow-x: scroll;
  -webkit-overflow-scrolling: touch;
}

.fixed-nav-content {
  display: flex;
}

.tab-title {
  padding: 14px 0;
  margin-right: 20px;
  flex-shrink: 0;
  font-size: 14px;
  color: #8a929f 100%;
  position: relative;

  &.select-tab {
    color: #2d84fb;

    &::after {
      content: "";
      position: absolute;
      bottom: -0;
      left: 30%;
      width: 24px;
      background: #2d84fb;
      height: 2px;
    }
  }
}

.date_name {
  padding: 4px 12px;
  font-weight: 500;
}

.date_value {
  font-size: 11px;
}

.t_time {
  flex-wrap: wrap;
  margin-top: 24px;
  cursor: pointer;

  .t_time_item {
    padding: 4px 6px;
    background: #f8f8f8;
    border-radius: 2px;
    font-size: 11px;
    color: #8a929f;
    margin-right: 8px;
    margin-bottom: 12px;

    &.active {
      color: #2d84fb;
    }
  }
}

// ::-webkit-scrollbar {
//   width: 0 !important;
// }
// ::-webkit-scrollbar {
//   width: 0 !important;
//   height: 0;
// }
.labelname {
  margin-bottom: 10px;
  font-size: 12px;
  color: #8a929f;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;

    &.checked {
      background: rgba(45, 132, 251, 0.15);
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .labels-tag {
    margin-bottom: 10px;
    cursor: pointer;
    border-radius: 4px;
    padding: 5px 22px;
    // min-width: 80px;
    font-size: 14px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 6px;
    color: #2E3C4E;
  }
}

.loadmore {
  text-align: center;
  cursor: pointer;
  margin-bottom: 10px;
  margin-top: 20px;
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
}

.title-label {
  margin-bottom: 12px;
  color: #2e3c4e;
  display: inline-block;
}
</style>
<style scoped lang="scss">
/* 利用穿透，设置input边框隐藏 */
.paperview-input-text {
  line-height: 1;

  .el-input__inner {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 0px;
    width: 100%;
  }

  .el-textarea__inner {
    border: 1px solid #fff;
    padding: 0;
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.label-invalid {
  color: #8a929f;
  margin-bottom: 12px;
}

.member {

  .left,
  .right {
    padding: 10px;
    border-top: 1px solid #e9e9e9;
  }

  .left {
    border-right: 1px solid #e9e9e9;
  }

  .select_title {
    font-size: 16px;
    font-weight: 600;
    color: #666;
  }

  .selected_list {
    padding: 10px 0;

    .selected_item {
      padding: 5px 10px;
      color: #2e3c4e;
      font-size: 14px;

      .delete {
        font-size: 22px;
        cursor: pointer;
      }

      .name {
        color: #2e3c4e;
        font-size: 14px;
      }
    }
  }
}

.audit_person {
  .audit_person_name {
    padding: 0 10px;
    margin-right: 5px;
    border-radius: 4px;
    border: 1px solid #999;

    &.audit_person_add {
      cursor: pointer;
      margin-right: 10px;
    }
  }
}

.el-timeline-item {
  padding: 20px 0;
}

.follow-record {

  // ==========
  .el-timeline {
    font-size: 15px;

    .el-timeline-item:hover {
      .el-timeline-item__wrapper {
        .el-timeline-item__content {
          .agent_info {
            .follow_info_box {
              display: flex;
            }
          }
        }
      }
    }
  }

  // ==========
  .agent_info {
    height: 34px;
    margin: -20px 0 24px 0;

    .time {
      margin-right: 10px;
      color: #8a929f;
    }

    .show_is_top {
      background-image: linear-gradient(180deg, #f9762e, #fc0606);
      color: #fff;
      padding: 2px 8px;
      border-radius: 4px;
      margin-left: 20px;
    }

    .follow_info_box {
      display: none;
      flex-direction: row;
      border: 1px solid #8a929f;
      border-radius: 3px;
      margin-left: 20px;

      .follow_info_praise,
      .follow_info_copy {
        width: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        text-align: center;
        padding: 6px;
        color: #8f959e;
        border-right: 1px solid #8f959e;
        cursor: pointer;
      }

      .follow_info_praise:active {
        background-color: #eff0f1;
      }

      .follow_info_copy:active {
        background-color: #eff0f1;
      }

      .follow_add_top {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #8f959e;
        padding: 6px;
        cursor: pointer;
      }

      .follow_add_top:active {
        background-color: #eff0f1;
      }
    }

    .img {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .agent_name {
      font-size: 12px;
      color: #8a929f;
    }
  }

  .f_content {
    display: flex;
    align-items: center;
    max-width: 450px;
    // margin-bottom: 20px;

    &.red {
      color: #fc0606;
    }

    // & span {
    //   display: flex;
    //   align-items: center;
    // }
  }

  .f_line {
    line-height: 24px;
  }

  .follow-picture {
    display: flex;
    flex-direction: row;
    margin-top: 13px;

    .follow-picture-box {
      width: 60px;
      height: 60px;
      margin-right: 10px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .uploader-actions {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        border-radius: 4px;
        cursor: default;
        text-align: center;
        color: #fff;
        opacity: 0;
        font-size: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s;

        .uploader-actions-item {
          font-size: 20px;
          cursor: pointer;

          & i {
            color: #fff;
          }
        }
      }

      .uploader-actions:hover {
        opacity: 1;
      }
    }
  }

  .follow-praise {
    display: inline-block;
    border-radius: 15px;
    color: #8a929f;
    background-color: #f1f4fa;
    padding: 5px 12px;
    box-sizing: border-box;
    margin-right: 20px;
    margin-top: 13px;

    .follow-praise-box {
      display: flex;
      flex-direction: row;
      align-items: center;

      .follow-praise-img {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 19px;
          height: 19px;
        }
      }

      .follow-praise-separate {
        width: 1px;
        height: 14px;
        background-color: #8a929f;
        margin: 0 5px;
        opacity: 0.5;
      }

      .follow-praise-text {
        padding: 0 5px;
      }
    }
  }
}

.infoFrame {
  display: none;
  width: 200px;
  position: fixed;
  top: 0px;
  left: 0px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
  border-radius: 7px;
  z-index: 2;

  .infoAngle {
    position: absolute;
    top: 74px;
    left: 95px;
    width: 0px;
    height: 0px;
    color: #fff;
  }

  .infoFrame-box {
    display: flex;
    padding: 10px;

    .infoFrame_icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #2d84fb;
      color: #fff;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 5px 12px 0;
    }

    .infoFrame-info {
      display: flex;
      flex-direction: column;
      color: #303133;

      & span {
        font-size: 12px;
        margin-top: 5px;
      }

      .infoFrame-phone {
        display: flex;
        align-items: center;

        .numberTop {
          background: #afb5b42e;
          border-radius: 50%;
          width: 16px;
          height: 16px;
          margin-left: 5px;
          cursor: pointer;
        }
      }
    }
  }
}

.el-dialog__body {
  .PhoneTitle {
    display: block;
    padding: 0 0 24px 0;
    font-size: 18px;
    color: #2e3c4e;
    font-weight: 500;
  }
}

.callBtn {
  .el-button {
    padding: 4px 8px;
    // margin-left: 5px;
  }
}

.el-icon-info {
  font-size: 1px;
  color: rgb(245, 108, 108);
  margin-left: 5px;
}

.itemCenter {
  align-items: center;
  justify-content: space-between;

  // margin-top: -10px;
  .all-phone {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .Belonging {
    color: #8a929f;
    font-weight: 500;
    margin-left: 10px;
    margin-top: 4px;
  }
}

.Colleagues {
  margin: 20px 0px 30px 0px;

  .FriendsPiece,
  .addPicture {
    max-width: 45px;
    background: #f4f4f5;
    font-size: 14px;
    border: 1px solid #eaeaec;
    border-radius: 5px;
    color: #7a7c7f;
    padding: 3px 8px;
    margin-bottom: 3px;
    cursor: pointer;
  }
}

.addPicture {
  margin-left: 10px;
}

.picture_list_box {
  width: 48px;
  height: 48px;
  border: 1px solid #eaeaec;
  border-radius: 4px;
  background-color: #e2e2e2;
  margin-left: 12px;
  position: relative;

  .photo-item-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .delete-picture {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    z-index: 2;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .uploader-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 4px;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;

    .uploader-actions-item {
      font-size: 20px;
      cursor: pointer;

      & i {
        color: #fff;
      }
    }
  }

  .uploader-actions:hover {
    opacity: 1;
  }
}

.colleague-box {
  display: flex;
  width: 240px;
  height: 300px;
  overflow-y: auto;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid #e6e7e8;
  box-sizing: content-box;
  padding: 12px;
  position: absolute;
  z-index: 5;
  border-radius: 7px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .colleague_content {
    display: flex;
    padding: 8px 10px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;

    &:hover {
      background: #f2f2f3;
    }
  }
}

#musicMp3 {
  position: absolute;
  left: -200%;
  top: -200%;
  width: 0;
  height: 0;
}

.TakeLook-box {
  padding: 0 20px;

  .TakeLook-follow {
    margin-bottom: 20px;
    margin-left: 10px;

    .el-textarea {
      .el-textarea__inner {
        min-height: 80px !important;
      }
    }

    .TakeLook-title {
      font-size: 14px;
      margin-bottom: 12px;
    }
  }

  .TakeLook-house {
    .TakeLook-select {
      min-width: 28px;
      text-align: center;
      margin-left: 10px;
      color: #c0c4cc;
      cursor: pointer;
    }
  }

  .el-form {
    .appointment-time {
      .el-form-item__content {
        .time-box {
          display: flex;
          // align-items: center;
          height: 30px;
          background: #f0eff4;
          border: 1px solid #f0eff4;
          border-radius: 6px;
          cursor: pointer;

          .time-boxList {
            display: flex;
            align-items: center;
            background: #f0eff4;
            padding: 4px 43px;
            border: 1px solid #f0eff4;

            & span {
              font-size: 12px;
            }
          }

          .time-active {
            background: #ffffff;
            border: 1px solid #f0eff4;
            border-radius: 6px;
          }
        }
      }
    }
  }
}

.TakeLook-footer {
  text-align: center;
}

.content-box-crm {
  margin-bottom: 28px;

  .bottom-border {
    align-items: center;
    padding-bottom: 24px;
    border-bottom: 1px dashed #e2e2e2;
    justify-content: flex-start;

    .text {
      font-size: 14px;
      color: #8a929f;
      width: 70px;
      text-align: right;
    }

    .el-input {
      width: 250px;
    }
  }
}

.TakeLook-personnel {
  color: #2d84fb;
  margin-right: 5px;
  cursor: pointer;
}

.TakeLookPopo {
  box-sizing: border-box;
  padding: 10px;

  .infoFrame-Plus {
    .infoFrame-box-Plus {
      display: flex;

      .infoFrame_icon-Plus {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #2d84fb;
        color: #fff;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 5px 12px 0;
      }

      .infoFrame-info-Plus {
        display: flex;
        flex-direction: column;
        color: #303133;

        & span {
          font-size: 12px;
          margin-top: 5px;
        }

        .infoFrame-phone-Plus {
          display: flex;
          align-items: center;

          .numberTop-Plus {
            background: #afb5b42e;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            margin-left: 5px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.addPicture {
  .uploader-create {
    .el-upload {
      position: relative;

      .uploader-actions {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        cursor: default;
        text-align: center;
        color: #fff;
        opacity: 0;
        font-size: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s;

        .uploader-actions-item {
          margin: 0 10px;
          font-size: 14px;
          cursor: pointer;

          & i {
            color: #fff;
          }
        }
      }

      .uploader-actions:hover {
        opacity: 1;
      }
    }
  }
}

.mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 60px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
      object-fit: cover;
    }

    .img {
      max-width: 800px;
      height: 600px;
      overflow-y: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.dialog_customer_label {
  height: 560px;
  overflow-y: auto;
}

.customer-status-box {
  margin-top: 20px;

  .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .el-radio {
      margin: 0 0 10px 0;
    }

    .el-radio:last-child {
      margin: 0;
    }
  }
}

.intention {
  max-width: 390px;
  text-align: left;
  line-height: 18px;
}

.warn-title {
  .el-tabs__nav-wrap::after {
    height: 0px;
  }

  .el-tabs__item {
    font-weight: 700;
    font-size: 18px;
  }

  .el-tabs__active-bar {
    height: 0px;
  }
}

.button-disabled {
  background: #909399;
  border-color: #909399;

  &:hover {
    background: #a6a9ad;
    border-color: #a6a9ad;
  }

  &:active {
    background: #82848a;
    border-color: #82848a;
  }
}

.tooltClass {
  margin-left: 54px;

  .popper__arrow {
    left: initial !important;
  }
}
</style>
  