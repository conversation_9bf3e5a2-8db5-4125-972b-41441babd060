<template>
  <div class="add">
    <!-- <div class="tips">
      <div>
        1.什么是群活码：通过多个群聊配置一个二维码，客户通过扫描二维码加入群聊，当前面的群人数
        达到上限后，自动发送后面的群二维码，从而突破群聊人数限制，实现一码多群功能。
      </div>
      <div>
        2.怎么用：首先给群活码配置接待员工，客户通过群活码添加员工为好友，添加通过后自动向客户
        发送入群引导语和群聊二维码，再通过扫描群聊二维码入群，当二维码到期后可手动更新二维码。
      </div>
    </div> -->
    <el-form label-width="100px">
      <div class="title">基本信息</div>

      <el-form-item label="活码名称">
        <div class="form-item-block">
          <el-input
            placeholder="请输入活码名称"
            v-model="form_params.name"
            style="width: 240px; margin-right: 12px"
          >
          </el-input>
        </div>
      </el-form-item>

      <div class="title">群设置</div>
      <el-form-item label="入群引导语">
        <div class="form-item-block">
          <el-input
            v-model="form_params.wcl_msg"
            style="width: 240px; margin-right: 12px"
            placeholder="请输入入群引导语"
            type="textarea"
          ></el-input>
        </div>
      </el-form-item>
      <el-form-item label="选择群聊">
        <div class="form-item-block">
          <el-select
            @change="onChange"
            v-model="form_params.chat_id_list"
            style="width: 240px; margin-right: 12px"
            placeholder="选择群聊"
            multiple
          >
            <el-option
              v-for="item in group_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="群满自动建群">
        <div class="form-item-block align-center flex-wrap">
          <el-switch
            v-model="form_params.auto_create_room"
            active-color="#2D84FB"
          >
          </el-switch>
        </div>
      </el-form-item>
      <el-form-item label="自动建群名称" v-if="form_params.auto_create_room">
        <div class="form-item-block">
          <el-input
            style="width: 240px; margin-right: 12px"
            v-model="form_params.room_base_name"
          >
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="建群起始序号" v-if="form_params.auto_create_room">
        <div class="form-item-block">
          <el-input
            style="width: 240px; margin-right: 12px"
            v-model="form_params.room_base_id"
          >
          </el-input>
        </div>
      </el-form-item>
    </el-form>
    <div class="footer row align-center">
      <el-button type="text" @click="cancel">取消</el-button>
      <el-button type="primary" @click="subform">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form_params: {
        wcl_msg: '',
        auto_create_room: false,
        room_base_name: '',
        room_base_id: ''
      },
      show_add_member: false,
      isSubmiting: false,
      show_select_dia: false,
      group_list: []
    }
  },
  computed: {
  },
  created() {
    this.getGroups()
  },
  methods: {
    subform() {
      let params = Object.assign({}, this.form_params)
      if (params.auto_create_room) {
        params.auto_create_room = 1
      } else {
        params.auto_create_room = 0
      }
      if (params.auto_create_room) {
        if (!params.room_base_name) {
          this.$message.warning("请输入自动建群名称")
          return
        }
        if (!params.room_base_id) {
          this.$message.warning("请输入自动建群起始值")
          return
        }
      }
      if (params.room_base_id == '') {
        delete params.room_base_id
      }
      if (params.chat_id_list.length) {
        params.chat_id_list = params.chat_id_list.join(",")
      }

      if (this.isSubmiting) return
      this.isSubmiting = true

      this.$http.addCrmMyGroupQrcode(params).then(res => {
        if (res.status == 200) {
          this.$message.success("添加成功");
          setTimeout(() => {
            this.isSubmiting = false
          }, 200);
          this.$emit("success")
        } else {
          this.isSubmiting = false
          this.$message.error("添加失败");
        }
      }).catch(() => {
        this.isSubmiting = false
      })
    },
    async getGroups() {
      this.$http
        .getCrmCustomerMyGroupDataEdit({
          params: {
            page: 1, per_page: 1000
          }
        })
        .then((res) => {
          if (res.status === 200) {
            this.group_list = res.data.data;

          }
        });
    },
    onChange() { },
    cancel() {
      this.$emit('cancel')
    },

  }
}
</script>

<style lang ="scss" scoped>
.footer {
  padding: 20px 40px;
  .el-button {
    margin-right: 20px;
  }
}

.form-item-block.imgurl {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  &.border {
    padding: 20px;
    border: 1px solid #f8f8f8;
    border-radius: 10px;
    .tip {
      max-width: 200px;
      white-space: normal;
      margin-top: 5px;
      line-height: 22px;
    }
  }
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
.tips {
  padding: 15px 30px;
  background: #e7f3fd;
  color: #8a929f;
  font-size: 14px;
  line-height: 20px;
}
.tip {
  color: #8a929f;
  font-family: PingFang SC;
  font-weight: regular;
  font-size: 12px;
}
.add {
  max-height: 70vh;
  overflow-y: auto;
  .title {
    color: #2e3c4e;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 16px;
    margin: 20px 0;
    font-weight: 600;
  }
}

.left,
.right {
  padding: 10px;
  border-top: 1px solid #e9e9e9;
}
.left {
  border-right: 1px solid #e9e9e9;
}

.select_title {
  font-size: 16px;
  font-weight: 600;
  color: #666;
}
.selected_list {
  padding: 10px 0;
  .selected_item {
    padding: 5px 10px;
    color: #2e3c4e;
    font-size: 14px;
    .delete {
      font-size: 22px;
      cursor: pointer;
    }
    .name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }
}
.img_list {
  flex-wrap: wrap;
  max-height: 375px;
  overflow-y: auto;
  .img {
    width: 120px;
    height: 120px;
    /* margin-right: 26px; */
    border: 1px solid #fff;
    position: relative;
    overflow: hidden;
    &.active {
      border: 1px solid #409eff;
      .checked {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 20px;
        height: 20px;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
.form-item-block.img_url {
  ::v-deep .el-upload--picture-card {
    width: auto;
    height: auto;
    line-height: 1;
    border: 0;
    margin-right: 5px;
  }
}
.form-item-block {
  img {
    width: 148px;
    height: 148px;
    object-fit: cover;
  }
}
</style>