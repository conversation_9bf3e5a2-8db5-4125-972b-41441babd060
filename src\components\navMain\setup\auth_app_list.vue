<template>
  <el-container v-loading="is_table_loading">
    <el-header>
      <el-button
        v-if="$hasShow('添加应用授权')"
        :icon="isTab ? 'el-icon-plus' : 'el-icon-arrow-left'"
        type="primary"
        @click="addApp"
        >{{ isTab ? "添加" : "返回" }}</el-button
      >
     <new_tips_list :tipsList="tipsList"></new_tips_list>
    </el-header>
    <el-main v-if="isTab">
      <myTable :table-list="tableData" :header="table_header"></myTable>
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :currentPage="params.currentPage"
          :pagesize="params.pagesize"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-main>
    <el-main v-else>
      <el-form
        :model="ip_list"
        label-width="100px"
        label-position="left"
        ref="ip_list"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            placeholder="请输入内容"
            style="width:200px"
            v-model="ip_list.name"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-for="(item, index) in ip_list.list"
          :prop="'list.' + index + '.value'"
          :key="item.key"
          :label="'IP白名单' + index"
        >
          <el-input
            placeholder="请输入IP地址"
            style="width:200px"
            v-model="item.value"
          ></el-input>
          <el-button type="danger" @click.prevent="removeItem(item)"
            >删除</el-button
          >
        </el-form-item>
        <el-form-item size="large">
          <el-button type="success" @click="addItem">新增IP</el-button>
          <el-button type="primary" @click="onSubmit('ip_list')"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </el-main>
    <el-dialog title="修改" :visible.sync="dialogUpdata">
      <el-form :model="update_form" label-width="100px" label-position="left">
        <el-form-item label="名称">
          <el-input
            placeholder="请输入内容"
            style="width:200px"
            v-model="update_form.name"
          ></el-input>
        </el-form-item>
        <el-form-item label="IP白名单">
          <el-input
            type="textarea"
            placeholder="多个IP请使用','隔开"
            v-model="update_form.ip_white_list"
          ></el-input>
        </el-form-item>
        <el-form-item size="large">
          <el-button type="primary" @click="onUpData">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import new_tips_list from "@/components/components/new_tips_list";
export default {
  name: "auth_app_list",
  components: {  myPagination, myTable,new_tips_list },
  data() {
    return {
      isTab: true,
      tableData: [],
      params: {
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      create_form: {
        name: "",
        ip_white_list: "",
      },
      ip_list: {
        name: "",
        list: [
          {
            value: "",
          },
        ],
      },
      update_form: {},
      dialogUpdata: false,
      tipsList: ["多个IP请使用,隔开"],
      table_header: [
        { prop: "id", label: "ID", width: "80" },
        { prop: "secret_key", label: "密钥" },
        { prop: "app_id", label: "app id" },
        { prop: "name", label: "名称" },
        { prop: "created_at", label: "创建时间" },
        {
          label: "操作",
          fixed: "right",
          width: "200",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("编辑应用授权") ? (
                  <el-button
                    icon="el-icon-edit"
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.updateData(data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除应用授权") ? (
                  <el-button
                    icon="el-icon-delete"
                    type="danger"
                    size="mini"
                    onClick={() => {
                      this.deleteData(data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      this.$http.getAuthAppList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    addApp() {
      this.isTab = !this.isTab;
    },
    deleteData(row) {
      this.$confirm("此操作将删除该内容, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteAuthAppList(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    updateData(row) {
      this.queryAuthAppList(row.id);
      this.dialogUpdata = true;
    },
    queryAuthAppList(ip) {
      this.$http.queryAuthAppList(ip).then((res) => {
        if (res.status === 200) {
          this.update_form = res.data;
        }
      });
    },
    onUpData() {
      this.$http.updataAuthApplist(this.update_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "修改成功",
            type: "success",
          });
          this.getDataList();
          this.dialogUpdata = false;
        }
      });
    },
    onSubmit(fromName) {
      let arr = this.ip_list.list.map((item) => {
        return item.value;
      });
      this.create_form.ip_white_list = arr.toString();
      this.create_form.name = this.ip_list.name;
      if (!this.create_form.name) {
        this.$message({
          message: "请输入名称",
          type: "error",
        });
        return;
      }
      this.$http.createAuthAppList(this.create_form).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "上传成功",
            type: "success",
          });
          this.$refs[fromName].resetFields();
          this.getDataList();
          this.addApp();
        }
      });
    },
    removeItem(item) {
      var index = this.ip_list.list.indexOf(item);
      if (index !== -1) {
        this.ip_list.list.splice(index, 1);
      }
    },
    addItem() {
      this.ip_list.list.push({
        value: "",
        key: Date.now(),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pagination-box {
  margin: 20px;
}
.el-main {
  margin-top: 80px;
}
</style>
