<template>
    <div class="container">
      <div class="project_title">项目管理</div>
      <div class="flex-row">
        <div 
          v-for="(item, index) in projectData"
          :key="index"
          class="project_manualAllot"
        >
          <div>
            <el-radio 
              v-model="projectSave_params.type" 
              :label="item.values"
            >
                {{ item.name }}
            </el-radio>
          </div>
          <div class="project_prompt">{{ item.des }}</div>
        </div>
      </div>
      <div v-show="projectSave_params.type == 1">
        <div class="project_form">添加项目</div>
        <div>
            <el-form ref="formAdd" inline :model="projectAdd_params" label-width="68px">
                <el-form-item label="所属区域" prop="area">
                    <tRegionCascader width="288px" v-model="projectAdd_params.regions"/>
                </el-form-item>
                <el-form-item label="项目名称" prop="name">
                    <el-input style="width: 288px;" v-model="projectAdd_params.name" maxlength="50" placeholder="请输入项目名称 最多50字符"></el-input>
                </el-form-item>
                <el-form-item label="封面图">
                    <el-upload class="uploader-create" :disabled="disabled_picture" :headers="myHeader"
                  :action="picture_upurl" :on-success="(e) => UploadParamsSuccess(e)" accept=".jpg,.png"
                  :show-file-list="false" :multiple="true">
                  <el-link type="primary" @click="addPictures($event)"><i class="el-icon-plus"></i>图片</el-link>
                </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="confirmAdd">添加项目</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <div class="picture_list_box" v-for="(item, index) in imgList" :key="index">
                <img v-if="item.url" :src="item.url" class="photo-item-img" />
                <div class="delete-picture" @click="deletePicture(index)">
                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
                <span class="uploader-actions" v-if="item.url">
                  <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                    <i class="el-icon-view"></i>
                  </span>
                </span>
              </div>
        </div>
        <div class="project_form project_form-add">
            <div>项目列表</div>
            <div class="search-wrapper">
                <el-input placeholder="搜索项目名称" size="small" v-model="projectList_params.keywords">
                    <template #append>
                        <el-button icon="el-icon-search" @click="searchProject"></el-button>
                    </template>
                </el-input>
            </div>
        </div>

        <div class="project_tableBox">
            <myTable
                v-loading="is_table_loading"
                :table-list="project_fromData"
                :header="table_header"
                :header-cell-style="{ background: '#EBF0F7' }"
                highlight-current-row
                :row-style="$TableRowStyle"
            ></myTable>
            <div class="project-page">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page.sync="projectList_params.page"
                    :page-size="10"
                    layout="total, prev, pager, next"
                    background
                    :total="tableTotle">
                </el-pagination>
            </div>
        </div>
      </div>
      <!-- 保存 -->
      <div class="footer_btn">
        <el-button :loading="is_loading" type="primary" @click="onSave">保存</el-button>
      </div>
      <el-dialog
        title="编辑项目"
        :visible.sync="showEditDialog"
        width="420px"
      >
        <el-form ref="formEdit" inline :model="projectEdit_params" label-width="68px">
            <el-form-item label="所属区域">
                <tRegionCascader width="300px" v-model="projectEdit_params.regions"/>
            </el-form-item>
            <el-form-item label="项目名称">
                <el-input style="width: 300px;" v-model="projectEdit_params.name" maxlength="50" placeholder="请输入项目名称 最多50字符"></el-input>
            </el-form-item>
            <el-form-item label="封面图">
                    <el-upload class="uploader-create" :disabled="setdisabled_picture" :headers="myHeader"
                  :action="picture_upurl" :on-success="(e) => setUploadParamsSuccess(e)" accept=".jpg,.png"
                  :show-file-list="false" :multiple="true">
                  <el-link type="primary" @click="setaddPictures($event)"><i class="el-icon-plus"></i>图片</el-link>
                </el-upload>
                </el-form-item>
        </el-form>
        <div>
            <div class="picture_list_box" v-for="(item, index) in setimgList" :key="index">
                <img v-if="item.url" :src="item.url" class="photo-item-img" />
                <div class="delete-picture" @click="setdeletePicture(index)">
                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
                <span class="uploader-actions" v-if="item.url">
                  <span class="uploader-actions-item" @click="handlePictureCardPreview(item, index)">
                    <i class="el-icon-view"></i>
                  </span>
                </span>
              </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="showEditDialog = false">取 消</el-button>
            <el-button type="primary" @click="confirmEdit" :loading="is_loading">确 定</el-button>
        </span>
    </el-dialog>
     <!-- 查看已上传图片模态框 -->
     <div class="mask1" v-if="show_dialog_pictures" @click="show_dialog_pictures = false">
      <div class="preview_img" @click.prevent.stop="() => { }">
        <img id="preImg" :src="dialog_pictures_src" alt="" />
      </div>
    </div>
        <!-- 项目库 -->
        <Project_library ref="Projectlibrary" ></Project_library>
    </div>
</template>
<script>
import myTable from "@/components/components/my_table";
import tRegionCascader from '@/components/tplus/tCascader/tRegionCascader.vue';
import config from "@/utils/config.js";
import Project_library from '@/components/tplus/tSelect/Project_library.vue';
export default {
    components: {
        myTable,
        tRegionCascader,
        Project_library
    },
    data() {
        return {
            projectSave_params: {
                type: 1
            },
            projectData: [], // 当前站点可用选项
            is_loading: false, // loading加载动画
            // 添加项目接口参数
            projectAdd_params: {
                regions: [], // 所属区域 最多20字
                name: "", // 项目名称 最多30字
            },
            // 编辑项目接口参数
            projectEdit_params: {
                id: "", // 项目id
                regions: [], // 所属区域 最多20字
                name: "", // 项目名称 最多30字
            },
            // 项目列表接口请求参数
            projectList_params: {
                page: 1,
                per_page: 10,
                keywords: "",
            },
            tableTotle: 0, // tabs列表总条数
            project_fromData: [], // 项目列表容器
            is_table_loading: false, // table表格loading
            // table表头参数
            table_header: [
                {
                    label: "项目名称",
                    render: (h, data) => {
                        return (
                            <div>
                                <div class="text-dispose" onClick={() => {
                                    this.ProjectDynamics(data.row);
                                }}>{data.row.name}</div>
                            </div>
                        )
                    }
                },
                {
                    label: "所属区域",
                    render: (h, data) => {
                        return (
                            <div>
                                {data.row.province_id? data.row.province_name+'/'+data.row.city_name+'/'+data.row.area_name : ''}
                            </div>
                        )
                    }
                },
                {
                    label: "创建时间",
                    prop: "created_at",
                },
                {
                    label: "更新时间",
                    prop: "updated_at",
                },
                {
                    label: "操作",
                    render: (h, data) => {
                        return (
                            <div>
                                <el-button 
                                    type="primary"
                                    size="mini" 
                                    onClick={() => {
                                        this.editProject(data.row);
                                    }}
                                >
                                    编辑
                                </el-button>
                                <el-button 
                                    type="danger"
                                    size="mini" 
                                    icon="el-icon-delete"
                                    onClick={() => {
                                        this.removeProject(data.row);
                                    }}
                                >
                                    删除
                                </el-button>
                            </div>
                        )
                    }
                }
            ],
            showEditDialog: false, // 控制编辑项目模态框
            disabled_picture: false, // 是否禁用上传图片
            setdisabled_picture:false,//编辑是否禁用上传图片
            picture_upurl: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`, // 上传图片url
            imgList: [],// 上传图片列表  
            setimgList:[],
            show_dialog_pictures: false, // 查看已上传的图片
            website_id:"",//站点id
        }
    },
    created() {
        if (this.$route.query.website_id) {
          this.website_id = this.$route.query.website_id
        }
        this.getProjectList(); // 获取当前站点可用项目选项
        this.getCrmProjectList(); // 获取项目列表
    },
    computed: {
    // 获取请求头
    myHeader() {
      return {
        // Authorization: "Bearer " + localStorage.getItem("TOKEN"),
        Authorization: config.TOKEN,
      };
    },
    },
    methods: {
        // 获取当前站点可用项目选项
        getProjectList() {
            this.$http.getProjectAdminList().then((res) => {
                if(res.status == 200) {
                    console.log(res.data,"项目数据")
                    this.projectData = res.data.config; // 赋值项目列表
                    this.projectSave_params.type = res.data.type; // 赋值当前选择的项目
                }
            })
        },
        // 保存
        onSave() {
            this.is_loading = true; // 开启loading动画
            this.$http.saveProjectAdmin(this.projectSave_params).then((res) => {
                if(res.status == 200) {
                    this.is_loading = false; // 关闭loading动画
                    this.$store.commit("setCrmConfigProjectType", this.projectSave_params.type);
                    this.$message.success("操作成功");
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 确定添加项目
        confirmAdd() {
            if(this.projectAdd_params.regions.length == 0) {
                return this.$message.warning("请选择所属区域");
            }
            if(this.projectAdd_params.name == "" || this.projectAdd_params.name == undefined) {
                return this.$message.warning("请输入项目名称");
            }
            
            const params = {...this.projectAdd_params};
            params.province_id = params.regions[0];
            params.city_id = params.regions[1] || 0;
            params.area_id = params.regions[2] || 0;
            if(this.imgList.length){
              params.cover_image = this.imgList[0].url||""  
            }
            delete params.regions;

            this.is_loading = true; // 加载loading动画
            this.$http.addCrmProject(params).then((res) => {
                if(res.status == 200) {
                    this.is_loading = false; // 关闭loading动画
                    this.$message.success("添加成功");
                    this.$refs.formAdd.resetFields();
                    this.projectAdd_params.regions = [];
                    this.imgList = []
                    this.projectList_params.page = 1; // 页码重置为 1
                    this.getCrmProjectList(); // 刷新项目列表
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 获取项目列表
        getCrmProjectList() {
            this.is_table_loading = true; // 显示loading
            this.$http.getCrmProjectList(this.projectList_params).then((res) => {
                if(res.status == 200) {
                    this.is_table_loading = false; // 关闭loading
                    this.project_fromData = res.data.data;
                    this.tableTotle = res.data.total;
                    // console.log(this.project_fromData,"项目列表")
                } else {
                    this.is_table_loading = false;
                }
            }).catch(() => {
                this.is_table_loading = false;
            })
        },
        // 点击上传图片
        addPictures() {
          // 判断图片列表的个数
          if (this.imgList.length == 1) {
            this.disabled_picture = true; // 禁用上传
            this.$message({
              message: '最多只能上传1张封面图',
              type: 'warning'
            });
          } else {
            this.disabled_picture = false; // 启用上传
          }
        },
        // 点击上传图片
        setaddPictures() {
          // 判断图片列表的个数
          if (this.setimgList.length == 1) {
            this.setdisabled_picture = true; // 禁用上传
            this.$message({
              message: '最多只能上传1张封面图',
              type: 'warning'
            });
          } else {
            this.setdisabled_picture = false; // 启用上传
          }
        },
        // 上传图片成功
        UploadParamsSuccess(e) {
          this.imgList.unshift(e);
        },
        // 上传图片成功
        setUploadParamsSuccess(e) {
          this.setimgList.unshift(e);
        },
        // 删除已上传的图片
        deletePicture(index) {
          this.imgList.splice(index, 1);
        },
         //编辑删除已上传的图片
        setdeletePicture(index){
            this.setimgList.splice(index, 1);
        },
        // 查看已上传的图片
        handlePictureCardPreview(item) {
          // console.log(item,'item');
          this.show_dialog_pictures = true;
          if (item.url) {
            this.dialog_pictures_src = item.url;
          } else {
            this.dialog_pictures_src = item;
          }
        },
        //点击名称激活右侧项目库动态
        ProjectDynamics(row){
            // console.log(row);
            this.$refs.Projectlibrary.open(row);
        },
        // 显示编辑项目模态框
        editProject(row) {
            this.setimgList = []
            //省市区
            const regions = [];
            if(row.province_id && row.city_id && row.area_id){
                regions.push(row.province_id);
                regions.push(row.city_id);
                regions.push(row.area_id);
            }
            if(row.cover_image){  
                this.setimgList.push({url:row.cover_image})
            }
            this.showEditDialog = true; // 显示模态框
            this.projectEdit_params.id = row.id; // 赋值当前项目id
            this.projectEdit_params.regions = regions; // 赋值当前所属区域
            this.projectEdit_params.name = row.name; // 赋值当前项目名称
        },
        // 删除项目
        removeProject(row) {
            this.$confirm('此操作将永久删除该项目, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            type: 'warning'
            }).then(() => {
                this.$http.deleteCrmProject(row.id).then((res) => {
                    if(res.status == 200) {
                        this.$message.success("删除成功");
                        // 如果项目列表只有一列
                        if(this.project_fromData.length == 1) {
                            this.projectList_params.page = 1; // 将页码重置为 1
                        }
                        this.getCrmProjectList(); // 重新获取项目列表
                    }
                })
            }).catch(() => {
                return;
            });
        },
        // 编辑项目
        confirmEdit() {
            if(this.projectEdit_params.regions.length ==  0) {
                return this.$message.warning("请选择所属区域");
            }
            if(this.projectEdit_params.name == "" || this.projectEdit_params.name == undefined) {
                return this.$message.warning("请输入项目名称");
            }
            const params = {...this.projectEdit_params};
            params.province_id = params.regions[0];
            params.city_id = params.regions[1] || 0;
            params.area_id = params.regions[2] || 0;
            delete params.regions;
            if(this.setimgList.length){
                params.cover_image = this.setimgList[0].url
            }
            this.is_loading = true; // 显示loading
            this.$http.addCrmProject(params).then((res) => {
                if(res.status == 200) {
                    this.is_loading = false; // 关闭loading
                    this.showEditDialog = false; // 关闭模态框
                    this.$message.success("编辑成功");
                    this.setimgList = []
                    this.getCrmProjectList(); // 重新获取项目列表
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
        },
        // 切换分页
        handleCurrentChange(value) {
            this.projectList_params.page = value;
            this.getCrmProjectList(); // 重新获取项目列表
        },
        //搜索项目
        searchProject(){
            this.projectList_params.page = 1;
            this.getCrmProjectList();
        }
    }
}
</script>
<style lang="scss" scoped>
.container {
    .project_title {
        color: #2e3c4e;
        margin-bottom: 24px;
    }
    .project_manualAllot {
        width: 304px;
        background-color: #f8f8f9;
        padding: 20px;
        box-sizing: border-box;
        border-radius: 6px;
        margin-right: 20px;
        .el-radio {
            .el-radio__label {
                color: #2e3c4e;
            }
        }
        .project_prompt {
            font-size: 12px;
            color: #8a929f;
            margin-left: 24px;
            margin-top: 10px;
        }
    }
    .footer_btn {
        margin-top: 30px;
        .el-button {
            padding: 10px 30px;
        }
    }
    .project_form {
        color: #2e3c4e;
        margin: 24px 0;
        font-size: 14px;
        &.project_form-add{
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            width: 950px;
            margin: 18px 0 17px 0
        }   
    }
    .picture_list_box {
  width: 48px;
  height: 48px;
  border: 1px solid #eaeaec;
  border-radius: 4px;
  background-color: #e2e2e2;
  margin-left: 12px;
  position: relative;

  .photo-item-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .delete-picture {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    z-index: 2;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .uploader-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 4px;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;

    .uploader-actions-item {
      font-size: 20px;
      cursor: pointer;

      & i {
        color: #fff;
      }
    }
  }

  .uploader-actions:hover {
    opacity: 1;
  }
}
    .project_tableBox {
        width: 950px;
        .project-page {
            text-align: end;
            margin-top: 24px;
        }
    }
    .mask1 {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  top: 60px;
  bottom: 0;
  right: 0;
  left: 230px;
  padding: 10px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;

  .preview_img {
    position: relative;

    img {
      max-width: 1000px;
      object-fit: cover;
    }

    .img {
      max-width: 800px;
      height: 600px;
      overflow-y: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
}
::v-deep .text-dispose {
    max-width: 172px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
</style>