<template>
  <div class="pages">
    <template v-if="is_tabs !== 'wxwork'">
      <!--:is 的作用：会将div标签转换成 currentView 变量绑定的这个组件-->
      <!-- <div :is="is_tabs" keep-alive></div> -->
      <div class="content-box-crm" style="margin-bottom: 24px">
        <div class="bottom-border div row">
          <span class="text">客户来源：</span>
          <myLabel labelKey="title" :arr="source_list" @onClick="onClickType($event, 1)"></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户状态：</span>
          <myLabel labelKey="title" :arr="tracking_list" @onClick="onClickType($event, 2)"></myLabel>
        </div>
        <div class="bottom-border div row" style="padding-top: 24px">
          <span class="text">客户标签：</span>
          <div class="label_list div row">
            <span class="selected-header" :class="is_all ? 'label_actions' : ''" @click="getAllLabelList">
              全部
            </span>
            <span v-for="(item, index) in label_list" :key="index" class="label_item">
              <el-select class="selected-label" v-model="customerLabelList[item.id]" @change="changeCustomerLabel"
                :placeholder="item.name" :style="{
                                  minWidth: '40px',
                                  width: getSelectWidth(item, customerLabelList[item.id]),
                                  background: changeParentLabel == item.id ? '#E8F1FF' : '',
                                }">
                <el-option v-for="arr in item.label" :key="arr.id" :label="arr.name" :value="[arr.id, arr, item]">
                </el-option>
              </el-select>
            </span>
          </div>
        </div>
        <!-- 折叠面板 -->
        <myCollapse :isActive="is_collapse">
          <template v-slot:content>
            <div class="bottom-border div row" style="padding-top: 24px">
              <span class="text">客户类型：</span>
              <myLabel labelKey="title" :arr="type_list" :isdesc="true" @onClick="onClickType($event, 6)"></myLabel>
            </div>
            <div class="bottom-border div row" style="padding-top: 24px">
              <span class="text">绑定企微：</span>
              <myLabel labelKey="name" :arr="bind_list" @onClick="onClickType($event, 3)"></myLabel>
            </div>

            <div class="bottom-border div row" style="padding-top: 24px">
              <span class="text">筛选时间：</span>
              <myLabel ref="childRef" :arr="time_list" @onClick="onClickType($event, 4)"></myLabel>
              <span class="text">自定义：</span>
              <el-date-picker style="width: 250px" size="small" v-model="timeValue" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="onChangeTime">
              </el-date-picker>
            </div>
          </template>
        </myCollapse>
        <div class="div row loadmore" @click="onChangeCollapse">
          <span class="text"> 更多 </span>
          <span :class="is_collapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></span>
        </div>
      </div>
      <div class="content-box-crm">
        <div class="table-top-box div row">
          <div class="t-t-b-left div b-tabs row">
            <!-- <div
              v-for="(date, index) in type_list"
              :key="index"
              @click="changeTab(date, 2)"
              :class="{ isactive: params.type == date.id }"
              class="b-t-item"
            >
              <div class="date_value">
                {{ date.title }}
              </div>
            </div> -->
            <!-- 新线索 -->
            <div v-for="item in customer_list_type" :key="item.id" @click="getDataLists(item)" class="b-t-item"
              :class="{ isactive: params.sort_type == item.id }">
              <span v-if="item.is_select != true">{{ item.title }}</span>
              <span v-else>
                {{ item.title }}
                <el-popover v-model="is_pullDown" placement="bottom" width="150" trigger="click">
                  <div class="screen-type">
                    <div class="screen-type-content" v-for="list in screen_list_type" :key="list.id"
                      @click="changeScreenType(list, item)">
                      {{ list.title }}
                    </div>
                  </div>
                  <span slot="reference">
                    <span @click="showTypeStatus($event)" :class="
                                            item.is_select == true ? 'el-icon-caret-bottom' : null
                                          ">
                    </span>
                  </span>
                </el-popover>
              </span>
            </div>
          </div>
          <div class="t-t-b-right div row">
            <el-button style="font-size: 14px" @click="is_push_customer = true" type="primary" size="mini"
              class="btn el-icon-plus">录入客户</el-button>
            <el-button style="font-size: 14px" v-if="is_show_upload" @click="getFile" type="primary"
              size="mini">导入</el-button>
            <el-button style="font-size: 14px" type="primary" size="mini" @click="TransferCustomer">
              转交
            </el-button>
            <el-button style="font-size: 14px" type="primary" @click="setCustomerLabel" size="mini">
              设置标签
            </el-button>
            <el-popover placement="bottom" width="500px" v-model="show_tel_search">
              <div>
                <div>搜索</div>
                <div class="inps div row align-center" style="margin: 10px 0">
                  <el-input placeholder="请输入手机号" v-model="params.mobile"
                    style="margin-right: 10px; width: 180px"></el-input>
                </div>
                <div class="btns" style="text-align: right">
                  <el-button @click="resetLoudongSearch()">重置</el-button>
                  <el-button type="primary" @click="handleSearch">确定</el-button>
                </div>
              </div>
              <div class="search_loudong div row align-center" slot="reference">
                <div class="seach_value">电话</div>
                <div class="sanjiao" :class="{ transt: show_tel_search }"></div>
              </div>
            </el-popover>
            <el-popover v-model="pop_depart" placement="bottom" width="500px" trigger="click">
              <div class="flex-box">
                <div>搜索</div>
                <div style="margin: 10px 0" class="flex-row">
                  <el-cascader class="inp_no_border" v-model="params.department_id" placeholder="请选择部门"
                    :options="AllDepartment" @change="changePopDepar" :clearable="true" :show-all-levels="false" :props="{
                                          label: 'name',
                                          value: 'id',
                                          children: 'subs',
                                          checkStrictly: true,
                                          emitPath: false,
                                        }">
                  </el-cascader>
                  <el-button slot="append" icon="el-icon-search" style="
                      background: #f5f7fa;
                      border-left: none;
                      border-top-left-radius: 0;
                      border-bottom-left-radius: 0;
                    " @click="searchDepart"></el-button>
                </div>
                <div class="flex-row">
                  <el-select class="search-member-box" v-model="selectedMember" placeholder="请选择成员"
                    @change="changeSearchMember" clearable>
                    <el-option v-for="item in filtrMember" :key="item.id" :label="item.user_name" :value="item.id">
                    </el-option>
                  </el-select>
                  <el-button slot="append" icon="el-icon-search" style="
                      background: #f5f7fa;
                      border-left: none;
                      border-top-left-radius: 0;
                      border-bottom-left-radius: 0;
                    " @click="searchMember">
                  </el-button>
                </div>
              </div>
              <div slot="reference" @click="getCrmDepartmentList" class="search_loudong div row align-center">
                <div class="seach_value">部门</div>
                <div class="sanjiao" :class="{ transt: pop_depart }"></div>
              </div>
            </el-popover>
            <el-popover placement="bottom" width="500px" v-model="show_xiansuo_search">
              <div>
                <div>搜索</div>
                <div class="inps div row align-center" style="margin: 10px 0">
                  <el-input placeholder="请输入客户线索" v-model="params.keywords"
                    style="margin-right: 10px; width: 180px"></el-input>
                </div>
                <div class="btns" style="text-align: right">
                  <el-button @click="resetXiansuoSearch()">重置</el-button>
                  <el-button type="primary" @click="handleSearch">确定</el-button>
                </div>
              </div>
              <div class="search_loudong div row align-center" slot="reference">
                <div class="seach_value">线索</div>
                <div class="sanjiao" :class="{ transt: show_xiansuo_search }"></div>
              </div>
            </el-popover>
            <!-- <el-input
              size="small"
              placeholder="请输入客户线索"
              style="width: 256px; margin-left: 12px"
              v-model="params.keywords"
              @change="onChangeKeywords"
            >
              <span slot="append" class="el-icon-search"></span>
            </el-input> -->
          </div>
        </div>
        <myTable v-loading="is_table_loading" :table-list="tableData" :header="table_header" select
          :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle"
          @selection-change="selectionChange"></myTable>
        <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
          :total="params.total" :page-size="params.per_page" :current-page="params.page" @current-change="onPageChange">
        </el-pagination>
      </div>
    </template>

    <!-- <wxwork v-if="is_tabs === 'wxwork'"></wxwork> -->
    <el-dialog width="400px" :visible.sync="is_push_customer" title="录入客户" :before-close="cancel"
      :close-on-click-modal="false">
      <!-- <myForm
        @clsoe="is_push_customer = false"
        :data1="n_client_field"
        :data2="n_company_field"
        :form="form"
        :form1="form1"
        @onClick="onClickForm"
      ></myForm> -->
      <div class="i-form-list">
        <el-form inline :model="push_form" label-width="90px" label-position="left">
          <el-form-item label="客户姓名：">
            <div class="row input-box div">
              <el-input placeholder="请输入客户姓名" v-model="push_form.cname"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="客户电话：">
            <div class="row input-box div isbottom" v-for="(domain, index) in other_mobile" :key="index">
              <el-input placeholder="请输入电话号码" v-model="domain.mobile" maxlength="11"></el-input>
              <img v-if="index === 0" class="add" src="https://img.tfcs.cn/backup/static/admin/customer/tianjia.png"
                alt="" @click.prevent="addDomain" />
              <img class="add" v-if="index !== 0" @click.prevent="removeDomain(domain)"
                src="https://img.tfcs.cn/backup/static/admin/customer/jianqu.png" alt="" />
            </div>
          </el-form-item>
          <el-form-item label="客户来源：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.source_id" placeholder="请选择">
                <el-option v-for="item in sourceLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户性别：">
            <div class="row input-box div">
              <!-- <el-select
                style="width:100%"
                v-model="push_form.sex"
                placeholder="请选择"
              >
                <el-option label="男" :value="1"></el-option>
                <el-option label="女" :value="2"></el-option>
              </el-select> -->
              <div class="sex-box div row">
                <img v-for="item in sex_list" :key="item.id" :class="{ is_active: item.id === push_form.sex }"
                  :src="`https://img.tfcs.cn/backup/static/admin/customer/${item.name}.png`" alt="" @click="() => {
                                        push_form.sex = item.id;
                                      }
                                      " />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户级别：">
            <div class="row input-box div">
              <div @click="onClickLevel(item)" class="l-item row" v-for="item in levelLabel" :key="item.id" :class="{
                              lisactive: item.id === push_form.level_id,
                            }">
                <div class="l-name">{{ item.title }}</div>
                <div class="l-name-1">{{ item.desc }}</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="客户类型：">
            <div class="input-box">
              <el-select style="width: 100%" v-model="push_form.type" placeholder="请选择">
                <el-option v-for="item in typeLabel" :key="item.id" :label="item.title" :value="item.id">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="客户标签：">
            <div class="input-box">
              <el-cascader style="width: 100%" v-model="push_form.label" clearable placeholder="请选择标签"
                :options="label_default_list" :props="{
                                  value: 'id',
                                  label: 'name',
                                  children: 'label',
                                  emitPath: false,
                                  multiple: true,
                                }">
              </el-cascader>
            </div>
          </el-form-item>
          <el-form-item label="客户意向：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.intention_community"></el-input>
            </div>
          </el-form-item>
          <!-- <el-form-item label="意向区域：">
            <div class="row input-box div">
              <el-input
                placeholder="请输入"
                v-model="push_form.intention_street"
              ></el-input>
            </div>
          </el-form-item> -->
          <el-form-item label="备注：">
            <div class="row input-box div">
              <el-input placeholder="请输入" v-model="push_form.remark" type="textarea" :rows="4"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer flex-row align-center">
        <el-switch v-model="push_form.add_type" active-color="#13ce66" inactive-color="#2d84fb" inactive-value="1"
          active-value="2" active-text="公海" inactive-text="私客">
        </el-switch>
        <div class="flex-1 flex-row" style="justify-content: flex-end">
          <el-button @click="cancel">取 消</el-button>
          <el-button v-loading="is_button_loading" :disabled="is_button_loading" type="primary" @click="onClickForm">确
            定</el-button>
        </div>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_follow_dialog" title="跟进记录">
      <el-table v-loading="is_follow_loading" :data="is_follow_data" border>
        <el-table-column label="ID" prop="client_follow_id"></el-table-column>
        <el-table-column label="跟进类型" prop="type_title"></el-table-column>
        <el-table-column label="跟进内容" prop="content"></el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="is_follow_params.total" :page-size="is_follow_params.per_page" :current-page="is_follow_params.page"
        @current-change="onPageChangeQwFollow">
      </el-pagination>
    </el-dialog>
    <el-dialog width="660px" :visible.sync="is_dialog_upload" title="导入" :before-close="cancels">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">1、点击下载导入数据模块</el-link><span
          class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <!-- <el-select
        style="width: 200px; margin-bottom: 10px"
        v-model="upload_form.type"
        placeholder="请选择"
      >
        <el-option label="不覆盖" :value="1"></el-option>
        <el-option label="覆盖" :value="2"></el-option>
      </el-select> -->
      <div class="flex-row">
        <!-- 是否覆盖 -->
        <el-select style="width: 200px; margin-bottom: 10px" v-model="upload_form.type" placeholder="请选择是否覆盖数据">
          <el-option label="不覆盖" :value="1"></el-option>
          <el-option label="覆盖" :value="2"></el-option>
        </el-select>
        <!-- 选择分类 -->
        <el-select style="width: 150px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.type_id"
          placeholder="请选择分类" clearable>
          <el-option v-for="item in type_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
        <!-- 选择客户来源 -->
        <el-select style="width: 200px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.source_id"
          placeholder="请选择客户来源" clearable>
          <!-- source_import -->
          <el-option v-for="item in source_list" :key="item.id" :label="item.title" :value="item.id" clearable>
          </el-option>
        </el-select>
      </div>
      <div class="flex-row">
        <!-- 选择成员 -->
        <el-input ref="focusMember" placeholder="请选择成员" v-model="uploadAdmin_id" style="width: 200px; display: block"
          @focus="focusSelete">
          <i v-show="uploadAdmin_id != '' && uploadAdmin_id != undefined" @click="delName" slot="suffix"
            class="el-input__icon el-icon-circle-close" style="cursor: pointer"></i>
        </el-input>
        <!-- 选择标签 -->
        <!-- <el-select
          style="width: 362px; margin-bottom: 10px; margin-left: 12px;"
          v-model="upload_form.label"
          multiple
          placeholder="请选择标签"
        >
          <el-option label="标签1" :value="1"></el-option>
          <el-option label="标签2" :value="2"></el-option>
        </el-select> -->
        <el-cascader style="width: 362px; margin-bottom: 10px; margin-left: 12px" v-model="upload_form.label" clearable
          placeholder="请选择标签" :options="label_list" :props="{
                      value: 'id',
                      label: 'name',
                      children: 'label',
                      emitPath: false,
                      multiple: true,
                    }">
        </el-cascader>
      </div>
      <!-- 客户备注线索 -->
      <div class="clueRemark">
        <el-input type="textarea" :rows="2" placeholder="请输入客户备注线索" v-model="upload_form.remark">
        </el-input>
      </div>
      <!-- <el-button type="primary" @click="$refs.file.click()" class="el-icon-plus"
        >添加文件</el-button
      > -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancels">取 消</el-button>
        <el-button type="primary" @click="startImport" :loading="is_loading">开始导入</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="show_member_list" width="660px" title="选择成员">
      <multipleTree ref="memberLists" v-if="show_member_list" :list="memberList" :defaultValue="selectedIds"
        @onClickItem="selecetedMember" :defaultExpandAll="false">
      </multipleTree>
    </el-dialog>
    <el-dialog title="客户标签" :visible.sync="show_Customer_label" width="660px">
      <div class="dialog_customer_label">
        <div v-for="(item, index) in labels_list" :key="item.id">
          <div class="labelname">{{ item.name }}</div>
          <div class="lables-box div row">
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.taggroup" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
            <div class="labels-item" :class="{ checked: i1.check }" v-for="(i1, i2) in item.label" :key="i2"
              @click="checkChangeLabels(index, i2, i1.id)">
              {{ i1.name }}
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="show_Customer_label = false">取 消</el-button>
        <el-button type="primary" @click="confirmSelected" :loading="is_loading">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="is_transfer_customer" title="转交客户">
      <div class="tips">
        <div>提示语：转交后跟进维护人将变更</div>
      </div>
      <el-input v-model="admin_params.user_name" placeholder="请输入用户名" style="width: 200px"></el-input>
      <el-button type="primary" @click="onAdminSearch">搜索</el-button>
      <el-table style="margin-top: 10px" :data="admin_list" border>
        <!-- <el-table-column label="ID" prop="id"></el-table-column> -->
        <el-table-column label="用户名" prop="user_name"></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-link type="primary" @click="onZhuanrang(scope.row)">转交客户</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
        :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
        @current-change="PersPageChange">
      </el-pagination>
    </el-dialog>

    <!-- 快速编辑客户维护资料模态框 -->
    <my-Maintenance v-if="show_cus_Edit" :show_cus_Edit="show_cus_Edit" :source_list="ponent_maintain_source"
      :level_list="ponent_maintain_level" :type_list="ponent_maintain_type" :label_list="ponent_maintain_label"
      :data_list="ponent_maintain_data" @fastCloseEdit="fastCloseEdit" @submitMaintain="submitMaintain">
    </my-Maintenance>
    <!-- 快速查看客户手机号模态框 -->
    <my-LookTel v-if="show_look_Tel" :show_look_Tel="show_look_Tel" :ponent_Tel_data="ponent_Tel_data" :webInfo="webInfo"
      @fastCloseTel="fastCloseTel" @fastSubmitTel="fastSubmitTel"></my-LookTel>

    <!-- 快速提交客户审批 -->
    <my-Examine v-if="show_Examine_dialog" :show_Examine_dialog="show_Examine_dialog"
      :ponent_Examine_data="ponent_Examine_data" :ponent_Examine_stutas="ponent_Examine_stutas"
      :ponent_Examine_type="Examine_type" :AllDepartment="AllDepartment" @closeExamine="closeExamine"
      @submitExamineAfter="submitExamineAfter"></my-Examine>
    <!-- 快速跟进客户内容模态框 -->
    <myFollowUp v-if="show_Follow_dialog" :show_Follow_dialog="show_Follow_dialog"
      :ponent_Follow_data="ponent_Follow_data" @addFollowSuccess="addFollowSuccess" @closeFollow="closeFollow">
    </myFollowUp>
    <input v-if="is_dialog_upload" type="file" ref="file" style="display: none" v-on:change="handleFileUpload($event)" />
  </div>
</template>

<script>
// import my from "./components/my";
// import seas from "./components/seas";
// import wxwork from "./components/wxwork";
// import myForm from "./components/customer_form";
import myTable from "@/components/components/my_table";
import myLabel from "./components/my_label.vue";
import myCollapse from "./components/collapse";
import myExamine from "@/components/components/my_Examine.vue"
import myMaintenance from "@/components/components/my_maintenance.vue";
import myLookTel from "@/components/components/my_lookTel.vue"
// import mySelect from "./components/my_select";
import multipleTree from "@/components/navMain/crm/components/my_multipleTree.vue";
import myFollowUp from "@/components/components/my_followUp.vue";
export default {
  name: "crm_customer_seas_list",
  components: {
    // my,
    // seas,
    // myForm,
    // wxwork,
    myTable,
    // mySelect,
    myLabel,
    myCollapse,
    multipleTree,
    myMaintenance,
    myLookTel,
    myExamine,
    myFollowUp,
  },
  data() {
    return {
      //选择成员弹框控制
      show_member_list: false,
      is_tabs: "all",
      selectedIds: [],
      tabs: [
        {
          id: 1,
          name: "所有客户",
          desc: "all",
        },
        {
          id: 2,
          name: "我的客户",
          desc: "my",
        },
        {
          id: 3,
          name: "公海客户",
          desc: "seas",
        },
        // {
        //   id: 4,
        //   name: "企微客户",
        //   desc: "wxwork",
        // },
      ],
      is_push_customer: false,
      push_form: {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        label: "", // 客户标签
        // intention_street: "",
        remark: "",
        add_type: "1",
      },
      sex_list: [
        { id: 1, name: "nan" },
        { id: 2, name: "nv3" },
      ],
      other_mobile: [{ mobile: "" }],
      type_list: [],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      timeValue: "",
      level_list: [],
      source_list: [],
      client_field: {
        // 获取客户字段
        type: 2,
      },
      n_client_field: {},
      n_company_field: {},
      form: {},
      form1: {},
      type: 1,
      tracking_list: [],
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      //成员部门信息
      memberList: [],
      multipleSelection: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        keywords: "",
        source_id: "",
        tracking_id: "",
        is_bind: "",
        type: 0,
        form: 3,
        status: 0,
        mobile: "",
        sort_type: 0,
        department_id: 0,
        admin_id: 0,
      },
      show_xiansuo_search: false,
      is_table_loading: false,
      tableData: [],
      table_header: [
        // {
        //   prop: "id",
        //   label: "ID",
        //   width: "80px",
        // },

        {
          label: "客户名称",
          width: "280px",
          fixed:"left",
          render: (h, data) => {
            return (
              <div class="cus-box div row">
                <div class="cus-box-header flex-box">
                  <div class="flex-row cus-header-user">
                    <span class="cus-userName">{data.row.cname}</span>
                    <div class="cus-sex">
                      {data.row.sex && data.row.sex == 1 ?
                        <img src="https://img.tfcs.cn/backup/static/admin/customer/nan.png" alt="" /> : null}
                      {data.row.sex && data.row.sex == 2 ?
                        <img src="https://img.tfcs.cn/backup/static/admin/customer/nv3.png" alt="" /> : null}
                    </div>
                  </div>
                  <div class="cus-box-foot flex-row">
                    {data.row.level ? (<span class="cus-icon-level">{data.row.level.title}级</span>) : null}
                    {data.row && data.row.create_id == this.status_id ? (<span class="cus-icon-type">私客</span>) : null}
                    {data.row.client_type ? (<span class="cus-icon-purchase">{data.row.client_type.title}</span>) : null}
                    <el-popover
                      popper-class="cus-poper-Label"
                      placement="bottom"
                      width="150"
                      trigger="click"
                    >
                      <div class="f-list">
                        {this.status_list.length ? this.status_list.map((item) => {
                          return <div class="f-item" onClick={() => { this.onClickFollowStatus(data.row, item) }}>
                            {item.title}
                          </div>
                        }) : null}
                      </div>
                      {(data.row.tracking && Object.keys(data.row.tracking).length) && data.row.tracking.title == '有效客户' ?
                        <span
                          slot="reference"
                          id={'popClick' + data.row.id}
                          class='cus-icon-customer'
                          onClick={() => {
                            this.setStatus(data.row);
                          }}
                        >有效客户</span>
                        :
                        data.row.tracking && Object.keys(data.row.tracking).length ?
                          <span
                            slot="reference"
                            id={'popClick' + data.row.id}
                            class='cus-icon-purchase'
                            onClick={() => {
                              this.setStatus(data.row);
                            }}>{data.row.tracking.title}
                          </span>
                          : ''
                      }
                    </el-popover>
                  </div>
                </div>
                {data.row.wxqy_id > 0 ? (
                  <img
                    class="cus-img"
                    src="https://img.tfcs.cn/backup/static/admin/customer/qywx.png"
                  />
                ) : (
                  ""
                )}
                <div class="fast-Edit-cus" onClick={() => { this.fastEditData(data.row) }}>
                  <img src="https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>" alt="" />
                </div>
              </div>
            );
          },
        },
        {
          label: "手机号",
          fixed:"left",
          minWidth: '200',
          prop: "mobile",
          render: (h, data) => {
            const mobileFilter = function (val) {
              let reg = /^(.{3}).*(.{3})$/;
              return val.replace(reg, "$1*****$2");
            };
            return <div class="flex-box table-btns">
              <span>{mobileFilter(data.row.mobile)}</span>
              <span class="search-Belong">
                {data.row.mobile_place == "" || data.row.mobile_place == undefined ?
                  <el-button
                    type="primary"
                    plain
                    onClick={() => {
                      this.HomeAddress(data.row);
                    }}>
                    归属地查询
                  </el-button>
                  :
                  <span class="Address">
                    {data.row.mobile_place}
                  </span>
                }
              </span>
              <div class="fast-look-tel" onClick={() => { this.fastLookTel(data.row) }}>
                <i class="el-icon-phone"></i>
              </div>
            </div>;
          },
        },
        {
          label: "客户线索",
          prop: "remark",
          width: "300px",
          render: (j, data) => {
            return (
              <div class="flex-box">
                <el-tooltip class="item" effect="light" placement="top">
                  <div slot="content" style="max-width:300px">
                    {data.row.remark}
                  </div>
                  <div class="cus-clue-text">
                    <span>{data.row.remark}</span>
                  </div>
                </el-tooltip>
                <div class="cus-clue-label flex-row">
                  {data.row.label.length ? data.row.label.slice(0, 4).map((item, index) => {
                    return <div class="flex-row align-center">
                      <span class="cus-icon-label" key={index}>{item}</span>
                    </div>
                  }) : null}
                </div>
                {data.row.label && data.row.label.length ?
                  <div
                    class="clueLabel"
                    onClick={() => {
                      this.fastEditLabel(data.row);
                    }}
                  >
                    <img src={"https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"} alt="" />
                  </div>
                  :
                  <div
                    class="clueLabel"
                    onClick={() => {
                      this.fastEditLabel(data.row);
                    }}
                  >
                    <img src={"https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"} alt="" />
                  </div>
                }
              </div>
            );
          },
        },
        {
          label: "创建时间",
          prop: "created_at",
          minWidth: '200',
          render: (j, data) => {
            return (
              <div style="text-align: left;">
                <div>
                  <div style="margin-bottom: 2px;">
                    {data.row.created_at}
                  </div>
                  {data.row.follow_user && data.row.follow_user.user_name ?
                    <span style="margin-right: 5px;">维护人：{data.row.follow_user.user_name}</span>
                    : ""
                  }
                  {data.row.public_status == 2 && data.row.public2_status ?
                    <span class="public-status">
                      {this.$options.filters.publicStatusParse(data.row.public2_status)}
                    </span>
                    : ""
                  }
                </div>
              </div>
            )
          }
        },
        {
          prop: "updated_at",
          label: "更新时间",
          minWidth: '200',
          render: (j, data) => {
            return (
              <div>
                {data.row.last_follow_day >= 0 ?
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content" style="max-width:300px">
                      {data.row.last_follow && data.row.last_follow.content ? data.row.last_follow.content : null}
                    </div>
                    <div class="follow-content">
                      <span style="margin-right: 5px;">
                        {this.$options.filters.getPastDay(data.row.last_follow_day)}
                      </span>
                      {data.row.last_follow && data.row.last_follow.content ? data.row.last_follow.content : null}
                    </div>
                  </el-tooltip> : ''
                }
                <div style="text-align: left;">{data.row.updated_at}</div>
                <div
                  class="followLabel"
                  onClick={() => {
                    this.fastFollowUp(data.row);
                  }}
                >
                  <img src={"https://img.tfcs.cn/backup/static/admin/customer/<EMAIL>"} alt="" />
                </div>
              </div>
            )
          }
        },
        {
          prop: "source.title",
          minWidth: '190',
          label: "来源",
          render: (h, data) => {
            return (
              <div>{ data.row.source.title }</div>
            )
          }
        },
        /* {
          label: "状态",
          width: "100px",
          render: (h, data) => {
            return (
              <div>
                {data.row.tracking ? (
                  <span>{data.row.tracking.title}</span>
                ) : (
                  "--"
                )}
              </div>
            );
          },
        }, */

        {
          label: "操作",
          width: "150",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.onClickDetail(data.row);
                  }}
                >
                  详情
                </el-link>
                {this.params.status != 1 && !data.row.follow_id ? (
                  <el-link
                    style="margin-left:20px"
                    type="primary"
                    onClick={() => {
                      this.onClickGet(data.row);
                    }}
                  >
                    认领
                  </el-link>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      is_follow_dialog: false,
      is_follow_data: [],
      is_follow_loading: false,
      is_follow_params: {
        page: 1,
        total: 0,
        per_page: 10,
      },
      label_list: [],
      multipleSelectionname: [],
      is_collapse: false,
      tracking_params: {
        type: 4,
      },
      is_dialog_upload: false,
      uploadAdmin_id: "",
      upload_form: {
        type: 1, // 是否覆盖数据 1：不覆盖 2：覆盖
        admin_id: "", // 管理员id
        file: "", // 导入的文件
        type_id: "", // 客户类型id
        source_id: "", // 客户来源
        label: "", // 标签:字符串格式，多个用逗号隔开
        remark: "", // 客户备注线索
      },
      admin_params: {
        page: 1,
        per_page: 10,
        total: 0,
        user_name: "",
      },
      admin_list: [],
      is_button_loading: false,
      pull_params: {
        next_user_cursor: 0,
        next_customer_cursor: "",
      },
      list_tabs: [
        { id: 0, title: "全部" },
        { id: 1, title: "已认领" },
        { id: 2, title: "已跟进" },
        { id: 3, title: "未跟进" },
      ],
      tips: [
        "模板中的表头不可更改，表头行不可删除",
        "单次导入的数据不超过3000条",
        "标红标题对应内容为必填项，如不填写则过滤出去不进行导入",
        "导入内容如有重复的手机号则只会导入第一条重复数据",
        "选择覆盖，导入的数据和已有数据重复时，会覆盖之前的数据，选择不覆盖则过滤掉重复数据",
      ],
      no_follow_number: "",
      show_tel_search: false,
      is_show_upload: false, // 是否显示导入按钮
      customer_list_type: [
        { id: 0, title: "新增线索", is_select: false },
        { id: 2, title: "最新跟进", is_select: true },
        { id: 3, title: "多条线索", is_select: false },
        { id: 4, title: "最近活跃", is_select: false },
      ],
      // 1:最近活跃,2:多条线索
      screen_list_type: [
        { id: 1, title: "已认领", is_select: true },
        { id: 5, title: "未跟进", is_select: true },
      ],
      customer_type: 0,
      notNewClue: true,
      is_show: 0, // 控制客户标签样式
      label_default_list: [], // 获取原始默认标签列表
      customerLabelList: {}, // 所选择的标签
      changeParentLabel: "", // 之前所选的上一级的标签
      is_all: true, // 控制客户标签，“全部”标签的样式
      datalist: [], // 全部部门人员
      show_Customer_label: false, // 控制客户标签模态框显示/隐藏
      labels_list: [], // 客户标签列表
      // 确定编辑客户标签的接口传参 
      confirm_batch_list: {
        ids: '', // 客户id 多个用逗号隔开
        label: '', // 客户标签id 多个用逗号隔开
        type: '1', // 客户类型 1公海 2私客
      },
      pop_depart: false, // 显示/隐藏部门搜索popover
      AllDepartment: [], // 全部部门列表
      is_transfer_customer: false, // 转交客户模态框
      c_id: '', // 转让客户id：多个用，隔开
      selectedMember: "", // 选择搜索部门
      Not_duplicate_datalist: [], // 没有重复部门成员的容器
      filtrMember: [], // 选中部门后过滤出的部门成员
      status_id: '', // 登录后的个人id
      status_list: [], // 快速编辑客户状态数据容器
      copy_status_list: [], // 深拷贝客户状态
      getStutas_params: {
        type: 2,
      },
      is_loading: false,
      show_Examine_dialog: false, // 控制客户审批模态框
      ponent_Examine_data: {}, // 提交审批客户信息
      ponent_Examine_stutas: {}, // 选择的要修改的状态
      website_id: "", // 当前站点id
      show_cus_Edit: false, // 显示快速编辑客户维护资料模态框
      ponent_maintain_data: {}, // 客户信息
      ponent_maintain_source: [], // 深拷贝客户来源列表
      ponent_maintain_level: [], // 深拷贝客户来源列表
      ponent_maintain_type: [], // 深拷贝客户类型列表
      ponent_maintain_label: [], // 深拷贝客户标签列表
      show_look_Tel: false, // 显示快速查看客户手机号模态框
      ponent_Tel_data: {}, // 客户信息
      is_fast: false, // 是否是快速编辑标签
      fastEdit_params: {
        client_id: "", // 客户id
        label: "", // 标签id多个，隔开
      },
      Examine_type: 19, // 默认审批类型
      is_pullDown: false, // 是否展开下拉框
      ponent_Follow_data: {}, // 客户信息
      show_Follow_dialog: false, // 显示快速跟进客户模态框
    };
  },
  computed: {
    levelLabel() {
      return this.level_list.filter((item) => {
        return item.id > 0;
      });
    },
    sourceLabel() {
      return this.source_list.filter((item) => {
        return item.id > 0;
      });
    },
    typeLabel() {
      return this.type_list.filter((item) => {
        return item.id > 0;
      });
    },
  },
  filters: {
    // 判断是否是掉公或转公客户
    publicStatusParse(row) {
      if (row == 1) {
        return "已转公";
      } else if (row == 2) {
        return "已掉公";
      }
    },
    // 计算过去时间天数
    getPastDay(val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
  },
  created() {
    // 赋值website_id
    if (this.$route.query.website_id) {
      this.website_id = this.$route.query.website_id
    }
    if (this.$route.query.status) {
      this.params.sort_type = this.$route.query.status;
    }
  },
  mounted() {
    this.getTypelist();
    this.getTrackingList();
    this.getLevelData();
    this.getSourceData();
    this.getLabelList();
    this.getAdmin();
    this.getCrmCustomerFollowNumber();
    this.getadminUser();
    this.getStatus();
  },
  methods: {
    //获取部门成员信息
    getMemberList() {
      this.$http.getDepartmentMemberList().then((res) => {
        if (res.status == 200) {
          this.memberList = res.data;
          this.memberList.push({
            id: 999,
            name: "未分配部门成员",
            order: 100000000,
            pid: 0,
            subs: this.memberList[0].user
          });
          this.recursionData(this.memberList);
          this.Not_duplicate_datalist = JSON.parse(JSON.stringify(this.datalist));
          this.filteredData();
          for (let i = 0; i < this.datalist.length; i++) {
            for (let j = i + 1; j < this.datalist.length; j++) {
              if (this.datalist[i].id == this.datalist[j].id) {
                this.datalist[i].id = parseInt(this.datalist[i].id += this.datalist[i].Parent + '');
              }
            }
          }
        }
      })
    },
    recursionData(data) {
      // console.log(data,"内容");
      // console.log(this.datalist,"全部人员");
      for (let key in data) {
        if (typeof data[key].subs == "object") {
          data[key].subs.map((item) => {
            if (item.user) {
              item.subs = item.user;
              item.subs.map((list) => {
                list.Parent = item.id;
              })
            }
            if (item.user_name) {
              item.name = item.user_name;
              this.datalist.push(item)
            }
          })
          this.recursionData(data[key].subs);
        }
      }
    },
    //选中部门人员
    selecetedMember(e) {
      console.log(e.checkedNodes, "成员列表");
      if (e.checkedNodes && e.checkedNodes.length) {
        this.uploadAdmin_id = e.checkedNodes[e.checkedNodes.length - 1].name;
        this.upload_form.admin_id = e.checkedNodes[e.checkedNodes.length - 1].id; // 将id赋值
      } else {
        this.upload_form.admin_id = ""
      }
      this.show_member_list = false;
    },
    //关闭弹窗之间的回调
    cancels() {
      this.is_dialog_upload = false;
      this.upload_form = {
        type: 1,
        admin_id: "",
        file: "",
      };
    },
    // 获取信息展示
    getadminUser() {
      this.$http.getAdmin().then((res) => {
        if (res.status === 200) {
          this.status_id = res.data.id;
          if (res.data.roles[0].name === "站长") {
            this.is_show_upload = true;
          } else {
            this.getSiteCrmSetting(res.data.id);
          }
        }
      });
    },
    // filterTime(val) {
    //     var date = new Date(val * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    //     var Y = date.getFullYear() + '-';
    //     var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
    //     var D = date.getDate() + ' ';
    //     var h = date.getHours() + ':';
    //     var m = date.getMinutes() + ':';
    //     var s = date.getSeconds();
    //     return Y+M+D+h+m+s;
    // },
    // 获取批量导入的crm站点设置
    getSiteCrmSetting(id) {
      this.$http.getAuthShow("batch_import_uid").then((res) => {
        if (res.status === 200) {
          let num = [];
          num.push(res.data)
          // console.log(num,"num",id)
          if (num.indexOf(id) != -1 || !res.data) {
            this.is_show_upload = true;
          }
        }
      });
    },
    // 快速编辑标签
    async fastEditLabel(row) {
      this.multipleSelection = []; // 清空
      this.multipleSelection.push(row.id); // 赋值客户id
      this.is_fast = true; // 设置为快速编辑标签
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        })
      })
      //console.log(row,"row");
      this.fastEdit_params.client_id = row.id;
      new Promise((resolve) => {
        // 获取客户标签列表
        if (!this.labels_list.length) {
          this.getLabelGroupNoPageNew();
        }
        if (!this.labels_list.length) {
          setTimeout(() => {
            resolve();
          }, 500)
        } else {
          resolve();
        }
      }).then(() => {
        this.show_Customer_label = true; // 显示模态框
        // console.log(this.labels_list,"this.labels_list");
        row.label.map((item) => {
          this.labels_list.map((list) => {
            list.label.map((arr) => {
              if (arr.name == item) {
                arr.check = true;
              }
            })
          })
        })
        this.$forceUpdate();
      })
    },
    resetLoudongSearch() {
      this.params.mobile = "";
    },
    handleSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    getCrmCustomerFollowNumber() {
      this.$http.getCrmCustomerFollowNumber().then((res) => {
        if (res.status === 200) {
          this.no_follow_number = res.data;
        }
      });
    },
    changeTab(e, type) {
      if (type == 1) {
        this.params.status = e.id;
      } else {
        this.params.type = e.id;
      }
      this.params.page = 1;
      this.getDataList();
    },
    getTypelist() {
      this.$http.getCrmCustomerTypeDataNopage().then((res) => {
        if (res.status === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
          this.push_form.type = res.data.filter((item) => {
            return item.is_default;
          })[0].id;
          let cus_type = parseInt(this.$route.query.cus_type);
          res.data.map((item) => {
            if (cus_type == 1 && item.title == "求购") {
              this.params.type = item.id;
            }
            if (cus_type == 2 && item.title == "求租") {
              this.params.type = item.id;
            }
          });
        }
        this.ponent_maintain_type = JSON.parse(JSON.stringify(res.data));
        let i = this.customer_list_type.findIndex(item => item.id == this.params.sort_type);
        if (i != -1) {
          this.getDataLists(this.customer_list_type[i]);
        }
      });
    },
    getLabelList() {
      this.$http.getLabelGroupNoPage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_label = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户类型列表
          this.label_list = res.data;
          // console.log(this.label_list,"查看");
          this.label_default_list = res.data;
        }
      });
    },
    getDataLists(item) {
      // if (item.title == '新增线索') {
      //   this.notNewClue = true;
      //   let isNewClue_update = this.table_header.filter((remo) => {
      //     return remo.label == "更新时间";
      //   })
      //   if (isNewClue_update.length != 0) {
      //     if (this.notNewClue) {
      //       this.table_header.splice(4, 1, { prop: 'source.title', label: '来源' });
      //     }
      //   }
      //   let isNewClue_create = this.table_header.filter((remo) => {
      //     return remo.label == "创建时间";
      //   })
      //   if (isNewClue_create.length == 0) {
      //     this.table_header.splice(4, 0, { prop: "created_at", label: "创建时间" });
      //   }
      // }
      // 如果不是“新增线索”就删除创建时间，添加更新时间
      // else if (item.title != '新增线索') {
      //   this.notNewClue = false;
      //   let notNewClue_create = this.table_header.filter((remo) => {
      //     return remo.label == "创建时间";
      //   })
      //   if (notNewClue_create.length != 0) {
      //     this.table_header.splice(4, 1);
      //   }
      //   let notNewClue_update = this.table_header.filter((remo) => {
      //     return remo.label == "更新时间";
      //   })
      //   if (notNewClue_update.length == 0) {
      //     this.table_header.splice(4, 0, { prop: "updated_at", label: "更新时间" });
      //   }
      // }
      if (item) {
        this.params.sort_type = item.id;
      }
      this.params.type = 0;
      this.params.page = 1
      this.getDataList();
    },
    // getSecondDataLists(item){
    //   console.log(item);
    //   if (item.title != '新增线索') {
    //     this.notNewClue = false;
    //     let notNewClue_create = this.table_header.filter((remo) => {
    //       return remo.label == "创建时间";
    //     })
    //     if (notNewClue_create.length != 0) {
    //       this.table_header.splice(4, 1);
    //     }
    //     let notNewClue_update = this.table_header.filter((remo) => {
    //       return remo.label == "更新时间";
    //     })
    //     if (notNewClue_update.length == 0) {
    //       this.table_header.splice(4, 0, { prop: "updated_at", label: "更新时间" });
    //     }
    //   }
    //   if (item) {
    //     // this.customer_type = item.id;
    //     // this.params.sort_type = item.id;
    //     delete this.params.status;
    //     this.params.sort_type = item.id;
    //   }
    //   this.params.page = 1
    //   this.getDataList()
    // },
    getDataList() {
      let params = Object.assign({}, this.params)
      if (params.sort_type == 1 || params.sort_type == 2) {
        params.status = params.sort_type;
        delete params.sort_type;
      } else if (params.sort_type == 3) {
        delete params.status;
        params.sort_type = 2;
      } else if (params.sort_type == 4) {
        delete params.status;
        params.sort_type = 1;
      } else if (params.sort_type == 5) {
        delete params.sort_type;
        params.status = 3;
      }
      console.log(params);
      // 如果是“新增线索”就删除更新时间列
      this.is_table_loading = true;
      if (!this.params.source_id) {
        delete params.source_id;
      }
      if (!this.params.tracking_id) {
        delete params.tracking_id;
      }
      if (!this.params.is_bind) {
        delete params.is_bind;
      }
      this.$http
        .getCrmCustomerClientList({ params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.page = res.data.current_page;
            this.params.total = res.data.total;
          }
        });
    },
    getLevelData() {
      this.$http.getCrmCustomerLevelNopage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_level = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户级别列表
          this.level_list = [{ title: "全部", id: 0 }, ...res.data];
        }
      });
    },
    getTrackingList() {
      this.$http
        .getCrmCustomerFollowInfo({ params: this.tracking_params })
        .then((res) => {
          if (res.status === 200) {
            this.tracking_list = [{ title: "全部", id: 0 }, ...res.data];
          }
        });
    },
    resetXiansuoSearch() {
      this.params.keywords = ""
    },
    onClickSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    onClickType(e, type) {
      switch (type) {
        case 1:
          this.params.source_id = e.id;
          break;
        case 2:
          this.params.tracking_id = e.id;
          break;
        case 3:
          this.params.is_bind = e.id;
          break;
        case 4:
          this.params.date_type = e.id;
          delete this.params.start_date; // 清空开始时间
          delete this.params.end_date; // 清空结束时间
          this.timeValue = ""; // 清空自定义时间绑定值
          break;
        case 5:
          this.params.level_id = e.id;
          break;
        case 6:
          this.params.type = e.id;
          break;
        default:
          break;
      }
      this.params.page = 1;
      this.getDataList();
    },
    onChangeTime(e) {
      this.params.start_date = e ? e[0] : ""; // 赋值开始时间
      this.params.end_date = e ? e[1] : ""; // 赋值结束时间
      this.params.date_type = 0; // 清空筛选时间
      this.$refs.childRef.clearScreening(); // 重置筛选时间为全部
      this.params.page = 1; // 显示第一页
      this.getDataList(); // 获取最新数据
    },
    getSourceData() {
      this.$http.getCrmCustomerSourceNopage().then((res) => {
        if (res.status === 200) {
          this.ponent_maintain_source = JSON.parse(JSON.stringify(res.data)); // 深拷贝客户来源列表
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
          this.push_form.source_id = res.data.filter((item) => {
            return item.is_default == 1;
          })[0].id;
        }
      });
    },
    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    addDomain() {
      this.other_mobile.push({
        mobile: "",
      });
    },
    onClickLevel(item) {
      this.push_form.level_id = item.id;
    },
    onClickTypeClient(item) {
      this.push_form.type = item.id;
    },
    onChangeKeywords() {
      this.params.page = 1;
      this.getDataList();
    },
    selectionChange(e) {
      this.multipleSelectionname = e; // 赋值当前客户信息
      let arr = e.map((item) => {
        return item.id;
      });
      this.multipleSelection = arr; // 赋值当前客户的id
      // 只有在客户标签列表为空时请求数据
      if (!this.labels_list.length) {
        this.getLabelGroupNoPageNew();
      }
    },
    onClickDetail(row) {
      let url = `/crm_customer_detail?id=${row.id}&type=seas`;
      this.$goPath(url);
    },
    onPageChange(current_page) {
      this.params.page = current_page;
      this.getDataList();
    },
    onPageChangeQwFollow(e) {
      this.is_follow_params.page = e;
      this.getFollow();
    },
    isShowFollow(row) {
      this.is_follow_dialog = true;
      this.is_follow_params.client_id = row.client_id;
      this.getFollow();
    },
    getFollow() {
      this.is_follow_loading = true;
      this.$http
        .getCrmCustomerFollowData({ params: this.is_follow_params })
        .then((res) => {
          this.is_follow_loading = false;
          if (res.status === 200) {
            this.is_follow_data = res.data.data;
            this.is_follow_params.total = res.data.total;
          }
        });
    },
    // onClickForm(e) {
    //   this.$http.setCrmCustomerData(e).then((res) => {
    //     if (res.status === 200) {
    //       this.$message.success("操作成功");
    //       this.is_push_customer = false;
    //       this.form = {};
    //       this.form1 = {};
    //       this.params.page = 1;
    //       this.getDataList();
    //     }
    //   });
    // },
    onClickForm() {
      if (this.push_form.label && this.push_form.label != undefined && typeof (this.push_form.label) !== 'string') {
        this.push_form.label = this.push_form.label.join(","); // 将多个id转换为字符串用,隔开
      }
      if (this.other_mobile.length > 0) {
        let arr = this.other_mobile.map((item) => {
          return item.mobile;
        });
        let othertel = arr.filter((item, index) => {
          if (index) {
            return item;
          }
        });
        this.push_form.mobile = arr[0];
        this.push_form.subsidiary_mobile = othertel.join(",");
      }
      if (!this.push_form.mobile) {
        this.$message.error("请检查联系方式");
        return;
      }
      if (!this.push_form.cname) {
        this.$message.error("请检查客户姓名");
        return;
      }
      if (!this.push_form.sex) {
        this.$message.error("请检查客户性别");
        return;
      }
      if (!this.push_form.level_id) {
        this.$message.error("请检查客户等级");
        return;
      }
      if (!this.push_form.type) {
        this.$message.error("请检查客户类型");
        return;
      }
      if (!this.push_form.source_id) {
        this.$message.error("请检查客户来源");
        return;
      }
      this.is_button_loading = true;
      this.$http.setCrmCustomerDataV2(this.push_form).then((res) => {
        this.is_button_loading = false;
        this.is_push_customer = false;
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.getDataList();
        } else if (res.status === 422) {
          const cus_id = res.data.data && (res.data.data.id != '' && res.data.data.id != undefined) ? res.data.data.id : 0; // 赋值客户id
          // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
          if ((res.data.data && res.data.data.follow_id) && (res.data.data.follow_id != undefined && res.data.data.follow_id != 0)) {
            this.$confirm('客户号码已有维护人，无法重复录入。', '提示', {
              confirmButtonText: '立即查看',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              let url = `/crm_customer_detail?id=${cus_id}&type=my`;
              this.$goPath(url); // 跳转客户详情
            }).catch(() => {
              return;
            });
          } else {
            // 该客户没有维护跟进人时触发
            this.$confirm('客户号码已存在，认领后可跟进。', '提示', {
              confirmButtonText: '立即认领',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$http.getCrmCustomerPublick({ ids: cus_id + "" }).then((res) => {
                if (res.status === 200) {
                  this.$message.success("认领成功");
                  let url = `/crm_customer_detail?id=${cus_id}&type=my`;
                  this.$goPath(url); // 跳转客户详情
                }
              });
            }).catch(() => {
              return;
            });
          }
        }
      });
    },
    // 折叠面板
    onChangeCollapse() {
      this.is_collapse = !this.is_collapse;
    },
    onClickGet(row) {
      this.$confirm("是否认领该客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.getCrmCustomerPublick({ ids: row.id + "" }).then((res) => {
            if (res.status === 200) {
              this.$message.success("认领成功");
              let url = `/crm_customer_detail?id=${row.id}&type=my`;
              this.$goPath(url);
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    // handleChangeLabel() {
    //   this.params.page = 1;
    //   this.getDataList();
    // },
    getFile() {
      this.is_dialog_upload = true;
      this.getMemberList();
      // this.$refs.file.click();
    },
    // 获取文件
    handleFileUpload(event) {
      console.log("执行")
      // 阻止发生默认行为
      event.preventDefault();
      let file = this.$refs.file.files[0];
      let formData = new FormData();
      formData.append("file", file);
      formData.append("admin_id", this.upload_form.admin_id || 0);
      formData.append("type", this.upload_form.type);
      formData.append("type_id", this.upload_form.type_id);
      formData.append("source_id", this.upload_form.source_id);
      if (Array.isArray(this.upload_form.label)) {
        formData.append("label", this.upload_form.label.join(","));
      } else {
        formData.append("label", this.upload_form.label);
      }
      formData.append("remark", this.upload_form.remark);
      // this.formData.get("file");
      this.onUpload(formData);
    },
    // 上传文件
    onUpload(formData) {
      this.$message.success("正在上传...");
      this.$http.uploadCrmCustomerData(formData).then((res) => {
        if (res.status === 200) {
          // 如果只有新增
          if (res.data.add_num > 0 && res.data.edit_num == 0) {
            this.$message.success("导入成功，新增" + res.data.add_num + "条");
          } else if (res.data.add_num == 0 && res.data.edit_num > 0) { // 如果只有编辑覆盖
            this.$message.success("导入成功，编辑覆盖" + res.data.edit_num + "条");
          } else if (res.data.add_num > 0 && res.data.edit_num > 0) { // 如果新增和覆盖都有
            this.$message.success("导入成功，新增" + res.data.add_num + "条，编辑覆盖" + res.data.edit_num + "条");
          } else if (res.data.add_num == 0 && res.data.edit_num == 0) { // 如果新增和覆盖都没有
            this.$message.error("导入失败，新增" + res.data.add_num + "条，编辑覆盖" + res.data.edit_num + "条");
          }
          this.params.page = 1;
          this.getDataList();
          this.is_dialog_upload = false;
          this.is_loading = false;
        }
      });
    },
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/client_template.xls?f=" + (+new Date())
      );
    },
    //表单重置
    reset() {
      this.push_form = {
        cname: "",
        source_id: "",
        level_id: 1,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        label: "",
        // intention_street: "",
        remark: "",
      };
      this.other_mobile = [{ mobile: "" }];
    },
    //取消
    cancel() {
      this.reset();
      this.is_push_customer = false;
    },
    // 检索客户标签发生改变时触发
    changeCustomerLabel(val) {
      this.is_all = false;
      // 父级标签id赋值
      let label_num = Object.keys(this.customerLabelList).length;
      // 如果选择的标签大于1就删除之前选择的标签
      if (label_num > 1) {
        delete this.customerLabelList[this.changeParentLabel];
      }
      this.changeParentLabel = val[2].id;
      // 更新数据
      this.params.label = val[0];
      this.params.page = 1;
      this.getDataList();
    },
    getSelectWidth(item, LabelList) {
      var text_temp = "",
        text_width = 0,
        text_el = null;
      var current_option = "";
      if (LabelList) {
        current_option = item.label && item.label.find((arr) => arr.id === LabelList[0]);
      } else {
        current_option = false;
      }
      if (current_option) {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${current_option.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      } else {
        text_temp = `<span id="text" style="font-size: 12px; height: 0; overflow: hidden">${item.name}</span>`;
        document.body.insertAdjacentHTML("afterbegin", text_temp);
      }
      text_el = document.getElementById("text");
      text_width = text_el.offsetWidth;
      text_el.remove();
      return text_width + 57 + "px";
    },
    getAllLabelList() {
      this.is_all = true;
      this.changeParentLabel = "";
      this.customerLabelList = {};
      delete this.params.label;
      this.params.page = 1;
      this.getDataList();
    },
    // 开始导入
    startImport() {
      this.is_loading = true;
      if (this.upload_form.type_id == '' || this.upload_form.type_id == undefined) {
        this.upload_form.type_id = 0;
      }
      if (this.upload_form.source_id == '' || this.upload_form.source_id == undefined) {
        this.upload_form.source_id = 0;
      }
      // 处理为正常部门成员id
      if (this.upload_form.admin_id.toString().length >= 6) {
        this.upload_form.admin_id = parseInt(this.upload_form.admin_id.toString().slice(0, 3))
      }
      this.$refs.file.click();
      this.is_loading = false;
    },
    // 清除当前选择成员
    delName() {
      this.uploadAdmin_id = "";
      this.upload_form.admin_id = 0;
    },
    // 点击设置客户标签按钮
    setCustomerLabel() {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: '请选择客户',
          type: 'warning'
        })
      }
      this.show_Customer_label = true; // 显示模态框
      // 去除已选中的标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          list.check = false;
        })
      })
    },
    // 选中标签
    checkChangeLabels(index0, index) {
      let that = this;
      that.labels_list[index0].label[index].check = !that.labels_list[index0]
        .label[index].check;
      this.$forceUpdate();
    },
    // 确定更改客户标签
    confirmSelected() {
      this.is_loading = true;
      let num = []; // 已选客户标签容器
      // 遍历出勾选中的客户标签
      this.labels_list.map((item) => {
        item.label.map((list) => {
          if (list.check) {
            num.push(list.id)
          }
        })
      })
      // 赋值已选中的客户标签参数
      if (num && num.length) {
        this.confirm_batch_list.label = num.join(",");
      }
      // 赋值已选中的客户id参数
      if (this.multipleSelection && this.multipleSelection.length) {
        this.confirm_batch_list.ids = this.multipleSelection.join(",");
      }
      // 请求接口
      this.$http.batchSetLabelGroup(this.confirm_batch_list).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.show_Customer_label = false; // 关闭模态框
          this.is_loading = false;
          this.getDataList(); // 获取最新数据
        } else {
          this.is_loading = false;
        }
      }).catch(() => {
        this.is_loading = false;
      })
    },
    // 导入成员当获取焦点时触发
    focusSelete() {
      this.$refs.focusMember.blur(); // 失去焦点
      this.show_member_list = true;
    },
    // 获取客户标签列表
    getLabelGroupNoPageNew() {
      // 获取客户标签列表
      this.$http.getLabelGroupNoPageNew().then((res) => {
        if (res.status == 200) {
          if (res.data.qiwei_tag && res.data.qiwei_tag.length) {
            res.data.qiwei_tag.map(item => {
              item.label = item.taggroup
              delete item.taggroup
            })
          }
          this.labels_list = res.data.qiwei_tag.concat(res.data.system_tag);
        }
        // 格式化数据-添加check属性
        if (this.labels_list && this.labels_list.length) {
          this.labels_list.map((item) => {
            if (item.label) {
              item.label.map((list) => {
                list.check = false;
              })
            }
          })
        }
      })
    },
    // 查询归属地
    HomeAddress(row) {
      this.$http.inquireHomeAddress(row.id).then((res) => {
        if (res.status == 200) {
          this.getDataList(); // 获取最新数据
        }
      })
    },
    // 部门选取发生改变
    changePopDepar(val) {
      this.selectedMember = "";
      if (val != null) {
        this.filtrMember = [];
        this.filtrDepartMember(this.memberList, val);
        this.filtrMember = this.filtrMember.filter(item => {
          return item.id.toString().length <= 4;
        })
      } else {
        this.filteredData();
      }
    },
    // 选中对应部门，遍历出部门成员
    filtrDepartMember(data, val) {
      for (let key in data) {
        // console.log(data[key].id,val,"执行",data[key].id == val)
        if (data[key].id == val) {
          if (data[key].subs) {
            data[key].subs.map((item) => {
              if (item.user) {
                item.user.map((list) => {
                  this.filtrMember.push(list);
                })
              } else if (item.user_name) {
                this.filtrMember.push(item);
              }
            })
          }
        }
        this.filtrDepartMember(data[key].subs, val);
      }
    },
    // 部门成员去重
    filteredData() {
      const uniqueIds = {};
      const filtered = [];
      for (let i = 0; i < this.Not_duplicate_datalist.length; i++) {
        const item = this.Not_duplicate_datalist[i];
        if (!uniqueIds[item.id]) {
          uniqueIds[item.id] = true;
          filtered.push(item);
        }
      }
      this.filtrMember = filtered;
    },
    // 获取全部的部门
    getCrmDepartmentList() {
      this.params.admin_id = 0; // 清空搜索内容
      this.params.department_id = 0; // 清空搜索内容
      this.getDepartmentList(); // 获取部门
      // 获取部门成员
      if (!this.datalist.length) {
        this.getMemberList();
      }
      // if (!this.AllDepartment.length) {
      //   this.$http.getCrmDepartmentList().then((res) => {
      //     if (res.status == 200) {
      //       this.AllDepartment = res.data;
      //     }
      //   })
      // }
    },
    // 获取部门
    getDepartmentList() {
      if (!this.AllDepartment.length) {
        this.$http.getCrmDepartmentList().then((res) => {
          if (res.status == 200) {
            this.AllDepartment = res.data;
          }
        })
      }
    },
    // 按照部门搜索
    searchDepart() {
      if (this.selectedMember == '' || this.selectedMember == undefined) {
        this.params.admin_id = 0; // 赋值0
      }
      this.getDataList();
    },
    // 按照成员搜索
    searchMember() {
      // 处理id为前三位
      if (this.params.admin_id.toString().length >= 6) {
        this.params.admin_id = parseInt(this.params.admin_id.toString().slice(0, 3));
      }
      this.getDataList();
    },
    // 搜索部门成员发生改变时触发
    changeSearchMember() {
      // 如果搜索的参数为空或undefined
      if (this.selectedMember == '' || this.selectedMember == undefined) {
        this.params.admin_id = 0; // 赋值0
      } else {
        this.params.admin_id = this.selectedMember; // 有值则赋值
      }
    },
    // 转交客户
    TransferCustomer() {
      // 判断是否选中客户
      if (!this.multipleSelection.length) {
        return this.$message({
          message: '请选择客户',
          type: 'warning'
        });
      }
      this.is_transfer_customer = true;
      this.admin_params.user_name = "";
      this.admin_params.page = 1
    },
    // 搜索转交人
    onAdminSearch() {
      this.admin_params.page = 1;
      this.getAdmin();
    },
    getAdmin() {
      this.$http
        .getUserList(
          this.admin_params.page,
          this.admin_params.per_page,
          this.admin_params.user_name
        )
        .then((res) => {
          if (res.status === 200) {
            this.admin_list = res.data.data;
            this.admin_params.total = res.data.total;
          }
        });
    },
    // 确定转让客户
    onZhuanrang(e) {
      this.$confirm("是否转交客户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 将数组转换字符串
          this.c_id = this.multipleSelection.join(",");
          // 点击确认调用公众号授权
          this.$http
            .setCrmCustomerZhuanrang({
              be_transfer_id: e.id,
              ids: this.c_id,
            })
            .then((res) => {
              if (res.status === 200) {
                this.$message.success("操作成功");
                this.is_transfer_customer = false;
                this.getDataList(); // 获取最新数据
              }
            });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    PersPageChange(e) {
      this.admin_params.page = e;
      this.getAdmin();
    },
    // 关闭客户审批模态框
    closeExamine() {
      this.show_Examine_dialog = false; // 关闭模态框
    },
    // 客户审批提交成功后的回调函数
    submitExamineAfter() {
      this.show_Examine_dialog = false;
      this.getDataList(); // 刷新客户列表
    },
    // 获取客户状态
    getStatus() {
      if (!this.status_list.length) {
        this.$http
          .getCrmCustomerFollowInfo({ params: this.getStutas_params })
          .then((res) => {
            if (res.status === 200) {
              this.status_list = res.data;
              this.status_list.map((item) => {
                if (item.title == '有效客户') {
                  item.value_name = 1;
                } else if (item.title == '无效客户') {
                  item.value_name = 2;
                } else if (item.title == '暂缓客户') {
                  item.value_name = 3;
                } else {
                  item.value_name = 1;
                }
                return item;
              })
              this.copy_status_list = JSON.parse(JSON.stringify(this.status_list)); // 深拷贝客户状态
            }
          });
      }
    },
    setStatus(row) {
      this.status_list = JSON.parse(JSON.stringify(this.copy_status_list)); // 重新赋值客户状态
      let delIndex = "";
      this.status_list.map((item, index) => {
        if (item.id == row.tracking.id) {
          delIndex = index;
        }
      })
      if (typeof (delIndex) == "number") {
        this.status_list.splice(delIndex, 1);
      }
    },
    // 点击更改客户状态
    onClickFollowStatus(row, item) {
      // 审批无需审核 is_state == 2
      if (row.is_state == 2) {
        let examine = false;
        if (row.state_list && row.state_list.length) {
          // 如果state_list中有当前要更改的状态
          row.state_list.map((list) => {
            if (list == item.id) {
              examine = true;
            }
          })
        }
        if (examine) {
          // 判断是不是客户管理员， 是就不审批直接更改
          if (row.admin_list && row.admin_list.length) {
            // 遍历客户管理员
            const isLargeNumber = (item) => item == this.selfID;
            let is_admins = row.admin_list.findIndex(isLargeNumber);
            // 如果是客户管理员无需审批
            if (is_admins >= 0) {
              this.setCrmCustomerStatus(row, item); // 无需审批
            } else {
              this.requiresExamineStatus(row, item); // 需要审批
            }
          }
        } else {
          // 不走审批
          this.setCrmCustomerStatus(row, item); // 无需审批
        }
      } else {
        this.$message.warning("当前客户状态不可以进行审批")
      }
      // console.log(row, "row", item, 'item')
    },
    // 无审批修改客户状态
    setCrmCustomerStatus(row, item) {
      this.$http.setCrmCustomerStatus({ id: row.id, tracking_id: item.id }).then((res) => {
        if (res.status == 200) {
          this.$message.success("操作成功");
          document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
          this.getDataList(); // 刷新数据
        }
      })
    },
    // 需审批修改客户状态
    requiresExamineStatus(row, item) {
      document.getElementById("popClick" + row.id).click(); // 点击后隐藏pop框
      this.Examine_type = 19; // 切换类型为修改客户状态
      this.show_Examine_dialog = true; // 显示审批模态框
      this.ponent_Examine_data = row; // 赋值客户信息
      this.ponent_Examine_stutas = item; // 选择的要修改的状态
      // 获取部门
      if (!this.AllDepartment.length) {
        this.getDepartmentList();
      }
    },
    // 快速编辑客户维护资料
    fastEditData(row) {
      this.ponent_maintain_data = row;
      this.show_cus_Edit = true;
    },
    // 关闭快速编辑客户维护资料
    fastCloseEdit() {
      this.show_cus_Edit = false;
    },
    // 刷新页面获取最新数据
    submitMaintain() {
      this.getDataList();
      this.show_cus_Edit = false;
    },
    // 快速查看客户手机号
    fastLookTel(row) {
      // 查看电话时跟进
      this.$http.setViewCrmCustomerTel(row.id).then((res) => {
        if (res.status === 200) {
          this.ponent_Tel_data = row;
          this.show_look_Tel = true; // 显示模态框
          this.getDataList(); // 刷新页面数据
        }
      });
    },
    // 关闭快速查看客户手机号回调函数
    fastCloseTel() {
      this.show_look_Tel = false;
    },
    // 提交查看手机号跟进成功回调函数
    fastSubmitTel() {
      this.getDataList();
      this.show_look_Tel = false;
    },
    // 阻止默认行为和事件传播
    showTypeStatus(e) {
      e.preventDefault();
      e.stopPropagation();
      if (this.is_pullDown) {
        this.is_pullDown = false;
      } else {
        this.is_pullDown = true;
      }
    },
    // 改变搜索类型
    changeScreenType(list, item) {
      // list: 当前选择类型 , item: 选择之前的类型
      console.log(list, "list", item, "item");
      this.is_pullDown = false; // 关闭popover
      let firstIndex = "";
      let secondIndex = "";
      this.customer_list_type.map((arr, index) => {
        if (arr.title == item.title) {
          firstIndex = index;
        }
      })
      this.customer_list_type.splice(firstIndex, 1, list);
      this.screen_list_type.map((arr, index) => {
        if (arr.title == list.title) {
          secondIndex = index;
        }
      })
      this.screen_list_type.splice(secondIndex, 1, item);
      this.getDataLists(list);
    },
    // 客户列表快速跟进客户内容
    fastFollowUp(row) {
      console.log(row, "row");
      this.ponent_Follow_data = row; // 赋值客户信息
      this.show_Follow_dialog = true;
    },
    // 快速添加跟进成功执行
    addFollowSuccess() {
      this.$message.success("操作成功");
      this.getDataList(); // 刷新页面数据
    },
    // 关闭快速跟进客户内容模态框
    closeFollow() {
      this.show_Follow_dialog = false;
    }
  },
  // 当该页面进入时触发
  async activated() {
    // 判断是否要刷新数据
    if (this.$store.state.allowUpdate) {
      this.$store.state.allowUpdate = false;
      this.getDataList(); // 刷新页面数据
    }
  },
};
</script>

<style lang="scss" scoped>
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 0 24px 24px;

  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    margin-left: -24px;
    margin-right: -24px;

    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }

  .grid-content {
    background: #fff;
    height: 113px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;

    img {
      width: 16px;
      height: 16px;
    }

    .left {
      font-size: 14px;
      color: #8a929f;
      margin-right: 64px;
    }

    .number {
      display: flex;
      align-items: center;

      .number1 {
        font-size: 24px;
        color: #2e3c4e;
        margin-right: 10px;
      }

      .number2 {
        font-size: 12px;
        color: #fe6c17;
      }
    }
  }
}

.isbottom {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-border {
  align-items: flex-start;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e2e2e2;
  justify-content: flex-start;

  .text {
    font-size: 14px;
    color: #8a929f;
    width: 70px;
    text-align: right;
    white-space: nowrap;
  }

  .selected-header {
    font-size: 14px;
    color: #8a929f;
    margin-right: 8px;
    padding: 3px 16px;
    cursor: pointer;
    margin-bottom: 5px;
    white-space: nowrap;
  }

  .label_actions {
    border-radius: 4px;
    background: #e8f1ff;
    color: #2d84fb;
  }

  .selected-label {
    border-radius: 4px;

    ::v-deep .el-input {
      width: auto;

      .el-input__inner {
        // max-width: 75px;
        // width: 75px;
        // border: none;
        // color: #409EFF;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: none;
        color: #2d84fb;
        //
        // background: #E8F1FF;
        line-height: 25px;
        height: 25px;
        border-radius: 4px;
        //
      }

      .el-input__suffix {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      ::-webkit-input-placeholder {
        /* WebKit browsers */
        color: #8a929f;
      }
    }
  }
}

.loadmore {
  border: none;
  width: 100%;
  text-align: end;
  line-height: 1;
  margin-top: 12px;
  color: #8a929f;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;

  .text {
    font-size: 14px;
  }
}

.labelrow {
  margin-bottom: 20px;
}

.crmuserbox {
  align-items: center;

  span {
    display: block;
  }

  img {
    border-radius: 50%;
  }

  .label {
    width: fit-content;
    padding: 0 6px;
    background: #2d84fb;
    border-radius: 4px;
    color: #fff;
  }
}
</style>
<style lang="scss">
.cus-box {
  align-items: center;
  line-height: 1;
  flex-wrap: wrap;

  .cus-box-header {
    .cus-header-user {
      align-items: center;

      .cus-userName {
        color: #2d84fb;
        text-align: left;
        margin: 5px 5px 5px 0;
      }

      .cus-sex {
        width: 16px;
        height: 16px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 12px;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      color: #67c23a;
      border: 1px solid #67c23a;
    }

    .cus-icon-purchase {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }
  }

  .cus-img {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }

  .cus-box-foot {
    align-items: center;
    flex-wrap: wrap;

    .cus-icon-level,
    .cus-icon-type,
    .cus-icon-customer,
    .cus-icon-purchase,
    .cus-icon-douyin {
      font-size: 12px;
      padding: 3px 9px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-label {
      color: #16a1bc;
      border: 1px solid #16a1bc;
      font-size: 14px;
      padding: 5px 11px;
      border-radius: 2px;
      margin: 5px 10px 5px 0;
    }

    .cus-icon-level {
      color: #fff;
      background: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    .cus-icon-type {
      color: #98a6c3;
      border: 1px solid #98a6c3;
    }

    .cus-icon-customer {
      display: block;
      color: #67c23a;
      border: 1px solid #67c23a;
      cursor: pointer;
    }

    .cus-icon-purchase {
      display: block;
      color: #98a6c3;
      border: 1px solid #98a6c3;
      cursor: pointer;
    }

    .cus-icon-douyin {
      color: #16a1bc;
      border: 1px solid #16a1bc;
    }

    .cus-douyinIcon {
      width: 16px;
      height: 16px;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .clueLabel {
    width: 20px;
    height: 20px;
    display: none;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .is_null_clueLabel {
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .fast-Edit-cus {
    display: none;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.i-form-list {
  .el-form-item__label {
    color: #8a929f;
  }

  .input-box {
    width: 238px;
    justify-content: space-between;

    .sex-box {
      img {
        border-radius: 4px;
        border: 1px solid #fff;
        margin-right: 14px;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }
    }

    .l-item {
      background: #f0f3f9;
      cursor: pointer;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      line-height: 1;
      padding: 10px;
      border: 1px solid #fff;

      .l-name-1 {
        font-size: 12px;
        margin-top: 12px;
      }
    }

    .l-item-type {
      width: 40%;
    }

    .lisactive {
      background: #fff;
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
    }
  }

  .isbottom {
    align-items: center;
    justify-content: space-between;

    .el-input {
      width: 210px;
    }

    .add {
      width: 16px;
      height: 16px;
    }
  }
}

.b-tabs {
  cursor: pointer;

  .b-t-item {
    margin-right: 24px;
    color: #8a929f;
    position: relative;

    &.isactive {
      color: #00a3ff;

      &::after {
        position: absolute;
        content: "";
        left: 50%;
        transform: translateX(-50%);
        height: 3px;
        background: #2d84fb;
        width: 100%;
        display: block;
        margin-top: 4px;
      }
    }
  }

  .config-customer {
    .el-button {
      padding: 7px 15px;
    }
  }
}

.search_loudong {
  background: #409eff;
  padding: 0 11px;
  margin-left: 10px;
  height: 100%;
  font-size: 13px;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;

  .seach_value {
    font-size: 14px;
    color: #fff;
  }

  .sanjiao {
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-top-color: #fff;
    margin-left: 5px;
    margin-top: 5px;

    &.transt {
      border-bottom-color: #fff;
      border-top-color: transparent;
      margin-top: 0;
      margin-bottom: 5px;
      /* transform: rotate(180deg); */
    }
  }
}

.clueRemark {
  .el-textarea {
    width: 360px;

    .el-textarea__inner {
      min-height: 40px !important;
      height: 40px;
    }
  }
}

.table-btns {
  .search-Belong {
    .el-button {
      padding: 4px 7px;
      margin-top: 3px;
      border-radius: 2px;
    }
  }

  .fast-look-tel {
    display: none;
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;
    opacity: 0.8;
    line-height: 1;

    i {
      color: #409eff;
    }

    // img {
    //   width: 100%;
    //   height: 100%;
    // }
  }
}

.labelname {
  margin-bottom: 10px;
}

.lables-box {
  flex-wrap: wrap;
  flex: 1;

  .labels-item {
    margin-bottom: 10px;
    cursor: pointer;
    background: #f1f4fa;
    border-radius: 4px;
    padding: 5px 22px;
    min-width: 80px;
    font-size: 16px;
    border: 1px solid #f1f4fa;
    text-align: center;
    margin-right: 24px;
    color: #8a929f;
  }

  .checked {
    background: rgba(45, 132, 251, 0.15);
    border: 1px solid rgba(45, 132, 251, 1);
    color: #2d84fb;
  }
}

.f-list {
  cursor: pointer;

  .f-item {
    padding: 8px 10px;

    &:hover {
      background: #2d84fb;
      color: #fff;
    }
  }
}

.inp_no_border {
  width: 155px;

  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.tips {
  margin-left: -20px;
  margin-right: -20px;
  background: #e7f3fd;
  padding: 15px 30px;
  margin-bottom: 30px;
  font-size: 14px;
  color: #8a929f;
}

.dialog_customer_label {
  height: 560px;
  overflow-y: auto;
}

.search-member-box {
  .el-input {
    .el-input__inner {
      width: 155px;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }
}

.cus-clue-label {
  align-items: center;
  flex-wrap: wrap;
  line-height: 1;

  .cus-icon-label {
    color: #16a1bc;
    border: 1px solid #16a1bc;
    // color: #409eff;
    // border: 1px solid #b3d8ff;
    font-size: 14px;
    padding: 5px 11px;
    border-radius: 2px;
    margin: 5px 10px 5px 0;
  }
}

.clueLabel {
  width: 20px;
  height: 20px;
  display: none;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.cus-clue-text {
  width: 270px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.label_list {
  flex-wrap: wrap;

  .label_item {
    margin-bottom: 5px;
  }
}

.el-table__body {
  .el-table__row {
    .is-center:nth-child(4):hover .clueLabel {
      display: block;
    }

    .is-center:nth-child(2):hover .fast-Edit-cus {
      display: block;
    }

    .is-center:nth-child(3):hover .fast-look-tel {
      display: block;
    }

    .is-center:nth-child(6):hover .followLabel {
      display: block;
    }
  }
}

.screen-type {
  display: flex;
  flex-direction: column;
  margin: -12px;
  padding: 6px 0px;

  .screen-type-content {
    display: flex;
    flex-direction: row;
    padding: 7px 22px;
    box-sizing: border-box;
    cursor: pointer;
  }

  .screen-type-content:hover {
    background-color: #f5f7fa;
  }
}

.public-status {
  // display: inline-block;
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  padding: 0 4px;
  border-radius: 4px;
}

.follow-content {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.followLabel {
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  right: 5px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
