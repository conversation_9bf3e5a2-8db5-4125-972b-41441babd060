<template>
  <div>
    <div class="pages">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="content-box-crm div row top" style="margin-bottom: 24px">
            <!-- 写筛选条件暂时没有 -->
            <span class="text" style="font-size: 14px; color: #8a929f"
              >筛选条件：</span
            >
            <myLabel :arr="file_list" @onClick="onClickType"></myLabel>
          </div>
          <div class="content-box-crm">
            <div class="table-top-box div row">
              <div class="t-t-b-left div row">
                <span class="text">共</span>
                <span class="blue">{{ total }}</span>
                <span class="text">条</span>
              </div>
              <div class="t-t-b-right">
                <el-button size="small" type="primary" @click="addData">
                  添加永久素材
                </el-button>
                <el-input
                  size="small"
                  placeholder="请输入标题"
                  v-model="params.keywords"
                  style="width: 256px; margin-left: 12px"
                  @change="onChangeKeywords"
                >
                  <span slot="append" class="el-icon-search"></span>
                </el-input>
              </div>
            </div>
            <el-table
              v-loading="is_table_loading"
              :data="tableData"
              border
              :header-cell-style="{ background: '#EBF0F7' }"
              highlight-current-row
              :row-style="$TableRowStyle"
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <!-- <el-table-column width="100" prop="id" label="ID"></el-table-column> -->
              <!-- <el-table-column prop="mediaid" label="企微素材ID">
            </el-table-column> -->
              <el-table-column prop="title" label="标题"></el-table-column>
              <el-table-column label="素材类型" v-slot="{ row }">
                <span v-if="row.type == 3">链接素材</span>
                <span v-if="row.type == 4">小程序素材</span>
              </el-table-column>
              <el-table-column label="有效状态">
                <span>永久素材</span>
              </el-table-column>
              <!-- <el-table-column prop="url" label="素材链接"> </el-table-column> -->
              <el-table-column
                prop="created_at"
                label="添加时间"
              ></el-table-column>
              <el-table-column label="操作" v-slot="{ row }">
                <el-button size="mini" type="success" @click="onEditData(row)">
                  编辑
                </el-button>
                <el-button size="mini" type="danger" @click="onDelData(row.id)">
                  删除
                </el-button>
              </el-table-column>
            </el-table>
            <el-pagination
              style="text-align: end; margin-top: 24px"
              background
              layout="prev, pager, next"
              :total="total"
              :page-size="params.per_page"
              :current-page="params.page"
              @current-change="onPageChange"
            >
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      :title="isAdd ? '添加永久素材' : '编辑永久素材'"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="cancel"
    >
      <el-form
        :model="form_file"
        label-position="left"
        label-width="128px"
        class="l_item"
      >
        <el-form-item label="素材类型: ">
          <el-radio-group
            v-model="form_file.type"
            :disabled="this.isAdd == false"
            @change="onChangemedialinshi"
          >
            <el-radio v-for="item in fileLabel" :key="item.id" :label="item.id">
              {{ item.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="素材标题：">
          <el-input
            style="width: 300px"
            maxlength="20"
            type="textarea"
            rows="1"
            show-word-limit
            v-model="form_file.title"
            placeholder="请填写素材标题"
          >
          </el-input>
        </el-form-item>
        <template v-if="form_file.type == 3">
          <el-form-item label="跳转链接：">
            <el-input
              style="width: 300px"
              v-model="form_file.url"
              placeholder="请填写跳转链接"
            >
            </el-input>
            <el-tooltip
              placement="top-start"
              width="200"
              trigger="hover"
              effect="light"
            >
              <div slot="content" style="line-height: 1.5">
                请输入完整的链接例如:https://tfy.tengfangyun.com/
              </div>
              <span icon="el-icon-info" type="danger">
                <i class="el-icon-info" style="color: #f56c6c"></i>
              </span>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              type="textarea"
              style="width: 300px"
              v-model="form_file.desc"
              :rows="2"
              maxlength="50"
              placeholder="请输入描述"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="封面图: ">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :headers="myHeader"
              :action="user_avatar"
              ref="uploadfile"
              :on-change="handleChange"
              :on-error="handleError"
              :on-success="handleSuccessAvatarTemporary"
              :on-remove="handleRemoveAvatorTemporary"
              :accept="fileaccept"
            >
              <!-- <el-button class="el-icon-download" size="small">
                本地上传
              </el-button> -->
              <img
                class="file_img"
                v-if="form_file.pic_url"
                :src="form_file.pic_url"
                alt=""
              />
              <i v-else class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span style="color: red">只能上传图片文件，且不能超过10M</span>
              </div>
            </el-upload>
          </el-form-item>
        </template>
        <template v-if="form_file.type == 4">
          <el-form-item label="跳转链接: ">
            <el-input
              style="width: 300px"
              v-model="form_file.url"
              placeholder="请填写跳转链接"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="小程序id：">
            <el-input
              style="width: 300px"
              v-model="form_file.appid"
              placeholder="请填写小程序addid"
            >
            </el-input>
          </el-form-item>

          <el-form-item label="封面图：">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              :headers="myHeader"
              :action="user_avatar"
              ref="uploadfile"
              :on-change="handleChange"
              :on-error="handleError"
              :on-success="handleSuccessAvatarTemporary"
              :on-remove="handleRemoveAvatorTemporary"
              :accept="fileaccept"
            >
              <!-- <el-button class="el-icon-download" size="small">
                本地上传
              </el-button> -->
              <img
                class="file_img"
                v-if="form_file.pic_url"
                :src="form_file.pic_url"
                alt=""
              />
              <i v-else class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <span style="color: red">只能上传图片文件，且不能超过10M</span>
              </div>
            </el-upload>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="onCreateMedias">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "@/components/navMain/crm/components/my_label";
import config from "@/utils/config";
export default {
  data() {
    return {
      //筛选条件
      file_list: [
        { id: 0, name: "全部", type: "all" },
        { id: 3, name: "H5素材", type: "h5" },
        { id: 4, name: "小程序素材", type: "min" },
      ],
      //永久素材列表参数
      params: {
        page: 1,
        per_page: 10,
        type: 0,
        keywords: "",
      },
      tableData: [],
      total: 0,
      //表格loading
      is_table_loading: false,
      //弹框控制
      dialogVisible: false,
      //判断是否为添加
      isAdd: true,
      //上传表单
      form_file: {
        type: 3
      },
      //图片上传地址
      user_avatar: `/api/common/file/upload/admin?category=${config.CATEGORY_IM_IMAGE}`
    };
  },
  components: {
    myLabel,
  },
  computed: {
    fileLabel() {
      return this.file_list.filter((item) => {
        return item.id
      }).concat([])
    },
    myHeader() {
      return { Authorization: config.TOKEN, }
    },
    //接受文件上传类型
    fileaccept() {
      let val = ""
      if (this.form_file.type == 3 && this.form_file.type == 4) {
        val = ".jpg,.png"
      }
      return val
    }
  },
  methods: {
    //获取列表数据
    foreverMediadData() {
      this.is_table_loading = true
      this.$http.myForeverMedias(this.params).then((res) => {
        this.is_table_loading = false
        if (res.status === 200) {
          this.total = res.data.total
          this.tableData = res.data.data
        }
      })
    },
    //分页
    onPageChange(current_page) {
      this.params.page = current_page;
      this.foreverMediadData();
    },
    //筛选条件
    onClickType(e) {
      this.params.type = e.id;
      this.params.page = 1;
      this.foreverMediadData()
    },
    //添加永久素材
    addData() {
      if (this.params.type == 3 || this.params.type == 0) {
        this.form_file = {
          type: 3,
          title: '',
          desc: '',
          pic_url: '',
          url: '',
        }
      } else if (this.params.type == 4) {
        this.form_file = {
          type: 4,
          title: '',
          pic_url: '',
          id: '',
          url: '',
          media_id: '',
          appid: ''
        }
      }

      this.dialogVisible = true;
      this.isAdd = true;
    },
    //编辑永久素材
    onEditData(row) {
      this.dialogVisible = true;
      this.isAdd = false;
      this.current = row
      if (row.type == 3) {
        this.form_file = {
          type: row.type,
          title: row.title,
          desc: row.desc,
          pic_url: row.pic_url,
          id: row.id,
          url: row.url,
        }
      } else if (row.type == 4) {
        this.form_file = {
          type: row.type,
          title: row.title,
          // desc: row.desc,
          pic_url: row.pic_url,
          id: row.id,
          url: row.url,
          media_id: row.media_id,
          appid: row.appid
        }
      }

    },
    //删除永久素材
    onDelData(e) {
      this.$confirm('此操作将删除改素材, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delMyForeverMedias(e).then((res) => {
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.foreverMediadData()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    //取消按钮
    cancel() {
      this.dialogVisible = false
      this.current = {}
      // this.reset()
    },
    //选择素材类型
    onChangemedialinshi(e) {
      let category = config.CATEGORY_IM_IMAGE;
      this.$refs.uploadfile && this.$refs.uploadfile.clearFiles();
      this.user_avatar = `/api/common/file/upload/admin?category=${category}`
      if (e == 3 || e == 4) {
        this.form_file.pic_url = ""
      }
      this.reset()
    },
    //重置表单
    reset() {
      let id = this.form_file.id
      if (this.form_file.type == 3) {
        this.form_file = {
          type: 3,
          title: '',
          desc: '',
          pic_url: '',
          url: '',
        }
      } else if (this.form_file.type == 4) {
        this.form_file = {
          type: 4,
          title: '',
          // desc: '',
          pic_url: '',
          id: '',
          url: '',
          media_id: '',
          appid: ''
        }
      }
      if (id) {
        this.form_file.id = id
      }
      // id && this.form_file.id = id
      setTimeout(() => {
        this.$refs.uploadfile && this.$refs.uploadfile.clearFiles()
      }, 100)
    },
    //获取上传文件大小
    handleChange(file) {
      let imgSize = Number(file.size / 1024 / 1024)
      if ((this.form_file.type == 5 || this.form_file.type == 6) &&
        imgSize > 10) {
        this.$message.error("图片不能大于10M，请重新上传");
        return
      }
    },
    //检查文件类型
    handleError() {
      this.$message.error("请检查上传文件类型是否正确")
    },
    //图片上传成功
    handleSuccessAvatarTemporary(response) {
      this.form_file.pic_url = response.url
      if (this.form_file.type == 4) {
        this.getMediaId(response.url)
      }

    },
    async getMediaId(url, type = 1) {
      let params = {}
      params.type = type  //  1 图片 3 视频 4 文件
      params.url = url
      let res = await this.$http
        .getAvatarId(params)
        .catch(() => this.$message.error("获取素材id失败 请检查素材大小"));
      this.form_file.media_id = res.data.media_id;
    },
    //移除图片
    handleRemoveAvatorTemporary(response) {
      this.form_file.pic_url = response.url;
      this.form_file.media_id = '';
    },
    //搜索
    onChangeKeywords() {
      this.params.page = 1;
      this.foreverMediadData()
    },
    //确定提交
    onCreateMedias() {
      if (!this.form_file.title) {
        this.$message.error("请检查内容后提交")
        return;
      }
      let file = {};
      file = Object.assign({}, this.form_file)
      if (this.form_file.type == 3) {
        if (!this.form_file.url) {
          this.$message.warning("请输入跳转链接")
          return
        }
        if (!this.form_file.desc) {
          this.$message.warning("请输入描述")
          return
        }
        delete this.form_file.appid
      }
      if (this.form_file.type == 4) {
        if (!this.form_file.url) {
          this.$message.warning("请输入小程序页面地址")
          return
        }
        if (!this.form_file.appid) {
          this.$message.warning("请输入小程序appid")
          return
        }
      }
      if (this.isAdd) {
        this.$http.addMyForeverMedias(file).then((res) => {
          if (res.status == 200) {
            this.$message.success("添加成功")
            this.cancel()
            this.foreverMediadData()
          }
        })
      } else {
        this.$http.editMyForeverMedias(file).then((res) => {
          if (res.status == 200) {
            this.$message.success("修改成功")
            this.cancel()
            this.foreverMediadData()
          }
        })
      }


    }


  },
  mounted() {
    this.foreverMediadData()
  }
};
</script>

<style  scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .top {
    align-items: center;
  }
  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }
  .l_item ::v-deep .el-radio {
    margin-bottom: 10px;
  }
}
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  width: 178px;
  height: 178px;
  text-align: center;

  overflow: hidden;
}
::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep .avatar-uploader .el-icon-plus {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.file_img {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
