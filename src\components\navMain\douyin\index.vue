<template >
  <div class="douyin_index">
    <div class="el-row">
      <div class="el-col" span="24">
        <div class="data">
          <div class="item_title flex-row items-center">
            <div class="flex-1">风向标</div>
            <div class="small red">每日12点前更新</div>
          </div>
          <div class="datas_list flex-row items-center">
            <div class="datas_item flex-row items-center flex-1" v-for="(item, index) in statistics" :key="index">
              <div class="datas_item_right">
                <div class="datas_item_right_name flex-row items-center">
                  <span>
                    {{ item.name }}
                  </span>

                  <el-tooltip class="item" effect="light" placement="top" v-if="item.tips">
                    <div slot="content" style="max-width: 300px">
                      {{ item.tips }}
                    </div>
                    <div class="img">
                      <img :src="warning" alt="" />
                    </div>
                  </el-tooltip>
                  <!-- <view class="img" v-else>
                    <img :src="warning" alt="" />
                  </view> -->
                </div>
                <div class="datas_item_right_top flex-row">
                  <span>{{ item.total | formatNum }} </span>
                  <span class="small"> {{ item.unit }} </span>
                </div>
                <div class="datas_item_right_bottom flex-row">
                  <span> 昨日新增： </span>
                  <span class="change" :class="{ jian: setClass(item.title) }">
                    {{ item.title }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="el-row">
      <div class="el-col" span="24">
        <div class="datas">
          <div class="item_title">榜单</div>
          <div class="bangdan_list flex-row">
            <div class="item-col flex-1" :style="{
                            minWidth: `calc((100% - 25*${list.length - 1}px)/${
                              list.length
                            })`,
                          }" v-for="(item, index) in list" :key="index">
              <div class="bangdan_item">
                <div class="bangdan_item_title flex-row items-center">
                  <!-- <div
                    class="bangdan_img"
                    :class="['bangdan_img' + index]"
                  ></div> -->
                  <div class="bangdan_title">
                    {{ index | filterName }}
                  </div>
                </div>
                <div class="bangdan_item_item">
                  <div class="bangdan_item_item_item flex-row items-center" :class="{ point: build.share_url }"
                    v-for="(build, idx) in item" :key="idx" @click="toUrl(build)">
                    <div class="bangdan_item_item_item_name bangdan_item_item_index">
                      <div class="jiangbei" v-if="idx < 3">
                        <img :src="
                                                    'https://img.tfcs.cn/backup/static/admin/douyin/jiangbei' +
                                                    idx +
                                                    '.png'
                                                  " alt="" />
                      </div>
                      <span v-else>{{ idx + 1 }}</span>
                    </div>
                    <template v-if="
                                            index !== 'topic' &&
                                            index !== 'trending_sentences' &&
                                            index !== 'sentence'
                                          ">
                      <div class="bangdan_item_item_item_name bangdan_item_item_img flex-row">
                        <div class="avatar">
                          <img :src="build.item_cover || build.cover" alt="" />
                        </div>
                      </div>
                      <div class="bangdan_item_item_item_name bangdan_item_item_name flex-1">
                        {{ build.author || build.nickname }}
                      </div>
                    </template>
                    <template v-else>
                      <div class="bangdan_item_item_item_name bangdan_item_item_name flex-1">
                        {{ build.title || build.sentence }}
                      </div>
                    </template>

                    <div class="bangdan_item_item_item_name bangdan_item_item_area">
                      <template v-if="index == 'hotVideo'">
                        {{ build.hot_value | formatNum }}播放
                      </template>
                      <template v-if="index == 'live'">
                        {{ build.hot_value | formatNum }}人气
                      </template>
                      <template v-if="
                                                index == 'topic' ||
                                                index == 'trending_sentences' ||
                                                index == 'sentence'
                                              ">
                        {{
                                                (build.effect_value || build.hot_level) | formatNum
                                                }}人气
                      </template>
                      <!-- <template v-if="index == 'live'">
                        {{ build.hot_value | formatNum }}万人气
                      </template> -->
                      <!-- {{ build.title }} -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {

  data() {
    return {
      "statistics": [],
      "list": {},
      minWidth: '',
      warning: 'https://img.tfcs.cn/backup/static/admin/douyin/douyin_warning.png'
    }
  },
  filters: {
    filterName(val) {
      let name = ''
      switch (val) {
        case 'hotVideo':
          name = '热门视频'
          break;
        case 'live':
          name = '直播榜单'
          break;
        case 'sentence':
          name = '热门词'
          break;
        case 'trending_sentences':
          name = '上升热门词'
          break;
        case 'topic':
          name = '话题'
          break;

        default:
          break;
      }
      return name

    },
    formatNum(val) {
      if (val < 100000) return val
      return (val / 10000).toFixed(2) + "万"
    },


  },
  created() {
    this.getData()
    this.getList()
  },
  methods: {
    getData() {
      this.$http.getDouyinStatics().then(res => {
        if (res.status == 200) {
          let arr = []
          for (const key in res.data) {
            switch (key) {
              case 'new_fans':
              case 'total_fans':
                arr[0] = {}
                arr[0]['total'] = res.data['total_fans'] || 0
                arr[0]['title'] = res.data['new_fans'] || 0
                // arr[0][key] = res.data[key]
                arr[0].name = '粉丝总数'
                arr[0].unit = '人'
                arr[0]['tips'] = res.data['fans_tips'] || ''
                break;
              case 'new_issue':
              case 'total_issue':
                arr[1] = {}
                arr[1]['total'] = res.data['total_issue'] || 0
                arr[1]['title'] = res.data['new_issue'] || 0
                arr[1].name = '作品总量'
                arr[1].unit = '个'
                arr[1]['tips'] = res.data['issue_tips'] || ''
                break;
              case 'new_profile_uv':
              case 'total_profile_uv':
                arr[2] = {}
                arr[2]['total'] = res.data['total_profile_uv'] || 0
                arr[2]['title'] = res.data['profile_uv'] || 0
                arr[2].name = '访问总量'
                arr[2].unit = '次'
                arr[2]['tips'] = res.data['profile_uv_tips'] || ''
                break;
              case 'new_share':
              case 'total_share':
                arr[3] = {}
                arr[3]['total'] = res.data['total_share'] || 0
                arr[3]['title'] = res.data['new_share'] || 0
                arr[3].name = '分享总量'
                arr[3].unit = '次'
                arr[3]['tips'] = res.data['share_tips'] || ''
                break;
              case 'new_comment':
              case 'total_comment':
                arr[4] = {}
                arr[4]['total'] = res.data['total_comment'] || 0
                arr[4]['title'] = res.data['new_comment'] || 0
                arr[4].name = '评论总量'
                arr[4].unit = '次'
                arr[4]['tips'] = res.data['comment_tips'] || ''
                break;
              case 'new_like':
              case 'total_like':
                arr[5] = {}
                arr[5]['total'] = res.data['total_like'] || 0
                arr[5]['title'] = res.data['new_like'] || 0
                arr[5].name = '点赞总量'
                arr[5].unit = '次'
                arr[5]['tips'] = res.data['like_tips'] || ''
                break;
              case 'new_play':
              case 'total_play':
                arr[6] = {}
                arr[6]['total'] = res.data['total_play'] || 0
                arr[6]['title'] = res.data['new_play'] || 0
                arr[6].name = '播放总量'
                arr[6].unit = '次'
                arr[6]['tips'] = res.data['play_tips'] || ''
                break;

              default:
                break;
            }
          }
          this.statistics = arr
        }
      })
    },
    getList() {
      this.$http.getDouyinIndexList().then(res => {
        console.log(res);
        if (res.status == 200) {
          this.list = res.data
          if (res.data.length) {
            // this.minWidth = `calc(100% - 25*${this.list.length - 1})/${this.list.length}`
            // this.minWidth = 'calc(100% - 25*' + (this.list.length - 1) + ')/' + this.list.length
          }

        }
      })
    },
    setClass(val) {
      if (val && val.indexOf("-") > -1) return true
      return false
    },
    toUrl(item) {
      if (!item.share_url) return
      window.open(item.share_url)
    }

  }
}
</script>
<style scoped lang="scss">
.items-center {
  align-items: center;
}

.douyin_index {
  // min-height: 100vh;
  background: #f1f4fa;
  margin: -14px;
  padding: 24px;
}

.datas_list {
  margin-top: 32px;

  .datas_item {
    margin-right: 24px;
    background: #fff;
    border-radius: 10px;
    padding: 24px;

    &.point {
      cursor: pointer;
    }

    &:last-child {
      margin-right: 0;
    }

    .datas_item_left {
      width: 35px;
      height: 35px;
      margin-right: 25px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .change {
      color: #fa6060;

      &.jian {
        color: #37e780;
      }
    }

    .datas_item_right {
      .datas_item_right_name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #2e3c4e;

        .img {
          width: 16px;
          height: 16px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .datas_item_right_top {
        color: #464d59;
        font-size: 18px;
        align-items: flex-end;
        font-family: PingFangSC-Medium, sans-serif;
        font-weight: 600;

        .small {
          font-size: 16px;
          font-weight: normal;
        }
      }

      .datas_item_right_bottom {
        color: #768196;
        margin-top: 5px;
        font-size: 13px;
      }
    }
  }
}

.item_title {
  color: #000000;
  font-family: PingFang-Medium, sans-serif;
  font-weight: 600;
  font-size: 22px;

  .small {
    font-size: 14px;
    font-weight: normal;
    color: #fa6060;
  }
}

.datas .item_title {
  margin-top: 25px;
}

.bangdan_list {
  margin-top: 10px;

  .item-col {
    margin-right: 25px;
    min-width: calc((100% - 100px) / 5);

    &:last-child {
      margin-right: 0;
    }
  }
}

.bangdan_item {
  border-radius: 10px;
  background: #ffffff;
  min-height: 520px;
  // margin-right: 24px;

  .bangdan_item_title {
    padding: 23px 23px 10px;
    color: #464d59;
    font-weight: 600;
    font-family: PingFangSC-Medium, sans-serif;
    font-size: 18px;

    .bangdan_title {
      color: #464d59;
      font-family: PingFang SC;
      font-family: PingFangSC-Medium, sans-serif;
      font-weight: medium;
      font-size: 18px;
    }

    .bangdan_img {
      width: 26px;
      height: 26px;
      overflow: hidden;
      margin-right: 8px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .bangdan_item_item {
    padding: 0 20px 20px;
    min-width: 50px;

    .bangdan_item_item_header {
      padding: 10px 0;

      .bangdan_item_item_header_name {
        color: #464d59;
        font-family: PingFangSC-regular, sans-serif;
        font-weight: regular;
        font-size: 15px;
      }
    }

    .bangdan_item_item_item {
      padding: 10px 0;
      min-height: 32px;

      &.point {
        cursor: pointer;
      }

      .video_single {
        align-self: flex-start;

        .bangdan_item_item_item_name {
          max-width: 100%;
        }

        .bangdan_item_item_area {
          margin-top: 30px;
        }
      }

      .bangdan_item_item_item_name {
        color: #2e3c4e;
        font-family: PingFangSC-Medium, sans-serif;
        font-size: 15px;

        &.video {
          flex-direction: column;
        }

        &.bangdan_item_item_area {
          color: #8a929f;
          font-size: 12px;
        }

        span {
          background: #afafaf;
          // padding: 3px;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 16px;
          height: 16px;
          color: #fff;
          font-size: 12px;
          border-radius: 2px;
        }

        &.bangdan_item_item_item_name1 {
          span {
            background: #fa6060;
          }
        }

        &.bangdan_item_item_item_name2 {
          span {
            background: #fa8560;
          }
        }

        &.bangdan_item_item_item_name3 {
          span {
            background: #faaa60;
          }
        }
      }
    }

    .bangdan_item_item_index {
      // width: 50px;
      margin-right: 5px;

      .jiangbei {
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          object-fit: cover;
        }
      }
    }

    .bangdan_item_item_img {
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 5px;
        overflow: hidden;

        &.video {
          width: 48px;
          height: 64px;
          border-radius: 5px;
        }

        img {
          width: 100%;
          object-fit: cover;
        }
      }
    }

    .bangdan_item_item_name {
      max-width: calc(100% - 90px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.video {
        display: block;
      }
    }

    .bangdan_item_item_area {
      // width: 80px;
      min-width: 50px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}</style>