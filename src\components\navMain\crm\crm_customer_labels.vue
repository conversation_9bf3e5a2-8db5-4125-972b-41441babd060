<template>
  <!--  标签库 -->
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div
            class="bottom-border div row"
            style="border-bottom: 1px solid #e2e2e2"
          >
            <span class="text">创建标签：</span>
            <el-button
              class="el-icon-plus"
              type="primary"
              size="mini"
              @click="addLabelData"
              >新建标签</el-button
            >
            <el-button
              class="el-icon-download"
              type="primary"
              size="mini"
              @click="getLabelData"
              >拉取标签</el-button
            >
          </div>
          <div
            class="bottom-border div row"
            style="margin-top: 20px; padding-bottom: 0"
          >
            <span class="text">创建时间：</span>
            <myLabel :arr="time_list" @onClick="onClickTime"></myLabel>
            <span class="text">自定义：</span>
            <el-date-picker
              style="width: 250px"
              size="small"
              v-model="value1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeTime"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="content-box-crm">
          <div class="table-top-box div row">
            <div class="t-t-b-left div row">
              <span class="text">共</span>
              <span class="blue">{{ params.total }}</span>
              <span class="text">条</span>
              <span class="text" style="margin: 0 12px">|</span>
              <span class="text">已选</span>
              <span class="blue">{{ multipleSelection.length }}</span>
              <span class="text">个</span>
              <el-select
                style="width: 107px; margin-left: 14px"
                v-model="select_value"
                placeholder="请选择"
                size="small"
                clearable
                @change="onChangeSelect"
              >
                <el-option
                  v-for="item in select_list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <el-table
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
            v-loading="is_table_loading"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="标签组名称" prop="name"></el-table-column>
            <el-table-column label="标签" v-slot="{ row }">
              <el-tag
                type="primary"
                v-for="item in row.subset"
                :key="item.id"
                size="mini"
                style="margin-right: 10px"
                closable
                @close="onDelete(item)"
                >{{ item.name }}</el-tag
              >
            </el-table-column>
            <el-table-column
              label="创建时间"
              width="200"
              prop="created_at"
            ></el-table-column>
            <el-table-column
              width="100"
              label="操作"
              fixed="right"
              v-slot="{ row }"
            >
              <el-link type="primary" @click="onChangeEdit(row)">编辑</el-link>
              <el-link
                type="primary"
                style="margin-left: 20px"
                @click="onDelete(row)"
                >删除</el-link
              >
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
      width="660px"
      :before-close="cancel"
    >
      <el-form
        :model="form_info"
        ref="dynamicValidateForm"
        label-width="130px"
        label-position="right"
      >
        <el-form-item
          label="标签组名称："
          prop="group_name"
          :rules="[{ required: true, message: '请输入标签组名称' }]"
        >
          <el-input
            style="width: 360px"
            placeholder="请输入"
            v-model="form_info.group_name"
            :disabled="dialogTitle === 'updataData'"
          ></el-input>
        </el-form-item>
        <div class="ove">
          <el-form-item
            v-for="(domain, index) in form_info.tag"
            label="标签"
            :key="domain.key"
            :prop="'tag.' + index + '.name'"
            :rules="{
              required: true,
              message: '标签名称不能为空',
              trigger: 'blur',
            }"
          >
            <el-input
              style="width: 360px"
              placeholder="请输入"
              v-model="domain.name"
            ></el-input
            ><span
              v-if="index"
              class="el-icon-close"
              style="font-size: 24px; color: #e3e3e3; margin-left: 10px"
              @click.prevent="removeDomain(domain)"
            ></span>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button
            @click="addDomain"
            class="el-icon-plus"
            type="primary"
            size="small"
            >添加新标签</el-button
          >
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-loading="is_button_loading"
          :disabled="is_button_loading"
          @click="onCreateData('dynamicValidateForm')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import myLabel from "./components/my_label";
export default {
  name: "crm_customer_labels",
  components: {
    myLabel,
  },
  data() {
    return {
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
      ],
      select_list: [{ id: 1, name: "删除" }],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        create_time: "",
      },
      multipleSelection: [],
      is_table_loading: false,
      tableData: [],
      value1: "",
      form_info: {
        order: 1,
        group_id: "",
        tag: [
          {
            name: "",
            key: Date.now(),
          },
        ],
      },
      dialogCreate: false,
      titleMap: {
        addData: "新建标签",
        updataData: "编辑标签",
      },
      dialogTitle: "",
      select_value: "",
      is_button_loading: false,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    //添加关闭之后的回调
    cancel(){
      this.dialogCreate=false;
      this.form_info={
        order: 1,
        group_id: "",
        tag: [
          {
            name: "",
            key: Date.now(),
          },
        ],
      }
      this.$refs.dynamicValidateForm.resetFields()
    },
    onChangeTime(e) {
      this.params.create_time = e ? e.join(",") : 0;
      this.params.page = 1;
      this.getDataList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getDataList() {
      this.is_table_loading = true;
      this.$http
        .getCrmCustomerLabelsList({ params: this.params })
        .then((res) => {
          this.is_table_loading = false;
          if (res.status === 200) {
            this.tableData = res.data.data;
            this.params.total = res.data.total;
          }
        });
    },
    addLabelData() {
      this.dialogCreate = true;
      this.dialogTitle = "addData";
      this.form_info = {
        order: 1,
        group_id: "",
        tag: [
          {
            name: "",
            key: Date.now(),
          },
        ],
      };
    },
    onClickTime(e) {
      this.params.create_time = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList();
    },
    onChangeEdit(row) {
      this.dialogTitle = "updataData";
      this.dialogCreate = true;
      this.form_info = {
        group_id: row.tagid,
        group_name: row.name,
        order: row.parent_order || 1,
        tag: [
          {
            name: "",
            key: Date.now(),
          },
        ],
      };
    },
    onDelete(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http.deleteCrmCustomerLabels({ ids: row.tagid }).then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
    onChangeSelect(e) {
      let arr = this.multipleSelection.map((item) => {
        return item.tagid;
      });
      switch (e) {
        case 1:
          var form = {
            ids: arr.join(","),
          };
          this.$confirm("是否删除", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              // 点击确认调用公众号授权
              this.$http.deleteCrmCustomerLabels(form).then((res) => {
                if (res.status === 200) {
                  this.$message.success("操作成功");
                  this.getDataList();
                }
              });
            })
            .catch(() => {
              // 点击取消控制台打印已取消
              console.log("已取消");
            });
          break;

        default:
          break;
      }
    },
    addDomain() {
      this.form_info.tag.push({
        name: "",
        key: Date.now(),
      });
    },
    removeDomain(item) {
      var index = this.form_info.tag.indexOf(item);
      if (index !== -1) {
        this.form_info.tag.splice(index, 1);
      }
    },
    onCreateData(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.form_info.tag = this.form_info.tag.map((item) => {
            return {
              name: item.name,
              order: 1,
            };
          });
          this.is_button_loading = true;
          this.$http.createCrmCustomerLabelsData(this.form_info).then((res) => {
            this.is_button_loading = false;
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
              this.dialogCreate = false;
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getLabelData() {
      this.$confirm("是否拉取标签", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 点击确认调用公众号授权
          this.$http.getCrmCustomerLabelsPull().then((res) => {
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.getDataList();
            }
          });
        })
        .catch(() => {
          // 点击取消控制台打印已取消
          console.log("已取消");
        });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .top {
    align-items: center;
  }
  .t-t-b-right /deep/ .el-input-group__append {
    background: #fff;
  }
  .bottom-border {
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 20px;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
.ove {
  max-height: 300px;
  overflow: auto;
}
</style>
