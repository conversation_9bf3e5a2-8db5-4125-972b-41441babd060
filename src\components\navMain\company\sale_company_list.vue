<template>
  <el-container>
    <el-header class="div row">
      <myTopTips title="公司列表" :number="tableData.length">
        <div class="add-build" v-if="$hasShow('添加销售公司')">
          <el-button type="primary" @click="addData" icon="el-icon-plus">添加公司/门店</el-button>
          <el-button type="success" @click="exportExcel">导出Excel表格</el-button>
          <el-button type="primary" @click="importExcel">批量导入</el-button>
        </div>
      </myTopTips>

      <div class="div row">
        <el-input placeholder="请输入内容" v-model="search_val" class="input-with-select" style="width: 400px"
          @change="onChange">
          <el-select v-model="search_select" slot="prepend" placeholder="请选择" style="width: 120px">
            <el-option v-for="item in search_select_list" :key="item.value" :label="item.description"
              :value="item.value"></el-option>
          </el-select>
        </el-input>
      </div>
    </el-header>
    <div class="browse-table">
      <div class="browse div row">
        <div class="browse-item" v-for="(item, index) in time_array" :key="index"
          :class="{ browse_active: item.value === list_params.date_str }" @click="onClickBrowse(index, item.id)">
          {{ item.desc }}
        </div>
      </div>
      <div class="block-time div row" v-if="isCustomize">
        <el-date-picker v-model="list_params.start" type="date" placeholder="请选择开始日期" value-format="yyyy-MM-dd">
          >
        </el-date-picker>
        <el-date-picker v-model="list_params.end" type="date" value-format="yyyy-MM-dd" placeholder="请选择结束日期">
          >
        </el-date-picker>
        <el-button type="primary" @click="clickTime">查询</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>成交套数</p>
            </div>
            <div class="right">{{ company_info.total }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>成交金额/万元</p>
            </div>
            <div class="right">{{ company_info.deal_amount }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content bg-purple div row">
            <div class="left">
              <p>业绩金额/元</p>
            </div>
            <div class="right">
              {{ company_info.brokerage_amount }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-tabs v-model="params.orderby" type="card" @tab-click="handleClick">
      <el-tab-pane v-for="item in params_list" :key="item.id" :label="item.name" :name="item.id">
      </el-tab-pane>
      <myTable v-loading="is_table_loading" :table-list="tableData" :header="table_header"></myTable>
    </el-tabs>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination :total="params.total" :pagesize="params.per_page" :currentPage="params.page"
          @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"></myPagination>
      </div>
    </el-footer>
    <el-dialog title="批量导入" :visible.sync="showBatchImport" :close-on-click-modal="false" width="660px">
      <div class="labelrow">
        <el-link type="primary" style="margin-right: 12px" @click="onUploadTem">
          1、点击下载导入数据模块
        </el-link>
        <span class="text">将要导入的数据填充到数据导入模板之中</span>
      </div>
      <div class="labelrow">注意事项：</div>
      <div class="labelrow" v-for="(item, index) in tips" :key="index">
        <span class="text">{{ item }}</span>
      </div>
      <!-- 上传文件 -->
      <div>
        <el-upload ref="fileUpload" class="upload-demo" action="" :auto-upload="false" :limit="1" :on-change="changeFile"
          :on-remove="removeFile">
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传xlsx文件</div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showBatchImport = false">取 消</el-button>
        <el-button type="primary" @click="confirmImport" :loading="is_loading">导 入</el-button>
      </span>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import axios from "axios"
export default {
  name: "sale_company_list",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      // 搜索框数据
      input: "",
      tableData: [],
      // 存放列表图片
      imgbox: [],
      category_list: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
        name: "",
        category: 2,
        orderby: "all",
      },
      params_list: [
        { id: "all", name: "全部" },
        { id: "broker_amount", name: "经纪人" },
        { id: "customer_amount", name: "客户量" },
        { id: "deal_amount", name: "成交量" },
        { id: "finished_brokerage", name: "已结佣" },
      ],
      time_array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
        { desc: "自定义", value: "customize", id: 8 },
      ],
      search_select_list: [
        {
          value: 1,
          description: "公司名称",
        },
        { value: 2, description: "门店码" },
      ],
      list_params: {
        date_str: "day",
        start: "",
        end: "",
      },
      isCustomize: false,
      company_info: {},
      table_header: [
        { prop: "id", label: "ID", width: "80" },
        {
          prop: "name",
          label: "公司名称",
          render: (h, data) => {
            return (
              <div class=" company-name">
                <img
                  style="height:50px;width:65px;"
                  src={this.$imageFilter(
                    data.row.logo || "https://img.tfcs.cn/static/img/que.jpg",
                    "w_240"
                  )}
                />
                <div class="right">
                  <el-link type="primary" underline={false} class="name">
                    {data.row.name}
                    <el-tag style="margin-left:5px" size="mini" type="success">
                      {data.row.store_category === 0 ? "门店" : "总公司"}
                    </el-tag>
                    {data.row.status === 0 ? (<el-tag style="margin-left:5px" size="mini" type="warning">
                      {data.row.status === 0 ? "已禁用" : "正常"}
                    </el-tag>) : (<el-tag style="margin-left:5px" size="mini" type="success">
                      {data.row.status === 0 ? "已禁用" : "正常"}
                    </el-tag>)}
                  </el-link>
                  <el-link type="primary" underline={false} class="address">
                    {data.row.full_address}
                  </el-link>
                </div>
              </div>
            );
          },
        },
        {
          label: "经理/店长信息",
          width: "260",
          render: (h, data) => {
            return (
              <div>
                {data.row.u_store_manager.map((item) => {
                  return (
                    <el-tag style="margin:4px;width:160px">
                      {(item.u_name || item.u_user_name || item.u_nickname) +
                        "：" +
                        item.u_phone}
                    </el-tag>
                  );
                })}
              </div>
            );
          },
        },
        { label: "经纪人", prop: "broker_amount", width: "100" },
        { label: "客户量", prop: "customer_amount", width: "100" },
        { label: "成交量", prop: "deal_amount", width: "100" },
        { label: "已结佣", prop: "finished_brokerage", width: "100" },
        {
          label: "门店码",
          width: "100",
          render: (h, data) => {
            return <el-tag type="success">{data.row.store_code}</el-tag>;
          },
        },
        { prop: "updated_at", label: "添加时间", width: "160" },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改销售公司") ? (
                  <el-button
                    style="margin:5px;width:120px;border-radius:5px"
                    icon="el-icon-edit"
                    size="mini"
                    type="primary"
                    onClick={() => {
                      this.updataCompany(0, data.row);
                    }}
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("公司成员") ? (
                  <el-button
                    style="margin:5px;;width:120px;border-radius:5px"
                    icon="el-icon-user-solid"
                    size="mini"
                    type="success"
                    onClick={() => {
                      this.seeUsers(data.row);
                    }}
                  >
                    查看成员（{data.row.employee_total}）
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("公司经理") && data.row.store_category === 1 ? (
                  <el-button
                    style="margin:5px;;width:120px;border-radius:5px"
                    icon="el-icon-s-custom"
                    type="primary"
                    size="mini"
                    onClick={() => {
                      this.companyManager(data.row);
                    }}
                  >
                    公司经理
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("业绩统计") && data.row.store_category === 0 ? (
                  <el-button
                    style="margin:5px;;width:120px;border-radius:5px"
                    icon="el-icon-data-analysis"
                    size="mini"
                    type="primary"
                    onClick={() => {
                      this.statistics(data.row);
                    }}
                  >
                    业绩统计
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("公司门店") && data.row.store_category === 1 ? (
                  <el-button
                    style="margin:5px;width:120px;border-radius:5px"
                    icon="el-icon-s-home"
                    size="mini"
                    type="success"
                    onClick={() => {
                      this.goStore(data.row);
                    }}
                  >
                    公司门店
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("刷新门店码") &&
                  data.row.store_category === 0 ? (
                  <el-button
                    plain
                    style="margin:5px;width:120px;border-radius:5px"
                    icon="el-icon-refresh"
                    size="mini"
                    type="success"
                    onClick={() => {
                      this.refreshCode(data.row);
                    }}
                  >
                    刷新门店码
                  </el-button>
                ) : (
                  ""
                )}
                {this.$hasShow("删除销售公司") ? (
                  <el-button
                    style="margin:5px;width:120px;border-radius:5px"
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    onClick={() => {
                      this.handleDelete(0, data.row);
                    }}
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      // 搜索条件
      search_val: "",
      search_select: 1,
      is_table_loading: true,
      showBatchImport: false, // 显示/隐藏批量导入模态框
      tips: [
        "公司名称必须唯一的。",
        "单次导入最多支持500条记录。",
        "每次导入仅支持一个总公司，所添加的门店均归属于该总公司。",
        "总公司信息可以为空，若为空则表示所添加的门店均为独立门店。",
        "导入时请注意，附属门店和独立门店需分别导入，每次只能导入其中一种类型。",
        "请务必正确填写店长手机号，所填写的手机号必须在用户列表中存在，并且该用户必须是经纪人。",
      ],
      uploadFile: {}, // 上传的文件属性容器
      is_loading: false, // loading加载动画
    };
  },
  created() { },
  mounted() {
    this.getDataList();
    this.getCompanyInfo();
  },
  methods: {
    handleClick() {
      this.getDataList();
    },
    // 获取列表数据
    getDataList() {
      this.is_table_loading = true;
      let params = JSON.parse(JSON.stringify(this.params));
      if (!params.orderby || params.orderby === "all") {
        delete params.orderby;
      }
      this.$http.showCompanyLists({ params: params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    onChange(e) {
      if (this.search_select === 1) {
        delete this.params.store_code;
        this.params.name = e;
      } else {
        delete this.params.name;
        this.params.store_code = e;
      }
      if (!e) {
        delete this.params.name;
        delete this.params.store_code;
      }
      this.params.page = 1;
      this.getSaleCompanyListParams();
    },
    getSaleCompanyListParams() {
      this.$http.showCompanyLists({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total;
        }
      });
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    seeUsers(row) {
      this.$goPath("/company_users?company_id=" + row.id);
    },
    // 点击进入添加楼盘界面
    addData() {
      this.$goPath("/upload_company?company_category=2");
    },
    // 操作

    // 删除操作
    handleDelete(index, row) {
      this.$confirm("此操作将删除该公司, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCompany(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 修改操作
    updataCompany(index, row) {
      this.$goPath(`/updata_company?id=${row.id}`);
    },
    refreshCode(row) {
      this.$http.refreshCompanyCode(row.id).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "刷新成功",
            type: "success",
          });
          this.getDataList();
        }
      });
    },
    statistics(row) {
      this.$goPath(`/statistics_list?id=${row.id}`);
    },
    // 点击选择日期数据
    onClickBrowse(index, id) {
      this.list_params.date_str = this.time_array[index].value;
      if (id === 8) {
        this.isCustomize = true;
      } else {
        this.isCustomize = false;
        this.list_params.start = "";
        this.list_params.end = "";
        this.getCompanyInfo();
      }
    },
    getCompanyInfo() {
      this.$http.getCompanyData(this.list_params).then((res) => {
        if (res.status === 200) {
          this.company_info = res.data;
        }
      });
    },
    clickTime() {
      if (!this.list_params.start) {
        this.$message({
          message: "请选择开始时间",
          type: "error",
        });
      } else if (!this.list_params.end) {
        this.$message({
          message: "请选择结束时间",
          type: "error",
        });
      } else {
        this.getCompanyInfo();
      }
    },
    // 点击进入公司门店
    goStore(row) {
      this.$goPath(`/company_store?company_id=${row.id}`);
    },
    // 公司经理列表
    companyManager(row) {
      this.$goPath(`/company_manager?company_id=${row.id}`);
    },
    // 过滤表格标识
    filterTag(value, row) {
      return row.store_category === parseInt(value);
    },
    exportExcel() {
      this.$message({
        showClose: true,
        message: "正在生成表格...",
        type: "warning",
      });
      this.$http.exportExcelForSale({ params: this.params }).then((res) => {
        if (res.status === 200) {
          // this.$exportExcelTable(res);
          window.open(res.data);
        }
      });
    },
    // 批量导入Excel表格
    importExcel() {
      this.showBatchImport = true; // 显示批量导入模态框
    },
    // 确定批量导入Excel表格
    confirmImport() {
      if(Object.keys(this.uploadFile).length === 0) {
        return this.$message.warning("请先上传Excel表格")
      }
      let file = new FormData(); // 创建一个FormData对象
      file.append('file', this.uploadFile); // 赋值导入的文件
      file.append('website_id', localStorage.getItem("website_id")); // 赋值站点id
      this.$message.success("正在导入中.....");
      this.is_loading = true; // 开启loading动画
      axios.post('/api/common/company_module/import/company', file, { headers: { Authorization: "Bearer " + localStorage.getItem("TOKEN") } }).then(res => {
        if(res.status == 200) {
          this.$message.success("批量导入成功");
          this.showBatchImport = false; // 隐藏批量导入模态框
          this.is_loading = false; // 关闭加载动画
          this.uploadFile = {}; // 清空上传文件数据容器
          this.$refs.fileUpload.clearFiles(); // 清空上传文件数据
          this.getDataList(); // 获取最新数据
        }
      }).catch((error) => {
        // 提示错误信息
        if(error.response) {
          this.$message.error(error.response.data.message);
        }
        this.is_loading = false;
      })
    },
    // 点击下载批量导入模板
    onUploadTem() {
      window.open(
        "https://img.tfcs.cn/backup/static/admin/xls/company_template.xlsx"
      );
    },
    // 文件状态改变时的回调函数
    changeFile(file) {
      console.log(file,"文件状态改变")
      this.uploadFile = file.raw; // 赋值当前上传的文件属性
    },
    // 删除已上传的文件的回调函数
    removeFile() {
      this.uploadFile = {}; // 清空数据
    }
  },
  computed: {},
};
</script>
<style lang="scss" scoped>
.company-name {
  display: flex;

  .right {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
<style scoped lang="scss">
// 点击切换时间展示数据
.browse-table {
  cursor: pointer;
  margin: 20px 0;

  .browse {
    width: 480px;

    .browse-item {
      margin: 0 5px;
      font-size: 14px;
      padding: 2px 10px;
      border-radius: 50px;
      color: #333;

      &.browse_active {
        color: #fff;
        background: #0068e6;
      }
    }
  }

  .block-time {
    justify-content: flex-start;
    margin: 10px;
  }

  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col {
    border-radius: 4px;
  }

  .bg-purple {
    border: 1px dashed #d3dce6;
  }

  .grid-content {
    padding: 10px;
    margin: 10px;
    justify-content: space-between;
    border-radius: 4px;
    min-height: 36px;

    .left {
      color: #999;
      font-size: 14px;
      text-align: start;

      p {
        color: #333;
      }

      .desc {
        color: #999;
      }
    }

    .right {
      color: #26bf8c;
    }
  }
}

.el-dropdown {
  color: #c0c4cc;
  width: 100px;
}

.el-button {
  border-radius: 10px;
  margin: 5px;
}

.el-input {
  border-left: none;
  border-radius: 0;
  width: 200px;
}

.row {
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #999;

  .add-build {
    margin-left: 10px;

    .el-button {
      border-radius: 4px;
    }
  }
}

.pagination-box {
  text-align: center;
  color: #333;
  line-height: 60px;
  margin-top: 30px;
}

.scope-box {
  height: 40px;

  .el-image {
    height: 40px;
    width: 100%;
  }
}

.title-ctn {
  margin-left: 10px;
  margin-bottom: 5px;
  padding: 10px;
  color: #fff;
  background: #edfbf8;

  p {
    color: #748a8f;
    margin: 4px 0;
    justify-content: flex-start;
  }

  i {
    margin-right: 5px;
    display: block;
    text-align: center;
    border-radius: 50%;
    line-height: 20px;
    width: 20px;
    height: 20px;
    background: #26bf8c;
    color: #fff;
  }
}

.labelrow {
  margin-bottom: 20px;
}
</style>
