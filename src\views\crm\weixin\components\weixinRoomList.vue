<template>
  <div v-fixed-scroll="48">
    <div class="filter-wrapper">
      <el-form inline size="medium">
        <el-form-item label="直播主题">
          <el-input placeholder="请输入" v-model="params.title"></el-input>
        </el-form-item>
        
        <el-form-item label="直播账号">
          <el-select v-model="params.account_id" clearable placeholder="选择直播账号" @change="search" filterable>
            <el-option v-for="item in accountList" :key="item.id" :label="item.nickname" :value="item.id">
              <span>{{item.nickname}}</span><small class="option-account-name">{{item.uniqId}}</small>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="params.start_date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            @change="search">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="params.end_date"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            @change="search">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="">
          <el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          <el-button type="primary" @click="exportExcel" :loading="exporting">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <myTable 
      :table-list="list" 
      :header="columns" 
      tooltipEffect="light" 
      v-loading="loading"
      :header-cell-style="{ background: '#EBF0F7' }" 
      highlight-current-row 
      :row-style="$TableRowStyle"
      :sort_change="handerListSort"
      ref="myTable">
    </myTable>
    
    <div class="tab-content-footer">
      <div></div>
      <el-pagination 
        background 
        layout="total,sizes,prev, pager, next, jumper" 
        :total="count"
        :page-sizes="[10, 20, 30, 50,100]" 
        :page-size="params.per_page" 
        :current-page="params.page"
        @current-change="onPageChange" 
        @size-change="handleSizeChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import myTable from "@/components/components/my_table";
import weixinHttp from "@/utils/weixinHttp";

export default {
  components: {
    myTable
  },
  data() {
    return {
      loading: false,
      exporting: false,
      accountList: [],
      params: {
        page: 1,
        per_page: 10,
        title: '',
        account_id: '',
        start_date: '',
        end_date: '',
        order: ''
      },
      curQueryParams: {},
      count: 0,
      list: [],
    }
  },
  computed: {
    columns() {
      return [
        { 
          prop: "account_name", 
          label: "直播账号", 
          minWidth: 180, 
          fixed: 'left'
        },
        { 
          prop: "title", 
          label: "直播主题", 
          minWidth: 220, 
          render: (h, {row}) => {
            return (
              <el-link type="primary" onClick={() => this.viewUsers(row)}>{row.title}</el-link>
            )
          } 
        },
        { 
          prop: "users_fill_num", 
          label: "已留资", 
          minWidth: 120, 
          sortable: 'custom' 
        },
        { 
          prop: "users_fill_no_num", 
          label: "未留资", 
          minWidth: 120, 
          sortable: 'custom' 
        },
        { 
          prop: "users_num", 
          label: "用户量", 
          minWidth: 120, 
          sortable: 'custom'
        },
        { 
          prop: "comments_num", 
          label: "评论条数", 
          minWidth: 120, 
          sortable: 'custom' 
        },
        { 
          prop: "start_time", 
          label: "开始时间", 
          minWidth: 180 
        },
        { 
          prop: "end_time", 
          label: "结束时间", 
          minWidth: 180 
        },
        { 
          prop: "living_duration", 
          label: "直播时长", 
          minWidth: 140, 
          fixed: 'right' 
        },
        { 
          prop: "start_date", 
          label: "直播日期", 
          minWidth: 120, 
          fixed: 'right' 
        }
      ];
    }
  },
  mounted() {
    let pagenum = localStorage.getItem('pagenum');
    this.params.per_page = Number(pagenum) || 10;
    this.getList();
    this.getAccountList();
  },
  methods: {
    //获取微信视频号账号列表
    async getAccountList(){
      try {
        const res = await weixinHttp.getWeixinChannelsAccountList({});
        if(res.status == 200){
          this.accountList = res.data?.data || [];
        }
      } catch (error) {
        console.error('获取账号列表失败:', error);
      }
    },
    async getList() {
      const params = {...this.params};
      this.curQueryParams = params;
      this.loading = true;
      try {
        const res = await weixinHttp.getWeixinLivingRoomList(params);
        if (res.status == 200) {
          this.count = res.data?.total || 0;
          this.list = res.data?.data || [];
        }
      } catch (error) {
        console.error('获取直播场次列表失败:', error);
      }
      this.loading = false;
    },
    search() {
      this.params.page = 1;
      this.getList();
    },
    onPageChange(page) {
      this.params.page = page;
      this.getList();
    },
    handleSizeChange(size) {
      this.params.per_page = size;
      this.search();
    },
    viewUsers(row) {
      this.$emit('room-change', row.room_id);
    },
    //处理排序
    handerListSort({ prop, order }) {
      //排序方式降序 1=已留资 2=未留资 3=用户量 4=评论条数
      if(order == 'ascending'){
        this.$refs.myTable.$refs.table.sort(prop, "descending");
        return;
      }
      
      if(order == 'descending'){
        switch(prop){
          case 'users_fill_num':
            this.params.order = '1';
            break;
          case 'users_fill_no_num':
            this.params.order = '2';
            break;
          case 'users_num':
            this.params.order = '3';
            break;
          case 'comments_num':
            this.params.order = '4';
            break;
        }
      }else{
        this.params.order = '';
      }
      this.search();
    },
    //导出
    async exportExcel(){
      const params = {...this.curQueryParams, opt: 'export'};
      this.exporting = true;
      try {
        const res = await weixinHttp.getWeixinLivingRoomList(params);
        if(res.status == 200){
          if(res.data.status == 1){
            if(res.data.url){
              window.location.href = res.data.url;
            }else{
              this.$message.warning(res.data?.msg || '没有可导出的数据');
            }
          }else{
            this.$message.error(res.data?.msg || '导出失败');
          }
        }
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
      this.exporting = false;
    }
  }
}
</script>

<style scoped lang="scss">
.filter-wrapper {
  padding: 16px 16px 12px;
}
::v-deep{
  .option-account-name{
    color: #a9a9a9;
    margin-left: 12px;
    float: right;
  }
}
</style> 