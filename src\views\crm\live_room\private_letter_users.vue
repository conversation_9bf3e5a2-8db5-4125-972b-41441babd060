<template>
	<div class="tab-content-container">
		<div class="tab-content-body white topped">
			<div class="body-inner main-scroll">
				<div class="tabs-container">
					<el-tabs class="header-tabs" v-model="componentId">
						<el-tab-pane label="私信" name="privateLetterUsers"></el-tab-pane>
						<el-tab-pane label="帐号" name="liveAccountList"></el-tab-pane>
					</el-tabs>

					<!-- 平台切换下拉菜单 -->
					<div class="platform-dropdown">
						<el-popover v-model="isPlatformDropdownOpen" placement="bottom-start" width="150" trigger="click" :offset="0">
							<div class="live-customer-menu">
								<div class="live-customer-menu-item weixin-item" @click="goWeixinPrivateLetterUsersPage">
									<img src="https://img.tfcs.cn/backup/icons/shiphimport.png" alt="视频号" class="menu-icon">
									<span class="menu-text">视频号</span>
								</div>
							</div>
							<div slot="reference" class="platform-dropdown-trigger">
								<img src="https://img.tfcs.cn/backup/icons/douyinimport.png" alt="抖音" class="platform-icon">
								<span class="platform-text">抖音</span>
								<i class="el-icon-arrow-down" :class="{ 'rotate-icon': isPlatformDropdownOpen }"></i>
							</div>
						</el-popover>
					</div>
				</div>


				<privateLetterUsers/>
			</div>
		</div>
	</div>
</template>
<script>
import privateLetterUsers from "./components/privateLetterUsers";
export default {
	name: 'crm_live_room_users',
	components: {
		privateLetterUsers
	},
	data(){
		return {
			componentId: 'privateLetterUsers',
			isPlatformDropdownOpen: false, // 平台下拉菜单是否展开
		}
	},
	watch: {
		componentId(newVal, oldVal){
			if(newVal != 'privateLetterUsers'){
				this.$goPath('/crm_live_room_users');
				this.$nextTick(()=>{
					this.componentId = oldVal;
				})

			}
		}
	},
	methods: {
		// 跳转到微信视频号私信页面
		goWeixinPrivateLetterUsersPage() {
			this.isPlatformDropdownOpen = false; // 关闭下拉菜单
			this.$goPath('/crm_weixin_users');
		}
	}
}
</script>
<style  scoped lang="scss">
.tab-content-body{
	padding: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	padding-bottom: 48px;
}
.body-inner{
	flex: 1;
	overflow: auto;
	padding: 0 28px 28px;
}

	
.tabs-container {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	padding: 16px 0 0;
	position: relative;
}

.header-tabs{
	flex: 1;
	padding: 0;

	/* 确保tab下划线在平台菜单下方 */
	::v-deep .el-tabs__nav-wrap::after {
		z-index: 1;
	}

	::v-deep .el-tabs__active-bar {
		z-index: 1;
	}
}

/* 平台下拉菜单样式 */
.platform-dropdown {
	position: relative;
	z-index: 2;
	margin-bottom: 14px; /* 与tab下划线对齐 */
}

.platform-dropdown-trigger {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	background: #fff;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 120px;
	border-bottom: 2px solid #e4e7ed; /* 默认显示灰色下划线 */

	&:hover {
		background-color: #f5f7fa;
		border-bottom-color: #409eff; /* 悬停时显示蓝色下划线 */
	}

	.platform-icon {
		width: 18px;
		height: 18px;
		margin-right: 8px;
		object-fit: contain;
	}

	.platform-text {
		font-size: 14px;
		color: #333;
		font-weight: 500;
		flex: 1;
	}

	.el-icon-arrow-down {
		margin-left: 2px; /* 缩小与文字的距离 */
		font-size: 12px;
		transition: transform 0.3s ease;
	}

	.rotate-icon {
		transform: rotate(180deg);
	}
}

/* 下拉菜单样式 */
.live-customer-menu {
	display: flex;
	flex-direction: column;
	margin: -12px;
	padding: 4px 0px;
	border-radius: 6px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	overflow: hidden;

	.live-customer-menu-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 12px 14px;
		box-sizing: border-box;
		cursor: pointer;
		transition: all 0.3s ease;
		position: relative;

		.menu-icon {
			width: 18px;
			height: 18px;
			margin-right: 10px;
			object-fit: contain;
			transition: transform 0.2s ease;
		}

		.menu-text {
			font-size: 14px;
			color: #333;
			line-height: 1.4;
			font-weight: 500;
			transition: color 0.3s ease;
		}

		&:hover {
			background: linear-gradient(135deg, #e6f0ff 0%, #d4e7ff 100%);
			box-shadow: inset 0 1px 3px rgba(24, 144, 255, 0.1);

			.menu-icon {
				transform: scale(1.1);
			}
		}
	}
}
::v-deep{
	.tab-content-footer{
		position: absolute;
		z-index: 99;
		bottom: 0;
		left: 0;
		right: 0;
		background: #fff;
		padding-left: 28px;
		padding-right: 28px;
		white-space: nowrap;
    	overflow: hidden;
	}
}
</style>