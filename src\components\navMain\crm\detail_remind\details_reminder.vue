<template>
    <div>
        <el-drawer
            title="提醒跟进"
            :visible.sync="show"
            :direction="direction"
            :show-close="false">
            <div class="addtixing">
              <el-button type="primary" @click="addreminder"><i class="el-icon-plus"></i>添加提醒</el-button>
            </div>
            <div class="page">
                <!-- 日历 -->
                    <div class="calendar">
                      <div class="calendar-header"> 
                        <span class="headriqi"> {{ currentYear }}年{{ monthNames[currentMonth] }}</span>
                        <div class="flex-row"> 
                          <el-link type="primary" :underline="false" @click="goToToday">今天</el-link>
                          <div @click="prevMonth" class="pre_and_next"><i class="el-icon-arrow-left"></i></div>

                          <div @click="nextMonth" class="pre_and_next"><i class="el-icon-arrow-right"></i></div>
                        </div>
                      </div>
                      <div class="calendar-grid">
                        <div class="day-name" v-for="(day, index) in dayNames" :key="index">{{ day }}</div>
                        <div
                          v-for="(date, index) in daysInMonth"
                          :key="`${currentMonth}-${date}-${index}`"
                          class="day"
                          :class="{ 
                            'current-day': isCurrentDay(date.formattedDate),
                            'highlighted': isHighlighted(date.formattedDate),
                            'selected-day': isSelectedDay(date.formattedDate),
                            'gray': !date.isCurrentMonth}"
                          @click="selectDate(date.formattedDate)"
                        >
                          {{ date.date }}
                     
                            <!-- 如果是当前日期，显示蓝点 -->
                            <div v-if="isCurrentDay(date.formattedDate)" class="blue-dot"></div>
                            <!-- 如果日期需要显示红点，显示红点 -->
                          <div v-if="isHighlighted(date.formattedDate)" class="red-dot"></div>
                        </div>
                      </div>
                    </div>
                    <div style="margin-top:25px;">历史提醒</div>
                <div class="operate">
                    <div class="switch">
                        <div class="titlepage">
                          <div class="classtitle">
                            <div class="titleitem" v-for="item in titledata" :key="item.id" :class="{isitem:titleid==item.id}"
                              @click="handleClick(item.id)">
                              {{item.name}}
                            </div>
                          </div>
                        </div>
                    </div>
                    <div>
                        <el-input
                            placeholder="请输入关键词"
                            style="width: 170px;"
                            v-model="params.keywords"
                            size="small">
                            <el-button slot="append" icon="el-icon-search" @click="searchreminder"></el-button>
                        </el-input>
                    </div>
                </div>
                <div class="reminderList">
                    <div class="block">
                        <el-timeline :reverse="false">
                          <el-timeline-item
                            v-for="(activity, index) in activities"
                            :key="index">
                                <div>
                                    <div class="remind_time">
                                        {{ activity.remind_time}}
                                    </div>
                                    <div class="">
                                       {{activity.admin.user_name}}  &nbsp;添加提醒&nbsp; {{activity.content}}
                                    </div>
                                </div>
                         
                          </el-timeline-item>
                        </el-timeline>
                    </div>
                </div>
            </div>

          
        </el-drawer>
    </div>
</template>
    
<script>
    export default {
        name: 'detailsreminder',
        components: {
        },
        data(){
            return {
                show: false,        //dialog是否显示
                direction: 'rtl',
                cancleFn: null,     //取消回调
                successFn: null,    //成功回调
                params: {
                    client_id:"",//客户id
                    // date:"",//日期
                    // type:"",//类型
                    keywords:"",//关键词检索内容
                },         //表单参数
                keywords:"",//检索词
                reverse: true,
                activities: [],//历史提醒
                titleid:"0",
                titledata:[
                    {id:0,name:"全部"},
                ],//历史提醒头部标题

                currentDate: new Date(),
                currentYear: new Date().getFullYear(),
                currentMonth: new Date().getMonth(),
                dayNames: ['日', '一', '二', '三', '四', '五', '六'],
                monthNames: [
                  '01月', '02月', '03月', '04月', '05月', '06月',
                  '07月', '08月', '09月', '10月', '11月', '12月'
                ],
                highlightedDates: [],// 需要显示红点的日期数组，日期格式为 "YYYY-MM-DD"
                listparams:{
                  keywords:"",//关键词检索内容
                },//查询某天的历史提醒
            }
        },
        computed: {
            daysInMonth() {
                // 获取当前月的第一天和最后一天
                  const firstDay = new Date(this.currentYear, this.currentMonth, 1);
                  const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
                  const days = [];

                  // 获取该月的所有日期
                  for (let i = 1; i <= lastDay.getDate(); i++) {
                      const date = new Date(this.currentYear, this.currentMonth, i);
                      const formattedDate = `${String(this.currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`; // 格式化日期为 MM-DD
                      days.push({ date: i, isCurrentMonth: true, formattedDate });
                  }

                  // 计算该月的第一天是星期几
                  const firstDayOfWeek = firstDay.getDay(); // 0: Sunday, 1: Monday, ..., 6: Saturday

                  // 获取上个月的最后一天日期
                  const lastDayOfPreviousMonth = new Date(this.currentYear, this.currentMonth, 0);
                  const lastDayPreviousMonth = lastDayOfPreviousMonth.getDate();

                  // 计算空白填充，填充到该月第一天之前的空格
                  const leadingSpaces = [];

                  // 计算需要从上个月填充的天数
                  for (let i = lastDayPreviousMonth - firstDayOfWeek + 1; i <= lastDayPreviousMonth; i++) {
                      const date = new Date(this.currentYear, this.currentMonth - 1, i);
                      const formattedDate = `${String(this.currentMonth).padStart(2, '0')}-${String(i).padStart(2, '0')}`; // 格式化日期为 MM-DD
                      leadingSpaces.push({ date: i, isCurrentMonth: false, formattedDate });
                  }

                  // 获取下个月的第一天日期
                  const firstDayOfNextMonth = new Date(this.currentYear, this.currentMonth + 1, 1);
                  const lastDayOfWeek = new Date(this.currentYear, this.currentMonth + 1, 0).getDay(); // 当前月最后一天是星期几

                  // 计算下个月需要填充的天数
                  const trailingSpaces = [];
                  let daysToFillNextMonth = 7 - (leadingSpaces.length + days.length) % 7;
                  if (daysToFillNextMonth !== 7) { // 如果不满一周，填充下个月的日期
                      for (let i = 1; i <= daysToFillNextMonth; i++) {
                          const date = new Date(this.currentYear, this.currentMonth + 1, i);
                          const formattedDate = `${String(this.currentMonth + 2).padStart(2, '0')}-${String(i).padStart(2, '0')}`; // 格式化日期为 MM-DD
                          trailingSpaces.push({ date: i, isCurrentMonth: false, formattedDate });
                      }
                  }

                  // 最终日期数组，先填充空白，再填充实际的日期，最后填充下个月的日期
                  const totalDays = [...leadingSpaces, ...days, ...trailingSpaces];
                  // 返回最终的日期数组
                  return totalDays;
            }
        },
        methods: {
            //打开弹层
            open(c_id){
                this.c_id = c_id,//赋值客户id
                this.getreminderlist()
                this.show = true;
                return this;
            },
            // 判断是否是特定的日期 (例如：2025年3月20日)
            isSpecialDate(date) {
              const targetDate = new Date(2025, 2, 20); // 注意：月份是从0开始的，2表示3月
              return date.getDate() === targetDate.getDate() &&
                     date.getMonth() === targetDate.getMonth() &&
                     date.getFullYear() === targetDate.getFullYear();
            },
            //搜索提醒关键词
            searchreminder(){
                  if ( this.formattedDate === this.currentFormattedDate) {
                    // console.log("全部");
                      // 如果是当前日期，调用 this.getreminderlist()
                      this.getreminderlist();
                  } else {
                    // console.log("日期查询");
                      // 否则调用 this.queryreminderdata()
                      this.queryreminderdata();
                  }
            },
            //提醒头部切换
            handleClick(id){
                this.titleid = id
                this.queryreminderdata()
            },
            //获取提醒列表
            getreminderlist(){
                let push_formcopy = JSON.parse(JSON.stringify(this.params)); // 拷贝
                push_formcopy.client_id = this.c_id
                this.$http.historicalreminder(push_formcopy).then(res=>{
                    if(res.status == 200){
                       this.activities = res.data
                       this.highlightedDates = this.activities.map(activity => {
                            // 提取 remind_time 字符串的前部分（即日期，不包括时间）
                            const remindDate = activity.remind_time.split(' ')[0];
                            return remindDate;
                        });
                    }
                }) 
            },
            //添加提醒
            addreminder(){
                this.$emit("addreminderdetail")
            },
            // //取消
            // cancle(){
            //     this.show = false;
            //     this.cancleFn && this.cancleFn();
            // },
              // 跳转到当前日期
              goToToday() {
                const today = new Date(); // 获取当前日期
                this.currentYear = today.getFullYear(); // 设置当前年份
                this.currentMonth = today.getMonth(); // 设置当前月份
                // 更新日期选择为今天
                this.selectedDate = today.getDate(); // 设置选中的日期为今天
                this.formattedDate = `${this.currentYear}-${(this.currentMonth + 1).toString().padStart(2, '0')}-${this.selectedDate.toString().padStart(2, '0')}`;
                // 调用获取提醒数据等需要的操作
                this.getreminderlist();
                this.titledata = this.titledata.filter(item => item.id !== 1);
              },
            //上一个月
            prevMonth() {
              if (this.currentMonth === 0) {
                this.currentMonth = 11;
                this.currentYear--;
              } else {
                this.currentMonth--;
              }
            },
            //下一个月
            nextMonth() {
              if (this.currentMonth === 11) {
                this.currentMonth = 0;
                this.currentYear++;
              } else {
                this.currentMonth++;
              }
            },
            // 判断日期是否为当前日期
            isCurrentDay(date) {
              const currentDate = new Date(); // 获取当前日期
              const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // 当前月份，格式化为 MM
              const currentDay = currentDate.getDate().toString().padStart(2, '0'); // 当前日期，格式化为 DD
              // 格式化当前日期为 "MM-DD" 形式
              const currentDateStr = `${currentMonth}-${currentDay}`;
              // 判断传入的日期是否等于当前日期
              return date === currentDateStr;
            },
            // 判断日期是否为选中的日期
            isSelectedDay(date) {
                return this.selectedDate === date && !this.isCurrentDay(date); // 选中的日期，并且不是当前日期
            },
            isHighlighted(date) {
              const filteredHighlightedDates = this.highlightedDates.filter(date => {
                const today = new Date();
                const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;
                return date !== todayStr;
              });
              // 更新方法中的 highlightedDates 使用过滤后的数组
                if (date === null || date === undefined) return false;
                // 将当前日期格式拼接上年份为 "YYYY-MM-DD" 格式
                const currentDateStr = `${this.currentYear}-${date.toString()}`;
                // 判断是否包含在过滤后的 highlightedDates 数组中
                return filteredHighlightedDates.includes(currentDateStr);

            },
            selectDate(date) {
                // 拼接当前日期的完整格式
                this.formattedDate = `${this.currentYear}-${date}`;
                // 获取当前日期对象
                const selectedDate = new Date(this.formattedDate);
                const currentDate = new Date();
                // 计算点击日期所属的月份
                const selectedMonth = selectedDate.getMonth();
                const selectedYear = selectedDate.getFullYear();
                // 记录当前点击月份
                let isMonthChanged = false;
                // 如果点击的是下一个月
                if (selectedMonth > currentDate.getMonth() || (selectedMonth === 0 && currentDate.getMonth() === 11)) {
                    // 只有当点击的月份和当前月份不同才进行跳转
                    if (this.currentMonth !== selectedMonth || this.currentYear !== selectedYear) {
                        this.currentMonth = selectedMonth;
                        this.currentYear = selectedYear;
                        isMonthChanged = true; // 标记月份已发生变化
                    }
                }
                // 如果点击的是上一个月
                else if (selectedMonth < currentDate.getMonth() || (selectedMonth === 11 && currentDate.getMonth() === 0)) {
                    // 只有当点击的月份和当前月份不同才进行跳转
                    if (this.currentMonth !== selectedMonth || this.currentYear !== selectedYear) {
                        this.currentMonth = selectedMonth;
                        this.currentYear = selectedYear;
                        isMonthChanged = true; // 标记月份已发生变化
                    }
                }
                // 如果点击的是当前月份
                else if (selectedMonth === currentDate.getMonth() && selectedYear === currentDate.getFullYear()) {
                    // 当前月点击日期，直接跳转
                    if (this.currentMonth !== selectedMonth || this.currentYear !== selectedYear) {
                        this.currentMonth = selectedMonth;
                        this.currentYear = selectedYear;
                        isMonthChanged = true;
                    }
                }
                // 更新所选日期
                this.selectedDate = date;
                // 计算是否为当前日期
                const currentFormattedDate = `${currentDate.getFullYear()}-${(currentDate.getMonth() + 1)
                    .toString().padStart(2, '0')}-${currentDate.getDate().toString().padStart(2, '0')}`;
                if (this.formattedDate === currentFormattedDate) {
                    // 如果是当前日期，调用 this.getreminderlist()
                    this.getreminderlist();
                    this.titledata = this.titledata.filter(item => item.id !== 1);
                } else {
                    this.listparams.date = this.formattedDate;
                    // 如果不是当前日期，确保 id 为 1 的数据在 titledata 中
                    if (!this.titledata.some(item => item.id === 1)) {
                        this.titledata.push({ id: 1, name: "我的" });
                    }
                    // 否则调用 this.queryreminderdata()
                    this.queryreminderdata();
                }
            },
            //查询单个日期的提醒记录
            queryreminderdata(){
              this.listparams.client_id = this.c_id
              this.listparams.type = this.titleid
              this.listparams.keywords = this.params.keywords
              this.$http.historicalreminderlist(this.listparams).then(res=>{
                if(res.status==200){
                  this.activities = res.data.data
                }
              })
            },
        }
    }
</script>
<style lang="scss" scoped>
 ::v-deep .el-drawer {
    width: 500px !important;
 }
 .addtixing{
    position: absolute;
    right: 10px;
    top: 13px;
  }
   .page{
    width: 95%;
    margin: 0 auto;
    height: calc(100vh - 90px);
    overflow-y: auto;
    overflow-x: hidden;
    .calendar{
        width: 100%;
        // height: 300px;
        ::v-deep.el-calendar-table .el-calendar-day {
            padding: 3px;
            height: 45px;
        }
        ::v-deep.el-calendar__body{
            padding: 12px 0px 25px;
        }
        .calendar {
          width: 93%;
          margin: 0 auto;
          padding: 20px;
        //   border: 1px solid #ccc;
          border-radius: 8px;
          margin-bottom: 20px;
        //   background-color: #f9f9f9;
        }

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  margin-bottom: 20px;
  .headriqi{
    font-size: 16px;
    color: #8a929f;
  }
  .pre_and_next{
    width: 24px;
    text-align: center;
    height: 24px;
    line-height: 24px;
    border-radius: 50%;
    margin-left: 12px;
    background-color: #f1f4fa;
    color: #7b899c;
    cursor: pointer;
  }
}

button {
  font-size: 16px;
  background: none;
  border: none;
  cursor: pointer;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}

.day-name {
  text-align: center;
  font-weight: bold;
}

.day {
  width: 30px;
  margin-left: 5px;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 50%;
}

.day:hover {
  background-color: #e0e0e0;
}

.current-day {
  background-color: rgb(236, 248, 255);
}
.blue-dot {
  transform: translateX(-50%);
  width: 5px;
  height: 5px;
  background-color: #409EFF;
  border-radius: 50%;
  margin-left: 15px;
}
.red-dot {
//   position: absolute;
//   bottom: 5px;
//   left: 50%;
  transform: translateX(-50%);
  width: 5px;
  height: 5px;
  background-color: red;
  border-radius: 50%;
  margin-left: 15px;
}

.highlighted {
  font-weight: bold;
}
.selected-day {
  background-color: #e0e0e0; /* 设置背景为灰色 */
}
.gray {
    color: lightgray !important;  /* 使用 !important 确保样式优先级 */
}
    }
    .operate{
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .switch{
            width: 25%;
            // ::v-deep.el-tabs__nav-wrap::after {
            //     width: 69%;
            // }
            .titlepage {
              width: 100%;
              height: 35px;
              // background-color: aqua;
              overflow: hidden;
            //   border-bottom: 1px solid #F2F3F5;
                        
              .classtitle {   
                min-width: 150px;
                display: flex;
                flex-direction: row;
                // justify-content: center;
                // margin-top: 33px;
            
                .titleitem {
                  margin-right: 40px;
                  color: #4E5969;
                  position: relative;
                
                  &.isitem {
                    color: #165DFF;
                
                    &::after {
                      position: absolute;
                      left: 38%;
                      transform: translateX(-38%);
                      content: "";
                      height: 2px;
                      background: #2d84fb;
                      width: 40px;
                      display: block;
                      margin-top: 8px;
                    }
                  }
                }
            
              }
            }
        }
    }
    .reminderList{
        margin-top: 20px;
        .remind_time{
            font-size: 14px;
            color: #8a929f;
            margin-bottom: 10px;
        }
    }

   } 

</style>