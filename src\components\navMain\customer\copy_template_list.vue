<template>
  <el-container>
    <div>
      <el-button
        v-if="$hasShow('创建模板')"
        @click="copyTemplate"
        type="primary"
        style="margin-bottom:15px"
        >创建复制模板</el-button
      >
    </div>
    <myTable
      v-loading="is_table_loading"
      :table-list="tableData"
      :header="table_header"
    >
    </myTable>
    <el-footer>
      <!-- 分页 -->
      <div class="pagination-box">
        <myPagination
          :total="params.total"
          :pagesize="params.per_page"
          :currentPage="params.page"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </div>
    </el-footer>
    <el-dialog :title="titleMap[dialogTitle]" :visible.sync="dialogCreate">
      <el-form label-width="100px">
        <el-form-item label="字段说明：">
          <div class="copy-box">
            <el-tag v-for="(item, index) in copy_arr" :key="index">{{
              item
            }}</el-tag>
          </div>
        </el-form-item>
      </el-form>
      <myForm :form_create="form_create_obj" @onClick="onCreate"> </myForm>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
import myForm from "@/components/components/my_form";
export default {
  name: "copy_template_list",
  components: {
    myPagination,
    myTable,
    myForm,
  },
  data() {
    return {
      tableData: [],
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        row: 0,
      },
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      copy_arr: [
        "${build_name}：楼盘名称",
        "${customer_name}：客户姓名",
        "${customer_sex}：客户性别",
        "${customer_phone}：客户电话",
        "${created_at}：添加时间",
        "${u_name}：经纪人",
        "${u_company_name}: 经纪人公司",
        "${u_phone}: 经纪人联系方式",
        "${visit_time}：预计到访时间",
        "${visit_people}：到访人数",
        "${visit_category}：到访方式",
        "${remark}：备注信息",
        "${copy_time}：复制时间",
        "${visit_time_str_1}：到访时间显示（今天、明天、近期）",
        "${go_with}：客户是否需要陪同",
        "${customer_id_no}客户身份证号后六位",
        "${intention_build_category}客户意向楼盘",
      ],
      placeholder_content:
        "参考格式：标题+内容；在${ }中填写； \n【楼盘名称】${build_name}\n【客户姓名】${customer_name}\n【客户性别】${customer_sex}\n【客户电话】${customer_phone}\n【添加时间】${created_at}\n【报备经纪人】${u_name}\n【经纪人公司】${u_company_name}\n【联系方式】${u_phone}",
      table_header: [
        {
          expand: "expand",
          render: (h, data) => {
            return (
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="备注：">
                  <span domPropsInnerHTML={data.row.remark}></span>
                </el-form-item>
                <el-form-item label="内容：">
                  <span domPropsInnerHTML={data.row.content}></span>
                </el-form-item>
              </el-form>
            );
          },
        },
        { prop: "id", label: "ID" },
        { prop: "name", label: "模板名称" },
        {
          prop: "enable",
          label: "是否开启",
          formatter: (row) => {
            let enable = row.enable;
            if (enable === 1) {
              return "开启";
            } else {
              return "关闭";
            }
          },
        },
        {
          label: "操作",
          fixed: "right",
          width: "400",
          render: (h, data) => {
            return (
              <div>
                {this.$hasShow("修改模板") ? (
                  <el-button
                    onClick={() => {
                      this.changeData(data.row);
                    }}
                    type="success"
                    size="mini"
                  >
                    修改
                  </el-button>
                ) : (
                  ""
                )}

                {this.$hasShow("删除模板") ? (
                  <el-button
                    icon="el-icon-delete"
                    onClick={() => {
                      this.deleteData(data.row);
                    }}
                    type="danger"
                    size="mini"
                  >
                    删除
                  </el-button>
                ) : (
                  ""
                )}

                {this.$hasShow("关闭模板") ? (
                  <el-button
                    type={data.row.enable === 1 ? "success" : "primary"}
                    size="mini"
                    onClick={() => {
                      this.updateEnable(data.row);
                    }}
                    domPropsInnerHTML={data.row.enable === 1 ? "关闭" : "开启"}
                  ></el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      form_create_obj: {
        model: {},
        label_width: "100px",
        formLabel: [
          {
            label: "模板名称：",
            model: "name",
            placeholder: "请输入模板名称",
            type: "input",
          },
          {
            label: "是否开启：",
            model: "enable",
            type: "radio",
            opts: [
              { value: 0, description: "关闭" },
              { value: 1, description: "开启" },
            ],
          },
          {
            label: "备注信息：",
            model: "remark",
            placeholder: "备注信息",
            type: "input",
            inputType: "textarea",
            inputRows: "5",
          },
          {
            label: "复制内容：",
            model: "content",
            placeholder:
              "参考格式：标题+内容；在${ }中填写； \n【楼盘名称】${build_name}\n【客户姓名】${customer_name}\n【客户性别】${customer_sex}\n【客户电话】${customer_phone}\n【添加时间】${created_at}\n【报备经纪人】${u_name}\n【经纪人公司】${u_company_name}\n【联系方式】${u_phone}",
            type: "input",
            inputType: "textarea",
            inputRows: "8",
          },
        ],
      },
      is_table_loading: true,
    };
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.page - 1) * this.params.per_page;
      let end = start + this.params.per_page;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.per_page = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      this.getPageData();
      this.getDataList();
    },
    getDataList() {
      this.$http.getCopyTemplateList({ params: this.params }).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.page = res.data.current_page;
          this.params.total = res.data.total;
        }
      });
    },
    copyTemplate() {
      this.form_create_obj.model = {
        enable: "1",
      };
      this.dialogTitle = "addData";
      this.dialogCreate = true;
    },
    changeData(row) {
      this.dialogTitle = "updateData";
      this.dialogCreate = true;
      this.form_create_obj.model = row;
    },
    // 删除
    deleteData(row) {
      this.$confirm("是否删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCopyTemplate(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {});
    },
    onCreate() {
      if (this.dialogTitle === "addData") {
        this.$http
          .createCopyTemplate(this.form_create_obj.model)
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "创建成功",
                type: "success",
              });
              this.getDataList();
              this.dialogCreate = false;
            }
          });
      } else {
        this.$http
          .updateCopyTemplate(this.form_create_obj.model)
          .then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "修改成功",
                type: "success",
              });
              this.getDataList();
              this.dialogCreate = false;
            }
          });
      }
    },
    updateEnable(row) {
      var status = row.enable === 1 ? 0 : 1;
      this.$http.updateCopyTemplateEnable(row.id, status).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "操作成功",
            type: "success",
          });
          this.getDataList();
        }
      });
    },
  },
};
</script>

<style lang="scss">
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.copy-box {
  margin: 10px auto;
  .el-tag {
    margin: 5px;
  }
}
</style>
