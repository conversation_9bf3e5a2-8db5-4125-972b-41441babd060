<template>
  <div class="div row">
    <div class="title">
      {{ title }}
    </div>
    <div class="title_number">
      <div>
        当前页面共(
        <i>{{ number }}</i>
        )条数据
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      default: "列表",
      type: String,
    },
    number: {
      default: 0,
      type: [String, Number],
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  color: #333;
  font-weight: bold;
}
.title_number {
  margin-left: 10px;
}
i {
  text-align: center;
}
</style>
