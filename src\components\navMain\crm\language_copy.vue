<template>
<div>
<div class="tab-content-container" v-fixed-scroll="48" v-show="!showScriptList">
  <div class="tab-content-body main-scroll pages">
    <div class="header-title div row">
      <div class="ht-title">话术库</div>
      <div class="tabs">
        <div class="tabs-item" v-for="tab in scopeTabs" :key="tab.value" :class="{active: tab.value == params.scope}" @click="onScopeTableClick(tab)">{{tab.label}}</div>
      </div>
      <div style="margin-right: 24px">
        <el-button size="mini" type="primary" icon="el-icon-sort" @click="dialogs.setSortForm = true">排序</el-button>
        <el-button
          style="margin-left: 10px"
          type="primary"
          size="mini"
          class="el-icon-plus"
          @click="onCreateTalk"
        >                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
          新增
        </el-button>
      </div>
    </div>
    <div class="cardku" >
      <div class="ScriptPack" :class="{'detail-prevent': !detailsAbled(item)}" v-for="(item,index) in tableData" :key="index">
        <div class="Packimg" @click="librarydetails(item)">
          <img :src="(item.cover_pic || 'https://img.tfcs.cn/images/new_icon/ScriptPack.png')" alt="">
          <div class="tag-status">
            
            <el-button plain :type="item.status == 1?'success':'danger'" :loading="submitings[index].setStatusing"
              size="mini" @click.stop="setSpeechLibraryStatus(item, index)" v-if="statusAbled(item)">
              {{ item.status == 1? '启用' : '禁用' }}
            </el-button>
            
          </div>
        </div>
        <div class="Packtext">
          <div class="title" @click="librarydetails(item)" :title="item.package_name">
            {{item.package_name}}
          </div>
         <div class="Packrange">

          <div class="marks">
            <el-tag size="medium" type="success" v-if="item.is_platform==1">平台</el-tag>
            <el-tag size="medium" type="danger" v-if="item.is_system==1">内置</el-tag>
            <el-tag size="medium" :type="item.scope==1?'':'warning'">{{item.scope==1?"团队":"自建"}}</el-tag>
          </div>

          <!-- v-if="item.scope==2" -->
          <div>
            <el-button type="info" size="mini" @click="scriptdetails(item)">预览</el-button>
            <el-dropdown class="speech-item-op" placement="top" trigger="click" @command="(e)=>handleCommand(e,item,index)" style="margin-left: 12px;">
              <span class="el-dropdown-link">
                <el-button type="info" size="mini">操作</el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="copyAbled(item)"  command="copy">云导入</el-dropdown-item>
                <el-dropdown-item v-if="importAbled(item)" command="import">导入</el-dropdown-item>
                <el-dropdown-item v-if="editAbled(item)" command="edit">编辑</el-dropdown-item>
                <el-dropdown-item v-if="deleteAbled(item)" command="delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
         </div>
        </div>
      </div> 
      <div class="bgcolor">
        <div @click="onCreateTalk" class="bgcolortext"><i class="el-icon-plus"></i> 新增</div>
      </div>  
    </div>

      <el-drawer
        title="新增话术库"
        :visible.sync="Moduleclassification"
        :direction="direction"
        :before-close="handleClose">
        <div class="QuickEditA">
          <div class="text" style="margin: 0px 0px 10px 0px;">话术库标题</div>
          <el-input v-model="Scriptsdata.package_name" placeholder="请输入内容"></el-input>
          <!-- <div class="text">话术标题</div>
          <el-input v-model="Scriptsdata.title" placeholder="请输入内容"></el-input>
          <div class="text">话术内容</div>
          <el-input :autosize="{ minRows: 4, maxRows: 6}" 
          maxlength="100" type="textarea" v-model="Scriptsdata.content"
          show-word-limit></el-input> -->
          <!-- <div class="text">封面图</div>
          <div>
            <img src="https://img.tfcs.cn/images/new_icon/ScriptPack.png" alt="">
          </div> -->
          <div class="text">发布方式</div>
          <el-select style="width: 100%;" v-model="Scriptsdata.scope" placeholder="请选择">
            <el-option
              v-for="item in Publishway"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <div class="text">使用模块</div>
          <el-select style="width: 100%;" v-model="Scriptsdata.module" placeholder="请选择" multiple>
            <el-option
              v-for="item in type_list"
              :key="item.id"
              :label="item.lable"
              :value="item.id">
            </el-option>
          </el-select>

          <template v-if="isPlatformWebsite">
            <div class="text">是否平台</div>
            <el-radio-group v-model="Scriptsdata.is_platform">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>

            <div class="text">封面图</div>
            <tUpload v-model="Scriptsdata.cover_pic" accept=".jpeg,.jpg,.png"></tUpload>
          </template>
        </div>

          <div class="demo-drawer__footer">
              <el-button size="small" @click="handleClose">取 消</el-button>
              <el-button size="small" type="primary"  @click="SubmitScript"
              v-loading="is_button_loading"
              :disabled="is_button_loading">确定</el-button>
          </div>
      </el-drawer>
      <el-dialog
        title="编辑"
        :visible.sync="EditingVisible"
        width="30%"
        :before-close="handleClose">
        <div class="text" style="margin: 0px 0px 10px 0px;">话术库标题</div>
          <el-input v-model="rowdata.package_name" placeholder="请输入内容"></el-input>
          <div class="text">发布方式</div>
          <el-select style="width: 100%;" v-model="rowdata.scope" placeholder="请选择">
            <el-option
              v-for="item in Publishway"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <div class="text">使用模块</div>
          <el-select style="width: 100%;" v-model="rowdata.module" placeholder="请选择" multiple>
            <el-option
              v-for="item in type_list"
              :key="item.id"
              :label="item.lable"
              :value="item.id">
            </el-option>
          </el-select>

          <template v-if="isPlatformWebsite">
            <div class="text">是否平台</div>
            <el-radio-group v-model="rowdata.is_platform">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>

            <div class="text">封面图</div>
            <tUpload v-model="rowdata.cover_pic" accept=".jpeg,.jpg,.png"></tUpload>
          </template>

        <span slot="footer" class="dialog-footer">
          <el-button @click="EditingVisible = false">取 消</el-button>
          <el-button type="primary" @click="editlibraryname">确 定</el-button>
        </span>
      </el-dialog>
      <scriptdetails ref="scriptdetails" :showTopCate="false" :showCopy="true" is-preview></scriptdetails>
      <setSortForm @change="getDataList" :visible.sync="dialogs.setSortForm"/>
      <importSpeechLibrary ref="importSpeechLibrary" v-if="dialogs.importSpeechLibrary"/>
  </div>
  <div class="tab-content-footer">
    <div>
      <el-button type="primary" @click="getDataList" size="small" :loading="is_table_loading">刷新</el-button>
    </div>
    <el-pagination background layout="total,sizes,prev, pager, next, jumper" :total="params.total"
    :page-sizes="[10, 20, 30, 50,100]" :page-size="params.per_page" :current-page="params.page"
    @current-change="onPageChange" @size-change="handleSizeChange">
    </el-pagination>
  </div>
</div>
<ScriptList v-if="showScriptList" @Listchange="Listchange" :Scriptiddata="Scriptiddata"></ScriptList>
</div>
</template>

<script>
import ScriptList from '@/views/crm/share_follower/ScriptList.vue'
import scriptdetails from '@/views/crm/share_follower/detailedinformation.vue'
import setSortForm from '@/views/speech_library/components/setSortForm.vue'
import importSpeechLibrary from '@/views/speech_library/components/importSpeechLibrary.vue'
import tUpload from '@/components/tplus/tUpload'
export default {
  name: "language",
  components:{
    ScriptList,
    scriptdetails,
    setSortForm,
    importSpeechLibrary,
    tUpload
  },
  data() {
    return {
      tableData: [],
      is_table_loading: false,
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        scope: "",
        module: "",
        keywords:"",
        scope: '',
      },
      scopeTabs: [
        {value: '', label: '全部'},
        {value: 1, label: '团队'},
        {value: 2, label: '自建'},
        {value: 3, label: '平台'},
      ],
      direction:"rtl",
      input:"",
      Publishway:[
        { value: 1, name: "团队" },
        { value: 2, name: "自建" },
      ],
      type_list: [
        { id: 1, lable: "CRM" },
        { id: 2, lable: "企业微信" },
        { id: 3, lable: "房源" },
      ],
      value:"",
      Scriptsdata:{
        scope:1,//发布方式默认团队
        module: [],
        is_platform: 0,
        cover_pic: ''
      },//添加话术库数据
      EditingVisible:false,//编辑话术库弹框
      rowdata:{
        module: []
      },//话术编辑信息
      dialogCreate: false,
      titleMap: {
        addData: "添加数据",
        updateData: "修改数据",
      },
      dialogTitle: "",
      form_info: {
        title: "",
        content: "",
      },
      multipleSelection: [],
      is_button_loading: false,
      //表单验证
      rules:{
        title:[{required: true, message: "请输入标题名称", trigger: "blur"}],
        content:[{required:true,message:"请输入文本内容",trigger:"blur"}]
      },
      Moduleclassification:false,//模块分类
      moduledata:[],
      radio2:"1",//选中的模块id
      inputclassification:"",//分类名称
      classifydata:[],//分类数据
      ModuleName:"",//模块名称
      Secondaryclass:'',//二级分类名称
      temporarydata:{},//临时存放分类上一级数据
      Addsub:false,//添加下级分类时
      Editsub:false,//编辑分类时
      showScriptList:false,//话术列表
      Scriptiddata:{},
      dialogs: {
        setSortForm: false,
        importSpeechLibrary: false,
      },
      submitings: [],
    };
  },
  computed: {
    isPlatformWebsite(){
      return this.$route.query.website_id == 109;
    }
  },
  watch: {
    tableData(data){
      this.submitings =  data.map(e => {
        return {
          id: e.id,
          setStatusing: false,
          copying: false,
        }
      })
    }
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    //是否可编辑
    editAbled(item){
      return this.isPlatformWebsite || item.is_platform != 1;
    },
    //是否可进入话术库详情
    detailsAbled(item){
      return this.isPlatformWebsite || item.is_platform != 1;
    },
    //是否可删除
    deleteAbled(item){
      return this.isPlatformWebsite || (item.is_platform != 1 && item.is_system != 1);
    },
    //是否可云导入
    copyAbled(item){
      return item.is_platform == 1;
    },
    //是否可导入
    importAbled(item){
      return this.isPlatformWebsite || item.is_platform != 1;
    },
    //是否可排序
    sortAbled(item){
      return item.is_platform != 1;
    },
    //是否可启用/禁用
    statusAbled(item){
      return this.isPlatformWebsite || item.is_platform != 1;
    },
    
    //设置排序
    async openSetSortForm(item){
      this.dialogs.setSortForm = true;
      await this.$nextTick();
      this.$refs.setSortForm.open(item).onSuccess(()=>{
        this.getDataList();
      });
    },
    //设置状态
    async setSpeechLibraryStatus(item, index){
      let status = item.status;
      const confirm = await this.$confirm('确定要'+(status?'禁用':'启用')+'吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).catch(() => {});
      if(confirm){
        this.submitings[index].setStatusing = true;
        const res = await this.$http.setSpeechLibraryStatus({
          id: item.id,
          status: 1-status
        }).catch(e=>{})
        this.submitings[index].setStatusing = false;
        if(res.status == 200){
          this.$message.success('设置成功');
          this.getDataList();
        }
      }
    },
    handleCommand(e, item, index){
      switch(e){
        case 'copy':
          this.copySpeechLibrary(item, index);
          break;
        case 'import':
          this.importSpeechLibrary(item);
          break;
        case 'edit':
          this.Editingscript(item);
          break;
        case 'delete':
          this.deyllscript(item);
          break;
      }
    },
    //云导入
    async copySpeechLibrary(item, index){
      const confirm = await this.$confirm('确定要导入该话术库吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).catch(() => {});
      if(confirm){
        this.submitings[index].copying = true;
        const res = await this.$http.copySpeechLibrary(item.id).catch(e=>{})
        this.submitings[index].copying = false;
        if(res.status == 200){
          this.$message.success('导入成功');
          this.getDataList();
        }
      }
      console.log(confirm);

    },
    async importSpeechLibrary(item){
      this.dialogs.importSpeechLibrary = true;
      await this.$nextTick();
      this.$refs.importSpeechLibrary.open(item);
    },
    onScopeTableClick(item){
      this.params.scope = item.value;
      this.params.page = 1;
      this.getDataList()
    },

    getDataList() {
      this.is_table_loading = true;
      
      const api = this.params.scope == 3 ? 'speechLibraryPlatformList' : 'speechLibraryManagerList'
      this.$http[api](this.params).then((res) => {
        this.is_table_loading = false;
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.total = res.data.total
          console.log(res.data,"1212121212121");
       
        }
      }).catch(err => {
        this.is_table_loading = false;
      });
    },
    //关闭模块分类弹窗
    handleClose(){
      this.Moduleclassification = false
      this.EditingVisible = false
    },
    //话术预览
    scriptdetails(item){
      this.$refs.scriptdetails.open(item)
    },
    //编辑话术库
    Editingscript(row){
      let { package_name, scope, module, id, is_platform, cover_pic } = row;
      module = module ? module.split(',').map(e=>e*1) : [];
      this.rowdata = { package_name, scope, module, id, is_platform, cover_pic };
      this.EditingVisible = true
    },
    //确定编辑
    editlibraryname(){
      const rowdata = {...this.rowdata};
      rowdata.module = rowdata.module.join(',')
      if(!this.isPlatformWebsite){
        delete rowdata.is_platform
        delete rowdata.cover_pic
      }
      this.$http.setlibrarydataV2(rowdata).then((res)=>{
        if(res.status==200){
          this.$message.success("修改成功！")
          this.getDataList()
          this.EditingVisible = false
        }
      })
    },
    //删除话术库
    deyllscript(row){
      console.log(row,"del删除==============");
        this.$confirm('此操作将永久删除该话术库, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.deletlibraryV2(row.id).then((res)=>{
              if(res.status==200){
                this.getDataList()
                this.$message({
                type: 'success',
                message: '删除成功!'
              });
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    //新增话术库
    onCreateTalk() {
      this.Scriptsdata = {
        scope:1,//发布方式默认团队
        module: [],
        is_platform: 0,
        cover_pic: ''
      }
      this.dialogTitle = "addData";
      this.Moduleclassification = true
    },
    //打开话术库具体话术
    librarydetails(row){
      if(!this.detailsAbled(row)){
        return;
      }
      this.Scriptiddata = row
      this.showScriptList = !this.showScriptList
    },
    //打开话术列表
    Listchange(){
      // this.Scriptiddata = row
      this.showScriptList = !this.showScriptList
    },
    //添加话术库并创建一个话术
    SubmitScript() {
      let Scriptsdata = {}
      if (!this.Scriptsdata.package_name) {
        this.$message.warning("请检查话术库标题后提交");
        return;
      }
      // if (!this.Scriptsdata.title) {
      //   this.$message.warning("请检查话术标题后提交");
      //   return;
      // }
      // if (!this.Scriptsdata.content) {
      //   this.$message.warning("请检查话术内容后提交");
      //   return;
      // }
      if (!this.Scriptsdata.scope) {
        this.$message.warning("请检查发布方式后提交");
        return;
      }
      if (!this.Scriptsdata.module.length) {
        this.$message.warning("请检查使用模块后提交");
        return;
      }
      console.log(this.Scriptsdata,"12222======================");
      Scriptsdata = JSON.parse(JSON.stringify(this.Scriptsdata)); // 深拷贝客户类型列表
      Scriptsdata.module = Scriptsdata.module.join(',')
      if(!this.isPlatformWebsite){
        delete Scriptsdata.is_platform
        delete Scriptsdata.cover_pic
      }
      this.is_button_loading = true;
      this.$http.addscriptlibraryV2(Scriptsdata).then((res)=>{
        if(res.status==200){
          this.$message.success("添加成功")
          this.getDataList()
          this.is_button_loading = false;
          this.Moduleclassification = false
        }
      })
    },
    //检索
    onChangeKeywords() {
      this.params.page = 1;
      // this.getDataList();
    },
    onPageChange(e) {
      this.params.page = e;
      this.getDataList()
    },
    handleSizeChange(e){
      this.params.per_page = e;
      this.getDataList();
    },
  },
};
</script>
<style scoped lang="scss">
.pages {
  background: #f1f4fa 100%;
  padding: 24px;
  .header-title {
    height: 50px;
    line-height: 50px;
    background: #fff;
    margin: -24px -24px 10px;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
    .tabs{
      flex: 1;
      display: flex;
      flex-direction: row;
      padding: 0 12px;
      .tabs-item{
        cursor: pointer;
        color: #6c6c6c;
        padding: 0 12px;
        margin: 0 12px;
        position: relative;
        &.active{
          color: #409EFF;
          &::after{
            background-color: #409EFF;
          }
        }
        &::after{
          content: " ";
          position: absolute;
          bottom: 0;
          left: 50%;
          height: 3px;
          width: 18px;
          margin-left: -9px;
        }
      }
    }
    .ht-title {
      margin-left: 43px;
      font-size: 18px;
      color: #2e3c4e;
    }
  }
  .cardku{
    width: 98%;
    // height: 500px;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    .ScriptPack{
      position: relative;
      width: 360px;
      height: 360px;
      border: 1px solid #F2F3F5;
      border-radius: 8px;
      margin: 5px;
      &.detail-prevent{
        .Packimg{
          cursor: default;
        }
        .Packtext .title{
          cursor: text;
        }
      }
      .marks{
        .el-tag{
          margin-right: 10px;
        }
      }
      .Packimg{
        position: relative;
        width: 360px;
        height:250px;
        cursor: pointer;
        .tag-status{
          position: absolute;
          bottom: 6px;
          right: 6px;
        }
        img{
        width: 100%;
        height: 100%;
      }
      }
      .Packtext{
        margin: 20px 17px;
        font-size: 18px;
        color: #1D2129;
        font-weight: 600;
        .title{
          cursor: pointer;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .Packrange{
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
      }

    }
    .bgcolor{
      width: 24%;
      min-width: 360px;
      height: 250px;
      border-radius: 8px;
      background-color: #E8F3FF;
      overflow: hidden;
      margin: 6px;
      .bgcolortext{
        color: #165DFF;
        text-align: center;
        line-height: 240px;
        cursor: pointer;
      }
    }
  }
  .addname{
    margin-top: 20px;
    display: flex;
    align-items: center;
  }
  .QuickEditA {
    width: 90%;
    height: calc(97vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-left: 22px;
    margin-top: -15px;
  }
  .text{
      font-size: 14px;
      color: #4E5969;
      margin: 15px 0px;
    }
  .demo-drawer__footer{
    display: flex;
    justify-content: flex-end;
    margin: 10px 25px 0px 23px;
  }
}

</style>
