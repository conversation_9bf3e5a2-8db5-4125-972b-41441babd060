<template>
    <div>
        <el-dialog
            title="客户跟进"
            :visible.sync="show_Follow"
            :close-on-click-modal="false"
            width="400px"
            @close="closeFollow">
            <span class="PhoneTitle">{{ this.ponent_Follow_data.cname }}</span>
            <div class="flex-box">
                <div class="customer-status-box">
                    <el-radio-group v-model="follow_params1.tracking_id" size="mini">
                        <el-radio 
                            v-for="item in this.status_list" 
                            :key="item.id" 
                            :label="item.id" 
                            border>
                            {{ item.title }}
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="msg" style="margin: 20px 0">
                    <el-input
                        :rows="3"
                        v-model="follow_params1.content"
                        type="textarea"
                        placeholder="请输入跟进内容（企业内公开）"
                    ></el-input>
                </div>
                <div class="footer flex-row align-center j-center">
                    <el-button type="primary" @click="confirmFollow" :loading="is_loading">确定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        // 控制模态框
        show_Follow_dialog: {
            type: Boolean,
            default: false
        },
        // 客户信息
        ponent_Follow_data: {
            type: Object,
            default: (() => {})
        },
    },
    data() {
        return {
            show_Follow: false, // 控制模态框显示隐藏
            follow_params1: {
                type: 1,
                content: "",
                tracking_id: "", // 客户状态: 默认选中有效客户
                client_id: "",
            },
            // 获取客户跟进状态参数
            tracking_params: {
                type: 2,
            },
            status_list: [], // 客户状态列表 
            is_loading: false, // loading加载
        }
    },
    created() {
        this.initialization(); // 初始化数据
        this.getStatus();
    },
    methods: {
        // 初始化数据
        initialization() {
            this.show_Follow = this.show_Follow_dialog;
            this.follow_params1.client_id = this.ponent_Follow_data.id; // 赋值客户id
            // 判断用户是否有状态
            if(this.ponent_Follow_data.tracking && Object.keys(this.ponent_Follow_data.tracking).length) {
                this.follow_params1.tracking_id = this.ponent_Follow_data.tracking.id; // 赋值跟进id
            }
        },
        // 获取客户状态
        getStatus() {
            if(!this.status_list.length) {
                this.$http
                .getCrmCustomerFollowInfo({ params: this.tracking_params })
                .then((res) => {
                if (res.status === 200) {
                    this.status_list = res.data;
                    if(this.ponent_Follow_data.tracking && Object.keys(this.ponent_Follow_data.tracking).length) {
                        let add_tracking = true
                        this.status_list.map((item) => {
                            if(this.ponent_Follow_data.tracking.id == item.id) {
                                this.follow_params1.tracking_id = item.id
                                add_tracking = false
                            }
                        })
                        if(add_tracking) {
                            this.status_list.unshift(this.ponent_Follow_data.tracking);
                        }
                    }
                }
                });
            }
        },
        // 确定跟进
        confirmFollow() {
            if (!this.follow_params1.tracking_id) {
                this.$message.warning("请选择客源状态");
                return;
            }
            if (this.follow_params1.content.length < 5) {
                this.$message.warning("最少输入不能小于五个文字");
                return;
            }
            this.is_loading = true;
            if(this.ponent_Follow_data.information==1){
                this.$http.lookphoneFollow(this.follow_params1).then((res) => {
                if(res.status == 200) {
                    this.show_Follow = false; // 关闭模态框
                    this.is_loading = false; // 关闭loading加载动画
                    this.$emit("addFollowSuccess", {});
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
            }else{
                this.$http.setCrmCustomerFollowData(this.follow_params1).then((res) => {
                if(res.status == 200) {
                    this.show_Follow = false; // 关闭模态框
                    this.is_loading = false; // 关闭loading加载动画
                    this.$emit("addFollowSuccess", {});
                } else {
                    this.is_loading = false;
                }
            }).catch(() => {
                this.is_loading = false;
            })
            }
        },
        // 关闭模态框回调
        closeFollow() {
            this.$emit("closeFollow", {});
        }
    }
}
</script>
<style lang="scss" scoped>
.el-dialog__body {
  .PhoneTitle {
    display: block;
    // padding: 0 0 24px 0;
    font-size: 18px;
    color: #2e3c4e;
    font-weight: 500;
  }
}
.itemCenter {
  align-items: center;
}
.customer-status-box {
    margin-top: 20px;
    ::v-deep .el-radio-group {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        flex-wrap: wrap;
        .el-radio {
            margin: 0 0 10px 0;
            // margin-right: 19px;
            // margin-bottom: 10px;
            // &:nth-child(3) {
            //     margin-right: 0px;
            // }
            // &:last-child {
            //     margin-left: 0px;
            // }
        }
    }
}
</style>