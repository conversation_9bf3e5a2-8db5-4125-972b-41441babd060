<template>
  <div class="sms-list">
    <div class="sms_info">
      <div class="top row div">
        <!-- <el-button type="primary" @click="createOssOrder" size="mini"
        >创建短信订单</el-button
      >
      <div class="shu"></div>
      <el-button type="primary" @click="createSmsDialog" size="mini"
        >短信配置</el-button
      > -->
        <span> 短信 </span>
        <span style="margin-left: 24px">当前状态：</span>
        <span v-if="is_open_sms == 1">已开启</span>
        <span v-if="is_open_sms == 0">已关闭</span>
        <el-switch
          v-model="is_open_sms"
          active-color="#2D84FB"
          inactive-color="#ff4949"
          active-value="1"
          inactive-value="0"
          @change="onChangeSwitch"
        >
        </el-switch>
      </div>
      <div class="sms-border div row">
        <div class="sms-item flex-1">
          <div class="t">{{ sms_amount.sms_balance_total || 0 }}</div>
          <div class="b">短信余额</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">{{ sms_amount.sms_have_sent || 0 }}</div>
          <div class="b">短信发送</div>
        </div>
        <div class="sms-item flex-1 align-center" style="border: none">
          <div class="b1 btn w-160" @click="createOssOrder">短信充值</div>

          <!-- <div class="b2 btn" @click="$router.push('add_system_msg')">
          消息通知
        </div> -->
          <el-button type="primary" class="w-160" @click="createSmsDialog"
            >短信配置</el-button
          >
        </div>
        <div
          class="sms-item flex-1 align-center"
          style="border: none; justify-content: flex-start; padding-left: 0"
        >
          <!-- <div class="b1 btn" @click="createOssOrder">短信充值</div> -->

          <div class="b1 btn w-160" @click="rechargeRecord">充值记录</div>
          <!-- <div class="b1 btn w-160" @click="creatSignDialog">签名配置</div> -->
          <el-button type="primary" class="w-160" @click="creatSignDialog"
            >签名配置</el-button
          >
        </div>
      </div>
    </div>
    <div class="marketing_sms_info" style="margin-top: 20px">
      <div class="top row div" style="margin-bottom: 20px">
        <span>营销短信</span>
      </div>
      <el-alert
        :closable="false"
        title="营销短信为房产专用通道，群发任务操作前，请与客服确认短信签名、模版审核已通过后再提交发送操作"
        type="warning"
        show-icon
        style="margin: 0 40px 0 0; padding: 20px 10px"
      >
      </el-alert>
      <div class="sms-border div row">
        <div class="sms-item flex-1">
          <div class="t">{{ market_amount.marketing_sms_surplus || 0 }}</div>
          <div class="b">短信余额</div>
        </div>
        <div class="sms-item flex-1">
          <div class="t">{{ market_amount.sms_have_sent || 0 }}</div>
          <div class="b">短信发送</div>
        </div>
        <div class="sms-item flex-1 align-center" style="border: none">
          <div
            class="b1 btn w-160"
            @click="createMarketOrder"
            style="cursor: pointer"
          >
            短信充值
          </div>
          <el-button type="primary" class="w-160" @click="createMarketDialog"
            >签名配置</el-button
          >
        </div>
        <div
          class="sms-item j-center align-center flex-1"
          style="border: none; justify-content: flex-start; padding-left: 0"
        >
          <div class="b1 btn w-160" @click="orderMarketRecord">充值记录</div>
          <el-button
            type="primary"
            class="w-160"
            @click="createMarketTaskDialog"
            >短信群发</el-button
          >
        </div>
      </div>
    </div>

    <el-dialog title="记录" :visible.sync="dialogVisible" width="70%">
      <div style="margin-top: 20px; margin-bottom: 15px">
        <el-tabs v-model="recordName" @tab-click="changeRecordName">
          <el-tab-pane label="充值记录" name="TopUp"></el-tab-pane>
          <el-tab-pane label="发送记录" name="send"></el-tab-pane>
        </el-tabs>
        <myTable v-if="recordName == 'TopUp'" :table-list="tableData" :header="table_header"></myTable>
        <myTable v-if="recordName == 'send'" :table-list="sendDataList" :header="send_header"></myTable>
        <myPagination
          v-if="recordName == 'TopUp'" 
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
        <myPagination
          v-if="recordName == 'send'" 
          :total="send_params.total"
          :pagesize="send_params.pagesize"
          :currentPage="send_params.page"
          @handleSizeChange="sendParamsSizeChange"
          @handleCurrentChange="sendParamsCurrentChange"
        ></myPagination>
        <el-dialog v-if="recordName == 'TopUp'"  width="400px" :visible.sync="dialogPayCode" title="支付">
          <div class="code-box">
            <img :src="codeImg" alt="" />
            <p>请打开微信扫码支付</p>
          </div>
        </el-dialog>
      </div>
    </el-dialog>
    <el-dialog title="套餐列表" :visible.sync="smsDisplay">
      <myTable
        :table-list="sms_product_list"
        :header="sms_product_list_header"
      ></myTable>
    </el-dialog>
    <el-dialog :visible.sync="dialog_sms" title="短信配置">
      <el-form :model="msg_form" label-width="120px">
        <div v-for="item in msg_list" :key="item.id">
          <el-form-item :label="item.descp + '：'">
            <el-radio v-model="item.value" label="0">关闭</el-radio>
            <el-radio v-model="item.value" label="1">开启</el-radio>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="onClickForm">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog width="70%" :visible.sync="dialog_market_order" title="充值记录">
      <marketOrder v-if="dialog_market_order"></marketOrder>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="is_show_sign_Dia" title="签名">
      <div v-if="is_show_sign_Dia">
        <el-form label-width="60px">
          <el-form-item label="签名">
            <el-input maxlength="10" v-model="sign_form.value"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_show_sign_Dia = false">取 消</el-button>
        <el-button type="primary" v-loading="add_loading" @click="confirmSign"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog width="500px" :visible.sync="show_sms_dialog" title="短信签名">
      <div v-if="show_sms_dialog">
        <el-form label-width="60px">
          <el-form-item label="签名">
            <el-input v-model="sms_sign_form.value"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer-detail">
        <el-button @click="show_sms_dialog = false">取 消</el-button>
        <el-button
          type="primary"
          v-loading="add_loading"
          @click="confirmSmsSign"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog width="1000px" :visible.sync="is_show_task_Dia" title="任务列表">
      <marketTask></marketTask>
    </el-dialog>
    <el-dialog
      width="800px"
      :visible.sync="is_showDia"
      append-to-body
      title="套餐列表"
    >
      <div v-if="is_showDia">
        <el-table
          v-loading="is_table_loading1"
          :data="packageList"
          border
          :header-cell-style="{ background: '#EBF0F7' }"
          highlight-current-row
          :row-style="$TableRowStyle"
        >
          <!-- <el-table-column prop="id" label="订单ID"></el-table-column> -->
          <el-table-column
            prop="id"
            label="套餐id"
            width="80"
          ></el-table-column>
          <el-table-column prop="name" label="套餐名称"></el-table-column>
          <el-table-column prop="price" label="套餐价格"></el-table-column>
          <el-table-column prop="sms_total" label="短信条数"></el-table-column>
          <!-- sms_total -->
          <el-table-column
            prop="description"
            label="套餐描述"
          ></el-table-column>
          <el-table-column prop="status" label="套餐状态" v-slot="{ row }">
            <el-tag v-if="row.status == 1" type="success"> 正常 </el-tag>
            <el-tag v-else type="warning"> 已下架 </el-tag>
          </el-table-column>

          <el-table-column label="操作" v-slot="{ row }">
            <el-popconfirm
              title="确定创建订单吗？"
              style="margin: 0 10px"
              @onConfirm="confirmAdd(row)"
            >
              <el-link slot="reference" type="success">购买套餐</el-link>
            </el-popconfirm>
          </el-table-column>
        </el-table>

        <el-pagination
          style="text-align: end; margin-top: 24px"
          background
          layout="prev, pager, next"
          :total="packageTotal"
          :page-size="package_params.per_page"
          :current-page="package_params.page"
          @current-change="onPackagePageChange"
        >
        </el-pagination>
      </div>
      <!-- <span slot="footer" class="dialog-footer-detail">
        <el-button @click="is_showDia = false">取 消</el-button>
        <el-button type="primary"  @click="confirmAdd"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>
    <el-dialog width="500px" :visible.sync="showPayQrcode" title="支付">
      <div class="flex-row items-center j-center" v-if="showPayQrcode">
        <div>
          <div class="pay_img">
            <img :src="payQrcode" alt="" />
          </div>
          <div class="pay_tips">请打开微信扫一扫支付</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import myTable from "@/components/components/my_table";
import marketOrder from "@/components/navMain/crm/components/marketOrder/marketOrder";
import marketTask from "@/components/navMain/crm/components/marketingTask/marketingTask";
// import marketTask from "@/components/navMain/crm/marketingSms";
import myPagination from "@/components/components/my_pagination";
export default {
  components: {
    myTable,
    myPagination,
    marketOrder,
    marketTask
  },
  data() {
    return {
      dialogVisible: false,
      smsDisplay: false,
      sms_product_list: [],
      sms_product_list_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "name", label: "套餐名称" },
        { prop: "price", label: "套餐价格/元" },
        { prop: "sms_total", label: "短信数量/条" },
        {
          label: "操作",
          fixed: "right",
          render: (h, data) => {
            return (
              <el-button
                type="success"
                size="mini"
                onClick={() => {
                  this.buySms(data.row);
                }}
              >
                购买套餐
              </el-button>
            );
          },
        },
      ],
      tableData: [],
      params: {
        page: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      send_params: {
        page: 1,
        total: 0,
        row: 10,
      },
      is_open_sms: "0",
      sms_id: "",
      sms_amount: {},
      dialogPayCode: false,
      codeImg: "",
      table_header: [
        {
          expand: "expand",
          render: (h, data) => {
            return (
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="创建时间：">
                  <span>{data.row.created_at}</span>
                </el-form-item>
                {data.row.status === 1 ? (
                  <el-form-item label="支付时间：">
                    <span>{data.row.payment_at}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
                {data.row.status === 1 ? (
                  <el-form-item label="支付方式：">
                    {data.row.payment_category_id === 0
                      ? "暂未支付"
                      : data.row.payment_category_id === 1
                        ? "微信小程序支付"
                        : data.row.payment_category_id === 2
                          ? "微信扫码支付"
                          : data.row.payment_category_id === 3
                            ? "微信APP支付"
                            : "微信H5支付"}
                  </el-form-item>
                ) : (
                  ""
                )}
                <el-form-item label="订单支付状态：">
                  <span>
                    {data.row.payment_status === 0 ? "未付款" : "已付款"}
                  </span>
                </el-form-item>
                {data.row.status === 1 ? (
                  <el-form-item label="成交单号">
                    <span>{data.row.payment_trade_sn}</span>
                  </el-form-item>
                ) : (
                  ""
                )}
                <el-form-item label="备注信息：">
                  <span>{data.row.remark}</span>
                </el-form-item>
              </el-form>
            );
          },
        },
        { prop: "id", label: "id", width: "100" },
        { prop: "order_sn", label: "订单编号" },
        { prop: "sms_total", label: "短信数量/条" },
        { prop: "payment_amount", label: "支付金额/元" },
        {
          prop: "status",
          label: "订单状态",
          formatter: (row) => {
            var status = row.status;
            if (status === 0) {
              return "未完成";
            } else if (status === 1) {
              return "已完成";
            } else {
              return "已取消";
            }
          },
        },
        {
          label: "操作",
          width: "300",
          fixed: "right",
          render: (h, data) => {
            return (
              <div>
                {data.row.status === 0 ? (
                  <el-button
                    type="primary"
                    size="mini"
                    onClick={() => {
                      this.cancelOrder(data.row);
                    }}
                  >
                    取消订单
                  </el-button>
                ) : (
                  ""
                )}
                {data.row.status === 0 ? (
                  <el-button
                    type="success"
                    size="mini"
                    onClick={() => {
                      this.wxPay(data.row);
                    }}
                  >
                    微信支付
                  </el-button>
                ) : (
                  ""
                )}
              </div>
            );
          },
        },
      ],
      // 发送记录表格头
      send_header: [
        { prop: "id", label: "id", width: "100" },
        { prop: "phone", label: "手机号码" },
        { prop: "template_code", label: "模板信息" },
        { prop: "created_at", label: "发送时间" },
      ], 
      dialog_market_order: false,
      dialog_sms: false,
      msg_list: [],
      msg_form: {},
      is_show_sign_Dia: false, //签名弹框
      add_loading: false, //提交loading
      sign_form: {  //签名设置体骄傲参数
        value: ""
      },
      is_show_task_Dia: false,
      is_showDia: false,
      showPayQrcode: false,
      packageList: [],
      package_params: {
        page: 1,
        per_page: 10
      },
      packageTotal: 0,
      market_amount: {},
      show_sms_dialog: false,
      sms_sign_form: {
        value: ''
      },
      recordName: 'TopUp', // tabs切换绑定值
      sendDataList: [], // 存储发送记录表格数据 
    };
  },

  mounted() {
    this.getDataList();
    this.getMsgSwitchData();
    this.getSiteSmsAmountData();
    this.getCommonSettingRoles();
    this.getSiteMarketSmsAmountData()
  },
  methods: {
    createMarketOrder() {
      this.getPackageList()
      this.is_showDia = true
    },
    onPackagePageChange(current_page) {
      this.package_params.page = current_page
      this.getPackageList()
    },
    orderMarketRecord() {
      this.dialog_market_order = true
    },
    createMarketDialog() {
      this.getSign()
      this.is_show_sign_Dia = true
    },
    createMarketTaskDialog() {
      this.is_show_task_Dia = true
    },
    getPackageList() {
      this.is_table_loading1 = true
      this.$http.getPackageList({ status: 1 }).then(res => {
        console.log(res);
        if (res.status == 200) {
          this.packageList = res.data.data
        }
        this.packageTotal = res.data?.total || 0
        this.is_table_loading1 = false
      }).catch(() => {
        this.is_table_loading1 = false
      })
    },
    confirmAdd(row) {
      this.add_loading = true
      this.$http.addMarketOrder({ package_id: row.id }).then(res => {
        if (res.status == 200) {
          console.log(res);
          this.$message.success("订单创建成功")
          this.is_showDia = false
          this.add_loading = false
          this.$confirm("订单创建成功，是否立即支付？", "提示", {
            confirmButtonText: "立即支付",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.pay({ id: res.data.id })
          }).catch(() => {
            this.getList()
          })
          // this.getList()
        } else {
          // this.$message.success(res.message || '任务创建失败')
          this.add_loading = false
        }
      })
        .catch(() => {
          this.add_loading = false
        })

    },
    pay(row) {
      this.$http.getMarkeOrderQrcode(row.id).then(res => {
        console.log(res.data, 1111);
        if (res.status == 200) {
          this.showPayQrcode = true
          this.payQrcode = "data:image/png;base64," +
            btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            );
        }
      })
    },
    // 提交签名配置
    confirmSign() {
      this.add_loading = true
      this.$http.saveSign(this.sign_form)
        .then(res => {
          if (res.status == 200) {
            this.$message.success(res.$message || '保存成功')
            this.is_show_sign_Dia = false
          }
          this.add_loading = false

        })
        .catch(() => {
          this.add_loading = false
        })
    },
    // 获取签名配置
    getSign() {
      this.$http.getSign().then(res => {
        if (res.status == 200) {
          this.sign_form = res.data
        }
      })
    },
    //充值记录
    rechargeRecord() {
      this.dialogVisible = true
    },
    getCommonSettingRoles() {
      this.$http.getCommonSettingRolesConf(2).then((res) => {
        this.is_show = false;
        if (res.status === 200) {
          this.msg_list = res.data;
        }
      });
    },
    getDataList() {
      this.$http.SmsOrderList({ params: this.params }).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
        }
      });
    },
    createSmsDialog() {
      this.dialog_sms = true;
    },
    // 短信签名配置
    creatSignDialog() {
      this.getSmsSign()
      this.show_sms_dialog = true
    },
    getSmsSign() {
      this.$http.getSmsSign().then(res => {
        if (res.status == 200) {
          this.sms_sign_form = res.data
        }
      })
    },
    // 提交签名配置
    confirmSmsSign() {
      this.add_loading = true
      this.$http.saveSmsSign(this.sms_sign_form)
        .then(res => {
          if (res.status == 200) {
            this.$message.success(res.$message || '保存成功')
            this.show_sms_dialog = false
          }
          this.add_loading = false

        })
        .catch(() => {
          this.add_loading = false
        })
    },
    // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      // this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.page = val;
      // this.getPageData();
      this.getDataList();
    },
    // 获取系统短信数量
    getSiteSmsAmountData() {
      this.$http.getSiteSmsAmountData().then((res) => {
        if (res.status === 200) {
          this.sms_amount = res.data;
        }
      });
    },
    // 获取营销系统短信数量
    getSiteMarketSmsAmountData() {
      this.$http.getSiteSmsAmountDataNew().then((res) => {
        console.log(res, "123333");
        if (res.status === 200) {
          this.market_amount = res.data;
        }
      });
    },
    createOssOrder() {
      this.$http.getSmsProductList().then((res) => {
        if (res.status === 200) {
          this.smsDisplay = true;
          this.sms_product_list = res.data.data;
        }
      });
    },
    wxPay(row) {
      this.codeImg = "";
      this.dialogPayCode = true;
      this.$http.getPaySmsQrCode(row.id).then((res) => {
        if (res.status === 200) {
          this.codeImg =
            "data:image/png;base64," +
            btoa(
              new Uint8Array(res.data).reduce(
                (data, byte) => data + String.fromCharCode(byte),
                ""
              )
            );
        }
      });
    },
    cancelOrder(row) {
      this.$confirm("是否取消订单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.cancelSmsOrder(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                message: "已取消",
                type: "success",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => { });
    },
    getMsgSwitchData() {
      this.$http.getMshSwitchData().then((res) => {
        if (res.status === 200) {
          res.data.map((item) => {
            if (item.name === "ALI_SMS") {
              this.is_open_sms = item.value;
              this.sms_id = item.id;
            }
          });
        }
      });
    },
    buySms(row) {
      this.$http.createSmsOrder({ package_id: row.id }).then((res) => {
        if (res.status === 200) {
          this.$message({
            message: "创建成功",
            type: "success",
          });
          this.getDataList();
          this.smsDisplay = false;
        }
      });
    },
    onChangeSwitch(e) {
      let form = {
        id: this.sms_id,
        value: e,
      };
      this.$http.setMsgSwitchData(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
        }
      });
    },
    onClickForm() {
      var arr = [];
      this.msg_list.forEach((item) => {
        let obj = {
          id: item.id,
          value: item.value,
          key: item.key,
        };
        arr.push(obj);
      });
      var form = {
        data: arr,
      };
      this.$http.setCommonSettingRolesConf(form).then((res) => {
        if (res.status === 200) {
          this.$message.success("操作成功");
          this.dialog_sms = false
          // this.$goPath("/crm_customer_business");
        }
      });
    },
    // 当tabs发生切换触发
    changeRecordName() {
      if(this.recordName == 'send' && this.sendDataList == '') {
        this.getSendRecord();
      }
    },
    getSendRecord() {
      this.$http.getSendRecord(this.send_params).then((res) => {
        if(res.status == 200) {
          console.log(res,"res");
          this.sendDataList = res.data.data;
          this.send_params.total = res.data.total; // 总条数
        }
      })
    },
    // 分页器每页总条数变化触发
    sendParamsSizeChange(val) {
      console.log(val,"val1")
      this.send_params.row = val;
      this.getSendRecord();
    },
    // 分页器当前页变化触发
    sendParamsCurrentChange(val) {
      console.log(val,"val2")
      this.send_params.page = val;
      this.getSendRecord();
    }
  },
};
</script>

<style scoped lang="scss">
.sms-list {
  .sms_info,
  .marketing_sms_info {
    padding: 0 70px;
  }
  .top {
    align-items: center;
    span {
      font-size: 20px;
      color: #1c212a;
    }
  }
  .shu {
    width: 1px;
    height: 20px;
    background: #eee;
    margin: 0 20px;
  }
  .sms-border {
    margin-top: 10px;
    padding: 24px 0;
    border-bottom: 1px solid #eee;
    margin-left: -24px;
    margin-right: -24px;
    .sms-item {
      padding: 12px 0;
      flex: 1;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-right: 1px solid #eee;
      cursor: pointer;
      .max-w-160 {
        max-width: 160px;
      }
      .w-160 {
        width: 160px;
      }
      .t {
        color: #0083ff;
        font-size: 32px;
      }
      .b {
        font-size: 14px;
        color: #768196;
      }
      .btn {
        padding: 9px 0;
        color: #fff;
        text-align: center;
        border-radius: 5px;
      }
      .b1 {
        margin-bottom: 14px;
        background: #17b63a;
      }
      .b2 {
        background: #fd7979;
      }
    }
  }
}
.code-box {
  text-align: center;
  p {
    text-align: center;
    color: #6bcc03;
    font-size: 28px;
  }
}
.rechargRec {
  display: inline-block;
  padding: 9px 43px;
  height: 21px;
  line-height: 21px;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  background: #17b63a;
  margin-bottom: 14px;
  margin-top: 12px;
  margin-left: -50px;
}
.el-col-24 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.pay_img {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.pay_tips {
  margin-top: 10px;
  text-align: center;
  color: #6bcc03;
  font-size: 28px;
}
</style>
