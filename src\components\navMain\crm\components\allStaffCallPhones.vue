<template>
  <div class="maKe_phone_call">
    <template v-if="step == 1">
      <div class="maKe_phone_title">选择外显号码</div>
      <!-- <el-input v-model="autoPhonenNumber"></el-input> -->
      <el-select v-model="selected_phone" clearable>
        <el-option v-for="item in outShowPhoneList" :key="item.show_id" :label="item.phone"
          :value="[item.show_id, item.phone]">
        </el-option>
      </el-select>
      <div class="maKe_route_title" v-if="sitelinedata.length">请选择线路</div>
        <el-select v-model="multi_route" clearable  v-if="sitelinedata.length">
          <el-option v-for="item in sitelinedata" :key="item.multi_route" :label="item.name"
            :value="item.multi_route"> 
          </el-option>
        </el-select>
      <div class="flex-row align-center submit_make_phone">
        <el-button class="flex-1" type="primary" @click="makePhone()" :loading="makePhonetrue">
          确认拨打</el-button>
      </div>
    </template>
    <template v-if="step == 2 || step == 3">
      <div v-if="call_route != 2 && call_route != 4 && call_route != 8">
        <div class="avatar">
          <img src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" alt="" />
        </div>
        <div class="telphone">{{ concealPhoneNumber || autoPhonenNumber }}</div>
        <div class="area">{{phonePlace||"未知归属地"}}</div>
        <div>
          <div class="telphone waiting blue strong">
            {{ step == 2 ? "运营商转接等待中..." : "运营商转接成功" }}
          </div>
          <div class="link_step">
            <div class="link_step_title strong">通话步骤</div>
            <div class="link_step_con flex-row j-between">
              <div class="link_step_left">
                <div class="link_step_left_top">
                  <img src="https://img.tfcs.cn/backup/static/admin/waihu/zhuanjiechenggong.png?x-oss-process=style/w_80"
                    alt="" />
                </div>
                <div class="link_step_left_bottom">线路转接成功</div>
              </div>
              <div class="join_line">>>></div>
              <div class="link_step_left">
                <div class="link_step_left_top">
                  <img src="https://img.tfcs.cn/backup/static/admin/waihu/laidian.png?x-oss-process=style/w_80" alt="" />
                </div>
                <div class="link_step_left_bottom">手机来电</div>
              </div>
              <div class="join_line">>>></div>
              <div class="link_step_left">
                <div class="link_step_left_top">
                  <img src="https://img.tfcs.cn/backup/static/admin/waihu/jieting.png?x-oss-process=style/w_80" alt="" />
                </div>
                <div class="link_step_left_bottom">客户接听通话</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="call_route == 2 || call_route == 4|| call_route == 8">
        <div>
          <!-- 文本 -->
          <!-- <div class="success_left">
            一个被叫号每天只能拨打五次如果无法拨通尝试更换主叫
          </div> -->
          <div class="zhujihaoall">
            <!-- 主机号 -->
            <div class="zhujibohao">
              请使用
              <span class="zhujibohao_number">{{ callers }}</span>
              拨打
            </div>
            <!-- 隐私号 -->
            <div class="yinshi">
              <div style="color: #FF1607; font-size: 24px;">
                {{phoneNumber}}
              </div>
              <div class="yinshi_number">
                隐私号码
              </div>
            </div>
            <!-- 倒计时 -->
            <div class="tiem">
              <div class="tiem_image">
                <img src="../../../../assets/tiem (1).png" alt="" class="tiem_image_img">
              </div>
              <div style="color: rgba(46, 60, 78, 0.70); font-size: 12px;margin-left: 4px;">隐私号码
                <span style="color: #FF3D3D; font-size: 12px;">{{convertedTime}}s</span>
                后失效
              </div>
            </div>

          </div>
          <!-- 扫码 -->
          <!-- <div class="success_right"> -->
          <div class="tiem_images">
            <img src="../../../../assets/tiem (2).png" alt="" class="tiem_image_imgs">
          </div>

          <!-- <el-popover
    placement="top-start"
    title="标题"
    width="200"
    trigger="hover"
    content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
    <div style="color: #FFF; font-size: 14px;margin-left: 12px;margin-bottom: 1px;">
        扫码拨打
       </div> -->
          <el-popover placement="top-start" width="212" trigger="hover">
            <div style="margin-top:10px;">
              <el-alert title="请使用手机自带的扫一扫,不能使用微信,支付宝!!!" type="warning" show-icon :closable="false">
              </el-alert>
            </div>
            <div id="qrcode" class="qrcode" ref="qrCodeUrl"></div>
            <el-button slot="reference" class='button'>扫码拨打</el-button>
          </el-popover>
        </div>
      </div>

    </template>
  </div>
</template>
<script>
import QRCode from "qrcodejs2";
export default {
  props: {
    // 真实号码
    autoPhonenNumber: {
      type: String,
      default: () => {},
    },
    CallPhoneOwner: {
      type: String,
      default: () => {},
    },
    // 隐藏号码
    concealPhoneNumber: {
      type: [String, Number],
      default: "",
    },
    // 当前客户id
    clientID: {
      type: [String, Number],
      default: "",
    },
    information:{
      type: [String, Number],
      default: "",
    },
    //归属地
    phonePlace:{
      type: String,
      default: ""
    },
    sitelinedata:{
        type: Array,
        default: () => []//站点绑定的线路列表
      },
  },
  data() {
    return {
      // 拨打电话属性
      step: 1,
      selected_phone: "", //选中的外显号码id
      outShowPhoneList: [], //外显号码列表
      call_route:'',
      phoneNumber:'',// 小号
      callers:'',// 主号
      tiemexpires:0,//倒计时
      convertedTime:0,//处理后的时间
      intervalId: null,// 计时器
      makePhonetrue:false,
      multi_route:"",//线路id
    };
  },
  watch: {
        sitelinedata: {
          handler(newVal) {
            if (newVal.length > 0) {
              const defaultRoute = newVal.find(item => item.is_default === 1);
              if (defaultRoute) {
                this.multi_route = defaultRoute.multi_route;
              }
            }
          },
          immediate: true, // 立即触发一次
          deep: true
        }
    },
  created() {
    // this.getphonequan()
    this.showPhone();
    this.getShowTelNumber();
  },
  
  methods: {
    //获取客户的全号手机号
    getphonequan(){
      this.$http.getphonequan(this.clientID).then(res=>{
        if(res.status==200){
          console.log(res.data,"============");
        }
      })
    },
    showPhone() {
      if (this.autoPhonenNumber == "") {
        this.$message({
          message: "手机号不能为空",
          type: "warning",
        });
      } else if (this.autoPhonenNumber.length < 11) {
        this.$message({
          message: "手机号格式不正确",
          type: "warning",
        });
      } else {
        this.step = 1;
      }
    },
    makePhone() {
      // 测试传参
      // let autoPhonenNumber = JSON.parse(JSON.stringify(this.autoPhonenNumber));
      // autoPhonenNumber = autoPhonenNumber.replace(/\s*/g, "");
      // this.step = 2;
      // console.log({
      //   phone: autoPhonenNumber,
      //   show_id: this.selected_phone,
      //   client_id: this.clientID,
      //   multi_route: this.multi_route
      // })
      // setTimeout(() => {
      //   this.step = 3
      // }, 3000);
      this.makePhonetrue = true
      let autoPhonenNumber = JSON.parse(JSON.stringify(this.autoPhonenNumber));
      autoPhonenNumber = autoPhonenNumber.replace(/\s*/g, "");
      // console.log(this.information);
      let interfaceapi = "allStaffDialTelephone"
      if(this.information==1){
        interfaceapi = "informationoutphone"
      }
      let selected_phone = ""
          if (Array.isArray(this.selected_phone)) {
            selected_phone = this.selected_phone
          } else {
            let selectedPhone = this.outShowPhoneList.find(item => item.phone === this.selected_phone);
            selected_phone =  [selectedPhone.show_id,selectedPhone.phone]
          }
      this.$http[interfaceapi]({
          phone: autoPhonenNumber,
          show_id: selected_phone[0],
          client_id: this.clientID,
          multi_route: this.multi_route
        })
        .then((res) => {
          console.log(res.data, "8888999");
          if (res.status == 200) {
            this.makePhonetrue = false
            this.step = 2;
            // 将电话记录ID、外呼拨打的客户姓名、外呼拨打的客户号码、外呼拨打的外显号码，参数传递
            this.$emit("getCallId", [
              res.data.call_id,
              this.CallPhoneOwner,
              autoPhonenNumber,
              this.selected_phone[1],
            ]);
  
            this.callers = res.data.caller
          this.call_route = res.data.call_route
          this.tiemexpires = res.data.expires
          this. convertedTime =  this.tiemexpires / 2
          // 倒计时处理
      this.intervalId = setInterval(() => {
        this.step = 3;
        if (this. convertedTime > 1) {
          this. convertedTime--;
        } else {
          clearInterval(this.intervalId);
          this.$emit("allStaffphoneClose");

        }
      }, 1000);
// 显示二维码
          if ([2, 4, 8].some(item => item == res.data.call_route)) {
            var that = this
            that.phoneNumber = res.data.telX
            console.log(that.$refs.qrCodeUrl,'this.$refs.qrCodeUrl');
        setTimeout(() => {
                  new QRCode(that.$refs.qrCodeUrl, {
                  text: 'tel:'+ that. phoneNumber, // 需要转换为二维码的内容
                  width: 170,
                  height: 170,
                  colorDark: '#000000',
                  colorLight: '#ffffff' ,
                  correctLevel: QRCode.CorrectLevel.H
              })
               }, 0)

      
          }else{
                      setTimeout(() => {
              this.step = 3;
              setTimeout(() => {
                this.$emit("allStaffphoneClose");
              }, 1000);
            }, 30000);
          }
          }else{
            this.makePhonetrue = false
          }

        });
    },
    // 获取拨打电话下拉信息
    getShowTelNumber() {
      this.$http.getExplicitNumber().then((res) => {
        if (res.status == 200) {
          this.selected_phone = res.data[0].phone
          this.outShowPhoneList = res.data;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .button {
  color: #ffff;
  border-color: #12D367;
  background-color: #12D367;
  width: 100%;
}

::v-deep .el-button:active .button {
  background-color: #12D367;
  border-color: #12D367;
  color: #ffff;
}

::v-deep .el-button .button {
  position: relative;
  background: #12D367;
  border: 1px solid #12D367;
  width: 100%;
  color: #fff;
}

.zhujihaoall {
  width: 100%;
  text-align: center;
}

.success_right {
  // width: 92px;
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 4px;
  background: #12D367;
}

.success_left {
  padding: 8px 10px;
  // width: 70%;
  color: #12D367;
  font-size: 12px;
  border-radius: 4px;
  // text-align: left;s
  border: 1px solid #12D367;
  background: rgba(18, 211, 103, 0.10);
  // margin-right: 12px;
}

.yinshi_number {
  color: rgba(46, 60, 78, 0.40);
  font-size: 12px;
  padding: 4px;
  border-radius: 4px;
  line-height: 22px;
  background: #F1F4FA;
  margin-left: 8px;

}

.tiem_image_img {
  width: 100%;
  height: 100%;
}

.tiem_image_imgs {
  width: 100%;
  height: 100%;
  margin: 0 4px;
}

.tiem_images {
  position: absolute;
  left: 10px;
  z-index: 999;
  top: 222px;
  width: 16px;
  height: 16px;
  margin-left: 145px;
}

.tiem_image {
  width: 16px;
  height: 16px;
}

.tiem {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  margin-bottom: 24px;
  justify-content: center;
}

.yinshi {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-bottom: 8px;
  justify-content: center;
}

.zhujibohao_number {
  color: #2D84FB;
  font-size: 14px;
}

.zhujibohao {
  color: rgba(46, 60, 78, 0.70);
  font-size: 14px;
  // text-align: left;
  margin-bottom: 24px;
  margin-top: 24px;
}

.qrcode {
  width: 200px;
  height: 200px;
  text-align: center;
  margin-left: 20px;
  margin-top: 40px;
}

::v-deep .dialog {
  border-radius: 20px;
  background: #f1f4fa;

  /* box-shadow: 0px 4px 50px 0px #0000003f; */
  .el-dialog__title {
    border-left: 0;
  }

}

.mr10 {
  margin-right: 10px;
}

.strong {
  font-weight: 600;
}

.blue {
  color: #2d84fb;
}

.table_oper {
  padding: 16px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #e1e1e1;
}

.maKe_phone_call {
  // height: 500px;
  text-align: center;
  background: #fff;
  // padding: 10px 20px;
  margin-bottom: 10px;
  box-sizing: border-box;

  .maKe_phone_title {
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
  }
  .maKe_route_title{
    padding: 20px 0;
    color: #2e3c4e;
    font-size: 16px;
    font-weight: 500;
  }

  .submit_make_phone {
    margin-top: 65px;
  }

  .avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 50%;

    img {
      width: 100%;
      object-fit: cover;
    }
  }

  .telphone {
    color: #2e3c4e;
    font-size: 20px;
    font-weight: 600;
    margin-top: 16px;
  }

  .area {
    color: #8a929f;
    font-size: 14px;
    margin-top: 16px;
  }

  .waiting {
    margin: 15px 0 20px;
  }

  .link_step {
    border-radius: 13px;
    background: #f1f4fa;
    padding: 10px 16px;

    .link_step_title {
      color: #2e3c4e;
      font-size: 14px;
      margin-bottom: 15px;
      white-space: nowrap;
    }

    .link_step_con {
      .link_step_left {
        width: 48px;

        .link_step_left_bottom {
          color: #2e3c4e;
          font-size: 12px;
          white-space: nowrap;
          margin-left: -10px;
        }
      }

      .join_line {
        margin-top: 5px;
        color: #2d84fb;
      }
    }
  }

  .to_back {
    margin-top: 65px;

    .to_back_img {
      width: 50px;
      height: 50px;
      margin: 0 auto 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .to_back_name {
      color: #2e3c4e;
      font-size: 14px;
    }
  }

  .el-select {
    width: 100%;
  }
}

.phone_btn {
  margin-bottom: 20px;
}
</style>
