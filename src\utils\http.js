// /**
//  * 登录
//  */

/* eslint-disable */
import axios from "axios";
// import router from "../router";
import { Message } from "element-ui";
import router from "../router";
import store from "../store/index";
import { showLoading, hideLoading } from "./my_loading";
import {
  goPath,
} from "@/utils/tools";
class UserCenter {
  constructor() {
    this.$http = axios.create({
      baseURL: "/api",
      timeout: 10000,
    });

    // 请求拦截
    this.$http.interceptors.request.use(
      function (config) {
        // showLoading();
        let auth_way = localStorage.getItem("auth_way"); // 测试来源
        config.headers["authway"] = auth_way ? auth_way : 0;
        config.headers["From"] = "computer";
        config.headers['website-from'] = 1;
        // 报备：1 、自建：2、t+：3
        if (localStorage.getItem("company_token")) {
          config.headers["Authorization"] =
            "Bearer " + localStorage.getItem("company_token");
        } else {
          config.headers["Authorization"] =
            "Bearer " + localStorage.getItem("TOKEN");
        }
        // config.headers["Content-Type"] = "application/json";
        // 导出添加一分钟超时限制
        if (
          //common/file/upload/admin  admin/crm/client/import
          config.url === "/admin/company/export/search " || config.url.includes('admin/personnelMatters/import') ||
          config.url === "/admin/vr/upload_panoramic" || config.url == 'admin/byte_dance/upload_byte_media' ||
          config.url === "/admin/crm/client/export_client" ||
          config.url === "/admin/crm/client/import" ||
          config.url === "/admin/crm/excel/import/add_client"||
          config.url.includes("/admin/customer/reported/export/search")
          || (config.url && config.url.includes("common/file/upload/admin")) || (config.url && config.url.includes("common/file/upload/admin"))
        ) {
          config.timeout = 600000;
          // config.responseType = "blob";
        }
        // 添加五分钟超时限制
        if (config.url ===
          "/admin/call_clue/addCallClueFromCrm" ||
          config.url ===
          "/admin/crm/operate/search" ||
          config.url ===
          "/admin/crm/operate/export"||
          config.url ===
         " /admin/ming_yuan/init _projects"
        ) {
          config.timeout = 300000;
        }
        // 添加一小时超时限制
        if (config.url.indexOf("/personnelMatters/inheritMember") != -1 || config.url.indexOf("personnelMatters/inheritMemberOnJob") != -1) {
          config.timeout = 6000000;
        }
        // 房源群发添加2分钟超时限制
        if (config.url.indexOf("/admin/house/releaseToMassSite") != -1) {
          config.timeout = 120000;
        }
        // 请求方式 post/get
        if (config.method == "post") {
          config.data = {
            ...config.data,
            website_id: localStorage.getItem("website_id"),
          };
        } else if (config.method == "get") {
          config.params = {
            website_id: localStorage.getItem("website_id"),
            ...config.params,
          };
        }
        return config;
      },
      function (error) {
        return Promise.reject(error);
      }
    );

    // 响应拦截
    this.$http.interceptors.response.use(
      (response) => {
        // hideLoading();
        return response;
      },
      (error) => {
        // console.log(error, "12345");
        let exUrl = ['/admin/customer/create']  // 弹出提示的接口
        // hideLoading();
        // console.log(error.response.data);
        if (error.response?.status === undefined) {
          Message.error("请检查网络连接");
          return;
        }
        if (error.response.status === 422) {
          // 判断微信小程序授权
          var str = error.response.config.url;
          if (
            str.indexOf("/admin/wx_open/mini_program/auth/fast_register/") != -1
          ) {
            router.push({
              path: "/website_update",
            });
            return;
          }
          if (str.indexOf("/admin/message/im/check") != -1) {
            return;
          }
          if (str.indexOf("/admin/wx_work/material/get") != -1) {
            Message.error("media_id仅三天有效期，请检查是否过期！");
            return;
          }
          if (str.indexOf("/admin/customer/reported/import") != -1) {
            return error.response;
          }

          console.log(error.response, error.response.config);
          if (error.response.config.params?.autoMsg === false) {
            return;
          }
          if (error.response.data.message) {
            if (error.response.data.errors) {
              let promise = Promise.resolve();
              for (var key in error.response.data.errors) {
                error.response.data.errors[key].forEach((item) => {
                  promise = promise.then(() => {
                    return new Promise((resolve) => {
                      resolve(item);
                    });
                  });
                  promise.then((err) => {

                    Message.error(err);
                  });
                });
              }
            } else {
              if (!exUrl.includes(error.config.url)) {
                Message.error(error.response.data.message);
              }

            }
          } else {
            Message.error("请求错误");
          }
          // return Promise.reject(error);
          return error.response;
        }
        if (error.response.status === 401) {
          if (!store.state.hasMessage) {
            store.state.hasMessage = true
            Message.error(error.response.data.message || 'token失效');
            setTimeout(() => {
              store.state.hasMessage = false
            }, 800);
          }

          setTimeout(() => {
            if (localStorage.getItem("company_token")) {
              localStorage.removeItem("company_token");
              router.push({
                path: "/companyLogin",
              });
              // goPath("/companyLogin");
            } else {
              localStorage.removeItem("TOKEN");
              localStorage.removeItem(
                "admin_token_" + localStorage.getItem("website_id")
              );
              router.push({
                path: "/login",
              });
            }
            localStorage.removeItem("user_name");
            localStorage.removeItem("website_id");
            localStorage.removeItem("website_crm");
            sessionStorage.removeItem("top_select_id");
            sessionStorage.removeItem("top_menu_info");
          }, 1000);

        }
        if (error.response.status === 500) {
          Message.error(
            "网络连接错误，请检查网络连接： " + error.response.data.message
          );
        }
        if (error.response.status === 404) {
          Message.error("请求出错404！");
        }
        // if (JSON.stringify(error).indexOf("timeout") != -1) {
        //   Message.error("请求超时请刷新重试");
        // var newHttp = new Promise(function(resolve) {
        //   resolve();
        // })`newHttp实例执行完成后会再次执行`;
        // // 返回一个promise实例，同时重新发起请求，config请求配置，包扩请求头和请求参数
        // return newHttp.then(function() {
        //   return axios(config);
        // });
        // }


        return Promise.reject(error);
      }
    );
  }

  /**
   * 登录
   */
  login(data = {}) {
    return this.$http.post("/auth/admin/login/user_name", data);
  }

  checkLoginTel(data = {}) {
    return this.$http.post("/auth/admin/login/findSiteByPhone", data);
  }
  // 公司后台=》登录后获取企业信息
  companyGetInfo() {
    return this.$http.get("/enterprise/enterprise/info");
  }
  // 获取登录后个人信息
  getAdmin() {
    return this.$http.get("/admin/my/query");
  }
  // 登录后判断个人信息完善资料
  setUserInfo(data) {
    return this.$http.post("/admin/personnelMatters/improveInfo", data);
  }
  // setUserInfo(data) {
  //   return this.$http.post("/admin/personnelMatters/improveInfoAndBind", data);
  // }
  // //获取手机号短信验证码
  // getphonecode(data){
  //   return this.$http.post("/admin/personnelMatters/sendSmsUserBind",data)
  // }
  // 修改管理员密码
  updataAdmin(data) {
    return this.$http.post("/admin/my/update/password", data);
  }
  // 退出登录
  loginOut() {
    return this.$http.get("/auth/admin/logout");
  }
  // 获取系统提醒消息
  getSystemReminder(params) {
    return this.$http.get("/admin/message/letter/search", { params });
  }
  // 获取系统提醒消息未阅读（3条）
  getSystemUnreadReminder() {
    return this.$http.get("/admin/message/letter/not_reading_list");
  }
  // 标记系统提醒消息已读
  setMarkSystemReminder(id) {
    return this.$http.get(`/admin/message/letter/state/${id}`);
  }
  // 删除系统提醒消息
  deleteSystemReminder(id) {
    return this.$http.get(`/admin/message/letter/delete/${id}`);
  }
  // 新建系统提醒
  addSystemReminder(data) {
    return this.$http.post("/admin/message/letter/create", data);
  }
  // 获取验证码
  // getLoginCode(time) {
  //   return this.$http.get(`/auth/admin/login/captcha?time=${time}`);
  // }
  // 获取楼盘列表数据
  getHousesList(page, name) {
    return this.$http.get(`/admin/build/search?page=${page}&name=${name}`);
  }
  getAllHouses(page, row, name) {
    return this.$http.get(
      `/admin/build/search?page=${page}&per_page=${row}&name=${name}`
    );
  }
  // 楼盘列表搜索
  searchList(input) {
    return this.$http.get(`/admin/build/search?name=${input}`);
  }
  queryBuild(params) {
    return this.$http.get("/admin/build/search", params);
  }
  // 搜索公司列表数据
  searchCompany(input) {
    return this.$http.get(`/admin/company/search?name=${input}`);
  }
  // 销售楼盘
  searchCompanyCategory(input, category) {
    return this.$http.get(
      `/admin/company/search?name=${input}&category=${category}`
    );
  }
  // 多数据搜索公司列表数据
  showCompanyLists(params) {
    return this.$http.get(`/admin/company/search`, params);
  }
  // 上传楼盘数据
  createBuild(data = {}) {
    return this.$http.post("/admin/build/simple/create", data);
  }
  // 修改楼盘信息
  updateBuild(data = {}) {
    return this.$http.post(`/admin/build/simple/update`, data);
  }
  // 删除楼盘列表数据
  deleteBuild(id) {
    return this.$http.get(`/admin/build/delete/${id}`);
  }
  // 上传户型列表
  createHouse(data = {}) {
    return this.$http.post("/admin/build/house_type/create", data);
  }
  // 获取楼盘户型列表
  getBuildList(id) {
    return this.$http.get(`/admin/build/house_type/all/${id}`);
  }
  // 获取楼盘展示
  getQueryBuildList(id) {
    return this.$http.get(`/admin/build/query/${id}`);
  }
  // 获取物业展示信息
  getQueryAttrList(id) {
    return this.$http.get(`/admin/build/attr/query/${id}`);
  }
  // 删除户型列表数据
  deleteHouse(id) {
    return this.$http.get(`/admin/build/house_type/delete/${id}`);
  }
  // 获取展示列表
  getQueryTypeList(id) {
    return this.$http.get(`/admin/build/house_type/query/${id}`);
  }
  // 获取相册列表展示类型图片
  getphototypeList(type) {
    return this.$http.get(`/common/dictionary/search?name=${type}`);
  }
  // 修改户型列表数据
  updataType(data = {}) {
    return this.$http.post("/admin/build/house_type/update", data);
  }
  // 获取相册列表分类图片
  getImgList(id) {
    return this.$http.get(`/admin/build/img/all/${id}`);
  }
  // 公司后台=》获取相册列表分类图片
  companyGetImgList(id) {
    return this.$http.get(`/enterprise/build/img/all/${id}`);
  }
  // 删除相册图片
  deleteImg(id) {
    return this.$http.get(`/admin/build/img/delete/${id}`);
  }
  // 公司后台=》删除相册图片
  companyDeleteImg(id) {
    return this.$http.get(`/enterprise/build/img/delete/${id}`);
  }
  // 规划图相册管理上传图片
  createBuildImg(data = {}) {
    return this.$http.post("/admin/build/img/create", data);
  }
  // 公司后台=》规划图相册管理上传图片
  companyCreateBuildImg(data = {}) {
    return this.$http.post("/enterprise/build/img/create", data);
  }
  // 公司列表获取
  getCompanyList(data) {
    return this.$http.get(
      `/admin/company/search?name=${data.name}&category=${data.category}`,
      data
    );
  }
  // 展示公司列表
  porjectCompanyList(name, category, page) {
    return this.$http.get(
      `/admin/company/search?name=${name}&category=${category}&per_page=${page}`,
    );
  }
  showCompanyList(page, row) {
    return this.$http.get(`/admin/company/search?page=${page}&per_page=${row}`);
  }
  // 添加公司
  createCompany(data = {}) {
    return this.$http.post(`/admin/company/create`, data);
  }
  // 删除公司
  deleteCompany(id) {
    return this.$http.get(`/admin/company/delete/${id}`);
  }
  // 修改公司信息
  updataCompany(data = {}) {
    return this.$http.post(`/admin/company/update`, data);
  }
  // 公司展示列表
  queryCompany(id) {
    return this.$http.get(`/admin/company/query/${id}`);
  }
  // 获取项目列表
  getProjectList(params) {
    return this.$http.get(`/admin/project/search`, params);
  }
  // 搜索项目信息
  searchProject(input) {
    return this.$http.get(`/admin/project/search?name=${input}`);
  }
  // 删除项目信息
  deleteProject(id) {
    return this.$http.get(`/admin/project/delete/${id}`);
  }
  // 添加项目信息
  createProject(data) {
    return this.$http.post("/admin/project/create", data);
  }
  // 公司后台=》添加项目信息
  companyCreateProject(data) {
    return this.$http.post("/enterprise/project/create", data);
  }
  // 查询项目信息
  queryProject(id) {
    return this.$http.get(`/admin/project/query/${id}`);
  }
  // 公司后台=》查询项目信息
  companyQueryProject(id) {
    return this.$http.get(`/enterprise/project/query/${id}`);
  }
  // 更新项目列表
  updataProject(data) {
    return this.$http.post("/admin/project/update", data);
  }
  // 公司后台=》更新项目列表
  companyUpdataProject(data) {
    return this.$http.post("/enterprise/project/update", data);
  }
  // 项目公司绑定
  porjectCompany(data) {
    return this.$http.post("/admin/project/company/create", data);
  }
  // 项目经纪人绑定
  projectBroker(data) {
    return this.$http.post("/admin/project/broker/create", data);
  }
  // 经纪人列表展示
  showBrokerList(id, page) {
    return this.$http.get(
      `/admin/project/broker/search?project_id=${id}&page=${page}`
    );
  }
  searchBroker(id, name) {
    return this.$http.get(
      `/admin/project/broker/search?project_id=${id}&user_name=${name}`
    );
  }
  // 删除经纪人
  deleteBroker(id) {
    return this.$http.get(`/admin/project/broker/delete/${id}`);
  }
  // 项目经理展示
  showManagerList(id, page, name) {
    return this.$http.get(
      `/admin/project/manager/search?project_id=${id}&page=${page}&user_name=${name}`
    );
  }
  // 项目经理查询
  searchManager(id, name) {
    return this.$http.get(
      `/admin/project/manager/search?project_id=${id}&user_name=${name}`
    );
  }
  // 项目经理绑定
  projectManager(data) {
    return this.$http.post("/admin/project/manager/create", data);
  }
  // 删除项目经理
  deleteManager(id) {
    return this.$http.get(`/admin/project/manager/delete/${id}`);
  }
  // 查询用户列表
  searchUserList(row, phone, category) {
    if (category) {
      return this.$http.get(
        `/admin/user/search?per_page=${row}&phone=${phone}&category=${category}`
      );
    } else {
      return this.$http.get(
        `/admin/user/search?per_page=${row}&phone=${phone}`
      );
    }
  }
  // 用户名查询公司用户列表
  searchUserListByName(row, user_name, category, company_id = 0) {
    return this.$http.get(
      `/admin/user/search?per_page=${row}&name=${user_name}&category=${category}&company_id=${company_id}`
    );
  }
  // 用户名查询公司用户列表
  searchUserListByCompany(row, company_id, name, phone) {
    return this.$http.get(
      `/admin/user/search?page=${row}&company_id=${company_id}&name=${name}&phone=${phone}`
    );
  }
  // 手机号查询公司用户列表
  searchUserListByphone(row, phone, category, company_id = 0) {
    return this.$http.get(
      `/admin/user/search?per_page=${row}&phone=${phone}&category=${category}&company_id=${company_id}`
    );
  }
  searchWebUserList(user_name) {
    return this.$http.get(`/admin/user/search?user_name=${user_name}`);
  }
  //
  searchUserByPhone(phone) {
    return this.$http.get(`/admin/user/search?phone=${phone}`);
  }
  getUserBroker(params) {
    return this.$http.get("/admin/user/search", params);
  }
  // 查询用户信息
  searchUser(user_name) {
    return this.$http.get(`/admin/admin_user/search?user_name=${user_name}`);
  }
  // 查询客户轨迹列表
  getGuijinInfo(params) {
    return this.$http.get(`/admin/crm/tfy_client_track/search`, { params });
  }

  // 获取用户列表
  getUserList(params) {
    return this.$http.get(
      `/admin/crm/admin_users/search`, { params }
    );
  }
  //获取用户列表的表头
  watchhead() {
    return this.$http.get(`admin/crm/custom_table/my_client_title`)
  }
  setCustomTableColumn(data) {
    return this.$http.post(`admin/crm/custom_table/update_my_client`, data)
  }
  //详情页腾房云是否显示
  tfyshow() {
    return this.$http.get(`/admin/crm/is_open_tengfangyun`)
  }
  // 获取用户列表带有客户数量
  getUserListNew(params) {
    return this.$http.get(
      `/admin/crm/admin_users/search`, { params }
    );
  }
  // 删除用户列表
  deleteUser(id) {
    return this.$http.get(`/admin/admin_user/delete/${id}`);
  }
  // 添加用户
  createUser(data) {
    return this.$http.post("/admin/admin_user/create", data);
  }
  // 更新用户
  updataUser(data) {
    return this.$http.post("/admin/admin_user/update", data);
  }
  // 移动端设备用户列表
  showUserList(params) {
    return this.$http.get(`/admin/user/search`, params);
  }
  //新房用户列表导出
  newhouseuserimport(params){
     return this.$http.get(`/admin/user/export_user`, {params});
  }
  //公司成员列表
  showCompanyUserList(page, row, company_id = 0, phone) {
    return this.$http.get(
      `/admin/user/search?page=${page}&per_page=${row}&company_id=${company_id}&phone=${phone}`
    );
  }
  //添加公司成员(暂时不用)
  addCompanyUser(data) {
    return this.$http.post(`/admin/user/update/user_company`, data);
  }
  //添加公司成员
  addCompanyUsers(data) {
    return this.$http.post(`/admin/user/update/multi/user_company`, data);
  }
  //删除公司成员
  delCompanyUser(data) {
    return this.$http.post(`/admin/user/update/user_company`, data);
  }
  //批量删除公司成员
  delCompanyUsers(data) {
    return this.$http.post(`/admin/user/update/multi/user_company`, data);
  }
  // 修改密码
  modifyPassword(data) {
    return this.$http.post("/admin/admin_user/password/update", data);
  }
  // 字典查找类型
  dictionaryFind(type) {
    return this.$http.get(`/common/dictionary/search?name=${type}`);
  }
  // 项目动态查询
  projectNews(id, page, name) {
    return this.$http.get(
      `/admin/project/news/search?build_id=${id}&page=${page}&title=${name}`
    );
  }
  // 公司后台=》项目动态查询
  companyProjectNews(id, page, name) {
    return this.$http.get(
      `/enterprise/build/news/search?build_id=${id}&page=${page}&title=${name}`
    );
  }
  // 删除项目动态
  deleteNews(id) {
    return this.$http.get(`/admin/project/news/delete/${id}`);
  }
  // 公司后台=》删除楼盘动态
  companyDeleteNews(id) {
    return this.$http.get(`/enterprise/build/news/delete/${id}`);
  }
  // 添加项目动态
  uploadNews(data) {
    return this.$http.post("/admin/project/news/create", data);
  }
  // 公司后台=》添加项目动态
  companyUploadNews(data) {
    return this.$http.post("/enterprise/build/news/create", data);
  }
  // 展示动态内容
  queryNews(id) {
    return this.$http.get(`/admin/project/news/query/${id}`);
  }
  // 公司后台=》查询动态内容
  companyQueryNews(id) {
    return this.$http.get(`/enterprise/build/news/query/${id}`);
  }
  // 修改动态
  updataNews(data) {
    return this.$http.post(`/admin/project/news/update`, data);
  }
  // 公司后台=》修改动态
  companyUpdataNews(data) {
    return this.$http.post("/enterprise/build/news/update", data);
  }
  // 添加推广内容
  uploadShare(data) {
    return this.$http.post("/admin/project/share/content/create", data);
  }
  // 公司后台=》添加推广内容
  companyUploadShare(data) {
    return this.$http.post("/enterprise/project/share/content/create", data);
  }
  // 查询推广内容列表
  allShare(id, page) {
    return this.$http.get(
      `/admin/project/share/content/all/${id}?page=${page}`
    );
  }
  // 公司后台=》查询推广内容列表
  companyAllshare(id, page) {
    return this.$http.get(
      `/enterprise/project/share/content/all/${id}?page=${page}`
    );
  }
  // 删除推广内容
  deleteShare(id) {
    return this.$http.get(`/admin/project/share/content/delete/${id}`);
  }
  //公司后台=》删除推广内容
  companyDeleteShare(id) {
    return this.$http.get(`/enterprise/project/share/content/delete/${id}`);
  }
  // 展示推广内容
  queryShare(id) {
    return this.$http.get(`/admin/project/share/content/query/${id}`);
  }
  // 修改推广展示内容
  updataShare(data) {
    return this.$http.post("/admin/project/share/content/update", data);
  }
  // 公司后台=》修改推广展示内容
  companyUpdataShare(data) {
    return this.$http.post("/enterprise/project/share/content/update", data);
  }
  // 获取分类列表
  uploadCategory(page, row) {
    return this.$http.get(
      `/admin/news/category/search?page=${page}&per_page=${row}`
    );
  }
  // 修改分类列表
  deleteCategory(id) {
    return this.$http.get(`/admin/news/category/delete/${id}`);
  }
  // 修改分类内容
  updataCategory(data) {
    return this.$http.post("/admin/news/category/update", data);
  }
  // 增加分类内容
  createCategory(data) {
    return this.$http.post("/admin/news/category/create", data);
  }
  // 增加资讯
  uploadInfo(data) {
    return this.$http.post("/admin/news/create", data);
  }
  // 获取资讯列表
  searchInfo(page, row, title) {
    return this.$http.get(
      `/admin/news/search?page=${page}&per_page=${row}&title=${title}`
    );
  }
  getNewsCategoryList() {
    return this.$http.get(
      `/admin/news/category/search?is_page=0&website_id=${localStorage.getItem(
        "website_id"
      )}`
    );
  }
  // 删除资讯列表
  deleteInfo(id) {
    return this.$http.get(`/admin/news/delete/${id}`);
  }
  // 修改资讯列表
  updataInfo(data) {
    return this.$http.post("/admin/news/update", data);
  }
  // 获取展示修改页面的内容
  queryInfo(id) {
    return this.$http.get(`/admin/news/query/${id}`);
  }
  // 获取城市列表
  getCity() {
    return this.$http.get("/common/city/search");
  }
  // 获取地区列表
  getRegionList() {
    return this.$http.get("/admin/region/all");
  }
  // 获取企业标签列表
  getTagList() {
    return this.$http.get("/admin/wx_agent/contacts/tag_list");
  }
  // 接待人员成员下拉选
  userOption() {
    return this.$http.get("/admin/wx_agent/kf/user_option");
  }
  // 接待人员部门下拉选
  departmentOption() {
    return this.$http.get("/admin/wx_agent/kf/department_option");
  }
  // 账号添加素材下拉选
  mediaOption() {
    return this.$http.get("/admin/wx_agent/kf/media_option");
  }
  // 获取部门列表
  getDepartmentList() {
    return this.$http.get("/admin/wx_agent/contacts/department_list");
  }
  // 获取成员列表
  getMemberList(params) {
    return this.$http.get("/admin/wx_agent/contacts/user_list", params);
  }
  // 获取成员列表
  getMemberListpage() {
    return this.$http.get("/admin/personnelMatters/allMembers");
  }
  // 获取客户列表
  getCustomerList(params) {
    return this.$http.get("/admin/wx_agent/external/custom_list", params);
  }
  // 获取企微客户列表搜索条件
  getCustomrListSearch() {
    return this.$http.get("/admin/wx_agent/external/custom_filters");
  }
  // 获取客户群群聊列表
  getGroupchatList(params) {
    return this.$http.get("/admin/wx_agent/external/groupchat_list", params);
  }
  // 导入客户群群聊列表  /admin/activityCode/importGroupChat
  importGroupchatList(params) {
    return this.$http.get("/admin/activityCode/importGroupChat", params);
  }
  // 获取客服接待人员列表
  getServicerList(params) {
    return this.$http.get("/admin/wx_agent/kf/servicer_list", params);
  }
  // 获取素材列表
  getMaterialList(params) {
    return this.$http.get("/admin/wx_agent/media/list", params);
  }
  // 获取客服账号列表
  getCustomerService(params) {
    return this.$http.get("/admin/wx_agent/kf/account_list", params);
  }
  // 获取客户消息列表
  getChatMsgList(params) {
    return this.$http.get("/admin/wx_agent/external/chat_msg_list", params);
  }
  // 获取secret
  getSecret() {
    return this.$http.get("/admin/wx_agent/config/secret_get");
  }
  // 删除地区
  deleteRegion(id) {
    return this.$http.get(`/admin/region/delete/${id}`);
  }
  // 创建地区（）
  createRegion(data) {
    return this.$http.post("/admin/region/create", data);
  }
  // 编辑地区
  updateRegion(data) {
    return this.$http.post("/admin/region/update", data);
  }
  //关于我们
  putAboutUs(data) {
    return this.$http.post("/admin/about_us/put", data);
  }
  // 获取关于我们展示内容
  getAboutUs() {
    return this.$http.get("/admin/about_us/query");
  }
  // 联系我们
  putContactUs(data) {
    return this.$http.post("/admin/contact_us/put", data);
  }
  // 获取联系我们展示内容
  getContactUs() {
    return this.$http.get("/admin/contact_us/query");
  }
  // 修改用户状态
  editUserStatus(data) {
    return this.$http.post("/admin/user/update/user_status", data);
  }
  // 修改审核状态
  editAuditStatus(data) {
    return this.$http.post("/admin/user/update/audit_status", data);
  }
  // 微信授权跳转
  openWX(url) {
    return this.$http.get(
      `/admin/wx_open/auth/login_page/url?redirect_uri=${url}`
    );
  }
  // 获取信息发送给后台
  sendWxCode(data) {
    return this.$http.post("/admin/wx_open/auth/bind/auth_code", data);
  }
  // 系统消息提醒列表
  remindMsgList(page, row) {
    return this.$http.get(`/admin/msg/search?page=${page}&per_page=${row}`);
  }
  // 系统提示消息创建
  createMsgCtn(data) {
    return this.$http.post("/admin/msg/create", data);
  }
  // 系统广告设置
  createAdv(data) {
    return this.$http.post("/admin/advertise/create", data);
  }
  // 查询广告位列表
  advAll(id, sort) {
    return this.$http.get(`/admin/advertise/all/${id}&${sort}`);
  }
  /// 广告位删除
  deleteAdv(id) {
    return this.$http.get(`/admin/advertise/delete/${id}`);
  }
  // 获取查询id获取广告
  queryAdv(id) {
    return this.$http.get(`/admin/advertise/query/${id}`);
  }
  // 修改广告位
  updataAdv(data) {
    return this.$http.post("/admin/advertise/update", data);
  }
  // 获取系统配置列表
  getSystemConfig() {
    return this.$http.get("/admin/sys_conf/ali_sms/all");
  }
  // 获取微信系统配置展示
  getWxconfig() {
    return this.$http.get("/admin/sys_conf/wx_open_template_msg/all");
  }
  // 修改短信配置信息
  editConfig(data) {
    return this.$http.post("/admin/sys_conf/ali_sms/put", data);
  }
  // 获取报备客户列表
  getReportList(params, time_value) {
    if (time_value) {
      return this.$http.get(
        `/admin/customer/reported/search?updated_date[start]=${time_value[0]}&updated_date[end]=${time_value[1]}`,
        params
      );
    } else {
      return this.$http.get(`/admin/customer/reported/search`, params);
    }
  }
  // 公司后台=》获取报备客户列表
  companyGetReportList(params, time_value) {
    if (time_value) {
      return this.$http.get(
        `/enterprise/build/customer/reported/search?updated_date[start]=${time_value[0]}&updated_date[end]=${time_value[1]}`,
        params
      );
    } else {
      return this.$http.get(
        `/enterprise/build/customer/reported/search`,
        params
      );
    }
  }
  // 生成excel表格
  exportExcelTable(params, time_value) {
    if (time_value) {
      return this.$http.get(
        `/admin/customer/reported/export/search?is_statistics_status=0&updated_date[start]=${time_value[0]}&updated_date[end]=${time_value[1]}`,
        params
      );
    } else {
      return this.$http.get(
        `/admin/customer/reported/export/search?is_statistics_status=0`,
        params
      );
    }
  }
  // 提交更改报备状态
  updataReport(data) {
    return this.$http.post("/admin/customer/reported/audit/status", data);
  }
  // 公司后台=》提交更改报备状态
  companyUpdataReport(data) {
    return this.$http.post("/enterprise/build/customer/status", data);
  }
  // 系统查询列表
  getWebsiteList(params) {
    return this.$http.get(`/common/website/search`, params);
  }

  // 报备列表客户资料图片获取
  getCustomerImg(pro_id, cus_id) {
    return this.$http.get(
      `/admin/customer/reported/audit/attached_file/all/${pro_id}/${cus_id}`
    );
  }
  // 公司后台=》报备列表客户资料图片获取
  companyGetCustomerImg(pro_id, cus_id) {
    return this.$http.get(
      `/enterprise/build/attached_file/all/${pro_id}/${cus_id}`
    );
  }
  // 获取报备进度资料
  getReportStep(pro_id, cus_id) {
    return this.$http.get(
      `/admin/customer/reported/audit/record/all/${pro_id}/${cus_id}`
    );
  }
  // 公司后台=》获取报备进度资料
  companyGetReportStep(pro_id, cus_id) {
    return this.$http.get(`/enterprise/build/record/all/${pro_id}/${cus_id}`);
  }
  // 系统内容修改
  updateWebsite(data) {
    return this.$http.post("/admin/website/update", data);
  }
  // 获取系统信息
  getWebsite(id) {
    return this.$http.get(`/common/website/query/${id}`);
  }
  // 获取身份审核的客户列表
  getAuditList(params) {
    return this.$http.get(`/admin/user/real_name/search`, params);
  }
  // 提交身份审核
  updateAudit(data) {
    return this.$http.post("/admin/user/real_name/audit", data);
  }
  // 获取提现审核
  getAuditWithdrawalList(params) {
    return this.$http.get("/admin/user/withdraw/search", params);
  }
  // 提交提现审核
  updateAuditWithdrawal(data) {
    return this.$http.post("/admin/user/withdraw/audit", data);
  }
  // 获取总数数据
  getTotalData() {
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  // 获取今日新增数据
  getDayData() {
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  // 报备提交合同编号
  createSale(data) {
    return this.$http.post("/admin/sale_order/create", data);
  }
  // 查询客户成交订单
  QueryOrder(id) {
    return this.$http.get(`/admin/sale_order/query/reported/${id}`);
  }
  // 更改订单信息
  editOrder(data) {
    return this.$http.post("/admin/sale_order/update", data);
  }
  // 确认佣金审核
  uploadSale(id) {
    return this.$http.get(`/admin/sale_order/settle/brokerage/reported/${id}`);
  }
  // 设置首页推荐
  setHomeRecommend(data) {
    return this.$http.post("/admin/build/set/home_recommend", data);
  }
  // 设置列表推荐
  setListRecommend(data) {
    return this.$http.post("/admin/build/set/list_recommend", data);
  }
  // 设置热搜推荐
  setHotRecommend(data) {
    return this.$http.post("/admin/build/set/hot_search", data);
  }
  // 创建微信消息通知模板
  createWxMsg(data) {
    return this.$http.post("/admin/sys_conf/wx_open_template_msg/put", data);
  }
  // 修改secret
  changeSecret(data) {
    return this.$http.post("/admin/wx_agent/config/secret_set", data);
  }
  // 获取客户标签组
  getGroups() {
    return this.$http.get("/admin/wx_agent/external/groups");
  }
  // 修改规则
  updataRules(data) {
    return this.$http.post("/admin/project/update/brokerage/rule", data);
  }
  // 公司后台=》修改规则
  companyUpdataRules(data) {
    return this.$http.post("/enterprise/project/update/brokerage/rule", data);
  }
  // 刷新门店码
  refreshCompanyCode(id) {
    return this.$http.get(`/admin/company/refresh/store_code/${id}`);
  }
  // 设置项目助理类型
  setUserCategory(data) {
    return this.$http.post("/admin/user/update/user_category", data);
  }
  // 查询用户信息
  queryUserInfo(id) {
    return this.$http.get(`/admin/user/query/${id}`);
  }
  // 修改用户信息
  updataUserInfo(data) {
    return this.$http.post("/admin/user/update", data);
  }
  // 获取报名列表
  getEnrollList(params) {
    return this.$http.get(`/admin/build/enroll/search`, params);
  }
  // 公司后台=》获取报名列表
  companyGetEnrollList(params) {
    return this.$http.get(`/enterprise/build/enroll/search`, params);
  }
  // 客户报备列表数据展示
  getReportData(params) {
    return this.$http.get(
      `/admin/statistics/customer_reported/overview`,
      params
    );
  }
  // 公司后台=》客户报备列表数据展示
  companyGetReportData(params) {
    return this.$http.get("/enterprise/build/statistics/overview", params);
  }
  // 获取用户数据信息
  getUserData(params) {
    if (params.date_str === "customize") {
      return this.$http.get(
        `/admin/statistics/user/overview?date[start]=${params.start}&date[end]=${params.end}`
      );
    } else {
      return this.$http.get(
        `/admin/statistics/user/overview?date_str=${params.date_str}`
      );
    }
  }
  // 获取提现审核列表数据
  getWithdrawalData(params) {
    if (params.date_str === "customize") {
      return this.$http.get(
        `/admin/statistics/withdraw/overview?date[start]=${params.start}&date[end]=${params.end}`
      );
    } else {
      return this.$http.get(
        `/admin/statistics/withdraw/overview?date_str=${params.date_str}`
      );
    }  
  }
  //   获取公司列表数据展示
  getCompanyData(params) {
    if (params.date_str === "customize") {
      return this.$http.get(
        `/admin/statistics/company/overview?date[start]=${params.start}&date[end]=${params.end}`
      );
    } else {
      return this.$http.get(
        `/admin/statistics/company/overview?date_str=${params.date_str}`
      );
    }
  }
  //   获取公司业绩统计列表数据具体展示
  getCompanyEmployeeData(params) {
    if (params.date_str === "customize") {
      return this.$http.get(
        `/admin/statistics/company/overview?company_id=${params.company_id}&date[start]=${params.start}&date[end]=${params.end}`
      );
    } else {
      return this.$http.get(
        `/admin/statistics/company/overview?company_id=${params.company_id}&date_str=${params.date_str}`
      );
    }
  }
  // 获取公司经纪人提现数据
  getCompanyBrokerageData(params) {
    if (params.date_str === "customize") {
      return this.$http.get(
        `/admin/statistics/company/brokerage/${params.company_id}?date[start]=${params.start}&date[end]=${params.end}`
      );
    } else {
      return this.$http.get(
        `/admin/statistics/company/brokerage/${params.company_id}?date_str=${params.date_str}`
      );
    }
  }
  // 获取公司员工列表
  getCompanyEmployeeList(
    company_id,
    page,
    per_page,
    name,
    phone,
    list_params = {}
  ) {
    if (list_params.date_str === "customize") {
      return this.$http.get(
        `/admin/statistics/company/employee/list?company_id=${company_id}&page=${page}&per_page=${per_page}&name=${name}&phone=${phone}&date[start]=${list_params.start}&date[end]=${list_params.end}`
      );
    } else {
      return this.$http.get(
        `/admin/statistics/company/employee/list?company_id=${company_id}&page=${page}&per_page=${per_page}&name=${name}&phone=${phone}&date_str=${list_params.date_str}`
      );
    }
  }
  // 批量设置店铺经纪人结佣金额
  settleBrokerageMoney(data) {
    return this.$http.post("/admin/company/settle/brokerage", data);
  }
  // 创建公司经纪人佣金规则
  createBrokerRules(data) {
    return this.$http.post("/admin/company/brokerage/rule/create", data);
  }
  // 查询公司规则列表
  getCompanyRules(project_id, page, per_page) {
    return this.$http.get(
      `/admin/company/brokerage/rule/search?project_id=${project_id}&page=${page}&per_page=${per_page}`
    );
  }
  // 查询规则
  queryBrokerRules(rules_id) {
    return this.$http.get(`/admin/company/brokerage/rule/query/${rules_id}`);
  }
  // 修改公司规则
  updataBrokerRules(data) {
    return this.$http.post("/admin/company/brokerage/rule/update", data);
  }
  // 删除公司规则
  deleteBrokerRules(relus_id) {
    return this.$http.get(`/admin/company/brokerage/rule/delete/${relus_id}`);
  }
  // 获取应用报名单列表
  getAuthAppList(params) {
    return this.$http.get("/admin/system/auth_app/search", params);
  }
  // 删除列表
  deleteAuthAppList(id) {
    return this.$http.get(`/admin/system/auth_app/delete/${id}`);
  }
  // 添加应用列表
  createAuthAppList(data) {
    return this.$http.post("/admin/system/auth_app/create", data);
  }
  // 查找应用权限
  queryAuthAppList(ip) {
    return this.$http.get(`/admin/system/auth_app/query/${ip}`);
  }
  // 修改应用权限列表
  updataAuthApplist(data) {
    return this.$http.post("/admin/system/auth_app/update", data);
  }
  // 小程序授权
  authorization(data) {
    return this.$http.post("/admin/wx_open/mini_program/auth/page", data);
  }
  //小程序授权（新，测试）
  Newauthorization(data) {
    return this.$http.post("/admin/wx_open/mini_program/auth/login_url", data);
  }
  // 授权成功回调获取小程序信息
  successNewauthorization(data) {
    return this.$http.post("/admin/wx_open/mini_program/auth/bind_auth", data);
  }
  // 复制公众号主体快速注册小程序
  fastRegister(ticket) {
    return this.$http.get(
      `/admin/wx_open/mini_program/auth/fast_register/${ticket}`
    );
  }
  // 提交小程序名称
  submitMini(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/set/nickname",
      data
    );
  }
  // 检测小程序名称
  detectionMini(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/check/wx/verify/nickname",
      data
    );
  }
  // 查询小程序改名审核状态
  miniNameAuditStatus(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/api/wxa/query/nickname",
      data
    );
  }
  // 新增临时素材
  // addmaterial(data) {
  //   return this.$http.post('/admin/wx_open/public/material/new/temp', forms, configs)
  // }
  // 修改小程序头像
  modifyAvatar(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/modify/head_image",
      data
    );
  }
  // 修改小程序功能介绍
  submitSignature(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/modify/signature",
      data
    );
  }
  // 查询小程序是否可以被搜索设置
  querySearchStatus() {
    return this.$http.get(
      "/admin/wx_open/mini_program/auth/get/wxa/search/status"
    );
  }
  // 设置小程序是否可被搜索
  searchStatus(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/change/wxa/search/status",
      data
    );
  }
  //设置小程序类目
  getCategory() {
    return this.$http.get("/admin/wx_open/mini_program/category/get/category");
  }
  // 添加类目
  addCategories(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/category/add/category",
      data
    );
  }
  // 提交服务器域名
  createDomain(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/modify/domain",
      data
    );
  }
  // 提交服务器业务域名
  createWebviewDomain(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/auth/set/webview/domain",
      data
    );
  }
  // 查询小程序基本信息
  queryMiniInfo() {
    return this.$http.get(
      "/admin/wx_open/mini_program/auth/account/basic_info"
    );
  }
  // 上传附件列表
  getFileList(build_id, params) {
    return this.$http.get(
      `/admin/build/attached_file/search?build_id=${build_id}`,
      params
    );
  }
  // 公司后台=》上传附件列表
  companyGetFileList(build_id, params) {
    return this.$http.get(
      `/enterprise/build/attached_file/search?build_id=${build_id}`,
      params
    );
  }
  // 删除附件
  deleteFile(id) {
    return this.$http.get(`/admin/build/attached_file/delete/${id}`);
  }
  // 公司后台=》删除附件
  companyDeleteFile(id) {
    return this.$http.get(`/enterprise/build/attached_file/delete/${id}`);
  }
  // 上传附件
  uploadFile(data) {
    return this.$http.post("/admin/build/attached_file/create", data);
  }
  // 公司后台=》上传附件
  companyUploadFile(data) {
    return this.$http.post("/enterprise/build/attached_file/create", data);
  }
  // 查找是否绑定小程序
  queryXiaoApp() {
    return this.$http.get("/admin/sys_conf/wx_open_auth/query/base/mp");
  }
  // 查找是否授权公众号
  queryGongZhong() {
    return this.$http.get("/admin/sys_conf/wx_open_auth/query/base/pub");
  }
  // 创建oss订单
  createOssOrder(data) {
    return this.$http.post("/admin/order/oss_storage/create", data);
  }
  // 订单列表
  ossOrderList(params) {
    return this.$http.get("/admin/order/oss_storage/search", params);
  }
  // 取消订单
  cancelOrder(id) {
    return this.$http.get(`/admin/order/oss_storage/cancel/${id}`);
  }
  // 获取支付二维码
  getPayQrCode(id) {
    return this.$http.get(`/admin/order/oss_storage/get/pay/qrcode/${id}`, {
      responseType: "arraybuffer",
    });
  }
  // 创建短信订单
  createSmsOrder(data) {
    return this.$http.post("/admin/order/sms/create", data);
  }
  // 短信订单列表
  SmsOrderList(params) {
    return this.$http.get("/admin/order/sms/search", params);
  }
  // 取消短信订单
  cancelSmsOrder(id) {
    return this.$http.get(`/admin/order/sms/cancel/${id}`);
  }
  // 获取短信支付二维码
  getPaySmsQrCode(id) {
    return this.$http.get(`/admin/order/sms/get/pay/qrcode/${id}`, {
      responseType: "arraybuffer",
    });
  }
  // 获取模板列表
  getCopyTemplateList(params) {
    return this.$http.get(
      "/admin/customer/reported/copy_template/search",
      params
    );
  }
  // 创建复制模板信息
  createCopyTemplate(data) {
    return this.$http.post(
      "/admin/customer/reported/copy_template/create",
      data
    );
  }
  // 修改复制模板信息
  updateCopyTemplate(data) {
    return this.$http.post(
      "/admin/customer/reported/copy_template/update",
      data
    );
  }
  // 删除模板信息
  deleteCopyTemplate(id) {
    return this.$http.get(
      `/admin/customer/reported/copy_template/delete/${id}`
    );
  }
  // 更改模板开启关闭状态
  updateCopyTemplateEnable(id, status) {
    return this.$http.get(
      `/admin/customer/reported/copy_template/update/enable/${id}/${status}`
    );
  }
  // 交易记录列表
  getTransactionList(params) {
    return this.$http.get("/admin/user/transaction/search", params);
  }
  // 添加补全隐号手机号
  fillCustomerPhone(data) {
    return this.$http.post(
      "/admin/customer/reported/fill/customer_phone",
      data
    );
  }
  // 查询oss产品列表
  getOssProductList() {
    return this.$http.get("/admin/package/oss_storage/search?status=1");
  }
  // 查询短信产品列表
  getSmsProductList() {
    return this.$http.get("/admin/package/sms/search?status=1");
  }
  // 查询管理员所拥有的角色
  getAdminRoles(id) {
    return this.$http.get(`/admin/admin_user/role/all/${id}`);
  }
  // 获取权限列表
  getPermissionList() {
    return this.$http.get("/admin/my/query/permissions/all");
  }
  // 获取系统角色列表
  getWebsiteRoles(params) {
    return this.$http.get("/admin/role/search", params);
  }
  // 获取系统角色列表(单独)
  getRolelist() {
    return this.$http.get("admin/role/list");
  }
  // 创建系统角色
  createWebsiteRole(data) {
    return this.$http.post("/admin/role/create", data);
  }
  // 修改系统角色
  updataWebsiteRole(data) {
    return this.$http.post("/admin/role/update", data);
  }
  // 删除系统角色
  deleteWebsiteRole(id) {
    return this.$http.get(`/admin/role/delete/${id}`);
  }
  // 从新分配权限
  resetPermission(data) {
    return this.$http.post("/admin/role/permission/reset/all", data);
  }
  // 查询角色权限
  queryRolesPermission(id) {
    return this.$http.get(`/admin/role/permission/all/${id}`);
  }
  // 绑定管理员角色 重置
  resetUserRole(data) {
    return this.$http.post("/admin/admin_user/role/reset/all", data);
  }
  // 查询管理员绑定角色
  queryAdminRoles(id) {
    return this.$http.get(`/admin/admin_user/role/all/${id}`);
  }
  // 公司设置经理查询列表
  companyManagerList(params) {
    return this.$http.get(
      `/admin/company/manager/search?company_id=${params.id}`
    );
  }
  //创建公司经理
  createCompanyManager(data) {
    return this.$http.post("/admin/company/manager/create", data);
  }
  // 修改公司经理
  updataCompanyManager(data) {
    return this.$http.post("/admin/company/manager/update", data);
  }
  // 删除公司经理
  deleteCompanyManager(id) {
    return this.$http.get(`/admin/company/manager/delete/${id}`);
  }
  // 搜索门店列表
  queryStoreList(pid) {
    return this.$http.get(`/admin/company/search?per_page=100&pid=${pid}`);
  }
  // 获取门店绑定经理列表
  getManagerStore(params) {
    return this.$http.get(
      `/admin/company/manager/store/search?company_id=${params.id}`,
      params
    );
  }
  // 区域经理绑定门店
  managerBindStore(data) {
    return this.$http.post("/admin/company/manager/store/create", data);
  }
  // 取消区域经理绑定门店
  cancelManagerStore(id) {
    return this.$http.get(`/admin/company/manager/store/delete/${id}`);
  }
  // 获取销售公司（总公司）列表
  getSaleCompanyList(per_page) {
    return this.$http.get(
      `/admin/company/search?store_category=1&per_page=${per_page}`
    );
  }
  // 获取楼盘配置
  getBuildSystem() {
    return this.$http.get("/admin/build/category/all/system");
  }
  // 创建楼盘标签
  createBuildLabel(data) {
    return this.$http.post("/admin/build/category/create", data);
  }
  // 修改楼盘标签
  updateBuildLabel(data) {
    return this.$http.post("/admin/build/category/update", data);
  }
  // 删除标签
  deleteBuildLabel(id) {
    return this.$http.get(`/admin/build/category/delete/${id}`);
  }
  // 获取自定义楼盘数据
  gteBuildCategory(params) {
    return this.$http.get("/admin/build/category/search", params);
  }
  // 获取设置敏感词列表
  getSensitiveWords(params) {
    return this.$http.get("/admin/sensitive_words/search", params);
  }
  // 创建敏感词
  createSensitiveWords(data) {
    return this.$http.post("/admin/sensitive_words/create", data);
  }
  // 修改敏感词
  updataSensitiveWords(data) {
    return this.$http.post("/admin/sensitive_words/update", data);
  }
  // 删除
  deleteSensitiveWords(id) {
    return this.$http.get(`/admin/sensitive_words/delete/${id}`);
  }
  // 查询所有字典数据
  queryDicData(website_id) {
    return this.$http.get(`/common/dictionary/all?website_id=${website_id}`);
  }
  // 获取楼号
  getFloorList(params) {
    return this.$http.get(`/admin/build/no/search`, params);
  }
  // 创建楼号数据
  createFloorData(data) {
    return this.$http.post("/admin/build/no/create", data);
  }
  //删除楼号
  deleteFloorData(id) {
    return this.$http.get(`/admin/build/no/delete/${id}`);
  }
  // 修改楼号数据
  updataFloorData(data) {
    return this.$http.post("/admin/build/no/update", data);
  }
  // 添加户型
  bindHouseForFloor(data) {
    return this.$http.post("/admin/build/no/house_type/create", data);
  }
  // 获取楼号绑定户型
  getFloorForHouse() {
    return this.$http.get(`/admin/build/no/house_type/search`);
  }
  // 删除楼号绑定户型
  deleteFloorForHouse(id) {
    return this.$http.get(`/admin/build/no/house_type/delete/${id}`);
  }
  // 获取问题反馈列表
  getFeedbackList(params) {
    return this.$http.get("/admin/issue_feedback/search", params);
  }
  // 提交利率
  createLoanForm(data) {
    return this.$http.post("/admin/loan_interest_rate/put", data);
  }
  // 获取利率
  getLoan(website_id) {
    return this.$http.get(`/admin/loan_interest_rate/query/${website_id}`);
  }
  // 获取楼盘状态列表
  getBuildStatus() {
    return this.$http.get("/admin/build/status_category/all/system");
  }
  // 获取楼盘自定义列表
  getZiBuildStatus(params) {
    return this.$http.get("/admin/build/status_category/search", params);
  }
  // 创建楼盘自定义
  createBuildSatus(data) {
    return this.$http.post("/admin/build/status_category/create", data);
  }
  // 修建楼盘自定义状态
  updataBuildStatus(data) {
    return this.$http.post("/admin/build/status_category/update", data);
  }
  // 删除楼盘自定义状态
  deleteBuildStatus(id) {
    return this.$http.get(`/admin/build/status_category/delete/${id}`);
  }
  // 获取客户自定义榜单内容
  getCustomizeRanking(params) {
    return this.$http.get("/admin/build/rank_list/search", params);
  }
  // 提交榜单信息
  createCustomizeRanking(data) {
    return this.$http.post("/admin/build/rank_list/create", data);
  }
  // 修改榜单信息
  updateCustomizeRanking(data) {
    return this.$http.post("/admin/build/rank_list/update", data);
  }
  // 删除榜单数据
  deleteCustomizeRanking(id) {
    return this.$http.get(`/admin/build/rank_list/delete/${id}`);
  }
  // 已绑定楼盘榜单
  getRankingBindBuild(params) {
    return this.$http.get("/admin/build/rank_list/bind/search", params);
  }
  // 给楼盘绑定榜单
  createRankingForBuild(data) {
    return this.$http.post("/admin/build/rank_list/bind/create", data);
  }
  // 解除楼盘绑定榜单
  cancelRankingForBuild(id) {
    return this.$http.get(`/admin/build/rank_list/bind/delete/${id}`);
  }
  // 获取字典默认榜单
  getDefaultRanking() {
    return this.$http.get(
      `/common/build/rank_list/all/system?website_id=${localStorage.getItem(
        "website_id"
      )}`
    );
  }
  // 获取小程序二维码
  getAppQrcode(data) {
    return this.$http.post(
      "/admin/wx_open/mini_program/jump_qrcode/get/wxa/code",
      data,
      { responseType: "arraybuffer" }
    );
  }
  // 获取小程序私有配置
  getMiniProgramPrivateConfig(website_id) {
    return this.$http.get(`/common/website/private_config/${website_id}`);
  }
  // 获取广告类型列表
  getAdvTypeList() {
    return this.$http.get(
      `/common/banner_position/all?website_id=${localStorage.getItem(
        "website_id"
      )}`
    );
  }
  // 查询免责声明
  getDisclaimerData(category) {
    return this.$http.get(`/admin/disclaimer/query/category/${category}`);
  }
  // 获取评论审核列表
  getCommentReviewList(params) {
    return this.$http.get("/admin/build/comment/search", params);
  }
  // 评论审核批量
  commentReview(data) {
    return this.$http.post("/admin/build/comment/audit", data);
  }
  // 获取预售列表
  getPresellList(params) {
    return this.$http.get("/admin/build/presell/search", params);
  }
  // 创建预售信息
  createPresellData(data) {
    return this.$http.post("/admin/build/presell/create", data);
  }
  // 修改预售信息
  updatePresellData(data) {
    return this.$http.post("/admin/build/presell/update", data);
  }
  // 删除预售信息
  deletePresellData(id) {
    return this.$http.get(`/admin/build/presell/delete/${id}`);
  }
  // 创建自定义导航
  createCustomNavLink(data) {
    return this.$http.post("/admin/custom_nav_link/create", data);
  }
  // 修改自定义导航
  updateCustomNavLink(data) {
    return this.$http.post("/admin/custom_nav_link/update", data);
  }
  // 删除自定义导航
  deleteCustomNavLink(id) {
    return this.$http.get(`/admin/custom_nav_link/delete/${id}`);
  }
  // 获取列表
  getCustomNavLink(params) {
    return this.$http.get("/admin/custom_nav_link/search", params);
  }
  // 获取总控自定义
  getCustomNavLinkSystem(params) {
    return this.$http.get("/admin/custom_nav_link/search/system", params);
  }
  // 创建自定义价格
  createPriceRange(data) {
    return this.$http.post("/admin/build/price_range/create", data);
  }
  // 修改自定义价格
  updatePriceRange(data) {
    return this.$http.post("/admin/build/price_range/update", data);
  }
  // 获取自定义价格列表数据
  getPriceRange(params) {
    return this.$http.get("/admin/build/price_range/search", params);
  }
  // 删除自定义价格列表
  deletePriceRange(id) {
    return this.$http.get(`/admin/build/price_range/delete/${id}`);
  }
  // 获取总控自定义价格
  getPriceRangeSystem(params) {
    return this.$http.get("/admin/build/price_range/search/system", params);
  }
  // 订单状态修改
  updateOrderStatus(data) {
    return this.$http.post("/admin/sale_order/update/confirm_status", data);
  }
  // 获取账单列表
  getBillList(params) {
    return this.$http.get("/admin/sale_order/bill/search", params);
  }

  // 提交应付金额数据
  createDisburseData(data) {
    return this.$http.post("/admin/sale_order/bill/disburse", data);
  }
  // 提交应收金额数据
  createEarningData(data) {
    return this.$http.post("/admin/sale_order/bill/earning", data);
  }
  // 获取付款记录列表
  getPaymentRecordsList(params) {
    return this.$http.get(
      "/admin/sale_order/bill/brokerage/payment/record/search",
      params
    );
  }
  // 获取开票列表
  getInvoiceList(params) {
    return this.$http.get("/admin/sale_order/bill/invoice/search", params);
  }
  // 提交开票记录
  createInvoiceData(data) {
    return this.$http.post("/admin/sale_order/bill/invoice/create", data);
  }
  // 回退开票记录（取消）
  cancelInvoiceRecording(data) {
    return this.$http.post("/admin/sale_order/bill/invoice/return/make", data);
  }
  // 回退付款记录（取消）
  cancelPaymentRecording(data) {
    return this.$http.post("/admin/sale_order/bill/return/payment", data);
  }
  // 创建批次记录
  createBatchData(data) {
    return this.$http.post("/admin/sale_order/bill/batch/create", data);
  }
  // 获取批次记录列表
  getBatchData(params) {
    return this.$http.get("/admin/sale_order/bill/batch/search", params);
  }
  // 创建批次中应付金额
  createBatchDisburseData(data) {
    return this.$http.post("/admin/sale_order/bill/batch/disburse", data);
  }
  // 创建批次中应收金额
  createBatchEarningData(data) {
    return this.$http.post("/admin/sale_order/bill/batch/earning", data);
  }
  // 批次记录取消
  cancelBatchData(data) {
    return this.$http.post("/admin/sale_order/bill/batch/return/payment", data);
  }
  // 创建开票的批次记录
  createInvoiceBatchData(data) {
    return this.$http.post("/admin/sale_order/bill/invoice/batch/create", data);
  }
  // 获取开票批次列表数据
  getInvoiceBatchList(params) {
    return this.$http.get(
      "/admin/sale_order/bill/invoice/batch/search",
      params
    );
  }
  // 取消开票批次数据
  cancelInvoiceBatchData(data) {
    return this.$http.post(
      "/admin/sale_order/bill/invoice/batch/return/make",
      data
    );
  }
  // 佣金结算列表数据
  saleSettlementList(params) {
    return this.$http.get(`/admin/sale_order/search`, params);
  }
  // 批次付款记录列表
  getBatchPayList(params) {
    return this.$http.get(
      `/admin/sale_order/bill/brokerage/payment/batch/record/search`,
      params
    );
  }
  // 授权企业微信
  bindQyWx(data) {
    return this.$http.post("/admin/wx_work/auth/get/link/app/auth/3rd", data);
  }
  // 授权企业微信
  sendCodeQyWx(data) {
    return this.$http.post("/admin/wx_work/auth/app_auth/create", data);
  }
  // 企业微信授权  新
  sendCodeQyWxNew(data) {
    return this.$http.post("/common/wx_work/bindWebsiteT", data);
  }
  // 获取直播记录列表
  getLiveList(params) {
    return this.$http.get("/admin/wx_work/living/search", params);
  }
  // 获取最后一次直播数据
  getLastLiveList() {
    return this.$http.get("/admin/wx_work/living/get/last");
  }
  // 删除重播记录
  deleteReplayLive(data) {
    return this.$http.post("/admin/wx_work/living/delete_replay_data", data);
  }
  // 取消直播（预约）
  cancelLiving(data) {
    return this.$http.post("/admin/wx_work/living/cancel", data);
  }
  // 删除直播记录
  deleteLiving(id) {
    return this.$http.get(`/admin/wx_work/living/delete/${id}`);
  }
  // 创建直播
  craeteLiving(data) {
    return this.$http.post("/admin/wx_work/living/create", data);
  }
  // 修改直播内容
  updataLiving(data) {
    return this.$http.post("/admin/wx_work/living/update", data);
  }
  // 上传素材
  uploadImg(data) {
    return this.$http.post("/admin/wx_agent/media/upload_img", data);
  }
  // 微信客服账号添加
  accountAdd(data) {
    return this.$http.post("/admin/wx_agent/kf/account_add", data);
  }
  // 微信客服账号修改
  accountEdit(data) {
    return this.$http.post("/admin/wx_agent/kf/account_edit", data);
  }
  // 微信客服账号删除
  accountDel(data) {
    return this.$http.post("/admin/wx_agent/kf/account_del", data);
  }
  // 接待人员添加或删除
  servicerAddOrDel(data) {
    return this.$http.post("/admin/wx_agent/kf/servicer_add_or_del", data);
  }
  // 上传临时素材
  upload(data) {
    return this.$http.post("/admin/wx_agent/media/upload", data);
  }
  // 欢迎语编辑
  welcomeEdit(data) {
    return this.$http.post("/admin/wx_agent/kf/welcome_edit", data);
  }
  // 创建部门
  departmentCreate(data) {
    return this.$http.post("/admin/wx_agent/contacts/department_create", data);
  }
  // 获取企业微信用户列表
  getQywxUsersList() {
    return this.$http.get(
      "/admin/wx_work/user/simple/list?department_id=1&fetch_child=1"
    );
  }
  // 上传临时素材返回media_id
  getUploadMediaId(data) {
    return this.$http.post("/admin/wx_work/material/upload", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 根据media_id获取图片内容
  getImgForMediaId(media_id) {
    return this.$http.get(`/admin/wx_work/material/get?media_id=${media_id}`, {
      responseType: "arraybuffer",
    });
  }
  //导出excel表格（销售公司）
  exportExcelForSale(params) {
    return this.$http.get("/admin/company/export/search", params);
  }
  getCommonProjectList(params) {
    // 获取用户管理多选项目筛选列表
    return this.$http.get("/admin/user/searchProjectByName", params);
  }
  BatchReportCustomers(data) {
    // 批量报备客户
    return this.$http.post("/admin/customer/reported/import", data);
  }
  deleteReportInfo(id) {
    // 删除用户下的报备信息
    return this.$http.get(`/admin/user/deleteCustomerReport/${id}`);
  }
  deleteUserInfo(id) {
    // 删除用户信息
    return this.$http.get(`/admin/user/delete/${id}`);
  }
  setProjectManagerSort(data) {
    // 设置项目助理排序
    return this.$http.post("/admin/project/manager/update", data);
  }
  getIsCaseManagerData(params) {
    // 获取置业顾问
    return this.$http.get("/common/user/broker/search", params);
  }
  getWebsiteReportInfo() {
    // 获取系统报备信息
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  getWebsiteBuildRank() {
    // 获取系统楼盘排行榜
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  getWebsiteUserRank() {
    // 获取业绩排行榜
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  getWebsiteMoneyInfo() {
    // 获取系统成交佣金数据
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  getWebsiteIndexCustom() {
    // 获取系统首页样式配置
    return this.$http.get("/admin/website_index_custom/get");
  }
  setWebsiteIndexCustom(data) {
    // 设置系统首页样式配置
    return this.$http.post("/admin/website_index_custom/put", data);
  }
  getWxWorkAppId() {
    // 获取企业微信appid
    return this.$http.get("/common/wx_work/getAppIdT");
  }
  getWxworkByCode(appid) {
    // 通过企业微信appid
    return this.$http.get(`/common/wx_work/getAdminUserInfo?code=${appid}`);
  }
  setWxworkBycodeT(code) {
    // t+系统授权
    // return this.$http.get(`/common/wx_work/getAdminUserInfoT?code=${code}`);
    return this.$http.get(`/common/qywx/thirdparty_t/login?code=${code}`);
  }
  // 企业微信第三方获取回调地址
  getWxworkByCodeV2(params) {
    return this.$http.get(`/common/qywx/fxbb/authorize`, params);
  }
  // 获取回调地址的code获取token
  // 登录
  setWxworkBycodeV2(code) {
    // return this.$http.get(`/common/qywx/fxbb/login?code=${code}`);
    return this.$http.get(`/common/qywx/thirdparty/login?code=${code}`);
  }
  // 开启关闭项目助理显示
  setProjectManagerDisplay(data) {
    // 开启关闭项目助理显示
    return this.$http.post("/admin/project/manager/display", data);
  }
  // 系统后台销售公司业绩排行
  setAdminCompanyRank() {
    return this.$http.get("/admin/statistics/home/<USER>");
  }
  // 系统楼盘列表编辑排序 ，均价，售楼热线
  setBuildSimpleData(data) {
    return this.$http.post("/admin/build/simple/updateListRow", data);
  }
  // 公司后台=》系统楼盘列表编辑排序 ，均价，售楼热线
  companySetBuildSimpleData(data) {
    return this.$http.post("/enterprise/build/updateListRow", data);
  }
  // 获取微信短信开关配置
  getMshSwitchData() {
    return this.$http.get("/admin/sys_conf/msg_switch/get");
  }
  // 修改系统微信开关配置
  setMsgSwitchData(data) {
    return this.$http.post("/admin/sys_conf/msg_switch/update", data);
  }
  // 获取系统短信数量接口
  getSiteSmsAmountData() {
    return this.$http.get("/admin/sys_conf/ali_sms/get_sms_data");
  }
  // 获取系统短信数量接口新
  getSiteSmsAmountDataNew() {
    return this.$http.get("/admin/sys_conf/ali_sms/get_marketing_sms_data");
  }
  // 获取移动端banner
  getMobileBannerData(params) {
    return this.$http.get("/common/banner/website/all/1", params);
  }
  // 获取移动端列表
  getMobileListData(params) {
    return this.$http.get("/common/project/list?home_recommend=1", params);
  }
  // 根据系统id获取楼盘id
  getBuildByWebsiteId(id) {
    return this.$http.get(`/common/build/query/first?website_id=${id}`);
  }
  // 获取楼盘 信息
  getBuildInfoByid(id) {
    return this.$http.get(`/common/project/query/build/${id}`);
  }
  // 查询楼盘浏览数
  queryViewUser(id) {
    return this.$http.get(`/common/build/view_user/search?build_id=${id}`);
  }
  // 获取楼盘相册
  getBuildPhotosData(id) {
    return this.$http.get(`/common/build/img/${id}`);
  }
  // 获取楼盘动态
  getBuildDynamicData(params) {
    return this.$http.get("/common/news/list", params);
  }
  // 快速设置案场方（置业顾问）
  setIsCaseForUser(data) {
    return this.$http.post("/admin/user/update/user_is_cass", data);
  }
  // 设置楼盘置业顾问
  // setestatepurchase
  setestatepurchase(params) {
    return this.$http.post("/admin/user/update/user_build_consultant", params)
  }
  // crm
  // 我的客户
  getCrmMycustomerData(params) {
    return this.$http.get("/admin/crm/client/my", params);
  }
  // 公海客户
  getCrmCommoncustomerData(params) {
    return this.$http.get("/admin/crm/client/high_seas", params);
  }
  // 所有客户
  getCrmAllCustomerData(params) {
    return this.$http.get("/admin/crm/client/all", params);
  }
  // 获取客户等级
  getCrmCustomerLevel(params) {
    return this.$http.get("/admin/crm/level/search", params);
  }
  // 编辑客户等级
  editCrmCustomerLevel(data) {
    return this.$http.post("admin/crm/level/update", data);
  }
  // 获取客户状态
  getCrmCustomerStatus(params) {
    return this.$http.get("/admin/crm/tracking/search", params);
  }
  // 更新客户状态
  updateCrmCustomerStatus(data) {
    return this.$http.post("/admin/crm/tracking/update", data);
  }
  // 获取客户跟进状态不分页
  getCrmCustomerFollowInfo(params) {
    return this.$http.get("/admin/crm/tracking/list", params);
  }
  //获取T+助手配置
  getTassistant() {
    return this.$http.get("/common/dy/config");
  }
  //设置T+助手配置
  setTassistant(params) {
    return this.$http.post("/common/dy/config", params);
  }
  // 获取客户未跟进客户数量
  getCrmCustomerFollowNumber() {
    return this.$http.get("/admin/crm/client/get_notfollow_num");
  }
  //获取客户来源(新，分页)
  customersourcenew(param) {
    return this.$http.get("/admin/crm/sources/search", param)
  }
  //自定义添加客户来源
  addcustomersource(params) {
    return this.$http.post("/admin/crm/sources/create", params)
  }
  //编辑客户来源
  newmodifycustomer(params) {
    return this.$http.post("/admin/crm/source/update", params)
  }
  //删除客户来源
  newdelCustomSource(id) {
    return this.$http.get(`/admin/crm/source/delete/${id}`,)
  }
  //获取客户来源(新，分页)
  listcustomersourcenew() {
    return this.$http.get("/admin/crm/sources/list")
  }
  // 获取客户来源
  getCrmCustomerFrom(params) {
    return this.$http.get("/admin/crm/source/search", params);
  }
  //修改客户来源
  Modifycustomersource(parmas) {
    return this.$http.post("/admin/crm/source/update", parmas)
  }
  //添加自定义客户来源
  addCustomSource(parmas) {
    return this.$http.post("/admin/crm/source/create", parmas)
  }
  //删除客户来源
  delCustomSource(id) {
    return this.$http.get(`/admin/crm/source/delete/${id}`)
  }
  // 获取客户自定义列表
  getCrmCustomerSettingData(params) {
    return this.$http.get("/admin/crm/client_column_field/search", params);
  }
  // 获取字段类型
  getFieldTypeData() {
    return this.$http.get("/admin/crm/common/get_field_type");
  }
  // 新增客户字段类型
  setFieldTypeData(data) {
    return this.$http.post("/admin/crm/client_column_field/create", data);
  }
  // 编辑客户字段类型
  editFieldTypeData(data) {
    return this.$http.post("/admin/crm/client_column_field/update", data);
  }
  // 删除客户字段类型
  deleteFieldTypeData(id) {
    return this.$http.get(`/admin/crm/client_column_field/delete/${id}`);
  }
  // 获取公司自定义列表
  getCrmCompanySettingData(params) {
    return this.$http.get("/admin/crm/company_column_field/search", params);
  }
  // 新增客户自定义字段
  setFieldCompanyTypeData(data) {
    return this.$http.post("/admin/crm/company_column_field/create", data);
  }
  // 编辑客户公司自定义列表
  editFieldCompanyTypeData(data) {
    return this.$http.post("/admin/crm/company_column_field/update", data);
  }
  // 删除客户公司自定义列表
  deleteFieldCompanyData(id) {
    return this.$http.get(`/admin/crm/company_column_field/delete/${id}`);
  }
  // 获取标签组列表
  getLabelsGroupData(params) {
    return this.$http.get("/admin/crm/label/search", params);
  }
  // 获取标签列表不分页
  getLabelGroupNoPage() {
    // return this.$http.get("/admin/crm/info/label_list");
    return this.$http.get("/admin/crm/label/list");
  }
  // 获取标签列表不分页 新
  getLabelGroupNoPageNew() {
    // return this.$http.get("/admin/crm/info/label_list");
    return this.$http.get("/admin/crm/label/all");
  }
  // 批量更新客户标签
  batchSetLabelGroup(data) {
    return this.$http.post("/admin/crm/client/batch_update_tag", data);
  }
  //批量更新客户标签添加追加覆盖选项
  newbatchSetLabelGroup(data){
    return this.$http.post("admin/crm/client/batch_update_tags", data)
  }
  // 新增标签组
  setLabelsGroupData(data) {
    return this.$http.post("/admin/crm/label/create", data);
  }
  // 编辑标签组
  editLabelsGroupData(data) {
    return this.$http.post("/admin/crm/label/update", data);
  }
  // 删除标签组
  deleteLabelsGroupData(data) {
    return this.$http.post("/admin/crm/label/del", data);
  }
  // 获取标签列表
  getLabelsList(params) {
    return this.$http.get("/admin/crm/label/search", params);
  }
  setLabelsList(data) {
    return this.$http.post("/admin/crm/label/create", data);
  }
  editLabelsList(data) {
    return this.$http.post("/admin/crm/label/update", data);
  }
  deleteLabels(data) {
    return this.$http.post("/admin/crm/label/del", data);
  }
  // 获取客户添加字段
  getCrmGetClientField(params) {
    return this.$http.get("/admin/crm/common/get_client_field", params);
  }
  // 添加客户
  setCrmCustomerData(data) {
    return this.$http.post("/admin/crm/client/add", data);
  }
  // 获取公海客户
  getCrmCustomerPublick(data) {
    // return this.$http.post("/admin/crm/client/get_client", data);
    return this.$http.post("/admin/crm/client/get", data);
  }
  // crm客户详情
  getCrmCustomerDetail(id) {
    return this.$http.get(`/admin/crm/info/client_info/${id}`);
  }
  //获取AI分析关键词，打开AI分析侧边栏
  getCrmCustomerKeywords(id) {
    return this.$http.get(`/admin/crm/client_follow/analysis_keyWord?client_id=${id}`);
  }
  // crm客户维护
  getCrmCustomerFollowData(params) {
    // return this.$http.get("/admin/crm/info/follow_list", params);
    // return this.$http.get("/admin/crm/follow/search", params);
    return this.$http.get("/admin/crm/client_follow/search", { params });
  }
  // 更新客户等级
  setCrmCustomerLevelData(data) {
    // return this.$http.post("/admin/crm/info/update_level", data);
    return this.$http.post("/admin/crm/client/update_level", data);
  }
  // 获取客访轨迹
  getCrmCustomerbehavior(params) {
    return this.$http.get("/admin/crm/info/behavior_list", params);
  } // 修改客户跟进状态
  setCrmCustomerFollowStatusv1(data) {
    // return this.$http.post("/admin/crm/info/update_tracking", data);
    return this.$http.post("/admin/crm/client/update_tracking", data);
  }
  // 修改客户跟进状态
  setCrmCustomerFollowStatus(data) {
    // return this.$http.post("/admin/crm/info/update_tracking", data);
    // return this.$http.post("/admin/crm/client/update_tracking", data);
    return this.$http.post("/admin/crm/client/create_client_approver", data);
  }

  // 修改客户跟进状态房源审批
  setCrmHouseFollowStatus(data) {
    // return this.$http.post("/admin/crm/house/examine_create", data);
    return this.$http.post("/admin/crm/client/create_house_approver", data);
  }
  // 修改审批内容
  onAgreeAuditData(data) {
    return this.$http.post("/admin/crm/examine/agree", data);
  }
  // 审批失败
  onRefuseAuditData(data) {
    return this.$http.post("/admin/crm/examine/refuse", data);
  }
  // 转让客户
  setCrmCustomerZhuanrang(data) {
    return this.$http.post("/admin/crm/client/transfer", data);
  }
  //复制crm客户到同事的流转客
  setCrmCustomercopy(params){
    return this.$http.post("/admin/private_client/copy_to_colleague_from_crm", params)
  }
  //经营视图批量删除(只有筛选潜在客户+无效客户状态显示批量删除)
  batchdeletecustomer(params){
    return this.$http.post("/admin/crm/client/delete_all",params)
  }
  // 获取客户跟进状态
  getCrmCustomerFollowStatus() {
    // return this.$http.get("/admin/crm/action/get_follow_status");
    return this.$http.get("/admin/crm/follow/list");
  }
  // 添加代办事项/添加提醒
  setCrmCustomerRemind(data) {
    // return this.$http.post("/admin/crm/action/add_dispose", data);
    return this.$http.post("/admin/crm/client_remind/create", data);
  }
  // 编辑客户资料
  editCrmCustomerData(data) {
    return this.$http.post("/admin/crm/info/update", data);
  }
  // 更新客户标签
  setCrmCustomerLabelsData(data) {
    // return this.$http.post("/admin/crm/info/update_label", data);
    return this.$http.post("/admin/crm/client/update_tag", data);
  }
  // 获取企业微信用户
  getWxWorkUserData(params) {
    // return this.$http.get("/admin/crm/info/wxqy_list", params);
    // return this.$http.get("/admin/crm/qywx/client/search", params);
    return this.$http.get("/admin/crm/client/qw_search", params);
  }
  // 绑定企业微信用户
  setCrmCustomerBindWxUser(data) {
    // return this.$http.post("/admin/crm/info/bind", data);
    return this.$http.post("/admin/crm/client/bind", data);
  }
  // 获取crm统计数据
  getCrmCustomerStatisticsData() {
    return this.$http.get("/admin/crm/client/statistics");
  }
  // 添加跟进记录
  setCrmCustomerFollowData(data) {
    // return this.$http.post("/admin/crm/action/add_follow", data);
    return this.$http.post("/admin/crm/client_follow/tel_follow", data);
  }
  //获取站点绑定的线路
  getsiteline() {
    return this.$http.get("/admin/call_clue/website_call_routes");
  }
  setCrmCustomerTelFollowData(data) {
    // return this.$http.post("/admin/crm/action/add_follow", data);
    return this.$http.post("/admin/crm/client_follow/create", data);
  }
  //  获取财务列表财务统计
  getCrmCustomerFinancial(params) {
    return this.$http.get("/admin/crm/finance/census", params);
  }
  // 新增财务报告
  createFinancialData(data) {
    return this.$http.post("/admin/crm/finance/create", data);
  }
  // 编辑财务报告
  editFinancialData(data) {
    return this.$http.post("/admin/crm/finance/update", data);
  }
  // 删除财务报告
  deleterFinancialData(id) {
    return this.$http.get(`/admin/crm/finance/delete/${id}`);
  }
  // 获取财务列表数据
  getCrmCustomerFinancialList(params) {
    return this.$http.get("/admin/crm/finance/search", params);
  }
  // 离职继承
  setPersonnelMattersData(data) {
    return this.$http.post("/admin/personnelMatters/inheritMember", data, { timeout: 300000 });
  }
  /*
    在职继承 
    POST /api/admin/personnelMatters/inheritMemberOnJob {
    from_id : 被继承会员id
    to_id : 继承会员id
  */
  setPersonnelMattersDataZaizhi(data) {
    return this.$http.post("/admin/personnelMatters/inheritMemberOnJob", data, { timeout: 300000 });
  }
  // 快速更改成员状态
  setFastmemberStatus(id) {
    return this.$http.get(`admin/personnelMatters/update_status/${id}`);
  }
  // 获取离职人员列表
  getResignedUsersData() {
    return this.$http.get("/admin/personnelMatters/getResignedUsers");
  }
  // 获取批量添加好友列表
  getFriendMemberData(params) {
    return this.$http.get("/admin/personnelMatters/friendMember", params);
  }
  // 获取审批详情
  getAuditDetail(id) {
    // return this.$http.get(`/admin/crm/examine/info/${id}`);
    return this.$http.get(`/admin/crm/finance/info/${id}`);
  }
  // 提交成交审批数据
  setCrmCustomerDealData(data) {
    return this.$http.post("/admin/crm/deal/commission", data);
  }
  // 客户线索->操作日志
  getCrmCustomerLogList(params) {
    // return this.$http.get("/admin/crm/info/log_list", params);
    return this.$http.get("/admin/crm/client_clue/search", params);
  }
  // 获取客户话术库
  getCrmCustomerTalkData(params) {
    return this.$http.get("/admin/crm/qywx/words/search", params);
  }
  // 新增话术
  setCrmCustomerTalkData(data) {
    return this.$http.post("/admin/crm/qywx/words/create", data);
  }
  // 编辑话术
  editCrmCustomerTalkData(data) {
    return this.$http.post("/admin/crm/qywx/words/update", data);
  }
  // 删除话术
  deleteCrmCustomerTalkData(id) {
    return this.$http.get(`/admin/crm/qywx/words/delete/${id}`);
  }

  //话术库模块
  scriptlibrarymodule() {
    return this.$http.get("/admin/words_library/cate_modules")
  }
  //话术库分类
  scriptLibraryClassification(id) {
    return this.$http.get(`/admin/words_library/words_cate?module=${id}`)
  }
  // 添加话术库分类
  addscriptlibrary(parmas) {
    return this.$http.post(`/admin/words_library/add_words_cate`, parmas)
  }
  //编辑话术库分类
  editscriptlibrar(parmas) {
    return this.$http.post(`/admin/words_library/edit_words_cate`, parmas)
  }
  //删除话术库分类
  delscriptlibrar(id) {
    return this.$http.get(`/admin/words_library/del_words_cate?id=${id}`)
  }
  //获取话术库列表
  getscriptlibrarylist(param) {
    return this.$http.get(`/admin/words_library/words?cate_id=${param.cate_id}&keywords=${param.keywords}&page=${param.page}`)
  }
  // //获取话术库列表
  // getScriptlibrarylist(parmas) {
  //   return this.$http.get(`/admin/words_library/words`,{parmas})
  // }
  //添加话术(V2)
  addcontributeV2(parmas) {
    return this.$http.post("/admin/words_library/add_words", parmas)
  }
  //编辑话术(V2)
  editscriptV2(parmas) {
    return this.$http.post("/admin/words_library/edit_words", parmas)
  }
  //删除话术(V2)
  newdelscriptV2(id) {
    return this.$http.get(`/admin/words_library/del_words?id=${id}`)
  }
  //话术包(v2版本)
  getscriptpackV2(params){
    return this.$http.get(`/admin/words_library/words_package`, params )
  }
  //话术库管理列表
  speechLibraryManagerList(params) {
    return this.$http.get("/admin/words_library/words_package_manager", {params} )
  }
  //话术库管理-平台列表
  speechLibraryPlatformList(params) {
    return this.$http.get("/admin/words_library/words_package_manager_platform", {params} )
  }
  //话术库管理列表-无分页
  allSpeechLibraryManagerList(params) {
    return this.$http.get("/admin/words_library/words_package_manager_no_page", {params} )
  }
  //话术库管理列表-带平台-无分页(预览用)
  allSpeechLibraryPreviewList() {
    return this.$http.get("/admin/words_library/words_package_preview")
  }
  //设置话术库状态
  setSpeechLibraryStatus(data) {
    return this.$http.post("/admin/words_library/package_status", data)
  }
  //设置话术库排序
  setSpeechLibrarySort(data) {
    return this.$http.post("/admin/words_library/words_package_sort", data)
  }
  //话术库管理列表-无分页
  setSpeechLibraryBatchSort(data) {
    return this.$http.post("/admin/words_library/words_package_sort_batch", data)
  }
  //话术库云导入
  copySpeechLibrary(id) {
    return this.$http.get(`/admin/words_library/cloud_export_words?id=${id}`)
  }
  //导入话术库
  importSpeechLibrary(data) {
    return this.$http.post("/admin/words_library/export_words", null, {
      timeout: 300000,
      transformRequest: [function () {
        return data;
      }],
    })
  }
  //话术库分类排序
  setSpeechLibraryCateSort(data) {
    return this.$http.post("/admin/words_library/words_cate_sort_batch", data)
  }
  //话术列表-无分页
  allSpeechList(params) {
    return this.$http.get("/admin/words_library/words_no_page", {params})
  }
  //话术批量替换
  speechReplaceKeywords(data){
    return this.$http.post("/admin/words_library/package_replace", data)
  }
  //话术排序
  setSpeechItemSort(data) {
    return this.$http.post("/admin/words_library/words_sort_batch", data)
  }
  //获取话术库配置
  getSpeechLibraryConfig() {
    return this.$http.get("/admin/words_library/words_config")
  }
  //保存话术库配置
  saveSpeechLibraryConfig(data) {
    return this.$http.post("/admin/words_library/save_words_config", data)
  }
  //成员话术库权限
  getSpeechLibraryAdminAuth() {
    return this.$http.get("/admin/words_library/admin_user_auth")
  }
  //获取团队话术库数量
  getSpeechnumber(){
    return this.$http.get("/admin/words_library/check_team")
  }
  //添加话术库(v2版本)
  addscriptlibraryV2(parmas){
    return this.$http.post("/admin/words_library/add_package", parmas)
  }
  //编辑话术库(V2版本)
  setlibrarydataV2(parmas){
    return this.$http.post("/admin/words_library/edit_package", parmas)
  }
  //删除话术库(V2版本)
  deletlibraryV2(id){
    return this.$http.get(`/admin/words_library/del_package?id=${id}`)
  }
  //获取话术分类(V2版本)
  getscriptsortV2(id){
    return this.$http.get(`/admin/words_library/words_cate?package_id=${id}`)
  }
  //添加话术分类(V2版本)
  addscriptassortV2(parmas){
    return this.$http.post("/admin/words_library/add_words_cate", parmas)
  }
  //获取话术(V2版本)
  getscriptV2(params){
    return this.$http.get(`/admin/words_library/words`, { params })
  }

  // 获取系统crm配置
  getSiteCrmSetting() {
    return this.$http.get("/admin/crm/config/get_crm_config");
  }
  // 设置系统crm配置
  setSiteCrmSetting(data) {
    return this.$http.post("/admin/crm/config/update_crm_config", data);
  }
  //获取自定义配置(成员每日查看电话客户数量，每日最大领取数量，领取客户最大数量)
  getlookphonenumlist(id){
    return this.$http.get(`/admin/crm/config/get_diy_config?type=${id}`)
  }
  //创建自定义配置
  setlookphonenumlist(data){
    return this.$http.post("/admin/crm/config/create_diy_config", data)
  }
  //更新自定义配置
  updateLookPhoneNumList(data){
    return this.$http.post("/admin/crm/config/updates_diy_config", data)
  }
  //新掉公，获取配置条件
  newdiaogongcondition(){
    return this.$http.get("/admin/crm/config/recovery/get_fixed_where")
  }
  //添加新掉公配置
  addnewpublicconfiguration(data){
    return this.$http.post("/admin/crm/config/recovery/create", data)
  }
  //批量开启关闭(新掉公)
  bulkoperationpublic(data){
    return this.$http.post("/admin/crm/config/recovery/update_status", data)
  }
  //获取掉公流程日志
  getprocesslogpublic(params){
    return this.$http.get("/admin/crm/config/recovery/log", {params})
  }
  //新掉公流程表格
  newpublictable(params){
    return this.$http.get("/admin/crm/config/recovery/search", {params})
  }
  //编辑新掉公流程
  editnewpublic(data){
    return this.$http.post("/admin/crm/config/recovery/update", data)
  }
  //删除新掉公流程
  delnewpublic(id){
    return this.$http.get(`/admin/crm/config/recovery/delete/${id}`)
  }
  //掉公导入
  droppublicimport(){
    return this.$http.get("/admin/crm/config/recovery/import");
  }
  //获取配置项角色名称
  getsetuprolename(){
    return this.$http.get("/admin/crm/config/get_config_diy_name");
  }
  //明源 ： 明源云客是否开启
  setopenmingyuan(data){
    return this.$http.post("/admin/ming_yuan/ming_yuan_state", data);
  }
  //明源 ： 秘钥管理列表
  mykeymanagementlist(params){
    return this.$http.get("/admin/ming_yuan/configs",)
  }
  //明源 ： 添加秘钥
  addmingyuankey(params){
    return this.$http.post("/admin/ming_yuan/add_configs",params)
  }
  //明源 ： 编辑秘钥
  editmingyuankey(params){
    return this.$http.post("/admin/ming_yuan/edit_configs",params)
  }
  //明源 ： 设置默认秘钥
  setdefaultkeymy(params){
    return this.$http.post("/admin/ming_yuan/set_default",params)
  }
  //明源 ： 明源云项目管理
  myprojectmanagement(){
    return this.$http.get("/admin/ming_yuan/projects")
  }
  //明源 ： 系统项目列表
  mysystemproject(){
    return this.$http.get("/admin/ming_yuan/system_projects")
  }
  //明源 ： 明源云项目
  mingyuanyunproject(params){
    return this.$http.get("/admin/ming_yuan/init_projects", {params})
  }
  //明源 ： 明源项目绑定到系统项目上
  mybindingsystem(params){
    return this.$http.post("/admin/ming_yuan/bind_system_project", params)
  }
  //明源 ： 报备列表
  myreportinglist(params){
    return this.$http.get("/admin/ming_yuan/reports",{params})
  }
  // 文件库
  getCrmCustomerFileData(params) {
    return this.$http.get("/admin/crm/qywx/file/search", params);
  }
  // 获取客户群列表
  getCrmCustomerGroupData(params) {
    return this.$http.get("/admin/activityCode/groupChatList", params);
  }
  // 获取客户群列表添加编辑用
  getCrmCustomerGroupDataEdit(params) {
    return this.$http.get("/admin/activityCode/groupChatListSelf", params);
  }
  // 获取客户群详情
  getCrmCustomerGroupDeatail(id) {
    return this.$http.get(`/admin/activityCode/groupChatDetail/${id}`);
  }
  // 获取客户群成员列表
  getCrmCustomerGroupList(id, params) {
    return this.$http.get(`admin/activityCode/groupChatMembers/${id}`, {
      params,
    });
  }
  //获取创始人信息
  getfounderinfo() {
    return this.$http.get("/admin/crm/config/get_founder")
  }
  //获取流转客列表
  getCrmcustomerinformation(params) {
    return this.$http.get("/admin/private_client/search", params)
  }
  //添加流转客
  addCustomerinformation(params) {
    return this.$http.post("/admin/private_client/create", params)
  }
  //获取流转客客户详情
  getcustomerinfor(id) {
    return this.$http.get(`/admin/private_client/info/${id}`)
  }
  //更新流转客
  renewcustomerinfor(params) {
    return this.$http.post('/admin/private_client/update', params)
  }
  //流转客的查看电话
  informationlookphone(id) {
    return this.$http.get(`/admin/private_client/see_tel/${id}`)
  }
  //流转客添加置顶
  setinformationTop(id) {
    return this.$http.get(`/admin/private_client/top/${id}`);
  }
  //流转客手机号查询归属地
  lookphoneLocation(id) {
    return this.$http.get(`/admin/private_client/query_mobile_place/${id}`)
  }
  //流转客查看电话跟进
  lookphoneFollow(params) {
    return this.$http.post("/admin/private_client_follow/tel_follow", params)
  }
  //修改流转客，客户标签
  informationlable(params) {
    return this.$http.post("/admin/private_client/update_tag", params)
  }
  //批量修改流转客客户标签
  batchgetinformationlable(data) {
    return this.$http.post("/admin/private_client/batch_update_tag", data)
  }
  // //流转客客户详情
  // information_details(id, params){
  //   return this.$http.get(`/admin/private_client/info/${id}`, { params });
  // }
  //设置流转客客户备注
  setinformationRemarkData(data) {
    return this.$http.post("/admin/private_client/update_remark", data);
  }
  //更改流转客的详情的客户状态
  setinformationstatus(data) {
    return this.$http.post("/admin/private_client/update_tracking", data)
  }
  //更改流转客录入/维护/带看/成交人
  setinformationuser(data) {
    return this.$http.post("/admin/private_client/update_maintainer", data)
  }
  //更改流转客的客户等级
  setinformationlevel(data) {
    return this.$http.post("/admin/private_client/update_level", data)
  }
  //流转客外呼拨打电话
  informationoutphone(data) {
    return this.$http.post("/admin/call_clue/directCallPhone", data)
  }
  //流转客企业微信列表
  informationqwlist(params) {
    return this.$http.get("/admin/private_client/qw_search", params)
  }
  // 流转客获取客户企微信息
  informationgetQwInfo(id) {
    return this.$http.get(`/admin/private_client/qw_fixed_info/${id}`)
  }
  //流转客的企微绑定
  informationqwbind(data) {
    return this.$http.post("/admin/private_client/qw_bind", data)
  }
  //流转客的转公
  informationgiveupCustomer(data) {
    return this.$http.post("/admin/private_client/discard", data);
  }
  //流转客添加邀约跟进
  informationInvitationfollow() {
    return this.$http.post("/admin/private_client_follow/tel_follow")
  }
  //流转客普通跟进
  informationordinaryfollow(data) {
    return this.$http.post("/admin/private_client_follow/create", data)
  }
  //流转客添加带看记录
  informationTakelook(data) {
    return this.$http.post("/admin/private_client_follow/follow_take", data)
  }
  // 流转客客户跟进记录设为置顶
  setinformationFollowTextTop(id) {
    return this.$http.get(`/admin/private_client_follow/top/${id}`)
  }
  //流转客跟进记录
  informationfollowrecord(params) {
    return this.$http.get("/admin/private_client_follow/follow_search", params)
  }
  // 流转客获取预约带看列表
  getinformationlookList(params) {
    return this.$http.get("/admin/private_client_follow/follow_take_search", { params })
  }
  // 流转客获取客户详情-外呼/维护记录
  getinformationTelephoneRecord(params) {
    return this.$http.get("/admin/private_client_follow/search", { params });
  }
  //流转客添加提醒
  informationaddremind(data) {
    return this.$http.post("/admin/private_client/client_remind/create", data)
  }
  // 流转客-短信提醒跟进
  informationaddWarnFollow(data) {
    return this.$http.post("/admin/private_client/sms_remind", data);
  }
  //流转客合并客户列表
  informationmergelist(params) {
    return this.$http.get('/admin/private_client/merge_client_list', params)
  }
  //流转客合并客户
  informationmergeuser(data) {
    return this.$http.post("/admin/private_client/merge", data)
  }
  //流转客的导入客户
  informationimport(data) {
    return this.$http.post("/admin/private_client/excel/import/add_client", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    })
  }
  //流转客导入任务列表详情
  informationimportlist(id, params) {
    return this.$http.get(`/admin/private_client/excel/import/info_search/${id}`, { params })
  }
  //流转客导入任务列表
  newinformationimportlist(params) {
    return this.$http.get("/admin/private_client/excel/import/task_search", { params })
  }
  //流转客导出客户
  informationexport(params) {
    return this.$http.get("/admin/private_client/export_client", { params })
  }
  //流转客导出客户任务列表
  informationexportlist(params) {
    return this.$http.get("/admin/private_client/excel/export/task_search", { params })
  }
  //流转客导出客户任务列表详情
  newinformationexportlist(id, params) {
    return this.$http.get(`/admin/private_client/excel/export/info_search/${id}`, { params })
  }
  //流转客经营视图
  informationfollowlist(params) {
    return this.$http.get("/admin/private_client/news_search", params)
  }
  // 流转客删除
  informationdell(id) {
    return this.$http.get(`/admin/private_client/del_client/${id}`)
  }
  // 流转客转交到同事
  informationTransToMember(data) {
    return this.$http.post(`/admin/private_client/transfer`, data)
  }
  //流转客上一条下一条
  informationnext(data) {
    return this.$http.get(`/admin/private_client/one_search`, data)
  }
  //流转客流转视图上一条下一条
  determineinformationnext(data) {
    return this.$http.get(`/admin/private_client/one_search_jy`, data)
  }
  //流转客的外呼按钮显示与否
  informationoutbound() {
    return this.$http.get("/admin/private_client/call/verify")
  }
  //流转客转为私客
  converttoprivate(data) {
    return this.$http.post('/admin/private_client/to_crm_private_client', data)
  }
  //流转客复制
  Transferredcustomerscopy(params) {
    return this.$http.post("/admin/private_client/copy_to_colleague", params)
  }
  //流转客户详情用户画像客户线索
  informationcustomerleads(params) {
    return this.$http.get(`/admin/private_client_clue/search`, params)
  }
  // 流转客获取客户未跟进客户数量
  getinformationFollowNumber() {
    return this.$http.get("/admin/private_client/get_not_follow_num");
  }
  //流转客批量删除
  informationbatch_del(params) {
    return this.$http.post(`/admin/private_client/del`, params)
  }
  //流转客24小时新增客户
  informtionnewNumber() {
    return this.$http.get("/admin/private_client/get_client_num")
  }
  //流转客操作日志
  informationdailyrecord(params) {
    return this.$http.get("/admin/private_client/syn_log/search", { params })
  }
  //获取流转任务列表
  getTransfertasklist(params) {
    return this.$http.get("/admin/crm/auto_work/works", { params })
  }
  // 添加转交任务(批量加入流转)
  batchcirculation(params) {
    return this.$http.post("/admin/crm/auto_work/add_works", params, { timeout: 300000 })
  }
  // 添加转交任务(表格批量加入流转)
  tablebatchcirculation(data) {
    return this.$http.post("/admin/crm/auto_work/add_works", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    })
  }
  //直播导入表格
  livebroadcastimport(data){
    return this.$http.post("/admin/douyin/clue/upload", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    })
  }
  //设置转交任务的状态
  setbatchcirculationstatus(params) {
    return this.$http.post("/admin/crm/auto_work/save_work_status", params)
  }
  //删除流转任务
  delebatchcirculation(id) {
    return this.$http.get(`/admin/crm/auto_work/del_auto_works?auto_work_id=${id}`)
  }
  //添加流转任务的节点
  addwanderaboutnode(params) {
    return this.$http.post("/admin/crm/auto_work/add_work_points", params, { timeout: 300000 })
  }
  //编辑节点
  editwanderaboutnodes(params) {
    return this.$http.post("/admin/crm/auto_work/edit_work_points", params, { timeout: 300000 })
  }
  //获取节点列表
  getnodelist(id, params) {
    return this.$http.get(`/admin/crm/auto_work/work_points?auto_work_id=${id}`, { params })
  }
  //设置节点状态
  setnodestatus(params) {
    return this.$http.post("/admin/crm/auto_work/work_point_status", params)
  }
  //删除任务节点
  deletasknode(id) {
    return this.$http.get(`/admin/crm/auto_work/del_work_point?work_point_id=${id}`)
  }
  //获取任务流转可分配成员列表(排除当前成员以及禁用成员)
  getAutoWorkUsers() {
    return this.$http.get("/admin/crm/auto_work/assign_users")
  }
  //判断转交任务管理权限
  checkAutoWorkAuth(params) {
    return this.$http.get("/admin/crm/auto_work/check_auth", { params })
  }
  //取消转交任务
  cancelAutoWork(auto_work_id) {
    return this.$http.get("/admin/crm/auto_work/cancel_work?auto_work_id=" + auto_work_id)
  }
  //确认转交任务
  allowAutoWork(auto_work_id) {
    return this.$http.get("/admin/crm/auto_work/allow_work?auto_work_id=" + auto_work_id)
  }
  //导出客户
  leadingoutCustomers(params) {
    return this.$http.get("/admin/crm/client/export_client", { params });
  }
  newleadingoutCustomers(params) {
    return this.$http.get("/admin/crm/business_view/export", { params });
  }
  // 获取客户标签列表
  getCrmCustomerLabelsList(params) {
    return this.$http.get("/admin/crm/qywx/tag/search", params);
  }
  // 新增客户标签列表
  createCrmCustomerLabelsData(data) {
    return this.$http.post("/admin/crm/qywx/tag/create", data);
  }
  // 删除客户标签列表
  deleteCrmCustomerLabels(data) {
    return this.$http.post(`/admin/crm/qywx/tag/delete`, data);
  }
  // 获取crm客户列表
  getCrmCustomerClientList(params) {
    return this.$http.get("/admin/crm/client/search", params);
  }
  // 获取crm客户列表(新,经营视图)
  getCrmCustomerClientListNew(params) {
    return this.$http.get("/admin/crm/client/news_search", params);
  }
  // 获取crm客户列表(最新,经营视图)
  getCrmCustomerClientListlastNew(params) {
    return this.$http.get("/admin/crm/business_view/search", params)
  }
  //批量修改客户等级
  plcustomerlevels(params){
    return this.$http.post("/admin/crm/client/update_level_all", params)
  }
  //批量修改客户来源
  plcustomersource(params){
    return this.$http.post("/admin/crm/client/update_source_all", params)
  }
  //批量转交公海
  plzhuangong(params){
    return this.$http.post("/admin/crm/client/discard", params)
  }
  //批量修改角色
  plchangerole(params){
    return this.$http.post("/admin/crm/client/update_role_all", params)
  }
  //批量修改客户状态
  plmodifystate(params){
    return this.$http.post("/admin/crm/client/update_tracking_all", params)
  }
  //批量自动分配
  plbatchautomatic(params){
    return this.$http.post("/admin/crm/client/auto_allocation", params)
  }
  // 上传临时素材
  setCrmFileData(data) {
    return this.$http.post("/admin/crm/qywx/file/create", data);
  }
  // 获取客户等级列表
  getCrmCustomerLevelNopage() {
    return this.$http.get("/admin/crm/level/list");
  }
  // 客户渠道筛选
  getCrmCustomerSourceNopage() {
    return this.$http.get("/admin/crm/source/list");
  }
  // 设置客户备注
  setCrmCustomerRemarkData(data) {
    // return this.$http.post("/admin/crm/info/update_remarks", data);
    return this.$http.post("/admin/crm/client/update_remark", data);
  }
  // 放弃客户
  giveupCrmCustomerData(data) {
    return this.$http.post("/admin/crm/client/discard", data);
  }
  // 查询手机号归属地
  inquireHomeAddress(id) {
    return this.$http.get(`/admin/crm/client/query_mobile_place/${id}`);
  }
  // 合并客户
  mergeCrmCustomerData(data) {
    return this.$http.post("/admin/crm/info/merge", data);
  }
  // 2022/08/09  v2 版本crm客户
  setCrmCustomerDataV2(data) {
    return this.$http.post("/admin/crm/client/create", data);
  }
  //录入客户新接口
  newEntercustomer(data) {
    return this.$http.post("/admin/crm/client/creates", data);
  }
  //录入客户时验证
  verifyCrmCustomerCreate(mobile) {
    return this.$http.get("/admin/crm/client/verify_create?mobile="+mobile);
  }
  //编辑客户资料
  neweditcrminfromation(params) {
    return this.$http.post("/admin/crm/client/updates", params)
  }
  //客户导入
  uploadCrmCustomer(data) {
    return this.$http.post("/admin/crm/excel/import/add_client", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 获取客户详情
  getCrmCustomerDetailV2(id, params) {
    return this.$http.get(`/admin/crm/client/info/${id}`, { params });
  }
  // //上一条，下一条
  previousandNext(params) {
    return this.$http.get(`/admin/crm/client/one_search`, params)
  }
  // 客户详情，更改客户状态状态审批无需审核
  setCrmCustomerStatus(data) {
    return this.$http.post("/admin/crm/client/update_tracking", data);
  }
  // 更新客户详情
  updateCrmCustomerDataV2(data) {
    return this.$http.post("/admin/crm/client/update", data);
  }
  //获取省市区
  cities_and_provinces() {
    return this.$http.get("/admin/crm/region/list");
  }
  //获取省市区的最近记录
  regionalrecords(id) {
    return this.$http.get(`/admin/crm/client/get_city_log/${id}`);
  }
  // 查看客户电话
  setViewCrmCustomerTel(id) {
    // /${id}/admin/crm/client/look_tel
    return this.$http.get(`/admin/crm/client/see_tel/${id}`);
  }
  //判断外呼按钮是否显示
  call_crmshow(id) {
    return this.$http.get(`/admin/crm/client/verify_call/${id}`)
  }
  // 客户预约带看房源列表
  getAppointHouseList(params) {
    return this.$http.get("/admin/crm/client_follow/house_list", { params })
  }
  // 客户预约带看提交
  addAppointHouse(data) {
    return this.$http.post("/admin/crm/client_follow/follow_take", data)
  }
  // 获取预约带看列表
  getAppointAccompanyList(params) {
    return this.$http.get("/admin/crm/client_follow/follow_take_search", { params })
  }
  // 客户跟进记录设为置顶
  setFollowTextTop(id) {
    return this.$http.get(`/admin/crm/client_follow/top/${id}`)
  }
  // 客户跟进记录点赞
  addFollowPraise(id) {
    return this.$http.get(`/admin/crm/client_follow/click/${id}`)
  }
  // 客户跟进记录批注
  addFollowreply(data) {
    return this.$http.post(`/admin/crm/client_follow/reply`, data)
  }
  // 删除客户（无需审核）
  deleteFollowClient(id) {
    return this.$http.get(`/admin/crm/client/delete/${id}`)
  }
  //获取导入模板信息
  gettemplatedata() {
    return this.$http.get(`/admin/crm/excel/import/get_cate`)
  }
  // 导入企业微信文件
  uploadCrmCustomerData(data) {
    return this.$http.post("/admin/crm/client/new_import", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 导入企业微信文件
  uploadCrmCustomerDataURL(data) {
    return this.$http.post("/admin/crm/client/import", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 导入表格(新)
  uploadexportTable(data) {
    return this.$http.post("/admin/crm/client/import", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  //导入智能AI
  uploadCrmCustomerAI(data) {
    return this.$http.post("/admin/call_clue/robot/import_file", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }

  // 导入人事文件
  uploadCrmCustomerPersonnelData(data) {
    let url = "/admin/personnelMatters/import";
    return this.$http.post(url, data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 批量导入客户文件
  uploadCrmCustomerFriendData(data) {
    return this.$http.post("/admin/personnelMatters/importFriend", data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 获取员工活码数据
  getMemberCodeData(id) {
    return this.$http.get(`/admin/activityCode/userActivityCodeData/${id}`);
  }
  // 获取客户流失数据
  getCrmCustomerLossData(params) {
    return this.$http.get("/admin/wx_agent/external/loss_list", params);
  }
  // 获取客户详情客户列表
  getDetailClient(params) {
    return this.$http.get("/admin/crm/client/merge_client_list", params);
  }
  // 合并客户
  setMergeDetailClient(data) {
    return this.$http.post("/admin/crm/client/merge", data);
  }
  // 类型列表

  getCrmCustomerTypeData(params) {
    return this.$http.get("/admin/crm/type/search", params);
  }
  // 新增类型列表
  createCrmCustomerTypeData(data) {
    return this.$http.post("/admin/crm/type/create", data);
  }
  //  更新类型列表
  updateCrmCustomerTypeData(data) {
    return this.$http.post("/admin/crm/type/update", data);
  }
  // 类型列表不分页
  getCrmCustomerTypeDataNopage() {
    return this.$http.get("/admin/crm/type/list");
  }
  // 获取审批列表
  getCrmCustomerExamineData(params) {
    return this.$http.get("/admin/crm/examine/search", params);
  }
  // 设置无效客户
  setCrmCustomerInvalid(data) {
    return this.$http.post("/admin/crm/client/update_invalid", data);
  }
  // 开通crm
  openCrmData() {
    return this.$http.get("/admin/crm/config/open_crm");
  }
  // 拉取标签
  getCrmCustomerLabelsPull() {
    return this.$http.get("/admin/crm/qywx/tag/pull");
  }
  // //  获取crm首页数据
  // getCrmIndexData() {
  //   return this.$http.get("/admin/crm/home/<USER>");
  // }
  //获取crm中间数据(新)
  newgetCrmIndex() {
    return this.$http.get("/admin/crm/census/middle_census");
  }
  // 获取crm首页荣誉榜数据
  getCrmIndexHonorData(params) {
    return this.$http.get("/admin/crm/census/team_census", params);
  }
  // // 获取crm首页日历数据
  // getCrmIndexDateData(params) {
  //   return this.$http.get("/admin/crm/home/<USER>", params);
  // }
  //获取crm顶部统计(新)
  topgetCrmIndexDate() {
    return this.$http.get("/admin/crm/census/top_census")
  }
  //获取工作台数据是否可见
  Workbenchdata() {
    return this.$http.get("/admin/crm/census/verify_work_show")
  }
  //获取客户来源统计
  sourcestatistics(params) {
    return this.$http.get("/admin/crm/home/<USER>", params)
  }
  // 获取crm首页圆饼图数据
  getCrmIndexPieData() {
    return this.$http.get("/admin/crm/home/<USER>");
  }
  // 拉取企微客户
  getCrmWxworkPullData(params) {
    return this.$http.get(
      "/admin/wx_agent/external/batch_pull_external_custom",
      params
    );
  }
  // crm
  // 企业微信jssdk
  getWxWrokConfig(params) {
    // return this.$http.get("/common/wx_agent/jssdk/config_get", params);
    // return this.$http.get("/auth/qywx/jssdk", params);
    // return this.$http.get("/common/qywx/self/jssdk", params);
    return this.$http.get("/qywx/home/<USER>", params);
  }
  // 企业微信授权通过地址换取回调地址
  setWxWorkGetauthorize(params) {
    // return this.$http.get("/auth/qywx/authorize", params);
    return this.$http.get("/common/qywx/self/authorize", params);
  }
  // 企业微信授权通过code换取token
  getWxWorkSiteToken(params) {
    // return this.$http.post("/auth/qywx/login", data);
    return this.$http.get("/common/qywx/self/login", params);
  }
  // 获取权限顶级菜单列表
  getPermissionTopList() {
    return this.$http.get("/common/menu/mode/list");
  }
  // 获取权限列表
  getPermissionSlideList() {
    return this.$http.get("/admin/my/query/permissions/list");
  }
  // 获取角色管理 权限列表
  getRolesPermissionSlide() {
    return this.$http.get("/admin/permission/all");
  }
  // 获取腾房云token
  getTfyTokenData(params) {
    return this.$http.get("/auth/admin/login/login_by_tfy_site", params);
  }
  // 部门列表
  getCrmDepartmentList(params) {
    return this.$http.get("/admin/personnelMatters/departments", params);
  }
  //修改客户维护人
  Modifycustomermaintainer(params) {
    return this.$http.post("/admin/crm/client/update_maintainer", params)
  }
  //添加共享维护人
  addsharedmaintainer(params) {
    return this.$http.post("/admin/crm/share_follow/add_share_follow", params)
  }
  //删除共享维护人
  dellsharedmaintainer(id) {
    return this.$http.get(`/admin/crm/share_follow/del_share_follow?share_id=${id}`)
  }
  //我共享的人员列表
  mysharelist(params) {
    return this.$http.get("/admin/crm/share_follow/share_list", params)
  }
  //共享给我的列表
  sharedwithme(params){
    return this.$http.get("/admin/crm/share_follow/share_my_list", params)
  }
  // 部门成员列表
  getDepartmentMemberList(params) {
    return this.$http.get("/admin/personnelMatters/departmentsAndMembers", { params })
  }
  // 成员列表（经营视图检索条件成员用）
  getDepartmentMemberListNew() {
    return this.$http.get("/admin/crm/admin_users/list")
  }
  //获取带看数据列表
  gettakelooklist(params) {
    return this.$http.get(`/admin/crm/smart_management/take_statistics`, { params })
  }
  // 近七天带看数据统计
  getsevenlookdata(){
    return this.$http.get(`/admin/crm/smart_management/week_take_statistics`)
  }
  //获取回访数据列表
  getFollowuplist(params) {
    return this.$http.get(`/admin/crm/smart_management/follow_up_statistics`, { params })
  }
  //获取维护数据列表
  getmaintenancelist(params) {
    return this.$http.get(`/admin/crm/smart_management/service_statistics`, { params })
  }
  //获取主播数据列表
  getanchordatalist(params) {
    return this.$http.get(`/admin/crm/smart_management/anchor_statistics`, { params })
  }
  //导出主播数据
  exportanchordatalist(params) {
    return this.$http.get(`/admin/crm/smart_management/export_anchor`, { params })
  }
  //获取主播账号推送
  getanchoraccountdata(params){
    return this.$http.get(`/admin/crm/live_anchor/live_account_statistics`, { params })
  }
  //获取详情
  getstatisticaldetails(params){
    return this.$http.get(`/admin/crm/live_anchor/live_account_detail`, { params })
  }
  //获取成员分客统计
  getanchormembersdata(params){
    return this.$http.get(`/admin/crm/live_anchor/member_statistics`, { params })
  }
  //智慧经营获取流转数据
  getwanderaboutdata(params){
    return this.$http.get(`/admin/private_client_statistics/assign_list`, { params })
  }
  //导出经营获取流转数据
  exportbusiness(params){
    return this.$http.get(`/admin/private_client_statistics/export_assign_list`, { params })
  }
  //获取主播账号
  getobtainanchoraccount(params){
    return this.$http.get(`/admin/crm/live_anchor/accounts_member`, { params })
  }
  //获取主播账号(分页)
  getobtainanchoraccountpage(params){
    return this.$http.get(`/admin/crm/live_anchor/accounts_member_page`, { params })
  }
  //线索批量开启和关闭
  cluebatchopen(params){
    return this.$http.post("admin/crm/config/all_update_auto_status", params)
  }
  //导出主播账号数据
  exportanchoraccountdata(params){
    return this.$http.get(`/admin/crm/live_anchor/export_live_account_statistics`, { params })
  }
  //导出成员统计
  exportanchormembersdata(params){
    return this.$http.get(`/admin/crm/live_anchor/export_member_statistics`, { params })
  }
  //获取直播平台
  getzhiboplatform(){
    return this.$http.get(`/admin/crm/live_anchor/live_platform`)
  }
  //获取主播列表
  getanchorlist(params){
    return this.$http.get("/admin/crm/smart_management/live_members",{ params })
  }
  //添加主播
  addanchor(params){
    return this.$http.post("/admin/crm/smart_management/add_live_members", params);
  }
  //确定关联人事
  editanchor(params){
    return this.$http.post("/admin/crm/live_anchor/bind_member", params);
  }
  //删除主播
  deletanchor(params){
    return this.$http.post(`/admin/crm/live_anchor/cancel_bind`, params)
  }
  //导出带看数据
  exportandviewdata(params) {
    return this.$http.get(`/admin/crm/smart_management/export_take`, { params })
  }
  //导出回访数据
  exportfollowupdata(params) {
    return this.$http.get(`/admin/crm/smart_management/export_follow_up`, { params })
  }
  //导出维护数据
  exportmaintenancedata(params) {
    return this.$http.get(`/admin/crm/smart_management/export_service`, { params })
  }
  //判断是不是客户管理员
  determinecustomeradmin() {
    return this.$http.get(`/admin/my/is_manager`)
  }
  //获取经纪人日报
  getbrokerdailyreport(params) {
    return this.$http.get("/admin/crm/smart_management/agent_daily", { params });
  }
  getdailysummary(params){
    return this.$http.get("/admin/crm/smart_management/agent_daily_collect", { params });
  }
  // 获取经纪人月报
  getbrokermonthlyreport(params){
    return this.$http.get("/admin/crm/smart_management/agent_daily_month", { params });
  }
  // 获取主播运营数据列表
  getanchoroperationdata(params){
    return this.$http.get("/admin/crm/live_anchor/operate_data", { params });
  }
  //导出日报
  exportdailyreport(params){
    return this.$http.get("/admin/crm/smart_management/export_agent_daily", { params });
  }
  //导出月报
  exportmonthlyreport(params){
    return this.$http.get("/admin/crm/smart_management/export_agent_daily_month", { params });
  }
  //导出主播运营数据
  exportanchoroperationdata(params){
    return this.$http.get("/admin/crm/live_anchor/export_operate_data", { params });
  }
  // 获取人事成交列表
  getCrmCustomerDeal(params) {
    return this.$http.get("/admin/crm/deal/search", params);
  }
  // 获取人事成交列表新  crm/deal/deals?state=1&department_id=39
  getCrmCustomerDealNew(params) {
    return this.$http.get("/admin/crm/deal/deals", params);
  }

  // 获取可查看的客户列表
  getSeeCustomerList(id) {
    return this.$http.get(`/admin/crm/deal/showDealCustomer/${id}`);
  }

  // 获取企微首页数据
  getWxWorkIndexData() {
    return this.$http.get("/admin/crm/qywx/home/<USER>");
  }
  // 添加部门
  /*
   * /admin/personnelMatters/createDepartment
   * name  部门名称
   * order  排序
   * parentid    父类id,顶级部门传0
   * syn_wx是否同步企业微信通讯录，1：同步，0：不同步
   */
  addCrmDepartment(params) {
    return this.$http.post("/admin/personnelMatters/createDepartment", params);
  }
  // 编辑部门  /admin/personnelMatters/updateDepartment
  editCrmDepartment(params) {
    return this.$http.post("/admin/personnelMatters/updateDepartment", params);
  }

  // 删除部门 url:https://yun.tfcs.cn/api/admin/personnelMatters/deleteDepartment/55
  // 请求方式:get
  // 结果集:判断http状态为200
  delCrmDepartment(id) {
    return this.$http.get(`/admin/personnelMatters/deleteDepartment/${id}`);
  }
  //获取is_upload_dialog模态框提示文字
  warning_text() {
    return this.$http.get(`/admin/personnelMatters/personnel_count_msg`);
  }
  //分享
  shareDepartment(id, share_no_follow) {
    return this.$http.get(`/admin/personnelMatters/share_no_follow?id=${id}&share_no_follow=${share_no_follow}`)
  }

  //分享排行总榜
  shareRanking(params) {
    return this.$http.get("/admin/map_plugin/map_share_statistics", { params })
  }
  // /admin/personnelMatters/memberList?per_page=10&department_id=44&phone=13791416566&user_name=刘
  // 成员列表  page: 1,
  getCrmMemberList(params) {
    return this.$http.get("/admin/personnelMatters/memberList", params);
  }

  /*url:https://yun.tfcs.cn//api/admin/personnelMatters/createMember
   * 请求方式:post
   * 请求参数: phone 手机号  password_confirmation  确认密码  password 密码 user_name 名称 website_id 系统id wx_work_department_id  部门id，多个英文逗号隔开   syn_wx  是否同步微信，1：同步，0：不同步
   */
  addCrmMember(params) {
    return this.$http.post("/admin/personnelMatters/createMember", params);
  }

  // 删除员工  url:https://yun.tfcs.cn//api/admin/personnelMatters/deleteMember/463   判单返回http状态为200即是成功
  delCrmMember(id) {
    return this.$http.get(`/admin/personnelMatters/deleteMember/${id}`);
  }

  // 编辑员工  /admin/personnelMatters/updateMember
  editCrmMember(params) {
    return this.$http.post("/admin/personnelMatters/updateMember", params);
  }

  // 同步企业微信通讯录
  // url:https://yun.tfcs.cn/api/admin/personnelMatters/importWxDepartment
  // 请求方式:get

  // 结果集:判单返回http状态为200即是成功
  importWxDepartment() {
    return this.$http.get("/admin/personnelMatters/importWxDepartment");
  }

  // 客服
  // 客服列表
  getCrmServiceList(params) {
    return this.$http.get("/admin/wxkf/wxCustomers", params);
  }
  // 获取房源开通省市县
  getOpenHouseProvinceList() {
    return this.$http.get("/admin/house/provinceList");
  }
  // 通过上级id查下级数据
  getCityListByPid(pid) {
    return this.$http.get(`/admin/house/cityListByPid/${pid}`);
  }
  // 提交开通房源数据
  setOpenHouseData(data) {
    return this.$http.post("/admin/house/initializeHouse", data);
  }
  //  添加
  /**
   *
   * @param {name} params 客服名称
   * @param {avatar} params 客服头像素材id
   * @param {avatar_url} params 客服头像链接
   * @param {greeting} params 欢迎语
   * @param {open_welcome} params 是否开启欢迎语 默认1
   * @param {admin_user_id} params 接待人员 ,隔开
   * @param {is_online} params 是否坐席
   *
   */
  addCrmService(params) {
    return this.$http.post("/admin/wxkf/createWxCustomer", params);
  }
  // /admin/wxkf/wxCustomerDetail 获取详情 编辑 用
  getCrmServiceDetail(id) {
    return this.$http.get(`/admin/wxkf/wxCustomerDetail/${id}`);
  }
  // 编辑
  /**
   *
   * @param {delete_admin_user_id} params 删除的接待人员id ,隔开
   * @returns
   */
  editCrmService(params) {
    return this.$http.post("/admin/wxkf/updateWxCustomer", params);
  }
  // 导入微信客服 /admin/wxkf/initWxCustomer
  importCrmService() {
    return this.$http.get("/admin/wxkf/initWxCustomer");
  }
  // 删除微信客服 url:https://yun.tfcs.cn/api/admin/wxkf/delWxCustomer/73
  delCrmService(id) {
    return this.$http.get(`/admin/wxkf/delWxCustomer/${id}`);
  }
  // 上传素材/admin/wxkf/uploadAvatar  参数url  type=（1 => 'image', 2 => 'voice', 3 => 'video', 4 => 'file' 通过图片链接获取图片链接的id
  getAvatarId(params) {
    return this.$http.post(`/admin/crm/qywx/file/uploadMedia`, params);
  }
  // 获取素材库 文件 视频 音频 等列表   type=（1 => 'image', 2 => 'voice', 3 => 'video', 4 => 'file'）&per_page=（页码）
  getCrmServiceAvatarList(params) {
    return this.$http.get(`/admin/crm/qywx/file/medias`, { params });
  }

  // 接待人员列表/admin/wxkf/receptionists/68?per_page=10
  getCrmReceiverList(id, params) {
    return this.$http.get(`/admin/wxkf/receptionists/${id}`, { params });
  }
  // 删除接待人员 /admin/wxkf/delReceptionist  "kf_id", 'id'
  delCrmReceiver(params) {
    return this.$http.post(`/admin/wxkf/delReceptionist`, params);
  }
  // 添加接待人员 /admin/wxkf/addReceptionist  "kf_id", 'id'
  addCrmReceiver(params) {
    return this.$http.post(`/admin/wxkf/addReceptionist`, params);
  }

  // 渠道
  // 添加渠道
  addCrmScene(params) {
    return this.$http.post(`/admin/wxkf/addKFScene`, params);
  }
  // 编辑渠道
  editCrmScene(params) {
    return this.$http.post(`/admin/wxkf/editKFScene`, params);
  }
  // 渠道分类
  crmSceneType(params) {
    return this.$http.get(`/admin/wxkf/sceneCate`, { params });
  }

  // 渠道列表
  getCrmSceneList(params) {
    return this.$http.get(`/admin/wxkf/kfScenes`, params);
  }
  // 渠道统计列表 /admin/wxkf/getkfCorpStatistic?kf_id
  getCrmSceneStaticList(params) {
    return this.$http.get(`/admin/wxkf/getkfCorpStatistic`, params);
  }
  // 渠道删除 url:https://yun.tfcs.cn/api/admin/wxkf/delKFScene/1
  delCrmScene(id) {
    return this.$http.get(`/admin/wxkf/delKFScene/${id}`);
  }

  //欢迎语
  // 欢迎语列表
  getCrmWelcomeWordsList(params) {
    return this.$http.get(`/admin/crm/qywx/welcome/search`, params);
  }
  // 添加欢迎语
  addCrmWelcomeWord(params) {
    return this.$http.post(`/admin/crm/qywx/welcome/create`, params);
  }
  // 编辑欢迎语
  editCrmWelcomeWord(params) {
    return this.$http.post(`/admin/crm/qywx/welcome/update`, params);
  }
  // /admin/crm/qywx/welcome/delete/61?website_id=158
  delCrmWelcomeWord(id) {
    return this.$http.get(`/admin/crm/qywx/welcome/delete/${id}`);
  }

  //群欢迎语
  // 群欢迎语列表/admin/crm/qywx/group_welcome/search?create_time=1&type=1
  getCrmGroupWelcomeWordsList(params) {
    return this.$http.get(`/admin/crm/qywx/group_welcome/search`, params);
  }
  // 添加群欢迎语
  addCrmGroupWelcomeWord(params) {
    return this.$http.post(`/admin/crm/qywx/group_welcome/create`, params);
  }
  // 编辑群欢迎语
  editCrmGroupWelcomeWord(params) {
    return this.$http.post(`/admin/crm/qywx/group_welcome/update`, params);
  }
  // /admin/crm/qywx/group_welcome/delete/{id}
  delCrmGroupWelcomeWord(id) {
    return this.$http.get(`/admin/crm/qywx/group_welcome/delete/${id}`);
  }

  //成员活码

  // 列表/admin/activityCode/userActivityCodes
  getCrmMemberQrcodeList(params) {
    return this.$http.get(`/admin/activityCode/userActivityCodes`, params);
  }
  //添加成员活码 /admin/activityCode/createUserActivityCode
  addCrmMemberQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/createUserActivityCode`,
      params
    );
  }
  // 成员活码详情/admin/activityCode/userActivityCodeDetail/id
  crmMemberQrcodeDetail(id) {
    return this.$http.get(`/admin/activityCode/userActivityCodeDetail/${id}`);
  }
  // 编辑成员活码
  editCrmMemberQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/updateUserActivityCode`,
      params
    );
  }
  // 删除成员活码
  delCrmMemberQrcode(id) {
    return this.$http.get(`/admin/activityCode/delUserActivityCode/${id}`);
  }

  // 群活码
  // 群活码列表
  getCrmGroupQrcodeList(params) {
    return this.$http.get(`/admin/activityCode/groupActivityCodes`, params);
  }
  //添加群活码
  addCrmGroupQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/createGroupActivityCode`,
      params
    );
  }
  // 群活码详情/admin/activityCode/userActivityCodeDetail/id
  crmGroupQrcodeDetail(id) {
    return this.$http.get(`/admin/activityCode/groupActivityCodeDetail/${id}`);
  }
  // 编辑群活码
  editCrmGroupQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/updateGroupActivityCode`,
      params
    );
  }
  // 删除群活码
  delCrmGroupQrcode(id) {
    return this.$http.get(`/admin/activityCode/delGroupActivityCode/${id}`);
  }

  // 群sop
  // 群sop列表
  getCrmGroupSopList(params) {
    return this.$http.get(`/admin/sop/groupSops`, params);
  }
  //添加群sop
  addCrmGroupSop(params) {
    return this.$http.post(`/admin/sop/addGroupSop`, params);
  }
  // 群sop详情/admin/activityCode/userActivityCodeDetail/id
  crmGroupSopDetail(id) {
    return this.$http.get(`/admin/sop/groupSopDetail/${id}`);
  }
  // 编辑群sop
  editCrmGroupSop(params) {
    return this.$http.post(`/admin/sop/updateGroupSop`, params);
  }
  // 删除群sop
  delCrmGroupSop(id) {
    return this.$http.get(`/admin/sop/delGroupSop/${id}`);
  }

  // sop记录
  getCrmGroupSopLog(params) {
    return this.$http.get(`/admin/sop/groupSopSendLog`, { params });
  }
  // 群sop列表获取员工发送情况送达群聊
  /***
  
   * url:https://yun.tfcs.cn/api/admin/sop/getSendStatus?sop_id=12&sop_type=1
  请求参数:sop_id=任务id，sop_type=(1:员工发送情况,2:送达群聊)
  结果集:
  {
  "sendSuccessCount": 1,//成功人数或者群条数
  "sendFailCount": 0//失败人数或群条数
  }
   */

  getCrmGroupSopStatus(params) {
    return this.$http.get(`/admin/sop/getSendStatus`, { params });
  }
  // /admin/sop/getMemberSendStatus

  //员工SOP
  // 获取员工sop列表
  getCrmMemberSopList(params) {
    return this.$http.get(`/admin/sop/memberSops`, params);
  }
  //添加员工sop
  addCrmMemberSop(params) {
    return this.$http.post(`/admin/sop/addMemberSop`, params);
  }
  // 员工sop详情
  crmMemberSopDetail(id) {
    return this.$http.get(`/admin/sop/memberSopDetail/${id}`);
  }
  // 编辑员工sop
  editCrmMemberSop(params) {
    return this.$http.post(`/admin/sop/updateMemberSop`, params);
  }
  // 删除员工sop
  delCrmMemberSop(id) {
    return this.$http.get(`/admin/sop/delMemberSops/${id}`);
  }
  getCrmMemberSopStatus(params) {
    return this.$http.get(`/admin/sop/getMemberSendStatus`, { params });
  }
  // sop记录
  getCrmMemberSopLog(params) {
    return this.$http.get(`/admin/sop/memberSopSendLog`, { params });
  }
  // /admin/sop/externalUser/395 获取客户
  getCrmMemberCustomer(id) {
    return this.$http.get(`/admin/sop/externalUser/${id}`);
  }
  // 智慧经营房源量化核心指标数据
  getCrmIntelligentRateHouse() {
    return this.$http.get("/admin/house/intelligentRate");
  }
  // 智慧经营获取房源核心指标数据
  // getCrmIntelligentRateList(params) {
  //   return this.$http.get("/admin/house/intelligentList", params);
  // }
  getCrmIntelligentRateList_new(params) {
    return this.$http.get("/admin/house/intelligentList_new", params);
  }
  //获取是否有设置主播
  getananchorpresent() {
    return this.$http.get("/admin/crm/smart_management/anchors")
  }
  //设置主播
  setananchorpresent(param) {
    return this.$http.post("/admin/crm/smart_management/save_anchor", param)
  }
  //判断导出是否需要短信验证
  determinetextmessage() {
    return this.$http.get("/admin/crm/business_view/is_verify")
  }
  //发送短信验证
  sendingcode() {
    return this.$http.get("/admin/crm/config/sms/send_business_view_sms")
  }
  //验证短信验证码
  test_verifycode(params) {
    return this.$http.post("/admin/crm/config/sms/verify_business_view_sms", params)
  }
  // 获取审批模板
  getCrmAuditTemplate(id) {
    return this.$http.get(`/admin/crm/deal/approver_info/${id}`);
  }
  // 扫码授权查询权限
  setCrmQueryAuth() {
    return this.$http.get("/common/wx_work/checkWebsiteAuth");
  }
  // 扫码授权
  setCrmLoginCodeData(code) {
    return this.$http.get(`/common/wx_work/getAdminUserInfoWeb?code=${code}`);
  }
  // 获取管理员列表（权限区分）
  getManagerAuthList(params) {
    return this.$http.get("/admin/crm/admin_user/search", params);
  }
  //获取全部成员(线索推送用)
  getallAuthList(params){
    return this.$http.get("/admin/crm/admin_user/list", params);
  }
  // 报备客户绑定企业客户
  setReportBindWxwork(data) {
    return this.$http.post("/admin/customer/reported/evenCustomer", data);
  }
  // 获取房源模块设置范围
  getHouseSettingData() {
    return this.$http.get("/admin/house/houseSysConfig");
  }

  getFollowInfo() {
    return this.$http.get("/admin/house/remindPhoneFollow");
  }
  // 获取房源配置掉公范围
  // getHouseFallRange() {
  //   return this.$http.get("/admin/house/houseTradeStatus");
  // }
  // 获取房源配置-掉公规则参数
  getHouseInfoList() {
    return this.$http.get("/admin/house/transferRules/condition");
  }
  // 获取房源配置-掉公规则配置值
  getHouseControl() {
    return this.$http.get("/admin/house/transferRules");
  }
  // 提交房源配置-掉公规则参数
  setHouseFallRule(data) {
    return this.$http.post("/admin/house/transferRules", data);
  }
  // 获取房源人事列表
  getHousePersonList(params) {
    return this.$http.get("/admin/house/userListNoFounder", params);
  }
  // 提交房源模块设置
  setHouseSettingData(data) {
    return this.$http.post("/admin/house/saveHouseSysConfig", data);
  }
  // // 获取智慧经营客户数据
  // getCrmIntelligentRateCrm() {
  //   return this.$http.get("/admin/crm/operate/census");
  // }
  getCrmIntelligentRateCrmNew() {
    return this.$http.get("/admin/crm/operate/census_new");
  }
  // 获取智慧经营客户数据列表(表格)
  getCrmIntelist(params) {
    return this.$http.get("/admin/crm/operate/search", params);
  }
  // 获取智慧经营客户数据列表(表格,测试速度)
  newgetCrmIntelist(params) {
    return this.$http.get("/admin/crm/smart_management/search", params);
  }
  // 获取智慧经营客户数据卡片(视图)
  getCrmIntelligentRateListcst(params) {
    return this.$http.get("/admin/crm/operate/census", params);
  }
  // 获取智慧经营客户数据卡片(视图，测试速度)
  newgetCrmIntelligentRateListcst(params) {
    return this.$http.get("/admin/crm/smart_management/census", params);
  }
  // 获取智慧经营客户数据列表(导出)
  getCrmIntelligentRateListCrm(params) {
    return this.$http.get("/admin/crm/operate/export", params);
  }
  //智慧经营客户数据列表(导出，测试速度)
  newgetCrmIntelligentRateListCrm(params) {
    return this.$http.get("/admin/crm/smart_management/export", params);
  }
  //点击客户总量弹出近7日客户量变动记录(智慧经营)
  getsevencustomtotalchange(params){
    return this.$http.get("/admin/crm/smart_management/assign_detail", {params});
  }
  //分客数据排序字段
  sortingfieldsdata(){
    return this.$http.get("/admin/crm/smart_management/assign_sort");
  }
  // 获取成交，财务，智慧经营显示的权限
  getCheckShow() {
    return this.$http.get("/admin/house/userHousePrivilege");
  }
  // 获取权限显示范围(获取某个基本配置)
  getAuthShow(name) {
    // /admin/crm/config/get_crm_fixed_config?key=is_auto_recycle
    //权限名称（personnel_auth:人事权限范围,deal_auth:成交权限范围,finance_auth:财务权限范围,batch_import_uid：导入）
    return this.$http.get(`/admin/crm/config/get_crm_fixed_config?key=${name}`);
  }
  // 获取标签列表 添加编辑 员工活码 用
  getCrmGoupTags() {
    return this.$http.get("/admin/crm/qywx/tag/allTags");
  }

  // 获取通用配置权限   智慧经营 平台配置用
  getCommonSettingRoles() {
    return this.$http.get("/admin/website/get_config");
  }
  // 获取站点配置
  getCommonSettingRolesConf(type = 1) {
    // type:1原来/全部，2是短信
    return this.$http.get(`/admin/website/get_conf?type=${type}`);
  }
  setCommonSettingRoles(params) {
    return this.$http.post("/admin/website/update_config", params);
  }

  setCommonSettingRolesConf(params) {
    return this.$http.post("/admin/website/update_conf", params);
  }
  editCompanyPassword(params) {
    return this.$http.post(`/admin/company/setPassword`, params);
  }
  // 短信配置-获取发送记录
  getSendRecord(params) {
    return this.$http.get("/admin/sys_conf/ali_sms/sms_send_log", { params });
  }

  //******* 企业项目后台 ************ */
  //登录
  setCompanyLogin(data) {
    return this.$http.post("/common/enterprise/login", data);
  }
  //获取楼盘列表
  getcomapnyBuildList(params) {
    return this.$http.get("/enterprise/build/list", params);
  }
  getcomapnyBuildListByname(name) {
    return this.$http.get(`/enterprise/build/list?name=${name}`);
  }
  // 获取城市
  getCompanyRegion() {
    return this.$http.get("/enterprise/project/regions");
  }
  //添加楼盘
  setCompanyBuildData(data) {
    return this.$http.post("/enterprise/build/create", data);
  }
  // 编辑楼盘
  updateCompanyBuildData(data) {
    return this.$http.post("/enterprise/build/update", data);
  }
  // 获取楼盘详情
  getComapnyBuildDetail(id) {
    return this.$http.get(`/enterprise/build/query/${id}`);
  }
  // 获取楼盘属性详情
  getComapnyBuildDetailAttr(id) {
    return this.$http.get(`/enterprise/build/attr/query/${id}`);
  }
  // 获取楼盘户型列表
  getCompanyHouseList(id) {
    return this.$http.get(`/enterprise/build/house_type/all/${id}`);
  }
  // 新增户型图
  createCompanyHouse(data) {
    return this.$http.post("/enterprise/build/house_type/create", data);
  }
  // 查询户型
  queryCompanyHouse(id) {
    return this.$http.get(`/enterprise/build/house_type/query/${id}`);
  }
  // 编辑户型
  updateCompanyHouse(data) {
    return this.$http.post("/enterprise/build/house_type/update", data);
  }
  // 删除户型图
  deleteCompanyHouse(id) {
    return this.$http.get(`/enterprise/build/house_type/delete/${id}`);
  }
  // 项目列表
  getCompanyProjectList(params) {
    return this.$http.get("/enterprise/project/list", params);
  }

  //******* 企业项目 ************ */
  // 获取站点设置导航条
  getSiteMenuConfig() {
    return this.$http.get("/admin/website/getMenuConfig");
  }
  // 设置站点顶部导航条
  setSiteMenuConfig(data) {
    return this.$http.post("/admin/website/saveMenuConfig", data);
  }
  // 成交管理列表删除数据
  deleteDealData(id) {
    return this.$http.get(`/admin/crm/deal/delete/${id}`);
  }

  // 成交管理列表删除数据新
  deleteDealDataNew(id) {
    return this.$http.get(`/admin/crm/deal/delDeals/${id}`);
  }
  // 新增成交管理数据
  createDealData(data) {
    return this.$http.post("/admin/crm/deal/create", data);
  }

  // 新增成交管理数据 新
  createDealDataNew(data) {
    return this.$http.post("/admin/crm/deal/createDeal", data);
  }

  //  获取成交详情
  getDealDetail(id) {
    return this.$http.get(`/admin/crm/deal/showDeal/${id}`);
  }

  // 编辑成交管理数据
  editDealData(data) {
    return this.$http.post("/admin/crm/deal/update", data);
  }

  editDealDataNew(data) {
    return this.$http.post("/admin/crm/deal/updateDeal", data);
  }
  // 获取成交管理详情
  getDealDetailData(id) {
    return this.$http.get(`/admin/crm/deal/details/${id}`);
  }
  //设置分佣数据
  setFyData(data) {
    return this.$http.post("/admin/crm/deal/commission", data);
  }

  //报备客户验证手机号
  validatePhone(data) {
    return this.$http.post("/admin/customer/reported/validatePhone", data);
  }
  //报备项目列表
  reportProjects(params) {
    return this.$http.get("/admin/project/reportProjects?type=1", { params })
  }
  //报备客户
  report_create(data) {
    return this.$http.post("/admin/customer/create", data)
  }
  //我的报备客户
  my_report(params) {
    return this.$http.get("/admin/customer/reported/myReportedCustomers", params)
  }

  // 获取vr相机订单列表
  getOrderList(params) {
    return this.$http.get(`/admin/house/vr/listOrder`, { params });
  }
  // 获取vr相机订单状态列表
  getOrderStatusList() {
    return this.$http.get(`/admin/house/vr/orderStatus`,);
  }
  getCameraList() {
    return this.$http.get(`/admin/house/vr/camera_list`,);
  }
  // 获取相机详情 编辑之前展示
  getPhotoDetail(id) {
    return this.$http.get(`/admin/house/vr/camera/detail/${id}`);
  }
  // 获取相机列表
  getPhotoList() {
    return this.$http.get(`/admin/house/vr/camera/list`);
  }
  // 添加相机
  addPhoto(params) {
    return this.$http.post(`/admin/house/vr/camera/add`, params);
  }
  // 编辑相机
  editPhoto(params) {
    return this.$http.post(`/admin/house/vr/camera/edit`, params);
  }
  // vr作品列表
  /*
    "pk_works_main": 8,//作品id
    "name": "阿正科技3",//作品名称
    "thumb_path": "" // 作品缩略图
    "photo_date": "" // 作品添加时间
    "browsing_num": 1,//浏览量
    "praised_num": 0,//点赞量
    "profile": "",//作品简介
    "albums": "默认图册",//作品分类
    "author": "刘政",//作品作者
    "preview_url": " " // 作品预览路径
    "tags": [] // 作品标签
  */
  getVrWorksList(params) {
    return this.$http.get("/admin/vr/pic_project", { params });
  }
  // 编辑vr作品标签
  /*
    "name": "", // 标签名称
    "type": "", // 标签类型 1：图片 2：视频
    "sort": "", // 排序
    "id": "", // 标签id
  */
  editVrWorksLabel(params) {
    return this.$http.post("/admin/vr/edit_vr_tag", params)
  }
  // 编辑vr作品相册
  /*
    name: 相册名称
    id： 相册id
  */
  editVrWorksPhoto(params) {
    return this.$http.post("/admin/vr/edit_vr_albums", params)
  }
  // 获取vr标签
  /*
    type: 1：图片标签，2：视频标签
  */
  getVrWorksLabel(params) {
    return this.$http.get("/admin/vr/vr_tags", { params })
  }
  // 获取vr相册
  /*
    pk_atlas_main: 1 // 相册id 
    name: "测试相册" // 相册名称
  */
  getVrworksPhoto() {
    return this.$http.get("/admin/vr/vr_albums")
  }
  // 添加vr标签
  /*
    name: 标签名称
    type： 1：图片标签，2：视频标签
    sort: 排序
  */
  addVrworksLabel(params) {
    return this.$http.post("/admin/vr/add_vr_tag", params)
  }
  // 添加vr相册
  /*
    name: 相册名称
  */
  addVrworksPhoto(params) {
    return this.$http.post("/admin/vr/add_vr_albums", params)
  }
  // 创建vr全景任务(创建作品)
  /*
    pname: "", // 作品名称
    pic_tags: "", // 标签,多个用英文逗号隔开
    atlas_id: "", // 相册id 
    images: "", // 作品详情,数字对象格式[{}](JSON字符串) imgsrc:全景图片路径 imgname:全景图片名称
  */
  createVrworks(params) {
    return this.$http.post("/admin/vr/create_vr_task", params)
  }
  // 获取VR全景任务列表
  /*
    name: 任务作品名称
    atlas_id: 相册id
    time_s: 任务创建开始时间
    time_e: 任务创建结束时间
  */
  getVrworksTask(params) {
    return this.$http.get("/admin/vr/vr_task", { params })
  }
  // 获取素材列表
  /*
    "pk_media_res": 16,//素材id
    "absolutelocation": "",//素材url
    "create_time": "2023-03-01 10:27:43",//添加时间
    "media_name": "图片素材2",//素材名称
    "media_size": 7435,//素材大小单位kb
    "media_suffix": ".jpg"//素材后缀
  */
  getMaterialList(params) {
    return this.$http.get("/admin/vr/medias", { params })
  }
  // 素材重命名
  editMaterialRename(params) {
    return this.$http.post("/admin/vr/rename_media", params)
  }
  // 删除素材
  deleteMaterial(id) {
    return this.$http.get(`/admin/vr/del_media/${id}`)
  }
  // 获取全景图片列表
  /*
    "pk_img_main": 29,//全景图片id
    "filename": "3",//名称
    "thumb_path": xxx //缩略图
    "create_time": "2023-03-01 15:15:06",//创建时间
    "is_bind_project": 1//1=绑定VR作品
  */
  getPanoramaList(params) {
    return this.$http.get("/admin/vr/panoramics", { params })
  }
  // 全景图片重命名
  /*
    id: 全景图片id
    filename: 名称
  */
  setPanoramaName(data) {
    return this.$http.post("/admin/vr/rename_panoramic", data)
  }
  // 删除全景图片
  /*
    id: 全景图片id
  */
  deletePanoramaPicture(id) {
    return this.$http.post(`/admin/vr/del_panoramic/26${id}`)
  }
  // 确定上传素材
  /*
    "media_type": 0:图片素材，1：音频素材，2：视频素材，3：贴片视频素材
    "media_path": 素材url
    "media_name": 素材名称
    "media_suffix": 素材后缀
    "media_size": 素材大小
  */
  confirmUploadMaterial(data) {
    return this.$http.post("/admin/vr/add_media", data)
  }
  // 上传全景图片
  /*
    imgsrc: 全景图片路径
    imgname: 全景图片名称
  */
  addUploadPanoramaPicture(data) {
    return this.$http.post("/admin/vr/upload_panoramic", data)
  }
  //vr可视化编辑跳转
  visualediting(id) {
    return this.$http.get(`/admin/vr/edit_vr_vision?pk_works_main=${id}`)
  }
  //获取绑定房源T+房源信息
  TPropertyInformation(params) {
    return this.$http.get(`/admin/vr/house_list`, { params })
  }
  //vr绑定房源
  bindlistings(params) {
    return this.$http.post(`/admin/vr/bind_house`, params)
  }
  //vr全景编辑
  Panoramicediting(params) {
    return this.$http.post(`/admin/vr/edit_vr`, params)
  }
  //vr背景音乐
  Musicuploading(params) {
    return this.$http.post(`/admin/vr/edit_vr_bg_music`, params)
  }
  //vr上传解说音频
  confirmAddLabel(params) {
    return this.$http.post(`/admin/vr/edit_vr_audio_explain`, params)
  }
  //vr右键菜单
  Right_clickupload(params) {
    return this.$http.post(`/admin/vr/edit_vr_right_menu`, params)
  }
  //vr访问密码
  upload_password(params) {
    return this.$http.post(`/admin/vr/edit_vr_pass`, params)
  }
  //vr顶部广告语
  accessadvertisement(params) {
    return this.$http.post(`/admin/vr/edit_vr_topad`, params)
  }
  //vr企业名片
  EnterprisesCard(params) {
    return this.$http.post(`/admin/vr/edit_vr_company_card`, params)
  }
  //vr开场过度图
  Confirmuploadstart(params) {
    return this.$http.post(`/admin/vr/edit_vr_loading`, params)
  }
  //vr上传遮罩
  UploadMask(params) {
    return this.$http.post(`/admin/vr/edit_vr_cover`, params)
  }
  //vr上传自定义logo和作者名
  Authorupload(params) {
    return this.$http.post(`/admin/vr/edit_vr_logo`, params)
  }
  //vr上传开场提示
  VReminder(params) {
    return this.$http.post(`/admin/vr/edit_vr_open_alert`, params)
  }
  //上传链接电话与导航
  uploadlinknavigation(parmas) {
    return this.$http.post(`/admin/vr/edit_vr_url_phone_nvg`, parmas)
  }


  // 获取团队详情 编辑之前展示
  // getPhotoDetail(id) {
  //   return this.$http.get(`/admin/house/vr/camera/detail/${id}`);
  // }
  // 获取团队列表
  getTeamList(params) {
    return this.$http.get(`/admin/project/team/search`, { params });
  }
  // 添加团队
  addTeam(params) {
    return this.$http.post(`/admin/project/team/create`, params);
  }
  // 编辑团队
  editTeam(params) {
    return this.$http.post(`/admin/project/team/update`, params);
  }
  // 删除team
  deleteTeam(params) {
    return this.$http.post(`/admin/project/team/delete`, params);
  }

  //获取团队成员列表
  getTeamMemberList() {
    return this.$http.get(`/admin/project/team/member_list`);
  }
  //团队列表
  teamList() {
    return this.$http.get(`/admin/project/team/list`);
  }
  // 导入团队
  exportTeam(params) {
    return this.$http.post("/admin/project/manager/import", params)
  }

  // 获取客户企微信息
  getQwInfo(id) {
    return this.$http.get(`/admin/crm/client/qw_fixed_info/${id}`)
  }

  // getDouyinShouquan
  getDouyinShouquan(url) {
    return this.$http.get(`/admin/byteDance/authLink?redirect_uri=${url}`)
  }
  getDouyinShouquanNoLogin(url) {
    return this.$http.get(`/common/byte_dance/auth_link?redirect_uri=${url}`)
  }
  bindDouyin(code) {
    return this.$http.get(`/admin/byteDance/userBindByte/${code}`)
  }
  shareDouyin(params) {
    return this.$http.get(`/admin/byteDance/shareH5Schema`, { params })
  }


  // 获取项目助理列表
  getProjectMember(params) {
    return this.$http.get(`/admin/user/project/user_list`, {
      params
    })
  }
  // 获取项目助理绑定的项目
  getUserProject(params) {
    return this.$http.get(`/admin/user/project/user_project_list`, {
      params
    })
  }

  submitBindObject(params) {
    return this.$http.post(`/admin/user/project/bind_project`,
      params
    )
  }


  //永久素材列表
  foreverMedias(params) {
    return this.$http.get(`/admin/crm/qywx/file/foreverMedias`, { params })
  }
  //添加永久素材
  addForeverMedias(params) {
    return this.$http.post(`/admin/crm/qywx/file/addForeverMedias`, params)
  }

  //编辑永久素材 updatedForeverMedias
  editForeverMedias(params) {
    return this.$http.post(`/admin/crm/qywx/file/updatedForeverMedias`, params)
  }
  //删除永久素材
  delForeverMedias(id) {
    return this.$http.get(`/admin/crm/qywx/file/delForeverMedias/${id}`)
  }
  //房源图片视频VR数量
  housePhotoVideoVrNum(id) {
    return this.$http.get(`/admin/house/housePhotoVideoVrNum/${id}`)
  }
  //添加房源VR链接
  houseAddVr(params) {
    return this.$http.post(`/admin/house/houseAddVr`, params)
  }
  //房源VR链接类型列表
  vrType() {
    return this.$http.get(`/admin/house/vrType`)
  }
  //房源视频列表
  houseVideo(id) {
    return this.$http.get(`/admin/house/houseVideo/${id}`)
  }
  //房源VR列表
  houseVr(id) {
    return this.$http.get(`/admin/house/houseVr/${id}`)
  }
  //删除房源VR信息
  delHouseVr(id) {
    return this.$http.get(`/admin/house/houseDelVr/${id}`)
  }
  //编辑房源vr
  houseEditVr(params, id) {
    return this.$http.post(`/admin/house/houseEditVr/${id}`, params)
  }
  //删除房源
  delHouse(data) {
    return this.$http.post(`/admin/house/delHouse`, data)
  }
  //房源添加视频
  privateHousesAddVideo(params, id) {
    return this.$http.post(`/admin/house/privateHousesAddVideo/${id}`, params)
  }
  //房源删除视频
  privateHousesDelVideo(id) {
    return this.$http.get(`/admin/house/privateHousesDelVideo/${id}`)
  }
  //房源修改视频
  privateHousesEditVideo(params, id) {
    return this.$http.post(`/admin/house/privateHousesEditVideo/${id}`, params)
  }

  // h获取新房配置
  getNewHouseSetting() {
    return this.$http.get("/admin/new_house/config/get_config")
  }
  //修改新房配置
  setNewHouseSetting(params) {
    return this.$http.post("/admin/new_house/config/update_config", params)
  }

  // 报备客户前置验证 /admin/crm/client/simple_auth 
  checkBaobeiInfo(params) {
    return this.$http.post("/admin/crm/client/simple_auth", params)
  }

  //获取报备项目列表admin/project/reportProjects
  getBaobeiProjectList() {
    return this.$http.get("/admin/project/reportProjects")
  }
  //添加报备客户(报备记录)
  crmaddmitreport(params){
    return this.$http.post("/admin/crm/report/create",params)
  }
  //报备列表
  getreportingrecordslist(params){
    return this.$http.get("/admin/crm/report/search",{params})
  }
  //报备发送提醒
  sendreportreminder(params){
    return this.$http.post("/admin/crm/report/create_remind", params)
  }
  // 提交前验证
  checkPhoneAndProject(params) {
    return this.$http.post("/admin/customer/reported/validatePhone", params)
  }
  // 报备客户 /admin/customer/create
  submitBaobeiCustomer(params) {
    return this.$http.post("/admin/customer/create", params)
  }
  // 获取站点报备信息
  getWebInfo(website_id) {
    return this.$http.get(`/common/website/query/${website_id}`)
  }
  // 提交报备
  submitBaobeiReal(params) {
    return this.$http.post(`/admin/customer/create`, params)
  }

  // 后台报备回调/admin/crm/client/simple_call_back
  submitBaobeiCallback(params) {
    return this.$http.post(`/admin/crm/client/simple_call_back`, params)
  }

  // 我的助理

  //我的SOP
  // 获取我的sop列表
  getCrmMySopList(params) {
    return this.$http.get(`/admin/sop/myMemberSops`, params);
  }
  //添加我的sop
  addCrmMySop(params) {
    return this.$http.post(`/admin/sop/addMyMemberSop`, params);
  }
  //我的sop详情
  crmMySopDetail(id) {
    return this.$http.get(`/admin/sop/myMemberSopDetail/${id}`);
  }
  // 编辑我的sop
  editCrmMySop(params) {
    return this.$http.post(`/admin/sop/updateMyMemberSop`, params);
  }
  // 删除我的sop
  delCrmMySop(id) {
    return this.$http.get(`/admin/sop/delMyMemberSops/${id}`);
  }


  // 获取我的话术库
  getCrmMyTalkData(params) {
    return this.$http.get("/admin/crm/qywx/words/myWords", params);
  }
  // 新增我的话术
  setCrmMyTalkData(data) {
    return this.$http.post("/admin/crm/qywx/words/createMyWords", data);
  }
  // 编辑我的话术
  editCrmMyTalkData(data) {
    return this.$http.post("/admin/crm/qywx/words/updateMyWords", data);
  }
  // 删除我的话术
  deleteCrmMyTalkData(id) {
    return this.$http.get(`/admin/crm/qywx/words/deleteMyWords/${id}`);
  }
  // w我的欢迎语
  // 我的欢迎语列表
  getCrmMyWelcomeWordsList(params) {
    return this.$http.get(`/admin/crm/qywx/welcome/myWcl`, params);
  }
  // 添加我的欢迎语
  addCrmMyWelcomeWord(params) {
    return this.$http.post(`/admin/crm/qywx/welcome/createMyWcl`, params);
  }
  // 编辑我的欢迎语
  editCrmMyWelcomeWord(params) {
    return this.$http.post(`/admin/crm/qywx/welcome/updateMyWcl`, params);
  }
  //
  delCrmMyWelcomeWord(id) {
    return this.$http.get(`/admin/crm/qywx/welcome/delMyWcl/${id}`);
  }





  //我的活码

  // 列表
  getCrmMyQrcodeList(params) {
    return this.$http.get(`/admin/activityCode/userMyActivityCodes`, params);
  }
  //添加我的活码
  addCrmMyQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/createUserMyActivityCode`,
      params
    );
  }
  // 我的活码详情
  crmMyQrcodeDetail(id) {
    return this.$http.get(`/admin/activityCode/userMyActivityCodeDetail/${id}`);
  }
  // 编辑我的活码
  editCrmMyQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/updateUserMyActivityCode`,
      params
    );
  }
  // 删除我的活码
  delCrmMyQrcode(id) {
    return this.$http.get(`/admin/activityCode/delUserMyActivityCode/${id}`);
  }

  // 我的 客户列表
  getMyCustomerList(params) {
    return this.$http.get("/admin/wx_agent/external/my_custom_list", params);
  }
  // 获取我的客户流失数据
  getCrmMyCustomerLossData(params) {
    return this.$http.get("/admin/wx_agent/external/my_loss_list", params);
  }

  // 获取我的客户群列表
  getCrmCustomerMyGroupData(params) {
    return this.$http.get("/admin/activityCode/myGroupChatList", params);
  }

  // 我的群sop
  // 我的群sop列表
  getCrmMyGroupSopList(params) {
    return this.$http.get(`/admin/sop/myGroupSops`, params);
  }
  //添加我的群sop
  addCrmMyGroupSop(params) {
    return this.$http.post(`/admin/sop/addMyGroupSop`, params);
  }
  // 我的群sop详情/admin/activityCode/userActivityCodeDetail/id
  crmMyGroupSopDetail(id) {
    return this.$http.get(`/admin/sop/myGroupSopDetail/${id}`);
  }
  // 编辑我的群sop
  editCrmMyGroupSop(params) {
    return this.$http.post(`/admin/sop/updateMyGroupSop`, params);
  }
  // 删除我的群sop
  delCrmMyGroupSop(id) {
    return this.$http.get(`/admin/sop/delMyGroupSop/${id}`);
  }

  // 我的群活码
  // 我的群活码列表
  getCrmMyGroupQrcodeList(params) {
    return this.$http.get(`/admin/activityCode/myGroupActivityCodes`, params);
  }
  //添加我的群活码
  addCrmMyGroupQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/createMyGroupActivityCode`,
      params
    );
  }
  // 获取我的群聊列表
  getCrmCustomerMyGroupDataEdit(params) {
    return this.$http.get("/admin/activityCode/groupMyChatListSelf", params);
  }
  // 我的群活码详情
  crmMyGroupQrcodeDetail(id) {
    return this.$http.get(`/admin/activityCode/myGroupActivityCodeDetail/${id}`);
  }
  // 编辑我的群活码
  editCrmMyGroupQrcode(params) {
    return this.$http.post(
      `/admin/activityCode/updateMyGroupActivityCode`,
      params
    );
  }
  // 删除我的群活码
  delCrmMyGroupQrcode(id) {
    return this.$http.get(`/admin/activityCode/delMyGroupActivityCode/${id}`);
  }


  //我的永久素材列表
  myForeverMedias(params) {
    return this.$http.get(`/admin/crm/qywx/file/myForeverMedias`, { params })
  }
  //添加我的永久素材
  addMyForeverMedias(params) {
    return this.$http.post(`/admin/crm/qywx/file/addMyForeverMedias`, params)
  }

  //编辑我的永久素材 updatedForeverMedias
  editMyForeverMedias(params) {
    return this.$http.post(`/admin/crm/qywx/file/updatedMyForeverMedias`, params)
  }
  //删除我的永久素材
  delMyForeverMedias(id) {
    return this.$http.get(`/admin/crm/qywx/file/delMyForeverMedias/${id}`)
  }
  // 我的文件库
  // 我的 文件库 临时素材 列表
  getCrmMyCustomerFileData(params) {
    return this.$http.get("/admin/crm/qywx/file/myMedias", params);
  }
  // /admin/crm/qywx/file/createMyMedia  
  // 上传我的临时素材  添加我的文件库
  setCrmMyFileData(data) {
    return this.$http.post("/admin/crm/qywx/file/createMyMedia", data);
  }
  // 编辑我的临时素材  编辑我的文件库
  editCrmMyFileData(data) {
    return this.$http.post("/admin/crm/qywx/file/updateMyMedia", data);
  }
  // 删除我的临时素材  删除我的文件库
  delCrmMyFileData(id) {
    return this.$http.get(`/admin/crm/qywx/file/delMyMedia/${id}`);
  }




  //获取幸福里参数
  xflGetSetting(id) {
    return this.$http.get(`/admin/house/xfl/getSetting/${id}`);
  }
  //幸福里参数设置
  xflSetting(params) {
    return this.$http.post(`/admin/house/xfl/setting`, params);
  }
  //房源设置聚焦房源
  houseTopEdit(params) {
    return this.$http.post(`/admin/house/houseTopEdit`, params)
  }
  //全量同步房源
  synAllPub() {
    return this.$http.get(`/admin/house/xfl/all_pub`)
  }
  //全量下架房源
  removeAllPub() {
    return this.$http.get(`/admin/house/xfl/all_remove`)
  }
  // 获取房源评分
  getHouseScore() {
    return this.$http.get(`/admin/house/levelScore`)
  }
  // 设置房源评分
  setHouseScore(params) {
    return this.$http.post(`/admin/house/levelScore`, params)
  }

  // 营销短信
  // 获取任务列表
  getMaketingTaskList(params) {
    return this.$http.get(`/admin/marketing_sms/smsTask`, { params });
  }
  // 导入手机号到crm-
  importPhoneToCrm(params) {
    return this.$http.post(`/admin/marketing_sms/importPhoneToCrm`, params)
  }
  // // 添加任务
  // addMarketTask() {
  //   return this.$http.post(`/admin/personnelMatters/sendSms`, params)
  // }
  // 添加任务
  addMarketTask(params) {
    return this.$http.post(`/admin/marketing_sms/addSMSTask`, params)
  }
  // 编辑任务
  editMarketTask(params) {
    return this.$http.post(`/admin/marketing_sms/updateSMSTask`, params)
  }
  // 
  // 获取发送记录
  getMarketSendLog(params) {
    return this.$http.get(`/admin/marketing_sms/sendSMSLog`, { params });
  }

  // 营销短信 订单相关
  // 获取订单列表
  getMaketingOrderList(params) {
    return this.$http.get(`/admin/order/market_sms/search`, { params });
  }
  // 添加订单
  addMarketOrder(params) {
    return this.$http.post(`/admin/order/market_sms/create`, params)
  }
  // 取消订单
  cancelMarketOrder(id) {
    return this.$http.get(`/admin/order/market_sms/cancel/${id}`);
  }
  // 获取支付二维码
  getMarkeOrderQrcode(id) {
    return this.$http.get(`/admin/order/market_sms/get/pay/qrcode/${id}`, {
      responseType: "arraybuffer",
    });
  }
  getPackageList(params) {
    return this.$http.get(`/admin/package/sms_market/search`, { params });
  }

  // 获取签名
  getSign() {
    return this.$http.get(`/admin/marketing_sms/getSMSConfig`);
  }
  //保存签名 /admin/personnelMatters/saveSMSConfig
  saveSign(params) {
    return this.$http.post(`/admin/marketing_sms/saveSMSConfig`, params)
  }
  // 获取短信签名
  getSmsSign() {
    return this.$http.get(`/admin/sys_conf/ali_sms/get_sign`);
  }
  //保存短信签名 
  saveSmsSign(params) {
    return this.$http.post(`/admin/sys_conf/ali_sms/save_sign`, params)
  }

  // 获取模板列表
  getMaketingTemplateList(params) {
    return this.$http.get(`/admin/marketing_sms/templateLibraries`, { params });
  }
  // 添加模板
  addMarketTemplate(params) {
    return this.$http.post(`/admin/marketing_sms/addTemplateLibrary`, params)
  }
  // 编辑模板
  editMarketTemplate(params) {
    return this.$http.post(`/admin/marketing_sms/updateTemplateLibrary`, params)
  }
  // 删除模板
  cancelMarketTemplate(id) {
    return this.$http.get(`/admin/marketing_sms/delTemplateLibrary/${id}`);
  }


  // 获取号码库列表
  getMaketingTelList(params) {
    return this.$http.get(`/admin/marketing_sms/numberLibraries`, { params });
  }
  // 添加号码库
  addMarketTel(params) {
    return this.$http.post(`/admin/marketing_sms/addNumberLibrary`, params)
  }
  // 编辑号码库名称
  editMarketTel(params) {
    return this.$http.post(`/admin/marketing_sms/updateNumberLibrary`, params)
  }
  // 删除号码库
  cancelMarketTel(id) {
    return this.$http.get(`/admin/marketing_sms/delNumberLibrary/${id}`);
  }

  // 获取号码列表
  getMaketingTellogList(id) {
    return this.$http.get(`/admin/marketing_sms/phones/${id}`);
  }
  // 添加号码
  addMarketTellog(params) {
    return this.$http.post(`/admin/marketing_sms/addPones`, params)
  }
  // 编辑号码
  editMarketTellog(params) {
    return this.$http.post(`/admin/marketing_sms/updatePhone`, params)
  }
  // 删除号码
  cancelMarketTellog(id) {
    return this.$http.get(`/admin/marketing_sms/delPhone/${id}`);
  }
  //执行发送营销短信任务
  sendMarketTask(params) {
    return this.$http.post(`/admin/marketing_sms/sendSMS`, params)
  }

  // 外呼
  // 获取机构信息
  getJigou() {
    return this.$http.get(`/admin/call_phone/getOrganization`);
  }
  // 保存机构信息
  saveJigou(params) {
    return this.$http.post(`/admin/call_phone/saveOrganization`, params);
  }
  //获取坐席列表
  getZuoxi(params) {
    return this.$http.get(`/admin/call_phone/callSeatsList`, { params });
  }
  //添加坐席
  addZuoxzi(params) {
    return this.$http.post(`/admin/call_phone/addCallSeatsNew`, params);
  }
  //编辑坐席
  editZuoxzi(params) {
    return this.$http.post(`/admin/call_phone/updateCallSeatsNew`, params);
  }
  //坐席详情
  DetailsZuoxzi(id) {
    return this.$http.get(`/admin/call_phone/getCallSeatsDetail/${id}`);
  }
  //删除坐席id
  delZuoxzi(id) {
    return this.$http.get(`/admin/call_phone/delCallSeats/${id}`);
  }
  //AI配置
  getAiConfig() {
    return this.$http.get(`/admin/call_phone/get_analysis_config`);
  }
  //更改AI配置
  saveAiConfig(params) {
    return this.$http.post(`/admin/call_phone/auto_analysis_config`, params);
  }
  //一键覆盖(采纳AI建议)
  coverPhone(id) {
    return this.$http.get(`/admin/call_phone/cover_client?call_record_id=${id}`);
  }
  //批量导入
  BatchImport(data) {
    return this.$http.post(`/admin/call_phone/importCallPhoneShow`, data, {
      headers: { "Content-Type": "multipart/form-data" },
      transformRequest: [
        function () {
          return data;
        },
      ],
    });
  }
  // 获取外显号码列表
  getShowTelNumber(params) {
    return this.$http.get(`/admin/call_phone/callPhoneShowList`, { params });
  }
  // 更改外显号码的禁用状态
  changeTelDisabled(id, params) {
    return this.$http.get(`/admin/call_phone/setCallPhoneShow/${id}`, { params });
  }
  // 添加外显号码
  addOutTel(params) {
    return this.$http.post(`/admin/call_phone/saveCallPhoneShow`, params);
  }

  // 外呼渠道列表
  getChannel(params) {
    return this.$http.get(`/admin/call_phone/callChannels`, { params });
  }
  // 添加外呼渠道
  addChannel(params) {
    return this.$http.post(`/admin/call_phone/addCallChannel`, params);
  }
  // 编辑外呼渠道
  editChannel(params) {
    return this.$http.post(`/admin/call_phone/updateCallChannel`, params);
  }
  // 删除外呼渠道
  delChannel(id) {
    return this.$http.get(`/admin/call_phone/delCallChannel/${id}`);
  }


  // 获取外呼包名列表
  getCallClues(params) {
    return this.$http.get(`/admin/call_clue/callClues`, { params });
  }
  // 获取包的手机号列表
  getTelList(params) {
    return this.$http.get(`/admin/call_clue/callCluePhones`, { params });
  }
  // 获取部门列表 人工分配用
  getOutboundDepartmentList(params) {
    return this.$http.get(`/admin/call_clue/seatsDepartment`, { params });
  }
  // 获取部门下员工列表 人工分配用
  getOutboundMemberList(params) {
    return this.$http.get(`/admin/call_clue/seatsDepartmentMember`, { params });
  }
  // 提交分配任务
  submitClueAssign(params) {
    return this.$http.post(`/admin/call_clue/clueAssign`, params);
  }
  // 获取外呼任务包列表
  getTaskPackageList(params) {
    return this.$http.get(`/admin/call_clue/myCallTaskPackage`, { params });
  }
  // /admin/call_clue/unAssignClueCount/1 获取任务包未分配数量
  getUnAssignClueCount(id) {
    return this.$http.get(`/admin/call_clue/unAssignClueCount/${id}`);
  }
  // admin/call_clue/myCallTaskList 获取我的外呼任务列表
  getMyOutboundTaskList(params) {
    return this.$http.get(`/admin/call_clue/myCallTaskList`, { params });
  }
  // 获取外呼任务统计信息
  getCallTaskStatistics(params) {
    return this.$http.get(`/admin/call_clue/myCallTaskStatistics`, { params })
  }
  // 拨打外呼电话
  callOutboundNum(id, params) {
    return this.$http.get(`/admin/call_clue/memberCallCluePhone/${id}`, { params })
  }
  // 外呼任务 电话详细列表
  getCallPhoneList(id, params) {
    return this.$http.get(`/admin/call_clue/callPhoneDetail/${id}`, { params })
  }
  // 外呼任务 电话详细列表 呼叫记录
  getCallPhoneRecord(params) {
    return this.$http.get(`/admin/call_clue/callRecord`, { params })
  }
  // 外呼首页接口
  getOutboundIndexTopInfo(params) {
    return this.$http.get("/admin/call_phone/dataKanbanTopNew", { params })
  }
  //客户线索统计(页面上部分)
  Customer_leads(params) {
    return this.$http.get("/admin/crm/statis/client_clue_statis", { params })
  }
  //客户线索折线统计图(页面下部分)
  Customer_line_chart(params) {
    return this.$http.get("/admin/crm/statis/client_clue_statis_chart", { params })
  }
  //成交转化面板
  Transaction_conversion(params) {
    return this.$http.get("/admin/crm/statis/client_deal_statis", { params })
  }
  Transaction_conversion_chart(params) {
    return this.$http.get("/admin/crm/statis/client_deal_statis_chart", { params })
  }
  getOutboundIndexMiddleInfo(params) {
    return this.$http.get("/admin/call_phone/dataKanBanMiddle", { params })
  }
  // http://www.fenxiao.com/api/admin/call_phone/dataKanBanBottomNew?sort=&times=
  getOutboundIndexBottomInfo(params) {
    return this.$http.get("/admin/call_phone/dataKanBanBottomNew", { params })
  }
  // 导出坐席
  exportZuoxi() {
    return this.$http.get("/admin/call_phone/exportSeats")
  }
  exportTelTask(params) {
    return this.$http.get("/admin/call_clue/exportMyTask", { params })
  }
  // 拨打记录-直拨电话
  DirectTelephone(params) {
    return this.$http.post("/admin/call_clue/directCallPhone", params)
  }
  // 拨打记录-直拨记录
  DirectRecord(params) {
    return this.$http.get("admin/call_clue/myDirectCallLog", { params })
  }
  // 拨打记录-直拨录音
  DirectRecording(params) {
    return this.$http.get("admin/call_clue/directCallRecord", { params })
  }
  // 外部页面进入本项目以后获取token信息
  getTokenInfo(params) {
    return this.$http.get("auth/admin/login/to_tfy_site", { params })
  }
  // 资源客户检索
  CustomerRetrieval(params) {
    return this.$http.get("/admin/crm/client/resourceCustomer", { params })
  }
  // CRM公海客户导入线索包
  CustomerImportingClue(params) {
    return this.$http.post("/admin/call_clue/addCallClueFromCrm", params)
  }
  // 资源客户导入进度
  CustomerImportingProgress(id) {
    return this.$http.get(`/admin/call_clue/importStatus/${id}`)
  }
  // 坐席套餐-坐席套餐购买列表
  getSeatsMenuList(params) {
    return this.$http.get("/admin/package/call_seat_market/search?status=1", { params })
  }
  // 坐席套餐-购买记录列表
  getSeatsRecordList(params) {
    return this.$http.get("/admin/order/market_call_seat/search", { params })
  }
  // 坐席套餐-购买记录-取消订单
  cancelSeatsRecord(id) {
    return this.$http.get(`/admin/order/market_call_seat/cancel/${id}`)
  }
  // 坐席套餐-购买套餐创建订单
  getBuySeatsMenuOrder(params) {
    return this.$http.post("/admin/order/market_call_seat/create", params)
  }
  // 坐席套餐-二维码
  getQRcode(id) {
    return this.$http.get(`/admin/order/market_call_seat/get/pay/qrcode/${id}`, {
      responseType: "arraybuffer",
    })
  }
  // 通话套餐-通话充值列表
  getCallMenu(params) {
    return this.$http.get("/admin/package/call_market/search?status=1", { params })
  }
  // 通话套餐-购买套餐创建订单
  getBuyMenuOrder(params) {
    return this.$http.post("/admin/order/market_call/create", params)
  }
  // 通话套餐-根据订单ID获取支付二维码
  buyCallMenuQRcode(id) {
    return this.$http.get(`/admin/order/market_call/get/pay/qrcode/${id}`, {
      responseType: "arraybuffer",
    })
  }
  // 我的报备-报备项目列表
  getReporting() {
    return this.$http.get("/admin/project/reportProjects")
  }
  // 套餐管理-通话套餐-获取充值记录
  getRechargeRecord(params) {
    return this.$http.get("/admin/order/market_call/search", { params })
  }
  // 套餐管理-通话套餐-充值记录取消订单
  deleteRechargeOrder(id) {
    return this.$http.get(`/admin/order/market_call/cancel/${id}`)
  }
  // 坐席管理-添加坐席-可用坐席列表
  getAvailableList() {
    return this.$http.get("/admin/call_phone/callSeatAvailableList")
  }
  // 坐席管理-添加坐席-坐席收费状态
  getSeatsState(id) {
    return this.$http.get(`/admin/call_phone/callSeatStatus/${id}`)
  }
  // 呼叫中心-录入CRM-是否开通外呼录入
  getWhetherOpening() {
    return this.$http.get("/admin/call_phone/getCallPhoneToCrm")
  }
  // 呼叫中心-录入CRM-操作外呼录入crm状态
  changeOpeningStatus(params) {
    return this.$http.post("/admin/call_phone/openCallPhoneToCrm", params)
  }
  // 呼叫中心-外呼配置-获取一键加微信
  getOutboundWeChat() {
    return this.$http.get("/admin/call_phone/getCallPhoneFriends")
  }
  // 呼叫中心-外呼配置-设置一键加微信
  setOutboundWeChat(params) {
    return this.$http.post("/admin/call_phone/callPhoneFriends", params)
  }
  // 呼叫中心-外呼数据统计
  getOutboundStatistics() {
    return this.$http.get("/admin/call_phone/statistics")
  }
  // 呼叫中心-是否开启坐席收费
  getSeatsFees() {
    return this.$http.get("/admin/call_phone/callSeatPayStatus")
  }
  // 房源跟进-@同事列表
  getColleagueList() {
    return this.$http.get("/admin/house/follow/userListDepartment")
  }
  // 房源跟进-带看登记
  addHouseRegister(params) {
    return this.$http.post("/admin/house/take/add", params)
  }
  // 线索拨打和直接拨打-获取外显号码
  getExplicitNumber() {
    return this.$http.get("/admin/call_clue/getSeatsPhone")
  }
  // 客户轨迹-获取楼盘小程序
  getRealEstateXiaoApp(id, params) {
    return this.$http.get(`/admin/crm/client_track/search?id=${id}&source=1`, { params })
  }
  // 客户详情-获取@同事列表
  getColleagueDetailsList(params) {
    return this.$http.get("/admin/crm/client_follow/userListDepartment", { params })
  }
  // 客户详情-获取@同事详细信息
  getColleagueDetailsData(id) {
    return this.$http.get(`/admin/crm/client_follow/userInfo/${id}`)
  }
  // 抖音线索-获取抖音配置
  getTikTokConfig() {
    return this.$http.get("/admin/crm/config/get_dy_config")
  }
  newgetTikTokConfig() {
    return this.$http.get("/admin/crm/config/get_auto_config")
  }
  //线索推送设置留资是否去重
  setliuziDeduplication(params){
    return this.$http.post("/admin/crm/config/update_repeat_config", params)
  }
  //获取线索分组（不分页）
  getcluegrouping() {
    return this.$http.get("/admin/crm/config/push_group/list")
  }
  // 抖音线索-更新抖音配置
  updataTikTokConfig(params) {
    return this.$http.post("/admin/crm/config/update_dy_config", params)
  }
  newupdataTikTokConfig(params) {
    return this.$http.post("/admin/crm/config/update_auto_config", params)
  }
  //线索推送-微信小店(初始化)
  TikTokinitialization(params){
    return this.$http.get(`/admin/crm/config/init_wx_store?key=${params}`)
  }
  // 线索分配-获取站点CRM线索分配配置
  /*
    "push_type": 0, //推送方式(0:手动分配,1:自动分配)
    "push_style": 0, //分类规则(0:轮流分配,1:按需分配)
    "push_uid": "" //推送员工列表
  */
  getClueAllocation() {
    return this.$http.get("/admin/crm/config/get_push_config")
  }
  // 更新站点CRM线索分配配置
  /*
    "push_type": 0, //推送方式(0:手动分配,1:自动分配)
    "push_style": 0, //分类规则(0:轮流分配,1:按需分配)
    "push_uid": "699,700,701" //推送员工列表
  */
  setClueAllocation(params) {
    return this.$http.post("/admin/crm/config/update_push_config", params)
  }
  setClueAllocationNew(params) {
    return this.$http.post("/admin/crm/config/update_allocation_config", params)
  }
  getAuthCrmShow(key) {
    return this.$http.get("/admin/website/get_fixed_conf?key=" + key)
  }
  //获取分组信息(分页)
  cluegrouping(page, per_page) {
    return this.$http.get(`/admin/crm/config/push_group/search?page=${page}&per_page=${per_page}`)
  }
  //添加分组名称
  addgrouptitle(params) {
    return this.$http.post("/admin/crm/config/push_group/create", params)
  }
  // 编辑分组名称
  editgoup(parmas) {
    return this.$http.post("/admin/crm/config/push_group/update", parmas)
  }
  //删除分组
  delegoup(id) {
    return this.$http.get(`/admin/crm/config/push_group/delete/${id}`)
  }
  //获取组内成员
  getgroupEmployees(id) {
    return this.$http.get(`/admin/crm/config/push_group_info/list?group_id=${id}`)
  }
  //获取全部组成员
  getgroupall() {
    return this.$http.get(`/admin/crm/config/push_group_info/list`)
  }
  // 添加分组员工
  groupemployees(parmas) {
    return this.$http.post("/admin/crm/config/push_group_info/create_all", parmas)
  }
  //删除分组员工
  delstaff(id) {
    return this.$http.get(`/admin/crm/config/push_group_info/delete/${id}`)
  }
  //更改员工接单状态
  setstaffstatus(params) {
    return this.$http.post(`/admin/crm/config/push_group_info/update_status`, params)
  }
  // 获取抖音首页统计 
  /*  
  time_type  时间筛选 1:全部，2：昨日，3：近七天，4：近一个月
  project_id 项目id
  custom_stime  自定义开始时间（不能和time_type共存）
  custom_etime  自定义结束时间（不能和time_type共存）
  */
  getDouyinStatics(params) {
    return this.$http.get("/admin/byte_dance/site_statistics", { params })
  }
  //获取首页热门数据
  /*  
  time_type  时间筛选 1:全部，2：昨日，3：近七天，4：近一个月
  project_id 项目id
  custom_stime  自定义开始时间（不能和time_type共存）
  custom_etime  自定义结束时间（不能和time_type共存）
  */
  getDouyinIndexList(params) {
    return this.$http.get("/admin/byte_dance/hot_billboard", { params })
  }
  //获取抖音看板详细数据 
  /*  
  open_id 抖音授权id
  */
  getDouyinKanbanDetail(params) {
    return this.$http.get("/admin/byte_dance/byte_user_data", { params })
  }

  //数据看板 /admin/byte_dance/byte_data
  /*  
  phone 手机号
  project_id 项目id
  name   名称
  */
  getDouyinKanbanList(params) {
    return this.$http.get("/admin/byte_dance/byte_data", { params })
  }
  /**
   * 获取项目列表
   * admin/byte_dance/projects
   */
  getDouyinProjectList(params) {
    return this.$http.get("/admin/byte_dance/projects", { params })
  }

  /**
   * 添加项目
   * /admin/byte_dance/save_project
   * id 存在就是编辑  不存在就是添加
   *  name
   */
  saveDouyinProjectList(params) {
    return this.$http.post("/admin/byte_dance/save_project", params)
  }
  /**
   * 
   * @param {
   *  phone 手机号
   *  project_id：项目id
   *  name 名称
   *  auth_type 授权状态
   * } params 
   */
  getAuthorizeList(params) {
    return this.$http.get("/admin/byte_dance/auth_user", { params })
  }
  /**
   * 添加抖音授权 post
   * /admin/byte_dance/add_byte_user
   * @params {
   *  name：名称
   *  project_id :项目id
   *  phone 手机号
   *  会员id :admin_id
   * }
   */
  addDouyinMember(params) {
    return this.$http.post("/admin/byte_dance/add_byte_user", params)
  }
  /**
   * 编辑抖音授权 post
   * /admin/byte_dance/edit_byte_user
   * @params {
   *  name：名称
   *  project_id :项目id
   *  phone 手机号
   *  会员id :admin_id
   * id
   * }
   */
  editDouyinMember(params) {
    return this.$http.post("/admin/byte_dance/edit_byte_user", params)
  }
  /**
   * 删除抖音用户
   * /admin/byte_dance/del_byte_user/6
  
  */
  delDouyinMember(id) {
    return this.$http.get(`/admin/byte_dance/del_byte_user/${id}`)
  }
  /**
   * 授权成功以后绑定
   * /common/byte_dance/auth_byte_dance
   * get
   * id  code
  
  */
  bindDouyinMember(id, code) {
    return this.$http.get(`/common/byte_dance/auth_byte_dance?id=${id}&code=${code}`)
  }

  /**
   * 
   * 删除项目
   * /admin/byte_dance/del_project/1
   */
  delDouyinProject(id) {
    return this.$http.get(`/admin/byte_dance/del_project/${id}`)
  }

  /**
   * 
   * 
   */
  getDouyinVideosList(params) {
    return this.$http.get(`/admin/byte_dance/my_videos`, { params })
  }
  /**
  * 
  * 
  */
  getDouyinVideosCommentList(params) {
    return this.$http.get(`/admin/byte_dance/video_comments`, { params })
  }
  /**
   * 
   * 抖音授权小程序成功以后 回调 
   */
  authorizationDouyin(authorization_code) {
    return this.$http.get(`/common/byte_dance/auth_token?authorization_code=${authorization_code}`)
  }
  //新增拓客区域
  Newly_area(params) {
    return this.$http.post(`admin/poster/region/create`, params)
  }
  //拓客区域列表
  Expansion_arealist(params) {
    return this.$http.get(`admin/poster/region/search`, { params })
  }
  //修改区域位置
  Modify_Area(params) {
    return this.$http.post(`admin/poster/region/update`, params)
  }
  //删除区域
  Region_deletion(id) {
    return this.$http.get(`admin/poster/region/delete/${id}`)
  }


  //添加传阅
  New_Reading(params) {
    return this.$http.post(`admin/poster/poster/add`, params)
  }
  //删除传阅
  Delete_circulation(id) {
    return this.$http.get(`admin/poster/poster/delete/${id}`)
  }
  //传阅详情
  details_circulation(id) {
    return this.$http.get(`admin/poster/poster/detail/${id}`)
  }
  // 传阅修改
  Modify_circulation(params) {
    return this.$http.post(`admin/poster/poster/update`, params)
  }
  //模糊搜索
  Circular_Fuzzy_Search(params) {
    return this.$http.get(`admin/poster/poster/list`, { params })
  }
  //后台会员列表
  Background_Member_List(params) {
    return this.$http.get(`admin/poster/manager/list`, { params })
  }
  //添加会员
  AddMember_List(params) {
    return this.$http.post(`admin/poster/manager/add`, params)
  }
  //会员详情
  Member_Details(id) {
    return this.$http.get(`admin/poster/manager/detail/${id}`)
  }
  //修改会员
  Modify_membership(params) {
    return this.$http.post(`admin/poster/manager/update`, params)
  }
  //删除会员
  Delete_Member(id) {
    return this.$http.get(`admin/poster/manager/delete/${id}`)
  }


  /**
 * 
 * T抖音授权小程序成功以后 回调 
 */
  authorizationTDouyin(authorization_code) {
    return this.$http.post(`/admin/byte_dance_t/auth_erp?authorization_code=${authorization_code}`)
  }
  /**
   * 
   * T抖音授权获取授权链接 
   */
  getTDouyinLink(uri) {
    return this.$http.post(`/admin/byte_dance_t/get_erp_link?redirect_uri=${uri}`)
  }
  /**
 * 
 * 新房抖音授权小程序成功以后 回调 
 */
  authorizationXinfangDouyin(authorization_code) {
    return this.$http.post(`/admin/byte_dance_t/auth_new_house?authorization_code=${authorization_code}`)
  }
  /**
   * 
   * 新房抖音授权获取授权链接 
   */
  getXinfangDouyinLink(uri) {
    return this.$http.post(`/admin/byte_dance_t/get_new_house_link?redirect_uri=${uri}`)
  }
  // 获取头条设置
  getTtSetting(params) {
    return this.$http.get(`/common/mini/config/detail`, { params })
  }
  // 获取新的三级联动列表 楼盘
  getTtSettingList(params) {
    return this.$http.get('/common/mini/config/build_parameter',)
  }
  // 房源
  getTtSettingList1(params) {
    return this.$http.get('/common/mini/config/house_parameter',)
  }
  // 保存头条设置
  saveTtSetting(params) {
    return this.$http.post(`/common/mini/config/save`, params)
  }
  // 公司信息
  getTtCompanySetting(params) {
    return this.$http.get(`/common/mini/config/contact`, { params })
  }
  // 公司信息
  saveTtCompanySetting(params) {
    return this.$http.post(`/common/mini/config/saveContact`, params)
  }




  // 语音聊天
  //  获取聊天token
  getIMToken() {
    return this.$http.get(`/admin/chat/im_token`)
  }

  // 获取好友列表
  getFriendList(params) {
    return this.$http.get(`/admin/chat/chat_friends`, { params })
  }
  // 获取聊天消息列表
  getChatMessage(params) {
    return this.$http.get("/admin/message/im/search", { params });
  }
  // 获取好友信息 to_id
  getFriendInfo(params) {
    return this.$http.get(`/admin/chat/contact_details`, { params })
  }
  // IM系统 获取消息时检查是否绑定前台用户 (422为没有绑定前台用户)
  getIMWhetherBind() {
    return this.$http.get("/admin/message/im/check");
  }
  // IM系统获取最新的三条未读消息
  getThreeNewMessage() {
    return this.$http.get("/admin/message/im/not_reading_list");
  }
  // 保存聊天信息  chat_id，type 
  saveMessage(data) {
    return this.$http.post(`/admin/chat/save_chat_log`, data)
  }
  // 获取IM系统user_token
  getSystemToken() {
    return this.$http.get("/admin/message/im/user_token");
  }
  // 获取IM系统im_Token
  getSystemImToken() {
    return this.$http.get("/admin/message/im/im_token");
  }
  // 修改IM系统会话为已读
  setConversationRead(id) {
    return this.$http.get(`/admin/message/im/read_all/${id}`);
  }
  // 一键阅读IM系统会话
  clearAllUnread() {
    return this.$http.get(`/admin/message/im/quick_read`);
  }
  // 保存发送的消息
  saveSendMessage(data) {
    return this.$http.post("/admin/message/im/create", data);
  }
  //聊天记录
  getChatLog(params) {
    return this.$http.post("/admin/chat/chat_log", params)
  }
  // IM系统会话详情
  getConversationDetails(params) {
    return this.$http.get("/admin/message/im/details", { params });
  }
  getDouyinUser(params) {
    return this.$http.get("/admin/client_user/users?platform_cat=3", { params })
  }
  //获取配置项获客外网用户列表(新)
  getuserlist(params){
    return this.$http.get("/admin/client_user/user_list",{params})
  }
  //获取配置项获客外网获客列表(新)
  gethuokelist(params){
    return this.$http.get("/admin/client_user/client_list",{params})
  }

  // 设置内部成员

  setNeibuChengyuan(params) {
    return this.$http.post("/admin/user/bindInnerUserId", params)
  }

  cancelNeibuChengyuan(id) {
    return this.$http.get(`/admin/user/cancelInnerUserId/${id}`)
  }
  //领资料跳转H5授权
  redirectH5authorization(params){
    return this.$http.post("/client_api/map_plugin/loginByWxPublic", params)
  }
  // 发送登录验证码
  sendLoginCode(params) {
    return this.$http.post("/auth/admin/send/sms/captcha", params)
  }
  // 验证码登录
  codeLogin(params) {
    return this.$http.post("/auth/admin/login/login_by_code", params)
  }

  // getNewSideBar点击获取左侧菜单栏
  getNewSideBar(type) {
    return this.$http.get(`/admin/my/query/permissions/list/${type}`)
  }


  fabudouyin(params) {
    return this.$http.post("/admin/byte_dance/create_project", params)
  }
  //获取要@的用户列表  keywords 支持手机号和用户名
  atUserList(params) {
    return this.$http.get("/admin/byte_dance/at_users", { params })
  }

  uploadVideoTomedia(params) {
    return this.$http.post("/admin/byte_dance/upload_byte_media", params)
  }

  getDouyinSucaiList(params) {
    return this.$http.get("/admin/byte_dance/medias", { params })
  }
  submitDouyinsuCai(params) {
    return this.$http.post("/admin/byte_dance/add_medias", params)
  }

  // 删除素材 https://yun.tfcs.cn/api/admin/byte_dance/del_medias/1
  delDouyinSucai(id) {
    return this.$http.get(`/admin/byte_dance/del_medias/${id}`)
  }






  setProjectSimpleData(params) {
    return this.$http.post(`/admin/project/updateListRow`, params)
  }
  // 检测人事成员凭证
  getInfoConfig() {
    return this.$http.get(`/admin/personnelMatters/personnel_count`,)
  }
  // 平台配置-公司信息，获取详情
  getCorporationDetails() {
    return this.$http.get("/common/company_module/info/detail")
  }
  // 平台配置-公司信息，保存信息
  saveCorporationDetails(data) {
    return this.$http.post("/common/company_module/info/save", data)
  }

  // 新成员默认创建部门 
  setDefaultDepart() {
    return this.$http.get("/admin/personnelMatters/checkDefaultDepartmentAndRole")
  }
  // 获取外呼记录列表
  getOutbondTaocanLog(params) {
    return this.$http.get("/admin/call_phone/phoneBillLogs", { params })
  }
  //成员消费统计
  memberexpend(params) {
    return this.$http.get("/admin/call_phone/memberBillLogs", { params })
  }
  //AI消费记录
  aiexpend(params){
    return this.$http.get("/admin/call_phone/analysisBillLogs", { params })
  }
  //AI分析成员消费统计
  aiexpandmember(params) {
    return this.$http.get("/admin/call_phone/memberAnalysisBillLogs", { params })
  }
  //导出AI分析成员消费记录
  exportaiexpandmember(params){
    return this.$http.get("/admin/call_phone/exportMemberAnalysisBillLogs", { params })
  }
  //导出成员消费统计
  exportmemberexpend(params) {
    return this.$http.get("/admin/call_phone/exportMemberBill", { params })
  }
  // https://docs.apipost.cn/preview/ead0696cc6036d50/96077f6107afcc88
  // https://yun.tfcs.cn/api/admin/call_phone/billDetail?id=10
  getBoundTelDetail(id) {
    return this.$http.get("/admin/call_phone/billDetail?id=" + id)
  }
  sendPhoneTel(data) {
    return this.$http.post("/common/call_module/addCallOutRecord", data)
  }
  //获取客户全号手机号
  getphonequan(id){
    return this.$http.get("/admin/crm/client/get_mobile?id=" + id)
  }
  // 全员拨打电话
  allStaffDialTelephone(data) {
    return this.$http.post("/admin/call_clue/anyOneCallByCrm", data)
  }
  // /api/admin/crm/client_follow/search
  // 获取客户详情-电话记录
  getClientTelephoneRecord(params) {
    return this.$http.get("/admin/crm/client_follow/search", { params });
  }
  //AI分析通话质量
  aianalysiscallquality(id){
    return this.$http.get(`/admin/call_phone/analysis_record?call_record_id=${id}`)
  }
  //更改接单状态
  changegordersvalue(params) {
    return this.$http.post("/admin/crm/config/update_allocation", params)
  }
  // 我的客户置顶/取消置顶
  setMyClientTop(id) {
    return this.$http.get(`/admin/crm/client/top/${id}`);
  }
  // 查看电话-判断全号显示、隐号显示
  getJudgmentPhone(id) {
    return this.$http.get(`/admin/crm/client/see_tel/${id}`);
  }
  // 获取客户详情-跟进记录
  getMaintainRecord(params) {
    return this.$http.get("/admin/crm/client_follow/follow_search", params)
  }
  // 客户详情-提醒跟进
  addWarnFollow(data) {
    return this.$http.post("/admin/crm/client/sms_remind", data);
  }
  //提醒跟进-历史提醒列表
  historicalreminderlist(params){
    return this.$http.get("/admin/crm/client_remind/search", {params})
  }
  //历史记录
  historicalreminder(params){
    return this.$http.get("/admin/crm/client_remind/logs", {params})
  }
  // 客户详情-获取短信模板
  getSmsTemplate() {
    return this.$http.get("/admin/crm/sms_template/list");
  }
  // 客户详情-保存短信模板
  saveSmsTemplate(data) {
    return this.$http.post("/admin/crm/sms_template/update", data);
  }
  // 客户详情-打开带看中的项目库
  takelookprojectlibrary(params) {
    return this.$http.get(`/admin/crm/project_trends/list`, { params });
  }
  //项目详情
  getProjectDetails(id) {
    return this.$http.get(`/admin/crm/project_trends/project_detail?project_id=${id}`);
  }
  //保存项目库动态
  saveprojectupdates(params) {
    return this.$http.post(`/admin/crm/project_trends/save`, params);
  }
  //项目动态动态置顶
  projectupdatesTop(id) {
    return this.$http.get(`/admin/crm/project_trends/top?id=${id}`);
  }
  //项目动态动态置顶
  projectcancellationTop(id) {
    return this.$http.get(`/admin/crm/project_trends/cancel_top?id=${id}`);
  }
  //项目动态动态置顶
  delprojectcancellation(id) {
    return this.$http.get(`/admin/crm/project_trends/del?id=${id}`);
  }
  //切换至移动端管理系统
  switchtomobileend() {
    return this.$http.get(`/auth/admin/login/mini_wechat_qrcode`);
  }
  // admin/crm/client/sms_remind
  //  获取是否需要去跟进
  getForceFollow() {
    return this.$http.get("/admin/crm/client/verify_follow");
  }
  // 客源配置-项目管理获取当前站点可用项目选项
  getProjectAdminList() {
    return this.$http.get("/admin/crm/config/get_project_select");
  }
  // 客源配置-项目管理保存当前项目选项
  saveProjectAdmin(data) {
    return this.$http.post("/admin/crm/config/save_project_type", data);
  }
  // 客源配置-项目管理获取项目列表
  getCrmProjectList(params) {
    return this.$http.get("/admin/crm/config/project_list", { params });
  }
  // 客源配置-项目管理添加项目
  addCrmProject(data) {
    return this.$http.post("/admin/crm/config/save_project", data);
  }
  // 客源配置-项目管理删除项目
  deleteCrmProject(id) {
    return this.$http.get(`/admin/crm/config/del_project/${id}`);
  }
  // 客户详情-项目管理获取当前配置项目列表 客户资料用
  getProjectComfirm(params) {
    return this.$http.get("/admin/crm/config/get_project_by_config", { params });
  }
  //客户详情提醒跟进中短信提醒是否显示
  TextAlert() {
    return this.$http.get("/admin/crm/client/verify_tel_follow");
  }
  // 获取客户数据统计
  getClientStatistics(params) {
    return this.$http.get("/admin/crm/statis/client_statis", { params });
  }
  newgetClientStatistics(params) {
    return this.$http.get("/admin/crm/statis/client_statis_new", { params });
  }
  // 获取客户数据统计底部chart
  getClientChart(params) {
    return this.$http.get("/admin/crm/statis/client_statis_chart", { params });
  }
  newgetClientChart(params) {
    return this.$http.get("/admin/crm/statis/client_statis_chart_new", { params });
  }

  // 经营视图用 获取线索列表 
  getJingyingViewList(params) {
    return this.$http.get("/admin/crm/client/third_party_clue/search", { params });
  }
  // 经营视图用 获取线索来源列表
  getThirdSourceList(params) {
    return this.$http.get("/admin/crm/client/access/list", { params });
  }
  // /admin/crm/client/third_party_clue/get_client_id  通过手机号查询客户 id
  getClientIdByPhone(params) {
    return this.$http.get("/admin/crm/client/third_party_clue/get_client_id", { params });
  }
  //获取资料包列表
  obtainlistdatapackages(params){
    return this.$http.get("/admin/map_plugin/packages",{params});
  }
  //添加资料包
  adddatapackage(data){
    return this.$http.post(`/admin/map_plugin/create_package`, data);
  }
  //删除资料包
  deletedatapackage(id){
    return this.$http.get(`/admin/map_plugin/del_package/${id}`);
  }
  //编辑资料包
  editdatapackage(data){
    return this.$http.post(`/admin/map_plugin/update_package`, data);
  }
  //获取资料包详情
  getpackagedetails(id){
    return this.$http.get(`/admin/map_plugin/package_detail/${id}`);
  }
  //资料包小程序码
  datapackageminiprogram(id){
    return this.$http.get(`/admin/map_plugin/package_qr_code/${id}`);
  }
  //资料包短链接
  datapackageshortlink(id){
    return this.$http.get(`/admin/map_plugin/package_short_link/${id}`);
  }
  //资料包获客记录
  datapackagecustomer(params){
    return this.$http.get(`/admin/map_plugin/package_users`, { params });
  }

  // 获取地图插件列表 
  getMapPluginList() {
    return this.$http.get("/admin/map_plugin/map_plugins");
  }
  // 添加地图插件
  addMapPlugin(data) {
    return this.$http.post("/admin/map_plugin/create_map", data);
  }
  // 获取地图插件详情
  getMapPluginDetail(id) {
    return this.$http.get(`/admin/map_plugin/map_plugin_detail/${id}`);
  }
  // 编辑地图插件
  editMapPlugin(data) {
    return this.$http.post(`/admin/map_plugin/update_map`, data);
  }
  // 删除地图插件
  deleteMapPlugin(id) {
    return this.$http.get(`/admin/map_plugin/del_map_plugin/${id}`);
  }
  // 获取地图插件微信二维码
  getMapPluginQRcode(id, params) {
    return this.$http.get(`/admin/map_plugin/get_qr_code/${id}`, { params });
  }
  // 获取地图插件分享排行榜
  getMapShareBank(params) {
    return this.$http.get("/admin/map_plugin/map_shares", { params });
  }
  //导出获客记录列表
  exportMapRecorde(params) {
    return this.$http.get("/admin/map_plugin/export_map_users", { params })
  }
  // 获取地图插件获客记录
  getMapRecorde(params) {
    return this.$http.get("/admin/map_plugin/map_users", { params });
  }
  // 话术设置-获取话术列表
  getVerbalList(params) {
    return this.$http.get("/admin/call_phone/speech", { params });
  }
  // 话术设置-获取话术分类
  getVerbalType() {
    return this.$http.get("/admin/call_phone/speech_cate");
  }
  // 话术设置-添加话术
  addVerbal(data) {
    return this.$http.post("/admin/call_phone/create_speech", data);
  }
  // 话术设置-添加话术分类
  addVerbalClassify(data) {
    return this.$http.post("/admin/call_phone/create_speech_cate", data);
  }
  // 话术设置-编辑话术分类
  editVerbalClassify(data) {
    return this.$http.post("/admin/call_phone/update_speech_cate", data);
  }
  // 话术设置-删除话术分类
  deleteVerbalClassify(id) {
    return this.$http.get(`/admin/call_phone/del_speech_cate/${id}`)
  }
  // 录音详情-实体抽取
  getEntityFetch(params) {
    return this.$http.get("/admin/call_phone/words", { params });
  }
  // 录音详情-内容质检
  getContentCheck(params) {
    return this.$http.get("/admin/call_phone/emotion", { params });
  }
  // 录音详情-词频统计
  getWordFrequency(params) {
    return this.$http.get("/admin/call_phone/word_statistics", { params });
  }
  // 录音详情-识别列表
  getRecordRecognition(params) {
    return this.$http.get("/admin/call_phone/recognitions", { params });
  }
  // 录音详情-智能完善
  getSmartForm(params) {
    return this.$http.get("/admin/call_phone/form_field", { params });
  }
  // 录音详情-修改客户问题答案信息
  setRecordUserInfo(data) {
    return this.$http.post("/admin/crm/call/update_client", data);
  }
  // 录音详情-分析结果
  getAnalyseResult(params) {
    return this.$http.get("/admin/call_phone/call_speech_match", { params });
  }
  // 数据分析-录音量排行榜
  getRecordQuantity(params) {
    return this.$http.get("/admin/call_phone/count_rank", { params });
  }
  // 数据分析-录音时长排行榜
  getRecordDuration(params) {
    return this.$http.get("/admin/call_phone/times_rank", { params });
  }
  // 数据分析-话术执行率排行榜
  getVerbalExecuteRate(params) {
    return this.$http.get("/admin/call_phone/speech_rank", { params });
  }
  // 数据分析-折线图
  getAnalyseChart() {
    return this.$http.get("/admin/call_phone/line_chart");
  }
  // 客户详情-沟通记录
  getCallRecode(params) {
    return this.$http.get("/admin/crm/call/search", { params });
  }
  // 我的拨打-黑名单列表
  getDialBlackList(params) {
    return this.$http.get("/admin/call_clue/my_call_phone_error", { params })
  }

  //成交分销模块
  getdistributiontabel() {
    return this.$http.get("/admin/crm/deal/reports")
  }

  //智能AI机器人模块
  getrobotstatistics() {
    return this.$http.get("/admin/call_clue/robot/robot_statistics")
  }

  //通话记录列表
  getcallrobot(params) {
    return this.$http.get("/admin/call_clue/robot/robot_record", { params })
  }

  //号码库列表
  getimportDetail(params) {
    return this.$http.get("/admin/call_clue/robot/import_detail", { params })
  }
  //导入记录
  getrobotimport(params) {
    return this.$http.get("/admin/call_clue/robot/import_logs", { params })
  }

  //意见列表
  getoptionsList(params) {
    return this.$http.get("/admin/call_clue/robot/label_types", { params })
  }

  //保存自动分配规则
  setrulesfrom(parmas) {
    return this.$http.post("/admin/call_clue/robot/save_config", parmas)
  }
  //获取自动分配规则
  getrulesfrom() {
    return this.$http.get("/admin/call_clue/robot/robot_config")
  }

  getformrediotype(id) {
    return this.$http.get(`/admin/house/houseTradeType/${id}`)
  }
  //导出坐席统计
  // https://yun.tfcs.cn/api/admin/call_phone/exportSeatsNew?sort=on_call_percentage_desc&times=
  exportSeatsStatic(params) {
    return this.$http.get("/admin/call_phone/exportSeatsNew", { params });
  }


  // 成交报告模块-报告列表
  getReportListAPI(params) {
    return this.$http.get(`/admin/crm/deal/reports`, { params })
  }
  // 成交报告模块-报告详情
  getReportDetailAPI(id) {
    return this.$http.get(`/admin/crm/deal/report_detail?id=${id}`)
  }
  // 成交报告模块-添加报告
  addReportAPI(data) {
    return this.$http.post(`/admin/crm/deal/add_report`, data)
  }
  // 成交报告模块-删除报告
  delReportAPI(id) {
    return this.$http.get(`/admin/crm/deal/del_report?id=${id}`)
  }
  // 成交报告模块-编辑报告
  editReportAPI(data) {
    return this.$http.post(`/admin/crm/deal/edit_report`, data)
  }
  // 成交报告模块-公司佣金（扣除款项）
  getCompanyCommissionListAPI(id) {
    return this.$http.get(`/admin/crm/deal/company_commission?report_id=${id}`)
  }
  // 成交报告模块-添加公司佣金（扣除款项）
  addCompanyCommissionAPI(data) {
    return this.$http.post(`/admin/crm/deal/add_company_commission`, data)
  }
  // 成交报告模块-编辑公司佣金（扣除款项）
  editCompanyCommissionAPI(data) {
    return this.$http.post(`/admin/crm/deal/edit_company_commission`, data)
  }
  // 成交报告模块-删除公司佣金（扣除款项）
  delCompanyCommissionAPI(id) {
    return this.$http.get(`/admin/crm/deal/del_company_commission?id=${id}`)
  }
  // 成交报告模块-成员佣金
  memberCommissionListAPI(id) {
    return this.$http.get(`/admin/crm/deal/member_commission?report_id=${id}`)
  }
  // 成交报告模块-添加成员佣金
  addMemberCommissionAPI(data) {
    return this.$http.post(`/admin/crm/deal/add_member_commission`, data)
  }
  // 成交报告模块-编辑成员佣金
  editMemberCommissionAPI(data) {
    return this.$http.post(`/admin/crm/deal/edit_member_commission`, data)
  }
  // 成交报告模块-删除成员佣金
  delMemberCommissionAPI(id) {
    return this.$http.get(`/admin/crm/deal/del_member_commission?id=${id}`)
  }
  // 成交报告模块-添加应收佣金
  addReceiveCommissionAPI(data) {
    return this.$http.post(`/admin/crm/deal/add_receive_commission`, data)
  }
  // 成交报告模块-应收佣金列表
  getReceiveCommissionListAPI(id) {
    return this.$http.get(`/admin/crm/deal/receive_commission?report_id=${id}`)
  }
  // 成交报告模块-删除应收佣金
  delReceiveCommissionAPI(id) {
    return this.$http.get(`/admin/crm/deal/del_receive_commission?id=${id}`)
  }
  // 成交报告模块-编辑应收佣金
  editReceiveCommissionAPI(data) {
    return this.$http.post(`/admin/crm/deal/edit_receive_commission`, data)
  }

  // 成交报告模块-回款记录列表
  getPaymentLogsListAPI(id) {
    return this.$http.get(`/admin/crm/deal/payment_logs?id=${id}`)
  }
  // 成交报告模块-添加回款记录
  addPaymentLogsListAPI(data) {
    return this.$http.post(`/admin/crm/deal/add_payment_logs`, data)
  }
  // 成交报告模块-编辑回款记录
  editPaymentLogsListAPI(data) {
    return this.$http.post(`/admin/crm/deal/edit_payment_log`, data)
  }
  // 成交报告模块-删除回款记录
  delPaymentLogsListAPI(id) {
    return this.$http.get(`/admin/crm/deal/del_payment_log?id=${id}`)
  }

  // 成交报告模块-附件列表
  getReportFilesListAPI(id) {
    return this.$http.get(`/admin/crm/deal/report_files?report_id=${id}`)
  }
  // 成交报告模块-添加附件
  addReportFilesAPI(data) {
    return this.$http.post(`/admin/crm/deal/add_report_files`, data)
  }
  // 成交报告模块-编辑附件
  editReportFilesAPI(data) {
    return this.$http.post(`/admin/crm/deal/edit_report_files`, data)
  }
  // 成交报告模块-删除附件
  delReportFilesAPI(id) {
    return this.$http.get(`/admin/crm/deal/del_report_files?id=${id}`)
  }
  // 成交报告模块-导出成交报告
  exportCrmDealReport(params) {
    return this.$http.get(`/admin/crm/deal/export_report`, { params })
  }
  // 成交报告模块-作废成交报告
  cancelCrmDealReport(data) {
    return this.$http.post(`/admin/crm/deal/cancel_report`, data)
  }
  // 成交报告模块-回款记录列表
  crmDealReportPaymentLogs(params) {
    return this.$http.get(`/admin/crm/deal/payment_logs`, { params })
  }
  // 成交报告模块-添加回款记录
  addCrmDealReportPaymentLogs(data) {
    return this.$http.post(`/admin/crm/deal/add_payment_logs`, data)
  }
  // 成交报告模块-编辑回款记录
  editCrmDealReportPaymentLogs(data) {
    return this.$http.post(`/admin/crm/deal/edit_payment_log`, data)
  }
  // 成交报告模块-编辑回款记录
  delCrmDealReportPaymentLogs(params) {
    return this.$http.get(`/admin/crm/deal/del_payment_log`, { params })
  }
  // 成交报告模块-通过手机号客户查询成交人
  crmDealSearchDealUser(phone) {
    return this.$http.get(`/admin/crm/deal/search_deal_user?phone=${phone}`)
  }
  //成交报告-获取客户维护信息- 用于新增成交报告的业绩提成时，快捷选择
  crmDealClientInfo(report_id) {
    return this.$http.get(`/admin/crm/deal/client_info?report_id=${report_id}`)
  }
  // 大悟导入数据
  commonDawuImport(data) {
    return this.$http.post("/common/da_wu/import", null, {
      timeout: 300000,
      transformRequest: [function () {
        return data;
      }],
    })
  }
  commonFileUpload(data) {
    return this.$http.post("/common/file/upload/admin?category=13", null, {
      timeout: 300000,
      transformRequest: [function () {
        return data;
      }],
    })
  }
  // 获取当前登录成员权限
  getMyRoleInfo() {
    return this.$http.get(`/admin/my/role`)
  }
  // 成交审批操作
  crmDealApprove(data) {
    return this.$http.post(`/admin/house/multiDealApprove`, data, {
      timeout: 300000
    })
  }
  // 获取省市区
  getRegionAll() {
    return this.$http.get(`/qywx/region/list`)
  }
  // 获取crm带看项目
  getCrmProjectByConfig(keyword) {
    return this.$http.get(`/admin/crm/config/get_project_by_config?keyword=${keyword}`)
  }
  // 获取当前站点的项目选项类型  1自建项目  2房源小区库
  getCrmConfigProjectType() {
    return this.$http.get(`/admin/crm/config/get_project_type`)
  }
  //获取地图插件配置
  getMapPluginConfig() {
    return this.$http.get(`/admin/map_plugin/config`)
  }
  //保存地图插件配置
  saveMapPluginConfig(data) {
    return this.$http.post(`/admin/map_plugin/save_config`, data)
  }
  //获取crm客户自定义tabs
  getCrmCustomTabs(params) {
    return this.$http.get("/admin/crm/crm_tab/tabs", { params })
  }
  //添加crm客户自定义tab
  addCrmCustomTab(parmas) {
    return this.$http.post("/admin/crm/crm_tab/add_tab", parmas)
  }
  //编辑crm客户自定义tab
  editCrmCustomTab(parmas) {
    return this.$http.post("/admin/crm/crm_tab/edit_tab", parmas)
  }
  //获取crm客户自定义tab详情
  getCrmCustomTabDetail(id, tabType) {
    return this.$http.get("admin/crm/crm_tab/tab_detail?id=" + id + '&tab_type=' + tabType)
  }
  //删除crm客户自定义tab
  deleteCrmCustomTab(id, tabType) {
    return this.$http.get("admin/crm/crm_tab/del_tab?id=" + id + '&tab_type=' + tabType)
  }
  //crm客户自定义tab排序
  sortCrmCustomTab(data) {
    return this.$http.post("admin/crm/crm_tab/tab_sort", data)
  }
  //同步覆盖自定义tab
  coverCrmCustomTab(params) {
    return this.$http.get("admin/crm/crm_tab/cover_tab", { params }, { timeout: 300000 })
  }
  //客户详情上一条下一条
  customerOneSearch(params) {
    return this.$http.get("admin/crm/client/one_search", { params })
  }

  // 传阅页 列表
  getLeafletListAPI(params) {
    return this.$http.get("admin/page_plugin/list")
  }
  // 传阅页 编辑
  editLeaflet(data) {
    return this.$http.post("admin/page_plugin/update", data)
  }
  // 传阅页 添加
  addLeaflet(data) {
    return this.$http.post("admin/page_plugin/create", data)
  }
  // 传阅页 详情
  leafletDetail(id) {
    return this.$http.get(`admin/page_plugin/detail/${id}`)
  }
  // 传阅页 批量操作
  changeLeaflet(data) {
    return this.$http.post(`admin/page_plugin/multi_field`, data)
  }
  // 传阅页 获客记录
  getCaRecord(id, params) {
    return this.$http.get(`admin/page_plugin/record_list/${id}`, params)
  }
  // 传阅页 抖音二维码预览
  getQrCodeDyAPI(data) {
    return this.$http.post(`admin/page_plugin/get_qr_code_dy`, data)
  }
  // 传阅页 获取分享链接
  getLeafletSharePath(data) {
    return this.$http.post(`admin/page_plugin/get_url_dy`, data)
  }
  //获取前台成员id
  getShareUserId(admin_id) {
    return this.$http.get(`admin/map_plugin/share_user_id?admin_id=${admin_id}`)
  }
  //获取h5打开微信小程序链接
  getMiniWechatScheme() {
    return this.$http.get('auth/admin/login/mini_wechat_scheme')
  }

  //委托房源模块 房源列表
  getEntrustListAPI(params) {
    return this.$http.get('admin/house/entrust/list', params)
  }
  confirmEntrustAPI(id) {
    return this.$http.get(`admin/house/entrust/confirm?id=${id}`)
  }
  cancelEntrustAPI(id) {
    return this.$http.get(`admin/house/entrust/cancel?id=${id}`)
  }
  searchConditionAPI() {
    return this.$http.get('admin/house/entrust/searchCondition')
  }

  // 获客列表模块
  getCustomerListAPI(params) {
    return this.$http.get('admin/crm/client_track/admin_ranking', params)
  }
  // 获客统计
  customerStatisticsAPI() {
    return this.$http.get('admin/crm/client_track/statistics')
  }
  // 楼盘分享列表
  houseShareAPI() {
    return this.$http.get('admin/crm/client_track/build_ranking')
  }
  // 页面分享列表
  pageShareAPI() {
    return this.$http.get('admin/crm/client_track/page_ranking')
  }
  // 导出获客列表
  exportCustomerAPI() {
    return this.$http.get('admin/crm/client_track/export_ranking')
  }
  // 导出楼盘分享列表
  exportHouseAPI() {
    return this.$http.get('admin/crm/client_track/export_build_ranking')
  }
  // 导出页面分享列表
  exportPageAPI() {
    return this.$http.get('admin/crm/client_track/export_page_ranking')
  }
  // 培训视频详情
  videoTutorialDetail(id) {
    return this.$http.get('common/video_tutorial/detail/' + id)
  }
  // 验证培训视频链接是否过期
  verifyVideoTutorialToken(data) {
    return this.$http.post('common/video_tutorial/verify_token', data)
  }
  //短视频
  getshortvideo(params){
    return this.$http.get('/admin/douyin/video/list', { params })
  }
  //短视频评论列表
  shortvideocommentlist(params){
    return this.$http.get('/admin/douyin/video/comment_list', { params })
  }
  //获取短视频检索条件
  getshortvideoquerycriteria(){
    return this.$http.get('/admin/douyin/video/search_condition')
  }
  //获取评论用户列表
  getcommentuserslist(params){
    return this.$http.get('/admin/douyin/video/client_list', { params })
  }
  //修改用户状态
  setshortvideostatus(params){
    return this.$http.post('/admin/douyin/video/user_status_update', params)
  }
  //保存短视频手机号
  saveshortvideophone(params){
    return this.$http.post('/admin/douyin/video/user_mobile_update', params)
  }
  //添加手机号码后确定录入客户接口
  addphoneenter(params){
    return this.$http.get(`/admin/douyin/video/push_user_to_crm`, { params })
  }
  //所有直播间用户
  getAllLiveRoomUserList(params) {
    return this.$http.get('admin/douyin/users', { params })
  }
  //指定直播间用户
  getLiveRoomUserList(params) {
    return this.$http.get('admin/douyin/living_room/users', { params })
  }
  //获取站点直播间
  getDouyinRooms(params) {
    return this.$http.get('admin/douyin/rooms', { params })
  }
  //获取抖音移动端发起聊天Schema
  getDouyinChatSchema(data) {
    return this.$http.post('admin/douyin/user/getSchemaChat', data)
  }
  //获取用户所有留资的直播间
  getUserAllClueRooms(sec_uid) {
    return this.$http.get(`admin/douyin/user/fill/${sec_uid}`)
  }
  //获取直播用户搜索项
  getLiveRoomUserCondition() {
    return this.$http.get(`admin/douyin/search_condition`)
  }
  //修改直播用户标记状态
  updateDouyinRoomUserStatus(data) {
    return this.$http.post(`admin/douyin/user_status_update`, data)
  }
  //获取抖音直播间-分页
  getDouyinRoomList(params){
    return this.$http.get(`admin/douyin/room_list`, {params,  timeout: 300000 })
  }
  //修改直播用户手机号
  updateDouyinUserMobile(data){
    return this.$http.post(`admin/douyin/user_mobile_update`, data)
  }
  //添加手机号码后确定录入客户接口
  pushDouyinUserToCRM(params){
    return this.$http.get(`admin/douyin/push_user_to_crm`, {params})
  }
  //直播间用户可进入权限
  douyinRoomUsersPermission() {
    return this.$http.get(`admin/douyin/permission`)
  }
  //直播帐号列表
  douyinLiveAccountList(params) {
    return this.$http.get(`admin/douyin/account_list`, {params})
  }
  //主播账号关联人事账号
  updateDouyinLiveAccount(data) {
    return this.$http.post(`admin/douyin/account/update`, data)
  }
  //关联主播时人事选择接口
  getDouyinLiveAccountAdminList(params) {
    return this.$http.get(`admin/douyin/admin_list`, {params})
  }
  //直播帐号取消人事关联
  cancelDouyinLiveAccount(account_id) {
    return this.$http.get(`admin/douyin/account/cancel?account_id=${account_id}`)
  }
  //私信用户列表
  getPrivateLetterUserList(params) {
    return this.$http.get(`admin/douyin/private_letter/users`, {params})
  }
  //私信用户列表-查询条件
  getPrivateLetterUserCondition(params) {
    return this.$http.get(`admin/douyin/private_letter/search_condition`, {params})
  }
  //查询用户私信记录和回复
  getPrivateLetterUserChatList(params) {
    return this.$http.get(`admin/douyin/private_letter/user/chat`, {params})
  }
  //私信用户列表-修改用户状态
  updatePrivateLetterUserStatus(data) {
    return this.$http.post(`admin/douyin/private_letter/user_status_update`, data)
  }
  //私信用户列表-修改用户状态
  updatePrivateLetterUserMobile(data) {
    return this.$http.post(`admin/douyin/private_letter/user_mobile_update`, data)
  }
  //私信用户列表-修改用户状态
  pushPrivateLetterUserToCrm(params) {
    return this.$http.get(`admin/douyin/private_letter/push_user_to_crm`, {params})
  }

  //更新成员密码
  resetAdminPassword(data) {
    return this.$http.post('admin/admin_user/password/update_all', data)
  }
  //更新成员状态
  setAdminStatus(data) {
    return this.$http.post('admin/admin_user/status/update_all', data)
  }
  //绑定成员角色
  bindAdminRoles(data) {
    return this.$http.post('admin/admin_user/role/update_all', data)
  }
  //重置录入人信息
  crmRepeatCreateConfig(data) {
    return this.$http.post('/admin/crm/config/repeat_create_config', data, {timeout: 300000})
  }
  //获取重置录入人历史记录更新信息
  getCrmRepeatLog(key) {
    return this.$http.get('/admin/crm/config/get_repeat_log?key='+key)
  }
  //设置主播
  crmSetAnchor(data) {
    return this.$http.post('/admin/crm/smart_management/set_anchor', data)
  }
  //获取地区-省市列表
  getRegionCityList(){
    return this.$http.get('/qywx/city/list')
  }

  // AI分析列表相关接口
  // 获取AI分析列表
  getAiAnalysisList(params) {
    return this.$http.get("/admin/call_phone/analysis_list", { params });
  }

  // 获取人员列表
  getUserListDepartment(params) {
    return this.$http.get("/admin/crm/client_follow/userListDepartment", { params });
  }

  // 获取部门列表
  getDepartmentList(params) {
    return this.$http.get("/admin/personnelMatters/departments", { params });
  }
}

export default new UserCenter();
