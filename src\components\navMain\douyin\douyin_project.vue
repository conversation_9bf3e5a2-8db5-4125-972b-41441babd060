<template>
  <div class="pages" id="page">
    <el-row :gutter="20">
      <el-col :span="24">
        <div
          class="content-box-crm flex-row align-center"
          style="padding: 0 12px"
        >
          <el-button type="primary" icon="el-icon-plus" @click="add"
            >添加</el-button
          >
        </div>
        <div class="content-box-crm">
          <el-table
            v-loading="is_table_loading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#EBF0F7' }"
            highlight-current-row
            :row-style="$TableRowStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column width="100" prop="id" label="ID"></el-table-column>
            <el-table-column label="名称" prop="name"> </el-table-column>
            <el-table-column label="操作" v-slot="{ row }">
              <el-link
                type="primary"
                style="margin-right: 10px"
                @click="onClickData(row)"
                >编辑</el-link
              >
              <el-popconfirm
                title="确定删除吗？"
                @onConfirm="deleteProject(row)"
              >
                <el-link slot="reference" type="danger" icon="el-icon-delete"
                  >删除</el-link
                >
              </el-popconfirm>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: end; margin-top: 24px"
            background
            layout="prev, pager, next"
            :total="params.total"
            :page-size="params.per_page"
            :current-page="params.page"
            @current-change="onPageChange"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
    <!-- <el-dialog :visible.sync="show_select_dia" width="660px" :title="title">
      <memberListSingle
        v-if="show_select_dia"
        :list="memberList"
        :defaultValue="selectedIds"
        @onClickItem="selecetedMember"
      ></memberListSingle>
    </el-dialog> -->
    <el-dialog :visible.sync="show_add_project" width="500px" :title="title">
      <el-form label-width="100px">
        <el-form-item label="项目名称">
          <el-input style="width: 220px" v-model="proform.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-loading="submiting" @click="onCreate"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import memberListSingle from "../site/components/memberList_single.vue";
export default {
  name: "crm_customer_member_qrcode",
  components: {
    // memberListSingle,
  },
  data() {
    return {
      params: {
        page: 1,
        per_page: 10,
        total: 0,
        time_type: 1,
      },
      tableData: [],
      is_table_loading: false,
      multipleSelection: [],
      show_detail: false,
      project_loading: false,
      proform: {
        name: '',
        id: ""
      },
      show_add_project: false,
      submiting: false

    };
  },
  created() {
    this.getData()
    // this.getProjectData()

    // this.getDepartment();
  },
  filters: {
    filterName(val) {
      let name = ''
      switch (val) {
        case 'hotVideo':
          name = '热门视频'
          break;
        case 'live':
          name = '直播榜单'
          break;
        case 'sentence':
          name = '热门词'
          break;
        case 'trending_sentences':
          name = '热度榜'
          break;
        case 'topic':
          name = '话题榜'
          break;

        default:
          break;
      }
      return name

    },
    formatNum(val) {
      return (val / 10000).toFixed(0)
    }

  },
  mounted() {
    // this.getDataList();
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    previewImg(src) {
      console.log(123);
      this.imgSrc = src;
      this.show_preview = true;
    },
    setClass(val) {
      if (val && val.indexOf("-") > -1) return true
      return false
    },
    getData() {
      this.getList()
    },
    add() {
      this.title = '添加'
      this.proform = {
        name: ""
      }
      this.show_add_project = true
    },

    onCreate() {
      this.submiting = true
      this.$http.saveDouyinProjectList(this.proform).then(res => {
        if (res.status == 200) {
          this.$message.success(res.message || "操作成功")
        }
        this.submiting = false
        this.show_add_project = false
      }).catch(() => {
        this.submiting = false
      })
    },
    getList() {
      if (this.p_time && this.p_time.length) {
        this.params.custom_stime = this.p_time[0]
        this.params.custom_etime = this.p_time[1]
      } else {
        this.params.custom_stime = ''
        this.params.custom_etime = ''
      }
      this.is_table_loading = true
      this.$http.getDouyinProjectList(this.params).then(res => {
        if (res.status == 200) {
          this.tableData = res.data
          this.params.total = res.data.total;
        }
        this.is_table_loading = false
      }).catch(() => {
        this.is_table_loading = false
      })
    },
    deleteProject(row) {
      this.$http.delDouyinProject(row.id).then(res => {
        if (res.status == 200) {
          this.$message.success('操作成功')
          this.getList()
        }
      })
    },

    selecetedMember(e) {
      if (this.currentType == 1) {
        //使用员工
        if (e.checkedNodes && e.checkedNodes.length) {
          this.user_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.user_id = e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.user_name = "";
          this.params.user_id = "";
        }
      } else {
        if (e.checkedNodes && e.checkedNodes.length) {
          this.creat_name = e.checkedNodes[e.checkedNodes.length - 1].name;
          this.params.create_user =
            e.checkedNodes[e.checkedNodes.length - 1].id;
        } else {
          this.creat_name = "";
          this.params.create_user = "";
        }
      }

      this.show_select_dia = false;
      this.params.page = 1;
      this.getDataList();
    },

    onPageChange(e) {
      this.params.page = e;
      this.getList();
    },
    onClickData(row) {
      this.proform = row
      this.title = "编辑"
      this.show_add_project = true
    },
  },
};
</script>

<style scoped lang="scss">
.items-center {
  align-items: center;
}
.mla {
  margin-left: auto;
}
.mr10 {
  margin-right: 10px;
}
.mb20 {
  margin-bottom: 20px;
}
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
  .padd0 {
    padding: 0 24px;
    margin: 0 -20px;
    .title {
      padding: 15px 40px;
    }
  }
  .bottom-border {
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e2e2;
    .text {
      font-size: 14px;
      color: #8a929f;
    }
  }
}
// ::v-deep .el-link {
//   ~ .el-link {
//     margin-right: 10px;
//   }
// }
.row_img {
  width: 60px;
  height: 60px;
  cursor: pointer;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.datas_list {
  margin-top: 32px;
  .datas_item {
    margin-right: 24px;
    background: #fff;
    border-radius: 10px;
    padding: 24px;
    &.point {
      cursor: pointer;
    }
    &:last-child {
      margin-right: 0;
    }
    .datas_item_left {
      width: 35px;
      height: 35px;
      margin-right: 25px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .change {
      color: #fa6060;
      &.jian {
        color: #37e780;
      }
    }
    .datas_item_right {
      .datas_item_right_name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #2e3c4e;
        .img {
          width: 16px;
          height: 16px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
      .datas_item_right_top {
        color: #464d59;
        font-size: 18px;
        font-family: PingFangSC-Medium, sans-serif;
        font-weight: 600;
        .small {
          font-size: 16px;
          font-weight: normal;
        }
      }
      .datas_item_right_bottom {
        color: #768196;
        margin-top: 5px;
        font-size: 13px;
      }
    }
  }
}
.item_title {
  color: #000000;
  font-family: PingFang-Medium, sans-serif;
  font-weight: 600;
  font-size: 22px;
}

.datas .item_title {
  margin-top: 25px;
}
</style>
