<template>
 <div>
    <div class="tipsContainer" 
        :style="{
        borderLeft:borderStyle,
        background:ctnerBcakground
    }">
        <b 
        :style="{
        color:titleColor,
        fontSize:titleFontsize
        }">
        {{tipsTitle}}
        </b><br>
        <p 
        class="tipsContent" 
        v-for="item in tipsList" :key="item"
        :style="{
            fontSize:contentSize,
            color:contentColor
        }"
        >
          {{item}}
        </p>
    </div>
 </div>
</template>

<script>
export default {
    props:{
        //标题内容
        tipsTitle:{
            type:String,
            default:"提示 ："
        },
        //标题颜色
        titleColor:{
            type:String,
            default:"#5e6d82"
        },
        //标题字体大小
        titleFontsize:{
            type:String,
            default:"14px"
        },
        //内容文本
        tipsList:[Array],
        //边框样式
        borderStyle:{
            type:String,
            default:"5px solid #50bfff"
        },
        //盒子背景颜色
        ctnerBcakground:{
            type:String,
            default:"#ecf8ff"
        },
        //内容字体大小
        contentSize:{
            type:String,
            default:"14px"
        },
        //内容字体颜色
        contentColor:{
            type:String,
            default:"#5e6d82"
        }
    }
};
</script>

<style  scoped lang="scss">
.tipsContainer{
  padding: 8px 16px;
  border-radius: 4px;
  margin: 20px 0px;
    .tipsContent{
      line-height: 1.5em;
    }
}

</style>
