<template>
    <div>
        <el-dialog 
            width="500px" 
            :visible.sync="show_audit" 
            title="提交审批"
            @close="closeExamine"
        >
            <div class="audit_form" v-if="show_audit">
                <el-form label-width="80px">
                    <el-form-item label="审批类型">
                        <el-select
                            style="width: 300px"
                            v-model="audit_form.cat_id"
                            @change="changeAudit"
                        >
                            <el-option
                                v-for="item in auditList"
                                :key="item.values"
                                :value="item.values"
                                :label="item.name"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        v-for="(item, index) in keyAuditList"
                        :label="item.title"
                        :key="index"
                    >
                        <template v-if="item.show_type == '1' && item.type != 'date'">
                            <el-input 
                                style="width: 300px" 
                                v-model="audit_form[item.name]"
                                @input="inputCusName"
                            >
                                <template v-if="item.units" slot="append">
                                    {{ item.units }}
                                </template>
                            </el-input>
                        </template>
                        <template v-if="item.show_type == '1' && item.type == 'date'">
                            <el-date-picker
                                v-model="audit_form[item.name]"
                                type="date"
                                style="width: 300px"
                                :valueFormat="item.date_format"
                                :format="item.date_format"
                                placeholder="选择日期"
                                @change="$forceUpdate()"
                            >
                            </el-date-picker>
                        </template>
                        <template v-if="item.show_type == '4'">
                            <el-input
                                style="width: 300px"
                                type="textarea"
                                v-model="audit_form[item.name]"
                                @input="inputCusName"
                            ></el-input>
                        </template>
                        <template v-if="item.show_type == '2'">
                            <el-select 
                                style="width: 300px" 
                                v-model="audit_form[item.name]"
                                @change="changeCusStatus"
                            >
                                <el-option
                                    v-for="op in item.options"
                                    :key="op.values"
                                    :label="op.name"
                                    :value="op.values"
                                >
                                </el-option>
                            </el-select>
                        </template>
                        <template v-if="item.show_type == '3'">
                            <el-select
                                style="width: 300px"
                                :multiple="true"
                                clearable
                                v-model="audit_form[item.name]"
                                @change="changeCusStatus"
                            >
                                <el-option
                                    v-for="op in item.options"
                                    :key="op.values"
                                    :label="op.name"
                                    :value="op.values"
                                >
                                </el-option>
                            </el-select>
                        </template>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input
                            style="width: 300px"
                            type="textarea"
                            v-model="audit_form.remarks"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="审批人" v-if="auditPersonList.length">
                        <div class="audit_person flex-row items-center">
                            <template v-if="auditPersonList.length">
                                <div
                                    class="audit_person_name"
                                    v-for="item in auditPersonList"
                                    :key="item.values"
                                >
                                    {{ item.user_name }}
                                </div>
                            </template>
                            <template v-else>
                                <template v-if="auditPersonSelect.length">
                                    <div
                                        class="audit_person_name"
                                        v-for="item in auditPersonSelect"
                                        :key="item.values"
                                    >
                                        {{ item.name }}
                                    </div>
                                </template>
                                <div
                                    class="audit_person_name audit_person_add"
                                    @click="showMemberList('spr')"
                                >
                                    添加审批人
                                </div>
                            </template>
                        </div>
                    </el-form-item>
                    <el-form-item label="凭证">
                        <div class="attachment">
                            <el-upload
                                multiple
                                action="/api/common/file/upload/admin?category=6"
                                :headers="upload_headers"
                                :limit="10"
                                :files="attenchmentList"
                                accept=".jpg,.jpeg,.png,.JPG,.JPEG,.bmp,.gif"
                                list-type="picture-card"
                                :on-success="
                                (response, file, fileList) => {
                                    onUploadAttechmentSuccess({
                                    response,
                                    file,
                                    fileList,
                                    });
                                }"
                                :on-remove="onRemoveAttechment"
                            >
                                <i class="el-icon-picture-outline"></i>
                            </el-upload>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer-detail">
                <el-button @click="show_audit = false">取 消</el-button>
                <el-button 
                    type="primary" 
                    @click="submitAudit" 
                    :loading="is_loading"
                >
                    确 定
                </el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="show_audit_member"
            width="660px"
            title="选择审批人员"
        >
            <div class="member" v-if="show_audit_member">
                <div class="member_box flex-row">
                <div class="left member_con flex-1">
                    <memberList
                        :list="memberList"
                        :defaultValue="selectedAuditIds"
                        @onClickItem="selecetedAuditMember"
                        ref="auditMemberList"
                        :checkStrictly="true"
                        from="service"
                    >
                    </memberList>
                </div>
                <div class="right member_con flex-1">
                    <div class="select_title">已选择审批人</div>
                    <div class="selected_list">
                    <div
                        class="selected_item flex-row align-center"
                        v-for="item in auditPersonSelect"
                        :key="item.id"
                    >
                        <div class="name flex-1">{{ item.name }}</div>
                        <!-- <div class="delete" @click="deleteSelected(item)">×</div> -->
                    </div>
                    </div>
                </div>
                </div>
                <div class="footer flex-row align-center">
                <el-button type="text" @click="show_audit_member = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="selectAuditOk">确定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import memberList from '@/components/navMain/crm/components/memberList.vue';
import config from "@/utils/config.js";
export default {
    components: {
        memberList,
    },
    props: {
        // 控制提交审批模态框显示/隐藏
        show_Examine_dialog: {
            type: Boolean,
            default: false
        },
        // 获取提交审批客户信息
        ponent_Examine_data: {
            type: Object,
            default:(() => {})
        },
        // 获取选择的客户更改状态
        ponent_Examine_stutas: {
            type: Object,
            default: (() => {})
        },
        // 获取部门
        AllDepartment: {
            type: Array,
            default: (() => {})
        },
        // 审批类型
        ponent_Examine_type: {
            type: Number,
            default: 19
        }
    },
    data() {
        return {
            show_audit: false, // 控制显示隐藏提交审批模态框
            // 提交审批模态框提交参数
            audit_form: {
                cat_id: 18,
            },
            c_detail: {}, //客户详情
            auditList: [], // 审批类型列表
            keyAuditList: [], // 审批模板列表
            auditPersonList: [], // 审批人列表
            attenchmentList: [],
            auditPersonSelect: [],
            selectedAuditIds: [],
            show_audit_member: false, // 选择审批人模态框
            memberList: [], // 部门列表
            upload_headers: {
                Authorization: config.TOKEN,
            },
            is_loading: false, // loading加载动画
        }
    },
    mounted() {
        this.initialization(); // 初始化数据
    },
    methods: {
        onClickFollowStatus(id, item) {
            console.log(item,"123")
            this.audit_form = { cat_id: id }
            if (id == 18) {
                this.audit_form.f_cj_type = 1
            }
            if (id == 19) {
                this.audit_form.f_trade_status = item.value_name
            }
            this.audit_form.f_name = this.c_detail.cname
            this.attenchmentList = []
            this.auditPersonSelect = []
            this.selectedAuditIds = []
            this.getAuditTypeList(id)
            this.show_audit = true
            return
        },
        // 获取部门列表
        getDepartment() {
            this.memberList = this.reFormData(this.AllDepartment);
        },
        // 处理部门列表数据
        reFormData(data, line = "") {
            data.map((item) => {
                item.pArr = `${line ? line + "," : ""}${item.pid}`;
                if (item.subs && item.subs instanceof Array && item.subs.length) {
                    let nameLine = `${line ? line + "," : ""}${item.pid}`;
                    this.reFormData(item.subs, nameLine);
                }
            });
            return data;
        },
        // 审批类型发生改变触发
        changeAudit(e) {
            console.log(e,"改变")
            // 18:成交审批 19:变更状态
            if (e == 18) {
                this.audit_form.f_cj_type = 1
                this.audit_form.f_name = this.c_detail.cname; // 赋值客户名称
                // 判断当前客户成交状态，如果是他司成交就赋值该成交状态
                if(this.c_detail.tracking.title == "他司成交") {
                    this.audit_form.f_cj_type = 2;
                }
            }
            let curr = this.auditList.find(item => item.values == e)
            if (curr && curr.is_model) {
                this.getModelList(e) // 获取审批类型模板
            } else {
                this.keyAuditList = []
            }
        },
        //根据审批类型获取模板列表
        getModelList(id) {
            this.$ajax.house.getModalList(id, this.c_detail.id).then(res => {
                res.data.map(item => {
                    if (item.type == 'date') {
                        this.audit_form[item.name] = new Date()
                    }
                })
                this.keyAuditList = res.data;
                // console.log(this.keyAuditList,"keyAuditList");
            })
        },
        // 获取审批类型
        getAuditTypeList(id) {
            this.$ajax.house.getAuditTypeList(1).then(res => {
                if (res.status == 200) {
                this.auditList = res.data;
                if (this.auditList.length) {
                    this.getAuditPerson(this.auditList[0].values)
                }
                if (this.auditList.length && this.auditList[0].is_model == 1) {
                    if (!id) {
                        id = this.auditList[0].values
                    }
                    this.getModelList(id)
                }
                }
            })
        },
        // 获取审批人列表
        getAuditPerson(id) {
            this.$ajax.house.getAuditPerson(id).then(res => {
                // console.log(res);
                this.auditPersonList = res.data
            })
        },
        // 添加审批人
        showMemberList() {
            if (!this.memberList.length) {
                this.getDepartment()
            }
            this.show_audit_member = true;
        },
        selecetedAuditMember(e) {
            this.selectedAuditIds = e.checkedKeys
            this.auditPersonSelect = e.checkedNodes
        },
        // 确定选择审批人
        selectAuditOk() {
            if(this.selectedAuditIds && this.selectedAuditIds.length) {
                this.audit_form.approver_uid = this.selectedAuditIds.join(",")
            } else {
                this.audit_form.approver_uid = this.selectedAuditIds;
            }
            this.show_audit_member = false
        },
        // 上传凭证成功后的回调函数
        onUploadAttechmentSuccess(options = {}) {
            let { response } = options;
            // this.attenchmentList = []
            this.attenchmentList.push(response.url)
        },
        // 删除凭证后的回调函数
        onRemoveAttechment(file, fileList) {
            let numFile = [];
            if(fileList && fileList.length) {
                fileList.map((item) => {
                    numFile.push(item.response.url);
                })
            } else {
                this.attenchmentList = [];
            }
            this.attenchmentList = numFile;
        },
        // 确定提交审批
        submitAudit() {
            this.is_loading = true; // 开启loading动画
            let params = Object.assign({}, this.audit_form)
            params.sys_hid = this.c_detail.id;
            if (this.auditPersonList.length) {
                params.approver_uid = []
                this.auditPersonList.map(item => {
                    params.approver_uid.push(item.id)
                })
            } else {
                params.approver_uid = this.selectedAuditIds
            }
            // 将数组转换为字符串
            if(this.attenchmentList && this.attenchmentList.length) {
                params.attachment = this.attenchmentList.join(",");
            } else {
                params.attachment = this.attenchmentList;
            }
            for (let key in params) {
                if (Object.prototype.toString.call(params[key]) === "[object Array]") {
                    if (params[key].length > 0 && typeof params[key][0] === "object") {
                        params[key] = JSON.stringify(params[key]);
                    } else {
                        params[key] = params[key].join(",");
                    }
                }
            }
            this.$ajax.house.addHouseAudit(params).then(res => {
                if (res.status == 200) {
                    this.$message.success(res.mesage || '提交成功');
                    this.show_audit = false; // 关闭模态框
                    // this.getDetail() $emit获取最新数据
                    this.$emit("submitExamineAfter", "");
                }
                this.is_loading = false; // 关闭loading动画
            })
        },
        // 关闭客户审批模态框时触发
        closeExamine() {
            this.$emit("closeExamine", "")
        },
        // 初始化数据
        initialization() {
            // console.log(this.ponent_Examine_data,"客户信息",this.ponent_Examine_stutas,"选择的状态");
            this.show_audit = this.show_Examine_dialog; // 赋值模态框显示/隐藏
            this.c_detail = this.ponent_Examine_data; // 赋值客户信息
            this.onClickFollowStatus(this.ponent_Examine_type, this.ponent_Examine_stutas);
        },
        // 客户状态修改时触发
        changeCusStatus() {
            this.$forceUpdate();
        },
        // show_type==1,修改参数时触发
        inputCusName() {
            this.$forceUpdate();
        }
    }
}
</script>
<style lang="scss" scoped>
.member {
  .left,
  .right {
    padding: 10px;
    border-top: 1px solid #e9e9e9;
  }
  .left {
    border-right: 1px solid #e9e9e9;
  }

  .select_title {
    font-size: 16px;
    font-weight: 600;
    color: #666;
  }
  .selected_list {
    padding: 10px 0;
    .selected_item {
      padding: 5px 10px;
      color: #2e3c4e;
      font-size: 14px;
      .delete {
        font-size: 22px;
        cursor: pointer;
      }
      .name {
        color: #2e3c4e;
        font-size: 14px;
      }
    }
  }
}
.audit_person {
  .audit_person_name {
    padding: 0 10px;
    margin-right: 5px;
    border-radius: 4px;
    border: 1px solid #999;
    &.audit_person_add {
      cursor: pointer;
      margin-right: 10px;
    }
  }
}
</style>