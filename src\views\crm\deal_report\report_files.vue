<template>
    <div>
        <div class="header" v-if="!preview">
            <el-button type="primary" @click="add()">添加</el-button>
        </div>
        <el-table v-loading="loading" :data="list" border :header-cell-style="{ background: '#EBF0F7' }" highlight-current-row :row-style="$TableRowStyle">
            <el-table-column label="操作时间" prop="created_at"></el-table-column>
            <el-table-column label="凭证文件" v-slot="{ row }">
                <el-image class="report-image" :src="row.file" :preview-src-list="fileImageList" v-if="row.isImage"></el-image>
                <div v-else><a :href="row.file" :download="row.file">{{row.filename}}</a></div>
            </el-table-column>
            <el-table-column label="凭证描述" v-slot="{ row }">
                {{ row.file_desc }}
            </el-table-column>
            <el-table-column label="操作" fixed="right" v-slot="{ row }" v-if="!preview">
                <el-link style="margin-right: 20px;" type="primary" @click="edit(row)">编辑</el-link>
                <el-link type="danger" @click="del(row)">删除</el-link>
            </el-table-column>
        </el-table>

        <addReportFiles v-if="dialogs.add" ref="add"/>
    </div>
</template>

<script>
import addReportFiles from './add_report_files.vue'
export default {
    components: {
        addReportFiles
    },
    props: {
        reportData: {type: Object, default: ()=>{return {}}},
        preview: {type: Boolean, default: false}
    },
    data(){
        return {
            loading: false,
            list: [],
            dialogs: {
                add: false
            }
        }
    },
    computed: {
        //凭证图集
        fileImageList(){
            return this.list.filter(e=>e.isImage).map(e=>e.file);
        }
    },
    created(){
        this.getList();
    },
    methods: {
        async getList(){
            this.loading = true;
            const res = await this.$http.getReportFilesListAPI(this.reportData.report_id);
            this.loading = false;
            if(res.status == 200){
                this.list = res.data.map(e=>{
                    const filename = e.file.substring(e.file.lastIndexOf('/')+1);
                    const pos = filename.lastIndexOf('.');
                    const isImage = pos!== -1 && ['png','jpg','jpeg','gif','webp','bmp'].includes(filename.substring(pos+1));
                    return {...e, filename, isImage};
                });
            }
        },
        async add(data = {}){
            this.dialogs.add = true; 
            await this.$nextTick();

            data.report_id = this.reportData.report_id;
            this.$refs.add.open(data).onSuccess(()=>{
                this.getList();
                this.$emit('commissionChange');
            });
        },
        edit(data){
            this.add(data);
        },
        async del(data){
            const res = await this.$http.delReportFilesAPI(data.id);
            if(res.status == 200){
                this.$message.success( res.data?.msg || "删除成功");
                this.getList();
                this.$emit('commissionChange');
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.header{
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.report-image{
    width: 60px; height: 60px;border-radius: 8px;
}
</style>