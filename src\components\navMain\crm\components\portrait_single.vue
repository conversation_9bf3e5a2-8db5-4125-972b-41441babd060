<template>
    <div class="tree_page">
      <el-input
        v-if="searchshow==true"
        placeholder="输入关键字进行过滤"
        v-model="filterText">
      </el-input>
      <el-tree
        :data="list"
        :show-checkbox="showCheckbox"
        :default-expand-all="defaultExpandAll"
        node-key="id"
        ref="tree"
        :check-strictly="checkStrictly"
        highlight-current
        :props="defaultProps"
        :expand-on-click-node="false"
        @node-click="clickItem"
        @check="checkChange"
        @current-change="currentChange"
        @check-change="currentCheckChange"
        :filter-node-method="filterNode"
        :default-checked-keys="defaultValue"
      >
      </el-tree>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      list: {
        type: Array,
        default: () => [],
      },
      from: {
        type: [String],
        default: "service",
      },
      // 目前source == "douyin "或者source =="renshi"时获取人员列表的时候传参is_wx=0
      source: {
        type: [String],
        default: "",
      },
  
      checkStrictly: {
        type: [Boolean],
        default: true,
      },
      showCheckbox: {
        type: [Boolean],
        default: true,
      },
      defaultExpandAll: {
        type: [Boolean],
        default: true,
      },
      keyData: {
        type: Array,
        default: () => [],
      },
      defaultValue: {
        type: Array,
        default: () => [],
      },
      value: {
        type: [String],
        default: "id",
      },
      isOutbound: {
        type: [Boolean],
        default: false,
      },
      searchshow: {
        type: [Boolean],
        default: false,
      },
      defaultProps: {
        type: Object,
        default: () => {
          return {
            children: "subs",
            label: "name",
            value: "id",
            disabled: (data) => {
              return !data.user_name;
            },
          };
        },
      },
    },
    name: "memberList",
    data() {
      return {
        defaultIds: [],
        filterText:"",
      };
    },
    created() {
      this.defaultIds = JSON.parse(JSON.stringify(this.defaultValue));
    },
    watch: {
    defaultExpandAll: {
      handler(newVal,oldVal) {
        console.log(newVal,oldVal);
        if(newVal) {
          this.defaultExpandAll = newVal
        }
        console.log(this.defaultExpandAll);
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
    components: {},
    mounted() {
      this.$nextTick(() => {
        this.changeSelected();
      });
    },
    methods: {
      filterNode(value, data) {
        // console.log(value, data,);
          if (!value) return true;
          return data.name.indexOf(value) !== -1;
        },
      checkChange(e, node) {
        console.log(e, node,"e, node");
        const newCheckedKeys = node.checkedKeys.map(key => {
        if (typeof key === 'string' && key.includes('_')) {
          const [numStr] = key.split('_'); // 使用解构赋值获取第一个元素
          return parseInt(numStr); // 将字符串转换为数值
        } else {
          return key;
        }
      });
        console.log(newCheckedKeys);
        node.checkedKeys = newCheckedKeys
          this.$emit("onClickItem", node);
        // }, 100);
      },
      changeSelected(ids = this.defaultValue, type = true) {
        this.$refs.tree.setCheckedKeys(ids, type);
        let nodes = this.$refs.tree.getCheckedNodes();
        let keys = this.$refs.tree.getCheckedKeys();
        let node = {
          checkedKeys: keys,
          checkedNodes: nodes,
        };
        if (keys.length && nodes.length) {
          this.$emit("onClickItem", node);
        }
      },
      async clickItem(data) {
        console.log(data,"qqqq");
        // if (this.from != "service") return;
        // if (data.user_name) return;
  
        // let res = {}
        // if (this.isOutbound) {
        //   let par = {
        //     department_id: data.id
        //   }
        //   res = await this.$http
        //     .getOutboundMemberList(par).catch((err) => console.log(err));
  
        //   if (res.status == 200) {
        //     if (res.data.length) {
        //       let newIds = [];
        //       res.data.map((item) => {
        //         item.name = item.user_name;
        //         newIds.push(item.id);
        //         return item;
        //       });
        //       // newIds = Array.from(new Set(newIds));  // 对 newIds 进行去重操作
        //       this.append(data, res.data, newIds);
        //     }
        //   }
        // } else {
        //   let params = {
        //     department_id: data.id,
        //     is_wx: (this.source == 'douyin' || this.source == 'renshi') ? 0 : 1,
        //     page: 1,
        //     per_page: 1000
        //   };
        //   res = await this.$http
        //     .getCrmMemberList({ params }).catch((err) => console.log(err));
  
        //   if (res.status == 200) {
        //     if (res.data.data.length) {
        //       let newIds = [];
        //       res.data.data.map((item) => {
        //         item.name = item.user_name;
        //         newIds.push(item.id);
        //         return item;
        //       });
        //       this.append(data, res.data.data, newIds);
        //     }
        //   }
        // }
  
      },
      append(data, newData) {
  
  
        if (data.subs) {
          // if (this.list && data.id == this.list[0].id) {
          if (!data.isAppend) {
            newData.map(item => {
              let obj = {
                id: item.id,
                wx_work_user_id: item.wx_work_user_id,
                pid: 0,
                user_name: item.user_name,
                name: item.user_name
              }
              obj[this.value] = item[this.value]
              data.subs.unshift(obj)
              data.isAppend = true
            })
  
          }
  
  
  
        } else {
          this.$set(data, 'subs', newData);
          data.isAppend = true
        }
        setTimeout(() => {
          this.$emit("setchecked")
        }, 100);
        // if (data.subs) {
        //   let filter = data.subs.filter((item) => !newIds.includes(item.id));
        //   filter.map((item) => {
        //     data.subs.push(item);
        //   });
        // } else {
        //   this.$set(data, "subs", newData);
        // }
        // setTimeout(() => {
        //   this.$emit("setchecked");
        // }, 100);
      },
      currentChange() { },
      currentCheckChange(data, type) {
        if (type === false) {
          let index = this.defaultIds.findIndex((item) => item == data.id);
          this.defaultIds.splice(index, 1);
        } else {
          this.changeSelected([data.id]);
          // this.$emit("onClickItem", data)
        }
      },
  
      // clickMenu (e, value) {
      //   console.log(e, 12323);
      //   this.$emit('onClickMenu', { detail: e.detail, item: value })
      // },
    },
  };
  </script>
  <style scoped lang="scss">
  .tree_page {
    height: 50vh;
    overflow-y: auto;
  }
  .row_item {
    .img {
      width: 20px;
      height: 20px;
      object-fit: cover;
    }
  }
  .el-submenu .el-menu-item {
    padding: 0 20px 0 45px;
  }
  </style>
  