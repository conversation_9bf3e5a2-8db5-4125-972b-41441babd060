<template>
  <div class="pages">
    <el-row :gutter="20">
      <el-col :span="24">
        <div
          class="content-box-crm"
          style="margin-bottom: 24px; padding-bottom: 0"
        >
          <div class="div row">
            <div class="title flex-1">来源分析</div>
          </div>
        </div>
        <div class="content-box-crm" style="margin-bottom: 24px">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <!-- <el-tab-pane label="按日期" name="first"></el-tab-pane> -->
            <el-tab-pane label="按员工" name="second"></el-tab-pane>
            <el-tab-pane label="按部门" name="third"></el-tab-pane>
          </el-tabs>
          <div
            class="bottom-border div row align-center"
            style="margin-top: 24px"
          >
            <span class="text">负责人</span>
            <el-input
              style="width: 200px; margin-left: 20px"
              size="small"
              v-model="params.username"
            ></el-input>
          </div>
        </div>
        <!-- <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="div row align-center">
            <span class="text">总览：</span>
            <span>企微客户数：101人</span>
            <span>实际客户数：91人</span>
          </div>
        </div> -->
        <div class="content-box-crm" style="margin-bottom: 24px">
          <div class="charts_box">
            <div class="bottom-line div row align-center">
              <span class="text">总览：</span>
              <span> 企微客户数：</span>
              <span class="primary"> 154 </span>
              <span> 人 </span>
            </div>
            <div
              class="chart-box"
              id="chart-box-left"
              style="width: 100%"
            ></div>
          </div>
          <!-- <div
            class="chart-box"
            id="chart-box"
            style="width: 100%; height: 250px"
          ></div> -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// import myLabel from "../../crm/components/my_label.vue"
import * as echarts from "echarts";
export default {
  name: "crm_source_analysis",
  components: {
    // myLabel
  },
  data() {
    return {
      activeName: "first",
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      params: {},
      echartsData: {
        dateArr: [
          "2020-08-21",
          "2020-08-22",
          "2020-08-23",
          "2020-08-24",
          "2020-08-25",
          "2020-08-26",
          "2020-08-27",
          "2020-08-28",
          "2020-08-29",
        ],
        colorArr: ["#3D74FC", "#FEC94C", "#04D38A", "#FF655F"],
        legendArr: ["已成交金额", "已成交佣金", "未完成佣金", "已发放佣金"],
        series: [
          {
            name: "已成交金额",
            type: "line",
            smooth: true,
            data: [1, 2, 4, 4, 3, 6, 5, 10, 3],
            tooltip: {
              valueFormatter: (value) => value + "人",
            },
          },

          {
            name: "已成交佣金",
            type: "line",
            smooth: true,
            data: [2, 4, 4, 3, 1, 6, 5, 10, 3],
            tooltip: {
              valueFormatter: (value) => value + "人",
            },
          },

          {
            name: "未完成佣金",
            type: "line",
            smooth: true,
            data: [6, 5, 1, 2, 4, 4, 3, 10, 3],
            tooltip: {
              valueFormatter: (value) => value + "位",
            },
          },

          {
            name: "已发放佣金",
            type: "line",
            smooth: true,
            data: [10, 1, 2, 4, 4, 3, 6, 5, 3],
            tooltip: {
              valueFormatter: (value) => value + "人",
            },
          },
        ],
      },
    };
  },
  created() {},
  mounted() {
    this.initEchart(this.echartsData);
  },
  methods: {
    handleClick(e) {
      console.log(e);
    },
    onClickTime(e) {
      console.log(e);
    },
    initEchart(res) {
      console.log(res);
      var chartDom = document.getElementById("chart-box-left");
      var myChart = echarts.init(chartDom);
      var option;
      const data = [
        {
          name: "Apples",
          value: 70,
        },
        {
          name: "Strawberries",
          value: 68,
        },
        {
          name: "Bananas",
          value: 48,
        },
        {
          name: "Oranges",
          value: 40,
        },
        {
          name: "Pears",
          value: 32,
        },
        {
          name: "Pineapples",
          value: 27,
        },
        {
          name: "Grapes",
          value: 18,
        },
      ];
      option = {
        tooltip: {
          trigger: "item",
        },
        padding: [10, 10, 10, 10],
        legend: {
          top: 10,
          left: "center",
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            center: ["50%", "50%"],
            data: data,
            top: 20,
            label: {
              position: "outer",
              alignTo: "none",
              bleedMargin: 5,
            },
          },
        ],
      };

      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.pages {
  height: 100%;
  background: #f1f4fa 100%;
  margin: -15px;
  padding: 24px;
}
.charts_box {
  width: 100%;
  .chart-box {
    height: 300px;
  }
  .left {
    margin-right: 20px;
    background: #fff;
    // width: calc();
  }
  .right {
    background: #fff;
  }
}
.content-box-crm {
  &.bg_hui {
    background: #f7f7f7;
  }
  .charts_box {
    // height: 500px;
    .bottom-line {
      padding: 0 20px 20px;
      border-bottom: 1px solid #eee;
      span {
        color: #8a929f;
        font-family: PingFang SC;
        font-weight: regular;
        font-weight: normal;
        font-size: 14px;
        &.text {
          color: #333;
          font-weight: 600;
          font-size: 18px;
        }
        &.primary {
          color: #2d84fb;
        }
      }
    }
  }
}
</style>
