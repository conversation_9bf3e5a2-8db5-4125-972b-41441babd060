<template>
  <div class="content_page">
    <div class="content_container" ref="content">
      <div class="step_box">
        <div class="step_item" :class="{ success: step >= 1 }">
          <div class="line"></div>
          <div class="number">1</div>
          <div class="title">基础信息</div>
        </div>
        <div class="step_item" :class="{ success: step >= 2 }">
          <div class="line"></div>
          <div class="number">2</div>
          <div class="title">完善信息</div>
        </div>
        <div class="step_item" :class="{ success: step >= 3 }">
          <div class="line"></div>
          <div class="number">3</div>
          <div class="title">业主信息</div>
        </div>
        <div class="step_item" :class="{ success: step >= 4 }">
          <div class="line"></div>
          <div class="number">4</div>
          <div class="title">上架成功</div>
        </div>
      </div>
      <Form1 v-show="step === 1" :form_options="form_options" :house_type="params.trade_type" :entrustData="entrustData"
        @change="onForm1Change" :isOpenShowingSingle="isOpenShowingSingle" ref="form1" />
      <Form3 v-show="step === 2" :entrustData="entrustData" :form_options="form_options"
        :usage_type="params.usage_type_id" :form_options_rent="form_options_rent" :form_options_sale="form_options_sale"
        :house_type="params.trade_type" ref="form3" />
      <Form2 v-show="step === 3" :entrustData="entrustData" :form_options="form_options" :form_data="params"
        :privacy_status="privacy_status" :privaceNum="privaceNum" :isOpenShowingSingle="isOpenShowingSingle"
        :house_type="params.trade_type" ref="form2" />
      <div v-show="step === 4">
        <div class="success_info">
          <img src="@/assets/success_res.png" alt="" />
          <div class="success_info_text flex-row">
            <span>房源添加成功，请及时跟进维护。 </span>
            <!-- <span style="cursor: pointer" @click="goDetail"> 立即查看</span> -->
            <el-button class="Look_to_Immediately" type="primary" @click="goDetail">立即查看</el-button>
            <el-button class="Continue_to_add" type="success" @click="continueAdd">继续录入</el-button>
          </div>
          <div class="success_info_text" v-if="isEntrust == 1">
            <div>
              <span>您有 {{ entrustData.pic_list.length }} 张房源相册没有同步，是否同步？ </span>
              <el-button class="Look_to_Immediately" type="primary" @click="setPic">立即同步</el-button>
            </div>
            <div class="img-box" v-for="(item, index) in entrustData.pic_list" :key="index">
              <i style="position: absolute;z-index: 9;color: red;" class="el-icon-close"></i>
              <el-image style=" width: 100px;height: auto;" :src="item.url"></el-image>
            </div>
          </div>
        </div>
        <!-- <div class="success_tip">
          <p><i class="el-icon-info"></i> 您可以复制房源地址分享给好友或客户</p>
        </div>
        <div class="flex-box copy-link">
          <el-input :value="mobile_link" disabled> </el-input>
          <el-button
            icon="el-icon-document-copy"
            type="primary"
            @click="copyLink"
            >点击复制</el-button
          >
        </div> -->
      </div>
      <!-- 底部操作菜单 -->
      <div class="options_btn">
        <el-form label-width="80px">
          <el-form-item>
            <el-button v-show="step === 2 || step === 3" size="base" style="margin-left: 24px" @click="prevStep()"><i
                class="el-icon-arrow-left"></i>上一步</el-button>
            <el-button v-show="step < 3" size="base" type="primary" @click="nextStep()">下一步 <i
                class="el-icon-arrow-right"></i>
            </el-button>
            <el-button v-show="step === 3" type="primary" size="base" :loading="subing" style="margin-left: 24px"
              @click="onSubmit">提交</el-button>
            <el-button v-show="step == 1 || step == 4" size="base" style="margin-left: 24px" @click="onCancel">{{ step ===
              4 ? "返回" : "取消" }}</el-button>
            <el-button v-show="step === 2" size="base" style="margin-left: 24px" @click="onSkip">跳过</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
// import { Drawer } from 'element-ui'
// import breadcrumb from '@/components/Breadcrumb'
// import Form1 from './components/Form1'
import Form1 from './components/formcopy'
import Form2 from './components/Form2'
import Form3 from './components/Form_perfect'
// import { mapState } from 'vuex'
// import copyText from '@/utils/copy_text'
// import HouseDetail from '@/components/house/Detail'
export default {
  name: 'LmAdd',
  components: { Form1, Form2, Form3 },
  data() {
    return {
      isEntrust: 0,
      breadcrumb: [],
      step: 1,
      subing: false,
      params: {
        tel: [],
      },
      form_options: {},
      mobile_link: '',
      show_detail: false,
      current_info_id: null,
      isOpenShowingSingle: false,
      privaceNum: 0,
      privacy_status: 0,
      form_options_rent: [], // 出租 房源标签
      form_options_sale: [], // 出售 租售 房源标签
      entrustData: {}//委托数据
    }
  },
  computed: {

  },
  created() {
    if (this.$route.query.entrustData) {
      this.isEntrust = 1
      this.entrustData = JSON.parse(this.$route.query.entrustData)
    }
    this.params.trade_type = parseInt(this.$route.query.trade_type) || 1
    this.getOptions()
    this.getOPenShowSingle()
    this.getPrivaceNum()
  },
  // beforeDestroy() {
  //   window.eventBus.$off("closeTab")
  // },
  methods: {
    async setPic() {
      let pic = JSON.stringify(this.entrustData.pic_list)
      let res = await this.$ajax.house.editPic(this.house_id, { pic })
      if (res.status == 200) {
        this.$message({
          message: `${this.entrustData.pic_list.length} 张图片同步成功！`,
          type: 'success'
        });
        this.$goPath("house_detail?id=" + this.house_id)
      }
    },
    setNullPic() {

    },
    getOptions() {
      this.$ajax.house.options(this.trade_type).then((res) => {
        if (res.status === 200) {
          this.form_options = res.data;
          console.log(this.form_options, "this.form_options")
          this.form_options_rent = JSON.parse(JSON.stringify(res.data.label_rent)); // 出租 房源标签
          this.form_options_sale = JSON.parse(JSON.stringify(res.data.label_sale)); // 出售 租售 房源标签
          // this.$refs.form1.tradeType();
        }
      })
    },
    getOPenShowSingle() {
      this.$ajax.house.getOPenShowSingle().then((res) => {
        if (res.status === 200) {
          this.isOpenShowingSingle = res.data.status
        }
      })
    },
    getPrivaceNum() {
      this.$ajax.house.getPrivaceNum().then(res => {
        // console.log(res, 12133);
        if (res.status == 200) {
          this.privaceNum = res.data.num
          this.privacy_status = res.data.status
        }

      })
    },

    nextStep() {
      this.$refs.content.scrollTop = 0
      this.step++
    },
    prevStep() {
      if (this.step > 1) {
        this.step--
        this.$refs.content.scrollTop = 0
      }
    },
    onForm1Change(e) {
      this.params.trade_type = e.trade_type
      this.params.usage_type_id = e.usage_type_id
    },
    onSubmit() {
      let params = Object.assign(
        this.params,
        this.$refs.form1.form,
        this.$refs.form2.form,
        this.$refs.form3.form
      )
      // 将配套设施多选参数转换为字符串用逗号隔开
      this.$set(params, "facilities", params.facilities.toString())
      // 将看房时间多选参数转换为字符串用逗号隔开
      this.$set(params, "see_time", params.see_time.toString())
      // 如果没有设置封面  默认第一张图为封面
      let hasSetCover = this.$refs.form1.has_set_cover
      if (!hasSetCover && params.pic.length > 0) {
        params.pic[0].is_cover = 1
      }
      if (params.sale_price) {
        params.sale_price = params.sale_price * 10000
      }
      if (params.tel.length) {
        let ownerError = ''
        for (let index = 0; index < params.tel.length; index++) {
          const element = params.tel[index];
          if (element.owner == '' || element.owner == undefined) {
            ownerError = true;
            this.$message.warning("业主姓名不能为空")
            break;
          }
          if (element.owner.length > 5) {
            ownerError = true
            this.$message.warning("业主姓名不能大于5个字符")
            break;
          }
          console.log(element.owner_tel)
          if (element.owner_tel == '' || element.owner_tel == undefined) {
            ownerError = true
            this.$message.warning("业主手机号不能为空")
            break;
          }
          if (element.owner_tel && (element.owner_tel.length !== 11 || element.owner_tel[0] != 1)) {
            ownerError = true
            this.$message.warning("业主手机号格式错误")
            break;
          }
          // if (params.building_unit_id == '' || params.building_unit_id == undefined) {
          //   ownerError = true
          //   this.$message.warning("请填写房号信息")
          //   break;
          // }
          if (params.mianji == '' || params.mianji == undefined) {
            ownerError = true
            this.$message.warning("请填写面积")
            break;
          }
        }
        if (ownerError) return
      }


      // params.picture = JSON.stringify(params.picture)
      for (let key in params) {
        if (Object.prototype.toString.call(params[key]) === '[object Array]') {
          if (params[key].length > 0 && typeof params[key][0] === 'object') {
            params[key] = JSON.stringify(params[key])
          } else {
            params[key] = params[key].join(',')
          }
        }
        if (params[key] === '' || params[key] === null) {
          if (key == 'building_loudong_id' || key == 'building_danyuan_id') {
            console.log(1);
          } else {
            delete params[key]
          }

        }
      }
      // 选择房号后只需要传building_unit_id，清除选择的楼栋和单元数据
      if (params.building_unit_id && params.building_unit_id.split("_").length == 1) {
        delete params.loudong
        delete params.loudongdanwei
        delete params.danyuan
        delete params.danyuandanwei
      } else {
        if (params.building_loudong_id && params.building_loudong_id.split("_").length > 1) {
          params.loudong = parseInt(params.building_loudong_id.split("_")[1])
          if (isNaN(params.loudong)) {
            this.$message("楼栋请以数字开头")
            return
          }
          delete params.building_loudong_id
        } else {
          params.loudong = this.$refs.form1.current_loudong_name
        }
        if (params.building_danyuan_id && params.building_danyuan_id.split("_").length > 1) {
          params.danyuan = parseInt(params.building_danyuan_id.split("_")[1])
          if (isNaN(params.danyuan)) {
            this.$message("单元请以数字开头")
            return
          }
          delete params.building_danyuan_id
        } else {
          params.danyuan = this.$refs.form1.current_danyuan_name
        }
        if (params.building_unit_id && params.building_unit_id.split("_").length > 1) {
          params.fanghao = params.building_unit_id.split("_")[1]
          delete params.building_unit_id
        }
      }
      // 如果梯户比例没有填写，默认1梯1户
      if (!params.households && !params.ladder) {
        params.households = 1;
        params.ladder = 1;
      }
      if (this.isEntrust == 1) {
        params.isEntrust = 1
        this.params.entrust_id = this.entrustData.id
      }
      this.subing = true;
      console.log(params, '委托房源录入----')
      this.$ajax.house
        .add(params)
        .then((res) => {
          console.log(res.data)
          this.subing = false
          if (res.status === 200) {
            this.$message.success(res.data.message || '发布成功')
            this.step = 4;
            this.house_id = res.data.id
            console.log(res.data, '房源录入成功----')
            // this.mobile_link = `${process.env.VUE_APP_DOMAIN}/m${this.user_info.pinyin ? '/' + this.user_info.pinyin : ''
            //   }/house/detail?id=${res.data.data.id || ''}`
          }
        })
        .catch(() => {
          this.subing = false
        })
    },
    // 立即查看
    goDetail() {
      this.$goPath("house_detail?id=" + this.house_id)
    },
    // 继续录入
    continueAdd() {
      this.step = 1;
      // 调用子组件中的方法,清除已经填入的信息
      this.$refs.form1.clearChildData();
      this.$refs.form3.clearChildData();
      this.$refs.form2.clearChildData();
    },
    copyLink() {
      if (!this.mobile_link) {
        this.$message.warning('复制失败')
        return
      }
      // copyText(this.mobile_link, () => {
      //   this.$message.success('复制成功')
      // })
    },
    onCancel() {
      // 点击返回可查看填写内容
      if (this.step == 4) {
        return this.step = 3;
      }
      let name = window.location.href.split("#")[1]
      this.$store.state.closeTab = true
      window.eventBus.$emit("closeTab", name)
      this.$router.go(-1)
    },
    // 跳过
    onSkip() {
      this.step = 3
    }
  },
  // 路由独享守卫
  beforeRouteLeave(to, from, next) {
    // 设置下一个路由的 meta
    if (to.path.includes('/house_list') && this.step == 3) {
      this.$store.state.allowUpdate = true
      // to.meta.keepAlive = false; // C 跳转到 A 时让 A 不缓存，即刷新
    }

    next();
  }
}
</script>

<style scoped lang="scss">
.content_page {
  height: 100%;
  overflow-x: hidden;
  background-color: #f1f4fa;
  display: flex;
  flex-direction: column;

  .breadcrumb_box {
    position: sticky;
    top: 0;
    z-index: 10;
  }
}

.content_container {
  flex: 1;
  margin: 24px;
  padding: 24px 32px;
  border: 2px;
  background-color: #fff;
  overflow-x: hidden;
}

// 步骤条
.step_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 950px;
  margin: auto;
  padding: 40px 0;

  >.step_item {
    flex-shrink: 1;
    flex-basis: 50%;
    display: flex;
    align-items: center;

    .number {
      width: 40px;
      text-align: center;
      height: 40px;
      line-height: 40px;
      margin-right: 12px;
      margin-left: 20px;
      border-radius: 50%;
      font-size: 18px;
      background-color: #dde1e9;
      color: #fff;
    }

    .title {
      margin-right: 20px;
      font-size: 14px;
      color: #dde1e9;
    }

    .line {
      flex: 1;
      height: 2px;
      background-color: #dde1e9;
    }

    &:first-of-type {
      flex-shrink: 0;
      flex-basis: auto;

      .line {
        display: none;
      }
    }

    &.success {
      .number {
        background: #2d84fb;
        box-shadow: 0 2px 6px 0 rgba(#2d84fb, 0.4);
      }

      .title {
        color: #2d84fb;
      }

      .line {
        background-color: #2d84fb;
      }
    }
  }
}

.options_btn {
  margin-top: 24px;
  padding: 12px 0;
  border-top: 1px solid #dde1e9;
}

.el-form {
  .title {
    margin-top: 42px;
    margin-bottom: 32px;
    font-size: 18px;
    font-weight: bold;

    .remarks {
      font-size: 14px;
      font-weight: initial;
    }
  }
}

.success_info {
  width: 500px;
  margin: auto;
  margin-top: 12px;
  margin-bottom: 32px;
  text-align: center;

  img {
    margin-bottom: 12px;
    width: 120px;
  }

  .success_info_text {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #2d84fb;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;

    .img-box {
      display: flex;
      width: 100%;
      overflow-x: auto;
      margin-top: 20px;
      padding: 10px;
      background-color: #f3f3f3;
    }

    .Look_to_Immediately,
    .Continue_to_add {
      padding: 4px 6px;
    }

    .Look_to_Immediately {
      background-color: #2d84fb;
      border-color: #2d84fb;
    }

    .Look_to_Immediately:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }

    .Look_to_Immediately:active {
      background-color: #3a8ee6;
      border-color: #3a8ee6;
    }
  }
}

.copy-link {
  margin: auto;
  width: 600px;
}

.success_tip {
  width: 600px;
  margin: auto;
  margin-bottom: 24px;
  padding: 12px 20px;
  border-radius: 4px;
  font-size: 13px;
  color: #2d84fb;
  background-color: rgba(#2d84fb, 0.2);

  i {
    margin-right: 3px;
    font-size: 18px;
  }
}
</style>
