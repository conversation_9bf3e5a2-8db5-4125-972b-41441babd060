<template>
    <div class="pages">
        <div class="report-container">
            <el-steps :active="stepNumber" type="navigation" align-center class="steps">
                <el-step title="录入成交信息"></el-step>
                <el-step title="佣金分配"></el-step>
                <el-step title="成交报告"></el-step>
            </el-steps>

            <addReportBaseInfo ref="addReportBaseInfo" :report-data="reportData" :params.sync="baseInfoParams" v-bind.sync="calcCommissions"/>
         
            <template v-if="stepNumber==2">
                <detailCommissionTabs :report-data="reportData" @commissionChange="onCommissionChange"/>
            </template>
            
            <div class="step-bottom">
                <el-button type="primary" @click="addBaseInfo" :loading="submiting" v-if="stepNumber==1">下一步</el-button>
                <template v-if="stepNumber==2">
                    <el-button type="primary" @click="editBaseInfo()" :loading="submiting">保存</el-button>
                    <el-button type="primary" @click="editBaseInfoAndBack" :loading="submiting">保存并返回</el-button>
                </template>
            </div>
        </div>
    </div>
</template>
  
<script>
import addReportBaseInfo from "./add_report_base_info.vue";
import detailCommissionTabs from "./detail_commission_tabs.vue";
export default {
    components: {
        addReportBaseInfo, detailCommissionTabs
    },
    provide(){
        return {
            checkSaveBaseInfo: this.checkSaveBaseInfo,
            commissions: this.calcCommissions
        }
    },
    data() {
        return {
            loading: false,
            submiting: false,
            stepNumber: 1,                  //步骤值
            reportId: 0,                    //报告id
            reportData: {},                 //报告数据
            calcCommissions: {              
                normalCommission: 0,        //正常应收佣金        
                channelCommission: 0,       //渠道分佣
                withholdTaxAmount: 0,       //代扣税
                calcCommission: 0           //应收佣金
            },                              
            baseInfoParams: {},             //基本信息
            oldBaseInfoParams: {},          //未改变的基本信息
        };
    },
    computed: {
        //是否新增
        isAdd(){
            return !this.reportId;
        }
    },
    watch: {
        baseInfoParams:{
            handler(val, oldVal){
                if(Object.keys(oldVal).length === 0){
                    this.oldBaseInfoParams = {...val};
                }
            },
            deep: true
        }
    },
    created() {
        this.reportId = this.$route.query.id || 0;
        if (!this.isAdd) {
            this.initReportDetail();
        }
    },
    methods: {
        //检查保存基本信息--在基本信息修改后进行保存
        async checkSaveBaseInfo(){
            if(JSON.stringify(this.oldBaseInfoParams) !== JSON.stringify(this.baseInfoParams)){
                console.log(this.oldBaseInfoParams);   
                return await this.editBaseInfo(false); 
            }
            return true;
        },

        initReportDetail(){
            this.stepNumber = 2;
            this.getReportDetail()
        },
        //获取报详情
        async getReportDetail(){
            const res = await this.$http.getReportDetailAPI(this.reportId);
            if(res.status == 200){
                this.reportData = res.data
            }
        },
        //新增报告基本信息
        async addBaseInfo(){
            this.submiting = true;
            await this.$refs.addReportBaseInfo.submit((data)=>{
                this.reportId = data.id;
                this.initReportDetail();
                this.$emitPageRefresh('crm_deal_report_index');
            });
            this.submiting = false;
        },
        //编辑报告基本信息
        async editBaseInfo(autoSuccessMsg = true){
            this.submiting = true;
            const res = await this.$refs.addReportBaseInfo.submit(()=>{
                this.baseInfoParams = {};
                this.getReportDetail();
                this.$emitPageRefresh('crm_deal_report_index');
            }, {autoSuccessMsg});
            this.submiting = false;
            return res;
        },
        //保存并返回
        async editBaseInfoAndBack(){
            this.submiting = true;
            await this.$refs.addReportBaseInfo.submit(()=>{
                this.baseInfoParams = {};
                //返回成交报告详情
                if(this.$listeners.editReportDone){
                    this.$emit("editReportDone");
                }else{
                    this.goReportIndexPage();
                }
                this.$emitPageRefresh('crm_deal_report_index');
            });
            this.submiting = false;
        },
        //佣金数据变化事件
        onCommissionChange(name){
            this.getReportDetail();
            //应收佣金和扣除款项 更新后，业绩提成会重新计算
            if(name == 'receiveCommission' || name == 'companyCommission'){
                this.$emitPageRefresh('crmDealReportMemberCommission');
            }
            this.$emitPageRefresh('crm_deal_report_index');
        },
        //返回成交报告列表
        goReportIndexPage(){
            let name = window.location.href.split("#")[1];
            this.$store.state.closeTab = true;
            eventBus.$emit("closeTab", name);
            this.$goPath('/crm_deal_report_index');
        }
    },
};
</script>
  
<style lang="scss" scoped>
.pages {
    min-height: 100%;
    background: #f1f4fa 100%;
    margin: -15px;
    padding: 0 24px 24px;
}
.report-container{
    padding: 24px;
    background: #fff;
    .steps{
        margin:20px 0 20px 0;padding-bottom: 20px; border-bottom: 1px solid #f4f4f4;
    }
    .step-bottom{
        display: flex;
        height: 60px;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>