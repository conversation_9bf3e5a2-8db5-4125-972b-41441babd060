<template>
  <el-container>
    <el-header class="div row">
      <div class="div row">
        <el-button type="primary" @click="addData" icon="el-icon-plus"
          >添加</el-button
        >
      </div>
    </el-header>
    <el-main>
      <myTable :table-list="tableData" :header="table_header"></myTable>
      <el-footer>
        <!-- 分页 -->
        <myPagination
          :total="params.total"
          :pagesize="params.pagesize"
          :currentPage="params.currentPage"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></myPagination>
      </el-footer>
    </el-main>
    <el-dialog
      :title="titleMap[dialogTitle]"
      :visible.sync="dialogCreate"
      width="50%"
    >
      <el-form :model="form_create" label-width="100px">
        <el-form-item label="选择职位">
          <el-radio
            v-model="form_create.role_category"
            v-for="item in role_category_list"
            :key="item.id"
            :label="item.value"
            >{{ item.description }}</el-radio
          >
        </el-form-item>
        <el-form-item label="选择用户">
          <el-select
            placeholder="请输入手机号"
            remote
            filterable
            v-model="form_create.user_id"
            :loading="broker_loading"
            :remote-method="getbrokerData"
            value-key="user_id"
          >
            <el-option
              v-for="item in store_manager_list"
              :key="item.user_id"
              :label="item.name"
              :value="item.user_id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-container>
</template>

<script>
import myPagination from "@/components/components/my_pagination";
import myTable from "@/components/components/my_table";
export default {
  name: "company_manager",
  components: {
    myPagination,
    myTable,
  },
  data() {
    return {
      tableData: [],
      params: {
        id: "",
        currentPage: 1,
        pagesize: 10,
        total: 0,
        row: 0,
      },
      //
      dialogCreate: false,
      titleMap: {
        addData: "添加",
        updataData: "修改",
      },
      dialogTitle: "",
      //   创建公司经理
      form_create: {
        company_id: "",
        role_category: "",
        user_id: "",
      },
      broker_loading: false,
      store_manager_list: [],
      role_category_list: this.$getDictionary("COMPANY_MANAGER_ROLE_CATEGORY"),
      table_header: [
        { prop: "id", label: "ID", width: "100" },
        { prop: "company_id", label: "公司ID", width: "100" },
        {
          label: "头像",
          render: (h, data) => {
            return (
              <el-popover width="500px" trigger="hover" placement="right">
                <el-image
                  style="width:300px;height:300px"
                  fit="contain"
                  src={data.row.u_avatar}
                ></el-image>
                <img
                  slot="reference"
                  src={data.row.u_avatar}
                  style="max-height:50px;max-width:100px"
                ></img>
              </el-popover>
            );
          },
        },
        {
          label: "员工/联系方式",
          render: (h, data) => {
            return (
              <el-tag>
                {data.row.u_name || data.row.u_nickname || data.row.u_user_name}
                ：{data.row.u_phone}
              </el-tag>
            );
          },
        },
        {
          label: "职位",
          render: (h, data) => {
            return (
              <el-tag type="success">
                {data.row.role_category === 1 ? "总经理" : "区域经理"}
              </el-tag>
            );
          },
        },
        {
          label: "操作",
          render: (h, data) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-edit"
                  onClick={() => {
                    this.changeData(data.row);
                  }}
                >
                  修改
                </el-button>
                {data.row.role_category === 2 ? (
                  <el-button
                    size="mini"
                    type="success"
                    icon="el-icon-plus"
                    onClick={() => {
                      this.bindStore(data.row);
                    }}
                  >
                    绑定门店
                  </el-button>
                ) : (
                  ""
                )}
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  type="danger"
                  onClick={() => {
                    this.handleDelete(data.row);
                  }}
                >
                  删除
                </el-button>
              </div>
            );
          },
        },
      ],
    };
  },
  mounted() {
    this.params.id = this.$route.query.company_id;
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.$http.companyManagerList(this.params).then((res) => {
        if (res.status === 200) {
          this.tableData = res.data.data;
          this.params.currentPage = res.data.current_page;
          this.params.total = res.data.total;
          this.params.row = res.data.per_page;
          this.getbrokerData(1);
        }
      });
    }, // 根据分页设置的数据控制每页显示的数据条数及页码跳转页面刷新
    getPageData() {
      let start = (this.params.currentPage - 1) * this.params.pagesize;
      let end = start + this.params.pagesize;
      this.schArr = this.tableData.slice(start, end);
    },
    // 分页自带的函数，当pageSize变化时会触发此函数
    handleSizeChange(val) {
      this.params.pagesize = val;
      this.getPageData();
      this.getDataList();
    },
    // 分页自带函数，当currentPage变化时会触发此函数
    handleCurrentChange(val) {
      this.params.currentPage = val;
      this.getPageData();
      this.getDataList();
    },
    getbrokerData(query) {
      // 项目助理不能为店长
      this.broker_loading = true;
      this.$http.searchUserList("100", query, 1).then((res) => {
        this.broker_loading = false;
        this.store_manager_list = res.data.data.map((item) => {
          return {
            user_id: item.id,
            name: item.name || item.nickname || item.user_name || item.phone,
          };
        });
      });
    },
    // 添加经理
    addData() {
      this.form_create = {};
      this.form_create.company_id = this.params.id;
      this.dialogCreate = true;
      this.dialogTitle = "addData";
    },
    changeData(row) {
      this.dialogTitle = "updataData";
      this.dialogCreate = true;
      this.form_create = {
        id: row.id,
        company_id: row.company_id,
        role_category: row.role_category + "",
        user_id: row.user_id,
      };
    },
    onSubmit() {
      if (this.dialogTitle === "addData") {
        this.$http.createCompanyManager(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "创建成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      } else if (this.dialogTitle === "updataData") {
        this.$http.updataCompanyManager(this.form_create).then((res) => {
          if (res.status === 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.getDataList();
            this.dialogCreate = false;
          }
        });
      }
    },
    handleDelete(row) {
      this.$confirm("此操作将删除该员工, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$http.deleteCompanyManager(row.id).then((res) => {
            if (res.status === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    bindStore(row) {
      this.$goPath(
        `/binding_store?company_id=${this.params.id}&manager_id=${row.id}`
      );
    },
  },
};
</script>

<style lang="scss">
.el-header {
  align-items: center;
}
.el-table {
  .el-button {
    border-radius: 4px;
    margin: 5px;
  }
}
</style>
