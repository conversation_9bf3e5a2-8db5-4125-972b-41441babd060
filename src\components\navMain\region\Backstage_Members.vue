<template>
    <div class="header_catalogue">
        <div class="newly_increased">
            <div class="search_for">
                <div class="search_input">
                    <div style="width: 240px">
                        <el-input prefix-icon="el-icon-search" placeholder="请在这里输入手机号" v-model="memberData.phone"
                            class="input-with-select">
                        </el-input>
                    </div>
                    <div>
                    <el-select v-model="memberData.type" slot="prepend" placeholder="请选择" style="margin-left: 10px;">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="置业顾问" value="1"></el-option>
                        <el-option label="经纪人" value="2"></el-option>
                    </el-select>
                    <!-- <el-select v-model="memberData.status" slot="prepend" placeholder="请选择" style="margin-left: 10px;">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="启用" value="1"></el-option>
                        <el-option label="禁用" value="0"></el-option>
                    </el-select> -->
                </div>
                    <div class="search_btn">
                        <el-button type="primary" @click="Member_Search">搜索</el-button>
                    </div>
                </div>
                <div>
                    <el-button type="warning" @click="addmember">新增会员</el-button>
                </div>
            </div>
            <div class="List_Mode">
                <el-table :data="tableData" border style="width: 100%" :header-cell-style="{
                    background: '#DDE1E9',
                    color: '#2E3C4E',
                }">
                    <el-table-column prop="id" label="ID" align="center">
                    </el-table-column>
                    <el-table-column prop="type" label="类型" align="center">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.type === 2 ? '' : 'success'">{{
                                scope.row.type === 2 ? "经纪人" : "置业顾问" }}</el-tag>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="name" label="姓名" align="center">
                    </el-table-column> -->
                    <el-table-column prop="phone" label="手机号" align="center">
                    </el-table-column>
                    <el-table-column prop="status" label="状态" align="center">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status === 0 ? 'danger' : 'success'">{{
                                scope.row.status === 0 ? "禁用" : "启用" }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="address" label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button plain @click="Member_Editor(scope.row.id)">修改</el-button>
                            <el-button plain @click="Delete_Member(scope.row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="paging">
                    <el-pagination background layout="pager" :total="respage.total" :page-size="respage.per_page"
                        :current-page="respage.current_page" @current-change="handleCurrentChange"> </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            select:"",
            tableData: [],
            respage: [{
                current_page: "",
                total: "",
                per_page: ""
            }],
            memberData: {
                page: "1",
                per_page: "",
                type: "",
                phone: "",
                // status:""
            },
        }
    },
    methods: {
        Backstage_Members() {
            let params = Object.assign({}, this.memberData)
            // console.log(params);
            this.$http.Background_Member_List(params).then(res => {
                console.log(res);
                var { data } = res.data
                if (res.status == 200) {
                    // console.log(data);
                    this.respage.current_page = res.data.current_page
                    this.respage.total = res.data.total
                    this.respage.per_page = res.data.per_page
                    this.tableData = data
                    // console.log(this.respage);
                }
            })
        },
        //模糊搜索
        Member_Search(){
            this.Backstage_Members() 
        },
        //分页
        handleCurrentChange(val) {
            this.memberData.page = val
            this.Backstage_Members()
        },
        //修改会员
        Member_Editor(id) {
            // console.log(id);
            this.$goPath(`Member_Editor?id=${id}`)
        },
        //跳转到添加会员
        addmember() {
            this.$goPath("add_member")
        },
        Delete_Member(id) {
            this.$confirm('此操作将永久删除该会员, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // console.log(id);
                this.$http.Delete_Member(id).then(res => {
                    console.log(res);
                    if (res.status == 200) {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                    }
                    this.Backstage_Members()
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        submitForm(memberData) {
            this.$refs[memberData].validate((valid) => {
                if (valid) {
                    alert('submit!');
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        resetForm(memberData) {
            this.$refs[memberData].resetFields();
        }
    },
    mounted() {
        this.Backstage_Members()
    }
}
</script>
  
<style scoped lang="scss" >
.header_catalogue {
    background: #f1f4fa;
    margin: -14px;
    padding: 24px;

    .newly_increased {
        width: 96%;
        height: 850px;
        background-color: #fff;
        margin: 10px auto;
        overflow: hidden;

        .search_for {
            width: 96%;
            height: 35px;
            // background-color: coral;
            margin: 20px auto;
            display: flex;
            justify-content: space-between;

            .search_input {
                display: flex;
            }

            /deep/.el-input__inner {
                height: 32px;
            }

            /deep/.el-input__icon {
                line-height: 33px;
            }

            .search_btn {
                .el-button {
                    width: 76px;
                    height: 36px;
                    margin-left: 10px;
                    line-height: 0;
                }
            }
        }

        .List_Mode {
            width: 96%;
            height: 500px;
            margin: 30px auto;

            .el-button {
                border-color: #2d84fb;
                color: #2d84fb;
            }

            .paging {
                display: flex;
                justify-content: flex-end;
                margin-top: 20px;
            }
        }
    }
}
</style>