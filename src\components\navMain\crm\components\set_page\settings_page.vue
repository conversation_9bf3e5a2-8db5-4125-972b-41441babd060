<template>
    <div class="page">
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="最大查看电话次数" name="max_look_num"></el-tab-pane>
          <el-tab-pane label="每日最大领取数量" name="get_day_num"></el-tab-pane>
          <el-tab-pane label="领取客户最大数量" name="get_num"></el-tab-pane>
        </el-tabs>
      </div>
        <div class="top">
          <div>
            <el-button type="primary" @click="adduser">添加成员</el-button>
          </div>
          <div>
            <p class="reminder" v-if="activeName=='max_look_num'">
              <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;"></i>
              按成员设置优先级大于全局配置，添加成员后最大查看次数将以本页设置为准。
            </p>
            <p class="reminder" v-if="activeName=='get_day_num'">
              <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;"></i>
              按成员设置优先级大于全局配置，添加成员后每日最大领取数量将以本页设置为准。
            </p>
            <p class="reminder" v-if="activeName=='get_num'">
              <i class="el-icon-info" style="
              color: #f56c6c;
              font-size: 20px;"></i>
              按成员设置优先级大于全局配置，添加成员后领取客户最大数量将以本页设置为准。
            </p>
          </div>
        </div>
        <div>
            <el-table
              :data="tableData"
              border
              style="width: 100%"
              v-loading="is_table_loading"
              :header-cell-style="{ background: '#EBF0F7' }"
              >
              <el-table-column
                prop="user_name"  
                label="姓名">
              </el-table-column>
              <el-table-column
              v-if="activeName == 'max_look_num'"
                prop="max_look_num" 
                label="最大查看次数">
                <!-- <div class="flex-row">
                  <div>
                    <el-input
                        size="mini"
                        style="width: 200px;"
                        placeholder="请输入数量"
                        class="overdue"
                        v-model="row.max_look_num"
                        min="0"
                        step="1"
                        type="number"
                        @focus="preservetime(row)"
                        >
                        <template slot="append">
                            个
                        </template>
                    </el-input>
                  </div>
                  <div style="margin-left:6px;">
                        <el-button type="primary" size="mini" @click="setnum(row)">保存</el-button>
                  </div>
                </div> -->
              </el-table-column> 
              <el-table-column
              v-if="activeName == 'get_day_num'"
                prop="get_day_num" 
                label="每日最大领取数量">
              </el-table-column> 
              <el-table-column
              v-if="activeName == 'get_num'"
                prop="get_num" 
                label="领取客户最大数量">
              </el-table-column> 
              <el-table-column 
                label="操作"
                width="200px"
                v-slot="{ row }">
                <el-link style="margin-right:10px;" type="primary" @click="editnum(row)">修改</el-link>
                <el-link type="danger" @click="delnum(row)">删除</el-link>
              </el-table-column>
            </el-table>
            <div class="page_footer flex-row items-center">
                <div class="page_footer_l flex-row flex-1 items-center">
                  <div class="head-list"> 
                    <el-button type="primary" size="small" @click="Refresh">刷新</el-button>
                  </div>
                </div>
                <div style="margin-right:10px;">
                  <!-- <el-pagination align='center' 
                  background
                     @size-change="handleSizeChange" 
                     @current-change="handleCurrentChange"
                     :current-page="params.page" 
                     :page-sizes="[10,20,30,50]" 
                     :page-size="params.per_page" 
                     layout="total, sizes, prev, pager, next, jumper" 
                     :total="params.total">
                  </el-pagination> -->
                </div>
            </div>
        </div>   
        <el-dialog :visible.sync="is_transfer_customer" :title="Headtips">
            <div class="head-list">
                <el-select v-model="formLabelAlign.user_id" style="width: 100%;" placeholder="请选择成员"
                @change="onClickType">
                    <el-option
                    v-for="item in member_listNEW"
                    :key="item.id"
                    :label="item.user_name"
                    :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>

            <div class="quantity_config"> 
              <el-form :label-position="labelPosition" label-width="130px" :model="formLabelAlign">
                <el-form-item label="最大查看电话次数" v-if="activeName == 'max_look_num'">
                  <el-input v-model="formLabelAlign.max_look_num">
                    <template slot="append">次</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="每日最大领取数量" v-if="activeName == 'get_day_num'"> 
                  <el-input v-model="formLabelAlign.get_day_num">
                    <template slot="append">个</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="领取客户最大数量" v-if="activeName == 'get_num'">
                  <el-input v-model="formLabelAlign.get_num">
                    <template slot="append">个</template>
                  </el-input>
                </el-form-item>
              </el-form> 
            </div>

          <!-- <el-table style="margin-top: 20px" :data="admin_list" border>，
            <el-table-column label="用户名" prop="user_name"></el-table-column>
            <el-table-column label="最大查看数量" prop="max_look_num"
            v-slot="{row}">
                <div>
                    <el-input
                        size="mini"
                        style="width: 200px;"
                        placeholder="请输入数量"
                        class="overdue"
                        v-model="row.max_look_num"
                        min="0"
                        step="1"
                        type="number"
                        @focus="preservetime(row)"
                        >
                        <template slot="append">
                            个
                        </template>
                    </el-input>
                </div>
            </el-table-column>
          </el-table>
           <el-pagination style="text-align: end; margin-top: 24px" background layout="prev, pager, next"
            :total="admin_params.total" :page-size="admin_params.per_page" :current-page="admin_params.page"
            @current-change="PersPageChange">
          </el-pagination> -->
          <div slot="footer" class="dialog-footer">
            <el-button @click="is_transfer_customer = false">取 消</el-button>
            <el-button v-if="Headtips == '添加'" type="primary" @click="confirmadd">添 加</el-button>
            <el-button v-if="Headtips == '修改'" type="primary" @click="confirmedit">修 改</el-button>
          </div>
        </el-dialog>                                      
    </div>
</template>
<script>
export default {
    data() {
        return {
            tableData:[],//配置数据
            params:{
                page: 1,
                per_page: 10,
            },
            is_transfer_customer:false,
            member_listNEW:[],//成员
            value1:"",
            // admin_list:[],
            max_look_num:"",
            is_table_loading:false,
            activeName:"max_look_num",//默认选中
            labelPosition: 'left',
            formLabelAlign:{
              max_look_num:"",
              get_day_num:"",
              get_num:""
            },
            Headtips:"添加", 
            type:1,
        }
    },
    mounted(){
      //获取地址栏的关键词，如果等于claim，this.activeName = "get_day_num",如果等于total this.activeName = "get_num" 如果等于describe this.activeName = "max_look_num" 
      if(this.$route.query.keyword == "claim"){
        this.activeName = "get_day_num"
        this.type = 2
      }else if(this.$route.query.keyword == "total"){
        this.activeName = "get_num"
        this.type = 3
      }else if(this.$route.query.keyword == "describe"){
        this.activeName = "max_look_num"
        this.type = 1
      }
        this.getdata()
        this.MembersNEW()
    },
    methods:{
        // 获取成员的接口（新）
        MembersNEW(){
            this.$http.getDepartmentMemberListNew().then((res)=>{
                if(res.status==200){
                this.member_listNEW = res.data
                }
            }) 
        },
        //切换title
        handleClick() {
            if(this.activeName == "get_day_num"){
              this.type = 2
            }else if(this.activeName == "get_num"){
              this.type = 3
            }else if(this.activeName == "max_look_num"){
              this.type = 1
            }
            this.getdata()
        },
        //获取自定义配置
        getdata(){
          this.is_table_loading = true
            this.$http.getlookphonenumlist(this.type).then( res =>{
                if(res.status==200){
                    // console.log(res,"配置项数据");
                    this.is_table_loading = false
                    this.tableData = res.data 
                    
                }
            })
        },
        //添加成员
        adduser(){
            this.Headtips = "添加"
            this.formLabelAlign = {user_id:"",max_look_num:"",get_day_num:"",get_num:""}
            this.is_transfer_customer = true
        },
        //保存数量
        preservetime(row){
            this.max_look_num = row.max_look_num
         },
        //确定添加
        confirmadd(){
          if(!this.formLabelAlign.user_id){
            this.$message.error("请选择成员")
            return
          }
          let push_formcopy = {}
          push_formcopy.user_id = this.formLabelAlign.user_id
          push_formcopy.type = this.type
          //判断push_formcopy里有值的字段赋值给push_formcopy.number
          if (this.formLabelAlign.max_look_num) {
            push_formcopy.number = this.formLabelAlign.max_look_num;
          } else if (this.formLabelAlign.get_day_num) {
            push_formcopy.number = this.formLabelAlign.get_day_num;
          } else if (this.formLabelAlign.get_num) {
            push_formcopy.number = this.formLabelAlign.get_num;
          }

          //   // const mergedArray = [...new Set([...this.admin_list, ...this.tableData])];
          //  // 1. 遍历 this.tableData this.admin_list 中对应的 max_look_num，并更新 arr2 中的 max_look_num
          //  this.tableData.forEach(item2 => {
          //     const matchingItem1 = this.admin_list.find(item1 => item1.user_id === item2.user_id);
          //     if (matchingItem1) {
          //       item2.max_look_num = matchingItem1.max_look_num;  // 更新 max_look_num
          //     }
          //   });
          //   // 2. 合并 this.admin_list 和 this.tableData user_id 去重
          //   const mergedArray = [...this.admin_list, ...this.tableData].reduce((acc, current) => {
          //     const existing = acc.get(current.user_id);
          //     if (existing) {
          //       // 如果相同 user_id，保持 arr2 中的 id，不做其他修改
          //       acc.set(current.user_id, { ...existing, ...current });
          //     } else {
          //       // 如果没有相同 user_id，直接加入
          //       acc.set(current.user_id, { ...current });
          //     }
          //     return acc;
          //   }, new Map());
          //   // 转换为数组并输出
          //   const finalArray = [...mergedArray.values()];
            // console.log(push_formcopy,"======");
            this.sureadd(push_formcopy)
        },
        //确定添加
        sureadd(push_formcopy){
          this.$http.setlookphonenumlist(push_formcopy).then(res=>{
            if(res.status==200){
                this.$message.success("添加成功！")
              this.is_transfer_customer = false
              this.value1 = ""
              // this.admin_list = []
              this.getdata()
            }
          })
        },
        //选中成员时
        onClickType(){
            // let seen = new Set();
            // this.admin_list = this.member_listNEW
            //   .filter(item => this.value1.includes(item.id) && !seen.has(item.id))
            //   .map(item => {
            //     seen.add(item.id); // 记录已处理过的 id
            //     return { user_id: item.id, user_name: item.user_name,max_look_num: "" };
            // });
            
        },
        //保存修改的查看次数
        // setnum(){
        //   this.sureadd(this.tableData,1)
        // },
        //修改
        editnum(row){
          this.Headtips = "修改"
          this.formLabelAlign =  row
          this.is_transfer_customer = true
        },
        //确定修改
        confirmedit(){
          let formcopy = {}
          formcopy.user_id = this.formLabelAlign.user_id
          formcopy.type = this.type
          formcopy.id = this.formLabelAlign.id
          if (this.formLabelAlign.max_look_num) {
            formcopy.number = this.formLabelAlign.max_look_num;
          } else if (this.formLabelAlign.get_day_num) {
            formcopy.number = this.formLabelAlign.get_day_num;
          } else if (this.formLabelAlign.get_num) {
            formcopy.number = this.formLabelAlign.get_num;
          }
          // console.log(formcopy,"=====");
          this.$http.updateLookPhoneNumList(formcopy).then(res=>{
            if(res.status==200){
                this.$message.success("编辑成功！")
              this.is_transfer_customer = false
              this.value1 = ""
              // this.admin_list = []
              this.getdata()
            }
          })
        },
        //删除
        delnum(row){
        this.$confirm('此操作将删除该成员限制, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let delform = {}
          delform.user_id = row.user_id
          delform.type = this.type
          delform.id = row.id
          delform.number = 0
          // console.log(delform,"=====");
          this.$http.updateLookPhoneNumList(delform).then(res=>{
            if(res.status==200){
              this.$message.success("删除成功！")
              this.getdata()
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });          
        });
        
        },
        //刷新
        Refresh(){
          this.getdata()
        },
        handleSizeChange(val) {
          // console.log(`每页 ${val} 条`);
          this.currentPage = 1;
          this.pageSize = val;
        },
        //当前页改变时触发 跳转其他页
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.currentPage = val;
            this.backupscurrentPage = val
        },
        PersPageChange(){

        },
    },
}
</script>
<style lang="scss" scoped>
.page{
   height: 100%; 
   ::v-deep .el-dialog{
      min-height: 600px;
   }
}
.top{
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    .reminder {
     width: 493px;
     box-sizing: border-box;
     background-color: #F7F8FA;
     color: #4E5969;
     padding: 7px 12px;
    //  margin: 10px 60px 10px 0px;
     font-size: 12px;
     line-height: 20px;
 }
}
.page_footer {
  position: fixed;
  left: 230px;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 10px;
  z-index: 1000;
  .head-list{
    margin-left: 13px;
  }
}
.quantity_config{
    margin-top: 20px;
    width: 400px;
  }
</style> 